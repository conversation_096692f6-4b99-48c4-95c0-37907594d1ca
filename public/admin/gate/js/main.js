(self.webpackChunk=self.webpackChunk||[]).push([[522],{516:(e,a,t)=>{"use strict";var n=t(808),s=t.n(n),i=t(755),o=t.n(i);t(686);t(824);var l=t(901),r=t.n(l);function c(e){return e.type||(e.type="get"),e.data||(e.data=!1),void 0===e.async&&(e.async=!0),void 0===e.global&&(e.global=!0),o().ajax({global:e.global,url:e.url,cache:!1,type:e.type,async:e.async,dataType:"json",data:e.data,success:function(a){a.replace&&(o()(".panel").trigger("dismiss.panel"),o()(a.replace.selector).html(a.replace.html)),e.success&&e.success(a)},error:function(a){e.error&&e.error(a)}})}function d(e){e.removeClass("has-error").find("._form-error").remove(),e.find(".input-has-error").removeClass("input-has-error")}function u(e){e.find(":submit").trigger("loading.start").prop("disabled",!0)}function p(e){e.hasClass("js-keep-loader")||e.find(":submit").trigger("loading.end").prop("disabled",!1)}function m(e){e.trigger("loading.end")}function h(){o()(document).on("submit",".js-ajax-form",(function(e){var a,t;e.preventDefault(),a=o()(this),P()&&(u(a),d(o()(a).find("._form-group")),(t=a).find(".cc-form__error").remove(),t.find(".cc-form__row").removeClass("invalid").removeClass("invalid-empty"),o().when(c({url:a.attr("action"),type:a.attr("method"),data:a.serialize()})).done((function(){a.hasClass("js-no-redirect-on-request")&&p(a)})))})),o()(document).on("click",".js-ajax-form-submit",(function(e){e.preventDefault(),o()(this).closest(".js-ajax-form").trigger("submit")})),o()(document).on("input change",".input-has-error",(function(e){d(o()(this).closest("._form-group"))})),o()(document).on("focusin","input",(function(){o()(this).closest(".cc-form__row").addClass("focused")})),o()(document).on("focusout","input",(function(){o()(this).closest(".cc-form__row").removeClass("focused")})),o()(document).on("keyup","input",(function(){this.value.length>1?o()(this).closest(".cc-form__row").addClass("valid").removeClass("invalid"):o()(this).closest(".cc-form__row").removeClass("valid")})),o()(window).on("load",(function(){o()("input:-webkit-autofill").closest(".cc-form__row").addClass("valid")}))}var v=t(699),f=t.n(v);t(553);function g(e){!function(e){function a(e,a){return e.is("input, select, textarea")||(e=e.find(".select2-container")?e.find("select"):e.find("input, textarea")),e.prop("disabled",a).trigger("change"),e}o()("select",e).each((function(){var e=o()(this),a={placeholder:e.data("placeholder"),disabled:e.data("disabled"),width:"100%",allowClear:!e.data("disable-clear"),dropdownAutoWidth:!1,minimumResultsForSearch:e.data("no-input")?-1:0};e.data("select2")||e.select2(a)})),o()(document).on("change","[data-select-toggle]",(function(){var e=o()(this),t=o()('[data-select-toggle-target="'+e.attr("data-select-toggle")+'"]'),n=e.val();t.addClass("hidden").attr("data-selected",""),t.each((function(){var e,t,s,i=o()(this);if(i.is('[data-select-target="'+n+'"]')&&(e=i).removeClass("hidden").attr("data-selected","selected"),(s=a(i,!e)).attr("data-select-toggle")&&(t=o()('[data-select-toggle-target="'+s.attr("data-select-toggle")+'"]')),t){var l=e&&t.attr("data-selected");t.addClass("hidden"),l&&t.removeClass("hidden"),a(t,!l)}}))}))}(e),function(e){o()('[data-toggle="tooltip"]',e).tooltip({trigger:"hover"}),o()('[data-toggle="tooltip"]',e).on("shown.bs.tooltip",(function(){var a=w(e);o()(".tooltip").css({"z-index":a})}))}(e),function(e){o()("#CardAddFormModal",e).length&&o()("#CardAddFormModal",e).closest(".modal").length&&o().getScript("https://js.braintreegateway.com/web/dropin/1.20.0/js/dropin.min.js",(function(){var a=o()("#CardAddFormModal",e),t=o()("#nonce",e),n=o()(".js-braintree-add-credit-card-submit",e);a.on("submit",(function(e){e.preventDefault()})),n.on("click",(function(e){e.preventDefault(),a.submit()}));var s={vaultManager:!0,container:o()("#dropin-container",e)[0],authorization:t.data("authorization"),translations:t.data("translations"),threeDSecure:{amount:"1.00"},card:{vault:{vaultCard:!1}}};braintree.dropin.create(s,(function(e,n){a.on("submit",(function(e){return e.preventDefault(),u(a),n.requestPaymentMethod((function(e,s){if(e)return o()(".braintree-sheet__container").hasClass("braintree-sheet--active")||(r().error("Error: "+e.message),console.log(e)),p(a),!1;t.val(s.nonce),c({url:a.attr("action"),type:a.attr("method"),data:a.serialize(),success:function(e){a.trigger("cc.ajax.success",[e]),e.redirect?document.location.href=e.redirect:N(a)},error:function(){p(a),n.clearSelectedPaymentMethod()}})})),!1}))}))}))}(e),function(e){o()("[data-toggle-row]",e).on("click",(function(e){o()(e.target).closest("a").length||(o()(this).next(".js-tr-placeholder").toggle(),e.preventDefault())}))}(e),function(e){function a(e,a){359!==a||e.value||e.setAttribute("placeholder",e.dataset.placeholderBg)}(e?e.get(0):document).querySelectorAll(".input-tel").forEach((function(e){var t=e.dataset.countryCode?e.dataset.countryCode.toLowerCase():"auto",n=e.dataset.fullNumberInputName,s=f()(e,{preferredCountries:[],separateDialCode:!0,initialCountry:t,hiddenInput:n});a(e,parseInt(s.getSelectedCountryData().dialCode)),e.addEventListener("countrychange",(function(){var e=s.getSelectedCountryData();a(this,parseInt(e.dialCode))})),e.addEventListener("keyup",(function(){var e=s.getSelectedCountryData();a(this,parseInt(e.dialCode))}))}))}(e),function(e){var a=o()("[data-installation-progress]",e);if(a.length){var t,n={initializing:o()("#step-initializing"),installing:o()("#step-installing"),finalizing:o()("#step-finalizing"),completed:o()("#step-completed"),error:o()("#install-error")},s=a.data("installation-progress"),i=0,l=o()(".progress-bar"),r=o()(".progress-title"),d=0;t=setTimeout((function e(){if(++i>120)return clearTimeout(t),n.initializing.removeClass("active"),n.installing.removeClass("active"),n.finalizing.removeClass("active"),void n.error.addClass("active");c({url:s,success:function(a){if(a.progress){if("initializing"===a.progress)n.initializing.addClass("active"),d=30,l.css("width",d+"%"),r.css("width",d+"%"),r.text(d+"%");else if("installing"===a.progress)n.initializing.removeClass("active"),n.installing.addClass("active"),d=60,l.css("width",d+"%"),r.css("width",d+"%"),r.text(d+"%");else if("finalizing"===a.progress)n.initializing.removeClass("active"),n.installing.removeClass("active"),n.finalizing.addClass("active"),d=90,l.css("width",d+"%"),r.css("width",d+"%"),r.text(d+"%");else if("completed"===a.progress)return clearTimeout(t),n.initializing.removeClass("active"),n.installing.removeClass("active"),n.finalizing.removeClass("active"),n.completed.addClass("active"),d=100,l.css("width",d+"%"),r.css("width",d+"%"),void r.text(d+"%");"completed"!==a.progress&&(t=setTimeout(e,1e3))}}})}),1e3)}}(e)}function b(e,a,t){document.addEventListener(e,(function(e){var n=e.target.closest(a);n&&(t(e,n),e.preventDefault())}))}function y(e,a){return e&&e.scrollTop(a),a}function w(e){return Math.max.apply(null,o()(e||".panel, .ajax-page").map((function(){var e;return isNaN(e=parseInt(o()(this).css("z-index"),10))?1:e+1})))}function C(e,a){var t=w(),n=o()('<span class="js-box-loader-backdrop">').css("z-index",t),s=o()('<span class="js-box-loader">').css("z-index",t+1);s.append('<span class="js-box-loader-animation">');var i=a||"Please wait...";s.append('<span class="js-box-loader-text">'+i+"</span>");var l=!0;return(e=o()(e)).hasClass("js-box-loader-container")&&(e.find(".js-box-loader").html()!==s.html()?(e.find(".js-box-loader").remove(),e.find(".js-box-loader-backdrop").remove(),e.removeClass("js-box-loader-container")):l=!1),l&&(e.addClass("js-box-loader-container"),e.append(n),e.append(s)),e}function k(e){return(e=o()(e)).find(".js-box-loader").fadeOut(200,(function(){e.removeClass("js-box-loader-container"),e.find(".js-box-loader-backdrop").remove(),o()(this).remove()})),e}function _(){return{Android:function(){return navigator.userAgent.match(/Android/i)},BlackBerry:function(){return navigator.userAgent.match(/BlackBerry/i)},iOS:function(){return navigator.userAgent.match(/iPhone|iPad|iPod/i)},Opera:function(){return navigator.userAgent.match(/Opera Mini/i)},Windows:function(){return navigator.userAgent.match(/IEMobile/i)},any:function(){return this.Android()||this.BlackBerry()||this.iOS()||this.Opera()||this.Windows()}}}function j(){var e=document.createElement("div");e.style.visibility="hidden",e.style.width="100px",document.body.appendChild(e);var a=e.offsetWidth;e.style.overflow="scroll";var t=document.createElement("div");t.style.width="100%",e.appendChild(t);var n=t.offsetWidth;return e.parentNode.removeChild(e),a-n}function N(e){var a=e.closest(".panel");a.length>0&&!a.hasClass("js-no-submit-dismiss")&&a.trigger("dismiss.panel")}var x=[];function P(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ajax";return!x[e]&&(x[e]=!0,!0)}function S(){x[arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ajax"]=!1}function q(e){return function(e){if(Array.isArray(e))return T(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,a){if(!e)return;if("string"==typeof e)return T(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return T(e,a)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,n=new Array(a);t<a;t++)n[t]=e[t];return n}function A(e,a){P()&&(a||(a={}),a.url||(a.url=e.data("ajax-toast")),e.is("._button")?e.trigger("loading.start"):C("body"),c({url:a.url,success:function(a){e.is("._button")?m(e):k("body"),S(),e.trigger("cc.ajax.success",[a])},error:function(a){e.is("._button")?m(e):k("body"),S(),e.trigger("cc.ajax.error",[a])}}))}function O(){o()(document).on("click","[data-ajax-toast]",(function(e){e.preventDefault();var a=o()(this);a.data("confirm")?function(e){var a={type:"modal",size:"small",close:!1};a.title=e.data.title||TranslationLabels.Confirm,a.subtitle=e.data.message;var t=e.data.cancelButtonText||TranslationLabels.Cancel,n=o()('<div class="col-sm-5">'),s=o()('<button class="_button secondary ghost full" data-dismiss="panel">');s.html('<span class="_button-text">'+t+"</span>"),s.appendTo(n);var i=e.data.confirmButtonText||TranslationLabels.Confirm,l=o()('<div class="col-sm-5 text-right">'),r=o()('<button class="_button primary full" data-dismiss="panel">');r.html('<span class="_button-text">'+i+"</span>"),r.appendTo(l),a.footer=o()('<div class="row justify-content-center">'),a.footer.append(n),a.footer.append(l),e.cancel&&s.on("click",e.cancel),e.success&&r.on("click",e.success),o()(document).trigger("create.panel",[a])}({data:a.data("confirm"),success:function(){A(a)}}):A(a)}))}function z(){window.PanelsLength=0,window.PositionTop=0,window.ClassPanel=function(e,a){var t=function(){for(var e in{toString:1})if("toString"===e)return!1;return!0}(),n=null,s=[].slice.call(arguments);function i(){}"[object Function]"===Object.prototype.toString.call(s[0])&&(n=s.shift());var o=function(){this.__construct.apply(this,arguments)};for(var l in o.superclass=n,o.subClasses=[],o.addMethods=function(e){var a=Object.keys(e);t&&(e.toString!==Object.prototype.toString&&a.push("toString"),e.valueOf!==Object.prototype.valueOf&&a.push("valueOf"));for(var n=0,s=a.length;n<s;n++){var i=a[n];this.prototype[i]=e[i]}return this},n&&(i.prototype=n.prototype,o.prototype=new i,n.subClasses.push(o),o.addMethods(n.prototype)),s||{})s.hasOwnProperty(l)&&o.addMethods(s[l]);return o.prototype.__construct||(o.prototype.__construct=function(){}),o.prototype.constructor=o,o}({$opener:null,opened_callback:null,elements:{$panel:null,$backdrop:null,$inner:null,$content:null,$title:null,$subtitle:null,$header:null,$body:null,$footer:null,$close:null},options:{instance:null,type:null,header:null,title:null,subtitle:null,body:null,footer:null,size:null,class:null,url:null,position:null,history:null,close:!0},defaults:{position:"right",parent:"body",parentOpenedClass:"panel-open",CSSTransitionDelay:300},init:function(){var e=this;o()(document).off(".cc.panel").on("click.cc.panel","*[data-panel]",(function(e){e.preventDefault();var a=new ClassPanel;return a.$opener=o()(this),a.options.instance=a.$opener.data("panel-instance")||null,a.options.type=a.$opener.data("panel-type")||"modal",a.options.header=a.$opener.data("panel-header")||null,a.options.title=a.$opener.data("panel-title")||null,a.options.subtitle=a.$opener.data("panel-subtitle")||null,a.options.body=a.$opener.data("panel-body")||a.$opener.data("panel-html")||null,a.options.footer=a.$opener.data("panel-footer")||null,a.options.size=a.$opener.data("panel-size")||null,a.options.class=a.$opener.data("panel-class")||null,a.options.position=a.$opener.data("panel-position")||null,a.options.history=a.$opener.data("panel-history")||null,a.options.url=a.$opener.data("panel")||a.$opener.attr("href")||null,a.create(),!1})).on("create.panel",(function(e,a){var t=new ClassPanel;t.options.instance=a.instance||null,t.options.type=a.type||"modal",t.options.header=a.header||null,t.options.title=a.title||null,t.options.subtitle=a.subtitle||null,t.options.body=a.body||a.html||null,t.options.footer=a.footer||null,t.options.size=a.size||null,t.options.class=a.class||null,t.options.position=a.position||null,t.options.history=a.history||null,t.options.url=a.url||null,t.create()})).on("click.cc.panel",'[data-dismiss="panel"]',(function(a){var t=o()(this).closest("[data-dismiss-panel]").length?o()(this).closest("[data-dismiss-panel]"):o()(this),n=o()('[data-dismiss-panel="'+t.data("dismiss-panel")+'"]');n.removeClass("open"),e.multiplePanels(!1),e.removeParentSettings(),setTimeout((function(){n.trigger("dismiss.panel")}),e.defaults.CSSTransitionDelay),a.preventDefault()}))},create:function(){if(window.PanelsLength++,this.createBackdrop(),this.createPanel(),this.addParentSettings(),C(this.elements.$panel),(this.options.title||this.options.subtitle)&&this.createPanelHeader(this.options.title,this.options.subtitle),this.options.body)this.createPanelBody(this.options.body),o()(document).trigger("cc.panel.success",[this.elements.$panel]);else if(this.isSelector(this.options.url))this.createPanelBody(o()(this.options.url).html()),o()(document).trigger("cc.panel.success",[this.elements.$panel]);else if(this.isLink(this.options.url)){var e=this;setTimeout((function(){c({url:e.options.url,success:function(a){("title"in a||"subtitle"in a)&&e.createPanelHeader(a.title,a.subtitle),"html"in a?e.createPanelBody(a.html):"body"in a&&e.createPanelBody(a.body),"footer"in a&&e.createPanelFooter(a.footer),o()(document).trigger("cc.panel.success",[e.options.panel]),"history"in a&&e.options.panel&&(e.options.panel.data("history",{href:document.location.href,title:document.title}),History.pushState({link:a.history.href},a.history.title,a.history.href),o()(window).one("popstate",(function(){e.options.panel&&e.options.panel.find('[data-dismiss="panel"]').click()})))},error:function(e,a,t){console.log("status: ".concat(e.status,"; text status: ").concat(a,", error thrown: ").concat(t))}})}),e.defaults.CSSTransitionDelay)}this.options.footer&&this.createPanelFooter(this.options.footer),this.createPanelHistory()},createPanelHistory:function(){var e=this.options.history;e&&e.hasOwnProperty("new")&&e.hasOwnProperty("old")&&(History.pushState({link:e.new},document.title,e.new),this.elements.$panel.on("dismiss.panel",(function(){History.pushState({link:e.old},document.title,e.old)})),o()(window).off(".popstatePanel").on("popstate.popstatePanel",(function(e){e.originalEvent&&o()('[data-dismiss="panel"]').reverse().first().click()})))},createPanelHeader:function(e,a){var t=this.options.type+"-header";return this.elements.$header=o()('<div class="'+t+'">'),e&&this.elements.$header.append(this.createPanelTitle(e)),a&&this.elements.$header.append(this.createPanelSubtitle(a)),this.elements.$content.append(this.elements.$header),this.elements.$header},createPanelTitle:function(e){var a=this.options.type+"-title";this.elements.$title=o()('<div class="'+a+'">');var t=o()('<div class="'+a+'-main">').appendTo(this.elements.$title);if(e instanceof o()&&e.is("[data-panel-title]")){var n=e.filter('[data-panel-title="main"]');n.length&&t.append("<h2>"+n.html()+"</h2>");var s=e.filter('[data-panel-title="addon"]');s.length&&this.elements.$title.append('<div class="'+a+'-addon">'+s.html()+"</div>")}else t.append("<h2>"+(this.isSelector(e)?o()(e).html():e)+"</h2>");return this.elements.$title},createPanelSubtitle:function(e){var a=this.options.type+"-subtitle";return this.elements.$subtitle=o()('<div class="'+a+'">'),this.elements.$subtitle.append(this.isSelector(e)?o()(e).html():e),this.elements.$subtitle},createPanelBody:function(e){var a=this.options.type+"-body";this.elements.$body=o()('<div class="'+a+'">');var t=o()('<div class="'+a+'-section">').html(e),n=t.find("[data-panel-title-stack]"),s=n.find("[data-panel-title]"),i=n.siblings("[data-panel-subtitle]");return(s.length||i.length)&&(this.createPanelHeader(s,i),n.remove()),this.elements.$body.html(t),this.options.header?this.elements.$body.insertAfter(this.elements.$header):this.elements.$body.appendTo(this.elements.$content),o().isFunction(this.opened_callback)&&this.opened_callback.call(this),y(this.elements.$panel,0),k(this.elements.$panel),this.options.footer||g(this.elements.$panel),this.elements.$body},createPanelFooter:function(e){var a=this.options.type+"-footer";return this.elements.$footer=o()('<div class="'+a+'">').html(e),this.elements.$content.append(this.elements.$footer),g(this.elements.$panel),k(this.elements.$panel),this.elements.$footer},createPanelCloseButton:function(){if(this.options.close){var e=this.options.type+"-close";this.elements.$close=o()('<button type="button" class="'+e+'" data-dismiss="panel"><i class="fa fa-times"></i></button>');var a=this.elements.$close.clone();return this.elements.$inner.prepend(a),this.elements.$panel.prepend(this.elements.$close),this.elements.$close}},createPanelContent:function(){var e=this.options.type+"-content";return o()('<div class="'+e+' js-scroll-element">')},createPanelInner:function(){var e=this.options.type+"-inner";return o()('<div class="'+e+' js-scroll-container">')},createPanel:function(){this.elements.$panel=o()('<div class="panel '+this.options.type+" "+this.getPanelPosition()+' cc-checkout-style">').attr("tabindex",-1).attr("data-dismiss-panel","panel-"+this.getId()).css("z-index",w()),this.elements.$inner=this.createPanelInner(),this.elements.$content=this.createPanelContent(),this.elements.$panel&&this.elements.$panel.trigger("dismiss.panel"),this.options.class&&this.elements.$panel.addClass(this.options.class),this.options.size&&this.elements.$panel.addClass(this.options.size),this.getParent().append(this.elements.$panel),this.elements.$inner.append(this.elements.$content),this.elements.$panel.append(this.elements.$inner),this.panelDismiss(),this.createPanelCloseButton();var e=this;return setTimeout((function(){window.PositionTop=o()(window).scrollTop(),e.multiplePanels(!0),e.elements.$panel.addClass("open"),e.elements.$backdrop&&e.elements.$backdrop.addClass("open")}),5),setTimeout((function(){/iPhone|iPod|iPad/.test(navigator.userAgent)&&o()("html").addClass("ios-device")}),this.defaults.CSSTransitionDelay),this.elements.$panel},createBackdrop:function(){var e=this.options.type+"-backdrop";return this.elements.$backdrop=o()('<div class="panel-backdrop '+e+'">').attr("data-dismiss","panel").attr("data-dismiss-panel","panel-"+this.getId()).css("z-index",w()),o()(".panel").length>1&&this.elements.$backdrop.addClass("panel-backdrop-secondary"),this.getParent().append(this.elements.$backdrop),this.elements.$backdrop},panelDismiss:function(){if(this.elements.$panel){var e=this;return this.elements.$panel.on("dismiss.panel",(function(){var a=o()('[data-dismiss-panel="'+o()(this).data("dismiss-panel")+'"]'),t=a.filter(".panel").data("history");a.remove(),e.removeParentSettings(),o()(".panel").length||(/iPhone|iPod|iPad/.test(navigator.userAgent)&&o()("html").removeClass("ios-device"),y(o()(window),window.PositionTop)),t&&History.pushState({link:t.href},t.title,t.href),e.options.instance=null}))}return!1},multiplePanels:function(e){var a=o()(".panel"),t=e?1:2;a.each((function(){var e=5*(a.length-t)+"%";o()(this).css({right:e}),t++}))},isSelector:function(e){try{return!this.isLink(e)&&!!o()(e).length}catch(e){return!1}},isLink:function(e){return"string"==typeof e&&(e.indexOf("http://")>-1||e.indexOf("https://")>-1)},getId:function(){return window.PanelsLength},getParent:function(){return o()(this.defaults.parent)},addParentSettings:function(){this.getParent().addClass(this.defaults.parentOpenedClass).css({"padding-right":j()}),o()(".js-header-fixed").css({"padding-right":j()})},removeParentSettings:function(){o()("[data-dismiss-panel]").length||(this.getParent().removeClass(this.defaults.parentOpenedClass).css({"padding-right":0}),o()(".js-header-fixed").css({"padding-right":0}))},getPanelPosition:function(){return void 0===this.options.position||-1===o().inArray(this.options.position,["left","right"])?this.defaults.position:this.options.position}}),window.CCPanel=new ClassPanel,CCPanel.init()}function D(){o()(".js-choose-plan-radio").on("change",(function(){var e=o()(this);o()("#price-to-be-paid").html(e.data("price")),o()("#plan-mapping").val(e.data("plan-mapping")),o()("#discount_code").val(e.data("discount-code"))})),o()("#js-add-discount-code-button").on("click",(function(e){o()("#plan-discount-code").toggleClass("hidden"),e.preventDefault()})),o()("#BuyPlan").on("submit",(function(){var e=o()(this).find("input:checked").data("gtm-product");dataLayer.push({event:"checkout",ecommerce:{checkout:{products:[e]}}})})),o()(".js-set-discount-code").on("click",(function(e){var a=o()(this);c({url:a.data("route"),type:"post",data:{_token:a.data("csrf"),discount_code:o()("#discount_code").val(),"plan-mapping":o()("#plan-mapping").val()},success:function(e){!function(e){o()("#plan-discount-code").addClass("hidden"),o()("#price-to-be-paid").html(e.plan_discounted_price);var a=o()("#BuyPlan").find("input:checked");a.data("discount-code",e.discount_code),a.data("price",e.plan_discounted_price),a.siblings("._radio-text").html(e.text),a.closest("._card").find(".js-discount-percent").html(e.discount_percent)}(e)}}),e.preventDefault()}))}function I(){var e=o()(".js-gear-slider"),a=e.find("._gear-slide:first"),t=o()(a.data("target")),n=null;o()(document).ready((function(){s()}));var s=function(){setTimeout((function(){n?(n.removeClass("fading"),n.addClass("hide")):(a.removeClass("fading"),a.addClass("hide")),a.removeClass("hide"),setTimeout((function(){a.addClass("current"),e.removeClass("loading").height(a.outerHeight(!0))}),10),setTimeout((function(){e.height("auto")}),500)}),2e3)};o()(document).on("cc.ajax.success","#RegisterDetails",(function(){a.removeClass("current"),a.addClass("fading"),e.addClass("loading").height(a.outerHeight(!0)),a.removeClass("current"),n=a,a=t,s()}))}var E={global:[{content:"<div class='facebook-chat-message-text'><p> Hi there and welcome to our CloudCart shop! I am CloudIO 🤖 and I am here to answer all your questions and help you find the products you want.<br>What are you looking for? 👇</p></div>",delay:3e3,align:"left"},{content:"<div class='facebook-chat-message-tags'><span class='facebook-chat-message-tag'>Shirts</span><span class='facebook-chat-message-tag'>Shoes</span><span class='facebook-chat-message-tag'>Cups & Mugs</span><span class='facebook-chat-message-tag'>Dolor</span></div>",delay:300,align:"left"},{content:"<div class='facebook-chat-message-text'><p>Shoes</p></div>",delay:3e3,align:"right"},{content:"<div class='facebook-chat-message-text'><p>Great choice! Let me show you our exclusive CloudCart shoes. Get them now because quantities are limited! 🕝💙</p></div>",delay:3e3,align:"left"},{content:"<div class='facebook-chat-message-figure'><img src='https://cdncloudcart.com/storage/cc-shoe.png'><div class='facebook-chat-message-figure-caption'><strong>CC Shoes by CloudCart</strong><br><br>$29.99<br>www.cloudcart.com</div><a class='facebook-chat-message-figure-caption' href='javascript:;' style='text-align: center;'>👉 BUY NOW 👈</a><a class='facebook-chat-message-figure-caption' href='javascript:;' style='text-align: center;'>Related Products</a></div>",delay:300,align:"left"},{content:"<div class='facebook-chat-message-text'><p>👉 BUY NOW 👈</p></div>",delay:3e3,align:"right"},{content:"<div class='facebook-chat-message-text'><p>Please, choose a color. 🤗</p></div>",delay:3e3,align:"left"},{content:"<div class='facebook-chat-message-tags'><span class='facebook-chat-message-tag'>Blue</span><span class='facebook-chat-message-tag'>White</span><span class='facebook-chat-message-tag'>Purple</span><span class='facebook-chat-message-tag'>Green</span><span class='facebook-chat-message-tag'>Yellow</span><span class='facebook-chat-message-tag'>Red</span><span class='facebook-chat-message-tag'>Silver</span></div>",delay:300,align:"left"},{content:"<div class='facebook-chat-message-text'><p>Blue</p></div>",delay:3e3,align:"right"},{content:"<div class='facebook-chat-message-text'><p>Please, choose a size. 🤗</p></div>",delay:3e3,align:"left"},{content:"<div class='facebook-chat-message-tags'><span class='facebook-chat-message-tag'>37</span><span class='facebook-chat-message-tag'>38</span><span class='facebook-chat-message-tag'>39</span><span class='facebook-chat-message-tag'>40</span><span class='facebook-chat-message-tag'>41</span><span class='facebook-chat-message-tag'>42</span></div>",delay:300,align:"left"},{content:"<div class='facebook-chat-message-text'><p>40</p></div>",delay:3e3,align:"right"},{content:"<div class='facebook-chat-message-text'><p>Thanks! Now give us your email address ✉</p></div>",delay:3e3,align:"left"},{content:"<div class='facebook-chat-message-text'><p><EMAIL></p></div>",delay:3e3,align:"right"},{content:"<div class='facebook-chat-message-text'><p>Is this the name you want to use for your order \"Your Name\" 👈❓</p></div>",delay:3e3,align:"left"},{content:"<div class='facebook-chat-message-tags'><span class='facebook-chat-message-tag'>Yes</span><span class='facebook-chat-message-tag'>No</span></div>",delay:300,align:"left"},{content:"<div class='facebook-chat-message-text'><p>Yes</p></div>",delay:3e3,align:"right"},{content:"<div class='facebook-chat-message-text'><p>Thanks! 👌 Now use the button below to choose your shipping address. 🌎</p></div>",delay:3e3,align:"left"},{content:"<div class='facebook-chat-message-tags'><span class='facebook-chat-message-tag'>📍 Send Location</span></div>",delay:300,align:"left"},{content:"<div class='facebook-chat-message-figure'><img src='https://cdncloudcart.com/storage/location-google.jpeg'><div class='facebook-chat-message-figure-caption'>Your Location</div></div>",delay:3e3,align:"right"},{content:"<div class='facebook-chat-message-text'><p>We found you! 🤗 Now give us your phone number. 📱</p></div>",delay:3e3,align:"left"},{content:"<div class='facebook-chat-message-text'><p>123456789</p></div>",delay:3e3,align:"right"},{content:"<div class='facebook-chat-message-text'><p>Thanks! 👌  Please, check if all the data you gave us is correct. First name ➡️ Last name ➡️ <EMAIL> ➡️ Phone: 0873245498  ➡️ Address: 11 Location avenue</p></div>",delay:3e3,align:"left"},{content:"<div class='facebook-chat-message-tags'><span class='facebook-chat-message-tag'>Yes</span><span class='facebook-chat-message-tag'>No</span></div>",delay:300,align:"left"},{content:"<div class='facebook-chat-message-text'><p>Yes</p></div>",delay:3e3,align:"right"},{content:"<div class='facebook-chat-message-text'><p>Thank you so much! 🤗 See the product you've selected to purchase 👇</p></div>",delay:3e3,align:"left"},{content:"<div class='facebook-chat-message-figure'><img src='https://cdncloudcart.com/storage/cc-shoe.png'><div class='facebook-chat-message-figure-caption'><strong>CC Shoes by CloudCart</strong></div><div class='facebook-chat-message-figure-caption'>$29.99<br>www.cloudcart.com</div><a class='facebook-chat-message-figure-caption' href='javascript:;' style='text-align: center;'>remove from cart</a><a class='facebook-chat-message-figure-caption' href='javascript:;' style='text-align: center;'>🗸 ORDER NOW 🗸</a></div>",delay:300,align:"left"},{content:"<div class='facebook-chat-message-text'><p>🗸 ORDER NOW 🗸</p></div>",delay:3e3,align:"right"},{content:"<div class='facebook-chat-message-text'><p>Thank you for your order! 👌 Please, expect a delivery this friday. 🤗 Have a great day! 🤗</p></div>",delay:3e3,align:"left"}]};var L=t(821),B={class:"cc-popup__select"},M=["name","data-placeholder","id","dependency-id"],V=(0,L._)("option",{value:"",selected:""},null,-1),H=["value","id"],K={class:"cc-form__row cc-form__row--questions"},F=["id","dependency-id","placeholder"],W={class:"cc-popup__actions cc-form__actions"},Z=["disabled"],G={key:0,class:"loader-dots"},R=[(0,L._)("span",{class:"loader-dot"},null,-1),(0,L._)("span",{class:"loader-dot"},null,-1),(0,L._)("span",{class:"loader-dot"},null,-1)],U={key:1,class:"_button-text"};const Y={props:["items","submitButtonText","inputPlaceholder","selectPlaceholder","url"],data:function(){return{chosenAnswers:[],dependency:!1,dataValid:!1,allValues:[],parsedItems:JSON.parse(this.items),submitLoader:!1}},methods:{submit:function(){if(!this.submitLoader){var e=this;e.chosenAnswers=[],$(".js-select-question, .js-questions-input",".js-question-step:not(.inactive)").map((function(){var a=$(this);e.chosenAnswers.push({id:a.attr("id"),value:a.val()})})),this.submitLoader=!0;var a=document.head.querySelector('meta[name="csrf-token"]').content;c({url:this.url,type:"post",data:{data:e.chosenAnswers,_token:a},success:function(e){this.submitLoader=!1},error:function(){this.submitLoader=!1}})}},checkFields:function(e){e.allValues=[],$(".js-question-step:not(.inactive) .js-select-question, .js-question-step:not(.inactive) .js-questions-input").map((function(){var a=$(this);e.allValues.push(a.val())}));var a=$.inArray("",e.allValues);e.dataValid=-1===a}},computed:{},mounted:function(){var e=this;document.body.classList.add("questions-wrap"),this.$nextTick((function(){!function(e){var a=e.fn.select2.amd.require("select2/defaults");e.extend(a.defaults,{dropdownPosition:"auto"});var t=e.fn.select2.amd.require("select2/dropdown/attachBody");t.prototype._positionDropdown;t.prototype._positionDropdown=function(){var a=e(window),t=this.$dropdown.hasClass("select2-dropdown--above"),n=this.$dropdown.hasClass("select2-dropdown--below"),s=null,i=this.$container.offset();i.bottom=i.top+this.$container.outerHeight(!1);var o={height:this.$container.outerHeight(!1)};o.top=i.top,o.bottom=i.top+o.height;var l=this.$dropdown.outerHeight(!1),r=a.scrollTop(),c=a.scrollTop()+a.height(),d=r<i.top-l,u=c>i.bottom+l,p={left:i.left,top:o.bottom},m=this.$dropdownParent;"static"===m.css("position")&&(m=m.offsetParent());var h=m.offset();p.top-=h.top,p.left-=h.left;var v=this.options.get("dropdownPosition");"above"===v||"below"===v?s=v:(t||n||(s="below"),u||!d||t?!d&&u&&t&&(s="below"):s="above"),("above"==s||t&&"below"!==s)&&(p.top=o.top-h.top-l),null!=s&&(this.$dropdown.removeClass("select2-dropdown--below select2-dropdown--above").addClass("select2-dropdown--"+s),this.$container.removeClass("select2-container--below select2-container--above").addClass("select2-container--"+s)),this.$dropdownContainer.css(p)}}(window.jQuery),$(".js-select-question").select2({dropdownPosition:"below",placeholder:"Choose an option",minimumResultsForSearch:-1,allowClear:!1}),setTimeout((function(){$(".js-question-step:first-child").addClass("open")}),800),setTimeout((function(){$(".js-question-step.open .js-select-question").select2("open")}),1600),$(".js-select-question").on("select2:select",(function(a){for(var t=$(this).closest(".js-question-step"),n=[],s=0;s<a.target.options.length;s++)n.push(a.target.options[s].id);$(".js-select-question, .js-questions-input").map((function(){var e=$(this),t=e.closest(".js-question-step");n.filter((function(a){return e.attr("dependency-id").split(",").includes(a)})).length&&(e.attr("dependency-id").includes(a.target.value)?t.hasClass("inactive")&&(t.addClass("active").removeClass("inactive"),e.addClass("active").removeClass("inactive")):t.hasClass("active")&&(t.removeClass("active").addClass("inactive"),e.removeClass("active").addClass("inactive")),e.is("select")?e.hasClass("inactive")?e.select2("enable",!1):e.hasClass("active")&&e.select2("enable"):e.hasClass("inactive")?e.attr("disabled","disabled"):e.hasClass("active")&&e.removeAttr("disabled"))})),$(t.next(".js-question-step:not(.inactive)")).nextAll(".js-question-step").map((function(){var e=$(this);e.removeClass("open"),e.find(".js-select-question, .js-questions-input").val()&&e.find(".js-select-question, .js-questions-input").val(null).trigger("change.select2")})),$(t.nextAll(".js-question-step")[0]).find(".js-select-question, .js-questions-input").val()&&$(t.nextAll(".js-question-step")[0]).find(".js-select-question, .js-questions-input").val(null).trigger("change.select2"),e.checkFields(e),$(t.nextAll(".js-question-step:not(.inactive)")[0]).addClass("open"),$(t.nextAll(".js-question-step:not(.inactive)")[0]).find(".js-select-question").val()||$(t.nextAll(".js-question-step:not(.inactive)")[0]).find(".js-select-question").select2("open"),setTimeout((function(){t.next(".js-question-step:not(.inactive)").find(".js-questions-input").parent().addClass("focused"),$(t.next(".js-question-step:not(.inactive)").find(".js-questions-input")).focus()}),750)})),$(".js-select-question").on("select2:open",(function(e){$(".select2-dropdown").hide(),setTimeout((function(){jQuery(".select2-dropdown").slideDown(800)}))})),$(".js-questions-input",".js-question-step").on("blur",(function(){$(this).closest(".js-question-step").next(".js-question-step").find(".js-select-question").val()})),$(".js-questions-input",".js-question-step").on("keyup",(function(){var a=$(this);e.checkFields(e),a.val().length>5&&!a.closest(".js-question-step").next(".js-question-step").find(".js-select-question").val()&&(a.closest(".js-question-step").next(".js-question-step").addClass("open"),a.closest(".js-question-step").next(".js-question-step").find(".js-select-question").select2("open"),$(a.closest(".js-question-step").nextAll(".js-question-step:not(.inactive)")[0]).addClass("open"),$(a.closest(".js-question-step").nextAll(".js-question-step:not(.inactive)")[0]).find(".js-select-question").val()||$(a.closest(".js-question-step").nextAll(".js-question-step:not(.inactive)")[0]).find(".js-select-question").select2("open"))}));var a=document.querySelectorAll(".js-questions-input");if(a)for(var t=0;t<a.length;t++)a[t].setAttribute("size",a[t].getAttribute("placeholder").length+3),a[t].addEventListener("keyup",n);function n(){this.value.length?this.style.width=this.value.length+"ch":this.style.width=this.getAttribute("placeholder").length+"ch"}}))}};var J=t(379),Q=t.n(J),X=t(305),ee={insert:"head",singleton:!1};Q()(X.Z,ee);X.Z.locals;const ae=(0,t(744).Z)(Y,[["render",function(e,a,t,n,s,i){return(0,L.wg)(),(0,L.iD)("div",null,[((0,L.wg)(!0),(0,L.iD)(L.HY,null,(0,L.Ko)(s.parsedItems,(function(e,a){return(0,L.wg)(),(0,L.j4)(L.uT,{key:e.id},{default:(0,L.w5)((function(){return[(0,L._)("div",{class:(0,L.C_)(["question-step js-question-step",[{inactive:e.dependency.length},{industry:1===e.industry}]])},[(0,L._)("span",null,(0,L.zw)(e.value_translated),1),e.answers.length?((0,L.wg)(),(0,L.j4)(L.uT,{key:0},{default:(0,L.w5)((function(){return[(0,L._)("div",B,[(0,L._)("select",{name:e.id,class:(0,L.C_)(["js-select-question",{inactive:e.dependency.length}]),"data-placeholder":t.selectPlaceholder,id:e.id,"dependency-id":e.dependency},[V,((0,L.wg)(!0),(0,L.iD)(L.HY,null,(0,L.Ko)(e.answers,(function(e){return(0,L.wg)(),(0,L.iD)("option",{value:e.id,id:e.id,key:e.id},(0,L.zw)(e.value_translated),9,H)})),128))],10,M)])]})),_:2},1024)):((0,L.wg)(),(0,L.j4)(L.uT,{key:1},{default:(0,L.w5)((function(){return[(0,L._)("div",K,[(0,L._)("input",{type:"text",id:e.id,"dependency-id":e.dependency,class:"cc-field cc-field--icon cc-form__field js-questions-input",placeholder:t.inputPlaceholder,required:""},null,8,F)])]})),_:2},1024))],2)]})),_:2},1024)})),128)),(0,L._)("div",W,[s.dataValid?((0,L.wg)(),(0,L.iD)("button",{key:0,type:"submit",class:(0,L.C_)(["cc-form__btn cc-btn cc-btn--orange cc-btn--large js-submit",{loading:s.submitLoader}]),onClick:a[0]||(a[0]=function(){return i.submit&&i.submit.apply(i,arguments)}),disabled:s.submitLoader},[s.submitLoader?((0,L.wg)(),(0,L.iD)("span",G,R)):((0,L.wg)(),(0,L.iD)("span",U,(0,L.zw)(t.submitButtonText),1))],10,Z)):(0,L.kq)("",!0)])])}]]);window.$=window.jQuery=t(755);var te,ne,se,ie,oe,le,re,ce,de,ue,pe,me,he;o()(document).ajaxSuccess((function(e,a){var t=a.responseJSON,n=o()(e.target.activeElement),s=n.closest("form");278===a.status&&a.responseJSON.redirect_url&&(window.parent?window.parent:window).location.replace(a.responseJSON.redirect_url),t&&t.success&&r().success(t.success),t&&s.length&&(s.hasClass("js-no-submit-dismiss")||N(s),"submit"===n.attr("type")&&s.trigger("cc.ajax.success",[t]),S(),t.body&&!n.closest("[data-panel]").length&&o()(document).trigger("create.panel",[t]))})),o()(document).ajaxError((function(e,a){var t=a.responseJSON,n=o()(e.target.activeElement).closest("form");n.length||(n=o()("form.js-ajax-form").first()),n.length&&(p(n),S()),t.errors?n.length&&(n.trigger("cc.ajax.error",[{error:t.errors}]),o().each(t.errors,(function(e,a){if(~e.indexOf(".")){var s=(e=e.split("."))[0];e.shift(),o().each(e,(function(e,a){"0"===a&&(a=""),s=s+"["+a+"]"})),e=s}var i=n.find('*[name^="'+e+'"]');i.addClass("input-has-error"),i.closest("._form-group").addClass("has-error").append('<p class="_form-error">'+a+"</p>"),i.length?(i.val().length>0?i.closest(".cc-form__row").addClass("invalid"):i.closest(".cc-form__row").addClass("invalid invalid-empty"),i.closest(".cc-form__controls").append('<span class="cc-form__error">'+a+"</span>")):r().error("Error: "+t.message)}))):419===a.status?r().error("Expired XSRF-TOKEN. Please reload the page and try again."):r().error("Error: "+t.message)})),g(),z(),o()(document).on("cc.ajax.reload","[data-ajax-box]",(function(e,a){e.stopPropagation();var t=o()(this),n=a?"post":"get";C(t),c({url:t.data("ajax-box"),data:a,type:n,success:function(e){k(t),e.error?(t.trigger("cc.ajax.error",[e]),r().error(e.error)):(t.html(e.content),t.trigger("cc.ajax.success",[e]),g(t))},error:function(){k(t)}})})),o()(document).on("cc.ajax.success",".js-reset-success",(function(){o()(this)[0].reset()})),o()(document).on("cc.ajax.success","[data-ajax-reload]",(function(){var e=o()(this);o()(e.data("ajax-reload")).trigger("cc.ajax.reload")})),O(),h(),o()(document).on("cc.ajax.success","[data-location-reload]",(function(){window.location.reload()})),o()(document).on("loading.start","._button, .cc-form__btn",(function(e){e.preventDefault(),e.stopPropagation();var a=o()('<span class="loader-dots"><span class="loader-dot"></span><span class="loader-dot"></span><span class="loader-dot"></span></span>'),t=o()(this),n=o()(".button-next ._button-addon");t.addClass("loading").append(a),n.css({display:"none"}),t.on("click, click.loading",(function(e){e.preventDefault(),e.stopPropagation()}))})),o()(document).on("loading.end","._button, .cc-form__btn",(function(e){e.preventDefault(),e.stopPropagation();var a=o()(this),t=o()(".button-next ._button-addon");a.removeClass("loading").find(".loader-dots").remove(),t.css({display:"unset"}),a.off("click.loading")})),b("click",".disabled",(function(e){e.preventDefault()})),b("click","[data-href]",(function(e,a){window.location.href=a.dataset.href})),b("click","[data-toggle-class]",(function(e,a){document.querySelectorAll(a.dataset.target).forEach((function(e){a.dataset.toggleClass.split(" ").forEach((function(a){e.classList.toggle(a)}))}))})),b("click","[data-open-tab]",(function(e,a){var t=q(a.parentElement.children).filter((function(e){return 1===e.nodeType&&e!==a}));a.classList.add("active"),t.forEach((function(e){e.classList.remove("active")}));var n=document.querySelectorAll(a.dataset.openTab),s=[];n.forEach((function(e){e.style.display="block",s=s.concat(q(e.parentElement.children).filter((function(a){return 1===a.nodeType&&a!==e})))})),s.forEach((function(e){e.style.display="none"}))})),b("click","[data-close]",(function(e,a){document.querySelectorAll(a.dataset.close).forEach((function(e){e.style.display="none"}))})),b("click",".js-accept-cookies",(function(e,a){s().set("consent-accepted","1",{path:"/"}),gtag("consent","update",{ad_personalization:"granted",ad_storage:"granted",ad_user_data:"granted",analytics_storage:"granted",functionality_storage:"granted",personalization_storage:"granted",security_storage:"granted"})})),function(){function e(e){var a='[for="'+e.getAttribute("id")+'"]',t=document.querySelectorAll(a);if(t.length){var n='[name="'+e.getAttribute("name")+'"]';document.querySelectorAll(n).forEach((function(e){var a='[for="'+e.getAttribute("id")+'"]';document.querySelectorAll(a).forEach((function(e){e.classList.remove("checked")}))})),t.forEach((function(e){e.classList.add("checked")}))}}b("change",'input[type="radio"]',(function(a,t){e(t)})),document.querySelectorAll('input[type="radio"]').forEach((function(a){a.checked&&e(a)}))}(),function(){var e=o()(window),a=e.height(),t=e.width(),n=o()(".js-parallax-item");function s(){var t=e.scrollTop();n.each((function(){var e=o()(this),n=o()(e.data("target")),s=n.length?n.offset().top:e.offset().top,i=n.length?n.outerHeight():e.outerHeight(),l=s-a+e.data("parallax-start"),r=s+i-a-e.data("parallax-stop"),c=e.data("offset-x"),d=e.data("offset-y"),u=e.data("scale"),p=e.data("opacity"),m=(t-l)/(r-l),h=c-c*m,v=d-d*m,f=u+(1-u)*m,g=p?m:1,b=!1,y=!e.data("isFirstTime")||e.data("isFirstTime"),w=e.data("setAnimation")?e.data("setAnimation"):y;m<=0&&w?(h=c,v=d,g=p?0:1,f=u,b=!0):m>1&&w?(h=0,v=0,g=1,f=1,b=!0):m>0&&m<=1&&(w=!0),w&&(e.css({transform:"translate("+h+"px, "+v+"px) scale("+f+")",opacity:g}),e.data("setAnimation",!b),setTimeout((function(){e.addClass("ready"),e.data("setAnimation",!1)}),100))}))}o()(document).ready((function(){s()})),e.on("scroll",(function(){s()})),(t<=1024||_().any())&&_().any()&&o()("body").addClass("is-mobile")}(),(te=o()(window)).width(),ne=!1,se=o()(".js-header-fixed"),ie=se.length,oe=function(){te.on("scroll load",(function(){ie&&le()}))},le=function(){te.scrollTop()>0?ne||re():ne&&ce()},re=function(){se.addClass("fixed"),ne=!0},ce=function(){se.removeClass("fixed"),ne=!1},oe(),o()(".js-nav-collapse").on("click",(function(e){o()(this).toggleClass("open").closest(".has-dropdown").toggleClass("open"),e.preventDefault()})),o()(document).on("click",".js-nav-toggle",(function(e){var a=o()(".js-nav-backdrop");a.length?(a.css({"pointer-events":"none"}).fadeOut(300,(function(){o()(this).remove()})),o()(".js-nav-toggle").removeClass("open"),o()(".js-nav-mobile").removeClass("open"),o()("body").removeClass("no-scroll")):((a=o()('<div class="_nav-mobile-backdrop js-nav-backdrop js-nav-toggle"></div>')).hide().insertAfter(".js-nav-mobile").fadeIn(300),o()(".js-nav-toggle").addClass("open"),o()(".js-nav-mobile").addClass("open"),o()("body").addClass("no-scroll")),e.preventDefault()})),o()(".js-plan-features-toggler").on("click",(function(e){var a=o()(this),t=o()(a.data("target"));t.css({display:"table-cell"}),t.siblings("[class*=js-plan-]").hide(),a.addClass("active").siblings().removeClass("active"),e.preventDefault()})),o()(".js-switch-toggler").on("click",(function(e){e.preventDefault();var a=o()(this),t=a.data("period");a.addClass("active").siblings().removeClass("active").css({transition:"ease-in .2s"}),o()("._price-card-price").addClass("hidden"),o()(".js-discount").addClass("hidden"),o()("."+t).removeClass("hidden")})),D(),I(),o()(".js-facebook-chat").each((function(){var e=o()(this),a=!1;o()(window).on("scroll",(function(){if(!(a||e.offset().top>o()(window).scrollTop()+o()(window).height())){a=!0;var t=0,n=E[e.data("chat-messages")],s=e.data("chat-avatar");o().each(n,(function(a,l){var r="",c=n[a+1];c&&(l.align===c.align?-1===c.content.indexOf("tags")&&(r="same"):r="last");var d=(t+=500)+l.delay,u=d+10,p="facebook-chat-message",m="left"===l.align?'<div class="'+p+'-avatar"><img class="'+p+'-avatar-image" src="'+s+'"></div>':"",h='<div class="'+p+" "+l.align+" "+r+'" style="display: none;">'+m+'<div class="'+p+'-spinner"><div class="'+p+'-spinner-dots"><div class="'+p+'-spinner-dot"></div><div class="'+p+'-spinner-dot"></div><div class="'+p+'-spinner-dot"></div></div></div><div class="'+p+'-content">'+l.content+"</div></div>",v=o()(h).appendTo(e.find(".js-facebook-chat-messages")),f="";"left"===l.align&&(f=v.prev(".left").length?v.prev(".left"):v.prevUntil(".left").prev()),f&&f.find(".facebook-chat-message-avatar-image").delay(t).hide(1),v.delay(t).fadeIn(),v.find(".facebook-chat-message-spinner").delay(d).hide(1),v.find(".facebook-chat-message-content").delay(u).fadeIn(),setTimeout(i,t),setTimeout(i,u),t=u}))}function i(){var a=e.find(".js-facebook-chat-inner");a.animate({scrollTop:a.prop("scrollHeight")})}})),o()(window).scroll()})),function(){var e=document.querySelectorAll(".js-scroll-video");if(e)for(var a=function(){var a=e[t];a.videoStarted=!1,n(a),window.addEventListener("scroll",(function(){n(a)}))},t=0;t<e.length;t++)a();function n(e){e.videoStarted||function(e){var a=0,t=0;if(e.offsetParent){do{a+=e.offsetTop,t+=e.offsetParent?e.offsetParent.scrollTop:0}while(e=e.offsetParent);return a-t}}(e)>window.scrollY+window.innerHeight||(e.play(),e.videoStarted=!0)}}(),de=$(".progress-bar"),ue=$(".progress-title"),pe=0,me=setInterval((function(){pe>=100?clearInterval(me):pe<100&&de.hasClass("details")&&(pe+=5,de.css("width",pe+"%"),ue.css("width",pe+"%"),ue.text(pe+"%"))}),80),o()("#migration_check").on("click",(function(){o()(this).is(":checked")?o()("#domain").attr("disabled",!1):o()("#domain").attr("disabled",!0)})),o()(".js-close-topline").on("click",(function(){o()(this).closest(".topline-wrap").slideUp().remove()})),he=".js-button-show-click",o()(he).on("click",(function(){o()(he).removeClass("hidden"),o()("._plan-features").not("._plan-features:first").removeClass("hidden"),o()(he).addClass("hidden")})),o()(document).on("click","#gdpr-trigger",(function(e){var a=300,t=o()("#gdpr-modal"),n=o()(".js-cookie"),s=o()("._header-actions.js-nav-mobile").hasClass("open");n.length?n.length&&s?((n=o()('<div class="_nav-mobile-backdrop js-nav-backdrop js-cookie"></div>')).hide().insertAfter("#gdpr-modal").fadeIn(a),t.addClass("show-modal"),t.parent(".panel-static-wrapper").show("fast"),o()("#tab-privacy").addClass("active"),o()("body").addClass("no-scroll")):(n.css({"pointer-events":"none"}).fadeOut(a,(function(){o()(this).remove(),t.parent(".panel-static-wrapper").hide("fast"),t.removeClass("show-modal")})),t.parent().hide("fast"),t.removeClass("show-modal"),o()("body").removeClass("no-scroll")):((n=o()('<div class="_nav-mobile-backdrop js-nav-backdrop js-cookie"></div>')).hide().insertAfter("#gdpr-modal").fadeIn(a),t.addClass("show-modal"),t.parent(".panel-static-wrapper").show("fast"),o()("#tab-privacy").addClass("active"),o()("body").addClass("no-scroll")),o()("#cookies-accept-modal").on("click",(function(){t.parent(".panel-static-wrapper").hide("fast"),t.removeClass("show-modal"),s||o()("body").removeClass("no-scroll"),n.css({"pointer-events":"none"}).fadeOut(a,(function(){o()(this).remove()}))})),o()(".js-cookie").on("click",(function(){t.parent(".panel-static-wrapper").hide("fast"),t.removeClass("show-modal"),s||o()("body").removeClass("no-scroll"),n.css({"pointer-events":"none"}).fadeOut(a,(function(){o()(this).remove()}))})),o()(".modal-tabs-title-close").on("click",(function(){t.parent(".panel-static-wrapper").hide("fast"),t.removeClass("show-modal"),s||o()("body").removeClass("no-scroll"),n.css({"pointer-events":"none"}).fadeOut(a,(function(){o()(this).remove()}))})),e.preventDefault()})),$(".js-boarding-app").length&&(0,L.ri)({}).component("boarding",ae).mount("#app")},396:e=>{e.exports.K={ab:{name:"Abkhaz",nativeName:"аҧсуа"},aa:{name:"Afar",nativeName:"Afaraf"},af:{name:"Afrikaans",nativeName:"Afrikaans"},ak:{name:"Akan",nativeName:"Akan"},sq:{name:"Albanian",nativeName:"Shqip"},am:{name:"Amharic",nativeName:"አማርኛ"},ar:{name:"Arabic",nativeName:"العربية"},an:{name:"Aragonese",nativeName:"Aragonés"},hy:{name:"Armenian",nativeName:"Հայերեն"},as:{name:"Assamese",nativeName:"অসমীয়া"},av:{name:"Avaric",nativeName:"авар мацӀ, магӀарул мацӀ"},ae:{name:"Avestan",nativeName:"avesta"},ay:{name:"Aymara",nativeName:"aymar aru"},az:{name:"Azerbaijani",nativeName:"azərbaycan dili"},bm:{name:"Bambara",nativeName:"bamanankan"},ba:{name:"Bashkir",nativeName:"башҡорт теле"},eu:{name:"Basque",nativeName:"euskara, euskera"},be:{name:"Belarusian",nativeName:"Беларуская"},bn:{name:"Bengali",nativeName:"বাংলা"},bh:{name:"Bihari",nativeName:"भोजपुरी"},bi:{name:"Bislama",nativeName:"Bislama"},bs:{name:"Bosnian",nativeName:"bosanski jezik"},br:{name:"Breton",nativeName:"brezhoneg"},bg:{name:"Bulgarian",nativeName:"български език"},my:{name:"Burmese",nativeName:"ဗမာစာ"},ca:{name:"Catalan; Valencian",nativeName:"Català"},ch:{name:"Chamorro",nativeName:"Chamoru"},ce:{name:"Chechen",nativeName:"нохчийн мотт"},ny:{name:"Chichewa; Chewa; Nyanja",nativeName:"chiCheŵa, chinyanja"},zh:{name:"Chinese",nativeName:"中文 (Zhōngwén), 汉语, 漢語"},cv:{name:"Chuvash",nativeName:"чӑваш чӗлхи"},kw:{name:"Cornish",nativeName:"Kernewek"},co:{name:"Corsican",nativeName:"corsu, lingua corsa"},cr:{name:"Cree",nativeName:"ᓀᐦᐃᔭᐍᐏᐣ"},hr:{name:"Croatian",nativeName:"hrvatski"},cs:{name:"Czech",nativeName:"česky, čeština"},da:{name:"Danish",nativeName:"dansk"},dv:{name:"Divehi; Dhivehi; Maldivian;",nativeName:"ދިވެހި"},nl:{name:"Dutch",nativeName:"Nederlands, Vlaams"},en:{name:"English",nativeName:"English"},eo:{name:"Esperanto",nativeName:"Esperanto"},et:{name:"Estonian",nativeName:"eesti, eesti keel"},ee:{name:"Ewe",nativeName:"Eʋegbe"},fo:{name:"Faroese",nativeName:"føroyskt"},fj:{name:"Fijian",nativeName:"vosa Vakaviti"},fi:{name:"Finnish",nativeName:"suomi, suomen kieli"},fr:{name:"French",nativeName:"français, langue française"},ff:{name:"Fula; Fulah; Pulaar; Pular",nativeName:"Fulfulde, Pulaar, Pular"},gl:{name:"Galician",nativeName:"Galego"},ka:{name:"Georgian",nativeName:"ქართული"},de:{name:"German",nativeName:"Deutsch"},el:{name:"Greek, Modern",nativeName:"Ελληνικά"},gn:{name:"Guaraní",nativeName:"Avañeẽ"},gu:{name:"Gujarati",nativeName:"ગુજરાતી"},ht:{name:"Haitian; Haitian Creole",nativeName:"Kreyòl ayisyen"},ha:{name:"Hausa",nativeName:"Hausa, هَوُسَ"},he:{name:"Hebrew (modern)",nativeName:"עברית"},hz:{name:"Herero",nativeName:"Otjiherero"},hi:{name:"Hindi",nativeName:"हिन्दी, हिंदी"},ho:{name:"Hiri Motu",nativeName:"Hiri Motu"},hu:{name:"Hungarian",nativeName:"Magyar"},ia:{name:"Interlingua",nativeName:"Interlingua"},id:{name:"Indonesian",nativeName:"Bahasa Indonesia"},ie:{name:"Interlingue",nativeName:"Originally called Occidental; then Interlingue after WWII"},ga:{name:"Irish",nativeName:"Gaeilge"},ig:{name:"Igbo",nativeName:"Asụsụ Igbo"},ik:{name:"Inupiaq",nativeName:"Iñupiaq, Iñupiatun"},io:{name:"Ido",nativeName:"Ido"},is:{name:"Icelandic",nativeName:"Íslenska"},it:{name:"Italian",nativeName:"Italiano"},iu:{name:"Inuktitut",nativeName:"ᐃᓄᒃᑎᑐᑦ"},ja:{name:"Japanese",nativeName:"日本語 (にほんご／にっぽんご)"},jv:{name:"Javanese",nativeName:"basa Jawa"},kl:{name:"Kalaallisut, Greenlandic",nativeName:"kalaallisut, kalaallit oqaasii"},kn:{name:"Kannada",nativeName:"ಕನ್ನಡ"},kr:{name:"Kanuri",nativeName:"Kanuri"},ks:{name:"Kashmiri",nativeName:"कश्मीरी, كشميري‎"},kk:{name:"Kazakh",nativeName:"Қазақ тілі"},km:{name:"Khmer",nativeName:"ភាសាខ្មែរ"},ki:{name:"Kikuyu, Gikuyu",nativeName:"Gĩkũyũ"},rw:{name:"Kinyarwanda",nativeName:"Ikinyarwanda"},ky:{name:"Kirghiz, Kyrgyz",nativeName:"кыргыз тили"},kv:{name:"Komi",nativeName:"коми кыв"},kg:{name:"Kongo",nativeName:"KiKongo"},ko:{name:"Korean",nativeName:"한국어 (韓國語), 조선말 (朝鮮語)"},ku:{name:"Kurdish",nativeName:"Kurdî, كوردی‎"},kj:{name:"Kwanyama, Kuanyama",nativeName:"Kuanyama"},la:{name:"Latin",nativeName:"latine, lingua latina"},lb:{name:"Luxembourgish, Letzeburgesch",nativeName:"Lëtzebuergesch"},lg:{name:"Luganda",nativeName:"Luganda"},li:{name:"Limburgish, Limburgan, Limburger",nativeName:"Limburgs"},ln:{name:"Lingala",nativeName:"Lingála"},lo:{name:"Lao",nativeName:"ພາສາລາວ"},lt:{name:"Lithuanian",nativeName:"lietuvių kalba"},lu:{name:"Luba-Katanga",nativeName:""},lv:{name:"Latvian",nativeName:"latviešu valoda"},gv:{name:"Manx",nativeName:"Gaelg, Gailck"},mk:{name:"Macedonian",nativeName:"македонски јазик"},mg:{name:"Malagasy",nativeName:"Malagasy fiteny"},ms:{name:"Malay",nativeName:"bahasa Melayu, بهاس ملايو‎"},ml:{name:"Malayalam",nativeName:"മലയാളം"},mt:{name:"Maltese",nativeName:"Malti"},mi:{name:"Māori",nativeName:"te reo Māori"},mr:{name:"Marathi (Marāṭhī)",nativeName:"मराठी"},mh:{name:"Marshallese",nativeName:"Kajin M̧ajeļ"},mn:{name:"Mongolian",nativeName:"монгол"},na:{name:"Nauru",nativeName:"Ekakairũ Naoero"},nv:{name:"Navajo, Navaho",nativeName:"Diné bizaad, Dinékʼehǰí"},nb:{name:"Norwegian Bokmål",nativeName:"Norsk bokmål"},nd:{name:"North Ndebele",nativeName:"isiNdebele"},ne:{name:"Nepali",nativeName:"नेपाली"},ng:{name:"Ndonga",nativeName:"Owambo"},nn:{name:"Norwegian Nynorsk",nativeName:"Norsk nynorsk"},no:{name:"Norwegian",nativeName:"Norsk"},ii:{name:"Nuosu",nativeName:"ꆈꌠ꒿ Nuosuhxop"},nr:{name:"South Ndebele",nativeName:"isiNdebele"},oc:{name:"Occitan",nativeName:"Occitan"},oj:{name:"Ojibwe, Ojibwa",nativeName:"ᐊᓂᔑᓈᐯᒧᐎᓐ"},cu:{name:"Old Church Slavonic, Church Slavic, Church Slavonic, Old Bulgarian, Old Slavonic",nativeName:"ѩзыкъ словѣньскъ"},om:{name:"Oromo",nativeName:"Afaan Oromoo"},or:{name:"Oriya",nativeName:"ଓଡ଼ିଆ"},os:{name:"Ossetian, Ossetic",nativeName:"ирон æвзаг"},pa:{name:"Panjabi, Punjabi",nativeName:"ਪੰਜਾਬੀ, پنجابی‎"},pi:{name:"Pāli",nativeName:"पाऴि"},fa:{name:"Persian",nativeName:"فارسی"},pl:{name:"Polish",nativeName:"polski"},ps:{name:"Pashto, Pushto",nativeName:"پښتو"},pt:{name:"Portuguese",nativeName:"Português"},qu:{name:"Quechua",nativeName:"Runa Simi, Kichwa"},rm:{name:"Romansh",nativeName:"rumantsch grischun"},rn:{name:"Kirundi",nativeName:"kiRundi"},ro:{name:"Romanian, Moldavian, Moldovan",nativeName:"română"},ru:{name:"Russian",nativeName:"русский язык"},sa:{name:"Sanskrit (Saṁskṛta)",nativeName:"संस्कृतम्"},sc:{name:"Sardinian",nativeName:"sardu"},sd:{name:"Sindhi",nativeName:"सिन्धी, سنڌي، سندھی‎"},se:{name:"Northern Sami",nativeName:"Davvisámegiella"},sm:{name:"Samoan",nativeName:"gagana faa Samoa"},sg:{name:"Sango",nativeName:"yângâ tî sängö"},sr:{name:"Serbian",nativeName:"српски језик"},gd:{name:"Scottish Gaelic; Gaelic",nativeName:"Gàidhlig"},sn:{name:"Shona",nativeName:"chiShona"},si:{name:"Sinhala, Sinhalese",nativeName:"සිංහල"},sk:{name:"Slovak",nativeName:"slovenčina"},sl:{name:"Slovene",nativeName:"slovenščina"},so:{name:"Somali",nativeName:"Soomaaliga, af Soomaali"},st:{name:"Southern Sotho",nativeName:"Sesotho"},es:{name:"Spanish; Castilian",nativeName:"español, castellano"},su:{name:"Sundanese",nativeName:"Basa Sunda"},sw:{name:"Swahili",nativeName:"Kiswahili"},ss:{name:"Swati",nativeName:"SiSwati"},sv:{name:"Swedish",nativeName:"svenska"},ta:{name:"Tamil",nativeName:"தமிழ்"},te:{name:"Telugu",nativeName:"తెలుగు"},tg:{name:"Tajik",nativeName:"тоҷикӣ, toğikī, تاجیکی‎"},th:{name:"Thai",nativeName:"ไทย"},ti:{name:"Tigrinya",nativeName:"ትግርኛ"},bo:{name:"Tibetan Standard, Tibetan, Central",nativeName:"བོད་ཡིག"},tk:{name:"Turkmen",nativeName:"Türkmen, Түркмен"},tl:{name:"Tagalog",nativeName:"Wikang Tagalog, ᜏᜒᜃᜅ᜔ ᜆᜄᜎᜓᜄ᜔"},tn:{name:"Tswana",nativeName:"Setswana"},to:{name:"Tonga (Tonga Islands)",nativeName:"faka Tonga"},tr:{name:"Turkish",nativeName:"Türkçe"},ts:{name:"Tsonga",nativeName:"Xitsonga"},tt:{name:"Tatar",nativeName:"татарча, tatarça, تاتارچا‎"},tw:{name:"Twi",nativeName:"Twi"},ty:{name:"Tahitian",nativeName:"Reo Tahiti"},ug:{name:"Uighur, Uyghur",nativeName:"Uyƣurqə, ئۇيغۇرچە‎"},uk:{name:"Ukrainian",nativeName:"українська"},ur:{name:"Urdu",nativeName:"اردو"},uz:{name:"Uzbek",nativeName:"zbek, Ўзбек, أۇزبېك‎"},ve:{name:"Venda",nativeName:"Tshivenḓa"},vi:{name:"Vietnamese",nativeName:"Tiếng Việt"},vo:{name:"Volapük",nativeName:"Volapük"},wa:{name:"Walloon",nativeName:"Walon"},cy:{name:"Welsh",nativeName:"Cymraeg"},wo:{name:"Wolof",nativeName:"Wollof"},fy:{name:"Western Frisian",nativeName:"Frysk"},xh:{name:"Xhosa",nativeName:"isiXhosa"},yi:{name:"Yiddish",nativeName:"ייִדיש"},yo:{name:"Yoruba",nativeName:"Yorùbá"},za:{name:"Zhuang, Chuang",nativeName:"Saɯ cueŋƅ, Saw cuengh"}}},970:(e,a,t)=>{"use strict";t(81);var n=t(614),s=t.n(n);$(".js-typed").length&&$(".js-typed").each((function(e,a){var t=$(a),n=t.data("strings"),i=t.data("start-delay");new(s())(a,{typeSpeed:70,showCursor:!0,cursorChar:"|",strings:n,loop:!0,backSpeed:10,smartBackspace:!0,startDelay:i,preStringTyped:function(){t.addClass("is-active"),1==e&&($(".fadein p:gt(0)").hide(),$(".fadein > :first-child").fadeOut().next("p").fadeIn().end().appendTo(".fadein"))},onComplete:function(e){e.startDelay=0}});t.parent().find(".typed-cursor").addClass("typed-cursor--blink")}));t(771);var i=t(247),o=t(273),l=t.n(o),r={useEasing:!0,useGrouping:!0,separator:",",decimal:"."},c=$(".js-stat-number"),d=$(".js-stat-number-decimal");(0,i.Z)().reveal(".js-stats",{beforeReveal:function(){c.each((function(e){var a=$(c[e]).html();new(l())(c[e],0,a,0,5,r).start()})),d.each((function(e){var a=$(d[e]).html();new(l())(d[e],0,a,1,5,r).start()}))}});var u,p=t(838);new p.Z(".js-slider-partners",{slidesPerView:2,loop:!0,autoplayDisableOnInteraction:!1,navigation:{nextEl:".js-slider-partners-next",prevEl:".js-slider-partners-prev"},breakpoints:{1024:{slidesPerView:9,loop:!1},768:{slidesPerView:3}}});function m(){var e=$(window).outerWidth();e<1399&&null==u?u=new p.Z(".js-slider-companies",{slidesPerView:2,loop:!0,autoplayDisableOnInteraction:!1,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},breakpoints:{1399:{slidesPerView:9,loop:!1},1024:{slidesPerView:5,loop:!0},768:{slidesPerView:3}}}):e>1399&&null!=u&&(u.destroy(),u=void 0,$(".js-slider-logos").find(".swiper-wrapper").removeAttr("style"),$(".js-slider-logos").find(".swiper-slide").removeAttr("style"))}m(),$(window).on("resize load",(function(){m()}));var h;new p.Z(".js-slider-products",{slidesPerView:1,loop:!1,autoplayDisableOnInteraction:!1,navigation:{nextEl:".js-slider-products-next",prevEl:".js-slider-products-prev"},breakpoints:{1200:{slidesPerView:6,loop:!1},1024:{slidesPerView:4,loop:!1},768:{slidesPerView:3},420:{slidesPerView:2}}}),new p.Z(".js-slider-relative-products",{slidesPerView:1,loop:!0,autoplayDisableOnInteraction:!1,breakpoints:{1200:{slidesPerView:6,loop:!1},1024:{slidesPerView:4,loop:!1},768:{slidesPerView:3},420:{slidesPerView:2}}});function v(){var e=$(window).outerWidth();e<1399&&null==h?h=new p.Z(".js-slider-logos",{slidesPerView:2,loop:!0,autoplayDisableOnInteraction:!1,navigation:{nextEl:".js-slider-logos-next",prevEl:".js-slider-logos-prev"},breakpoints:{1399:{slidesPerView:9,loop:!1},1024:{slidesPerView:5,loop:!0},768:{slidesPerView:3}}}):e>1399&&null!=h&&(h.destroy(),h=void 0,$(".js-slider-logos").find(".swiper-wrapper").removeAttr("style"),$(".js-slider-logos").find(".swiper-slide").removeAttr("style"))}v(),$(window).on("resize load",(function(){v()}));t(27),t(580),t(316),t(113),t(156),t(729);$(".js-open-popup").magnificPopup({type:"inline",midClick:!0}),$(".cc-popup__close").on("click",(function(){$.magnificPopup.close()}));t(538),t(513),t(241),t(686);$(".js-select-income").select2({placeholder:"Прогнозен оборот",minimumResultsForSearch:-1}),$(".js-select-country").select2({placeholder:"Държава",minimumResultsForSearch:-1}),$(".js-select-template").select2({minimumResultsForSearch:-1});t(603),t(25),t(619),t(198);var f=t(396),g=$(".cc-main a[href]"),b=Object.keys(f.K),y=window.app_url||"https://cloudcart.com",w=y.replace("https:",""),C=$("html").attr("lang");"en"===C&&(C=""),g.map((function(){var e=$(this),a=e.attr("href");if(a.startsWith("/")&&!a.startsWith("//")){var t=a.split("/")[1];b.includes(t)&&2===t.length?e.attr("href",y+a.replace(t,C)):e.attr("href",y+"/"+C+a)}else if(a.startsWith(y)){var n=a.split("/")[3];b.includes(n)&&2===n.length?e.attr("href",a.replace(n,C)):e.attr("href",y+"/"+C+a.replace(y,""))}else if(a.startsWith(w)){var s=a.split("/")[3];b.includes(s)&&2===s.length?e.attr("href",a.replace(s,C)):e.attr("href",y+"/"+C+a.replace(w,""))}e.attr("href",e.attr("href").replace(/\/\//g,"/")),e.attr("href",e.attr("href").replace(":/","://"))}));t(278),t(369);var k=t(808),_=t.n(k),j=$(".js-section-email"),N=$(".js-ajax-form");j.on("submit",(function(e){e.preventDefault();var a=$(this);a.find('[type="submit"]').trigger("loading.start"),""!==a.find("input").val()?(_().set("hasMail",a.find("input").val()),location.href="/"+$("html").attr("lang")+"/register"):a.find('[type="submit"]').trigger("loading.end")})),window.addEventListener("load",(function(){_().get("hasMail")&&N.find("#formEmail").val(_().get("hasMail"))}))},316:()=>{!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".js-accordion-categories");e.find(".cc-accordion__section").not(".is-current").find(".cc-accordion__body").hide(),e.on("click",".cc-accordion__head",(function(e){var a=$(this).closest(".cc-accordion__section");a.find(".cc-accordion__body").stop().slideToggle(),a.toggleClass("is-current"),a.siblings().removeClass("is-current").find(".cc-accordion__body").slideUp(),$(window).width()<1024&&a.parent().siblings().find(".cc-accordion__section").removeClass("is-current").find(".cc-accordion__body").slideUp()}))}()},580:()=>{!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:$(".js-accordion-footer");$(".cc-accordion__section").not(".is-current").find(".cc-accordion__body").hide(),e.on("click",".cc-accordion__head",(function(e){var a=$(this).closest(".cc-accordion__section");a.find(".cc-accordion__body").stop().slideToggle(),a.toggleClass("is-current"),a.siblings().removeClass("is-current").find(".cc-accordion__body").slideUp()}))}()},771:()=>{$.fn.isOnScreen=function(){var e=$(window),a={top:e.scrollTop(),left:e.scrollLeft()};a.right=a.left+e.width(),a.bottom=a.top+e.height();var t=this.offset();return t.right=t.left+this.outerWidth(),t.bottom=t.top+this.outerHeight(),!(a.right<t.left||a.left>t.right||a.bottom<t.top||a.top>t.bottom)},$(".js-image").each((function(){$(window).scrollTop();var e=$(this);$(window).on("scroll",(function(){$(window).scrollTop();1==e.isOnScreen()&&e.addClass("animated")})),$(window).on("load",(function(){var a=$(window).scrollTop();1==e.isOnScreen()&&e.addClass("animated"),a}))}))},27:()=>{var e=$(".js-burger-button"),a=$(".js-mobile-menu"),t=$("body"),n=$(".js-header"),s=$(".js-header-bar"),i=$(".js-mobile-nav");e.on("click",(function(o){o.preventDefault(),e.toggleClass("active"),a.toggleClass("open"),t.toggleClass("menu-open"),n.toggleClass("menu-open"),$(".cc-nav__dropdown").hide(),$(".cc-nav__dropdown-inner").hide(),s.removeClass("inner-open-sub"),s.removeClass("inner-open"),i.find("li").removeClass("dropdown-is-visible")})),$(window).on("resize",(function(){$(window).outerWidth()>1023&&(n.removeClass("menu-open"),t.removeClass("menu-open"),e.removeClass("active"))}))},198:()=>{var e=$('.cc-hero__actions a[href*="/demo"], .cc-hero__actions a[href*="/consultation"],.cc-section__actions a[href*="/demo"], .cc-section__actions a[href*="/consultation"],.cc-hero__link[href*="/demo"], .cc-hero__link[href*="/consultation"],.cc-section__link[href*="/demo"], .cc-section__link[href*="/consultation"],.cc-card__link[href*="/demo"], .cc-card__link[href*="/consultation"]'),a=$('.cc-hero__actions a[href*="/consultation"],\n                           .cc-section__actions a[href*="/consultation"], \n                           .cc-hero__link[href*="/consultation"], \n                           .cc-section__link[href*="/consultation"],\n                           .cc-card__link[href*="/consultation"]'),t=($('.cc-hero__actions a[href*="/join-ecosystem"], .cc-section__actions a[href*="/join-ecosystem"], .cc-section__link[href*="/join-ecosystem"], .cc-card__link[href*="/join-ecosystem"],.cc-hero__actions a[href*="/ecommerce-for-anyone/partners"], .cc-section__actions a[href*="/ecommerce-for-anyone/partners"], .cc-card__link[href*="/ecommerce-for-anyone/partners"]'),$('.cc-hero__actions a[href*="/connected-ecommerce/services"], .cc-section__actions a[href*="/connected-ecommerce/services"], .cc-section__link[href*="/connected-ecommerce/services"], .cc-card__link[href*="/connected-ecommerce/services"]'));e.each((function(){var e=$(this),a=e.attr("href").replace("demo","register");e.hasClass("cc-link-arrow")||("bg"==$("html").attr("lang")?e.text("ЗАПОЧНИ СЕГА"):"el"==$("html").attr("lang")?e.text("ΞΕΚΙΝΑ ΤΩΡΑ"):"mk"==$("html").attr("lang")?e.text("ЗАПОЧНИ СЕГА"):"ro"==$("html").attr("lang")?e.text("Începe acum"):"sq"==$("html").attr("lang")?e.text("Fillo tani"):"sr"==$("html").attr("lang")?e.text("Počnite odmah"):e.text("Start now")),e.attr("href",a)})),a.each((function(){var e=$(this),a=e.attr("href").replace("consultation","register");e.hasClass("cc-link-arrow")&&("bg"==$("html").attr("lang")?e.html('<span>Прехвърли се сега</span> <i class="fal fa-long-arrow-right"></i>'):"el"==$("html").attr("lang")?e.html('<span>Μετεγκατάσταση στη CloudCart</span> <i class="fal fa-long-arrow-right"></i>'):"mk"==$("html").attr("lang")?e.html('<span>Мигрирајте во CloudCart</span> <i class="fal fa-long-arrow-right"></i>'):"ro"==$("html").attr("lang")?e.html('<span>Migrați la CloudCart</span> <i class="fal fa-long-arrow-right"></i>'):e.html('<span>Migrate to CloudCart</span> <i class="fal fa-long-arrow-right"></i>')),e.attr("href",a)})),t.on("click",(function(){var e="https://calendly.com/d/ckb-dws-qt9?hide_gdpr_banner=1&text_color=333333&primary_color=ff4b51";return e="bg"==$("html").attr("lang")?"https://calendly.com/d/ckr-km6-7w8/cloudcart?hide_gdpr_banner=1&text_color=333333&primary_color=ff4b51":"el"==$("html").attr("lang")?"https://calendly.com/d/cks-p9h-4w3/cloudcart-clone?hide_gdpr_banner=1&text_color=333333&primary_color=ff4b51":"mk"==$("html").attr("lang")?"https://calendly.com/d/cky-fzc-7x5/cloudcart-clone?month=2022-01&hide_gdpr_banner=1&text_color=333333&primary_color=ff4b51":"https://calendly.com/d/ckb-dws-qt9?hide_gdpr_banner=1&text_color=333333&primary_color=ff4b51",Calendly.initPopupWidget({url:e}),!1}))},513:()=>{var e=$(".js-form-demo"),a=$(".js-form-submit"),t=!1,n=!1,s=!1,i=!1,o=!1,l=!1,r=!1,c=!1,d=/^[A-Z0-9._%+-]+@([A-Z0-9-]+\.)+[A-Z]{2,4}$/i,u=/^[+-]?\d+(\.\d+)?([eE][+-]?\d+)?$/,p=/(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/,m=$(".js-first-name"),h=$(".js-last-name"),v=$(".js-email"),f=$(".js-phone"),g=$(".js-website"),b=$(".js-company"),y=$(".js-select-income"),w=$(".js-select-country");e.each((function(){$(this).find(".cc-field").each((function(){var e=$(this);e.on("focusin",(function(){e.parent().parent().addClass("focused")})),e.on("focusout",(function(){e.parent().parent().removeClass("focused"),this.value.length<1&&e.parent().parent().removeClass("focused").removeClass("invalid").addClass("invalid-empty")})),e.keyup((function(){this.value.length>0&&e.parent().parent().removeClass("invalid-empty")}))})),m.keyup((function(){this.value.length>1?($(this).parent().parent().addClass("valid").removeClass("invalid"),t=!0):($(this).parent().parent().removeClass("valid").addClass("invalid"),t=!1)})),h.keyup((function(){this.value.length>1?($(this).parent().parent().addClass("valid").removeClass("invalid"),n=!0):($(this).parent().parent().removeClass("valid").addClass("invalid"),n=!1)})),v.keyup((function(){d.test(this.value)?($(this).parent().parent().addClass("valid").removeClass("invalid"),s=!0):($(this).parent().parent().removeClass("valid").addClass("invalid"),s=!1)})),f.keyup((function(){u.test(this.value)&&this.value.length>2?($(this).parent().parent().addClass("valid").removeClass("invalid"),i=!0):($(this).parent().parent().removeClass("valid").addClass("invalid"),i=!1)})),g.keyup((function(){p.test(this.value)?($(this).parent().parent().addClass("valid").removeClass("invalid"),o=!0):($(this).parent().parent().removeClass("valid").addClass("invalid"),o=!1)})),b.keyup((function(){this.value.length>1?($(this).parent().parent().addClass("valid").removeClass("invalid"),l=!0):($(this).parent().parent().removeClass("valid").addClass("invalid"),l=!1)})),y.on("change",(function(){r=!0,$(this).parents(".cc-form__row").first().addClass("valid").removeClass("invalid")})),w.on("change",(function(){c=!0,$(this).parents(".cc-form__row").first().addClass("valid").removeClass("invalid")})),e.find("select").on("change",(function(){$(this).parents(".cc-select").removeClass("open"),t&&n&&s&&i&&o&&l&&r&&c?a.removeClass("cc-btn--disabled"):a.addClass("cc-btn--disabled")})),e.find("input").keyup((function(){$(this).parents(".cc-select").removeClass("open"),t&&n&&s&&i&&o&&l&&r&&c?a.removeClass("cc-btn--disabled"):a.addClass("cc-btn--disabled")}))})),$(document).ready((function(){$(".select2-container").each((function(){$(this).on("click",(function(){$(this).parents(".cc-select").toggleClass("open")})),$(this).on("focusout",(function(){$(this).parents(".cc-select").removeClass("open")}))}))}))},156:()=>{$(".js-nav-lang").each((function(){var e=$(this);e.find(".is-current > a").on("click",(function(a){a.preventDefault(),e.toggleClass("open")}))}))},113:()=>{var e=$(".js-mobile-nav"),a=$(".js-mobile-nav-inner"),t=$(".js-header-bar"),n=$(".js-header-back");e.find("li:not(.cc-megamenu) > a").each((function(){var e=$(this);e.on("click",(function(a){e.siblings("ul").length>0&&a.preventDefault()}))})),e.find("nav > ul > li").each((function(){var e=$(this);e.has(".cc-nav__dropdown").length,e.on("click",(function(){e.has(".cc-nav__dropdown").length?(e.addClass("dropdown-is-visible"),t.addClass("inner-open"),t.parent().addClass("inner-open")):(e.removeClass("dropdown-is-visible"),t.removeClass("inner-open"),t.parent().removeClass("inner-open"))}))})),a.find("> li").each((function(){var e=$(this);e.has("ul").length,e.on("click",(function(){e.has(".cc-nav__dropdown-inner").length?(t.addClass("inner-open-sub"),e.addClass("dropdown-is-visible")):(t.removeClass("inner-open-sub"),e.removeClass("dropdown-is-visible"))}))})),n.on("click",(function(e){e.preventDefault(),t.hasClass("inner-open-sub")?(t.removeClass("inner-open-sub"),$(".cc-nav__dropdown-inner").parent().removeClass("dropdown-is-visible")):(t.removeClass("inner-open"),t.parent().removeClass("inner-open"),$(".cc-nav__dropdown").parent().removeClass("dropdown-is-visible"))}))},538:()=>{$(".js-popup-select").each((function(){var e=$(this);e.find("> a").on("click",(function(a){e.siblings(".js-popup-select").find(".cc-popup__list").removeClass("open"),e.find(".cc-popup__list").hasClass("open")?e.find(".cc-popup__list").removeClass("open"):e.find(".cc-popup__list").addClass("open")})),e.find(".cc-popup__list a").on("click",(function(a){var t=$(this);e.find("> a").html(t.text()),$(".cc-popup__list").removeClass("open")}))}))},81:()=>{var e=$(".js-search");$(window).click((function(){e.parents(".cc-search").removeClass("expanded"),$(this).find(".cc-search__field").focusout()})),e.on("click",(function(a){e.parents(".cc-search").hasClass("expanded")||a.preventDefault()})),$(".cc-search").click((function(a){a.stopPropagation(),e.parents(".cc-search").addClass("expanded"),$(this).find(".cc-search__field").focus()}))},241:()=>{var e,a,t,n,s,i,o,l=function(){$(".js-table-price").each((function(){var e=$(this),a=$("[data-mapping]"),t=$(".is-current");a.each((function(n){e.data("price-mapping")==t.find($(this)).data("mapping")&&e.text($(a[n]).find(".cc-price__from").text()+" € "+$(a[n]).find(".js-price-value").text())}))}))};e="is-current",$(".cc-tabs__nav a").on("click",(function(a){a.preventDefault();var t=$(this),n=$("#"+t.attr("href").split("#")[1]);t.parent().add(n).addClass(e).siblings().removeClass(e),l()})),l(),n=$('.cc-cards__item[data-months="12"]'),s=$('.cc-cards__item[data-months="24"]'),i=[],o=[],n.each((function(){i.push($(this).find(".js-discount-percent").text().replace("%",""))})),s.each((function(){o.push($(this).find(".js-discount-percent").text().replace("%",""))})),a=i.reduce((function(e,a){return Math.max(e,a)}),0),t=o.reduce((function(e,a){return Math.max(e,a)}),0),$(".js-discount-percent-one-year").text("-"+a+"%"),$(".js-discount-percent-two-years").text("-"+t+"%")},619:()=>{var e=$(".js-header");$(window).ready((function(){$(".cc-main--dark").length>0&&e.addClass("cc-header--alt")}))},369:()=>{$(".js-nav-dropdown-trigger").on("click",(function(){var e=$(this);e.closest(".cc-nav-dropdown-trigger").find(".js-nav-dropdown").slideToggle((function(){e.closest(".cc-nav-dropdown-trigger__item").toggleClass("active"),e.closest(".cc-nav-dropdown-trigger__item").hasClass("active")?e.find("i").removeClass("fa-plus").addClass("fa-minus"):e.find("i").removeClass("fa-minus").addClass("fa-plus")})),e.closest(".cc-nav-dropdown-trigger").siblings().find(".js-nav-dropdown").slideUp((function(){e.closest(".cc-nav-dropdown-trigger").siblings().find(".cc-nav-dropdown-trigger__item").removeClass("active"),e.closest(".cc-nav-dropdown-trigger").siblings().find(".js-nav-dropdown-trigger i").removeClass("fa-minus").addClass("fa-plus")}))}))},278:()=>{var e=$(".js-hover-item-trigger"),a=$(".js-hover-item");e.on("mouseenter",(function(){var e=$(this);a.map((function(){e.data("hover")==$(this).data("hover-item")&&($(this).siblings().removeClass("active"),$(this).addClass("active"),e.addClass("active"),e.siblings().removeClass("active"))}))}))},603:()=>{$(".js-toggle-table").on("click",(function(e){e.preventDefault();var a=$(this);a.toggleClass("closed"),a.parent().siblings("table, .cc-links__list").toggle()}))},25:()=>{var e=$(".js-header"),a=e.outerHeight(),t=$(".cc-table-prices");$(window).scroll((function(){$(window).scrollTop()>=a+60?e.addClass("fixed"):e.removeClass("fixed")})),$(window).scroll((function(){t.length&&$(window).width()>1023&&location.pathname.includes("/pricing")&&t.isInViewport()&&(t.closest(".wrapper").css("overflow","visible"),t.find(".js-card-actions").css("display","table-row"),e.length&&t.find("thead").css("top",e[0].clientHeight-2))})),$.fn.isInViewport=function(){var e=$(this).offset().top,a=e+$(this).outerHeight(),t=$(window).scrollTop(),n=t+$(window).height();return a>t&&e<n}},305:(e,a,t)=>{"use strict";t.d(a,{Z:()=>i});var n=t(519),s=t.n(n)()((function(e){return e[1]}));s.push([e.id,".question-step{transition:opacity .7s,visibility .7s,position .7s}.question-step.inactive,.question-step.inactive.open{opacity:0;position:absolute;visibility:hidden}.question-step.active{position:absolute}.question-step.active.open{position:static}.cc-popup__entry{transition:.5s}.cc-form .cc-form__row.cc-form__row--questions{display:inline}.cc-form .cc-form__row.cc-form__row--questions .cc-field{max-width:300px;width:auto}.select2-container--default .select2-results>.select2-results__options{max-height:32rem}.questions-wrap .select2-dropdown{min-width:33rem}.question-step.industry{margin-top:20px}",""]);const i=s}},e=>{var a=a=>e(e.s=a);e.O(0,[898],(()=>(a(516),a(970))));e.O()}]);