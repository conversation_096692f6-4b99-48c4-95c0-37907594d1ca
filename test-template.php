<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

// Set up the application
$app->boot();

// Get the template loader
$loader = $app->make(\App\LiquidEngine\Services\TemplateLoader::class);

// Test data
$data = [
    'title' => 'Test Page',
    'meta_description' => 'Testing template loading',
    'page_title' => 'Test Page',
    'content' => 'This is a test of the template loading system.'
];

// Templates to test
$templates = [
    'page',
    'templates.page',
    'templates/page',
    'default',
    'templates.default',
    'templates/default'
];

// Test each template
foreach ($templates as $template) {
    echo "Testing template: {$template}\n";
    
    try {
        $result = $loader->load($template, $data);
        
        if ($result) {
            echo "SUCCESS: Template loaded successfully\n";
        } else {
            echo "FAILURE: Template not found\n";
        }
    } catch (Exception $e) {
        echo "ERROR: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// Terminate the application
$kernel->terminate($request, $response); 