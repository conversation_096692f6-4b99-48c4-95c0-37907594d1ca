<template>
    <div class="panel-box panel-box-result text-center mb-4" v-if="hasError">
        <i class="fal fa-robot"></i>
        <p class="mt-3" v-if="errorMessage">{{ errorMessage }}</p>
        <p class="mt-3" v-else>{{  translations['cloudio::app.sidepanel.error.shopper_pen'] }}</p>
        <a href="javascript:;" @click.prevent="retry" class="mt-3 btn btn-white">
            {{ translations['cloudio::app.sidepanel.button.retry'] }}
        </a>
    </div>
</template>

<script>
import axios from "axios"

export default {
    name: "Error",
    props: {
        hasError: {},
        translations: {},
        errorMessage: {
            type: String,
        },
        productId: {},
        type: {},
        log_id: {}
    },
    methods: {
        retry() {
            axios.post('/admin/api/cloudio/restart/', {product_id: this.productId, type: 'product_'+this.type})
                .then((res) => {
                    this.$emit('update:hasError', false)
                    this.$emit('update:logId', res.data.log_id)
                })
                .catch((err) => {
                    console.log(err)
                    this.$emit('update:hasError', false)
                })
        }
    },
    emits: ["update:hasError", "update:logId"]
}
</script>

<style lang="scss">
.panel-box {
    .fa-robot {
        color: #D6D9E9;
        text-align: center;
        font-size: 64px;
        font-weight: 300;
        line-height: 71px;
    }
}
</style>