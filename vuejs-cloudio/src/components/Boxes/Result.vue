<template>
    <transition name="expand">
        <div class="description-result-box" :class="{'collapsed c-pointer': isCollapsed }" @click="setCollapsed" v-if="!isCollapsed">
            <div class="description-result-head">
                <div class="result-date">
                    {{ result.date }}
                </div>
                <div class="used-tokens">
                    Tokens: <strong>{{ result.used_tokens }}</strong>
                </div>
            </div>

            <div class="info-box info-box-warning" v-if="this.index === 0">
                <span v-html="translations['cloudio::app.sidepanel.help.result']"></span>
            </div>

            <div class="description-result-content mt-3"
                 v-if="typeof result.response.text === 'string'"
                 v-html="result.response.text" :class="{collapsed: isCollapsed}"
            ></div>
            <div class="seo-google-box mt-3" v-else :class="{collapsed: isCollapsed}" style="display: block;">
                <p class="seo-google-title">{{ result.response.text.title }}</p>
                <p class="seo-google-url js-seo-google-url">{{ locationOrigin }}/product/{{ locationValue }}</p>
                <p class="seo-google-description js-seo-google-description">{{ result.response.text.description }}</p>
            </div>

            <ShopperSense
                v-if="panelType === 'description'"
                :shopperSense="shopperSense"
                :result="result"
                :analyze-loading="analyzeLoading"
                :index="index"
                @update:result="val => result = val"
                @update:shopperSense="val => shopperSense = val"
                @analyze="analyze(result)"
                @activateShopperSense="activateShopperSense"
                :translations="translations"
            />

            <div class="row results-controls mt-4">
                <div class="col-xs-6">
                    <div class="evaluation">
                        <i class="far fa-thumbs-up" @click.stop="setEvaluation(result.id, 2)"
                           :class="{ checked: result.evaluation === 2 }"></i>
                        <i class="far fa-thumbs-down" @click.stop="setEvaluation(result.id, 1)"
                           :class="{ checked: result.evaluation === 1 }"></i>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="save-text text-right">
                        <a href="javascript:;"
                           @click.stop="typeof result.response.text === 'string' ? copyText(result.response.text) : copyText(result.response.text.description)"
                           ref="copyTrigger"
                           class="btn-cancel-task me-3"
                        >
                            <i class="far fa-copy me-2"></i>
                            {{ translations['cloudio::app.sidepanel.button.copy_text'] }}
                        </a>
                        <a href="javascript:;" class="btn btn-primary" v-if="result.response.type === 'product_description'"
                           @click.stop.prevent="keepDescription(result.id, result.response.text)">
                            <i class="far fa-check"></i>{{ translations['cloudio::app.sidepanel.button.keep'] }}
                        </a>
                        <a href="javascript:;" class="btn btn-primary" v-else-if="result.response.type === 'product_short_description'"
                           @click.stop.prevent="keepShortDescription(result.id, result.response.text)">
                            <i class="far fa-check"></i>{{ translations['cloudio::app.sidepanel.button.keep'] }}
                        </a>
                        <a href="javascript:;" class="btn btn-primary" v-else-if="result.response.type === 'product_meta_description'"
                           @click.stop.prevent="keepMetaDescription(result.id, result.response.text.title, result.response.text.description)">
                            <i class="far fa-check"></i>{{ translations['cloudio::app.sidepanel.button.keep'] }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="description-result-box c-pointer" v-else @click="setCollapsed">
            <div class="description-result-head">
                <div class="result-date">
                    {{ result.date }}
                </div>
                <div class="used-tokens">
                    Tokens: <strong>{{ result.used_tokens }}</strong>
                </div>
            </div>

            <div class="info-box info-box-warning" v-if="this.index === 0">
                <span v-html="translations['cloudio::app.sidepanel.help.result']"></span>
            </div>

            <div class="description-result-content mt-3"
                 v-if="typeof result.response.text === 'string'"
                 v-html="result.response.text" :class="{collapsed: isCollapsed}"
            ></div>
            <div class="seo-google-box mt-3" v-else :class="{collapsed: isCollapsed}" style="display: block;">
                <p class="seo-google-title">{{ result.response.text.title }}</p>
                <p class="seo-google-url js-seo-google-url">{{ locationOrigin }}/product/{{ locationValue }}</p>
                <p class="seo-google-description js-seo-google-description">{{ result.response.text.description }}</p>
            </div>
        </div>
    </transition>
</template>

<script>
import InfoComponent from '../Form/InfoComponent'
import axios from 'axios'
import CloudioLoader from '../Helpers/CloudioLoader'
import ShopperSense from "./ShopperSense";

export default {
    name: "Result",
    components: {
        ShopperSense,
        InfoComponent,
        CloudioLoader
    },
    data() {
        return {
            locationOrigin: window.location.origin,
            locationValue: '',
        }
    },
    props: {
        result: {
            type: Object,
        },
        index: {
            required: false
        },
        translations: {},
        analyzeLoading: {
            required: false,
            default: false
        },
        shopperSense: {},
        panelType: {},
        isCollapsed: {}
    },
    mounted() {
        if (this.index === 0) {
            this.$emit('update:isCollapsed', false)
        }
        this.locationValue = document.querySelector('#product_url_handle').value
    },
    methods: {
        setCollapsed(value) {
            let isCollapseInternal = this.isCollapsed
            this.index !== 0 ? this.$emit('update:isCollapsed', isCollapseInternal = !isCollapseInternal) : this.$emit('update:isCollapsed', false)
        },
        analyze(result) {
            if (result) {
                let resultUpdated = result
                resultUpdated.analyze_text = null
                resultUpdated.analyze_is_error = false
                resultUpdated.analyze_progress = "starting"
                this.$emit('update:result', resultUpdated)
            }

            this.$emit('update:analyzeLoading', true)
            axios.get(`/admin/api/cloudio/shopper-sense/analyze/${this.result.id}`)
                .then(res => {
                    if (res.data.status === "success") {
                        // CCHelper.toastrSuccess(res?.data?.msg);
                        this.$emit('update:history', res.data.log_id)
                    }
                })
                .catch(err => {
                    CCHelper.toastrError(res?.data?.msg);
                })
        },
        activateShopperSense() {
            let body = {
                key: "shopper_sense",
                status: 1
            }
            axios
                .post('/admin/api/cloudio/skills/status', body)
                .then(res => {
                    CCHelper.toastrSuccess(res?.data?.msg);
                    this.$emit('update:shopperSense', true);
                })
                .catch(err => {
                    CCHelper.toastrError(err?.data?.msg);
                })
        },
        keepDescription(id, text) {
            let descriptionSelector = document.querySelector('#product-description .dynamic_description')
            descriptionSelector.innerHTML = '<textarea data-exists="false" data-seo-description class="tinymce" id="tinymce-' + id + '" data-height="284" name="description" placeholder="{t}product.ph.description{/t}">' + text + '</textarea>'
            tinymceInit(descriptionSelector);
            this.$emit('update:isOpened', false)
        },
        keepShortDescription(id, text) {
            let descriptionSelector = document.querySelector('#product-description .dynamic_short_description')
            descriptionSelector.innerHTML = '<textarea data-exists="false" data-seo-description class="tinymce" id="tinymce-' + id + '" data-height="101" name="short_description" placeholder="{t}product.ph.short_description{/t}">' + text + '</textarea>'
            tinymceInit(descriptionSelector);
            this.$emit('update:isOpened', false)
        },
        keepMetaDescription(id, title , text) {
            let titleSelector = document.querySelector('.js-field-seo-google-title')
            let textSelector = document.querySelector('.js-field-seo-google-description')
            let titleVisualizeSelector = document.querySelector('.js-seo-google-holder .js-seo-google-title')
            let textVisualizeSelector = document.querySelector('.js-seo-google-holder .js-seo-google-description')

            titleSelector.value = title
            titleVisualizeSelector.innerHTML = title
            textSelector.value = text
            textVisualizeSelector.innerHTML = text
            this.$emit('update:isOpened', false)
        },
        setEvaluation(logId, rating) {
            axios
                .get(`/admin/api/cloudio/rating/${logId}/${rating}`)
                .then(res => {
                    CCHelper.toastrSuccess(res?.data?.msg);
                    this.result.evaluation = rating
                    this.$emit('update:result', this.result)
                })
                .catch(err => {
                    CCHelper.toastrSuccess(err?.data?.msg);
                })
        },
        copyText(str) {
            const el = document.createElement('textarea')
            el.addEventListener('focusin', e => e.stopPropagation())
            el.value = str
            this.$refs.copyTrigger.appendChild(el)
            el.select()
            document.execCommand('copy')
            this.$refs.copyTrigger.removeChild(el)
            CCHelper.toastrSuccess("Copied");
        },
    },
    emits: [
        "update:analyzeLoading",
        "update:history",
        "update:isOpened",
        "update:result",
        "update:shopperSense",
        'update:isCollapsed'
    ]
}
</script>

<style>
.slide-fade-enter-active {
    transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
    transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
}

.expand-leave-active,
.expand-enter-active {
    transition: all 500ms ease;
    overflow: hidden;
}

.expand-enter-to,
.expand-leave-from {
    height: v-bind(height);
}

.expand-enter-from,
.expand-leave-to {
    opacity: 0;
    height: 0;
}
</style>