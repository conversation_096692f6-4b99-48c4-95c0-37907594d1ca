<template>
    <div class="panel-box panel-box-banner mt-3">
        <div class="panel-box-banner-inner">
            <div class="icon-box smaller skill-icon-box-shopper_pen_advanced"><i class="far fa-magic font-18"></i></div>
            <div class="panel-box-banner-text">
                <h5 class="mt-0">{{ translations['cloudio::app.sidepanel.banner.generate_descriptions_in_bulk'] }}</h5>
                <p>{{ translations['cloudio::app.sidepanel.banner.try_shopper_pen_advanced'] }}</p>

                <a href="/admin/apps/cloudio/shopper_pen_advanced"
                   target="blank"
                   class="btn btn-default"
                   v-if="shopperPenAdvanced"
                >
                    {{ translations['cloudio::app.sidepanel.button.open'] }}
                </a>
                <a href="javascript:;"
                   class="btn btn-white"
                   @click.prevent="activateShopperPenAdvanced"
                   v-else
                >
                    {{ translations['cloudio::app.sidepanel.button.activate_skill'] }}
                </a>
            </div>

            <div class="panel-box-banner-image col-md-5">
                <img src="https://cdncloudcart.com/storage/shopper_pen_advanced_banner.png" alt="shopper_pen_advanced_banner">
            </div>
        </div>

    </div>
</template>

<script>
import axios from "axios";

export default {
    name: "ShopperPenAdvancedBanner",
    props: {
        translations: {},
        shopperPenAdvanced: {}
    },
    methods: {
        activateShopperPenAdvanced() {
            let body = {
                key: "shopper_pen_advanced",
                status: 1
            }
            axios
                .post('/admin/api/cloudio/skills/status', body)
                .then(res => {
                    CCHelper.toastrSuccess(res?.data?.msg);
                    this.$emit('update:shopperPenAdvanced', true);
                })
                .catch(err => {
                    CCHelper.toastrError(err?.data?.msg);
                })
        }
    }
}
</script>

<style scoped lang="scss">
.panel-box-banner {
    background-color: #EFE5FF;
    padding: var(--spacing-cc-space-lg, 16px);

    h5 {
        color: var(--color-text-body-cc-color-text-primary, #3D414D);
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 18px; /* 128.571% */
    }

    p {
        overflow: hidden;
        color: var(--color-text-body-cc-color-text-secondary, #7A7D84);
        text-overflow: ellipsis;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
    }

    .panel-box-banner-inner {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-cc-space-xs, 8px);
        align-self: stretch;
    }

    .panel-box-banner-text,
    .panel-box-banner-image {
        width: 50%
    }

    .panel-box-banner-image {
        text-align: right;
    }
}

.skill-icon-box-shopper_pen_advanced {
    background-color: #FF774F;
}
</style>