body {
  overflow: visible !important;
}

.terms-text {
  color: var(--color-text-body-cc-color-text-subdued, #9DA0A6);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;

  a {
    color: var(--color-text-body-cc-color-text-link, #8D58E0);
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-decoration-line: underline;
  }
}

.h-100 {
  height: 100px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.disabled,
.readonly {
  pointer-events: none;
  opacity: 0.4;
}

label.readonly {
    opacity: 1;
}

.help-text {
  color: var(--color-text-body-cc-color-text-subdued, #9DA0A6);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;

  a {
    color: var(--color-text-body-cc-color-text-link, #8D58E0);
    font-weight: bold;

    &:hover {
      opacity: .8;
    }
  }
}

.c-pointer {
  cursor: pointer;
}

.label-text {
  color: var(--color-text-body-cc-color-text-secondary, #7A7D84);
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 10px;
}

.related-keywords {
  a {
    color: var(--color-text-body-cc-color-text-link, #8D58E0);
    font-family: Roboto;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;

    &:hover {
      opacity: .8;
    }
  }
}

.description-skill {
  background: var(--color-purple-200, #EFE5FF);
  height: auto;
  width: calc(100% + 2px);

  .description-skill-label {
    display: flex;
    align-items: center;
  }

  &.shopper-pen-seo-wrap {
    background-color: #fff;
    margin-top: -2px;
    width: 100%;

    .skill-icon-box-shopper_pen {
      background-color: #FFE1CB;
    }
  }
}

.results-controls {
  display: flex;
  align-items: center;
}