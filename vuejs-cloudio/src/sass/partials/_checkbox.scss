$form-color: #7a7d84;
$form-background: #fafaff;
$form-input-background: #fff;
$form-border-color: #dedede;
$form-active-background: #8d58e0;
$form-active-color: #fff;
$form-input-focus-border: #B38AF4;
$border-radius-cc-border-radius-sm: 6px;
$color-border-neutral-cc-color-border-focused: #B38AF4;

.checkbox-wrapper {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 6px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  font-size: 16px;

  label {
    color: var(--color-text-body-cc-color-text-primary, #3D414D);
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    width: 100%;
  }

  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }

  .checkmark {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: -18px;
    height: 21px;
    width: 21px;
    border-radius: 2px;
    background-color: #eee;
    border: 1px solid #ccc;
  }

  .checkbox-wrapper:hover input ~ .checkmark {
    background-color: #ccc;
  }

  div.checker {
    display: none;
  }

  input:checked ~ .checkmark,
  .checkmark.checked {
    background-color: $form-active-background;
    border-color: $form-active-background;

    &:hover {
      background-color: #b38af4;
      border-color: #b38af4;
    }

    &:active {
      background-color: #efe5ff;
      border-color: #d2baf7;
    }

    &:focus {
      &:before {
        content: '';
        background-color: transparent;
        border: 1px solid $color-border-neutral-cc-color-border-focused;
        border-radius: $border-radius-cc-border-radius-sm;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: calc(100% + 0.572em);
        height: calc(100% + 0.572em);
        z-index: 20;
      }
    }
  }

  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }

  .checkmark.checked:after,
  input:checked ~ .checkmark:after {
    display: block;
  }

  .checkmark:after {
    left: 7px;
    top: 0px;
    width: 7px;
    height: 15px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
  }
}

