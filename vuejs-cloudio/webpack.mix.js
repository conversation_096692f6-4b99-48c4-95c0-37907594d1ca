/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

const mix = require("laravel-mix");
const path = require("path")

mix.webpackConfig({
    resolve: {
        alias: {
            "@components": path.resolve(__dirname, "src/components"),
            "@js": path.resolve(__dirname, "src/js"),
            // "@services": path.resolve(__dirname, "src/services"),
            // "@utilities": path.resolve(__dirname, "src/utilities"),
        }
    },
    output: {
        filename: '[name].js',
        chunkFilename: 'components/[name].js',
    },
});

mix.extract()
mix.setPublicPath('../../assets/sitecp/vue-cloudio')
mix.vue()
mix.version()
mix.js('src/main.js', 'js/app.js')
mix.sass('src/sass/_main.scss', 'css/style.css')
