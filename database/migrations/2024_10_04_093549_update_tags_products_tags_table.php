<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateTagsProductsTagsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        if (
            Schema::hasTable('tags__products_tags') &&
            !Schema::hasColumns('tags__products_tags', [
                'description',
                'seo_title',
                'seo_description',
                'image',
                'max_thumb_size',
            ])
        ) {
            Schema::table('tags__products_tags', function (Blueprint $table): void {
                if (!Schema::hasColumn('tags__products_tags', 'description')) {
                    $table->text('description')->nullable()->default(null);
                }

                if (!Schema::hasColumn('tags__products_tags', 'seo_title')) {
                    $table->string('seo_title')->nullable()->default(null);
                }

                if (!Schema::hasColumn('tags__products_tags', 'seo_description')) {
                    $table->string('seo_description', 2000)->nullable()->default(null);
                }

                if (!Schema::hasColumn('tags__products_tags', 'image')) {
                    $table->string('image')->index()->nullable()->default(null);
                }

                if (!Schema::hasColumn('tags__products_tags', 'max_thumb_size')) {
                    $table->smallInteger('max_thumb_size')->nullable()->index();
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        //
    }
}
