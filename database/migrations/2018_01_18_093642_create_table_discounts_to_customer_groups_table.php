<?php

declare(strict_types=1);

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTableDiscountsToCustomerGroupsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        if (!Schema::hasTable('discounts_to_customer_groups')) {
            Schema::create('discounts_to_customer_groups', function (Blueprint $table): void {
                $table->increments('id');
                $table->unsignedInteger('discount_id');
                $table->unsignedInteger('customer_group_id');

                $table->unique(['discount_id', 'customer_group_id'], 'uk_discount_id_customer_group_id');

                $table->foreign('discount_id', 'discounts_to_customer_groups_discount_id')->references('id')
                    ->on('discounts')->onDelete('CASCADE')->onUpdate('NO ACTION');
                $table->foreign('customer_group_id', 'discounts_to_customer_groups_customer_group_id')->references('id')
                    ->on('type__customer_groups')->onDelete('CASCADE')->onUpdate('NO ACTION');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('discounts_to_customer_groups');
    }
}
