<?php

declare(strict_types=1);

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateNavigationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        if (!Schema::hasTable('navigations')) {
            Schema::create('navigations', function (Blueprint $table): void {
                $table->increments('id');
                $table->unsignedInteger('group_id');
                $table->unsignedInteger('parent_id')->nullable()->default(null);
                $table->unsignedInteger('order')->index();
                $table->string('type')->nullable()->default(null)
                    ->comment('url,product,category,vendor,blog,article,page,section,group,selection,widget');
                $table->string('link_type')->nullable()->default(null);
                $table->unsignedInteger('link_id')->nullable()->default(null);
                $table->string('route')->nullable()->default(null);
                $table->string('active_find')->nullable()->default(null);
                $table->string('url', 2048)->nullable()->default(null);
                $table->boolean('blank')->default(0);
                $table->string('class')->nullable()->default(null);
                $table->string('name')->nullable()->default(null);
                $table->string('widget')->nullable()->default(null);
                $table->text('widget_text')->nullable()->default(null);
                $table->text('widget_config')->nullable()->default(null);
                $table->text('permissions')->nullable()->default(null);

                $table->foreign('group_id')->references('id')->on('navigations_groups')
                    ->onDelete('CASCADE')->onUpdate('NO ACTION');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('navigations');
    }
}
