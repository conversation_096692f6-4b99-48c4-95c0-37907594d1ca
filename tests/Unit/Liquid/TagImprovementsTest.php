<?php

declare(strict_types=1);

namespace Tests\Unit\Liquid;

use Tests\TestCase;
use Liquid\LiquidCompiler;
use Liquid\Context;

/**
 * Test improvements made to Liquid tags
 */
class TagImprovementsTest extends TestCase
{
    protected LiquidCompiler $compiler;
    protected Context $context;

    protected function setUp(): void
    {
        parent::setUp();
        $this->compiler = new LiquidCompiler();
        $this->context = new Context();
    }

    /** @test */
    public function test_tag_if_new_operators()
    {
        // Test size operator
        $template = '{% if products.size > 0 %}Has products{% endif %}';
        $this->context->set('products', ['item1', 'item2', 'item3']);
        
        $result = $this->compiler->parseAndRender($template, $this->context->assigns);
        $this->assertEquals('Has products', $result);

        // Test type operator
        $template = '{% if name.type == "string" %}Is string{% endif %}';
        $this->context->set('name', 'test');
        
        $result = $this->compiler->parseAndRender($template, $this->context->assigns);
        $this->assertEquals('Is string', $result);

        // Test first operator
        $template = '{{ products.first }}';
        $this->context->set('products', ['first', 'second', 'third']);
        
        $result = $this->compiler->parseAndRender($template, $this->context->assigns);
        $this->assertEquals('first', $result);

        // Test last operator
        $template = '{{ products.last }}';
        $this->context->set('products', ['first', 'second', 'third']);
        
        $result = $this->compiler->parseAndRender($template, $this->context->assigns);
        $this->assertEquals('third', $result);
    }

    /** @test */
    public function test_tag_tablerow_shopify_attributes()
    {
        // Test limit attribute
        $template = '{% tablerow product in products limit:2 %}<td>{{ product }}</td>{% endtablerow %}';
        $this->context->set('products', ['item1', 'item2', 'item3', 'item4']);
        
        $result = $this->compiler->parseAndRender($template, $this->context->assigns);
        $this->assertStringContains('<td>item1</td>', $result);
        $this->assertStringContains('<td>item2</td>', $result);
        $this->assertStringNotContains('<td>item3</td>', $result);

        // Test cols attribute
        $template = '{% tablerow product in products cols:2 %}<td>{{ product }}</td>{% endtablerow %}';
        $this->context->set('products', ['item1', 'item2', 'item3', 'item4']);
        
        $result = $this->compiler->parseAndRender($template, $this->context->assigns);
        $this->assertStringContains('<tr class="row1">', $result);
        $this->assertStringContains('<tr class="row2">', $result);
    }

    /** @test */
    public function test_tag_javascript_creation()
    {
        $template = '{% javascript %}console.log("Hello World");{% endjavascript %}';
        
        $result = $this->compiler->parseAndRender($template, $this->context->assigns);
        $this->assertStringContains('<script>', $result);
        $this->assertStringContains('console.log("Hello World");', $result);
        $this->assertStringContains('</script>', $result);
    }

    /** @test */
    public function test_tag_yield_creation()
    {
        // Set up content_for area
        $this->context->set('content_areas', ['header' => '<meta name="test" content="value">']);
        
        $template = '{% yield "header" %}';
        
        $result = $this->compiler->parseAndRender($template, $this->context->assigns);
        $this->assertEquals('<meta name="test" content="value">', $result);
    }

    /** @test */
    public function test_custom_tags_error_handling()
    {
        // Test TagCall with missing function
        $this->context->set('strict_variables', false);
        
        $template = '{% call "nonexistent_function" %}';
        
        // Should not throw exception in non-strict mode
        $result = $this->compiler->parseAndRender($template, $this->context->assigns);
        $this->assertEquals('', $result);
    }

    /** @test */
    public function test_tag_route_error_handling()
    {
        // Test TagRoute with invalid route
        $this->context->set('strict_variables', false);
        
        $template = '{% route "invalid.route" %}';
        
        // Should return # in non-strict mode for invalid routes
        $result = $this->compiler->parseAndRender($template, $this->context->assigns);
        $this->assertEquals('#', $result);
    }

    /** @test */
    public function test_tag_function_validation()
    {
        // Test function with valid name
        $template = '{% function valid_function_name %}Content{% endfunction %}';
        
        $result = $this->compiler->parseAndRender($template, $this->context->assigns);
        $this->assertEquals('', $result); // Function registration returns empty string

        // Test function call
        $template2 = '{% call "valid_function_name" %}';
        
        $result2 = $this->compiler->parseAndRender($template2, $this->context->assigns);
        $this->assertEquals('Content', trim($result2));
    }
}
