@startuml LAYOUT_TOP_DOWN Sample
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml
!define DEVICONS https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/devicons
!define FONTAWESOME https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/font-awesome-5
!include FONTAWESOME/users.puml


Person(user, "Murchant / Admin")
Person(custs, "Customers")
System(email, "e-Mail")
System(extsys, "External Systems")


' LAYOUT_TOP_DOWN()
LAYOUT_LEFT_RIGHT()

System_Boundary(ccadmin, 'CloudCart Administration') {

    System_Boundary(orders, 'Orders') {
        Container(ordermanagement, "View / Manage Orders", "ordermanagement", "View, modifay and process orders.")
        Container(orderedproducts, "Products in Orders", "orderedproducts", "List of products and the qtty of ordered products.")
        Container(abondenedcarts, "View Abondened Carts", "abondenedcarts", "View Abondened Carts")
        Container(invoices, "Invoices", "invoices", "Invoices")
    }

    System_Boundary(products, 'Products') {
        Container(allproducts, "Products", "allproducts", "All Products List")
        Container(inventory, "Inventory", "inventory", "Addjust products inventory")
        Container(categories, "Categories", "productcategories", "Manage categories")
        Container(producers, "Producers", "product", "Manage producers")
        Container(collections, "Collections", "collections", "Manage Collections")

        System_Boundary(others, 'Other') {
            Container(variants, "Variants", "productvariants", "Manage Variations")
            Container(characteristics, "Product Characteristics", "characteristics", "Manage characteristics")
            Container(statuses, "Product Statuses", "productstatus", "Manage statuses")
            Container(options, "Product Options", "productoptions", "Manage Options")
        }
    }

    System_Boundary(clients, 'Clients') {
        Container(allclient, "Customers", "allclients", "List and manage customers")
        Container(custgroups, "Customer Groups", "custgroups", "Manage groups")
        Container(extrafields, "Additional Fields", "extrafields", "Manage fields")
        Container(segments, "Customer segments", "segments", "Manage customer segments")
    }

    System_Boundary(reports, 'Reports') {
        Container(bysales, "Sales", "bysales", "Report")
        Container(byproducts, "Products", "byproducts", "Report")
        Container(bypayments, "Payments", "bypayments", "Report")
        Container(bycustomers, "Customers", "bycustomers", "Report")
    }

    System_Boundary(marketing, 'Marketing') {
        Container(discounts, "Discounts", "discounts", "Manage product discounts")
        Container(bumper, "Cart offers", "bumper", "Manage bumper offers")
        Container(seooptimizations, "SEO Optimization", "seooptimizations", "Manage SEO")
        Container(channelsales, "Sales Channels", "channelsales", "Manage external channels")
        Container(notifications, "Notifications", "notifications", "Manage notifications and templates")
        Container(corepages, "Pages", "corepages", "Manage and create pages e.g. GDPR, T&C")
        Container(messenger, "Messenger", "messenger", "Manage messanger")

        System_Boundary(blog, 'Blog') {
            Container(publications, "Publications", "publications", "Manage publications")
            Container(pagecategories, "Categories", "pagecategories", "Manage blog categories")
            Container(blogcomments, "Comments", "blogcomments", "Manage comments")
        }

    }

    Container(apps, "Applications", "apps", "Manage, install, delete additional functionality")
    Container(ccprofile, "Profile", "profile", "CloudCart Session")


    System_Boundary(shopconf, 'Shop Configuration') {
        Container(shopdesign, "Choose design", "shopdesign", "Switch shop designs")
        Container(menunavigation, "Menu and navigation", "menunavigation", "Manage navigation")
        Container(widgets, "Widgets", "widgets", "Manage and configure widgets")
        Container(edittemplates, "UI Templates", "edittemplates", "Customize current template")
    }


    System_Boundary(settings, 'Settings') {
        Container(mains, "Settings", "main", "Manage configuration")
        Container(carts, "Cart Settings", "cart", "Manage cart settings")
        Container(invoices, "Invoices", "invoices", "Setup invoices")
        Container(payment_s, "Payments", "payments", "Setup payments")
        Container(shipping_s, "Shipping", "shippings", "Setup shipping")
        Container(zones_s, "Geo Zones", "zones", "Setup delivery zones")
        Container(taxes_s, "Taxes", "taxes", "Setup shop taxes and fees")
        Container(domain_s, "Domains", "domains", "Setup shop domains")
        Container(emails_s, "e-mails", "emails", "Setup and manage e-mails")

        System_Boundary(set_others, 'Other settings') {
            Container(translations, "Translations", "translations", "Manage text translations")
            Container(files_s, "Shop files", "shopfiles", "Manage shop files")
            Container(statuses, "Statuses", "orderstatus", "Manage statuses")
            Container(custfiles, "Customer files", "custfiles", "View and manage customer uploads")

            Container(apikeys, "API Keys", "apikeys", "Manage API keys")
            Container(custfiles, "Customer files", "custfiles", "View and manage customer uploads")
            Container(webhooks, "Web Hooks", "webhooks", "Manage web hooks")
            Container(deliveryboxes, "Delivery boxes", "deliveryboxes", "Manage delivery boxes and dementions")
            Container(myshops, "Shops", "custfiles", "View and manage customer uploads")
            Container(queuejobs, "Jobs", "queuejobs", "View current shop jobs")
        }


    }

}


Rel_L(user, ccadmin, "Uses", "https")
Rel_L(user, ccprofile, "Uses", "https")
Rel_L(ccadmin, email, "Uses", "smtp")


Rel_D(ccadmin, extsys, "Uses", "API")
Rel(extsys, ccadmin, "Uses", "API")

Rel_L(email, custs, "Uses", "smtp")


Rel(ccprofile, orders, "Uses", "https")
Rel(ccprofile, products, "Uses", "https")

' Rel(products, orders, "Uses", "https")

Rel_U(ccprofile, apps, "Uses", "https")


Rel(ccprofile, clients, "Uses", "https")
Rel(ccprofile, reports, "Uses", "https")
Rel(ccprofile, marketing, "Uses", "https")

Rel(ccprofile, shopconf, "Uses", "https")
Rel(ccprofile, settings, "Uses", "https")



@enduml
