<?php

declare(strict_types=1);

use App\Models\Blog\Article;
use App\Models\Product\Product;

use App\LiquidEngine\LiquidHelpers\Drop\Blog\Article as ArticleDrop;
use App\LiquidEngine\LiquidHelpers\Drop\ProductByType;

// Core tags from Laravel-Liquid package
use Liquid\Tag\TagAssign;
use Liquid\Tag\TagBlock;
use Liquid\Tag\TagBreak;
use Liquid\Tag\TagCapture;
use Liquid\Tag\TagCase;
use Liquid\Tag\TagComment;
use Liquid\Tag\TagContentFor;
use Liquid\Tag\TagContinue;
use Liquid\Tag\TagCycle;
use Liquid\Tag\TagDecrement;
use Liquid\Tag\TagDoc;
use Liquid\Tag\TagEcho;
use Liquid\Tag\TagExtends;
use Liquid\Tag\TagFor;
use Liquid\Tag\TagForm;
use Liquid\Tag\TagIf;
use Liquid\Tag\TagIfchanged;
use Liquid\Tag\TagInclude;
use Liquid\Tag\TagIncrement;
use Liquid\Tag\TagInlineComment;
use Liquid\Tag\TagLayout;
use Liquid\Tag\TagLiquid;
use Liquid\Tag\TagPaginate;
use Liquid\Tag\TagRaw;
use Liquid\Tag\TagRender;
use Liquid\Tag\TagSchema;
use Liquid\Tag\TagSection;
use Liquid\Tag\TagSections;
use Liquid\Tag\TagStyle;
use Liquid\Tag\TagStylesheet;
use Liquid\Tag\TagTablerow;
use Liquid\Tag\TagUnless;
use Liquid\Tag\TagJavascript;
use Liquid\Tag\TagYield;

// Custom tags from CloudCart LiquidEngine
use App\LiquidEngine\LiquidHelpers\Tags\TagAuth;
use App\LiquidEngine\LiquidHelpers\Tags\TagCall;
use App\LiquidEngine\LiquidHelpers\Tags\TagFunction;
use App\LiquidEngine\LiquidHelpers\Tags\TagGdpr;
use App\LiquidEngine\LiquidHelpers\Tags\TagGoogleReCaptcha;
use App\LiquidEngine\LiquidHelpers\Tags\TagGuest;
use App\LiquidEngine\LiquidHelpers\Tags\TagInstagram;
use App\LiquidEngine\LiquidHelpers\Tags\TagOrderBy;
use App\LiquidEngine\LiquidHelpers\Tags\TagPerPage;
use App\LiquidEngine\LiquidHelpers\Tags\TagScript;
use App\LiquidEngine\LiquidHelpers\Tags\TagSearchInputConfig;
use App\LiquidEngine\LiquidHelpers\Tags\TagSocials;
use App\LiquidEngine\LiquidHelpers\Tags\TagRoute;
use App\LiquidEngine\LiquidHelpers\Tags\TagCsrfToken;

// Core filters from Laravel-Liquid package
use Liquid\Filters\ArrFilters;
use Liquid\Filters\ColorFilters;
use Liquid\Filters\CookieFilters;
use Liquid\Filters\CustomerAvatarFilters;
use Liquid\Filters\DateFilters;
use Liquid\Filters\DefFilters;
use Liquid\Filters\EscapeFilters;
use Liquid\Filters\FontModifyFilters;
use Liquid\Filters\HelperFilters;
use Liquid\Filters\InlineAssetFilters;
use Liquid\Filters\LineItemsForCartFilters;
use Liquid\Filters\MathFilters;
use Liquid\Filters\MultyFilters;
use Liquid\Filters\StrFilters;
use Liquid\Filters\StructuredDataFilters;

// Custom filters from CloudCart LiquidEngine
use App\LiquidEngine\LiquidHelpers\LiquidFilters\All;
use App\LiquidEngine\LiquidHelpers\LiquidFilters\UrlFilters;
use App\LiquidEngine\LiquidHelpers\LiquidFilters\AssetFilters;

// Custom drops
use App\LiquidEngine\LiquidHelpers\Drop\Request;
use App\LiquidEngine\LiquidHelpers\Drop\Cart;

return [

    /*
     |--------------------------------------------------------------------------
     | Liquid auto escape html
     |--------------------------------------------------------------------------
     |
     | By default is on.
     */
    'auto_escape' => true,

    /*
     |--------------------------------------------------------------------------
     | Liquid extension
     |--------------------------------------------------------------------------
     |
     | By default extension is liquid.
     */
    'extension' => 'liquid',

    /*
     |--------------------------------------------------------------------------
     | Liquid Compiled Templates Storage
     |--------------------------------------------------------------------------
     |
     | This option controls how compiled Liquid templates are stored.
     | Supported drivers: "file", "database"
     |
     | The file driver stores compiled templates as PHP files on disk.
     | The database driver stores compiled templates in the database.
     */
    'compiled_store' => [
        'default' => env('LIQUID_COMPILED_STORE', 'file'),

        'drivers' => [
            'file' => [
                'driver' => 'file',
                'path' => storage_path('framework/views/compiled/liquid'),
                'debug' => env('APP_DEBUG', false),
            ],

            'database' => [
                'driver' => 'database',
                'table' => 'liquid_compiled_templates',
                'connection' => null, // uses default DB connection
                'cache_size' => 100,  // LRU cache size
                'debug' => env('APP_DEBUG', false),
            ],

            // Future Redis driver template
            'redis' => [
                'driver' => 'redis',
                'connection' => 'default',
                'prefix' => 'liquid_compiled',
                'ttl' => 86400, // 24 hours
                'debug' => env('APP_DEBUG', false),
            ],
        ]
    ],

    /*
     |--------------------------------------------------------------------------
     | Liquid templates finder
     |--------------------------------------------------------------------------
     |
     | By default finder is file
     */
    'finder' => [
        'default' => env('LIQUID_VIEW_FINDER', 'file'),

        'drivers' => [
            // Core drivers from Laravel-Liquid
            'file' => [
                'driver' => 'file',
                'paths' => [
                    resource_path('resources/themes/_global/global-theme')
                ],
            ],

            'database' => [
                'driver' => 'database',
                'table' => 'templates',
                'connection' => null,
            ],

            // Custom drivers from CloudCart LiquidEngine
            'custom_file' => [
                'driver' => 'custom_file',
                'path' => [
                    resource_path('themes/%s'),
                    resource_path('themes/_global/global-theme'),
                ],
            ],

            'custom_database' => [
                'driver' => 'custom_database',
                'table' => 'themes_templates',
                'connection' => 'themes',
            ],
        ]
    ],

    /*
     |--------------------------------------------------------------------------
     | Liquid allowed tags
     |--------------------------------------------------------------------------
     */
    'tags' => [
        // Core tags from Laravel-Liquid package
        'assign' => TagAssign::class,
        'block' => TagBlock::class,
        'break' => TagBreak::class,
        'capture' => TagCapture::class,
        'case' => TagCase::class,
        'comment' => TagComment::class,
        'content_for' => TagContentFor::class,
        'continue' => TagContinue::class,
        'cycle' => TagCycle::class,
        'decrement' => TagDecrement::class,
        'doc' => TagDoc::class,
        'echo' => TagEcho::class,
        'extends' => TagExtends::class,
        'for' => TagFor::class,
        'form' => TagForm::class,
        'if' => TagIf::class,
        'ifchanged' => TagIfchanged::class,
        'include' => TagInclude::class,
        'increment' => TagIncrement::class,
        'inline_comment' => TagInlineComment::class,
        'layout' => TagLayout::class,
        'liquid' => TagLiquid::class,
        'paginate' => TagPaginate::class,
        'raw' => TagRaw::class,
        'render' => TagRender::class,
        'schema' => TagSchema::class,
        'section' => TagSection::class,
        'sections' => TagSections::class,
        'style' => TagStyle::class,
        'stylesheet' => TagStylesheet::class,
        'tablerow' => TagTablerow::class,
        'unless' => TagUnless::class,
        'javascript' => TagJavascript::class,
        'yield' => TagYield::class,

        // Custom tags from CloudCart LiquidEngine
        'auth' => TagAuth::class,
        'call' => TagCall::class,
        'function' => TagFunction::class,
        'gdpr' => TagGdpr::class,
        'googleReCaptcha' => TagGoogleReCaptcha::class,
        'guest' => TagGuest::class,
        'instagram' => TagInstagram::class,
        'orderBy' => TagOrderBy::class,
        'perPage' => TagPerPage::class,
        'script' => TagScript::class,
        'searchInputConfig' => TagSearchInputConfig::class,
        'socials' => TagSocials::class,
        'route' => TagRoute::class,
        'csrf_token' => TagCsrfToken::class,
    ],

    /*
     |--------------------------------------------------------------------------
     | Liquid allowed filters
     |--------------------------------------------------------------------------
     */
    'filters' => [
        // Core filters from Laravel-Liquid package
        ArrFilters::class,
        ColorFilters::class,
        CookieFilters::class,
        CustomerAvatarFilters::class,
        DateFilters::class,
        DefFilters::class,
        EscapeFilters::class,
        FontModifyFilters::class,
        HelperFilters::class,
        InlineAssetFilters::class,
        LineItemsForCartFilters::class,
        MathFilters::class,
        MultyFilters::class,
        StrFilters::class,
        StructuredDataFilters::class,

        // Custom filters from CloudCart LiquidEngine
        All::class,
        UrlFilters::class,
        AssetFilters::class,
    ],

    /*
     |--------------------------------------------------------------------------
     | Transform Laravel Model
     |--------------------------------------------------------------------------
     */
    'transform_model' => [
        Product::class => [ProductByType::class, 'get'],
        Article::class => [ArticleDrop::class, 'make'],
    ],

    /*
     |--------------------------------------------------------------------------
     | Protected variables for assign
     |--------------------------------------------------------------------------
     */
    'protected_variables' => [
        'content_for_header',
        'content_for_layout',
        'content_for_index',
        'content_for_footer',
        //checkout
        'order_summary_toggle',
        'content_for_order_summary',
        'powered_by',
    ],

    'templates_path' => 'resources/themes',

    'locales' => [
        [
            'name' => 'English',
            'code' => 'en',
        ],
        [
            'name' => 'Bulgarian',
            'code' => 'bg',
        ],
        // TODO - add others
    ],

    /*
    |--------------------------------------------------------------------------
    | Layout Settings
    |--------------------------------------------------------------------------
    |
    | These settings control how layouts are applied to templates.
    |
    */

    // Default layout template
    'default_layout' => 'theme::layout.theme',

    // Whether to automatically wrap content with layouts
    'auto_layout' => true,

    // Paths that should not have layouts applied
    'exclude_layout_paths' => [
        'api/*',
        'ajax/*',
        'webhook/*',
        'assets/*',
    ],

    /*
    |--------------------------------------------------------------------------
    | Template Resolution
    |--------------------------------------------------------------------------
    |
    | These settings control how templates are resolved based on controllers.
    |
    */

    // Template resolution cache duration in seconds
    'template_cache_ttl' => 86400, // 1 даъ

    // Default template to use when no specific template is found
    'default_template' => 'templates.default',

    // Template fallback paths
    'template_fallbacks' => [
        'index' => 'templates.list',
        'view' => 'templates.detail',
        'show' => 'templates.detail',
    ],

    /*
    |--------------------------------------------------------------------------
    | Section Rendering
    |--------------------------------------------------------------------------
    |
    | These settings control how sections are rendered.
    |
    */

    // Section parameter name in requests
    'section_param' => 'section_id',

    // Whether to cache rendered sections
    'cache_sections' => true,

    // Section cache duration in seconds
    'section_cache_ttl' => 600, // 10 minutes

    /*
    |--------------------------------------------------------------------------
    | URL Settings
    |--------------------------------------------------------------------------
    |
    | These settings control how URLs are handled.
    |
    */

    // Whether URLs should have trailing slashes
    'url_trailing_slash' => false,

    // Whether to normalize URLs (redirect to canonical paths)
    'url_normalize' => true,

    // URL path mappings for redirects (old path => new path)
    'url_mappings' => [
        'product/' => 'products/',
        'category/' => 'collections/',
        'blog/' => 'blogs/',
    ],

];
