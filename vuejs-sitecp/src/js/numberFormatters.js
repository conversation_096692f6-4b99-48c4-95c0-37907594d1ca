import {appConfig} from "./appHolder";

export function number(value, decimals, dec_point, thousands_sep) {
    let number = ((typeof value !== 'number' ? Number(value) : value) + '').replace(/[^0-9+\-Ee.]/g, ''),
        n = !isFinite(+number) ? 0 : +number,
        prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
        sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
        dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
        s = '',
        toFixedFix = function (n, prec) {
            var k = Math.pow(10, prec);
            return '' + (Math.round(n * k) / k).toFixed(prec);
        };
    // Fix for IE parseFloat(0.55).toFixed(0) = 0;
    s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }
    if ((s[1] || '').length < prec) {
        s[1] = s[1] || '';
        s[1] += new Array(prec - s[1].length + 1).join('0');
    }
    return s.join(dec);
}

export function precisionRound(value, precision) {
    let number = ( typeof value !== 'number' ? Number(value) : value );
    precision = typeof(precision) == 'undefined' ? 2 : precision;
    var factor = Math.pow(10, precision);
    return Math.round(number * factor) / factor;
}

export function money(value, decimals, dec_point, thousands_sep, simbol_left, simbol_right) {
    let number = (typeof value !== 'number' ? Number(value) : value),
        stringNumber = String(number),
        prefix = '';

    if (stringNumber.substring(0, 1) === '+' || stringNumber.substring(0, 1) === '-') {
        prefix = stringNumber.substring(0, 1) + ' ';
        number = precisionRound(stringNumber.substring(1), decimals);
    }

    return prefix + (simbol_left ? simbol_left : '') + numberFormat(number, decimals, dec_point, thousands_sep) + (simbol_right ? '' + simbol_right : '');
}

export function numberFormat(value, coins_pad = 0){
    return number(value, coins_pad !== undefined ? coins_pad : appConfig().get('currency.coins_pad'), appConfig().get('currency.dec_point'), appConfig().get('currency.thousands_sep'));
}

export function numberInput(value, coins_pad = 0){
    return number(value, coins_pad, '.', '');
}

export function weightFormat(value, coins_pad = 0){
    return number(value, 3, '.', '');
}

export function quantityFormat(value, coins_pad = 0){
    return number(value, 3, '.', '');
}

export function moneyInput(value, coins_pad = undefined){
    return Number(value).toFixed(coins_pad !== undefined ? coins_pad : appConfig().get('currency.coins_pad'));
}

export function moneyFormat(value, coins_pad){
    return money(value, coins_pad !== undefined ? coins_pad : appConfig().get('currency.coins_pad'), appConfig().get('currency.dec_point'), appConfig().get('currency.thousands_sep'), appConfig().get('currency.sign_left'), appConfig().get('currency.sign_right'));
}

export function moneyFormatNoSign(value){
    return (Number(value) / 100).toFixed(2);
}

export function percentFormat(value, coins_pad = 2){
    return numberFormat(value, coins_pad) + '%';
}

export const numberFormatters = {
    install(app) {
        app.config.globalProperties.numberFormat = numberFormat
        app.config.globalProperties.moneyFormat = moneyFormat
        app.config.globalProperties.percentFormat = percentFormat
        app.config.globalProperties.moneyFormatNoSign = moneyFormatNoSign
        app.config.globalProperties.moneyInput = moneyInput
        app.config.globalProperties.weightFormat = weightFormat
        app.config.globalProperties.quantityFormat = quantityFormat
        app.config.globalProperties.numberInput = numberInput
    }
}