//not completed
import { reactive, watch, ref } from 'vue';
import axios from 'axios';
import { CcSentry } from '../Sentry/sentry';

const axiosInstance = axios.create({
    headers: {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        // 'Connection': 'keep-alive', // Refused to set unsafe header "Connection"
        // 'Content-Type': 'application/json',
        'Upgrade-Insecure-Requests': '1',
        'Alt-Used': 'http2',
    },
    withCredentials: true,
});

axiosInstance.interceptors.request.use(request => {
    return request
}, error => {
    console.error('Error in request:', error);
    CcSentry.exception({ ...error, fromModelRequest: true, fromAxiosInterceptors: true });
    return Promise.reject(error);
})

axiosInstance.interceptors.response.use(response => {
    return response
}, error => {
    console.error('Error in response:', error);
    CcSentry.exception({ ...error, fromModelRequest: true, fromAxiosInterceptors: true });
    return Promise.reject(error);
})

class Model {

    updateMethod = 'put'

    constructor(endpoint, primaryKey = 'id') {
        this.endpoint = endpoint;
        this.data = reactive({});
        this.response = reactive({});
        this.isFetching = ref(false);
        this.isDeleting = ref(false);
        this.events = {
            creating: [],
            created: [],
            updating: [],
            updated: [],
            deleting: [],
            deleted: [],
        };
        this.exists = ref(false);
        this.primaryKey = primaryKey;
        this.conditions = {}

        // Watch for changes and update the model data
        watch(() => this.isFetching, (newVal) => {
            if (!newVal) {
                this.data = { ...this.data };
            }
        });
    }

    async find(id) {
        try {
            this.isFetching = true;
            const response = await axiosInstance.get(`${this.endpoint}/${id}`);
            this.data = response.data;
            this.response = response;
            return this.data;
        } catch (error) {
            console.error('Error fetching data:', error);
            this.response = error.response;
            throw error;
        } finally {
            this.isFetching = false;
        }
    }

    async all() {
        try {
            this.isFetching = true;
            const response = await axiosInstance.get(this.endpoint, {
                params: this.conditions,
            });
            this.response = response;
            return response.data;
        } catch (error) {
            console.error('Error fetching data:', error);
            this.response = error.response;
            throw error;
        } finally {
            this.isFetching = false;
        }
    }

    async paginate(page = 1, perPage = 15) {
        try {
            this.isFetching = true;
            const response = await axiosInstance.get(this.endpoint, {
                params: {
                    ...this.conditions,
                    page: page,
                    perpage: perPage,
                }
            });

            return response.data;
        } catch (error) {
            console.error('Error fetching data:', error);
            this.response = error.response;
            throw error;
        } finally {
            this.isFetching = false;
        }
    }

    async create(data, headers = null) {
        try {
            // Emit creating event
            let allowCreate = this.emitEvent('creating', data);
            if (!allowCreate) {
                return;
            }

            this.isFetching = true;
            const response = await axiosInstance.post(this.endpoint, data, headers ? { ...headers } : null);
            this.data = response.data;
            this.response = response;

            // Emit created event
            this.emitEvent('created', this.data);

            return this.data;
        } catch (error) {
            console.error('Error creating data:', error);
            this.response = error.response;
            throw error;
        } finally {
            this.isFetching = false;
        }
    }

    async post(id = null, data = {}, headers = null) {
        try {

            this.isFetching = true;

            const response = await axiosInstance.post(`${this.endpoint}/${id}`, data, headers ? { ...headers } : null);

            this.data = response.data;
            this.response = response;

            // Emit created event
            this.emitEvent('post', this.data);

            return this.data;
        } catch (error) {
            console.error('Error posting data:', error);
            this.response = error.response;
            throw error;
        } finally {
            this.isFetching = false;
        }
    }

    async update(data, id = null) {
        const key = id || this.getKey();

        if (!key) {
            return;
        }

        try {
            // Emit updating event
            let allowUpdate = this.emitEvent('updating', data);
            if (!allowUpdate) {
                return;
            }

            if (data instanceof FormData && this.updateMethod === 'post') {
                data.append('_method', 'PUT');
            }

            this.isFetching = true;
            const response = await axiosInstance[this.updateMethod](`${this.endpoint}/${id}`, data);
            this.data = response.data;
            this.response = response;

            // Emit updated event
            this.emitEvent('updated', this.data);

            return this.data;
        } catch (error) {
            console.error('Error updating data:', error);
            this.response = error.response;
            throw error;
        } finally {
            this.isFetching = false;
        }
    }

    async patch(data, id = null, headers = null) {
        // const key = id || this.getKey();
        //
        // if (!key) {
        //     return;
        // }
        let url = this.endpoint;
        if (id) {
            url += `/${id}`;
        }
        try {
            // Emit updating event
            let allowUpdate = this.emitEvent('updating', data, headers ? { ...headers } : null);
            if (!allowUpdate) {
                return;
            }

            this.isFetching = true;
            // const response = await axiosInstance.patch(`${this.endpoint}/${id}`, data);
            const response = await axiosInstance.patch(url, data);
            this.data = response.data;
            this.response = response;

            // Emit updated event
            this.emitEvent('updated', this.data);

            return this.data;
        } catch (error) {
            console.error('Error updating data:', error);
            this.response = error.response;
            throw error;
        } finally {
            this.isFetching = false;
        }
    }

    async delete(id = null) {
        const key = id || this.getKey();

        if (!key) {
            return;
        }

        try {
            // Emit deleting event
            let allowDelete = this.emitEvent('deleting', key);
            if (!allowDelete) {
                return;
            }

            this.isFetching = true;
            this.isDeleting = true;
            const response = await axiosInstance.delete(`${this.endpoint}/${key}`, {
                params: this.conditions
            });
            this.response = response;

            // Emit deleted event
            this.emitEvent('deleted', key);

            return response.data;
        } catch (error) {
            console.error('Error deleting data:', error);
            this.response = error.response;
            throw error;
        } finally {
            this.isFetching = false;
            this.isDeleting = false;
        }
    }

    async deleteBulk(ids = null) {
        if (!ids) {
            return;
        }

        try {
            // Emit deleting event
            let allowDelete = this.emitEvent('deleting', ids);
            if (!allowDelete) {
                return;
            }

            this.isFetching = true;
            this.isDeleting = true;
            const response = await axiosInstance.delete(`${this.endpoint}`, {
                params: { ids }
            });
            this.response = response;

            // Emit deleted event
            this.emitEvent('deleted', ids);

            return response.data;
        } catch (error) {
            console.error('Error deleting data:', error);
            this.response = error.response;
            throw error;
        } finally {
            this.isFetching = false;
            this.isDeleting = false;
        }
    }

    setEndpoint(endpoint) {
        this.endpoint = endpoint;
        return this;
    }

    where(conditions) {
        this.conditions = conditions;
        return this;
    }

    get(attribute) {
        return this.data[attribute];
    }

    set(attribute, value) {
        this.data[attribute] = value;
    }

    creating(callback) {
        this.on('creating', callback);
    }

    created(callback) {
        this.on('created', callback);
    }

    updating(callback) {
        this.on('updating', callback);
    }

    updated(callback) {
        this.on('updated', callback);
    }

    deleting(callback) {
        this.on('deleting', callback);
    }

    deleted(callback) {
        this.on('deleted', callback);
    }

    on(event, callback) {
        String(event).split(' ').map((e) => {
            if (this.events[e]) {
                this.events[e].push(callback);
            }
        })
    }

    emitEvent(event, payload) {
        let emitResult = true;
        if (this.events[event]) {
            this.events[event].forEach((callback) => {
                if (typeof callback === 'function') {
                    const result = callback.call(this.data, payload);
                    if (result === false) {
                        emitResult = false;
                    }
                }
            });
        }

        return emitResult;
    }

    getKey() {
        return this.data[this.primaryKey];
    }

    setPrimaryKey(key) {
        this.primaryKey = key;
    }
}

export default Model;
