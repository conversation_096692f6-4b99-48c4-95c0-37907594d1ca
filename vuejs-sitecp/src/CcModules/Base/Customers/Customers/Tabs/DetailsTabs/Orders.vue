<template>
    <data-table
        v-bind="table"
        @pagination-change-page="paginate"
        :enable-mobile="true"
        app-name="orders"
        :get-data="getData"
        :filter-options="filterOptions"
        v-model:query="query"
        :filters="true"
    >
    </data-table>
</template>

<script>
import Orders from "../../js/Orders";
import useSharedState from "../../composables/useSharedState";
import { markRaw } from "vue";

import DataTable from "@components/Table";
import TableActions from "@components/TableActions";
import OrdersFilters from "./filters/CustomersFilter";

import Order from "../../components/Orders/Order";
import Address from "../../components/Orders/Address";
import Date from "./../../components/Table/Date"
import Status from "../../components/Orders/Status";
import Total from "../../components/Orders/Total";
import RowActions from "../../components/Orders/RowActions";

export default {
    name: "Orders",
    mixins: [OrdersFilters],
    components: {
        DataTable,
        TableActions,
    },
    setup() {
        const { details } = useSharedState();
        return { details };
    },
    data() {
        return {
            tableLoading: true,
            model: new Orders(),
            tableData: { data: [] },
            page: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            query: this.$route.query,
            translations: {
                Address: this.$t("Address"),
                Type: this.$t("Type"),
                "Post code": this.$t("Post code"),
                "Order ID": this.$t("Order ID"),
                "Deleted successfully": this.$t("Deleted successfully"),
                "Error while deleting": this.$t("Error while deleting"),
                "Default address updated": this.$t("Default address updated"),

                // filters
                Payment: this.$t("Payment"),
                Region: this.$t("Region"),
                Customer: this.$t("Customer"),
                Date: this.$t("Date"),
                Is: this.$t("Is"),
                No: this.$t("No"),
                Yes: this.$t("Yes"),
                Archived: this.$t("Archived"),
                Fulfilled: this.$t("Fulfilled"),
                Discount: this.$t("Discount"),
                Draft: this.$t("Draft"),
                Initiated: this.$t('Initiated'),
                Fulfillment: this.$t("Fulfillment"),
                Receiving: this.$t("Receiving"),
                Status: this.$t("Status"),
                "Total price": this.$t("Total price"),
                Exactly: this.$t("Exactly"),
                Pending: this.$t('Pending'),
                Requested: this.$t('Requested'),
                Held: this.$t('Held'),
                Completed: this.$t('Completed'),
                Failed: this.$t('Failed'),
                Refunded: this.$t('Refunded'),
                Voided: this.$t('Voided'),
                Paid: this.$t("Paid"),
                ID: this.$t("ID"),
                Cancelled: this.$t('Cancelled'),
                Timeouted: this.$t('Timeouted'),
                Chargebacked: this.$t('Chargebacked'),
                Disputed: this.$t('Disputed'),
                Between: this.$t("Between"),
                Flat: this.$t("Flat"),
                Percent: this.$t("Percent"),
                Shipping: this.$t("Shipping"),
                Fixed: this.$t("Fixed"),
                Invoice: this.$t("Invoice"),
                In: this.$t("In"),
                Authorized: this.$t("Authorized"),
                Supplier: this.$t("Supplier"),
                Includes: this.$t("Includes"),
                Referer: this.$t("Referer"),
                "Date added": this.$t("Date added"),
                "Payment providers": this.$t("Payment providers"),
                "Order status": this.$t("Order status"),
                "Not equal to": this.$t("Not equal to"),
                "More than": this.$t("More than"),
                "Less than": this.$t("Less than"),
                "Payment status": this.$t("Payment status"),
                "Created by admin": this.$t("Created by admin"),
                "Shipping method": this.$t("Shipping method"),
                "Is not": this.$t("Is not"),
                "Discount code": this.$t("Discount code"),
                "Fulfillment status": this.$t("Fulfillment status"),
                "Fast order": this.$t("Fast order"),
                "Not Fulfilled": this.$t("Not Fulfilled"),
                "Customer group": this.$t("Customer group"),
                "Credit Note": this.$t("Credit Note"),
                "UTM Source": this.$t("UTM Source"),
                "UTM Medium": this.$t("UTM Medium"),
                "UTM Campaign": this.$t("UTM Campaign"),
                "Status payment": this.$t("Status payment"),
                "More than or equal": this.$t("More than or equal"),
                "Less than or equal": this.$t("Less than or equal"),
                "Does not include": this.$t("Does not include"),
                "Messenger bot": this.$t("Messenger bot"),
                "Made through": this.$t("Made through"),
                "Not in": this.$t("Not in"),
                "No Discount": this.$t("No Discount"),
                "Invoice number": this.$t("Invoice number"),
                "Any Discount": this.$t("Any Discount"),
                "Recovered Source": this.$t("Recovered source"),
                "Is refunded": this.$t("Is refunded"),
                "Is chargebacked": this.$t("Is chargebacked"),
                "Is completed": this.$t("Is completed"),
                "Is pending": this.$t("Is pending"),
            },
        };
    },
    computed: {
        filterOptions(){
            return [
                {
                    key: "total_price",
                    label: this.translations["Total price"],
                    type: "currency",
                    options: this.compareOptions
                },
                {
                    key: "status",
                    label: this.translations["Order status"],
                    options: this.isInNotIn,
                    selectOptions: [
                        {id: "authorized", name: this.translations["Authorized"]},
                        {id: "pending", name: this.translations["Is pending"]},
                        {id: "voided", name: this.translations["Voided"]},
                        {id: "timeouted", name: this.translations["Timeouted"]},
                        {id: "cancelled", name: this.translations["Cancelled"]},
                        {id: "failed", name: this.translations["Failed"]},
                        {id: "refunded", name: this.translations["Is refunded"]},
                        {id: "chargebacked", name: this.translations["Is chargebacked"]},
                        {id: "paid", name:this.translations["Paid"]},
                        {id: "completed", name: this.translations["Is completed"]},
                        {id: "disputed", name: this.translations["Disputed"]},
                    ],
                    type: "select",
                    multiple: true,
                },
                {
                    key: "shipping",
                    label: this.translations["Shipping method"],
                    options: this.inIsNotOptions,
                    url: "/admin/api/shipping/name",
                    valueLabelIndicator: "name",
                    type: "select",
                    multiple: true,
                },
                {
                    key: "status_payment",
                    label: this.translations["Payment status"],
                    options: this.inIsNotOptions,
                    selectOptions: [
                        {id: "initiated", name: this.translations["Initiated"]},
                        {id: "pending", name: this.translations["Pending"]},
                        {id: "requested", name: this.translations["Requested"]},
                        {id: "held", name: this.translations["Held"]},
                        {id: "completed", name: this.translations["Completed"]},
                        {id: "failed", name: this.translations["Failed"]},
                        {id: "refunded", name: this.translations["Refunded"]},
                        {id: "voided", name: this.translations["Voided"]},
                        {id: "cancelled", name: this.translations["Cancelled"]},
                        {id: "timeouted", name:this.translations[ "Timeouted"]},
                        {id: "chargebacked", name: this.translations["Chargebacked"]},
                        {id: "disputed", name: this.translations["Disputed"]},
                    ],
                    type: "select",
                    multiple: true,
                },
                {
                    key: "payment",
                    label: this.translations["Payment providers"],
                    options: this.inIsNotOptions,
                    url: "/admin/payment-providers",
                    type: "select",
                    valueLabelIndicator: "map",
                    multiple: true,
                },
                {
                    key: "fast_order",
                    label: this.translations["Fast order"],
                    options: this.yesNoOptions,
                },
                {
                    key: "archived",
                    label: this.translations["Archived"],
                    options: this.yesNoOptions,
                },
                {
                    key: "customer_group",
                    label: this.translations["Customer group"],
                    url: "/admin/api/core/customers/groups",
                    type: "select",
                    multiple: true,
                },
                {
                    key: "discount",
                    label: this.translations["Discount"],
                    selectOptions: [
                        {id: "any", name: this.translations["Any Discount"]},
                        {id: "no", name: this.translations["No Discount"]},
                        {id: "flat", name: this.translations["Flat"]},
                        {id: "percent", name: this.translations["Percent"]},
                        {id: "shipping", name: this.translations["Shipping"]},
                        {id: "fixed", name: this.translations["Fixed"]},
                    ],
                    type: "select",
                    multiple: true,
                },
                {
                    key: "discount_code",
                    label: this.translations["Discount code"],
                    url: "/admin/api/core/discounts/codes",
                    type: "select",
                    valueLabelIndicator: "code",
                    multiple: true,
                },
                {
                    key: "status_fulfillment",
                    label: this.translations["Fulfillment status"],
                    options: this.isIsNotOptions,
                    selectOptions: [
                        {id: "fulfilled", name: this.translations["Fulfilled"]},
                        {id: "not_fulfilled", name: this.translations["Not Fulfilled"]},
                    ],
                    type: "select",
                },
                {
                    key: "date_added",
                    label: this.translations["Date"],
                    options: this.date,
                    type: "date"
                },
                {
                    key: "credit_note",
                    label: this.translations["Credit Note"],
                    options: this.yesNoOptions,
                },
                {
                    key: "invoice",
                    label: this.translations["Invoice"],
                    options: this.yesNoOptions,
                },
                {
                    key: "invoice_number",
                    label: this.translations["Invoice number"],
                    url: "/admin/api/core/orders/invoices",
                    valueLabelIndicator: "invoice_number",
                    type: "select",
                    multiple: true,
                },
                {
                    key: "recovered_source",
                    label: this.translations["Recovered Source"],
                    type: "string",
                },

                {
                    key: "made_through",
                    label: this.translations["Made through"],
                    type: "string",
                },
                {
                    key: "referer",
                    label: this.translations["Referer"],
                    type: "string",
                },
                {
                    key: "utm_source",
                    label: this.translations["UTM Source"],
                    type: "string",
                },
                {
                    key: "utm_medium",
                    label: this.translations["UTM Medium"],
                    type: "string",
                },
                {
                    key: "utm_campaign",
                    label: this.translations["UTM Campaign"],
                    type: "string",
                },
                {
                    key: "draft",
                    label: this.translations["Draft"],
                    options: this.yesNoOptions,
                },
                {
                    key: "is_admin",
                    label: this.translations["Created by admin"],
                    options: this.yesNoOptions,
                },
                {
                    key: "region",
                    label: this.translations["Region"],
                    url: "/admin/autocomplete/cities",
                    type: "select",
                    multiple: true,
                },
                {
                    key: "supplier",
                    label: this.translations["Supplier"],
                    options: this.inIsNotOptions,
                    url: "/admin/api/suppliers/list",
                    type: "select",
                    multiple: true,
                },
            ]
        },
        table() {
            return {
                data: this.tableData,
                defaultSorting: [{ key: "id", sortingMode: "desc" }],
                isLoading: this.tableLoading,
                columns: [
                    {
                        column: "id",
                        key: "id",
                        title: this.translations["Order ID"],
                        sortable: true,
                        component: markRaw(Order),
                    },
                    {
                        column: "shipping_address",
                        key: "shipping_address",
                        title: this.translations["Address"],
                        component: markRaw(Address),
                    },
                    {
                        column: "date_added",
                        key: "date_added",
                        title: this.translations["Date"],
                        sortable: true,
                        component: markRaw(Date),
                    },
                    {
                        column: "shipping_provider",
                        key: "shipping_provider",
                        title: this.translations["Fulfillment"],
                        sortable: false,
                    },
                    {
                        column: "shipping_date_delivery",
                        key: "shipping_date_delivery",
                        title: this.translations["Receiving"],
                        sortable: false,
                        component: markRaw(Date),
                    },
                    {
                        column: "status",
                        key: "status",
                        title: this.translations["Status"],
                        sortable: false,
                        component: markRaw(Status),
                    },
                    {
                        column: "price_total_input",
                        key: "price_total_input",
                        title: this.translations["Total price"],
                        sortable: false,
                        component: markRaw(Total),
                    },
                    {
                        column: "Actions",
                        key: "Actions",
                        title: " ",
                        component: markRaw(RowActions),
                    },
                ],
            };
        },
    },
    methods: {
        async getData() {
            try {
                this.tableLoading = true;

                this.model.where({
                    "filters[customer_id]": this.details.id,
                    ...this.query,
                });

                const response = await this.model.paginate(this.query?.page || this.page, this.query?.perpage || this.perPage);

                this.tableData = {
                    ...response,
                    data: response.data.map((order) => ({
                        ...order,
                        shipping_address: order.shipping_address || {},
                        deleteRow: this.deleteRow,
                        toggleModal: this.toggleModal,
                    })),
                };

                this.$router.push({
                    query: {
                        ...this.$route.query,
                        page: this.tableData.current_page,
                    },
                });
            } catch (error) {
                console.error("Error fetching data:", error);
                this.$errorResponse(error);
            } finally {
                this.tableLoading = false;
            }
        },

        async paginate(page) {
            this.page = page;
            await this.getData();
        },
    },
};
</script>
