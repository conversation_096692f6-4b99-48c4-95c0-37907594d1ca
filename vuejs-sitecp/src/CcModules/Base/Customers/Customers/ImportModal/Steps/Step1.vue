<template>
    <SettingsCard :bordered="true">
        <div
            class="d-flex align-items-center justify-content-between gap-3 w-100 flex-wrap"
        >
            <div class="d-flex flex-column gap-1">
                <p class="label-500-16px m-0">
                    {{ translations["Upload CSV file"] }}
                </p>
                <span class="text-400-secondary-16px">
                    {{ translations["Add your file with import data"] }}
                </span>
            </div>
            <a
                class="btn btn-white"
                :href="`${serverSettings(
                    'img_url'
                )}sitecp/docs/customer-template.csv`"
                :class="{ disabled: submitLoader }"
                download="customer_template.csv"
            >
                <i class="fa-light fa-file-arrow-down"></i>
                {{ translations["CSV Template"] }}
            </a>
        </div>
        <template v-if="!file">
            <Dropzone />
        </template>
        <div v-else class="valid-csv-file">
            <div class="file-name">
                <i class="fa-light fa-file-csv"></i>
                <span>{{ file.name }}</span>
            </div>
            <DeleteComponent
                :delete-function="() => (file = null)"
                :label="translations['Remove uploaded file?']"
                :confirm-button-text="translations.Remove"
                :disabled="submitLoader"
            >
                <template #action>
                    <i class="fa-light fa-trash-can remove-csv"></i>
                </template>
            </DeleteComponent>
        </div>
    </SettingsCard>
    <SettingsCard
        :bordered="true"
        :title="translations['CSV file settings']"
        classes="mt-3"
    >
        <ActiveSwitch
            v-model:is-active="config.has_header_line"
            :label-text="
                translations[
                    'Check this if your file has a header line explaining the columns'
                ]
            "
            :is-disabled="submitLoader"
        />
        <SelectWithAjax
            v-model:val="config.customer_group_id"
            :label="translations['Customer group']"
            :searchable="true"
            :request-on-open="true"
            :request-on-search="true"
            api-url="/admin/api/core/customers/groups"
            :no-margin="true"
            :cols-width="4"
            :is-select-disabled="submitLoader"
        />
    </SettingsCard>
</template>
<script>
import useSharedImportModalState from "../js/useSharedImportModalState";

import SettingsCard from "@components/SettingsForm/Helpers/SettingsCard";
import Dropzone from "../Helpers/Dropzone";
import DeleteComponent from "@components/DeleteComponent";
import ActiveSwitch from "@components/Form/ActiveSwitch";
import SelectWithAjax from "@components/Form/SelectWithAjax";

export default {
    name: "Step1",
    components: {
        SettingsCard,
        Dropzone,
        DeleteComponent,
        ActiveSwitch,
        SelectWithAjax,
    },
    props: {
        model: {
            type: Object,
            default: () => {},
        },
        type: {
            type: String,
            default: "products",
        },
    },
    setup() {
        const {
            file,
            meta,
            config,
            submitLoader,
            cc2FaModal,
            isDownload,
            code,
        } = useSharedImportModalState();

        return {
            file,
            meta,
            config,
            submitLoader,
            cc2FaModal,
            isDownload,
            code,
        };
    },
    data() {
        return {
            translations: {
                "Add your file with import data": this.$t(
                    "Add your file with import data"
                ),
                "Upload file": this.$t("Upload file"),
                "Upload CSV file": this.$t("Upload CSV file"),
                "CSV Template": this.$t("CSV Template"),
                "Remove uploaded file?": this.$t("Remove uploaded file?"),
                Remove: this.$t("Remove"),
                "Customer group": this.$t("Customer group"),
                "Check this if your file has a header line explaining the columns":
                    this.$t(
                        "Check this if your file has a header line explaining the columns"
                    ),
                "CSV file settings": this.$t("CSV file settings"),
            },
        };
    },
    methods: {
        startDownload() {
            if (!this.code) {
                this.cc2FaModal = true;
                this.isDownload = true;
            }
        },
    },
};
</script>
<style lang="scss">
.valid-csv-file {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 3rem;
    width: 100%;
    border-radius: 6px;
    background: var(--Color-Surface-Neutral---cc-color-bg-light, #fafaff);
    height: 60px;
    padding: 16px;

    .file-name {
        display: flex;
        align-items: center;
        gap: 1rem;

        span {
            color: var(--Color-Text-Body---cc-color-text-accent, #686b8b);
            font-family: Roboto;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 24px;
        }

        i {
            color: var(--Color-Text-Body---cc-color-text-accent, #686b8b);
            font-size: 24px;
            font-style: normal;
            font-weight: 300;
            line-height: 24px;
        }
    }
    .remove-csv {
        color: var(--Color-Text-Body---cc-color-text-error, #fc4f4e);
        text-align: center;
        font-size: 18px;
        font-style: normal;
        font-weight: 300;
        line-height: 20px;
    }
}
</style>