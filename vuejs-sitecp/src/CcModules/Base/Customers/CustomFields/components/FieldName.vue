<template>
    <a
        href="javascript:void(0);"
        class="cc-tooltip-dotted"
        @click="() => column.openModal(data.id)"
    >
        {{ data.name }}
    </a>
</template>
<script>
export default {
    name: "FieldName",
    props: {
        data: {
            type: Object,
            default: () => ({}),
        },
        column: {
            type: Object,
            default: () => ({}),
        },
    },
};
</script>