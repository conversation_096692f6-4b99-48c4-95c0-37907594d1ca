<template>
    <b-card class="card-shadow mx-2">
        <b-row>
            <b-col class="col-12 d-flex align-items-center justify-content-between">
                <span class="label-400-16px m-0">
                    {{ translations["Products price"] }}
                </span>
                <span class="label-500-16px m-0">
                    {{ moneyFormat(total) }}
                </span>
            </b-col>
            <b-col class="col-12">
                <hr class="my-3 mx-0" />
            </b-col>
            <b-col class="col-12 col-md-6">
                <SelectWithAjax
                    v-model:val="settings.variant.type"
                    :options="type_options"
                    :label="translations['Apply discount to the bundle']"
                    :is-select-disabled="disabled"
                    :can-clear="false"
                    :column-style="true"
                    :cols-width="12"
                    selectClasses=""
                />
            </b-col>
            <b-col class="col-12 col-md-6 d-flex align-items-end">
                <component
                    :is="unitComponent"
                    v-model="totalVariantAmount"
                    :column-style="true"
                    :label-text="translations['Total amount']"
                    :no-margin="true"
                    :digits="2"
                    :readonly="disabled"
                />
            </b-col>
        </b-row>
    </b-card>
</template>

<script>
import SelectWithAjax from "@components/Form/SelectWithAjax";
import InputComponent from "@components/Form/InputComponent";
import PercentComponent from "@components/Form/PercentComponent";
import CurrencyComponent from "@components/Form/CurrencyComponent";

export default {
    name: "Total",

    components: {
        SelectWithAjax,
        InputComponent,
        PercentComponent,
        CurrencyComponent,
    },
    props: {
        modelValue: {
            type: Object,
            default: {},
        },
    },
    data() {
        return {
            settings: this.modelValue,
            translations: {
                "Products price": this.$t("Products price"),
                Price: this.$t("Price"),
                Percent: this.$t("Percent"),
                "Apply discount to the bundle": this.$t("Apply discount to the bundle"),
            },
        };
    },
    computed: {
        unitComponent() {
            switch (this.settings.variant.type) {
                case "percent":
                    return PercentComponent;
                case "price":
                    return CurrencyComponent;
                default:
                    return InputComponent;
            }
        },
        unit() {
            return this.settings.variant.type === "percent"
                ? "%"
                : this.serverSettings("currency.sign");
        },
        type_options() {
            return [
                { id: "price", name: this.translations["Price"] },
                { id: "percent", name: this.translations["Percent"] },
            ];
        },
        total() {
            let result = 0;

            this.settings.bundle_products.forEach((row) => {
                let price = row.individual_price_enabled
                    ? row.individual_price
                    : row.product.price_to;
                let qty = row.individual_qty_enabled ? row.qty : 1;

                result += parseFloat(price) * qty;
            });

            return result;
        },
        disabled() {
            if (
                this.settings.bundle_products &&
                Array.isArray(this.settings.bundle_products) &&
                this.settings.bundle_products.length > 0
            ) {
                return this.settings.bundle_products.some((row) => {
                    return row.individual_price_enabled;
                });
            }
            return false;
        },
        totalVariantAmount: {
            get() {
                return this.disabled ? this.total : this.settings.variant[this.settings.variant.type]
            },
            set(value) {
                if(!this.disabled) {
                    this.settings.variant[this.settings.variant.type] = value
                }
            }
        }
    },
    watch: {
        settings: {
            deep: true,
            handler(val) {
                this.$emit("update:modelValue", val);
            },
        },
        modelValue: {
            deep: true,
            handler(val) {
                this.settings = val;
            },
        },
        disabled(val) {
            if(val) {
                this.settings.variant.type = 'price'
            }
        }
    },
    emits: ["update:modelValue"],
};
</script>
<style>
.card-shadow {
    box-shadow: 0px -1px 15px 0px rgba(104, 107, 139, 0.25);
}
</style>
