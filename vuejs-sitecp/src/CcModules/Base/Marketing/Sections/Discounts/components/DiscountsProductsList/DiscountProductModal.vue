<template>
    <b-modal
        v-model="productsModal"
        :dialog-class="different_prices === 'multiple' ? 'xll' : ''"
        :no-close-on-backdrop="submitLoader"
        :no-footer="true"
        body-class="edit-settings-modal-content"
        class="modal-right"
        header-class="edit-settings-modal-header"
        size="lg"
    >
        <template #header>
            <div class="d-flex align-items-center gap-2">
                <h5
                    class="settings-modal-title"
                    v-html="
                        data.id
                            ? translations['Edit product']
                            : translations['Add product']
                    "
                ></h5>
            </div>
            <div class="d-flex justify-content-end align-items-center gap-2">
                <button
                    :disabled="submitLoader"
                    class="btn btn-white"
                    type="button"
                    @click="productsModal = false"
                    v-html="translations['Close']"
                ></button>
                <button
                    :disabled="submitLoader || !product_id"
                    class="btn btn-primary"
                    type="button"
                    @click="createOrEdit"
                >
                    <b-spinner v-if="submitLoader" small></b-spinner>
                    {{ translations["Save"] }}
                </button>
            </div>
        </template>
        <Loading
            v-if="isLoading"
            :loading="isLoading"
            class="app-loader-center"
        ></Loading>
        <div v-else>
            <b-card>
                <b-row class="gy-3">
                    <b-col v-if="data.name" class="col-12 d-flex align-items-center gap-3">
                        <img
                            :src="
                            product?.image?.link['150x150'] ||
                            serverSettings('noImages.150x150')
                        "
                            alt="logo"
                            style="max-width: 80px"
                        />
                        <h3 v-html="product.name"></h3>
                    </b-col>
                    <b-col v-else class="col-12">
                        <SelectWithAjax
                            v-model:val="product_id"
                            :can-clear="false"
                            :cols-width="12"
                            :column-style="true"
                            :filterResults="false"
                            :label="translations['Product']"
                            :no-margin="true"
                            :request-on-search="true"
                            :searchable="true"
                            api-url="/admin/api/core/products/search"
                        />
                    </b-col>
                    <b-col v-if="differentPricesSupported" class="col-12">
                        <RadioComponent
                            v-model="different_prices"
                            :options="radioOptions"
                            :selected="type"
                            :stacked="false"
                            class="radio"
                            name="type"
                        />
                    </b-col>
                    <b-col v-if="product?.id || isLoadingProduct" class="col-12">
                        <Vue3SlideUpDown
                            :duration="130"
                            :model-value="isLoadingProduct"
                            class="d-flex align-items-center justify-content-center"
                        >
                            <Loading
                                :loading="isLoadingProduct"
                                class="py-5"
                            ></Loading>
                        </Vue3SlideUpDown>
                        <Vue3SlideUpDown
                            :duration="130"
                            :model-value="!isLoadingProduct"
                        >
                            <data-table
                                v-if="!differentPricesSupported"
                                :enable-mobile="true"
                                :openByDefault="true"
                                v-bind="tableSimple"
                            />
                            <data-table
                                v-else-if="differentPricesSupported && different_prices === 'single'"
                                :enable-mobile="true"
                                :openByDefault="true"
                                v-bind="tableMultipleCommonPrice"
                            />
                            <data-table
                                v-else-if="differentPricesSupported && different_prices === 'multiple'"
                                :enable-mobile="true"
                                :openByDefault="true"
                                v-bind="tableMultipleMultiplePrice"
                            />
                        </Vue3SlideUpDown>

                    </b-col>
                </b-row>
            </b-card>
        </div>
    </b-modal>
</template>

<script>
import {RequestModel} from "../../js/Discounts.js";
import {markRaw} from "vue";
import {toast} from "@js/toast";
import Loading from "@components/Loading";
import useSharedDiscounts from './../../js/useSharedDiscounts.js'
import {useWindowSize} from "@vueuse/core";
import {buildFormData} from "@js/shippingHelpers.js";


import RadioComponent from "@components/Form/RadioComponent.vue";
import SelectWithAjax from "@components/Form/SelectWithAjax.vue";
import DataTable from "@components/Table";

import ProductPriceInStore from './Helpers/ModalComponents/ProductPriceInStore.vue'
import ProductSupplierPriceField from './Helpers/ModalComponents/ProductSupplierPriceField.vue'
import ProductOfDiscountName from './Helpers/ModalComponents/ProductOfDiscountName.vue'
import ProductVariantOptions from './Helpers/ModalComponents/ProductVariantOptions.vue'
import {Vue3SlideUpDown} from "vue3-slide-up-down";

export default {
    name: "DiscountProductModal",
    components: {
        Vue3SlideUpDown,
        SelectWithAjax,
        RadioComponent,
        Loading,
        DataTable,
    },
    setup() {
        const {productsModal} = useSharedDiscounts()
        const {width} = useWindowSize();

        return {
            productsModal,
            width,
        }
    },
    data() {
        return {
            translations: {
                Save: this.$t("Save"),
                Close: this.$t("Close"),
                Product: this.$t("Product"),
                "Add product": this.$t("Add product"),
                "Edit product": this.$t("Edit product"),
                "Saved successfully": this.$t("Saved successfully"),
                "Error occurred while saving": this.$t("Error occurred while saving"),
                "Common price": this.$t("Common price"),
                "Multiple price": this.$t("Multiple price"),
                Identifier: this.$t("Identifier"),
                "Supplier price": this.$t("Supplier price"),
                "Price in store": this.$t("Price in store"),
                "New price": this.$t("New price"),
            },
            responseErrors: {},
            submitLoader: false,
            data: {},
            product_id: null,
            product: {},
            type: "",
            variants: [],
            different_prices: "single",
            isLoading: false,
            isLoadingProduct: false
        };
    },
    computed: {
        commonColumns() {
            return [
                {
                    column: "price_in_store",
                    key: "price_in_store",
                    sortable: false,
                    title: this.translations["Price in store"],
                    component: markRaw(ProductPriceInStore),
                },
                {
                    column: "discounted_price",
                    key: "discounted_price",
                    sortable: false,
                    title: this.translations["New price"],
                    component: markRaw(ProductSupplierPriceField),
                },
            ];
        },
        tableSimple() {
            return {
                data: {
                    data: [this.product?.variants?.[0] || []],
                },
                paginationShowDisabled: true,
                columns: [
                    ...(this.width <= 768
                        ? [
                            {
                                column: "name",
                                key: "name",
                                sortable: false,
                                isMain: true,
                                title: " ",
                                product: this.product,
                                component: markRaw(ProductOfDiscountName),
                            },
                        ]
                        : []),
                    ...this.commonColumns,
                ],
            };
        },
        tableMultipleMultiplePrice() {
            return {
                data: {
                    data:
                        this.different_prices === "multiple"
                            ? this.product?.variants || []
                            : [this.product?.variants?.[0] || {}],
                },
                paginationShowDisabled: true,
                columns: [
                    {
                        column: "options",
                        key: "options",
                        sortable: false,
                        isMain: true,
                        title: this.translations["Variant options"],
                        component: markRaw(ProductVariantOptions),
                    },
                    ...this.commonColumns,
                ],
            };
        },
        tableMultipleCommonPrice() {
            return {
                data: {
                    data:
                        this.different_prices === "multiple"
                            ? this.product?.variants || []
                            : [this.product?.variants?.[0] || {}],
                },
                paginationShowDisabled: true,
                columns: [
                    ...(this.width <= 768
                        ? [
                            {
                                column: "name",
                                key: "name",
                                sortable: false,
                                isMain: true,
                                title: " ",
                                product: this.product,
                                component: markRaw(ProductOfDiscountName),
                            },
                        ]
                        : []),
                    ...this.commonColumns,
                ],
            };
        },
        radioOptions() {
            return [
                {value: "single", text: this.translations["Common price"]},
                {value: "multiple", text: this.translations["Multiple price"]},
            ];
        },
        differentPricesSupported() {
            return this.product?.variants?.length > 1;
        },
    },
    methods: {
        async createOrEdit() {
            this.submitLoader = true;

            let body = this.product.variants.map(x => {
                return {
                    product_id: this.product.id,
                    variant_id: x.id,
                    price: parseFloat(x.discount_price) * 100
                }
            })


            try {
                let model = new RequestModel(`/admin/api/core/discounts/products/${this.$route.params.id}`);
                await model.create(body)

            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.submitLoader = false;
            }
        },
        async getProductInfo() {
            this.isLoadingProduct = true;

            try {

                let model = new RequestModel('/admin/api/core/products')

                const response = await model.find(this.product_id)
                this.product = response;
                this.different_prices = this.product?.variants?.length > 1
                    ? "multiple"
                    : "single";

            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.isLoadingProduct = false;
            }
        },
    },
    watch: {
        productsModal(newVal) {
            if (!newVal) {
                this.data = {};
                this.product = {};
                this.product_id = null;
                this.different_prices = "single";
                this.responseErrors = {};
            }
        },
        product_id: async function (newVal) {
            if (newVal) {
                await this.getProductInfo();
            }
        },
    },
};
</script>
