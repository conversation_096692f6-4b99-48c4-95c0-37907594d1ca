export default {
    props: {
        modelValue: {
            type: Object,
            default: {}
        },
        responseErrors: {
            type: Object,
            default: {}
        },
        translationLabels: {
            type: Object,
            default: {}
        },
        hideTimer: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        settings: {
            get() {
                return this.modelValue;
            },
            set(value) {
                this.$emit('update:modelValue', value);
            }
        }
    },
    emits: ['update:modelValue'],
}
