<template>
    <b-modal
        :id="this.$route.name.includes('category') ? 'cat-modal' : undefined"
        v-model="modal"
        :no-close-on-backdrop="true"
        body-class="edit-settings-modal-content"
        class="modal-right"
        header-class="edit-settings-modal-header"
        :no-footer="true"
        :size="cloudioModal ? 'xll' : 'xl'"
    >
        <template #header>
            <div class="d-flex align-items-center gap-2">
                <h5 class="settings-modal-title" v-html="label"></h5>
            </div>
            <div class="d-flex justify-content-end align-items-center gap-2">
                <button
                    :disabled="loading"
                    class="btn btn-white"
                    type="button"
                    @click="modal = false"
                    v-html="translations['Close']"
                ></button>
                <button
                    :disabled="submitLoader || loading"
                    class="btn btn-primary"
                    type="button"
                    @click="saveData"
                >
                    <b-spinner v-if="submitLoader" small></b-spinner>
                    {{ translations["Save"] }}
                </button>
            </div>
        </template>
        <Loading v-if="loading" :loading="loading" class="app-loader-center"/>
        <template v-else-if="!loading && modal">
            <SettingsForm>
                <DefaultLayout>
                    <template #general>
                        <SettingsCard
                            :title="translations['General settings']"
                            class="mb-3"
                        >
                            <InputComponent
                                v-model="item.name"
                                :column-style="true"
                                :error="
                                    translations[responseErrors['name']] ||
                                    responseErrors['name']
                                "
                                :fosuc-on-mount="true"
                                :label-text="translations['Category name']"
                                :no-margin="true"
                            />
                            <div>
                                <TextEditor
                                    v-model:value="item.description"
                                    :error="
                                        translations[
                                            responseErrors['description']
                                        ] || responseErrors['description']
                                    "
                                    :label-text="translations['Description']"
                                />
                                <ShopperPenCategoryPanel
                                    v-if="skill?.isActive"
                                    v-model="cloudioModal"
                                    :category-id="item.id"
                                    :categoryDescription="true"
                                    @update-description="(value) => item.description = value"
                                    @save-category="() =>{ saveData(); tryGenerate = true;}"
                                />
                            </div>
                            <SelectWithAjax
                                v-model:val="item.parent_id"
                                :cols-width="12"
                                :column-style="true"
                                :error="
                                    translations[responseErrors['parent_id']] ||
                                    responseErrors['parent_id']
                                "
                                :label="translations['Parent category']"
                                :options="categories"
                                :placeholder="
                                    translations['Select parent category']
                                "
                                :searchable="true"
                                select-classes=""
                            >
                                <span
                                    class="text-400-secondary-12px d-block ps-1 pt-1"
                                >
                                    {{
                                        translations[
                                            "If this record is a subcategory, simply choose a parent category from the list"
                                            ]
                                    }}
                                </span>
                            </SelectWithAjax>
                            <ActiveSwitch
                                v-model:is-active="item.display_child"
                                :label-text="
                                    translations['Display subcategories']
                                "
                                :reverse="true"
                            />
                        </SettingsCard>
                    </template>
                </DefaultLayout>
            </SettingsForm>

            <LogoSection
                v-model:has_image="has_image"
                v-model:image="item.image"
                :default-image="this.serverSettings('noImage.150x150')"
                :error="
                    translations[responseErrors['image']] ||
                    responseErrors['image']
                "
                :is-card="true"
                :label="translations['Category image']"
                :url="`/admin/api/core/product-categories/${item.id}/delete-image`"
                container-classes="mb-3"
                @delete:image="
                    () => {
                        has_image = falseValue;
                        item.image = null;
                    }
                "
            />

            <TaxonomyTemplate
                v-model:taxonomy_id="item.taxonomy_id"
                :options="taxonomy_categories"
                :response-errors="responseErrors"
            />
            <SettingsForm>
                <DefaultLayout>
                    <template #general>
                        <SettingsCard
                            :title="translations['Cart and checkout rules']"
                            class="mt-3"
                        >
                            <ActiveSwitch
                                v-model:is-active="item.all_payment"
                                :false-value="1"
                                :label-text="
                                    translations[
                                        'Define custom payment methods for this category'
                                    ]
                                "
                                :true-value="0"
                            />
                            <Vue3SlideUpDown
                                :duration="180"
                                :model-value="item.all_payment == 0"
                            >
                                <div
                                    class="alert-warning-yellow d-flex flex-column align-items-start gap-2 mb-3"
                                >
                                    <p
                                        class="text-400-secondary m-0"
                                        v-html="
                                            translations[
                                                'Important! If you set a restriction to this category by payment method, and if a customer has at least one product from this category in his cart, the system will provide him with only the selected payment method option.'
                                            ]
                                        "
                                    ></p>
                                </div>
                                <!-- api-url="/admin/payment-providers" -->
                                <SelectWithAjax
                                    v-model:val="item.payment"
                                    :cols-width="12"
                                    :column-style="true"
                                    :error="
                                        translations[
                                            responseErrors['payment']
                                        ] || responseErrors['payment']
                                    "
                                    :options="payment_providers"
                                    :placeholder="
                                        translations['Select payment methods']
                                    "
                                    :request-on-search="true"
                                    :searchable="true"
                                    mode="tags"
                                    select-classes=""
                                />
                            </Vue3SlideUpDown>
                            <hr/>

                            <ActiveSwitch
                                v-model:is-active="item.all_shipping"
                                :false-value="1"
                                :label-text="
                                    translations[
                                        'Define custom shipping methods for this category'
                                    ]
                                "
                                :true-value="0"
                            />
                            <Vue3SlideUpDown
                                :duration="180"
                                :model-value="item.all_shipping == 0"
                            >
                                <div
                                    class="alert-warning-yellow d-flex flex-column align-items-start gap-2 mb-3"
                                >
                                    <p
                                        class="text-400-secondary m-0"
                                        v-html="
                                            translations[
                                                'Important! If you set a restriction to this category by delivery method, and if a customer has at least one product from this category in his cart, the system will provide him with only the selected product as a delivery option.'
                                            ]
                                        "
                                    ></p>
                                </div>
                                <!-- api-url="/admin/api/shipping" -->
                                <SelectWithAjax
                                    v-model:val="item.shipping"
                                    :cols-width="12"
                                    :column-style="true"
                                    :error="
                                        translations[
                                            responseErrors['shipping']
                                        ] || responseErrors['shipping']
                                    "
                                    :options="shipping_providers"
                                    :placeholder="
                                        translations['Select shipping methods']
                                    "
                                    :request-on-search="true"
                                    :searchable="true"
                                    mode="tags"
                                    select-classes=""
                                />
                            </Vue3SlideUpDown>
                        </SettingsCard>
                        <SettingsCard v-if="item.enabled_delivery_time">
                            <span
                                class="text-400-secondary"
                                v-html="
                                    translations[
                                        'Set the delivery time required for shipping the product, which may affect the availability of delivery slots.'
                                    ]
                                "
                            ></span>
                            <InputNumberComponent
                                v-model="item.make_interval"
                                :column-size="4"
                                :error="
                                    translations[
                                        responseErrors['make_interval']
                                    ] || responseErrors['make_interval']
                                "
                                :label-text="
                                    translations[
                                        'Technological delivery time in hours'
                                    ]
                                "
                                :min="0"
                                :no-margin="true"
                                :show-increment-decrement="false"
                                :step="1"
                            />
                        </SettingsCard>
                    </template>
                    <template #advanced>
                        <SettingsCard
                            :collapsible="true"
                            :title="translations['Advanced settings']"
                        >
                            <InputComponent
                                v-model="item.seo_title"
                                :column-size="6"
                                :column-style="false"
                                :error="
                                    translations[responseErrors['seo_title']] ||
                                    responseErrors['seo_title']
                                "
                                :label-text="translations['SEO page title']"
                                :no-margin="true"
                            />
                            <InputComponent
                                v-model="item.url_handle"
                                :colored="true"
                                :column-size="12"
                                :column-style="true"
                                :error="
                                    translations[
                                        responseErrors['url_handle']
                                    ] || responseErrors['url_handle']
                                "
                                :label-text="translations['URL']"
                                :no-margin="true"
                                :unit="`${this.serverSettings(
                                    'host'
                                )}/category/`"
                                unit_position="left"
                                @blur="handleSlugify"
                            />

                            <InputComponent
                                v-model="item.seo_description"
                                :error="
                                    translations[
                                        responseErrors['seo_description']
                                    ] || responseErrors['seo_description']
                                "
                                :label-text="
                                    translations['SEO meta description']
                                "
                                :no-margin="true"
                            />
                            <SeoGooglePreview
                                :description="item.seo_description"
                                :label-text="translations['Google preview']"
                                :no-margin="true"
                                :title="item.seo_title"
                                :url="`${this.serverSettings('host')}/category/${
                                    item.url_handle
                                }`"
                            />
                        </SettingsCard>
                    </template>
                </DefaultLayout>
            </SettingsForm>
        </template>
    </b-modal>
</template>

<script>
import _ from "lodash";
import {buildFormData} from "@js/shippingHelpers.js";
import useSharedState from './../../js/useSharedState.js'
import EventBus from '@js/eventBus';
import {toast} from "@js/toast";
import useSharedAppsInfo     from "../../../../Apps/composables/useSharedAppsInfo";

import InputComponent from "@components/Form/InputComponent";
import InputNumberComponent from "@components/Form/InputNumberComponent";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import TextEditor from "@components/Form/TextEditor";
import ActiveSwitch from "@components/Form/ActiveSwitch";
import LogoSection from "@components/Form/LogoSection";

import TaxonomyTemplate from "./../helpers/TaxonomyTemplate";

import SettingsForm from "@components/SettingsForm/SettingsForm";
import SettingsCard from "@components/SettingsForm/Helpers/SettingsCard";
import DefaultLayout from "@components/SettingsForm/Layouts/DefaultLayout";
import {Vue3SlideUpDown} from "vue3-slide-up-down";
import SeoGooglePreview from "@components/SeoGooglePreview";
import slugify from '@js/slugify';

import ShopperPenCategoryPanel from './../../../../Cloudio/components/Panel/ShopperPenCategoryPanel.vue'

import Loading from "@components/Loading";
import axios from "axios";

export default {
    name: "CreateOrEditModal",
    components: {
        TaxonomyTemplate,
        SettingsForm,
        SettingsCard,
        DefaultLayout,
        InputComponent,
        TextEditor,
        SelectWithAjax,
        ActiveSwitch,
        LogoSection,
        Vue3SlideUpDown,
        SeoGooglePreview,
        Loading,
        InputNumberComponent,
        ShopperPenCategoryPanel
    },
    props: {
        modelValue: {
            type: Boolean,
            required: true,
        },
        model: {
            type: Object,
            required: true,
        },
        prefillName: {
            type: String,
            default: "",
        },
    },
    setup() {
        const {dataRow} = useSharedState();
        const {apps} = useSharedAppsInfo()

        return {dataRow, apps};
    },
    data() {
        return {
            modal: false,
            submitLoader: false,
            responseErrors: {},
            loading: true,
            tryGenerate: false,
            skill:{},
            cloudioModal: false,

            shipping_providers: [],
            payment_providers: [],
            google_taxonomy: [],
            taxonomy_categories: [],
            has_image: false,
            categories: [],

            item: {
                all_payment: 1,
                all_shipping: 1,
                enabled_delivery_time: false,
                seo_title: "",
                taxonomy_id: "null",
                url_handle: "",
                seo_description: "",
                make_interval: 0,
                parent_id: "",
                description: "",
                name: this.prefillName,
                display_child: 0,
            },

            translations: {
                Close: this.$t("Close"),
                Save: this.$t("Save"),
                Description: this.$t("Description"),
                "Parent category": this.$t("Parent category"),
                "Saved successfully": this.$t("Saved successfully"),
                "Add new category": this.$t("Add new category"),
                "Edit - {name}": this.$t("Edit - {name}"),
                "General settings": this.$t("General settings"),
                "Category name": this.$t("Category name"),
                "If this record is a subcategory, simply choose a parent category from the list":
                    this.$t(
                        "If this record is a subcategory, simply choose a parent category from the list"
                    ),
                "Display subcategories": this.$t("Display subcategories"),
                "Select parent category": this.$t("Select parent category"),
                "Category image": this.$t("Category image"),
                "Cart and checkout rules": this.$t("Cart and checkout rules"),
                "Define custom payment methods for this category": this.$t(
                    "Define custom payment methods for this category"
                ),
                "Select payment methods": this.$t("Select payment methods"),
                "Select shipping methods": this.$t("Select shipping methods"),
                Any: this.$t("Any"),
                "Important! If you set a restriction to this category by payment method, and if a customer has at least one product from this category in his cart, the system will provide him with only the selected payment method option.":
                    this.$t(
                        "Important! If you set a restriction to this category by payment method, and if a customer has at least one product from this category in his cart, the system will provide him with only the selected payment method option."
                    ),
                "Define custom shipping methods for this category": this.$t(
                    "Define custom shipping methods for this category"
                ),
                "Important! If you set a restriction to this category by delivery method, and if a customer has at least one product from this category in his cart, the system will provide him with only the selected product as a delivery option.":
                    this.$t(
                        "Important! If you set a restriction to this category by delivery method, and if a customer has at least one product from this category in his cart, the system will provide him with only the selected product as a delivery option."
                    ),
                "Technological delivery time in hours": this.$t(
                    "Technological delivery time in hours"
                ),
                "Advanced settings": this.$t("Advanced settings"),
                "SEO page title": this.$t("SEO page title"),
                "Enter unique SEO page title": this.$t(
                    "Enter unique SEO page title"
                ),
                URL: this.$t("URL"),
                "SEO meta description": this.$t("SEO meta description"),
                "Enter unique SEO meta description": this.$t(
                    "Enter unique SEO meta description"
                ),
                "Google preview": this.$t("Google preview"),
                "Set the delivery time required for shipping the product, which may affect the availability of delivery slots.":
                    this.$t(
                        "Set the delivery time required for shipping the product, which may affect the availability of delivery slots."
                    ),

                //error msgs
                "The taxonomy id field is required.": this.$t(
                    "The taxonomy id field is required."
                ),
                "The name field is required.": this.$t(
                    "The name field is required."
                ),
                "The Technological delivery time in hours field is required.":
                    this.$t(
                        "The Technological delivery time in hours field is required."
                    ),
                "The payment field is required when all payment is 0.": this.$t(
                    "The payment field is required when all payment is 0."
                ),
                "The shipping field is required when all shipping is 0.":
                    this.$t(
                        "The shipping field is required when all shipping is 0."
                    ),
                "Category name is already taken!": this.$t(
                    "Category name is already taken!"
                ),
            },
        };
    },
    methods: {
        handleSlugify() {
            this.item.url_handle = slugify(this.item.url_handle)
        },
        async checkIfSkillIsEnabled(){
            const response = await axios.get("/admin/api/cloudio/skills");
            this.skill = response.data.find(
                (skill) => skill.key === "shopper_pen_category"
            );
        },
        async getData(id = null) {
            this.loading = true;
            try {
                let response = await this.model.find(
                    id ? `${id}?for_update=1` : "create"
                );
                const {
                    categories,
                    google_taxonomy,
                    payment_providers,
                    shipping_providers,
                    taxonomy_categories,
                    restrictions,
                    regex,
                    ...rest
                } = response;

                this.item = rest;
                if (!id && this.prefillName) {
                    this.item.name = this.prefillName;
                }
                if (!this.item.make_interval) {
                    this.item.make_interval = 0;
                }

                if (!this.item.hasOwnProperty("display_child")) {
                    this.item.display_child = 0;
                }
                this.item.image =
                    this.item.img_src ||
                    this.serverSettings("noImages.150x150");

                this.has_image = Boolean(!this.item.image.includes("no-image"));

                this.categories = categories || [];
                this.taxonomy_categories = taxonomy_categories;
                this.payment_providers = (payment_providers || []).map((x) => ({
                    id: x.provider,
                    name: x.title,
                }));
                this.shipping_providers = shipping_providers;

                if (restrictions && restrictions.shipping) {
                    this.item.shipping =
                        Object.keys(restrictions.shipping) || [];
                    this.item.all_shipping = 0;
                } else {
                    this.item.shipping = [];
                    this.item.all_shipping = 1;
                }

                if (restrictions && restrictions.payment) {
                    this.item.payment = Object.keys(restrictions.payment) || [];
                    this.item.all_payment = 0;
                } else {
                    this.item.payment = [];
                    this.item.all_payment = 1;
                }
                await this.checkIfSkillIsEnabled()
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.loading = false;
            }
        },
        async saveData() {
            this.submitLoader = true;

            this.payload = _.clone(this.item);

            if (this.payload.all_payment == 1) {
                this.payload.payment = [];
            }

            if (this.payload.all_shipping == 1) {
                this.payload.shipping = [];
            }

            const formData = new FormData();

            buildFormData(formData, this.payload);

            if (this.payload.image instanceof File) {
                formData.set("image", this.payload.image);
            } else {
                formData.delete("image");
            }

            try {
                const response = this.item.id
                    ? await this.model.post(this.item.id, formData, {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    })
                    : await this.model.create(formData, {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    });
                this.$emit("success", response, this.item.id ? true : false);
                EventBus.emit("category-edit", {response, update: this.item.id ? true : false});
                this.item.id = response.id;

                if(this.tryGenerate){
                    this.cloudioModal = true;
                }
                if(!this.skill.isActive || !this.tryGenerate){
                    this.modal = false;
                }
                toast.success(this.translations['Saved successfully']);
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.submitLoader = false;
            }
        },
    },
    computed: {
        isCloudioInstalled(){
            return (this.apps || []).find((app) => app.key === 'cloudio' && app.is_installed)
        },
        label() {
            if (this.dataRow) {
                return this.dataRow.name
                    ? this.$trp(this.translations["Edit - {name}"], {
                        name: this.dataRow.name,
                    })
                    : this.translations["Edit your category"];
            }
            return this.dataRow
                ? this.$trp(this.translations["Edit - {name}"], {
                    name: this.dataRow.name,
                })
                : this.translations["Add new category"];
        },
    },
    watch: {
        modelValue(val) {
            if (val) {
                this.getData(this.dataRow ? this.dataRow.id : null);
            }
            this.modal = val;
        },
        modal(val) {
            if (!val) {
                this.dataRow = null;
                this.$emit("update:modelValue", val);
                this.$emit("update:prefillName", "");
                this.responseErrors = {};
                this.item = {};
                this.shipping_providers = [];
                this.payment_providers = [];
                this.google_taxonomy = [];
                this.taxonomy_categories = [];
                this.loading = true;
                this.item = {
                    payment_provider: 1,
                    shipping_provider: 1,
                    shipping: [],
                    payment: [],
                    make_interval: 0,
                };
                this.tryGenerate = false;
            }
        },
    },
    emits: [
        "update:modelValue",
        "success-taxonomy",
        "success",
        "update:prefillName",
    ],
};
</script>
<style>
#cat-modal {
    z-index: 1057 !important;
}
</style>
