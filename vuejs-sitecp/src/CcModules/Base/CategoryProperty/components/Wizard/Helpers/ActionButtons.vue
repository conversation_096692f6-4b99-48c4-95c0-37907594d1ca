<template>
    <hr />
    <div class="d-flex justify-content-end align-items-center gap-2 w-100">
        <template v-if="modelValue !== 'success'">
            <button
                @click="navigateBack"
                class="btn btn-ghost"
                :style="{
                    opacity: ['category', 'values'].includes(modelValue) ? 1 : 0,
                    pointerEvents: ['category', 'values'].includes(modelValue)
                        ? 'auto'
                        : 'none',
                }"
            >
                <i class="fa-solid fa-chevron-left"></i>
                {{ translations["Back"] }}
            </button>
            <button @click="submit" class="btn btn-primary" :disabled="submitLoader">
                <b-spinner small v-if="submitLoader"></b-spinner>
                {{ translations[modelValue !== "values" ? "Next" : "Save"] }}
            </button>
        </template>
        <button v-else class="btn btn-ghost" @click="$emit('update:modal', false)">
            {{ translations["Close"] }}
        </button>
    </div>
</template>
<script>
export default {
    name: "ActionButtons",
    props: {
        modelValue: {
            type: String,
            default: "property",
        },
        submit: {
            type: Function,
            required: true,
        },
        steps: {
            type: Array,
            default: () => [],
        },
        submitLoader: {
            type: Boolean,
            default: false,
        },
        categories: {
            type: Array,
            default: () => [],
        },
        values: {
            type: Array,
            default: () => [],
        },
        modal: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            translations: {
                Back: this.$t("Back"),
                Next: this.$t("Next"),
                Close: this.$t("Close"),
                Save: this.$t("Save"),
            },
        };
    },
    computed: {
        disabledState() {
            if (this.modelValue === "property") {
                return !Boolean(this.property.name);
            } else if (this.modelValue === "category") {
                return this.categories.length === 0;
            } else if (this.modelValue === "values") {
                return this.values.length === 0;
            }
            return true;
        },
    },
    methods: {
        async navigateBack() {
            let temp = this.steps.map((x) => x.type);
            let index = temp.indexOf(this.modelValue);
            let prevStep = temp[index - 1];

            this.$emit("update:modelValue", prevStep);
        },
    },
    emits: ["update:modal", "update:modelValue"],
};
</script>
