<template>
    <div class="mt-4">
        <data-table
            v-bind="table"
            v-model="ids"
            @pagination-change-page="paginate"
            :draggable="true"
            @load-all="(val) => (loadAll = val)"
            @drag-row="(val) => handleSorting(val)"
            :enable-mobile="true"
            :filters="!loadAll"
            app-name="category_property_values"
            :get-data="getData"
            :filter-options="filterOptions"
            v-model:query="query"
        >
            <template #button>
                <button
                    class="btn btn-white btn-small text-nowrap d-flex align-items-center flex-nowrap"
                    @click="modalMerge = true"
                >
                    <i class="far fa-code-merge"></i>
                    {{ translations["Merge values"] }}
                </button>
                <button
                    class="btn btn-default btn-small text-nowrap d-flex align-items-center flex-nowrap"
                    @click="modal = true"
                >
                    <i class="far fa-plus"></i>
                    {{ translations["Add property value"] }}
                </button>
            </template>
            <template #noResult>
                <b-button
                    variant="default"
                    class="text-nowrap d-flex align-items-center flex-nowrap"
                    @click="modal = true"
                >
                    <i class="far fa-plus"></i>
                    {{ translations["Add property value"] }}
                </b-button>
            </template>
            <TableActions
                v-model="ids"
                app-key="category_property"
                :actions="actions"
                @success="handleActions"
                :ignoreDefault="true"
                :enable-mobile="true"
            />
        </data-table>
        <CreateOrEditValue
            v-model="modal"
            v-model:data-row="dataRow"
            :model="model"
            @success="handleCreateOrUpdateRecords"
        />
        <MergeValues v-model="modalMerge" :model="model" @success="getData" />
    </div>
</template>

<script>
import Values from "../../js/Values";
import { markRaw } from "vue";
import { toast } from "@js/toast";

import DataTable from "@components/Table";
import TableActions from "@components/TableActions";
import CreateOrEditValue from "./../Helpers/CreateOrEditValue";
import MergeValues from "./../Helpers/MergeValues";

import ButtonLinkOption from "./../Table/ButtonLinkOption";
import DeleteRow from "./../Table/DeleteRow";
import Name from "./../Table/Name";

export default {
    components: {
        DataTable,
        TableActions,
        CreateOrEditValue,
        MergeValues,
    },
    data() {
        return {
            modal: false,
            modalMerge: false,
            isLoading: false,
            dataRow: null,
            ids: [],
            loadAll: false,
            paginationShowDisabled: false,
            total: 0,
            page: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            query: this.$route.query,
            model: new Values(this.$route.params.id),
            translations: {
                Name: this.$t("Name"),
                "Propery name": this.$t("Propery name"),
                Products: this.$t("Products"),
                "Deleted successfully": this.$t("Deleted successfully"),
                "Error while deleting": this.$t("Error while deleting"),
                "Are you are sure you want to delete? Caution: This action cannot be undone.": this.$t(
                    "Are you are sure you want to delete? Caution: This action cannot be undone."
                ),
                "Add property value": this.$t("Add property value"),
                Delete: this.$t("Delete"),
                "Merge values": this.$t("Merge values"),
                "Has products": this.$t("Has products"),
                Product: this.$t("Product"),
                "Includes": this.$t("Includes"),
                "Does not include": this.$t("Does not include"),
                Yes: this.$t("Yes"),
                No: this.$t("No"),
                "Sort priority": this.$t("Sort priority"),
            },
            data: {
                data: [],
            },
            loading: true,
        };
    },
    computed: {
        filterOptions() {
            return [
                {
                    key: "has_products",
                    label: this.translations["Has products"],
                    input: false,
                    options: [
                        { value: "1", label: this.translations["Yes"] },
                        { value: "0", label: this.translations["No"] },
                    ],
                },
                {
                    key: "product",
                    label: this.translations["Product"],
                    url: "/admin/api/core/products/search",
                    type: "select",
                    multiple: true,
                    options: [
                        { value: "in", label: this.translations["Includes"] },
                        { value: "not_in", label: this.translations["Does not include"] },
                    ],
                },
            ];
        },
        table() {
            return {
                data: this.data,
                paginationShowDisabled: this.paginationShowDisabled,
                isLoading: this.loading,
                defaultSorting: [{key: 'sort', sortingMode: 'asc'}],
                columns: [
                    {
                        column: "value",
                        key: "value",
                        sortable: !this.loadAll,
                        title: this.translations["Name"],
                        component: markRaw(Name),
                    },
                    // {
                    //     column: "property_name",
                    //     key: "property_name",
                    //     sortable: true,
                    //     title: this.translations["Propery name"],
                    // },
                    {
                        column: "products_count",
                        key: "products_count",
                        sortable: !this.loadAll,
                        title: this.translations["Products"],
                        component: markRaw(ButtonLinkOption),
                    },
                    {
                        column: "sort",
                        key: "sort",
                        sortable: !this.loadAll,
                        title: this.translations["Sort priority"],
                    },
                    {
                        column: "actions",
                        key: "actions",
                        sortable: false,
                        title: " ",
                        component: markRaw(DeleteRow),
                    },
                ],
            };
        },
        actions() {
            return [
                {
                    label: this.translations["Delete"],
                    icon: "fal fa-trash-alt",
                    action: "delete",
                    type: "post",
                    confirm: {
                        messageConfirm: this.translations[
                            "Are you are sure you want to delete? Caution: This action cannot be undone."
                        ],
                    },
                    messageSuccess: this.translations["Deleted successfully"],
                    messageError: this.translations["Error while deleting"],
                    url: `/admin/api/core/properties/${this.$route.params.id}/values/delete`,
                },
            ];
        },
    },
    methods: {
        handleCreateOrUpdateRecords(item, create) {
            if (create) {
                this.data.data.push({
                    ...item,
                    deleteRow: this.deleteRow,
                    openModal: this.openCreateOrEditModal,
                });
            } else {
                this.data.data = this.data.data.map((x) => {
                    if (x.id === item.id) {
                        x = {
                            ...item,
                            deleteRow: this.deleteRow,
                            openModal: this.openCreateOrEditModal,
                        };
                    }
                    return x;
                });
            }
        },
        async handleSorting(value) {
            try {
                await this.model.post("sort", {
                    sortable: value,
                });
            } catch (err) {
                this.$errorResponse(err);
            }
        },
        openCreateOrEditModal(data) {
            this.dataRow = data;
            this.modal = true;
        },
        async paginate(page) {
            this.page = page;
            await this.getData("paginate");
        },
        async getData(reload = false) {
            try {
                if (!reload) {
                    this.loading = true;
                }

                this.model.where(this.query);

                if (Object.keys(this.query).toString().includes("filters")) {
                    this.page = 1;
                }

                if(this.loadAll) {
                    this.data = await this.model.sorting();
                } else {
                    this.data = await this.model.paginate(
                        this.query?.page || this.page, 
                        this.query?.perpage || this.perPage
                    );
                }

                this.$emit("update:tabName", this.data.meta.property.name);

                this.total = this.data.total;

                this.data.data = this.data.data.map((x) => {
                    return {
                        ...x,
                        deleteRow: this.deleteRow,
                        openModal: this.openCreateOrEditModal,
                    };
                });
                this.$router.push({
                    query: {
                        ...this.$route.query,
                        page: this.table.data.current_page,
                    },
                });
                // if (this.data.data.length === 0) {
                //     this.modal = true;
                // }
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.loading = false;
            }
        },
        async deleteRow(data) {
            try {
                data.deleteLoader = true;

                await this.model.delete(data.id);

                this.data.data = this.data.data.filter((x) => x.id !== data.id);

                document.querySelectorAll(".tooltip").forEach((tooltip) => {
                    tooltip.parentNode.removeChild(tooltip);
                });

                toast.success(this.translations["Deleted successfully"]);
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                delete data.deleteLoader;
            }
        },
        handleActions(value) {
            if (value.action === "delete") {
                this.data.data = this.data.data.filter((x) => !this.ids.includes(x.id));
                if (this.data.data.length === 0 && this.page > 1) {
                    this.page--;
                    this.getData();
                } else if (
                    this.data.data.length === 0 &&
                    this.page === 1 &&
                    this.data.next_page_url
                ) {
                    this.getData();
                }
            }
            this.ids = [];
        },
    },
    watch: {
        loadAll(val) {
            if (val) {
                this.paginationShowDisabled = true;
                this.getData();
            } else {
                this.paginationShowDisabled = false;
                this.getData();
            }
        },
    },
    emits: ["update:addPropModal", "update:tabName"],
};
</script>
<style lang="scss">
#values-list {
    .col-value {
        padding: 16px 34px !important;
    }
}
</style>
