<template>
    <div class="d-flex align-items-center justify-content-end justify-content-md-start w-100 gap-2 gap-md-5">
        <ActiveSwitch
            :is-active="data.active"
            @change-value="(val) => data.onStatusChange(val, data)"
            :has-label="false"
            :loader="data.loader"
        />
        <DeleteComponent
            v-model:loader="data.loader"
            :deleteFunction="() => data.onDelete(data)"
        />
    </div>
</template>
<script>
import ActiveSwitch from "@components/Form/ActiveSwitch";
import DeleteComponent from "@components/DeleteComponent";

export default {
    name: "StatusChange",
    components: {
        ActiveSwitch,
        DeleteComponent,
    },
    props: {
        data: {
            required: true,
            default: {},
            type: Object,
        },
        column: {
            required: true,
            default: {},
            type: Object,
        },
    },
};
</script>
