<template>
    <SettingsWrapper
        :breadcrumb="breadcrumbs"
        :description="translations[
            'Set quadrants on the map, according to which you can accept orders in your store. '
        ]
            "
        :loading="loading"
        :prevent-save="true"
        :tabs="tabs"
        :title="translations['Geo polygons']"
        icon="far fa-globe-africa"
        setting-key="geo_polygons"
    >
        <template v-if="!isAddGeoPolygonRoute && !isEditGeoPolygonRoute" #headerButton>
            <router-link :to="{name: 'geo_polygons_add.settings'}" class="btn btn-primary text-nowrap">
                <i class="far fa-plus"></i>
                {{ translations["New Geo polygon"] }}
            </router-link>
        </template>
        <template #settings>
            <router-view
                :model="model"
            >
            </router-view>
        </template>
    </SettingsWrapper>
</template>

<script>
import SettingsWrapper from "@components/SettingsWrapper/SettingsWrapper";
import GeoPolygons from "./js/GeoPolygons.js";

export default {
    name: "GeoPolygonsIndex",
    components: {
        SettingsWrapper,
    },
    data() {
        return {
            model: new GeoPolygons(),
            loading: false,
            meta: null,
            translations: {
                "Settings": this.$t("Settings"),
                "Geo polygons": this.$t("Geo polygons"),
                "New Geo polygon": this.$t("New Geo polygon"),
                "Add new Polygon": this.$t("Add new Polygon"),
                "Polygons": this.$t("Polygons"),
                "Edit Polygon": this.$t("Edit Polygon"),
                "Set quadrants on the map, according to which you can accept orders in your store. ": this.$t(
                    "Set quadrants on the map, according to which you can accept orders in your store. "
                ),
            },
        };
    },
    computed: {
        tabs() {
            const tabs = [
                {
                    to: {name: "geo_polygons.settings"},
                    label: this.translations['Polygons'],
                },
            ];

            if (this.isAddGeoPolygonRoute) {
                tabs.push({
                    to: {name: "geo_polygons_add.settings"},
                    label: this.translations['Add new Polygon'],
                })
            }

            if (this.isEditGeoPolygonRoute) {
                tabs.push({
                    to: {name: "geo_polygons_edit.settings"},
                    label: this.translations['Edit Polygon'],
                })
            }

            return tabs;
        },
        breadcrumbs() {
            let breadcrumbs = [
                {text: this.translations['Settings'], to: {name: 'admin.index'}},
                {
                    text: this.translations['Polygons'],
                    to: {name: 'geo_polygons.settings'}
                }
            ]

            if (this.$route.name === 'geo_polygons_add.settings') {
                breadcrumbs = [...breadcrumbs, {text: this.translations['Add new Polygon']}]
            }

            if (this.$route.name === 'geo_polygons_edit.settings') {
                breadcrumbs = [...breadcrumbs, {text: this.translations['Edit Polygon']}]
            }

            return breadcrumbs
        },
        isAddGeoPolygonRoute() {
            return this.$route.name === 'geo_polygons_add.settings'
        },
        isEditGeoPolygonRoute() {
            return this.$route.name === 'geo_polygons_edit.settings'
        }
    },
};
</script>
