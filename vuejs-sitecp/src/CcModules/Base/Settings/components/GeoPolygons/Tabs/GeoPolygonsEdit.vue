<template>
    <b-container class="container-medium pb-5">
        <b-row no-gutters class="justify-content-center">
            <b-col>
                <SettingDescription v-model="boxes.data" box-key="geo_polygons" />
                <b-card class="mt-3">
                    <p class="label-500-16px">{{ translations['Edit Polygon'] }}</p>
                    <InputComponent :label-text="translations['Polygon name']"
                        :placeholder="translations['Add the name of the Polygon. Example: Paris or France']"
                        v-model="modelData.name" :error="responseErrors['name']" type="text" :column-style="true" />
                    <hr class="mx-2">
                    <p class="section-settings-description">
                        {{ translations[`Use the small buttons located in the upper left corner of the map to outline the polygon. Note you can only have one field on one polygon.You cannot make more than one outline.Change the polygon color from the rectangles on the right side of the map.`] }}</p>
                    <GoogleDrawMapComponent v-if="!loading" v-model="modelData.area" :polygon-data="modelData" :error="responseErrors['area']" @update:modified="onUpdatedMapData" />
                </b-card>
                <SubmitChanges v-if="!preventSave" v-model="geoPolygonsData" :submit-loader="submitLoader" :response-errors="responseErrors"
                    :disable-save="submitLoader" :save-func="actionSubmit" />
            </b-col>
        </b-row>

    </b-container>
</template>

<script>
import SubmitChanges from "@components/SubmitChanges";
import SettingDescription from "@components/SettingDescription";
import InputComponent from "@components/Form/InputComponent";
import GoogleDrawMapComponent from "@components/Form/GoogleDrawMapComponent";

export default {
    name: "GeoPolygonssAdd",
    components: {
        SettingDescription,
        SubmitChanges,
        InputComponent,
        GoogleDrawMapComponent
    },
    props: {
        model: {
            required: true
        }
    },
    data() {
        return {
            boxes: {
                data: {
                    key: "geo-polygons-edit",
                    group: "geo_polygons",
                    title: "Edit Polygon",
                    editMethod: "inline",
                    titleHelp: {
                        label: null,
                        parameters: [],
                    },
                    helpClass: 'col-12',
                    infoTitle: "Edit Polygon",
                    infoTitleHelp: null,
                    infoDescription: "Scelerisque eros pulvinar sed viverra urna at. Magna nibh cursus tortor faucibus turpis lacus mauris et tellus. Lobortis amet sapien dolor et magna sit condimentum donec volutpat. Non a et fusce metus ut tempus.",
                },
            },
            modelData: {},
            submitLoader: false,
            loading: true,
            responseErrors: {},
            geoPolygonsData: {},
            preventSave: true,
            translations: {
                "Standards and formats": this.$t("Standards and formats"),
                "Add Geo zone name, e.g. Paris or France": this.$t(
                    "Add Geo zone name, e.g. Paris or France"
                ),
                "Geo zone name": this.$t("Geo zone name"),
                "Add the name of the Polygon. Example: Paris or France": this.$t("Add the name of the Polygon. Example: Paris or France"),
                "Polygon name": this.$t("Polygon name"),
                "Edit Polygon": this.$t("Edit Polygon"),
                "Use the small buttons located in the upper left corner of the map to outline the polygon. Note you can only have one field on one polygon.You cannot make more than one outline.Change the polygon color from the rectangles on the right side of the map.": this.$t("Use the small buttons located in the upper left corner of the map to outline the polygon. Note you can only have one field on one polygon.You cannot make more than one outline.Change the polygon color from the rectangles on the right side of the map."),
            },
        }
    },
    async created() {
        await this.getData();
        this.loading = false;
    },
    mounted() {
        this.helpBoxes(this.boxes)
    },
    computed: {
        isGoogleAPIKey() {
            return !!this.serverSettings('google_maps_api_key')
        }
    },
    methods: {
        async getData(){
            try {
                const {id} = this.$route.params
                const data = await this.model.find(id)
                this.modelData = data
            } catch (error) {
                this.$errorResponse(error);
            }
        },
        async actionSubmit() {
            try {
                this.submitLoader = true
                this.responseErrors = {}
                await this.model.patch(this.modelData, this.$route.params.id)
                this.submitLoader = false;
                this.$router.push({name: 'geo_polygons.settings'})
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.submitLoader = false;
            }
        },
        onUpdatedMapData(value) {
            this.modelData.area = value;
        }
    },
    watch:{
        modelData: {
            handler(){
                if(this.loading) return
                this.preventSave = false
                this.geoPolygonsData = {
                    area: this.modelData.area,
                    name: this.modelData.name
                }
            },
            deep: true,
        }
    }
};
</script>
