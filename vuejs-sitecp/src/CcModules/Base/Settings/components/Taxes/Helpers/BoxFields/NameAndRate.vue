<template>
    <b-row>
        <b-col class="col-12 col-md-7">
            <InputComponent
                v-model="modelValue.name"
                :column-style="true"
                :label-text="
                    translations[
                        this.$route.params.type === 'fee'
                            ? 'Name (e.g. Service fee etc)'
                            : 'Name (e.g. Service tax etc)'
                    ]
                "
                :placeholder="
                    translations[
                        this.$route.params.type === 'fee'
                            ? 'Enter fee name'
                            : 'Enter tax name'
                    ]
                "
                :help-block="translations['This name will be visible in the checkout']"
                :no-margin="true"
                :error="
                    translationLabels[responseErrors['name']] || responseErrors['name']
                "
            />
        </b-col>
        <b-col class="col-12 col-md-5">
            <component
                :is="unitComponent"
                v-model="modelValue.tax"
                :column-style="true"
                :label-text="translations['Rate']"
                placeholder="0.00"
                :no-margin="true"
                :error="translationLabels[responseErrors['tax']] || responseErrors['tax']"
                v-bind="rateBindings"
            />
        </b-col>
    </b-row>
</template>

<script>
import InputComponent from "@components/Form/InputComponent";
import PercentComponent from "@components/Form/PercentComponent";
import CurrencyComponent from "@components/Form/CurrencyComponent";

export default {
    components: {
        InputComponent,
        PercentComponent,
        CurrencyComponent,
    },
    props: {
        modelValue: {
            type: Object,
            required: true,
            default: {},
        },
        responseErrors: {
            required: false,
            default: {},
        },
        translationLabels: {
            required: false,
            default: {},
        },
    },
    data() {
        return {
            translations: {
                "Name (e.g. Service fee etc)": this.$t("Name (e.g. Service fee etc)"),
                "Enter fee name": this.$t("Enter fee name"),
                "Enter tax name": this.$t("Enter tax name"),
                "This name will be visible in the checkout": this.$t(
                    "This name will be visible in the checkout"
                ),
                Rate: this.$t("Rate"),
                'The Name field is required': this.$t('The Name field is required'),
                'The Rate field is required': this.$t('The Rate field is required'),
                "Name (e.g. Service tax etc)": this.$t("Name (e.g. Service tax etc)"),
            },
        };
    },
    computed: {
        unitComponent() {
            switch (this.modelValue.type) {
                case "percent":
                    return PercentComponent;
                case "flat":
                    return CurrencyComponent;
                default:
                    return InputComponent;
            }
        },
        rateBindings() {
            if (this.modelValue.type === "percent") {
                return {
                    max: 90,
                    digits: 2,
                };
            }
            return { digits: 2 };
        },
        unit() {
            return this.modelValue.type === "percent"
                ? "%"
                : this.serverSettings("currency.sign");
        },
    },
};
</script>
