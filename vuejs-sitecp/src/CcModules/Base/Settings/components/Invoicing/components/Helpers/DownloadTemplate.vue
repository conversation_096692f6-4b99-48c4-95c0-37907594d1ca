<template>
    <b-row>
        <b-col class="d-flex align-items-center justify-content-between">
            <span class="label-text" v-html="label"></span>
            <a
                :href="url"
                target="_blank"
                class="btn btn-white d-flex align-items-center gap-1"
                download="template.txt"
                @click="handleDownloadAnimation"
            >
                <b-spinner small v-if="downloadLoader"></b-spinner>
                <i v-else class="far fa-arrow-to-bottom me-0"></i>
                {{ translations["Template"] }}
            </a>
        </b-col>
    </b-row>
</template>
<script>
export default {
    name: "DownloadTemplate",
    props: ["label", "url"],
    data() {
        return {
            downloadLoader: false,
            translations: {
                "Download invoice template": this.$t("Download invoice template"),
                Template: this.$t("Template"),
            },
        };
    },
    methods: {
        handleDownloadAnimation() {
            this.downloadLoader = true;
            setTimeout(() => {
                this.downloadLoader = false;
            }, 1600);
        },
    },
};
</script>
