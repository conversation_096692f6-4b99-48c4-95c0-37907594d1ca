<template>
    <SettingsWrapper
        v-model:boxes="boxes"
        v-model:responseErrors="responseErrors"
        v-model:settings="settings"
        :action-submit="submitData"
        :breadcrumb="[{ text: translations['Settings'], to: { name: 'admin.index' } }, { text: translations['Cart and checkout'] }]"
        :loading="loading"
        :submit-loader="submitLoader"
        :translationLabels="translations"
        setting-key="cart"
    />
    <PlanFeature
        v-model="modal"
        v-model:record="abandoned_order_feature"
        type="bool"
        @success="handleAfterPay"
    />
</template>

<script>
import _ from "lodash";
import fields from "./fields.js";
import Settings from "./../../js/Models/Settings";

import SettingsWrapper from "@components/SettingsWrapper/SettingsWrapper";


export default {
    mixins: [fields],
    name: "CartSettings",
    components: {
        SettingsWrapper,
    },
    data() {
        return {
            boxes: [],
            model: new Settings(),
            settings: {},
            meta: {},
            loading: true,
            submitLoader: false,
            responseErrors: {},
            field_options: [],
            abandoned_order_feature: {},
            modal: false,

            translations: {
                "Settings": this.$t("Settings"),
                "Cart and checkout": this.$t("Cart and checkout"),
                "Customer Accounts": this.$t("Customer Accounts"),
                "Choose if you want to prompt your customer to create an account when they check out.": this.$t(
                    "Choose if you want to prompt your customer to create an account when they check out."
                ),
                "Customer accounts verification": this.$t(
                    "Customer accounts verification"
                ),
                "Customer profiles": this.$t("Customer profiles"),
                "Convert guests into members": this.$t("Convert guests into members"),
                "Show product price only for logged in users": this.$t(
                    "Show product price only for logged in users"
                ),
                "Require shipping address on registration": this.$t(
                    "Require shipping address on registration"
                ),
                "Require billing address on registration": this.$t(
                    "Require billing address on registration"
                ),
                "Processing orders": this.$t("Processing orders"),
                "Habitant est orci in convallis arcu nunc. Cum mattis semper erat rhoncus pretium.": this.$t(
                    "Habitant est orci in convallis arcu nunc. Cum mattis semper erat rhoncus pretium."
                ),
                "Abandoned cart reminder": this.$t("Abandoned cart reminder"),
                "Send abondoned cart reminders via": this.$t(
                    "Send abondoned cart reminders via"
                ),
                Email: this.$t("Email"),
                Messenger: this.$t("Messenger"),
                "Email and Messenger": this.$t("Email and Messenger"),
                "Send abondoned reminder in (minutes)": this.$t(
                    "Send abondoned reminder in (minutes)"
                ),
                "Ask for shipping address for digital products": this.$t(
                    "Ask for shipping address for digital products"
                ),
                "Automatically select the shipping option if it is only one": this.$t(
                    "Automatically select the shipping option if it is only one"
                ),
                "Choose a default payment provider": this.$t(
                    "Choose a default payment provider"
                ),
                "Choose a default shipping type": this.$t(
                    "Choose a default shipping type"
                ),
                "Choose a default shipping provider": this.$t(
                    "Choose a default shipping provider"
                ),
                "Optional settings": this.$t("Optional settings"),
                "Minimum amount for an order": this.$t("Minimum amount for an order"),
                "Maximum amount of products in the cart": this.$t(
                    "Maximum amount of products in the cart"
                ),
                "Maximum products quantity of a kind per customer": this.$t(
                    "Maximum products quantity of a kind per customer"
                ),
                "Maximum cart quantity of a kind per customer": this.$t(
                    "Maximum cart quantity of a kind per customer"
                ),
                "When the total quantity of a product decrease, send me an email": this.$t(
                    "When the total quantity of a product decrease, send me an email"
                ),
                "I want to decrease product quantity when the status is": this.$t(
                    "I want to decrease product quantity when the status is"
                ),
                "Paid / Fulfilled": this.$t("Paid / Fulfilled"),
                "Pending / Fulfilled": this.$t("Pending / Fulfilled"),
                "Order number": this.$t("Order number"),
                "System ID": this.$t("System ID"),
                "A string of numbers and letters": this.$t(
                    "A string of numbers and letters"
                ),
                "Automatic completion of the order": this.$t(
                    "Automatic completion of the order"
                ),
                "Show a description of payment methods": this.$t(
                    "Show a description of payment methods"
                ),
                "Payment methods": this.$t("Payment methods"),
                "Available payment methods for orders created through the administrative panel": this.$t(
                    "Available payment methods for orders created through the administrative panel"
                ),
                "Additional fields settings": this.$t("Additional fields settings"),
                "Additional fields settings description": this.$t(
                    "Additional fields settings description"
                ),
                Hidden: this.$t("Hidden"),
                Optional: this.$t("Optional"),
                Required: this.$t("Required"),
                "Not required": this.$t("Not required"),
                "Require billing address on checkout": this.$t(
                    "Require billing address on checkout"
                ),
                "First name": this.$t("First name"),
                "Company name": this.$t("Company name"),
                "Street name": this.$t("Street name"),
                "Street number": this.$t("Street number"),
                "Last name": this.$t("Last name"),
                Phone: this.$t("Phone"),
                "Additional information: Apt, suite, etc.": this.$t(
                    "Additional information: Apt, suite, etc."
                ),
                "Require postal/zip code at checkout": this.$t(
                    "Require postal/zip code at checkout"
                ),
                "Company VAT or EIN": this.$t("Company VAT or EIN"),
                "Validate VAT number from VIES service": this.$t(
                    "Validate VAT number from VIES service"
                ),
                "Company registration number": this.$t("Company registration number"),
                "Company owner": this.$t("Company owner"),
                "Don't ask for billing address": this.$t("Don't ask for billing address"),
                "Show cart icon at the site header": this.$t(
                    "Show cart icon at the site header"
                ),
                Miscellaneous: this.$t("Miscellaneous"),
                "Miscellaneous header description": this.$t(
                    "Miscellaneous header description"
                ),
                "How to display the cart bubble": this.$t(
                    "How to display the cart bubble"
                ),
                "Total amount of products in the cart": this.$t(
                    "Total amount of products in the cart"
                ),
                "Number of unique products in the cart": this.$t(
                    "Number of unique products in the cart"
                ),
                "Sort by product category": this.$t("Sort by product category"),
                "Sort by addition order": this.$t("Sort by addition order"),
                "Sort products in cart by": this.$t("Sort products in cart by"),
                "Enable cart button animations": this.$t("Enable cart button animations"),
                "Action after customer click on 'Buy now' button": this.$t(
                    "Action after customer click on 'Buy now' button"
                ),
                "Forward to the checkout page": this.$t("Forward to the checkout page"),
                "Forward to the cart page": this.$t("Forward to the cart page"),
                "Show confirmation popup": this.$t("Show confirmation popup"),
                "Do nothing": this.$t("Do nothing"),
                "Show 'Cart page' as a side panel": this.$t(
                    "Show 'Cart page' as a side panel"
                ),
                "works only with Forward to the cart page function": this.$t(
                    "works only with Forward to the cart page function"
                ),
                "Merging carts": this.$t("Merging carts"),
                "At the entrance, if the customer had an old cart with products, add them to the new cart": this.$t(
                    "At the entrance, if the customer had an old cart with products, add them to the new cart"
                ),
                "Show Google map in addresses": this.$t("Show Google map in addresses"),
                "Show Google map in Office Delivery Method": this.$t(
                    "Show Google map in Office Delivery Method"
                ),
                "Show Google Map in Locker Delivery Method": this.$t(
                    "Show Google Map in Locker Delivery Method"
                ),
                "Google Maps API key": this.$t("Google Maps API key"),
                'Show "I accept marketing" checkbox': this.$t(
                    'Show "I accept marketing" checkbox'
                ),
                "Choose the static page which contents your Terms of Service": this.$t(
                    "Choose the static page which contents your Terms of Service"
                ),
                "Application of fees and taxes according to": this.$t(
                    "Application of fees and taxes according to"
                ),
                "Billing address": this.$t("Billing address"),
                "Shipping address": this.$t("Shipping address"),

                //validations
                "The product threshold must be a number.": this.$t(
                    "The product threshold must be a number."
                ),
                "The product threshold must be an integer.": this.$t(
                    "The product threshold must be an integer."
                ),
                "The product threshold must be at least 1.": this.$t(
                    "The product threshold must be at least 1."
                ),
                "The checkout customer access field is required.": this.$t(
                    "The checkout customer access field is required."
                ),
                "The selected checkout customer access is invalid.": this.$t(
                    "The selected checkout customer access is invalid."
                ),
                "The unconfirmed accounts restrict field is required.": this.$t(
                    "The unconfirmed accounts restrict field is required."
                ),
                "The selected unconfirmed accounts restrict is invalid.": this.$t(
                    "The selected unconfirmed accounts restrict is invalid."
                ),
            },
        };
    },
    created() {
        this.field_options = [
            {id: "hidden", name: "Hidden"},
            {id: "optional", name: "Optional"},
            {id: "required", name: "Required"},
        ];
        this.getData();
    },
    methods: {
        handleAfterPay(result) {
            Object.values(result?.status || []).forEach((item) => {
                if (item.status === "success") {
                    this.abandoned_order_feature.current = true;
                    this.settings.show_powered_by_info = 0;
                }
            });
        },
        async getData() {
            try {
                this.loading = true;
                const {settings, meta} = await this.model.find("cart");

                this.settings = settings;
                this.meta = meta;
                if (!this.settings.checkout_other_pages) {
                    this.settings.checkout_other_pages = [];
                }
                this.abandoned_order_feature = this.meta.features[0];

                this.setupBoxes();
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.loading = false;
                this.preventSave = false;
            }
        },
        handlePayloadCheckAndModificate() {
            let temp = _.clone(this.settings);

            let numericKeys = ['checkout_min_price', 'checkout_max_price', 'cart_max_products', 'cart_max_quantity', 'product_threshold']
            numericKeys.forEach(key => {
                if (!temp[key]) {
                    temp[key] = 0
                }
            })
            return temp;
        },
        async submitData() {
            this.responseErrors = {};
            let payload = this.handlePayloadCheckAndModificate();

            if (payload?.checkout_other_pages?.length === 0) {
                delete payload.checkout_other_pages;
            }

            try {
                this.submitLoader = true;
                const settings = await this.model.update(payload, "cart");
                if (!settings.checkout_other_pages) {
                    settings.checkout_other_pages = [];
                }

                this.settings = settings;
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.submitLoader = false;
            }
        },
    },
    watch: {
        "settings.abandoned_remainder"(newVal, oldVal) {
            if (
                !this.loading &&
                newVal === 1 &&
                oldVal === 0 &&
                !this.abandoned_order_feature.current
            ) {
                this.modal = true;
            }
        },
        "settings.google_map_api_key"(newVal, oldVal) {
            if (process.env.NODE_ENV !== 'production') {
                return
            }

            this.boxes.forEach((box) => {
                if (box.key === "google_api_key") {
                    box.fields.forEach((field) => {
                        if (
                            [
                                "checkout_hide_address_map",
                                "checkout_hide_office_map",
                                "checkout_hide_locker_map",
                            ].includes(field.key)
                        ) {
                            if (typeof newVal === "string") {
                                field.disabled = newVal.length < 39;
                            } else {
                                field.disabled = !Boolean(newVal);
                            }
                        }
                    });
                }
            });
        },
        modal(val) {
            if (!val) {
                if (!this.abandoned_order_feature.current) {
                    this.settings.abandoned_remainder = 0;
                }
            }
        },
    },
};
</script>
