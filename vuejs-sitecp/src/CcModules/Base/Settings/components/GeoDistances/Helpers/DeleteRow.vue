<template>
    <div class="d-flex align-items-center justify-content-end w-100">
        <DeleteComponent
            v-model:loader="data.loader"
            :deleteFunction="() => data.onDelete(data)"
        />
    </div>
</template>
<script>
import DeleteComponent from "@components/DeleteComponent";
export default {
    components: {
        DeleteComponent,
    },
    props: {
        data: {
            required: true,
            default: {},
            type: Object,
        },
        column: {
            required: true,
            default: {},
            type: Object,
        },
    },
};
</script>
