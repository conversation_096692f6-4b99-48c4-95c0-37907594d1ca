<template>
    <transition name="expand">
        <b-card
            :class="{ 'collapsed c-pointer': isCollapsed }"
            @click="setCollapsed"
            v-if="!isCollapsed"
        >
            <b-row class="description-result-head">
                <b-col class="col-6 d-flex align-items-center justify-content-start">
                    <span class="label-400">
                        {{ result.date }}
                    </span>
                </b-col>
                <b-col class="col-6 d-flex align-items-center justify-content-end">
                    <span class="label-400">
                        {{ translations["Tokens:"] }}
                    </span>
                    <span class="label-500">{{ result.used_tokens }}</span>
                </b-col>
            </b-row>

            <div class="info-box info-box-warning mt-3" v-if="this.index === 0">
                <span
                    v-html="
                        translations[
                            'Be aware that this content is AI-generated. We apply two-factor standards for proofreading and grammar checks. However, it\'s strongly advised to review before use.'
                        ]
                    "
                ></span>
            </div>

            <div
                class="label-400 mt-3"
                v-if="typeof result.response.text === 'string'"
                v-html="result.response.text"
                :class="{ collapsed: isCollapsed }"
            ></div>
            <div
                class="seo-google-box mt-3"
                v-else
                :class="{ collapsed: isCollapsed }"
                style="display: block"
            >
                <p class="seo-google-title">{{ result.response.text.title }}</p>
                <p class="seo-google-url">
                    {{ serverSettings("host") }}/product/{{ urlHandle }}
                </p>
                <p class="seo-google-description">
                    {{ result.response.text.description }}
                </p>
            </div>

            <ShopperSense
                v-if="panelType === 'description'"
                :shopperSense="shopperSense"
                :result="result"
                :analyze-loading="analyzeLoading"
                :index="index"
                @update:result="(val) => (result = val)"
                @update:shopperSense="(val) => (shopperSense = val)"
                @analyze="analyze(result)"
                @activateShopperSense="activateShopperSense"
            />

            <div class="row results-controls mt-4">
                <div class="col-xs-6">
                    <div class="evaluate">
                        <i
                            class="far fa-thumbs-up"
                            @click.stop="setEvaluation(result.id, 2)"
                            :class="{ checked: result.evaluation === 2 }"
                        ></i>
                        <i
                            class="far fa-thumbs-down"
                            @click.stop="setEvaluation(result.id, 1)"
                            :class="{ checked: result.evaluation === 1 }"
                        ></i>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="save-text text-right">
                        <a
                            href="javascript:;"
                            @click.stop="
                                typeof result.response.text === 'string'
                                    ? copyText(result.response.text)
                                    : copyText(result.response.text.description)
                            "
                            ref="copyTrigger"
                            class="cc-purple fw-500 me-3"
                        >
                            <i class="far fa-copy me-2"></i>
                            {{ translations["Copy text"] }}
                        </a>
                        <a
                            href="javascript:;"
                            class="btn btn-primary"
                            v-if="result.response.type === 'product_description'"
                            @click.stop.prevent="
                                keepDescription(result.id, result.response.text)
                            "
                        >
                            <i class="far fa-check"></i>{{ translations["Keep"] }}
                        </a>
                        <a
                            href="javascript:;"
                            class="btn btn-primary"
                            v-else-if="
                                result.response.type === 'product_short_description'
                            "
                            @click.stop.prevent="
                                keepShortDescription(result.id, result.response.text)
                            "
                        >
                            <i class="far fa-check"></i>{{ translations["Keep"] }}
                        </a>
                        <a
                            href="javascript:;"
                            class="btn btn-primary"
                            v-else-if="
                                result.response.type === 'product_meta_description'
                            "
                            @click.stop.prevent="
                                keepMetaDescription(
                                    result.id,
                                    result.response.text.title,
                                    result.response.text.description
                                )
                            "
                        >
                            <i class="far fa-check"></i>{{ translations["Keep"] }}
                        </a>
                    </div>
                </div>
            </div>
        </b-card>
        <b-card class="mt-3" v-else @click="setCollapsed">
            <b-row>
                <b-col class="col-6 d-flex align-items-center justify-content-start">
                    <span class="label-400">
                        {{ result.date }}
                    </span>
                </b-col>
                <b-col class="col-6 d-flex align-items-center justify-content-end">
                    <span class="label-400">
                        {{ translations["Tokens:"] }}
                    </span>
                    <span class="label-500">{{ result.used_tokens }}</span>
                </b-col>
            </b-row>

            <div class="info-box info-box-warning mt-3" v-if="this.index === 0">
                <span
                    v-html="
                        translations[
                            'Be aware that this content is AI-generated. We apply two-factor standards for proofreading and grammar checks. However, it\'s strongly advised to review before use.'
                        ]
                    "
                ></span>
            </div>

            <div
                class="label-400 mt-3"
                v-if="typeof result.response.text === 'string'"
                v-html="result.response.text"
                :class="{ collapsed: isCollapsed }"
            ></div>
            <div
                class="seo-google-box mt-3"
                v-else
                :class="{ collapsed: isCollapsed }"
                style="display: block"
            >
                <p class="seo-google-title">{{ result.response.text.title }}</p>
                <p class="seo-google-url">
                    {{ serverSettings("host") }}/product/{{ urlHandle }}
                </p>
                <p class="seo-google-description">
                    {{ result.response.text.description }}
                </p>
            </div>
        </b-card>
    </transition>
</template>

<script>
import { toast } from "@js/toast";
import InfoComponent from "../Form/InfoComponent";
import CloudioLoader from "../Helpers/CloudioLoader";
import ShopperSense from "./ShopperSense";

export default {
    name: "Result",
    components: {
        ShopperSense,
        InfoComponent,
        CloudioLoader,
    },
    props: {
        result: {
            type: Object,
        },
        index: {
            required: false,
        },
        analyzeLoading: {
            required: false,
            default: false,
        },
        shopperSense: {},
        panelType: {},
        isCollapsed: {},
        model: {
            type: Object,
            default: () => ({}),
        },
        description: {
            required: false,
        },
        seoDescription: {
            required: false,
        },
        shortDescription: {
            required: false,
        },
        urlHandle: {
            required: false,
        },
    },
    data() {
        return {
            translations: {
                "Be aware that this content is AI-generated. We apply two-factor standards for proofreading and grammar checks. However, it's strongly advised to review before use.": this.$t(
                    "Be aware that this content is AI-generated. We apply two-factor standards for proofreading and grammar checks. However, it's strongly advised to review before use."
                ),
                "Copy text": this.$t("Copy text"),
                Keep: this.$t("Keep"),
                "Tokens:": this.$t("Tokens:"),
                "ShopperSense activated": this.$t("ShopperSense activated"),
                Copied: this.$t("Copied"),
            },
        };
    },
    mounted() {
        if (this.index === 0) {
            this.$emit("update:isCollapsed", false);
        }
    },
    methods: {
        setCollapsed(value) {
            let isCollapseInternal = this.isCollapsed;
            this.index !== 0
                ? this.$emit(
                      "update:isCollapsed",
                      (isCollapseInternal = !isCollapseInternal)
                  )
                : this.$emit("update:isCollapsed", false);
        },
        async analyze(result) {
            if (result) {
                let resultUpdated = result;
                resultUpdated.analyze_text = null;
                resultUpdated.analyze_is_error = false;
                resultUpdated.analyze_progress = "starting";
                this.$emit("update:result", resultUpdated);
            }

            this.$emit("update:analyzeLoading", true);

            try {
                const response = await this.model.post("analyze", {
                    product_id: this.model.product_id,
                    type: this.result.response.type,
                    text: this.result.response.text,
                });
                if (response.status === "success") {
                    this.$emit("update:history", response.log_id);
                }
            } catch (error) {
                this.$errorResponse(error);
            }
        },
        async activateShopperSense() {
            let body = {
                key: "shopper_sense",
                status: 1,
            };

            try {
                await this.model.post("skills/status", body);
                this.$emit("update:shopperSense", true);
                toast.success(this.translations["ShopperSense activated"]);
            } catch (error) {
                this.$errorResponse(error);
            }
        },
        keepDescription(id, text) {
            this.$emit("update:description", text);
            this.$emit("update:isOpened", false);
        },
        keepShortDescription(id, text) {
            this.$emit("update:shortDescription", text);
            this.$emit("update:isOpened", false);
        },
        keepMetaDescription(id, title, text) {
            this.$emit("update:seoDescription", text);
            this.$emit("update:isOpened", false);
        },
        async setEvaluation(logId, rating) {
            try {
                const response = await this.model.find(`rating/${logId}/${rating}`);
                toast.success(response.msg);
                this.result.evaluation = rating;
                this.$emit("update:result", this.result);
            } catch (error) {
                this.$errorResponse(error);
            }
        },
        copyText(str) {
            navigator.clipboard.writeText(str).then(() => {
                toast.success(this.translations["Copied"]);
            });
        },
    },
    emits: [
        "update:analyzeLoading",
        "update:history",
        "update:isOpened",
        "update:result",
        "update:shopperSense",
        "update:isCollapsed",
        "update:description",
        "update:shortDescription",
        "update:seoDescription",
    ],
};
</script>
<style lang="scss">
.slide-fade-enter-active {
    transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
    transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
}

.expand-leave-active,
.expand-enter-active {
    transition: all 500ms ease;
    overflow: hidden;
}

.expand-enter-to,
.expand-leave-from {
    height: v-bind(height);
}

.expand-enter-from,
.expand-leave-to {
    opacity: 0;
    height: 0;
}
.evaluate {
    display: flex;
    gap: 8px;
    align-items: center;

    i {
        cursor: pointer;
        font-size: 20px;
        font-style: normal;
        font-weight: 300;
        line-height: 22px;
        color: var(--Color-Text-Body---cc-color-text-subdued, #d5d5d6);

        &.fa-thumbs-up {
            &.checked {
                color: #25cf8b;
            }
        }

        &.fa-thumbs-down {
            &.checked {
                color: #fc4f4d;
            }
        }
    }
}
.seo-google-box {
    padding: var(--Spacing---cc-space-md, 16px);
    border-radius: var(--Border-radius---cc-border-radius-sm, 6px);
    border: var(--space-size-1, 1px) solid
        var(--Color-Border-Neutral---cc-color-border, #d6d9e9);
}
</style>
