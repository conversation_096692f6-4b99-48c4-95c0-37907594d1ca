<template>
    <b-row v-for="(row, rowIndex) in rows" :key="rowIndex">
        <b-col>
            <SelectWithAjax
                v-for="(subField, index) in Object.keys(fields)"
                v-model:val="rows[rowIndex][subField]"
                :label="translations[subField]"
                :searchable="true"
                :options="map_options"
                :selectClasses="index < subField.length - 1 ? 'mb-3' : ''"
                :error="
                    translations[errors[`import_binds.${index}.${subField}`]] ||
                    errors[`import_binds.${index}.${subField}`]
                "
            />
        </b-col>
        <b-col class="col-auto d-flex align-items-center">
            <DeleteComponent
                :delete-function="removeRow"
                :data="rowIndex"
                :label="translations['Remove row?']"
                :confirm-button-text="translations.Remove"
            >
                <template #action>
                    <i
                        class="fa-light fa-trash-can remove-from-multiple-csv"
                    ></i>
                </template>
            </DeleteComponent>
        </b-col>
        <b-col class="col-12" v-if="rowIndex < rows.length - 1">
            <hr />
        </b-col>
    </b-row>
    <hr />
    <a
        href="javascript:void(0);"
        @click="addNewRow"
        class="label-500 primary text-center"
    >
        <i class="fa-light fa-plus"></i>
        {{ translations["Add new row"] }}
    </a>
</template>
<script>
import useSharedImportModalState from "../js/useSharedImportModalState";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import DeleteComponent from "@components/DeleteComponent";

export default {
    name: "MultipleFields",
    components: {
        SelectWithAjax,
        DeleteComponent,
    },
    setup() {
        const { import_binds, responseErrors } = useSharedImportModalState();
        return { import_binds, responseErrors };
    },
    props: {
        fields: {
            required: true,
            default: () => {},
        },
        map_options: {
            type: Array,
            default: () => [],
        },
        translations: {
            type: Object,
            default: () => {},
        },
        errors: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            rows: [{}],
        };
    },
    methods: {
        addNewRow() {
            let temp = {};
            Object.keys(this.fields).forEach((key) => {
                temp[key] = "";
            });
            this.rows.push(temp);
        },
        removeRow(index) {
            this.rows.splice(index, 1);
            if (this.rows.length === 0) {
                this.addNewRow();
            }
        },
    },
    created() {
        Object.keys(this.fields || {}).forEach((key) => {
            this.rows[0][key] = "";
        });
    },
    watch: {
        rows: {
            deep: true,
            handler(value) {
                let keys = Object.keys(this.fields);

                let temp = Object.assign(
                    {},
                    ...keys.map((key) => ({ [key]: [] }))
                );
                value.forEach((row) => {
                    keys.forEach((key) => {
                        temp[key].push(row[key]);
                    });
                });
                this.import_binds = {
                    ...this.import_binds,
                    ...temp,
                };
            },
        },
    },
};
</script>
<style>
.remove-from-multiple-csv {
    color: var(--Color-Text-Body---cc-color-text-error, #fc4f4e);
    text-align: center;
    font-size: 18px;
    font-style: normal;
    font-weight: 300;
    line-height: 20px;
}
</style>