<template>
    <div class="category-error">
        <SettingsCard :title="translations['Categories']">
            <SelectWithAjax
                v-model:val="modelValue.category_id"
                :api-url="categoryUrl"
                :can-clear="false"
                :cols-width="12"
                :column-style="true"
                :emit-on-get="true"
                :error="
                    translations[responseErrors['category_id']] ||
                    responseErrors['category_id']
                "
                :label="
                    translations[
                        'Select a category for the product or create a new one'
                    ]
                "
                :no-margin="true"
                :options="categoryOptions"
                :request-on-open="true"
                :request-on-search="true"
                :resolve-on-load="true"
                :searchable="true"
                @options="
                    (o) => {
                        additionalOptions = o;
                        categoryOptions = o;
                    }
                "
                @selected-item="handleRefreshData"
            />

            <div class="d-flex alin-items-center gap-4">
                <a
                    class="add-action-btn py-1"
                    href="javascript:void(0);"
                    @click="() => openModalCategory(null)"
                >
                    <i class="far fa-plus"></i>
                    {{ translations["Create new category"] }}
                </a>
                <a
                    class="add-action-btn py-1"
                    href="javascript:void(0);"
                    @click="() => openModalCategory(modelValue.category_id)"
                >
                    <i class="fa-light fa-pen"></i>
                    {{ resolveEditLabel }}
                </a>
            </div>
            <div class="card-bordered column p-3 gap-3">
                <div class="d-flex flex-column gap-1 gap-md-0">
                    <ActiveSwitch
                        v-model:is-active="additionalCategoriesEnabled"
                        :false-value="false"
                        :label-text="translations['Show also in']"
                        :true-value="true"
                        label-classes="label-500 m-0"
                        @click="handleAdditionalCategories"
                    />
                    <span
                        class="help-block"
                        v-html="translations['You can select up to 5 additional categories where this product will be shown']"></span>
                </div>
                <!--                <div class="d-flex flex-column gap-1">-->
                <!--                    <div class="d-flex flex-row align-items-baseline gap-2">-->
                <!--                        <TooltipLabel :hide-icon="true" :tooltip-text="hintText" >-->
                <!--                            <button class="btn btn-ghost btn-xs">-->
                <!--                                <i class="fa-light fa-triangle-exclamation hint-additional-categories"></i>-->
                <!--                            </button>-->
                <!--                        </TooltipLabel>-->
                <!--                        <span class="label-500 mb-2">{{ translations['Show also in'] }}</span>-->
                <!--                    </div>-->
                <!--                    <span-->
                <!--                        class="text-400-secondary">-->
                <!--                        {{-->
                <!--                            translations['You can select up to 5 additional categories where this product will be shown']-->
                <!--                        }}-->
                <!--                    </span>-->
                <!--                </div>-->
                <div class="info-box info-box-warning rounded border-0 m-0 p-3">
                    <p class="m-0" v-html="hintText"></p>
                </div>
                <Vue3SlideUpDown v-model="additionalCategoriesEnabled" :duration="130">
                    <div class="d-flex flex-column gap-3">
                        <div v-for="({id, index}) in otherCategoriesOptions"
                             class="d-flex flex-nowrap gap-3 align-items-center"
                        >
                            <div class="w-100">
                                <SelectWithAjax
                                    v-model:val="modelValue.other_categories[index]"
                                    :cols-width="12"
                                    :column-style="true"
                                    :error="translations[responseErrors['other_categories']] || responseErrors['other_categories']"
                                    :no-margin="true"
                                    :options="additionalOptions"
                                    :placeholder="$trp(translations[`Additional category {index}`], {index: index + 1})"
                                    :searchable="true"
                                    :request-on-search="true"
                                />
                            </div>
                            <div>
                                <a
                                    class="pt-1"
                                    href="javascript:void(0);"
                                    @click.stop="()=>removeAdditionalCategory(index)"
                                >
                                    <i
                                        :style="{color:'#7a7d84', 'font-size': '18px'}"
                                        class="fa-light fa-circle-xmark"
                                    ></i>
                                </a>
                            </div>
                        </div>
                        <a
                            v-if="otherCategoriesOptions.length < 5"
                            class="add-action-btn py-1 mt-1"
                            href="javascript:void(0);"
                            @click="addRowAdditionalCategory"
                        >
                            <i class="far fa-plus"></i>
                            {{ translations["Add additional category"] }}
                        </a>
                    </div>
                </Vue3SlideUpDown>

            </div>

            <Vue3SlideUpDown
                :duration="180"
                :model-value="!Boolean(properties?.length) && !loading"
            >
                <CategoryPropertiesAlert
                    v-model:wizardModal="wizardModal"
                    :is-managable="!!Number(category?.properties_count) > 0"
                    :manageProperty="manageProperty"
                    :translations="translations"
                    @update:manageProperty="checkAndOpenSidebar"
                />
            </Vue3SlideUpDown>
            <Vue3SlideUpDown
                :duration="180"
                :model-value="
                    Boolean(properties?.length) &&
                    Number(category?.properties_count) > 0 &&
                    !loading
                "
            >
                <MappedCategories
                    v-model:manageProperty="manageProperty"
                    v-model:wizardModal="wizardModal"
                    :properties="properties"
                    :translations="translations"
                />
            </Vue3SlideUpDown>
            <Vue3SlideUpDown :duration="180" :model-value="loading">
                <div class="position-relative" style="height: 130px">
                    <Loading :loading="loading" class="app-loader-center"/>
                </div>
            </Vue3SlideUpDown>
            <!-- <CreateOrEditModal v-model="modalCategory" :model="modelCategories" /> -->

            <CategoryPropertiesModal
                v-model="manageProperty"
                v-model:dataProperties="properties"
                :category-id="modelValue.category_id"
                :model="categoryPropertiesModel"
                :model-products="model"
                @update="
                    (val) => {
                        properties = val;
                    }
                "
            />
            <WizardCategoryModal
                v-model="wizardModal"
                :categoryId="modelValue.category_id"
                @update-values="
                    () => getCategoryInfo(this.modelValue.category_id)
                "
            />
        </SettingsCard>
        <ConfirmModal
            v-model="confirmSaveChanges"
            :message="
                translations[
                    'You need to save the products new category, before you can manage the properties.'
                ]
            "
            :no="translations['Cancel']"
            :submitLoader="submitLoader"
            :title="translations['Manage your category properties']"
            :yes="translations['Save']"
            confirmButtonClass="btn-primary"
            @cancel="rejectSaveChanges"
            @ok="saveAndOpen"
        >
            <template #icon>
                <i
                    class="fa-light fa-triangle-exclamation fs-6"
                    style="
                        color: var(
                            --Color-Border-Status---cc-color-border-status-caution,
                            #fcc400
                        );
                    "
                ></i>
            </template>
        </ConfirmModal>
    </div>
</template>

<script>
import useSharedState from "./../../composables/useSharedState";
import useSharedCategoriesModal from "./../../composables/useSharedCategoriesModal";
import SectionMixin from "../../js/SectionMixin";
import Categories from "../../../ProductCategories/js/Categories.js";
import {Vue3SlideUpDown} from "vue3-slide-up-down";
import Loading from "@components/Loading";

import SettingsCard from "@components/SettingsForm/Helpers/SettingsCard";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import ActiveSwitch from "@components/Form/ActiveSwitch";
// import CreateOrEditModal from "../../../ProductCategories/components/modals/CreateOrEditModal";
import CategoryProperties from "../../js/CategoryProperties.js";
import CategoryPropertiesModal from "./../Helpers/Categories/CategoryPropertiesModal";
import CategoryPropertiesAlert from "./../Helpers/Categories/CategoryPropertiesAlert";
import CategoryPropertiesList from "./../Helpers/Categories/CategoryPropertiesList";
import MappedCategories from "../Helpers/Categories/MappedCategories";
import WizardCategoryModal from "../../../CategoryProperty/components/Wizard/WizardCategoryModal";
import ConfirmModal from "@components/ConfirmModal";
import TooltipLabel from "@components/Apps/Erp/TooltipLabel";

export default {
    name: "SectionCategories",
    mixins: [SectionMixin],
    components: {
        SelectWithAjax,
        CategoryPropertiesModal,
        // CreateOrEditModal,
        CategoryPropertiesAlert,
        CategoryPropertiesList,
        SettingsCard,
        Vue3SlideUpDown,
        MappedCategories,
        WizardCategoryModal,
        Loading,
        ConfirmModal,
        TooltipLabel,
        ActiveSwitch
    },
    props: {
        resetData: {
            type: Boolean,
            default: false,
        },
        saveProduct: {
            type: Function,
            default: false,
        },
    },
    setup() {
        const {translations} = useSharedState();
        const {
            modalCategory,
            openModalCategory,
            categoryUrl,
            categoryOptions,
        } = useSharedCategoriesModal();

        return {
            translations,
            modalCategory,
            openModalCategory,
            categoryUrl,
            categoryOptions,
        };
    },
    data() {
        return {
            initialCategoryId: "",
            id: this.$route.params.id,
            modelCategories: new Categories(),
            categoryPropertiesModel: new CategoryProperties(),
            confirmSaveChanges: false,
            submitLoader: false,
            additionalCategoriesEnabled: false,
            categories: [],
            category: {},
            options: [],
            additionalOptions: [],
            properties: [],
            values: [],

            wizardModal: false,
            manageProperty: false,

            loading: false,
        };
    },
    mounted() {
        if (this.modelValue.category_id) {
            this.getCategoryInfo(this.modelValue.category_id);
        }
        this.properties = this.modelValue.properties;
        this.initialCategoryId = this.modelValue.category_id;

        if (this.modelValue?.other_categories?.length > 0) {
            this.additionalCategoriesEnabled = true;
        }
    },
    computed: {
        otherCategoriesOptions() {
            let arr = Array.isArray(this.modelValue.other_categories) ? this.modelValue.other_categories : []
            return arr.map((x, index) => ({id: x, index}));
        },
        hintText() {
            return this.translations['<strong>Note:</strong> Products added to \"Show Also In\" categories will appear in those categories on the storefront. However, they are not considered part of these categories for backend operations such as discounts, product widgets, or automated selections. Only the primary \"Category\" is used for such functionalities.']
        },
        resolveEditLabel() {
            let selectedCategory = this.categoryOptions.find(
                (category) => category.id == this.modelValue.category_id
            );
            if (selectedCategory) {
                return this.$trp(
                    this.translations['Edit "{category}" category'],
                    {
                        category: (selectedCategory.name || "")
                            .split(">")
                            .pop()
                            .trim(),
                    }
                );
            }

            return this.translations["Edit your product category"];
        },
    },
    methods: {
        async handleAdditionalCategories() {
            await new Promise((resolve) => setTimeout(resolve, 50));
            if (this.additionalCategoriesEnabled) {
                this.addRowAdditionalCategory()
            } else {
                this.modelValue.other_categories = [];
            }
        },
        removeAdditionalCategory(index) {
            this.modelValue.other_categories.splice(index, 1)
        },
        addRowAdditionalCategory() {
            if (this.modelValue.other_categories.length < 5) {
                this.modelValue.other_categories ??= []
                this.modelValue.other_categories.push(null);
            }
        },
        checkAndOpenSidebar(value) {
            if (this.modelValue.category_id === this.initialCategoryId) {
                this.manageProperty = value;
            } else {
                this.confirmSaveChanges = true;
            }
        },
        async saveAndOpen() {
            this.submitLoader = true;
            try {
                this.modelValue.properties = [];
                const response = await this.saveProduct();
                if (response) {
                    this.manageProperty = true;
                    this.initialCategoryId = this.modelValue.category_id;
                }
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.confirmSaveChanges = false;
                this.submitLoader = false;
            }
        },
        rejectSaveChanges() {
            this.confirmSaveChanges = false;
        },
        async handleRefreshData(option) {
            if (this.resetData || this.loading) {
                return;
            }

            this.properties = [];
            await this.getCategoryInfo(option.id);
        },
        async getCategoryInfo(id) {
            this.loading = true;
            try {
                this.category = await this.modelCategories.find(id);
            } catch (error) {
                console.error(error);
            } finally {
                this.loading = false;
            }
        },
        removeCategory(index) {
            this.modelValue.other_categories.splice(index, 1);
        },
        openModalCategoryProperties(state) {
            this.modal = state;
        },
    },
    emits: ["update:modelValue"],
};
</script>

<style scoped>
.remove-row-btn {
    font-size: 18px;
    padding: 8px;
    color: var(
        --Color-Surface-Action-Subtle---cc-color-bg-action-subtle,
        #aeb1cd
    );
    cursor: pointer;
}

.hint-additional-categories {
    color: var(
        --Color-Border-Status---cc-color-border-status-caution,
        #fcc400
    ) !important;
    text-align: center;
    font-style: normal;
    font-size: 14px !important;
    font-weight: 400 !important;
    line-height: 16px !important;
    margin: 1px -2px !important;
}
</style>
