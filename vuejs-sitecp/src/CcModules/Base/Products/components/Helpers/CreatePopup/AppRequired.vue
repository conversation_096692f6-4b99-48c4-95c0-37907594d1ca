<template>
    <b-col class="col-12 mb-3 d-flex flex-row w-100 justify-content-between">
        <a href="javascript:void(0);" @click="$emit('go-back')">
            <i class="fa-solid fa-chevron-left"></i>
            {{ translations.Back }}
        </a>

        <a class="ms-auto" href="javascript:void(0);" @click="$emit('toggle-modal')">
            <i
                style="font-size: 20px; color: #9b9ca3; font-weight: 300"
                class="fas fa-times"
            ></i>
        </a>
    </b-col>
    <b-col
        v-if="appLoading"
        class="col-12 d-flex align-items-center justify-content-center"
    >
        <Loading :loading="appLoading" />
    </b-col>
    <template v-else>
        <b-col class="col-12">
            <p class="label-500-18px mb-2 w-100 text-center">
                {{ translations["Reqired app"] }}
            </p>
            <p class="text-400-secondary mb-3 w-100 text-center text-wrap">
                {{
                    $trp(
                        translations[
                            "To create a product of this type requires {app} app, please install the following app:"
                        ],
                        { app: app.name }
                    )
                }}
            </p>
        </b-col>
        <b-col>
            <OptionCard>
                <template #thumbnail>
                    <img :src="app.icon" alt="logo" style="width: 40px; height: auto" />
                </template>
                <template #title>
                    {{ app.name }}
                </template>
                <template #append>
                    <button
                        class="btn btn-primary btn-small"
                        :disabled="installLoader"
                        @click="$emit('handle-action')"
                    >
                        <b-spinner small v-if="installLoader"></b-spinner>
                        {{ translations[app?.is_paid ? "Install" : "Purchase app"] }}
                    </button>
                </template>
            </OptionCard>
        </b-col>
    </template>
</template>

<script>
import OptionCard from "@components/OptionCard";
import Loading from "@components/Loading";

export default {
    name: "AppRequired",
    components: {
        OptionCard,
        Loading,
    },
    props: {
        app: Object,
        translations: Object,
        appLoading: Boolean,
        modal: Boolean,
        installLoader: Boolean,
    },
    emits: ["toggle-modal", "handle-action", "go-back"],
};
</script>
