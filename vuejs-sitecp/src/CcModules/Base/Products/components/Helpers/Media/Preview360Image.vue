<template>
    <b-row>
        <b-col class="col-12 col-md-6 d-flex align-items-center justify-content-center">
            <Loading :loading="loading" v-if="loading" />
            <div class="image-360-viewer" v-else>
                <canvas
                    ref="canvas"
                    @mousedown="startDrag"
                    @mousemove="onDrag"
                    @mouseup="stopDrag"
                    @mouseleave="stopDrag"
                ></canvas>
            </div>
        </b-col>

        <b-col class="col-12 col-md-6">
            <ActiveSwitch
                :is-active="item.is_primary ?? false"
                @update:is-active="(value) => (item.is_primary = value)"
                :trueValue="true"
                :falseValue="false"
                :label-text="translations['Make this the primary product image']"
            />

            <InputComponent
                v-model="item.name"
                :column-style="true"
                :label-text="translations['Image alt text']"
            />
        </b-col>
    </b-row>
</template>
<script>
import Loading from "@components/Loading";
import ActiveSwitch from "@components/Form/ActiveSwitch";
import InputComponent from "@components/Form/InputComponent";

export default {
    name: "Preview360Image",
    components: {
        ActiveSwitch,
        InputComponent,
        Loading,
    },
    props: {
        modelValue: {
            type: Object,
            default: {},
        },
        responseErrors: {
            type: Object,
            default: {},
        },
        media: {
            type: Object,
            default: {},
        },
    },
    data() {
        return {
            currentFrame: 0,
            dragging: false,
            startX: 0,

            images: [],
            loading: true,

            translations: {
                "Make this the primary product image": this.$t(
                    "Make this the primary product image"
                ),
                "Image alt text": this.$t("Image alt text"),
            },
            item: this.modelValue,
        };
    },
    mounted() {
        this.item = this.modelValue;
        this.images = this.getImages();
    },
    methods: {
        async getImages() {
            this.loading = true;

            try {
                const response = await this.media.find(
                    `360/${this.$route.params.id}/${this.item.id}`
                );
                let images = response.images;
                this.loadImages(images);
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.loading = false;
            }
        },
        loadImages(imageUrls) {
            const promises = imageUrls.map((url) => {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.src = url;
                    img.onload = () => resolve(img);
                    img.onerror = reject;
                });
            });

            Promise.all(promises)
                .then((loadedImages) => {
                    this.images = loadedImages;
                    this.drawFrame();
                })
                .catch((error) => {
                    console.error("Error loading images:", error);
                });
        },
        drawFrame() {
            const canvas = this.$refs.canvas;
            if (!canvas || this.images.length === 0) return;

            const context = canvas.getContext("2d");
            const img = this.images[this.currentFrame];

            canvas.width = img.width;
            canvas.height = img.height;
            context.clearRect(0, 0, canvas.width, canvas.height);
            context.drawImage(img, 0, 0, canvas.width, canvas.height);
        },
        startDrag(event) {
            this.dragging = true;
            this.startX = event.clientX;
        },
        onDrag(event) {
            if (this.dragging) {
                const deltaX = event.clientX - this.startX;
                const framesToMove = Math.floor(deltaX / 10); // Adjust sensitivity here
                if (framesToMove !== 0) {
                    this.currentFrame =
                        (this.currentFrame + framesToMove + this.images.length) %
                        this.images.length;
                    this.startX = event.clientX;
                    this.drawFrame();
                }
            }
        },
        stopDrag() {
            this.dragging = false;
        },
    },
    watch: {
        modelValue: {
            deep: true,
            handler(value) {
                this.item = value;
            },
        },
        item: {
            deep: true,
            handler(value) {
                this.$emit("update:modelValue", value);
            },
        },
    },
    emits: ["update:modelValue"],
};
</script>

<style>
.center-image-360 {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.cloudimage-360 {
    position: relative;
    width: 100%;
    cursor: grab;
    min-height: auto;
}

.cloudimage-inner-box {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.cloudimage-inner-box img {
    max-width: 100%;
    max-height: 100%;
    user-select: none;
    pointer-events: none;
}
</style>
