<template>
    <div id="toggle-product-variant">
        <div
            class="item"
            @click="
                () => {
                    if (type !== 'simple') {
                        toggleSimpleAndVariants('simple');
                    }
                }
            "
            :class="{ active: type === 'simple' }"
        >
            <div class="top" :class="{ active: type === 'simple' }"></div>
            <div class="content">
                <span class="responsive-label-500">
                    {{ translations["Simple Product"] }}
                </span>
                <figure
                    class="m-0 d-none d-md-block"
                    v-html="svgSimple"
                ></figure>
            </div>
            <div class="bottom" :class="{ active: type === 'simple' }"></div>
        </div>
        <div
            class="item"
            @click="toggleSimpleAndVariants('multiple')"
            :class="{ active: type === 'multiple' }"
        >
            <div class="top" :class="{ active: type === 'multiple' }"></div>
            <div class="content">
                <span class="responsive-label-500">
                    {{ translations["Product with variants"] }}
                </span>
                <figure
                    class="m-0 d-none d-md-block"
                    v-html="svgMultiple"
                ></figure>
            </div>
            <div class="bottom" :class="{ active: type === 'multiple' }"></div>
        </div>
    </div>
    <ConfirmModal
        v-model="confirmChangeTypeModal"
        :title="confirm.title"
        :message="confirm.description"
        :yes="translations['Confirm']"
        :no="translations['Cancel']"
        @ok="confirm.actionAccept"
        @cancel="rejectChangeType"
        confirmButtonClass="btn-primary"
    >
        <template #icon>
            <i
                class="fa-light fa-triangle-exclamation fs-6"
                style="
                    color: var(
                        --Color-Border-Status---cc-color-border-status-caution,
                        #fcc400
                    );
                "
            ></i>
        </template>
    </ConfirmModal>
</template>
<script>
import ConfirmModal from "@components/ConfirmModal.vue";

export default {
    name: "ToggleProductType",
    components: {
        ConfirmModal,
    },
    props: {
        product: {
            type: Object,
            default: {},
        },
        variants: {
            type: Object,
            default: {},
        },
    },
    data() {
        return {
            type: this.product?.type || "simple",
            translations: {
                "Simple Product": this.$t("Simple Product"),
                "Product with variants": this.$t("Product with variants"),
                "Set Product to Simple": this.$t("Set Product to Simple"),
                "This will remove the variants from you product and set it as a simple product.":
                    this.$t(
                        "This will remove the variants from you product and set it as a simple product."
                    ),
            },
            confirmChangeTypeModal: false,
            svgSimple: `<svg xmlns="http://www.w3.org/2000/svg" width="80" height="60" viewBox="0 0 80 60" fill="none">
                            <path d="M28.7985 12.4687C32.0639 11.036 69.2692 13.5319 70.9186 14.1079C72.5705 14.6839 72.7019 18.1734 71.7625 19.3013C71.7625 19.3013 73.3163 22.5844 71.8366 24.4684C70.3545 26.3523 51.3741 42.9718 47.4442 45.0261C43.5143 47.0804 6.96153 44.3493 4.42046 43.4854C1.87938 42.619 1.59492 36.1536 3.35192 33.7633C5.10891 31.3729 26.3244 13.5559 28.7985 12.4687Z" fill="#FF999C"/>
                            <path d="M71.9684 19.809C71.8536 19.4946 71.7628 19.3026 71.7628 19.3026C71.8847 19.1562 71.9875 18.9714 72.0712 18.7578C71.2201 19.3098 70.4361 19.9986 69.6448 20.6274C68.7819 21.3137 67.9213 22.0073 67.0631 22.7009C65.3898 24.052 63.7212 25.4128 62.0622 26.7832C58.7012 29.5575 55.3833 32.3798 52.0916 35.2381C51.176 36.0325 50.2724 36.8436 49.3688 37.6524C48.4365 38.4852 47.3919 39.2844 46.5672 40.2251C46.2062 40.6379 46.6461 41.4587 47.1792 41.0843C48.066 40.4579 48.8501 39.6635 49.6653 38.9436C50.4924 38.2116 51.3195 37.4844 52.149 36.7572C53.7673 35.3413 55.3713 33.9085 56.9944 32.4974C60.279 29.6367 63.5826 26.7976 66.9149 23.9968C67.8424 23.2169 68.7747 22.4393 69.707 21.6665C70.448 21.0521 71.2417 20.4594 71.9684 19.8114V19.809Z" fill="#F06969"/>
                            <path d="M45.8973 14.2262C45.6582 14.0534 42.1012 14.831 42.1012 14.831C38.8334 16.7725 20.7591 35.9719 18.7941 38.2087C17.3694 39.8262 17.1877 42.8285 18.1439 44.8948C20.8356 45.0868 23.7113 45.2644 26.5751 45.4084C25.8269 43.8509 25.3559 41.8493 26.2763 40.0398C28.1289 36.3991 47.6352 18.4045 47.9913 17.5933C48.3475 16.7821 46.1363 14.3966 45.8973 14.2238V14.2262Z" fill="#FC4F4E"/>
                            <path d="M40.4661 9.78806C43.6263 7.70733 55.158 5.27861 60.1302 10.1049C60.1302 10.1049 58.8274 14.1895 58.3182 14.0431C57.809 13.8967 48.3499 15.8815 47.5898 15.5959C46.8296 15.3103 40.7004 14.0575 40.196 13.7815C39.6916 13.5055 40.4661 9.78806 40.4661 9.78806Z" fill="#EE3137"/>
                            <path d="M40.4666 9.78906C37.0411 12.1194 35.2984 17.7376 35.5279 18.52C35.7574 19.3024 45.2261 14.6969 45.2261 14.6969C45.2261 14.6969 48.6922 20.7999 49.8205 21.6159C50.9488 22.4319 61.1131 18.664 62.0861 16.6336C63.059 14.6033 60.133 10.1083 60.133 10.1083C60.133 10.1083 52.3903 14.4785 46.4954 13.8809C40.6005 13.2834 40.469 9.79146 40.469 9.79146L40.4666 9.78906Z" fill="#F06969"/>
                            <path d="M44.2483 17.6975C44.21 17.6159 44.1598 17.5367 44.0953 17.4623C44.0355 17.3903 43.9566 17.3327 43.8801 17.2775C43.8706 17.2703 43.8634 17.2655 43.8538 17.2607C43.7845 17.2223 43.7056 17.1887 43.6315 17.1575C43.6076 17.1479 43.5837 17.1407 43.5598 17.1335C43.4761 17.1167 43.3901 17.0999 43.3064 17.0903C43.2945 17.0903 43.2849 17.0903 43.2729 17.0879C43.261 17.0879 43.249 17.0879 43.2395 17.0879C43.1534 17.0927 43.0626 17.0999 42.9789 17.1143C42.955 17.1191 42.9311 17.1239 42.9072 17.1311C42.8307 17.1575 42.7518 17.1863 42.6777 17.2175C42.6634 17.2223 42.6514 17.2295 42.6371 17.2367C42.5558 17.2823 42.4793 17.3423 42.4076 17.3999C42.3813 17.4215 42.355 17.4455 42.3335 17.4695C42.2474 17.5655 42.159 17.6663 42.0944 17.7815C42.0299 17.8967 41.9797 18.0167 41.9271 18.1367C41.8745 18.2591 41.8291 18.3766 41.8291 18.5158C41.8291 18.6166 41.8363 18.7174 41.8602 18.8158C41.9032 18.9886 41.9677 19.1014 42.0777 19.2382C42.0849 19.2478 42.0944 19.2574 42.104 19.267C42.1423 19.303 42.2188 19.3726 42.2642 19.399C42.2976 19.4206 42.3359 19.435 42.3717 19.4518C42.4172 19.4734 42.4554 19.495 42.5032 19.507C42.5128 19.507 42.5271 19.5118 42.5439 19.5142C42.5845 19.5334 42.6156 19.5502 42.6586 19.5622C42.6658 19.5622 42.8474 19.6054 42.9287 19.6054C42.9383 19.6054 42.9455 19.6054 42.955 19.6054C43.0363 19.6006 43.1271 19.5934 43.2084 19.579C43.3112 19.5622 43.4092 19.5142 43.5024 19.4734C44.1765 19.1734 44.5662 18.3503 44.253 17.6975H44.2483Z" fill="#663335"/>
                            <path d="M36.7873 24.5119C36.7491 24.4303 36.6965 24.3511 36.6343 24.2767C36.5746 24.2047 36.4957 24.1471 36.4192 24.0919C36.4096 24.0847 36.4025 24.0799 36.3929 24.0751C36.3236 24.0367 36.2447 24.0031 36.1706 23.9719C36.1467 23.9623 36.1228 23.9551 36.0989 23.9479C36.0176 23.9311 35.9291 23.9143 35.8455 23.9047C35.8335 23.9047 35.824 23.9047 35.812 23.9023C35.8001 23.9023 35.7881 23.9023 35.7785 23.9023C35.6925 23.9071 35.6017 23.9143 35.518 23.9287C35.4941 23.9335 35.4702 23.9383 35.4463 23.9455C35.3698 23.9719 35.2909 24.0007 35.2168 24.0319C35.2024 24.0367 35.1905 24.0439 35.1761 24.0511C35.0949 24.0967 35.0184 24.1567 34.9467 24.2143C34.9204 24.2359 34.8941 24.2599 34.8726 24.2839C34.7865 24.3799 34.698 24.4807 34.6335 24.5959C34.569 24.7111 34.5188 24.8311 34.4662 24.9511C34.4136 25.0735 34.3682 25.1911 34.3682 25.3303C34.3682 25.4311 34.3753 25.5319 34.3992 25.6303C34.4423 25.8031 34.5068 25.9159 34.6168 26.0527C34.6239 26.0623 34.6335 26.0719 34.6431 26.0815C34.6813 26.1175 34.7578 26.1871 34.8032 26.2135C34.8367 26.2351 34.8749 26.2495 34.9108 26.2663C34.9562 26.2879 34.9945 26.3095 35.0423 26.3215C35.0518 26.3215 35.0662 26.3263 35.0829 26.3287C35.1236 26.3479 35.1546 26.3647 35.1977 26.3767C35.2048 26.3767 35.3865 26.4199 35.4678 26.4199C35.4773 26.4199 35.4845 26.4199 35.4941 26.4199C35.5754 26.4151 35.6662 26.4079 35.7475 26.3935C35.8503 26.3767 35.9483 26.3287 36.0415 26.2879C36.7156 25.9879 37.1053 25.1647 36.7921 24.5119H36.7873Z" fill="#663335"/>
                            <path d="M29.3889 31.8225C29.3506 31.7409 29.298 31.6617 29.2359 31.5873C29.1761 31.5153 29.0972 31.4577 29.0208 31.4025C29.0112 31.3953 29.004 31.3905 28.9945 31.3857C28.9251 31.3473 28.8462 31.3137 28.7721 31.2825C28.7482 31.2729 28.7243 31.2657 28.7004 31.2585C28.6192 31.2417 28.5307 31.2249 28.447 31.2153C28.4351 31.2153 28.4255 31.2153 28.4136 31.2129C28.4016 31.2129 28.3897 31.2129 28.3801 31.2129C28.294 31.2177 28.2032 31.2249 28.1195 31.2393C28.0956 31.2441 28.0717 31.2489 28.0478 31.2561C27.9713 31.2825 27.8924 31.3113 27.8183 31.3425C27.804 31.3473 27.792 31.3545 27.7777 31.3617C27.6964 31.4073 27.6199 31.4673 27.5482 31.5249C27.5219 31.5465 27.4956 31.5705 27.4741 31.5945C27.3881 31.6905 27.2996 31.7913 27.2351 31.9065C27.1705 32.0193 27.1203 32.1417 27.0677 32.2617C27.0151 32.3841 26.9697 32.5016 26.9697 32.6408C26.9697 32.7416 26.9769 32.8424 27.0008 32.9408C27.0438 33.1136 27.1084 33.2264 27.2183 33.3632C27.2255 33.3728 27.2351 33.3824 27.2446 33.392C27.2829 33.428 27.3594 33.4976 27.4048 33.524C27.4383 33.5456 27.4765 33.56 27.5124 33.5768C27.5578 33.5984 27.596 33.62 27.6438 33.632C27.6534 33.632 27.6677 33.6368 27.6845 33.6392C27.7251 33.6584 27.7562 33.6752 27.7992 33.6872C27.8064 33.6872 27.9881 33.7304 28.0693 33.7304C28.0789 33.7304 28.0861 33.7304 28.0956 33.7304C28.1769 33.7256 28.2678 33.7184 28.349 33.704C28.4518 33.6872 28.5498 33.6392 28.6431 33.5984C29.3172 33.2984 29.7068 32.4753 29.3937 31.8225H29.3889Z" fill="#663335"/>
                            <path d="M23.1975 39.8401C23.1592 39.7585 23.109 39.6793 23.0445 39.6049C22.9847 39.5329 22.9058 39.4753 22.8293 39.4201C22.8198 39.4129 22.8126 39.4081 22.8031 39.4033C22.7337 39.3649 22.6548 39.3313 22.5807 39.3001C22.5568 39.2905 22.5329 39.2833 22.509 39.2761C22.4277 39.2593 22.3393 39.2425 22.2556 39.2329C22.2437 39.2329 22.2341 39.2329 22.2222 39.2305C22.2102 39.2305 22.1983 39.2305 22.1887 39.2305C22.1026 39.2353 22.0118 39.2425 21.9281 39.2569C21.9042 39.2617 21.8803 39.2665 21.8564 39.2737C21.7799 39.3001 21.701 39.3289 21.6269 39.3601C21.6126 39.3649 21.6006 39.3721 21.5863 39.3793C21.505 39.4249 21.4285 39.4849 21.3568 39.5425C21.3305 39.5641 21.3042 39.5881 21.2827 39.6121C21.1967 39.7081 21.1082 39.8089 21.0437 39.924C20.9791 40.0368 20.9289 40.1592 20.8763 40.2792C20.8237 40.4016 20.7783 40.5192 20.7783 40.6584C20.7783 40.7592 20.7855 40.86 20.8094 40.9584C20.8524 41.1312 20.917 41.244 21.0269 41.3808C21.0341 41.3904 21.0437 41.4 21.0532 41.4096C21.0915 41.4456 21.168 41.5152 21.2134 41.5416C21.2469 41.5632 21.2851 41.5776 21.321 41.5944C21.3664 41.616 21.4046 41.6376 21.4524 41.6496C21.462 41.6496 21.4763 41.6544 21.4931 41.6568C21.5337 41.676 21.5648 41.6928 21.6078 41.7048C21.615 41.7048 21.7967 41.748 21.8779 41.748C21.8875 41.748 21.8947 41.748 21.9042 41.748C21.9855 41.7432 22.0763 41.736 22.1576 41.7216C22.2604 41.7048 22.3584 41.6568 22.4517 41.616C23.1234 41.316 23.5154 40.4928 23.2023 39.8401H23.1975Z" fill="#663335"/>
                            <path d="M51.0395 30.0526C50.6187 29.7358 49.5693 29.6446 49.2346 29.5942C48.2378 29.4382 47.2458 29.2654 46.249 29.1094C45.7828 29.0374 45.3071 29.0566 44.841 28.9918C43.9828 28.8742 42.6011 28.2046 41.9533 29.1334C40.9564 30.5662 39.5604 31.8069 38.3939 33.1029C38.2648 33.2445 38.2648 33.5061 38.346 33.6693C38.6807 34.3412 39.0154 35.0132 39.3524 35.6852C39.6728 36.3284 39.9094 36.9428 40.7293 36.9164C41.4656 36.8924 42.2043 36.7892 42.9381 36.7244C43.6338 36.662 44.339 36.6308 45.0298 36.5324C45.7135 36.434 46.1653 36.0764 46.629 35.5868C47.1334 35.054 47.6283 34.514 48.1183 33.9693C48.5773 33.4605 51.618 30.487 51.0418 30.0526H51.0395Z" fill="#F06969"/>
                            <path d="M29.5587 13.497C28.3444 13.8858 27.3571 14.8914 26.3913 15.7002C25.3085 16.6049 24.2064 17.4833 23.1092 18.3665C21.6295 19.5592 23.7188 21.5944 25.1818 20.4472C26.2264 19.6288 27.2471 18.7817 28.2583 17.9249C29.1834 17.1425 30.3452 16.3241 30.8687 15.2082C31.2703 14.3514 30.5986 13.1634 29.5587 13.497Z" fill="#EBFAFF"/>
                            <path d="M21.7036 21.6199C21.5076 21.2839 21.1395 21.0703 20.7498 21.0703C20.3721 21.0703 19.9036 21.1831 19.5833 21.3847C19.2868 21.5719 19.0095 21.8071 18.7346 22.0207C18.388 22.2919 18.2542 22.9207 18.3928 23.3214C18.5075 23.6574 18.6892 23.943 19.0048 24.1206C19.5546 24.4326 20.1594 24.3078 20.6183 23.9118C20.702 23.8398 20.7857 23.7678 20.8717 23.6958C20.9171 23.6574 20.9649 23.619 21.0128 23.583C21.0343 23.5662 21.0558 23.5494 21.0869 23.5254C21.3307 23.3358 21.5745 23.0263 21.7036 22.7383C21.8709 22.3663 21.9187 21.9895 21.7036 21.6199Z" fill="#EBFAFF"/>
                            <line y1="-0.602215" x2="10.8758" y2="-0.602215" transform="matrix(0.363364 0.985542 -0.879732 0.330111 54.4531 20.7246)" stroke="#D6D9E9" stroke-width="1.20443"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M63.647 27.5853C64.2308 27.7537 64.7145 27.9678 65.1428 28.3213C65.5718 28.675 65.8746 29.1097 66.1523 29.6512C66.4209 30.176 66.6848 30.8393 67.015 31.6696L67.8488 33.7658C68.4248 35.2141 70.1163 39.2125 70.3523 40.1578C70.5969 41.1297 70.6335 41.9787 70.2813 42.7974C69.9288 43.617 69.2889 44.1703 68.4187 44.6549C67.5721 45.127 66.4298 45.5758 64.9865 46.1415L64.8984 46.176C63.4558 46.742 62.3128 47.1905 61.3702 47.4197C60.4011 47.6574 59.5525 47.6874 58.7319 47.3288C57.9114 46.9701 57.3539 46.3255 56.8626 45.4504C56.3855 44.5998 54.6936 40.602 54.1171 39.1526L53.2845 37.0561C52.9546 36.2269 52.6904 35.5624 52.525 34.9978C52.3554 34.4132 52.2774 33.8904 52.3468 33.3417C52.4166 32.7922 52.6223 32.3076 52.9326 31.7884C53.2323 31.2855 53.654 30.7135 54.1794 29.9987L54.8415 29.098C55.281 28.5019 55.6386 28.0152 55.9729 27.6489C56.3226 27.2656 56.6806 26.9718 57.1387 26.7921C57.5968 26.6123 58.0614 26.5834 58.5791 26.627C59.0755 26.6668 59.6715 26.7793 60.4022 26.9169L61.5053 27.1251C62.3814 27.2888 63.0822 27.4215 63.6473 27.5845" fill="#F2F2FA"/>
                            <ellipse cx="1.77621" cy="1.76252" rx="1.77621" ry="1.76252" transform="matrix(-0.719439 -0.680315 -0.677696 0.722669 60.9473 30.7617)" fill="#D6D9E9"/>
                            <path d="M64.0511 36.4586C63.9127 36.5144 63.8157 36.4731 63.7599 36.3347L62.153 32.3463C62.0972 32.2079 62.1385 32.1108 62.2769 32.055L64.0761 31.3301C64.1726 31.2913 64.2512 31.3473 64.3121 31.4983L64.3627 31.6241C64.4236 31.7751 64.4058 31.87 64.3093 31.9089L62.9882 32.4412L63.5053 33.7245L64.688 33.248C64.7802 33.2108 64.8568 33.2677 64.9176 33.4187L64.9683 33.5445C65.0291 33.6955 65.0134 33.7896 64.9211 33.8268L63.7385 34.3033L64.2961 35.6872L65.6171 35.155C65.7136 35.1161 65.7922 35.1722 65.8531 35.3232L65.9038 35.449C65.9646 35.6 65.9468 35.6949 65.8503 35.7337L64.0511 36.4586Z" fill="#B2B5D0"/>
                            <path d="M60.4282 37.9333C60.3234 37.9755 60.2428 37.9933 60.1865 37.9868C60.1328 37.9743 60.0957 37.9429 60.0755 37.8926L58.4179 33.7784C58.3756 33.6735 58.451 33.5822 58.6439 33.5045L58.7949 33.4437C58.8871 33.4065 58.9605 33.3891 59.0151 33.3915C59.0739 33.3922 59.1289 33.4139 59.1803 33.4566L61.4133 35.4963C61.4604 35.5407 61.5126 35.5977 61.5699 35.6672C61.6297 35.7309 61.6836 35.792 61.7316 35.8507L61.7542 35.8343C61.7297 35.7856 61.6972 35.7232 61.6567 35.6468C61.6205 35.5689 61.5871 35.4921 61.5567 35.4166L60.5505 32.9191C60.5302 32.8688 60.5359 32.8226 60.5677 32.7806C60.602 32.7326 60.6716 32.6875 60.7765 32.6453L60.8583 32.6123C60.9631 32.5701 61.0424 32.5552 61.0962 32.5677C61.1524 32.5742 61.1907 32.6027 61.211 32.653L62.8686 36.7672C62.9108 36.8721 62.8355 36.9634 62.6426 37.0411L62.4727 37.1095C62.3847 37.145 62.3142 37.1637 62.2613 37.1655C62.2109 37.1614 62.16 37.138 62.1087 37.0953L59.8618 35.0392C59.8206 34.9974 59.77 34.9446 59.7103 34.8809C59.6547 34.8156 59.6021 34.7515 59.5524 34.6886L59.5297 34.7051C59.5593 34.7663 59.5913 34.8338 59.626 34.9076C59.6648 34.9797 59.6985 35.0514 59.7272 35.1227L60.736 37.6265C60.7563 37.6768 60.7493 37.7259 60.715 37.7739C60.6832 37.8159 60.6148 37.8581 60.51 37.9003L60.4282 37.9333Z" fill="#B2B5D0"/>
                            <path d="M57.8607 39.0041C57.3364 39.2154 56.8637 39.1913 56.4424 38.9321C56.0253 38.6712 55.663 38.1591 55.3554 37.3958C55.0462 36.6283 54.9532 36.0102 55.0762 35.5413C55.2018 35.0666 55.5268 34.7237 56.051 34.5125C56.5752 34.3013 57.0451 34.324 57.4605 34.5807C57.8742 34.8333 58.2357 35.3433 58.5449 36.1108C58.8524 36.8741 58.9463 37.4943 58.8266 37.9715C58.7069 38.4487 58.3849 38.7929 57.8607 39.0041ZM57.6275 38.4254C57.9379 38.3003 58.1103 38.0749 58.1447 37.749C58.1833 37.4215 58.0861 36.9683 57.8529 36.3896C57.6214 35.815 57.379 35.4252 57.1258 35.2201C56.8751 35.0092 56.5945 34.9662 56.2842 35.0912C55.9696 35.218 55.793 35.4451 55.7544 35.7726C55.7183 36.0943 55.8159 36.5424 56.0474 37.117C56.2806 37.6958 56.5226 38.0906 56.7733 38.3016C57.0282 38.5108 57.313 38.5521 57.6275 38.4254Z" fill="#B2B5D0"/>
                            <path d="M66.4349 42.6286C66.2965 42.6843 66.1994 42.643 66.1437 42.5046L64.5368 38.5162C64.481 38.3778 64.5223 38.2807 64.6607 38.225L66.4599 37.5001C66.5564 37.4612 66.635 37.5173 66.6958 37.6682L66.7465 37.7941C66.8074 37.945 66.7895 38.04 66.6931 38.0788L65.372 38.6111L65.8891 39.8944L67.0717 39.4179C67.164 39.3807 67.2406 39.4377 67.3014 39.5886L67.3521 39.7145C67.4129 39.8654 67.3972 39.9595 67.3049 39.9967L66.1222 40.4732L66.6798 41.8572L68.0009 41.3249C68.0974 41.286 68.176 41.3421 68.2369 41.4931L68.2875 41.6189C68.3484 41.7699 68.3306 41.8648 68.2341 41.9037L66.4349 42.6286Z" fill="#B2B5D0"/>
                            <path d="M63.2689 43.9039C63.2018 43.931 63.1308 43.906 63.0559 43.8289C62.9851 43.7502 62.9346 43.673 62.9042 43.5976C62.8957 43.5766 62.8894 43.5548 62.8851 43.5321C62.8809 43.5094 62.8804 43.478 62.8836 43.4377L63.2629 39.4607L61.7342 40.0766C61.6377 40.1154 61.5591 40.0594 61.4982 39.9084L61.4476 39.7826C61.3867 39.6316 61.4045 39.5367 61.501 39.4978L63.6525 38.631C63.7196 38.604 63.7885 38.6298 63.8592 38.7086C63.9341 38.7856 63.9868 38.8619 64.0172 38.9374C64.0358 38.9835 64.0427 39.0368 64.0378 39.0973L63.6585 43.0743L65.2564 42.4305C65.3529 42.3916 65.4315 42.4477 65.4923 42.5987L65.543 42.7245C65.6039 42.8755 65.586 42.9704 65.4896 43.0092L63.2689 43.9039Z" fill="#B2B5D0"/>
                            <path d="M62.0927 44.3917C61.9879 44.4339 61.9073 44.4517 61.851 44.4452C61.7931 44.4344 61.7539 44.4039 61.7337 44.3535L60.0761 40.2393C60.0558 40.189 60.0636 40.1419 60.0996 40.0982C60.1339 40.0502 60.2035 40.0051 60.3084 39.9629L60.409 39.9224C60.5139 39.8801 60.5932 39.8652 60.6469 39.8777C60.7032 39.8843 60.7415 39.9127 60.7618 39.963L62.4194 44.0773C62.4396 44.1276 62.4326 44.1767 62.3983 44.2247C62.3665 44.2667 62.2982 44.3089 62.1934 44.3511L62.0927 44.3917Z" fill="#B2B5D0"/>
                            <path d="M59.9007 45.3046C59.7245 45.3756 59.5492 45.4243 59.3746 45.4508C59.1983 45.4731 59.0283 45.4684 58.8645 45.4369C58.7461 45.4213 58.6708 45.3736 58.6387 45.2939C58.6252 45.2604 58.612 45.2096 58.5993 45.1416C58.589 45.0678 58.5884 44.9998 58.5975 44.9376C58.6049 44.8713 58.6275 44.8305 58.6652 44.8153C58.703 44.8001 58.771 44.7995 58.8692 44.8135C58.9658 44.8233 59.0842 44.8268 59.2244 44.824C59.3629 44.8169 59.514 44.7804 59.6775 44.7145C59.8621 44.6402 59.9961 44.5252 60.0797 44.3697C60.1633 44.2142 60.162 44.0294 60.0758 43.8155C60.0167 43.6688 59.935 43.5628 59.8307 43.4975C59.7247 43.4281 59.6024 43.3847 59.4639 43.3675C59.3278 43.3443 59.1833 43.3245 59.0305 43.3081C58.8348 43.2943 58.6454 43.2658 58.4623 43.2226C58.2791 43.1794 58.1131 43.1 57.9642 42.9845C57.8136 42.8649 57.6893 42.6834 57.5913 42.4401C57.4882 42.1843 57.4561 41.9413 57.495 41.7112C57.5322 41.4768 57.6299 41.2717 57.7882 41.0958C57.9447 40.9158 58.1488 40.775 58.4005 40.6737C58.5347 40.6196 58.6756 40.5823 58.8234 40.5618C58.9753 40.5396 59.1155 40.5367 59.244 40.5532C59.286 40.5606 59.3196 40.5714 59.3448 40.5857C59.3701 40.5999 59.3903 40.6258 59.4055 40.6636C59.419 40.6971 59.4321 40.7479 59.4449 40.8159C59.4577 40.8839 59.4625 40.9502 59.4593 41.0148C59.4586 41.0736 59.4414 41.1098 59.4079 41.1233C59.3618 41.1419 59.2677 41.1505 59.1258 41.1492C58.9822 41.1437 58.8182 41.1781 58.6337 41.2524C58.4407 41.3301 58.3054 41.448 58.2277 41.6061C58.1484 41.7599 58.1467 41.9312 58.2227 42.1199C58.2819 42.2667 58.3657 42.3719 58.4741 42.4354C58.5826 42.499 58.707 42.5415 58.8473 42.563C58.9858 42.5802 59.1328 42.5941 59.2882 42.6047C59.483 42.6285 59.6699 42.6629 59.8489 42.7078C60.032 42.751 60.1968 42.8333 60.3432 42.9547C60.4938 43.0744 60.6181 43.2558 60.7161 43.4991C60.8715 43.8849 60.8741 44.2422 60.7238 44.5709C60.5777 44.8978 60.3033 45.1424 59.9007 45.3046Z" fill="#B2B5D0"/>
                        </svg>`,
            svgMultiple: `<svg xmlns="http://www.w3.org/2000/svg" width="80" height="61" viewBox="0 0 80 61" fill="none">
                            <g clip-path="url(#clip0_4007_54241)">
                              <path d="M38.2444 25.2859C41.0197 24.0682 72.6413 26.1895 74.0432 26.679C75.4471 27.1686 75.5589 30.1344 74.7604 31.0931C74.7604 31.0931 76.081 33.8834 74.8234 35.4846C73.5637 37.0858 57.4319 51.2111 54.0918 52.9571C50.7517 54.7031 19.6847 52.3819 17.525 51.6476C15.3653 50.9112 15.1235 45.4162 16.6168 43.3846C18.1101 41.353 36.1416 26.2099 38.2444 25.2859Z" fill="#FF999C"/>
                              <path d="M74.9353 31.5262C74.8377 31.259 74.7605 31.0958 74.7605 31.0958C74.8642 30.9714 74.9515 30.8143 75.0226 30.6328C74.2993 31.102 73.6329 31.6874 72.9604 32.2218C72.227 32.8051 71.4956 33.3946 70.7662 33.9841C69.344 35.1325 67.9259 36.289 66.5158 37.4537C63.6593 39.8117 60.8392 42.2104 58.0416 44.6397C57.2634 45.3149 56.4954 46.0043 55.7274 46.6917C54.9351 47.3995 54.0472 48.0788 53.3463 48.8783C53.0395 49.2292 53.4133 49.9268 53.8664 49.6086C54.6202 49.0762 55.2866 48.401 55.9794 47.7891C56.6823 47.167 57.3853 46.5489 58.0903 45.9309C59.4658 44.7275 60.8291 43.5097 62.2086 42.3104C65.0002 39.879 67.808 37.466 70.6402 35.0856C71.4285 34.4227 72.2209 33.7618 73.0133 33.105C73.6431 32.5828 74.3176 32.079 74.9353 31.5283V31.5262Z" fill="#F06969"/>
                              <path d="M52.7775 26.7811C52.5744 26.6342 49.5512 27.2951 49.5512 27.2951C46.7738 28.9452 31.412 45.2632 29.742 47.1642C28.5311 48.539 28.3767 51.0908 29.1893 52.847C31.4771 53.0102 33.9212 53.1611 36.3552 53.2835C35.7193 51.9597 35.319 50.2585 36.1012 48.7206C37.6758 45.6263 54.2546 30.3323 54.5573 29.6428C54.8601 28.9534 52.9807 26.9259 52.7775 26.779V26.7811Z" fill="#FC4F4E"/>
                              <path d="M48.1617 23.0082C50.8476 21.2398 60.6486 19.1755 64.8746 23.2775C64.8746 23.2775 63.7673 26.7491 63.3346 26.6247C62.9018 26.5003 54.8623 28.1871 54.2162 27.9444C53.5701 27.7017 48.3608 26.6369 47.9321 26.4024C47.5034 26.1678 48.1617 23.0082 48.1617 23.0082Z" fill="#FC4F4E"/>
                              <path d="M48.1619 23.0078C45.2504 24.9884 43.7693 29.7635 43.9643 30.4284C44.1594 31.0934 52.207 27.1791 52.207 27.1791C52.207 27.1791 55.153 32.3662 56.112 33.0597C57.0709 33.7532 65.7098 30.5508 66.5367 28.8252C67.3636 27.0995 64.8768 23.2791 64.8768 23.2791C64.8768 23.2791 58.2961 26.9935 53.2858 26.4856C48.2756 25.9777 48.1639 23.0099 48.1639 23.0099L48.1619 23.0078Z" fill="#FF999C"/>
                              <path d="M51.3754 29.731C51.3429 29.6616 51.3003 29.5943 51.2454 29.5311C51.1946 29.4699 51.1276 29.4209 51.0625 29.374C51.0544 29.3679 51.0483 29.3638 51.0402 29.3598C50.9813 29.3271 50.9142 29.2986 50.8512 29.272C50.8309 29.2639 50.8106 29.2578 50.7903 29.2516C50.7192 29.2374 50.646 29.2231 50.5749 29.2149C50.5648 29.2149 50.5566 29.2149 50.5465 29.2129C50.5363 29.2129 50.5262 29.2129 50.518 29.2129C50.4449 29.217 50.3677 29.2231 50.2966 29.2353C50.2763 29.2394 50.256 29.2435 50.2356 29.2496C50.1706 29.272 50.1036 29.2965 50.0406 29.323C50.0284 29.3271 50.0182 29.3332 50.0061 29.3394C49.937 29.3781 49.872 29.4291 49.811 29.4781C49.7887 29.4964 49.7663 29.5168 49.748 29.5372C49.6749 29.6188 49.5997 29.7045 49.5449 29.8024C49.49 29.8982 49.4473 30.0023 49.4026 30.1043C49.3579 30.2083 49.3193 30.3082 49.3193 30.4265C49.3193 30.5122 49.3254 30.5979 49.3457 30.6815C49.3823 30.8284 49.4372 30.9242 49.5306 31.0405C49.5367 31.0487 49.5449 31.0568 49.553 31.065C49.5855 31.0956 49.6505 31.1547 49.6891 31.1772C49.7176 31.1955 49.7501 31.2078 49.7805 31.222C49.8191 31.2404 49.8516 31.2588 49.8923 31.269C49.9004 31.269 49.9126 31.273 49.9268 31.2751C49.9614 31.2914 49.9878 31.3057 50.0243 31.3159C50.0304 31.3159 50.1848 31.3526 50.2539 31.3526C50.2621 31.3526 50.2681 31.3526 50.2763 31.3526C50.3454 31.3485 50.4226 31.3424 50.4916 31.3301C50.579 31.3159 50.6623 31.2751 50.7415 31.2404C51.3145 30.9854 51.6456 30.2858 51.3795 29.731H51.3754Z" fill="#663335"/>
                              <path d="M45.0346 35.522C45.0021 35.4526 44.9574 35.3853 44.9046 35.3221C44.8538 35.2609 44.7867 35.212 44.7217 35.165C44.7136 35.1589 44.7075 35.1548 44.6994 35.1508C44.6405 35.1181 44.5734 35.0896 44.5104 35.0631C44.4901 35.0549 44.4698 35.0488 44.4495 35.0427C44.3804 35.0284 44.3052 35.0141 44.2341 35.0059C44.224 35.0059 44.2158 35.0059 44.2057 35.0039C44.1955 35.0039 44.1854 35.0039 44.1772 35.0039C44.1041 35.008 44.0269 35.0141 43.9558 35.0263C43.9355 35.0304 43.9151 35.0345 43.8948 35.0406C43.8298 35.0631 43.7628 35.0875 43.6998 35.1141C43.6876 35.1181 43.6774 35.1243 43.6652 35.1304C43.5962 35.1691 43.5311 35.2201 43.4702 35.2691C43.4478 35.2874 43.4255 35.3078 43.4072 35.3282C43.3341 35.4098 43.2589 35.4955 43.204 35.5934C43.1492 35.6893 43.1065 35.7933 43.0618 35.8953C43.0171 35.9993 42.9785 36.0992 42.9785 36.2176C42.9785 36.3032 42.9846 36.3889 43.0049 36.4725C43.0415 36.6194 43.0964 36.7153 43.1898 36.8315C43.1959 36.8397 43.204 36.8478 43.2122 36.856C43.2447 36.8866 43.3097 36.9457 43.3483 36.9682C43.3767 36.9865 43.4092 36.9988 43.4397 37.0131C43.4783 37.0314 43.5108 37.0498 43.5515 37.06C43.5596 37.06 43.5718 37.064 43.586 37.0661C43.6205 37.0824 43.647 37.0967 43.6835 37.1069C43.6896 37.1069 43.844 37.1436 43.9131 37.1436C43.9212 37.1436 43.9273 37.1436 43.9355 37.1436C44.0045 37.1395 44.0817 37.1334 44.1508 37.1212C44.2382 37.1069 44.3215 37.0661 44.4007 37.0314C44.9737 36.7764 45.3048 36.0768 45.0387 35.522H45.0346Z" fill="#663335"/>
                              <path d="M38.7465 41.7388C38.714 41.6694 38.6693 41.6021 38.6165 41.5389C38.5657 41.4777 38.4987 41.4288 38.4336 41.3818C38.4255 41.3757 38.4194 41.3716 38.4113 41.3676C38.3524 41.3349 38.2853 41.3064 38.2223 41.2799C38.202 41.2717 38.1817 41.2656 38.1614 41.2595C38.0923 41.2452 38.0171 41.2309 37.946 41.2227C37.9359 41.2227 37.9277 41.2227 37.9176 41.2207C37.9074 41.2207 37.8973 41.2207 37.8891 41.2207C37.816 41.2248 37.7388 41.2309 37.6677 41.2431C37.6474 41.2472 37.627 41.2513 37.6067 41.2574C37.5417 41.2799 37.4747 41.3043 37.4117 41.3308C37.3995 41.3349 37.3893 41.341 37.3771 41.3472C37.3081 41.3859 37.2431 41.4369 37.1821 41.4859C37.1598 41.5042 37.1374 41.5246 37.1191 41.545C37.046 41.6266 36.9708 41.7123 36.9159 41.8102C36.8611 41.9061 36.8184 42.0101 36.7737 42.1121C36.729 42.2161 36.6904 42.316 36.6904 42.4344C36.6904 42.52 36.6965 42.6057 36.7168 42.6893C36.7534 42.8362 36.8083 42.932 36.9017 43.0483C36.9078 43.0565 36.9159 43.0646 36.9241 43.0728C36.9566 43.1034 37.0216 43.1625 37.0602 43.185C37.0886 43.2033 37.1212 43.2156 37.1516 43.2299C37.1902 43.2482 37.2227 43.2666 37.2634 43.2768C37.2715 43.2768 37.2837 43.2808 37.2979 43.2829C37.3325 43.2992 37.3589 43.3135 37.3954 43.3237C37.4015 43.3237 37.5559 43.3604 37.625 43.3604C37.6331 43.3604 37.6392 43.3604 37.6474 43.3604C37.7164 43.3563 37.7936 43.3502 37.8627 43.338C37.9501 43.3237 38.0334 43.2829 38.1126 43.2482C38.6856 42.9932 39.0167 42.2936 38.7506 41.7388H38.7465Z" fill="#663335"/>
                              <path d="M33.4838 48.5493C33.4513 48.48 33.4087 48.4127 33.3538 48.3494C33.303 48.2883 33.236 48.2393 33.1709 48.1924C33.1628 48.1863 33.1567 48.1822 33.1486 48.1781C33.0897 48.1455 33.0226 48.1169 32.9596 48.0904C32.9393 48.0822 32.919 48.0761 32.8987 48.07C32.8296 48.0557 32.7544 48.0414 32.6833 48.0333C32.6732 48.0333 32.665 48.0333 32.6549 48.0312C32.6447 48.0312 32.6346 48.0312 32.6264 48.0312C32.5533 48.0353 32.4761 48.0414 32.405 48.0537C32.3847 48.0578 32.3644 48.0618 32.344 48.068C32.279 48.0904 32.212 48.1149 32.149 48.1414C32.1368 48.1455 32.1266 48.1516 32.1145 48.1577C32.0454 48.1965 31.9804 48.2475 31.9194 48.2964C31.8971 48.3148 31.8747 48.3352 31.8564 48.3556C31.7833 48.4372 31.7081 48.5228 31.6533 48.6207C31.5984 48.7186 31.5557 48.8206 31.511 48.9226C31.4663 49.0266 31.4277 49.1266 31.4277 49.2449C31.4277 49.3306 31.4338 49.4162 31.4541 49.4999C31.4907 49.6467 31.5456 49.7426 31.639 49.8589C31.6451 49.867 31.6533 49.8752 31.6614 49.8833C31.6939 49.9139 31.7589 49.9731 31.7975 49.9955C31.826 50.0139 31.8585 50.0261 31.8889 50.0404C31.9275 50.0588 31.96 50.0771 32.0007 50.0873C32.0088 50.0873 32.021 50.0914 32.0352 50.0934C32.0698 50.1098 32.0962 50.124 32.1327 50.1342C32.1388 50.1342 32.2932 50.1709 32.3623 50.1709C32.3704 50.1709 32.3765 50.1709 32.3847 50.1709C32.4538 50.1669 32.531 50.1607 32.6 50.1485C32.6874 50.1342 32.7707 50.0934 32.8499 50.0588C33.4208 49.8038 33.754 49.1042 33.4879 48.5493H33.4838Z" fill="#663335"/>
                              <path d="M57.1475 40.2305C56.7899 39.9613 55.898 39.8838 55.6136 39.8409C54.7664 39.7083 53.9232 39.5615 53.076 39.4289C52.6798 39.3677 52.2755 39.384 51.8793 39.329C51.1499 39.229 49.9756 38.6599 49.425 39.4493C48.5778 40.667 47.3912 41.7216 46.3998 42.823C46.29 42.9434 46.29 43.1657 46.3591 43.3044C46.6436 43.8755 46.928 44.4467 47.2145 45.0178C47.4867 45.5645 47.6879 46.0866 48.3847 46.0642C49.0105 46.0438 49.6383 45.9561 50.262 45.901C50.8533 45.848 51.4526 45.8215 52.0398 45.7378C52.6209 45.6542 53.0049 45.3503 53.399 44.9342C53.8277 44.4814 54.2483 44.0224 54.6648 43.5594C55.0549 43.127 57.6392 40.5997 57.1496 40.2305H57.1475Z" fill="#FF999C"/>
                              <path d="M38.8907 26.1603C37.8586 26.4907 37.0195 27.3454 36.1987 28.0328C35.2783 28.8017 34.3417 29.5483 33.4092 30.2989C32.1515 31.3127 33.9272 33.0424 35.1707 32.0674C36.0585 31.3718 36.9261 30.6518 37.7855 29.9236C38.5717 29.2586 39.5592 28.5631 40.0041 27.6146C40.3454 26.8864 39.7745 25.8767 38.8907 26.1603Z" fill="#F9A7B5"/>
                              <path d="M32.2143 33.0667C32.0477 32.7811 31.7348 32.5996 31.4036 32.5996C31.0826 32.5996 30.6844 32.6955 30.4121 32.8668C30.1602 33.0259 29.9245 33.2258 29.6909 33.4074C29.3963 33.6378 29.2825 34.1723 29.4003 34.5129C29.4979 34.7985 29.6523 35.0412 29.9205 35.1921C30.3877 35.4573 30.9018 35.3512 31.2919 35.0147C31.363 34.9535 31.4341 34.8923 31.5072 34.8311C31.5458 34.7985 31.5865 34.7658 31.6271 34.7352C31.6454 34.7209 31.6637 34.7067 31.6901 34.6863C31.8973 34.5251 32.1045 34.262 32.2143 34.0172C32.3565 33.7011 32.3971 33.3808 32.2143 33.0667Z" fill="#F9A7B5"/>
                              <path d="M38.2444 17.8679C41.0197 16.6502 72.6413 18.7715 74.0432 19.2611C75.4471 19.7506 75.5589 22.7164 74.7604 23.6751C74.7604 23.6751 76.081 26.4655 74.8234 28.0667C73.5637 29.6679 57.4319 43.7931 54.0918 45.5391C50.7517 47.2852 19.6847 44.9639 17.525 44.2296C15.3653 43.4933 15.1235 37.9982 16.6168 35.9666C18.1101 33.935 36.1416 18.7919 38.2444 17.8679Z" fill="#D2BAF7"/>
                              <path d="M74.9353 24.1083C74.8377 23.841 74.7605 23.6779 74.7605 23.6779C74.8642 23.5534 74.9515 23.3964 75.0226 23.2148C74.2993 23.684 73.6329 24.2694 72.9604 24.8038C72.227 25.3872 71.4956 25.9767 70.7662 26.5661C69.344 27.7145 67.9259 28.8711 66.5158 30.0358C63.6593 32.3937 60.8392 34.7924 58.0416 37.2218C57.2634 37.8969 56.4954 38.5864 55.7274 39.2738C54.9351 39.9816 54.0472 40.6608 53.3463 41.4604C53.0395 41.8112 53.4133 42.5088 53.8664 42.1906C54.6202 41.6582 55.2866 40.9831 55.9794 40.3711C56.6823 39.749 57.3853 39.131 58.0903 38.5129C59.4658 37.3095 60.8291 36.0918 62.2086 34.8924C65.0002 32.461 67.808 30.048 70.6402 27.6676C71.4285 27.0047 72.2209 26.3438 73.0133 25.687C73.6431 25.1648 74.3176 24.661 74.9353 24.1103V24.1083Z" fill="#B38AF4"/>
                              <path d="M52.7775 19.3611C52.5744 19.2143 49.5512 19.8752 49.5512 19.8752C46.7738 21.5253 31.412 37.8433 29.742 39.7443C28.5311 41.1191 28.3767 43.6708 29.1893 45.4271C31.4771 45.5902 33.9212 45.7412 36.3552 45.8636C35.7193 44.5398 35.319 42.8386 36.1012 41.3006C37.6758 38.2064 54.2546 22.9123 54.5573 22.2229C54.8601 21.5335 52.9807 19.506 52.7775 19.3591V19.3611Z" fill="#8D58E0"/>
                              <path d="M48.1617 15.5903C50.8476 13.8218 60.6486 11.7576 64.8746 15.8595C64.8746 15.8595 63.7673 19.3312 63.3346 19.2067C62.9018 19.0823 54.8623 20.7692 54.2162 20.5264C53.5701 20.2837 48.3608 19.219 47.9321 18.9844C47.5034 18.7498 48.1617 15.5903 48.1617 15.5903Z" fill="#8D58E0"/>
                              <path d="M48.1619 15.5898C45.2504 17.5704 43.7693 22.3455 43.9643 23.0104C44.1594 23.6754 52.207 19.7611 52.207 19.7611C52.207 19.7611 55.153 24.9482 56.112 25.6417C57.0709 26.3352 65.7098 23.1328 66.5367 21.4072C67.3636 19.6816 64.8768 15.8611 64.8768 15.8611C64.8768 15.8611 58.2961 19.5755 53.2858 19.0676C48.2756 18.5597 48.1639 15.5919 48.1639 15.5919L48.1619 15.5898Z" fill="#B38AF4"/>
                              <path d="M51.3754 22.313C51.3429 22.2437 51.3003 22.1764 51.2454 22.1131C51.1946 22.0519 51.1276 22.003 51.0625 21.9561C51.0544 21.9499 51.0483 21.9459 51.0402 21.9418C50.9813 21.9091 50.9142 21.8806 50.8512 21.8541C50.8309 21.8459 50.8106 21.8398 50.7903 21.8337C50.7192 21.8194 50.646 21.8051 50.5749 21.797C50.5648 21.797 50.5566 21.797 50.5465 21.7949C50.5363 21.7949 50.5262 21.7949 50.518 21.7949C50.4449 21.799 50.3677 21.8051 50.2966 21.8174C50.2763 21.8214 50.256 21.8255 50.2356 21.8316C50.1706 21.8541 50.1036 21.8786 50.0406 21.9051C50.0284 21.9091 50.0182 21.9153 50.0061 21.9214C49.937 21.9601 49.872 22.0111 49.811 22.0601C49.7887 22.0784 49.7663 22.0988 49.748 22.1192C49.6749 22.2008 49.5997 22.2865 49.5449 22.3844C49.49 22.4803 49.4473 22.5843 49.4026 22.6863C49.3579 22.7903 49.3193 22.8903 49.3193 23.0086C49.3193 23.0942 49.3254 23.1799 49.3457 23.2635C49.3823 23.4104 49.4372 23.5063 49.5306 23.6225C49.5367 23.6307 49.5449 23.6389 49.553 23.647C49.5855 23.6776 49.6505 23.7368 49.6891 23.7592C49.7176 23.7776 49.7501 23.7898 49.7805 23.8041C49.8191 23.8224 49.8516 23.8408 49.8923 23.851C49.9004 23.851 49.9126 23.8551 49.9268 23.8571C49.9614 23.8734 49.9878 23.8877 50.0243 23.8979C50.0304 23.8979 50.1848 23.9346 50.2539 23.9346C50.2621 23.9346 50.2681 23.9346 50.2763 23.9346C50.3454 23.9305 50.4226 23.9244 50.4916 23.9122C50.579 23.8979 50.6623 23.8571 50.7415 23.8224C51.3145 23.5675 51.6456 22.8678 51.3795 22.313H51.3754Z" fill="#432F74"/>
                              <path d="M45.0346 28.1039C45.0021 28.0346 44.9574 27.9673 44.9046 27.9041C44.8538 27.8429 44.7867 27.7939 44.7217 27.747C44.7136 27.7409 44.7075 27.7368 44.6994 27.7327C44.6405 27.7001 44.5734 27.6715 44.5104 27.645C44.4901 27.6368 44.4698 27.6307 44.4495 27.6246C44.3804 27.6103 44.3052 27.596 44.2341 27.5879C44.224 27.5879 44.2158 27.5879 44.2057 27.5879C44.1955 27.5879 44.1854 27.5879 44.1772 27.5879C44.1041 27.592 44.0269 27.5981 43.9558 27.6103C43.9355 27.6144 43.9151 27.6185 43.8948 27.6246C43.8298 27.647 43.7628 27.6715 43.6998 27.698C43.6876 27.7021 43.6774 27.7082 43.6652 27.7144C43.5962 27.7531 43.5311 27.8041 43.4702 27.8531C43.4478 27.8714 43.4255 27.8918 43.4072 27.9122C43.3341 27.9938 43.2589 28.0795 43.204 28.1774C43.1492 28.2732 43.1065 28.3773 43.0618 28.4793C43.0171 28.5833 42.9785 28.6832 42.9785 28.8015C42.9785 28.8872 42.9846 28.9729 43.0049 29.0565C43.0415 29.2034 43.0964 29.2992 43.1898 29.4155C43.1959 29.4237 43.204 29.4318 43.2122 29.44C43.2447 29.4706 43.3097 29.5297 43.3483 29.5522C43.3767 29.5705 43.4092 29.5828 43.4397 29.597C43.4783 29.6154 43.5108 29.6338 43.5515 29.644C43.5596 29.644 43.5718 29.648 43.586 29.6501C43.6205 29.6664 43.647 29.6807 43.6835 29.6909C43.6896 29.6909 43.844 29.7276 43.9131 29.7276C43.9212 29.7276 43.9273 29.7276 43.9355 29.7276C44.0045 29.7235 44.0817 29.7174 44.1508 29.7051C44.2382 29.6909 44.3215 29.6501 44.4007 29.6154C44.9737 29.3604 45.3048 28.6608 45.0387 28.106L45.0346 28.1039Z" fill="#432F74"/>
                              <path d="M38.7465 34.3169C38.714 34.2476 38.6693 34.1803 38.6165 34.117C38.5657 34.0558 38.4987 34.0069 38.4336 33.96C38.4255 33.9538 38.4194 33.9498 38.4113 33.9457C38.3524 33.9131 38.2853 33.8845 38.2223 33.858C38.202 33.8498 38.1817 33.8437 38.1614 33.8376C38.0923 33.8233 38.0171 33.809 37.946 33.8009C37.9359 33.8009 37.9277 33.8009 37.9176 33.7988C37.9074 33.7988 37.8973 33.7988 37.8891 33.7988C37.816 33.8029 37.7388 33.809 37.6677 33.8213C37.6474 33.8253 37.627 33.8294 37.6067 33.8355C37.5417 33.858 37.4747 33.8825 37.4117 33.909C37.3995 33.9131 37.3893 33.9192 37.3771 33.9253C37.3081 33.964 37.2431 34.015 37.1821 34.064C37.1598 34.0824 37.1374 34.1027 37.1191 34.1231C37.046 34.2047 36.9708 34.2904 36.9159 34.3883C36.8611 34.4862 36.8184 34.5882 36.7737 34.6902C36.729 34.7942 36.6904 34.8942 36.6904 35.0125C36.6904 35.0981 36.6965 35.1838 36.7168 35.2674C36.7534 35.4143 36.8083 35.5102 36.9017 35.6264C36.9078 35.6346 36.9159 35.6428 36.9241 35.6509C36.9566 35.6815 37.0216 35.7407 37.0602 35.7631C37.0886 35.7815 37.1212 35.7937 37.1516 35.808C37.1902 35.8263 37.2227 35.8447 37.2634 35.8549C37.2715 35.8549 37.2837 35.859 37.2979 35.861C37.3325 35.8773 37.3589 35.8916 37.3954 35.9018C37.4015 35.9018 37.5559 35.9385 37.625 35.9385C37.6331 35.9385 37.6392 35.9385 37.6474 35.9385C37.7164 35.9344 37.7936 35.9283 37.8627 35.9161C37.9501 35.9018 38.0334 35.861 38.1126 35.8263C38.6856 35.5714 39.0167 34.8717 38.7506 34.3169H38.7465Z" fill="#432F74"/>
                              <path d="M33.4838 41.1293C33.4513 41.06 33.4087 40.9927 33.3538 40.9294C33.303 40.8683 33.236 40.8193 33.1709 40.7724C33.1628 40.7663 33.1567 40.7622 33.1486 40.7581C33.0897 40.7255 33.0226 40.6969 32.9596 40.6704C32.9393 40.6622 32.919 40.6561 32.8987 40.65C32.8296 40.6357 32.7544 40.6214 32.6833 40.6133C32.6732 40.6133 32.665 40.6133 32.6549 40.6133C32.6447 40.6133 32.6346 40.6133 32.6264 40.6133C32.5533 40.6174 32.4761 40.6235 32.405 40.6357C32.3847 40.6398 32.3644 40.6439 32.344 40.65C32.279 40.6724 32.212 40.6969 32.149 40.7234C32.1368 40.7275 32.1266 40.7336 32.1145 40.7397C32.0454 40.7785 31.9804 40.8295 31.9194 40.8784C31.8971 40.8968 31.8747 40.9172 31.8564 40.9376C31.7833 41.0192 31.7081 41.1049 31.6533 41.2028C31.5984 41.2986 31.5557 41.4027 31.511 41.5046C31.4663 41.6087 31.4277 41.7086 31.4277 41.8269C31.4277 41.9126 31.4338 41.9983 31.4541 42.0819C31.4907 42.2288 31.5456 42.3246 31.639 42.4409C31.6451 42.4491 31.6533 42.4572 31.6614 42.4654C31.6939 42.496 31.7589 42.5551 31.7975 42.5776C31.826 42.5959 31.8585 42.6082 31.8889 42.6224C31.9275 42.6408 31.96 42.6591 32.0007 42.6693C32.0088 42.6693 32.021 42.6734 32.0352 42.6755C32.0698 42.6918 32.0962 42.7061 32.1327 42.7163C32.1388 42.7163 32.2932 42.753 32.3623 42.753C32.3704 42.753 32.3765 42.753 32.3847 42.753C32.4538 42.7489 32.531 42.7428 32.6 42.7305C32.6874 42.7163 32.7707 42.6755 32.8499 42.6408C33.4208 42.3858 33.754 41.6862 33.4879 41.1314L33.4838 41.1293Z" fill="#432F74"/>
                              <path d="M57.1475 32.8126C56.7899 32.5433 55.898 32.4658 55.6136 32.423C54.7664 32.2904 53.9232 32.1435 53.076 32.0109C52.6798 31.9497 52.2755 31.9661 51.8793 31.911C51.1499 31.811 49.9756 31.2419 49.425 32.0313C48.5778 33.2491 47.3912 34.3036 46.3998 35.4051C46.29 35.5254 46.29 35.7477 46.3591 35.8864C46.6436 36.4576 46.928 37.0287 47.2145 37.5998C47.4867 38.1465 47.6879 38.6687 48.3847 38.6462C49.0105 38.6258 49.6383 38.5381 50.262 38.483C50.8533 38.43 51.4526 38.4035 52.0398 38.3199C52.6209 38.2362 53.0049 37.9323 53.399 37.5162C53.8277 37.0634 54.2483 36.6044 54.6648 36.1414C55.0549 35.709 57.6392 33.1817 57.1496 32.8126H57.1475Z" fill="#B38AF4"/>
                              <path d="M38.8907 18.7423C37.8586 19.0727 37.0195 19.9274 36.1987 20.6148C35.2783 21.3838 34.3417 22.1303 33.4092 22.8809C32.1515 23.8947 33.9272 25.6244 35.1707 24.6494C36.0585 23.9538 36.9261 23.2338 37.7855 22.5056C38.5717 21.8407 39.5592 21.1451 40.0041 20.1966C40.3454 19.4684 39.7745 18.4588 38.8907 18.7423Z" fill="#F7F2FF"/>
                              <path d="M32.2143 25.6468C32.0477 25.3612 31.7348 25.1797 31.4036 25.1797C31.0826 25.1797 30.6844 25.2756 30.4121 25.4469C30.1602 25.606 29.9245 25.8059 29.6909 25.9874C29.3963 26.2179 29.2825 26.7523 29.4003 27.093C29.4979 27.3785 29.6523 27.6213 29.9205 27.7722C30.3877 28.0374 30.9018 27.9313 31.2919 27.5947C31.363 27.5336 31.4341 27.4724 31.5072 27.4132C31.5458 27.3806 31.5865 27.3479 31.6271 27.3173C31.6454 27.3031 31.6637 27.2888 31.6901 27.2684C31.8973 27.1072 32.1045 26.8441 32.2143 26.5994C32.3565 26.2832 32.3971 25.9629 32.2143 25.6488V25.6468Z" fill="#F7F2FF"/>
                              <line y1="-0.602215" x2="10.025" y2="-0.602215" transform="matrix(-0.662163 0.704965 -0.749939 -0.705342 23.168 34.2988)" stroke="#D6D9E9" stroke-width="1.20443"/>
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M21.1661 44.1812C21.2716 44.7084 21.3046 45.1718 21.2144 45.6409C21.1243 46.1108 20.926 46.5087 20.6399 46.925C20.3625 47.3279 19.9823 47.7767 19.5064 48.3385L18.305 49.7569C17.4749 50.7368 15.2225 53.5231 14.626 54.0508C14.0135 54.5949 13.4092 54.9287 12.675 54.9408C11.9401 54.9529 11.2869 54.6398 10.5937 54.1179C9.91889 53.6105 9.14549 52.8583 8.1693 51.9074L8.10969 51.8494C7.13356 50.8992 6.3601 50.1463 5.82475 49.475C5.27284 48.7856 4.91931 48.1179 4.85996 47.3333C4.80061 46.5488 5.05244 45.8719 5.49805 45.1653C5.93138 44.4788 8.18303 41.6925 9.01378 40.7118L10.216 39.2942C10.6913 38.7331 11.0721 38.2835 11.4188 37.9488C11.7786 37.6036 12.129 37.3537 12.5559 37.2124C12.9835 37.0711 13.417 37.0617 13.9166 37.1236C14.4001 37.1827 14.9817 37.3148 15.7078 37.4784L16.6228 37.6846C17.2289 37.8221 17.7233 37.9335 18.1208 38.0693C18.5367 38.2114 18.8907 38.3922 19.2007 38.694C19.5107 38.9957 19.7133 39.3568 19.8839 39.7862C20.0489 40.1973 20.1998 40.7141 20.3851 41.3476L20.6645 42.3041C20.8876 43.0631 21.0647 43.671 21.1668 44.1812" fill="#F2F2FA"/>
                              <path d="M11.7481 47.4269C11.3509 46.8255 11.1283 46.3079 11.0804 45.874C11.037 45.4437 11.1511 45.0589 11.4228 44.7198C11.7281 44.3387 12.1046 44.1475 12.5525 44.1461C13.0004 44.1448 13.4679 44.3429 13.955 44.7406C14.3668 45.0769 14.6903 45.4137 14.9254 45.7511C15.0082 45.8672 15.0436 45.9966 15.0318 46.1393C15.0278 46.2815 14.9822 46.407 14.895 46.5159L14.8497 46.5724C14.7859 46.652 14.698 46.6841 14.5858 46.6686C14.477 46.6491 14.3945 46.5921 14.3383 46.4977C14.1034 46.1051 13.8133 45.7678 13.4679 45.4858C13.2465 45.305 13.0397 45.2157 12.8474 45.2181C12.6551 45.2204 12.4902 45.3073 12.3527 45.479C12.1112 45.7805 12.1465 46.1661 12.4588 46.6358C12.9123 47.3039 13.162 47.8471 13.2077 48.2654C13.2579 48.6874 13.1371 49.0805 12.8452 49.4448C12.1408 50.3241 11.2151 50.2955 10.0683 49.3591C9.70959 49.0663 9.42606 48.7482 9.21767 48.4049C9.14159 48.2804 9.10946 48.1468 9.12126 48.0041C9.1375 47.865 9.1909 47.7389 9.28147 47.6258L9.34186 47.5504C9.40559 47.4709 9.4863 47.4433 9.58399 47.4676C9.68503 47.4878 9.75977 47.5454 9.80821 47.6403C9.98976 48.0171 10.2422 48.3375 10.5654 48.6014C11.1278 49.0606 11.57 49.0892 11.892 48.6872C12.0228 48.5239 12.077 48.3465 12.0546 48.1551C12.0356 47.9594 11.9334 47.7167 11.7481 47.4269Z" fill="#AEB1CD"/>
                              <ellipse cx="1.4504" cy="1.54118" rx="1.4504" ry="1.54118" transform="matrix(0.270127 -0.961658 -0.950214 -0.304177 18.0957 43.0508)" fill="#D6D9E9"/>
                              <path d="M38.2444 10.4519C41.0197 9.23419 72.6413 11.3555 74.0432 11.8451C75.4471 12.3346 75.5589 15.3004 74.7604 16.2591C74.7604 16.2591 76.081 19.0494 74.8234 20.6506C73.5637 22.2518 57.4319 36.3771 54.0918 38.1231C50.7517 39.8691 19.6847 37.5479 17.525 36.8136C15.3653 36.0772 15.1235 30.5822 16.6168 28.5506C18.1101 26.519 36.1416 11.3759 38.2444 10.4519Z" fill="#89DFFA"/>
                              <path d="M74.9353 16.6903C74.8377 16.4231 74.7605 16.2599 74.7605 16.2599C74.8642 16.1355 74.9515 15.9784 75.0226 15.7969C74.2993 16.266 73.6329 16.8514 72.9604 17.3858C72.227 17.9692 71.4956 18.5587 70.7662 19.1482C69.344 20.2966 67.9259 21.4531 66.5158 22.6178C63.6593 24.9757 60.8392 27.3745 58.0416 29.8038C57.2634 30.479 56.4954 31.1684 55.7274 31.8558C54.9351 32.5636 54.0472 33.2428 53.3463 34.0424C53.0395 34.3932 53.4133 35.0908 53.8664 34.7726C54.6202 34.2403 55.2866 33.5651 55.9794 32.9532C56.6823 32.3311 57.3853 31.713 58.0903 31.095C59.4658 29.8915 60.8291 28.6738 62.2086 27.4744C65.0002 25.043 67.808 22.63 70.6402 20.2496C71.4285 19.5867 72.2209 18.9258 73.0133 18.269C73.6431 17.7469 74.3176 17.2431 74.9353 16.6923V16.6903Z" fill="#5FCCEE"/>
                              <path d="M52.7775 11.9451C52.5744 11.7983 49.5512 12.4591 49.5512 12.4591C46.7738 14.1093 31.412 30.4273 29.742 32.3283C28.5311 33.7031 28.3767 36.2548 29.1893 38.011C31.4771 38.1742 33.9212 38.3252 36.3552 38.4475C35.7193 37.1237 35.319 35.4226 36.1012 33.8846C37.6758 30.7903 54.2546 15.4963 54.5573 14.8069C54.8601 14.1175 52.9807 12.0899 52.7775 11.9431V11.9451Z" fill="#428EA6"/>
                              <path d="M48.1617 8.17425C50.8476 6.40579 60.6486 4.34156 64.8746 8.44349C64.8746 8.44349 63.7673 11.9151 63.3346 11.7907C62.9018 11.6663 54.8623 13.3532 54.2162 13.1104C53.5701 12.8677 48.3608 11.803 47.9321 11.5684C47.5034 11.3338 48.1617 8.17425 48.1617 8.17425Z" fill="#428EA6"/>
                              <path d="M48.1619 8.17383C45.2504 10.1544 43.7693 14.9295 43.9643 15.5944C44.1594 16.2594 52.207 12.3451 52.207 12.3451C52.207 12.3451 55.153 17.5322 56.112 18.2257C57.0709 18.9192 65.7098 15.7168 66.5367 13.9912C67.3636 12.2656 64.8768 8.44511 64.8768 8.44511C64.8768 8.44511 58.2961 12.1595 53.2858 11.6516C48.2756 11.1437 48.1639 8.17587 48.1639 8.17587L48.1619 8.17383Z" fill="#5FCCEE"/>
                              <path d="M51.3754 14.897C51.3429 14.8277 51.3003 14.7603 51.2454 14.6971C51.1946 14.6359 51.1276 14.587 51.0625 14.54C51.0544 14.5339 51.0483 14.5298 51.0402 14.5258C50.9813 14.4931 50.9142 14.4646 50.8512 14.4381C50.8309 14.4299 50.8106 14.4238 50.7903 14.4177C50.7192 14.4034 50.646 14.3891 50.5749 14.3809C50.5648 14.3809 50.5566 14.3809 50.5465 14.3789C50.5363 14.3789 50.5262 14.3789 50.518 14.3789C50.4449 14.383 50.3677 14.3891 50.2966 14.4013C50.2763 14.4054 50.256 14.4095 50.2356 14.4156C50.1706 14.4381 50.1036 14.4625 50.0406 14.4891C50.0284 14.4931 50.0182 14.4993 50.0061 14.5054C49.937 14.5441 49.872 14.5951 49.811 14.6441C49.7887 14.6624 49.7663 14.6828 49.748 14.7032C49.6749 14.7848 49.5997 14.8705 49.5449 14.9684C49.49 15.0663 49.4473 15.1683 49.4026 15.2703C49.3579 15.3743 49.3193 15.4743 49.3193 15.5926C49.3193 15.6782 49.3254 15.7639 49.3457 15.8475C49.3823 15.9944 49.4372 16.0903 49.5306 16.2065C49.5367 16.2147 49.5449 16.2228 49.553 16.231C49.5855 16.2616 49.6505 16.3207 49.6891 16.3432C49.7176 16.3615 49.7501 16.3738 49.7805 16.3881C49.8191 16.4064 49.8516 16.4248 49.8923 16.435C49.9004 16.435 49.9126 16.439 49.9268 16.4411C49.9614 16.4574 49.9878 16.4717 50.0243 16.4819C50.0304 16.4819 50.1848 16.5186 50.2539 16.5186C50.2621 16.5186 50.2681 16.5186 50.2763 16.5186C50.3454 16.5145 50.4226 16.5084 50.4916 16.4962C50.579 16.4819 50.6623 16.4411 50.7415 16.4064C51.3145 16.1514 51.6456 15.4518 51.3795 14.897H51.3754Z" fill="#26434D"/>
                              <path d="M45.0346 20.688C45.0021 20.6187 44.9574 20.5514 44.9046 20.4881C44.8538 20.4269 44.7867 20.378 44.7217 20.3311C44.7136 20.3249 44.7075 20.3209 44.6994 20.3168C44.6405 20.2841 44.5734 20.2556 44.5104 20.2291C44.4901 20.2209 44.4698 20.2148 44.4495 20.2087C44.3804 20.1944 44.3052 20.1801 44.2341 20.172C44.224 20.172 44.2158 20.172 44.2057 20.1699C44.1955 20.1699 44.1854 20.1699 44.1772 20.1699C44.1041 20.174 44.0269 20.1801 43.9558 20.1924C43.9355 20.1964 43.9151 20.2005 43.8948 20.2066C43.8298 20.2291 43.7628 20.2536 43.6998 20.2801C43.6876 20.2841 43.6774 20.2903 43.6652 20.2964C43.5962 20.3351 43.5311 20.3861 43.4702 20.4351C43.4478 20.4534 43.4255 20.4738 43.4072 20.4942C43.3341 20.5758 43.2589 20.6615 43.204 20.7594C43.1492 20.8573 43.1065 20.9593 43.0618 21.0613C43.0171 21.1653 42.9785 21.2653 42.9785 21.3836C42.9785 21.4692 42.9846 21.5549 43.0049 21.6385C43.0415 21.7854 43.0964 21.8813 43.1898 21.9975C43.1959 22.0057 43.204 22.0139 43.2122 22.022C43.2447 22.0526 43.3097 22.1118 43.3483 22.1342C43.3767 22.1526 43.4092 22.1648 43.4397 22.1791C43.4783 22.1974 43.5108 22.2158 43.5515 22.226C43.5596 22.226 43.5718 22.2301 43.586 22.2321C43.6205 22.2484 43.647 22.2627 43.6835 22.2729C43.6896 22.2729 43.844 22.3096 43.9131 22.3096C43.9212 22.3096 43.9273 22.3096 43.9355 22.3096C44.0045 22.3055 44.0817 22.2994 44.1508 22.2872C44.2382 22.2729 44.3215 22.2321 44.4007 22.1974C44.9737 21.9425 45.3048 21.2428 45.0387 20.688H45.0346Z" fill="#26434D"/>
                              <path d="M38.7465 26.9009C38.714 26.8316 38.6693 26.7642 38.6165 26.701C38.5657 26.6398 38.4987 26.5909 38.4336 26.544C38.4255 26.5378 38.4194 26.5338 38.4113 26.5297C38.3524 26.497 38.2853 26.4685 38.2223 26.442C38.202 26.4338 38.1817 26.4277 38.1614 26.4216C38.0923 26.4073 38.0171 26.393 37.946 26.3849C37.9359 26.3849 37.9277 26.3849 37.9176 26.3828C37.9074 26.3828 37.8973 26.3828 37.8891 26.3828C37.816 26.3869 37.7388 26.393 37.6677 26.4052C37.6474 26.4093 37.627 26.4134 37.6067 26.4195C37.5417 26.442 37.4747 26.4664 37.4117 26.493C37.3995 26.497 37.3893 26.5032 37.3771 26.5093C37.3081 26.548 37.2431 26.599 37.1821 26.648C37.1598 26.6663 37.1374 26.6867 37.1191 26.7071C37.046 26.7887 36.9708 26.8744 36.9159 26.9723C36.8611 27.0682 36.8184 27.1722 36.7737 27.2742C36.729 27.3782 36.6904 27.4782 36.6904 27.5965C36.6904 27.6821 36.6965 27.7678 36.7168 27.8514C36.7534 27.9983 36.8083 28.0942 36.9017 28.2104C36.9078 28.2186 36.9159 28.2267 36.9241 28.2349C36.9566 28.2655 37.0216 28.3247 37.0602 28.3471C37.0886 28.3654 37.1212 28.3777 37.1516 28.392C37.1902 28.4103 37.2227 28.4287 37.2634 28.4389C37.2715 28.4389 37.2837 28.443 37.2979 28.445C37.3325 28.4613 37.3589 28.4756 37.3954 28.4858C37.4015 28.4858 37.5559 28.5225 37.625 28.5225C37.6331 28.5225 37.6392 28.5225 37.6474 28.5225C37.7164 28.5184 37.7936 28.5123 37.8627 28.5001C37.9501 28.4858 38.0334 28.445 38.1126 28.4103C38.6856 28.1554 39.0167 27.4557 38.7506 26.9009H38.7465Z" fill="#26434D"/>
                              <path d="M33.4838 33.7154C33.4513 33.646 33.4087 33.5787 33.3538 33.5155C33.303 33.4543 33.236 33.4053 33.1709 33.3584C33.1628 33.3523 33.1567 33.3482 33.1486 33.3441C33.0897 33.3115 33.0226 33.2829 32.9596 33.2564C32.9393 33.2483 32.919 33.2421 32.8987 33.236C32.8296 33.2217 32.7544 33.2075 32.6833 33.1993C32.6732 33.1993 32.665 33.1993 32.6549 33.1973C32.6447 33.1973 32.6346 33.1973 32.6264 33.1973C32.5533 33.2013 32.4761 33.2075 32.405 33.2197C32.3847 33.2238 32.3644 33.2279 32.344 33.234C32.279 33.2564 32.212 33.2809 32.149 33.3074C32.1368 33.3115 32.1266 33.3176 32.1145 33.3237C32.0454 33.3625 31.9804 33.4135 31.9194 33.4624C31.8971 33.4808 31.8747 33.5012 31.8564 33.5216C31.7833 33.6032 31.7081 33.6888 31.6533 33.7868C31.5984 33.8826 31.5557 33.9866 31.511 34.0886C31.4663 34.1927 31.4277 34.2926 31.4277 34.4109C31.4277 34.4966 31.4338 34.5823 31.4541 34.6659C31.4907 34.8127 31.5456 34.9086 31.639 35.0249C31.6451 35.033 31.6533 35.0412 31.6614 35.0494C31.6939 35.08 31.7589 35.1391 31.7975 35.1615C31.826 35.1799 31.8585 35.1921 31.8889 35.2064C31.9275 35.2248 31.96 35.2431 32.0007 35.2533C32.0088 35.2533 32.021 35.2574 32.0352 35.2594C32.0698 35.2758 32.0962 35.29 32.1327 35.3002C32.1388 35.3002 32.2932 35.337 32.3623 35.337C32.3704 35.337 32.3765 35.337 32.3847 35.337C32.4538 35.3329 32.531 35.3268 32.6 35.3145C32.6874 35.3002 32.7707 35.2594 32.8499 35.2248C33.4208 34.9698 33.754 34.2702 33.4879 33.7154H33.4838Z" fill="#26434D"/>
                              <path d="M57.1475 25.3965C56.7899 25.1273 55.898 25.0498 55.6136 25.0069C54.7664 24.8744 53.9232 24.7275 53.076 24.5949C52.6798 24.5337 52.2755 24.55 51.8793 24.495C51.1499 24.395 49.9756 23.8259 49.425 24.6153C48.5778 25.833 47.3912 26.8876 46.3998 27.9891C46.29 28.1094 46.29 28.3317 46.3591 28.4704C46.6436 29.0416 46.928 29.6127 47.2145 30.1838C47.4867 30.7305 47.6879 31.2526 48.3847 31.2302C49.0105 31.2098 49.6383 31.1221 50.262 31.067C50.8533 31.014 51.4526 30.9875 52.0398 30.9039C52.6209 30.8202 53.0049 30.5163 53.399 30.1002C53.8277 29.6474 54.2483 29.1884 54.6648 28.7254C55.0549 28.293 57.6392 25.7657 57.1496 25.3965H57.1475Z" fill="#5FCCEE"/>
                              <path d="M38.8907 11.3263C37.8586 11.6567 37.0195 12.5114 36.1987 13.1988C35.2783 13.9678 34.3417 14.7143 33.4092 15.4649C32.1515 16.4787 33.9272 18.2084 35.1707 17.2334C36.0585 16.5378 36.9261 15.8178 37.7855 15.0896C38.5717 14.4247 39.5592 13.7291 40.0041 12.7806C40.3454 12.0524 39.7745 11.0428 38.8907 11.3263Z" fill="#EBFAFF"/>
                              <path d="M32.2143 18.2308C32.0477 17.9452 31.7348 17.7637 31.4036 17.7637C31.0826 17.7637 30.6844 17.8595 30.4121 18.0309C30.1602 18.19 29.9245 18.3899 29.6909 18.5714C29.3963 18.8019 29.2825 19.3363 29.4003 19.677C29.4979 19.9625 29.6523 20.2052 29.9205 20.3562C30.3877 20.6214 30.9018 20.5153 31.2919 20.1787C31.363 20.1175 31.4341 20.0563 31.5072 19.9952C31.5458 19.9625 31.5865 19.9299 31.6271 19.8993C31.6454 19.885 31.6637 19.8707 31.6901 19.8503C31.8973 19.6892 32.1045 19.4261 32.2143 19.1813C32.3565 18.8651 32.3971 18.5449 32.2143 18.2308Z" fill="#EBFAFF"/>
                              <line y1="-0.602215" x2="9.24359" y2="-0.602215" transform="matrix(0.363364 0.985542 -0.880013 0.329139 60.0488 17.4688)" stroke="#D6D9E9" stroke-width="1.20443"/>
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M67.8668 23.3018C68.3629 23.4452 68.7737 23.6275 69.1375 23.928C69.502 24.2289 69.7591 24.5983 69.9949 25.0587C70.2229 25.5047 70.4468 26.0685 70.7271 26.7741L71.4348 28.5557C71.9237 29.7866 73.3595 33.1851 73.5596 33.9884C73.767 34.8143 73.7978 35.5357 73.4982 36.2312C73.1983 36.9274 72.6543 37.3971 71.9146 37.8084C71.195 38.209 70.2242 38.5897 68.9974 39.0694L68.9226 39.0987C67.6965 39.5788 66.725 39.9592 65.924 40.1534C65.1004 40.3548 64.3792 40.3798 63.6821 40.0746C62.9849 39.7694 62.5115 39.2213 62.0944 38.4774C61.6893 37.7544 60.2533 34.3566 59.7639 33.1247L59.0572 31.3428C58.7773 30.6381 58.553 30.0733 58.4127 29.5935C58.2689 29.0966 58.2027 28.6524 58.262 28.1862C58.3215 27.7193 58.4965 27.3077 58.7604 26.8667C59.0152 26.4396 59.3739 25.9539 59.8206 25.3468L60.3836 24.5819C60.7573 24.0756 61.0614 23.6623 61.3456 23.3513C61.643 23.0258 61.9473 22.7764 62.3366 22.6239C62.726 22.4715 63.1208 22.4472 63.5607 22.4845C63.9825 22.5186 64.4889 22.6146 65.1098 22.732L66.0471 22.9095C66.7915 23.0491 67.3869 23.1623 67.8671 23.3011" fill="#F2F2FA"/>
                              <path d="M65.6547 37.069C65.5465 37.1116 65.4364 37.1082 65.3245 37.0588C65.2126 37.0095 65.1351 36.9301 65.092 36.8207L63.3604 32.4262C63.3173 32.3168 63.3199 32.2059 63.368 32.0936C63.4162 31.9813 63.4944 31.9038 63.6026 31.8612L63.7791 31.7918C63.9015 31.7436 64.0276 31.7325 64.1574 31.7583C64.2872 31.7842 64.3972 31.8425 64.4873 31.9334L66.7855 34.144C66.7874 34.1488 66.7907 34.1502 66.7954 34.1484C66.8001 34.1465 66.8015 34.1432 66.7996 34.1384L66.9726 30.9557C66.9766 30.8278 67.0172 30.7102 67.0945 30.6029C67.1718 30.4956 67.2717 30.4179 67.3941 30.3697L67.5988 30.2892C67.7071 30.2466 67.8171 30.25 67.929 30.2993C68.0409 30.3487 68.1184 30.4281 68.1615 30.5375L69.8931 34.932C69.9362 35.0414 69.9337 35.1523 69.8855 35.2646C69.8374 35.3769 69.7592 35.4544 69.6509 35.497L69.4956 35.5581C69.3873 35.6007 69.2772 35.5973 69.1654 35.5479C69.0535 35.4985 68.976 35.4191 68.9329 35.3097L67.7101 32.2065C67.7082 32.2017 67.7049 32.2003 67.7002 32.2021C67.6955 32.204 67.6941 32.2073 67.696 32.212L67.5712 34.3788C67.5625 34.5085 67.5181 34.6304 67.4379 34.7443C67.3606 34.8516 67.2631 34.9284 67.1454 34.9747L67.1031 34.9914C66.9854 35.0377 66.8593 35.0488 66.7248 35.0248C66.5932 34.9942 66.4799 34.9344 66.385 34.8454L64.8153 33.3452C64.8134 33.3405 64.8102 33.339 64.8054 33.3409C64.8007 33.3427 64.7993 33.346 64.8012 33.3508L66.024 36.4541C66.0671 36.5635 66.0646 36.6743 66.0164 36.7866C65.9683 36.899 65.8901 36.9764 65.7818 37.019L65.6547 37.069Z" fill="#AEB1CD"/>
                              <ellipse cx="1.50965" cy="1.49794" rx="1.50965" ry="1.49794" transform="matrix(-0.719103 -0.680787 -0.678059 0.72222 65.5684 26.002)" fill="#D6D9E9"/>
                            </g>
                            <defs>
                              <clipPath id="clip0_4007_54241">
                                <rect width="80" height="60" fill="white" transform="translate(0 0.5)"/>
                              </clipPath>
                            </defs>
                        </svg>`,
        };
    },
    computed: {
        confirm() {
            return {
                title: this.translations["Set Product to Simple"],
                description:
                    this.translations[
                        "This will remove the variants from you product and set it as a simple product."
                    ],
                actionAccept: this.setAsSimple,
            };
        },
    },
    methods: {
        toggleSimpleAndVariants(value) {
            this.type = value;
            if (value === "simple") {
                if (
                    this.product.variants[0]?.v1 ||
                    this.product.variants[0]?.v1_id
                ) {
                    this.confirmChangeTypeModal = true;
                } else {
                    this.setAsSimple();
                }
            } else {
                this.product.type = value;
                // this.product.variants[0].price = 0;
                // this.product.variants[0].discount = 0;
                // this.product.variants[0].save = 0;
                // this.product.variants[0].unit_value = 1;
                this.product.variants[0].minimum = 1;
                this.product.variants[0].unit_text = "";
            }
        },
        rejectChangeType() {
            this.confirmChangeTypeModal = false;
            this.type = this.product.type;
        },
        setAsSimple() {
            this.product.type = "simple";
            this.product.variants.length = 1;
            delete this.product?.variants?.[0]?.v1;
            delete this.product?.variants?.[0]?.v1_id;
            delete this.product?.variants?.[0]?.v1r;
            delete this.product?.variants?.[0]?.v2;
            delete this.product?.variants?.[0]?.v2_id;
            delete this.product?.variants?.[0]?.v2r;
            delete this.product?.variants?.[0]?.v3;
            delete this.product?.variants?.[0]?.v3_id;
            delete this.product?.variants?.[0]?.v3r;
            // this.product.variants[0].price = 0;
            // this.product.variants[0].discount = 0;
            // this.product.variants[0].save = 0;
            // this.product.variants[0].unit_value = 1;
            this.product.variants[0].unit_text = "";

            delete this.product?.p1;
            delete this.product?.p1_id;
            delete this.product?.p1r;
            delete this.product?.p2;
            delete this.product?.p2_id;
            delete this.product?.p2r;
            delete this.product?.p3;
            delete this.product?.p3_id;
            delete this.product?.p3r;

            this.$emit("update:variants", {
                p1r: {
                    value: "",
                    values_init: [],
                    options: [],
                    options_init: [],
                },
            });
            this.confirmChangeTypeModal = false;
        },
    },
    mounted() {
        this.type = this.product?.type || "simple";
    },
    watch: {
        product: {
            deep: true,
            handler(val) {
                this.type = val.type;
            },
        },
    },
    emits: ["update:modelValue", "update:variants"],
};
</script>
<style lang="scss">
@use "./../../../scss/style.scss" as *;
</style>
