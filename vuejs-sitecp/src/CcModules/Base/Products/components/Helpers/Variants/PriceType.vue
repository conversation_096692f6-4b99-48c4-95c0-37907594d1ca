<template>
    <div class="sell-type-wrapper">
        <div
            v-for="option in priceTypeOptions"
            :key="option.value"
            class="sell-type-card"
            :class="{ active: type === option.value }"
            @click="() => changeType(option.value)"
        >
            <div class="radio-wrapper">
                <input
                    type="radio"
                    :id="option.value"
                    :value="option.value"
                    name="sellType"
                    class="form-check-input"
                    :checked="type === option.value"
                />
            </div>
            <div class="d-flex flex-column gap-1 flex-grow-1">
                <span class="label-500" v-html="option.text"></span>
                <span
                    class="text-400-secondary text-wrap"
                    v-html="option.description"
                ></span>
            </div>
            <figure v-html="option.svg" class="m-0 rounded"></figure>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        modelValue: {
            required: false,
            default: null,
        },
        unit: {
            required: true,
            default: {},
        },
    },
    data() {
        return {
            type: this.modelValue,
            translations: {
                "Use Price per unit for products sold by kg, liter, meter etc. ":
                    this.$t(
                        "Use Price per unit for products sold by kg, liter, meter etc. "
                    ),
                "Use for products sold in predefined volumes (packages)":
                    this.$t(
                        "Use for products sold in predefined volumes (packages)"
                    ),
                "Price per measurement unit": this.$t(
                    "Price per measurement unit"
                ),
                "Price per package": this.$t("Price per package"),
            },
            packages: `
           <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 8C0 3.58172 3.58172 0 8 0H56C60.4183 0 64 3.58172 64 8V56C64 60.4183 60.4183 64 56 64H8C3.58172 64 0 60.4183 0 56V8Z" fill="white"/>
                <path d="M8.77344 38.1953V53.6792C8.77344 54.0898 8.93657 54.4837 9.22695 54.774C9.51733 55.0644 9.91117 55.2275 10.3218 55.2275H25.8057C26.2163 55.2275 26.6102 55.0644 26.9005 54.774C27.1909 54.4837 27.3541 54.0898 27.3541 53.6792V38.1953H8.77344Z" fill="#D2BAF7"/>
                <path d="M39.739 8.77344H50.5777C50.7831 8.77344 50.98 8.855 51.1252 9.00019C51.2704 9.14538 51.3519 9.3423 51.3519 9.54763V12.6444H38.9648V9.54763C38.9648 9.3423 39.0464 9.14538 39.1916 9.00019C39.3368 8.855 39.5337 8.77344 39.739 8.77344Z" fill="#D2BAF7"/>
                <path d="M23.4831 16.5156H12.6444C12.6433 18.4988 11.8553 20.4006 10.4534 21.8034L8.77344 23.4834H27.3541L25.6741 21.8034C24.2722 20.4006 23.4842 18.4988 23.4831 16.5156Z" fill="#D2BAF7"/>
                <path d="M34.6551 45.9365C32.5233 45.9379 30.4231 45.4215 28.5351 44.4316C26.6471 43.4417 25.0278 42.0081 23.8164 40.2539L24.8229 55.2268H40.7248L41.3441 45.9365H34.6551Z" fill="#D2BAF7"/>
                <path d="M34.656 45.9364H41.345L42.065 35.0977H23.4844L23.825 40.2538C25.0356 42.0069 26.6537 43.4399 28.5403 44.4297C30.4269 45.4195 32.5255 45.9365 34.656 45.9364Z" fill="white"/>
                <path d="M21.9355 32H43.6129V35.0968H21.9355V32Z" fill="#D2BAF7"/>
                <path d="M35.8714 42.8405C37.1541 42.8405 38.194 41.8006 38.194 40.5179C38.194 39.2352 37.1541 38.1953 35.8714 38.1953C34.5887 38.1953 33.5488 39.2352 33.5488 40.5179C33.5488 41.8006 34.5887 42.8405 35.8714 42.8405Z" fill="#D2BAF7"/>
                <path d="M35.8702 43.6115C36.4827 43.6115 37.0814 43.4299 37.5907 43.0896C38.0999 42.7493 38.4969 42.2657 38.7313 41.6998C38.9656 41.134 39.027 40.5113 38.9075 39.9106C38.788 39.3099 38.493 38.7581 38.06 38.325C37.6269 37.8919 37.0751 37.597 36.4744 37.4775C35.8736 37.358 35.251 37.4193 34.6851 37.6537C34.1193 37.8881 33.6356 38.285 33.2953 38.7943C32.9551 39.3035 32.7734 39.9023 32.7734 40.5147C32.7734 41.3361 33.0997 42.1237 33.6805 42.7045C34.2612 43.2852 35.0489 43.6115 35.8702 43.6115ZM35.8702 38.9664C36.1764 38.9664 36.4758 39.0572 36.7304 39.2273C36.9851 39.3974 37.1835 39.6393 37.3007 39.9222C37.4179 40.2051 37.4486 40.5165 37.3888 40.8168C37.3291 41.1172 37.1816 41.3931 36.9651 41.6096C36.7485 41.8262 36.4726 41.9736 36.1723 42.0334C35.8719 42.0931 35.5606 42.0625 35.2777 41.9453C34.9947 41.8281 34.7529 41.6296 34.5828 41.375C34.4126 41.1203 34.3218 40.821 34.3218 40.5147C34.3218 40.1041 34.485 39.7102 34.7753 39.4199C35.0657 39.1295 35.4596 38.9664 35.8702 38.9664Z" fill="#686B8B"/>
                <path d="M53.6774 29.1587L54.1341 28.5548C55.3404 26.9345 55.9944 24.9697 55.9999 22.9497C56.003 21.5502 55.6899 20.1681 55.084 18.9066C54.478 17.6451 53.5949 16.5368 52.5006 15.6645L52.129 15.3703V9.54839C52.129 9.13773 51.9659 8.74389 51.6755 8.45351C51.3851 8.16313 50.9913 8 50.5806 8H39.7419C39.3312 8 38.9374 8.16313 38.647 8.45351C38.3567 8.74389 38.1935 9.13773 38.1935 9.54839V15.3703L37.8219 15.6645C36.7276 16.5368 35.8445 17.6451 35.2385 18.9066C34.6326 20.1681 34.3195 21.5502 34.3226 22.9497C34.3281 24.9697 34.9821 26.9345 36.1884 28.5548L36.6451 29.1587V31.2258H26.8206C27.0449 30.3001 27.4273 29.4201 27.951 28.6245L27.9974 28.5548C28.0821 28.4288 28.1278 28.2808 28.129 28.129V23.4839C28.1296 23.382 28.1101 23.281 28.0716 23.1866C28.033 23.0923 27.9763 23.0065 27.9045 22.9342L26.2245 21.2542C25.1523 20.1897 24.4751 18.7915 24.3045 17.2903C24.5017 17.2785 24.6869 17.1917 24.8222 17.0478C24.9574 16.9038 25.0326 16.7136 25.0322 16.5161V13.4193C25.0322 13.214 24.9507 13.0171 24.8055 12.8719C24.6603 12.7267 24.4634 12.6452 24.2581 12.6452H11.871C11.6656 12.6452 11.4687 12.7267 11.3235 12.8719C11.1783 13.0171 11.0968 13.214 11.0968 13.4193V16.5161C11.0964 16.7136 11.1716 16.9038 11.3069 17.0478C11.4422 17.1917 11.6274 17.2785 11.8245 17.2903C11.6556 18.7942 10.9783 20.1954 9.90453 21.2619L8.22453 22.9342C8.15278 23.0065 8.09601 23.0923 8.05748 23.1866C8.01895 23.281 7.99942 23.382 8.00001 23.4839V28.129C8.0012 28.2808 8.04697 28.4288 8.13163 28.5548L8.17808 28.6245C9.07361 29.9679 9.5505 31.5467 9.5484 33.1613C9.5505 34.7758 9.07361 36.3546 8.17808 37.698L8.13163 37.7677C8.04697 37.8937 8.0012 38.0417 8.00001 38.1935V53.6774C8.00001 54.2933 8.24471 54.8841 8.68028 55.3197C9.11585 55.7552 9.7066 55.9999 10.3226 55.9999H53.6774C54.2934 55.9999 54.8841 55.7552 55.3197 55.3197C55.7552 54.8841 55.9999 54.2933 55.9999 53.6774V41.4761C56.0076 39.2806 55.1768 37.165 53.6774 35.5613V29.1587ZM39.7419 9.54839H50.5806V11.871H39.7419V9.54839ZM42.8387 32.7742V34.3225H22.7097V32.7742H42.8387ZM10.1058 28.9032H26.0232C25.6675 29.6413 25.4073 30.4218 25.249 31.2258H21.9355C21.7301 31.2258 21.5332 31.3073 21.388 31.4525C21.2429 31.5977 21.1613 31.7946 21.1613 32V35.0967C21.1613 35.3021 21.2429 35.499 21.388 35.6442C21.5332 35.7894 21.7301 35.8709 21.9355 35.8709H22.7561L22.8645 37.4193H10.1058C10.7582 36.0947 11.0972 34.6378 11.0968 33.1613C11.0972 31.6847 10.7582 30.2278 10.1058 28.9032ZM12.6452 14.1935H23.4839V15.7419H12.6452V14.1935ZM11.0039 22.3535C12.3772 21.0008 13.232 19.2089 13.4194 17.2903H22.7871C22.9639 19.2038 23.8048 20.995 25.1639 22.3535L25.5277 22.7097H10.64L11.0039 22.3535ZM9.5484 24.258H26.5806V27.3548H9.5484V24.258ZM10.3226 54.4515C10.1173 54.4515 9.92034 54.37 9.77515 54.2248C9.62996 54.0796 9.5484 53.8827 9.5484 53.6774V38.9677H22.9652L23.9948 54.4515H10.3226ZM39.9974 54.4515H25.551L24.7768 42.5754C26.0694 43.8819 27.6077 44.9198 29.3031 45.6293C30.9986 46.3389 32.8176 46.706 34.6555 46.7096H40.5161L39.9974 54.4515ZM40.6167 45.1612H34.6555C32.6864 45.1588 30.7462 44.687 28.9959 43.7849C27.2455 42.8828 25.7355 41.5765 24.591 39.9741L24.3123 35.8709H41.2361L40.6167 45.1612ZM54.4516 41.4761V53.6774C54.4516 53.8827 54.37 54.0796 54.2248 54.2248C54.0796 54.37 53.8827 54.4515 53.6774 54.4515H41.5535L42.7922 35.8709H43.6129C43.8182 35.8709 44.0151 35.7894 44.1603 35.6442C44.3055 35.499 44.3871 35.3021 44.3871 35.0967V32C44.3871 31.7946 44.3055 31.5977 44.1603 31.4525C44.0151 31.3073 43.8182 31.2258 43.6129 31.2258H41.2903L42.6838 29.3677C42.7448 29.2864 42.7892 29.1938 42.8145 29.0953C42.8397 28.9968 42.8453 28.8944 42.8309 28.7937C42.8165 28.6931 42.7825 28.5962 42.7307 28.5088C42.6789 28.4213 42.6103 28.3448 42.529 28.2838C42.4477 28.2228 42.3551 28.1785 42.2566 28.1532C42.1581 28.128 42.0556 28.1224 41.955 28.1368C41.8543 28.1512 41.7575 28.1852 41.67 28.237C41.5826 28.2888 41.5061 28.3573 41.4451 28.4387L39.3548 31.2258H38.1935V28.9032C38.1935 28.7357 38.1392 28.5727 38.0387 28.4387L37.4271 27.6645C36.2149 26.0454 35.6795 24.0187 35.9337 22.0122C36.1879 20.0057 37.212 18.1765 38.7896 16.911L39.4477 16.3845C39.5442 16.3084 39.6213 16.2105 39.6724 16.0987C39.7236 15.987 39.7474 15.8647 39.7419 15.7419V13.4193H50.5806V15.7419C50.5811 15.8582 50.6079 15.9729 50.6588 16.0774C50.7097 16.182 50.7836 16.2737 50.8748 16.3458L51.5329 16.8722C53.1117 18.1367 54.1372 19.9653 54.3929 21.9719C54.6486 23.9784 54.1144 26.0058 52.9032 27.6258L52.2916 28.4C52.1911 28.534 52.1367 28.697 52.1367 28.8645V35.8322C52.1361 35.9341 52.1557 36.0351 52.1942 36.1294C52.2327 36.2238 52.2895 36.3096 52.3612 36.3819C53.0324 37.0482 53.5632 37.8424 53.9222 38.7173C54.2813 39.5923 54.4613 40.5304 54.4516 41.4761Z" fill="#686B8B"/>
            </svg>
            `,
            topUnit: `
           <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 8C0 3.58172 3.58172 0 8 0H56C60.4183 0 64 3.58172 64 8V56C64 60.4183 60.4183 64 56 64H8C3.58172 64 0 60.4183 0 56V8Z" fill="white"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M15.5321 33.4737C15.5321 33.2586 15.3582 33.085 15.1432 33.085C14.9286 33.085 14.7549 33.2591 14.7549 33.4737C14.7549 33.6883 14.9287 33.8624 15.1432 33.8624C15.3581 33.8624 15.5321 33.6888 15.5321 33.4737ZM19.151 36.1515C18.9364 36.1515 18.7626 36.3258 18.7626 36.5402C18.7626 36.7551 18.936 36.9289 19.151 36.9289C19.3662 36.9289 19.5399 36.7555 19.5399 36.5402C19.5399 36.3256 19.3658 36.1515 19.151 36.1515ZM14.7669 28.7216C14.9817 28.7216 15.1557 28.5476 15.1557 28.3328C15.1557 28.1185 14.9812 27.9443 14.7669 27.9443C14.5523 27.9443 14.3785 28.1184 14.3785 28.3328C14.3785 28.5477 14.5518 28.7216 14.7669 28.7216ZM19.8778 31.7384C19.8778 31.5239 19.704 31.3497 19.4894 31.3497C19.2742 31.3497 19.1006 31.5231 19.1006 31.7384C19.1006 31.9532 19.2747 32.1269 19.4894 32.1269C19.7035 32.1269 19.8778 31.9524 19.8778 31.7384ZM26.8728 37.8063C27.0879 37.8063 27.2616 37.6326 27.2616 37.4176C27.2616 37.2031 27.0873 37.0289 26.8728 37.0289C26.6584 37.0289 26.4839 37.2032 26.4839 37.4176C26.4839 37.6324 26.6579 37.8063 26.8728 37.8063ZM30.9398 38.0044C30.9398 38.2189 31.1142 38.3931 31.3287 38.3931C31.5427 38.3931 31.7175 38.2184 31.7175 38.0044C31.7175 37.7898 31.5432 37.6157 31.3287 37.6157C31.1137 37.6158 30.9398 37.7894 30.9398 38.0044ZM23.198 34.8884C23.198 34.6733 23.0247 34.4999 22.8096 34.4999C22.5943 34.4999 22.4208 34.6731 22.4208 34.8884C22.4208 35.1032 22.5948 35.2771 22.8096 35.2771C23.0241 35.2771 23.198 35.103 23.198 34.8884ZM43.435 39.8341C44.3527 39.8341 45.2294 40.1352 45.9792 40.6602C46.113 40.7539 46.2908 40.7542 46.4246 40.6603C46.8454 40.3651 47.3077 40.1382 47.8025 39.9978L47.8896 39.9731L47.9753 40.002C48.5751 40.2043 49.2033 40.3084 49.8363 40.3084C50.119 40.3084 50.4016 40.2868 50.6814 40.2462L50.7696 40.2334L50.85 40.2722C51.8737 40.7663 52.6778 41.6147 53.2241 42.6018C53.8195 43.6779 54.1114 44.9151 54.1114 46.1421C54.1114 47.7259 53.6254 49.3362 52.6241 50.578C51.7179 51.7018 50.432 52.4508 48.9675 52.4508C48.9286 52.4508 48.8898 52.4489 48.8508 52.4479L48.7493 52.4455L48.6713 52.3806C48.5517 52.2811 48.4226 52.1944 48.2842 52.1232L38.3395 47.0107L38.3253 46.8508C38.3043 46.6152 38.2926 46.3786 38.2926 46.142C38.2926 44.5584 38.7786 42.9484 39.7797 41.7069C40.6854 40.5834 41.971 39.8341 43.435 39.8341ZM12.9799 36.8622C11.0862 33.1877 11.0696 28.8087 12.9401 25.1219L13.0794 24.8475L35.3872 37.7269L35.2197 37.9845C33.9223 39.9805 32.1217 41.5587 29.971 42.58L29.8408 42.6418L27.3265 41.3487C25.5714 40.4462 23.5257 40.4132 21.7243 41.2111C20.9234 41.5659 20.1979 42.0672 19.5818 42.6897L19.4408 42.8321L19.2574 42.7515C18.9541 42.6182 18.6562 42.4723 18.364 42.3161L18.2738 42.2679L18.2335 42.1739C18.0121 41.6576 17.7579 41.1541 17.4772 40.6675C16.9102 39.685 16.1757 38.6623 15.336 37.899C14.8535 37.4604 14.0145 36.8287 13.3121 37.0108L13.0866 37.0693L12.9799 36.8622ZM17.687 45.9666C17.379 46.2422 16.5693 46.2978 16.1789 46.3061C15.159 46.3277 14.0858 46.1415 13.104 45.8784C12.1222 45.6153 11.0996 45.2398 10.2269 44.7111C9.89338 44.509 9.21975 44.0558 9.09081 43.6638L9.03125 43.4826L9.1734 43.3554C9.48141 43.08 10.2906 43.0239 10.6811 43.0156C11.7011 42.9936 12.7746 43.1799 13.7563 43.4428C14.7383 43.7058 15.7609 44.0813 16.6334 44.6105C16.967 44.8128 17.64 45.2661 17.7693 45.6578L17.8292 45.8391L17.687 45.9666ZM24.7121 18.2008C26.968 18.771 29.3378 19.8593 30.6963 21.8304L30.966 22.2216L30.495 22.2847C29.5221 22.4151 28.5708 22.6743 27.6662 23.0553L27.383 23.1745L27.2789 22.8854C26.7041 21.2887 25.7187 19.8264 24.4476 18.7016L23.5493 17.9068L24.7121 18.2008ZM39.5455 18.7054C38.2781 19.829 37.2955 21.288 36.7204 22.8808L36.6163 23.169L36.3337 23.0506C35.4287 22.6719 34.4776 22.4131 33.5051 22.2827L33.0339 22.2195L33.3041 21.8282C34.6613 19.8626 37.0301 18.7759 39.2805 18.2047L40.4432 17.9096L39.5455 18.7054ZM31.727 18.8109L31.9987 19.5149L32.2708 18.811C32.5834 18.0022 33.0092 17.2473 33.5434 16.5643L33.6123 16.4763L33.6046 16.3647C33.5122 15.025 33.3378 13.6751 33.0301 12.3668C32.8406 11.5608 32.5963 10.748 32.2663 9.9873L31.999 9.37109L31.7315 9.9873C31.4016 10.7474 31.1574 11.5604 30.968 12.3659C30.6602 13.6745 30.4856 15.0247 30.3928 16.3648L30.385 16.4766L30.4541 16.5647C30.9889 17.2471 31.4148 18.002 31.727 18.8109ZM30.9233 20.8416L31.6419 21.7032L31.4335 20.6007C30.9776 18.189 29.6528 16.3312 27.5832 15.0209C27.1074 14.7197 26.6064 14.4581 26.0897 14.2341L25.1669 13.8341L25.7329 14.6656C26.4305 15.6906 26.9016 16.8914 27.2124 18.0873L27.2477 18.2234L27.3762 18.2804C28.7196 18.8773 29.9756 19.7053 30.9233 20.8416ZM36.6212 18.2804L36.7494 18.2235L36.7849 18.0876C37.0965 16.8919 37.5674 15.691 38.2641 14.6654L38.8292 13.8337L37.9069 14.2343C37.3908 14.4585 36.8902 14.7201 36.4147 15.021C34.3443 16.3311 33.0208 18.1888 32.5648 20.6008L32.3562 21.7048L33.0752 20.8415C34.0219 19.7049 35.2784 18.8771 36.6212 18.2804Z" fill="#D2BAF7"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M23.4908 34.887C23.4908 35.2621 23.1868 35.5672 22.811 35.5672C22.4347 35.5672 22.1307 35.2621 22.1307 34.887C22.1307 34.5109 22.4347 34.207 22.811 34.207C23.1868 34.207 23.4908 34.5109 23.4908 34.887ZM30.6497 38.003C30.6497 37.6271 30.9543 37.3228 31.3301 37.3228C31.7049 37.3228 32.0104 37.6271 32.0104 38.003C32.0104 38.378 31.705 38.6832 31.3301 38.6832C30.9543 38.6833 30.6497 38.378 30.6497 38.003ZM27.5545 37.4161C27.5545 37.7922 27.25 38.0964 26.8742 38.0964C26.4988 38.0964 26.1938 37.7922 26.1938 37.4161C26.1938 37.0411 26.4988 36.736 26.8742 36.736C27.25 36.7361 27.5545 37.0411 27.5545 37.4161ZM20.1706 31.737C20.1706 32.1121 19.8656 32.4171 19.4907 32.4171C19.1144 32.4171 18.8104 32.1121 18.8104 31.737C18.8104 31.3611 19.1144 31.0569 19.4907 31.0569C19.8657 31.0569 20.1706 31.3611 20.1706 31.737ZM14.7683 29.0117C14.3925 29.0117 14.0884 28.7076 14.0884 28.3314C14.0884 27.9564 14.3925 27.6514 14.7683 27.6514C15.1436 27.6514 15.4486 27.9564 15.4486 28.3314C15.4486 28.7075 15.1436 29.0117 14.7683 29.0117ZM15.8249 33.4723C15.8249 33.8483 15.5204 34.1525 15.1446 34.1525C14.7697 34.1525 14.4648 33.8484 14.4648 33.4723C14.4648 33.0962 14.7698 32.7921 15.1446 32.7921C15.5204 32.7922 15.8249 33.0963 15.8249 33.4723ZM19.8327 36.5389C19.8327 36.9148 19.5287 37.2191 19.1524 37.2191C18.7766 37.2191 18.4726 36.9148 18.4726 36.5389C18.4726 36.1639 18.7766 35.8587 19.1524 35.8587C19.5287 35.8587 19.8327 36.1639 19.8327 36.5389ZM48.969 52.1577C48.932 52.1577 48.8963 52.1558 48.8593 52.1549C48.7263 52.0442 48.5805 51.945 48.419 51.8624L38.6169 46.8235C38.5968 46.5972 38.5854 46.3687 38.5854 46.1405C38.5854 42.8233 40.7617 40.1242 43.4363 40.1242C44.2693 40.1242 45.0901 40.3914 45.8133 40.8975C46.0474 41.0618 46.3593 41.0618 46.5934 40.8975C46.9993 40.6128 47.4319 40.4049 47.8836 40.2767C48.52 40.4913 49.1789 40.5983 49.8378 40.5983C50.134 40.5983 50.4303 40.5759 50.7248 40.5331C52.5847 41.4309 53.8215 43.655 53.8215 46.1405C53.8214 49.4586 51.645 52.1577 48.969 52.1577ZM48.2754 54.0389C48.3764 53.659 48.1849 53.2713 47.7971 53.0721L40.2697 49.2021L39.5926 51.7292C39.5111 52.0334 39.236 52.2336 38.936 52.2336C38.8775 52.2336 38.8185 52.2258 38.7591 52.2102C38.3966 52.113 38.1812 51.7399 38.279 51.3776L39.032 48.5657L27.5882 42.6834L26.1307 48.1225C26.0493 48.4267 25.775 48.6268 25.4741 48.6268C25.4156 48.6268 25.3575 48.619 25.2981 48.6035C24.9346 48.5063 24.7198 48.1332 24.8171 47.7709L26.3438 42.0742C24.9996 41.5427 23.5333 41.5777 22.1584 42.1869C20.5071 42.9185 19.2904 44.3458 18.8195 46.1035C18.3335 47.9175 18.6696 49.8006 19.7422 51.2717C20.7256 52.6203 22.1809 53.4074 23.8405 53.4879L30.4129 53.8095L31.4511 49.9337C31.5485 49.5713 31.9216 49.3564 32.2841 49.4527C32.6471 49.5499 32.8621 49.923 32.7651 50.2864L31.8032 53.8765L47.3783 54.6373C47.8127 54.6569 48.1734 54.4179 48.2754 54.0389ZM14.3061 49.5869C12.0635 50.8812 10.5013 51.0132 10.1785 50.8499C10.2 50.4906 11.0947 49.2022 13.3364 47.9079C13.7543 47.6669 14.1758 47.4532 14.5928 47.2685C15.1008 47.3385 15.5895 47.3755 16.0394 47.3755C16.2035 47.3755 16.364 47.3715 16.5176 47.3608C16.7407 47.3472 16.9423 47.323 17.1257 47.2908C16.5902 48.0119 15.536 48.8766 14.3061 49.5869ZM9.36912 43.5714C9.63846 43.3305 11.181 43.0536 13.6824 43.7231C16.1833 44.3935 17.3812 45.406 17.4941 45.748C17.2249 45.9888 15.6809 46.2659 13.1808 45.5954C10.68 44.9259 9.48163 43.9135 9.36912 43.5714ZM11.0558 24.0135L12.0219 24.5712C9.8872 28.7258 9.9727 33.7026 12.2696 37.7912C12.1741 38.1798 12.1635 38.7016 12.3053 39.4041C12.3519 39.6393 12.416 39.885 12.4919 40.1386C9.00388 35.5827 8.34142 29.2515 11.0558 24.0135ZM24.1533 26.9711L26.8266 29.6431L25.6186 30.8508L22.7497 29.1941C23.1228 28.3867 23.5969 27.6416 24.1533 26.9711ZM29.2461 23.6395C27.6962 24.0835 26.2687 24.8919 25.0892 25.9819L27.7886 28.6812L31.0384 25.4321L29.2461 23.6395ZM24.642 18.482C26.63 18.9844 29.077 19.9909 30.4577 21.9944C29.4569 22.1285 28.482 22.3947 27.5544 22.7853C27.1618 21.6942 26.3233 19.9696 24.642 18.482ZM25.9753 14.5002C26.7351 15.6166 27.2057 16.8954 27.496 18.0127C28.8302 18.6054 30.1446 19.4498 31.1486 20.6536C30.7161 18.3655 29.4702 16.5582 27.4288 15.266C26.9364 14.954 26.4416 14.7023 25.9753 14.5002ZM32.0003 10.1018C32.463 11.1686 33.0926 13.1554 33.3152 16.3834C32.8032 17.038 32.3473 17.8068 32.0003 18.7046C31.6537 17.8068 31.1979 17.038 30.6849 16.3834C30.9085 13.1545 31.5381 11.1666 32.0003 10.1018ZM36.5719 15.2659C34.5296 16.5581 33.2851 18.3654 32.8526 20.6535C33.8553 19.4497 35.1702 18.6054 36.5043 18.0126C36.7955 16.8952 37.2661 15.6165 38.0245 14.5001C37.559 14.7023 37.0648 14.954 36.5719 15.2659ZM36.4476 22.7805C35.5332 22.3978 34.5598 22.1286 33.5452 21.9926C34.9232 19.9968 37.3669 18.9902 39.3535 18.486C37.6788 19.9706 36.8412 21.6903 36.4476 22.7805ZM39.5237 40.4177C40.2826 39.684 41.1893 39.1584 42.1823 38.9155V36.5757L40.4248 34.8171L37.5202 37.7223L38.4053 38.2334C38.5617 38.3226 38.6755 38.4714 38.7222 38.6463C38.7688 38.8202 38.7445 39.0058 38.6541 39.1623C38.6074 39.244 38.5581 39.3226 38.5096 39.4032L39.5237 40.4177ZM36.1573 38.5054C34.9311 40.4118 33.286 41.9684 31.3292 43.0769L32.4951 43.6764C34.35 42.5027 35.9347 40.9218 37.1229 39.0632L36.1573 38.5054ZM31.3319 34.1495L26.8395 31.5554L27.7887 30.6052L31.3319 34.1495ZM35.2506 29.6431L32.0003 32.8932L28.7505 29.6431L32.0003 26.393L35.2506 29.6431ZM36.2125 28.6812L38.9224 25.9714C37.7566 24.8881 36.3369 24.0748 34.7637 23.6308L32.9623 25.4321L36.2125 28.6812ZM35.8838 36.7768L32.9623 33.8552L36.2125 30.6051L39.4629 33.8552L36.2999 37.0178L35.8838 36.7768ZM40.4248 32.8943L37.1746 29.6432L39.8569 26.9606C40.8871 28.21 41.6263 29.7082 41.9683 31.3493L40.4248 32.8943ZM42.1823 33.4315V34.6519L41.3863 33.8553L42.1755 33.0662C42.1791 33.1878 42.1823 33.3092 42.1823 33.4315ZM32.0003 23.2487C32.3908 23.2487 32.7758 23.2721 33.1547 23.315L32.0003 24.4702L30.8473 23.3167C31.2272 23.2721 31.6126 23.2487 32.0003 23.2487ZM15.2594 42.7942C16.1024 43.1004 16.8587 43.4667 17.4552 43.8629C17.5627 43.934 17.661 44.005 17.7543 44.0747C17.5609 43.2401 17.1343 42.2093 16.5523 41.2008C15.258 38.9593 13.971 38.0632 13.6102 38.0428C13.4479 38.3656 13.5791 39.9279 14.8744 42.1703C14.9969 42.3833 15.1272 42.5911 15.2594 42.7942ZM27.4612 41.0881L29.8474 42.3153C31.951 41.3163 33.7084 39.7754 34.9767 37.8243L13.2014 25.2523C11.3735 28.8551 11.389 33.1352 13.2403 36.7273C14.6119 36.3717 16.4864 38.3645 17.731 40.5205C18.0273 41.0345 18.2861 41.5524 18.5028 42.0576C18.7881 42.2102 19.0797 42.353 19.3761 42.4832C20.0089 41.8438 20.7605 41.3182 21.6077 40.9431C23.5091 40.1007 25.642 40.1533 27.4612 41.0881ZM33.8776 44.3867L37.2258 46.1075C37.2317 44.3439 37.7625 42.7241 38.6408 41.4581L37.7457 40.5633C36.6771 42.0441 35.3672 43.3393 33.8776 44.3867ZM50.2757 35.1892C51.619 34.4138 53.2114 34.3332 54.6123 34.9521C54.4482 36.4745 53.5813 37.8125 52.2375 38.589C51.1639 39.2089 49.9318 39.3828 48.7651 39.1126L51.5975 37.4783C51.9216 37.2899 52.0337 36.8739 51.8462 36.5485C51.6588 36.224 51.2418 36.1122 50.9171 36.2997L48.0847 37.9349C48.4345 36.7894 49.2012 35.8091 50.2757 35.1892ZM52.435 40.0182C52.5983 39.9415 52.7592 39.8578 52.9178 39.7666C54.7997 38.6802 55.9523 36.7332 55.9999 34.5577C56.0049 34.3049 55.8687 34.0688 55.6474 33.9474C53.7399 32.901 51.4767 32.9253 49.5958 34.0115C48.3125 34.752 47.3688 35.8927 46.8828 37.2334V34.4411C46.8828 34.066 46.5788 33.7608 46.203 33.7608C45.8281 33.7608 45.5226 34.066 45.5226 34.4411V39.1903C44.8903 38.9242 44.2219 38.7794 43.5424 38.7658V33.4315C43.5424 29.1291 41.1764 25.3688 37.6765 23.384C38.1272 22.0538 39.418 19.3701 42.7478 17.9322C43.0468 17.8018 43.2133 17.4773 43.1411 17.1587C43.0693 16.8399 42.7817 16.6165 42.4543 16.6282C42.2929 16.6331 40.3466 16.7136 38.1081 17.4123C38.5762 16.0694 39.4083 14.4673 40.8395 13.6403C41.121 13.4782 41.2486 13.1399 41.1435 12.8329C41.0397 12.525 40.7315 12.3345 40.4101 12.3773C40.3178 12.3897 38.1132 12.695 35.8888 14.0883C35.4567 14.3593 35.0068 14.6839 34.5643 15.0687C34.0339 10.1804 32.5969 8.33706 32.5311 8.25476C32.4018 8.09328 32.2065 8 32.0003 8C31.7945 8 31.5988 8.09328 31.47 8.25467C31.4041 8.33696 29.9658 10.1803 29.4364 15.0686C28.9943 14.6838 28.5444 14.3592 28.1123 14.0882C25.8871 12.6949 23.6833 12.3896 23.591 12.3772C23.2682 12.3344 22.9614 12.5249 22.8576 12.8328C22.7525 13.1398 22.8796 13.4781 23.1603 13.6402C24.5927 14.4671 25.4244 16.0684 25.893 17.4122C23.6541 16.7135 21.7078 16.633 21.5454 16.6281C21.2181 16.6144 20.9305 16.8397 20.8597 17.1586C20.7879 17.4772 20.9529 17.8017 21.2533 17.9321C24.589 19.3719 25.8775 22.0634 26.326 23.3908C26.021 23.5647 25.723 23.7512 25.4322 23.9534C23.7551 25.1203 22.4306 26.6855 21.566 28.5113L11.1335 22.487C10.9772 22.3977 10.7915 22.3734 10.6178 22.42C10.4427 22.4667 10.294 22.5804 10.2036 22.7367C8.21793 26.1773 7.54536 30.2173 8.3112 34.1127C8.89865 37.1004 10.2982 39.8483 12.3277 42.0596C10.2708 41.7702 8.36834 42.0043 8.04371 43.2159C7.64641 44.6977 10.0229 46.0929 12.5056 46.8187C10.2387 48.1635 8.20831 50.1806 8.99096 51.5359C9.19714 51.8935 9.63612 52.2628 10.572 52.2628C10.8491 52.2628 11.1687 52.2306 11.5398 52.1558C12.5668 51.9489 13.7906 51.4543 14.9862 50.7645C15.9043 50.2339 16.7177 49.6402 17.355 49.0458C17.547 50.1311 17.9804 51.1659 18.6434 52.0733C19.8733 53.76 21.6961 54.7453 23.7732 54.8472L47.311 55.9958H47.3124C47.3513 55.9977 47.391 55.9986 47.4308 55.9986C48.4541 55.9986 49.3314 55.3505 49.5888 54.3906C49.6716 54.0835 49.6821 53.7726 49.6309 53.4753C52.7459 53.0819 55.1818 49.9434 55.1818 46.1405C55.1819 43.653 54.1208 41.3669 52.435 40.0182Z" fill="#686B8B"/>
            </svg>
            `,
        };
    },
    computed: {
        priceTypeOptions() {
            return [
                {
                    value: "measured",
                    text: this.translations["Price per measurement unit"],
                    svg: this.topUnit,
                    description:
                        this.translations[
                            "Use Price per unit for products sold by kg, liter, meter etc. "
                        ],
                },
                {
                    value: "countable",
                    text: this.translations["Price per package"],
                    svg: this.packages,
                    description:
                        this.translations[
                            "Use for products sold in predefined volumes (packages)"
                        ],
                },
            ];
        },
    },
    mounted() {
        this.type = this.modelValue;
    },
    methods: {
        changeType(value) {
            this.type = value;
            this.$emit("update:modelValue", value);
        },
    },
    watch: {
        modelValue: {
            deep: true,
            handler(value) {
                this.type = value;
            },
        },
    },
    emits: ["update:modelValue"],
};
</script>
<style lang="scss">
@use "./../../../scss/style.scss" as *;
</style>
