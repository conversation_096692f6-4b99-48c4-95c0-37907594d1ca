<template>
    <div
        v-if="!modelValue"
        class="dashed-border upload-digital-file"
        @drop.prevent="(ev) => handleDropFile(ev, setting)"
        @dragover.prevent="(ev) => handleDragover(ev, $event)"
        @dragleave.prevent="(ev) => handleDragleave(ev, $event)"
    >
        <div
            class="position-relative d-flex justify-content-center align-items-center flex-column gap-2 w-100"
            @click="$refs.file.click()"
        >
            <input
                ref="file"
                :accept="accept"
                class="drag-drop-input"
                name="file"
                type="file"
                @input="handleFileUpload"
            />
            <svg
                fill="none"
                height="28"
                viewBox="0 0 40 28"
                width="40"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M19.5 8.5625L25.6875 14.6875C25.9375 15 25.9375 15.5 25.6875 15.75L25.3125 16.125C25 16.4375 24.5625 16.4375 24.25 16.125L20 11.875V21.25C20 21.6875 19.625 22 19.25 22H18.75C18.3125 22 18 21.6875 18 21.25V11.875L13.6875 16.125C13.4375 16.4375 12.9375 16.4375 12.625 16.125L12.3125 15.75C12 15.5 12 15 12.3125 14.6875L18.4375 8.5625C18.75 8.25 19.1875 8.25 19.5 8.5625ZM35.6875 12.9375C38.375 14.375 40 17.0625 40 20C40 24.4375 36.375 28 32 28H9C4 28 0 24 0 19C0 15.1875 2.4375 11.8125 6 10.5625C6.25 4.6875 11.0625 0 17 0C20.6875 0 24 1.875 26 4.6875C26.9375 4.25 27.9375 4 29 4C32.8125 4 36 7.1875 36 11C36 11.6875 35.875 12.3125 35.6875 12.9375ZM32 26C35.3125 26 38 23.3125 38 20C38 17.0625 35.8125 14.5625 32.9375 14.125C33.5625 13.25 34 12.1875 34 11C34 8.25 31.75 6 29 6C27.5 6 26.25 6.625 25.3125 7.625C23.9375 4.3125 20.75 2 17 2C12 2 8 6.0625 8 11C8 11.375 8 11.75 8.0625 12.0625C4.625 12.5625 2 15.5 2 19C2 22.875 5.125 26 9 26H32Z"
                    fill="#8D58E0"
                />
            </svg>
            <span class="value-400-16px">
                {{ translations["You can drop your file here to upload it"] }}
            </span>
        </div>

        <div class="d-flex flex-row align-items-center">
            <button
                class="btn btn-primary btn-small"
                @click="$refs.file.click()"
            >
                {{ translations["Upload file"] }}
            </button>
        </div>
        <Vue3SlideUpDown
            :duration="120"
            :model-value="
                !!error ||
                !!responseErrors.file ||
                !!responseErrors.digital_file
            "
        >
            <!-- scroll to the error message -->
            <p
                class="info-box info-box-error rounded py-2 px-3 m-0"
                v-html="
                    error ||
                    translations[responseErrors.file] ||
                    responseErrors.file ||
                    translations[responseErrors.digital_file] ||
                    responseErrors.digital_file
                "
            ></p>
        </Vue3SlideUpDown>
    </div>
    <div v-else>
        <p class="text-400-secondary mb-1">
            {{ translations["Uploaded file"] }}
        </p>
        <div
            class="d-flex align-items-center justify-content-between gap-2 p-3 rounded bg-lightgray"
        >
            <div class="d-flex align-items-center gap-2">
                <i :class="resolveFileIcon" class="product-file-icon"></i>
                <span class="label-500-16px gray">{{ modelValue.name }}</span>
            </div>
            <DeleteComponent
                :delete-function="() => $emit('update:modelValue', null)"
                :label="translations['Remove file?']"
                :loader="false"
            >
                <template #action>
                    <a href="javascript:void(0);" style="color: #fc4f4e">
                        <i
                            class="fal fa-trash-alt"
                            style="font-weight: 400; font-size: 18px"
                        ></i>
                    </a>
                </template>
            </DeleteComponent>
        </div>
    </div>
    <div class="err-empty-field border-0"></div>
</template>
<script>
import DeleteComponent from "@components/DeleteComponent";
import {Vue3SlideUpDown} from "vue3-slide-up-down";

export default {
    name: "UploadDigitalProductFile",
    components: {
        DeleteComponent,
        Vue3SlideUpDown,
    },
    props: {
        modelValue: {
            required: true,
        },
        accept: {
            type: String,
            required: true,
        },
        meta: {
            type: Object,
            default: () => ({}),
        },
        responseErrors: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            error: null,
            translations: {
                "Upload file": this.$t("Upload file"),
                "Remove file?": this.$t("Remove file?"),
                "Uploaded file": this.$t("Uploaded file"),
                "Click or Drop here to upload": this.$t(
                    "Click or Drop here to upload"
                ),
                "File size is too large": this.$t("File size is too large"),
                "You can drop your file here to upload it": this.$t(
                    "You can drop your file here to upload it"
                ),
                "Invalid file type, allowed types are: {types}": this.$t(
                    "Invalid file type, allowed types are: {types}"
                ),
            },
        };
    },
    methods: {
        handleFileUpload(event) {
            delete this.responseErrors.file;
            this.error = null;

            const file = event.target.files[0];
            console.log(file)

            const fileExtension = file.name
                .slice(file.name.lastIndexOf("."))
                .toLowerCase();

            if (!this.accept.includes(fileExtension)) {
                this.error = this.$trp(
                    this.translations[
                        "Invalid file type, allowed types are: {types}"
                        ],
                    {
                        types: this.accept,
                    }
                );
                this.$refs.file.value = "";
                return;
            }

            if (Boolean(this.maxFileSize)) {
                if (file.size > this.maxFileSize) {
                    this.responseErrors = {
                        digital_file:
                            this.translations["File size is too large"],
                    };
                    this.$refs.file.value = "";
                    return;
                }
            }
            this.$emit("update:modelValue", file);
            this.$refs.file.value = "";
        },
        handleDropFile(ev) {
            ev.preventDefault();
            const element = ev.currentTarget;
            if (element) {
                element.classList.remove("hovered");
            }

            if (ev.dataTransfer.items) {
                [...ev.dataTransfer.items].forEach((item, i) => {
                    if (item.kind === "file") {
                        const file = item.getAsFile();
                        if (file) {
                            const event = {
                                target: {
                                    files: [file],
                                },
                            };
                            this.handleFileUpload(event);
                        }
                    }
                });
            } else {
                [...ev.dataTransfer.files].forEach((file, i) => {
                    console.log(`… file[${i}].name = ${file.name}`);
                    // Handle file upload
                });
            }
        },
        handleDragover(ev) {
            const element = ev.currentTarget;
            if (element) {
                element.classList.add("hovered");
            }
            ev.preventDefault();
        },
        handleDragleave(ev) {
            const element = ev.currentTarget;
            if (element) {
                element.classList.remove("hovered");
            }
        },
    },
    computed: {
        maxFileSize() {
            return this.meta?.uploads?.digital_files?.max_file_size;
        },
        resolveFileIcon() {
            if (this.modelValue) {
                let extention = this.modelValue.name.split(".").pop();

                switch (extention) {
                    case "png":
                    case "jpg":
                    case "jpeg":
                    case "gif":
                    case "svg":
                        return "fa-regular fa-file-image";
                    case "pdf":
                        return "fa-regular fa-file-pdf";
                    case "zip":
                    case "rar":
                    case "7z":
                        return "fa-regular fa-file-zipper";
                    default:
                        return "fa-regular fa-file";
                }
            }

            return "fa-regular fa-file";
        },
    },
    emits: ["update:modelValue"],
};
</script>
<style lang="scss">
.dashed-border {
    display: flex;
    padding: 32px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--Spacing---cc-space-md, 16px);
    border-radius: var(--Border-radius---cc-border-radius-md, 8px);
    border: var(--space-size-1, 1px) dashed var(--Color-Border-Neutral---cc-color-border-subdued, #dedede);
    background: var(--Color-Surface-Neutral---cc-color-bg, #fff);
    position: relative;

    .drag-drop-input {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 10;
        opacity: 0;
        cursor: pointer;
    }

    &.hovered {
        border: 2px dashed var(--Color-Border-Neutral---cc-color-border-active, #b38af4);
        background: var(--Color-Surface-Neutral---cc-color-bg-hover, #f6f7fb);
    }
}

.product-file-icon {
    color: var(--Color-Text-Body---cc-color-text-accent, #686b8b);
    font-size: 24px;
    font-style: normal;
    font-weight: 300;
    line-height: 24px;
}
</style>
