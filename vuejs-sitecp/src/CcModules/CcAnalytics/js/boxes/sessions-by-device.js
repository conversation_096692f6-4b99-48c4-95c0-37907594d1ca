"use strict";

import {numberFormat, percentFormat} from "@js/numberFormatters";

export default {
    "sessions-by-device": {
        key: "sessions-by-device",
        type: "table",
        collectDataFrom: '2023-01-01',
        navigationSort: 25,
        formatters: {
            item: {
                current: function () {
                    return numberFormat(this);
                },
                percent: function () {
                    return percentFormat(this);
                }
            }
        },
        labels: {
            "title": "Online store sessions by device type"
        }
    }
}