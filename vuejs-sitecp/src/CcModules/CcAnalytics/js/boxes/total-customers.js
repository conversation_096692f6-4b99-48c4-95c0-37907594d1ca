"use strict";

import {numberFormat, percentFormat} from "@js/numberFormatters";
import { t } from "@js/i18n";
import dateFormat from "./../dateFormat";
import dotGet from "@js/dotGet";

const labels = {
    "title": "Total Customers",
    "subTitle": "Customers over time",
    "tooltip": {
        "boxTip": "Total unique customers, depend on selected order statuses in Settings. Customers made more than one order in your store VS customers made an order into a time frame."
    },
    "text": {
        "new": "New",
        "returning": "Returning"
    },
    "header": {
        "custom": {
            "row1": "New",
            "row2": "Returning"
        }
    },
    "chart": {
        "tooltip": {
            "label": "{count} {label} customer|{count} {label} customers"
        }
    }
};

export default {
    "total-customers": {
        key: "total-customers",
        type: "chart",
        hasIndustryCompare: true,
        navigationSort: 2,
        formatters: {
            text1: function () {
                return numberFormat(this);
            },
            custom: {
                total: function () {
                    return numberFormat(this);
                }
            },
            chart: {
                tooltip: {
                    count: function () {
                        return numberFormat(this);
                    },
                    label: function () {
                        return t(dotGet(labels).get(`text.${this}`, this))
                    }
                },
                datasets: function(options) {
                    this.label = t(dotGet(labels).get(`text.${this.label}`, this.label))
                    this.titles = this.titles.map((title) => {
                        return dateFormat(title, options.group)
                    })

                    return this
                },
                labels: function(options) {
                    return dateFormat(this, options.group, options.daysDiff)
                }
            }
        },
        labels: labels,
        translations: [
            labels.text.new, labels.text.returning,
        ]
    }
}