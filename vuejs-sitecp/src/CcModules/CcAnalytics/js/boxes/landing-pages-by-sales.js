"use strict";

import {moneyFormat, numberFormat, percentFormat} from "@js/numberFormatters";
import PageLink from "./../../components/Helpers/PageLink";
import {markRaw} from "vue";
import DateGroup from "./../../components/Helpers/DateGroup";
import { tc } from "@js/i18n";
import dateFormat from "./../dateFormat";

const labels = {
    "title": "Landing pages by sales",
    "sales": "Sale {value}|Sales {value}",
    "orders": "Order {value}|Orders {value}",
    "tooltip": {
        "boxTip": "Top landing pages where visitors entered your online store and placed an order, depend on selected order statuses in Settings.",
        "device": "Orders: {total}"
    },
    "viewMore": {
        "table": {
            "date": "Date",
            "sales": "Orders",
            "amount": "Amount"
        },
        "chart": {
            "tooltip": {
                "label": "{amount} from {count} order for {date}|{amount} from {count} orders for {date}"
            }
        }
    },
    "details": {
        "table": {
            "page_name": "Name",
            "sales": "Orders",
            "amount": "Amount"
        }
    }
}

export default {
    "landing-pages-by-sales": {
        key: "landing-pages-by-sales",
        type: "table",
        collectDataFrom: '2023-01-01',
        viewMore: true,
        hasDetails: true,
        hasViewMoreChart: true,
        navigationSort: 16,
        details: {
            group: false,
            defaultSorting: [
                {key: 'views', sortingMode: 'desc'},
            ],
        },
        formatters: {
            item: {
                meta: {
                    row1: function () {
                        return tc(labels.orders, this, {
                            value: `<span class="bold-black">${numberFormat(this)}</span>`
                        });
                    },
                    row4: function () {
                        let numeric = this;
                        let value = numberFormat(this);
                        if(!/^\d+$/.test(this)) {
                            numeric = 0;
                            value = this
                        }

                        return tc(labels.sales, numeric, {
                            value: `<span class="bold-black">${value}</span>`
                        });
                    }
                },
                current: function () {
                    return moneyFormat(this);
                },
                percent: function () {
                    return percentFormat(this);
                }
            },
            details: {
                columnTypes: {
                    sales: 'number',
                    amount: 'price'
                },
                row: {
                    page_name: {
                        currently: function () {
                            return markRaw(PageLink);
                        },
                        previous: function () {
                            return null;
                        }
                    },
                    sales: {
                        currently: function () {
                            return numberFormat(this);
                        },
                        previous: function () {
                            return numberFormat(this);
                        }
                    },
                    amount: {
                        currently: function () {
                            return moneyFormat(this);
                        },
                        previous: function () {
                            return moneyFormat(this);
                        }
                    }
                }
            },
            viewMore: {
                columnTypes: {
                    date: 'date',
                    sales: 'number',
                    amount: 'price'
                },
                row: {
                    date: {
                        currently: function () {
                            return markRaw(DateGroup);
                        },
                        previous: function () {
                            return markRaw(DateGroup);
                        }
                    },
                    sales: {
                        currently: function () {
                            return numberFormat(this);
                        },
                        previous: function () {
                            return numberFormat(this);
                        }
                    },
                    amount: {
                        currently: function () {
                            return moneyFormat(this);
                        },
                        previous: function () {
                            return moneyFormat(this);
                        }
                    }
                },
                chart: {
                    tableToChart: function(options) {
                        let chart = {
                            datasets: [
                                {
                                    backgroundColor: "rgba(141, 88, 224, 0.1)",
                                    borderColor: "rgb(141, 88, 224)",
                                    borderWidth: 2,
                                    fill: true,
                                    tension: 0.5,
                                    data: this.map((data) => data.amount),
                                    labels: this.map((data) => [{
                                        date: data.date,
                                        count: data.sales,
                                        amount: data.amount
                                    }]),
                                    titles: this.map((data) => data.date),
                                    label: this.length > 0 ? {
                                        from: (this[0] || null)?.date,
                                        to: (this[this.length - 1] || null)?.date,
                                    } : {}
                                }
                            ],
                            labels: this.map((data) => data.date),
                        }

                        if(options.compare !== 'no') {
                            chart.datasets.push({
                                borderColor: "#717389",
                                borderWidth: 1,
                                borderDash: [3, 3],
                                tension: 0.5,
                                data: this.map((data) => data.amount_previous),
                                labels: this.map((data) => {
                                    return {
                                        1: {
                                            date: data.date_previous,
                                            count: data.sales_previous,
                                            amount: data.amount_previous
                                        }
                                    }
                                }),
                                titles: this.map((data) => data.date_previous),
                                label: this.length > 0 ? {
                                    from: (this[0] || null)?.date_previous,
                                    to: (this[this.length - 1] || null)?.date_previous,
                                } : {}
                            });
                        }

                        return chart;
                    },
                    tooltip: {
                        amount: function () {
                            return moneyFormat(this);
                        },
                        count: function () {
                            return numberFormat(this);
                        }
                    },
                    datasets: function(options) {
                        this.label = `${dateFormat(this.label.from, options.group === 'hourly' ? 'daily' : options.group)} - ${dateFormat(this.label.to, options.group === 'hourly' ? 'daily' : options.group)}`

                        this.labels = this.labels.map((label) => {
                            label[options.index].date = dateFormat(label[options.index].date, options.group)
                            return label
                        })
                        this.titles = this.titles.map((title) => {
                            return dateFormat(title, options.group)
                        })

                        return this
                    },
                    labels: function(options) {
                        return dateFormat(this, options.group, options.daysDiff)
                    }
                }
            }
        },
        labels: labels,
        translations: [
            labels.sales, labels.orders
        ]
    }
}