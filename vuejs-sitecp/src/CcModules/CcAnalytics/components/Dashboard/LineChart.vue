<template>
    <div class="position-relative" v-if="inDetails ? group !== 'none' : visible" ref="box">
        <section class="cc-card h-100" :class="{
        'cc-card__details': inDetails,
        'cc-card__chart': config(`type`) === 'chart',
        'cc-card__bar': config(`type`) === 'bar'
    }" :id="`${config(`type`)}-${model}`">

            <card-head
                :text1="headers.currently"
                :device="headers.device"
                :text2="headers.percent"
                :text3="headers.previous"
                :arrow="headers.arrow"
                :title="boxTitle ? boxTitle : translatedTitle"
                :box="model"
                :boxes="boxes"
                :range="range"
                :compare="compare"
                :in-details="inDetails"
                :loading="loading"
                :error="error ? error: periodError"
                v-on:box-change="boxChange($event)"
                :custom="headers.custom"
                :group="inDetails ? group : group2"
                :configuration="boxConfig"
                :has-view-more="allowDetails && (chartData.datasets || []).length > 0"
                :previous-date-from="previousDateFrom"
                :in-dashboard="inDashboard"
            />

            <div v-if="error" v-html="error" class="alert alert-danger text-break-all--"></div>
            <div v-else-if="periodError" v-html="periodError" class="alert alert-warning text-break-all--"></div>
            <loading :loading="loading" />
            <div v-if="!loading && !error && !periodError && (chartData.datasets || []).length < 1" class="no-data roboto-normal-black-14px-2 padding-top-30 padding-bottom-30">
                {{ translations['No data available for the selected range.'] }}
            </div>

            <div class="cc-subtitle__wrap" v-if="translatedSubTitle && !error && !loading && (chartData.datasets || []).length > 0">
                <div class="cc-subtitle__text" v-if="translatedToolTipSubTitle">
                    <span class="tooltips cc-tooltip-dotted" data-bs-placement="top" data-bs-toggle="tooltip" data-bs-html="true"
                          :title="translatedToolTipSubTitle" v-html="translatedSubTitle"></span>
                </div>
                <div class="cc-subtitle__text" v-else v-html="translatedSubTitle"></div>
            </div>

          <div class="mt-auto w-100">
              <chart-generator v-bind="chartGeneratorProps"></chart-generator>
          </div>
        </section>
        <compare-label
            v-bind="compareLabelProps"
            v-if="showCompareLabel"
        ></compare-label>
    </div>
</template>

<script>
import CardHead from "./CardHead";
import Loading from "@components/Loading";
import ChartGenerator from "./../Helpers/ChartGenerator";

import axios from "axios";
import { Tooltip as BtTooltip } from 'bootstrap'
import CompareLabel from './CompareLabel'
import moment from 'moment'

export default {
    name: "LineChart",
    components: {
        CardHead,
        Loading,
        CompareLabel,
        ChartGenerator
    },
    props: {
        box: {
            type: String,
            required: true,
        },
        boxes: {
            type: Array,
            default() {
                return []
            }
        },
        range: {
            type: Array,
            required: true
        },
        compare: {
            type: String,
            required: true,
        },
        datasetIdKey: {
            type: String,
            default: 'label'
        },
        width: {
            type: Number,
            default: 350
        },
        height: {
            type: Number,
            default: 180
        },
        cssClasses: {
            default: 'width-100',
            type: String
        },
        styles: {
            type: Object,
            default: () => {}
        },
        plugins: {
            type: Array,
            default: () => []
        },
        text1: {
            type: String
        },
        text2: {
            default: false,
        },
        title: {
            type: String,
            default: null
        },
        title2: {
            type: String,
            default: null
        },
        inDetails: {
            default: false
        },
        customers: {
            default: null
        },
        device: {
            default: null,
        },
        group: {
            type: String,
            default: 'auto',
        },
        type: {
            type: String,
            default: 'line',
        },
        minY: {
            default: null
        },
        maxY: {
            default: null
        },
        visible: {
            default: true,
        },
        configuration: {
            type: Object,
            default: {}
        },
        statistic: {
            type: Object,
            default: null
        },
        cacheHash: {
            type: String,
            default: null
        },
        footerText: {
            type: String,
            default: null
        },
        planLimits: {
            'cc_analytics.allow_industry_compare': {
                value: false,
                unrestricted_plans: null
            },
        },
        inDashboard: {
          type: Boolean,
          default: false
        }
    },
    data() {
        let that = this;
        return {
            headers: {
                previous: null,
                currently: null,
                percent: null,
                arrow: null,
                custom: null
            },
            loading: false,
            error: null,
            periodError: null,
            chartData: {},
            group2: 'auto',
            model: this.box,
            boxTitle: null,
            boxConfig: this.configuration,
            allowDetails: true,
            daysDiff: 0,
            barHeight: 0,
            abortController: new AbortController(),
            previousDateFrom: null,
            translations: {
                'Sales distribution {from} - {to}': this.$t('Sales distribution {from} - {to}'),
                'No data available for the selected range.': this.$t('No data available for the selected range.'),
                'We cannot generate statistics for the selected period, please reduce it.': this.$t('We cannot generate statistics for the selected period, please reduce it.'),
                'There is no data for the selected period. Please select a period after {date} to view data.': this.$t('There is no data for the selected period. Please select a period after {date} to view data.', {date: '{date}'}),
            }
        }
    },
    mounted() {
        this.getItems();
        this.boxesToTranslate();

        new BtTooltip(this.$el, {
            selector: "[data-bs-toggle='tooltip']",
        })
    },
    methods: {
        resetData() {
            this.chartData = {}
            this.headers = {
                previous: null,
                currently: null,
                percent: null,
                arrow: null,
                custom: null
            }
            this.group2 = 'auto'
            this.boxTitle = null
            this.allowDetails = true
            this.previousDateFrom = null
        },
        getCacheKey() {
            return `${this.$route.name}.${this.model}.${this.range[0].format('YYYY-MM-DD')}.${this.range[1].format('YYYY-MM-DD')}.${this.compare}`
        },
        getItems() {
            if(this.inDetails && this.group === 'none' && this.loading) {
                return;
            }

            this.loading = true
            this.error = null
            this.periodError = null
            this.resetData()
            if(this.config('collectDataFrom') && moment(this.config('collectDataFrom')).isAfter(this.range[0])) {
                this.loading = false
                this.periodError = this.$trp(this.translations['There is no data for the selected period. Please select a period after {date} to view data.'], {
                    date: moment(this.config('collectDataFrom')).format(this.serverSettings('format.date'))
                })
                return;
            }

            if(this.$ccCache().has(this.getCacheKey())) {
                let cache = this.$ccCache().get(this.getCacheKey());
                Object.keys(cache).map((key) => {
                    this[key] = cache[key]
                })

                this.loading = false
                this.error = null
                this.periodError = null
                return;
            }

            axios.get(`/admin/api/analytics/dashboard/${this.model}`, { params: {
                dateFrom:this.range[0].format('YYYY-MM-DD'),
                dateTo: this.range[1].format('YYYY-MM-DD'),
                compare: this.compare,
                group: this.inDetails ? this.group : 'auto'
            }, signal: this.abortController.signal })
                .then(response => {
                    if(!response.data || !('data' in response.data)) {
                        this.error = this.translations['No data available for the selected range.'];
                        return;
                    }

                    this.chartData = response.data.data.chart
                    this.chartData.datasets = (this.chartData.datasets || []).map((set, setIndex) => {
                        return this.CcAnalyticsVariableFormatter(set, `${this.model}.chart.datasets`, {
                            group: response.data.group,
                            daysDiff: response.data.daysDiff,
                            index: setIndex
                        });
                    })
                    this.chartData.labels = (this.chartData.labels || []).map((set, setIndex) => {
                        return this.CcAnalyticsVariableFormatter(set, `${this.model}.chart.labels`, {
                            group: response.data.group,
                            daysDiff: response.data.daysDiff,
                            index: setIndex
                        });
                    })

                    // console.log(this.chartData)
                    this.headers = response.data.data.headers
                    this.group2 = response.data.group
                    this.allowDetails = response.data.allowDetails
                    this.previousDateFrom = response.data.previousDateFrom
                    this.loading = false

                    if(this.headers.title_labels && typeof(this.headers.title_labels) === 'object') {
                        let labelValue = Object.assign({}, this.headers.title_labels);

                        Object.keys(labelValue).forEach((key) => {
                            labelValue[key] = this.CcAnalyticsVariableFormatter(labelValue[key], `${this.model}.title_labels.${key}`, {
                                group: response.data.group
                            });
                        });
                        this.boxTitle = this.$trp(this.translatedTitleLabels, labelValue)
                    }

                    this.$ccCache().set(this.getCacheKey(), {
                        chartData: this.chartData,
                        headers: this.headers,
                        group2: this.group2,
                        boxTitle: this.boxTitle,
                        allowDetails: this.allowDetails,
                        previousDateFrom: this.previousDateFrom,
                    }, 60)

                    this.barHeight = this.$refs.box.clientHeight - 100;
                })
                .catch(error => {
                    if(error.response && error.response.status === 504) {
                        this.error = this.translations['We cannot generate statistics for the selected period, please reduce it.']
                    } else {
                        this.error = error.response?.data?.message || error.message
                    }
                    this.loading = false;
                })
        },
        boxChange(value) {
            this.model = value;
            this.getItems();
        },
        config(key, def) {
            return this.getFromDot(this.boxConfig).get(key, def)
        },
        boxesToTranslate() {
            ['title', 'subTitle', 'tooltip.subTitle', 'title_labels', 'chart.tooltip.label'/*, 'tooltip.boxTip', 'chart.tooltip.label'*/].map((labelKey) => {
                const label = this.config(`labels.${labelKey}`);
                if(label) {
                    this.translations[label] = this.$t(label);
                }
            });

            this.config(`translations`, []).map((t) => {
                this.translations[t] = this.$t(t);
            })
        },
    },
    watch: {
        cacheHash: function () {
            this.getItems()
        },
        range: function (value) {
            if(value && value[0] && value[1]) {
                this.getItems()
            }
        },
        compare: function (value) {
            if(value) {
                this.getItems()
            }
        },
        group: function (value) {
            if(value) {
                this.getItems()
            }
        },
        model: function (value) {
            if (value) {
                this.boxConfig = this.CcAnalyticsBoxes().get(value, {})

                this.getItems()
            }
        },
        box: function (value) {
            this.model = value;
        },
        $route: function (currently, previous) {
            if(
                currently.name !== previous.name
            ) {
                this.abortController.abort()
            }
        }
    },
    computed: {
        compareLabelProps() {
            let stat = this.statistic[this.model] || {};
            return {
                percent: stat?.percent || 0,
                type: stat?.type || (parseInt(Math.round(Math.random())) ? 'below' : 'above'),
                reverse: stat?.reverse || false,
                year: stat?.year || 0,
                week: stat?.week || 0,
                industry_id: stat?.industry_id || 0,
                industry_name: stat?.industry_name || '',
                period: stat?.period || '',
                value: stat?.value || 0,
                planLimits: this.planLimits,
                box: this.model,
            }
        },
        showCompareLabel() {
            if(!this.config('hasIndustryCompare', false) || this.inDetails || this.loading || this.error) {
                return false;
            }

            if(!this.planLimits['cc_analytics.allow_industry_compare'].value) {
                return true;
            }

            return this.statistic && this.statistic[this.model] && this.statistic[this.model].percent > 0;

            // return this.type === 'line'
            //     //&& (this.chartData.datasets || []).length > 0
            //      this.statistic
            //     && this.statistic.percent > 0;
        },
        chartGeneratorProps() {
            return {
                chartData: this.chartData,
                model: this.model,
                datasetIdKey: this.datasetIdKey,
                plugins: this.plugins,
                cssClasses: this.cssClasses,
                styles: this.styles,
                width: this.width,
                height: this.height,
                barHeight: this.barHeight,
                type: this.type,
                minY: this.minY,
                maxY: this.maxY,
                visible: !this.error && !this.periodError && !this.loading && (this.chartData.datasets || []).length > 0
            }
        },
        translatedTitle() {
            return this.$t(this.config('labels.title', this.box))
        },
        translatedSubTitle() {
            const label = this.config('labels.subTitle');
            return label ? this.$t(label) : null
        },
        translatedToolTipSubTitle() {
            const label = this.config('labels.tooltip.subTitle');
            return label ? this.$t(label) : null
        },
        translatedTitleLabels() {
            const label = this.config('labels.title_labels');
            return label ? this.$t(label) : null
        }
    }
};
</script>
