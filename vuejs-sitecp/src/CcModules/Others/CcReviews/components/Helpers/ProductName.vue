<template>
    <div class="d-flex flex-row gap-3 align-items-center">
        <ImgWrapper
            :img="$route.name.includes('reviews') ? data?.product?.image : data?.image"
            :url="data?.product?.url || data?.product_url"
            :tooltip="translations['See in store']"
        />
        <a
            class="value-400 text-break text-wrap cc-tooltip-dotted"
            target="_blank"
            :href="data?.product?.id ? `/admin/products/edit/${data?.product?.id}` : data?.product?.url || data?.product_url"
            v-html="$route.name.includes('reviews') ? data?.product?.name : data?.product_name"
        >
        </a>
    </div>
</template>
<script>
import ImgWrapper from "@components/Apps/Erp/ImgWrapper.vue";

export default {
    name: "ProductName",
    components: {ImgWrapper},
    props: {
        data: {
            type: Object,
            required: true,
            default: {},
        },
        column: {
            required: false,
            type: Object,
            default: {},
        },
    },
    data() {
        return {
            translations: {
                "See in store": this.$t("See in store"),
            },
        };
    },
    computed: {
        url() {
            return `${this.serverSettings("host")}/product/${this.data.url}`;
        },
    },
};
</script>
