<template>
  <b-modal
    v-model="modal"
    class="modal-right"
    size="lg"
    :no-footer="true"
    header-class="edit-settings-modal-header"
    body-class="edit-settings-modal-content"
    :no-close-on-backdrop="true"
  >
    <template #header>
      <div class="d-flex align-items-center gap-2">
        <h5 v-html="title" class="settings-modal-title"></h5>
      </div>
      <div class="d-flex justify-content-end align-items-center gap-2">
        <button
          type="button"
          class="btn btn-white"
          @click="closeModal"
          v-html="translations['Close']"
        ></button>
      </div>
    </template>
    <b-row>
      <Loading
        v-if="isLoading"
        style="margin-left: 45%; margin-top: 20%"
        :loading="isLoading"
      ></Loading>
      <b-card v-else :class="'mt-3'">
        <b-row v-for="(answer, index) in answers" :key="index">
          <b-col>
            <div class="d-flex flex-column gap-3">
              <div class="d-flex gap-2">
                <span class="label-500" v-html="translations['User:']"></span>
                <span class="value-400" v-html="answer.user_name"></span>
              </div>
              <div class="d-flex gap-2">
                <span class="label-500" v-html="translations['Date:']"></span>
                <span class="value-400" v-html="answer.created_at"></span>
              </div>
            </div>
            <p class="value-400 mt-2" v-html="answer.comment"></p>
            <hr />
            <div class="d-flex align-items-center justify-content-between">
              <div>
                <a href="javascript:void(0)" @click="() => changeStatus(answer, index)">
                  <TooltipLabel
                    v-if="answer.is_approved == 0"
                    :icon="statusLoading ? null : 'far fa-thumbs-up'"
                    :label-text="translations['Approve answer']"
                    :tooltip-text="translations['Change the status of the answer']"
                    :reverse="true"
                  >
                    <b-spinner small v-if="statusLoading"></b-spinner>
                  </TooltipLabel>
                </a>
              </div>
              <a href="javascript:void(0)" @click="() => deleteAnswer(answer.id, index)">
                <TooltipLabel
                  :icon="deleteLoading ? null : 'fal fa-times-circle'"
                  :label-text="translations['Delete']"
                  :tooltip-text="translations['Delete answer']"
                  :reverse="true"
                >
                  <b-spinner small v-if="deleteLoading"></b-spinner>
                </TooltipLabel>
              </a>
            </div>
          </b-col>
        </b-row>
      </b-card>
    </b-row>
  </b-modal>
</template>
<script>
import axios from "axios";
import Loading from "@components/Loading.vue";
import TooltipLabel from "@components/Apps/Erp/TooltipLabel.vue";
import {toast} from "@js/toast";

export default {
  name: "Answers",
  components: {
    Loading,
    TooltipLabel,
  },
  props: {
    data: {
      required: false,
      default: {},
    },
    open: {
      required: true,
      default: false,
    },
  },
  data() {
    return {
      modal: false,
      answers: [],
      isLoading: false,
      deleteLoading: false,
      statusLoading: false,
      translations: {
        "User:": this.$t("User:"),
        Delete: this.$t("Delete"),
        "Delete answer": this.$t("Delete answer"),
        "Date:": this.$t("Date:"),
        Close: this.$t("Close"),
        "Deleted successfully": this.$t("Deleted successfully"),
        "Status changed successfully": this.$t("Status changed successfully"),
        "Error occured while deleting": this.$t("Error occured while deleting"),
        "Error occured while changing the status": this.$t(
          "Error occured while changing the status"
        ),
        "Change the status of the answer": this.$t("Change the status of the answer"),
        "Approve answer": this.$t("Approve answer"),
      },
    };
  },
  computed: {
    title() {
      return this.data.id ? this.data.title : "";
    },
  },
  methods: {
    getAnswers() {
      if (!this.data.id) {
        return;
      }
      this.isLoading = true;
      this.modal = true;

      let url;
      if (this.$route.name.includes("reviews")) {
        url = `/admin/api/product_review/answers/${this.data.id}`;
      } else {
        url = `/admin/api/product_review/questions/answers/${this.data.id}`;
      }

      axios
        .get(url)
        .then((response) => {
          this.answers = this.$route.name.includes("reviews")
            ? response.data
            : response.data.data;
          this.isLoading = false;
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    deleteAnswer(id) {
      this.deleteLoading = true;

      let url;

      if (this.$route.name.includes("reviews")) {
        url = `/admin/api/product_review/delete?ids[]=${id}`;
      } else {
        url = `/admin/api/product_review/questions/delete?ids[]=${id}`;
      }
      axios
        .delete(url)
        .then((response) => {
          this.answers = this.answers.filter((x) => x.id !== id);
          toast.success("Deleted successfully");
        })
        .catch((err) => {
          toast.error(this.translations["Error occured while deleting"]);
        })
        .finally(() => {
          this.deleteLoading = false;
        });
    },
    changeStatus(answer, index) {
      this.statusLoading = true;

      let url;

      if (this.$route.name.includes("reviews")) {
        url = `/admin/api/product_review/status/1`;
      } else {
        url = `/admin/api/product_review/questions/status/1`;
      }

      axios
        .post(url, {
          ids: [answer.id],
        })
        .then((res) => {
          this.answers[index].is_approved = 1;
          toast.success(this.translations["Status changed successfully"]);
        })
        .catch((err) => {
          toast.error(this.translations["Error occured while changing the status"]);
        })
        .finally(() => {
          this.statusLoading = false;
        });
    },
    closeModal() {
      this.modal = false;
      this.answers = [];
      this.$emit("update:data", {});
    },
  },
  watch: {
    open(val) {
      if (val) {
        this.getAnswers();
      }
    },
    // data: {
    //   deep: true,
    //   handler(newVal) {
    //     if (newVal.id) {
    //       this.getAnswers();
    //     }
    //   },
    // },
  },
};
</script>
