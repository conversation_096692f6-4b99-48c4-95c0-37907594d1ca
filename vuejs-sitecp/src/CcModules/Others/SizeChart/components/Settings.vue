<template>
    <b-container class="container-medium">
        <data-table
            v-if="table.data"
            :enable-mobile="true"
            v-bind="this.table"
            @pagination-change-page="paginate"
            :filters="true"
            v-model:query="query"
            :get-data="getData"
            app-name="size_chart"
        >
        <template #additional_buttons>
            <router-link
                :to="{ name: 'apps.size_chart.create' }"
                class="btn btn-default align-items-center gap-1"
            >
                <i class="far fa-plus m-0"></i> <span class="d-none d-sm-inline">{{ translations["Create new"] }}</span>
            </router-link>
        </template>
            <template #noResult>
                <router-link
                    :to="{ name: 'apps.size_chart.create' }"
                    class="btn btn-default align-items-center gap-1"
                >
                    <i class="far fa-plus m-0"></i><span class="d-none d-sm-inline">{{ translations["Create new"] }}</span>
                </router-link>
            </template>
        </data-table>
    </b-container>
</template>

<script>
import { markRaw } from "vue";

import DataTable from "@components/Table";
import NoResultsSearch from "@components/NoResultsSearch";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import InputComponent from "@components/Form/InputComponent";
import ItemName from "./Helpers/ItemName";
import Page from "./Helpers/Page";
import DeleteSizeChart from "./Helpers/DeleteSizeChart";
import ChangeStatus from "./Helpers/ChangeStatus";
import Loading from "@components/Loading";

import SizeChart from "./../SizeChart";

export default {
    components: {
        DataTable,
        NoResultsSearch,
        SelectWithAjax,
        InputComponent,
        ItemName,
        Page,
        DeleteSizeChart,
        ChangeStatus,
        Loading,
    },
    props: {
        app: {
            required: false,
            default: {},
        },
        settings: {
            required: false,
            default: {},
        },
    },
    data() {
        return {
            table: {},
            responseErrors: {},
            modal: false,
            isLoading: true,
            isLoaded: false,
            category_options: [],
            vendor_options: [],
            page_options: [],
            submitLoader: false,
            condition: {
                name: null,
                name_frontend: null,
                categories: [],
                vendors: [],
                page: null,
            },
            isChartLoading: false,
            currentId: null,
            titleModal: "",
            page: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            query: this.$route.query,
            model: new SizeChart(),
            translations: {
                title: this.$t("Title"),
                "Title on website": this.$t("Title on website"),
                Page: this.$t("Page"),
                Status: this.$t("Status"),
                "Title (Will be Visible in the Store)": this.$t(
                    "Title (Will be Visible in the Store)"
                ),
                Category: this.$t("Category"),
                Manufacturers: this.$t("Manufacturers"),
                "Size Page": this.$t("Size Page"),
                Cancel: this.$t("Cancel"),
                Save: this.$t("Save"),
                "Create new": this.$t("Create new"),
                "No results found": this.$t("No results found"),
                "Edit {name}": this.$t("Edit {name}"),
                "Successfully updated": this.$t("Successfully updated"),
                "Successfully created": this.$t("Successfully created"),
                Title: this.$t("Title"),
            },
        };
    },
    methods: {
        async paginate(page) {
            this.page = page;
            await this.getData('paginate');
        },
        async removeRow(id) {
            this.table.data.data = this.table.data.data.filter((x) => x.id !== id);
            if (this.table.data.data.length === 0 && this.page > 1) {
                this.page -= 1;
                await this.getData();
            }
        },
        async getData(paginate) {
            try {
                this.table.isLoading = true;

                this.model.where(this.query);
                if(Object.keys(this.query).toString().includes('filters') && !paginate){
                    this.page = 1
                }
                this.table.data = await this.model.paginate(this.page, this.perPage);

                this.table.data.data = this.table.data.data.map((x) => {
                    return {
                        ...x,
                        getData: this.getData,
                        model: this.model,
                        removeRow: this.removeRow,
                    };
                });

                this.$router.push({
                    query: { ...this.$route.query, page: this.table.data.current_page },
                });
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.isLoaded = true;
                this.table.isLoading = false;
            }
        },
        setTable() {
            this.table = {
                data: {
                    data: [],
                },
                isLoading: this.loading,
                defaultSorting: [{ key: "id", sortingMode: "desc" }],
                columns: [
                    {
                        column: "condition_name",
                        key: "condition_name",
                        sortable: false,
                        title: this.translations["Title"],
                        component: markRaw(ItemName),
                    },
                    {
                        column: "condition_name_front",
                        key: "condition_name_front",
                        sortable: false,
                        title: this.translations["Title on website"],
                    },
                    {
                        column: "page",
                        key: "page",
                        sortable: false,
                        title: this.translations["Page"],
                        component: markRaw(Page),
                    },
                    {
                        column: "actions",
                        key: "status",
                        sortable: false,
                        title: this.translations["Status"],
                        component: markRaw(ChangeStatus),
                    },
                    {
                        column: "actions",
                        key: "action",
                        sortable: false,
                        title: " ",
                        component: markRaw(DeleteSizeChart),
                    },
                ],
            };
        },
    },
    created() {
        this.setTable();
    },
};
</script>
