<template>
    <b-modal
        class="modal-right"
        no-footer="true"
        size="lg"
        header-class="edit-settings-modal-header"
        body-class="edit-settings-modal-content"
        v-model="modal"
        :cancel-title="translations['Cancel']"
        :no-close-on-backdrop="editLoader"
    >
        <template #header>
            <div class="d-flex align-items-center gap-2">
                <h5
                    v-if="type === 'create'"
                    v-html="translations['Create new terminal']"
                    class="settings-modal-title"
                ></h5>
                <h5
                    v-if="type === 'edit'"
                    v-html="item.name"
                    class="settings-modal-title"
                ></h5>
            </div>
            <div class="d-flex justify-content-end align-items-center gap-2">
                <button
                    type="button"
                    class="btn btn-white"
                    :disabled="editLoader"
                    @click="modal = false"
                    v-html="translations['Close']"
                ></button>
                <button
                    type="button"
                    class="btn btn-primary"
                    :disabled="submitLoader || editLoader"
                    @click="actionSubmit"
                >
                    <b-spinner small v-if="submitLoader"></b-spinner>
                    {{ translations["Save"] }}
                </button>
            </div>
        </template>
        <Loading v-if="editLoader" :loading="editLoader" class="app-loader-center" />
        <b-row v-if="!editLoader">
            <b-col>
                <b-card>
                    <ActiveSwitch
                        @change-value="(val) => (data.is_active = val)"
                        :is-active="data.is_active ?? 0"
                        :label-text="translations['Active']"
                    />
                    <InputComponent
                        v-model="data.name"
                        :label-text="translations['Terminal name']"
                        :column-style="false"
                        :cols-width="6"
                        :type="'string'"
                        :error="responseErrors['name']"
                    />
                    <SelectWithAjax
                        v-model:val="data.user_access"
                        @options="(val) => (user_access_array = val)"
                        :options="user_access_array"
                        :error="responseErrors['user_access']"
                        :api-url="'/admin/api/pick_and_pack/autocomplete/admins'"
                        :request-on-search="true"
                        :mode="'tags'"
                        :searchable="true"
                        :label="translations['Choose users']"
                        :tooltipText="
                            translations['Choose users with access to the terminal']
                        "
                        :tooltip="true"
                        class="my-3"
                    />
                    <SelectWithAjax
                        v-model:val="data.zones"
                        @options="(val) => (zones_array = val)"
                        :options="zones_array"
                        :error="responseErrors['zones']"
                        :api-url="'/admin/api/pick_and_pack/autocomplete/geo-zone'"
                        :request-on-search="true"
                        :mode="'tags'"
                        :searchable="true"
                        :label="translations['Geo zones']"
                        :tooltipText="
                            translations['Select zones to be served by the terminal']
                        "
                        :tooltip="true"
                        class="my-3"
                    />
                    <SelectWithAjax
                        v-model:val="data.order_status"
                        @options="(val) => (order_status_array = val)"
                        :options="order_status_array"
                        :error="responseErrors['order_status']"
                        :api-url="'/admin/api/pick_and_pack/autocomplete/status'"
                        :request-on-search="true"
                        :mode="'tags'"
                        :searchable="true"
                        :label="
                            translations[
                                'Choose statuses for the orders which will be visualised in the terminal'
                            ]
                        "
                        :tooltipText="
                            translations[
                                'Only orders with the selected statuses will be visualised in the terminal'
                            ]
                        "
                        :tooltip="true"
                        class="my-3"
                    />
                    <InputComponent
                        v-model="data.order_time"
                        :label-text="
                            translations[
                                'Choose how many days back to display the orders / products'
                            ]
                        "
                        :column-style="false"
                        :cols-width="6"
                        :tooltip="true"
                        :tooltipText="
                            translations[
                                'If the field is empty, all orders / products will be displayed according to the other settings'
                            ]
                        "
                        :type="'number'"
                        :error="responseErrors['order_time']"
                    />
                    <SelectWithAjax
                        :isSelectDisabled="this.type == 'edit'"
                        v-model:val="data.type"
                        @update:val="(val) => (data.type = val)"
                        :val="data.type"
                        :options="[
                            {
                                name: this.translations['Group by products'],
                                id: 'products',
                            },
                            {
                                name: this.translations['Group by orders - Pick & Pack'],
                                id: 'pick_pack',
                            },
                            {
                                name: this.translations['Group by orders - Pack'],
                                id: 'pack',
                            },
                        ]"
                        :column-style="false"
                        :cols-width="6"
                        :label="translations['Type of the terminal']"
                        :error="responseErrors['type']"
                        class="my-3"
                    />
                </b-card>
            </b-col>
        </b-row>
        <CreatePickPack
            v-if="!editLoader && data.type == 'pick_pack'"
            :data="data"
            :responseErrors="responseErrors"
            :name="translations['Group by orders - Pick & Pack']"
            :translations="translations"
        />
        <CreatePack
            v-if="!editLoader && data.type == 'pack'"
            :data="data"
            :responseErrors="responseErrors"
            :name="translations['Group by orders - Pack']"
            :translations="translations"
        />
        <CreateOrderBy
            v-if="!editLoader && (data.type == 'pick_pack' || data.type == 'pack')"
            :data="data"
            :responseErrors="responseErrors"
            :translations="translations"
        />
    </b-modal>
</template>

<script>
import axios from "axios";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import InputComponent from "@components/Form/InputComponent";
import {toast} from "@js/toast";
import ActiveSwitch from "@components/Form/ActiveSwitch.vue";
import CreatePickPack from "./Create/CreatePickPack";
import CreateOrderBy from "./Create/CreateOrderBy";
import CreatePack from "./Create/CreatePack";
import Loading from "@components/Loading";

export default {
    name: "Create_BACKUP",
    components: {
        Loading,
        InputComponent,
        SelectWithAjax,
        ActiveSwitch,
        CreatePickPack,
        CreateOrderBy,
        CreatePack,
    },
    props: {
        type: {
            type: [String, null],
            required: true,
            default: "create",
        },
        modelValue: {
            type: Boolean,
            required: false,
        },
        item: {
            default: {},
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            modal: false,
            button_default: "",
            text_default: "",
            loading: false,
            responseErrors: {},
            editLoader: false,
            data: {
                pick_pack: {
                    show_pick: {},
                },
                pack: {
                    show_pick: {},
                },
                send_minute: [],
            },
            submitLoader: false,
            order_status_array: [],
            user_access_array: [],
            zones_array: [],
            status_pack_array: [],
            status_pack_missing_array: [],
            status_unpack_array: [],
            translations: {
                "Create new terminal": this.$t("Create new terminal"),
                Create: this.$t("Create"),
                Save: this.$t("Save"),
                Close: this.$t("Close"),
                "Terminal name": this.$t("Terminal name"),
                Active: this.$t("Active"),
                "Choose users": this.$t("Choose users"),
                "Choose users with access to the terminal": this.$t(
                    "Choose users with access to the terminal"
                ),
                "Geo zones": this.$t("Geo zones"),
                "Select zones to be served by the terminal": this.$t(
                    "Select zones to be served by the terminal"
                ),
                "Choose statuses for the orders which will be visualised in the terminal": this.$t(
                    "Choose statuses for the orders which will be visualised in the terminal"
                ),
                "Only orders with the selected statuses will be visualised in the terminal": this.$t(
                    "Only orders with the selected statuses will be visualised in the terminal"
                ),
                "Choose how many days back to display the orders / products": this.$t(
                    "Choose how many days back to display the orders / products"
                ),
                "If the field is empty, all orders / products will be displayed according to the other settings": this.$t(
                    "If the field is empty, all orders / products will be displayed according to the other settings"
                ),
                "Type of the terminal": this.$t("Type of the terminal"),
                "Group by products": this.$t("Group by products"),
                "Group by orders - Pick & Pack": this.$t("Group by orders - Pick & Pack"),
                "Group by orders - Pack": this.$t("Group by orders - Pack"),
                "Constant sound signal on new order": this.$t(
                    "Constant sound signal on new order"
                ),
                "Generate form for print": this.$t("Generate form for print"),
                "Show price": this.$t("Show price"),
                "Show button for order cancelling": this.$t(
                    "Show button for order cancelling"
                ),
                "Allow packing": this.$t("Allow packing"),
                "Sort by": this.$t("Sort by"),
                "Choose what to sort the orders by": this.$t(
                    "Choose what to sort the orders by"
                ),
                "Sort type": this.$t("Sort type"),
                "Choose a sort type": this.$t("Choose a sort type"),
                "Date of creation": this.$t("Date of creation"),
                "Estimated shipping date": this.$t("Estimated shipping date"),
                Ascending: this.$t("Ascending"),
                Descending: this.$t("Descending"),
                "I understand button to stop sound on new order": this.$t(
                    "I understand button to stop sound on new order"
                ),
                "Allow entering package count": this.$t("Allow entering package count"),
                "Allow sending the order": this.$t("Allow sending the order"),
                "Status for packed order": this.$t("Status for packed order"),
                "Select a status which will be set for the order after it has been packed": this.$t(
                    "Select a status which will be set for the order after it has been packed"
                ),
                "Status for packing an order with missing products": this.$t(
                    "Status for packing an order with missing products"
                ),
                "Choose a status that will be set when packing an order that contains missing products": this.$t(
                    "Choose a status that will be set when packing an order that contains missing products"
                ),
                "Status for unpacking an order": this.$t("Status for unpacking an order"),
                "Select a status that will be set when the order is unpacked": this.$t(
                    "Select a status that will be set when the order is unpacked"
                ),
                "Details of the order": this.$t("Details of the order"),
                "If enabled, you will confirm new orders as viewed via the button, this action will stop the sound. With the option off, you will have a beep until the complete order list is packed or shipped.": this.$t(
                    "If enabled, you will confirm new orders as viewed via the button, this action will stop the sound. With the option off, you will have a beep until the complete order list is packed or shipped."
                ),
                "+ Add a new interval for sending the order":
                    "+ Add a new interval for sending the order",
                "Add a new interval (minutes)": "Add a new interval (minutes)",
                "You have successfully saved the terminal":
                    "You have successfully saved the terminal",
                "You have not selected a status for packed orders":
                    "You have not selected a status for packed orders",
                "You have not entered a name for the terminal":
                    "You have not entered a name for the terminal",
                "You have not selected users who have access to the terminal":
                    "You have not selected users who have access to the terminal",
                "You have not selected statuses for the order which will be visualised in the terminal":
                    "You have not selected statuses for the order which will be visualised in the terminal",
                "You have not selected a type for the terminal":
                    "You have not selected a type for the terminal",
                "You have not entered minutes for sending an order":
                    "You have not entered minutes for sending an order",
                "You have not selected a status for orders with missing products":
                    "You have not selected a status for orders with missing products",
                "You have not selected a status for unpacked orders":
                    "You have not selected a status for unpacked orders",
                "You have not selected a sorting parameter for the terminal":
                    "You have not selected a sorting parameter for the terminal",
                "You have not selected a type of order sorting":
                    "You have not selected a type of order sorting",
            },
        };
    },
    methods: {
        actionSubmit() {
            this.responseErrors = {};
            this.submitLoader = true;
            let url;
            if (this.type === "edit") {
                url = "/admin/api/pick_and_pack/update/" + this.data.id;
            } else {
                url = "/admin/api/pick_and_pack/create/";
            }
            axios
                .post(url, this.data)
                .then((response) => {
                    toast.success(
                        this.translations["You have successfully saved the terminal"]
                    );
                    if (this.type === "edit") {
                        this.$emit("success", this.item.id, response.data);
                    } else {
                        this.$emit("success", null, response.data);
                    }
                    this.resetData();
                    this.modal = false;
                })
                .catch((error) => {
                    this.$errorResponse(error);
                })
                .finally(() => {
                    this.submitLoader = false;
                });
        },
        resetData() {
            this.data = {
                pick_pack: {
                    show_pick: {},
                },
                pack: {
                    show_pick: {},
                },
                send_minute: [],
            };
        },
        getData() {
            this.editLoader = true;
            axios
                .get(`/admin/api/pick_and_pack/get/${this.item.id}`)
                .then((response) => {
                    this.data = response.data;
                    if (this.data.order_status_array) {
                        this.order_status_array = this.data.order_status_array;
                        delete this.data.order_status_array;
                    }
                    if (this.data.user_access_array) {
                        this.user_access_array = this.data.user_access_array;
                        delete this.data.user_access_array;
                    }
                    if (this.data.zones_array) {
                        this.zones_array = this.data.zones_array;
                        delete this.data.zones_array;
                    }
                })
                .catch((error) => {})
                .finally(() => {
                    this.editLoader = false;
                });
        },
    },
    watch: {
        modelValue: function (val) {
            this.modal = val;
        },
        modal: function (val) {
            this.$emit("update:modelValue", val);
            if (!val) {
                this.$emit("update:type", null);
                this.$emit("update:item", {});
                this.resetData();
            }
        },
        type: function (val) {
            if (val === "edit") {
                this.getData();
            } else {
                this.resetData();
            }
        },
    },
    emits: ["success", "update:modelValue", "update:type", "update:item"],
};
</script>
