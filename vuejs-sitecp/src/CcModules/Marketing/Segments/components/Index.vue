<template>
    <b-row class="bg-cp pt-3 pb-4 h-100 app-loader-holder scroll-lock">
        <b-col class="pb-5">
            <b-col class="col-lg-12 d-none d-lg-block mb-4">
                <b-breadcrumb :items="breadcrumbItems" />
            </b-col>

            <b-container
                class="transition-width"
            >
                <SettingsHeader
                    :translations="translations"
                    icon="fal fa-layer-group"
                    :title="translations['Segments']"
                    :description="translations['Segments Description']"
                >
                </SettingsHeader>

                <data-table
                    v-bind="table"
                    @pagination-change-page="paginate"
                    :enable-mobile="true"
                    :filters="true"
                    app-name="segments"
                    :get-data="getItems"
                    :filter-options="filterOptions"
                    v-model:query="query"
                >
                </data-table>
            </b-container>
        </b-col>
    </b-row>
</template>
<script>
import Segments from "../js/Segments";
import DataTable from "@components/Table";
import { markRaw } from "vue";
import Name from "./Helpers/TableList/Name";
import Delete from "./Helpers/TableList/Delete";
import moment from "moment";
import SettingsHeader from "@components/SettingsBox/Helpers/SettingsHeader";
import EventBus from '@js/eventBus';
import { toast } from "@js/toast";
import Executing from "./Helpers/TableList/Executing";

export default {
    name: "Index",
    components: {
        DataTable,
        SettingsHeader,
    },
    data() {
        return {
            modal: false,
            translations: {
                Name: this.$t("Name"),
                "Active to": this.$t("Active to"),
                "Subscribers": this.$t("Subscribers"),
                "Status": this.$t("Status"),
                "Campaigns": this.$t("Campaigns"),
                "Last generate": this.$t("Last generate"),
                "Marketing": this.$t("Marketing"),
                "N/A": this.$t("N/A"),
                "Segments": this.$t("Segments"),
                "Segments Description": this.$t("Segments Description"),
                '"{name}" is updated successfully': this.$t('"{name}" is updated successfully'),
                "ID #": this.$t("ID #"),
                "Campaign": this.$t("Campaign"),
                "Subscriber": this.$t("Subscriber"),
                "Has subscribers": this.$t("Has subscribers"),
                "Has campaigns": this.$t("Has campaigns"),
                "Yes": this.$t("Yes"),
                "No": this.$t("No"),
                Finished: this.$t("Finished"),
                Pending: this.$t("Pending"),
            },
            model: new Segments(),
            isLoading: true,
            currentPage: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            data: {
                data: []
            },
            query: this.$route.query,
        };
    },
    methods: {
        handleSegmentPopulated(segment) {
            if(!segment || !segment.id || segment.processing) {
                return;
            }

            this.data.data = this.data.data.map((x) => {
                if(x.id === segment.id) {
                    x = {...x, ...segment}
                    toast.success(this.$trp(this.translations['"{name}" is updated successfully'], {name: segment.name}));
                }

                return x
            })
        },
        getItems(paginate) {
            this.isLoading = true;

            this.model.where(this.query);
            if (Object.keys(this.query).toString().includes("filters") && !paginate) {
                this.currentPage = 1;
            }

            this.model
                .paginate(this.query?.page || this.currentPage, this.query?.perpage || this.perPage)
                .then((result) => {
                    this.data = result;
                    this.$router.push({
                        query: {
                            ...this.$route.query,
                            page: this.data.current_page,
                        },
                    });
                })
                .catch((err) => {
                    this.$errorResponse(err);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        paginate(page) {
            this.currentPage = page;
            this.getItems("paginate");
        },
    },
    created() {
        EventBus.on('SegmentPopulated', this.handleSegmentPopulated);
    },
    computed: {
        table() {
            return {
                data: this.data,
                defaultSorting: [{ key: "id", sortingMode: "desc" }],
                isLoading: this.isLoading,
                paginationLimit: 2,
                columns: [
                    {
                        column: "id",
                        key: "id",
                        sortable: true,
                        title: this.translations["ID #"],
                        showExpandToggle: true,
                    },
                    {
                        column: "name",
                        key: "name",
                        sortable: true,
                        title: this.translations["Name"],
                        component: markRaw(Name),
                        viewRule: (item) => {
                            this.$router.push({
                                name: "product-labels.edit",
                                params: { id: item.id },
                            });
                        },
                    },
                    {
                        column: "processing",
                        key: "processing",
                        title: this.translations["Status"],
                        sortable: true,
                        component: markRaw(Executing),
                    },
                    {
                        column: "subscribers_count",
                        key: "subscribers_count",
                        sortable: true,
                        title: this.translations["Subscribers"],
                    },
                    {
                        column: "campaigns_count",
                        key: "campaigns_count",
                        sortable: true,
                        title: this.translations["Campaigns"],
                    },
                    {
                        column: "last_execute",
                        key: "last_execute",
                        sortable: true,
                        title: this.translations["Last generate"],
                        format: (data) => {
                            return data.last_execute_at
                                ? moment(data.last_execute_at).format(
                                    this.serverSettings("format.dateTime")
                                )
                                : this.translations["N/A"];
                        },
                    },
                    {
                        column: "actions",
                        key: "actions",
                        sortable: false,
                        title: " ",
                        component: markRaw(Delete),
                        onDelete: (item) => {
                            item.deleting = true;
                            this.model
                                .deleteBulk([item.id])
                                .then(() => {
                                    this.getItems();
                                })
                                .catch((err) => {
                                    this.$errorResponse(err);
                                })
                                .finally(() => {
                                    item.deleting = false;
                                });
                        },
                    },
                ],
            }
        },
        breadcrumbItems() {
            return [
                { text: this.translations["Marketing"] },
                { text: this.translations["Segments"] },
            ];
        },
        filterOptions() {
            return [
                {
                    key: "has_campaigns",
                    label: this.translations["Has campaigns"],
                    input: false,
                    options: [
                        { value: "1", label: this.translations["Yes"] },
                        { value: "0", label: this.translations["No"] },
                    ],
                },
                {
                    key: "campaign",
                    label: this.translations["Campaign"],
                    url: "/admin/api/core/marketing/campaigns/search",
                    type: "select",
                    multiple: true,
                },
                {
                    key: "has_subscribers",
                    label: this.translations["Has subscribers"],
                    input: false,
                    options: [
                        { value: "1", label: this.translations["Yes"] },
                        { value: "0", label: this.translations["No"] },
                    ],
                },
                {
                    key: "subscriber",
                    label: this.translations["Subscriber"],
                    url: "/admin/api/core/marketing/subscribers/search",
                    type: "select",
                    multiple: true,
                },
                {
                    key: "processing",
                    label: this.translations["Status"],
                    options: [
                        {value: "0", label: this.translations["Finished"]},
                        {value: "1", label: this.translations["Pending"]},
                    ],
                },
            ];
        }
    }
};
</script>
