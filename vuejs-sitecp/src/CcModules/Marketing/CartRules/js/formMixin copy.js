import _, { filter } from 'lodash'

export default {
    methods: {
        rowNormalise(val) {
            for (let row = 1; row < this.item.rows.length; row++) {
                this.triggersNormalise(val, row)
                this.actionsNormalise(val, row)
            }
        },
        triggersNormalise(val, row) {
            if(val?.triggers?.length) {
                for (let conditionIndex in val.triggers) {
                    let condition = val.triggers[conditionIndex];

                    if (this.item.rows[row]?.triggers?.[conditionIndex]) {
                        this.item.rows[row].triggers[conditionIndex] =
                            {
                                ...this.item.rows[row].triggers[conditionIndex],
                                condition_type: condition.condition_type,
                                filter_type: condition.filter_type,
                                record_type: condition.record_type,
                                value_type: condition.value_type,
                                records: condition.records,
                                records_init: condition.records_init,
                                value: this.item.rows[row]?.triggers?.[conditionIndex]?.value || null,
                                sub_value: this.item.rows[row]?.triggers?.[conditionIndex]?.sub_value || null,
                                operator: this.item.rows[row]?.triggers?.[conditionIndex]?.operator || 'in'
                            }
                    } else {
                        this.item.rows[row].triggers = this.item.rows[row].triggers || [];
                        this.item.rows[row].triggers[conditionIndex] = _.cloneDeep({...condition, value: null, sub_value: null});
                    }

                }
            } else {
                this.item.rows[row].triggers = []
            }
        },
        actionsNormalise(val, row) {
            let action = val.action;
            if (this.item.rows[row]?.action) {
                this.item.rows[row].action = {
                    ...this.item.rows[row].action,
                    condition_type: action.condition_type,
                    record_type: action.record_type,
                    records: action.records,
                    records_init: action.records_init,
                }
            } else {
                this.item.rows = this.item.rows || []
                this.item.rows[row] = this.item.rows[row] || {}
                this.item.rows[row].action = _.cloneDeep(action)
            }
        },
        addTrigger() {
            this.item.rows = this.item.rows || []
            this.item.rows[this.rowIndex] = this.item.rows[this.rowIndex] || {}
            this.item.rows[this.rowIndex].triggers = this.item.rows[this.rowIndex].triggers || []

            this.item.rows[this.rowIndex].triggers.push({
                // conditions: [
                    ...this.getConditionDefaultStructure(this.item.rows[this.rowIndex].triggers.length)
                // ],
            })
        },
        getConditionDefaultStructure(sort, condition_type='cart') {
            let selectedOptionsCartPerRow = (this.item?.rows?.[this.rowIndex]?.triggers || [])
                                            .filter((item) => item.condition_type === 'cart').map((item) => item.filter_type)
            let allowedOptionsCartPerRow = ['cart_amount', 'cart_products_count', 'cart_quantity']
                                            .filter((item) => !selectedOptionsCartPerRow.includes(item))
            
            let selectedOptionsProductPerRow = (this.item?.rows?.[this.rowIndex]?.triggers || [])
                                                .filter((item) => item.condition_type === 'product').map((item) => item.filter_type)
            let allowedOptionsProductPerRow = ['product-tags', 'product-categories', 'products', 'vendors', 'collections',
                                                'product_title', 'product_variant', 'product_option', 'product_new', 'product_featured', 'product_sale', 
                                                'product_amount', 'product_line_amount', 'product_quantity']
                                                .filter((item) => !selectedOptionsProductPerRow.includes(item))


            let filter_type = null;
            let type = condition_type;
            let record_type = null;

            if (type === 'cart') {
                filter_type = allowedOptionsCartPerRow[0]
                if (allowedOptionsCartPerRow.length === 0) {
                    type = 'product'
                }
            } 
            
            if (type === 'product') {
                filter_type = allowedOptionsProductPerRow[0]
                record_type = allowedOptionsProductPerRow[0]
                console.log(filter_type)
            }

            return {
                condition_type: type,
                // filter_type: null,
                // record_type: null,  
                filter_type,
                record_type,
                records: [],
                records_init: [],
                sorting: sort,
                sub_value: null,
                value: null,
                value_type: this.$route.params.rule && this.$route.params.rule === 'single' ? 'gte' : 'between',
                operator: 'in',
            }
        },
        removeTrigger(index) {
            if (this.item.rows && Array.isArray(this.item.rows)) {
                for(let rowIndex in this.item.rows) {
                    if (this.item.rows[rowIndex] && this.item.rows[rowIndex].triggers && Array.isArray(this.item.rows[rowIndex].triggers)) {
                        this.item.rows[rowIndex].triggers.splice(index, 1);
                    }
                }
            }
        },
        addActionTrigger() {
            this.item.rows = this.item.rows || []
            this.item.rows[this.rowIndex] = this.item.rows[this.rowIndex] || {}
            this.item.rows[this.rowIndex].action = this.item.rows[this.rowIndex].action || {}
            this.item.rows[this.rowIndex].action = this.item.rows[this.rowIndex].action || {}
            this.item.rows[this.rowIndex].action.triggers = this.item.rows[this.rowIndex].action.triggers || []

            this.item.rows[this.rowIndex].action.triggers.push({
                conditions: [
                    this.getConditionDefaultStructure(this.item.rows[this.rowIndex].action.triggers.length, 'product')
                ],
            })
        },
        removeActionTrigger(conditionIndex) {
            if (this.item.rows && Array.isArray(this.item.rows) && Array.isArray(this.item.rows[this.rowIndex]?.action?.triggers)) {
                this.item.rows[this.rowIndex].action.triggers.splice(conditionIndex, 1);
            }
        },
        addBetweenRow() {
            if(!this.item.rows) {
                this.item.rows = []
            }

            this.item.rows.push({message:null})

            this.rowNormalise(this.item.rows[0] || [])
        },
        removeBetweenRow(index) {
            if (this.item.rows && Array.isArray(this.item.rows)) {
                this.item.rows.splice(index, 1);
            }
        },
    }
}