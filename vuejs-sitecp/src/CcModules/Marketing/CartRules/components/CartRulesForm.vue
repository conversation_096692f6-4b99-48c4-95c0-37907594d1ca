<template>
    <Loading v-if="loading" :loading="loading" class="app-loader-center" />
    <b-container v-else class="container-medium pb-5 mb-3">
        <GenerateRuleAI
            v-model="item"
            :load-template="getTemplateAndLoadContent"
            @loadGenerated="(val) => (item = val)"
        />
        <GeneralSettingsRow
            v-model="item"
            :response-errors="responseErrors"
            v-model:start="startDate"
            v-model:end="endDate"
            v-model:boxes="boxes"
            :showHr="isRange"
        />

        <div
            v-for="(row, index) in item.rows || []"
            class="pb-3 position-relative"
            style="transition: all 200ms ease-in-out"
            :class="[{ 'border-range pt-3 px-4 mt-4': isRange }, `rule-range-${index}`]"
        >
            <span v-if="isRange" class="range-badge">
                {{
                    $trp(translations["Range {num}"], { num: index + 1 })
                }}</span
            >
            <RuleMessage
                v-model="item"
                v-model:boxes="boxes"
                :row-index="index"
                :response-errors="responseErrors"
                :isRange="isRange"
            />
            <Row
                v-model="item"
                :row-index="index"
                :response-errors="responseErrors"
            />
        </div>
        <b-row v-if="isRange" class="mt-3">
            <b-col v-if="addNewRangeCheck">
                <a
                    href="javascript:;"
                    @click.prevent="addBetweenRow"
                    class="btn btn-ghost text-nowrap"
                >
                    <i class="fa-regular fa-plus fa-sm"></i>
                    {{ translations["Add amount range"] }}
                </a>
            </b-col>
            <b-col
                v-else
                class="col-12 d-flex flex-wrap w-100 info-box info-box-error align-items-center justify-content-between rounded-3 p-3 my-3"
            >
                <p
                    class="m-0 d-flex align-items-center justify-content-start gap-3"
                >
                    <i class="far fa-exclamation-circle fs-5"></i>
                    {{
                        $trp(
                            translations[
                                "You have reached the maximum limit of {count} ranges for this rule"
                            ],
                            { count: ruleRangeCountFeature?.current }
                        )
                    }}
                </p>
                <button
                    class="btn btn-white"
                    @click="() => setFeatureModal(ruleRangeCountFeature)"
                >
                    {{ translations["Purchase more"] }}
                </button>
            </b-col>
        </b-row>
        <SubmitChanges
            v-if="!loading && !preventSave"
            v-model="item"
            :submit-loader="submitLoading"
            v-model:response-errors="responseErrors"
            :disable-save="submitLoading"
            :save-func="saveItem"
        />
    </b-container>
</template>
<script>
import axios from "axios";
import moment from "moment";
import { toast } from "@js/toast";
import CartRules from "../js/CartRules";
import formMixin from "../js/formMixin";

import useSharedState from "../js/useSharedState";

import Loading from "@components/Loading";
import Row from "./Helpers/Form/Row";
import GeneralSettingsRow from "./Helpers/Form/SettingRows/GeneralSettingsRow";
import GenerateRuleAI from "./Helpers/Form/SettingRows/GenerateRuleAI";
import RuleMessage from "./Helpers/Form/SettingRows/RuleMessage";

import SubmitChanges from "@components/SubmitChanges";

export default {
    name: "CartRulesForm",
    mixins: [formMixin],
    components: {
        Loading,

        GeneralSettingsRow,
        GenerateRuleAI,
        RuleMessage,

        Row,
        SubmitChanges,
    },
    props: {
        app: {
            required: false,
            default: {},
        },
        settings: {
            required: false,
            default: {},
        },
        errors: {
            required: false,
            default: {},
        },
        action: {
            required: false,
            type: String,
        },
    },
    setup() {
        const {
            ruleRangeCountFeature,
            ruleConditionCountFeature,
            ruleActionCountFeature,
            setFeatureModal,
            createdTemplate,
        } = useSharedState();

        return {
            ruleRangeCountFeature,
            ruleConditionCountFeature,
            ruleActionCountFeature,
            setFeatureModal,
            createdTemplate,
        };
    },
    data() {
        return {
            responseErrors: this.errors,
            type: this.$route?.params?.type,
            rule: this.$route?.params?.rule,
            preventSave: true,
            boxes: {
                general: {
                    key: "general",
                    group: "cart-rules",
                    title: null,
                    infoTitle: "General settings",
                    infoDescription: "Dial lorem ipsum",
                },
                rule_message: {
                    key: "rule_message",
                    group: "cart-rules",
                    title: null,
                    infoTitle: "Rule message",
                    infoDescription: "Rule Message Dial lorem ipsum",
                },
            },

            translations: {
                Save: this.$t("Save"),
                "The record has been successfully created": this.$t(
                    "The record has been successfully created"
                ),
                "The record has been successfully edited": this.$t(
                    "The record has been successfully edited"
                ),
                "Range {num}": this.$t("Range {num}"),
                "Add amount range": this.$t("Add amount range"),
                "You have reached the maximum limit of {count} ranges for this rule":
                    this.$t(
                        "You have reached the maximum limit of {count} ranges for this rule"
                    ),
                "Purchase more": this.$t("Purchase more"),
                //error messages
                "Field is required": this.$t("Field is required"),
                "Field must be at least {min} characters": this.$t(
                    "Field must be at least {min} characters"
                ),
                "Field may not be greater than {max} characters": this.$t(
                    "Field may not be greater than {max} characters"
                ),
                "Field must be a valid date": this.$t(
                    "Field must be a valid date"
                ),
                'The "active to" field must be a date after or equal to the "active from" date':
                    this.$t(
                        'The "active to" field must be a date after or equal to the "active from" date'
                    ),
                "You must have at least one row with conditions": this.$t(
                    "You must have at least one row with conditions"
                ),
                "You may have a maximum of {max} rows": this.$t(
                    "You may have a maximum of {max} rows"
                ),
                "You must have at least one condition": this.$t(
                    "You must have at least one condition"
                ),
                "You may have a maximum of {max} conditions": this.$t(
                    "You may have a maximum of {max} conditions"
                ),
                'The "conditions" field must be an array': this.$t(
                    'The "conditions" field must be an array'
                ),
                'Each condition in the "conditions" field is required': this.$t(
                    'Each condition in the "conditions" field is required'
                ),
                "The selected records are invalid or do not exist": this.$t(
                    "The selected records are invalid or do not exist"
                ),
                "Field must be a string": this.$t("Field must be a string"),
                "Field must be a number": this.$t("Field must be a number"),
                "Field must be a integer": this.$t("Field must be a integer"),
                "Field is invalid": this.$t("Field is invalid"),
                "Field must be at least {min}": this.$t(
                    "Field must be at least {min}"
                ),
                "Field may not be greater than {max}": this.$t(
                    "Field may not be greater than {max}"
                ),
                "Field must be greater than {value}": this.$t(
                    "Field must be greater than {value}"
                ),
                "Unexpected error. Please try again later": this.$t(
                    "Unexpected error. Please try again later"
                ),

                "You already have a condition with <strong>{filter}</strong>":
                    this.$t(
                        "You already have a condition with <strong>{filter}</strong>"
                    ),

                "Cart amount": this.$t("Cart amount"),
                "Unique products count": this.$t("Unique products count"),
                "Cart quantity": this.$t("Cart quantity"),
                "Specific product": this.$t("Specific product"),
                Manufacturer: this.$t("Manufacturer"),
                "Product tags": this.$t("Product tags"),
                "Product collections": this.$t("Product collections"),
                "Product categories": this.$t("Product categories"),
                "Product title": this.$t("Product title"),
                "Product variant value": this.$t("Product variant value"),
                "Product option value": this.$t("Product option value"),
                "Product marked as new": this.$t("Product marked as new"),
                "Product marked as featured": this.$t(
                    "Product marked as featured"
                ),
                "Product on sale": this.$t("Product on sale"),
                "Product amount": this.$t("Product amount"),
                "Product line amount": this.$t("Product line amount"),
                "Product quantity": this.$t("Product quantity"),
                "Order amount": this.$t("Order amount"),
                "Order count": this.$t("Order count"),
                "Customer group": this.$t("Customer group"),
            },
            model: new CartRules(),
            loading: true,
            submitLoading: false,
            item: {
                status: 0,
                active_from: null,
                active_to: null,
                noExpire: true,
                name: "",
                title: "",
                rows: [
                    {
                        triggers: [this.getConditionDefaultStructure(0)],
                        action: {
                            action_type: "discount",
                            value_type: "percent",
                            triggers: [this.getConditionDefaultStructure(0)],
                        },
                    },
                ],
            },
        };
    },
    methods: {
        scrollToMainError() {
            let mainError = document.querySelector("#main-error-rule");
            if (mainError) {
                mainError.scrollIntoView({
                    block: "center",
                    behavior: "smooth",
                });
            }
        },
        async getItem() {
            this.loading = true;

            try {
                this.item = await this.model.find(
                    this.$route.params.id,
                    this.perPage
                );
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.loading = false;
                setTimeout(() => {
                    this.preventSave = false;
                }, 500);
            }
        },
        checkConditionsInRows() {
            let rows = this.item.rows;

            rows.forEach((row, rowIndex) => {
                let count = {
                    //cart
                    cart_amount: 0,
                    cart_products_count: 0,
                    cart_quantity: 0,

                    //product
                    tag: 0,
                    category: 0,
                    product: 0,
                    vendor: 0,
                    selection: 0,
                    product_title: 0,
                    product_variant: 0,
                    product_option: 0,
                    product_new: 0,
                    product_featured: 0,
                    product_sale: 0,
                    product_amount: 0,
                    product_line_amount: 0,
                    product_quantity: 0,

                    //customer
                    order_amount: 0,
                    order_count: 0,
                    customer_group: 0,
                };

                row.triggers.forEach((trigger, conditionIndex) => {
                    count[trigger.filter_type] += 1;

                    if (
                        count[trigger.filter_type] > 1 ||
                        count[trigger.record_type] > 1
                    ) {
                        this.responseErrors[
                            `rows.${rowIndex}.triggers.${conditionIndex}.filter_type`
                        ] = this.$trp(
                            this.translations[
                                "You already have a condition with <strong>{filter}</strong>"
                            ],
                            {
                                filter:
                                    this.msgEnum[trigger.filter_type] ||
                                    this.msgEnum[trigger.record_type],
                            }
                        );
                    }
                });
            });
        },
        async saveItem() {
            this.submitLoading = true;
            this.responseErrors = {};

            this.checkConditionsInRows();

            if (Object.keys(this.responseErrors).length > 0) {
                this.submitLoading = false;
                return;
            }

            try {
                let result;
                if (this.item.id) {
                    result = await this.model.update(this.item, this.item.id);
                } else {
                    result = await this.model.create(this.item);
                }

                this.item = { ...this.item, ...result };

                setTimeout(()=>{
                    this.$router.push({
                        name: "apps.cart-rules.edit",
                        params: { id: result.id },
                    });
                },1500)

            } catch (err) {
                this.$errorResponse(err);
                (this.item.rows || []).forEach((row, index) => {
                    if (this.responseErrors[`rows.${index}.triggers`]) {
                        setTimeout(() => {
                            this.scrollToMainError();
                        }, 200);
                    }
                });
            } finally {
                this.submitLoading = false;
            }
        },
        async getTemplateAndLoadContent() {
            this.loading = true;
            try {
                const req = await axios.get(
                    `/admin/api/core/demo/templates/cart-rules/${this.$route.params.rule}`
                );

                let response = req.data;

                let template =
                    typeof response.template === "string"
                        ? JSON.parse(response.template)
                        : response.template;

                this.item = {
                    ...template,
                    active_from: null,
                    active_to: null,
                    name: "",
                    noExpire: false,
                    status: 0,
                };
            } catch (err) {
                this.$errorResponse(err);
                this.$router.push({ name: "error404" });
            } finally {
                this.loading = false;
                this.preventSave = false;
            }
        },
    },
    beforeUnmount() {
        this.createdTemplate = null;
    },
    async mounted() {
        if (
            this.$route?.query?.["ai-generated"] === "yes" &&
            this.createdTemplate
        ) {
            this.item = this.createdTemplate;
            this.loading = false;
            this.preventSave = false;
            this.$router.push({ query: {} });
            setTimeout(() => {
                this.item.title ??= '';
                this.item.title = this.item.title + ' ';
                console.log(this.item.title);
            }, 750);
            return;
        }
        if (
            this.$route?.params?.type &&
            this.$route?.params?.rule &&
            this.$route?.params?.type === "template"
        ) {
            await this.getTemplateAndLoadContent();
            return;
        }
        if (this.action === "edit") {
            await this.getItem();
        } else {
            this.loading = false;
            this.preventSave = false;
        }
    },
    created() {
        this.helpBoxes(this.boxes);
    },
    computed: {
        msgEnum() {
            return {
                //cart
                cart_amount: this.translations["Cart amount"],
                cart_products_count: this.translations["Unique products count"],
                cart_quantity: this.translations["Cart quantity"],

                //product
                tag: this.translations["Product tags"],
                category: this.translations["Product categories"],
                product: this.translations["Specific product"],
                vendor: this.translations["Manufacturer"],
                selection: this.translations["Product collections"],
                product_title: this.translations["Product title"],
                product_variant: this.translations["Product variant value"],
                product_option: this.translations["Product option value"],
                product_new: this.translations["Product marked as new"],
                product_featured:
                    this.translations["Product marked as featured"],
                product_sale: this.translations["Product on sale"],
                product_amount: this.translations["Product amount"],
                product_line_amount: this.translations["Product line amount"],
                product_quantity: this.translations["Product quantity"],

                //customer
                order_amount: this.translations["Order amount"],
                order_count: this.translations["Order count"],
                customer_group: this.translations["Customer group"],
            };
        },
        addNewRangeCheck() {
            if (
                this.ruleRangeCountFeature &&
                +this.ruleRangeCountFeature?.used >=
                    +this.ruleRangeCountFeature?.current
            ) {
                return false;
            }
            return true;
        },
        startDate: {
            get() {
                return this.item?.active_from
                    ? moment(this.item.active_from).toDate()
                    : null;
            },
            set(value) {
                this.item.active_from = value
                    ? moment(value).format("YYYY-MM-DD")
                    : null;
            },
        },
        endDate: {
            get() {
                return this.item?.active_to
                    ? moment(this.item.active_to).toDate()
                    : null;
            },
            set(value) {
                this.item.active_to = value
                    ? moment(value).format("YYYY-MM-DD")
                    : null;
            },
        },
    },
    watch: {
        endDate(value) {
            if (!value) {
                this.item.noExpire = true;
            }
        },
        "item.rows.0": {
            deep: true,
            handler(val) {
                if (this.item.rows.length < 1) {
                    return;
                }

                this.rowNormalise(val);
            },
        },
        isRange(val) {
            if (!val) {
                this.item.rows = this.item.rows.slice(0, 1);
            }
        },
        "item.rows": {
            handler(val) {
                if ((val || []).length) {
                    this.ruleRangeCountFeature.used = val.length;
                }
            },
            deep: true,
            immediate: true,
        },
    },
};
</script>
<style>
@import '../scss/style.scss';
</style>
