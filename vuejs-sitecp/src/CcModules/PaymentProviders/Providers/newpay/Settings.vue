<template>
    <SettingsFormPayments
        v-model:app="application"
        v-model:provider="settings"
        :response-errors="responseErrors"
        :rows="['logo', 'mode', 'amount', 'discount']"
        :provider-key="key"
        v-model:prevent-save="preventSave"
    >
        <template #settings>
            <SettingsBox
                v-model:settings="settings"
                v-model:boxes="settingsRows"
                v-model:setting-open="preventSave"
                :response-errors="responseErrors"
                :box-key="key"
                :translationLabels="translations"
            />
        </template>
    </SettingsFormPayments>
</template>

<script>
import SettingsFormPayments from "../SettingsForm/SettingsFormPayments";
import SettingsBox from "@components/SettingsBox/SettingsBox";

import providerMixin from "../../providerMixin";

export default {
    mixins: [providerMixin],
    components: {
        SettingsFormPayments,
        SettingsBox,
    },
    data() {
        return {
            key: "newpay",
            translations: {
                "API key is required": this.$t("API key is required"),
                "API Secret is required": this.$t("API Secret is required"),
            },
            settingsRows: [],
        };
    },
    created() {
        this.setupRows();
    },
    watch: {
        "settings.configuration.mode"(val) {
            this.updateSettingsRows(val);
        },
    },
    methods: {
        setupRows() {
            this.settingsRows = [
                {
                    key: "configuration",
                    group: "newpay",
                    title: "NewPay settings",
                    titleHelp: {
                        label: "NewPay settings",
                        parameters: [],
                    },
                    infoTitle: "NewPay settings",
                    infoTitleHelp: null,
                    infoDescription: "NewPay settings description",
                    editMethod: "slide",
                    isVisible: true,
                    cardClass:
                        this.settings.configuration.mode === "live"
                            ? "border-color-live"
                            : "border-color-test",
                    fields: [
                        {
                            key: "configuration.api_key",
                            type: "string",
                            label: "NewPay API key",
                            disabled: false,
                            readonly: false,
                            help: null,
                            inputType: "string",
                            required: true
                        },
                        {
                            key: "configuration.api_secret",
                            type: "string",
                            label: "NewPay API secret",
                            disabled: false,
                            readonly: false,
                            help: null,
                            inputType: "string",
                            required: true
                        },
                    ],
                },
                {
                    key: "newpay_url",
                    group: "newpay",
                    title: 'NewPay URL',
                    titleHelp: null,
                    infoTitle: "NewPay URL's",
                    infoTitleHelp: null,
                    infoDescription: "NewPay URL's description",
                    editMethod: "inline",
                    isVisible: true,
                        fields: [
                        {
                            type: "title",
                            label: this.provider.webhook_url,
                            inputType: "title",
                        },
                    ],
                },
            ];
        },
    },
};
</script>
