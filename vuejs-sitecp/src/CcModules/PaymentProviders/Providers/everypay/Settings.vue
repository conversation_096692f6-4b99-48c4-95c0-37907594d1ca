<template>
    <SettingsFormPayments
        v-model:app="application"
        v-model:provider="settings"
        :response-errors="responseErrors"
        :rows="['logo', 'mode', 'amount', 'discount']"
        :provider-key="key"
        v-model:prevent-save="preventSave"
    >
        <template #settings>
            <SettingsBox
                v-model:settings="settings"
                v-model:boxes="settingsRows"
                v-model:setting-open="preventSave"
                :response-errors="responseErrors"
                :box-key="key"
                :translationLabels="translations"
            />
        </template>
    </SettingsFormPayments>
</template>

<script>
import providerMixin from "../../providerMixin";
import SettingsFormPayments from "./../SettingsForm/SettingsFormPayments";
import SettingsBox from "@components/SettingsBox/SettingsBox";


export default {
    mixins: [providerMixin],
    components: {
        SettingsFormPayments,
        SettingsBox,
    },
    data() {
        return {
            key: "everypay",
            settingsRows: [],
            translations: {
                "Public key is required.": this.$t("Public key is required."),
                "Secret key is required.": this.$t("Secret key is required."),
            },
        };
    },
    created() {
        this.setupRows();
    },
    watch: {
        "settings.configuration.mode"(val) {
            this.updateSettingsRows(val);
        },
    },
    methods: {
        setupRows() {
            this.settingsRows = [
                {
                    key: "configuration",
                    group: "everypay",
                    title: "Everypay settings",
                    titleHelp: {
                        label: "Everypay settings",
                        parameters: [],
                    },
                    infoTitle: "Everypay settings",
                    infoTitleHelp: null,
                    infoDescription: "Everypay settings description",
                    editMethod: "slide",
                    cardClass:
                        this.settings.configuration.mode === "live"
                            ? "border-color-live"
                            : "border-color-test",
                    isVisible: true,
                    fields: [
                        {
                            key: "configuration.public_key",
                            type: "string",
                            label: "Everypay public key",
                            disabled: false,
                            readonly: false,
                            help: null,
                            inputType: "string",
                            required: true
                        },
                        {
                            key: "configuration.secret_key",
                            type: "string",
                            label: "Everypay secret key",
                            disabled: false,
                            readonly: false,
                            help: null,
                            inputType: "string",
                            required: true
                        },
                    ],
                },
            ];
        },
    },
};
</script>
