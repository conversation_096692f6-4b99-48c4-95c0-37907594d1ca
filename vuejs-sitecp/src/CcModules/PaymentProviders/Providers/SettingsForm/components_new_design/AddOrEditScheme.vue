<template>
    <div>
        <a
            class="btn btn-primary btn-md"
            href="javascript:;"
            @click.prevent="
        this.modal = true;
        this.loading = false;
      "
            v-html="translations['Add Schema']"
        >
        </a>
        <b-modal
            v-model="modal"
            class="modal-right"
            size="lg"
            no-footer="true"
            header-class="edit-settings-modal-header"
            body-class="edit-settings-modal-content"
            :no-close-on-backdrop="true"
        >
            <template #header>
                <div
                    class="col-12 text-right d-flex justify-content-end align-items-center gap-2"
                >
                    <button
                        @click="modal = false"
                        type="button"
                        class="btn btn-white"
                        v-html="translations['Cancel']"
                    ></button>

                    <button
                        type="button"
                        class="btn btn-primary"
                        @click="createNew"
                        :disabled="submitLoader || loading"
                    >
                        <b-spinner small v-if="submitLoader"></b-spinner>
                        {{ translations["Save"] }}
                    </button>
                </div>
            </template>
            <Loading
                v-if="loading"
                style="margin-left: 45%; margin-top: 45%"
                :loading="loading"
            />
            <b-card v-else>
                <b-row>
                    <b-col>
                        <InputComponent
                            v-model="schema.months"
                            type="number"
                            :label-text="translations['Number of months']"
                            :error="responseErrors['months']"
                        />
                        <SelectWithAjax
                            v-model:val="schema.products"
                            :options="this.id ? product_options : null"
                            :label="translations['Discount target - Product/s']"
                            :column-style="true"
                            :cols-width="12"
                            :request-on-search="true"
                            :searchable="true"
                            mode="tags"
                            api-url="/admin/api/core/products/search"
                            :error="responseErrors['products']"
                            :filterResults="false"
                        />
                    </b-col>
                </b-row>
            </b-card>
        </b-modal>
    </div>
</template>

<script>
import axios from "axios";
import {buildFormData} from "@js/shippingHelpers";
import {toast} from "@js/toast";

import InputComponent from "@components/Form/InputComponent";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import Loading from "@components/Loading";

export default {
    name: "AddOrEditPromotion",
    components: {
        InputComponent,
        Loading,
        SelectWithAjax,
    },
    props: {
        getterFunc: {
            type: Function,
            required: false,
        },
        id: {
            required: false,
            default: null,
        },
    },
    data() {
        return {
            schema: {
                months: "",
                products: [],
            },
            product_options: [],
            modal: false,
            submitLoader: false,
            loading: true,
            responseErrors: {},
            translations: {
                Cancel: this.$t("Cancel"),
                Save: this.$t("Save"),
                "Add Schema": this.$t("Add Schema"),
                "Number of months": this.$t("Number of months"),
                "Discount target - Product/s": this.$t("Discount target - Product/s"),
                "Error occurred while connecting": this.$t("Error occurred while connecting"),
                "Saved Successfully": this.$t("Saved Successfully"),
                "Error occurred while saving": this.$t("Error occurred while saving"),
            },
        };
    },
    methods: {
        getData() {
            this.loading = true;
            this.modal = true;

            let currentID = this.id || 0;

            axios
                .get(
                    `/admin/api/payment_providers/dsk_zero/schemes/edit/${currentID}?json=1`
                )
                .then((res) => {
                    this.schema = res.data?.schema;
                    this.product_options = res.data?.products;
                })
                .catch((err) => {
                    toast.error(this.translations["Error occurred while connecting"]);
                })
                .finally(() => {
                    this.loading = false;
                    this.initialTableLoaded = true;
                });
        },
        createNew() {
            this.submitLoader = true;
            this.responseErrors = {};
            let url = this.id
                ? `/admin/settings/payment_providers/dsk_zero/schemes/edit/${this.id}`
                : "/admin/settings/payment_providers/dsk_zero/schemes/create";

            let body = {
                months: this.schema.months,
                products: this.schema.products.length > 0 ? this.schema.products.join(",") : "",
            };

            const formData = new FormData();

            buildFormData(formData, body);

            axios
                .post(url, formData, {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                })
                .then((res) => {
                    toast.success("Saved successfully");
                    this.modal = false;
                    this.getterFunc();
                })
                .catch((err) => {
                    this.$errorResponse(err)
                })
                .finally(() => {
                    this.submitLoader = false;
                });
        },
    },
    watch: {
        id(val) {
            if (val) {
                this.getData();
            }
        },
        modal(val) {
            if (!val) {
                this.schema = {
                    months: "",
                    products: [],
                };
                this.product_options = [];
                this.responseErrors = {};
                this.$emit("update:id", null);
            }
        },
    },
    emits: ["update:id"],
};
</script>
