<template>
    <data-table
        v-bind="table"
        v-model="ids"
        @pagination-change-page="paginate"
        :enable-mobile="true"
        :app-name="`shipping-${providerKey}`"
        :get-data="getData"
        :filterOptions="filterOptions"
        v-model:query="query"
        :filters="true"
    >
        <template #button>
            <div class="shipments-filter-by-date">
                <SelectWithAjax
                    @selected-item="debouncedGetData"
                    :val="date"
                    :options="dateOptions"
                    :column-style="true"
                    :cols-width="12"
                    :placeholder="translations['Select shipping day']"
                    selectClasses=""
                />
            </div>
        </template>
        <TableActions
            v-model="ids"
            :app-key="`shipping-${providerKey}`"
            :enable-mobile="true"
            :ignoreDefault="true"
            :actions="bulkActions"
            :externalFunction="handleSelectFormat"
        />
    </data-table>
    <PrintFormatSelectModal
        v-model="printModal"
        v-model:ids="ids"
        :providerKey="providerKey"
    />
</template>
<script>
import { markRaw } from "vue";
import moment from "moment";
import Shipments from "../js/Shipments";
import Labels from "../js/Labels";

import DataTable from "@components/Table";
import TableActions from "@components/TableActions";
import PrintFormatSelectModal from "./Helpers/ShipmentsTab/PrintFormatSelectModal";
import SelectWithAjax from "@components/Form/SelectWithAjax";

import Date from "./../components/Helpers/ShipmentsTab/Date";
import TrackingNumber from "./../components/Helpers/ShipmentsTab/TrackingNumber";
import OrderNumber from "./../components/Helpers/ShipmentsTab/OrderNumber";
import SinglePrint from "./Helpers/ShipmentsTab/SinglePrint";

export default {
    name: "Shipments",
    components: {
        DataTable,
        TableActions,
        PrintFormatSelectModal,
        SelectWithAjax,
    },
    props: {
        providerKey: {
            type: String,
            required: false,
        },
        providerData: {
            required: false,
            default: {},
            type: Object,
        },
        isCredentialsValid: {
            required: false,
            default: false,
        },
        isAllContentLoaded: {
            required: false,
            default: false,
        },
        responseErrors: {
            default: {},
            type: Object,
        },
        credentialErrors: {
            default: {},
            type: Object,
        },
        app: {
            type: Object,
            default: {},
            required: false,
        },
        isValid: {
            required: false,
            default: false,
        },
        actions: {
            required: false,
            default: {},
            type: Object,
        },
        disableSave: {
            required: false,
            default: false,
        },
        credentialsErrors: {
            required: false,
            default: {},
        },
    },
    data() {
        return {
            ids: [],
            loading: true,
            page: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            query: this.$route.query,
            model: new Shipments(),
            tableData: {
                data: [],
            },
            date: null,
            printModal: false,
            debouncedGetData: null,

            translations: {
                "Date added": this.$t("Date added"),
                "Shipping date Delivery": this.$t("Shipping date Delivery"),
                "Tracking number": this.$t("Tracking number"),
                Status: this.$t("Status"),
                "Total price": this.$t("Total price"),
                "Payment provider": this.$t("Payment provider"),
                "Shipping date": this.$t("Shipping date"),
                "Print labels": this.$t("Print labels"),
                exactly: this.$t("exactly"),
                before: this.$t("before"),
                after: this.$t("after"),
                "before or equals": this.$t("before or equals"),
                "after or equals": this.$t("after or equals"),
                Yesterday: this.$t("Yesterday"),
                Today: this.$t("Today"),
                Tomorrow: this.$t("Tomorrow"),
                is: this.$t("is"),
                "Select shipping day": this.$t("Select shipping day"),
                "Order ID": this.$t("Order ID"),
            },
        };
    },
    methods: {
        async paginate(page) {
            this.page = page;
            await this.getData();
        },
        async handleSelectFormat() {
            let printType = this.providerData?.settings?.speedy_print_size;

            if (
                !printType ||
                (Array.isArray(printType) && printType.length > 0) ||
                printType === "ALL"
            ) {
                this.printModal = true;
                return;
            }
            let singlePrint = null;

            if (this.$route.query.singlePrint) {
                singlePrint = this.$route.query.singlePrint;
            }

            try {
                let model = new Labels(this.providerKey);

                let payload = {
                    order_ids: singlePrint ? [singlePrint] : this.ids,
                    type: printType,
                    is_return:
                        this.$route.name ===
                        `apps.${this.providerKey}.shipments-return`
                            ? true
                            : false,
                };

                const response = await model.create(payload, {
                    responseType: "blob",
                });

                const blob = new Blob([response], { type: "application/pdf" });
                const url = URL.createObjectURL(blob);
                window.open(url);
                URL.revokeObjectURL(url);
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.$router.push({ query: {} });
            }
        },
        async getData() {
            this.loading = true;

            try {
                this.loading = true;

                let filters = {
                    "filters[shipping][operator]": "in",
                    "filters[shipping][value][]": this.providerKey,
                    "filters[status_fulfillment][operator]": "in",
                    "filters[status_fulfillment][value]": "fulfilled",
                    ...(this.$route.name ===
                    `apps.${this.providerKey}.shipments-return`
                        ? { "filters[is_return_voucher]": 1 }
                        : {}),
                };

                this.model.where({
                    ...this.query,
                    ...filters,
                });

                const response = await this.model.paginate(
                    this.query?.page || this.page,
                    this.query?.perpage || this.perPage
                );
                this.tableData = response;
                this.loading = false;
                this.date = null;
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.loading = false;
            }
        },
        async filterByDate(option) {
            this.date = option.id;
            let value = option.id;
            this.query = {
                ...this.query,
                "filters[shipping_date][operator]": "is",
                "filters[shipping_date][value]": value,
            };
            await this.getData();
        },
    },
    computed: {
        dateOptions() {
            return [
                {
                    id: moment()
                        .subtract(1, "days")
                        .format(this.serverSettings("format.date")),
                    name: this.translations.Yesterday,
                },
                {
                    id: moment().format(this.serverSettings("format.date")),
                    name: this.translations.Today,
                },
                {
                    id: moment()
                        .add(1, "days")
                        .format(this.serverSettings("format.date")),
                    name: this.translations.Tomorrow,
                },
            ];
        },
        filterOptions() {
            return [
                {
                    key: "date_added",
                    label: this.translations["Date added"],
                    type: "date",
                    options: [
                        { value: "lt", label: this.translations["before"] },
                        {
                            value: "lte",
                            label: this.translations["before or equals"],
                        },
                        { value: "gt", label: this.translations["after"] },
                        {
                            value: "gte",
                            label: this.translations["after or equals"],
                        },
                    ],
                },
                {
                    key: "shipping_date",
                    label: this.translations["Shipping date"],
                    type: "date",
                    options: [
                        { value: "is", label: this.translations["is"] },
                        { value: "lt", label: this.translations["before"] },
                        {
                            value: "lte",
                            label: this.translations["before or equals"],
                        },
                        { value: "gt", label: this.translations["after"] },
                        {
                            value: "gte",
                            label: this.translations["after or equals"],
                        },
                    ],
                },
            ];
        },
        bulkActions() {
            return [
                {
                    label: this.translations["Print labels"],
                    icon: "fa-light fa-print",
                    action: "print",
                },
            ];
        },
        table() {
            return {
                data: this.tableData,
                defaultSorting: [{ key: "id", sortingMode: "desc" }],
                isLoading: this.loading,
                columns: [
                    {
                        column: "id",
                        key: "id",
                        sortable: true,
                        title: this.translations["Order ID"],
                        component: markRaw(OrderNumber),
                    },
                    {
                        column: "date_added",
                        key: "date_added",
                        sortable: true,
                        headerClasses: "text-wrap",
                        title: this.translations["Date added"],
                        component: markRaw(Date),
                    },
                    {
                        column: "date_shipping",
                        key: "date_shipping",
                        sortable: false,
                        headerClasses: "text-wrap",
                        title: this.translations["Shipping date"],
                        component: markRaw(Date),
                    },
                    {
                        column: "shipping_date_delivery",
                        key: "shipping_date_delivery",
                        sortable: false,
                        headerClasses: "text-wrap",
                        title: this.translations["Shipping date Delivery"],
                        component: markRaw(Date),
                    },
                    {
                        column: "tracking_number",
                        key: "tracking_number",
                        sortable: false,
                        title: this.translations["Tracking number"],
                        component: markRaw(TrackingNumber),
                    },
                    {
                        column: "payment_provider",
                        key: "payment_provider",
                        sortable: false,
                        headerClasses: "text-wrap",
                        title: this.translations["Payment provider"],
                    },
                    {
                        column: "price_total_input",
                        key: "price_total_input",
                        sortable: false,
                        headerClasses: "text-wrap",
                        type: "number",
                        title: this.translations["Total price"],
                        classes: "text-wrap",
                        format: (value) => {
                            return [
                                this.serverSettings("currency.sign_left"),
                                value.price_total_input,
                                this.serverSettings("currency.sign_right"),
                            ]
                                .filter((x) => Boolean)
                                .join("");
                        },
                    },
                    {
                        column: "single_print",
                        key: "single_print",
                        sortable: false,
                        title: " ",
                        singlePrint: this.handleSelectFormat,
                        component: markRaw(SinglePrint),
                    },
                ],
            };
        },
    },
    mounted() {
        this.debouncedGetData = _.debounce(this.filterByDate, 350);
        if (!this.providerData?.session) {
            this.$router.push({ name: `apps.${this.providerKey}.settings` });
        }
    },
    watch: {
        async "$route.name"() {
            this.tableData.data = [];
            this.page = 1;
            this.perPage = 25;
            // this.query = {};
            this.ids = [];
            // this.$router.push({
            //     query: {},
            // });
            await this.getData();
        },
    },
};
</script>
<style lang="scss">
.shipments-filter-by-date {
    min-width: 280px;

    @media (max-width: 400px) {
        width: 100%;
    }
}
</style>