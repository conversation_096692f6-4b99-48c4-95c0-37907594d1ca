<template>
    <b-card>
        <div class="col-8">
            <h5 class="card-title" v-html="translations['Address']"></h5>
        </div>
        <b-row>
            <b-col>
                <b-row class="mt-3" v-if="mapKey">
                    <b-col class="col-12">
                        <GMapAutocomplete
                            :select-first-on-enter="true"
                            :value="address.text"
                            :placeholder="translations['Enter city and address here']"
                            :language="locale"
                            class="form-control input-component-input"
                            @place_changed="handleAutocompleteChange"
                        />
                    </b-col>
                    <b-col class="col-12" style="position: relative">
                        <div class="geonames-loader" v-if="loading">
                            <Loading :loading="loading" />
                        </div>

                        <GMapMap
                            :center="mapCenter"
                            style="width: 100%; height: 300px"
                            class="d-flex flex-column-reverse gap-3"
                            :language="locale"
                            :options="mapOptions"
                            ref="googleMapInstance"
                        >
                            <GMapMarker
                                :position="mapCenter"
                                :clickable="clickableMarker"
                                :draggable="draggableMarker"
                                @dragend="handleMarkerDragEnd"
                                :options="markerOptions"
                            />
                        </GMapMap>
                    </b-col>
                </b-row>
                <div v-else class="info-box p-3 mt-4 mb-4 me-2 ms-2">
                    {{ translations['To see Google Map, you need to set up a Google Maps Api Key'] }}
                    <a :href="`/admin/settings/cart`" target="_blank"><i class="fas fa-external-link"></i></a>
                </div>
                <InputComponent
                    :label-text="translations['Country']"
                    :placeholder="translations['Enter country']"
                    v-model="address.country_name"
                    :error="responseErrors['country_name']"
                />
                <InputComponent
                    :label-text="translations['State']"
                    :placeholder="translations['Enter state']"
                    v-model="address.state_name"
                    :error="responseErrors['state_name']"
                />
                <InputComponent
                    :label-text="translations['City']"
                    :placeholder="translations['Enter city']"
                    v-model="address.city_name"
                    :error="responseErrors['city_name']"
                />
                <InputComponent
                    :label-text="translations['Street']"
                    :placeholder="translations['Enter street']"
                    v-model="address.street_name"
                    :error="responseErrors['street_name']"
                />
                <InputComponent
                    :label-text="translations['Street number']"
                    :placeholder="translations['Enter street number']"
                    v-model="address.street_number"
                    :error="responseErrors['street_number']"
                />
                <InputComponent
                    :label-text="translations['City postal code']"
                    :placeholder="translations['Enter city postal code']"
                    v-model="address.post_code"
                    :error="responseErrors['post_code']"
                />
                <InputComponent
                    :label-text="translations['Additional address info']"
                    :placeholder="translations['Info']"
                    v-model="address.address1"
                    :column-style="true"
                    :error="responseErrors['address1']"
                />
            </b-col>
        </b-row>
    </b-card>
</template>
<script>
import InputComponent from "@components/Form/InputComponent";
import _ from "lodash";
import axios from "axios";
import VueGoogleMaps from "@fawmi/vue-google-maps";
import Loading from "@components/Loading";

export default {
    name: "AddressSection",
    components: {
        InputComponent,
        Loading,
    },
    props: {
        modelValue: {
            type: Object,
            required: true,
            default: {},
        },
        draggableMarker: {
            required: false,
            default: true,
            type: Boolean,
        },
        clickableMarker: {
            required: false,
            default: false,
            type: Boolean,
        },
        withGeonames: {
            required: false,
            default: true,
            type: Boolean,
        },
        responseErrors: {
            type: Object,
            required: false,
            default: {},
        },
        language: {
            type: String,
            required: false,
            default: null,
        },
        googleMapsApiKey: {
            type: String,
            required: false,
            default: null,
        },
        provider: {
            type: String,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            locale: this.language || this.serverSettings('language_cp'),
            mapKey: this.googleMapsApiKey || this.serverSettings('google_maps_api_key'),
            address: this.modelValue,
            loading: false,
            translations: {
                "To see Google Map, you need to set up a Google Maps Api Key": this.$t("To see Google Map, you need to set up a Google Maps Api Key"),
                "Enter location name": this.$t("Enter location name"),
                Country: this.$t("Country"),
                "Enter country": this.$t("Enter country"),
                "Enter state": this.$t("Enter state"),
                State: this.$t("State"),
                "Enter city": this.$t("Enter city"),
                City: this.$t("City"),
                "Enter street": this.$t("Enter street"),
                Street: this.$t("Street"),
                "Enter street number": this.$t("Enter street number"),
                "Street number": this.$t("Street number"),
                "Additional address info": this.$t("Additional address info"),
                Info: this.$t("Info"),
                "City postal code": this.$t("City postal code"),
                "Enter city postal code": this.$t("Enter city postal code"),
                Address: this.$t("Address"),
                "Enter city and address here": this.$t("Enter city and address here"),
            },
            mapOptions: {
                zoom: 15,
                mapTypeId: 'roadmap', //google.maps.MapTypeId.ROADMAP
                scrollwheel: false,
                streetViewControl: false
            },
            markerOptions: {
                animation: 2, //google.maps.Animation.DROP
                draggable: this.draggableMarker,
            },
            emptyAddress: {
                address1: "",
                city: {
                    id: '',
                    ascii_name: '',
                    name: '',
                },
                city_id: '',
                city_ascii_name: '',
                geo_name_city_ascii_name: '',
                city_name: '',
                country: {
                    id: '',
                    iso2: '',
                    iso3: '',
                    name: '',
                },
                country_id: '',
                country_iso2: '',
                country_iso3: '',
                country_name: '',
                post_code: '',
                state: {
                    id: '',
                    iso2: '',
                    name: '',
                },
                state_id: '',
                state_iso2: '',
                state_name: '',
                street: {
                    id: '',
                    name: '',
                },
                street_id: '',
                street_name: '',
                street_number: '',
                geo_name_city_id: '',
                utc_offset: '',
                neighborhood: '',
            }
        };
    },
    methods: {
        geoNamesRequest(address) {
            this.loading = true;

            axios.post(`/admin/api/v1/geo-zones/format/${this.locale}/${this.provider || ''}?__json=1`, address)
                .then((res) => {
                    this.address = this.transformGeoZoneFormatToAddress(res.data)
                }).catch((err) => {
                this.address = this.transformGeoZoneFormatToAddress(address)
                this.$errorResponse(err)
            }).finally(() => {
                this.loading = false
            })
        },
        handleAutocompleteChange(place) {
            if(!place) {
                this.address = _.cloneDeep(this.emptyAddress);
                return;
            }

            if (place.geometry.viewport) {
                this.$refs.googleMapInstance.fitBounds(place.geometry.viewport);
            } else {
                this.$refs.googleMapInstance.setCenter(place.geometry.location);
                this.$refs.googleMapInstance.setZoom(17);  // Why 17? Because it looks good.
            }

            let address = this.extractComponentsFromPlace(place);

            if(this.withGeonames) {
                this.geoNamesRequest(address)
            } else {
                this.address = this.transformGeoZoneFormatToAddress(address)
            }
        },
        handleMarkerDragEnd(marker) {
            let geocoder = new google.maps.Geocoder();
            let position = new google.maps.LatLng(marker.latLng.lat(), marker.latLng.lng());
            geocoder.geocode({'latLng': position}, (results, status) => {
                if (status === google.maps.GeocoderStatus.OK) {
                    if(results && results[0]) {
                        this.$refs.googleMapInstance.panTo(position)
                        setTimeout(() => {
                            let address = this.extractComponentsFromPlace(results[0]);
                            address.send_from = 'marker'
                            if(this.withGeonames) {
                                this.geoNamesRequest(address)
                            } else {
                                this.address = this.transformGeoZoneFormatToAddress(address)
                            }
                        }, 150)
                    } else {
                        this.address = _.cloneDeep(this.emptyAddress);
                    }
                }
            });
        },
        extractComponentsFromPlace(place) {
            if(!place) {
                return [];
            }

            return {
                lat: place.geometry.location.lat(),
                lng: place.geometry.location.lng(),
                formatted_address: place.formatted_address,
                name: place.name,
                id: place.id,
                place_id: place.place_id,
                utc_offset: place.utc_offset,
                send_from: place.types[0],
                ...this.getAddressObject(place)
            }
        },
        getAddressObject(place) {
            let comp = {};
            for (var i = 0; i < place.address_components.length; i++) {
                let component = place.address_components[i];
                let componentType = component.types[0];
                comp[componentType] = component.long_name;
                comp[componentType+'_short'] = component.short_name;
            }

            return comp;
        },
        transformGeoZoneFormatToAddress(address)
        {
            return {
                latitude: address.lat,
                longitude: address.lng,
                text: address.formatted_address,
                name: address.name || address.formatted_address,
                ...this.getAddressObjectFromGeoZone(address)
            }
        },
        getAddressObjectFromGeoZone(address_components)
        {
            let isObject = (val) => {
                return Object.prototype.toString.call(val) === '[object Object]';
            }

            let ShouldBeComponent = {
                country: {
                    id: ["country_id"],
                    iso2: ["country_short"],
                    name: ["country"],
                },
                country_id: ["country_id"],
                country_iso2: ["country_short"],
                country_name: ["country"],
                state: {
                    iso2: [
                        "administrative_area_level_1_short",
                        "administrative_area_level_2_short",
                        "administrative_area_level_3_short",
                        "administrative_area_level_4_short",
                        "administrative_area_level_5_short"
                    ],
                    name: [
                        "administrative_area_level_1",
                        "administrative_area_level_2",
                        "administrative_area_level_3",
                        "administrative_area_level_4",
                        "administrative_area_level_5"
                    ],
                },
                state_iso2: [
                    "administrative_area_level_1_short",
                    "administrative_area_level_2_short",
                    "administrative_area_level_3_short",
                    "administrative_area_level_4_short",
                    "administrative_area_level_5_short"
                ],
                state_name: [
                    "administrative_area_level_1",
                    "administrative_area_level_2",
                    "administrative_area_level_3",
                    "administrative_area_level_4",
                    "administrative_area_level_5"
                ],
                city: {
                    id: ["locality_id"],
                    name: [
                        "locality",
                        "sublocality",
                        "sublocality_level_1",
                        "sublocality_level_2",
                        "sublocality_level_3",
                        "sublocality_level_4"
                    ],
                },
                city_id: ["locality_id"],
                city_name: [
                    "locality",
                    "sublocality",
                    "sublocality_level_1",
                    "sublocality_level_2",
                    "sublocality_level_3",
                    "sublocality_level_4"
                ],
                geo_name_city_id: ['geo_name_city_id'],
                geo_name_city_ascii_name: ['geo_name_city_ascii_name'],
                post_code: ["postal_code"],
                street: {
                    name: ["street_address_short", "street_address", "route", "neighborhood_short", "neighborhood"],
                },
                street_name: ["street_address_short", "street_address", "route", "neighborhood_short", "neighborhood"],
                street_number: ["street_number", "premise_short"],
                utc_offset: ['utc_offset'],
                neighborhood: ['neighborhood_short', 'neighborhood'],
            }

            let address = _.cloneDeep(this.emptyAddress);
            Object.keys(address_components).forEach((componentType) => {
                for (let shouldBe in ShouldBeComponent) {
                    if(isObject(ShouldBeComponent[shouldBe])) {
                        if(!isObject(address[shouldBe])) {
                            address[shouldBe] = {}
                        }

                        for(let shouldBeSub in ShouldBeComponent[shouldBe]) {
                            if(ShouldBeComponent[shouldBe][shouldBeSub].includes(componentType) && !address[shouldBe][shouldBeSub]) {
                                address[shouldBe][shouldBeSub] = address_components[componentType];
                            }
                        }
                    } else if(ShouldBeComponent[shouldBe].includes(componentType) && !address[shouldBe]) {
                        address[shouldBe] = address_components[componentType];
                    }
                }
            })

            return address;
        }
    },
    created() {

    },
    watch: {
        address: {
            deep: true,
            handler(value) {
                this.$emit('update:modelValue', value)
            }
        },
        modelValue: {
            deep: true,
            handler(value) {
                this.address = value;
            }
        },
        language(value) {
            this.locale = value;
        },
    },
    computed: {
        mapCenter() {
            if(!isNaN(parseFloat(this.address?.latitude)) && !isNaN(parseFloat(this.address?.longitude))) {
                return { lat: this.address.latitude, lng: this.address.longitude }
            }

            return { lat: 42.6977082, lng: 23.3218675 };
        },
    },
    emits: ['update:modelValue'],
};
</script>
<style>
.g-map-card {
    display: flex;
    flex-direction: column-reverse;
    gap: 10px;
}
.pac-container.pac-logo {
    z-index: 1060;
}
.geonames-loader {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 2;
    background: rgba(255, 255, 255, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
