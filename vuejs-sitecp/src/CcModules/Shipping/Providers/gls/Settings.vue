<template>
    <SettingsFormShippings
        provider-key="gls"
        v-model:config="config"
        v-model:isAllContentLoaded="contentLoaded"
        v-model:isCredentialsChangedCourier="isCredentialsChanged"
        v-model:credentialsError="error"
        v-model:isValid="isValidCredentials"
        :response-errors="responseErrors"
        :credentials-keys="['username', 'password', 'client_id', 'endpoint_id', 'test_mode']"
        :pickup-keys="{
            firstLine: ['sender_country', 'sender_city', 'sender_zip_code'],
            secondLine: ['sender_street', 'sender_street_number', 'sender_addition_info'],
            thirdLine: ['sender_name', 'sender_contact_name', 'sender_phone', 'sender_email'],
            settingsKeys: [
                'sender_country',
                'sender_city',
                'sender_zip_code',
                'sender_street',
                'sender_street_number',
                'sender_addition_info',
                'sender_name',
                'sender_contact_name',
                'sender_phone',
                'sender_email'
            ],
            selects: ['sender_country'],
        }"
        :app="app"
        v-model:prevent-save="preventSave"
    >
        <template #credentials>
            <CourierCredentialsSection
                v-model:credentials="config.settings"
                v-model:error="error"
                @change-credentials="
          (a) => {
            isCredentialsChanged = a;
          }
        "
            />
        </template>
        <template #senderData="slotProps">
            <SenderDataSection
                :sender="slotProps.data?.settings"
                :response-errors="responseErrors"
                :meta="config.meta"
            />
        </template>
    </SettingsFormShippings>
</template>

<script>
import SettingsFormShippings from "./../../components/SettingsFormShippings";
import CourierCredentialsSection from "./section-components/CourierCredentialsSection";
import mixin from "./../../js/mixin.js";
import SenderDataSection from "./section-components/SenderDataSection";


export default {
    mixins: [mixin],
    components: {
        SenderDataSection,
        CourierCredentialsSection,
        SettingsFormShippings,
    },
    data() {
        return {
            translations: {
                "Username is required": this.$t("Username is required"),
                "Password is required": this.$t("Password is required"),
                "Client ID is required": this.$t("Client ID is required"),
                "Select country is required": this.$t("Select country is required"),
                "The minimum value for Select country is 1": this.$t("The minimum value for Select country is 1"),
                "The maximum value for Select country is 7": this.$t("The maximum value for Select country is 7"),
                "Mode is required": this.$t("Mode is required"),
                "Country is required": this.$t("Country is required"),
                "City is required": this.$t("City is required"),
                "Post code is required": this.$t("Post code is required"),
                "Street name is required": this.$t("Street name is required"),
                "Street number is required": this.$t("Street number is required"),
                "Sender name or Company name is required": this.$t("Sender name or Company name is required"),
                "Contact name is required": this.$t("Contact name is required"),
                "Phone is required": this.$t("Phone is required"),
                "Email address is required": this.$t("Email address is required"),
            }
        };
    },

};
</script>
