<template>
    <SettingsFormShippings
        provider-key="elslogistic"
        v-model:config="config"
        v-model:isAllContentLoaded="contentLoaded"
        v-model:isCredentialsChangedCourier="isCredentialsChanged"
        v-model:credentialsError="error"
        v-model:isValid="isValidCredentials"
        :response-errors="responseErrors"
        :pickup-keys="{
            firstLine: ['sender_address', 'office_id', 'sender_city'],
            secondLine: ['sender_name'],
            thirdLine: ['sender_phone'],
            settingsKeys: [
                'sender_address',
                'office_id',
                'sender_city',
                'sender_name',
                'send_type',
                'sender_phone',
            ],
            selects: ['sender_city', 'office_id'],
        }"
        :app="app"
        v-model:prevent-save="preventSave"
    >
        <template #senderData="slotProps">
            <SenderDataSection
                :sender="slotProps.data?.settings"
                :response-errors="responseErrors"
                :meta="config.meta"
            />
        </template>
    </SettingsFormShippings>
</template>


<script>
import SettingsFormShippings from "./../../components/SettingsFormShippings";
import SenderDataSection from "./section-components/SenderDataSection";
import mixin from "./../../js/mixin.js";


export default {
    mixins: [mixin],
    components: {
        SenderDataSection,
        SettingsFormShippings,
    },
    mounted() {
        if (this.config.meta != null) {
            this.config.meta.office_id = [
                this.config.meta.office_id ? this.config.meta.office_id : "",
            ];
            this.config.meta.sender_city = [
                this.config.meta.sender_city ? this.config.meta.sender_city : "",
            ];
        } else {
            this.config.meta = {};
            this.config.meta.office_id = [];
            this.config.meta.sender_city = [];
        }
    },
    data() {
        return {
            translations: {
                "Please add a value for default weight": this.$t("Please add a value for default weight"),
                "Please select at least one service": this.$t("Please select at least one service"),
                "Name of the person in charge": this.$t("Name of the person in charge"),
                "Phone is required": this.$t("Phone is required"),
                "OR to ELS Logistic Office": this.$t("OR to ELS Logistic Office"),
                "Enter the senders name": this.$t("Enter the senders name"),
                "Select a shipping method": this.$t("Select a shipping method"),
                "Select type": this.$t("Select type"),
                "Choose an office": this.$t("Choose an office"),
                "Choose a location": this.$t("Choose a location"),
                "Enter an address": this.$t("Enter an address"),
                "Please select delivery method. For example TO ADDRESS": this.$t("Please select delivery method. For example TO ADDRESS"),
            },
        };
    },
};
</script>
