<template>
    <SettingsFormShippings
        provider-key="ntclogistics"
        v-model:config="config"
        v-model:isAllContentLoaded="contentLoaded"
        v-model:isCredentialsChangedCourier="isCredentialsChanged"
        v-model:credentialsError="error"
        v-model:isValid="isValidCredentials"
        :response-errors="responseErrors"
        :pickup-keys="{
            //street num,street name, code, city
            firstLine: ['country_code', 'address', 'post_code', 'city'],
            // firm, sender name
            secondLine: ['sender_name'],
            // phone and ect
            thirdLine: ['sender_phone'],
            settingsKeys: [
                'country_code',
                'address',
                'post_code',
                'city',
                'sender_name',
                'sender_phone',
            ],
        }"
        :credentials-keys="['username', 'password']"
        :app="app"
        v-model:prevent-save="preventSave"
    >
        <template #senderData="slotProps">
            <SenderDataSection
                :sender="slotProps.data?.settings"
                :response-errors="responseErrors"
            />
        </template>
    </SettingsFormShippings>
</template>

<script>
import SenderDataSection from "./section-components/SenderDataSection";
import SettingsFormShippings from "./../../components/SettingsFormShippings";
import mixin from "./../../js/mixin.js";


export default {
    mixins: [mixin],
    components: {
        SenderDataSection,
        SettingsFormShippings,
    },
    data() {
        return {
            translations: {
              "Weight must be a number": this.$t("Weight must be a number"),
              "Sender name is required": this.$t("Sender name is required"),
              "Sender phone is required": this.$t("Sender phone is required"),
              "Country code is required": this.$t("Country code is required"),
              "City is required": this.$t("City is required"),
              "Sender address is required": this.$t("Sender address is required"),
              "Post code is required": this.$t("Post code is required"),
            }
        };
    },
};
</script>
