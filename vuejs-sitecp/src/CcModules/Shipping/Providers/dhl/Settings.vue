<template>
  <SettingsFormShippings
    provider-key="dhl"
    v-model:config="config"
    v-model:isAllContentLoaded="contentLoaded"
    v-model:isCredentialsChangedCourier="isCredentialsChanged"
    v-model:credentialsError="error"
    v-model:isValid="isValidCredentials"
    :response-errors="responseErrors"
  >
  </SettingsFormShippings>
</template>

<script>
import SettingsFormShippings from "./../../components/SettingsFormShippings";
import mixin from "./../../js/mixin.js";


export default {
  name: "Settings",
  mixins:[mixin],
  components: {
    SettingsFormShippings,
  },
};
</script>
