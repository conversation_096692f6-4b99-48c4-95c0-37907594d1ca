<template>
    <b-row class="mt-2">
        <b-col>
            <SelectWithAjax
                v-model:val="sender.address_id"
                :label="translations['Main address']"
                @options="(val) => (meta.address_id = val)"
                :tooltip="false"
                :column-style="true"
                :colsWidth="12"
                :error="responseErrors['evropat.address_id']"
                :resolve-on-load="true"
                api-url="/admin/api/evropat/addresses"
                :required="true"
            />
        </b-col>
    </b-row>
</template>

<script>
import SelectWithAjax from "@components/Form/SelectWithAjax";

export default {
    components: {
        SelectWithAjax,
    },
    props: {
        sender: {
            type: Object,
            required: true,
        },
        responseErrors: {
            required: true,
            type: Object,
            default: {},
        },
        meta: {
            required: true,
            type: Object,
            default: {},
        },
    },
    data() {
        return {
            translations: {
                "Main address": this.$t("Main address"),
            },
        };
    },
};
</script>
