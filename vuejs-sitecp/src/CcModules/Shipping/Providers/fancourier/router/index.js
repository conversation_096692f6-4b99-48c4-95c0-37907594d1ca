let routes = [
    {
        component: () => import("./../../../components/Index"),
        props: {
            providerKey: 'fancourier',
            appKey: 'fancourier',
        },
        children: [
            {
                name: 'apps.fancourier.overview',
                path: '/admin/shipping/fancourier/',
                component: () => import("./../../../../Apps/components/AppOverview"),
            },
            {
                name: 'apps.fancourier.settings',
                path: '/admin/shipping/fancourier/settings',
                component: () => import('./../Settings'),
            },
            {
                name: 'apps.fancourier.shipments',
                path: '/admin/shipping/fancourier/shipments',
                component: () => import('./../../../components/Shipments'),
            },
            {
                name: 'apps.fancourier.shipments-return',
                path: '/admin/shipping/fancourier/shipments-return',
                component: () => import('./../../../components/Shipments'),
            },
        ]
    }
];

export {routes};
