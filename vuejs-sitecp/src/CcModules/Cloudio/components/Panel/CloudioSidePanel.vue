<template>
    <a class="btn btn-primary gradient-border" @click.prevent="isOpened = true">
        <i class="fal fa-stars"></i> {{ translations["Start a new task"] }}
    </a>

    <b-modal
        v-model="isOpened"
        :no-footer="true"
        header-class="edit-settings-modal-header"
        body-class="edit-settings-modal-content"
        class="modal-right"
        :size="(history && history.length > 0) || hasError ? 'xll' : 'xl'"
    >
        <template #header>
            <div class="d-flex align-items-start gap-2 position-relative">
                <h5
                    class="settings-modal-title"
                    v-html="
                        translations[
                            '<strong>ShopperPen</strong> - AI Product description generator'
                        ]
                    "
                ></h5>
            </div>
            <div class="d-flex justify-content-end align-items-center gap-2">
                <button
                    @click.prevent="isOpened = false"
                    type="button"
                    class="btn btn-white"
                    v-html="translations['Close']"
                ></button>
            </div>
        </template>
        <!-- <div class="cc-sidepanel-content"> -->
        <div>
            <div class="text-center" v-if="!isLoaded">
                <Loading :loading="!isLoaded" class="app-loader-center"></Loading>
            </div>
            <div class="row" v-else>
                <div
                    :class="
                        (history && history.length > 0) || hasError
                            ? 'col-lg-6'
                            : 'col-lg-12'
                    "
                >
                    <div class="panel-box panel-filter" id="panel-filter">
                        <SelectWithAjax
                            :val="panelType"
                            @update:val="
                                (value) => {
                                    $emit('update:panelType', value);
                                }
                            "
                            :options="panelTypes"
                            :column-style="true"
                            :cols-width="12"
                            :label="translations['Choose action']"
                            :placeholder="translations['Choose action']"
                            :can-clear="false"
                            :is-select-disabled="seeHistory"
                            select-classes="mb-3"
                        />

                        <SelectWithAjax
                            class="mt-3"
                            v-if="panelType === 'description'"
                            v-model:val="styleType"
                            :options="stylesType"
                            :column-style="true"
                            :cols-width="12"
                            :label="translations['Select a description style']"
                            :placeholder="translations['Select a description style']"
                            :can-clear="false"
                            :is-select-disabled="seeHistory"
                        />

                        <ProductFilters
                            class="mt-3"
                            v-model:filterProp="filter"
                            v-model:filterValueProp="filterValue"
                            :see-history="seeHistory"
                            @update:filter="(val) => (filter = val)"
                            :translations="translations"
                        />

                        <CheckboxComponent
                            v-model:value="use_description_max"
                            :label="
                                $trp(
                                    translations[
                                        'Skip products with more than {description_max} characters in the description'
                                    ],
                                    {
                                        description_max: description_max
                                            ? description_max
                                            : 'XXX',
                                    }
                                )
                            "
                            @update:value="(val) => updateField(val, 'description_max')"
                            class="mt-3"
                            :readonly="seeHistory"
                        />

                        <b-row v-if="use_description_max" class="ms-md-3 pt-2">
                            <b-col :cols="12" :md="5">
                                <InputComponent
                                    v-model="description_max"
                                    :placeholder="
                                        translations[
                                            'Characters count in the description'
                                        ]
                                    "
                                    class="mt-0"
                                    type="number"
                                    :column-style="true"
                                    :focus-on-mount="true"
                                    :disabled="seeHistory"
                                    :show-increment-decrement="false"
                                />
                            </b-col>
                        </b-row>

                        <CheckboxComponent
                            v-model:value="only_active"
                            :name="'only_active-' + panelType"
                            :id="'only_active-' + panelType"
                            :label="
                                translations['Create content for active products only']
                            "
                            :readonly="seeHistory"
                            class="mt-3"
                        />
                    </div>

                    <div
                        class="panel-box panel-vision-sense mt-3"
                        :class="{ 'no-interaction': noFilter && !seeHistory }"
                        id="panel-filter-vision-sense"
                        v-if="
                            panelType === 'description' ||
                            panelType === 'short_description'
                        "
                    >
                        <a
                            v-if="!skills.vision_sense"
                            href="javascript:;"
                            class="btn btn-white"
                            @click.prevent="installVisionSense"
                        >
                            {{ translations["Activate VisionSense AI"] }}
                        </a>
                        <template v-else>
                            <div class="mb-3">
                                <ActiveSwitch
                                    v-model:isActive="use_vision_sense"
                                    :trueValue="true"
                                    :falseValue="false"
                                    :label-text="translations['Use VisionSense AI']"
                                    :is-disabled="seeHistory"
                                    label-classes="label-500 m-0"
                                />
                            </div>
                            <CloudioInfoComponent
                                :content="
                                    translations[
                                        'Using VisionSense AI will analyze the images of each product and make your product descriptions much richer and more detailed. This will improve the user experience and increase the site\'s ranking in search results.'
                                    ]
                                "
                            />
                        </template>
                    </div>

                    <div
                        class="panel-box panel-advanced-settings mt-3"
                        :class="{ 'no-interaction': noFilter && !seeHistory }"
                        id="panel-advanced-settings"
                    >
                        <div>
                            <ActiveSwitch
                                v-model:isActive="advancedSettings"
                                :trueValue="true"
                                :falseValue="false"
                                :label-text="translations['Advanced settings']"
                                label-classes="label-500 m-0"
                                :is-disabled="seeHistory"
                            />
                        </div>
                        <Vue3SlideUpDown :duration="220" v-model="advancedSettings">
                            <RangeComponent
                                v-if="panelType === 'description'"
                                v-model:value="rangeValue"
                                @update:value="updateTokensWithRange(Number(rangeValue))"
                                :translations="translations"
                                :disabled="seeHistory"
                            />
                            <hr v-if="panelType === 'description'" />

                            <div class="panel-section">
                                <p class="panel-section-title" :class="panelType !== 'description' ? 'mt-3' : ''">
                                    {{
                                        translations[
                                            "Select which product details to include in the description info-circle"
                                            ]
                                    }}
                                </p>

                                <div v-for="(value, key) in product_details_checkbox">
                                    <CheckboxComponent
                                        v-model:value="value.ready"
                                        @update:value="
                                            updateTokensWithCheckbox(
                                                value.ready,
                                                key === 'description'
                                                    ? checkboxPriceDescription
                                                    : checkboxPrice
                                            )
                                        "
                                        :label="value.text"
                                        :name="key + '-' + panelType"
                                        :id="key + '-' + panelType"
                                        :disabled="in_process || value.disabled"
                                        :readonly="
                                            key === 'category' ||
                                            key === 'name' ||
                                            seeHistory
                                        "
                                        :tooltip-text="
                                            value.disabled
                                                ? translations[
                                                      'The description is longer than 3000 characters. You can\'t use it.'
                                                  ]
                                                : ''
                                        "
                                        :help-text="value.description"
                                    />
                                </div>

                                <div v-if="panelType === 'meta_description'" class="d-flex flex-column gap-3 mb-3">
                                    <CheckboxComponent
                                        v-model:value="use_emojis"
                                        @update:value="
                                            updateTokensWithCheckbox(
                                                use_emojis,
                                                checkboxPrice
                                            )
                                        "
                                        :name="'use_emojis-' + panelType"
                                        :id="'use_emojis-' + panelType"
                                        :label="translations['Use emojis']"
                                        :readonly="seeHistory"
                                    />
                                    <CheckboxComponent
                                        v-model:value="use_phone"
                                        @update:value="
                                            updateTokensWithCheckbox(
                                                use_phone,
                                                checkboxPrice
                                            )
                                        "
                                        :name="'use_phone-' + panelType"
                                        :id="'use_phone-' + panelType"
                                        :label="translations['Phone']"
                                        :readonly="seeHistory"
                                    />
                                    <div
                                        v-if="use_phone"
                                        class="row ms-md-3 pt-2"
                                    >
                                        <div class="col-12 col-md-6">
                                            <InputComponent
                                                v-model="phone"
                                                :placeholder="translations['Phone']"
                                                :disabled="seeHistory"
                                                :column-style="true"
                                                :no-margin="true"
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div
                                    v-if="
                                        panelType === 'short_description' ||
                                        panelType === 'meta_description'
                                    "
                                >
                                    <CheckboxComponent
                                        v-model:value="use_delivery_price"
                                        @update:value="
                                            updateTokensWithCheckbox(
                                                use_delivery_price,
                                                checkboxPrice
                                            )
                                        "
                                        :name="'use_delivery_price-' + panelType"
                                        :id="'use_delivery_price-' + panelType"
                                        :label="translations['Free delivery']"
                                        :readonly="seeHistory"
                                        :help-text="
                                            translations[
                                                'You can enter its value here to include this critical detail in the description if you offer free shipping.'
                                            ]
                                        "
                                    />

                                    <div
                                        class="ms-md-3 pt-2"
                                        v-if="use_delivery_price"
                                    >
                                        <div class="col-12 col-md-6">
                                            <InputComponent
                                                v-model="delivery_price"
                                                :placeholder="
                                                    translations['Free delivery']
                                                "
                                                class="mt-0"
                                                :unit_position="
                                                    ccsettings.sign_left
                                                        ? 'left'
                                                        : 'right'
                                                "
                                                :unit="
                                                    ccsettings.sign_left
                                                        ? ccsettings.sign_left
                                                        : ccsettings.sign_right
                                                "
                                                type="number"
                                                :columnStyle="true"
                                                :disabled="seeHistory"
                                                :show-increment-decrement="false"
                                            />
                                        </div>
                                    </div>
                                    <CheckboxComponent
                                        v-model:value="use_other_facts"
                                        @update:value="
                                            updateTokensWithCheckbox(
                                                use_other_facts,
                                                checkboxPrice
                                            )
                                        "
                                        :name="'use_other_facts-' + panelType"
                                        :id="'use_other_facts-' + panelType"
                                        :label="
                                            translations['Enable additional information']
                                        "
                                        :readonly="seeHistory"
                                        :help-text="
                                            translations[
                                                'Here, you can add information such as warranty service, right of return and exchange, and other general information.'
                                            ]
                                        "
                                    />

                                    <div
                                        v-if="use_other_facts"
                                        class="row ms-md-3 pt-2"
                                    >
                                        <div class="col-12 col-md-6">
                                            <InputComponent
                                                v-model="other_facts"
                                                :placeholder="
                                                    translations[
                                                        'Enable additional information'
                                                    ]
                                                "
                                                :column-style="true"
                                                :no-margin="true"
                                                :disabled="seeHistory"
                                                class="mt-0"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Vue3SlideUpDown>
                    </div>

                    <div
                        class="panel-box panel-rank-master mt-3"
                        :class="{ 'no-interaction': noFilter && !seeHistory }"
                        id="panel-rank-master"
                    >
                        <RankMaster
                            v-model:activateRankMaster="activateRankMaster"
                            v-model:rankMaster="skills.rank_master"
                            v-model:keyword="keyword"
                            v-model:remaining="remaining"
                            v-model:minimum_tokens="minimum_tokens"
                            v-model:view-more-products="viewMoreProducts"
                            v-model:link-category="linkCategory"
                            v-model:enable-eeat="enableEeat"
                            v-model:main-category-for-keyword="main_category_for_keyword"
                            :see-history="seeHistory"
                            :buy-url="buy_url"
                            :translations="translations"
                            :panel-type="panelType"
                            :in-process="in_process"
                            @update:activateRankMaster="updateTokensWithRankMaster"
                        />
                        <!-- @update:remaining="(val) => (this.remaining = val)" -->
                    </div>

                    <div
                        class="panel-box panel-tokens mt-3"
                        :class="{ 'no-interaction': noFilter && !seeHistory }"
                        id="panel-tokens"
                    >
                        <Tokens
                            v-model:tokens="tokens"
                            v-model:available_generate="available_generate"
                            v-model:remaining="remaining"
                            v-model:minimum_tokens="minimum_tokens"
                            v-model:buyTokens="buyTokens"
                            v-model:inProcess="in_process"
                            v-model:totalProducts="products_count"
                            v-model:totalTokens="totalTokens"
                            v-model:loader="loader"
                            v-model:publish="publish"
                            v-model:useCreateTab="use_create_tab"
                            v-model:createTab="create_tab"
                            v-model:filter="filter"
                            v-model:filterValue="filterValue"
                            :see-history="seeHistory"
                            :panel-type="panelType"
                            :buy-url="buy_url"
                            :func="($event) => generateDescription($event)"
                        />
                    </div>

                    <p
                        class="terms-text mt-3"
                        v-html="
                            translations[
                                `By continuing, you agree to <a href='https://openai.com/policies' target='_blank'>Open AI’s terms.</a> Verify the accuracy of generated results before publishing. CloudCart is not responsible for results.`
                            ]
                        "
                    ></p>

                    <hr />
                    <router-link :to="'/admin/apps/cloudio'">
                        <b-row class="part-of-cloudio">
                            <figure class="part-of-cloudio-image pe-0">
                                <img
                                    :src="
                                        this.serverSettings('img_url') +
                                        '/sitecp/img/all-icons/part_of_cloudio.png?' +
                                        this.serverSettings('last_build')
                                    "
                                    alt="logo"
                                />
                            </figure>
                            <b-col cols="10" class="part-of-cloudio-text ps-0">
                                <h4 v-html="translations['Part of CloudIO App']"></h4>
                                <p
                                    v-html="
                                        translations[
                                            `Now you can do more with the new CloudIO AI powered app. Check out all its amazing skills and features <a href='/apps/cloudio' target='_blank'>here</a>.`
                                        ]
                                    "
                                ></p>
                            </b-col>
                        </b-row>
                    </router-link>
                </div>
            </div>
        </div>
    </b-modal>
</template>

<script>
import { Vue3SlideUpDown } from "vue3-slide-up-down";

import SelectWithAjax from "@components/Form/SelectWithAjax";
import ActiveSwitch from "@components/Form/ActiveSwitch";
import RangeComponent from "./Form/RangeComponent";
import CheckboxComponent from "@components/Form/CheckboxComponent";
import InputComponent from "@components/Form/InputComponent";
import CloudioInfoComponent from "./Form/CloudioInfoComponent";

import Error from "./Helpers/Error";
import RankMaster from "./Boxes/RankMaster";
import Tokens from "./Boxes/Tokens";
import Result from "./Boxes/Result";
import Loading from "@components/Loading";
import ProductFilters from "./Form/ProductFilters";

import mixin from "./js/mixin";

export default {
    name: "SidebarCloudio",
    props: {
        panelType: {
            type: String,
            default: "description",
            required: false,
        },
        logId: {
            type: String,
            required: false,
            default: null,
        },
    },
    components: {
        RangeComponent,
        ActiveSwitch,
        SelectWithAjax,
        RankMaster,
        CheckboxComponent,
        InputComponent,
        Tokens,
        Error,
        Result,
        Loading,
        ProductFilters,
        CloudioInfoComponent,
        Vue3SlideUpDown,
    },
    mixins: [mixin]
};
</script>

<style lang="scss">
@use "./../../scss/panel/_main.scss" as *;

/* list */
.list-enter-active,
.list-leave-active {
    transition: all 0.5s ease;
}

.list-enter-from,
.list-leave-to {
    opacity: 0;
    transform: translateX(30px);
}

/* slide-fade */
.slide-fade-enter-active {
    transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
    transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
}

/* fade */
.fade-enter-active {
    transition: all 0.4s ease-in-out;
}

.fade-leave-active {
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

</style>
