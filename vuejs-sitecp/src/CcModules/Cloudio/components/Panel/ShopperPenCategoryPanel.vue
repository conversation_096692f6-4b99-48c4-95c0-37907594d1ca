<template>
    <div v-if="categoryDescription" class="cloudio-bottom-bar">
        <div class="text">
            <p class="label-500 m-0">
                {{ translations["Write with ShopperPen Category by"] }}

                <a
                    href="/admin/apps/cloudio/shopper_pen_category"
                    target="_blank"
                >
                    {{ translations["CloudIO app"] }}
                </a>
            </p>
            <span class="text-400-secondary">
                {{
                    translations[
                        "ShopperPen Category is your AI assistant for quickly and efficiently creating product category descriptions"
                        ]
                }}
            </span>
        </div>
        <a
            class="btn btn-primary gradient-border d-flex align-items-center gap-1 flex-norap text-nowrap"
            @click.prevent="openModal"
        >
            <svg
                fill="none"
                height="18"
                viewBox="0 0 22 18"
                width="22"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M0 4C0 1.79086 1.79086 0 4 0H18C20.2091 0 22 1.79086 22 4V14C22 16.2091 20.2091 18 18 18H4C1.79086 18 0 16.2091 0 14V4Z"
                    fill="white"
                />
                <path
                    d="M9.4707 5.76367L7.03906 13H5.33398L8.52148 4.46875H9.61133L9.4707 5.76367ZM11.5039 13L9.06055 5.76367L8.91406 4.46875H10.0098L13.2148 13H11.5039ZM11.3926 9.83008V11.1016H6.81641V9.83008H11.3926ZM15.8105 4.46875V13H14.1992V4.46875H15.8105Z"
                    fill="#8D58E0"
                />
            </svg>
            {{ translations["Generate"] }}
        </a>
    </div>

    <a v-else ref="triggerPanel" class="btn btn-primary gradient-border" @click.prevent="modal = !modal">
        <i class="fal fa-stars"></i> {{ translations['Start a new task'] }}
    </a>

    <b-modal
        id="shopperPenCat-modal"
        v-model="modal"
        :size="fullWidthPanel || (!!progress && !progress.is_complete) ? 'xll' : 'xl'"
        body-class="edit-settings-modal-content"
        class="modal-right"
        header-class="edit-settings-modal-header sticky"
        :no-footer="true"

    >
        <template #header>
            <div class="d-flex align-items-start gap-2 position-relative">
                <h5 class="settings-modal-title" v-html="title"></h5>
            </div>
            <div class="d-flex justify-content-end align-items-center gap-2">
                <button
                    class="btn btn-white"
                    @click="modal = false"
                    v-html="translations['Close']"
                ></button>
            </div>
        </template>
        <div class="shopperPenCategoryTop"></div>
        <Loading v-if="loading" :loading="loading" class="app-loader-center"></Loading>
        <b-row v-else>
            <b-col :class="!!progress && !progress.is_complete ? 'col-6' : 'col-12'">
                <ul v-if="categoryId && !!progress && !progress.is_complete" class="tabs-large mb-3">
                    <li>
                        <a class="tabs-large-item router-link-active router-link-exact-active"
                           href="javascript:void(0);">
                            {{ translations['Prompt'] }}
                        </a>
                    </li>
                </ul>

                <ShopperPenCategoryMainSettings
                    :category-id="categoryId"
                    :disabled="disabled"
                    :settings="settings"
                />

                <Vue3SlideUpDown
                    :duration="160"
                    :model-value="!!settings.category && !!settings.language"
                    class="pb-3 d-flex flex-column gap-3"
                >
                    <RankMaster
                        v-model="settings.rank_master"
                        v-model:rankMasterInstalled="skills.rank_master"
                        v-model:remaining="remaining"
                        :disabled="disabled"
                    />
                    <ShopperPenCategoryForm :disabled="disabled" :settings="settings"/>
                    <b-col
                        id="panel-tokens"
                        class="col-12 mt-3"
                    >
                        <b-card>
                            <TokensCategory
                                v-model:loader="loader"
                                v-model:remaining="remaining"
                                v-model:tokens="tokensUsed"
                                v-model:width="fullWidthPanel"
                                :price="price"
                                :disabled="disabled"
                                :func="generateDescription"
                            />
                        </b-card>
                        <p class="terms-text mt-3 mb-0 px-1"
                           v-html="translations[`By continuing, you agree to <a href='https://openai.com/policies' target='_blank'>Open AI’s terms.</a> Verify the accuracy of generated results before publishing. CloudCart is not responsible for results.`]"
                        ></p>
                    </b-col>
                </Vue3SlideUpDown>
                <Vue3SlideUpDown :duration="160" :model-value="!settings.category || !settings.language">
                    <CloudioInfoComponent
                        :content="translations['Select the language and the category to start your configuration of the ShopperPen Category task.']"
                    />
                </Vue3SlideUpDown>
            </b-col>
            <b-col v-if="progress && !progress.is_complete" :class="!!progress ? 'col-6' : 'col-12'">
                <ul class="tabs-large mb-3">
                    <li>
                        <a class="tabs-large-item router-link-active router-link-exact-active"
                           href="javascript:void(0);">
                            {{ translations['Progress'] }}
                        </a>
                    </li>
                </ul>
                <div
                    v-if="!this.previewText"
                    class="text-center description-loading-box"
                >
                    <AiLoader/>
                    <LoadingText
                        :error="null"
                        :loadingText="loading_text"
                    />
                    <div
                        v-if="!progress?.is_complete"
                        class="d-flex align-items-center justify-content-center mt-4"
                    >
                        <button
                            :disabled="cancelLoader"
                            class="btn btn-white d-flex align-items-center gap-1"
                            @click="cancelTask"
                        >
                            <b-spinner v-if="cancelLoader" small/>
                            {{ translations["Cancel task"] }}
                        </button>
                    </div>
                </div>
                <template v-else>
                    <h3 class="label-500-18px mb-4">{{ translations['GENERATED TEXT:'] }}</h3>
                    <div v-html="previewText"></div>
                </template>
            </b-col>
        </b-row>
    </b-modal>
</template>
<script>
import axios from 'axios';
import {toast} from '@js/toast';

import {Vue3SlideUpDown} from "vue3-slide-up-down";
import Loading from '@components/Loading'

import CloudioInfoComponent from './Form/CloudioInfoComponent.vue'
import RankMaster from "./Boxes/RankMasterSimple";
import TokensCategory from "./Boxes/TokensCategory";
import ShopperPenCategoryMainSettings from "./Boxes/ShopperPenCategoryMainSettings";
import ShopperPenCategoryForm from "./Boxes/ShopperPenCategoryForm.vue";
import LoadingText from "../../../Base/Products/CloudioProduct/components/Helpers/LoadingText.vue";
import AiLoader from "@components/AiLoader";

export default {
    name: "ShopperPenCategoryPanel",
    components: {
        LoadingText,
        Vue3SlideUpDown,
        RankMaster,
        Loading,
        TokensCategory,
        ShopperPenCategoryMainSettings,
        ShopperPenCategoryForm,
        CloudioInfoComponent,
        AiLoader
    },
    props: {
        categoryId: {
            type: [String, Number],
            required: false,
            default: null
        },
        categoryDescription: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            modal: false,
            loading: false,
            loader: false,
            hasError: false,
            categories: [],
            skills: {},
            buy_url: '',
            remaining: 0,
            available_generate: true,
            buyTokens: false,
            fullWidthPanel: false,
            settings: {},
            progress: null,
            loading_text: 1,
            cancelLoader: false,
            progressInterval: null,
            previewText: '',
            price: 0.32,

            translations: {
                'Close': this.$t('Close'),
                '<strong>ShopperPen Category</strong> - AI Category description generator': this.$t('<strong>ShopperPen Category</strong> - AI Category description generator'),
                "Start a new task": this.$t('Start a new task'),
                "Select category": this.$t('Select category'),
                "By continuing, you agree to <a href='https://openai.com/policies' target='_blank'>Open AI’s terms.</a> Verify the accuracy of generated results before publishing. CloudCart is not responsible for results.":
                    this.$t('By continuing, you agree to <a href=\'https://openai.com/policies\' target=\'_blank\'>Open AI’s terms.</a> Verify the accuracy of generated results before publishing. CloudCart is not responsible for results.'),
                "Part of CloudIO App": this.$t('Part of CloudIO App'),
                'Generated successfully': this.$t('Generated successfully'),
                "Select the language and the category to start your configuration of the ShopperPen Category task.": this.$t('Select the language and the category to start your configuration of the ShopperPen Category task.'),
                "Now you can do more with the new CloudIO AI powered app. Check out all its amazing skills and features <a href='#' target='_blank'>here</a>.":
                    this.$t('Now you can do more with the new CloudIO AI powered app. Check out all its amazing skills and features <a href=\'#\' target=\'_blank\'>here</a>.'),
                "Write with ShopperPen Category by": this.$t('Write with ShopperPen Category by'),
                "ShopperPen Category is your AI assistant for quickly and efficiently creating product category descriptions":
                    this.$t('ShopperPen Category is your AI assistant for quickly and efficiently creating product category descriptions'),
                "CloudIO app": this.$t('CloudIO app'),
                "Generate": this.$t('Generate'),
                "Generate text": this.$t('Generate text'),
                "Progress": this.$t('Progress'),
                "Prompt": this.$t('Prompt'),
                "Cancel task": this.$t('Cancel task'),
                "GENERATED TEXT:": this.$t('GENERATED TEXT:'),
                "Cancelled successfully": this.$t('Cancelled successfully'),
            }
        }
    },
    computed: {
        title() {
            return this.translations['<strong>ShopperPen Category</strong> - AI Category description generator']
        },
        disabled() {
            return !!this.progress && !this.progress.text
        },
        tokensUsed() {
            let total = 0;

            if (this.settings?.rank_master?.activate) {
                if (this.settings?.rank_master?.keywords) {
                    total += this.priceEnum.rank_master.keywords
                }
                total += this.settings.rank_master.wikipedia ? this.priceEnum.rank_master.wikipedia : 0
                total += this.settings.rank_master.properties_keyword_links ? this.priceEnum.rank_master.properties_keyword_links : 0
                total += this.settings.rank_master.best_buy_vendors_links ? this.priceEnum.rank_master.best_buy_vendors_links : 0
            }
            if (this.settings?.intro?.enable) {
                total += this.priceEnum.intro.length
            }
            if (this.settings?.interesting_facts?.enable) {
                total += this.priceEnum.interesting_facts.enable
            }
            if (this.settings?.best_buy_products?.enable) {
                total += this.priceEnum.best_buy_products.length
            }
            if (this.settings?.best_buy_vendors?.enable) {
                total += this.priceEnum.best_buy_vendors.length
            }
            if (this.settings?.related_categories?.enable) {
                total += this.settings.related_categories.main ? this.priceEnum.related_categories.main : 0
                total += this.settings.related_categories.other ? this.priceEnum.related_categories.other : 0
            }
            if (this.settings?.properties?.enable) {
                total += this.priceEnum.properties.length
            }
            if (this.settings?.description?.length) {
                total += this.priceEnum.description.length
                total += this.settings.description.link ? this.priceEnum.description.link : 0
            }

            return total
        },
        priceEnum() {
            return {
                "rank_master": {
                    "keywords": 4500,
                    "wikipedia": 3500,
                    "properties_keyword_links": 4500,
                    "best_buy_vendors_links": 4500
                },
                "intro": {
                    "length": 3500,
                },
                "interesting_facts": {
                    "enable": 2500
                },
                "best_buy_products": {
                    "length": 8500
                },
                "best_buy_vendors": {
                    "length": 4000,
                },
                "properties": {
                    "length": 6500,
                },
                "related_categories": {
                    "main": 8500,
                    "other": 8500
                },
                "description": {
                    "length": 12000,
                    "link": 4500
                }
            }
        }
    },
    methods: {
        openModal() {
            if (this.categoryId && this.categoryDescription) {
                this.modal = true;
            } else if (!this.categoryId && this.categoryDescription) {
                this.$emit('save-category', true);
            }
        },
        async cancelTask() {
            this.cancelLoader = true
            try {
                const response = await axios.post('/admin/api/cloudio/shopper-pen-category/cancel', {
                    log_id: this.progress.log_id,
                })
                toast.success(this.translations['Cancelled successfully'])

                this.progress = null;
                this.previewText = '';
                clearInterval(this.progressInterval)
                this.progressInterval = null;

            } catch (error) {
                this.$errorResponse(error)
            } finally {
                this.cancelLoader = false
            }
        },
        async getData() {
            this.loading = true
            try {
                const res = await axios.get('/admin/api/cloudio/shopper-pen-category/create/');

                this.skills = res.data.skills;
                this.buy_url = res.data.buy_url;
                this.remaining = res.data.remaining;
                this.settings = res.data.settings;
                this.price = res.data.one_thousand_price;

                if (this.categoryId && this.categoryDescription) {
                    this.settings.category = this.categoryId;
                    this.settings.publish = true
                }

                if (this.categoryId) {
                    await this.getProgress();
                }

            } catch (error) {
                this.$errorResponse(error)
            } finally {
                this.loading = false
            }
        },
        async getProgress() {
            try {
                const res = await axios.get(`/admin/api/cloudio/shopper-pen-category/history/${this.categoryId}`);

                if (res.data.is_complete && res.data.text) {
                    clearInterval(this.progressInterval);
                    this.previewText = res.data.text;
                    this.$emit('update-description', this.previewText);
                    return
                }

                this.progress = res.data;

                if (!this.progressInterval) {
                    this.progressInterval = setInterval(() => {
                        this.getProgress();
                    }, 20000);
                } else if (this.progress.is_complete) {
                    clearInterval(this.progressInterval);
                    this.progressInterval = null;
                }
            } catch (err) {
                console.log(err)
            }
        },
        async generateDescription() {
            this.hasError = false

            this.loader = true
            let payload = _.cloneDeep(this.settings)
            if (payload.rank_master.keywords) {
                payload.rank_master.secondary_keywords = (payload?.rank_master?.secondary_keywords || []).join(',')
            }
            try {
                const res = await axios.post(`/admin/api/cloudio/shopper-pen-category/create`, payload)

                this.$emit('update:table')
                await new Promise(resolve => setTimeout(resolve, 1000));

                if (!this.categoryId && !this.categoryDescription) {
                    toast.success(this.translations['Generated successfully'])
                    this.modal = false;
                } else {
                    this.progress = null
                    this.previewText = ''

                    await this.getProgress();
                    setTimeout(() => {
                        let topContent = document.querySelector('.shopperPenCategoryTop');

                        if (topContent) {
                            topContent.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start',
                                inline: 'nearest'
                            });
                        }
                    }, 300)
                }
            } catch (err) {
                this.hasError = true
                this.$errorResponse(err)
            } finally {
                this.loader = false
            }
        }
    },
    watch: {
        modal: async function (value) {
            this.$emit('update:modelValue', value)
            if (!value) {
                this.settings = {}
                this.loading = false
                this.loader = false
                this.hasError = false
                this.remaining = 0
                this.available_generate = true
                this.buyTokens = false
                this.skills = {}
                this.categories = []
                this.loading = true
                clearInterval(this.progressInterval);
                this.progressInterval = null;
                this.progress = null
                this.previewText = ''

            } else {
                await this.getData()
            }
        },
        modelValue(value) {
            if (this.categoryDescription) {
                this.modal = value
            }
        }
    },
    emits: ['update:table', 'save-category', 'update:modelValue', 'update-description'],
}

</script>
<style lang="scss">
#shopperPenCat-modal {
    z-index: 1060 !important;
}

.cloudio-bottom-bar {
    position: relative;
    z-index: 5;
    display: flex;
    justify-content: space-between;
    flex-wrap: nowrap;
    gap: 64px;
    align-items: center;
    width: calc(100% + 1px);
    padding: 12px 16px;
    border-radius: 0px 0px 8px 8px;
    background: var(--color-purple-100, #f7f2ff);

    @media (max-width: 767px) {
        gap: 32px;
    }

    @media (max-width: 575px) {
        gap: 12px;
        flex-wrap: wrap;
        a.btn {
            align-self: self-end;
            margin-left: auto;
            width: 100%;
            justify-content: center;
        }
    }

    .text {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }
}
</style>
