<template>
    <div v-if="data.draft">
        {{ translations.Draft }}
    </div>
    <Toggle v-else
            @input="toggleSwitch"
            v-model="data.active"
            :trueValue="1"
            :falseValue="0"
            :disabled="disabled"
    />
</template>

<script>
import Toggle from "@vueform/toggle";
import axios from "axios";
import {toast} from "@js/toast";

export default {
    name: "Switch",
    components: {
        Toggle
    },
    props: {
        data: {
            type: Object,
            required: true,
        },
        column: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            disabled: false,
            translations: {
                Draft: this.$t('Draft'),
                'You can have maximum {max} active tasks.': this.$t('You can have maximum {max} active tasks.'),
            }
        }
    },
    methods: {
        toggleSwitch(val) {
            this.disabled = true;
            let status = "";
            let oldActive = this.data.active
            val === 1 ? status = 'yes' : status = 'no'
            axios
                .get(`/admin/api/xml_import/active/${this.data.id}/${status}`)
                .then(res => {
                    toast.success(res.data.msg)
                })
                .catch(err => {
                    this.data.active = oldActive;
                    this.$errorResponse(err);
                })
                .finally(() => {
                    this.disabled = false;
                })
        },
        openFeaturePanel(data) {
            this.column.openFeaturePanel(data);
        }
    }
}
</script>

<style scoped>

</style>