<template>
    <div>
        <b-row v-show="isLoaded" class="py-lg-0 py-3">
            <b-col md="6" lg="8">

            </b-col>
            <b-col v-show="isLoaded" md="6" lg="4" class="text-right">
                <div class="step-controls">
                    <router-link :to="{name: 'apps.xml_import.settings'}" class="btn btn-ghost me-2" >
                        {{ translations["Cancel"] }}
                    </router-link>
                    <b-button variant="primary" @click.prevent="submitData" :disabled="submitLoader || disabledAditionalSettings">
                        <b-spinner small v-if="submitLoader"></b-spinner>
                        {{ translations["Save and go to step 2"] }}
                    </b-button>
                </div>
            </b-col>
        </b-row>

        <Loading
            v-if="!isLoaded"
            class="app-loader-center"
            :loading="!isLoaded"
        ></Loading>

        <div v-else>
            <b-row>
                <SettingDescription
                    v-model="boxes.CreateNew" box-key="xml_import"
                />

                <b-col md="7">
                    <b-card>
                        <b-row>
                            <b-col cols="12">
                                <InputComponent
                                    v-model="xml_import.name"
                                    :tooltip="false"
                                    :label-text="translations['Job name']"
                                    :placeholder="translations['Job name']"
                                    :column-style="false"
                                    :error="responseErrors['name'] || responseErrors['xml_import.name']"
                                />
                            </b-col>
                            <b-col cols="12">
                                <InputComponent
                                    v-model="xml_import.url"
                                    :tooltip="false"
                                    :label-text="translations['XML url']"
                                    :placeholder="translations['URL']"
                                    :column-style="false"
                                    :error="responseErrors['url'] || responseErrors['xml_import.url']"
                                />
                            </b-col>
                        </b-row>
                        <b-row>
                            <b-col cols="12">
                                <InputComponent
                                    v-model="xml_import.product_tag"
                                    :tooltip="false"
                                    :label-text="translations['XML Product tag']"
                                    :placeholder="translations['Entry XML tag for product']"
                                    :column-style="false"
                                    :error="responseErrors['product_tag'] || responseErrors['xml_import.product_tag']"
                                    class="product-tag-input"
                                ></InputComponent>
                            </b-col>
                            <b-col cols="12">
                                <InputComponent
                                    v-model="lines"
                                    :tooltip="false"
                                    :label-text="translations['Rows']"
                                    :placeholder="translations['Rows']"
                                    :column-style="false"
                                    :error="responseErrors['lines'] || responseErrors['xml_import.lines']"
                                    :min="20"
                                    :max="1500"
                                    type="number"
                                />
                            </b-col>
                        </b-row>

                        <Vue3SlideUpDown v-model="disabledAditionalSettings" :duration="duration">
                            <b-row class="justify-content-center mt-3">
                                <b-col class="col-12 d-flex justify-content-end">
                                    <button
                                        @click.prevent="testTag"
                                        :disabled="submitLoaderTag"
                                        class="btn btn-primary"
                                    >
                                        <b-spinner small v-if="submitLoaderTag"></b-spinner>
                                        {{ translations['Validate XML and continue'] }}
                                    </button>
                                </b-col>
                            </b-row>
                        </Vue3SlideUpDown>
                    </b-card>
                </b-col>
            </b-row>

            <Vue3SlideUpDown v-model="allowAditionalSettings" :duration="duration">
                <hr>

                <b-row>
                    <SettingDescription
                        v-model="boxes.Action" box-key="xml_import"
                    />

                    <b-col md="7">
                        <b-card>
                            <b-row>
                                <b-col cols="12">
                                    <SelectWithAjax
                                        v-model:val="meta.action"
                                        :label="translations['Action']"
                                        :placeholder="translations['Select']"
                                        :options="action_options"
                                        :column-style="false"
                                        :cols-width="6"
                                        :can-clear="false"
                                    />
                                </b-col>
                                <b-col cols="12">
                                    <SelectWithAjax
                                        v-model:val="meta.compare_by"
                                        :tooltip="false"
                                        :label="translations['Compare by']"
                                        :options="compare_options"
                                        :column-style="false"
                                        :cols-width="6"
                                        :can-clear="false"
                                    />
                                </b-col>
                                <b-col cols="12">
                                    <ActiveSwitch
                                        v-model:isActive="xml_import.product_draft"
                                        :label-text="translations['Publish imported products']"
                                        true-value="no"
                                        false-value="yes"
                                        class="mt-2"
                                    />
                                </b-col>
                            </b-row>
                        </b-card>
                    </b-col>
                </b-row>

                <hr>

                <b-row>
                    <SettingDescription
                        v-model="boxes.Other" box-key="xml_import"
                    />

                    <b-col md="7">
                        <b-card>
                            <b-row v-if="isSuppliersInstalled">
                                <b-col cols="12">
                                    <SelectWithAjax
                                        v-model:val="meta.supplier_id"
                                        :options="supplier_options"
                                        :label="translations['Supplier']"
                                        :column-style="false"
                                    />
                                </b-col>
                            </b-row>

                            <b-row>
                                <b-col cols="12">
                                    <SelectWithAjax
                                        v-model:val="xml_import.parent_id"
                                        :options="parent_options"
                                        :label="translations['Assign to']"
                                        :tooltip="false"
                                        :column-style="false"
                                        :error="responseErrors['xml_import.parent_id']"
                                        :placeholder="translations['This is a main XML job']"
                                    />
                                </b-col>

                                <b-col cols="12">
                                    <SelectWithAjax
                                        v-model:val="this.xml_import.task_key"
                                        :label="translations['Task ID number']"
                                        :options="computedTaskKey"
                                        :tooltip="false"
                                        :placeholder="translations['Example: 123123']"
                                        :column-style="false"
                                        :searchable="true"
                                        :createOption="true"
                                        :error="responseErrors['xml_import.task_key']"
                                        :api-url="'/admin/api/xml_import/task_key'"
                                    />
                                </b-col>

                                <b-col cols="12">
                                    <SelectWithAjax
                                        v-model:val="xml_import.discount_id"
                                        :options="discounts_options"
                                        :label="translations['Choose Fixed Discount type']"
                                        :tooltip="false"
                                        :column-style="false"
                                    />
                                </b-col>

                                <b-col cols="12">
                                    <ActiveSwitch
                                        class="mt-3 mb-3"
                                        v-model:isActive="xml_import.track_inventory"
                                        :label-text="translations['Track quantity of imported products']"
                                        :tooltip="false"
                                        :column-style="false"
                                    />
                                </b-col>

                                <b-col cols="12">
                                    <ActiveSwitch
                                        class="mt-3 mb-3"
                                        v-model:isActive="xml_import.continue_selling"
                                        :label-text="translations['Continue selling']"
                                        :tooltip="false"
                                        :column-style="false"
                                    />
                                </b-col>

                                <b-col cols="12">
                                    <ActiveSwitch
                                        class="mt-3 mb-3"
                                        v-model:isActive="meta.disable_missings"
                                        :label-text="translations['Disable missing products']"
                                        :tooltip="false"
                                        :column-style="false"
                                    />
                                </b-col>
                            </b-row>
                        </b-card>
                    </b-col>
                </b-row>
            </Vue3SlideUpDown>
        </div>
    </div>
</template>

<script>
import {defineComponent} from "vue";
import Loading from "@components/Loading.vue";
import SelectWithAjax from "@components/Form/SelectWithAjax";
import ActiveSwitch from "@components/Form/ActiveSwitch";
import InputComponent from "@components/Form/InputComponent";
import SettingDescription from "@components/SettingDescription";
import axios from "axios";

import {toast} from "@js/toast";
import {buildFormData} from "@js/shippingHelpers.js";
import { Vue3SlideUpDown } from "vue3-slide-up-down";

export default defineComponent({
    name: "AddOrEdit",
    components: {
        InputComponent,
        ActiveSwitch,
        Loading,
        SelectWithAjax,
        SettingDescription,
        Vue3SlideUpDown,
    },
    props: {
        id: {
            required: false,
        },
        modelValue: {},
        appKey: {},
        tBoxes: {},
        currentStep: {}
    },
    created() {
        this.helpBoxes(this.boxes);
    },
    data() {
        return {
            app: this.modelValue,
            isLoaded: false,
            submitLoader: false,
            submitLoaderTag: false,
            product_tag: null,
            isTagExist: false,
            isTagOrProductTagChanged: false,
            duration: 200,
            url: null,
            xml_import: {
                // product_draft: 'no',
                // track_inventory: 1,
                // continue_selling: 0,
                // lines: 100,
                // parent_id: "",
            },
            meta: {
                supplier_id: "",
                compare_by: "id",
                action: "import",
                disable_missings: 0,
            },
            parameterRow: {
                key: "",
                value: "",
            },
            discounts_options: [],
            xml_import_tasks: [],
            responseErrors: {},
            vendor_options: [],
            category_options: [],
            supplier_options: [],
            task_options: [],
            product_draft_options: [],
            parent_options: [],
            compare_options: [],
            action_options: [],
            boxes: {
                CreateNew: {
                    key: "createNew",
                    group: "xml_import",
                    title: "Create new XML job",
                    infoTitle: "Create new XML job",
                    infoDescription: "__Create new XML job description",
                },
                Action: {
                    key: "action",
                    group: "xml_import",
                    title: "Action",
                    infoTitle: "Action",
                    infoDescription: "__Action description",
                },
                Other: {
                    key: "other",
                    group: "xml_import",
                    title: "Other",
                    infoTitle: "Other",
                    infoDescription: "__Other description",
                }
            },
            translations: {
                "Cancel": this.$t("Cancel"),
                "Validate XML and continue": this.$t("Validate XML and continue"),
                "Select": this.$t("Select"),
                "Job name": this.$t("Job name"),
                "XML url": this.$t('XML url'),
                "URL": this.$t('URL'),
                "Full url to XML feed": this.$t('Full url to XML feed'),
                "Test tag": this.$t('Test tag'),
                "Success": this.$t('Success'),
                "Error": this.$t('Error'),
                "Action": this.$t('Action'),
                "Compare by": this.$t('Compare by'),
                "XML Product tag": this.$t('XML Product tag'),
                "Entry XML tag for product": this.$t('Entry XML tag for product'),
                "Rows": this.$t('Rows'),
                "Publish imported products": this.$t('Publish imported products'),
                "Supplier": this.$t('Supplier'),
                "Assign to": this.$t('Assign to'),
                "This is a main XML job": this.$t('This is a main XML job'),
                "Disable missing product": this.$t('Disable missing product'),
                "Track quantity of imported products": this.$t('Track quantity of imported products'),
                "Continue selling": this.$t('Continue selling'),
                "Task ID number": this.$t('Task ID number'),
                "Example: 123123": this.$t('Example: 123123'),
                "Choose Fixed Discount type": this.$t('Choose Fixed Discount type'),
                "Disable missing products": this.$t('Disable missing products'),
                "Not published": this.$t('Not published'),
                "Published": this.$t('Published'),
                "Official ID": this.$t('Official ID'),
                "SKU": this.$t('SKU'),
                "Barcode": this.$t('Barcode'),
                "Import and update": this.$t('Import and update'),
                "Update": this.$t('Update'),
                "Save and go to step 2": this.$t('Save and go to step 2'),
                'XML Product tag field is required.': this.$t('XML Product tag field is required.'),
                'XML url field is required.': this.$t('XML url field is required.'),
                'XML url field must be a valid URL.': this.$t('XML url field must be a valid URL.'),
                'Rows field is required.': this.$t('Rows field is required.'),
                'Rows field must be an integer.': this.$t('Rows field must be an integer.'),
                'Rows field must be at least 20.': this.$t('Rows field must be at least 20.'),
                'Rows field must not be greater than 1500.': this.$t('Rows field must not be greater than 1500.'),
                'Unable to find search tag: {product_tag}': this.$t('Unable to find search tag: {product_tag}', {product_tag:'{product_tag}'}),
                'Document is empty or is not valid XML': this.$t('Document is empty or is not valid XML'),
                'Unable to open XML file': this.$t('Unable to open XML file'),
                'Unable to open url': this.$t('Unable to open url'),
                'Unable to parse XML file': this.$t('Unable to parse XML file'),
                'Job name field is required.': this.$t('Job name field is required.'),
                'Task ID number field is required.': this.$t('Task ID number field is required.'),
                'Task ID number may only contain letters and numbers.': this.$t('Task ID number may only contain letters and numbers.'),
                'You can have maximum {max} active tasks.': this.$t('You can have maximum {max} active tasks.'),
            },
        };
    },
    mounted() {
        // this.getCategories();
        //this.getTaskOptions();
        this.getData();

        this.discounts_options = [
            {id: "", name: this.translations["Select"]}
        ];

        this.product_draft_options = [
            {id: "yes", name: this.translations["Not published"]},
            {id: "no", name: this.translations["Published"]},
        ];
        this.parent_options = [{id: "", name: this.translations["This is a main XML job"]}];
        this.compare_options = [
            {id: "id", name: this.translations["Official ID"]},
            {id: "sku", name: this.translations["SKU"]},
            {id: "barcode", name: this.translations["Barcode"]},
        ];
        this.action_options = [
            {id: "import", name: this.translations["Import and update"]},
            {id: "update", name: this.translations["Update"]},
        ]
    },
    methods: {
        getData() {
            this.isLoaded = false;

            let url = "";
            this.id
                ? (url = `/admin/api/xml_import/edit/${this.id}`)
                : (url = "/admin/api/xml_import/create");

            this.discounts = [];
            this.xml_import_tasks = [];
            axios
                .get(url)
                .then((res) => {
                    if (!Array.isArray(res.data.xml_import)) {
                        this.xml_import = {
                            ...this.xml_import,
                            ...res.data.xml_import
                        }
                    }
                    if (res.data.meta) {
                        this.meta = {
                            ...this.meta,
                            ...res.data.meta
                        }
                    }

                    Object.entries(res.data.discounts).map(([value, label]) =>
                        this.discounts_options.push({id: value, name: label})
                    );

                    this.supplier_options = res.data.suppliers.map((x) => ({
                        id: x.id,
                        name: x.name,
                    }));

                    Object.entries(res.data.lists).forEach(([value, label]) => {
                        this.parent_options.push({id: value, name: label});
                    });

                    if (res.data.xml_import.length != 0) {
                        if (
                            this.xml_import.task_key &&
                            Object.keys(this.xml_import.task_key).length != 0
                        ) {
                            this.xml_import.task_key = Object.values(this.xml_import.task_key)[0];
                        }
                    }

                    if (res.data.xml_import_vendor) {
                        this.vendor_options.push(res.data.xml_import_vendor);
                    }

                    if (res.data.xml_import_category) {
                        this.category_options.push(res.data.xml_import_category);
                    }

                    this.isLoaded = true;
                })
                .catch((err) => {
                    this.$errorResponse(err);
                    if ([403, 402].includes(err.response.status)) {
                        setTimeout(() => {
                            this.$router.push({name: "apps.xml_import.settings"});
                        }, 200);
                    }
                })
                .finally(() => {
                });
        },
        submitData() {
            this.submitLoader = true;

            this.responseErrors = {};

            const body = {
                xml_import: {
                    name: this.xml_import.name,
                    url: this.xml_import.url,
                    product_tag: this.xml_import.product_tag,
                    lines: this.xml_import.lines,
                    parent_id: this.xml_import.parent_id,
                    task_key: this.xml_import.task_key,
                    discount_id: this.xml_import.discount_id,
                },
                category_id: this.xml_import.category_id,
                vendor_id: this.xml_import.vendor_id,
                product_draft: this.xml_import.product_draft,
                meta: this.meta,
                track_inventory: this.xml_import.track_inventory,
                continue_selling: this.xml_import.continue_selling,
            };

            const formData = new FormData();
            buildFormData(formData, body);

            let url = "";
            this.id
                ? (url = `/admin/api/xml_import/edit/${this.id}`)
                : (url = "/admin/api/xml_import/create/");

            axios
                .post(url, formData, {
                    headers: {
                        "Content-Type": "multipart/form-data",
                    },
                })
                .then((res) => {
                    this.$router.push(`/admin/apps/xml_import/step2/${res.data.id}`);
                })
                .catch((err) => {
                    this.$errorResponse(err);
                })
                .finally(() => {
                    this.submitLoader = false;
                });
        },
        getCategories() {
            axios.get("/admin/api/core/product-categories/search").then((res) => {
                this.category_options = Object.entries(res.data).map(([value, label]) => ({
                    value,
                    label,
                }));
            })
            .catch(err => {
                this.$errorResponse(err);
            });
        },
        // getTaskOptions() {
        //     axios.get("/admin/api/xml_import/task_key").then((res) => {
        //         this.task_options = Object.keys(res.data.results);
        //     })
        //     .catch((err) => {
        //         console.log(err)
        //     });
        // },
        testTag() {
            this.submitLoaderTag = true;
            this.responseErrors = {}
            axios.post('/admin/api/xml_import/check-tag/' + (this.xml_import.id || ''), {name: this.xml_import.name,product_tag: this.xml_import.product_tag,url: this.xml_import.url,lines: this.lines})
                .then((res) => {
                    this.isTagExist = res.data.exists
                    this.xml_import = res.data.feed
                    this.isTagOrProductTagChanged = !this.isTagExist
                    this.url = this.xml_import.url
                    this.product_tag = this.xml_import.product_tag
                    this.$emit('update:id', this.xml_import.id)
                    this.$router.push({ name: 'apps.xml_import.edit', params: { id: this.xml_import.id  } })
                })
                .catch(err => {
                    this.$errorResponse(err, {
                        product_tag: this.xml_import.product_tag
                    })
                    this.isTagExist = false
                })
                .finally(() => {
                    this.submitLoaderTag = false;
                })
        },
    },
    watch: {
        'xml_import': {
            deep: true,
            handler(newVal, oldVal) {
                if(this.url === null) {
                    this.url = newVal.url;
                }
                if(this.product_tag === null) {
                    this.product_tag = newVal.product_tag;
                }

                this.isTagOrProductTagChanged = this.product_tag !== newVal.product_tag
                    || this.url !== newVal.url;
            }
        }
    },
    computed: {
        allowAditionalSettings() {
            if(this.isTagOrProductTagChanged) {
                return false;
            }

            return this.isTagExist;
        },
        disabledAditionalSettings() {
            return !this.allowAditionalSettings;
        },
        isSuppliersInstalled() {
            const suppliers = (this.app?.optional_apps || []).find((app) => {
                return app.key === 'suppliers'
            });

            return suppliers?.is_installed || false;
        },
        computedTaskKey: {
            get() {
                return this.xml_import.task_key ? [{id: this.xml_import.task_key, name: this.xml_import.task_key}] : null
            },
            set(value) {
                this.xml_import.task_key = value;
            }
        },
        lines: {
            get() {
                return this.xml_import?.lines || 100;
            },
            set(val) {
                this.xml_import.lines = val
            }
        }
    },
    emits: ['update:id']
});
</script>
