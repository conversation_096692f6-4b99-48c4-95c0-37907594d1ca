<template>
    <div>
        <b-row v-if="joinable">
            <b-col class="my-2" :md="type === 'join' ? 12 : 5">
                <SelectWithAjax
                    :val="type"
                    @update:val="(val) => $emit('update:type', val)"
                    :options="type_options"
                    :column-style="true"
                    :cols-width="12"
                    :searchable="true"
                    :no-margin="true"
                    :can-clear="false"
                />
            </b-col>
            <b-col v-if="type == 'join'" cols="12">
                <div v-for="(item, index) in rowData" class="item-map w-100 my-2">
                    <SelectWithAjax
                        @update:val="(val) => (rowData[index] = val)"
                        :val="item"
                        :options="options"
                        :column-style="true"
                        :cols-width="index != 0 ? 11 : 12"
                        :searchable="true"
                        :error="responseErrors['join_name']"
                        :no-margin="true"
                    />
                    <a
                        v-if="index !== 0"
                        @click.prevent="removeRow(index)"
                        href="#"
                        class="fal fa-times-circle fa-lg cc-grey remove-item"
                    ></a>
                </div>
            </b-col>
            <b-col v-else class="my-2">
                <SelectWithAjax
                    @update:val="(val) => $emit('update:mapped', val)"
                    :val="rowData"
                    :options="options"
                    :column-style="true"
                    :cols-width="12"
                    :searchable="true"
                    :error="responseErrors['name']"
                    :no-margin="true"
                />
            </b-col>
        </b-row>
        <b-row v-if="addable && !joinable">
            <b-col cols="12">
                <div v-for="(item, index) in rowData" class="item-map w-100 my-2">
                    <SelectWithAjax
                        @update:val="(val) => {rowData[index] = val; this.$emit('update:mapped', rowData)}"
                        :val="item"
                        :options="options"
                        :column-style="true"
                        :cols-width="index != 0 ? 11 : 12"
                        :searchable="true"
                        :error="responseErrors[rowData?.name]"
                        :no-margin="true"
                    />
                    <a
                        v-if="index != 0"
                        @click.prevent="removeRow(index)"
                        href="#"
                        class="fal fa-times-circle fa-lg cc-grey remove-item"
                    ></a>
                </div>
            </b-col>
        </b-row>
        <b-row v-if="(addable || (joinable && type == 'join')) && rowData?.length <= 5">
            <b-col class="text-center">
                <a href="#"
                   class="add-link smaller fw-medium d-inline-flex align-items-center"
                   @click.prevent="addRow"
                >
                    <i class="far fa-plus-circle me-1"></i>
                    {{ translations["Add level"] }}
                </a>
            </b-col>
        </b-row>
    </div>
</template>
<script>
import SelectWithAjax from "@components/Form/SelectWithAjax";

export default {
    name: "Step2Item",
    components: {
        SelectWithAjax,
    },
    props: {
        data: {},
        mapped: {},
        options: {},
        type: {},
        addable: {},
        joinable: {},
        responseErrors: {
            default: {}
        }
    },
    data() {
        return {
            rowData: this.mapped,
            selectedType: this.type,
            type_options: [],
            typeBaseJoin: this.type,
            translations: {
                "Add level": this.$t("Add level"),
                "Normal": this.$t("Normal"),
                "Multiple elements": this.$t("Multiple elements"),
            }
        };
    },
    mounted() {
        this.type_options = [
            {id: "base", name: this.translations["Normal"]},
            {id: "join", name: this.translations["Multiple elements"]},
        ]

        if (!this.rowData) {
            this.addRow()
        }

        this.data.multiple ? this.$emit("update:type", "join") : this.$emit("update:type", "base")
        },
    methods: {
        addRow() {
            // this.rowData.push(_.cloneDeep(this.row));
            if(this.rowData) {
                this.rowData.push('');
            } else {
                this.rowData = [''];
            }
        },
        removeRow(index) {
            this.rowData.splice(index, 1);
        },
        changeValue(val, index) {
        },
    },
    watch: {
        type(val) {
            if (val == "join") {
                if(this.rowData && !Array.isArray(this.rowData)) {
                    this.rowData = [this.rowData]
                }
                this.$emit("update:mapped", this.rowData);
                this.$emit('update:data', {
                    ...this.data,
                    multiple: true,
                    type: 'join'
                });
            } else {
                this.rowData && this.rowData.length > 0 ? this.rowData = this.rowData[0] : this.rowData = '';
                this.$emit("update:mapped", this.rowData);
                this.$emit('update:data', {
                    ...this.data,
                    multiple: null,
                    type: 'base'
                });
            }
        },
    },
    emits: ['update:type', 'update:mapped', 'update:data']
};
</script>
