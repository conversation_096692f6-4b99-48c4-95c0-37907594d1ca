<template>
    <b-col lg="4">
        <SelectWithAjax
            v-model:val="data.has_action_yes_no"
            :options="options"
            :searchable="true"
            :column-style="true"
            :cols-width="12"
            :placeholder="translations['Select']"
            :can-clear="false"
        />
    </b-col>
    <b-col lg="4">
        <InputComponent
            v-model="data.has_yes_no"
            :column-style="true"
        />
    </b-col>
    <b-col lg="4">
        <SelectWithAjax
            v-model:val="data.yesno"
            :options="subOptions"
            :searchable="true"
            :column-style="true"
            :cols-width="12"
            :placeholder="translations['Select']"
            :can-clear="false"
        />
    </b-col>
</template>

<script>
import SelectWithAjax from "@components/Form/SelectWithAjax";
import InputComponent from "@components/Form/InputComponent";

export default {
    name: 'yesno',
    components: {
        SelectWithAjax,
        InputComponent,
    },
    props: {
        modelValue: {
            type: Object,
            required: true,
            default: {},
        }
    },
    data() {
        return {
            data: this.modelValue,
            translations: {
                'Select': this.$t('Select'),
                'Yes': this.$t('Yes'),
                'No': this.$t('No'),
                'Equal': this.$t('Equal'),
                'Contained': this.$t('Contained'),
                'Is less than': this.$t('Is less than'),
                'Is greater than': this.$t('Is greater than'),
            }
        }
    },
    watch: {
        data:{
            deep: true,
            handler(val) {
                this.$emit('update:modelValue', val)
            }
        },
        modelValue:{
            deep: true,
            handler(val) {
                this.data = val
            }
        },
    },
    computed: {
        options() {
            return [
                {id: 'equal', name: this.translations['Equal']},
                {id: 'contained', name: this.translations['Contained']},
                {id: 'larger', name: this.translations['Is greater than']},
                {id: 'less', name: this.translations['Is less than']},
            ]
        },
        subOptions() {
            return [
                {id: 'yes', name: this.translations['Yes']},
                {id: 'no', name: this.translations['No']},
            ]
        },
    },
    emits: ['update:modelValue']
}
</script>