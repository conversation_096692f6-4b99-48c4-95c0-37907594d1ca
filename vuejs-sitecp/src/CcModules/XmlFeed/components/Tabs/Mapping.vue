<template>
        <data-table
            v-if="table.data"
            :enable-mobile="true"
            v-bind="this.table"
            v-model="selected"
            v-model:query="query"
            :get-data="getItems"
    
            @pagination-change-page="paginate">

            <template #button>
                <b-button type="default" variant="default" @click.prevent="() => { item={}; modal = true }">
                    <i class="fal fa-plus"></i>
                    {{ translations['Add new record'] }}
                </b-button>
            </template>
            <template #noResult>
                <b-button type="default" variant="default" @click.prevent="() => { item={}; modal = true }">
                    <i class="fal fa-plus"></i>
                    {{ translations['Add new record'] }}
                </b-button>
            </template>
            <TableActions
                v-model="selected"
                :app-key="app.key"
                :enable-mobile="true"
                delete-type="delete"
                :delete-url="`/admin/api/xml_feed/mapping/${appName}/0`"
                @success="handleActions" />
        </data-table>

        <mapping-modal
            v-model="modal"
            :item="item"
            :app="app"
            :app-name="appName"
            @created="(x) => { withoutLoading = true; paginate(1, () => { flashItem(x) }); }"
            @updated="(x) => { updateItem(x) }"
        ></mapping-modal>
</template>

<script>
import DataTable from "@components/Table";
import {markRaw} from "vue";
import Actions from "./CategoryMapTable/Actions";
import Edit from "./CategoryMapTable/Edit";
import MappingModal from "./CategoryMapTable/MappingModal";
import CategoryMap from "./../../js/CategoryMap";
import {toast} from "@js/toast";
import TableActions from "@components/TableActions.vue";
import _ from "lodash";

export default {
    components: {
        TableActions,
        DataTable,
        MappingModal
    },
    props: {
        appName: {
            type: String,
            required: false
        },
        appKey: {
            type: String,
            required: false
        },
        modelValue: {
            type: Object,
            default: {}
        },
    },
    data() {
        return {
            loading: true,
            translations: {
                'Apps': this.$t('Apps'),
                'Products': this.$t('Products'),
                'No results found': this.$t('No results found'),
                'CloudCart category': this.$t('CloudCart category'),
                '{app} category': this.$t('{app} category', {app:'{app}'}),
                'Action': this.$t('Action'),
                'Add new record': this.$t('Add new record'),
                'Record was deleted successfully': this.$t('Record was deleted successfully'),
            },
            currentPage: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            query: this.$route.query,
            table: {},
            app: this.modelValue,
            config: this.settings,
            model: new CategoryMap(this.appName),
            item: {},
            modal: false,
            selected: [],
            withoutLoading: false,
        };
    },
    methods: {
        async handleActions(value) {
            if (value.action === "delete") {
                this.withoutLoading = true;
                await this.getItems();
            }
        },
        async getItems(callback) {
            try {
                if(!this.withoutLoading) {
                    this.table.isLoading = true
                }
                this.withoutLoading = false
                this.table.data = await this.model.paginate(this.query?.page || this.currentPage, this.query?.perpage || this.perPage)

                this.table.data.data = this.table.data.data.map((x) => {
                    return {
                        ...x,
                        onDelete: this.onDelete,
                        onEdit: this.onEdit
                    }
                })

                if(typeof(callback) === 'function') {
                    callback()
                }

                this.$router.push({query: {...this.$route.query, page: this.table.data.current_page}})
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.loading = false
                this.table.isLoading = false
            }
        },
        async paginate(page, callback) {
            this.currentPage = page;
            await this.getItems(callback);
        },
        async onDelete(item, ui)
        {
            ui.deleteLoading = true
            item.deleting = true
            try {
                this.model.where({page:this.currentPage, perpage:this.perPage})
                this.table.data = await this.model.delete(item.id)
                this.table.data.data = this.table.data.data.map((x) => {
                    return {
                        ...x,
                        onDelete: this.onDelete,
                        onEdit: this.onEdit
                    }
                })

                //fix for tooltip Mitio
                document.querySelectorAll('.tooltip').forEach((tooltip) => {
                    tooltip.parentNode.removeChild(tooltip);
                })

                toast.success(this.translations['Record was deleted successfully']);
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                ui.deleteLoading = false
            }
        },
        async onEdit(item, ui)
        {
            this.item = {...item, _: Date.now()}
            this.modal = true
        },
        updateItem(item)
        {
            const index = _.findIndex(this.table.data.data, {id: item.id});
            if(index > -1) {
                _.merge(this.table.data.data[index], {...item, flashing: true});
            }
        },
        flashItem(item)
        {
            const index = _.findIndex(this.table.data.data, {id: item.id});
            if(index > -1 && this.table.data.data[index]) {
                this.table.data.data[index].flashing = true;
            }
        },
        setupTable() {
                this.table = {
                    data: {
                        data: [],
                    },
                    defaultSorting: [{ key: "id", sortingMode: "desc" }],
                    isLoading: this.loading,
                    paginationLimit: 2,
                    columns: [
                        {
                            column: "internalCategoryName",
                            key: "internalCategoryName",
                            sortable: false,
                            title: this.translations['CloudCart category'],
                            component: markRaw(Edit),
                        },
                        {
                            column: "externalCategoryName",
                            key: "externalCategoryName",
                            sortable: false,
                            title: this.$trp(this.translations['{app} category'], {app: this.app.name}),
                        },
                        {
                            column: "actions",
                            key: "action",
                            sortable: false,
                            title: this.translations['Action'],
                            component: markRaw(Actions),
                        },
                    ],
                }
        },
    },
    async mounted() {
        this.setupTable();
        await this.getItems();
    },
};
</script>

<style>
.history-list-thumb {
    display: table;
}
.history-list-thumb > * {
    display: table-cell;
    vertical-align: top;
}
.history-list-thumb img {
    width: 50px;
    margin-right: 15px;
}

.history-list-actions > * {
    display: inline-block;
}

.history-list-actions i {
    cursor: pointer;
}
</style>