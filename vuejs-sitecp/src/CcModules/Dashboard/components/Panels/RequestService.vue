<template>
    <div class="panel panel-default panel-request-service">
        <div class="panel-body pb-0">
            <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-3">
                <div>
                    <h2 class="mb-3">
                        {{ translations['Request a service'] }}
                    </h2>
                    <p class="mb-0">
                        {{ translations['Partner with top notch agencies & providers from our Partners Network & unleash the true potential of your business.'] }}
                    </p>
                </div>
                <div>
                    <TypeForm />
                </div>
            </div>
            <figure class="position-relative c-pointer" @click="showVideo = true">
                <img
                    :src="this.serverSettings('img_url')+ 'sitecp/img/request-a-service.png?' + this.serverSettings('last_build')"
                    alt="Request a service"
                />
                <PlayButton />
            </figure>

            <b-modal v-model="showVideo" class="modal-lg" no-footer="true">
                <iframe
                    ref="iframe"
                    v-if="showVideo"
                    width="100%" height="911"
                    src="https://www.youtube.com/embed/DzCzNa3v3l0"
                    title="CloudCart Growth Ecosystem - Don&#39;t worry, we got it"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    allowfullscreen
                ></iframe>
            </b-modal>
        </div>
    </div>
</template>

<script>
import TypeForm from '../Helpers/TypeForm';
import PlayButton from "../Helpers/PlayButton"

export default {
    name: "RequestService",
    components: {
        TypeForm,
        PlayButton
    },
    data () {
        return {
            showVideo: false,
            translations: {
                'Request a service': this.$t('Request a service'),
                'Partner with top notch agencies & providers from our Partners Network & unleash the true potential of your business.':
                this.$t('Partner with top notch agencies & providers from our Partners Network & unleash the true potential of your business.')
            }
        }
    }
}
</script>

<style>
@import './../../scss/_request-service.scss';
</style>
