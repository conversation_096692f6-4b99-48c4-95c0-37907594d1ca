<template>
    <div id="steps-config">
        <b-card>
            <b-row class="gy-3 mb-4">
                <b-col
                    class="col-12 d-flex align-items-center gap-3 justify-content-between"
                >
                    <p class="label-500-18px m-0">
                        {{ translations["Setup your store"] }}
                    </p>
                </b-col>
                <b-col class="col-12">
                    <p
                        class="text-400-secondary-16px m-0"
                        v-html="
                            translations[
                                'This store setup checklist is designed to help you walk through all the important steps to successfully launch your online store. It shows you exactly what you need to do and tracks your progress until you complete each task. This way, you can be sure you haven\'t missed any important settings and you\'re ready to start selling.'
                            ]
                        "
                    ></p>
                </b-col>
                <b-col class="col-12">
                    <span
                        class="text-400-secondary d-block w-100 text-right mb-1"
                    >
                        {{ progress }}
                    </span>
                    <b-progress
                        :value="stepsCompleted"
                        variant="success"
                        :max="Object.keys(steps).length"
                    >
                    </b-progress>
                </b-col>
                <b-col
                    v-if="isCompletedConfiguration"
                    class="col-12 d-flex justify-content-center align-items-center mt-3"
                >
                    <button
                        class="btn btn-white complete-task"
                        @click="onSubmit"
                        :disabled="submitLoader"
                    >
                        <b-spinner v-if="submitLoader" small></b-spinner>
                        <i v-else class="fa-regular fa-badge-check mb-1"></i>
                        {{ translations["Switch to Full Dashboard"] }}
                    </button>
                </b-col>
            </b-row>

            <DropdownWithBorder
                v-for="step in Object.values(sortedSteps)"
                :id="`${step.key}-dashboard`"
                :key="step.key"
                :modelValue="openAccordion === step.key"
                :title="titleEnum[step.key]"
                :dropdown-ref="step.key"
                :border-type="step.completed ? 'success' : 'default'"
                :toggle="() => handleToggleAccordion(step)"
                :content-classes="step.completed ? 'step-done' : ''"
            >
                <template #logo>
                    <i :class="step.icon"></i>
                </template>
                <template v-if="step.completed" #action>
                    <i
                        class="fa-regular fa-badge-check status-color"
                        :class="{
                        completed: step.completed,
                    }"
                    ></i>
                </template>
                <template #content>
                    <div class="px-2 py-3">
                        <p
                            class="text-400-primary mb-3"
                            style="max-width: 90%; line-height: 22px"
                            v-html="descriptionEnum[step.key]"
                        ></p>
                        <div>
                            <p class="label-500-16px mb-2">
                                {{
                                    translations[
                                        "Conditions for completing the tasks"
                                        ]
                                }}
                            </p>
                            <div class="d-flex flex-column gap-2 ps-2 pe-1">
                                <div
                                    class="d-flex align-items-center justify-content-between gap-3"
                                    style="max-width: 90%"
                                >
                                    <div
                                        class="text-400-primary"
                                        v-html="
                                        conditionsEnum?.[step?.key]
                                    "
                                    ></div>
                                </div>
                            </div>
                        </div>
                        <div v-if="checkWaringMessage(step) && step?.warningMessage">
                            <div
                                class="info-box rounded d-flex align-items-center flex-row gap-3 p-3 border-0"
                            >
                                <figure class="m-0">
                                    <svg
                                        width="34"
                                        height="34"
                                        viewBox="0 0 34 34"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M0 17C0 7.61116 7.61116 0 17 0C26.3888 0 34 7.61116 34 17C34 26.3888 26.3888 34 17 34C7.61116 34 0 26.3888 0 17Z"
                                            fill="white"
                                        />
                                        <path
                                            d="M14.3281 23.1914H15.0312V18.0938H14.3281C13.9062 18.0938 13.625 17.7773 13.625 17.3906V15.7031C13.625 15.3164 13.9062 15 14.3281 15H18.2656C18.6523 15 18.9688 15.3164 18.9688 15.7031V23.1914H19.6719C20.0586 23.1914 20.375 23.5078 20.375 23.8945V25.5469C20.375 25.9688 20.0586 26.25 19.6719 26.25H14.3281C13.9062 26.25 13.625 25.9688 13.625 25.5469V23.8945C13.625 23.5078 13.9062 23.1914 14.3281 23.1914ZM17 8.25C18.3711 8.25 19.5312 9.41016 19.5312 10.7812C19.5312 12.1875 18.3711 13.3125 17 13.3125C15.5938 13.3125 14.4688 12.1875 14.4688 10.7812C14.4688 9.41016 15.5938 8.25 17 8.25Z"
                                            fill="#5FCCEE"
                                        />
                                    </svg>
                                </figure>
                                <div
                                    class="info-text text-wrap"
                                    v-html="warningMsgEnum[step.key]"
                                ></div>
                            </div>
                        </div>
                        <div
                            v-if="step?.action || step?.to"
                            class="d-flex align-items-center justify-content-between gap-3 w-100 flex-wrap mt-3"
                        >
                            <component
                                :is="step.to ? 'router-link' : 'a'"
                                :to="step.to || null"
                                class="btn"
                                :href="step.to ? null : 'javascript:void(0)'"
                                :class="
                                step?.completed ? 'btn-white' : 'btn-primary'"
                                :style="{
                                opacity:
                                    step?.completed || step?.loading || step.skipLoader ? 0.4 : 1,
                                pointerEvents:
                                    step?.completed || step?.loading || step.skipLoader
                                        ? 'none'
                                        : 'auto',
                            }"
                                @click="
                                () => {
                                    if (step.action) {
                                        step.action();
                                    }
                                }
                            "
                            >
                                <b-spinner v-if="step?.loading" small></b-spinner>
                                <i
                                    v-if="!step?.loading && step?.completed"
                                    class="fa-regular fa-badge-check"
                                ></i>
                                {{
                                    step?.completed
                                        ? translations[
                                            "This step has been successfully completed"
                                            ]
                                        : buttonLabelEnum[step.key]
                                }}
                            </component>

                            <a
                                v-if="step.skippable"
                                href="javascript:void(0);"
                                class="btn btn-ghost d-flex align-items-center gap-1"
                                @click="() => skipStepWithToaster(step)"
                                :style="{
                                opacity:
                                    step?.completed || step?.loading || step.skipLoader ? 0.4 : 1,
                                pointerEvents:
                                    step?.completed || step?.loading || step.skipLoader
                                        ? 'none'
                                        : 'auto',
                                }"
                            >
                                <b-spinner v-if="step?.skipLoader" small></b-spinner>
                                {{ translations['Skip this step'] }}
                            </a>
                        </div>
                    </div>
                </template>
            </DropdownWithBorder>
        </b-card>

        <ConfirmModal
            :title="translations['Switching to Live Environment']"
            :message="
                this.translations[
                    'Please confirm that you want to move to the live environment. Note that this action is irreversible.'
                ]
            "
            v-model="confirmGoLiveModal"
            :submitLoader="steps?.go_live?.loading"
            @ok="onSubmitGoLive"
        >
            <template v-if="goLiveError">
                <div
                    class="info-box info-box-error d-flex align-items-center justify-content-center gap-3 rounded-3 p-3"
                >
                    <i class="far fa-exclamation-circle fs-5"></i>
                    <span>{{ translations['Something went wrong, please try again later'] }}</span>
                </div>
            </template>
        </ConfirmModal>
    </div>
</template>
<script>
import {toast} from "@js/toast";

import DropdownWithBorder from "@components/DropdownWithBorder";
import ConfirmModal from "@components/ConfirmModal";
import useOnboardingProgress from "../js/useOnboardingProgress";

export default {
    name: 'OnboardingProgressNew',
    components: {
        DropdownWithBorder,
        ConfirmModal
    },
    setup() {
        const {
            sortedSteps,
            goLiveError,
            confirmGoLiveModal,
            getStepsStatus,
            steps,
            loading,
            onboardingProgressComplete,
            stepsCompleted,
            isCompletedConfiguration,
            onSubmit,
            onSubmitGoLive,
            submitLoader,
            skipStep
        } = useOnboardingProgress();

        return {
            sortedSteps,
            goLiveError,
            confirmGoLiveModal,
            getStepsStatus,
            steps,
            loading,
            onboardingProgressComplete,
            stepsCompleted,
            isCompletedConfiguration,
            onSubmit,
            onSubmitGoLive,
            submitLoader,
            skipStep
        }
    },
    data() {
        return {
            openAccordion: null,
            translations: {
                "Setup your store": this.$t("Setup your store"),
                'Skip this step': this.$t('Skip this step'),
                "{processed} of {total} steps completed": this.$t(
                    "{processed} of {total} steps completed"
                ),
                "Products add": this.$t("Products add"),
                "Add products to your store": this.$t(
                    "Add products to your store"
                ),

                "Add products to your store long description": this.$t(
                    "Add products to your store long description"
                ),
                "Add products": this.$t("Add products"),
                "Domains set": this.$t("Domains set"),
                "Connect a custom domain": this.$t("Connect a custom domain"),
                "Custom domain setup description": this.$t(
                    "Custom domain setup description"
                ),
                "Connect domain": this.$t("Connect domain"),
                "Payment methods": this.$t("Payment methods"),
                "Configure payment methods": this.$t(
                    "Configure payment methods"
                ),
                "Configure payment methods description": this.$t(
                    "Configure payment methods description"
                ),
                "Configure payments": this.$t("Configure payments"),
                "Shipping methods": this.$t("Shipping methods"),
                "Configure shipping methods": this.$t(
                    "Configure shipping methods"
                ),
                "Configure shipping methods description": this.$t(
                    "Configure shipping methods description"
                ),
                "Configure shipings": this.$t("Configure shipings"),
                "Go live": this.$t("Go live"),
                "Start selling online": this.$t("Start selling online"),
                "Start selling online description": this.$t(
                    "Start selling online description"
                ),
                "Start selling": this.$t("Start selling"),
                SSL: this.$t("SSL"),
                "Secure your store": this.$t("Secure your store"),
                "Install an SSL certificate to protect your store.": this.$t(
                    "Install an SSL certificate to protect your store."
                ),
                "Install SSL": this.$t("Install SSL"),
                GDPR: this.$t("GDPR"),
                "Install the GDPR application and create the required pages":
                    this.$t(
                        "Install the GDPR application and create the required pages"
                    ),
                "Plan checklist": this.$t("Plan checklist"),
                "Plan selection": this.$t("Plan selection"),
                "The user needs to select and activate a plan through the AI ​​assistant.":
                    this.$t(
                        "The user needs to select and activate a plan through the AI ​​assistant."
                    ),
                "Install and setup GDPR": this.$t("Install and setup GDPR"),
                "Orders checklist": this.$t("Orders checklist"),
                "Orders short description": this.$t("Orders short description"),
                "Place three test orders to test your store's ordering process.":
                    this.$t(
                        "Place three test orders to test your store's ordering process."
                    ),
                "Set orders": this.$t("Set orders"),
                "Select Plan": this.$t("Select Plan"),
                "Add at least one product and publish it": this.$t(
                    "Add at least one product and publish it"
                ),
                "Create new domain and set it as primary": this.$t(
                    "Create new domain and set it as primary"
                ),
                "Install SSL certificate.": this.$t("Install SSL certificate."),
                "Select and activate at least two payment providers": this.$t(
                    "Select and activate at least two payment providers"
                ),
                "Select and activate at least two shipping provivers": this.$t(
                    "Select and activate at least two shipping provivers"
                ),
                'At least one order to be marked as "Completed".': this.$t(
                    'At least one order to be marked as "Completed".'
                ),
                'At least one order to be marked as "Fulfilled".': this.$t(
                    'At least one order to be marked as "Fulfilled".'
                ),
                'At least one order to be marked as "Cancelled".': this.$t(
                    'At least one order to be marked as "Cancelled".'
                ),
                "You have selected a plan for your store": this.$t(
                    "You have selected a plan for your store"
                ),
                "The store must be up and running": this.$t(
                    "The store must be up and running"
                ),
                "Conditions for completing the tasks": this.$t(
                    "Conditions for completing the tasks"
                ),
                "The GDPR app must be installed": this.$t(
                    "The GDPR app must be installed"
                ),
                "The necessary pages have been created (Privacy Policy, General Terms, etc.).":
                    this.$t(
                        "The necessary pages have been created (Privacy Policy, General Terms, etc.)."
                    ),
                "GDPR short description": this.$t("GDPR short description"),
                "Make sure the product is posted to be visible to customers.":
                    this.$t(
                        "Make sure the product is posted to be visible to customers."
                    ),
                "Add your custom domain": this.$t("Add your custom domain"),
                "Make the domain as your primary": this.$t(
                    "Make the domain as your primary"
                ),
                "Activate your certificate, to protect your store": this.$t(
                    "Activate your certificate, to protect your store"
                ),
                "Check if the payment methods are working correctly": this.$t(
                    "Check if the payment methods are working correctly"
                ),
                "Check if the shipping methods are working correctly": this.$t(
                    "Check if the shipping methods are working correctly"
                ),
                "Choose and activate a plan that best suits your needs.":
                    this.$t(
                        "Choose and activate a plan that best suits your needs."
                    ),
                "Check our plans": this.$t("Check our plans"),
                "Make sure all key settings are complete": this.$t(
                    "Make sure all key settings are complete"
                ),
                "Select a plan if you haven't already": this.$t(
                    "Select a plan if you haven't already"
                ),
                "Launch your store and remove Sandbox mode. All test data will be deleted":
                    this.$t(
                        "Launch your store and remove Sandbox mode. All test data will be deleted"
                    ),
                "This store setup checklist is designed to help you walk through all the important steps to successfully launch your online store. It shows you exactly what you need to do and tracks your progress until you complete each task. This way, you can be sure you haven't missed any important settings and you're ready to start selling.":
                    this.$t(
                        "This store setup checklist is designed to help you walk through all the important steps to successfully launch your online store. It shows you exactly what you need to do and tracks your progress until you complete each task. This way, you can be sure you haven't missed any important settings and you're ready to start selling."
                    ),
                "Switch to Full Dashboard": this.$t("Switch to Full Dashboard"),
                "This step has been successfully completed": this.$t(
                    "This step has been successfully completed"
                ),
                "Set your domain": this.$t("Set your domain"),
                "To secure your store with an SSL certificate, you first need to set up your store's domain.":
                    this.$t(
                        "To secure your store with an SSL certificate, you first need to set up your store's domain."
                    ),
                "Switching to Live Environment": this.$t(
                    "Switching to Live Environment"
                ),
                "Please confirm that you want to move to the live environment. Note that this action is irreversible.":
                    this.$t(
                        "Please confirm that you want to move to the live environment. Note that this action is irreversible."
                    ),
                'The step has been successfully skipped': this.$t('The step has been successfully skipped'),
                'Something went wrong, please try again later': this.$t('Something went wrong, please try again later'),
            },
        };
    },
    methods: {
        async skipStepWithToaster(step) {
            try {
                const response = await this.skipStep(step);

                if (response) {
                    toast.success(this.translations['The step has been successfully skipped']);
                    this.openAccordion = null;
                } else {
                    toast.error(this.translations['Something went wrong, please try again later']);
                }
            } catch (err) {
                this.$errorResponse(err)
            }
        },
        checkWaringMessage(step) {
            if (step.key === "ssl" && !this.steps?.domain?.completed) {
                return true;
            }
        },
        handleToggleAccordion(step) {
            this.openAccordion =
                this.openAccordion === step.key ? null : step.key;
        },
    },
    computed: {
        progress() {
            return this.$trp(
                this.translations["{processed} of {total} steps completed"],
                {
                    processed: this.stepsCompleted,
                    total: Object.keys(this.steps).length,
                }
            );
        },
        titleEnum() {
            return {
                products: this.translations["Products add"],
                domain: this.translations["Domains set"],
                ssl: this.translations["SSL"],
                gdpr: this.translations["GDPR"],
                payment: this.translations["Payment methods"],
                shipping: this.translations["Shipping methods"],
                orders: this.translations["Orders checklist"],
                plan: this.translations["Plan checklist"],
                go_live: this.translations["Go live"],
            };
        },
        shortDescriptionEnum() {
            return {
                products: this.translations["Add products to your store"],
                domain: this.translations["Connect a custom domain"],
                ssl: this.translations["Secure your store"],
                gdpr: this.translations["GDPR short description"],
                payment: this.translations["Configure payment methods"],
                shipping: this.translations["Configure shipping methods"],
                orders: this.translations["Orders short description"],
                plan: this.translations["Plan selection"],
                go_live: this.translations["Start selling online"],
            };
        },
        descriptionEnum() {
            return {
                products:
                    this.translations[
                        "Add products to your store long description"
                        ],
                domain: this.translations["Custom domain setup description"],
                ssl: this.translations[
                    "Install an SSL certificate to protect your store."
                    ],
                gdpr: this.translations[
                    "Install the GDPR application and create the required pages"
                    ],
                payment:
                    this.translations["Configure payment methods description"],
                shipping:
                    this.translations["Configure shipping methods description"],
                orders: this.translations[
                    "Place three test orders to test your store's ordering process."
                    ],
                plan: this.translations[
                    "The user needs to select and activate a plan through the AI ​​assistant."
                    ],
                go_live: this.translations["Start selling online description"],
            };
        },
        conditionsEnum() {
            return {
                products: this.translations[
                    "Add at least one product and publish it"
                    ],
                domain: this.translations["Add your custom domain"],
                ssl: this.translations["Install SSL certificate."],
                gdpr: this.translations["The GDPR app must be installed"],
                payment: this.translations[
                    "Select and activate at least two payment providers"
                    ],
                shipping: this.translations[
                    "Select and activate at least two shipping provivers"
                    ],
                plan: this.translations["Check our plans"],
                orders: this.translations[
                    'At least one order to be marked as "Completed".'
                    ],
                go_live: this.translations[
                    "Make sure all key settings are complete"
                    ],
            };
        },
        buttonLabelEnum() {
            return {
                products: this.translations["Add products"],
                domain: this.translations["Connect domain"],
                ssl: this.steps?.domain.completed
                    ? this.translations["Install SSL"]
                    : this.translations["Set your domain"],
                gdpr: this.translations["Install and setup GDPR"],
                payment: this.translations["Configure payments"],
                shipping: this.translations["Configure shipings"],
                orders: this.translations["Set orders"],
                plan: this.translations["Select Plan"],
                go_live: this.steps?.plan?.completed
                    ? this.translations["Start selling"]
                    : this.translations["Select Plan"],
            };
        },
        warningMsgEnum() {
            return {
                ssl: this.translations[
                    "To secure your store with an SSL certificate, you first need to set up your store's domain."
                    ],
            };
        },
    }
}
</script>
<style lang="scss">
.step-done {
    background: #ebebf3 !important;
}

#steps-config {
    .complete-task {
        font-size: 16px !important;
        padding: 8px 24px !important;
    }

    .status-color {
        color: var(--Color-Text-Body---cc-color-text-subdued, #cbcbcb);
        font-size: 30px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-right: 2px;

        &.completed {
            color: var(--color-green-400, #25cf8b);
            font-weight: 600;
        }
    }

    .step-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 18px;
        padding: 18px;
        margin-top: 8px;
        border-radius: 8px;
    }

    .skip-button-step.disabled {
        pointer-events: none;
        opacity: 0.5;
    }
}
</style>
