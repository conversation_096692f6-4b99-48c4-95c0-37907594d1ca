<template>
    <b-row
        id="radio-multilang"
        class="justify-content-center p-2"
        v-if="!settings.mapping && data.plans && data.plans.length !== 0"
    >
        <b-col class="col-12 col-md-8 plans-list">
            <b-col
                v-if="errorRequired"
                class="col-10 col-md-8"
                style="color: #fc4f4e; font-weight: 400; line-height: 20px"
            >
                <span v-html="translations['You have not selected a subscription plan']"></span>
            </b-col>
            <div
                v-for="(plan, index) in data.plans"
                :style="{ marginBottom: index < data.plans.length - 1 ? '24px' : '' }"
            >
                <div
                    class="plan-wrapper"
                    :class="activeTab === index ? 'active' : ''"
                    @click="handleChangePlan(plan.mapping, index)"
                >
                    <div class="w-50 d-flex justify-content-start">
                        <span class="label-500" v-html="plan.name"></span>
                    </div>
                    <div class="w-50 d-flex justify-content-end">
                        <a
                            href="javascript:void(0)"
                            style="font-weight: 500; font-size: 14px; color: #8d58e0"
                            class="label-500"
                        >
                            {{ translations["View plan"] }}
                            <i
                                class="far"
                                :class="activeTab === index ? 'fa-chevron-up' : 'fa-chevron-down'"
                            ></i>
                        </a>
                    </div>
                </div>
                <Vue3SlideUpDown
                    :model-value="activeTab === index"
                    :duration="200"
                    class="sub-plans"
                >
                    <Loading
                        v-if="planLoader"
                        :loading="planLoader"
                        style="margin-left: 45%; margin-top: 5%"
                    />
                    <div
                        v-if="planOptions.plans && planOptions.plans.length !== 0"
                        v-for="(option, index) in planOptions.plans"
                        :key="index"
                        class="option-plans"
                    >
                        <div class="radio-wrapper mt-1">
                            <b-form-radio
                                v-model="data.selected_plan"
                                :name="option.display_name"
                                :value="option.id"
                            >
                                <div class="d-flex flex-row flex-wrap align-items-center gap-1">
                  <span
                      class="label-600 text-wrap"
                      style="margin-top: 1px"
                      v-html="option?.name?.en || option?.name?.bg"
                  >
                  </span>
                                    <span
                                        v-if="option.save_plan"
                                        style="font-size: 14px"
                                        class="cc-badge-status cc-tag-status--enabled"
                                        v-html="
                      $trp(translations['Save € {price}'], { price: option.save_plan })
                    "
                                    ></span>
                                </div>
                            </b-form-radio>
                        </div>
                        <div class="d-flex flex-grow-1 justify-content-end">
                            <span class="label-500 text-nowrap" v-html="priceTag(option)"> </span>
                        </div>
                    </div>
                </Vue3SlideUpDown>
            </div>
        </b-col>
    </b-row>

    <b-row class="justify-content-center p-2" v-if="data.apps && data.apps.length !== 0">
        <b-col class="col-10 col-md-8 p-0">
            <b-card class="p-2">
        <span
            class="label-600 d-block mb-3"
            v-html="translations['Recomended applications']"
        ></span>

                <div v-for="(app, index) in data.apps" :key="index">
                    <div class="d-flex flex-row align-items-center">
                        <div class="checkbox-wrapper">
                            <b-form-checkbox
                                v-model="data.selected_apps"
                                :id="app.id"
                                :key="app.id"
                                :value="app.id"
                                :name="`app-id-${app.id}`"
                            >
                            </b-form-checkbox>
                        </div>
                        <b-row class="ms-0 justify-content-between align-items-center w-100">
                            <b-col class="d-flex align-items-center gap-2">
                                <img width="40" :src="app.icon" :alt="app.name" />
                                <span v-html="app.name"> </span>
                            </b-col>
                            <b-col class="text-right" v-if="app.price">
                                <span class="label-500" v-html="`€ ${app.price}`"></span>
                                <br />
                                <small
                                    style="font-size: 13px; line-height: 20px; color: #7a7d84"
                                    v-html="
                    $trp(translations['With discount of € {sum}'], { sum: app.save_app })
                  "
                                ></small>
                            </b-col>
                        </b-row>
                    </div>

                    <hr v-if="index < data.apps.length - 1" style="margin: 24px 0" />
                </div>
            </b-card>
        </b-col>
    </b-row>
</template>

<script>
import ActiveSwitch from "@components/Form/ActiveSwitch";
import RadioComponent from "@components/Form/RadioComponent";
import axios from "axios";
import Loading from "@components/Loading";
import { Vue3SlideUpDown } from "vue3-slide-up-down";

export default {
    name: "CreateStep4",
    components: {
        ActiveSwitch,
        RadioComponent,
        Loading,
        Vue3SlideUpDown,
    },
    props: {
        data: {
            required: true,
            default: {},
        },
        errorRequired: {
            required: false,
            default: false,
            type: Boolean,
        },
    },
    mounted() {
        this.settings = this.data;
        this.selected_plan = this.settings.selected_plan;
        this.newSettings.plan_id = this.selected_plan;
    },
    data() {
        return {
            loading: true,
            responseErrors: {},
            settings: {},

            planLoader: false,
            planOptions: {},

            newSettings: {
                is_mapped: false,
                plan_id: null,
                app: [],
            },
            translations: {
                "Recomended applications": this.$t("Recomended applications"),
                "With discount of € {sum}": this.$t("With discount of € {sum}"),
                "View plan": this.$t("View plan"),
                month: this.$t("month"),
                "{period} year": this.$t("{period} year"),
                "{period} years": this.$t("{period} years"),
                "You have not selected a subscription plan": this.$t(
                    "You have not selected a subscription plan"
                ),
                "Save € {price}": this.$t("Save € {price}"),
            },
            activeTab: null,
            options: [],
            apps_options: [],
            new_options: [],
            plans: [],
            selected_plan: null,
            selectionMade: false,
            showNewOptions: false,
        };
    },
    methods: {
        handleChangePlan(value, index) {
            this.planLoader = true;

            if (this.activeTab === index) {
                this.activeTab = null;
                return;
            }

            this.activeTab = index;

            this.data.is_mapped = value;
            this.data.plan_id = value;

            this.planOptions = [];
            axios
                .get(`/admin/api/multilang/get-plan/${value}`)
                .then((res) => {
                    this.planOptions = res.data;
                })
                .catch((err) => {
                    // Handle error
                })
                .finally(() => {
                    this.planLoader = false;
                });
        },
        priceTag(option) {
            let isMonth = option.billing_months === 1;

            let billingPeriod = isMonth ? 1 : +option.billing_months / 12;

            let period;
            if (isMonth) {
                period = this.translations["month"];
            } else {
                period = this.$trp(
                    this.translations[billingPeriod === 1 ? "{period} year" : "{period} years"],
                    { period: billingPeriod }
                );
            }
            return `€ ${option.price_without_vat_input} / ${period}`;
        },
    },
};
</script>

<style>
@import url("./../../scss/style.scss");
</style>
