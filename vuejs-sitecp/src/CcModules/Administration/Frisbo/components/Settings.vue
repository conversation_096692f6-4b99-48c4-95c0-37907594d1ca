<template>
    <SettingsBox
            v-model:settings="config"
            v-model:boxes="boxes"
            v-model:setting-open="settingOpen"
            box-key="frisbo"
            :response-errors="errors"
            :live-watch="['connect.password', 'connect.email']"
    >
        <template #selectWarehouse>
            <SelectWithAjax
                    v-model:val="config.organization_id"
                    @selected-item="() => (config.warehouse_id = null, config.channel_id = null)"
                    :label="translations['Organization']"
                    apiUrl="/admin/api/frisbo/organizations"
                    :resolve-on-load="true"
                    :searchable="true"
                    :request-on-search="true"
                    :can-clear="true"
                    :no-margin="true"
            />
            <SelectWithAjax
                    v-model:val="config.warehouse_id"
                    :label="translations['Warehouse']"
                    :apiUrl="warehouseUrl"
                    :resolve-on-load="true"
                    :is-select-disabled="!config.organization_id"
                    :searchable="true"
                    :request-on-search="true"
                    :can-clear="true"
                    :no-margin="true"
            />
            <SelectWithAjax
                    v-model:val="config.channel_id"
                    :label="translations['Channel']"
                    :apiUrl="channelUrl"
                    :resolve-on-load="true"
                    :is-select-disabled="!config.organization_id"
                    :searchable="true"
                    :request-on-search="true"
                    :can-clear="true"
                    :no-margin="true"
            />
        </template>
    </SettingsBox>
</template>

<script>
import {markRaw} from "vue";
import SettingDescription from "@components/SettingDescription";
import SettingsBox from "@components/SettingsBox/SettingsBox";
import SelectWithAjax from "@components/Form/SelectWithAjax";

import ValidateAndSave from "./ValidateAndSave";
import ApplicationSettings from "../../../Apps/components/ApplicationSettings.vue";

export default {
    name: "FrisboSettings",
    components: {
        ApplicationSettings,
        SettingDescription,
        SettingsBox,
        SelectWithAjax,
    },

    props: {
        app: {
            required: false,
            default: {},
        },
        settings: {
            type: Object,
            default: {},
            required: true,
        },
        responseErrors: {
            type: Object,
            default: {},
            required: true,
        },
        disableSave: {
            required: true,
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            config: this.settings,
            settingOpen: this.disableSave,
            errors: this.responseErrors,
            translations: {
                "Email address": this.$t("Email address"),
                Password: this.$t("Password"),
                Warehouse: this.$t("Warehouse"),
                Channel: this.$t("Channel"),
                Organization: this.$t("Organization"),
                "Sending an order with status": this.$t("Sending an order with status"),
                "Automatic order dispatch": this.$t("Automatic order dispatch"),
                "New order": this.$t("New order"),
                "Paid": this.$t("Paid"),

            },
            password: this.settings.password,
            email: this.settings.email,
            isChangedCredentials: false,
            credentials: {
                email: this.settings.email,
                password: this.settings.password,
            },
            boxes: [],
        };
    },
    created() {
        this.setupBoxes();
    },
    mounted() {
        this.helpBoxes(this.boxes);
    },
    computed: {
        warehouseUrl() {
            return `/admin/api/frisbo/warehouses/${this.config.organization_id}`;
        },
        channelUrl() {
            return `/admin/api/frisbo/channels/${this.config.organization_id}`;
        }
    },
    methods: {
        setupBoxes() {
            this.boxes = [
                {
                    key: "connect",
                    group: "frisbo",
                    title: "frisbo connect",
                    titleHelp: {
                        label: "frisbo connect",
                        parameters: [],
                    },
                    infoTitle: "frisbo connect",
                    infoTitleHelp: null,
                    infoDescription: "frisbo connect",
                    editMethod: this.app.is_configured ? "slide" : "inline",
                    lockEditMethod: this.app.is_configured
                        ? this.isChangedCredentials
                        : true,
                    hideActions: true,
                    isVisible: true,
                    hr: "hide",
                    credentials: true,
                    fields: [
                        {
                            key: "email",
                            type: "string",
                            label: "Email address",
                            disabled: false,
                            readonly: false,
                            help: null,
                            inputType: "string",
                            credentials: true,
                        },
                        {
                            key: "password",
                            type: "string",
                            label: "Password",
                            disabled: false,
                            readonly: false,
                            help: null,
                            inputType: "string",
                            credentials: true,
                        },
                        {
                            key: "html",
                            type: "html",
                            component: markRaw(ValidateAndSave),
                            props: {
                                isHidden: this.isChangedCredentials
                                    ? false
                                    : this.app.is_configured,
                                setupBoxes: this.setupBoxes,
                            },
                        },
                    ],
                },
                {
                    key: "general_settings",
                    group: "frisbo",
                    title: "frisbo settings",
                    titleHelp: {
                        label: "frisbo settings",
                        parameters: [],
                    },
                    infoTitle: "frisbo settings",
                    infoTitleHelp: null,
                    infoDescription: "frisbo settings",
                    editMethod: "inline",
                    lockEditMethod: this.app.is_configured
                        ? this.isChangedCredentials
                        : true,
                    isVisible: this.app.is_configured
                        ? !this.isChangedCredentials
                        : false,
                    fields: [
                        {
                            type: "slot",
                            slotName: "selectWarehouse",
                        },
                    ],
                },
                {
                    key: "order_settings",
                    group: "frisbo",
                    title: "frisbo settings",
                    titleHelp: {
                        label: "frisbo settings",
                        parameters: [],
                    },
                    infoTitle: "frisbo settings",
                    infoTitleHelp: null,
                    infoDescription: "frisbo settings",
                    editMethod: "inline",
                    lockEditMethod: this.app.is_configured
                        ? this.isChangedCredentials
                        : true,
                    isVisible: this.app.is_configured
                        ? !this.isChangedCredentials
                        : false,
                    fields: [
                        {
                            key: "automate_send",
                            type: "switch",
                            label: "Automatic order dispatch",
                            disabled: false,
                            readonly: false,
                            help: null,
                            trueValue: 1,
                            falseValue: 0,
                        },
                        {
                            key: "order_status",
                            type: "select",
                            label: "Sending an order with status",
                            disabled: false,
                            readonly: false,
                            help: null,
                            dependField: "automate_send",
                            dependValue: [1],
                            options: [
                                {id: "new_order", name: "New order"},
                                {id: "paid", name: "Paid"}
                            ],
                        },
                        // {
                        //     key: "create_products",
                        //     type: "switch",
                        //     label: "Create a product in Frisbo",
                        //     disabled: false,
                        //     readonly: false,
                        //     help: {
                        //         label:
                        //             "If the product does not exist in Frisbo, create it",
                        //     }
                        // },
                    ],
                },
            ];
        },
        handleIsBoxVisible() {
            this.boxes.map((box) => {
                if (["settings"].indexOf(box.key) > -1) {
                    box.lockEditMethod = this.app.is_configured
                        ? this.isChangedCredentials
                        : true;
                    box.isVisible = this.app.is_configured
                        ? !this.isChangedCredentials
                        : false;
                }

                if (box.key === "connect") {
                    box.lockEditMethod = this.app.is_configured
                        ? this.isChangedCredentials
                        : true;
                    box.fields.map((field) => {
                        if (field.key === "html") {
                            field.props.isHidden = this.isChangedCredentials
                                ? false
                                : this.app.is_configured;
                        }
                    });
                }
            });
        },
    },
    watch: {
        config: {
            deep: true,
            handler(val) {
                this.$emit("update:settings", val);
            },
        },
        settingOpen(val) {
            this.$emit("update:disableSave", val);
        },
        settings: {
            deep: true,
            handler(val) {
                this.config = val;
            },
        },
        "config.password": {
            handler(val) {
                if (this.password) {
                    this.isChangedCredentials = val !== this.password;
                } else {
                    this.password = val;
                }

                this.credentials.password = val;

                this.handleIsBoxVisible();
            },
        },
        "config.email": {
            handler(val) {
                if (this.email) {
                    this.isChangedCredentials = val !== this.email;
                } else {
                    this.email = val;
                }

                this.credentials.email = val;

                this.handleIsBoxVisible();
            },
        },
        "settings.organization_id"(val) {

        },
        responseErrors: {
            deep: true,
            handler(val) {
                this.errors = val;
            },
        },
        errors: {
            deep: true,
            handler(val) {
                this.$emit("update:responseErrors", val);
            },
        },
    },
    emits: ["update:settings", "update:responseErrors", "update:disableSave"],
};
</script>
