<template>
    <div class="d-flex gap-3 align-items-center justify-content-end w-100 product-options-switch-delete-action">
        <DeleteComponent v-model:loader="submitLoader" :deleteFunction="removeOption"/>
    </div>
</template>

<script>
import {toast} from "@js/toast";
import axios from "axios";
import DeleteComponent from '@components/DeleteComponent'

export default {
    name: "DeleteOption",
    components: {
        DeleteComponent,
    },
    props: {
        data: {
            type: Object,
            required: true,
        },
        column: {
            type: Object,
            required: false,
        },
    },
    data() {
        return {
            submitLoader: false,
            translations: {
                "Option was deleted successfully": this.$t("Option was deleted successfully"),
                "Option was NOT deleted successfully": this.$t(
                    "Option was NOT deleted successfully"
                ),
            },
        };
    },
    methods: {
        async removeOption() {
            this.submitLoader = true;

            try {
                await axios.get(`/admin/api/product-options/delete/${this.data.id}`)
                this.data.removeRow(this.data.id);
            } catch (err) {
                this.$errorResponse(err)
            } finally {
                this.submitLoader = false;
            }
        },
    },
};
</script>
<style scoped>
@media (max-width: 768px) {
    div.product-options-switch-delete-action {
        width: auto !important;
    }
}
</style>



