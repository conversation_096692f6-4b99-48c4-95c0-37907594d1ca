<template>
    <b-container class="conteiner-medium">
        <data-table
                app-name="n18_audit"
                :enable-mobile="true"
                v-bind="this.table"
                v-model="selected"
                :get-data="getData"
                v-model:query="query"
                :filters="true"
                @pagination-change-page="paginate"
        >
            <template #button>
                <AddPeriod/>
            </template>
            <template v-if="table.data.data.length === 0" #noResult>
                <AddPeriod/>
            </template>
            <TableActions
                    v-model="selected"
                    app-key="n18_audit"
                    @success="handleActions"
                    :enable-mobile="true"
            />
        </data-table>
    </b-container>
</template>

<script>
import {markRaw} from "vue";
import List from "./../List";
import DataTable from "@components/Table";
import AddPeriod from "./Helpers/AddPeriod";
import Name from "./TableColums/Name";
import Button from "./TableColums/Button";
import Remove from "./TableColums/Remove";
import TableActions from "@components/TableActions";
import moment from "moment/moment";

export default {
    components: {
        DataTable,
        TableActions,
        Name,
        Button,
        Remove,
        AddPeriod,
    },
    data() {
        return {
            table: {},
            loader: true,
            page: this.$route.query?.page || 1,
            perPage: this.$route.query?.perpage || 25,
            query: this.$route.query,
            model: new List(),
            rows: [],
            selected: [],
            translations: {
                Name: this.$t("Name"),
                Period: this.$t("Period"),
                "Last modified": this.$t("Last modified"),
                Url: this.$t("Url"),
                "No results found": this.$t("No results found"),
                Apps: this.$t("Apps"),
                "XML Files": this.$t("XML Files"),
                "ID #": this.$t("ID #"),
            },
        };
    },
    async created() {
        this.setupTable();
        // console.log(this.serverSettings('format.date'))
        // console.log(this.serverSettings('format.time'))
        // console.log(this.serverSettings('format.dateTime'))
    },
    methods: {
        handleActions(value) {
            if (this.table.data.data.length === 0 && this.page > 1) {
                this.page -= 1;
                this.getData();
            } else
                this.table.data.data = this.table.data.data.filter(
                    (x) => !this.selected.includes(x.id)
                );
            this.selected = [];
        },
        setupTable() {
            this.table = {
                data: {
                    data: [],
                },
                isLoading: this.loading,
                defaultSorting: [{ key: "id", sortingMode: "desc" }],
                columns: [
                    {
                        column: "id",
                        key: "id",
                        sortable: true,
                        title: this.translations["ID #"],
                    },
                    {
                        column: "name",
                        key: "name",
                        sortable: true,
                        title: this.translations["Name"],
                        component: markRaw(Name),
                        isMain: true,
                    },
                    {
                        column: "period",
                        key: "period",
                        sortable: true,
                        title: this.translations["Period"],
                        format: (data) => {
                            const startOfMonth = moment(data.period).startOf('month').format(this.serverSettings("format.date"));
                            const endOfMonth = moment(data.period).endOf('month').format(this.serverSettings("format.date"));

                            return `${startOfMonth} - ${endOfMonth}`;
                        },
                    },
                    {
                        column: "created_at",
                        key: "created_at",
                        sortable: true,
                        title: this.translations["Last modified"],
                        format: (data) => {
                            return moment(data.created_at).format(
                                    this.serverSettings("format.dateTime")
                                );
                        },
                    },
                    {
                        column: "actions",
                        key: "download",
                        sortable: false,
                        // title: this.translations["Url"],
                        title: " ",
                        component: markRaw(Button),
                    },
                    {
                        column: "actions",
                        key: "remove",
                        sortable: false,
                        title: " ",
                        component: markRaw(Remove),
                    },
                ],
            };
        },
        async getData(paginate) {
            try {
                this.table.isLoading = true;

                this.model.where(this.query);
                if (
                    Object.keys(this.query).toString().includes("filters") &&
                    !paginate
                ) {
                    this.page = 1;
                }
                this.table.data = await this.model.paginate(this.query?.page || this.page, this.query?.perpage || this.perPage);
                this.table.data.data = this.table.data.data.map((x) => {
                    return {
                        ...x,
                        func: this.getData,
                        removeRow: this.removeRow,
                    };
                });
                this.$router.push({
                    query: {...this.$route.query, page: this.table.data.current_page},
                });
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.loader = false;
                this.table.isLoading = false;
            }
        },
        removeRow(id) {
            if (this.table.data.data.length === 0 && this.page > 1) {
                this.page -= 1;
                this.getData();
                return;
            }
            this.table.data.data = this.table.data.data.filter((x) => x.id !== id);
        },
        async paginate(page) {
            this.page = page;
            await this.getData("paginate");
        },
    },
};
</script>
