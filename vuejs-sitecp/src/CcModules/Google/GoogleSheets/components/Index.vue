<template>
    <Application
        v-model="app"
        v-model:settings="settings"
        app-key="google_sheets"
        :support-config="true"
        :has-active-switch="false"
        :post-settings="postSettings"
    >
        <b-container class="container-medium pb-3">
            <ActiveStatusTopSection
                :application="app"
                :show-activate-button="false"
                :is-hr="false"
            />

            <Tabs
                :tabs="tabs"
                v-model="activeTab"
            ></Tabs>

            <router-view
                v-model="app"
                app-key="google_sheets"
                v-model:t-boxes="boxes"
                v-model:settings="settings"
                v-model:auth="auth"
                v-model:allowed-columns-options="allowed_columns_options"
                v-model:category="category"
                v-model:product="product"
                v-model:vendor="vendor"
                :responseErrors="responseErrors"
            ></router-view>

            <SubmitChanges
                v-if="auth && $route.name === 'apps.google_sheets.settings'"
                v-model:submit-loader="submitLoader"
                v-model="settings"
                :response-errors="responseErrors"
                :save-func="saveSettings"
            />
        </b-container>
    </Application>
</template>

<script>
import {buildFormData} from "@js/shippingHelpers";

import Application from "./../../../Apps/components/Application";
import ActiveStatusTopSection from "@components/ActiveStatusTopSection";
import Tabs from "@components/Apps/Help/Tabs";
import SubmitChanges from "@components/SubmitChanges";
import axios from "axios"
import {toast} from "@js/toast";

export default {
    name: "Index",
    components: {
        Application,
        Tabs,
        ActiveStatusTopSection,
        SubmitChanges
    },
    props: {
        tBoxes: {
            type: Object,
            default: {}
        }
    },
    data() {
        return {
            app: {},
            isLoaded: false,
            responseErrors: {},
            activeTab: '',
            settings: {
                spreadsheet_id: "",
                discount_id: "",
                worksheet_name: "",
                filter_group: "all",
                filter_group_value: [],
            },
            boxes: this.tBoxes,
            auth: false,
            spreadSheetUrl: null,
            allowed_columns_options: [],
            category: [],
            product: [],
            vendor: [],
            submitLoader: false,
            tabs: [],
            translations: {
                "Overview": this.$t("Overview"),
                "Tasks": this.$t("Tasks"),
                "Settings": this.$t("Settings"),
                "Open the spreadsheet in Google Spreadsheets": this.$t("Open the spreadsheet in Google Spreadsheets"),
                "Upload to Google Sheets": this.$t("Upload to Google Sheets"),
                "Download from Google Sheets": this.$t("Download from Google Sheets"),
                "Successfully updated settings": this.$t('Successfully updated settings'),
            }
        }
    },
    mounted() {
        this.helpBoxes(this.boxes);

        this.tabs = [
            {
                to: {name: 'apps.google_sheets.overview'},
                label: this.translations["Overview"]
            },
            {
                to: {name: 'apps.google_sheets.settings'},
                label: this.translations["Settings"]
            },
        ]

        if (this.auth) {
            this.tabs.push({
                to: {name: 'apps.google_sheets.tasks'},
                label: this.translations["Tasks"]
            })
        }
    },
    methods: {
        postSettings(config) {
            this.auth = config.oauth;

            this.settings = {
                spreadsheet_id: config.spreadsheet_id,
                spreadsheet_url: config.spreadsheet_url,
                worksheet_name: config.worksheet_name,
                discount_id: config.discount_id ? config.discount_id : "",
                allowed_columns: config.allowed_columns && config.allowed_columns?.length > 0 ? config.allowed_columns : Object.values(config.default_columns),
                filter_group: config.filter_group ? config.filter_group : "all",
                filter_group_value: config.filter_group_value ? config.filter_group_value : [],
            }

            config.allowed_columns_all.map(item => {
                this.allowed_columns_options.push({id: item, name: item});
            });

            if (config.filter_group !== 'all') {
                this[config.filter_group] = config.filter_value;
            }
            this.spreadSheetUrl = config.spreadsheet_url;
            // this.$emit('spreadsheet-url', config.spreadsheet_url);
        },
        async saveSettings() {
            this.submitLoader = true;
            this.responseErrors = {}

            let formData = new FormData();
            buildFormData(formData, this.settings);

            axios
                .post("/admin/api/google_sheets/settings", formData)
                .then((res) => {
                    if (res.data.status == "error") {
                        toast.error(res.data.msg);
                        return;
                    }
                    // toast.success(res.data.msg);
                    // toast.success(this.translations['Successfully updated settings']);
                })
                .catch((err) => {
                    this.$errorResponse(err)
                })
                .finally(() => {
                    this.submitLoader = false;
                });
        },
    },
    computed: {
        computedSettings: {
            get() {
                return {
                    ...this.settings,
                }
            },
            set(value) {
                this.$emit("update:settings", value);
            },
        },
    },
    watch: {
        auth(val) {
            if(val) {
                this.tabs = [
                    {
                        to: {name: 'apps.google_sheets.overview'},
                        label: this.translations["Overview"]
                    },
                    {
                        to: {name: 'apps.google_sheets.settings'},
                        label: this.translations["Settings"]
                    },
                    {
                        to: {name: 'apps.google_sheets.tasks'},
                        label: this.translations["Tasks"]
                    }
                ]
            } else {
                this.tabs = [
                    {
                        to: {name: 'apps.google_sheets.overview'},
                        label: this.translations["Overview"]
                    },
                    {
                        to: {name: 'apps.google_sheets.settings'},
                        label: this.translations["Settings"]
                    },
                ]
            }
        },
    }
}
</script>

<style lang="scss">
@use './../scss/style.scss' as *;
</style>
