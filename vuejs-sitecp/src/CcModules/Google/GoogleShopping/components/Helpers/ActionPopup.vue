<template>
    <b-modal
        v-model="modal"
        :no-header="true"
        :no-footer="true"
        :no-close-on-backdrop="submitLoader"
    >
        <b-row class="popup-content">
            <b-col class="col-12 mb-4 d-flex">
                <span class="label-500-16px">
                    {{ tableActionsData.title }}
                </span>
                <a
                    class="ms-auto"
                    href="javascript:void(0);"
                    @click="
                        () => {
                            if (!submitLoader) {
                                modal = false;
                            }
                        }
                    "
                >
                    <i
                        style="font-size: 20px; color: #9b9ca3; font-weight: 300"
                        class="fas fa-times"
                    ></i>
                </a>
            </b-col>
            <b-col class="col-12">
                <span class="text-400-secondary">
                    {{ tableActionsData.description }}
                </span>
            </b-col>
            <template
                v-if="
                    [
                        'include_destination',
                        'exclude_destination',
                    ].includes(tableActionsData?.key)
                "
            >
                <b-col class="col-12">
                    <SelectWithAjax
                        v-model:val="selectData"
                        :column-style="true"
                        :cols-width="12"
                        :apiUrl="tableActionsData.url"
                        :searchable="true"
                        :request-on-search="true"
                        :isSelectDisabled="submitLoader"
                        :mode="'single'"
                    />
                </b-col>
            </template>
            <b-col>
                <hr class="mx-0 mt-0 mb-3" />
            </b-col>
            <b-col class="col-12 d-flex align-items-center gap-3 justify-content-end">
                <button class="btn btn-white" @click="modal = false">
                    {{ translations.Cancel }}
                </button>
                <button class="btn btn-primary" :disabled="submitLoader" @click="submit">
                    <b-spinner v-if="submitLoader" small></b-spinner>
                    {{ translations.Save }}
                </button>
            </b-col>
        </b-row>
    </b-modal>
</template>

<script>
import { toast } from "@js/toast";

import SelectWithAjax from "@components/Form/SelectWithAjax";
import axios from "axios";

export default {
    name: "ActionPopup",
    components: {
        SelectWithAjax,
    },
    props: {
        modelValue: {
            required: false,
            type: Boolean,
            default: false,
        },
        tableActionsData: {
            type: Object,
            default: () => ({}),
        },
        ids: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            loading: false,
            modal: this.modelValue,
            selectData: [],
            numValue: 0,
            submitLoader: false,
            responseErrors: {},

            translations: {
                Cancel: this.$t("Cancel"),
                Save: this.$t("Save"),
                "Updating destination in progress, please wait": this.$t(
                    "Updating destination in progress, please wait"
                ),
            },
        };
    },
    computed: {
        pathEnum() {
            return {
                include_destination: "/admin/api/google_shopping/update-destination/include",
                exclude_destination: "/admin/api/google_shopping/update-destination/exclude",
            };
        },
        messageEnum() {
            return {
                include_destination: this.translations["Updating destination in progress, please wait"],
                exclude_destination: this.translations["Updating destination in progress, please wait"],
            };
        },
        errorMsg() {
            return (
                this.translations["Error occurred while adding / remove destination"]
            );
        },
    },
    methods: {
        async submit() {
            this.responseErrors = {};
            this.submitLoader = true;

            try {
                await axios.post(this.pathEnum[this.tableActionsData.key], {
                    ids: this.ids,
                    value: this.selectData,
                }).then((response) => {
                    if (this.tableActionsData.key === "include_destination") {
                        this.$emit("success", "include_destination", response.data);
                    } else if(this.tableActionsData.key === 'exclude_destination') {
                        this.$emit("success", "exclude_destination", response.data);
                    }
                });
                toast.success(this.messageEnum[this.tableActionsData.key]);
                this.$emit("update:ids", []);
                this.modal = false;
            } catch (error) {
                this.$errorResponse(error);
            } finally {
                this.submitLoader = false;
            }
        },
    },
    watch: {
        modelValue(val) {
            this.modal = val;
        },
        modal(val) {
            this.$emit("update:modelValue", val);
            if (!val) {
                this.selectData = [];
                this.sort_order = 0;
                this.responseErrors = {};
                this.$emit("update:tableActionsData", {});
            }
        },
    },
    emits: ["update:modelValue", "update:tableActionsData", "update:ids", "success"],
};
</script>
