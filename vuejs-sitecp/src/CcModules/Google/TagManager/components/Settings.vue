<template>
    <SettingsBox
        v-model:settings="config"
        v-model:boxes="boxes"
        box-key="google_tags"
        :response-errors="responseErrors"
    />
</template>
<script>
import SettingsBox from "@components/SettingsBox/SettingsBox";


export default {
    components: {
        SettingsBox
    },
    props: {
        app: {
            required: false,
            default: {},
        },
        settings: {
            required: false,
            default: {},
        },
        responseErrors: {
            required: false,
            default: {},
        },
    },
    data() {
        return {
            boxes: [],
            config: this.settings,
            translations: {
                'Insert the code provided by Google Tag Manager': 'Insert the code provided by Google Tag Manager',
                'The Google Tag ID is invalid': 'The Google Tag ID is invalid',
                'Please add your Google Tag ID': 'Please add your Google Tag ID',
                'Google tags settings': 'Google tags settings',
            }
        };
    },
    created() {
        this.setupBoxes();
    },
    watch: {
        config: {
            deep: true,
            handler(val) {
                this.$emit('update:settings', val)
            }
        },
        settings: {
            deep: true,
            handler(val) {
                this.config = val;
            },
        },
    },
    methods: {
        setupBoxes() {
            this.boxes = [
                {
                    key: "settings",
                    group: "google_tags",
                    title: this.translations["Google tags"],
                    titleHelp: {
                        label: "Google tags settings",
                        parameters: [],
                    },
                    infoTitle: "Google tags settings",
                    infoTitleHelp: null,
                    infoDescription: "Google tags settings",
                    editMethod: "inline",
                    isVisible: true,
                    hr: 'hide',
                    fields: [
                        {
                            key: "code",
                            type: "string",
                            label: 'Insert the code provided by Google Tag Manager',
                            disabled: false,
                            readonly: false,
                            placeholder: "GTM-XXXXXXX",
                        }
                    ],
                },
            ];
        }
    },
};
</script>