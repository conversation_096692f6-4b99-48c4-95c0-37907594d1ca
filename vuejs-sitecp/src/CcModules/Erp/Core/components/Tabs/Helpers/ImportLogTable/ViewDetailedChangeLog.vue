<template>
    <b-modal
        class="modal-right"
        v-model="openModal"
        @close="openModal = false"
        :no-footer="true"
        header-class="edit-settings-modal-header"
        body-class="edit-settings-modal-content"
        :size="!openDetails ? 'xl' : 'xll'"
    >

        <template #header>
            <div class="w-100 d-flex align-items-start justify-content-between">
                <p class="settings-modal-title m-0">
                    {{ $trp(translations['History: {name}'], {name: history.name}) }}
                </p>
                <div class="d-flex justify-content-end align-items-center gap-2">
                    <a class="me-2 btn btn-primary text-nowrap" v-if="data.data_original"
                       href="javascript:void(0)" @click.prevent="openDetails = true">
                        {{ translations['Show details'] }}
                    </a>
                    <a @click.prevent="openModal = false" class="btn btn-white" href="javascript:void(0)">
                        {{ translations.Close }}
                    </a>
                </div>
            </div>
        </template>

        <Loading
            v-if="loading"
            :loading="loading"
            class="app-loader-center"
        />
        <template v-else>
            <div class="alert alert-info">
                {{translations['Application']}}: <strong>{{app.name}}</strong>;
                {{translations['Action']}}: <strong>{{actionLabel}}</strong>;
                {{translations['Date']}}: <strong>{{date}}</strong>;
                {{translations['Compare by']}}: <strong>{{guessCompareKey(history.compare_key)}}</strong>;
                {{translations['Value of']}}: <strong>{{[...new Set(history.compare_value || [])].join(', ')}}</strong>;
            </div>

            <template v-if="['create', 'update'].includes(data.action)" class="cc-table w-100">
                <div v-if="data.log" class="data-table-table rounded-2 overflow-hidden">
                    <table class="table">
                        <thead>
                        <tr>
                            <th>{{translations.Key}}</th>
                            <th>{{translations.Old}}</th>
                            <th>{{translations.New}}</th>
                        </tr>
                        </thead>
                        <template v-for="(value, key) in mainProductInfo">
                            <tbody>
                            <template v-if="isAttributeComponent(key)">
                                <component :is="getAttributeComponent(key)" :value="value" :app="app" :key="generateKey()" />
                            </template>
                            <template v-else>
                                <template v-for="(value, key) in mainProductInfo">
                                    <tbody>
                                    <template v-if="isAttributeComponent(key)">
                                        <component :is="getAttributeComponent(key)" :value="value" :app="app"
                                                   :key="generateKey()"/>
                                    </template>
                                    <template v-else>
                                        <template v-for="(v, k) in value">
                                            <tr>
                                                <td>{{ getLabel(`log.${k}`) }}</td>
                                                <td>{{ getValue(v.old, k) }}</td>
                                                <td>{{ getValue(v.new, k) }}</td>
                                            </tr>
                                        </template>
                                    </template>
                                    </tbody>
                                </template>
                            </template>
                            </tbody>
                        </template>
                        <template v-for="(value, key) in relationsProductInfo">
                            <tbody>
                            <component :is="key" :value="value" :app="app"></component>
                            </tbody>
                        </template>
                    </table>
                </div>
                <div class="data-table-table rounded-2 overflow-hidden" v-else>
                    <template v-if="data.updates?.length > 0">
                        <table class="table">
                            <tbody>
                            <tr v-for="(value, key) in data.updates">
                                <td>
                                    {{key}}
                                </td>
                                <td>
                                    {{value}}
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </template>
                    <template v-if="data.variants?.length > 0">
                        <table class="table">
                            <tbody>
                            <template v-for="variant in data.variants">
                                <tr v-for="(value, key) in variant">
                                    <th class="pt-1 pb-1" v-if="key === 'name'"  colspan="2">
                                        {{value}}
                                    </th>
                                    <template v-else>
                                        <td>
                                            {{key}}
                                        </td>
                                        <td>
                                            {{value}}
                                        </td>
                                    </template>
                                </tr>
                            </template>
                            </tbody>
                        </table>
                    </template>
                </div>
            </template>
            <template v-else>
                <div v-if="['error'].includes(data?.action) && data?.error" class="alert alert-danger" v-html="data.error"></div>
                <div v-else class="alert alert-warning" >{{translations['No action']}}</div>
            </template>
        </template>

    </b-modal>

    <b-modal
        class="modal-right"
        v-model="openDetails"
        @close="openDetails = false"
        hide-footer="true"
        header-class="edit-settings-modal-header"
        body-class="edit-settings-modal-content"
        size="xl"
    >

        <template #header>
            <div class="w-100 d-flex align-items-start justify-content-between">
                <p class="settings-modal-title m-0">
                    {{ $trp(translations['Received information for: {name}'], {name: history.name}) }}
                </p>
                <a @click.prevent="openDetails = false" class="btn btn-white" href="javascript:void(0)">
                    {{ translations.Close }}
                </a>
            </div>
        </template>

        <vue-json-pretty :data="data.data_original" />

    </b-modal>

</template>

<script>
import HistoryLog from "./../../../../js/HistoryLog";
import VueJsonPretty from 'vue-json-pretty';
import moment from 'moment';
import 'vue-json-pretty/lib/styles.css';
import Loading from "@components/Loading";
import dotGet from "@js/dotGet";
import {moneyFormat, moneyFormatNoSign} from "@js/numberFormatters";
//rows
import variants from "./../HistoryTable/Rows/relations/variants";
import brand_model from "./../HistoryTable/Rows/relations/brand_model";
import bundle_products from "./../HistoryTable/Rows/relations/bundle_products";
import categories from "./../HistoryTable/Rows/relations/categories";
import linked_products from "./../HistoryTable/Rows/relations/linked_products";
import metaData from "./../HistoryTable/Rows/relations/meta";
import tags from "./../HistoryTable/Rows/relations/tags";
import discounts from "./../HistoryTable/Rows/relations/discounts";
import featured from "./../HistoryTable/Rows/attributes/featured";
import is_hidden from "./../HistoryTable/Rows/attributes/is_hidden";
import p1 from "./../HistoryTable/Rows/attributes/p1";
import p2 from "./../HistoryTable/Rows/attributes/p2";
import p3 from "./../HistoryTable/Rows/attributes/p3";
import price_from from "./../HistoryTable/Rows/attributes/price_from";
import price_to from "./../HistoryTable/Rows/attributes/price_to";

export default {
    components: {
        VueJsonPretty,
        Loading,
        variants,
        brand_model,
        bundle_products,
        categories,
        linked_products,
        metaData,
        tags,
        discounts,
        featured,
        is_hidden,
        p1,
        p2,
        p3,
        price_from,
        price_to,
    },
    props: {
        history: {
            required: true,
            type: Object,
            default: {},
        },
        app: {
            required: true,
            type: Object,
            default: {},
        },
        modelValue: {
            required: true,
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            translations: {
                'Application': this.$t("Application"),
                'Action': this.$t("Action"),
                'Date': this.$t("Date"),
                'Close': this.$t("Close"),
                'Compare by': this.$t("Compare by"),
                'Value of': this.$t("Value of"),
                'History: {name}': this.$t("History: {name}", {name: '{name}'}),
                'Received information for: {name}': this.$t("Received information for: {name}", {name: '{name}'}),
                "No action": this.$t("No action"),
                "Create": this.$t("Create"),
                "Update": this.$t("Update"),
                "Error": this.$t("Error"),
                "Show details": this.$t("Show details"),
                "Key": this.$t("Key"),
                "Old": this.$t("Old"),
                "New": this.$t("New"),
                'SKU': this.$t('SKU'),
                'Barcode': this.$t('Barcode'),
                "External ID": this.$t("External ID"),
                "System ID": this.$t("System ID"),
                'log.name': this.$t('Name'),
                'log.category_id': this.$t('Category'),
                'log.vendor_id': this.$t('Vendor'),
                'log.sort_order': this.$t('Sort'),
                'log.url_handle': this.$t('Url handle'),
                'log.new': this.$t('NEW'),
                'log.tracking': this.$t('Quantity tracking'),
                'log.threshold': this.$t('Quantity threshold'),
                'log.shipping': this.$t('Require shipping'),
                'log.type': this.$t('Type'),
                'log.out_of_stock_id': this.$t('Out ot stock status'),
                'log.status_id': this.$t('In stock status'),
                'log.minimum': this.$t('Minimum quantity to order'),
                'log.seo_title': this.$t('SEO title'),
                'log.seo_description': this.$t('SEO description'),
                'log.active': this.$t('Active'),
                'log.description': this.$t('Description'),
                'log.short_description': this.$t('Short description'),
                'log.continue_selling': this.$t('Continue selling'),
                'log.price_from': this.$t('Price from'),
                'log.price_to': this.$t('Price to'),
                'log.p1': this.$t('Variant 1'),
                'log.p2': this.$t('Variant 2'),
                'log.p3': this.$t('Variant 3'),
            },
            openModal: this.modelValue,
            action: {},
            loading: true,
            currentPage: 1,
            perPage: 25,
            table: null,
            name: this.history.name,
            viewLogModal: false,
            openDetails: false,
            modalItem: {},
            data: {},
            model: new HistoryLog()
        };
    },
    methods: {
        getLabel(key) {
            const formatString = (str) => {
                return str
                    .split('.')
                    .pop()
                    .split('_')
                    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(' ');
            }

            if(!this.translations[key]) {
                this.translations[key] = formatString(key);
            }

            return this.translations[key];
        },
        getValue(value, key) {
            if (!value) {
                return null;
            }
            if((key || '').includes('price') || (key || '').includes('money')){
                return moneyFormat(moneyFormatNoSign(value))
            }
            return value;
        },
        async getItem() {
            try {
                this.loading = true
                this.data = await this.model.find(this.history.id)
            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.loading = false
            }
        },
        isAttributeComponent(key) {
            if(key === 'meta') {
                key = 'metaData';
            }
            return this.$options.components && this.$options.components[key];
        },
        getAttributeComponent(key) {
            if(key === 'meta') {
                key = 'metaData';
            }
            return this.$options.components[key];
        },
        generateKey() {
            return Math.random().toString(36).substr(2, 9);
        },
        guessCompareKey(item) {
            switch (item) {
                case 'sku':
                    return this.translations['SKU']
                case 'barcode':
                    return this.translations['Barcode']
                case 'External ID':
                    return this.translations['External ID']
                case 'System ID':
                    return this.translations['System ID']
            }

            return item
        },
    },
    watch: {
        openModal(value) {
            this.$emit('update:modelValue', value)

            if(value) {
                this.getItem()
            }
        },
        modelValue(value) {
            this.openModal = value
        }
    },
    computed: {
        actionLabel() {
            switch (true) {
                case !this.data.action:
                    return this.translations["No action"];
                default:
                    let val = this.data.action.charAt(0).toUpperCase() + this.data.action.slice(1);
                    return this.translations[val] || val;
            }
        },
        date() {
            return this.data.date ? moment(this.data.date).format(this.serverSettings('format.dateTime')) : null
        },
        mainProductInfo() {
            return dotGet(this.data?.log?.dirty || []).getAll(['attributes', 'relations.meta', 'relations.categories']).all()
        },
        relationsProductInfo() {
            let relations = this.data?.log?.dirty?.relations || {};
            if(relations.relations) {
                delete relations.relations.meta;
                delete relations.relations.categories;
            }

            return relations;
        },
    },
    emits: ['update:modelValue'],
};
</script>
