let routes = [
    {
        component: () => import("./../../../Core/components/ErpMain"),
        props: {appKey: 'microinvest'},
        children: [
            {
                name: "apps.microinvest.overview",
                path: "/admin/apps/microinvest",
                component: () =>
                    import("./../../../Core/components/Tabs/ErpOverview.vue"),
            },
            {
                name: "apps.microinvest.settings",
                path: "/admin/apps/microinvest/settings",
                component: () => import("./../components/Tabs/Settings"),
            },
            {
                name: "apps.microinvest.status",
                path: "/admin/apps/microinvest/status",
                component: () => import("./../components/Tabs/Status"),
            },
            {
                name: "apps.microinvest.products",
                path: "/admin/apps/microinvest/products",
                component: () => import("./../components/Tabs/Products"),
            },
            {
                name: "apps.microinvest.tasks",
                path: "/admin/apps/microinvest/tasks/:taskId?",
                component: () => import("./../components/Tabs/Tasks"),
            },
            {
                name: "apps.microinvest.importLog",
                path: "/admin/apps/microinvest/import-history",
                component: () => import("./../../../Core/components/Tabs/ErpImportLog"),
            },
            {
                name: "apps.microinvest.importList",
                path: "/admin/apps/microinvest/import-history/:id",
                component: () => import("./../../../Core/components/Tabs/ErpImportList"),
            },
        ],
    },
];

export {routes};
