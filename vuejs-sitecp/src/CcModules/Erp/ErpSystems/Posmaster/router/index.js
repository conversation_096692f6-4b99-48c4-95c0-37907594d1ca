let routes = [
    {
        component: () => import("./../../../Core/components/ErpMain"),
        props: {
            appKey: 'posmaster',
        },
        children: [
            {
                name: "apps.posmaster.overview",
                path: "/admin/apps/posmaster",
                component: () => import("./../../../Core/components/Tabs/ErpOverview"),
            },
            {
                name: "apps.posmaster.settings",
                path: "/admin/apps/posmaster/settings",
                component: () => import("./../components/Tabs/Settings"),
            },
            {
                name: "apps.posmaster.status",
                path: "/admin/apps/posmaster/status",
                component: () => import("./../components/Tabs/Status"),
            },
            {
                name: "apps.posmaster.products",
                path: "/admin/apps/posmaster/products",
                component: () => import("./../components/Tabs/Products"),
            },
            // {
            //     name: "apps.posmaster.categoriesMapping",
            //     path: "/admin/apps/posmaster/categories-mapping",
            //     component: () => import("./../components/Tabs/CategoriesMapping"),
            // },
            {
                name: "apps.posmaster.importLog",
                path: "/admin/apps/posmaster/import-history",
                component: () => import("./../../../Core/components/Tabs/ErpImportLog"),
            },
            {
                name: "apps.posmaster.importList",
                path: "/admin/apps/posmaster/import-history/:id",
                component: () => import("./../../../Core/components/Tabs/ErpImportList"),
            },
        ]
    },
];

export {routes};
