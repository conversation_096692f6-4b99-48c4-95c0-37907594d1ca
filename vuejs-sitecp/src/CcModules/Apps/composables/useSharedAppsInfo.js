import { computed, inject, onMounted, ref } from 'vue';
import { createSharedComposable } from '@vueuse/core';
import Apps from '../js/Apps';


const useSharedAppsInfo = createSharedComposable(() => {
    const errorResponse = inject('errorResponse');
    const apps = ref([]);
    const loading = ref(false);
    const loadingFeatured = ref(false);

    const featuredApps = ref([]);

    const getFeaturedApps = async () => {
        loadingFeatured.value = true;
        let model = new Apps();

        try {
            featuredApps.value = await model.find('marketing/featured');

        } catch (error) {
            errorResponse(error);
        } finally {
            loadingFeatured.value = false;
        }
    }

    const getApps = async () => {
        loading.value = true;
        let model = new Apps();

        try {
            loading.value = true;
            const response = await model.all();

            apps.value = Object.values(response).sort((a, b) => {
                if (a.other.recommend === b.other.recommend) {
                    return a.name?.toLowerCase().localeCompare(b.name?.toLowerCase());
                }

                return a.other.recommend && !b.other.recommend ? -1 : 1;
            });
        } catch (error) {
            errorResponse(error);
        } finally {
            loading.value = false;
        }
    }

    const setAppAsInstalled = async (key) => {
        if (apps?.value?.length === 0) {
            return;
        }

        apps.value.forEach((item, index) => {
            if (item.key === key) {
                item.is_installed = true;
                item.paid = true;
            }
        });
    }
    const setAppAsUninstalled = async (key) => {
        if (apps?.value?.length === 0) {
            return;
        }

        apps.value.forEach((item, index) => {
            if (item.key === key) {
                item.is_installed = false;
            }
        });

    }

    const updateAppInfo = async (app) => {
        if (apps?.value?.length === 0) {
            return;
        }
        const index = apps.value.findIndex(item => item.key === app.key);

        if (index !== -1) {
            apps.value[index] = app;
        } else {
            await getApps();
        }
    }


    // onMounted(async () => {
    //     if (apps.value.length === 0) {
    //         await getApps();
    //     }
    // });

    return {
        apps,
        loading,
        getApps,
        setAppAsInstalled,
        updateAppInfo,
        setAppAsUninstalled,
        featuredApps,
        loadingFeatured,
        getFeaturedApps
    };
});

export default useSharedAppsInfo;