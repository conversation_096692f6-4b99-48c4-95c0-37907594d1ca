<template>
    <b-container v-show="isLoaded" class="mt-4 deprecated-app">
        <b-row class="justify-content-center">
            <b-col class="col-10">
                <b-row>
                    <b-col class="col-9">
                        <div>
                            <h3>
                                {{ app.name }}
                            </h3>
                            <p class="m-0" v-if="app.category">
                                <strong class="app-category">{{ translations['Category:'] }} </strong>
                                <span style="color: #8d58e0; font-size: 18px">
                                    {{ app.category.name }}
                                  </span>
                            </p>
                            <p class="deprecated-title" v-html="translations.DEPRICATED"></p>
                        </div>
                        <div class="deprecated-info">
                            <h5 v-html="translations['This app is no longer available.']"></h5>
                            <p class="deprecated-description"
                                v-html="translations['For more information or if you have any questions, please do not hesitate to contact our team. We will find the best solution for your business.']"
                            ></p>
                        </div>
                        <!-- <button class="btn btn-white" v-html="translations['Contact us']"></button> -->
                    </b-col>
                    <b-col class="col-3">
                        <div id="deprecated-image-container">
                            <img :src="app.icon" alt="logo"/>
                        </div>
                    </b-col>
                </b-row>
            </b-col>
        </b-row>

        <b-row class="mt-5 justify-content-center">
            <b-col class="col-10">
                <AppSwipe :apps="appList" :title="translations['Similar Apps']" class="similar-apps"></AppSwipe>
            </b-col>
        </b-row>
    </b-container>
</template>
<script>
import axios from "axios";

import Loading from "@components/Loading";
import AppSwipe from "@components/Apps/AppSwiper/AppSwipe.vue";

export default {
    name: "Deprecated",
    components: {
        AppSwipe,
        Loading,
    },
    props: {
        app: {
            type: Object,
            required: true
        }
    },
    data() {
        return {
            appList: [],
            isLoaded: false,
            translations: {
                'Category:': this.$t('Category:'),
                'DEPRICATED': this.$t('DEPRICATED'),
                'Contact us': this.$t('Contact us'),
                'Similar Apps': this.$t('Similar Apps'),
                'This app is no longer available.': this.$t('This app is no longer available.'),
                'For more information or if you have any questions, please do not hesitate to contact our team. We will find the best solution for your business.': this.$t('For more information or if you have any questions, please do not hesitate to contact our team. We will find the best solution for your business.'),
            }
        };
    },
    methods: {
        getAppList() {
            axios
                .get("/admin/api/core/applications")
                .then((res) => {
                    this.appList = Object.values(res.data).filter(
                        (x) => x.category?.id === this.app?.category.id && x.key !== this.app.key
                    );
                })
                .finally(() => {
                    this.isLoaded = true;
                });
        },
    },
    mounted() {
        this.getAppList();
    },
};
</script>

<style>
.deprecated-app h3 {
    font-size: 40px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    margin-bottom: 0px;
}

.deprecated-app h5 {
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
}

.deprecated-title {
    display: inline-block;
    padding: 3px 6px;
    border-radius: 6px;
    background: #fc4f4e;
    color: white;
    letter-spacing: 0.48px;
    text-transform: uppercase;
    font-weight: 700;
    line-height: normal;
    font-size: 12px;
    margin-top: 7px;
}

#deprecated-image-container {
    width: fit-content;
    padding: 30px;
    border-radius: 32px;
    background: white;
}

#deprecated-image-container img {
    max-width: 140px;
    opacity: 0.6;
}

.deprecated-description {
    text-overflow: ellipsis;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
}

.deprecated-info {
    margin-top: 15px;
}

.app-category{
    color:  #3D414D;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin-right: 10px;
}
</style>
