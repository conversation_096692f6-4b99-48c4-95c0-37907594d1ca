@use './../base/variables' as vars;

$form-color: var(--color-greys-800, #7A7D84);
$form-background: #fafaff;
$form-input-background: #fff;
$form-border-color: #dedede;
$form-active-background: #8d58e0;
$form-active-color: #fff;
$form-input-focus-border: #B38AF4;
$border-radius-cc-border-radius-sm: var(--border-radius---cc-border-radius-sm, 6px);
$color-border-neutral-cc-color-border-focused: #B38AF4;
$color-border-neutral-cc-color-border-focused: #B38AF4;

.form-control,
.form-control:focus {
  background-color: $form-input-background;
  border: 1px solid $form-border-color;
  border-radius: var(--border-radius---cc-border-radius-sm, 6px);
  box-shadow: none;
  height: 36px;
  padding: 0 12px;
  font-size: 16px;
  font-weight: normal;
  color: $form-color;
}

.form-group label,
.control-label,
.col label {
  color: $form-color;
  font-size: 14px;
  font-weight: 400;
  padding: 0;
  text-align: left;
  width: auto;
  display: inline-block;
  max-width: 100%;
}

/* input */
.input-unit,
.input-unit-left {
  position: absolute;
  left: auto;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  background: #FAFAFF;
  border: 1px solid #DEDEDE;
  border-radius: var(--border-radius---cc-border-radius-sm, 6px);
  height: 36px;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: #7A7D84;
  font-size: 14px;
  font-weight: 400;
}

.purple-unit .input-group-text{
  color: var(--Color-Text-Body---cc-color-text-link, #8D58E0);
  font-size: 16px;
  border-radius: 6px 0px 0px 6px !important;
  border:  1px solid #8D58E0;
  background:  var(--color-purple-100, #F7F2FF);
  z-index: 10;
}
.input-group-text {
  align-self: flex-end;
  font-size: 14px;
  font-weight: 400;
  /* height: 37px; */
  height: 36px;
  border-radius: var(--border-radius---cc-border-radius-sm, 6px);
  padding: 8px 12px;
  color: #7A7D84;
}

.input-unit-left {
  left: 0;
  right: auto;
}

.input-component-input {
  color: var(--color-text-body-cc-color-text-secondary, #7A7D84);
  height: 36px;

  // THIS IS ADDED TO FIX THE ISSUE OF THE INPUT FIELD GETTING OVER FIXED COLUMN IN DATATABLE
  z-index: 0 !important;
}

.input-component-input::placeholder {
  background-color: #fafaff;
  font-size: 16px;
  font-weight: normal;
  color: var(--color-text-body-cc-color-text-subdued, #9DA0A6) !important;
  //opacity: 0.3;
}

.column-style {
  flex-direction: column !important;
  align-items: flex-start !important;

  input {
    width: 100%;
  }

  input.form-control {
    /*
    margin-top: 5px;
    */
  }

  .vs__search {
    width: 0;
  }
}


.input-icon-right {
  color: var(--color-text-body-cc-color-text-secondary, #7A7D84);
  text-align: center;
  /* font-family: 'Font Awesome 5 Pro'; */
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  /* content: "\f042"; */
  display: inline-block;
  width: 18px;
  height: 18px;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}
.input-supporting-message {
  color: var(--color-text-body-cc-color-text-secondary, #7A7D84);
  font-family: Roboto;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  letter-spacing: 0.36px;
}
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type=number].with-increment-decrement {
    border-radius: 6px !important;
    padding-right: 48px !important;
}
.decrement-increment-buttons {
  color: var(--color-text-body-cc-color-text-secondary, #7A7D84);
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  height: 25px;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 1;
  transition: opacity .3s;
  z-index: 0;
}
/*
.input-group {
  &:hover {
    .decrement-increment-buttons {
      @media (min-width: 1200px) {
        opacity: 1;
        visibility: visible;
      }
    }
  }
}
*/
.column-style {
  .bs-input-group {
    input.form-control {
      ~ .decrement-increment-buttons {
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}

.unit-and-arrows {
  margin-right: 35px;
}
.increment, .decrement {
  background: transparent;
  border: 0;
  font-size: 18px;
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--space-size-8, 8px);
  padding-inline: 6px;
  color: #3D414D;
  &:disabled {
    color: var(--Color-Text-Body---cc-color-text-disabled, #DEDEDE);
  }
}
.input-icon-left-placeholder {
  color: var(--color-text-body-cc-color-text-subdued, #9DA0A6);
  text-align: center;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  display: inline-block;
  width: 18px;
  height: 18px;
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}
.icon-left-padding {
  padding-left: 30px !important;
}
.tooltips .tooltip-icon i {
  color: var(--color-text-body-cc-color-text-link, #8D58E0);
}
.input-group:not(.has-validation) > .input-group-text + :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group:not(.has-validation) :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating).border-6 {
  border-top-right-radius: var(--border-radius---cc-border-radius-sm, 6px);
  border-bottom-right-radius: var(--border-radius---cc-border-radius-sm, 6px);
}
/* end input */

/* textarea */
textarea {
  &.form-control {
    padding: 0.75rem;

    &::placeholder {
      color: #7a7d84;
      opacity: 0.3;
    }
  }

  &.form-control,
  &.form-control:focus {
    height: auto;
    padding: 0.75rem;
  }

  &.form-control {
    &:focus {
      border-color: $form-input-focus-border;
    }
  }
}

/* end textarea */

/* select */
.label-text {
  color: var(--color-text-body-cc-color-text-secondary, #7A7D84);
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 5px;
  width: -webkit-fill-available;
}

.switch_margin {
  margin-bottom: 10px;
}

.multi {
  margin-right: 0;
}

.column-style {
  .multi {
    margin-top: 5px;
    width: 100% !important;
  }
}

.multiselect-clear,
.multiselect-caret {
  z-index: auto;
}
/* end select */

.bordered-top {
  margin-top: 20px;
  border-top: 1px solid #d6d9e9;
  padding-top: 10px;
}

.section-title {
  color: #1c1c1c;
  font-size: 16px;
  text-align: left;
  margin-bottom: var(--border-radius---cc-border-radius-sm, 6px);
}

.subtitle {
  color: #B0B0B0;
  font-size: 13px;
  font-weight: 300;
  padding: 0;
  width: auto;
  display: inline-block;
  max-width: 100%;
}

.unit-container {
  position: relative;
}

.units {
  position: absolute;
  right: 13px;
  top: 50%;
  transform: translateY(-50%);
  background: #FFF;
  border: 1px solid #d6d9e9;
  height: 36px;
  border-radius: var(--border-radius---cc-border-radius-sm, 6px);
  display: flex;
  align-items: center;
  padding: 0 4px;
  color: #757677;
  font-size: 12px;
}

/* radio */
.radio-wrapper {
  .form-check {
    padding-left: 2em;
    min-height: 1.8rem;

    input:not([disabled]) {
      cursor: pointer;

      + label {
        cursor: pointer;
      }
    }
  }

  .form-check-input {
    width: 1.145em;
    height: 1.145em;
    position: relative;
    margin-left: -24px;
    margin-top: 0.2em;

    &:not(:checked) {
      margin-left: -1.9em;

      &:not([disabled]) {
        border: 1px solid #DEDEDE;

        &:hover {
          background-color: #EDEEFA;
        }

        &:active {
          background-color: #EFE5FF;
        }
      }
    }

    &:checked {
      background-color: $form-active-background;
      border-color: transparent;
      width: 0.715em;
      height: 0.715em;

      &[type=radio] {
        background-image: none;
        top: 2px;
      }

      &:before {
        content: '';
        background-color: transparent;
        border: 1px solid $form-active-background;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: calc(100% + 0.572em);
        height: calc(100% + 0.572em);
        //z-index: 20;
      }

      &:hover {
        background-color: $form-input-focus-border;

        &:before {
          border-color: $form-input-focus-border;
        }
      }

      &:active {
        background-color: #62499C;

        &:before {
          border-color: #62499C;
        }
      }

      &:focus {
        box-shadow: none;

        &:active {
          &:after {
            content: none;
          }
        }
      }

      &:focus-visible {
        &:after {
          content: '';
          background-color: transparent;
          border: 1px solid $form-input-focus-border;
          position: absolute;
          top: 50%;
          left: 50%;
          border-radius: 50%;
          transform: translate(-50%, -50%);
          width: calc(100% + 0.858em);
          height: calc(100% + 0.858em);
          z-index: 3;
        }
      }
    }

    &:focus {
      box-shadow: none;
    }
  }
}
.form-check-label {
  color: $form-color;
}
/* end radio */

/* checkbox */
.form-check-input:checked[type=checkbox] {
  --bs-form-check-bg-image: url("data:image/svg+xml,<svg width='8' height='11' viewBox='0 0 14 11' fill='none' xmlns='http://www.w3.org/2000/svg'><path d= 'M11.8945 1.04688C12.0312 0.910156 12.25 0.910156 12.3594 1.04688L13.1523 1.8125C13.2617 1.94922 13.2617 2.16797 13.1523 2.27734L4.94922 10.4805C4.8125 10.6172 4.62109 10.6172 4.48438 10.4805L0.820312 6.84375C0.710938 6.70703 0.710938 6.48828 0.820312 6.37891L1.61328 5.58594C1.72266 5.47656 1.94141 5.47656 2.07812 5.58594L4.70312 8.23828L11.8945 1.04688Z' fill='white'/></svg>");
}
.form-check-input[type=checkbox] {
  border-radius: var(--border-radius-cc-border-radius-xs, 4px);
}
.checkbox-wrapper {
  .form-check-input {
    width: 1.143em;
    height: 1.143em;
    position: relative;
    //top: -0.3em;
    padding: 10px;
    margin-right: 5px;
    margin-top: 0;

    &:checked {
      background-color: $form-active-background;
      border-color: $form-active-background;

      &:hover {
        background-color: #b38af4;
        border-color: #b38af4;
      }

      &:active {
        background-color: #efe5ff;
        border-color: #d2baf7;
      }

      &:focus-visible {
        &:before {
          content: '';
          background-color: transparent;
          border: 1px solid $color-border-neutral-cc-color-border-focused;
          border-radius: var(--border-radius-cc-border-radius-xs, 4px);
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: calc(100% + 0.572em);
          height: calc(100% + 0.572em);
          z-index: 20;
        }
      }
    }

    &:focus {
      outline: 0;
      box-shadow: none;
    }
  }

  .checkbox-label {
    padding: 4px;
  }
}
/* end checkbox */

/* multiselect */
:root {
  --ms-tag-bg: #8d58e0;
  --ms-radius: var(--border-radius---cc-border-radius-sm, 6px);
  --ms-dropdown-radius: var(--border-radius---cc-border-radius-sm, 6px);
  --ms-dropdown-bg: #fafaff;
  //--ms-placeholder-color: #D5D5D6;
  --ms-placeholder-color: #9DA0A6;
  --ms-line-height: 1;
  --ms-max-height: 20rem;
  --ms-option-py: 0.3rem;
}

.multiselect {
  color: $form-color;
  background-color: $form-background;
  border-color: $form-border-color;
  //height: auto;
  //min-height: 36px;

  &.is-active {
    box-shadow: 0px 2px 10px 4px rgba(174,177,205,0.3);
    //border-color: $form-active-background;
  }
}
.multiselect-wrapper {
  font-size: 14px !important;
}
.multiselect-search {
  background-color: $form-background;
  color: $form-color;
  font-size: 14px !important;

  &::placeholder {
    font-size: 14px !important;
  }
}

.multiselect-tags-search {
  background-color: $form-background;
  font-size: 14px;
    color: $form-color;
  &::placeholder {
    font-size: 14px !important;
  }
}

.multiselect-tag {
  white-space: normal;
}

.multiselect-options {
  background-color: $form-background;
  padding: 12px;
  .multiselect-group .multiselect-group-options {
    padding-left: 16px;
  }
}

.multiselect-option,
.multiselect-group,
.multiselect-group-label {
  background-color: $form-background;
  border-radius: 5px;
  color: $form-color;
  font-size: 14px !important;

  + .multiselect-option {
    margin-top: 2px;
    font-size: 14px !important;
  }

  &.is-pointed {
    background-color: var(--color-neutrals-400, #EDEEFA);
    color: $form-color;
  }

  &.is-selected {
    background-color: $form-active-background;
    color: $form-active-color;
  }

  &.is-pointed.is-selected {
    background-color: #b38af4;
    color: $form-active-color;
  }
}

.multiselect-dropdown {
  overflow-y: auto;
  box-shadow: 0px 5px 10px 4px rgba(174, 177, 205, 0.3);
  z-index: 14;
    &::-webkit-scrollbar {
        width: 8px;
    }

    &::-webkit-scrollbar-track {
        border-radius: 10px;
        background: #e6e7eb;
    }

    &::-webkit-scrollbar-thumb {
        background: #aaa;
        border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb:hover {
        background: #7a7d84;
    }
}

.multiselect-placeholder {
  opacity: 1;
  color:  var(--cc-color-text-secondary,#7A7D84);
  display: inline-block;
  line-height: 36px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px !important;
}

.multiselect-no-options, .multiselect-no-results {
  margin-top: -24px;
  font-size: 14px !important;
  color: var(--color-greys-800, #7A7D84);
}
/* end multiselect */

.form-control[readonly],
input[readonly] {
  border: 1px solid var(--Color-Border-Neutral---cc-color-border-subdued, #DEDEDE);
  background: var(--Color-Surface-Neutral---cc-color-bg-input-readonly, #F7F2FF);
  cursor: default;
}

.form-control,
input {
  &:disabled {
    background-color: vars.$form-disabled-bg-color !important;
    border-color: vars.$form-disabled-border-color !important;
    color: var(--Color-Text-Body---cc-color-text-disabled, #DEDEDE);
    cursor: not-allowed;
  }
}

.form-control::placeholder,
input::placeholder {
  background-color: transparent !important;
  color: var(--color-text-body-cc-color-text-subdued, #9DA0A6);
  font-weight: 400;
}

input {
  &.form-control {
    &:focus {
      border-color: $form-input-focus-border;
    }
  }
}

.no-interaction {
  opacity: .6;
  pointer-events: none;
  user-select: none;
}

.product-tag-input .input-component-input {
  border-top-right-radius: var(--border-radius---cc-border-radius-sm, 6px) !important;
  border-bottom-right-radius: var(--border-radius---cc-border-radius-sm, 6px) !important;
}

.country-flag.small-flag {
  margin: -0.9em -0.4em -0.9em -2em !important;
  transform: scale(0.35) !important;
}
