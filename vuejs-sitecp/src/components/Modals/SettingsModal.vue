<template>
    <b-card v-if="!hideCard" class="card-height">
        <b-row
            class="d-flex"
            :class="cardAlign === 'start' ? 'align-items-start' : 'align-items-center'"
        >
            <b-col class="col-11">
                <h5
                    class="label-600"
                    v-html="box.title || label"
                    style="line-height: 28px"
                    :class="$slots.preview_settings ? 'mb-2' : ''"
                ></h5>
                <slot name="preview_settings"></slot>
            </b-col>
            <b-col class="col-1 d-flex justify-content-center px-0">
                <a
                    href="javascript:void(0)"
                    @click.prevent="openModal"
                    class="edit-settings-btn-toggle"
                >
                    <i class="fal fa-pen"></i>
                </a>
            </b-col>
        </b-row>
    </b-card>
    <b-modal
        @show="scrollOpenModal"
        @hidden="scrollCloseModal"
        v-model="obj[value]"
        class="modal-right"
        :size="size"
        :no-footer="true"
        header-class="edit-settings-modal-header"
        body-class="edit-settings-modal-content"
        :dialog-class="calculatorWidth ? 'calculator-width' : ''"
        :no-close-on-backdrop="noCloseOnBackdrop"
    >
        <template #header>
            <div class="d-flex align-items-center gap-2">
                <h5 v-html="sectionTitle" class="settings-modal-title"></h5>
            </div>
            <div class="d-flex justify-content-end align-items-center gap-2">
                <TooltipLabel
                    :hide-icon="true"
                    :tooltip-text="
                        translations['Share the currently open panel with another user']
                    "
                >
                    <a
                        href="javascript:void(0);"
                        @click.prevent="copyUrl"
                        class="link-icon me-1"
                    >
                        <i class="fal fa-link link-icon"></i>
                    </a>
                </TooltipLabel>
                <button
                    @click="closeModal"
                    type="button"
                    class="btn btn-white"
                    v-html="translations['Close']"
                ></button>
                <button
                    @click="saveData"
                    type="button"
                    class="btn btn-primary"
                    v-html="translations['Confirm']"
                ></button>
            </div>
            <!-- <pre>{{ settingsKeys }}</pre> -->
        </template>
        <div class="mt-2 setting-row-modal">
            <slot name="setting" ref="settingSlot"> </slot>
        </div>
    </b-modal>
</template>

<script>
import TooltipLabel from "@components/Apps/Erp/TooltipLabel";
import {toast} from "@js/toast";

export default {
    name: "SettingsModal",
    components: { TooltipLabel },
    props: {
        sectionTitle: {
            required: false,
            type: String,
            default: "",
        },
        value: {
            required: false,
        },
        modal: {
            required: false,
            default: false,
        },
        calculatorWidth: {
            type: Boolean,
            required: false,
            default: false,
        },
        label: {
            required: false,
            default: null,
        },
        cardAlign: {
            required: false,
            type: String,
            default: "start",
        },
        settingsKeys: {
            required: false,
            default: [],
            type: Array,
        },
        responseErrors: {
            required: false,
            default: {},
            type: Object,
        },
        size: {
            required: false,
            default: "xl",
            type: String,
        },
        box: {
            required: false,
            default: {},
        },
        hideCard: {
            required: false,
            default: false,
            type: Boolean,
        },
        noCloseOnBackdrop: {
            required: false,
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            obj: this.modal,
            translations: {
                Close: this.$t("Close"),
                Confirm: this.$t("Confirm"),
                Save: this.$t("Save"),
                "Share the currently open panel with another user": this.$t(
                    "Share the currently open panel with another user"
                ),
                "The URL was copied and ready to share": this.$t(
                    "The URL was copied and ready to share"
                ),
            },
        };
    },
    methods: {
        scrollOpenModal() {
          //document.querySelector('.scroll-lock').style.overflowY = 'hidden';
        },
        scrollCloseModal() {
         // document.querySelector('.scroll-lock').style.overflowY = 'visible';
        },
        saveData() {
            this.obj[this.value] = false;
            this.$emit("toggle-modal", "save");
            this.$router.push({
                hash: "",
            });
        },
        closeModal() {
            this.obj[this.value] = false;
            this.$emit("toggle-modal", false);
            this.$router.push({
                hash: "",
            });
        },
        openModal() {
            this.obj[this.value] = true;
            this.$emit("toggle-modal", true);
            this.$router.push({
                hash: `#${this.box?.group}~${this.box?.key}`,
            });
        },
        copyUrl() {
            navigator.clipboard
                .writeText(`${this.serverSettings("host")}${this.$route.fullPath}`)
                .then(() => {
                    toast.success(this.translations["The URL was copied and ready to share"]);
                });
        },
    },
    watch: {

        responseErrors: {
            deep: true,
            handler(val) {
                const errorKeys = Object.keys(val);
                const filteredKeys =
                    errorKeys.length !== 0
                        ? errorKeys.map((x) => (x.includes(".") ? x.split(".")[1] : x))
                        : [];

                // errorKeys.forEach((key) => {
                //     if (
                //         this.settingsKeys.includes(key.split(".")[1]) ||
                //         this.settingsKeys.includes(key)
                //     ) {
                //         this.obj[this.value] = true;
                //         this.$emit("toggle-modal", true, true, true);
                //     }
                // });

                for(let index in errorKeys) {
                    let key = errorKeys[index];
                    if (
                        this.settingsKeys.includes(key.split(".")[1]) ||
                        this.settingsKeys.includes(key)
                    ) {
                        this.obj[this.value] = true;
                        this.$emit("toggle-modal", true, true, true);
                        return;
                    }
                }

                // this.settingsKeys.forEach((key) => {
                //     if (filteredKeys.includes(key)) {
                //         this.obj[this.value] = true;
                //         this.$emit("toggle-modal", true, true, true);
                //     }
                // });

                for(let sIndex in this.settingsKeys) {
                    let key2 = this.settingsKeys[sIndex];
                    if (filteredKeys.includes(key2)) {
                        this.obj[this.value] = true;
                        this.$emit("toggle-modal", true, true, true);
                        return;
                    }
                }

                for(let sIndex2 in this.settingsKeys) {
                    let key3 = this.settingsKeys[sIndex2];
                    for(let eIndex in errorKeys) {
                        let regexString = null;
                        if(key3.indexOf('[d]')) {
                            regexString = key3.replace(/\[d\]/g, '\\d+').replace(/\./g, '\\.');
                        }

                        if(regexString) {
                            const regex = new RegExp(regexString);
                            if(regex.test(errorKeys[eIndex])) {
                                this.obj[this.value] = true;
                                this.$emit("toggle-modal", true, true, true);
                                return;
                            }
                        }
                    }
                }
            },
        },
    },
    emits: ["toggle-modal"],
};
</script>
