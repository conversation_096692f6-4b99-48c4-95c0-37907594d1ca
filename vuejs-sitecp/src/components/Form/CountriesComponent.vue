<template>
    <SelectWithAjax
        v-model:val="value"
        v-bind="getMultiselectBinds"
        :show-country-flag="true"
    />
</template>

<script>
import SelectWithAjax from "./SelectWithAjax";

export default {
    name: "CountriesComponent",
    components: {
        SelectWithAjax,
    },
    props: {
        modelValue: {
            type: String,
            required: false,
        },
        id: {
            type: String,
            required: false,
        },
        multiselectRef: {
            type: String,
            required: false,
            default: "country",
        },
        label: {
            type: String,
            required: false,
            default: null,
        },
        placeholder: {
            type: String,
            required: false,
        },
        searchable: {
            type: Boolean,
            required: false,
            default: true,
        },
        euOnly: {
            type: Boolean,
            required: false,
            default: false,
        },
        isLoading: {
            type: Boolean,
            default: false,
        },
        tooltipText: {
            type: String,
            required: false,
        },
        tooltip: {
            type: Boolean,
            required: false,
        },
        icon: {
            type: String,
            required: false,
            default: "far fa-info-circle",
        },
        error: {
            required: false,
        },
        columnStyle: {
            type: Boolean,
            required: false,
        },
        canClear: {
            type: Boolean,
            required: false,
            default: true,
        },
        errors: {
            type: Array,
            required: false,
        },
        colsWidth: {
            type: Number,
            required: false,
            default: 12,
        },
        spaceBetween: {
            type: Boolean,
            required: false,
            default: true,
        },
        isSelectDisabled: {
            required: false,
        },
        groups: {
            type: Boolean,
            required: false,
            default: false,
        },
        mode: {
            required: false,
            // [tags]
        },
        minChars: {
            required: false,
            default: 0,
        },
        resolveOnLoad: {
            type: Boolean,
            required: false,
            default: false,
        },
        hideSelected: {
            type: Boolean,
            required: false,
            default: true,
        },
        noMargin: {
            required: false,
            default: false,
        },
        required: {
            type: Boolean,
            required: false,
        },
    },
    data() {
        return {
            value: this.modelValue,
            countries: this.euOnly ?
                this.serverSettings("countriesEU", []) :
                this.serverSettings("countries", []),
        };
    },
    watch: {
        modelValue(value) {
            this.value = value;
        },
        value(value) {
            this.$emit("update:modelValue", value);

            this.$emit(
                "selected-item",
                this.countries.find((x) => x.id === value)
            );
        },
    },
    methods: {
        filterBG() {
            let arr = this.countries.map((country) => {
                if (
                    country.name &&
                    String(country.name) &&
                    country.name.toLowerCase() === "българия"
                ) {
                    return {
                        id: country.id,
                        name: "Bulgaria",
                        localized_name: "Bulgaria",
                    };
                }
                return country;
            });
            return arr;
        },
    },
    computed: {
        getMultiselectBinds() {
            return {
                id: this.id,
                multiselectRef: "country",
                label: this.label,
                placeholder: this.placeholder,
                searchable: this.searchable,
                tooltipText: this.tooltipText,
                tooltip: this.tooltip,
                icon: this.icon,
                error: this.error,
                columnStyle: this.columnStyle,
                canClear: this.canClear,
                errors: this.errors,
                colsWidth: this.colsWidth,
                spaceBetween: this.spaceBetween,
                isSelectDisabled: this.isSelectDisabled,
                groups: this.groups,
                mode: this.mode,
                minChars: this.minChars,
                noMargin: this.noMargin,
                hideSelected: this.hideSelected,
                required: this.required,
                // options: this.filterBG(),
                options: this.countries,
            };
        },
    },
    emits: ["update:modelValue", "selected-item"],
};
</script>
