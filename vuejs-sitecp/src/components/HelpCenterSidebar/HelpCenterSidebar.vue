<template>
    <b-modal
        class="modal-right"
        id="help-center-modal"
        v-model="helpCenter.modal"
        size="xl"
        header-class="edit-settings-modal-header"
        body-class="edit-settings-modal-content"
        :no-footer="true"
    >
        <template #header>
            <div class="d-flex align-items-center">
                <h5 class="settings-modal-title" v-if="!helpCenter.loading" v-html="helpCenter?.help?.title"></h5>
            </div>
            <div class="d-flex justify-content-end align-items-center gap-2">
                <button class="btn btn-white"
                        @click="helpCenter.modal = false"
                        v-html="translations['Close']"
                >
                </button>
            </div>
        </template>

        <Loading
            v-if="helpCenter.loading"
            :loading="helpCenter.loading"
            class="app-loader-center"
        />
        <b-row v-else>
            <b-col>
                <b-card>
                    <div class="help-center-article-description" v-html="helpCenter?.help?.description"></div>
                </b-card>
            </b-col>
        </b-row>
    </b-modal>
</template>
<script>
import {useHelpCenter} from '@js/Layout/composables/useHelpCenter.js';

export default {
    setup() {
        const {helpCenter} = useHelpCenter();

        return {
            helpCenter
        }
    },
    data(){
        return {
            translations:{
                'Close': this.$t('Close'),
            }
        }
    }
}

</script>
