<template>
    <template v-if="loading || onboardingProgressComplete">
        <div
            class="icon-wrap tooltips"
            data-bs-custom-class="tooltip-dark"
            data-bs-placement="right"
            data-bs-toggle="tooltip"
            data-bs-html="true"
            :data-bs-original-title="submenuOpened ? translations[nav?.label] : ''"
            :title="submenuOpened ? translations[nav?.label] : ''"
        >
            <b-spinner small v-if="loading" class="ms-1"></b-spinner>
            <i v-else :class="nav.icon"></i>
        </div>
        <span v-html="translations[nav.label]"></span>
    </template>
    <template v-else>
        <div class="icon-wrap tooltips">
            <i class="fa-light fa-rocket-launch launch"></i>
        </div>
        <div v-if="!submenuOpened" id="progress-nav-action">
            <div class="title">
                <span v-html="translations['Setup guide']"></span>
                <span class="progress-text">{{ `${stepsCompleted || 0}/${Object.keys(steps).length}` }}</span>
            </div>
            <div class="progress-in-tab">
                <b-progress
                    :value="stepsCompleted"
                    variant="success"
                    :max="Object.keys(steps).length"
                ></b-progress>
            </div>
        </div>
    </template>
</template>
<script setup>
import useOnboardingProgress from "../../CcModules/Dashboard/components/js/useOnboardingProgress";
const {
    steps,
    loading,
    onboardingProgressComplete,
    stepsCompleted,
} = useOnboardingProgress();

const props = defineProps({
    nav: {
        type: Object,
        default: {},
    },
    translations: {
        type: Object,
        default: {},
    },
    submenuOpened: {
        type: Boolean,
        default: false,
    },
});
</script>
<style lang="scss">
#progress-nav-action {
    display: flex;
    flex-direction: column;
    gap: 2px;
    width: 100%;
    margin-left: 18px;
    margin-right: 8px;

    .title {
        display: flex;
        justify-content: space-between;
        gap: 16px;
        align-items: center;
        height: 26px;

        .progress-text {
            // color: var(--Color-Text-Body---cc-color-text-invert, #fff);
            font-family: Roboto;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
        }
    }
    .progress-in-tab {
        width: 100%;

        // fixes the progress bar position
        padding-bottom: 3px;

        .progress {
            height: 5px;
            background: #FFF;
        }

        .progress-bar.bg-success {
            background-color: #686b8b !important;
        }
    }
}
@keyframes rocker-launch {
    0% {
        transform: rotate(3deg);
    }
    50% {
        transform: rotate(-3deg);
    }
    100% {
        transform: rotate(3deg);
    }
}

.launch {
    display: inline-block;
    animation: rocker-launch 0.5s infinite ease-in-out;
}
</style>
