<template>
    <b-card
        class="settings-card"
        :class="[
            {
                'bordered-card': bordered,
            },
            classes,
        ]"
    >
        <b-row
            v-if="title || $slots.title"
            @click.stop="toggleCollapsed"
            :class="{ 'c-pointer': collapsible }"
        >
            <b-col class="d-flex align-items-center">
                <slot name="title"></slot>
                <h5 v-html="title" class="label-500 mb-0"></h5>
            </b-col>
            <b-col v-if="collapsible" class="text-right user-select-none" :class="collapseClasses">
                <a href="javascript:void(0)">
                    {{
                        collapsed
                            ? translations["Hide settings"]
                            : translations["Show settings"]
                    }}
                    <i
                        class="ms-1"
                        :class="collapsed ? 'fal fa-chevron-up' : 'fal fa-chevron-down'"
                    ></i>
                </a>
            </b-col>
            <b-col class="col-12 mt-2" v-if="placeholder">
                <span class="text-400-secondary">{{ placeholder }}</span>
            </b-col>
        </b-row>
        <Vue3SlideUpDown v-model="collapsed" :duration="200">
            <FormWrapper>
                <slot></slot>
            </FormWrapper>
        </Vue3SlideUpDown>
    </b-card>
</template>

<script>
import { Vue3SlideUpDown } from "vue3-slide-up-down";
import FormWrapper from "@components/Form/FormWrapper";
import ActiveSwitch from "@components/Form/ActiveSwitch";

export default {
    components: {
        Vue3SlideUpDown,
        FormWrapper,
        ActiveSwitch,
    },
    props: {
        title: {
            type: String,
            default: "",
        },
        collapsible: {
            type: Boolean,
            default: false,
        },
        placeholder: {
            type: String,
            default: null,
        },
        open: {
            type: Boolean,
        },
        bordered: {
            type: Boolean,
            default: false,
        },
        classes: {
            type: String,
            default: "",
        },
        collapseClasses: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            collapsed: false,
            translations: {
                "Hide settings": this.$t("Hide settings"),
                "Show settings": this.$t("Show settings"),
            },
        };
    },
    methods: {
        toggleCollapsed() {
            if (this.collapsible) {
                this.collapsed = !this.collapsed;
            }
        },
    },
    created() {
        this.collapsed = !this.collapsible;
    },
    watch: {
        open(value) {
            this.collapsed = value;
        },
        collapsed: {
            handler(value) {
                this.$emit("update:open", value);
            },
        },
    },
    mounted() {
        if (this.collapsible && this.open) {
            this.collapsed = this.open;
        }
    },
    emits: ["update:open"],
};
</script>

<style lang="scss">
.settings-card {
    &.bordered-card {
        border: var(--space-size-1, 1px) solid
            var(--Color-Border-Neutral---cc-color-border-disabled, #e6e7eb);
        border-radius: 8px;
    }
    .card-body {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        // ??? padding: var(--Spacing---cc-space-lg, 24px) !important; ???
    }
    .card-body hr {
        margin: 0 !important;
    }
}
</style>
