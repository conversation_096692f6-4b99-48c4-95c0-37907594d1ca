<template>
    <div class="notification-logger-wrapper">
        <template
            v-for="(notificationType, index) in Object.keys(notifications)"
            :key="index"
        >
            <div
                v-if="notifications[notificationType]?.length > 0"
                class="d-flex flex-column gap-2 align-items-end"
            >
                <Vue3SlideUpDown
                    :model-value="notifications[notificationType]?.length > 2"
                    :duration="200"
                >
                    <div class="notification-type-head">
                        <button
                            class="btn btn-ghost btn-small d-flex align-items-center gap-1"
                            style="padding: 6px 8px !important"
                            @click.stop="
                                () => toggleOpenNotifications(notificationType)
                            "
                        >
                            <i
                                class="fa-light fa-angle-down rotate m-0"
                                :class="{ up: toggled[notificationType] }"
                            ></i>

                            {{
                                translations[
                                    toggled[notificationType]
                                        ? "Show more"
                                        : "Show less"
                                    ]
                            }}
                        </button>
                        <button
                            class="btn btn-ghost btn-small d-flex"
                            style="padding: 6px 8px !important"
                            @click="
                                () => removeAllNotifications(notificationType)
                            "
                        >
                            <i class="fa-regular fa-xmark m-0"></i>
                        </button>
                    </div>
                </Vue3SlideUpDown>
                <Vue3SlideUpDown
                    :model-value="notifications[notificationType]?.length > 0"
                    :duration="200"
                    class="w-100"
                    :class="{
                        'notification-toasts-scroll':
                            notifications[notificationType]?.length > 2 &&
                            !toggled[notificationType],
                    }"
                >
                    <NotificationTypeWrapper
                        v-model:notifications="notifications[notificationType]"
                        :type="notificationType"
                        :stacked="toggled[notificationType]"
                    />
                </Vue3SlideUpDown>
            </div>
        </template>
    </div>
</template>
<script>
import useLiveNotifications from "@js/useLiveNotifications";

import EventBus from "@js/eventBus";
import NotificationTypeWrapper from "./Helpers/NotificationTypeWrapper";
import {Vue3SlideUpDown} from "vue3-slide-up-down";

export default {
    name: "NotificationsLogger",
    components: {
        NotificationTypeWrapper,
        Vue3SlideUpDown,
    },
    setup() {
        const {allowedNotifications} = useLiveNotifications();

        return {allowedNotifications}
    },
    data() {
        return {
            notifications: {
                alert: [],
                error: [],
                important: [],
                warning: [],
                success: [],
                order: [],
                info: [],
                other: [],
            },
            toggled: {
                alert: false,
                error: false,
                important: false,
                warning: false,
                success: false,
                order: false,
                info: false,
                other: false,
            },
            translations: {
                "Show more": this.$t("Show more"),
                "Show less": this.$t("Show less"),
            },
        };
    },
    computed: {
        filteredNotifications() {
            return Object.keys(this.notifications).reduce((acc, key) => {
                if (this.notifications[key].length) {
                    acc[key] = this.notifications[key];
                }
                return acc;
            }, {});
        },
    },
    methods: {
        async toggleOpenNotifications(key) {
            this.toggled[key] = !this.toggled[key];
        },
        removeAllNotifications(key) {
            let interval;
            interval = setInterval(() => {
                if (this.notifications[key].length === 0) {
                    clearInterval(interval);
                    return;
                }
                this.notifications[key].pop();
            }, 30);
        },
        handleLogNotification({type, data}) {
            if (this.allowedNotifications[type]) {
                this.notifications[type] ??= [];
                this.notifications[type].unshift(data);
            }
        },
    },
    created() {
        EventBus.on("logNotification", this.handleLogNotification);
    },
    watch: {
        notifications: {
            deep: true,
            handler(value) {
                let keys = Object.keys(value);
                keys.forEach((key) => {
                    if (value[key]?.length < 3) {
                        this.toggled[key] = false;
                    }
                });
            },
        },
    },
};
</script>
<style lang="scss">
@use './scss/style.scss' as *;
</style>
