<template>
        <span v-if="mode === 'popover' ? true : !isEditing" @click="startEditing" class="editable-field" :id="id" ref="popoverLink">
            <span v-html="editedValue"></span>
            <i v-show="useCtrl ? ctrlPressed : true" class="fas fa-pencil-alt"></i>
        </span>

        <span v-else-if="isEditing && mode === 'inline'">
            <b-form-group :label="label" v-slot="{ ariaDescribedby }" class="mt-3 mb-0" v-if="prefix">
                <b-form-radio-group
                        v-model="prefixValue"
                        :options="prefixOptions"
                        :aria-describedby="ariaDescribedby"
                        label="Label"
                        :name="label"
                        :stacked="false"
                ></b-form-radio-group>
            </b-form-group>

            <b-input-group>
                <b-form-textarea
                        v-if="type === 'textarea'"
                        v-model="editedValue"
                        @keyup.enter="save" ref="inlineInput"
                ></b-form-textarea>
                <b-form-input
                        v-else
                        v-model="editedValue"
                        @keyup.enter="save" ref="inlineInput"
                ></b-form-input>
                <b-button @click.prevent="save" variant="success"><i class="fa fa-check"></i></b-button>
                <b-button @click.prevent="cancel" variant="danger"><i class="fa fa-times"></i></b-button>
            </b-input-group>
        </span>
    <b-popover
            :target="id"
            triggers="click"
            placement="top"
            offset="0, 10"
            custom-class="popover-editable"
            v-if="mode === 'popover'"
    >

        <b-form-group :label="label" v-slot="{ ariaDescribedby }" class="mt-3 mb-0" v-if="prefix">
            <b-form-radio-group
                    v-model="prefixValue"
                    :options="prefixOptions"
                    :aria-describedby="ariaDescribedby"
                    label="Label"
                    :name="label"
                    :stacked="false"
            ></b-form-radio-group>
        </b-form-group>

        <b-input-group>
            <b-form-textarea
                v-if="type === 'textarea'"
                v-model="editedValue"
                @keyup.enter="save" ref="popoverInput"
            ></b-form-textarea>
            <b-form-input
                v-else
                v-model="editedValue"
                @keyup.enter="save" ref="popoverInput"
            ></b-form-input>
            <b-button @click.prevent="save" variant="success"><i class="fa fa-check"></i></b-button>
            <b-button @click.prevent="cancel" variant="danger"><i class="fa fa-times"></i></b-button>
        </b-input-group>
    </b-popover>
</template>

<script>
import _ from 'lodash'

export default {
    name: 'EditableButton',
    props: {
        modelValue: {
            type: String,
            required: true,
        },
        label: {
            type: String,
            required: true,
        },
        prefix: {
            type: String,
            required: false,
        },
        mode: {
            type: String,
            required: false,
            default: 'inline'
        },
        type: {
            type: String,
            required: false,
            default: 'input'
        },
        autoClose: {
            type: Boolean,
            required: false,
            default: true
        },
        useCtrl: {
            type: Boolean,
            required: false,
            default: true
        },
        usePrefix: {
            type: Boolean,
            required: false,
            default: false
        }
    },
    mounted() {
        this.original = _.clone(this.modelValue)

        document.addEventListener('keydown', this.handleKeyDown);
        document.addEventListener('keyup', this.handleKeyUp);
    },
    beforeDestroy() {
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
    },
    data() {
        return {
            isEditing: false,
            editedValue: this.modelValue,
            original: '',
            id: this.generateUniqueId(),
            ctrlPressed: false,
            prefixValue: this.usePrefix,
            prefixOptions: [
                {value:false, text: this.label},
                {value:true, text: `${this.prefix}.${this.prefix}.${this.label}`},
            ]
        };
    },
    methods: {
        handleKeyDown(event) {
            if (event.key === 'Control') {
                this.ctrlPressed = true;
            }
        },

        handleKeyUp(event) {
            if (event.key === 'Control') {
                this.ctrlPressed = false;
            }
        },
        startEditing(event) {
            if (this.useCtrl ? !event.ctrlKey : false) {
                return;
            }

            this.isEditing = true;

            if(this.mode === 'inline') {
                this.$nextTick(() => this.$refs.inlineInput.focus());
            } else if(this.mode === 'popover') {
                this.$nextTick(() => this.$refs.popoverInput.focus());
            }
        },
        stopEditing() {
            this.isEditing = false;
            this.$emit('update:modelValue', this.editedValue);
            if(this.autoClose && this.mode === 'popover') {
                this.$refs.popoverLink.click();
            }
        },
        cancel() {
            this.isEditing = false;
            this.editedValue = this.original;
            this.stopEditing()
        },
        save() {
            this.isEditing = false;
            this.stopEditing()
            if(this.original !== this.editedValue) {
                this.$emit('save', this.editedValue)
            }
            this.original = _.clone(this.editedValue)
        },
        generateUniqueId() {
            const timestamp = new Date().getTime();

            const randomNumber = Math.floor(Math.random() * 1000);
            const randomHex = randomNumber.toString(16);

            return `rId_${timestamp}_${randomHex}`;
        }
    },
    watch: {
        prefixValue(value) {
            this.$emit('update:use-prefix', value)
        }
    },
    emits: ['save', 'update:modelValue', 'update:use-prefix']
};
</script>

<style scoped>
.editable-field {
    cursor: pointer;
    position: relative;
}

.editable-field i {
    position: absolute;
    right: -20px;
    top: 0;
    cursor: pointer;
    font-size: 0.75rem;
    display: none;
}

.editable-field:hover i {
    display: block;
}
</style>

<style>
.popover-editable {
    min-width: 50%;
}
@media screen and (max-width: 768px) {
    .popover-editable {
        min-width: 350px;
    }
}
</style>