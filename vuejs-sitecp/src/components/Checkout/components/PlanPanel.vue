<template>
    <b-modal
        id="plan-sidebar"
        class="modal-right"
        :size="Array.isArray(result) && !this.plans?.length ? 'xl' : 'xll'"
        :no-footer="buyFiltered.length === 0"
        v-model="openModal"
        header-class="edit-settings-modal-header"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
    >
        <template #header>
            <div class="col-10">
                <h5 class="modal-title">
                    {{ translations['Plans'] }}
                    <span v-if="plan" class="cc-grey-800"> > {{plan.name}}</span>
                </h5>
            </div>
            <div class="col-2 text-right d-flex justify-content-end align-items-center">
                <button @click.prevent="plan = null" class="btn btn-secondary me-2" v-if="plan">
                    {{ translations['Back'] }}
                </button>
                <b-button variant="ghost" @click.prevent="openModal = false">
                    {{ translations['Close'] }}
                </b-button>
            </div>
        </template>

        <div class="text-center" v-if="loading">
            <Loading :loading="loading" class="app-loader-center" />
        </div>
        <template v-else-if="Array.isArray(result) && !this.plans?.length">
            <ContractInfoPreview />
        </template>
        <template v-else-if="!plan">
            <div v-if="message"
                class="d-flex flex-wrap w-100 info-box info-box-warning border-0 align-items-center justify-content-between rounded-3 p-3 my-3"
            >
                <div
                    class="m-0 d-flex align-items-center justify-content-center w-100 gap-3"
                >
                        <i class="far fa-exclamation-circle fs-5"></i>
                    <p
                        class="m-0 fs-6"
                        v-html="message"
                    >
                    </p>
                </div>
            </div>
            <b-row v-if="!details">
                <b-col>
                    <ul class="tabs-large justify-content-center">
                            <li v-for="(months, index) in billingMonths" class="text-center px-5" :class="{'li-router-link-exact-active': activeTab === index}" >
                                <a
                                    href="#"
                                    class="tabs-large-item w-100"
                                    @click.prevent="() => { activeTab = index }"
                                >
                                    {{ tabTitle(months) }}
                                </a>
                            </li>
                        </ul>
                </b-col>
            </b-row>
            <b-row v-if="!details">
                <b-col>
                    <template v-for="(months, index) in billingMonths">
                        <b-row v-if="activeTab === index" class="g-2 g-md-3">
                            <div class="plans-container-new">
                                    <template v-for="(plan, planIndex) in plans">
                                        <PlanCard
                                            :plan="plan"
                                            :months="months"
                                            :planIndex="planIndex"
                                            :currencyPosition="currencyPosition"
                                            :currencySign="currencySign"
                                            :buyPlan="buyPlan"
                                        />
                                    </template>
                                    <PlanCard v-if="!isGermanyBased" :plan="'custom'" :openMeet="openMeet" />
                                </div>
                            <!-- <template v-for="(plan, planIndex) in plans">
                                <b-col md="4" cols="12" v-if="plan.details[months]">
                                    <b-card class="pricing-card">
                                        <div class="d-flex align-items-start justify-content-between">
                                            <h5 class="plan-name" v-html="plan.name"></h5>
                                            <span v-if="planIndex === 1"
                                                  class="cc-badge-status cc-tag-status--enabled popular-badge"
                                            >
                                                {{ translations['Popular'] }}
                                            </span>
                                        </div>
                                        <div class="plan-price">
                                            <sup class="price-sign me-1" v-html="currencySign" v-if="currencyPosition === 'before'"></sup>
                                            <span class="price" v-html="calculatePriceForMonth(plan, months)"></span>
                                            <sup class="price-sign ms-1" v-html="currencySign" v-if="currencyPosition !== 'before'"></sup>
                                            <span class="price-billing-period">
                                                / {{ translations['month'] }}
                                            </span>
                                        </div>
                                        <div class="price-description pricing-text" v-if="months !== 1">
                                            {{
                                                plan.details[months]?.price_without_vat_formatted + ' ' + plan.details[months]?.billing_period_formatted
                                            }}
                                        </div>
                                        <div class="price-save text text-danger" v-if="plan.details[months]?.price_save_input">
                                            {{
                                                $trp(translations['(Save {amount})'], {
                                                    amount: money(plan.details[months]?.price_save_input)
                                                })
                                            }}
                                        </div>
                                        <div><small>{{ translations['*The quoted prices are exclusive of VAT'] }}</small></div>

                                        <hr class="mx-auto"/>

                                        <a href="#"
                                           class="btn w-100 mt-3"
                                           :class="planIndex === 2 ? 'btn-default' : 'btn-primary'"
                                           @click.prevent="buyPlan(plan, months)"
                                        >
                                            {{
                                                this.serverSettings('site.plan.mapping') === plan.mapping ? translations['Current plan'] : $trp(translations['Choose {plan}'], {plan: plan.name})
                                            }}
                                        </a>
                                    </b-card>
                                </b-col>
                            </template> -->
                        </b-row>
                    </template>
                </b-col>
            </b-row>
            <b-row class="mt-4">
                <b-col class="col-12 text-center d-block d-lg-none mb-3">
                        <p class="label-600-24px">{{ filteredPlansByMonths?.length > 1 ? translations['Compare plans'] :translations['Plan details'] }}</p>
                    </b-col>
                <b-col>
                    <div class="plan-features-head" v-if="!details">
                            <b-row class="justify-content-end">
                                <b-col :lg="12 - (filteredPlansByMonths.length * 2)" :cols="3" class="align-self-center d-none d-lg-flex">
                                    <span class="compare-title">
                                        {{ filteredPlansByMonths?.length > 1 ? translations['Compare plans'] :translations['Plan details'] }}
                                    </span>
                                </b-col>
                                    <b-col lg="2" :cols="filteredPlansByMonths?.length === 1 ? '12' : 3" class="text-center d-flex flex-column justify-content-end" :class="resolveSelectionPlanPlacement" v-for="(plan, planIndex) in filteredPlansByMonths">
                                        <h5 class="plan-name" v-html="plan.name"></h5>
                                        <div class="plan-price pricing-text mb-2 d-none d-lg-block">
                                            <p class="price-billing-period m-0">
                                                {{ tabTitle(billingMonths[activeTab]) }}
                                            </p>
                                            <span class="price-sign" v-html="currencySign" v-if="currencyPosition === 'before'"></span>
                                            <span class="price" v-html="calculatePriceForMonth(plan, billingMonths[activeTab])"></span>
                                            <span class="price-sign" v-html="currencySign" v-if="currencyPosition !== 'before'"></span>
                                            <span> / {{ translations['month'] }}</span>
                                        </div>
                                        <a href="#"
                                            class="btn btn-primary"
                                            @click.prevent="buyPlan(plan, months)"
                                        >
                                            {{ translations['Choose'] }}
                                        </a>
                                    </b-col>
                            </b-row>
                        </div>
                    <b-row v-for="group in planFeatureGroups">
                        <b-col>
                            <div class="plan-feature-row-title c-pointer" :class="{'bordered-close': !visibleFeatures[group.mapping]}" @click="visibleFeatures[group.mapping] = !visibleFeatures[group.mapping]">
                                <b-row>
                                    <b-col cols="6">
                                        <span v-html="group.name" class="group-name"></span>
                                    </b-col>
                                    <b-col cols="6" class="text-end">
                                        <a href="javascript:;">
                                            {{visibleFeatures[group.mapping] ? translations['Hide full list'] : translations['Show full list'] }}
                                            <i class="fal ms-1" :class="visibleFeatures[group.mapping] ? 'fa-angle-up' : 'fa-angle-down'"></i>
                                        </a>
                                    </b-col>
                                </b-row>
                            </div>
                            <Vue3SlideUpDown v-model="visibleFeatures[group.mapping]" :duration="200">
                                <template v-for="(feature, index) in group.features">
                                        <div class="plan-feature-row" :class="{ 'feature-row-top-radius': index === 0, 'feature-row-bottom-radius': index === group.features?.length - 1 }">
                                            <b-row class="d-block d-lg-none mb-4">
                                                <b-col v-html="feature.name" class="label-500"></b-col>
                                            </b-row>
                                            <b-row class="justify-content-end">
                                                <b-col :lg="resolveNameFeatureWidth" :cols="3" v-html="feature.name" class="d-none d-lg-block" ></b-col>
                                                <b-col lg="2" :cols="resolveValueFeatureWidth" v-for="plan in filteredPlansByMonths?.length ? filteredPlansByMonths : plans" class="text-center d-flex align-items-center" :class="resolveValuePlacement">
                                                    <component
                                                        :is="showFeature(feature, plan.restrictions[feature.mapping]?.value)"
                                                        :feature="feature"
                                                        :value="plan.restrictions[feature.mapping]?.value"
                                                    ></component>
                                                </b-col>
                                            </b-row>
                                        </div>
                                        <hr class="m-0" v-if="index !== group.features.length - 1" />
                                    </template>
                            </Vue3SlideUpDown>
                        </b-col>
                    </b-row>
                </b-col>
            </b-row>
        </template>
        <template v-else>
            <b-row>
                <b-col>
                    <b-card>
                        <RadioComponent
                            v-model="months"
                            :name="'stock'"
                            :options="stepTwoOptionsForMonths"
                            :stacked="false"
                        ></RadioComponent>
                    </b-card>
                </b-col>
            </b-row>
            <b-row v-if="recommended?.services?.length" class="mt-4">
                <b-col>
                    <div class="recommended-services">
                        <div class="plan-feature-row">
                            <div class="plan-feature-row-title-services">
                                <h5 class="group-name mb-4">{{ translations['Recommended services'] }}</h5>
                            </div>
                            <template v-for="(service, index) in recommended.services">
                                <b-row>
                                    <b-col>
                                        <CheckboxComponent
                                            v-model:value="services[service.id]"
                                            :label="service.name_translated"
                                        />
                                    </b-col>
                                    <b-col class="text-end service-price">
                                        {{$trp(translations['{recommended_price} / {period}'], {
                                            recommended_price: money(service.price_without_vat_input),
                                            period: billingPeriod(service.billing_period)
                                        })}}
                                    </b-col>
                                </b-row>

                                <b-row>
                                    <b-col md="8">
                                        <EllipsisWithMarkdown
                                            v-model="service.description_translated"
                                            class="service-description"
                                            :read-more="translations['Show more']"
                                            :read-less="translations['Show less']"
                                        />
                                    </b-col>
                                </b-row>
                                <hr class="my-4" v-if="index < recommended?.services?.length - 1" />
                            </template>
                        </div>
                    </div>
                </b-col>
            </b-row>
            <b-row v-if="recommended?.apps?.length">
                <b-col>
                    <div class="recommended-apps">
                        <div class="plan-feature-row-title-apps">
                            <h5 class="group-name mb-4">{{ translations['Recommended applications'] }}</h5>
                        </div>
                        <div class="plan-feature-row">
                            <template v-for="(app, index) in recommended.apps">
                                <b-row>
                                    <b-col>
                                        <CheckboxComponent
                                            v-model:value="applications[app.key]"
                                            :label="app.name"
                                        />
                                    </b-col>
                                    <b-col class="text-end service-price">
                                        {{$trp(translations['{recommended_price} / {period}'], {
                                            recommended_price: money(app.price_without_vat_input),
                                            period: billingPeriod(app.billing_period)
                                        })}}
                                    </b-col>
                                </b-row>
                                <hr class="my-4" v-if="index < recommended?.apps?.length - 1" />
                            </template>
                        </div>
                    </div>
                </b-col>
            </b-row>
        </template>

        <template #footer>
            <b-row>
                <b-col class="d-flex gap-2">
                    <button @click.prevent="buyPanel = true" class="btn btn-primary" :disabled="buyPanel">
                        {{ translations['Proceed to checkout'] }}
                    </button>
                </b-col>
            </b-row>
        </template>
    </b-modal>

    <Checkout
        v-model="buyPanel"
        v-model:records="buyFiltered"
        @success="(result) => {
            // this.buyPanel = false
            this.openModal = false
            this.successState = result
            this.$emit('success', result);
        }"
    ></Checkout>
</template>

<script>
import useSharedPlanPanelState from "../js/useSharedPlanPanelState";
import useSharedBookMeeting from "../../BookMeeting/composables/useSharedBookMeeting";

import Loading from "./../../Loading";
import ContractInfoPreview from "../../../CcModules/Base/Plans/Helpers/ContractInfoPreview.vue";
import Plan from "./../js/PlanModel";
import {Checkout} from "./../../Checkout";
import Bool from "./Helpers/FeatureDisplay/Bool";
import Storage from "./Helpers/FeatureDisplay/Storage";
import Fee from "./Helpers/FeatureDisplay/Fee";
import Int from "./Helpers/FeatureDisplay/Int";
import RadioComponent from "@components/Form/RadioComponent";
import CheckboxComponent from "@components/Form/CheckboxComponent";
import {money} from "@js/numberFormatters";
import { Vue3SlideUpDown } from "vue3-slide-up-down";
import EllipsisWithMarkdown from "@components/EllipsisWithMarkdown";
import PlanCard from "../../../CcModules/Base/Plans/Helpers/PlanCard";
import { resolve } from "path";

export default {
    name: "PlanPanel",
    components: {
        ContractInfoPreview,
        Loading,
        Checkout,
        RadioComponent,
        CheckboxComponent,
        Vue3SlideUpDown,
        EllipsisWithMarkdown,
        PlanCard
    },
    props: {
        modelValue: {
            required: true,
            type: Boolean,
            default: false,
        },
        details: {
            required: false,
            type: String,
            default: null,
        },
    },
    setup() {
        const { openModal, successState, message } = useSharedPlanPanelState();
        const { handleMeetOpen } = useSharedBookMeeting();

        return {
            openModal,
            successState,
            message,
            handleMeetOpen
        };
    },
    data() {
        return {
            translations: {
                'Compare plans': this.$t("Compare plans"),
                'Hide full list': this.$t("Hide full list"),
                'Show full list': this.$t("Show full list"),
                'Back': this.$t("Back"),
                'Close': this.$t("Close"),
                'Plans': this.$t('Plans'),
                'Plan details': this.$t('Plan details'),
                'Monthly': this.$t('Monthly'),
                'Annually': this.$t('Annually'),
                'Biennially': this.$t('Biennially'),
                '{months} months': this.$t('{months} months'),
                '(Save {amount})': this.$t('(Save {amount})'),
                'Choose {plan}': this.$t('Choose {plan}'),
                'Current plan': this.$t('Current plan'),
                'Choose': this.$t('Choose'),
                'Recommended services': this.$t('Recommended services'),
                'Recommended applications': this.$t('Recommended applications'),
                'Show more': this.$t('Show more'),
                'Show less': this.$t('Show less'),
                'Proceed to checkout': this.$t('Proceed to checkout'),
                '{plan_name} {plan_price} {plan_period} {plan_save}': this.$t('{plan_name} {plan_price} {plan_period} {plan_save}'),
                '{recommended_price} / {period}': this.$t('{recommended_price} / {period}'),
                '*The quoted prices are exclusive of VAT': this.$t('*The quoted prices are exclusive of VAT'),
                'per month': this.$t('per month'),
                'per year': this.$t('per year'),
                'per 2 years': this.$t('per 2 years'),
                'onetime': this.$t('onetime'),
                'month': this.$t('month'),
                'year': this.$t('year'),
                'Popular': this.$t('Popular'),
                'For six months turnover up to {turnover}': this.$t('For six months turnover up to {turnover}'),
                'For six months turnover over {turnover}': this.$t('For six months turnover over {turnover}'),
                'Regular functionalities for good online sales and start of business growth.': this.$t('Regular functionalities for good online sales and start of business growth.'),
                'New growth & expansion opportunities for your stable online business.': this.$t('New growth & expansion opportunities for your stable online business.'),
                'Everything your large business needs for sustainable & international growth.': this.$t('Everything your large business needs for sustainable & international growth.'),
            },
            // openModal: this.modelValue,
            submitLoader: false,
            loading: true,
            model: new Plan(),
            feature: null,
            plans: [],
            planFeatureGroups: [],
            billingMonths: [],
            partnerPlans: false,
            lta: false,
            responseErrors: {},
            recommended: {
                services: [],
                apps: [],
            },
            plan: null,
            planGermany: null,
            months: 1,
            buyPanel: false,
            activeTab: 1, // TODO => REPLARE INDEX WITH KEY
            currencySign: null,
            currencyPosition: 'before',
            services: {},
            applications: {},
            buy: {},
            visibleFeatures: {},
            result: {},
        };
    },
    methods: {
        submit() {
            this.submitLoader = true
        },
        async loadData() {
            this.loading = true;

            try {
                this.result = {};

                if(this.details) {
                    this.result = await this.model.find(this.details);
                } else {
                    this.result = await this.model.all();
                }

                for(let key in this.result) {
                    if(this.$data && this.$data.hasOwnProperty(key)) {
                        this[key] = this.result[key];
                    }
                }
                if(!Array.isArray(this.result)){
                    this.planFeatureGroups.forEach((feature) => {
                        this.visibleFeatures[feature.mapping] = true
                    })
                }

                // if(!this.details) {
                //     this.$Sentry.viewPlans()
                // }

            } catch (err) {
                this.$errorResponse(err);
            } finally {
                this.loading = false;
            }
        },
        buyPlan(plan, months) {
            this.months = months
            if(this.isGermanyBased){
                this.planGermany = plan
                setTimeout(()=>{
                    this.buyPanel = true
                },200)
            } else {
                this.plan = plan
            }
        },
        tabTitle(months) {
            switch (months) {
                case 1:
                    return this.translations['Monthly'];
                case 12:
                    return this.translations['Annually'];
                case 24:
                    return this.translations['Biennially'];
                default:
                    return this.$trp(this.translations[`{months} months`], {months: months});
            }
        },
        planPeriod(months) {
            switch (months) {
                case 1:
                    return this.translations['per month'];
                case 12:
                    return this.translations['per year'];
                case 24:
                    return this.translations['per 2 years'];
                default:
                    return this.$trp(this.translations[`{months} months`], {months: months});
            }
        },
        billingPeriod(period) {
            switch (period) {
                case 'once':
                default:
                    return this.translations['onetime'];
                case 'month':
                    return this.translations['month'];
                case 'year':
                    return this.translations['year'];
            }
        },
        showFeature(feature, value) {
            switch(feature.cast) {
                case 'bool':
                    return Bool;
                case 'storage':
                    return Storage;
                case 'fee':
                    return Fee;
                case 'int':
                    return Int;
                default:
                    return value;
            }
        },
        money(value) {
            return money(value, 2, '.', ',', this.currencyPosition === 'before' ? this.currencySign : '', this.currencyPosition !== 'before' ? this.currencySign : '');
        },
        resetData() {
            this.plan = null
            this.planGermany = null
            this.plans = []
            this.months = 1
            this.activeTab = 1
            this.services = {}
            this.applications = {}
            this.billingMonths = []
            this.currencyPosition = 'before'
            this.currencySign = null
            this.lta = false
            this.partnerPlans = false
            this.planFeatureGroups = false
            this.recommended = {
                services: [],
                apps: [],
            }
            this.buy = {}
            this.visibleFeatures = {}
            this.$emit('update:details', null)
        },
        calculatePriceForMonth(plan, months) {
            return (parseFloat(plan.details[months]?.price_without_vat_input)/months).toFixed(0)
        },
        openMeet(){
            if(this.isGermanyBased){
                this.initiateHubspotMeet()
            } else {
                this.handleMeetOpen()
            }

        },
        initiateHubspotMeet() {
            const scriptId = "hs-script-loader";
            if (!document.getElementById(scriptId)) {
                const script = document.createElement("script");
                script.id = scriptId;
                script.async = true;
                script.defer = true;
                script.src = "//js-eu1.hs-scripts.com/144200173.js";
                document.body.appendChild(script);
                setTimeout(()=>{
                    window.HubSpotConversations.widget.open();
                }, 500)
            } else {
                setTimeout(()=>{
                    window.HubSpotConversations.widget.open();
                }, 500)
            }
        },
    },
    watch: {
        async openModal(newValue, oldValue) {
            // this.$emit('update:modelValue', value)
            if (newValue && !oldValue) {
                await this.loadData();
            } else {
                this.message = null
                this.resetData();
            }
        },
        // async modelValue(value) {
        //     if (value) {
        //         await this.loadData();
        //     } else {
        //         this.message = null
        //         this.resetData();
        //     }
        //     // this.openModal = value
        // },
        months(value) {
            const index = this.billingMonths.indexOf(value);
            if(index > -1) {
                this.activeTab = index

                this.buy.plan = this.planRecord?.details && this.planRecord?.details[value] ? {
                    type: 'plan_details',
                    mapping: this.planRecord?.details[value].id,
                } : null
            }
        },
        applications: {
            deep: true,
            handler(value) {
                Object.keys(value).forEach((key) => {
                    this.buy[`app-${key}`] = value[key] ? {
                        type: 'cloudcart_app',
                        mapping: key,
                    } : null
                })
            }
        },
        services: {
            deep: true,
            handler(value) {
                Object.keys(value).forEach((key) => {
                    this.buy[`service-${key}`] = value[key] ? {
                        type: 'cloudcart_service',
                        mapping: key,
                    } : null
                })
            }
        },
        plan: {
            deep: true,
            handler(value) {
                this.buy.plan = value && value?.details[this.months] ? {
                    type: 'plan_details',
                    mapping: value?.details[this.months].id,
                } : null
            }
        },
        planGermany: {
            deep: true,
            handler(value) {
                this.buy.plan = value && value?.details[this.months] ? {
                    type: 'plan_details',
                    mapping: value?.details[this.months].id,
                } : null
            }
        },
        buyPanel(value){
            if(!value){
                this.planGermany = null;
            }
        }
    },
    computed: {
        resolveNameFeatureWidth(){
            if(this.filteredPlansByMonths.length){
                return 12 - (this.filteredPlansByMonths.length * 2)
            }
           return 12 - this.plans.length * 2
        },
        resolveValueFeatureWidth(){
            if(this.filteredPlansByMonths.length){
                return this.filteredPlansByMonths?.length === 1 ? '12' : 3
            }
            return this.plans?.length === 1 ? '12' : 3
        },
        resolveValuePlacement(){
            if(this.filteredPlansByMonths.length){
                return this.filteredPlansByMonths?.length === 1 ? 'justify-content-end' : 'justify-content-center'
            }
            return this.plans?.length === 1 ? 'justify-content-end' : 'justify-content-center'
        },
        resolveSelectionPlanPlacement(){
            if(this.filteredPlansByMonths.length){
                return this.filteredPlansByMonths?.length === 1 ? 'align-items-end' : 'align-items-center'
            }
            return this.plans?.length === 1 ? 'align-items-end' : 'align-items-center'
        },
        resolvePossibleOptions(){
            let temp = []
            let currentMonths = this.billingMonths[this.activeTab];
            this.plans.forEach((plan) => {
                if(plan.details[currentMonths]){
                    temp.push(currentMonths)
                }
            })
            return temp
        },
        filteredPlansByMonths(){
            return this.plans.filter((plan) => {
                return plan.details[this.billingMonths[this.activeTab]]
            })
        },
        isGermanyBased() {
            return this.serverSettings('siteUser.issuer_company_id') === 7;
            return this.serverSettings('siteUser.issuer_company_id') === 5; // BG FOR CHECK ONLY
        },
        planRecord() {
            return this.plan || {};
        },
        stepTwoOptionsForMonths() {
            return Object.values(this.planRecord.details || []).map((detail) => {
                return {
                    text: this.$trp(this.translations['{plan_name} {plan_price} {plan_period} {plan_save}'], {
                        plan_name: detail?.plan?.name || detail?.name?.en,
                        plan_price: detail.price_without_vat_formatted,
                        plan_period: this.planPeriod(detail.billing_months),
                        plan_save: detail.price_save ? this.$trp(this.translations['(Save {amount})'], {
                            amount: this.money(detail.price_save_input)
                        }) : '',
                    }),
                    value: detail.billing_months,
                    props: {
                        inline: false
                    }
                }
            })
        },
        buyFiltered() {
            return Object.values(this.buy).filter((value) => {
                return value !== null;
            })
        }
    },
    emits: ['update:modelValue', 'success', 'update:details'],
};
</script>
<style lang="scss">
@use "../scss/pricing.scss" as *;
</style>
