import {defineConfig, loadEnv} from 'vite';
import {nodePolyfills} from 'vite-plugin-node-polyfills';
import vue from '@vitejs/plugin-vue';
import commonjs from 'vite-plugin-commonjs';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';
import laravel from 'laravel-vite-plugin';
import vueDevTools from 'vite-plugin-vue-devtools';
import AutoImport from 'unplugin-auto-import/vite'

import Components from 'unplugin-vue-components/vite'
import {BootstrapVueNextResolver} from 'bootstrap-vue-next'
import {exec} from 'child_process';

export default ({mode}) => {

    dotenv.config({path: path.resolve(process.cwd(), '.env')})

    process.env = {...process.env, ...loadEnv(mode, process.cwd())}

    const outDir = mode === 'production' ? 'vuejs' : 'vuejs-dev'

    process.env.NODE_ENV = mode

    console.log('Mode:', process.env.NODE_ENV);
    console.log('Output Dir:', path.resolve(__dirname, `../public/admin/${outDir}`));
    console.log('VITE on PORT', process.env.VITE_DEV_SERVER_PORT || 5174);

    return defineConfig({
        base: process.env.VITE_PUBLIC_PATH || '/',
        root: '.',
        // root: path.resolve(__dirname, 'vuejs-sitecp'),
        resolve: {
            alias: {
                '@components': path.resolve(__dirname, 'src/components'),
                '@mixins': path.resolve(__dirname, 'src/mixins'),
                '@js': path.resolve(__dirname, 'src/js'),
                '@': path.resolve(__dirname, 'src'),
                '~': path.resolve(__dirname, 'node_modules'),
            },
            extensions: ['.js', '.vue', '.json', '.css', 'scss'],
        },
        optimizeDeps: {
            include: [
                "@fawmi/vue-google-maps",
                "fast-deep-equal",
            ],
        },
        build: {
            minify: mode === 'production',
            manifest: 'manifest.json',
            outDir: path.resolve(__dirname, `../public/admin/${outDir}`),
            emptyOutDir: true,
            commonjsOptions: {
                transformMixedEsModules: true,
            },
            chunkSizeWarningLimit: 2000,
            rollupOptions: {
                input: {
                    app: path.resolve(__dirname, 'src/js/__main.js'),
                    style: path.resolve(__dirname, 'src/sass/__main.scss'),
                },
                output: {
                    entryFileNames: 'js/[name]-[hash].js',
                    chunkFileNames: 'components/[name]-[hash].js',
                    assetFileNames: (assetInfo) => {
                        if (assetInfo?.name?.endsWith('.css')) {
                            return 'css/app-[hash].css';
                        }
                        return 'assets/[name]-[hash][extname]';
                    },
                    manualChunks(id) {
                        if (id.includes('Checkout')) {
                            return 'checkout';
                        }
                        if (id.includes('XmlSync')) {
                            return 'xmlsync';
                        }
                    }
                },
            },
        },
        server: {
            cors: true,
            host: '0.0.0.0',
            port: process.env.VITE_DEV_SERVER_PORT || 5174,
            hmr: {
                protocol: 'wss',
                port: process.env.VITE_DEV_SERVER_PORT || 5174,
                clientPort: process.env.VITE_DEV_SERVER_PORT || 5174,
                host: process.env.VITE_DEV_SERVER_HOST || 'localhost',
                path: `/admin/${outDir}/`,
                force: true,
                verbose: true,
                overlay: true,
            },
            watch: {
                usePolling: true,
                ignored: ['node_modules'],
                // interval: 100,
            },
            https: process.env.VITE_DEV_SERVER_HTTPS_KEY && process.env.VITE_DEV_SERVER_HTTPS_CERT
                ? {
                    key: fs.readFileSync(process.env.VITE_DEV_SERVER_HTTPS_KEY),
                    cert: fs.readFileSync(process.env.VITE_DEV_SERVER_HTTPS_CERT),
                }
                : false,
        },
        define: {
            'process': process,
        },
        css: {
            preprocessorOptions: {
                scss: {
                    quietDeps: true,
                }
            }
        },
        plugins: [
            AutoImport({
                include: [
                    /\.[tj]sx?$/,
                    /\.vue$/,
                    /\.vue\?vue/,
                    /\.md$/
                ],
                imports: [
                    'vue',
                    'vue-router',
                    'vue-i18n',
                    '@vueuse/core',
                    {
                        'lodash': ['_'],
                    }
                ]
            }),
            nodePolyfills(),
            vueDevTools(),
            vue({
                template: {
                    transformAssetUrls: {
                        base: null,
                        includeAbsolute: false,
                    },
                },
            }),
            Components({
                resolvers: [BootstrapVueNextResolver()],
            }),
            commonjs({
                include: ['vue-datepicker-next', 'bootstrap-vue-next', 'vue-country-flag-next'],
            }),
            laravel({
                buildDirectory: path.resolve(__dirname, `../public/admin/${outDir}`),
                input: ['src/sass/__main.scss', 'src/js/__main.js'],
                // refresh: true,
            }),
            {
                name: 'after-build-plugin',
                apply: 'build',
                closeBundle() {
                    const timestamp = Date.now();
                    exec(`echo ${timestamp} > ./../public/admin/vuejs/last-compiled.txt && git add ./../public/admin/vuejs/.`,
                        (error, stdout, stderr) => {
                            if (error) {
                                console.error(`Error executing command: ${error.message}`);
                            }
                            if (stderr) {
                                console.error(`stderr: ${stderr}`);
                            }
                            console.log(`stdout: ${stdout}`);
                        }
                    );
                }
            }
        ],
    })
}
