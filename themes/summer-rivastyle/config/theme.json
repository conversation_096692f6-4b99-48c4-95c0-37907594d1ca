{"name": "summer", "version": "0.1", "page_builder": true, "assets": {"default_images": {"logo": "{$img_url}/themes/summer/img/defaults/logo.png", "favicon": "", "product": {"50": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "100": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "150": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "300": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "600": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "800": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "1024": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "1280": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "1920": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png"}, "category": {"50": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "100": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "150": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "300": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "600": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "800": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "1024": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "1280": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "1920": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png"}, "vendor": {"50": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "100": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "150": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "300": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "600": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "800": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "1024": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "1280": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png", "1920": "{$img_url}/themes/summer/img/defaults/noimage/no-image.png"}}, "default_content_images": {"product": ["{$img_url}/themes/summer/img/defaults/products/product-01.jpg", "{$img_url}/themes/summer/img/defaults/products/product-02.jpg", "{$img_url}/themes/summer/img/defaults/products/product-03.jpg", "{$img_url}/themes/summer/img/defaults/products/product-04.jpg", "{$img_url}/themes/summer/img/defaults/products/product-05.jpg", "{$img_url}/themes/summer/img/defaults/products/product-06.jpg", "{$img_url}/themes/summer/img/defaults/products/product-07.jpg", "{$img_url}/themes/summer/img/defaults/products/product-08.jpg", "{$img_url}/themes/summer/img/defaults/products/product-09.jpg", "{$img_url}/themes/summer/img/defaults/products/product-10.jpg", "{$img_url}/themes/summer/img/defaults/products/product-11.jpg", "{$img_url}/themes/summer/img/defaults/products/product-12.jpg", "{$img_url}/themes/summer/img/defaults/products/product-13.jpg", "{$img_url}/themes/summer/img/defaults/products/product-14.jpg"], "category": ["{$img_url}/themes/summer/img/defaults/categories/category1.jpg", "{$img_url}/themes/summer/img/defaults/categories/category2.jpg", "{$img_url}/themes/summer/img/defaults/categories/category3.jpg", "{$img_url}/themes/summer/img/defaults/categories/category4.jpg", "{$img_url}/themes/summer/img/defaults/categories/category5.jpg", "{$img_url}/themes/summer/img/defaults/categories/category6.jpg", "{$img_url}/themes/summer/img/defaults/categories/category7.jpg", "{$img_url}/themes/summer/img/defaults/categories/category8.jpg"], "vendor": ["{$img_url}/themes/summer/img/defaults/brands/brand1.png", "{$img_url}/themes/summer/img/defaults/brands/brand2.png", "{$img_url}/themes/summer/img/defaults/brands/brand3.png", "{$img_url}/themes/summer/img/defaults/brands/brand4.png", "{$img_url}/themes/summer/img/defaults/brands/brand5.png", "{$img_url}/themes/summer/img/defaults/brands/brand6.png"]}}, "functions": {"discount": {"color": {"status": true}}}, "widgets": {"productInBundles": {"map": "product.productInBundles", "name": {"en": "Product in bundles", "bg": "Продукт в пакети"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "per_row": 4}}}, "utilities": {"map": "base.utilities"}, "logo": {"map": "extra.logo", "editable": "no"}, "search": {"map": "extra.search"}, "userControls": {"map": "user.controls"}, "wishlistMenu": {"map": "wishlist.menu", "name": {"en": "Menu for latest favorites products", "bg": "Меню за последни любими продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "limit": 10}}}, "wishlist": {"map": "wishlist.listing"}, "productCompare": {"map": "product.compare"}, "navigationMain": {"map": "navigation.main"}, "navigationFooter": {"map": "navigation.footer"}, "headerText": {"map": "extra.text", "name": {"en": "Header Text", "bg": "<PERSON><PERSON><PERSON><PERSON><PERSON> текст"}, "settings": {"defaults": {"title": "", "text": "<p><img class=\"alignleft\" src=\"{$img_url}/themes/summer/img/defaults/other/phone.png\" alt=\"\">Need help? Call us today <br><span style=\"font-size: 24px; color: #fff;\">0700 18 338</span></p>"}}}, "footerText": {"map": "extra.text", "name": {"en": "Footer Text", "bg": "Футър текст"}, "settings": {"defaults": {"title": "За връзка с нас", "text": "<p>Адрес: гр<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ул.Георг<PERSON> Кочев 171 <br> Телефон: 0700 18 338 <br> Еmail: <a href=\"mailto:<EMAIL>\"><EMAIL></a> <br> Skype: SUMMER</p>"}}}, "newsletter": {"map": "mailchimp.newsletter", "settings": {"defaults": {"enabled": false}}}, "carousel": {"map": "extra.carousel", "name": {"en": "Slide<PERSON>", "bg": "Слайдер"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 2, "autoplay": "yes", "interval": 5000, "caption": "no", "controls": "yes", "indicators": "no", "pause": "no", "cycle": "no", "slides": {"1": {"img_type": "external", "src": "{$img_url}/themes/summer/img/defaults/carousel/slide1.jpg", "link_type": false, "link_value": false, "link_caption": "Shop now", "html": "<p>SPRING/SUMMER COLLECTION</p><h2 style=\"font-weight: 900;\">NEW ARRIVALS <br> <span style=\"color: #fff;\">70% OFF</span></h2>", "horizontal_position": "left", "vertical_position": "middle", "target": "_self"}, "2": {"img_type": "external", "src": "{$img_url}/themes/summer/img/defaults/carousel/slide2.jpg", "link_type": false, "link_value": false, "link_caption": "Shop now", "html": "<p>FOR THE FUTURE SEASON</p><h2 style=\"font-weight: 900; color: #00bcd4;\">AUTUMN COLORS <br> <span style=\"color: #fff;\">40% OFF</span> <br><br></h2>", "horizontal_position": "left", "vertical_position": "bottom", "target": "_self"}}}}}, "textbox1": {"map": "extra.text", "name": {"en": "Text Box 1", "bg": "Текстова кутийка 1"}, "settings": {"defaults": {"title": "", "text": "<p class=\"text-aligncenter\"><img src=\"{$img_url}/themes/summer/img/defaults/other/text-box1.png\" alt=\"\"></p><h5 class=\"text-aligncenter\">RETURN & EXCHANGE</h5>"}}}, "textbox1_tooltip": {"map": "extra.text", "name": {"en": "Text Box 1 - tooltip", "bg": "Текстова кутийка 1 - подсказка"}, "settings": {"defaults": {"title": "", "text": "<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod. Lorem ipsum dolor sit amet, consectetur</p>"}}}, "textbox2": {"map": "extra.text", "name": {"en": "Text Box 2", "bg": "Текстова кутийка 2"}, "settings": {"defaults": {"title": "", "text": "<p class=\"text-aligncenter\"><img src=\"{$img_url}/themes/summer/img/defaults/other/text-box2.png\" alt=\"\"></p><h5 class=\"text-aligncenter\">FREE SHIPPING</h5>"}}}, "textbox2_tooltip": {"map": "extra.text", "name": {"en": "Text Box 2 - tooltip", "bg": "Текстова кутийка 2 - подсказка"}, "settings": {"defaults": {"title": "", "text": "<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod. Lorem ipsum dolor sit amet, consectetur</p>"}}}, "textbox3": {"map": "extra.text", "name": {"en": "Text Box 3", "bg": "Текстова кутийка 3"}, "settings": {"defaults": {"title": "", "text": "<p class=\"text-aligncenter\"><img src=\"{$img_url}/themes/summer/img/defaults/other/text-box3.png\" alt=\"\"></p><h5 class=\"text-aligncenter\">MEMBER DISCOUNT</h5>"}}}, "textbox3_tooltip": {"map": "extra.text", "name": {"en": "Text Box 3 - tooltip", "bg": "Текстова кутийка 3 - подсказка"}, "settings": {"defaults": {"title": "", "text": "<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod. Lorem ipsum dolor sit amet, consectetur</p>"}}}, "textbox4": {"map": "extra.text", "name": {"en": "Text Box 4", "bg": "Текстова кутийка 4"}, "settings": {"defaults": {"title": "", "text": "<p class=\"text-aligncenter\"><img src=\"{$img_url}/themes/summer/img/defaults/other/text-box4.png\" alt=\"\"></p><h5 class=\"text-aligncenter\">SHOPPING SAFETY</h5>"}}}, "textbox4_tooltip": {"map": "extra.text", "name": {"en": "Text Box 4 - tooltip", "bg": "Текстова кутийка 4 - подсказка"}, "settings": {"defaults": {"title": "", "text": "<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod. Lorem ipsum dolor sit amet, consectetur</p>"}}}, "text1": {"map": "extra.text", "name": {"en": "Homepage text 1", "bg": "Текст на началната страница 1"}, "settings": {"defaults": {"title": "", "text": "<p><img class=\"alignleft\" src=\"{$img_url}/themes/summer/img/defaults/other/text1.png\" alt=\"\"></p><h2 class=\"text-aligncenter\"><br>GIANT <strong style=\"color: #00bcd4\">SALE</strong></h2><p class=\"text-aligncenter\">WORKS EVERY DAY DURING THIS HOLIDAYS SEASON</p><h2 class=\"text-aligncenter\" style=\"margin-bottom: 0;\"><strong style=\"color: #00bcd4;\">70% OFF</strong></h2>"}}}, "text1Background": {"map": "extra.backgroundImage", "name": {"en": "Homepage text 1 - background", "bg": "Текст на началната страница 1 - фон"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"type": "external", "src": "{$img_url}/themes/summer/img/defaults/other/bg-about.jpg"}}}, "text2": {"map": "extra.text", "name": {"en": "Homepage text 2", "bg": "Текст на началната страница 2"}, "settings": {"defaults": {"title": "", "text": "<h2 class=\"text-aligncenter\">ENJOY THE NEW <br><strong>FASHION EXPERIENCE</strong></h2> <p class=\"text-aligncenter\">LOREM IPSUM IS SIMPLY DUMMY TEXT OF THE PRINTING AND TYPESETTING INDUSTRY. LOREM IPSUM HAS BEEN <br> THE INDUSTRY'S STANDARD DUMMY TEXT EVER SINCE THE 1500S, WHEN.</p><br><p class=\"text-aligncenter\"><a href=\"#\" class=\"_button\">READ MORE</a><p>"}}}, "text2Background": {"map": "extra.backgroundImage", "name": {"en": "Homepage text 2 - background", "bg": "Текст на началната страница 2 - фон"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": false, "type": "external", "src": "{$img_url}/themes/summer/img/defaults/other/bg-about.jpg"}}}, "text3": {"map": "extra.text", "name": {"en": "Homepage text 3", "bg": "Текст на началната страница 3"}, "settings": {"defaults": {"title": "", "text": "<h2 class=\"text-aligncenter\">ENJOY THE NEW <br><strong style=\"color: #00bcd4;\">FASHION EXPERIENCE</strong></h2> <p class=\"text-aligncenter\">LOREM IPSUM IS SIMPLY DUMMY TEXT OF THE PRINTING AND TYPESETTING INDUSTRY. LOREM IPSUM HAS BEEN <br> THE INDUSTRY'S STANDARD DUMMY TEXT EVER SINCE THE 1500S, WHEN.</p><br><p class=\"text-aligncenter\"><a href=\"#\" class=\"_button\">READ MORE</a><p>"}}}, "text3Background": {"map": "extra.backgroundImage", "name": {"en": "Homepage text 3 - background", "bg": "Текст на началната страница 3 - фон"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": false, "type": "external", "src": "{$img_url}/themes/summer/img/defaults/other/bg-about.jpg"}}}, "filters": {"map": "product.filters", "mode": "range_slider", "settings": {"defaults": {"per_row": 4, "per_page": "16", "per_page_options": [16, 32, 64, 96], "products_price_ranges": [[100, 100000]], "price_range_step": "500"}}, "name": {"en": "Product listing settings", "bg": "Настройки на продуктов каталог"}, "description": {"en": "", "bg": ""}}, "productsDetails": {"map": "product.productsDetails", "name": {"en": "Detail information for product", "bg": "Детайлна информация за продукт"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {}}}, "showcaseCategories": {"map": "product.showcase", "name": {"en": "Category Showcase", "bg": "Витрина с категории"}, "settings": {"defaults": {"header": "", "type": "category", "amount": 8, "per_row": 4, "show_name": true, "show_description": false}}}, "showcaseBrands1": {"map": "product.showcase", "name": {"en": "Brands Showcase 1", "bg": "Витрина с марки 1"}, "settings": {"defaults": {"header": "", "type": "vendor", "amount": 6, "per_row": 6, "show_name": false, "show_description": false}}}, "showcaseBrands2": {"map": "product.showcase", "name": {"en": "Brands Showcase 2", "bg": "Витрина с марки 2"}, "settings": {"defaults": {"header": "", "type": "vendor", "amount": 6, "per_row": 6, "show_name": true, "show_description": true}}}, "banners1": {"map": "extra.banner", "name": {"en": "Banners 1", "bg": "Банери 1"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 3, "per_row": 3, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/summer/img/defaults/banners/banner1.jpg", "link_type": "external", "link_value": "https://example.com", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/summer/img/defaults/banners/banner2.jpg", "link_type": "external", "link_value": "https://example.com", "caption": "", "target": "_self"}, "3": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/summer/img/defaults/banners/banner3.jpg", "link_type": "external", "link_value": "https://example.com", "caption": "", "target": "_self"}}}}}, "banners2": {"map": "extra.banner", "name": {"en": "Banners 2", "bg": "Банери 2"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 2, "per_row": 2, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/summer/img/defaults/banners/banner4.jpg", "link_type": "external", "link_value": "https://example.com", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/summer/img/defaults/banners/banner5.jpg", "link_type": "external", "link_value": "https://example.com", "caption": "", "target": "_self"}}}}}, "blogHome": {"map": "blog.blog", "name": {"en": "Blog - homepage", "bg": "Блог - начална страница"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": false, "per_page": 6}}}, "showcaseProducts1": {"map": "product.productShowcase", "name": {"en": "Products Showcase 1", "bg": "Витрина с продукти 1"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "title": "New arrivals", "products": 8, "per_row": 4, "sale": "no", "new": "yes"}}}, "showcaseProducts2": {"map": "product.productShowcase", "name": {"en": "Products Showcase 2", "bg": "Витрина с продукти 2"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": false, "title": "Featured products", "products": 8, "per_row": 4, "featured": "yes", "sale": "no", "new": "no"}}}, "showcaseProducts3": {"map": "product.productShowcase", "name": {"en": "Products Showcase 3", "bg": "Витрина с продукти 3"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "title": "Products on SALE", "products": 8, "per_row": 4, "sale": "yes", "new": "no"}}}, "showcaseProducts4": {"map": "product.productShowcase", "name": {"en": "Products Showcase 4", "bg": "Витрина с продукти 4"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": false, "title": "Our best offers", "products": 8, "per_row": 4, "featured": "yes", "sale": "yes", "new": "yes"}}}, "lastViewedProducts": {"map": "product.lastViewed", "name": {"en": "Latest viewed products", "bg": "Последно видяни продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "title": "Last Viewed", "products": 8}}}, "htmlLine": {"map": "extra.htmlLine", "group": "top_bar", "name": {"en": "Promo Bar", "bg": "Промо лента"}, "description": {"en": "Promo Bar", "bg": "Промо лента"}, "settings": {"defaults": {"text": "", "enabled": false, "period": {"from": "", "to": ""}, "button": {"float": "right", "enabled": true, "link": "", "target": "_blank", "text": "View More"}}}}, "showcaseList": {"map": "product.showcaseList"}, "categoryProperties": {"map": "product.categoryProperties", "name": {}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true}}}, "productText": {"map": "extra.text", "name": {"en": "Product details page text", "bg": "Текст в детайлната страница"}, "settings": {"defaults": {"title": "", "text": "<p>Гаранция за физическо лице: <span style=\"color: #333;\">24 м.</span><br> Безплатна доставка <br> Проверка на пратката</p>"}}}, "productsRelated": {"map": "product.related", "name": {"en": "Related products", "bg": "Свързани продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Related products", "type": "category", "enabled": true, "per_row": 4}}}, "blog": {"map": "blog.blog", "name": {"en": "Blog", "bg": "Блог"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"per_page": 5}}}, "page": {"map": "extra.page"}, "navigationLinksPage": {"map": "navigation.links", "name": {"en": "Sidebar menu - Pages", "bg": "Странично меню - Страници"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"links": [{"link_type": "section", "link_value": "blog", "link_caption": "Блог", "target": ""}, {"link_type": "section", "link_value": "faq", "link_caption": "FAQ", "target": ""}]}}}, "vendors": {"map": "product.vendors"}, "googleMap": {"map": "contact.googleMap", "name": {"en": "Google Map", "bg": "<PERSON>у<PERSON><PERSON>л карта"}, "description": {"en": "", "bg": ""}}, "contactForm": {"map": "contact.form"}, "contactInformation": {"map": "contact.information", "name": {"en": "Contact information", "bg": "Контактна информация"}, "description": {"en": "", "bg": ""}}, "authorize": {"map": "user.authorize"}, "social": {"map": "extra.social", "name": {"en": "Social links", "bg": ""}, "description": {"en": "", "bg": ""}}, "providers": {"map": "payment.providers"}, "article": {"map": "blog.article"}, "recentArticles": {"map": "blog.recentArticles", "name": {"en": "Recent articles", "bg": "Последни статии"}, "description": {"en": "", "bg": ""}}, "recentComments": {"map": "blog.recentComments", "name": {"en": "Recent comments", "bg": "Последни коментари"}, "description": {"en": "", "bg": ""}}, "share": {"group": "products", "map": "extra.addThisShare", "name": {"en": "Product share", "bg": "Споделяне на продукт"}, "description": {"en": "This widget allows you to share your products in the social networks.", "bg": "Това разширение активира възможността за споделяне на продукт в социалните мрежи."}, "settings": {"defaults": {"layout": "small"}}}, "leasing": {"map": "store.leasing"}, "checkoutText": {"map": "extra.text", "name": {"en": "Checkout text", "bg": "Завършване на поръчка - текст"}, "settings": {"defaults": {"title": "", "text": "<h6>ДОПЪЛНИТЕЛНА ИНФОРМАЦИЯ</h6> <p>Prices and delivery costs are not confirmed until youe reached the checkout. 30 days withdrawal. Read about our return and refund policy here return and refund.</p>"}}}, "pageLoader": {"map": "extra.backgroundImage", "name": {"en": "Page loader", "bg": "Индикатор за зареждане на страница"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "type": "external", "src": "{$img_url}/themes/summer/img/defaults/logo-dark.png"}}}, "buttonToTop": {"map": "extra.text", "name": {"en": "<PERSON>ton \"To top\"", "bg": "Бутон \"До горе\""}, "settings": {"defaults": {"enabled": false, "title": "", "text": ""}}}}, "globals": {"widgets": {"product": {"listing": {}, "details": {}, "showcase": {"settings": {"defaults": {"products": 4}}}}}}, "settings": {"groups": {"main": {"en": {"name": "Main"}, "bg": {"name": "Основни"}}, "header": {"en": {"name": "Header"}, "bg": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "footer": {"en": {"name": "Footer"}, "bg": {"name": "<PERSON>ут<PERSON>р"}}, "slider": {"en": {"name": "Slide<PERSON>"}, "bg": {"name": "Слай<PERSON>ър"}}, "products-listing": {"en": {"name": "Products listing"}, "bg": {"name": "Списък с продукти"}}, "product-details": {"en": {"name": "Product details"}, "bg": {"name": "Детайли на продукта"}}, "forms": {"en": {"name": "Forms"}, "bg": {"name": "Форми"}}, "buttons": {"en": {"name": "Buttons"}, "bg": {"name": "Бутони"}}, "second": {"en": {"name": "Secondary"}, "bg": {"name": "Второстепенни"}}, "text-boxes": {"en": {"name": "Text boxes - Homepage"}, "bg": {"name": "Текстови кутийки - Начало"}}, "text-sections": {"en": {"name": "Text sections - Homepage"}, "bg": {"name": "Текстови секции - Начало"}}, "promo-bar": {"en": {"name": "Promo Bar"}, "bg": {"name": "Промо лента"}}, "breadcrumb": {"en": {"name": "Breadcrumb"}, "bg": {"name": "Път към страницата"}}, "pagination": {"en": {"name": "Pagination"}, "bg": {"name": "Странициране"}}, "tooltips": {"en": {"name": "Tooltips"}, "bg": {"name": "Подсказки"}}, "labels": {"en": {"name": "Labels"}, "bg": {"name": "Етикети"}}, "popups": {"en": {"name": "Popups"}, "bg": {"name": "Popup-и"}}, "font-main": {"en": {"name": "Main"}, "bg": {"name": "Основни"}}, "font-titles": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}, "font-buttons": {"en": {"name": "Buttons"}, "bg": {"name": "Бутони"}}, "font-product-list": {"en": {"name": "Listing product"}, "bg": {"name": "Продукт в листинга"}}}, "variables": {"color-main-background": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-main-borders": {"type": "color", "group": "main", "default": "#e8e8e8", "translations": {"en": {"name": "Borders and lines"}, "bg": {"name": "Рамки и линии"}}}, "color-main-text": {"type": "color", "group": "main", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-main-secondary-text": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "Text 2"}, "bg": {"name": "Текст 2"}}}, "color-main-meta-text": {"type": "color", "group": "main", "default": "#999", "translations": {"en": {"name": "Meta text"}, "bg": {"name": "Помощен текст"}}}, "color-main-titles": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-main-highlight": {"type": "color", "group": "main", "default": "#00bcd4", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-main-icons": {"type": "color", "group": "main", "default": "#00bcd4", "translations": {"en": {"name": "Icons"}, "bg": {"name": "Иконки"}}}, "color-second-background": {"type": "color", "group": "second", "default": "#f8f8f8", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-second-borders": {"type": "color", "group": "second", "default": "#e8e8e8", "translations": {"en": {"name": "Borders and lines"}, "bg": {"name": "Рамки и линии"}}}, "color-second-text": {"type": "color", "group": "second", "default": "#333", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-second-secondary-text": {"type": "color", "group": "second", "default": "#666", "translations": {"en": {"name": "Secondary text"}, "bg": {"name": "Второстепенен текст"}}}, "color-second-meta-text": {"type": "color", "group": "second", "default": "#999", "translations": {"en": {"name": "Meta text"}, "bg": {"name": "Помощен текст"}}}, "color-second-titles": {"type": "color", "group": "second", "default": "#333", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-second-highlight": {"type": "color", "group": "second", "default": "#00bcd4", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-second-image-box": {"type": "color", "group": "second", "default": "#51546c", "translations": {"en": {"name": "Image box"}, "bg": {"name": "Подложка на картинката"}}}, "color-second-button-background": {"title": {"en": "Primary buttons", "bg": "Основни бутони"}, "separator": "true", "type": "color", "group": "second", "default": "#00bcd4", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-second-button-borders": {"type": "color", "group": "second", "default": "#00bcd4", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-second-button-text": {"type": "color", "group": "second", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-second-button-background-hover": {"type": "color", "group": "second", "default": "#00a1b5", "translations": {"en": {"name": "Hover - background"}, "bg": {"name": "Акцент - фон"}}}, "color-second-button-borders-hover": {"type": "color", "group": "second", "default": "#00a1b5", "translations": {"en": {"name": "Hover - border"}, "bg": {"name": "Акцент - рамка"}}}, "color-second-button-text-hover": {"type": "color", "group": "second", "default": "#fff", "translations": {"en": {"name": "Hover - text"}, "bg": {"name": "Акцент - текст"}}}, "color-second-button-secondary-background": {"title": {"en": "Secondary buttons", "bg": "Второстепенни бутони"}, "separator": "true", "type": "color", "group": "second", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-second-button-secondary-borders": {"type": "color", "group": "second", "default": "#00bcd4", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-second-button-secondary-text": {"type": "color", "group": "second", "default": "#00bcd4", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-second-button-secondary-background-hover": {"type": "color", "group": "second", "default": "#00a1b5", "translations": {"en": {"name": "Hover - background"}, "bg": {"name": "Акцент - фон"}}}, "color-second-button-secondary-borders-hover": {"type": "color", "group": "second", "default": "#00a1b5", "translations": {"en": {"name": "Hover - border"}, "bg": {"name": "Акцент - рамка"}}}, "color-second-button-secondary-text-hover": {"type": "color", "group": "second", "default": "#fff", "translations": {"en": {"name": "Hover - text"}, "bg": {"name": "Акцент - текст"}}}, "color-header-background": {"type": "color", "group": "header", "default": "#212331", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-header-borders": {"type": "color", "group": "header", "default": "#51546c", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамка"}}}, "color-header-text": {"type": "color", "group": "header", "default": "#99a9b5", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-header-secondary-text": {"type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Secondary text"}, "bg": {"name": "Второстепенен текст"}}}, "color-header-icons": {"title": {"en": "Icons", "bg": "Иконки"}, "separator": "true", "type": "color", "group": "header", "default": "#99a9b5", "translations": {"en": {"name": "Base"}, "bg": {"name": "Основа"}}}, "color-header-icons-hover": {"type": "color", "group": "header", "default": "#00bcd4", "translations": {"en": {"name": "Hover - base"}, "bg": {"name": "Акцент - основа"}}}, "color-header-icons-bubble-background": {"type": "color", "group": "header", "default": "#00bcd4", "translations": {"en": {"name": "Bubble - background"}, "bg": {"name": "Балонче - фон"}}}, "color-header-icons-bubble-text": {"type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Bubble - text"}, "bg": {"name": "Балонче - текст"}}}, "color-navigation-background": {"title": {"en": "Navigation", "bg": "Навигация"}, "separator": "true", "type": "color", "group": "header", "default": "#212331", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-navigation-text": {"type": "color", "group": "header", "default": "#99a9b5", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-navigation-hover-background": {"type": "color", "group": "header", "default": "#393d50", "translations": {"en": {"name": "Background - highlight"}, "bg": {"name": "Фон - акцент"}}}, "color-navigation-hover-text": {"type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Text - highlight"}, "bg": {"name": "Текст - акцент"}}}, "color-dropdowns-background": {"title": {"en": "Dropdowns", "bg": "Падащи менюта"}, "separator": "true", "type": "color", "group": "header", "default": "#393d50", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-dropdowns-borders": {"type": "color", "group": "header", "default": "#51546c", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Линии"}}}, "color-dropdowns-text": {"type": "color", "group": "header", "default": "#99a9b5", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-dropdowns-secondary-text": {"type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Secondary text"}, "bg": {"name": "Второстепенен текст"}}}, "color-dropdowns-meta-text": {"type": "color", "group": "header", "default": "#999", "translations": {"en": {"name": "Meta text"}, "bg": {"name": "Помощен текст"}}}, "color-dropdowns-highlight": {"type": "color", "group": "header", "default": "#00bcd4", "translations": {"en": {"name": "Hover"}, "bg": {"name": "Акцент"}}}, "color-dropdowns-image-box": {"type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Image box"}, "bg": {"name": "Подложка на картинката"}}}, "color-dropdowns-button-background": {"title": {"en": "Dropdowns - buttons", "bg": "Падащи менюта - бутони"}, "separator": "true", "type": "color", "group": "header", "default": "#00bcd4", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-dropdowns-button-borders": {"type": "color", "group": "header", "default": "#00bcd4", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-dropdowns-button-text": {"type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-dropdowns-button-background-hover": {"type": "color", "group": "header", "default": "#00a1b5", "translations": {"en": {"name": "Hover - background"}, "bg": {"name": "Акцент - фон"}}}, "color-dropdowns-button-borders-hover": {"type": "color", "group": "header", "default": "#00a1b5", "translations": {"en": {"name": "Hover - border"}, "bg": {"name": "Акцент - рамка"}}}, "color-dropdowns-button-text-hover": {"type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Hover - text"}, "bg": {"name": "Акцент - текст"}}}, "color-footer-background": {"type": "color", "group": "footer", "default": "#212331", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-footer-borders": {"type": "color", "group": "footer", "default": "#51546c", "translations": {"en": {"name": "Borders and lines"}, "bg": {"name": "Рамки и линии"}}}, "color-footer-text": {"type": "color", "group": "footer", "default": "#99a9b5", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-footer-titles": {"type": "color", "group": "footer", "default": "#fff", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-footer-highlight": {"type": "color", "group": "footer", "default": "#00bcd4", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-footer-socials-background": {"title": {"en": "Social icons", "bg": "Социални иконки"}, "separator": "true", "type": "color", "group": "footer", "default": "#212331", "translations": {"en": {"name": "Bar"}, "bg": {"name": "Лента"}}}, "color-footer-socials-icon": {"type": "color", "group": "footer", "default": "#99a9b5", "translations": {"en": {"name": "Base"}, "bg": {"name": "Основа"}}}, "color-footer-socials-icon-background": {"type": "color", "group": "footer", "default": "#212331", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-footer-socials-icon-hover": {"type": "color", "group": "footer", "default": "#00bcd4", "translations": {"en": {"name": "Hover - base"}, "bg": {"name": "Акцент - основа"}}}, "color-footer-socials-icon-background-hover": {"type": "color", "group": "footer", "default": "#212331", "translations": {"en": {"name": "Hover - background"}, "bg": {"name": "Акцент - фон"}}}, "color-footer-bottom-bar-background": {"title": {"en": "Bottom bar", "bg": "Долна лента"}, "separator": "true", "type": "color", "group": "footer", "default": "#212331", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-footer-bottom-bar-text": {"type": "color", "group": "footer", "default": "#616f7a", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-footer-bottom-bar-links": {"type": "color", "group": "footer", "default": "#99a9b5", "translations": {"en": {"name": "Links"}, "bg": {"name": "Връзки"}}}, "color-footer-bottom-bar-highlight": {"type": "color", "group": "footer", "default": "#00bcd4", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-slider-background": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-slider-text": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-slider-titles": {"type": "color", "group": "slider", "default": "#f36b7d", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-slider-arrows": {"separator": "true", "type": "color", "group": "slider", "default": "#00bcd4", "translations": {"en": {"name": "Arrows"}, "bg": {"name": "Стрелки"}}}, "color-slider-dots": {"type": "color", "group": "slider", "default": "#00bcd4", "translations": {"en": {"name": "Dots"}, "bg": {"name": "Точки"}}}, "color-slider-button-background": {"title": {"en": "<PERSON><PERSON>", "bg": "<PERSON>у<PERSON><PERSON><PERSON>"}, "separator": "true", "type": "color", "group": "slider", "default": "#00bcd4", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-slider-button-borders": {"type": "color", "group": "slider", "default": "#00bcd4", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамка"}}}, "color-slider-button-text": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-slider-button-background-hover": {"type": "color", "group": "slider", "default": "#00a1b5", "translations": {"en": {"name": "Highlight background"}, "bg": {"name": "Акцент - фон"}}}, "color-slider-button-borders-hover": {"type": "color", "group": "slider", "default": "#00a1b5", "translations": {"en": {"name": "Highlight borders"}, "bg": {"name": "Акцент - рамка"}}}, "color-slider-button-text-hover": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Highlight text"}, "bg": {"name": "Акцент - текст"}}}, "color-product-listing-borders": {"type": "color", "group": "products-listing", "default": "#e8e8e8", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамки и линии"}}}, "color-product-listing-title": {"type": "color", "group": "products-listing", "default": "#333", "translations": {"en": {"name": "Title"}, "bg": {"name": "Заглавие"}}}, "color-product-listing-highlight": {"type": "color", "group": "products-listing", "default": "#00bcd4", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-product-listing-price": {"type": "color", "group": "products-listing", "default": "#00bcd4", "translations": {"en": {"name": "Price"}, "bg": {"name": "Цена"}}}, "color-product-listing-price-old": {"type": "color", "group": "products-listing", "default": "#ccc", "translations": {"en": {"name": "Old price"}, "bg": {"name": "Стара цена"}}}, "color-product-listing-actions": {"type": "color", "group": "products-listing", "default": "#999", "translations": {"en": {"name": "Actions"}, "bg": {"name": "Действия"}}}, "color-product-listing-actions-highlight": {"type": "color", "group": "products-listing", "default": "#00bcd4", "translations": {"en": {"name": "Actions highlight"}, "bg": {"name": "Действия - акцент"}}}, "color-product-image-background": {"type": "color", "group": "product-details", "default": "#fff", "translations": {"en": {"name": "Images background"}, "bg": {"name": "Снимки - фон"}}}, "color-product-image-borders": {"type": "color", "group": "product-details", "default": "#e8e8e8", "translations": {"en": {"name": "Images borders"}, "bg": {"name": "Снимки - очертания"}}}, "color-product-price": {"type": "color", "group": "product-details", "default": "#00bcd4", "translations": {"en": {"name": "Price"}, "bg": {"name": "Цена"}}}, "color-product-icons": {"type": "color", "group": "product-details", "default": "#ccc", "translations": {"en": {"name": "Icons"}, "bg": {"name": "Иконки"}}}, "color-product-icons-highlight": {"type": "color", "group": "product-details", "default": "#00bcd4", "translations": {"en": {"name": "Icons - hover"}, "bg": {"name": "Иконки - акцент"}}}, "color-product-gallery-background": {"type": "color", "group": "product-details", "default": "#212331", "translations": {"en": {"name": "Lighbox gallery background"}, "bg": {"name": "Popup галерия на продукта"}}}, "color-forms-fields-background": {"title": {"en": "Fields", "bg": "Полета"}, "type": "color", "group": "forms", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-forms-fields-borders": {"type": "color", "group": "forms", "default": "#e8e8e8", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-forms-fields-text": {"type": "color", "group": "forms", "default": "#999", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-forms-fields-placeholder": {"type": "color", "group": "forms", "default": "#ccc", "translations": {"en": {"name": "Placeholder text"}, "bg": {"name": "Placeholder текст"}}}, "color-forms-fields-icons": {"type": "color", "group": "forms", "default": "#00bcd4", "translations": {"en": {"name": "Icons"}, "bg": {"name": "Иконки"}}}, "color-forms-checkbox-background": {"title": {"en": "Checkboxes", "bg": "Чекбоксове"}, "separator": "true", "type": "color", "group": "forms", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-forms-checkbox-borders": {"type": "color", "group": "forms", "default": "#e8e8e8", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-forms-checkbox-check": {"type": "color", "group": "forms", "default": "#00bcd4", "translations": {"en": {"name": "Check"}, "bg": {"name": "Тикче"}}}, "color-forms-radio-background": {"title": {"en": "Radio buttons", "bg": "Радиобутони"}, "separator": "true", "type": "color", "group": "forms", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-forms-radio-borders": {"type": "color", "group": "forms", "default": "#e8e8e8", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-forms-radio-check": {"type": "color", "group": "forms", "default": "#00bcd4", "translations": {"en": {"name": "Check"}, "bg": {"name": "Избран"}}}, "color-forms-range-slider": {"title": {"en": "Range slider", "bg": "Обхват на стойности"}, "separator": "true", "type": "color", "group": "forms", "default": "#e8e8e8", "translations": {"en": {"name": "Base"}, "bg": {"name": "Основа"}}}, "color-forms-range-slider-highlight": {"type": "color", "group": "forms", "default": "#00bcd4", "translations": {"en": {"name": "Selected range"}, "bg": {"name": "Избран обхват"}}}, "color-forms-range-slider-sliders": {"type": "color", "group": "forms", "default": "#666", "translations": {"en": {"name": "Sliders"}, "bg": {"name": "Плъзгачи"}}}, "color-button-background": {"title": {"en": "Primary", "bg": "Основен"}, "type": "color", "group": "buttons", "default": "#00bcd4", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-button-borders": {"type": "color", "group": "buttons", "default": "#00bcd4", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-button-text": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-button-background-hover": {"type": "color", "group": "buttons", "default": "#00a1b5", "translations": {"en": {"name": "Hover - background"}, "bg": {"name": "Акцент - фон"}}}, "color-button-borders-hover": {"type": "color", "group": "buttons", "default": "#00a1b5", "translations": {"en": {"name": "Hover - border"}, "bg": {"name": "Акцент - рамка"}}}, "color-button-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Hover - text"}, "bg": {"name": "Акцент - текст"}}}, "color-button-secondary-background": {"title": {"en": "Secondary", "bg": "Второстепенен"}, "separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-button-secondary-borders": {"type": "color", "group": "buttons", "default": "#00bcd4", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-button-secondary-text": {"type": "color", "group": "buttons", "default": "#00bcd4", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-button-secondary-background-hover": {"type": "color", "group": "buttons", "default": "#00a1b5", "translations": {"en": {"name": "Hover - background"}, "bg": {"name": "Акцент - фон"}}}, "color-button-secondary-borders-hover": {"type": "color", "group": "buttons", "default": "#00a1b5", "translations": {"en": {"name": "Hover - border"}, "bg": {"name": "Акцент - рамка"}}}, "color-button-secondary-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Hover - text"}, "bg": {"name": "Акцент - текст"}}}, "color-button-thertiary-background": {"title": {"en": "Thertiary", "bg": "Третостепенен"}, "separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-button-thertiary-borders": {"type": "color", "group": "buttons", "default": "#e8e8e8", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-button-thertiary-text": {"type": "color", "group": "buttons", "default": "#00bcd4", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-button-thertiary-background-hover": {"type": "color", "group": "buttons", "default": "#00bcd4", "translations": {"en": {"name": "Hover - background"}, "bg": {"name": "Акцент - фон"}}}, "color-button-thertiary-borders-hover": {"type": "color", "group": "buttons", "default": "#00bcd4", "translations": {"en": {"name": "Hover - border"}, "bg": {"name": "Акцент - рамка"}}}, "color-button-thertiary-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Hover - text"}, "bg": {"name": "Акцент - текст"}}}, "color-text-box-background": {"type": "color", "group": "text-boxes", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-text-box-borders": {"type": "color", "group": "text-boxes", "default": "#e8e8e8", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-text-box-text": {"type": "color", "group": "text-boxes", "default": "#999", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-text-box-titles": {"type": "color", "group": "text-boxes", "default": "#333", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-text-box-tooltip-background": {"title": {"en": "Tooltips", "bg": "Подсказка"}, "separator": "true", "type": "color", "group": "text-boxes", "default": "#393d50", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-text-box-tooltip-borders": {"type": "color", "group": "text-boxes", "default": "#4f526a", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамки"}}}, "color-text-box-tooltip-text": {"type": "color", "group": "text-boxes", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-text1-background": {"title": {"en": "Text section 1", "bg": "Текстова секция 1"}, "type": "color", "group": "text-sections", "default": "#f8f8f8", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-text1-text": {"type": "color", "group": "text-sections", "default": "#999", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-text1-titles": {"type": "color", "group": "text-sections", "default": "#666", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-text1-highlight": {"type": "color", "group": "text-sections", "default": "#00bcd4", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-text2-background": {"title": {"en": "Text section 2", "bg": "Текстова секция 2"}, "separator": "true", "type": "color", "group": "text-sections", "default": "#393d50", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-text2-text": {"type": "color", "group": "text-sections", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-text2-titles": {"type": "color", "group": "text-sections", "default": "#fff", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-text2-highlight": {"type": "color", "group": "text-sections", "default": "#00bcd4", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-text3-background": {"title": {"en": "Text section 3", "bg": "Текстова секция 3"}, "separator": "true", "type": "color", "group": "text-sections", "default": "#f8f8f8", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-text3-text": {"type": "color", "group": "text-sections", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-text3-titles": {"type": "color", "group": "text-sections", "default": "#333", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-text3-highlight": {"type": "color", "group": "text-sections", "default": "#00bcd4", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-promo-bar-background": {"type": "color", "group": "promo-bar", "default": "#00bcd4", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-promo-bar-text": {"type": "color", "group": "promo-bar", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-promo-bar-close": {"type": "color", "group": "promo-bar", "default": "#fff", "translations": {"en": {"name": "Close"}, "bg": {"name": "Затвори"}}}, "color-promo-bar-button-background": {"title": {"en": "<PERSON><PERSON>", "bg": "<PERSON>у<PERSON><PERSON><PERSON>"}, "separator": "true", "type": "color", "group": "promo-bar", "default": "#00bcd4", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-promo-bar-button-borders": {"type": "color", "group": "promo-bar", "default": "#fff", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-promo-bar-button-text": {"type": "color", "group": "promo-bar", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-promo-bar-button-background-hover": {"type": "color", "group": "promo-bar", "default": "#00a1b5", "translations": {"en": {"name": "Hover - background"}, "bg": {"name": "Акцент - фон"}}}, "color-promo-bar-button-borders-hover": {"type": "color", "group": "promo-bar", "default": "#00a1b5", "translations": {"en": {"name": "Hover - border"}, "bg": {"name": "Акцент - рамка"}}}, "color-promo-bar-button-text-hover": {"type": "color", "group": "promo-bar", "default": "#fff", "translations": {"en": {"name": "Hover - text"}, "bg": {"name": "Акцент - текст"}}}, "color-breadcrumb-background": {"type": "color", "group": "breadcrumb", "default": "#f8f8f8", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-breadcrumb-text": {"type": "color", "group": "breadcrumb", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-breadcrumb-text-active": {"type": "color", "group": "breadcrumb", "default": "#999", "translations": {"en": {"name": "Active"}, "bg": {"name": "Акти<PERSON><PERSON>н"}}}, "color-breadcrumb-text-hover": {"type": "color", "group": "breadcrumb", "default": "#00bcd4", "translations": {"en": {"name": "Hover"}, "bg": {"name": "Акцент"}}}, "color-pagination-borders": {"type": "color", "group": "pagination", "default": "#e8e8e8", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамки и линии"}}}, "color-pagination-text": {"type": "color", "group": "pagination", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-pagination-highlight": {"type": "color", "group": "pagination", "default": "#00bcd4", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-pagination-disabled": {"type": "color", "group": "pagination", "default": "#ccc", "translations": {"en": {"name": "Disabled"}, "bg": {"name": "Изключен"}}}, "color-tooltips-background": {"type": "color", "group": "tooltips", "default": "#212331", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-tooltips-borders": {"type": "color", "group": "tooltips", "default": "#51546c", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамка"}}}, "color-tooltips-text": {"type": "color", "group": "tooltips", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-label-new-background": {"title": {"en": "NEW", "bg": "НОВ"}, "type": "color", "group": "labels", "default": "#393d50", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-label-new-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-label-sale-background": {"title": {"en": "SALE", "bg": "SALE"}, "separator": "true", "type": "color", "group": "labels", "default": "#00bcd4", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-label-sale-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-label-discount-background": {"title": {"en": "Discount", "bg": "Отстъпка"}, "separator": "true", "type": "color", "group": "labels", "default": "#f36b7d", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-label-discount-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-label-delivery-background": {"title": {"en": "Free delivery", "bg": "Безплатна доставка"}, "separator": "true", "type": "color", "group": "labels", "default": "#393d50", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-label-delivery-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-label-custom-background": {"title": {"en": "Custom", "bg": "Персонализ<PERSON><PERSON><PERSON>н"}, "separator": "true", "type": "color", "group": "labels", "default": "#00bcd4", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-label-custom-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-label-featured-background": {"title": {"en": "Featured", "bg": "Препоръчан"}, "separator": "true", "type": "color", "group": "labels", "default": "#f36b7d", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-label-featured-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-label-leasing-background": {"title": {"en": "Leasing", "bg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "separator": "true", "type": "color", "group": "labels", "default": "#00bcd4", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-label-leasing-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-popups-background": {"type": "color", "group": "popups", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-popups-borders": {"type": "color", "group": "popups", "default": "#e8e8e8", "translations": {"en": {"name": "Borders and lines"}, "bg": {"name": "Рамки и линии"}}}, "color-popups-text": {"type": "color", "group": "popups", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-popups-titles": {"type": "color", "group": "popups", "default": "#333", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-popups-highlight": {"type": "color", "group": "popups", "default": "#00bcd4", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-popups-button-background": {"title": {"en": "Primary buttons", "bg": "Основни бутони"}, "separator": "true", "type": "color", "group": "popups", "default": "#00bcd4", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-popups-button-borders": {"type": "color", "group": "popups", "default": "#00bcd4", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-popups-button-text": {"type": "color", "group": "popups", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-popups-button-background-hover": {"type": "color", "group": "popups", "default": "#00a1b5", "translations": {"en": {"name": "Hover - background"}, "bg": {"name": "Акцент - фон"}}}, "color-popups-button-borders-hover": {"type": "color", "group": "popups", "default": "#00a1b5", "translations": {"en": {"name": "Hover - border"}, "bg": {"name": "Акцент - рамка"}}}, "color-popups-button-text-hover": {"type": "color", "group": "popups", "default": "#fff", "translations": {"en": {"name": "Hover - text"}, "bg": {"name": "Акцент - текст"}}}, "color-popups-button-secondary-background": {"title": {"en": "Secondary buttons", "bg": "Второстепенни бутони"}, "separator": "true", "type": "color", "group": "popups", "default": "#f7f7f7", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-popups-button-secondary-borders": {"type": "color", "group": "popups", "default": "#00bcd4", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-popups-button-secondary-text": {"type": "color", "group": "popups", "default": "#00bcd4", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-popups-button-secondary-background-hover": {"type": "color", "group": "popups", "default": "#00a1b5", "translations": {"en": {"name": "Hover - background"}, "bg": {"name": "Акцент - фон"}}}, "color-popups-button-secondary-borders-hover": {"type": "color", "group": "popups", "default": "#00a1b5", "translations": {"en": {"name": "Hover - border"}, "bg": {"name": "Акцент - рамка"}}}, "color-popups-button-secondary-text-hover": {"type": "color", "group": "popups", "default": "#fff", "translations": {"en": {"name": "Hover - text"}, "bg": {"name": "Акцент - текст"}}}, "font-family-main": {"type": "font-family", "group": "font-main", "default": "Roboto", "translations": {"en": {"name": "Font Family"}, "bg": {"name": "<PERSON>ри<PERSON><PERSON>"}}}, "font-size-main": {"type": "font-size", "group": "font-main", "default": "14px", "translations": {"en": {"name": "Font Size"}, "bg": {"name": "Размер на шрифта"}}}, "font-weight-main": {"type": "font-weight", "group": "font-main", "default": "400", "translations": {"en": {"name": "Font Weight"}, "bg": {"name": "Де<PERSON><PERSON><PERSON>ина на шрифта"}}}, "font-style-main": {"type": "font-style", "group": "font-main", "default": "normal", "translations": {"en": {"name": "Font Style"}, "bg": {"name": "Стил на шрифта"}}}, "font-family-titles": {"type": "font-family", "group": "font-titles", "default": "Roboto", "translations": {"en": {"name": "Font Family"}, "bg": {"name": "<PERSON>ри<PERSON><PERSON>"}}}, "font-weight-titles": {"type": "font-weight", "group": "font-titles", "default": "700", "translations": {"en": {"name": "Font Weight"}, "bg": {"name": "Де<PERSON><PERSON><PERSON>ина на шрифта"}}}, "font-style-titles": {"type": "font-style", "group": "font-titles", "default": "normal", "translations": {"en": {"name": "Font Style"}, "bg": {"name": "Стил на шрифта"}}}, "font-size-heading-1": {"type": "font-size", "group": "font-titles", "default": "30px", "translations": {"en": {"name": "Heading 1"}, "bg": {"name": "Заглавие 1"}}}, "font-size-heading-2": {"type": "font-size", "group": "font-titles", "default": "24px", "translations": {"en": {"name": "Heading 2"}, "bg": {"name": "Заглавие 2"}}}, "font-size-heading-3": {"type": "font-size", "group": "font-titles", "default": "20px", "translations": {"en": {"name": "Heading 3"}, "bg": {"name": "Заглавие 3"}}}, "font-size-heading-4": {"type": "font-size", "group": "font-titles", "default": "18px", "translations": {"en": {"name": "Heading 4"}, "bg": {"name": "Заглавие 4"}}}, "font-size-heading-5": {"type": "font-size", "group": "font-titles", "default": "16px", "translations": {"en": {"name": "Heading 5"}, "bg": {"name": "Заглавие 5"}}}, "font-size-heading-6": {"type": "font-size", "group": "font-titles", "default": "14px", "translations": {"en": {"name": "Heading 6"}, "bg": {"name": "Заглавие 6"}}}, "font-family-buttons": {"type": "font-family", "group": "font-buttons", "default": "Roboto", "translations": {"en": {"name": "Font Family"}, "bg": {"name": "<PERSON>ри<PERSON><PERSON>"}}}, "font-size-buttons": {"type": "font-size", "group": "font-buttons", "default": "18px", "translations": {"en": {"name": "Font Size"}, "bg": {"name": "Размер на шрифта"}}}, "font-weight-buttons": {"type": "font-weight", "group": "font-buttons", "default": "700", "translations": {"en": {"name": "Font Weight"}, "bg": {"name": "Де<PERSON><PERSON><PERSON>ина на шрифта"}}}, "font-style-buttons": {"type": "font-style", "group": "font-buttons", "default": "normal", "translations": {"en": {"name": "Font Style"}, "bg": {"name": "Стил на шрифта"}}}, "font-family-product-list-title": {"title": {"en": "Title", "bg": "Заглавие"}, "type": "font-family", "group": "font-product-list", "default": "Roboto", "translations": {"en": {"name": "Font Family"}, "bg": {"name": "<PERSON>ри<PERSON><PERSON>"}}}, "font-size-product-list-title": {"type": "font-size", "group": "font-product-list", "default": "14px", "translations": {"en": {"name": "Font Size"}, "bg": {"name": "Размер на шрифта"}}}, "font-weight-product-list-title": {"type": "font-weight", "group": "font-product-list", "default": "400", "translations": {"en": {"name": "Font Weight"}, "bg": {"name": "Де<PERSON><PERSON><PERSON>ина на шрифта"}}}, "font-style-product-list-title": {"type": "font-style", "group": "font-product-list", "default": "normal", "translations": {"en": {"name": "Font Style"}, "bg": {"name": "Стил на шрифта"}}}, "image-orientation": {"type": "image", "group": "image", "default": "100%", "translations": {"en": {"name": "Image Orientation"}, "bg": {"name": "Ориентация на снимката"}}}, "image-orientation-width": {"type": "image", "group": "image", "default": "1", "translations": {"en": {"name": "<PERSON><PERSON><PERSON>"}, "bg": {"name": "Ши<PERSON><PERSON><PERSON>"}}}, "image-orientation-height": {"type": "image", "group": "image", "default": "1", "translations": {"en": {"name": "Height"}, "bg": {"name": "Висо<PERSON>ина"}}}}}}