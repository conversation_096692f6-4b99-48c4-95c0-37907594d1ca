/*=============================================================================*\
    EXTRA SMALL DEVICES / PHONES
\*=============================================================================*/

@media (max-width: @screen-xs-max) {

	/* #Main
	-------------------------------------------------------------------------------*/

	h1, ._h1 {
		font-size: 24px; /* theme */
	}

	h2, ._h2 {
		font-size: 22px; /* theme */
	}

	h3, ._h3 {
		font-size: 20px; /* theme */
	}

	h4, ._h4 {
		font-size: 18px; /* theme */
	}

	h5, ._h5 {
		font-size: 16px; /* theme */
	}

	h6, ._h6 {
		font-size: 14px; /* theme */
	}

	._button,
	._meta-link-login,
	._meta-link-register,
	._meta-link-forgotten-password {
		width: 100%;
	}

	._section-title-actions {
		._button {
			width: auto;
		}
	}

	._section-title-addon {
		display: none;
	}

	._meta-links {
		padding-top: 15px;
		margin-top: 15px;

		a {
			display: block;

			+ a {
				margin: 10px 0 0;
			}
		}
	}

	._content {
		padding-top: 25px;
		padding-bottom: 25px;

		> ._breadcrumb-container,
		> ._breadcrumb-wrapper {
			&:first-child {
				margin-top: -25px;
			}
		}
	}

	._notification {
		font-size: calc(@font-size-main ~'-' 2px); /* theme */
	}

	._header,
	._header-fixed-inner {
		height: 60px;
	}

	._logo {
		width: 190px;
		padding: 5px;

		img {
			max-height: 50px;
		}
	}

	._utilities {
		padding-left: 0;
	}

	._mobile-hidden {
		display: none !important;
	}

	/* #Textbox
	-------------------------------------------------------------------------------*/

	._text,
	._textbox {
		.alignleft,
		.alignright {
			float: none;
			width: auto;
			display: block;
			margin: 0 0 20px;
		}
	}

	/* #Forms
	-------------------------------------------------------------------------------*/
	
	._form-row {
		display: block;
		margin-bottom: 10px;

		._form-col {
			float: none;
			display: block;
			margin-bottom: 10px;
			width: auto;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	._form-row-secondary {
		display: block;
		margin-bottom: 10px;

		._form-col-secondary {
			float: none;
			display: block;
			margin-bottom: 10px;
			width: auto;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	._block-title {
		margin-bottom: 10px;
	}

	[id^="recaptcha"] {
		-webkit-transform-origin: 0 0;
		        transform-origin: 0 0;
		-webkit-transform: scale(.9);
		        transform: scale(.9);
	}

	._forgotten-password-link {
		text-align: center;
	}

	._popup-title {
		h1,h2,h3,h4,h5,h6 {
			font-size: 18px;
		}
	}

	._to-top {
		width: 30px;
		height: 30px;
		line-height: 28px;
		font-size: 14px;
		bottom: 10px;
		right: 10px;
	}

	._compare-products {
		~ ._to-top {
			bottom: 50px;
		}

		&.hide {
			~ ._to-top {
				bottom: 10px;
			}
		}
	}

	._compare-box-items,
	._compare-box-item,
	._compare-box-actions {
		float: none;
		width: auto;
	}

	._compare-box-item {
		padding: 0;
		margin-bottom: 10px;

		._remove {
			right: 0;
			.centerer(false, true);
		}
	}

	._compare-box-item-image,
	._compare-box-item-info {
		display: table-cell;
		vertical-align: middle;
	}

	._compare-box-item-info {
		padding: 0;
		padding-left: 10px;
		padding-right: 20px;
	}

	/* #Separator
	-------------------------------------------------------------------------------*/

	._section-separator,
	._pagination {
		margin-bottom: 25px;
	}

	/* #Header
	-------------------------------------------------------------------------------*/

	._search-form-wrapper {
		padding: 10px 0;
	}

	/* #Slider
	-------------------------------------------------------------------------------*/

	._slider-container {
		+ ._homepage-text {
			margin-top: -26px;
		}
	}

	._slider {
		padding: 0;
	}

	.slide-text {
		display: none;
	}

	.owl-prev,
	.owl-next {
		width: 50px;
	}

	.owl-dots {
		bottom: 10px;
	}

	/* #Text boxes
	-------------------------------------------------------------------------------*/

	._text-boxes {
		._text-box {
			width: 50%;
		}
	}

	._homepage-text {
		padding: 30px 0;

		+ ._homepage-text {
			margin-top: -26px;
		}

		._text {
			h1,h2,h3,h4 {
				margin-bottom: 15px;
			}

			h1 {
				font-size: 36px;
			}

			h2 {
				font-size: 28px;
			}

			h3 {
				font-size: 24px;
			}

			h4 {
				font-size: 20px;
			}

			h5 {
				font-size: 18px;
			}

			h6 {
				font-size: 16px;
			}
		}
	}

	/* #Banners
	-------------------------------------------------------------------------------*/

	._banner-caption {
		display: none;
	}

	/* #Products list
	-------------------------------------------------------------------------------*/

	._sidebar-products,
	._sidebar-blog {
		width: 100%;
	}

	._sidebar-box-sort {
		display: block;

		._select {
			margin-bottom: 15px;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	._sidebar-main-button {
		._button {
			width: auto;
		}
	}

	._filters-col-sort,
	._filters-col-perpage {
		display: none;
	}

	._carousel {
		margin: 0 -15px;
	}

	._carousel-button-all {
		display: none;
	}

	._product {
		padding: 15px 15px 0;
	}

	._products-list-info {
		&-item {
			&-body {
				display: block;
			}

			&-image {
				display: block;
				width: auto;
				padding: 0;
				text-align: center;
			}

			&-description {
				margin-top: 20px;
				display: block;
			}
		}
	}

	._product-details-gallery {
		.swiper-wrapper {
			height: @gallery-thumb-size*3 + @gallery-thumb-offset*2;
		}
	}

	._product-details-ribbon-banner {
		max-width: 33.33%;
	}

	._product-details-ribbon-banner-image {
		max-height: 100px;
	}

	._product-details-text {
		display: block;
	}

	._product-details-info {
		margin-bottom: 15px;
	}

	._product-details-text {
		padding: 0;
		margin-bottom: 15px;
	}

	._product-details-price {
		width: auto;
	}

	._product-details-price-new {
		font-size: 24px;
	}

	._product-details-price-old {
		font-size: 14px;
	}

	._product-details-parameters-row {
		margin-bottom: 10px;
	}

	._product-details-tabs-nav {
		padding: 0 15px;
		margin-bottom: 5px;
	}

	._product-details-tabs-nav-link {
		border: 0;
		padding: 0;
		margin-right: 10px;
		font-size: @font-size-main; /* theme */

		&.active {
			color: @color-main-highlight; /* theme */
		}
	}

	._product-details-tabs {
		padding: 15px;
	}

	._product-details-sticky-info {
		._product-details-price-new {
			font-size: 18px;
			margin: 0;
		}

		._product-details-price-meta {
			display: none;
		}
	}

	._related-products-bottom {
		padding-top: 30px;
		margin-top: 0;
	}

	._pagination {
		text-align: center;

		.first,
		.last {
			display: none;
		}
	}

	/* #Vendors
	-------------------------------------------------------------------------------*/

	._vendors-list {
		margin-left: -7px;
		margin-right: -7px;

		._vendor {
			width: 50.00%;
			padding-left: 7px;
			padding-right: 7px;
			margin-bottom: 20px;

			&:nth-child(3n+1) {
				clear: none;
			}

			&:nth-child(2n+1) {
				clear: both;
			}
		}
	}

	/* #Blog
	-------------------------------------------------------------------------------*/

	._blog-main {
		._blog-list-article-inner {
            border: 1px solid;
            border-color: @color-main-borders; /* theme */
			text-align: center;
			margin: 0 auto 40px;
		}

		._blog-list-article-image {
            float: none;
            border: 0;
            width: auto;
            margin: 0;
		}

		._blog-list-article-info {
            padding: 17px 20px;
		}

		._blog-list-article-title {
            margin-bottom: 16px;
		}

        ._blog-list-article-title-tag {
            ._h6();

            a {
                color: @color-main-titles; /* theme */

                @media @hover {
                    &:hover {
                        color: @color-main-highlight; /* theme */
                    }
                }
            }
        }

        ._blog-list-article-meta {
            margin-bottom: 5px;
        }
	}

    ._blog-article-title {
        padding-bottom: 13px;
        margin-bottom: 12px;
    }

	/* #Contacts
	-------------------------------------------------------------------------------*/

	._contact-info {
		margin-bottom: 30px;
	}

	._google-map {
		position: relative;
		height: 250px !important;
	}

	/* #Account
	-------------------------------------------------------------------------------*/

	._sidebar-list-account {
		li {
			display: block;
			float: left;
			width: 50%;
			padding: 0;
			padding-right: 10px;

			&:nth-child(2n+1) {
				clear: both;
			}
		}
	}

	._order-details {
		&-product {
			&-image {
				margin-top: 0;
				margin-right: 15px;
				width: 25%;
			}

			&-title {
				margin-bottom: 5px;

				h2 {
					font-size: 18px;
				}
			}

			&-price {
				&-total {
					margin-top: 5px;
				}
			}
		}

		&-summary {
			li {
				width: 250px;
			}

			&-value {
				padding-right: 0;
			}
		}
	}

	/* #Cart
	-------------------------------------------------------------------------------*/

	._cart-product-image {
		width: 150px;
	}

	._cart-product-name {
		margin-bottom: 0;

		h4 {
			font-size: 18px;
		}
	}

	._cart-product-parameters,
	._cart-product-single-price {
		font-size: 13px;
	}

	._cart-product-box {
		padding-left: 15px;
	}

	._cart-product-quantity {
		left: 165px;

		.input-group {
			width: 120px;
		}
	}

	/* #Checkout
	-------------------------------------------------------------------------------*/

	._checkout-steps {
		._section-title,
		._secondary-title {
			h3 {
				._h3();
			}
		}
	}

	._checkout-account {
		display: block;

		._form-row {
			margin-bottom: 10px;
		}
	}

	._checkout-account-tabs {
		display: block;
		margin-bottom: 20px;
	}

	._checkout-account-forms {
		display: block;
	}

    ._checkout-shipping-providers-controls {
    	border-bottom: 0;

    	._radio {
    		display: block;
    		border-radius: 0;
    		width: auto;
    		margin: 0;
    	}
    }

    ._checkout-shipping-providers-list {
        margin: 0 -7px -7px;
    }

    ._checkout-shipping-provider {
    	margin: 7px;
        width: calc(50% ~'-' 14px);
    }

    ._checkout-shipping-provider-features-form {
    	.row {
    		margin-top: 15px;
    	}

        [class*='col'] {
			margin-top: 15px;

			&:first-child {
				margin-top: 0;
			}
        }

        .hidden + [class*='col'] {
        	margin-top: 0;
        }
    }

	._checkout-payment-providers {
		._form-col {
			margin-bottom: 0;
		}
	}

    ._address-book-list {
        ul {
            margin: 0;

            li {
                width: 100%;
                padding: 0;
                margin: 0 0 15px;

                &:last-child {
                	margin-bottom: 0;
                }
            }
        }
    }

	.region-wrapper {
		margin-bottom: 0;
	}

	.region-holder {
		margin-bottom: 10px;
	}

	._error-page-code {
		width: 180px;
		height: 180px;
		font-size: 80px;
		line-height: 175px;
	}

	._error-page-message {
		font-size: 20px;
	}

	._error-page-apologize {
		font-size: 42px;
	}
}

