{"name": "bootstrap", "version": "3.3.7", "dependencies": {"abbrev": {"version": "1.0.9", "from": "abbrev@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/abbrev/-/abbrev-1.0.9.tgz"}, "accepts": {"version": "1.3.3", "from": "accepts@>=1.3.3 <1.4.0", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.3.tgz"}, "acorn": {"version": "3.2.0", "from": "acorn@>=3.1.0 <4.0.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-3.2.0.tgz"}, "acorn-globals": {"version": "3.0.0", "from": "acorn-globals@>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-3.0.0.tgz"}, "agent-base": {"version": "2.0.1", "from": "agent-base@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-2.0.1.tgz", "dependencies": {"semver": {"version": "5.0.3", "from": "semver@>=5.0.1 <5.1.0", "resolved": "https://registry.npmjs.org/semver/-/semver-5.0.3.tgz"}}}, "align-text": {"version": "0.1.4", "from": "align-text@>=0.1.3 <0.2.0", "resolved": "https://registry.npmjs.org/align-text/-/align-text-0.1.4.tgz"}, "amdefine": {"version": "1.0.0", "from": "amdefine@>=0.0.4", "resolved": "https://registry.npmjs.org/amdefine/-/amdefine-1.0.0.tgz"}, "ansi-regex": {"version": "2.0.0", "from": "ansi-regex@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-2.0.0.tgz"}, "ansi-styles": {"version": "2.2.1", "from": "ansi-styles@>=2.2.1 <3.0.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz"}, "archiver": {"version": "1.0.0", "from": "archiver@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/archiver/-/archiver-1.0.0.tgz", "dependencies": {"lodash": {"version": "4.13.1", "from": "lodash@>=4.8.0 <5.0.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.13.1.tgz"}}}, "archiver-utils": {"version": "1.2.0", "from": "archiver-utils@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/archiver-utils/-/archiver-utils-1.2.0.tgz", "dependencies": {"lodash": {"version": "4.13.1", "from": "lodash@>=4.8.0 <5.0.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.13.1.tgz"}}}, "argparse": {"version": "1.0.7", "from": "argparse@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.7.tgz"}, "array-differ": {"version": "1.0.0", "from": "array-differ@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/array-differ/-/array-differ-1.0.0.tgz"}, "array-find-index": {"version": "1.0.1", "from": "array-find-index@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/array-find-index/-/array-find-index-1.0.1.tgz"}, "array-union": {"version": "1.0.2", "from": "array-union@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/array-union/-/array-union-1.0.2.tgz"}, "array-uniq": {"version": "1.0.3", "from": "array-uniq@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/array-uniq/-/array-uniq-1.0.3.tgz"}, "arrify": {"version": "1.0.1", "from": "arrify@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/arrify/-/arrify-1.0.1.tgz"}, "asap": {"version": "2.0.4", "from": "asap@>=2.0.3 <2.1.0", "resolved": "https://registry.npmjs.org/asap/-/asap-2.0.4.tgz"}, "asn1": {"version": "0.2.3", "from": "asn1@>=0.2.3 <0.3.0", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.3.tgz"}, "assert-plus": {"version": "0.2.0", "from": "assert-plus@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.2.0.tgz"}, "async": {"version": "1.5.2", "from": "async@>=1.5.2 <1.6.0", "resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz"}, "autoprefixer-core": {"version": "5.2.1", "from": "autoprefixer-core@>=5.1.7 <6.0.0", "resolved": "https://registry.npmjs.org/autoprefixer-core/-/autoprefixer-core-5.2.1.tgz"}, "aws-sign2": {"version": "0.6.0", "from": "aws-sign2@>=0.6.0 <0.7.0", "resolved": "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.6.0.tgz"}, "aws4": {"version": "1.4.1", "from": "aws4@>=1.2.1 <2.0.0", "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.4.1.tgz"}, "babel-runtime": {"version": "6.9.2", "from": "babel-runtime@>=6.9.2 <7.0.0", "resolved": "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.9.2.tgz"}, "babylon": {"version": "6.8.4", "from": "babylon@>=6.8.1 <7.0.0", "resolved": "https://registry.npmjs.org/babylon/-/babylon-6.8.4.tgz"}, "balanced-match": {"version": "0.4.1", "from": "balanced-match@>=0.4.1 <0.5.0", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.4.1.tgz"}, "basic-auth": {"version": "1.0.4", "from": "basic-auth@>=1.0.3 <1.1.0", "resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-1.0.4.tgz"}, "batch": {"version": "0.5.3", "from": "batch@0.5.3", "resolved": "https://registry.npmjs.org/batch/-/batch-0.5.3.tgz"}, "bl": {"version": "1.1.2", "from": "bl@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/bl/-/bl-1.1.2.tgz", "dependencies": {"readable-stream": {"version": "2.0.6", "from": "readable-stream@>=2.0.5 <2.1.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz"}}}, "body-parser": {"version": "1.14.2", "from": "body-parser@>=1.14.0 <1.15.0", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.14.2.tgz", "dependencies": {"http-errors": {"version": "1.3.1", "from": "http-errors@>=1.3.1 <1.4.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-1.3.1.tgz"}, "qs": {"version": "5.2.0", "from": "qs@5.2.0", "resolved": "https://registry.npmjs.org/qs/-/qs-5.2.0.tgz"}}}, "boom": {"version": "2.10.1", "from": "boom@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/boom/-/boom-2.10.1.tgz"}, "brace-expansion": {"version": "1.1.5", "from": "brace-expansion@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.5.tgz"}, "browserify-zlib": {"version": "0.1.4", "from": "browserify-zlib@>=0.1.4 <0.2.0", "resolved": "https://registry.npmjs.org/browserify-zlib/-/browserify-zlib-0.1.4.tgz"}, "browserslist": {"version": "0.4.0", "from": "browserslist@>=0.4.0 <0.5.0", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-0.4.0.tgz"}, "btoa": {"version": "1.1.2", "from": "btoa@>=1.1.2 <1.2.0", "resolved": "https://registry.npmjs.org/btoa/-/btoa-1.1.2.tgz"}, "buffer-crc32": {"version": "0.2.5", "from": "buffer-crc32@>=0.2.1 <0.3.0", "resolved": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.5.tgz"}, "buffer-shims": {"version": "1.0.0", "from": "buffer-shims@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/buffer-shims/-/buffer-shims-1.0.0.tgz"}, "builtin-modules": {"version": "1.1.1", "from": "builtin-modules@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/builtin-modules/-/builtin-modules-1.1.1.tgz"}, "bytes": {"version": "2.2.0", "from": "bytes@2.2.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-2.2.0.tgz"}, "camel-case": {"version": "3.0.0", "from": "camel-case@>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/camel-case/-/camel-case-3.0.0.tgz"}, "camelcase": {"version": "2.1.1", "from": "camelcase@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-2.1.1.tgz"}, "camelcase-keys": {"version": "2.1.0", "from": "camelcase-keys@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/camelcase-keys/-/camelcase-keys-2.1.0.tgz"}, "caniuse-db": {"version": "1.0.30000506", "from": "caniuse-db@>=1.0.30000214 <2.0.0", "resolved": "https://registry.npmjs.org/caniuse-db/-/caniuse-db-1.0.30000506.tgz"}, "caseless": {"version": "0.11.0", "from": "caseless@>=0.11.0 <0.12.0", "resolved": "https://registry.npmjs.org/caseless/-/caseless-0.11.0.tgz"}, "center-align": {"version": "0.1.3", "from": "center-align@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/center-align/-/center-align-0.1.3.tgz"}, "chalk": {"version": "1.1.3", "from": "chalk@>=1.1.1 <1.2.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz"}, "change-case": {"version": "3.0.0", "from": "change-case@>=3.0.0 <3.1.0", "resolved": "https://registry.npmjs.org/change-case/-/change-case-3.0.0.tgz"}, "character-parser": {"version": "2.2.0", "from": "character-parser@>=2.1.1 <3.0.0", "resolved": "https://registry.npmjs.org/character-parser/-/character-parser-2.2.0.tgz"}, "clean-css": {"version": "3.4.18", "from": "clean-css@>=3.4.2 <3.5.0", "resolved": "https://registry.npmjs.org/clean-css/-/clean-css-3.4.18.tgz"}, "cli": {"version": "0.6.6", "from": "cli@>=0.6.0 <0.7.0", "resolved": "https://registry.npmjs.org/cli/-/cli-0.6.6.tgz", "dependencies": {"glob": {"version": "3.2.11", "from": "glob@>=3.2.1 <3.3.0", "resolved": "https://registry.npmjs.org/glob/-/glob-3.2.11.tgz"}, "minimatch": {"version": "0.3.0", "from": "minimatch@>=0.3.0 <0.4.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.3.0.tgz"}}}, "cli-table": {"version": "0.3.1", "from": "cli-table@>=0.3.1 <0.4.0", "resolved": "https://registry.npmjs.org/cli-table/-/cli-table-0.3.1.tgz", "dependencies": {"colors": {"version": "1.0.3", "from": "colors@1.0.3", "resolved": "https://registry.npmjs.org/colors/-/colors-1.0.3.tgz"}}}, "cliui": {"version": "2.1.0", "from": "cliui@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/cliui/-/cliui-2.1.0.tgz"}, "coffee-script": {"version": "1.10.0", "from": "coffee-script@>=1.10.0 <1.11.0", "resolved": "https://registry.npmjs.org/coffee-script/-/coffee-script-1.10.0.tgz"}, "colors": {"version": "1.1.2", "from": "colors@>=1.1.2 <1.2.0", "resolved": "https://registry.npmjs.org/colors/-/colors-1.1.2.tgz"}, "combined-stream": {"version": "1.0.5", "from": "combined-stream@>=1.0.5 <1.1.0", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.5.tgz"}, "commander": {"version": "2.8.1", "from": "commander@>=2.8.0 <2.9.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.8.1.tgz"}, "comment-parser": {"version": "0.3.1", "from": "comment-parser@>=0.3.1 <0.4.0", "resolved": "https://registry.npmjs.org/comment-parser/-/comment-parser-0.3.1.tgz"}, "compress-commons": {"version": "1.0.0", "from": "compress-commons@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/compress-commons/-/compress-commons-1.0.0.tgz"}, "concat-map": {"version": "0.0.1", "from": "concat-map@0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"}, "concat-stream": {"version": "1.5.1", "from": "concat-stream@>=1.4.1 <2.0.0", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.5.1.tgz", "dependencies": {"readable-stream": {"version": "2.0.6", "from": "readable-stream@>=2.0.0 <2.1.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz"}}}, "connect": {"version": "3.4.1", "from": "connect@>=3.4.0 <4.0.0", "resolved": "https://registry.npmjs.org/connect/-/connect-3.4.1.tgz"}, "connect-livereload": {"version": "0.5.4", "from": "connect-livereload@>=0.5.0 <0.6.0", "resolved": "https://registry.npmjs.org/connect-livereload/-/connect-livereload-0.5.4.tgz"}, "console-browserify": {"version": "1.1.0", "from": "console-browserify@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/console-browserify/-/console-browserify-1.1.0.tgz"}, "constant-case": {"version": "2.0.0", "from": "constant-case@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/constant-case/-/constant-case-2.0.0.tgz"}, "constantinople": {"version": "3.1.0", "from": "constantinople@>=3.0.1 <4.0.0", "resolved": "https://registry.npmjs.org/constantinople/-/constantinople-3.1.0.tgz"}, "content-type": {"version": "1.0.2", "from": "content-type@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.2.tgz"}, "core-js": {"version": "2.4.0", "from": "core-js@>=2.4.0 <3.0.0", "resolved": "https://registry.npmjs.org/core-js/-/core-js-2.4.0.tgz"}, "core-util-is": {"version": "1.0.2", "from": "core-util-is@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz"}, "crc32-stream": {"version": "1.0.0", "from": "crc32-stream@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/crc32-stream/-/crc32-stream-1.0.0.tgz"}, "cryptiles": {"version": "2.0.5", "from": "cryptiles@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/cryptiles/-/cryptiles-2.0.5.tgz"}, "csscomb": {"version": "3.1.8", "from": "csscomb@>=3.1.0 <3.2.0", "resolved": "https://registry.npmjs.org/csscomb/-/csscomb-3.1.8.tgz", "dependencies": {"commander": {"version": "2.0.0", "from": "commander@2.0.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.0.0.tgz"}}}, "csscomb-core": {"version": "3.0.0-3.1", "from": "csscomb-core@3.0.0-3.1", "resolved": "https://registry.npmjs.org/csscomb-core/-/csscomb-core-3.0.0-3.1.tgz", "dependencies": {"minimatch": {"version": "0.2.12", "from": "minimatch@0.2.12", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.12.tgz"}}}, "csslint": {"version": "0.10.0", "from": "csslint@>=0.10.0 <0.11.0", "resolved": "https://registry.npmjs.org/csslint/-/csslint-0.10.0.tgz"}, "cst": {"version": "0.4.4", "from": "cst@>=0.4.3 <0.5.0", "resolved": "https://registry.npmjs.org/cst/-/cst-0.4.4.tgz"}, "currently-unhandled": {"version": "0.4.1", "from": "currently-unhandled@>=0.4.1 <0.5.0", "resolved": "https://registry.npmjs.org/currently-unhandled/-/currently-unhandled-0.4.1.tgz"}, "cycle": {"version": "1.0.3", "from": "cycle@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/cycle/-/cycle-1.0.3.tgz"}, "dashdash": {"version": "1.14.0", "from": "dashdash@>=1.12.0 <2.0.0", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.0.tgz", "dependencies": {"assert-plus": {"version": "1.0.0", "from": "assert-plus@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"}}}, "date-now": {"version": "0.1.4", "from": "date-now@>=0.1.4 <0.2.0", "resolved": "https://registry.npmjs.org/date-now/-/date-now-0.1.4.tgz"}, "date-time": {"version": "1.0.0", "from": "date-time@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/date-time/-/date-time-1.0.0.tgz"}, "dateformat": {"version": "1.0.12", "from": "dateformat@>=1.0.12 <1.1.0", "resolved": "https://registry.npmjs.org/dateformat/-/dateformat-1.0.12.tgz"}, "debug": {"version": "2.2.0", "from": "debug@>=2.2.0 <2.3.0", "resolved": "https://registry.npmjs.org/debug/-/debug-2.2.0.tgz"}, "decamelize": {"version": "1.2.0", "from": "decamelize@>=1.1.2 <2.0.0", "resolved": "https://registry.npmjs.org/decamelize/-/decamelize-1.2.0.tgz"}, "deep-equal": {"version": "1.0.1", "from": "deep-equal@*", "resolved": "https://registry.npmjs.org/deep-equal/-/deep-equal-1.0.1.tgz"}, "delayed-stream": {"version": "1.0.0", "from": "delayed-stream@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"}, "depd": {"version": "1.1.0", "from": "depd@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/depd/-/depd-1.1.0.tgz"}, "destroy": {"version": "1.0.4", "from": "destroy@>=1.0.4 <1.1.0", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz"}, "diff": {"version": "1.3.2", "from": "diff@>=1.3.0 <1.4.0", "resolved": "https://registry.npmjs.org/diff/-/diff-1.3.2.tgz"}, "doctypes": {"version": "1.0.0", "from": "doctypes@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/doctypes/-/doctypes-1.0.0.tgz"}, "dom-serializer": {"version": "0.1.0", "from": "dom-serializer@>=0.0.0 <1.0.0", "resolved": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.1.0.tgz", "dependencies": {"domelementtype": {"version": "1.1.3", "from": "domelementtype@>=1.1.1 <1.2.0", "resolved": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.1.3.tgz"}, "entities": {"version": "1.1.1", "from": "entities@>=1.1.1 <1.2.0", "resolved": "https://registry.npmjs.org/entities/-/entities-1.1.1.tgz"}}}, "domelementtype": {"version": "1.3.0", "from": "domelementtype@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.0.tgz"}, "domhandler": {"version": "2.3.0", "from": "domhandler@>=2.3.0 <2.4.0", "resolved": "https://registry.npmjs.org/domhandler/-/domhandler-2.3.0.tgz"}, "domutils": {"version": "1.5.1", "from": "domutils@>=1.5.0 <1.6.0", "resolved": "https://registry.npmjs.org/domutils/-/domutils-1.5.1.tgz"}, "dot-case": {"version": "2.1.0", "from": "dot-case@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/dot-case/-/dot-case-2.1.0.tgz"}, "ecc-jsbn": {"version": "0.1.1", "from": "ecc-jsbn@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.1.tgz"}, "ee-first": {"version": "1.1.1", "from": "ee-first@1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"}, "encodeurl": {"version": "1.0.1", "from": "encodeurl@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.1.tgz"}, "end-of-stream": {"version": "1.1.0", "from": "end-of-stream@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.1.0.tgz"}, "entities": {"version": "1.0.0", "from": "entities@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/entities/-/entities-1.0.0.tgz"}, "errno": {"version": "0.1.4", "from": "errno@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/errno/-/errno-0.1.4.tgz"}, "error-ex": {"version": "1.3.0", "from": "error-ex@>=1.2.0 <2.0.0", "resolved": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.0.tgz"}, "es6-promise": {"version": "2.3.0", "from": "es6-promise@>=2.3.0 <2.4.0", "resolved": "https://registry.npmjs.org/es6-promise/-/es6-promise-2.3.0.tgz"}, "escape-html": {"version": "1.0.3", "from": "escape-html@>=1.0.3 <1.1.0", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"}, "escape-string-regexp": {"version": "1.0.5", "from": "escape-string-regexp@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"}, "esprima": {"version": "2.7.2", "from": "esprima@>=2.6.0 <3.0.0", "resolved": "https://registry.npmjs.org/esprima/-/esprima-2.7.2.tgz"}, "estraverse": {"version": "4.2.0", "from": "estraverse@>=4.1.0 <5.0.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.2.0.tgz"}, "etag": {"version": "1.7.0", "from": "etag@>=1.7.0 <1.8.0", "resolved": "https://registry.npmjs.org/etag/-/etag-1.7.0.tgz"}, "eventemitter2": {"version": "0.4.14", "from": "eventemitter2@>=0.4.13 <0.5.0", "resolved": "https://registry.npmjs.org/eventemitter2/-/eventemitter2-0.4.14.tgz"}, "exit": {"version": "0.1.2", "from": "exit@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz"}, "extend": {"version": "3.0.0", "from": "extend@>=3.0.0 <3.1.0", "resolved": "https://registry.npmjs.org/extend/-/extend-3.0.0.tgz"}, "extract-zip": {"version": "1.5.0", "from": "extract-zip@>=1.5.0 <1.6.0", "resolved": "https://registry.npmjs.org/extract-zip/-/extract-zip-1.5.0.tgz", "dependencies": {"concat-stream": {"version": "1.5.0", "from": "concat-stream@1.5.0", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-1.5.0.tgz"}, "debug": {"version": "0.7.4", "from": "debug@0.7.4", "resolved": "https://registry.npmjs.org/debug/-/debug-0.7.4.tgz"}, "minimist": {"version": "0.0.8", "from": "minimist@0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz"}, "mkdirp": {"version": "0.5.0", "from": "mkdirp@0.5.0", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.0.tgz"}, "readable-stream": {"version": "2.0.6", "from": "readable-stream@>=2.0.0 <2.1.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz"}}}, "extsprintf": {"version": "1.0.2", "from": "extsprintf@1.0.2", "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.0.2.tgz"}, "eyes": {"version": "0.1.8", "from": "eyes@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/eyes/-/eyes-0.1.8.tgz"}, "faye-websocket": {"version": "0.10.0", "from": "faye-websocket@>=0.10.0 <0.11.0", "resolved": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.10.0.tgz"}, "fd-slicer": {"version": "1.0.1", "from": "fd-slicer@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.0.1.tgz"}, "fg-lodash": {"version": "0.0.2", "from": "fg-lodash@0.0.2", "resolved": "https://registry.npmjs.org/fg-lodash/-/fg-lodash-0.0.2.tgz", "dependencies": {"lodash": {"version": "2.4.2", "from": "lodash@>=2.4.1 <3.0.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-2.4.2.tgz"}, "underscore.string": {"version": "2.3.3", "from": "underscore.string@>=2.3.3 <2.4.0", "resolved": "https://registry.npmjs.org/underscore.string/-/underscore.string-2.3.3.tgz"}}}, "figures": {"version": "1.7.0", "from": "figures@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/figures/-/figures-1.7.0.tgz"}, "file-sync-cmp": {"version": "0.1.1", "from": "file-sync-cmp@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/file-sync-cmp/-/file-sync-cmp-0.1.1.tgz"}, "finalhandler": {"version": "0.4.1", "from": "finalhandler@0.4.1", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-0.4.1.tgz"}, "find-up": {"version": "1.1.2", "from": "find-up@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-1.1.2.tgz"}, "findup-sync": {"version": "0.3.0", "from": "findup-sync@>=0.3.0 <0.4.0", "resolved": "https://registry.npmjs.org/findup-sync/-/findup-sync-0.3.0.tgz", "dependencies": {"glob": {"version": "5.0.15", "from": "glob@>=5.0.0 <5.1.0", "resolved": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz"}}}, "forever-agent": {"version": "0.6.1", "from": "forever-agent@>=0.6.1 <0.7.0", "resolved": "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz"}, "form-data": {"version": "1.0.0-rc4", "from": "form-data@>=1.0.0-rc4 <1.1.0", "resolved": "https://registry.npmjs.org/form-data/-/form-data-1.0.0-rc4.tgz"}, "fresh": {"version": "0.3.0", "from": "fresh@0.3.0", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.3.0.tgz"}, "fs-extra": {"version": "0.26.7", "from": "fs-extra@>=0.26.4 <0.27.0", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-0.26.7.tgz"}, "fs.realpath": {"version": "1.0.0", "from": "fs.realpath@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"}, "gaze": {"version": "1.1.0", "from": "gaze@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/gaze/-/gaze-1.1.0.tgz"}, "generate-function": {"version": "2.0.0", "from": "generate-function@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/generate-function/-/generate-function-2.0.0.tgz"}, "generate-object-property": {"version": "1.2.0", "from": "generate-object-property@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/generate-object-property/-/generate-object-property-1.2.0.tgz"}, "get-stdin": {"version": "4.0.1", "from": "get-stdin@>=4.0.1 <5.0.0", "resolved": "https://registry.npmjs.org/get-stdin/-/get-stdin-4.0.1.tgz"}, "getobject": {"version": "0.1.0", "from": "getobject@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/getobject/-/getobject-0.1.0.tgz"}, "getpass": {"version": "0.1.6", "from": "getpass@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.6.tgz", "dependencies": {"assert-plus": {"version": "1.0.0", "from": "assert-plus@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"}}}, "glob": {"version": "7.0.5", "from": "glob@>=7.0.3 <7.1.0", "resolved": "https://registry.npmjs.org/glob/-/glob-7.0.5.tgz"}, "globule": {"version": "1.0.0", "from": "globule@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/globule/-/globule-1.0.0.tgz", "dependencies": {"lodash": {"version": "4.9.0", "from": "lodash@>=4.9.0 <4.10.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.9.0.tgz"}}}, "gonzales-pe": {"version": "3.0.0-28", "from": "gonzales-pe@3.0.0-28", "resolved": "https://registry.npmjs.org/gonzales-pe/-/gonzales-pe-3.0.0-28.tgz"}, "graceful-fs": {"version": "4.1.4", "from": "graceful-fs@>=4.1.2 <5.0.0", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.1.4.tgz"}, "graceful-readlink": {"version": "1.0.1", "from": "graceful-readlink@>=1.0.0", "resolved": "https://registry.npmjs.org/graceful-readlink/-/graceful-readlink-1.0.1.tgz"}, "grunt": {"version": "1.0.1", "from": "grunt@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/grunt/-/grunt-1.0.1.tgz", "dependencies": {"grunt-cli": {"version": "1.2.0", "from": "grunt-cli@>=1.2.0 <1.3.0", "resolved": "https://registry.npmjs.org/grunt-cli/-/grunt-cli-1.2.0.tgz"}}}, "grunt-autoprefixer": {"version": "3.0.4", "from": "grunt-autoprefixer@>=3.0.4 <3.1.0", "resolved": "https://registry.npmjs.org/grunt-autoprefixer/-/grunt-autoprefixer-3.0.4.tgz", "dependencies": {"ansi-regex": {"version": "1.1.1", "from": "ansi-regex@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-1.1.1.tgz"}, "chalk": {"version": "1.0.0", "from": "chalk@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-1.0.0.tgz"}, "has-ansi": {"version": "1.0.3", "from": "has-ansi@>=1.0.3 <2.0.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-1.0.3.tgz"}, "strip-ansi": {"version": "2.0.1", "from": "strip-ansi@>=2.0.1 <3.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-2.0.1.tgz"}, "supports-color": {"version": "1.3.1", "from": "supports-color@>=1.3.0 <2.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-1.3.1.tgz"}}}, "grunt-contrib-clean": {"version": "1.0.0", "from": "grunt-contrib-clean@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-contrib-clean/-/grunt-contrib-clean-1.0.0.tgz", "dependencies": {"rimraf": {"version": "2.5.3", "from": "rimraf@>=2.5.1 <3.0.0", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.5.3.tgz"}}}, "grunt-contrib-compress": {"version": "1.3.0", "from": "grunt-contrib-compress@>=1.3.0 <1.4.0", "resolved": "https://registry.npmjs.org/grunt-contrib-compress/-/grunt-contrib-compress-1.3.0.tgz", "dependencies": {"lodash": {"version": "4.13.1", "from": "lodash@>=4.7.0 <5.0.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.13.1.tgz"}}}, "grunt-contrib-concat": {"version": "1.0.1", "from": "grunt-contrib-concat@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-contrib-concat/-/grunt-contrib-concat-1.0.1.tgz", "dependencies": {"source-map": {"version": "0.5.6", "from": "source-map@>=0.5.3 <0.6.0", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.6.tgz"}}}, "grunt-contrib-connect": {"version": "1.0.2", "from": "grunt-contrib-connect@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-contrib-connect/-/grunt-contrib-connect-1.0.2.tgz"}, "grunt-contrib-copy": {"version": "1.0.0", "from": "grunt-contrib-copy@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-contrib-copy/-/grunt-contrib-copy-1.0.0.tgz"}, "grunt-contrib-csslint": {"version": "1.0.0", "from": "grunt-contrib-csslint@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-contrib-csslint/-/grunt-contrib-csslint-1.0.0.tgz"}, "grunt-contrib-cssmin": {"version": "1.0.1", "from": "grunt-contrib-cssmin@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-contrib-cssmin/-/grunt-contrib-cssmin-1.0.1.tgz"}, "grunt-contrib-htmlmin": {"version": "1.5.0", "from": "grunt-contrib-htmlmin@>=1.5.0 <1.6.0", "resolved": "https://registry.npmjs.org/grunt-contrib-htmlmin/-/grunt-contrib-htmlmin-1.5.0.tgz"}, "grunt-contrib-jshint": {"version": "1.0.0", "from": "grunt-contrib-jshint@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-contrib-jshint/-/grunt-contrib-jshint-1.0.0.tgz"}, "grunt-contrib-less": {"version": "1.3.0", "from": "grunt-contrib-less@>=1.3.0 <1.4.0", "resolved": "https://registry.npmjs.org/grunt-contrib-less/-/grunt-contrib-less-1.3.0.tgz", "dependencies": {"lodash": {"version": "4.13.1", "from": "lodash@>=4.8.2 <5.0.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.13.1.tgz"}}}, "grunt-contrib-pug": {"version": "1.0.0", "from": "grunt-contrib-pug@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-contrib-pug/-/grunt-contrib-pug-1.0.0.tgz"}, "grunt-contrib-qunit": {"version": "0.7.0", "from": "grunt-contrib-qunit@>=0.7.0 <0.8.0", "resolved": "https://registry.npmjs.org/grunt-contrib-qunit/-/grunt-contrib-qunit-0.7.0.tgz"}, "grunt-contrib-uglify": {"version": "1.0.1", "from": "grunt-contrib-uglify@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-contrib-uglify/-/grunt-contrib-uglify-1.0.1.tgz", "dependencies": {"lodash": {"version": "4.13.1", "from": "lodash@>=4.0.1 <5.0.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.13.1.tgz"}}}, "grunt-contrib-watch": {"version": "1.0.0", "from": "grunt-contrib-watch@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-contrib-watch/-/grunt-contrib-watch-1.0.0.tgz"}, "grunt-csscomb": {"version": "3.1.1", "from": "grunt-csscomb@>=3.1.0 <3.2.0", "resolved": "https://registry.npmjs.org/grunt-csscomb/-/grunt-csscomb-3.1.1.tgz"}, "grunt-exec": {"version": "1.0.0", "from": "grunt-exec@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-exec/-/grunt-exec-1.0.0.tgz"}, "grunt-html": {"version": "8.0.2", "from": "grunt-html@>=8.0.1 <8.1.0", "resolved": "https://registry.npmjs.org/grunt-html/-/grunt-html-8.0.2.tgz"}, "grunt-jekyll": {"version": "0.4.4", "from": "grunt-jekyll@>=0.4.4 <0.5.0", "resolved": "https://registry.npmjs.org/grunt-jekyll/-/grunt-jekyll-0.4.4.tgz"}, "grunt-jscs": {"version": "3.0.1", "from": "grunt-jscs@>=3.0.1 <3.1.0", "resolved": "https://registry.npmjs.org/grunt-jscs/-/grunt-jscs-3.0.1.tgz", "dependencies": {"lodash": {"version": "4.6.1", "from": "lodash@>=4.6.1 <4.7.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.6.1.tgz"}}}, "grunt-known-options": {"version": "1.1.0", "from": "grunt-known-options@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/grunt-known-options/-/grunt-known-options-1.1.0.tgz"}, "grunt-legacy-log": {"version": "1.0.0", "from": "grunt-legacy-log@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-legacy-log/-/grunt-legacy-log-1.0.0.tgz"}, "grunt-legacy-log-utils": {"version": "1.0.0", "from": "grunt-legacy-log-utils@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-legacy-log-utils/-/grunt-legacy-log-utils-1.0.0.tgz", "dependencies": {"lodash": {"version": "4.3.0", "from": "lodash@>=4.3.0 <4.4.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.3.0.tgz"}}}, "grunt-legacy-util": {"version": "1.0.0", "from": "grunt-legacy-util@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/grunt-legacy-util/-/grunt-legacy-util-1.0.0.tgz", "dependencies": {"lodash": {"version": "4.3.0", "from": "lodash@>=4.3.0 <4.4.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.3.0.tgz"}}}, "grunt-lib-phantomjs": {"version": "0.6.0", "from": "grunt-lib-phantomjs@>=0.6.0 <0.7.0", "resolved": "https://registry.npmjs.org/grunt-lib-phantomjs/-/grunt-lib-phantomjs-0.6.0.tgz", "dependencies": {"semver": {"version": "1.0.14", "from": "semver@>=1.0.14 <1.1.0", "resolved": "https://registry.npmjs.org/semver/-/semver-1.0.14.tgz"}}}, "grunt-saucelabs": {"version": "9.0.0", "from": "grunt-saucelabs@>=9.0.0 <9.1.0", "resolved": "https://registry.npmjs.org/grunt-saucelabs/-/grunt-saucelabs-9.0.0.tgz", "dependencies": {"lodash": {"version": "4.13.1", "from": "lodash@>=4.13.1 <4.14.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.13.1.tgz"}}}, "gzip-size": {"version": "1.0.0", "from": "gzip-size@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/gzip-size/-/gzip-size-1.0.0.tgz"}, "har-validator": {"version": "2.0.6", "from": "har-validator@>=2.0.6 <2.1.0", "resolved": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.6.tgz", "dependencies": {"commander": {"version": "2.9.0", "from": "commander@>=2.9.0 <3.0.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.9.0.tgz"}}}, "has-ansi": {"version": "2.0.0", "from": "has-ansi@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/has-ansi/-/has-ansi-2.0.0.tgz"}, "has-color": {"version": "0.1.7", "from": "has-color@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/has-color/-/has-color-0.1.7.tgz"}, "hasha": {"version": "2.2.0", "from": "hasha@>=2.2.0 <3.0.0", "resolved": "https://registry.npmjs.org/hasha/-/hasha-2.2.0.tgz"}, "hawk": {"version": "3.1.3", "from": "hawk@>=3.1.3 <3.2.0", "resolved": "https://registry.npmjs.org/hawk/-/hawk-3.1.3.tgz"}, "he": {"version": "1.1.0", "from": "he@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/he/-/he-1.1.0.tgz"}, "header-case": {"version": "1.0.0", "from": "header-case@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/header-case/-/header-case-1.0.0.tgz"}, "hoek": {"version": "2.16.3", "from": "hoek@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/hoek/-/hoek-2.16.3.tgz"}, "hooker": {"version": "0.2.3", "from": "hooker@>=0.2.3 <0.3.0", "resolved": "https://registry.npmjs.org/hooker/-/hooker-0.2.3.tgz"}, "hosted-git-info": {"version": "2.1.5", "from": "hosted-git-info@>=2.1.4 <3.0.0", "resolved": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.1.5.tgz"}, "html-minifier": {"version": "2.1.7", "from": "html-minifier@>=2.1.7 <2.2.0", "resolved": "https://registry.npmjs.org/html-minifier/-/html-minifier-2.1.7.tgz", "dependencies": {"commander": {"version": "2.9.0", "from": "commander@>=2.9.0 <2.10.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.9.0.tgz"}}}, "htmlparser2": {"version": "3.8.3", "from": "htmlparser2@>=3.8.0 <3.9.0", "resolved": "https://registry.npmjs.org/htmlparser2/-/htmlparser2-3.8.3.tgz", "dependencies": {"isarray": {"version": "0.0.1", "from": "isarray@0.0.1", "resolved": "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz"}, "readable-stream": {"version": "1.1.14", "from": "readable-stream@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-1.1.14.tgz"}}}, "http-errors": {"version": "1.5.0", "from": "http-errors@>=1.5.0 <1.6.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-1.5.0.tgz"}, "http-signature": {"version": "1.1.1", "from": "http-signature@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/http-signature/-/http-signature-1.1.1.tgz"}, "http2": {"version": "3.3.4", "from": "http2@>=3.3.4 <4.0.0", "resolved": "https://registry.npmjs.org/http2/-/http2-3.3.4.tgz"}, "https-proxy-agent": {"version": "1.0.0", "from": "https-proxy-agent@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-1.0.0.tgz"}, "i": {"version": "0.3.5", "from": "i@>=0.3.0 <0.4.0", "resolved": "https://registry.npmjs.org/i/-/i-0.3.5.tgz"}, "iconv-lite": {"version": "0.4.13", "from": "iconv-lite@>=0.4.13 <0.5.0", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.13.tgz"}, "image-size": {"version": "0.4.0", "from": "image-size@>=0.4.0 <0.5.0", "resolved": "https://registry.npmjs.org/image-size/-/image-size-0.4.0.tgz"}, "indent-string": {"version": "2.1.0", "from": "indent-string@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/indent-string/-/indent-string-2.1.0.tgz"}, "inflight": {"version": "1.0.5", "from": "inflight@>=1.0.4 <2.0.0", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.5.tgz"}, "inherit": {"version": "2.2.4", "from": "inherit@>=2.2.2 <3.0.0", "resolved": "https://registry.npmjs.org/inherit/-/inherit-2.2.4.tgz"}, "inherits": {"version": "2.0.1", "from": "inherits@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.1.tgz"}, "interpret": {"version": "1.0.1", "from": "interpret@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/interpret/-/interpret-1.0.1.tgz"}, "is-arrayish": {"version": "0.2.1", "from": "is-arrayish@>=0.2.1 <0.3.0", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"}, "is-buffer": {"version": "1.1.3", "from": "is-buffer@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.3.tgz"}, "is-builtin-module": {"version": "1.0.0", "from": "is-builtin-module@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-builtin-module/-/is-builtin-module-1.0.0.tgz"}, "is-expression": {"version": "2.0.1", "from": "is-expression@>=2.0.1 <3.0.0", "resolved": "https://registry.npmjs.org/is-expression/-/is-expression-2.0.1.tgz", "dependencies": {"acorn": {"version": "3.1.0", "from": "acorn@>=3.1.0 <3.2.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-3.1.0.tgz"}}}, "is-finite": {"version": "1.0.1", "from": "is-finite@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-finite/-/is-finite-1.0.1.tgz"}, "is-lower-case": {"version": "1.1.3", "from": "is-lower-case@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-lower-case/-/is-lower-case-1.1.3.tgz"}, "is-my-json-valid": {"version": "2.13.1", "from": "is-my-json-valid@>=2.12.4 <3.0.0", "resolved": "https://registry.npmjs.org/is-my-json-valid/-/is-my-json-valid-2.13.1.tgz"}, "is-promise": {"version": "2.1.0", "from": "is-promise@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/is-promise/-/is-promise-2.1.0.tgz"}, "is-property": {"version": "1.0.2", "from": "is-property@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-property/-/is-property-1.0.2.tgz"}, "is-regex": {"version": "1.0.3", "from": "is-regex@>=1.0.3 <2.0.0", "resolved": "https://registry.npmjs.org/is-regex/-/is-regex-1.0.3.tgz"}, "is-stream": {"version": "1.1.0", "from": "is-stream@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz"}, "is-typedarray": {"version": "1.0.0", "from": "is-typedarray@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"}, "is-upper-case": {"version": "1.1.2", "from": "is-upper-case@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/is-upper-case/-/is-upper-case-1.1.2.tgz"}, "is-utf8": {"version": "0.2.1", "from": "is-utf8@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/is-utf8/-/is-utf8-0.2.1.tgz"}, "isarray": {"version": "1.0.0", "from": "isarray@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"}, "isexe": {"version": "1.1.2", "from": "isexe@>=1.1.1 <2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-1.1.2.tgz"}, "isstream": {"version": "0.1.2", "from": "isstream@>=0.1.2 <0.2.0", "resolved": "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz"}, "jodid25519": {"version": "1.0.2", "from": "jodid25519@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/jodid25519/-/jodid25519-1.0.2.tgz"}, "js-base64": {"version": "2.1.9", "from": "js-base64@>=2.1.8 <2.2.0", "resolved": "https://registry.npmjs.org/js-base64/-/js-base64-2.1.9.tgz"}, "js-stringify": {"version": "1.0.2", "from": "js-stringify@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/js-stringify/-/js-stringify-1.0.2.tgz"}, "js-yaml": {"version": "3.5.5", "from": "js-yaml@>=3.5.2 <3.6.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.5.5.tgz"}, "jsbn": {"version": "0.1.0", "from": "jsbn@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.0.tgz"}, "jscs": {"version": "3.0.7", "from": "jscs@>=3.0.5 <3.1.0", "resolved": "https://registry.npmjs.org/jscs/-/jscs-3.0.7.tgz", "dependencies": {"commander": {"version": "2.9.0", "from": "commander@>=2.9.0 <2.10.0", "resolved": "https://registry.npmjs.org/commander/-/commander-2.9.0.tgz"}, "glob": {"version": "5.0.15", "from": "glob@>=5.0.1 <6.0.0", "resolved": "https://registry.npmjs.org/glob/-/glob-5.0.15.tgz"}, "js-yaml": {"version": "3.4.6", "from": "js-yaml@>=3.4.0 <3.5.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.4.6.tgz"}, "vow": {"version": "0.4.12", "from": "vow@>=0.4.8 <0.5.0", "resolved": "https://registry.npmjs.org/vow/-/vow-0.4.12.tgz"}, "vow-fs": {"version": "0.3.5", "from": "vow-fs@>=0.3.4 <0.4.0", "resolved": "https://registry.npmjs.org/vow-fs/-/vow-fs-0.3.5.tgz", "dependencies": {"glob": {"version": "4.5.3", "from": "glob@>=4.3.1 <5.0.0", "resolved": "https://registry.npmjs.org/glob/-/glob-4.5.3.tgz"}, "minimatch": {"version": "2.0.10", "from": "minimatch@>=2.0.1 <3.0.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.10.tgz"}}}, "vow-queue": {"version": "0.4.2", "from": "vow-queue@>=0.4.1 <0.5.0", "resolved": "https://registry.npmjs.org/vow-queue/-/vow-queue-0.4.2.tgz"}}}, "jscs-jsdoc": {"version": "2.0.0", "from": "jscs-jsdoc@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/jscs-jsdoc/-/jscs-jsdoc-2.0.0.tgz"}, "jscs-preset-wikimedia": {"version": "1.0.0", "from": "jscs-preset-wikimedia@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/jscs-preset-wikimedia/-/jscs-preset-wikimedia-1.0.0.tgz"}, "jsdoctypeparser": {"version": "1.2.0", "from": "jsdoctypeparser@>=1.2.0 <1.3.0", "resolved": "https://registry.npmjs.org/jsdoctypeparser/-/jsdoctypeparser-1.2.0.tgz"}, "jshint": {"version": "2.9.2", "from": "jshint@>=2.9.1 <2.10.0", "resolved": "https://registry.npmjs.org/jshint/-/jshint-2.9.2.tgz", "dependencies": {"lodash": {"version": "3.7.0", "from": "lodash@>=3.7.0 <3.8.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.7.0.tgz"}, "minimatch": {"version": "2.0.10", "from": "minimatch@>=2.0.0 <2.1.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-2.0.10.tgz"}, "shelljs": {"version": "0.3.0", "from": "shelljs@>=0.3.0 <0.4.0", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.3.0.tgz"}}}, "json-schema": {"version": "0.2.2", "from": "json-schema@0.2.2", "resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.2.tgz"}, "json-stringify-safe": {"version": "5.0.1", "from": "json-stringify-safe@>=5.0.1 <5.1.0", "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"}, "jsonfile": {"version": "2.3.1", "from": "jsonfile@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-2.3.1.tgz"}, "jsonlint": {"version": "1.6.2", "from": "jsonlint@>=1.6.2 <1.7.0", "resolved": "https://registry.npmjs.org/jsonlint/-/jsonlint-1.6.2.tgz"}, "jsonpointer": {"version": "2.0.0", "from": "j<PERSON><PERSON>er@2.0.0", "resolved": "https://registry.npmjs.org/jsonpointer/-/jsonpointer-2.0.0.tgz"}, "jsprim": {"version": "1.3.0", "from": "jsprim@>=1.2.2 <2.0.0", "resolved": "https://registry.npmjs.org/jsprim/-/jsprim-1.3.0.tgz"}, "jstransformer": {"version": "1.0.0", "from": "jstransformer@1.0.0", "resolved": "https://registry.npmjs.org/jstransformer/-/jstransformer-1.0.0.tgz"}, "JSV": {"version": "4.0.2", "from": "JSV@>=4.0.0", "resolved": "https://registry.npmjs.org/JSV/-/JSV-4.0.2.tgz"}, "kew": {"version": "0.7.0", "from": "kew@>=0.7.0 <0.8.0", "resolved": "https://registry.npmjs.org/kew/-/kew-0.7.0.tgz"}, "kind-of": {"version": "3.0.3", "from": "kind-of@>=3.0.2 <4.0.0", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-3.0.3.tgz"}, "klaw": {"version": "1.3.0", "from": "klaw@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/klaw/-/klaw-1.3.0.tgz"}, "lazy-cache": {"version": "1.0.4", "from": "lazy-cache@>=1.0.3 <2.0.0", "resolved": "https://registry.npmjs.org/lazy-cache/-/lazy-cache-1.0.4.tgz"}, "lazystream": {"version": "1.0.0", "from": "lazystream@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/lazystream/-/lazystream-1.0.0.tgz"}, "less": {"version": "2.6.1", "from": "less@>=2.6.0 <2.7.0", "resolved": "https://registry.npmjs.org/less/-/less-2.6.1.tgz", "dependencies": {"source-map": {"version": "0.5.6", "from": "source-map@>=0.5.3 <0.6.0", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.6.tgz"}}}, "linkify-it": {"version": "2.0.0", "from": "linkify-it@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/linkify-it/-/linkify-it-2.0.0.tgz"}, "livereload-js": {"version": "2.2.2", "from": "livereload-js@>=2.2.0 <3.0.0", "resolved": "https://registry.npmjs.org/livereload-js/-/livereload-js-2.2.2.tgz"}, "load-grunt-tasks": {"version": "3.5.0", "from": "load-grunt-tasks@>=3.5.0 <3.6.0", "resolved": "https://registry.npmjs.org/load-grunt-tasks/-/load-grunt-tasks-3.5.0.tgz"}, "load-json-file": {"version": "1.1.0", "from": "load-json-file@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/load-json-file/-/load-json-file-1.1.0.tgz"}, "lodash": {"version": "3.10.1", "from": "lodash@>=3.10.1 <3.11.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-3.10.1.tgz"}, "longest": {"version": "1.0.1", "from": "longest@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/longest/-/longest-1.0.1.tgz"}, "loud-rejection": {"version": "1.6.0", "from": "loud-rejection@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/loud-rejection/-/loud-rejection-1.6.0.tgz"}, "lower-case": {"version": "1.1.3", "from": "lower-case@>=1.1.1 <2.0.0", "resolved": "https://registry.npmjs.org/lower-case/-/lower-case-1.1.3.tgz"}, "lower-case-first": {"version": "1.0.2", "from": "lower-case-first@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/lower-case-first/-/lower-case-first-1.0.2.tgz"}, "lru-cache": {"version": "2.7.3", "from": "lru-cache@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-2.7.3.tgz"}, "map-obj": {"version": "1.0.1", "from": "map-obj@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/map-obj/-/map-obj-1.0.1.tgz"}, "markdown-it": {"version": "7.0.0", "from": "markdown-it@>=7.0.0 <8.0.0", "resolved": "https://registry.npmjs.org/markdown-it/-/markdown-it-7.0.0.tgz", "dependencies": {"entities": {"version": "1.1.1", "from": "entities@>=1.1.1 <1.2.0", "resolved": "https://registry.npmjs.org/entities/-/entities-1.1.1.tgz"}}}, "maxmin": {"version": "1.1.0", "from": "maxmin@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/maxmin/-/maxmin-1.1.0.tgz", "dependencies": {"pretty-bytes": {"version": "1.0.4", "from": "pretty-bytes@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-1.0.4.tgz"}}}, "mdurl": {"version": "1.0.1", "from": "mdurl@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/mdurl/-/mdurl-1.0.1.tgz"}, "media-typer": {"version": "0.3.0", "from": "media-typer@0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"}, "meow": {"version": "3.7.0", "from": "meow@>=3.3.0 <4.0.0", "resolved": "https://registry.npmjs.org/meow/-/meow-3.7.0.tgz"}, "mime": {"version": "1.3.4", "from": "mime@1.3.4", "resolved": "https://registry.npmjs.org/mime/-/mime-1.3.4.tgz"}, "mime-db": {"version": "1.23.0", "from": "mime-db@>=1.23.0 <1.24.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.23.0.tgz"}, "mime-types": {"version": "2.1.11", "from": "mime-types@>=2.1.11 <2.2.0", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.11.tgz"}, "minimatch": {"version": "3.0.2", "from": "minimatch@>=3.0.2 <4.0.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.0.2.tgz"}, "minimist": {"version": "1.2.0", "from": "minimist@>=1.1.3 <2.0.0", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz"}, "mkdirp": {"version": "0.5.1", "from": "mkdirp@>=0.5.0 <0.6.0", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.1.tgz", "dependencies": {"minimist": {"version": "0.0.8", "from": "minimist@0.0.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz"}}}, "morgan": {"version": "1.7.0", "from": "morgan@>=1.6.1 <2.0.0", "resolved": "https://registry.npmjs.org/morgan/-/morgan-1.7.0.tgz"}, "ms": {"version": "0.7.1", "from": "ms@0.7.1", "resolved": "https://registry.npmjs.org/ms/-/ms-0.7.1.tgz"}, "multimatch": {"version": "2.1.0", "from": "multimatch@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/multimatch/-/multimatch-2.1.0.tgz"}, "mute-stream": {"version": "0.0.6", "from": "mute-stream@>=0.0.4 <0.1.0", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.6.tgz"}, "natural-compare": {"version": "1.2.2", "from": "natural-compare@>=1.2.2 <1.3.0", "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.2.2.tgz"}, "ncname": {"version": "1.0.0", "from": "ncname@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/ncname/-/ncname-1.0.0.tgz"}, "ncp": {"version": "0.4.2", "from": "ncp@>=0.4.0 <0.5.0", "resolved": "https://registry.npmjs.org/ncp/-/ncp-0.4.2.tgz"}, "negotiator": {"version": "0.6.1", "from": "negotiator@0.6.1", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.1.tgz"}, "no-case": {"version": "2.3.0", "from": "no-case@>=2.2.0 <3.0.0", "resolved": "https://registry.npmjs.org/no-case/-/no-case-2.3.0.tgz"}, "node-int64": {"version": "0.4.0", "from": "node-int64@>=0.4.0 <0.5.0", "resolved": "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz"}, "node-uuid": {"version": "1.4.7", "from": "node-uuid@>=1.4.7 <1.5.0", "resolved": "https://registry.npmjs.org/node-uuid/-/node-uuid-1.4.7.tgz"}, "nomnom": {"version": "1.8.1", "from": "nomnom@>=1.5.0", "resolved": "https://registry.npmjs.org/nomnom/-/nomnom-1.8.1.tgz", "dependencies": {"ansi-styles": {"version": "1.0.0", "from": "ansi-styles@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-1.0.0.tgz"}, "chalk": {"version": "0.4.0", "from": "chalk@>=0.4.0 <0.5.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-0.4.0.tgz"}, "strip-ansi": {"version": "0.1.1", "from": "strip-ansi@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-0.1.1.tgz"}}}, "nopt": {"version": "3.0.6", "from": "nopt@>=3.0.6 <3.1.0", "resolved": "https://registry.npmjs.org/nopt/-/nopt-3.0.6.tgz"}, "normalize-package-data": {"version": "2.3.5", "from": "normalize-package-data@>=2.3.4 <3.0.0", "resolved": "https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.3.5.tgz"}, "normalize-path": {"version": "2.0.1", "from": "normalize-path@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.0.1.tgz"}, "num2fraction": {"version": "1.2.2", "from": "num2fraction@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/num2fraction/-/num2fraction-1.2.2.tgz"}, "number-is-nan": {"version": "1.0.0", "from": "number-is-nan@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/number-is-nan/-/number-is-nan-1.0.0.tgz"}, "oauth-sign": {"version": "0.8.2", "from": "oauth-sign@>=0.8.1 <0.9.0", "resolved": "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.8.2.tgz"}, "object-assign": {"version": "4.1.0", "from": "object-assign@>=4.0.1 <5.0.0", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.0.tgz"}, "on-finished": {"version": "2.3.0", "from": "on-finished@>=2.3.0 <2.4.0", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz"}, "on-headers": {"version": "1.0.1", "from": "on-headers@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.1.tgz"}, "once": {"version": "1.3.3", "from": "once@>=1.3.0 <2.0.0", "resolved": "https://registry.npmjs.org/once/-/once-1.3.3.tgz"}, "opn": {"version": "4.0.2", "from": "opn@>=4.0.0 <5.0.0", "resolved": "https://registry.npmjs.org/opn/-/opn-4.0.2.tgz"}, "os-tmpdir": {"version": "1.0.1", "from": "os-tmpdir@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.1.tgz"}, "package": {"version": "1.0.1", "from": "package@>=1.0.0 <1.2.0", "resolved": "https://registry.npmjs.org/package/-/package-1.0.1.tgz"}, "pako": {"version": "0.2.8", "from": "pako@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/pako/-/pako-0.2.8.tgz"}, "param-case": {"version": "2.1.0", "from": "param-case@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/param-case/-/param-case-2.1.0.tgz"}, "parse-json": {"version": "2.2.0", "from": "parse-json@>=2.2.0 <3.0.0", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-2.2.0.tgz"}, "parse-ms": {"version": "1.0.1", "from": "parse-ms@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/parse-ms/-/parse-ms-1.0.1.tgz"}, "parserlib": {"version": "0.2.5", "from": "parserlib@>=0.2.2 <0.3.0", "resolved": "https://registry.npmjs.org/parserlib/-/parserlib-0.2.5.tgz"}, "parseurl": {"version": "1.3.1", "from": "parseurl@>=1.3.1 <1.4.0", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.1.tgz"}, "pascal-case": {"version": "2.0.0", "from": "pascal-case@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/pascal-case/-/pascal-case-2.0.0.tgz"}, "path-case": {"version": "2.1.0", "from": "path-case@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/path-case/-/path-case-2.1.0.tgz"}, "path-exists": {"version": "2.1.0", "from": "path-exists@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-2.1.0.tgz"}, "path-is-absolute": {"version": "1.0.0", "from": "path-is-absolute@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.0.tgz"}, "path-type": {"version": "1.1.0", "from": "path-type@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-1.1.0.tgz"}, "pathval": {"version": "0.1.1", "from": "pathval@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/pathval/-/pathval-0.1.1.tgz"}, "pend": {"version": "1.2.0", "from": "pend@>=1.2.0 <1.3.0", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz"}, "phantomjs": {"version": "1.9.20", "from": "phantomjs@>=1.9.0-1 <1.10.0", "resolved": "https://registry.npmjs.org/phantomjs/-/phantomjs-1.9.20.tgz", "dependencies": {"bl": {"version": "1.0.3", "from": "bl@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/bl/-/bl-1.0.3.tgz"}, "qs": {"version": "5.2.0", "from": "qs@>=5.2.0 <5.3.0", "resolved": "https://registry.npmjs.org/qs/-/qs-5.2.0.tgz"}, "readable-stream": {"version": "2.0.6", "from": "readable-stream@>=2.0.5 <2.1.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz"}, "request": {"version": "2.67.0", "from": "request@>=2.67.0 <2.68.0", "resolved": "https://registry.npmjs.org/request/-/request-2.67.0.tgz"}}}, "pify": {"version": "2.3.0", "from": "pify@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"}, "pinkie": {"version": "2.0.4", "from": "pinkie@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz"}, "pinkie-promise": {"version": "2.0.1", "from": "pinkie-promise@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz"}, "pkg-up": {"version": "1.0.0", "from": "pkg-up@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/pkg-up/-/pkg-up-1.0.0.tgz"}, "pkginfo": {"version": "0.4.0", "from": "pkginfo@>=0.0.0 <1.0.0", "resolved": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.4.0.tgz"}, "plur": {"version": "1.0.0", "from": "plur@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/plur/-/plur-1.0.0.tgz"}, "portscanner": {"version": "1.0.0", "from": "portscanner@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/portscanner/-/portscanner-1.0.0.tgz", "dependencies": {"async": {"version": "0.1.15", "from": "async@0.1.15", "resolved": "https://registry.npmjs.org/async/-/async-0.1.15.tgz"}}}, "postcss": {"version": "4.1.16", "from": "postcss@>=4.1.11 <5.0.0", "resolved": "https://registry.npmjs.org/postcss/-/postcss-4.1.16.tgz"}, "pretty-bytes": {"version": "3.0.1", "from": "pretty-bytes@>=3.0.1 <4.0.0", "resolved": "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-3.0.1.tgz"}, "pretty-ms": {"version": "2.1.0", "from": "pretty-ms@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/pretty-ms/-/pretty-ms-2.1.0.tgz"}, "process-nextick-args": {"version": "1.0.7", "from": "process-nextick-args@>=1.0.6 <1.1.0", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-1.0.7.tgz"}, "progress": {"version": "1.1.8", "from": "progress@>=1.1.8 <1.2.0", "resolved": "https://registry.npmjs.org/progress/-/progress-1.1.8.tgz"}, "promise": {"version": "7.1.1", "from": "promise@>=7.1.1 <8.0.0", "resolved": "https://registry.npmjs.org/promise/-/promise-7.1.1.tgz"}, "prompt": {"version": "0.2.14", "from": "prompt@>=0.2.14 <0.3.0", "resolved": "https://registry.npmjs.org/prompt/-/prompt-0.2.14.tgz"}, "prr": {"version": "0.0.0", "from": "prr@>=0.0.0 <0.1.0", "resolved": "https://registry.npmjs.org/prr/-/prr-0.0.0.tgz"}, "pug": {"version": "2.0.0-beta3", "from": "pug@>=2.0.0-alpha3 <3.0.0", "resolved": "https://registry.npmjs.org/pug/-/pug-2.0.0-beta3.tgz"}, "pug-attrs": {"version": "2.0.1", "from": "pug-attrs@>=2.0.1 <3.0.0", "resolved": "https://registry.npmjs.org/pug-attrs/-/pug-attrs-2.0.1.tgz"}, "pug-code-gen": {"version": "0.0.7", "from": "pug-code-gen@0.0.7", "resolved": "https://registry.npmjs.org/pug-code-gen/-/pug-code-gen-0.0.7.tgz"}, "pug-error": {"version": "1.3.1", "from": "pug-error@>=1.3.0 <2.0.0", "resolved": "https://registry.npmjs.org/pug-error/-/pug-error-1.3.1.tgz"}, "pug-filters": {"version": "1.2.2", "from": "pug-filters@>=1.2.1 <2.0.0", "resolved": "https://registry.npmjs.org/pug-filters/-/pug-filters-1.2.2.tgz"}, "pug-lexer": {"version": "2.0.2", "from": "pug-lexer@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/pug-lexer/-/pug-lexer-2.0.2.tgz"}, "pug-linker": {"version": "1.0.0", "from": "pug-linker@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/pug-linker/-/pug-linker-1.0.0.tgz"}, "pug-load": {"version": "2.0.0", "from": "pug-load@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/pug-load/-/pug-load-2.0.0.tgz"}, "pug-parser": {"version": "2.0.1", "from": "pug-parser@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/pug-parser/-/pug-parser-2.0.1.tgz"}, "pug-runtime": {"version": "2.0.1", "from": "pug-runtime@>=2.0.1 <3.0.0", "resolved": "https://registry.npmjs.org/pug-runtime/-/pug-runtime-2.0.1.tgz"}, "pug-strip-comments": {"version": "0.0.1", "from": "pug-strip-comments@0.0.1", "resolved": "https://registry.npmjs.org/pug-strip-comments/-/pug-strip-comments-0.0.1.tgz", "dependencies": {"pug-error": {"version": "0.0.0", "from": "pug-error@>=0.0.0 <0.0.1", "resolved": "https://registry.npmjs.org/pug-error/-/pug-error-0.0.0.tgz"}}}, "pug-walk": {"version": "0.0.3", "from": "pug-walk@>=0.0.3 <0.0.4", "resolved": "https://registry.npmjs.org/pug-walk/-/pug-walk-0.0.3.tgz"}, "q": {"version": "1.4.1", "from": "q@>=1.4.1 <1.5.0", "resolved": "https://registry.npmjs.org/q/-/q-1.4.1.tgz"}, "qs": {"version": "6.2.0", "from": "qs@>=6.2.0 <6.3.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.2.0.tgz"}, "range-parser": {"version": "1.2.0", "from": "range-parser@>=1.2.0 <1.3.0", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.0.tgz"}, "raw-body": {"version": "2.1.7", "from": "raw-body@>=2.1.5 <2.2.0", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.1.7.tgz", "dependencies": {"bytes": {"version": "2.4.0", "from": "bytes@2.4.0", "resolved": "https://registry.npmjs.org/bytes/-/bytes-2.4.0.tgz"}}}, "read": {"version": "1.0.7", "from": "read@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/read/-/read-1.0.7.tgz"}, "read-pkg": {"version": "1.1.0", "from": "read-pkg@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/read-pkg/-/read-pkg-1.1.0.tgz"}, "read-pkg-up": {"version": "1.0.1", "from": "read-pkg-up@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-1.0.1.tgz"}, "readable-stream": {"version": "2.1.4", "from": "readable-stream@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.1.4.tgz"}, "rechoir": {"version": "0.6.2", "from": "rechoir@>=0.6.2 <0.7.0", "resolved": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz"}, "redent": {"version": "1.0.0", "from": "redent@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/redent/-/redent-1.0.0.tgz"}, "regenerator-runtime": {"version": "0.9.5", "from": "regenerator-runtime@>=0.9.5 <0.10.0", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.9.5.tgz"}, "relateurl": {"version": "0.2.6", "from": "relateurl@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/relateurl/-/relateurl-0.2.6.tgz"}, "repeat-string": {"version": "1.5.4", "from": "repeat-string@>=1.5.2 <2.0.0", "resolved": "https://registry.npmjs.org/repeat-string/-/repeat-string-1.5.4.tgz"}, "repeating": {"version": "2.0.1", "from": "repeating@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/repeating/-/repeating-2.0.1.tgz"}, "request": {"version": "2.73.0", "from": "request@>=2.51.0 <3.0.0", "resolved": "https://registry.npmjs.org/request/-/request-2.73.0.tgz"}, "request-progress": {"version": "2.0.1", "from": "request-progress@>=2.0.1 <2.1.0", "resolved": "https://registry.npmjs.org/request-progress/-/request-progress-2.0.1.tgz"}, "requestretry": {"version": "1.9.0", "from": "requestretry@>=1.9.0 <1.10.0", "resolved": "https://registry.npmjs.org/requestretry/-/requestretry-1.9.0.tgz"}, "reserved-words": {"version": "0.1.1", "from": "reserved-words@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/reserved-words/-/reserved-words-0.1.1.tgz"}, "resolve": {"version": "1.1.7", "from": "resolve@>=1.1.0 <1.2.0", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.1.7.tgz"}, "resolve-from": {"version": "2.0.0", "from": "resolve-from@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-2.0.0.tgz"}, "resolve-pkg": {"version": "0.1.0", "from": "resolve-pkg@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/resolve-pkg/-/resolve-pkg-0.1.0.tgz"}, "revalidator": {"version": "0.1.8", "from": "revalidator@>=0.1.0 <0.2.0", "resolved": "https://registry.npmjs.org/revalidator/-/revalidator-0.1.8.tgz"}, "right-align": {"version": "0.1.3", "from": "right-align@>=0.1.1 <0.2.0", "resolved": "https://registry.npmjs.org/right-align/-/right-align-0.1.3.tgz"}, "rimraf": {"version": "2.2.8", "from": "rimraf@>=2.2.8 <2.3.0", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-2.2.8.tgz"}, "sauce-tunnel": {"version": "2.5.0", "from": "sauce-tunnel@>=2.5.0 <2.6.0", "resolved": "https://registry.npmjs.org/sauce-tunnel/-/sauce-tunnel-2.5.0.tgz"}, "saucelabs": {"version": "1.2.0", "from": "saucelabs@>=1.2.0 <1.3.0", "resolved": "https://registry.npmjs.org/saucelabs/-/saucelabs-1.2.0.tgz"}, "semver": {"version": "5.2.0", "from": "semver@>=2.0.0 <3.0.0||>=3.0.0 <4.0.0||>=4.0.0 <5.0.0||>=5.0.0 <6.0.0", "resolved": "https://registry.npmjs.org/semver/-/semver-5.2.0.tgz"}, "send": {"version": "0.14.1", "from": "send@0.14.1", "resolved": "https://registry.npmjs.org/send/-/send-0.14.1.tgz"}, "sentence-case": {"version": "2.1.0", "from": "sentence-case@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/sentence-case/-/sentence-case-2.1.0.tgz"}, "serve-index": {"version": "1.8.0", "from": "serve-index@>=1.7.1 <2.0.0", "resolved": "https://registry.npmjs.org/serve-index/-/serve-index-1.8.0.tgz"}, "serve-static": {"version": "1.11.1", "from": "serve-static@>=1.10.0 <2.0.0", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.11.1.tgz"}, "setprototypeof": {"version": "1.0.1", "from": "setprototypeof@1.0.1", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.0.1.tgz"}, "shelljs": {"version": "0.7.0", "from": "shelljs@>=0.7.0 <0.8.0", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.7.0.tgz"}, "shx": {"version": "0.1.2", "from": "shx@>=0.1.2 <0.2.0", "resolved": "https://registry.npmjs.org/shx/-/shx-0.1.2.tgz"}, "sigmund": {"version": "1.0.1", "from": "sigmund@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/sigmund/-/sigmund-1.0.1.tgz"}, "signal-exit": {"version": "3.0.0", "from": "signal-exit@>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.0.tgz"}, "snake-case": {"version": "2.1.0", "from": "snake-case@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/snake-case/-/snake-case-2.1.0.tgz"}, "sntp": {"version": "1.0.9", "from": "sntp@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/sntp/-/sntp-1.0.9.tgz"}, "source-map": {"version": "0.4.4", "from": "source-map@>=0.4.2 <0.5.0", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.4.4.tgz"}, "source-map-support": {"version": "0.4.1", "from": "source-map-support@>=0.4.0 <0.5.0", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.4.1.tgz", "dependencies": {"source-map": {"version": "0.1.32", "from": "source-map@0.1.32", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.1.32.tgz"}}}, "spdx-correct": {"version": "1.0.2", "from": "spdx-correct@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/spdx-correct/-/spdx-correct-1.0.2.tgz"}, "spdx-exceptions": {"version": "1.0.5", "from": "spdx-exceptions@>=1.0.4 <2.0.0", "resolved": "https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-1.0.5.tgz"}, "spdx-expression-parse": {"version": "1.0.2", "from": "spdx-expression-parse@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-1.0.2.tgz"}, "spdx-license-ids": {"version": "1.2.1", "from": "spdx-license-ids@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-1.2.1.tgz"}, "split": {"version": "1.0.0", "from": "split@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/split/-/split-1.0.0.tgz"}, "sprintf-js": {"version": "1.0.3", "from": "sprintf-js@>=1.0.2 <1.1.0", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"}, "sshpk": {"version": "1.8.3", "from": "sshpk@>=1.7.0 <2.0.0", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.8.3.tgz", "dependencies": {"assert-plus": {"version": "1.0.0", "from": "assert-plus@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"}}}, "stack-trace": {"version": "0.0.9", "from": "stack-trace@>=0.0.0 <0.1.0", "resolved": "https://registry.npmjs.org/stack-trace/-/stack-trace-0.0.9.tgz"}, "statuses": {"version": "1.3.0", "from": "statuses@>=1.3.0 <2.0.0", "resolved": "https://registry.npmjs.org/statuses/-/statuses-1.3.0.tgz"}, "stream-buffers": {"version": "2.2.0", "from": "stream-buffers@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/stream-buffers/-/stream-buffers-2.2.0.tgz"}, "string_decoder": {"version": "0.10.31", "from": "string_decoder@>=0.10.0 <0.11.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz"}, "stringstream": {"version": "0.0.5", "from": "stringstream@>=0.0.4 <0.1.0", "resolved": "https://registry.npmjs.org/stringstream/-/stringstream-0.0.5.tgz"}, "strip-ansi": {"version": "3.0.1", "from": "strip-ansi@>=3.0.0 <4.0.0", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-3.0.1.tgz"}, "strip-bom": {"version": "2.0.0", "from": "strip-bom@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-2.0.0.tgz"}, "strip-indent": {"version": "1.0.1", "from": "strip-indent@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/strip-indent/-/strip-indent-1.0.1.tgz"}, "strip-json-comments": {"version": "1.0.4", "from": "strip-json-comments@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.4.tgz"}, "supports-color": {"version": "2.0.0", "from": "supports-color@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-2.0.0.tgz"}, "swap-case": {"version": "1.1.2", "from": "swap-case@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/swap-case/-/swap-case-1.1.2.tgz"}, "tar-stream": {"version": "1.5.2", "from": "tar-stream@>=1.5.0 <2.0.0", "resolved": "https://registry.npmjs.org/tar-stream/-/tar-stream-1.5.2.tgz"}, "temporary": {"version": "0.0.8", "from": "temporary@>=0.0.4 <0.1.0", "resolved": "https://registry.npmjs.org/temporary/-/temporary-0.0.8.tgz"}, "text-table": {"version": "0.2.0", "from": "text-table@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"}, "throttleit": {"version": "1.0.0", "from": "throttleit@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/throttleit/-/throttleit-1.0.0.tgz"}, "through": {"version": "2.3.8", "from": "through@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz"}, "time-grunt": {"version": "1.3.0", "from": "time-grunt@>=1.3.0 <2.0.0", "resolved": "https://registry.npmjs.org/time-grunt/-/time-grunt-1.3.0.tgz"}, "tiny-lr": {"version": "0.2.1", "from": "tiny-lr@>=0.2.1 <0.3.0", "resolved": "https://registry.npmjs.org/tiny-lr/-/tiny-lr-0.2.1.tgz", "dependencies": {"qs": {"version": "5.1.0", "from": "qs@>=5.1.0 <5.2.0", "resolved": "https://registry.npmjs.org/qs/-/qs-5.1.0.tgz"}}}, "title-case": {"version": "2.1.0", "from": "title-case@>=2.1.0 <3.0.0", "resolved": "https://registry.npmjs.org/title-case/-/title-case-2.1.0.tgz"}, "tmp": {"version": "0.0.28", "from": "tmp@>=0.0.28 <0.0.29", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.28.tgz"}, "to-double-quotes": {"version": "2.0.0", "from": "to-double-quotes@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/to-double-quotes/-/to-double-quotes-2.0.0.tgz"}, "to-single-quotes": {"version": "2.0.1", "from": "to-single-quotes@>=2.0.0 <3.0.0", "resolved": "https://registry.npmjs.org/to-single-quotes/-/to-single-quotes-2.0.1.tgz"}, "token-stream": {"version": "0.0.1", "from": "token-stream@0.0.1", "resolved": "https://registry.npmjs.org/token-stream/-/token-stream-0.0.1.tgz"}, "tough-cookie": {"version": "2.2.2", "from": "tough-cookie@>=2.2.0 <2.3.0", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.2.2.tgz"}, "trim-newlines": {"version": "1.0.0", "from": "trim-newlines@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/trim-newlines/-/trim-newlines-1.0.0.tgz"}, "tunnel-agent": {"version": "0.4.3", "from": "tunnel-agent@>=0.4.1 <0.5.0", "resolved": "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.4.3.tgz"}, "tweetnacl": {"version": "0.13.3", "from": "tweetnacl@>=0.13.0 <0.14.0", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.13.3.tgz"}, "type-is": {"version": "1.6.13", "from": "type-is@>=1.6.10 <1.7.0", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.13.tgz"}, "typedarray": {"version": "0.0.6", "from": "typedarray@>=0.0.5 <0.1.0", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz"}, "uc.micro": {"version": "1.0.2", "from": "uc.micro@>=1.0.1 <2.0.0", "resolved": "https://registry.npmjs.org/uc.micro/-/uc.micro-1.0.2.tgz"}, "uglify-js": {"version": "2.6.4", "from": "uglify-js@>=2.6.0 <2.7.0", "resolved": "https://registry.npmjs.org/uglify-js/-/uglify-js-2.6.4.tgz", "dependencies": {"async": {"version": "0.2.10", "from": "async@>=0.2.6 <0.3.0", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz"}, "source-map": {"version": "0.5.6", "from": "source-map@>=0.5.1 <0.6.0", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.5.6.tgz"}}}, "uglify-to-browserify": {"version": "1.0.2", "from": "uglify-to-browserify@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/uglify-to-browserify/-/uglify-to-browserify-1.0.2.tgz"}, "underscore": {"version": "1.6.0", "from": "underscore@>=1.6.0 <1.7.0", "resolved": "https://registry.npmjs.org/underscore/-/underscore-1.6.0.tgz"}, "underscore.string": {"version": "3.2.3", "from": "underscore.string@>=3.2.3 <3.3.0", "resolved": "https://registry.npmjs.org/underscore.string/-/underscore.string-3.2.3.tgz"}, "unpipe": {"version": "1.0.0", "from": "unpipe@>=1.0.0 <1.1.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"}, "upper-case": {"version": "1.1.3", "from": "upper-case@>=1.1.1 <2.0.0", "resolved": "https://registry.npmjs.org/upper-case/-/upper-case-1.1.3.tgz"}, "upper-case-first": {"version": "1.1.2", "from": "upper-case-first@>=1.1.0 <2.0.0", "resolved": "https://registry.npmjs.org/upper-case-first/-/upper-case-first-1.1.2.tgz"}, "uri-path": {"version": "1.0.0", "from": "uri-path@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/uri-path/-/uri-path-1.0.0.tgz"}, "util-deprecate": {"version": "1.0.2", "from": "util-deprecate@>=1.0.1 <1.1.0", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"}, "utile": {"version": "0.2.1", "from": "utile@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/utile/-/utile-0.2.1.tgz", "dependencies": {"async": {"version": "0.2.10", "from": "async@>=0.2.9 <0.3.0", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz"}}}, "utils-merge": {"version": "1.0.0", "from": "utils-merge@1.0.0", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.0.tgz"}, "uuid": {"version": "2.0.2", "from": "uuid@>=2.0.2 <3.0.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-2.0.2.tgz"}, "validate-npm-package-license": {"version": "3.0.1", "from": "validate-npm-package-license@>=3.0.1 <4.0.0", "resolved": "https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.1.tgz"}, "verror": {"version": "1.3.6", "from": "verror@1.3.6", "resolved": "https://registry.npmjs.org/verror/-/verror-1.3.6.tgz"}, "void-elements": {"version": "2.0.1", "from": "void-elements@>=2.0.1 <3.0.0", "resolved": "https://registry.npmjs.org/void-elements/-/void-elements-2.0.1.tgz"}, "vow": {"version": "0.4.4", "from": "vow@0.4.4", "resolved": "https://registry.npmjs.org/vow/-/vow-0.4.4.tgz"}, "vow-fs": {"version": "0.3.2", "from": "vow-fs@0.3.2", "resolved": "https://registry.npmjs.org/vow-fs/-/vow-fs-0.3.2.tgz", "dependencies": {"glob": {"version": "3.2.8", "from": "glob@3.2.8", "resolved": "https://registry.npmjs.org/glob/-/glob-3.2.8.tgz"}, "minimatch": {"version": "0.2.14", "from": "minimatch@>=0.2.11 <0.3.0", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-0.2.14.tgz"}, "node-uuid": {"version": "1.4.0", "from": "node-uuid@1.4.0", "resolved": "https://registry.npmjs.org/node-uuid/-/node-uuid-1.4.0.tgz"}}}, "vow-queue": {"version": "0.3.1", "from": "vow-queue@0.3.1", "resolved": "https://registry.npmjs.org/vow-queue/-/vow-queue-0.3.1.tgz"}, "websocket-driver": {"version": "0.6.5", "from": "websocket-driver@>=0.5.1", "resolved": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.6.5.tgz"}, "websocket-extensions": {"version": "0.1.1", "from": "websocket-extensions@>=0.1.1", "resolved": "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.1.tgz"}, "when": {"version": "3.7.7", "from": "when@>=3.7.5 <3.8.0", "resolved": "https://registry.npmjs.org/when/-/when-3.7.7.tgz"}, "which": {"version": "1.2.10", "from": "which@>=1.2.1 <1.3.0", "resolved": "https://registry.npmjs.org/which/-/which-1.2.10.tgz"}, "window-size": {"version": "0.1.0", "from": "window-size@0.1.0", "resolved": "https://registry.npmjs.org/window-size/-/window-size-0.1.0.tgz"}, "winston": {"version": "0.8.3", "from": "winston@>=0.8.0 <0.9.0", "resolved": "https://registry.npmjs.org/winston/-/winston-0.8.3.tgz", "dependencies": {"async": {"version": "0.2.10", "from": "async@>=0.2.0 <0.3.0", "resolved": "https://registry.npmjs.org/async/-/async-0.2.10.tgz"}, "colors": {"version": "0.6.2", "from": "colors@>=0.6.0 <0.7.0", "resolved": "https://registry.npmjs.org/colors/-/colors-0.6.2.tgz"}, "pkginfo": {"version": "0.3.1", "from": "pkginfo@>=0.3.0 <0.4.0", "resolved": "https://registry.npmjs.org/pkginfo/-/pkginfo-0.3.1.tgz"}}}, "with": {"version": "5.1.1", "from": "with@>=5.0.0 <6.0.0", "resolved": "https://registry.npmjs.org/with/-/with-5.1.1.tgz"}, "wordwrap": {"version": "0.0.2", "from": "wordwrap@0.0.2", "resolved": "https://registry.npmjs.org/wordwrap/-/wordwrap-0.0.2.tgz"}, "wrappy": {"version": "1.0.2", "from": "wrappy@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"}, "xml-char-classes": {"version": "1.0.0", "from": "xml-char-classes@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/xml-char-classes/-/xml-char-classes-1.0.0.tgz"}, "xmlbuilder": {"version": "3.1.0", "from": "xmlbuilder@>=3.1.0 <4.0.0", "resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-3.1.0.tgz"}, "xtend": {"version": "4.0.1", "from": "xtend@>=4.0.0 <5.0.0", "resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz"}, "yargs": {"version": "3.10.0", "from": "yargs@>=3.10.0 <3.11.0", "resolved": "https://registry.npmjs.org/yargs/-/yargs-3.10.0.tgz", "dependencies": {"camelcase": {"version": "1.2.1", "from": "camelcase@>=1.0.2 <2.0.0", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-1.2.1.tgz"}}}, "yauzl": {"version": "2.4.1", "from": "yauzl@2.4.1", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-2.4.1.tgz"}, "zip-stream": {"version": "1.0.0", "from": "zip-stream@>=1.0.0 <2.0.0", "resolved": "https://registry.npmjs.org/zip-stream/-/zip-stream-1.0.0.tgz", "dependencies": {"lodash": {"version": "4.13.1", "from": "lodash@>=4.8.0 <5.0.0", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.13.1.tgz"}}}}}