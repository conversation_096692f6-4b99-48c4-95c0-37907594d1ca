/*=============================================================================*\
    NOTIFICATIONS
\*=============================================================================*/

/*  MAIN
-------------------------------------------------------------------------------*/

._notification {
    display: block;
    border-width: 1px;
    border-style: dotted;
    padding: 20px 70px 20px 20px;
    background-color: @color-notification-background; /* theme */
    border-color: @color-notification-border; /* theme */
    color: @color-notification-text; /* theme */
    & when (@rtl) {
        padding: 20px 20px 20px 70px;
    }
    position: relative;
    &:after {
        font-size: @font-size-notification-icon; /* theme */
        position: absolute;
        top: 0;
        right: 20px;
        font-family: FontAwesome;
        content: '\f05a';
        .centerer(false, true);
    }
    &._notification-note {
        background-color: @color-notification-note-background; /* theme */
        border-color: @color-notification-note-border; /* theme */
        color: @color-notification-note-text; /* theme */
        &:after {
            content: '\f06a';
        }
    }
    &._notification-error {
        background-color: @color-notification-error-background; /* theme */
        border-color: @color-notification-error-border; /* theme */
        color: @color-notification-error-text; /* theme */
        &:after {
            content: '\f057';
        }
    }
    &._notification-success {
        background-color: @color-notification-success-background; /* theme */
        border-color: @color-notification-success-border; /* theme */
        color: @color-notification-success-text; /* theme */
        &:after {
            content: '\f058';
        }
    }
    *:not(br) {
        margin-top: 10px;
        &:first-child {
            margin-top: 0;
        }
    }
}

/*  TOASTR - https://github.com/CodeSeven/toastr
-------------------------------------------------------------------------------*/

#toast-container {
    position: fixed;
    z-index: 999999;
    &.toast-top-left {
        top: 10px;
        left: 10px;
    }
    &.toast-top-right {
        top: 10px;
        right: 10px;
    }
    &.toast-bottom-right {
        right: 10px;
        bottom: 10px;
    }
    &.toast-bottom-left {
        bottom: 10px;
        left: 10px;
    }
    > div {
        ._notification();
        border-width: 1px;
        border-style: dotted;
        background-color: @color-toastr-background; /* theme */
        border-color: @color-toastr-border; /* theme */
        color: @color-toastr-text; /* theme */
        &.toast-info {
            background-color: @color-toastr-note-background; /* theme */
            border-color: @color-toastr-note-border; /* theme */
            color: @color-toastr-note-text; /* theme */
            &:after {
                content: '\f06a';
            }
        }
        &.toast-error {
            background-color: @color-toastr-error-background; /* theme */
            border-color: @color-toastr-error-border; /* theme */
            color: @color-toastr-error-text; /* theme */
            &:after {
                content: '\f057';
            }
        }
        &.toast-success {
            background-color: @color-toastr-success-background; /* theme */
            border-color: @color-toastr-success-border; /* theme */
            color: @color-toastr-success-text; /* theme */
            &:after {
                content: '\f058';
            }
        }
        &.toast-warning {
        }
        .toast-title {
            font-weight: bold;
        }
        .toast-message {
            -ms-word-wrap: break-word;
            word-wrap: break-word;
        }
    }
}

/*  BUBBLE
-------------------------------------------------------------------------------*/

._bubble {
    display: inline-block;
    background-color: @color-main-highlight; /* theme */
    color: @color-main-secondary-text; /* theme */
    padding: 0;
    border-radius: 100%;
    font-size: @font-size-xsmall; /* theme */
    text-align: center;
    font-weight: 400;
    width: 16px;
    height: 16px;
    line-height: 16px;
}
