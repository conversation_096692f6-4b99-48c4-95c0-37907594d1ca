module.exports = {

    theme: {
        name: 'happydreams', // This name is used as a public directory name for assets. Use lowercase letters.
        variables: { // Copy/Paste all less variables with "_" prefix for replacing by php
            "color-main-titles": "_color-main-titles_",
            "color-main-titles-secondary": "_color-main-titles-secondary_",
            "color-main-titles-background": "_color-main-titles-background_",
            "color-main-text": "_color-main-text_",
            "color-main-highlight": "_color-main-highlight_",
            "color-main-text-over-highlight": "_color-main-text-over-highlight_",
            "color-main-secondary-text": "_color-main-secondary-text_",
            "color-main-meta-text": "_color-main-meta-text_",
            "color-main-background": "_color-main-background_",
            "color-main-borders": "_color-main-borders_",
            "color-main-fields-text": "_color-main-fields-text_",
            "color-main-fields-background": "_color-main-fields-background_",
            "color-main-fields-border": "_color-main-fields-border_",
            "color-main-secondary-background": "_color-main-secondary-background_",
            "color-main-secondary-background-borders": "_color-main-secondary-background-borders_",
            "color-main-secondary-background-text": "_color-main-secondary-background-text_",
            "color-main-secondary-background-titles": "_color-main-secondary-background-titles_",
            "color-main-secondary-background-highlight": "_color-main-secondary-background-highlight_",

            "color-promo-bar-text": "_color-promo-bar-text_",
            "color-promo-bar-background": "_color-promo-bar-background_",
            "color-promo-bar-button-text": "_color-promo-bar-button-text_",
            "color-promo-bar-button-text-hover": "_color-promo-bar-button-text-hover_",
            "color-promo-bar-button-background": "_color-promo-bar-button-background_",
            "color-promo-bar-button-background-hover": "_color-promo-bar-button-background-hover_",
            "color-promo-bar-button-border": "_color-promo-bar-button-border_",
            "color-promo-bar-button-border-hover": "_color-promo-bar-button-border-hover_",
            "color-promo-bar-close": "_color-promo-bar-close_",

            "color-header-background": "_color-header-background_",
            "color-header-text": "_color-header-text_",
            "color-header-text-secondary": "_color-header-text-secondary_",
            "color-header-navigation-text": "_color-header-navigation-text_",
            "color-header-navigation-highlight": "_color-header-navigation-highlight_",
            "color-header-navigation-submenu-background": "_color-header-navigation-submenu-background_",
            "color-header-navigation-submenu-text": "_color-header-navigation-submenu-text_",

            "color-footer-text": "_color-footer-text_",
            "color-footer-titles": "_color-footer-titles_",
            "color-footer-highlight": "_color-footer-highlight_",
            "color-footer-socials": "_color-footer-socials_",
            "color-footer-socials-highlight": "_color-footer-socials-highlight_",
            "color-footer-legal": "_color-footer-legal_",

            "color-slider-background": "_color-slider-background_",
            "color-slider-text": "_color-slider-text_",
            "color-slider-main-title": "_color-slider-main-title_",
            "color-slider-button-borders": "_color-slider-button-borders_",
            "color-slider-button-text": "_color-slider-button-text_",
            "color-slider-arrows": "_color-slider-arrows_",
            "color-slider-dots": "_color-slider-dots_",

            "color-slider-search-background": "_color-slider-search-background_",
            "color-slider-search-borders": "_color-slider-search-borders_",
            "color-slider-search-text": "_color-slider-search-text_",

            "color-products-background": "_color-products-background_",
            "color-products-borders": "_color-products-borders_",
            "color-products-title": "_color-products-title_",
            "color-products-text": "_color-products-text_",
            "color-products-labels": "_color-products-labels_",
            "color-products-highlight": "_color-products-highlight_",
            "color-products-background-hover": "_color-products-background-hover_",
            "color-products-new-price": "_color-products-new-price_",
            "color-products-old-price": "_color-products-old-price_",
            "color-products-filters": "_color-products-filters_",

            "color-product-details-image-background": "_color-product-details-image-background_",
            "color-product-details-image-borders": "_color-product-details-image-borders_",
            "color-product-details-background": "_color-product-details-background_",
            "color-product-details-text": "_color-product-details-text_",
            "color-product-details-highlight": "_color-product-details-highlight_",

            "color-button-text": "_color-button-text_",
            "color-button-text-hover": "_color-button-text-hover_",
            "color-button-background": "_color-button-background_",
            "color-button-background-hover": "_color-button-background-hover_",
            "color-button-border": "_color-button-border_",
            "color-button-border-hover": "_color-button-border-hover_",
            "color-button-secondary-text": "_color-button-secondary-text_",
            "color-button-secondary-text-hover": "_color-button-secondary-text-hover_",
            "color-button-secondary-background": "_color-button-secondary-background_",
            "color-button-secondary-background-hover": "_color-button-secondary-background-hover_",
            "color-button-secondary-border": "_color-button-secondary-border_",
            "color-button-secondary-border-hover": "_color-button-secondary-border-hover_",
            "color-button-disable-text": "_color-button-disable-text_",
            "color-button-disable-background": "_color-button-disable-background_",
            "color-button-disable-border": "_color-button-disable-border_",
            "color-button-active-text": "_color-button-active-text_",
            "color-button-active-background": "_color-button-active-background_",
            "color-button-active-border": "_color-button-active-border_",
            
            "color-label-new-text": "_color-label-new-text_",
            "color-label-new-background": "_color-label-new-background_",
            "color-label-sale-text": "_color-label-sale-text_",
            "color-label-sale-background": "_color-label-sale-background_",
            "color-label-delivery-text": "_color-label-delivery-text_",
            "color-label-delivery-background": "_color-label-delivery-background_",
            "color-label-discount-text": "_color-label-discount-text_",
            "color-label-discount-background": "_color-label-discount-background_",
            "color-label-leasing-text": "_color-label-leasing-text_",
            "color-label-leasing-background": "_color-label-leasing-background_",
            "color-label-featured-text": "_color-label-featured-text_",
            "color-label-featured-background": "_color-label-featured-background_",

            "color-popups-background": "_color-popups-background_",
            "color-popups-borders": "_color-popups-borders_",
            "color-popups-text": "_color-popups-text_",
            "color-popups-titles": "_color-popups-titles_",
            "color-popups-highlight": "_color-popups-highlight_",
            "color-popups-button-background": "_color-popups-button-background_",
            "color-popups-button-text": "_color-popups-button-text_",
            "color-popups-button-secondary-background": "_color-popups-button-secondary-background_",
            "color-popups-button-secondary-text": "_color-popups-button-secondary-text_"
        }
    },
    server: {
        http: 'http://tolevcheto.ccdev.pro/' // Write http address of the theme
    },
    fonts: {
        google: [ // Array of all google fonts
            {
                family: 'Roboto Condensed',
                subsets: ['latin', 'cyrillic', 'greek'],
                styles: [300, 400, 700]
            },
            {
                family: 'Roboto',
                subsets: ['latin', 'cyrillic', 'greek'],
                styles: [300, 400, 500, 700, 900]
            },
            {
                family: 'Lobster',
                subsets: ['latin', 'cyrillic', 'greek'],
                styles: [400]
            }
        ],
        embed: { // Array of all embed fonts. If you don't have embed fonts, leave this object empty
        }
    },
    vendors: { // Array of all vendors. Example: "bower-pkg-name": "version"
        devDep: {
            "jquery-breakpoint-check": "*",
            "toastr": "2.0.3",
            "twbs-pagination": "*",
            "theia-sticky-sidebar": "*",
            "jquery.scrollbar": "*"
        },
        styles: [ // All styles of the selected vendors
            '<%= _PATH.src_vendors %>/custom_pkg/jquery-ui.custom/jquery-ui.css',
            '<%= _PATH.src_vendors %>/custom_pkg/select2.custom/select2.css',
            '<%= _PATH.src_vendors %>/custom_pkg/jquery.uniform.custom/uniform.default.css',
            '<%= _PATH.src_vendors %>/custom_pkg/jquery.uniform.custom/uniformjs.css',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/less/bootstrap.less',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/less/font-awesome.less',
            '<%= _PATH.src_vendors %>/jquery.scrollbar/jquery.scrollbar.css'
        ],
        scripts: [ // All scripts of the selected vendors
            '<%= _PATH.src_vendors %>/jquery-breakpoint-check/js/jquery-breakpoint-check.js',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/dist/js/bootstrap.js',
            '<%= _PATH.src_vendors %>/custom_pkg/select2.custom/select2.js',
            '<%= _PATH.src_vendors %>/custom_pkg/resize-sensor/ResizeSensor.js',
            '<%= _PATH.src_vendors %>/bootstrap-touchspin/src/jquery.bootstrap-touchspin.js',
            '<%= _PATH.src_vendors %>/toastr/toastr.js',
            '<%= _PATH.src_vendors %>/twbs-pagination/jquery.twbsPagination.js',
            '<%= _PATH.src_vendors %>/jquery.scrollbar/jquery.scrollbar.js',
            '<%= _PATH.src_vendors %>/theia-sticky-sidebar/js/theia-sticky-sidebar.js'
        ],
        fonts: [
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.eot',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.svg',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.ttf',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.woff',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.woff2',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.eot',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.svg',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.ttf',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.woff',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.woff2'
        ]
    }
};
