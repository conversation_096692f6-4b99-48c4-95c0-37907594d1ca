{"name": "happydreams", "version": "0.1", "page_builder": true, "assets": {"default_images": {"logo": "", "favicon": "", "product": {"50": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "100": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "150": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "300": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "600": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "800": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "1024": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "1280": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "1920": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png"}, "vendor": {"50": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "100": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "150": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "300": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "600": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "800": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "1024": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "1280": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "1920": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png"}, "category": {"50": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "100": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "150": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "300": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "600": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "800": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "1024": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "1280": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png", "1920": "{$img_url}/themes/happydreams/img/defaults/noimage/no-image.png"}}, "default_content_images": {"product": ["{$img_url}/themes/happydreams/img/defaults/products/product1.jpg", "{$img_url}/themes/happydreams/img/defaults/products/product2.jpg", "{$img_url}/themes/happydreams/img/defaults/products/product3.jpg", "{$img_url}/themes/happydreams/img/defaults/products/product4.jpg", "{$img_url}/themes/happydreams/img/defaults/products/product5.jpg", "{$img_url}/themes/happydreams/img/defaults/products/product6.jpg"], "category": ["{$img_url}/themes/happydreams/img/defaults/categories/category1.jpg", "{$img_url}/themes/happydreams/img/defaults/categories/category2.jpg", "{$img_url}/themes/happydreams/img/defaults/categories/category3.jpg", "{$img_url}/themes/happydreams/img/defaults/categories/category4.jpg", "{$img_url}/themes/happydreams/img/defaults/categories/category5.jpg", "{$img_url}/themes/happydreams/img/defaults/categories/category6.jpg"], "vendor": ["{$img_url}/themes/happydreams/img/defaults/brands/brand1.jpg", "{$img_url}/themes/happydreams/img/defaults/brands/brand2.jpg"]}}, "eloquent": {"products": {"list": {"with": ["image", "category", "vendor", "favorite", "banners", "labels"], "scope": ["quantity"]}, "box": {"with": ["image", "category", "vendor", "favorite", "banners", "labels"]}}}, "functions": {"discount": {"color": {"status": true}}, "navigations": {"icon": {"status": true}}}, "widgets": {"navigationMain": {"map": "navigation.main"}, "navigationFooter": {"map": "navigation.footer"}, "navigationLinksPage": {"map": "navigation.links", "group": "header", "name": {"en": "Navigation Links - sidebar", "bg": "Навигационни връзки - страничната колона"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"links": [{"link_type": "section", "link_value": "blog", "link_caption": "Blog", "target": ""}, {"link_type": "section", "link_value": "contacts", "link_caption": "Contacts", "target": ""}, {"link_type": "section", "link_value": "vendors", "link_caption": "Brands", "target": ""}]}}}, "logoFixed": {"map": "extra.backgroundImage", "group": "footer", "name": {"en": "Fixed header logo", "bg": "Лого във фиксираната лента"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"type": "external", "src": "{$img_url}/themes/happydreams/img/png/logo2.png"}}}, "headerBackground": {"map": "extra.backgroundImage", "group": "footer", "name": {"en": "Header - Background", "bg": "Хедър - Фон"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/backgrounds/header.jpg"}}}, "headerSlogan": {"map": "extra.text", "group": "text_fields", "name": {"en": "Header text", "bg": "Заг<PERSON>авна част - текст"}, "settings": {"defaults": {"title": "Not visible", "text": "lorem ipsum dolor sit amet expetenda vituperata"}}}, "htmlLine": {"map": "extra.htmlLine", "group": "top_bar", "name": {"en": "Promo Bar", "bg": "Промо лента"}, "description": {"en": "Promo Bar", "bg": "Промо лента"}, "settings": {"defaults": {"text": "", "enabled": false, "period": {"from": "", "to": ""}, "button": {"float": "right", "enabled": true, "link": "", "target": "_blank", "text": "View More"}}}}, "carousel": {"map": "extra.carousel", "group": "slider", "name": {"en": "Slide<PERSON>", "bg": "Слай<PERSON>ър"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 2, "autoplay": "no", "interval": 5000, "caption": "no", "controls": "yes", "indicators": "yes", "pause": "no", "cycle": "no", "slides": {"1": {"img_type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/carousel/slider.jpg", "link_type": "section", "link_value": "products", "link_caption": "Виж още", "html": "<h3>МАТРАЦИ</h3><h1>HAPPY DREAMS</h1><p>ХАЙДЕ ДА СИ ЛЯГАМЕ</p>", "horizontal_position": "center", "vertical_position": "middle", "target": "_self"}, "2": {"img_type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/carousel/slider.jpg", "link_type": "section", "link_value": "products", "link_caption": "Виж още", "html": "<h3>МАТРАЦИ</h3><h1>HAPPY DREAMS</h1><p>ХАЙДЕ ДА СИ ЛЯГАМЕ</p>", "horizontal_position": "center", "vertical_position": "middle", "target": "_self"}}}}}, "categoryProperties": {"map": "product.categoryProperties", "name": {"en": "Category Properties", "bg": ""}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true}}}, "filters": {"map": "product.filters", "mode": "range_slider", "settings": {"defaults": {"per_page": "6", "per_page_options": [6, 12, 18, 36, 72], "products_price_ranges": [[100, 100000]], "price_range_step": "500"}}, "name": {"en": "Product listing settings", "bg": "Настройки на продуктов каталог"}, "description": {"en": "", "bg": ""}}, "productsRelated": {"map": "product.related", "name": {"en": "Related Products", "bg": "Свързани продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Related products", "products": 3, "per_row": 3, "type": "category"}}}, "productsDetails": {"map": "product.productsDetails", "name": {"en": "Detail information for product", "bg": "Детайлна информация за продукт"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {}}}, "showcaseProducts1": {"map": "product.productShowcase", "name": {"en": "Products Showcase 1", "bg": "Витрина с продукти 1"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "НОВИ ПРОДУКТИ", "products": 3, "per_row": 3, "new": "yes"}}}, "homeText1": {"map": "extra.text", "group": "text_fields", "name": {"en": "Homepage text 1", "bg": "Текст на началната страница"}, "settings": {"defaults": {"title": "", "text": "<h2>WHICH BED SHOULD I TAKE?</h2><p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore.</p><a class=\"_button _button-secondary _button-small\" href=\"#\"><span class=\"_figure-stack\"><span class=\"_figure-stack-label\">Виж още</span></span></a>"}}}, "homeText1Background": {"map": "extra.backgroundImage", "group": "footer", "name": {"en": "Homepage text - Background", "bg": "Текст на началната страница - Фон"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/backgrounds/home-text.jpg"}}}, "textbox1": {"map": "extra.text", "name": {"en": "Text Box 1", "bg": "Текстова кутийка 1"}, "settings": {"defaults": {"title": "", "text": "<p class=\"text-aligncenter\"><img src=\"{$img_url}/themes/happydreams/img/defaults/other/text-box1.png\" alt=\"\"></p><h5 class=\"text-aligncenter\"><strong>БЪРЗА И БЕЗПЛАТНА</strong> <br> ДОСТАВКА</h5><p class=\"text-aligncenter\">Всички поръчки в рамките на гр. София, <br> доставяме безплатно!</p>"}}}, "textbox2": {"map": "extra.text", "name": {"en": "Text Box 2", "bg": "Текстова кутийка 2"}, "settings": {"defaults": {"title": "", "text": "<p class=\"text-aligncenter\"><img src=\"{$img_url}/themes/happydreams/img/defaults/other/text-box2.png\" alt=\"\"></p><h5 class=\"text-aligncenter\"><strong>100% СИГУРНО</strong> <br> ПАЗАРУВАНЕ</h5><p class=\"text-aligncenter\">Условията за пазаруване в сайта <br> са 100% сигурни за клиентите ни.</p>"}}}, "textbox3": {"map": "extra.text", "name": {"en": "Text Box 3", "bg": "Текстова кутийка 3"}, "settings": {"defaults": {"title": "", "text": "<p class=\"text-aligncenter\"><img src=\"{$img_url}/themes/happydreams/img/defaults/other/text-box3.png\" alt=\"\"></p><h5 class=\"text-aligncenter\"><strong>100% ДОВОЛНИ</strong> <br> КЛИЕНТИ</h5><p class=\"text-aligncenter\">За допълнителни въпроси може да се <br> свържете с нас на +359  882 46 37 37</p>"}}}, "textbox4": {"map": "extra.text", "name": {"en": "Text Box 4", "bg": "Текстова кутийка 4"}, "settings": {"defaults": {"title": "", "text": "<p class=\"text-aligncenter\"><img src=\"{$img_url}/themes/happydreams/img/defaults/other/text-box4.png\" alt=\"\"></p><h5 class=\"text-aligncenter\"><strong>ОСИГУРЕН БЕЗПЛАТЕН ТРАНСПОРТ</strong></h5><p class=\"text-aligncenter\">За допълнителни въпроси може да се <br> свържете с нас на +359  882 46 37 37</p>"}}}, "textbox5": {"map": "extra.text", "name": {"en": "Text Box 5", "bg": "Текстова кутийка 5"}, "settings": {"defaults": {"title": "", "text": "<p class=\"text-aligncenter\"><img src=\"{$img_url}/themes/happydreams/img/defaults/other/text-box5.png\" alt=\"\"></p><h5 class=\"text-aligncenter\"><strong>ПРОФЕСИОНАЛНА КОНСУЛТАЦИЯ</strong></h5><p class=\"text-aligncenter\">За допълнителни въпроси може да се <br> свържете с нас на +359  882 46 37 37</p>"}}}, "productText": {"map": "extra.text", "name": {"en": "Product details text", "bg": "Текст в детайлите на продуктите"}, "settings": {"defaults": {"title": "", "text": "<table style=\"width: 100%;\"><tbody><tr><td style=\"width: 50%;\"><p class=\"text-aligncenter\"><img src=\"{$img_url}/themes/happydreams/img/defaults/other/text-box3.png\" alt=\"\"></p><h5 class=\"text-aligncenter\"><strong>100% ДОВОЛНИ</strong> <br> КЛИЕНТИ</h5></td><td style=\"width: 50%;\"><p class=\"text-aligncenter\"><img src=\"{$img_url}/themes/happydreams/img/defaults/other/text-box2.png\" alt=\"\"></p><h5 class=\"text-aligncenter\"><strong>100% СИГУРНО</strong> <br> ПАЗАРУВАНЕ</h5></td></tr></tbody></table>"}}}, "showcase1": {"map": "product.showcase", "name": {"en": "Category Showcase 1", "bg": "Витрина с категории 1"}, "settings": {"defaults": {"header": "", "type": "category", "amount": 6, "per_row": 3, "show_name": true, "show_description": false}}}, "showcase2": {"map": "product.showcase", "name": {"en": "Category Showcase 2", "bg": "Витрина с категории 2"}, "settings": {"defaults": {"header": "", "type": "vendor", "amount": 2, "per_row": 2, "show_name": true, "show_description": true}}}, "summaryAdditionalInfo": {"map": "extra.text", "group": "text_fields", "name": {"en": "Summary box text", "bg": "Информация в количката за цените"}, "settings": {"defaults": {"title": "Prices information", "text": "Prices and delivery costs are not confirmed until your reached the checkout. 30 days withdrawal. Read about our return and refund policy here return and refund."}}}, "checkoutSideText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Cart side text", "bg": "Текст в страничната колона в количката"}, "settings": {"defaults": {"title": "RETURN AND REFUND", "text": "You will need to arrange the return shipment to us yourself and bear the cost for the courier. Find out more about returns and refunds here. You will not get a fa piao unless you request it. Find out more about fa piao here."}}}, "wishlistMenu": {"map": "wishlist.menu", "name": {"en": "Menu for latest favorites products", "bg": "Меню за последни любими продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "limit": 10}}}, "wishlist": {"map": "wishlist.listing"}, "productCompare": {"map": "product.compare"}, "page": {"map": "extra.page"}, "vendors": {"map": "product.vendors"}, "search": {"map": "extra.search", "group": "header", "name": {"en": "Global Search", "bg": "Глобално търсене"}, "description": {"en": "A search form for your site", "bg": "Форма за търсене"}}, "bannersGroup": {"map": "extra.bannerGlobal"}, "banners1": {"map": "extra.banner", "group": "banners", "name": {"en": "Banners 1", "bg": "Банери 1"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": false, "amount": 2, "per_row": 2, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/banners/banner-06.jpg", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/banners/banner-07.jpg", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "banners2": {"map": "extra.banner", "group": "banners", "name": {"en": "Banners 2", "bg": "Банери 2"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 5, "per_row": 5, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/banners/banner-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/banners/banner-02.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "3": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/banners/banner-03.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "4": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/banners/banner-04.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "5": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/banners/banner-05.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "logo": {"map": "extra.logo", "editable": "no"}, "userControls": {"map": "user.controls"}, "googleMap": {"map": "contact.googleMap", "name": {"en": "Google Map", "bg": "<PERSON>у<PERSON><PERSON>л карта"}, "description": {"en": "", "bg": ""}}, "contactForm": {"map": "contact.form"}, "contactInformation": {"map": "contact.information", "name": {"en": "Contact Information", "bg": "Контактна информация"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"show_form": "yes", "show_custom_information": "yes", "custom_information": "<p>The Moana Residence is situated on the best lot at Kohanaiki and is a 5 bedroom, 5 1/2 bath home of approximately 8,000 interior sq. ft. It features Ipe hardwood flooring on the interior and granite stone flooring on the lanais, granite countertops, vaulted cedar ceilings, clerestory windows for lots of light.</p>", "page_text": "<h3>email</h3><p><a href=\"mailto:<EMAIL>\"><EMAIL></a></p><h3>phone</h3><p>TOLL-FREE at 855-HNM-SHOP&nbsp;<br />(************)</p><h3>Opening hours</h3><p>Monday - Sunday: 8am - 01am&nbsp;</p><h3>office seattle</h3><p>100 MAIN ST<br />TUCSON AZ 85705-7598<br />SEATTLE WA 98104<br />USA</p><h3>main office</h3><p>100 MAIN ST<br />TUCSON AZ 85705-7598<br />SEATTLE WA 98104<br />USA</p>"}}}, "authorize": {"map": "user.authorize"}, "social": {"map": "extra.social", "group": "footer", "name": {"en": "Social Links", "bg": ""}, "description": {"en": "", "bg": ""}}, "checkoutSignInLoginText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Checkout sing in - Login text", "bg": "Текст в количката при Login"}, "settings": {"defaults": {"title": "Not visible", "text": "Welcome back! Please sign in using your email address and password below."}}}, "checkoutSignInRegisterText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Checkout sing in - Register text", "bg": "Текст в количката при Register"}, "settings": {"defaults": {"title": "Not visible", "text": "Create an account to check out faster in the future and receive emails about your order new products, events and special offers!"}}}, "checkoutSignInGuestText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Checkout sing in - Guest text", "bg": "Текст в количката при Guest"}, "settings": {"defaults": {"title": "Not visible", "text": "You do not need to create an account to check out. You can check out as a Guest and create an account later."}}}, "providers": {"map": "payment.providers"}, "article": {"map": "blog.article"}, "blog": {"map": "blog.blog", "name": {"en": "Blog", "bg": ""}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"per_page": 6}}}, "recentArticles": {"map": "blog.recentArticles", "name": {"en": "Recent Articles", "bg": "Последни статии"}, "description": {"en": "", "bg": ""}}, "recentComments": {"map": "blog.recentComments", "name": {"en": "Recent Comments", "bg": "Последни коментари"}, "description": {"en": "", "bg": ""}}, "utilities": {"map": "base.utilities"}, "share": {"map": "extra.addThisShare", "group": "products", "name": {"en": "Social Share", "bg": "Споделяне"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"layout": "small"}}}, "shareBlog": {"map": "extra.addThisShare", "name": {"en": "Blog Social Share", "bg": "Споделяне - Блог"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"show_counter": "no"}}}, "sharrre": {"map": "extra.sharrreShare"}, "footerBackground": {"map": "extra.backgroundImage", "group": "footer", "name": {"en": "Footer - Background", "bg": "Футър - Фон"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"type": "external", "src": "{$img_url}/themes/happydreams/img/defaults/backgrounds/footer.jpg"}}}, "footerText": {"map": "extra.text", "group": "footer", "name": {"en": "Footer Text", "bg": "Футър текст"}, "settings": {"defaults": {"title": "First post", "text": "<p>This is your store’s blog. You can use it to talk about new product launches, experiences, tips or other news you want your customers.</p>"}}}, "newsletter": {"map": "mailchimp.newsletter", "name": {"en": "Newsletter (Mailchimp)", "bg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н"}, "description": {"en": "Newsletter form for your site", "bg": "Форма за бюлетин"}}, "leasing": {"map": "store.leasing"}}, "globals": {"widgets": {"product": {"listing": {}, "details": {}, "showcase": {"settings": {"defaults": {"products": 4}}}}}}, "settings": {"groups": {"main": {"en": {"name": "Main"}, "bg": {"name": "Основни"}}, "promo_bar": {"en": {"name": "Promo Bar"}, "bg": {"name": "Промо лента"}}, "header": {"en": {"name": "Header"}, "bg": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "footer": {"en": {"name": "Footer"}, "bg": {"name": "<PERSON>ут<PERSON>р"}}, "slider": {"en": {"name": "Slide<PERSON>"}, "bg": {"name": "Слай<PERSON>ър"}}, "products": {"en": {"name": "Products"}, "bg": {"name": "Продукти"}}, "product-details": {"en": {"name": "Product details"}, "bg": {"name": "Детайли на продукт"}}, "buttons": {"en": {"name": "Buttons"}, "bg": {"name": "Бутони"}}, "labels": {"en": {"name": "Labels"}, "bg": {"name": "Етикети"}}, "popups": {"en": {"name": "Popups"}, "bg": {"name": "Popup-и"}}}, "variables": {"color-main-titles": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-main-titles-secondary": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Framed titles - text"}, "bg": {"name": "Рамкирани заглавия - текст"}}}, "color-main-titles-background": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "Framed titles - background"}, "bg": {"name": "Рамкирани заглавия - фон"}}}, "color-main-text": {"type": "color", "group": "main", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-main-highlight": {"type": "color", "group": "main", "default": "#b2a08a", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-main-text-over-highlight": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Text over highlight"}, "bg": {"name": "Текст върху акцент"}}}, "color-main-secondary-text": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Secondary Text"}, "bg": {"name": "Вторичен текст"}}}, "color-main-meta-text": {"type": "color", "group": "main", "default": "#999", "translations": {"en": {"name": "Meta <PERSON>t"}, "bg": {"name": "Помощен текст"}}}, "color-main-background": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-main-borders": {"type": "color", "group": "main", "default": "#e5e5e5", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамки"}}}, "color-main-fields-text": {"type": "color", "group": "main", "default": "#666666", "translations": {"en": {"name": "Input Fields Text"}, "bg": {"name": "Полета за въвеждане - текст"}}}, "color-main-fields-background": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Input Fields Background"}, "bg": {"name": "Полета за въвеждане - фон"}}}, "color-main-fields-border": {"type": "color", "group": "main", "default": "#e8e8e8", "translations": {"en": {"name": "Input Fields Border"}, "bg": {"name": "Полета за въвеждане - рамка"}}}, "color-main-secondary-background": {"separator": "true", "type": "color", "group": "main", "default": "#ede3d0", "translations": {"en": {"name": "Secondary background (SB)"}, "bg": {"name": "Второстепенен фон (ВФ)"}}}, "color-main-secondary-background-borders": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "SB - Borders"}, "bg": {"name": "ВФ - Линии и рамки"}}}, "color-main-secondary-background-text": {"type": "color", "group": "main", "default": "#666", "translations": {"en": {"name": "SB - Text"}, "bg": {"name": "ВФ - Текст"}}}, "color-main-secondary-background-titles": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "SB - Titles"}, "bg": {"name": "ВФ - Заглавия"}}}, "color-main-secondary-background-highlight": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "SB - Highlight"}, "bg": {"name": "ВФ - Акцент"}}}, "color-promo-bar-text": {"type": "color", "group": "promo_bar", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-promo-bar-background": {"type": "color", "group": "promo_bar", "default": "#b2a08a", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-promo-bar-button-text": {"separator": "true", "type": "color", "group": "promo_bar", "default": "#fff", "translations": {"en": {"name": "Button Text"}, "bg": {"name": "Бутон - текст"}}}, "color-promo-bar-button-text-hover": {"type": "color", "group": "promo_bar", "default": "#fff", "translations": {"en": {"name": "Button Text Hover"}, "bg": {"name": "Бутон - текст - акцент"}}}, "color-promo-bar-button-background": {"type": "color", "group": "promo_bar", "default": "#2d2d2d", "translations": {"en": {"name": "<PERSON>ton Background"}, "bg": {"name": "Бутон - фон"}}}, "color-promo-bar-button-background-hover": {"type": "color", "group": "promo_bar", "default": "#2d2d2d", "translations": {"en": {"name": "Button Background Hover"}, "bg": {"name": "Бутон - фон - акцент"}}}, "color-promo-bar-button-border": {"type": "color", "group": "promo_bar", "default": "#2d2d2d", "translations": {"en": {"name": "Button Border"}, "bg": {"name": "Бутон - рамка"}}}, "color-promo-bar-button-border-hover": {"type": "color", "group": "promo_bar", "default": "#2d2d2d", "translations": {"en": {"name": "Button Border Hover"}, "bg": {"name": "Бутон - рамка - акцент"}}}, "color-promo-bar-close": {"separator": "true", "type": "color", "group": "promo_bar", "default": "#fff", "translations": {"en": {"name": "Close"}, "bg": {"name": "Затвори"}}}, "color-header-text": {"type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-header-text-secondary": {"type": "color", "group": "header", "default": "#ccc", "translations": {"en": {"name": "Secondary text"}, "bg": {"name": "Второстепенен текст"}}}, "color-header-navigation-text": {"separator": "true", "type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Navigation text"}, "bg": {"name": "Текст на менюто"}}}, "color-header-navigation-highlight": {"type": "color", "group": "header", "default": "#b2a08a", "translations": {"en": {"name": "Navigation highlight"}, "bg": {"name": "Мен<PERSON> - акцент"}}}, "color-header-navigation-submenu-background": {"separator": "true", "type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Navigation submenu background"}, "bg": {"name": "Фон на подменюто"}}}, "color-header-navigation-submenu-titles": {"type": "color", "group": "header", "default": "#333", "translations": {"en": {"name": "Navigation submenu text"}, "bg": {"name": "Текст на подменюто"}}}, "color-header-navigation-submenu-text": {"type": "color", "group": "header", "default": "#666", "translations": {"en": {"name": "Navigation submenu text"}, "bg": {"name": "Текст на подменюто"}}}, "color-header-background": {"separator": "true", "type": "color", "group": "header", "default": "#000", "translations": {"en": {"name": "Mobile menu background"}, "bg": {"name": "Фон на мобилното меню"}}}, "color-footer-text": {"type": "color", "group": "footer", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-footer-titles": {"type": "color", "group": "footer", "default": "#fff", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-footer-highlight": {"type": "color", "group": "footer", "default": "#b2a08a", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-footer-legal": {"type": "color", "group": "footer", "default": "#9a9a9a", "translations": {"en": {"name": "Bottom text"}, "bg": {"name": "Заключителен текст"}}}, "color-footer-socials": {"separator": "true", "type": "color", "group": "footer", "default": "#fff", "translations": {"en": {"name": "Social icons"}, "bg": {"name": "Социални иконки"}}}, "color-footer-socials-highlight": {"type": "color", "group": "footer", "default": "#b2a08a", "translations": {"en": {"name": "Social icons highlight"}, "bg": {"name": "Социални иконки - акцент"}}}, "color-slider-background": {"type": "color", "group": "slider", "default": "#f7f8fa", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-slider-text": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-slider-main-title": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Main title"}, "bg": {"name": "Основно заглавие"}}}, "color-slider-button-borders": {"separator": "true", "type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Button background"}, "bg": {"name": "Фон на бутона"}}}, "color-slider-button-text": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Button text"}, "bg": {"name": "Текст на бутона"}}}, "color-slider-arrows": {"separator": "true", "type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Navigation arrows"}, "bg": {"name": "Навигационни стрелки"}}}, "color-slider-dots": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Navigation controls"}, "bg": {"name": "Навигационни контроли"}}}, "color-slider-search-background": {"separator": "true", "type": "color", "group": "slider", "default": "#000", "translations": {"en": {"name": "Search - background"}, "bg": {"name": "Търсене - фон"}}}, "color-slider-search-borders": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Search - borders"}, "bg": {"name": "Търсене - рамка"}}}, "color-slider-search-text": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Search - text"}, "bg": {"name": "Търсене - текст"}}}, "color-products-background": {"type": "color", "group": "products", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-products-background-hover": {"type": "color", "group": "products", "default": "#f7f7f7", "translations": {"en": {"name": "Background - hover"}, "bg": {"name": "Фон - акцент"}}}, "color-products-borders": {"type": "color", "group": "products", "default": "#e8e8e8", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-products-title": {"type": "color", "group": "products", "default": "#333", "translations": {"en": {"name": "Title"}, "bg": {"name": "Заглавие"}}}, "color-products-text": {"type": "color", "group": "products", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-products-labels": {"type": "color", "group": "products", "default": "#b2a08a", "translations": {"en": {"name": "Labels"}, "bg": {"name": "Етикети"}}}, "color-products-highlight": {"type": "color", "group": "products", "default": "#b2a08a", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-products-new-price": {"type": "color", "group": "products", "default": "#333", "translations": {"en": {"name": "New price"}, "bg": {"name": "Нова цена"}}}, "color-products-old-price": {"type": "color", "group": "products", "default": "#b2a08a", "translations": {"en": {"name": "Old price"}, "bg": {"name": "Стара цена"}}}, "color-products-filters": {"separator": "true", "type": "color", "group": "products", "default": "#f7f8fa", "translations": {"en": {"name": "Filters background"}, "bg": {"name": "Фон на филтрите"}}}, "color-product-details-image-background": {"title": {"en": "Images", "bg": "Снимки"}, "type": "color", "group": "product-details", "default": "#f7f7f7", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-product-details-image-borders": {"type": "color", "group": "product-details", "default": "#e8e8e8", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-product-details-background": {"title": {"en": "Text field", "bg": "Текстово поле"}, "separator": true, "type": "color", "group": "product-details", "default": "#f7f7f7", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-product-details-text": {"type": "color", "group": "product-details", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-product-details-highlight": {"type": "color", "group": "product-details", "default": "#b2a08a", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-button-text": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Primary - Text"}, "bg": {"name": "Основен - текст"}}}, "color-button-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Primary - Text Hover"}, "bg": {"name": "Основен - текст акцент"}}}, "color-button-background": {"type": "color", "group": "buttons", "default": "#b2a08a", "translations": {"en": {"name": "Primary - Background"}, "bg": {"name": "Основен - фон"}}}, "color-button-background-hover": {"type": "color", "group": "buttons", "default": "#9d8d79", "translations": {"en": {"name": "Primary - <PERSON>ver"}, "bg": {"name": "Основен - фон акцент"}}}, "color-button-border": {"type": "color", "group": "buttons", "default": "#b2a08a", "translations": {"en": {"name": "Primary - Border"}, "bg": {"name": "Основен - Рамка"}}}, "color-button-border-hover": {"type": "color", "group": "buttons", "default": "#9d8d79", "translations": {"en": {"name": "Primary - <PERSON> Hover"}, "bg": {"name": "Основен - рамка акцент"}}}, "color-button-secondary-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#9d8d79", "translations": {"en": {"name": "Secondary - Text"}, "bg": {"name": "Вторичен - текст"}}}, "color-button-secondary-text-hover": {"type": "color", "group": "buttons", "default": "#f2f2f2", "translations": {"en": {"name": "Secondary - Text Hover"}, "bg": {"name": "Вторичен - текст акцент"}}}, "color-button-secondary-background": {"type": "color", "group": "buttons", "default": "#f2f2f2", "translations": {"en": {"name": "Secondary - Background"}, "bg": {"name": "Вторичен - фон"}}}, "color-button-secondary-background-hover": {"type": "color", "group": "buttons", "default": "#9d8d79", "translations": {"en": {"name": "Secondary - Background Hover"}, "bg": {"name": "Вторичен - фон акцент"}}}, "color-button-secondary-border": {"type": "color", "group": "buttons", "default": "#9d8d79", "translations": {"en": {"name": "Secondary - Border"}, "bg": {"name": "Вторичен - рамка"}}}, "color-button-secondary-border-hover": {"type": "color", "group": "buttons", "default": "#9d8d79", "translations": {"en": {"name": "Secondary - Border Hover"}, "bg": {"name": "Вторичен - рамка акцент"}}}, "color-button-disable-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Disable - Text"}, "bg": {"name": "Изключен - текст"}}}, "color-button-disable-background": {"type": "color", "group": "buttons", "default": "#e2e2e2", "translations": {"en": {"name": "Disable - Background"}, "bg": {"name": "Изключен - фон"}}}, "color-button-disable-border": {"type": "color", "group": "buttons", "default": "#e2e2e2", "translations": {"en": {"name": "Disable - Border"}, "bg": {"name": "Изключен - рамка"}}}, "color-button-active-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Active - Text"}, "bg": {"name": "Активен - текст"}}}, "color-button-active-background": {"type": "color", "group": "buttons", "default": "#5cb3eb", "translations": {"en": {"name": "Active - Background"}, "bg": {"name": "Активен - фон"}}}, "color-button-active-border": {"type": "color", "group": "buttons", "default": "#5cb3eb", "translations": {"en": {"name": "Active - Border"}, "bg": {"name": "Активен - рамка"}}}, "color-label-new-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "New - Text"}, "bg": {"name": "Нов - текст"}}}, "color-label-new-background": {"type": "color", "group": "labels", "default": "#333", "translations": {"en": {"name": "New - Background"}, "bg": {"name": "Нов - фон"}}}, "color-label-sale-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Sale - Text"}, "bg": {"name": "Разпродажба - текст"}}}, "color-label-sale-background": {"type": "color", "group": "labels", "default": "#333", "translations": {"en": {"name": "Sale - Background"}, "bg": {"name": "Разпродажба - фон"}}}, "color-label-delivery-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Delivery - Text"}, "bg": {"name": "Доставка - текст"}}}, "color-label-delivery-background": {"type": "color", "group": "labels", "default": "#333", "translations": {"en": {"name": "Delivery - Background"}, "bg": {"name": "Доставка - фон"}}}, "color-label-discount-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Discount - Text"}, "bg": {"name": "Отстъпка - текст"}}}, "color-label-discount-background": {"type": "color", "group": "labels", "default": "#5cb3eb", "translations": {"en": {"name": "Discount - Background"}, "bg": {"name": "Отстъпка - фон"}}}, "color-label-leasing-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Leasing - Text"}, "bg": {"name": "Лизин<PERSON> - текст"}}}, "color-label-leasing-background": {"type": "color", "group": "labels", "default": "#5cb3eb", "translations": {"en": {"name": "Leasing - Background"}, "bg": {"name": "Лизинг - фон"}}}, "color-label-featured-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Featured - Text"}, "bg": {"name": "Препоръчан - текст"}}}, "color-label-featured-background": {"type": "color", "group": "labels", "default": "#333", "translations": {"en": {"name": "Featured - Background"}, "bg": {"name": "Препоръчан - фон"}}}, "color-popups-background": {"type": "color", "group": "popups", "default": "#000", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-popups-borders": {"type": "color", "group": "popups", "default": "#еее", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Очертания и линии"}}}, "color-popups-text": {"type": "color", "group": "popups", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-popups-titles": {"type": "color", "group": "popups", "default": "#fff", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-popups-highlight": {"type": "color", "group": "popups", "default": "#b2a08a", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-popups-button-background": {"separator": "true", "type": "color", "group": "popups", "default": "#b2a08a", "translations": {"en": {"name": "Primary buttons - background"}, "bg": {"name": "Основни бутони - фон"}}}, "color-popups-button-text": {"type": "color", "group": "popups", "default": "#fff", "translations": {"en": {"name": "Primary buttons - text"}, "bg": {"name": "Основни бутони - текст"}}}, "color-popups-button-secondary-background": {"type": "color", "group": "popups", "default": "#f2f2f2", "translations": {"en": {"name": "Secondary buttons - background"}, "bg": {"name": "Второстепенни бутони - фон"}}}, "color-popups-button-secondary-text": {"type": "color", "group": "popups", "default": "#333", "translations": {"en": {"name": "Secondary buttons - text"}, "bg": {"name": "Второстепенни бутони - текст"}}}}}}