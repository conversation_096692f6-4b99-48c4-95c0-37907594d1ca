module.exports = function (grunt, data) {

	return {
		options: {
			livereload: true
		},
		setups: {
			files: ['<%= _PATH.config %>/<%= _FILE.config_grunt %>'],
			tasks: [
				'json_generator:bowerrc',
				'json_generator:bower',
				'shell:bowerRemove',
				'shell:bowerCacheClean',
				'shell:bowerUpdate',
				'less:vendors',
				'less:vendorsRTL',
				'less:theme',
				'shell:themeCSS',
				'concat:vendors',
				'googlefonts',
				'embedfont',
                'copy:vendorsFonts',
				'open'
			]
		},
		styles: {
			files: ['<%= _PATH.src_styles %>/<%= _FILE.styles_less %>', '<%= _PATH.src_styles %>/**/<%= _FILE.styles_less %>'],
			tasks: ['less:custom', 'less:customRTL', 'less:theme', 'shell:themeCSS']
		},
		sprites: {
			files: ['<%= _PATH.src_images %>/**/<%= _DIR.sprite %>/<%= _FILE.images_raster %>', '!<%= _PATH.src_images_svg_sprite %>/**'],
			tasks: ['sprite']
		},
		svgsprites: {
			files: ['<%= _PATH.src_images_svg_sprite %>/<%= _FILE.images_svg %>'],
			tasks: ['svg_sprite', 'copy:svgSprite']
		},
		images: {
			files: ['<%= _PATH.src_images %>/**/<%= _FILE.images_all %>', '!<%= _PATH.src_images %>/**/<%= _DIR.sprite %>/**'],
			tasks: ['newer:copy:images']
		},
		scripts: {
			files: ['<%= _PATH.src_scripts %>/<%= _FILE.scripts_js %>', '<%= _PATH.src_scripts %>/**/<%= _FILE.scripts_js %>'],
			tasks: ['concat:custom']
		},
		templates: {
			files: ['<%= _PATH.templates %>/<%= _FILE.templates_all %>', '<%= _PATH.templates %>/**/<%= _FILE.templates_all %>']
		}
	}

};
