# Създаване на теми за платформа CloudCart

by CloudCart Team

## Възможности на системата

###### Стилове
- LESS компилатор
- CSS форматиране за по-красив и четим код
- CSS минимизиране за по-бързо зареждане на страниците
- CSS autoprefixer за автоматична корекция на браузър префиксите (-webkit, -moz, -o, -ms и т.н.)
- CSS валидиране и проверка за грешки

###### Фонтове
- Поддръжка на Google фонтове и създаване на локални копия на използваните в проекта
- Автоматично генериране на Embed фонтове и необходимият CSS за незабавната им употреба

###### Изображения
- Автоматично генериране на PNG, JPG, GIF и SVG спрайтове + CSS по предварително зададен шаблон (handlebars)
- Оптимизатор/минификатор на всички изображения и спрайтове за по-бързо зареждане на страниците
- Автоматично пренасяне на изображенията от работната към публичната директория

###### Скриптове
- JS конкатенатор за обединяване на всички използвани скриптове
- JS форматиране за по-красив и четим код
- JS минимизиране за по-бързо зареждане на страниците
- JS валидиране и проверка за грешки

###### Vendors
- Интегриран [Bower] за избор на работни пакети като [Bootstrap], [Font-Awesome], [Jquery], [OWL Carousel] и т.н. Изберете желаните и **MakeCSS** ще се погрижи за интегрирането им.
- Base64 кодиране на всички изображения и фонтове в стиловия файл на съответния vendor за по-бързо зареждане и по-добра организация на файловете
- Интегриран Custom [Modernizr] Builder

###### Други
- Live Server за преглед на проекта в реално време. Интегриран [Grunt-Contrib-Watch-Chokidar] като алтернатива на Grunt-Watch. По-бърз и по-малко проблемен! Следене за модификации в работните файлове и мигновен преглед на промените в браузъра
- Своевременно генериране на RTL версия на сайта
- Бързодействие при изпълнението на таскове с [Jit-Grunt]. Оптимизира скоростта на изпълнение с над 50%!
- Модулно разпределение на тасковете с [Load-Grunt-Config]
- Кеширане и обработка само на модифицираните файлове с [Grunt-Newer]
- Визуализация в конзолата на времето на изпълнение на тасковете с [Time-Grunt]
- Автоматично почистване на всички работни/временни файлове, създадени в процеса на разработка
- Вграден JSON генератор
- HTML форматиране за по-красив и четим код
- Обновяване на всички [Grunt] модули и [Bower] пакети само с една команда! За да бъде системата ви винаги с актуални версии на [Bootstrap], [Font-Awesome], [Jquery] и т.н.
- Интуитивна файлова структура
- Бърза и лесна конфигурация. За да започнете работа е небоходимо да редактирате един-единствен файл - *assets/setup/config.js*

## Крайният резултат?

Крайният резултат е създаването на 2 минифицирани/оптимизирани файлa - за стилове и скриптове. Съотвено в LTR и RTL вариант.

За удобство на разработчика е добавена опция за генериране на още два сателитни, неминифицирани файла - *styles.css* и *scripts.js*. 

## Инсталация

Преди да пристъпите към инсталация, трябва да инсталирате следните технологии:

- [NodeJS]
- [Grunt]
- [Bower]

### Системна подготовка

Отворете конзола като Администратор и изберете пътя до *cloudcart\builder\themes*

```sh
cd cloudcart\builder\themes
```

Изпълнете команда:

```sh
npm install
```

ТАЗИ ПРОЦЕДУРА СЕ ИЗПЪЛНЯВА САМО ВЕДНЪЖ! Не е необходимо да се изпълнява при инсталация на следващи теми.

### Инсталация на тема

Отворете конзола като Администратор и изпълнете команда:

```sh
php themes.php install <theme_name> <site_url>
```

*theme_name - име на темата*
*site_url - http адрес на темата* (http://example.com)

Тази команда създава всички необходими директории за работа.

През конзолата изберете пътя до новосъздадената директория на темата:

```sh
cd cloudcart\builder\themes\theme_name
```

Сега инсталирайте [Grunt] модулите с команда:

```sh
npm install
```

Отворете файла *cloudcart\builder\themes\theme_name\config\grunt.js* и нанесете промени спрямо вашите нуджи. Всяка една декларация има пояснителен коментар с нейното предназначение.

```js
theme: {
	name: 'theme_name', // This name is used as a public directory name for assets. Use lowercase letters.
    variables: { // Copy/Paste all less variables with "cc-" prefix for replacing by php
        "color-1": "cc-color-1",
        "color-2": "cc-color-2",
        "color-3": "cc-color-3"
    }
},
server: {
	http: 'site_url' // Write http address of the theme
},
fonts: {
	google: [ // Array of all google fonts
		{
			family: 'Roboto Condensed',
			subsets: ['latin', 'cyrillic', 'greek'],
			styles: [300, 400, 700]
		},
		{
			family: 'Roboto',
			subsets: ['latin', 'cyrillic', 'greek'],
			styles: [300, 400, 500, 700, 900]
		}
	],
	embed: { // Array of all embed fonts. If you don't have embed fonts, leave this object empty
	}
},
vendors: { // Array of all vendors. Example: "bower-pkg-name": "version"
	devDep: {
		"bootstrap": "3.3.6", // required
		"fontawesome": "*", // required
		"jquery-breakpoint-check": "*" // required
	},
	styles: [ // All styles of the selected vendors
		'<%= _PATH.src_vendors %>/jqueryui/jquery-ui.css', // required
		'<%= _PATH.src_vendors %>/select2/select2.css', // required
		'<%= _PATH.src_vendors %>/uniformjs/uniformjs.css', // required
		'<%= _PATH.src_vendors %>/bootstrap/less/bootstrap.less', // required
		'<%= _PATH.src_vendors %>/fontawesome/less/font-awesome.less' // required
	],
	scripts: [ // All scripts of the selected vendors
		'<%= _PATH.src_vendors %>/bootstrap/dist/js/bootstrap.js', // required
		'<%= _PATH.src_vendors %>/jquery-breakpoint-check/js/jquery-breakpoint-check.js' // required
	]
}
```
*Конфигурационният файл се следи в реално време от [Grunt-Contrib-Watch-Chokidar], така че можете да нанасяте промени и в процеса на работа.*

След като приключи инсталацията на [Grunt] модулите, инсталирайте темата с команда:

```sh
grunt make-install
```

Тази команда ще подготви темата спрямо настройките, които нанесохте в конфигурационния файл по-горе - *cloudcart\builder\themes\theme_name\config\grunt.js*

След приключване на процедурата и при коректно конфигурирани файлове, в браузъра Ви трябва да се зареди избраният *site_url* адрес. Накрая, просто активирайте новосъздадената тема през административния панел.

### Деинсталация на тема

Отворете конзола като Администратор и изберете пътя до *cloudcart\builder\themes*

```sh
cd cloudcart\builder\themes
```

Изпълнете команда:

```sh
php themes.php uninstall <theme_name>
```

Това е всичко!

## Команди

Това е пълният списък с команди и тяхното предназначение.

##### Основни

Основните команди трябва да се изпълняват в посочената последователност.

| Команда               | Описание
| ----------------------- | -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------
| *grunt make-install*    | Инсталира системата. Подготвя проекта спрямо настройките в *config\grunt.js*. Използвайте командата само при инсталация!
| *grunt make-server*     | Активира [Grunt-Contrib-Watch-Chokidar] и Live сървъра за отразяване на промените в реално време. Ако прекъснете работа, това е командата, с която можете да продължите.
| *grunt make-production* | Финализиране на проекта - премахва всички временни/работни файлове от public директорията; оптимизира изображенията; минифицира стиловете и скриптовете.

##### Допълнителни

Допълнителните команди се използват само след финализиране на проекта, т.е. след изпълнение на команда *grunt make-production*. Употребата им не е задължителна.

| Команда               | Описание
| --------------------- | -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------
| *grunt make-change*   | За промени в проекта, т.е. след вече изпълнена команда grunt *make-production*

##### Помощни

Помощните команди могат да се използват по всяко време от работния процес.

| Команда               | Описание
| --------------------- | -----------------------------------------------------------------------------------------------------------------------------------------------------------------------------
| *grunt make-update*   | Обновява всички [Grunt] и [Bower] пакети. Премахва тези, които не се използват. Изчиства кеша.
| *grunt make-status*   | Връща информация в какъв режим е темата - development или production.

## Grunt модули

Системата използва следните [Grunt] модули:

- [Grunt-Cleanempty]
- [Grunt-Concat-CSS]
- [Grunt-Contrib-Clean]
- [Grunt-Contrib-Concat]
- [Grunt-Contrib-Copy]
- [Grunt-Contrib-CSSlint]
- [Grunt-Contrib-CSSmin]
- [Grunt-Contrib-Imagemin]
- [Grunt-Contrib-JShint]
- [Grunt-Contrib-LESS]
- [Grunt-Contrib-Uglify]
- [Grunt-Contrib-Watch-Chokidar]
- [Grunt-CSSbeautifier]
- [Grunt-Dev-Update]
- [Grunt-Embedfont]
- [Grunt-Googlefonts]
- [Grunt-JSbeautifier]
- [Grunt-JSONgenerator]
- [Grunt-Modernizr-Builder]
- [Grunt-Mkdir]
- [Grunt-Newer]
- [Grunt-Open]
- [Grunt-Prettify]
- [Grunt-Shell-Spawn]
- [Grunt-Spritesmith]
- [Grunt-SVG-Sprite]
- [Grunt-SVGmin]
- [Grunt-TargetHTML]
- [Grunt-Template]
- [Grunt-Text-Replace]
- [Jit-Grunt]
- [LESS-Plugin-Autoprefix]
- [LESS-Plugin-Inline-Urls]
- [LESS-Plugin-RTL]
- [LESS-Plugin-Theme]
- [Load-Grunt-Config]
- [Time-Grunt] 

[NodeJS]: <https://nodejs.org>
[Grunt]: <http://gruntjs.com/>
[Bower]: <http://bower.io/>
[Bootstrap]: <http://getbootstrap.com/>
[Jquery]: <https://jquery.com/>
[OWL Carousel]: <http://owlgraphic.com/owlcarousel/>
[Font-Awesome]: <https://fortawesome.github.io/Font-Awesome/>
[Modernizr]: <https://modernizr.com/>
[Grunt-Cleanempty]: <https://www.npmjs.com/package/grunt-cleanempty>
[Grunt-Concat-CSS]: <https://www.npmjs.com/package/grunt-concat-css>
[Grunt-Contrib-Clean]: <https://www.npmjs.com/package/grunt-contrib-clean>
[Grunt-Contrib-Concat]: <https://github.com/gruntjs/grunt-contrib-concat>
[Grunt-Contrib-Copy]: <https://github.com/gruntjs/grunt-contrib-copy>
[Grunt-Contrib-CSSlint]: <https://github.com/gruntjs/grunt-contrib-csslint>
[Grunt-Contrib-CSSmin]: <https://www.npmjs.com/package/grunt-contrib-cssmin>
[Grunt-Contrib-Imagemin]: <https://www.npmjs.com/package/grunt-contrib-imagemin>
[Grunt-Contrib-JShint]: <https://github.com/gruntjs/grunt-contrib-jshint>
[Grunt-Contrib-LESS]: <https://www.npmjs.com/package/grunt-contrib-less>
[Grunt-Contrib-Uglify]: <https://github.com/gruntjs/grunt-contrib-uglify>
[Grunt-Contrib-Watch-Chokidar]: <https://www.npmjs.com/package/grunt-contrib-watch-chokidar>
[Grunt-CSSbeautifier]: <https://www.npmjs.com/package/grunt-cssbeautifier>
[Grunt-Dev-Update]: <https://www.npmjs.com/package/grunt-dev-update>
[Grunt-Embedfont]: <https://github.com/tylerbeck/grunt-embedfont>
[Grunt-Googlefonts]: <https://github.com/Mika-/grunt-google-fonts>
[Grunt-JSbeautifier]: <https://www.npmjs.com/package/grunt-jsbeautifier>
[Grunt-JSONgenerator]: <https://www.npmjs.com/package/grunt-json-generator>
[Grunt-Modernizr-Builder]: <https://www.npmjs.com/package/grunt-modernizr-builder>
[Grunt-Mkdir]: <https://www.npmjs.com/package/grunt-mkdir> 
[Grunt-Newer]: <https://www.npmjs.com/package/grunt-newer>
[Grunt-Open]: <https://github.com/jsoverson/grunt-open>
[Grunt-Prettify]: <https://www.npmjs.com/package/grunt-prettify>
[Grunt-Shell-Spawn]: <https://github.com/cri5ti/grunt-shell-spawn>
[Grunt-Spritesmith]: <https://github.com/Ensighten/grunt-spritesmith>
[Grunt-SVG-Sprite]: <https://www.npmjs.com/package/grunt-svg-sprite>
[Grunt-SVGmin]: <https://www.npmjs.com/package/grunt-svgmin>
[Grunt-TargetHTML]: <https://github.com/changer/grunt-targethtml>
[Grunt-Template]: <https://github.com/mathiasbynens/grunt-template>
[Grunt-Text-Replace]: <https://www.npmjs.com/package/grunt-text-replace>
[Jit-Grunt]: <https://www.npmjs.com/package/jit-grunt>
[LESS-Plugin-Autoprefix]: <https://github.com/less/less-plugin-autoprefix>
[LESS-Plugin-Inline-Urls]: <https://github.com/less/less-plugin-inline-urls>
[LESS-Plugin-RTL]: <https://github.com/less/less-plugin-rtl>
[LESS-Plugin-Theme]: <https://github.com/cobomi/less-plugin-theme>
[Load-Grunt-Config]: <https://github.com/firstandthird/load-grunt-config>
[Time-Grunt]: <https://www.npmjs.com/package/time-grunt>

