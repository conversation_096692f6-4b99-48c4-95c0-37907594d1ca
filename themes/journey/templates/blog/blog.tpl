{include file="../layout/header.tpl"}



{W::blog()->handle(segment(2))}
{$widget->setSeo('blog')}
<!-- BEGIN: content -->
<div class="_content">
	<div class="container">
		<div class="_section-separator">
			<div class="row">
				<div class="col-sm-12">
					<div class="_section-title _section-title-breadcrumb">
						<h2>{W::blog()->getHeaderString()}</h2>
					</div>
					{$breadcrumbs = [['name' => "{t}sf.global.act.blog{/t}", "link" => route('blog.list')]]}
					{include file="widgets/common/breadcrumbs.tpl" breadcrumbs=$breadcrumbs active=W::blog()->getHeaderString()}
				</div>
			</div>
		</div>
	</div>
	{if W::blog()->isEnabled()}
		<div class="container">
			<div class="_section-separator">
				<div class="row">
					<div class="col-md-8">
						<div class="_blog-main">
							{include file="widgets/blog/articles.tpl" widget=W::blog()}
							{include file="widgets/common/pagging.tpl" page=W::blog()->getPage() pages=W::blog()->getPages() link="{W::blog()->getPagingUrl()}"}
						</div>
					</div>
					<div class="col-md-4">
						{include file="./sidebar.tpl"}
					</div>
				</div>
			</div>
		</div>
	{else}
		<div class="container _section-separator">
			{include file="../notifications/error-include.tpl" error="{t}sf.widget.blog.err.blog_is_disabled{/t}"}
		</div>
	{/if}
	<!-- LOAD: seo -->
	{if W::blog()->isEnabled()}
		{if !empty(W::blog()->getBlog())}
			{include file="widgets/common/microdata/blog.tpl" widget=W::blog()}
		{/if}
	{/if}
	{include file="widgets/common/microdata/breadcrumb.tpl" breadcrumbs=$breadcrumbs active=W::blog()->getHeaderString()}
</div><!--// END: content -->
{include file="../layout/footer.tpl"}