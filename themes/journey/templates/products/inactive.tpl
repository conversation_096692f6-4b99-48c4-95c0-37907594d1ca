{include file="widgets/product/details/primary_image.tpl" zoom_uicontrol="jqueryZoom"}
{$product->name}
{t}sf.global.label.you_save{/t}
{if !empty($product->description)}
	{$product->description nofilter}
{/if}
{t}sf.global.label.published{/t}
{$product->date_added_formatted}
{if !empty($product->vendor)}
	{t}sf.global.label.vendor{/t}
	<a href="{$product->vendor->url()}">{$product->vendor->name}</a>
{/if}
{if !empty($product->category)}
	{t}sf.global.label.category{/t}
	<a href="{$product->category->url()}">{$product->category->name}</a>
{/if}
{t}sf.widget.product.warn.product_inactive{/t}