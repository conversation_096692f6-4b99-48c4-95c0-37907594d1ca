{"name": "journey", "version": "0.1", "assets": {"default_images": {"logo": "{$img_url}/themes/journey/img/defaults/logo/logo.png", "favicon": "", "product": {"50": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "100": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "150": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "300": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "600": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "800": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "1024": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "1280": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "1920": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg"}, "vendor": {"50": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "100": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "150": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "300": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "600": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "800": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "1024": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "1280": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "1920": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg"}, "category": {"50": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "100": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "150": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "300": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "600": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "800": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "1024": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "1280": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg", "1920": "{$img_url}/themes/journey/img/defaults/noimage/noimage.svg"}}, "default_content_images": {"product": ["{$img_url}/themes/journey/img/defaults/products/product-01.png", "{$img_url}/themes/journey/img/defaults/products/product-02.png", "{$img_url}/themes/journey/img/defaults/products/product-03.png", "{$img_url}/themes/journey/img/defaults/products/product-04.png", "{$img_url}/themes/journey/img/defaults/products/product-05.png", "{$img_url}/themes/journey/img/defaults/products/product-06.png"], "category": ["{$img_url}/themes/journey/img/defaults/products/product-01.png", "{$img_url}/themes/journey/img/defaults/products/product-02.png", "{$img_url}/themes/journey/img/defaults/products/product-03.png", "{$img_url}/themes/journey/img/defaults/products/product-04.png"], "vendor": ["{$img_url}/themes/journey/img/defaults/brands/brand-01.svg", "{$img_url}/themes/journey/img/defaults/brands/brand-02.svg", "{$img_url}/themes/journey/img/defaults/brands/brand-03.svg", "{$img_url}/themes/journey/img/defaults/brands/brand-04.svg"]}}, "widgets": {"navigationMain": {"map": "navigation.main"}, "navigationFooter": {"map": "navigation.footer"}, "headerText": {"map": "extra.text", "group": "header", "name": {"en": "Header Text", "bg": "<PERSON><PERSON><PERSON><PERSON><PERSON> текст"}, "settings": {"defaults": {"title": "Contact Us:", "text": "<strong>+359888123456</strong>"}}}, "carousel": {"map": "extra.carousel", "group": "slider", "name": {"en": "Slide<PERSON>", "bg": "Слайдер"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 1, "autoplay": "no", "interval": 5000, "caption": "no", "controls": "yes", "indicators": "yes", "pause": "no", "cycle": "no", "slides": {"1": {"img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/carousel/slide.png", "link_type": "section", "link_value": "products", "link_caption": "", "html": "<h3><span class='_first-word'>Travel</span> Agency</h3><p>Lorem ipsum dolor sit amet, quo ut tation exerci facilisis. Sea harum primis feugait cu. Ad ius placerat singulis indoctum. Modo pertinax consequat vis in, no dicat denique apeirian nam, no mea alii nemore numquam. Ad nihil labitur ocurreret vel, ne eam amet impetus. Ei error utinam qui, posse sententiae persequeris usu in. Sumo possim probatus te eam.</p>", "horizontal_position": "center", "vertical_position": "middle", "target": "_self"}, "2": {"img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/carousel/slide-2.png", "link_type": "section", "link_value": "products", "link_caption": "", "html": "<h3><span class='_first-word'>Travel</span> Agency</h3><p>Lorem ipsum dolor sit amet, quo ut tation exerci facilisis. Sea harum primis feugait cu. Ad ius placerat singulis indoctum. Modo pertinax consequat vis in, no dicat denique apeirian nam, no mea alii nemore numquam. Ad nihil labitur ocurreret vel, ne eam amet impetus. Ei error utinam qui, posse sententiae persequeris usu in. Sumo possim probatus te eam.</p>", "horizontal_position": "center", "vertical_position": "middle", "target": "_self"}, "3": {"img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/carousel/slide-3.png", "link_type": "section", "link_value": "products", "link_caption": "", "html": "<h3><span class='_first-word'>Travel</span> Agency</h3><p>Lorem ipsum dolor sit amet, quo ut tation exerci facilisis. Sea harum primis feugait cu. Ad ius placerat singulis indoctum. Modo pertinax consequat vis in, no dicat denique apeirian nam, no mea alii nemore numquam. Ad nihil labitur ocurreret vel, ne eam amet impetus. Ei error utinam qui, posse sententiae persequeris usu in. Sumo possim probatus te eam.</p>", "horizontal_position": "center", "vertical_position": "middle", "target": "_self"}}}}}, "testimonial": {"map": "extra.carousel", "group": "testimonials", "name": {"en": "Testimonials", "bg": "Препоръки"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 4, "autoplay": "no", "interval": 5000, "caption": "no", "controls": "no", "indicators": "yes", "pause": "no", "cycle": "yes", "slides": {"1": {"img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/testimonial/testimonial-01.png", "link_type": "section", "link_value": "products", "link_caption": "", "html": "<p><em>\"Lorem ipsum dolor sit amet, ea sit hinc mundi erroribus, id sit saepe doming minimum. Lorem ipsum dolor sit amet, ea sit hinc mundi erroribus, id sit saepe doming minimum. Lorem ipsum dolor sit amet.\"</em></p>", "horizontal_position": "left", "vertical_position": "middle", "target": "_self"}, "2": {"img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/testimonial/testimonial-02.png", "link_type": "section", "link_value": "products", "link_caption": "", "html": "<p><em>\"Lorem ipsum dolor sit amet, ea sit hinc mundi erroribus, id sit saepe doming minimum. Lorem ipsum dolor sit amet, ea sit hinc mundi erroribus, id sit saepe doming minimum. Lorem ipsum dolor sit amet.\"</em></p>", "horizontal_position": "left", "vertical_position": "middle", "target": "_self"}, "3": {"img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/testimonial/testimonial-01.png", "link_type": "section", "link_value": "products", "link_caption": "", "html": "<p><em>\"Lorem ipsum dolor sit amet, ea sit hinc mundi erroribus, id sit saepe doming minimum. Lorem ipsum dolor sit amet, ea sit hinc mundi erroribus, id sit saepe doming minimum. Lorem ipsum dolor sit amet.\"</em></p>", "horizontal_position": "left", "vertical_position": "middle", "target": "_self"}, "4": {"img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/testimonial/testimonial-02.png", "link_type": "section", "link_value": "products", "link_caption": "", "html": "<p><em>\"Lorem ipsum dolor sit amet, ea sit hinc mundi erroribus, id sit saepe doming minimum. Lorem ipsum dolor sit amet, ea sit hinc mundi erroribus, id sit saepe doming minimum. Lorem ipsum dolor sit amet.\"</em></p>", "horizontal_position": "left", "vertical_position": "middle", "target": "_self"}}}}}, "categoryProperties": {"map": "product.categoryProperties", "name": {"en": "Category Properties", "bg": ""}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true}}}, "filters": {"map": "product.filters", "mode": "range_slider", "settings": {"defaults": {"per_page": "6", "per_page_options": [6, 12, 18, 24], "products_price_ranges": [[100, 100000]], "price_range_step": "500"}}, "name": {"en": "Product listing settings", "bg": "Настройки на продуктов каталог"}, "description": {"en": "", "bg": ""}}, "productsRelated": {"map": "product.related", "name": {"en": "Related Products", "bg": "Свързани продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Related products", "type": "tag", "products": 2}}}, "showcaseProducts": {"map": "product.productShowcase", "name": {"en": "Destination Showcase", "bg": "Витрина с продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "New Destinations", "products": 6, "new": "yes", "sale": "no"}}}, "productsDetails": {"map": "product.productsDetails", "name": {"en": "Detail information for product", "bg": "Детайлна информация за продукт"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {}}}, "showcaseCategory": {"map": "product.showcase", "name": {"en": "Category Showcase", "bg": "Витрина с категории"}, "settings": {"defaults": {"header": "Categories", "type": "category", "amount": 5, "show_name": true, "show_description": false}}}, "page": {"map": "extra.page"}, "vendors": {"map": "product.vendors"}, "search": {"map": "extra.search", "group": "header", "name": {"en": "Global Search", "bg": "Глобално търсене"}, "description": {"en": "A search form for your site", "bg": "Форма за търсене"}}, "banners": {"map": "extra.banner", "group": "banners", "name": {"en": "Banners", "bg": "Банери"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 6, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/banners/banner-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/banners/banner-02.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "3": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/banners/banner-03.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "4": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/banners/banner-04.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "5": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/banners/banner-05.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "6": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/banners/banner-06.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "bannersPages": {"map": "extra.banner", "group": "banners", "name": {"en": "Banners - St<PERSON> Pages", "bg": "Банери - Статични страници"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 1, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/journey/img/defaults/bannersPages/banner-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "logo": {"map": "extra.logo", "editable": "no"}, "userControls": {"map": "user.controls"}, "googleMap": {"map": "contact.googleMap", "name": {"en": "Google Map", "bg": "<PERSON>у<PERSON><PERSON>л карта"}, "description": {"en": "", "bg": ""}}, "contactForm": {"map": "contact.form"}, "contactInformation": {"map": "contact.information", "name": {"en": "Contact information", "bg": "Контактна информация"}, "description": {"en": "", "bg": ""}}, "authorize": {"map": "user.authorize"}, "social": {"map": "extra.social", "group": "footer", "name": {"en": "Social Links", "bg": ""}, "description": {"en": "", "bg": ""}}, "providers": {"map": "payment.providers"}, "article": {"map": "blog.article"}, "blog": {"map": "blog.blog", "name": {"en": "Blog", "bg": ""}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"per_page": 3}}}, "recentArticles": {"map": "blog.recentArticles", "name": {"en": "Recent Articles", "bg": "Последни статии"}, "description": {"en": "", "bg": ""}}, "recentComments": {"map": "blog.recentComments", "name": {"en": "Recent Comments", "bg": "Последни коментари"}, "description": {"en": "", "bg": ""}}, "utilities": {"map": "base.utilities"}, "share": {"map": "extra.addThisShare", "group": "products", "name": {"en": "Social Share", "bg": ""}, "description": {"en": "", "bg": ""}}, "sharrre": {"map": "extra.sharrreShare"}, "productsBackground": {"map": "extra.backgroundImage", "group": "products", "name": {"en": "Products - Background", "bg": "Продуктов листинг - Фон"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"type": "external", "src": "{$img_url}/themes/journey/img/defaults/products/background/background-01.png"}}}, "newsletter": {"map": "mailchimp.newsletter", "name": {"en": "Newsletter (Mailchimp)", "bg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н"}, "description": {"en": "Newsletter form for your site", "bg": "Форма за бюлетин"}}, "leasing": {"map": "store.leasing"}}, "globals": {"widgets": {"product": {"listing": {}, "details": {}, "showcase": {"settings": {"defaults": {"products": 4}}}}}}, "settings": {"groups": {"main": {"en": {"name": "Main"}, "bg": {"name": "Основни"}}, "header": {"en": {"name": "Header"}, "bg": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "footer": {"en": {"name": "Footer"}, "bg": {"name": "<PERSON>ут<PERSON>р"}}, "slider": {"en": {"name": "Slide<PERSON>"}, "bg": {"name": "Слайдер"}}, "showcase_category": {"en": {"name": "Showcase Categories"}, "bg": {"name": "Витрина с категории"}}, "product": {"en": {"name": "Products"}, "bg": {"name": "Продукти"}}, "brand": {"en": {"name": "Brands"}, "bg": {"name": "Марки"}}, "testimonials": {"en": {"name": "Testimonials"}, "bg": {"name": "Препоръки"}}, "breadcrumb": {"en": {"name": "Breadcrumb"}, "bg": {"name": "Пътечка с линкове"}}, "pagination": {"en": {"name": "Pagination"}, "bg": {"name": "Странициране"}}, "buttons": {"en": {"name": "Buttons"}, "bg": {"name": "Бутони"}}, "labels": {"en": {"name": "Labels"}, "bg": {"name": "Етикети"}}}, "variables": {"color-main-titles-text": {"type": "color", "group": "main", "default": "#000", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-main-text": {"type": "color", "group": "main", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-main-highlight": {"type": "color", "group": "main", "default": "#fb9f15", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-main-secondary-text": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Secondary Text"}, "bg": {"name": "Вторичен текст"}}}, "color-main-meta-text": {"type": "color", "group": "main", "default": "#b0b0af", "translations": {"en": {"name": "Meta <PERSON>t"}, "bg": {"name": "Помощен текст"}}}, "color-main-background": {"type": "color", "group": "main", "default": "#f5f8f5", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-main-secondary-background": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Secondary Background"}, "bg": {"name": "Вторичен фон"}}}, "color-main-borders": {"type": "color", "group": "main", "default": "#ddd", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамки"}}}, "color-main-fields-text": {"separator": "true", "type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "Input Fields Text"}, "bg": {"name": "Полета за въвеждане - текст"}}}, "color-main-fields-background": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Input Fields Background"}, "bg": {"name": "Полета за въвеждане - фон"}}}, "color-main-fields-border": {"type": "color", "group": "main", "default": "#ddd", "translations": {"en": {"name": "Input Fields Border"}, "bg": {"name": "Полета за въвеждане - рамка"}}}, "color-main-fields-highlight": {"type": "color", "group": "main", "default": "#53e7e1", "translations": {"en": {"name": "Input Fields Highlight"}, "bg": {"name": "Полета за въвеждане - акцент"}}}, "color-main-price-text": {"separator": "true", "type": "color", "group": "main", "default": "#000", "translations": {"en": {"name": "Price"}, "bg": {"name": "Цена"}}}, "color-main-oldprice-text": {"type": "color", "group": "main", "default": "#f4454a", "translations": {"en": {"name": "Old Price"}, "bg": {"name": "Стара цена"}}}, "color-header-text": {"type": "color", "group": "header", "default": "#d9d9e1", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-header-background": {"type": "color", "group": "header", "default": "#000", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-header-border": {"type": "color", "group": "header", "default": "#4f4f4d", "translations": {"en": {"name": "Separator"}, "bg": {"name": "Сепаратор"}}}, "color-header-icons": {"separator": "true", "type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Icons"}, "bg": {"name": "Икони"}}}, "color-header-icons-hover": {"type": "color", "group": "header", "default": "#fb9f15", "translations": {"en": {"name": "Icons Hover"}, "bg": {"name": "Икони - акцент"}}}, "color-header-menu-text": {"separator": "true", "type": "color", "group": "header", "default": "#797976", "translations": {"en": {"name": "Menu Text"}, "bg": {"name": "Меню - текст"}}}, "color-header-menu-text-hover": {"type": "color", "group": "header", "default": "#d9d9e1", "translations": {"en": {"name": "Menu Text Hover"}, "bg": {"name": "Меню - текст - акцент"}}}, "color-header-submenu-text": {"separator": "true", "type": "color", "group": "header", "default": "#d9d9e1", "translations": {"en": {"name": "Submenu Text"}, "bg": {"name": "Подменю - текст"}}}, "color-header-submenu-background": {"type": "color", "group": "header", "default": "#081414", "translations": {"en": {"name": "Submenu Background"}, "bg": {"name": "Подменю - фон"}}}, "color-header-submenu-border": {"type": "color", "group": "header", "default": "#fb9f15", "translations": {"en": {"name": "Submenu Separator"}, "bg": {"name": "Подменю - сепаратор"}}}, "color-footer-text": {"type": "color", "group": "footer", "default": "#898989", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-footer-background": {"type": "color", "group": "footer", "default": "#000", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-footer-menu-text": {"separator": "true", "type": "color", "group": "footer", "default": "#fff", "translations": {"en": {"name": "Menu Text"}, "bg": {"name": "Меню - текст"}}}, "color-footer-menu-text-hover": {"type": "color", "group": "footer", "default": "#fb9f15", "translations": {"en": {"name": "Menu Text Hover"}, "bg": {"name": "Меню - текст - акцент"}}}, "color-footer-submenu-text": {"separator": "true", "type": "color", "group": "footer", "default": "#898989", "translations": {"en": {"name": "Submenu Text"}, "bg": {"name": "Подменю - текст"}}}, "color-footer-submenu-text-hover": {"type": "color", "group": "footer", "default": "#fb9f15", "translations": {"en": {"name": "Submenu Text Hover"}, "bg": {"name": "Подменю - текст - акцент"}}}, "color-footer-copyright-text": {"separator": "true", "type": "color", "group": "footer", "default": "#fff", "translations": {"en": {"name": "Copyright Text"}, "bg": {"name": "Авторско право - текст"}}}, "color-footer-copyright-border": {"type": "color", "group": "footer", "default": "#fff", "translations": {"en": {"name": "Copyright Separator"}, "bg": {"name": "Авторско право - сепаратор"}}}, "color-slider-text": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-slider-arrows": {"type": "color", "group": "slider", "default": "#797976", "translations": {"en": {"name": "Arrows"}, "bg": {"name": "Стрелки"}}}, "color-slider-arrows-hover": {"type": "color", "group": "slider", "default": "#fb9f15", "translations": {"en": {"name": "Arrows Hover"}, "bg": {"name": "Стрелки - акцент"}}}, "color-slider-dots": {"type": "color", "group": "slider", "default": "#797976", "translations": {"en": {"name": "Dots"}, "bg": {"name": "Точки"}}}, "color-slider-dots-hover": {"type": "color", "group": "slider", "default": "#fb9f15", "translations": {"en": {"name": "Dots Hover"}, "bg": {"name": "Точки - акцент"}}}, "color-showcase-category-text": {"type": "color", "group": "showcase_category", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-showcase-category-text-hover": {"type": "color", "group": "showcase_category", "default": "#fff", "translations": {"en": {"name": "Text Hover"}, "bg": {"name": "Текст - акцент"}}}, "color-showcase-category-background": {"type": "color", "group": "showcase_category", "default": "#53e7e1", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-showcase-category-background-hover": {"type": "color", "group": "showcase_category", "default": "#8cefeb", "translations": {"en": {"name": "Background Hover"}, "bg": {"name": "Фон - акцент"}}}, "color-product-title-text": {"type": "color", "group": "product", "default": "#000", "translations": {"en": {"name": "Title"}, "bg": {"name": "Заглавие"}}}, "color-product-price-text": {"type": "color", "group": "product", "default": "#000", "translations": {"en": {"name": "Price"}, "bg": {"name": "Цена"}}}, "color-product-oldprice-text": {"type": "color", "group": "product", "default": "#f4454a", "translations": {"en": {"name": "Old Price"}, "bg": {"name": "Стара цена"}}}, "color-product-background": {"type": "color", "group": "product", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-product-separator": {"type": "color", "group": "product", "default": "#ddd", "translations": {"en": {"name": "Separator"}, "bg": {"name": "Сепаратор"}}}, "color-product-overlay-background": {"type": "color", "group": "product", "default": "#fb9f15", "translations": {"en": {"name": "Overlay Background"}, "bg": {"name": "Подложка - фон"}}}, "color-product-overlay-icons": {"type": "color", "group": "product", "default": "#fff", "translations": {"en": {"name": "Overlay Icons"}, "bg": {"name": "Подложка - икони"}}}, "color-brand-title-text": {"type": "color", "group": "brand", "default": "#000", "translations": {"en": {"name": "Title"}, "bg": {"name": "Име"}}}, "color-brand-border": {"type": "color", "group": "brand", "default": "#ddd", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-testimonials-text": {"type": "color", "group": "testimonials", "default": "#000", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-testimonials-quotes-text": {"type": "color", "group": "testimonials", "default": "#fff", "translations": {"en": {"name": "Quotes Icon Text"}, "bg": {"name": "Кавички - текст"}}}, "color-testimonials-quotes-background": {"type": "color", "group": "testimonials", "default": "#53e7e1", "translations": {"en": {"name": "Quotes I<PERSON>"}, "bg": {"name": "Кавички - фон"}}}, "color-testimonials-dots": {"type": "color", "group": "testimonials", "default": "#797976", "translations": {"en": {"name": "Dots"}, "bg": {"name": "Точки"}}}, "color-testimonials-dots-hover": {"type": "color", "group": "testimonials", "default": "#53e7e1", "translations": {"en": {"name": "Dots Hover"}, "bg": {"name": "Точки - акцент"}}}, "color-breadcrumb-text": {"type": "color", "group": "breadcrumb", "default": "#333", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-breadcrumb-active-text": {"type": "color", "group": "breadcrumb", "default": "#969696", "translations": {"en": {"name": "Active Text"}, "bg": {"name": "Активен текст"}}}, "color-pagination-text": {"type": "color", "group": "pagination", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-pagination-disabled-text": {"type": "color", "group": "pagination", "default": "#b0b0af", "translations": {"en": {"name": "Disabled Text"}, "bg": {"name": "Неактивен текст"}}}, "color-pagination-active-text": {"type": "color", "group": "pagination", "default": "#fff", "translations": {"en": {"name": "Active Text"}, "bg": {"name": "Активен текст"}}}, "color-pagination-active-background": {"type": "color", "group": "pagination", "default": "#53e7e1", "translations": {"en": {"name": "Active Text Background"}, "bg": {"name": "Актвиен текст - фон"}}}, "color-button-text": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Primary - Text"}, "bg": {"name": "Основен - текст"}}}, "color-button-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Primary - Text Hover"}, "bg": {"name": "Основен - текст - акцент"}}}, "color-button-background": {"type": "color", "group": "buttons", "default": "#53e7e1", "translations": {"en": {"name": "Primary - Background"}, "bg": {"name": "Основен - фон"}}}, "color-button-background-hover": {"type": "color", "group": "buttons", "default": "#8cefeb", "translations": {"en": {"name": "Primary - <PERSON>ver"}, "bg": {"name": "Основен бутон - фон - акцент"}}}, "color-button-secondary-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#53e7e1", "translations": {"en": {"name": "Secondary - Text"}, "bg": {"name": "Вторичен - текст"}}}, "color-button-secondary-text-hover": {"type": "color", "group": "buttons", "default": "#8cefeb", "translations": {"en": {"name": "Secondary - Text Hover"}, "bg": {"name": "Вторичен - текст - акцент"}}}, "color-button-secondary-border": {"type": "color", "group": "buttons", "default": "#53e7e1", "translations": {"en": {"name": "Secondary - Border"}, "bg": {"name": "Вторичен - рамка"}}}, "color-button-secondary-border-hover": {"type": "color", "group": "buttons", "default": "#8cefeb", "translations": {"en": {"name": "Secondary - Border Hover"}, "bg": {"name": "Вторичен - рамка - акцент"}}}, "color-button-secondary-background": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Secondary - Background"}, "bg": {"name": "Вторичен - фон"}}}, "color-button-secondary-background-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Secondary - Background Hover"}, "bg": {"name": "Вторичен - фон - акцент"}}}, "color-button-active-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Active - Text"}, "bg": {"name": "Активен - текст"}}}, "color-button-active-background": {"type": "color", "group": "buttons", "default": "#53e7e1", "translations": {"en": {"name": "Active - Background"}, "bg": {"name": "Активен - фон"}}}, "color-label-new-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "New - Text"}, "bg": {"name": "Нов - текст"}}}, "color-label-new-background": {"type": "color", "group": "labels", "default": "#333", "translations": {"en": {"name": "New - Background"}, "bg": {"name": "Нов - фон"}}}, "color-label-sale-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Sale - Text"}, "bg": {"name": "Разпродажба - текст"}}}, "color-label-sale-background": {"type": "color", "group": "labels", "default": "#333", "translations": {"en": {"name": "Sale - Background"}, "bg": {"name": "Разпродажба - фон"}}}, "color-label-discount-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Discount - Text"}, "bg": {"name": "Отстъпка - текст"}}}, "color-label-discount-background": {"type": "color", "group": "labels", "default": "#fb9f15", "translations": {"en": {"name": "Discount - Background"}, "bg": {"name": "Отстъпка - фон"}}}, "color-label-leasing-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Leasing - Text"}, "bg": {"name": "Лизин<PERSON> - текст"}}}, "color-label-leasing-background": {"type": "color", "group": "labels", "default": "#fb9f15", "translations": {"en": {"name": "Leasing - Background"}, "bg": {"name": "Лизинг - фон"}}}, "color-label-featured-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Featured - Text"}, "bg": {"name": "Препоръчан - текст"}}}, "color-label-featured-background": {"type": "color", "group": "labels", "default": "#333", "translations": {"en": {"name": "Featured - Background"}, "bg": {"name": "Препоръчан - фон"}}}}}}