// NOTE: DO NOT EDIT THE FOLLOWING SECTION DIRECTLY! It is autogenerated via the `build-customizer-html` Grunt task using the customizer-variables.pug template.
each section in sections
  if section.customizable
    h2(id=section.id)= section.heading
    if section.docstring
      p!= section.docstring.html
    each subsection in section.subsections
      if subsection.heading
        h3(id=subsection.id)= subsection.heading
      div.row
        each variable, index in subsection.variables
          if index > 0 && index % 3 === 0
            div.clearfix
          div.col-xs-4
            label(for="input-" + variable.name)= variable.name
            - var helpId = "help-block-" + variable.name
            input.form-control(
              id="input-" + variable.name
              type="text"
              aria-describedby=variable.docstring ? helpId : undefined
              value=variable.defaultValue
              data-var=variable.name)
            if variable.docstring
              p.help-block(id=helpId)!= variable.docstring.html
// NOTE: DO NOT EDIT THE PRECEDING SECTION DIRECTLY! It is autogenerated via the `build-customizer-html` Grunt task using the customizer-variables.pug template.
