<div class="bs-docs-section">
  <h1 id="page-header" class="page-header">Page header</h1>

  <p>A simple shell for an <code>h1</code> to appropriately space out and segment sections of content on a page. It can utilize the <code>h1</code>'s default <code>small</code> element, as well as most other components (with additional styles).</p>
  <div class="bs-example" data-example-id="simple-page-header">
    <div class="page-header">
      <h1>Example page header <small>Subtext for header</small></h1>
    </div>
  </div>
{% highlight html %}
<div class="page-header">
  <h1>Example page header <small>Subtext for header</small></h1>
</div>
{% endhighlight %}
</div>
