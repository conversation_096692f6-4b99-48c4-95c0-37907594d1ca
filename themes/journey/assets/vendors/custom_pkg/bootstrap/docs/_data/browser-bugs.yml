-
  browser: >
    Microsoft Edge
  summary: >
    Native browser tooltip for `title` shows on first keyboard focus (in addition to custom tooltip component)
  upstream_bug: >
    Edge#6793560
  origin: >
    Bootstrap#18692

-
  browser: >
    Microsoft Edge
  summary: >
    Hovered element still remains in `:hover` state after scrolling away.
  upstream_bug: >
    Edge#5381673
  origin: >
    Bootstrap#14211

-
  browser: >
    Microsoft Edge
  summary: >
    When hovering over a `<select>` menu item, the cursor for the element underneath the menu is displayed.
  upstream_bug: >
    Edge#817822
  origin: >
    Bootstrap#14528

-
  browser: >
    Microsoft Edge
  summary: >
    CSS `border-radius` sometimes causes lines of bleed-through of the `background-color` of the parent element.
  upstream_bug: >
    Edge#3342037
  origin: >
    Bootstrap#16671

-
  browser: >
    Microsoft Edge
  summary: >
    `background` of `<tr>` is only applied to first child cell instead of all cells in the row
  upstream_bug: >
    Edge#5865620
  origin: >
    Bootstrap#18504

-
  browser: >
    Microsoft Edge
  summary: >
    `@-ms-viewport{width: device-width;}` has side-effect of making scrollbars auto-hide
  upstream_bug: >
    Edge#7165383
  origin: >
    Bootstrap#18543

-
  browser: >
    Microsoft Edge
  summary: >
    Background color from lower layer bleeds through transparent border in some cases
  upstream_bug: >
    Edge#6274505
  origin: >
    Bootstrap#18228

-
  browser: >
    Microsoft Edge
  summary: >
    Hovering over descendant SVG element fires `mouseleave` event at ancestor
  upstream_bug: >
    Edge#7787318
  origin: >
    Bootstrap#19670

-
  browser: >
    Firefox
  summary: >
    `.table-bordered` with an empty `<tbody>` is missing borders.
  upstream_bug: >
    Mozilla#1023761
  origin: >
    Bootstrap#13453

-
  browser: >
    Firefox
  summary: >
    If the disabled state of a form control is changed via JavaScript, the normal state doesn't return after refreshing the page.
  upstream_bug: >
    Mozilla#654072
  origin: >
    Bootstrap#793

-
  browser: >
    Firefox
  summary: >
    `focus` events should not be fired at the `document` object
  upstream_bug: >
    Mozilla#1228802
  origin: >
    Bootstrap#18365

-
  browser: >
    Firefox
  summary: >
    Wide floated table doesn't wrap onto new line
  upstream_bug: >
    Mozilla#1277782
  origin: >
    Bootstrap#19839

-
  browser: >
    Firefox
  summary: >
    Mouse sometimes not within element for purposes of `mouseenter`/`mouseleave` when it's within SVG elements
  upstream_bug: >
    Mozilla#577785
  origin: >
    Bootstrap#19670

-
  browser: >
    Firefox
  summary: >
    `position: absolute` element that's wider than its column renders differently than other browsers
  upstream_bug: >
    Mozilla#1282363
  origin: >
    Bootstrap#20161

-
  browser: >
    Firefox (Windows)
  summary: >
    Right border of `<select>` menu is sometimes missing when screen is set to uncommon resolution
  upstream_bug: >
    Mozilla#545685
  origin: >
    Bootstrap#15990

-
  browser: >
    Firefox (OS X & Linux)
  summary: >
    Badge widget causes bottom border of Tabs widget to unexpectedly not overlap
  upstream_bug: >
    Mozilla#1259972
  origin: >
    Bootstrap#19626

-
  browser: >
    Chrome (Android)
  summary: >
    Tapping on an `<input>` in a scrollable overlay doesn't scroll the `<input>` into view
  upstream_bug: >
    Chromium#595210
  origin: >
    Bootstrap#17338

-
  browser: >
    Chrome (OS X)
  summary: >
    Clicking above `<input type="number">` increment button flashes the decrement button.
  upstream_bug: >
    Chromium#419108
  origin: >
    Offshoot of Bootstrap#8350 & Chromium#337668

-
  browser: >
    Chrome
  summary: >
    CSS infinite linear animation with alpha transparency leaks memory.
  upstream_bug: >
    Chromium#429375
  origin: >
    Bootstrap#14409

-
  browser: >
    Chrome
  summary: >
    `:focus` `outline` style causes cursor to not be displayed when toggling a `readonly` `<input>` to read-write.
  upstream_bug: >
    Chromium#465274
  origin: >
    Bootstrap#16022

-
  browser: >
    Chrome
  summary: >
    `table-cell` borders not overlapping despite `margin-right: -1px`
  upstream_bug: >
    Chromium#534750
  origin: >
    Bootstrap#17438, Bootstrap#14237

-
  browser: >
    Chrome
  summary: >
    Clicking scrollbar in `<select multiple>` with overflowed options will select nearby `<option>`
  upstream_bug: >
    Chromium#597642
  origin: >
    Bootstrap#19810

-
  browser: >
    Chrome
  summary: >
    Don't make `:hover` sticky on touch-friendly webpages
  upstream_bug: >
    Chromium#370155
  origin: >
    Bootstrap#12832

-
  browser: >
    Chrome (Windows & Linux)
  summary: >
    Animation glitch when returning to inactive tab after animations occurred while tab was hidden.
  upstream_bug: >
    Chromium#449180
  origin: >
    Bootstrap#15298

-
  browser: >
    Safari
  summary: >
    Safari
  summary: >
    `rem` units in media queries should be calculated using `font-size: initial`, not the root element's `font-size`
  upstream_bug: >
    WebKit#156684
  origin: >
    Bootstrap#17403

-
  browser: >
    Safari (OS X)
  summary: >
    `px`, `em`, and `rem` should all behave the same in media queries when page zoom is applied
  upstream_bug: >
    WebKit#156687
  origin: >
    Bootstrap#17403

-
  browser: >
    Safari (OS X)
  summary: >
    Weird button behavior with some `<input type="number">` elements.
  upstream_bug: >
    WebKit#137269, Safari#18834768
  origin: >
    Bootstrap#8350,
    Normalize#283,
    Chromium#337668

-
  browser: >
    Safari (OS X)
  summary: >
    Small font size when printing webpage with fixed-width `.container`.
  upstream_bug: >
    WebKit#138192, Safari#19435018
  origin: >
    Bootstrap#14868

-
  browser: >
    Safari (iPad)
  summary: >
    `<select>` menu on iPad causes shifting of hit-testing areas
  upstream_bug: >
    WebKit#150079, Safari#23082521
  origin: >
    Bootstrap#14975

-
  browser: >
    Safari (iOS)
  summary: >
    `transform: translate3d(0,0,0);` rendering bug.
  upstream_bug: >
    WebKit#138162, Safari#18804973
  origin: >
    Bootstrap#14603

-
  browser: >
    Safari (iOS)
  summary: >
    Text input's cursor doesn't move while scrolling the page.
  upstream_bug: >
    WebKit#138201, Safari#18819624
  origin: >
    Bootstrap#14708

-
  browser: >
    Safari (iOS)
  summary: >
    Can't move cursor to start of text after entering long string of text into `<input type="text">`
  upstream_bug: >
    WebKit#148061, Safari#22299624
  origin: >
    Bootstrap#16988

-
  browser: >
    Safari (iOS)
  summary: >
    `display: block` causes text of temporal `<input>`s to become vertically misaligned
  upstream_bug: >
    WebKit#139848, Safari#19434878
  origin: >
    Bootstrap#11266, Bootstrap#13098

-
  browser: >
    Safari (iOS)
  summary: >
    Tapping on `<body>` doesn't fire `click` events
  upstream_bug: >
    WebKit#151933
  origin: >
    Bootstrap#16028

-
  browser: >
    Safari (iOS)
  summary: >
    `position:fixed` is incorrectly positioned when tab bar is visible on iPhone 6S+ Safari
  upstream_bug: >
    WebKit#153056
  origin: >
    Bootstrap#18859

-
  browser: >
    Safari (iOS)
  summary: >
    Tapping into an `<input>` within a `position:fixed` element scrolls to the top of the page
  upstream_bug: >
    WebKit#153224, Safari#24235301
  origin: >
    Bootstrap#17497

-
  browser: >
    Safari (iOS)
  summary: >
    `<body>` with `overflow:hidden` CSS is scrollable on iOS
  upstream_bug: >
    WebKit#153852
  origin: >
    Bootstrap#14839

-
  browser: >
    Safari (iOS)
  summary: >
    Scroll gesture in text field in `position:fixed` element sometimes scrolls `<body>` instead of scrollable ancestor
  upstream_bug: >
    WebKit#153856
  origin: >
    Bootstrap#14839

-
  browser: >
    Safari (iOS)
  summary: >
    Tapping from one `<input>` to another in an overlay can cause shaking/jiggling effect
  upstream_bug: >
    WebKit#158276
  origin: >
    Bootstrap#19927

-
  browser: >
    Safari (iOS)
  summary: >
    Modal with `-webkit-overflow-scrolling: touch` doesn't become scrollable after added text makes it taller
  upstream_bug: >
    WebKit#158342
  origin: >
    Bootstrap#17695

-
  browser: >
    Safari (iOS)
  summary: >
    Don't make `:hover` sticky on touch-friendly webpages
  upstream_bug: >
    WebKit#158517
  origin: >
    Bootstrap#12832

-
  browser: >
    Safari (iPad Pro)
  summary: >
    Rendering of descendants of `position: fixed` element gets clipped on iPad Pro in Landscape orientation
  upstream_bug: >
    WebKit#152637, Safari#24030853
  origin: >
    Bootstrap#18738
