/*=============================================================================*\
    TAGS
\*=============================================================================*/

._tags {
	color: @color-main-meta-text; /* theme */
	ul {
		list-style: none;
		font-size: 0;
		li {
			display: inline-block;
			font-size: @font-size-base;
			&:after {
				content: ', ';
				display: inline-block;
				margin-right: 6px;
			}
			&:last-child {
				&:after {
					display: none;
				}
			}
			a {
				display: inline-block;
				color: @color-main-highlight; /* theme */
				@media (min-width: @screen-desktop) {
					&:hover {
						color: @color-main-highlight; /* theme */
					}
				}
				& when (@rtl) {
					direction: ltr;
				}
			}
		}
	}
}