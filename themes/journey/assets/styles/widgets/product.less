/*=============================================================================*\
    PRODUCT
\*=============================================================================*/

/*  BOX
-------------------------------------------------------------------------------*/

._product {
	width: 100%;
	@media (min-width: @screen-desktop) {
		&:hover ._product-image {
			._product-image-thumb {
				._product-image-thumb-holder {
					img {
						opacity: .6 !important;
					}
				}
			}
			._product-quick-view {
				display: block;
			}
		}
	}
	._product-image {
		position: relative;
		border-width: 1px;
		border-style: solid;
		border-color: @color-main-borders; /* theme */
		._product-image-thumb {
			display: block;
			padding: 15px;
			._product-image-thumb-holder {
				display: block;
				height: 231px;
				position: relative;
				img {
					bottom: 0;
					height: auto;
					left: 0;
					margin: auto;
					max-height: 100%;
					max-width: 100%;
					position: absolute;
					right: 0;
					top: 0;
					width: auto;
				}
			}
		}
		._product-quick-view {
			.centerer(true, true);
			display: none;
			border-radius: 100%;
			width: 60px;
			height: 60px;
			background-color: @color-main-text; /* theme */
			@media (min-width: @screen-desktop) {
				&:hover {
					background-color: @color-main-highlight; /* theme */
				}
			}
			> span {
				.centerer(true, true);
				i {
					font-size: 30px;
					color: @color-main-secondary-text; /* theme */
				}
			}
		}
		._product-ribbon-holder {
			position: absolute;
			left: 0;
			top: 15px;
			min-width: 52px;
		}
		._product-ribbon {
			display: block;
			font-size: @font-size-base;
			font-weight: 600;
			text-transform: uppercase;
			text-align: center;
			padding: 5px 10px;
			& + ._product-ribbon {
				margin-top: 5px;
			}
			& when (@rtl) {
				white-space: nowrap;
			}
			&._product-ribbon-new {
				background-color: @color-label-new-background; /* theme */
				color: @color-label-new-text; /* theme */
			}
			&._product-ribbon-sale {
				background-color: @color-label-sale-background; /* theme */
				color: @color-label-sale-text; /* theme */
			}
			&._product-ribbon-featured {
				background-color: @color-label-featured-background; /* theme */
				color: @color-label-featured-text; /* theme */
			}
			&._product-discount {
				position: absolute;
				left: 0;
				bottom: 15px;
				min-width: 84px;
				padding: 5px 10px;
				background-color: @color-label-discount-background; /* theme */
				color: @color-label-discount-text; /* theme */
			}
			&._product-leasing {
				position: absolute;
				right: 0;
				bottom: 15px;
				padding: 5px 10px;
				background-color: @color-label-leasing-background; /* theme */
				color: @color-label-leasing-text; /* theme */
			}
		}
        ._product-ribbon-banner {
            position: absolute;
            z-index: 1;
            max-width: 25%;

            img {
                max-width: 100%;
                max-height: 100px;
            }
        }
	}
	._product-info {
		margin-top: 15px;
		._product-name {
			padding-top: 3px;
			padding-bottom: 1px;
			h3 {
				._h5();
				letter-spacing: normal;
				line-height: 1.3;
				height: 57px;
				overflow: hidden;
				a {
					display: block;
				}
			}
			& + ._product-description {
				margin-top: 10px;
			}
		}
		._product-description {
			display: none;
		}
		._product-meta {
		}

		._product-countdown {
			margin-top: 10px;
			text-align: left;

			._countdown-label {
				display: none;
			}
		}
		._product-options {
			.clearfix();
			margin-top: 15px;
			position: relative;
			._product-price {
				.centerer(false, true);
				.text-overflow();
				width: 50%;
				font-weight: 500;
				direction: ltr;
				color: @color-main-price-text; /* theme */
				& when (@rtl) {
					text-align: left;
				}
				._product-price-old {
					font-weight: 300;
					color: @color-main-oldprice-text; /* theme */
					-ltr-margin-left: 10px;
					-rtl-margin-right: 10px;
				}
			}
			._product-add {
				float: right;
			}
		}
	}
}
/*  LIST
-------------------------------------------------------------------------------*/

/*  Head */

._products-list-head {
	.flexy-row(30px);
	margin-bottom: 50px;
	._products-list-head-item {
		.flexy-col();
		._products-list-head-item-title {
			display: none;
			h2 {
				.text-overflow();
			}
		}
		._products-list-head-item-image {
			float: left;
			margin-right: 20px;
			._products-list-head-item-image-thumb {
				height: 130px;
				width: 130px;
				position: relative;
				margin: 10px;
				img {
					bottom: 0;
					height: auto;
					left: 0;
					margin: auto;
					max-height: 100%;
					max-width: 100%;
					position: absolute;
					right: 0;
					top: 0;
					width: auto;
				}
			}
		}
	}
	& + ._section-title {
		._section-separator();
	}
}
/*  Main */

._products-list {
	.clearfix();
	margin-left: -15px;
	margin-right: -15px;
	._product {
		float: left;
		margin-top: 60px;
		width: 33.3%;
		padding-left: 15px;
		padding-right: 15px;
		&:nth-child(-n+3) {
			margin-top: 0;
		}
		&:nth-child(3n+4) {
			clear: left;
		}
	}
}
/*  DETAILS
-------------------------------------------------------------------------------*/

._product-details-bg {
	position: relative;
	padding-bottom: 80px;
	._product-details-bg-img {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		overflow: hidden;
		._product-details-bg-img-src {
			position: absolute;
			top: -30px;
			bottom: -30px;
			left: -30px;
			right: -30px;
			background-position: center;
			background-repeat: no-repeat;
			background-size: cover;
			filter: blur(15px);
		}
	}
}
._product-details-wrapper {
	background-color: @color-main-secondary-background; /* theme */
	.clearfix();
	padding: 30px 15px;
	position: relative;
}
/*  Title */

._product-details-title {
	text-align: center;
	position: relative;
	padding-bottom: 14px;
	&:after {
		width: 60px;
		height: 2px;
		content: '';
		position: absolute;
		bottom: 0;
		.centerer(true, false);
		background-color: @color-main-highlight; /* theme */
	}
	h2 {
		text-transform: uppercase;
		font-weight: 500;
		color: @color-main-titles-text; /* theme */
	}
}
/*  Image */

._product-details-image {
	position: relative;
	._product-details-image-thumb {
		text-align: center;
		img {
			cursor: pointer;
		}
	}
	._product-details-ribbon-holder {
		position: absolute;
		left: 0;
		top: 15px;
		min-width: 52px;
	}
	._product-details-ribbon {
		display: block;
		font-size: @font-size-base;
		font-weight: 600;
		text-transform: uppercase;
		text-align: center;
		padding: 5px 10px;
		& + ._product-details-ribbon {
			margin-top: 5px;
		}
		& when (@rtl) {
			white-space: nowrap;
		}
		&._product-details-ribbon-new {
			background-color: @color-label-new-background; /* theme */
			color: @color-label-new-text; /* theme */
		}
		&._product-details-ribbon-sale {
			background-color: @color-label-sale-background; /* theme */
			color: @color-label-sale-text; /* theme */
		}
		&._product-details-ribbon-featured {
			background-color: @color-label-featured-background; /* theme */
			color: @color-label-featured-text; /* theme */
		}
		&._product-details-discount {
			position: absolute;
			left: 0;
			bottom: 15px;
			min-width: 84px;
			padding: 5px 10px;
			background-color: @color-label-discount-background; /* theme */
			color: @color-label-discount-text; /* theme */
		}
		&._product-details-leasing {
			position: absolute;
			right: 0;
			bottom: 15px;
			padding: 5px 10px;
			background-color: @color-label-leasing-background; /* theme */
			color: @color-label-leasing-text; /* theme */
		}
	}
    ._product-details-ribbon-banner {
        position: absolute;
        z-index: 1;
    }
}
/*  Gallery */

._product-details-gallery {
	margin-top: 20px;
	padding-top: 10px;
	position: relative;
	ul {
		.clearfix();
		list-style: none;
		height: 0;
		width: ~'calc(100% - 60px)';
		li {
			float: left;
			width: 74px;
			padding: 5px;
			a {
				display: block;
				height: 62px;
				position: relative;
				img {
					bottom: 0;
					height: auto;
					left: 0;
					margin: auto;
					max-height: 100%;
					max-width: 100%;
					position: absolute;
					right: 0;
					top: 0;
					width: auto;
				}
			}
			& + li {
				margin-left: 10px;
			}
		}
		.slick-arrow {
			position: absolute;
			background-color: @color-button-background; /* theme */
			color: @color-button-text; /* theme */
			width: 20px;
			height: 20px;
			border-radius: 100%;
			z-index: 10;
			left: auto;
			@media (min-width: @screen-desktop) {
				&:hover {
					color: @color-button-text-hover; /* theme */
					background-color: @color-button-background-hover; /* theme */

				}
			}
			&:before {
				font-family: FontAwesome;
				font-size: @font-size-xsmall;
				opacity: 1;
				.centerer(true, true);
			}
			&.slick-prev {
				right: -35px;
				&:before {
					content: "\f060";
				}
			}
			&.slick-next {
				right: -60px;
				&:before {
					content: "\f061";
				}
			}
		}
	}
}
/*  Price */

._product-details-price {
	._section-separator-small();
	text-align: center;
	._product-details-price-bar {
		> span {
			display: inline-block;
			&._product-details-price-new {
				font-size: @font-size-product-details-price;
				font-weight: 400;
				color: @color-main-price-text;
				& + ._product-details-price-old {
					margin-left: 10px;
				}
				& when (@rtl) {
					direction: ltr;
				}
			}
			&._product-details-price-old {
				font-size: @font-size-xxlarge;
				font-weight: 600;
				color: @color-main-oldprice-text; /* theme */
				> i {
					font-style: normal;
					text-decoration: line-through;
				}
			}
		}
	}
	._product-details-choose {
		display: none;
		color: @color-main-meta-text; /* theme */
	}
	._product-details-sku {
		> i {
			font-style: normal;
		}
	}
}
/*  Parameters */

._product-details-parameters {
	._section-separator-small();
	._form {
		display: block;
		._form-row {
			display: block;
			margin: 0;
			._form-col {
				display: block;
				padding: 0;
				.clearfix();
				&-bottom {
					._section-separator-small();
					border-top-width: 1px;
					border-top-style: solid;
					border-top-color: @color-main-borders; /* theme */
					padding-top: 30px;
					._form-actions {
						text-align: left;
						._button {
							width: auto;
							float: none;
							._figure-stack {
								._figure-stack-label {
									padding-left: 0;
								}
							}
						}
					}
				}
			}
			& + ._form-row {
				margin-top: 15px;
			}
		}
	}
	.help-block-error {
		overflow: hidden;
		padding-left: 10px;
	}
	._product-out-of-stock {
		font-size: @font-size-small; /* theme */
		color: @color-main-meta-text; /* theme */
	}
	.input-group {
		display: table;
		width: 140px;
	}
	& + ._product-details-leasing {
		margin-top: 30px;
	}
}

._parameter-select-label,
._form-col-quantity ._label {
	float: left;
	color: @color-main-titles-text; /* theme */
	text-transform: uppercase;
	margin-top: 12px;
	padding-right: 15px;
}

._parameter-select-values {
	float: left;
	width: 180px;

	.select2-search-choice-close {
		display: none;
	}
}

._parameter-color-label,
._parameter-image-label {
	color: @color-main-titles-text; /* theme */
	text-transform: uppercase;
	margin-bottom: 5px;
}

._parameter-radio-values {
	margin-bottom: -3px;
	.clearfix();
}

._parameter-radio-value {
	float: left;
	display: table;
	height: 36px;
	min-width: 44px;
	margin-right: 3px;
    margin-bottom: 3px;

    ._radio {
    	display: table-cell;
    	vertical-align: middle;
		border: 1px solid;
		border-color: @color-main-borders; /* theme */
		padding: 5px;
	    text-align: center;
	    font-weight: normal;
	    font-size: 13px;

        &.active {
        	border-width: 2px;
        	padding: 4px;
			border-color: @color-main-highlight; /* theme */
        }

        .radio {
        	display: none;
        }
    }
}

._parameter-image-values {
    margin-left: -1%;
    margin-bottom: -1%;
    .clearfix();
}

._parameter-image-value {
    float: left;
    width: 24%;
    max-width: 100px;
    border: 1px solid @color-main-borders; /* theme */
    margin-left: 1%;
    margin-bottom: 1%;
    word-break: break-word;

    &.active {
        border-width: 2px;
        border-color: @color-main-highlight; /* theme */
    }

    ._radio {
        display: block;
        height: 0;
        padding: 0;
        padding-bottom: 100%;
        font-size: @font-size-small; /* theme */
        position: relative;
        overflow: hidden;

        .radio {
            top: 5px;
            left: 5px;
        }

        .radio-label {
            display: inline-block;
            padding-top: 4px;
            padding-left: 30px;
            padding-right: 5px;
        }

        img {
            .centerer(true, true);

            ~ .radio,
            ~ .radio-label {
                display: none;
            }
        }
    }
}

._parameter-color-values {
    margin-right: -5px;
    margin-bottom: -5px;
    .clearfix();
}

._parameter-color-value {
    float: left;
    margin-right: 5px;
    margin-bottom: 5px;

    &.active {
        ._radio {
            border-color: @color-main-highlight; /* theme */
        }
    }

    ._radio {
        display: block;
        width: 34px;
        height: 34px;
        border: 2px solid transparent;
        padding: 0;
        margin: 0;
        font-size: 0;
        line-height: 0;

        .radio {
            display: none;
        }

        .radio-label {
            display: none;
        }
    }
}

/*  Meta */

._product-details-meta {
	ul {
		list-style: none;
		display: table;
		width: 100%;
		table-layout: fixed;
		li {
			display: table-row;
			> span {
				display: table-cell;
				vertical-align: top;
				&._product-details-meta-title {
					&:after {
						content: ':';
					}
				}
				&._product-details-meta-value {
					text-align: right;
					& when (@rtl) {
						direction: ltr;
					}
				}
			}
		}
	}
	& + ._product-details-properties {
		margin-top: 15px;
	}
}
/*  Properties */

._product-details-properties {
	margin-top: 20px;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: @color-main-borders; /* theme */
	padding-bottom: 30px;
	ul {
		list-style: none;
		columns: 2;
		column-gap: 30px;
		li {
			position: relative;
			padding-left: 23px;
			break-inside: avoid;
			padding-top: 10px;
			&:before {
				position: absolute;
				left: 0;
				top: 10px;
				font-family: FontAwesome;
				content: "\f00c";
				color: @color-main-highlight; /* theme */
			}
			> span {
				display: inline-block;
			}
		}
	}
}
/*  Description */

._product-details-desription {
	font-size: 16px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
	h1, ._h1, h2, ._h2, h3, ._h3, h4, ._h4, h5, ._h5, h6, ._h6, p, ol, ul, pre {
		line-height: 1.88;
	}
}

/*  Leasing */
._product-details-leasing {
	._figure-stack {
		._figure-stack-icon {
			color: @color-main-highlight; /* theme */
		}
	}
}

._product-details-share {
	._section-separator-small();
}
._product-details-tags {
	._section-separator-small();
}
/*  DETAILS - POPUP
-------------------------------------------------------------------------------*/

.modal-dialog.product-details {
	width: 1180px;
	.modal-body {
		padding-left: 5px;
		padding-right: 5px;
	}
}