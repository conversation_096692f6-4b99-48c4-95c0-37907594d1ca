/*=============================================================================*\
    NAVIGATION
\*=============================================================================*/

.js-header-nav-toggle {
	&.active {
		.fa-bars {
			&:before {
				content:"\f00d";
			}
		}
	}
}

/*  MAIN
-------------------------------------------------------------------------------*/

._navigation {
	._navigation-hamburger {
		display: none;
	}
	ul {
		list-style: none;
	}
	& > ul {
		font-weight: 500;
		text-align: center;
		text-transform: uppercase;
		display: block;
		> li {
			display: inline-block;
			margin: 0 15px;
			font-size: 0;
			vertical-align: top;
			@media (min-width: @screen-desktop) {
				&:hover {
					> ._figure-stack {
						color: @color-header-menu-text-hover; /* theme */
					}
				}
			}
			&.active {
				> ._figure-stack {
					color: @color-header-menu-text-hover; /* theme */
				}
			}
			&.cc-open {
				> ul {
					display: block;
				}
			}
			&.item-collapse {
				@media (min-width: @screen-desktop) {
					&:hover {
						> ul {
							display: block;
						}
						> ._figure-stack {
							&:after {
								display: block;
							}
						}
					}
				}
			}
			> ._figure-stack {
				font-size: @font-size-nav;
				color: @color-header-menu-text; /* theme */
                padding: 25px 0;
				position: relative;
				&:after {
					.centerer(true, false);
					border-left: 7px solid transparent;
					border-right: 7px solid transparent;
					border-bottom-width: 7px;
					border-bottom-style: solid;
					border-bottom-color: @color-header-submenu-border; /* theme */
					height: 0;
					width: 0;
					position: absolute;
					bottom: 0;
					display: none;
					content: '';
				}
				._figure-stack-icon {
					font-size: 0;
					display: none;
					&:before {
						font-size: @font-size-nav-icon;
						display: inline-block;
					}
					&.collapse-icon-active {
						color: @color-main-highlight; /* theme */
					}
				}
			}
			> ul {
				.clearfix();
				display: none;
				position: absolute;
				font-size: @font-size-base;
				z-index: 10;
				background-color: @color-header-submenu-background; /* theme */
				border-top-width: 2px;
				border-top-style: solid;
				border-top-color: @color-header-submenu-border; /* theme */
				text-align: left;
				left: 0;
				right: 0;
				padding: 35px;
				padding-top: 7px;
				> li {
					float: left;
					width: 16.66%;
					font-size: 0;
					padding-top: 28px;
					&:nth-child(6n+7) {
						clear: left;
					}
					._figure-stack {
						display: inline-block;
						font-size: @font-size-base;
						color: @color-header-submenu-text; /* theme */
						._figure-stack-icon {
							display: none;
						}
					}
					ul {
						padding-top: 22px;
						text-transform: none;
						li {
							._figure-stack {
								margin-top: 4px;
								display: inline-block;
								color: @color-header-menu-text; /* theme */
								@media (min-width: @screen-desktop) {
									&:hover {
										color: @color-header-menu-text-hover; /* theme */
									}
								}

							}
						}
					}
				}
			}
		}
	}
}

/*  LINKS
-------------------------------------------------------------------------------*/

._navigation-links {
	ul {
		list-style: none;
		font-size: @font-size-small;
		li {
			display: inline-block;
			& + li {
				margin-left: 16px;
			}
		}
	}
}

/*  FOOTER
-------------------------------------------------------------------------------*/

._navigation-footer {
	> ul {
		.clearfix();
		list-style: none;
		text-transform: uppercase;
		margin-left: -10px;
		> li {
			width: 25%;
			float: left;
			font-size: 0;
			padding-top: 15px;
			padding-left: 10px;
			> a {
				font-size: @font-size-large;
				color: @color-footer-menu-text; /* theme */
				@media (min-width: @screen-desktop) {
					&:hover {
						color: @color-footer-menu-text-hover; /* theme */
					}
				}
				.collapse-icon {
					display: none;
				}
			}
			&:nth-child(4n+5) {
				clear: left;
			}
			ul {
				list-style: none;
				color: @color-footer-submenu-text; /* theme */
				text-transform: none;
				li {
					font-size: 0;
					padding-top: 15px;
					a {
						font-size: @font-size-base;
						@media (min-width: @screen-desktop) {
							&:hover {
								color: @color-footer-submenu-text-hover; /* theme */
							}
						}
					}
				}
			}
		}
	}
}