/*=============================================================================*\
    SLIDER
\*=============================================================================*/

/*  OWLCAROUSEL
-------------------------------------------------------------------------------*/

.owl-carousel .animated {
	-webkit-animation-duration: 1000ms;
	animation-duration: 1000ms;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
}
.owl-carousel .owl-animated-in {
	z-index: 0;
}
.owl-carousel .owl-animated-out {
	z-index: 1;
}
.owl-carousel .fadeOut {
	-webkit-animation-name: fadeOut;
	animation-name: fadeOut;
}
@-webkit-keyframes fadeOut {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}
@keyframes fadeOut {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}
.owl-height {
	-webkit-transition: height 500ms ease-in-out;
	-moz-transition: height 500ms ease-in-out;
	-ms-transition: height 500ms ease-in-out;
	-o-transition: height 500ms ease-in-out;
	transition: height 500ms ease-in-out;
}
.owl-carousel {
	display: none;
	width: 100%;
	-webkit-tap-highlight-color: transparent; /* theme */
	position: relative;
	z-index: 1;
}
.owl-carousel .owl-stage {
	position: relative;
	-ms-touch-action: pan-Y;
}
.owl-carousel .owl-stage:after {
	content: ".";
	display: block;
	clear: both;
	visibility: hidden;
	line-height: 0;
	height: 0;
}
.owl-carousel .owl-stage-outer {
	position: relative;
	overflow: hidden;
	-webkit-transform: translate3d(0px, 0px, 0px);
}
.owl-carousel .owl-item {
	position: relative;
	min-height: 1px;
	float: left;
	-webkit-backface-visibility: hidden;
	-webkit-tap-highlight-color: transparent; /* theme */
	-webkit-touch-callout: none;
}
.owl-carousel .owl-item img {
	display: block;
	width: 100%;
	-webkit-transform-style: preserve-3d;
}
.owl-carousel .owl-nav.disabled, .owl-carousel .owl-dots.disabled {
	display: none;
}
.owl-carousel .owl-nav .owl-prev, .owl-carousel .owl-nav .owl-next, .owl-carousel .owl-dot {
	cursor: pointer;
	cursor: hand;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.owl-carousel.owl-loaded {
	display: block;
}
.owl-carousel.owl-loading {
	opacity: 0;
	display: block;
}
.owl-carousel.owl-hidden {
	opacity: 0;
}
.owl-carousel.owl-refresh .owl-item {
	display: none;
}
.owl-carousel.owl-drag .owl-item {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.owl-carousel.owl-grab {
	cursor: move;
	cursor: -webkit-grab;
	cursor: -o-grab;
	cursor: -ms-grab;
	cursor: grab;
}
.owl-carousel.owl-rtl {
	direction: rtl;
}
.owl-carousel.owl-rtl .owl-item {
	float: right;
}
.no-js .owl-carousel {
	display: block;
}
.owl-carousel .owl-item .owl-lazy {
	opacity: 0;
	-webkit-transition: opacity 400ms ease;
	-moz-transition: opacity 400ms ease;
	-ms-transition: opacity 400ms ease;
	-o-transition: opacity 400ms ease;
	transition: opacity 400ms ease;
}
.owl-carousel .owl-item img {
	transform-style: preserve-3d;
}
.owl-carousel .owl-video-wrapper {
	position: relative;
	height: 100%;
	background: #000;
}
.owl-carousel .owl-video-play-icon {
	position: absolute;
	height: 80px;
	width: 80px;
	left: 50%;
	top: 50%;
	margin-left: -40px;
	margin-top: -40px;
	cursor: pointer;
	z-index: 1;
	-webkit-backface-visibility: hidden;
	-webkit-transition: scale 100ms ease;
	-moz-transition: scale 100ms ease;
	-ms-transition: scale 100ms ease;
	-o-transition: scale 100ms ease;
	transition: scale 100ms ease;
}
.owl-carousel .owl-video-play-icon:hover {
	-webkit-transition: scale(1.3, 1.3);
	-moz-transition: scale(1.3, 1.3);
	-ms-transition: scale(1.3, 1.3);
	-o-transition: scale(1.3, 1.3);
	transition: scale(1.3, 1.3);
}
.owl-carousel .owl-video-playing .owl-video-tn, .owl-carousel .owl-video-playing .owl-video-play-icon {
	display: none;
}
.owl-carousel .owl-video-tn {
	opacity: 0;
	height: 100%;
	background-position: center center;
	background-repeat: no-repeat;
	-webkit-background-size: contain;
	-moz-background-size: contain;
	-o-background-size: contain;
	background-size: contain;
	-webkit-transition: opacity 400ms ease;
	-moz-transition: opacity 400ms ease;
	-ms-transition: opacity 400ms ease;
	-o-transition: opacity 400ms ease;
	transition: opacity 400ms ease;
}
.owl-carousel .owl-video-frame {
	position: relative;
	z-index: 1;
	height: 100%;
	width: 100%;
}
.owl-carousel {
    .owl-item {
        .owl-video-tn {
            background-size: cover;
            padding-bottom: 56.25%;
        }

        .owl-video-play-icon {
            background-image: url(../img/png/video-play.png);
            background-size: contain;
        }
    }
}

/*  SLIDER
-------------------------------------------------------------------------------*/

._slider {
	&.slider-loaded {
		.slides {
			height: auto;
			overflow: visible;
		}
		.loader-container {
			display: none;
		}
	}
	&.slider-video-only {
		.owl-controls {
			display: none;
		}
	}
	.slides {
		overflow: hidden;
		height: 434px;
		&.owl-loaded {
			height: auto;
			overflow: visible;
		}
		.slide {
			position: relative;
			&.slide-link {
				cursor: pointer;
			}
			.slide-text {
				position: absolute;
				top: 0;
				left: 0;
				bottom: 0;
				right: 0;
				.slide-html {
					display: table-cell;
					color: @color-slider-text; /* theme */
					padding: 40px 102px 70px;
					text-shadow: 1px 1px rgba(0, 0, 0, 0.4);
					font-size: @font-size-slider-text;
					*:not(br) {
						margin-top: 30px;
						line-height: 1.7;
						&:first-child {
							margin-top: 0;
						}
					}
					h1, h2, h3 {
						font-size: @font-size-slider-title;
						font-family: @font-family-base;
						font-weight: 400;
						text-transform: uppercase;
						line-height: 1.2;
						position: relative;
						padding-bottom: 25px;
						color: @color-slider-text; /* theme */
					}
                    img {
                        width: auto;
                    }
					._first-word {
						color: @color-main-highlight; /* theme */;
					}
				}
				.slide-html-tablet,
				.slide-html-mobile {
					display: none;
				}
				&.topleft {
					.slide-html {
						vertical-align: top;
						text-align: left;
					}
				}
				&.topright {
					.slide-html {
						vertical-align: top;
						text-align: right;
					}
				}
				&.topcenter {
					.slide-html {
						vertical-align: top;
						text-align: center;
					}
				}
				&.middleleft {
					.slide-html {
						vertical-align: middle;
						text-align: left;
					}
				}
				&.middleright {
					.slide-html {
						vertical-align: middle;
						text-align: right;
					}
				}
				&.middlecenter {
					.slide-html {
						vertical-align: middle;
						text-align: center;
					}
				}
				&.bottomleft {
					.slide-html {
						vertical-align: bottom;
						text-align: left;
					}
				}
				&.bottomright {
					.slide-html {
						vertical-align: bottom;
						text-align: right;
					}
				}
				&.bottomcenter {
					.slide-html {
						vertical-align: bottom;
						text-align: center;
					}
				}
				.slide-text-outer,
				.slide-text-inner {
					height: 100%;
				}
				.slide-text-wrapper {
					display: table;
					width: 1140px;
					margin-left: auto;
					margin-right: auto;
					height: 100%;
				}
			}
		}
		.owl-controls {
			.owl-nav {
				font-size: @font-size-slider-nav;
				color: @color-slider-arrows; /* theme */
				.owl-prev,
				.owl-next {
					position: absolute;
					top: 0;
					bottom: 0;
					width: 82px;
					&.disabled {
						display: none !important;
					}
					.glyphicon {
						font-family: FontAwesome;
						.centerer(true, true);
					}
					@media (min-width: @screen-desktop) {
						&:hover {
							color: @color-slider-arrows-hover; /* theme */
						}
					}
				}
				.owl-prev {
					left: 0;
					.glyphicon {
						&:before {
							content: '\f104';
						}
					}
				}
				.owl-next {
					right: 0;
					.glyphicon {
						&:before {
							content: '\f105';
						}
					}
				}
			}
			.owl-dots {
				.clearfix();
				.centerer(true, false);
				bottom: 20px;
				padding-right: 1px; // bug fix
				.owl-dot {
					.shape-circle(15px);
					float: left;
					background-color: @color-slider-dots; /* theme */
					&.active {
						background-color: @color-slider-dots-hover; /* theme */
					}
					& + .owl-dot {
						margin-left: 15px;
					}
				}
			}
		}
	}
}