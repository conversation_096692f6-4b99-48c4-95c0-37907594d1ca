/*=============================================================================*\
    PAGINATION
\*=============================================================================*/

._pagination {
	._section-separator();
	font-size: 0;
	.pagination {
		font-size: @font-size-base; /* theme */
		border-radius: 0;
		border: none;
		li {
			display: inline-block;
			& + li {
				margin-left: 25px;
			}
			a {
				color: @color-pagination-text; /* theme */
				background-color: transparent; /* theme */
				border-radius: 0;
				border: none;
				padding: 0;
				margin-left: 0;
				@media (min-width: @screen-desktop) {
					&:hover {
						color: @color-main-highlight; /* theme */
					}
				}
			}
			&.active {
				&:not(.disabled) {
					position: relative;
					&:before {
						.shape-circle(30px);
						.centerer(true, true);
						position: absolute;
						background-color: @color-pagination-active-background; /* theme */
						content: '';
					}
					a {
						color: @color-pagination-active-text; /* theme */
						background-color: transparent; /* theme */
					}
				}
			}
			&.disabled {
				a {
					color: @color-pagination-disabled-text; /* theme */
				}
			}
		}
	}
}