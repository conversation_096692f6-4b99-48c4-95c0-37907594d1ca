/*=============================================================================*\
    SEARCH FORM
\*=============================================================================*/

._search-trigger-holder {
	display: inline-table;
	height: 65px;
}

._search-trigger {
	line-height: 0;
	margin-left: 25px;
	display: table-cell;
	vertical-align: middle;
	text-align: center;
}

._modal-search {
	bottom: 0 !important;
}

.modal-search {
	._popup {
		padding: 20px;
	}

	._field-icon {
		display: block;
	}

	._field {
		outline: 0; 
	}
}

.scroll-wrapper {
	background-color: @color-main-secondary-background; /* theme */
	border: 0;
	border-radius: 0;
	box-shadow: inset 0 0 0 1px @color-main-borders; /* theme */
	top: 45px;
	max-height: 250px;

	.ui-autocomplete {
		top: 0 !important;
	}

	.ui-menu-item {
		display: block;
		padding: 0;
		list-style: none;

		&:first-child {
			padding-top: 7px;
		}

		&:last-child {
			padding-bottom: 7px;
		}

		a {
			display: block;
			border-radius: 0;
			color: @color-main-text; /* theme */
			padding: 7px 15px;
			border: 0;
			margin: 0 !important;
		}

		.ui-state-focus {
			background-color: @color-main-highlight; /* theme */
			color: @color-button-text; /* theme */
		}
	}

	._autocomplete-item {
		display: table;
		table-layout: fixed;
		width: 100%;
	}

	._autocomplete-item-image {
		display: table-cell;
		vertical-align: middle;
		width: 40px;
		height: 40px;

		img {
			max-width: 40px;
			max-height: 40px;
			vertical-align: middle;
		}
	}

	._autocomplete-item-info {
		padding-left: 10px;
		display: table-cell;
		vertical-align: middle;
	}

	.scroll-element {
		z-index: 101;

	    .scroll-element_outer {
	        border-radius: 0 !important;
	        width: 10px !important;
	    	opacity: 1 !important;
	    }

	    .scroll-element_track {
	        background-color: @color-main-borders; /* theme */
	    }

	    .scroll-element_size {
	        border-radius: 0 !important;
	        background-color: transparent;
	    }

	    .scroll-bar {
	        border-radius: 0 !important;
	        width: 10px !important;
	        background-color: @color-main-highlight; /* theme */
	    }
	}
}