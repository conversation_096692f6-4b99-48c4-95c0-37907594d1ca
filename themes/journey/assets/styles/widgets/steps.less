/*=============================================================================*\
    CHECKOUT STEPS
\*=============================================================================*/

._checkout-steps {
	ul {
		display: table;
		width: 100%;
		table-layout: fixed;
		counter-reset: num-counter;
		height: 42px;
		li {
			display: table-cell;
			counter-increment: num-counter;
			text-align: center;
			position: relative;
			._checkout-step-name {
				font-size: @font-size-small; /* theme */
				padding: 57px 15px 0;
				display: block;
				&:before {
					.centerer(true, false);
					font-size: @font-size-base; /* theme */
					position: absolute;
					background-color: @color-main-fields-background; /* theme */
					width: 42px;
					height: 42px;
					line-height: 42px;
					content: counter(num-counter);
					top: 0;
					border-radius: 100%;
					text-align: center;
				}
			}
			&.completed {
				._checkout-step-name {
					color: @color-main-meta-text; /* theme */
					&:before {
						background-color: @color-main-borders; /* theme */
					}
				}
			}
			&.active {
				._checkout-step-name {
					&:before {
						background-color: @color-main-highlight; /* theme */
						color: @color-main-secondary-text; /* theme */
					}
				}	
			}
		}
	}
}