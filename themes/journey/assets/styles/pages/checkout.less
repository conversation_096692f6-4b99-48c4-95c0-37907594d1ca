/*=============================================================================*\
    CHECKOUT
\*=============================================================================*/

/*  MAIN
-------------------------------------------------------------------------------*/

._checkout-main {
	._section-title {
		._section-separator();
	}

    ._checkout-shipping-providers-controls {
        font-size: 0;
        position: relative;
        margin-bottom: 20px;
        &:after {
            content: '';
            border-bottom-width: 1px;
            border-bottom-style: solid;
            border-bottom-color: @color-button-background; /* theme */
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 2;
        }
        ._radio {
            background-color: @color-main-background; /* theme */
            padding: 10px 15px;
            text-align: center;
            position: relative;
            z-index: 1;
            &.active {
            	background-color: @color-button-background; /* theme */
                color: @color-button-text; /* theme */
            }
            .radio {
                display: none;
            }
            > span {
                font-size: @font-size-base;
            }
            & + ._radio {
                margin-left: 10px
            }
        }
    }
	._button {
		&:extend(._button._button-secondary all);
	}
    ._select-billing-address {
        margin-top: 15px;
    }
}

/*  SIDEBAR
-------------------------------------------------------------------------------*/

._checkout-sidebar {}

/*  ACCOUNT FORMS & TABS
-------------------------------------------------------------------------------*/

._checkout-account-tabs {
	& + ._checkout-account-forms {
		margin-top: 15px;
	}
}
._checkout-account-forms {
	._meta-links {
		display: none;
	}
}

/*  SHIPPING / BILLING ADDRESS
-------------------------------------------------------------------------------*/

._checkout-verify-addresses {
	.flexy-row(30px);
	._section-separator();
	._checkout-verify-address-row {
		.flexy-col();
		._checkout-verify-address {
			border-width: 1px;
			border-style: solid;
			border-color: @color-main-borders; /* theme */
			padding: 15px 20px 20px;
			._checkout-verify-address-title {
				._h5();
				letter-spacing: normal;
				& + ._checkout-verify-address-content {
					margin-top: 10px;
				}
			}
			._checkout-verify-address-content {
				._button {
					margin-top: 15px;
					white-space: nowrap;
				}
			}
		}
	}
}

/*  RETURN
-------------------------------------------------------------------------------*/

._checkout-return {
	text-align: center;
	._checkout-return-icon {
		font-size: @font-size-thanks-icon; /* theme */
		line-height: 1;
		& + ._textbox {
			._section-separator();
		}
	}
}

/*  EXPRESS
-------------------------------------------------------------------------------*/

._checkout-express {
	[id*='step-'] {
		&.step-disabled {
			height: 0;
			margin: 0;
			padding: 0;
			opacity: 0;
			overflow: hidden;
			position: relative;
			&:before {
				position: absolute;
				top: 0;
				bottom: 0;
				left: 0;
				right: 0;
				content: '';
				z-index: 1000;
				background-color: rgba(255, 255, 255, .7);
				cursor: not-allowed;
			}
		}
		background-color: @color-main-secondary-background; /* theme */
		padding: 20px 30px 30px;
		._section-title {
			position: relative;
            margin-top: 0;
            margin-bottom: 30px;
			padding-bottom: 15px;
			border-bottom-width: 1px;
			border-bottom-style: solid;
			border-bottom-color: @color-main-borders; /* theme */
			h2, h3 {
				font-size: @font-size-xxxlarge;
				font-weight: 400;
				text-transform: none;
				text-align: left;
			}
		}
		& + [id*='step-'] {
			._section-separator-medium();
		}
		&[id*='address'] {
			._address-book {
				& + ._address-book {
					margin-top: 30px;
					._section-title {
						padding-left: 0;
						padding-right: 0;
						padding-top: 0;
					}
					._address-book-controls {
						margin-top: 20px;
					}
				}
			}
		}
		&[id*='verify'] {
			._section-separator-medium();
			._checkout-main {
				& + ._section-title {
					._section-separator-small();
				}
                > ._section-title {
                    margin-top: 30px;
                }
			}
		}
	}
	._checkout-sidebar {}
	[class*='col-'] {
		position: static;
	}
}

._leasing-form {
    ._leasing-creditor-data-table {
        margin-top: 30px !important;
    }
}

/*  ADDRESS BOOK LIST
-------------------------------------------------------------------------------*/

.page-checkout {
	._address-book-list {
		padding-bottom: 15px;
	}
}
