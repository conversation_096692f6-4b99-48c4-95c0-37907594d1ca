/*=============================================================================*\
    TYPOGRAPHY
\*=============================================================================*/

/*  GENERAL
-------------------------------------------------------------------------------*/

p, ul, ol, dd, figcaption, pre, address {
	line-height: 1.618;
}
/*  HEADINGS
-------------------------------------------------------------------------------*/

h1, h2, h3, h4, h5, h6 {
	font-family: @font-family-titles;
	color: @color-main-titles-text; /* theme */
	line-height: 1.4;
	font-weight: 600;
	@media (min-width: @screen-desktop) {
		a {
			color: @color-main-titles-text; /* theme */
			&:hover {
				color: @color-main-highlight; /* theme */
			}
		}
	}
}

h1, ._h1 {
	font-size: @font-size-heading-1;
	font-weight: 800;
}
h2, ._h2 {
	font-size: @font-size-heading-2;
}
h3, ._h3 {
	font-size: @font-size-heading-3;
}
h4, ._h4 {
	font-size: @font-size-heading-4;
}
h5, ._h5 {
	font-size: @font-size-heading-5;
}
h6, ._h6 {
	font-size: @font-size-heading-6;
}

/*  PARAGRAPHS
-------------------------------------------------------------------------------*/

p {}

/*  LISTS
-------------------------------------------------------------------------------*/

/*  Unordered */

ul {}

/*  Ordered */

ol {}

/*  Description */

dl {
	dt {}
	dd {}
}

/*  TEXT-LEVEL SEMANTICS
-------------------------------------------------------------------------------*/

a {}
em {}
strong {}
del {}
small {}

/*  HORIZONTAL RULE
-------------------------------------------------------------------------------*/

hr {
	border-color: @color-main-borders; /* theme */
}

/*  BLOCKQUOTES
-------------------------------------------------------------------------------*/

blockquote {
	font-size: @font-size-base;
	:before {
		content: '\201E';
	}
	:after {
		content: '\201D';
	}
	p {
		font-style: italic;
	}
}

/*  CODE BLOCKS
-------------------------------------------------------------------------------*/

pre {
	font-size: @font-size-small;
	color: @color-main-meta-text; /* theme */
	code {}
}

/*  TABLES
-------------------------------------------------------------------------------*/

table {
	width: 100%;
	th {
		font-weight: 500;
		border-bottom-width: 1px;
		border-bottom-style: solid;
		border-bottom-color: @color-main-borders; /* theme */
		padding: 0 8px 15px;
		vertical-align: top;
		&:first-child {
			padding-left: 0;
		}
		&:last-child {
			padding-right: 0;
		}
	}
	td {
		padding: 15px 8px;
		border-bottom-width: 1px;
		border-bottom-style: solid;
		border-bottom-color: @color-main-borders; /* theme */
		vertical-align: top;
		&:first-child {
			padding-left: 0;
		}
		&:last-child {
			padding-right: 0;
		}
		a {
			display: inline-block;
		}
	}
}

/*  TEXTBOX
-------------------------------------------------------------------------------*/

._textbox {
	word-break: break-word;
	*:not(br):not(li) {
		margin-top: 20px;
		&:first-child {
			margin-top: 0;
		}
	}
	.text-aligncenter {
		text-align: center;
	}

	.text-alignleft {
		text-align: left;
	}

	.text-alignright {
		text-align: right;
	}

	.text-alignjustify {
		text-align: justify;
	}
	img {
		&.alignleft {
			float: left;
			margin-right: 20px;
			margin-bottom: 20px;
		}
		&.alignright {
			float: right;
			margin-left: 20px;
			margin-bottom: 20px;
		}
		&.aligncenter {
			margin-left: auto;
			margin-right: auto;
			display: block;
		}
	}
}