[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 1, "url_handle": "dhmioyrghste-enan-titlo-poy-perigrafei-ton-typo-to-xrwma-kai-to-yliko-toy-proiontos-ews-60-xarakthres", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Χρώμα", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 1, "vendor_id": null, "image_id": 17, "tracking": "yes", "threshold": 3, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 20000, "price_to": 20000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 114, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 114, "v1": "μαύρο", "v2": null, "v3": null, "v1_id": 26, "v2_id": null, "v3_id": null, "quantity": 0, "sku": "", "barcode": "", "price": 20000, "delivery_price": null, "weight": 1000}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 115, "v1": "λευκό", "v2": null, "v3": null, "v1_id": 28, "v2_id": null, "v3_id": null, "quantity": 0, "sku": "", "barcode": "", "price": 20000, "delivery_price": null, "weight": 1000}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 17, "name": "Елегантни вталени дънки със скъсена дължина", "parent_id": 1, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "<PERSON>ni-v<PERSON>ni-danki-sas-s<PERSON><PERSON>-da<PERSON><PERSON>a-image_61b87af86a1dd.jpeg", "background": "#696969", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 64, "color_id": 44, "percents": 66, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 65, "color_id": 55, "percents": 12, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 66, "color_id": 73, "percents": 8, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 67, "color_id": 26, "percents": 7, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 275881}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 27, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 1, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253d603a235a.jpeg", "background": "#F5F5F5", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 95, "color_id": 146, "percents": 65, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 96, "color_id": 50, "percents": 10, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 143585}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 1, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 2, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 3, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 257, "parameter_id": 2, "parameter_option_id": 26, "variant_id": 114}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 258, "parameter_id": 2, "parameter_option_id": 28, "variant_id": 115}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "dhmioyrghste-enan-titlo-poy-perigrafei-ton-typo-to-xrwma-kai-to-yliko-toy-proiontos-ews-60-xarakthres-1", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Μέγεθος", "p2": "Χρώμα", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 1, "vendor_id": null, "image_id": 16, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 80000, "price_to": 80000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "yes", "imported": "no", "draft": "no", "default_variant_id": 124, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 124, "v1": "S", "v2": "ρ<PERSON><PERSON>", "v3": null, "v1_id": 1, "v2_id": 27, "v3_id": null, "quantity": 10, "sku": "89754", "barcode": "875487", "price": 80000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 125, "v1": "S", "v2": "πράσινο", "v3": null, "v1_id": 1, "v2_id": 30, "v3_id": null, "quantity": 10, "sku": "89754", "barcode": "875487", "price": 80000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 126, "v1": "L", "v2": "ρ<PERSON><PERSON>", "v3": null, "v1_id": 3, "v2_id": 27, "v3_id": null, "quantity": 10, "sku": "89548", "barcode": "708547", "price": 80000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 127, "v1": "L", "v2": "πράσινο", "v3": null, "v1_id": 3, "v2_id": 30, "v3_id": null, "quantity": 10, "sku": "89548", "barcode": "708547", "price": 80000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 128, "v1": "м", "v2": "ρ<PERSON><PERSON>", "v3": null, "v1_id": 10, "v2_id": 27, "v3_id": null, "quantity": 10, "sku": "89568", "barcode": "08792547", "price": 80000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 129, "v1": "м", "v2": "πράσινο", "v3": null, "v1_id": 10, "v2_id": 30, "v3_id": null, "quantity": 10, "sku": "89568", "barcode": "08792547", "price": 80000, "delivery_price": null, "weight": 800}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 16, "name": "Официална вечерна рокля с ръчна бродерия и пайети", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "oficialna-vecerna-rokla-s-racna-broderia-i-pajeti-image_61b87ae89b9e6.jpeg", "background": "#708090", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 58, "color_id": 134, "percents": 30, "R": 112, "G": 128, "B": 144, "hex": "#708090"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 59, "color_id": 80, "percents": 27, "R": 119, "G": 136, "B": 153, "hex": "#778899"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 60, "color_id": 26, "percents": 16, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 61, "color_id": 55, "percents": 13, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 62, "color_id": 138, "percents": 6, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 63, "color_id": 44, "percents": 5, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 276500}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 30, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 2, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253d835e43e7.jpeg", "background": "#DCDCDC", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 103, "color_id": 50, "percents": 66, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 104, "color_id": 60, "percents": 6, "R": 205, "G": 92, "B": 92, "hex": "#CD5C5C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 105, "color_id": 12, "percents": 5, "R": 165, "G": 42, "B": 42, "hex": "#A52A2A"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 143048}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 259, "parameter_id": 1, "parameter_option_id": 1, "variant_id": 125}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 260, "parameter_id": 2, "parameter_option_id": 27, "variant_id": 128}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 261, "parameter_id": 2, "parameter_option_id": 30, "variant_id": 129}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 262, "parameter_id": 1, "parameter_option_id": 3, "variant_id": 127}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 263, "parameter_id": 1, "parameter_option_id": 10, "variant_id": 129}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 3, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-11", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Μέγεθος", "p2": "Χρώμα", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 2, "vendor_id": null, "image_id": 14, "tracking": "yes", "threshold": 2, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 30000, "price_to": 30000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "yes", "imported": "no", "draft": "no", "default_variant_id": 118, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 118, "v1": "S", "v2": "πράσινο", "v3": null, "v1_id": 1, "v2_id": 30, "v3_id": null, "quantity": 15, "sku": "0-86538093567", "barcode": "0925670", "price": 30000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 119, "v1": "S", "v2": "γκρι", "v3": null, "v1_id": 1, "v2_id": 32, "v3_id": null, "quantity": 15, "sku": "0-86538093567", "barcode": "0925670", "price": 30000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 120, "v1": "L", "v2": "πράσινο", "v3": null, "v1_id": 3, "v2_id": 30, "v3_id": null, "quantity": 15, "sku": "0-86538093567", "barcode": "0925670", "price": 30000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 121, "v1": "L", "v2": "γκρι", "v3": null, "v1_id": 3, "v2_id": 32, "v3_id": null, "quantity": 15, "sku": "0-86538093567", "barcode": "0925670", "price": 30000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 122, "v1": "м", "v2": "πράσινο", "v3": null, "v1_id": 10, "v2_id": 30, "v3_id": null, "quantity": 15, "sku": "0-86538093567", "barcode": "0925670", "price": 30000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 123, "v1": "м", "v2": "γκρι", "v3": null, "v1_id": 10, "v2_id": 32, "v3_id": null, "quantity": 15, "sku": "0-86538093567", "barcode": "0925670", "price": 30000, "delivery_price": null, "weight": 800}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 14, "name": "Класическа риза от памук Brunellino", "parent_id": 3, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "klasiceska-riza-ot-pamuk-brunellino-image_61b87ac2464a6.jpeg", "background": "#A9A9A9", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 47, "color_id": 26, "percents": 27, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 48, "color_id": 134, "percents": 20, "R": 112, "G": 128, "B": 144, "hex": "#708090"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 49, "color_id": 80, "percents": 19, "R": 119, "G": 136, "B": 153, "hex": "#778899"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 50, "color_id": 55, "percents": 11, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 51, "color_id": 138, "percents": 10, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 52, "color_id": 44, "percents": 6, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 163563}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 29, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 3, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253d7298d5bb.jpeg", "background": "#A9A9A9", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 101, "color_id": 26, "percents": 85, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 102, "color_id": 55, "percents": 9, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 199215}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 7, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 8, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 9, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 264, "parameter_id": 1, "parameter_option_id": 1, "variant_id": 119}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 265, "parameter_id": 2, "parameter_option_id": 30, "variant_id": 122}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 266, "parameter_id": 2, "parameter_option_id": 32, "variant_id": 123}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 267, "parameter_id": 1, "parameter_option_id": 3, "variant_id": 121}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 268, "parameter_id": 1, "parameter_option_id": 10, "variant_id": 123}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 4, "url_handle": "dhmioyrghste-enan-titlo-poy-perigrafei-ton-typo-to-xrwma-kai-to-yliko-toy-proiontos-ews-60-xarakthres-2", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Χρώμα", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 2, "vendor_id": null, "image_id": 15, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 45000, "price_to": 45000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "yes", "imported": "no", "draft": "no", "default_variant_id": 116, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 116, "v1": "πράσινο", "v2": null, "v3": null, "v1_id": 30, "v2_id": null, "v3_id": null, "quantity": 0, "sku": "", "barcode": "", "price": 45000, "delivery_price": null, "weight": 1000}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 117, "v1": "μπεζ", "v2": null, "v3": null, "v1_id": 31, "v2_id": null, "v3_id": null, "quantity": 0, "sku": "", "barcode": "", "price": 45000, "delivery_price": null, "weight": 1000}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 15, "name": "Спортно - елегантен панталон с ръб и басти от фина вълна Brunellino", "parent_id": 4, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "sportno-eleganten-pantalon-s-rab-i-basti-ot-fina-valna-brunellino-image_61b87ad602986.jpeg", "background": "#A9A9A9", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 53, "color_id": 26, "percents": 37, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 54, "color_id": 130, "percents": 24, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 55, "color_id": 44, "percents": 15, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 56, "color_id": 124, "percents": 14, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 57, "color_id": 138, "percents": 5, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 148847}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 28, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 4, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253d6e0534d1.jpeg", "background": "#FAF0E6", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 97, "color_id": 85, "percents": 55, "R": 250, "G": 240, "B": 230, "hex": "#FAF0E6"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 98, "color_id": 73, "percents": 18, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 99, "color_id": 50, "percents": 8, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 100, "color_id": 130, "percents": 6, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 110463}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 25, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 26, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 27, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 269, "parameter_id": 2, "parameter_option_id": 30, "variant_id": 116}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 270, "parameter_id": 2, "parameter_option_id": 31, "variant_id": 117}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 5, "url_handle": "dhmioyrghste-enan-titlo-poy-perigrafei-ton-typo-to-xrwma-kai-to-yliko-toy-proiontos-ews-60-xarakthres-4", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Μέγεθος", "p2": "Χρώμα", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 3, "vendor_id": null, "image_id": 5, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 8000, "price_to": 8000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "yes", "imported": "no", "draft": "no", "default_variant_id": 106, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 106, "v1": "S", "v2": "λευκό", "v3": null, "v1_id": 1, "v2_id": 28, "v3_id": null, "quantity": 30, "sku": "8970457895", "barcode": "349845709", "price": 8000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 107, "v1": "S", "v2": "ριγέ", "v3": null, "v1_id": 1, "v2_id": 29, "v3_id": null, "quantity": 30, "sku": "8970457895", "barcode": "349845709", "price": 8000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 108, "v1": "M", "v2": "λευκό", "v3": null, "v1_id": 2, "v2_id": 28, "v3_id": null, "quantity": 30, "sku": "8970457895", "barcode": "349845709", "price": 8000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 109, "v1": "M", "v2": "ριγέ", "v3": null, "v1_id": 2, "v2_id": 29, "v3_id": null, "quantity": 30, "sku": "8970457895", "barcode": "349845709", "price": 8000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 110, "v1": "L", "v2": "λευκό", "v3": null, "v1_id": 3, "v2_id": 28, "v3_id": null, "quantity": 30, "sku": "8970457895", "barcode": "349845709", "price": 8000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 111, "v1": "L", "v2": "ριγέ", "v3": null, "v1_id": 3, "v2_id": 29, "v3_id": null, "quantity": 30, "sku": "8970457895", "barcode": "349845709", "price": 8000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 112, "v1": "XS", "v2": "λευκό", "v3": null, "v1_id": 11, "v2_id": 28, "v3_id": null, "quantity": 30, "sku": "8970457895", "barcode": "349845709", "price": 8000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 113, "v1": "XS", "v2": "ριγέ", "v3": null, "v1_id": 11, "v2_id": 29, "v3_id": null, "quantity": 30, "sku": "8970457895", "barcode": "349845709", "price": 8000, "delivery_price": null, "weight": 500}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 5, "name": "Блуза с принт Junior", "parent_id": 5, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "bluza-s-print-junior-image_6183d0a05935f.jpeg", "background": "#DCDCDC", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 21, "color_id": 50, "percents": 72, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 22, "color_id": 146, "percents": 7, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 271348}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 26, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 5, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253d4ffdaf47.jpeg", "background": "#DCDCDC", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 93, "color_id": 50, "percents": 76, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 94, "color_id": 73, "percents": 18, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 74551}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 10, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 11, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 12, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 283, "parameter_id": 1, "parameter_option_id": 1, "variant_id": 107}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 284, "parameter_id": 2, "parameter_option_id": 28, "variant_id": 112}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 285, "parameter_id": 2, "parameter_option_id": 29, "variant_id": 113}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 286, "parameter_id": 1, "parameter_option_id": 2, "variant_id": 109}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 287, "parameter_id": 1, "parameter_option_id": 3, "variant_id": 111}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 288, "parameter_id": 1, "parameter_option_id": 11, "variant_id": 113}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 6, "url_handle": "dhmioyrghste-enan-titlo-poy-perigrafei-ton-typo-to-xrwma-kai-to-yliko-toy-proiontos-ews-60-xarakthres-3", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Μέγεθος", "p2": "Χρώμα", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 3, "vendor_id": null, "image_id": 6, "tracking": "yes", "threshold": 3, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 10000, "price_to": 10000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "yes", "imported": "no", "draft": "no", "default_variant_id": 100, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 98, "v1": "S", "v2": "μαύρο", "v3": null, "v1_id": 1, "v2_id": 26, "v3_id": null, "quantity": 30, "sku": "0985479635", "barcode": "907354976", "price": 10000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 99, "v1": "S", "v2": "λευκό", "v3": null, "v1_id": 1, "v2_id": 28, "v3_id": null, "quantity": 30, "sku": "0985479635", "barcode": "907354976", "price": 10000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 100, "v1": "L", "v2": "μαύρο", "v3": null, "v1_id": 3, "v2_id": 26, "v3_id": null, "quantity": 30, "sku": "0985479635", "barcode": "907354976", "price": 10000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 101, "v1": "L", "v2": "λευκό", "v3": null, "v1_id": 3, "v2_id": 28, "v3_id": null, "quantity": 30, "sku": "0985479635", "barcode": "907354976", "price": 10000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 102, "v1": "м", "v2": "μαύρο", "v3": null, "v1_id": 10, "v2_id": 26, "v3_id": null, "quantity": 30, "sku": "0985479635", "barcode": "907354976", "price": 10000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 103, "v1": "м", "v2": "λευκό", "v3": null, "v1_id": 10, "v2_id": 28, "v3_id": null, "quantity": 30, "sku": "0985479635", "barcode": "907354976", "price": 10000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 104, "v1": "XS", "v2": "μαύρο", "v3": null, "v1_id": 11, "v2_id": 26, "v3_id": null, "quantity": 30, "sku": "0985479635", "barcode": "907354976", "price": 10000, "delivery_price": null, "weight": 500}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 105, "v1": "XS", "v2": "λευκό", "v3": null, "v1_id": 11, "v2_id": 28, "v3_id": null, "quantity": 30, "sku": "0985479635", "barcode": "907354976", "price": 10000, "delivery_price": null, "weight": 500}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 6, "name": "Суитшърт с пайети Junior", "parent_id": 6, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "<PERSON><PERSON>-s-p<PERSON><PERSON>-junior-image_6183d1c1073ed.jpeg", "background": "#DCDCDC", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 23, "color_id": 50, "percents": 77, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 24, "color_id": 146, "percents": 15, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 126456}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 25, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 6, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253d48417e28.jpeg", "background": "#FAF0E6", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 91, "color_id": 85, "percents": 43, "R": 250, "G": 240, "B": 230, "hex": "#FAF0E6"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 92, "color_id": 103, "percents": 26, "R": 253, "G": 245, "B": 230, "hex": "#FDF5E6"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 170133}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 13, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 14, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 15, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 277, "parameter_id": 1, "parameter_option_id": 1, "variant_id": 99}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 278, "parameter_id": 2, "parameter_option_id": 26, "variant_id": 104}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 279, "parameter_id": 2, "parameter_option_id": 28, "variant_id": 105}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 280, "parameter_id": 1, "parameter_option_id": 3, "variant_id": 101}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 281, "parameter_id": 1, "parameter_option_id": 10, "variant_id": 103}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 282, "parameter_id": 1, "parameter_option_id": 11, "variant_id": 105}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 7, "url_handle": "dhmioyrghste-enan-titlo-poy-perigrafei-ton-typo-to-xrwma-kai-to-yliko-toy-proiontos-ews-60-xarakthres-5", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Χρώμα", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 4, "vendor_id": null, "image_id": 7, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 50000, "price_to": 50000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "yes", "imported": "no", "draft": "no", "default_variant_id": 96, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 96, "v1": "yellow", "v2": null, "v3": null, "v1_id": 25, "v2_id": null, "v3_id": null, "quantity": 0, "sku": "", "barcode": "", "price": 50000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 97, "v1": "ρ<PERSON><PERSON>", "v2": null, "v3": null, "v1_id": 27, "v2_id": null, "v3_id": null, "quantity": 0, "sku": "", "barcode": "", "price": 50000, "delivery_price": null, "weight": 400}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 7, "name": "Слънчеви очила с верига Sunglass", "parent_id": 7, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "slancevi-ocila-s-veriga-sunglass-image_6183d3136c5d4.jpeg", "background": "#DCDCDC", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 25, "color_id": 50, "percents": 87, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 26, "color_id": 146, "percents": 7, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 96130}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 24, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 7, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253d2a830abd.jpeg", "background": "#FFFAF0", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 86, "color_id": 47, "percents": 12, "R": 255, "G": 250, "B": 240, "hex": "#FFFAF0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 87, "color_id": 62, "percents": 10, "R": 255, "G": 255, "B": 240, "hex": "#FFFFF0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 88, "color_id": 130, "percents": 10, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 89, "color_id": 135, "percents": 8, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 90, "color_id": 138, "percents": 8, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 87090}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 37, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 38, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 39, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 289, "parameter_id": 2, "parameter_option_id": 25, "variant_id": 96}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 290, "parameter_id": 2, "parameter_option_id": 27, "variant_id": 97}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 8, "url_handle": "dhmioyrghste-enan-titlo-poy-perigrafei-ton-typo-to-xrwma-kai-to-yliko-toy-proiontos-ews-60-xarakthres-6", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Χρώμα", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 4, "vendor_id": null, "image_id": 13, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 8000, "price_to": 8000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "yes", "imported": "no", "draft": "no", "default_variant_id": 94, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 94, "v1": "yellow", "v2": null, "v3": null, "v1_id": 25, "v2_id": null, "v3_id": null, "quantity": 0, "sku": "", "barcode": "", "price": 8000, "delivery_price": null, "weight": 200}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 95, "v1": "μαύρο", "v2": null, "v3": null, "v1_id": 26, "v2_id": null, "v3_id": null, "quantity": 0, "sku": "", "barcode": "", "price": 8000, "delivery_price": null, "weight": 200}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 13, "name": "Ефектен сатен<PERSON><PERSON><PERSON><PERSON> к<PERSON><PERSON><PERSON><PERSON>", "parent_id": 8, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "efekten-sat<PERSON><PERSON>-kolan-la<PERSON>a-image_61b752fcdacca.jpeg", "background": "#808080", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 42, "color_id": 55, "percents": 36, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 43, "color_id": 44, "percents": 21, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 44, "color_id": 26, "percents": 18, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 45, "color_id": 146, "percents": 14, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 46, "color_id": 50, "percents": 7, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 90910}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 23, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 8, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253cfb121f42.jpeg", "background": "#F5F5F5", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 84, "color_id": 146, "percents": 79, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 85, "color_id": 53, "percents": 5, "R": 218, "G": 165, "B": 32, "hex": "#DAA520"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 68302}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 16, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 17, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 18, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 291, "parameter_id": 2, "parameter_option_id": 25, "variant_id": 94}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 292, "parameter_id": 2, "parameter_option_id": 26, "variant_id": 95}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 9, "url_handle": "dhmioyrghste-enan-titlo-poy-perigrafei-ton-typo-to-xrwma-kai-to-yliko-toy-proiontos-ews-60-xarakthres-7", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Μέγεθος", "p2": "Χρώμα", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 5, "vendor_id": null, "image_id": 9, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 25000, "price_to": 25000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "yes", "imported": "no", "draft": "no", "default_variant_id": 72, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 72, "v1": "35", "v2": "μαύρο", "v3": null, "v1_id": 12, "v2_id": 26, "v3_id": null, "quantity": 15, "sku": "9845380", "barcode": "90-87653-8", "price": 25000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 73, "v1": "35", "v2": "ρ<PERSON><PERSON>", "v3": null, "v1_id": 12, "v2_id": 27, "v3_id": null, "quantity": 15, "sku": "9845380", "barcode": "90-87653-8", "price": 25000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 74, "v1": "36", "v2": "μαύρο", "v3": null, "v1_id": 13, "v2_id": 26, "v3_id": null, "quantity": 15, "sku": "9845380", "barcode": "90-87653-8", "price": 25000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 75, "v1": "36", "v2": "ρ<PERSON><PERSON>", "v3": null, "v1_id": 13, "v2_id": 27, "v3_id": null, "quantity": 15, "sku": "9845380", "barcode": "90-87653-8", "price": 25000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 76, "v1": "37", "v2": "μαύρο", "v3": null, "v1_id": 14, "v2_id": 26, "v3_id": null, "quantity": 15, "sku": "9845380", "barcode": "90-87653-8", "price": 25000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 77, "v1": "37", "v2": "ρ<PERSON><PERSON>", "v3": null, "v1_id": 14, "v2_id": 27, "v3_id": null, "quantity": 15, "sku": "9845380", "barcode": "90-87653-8", "price": 25000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 78, "v1": "38", "v2": "μαύρο", "v3": null, "v1_id": 15, "v2_id": 26, "v3_id": null, "quantity": 15, "sku": "9845380", "barcode": "90-87653-8", "price": 25000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 79, "v1": "38", "v2": "ρ<PERSON><PERSON>", "v3": null, "v1_id": 15, "v2_id": 27, "v3_id": null, "quantity": 15, "sku": "9845380", "barcode": "90-87653-8", "price": 25000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 80, "v1": "39", "v2": "μαύρο", "v3": null, "v1_id": 16, "v2_id": 26, "v3_id": null, "quantity": 15, "sku": "9845380", "barcode": "90-87653-8", "price": 25000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 81, "v1": "39", "v2": "ρ<PERSON><PERSON>", "v3": null, "v1_id": 16, "v2_id": 27, "v3_id": null, "quantity": 15, "sku": "9845380", "barcode": "90-87653-8", "price": 25000, "delivery_price": null, "weight": 800}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 9, "name": "Елегантни обувки от лъскава кож<PERSON> M<PERSON>s", "parent_id": 9, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "elegantni-obuvki-ot-laskava-koza-morellis-image_6183d5d26a653.jpeg", "background": "#F5F5F5", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 28, "color_id": 146, "percents": 94, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 83103}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 21, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 9, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253cde9ba871.jpeg", "background": "#F5F5F5", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 80, "color_id": 146, "percents": 79, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 81, "color_id": 99, "percents": 8, "R": 255, "G": 228, "B": 225, "hex": "#FFE4E1"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 109360}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 19, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 20, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 21, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 293, "parameter_id": 1, "parameter_option_id": 12, "variant_id": 73}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 294, "parameter_id": 2, "parameter_option_id": 26, "variant_id": 80}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 295, "parameter_id": 2, "parameter_option_id": 27, "variant_id": 81}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 296, "parameter_id": 1, "parameter_option_id": 13, "variant_id": 75}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 297, "parameter_id": 1, "parameter_option_id": 14, "variant_id": 77}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 298, "parameter_id": 1, "parameter_option_id": 15, "variant_id": 79}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 299, "parameter_id": 1, "parameter_option_id": 16, "variant_id": 81}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 10, "url_handle": "dhmioyrghste-enan-titlo-poy-perigrafei-ton-typo-to-xrwma-kai-to-yliko-toy-proiontos-ews-60-xarakthres-8", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Μέγεθος", "p2": "Χρώμα", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 6, "vendor_id": null, "image_id": 10, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 24000, "price_to": 24000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "yes", "imported": "no", "draft": "no", "default_variant_id": 82, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 82, "v1": "40", "v2": "brown", "v3": null, "v1_id": 17, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 83, "v1": "40", "v2": "μαύρο", "v3": null, "v1_id": 17, "v2_id": 26, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 84, "v1": "41", "v2": "brown", "v3": null, "v1_id": 18, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 85, "v1": "41", "v2": "μαύρο", "v3": null, "v1_id": 18, "v2_id": 26, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 86, "v1": "42", "v2": "brown", "v3": null, "v1_id": 19, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 87, "v1": "42", "v2": "μαύρο", "v3": null, "v1_id": 19, "v2_id": 26, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 88, "v1": "43", "v2": "brown", "v3": null, "v1_id": 20, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 89, "v1": "43", "v2": "μαύρο", "v3": null, "v1_id": 20, "v2_id": 26, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 90, "v1": "44", "v2": "brown", "v3": null, "v1_id": 21, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 91, "v1": "44", "v2": "μαύρο", "v3": null, "v1_id": 21, "v2_id": 26, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 92, "v1": "45", "v2": "brown", "v3": null, "v1_id": 22, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 93, "v1": "45", "v2": "μαύρο", "v3": null, "v1_id": 22, "v2_id": 26, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 24000, "delivery_price": null, "weight": 800}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 10, "name": "Мока<PERSON><PERSON><PERSON><PERSON>", "parent_id": 10, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "moka<PERSON><PERSON>-er<PERSON><PERSON><PERSON>-image_6183d6ee42eba.jpeg", "background": "#F5F5F5", "width": 1401, "height": 1920, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 29, "color_id": 146, "percents": 29, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 30, "color_id": 51, "percents": 11, "R": 248, "G": 248, "B": 255, "hex": "#F8F8FF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 31, "color_id": 135, "percents": 9, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 32, "color_id": 97, "percents": 9, "R": 25, "G": 25, "B": 112, "hex": "#191970"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 33, "color_id": 44, "percents": 9, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 34, "color_id": 87, "percents": 7, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 35, "color_id": 129, "percents": 7, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 234686}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 22, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 10, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253cec077358.jpeg", "background": "#800000", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 82, "color_id": 87, "percents": 55, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 83, "color_id": 135, "percents": 13, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 86605}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 22, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 23, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 24, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 308, "parameter_id": 1, "parameter_option_id": 17, "variant_id": 83}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 309, "parameter_id": 2, "parameter_option_id": 23, "variant_id": 92}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 310, "parameter_id": 2, "parameter_option_id": 26, "variant_id": 93}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 311, "parameter_id": 1, "parameter_option_id": 18, "variant_id": 85}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 312, "parameter_id": 1, "parameter_option_id": 19, "variant_id": 87}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 313, "parameter_id": 1, "parameter_option_id": 20, "variant_id": 89}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 314, "parameter_id": 1, "parameter_option_id": 21, "variant_id": 91}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 315, "parameter_id": 1, "parameter_option_id": 22, "variant_id": 93}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 11, "url_handle": "dhmioyrghste-enan-titlo-poy-perigrafei-ton-typo-to-xrwma-kai-to-yliko-toy-proiontos-ews-60-xarakthres-9", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Μέγεθος", "p2": "Χρώμα", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 5, "vendor_id": null, "image_id": 11, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 35000, "price_to": 35000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "yes", "imported": "no", "draft": "no", "default_variant_id": 60, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 60, "v1": "35", "v2": "brown", "v3": null, "v1_id": 12, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 61, "v1": "35", "v2": "yellow", "v3": null, "v1_id": 12, "v2_id": 25, "v3_id": null, "quantity": 20, "sku": "09569865", "barcode": "9085684", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 62, "v1": "36", "v2": "brown", "v3": null, "v1_id": 13, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "132456", "barcode": "321654987", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 63, "v1": "36", "v2": "yellow", "v3": null, "v1_id": 13, "v2_id": 25, "v3_id": null, "quantity": 20, "sku": "132456", "barcode": "321654987", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 64, "v1": "37", "v2": "brown", "v3": null, "v1_id": 14, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "132456", "barcode": "321654987", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 65, "v1": "37", "v2": "yellow", "v3": null, "v1_id": 14, "v2_id": 25, "v3_id": null, "quantity": 20, "sku": "132456", "barcode": "321654987", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 66, "v1": "38", "v2": "brown", "v3": null, "v1_id": 15, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "132456", "barcode": "321654987", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 67, "v1": "38", "v2": "yellow", "v3": null, "v1_id": 15, "v2_id": 25, "v3_id": null, "quantity": 20, "sku": "132456", "barcode": "321654987", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 68, "v1": "39", "v2": "brown", "v3": null, "v1_id": 16, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "132456", "barcode": "321654987", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 69, "v1": "39", "v2": "yellow", "v3": null, "v1_id": 16, "v2_id": 25, "v3_id": null, "quantity": 20, "sku": "132456", "barcode": "321654987", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 70, "v1": "40", "v2": "brown", "v3": null, "v1_id": 17, "v2_id": 23, "v3_id": null, "quantity": 20, "sku": "132456", "barcode": "321654987", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 71, "v1": "40", "v2": "yellow", "v3": null, "v1_id": 17, "v2_id": 25, "v3_id": null, "quantity": 20, "sku": "132456", "barcode": "321654987", "price": 35000, "delivery_price": null, "weight": 400}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 11, "name": "Елегантни дамски мокасини", "parent_id": 11, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "<PERSON><PERSON>-<PERSON><PERSON>-m<PERSON><PERSON><PERSON>-image_6195fecb65469.jpeg", "background": "#F5F5F5", "width": 1096, "height": 1500, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 36, "color_id": 146, "percents": 84, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 37, "color_id": 87, "percents": 6, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 199561}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 20, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 11, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253cd7a85db8.jpeg", "background": "#F0E68C", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 76, "color_id": 63, "percents": 42, "R": 240, "G": 230, "B": 140, "hex": "#F0E68C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 77, "color_id": 28, "percents": 11, "R": 189, "G": 183, "B": 107, "hex": "#BDB76B"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 78, "color_id": 135, "percents": 8, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 79, "color_id": 109, "percents": 8, "R": 238, "G": 232, "B": 170, "hex": "#EEE8AA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 86516}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 28, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 29, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 30, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 316, "parameter_id": 1, "parameter_option_id": 12, "variant_id": 61}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 317, "parameter_id": 2, "parameter_option_id": 23, "variant_id": 70}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 318, "parameter_id": 2, "parameter_option_id": 25, "variant_id": 71}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 319, "parameter_id": 1, "parameter_option_id": 13, "variant_id": 63}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 320, "parameter_id": 1, "parameter_option_id": 14, "variant_id": 65}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 321, "parameter_id": 1, "parameter_option_id": 15, "variant_id": 67}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 322, "parameter_id": 1, "parameter_option_id": 16, "variant_id": 69}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 323, "parameter_id": 1, "parameter_option_id": 17, "variant_id": 71}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 12, "url_handle": "dhmioyrghste-enan-titlo-poy-perigrafei-ton-typo-to-xrwma-kai-to-yliko-toy-proiontos-ews-60-xarakthres-10", "name": "Δημιουργήστε έναν τίτλο που περιγράφει τον τύπο, το χρώμα και το υλικό του προϊόντος (έως 60 χαρακτήρες)", "p1": "Χρώμα", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; έ&chi;&epsilon;&iota; ή&delta;&eta; &delta;&iota;&alpha;&beta;ά&sigma;&epsilon;&iota; &tau;&eta;&nu; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &omicron;&pi;ό&tau;&epsilon; &epsilon;&delta;ώ &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &upsilon;&pi;&omicron;&gamma;&rho;&alpha;&mu;&mu;ί&sigma;&epsilon;&tau;&epsilon; &tau;&iota;&sigmaf; &upsilon;&pi;ό&lambda;&omicron;&iota;&pi;&epsilon;&sigmaf; &lambda;&epsilon;&pi;&tau;&omicron;&mu;έ&rho;&epsilon;&iota;&epsilon;&sigmaf; &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf;. &Alpha;&nu;ά&lambda;&omicron;&gamma;&alpha; &mu;&epsilon; &tau;&omicron;&nu; &kappa;&lambda;ά&delta;&omicron; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&epsilon;ί&tau;&epsilon; &nu;&alpha; &pi;&rho;&omicron;&sigma;&theta;έ&sigma;&epsilon;&tau;&epsilon; &tau;&epsilon;&chi;&nu;&iota;&kappa;ά &chi;&alpha;&rho;&alpha;&kappa;&tau;&eta;&rho;&iota;&sigma;&tau;&iota;&kappa;ά, &sigma;&upsilon;&sigma;&tau;&alpha;&tau;&iota;&kappa;ά, &pi;&rho;ώ&tau;&epsilon;&sigmaf; ύ&lambda;&epsilon;&sigmaf;, &beta;ί&nu;&tau;&epsilon;&omicron; ή &phi;&omega;&tau;&omicron;&gamma;&rho;&alpha;&phi;ί&epsilon;&sigmaf;. &Alpha;&upsilon;&tau;ό &epsilon;ί&nu;&alpha;&iota; &sigma;&eta;&mu;&alpha;&nu;&tau;&iota;&kappa;ό &beta;ή&mu;&alpha;, &mu;&iota;&alpha;&sigmaf; &kappa;&alpha;&iota; &omicron; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta;&sigmaf; &sigma;&alpha;&sigmaf; &epsilon;&xi;&epsilon;&tau;ά&zeta;&epsilon;&iota; ή&delta;&eta; &tau;&omicron; &sigma;&epsilon;&nu;ά&rho;&iota;&omicron; &nu;&alpha; &alpha;&gamma;&omicron;&rho;ά&sigma;&epsilon;&iota;. &Eta; &pi;&rho;&omicron;ϊ&omicron;&nu;&tau;&iota;&kappa;ή &sigma;&epsilon;&lambda;ί&delta;&alpha; &lambda;&omicron;&iota;&pi;ό&nu; &pi;&rho;έ&pi;&epsilon;&iota; &nu;&alpha; έ&chi;&epsilon;&iota; ό&lambda;&epsilon;&sigmaf; &tau;&iota;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &pi;&omicron;&upsilon; &chi;&rho;&epsilon;&iota;ά&zeta;&epsilon;&tau;&alpha;&iota; &nu;&alpha; &xi;έ&rho;&epsilon;&iota; &omicron; &pi;&epsilon;&lambda;ά&tau;&eta;&sigmaf;, &kappa;&alpha;&theta;&alpha;&rho;ά &kappa;&alpha;&iota; &epsilon;&upsilon;&alpha;&nu;ά&gamma;&nu;&omega;&sigma;&tau;&alpha;.</span></p>", "seo_title": "Δημιουργήστε έναν τίτλο που να περιγράφει το είδος των προϊόντων της κατηγορίας (έως 60 χαρακτήρες)", "seo_description": "Δεδομένου ότι υπάρχουν τόσα πολλά ηλεκτρονι<PERSON><PERSON> καταστήματα, η μακροπρόθεσμη επιτυχία σας απαιτεί μια καλά μελετημένη επιχειρηματική προσέγγιση. Είναι σημαντικό να βασίσετε την περιγραφή SEO σας σε μια ενδελεχή ανάλυση που θα γίνει από έναν ειδικό SEO", "category_id": 4, "vendor_id": null, "image_id": 12, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 32000, "price_to": 32000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 58, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:15107,&quot;3&quot;:{&quot;1&quot;:0},&quot;4&quot;:{&quot;1&quot;:2,&quot;2&quot;:16777215},&quot;11&quot;:4,&quot;12&quot;:0,&quot;14&quot;:{&quot;1&quot;:2,&quot;2&quot;:0},&quot;15&quot;:&quot;Arial&quot;,&quot;16&quot;:11}\">&Mu;ί&alpha; &sigma;ύ&nu;&tau;&omicron;&mu;&eta; &pi;&epsilon;&rho;&iota;&gamma;&rho;&alpha;&phi;ή &tau;&omicron;&upsilon; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &mu;&epsilon; &chi;&rho;ή&sigma;&iota;&mu;&epsilon;&sigmaf; &pi;&lambda;&eta;&rho;&omicron;&phi;&omicron;&rho;ί&epsilon;&sigmaf; &sigma;&chi;&epsilon;&tau;&iota;&kappa;ά &mu;&epsilon; &tau;&omicron; &pi;&rho;&omicron;ϊό&nu; &sigma;&alpha;&sigmaf; &mu;&pi;&omicron;&rho;&omicron;ύ&nu; &upsilon;&pi;&omicron;&sigma;&upsilon;&nu;&epsilon;ί&delta;&eta;&tau;&alpha; &nu;&alpha; &mu;&epsilon;&tau;&alpha;&tau;&rho;έ&psi;&omicron;&upsilon;&nu; έ&nu;&alpha;&nu; &epsilon;&pi;&iota;&sigma;&kappa;έ&pi;&tau;&eta; &tau;&eta;&sigmaf; &sigma;&epsilon;&lambda;ί&delta;&alpha;&sigmaf; &sigma;&alpha;&sigmaf; &sigma;&epsilon; &pi;&epsilon;&lambda;ά&tau;&eta;. &Mu;ί&alpha;-&delta;ύ&omicron; &mu;&iota;&kappa;&rho;έ&sigmaf; &pi;&rho;&omicron;&tau;ά&sigma;&epsilon;&iota;&sigmaf; &pi;&omicron;&upsilon; &epsilon;&xi;&eta;&gamma;&omicron;ύ&nu; &tau;&iota;&sigmaf; &kappa;&alpha;&iota;&nu;&omicron;&tau;&omicron;&mu;ί&epsilon;&sigmaf; &tau;&omicron;&upsilon;&sigmaf; &pi;&rho;&omicron;ϊό&nu;&tau;&omicron;&sigmaf; &kappa;&alpha;&iota; &tau;&omicron; &theta;έ&tau;&omicron;&upsilon;&nu; &mu;&pi;&rho;&omicron;&sigma;&tau;ά &sigma;&tau;&omicron;&nu; &alpha;&nu;&tau;&alpha;&gamma;&omega;&nu;&iota;&sigma;&mu;ό, &epsilon;ί&nu;&alpha;&iota; &kappa;&alpha;ί&rho;&iota;&epsilon;&sigmaf; &gamma;&iota;&alpha; &nu;&alpha; &gamma;ί&nu;&epsilon;&iota; &tau;&omicron; &kappa;&lambda;&iota;&kappa; &pi;&omicron;&upsilon; &theta;&alpha; &sigma;&alpha;&sigmaf; &phi;έ&rho;&epsilon;&iota; έ&nu;&alpha; &beta;ή&mu;&alpha; &pi;&iota;&omicron; &kappa;&omicron;&nu;&tau;ά &sigma;&tau;&eta;&nu; &pi;ώ&lambda;&eta;&sigma;&eta;. &Epsilon;&delta;ώ &kappa;&rho;ύ&beta;&epsilon;&tau;&alpha;&iota; &mu;ί&alpha; &epsilon;&upsilon;&kappa;&alpha;&iota;&rho;ί&alpha; &nu;&alpha; &kappa;&epsilon;&rho;&delta;ί&sigma;&epsilon;&tau;&epsilon; &tau;&eta;&nu; &epsilon;&mu;&pi;&iota;&sigma;&tau;&omicron;&sigma;ύ&nu;&eta; &tau;&omega;&nu; &epsilon;&pi;&iota;&sigma;&kappa;&epsilon;&pi;&tau;ώ&nu; &sigma;&alpha;&sigmaf;!</span></p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 58, "v1": "brown", "v2": null, "v3": null, "v1_id": 23, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 32000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 59, "v1": "bordeaux", "v2": null, "v3": null, "v1_id": 24, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 32000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 12, "name": "Чанта естествена кожа Cognac", "parent_id": 12, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "canta-estestvena-koza-cognac-image_6196009e50ffb.jpeg", "background": "#A0522D", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 38, "color_id": 129, "percents": 31, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 39, "color_id": 124, "percents": 27, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 40, "color_id": 135, "percents": 8, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 41, "color_id": 87, "percents": 5, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 301280}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 18, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 12, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6253ca9cddccc.jpeg", "background": "#F5F5F5", "width": 876, "height": 1200, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 68, "color_id": 146, "percents": 42, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 69, "color_id": 12, "percents": 20, "R": 165, "G": 42, "B": 42, "hex": "#A52A2A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 70, "color_id": 50, "percents": 13, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 71, "color_id": 44, "percents": 10, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 72, "color_id": 87, "percents": 5, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 342867}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 31, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 32, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 33, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 324, "parameter_id": 2, "parameter_option_id": 23, "variant_id": 58}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 325, "parameter_id": 2, "parameter_option_id": 24, "variant_id": 59}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 13, "url_handle": "<PERSON><PERSON><PERSON>", "name": "ди<PERSON><PERSON><PERSON>н", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>ди<PERSON><PERSON><PERSON><PERSON></p>", "seo_title": "ди<PERSON><PERSON><PERSON>н", "seo_description": "ди<PERSON><PERSON><PERSON>н", "category_id": 7, "vendor_id": null, "image_id": null, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": null, "price_to": null, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "no", "continue_selling": "no", "imported": "no", "draft": "yes", "default_variant_id": 57, "short_description": null, "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 57, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": null, "delivery_price": null, "weight": null}, "relations": []}], "images": [], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 34, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 35, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 36, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}]