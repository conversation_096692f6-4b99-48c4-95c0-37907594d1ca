[{"model": "App\\Models\\Product\\Parameter", "attributes": {"id": 1, "sort": 1, "name": "Μέγεθος", "description": null, "type": "radio", "visible": 1}, "relations": {"options": [{"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 1, "name": "S", "settings": null, "sort": 1, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 2, "name": "M", "settings": null, "sort": 2, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 3, "name": "L", "settings": null, "sort": 3, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 10, "name": "м", "settings": null, "sort": 4, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 11, "name": "XS", "settings": null, "sort": 5, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 12, "name": "35", "settings": null, "sort": 6, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 13, "name": "36", "settings": null, "sort": 7, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 14, "name": "37", "settings": null, "sort": 8, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 15, "name": "38", "settings": null, "sort": 9, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 16, "name": "39", "settings": null, "sort": 10, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 17, "name": "40", "settings": null, "sort": 11, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 18, "name": "41", "settings": null, "sort": 12, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 19, "name": "42", "settings": null, "sort": 13, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 20, "name": "43", "settings": null, "sort": 14, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 21, "name": "44", "settings": null, "sort": 15, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 22, "name": "45", "settings": null, "sort": 16, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}]}}, {"model": "App\\Models\\Product\\Parameter", "attributes": {"id": 2, "sort": 2, "name": "Χρώμα", "description": null, "type": "radio", "visible": 1}, "relations": {"options": [{"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 4, "name": "Чер<PERSON><PERSON>н", "settings": null, "sort": 1, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 5, "name": "<PERSON><PERSON><PERSON><PERSON>н", "settings": null, "sort": 2, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 6, "name": "<PERSON>ин", "settings": null, "sort": 3, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 23, "name": "brown", "settings": null, "sort": 4, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 24, "name": "bordeaux", "settings": null, "sort": 5, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 25, "name": "yellow", "settings": null, "sort": 6, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 26, "name": "μαύρο", "settings": null, "sort": 7, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 27, "name": "ρ<PERSON><PERSON>", "settings": null, "sort": 8, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 28, "name": "λευκό", "settings": null, "sort": 9, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 29, "name": "ριγέ", "settings": null, "sort": 10, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 30, "name": "πράσινο", "settings": null, "sort": 11, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 31, "name": "μπεζ", "settings": null, "sort": 12, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 32, "name": "γκρι", "settings": null, "sort": 13, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}]}}, {"model": "App\\Models\\Product\\Parameter", "attributes": {"id": 3, "sort": 3, "name": "Υλικό", "description": null, "type": "radio", "visible": 1}, "relations": {"options": [{"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 7, "name": "Кожа", "settings": null, "sort": 1, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 8, "name": "Па<PERSON><PERSON>к", "settings": null, "sort": 2, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}, {"model": "App\\Models\\Product\\ParameterOption", "attributes": {"id": 9, "name": "Вел<PERSON>р", "settings": null, "sort": 3, "max_thumb_size": null, "color": null, "visible": 1, "image": null}, "relations": []}]}}]