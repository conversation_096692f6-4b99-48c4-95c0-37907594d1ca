/*=============================================================================*\
    PRODUCT
\*=============================================================================*/

/*  BOX
-------------------------------------------------------------------------------*/

._product {
    @media @hover {
        &:hover ._product-image {
            ._product-image-thumb {
                ._product-image-thumb-holder {
                    img {
                        opacity: .6 !important;
                    }
                }
            }
            ._product-quick-view {
                display: block;
            }
        }
    }
    ._product-image {
        position: relative;
        border-width: 1px;
        border-style: dotted;
        border-color: @color-main-borders; /* theme */
        ._product-image-thumb {
            display: block;
        }
        ._product-quick-view {
            position: absolute;
            right: 10px;
            top: 10px;
            display: none;
            width: 48px;
            height: 48px;
            &:before {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                content: '';
                background-color: @color-button-tertiary-background; /* theme */
                opacity: .8;
            }
            @media @hover {
                &:hover {
                    &:before {
                        background-color: @color-button-tertiary-background-hover; /* theme */
                    }
                    > span {
                        i {
                            color: @color-button-tertiary-text-hover; /* theme */
                        }
                    }
                }
            }
            > span {
                .centerer(true, true);
                i {
                    font-size: 20px;
                    color: @color-button-tertiary-text; /* theme */
                }
            }
        }
        ._product-ribbon-holder {
            position: absolute;
            left: 0;
            top: 10px;
            min-width: 48px;
        }
        ._product-ribbon {
            display: block;
            font-size: calc(@font-size-main ~'-' 4px); /* theme */
            font-weight: 500;
            text-transform: uppercase;
            text-align: center;
            padding: 3px 10px;
            position: relative;
            z-index: 1;
            &:before {
                position: absolute;
                top: 0;
                bottom: 0;
                left: 0;
                right: 0;
                content: '';
                opacity: .8;
                z-index: -1;
            }
            & + ._product-ribbon {
                margin-top: 5px;
            }
            & when (@rtl) {
                white-space: nowrap;
            }
            &._product-ribbon-new {
                color: @color-label-new-text; /* theme */
                &:before {
                    background-color: @color-label-new-background; /* theme */
                }
            }
            &._product-ribbon-sale {
                color: @color-label-sale-text; /* theme */
                &:before {
                    background-color: @color-label-sale-background; /* theme */
                }
            }
            &._product-ribbon-featured {
                color: @color-label-featured-text; /* theme */
                &:before {
                    background-color: @color-label-featured-background; /* theme */
                }
            }
            &._product-ribbon-delivery {
                color: @color-label-delivery-text; /* theme */
                &:before {
                    background-color: @color-label-delivery-background; /* theme */
                }
            }
            &._product-ribbon-custom {
                color: @color-label-custom-text; /* theme */
                &:before {
                    background-color: @color-label-custom-background; /* theme */
                }
            }
            &._product-discount {
                position: absolute;
                left: 0;
                bottom: 10px;
                color: @color-label-discount-text; /* theme */
                &:before {
                    background-color: @color-label-discount-background; /* theme */
                }
            }
            &._product-leasing {
                position: absolute;
                right: 0;
                bottom: 10px;
                color: @color-label-leasing-text; /* theme */
                &:before {
                    background-color: @color-label-leasing-background; /* theme */
                }
            }
        }
        ._product-ribbon-banner {
            position: absolute;
            z-index: 1;
            max-width: 25%;

            img {
                max-width: 100%;
                max-height: 100px;
            }
        }
        ._remove {
            position: absolute;
            right: 10px;
            top: 10px;
            &:before {
                font-size: calc(@font-size-main ~'-' 2px); /* theme */
            }
        }
    }
    ._product-info {
        margin-top: 10px;
        ._product-name {
            padding-top: 3px;
            padding-bottom: 1px;
            ._product-name-tag {
                font-family: @font-family-product-list-title; /* theme */
                font-size: @font-size-product-list-title; /* theme */
                font-weight: @font-weight-product-list-title; /* theme */
                font-style: @font-style-product-list-title; /* theme */
                letter-spacing: normal;
                line-height: 1.5;
                height: 48px;
                overflow: hidden;
                a {
                    display: block;
                }
            }
            & + ._product-description {
                margin-top: 10px;
            }
        }
        ._product-description {
            display: none;
        }
        ._product-meta {
        }
        ._product-bar {
            border-top-width: 1px;
            border-top-style: dotted;
            border-top-color: @color-main-borders; /* theme */
            border-bottom-width: 1px;
            border-bottom-style: dotted;
            border-bottom-color: @color-main-borders; /* theme */
            display: table;
            width: 100%;
            height: 100%;
            margin-top: 10px;
            ._product-bar-col {
                display: table-cell;
                vertical-align: middle;
                font-size: calc(@font-size-main ~'-' 2px); /* theme */
                padding: 10px 0;
                white-space: nowrap;
                height: 100%;
                ._product-add-to-favorite {
                    height: 100%;
                    &.active {
                        color: @color-main-highlight; /* theme */
                    }
                }
                ._product-compare {
                    ._checkbox {
                        line-height: 18px;
                        .checker {
                            top: 0;
                        }
                    }
                }
                &:last-child {
                    text-align: right;
                }
            }
            & + ._product-options {
                margin-top: 15px;
            }
        }
        ._product-countdown {
            margin-top: 10px;
            margin-bottom: 10px;
            text-align: center;

            ._countdown-label {
                display: none;
            }
        }
        ._product-options {
            margin-top: 10px;
            height: 38px;
            display: table;
            width: 100%;
            table-layout: fixed;
            ._product-price {
                display: table-cell;
                vertical-align: middle;
                font-family: @font-family-product-list-price; /* theme */
                font-size: @font-size-product-list-price; /* theme */
                font-weight: @font-weight-product-list-price; /* theme */
                font-style: @font-style-product-list-price; /* theme */
                direction: ltr;
                & when (@rtl) {
                    text-align: left;
                }
                ._product-price-inner {
                    display: inline-block;
                    vertical-align: middle;
                }
                ._product-price-compare {
                    display: block;
                    line-height: 1.2;
                }
                ._product-price-old {
                    display: block;
                    font-size: calc(@font-size-main ~'-' 2px); /* theme */
                    font-weight: @font-weight-main; /* theme */
                }

                ._medal-info {
                    display: inline-block;
                    vertical-align: top;
                }
            }
            ._product-add {
                display: table-cell;
                vertical-align: middle;
                text-align: right;
                padding-left: 10px;
            }
        }
    }
}

/*  LIST
-------------------------------------------------------------------------------*/

/*  Head */

._products-list-head {
    .flexy-row(30px);
    ._products-list-head-item {
        .flexy-col();
        ._products-list-head-item-title {
            h2 {
                .text-overflow();
            }
        }
        ._products-list-head-item-image {
            float: left;
            border-width: 1px;
            border-style: dotted;
            border-color: @color-main-borders; /* theme */
            margin-right: 20px;
            ._products-list-head-item-image-thumb {
                height: 130px;
                width: 130px;
                position: relative;
                margin: 10px;
                img {
                    bottom: 0;
                    height: auto;
                    left: 0;
                    margin: auto;
                    max-height: 100%;
                    max-width: 100%;
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: auto;
                }
            }
        }
    }
    & + ._section-title {
        ._section-separator();
    }
}

/*  DETAILS
-------------------------------------------------------------------------------*/

._product-details-title {
    font-family: @font-family-product-details-title; /* theme */
    font-size: @font-size-product-details-title; /* theme */
    font-weight: @font-weight-product-details-title; /* theme */
    font-style: @font-style-product-details-title; /* theme */
}


/*  Image */

._product-details-image {
    position: relative;
    border: 1px dotted;
    border-color: @color-main-borders; /* theme */
    ._product-details-image-thumb {
        img {
            cursor: pointer;
        }

        .zoomImg {
            max-width: initial;
            max-height: initial;
            transform: none;
        }
    }
    ._product-details-ribbon-holder {
        position: absolute;
        left: 0;
        top: 15px;
        min-width: 52px;
        pointer-events: none;
    }
    ._product-details-ribbon {
        display: block;
        font-size: calc(@font-size-main ~'-' 2px); /* theme */
        font-weight: 500;
        text-transform: uppercase;
        text-align: center;
        padding: 5px 10px;
        pointer-events: none;
        & + ._product-details-ribbon {
            margin-top: 5px;
        }
        & when (@rtl) {
            white-space: nowrap;
        }
        &._product-details-ribbon-new {
            background-color: @color-label-new-background; /* theme */
            color: @color-label-new-text; /* theme */
        }
        &._product-details-ribbon-sale {
            background-color: @color-label-sale-background; /* theme */
            color: @color-label-sale-text; /* theme */
        }
        &._product-details-ribbon-featured {
            background-color: @color-label-featured-background; /* theme */
            color: @color-label-featured-text; /* theme */
        }
        &._product-details-ribbon-delivery {
            background-color: @color-label-delivery-background; /* theme */
            color: @color-label-delivery-text; /* theme */
        }
        &._product-details-ribbon-custom {
            background-color: @color-label-custom-background; /* theme */
            color: @color-label-custom-text; /* theme */
        }
        &._product-details-discount {
            position: absolute;
            left: 0;
            bottom: 15px;
            min-width: 84px;
            padding: 5px 10px;
            background-color: @color-label-discount-background; /* theme */
            color: @color-label-discount-text; /* theme */
            pointer-events: none;
        }
        &._product-details-leasing {
            position: absolute;
            right: 0;
            bottom: 15px;
            padding: 5px 10px;
            background-color: @color-label-leasing-background; /* theme */
            color: @color-label-leasing-text; /* theme */
            pointer-events: none;
        }
    }
    ._product-details-ribbon-banner {
        position: absolute;
        z-index: 1;
        pointer-events: none;
    }
}

/*  Gallery */

._product-details-gallery {
    padding-top: 10px;
    ul {
        list-style: none;
        font-size: 0;
        margin-right: -20px;
        li {
            display: inline-table;
            vertical-align: top;
            margin-top: 20px;
            margin-right: 20px;
            a {
                display: table-cell;
                vertical-align: middle;
                text-align: center;
                height: 172px;
                width: 172px;
                border-width: 1px;
                border-style: dotted;
                border-color: @color-main-borders; /* theme */
            }
        }
    }
}

._product-sidebar {
    ._section-title {
        margin: 30px 0 10px;
    }
}

/*  Tabs */
._product-details-tabs-nav {
    margin-bottom: @separator;
}

._product-details-tabs-nav-link {
    margin-right: 20px;
    color: #aaa; /* theme */

    &:last-child {
        margin-right: 0;
    }

    &.active {
        color: @color-main-titles; /* theme */
    }
}

._product-details-tab {
    display: none;

    &:first-child {
        display: block;
    }
}

/*  Properties */

._product-details-properties-wrapper {
    ._product-details-properties {
        ul {
            list-style: none;
            display: table;
            width: 100%;
            table-layout: fixed;
            border-top: 1px dotted @color-main-borders; /* theme */
            li {
                display: table-row;
                > span {
                    display: table-cell;
                    vertical-align: top;
                    padding: 8px 15px;
                    border-bottom: 1px dotted @color-main-borders; /* theme */
                    &._product-details-properties-title {
                        color: @color-main-meta-text; /* theme */
                    }
                    &._product-details-properties-value {
                        text-align: right;
                        & when (@rtl) {
                            direction: ltr;
                        }
                    }
                }
                &:nth-child(2n+2) {
                    background-color: @color-main-secondary-background; /* theme */
                }
            }
        }
    }
}

/*  Price */

._product-details-price {
    padding-bottom: 30px;
    ._product-details-price-bar {
        min-height: 57px;
        > span {
            display: inline-block;
            &._product-details-price-new {
                font-family: @font-family-product-details-price; /* theme */
                font-size: @font-size-product-details-price; /* theme */
                font-weight: @font-weight-product-details-price; /* theme */
                font-style: @font-style-product-details-price; /* theme */
                & + ._product-details-price-old {
                    margin-left: 10px;
                }
                & when (@rtl) {
                    direction: ltr;
                }
            }
            &._product-details-price-old {
                color: @color-main-meta-text; /* theme */
                text-decoration: line-through;
                > i {
                    font-style: @font-style-main; /* theme */
                    text-decoration: line-through;
                }
            }
        }
    }
    ._product-details-choose {
        display: none;
        color: @color-main-meta-text; /* theme */
    }
    ._product-details-status-and-sku {
        .clearfix();
    }
    ._product-details-sku {
        margin-bottom: 10px;
        > i {
            font-style: @font-style-main; /* theme */
        }
    }
}

._product-details-stock-status-bar {
    display: inline-block;
    padding: 2px 7px;
    margin-bottom: 10px;
    font-size: calc(@font-size-main ~'-' 4px); /* theme */

    &.in-stock {
        background-color: @color-status-instock-background;
        color: @color-status-instock-text;
    }
    &.out-stock {
        background-color: @color-status-outofstock-background;
        color: @color-status-outofstock-text;
    }
    &.two-days {
        background-color: @color-status-twodays-background;
        color: @color-status-twodays-text;
    }
    &.pre-order {
        background-color: @color-status-preorder-background;
        color: @color-status-preorder-text;
    }
    &.request {
        background-color: @color-status-request-background;
        color: @color-status-request-text;
    }

    + ._product-details-sku {
        float: right;
    }
}

._product-details-countdown {
    margin-bottom: 10px;

    ._countdown-icon {
        font-size: calc(@font-size-main ~'+' 8px);
    }

    ._countdown-timer-digit {
        font-size: calc(@font-size-main ~'+' 8px);
    }
}

/*  Parameters */

._product-details-parameters {
    ._product-out-of-stock {
        display: none !important;
        font-size: calc(@font-size-main ~'-' 2px); /* theme */
        color: @color-main-meta-text; /* theme */
    }
    .input-group {
        display: table;
    }
    & + ._product-details-leasing {
        margin-top: 20px;
    }
    ._medal-info {
        margin-top: 20px;
    }
}

._parameter-radio-values {
    margin-bottom: -3px;
    .clearfix();
}

._parameter-radio-value {
    float: left;
    display: table;
    height: 36px;
    min-width: 44px;
    margin-right: 3px;
    margin-bottom: 3px;

    ._radio {
        display: table-cell;
        vertical-align: middle;
        border: 1px solid;
        border-color: @color-main-borders; /* theme */
        padding: 5px;
        text-align: center;
        font-weight: normal;
        font-size: 13px;

        &.active {
            border-width: 2px;
            padding: 4px;
            border-color: @color-main-highlight; /* theme */
        }

        .radio {
            display: none;
        }
    }
}

._parameter-image-values {
    margin-left: -1%;
    margin-bottom: -1%;
    .clearfix();
}

._parameter-image-value {
    float: left;
    width: 24%;
    max-width: 100px;
    border: 1px solid @color-main-borders; /* theme */
    margin-left: 1%;
    margin-bottom: 1%;
    word-break: break-word;

    &.active {
        border-width: 2px;
        border-color: @color-main-highlight; /* theme */
    }

    ._radio {
        display: block;
        height: 0;
        padding: 0;
        padding-bottom: 100%;
        font-size: calc(@font-size-main ~'-' 2px); /* theme */
        position: relative;
        overflow: hidden;

        .radio {
            top: 5px;
            left: 5px;
        }

        .radio-label {
            display: inline-block;
            padding-top: 4px;
            padding-left: 30px;
            padding-right: 5px;
        }

        img {
            .centerer(true, true);

            ~ .radio,
            ~ .radio-label {
                display: none;
            }
        }
    }
}

._parameter-color-values {
    margin-right: -5px;
    margin-bottom: -5px;
    .clearfix();
}

._parameter-color-value {
    float: left;
    margin-right: 5px;
    margin-bottom: 5px;

    &.active {
        ._radio {
            border-color: @color-main-highlight; /* theme */
        }
    }

    ._radio {
        display: block;
        width: 34px;
        height: 34px;
        border: 2px solid transparent;
        padding: 0;
        margin: 0;
        font-size: 0;
        line-height: 0;

        .radio {
            display: none;
        }

        .radio-label {
            display: none;
        }
    }
}

._product-details-instagram-feed {
    ._section-title {
        h2 {
            ._h3();
        }
    }
}

/*	Leasing
*******/

._product-details-leasing {
    font-size: calc(@font-size-main ~'-' 2px); /* theme */
    text-align: right;
}

/*  Meta */

._product-details-meta {
    ul {
        list-style: none;
        display: table;
        width: 100%;
        table-layout: fixed;
        li {
            display: table-row;
            > span {
                display: table-cell;
                vertical-align: top;
                &._product-details-meta-title {
                    color: @color-main-meta-text; /* theme */
                    &:after {
                        content: ':';
                    }
                }
                &._product-details-meta-value {
                    text-align: right;
                    & when (@rtl) {
                        direction: ltr;
                    }
                }
            }
        }
    }
    & + ._product-details-properties {
        margin-top: 15px;
    }
}

/*	Extras
*******/

._product-details-extras {
    border-top: 1px dotted @color-main-borders; /* theme */
    border-bottom: 1px dotted @color-main-borders; /* theme */
    display: table;
    width: 100%;
    margin-top: 15px;
    padding: 15px 0 13px;
    ._product-details-extras-col {
        display: table-cell;
        vertical-align: top;
        font-size: calc(@font-size-main ~'-' 2px); /* theme */
        line-height: 1;
        > * {
            vertical-align: top;
        }
        ._product-details-extras-wishlist {
            line-height: 1;
            display: inline-block;
            > a {
                &.active {
                    color: @color-main-highlight; /* theme */
                }
            }
        }
        ._product-details-extras-compare {
            line-height: 1;
            display: inline-block;
            ._checkbox {
                .checker {
                    top: -1px;
                }
            }
            margin-left: 15px;
        }

    }
}

/*  DETAILS - POPUP
-------------------------------------------------------------------------------*/

.modal-dialog.product-details {
    width: 1180px;
    .modal-body {
        padding-left: 5px;
        padding-right: 5px;
    }
}