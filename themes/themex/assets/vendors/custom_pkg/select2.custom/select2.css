/*=============================================================================*\
    SELECT2
\*=============================================================================*/

/*
Version: 3.5.0 Timestamp: Mon Jun 16 19:29:44 EDT 2014
*/
.select2-container {
	display: inline-block;
	*display: inline;
	margin: 0;
	/* inline-block for ie7 */
	position: relative;
	vertical-align: middle;
	zoom: 1;
}
.select2-container,
.select2-drop,
.select2-search,
.select2-search input {
	/*
	  Force border-box so that % widths fit the parent
	  container without overlap because of margin/padding.
	  More Info : http://www.quirksmode.org/css/box.html
	*/
	box-sizing: border-box; /* webkit */
	-moz-box-sizing: border-box; /* firefox */
	-webkit-box-sizing: border-box; /* css3 */
}
.select2-container .select2-choice {
	background-clip: padding-box;
	background-color: #fff;
	background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.5, #fff));
	background-image: -webkit-linear-gradient(center bottom, #eee 0%, #fff 50%);
	background-image: -moz-linear-gradient(center bottom, #eee 0%, #fff 50%);

	background-image: linear-gradient(to top, #eee 0%, #fff 50%);
	border: 1px solid #aaa;
	border-radius: 4px;
	color: #444;
	display: block;

	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee', GradientType=0);

	height: 26px;

	line-height: 26px;
	overflow: hidden;
	padding: 0 0 0 8px;
	position: relative;
	text-decoration: none;

	white-space: nowrap;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
html[dir="rtl"] .select2-container .select2-choice {
	padding: 0 8px 0 0;
}
.select2-container.select2-drop-above .select2-choice {
	background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.9, #fff));

	background-image: -webkit-linear-gradient(center bottom, #eee 0%, #fff 90%);

	background-image: -moz-linear-gradient(center bottom, #eee 0%, #fff 90%);
	background-image: linear-gradient(to bottom, #eee 0%, #fff 90%);
	border-bottom-color: #aaa;
	border-radius: 0 0 4px 4px;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee', GradientType=0);
}
.select2-container.select2-allowclear .select2-choice .select2-chosen {
	margin-right: 42px;
}
.select2-container .select2-choice > .select2-chosen {
	display: block;
	float: none;
	margin-right: 26px;

	overflow: hidden;

	text-overflow: ellipsis;
	white-space: nowrap;
	width: auto;
}
html[dir="rtl"] .select2-container .select2-choice > .select2-chosen {
	margin-left: 26px;
	margin-right: 0;
}
.select2-container .select2-choice abbr {
	border: 0;
	cursor: pointer;
	display: none;
	font-size: 1px;
	height: 12px;

	outline: 0;
	position: absolute;

	right: 24px;
	text-decoration: none;
	top: 8px;
	width: 12px;
}
.select2-container.select2-allowclear .select2-choice abbr {
	display: inline-block;
}
.select2-container .select2-choice abbr:hover {
	background-position: right -11px;
	cursor: pointer;
}
.select2-drop-mask {
	background-color: #fff;
	border: 0;
	filter: alpha(opacity=0);
	height: auto;
	left: 0;
	margin: 0;
	min-height: 100%;
	min-width: 100%;
	opacity: 0;
	padding: 0;
	position: fixed;
	top: 0;
	/* styles required for IE to work */
	width: auto;
	z-index: 9998;
}
.select2-drop {
	background: #fff;
	border: 1px solid #aaa;
	border-radius: 0 0 4px 4px;
	border-top: 0;
	box-shadow: 0 4px 5px rgba(0, 0, 0, .15);

	-webkit-box-shadow: 0 4px 5px rgba(0, 0, 0, .15);
	color: #000;
	margin-top: -1px;
	position: absolute;

	top: 100%;

	width: 100%;
	z-index: 9999;
}
.select2-drop.select2-drop-above {
	border-bottom: 0;
	border-radius: 4px 4px 0 0;
	border-top: 1px solid #aaa;

	box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);

	-webkit-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);
	margin-top: 1px;
}
.select2-drop-active {
	border: 1px solid #5897fb;
	border-top: none;
}
.select2-drop.select2-drop-above.select2-drop-active {
	border-top: 1px solid #5897fb;
}
.select2-drop-auto-width {
	border-top: 1px solid #aaa;
	width: auto;
}
.select2-drop-auto-width .select2-search {
	padding-top: 4px;
}
.select2-container .select2-choice .select2-arrow {
	background: #ccc;
	background-clip: padding-box;
	background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #ccc), color-stop(0.6, #eee));
	background-image: -webkit-linear-gradient(center bottom, #ccc 0%, #eee 60%);
	background-image: -moz-linear-gradient(center bottom, #ccc 0%, #eee 60%);
	background-image: linear-gradient(to top, #ccc 0%, #eee 60%);

	border-left: 1px solid #aaa;
	border-radius: 0 4px 4px 0;

	display: inline-block;

	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#cccccc', GradientType=0);
	height: 100%;
	position: absolute;
	right: 0;
	top: 0;
	width: 18px;
}
html[dir="rtl"] .select2-container .select2-choice .select2-arrow {
	border-left: none;
	border-radius: 4px 0 0 4px;

	border-right: 1px solid #aaa;
	left: 0;
	right: auto;
}
.select2-container .select2-choice .select2-arrow b {
	display: block;
	height: 100%;
	width: 100%;
}
html[dir="rtl"] .select2-container .select2-choice .select2-arrow b {
	background-position: 2px 1px;
}
.select2-search {
	display: inline-block;
	margin: 0;
	min-height: 26px;
	padding-left: 4px;
	padding-right: 4px;
	position: relative;

	white-space: nowrap;
	width: 100%;

	z-index: 10000;
}
.select2-search input {

	border: 1px solid #aaa;
	border-radius: 0;
	box-shadow: none;

	-webkit-box-shadow: none;
	font-family: sans-serif;

	font-size: 1em;
	height: auto !important;

	margin: 0;
	min-height: 26px;
	outline: 0;
	padding: 4px 20px 4px 5px;
	width: 100%;
}
html[dir="rtl"] .select2-search input {
	padding: 4px 5px 4px 20px;
}
.select2-drop.select2-drop-above .select2-search input {
	margin-top: 4px;
}
.select2-search input.select2-active {
}
.select2-container-active .select2-choice,
.select2-container-active .select2-choices {
	border: 1px solid #5897fb;
	box-shadow: 0 0 5px rgba(0, 0, 0, .3);

	-webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3);
	outline: none;
}
.select2-dropdown-open .select2-choice {
	background-color: #eee;
	background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #fff), color-stop(0.5, #eee));
	background-image: -webkit-linear-gradient(center bottom, #fff 0%, #eee 50%);

	background-image: -moz-linear-gradient(center bottom, #fff 0%, #eee 50%);
	background-image: linear-gradient(to top, #fff 0%, #eee 50%);

	border-bottom-color: transparent;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0;
	box-shadow: 0 1px 0 #fff inset;
	-webkit-box-shadow: 0 1px 0 #fff inset;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0);
}
.select2-dropdown-open.select2-drop-above .select2-choice,
.select2-dropdown-open.select2-drop-above .select2-choices {
	background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #fff), color-stop(0.5, #eee));
	background-image: -webkit-linear-gradient(center top, #fff 0%, #eee 50%);

	background-image: -moz-linear-gradient(center top, #fff 0%, #eee 50%);
	background-image: linear-gradient(to bottom, #fff 0%, #eee 50%);
	border: 1px solid #5897fb;
	border-top-color: transparent;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0);
}
.select2-dropdown-open .select2-choice .select2-arrow {
	background: transparent;
	border-left: none;
	filter: none;
}
html[dir="rtl"] .select2-dropdown-open .select2-choice .select2-arrow {
	border-right: none;
}
.select2-dropdown-open .select2-choice .select2-arrow b {
	background-position: -18px 1px;
}
html[dir="rtl"] .select2-dropdown-open .select2-choice .select2-arrow b {
	background-position: -16px 1px;
}
.select2-hidden-accessible {
	border: 0;
	clip: rect(0 0 0 0);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px;
}
/* results */
.select2-results {
	margin: 4px 4px 4px 0;
	max-height: 200px;
	overflow-x: hidden;
	overflow-y: auto;
	padding: 0 0 0 4px;
	position: relative;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
html[dir="rtl"] .select2-results {
	margin: 4px 0 4px 4px;
	padding: 0 4px 0 0;
}
.select2-results ul.select2-result-sub {
	margin: 0;
	padding-left: 0;
}
.select2-results li {
	background-image: none;
	display: list-item;
	list-style: none;
}
.select2-results li.select2-result-with-children > .select2-result-label {
	font-weight: bold;
}
.select2-results .select2-result-label {
	cursor: pointer;
	margin: 0;
	min-height: 1em;

	padding: 3px 7px 4px;

	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.select2-results-dept-1 .select2-result-label {
	padding-left: 20px
}
.select2-results-dept-2 .select2-result-label {
	padding-left: 40px
}
.select2-results-dept-3 .select2-result-label {
	padding-left: 60px
}
.select2-results-dept-4 .select2-result-label {
	padding-left: 80px
}
.select2-results-dept-5 .select2-result-label {
	padding-left: 100px
}
.select2-results-dept-6 .select2-result-label {
	padding-left: 110px
}
.select2-results-dept-7 .select2-result-label {
	padding-left: 120px
}
.select2-results .select2-highlighted {
	background: #3875d7;
	color: #fff;
}
.select2-results li em {
	background: #feffde;
	font-style: normal;
}
.select2-results .select2-highlighted em {
	background: transparent;
}
.select2-results .select2-highlighted ul {
	background: #fff;
	color: #000;
}
.select2-results .select2-no-results,
.select2-results .select2-searching,
.select2-results .select2-selection-limit {
	background: #f4f4f4;
	display: list-item;
	padding-left: 5px;
}
/*
disabled look for disabled choices in the results dropdown
*/
.select2-results .select2-disabled.select2-highlighted {
	background: #f4f4f4;
	color: #666;
	cursor: default;
	display: list-item;
}
.select2-results .select2-disabled {
	background: #f4f4f4;
	cursor: default;
	display: list-item;
}
.select2-results .select2-selected {
	display: none;
}
.select2-more-results.select2-active {
}
.select2-more-results {
	background: #f4f4f4;
	display: list-item;
}
/* disabled styles */

.select2-container.select2-container-disabled .select2-choice {
	background-color: #f4f4f4;
	background-image: none;
	border: 1px solid #ddd;
	cursor: default;
}
.select2-container.select2-container-disabled .select2-choice .select2-arrow {
	background-color: #f4f4f4;
	background-image: none;
	border-left: 0;
}
.select2-container.select2-container-disabled .select2-choice abbr {
	display: none;
}
/* multiselect */

.select2-container-multi .select2-choices {
	background-color: #fff;
	background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(1%, #eee), color-stop(15%, #fff));
	background-image: -webkit-linear-gradient(top, #eee 1%, #fff 15%);
	background-image: -moz-linear-gradient(top, #eee 1%, #fff 15%);
	background-image: linear-gradient(to bottom, #eee 1%, #fff 15%);

	border: 1px solid #aaa;
	cursor: text;
	height: auto !important;

	height: 1%;
	margin: 0;
	overflow: hidden;
	padding: 0 5px 0 0;
	position: relative;
}
html[dir="rtl"] .select2-container-multi .select2-choices {
	padding: 0 0 0 5px;
}
.select2-locked {
	padding: 3px 5px 3px 5px !important;
}
.select2-container-multi .select2-choices {
	min-height: 26px;
}
.select2-container-multi.select2-container-active .select2-choices {
	border: 1px solid #5897fb;
	box-shadow: 0 0 5px rgba(0, 0, 0, .3);

	-webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3);
	outline: none;
}
.select2-container-multi .select2-choices li {
	float: left;
	list-style: none;
}
html[dir="rtl"] .select2-container-multi .select2-choices li {
	float: right;
}
.select2-container-multi .select2-choices .select2-search-field {
	margin: 0;
	padding: 0;
	white-space: nowrap;
}
.select2-container-multi .select2-choices .select2-search-field input {
	background: transparent !important;
	border: 0;

	box-shadow: none;
	-webkit-box-shadow: none;
	color: #666;
	font-family: sans-serif;
	font-size: 100%;
	margin: 1px 0;
	outline: 0;
	padding: 5px;
}
/*.select2-default {*/
/*color: #999 !important;*/
/*}*/

.select2-container-multi .select2-choices .select2-search-choice {
	background-clip: padding-box;
	background-color: #e4e4e4;
	background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eee));

	background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);
	background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);
	background-image: linear-gradient(to top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);
	border: 1px solid #aaaaaa;

	border-radius: 3px;

	box-shadow: 0 0 2px #fff inset, 0 1px 0 rgba(0, 0, 0, 0.05);
	-webkit-box-shadow: 0 0 2px #fff inset, 0 1px 0 rgba(0, 0, 0, 0.05);

	color: #333;

	cursor: default;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#f4f4f4', GradientType=0);
	line-height: 13px;
	margin: 3px 0 3px 5px;
	padding: 3px 5px 3px 18px;

	position: relative;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
html[dir="rtl"] .select2-container-multi .select2-choices .select2-search-choice {
	margin: 3px 5px 3px 0;
	padding: 3px 18px 3px 5px;
}
.select2-container-multi .select2-choices .select2-search-choice .select2-chosen {
	cursor: default;
}
.select2-container-multi .select2-choices .select2-search-choice-focus {
	background: #d4d4d4;
}
.select2-search-choice-close {
	display: block;
	font-size: 1px;
	height: 13px;
	outline: none;
	position: absolute;

	right: 3px;
	top: 4px;
	width: 12px;
}
html[dir="rtl"] .select2-search-choice-close {
	left: 3px;
	right: auto;
}
.select2-container-multi .select2-search-choice-close {
	left: 3px;
}
html[dir="rtl"] .select2-container-multi .select2-search-choice-close {
	left: auto;
	right: 2px;
}
.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover {
	background-position: right -11px;
}
.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close {
	background-position: right -11px;
}
/* disabled styles */
.select2-container-multi.select2-container-disabled .select2-choices {
	background-color: #f4f4f4;
	background-image: none;
	border: 1px solid #ddd;
	cursor: default;
}
.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice {
	background-color: #f4f4f4;
	background-image: none;
	border: 1px solid #ddd;
	padding: 3px 5px 3px 5px;
}
.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close {
	background: none;
	display: none;
}
/* end multiselect */

.select2-result-selectable .select2-match,
.select2-result-unselectable .select2-match {
	text-decoration: underline;
}
.select2-offscreen, .select2-offscreen:focus {
	border: 0 !important;
	clip: rect(0 0 0 0) !important;
	height: 1px !important;
	left: 0px !important;
	margin: 0 !important;
	outline: 0 !important;
	overflow: hidden !important;
	padding: 0 !important;
	position: absolute !important;
	top: 0px !important;
	width: 1px !important;
}
.select2-display-none {
	display: none;
}
.select2-measure-scrollbar {
	height: 100px;
	left: -10000px;
	overflow: scroll;
	position: absolute;
	top: -10000px;
	width: 100px;
}
/* Retina-ize icons */

@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-resolution: 2dppx) {
	.select2-search input,
	.select2-search-choice-close,
	.select2-container .select2-choice abbr,
	.select2-container .select2-choice .select2-arrow b {
		background-repeat: no-repeat !important;
		background-size: 60px 40px !important;
	}
	.select2-search input {
		background-position: 100% -21px !important;
	}
}
