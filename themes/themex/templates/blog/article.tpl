{$article = W::article()->getArticle(segment(2))}
{$author = W::article()->getAuthor()}
{$tags = W::article()->getTags()}
{$blog = W::article()->getBlog()}
{$dateFormats = \App\Common\DateTimeFormat::getSiteCurrentDateFormat()}
{if !empty($blog)}
    {W::blog()->handle($blog->url_handle)}
{/if}
{$widget->setSeo('article')}
{include file="../layout/header.tpl"}
<!-- BEGIN: content -->
<main class="_content">
    <div class="_breadcrumb-wrapper">
        <div class="container">
            <div class="_section-separator">
                <div class="row">
                    <div class="col-md-12">
                        {if !empty($blog)}
                            {$breadcrumbs = [['name' => "{t}sf.global.act.blog{/t}", "link" => route('blog.list')], ['name' => $blog->name, "link" => $blog->url()]]}
                        {else}
                            {$breadcrumbs = []}
                        {/if}
                        {include file="widgets/common/breadcrumbs.tpl" breadcrumbs=$breadcrumbs active=$article->name}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {if W::blog()->isEnabled()}
        <div class="container">
            <div class="_section-separator">
                <div class="row">
                    <div class="col-md-8">
                        {if $article->active == 'yes'}
                            <div class="_section-title">
                                <h1 class="_h2">{$article->name}</h1>
                            </div>
                            <div class="_blog-main">
                                <div class="_blog-article">
                                    <div class="_blog-article-image">
                                        <img src="{$article->getImage('800x800')}"
                                                alt="{$article->name}" title="{$article->name}">
                                    </div>
                                    <div class="_blog-article-meta">
                                        {if !empty($blog)}
                                            <span class="_blog-article-category">
                                                <a href="{$blog->url()}">{$blog->name}</a>
                                            </span>
                                        {/if}
                                        <span class="_blog-article-author">{$author->username}</span>
                                        <span class="_blog-article-time">
                                            {siteDateTime($article->created_at)}
                                        </span>
                                    </div>
                                    <div class="_blog-article-text">
                                        <div class="_textbox">
                                            {$article->content nofilter}
                                        </div>
                                    </div>
                                    {if !empty($tags)}
                                        <div class="_blog-article-tags">
                                            {include file="widgets/common/tags.tpl" tags=$tags type='blog'}
                                        </div>
                                    {/if}
                                    {if W::article()->commentsAllowed()}
                                        <div class="_section-title">
                                            <h3>{t}sf.article.header.article_comments{/t}</h3>
                                        </div>
                                        <div class="_blog-article-comments">
                                            <div data-ajax-box="{$article->url()}">
                                                {include file="widgets/blog/article/comments.tpl" widget=W::article()}
                                            </div>
                                        </div>
                                        <div class="_section-title">
                                            <h3>{t}sf.global.leave.a.comment{/t}</h3>
                                        </div>
                                        <div class="_blog-article-comments-form">
                                            {include file="widgets/blog/article/comment_form.tpl" widget=W::article()}
                                        </div>
                                    {else}
                                        <div class="_blog-article-comments _section-separator">
                                            <div class="_notification">
                                                <p>{t}sf.article.warn.comments_disabled{/t}</p>
                                            </div>
                                        </div>
                                    {/if}
                                </div>
                            </div>
                        {else}
                            {include file="../notifications/error-include.tpl" error="{t}sf.widget.blog.article.err.article_no_longer_active{/t}"}
                        {/if}
                    </div>
                    <div class="col-md-4">
                        {include file="./sidebar.tpl"}
                    </div>
                </div>
            </div>
        </div>
    {else}
        {include file="../notifications/error-include.tpl" error="{t}sf.widget.blog.err.blog_is_disabled{/t}"}
    {/if}
    <!-- LOAD: seo -->
    {include file="widgets/common/microdata/article.tpl" widget=W::article() article=$article blog=$blog}
    {include file="widgets/common/microdata/breadcrumb.tpl" breadcrumbs=$breadcrumbs active=$article->name}

</main><!--// END: content -->
{include file="../layout/footer.tpl"}