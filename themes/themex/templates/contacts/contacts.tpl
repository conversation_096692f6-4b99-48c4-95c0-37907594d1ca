{include file="../layout/header.tpl"}

<!-- BEGIN: content -->
<main class="_content">
    <div class="_breadcrumb-wrapper">
        <div class="container">
            <div class="_section-separator">
                <div class="row">
                    <div class="col-md-12">
                        {include file="widgets/common/breadcrumbs.tpl" active="{t}sf.global.contacts{/t}"}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="_section-separator">
            <div class="row">
                <div class="col-md-12">
                    <div class="_section-title">
                        <h2>{t}sf.global.contacts{/t}</h2>
                    </div>
                    {include file="widgets/contact/information.tpl" widget=W::contactInformation()}
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="_section-separator">
            <div class="row">
                <div class="col-md-12">
                    {include file="widgets/contact/googleMap.tpl" widget=W::googleMap()}
                </div>
            </div>
        </div>
    </div>
    {if !empty(W::contactInformation()->getSetting('page_text'))}
        <div class="container">
            <div class="_section-separator">
                <div class="row">
                    <div class="col-md-12">
                        <div class="_textbox">
                            {W::contactInformation()->getSetting('page_text') nofilter}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {/if}
    {if W::contactInformation()->getSetting('show_form') == 'yes'}
        <div class="container">
            <div class="_section-separator">
                <div class="row">
                    <div class="col-md-12">
                        <div class="_section-title">
                            <h3>{t}sf.global.feedback{/t}</h3>
                        </div>
                        {include file="widgets/contact/form.tpl" widget=W::contactForm()}
                    </div>
                </div>
            </div>
        </div>
    {/if}
    <!-- LOAD: seo -->
    {include file="widgets/common/microdata/about.tpl" widget=W::contactInformation() widget_map=W::googleMap()}
    {include file="widgets/common/microdata/breadcrumb.tpl" active="{t}sf.global.act.about{/t}"}

</main><!--// END: content -->
{include file="../layout/footer.tpl"}