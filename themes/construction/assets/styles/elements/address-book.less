/*=============================================================================*\
    ADDRESS BOOK
\*=============================================================================*/

._address-book {
    ._address-book-list {
        position: relative;
        overflow: hidden;
        &.hidden {
            display: block !important;
        }
        ul {
            font-size: 0;
            list-style: none;
            margin: -15px;
            li {
                width: 50%;
                display: inline-block;
                font-size: @font-size-main; /* theme */
                padding: 15px;
                vertical-align: top;
                ._address-book-list-item {
                    position: relative;
                    border-width: 2px;
                    border-style: solid;
                    border-color: @color-main-borders; /* theme */
                    // checkout
                    > ._radio {
                        display: block;
                        padding: 25px 30px;
                        .radio {
                            top: 0;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            height: auto;
                            width: auto;
                            > span {
                                background-color: transparent;
                                border-radius: 0;
                                height: 100%;
                                width: 100%;
                                &.checked {
                                    &:before {
                                        top: 0;
                                        bottom: 0;
                                        left: 0;
                                        right: 0;
                                        content: '';
                                        background-color: @color-main-fields-background; /* theme */
                                        width: auto;
                                        height: auto;
                                        border-radius: 0;
                                        transform: translate(0, 0);
                                    }
                                }
                            }
                        }
                        ._address-book-list-item-data {
                            display: block;
                            position: relative;
                            z-index: 1;
                            ._address-book-list-item-data-name {
                                color: @color-main-titles; /* theme */
                                display: block;
                                font-size: calc(@font-size-main ~'+' 2px); /* theme */
                                font-weight: 700;
                                margin-bottom: 8px;
                            }
                            ._address-book-list-item-data-item {
                                display: block;
                                color: @color-main-meta-text; /* theme */
                                font-size: @font-size-main; /* theme */
                                strong {
                                    color: @color-main-text; /* theme */
                                    font-weight: 300;
                                }
                                & + ._address-book-list-item-data-item {
                                    margin-top: 8px;
                                }
                            }
                        }
                    }
                    // address book
                    ._address-book-list-action-mdefault {
                        display: block;
                        padding: 25px 30px;
                        position: relative;
                        ._address-book-list-item-data {
                            display: block;
                            position: relative;
                            z-index: 1;
                            ._address-book-list-item-data-name {
                                color: @color-main-titles; /* theme */
                                display: block;
                                font-size: calc(@font-size-main ~'+' 2px); /* theme */
                                font-weight: 700;
                                margin-bottom: 8px;
                            }
                            ._address-book-list-item-data-item {
                                display: block;
                                color: @color-main-meta-text; /* theme */
                                font-size: @font-size-main; /* theme */
                                strong {
                                    color: @color-main-text; /* theme */
                                    font-weight: 300;
                                }
                                & + ._address-book-list-item-data-item {
                                    margin-top: 8px;
                                }
                            }
                        }
                    }
                    ._address-book-list-item-edit {
                        font-size: calc(@font-size-main ~'-' 1px); /* theme */
                        position: absolute;
                        right: 10px;
                        top: 10px;
                        z-index: 1;
                        @media (min-width: @screen-desktop) {
                            &:hover {
                                color: @color-main-highlight; /* theme */
                            }
                        }
                    }
                    ._address-book-list-actions {
                        font-size: calc(@font-size-main ~'-' 1px); /* theme */
                        position: absolute;
                        right: 10px;
                        top: 10px;
                        z-index: 1;
                        > ._figure-stack {
                            @media (min-width: @screen-desktop) {
                                &:hover {
                                    color: @color-main-highlight; /* theme */
                                }
                            }
                            & + ._figure-stack {
                                margin-left: 5px;
                            }
                        }
                    }
                }
                ._address-book-controls {
                    ._checkbox {
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        position: relative;
                        text-align: center;
                        font-size: calc(@font-size-main ~'+' 2px); /* theme */
                        text-transform: uppercase;
                        color: @color-main-meta-text; /* theme */
                        border-width: 1px;
                        border-style: dashed;
                        border-color: @color-main-borders; /* theme */
                        padding-left: 0;
                        min-height: 224px;
                        user-select: none;
                        &:before {
                            font-family: @font-awesome;
                            content: "\f055";
                            font-size: @font-size-address-book-add-icon;
                        }
                        .checker,
                        input {
                            display: none;
                        }
                        &.active {
                            border-style: solid;
                            border-color: @color-main-highlight; /* theme */
                            color: @color-main-highlight; /* theme */
                            &:before {
                                content: "\f057";
                            }
                        }
                    }
                }
                &.active {
                    ._address-book-list-item {
                        position: relative;
                        border-color: @color-main-highlight; /* theme */
                    }
                }
            }
        }
    }
    ._address-book-add {
        margin-top: 30px;
        ._form {
            ._form-row {
                ._form-col {
                    .region-wrapper {
                        .region-holder {
                            & + .region-holder {
                                margin-top: 15px
                            }
                        }
                    }
                }
                &._form-row-add-phone {
                    margin-top: 15px;
                }
            }
            ._form-row-add-address {
                margin-top: 15px;
            }
        }
    }
}

/*  EDIT
-------------------------------------------------------------------------------*/

._address-book-edit {
    ._form {
        ._form-row {
            ._form-col {
                .region-wrapper {
                    .region-holder {
                        & + .region-holder {
                            margin-top: 15px;
                        }
                    }
                }
            }
        }
    }
}