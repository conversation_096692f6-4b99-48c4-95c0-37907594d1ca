{if Request::ajax() || $include|default}
    <!-- LOAD: popup widgets -->

    {if $include|default}
        {$product = $include}
    {/if}

    {include file="widgets/common/product_popup.tpl"}

    <!-- BEGIN: popup -->
    <div class="_popup _popup-product-details">
        <div class="container-fluid product-details-js" data-product-id="{$product->id}">
            <form class="add-to-cart-form-js js-form-submit-ajax" action="{route('cart.add')}" method="POST">
                <div class="row">
                    <div class="col-md-6">
                        {if $product->is_bundle}
                            <div class="_product-details-bundle js-product-fixed-treshold">
                                {include file=base_path('themes/_global/templates/product/bundle/choose.tpl') display_button=false product=$product quantity_uicontrol="spinner" select_uicontrol="select2"}
                            </div>
                        {else}
                            <div class="_product-details clearfix">
                                <div class="_product-details-thumbs">
                                    <div class="swiper-gallery-holder">
                                        {include file="widgets/product/details/gallery_images_slider.tpl" initSlider=true sliderNavigation=true sliderSlidesPerView="4" sliderDirection="vertical" spaceBetweenItems="10"}
                                    </div>
                                </div>

                                <div class="_product-details-main-image">
                                    {include file="widgets/product/details/primary_image.tpl" zoom_uicontrol="jqueryZoom" custom_labels=true}
                                </div>
                            </div>
                        {/if}
                    </div>
                    <div class="col-md-6">
                        <h2 class="js-product-title">{$product->name}</h2>
                        {if !empty($product->short_description) && W::ProductsDetails('short_product_description')}
                            <div class="_product-details-short-description">
                                {$product->short_description nofilter}
                            </div>
                        {/if}
                        {include file="./sidebar.tpl"}
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!--// END: popup -->
{else}


    {include file="../layout/header.tpl"}
    <!-- BEGIN: content -->
    <div class="_content product-details-js" data-product-id="{$product->id}">
        <form class="add-to-cart-form-js js-form-submit-ajax" action="{route('cart.add')}" method="POST">
            <div class="container">
                <div class="_breadcrumbs-wrapper">
                    <div class="row">
                        <div class="col-sm-12">
                            {include file="widgets/common/breadcrumbs.tpl" breadcrumbs=$product->breadcrumb active=$product->name}
                        </div>
                    </div>
                </div>
            </div>
            <div class="container">
                <div class="_section-separator">
                    <div class="row">
                        <div class="col-md-6">
                            {if $product->is_bundle}
                                <div class="_product-details-bundle js-product-fixed-treshold">
                                    {include file=base_path('themes/_global/templates/product/bundle/choose.tpl') display_button=false product=$product quantity_uicontrol="spinner" select_uicontrol="select2"}
                                </div>
                            {else}
                                <div class="_product-details clearfix">
                                    <div class="_product-details-thumbs">
                                        <div class="swiper-gallery-holder">
                                            {include file="widgets/product/details/gallery_images_slider.tpl" initSlider=true sliderNavigation=true sliderSlidesPerView="6" sliderDirection="vertical" breakpoints="320 480 768 992" itemsPerBreakpoint="2 2 4" spaceBetweenItems="10 10 20 20"}
                                        </div>
                                    </div>

                                    <div class="_product-details-main-image">
                                        {include file="widgets/product/details/primary_image.tpl" zoom_uicontrol="jqueryZoom" custom_labels=true}
                                    </div>
                                </div>
                            {/if}
                        </div>
                        <div class="col-md-6">
                            <h1 class="js-product-title">{$product->name}</h1>
                                {if Apps::installed('product_review') && Apps::enabled('product_review')}
                                    <span class="_rating-line">{include file="_global/templates/product/rating/rating_line.tpl" is_js=true}</span>
                                {/if}
                                {if !empty($product->short_description) && W::ProductsDetails('short_product_description')}
                                    <div class="_product-details-short-description">
                                        {$product->short_description nofilter}
                                    </div>
                                {/if}
                            {include file="./sidebar.tpl" printLabels=true}
                        </div>
                    </div>
                </div>
            </div>


            {if Widget::has('productInBundles') && Widget::get('productInBundles')->isEnabled() && Widget::get('productInBundles')->getBundles()->count()}
                <div class="container _section-separator">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="_section-title">
                                <h2>{t}sf.product.product_in_bundle{/t}</h2>
                            </div>

                            {include file="{base_path('themes/widgets/product/bundleProducts.tpl')}" bundles=Widget::get('productInBundles')}
                        </div>
                    </div>
                </div>
            {/if}

            <div class="container">
                <div class="_section-separator">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="_product-details-desription">
                                <div class="_product-tabber__wrapper">
                                    {capture append="js"}
                                        <script type="text/javascript">
                                            $(window).on('load', function () {
                                                $('._product-tabber__title-row h6 label').on('click touchstart', function (e) {
                                                    var $target = $(e.target);

                                                    $target.parent().addClass('active').siblings().removeClass('active');
                                                })
                                            });
                                        </script>
                                    {/capture}
                                    <div class="_product-tabber__title-row">
                                        {if !empty($product->description) && W::ProductsDetails('show_product_description')}
                                            <h6 class="active">
                                                <label for="_tabber-product-description">{$product->description_title|default:"{t}sf.product.details.description_title{/t}"}</label>
                                            </h6>
                                        {/if}
                                        <h6{if empty($product->description)} class="active"{/if}>
                                            {if W::ProductsDetails('show_brand') || W::ProductsDetails('show_category')}
                                                <label for="_tabber-product-details">{t}sf.global.details{/t}</label>
                                            {/if}
                                        </h6>
                                        {if $product->tabs->count()}
                                            {$i = 0}
                                            {foreach $product->tabs AS $dinamic_tab}
                                                <h6>
                                                    <label for="_tabber-product-details-description-tab-{$dinamic_tab->id}-{$i++}">{$dinamic_tab->name}</label>
                                                </h6>
                                            {/foreach}
                                        {/if}
                                        {if $product->tags->count()}
                                            <h6>
                                                <label for="_tabber-product-tags">{t}sf.global.tags{/t}</label>
                                            </h6>
                                        {/if}
                                        {if $widget->share->isEnabled() && W::ProductsDetails('social_media_share')}
                                            <h6>
                                                <label for="_tabber-product-share">{t}sf.global.share{/t}</label>
                                            </h6>
                                        {/if}
                                        {if W::ProductsDetails('show_page') && W::ProductsDetails()->getInformationPage()}
                                            <h6>
                                                {$page = W::ProductsDetails()->getInformationPage()}
                                                {if W::ProductsDetails('show_link_as_popup')}
                                                    {$popup_url = $page->url()}
                                                    <label><a href="{$popup_url}" data-ajax-panel="true">{$page->name}</a></label>
                                                {else}
                                                    {$page_url = $page->url()}
                                                    <label><a href="{$page_url}">{$page->name}</a></label>
                                                {/if}
                                            </h6>
                                        {/if}
                                        {if !empty($product->size_chart)}
                                            <a href="{route('page', $product->size_chart['url_handle'])}" class="_product-details-size-chart" data-ajax-panel  target="_blank">{$product->size_chart['name']}</a>
                                        {/if}
                                    </div>
                                    <div class="_product-tabber__content-row">
                                        {if !empty($product->description)}
                                            {if W::ProductsDetails('show_product_description')}
                                                <input type="radio" name="tabber-radio" class="_tabber-radio-buttons"
                                                       id="_tabber-product-description" checked>
                                                <div data-article-content data-language="{locale()}">
                                                    <div class="_textbox">
                                                        {$product->description nofilter}
                                                    </div>
                                                </div>
                                            {/if}
                                        {/if}
                                        <input type="radio" name="tabber-radio" class="_tabber-radio-buttons"
                                               id="_tabber-product-details"{if empty($product->description)} checked{/if}>
                                        <div>
                                            <div class="_product-details-meta">
                                                <ul>
                                                    <li>
                                                        <div class="_product-details-sku variant-sku-js hide">{t}sf.global.label.sku{/t}
                                                            <i></i></div>
                                                    </li>
                                                    {if !empty($product->vendor) && W::ProductsDetails('show_brand')}
                                                        <li>
                                                            <span class="_product-details-meta-title">{t}sf.global.label.vendor{/t}</span>
                                                            <span class="_product-details-meta-value">
                                                                <a href="{$product->vendor->url()}">
                                                                    {$product->vendor->name}
                                                                </a>
                                                            </span>
                                                        </li>
                                                    {/if}
                                                    {if !empty($product->category) && W::ProductsDetails('show_category')}
                                                        <li>
                                                            <span class="_product-details-meta-title">{t}sf.global.label.category{/t}</span>
                                                            <span class="_product-details-meta-value">
                                                                <a href="{$product->category->url()}">
                                                                    {$product->category->name}
                                                                </a>
                                                            </span>
                                                        </li>
                                                    {/if}
                                                </ul>
                                            </div>

                                            {if W::ProductsDetails('show_categories_characteristics') && !W::categoryProperties()->getPropertiesForProduct($product)->isEmpty()}
                                                {include file="widgets/product/details/category_properties.tpl" properties=W::categoryProperties()->getPropertiesForProduct($product)}
                                            {/if}
                                        </div>
                                        {if $product->tabs->count()}
                                            {$i = 0}
                                            {foreach $product->tabs AS $dinamic_tab}
                                                <input type="radio" name="tabber-radio" class="_tabber-radio-buttons"
                                                       id="_tabber-product-details-description-tab-{$dinamic_tab->id}-{$i++}">
                                                <div>
                                                    {$hideTags = true}
                                                    {$dinamic_tab->description nofilter}
                                                </div>
                                            {/foreach}
                                        {/if}
                                        {if $product->tags->count()}
                                            <input type="radio" name="tabber-radio" class="_tabber-radio-buttons"
                                                   id="_tabber-product-tags">
                                            <div>
                                                {include file="widgets/common/tags.tpl" tags=$product->tags}
                                            </div>
                                        {/if}
                                        {if $widget->share->isEnabled() && W::ProductsDetails('social_media_share')}
                                            <input type="radio" name="tabber-radio" class="_tabber-radio-buttons"
                                                   id="_tabber-product-share">
                                            <div>
                                                {include file="widgets/extra/share.tpl" widget=W::share()}
                                            </div>
                                        {/if}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {if Apps::installed('product_review') && Apps::enabled('product_review')}
                <div class="container _section-separator _product-review-container js-ratings-trigger">
                    <div class="row">
                        <div class="col-md-12">
                            {$product->product_review['description'] nofilter}
                        </div>
                    </div>
                </div>
            {/if}

            {if Apps::enabled('yotpo')}
                <div class="container">
                    <div class="_section-separator">
                        <div class="row">
                            <div class="col-md-12">
                                {\App\Models\System\AppsManager::getManager('yotpo')->render($product) nofilter}
                            </div>
                        </div>
                    </div>
                </div>
            {/if}
            {if Apps::enabled('facebook_comments')}
                <div class="container">
                    <div class="_section-separator">
                        <div class="row">
                            <div class="col-sm-12">
                                {include file="widgets/product/details/comments-facebook.tpl"}
                            </div>
                        </div>
                    </div>
                </div>
            {/if}
            {if Apps::enabled('disqus_comments')}
                <div class="container">
                    <div class="_section-separator">
                        <div class="row">
                            <div class="col-sm-12">
                                {include file="widgets/product/details/comments-disqus.tpl"}
                            </div>
                        </div>
                    </div>
                </div>
            {/if}
            {if W::productsRelated()->isEnabled() && W::productsRelated()->getProducts($product)->count()}
                <div class="container">
                    <div class="_section-separator">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="_section-title">
                                    <h2>{W::productsRelated()->getHeaderString()}</h2>
                                </div>
                                <div class="_product-details-relative">
                                    {include file="widgets/product/list.tpl" utilities=W::utilities() products=W::productsRelated()->getProducts($product) widget=W::productsRelated() image_size='300x300' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {/if}

            {if Apps::setting('instagram', 'active_in_details_page')}
                <div class="container _section-separator">
                    <div class="row">
                        <div class="col-md-12">
                            {Instagram::images($product) nofilter}
                        </div>
                    </div>
                </div>
            {/if}

            <!-- LOAD: seo -->
            {*
{include file="widgets/common/microdata/product.tpl" related_products_widget=W::productsRelated()}
*}
            {include file="widgets/common/microdata/breadcrumb.tpl" breadcrumbs=$product->breadcrumb active=$product->name}

        </form>
    </div>
    <!--// END: content -->

    {include file="../layout/footer.tpl"}
{/if}
