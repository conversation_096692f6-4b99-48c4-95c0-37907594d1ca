/*=============================================================================*\
    CHECKOUT
\*=============================================================================*/

/* #CHECKOUT ACCOUNT
-------------------------------------------------------------------------------*/

._checkout-account-tabs-wrapper {
    display: table;
    table-layout: fixed;
    width: calc(~'100% + 30px');
    margin: 0 -15px;
}

._checkout-account-tabs {
    display: table-cell;
    vertical-align: top;
    padding: 0 15px;
    .uppercase();

    ._form-row {
        display: block;
        width: auto;
        margin: 0;
    }

    ._form-col {
        display: block;
        border-bottom: 1px solid;
        border-color: @color-main-borders; /* theme */
        width: auto;
        padding: 0;
        padding-bottom: 15px;
        margin-bottom: 15px;
    }
}

._checkout-account-forms {
    display: table-cell;
    vertical-align: top;
    padding: 0 15px;

    ._login,
    ._register {
        max-width: initial;
    }

    ._form-row {
        &:last-child {
            margin-bottom: 0;
        }
    }
    
    ._form-col {
        margin-bottom: @forms-spacing;
    }

    ._button {
        width: 100%;
        display: block;
    }

    ._meta-links {
        display: none;
    }
}

/*  SHIPPING PROVIDERS
-------------------------------------------------------------------------------*/

@shipping-providers-offsets: 15px;

._checkout-step-authorize,
._checkout-step-shipping,
._checkout-step-address {
    margin-bottom: @separator;
}

._checkout-shipping-providers-controls {
    margin-bottom: 15px;
    border-bottom: 2px solid;
    border-color: @color-button-background; /* theme */
    font-size: 0;

    ._radio {
        display: inline-block;
        background-color: @color-second-background; /* theme */
        border-radius: 0;
        color: @color-second-meta-text; /* theme */
        font-size: @font-size-main; /* theme */
        padding: 5px 20px 3px;
        margin-right: 5px;
        .uppercase();

        &:last-child {
            margin-right: 0;
        }

        &.active {
            background-color: @color-button-background; /* theme */
            color: @color-button-text; /* theme */
        }

        .radio {
            display: none;
        }
    }
}

._checkout-shipping-providers-list {
    list-style-type: none;
    margin: 0 -@shipping-providers-offsets -@shipping-providers-offsets;
    font-size: 0;
}

._checkout-shipping-provider {
    display: inline-block;
    vertical-align: top;
    width: calc(33.33% ~'-' @shipping-providers-offsets*2);
    margin: @shipping-providers-offsets;
    font-size: @font-size-main; /* theme */
}

._checkout-shipping-provider-details {
    text-align: center;

    ._radio {
        display: flex;
        align-content: center;
        align-items: center;
        flex-wrap: wrap;
        border: 2px solid;
        border-color: @color-main-borders; /* theme */
        border-radius: 0;
        width: 100%;
        min-height: 95px;
        padding: 11px 20px;
        padding-left: 48px;
        font-size: @font-size-main; /* theme */
        font-weight: bold;
        text-align: center;
        text-transform: none;
        
        &.active {
            background-color: @color-second-background; /* theme */
            border-color: @color-second-highlight; /* theme */
            color: @color-second-text; /* theme */
        }

        .radio {
            .centerer(false, true);
            left: 12px;
        }
    }
}

._checkout-shipping-provider-details-name {
    width: 100%;
}

._checkout-shipping-provider-image {
    width: 100%;
    height: 40px;
    line-height: 40px;
    margin-bottom: 5px;

    img {
        max-height: 100%;
        max-width: 100%;
        vertical-align: middle;
    }
}

._checkout-shipping-provider-services-holder {
    ._section-separator();

    ._radio {
        display: block;
        .uppercase();
    }
}

._checkout-shipping-provider-features {
    margin-top: @separator;
}

._checkout-shipping-provider-feature {
    margin-bottom: 10px;

    &:last-child {
        margin-bottom: 0;
    }
}

._checkout-shipping-provider-features-form {
    .row {
        margin-top: @forms-spacing;
        margin-bottom: 10px;
    }

    ._buttons-col {
        text-align: right;

        ._button {
            &:extend(._button._button-tertiary all);
        }
    }
}

._checkout-shipping-provider-price {
    font-weight: 400;
}

._checkout-shipping-provider-service {
    margin-bottom: 15px;

    &:last-child {
        margin-bottom: 0;
    }
}

._checkout-step-address-billing {
    ._checkout-main {
        margin-bottom: @separator - 30px;
    }
}

._select-billing-address {
    margin-top: 10px;
}

/*  PAYMENT PROVIDERS
-------------------------------------------------------------------------------*/

._checkout-payment-providers {
    ._section-separator();

    ._form-row {
        .flexy-row-mobile(15px);

        ._form-col {
            .flexy-col-mobile();

            ._radio {
                display: block;
                .uppercase();
            }
        }

        &:last-child {
            margin-bottom: 0;
        }
    }
}