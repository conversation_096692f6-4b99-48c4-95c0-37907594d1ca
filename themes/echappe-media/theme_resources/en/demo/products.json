[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 1, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add content information/genre/more information about the author through text, videos or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": null, "image_id": 2, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 2500, "price_to": 2500, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 2, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 2, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 2500, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 2, "name": "Домашни духчета", "parent_id": 1, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "domasni-duhceta-image_620a9d22df0cb.jpeg", "background": "#696969", "width": 600, "height": 930, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 6, "color_id": 44, "percents": 48, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 7, "color_id": 124, "percents": 13, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 8, "color_id": 55, "percents": 7, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 594473}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 1, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 2, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 3, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-5", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add content information/genre/more information about the author through text, videos or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 7, "vendor_id": null, "image_id": 3, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 900, "price_to": 900, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 4, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 4, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 900, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 3, "name": "Годишни времена", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "<PERSON><PERSON><PERSON>-vremena-image_620a9d71d28c8.jpeg", "background": "#800000", "width": 600, "height": 930, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 9, "color_id": 87, "percents": 40, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 10, "color_id": 44, "percents": 19, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 11, "color_id": 12, "percents": 15, "R": 165, "G": 42, "B": 42, "hex": "#A52A2A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 12, "color_id": 38, "percents": 6, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 455002}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 3, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-4", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add content information/genre/more information about the author through text, videos or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 6, "vendor_id": null, "image_id": 4, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 2400, "price_to": 2400, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 6, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 6, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 2400, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 4, "name": "<PERSON><PERSON><PERSON><PERSON> Diablo", "parent_id": 3, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "casa-diablo-image_620a9d979c868.jpeg", "background": "#800000", "width": 600, "height": 930, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 13, "color_id": 87, "percents": 22, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 14, "color_id": 38, "percents": 21, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 15, "color_id": 12, "percents": 21, "R": 165, "G": 42, "B": 42, "hex": "#A52A2A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 16, "color_id": 44, "percents": 18, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 17, "color_id": 146, "percents": 9, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 121268}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 5, "name": "<PERSON><PERSON><PERSON><PERSON> Diablo", "parent_id": 3, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "casa-diablo-image_620a9d98d3d6d.jpeg", "background": "#2F4F4F", "width": 600, "height": 930, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 18, "color_id": 38, "percents": 42, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 19, "color_id": 44, "percents": 22, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 20, "color_id": 146, "percents": 12, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 102051}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 7, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 8, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 9, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 4, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-3", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add content information/genre/more information about the author through text, videos or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 3, "vendor_id": null, "image_id": 6, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 2500, "price_to": 2500, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 8, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 8, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 2500, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 6, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> блус", "parent_id": 4, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "virhov-blus-image_620a9dbde2343.jpeg", "background": "#5F9EA0", "width": 600, "height": 930, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 21, "color_id": 14, "percents": 62, "R": 95, "G": 158, "B": 160, "hex": "#5F9EA0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 22, "color_id": 106, "percents": 7, "R": 255, "G": 165, "B": 0, "hex": "#FFA500"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 23, "color_id": 53, "percents": 6, "R": 218, "G": 165, "B": 32, "hex": "#DAA520"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 300460}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 10, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 11, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 12, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 5, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-2", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add content information/genre/more information about the author through text, videos or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 5, "vendor_id": null, "image_id": 7, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 299, "price_to": 299, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 10, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 10, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 299, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 7, "name": "Сейф (DVD)", "parent_id": 5, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "sejf-dvd-image_620a9f23bd590.jpeg", "background": "#DCDCDC", "width": 600, "height": 930, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 24, "color_id": 50, "percents": 32, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 25, "color_id": 73, "percents": 15, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 26, "color_id": 44, "percents": 13, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 27, "color_id": 130, "percents": 12, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 28, "color_id": 26, "percents": 12, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 29, "color_id": 55, "percents": 7, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 256375}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 13, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 14, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 15, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 6, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-1", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add content information/genre/more information about the author through text, videos or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 4, "vendor_id": null, "image_id": 8, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 12000, "price_to": 12000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 12, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 12, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 12000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 8, "name": "<PERSON> - Fine Line (Vinyl)", "parent_id": 6, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "harry-styles-fine-line-vinyl-image_620a9f534c7b0.jpeg", "background": "#D87093", "width": 600, "height": 930, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 30, "color_id": 112, "percents": 17, "R": 216, "G": 112, "B": 147, "hex": "#D87093"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 31, "color_id": 118, "percents": 16, "R": 176, "G": 224, "B": 230, "hex": "#B0E0E6"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 32, "color_id": 131, "percents": 12, "R": 135, "G": 206, "B": 235, "hex": "#87CEEB"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 33, "color_id": 69, "percents": 10, "R": 240, "G": 128, "B": 128, "hex": "#F08080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 34, "color_id": 122, "percents": 6, "R": 188, "G": 143, "B": 143, "hex": "#BC8F8F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 111599}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 16, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 17, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 18, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 7, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add content information/genre/more information about the author through text, videos or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 5, "vendor_id": null, "image_id": 9, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 999, "price_to": 999, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 14, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 14, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 999, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 9, "name": "Венъм (DVD)", "parent_id": 7, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "venam-dvd-image_620abf210c924.jpeg", "background": "#696969", "width": 600, "height": 930, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 35, "color_id": 44, "percents": 18, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 36, "color_id": 38, "percents": 14, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 37, "color_id": 146, "percents": 12, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 38, "color_id": 1, "percents": 10, "R": 240, "G": 248, "B": 255, "hex": "#F0F8FF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 39, "color_id": 26, "percents": 7, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 40, "color_id": 130, "percents": 6, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 41, "color_id": 50, "percents": 6, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 462695}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 19, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 20, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 21, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}]