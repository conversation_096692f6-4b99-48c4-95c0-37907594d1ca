{"name": "e<PERSON><PERSON>", "version": "0.1", "page_builder": true, "assets": {"default_images": {"logo": "{$img_url}/themes/echappe/img/png/logo.png", "favicon": "", "product": {"50": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "100": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "150": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "300": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "600": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "800": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "1024": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "1280": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "1920": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png"}, "category": {"50": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "100": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "150": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "300": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "600": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "800": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "1024": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "1280": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "1920": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png"}, "vendor": {"50": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "100": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "150": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "300": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "600": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "800": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "1024": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "1280": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png", "1920": "{$img_url}/themes/echappe/img/defaults/noimage/no-image.png"}}, "default_content_images": {"product": ["{$img_url}/themes/echappe/img/defaults/products/product-01.jpg", "{$img_url}/themes/echappe/img/defaults/products/product-02.jpg", "{$img_url}/themes/echappe/img/defaults/products/product-03.jpg", "{$img_url}/themes/echappe/img/defaults/products/product-04.jpg", "{$img_url}/themes/echappe/img/defaults/products/product-05.jpg", "{$img_url}/themes/echappe/img/defaults/products/product-06.jpg", "{$img_url}/themes/echappe/img/defaults/products/product-07.jpg", "{$img_url}/themes/echappe/img/defaults/products/product-08.jpg"], "category": ["{$img_url}/themes/echappe/img/defaults/categories/category-01.jpg", "{$img_url}/themes/echappe/img/defaults/categories/category-02.jpg", "{$img_url}/themes/echappe/img/defaults/categories/category-03.jpg", "{$img_url}/themes/echappe/img/defaults/categories/category-04.jpg"], "vendor": ["{$img_url}/themes/echappe/img/defaults/brands/brand-01.jpg", "{$img_url}/themes/echappe/img/defaults/brands/brand-02.jpg", "{$img_url}/themes/echappe/img/defaults/brands/brand-03.jpg"]}}, "functions": {"discount": {"color": {"status": true}}}, "widgets": {"wishlistMenu": {"map": "wishlist.menu", "name": {"en": "Menu for latest favorites products", "bg": "Меню за последни любими продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "limit": 10}}}, "wishlist": {"map": "wishlist.listing"}, "productCompare": {"map": "product.compare"}, "navigationMain": {"map": "navigation.main"}, "navigationFooter": {"map": "navigation.footer"}, "navigationLinks": {"map": "navigation.links", "name": {"en": "Navigation Links", "bg": "Навигационни връзки"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"links": [{"link_type": "section", "link_value": "blog", "link_caption": "Blog", "target": ""}, {"link_type": "section", "link_value": "contacts", "link_caption": "Contacts", "target": ""}, {"link_type": "section", "link_value": "vendors", "link_caption": "Brands", "target": ""}]}}}, "carousel": {"map": "extra.carousel", "name": {"en": "Slide<PERSON>", "bg": "Слайдер"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 2, "autoplay": "yes", "interval": 5000, "caption": "no", "controls": "yes", "indicators": "no", "pause": "no", "cycle": "yes", "slides": {"1": {"img_type": "external", "src": "{$img_url}/themes/echappe/img/defaults/carousel/slide1.jpg", "link_type": "section", "link_value": "products", "link_caption": "Shop now", "html": "<h1>THE PERFECT<br /><strong><span style=\"color: #f69679;\">POINTE SHOES</span></strong></h1><p>Different workouts demand different kinds of support.&nbsp;<br /> Whether you&rsquo;re in boot camp or barre class,&nbsp;<br /> get the right support for your feet.</p>", "horizontal_position": "left", "vertical_position": "middle", "target": "_self"}, "2": {"img_type": "external", "src": "{$img_url}/themes/echappe/img/defaults/carousel/slide2.jpg", "link_type": "section", "link_value": "products", "link_caption": "Shop now", "html": "<h1>THE PERFECT<br /><strong><span style=\"color: #f69679;\">POINTE SHOES</span></strong></h1><p>Different workouts demand different kinds of support.&nbsp;<br /> Whether you&rsquo;re in boot camp or barre class,&nbsp;<br /> get the right support for your feet.</p>", "horizontal_position": "left", "vertical_position": "middle", "target": "_self"}}}}}, "categoryProperties": {"map": "product.categoryProperties", "name": {}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true}}}, "filters": {"map": "product.filters", "mode": "range_slider", "settings": {"defaults": {"per_row": 3, "per_page": 12, "per_page_options": [12, 24, 48, 96], "products_price_ranges": [[100, 100000]], "price_range_step": "500"}}, "name": {"en": "Product listing settings", "bg": "Настройки на продуктов каталог"}, "description": {"en": "", "bg": ""}}, "productsRelated": {"map": "product.related", "name": {"en": "Similar products", "bg": "Сходни продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Similar products", "type": "tag", "products": 5, "per_row": 5}}}, "productsCombine": {"map": "product.related", "name": {"en": "Combine with", "bg": "Съчетай с"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Combine with", "type": "tag", "per_row": 4, "enabled": false}}}, "linkedProducts": {"map": "product.linked", "name": {"en": "Linked products", "bg": "Свързани продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Linked products", "enabled": true}}}, "productQuantity": {"map": "product.quantity"}, "lastViewedProducts": {"map": "product.lastViewed", "name": {"en": "Latest viewed products", "bg": "Последно видяни продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Last Viewed", "products": 5, "per_row": 5}}}, "showcaseProducts": {"map": "product.productShowcase", "name": {"en": "Products Showcase", "bg": "Витрина с продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "title": "", "products": 2, "per_row": 2}}}, "productsDetails": {"map": "product.productsDetails", "name": {"en": "Detail information for product", "bg": "Детайлна информация за продукт"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {}}}, "showcaseCategory": {"map": "product.showcase", "name": {"en": "Category Showcase", "bg": "Витрина с категории"}, "settings": {"defaults": {"enabled": false, "header": "Categories", "type": "category", "amount": 4, "per_row": 4, "show_name": true, "show_description": true}}}, "showcaseBrand": {"map": "product.showcase", "name": {"en": "Brands Showcase", "bg": "Витрина с марки"}, "settings": {"defaults": {"enabled": false, "header": "Brands", "type": "vendor", "amount": 4, "per_row": 4, "show_name": true, "show_description": true}}}, "homeText1": {"map": "extra.text", "name": {"en": "Homepage text 1", "bg": "Текст на начална страница 1"}, "settings": {"defaults": {"enabled": true, "title": "Our Story", "text": "<p class=\"text-aligncenter\">We believe that we should try to get better professionalists every day and we know that you can help us achieve that! Don&rsquo;t hesitate to contact us, leave us your feedback or share with us any questions or concerns. Just fill the form below and don&rsquo;t forget to check if you type an existing email because otherwise we won&rsquo;t be able to answer you.</p>"}}}, "homeText2": {"map": "extra.text", "name": {"en": "Homepage text 2", "bg": "Текст на начална страница 2"}, "settings": {"defaults": {"enabled": false, "title": "About our products", "text": "<table class=\"table-break\" border='0'><tbody><tr><td>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type</td><td>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type</td></tr></tbody></table>"}}}, "productText": {"map": "extra.text", "name": {"en": "Product details text", "bg": "Текст в детайлите на продукта"}, "settings": {"defaults": {"enabled": false, "title": "", "text": "<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Aspernatur autem dicta fugiat fugit magnam perferendis!</p>"}}}, "page": {"map": "extra.page"}, "htmlLine": {"map": "extra.htmlLine", "group": "top_bar", "name": {"en": "Promo Bar", "bg": "Промо лента"}, "description": {"en": "Promo Bar", "bg": "Промо лента"}, "settings": {"defaults": {"text": "", "enabled": false, "period": {"from": "", "to": ""}, "button": {"float": "right", "enabled": true, "link": "", "target": "_blank", "text": "View More"}}}}, "vendors": {"map": "product.vendors"}, "search": {"map": "extra.search"}, "banners": {"map": "extra.banner", "name": {"en": "Banners", "bg": "Банери"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 1, "per_row": 1, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/echappe/img/defaults/banners/banner-01.jpg", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "logo": {"map": "extra.logo", "editable": "no"}, "userControls": {"map": "user.controls"}, "googleMap": {"map": "contact.googleMap", "name": {"en": "Google Map", "bg": "<PERSON>у<PERSON><PERSON>л карта"}, "description": {"en": "", "bg": ""}}, "contactForm": {"map": "contact.form"}, "contactInformation": {"map": "contact.information", "name": {"en": "Contact information", "bg": "Контактна информация"}, "description": {"en": "", "bg": ""}}, "authorize": {"map": "user.authorize"}, "social": {"map": "extra.social", "name": {"en": "Social links", "bg": ""}, "description": {"en": "", "bg": ""}}, "providers": {"map": "payment.providers"}, "article": {"map": "blog.article"}, "blog": {"map": "blog.blog", "name": {"en": "Blog", "bg": ""}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"per_page": 20}}}, "recentArticles": {"map": "blog.recentArticles", "name": {"en": "Recent articles", "bg": "Последни статии"}, "description": {"en": "", "bg": ""}}, "recentComments": {"map": "blog.recentComments", "name": {"en": "Recent comments", "bg": "Последни коментари"}, "description": {"en": "", "bg": ""}}, "utilities": {"map": "base.utilities"}, "share": {"group": "products", "map": "extra.addThisShare", "name": {"en": "Product share", "bg": "Споделяне на продукт"}, "description": {"en": "This widget allows you to share your products in the social networks.", "bg": "Това разширение активира възможността за споделяне на продукт в социалните мрежи."}}, "shareBlog": {"map": "extra.addThisShare", "name": {"en": "Social media sharing", "bg": "Споделяне в социални мрежи"}, "description": {"en": "This widget allows you to share your blog posts in the social networks.", "bg": "Това разширение активира възможността за споделяне на статия от блога в социалните мрежи."}}, "sharrre": {"map": "extra.sharrreShare"}, "footerText": {"map": "extra.text", "name": {"en": "Footer Text", "bg": "Футър текст"}, "settings": {"defaults": {"title": "First post", "text": "<p>This is your store’s blog. You can use it to talk about new product launches, experiences, tips or other news you want your customers.</p>"}}}, "newsletter": {"map": "mailchimp.newsletter", "settings": {"defaults": {"enabled": false}}}, "leasing": {"map": "store.leasing"}, "checkoutText": {"map": "extra.text", "name": {"en": "Checkout text", "bg": "Завършване на поръчка - текст"}, "settings": {"defaults": {"title": "RETURN AND REFUND", "text": "<p>You will need to arrange the return shipment to us yourself and bear the cost for the courier. Find out more about returns and refunds here. You will not get a fa piao unless you request it. Find out more about here.</p>"}}}, "buttonToTop": {"map": "extra.text", "name": {"en": "<PERSON>ton \"To top\"", "bg": "Бутон \"До горе\""}, "settings": {"defaults": {"enabled": false, "title": "", "text": ""}}}}, "globals": {"widgets": {"product": {"listing": {}, "details": {}, "showcase": {"settings": {"defaults": {"products": 4}}}}}}, "settings": {"groups": {"main": {"en": {"name": "Main"}, "bg": {"name": "Основни"}}, "promo_bar": {"en": {"name": "Promo Bar"}, "bg": {"name": "Промо лента"}}, "header": {"en": {"name": "Header"}, "bg": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "footer": {"en": {"name": "Footer"}, "bg": {"name": "<PERSON>ут<PERSON>р"}}, "topbar": {"en": {"name": "Topbar"}, "bg": {"name": "Заг<PERSON>авна лента"}}, "slider": {"en": {"name": "Slide<PERSON>"}, "bg": {"name": "Слай<PERSON>ър"}}, "products-listing": {"en": {"name": "Products listing"}, "bg": {"name": "Списък с продукти"}}, "brands": {"en": {"name": "Brands"}, "bg": {"name": "Марки"}}, "product-details": {"en": {"name": "Product details"}, "bg": {"name": "Детайли на продукта"}}, "tags": {"en": {"name": "Tags"}, "bg": {"name": "Тагове"}}, "cart": {"en": {"name": "Cart summary"}, "bg": {"name": "Количка"}}, "forms": {"en": {"name": "Forms"}, "bg": {"name": "Форми"}}, "text-boxes": {"en": {"name": "Text boxes"}, "bg": {"name": "Текстови кутийки"}}, "breadcrumb": {"en": {"name": "Breadcrumb"}, "bg": {"name": "Път към страницата"}}, "pagination": {"en": {"name": "Pagination"}, "bg": {"name": "Странициране"}}, "buttons": {"en": {"name": "Buttons"}, "bg": {"name": "Бутони"}}, "labels": {"en": {"name": "Labels"}, "bg": {"name": "Етикети"}}, "popups": {"en": {"name": "Popups"}, "bg": {"name": "Popup-и"}}, "font-main": {"en": {"name": "Main"}, "bg": {"name": "Основни"}}, "font-titles": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}, "font-buttons": {"en": {"name": "Buttons"}, "bg": {"name": "Бутони"}}, "font-product-list": {"en": {"name": "Listing product"}, "bg": {"name": "Продукт в листинга"}}}, "variables": {"color-main-background": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-main-borders": {"type": "color", "group": "main", "default": "#e5e5e5", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Очертания и линии"}}}, "color-main-text": {"type": "color", "group": "main", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-main-secondary-text": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "Secondary text"}, "bg": {"name": "Второстепенен текст"}}}, "color-main-meta-text": {"type": "color", "group": "main", "default": "#b0b0b0", "translations": {"en": {"name": "Meta text"}, "bg": {"name": "Помощен текст"}}}, "color-main-titles": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-main-highlight": {"type": "color", "group": "main", "default": "#f69679", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-main-icons": {"type": "color", "group": "main", "default": "#999", "translations": {"en": {"name": "Icons"}, "bg": {"name": "Иконки"}}}, "color-header-background": {"type": "color", "group": "header", "default": "#eaeaec", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-header-borders": {"type": "color", "group": "header", "default": "#eaeaec", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Очертания и линии"}}}, "color-header-text": {"type": "color", "group": "header", "default": "#333", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-header-hovers": {"type": "color", "group": "header", "default": "#f69679", "translations": {"en": {"name": "Hovers"}, "bg": {"name": "Акцент"}}}, "color-footer-background": {"type": "color", "group": "footer", "default": "#ffffff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-footer-borders": {"type": "color", "group": "footer", "default": "#e5e5e5", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Очертания и линии"}}}, "color-footer-text": {"type": "color", "group": "footer", "default": "#999", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-footer-titles": {"type": "color", "group": "footer", "default": "#404040", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-footer-hovers": {"type": "color", "group": "footer", "default": "#f69679", "translations": {"en": {"name": "Hovers"}, "bg": {"name": "Акцент"}}}, "color-footer-socials-icon": {"separator": "true", "type": "color", "group": "footer", "default": "#666666", "translations": {"en": {"name": "Social icons"}, "bg": {"name": "Социалните иконки"}}}, "color-footer-socials-background": {"type": "color", "group": "footer", "default": "#ebebeb", "translations": {"en": {"name": "Social icons background"}, "bg": {"name": "Фон на социалните иконки"}}}, "color-footer-socials-hover": {"type": "color", "group": "footer", "default": "#f69679", "translations": {"en": {"name": "Social icons hover"}, "bg": {"name": "Акцен на социалните иконки"}}}, "color-topbar-background": {"type": "color", "group": "topbar", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-topbar-borders": {"type": "color", "group": "topbar", "default": "#e5e5e5", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Очертания и линии"}}}, "color-topbar-text": {"type": "color", "group": "topbar", "default": "#333", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-topbar-icons": {"type": "color", "group": "topbar", "default": "#424242", "translations": {"en": {"name": "Icons"}, "bg": {"name": "Иконки"}}}, "color-topbar-hovers": {"type": "color", "group": "topbar", "default": "#f69679", "translations": {"en": {"name": "Hovers"}, "bg": {"name": "Акцент"}}}, "color-topbar-cart-indicator-background": {"separator": "true", "type": "color", "group": "topbar", "default": "#f69679", "translations": {"en": {"name": "Cart indicator background"}, "bg": {"name": "Фон на индикатора на количката"}}}, "color-topbar-cart-indicator-text": {"type": "color", "group": "topbar", "default": "#fff", "translations": {"en": {"name": "Cart indicator text"}, "bg": {"name": "Текст на индикатора на количката"}}}, "color-slider-background": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-slider-text": {"type": "color", "group": "slider", "default": "#333", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-slider-titles": {"type": "color", "group": "slider", "default": "#333", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-slider-button-background": {"type": "color", "group": "slider", "default": "#f69679", "translations": {"en": {"name": "Button background"}, "bg": {"name": "Фон на бутона"}}}, "color-slider-button-text": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Button text"}, "bg": {"name": "Текст на бутона"}}}, "color-slider-arrows": {"separator": "true", "type": "color", "group": "slider", "default": "#333", "translations": {"en": {"name": "Arrows"}, "bg": {"name": "Стрелки"}}}, "color-slider-dots": {"type": "color", "group": "slider", "default": "#333", "translations": {"en": {"name": "Dots"}, "bg": {"name": "Точки"}}}, "color-product-listing-borders": {"type": "color", "group": "products-listing", "default": "#ffffff", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Очертания и линии"}}}, "color-product-listing-title": {"type": "color", "group": "products-listing", "default": "#2b2b2b", "translations": {"en": {"name": "Title"}, "bg": {"name": "Заглавие"}}}, "color-product-listing-text": {"type": "color", "group": "products-listing", "default": "#999999", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-product-listing-price": {"type": "color", "group": "products-listing", "default": "#292929", "translations": {"en": {"name": "Price"}, "bg": {"name": "Цена"}}}, "color-product-listing-price-old": {"type": "color", "group": "products-listing", "default": "#999", "translations": {"en": {"name": "Old price"}, "bg": {"name": "Стара цена"}}}, "color-product-listing-hovers": {"type": "color", "group": "products-listing", "default": "#f69679", "translations": {"en": {"name": "Hovers"}, "bg": {"name": "Акцент"}}}, "color-product-listing-quick-view-background": {"separator": "true", "type": "color", "group": "products-listing", "default": "#fff", "translations": {"en": {"name": "Quick view button background"}, "bg": {"name": "Фон на бутона за бърз преглед"}}}, "color-product-listing-quick-view-icon": {"type": "color", "group": "products-listing", "default": "#f69679", "translations": {"en": {"name": "Quick view button icon"}, "bg": {"name": "Иконка на бутона за бърз преглед"}}}, "color-brands-borders": {"type": "color", "group": "brands", "default": "#e5e5e5", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Очертания"}}}, "color-brands-title": {"type": "color", "group": "brands", "default": "#333", "translations": {"en": {"name": "Title"}, "bg": {"name": "Заглавие"}}}, "color-brands-hovers": {"type": "color", "group": "brands", "default": "#f69679", "translations": {"en": {"name": "Hovers"}, "bg": {"name": "Акцент"}}}, "color-product-images-background": {"type": "color", "group": "product-details", "default": "#fff", "translations": {"en": {"name": "Images background"}, "bg": {"name": "Фон на снимките"}}}, "color-product-images-border": {"type": "color", "group": "product-details", "default": "#ffffff", "translations": {"en": {"name": "Images borders"}, "bg": {"name": "Очертания на снимките"}}}, "color-product-images-active": {"type": "color", "group": "product-details", "default": "#f69679", "translations": {"en": {"name": "Images borders - active"}, "bg": {"name": "Очертания на снимките - акцент"}}}, "color-product-meta-text": {"type": "color", "group": "product-details", "default": "#999", "translations": {"en": {"name": "Meta text"}, "bg": {"name": "Помощен текст"}}}, "color-product-price": {"type": "color", "group": "product-details", "default": "#f69679", "translations": {"en": {"name": "Price"}, "bg": {"name": "Цена"}}}, "color-product-price-old": {"type": "color", "group": "product-details", "default": "#999", "translations": {"en": {"name": "Old price"}, "bg": {"name": "Стара цена"}}}, "color-product-sku": {"type": "color", "group": "product-details", "default": "#999", "translations": {"en": {"name": "SKU"}, "bg": {"name": "Код на продукта"}}}, "color-product-countdown": {"type": "color", "group": "product-details", "default": "#f69679", "translations": {"en": {"name": "Countdown timer"}, "bg": {"name": "Обратно отброяване"}}}, "color-product-category-properties-background": {"separator": "true", "type": "color", "group": "product-details", "default": "#f4f5f6", "translations": {"en": {"name": "Category properties background"}, "bg": {"name": "Свойства на продуктите - фон"}}}, "color-product-category-properties-text": {"type": "color", "group": "product-details", "default": "#666", "translations": {"en": {"name": "Category properties title"}, "bg": {"name": "Свойства на продуктите - име"}}}, "color-product-category-properties-text-value": {"type": "color", "group": "product-details", "default": "#999", "translations": {"en": {"name": "Category properties values"}, "bg": {"name": "Свойства на продуктите - стойност"}}}, "color-product-gallery-background": {"separator": "true", "type": "color", "group": "product-details", "default": "#ffffff", "translations": {"en": {"name": "Lighbox gallery background"}, "bg": {"name": "Popup галерия на продукта"}}}, "color-tags-borders": {"type": "color", "group": "tags", "default": "#e5e5e5", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Очертания"}}}, "color-tags-text": {"type": "color", "group": "tags", "default": "#333", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-tags-hover": {"type": "color", "group": "tags", "default": "#f69679", "translations": {"en": {"name": "Hover"}, "bg": {"name": "Акцент"}}}, "color-cart-background": {"type": "color", "group": "cart", "default": "#f5f5f5", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-cart-borders": {"type": "color", "group": "cart", "default": "#cfcfcf", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Очертания и линии"}}}, "color-cart-text": {"type": "color", "group": "cart", "default": "#5c5c5c", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-cart-meta-text": {"type": "color", "group": "cart", "default": "#5c5c5c", "translations": {"en": {"name": "Meta text"}, "bg": {"name": "Помощен текст"}}}, "color-cart-titles": {"type": "color", "group": "cart", "default": "#383838", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-cart-button-background": {"separator": "true", "type": "color", "group": "cart", "default": "#f69679", "translations": {"en": {"name": "Primary buttons - background"}, "bg": {"name": "Основни бутони - фон"}}}, "color-cart-button-text": {"type": "color", "group": "cart", "default": "#fff", "translations": {"en": {"name": "Primary buttons - text"}, "bg": {"name": "Основни бутони - текст"}}}, "color-forms-fields-background": {"type": "color", "group": "forms", "default": "#fff", "translations": {"en": {"name": "Fields background"}, "bg": {"name": "Фон на полетата"}}}, "color-forms-fields-borders": {"type": "color", "group": "forms", "default": "#e5e5e5", "translations": {"en": {"name": "Fields borders"}, "bg": {"name": "Очертания на полетата"}}}, "color-forms-fields-text": {"type": "color", "group": "forms", "default": "#999", "translations": {"en": {"name": "Fields text"}, "bg": {"name": "Текст на полетата"}}}, "color-forms-fields-placeholder": {"type": "color", "group": "forms", "default": "#999999", "translations": {"en": {"name": "Fields placeholder text"}, "bg": {"name": "Placeholder текст на полетата"}}}, "color-forms-fields-icons": {"type": "color", "group": "forms", "default": "#999", "translations": {"en": {"name": "Fields icons"}, "bg": {"name": "Иконки в полетата"}}}, "color-forms-checkbox": {"separator": "true", "type": "color", "group": "forms", "default": "#e5e5e5", "translations": {"en": {"name": "Checkbox"}, "bg": {"name": "Чекбокс"}}}, "color-forms-checkbox-checked": {"type": "color", "group": "forms", "default": "#333", "translations": {"en": {"name": "Checkbox - checked"}, "bg": {"name": "Чекбокс - избран"}}}, "color-forms-radio": {"separator": "true", "type": "color", "group": "forms", "default": "#e5e5e5", "translations": {"en": {"name": "Radio button"}, "bg": {"name": "Радио бутон"}}}, "color-forms-radio-checked": {"type": "color", "group": "forms", "default": "#333", "translations": {"en": {"name": "Radio button - checked"}, "bg": {"name": "Радио бутон - избран"}}}, "color-forms-range-slider": {"separator": "true", "type": "color", "group": "forms", "default": "#e5e5e5", "translations": {"en": {"name": "Range slider"}, "bg": {"name": "Обхват от стойности"}}}, "color-forms-range-slider-sliders": {"type": "color", "group": "forms", "default": "#333", "translations": {"en": {"name": "Range slider - sliders"}, "bg": {"name": "Обхват от стойности - плъзгачи"}}}, "color-text-box-background": {"separator": "true", "type": "color", "group": "text-boxes", "default": "#f4f5f6", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-text-box-titles": {"type": "color", "group": "text-boxes", "default": "#333", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-text-box-text": {"type": "color", "group": "text-boxes", "default": "#999", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-breadcrumb-background": {"type": "color", "group": "breadcrumb", "default": "#f4f5f6", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-breadcrumb-text": {"type": "color", "group": "breadcrumb", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-breadcrumb-text-hover": {"type": "color", "group": "breadcrumb", "default": "#f69679", "translations": {"en": {"name": "Hover"}, "bg": {"name": "Акцент"}}}, "color-breadcrumb-text-active": {"type": "color", "group": "breadcrumb", "default": "#999", "translations": {"en": {"name": "Active"}, "bg": {"name": "Акти<PERSON><PERSON>н"}}}, "color-pagination-text": {"type": "color", "group": "pagination", "default": "#333", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-pagination-highlight": {"type": "color", "group": "pagination", "default": "#f69679", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-pagination-disabled": {"type": "color", "group": "pagination", "default": "#999", "translations": {"en": {"name": "Disabled"}, "bg": {"name": "Изключен"}}}, "color-promo-bar-text": {"type": "color", "group": "promo_bar", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-promo-bar-background": {"type": "color", "group": "promo_bar", "default": "#101010", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-promo-bar-button-text": {"separator": "true", "type": "color", "group": "promo_bar", "default": "#fff", "translations": {"en": {"name": "Button Text"}, "bg": {"name": "Бутон - текст"}}}, "color-promo-bar-button-text-hover": {"type": "color", "group": "promo_bar", "default": "#fff", "translations": {"en": {"name": "Button Text Hover"}, "bg": {"name": "Бутон - текст - акцент"}}}, "color-promo-bar-button-background": {"type": "color", "group": "promo_bar", "default": "#f69679", "translations": {"en": {"name": "<PERSON>ton Background"}, "bg": {"name": "Бутон - фон"}}}, "color-promo-bar-button-background-hover": {"type": "color", "group": "promo_bar", "default": "#f69679", "translations": {"en": {"name": "Button Background Hover"}, "bg": {"name": "Бутон - фон - акцент"}}}, "color-promo-bar-button-border": {"type": "color", "group": "promo_bar", "default": "#f69679", "translations": {"en": {"name": "Button Border"}, "bg": {"name": "Бутон - рамка"}}}, "color-promo-bar-button-border-hover": {"type": "color", "group": "promo_bar", "default": "#f69679", "translations": {"en": {"name": "Button Border Hover"}, "bg": {"name": "Бутон - рамка - акцент"}}}, "color-promo-bar-close": {"separator": "true", "type": "color", "group": "promo_bar", "default": "#fff", "translations": {"en": {"name": "Close"}, "bg": {"name": "Затвори"}}}, "color-button-background": {"type": "color", "group": "buttons", "default": "#f69679", "translations": {"en": {"name": "Primary - Background"}, "bg": {"name": "Основен - Фон"}}}, "color-button-text": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Primary - Text"}, "bg": {"name": "Основен - Текст"}}}, "color-button-secondary-background": {"separator": "true", "type": "color", "group": "buttons", "default": "#e5e5e5", "translations": {"en": {"name": "Secondary - Background"}, "bg": {"name": "Второстепенен - фон"}}}, "color-button-secondary-text": {"type": "color", "group": "buttons", "default": "#333", "translations": {"en": {"name": "Secondary - Text"}, "bg": {"name": "Второстепенен - Текст"}}}, "color-button-thertiary-background": {"title": {"en": "Thertiary", "bg": "Третостепенен"}, "separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-button-thertiary-borders": {"type": "color", "group": "buttons", "default": "#e8e8e8", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-button-thertiary-text": {"type": "color", "group": "buttons", "default": "#f69679", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-button-thertiary-background-hover": {"type": "color", "group": "buttons", "default": "#f69679", "translations": {"en": {"name": "Hover - background"}, "bg": {"name": "Акцент - фон"}}}, "color-button-thertiary-borders-hover": {"type": "color", "group": "buttons", "default": "#ff7800", "translations": {"en": {"name": "Hover - border"}, "bg": {"name": "Акцент - рамка"}}}, "color-button-thertiary-text-hover": {"type": "color", "group": "buttons", "default": "#fff;", "translations": {"en": {"name": "Hover - text"}, "bg": {"name": "Акцент - текст"}}}, "color-label-new-background": {"title": {"en": "Products", "bg": "Продукти"}, "type": "color", "group": "labels", "default": "#2f2f2f", "translations": {"en": {"name": "NEW - Background"}, "bg": {"name": "НОВ - Фон"}}}, "color-label-new-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "NEW - Text"}, "bg": {"name": "НОВ - Текст"}}}, "color-label-sale-background": {"separator": "true", "type": "color", "group": "labels", "default": "#f69679", "translations": {"en": {"name": "SALE - Background"}, "bg": {"name": "SALE - Фон"}}}, "color-label-sale-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "SALE - Text"}, "bg": {"name": "SALE - Текст"}}}, "color-label-discount-background": {"separator": "true", "type": "color", "group": "labels", "default": "#f69679", "translations": {"en": {"name": "Discount - Background"}, "bg": {"name": "Отстъпка - Фон"}}}, "color-label-discount-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Discount - Text"}, "bg": {"name": "Отстъпка - Текст"}}}, "color-label-free-delivery-background": {"separator": "true", "type": "color", "group": "labels", "default": "#f69679", "translations": {"en": {"name": "Free delivery - Background"}, "bg": {"name": "Безплатна доставка - Фон"}}}, "color-label-free-delivery-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Free delivery - Text"}, "bg": {"name": "Безплатна доставка - Текст"}}}, "color-label-featured-background": {"separator": "true", "type": "color", "group": "labels", "default": "#f69679", "translations": {"en": {"name": "Featured - Background"}, "bg": {"name": "Препоръчан - Фон"}}}, "color-label-featured-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Featured - Text"}, "bg": {"name": "Препоръчан - Текст"}}}, "color-label-custom-background": {"separator": "true", "type": "color", "group": "labels", "default": "#f69679", "translations": {"en": {"name": "Custom label - Background"}, "bg": {"name": "Персонализи<PERSON><PERSON>н - Фон"}}}, "color-label-custom-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Custom label - Text"}, "bg": {"name": "Персонал<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - Текст"}}}, "color-label-date-background": {"title": {"en": "Blog", "bg": "Блог"}, "separator": "true", "type": "color", "group": "labels", "default": "#f69679", "translations": {"en": {"name": "Date - Background"}, "bg": {"name": "Дата - Фон"}}}, "color-label-date-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Date - Text"}, "bg": {"name": "Дата - Текст"}}}, "color-popups-background": {"type": "color", "group": "popups", "default": "#ffffff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-popups-borders": {"type": "color", "group": "popups", "default": "#ededed", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Очертания и линии"}}}, "color-popups-text": {"type": "color", "group": "popups", "default": "#919191", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-popups-titles": {"type": "color", "group": "popups", "default": "#1f1f1f", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-popups-highlight": {"type": "color", "group": "popups", "default": "#f69679", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-popups-button-background": {"separator": "true", "type": "color", "group": "popups", "default": "#f69679", "translations": {"en": {"name": "Primary buttons - background"}, "bg": {"name": "Основни бутони - фон"}}}, "color-popups-button-text": {"type": "color", "group": "popups", "default": "#fff", "translations": {"en": {"name": "Primary buttons - text"}, "bg": {"name": "Основни бутони - текст"}}}, "color-popups-button-secondary-background": {"type": "color", "group": "popups", "default": "#e5e5e5", "translations": {"en": {"name": "Secondary buttons - background"}, "bg": {"name": "Второстепенни бутони - фон"}}}, "color-popups-button-secondary-text": {"type": "color", "group": "popups", "default": "#333", "translations": {"en": {"name": "Secondary buttons - text"}, "bg": {"name": "Второстепенни бутони - текст"}}}, "font-family-main": {"type": "font-family", "group": "font-main", "default": "Montserrat", "translations": {"en": {"name": "Font Family"}, "bg": {"name": "<PERSON>ри<PERSON><PERSON>"}}}, "font-family-secondary": {"type": "font-family", "group": "font-main", "default": "Montserrat", "translations": {"en": {"name": "Font Family Secondary"}, "bg": {"name": "Второстепенен шрифт"}}}, "font-size-main": {"type": "font-size", "group": "font-main", "default": "14px", "translations": {"en": {"name": "Font Size"}, "bg": {"name": "Размер на шрифта"}}}, "font-weight-main": {"type": "font-weight", "group": "font-main", "default": "400", "translations": {"en": {"name": "Font Weight"}, "bg": {"name": "Де<PERSON><PERSON><PERSON>ина на шрифта"}}}, "font-style-main": {"type": "font-style", "group": "font-main", "default": "normal", "translations": {"en": {"name": "Font Style"}, "bg": {"name": "Стил на шрифта"}}}, "font-family-titles": {"type": "font-family", "group": "font-titles", "default": "Montserrat", "translations": {"en": {"name": "Font Family"}, "bg": {"name": "<PERSON>ри<PERSON><PERSON>"}}}, "font-weight-titles": {"type": "font-weight", "group": "font-titles", "default": "300", "translations": {"en": {"name": "Font Weight"}, "bg": {"name": "Де<PERSON><PERSON><PERSON>ина на шрифта"}}}, "font-style-titles": {"type": "font-style", "group": "font-titles", "default": "normal", "translations": {"en": {"name": "Font Style"}, "bg": {"name": "Стил на шрифта"}}}, "font-size-heading-1": {"type": "font-size", "group": "font-titles", "default": "30px", "translations": {"en": {"name": "Heading 1"}, "bg": {"name": "Заглавие 1"}}}, "font-size-heading-2": {"type": "font-size", "group": "font-titles", "default": "24px", "translations": {"en": {"name": "Heading 2"}, "bg": {"name": "Заглавие 2"}}}, "font-size-heading-3": {"type": "font-size", "group": "font-titles", "default": "20px", "translations": {"en": {"name": "Heading 3"}, "bg": {"name": "Заглавие 3"}}}, "font-size-heading-4": {"type": "font-size", "group": "font-titles", "default": "18px", "translations": {"en": {"name": "Heading 4"}, "bg": {"name": "Заглавие 4"}}}, "font-size-heading-5": {"type": "font-size", "group": "font-titles", "default": "16px", "translations": {"en": {"name": "Heading 5"}, "bg": {"name": "Заглавие 5"}}}, "font-size-heading-6": {"type": "font-size", "group": "font-titles", "default": "14px", "translations": {"en": {"name": "Heading 6"}, "bg": {"name": "Заглавие 6"}}}, "font-family-buttons": {"type": "font-family", "group": "font-buttons", "default": "Montserrat", "translations": {"en": {"name": "Font Family"}, "bg": {"name": "<PERSON>ри<PERSON><PERSON>"}}}, "font-size-buttons": {"type": "font-size", "group": "font-buttons", "default": "16px", "translations": {"en": {"name": "Font Size"}, "bg": {"name": "Размер на шрифта"}}}, "font-weight-buttons": {"type": "font-weight", "group": "font-buttons", "default": "400", "translations": {"en": {"name": "Font Weight"}, "bg": {"name": "Де<PERSON><PERSON><PERSON>ина на шрифта"}}}, "font-style-buttons": {"type": "font-style", "group": "font-buttons", "default": "normal", "translations": {"en": {"name": "Font Style"}, "bg": {"name": "Стил на шрифта"}}}, "font-family-product-list-title": {"title": {"en": "Title", "bg": "Заглавие"}, "type": "font-family", "group": "font-product-list", "default": "Montserrat", "translations": {"en": {"name": "Font Family"}, "bg": {"name": "<PERSON>ри<PERSON><PERSON>"}}}, "font-size-product-list-title": {"type": "font-size", "group": "font-product-list", "default": "16px", "translations": {"en": {"name": "Font Size"}, "bg": {"name": "Размер на шрифта"}}}, "font-weight-product-list-title": {"type": "font-weight", "group": "font-product-list", "default": "600", "translations": {"en": {"name": "Font Weight"}, "bg": {"name": "Де<PERSON><PERSON><PERSON>ина на шрифта"}}}, "font-style-product-list-title": {"type": "font-style", "group": "font-product-list", "default": "normal", "translations": {"en": {"name": "Font Style"}, "bg": {"name": "Стил на шрифта"}}}, "image-orientation": {"type": "image", "group": "image", "default": "100%", "translations": {"en": {"name": "Image Orientation"}, "bg": {"name": "Ориентация на снимката"}}}, "image-orientation-width": {"type": "image", "group": "image", "default": "1", "translations": {"en": {"name": "<PERSON><PERSON><PERSON>"}, "bg": {"name": "Ши<PERSON><PERSON><PERSON>"}}}, "image-orientation-height": {"type": "image", "group": "image", "default": "1", "translations": {"en": {"name": "Height"}, "bg": {"name": "Висо<PERSON>ина"}}}, "custom-css-js": {"type": "custom", "group": "custom", "default": "<style>._logo img {max-height: 35px;}._logo {width: 210px;}._navigation li, ._navigation-footer li a {font-size: 13px!important;}</style>", "translations": {"en": {"name": "Custom CSS"}, "bg": {"name": "Custom CSS"}}}}}}