{include file="../layout/header.tpl"}

{$widget->utilities->homeRedirect()}
{$widget->setSeo("utilities")}

<!-- BEGIN: content -->
<div class="_content">
	{include file="widgets/extra/carousel.tpl" widget=$widget->carousel}

	{$widgetText1 = $widget->homeText1}
	{if $widgetText1->isEnabled()}
		<div class="container _section-separator-large">
			<div class="row">
			    <div class="col-md-12">
					<div class="_home-text">
						{include file="widgets/extra/text.tpl" widget=$widgetText1}
					</div>
				</div>
			</div>
		</div>
    {/if}

	{if $widget->showcaseProducts->isEnabled()}
		<div class="container _section-separator">
			<div class="row">
				<div class="col-md-12">
					{include file="widgets/product/productShowcase.tpl" custom_labels=true product_bar=true showcase=$widget->showcaseProducts image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
				</div>
			</div>
		</div>
	{/if}

	{if $widget->showcaseCategory->isEnabled()}
		<div class="container _section-separator">
			<div class="row">
				<div class="col-md-12">
					{include file="widgets/product/showcase.tpl" showcase=$widget->showcaseCategory image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
				</div>
			</div>
		</div>
	{/if}

    {$widgetText2 = $widget->homeText2}
    {if $widgetText2->isEnabled()}
		<div class="container _section-separator-large">
			<div class="row">
				<div class="col-md-12">
					<div class="_home-text">
						{include file="widgets/extra/text.tpl" widget=$widgetText2}
					</div>
				</div>
			</div>
		</div>
    {/if}

	{if $widget->showcaseBrand->isEnabled()}
		<div class="container _section-separator">
			<div class="row">
				<div class="col-md-12">
					{include file="widgets/product/showcase.tpl" showcase=$widget->showcaseBrand image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
				</div>
			</div>
		</div>
	{/if}

	{if $widget->banners->isEnabled()}
		<div class="_home-banners">
			<div class="row">
				<div class="col-md-12">
					{include file="widgets/extra/banner.tpl" widget=$widget->banners}
				</div>
			</div>
		</div>
	{/if}

</div><!--// END: content -->
{include file="../layout/footer.tpl"}