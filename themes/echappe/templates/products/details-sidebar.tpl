<div class="_product-sidebar js-product-details-sidebar">
	<div class="_product-details-title sm-hidden">
		<div class="_h1 js-product-title">{$product->name}</div>
	</div>

	{if W::ProductsDetails('short_product_description') && !empty($product->short_description)}
		<div class="_product-details-short-description sm-hidden">
			<p>{$product->short_description nofilter}</p>
		</div>
	{/if}

	<div class="_product-details-price product-details-price-js{if $product->price_from_discounted} has-discount{/if}">
		{if W::ProductsDetails('show_price') && showPriceForUser()}
			<div class="_product-details-price-bar">
                {if $product->is_bundle}
                    {$price = $product->price_from_formatted}
                    {$price_old = $product->products_price_from_formatted}
                    {$price_save = money($product->products_price_from - $product->price_from)}
                {else}
                    {if !is_null($product->price_from_discounted)}
                        {$price = $product->price_from_discounted_formatted}
                        {$price_old = $product->price_from_formatted}
                        {$price_save = $product->price_from_saved_formatted}
                    {else}
                        {$price = $product->price_from_formatted}
                    {/if}
                {/if}

				<span class="_product-details-price-new price-new-js">{$price}</span>
				<span class="_product-details-price-old price-old-js"><i class="rtl-ltr">{$price_old|default}</i></span>
			</div>
		{/if}
		{if W::ProductsDetails('show_SKU')}
			<div class="_product-details-sku variant-sku-js {if !($product->variant->sku|default)}hide{/if}">{t}sf.global.label.sku{/t} <i>{$product->variant->sku|default}</i></div>
		{/if}
	</div>

	{if W::ProductsDetails('show_product_status')}
		<span class="_product-details-stock-status-bar js-status-bar {if $product->total_variants}hide{/if} {$product->status_type|replace:'_':'-'}" data-status="{$product->status_type|replace:'_':'-'}">{$product->status_name}</span>
	{/if}

	{if Apps::installed('product_review') && Apps::enabled('product_review')}
		<div class="_rating-line _mb-20">{include file="_global/templates/product/rating/rating_line.tpl" is_js=true}</div>
	{/if}

    {if !empty($product->description) && W::ProductsDetails('show_product_description')}
        <div class="_product-details-description">
            <div class="_product-details-description js-tabs">
                <div class="_product-details-description-tabs-nav">
                    <a class="_product-details-description-tabs-nav-link js-tabs-link" href="#product-details-description-{$product->id}">{$product->description_title|default:"{t}sf.global.label.description{/t}"}</a>

                    {if $product->tabs->count()}
						{$i = 0}
                        {foreach $product->tabs AS $dinamic_tab}
                            <a class="_product-details-description-tabs-nav-link js-tabs-link" href="#product-details-dynamic-tab-{$dinamic_tab->id}-{$i++}">{$dinamic_tab->name}</a>
                        {/foreach}
                    {/if}

                    <a class="_product-details-description-tabs-nav-link js-tabs-link" href="#product-details-meta">{t}sf.global.label.info{/t}</a>
                </div>

                <div class="_product-details-description-tabs">
                    <div class="_product-details-description-tab js-tab" id="product-details-description-{$product->id}">
                        <div class="_textbox" data-article-content data-language="{locale()}">
                            {$product->description nofilter}
                        </div>
                    </div>

                    {if $product->tabs->count()}
						{$i = 0}
                        {foreach $product->tabs AS $dinamic_tab}
                            <div class="_product-details-description-tab js-tab" id="product-details-dynamic-tab-{$dinamic_tab->id}-{$i++}" style="display: none;">
                                <div class="_textbox" data-article-content data-language="{locale()}">
                                    {$dinamic_tab->description nofilter}
                                </div>
                            </div>
                        {/foreach}
                    {/if}

                    <div class="_product-details-description-tab js-tab" id="product-details-meta" style="display: none;">
                        <div class="_product-details-meta">
                            <ul>
                                {if !empty($product->category) && W::ProductsDetails('show_category')}
                                    <li>
                                        <span class="_product-details-meta-title">{t}sf.global.label.category{/t}</span>

                                        <span class="_product-details-meta-value">
                                            <a href="{$product->category->url()}">
                                                {$product->category->name}
                                            </a>
                                        </span>
                                    </li>
                                {/if}

                                {if !empty($product->vendor) && W::ProductsDetails('show_brand')}
                                    <li>
                                        <span class="_product-details-meta-title">{t}sf.global.label.vendor{/t}</span>

                                        <span class="_product-details-meta-value">
                                            <a href="{$product->vendor->url()}">
                                                {$product->vendor->name}
                                            </a>
                                        </span>
                                    </li>
                                {/if}

                                {if $product->publicFiles && $product->publicFiles->count()}
                                    {foreach from=$product->publicFiles item=file}
                                        <li>
                                            <span class="_product-details-meta-title">{t}sf.global.files.for.download{/t}</span>
                                            <span class="_product-details-meta-value"><a target="_blank" href="{Linker::product_file($product->url_handle, $file->mask)}">{$file->name}</a></span>
                                        </li>
                                    {/foreach}
                                {/if}

                                {if W::ProductsDetails('show_page')}
                                    {$page = W::ProductsDetails()->getInformationPage()}
                                    {if $page}
                                        <li>
                                            {if W::ProductsDetails('show_link_as_popup')}
                                                {$popup_url = $page->url()}
                                                <span class="_product-details-meta-value"><a href="{$popup_url}" data-ajax-panel="true">{$page->name}</a></span>
                                            {else}
                                                {$page_url = $page->url()}
                                                <span class="_product-details-meta-value"><a href="{$page_url}">{$page->name}</a></span>
                                            {/if}
                                        </li>
                                    {/if}
                                {/if}
								{if !empty($product->size_chart)}
									<li>
										<a href="{route('page', $product->size_chart['url_handle'])}" class="_product-details-size-chart" data-ajax-panel  target="_blank">{$product->size_chart['name']}</a>
									</li>
								{/if}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {/if}

	{if $product->countDownDetails}
		<div class="_product-details-countdown">
			{include file="widgets/common/countdown.tpl" endDate="{$product->countDownDetails}"}
		</div>
	{/if}

	<div class="_product-details-choose choose-variant-msg-js hide">{t}sf.global.nfy.choose_variant_first_pls{/t}</div>

	<div class="_product-details-parameters">
		{if Widget::has('product.linked') && Widget::get('product.linked')->getSetting('position') == 'variant'}
			{include file="widgets/product/linked-products.tpl" linkedProducts=Widget::get('product.linked')->getProducts($product)}
		{/if}
		{include file="widgets/product/details/choose_variant.tpl" quantity_uicontrol="spinner" select_uicontrol="select2" printLabels=true tooltips=true}
	</div>

	{if !Request::ajax()}
		{if  W::leasing()->getActivePaymentProvidersForProduct($product)->count()}
			<div class="_product-details-leasing-button">
				<a id="credit-calculator-js" href="{route('creditor.select', $product->id)}" data-url="{route('creditor.select', $product->id)}" data-ajax-panel>
					<span class="_figure-stack">
						<span class="_figure-stack-label">{t}sf.leasing.btn.calculator{/t}</span>
					</span>
				</a>
			</div>
			<div class="_textbox">
				{W::leasing()->getPromoHtml($product) nofilter}
			</div>
			<div class="credtor_button">
				{W::leasing()->getPromoButton($product) nofilter}
			</div>
		{/if}
		{foreach $product->labels|default:[] AS $label}
			{if !empty($label->description)}
				<div class="_textbox">{$label->description nofilter}</div>
			{/if}
		{/foreach}
		{foreach $product->banners|default:[] AS $_banner}
			{if !empty($_banner->description)}
				<div class="_textbox">{$_banner->description nofilter}</div>
			{/if}
		{/foreach}
	{/if}

	{if Widget::has('product.linked') && Widget::get('product.linked')->getSetting('position') == 'recommended'}
		{include file="widgets/product/linked-products.tpl" linkedProducts=Widget::get('product.linked')->getProducts($product)}
	{/if}

	{include file="_global/templates/product/discounts/volume.tpl" product=$product}

	{$productText = $widget->productText}
	{if $productText->isEnabled()}
		<div class="_product-details-text" style="margin-bottom: 20px;">
			{include file="widgets/extra/text.tpl" widget=$productText}
		</div>
	{/if}

	{if W::ProductsDetails('show_categories_characteristics') && !W::categoryProperties()->getPropertiesForProduct($product)->isEmpty()}
		{include file="widgets/product/details/category_properties.tpl" properties=W::categoryProperties()->getPropertiesForProduct($product)}
	{/if}

	{$share_enabled = $widget->share->isEnabled() && W::ProductsDetails('social_media_share')}
	{$compare_enabled = Widget::has('productCompare') && W::ProductsDetails('show_compare')}
	{$wishlist_enabled = W::ProductsDetails('show_wishlist')}
	{$tags_enabled = $product->tags->count()}
	{if $share_enabled || $compare_enabled || $wishlist_enabled || $tags_enabled}
		<div class="_product-sidebar-actions">
			{if $tags_enabled}
				{include file="widgets/common/tags.tpl" tags=$product->tags}
			{/if}

			{if $share_enabled || $wishlist_enabled || $compare_enabled}
				<div class="_product-details-actions">
					{if $share_enabled}
						<div class="_product-details-share js-product-share">
							<a class="_button _button-secondary _button-small js-button-share" href="#"><i class="fa fa-share-alt"></i></a>

							<div class="_product-details-share-holder">
								{include file="widgets/extra/share.tpl" widget=W::share()}
							</div>
						</div>
					{/if}

					{if $wishlist_enabled}
						<div class="_product-details-wishlist">
							<a href="#" data-id="{$product->id}" data-widget="product-wishlist" class="_button _button-secondary _button-small _product-details-wishlist-button{if $product->favorite} active{/if}">
								<i class="fa fa-heart"></i>
							</a>
						</div>
					{/if}

					{if $compare_enabled}
						<div class="_product-details-compare">
							{include file="widgets/compare/add.tpl" product=$product}
						</div>
					{/if}
				</div>
			{/if}
		</div>
	{/if}
</div>