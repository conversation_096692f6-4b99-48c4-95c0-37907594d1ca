{"devDependencies": {"grunt": "^0.4.5", "grunt-cleanempty": "^1.0.3", "grunt-concat-css": "^0.3.1", "grunt-contrib-clean": "^0.7.0", "grunt-contrib-concat": "^0.5.1", "grunt-contrib-copy": "^0.8.2", "grunt-contrib-csslint": "^0.5.0", "grunt-contrib-cssmin": "^0.14.0", "grunt-contrib-imagemin": "^1.0.0", "grunt-contrib-jshint": "^0.11.3", "grunt-contrib-less": "^1.1.0", "grunt-contrib-uglify": "^0.11.0", "grunt-contrib-watch-chokidar": "^1.0.0", "grunt-cssbeautifier": "^0.1.9", "grunt-dev-update": "^1.4.0", "grunt-embedfont": "^0.2.6", "grunt-google-fonts": "^0.3.0", "grunt-jsbeautifier": "^0.2.10", "grunt-json-generator": "^0.1.0", "grunt-modernizr-builder": "^0.1.7", "grunt-mkdir": "^0.1.2", "grunt-newer": "^1.1.1", "grunt-open": "^0.2.3", "grunt-prettify": "^0.4.0", "grunt-shell-spawn": "^0.3.10", "grunt-spritesmith": "^6.1.0", "grunt-svg-sprite": "^1.2.15", "grunt-svgmin": "^3.1.0", "grunt-targethtml": "^0.2.6", "grunt-template": "^0.2.3", "grunt-text-replace": "^0.4.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-inline-urls": "^1.1.0", "less-plugin-rtl": "^1.0.0", "less-plugin-theme": "0.1.1", "load-grunt-config": "^0.19.1", "time-grunt": "^1.2.2"}, "private": true}