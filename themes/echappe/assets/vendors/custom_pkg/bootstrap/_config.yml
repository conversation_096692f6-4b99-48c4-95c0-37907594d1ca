# Dependencies
markdown:         kramdown
highlighter:      rouge

# Permalinks
permalink:        pretty

# Server
source:           docs
destination:      _gh_pages
host:             0.0.0.0
port:             9001
url:              http://getbootstrap.com
encoding:         UTF-8

gems:
  - jekyll-sitemap

# Custom vars
current_version:  3.3.7
repo:             https://github.com/twbs/bootstrap
sass_repo:        https://github.com/twbs/bootstrap-sass

download:
  source:         https://github.com/twbs/bootstrap/archive/v3.3.7.zip
  dist:           https://github.com/twbs/bootstrap/releases/download/v3.3.7/bootstrap-3.3.7-dist.zip
  sass:           https://github.com/twbs/bootstrap-sass/archive/v3.3.7.tar.gz

blog:             http://blog.getbootstrap.com
expo:             http://expo.getbootstrap.com
themes:           http://themes.getbootstrap.com

cdn:
  # See https://www.srihash.org for info on how to generate the hashes
  css:            https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css
  css_hash:       "sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u"
  css_theme:      https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap-theme.min.css
  css_theme_hash: "sha384-rHyoN1iRsVXV4nD0JutlnGaslCJuC7uwjduW9SVrLvRYooPp2bWYgmgJQIXwl/Sp"
  js:             https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js
  js_hash:        "sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa"
