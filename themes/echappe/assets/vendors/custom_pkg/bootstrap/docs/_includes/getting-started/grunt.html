<div class="bs-docs-section">
  <h1 id="grunt" class="page-header">Compiling CSS and JavaScript</h1>

  <p class="lead">Bootstrap uses <a href="http://gruntjs.com">Grunt</a> for its build system, with convenient methods for working with the framework. It's how we compile our code, run tests, and more.</p>

  <h2 id="grunt-installing">Installing Grunt</h2>
  <p>To install Grunt, you must <strong>first <a href="https://nodejs.org/download/">download and install node.js</a></strong> (which includes npm). npm stands for <a href="https://www.npmjs.com/">node packaged modules</a> and is a way to manage development dependencies through node.js.</p>

  Then, from the command line:
  <ol>
    <li>Install <code>grunt-cli</code> globally with <code>npm install -g grunt-cli</code>.</li>
    <li>Navigate to the root <code>/bootstrap/</code> directory, then run <code>npm install</code>. npm will look at the <a href="https://github.com/twbs/bootstrap/blob/master/package.json"><code>package.json</code></a> file and automatically install the necessary local dependencies listed there.</li>
  </ol>

  <p>When completed, you'll be able to run the various Grunt commands provided from the command line.</p>

  <h2 id="grunt-commands">Available Grunt commands</h2>
  <h3><code>grunt dist</code> (Just compile CSS and JavaScript)</h3>
  <p>Regenerates the <code>/dist/</code> directory with compiled and minified CSS and JavaScript files. As a Bootstrap user, this is normally the command you want.</p>

  <h3><code>grunt watch</code> (Watch)</h3>
  <p>Watches the Less source files and automatically recompiles them to CSS whenever you save a change.</p>

  <h3><code>grunt test</code> (Run tests)</h3>
  <p>Runs <a href="http://jshint.com">JSHint</a> and runs the <a href="http://qunitjs.com">QUnit</a> tests headlessly in <a href="http://phantomjs.org">PhantomJS</a>.</p>

  <h3><code>grunt docs</code> (Build &amp; test the docs assets)</h3>
  <p>Builds and tests CSS, JavaScript, and other assets which are used when running the documentation locally via <code>bundle exec jekyll serve</code>.</p>

  <h3><code>grunt</code> (Build absolutely everything and run tests)</h3>
  <p>Compiles and minifies CSS and JavaScript, builds the documentation website, runs the HTML5 validator against the docs, regenerates the Customizer assets, and more. Requires <a href="http://jekyllrb.com/docs/installation/">Jekyll</a>. Usually only necessary if you're hacking on Bootstrap itself.</p>

  <h2 id="grunt-troubleshooting">Troubleshooting</h2>
  <p>Should you encounter problems with installing dependencies or running Grunt commands, first delete the <code>/node_modules/</code> directory generated by npm. Then, rerun <code>npm install</code>.</p>
</div>
