/*=============================================================================*\
    ORDER DETAILS
\*=============================================================================*/

._tooltips {
	position: relative;

	@media @hover {
		._tooltip {
			display: block;
		}

		&:hover {
			._tooltip {
				opacity: 1;
				visibility: visible;
			}
		}
	}
}

._tooltip {
	display: none;
	width: 200px;
	font-size: calc(@font-size-main ~'-' 3px); /* theme */
	color: @color-popups-highlight; /* theme */
	text-align: center;
	.centerer(true, false);
	bottom: calc(100% ~'+' 8px);
	z-index: 10;
	opacity: 0;
	visibility: hidden;
	transition: .2s;

	p {
		margin-bottom: 0;
	}
}

._tooltip-inner {
	display: inline-block;
	background-color: @color-popups-background; /* theme */
	border: 1px solid;
	border-color: @color-popups-borders; /* theme */
	border-radius: 5px;
	padding: 5px 12px;
	position: relative;

	&:before,
	&:after {
		content: '';
		position: absolute;
		top: 100%;
		border-style: solid;
		border-color: transparent;
		.centerer(true, false);
	}

	&:before {
		border-width: 8px 8px 0 8px;
		border-top-color: @color-popups-borders; /* theme */
		z-index: 0;
	}

	&:after {
		border-width: 7px 7px 0 7px;
		border-top-color: @color-popups-background; /* theme */
		z-index: 1;
	}
}