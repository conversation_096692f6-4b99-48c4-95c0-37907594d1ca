/*=============================================================================*\
    VARIABLES
\*=============================================================================*/

/*  FONTS
-------------------------------------------------------------------------------*/

/*  Fonts changeble */
@font-family-main: 'Montserrat', sans-serif;
@font-family-secondary: 'Montserrat', sans-serif;
@font-family-titles: 'Montserrat', sans-serif;
@font-family-buttons: 'Montserrat', sans-serif;
@font-family-product-list-title: 'Montserrat', sans-serif;

@font-size-main: 14px;
@font-size-buttons: 16px;
@font-size-heading-1: 30px;
@font-size-heading-2: 24px;
@font-size-heading-3: 20px;
@font-size-heading-4: 18px;
@font-size-heading-5: 16px;
@font-size-heading-6: 14px;
@font-size-product-list-title: 16px;

@font-weight-main: 400;
@font-weight-titles: 300;
@font-weight-buttons: 400;
@font-weight-product-list-title: 600;

@font-style-main: normal;
@font-style-titles: normal;
@font-style-buttons: normal;
@font-style-product-list-title: normal;


/*  Family */
@font-awesome: 'FontAwesome', sans-serif;
@glyphicons: 'Glyphicons Halflings', sans-serif;

/*  Size */
@font-size-xsmall: 10px;
@font-size-nav: 16px;
@font-size-forms: 14px;
@font-size-slider-nav: 68px;
@font-size-notification-icon: 32px;
@font-size-loader-list: 50px;
@font-size-promo-bar-close: 16px;

/*  Line height */
@line-height-base: 1.72;
@line-height-medium: 1.4;
@line-height-low: 1.2;


/*  FRONT-END COLORS
-------------------------------------------------------------------------------*/

/*  Error Text */
@color-error-text: #ed1c24;

/*  Notifications */
@color-notification-text: #fa5400;
@color-notification-background: #fff;
@color-notification-border: #fa5400;

@color-notification-note-text: #ffa800;
@color-notification-note-background: #fff;
@color-notification-note-border: #ffa800;

@color-notification-error-text: #ed1c24;
@color-notification-error-background: #fff;
@color-notification-error-border: #ed1c24;

@color-notification-success-text: #79b12d;
@color-notification-success-background: #fff;
@color-notification-success-border: #79b12d;

/*  Toastr */
@color-toastr-text: #fa5400;
@color-toastr-background: #fff;
@color-toastr-border: #fa5400;

@color-toastr-note-text: #fff;
@color-toastr-note-background: #ffa800;
@color-toastr-note-border: #ffa800;

@color-toastr-error-text: #fff;
@color-toastr-error-background: #ed1c24;
@color-toastr-error-border: #ed1c24;

@color-toastr-success-text: #fff;
@color-toastr-success-background: #79b12d;
@color-toastr-success-border: #79b12d;

/*  Product status */
@color-status-instock-text: #fff;
@color-status-instock-background: #3c3;

@color-status-outofstock-text: #fff;
@color-status-outofstock-background: #f00;

@color-status-twodays-text: #fff;
@color-status-twodays-background: #f93;

@color-status-preorder-text: #fff;
@color-status-preorder-background: #3370cc;

@color-status-request-text: #fff;
@color-status-request-background: #c600d4;


/*  THEME COLORS
-------------------------------------------------------------------------------*/

/*  Main */
@color-main-background: #fff;
@color-main-borders: #e5e5e5;
@color-main-text: #666;
@color-main-secondary-text: #333;
@color-main-meta-text: #b0b0b0;
@color-main-titles: #333;
@color-main-highlight: #f69679;
@color-main-icons: #999;

/*	Promo Bar
*******/

@color-promo-bar-text: #fff;
@color-promo-bar-background: #101010;

@color-promo-bar-button-text: #fff;
@color-promo-bar-button-text-hover: #fff;
@color-promo-bar-button-background: #f69679;
@color-promo-bar-button-background-hover: #f69679;
@color-promo-bar-button-border: #f69679;
@color-promo-bar-button-border-hover: #f69679;

@color-promo-bar-close: #fff;

/*  Header */
@color-header-background: #eaeaec;
@color-header-borders: #eaeaec;
@color-header-text: #333;
@color-header-hovers: #f69679;

/*  Footer */
@color-footer-background: #ffffff;
@color-footer-borders: #e5e5e5;
@color-footer-text: #999;
@color-footer-titles: #404040;
@color-footer-hovers: #f69679;

@color-footer-socials-icon: #666666;
@color-footer-socials-background: #ebebeb;
@color-footer-socials-hover: #f69679;

/*  Topbar */
@color-topbar-background: #fff;
@color-topbar-borders: #e5e5e5;
@color-topbar-text: #333;
@color-topbar-icons: #424242;
@color-topbar-hovers: #f69679;

@color-topbar-cart-indicator-background: #f69679;
@color-topbar-cart-indicator-text: #fff;

/*  Slider */
@color-slider-background: #fff;
@color-slider-text: #333;
@color-slider-titles: #333;
@color-slider-button-background: #f69679;
@color-slider-button-text: #fff;
@color-slider-arrows: #333;
@color-slider-dots: #333;

/*  Product Listing */
@color-product-listing-borders: #ffffff;
@color-product-listing-title: #2b2b2b;
@color-product-listing-text: #999999;
@color-product-listing-price: #292929;
@color-product-listing-price-old: #999;
@color-product-listing-hovers: #f69679;
@color-product-listing-quick-view-background: #fff;
@color-product-listing-quick-view-icon: #f69679;

/*  Brands */
@color-brands-borders: #e5e5e5;
@color-brands-title: #333;
@color-brands-hovers: #f69679;

/*  Product Details */
@color-product-images-background: #fff;
@color-product-images-border: #ffffff;
@color-product-images-active: #f69679;
@color-product-meta-text: #999;
@color-product-price: #f69679;
@color-product-price-old: #999;
@color-product-sku: #999;
@color-product-countdown: #f69679;
@color-product-gallery-background: #ffffff;
@color-product-category-properties-background: #f4f5f6;
@color-product-category-properties-text: #666;
@color-product-category-properties-text-value: #999;

/*  Tags */
@color-tags-borders: #e5e5e5;
@color-tags-text: #333;
@color-tags-hover: #f69679;

/* Cart */
@color-cart-background: #f5f5f5;
@color-cart-borders: #cfcfcf;
@color-cart-text: #5c5c5c;
@color-cart-meta-text: #5c5c5c;
@color-cart-titles: #383838;

@color-cart-button-background: #f69679;
@color-cart-button-text: #fff;
@color-cart-button-secondary-background: #e5e5e5;
@color-cart-button-secondary-text: #333;

/*  Forms */
@color-forms-fields-background: #fff;
@color-forms-fields-borders: #e5e5e5;
@color-forms-fields-text: #999;
@color-forms-fields-placeholder: #999999;
@color-forms-fields-icons: #999;

@color-forms-checkbox: #e5e5e5;
@color-forms-checkbox-checked: #333;

@color-forms-radio: #e5e5e5;
@color-forms-radio-checked: #333;

@color-forms-range-slider: #e5e5e5;
@color-forms-range-slider-sliders: #333;

/*  Text box */
@color-text-box-background: #f4f5f6;
@color-text-box-titles: #333;
@color-text-box-text: #999;

/*  Breadcrumb */
@color-breadcrumb-background: #f4f5f6;
@color-breadcrumb-text: #666;
@color-breadcrumb-text-hover: #f69679;
@color-breadcrumb-text-active: #999;

/*  Pagination */
@color-pagination-text: #333;
@color-pagination-highlight: #f69679;
@color-pagination-disabled: #999;

/*  Buttons */
@color-button-background: #f69679;
@color-button-text: #fff;
@color-button-secondary-background: #e5e5e5;
@color-button-secondary-text: #333;

@color-button-thertiary-background: #fff;
@color-button-thertiary-borders: #e8e8e8;
@color-button-thertiary-text: #f69679;

@color-button-thertiary-background-hover: #f69679;
@color-button-thertiary-borders-hover: #f69679;
@color-button-thertiary-text-hover: #fff;

/*  Labels */
@color-label-new-background: #2f2f2f;
@color-label-new-text: #fff;
@color-label-sale-background: #f69679;
@color-label-sale-text: #fff;
@color-label-discount-background: #f69679;
@color-label-discount-text: #fff;
@color-label-free-delivery-background: #f69679;
@color-label-free-delivery-text: #fff;
@color-label-featured-background: #f69679;
@color-label-featured-text: #fff;
@color-label-custom-background: #f69679;
@color-label-custom-text: #fff;
@color-label-date-background: #f69679;
@color-label-date-text: #fff;

/*  Popups */
@color-popups-background: #ffffff;
@color-popups-borders: #ededed;
@color-popups-text: #919191;
@color-popups-titles: #1f1f1f;
@color-popups-highlight: #f69679;

@color-popups-button-background: #f69679;
@color-popups-button-text: #fff;
@color-popups-button-secondary-background: #e5e5e5;
@color-popups-button-secondary-text: #333;


/*  OTHER
-------------------------------------------------------------------------------*/

@separator: 40px;
@separatorLarge: @separator*2;
@separator-small: 25px;
@forms-spacing: 20px;
@image-orientation: 100%;

@golden-ratio-ngt: 0.618%;
@golden-ratio-pst: 1.618%;


/*  SCREEN
-------------------------------------------------------------------------------*/

/*  XXX small screen: phone */
@screen-xxxs: 320px;
@screen-xxxs-min: @screen-xxxs;
@screen-small-phone: @screen-xxxs-min;

/*  XX small screen: phone */
@screen-xxs: 360px;
@screen-xxs-min: @screen-xxs;
@screen-small-phone: @screen-xxs-min;

/*  Extra small screen: phone */
@screen-xs: 480px;
@screen-xs-min: @screen-xs;
@screen-phone: @screen-xs-min;

/*  Small screen: tablet */
@screen-sm: 768px;
@screen-sm-min: @screen-sm;
@screen-tablet: @screen-sm-min;

/*  Medium screen: tablet-landscape */
@screen-md: 992px;
@screen-md-min: @screen-md;
@screen-tablet-landscape: @screen-md-min;

/*  Medium screen: desktop */
@screen-desktop: 1025px;
@screen-desktop-min: @screen-desktop;

/*  Large screen: wide desktop */
@screen-lg: 1200px;
@screen-lg-min: @screen-lg;
@screen-lg-desktop: @screen-lg-min;

/*  So media queries don't overlap when required, provide a maximum */
@screen-xxxs-max: (@screen-xxs-min - 1);
@screen-xxs-max: (@screen-xs-min - 1);
@screen-xs-max: (@screen-sm-min - 1);
@screen-sm-max: (@screen-md-min - 1);
@screen-md-max: (@screen-lg-min - 1);
@screen-tablet-max: (@screen-desktop-min - 1);

@hover: ~'(min-width: @{screen-desktop})';

/*  Z-INDEX
-------------------------------------------------------------------------------*/

@z-to-top: 100;
