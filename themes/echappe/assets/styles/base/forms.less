/*=============================================================================*\
    FORMS
\*=============================================================================*/

._form-row {
	.flexy-row(16px);
	margin-bottom: @forms-spacing;

	._form-col {
		.flexy-col();
		vertical-align: top;

        &._form-col-bottom {
            vertical-align: bottom;
        }
        ._help-text {
            margin-top: 5px;
        }
	}
}

._form-row-secondary {
    .flexy-row(16px);
	margin-bottom: @forms-spacing;

	._form-col-secondary {
	    .flexy-col();
	    vertical-align: top;

	    &._form-col-bottom {
	        vertical-align: bottom;
	    }

	    ._help-text {
	        margin-top: 5px;
	    }
	}
}

._form-actions {
	text-align: right;
	.clearfix();

	._button {
		margin-right: 6px;

		&:last-child {
			margin-right: 0;
		}
	}
}

._form-row-group {
	margin-bottom: @forms-spacing;
}

._block-title {
	background-color: @color-text-box-background; /* theme */
	color: @color-text-box-text; /* theme */
	padding: 14px 18px 13px;
	margin-bottom: @forms-spacing;
	.uppercase();

	h1,h2,h3,h4,h5,h6 {
		color: @color-text-box-titles; /* theme */
	}
}

._form-actions {
    .clearfix();
    text-align: right;

    ._button {
        float: right;
        margin-left: 10px;
        &:last-child {
            margin-left: 0;
        }
    }

    &._form-actions-reverse {
        text-align: left;
        ._button {
            float: left;
            margin-left: 0;
            margin-right: 10px;
            &:last-child {
                margin-right: 0;
            }
        }
    }
}

._form-row-group-secondary {
    & + ._form-row-secondary {
        margin-top: 15px;
    }
}

/*  HELP TEXT
-------------------------------------------------------------------------------*/

._help-text {
    display: block;
    color: @color-main-meta-text; /* theme */
    font-size: calc(@font-size-main ~'-' 2px); /* theme */
}

/*  ERRORS
-------------------------------------------------------------------------------*/

.help-block-error {
	display: inline-block;
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	color: @color-error-text; /* theme */
}

/*  FIELDS
-------------------------------------------------------------------------------*/

/*  Input */
._field {
	display: block;
	background-color: @color-forms-fields-background; /* theme */
	border: 1px solid;
	border-color: @color-forms-fields-borders; /* theme */
	border-radius: 5px;
	font-size: @font-size-forms; /* theme */
	color: @color-forms-fields-text; /* theme */
	padding: 0 10px;
	height: 38px;
	width: 100%;

	&:focus {
		border-color: @color-forms-fields-borders; /* theme */
		outline-color: @color-main-highlight; /* theme */
	}

	.placeholder(@color-forms-fields-placeholder);
}

/*  Textarea */
._textarea {
	._field();
	padding: 10px;
	height: 170px;
}

textarea {
	&._field {
		height: auto;
		padding: 10px;
	}
}

/*  Stack - Field + Button */
._field-stack {
	display: inline-table;

	& ._field-stack-addon {
		display: table-cell;
		vertical-align: top;
		width: 1%;
		white-space: nowrap;
	}
}

/*  Stack - Field + Icon */
._field-icon {
	.field-icon(38px, @color-main-text, right);
}

/*  SELECT
-------------------------------------------------------------------------------*/

select {
	width: 100%;
	height: 38px;
	border-radius: 5px;
	border-color: @color-forms-fields-borders; /* theme */
}

._select {
	display: block;

	&.select2-dropdown-open {
		.select2-choice,
		.select2-choices {
			background: @color-forms-fields-background; /* theme */
			border-color: @color-forms-fields-borders; /* theme */
			border-radius: 5px 5px 0 0;
		}
	}

	.select2-choice {
		background: transparent;
		border: 1px solid @color-forms-fields-borders; /* theme */
		border-radius: 5px;
		box-shadow: none;
		font-size: @font-size-forms; /* theme */
		background-color: @color-forms-fields-background; /* theme */
		padding: 0;
		height: 38px;
		line-height: 38px;

		.select2-chosen {
			margin: 0;
			padding-left: 10px;
			padding-right: 30px;
			color: @color-forms-fields-text; /* theme */
		}

		#select2-chosen-2 {
			min-width: 265px;
		}

		.select2-arrow {
			background: transparent;
			border: none;
			border-radius: 0;
			width: 30px;

			b {
				background: transparent;

				&:before {
					.centerer(true, true);
					font-family: @font-awesome;
					font-weight: 400;
					font-size: @font-size-xsmall;
					content: '\f078';
					color: @color-forms-fields-icons; /* theme */
				}
			}
		}
	}

	&.select2-allowclear {
		.select2-choice {
			.select2-chosen {
				margin: 0;
				padding-right: 45px;
			}

			abbr {
				._remove();
				.centerer(false, true);
				background: transparent;
				height: auto;
				width: auto;
				color: @color-forms-fields-icons; /* theme */
				line-height: 1;
				right: 28px;
			}
		}
	}
}

.select2-drop {
	background-color: @color-forms-fields-background; /* theme */
	border-width: 1px;
	border-top-width: 0;
	border-style: solid;
	border-color: @color-forms-fields-borders; /* theme */
	border-radius: 0 0 5px 5px;
	box-shadow: none;
	line-height: @line-height-medium;
	overflow: hidden;

	&.select2-drop-above {
		box-shadow: none;
		border-radius: 0;
		margin: 0;

		&.select2-drop-active {
			border-color: @color-forms-fields-borders; /* theme */
		}
	}
}

.select2-search {
	padding: 10px;

	input {
		background-color: @color-forms-fields-background; /* theme */
		border-color: @color-forms-fields-borders; /* theme */
		color: @color-forms-fields-text; /* theme */
	}
}
	
.select2-results {
	padding: 0;
	margin: 0;
	max-height: 204px;

	.select2-result {
		color: @color-forms-fields-text; /* theme */

		&.select2-highlighted {
			background-color: @color-button-background; /* theme */
			color: @color-button-text; /* theme */
		}
	}

	.select2-result-label {
		padding: 7px 10px;
	}

	.select2-no-results,
	.select2-searching {
		background-color: transparent;
		padding: 5px 10px;
		font-size: calc(@font-size-main ~'-' 2px); /* theme */
	}
}

.select2-suboption {
	.select2-result-label {
		padding-left: 26px;
		position: relative;

		&:before {
			display: inline-block;
			vertical-align: middle;
			content:"\f054";
			font-family: @font-awesome;
			margin-right: 3px;
			font-size: @font-size-xsmall;
			position: absolute;
			top: 9px;
			left: 10px;

			& when (@rtl) {
				content:"\f053";
			}
		}
	}

	&-2 {
		.select2-result-label {
			padding-left: 37px;

			&:before {
				left: 26px;
			}
		}
	}

	&-3 {
		.select2-result-label {
			padding-left: 48px;

			&:before {
				left: 37px;
			}
		}
	}
}

/*  INPUT GROUP - BOOTSTRAP
-------------------------------------------------------------------------------*/

.input-group {
	display: inline-table;
	
	.input-group-btn {
		.btn {
			border-radius: 0;
			border: none;
			margin: 0;
			width: 38px;
			height: 38px;
			padding: 0;
			background-color: @color-button-background; /* theme */
			color: @color-button-text; /* theme */
			font-size: 0;
			position: relative;
			&:before {
				font-size: calc(@font-size-main ~'-' 2px); /* theme */
				font-family: @glyphicons;
				.centerer(true, true);
			}
			&.bootstrap-touchspin-down {
				&:before {
					content: '\2212';
				}
			}
			&.bootstrap-touchspin-up {
				&:before {
					content: '\2b';
				}
			}
		}
	}
	._field {
		box-shadow: none;
		text-align: center;
	}
}

/*  CHECKBOX
-------------------------------------------------------------------------------*/

._checkbox {
	min-height: 14px;
	padding-left: 20px;
	position: relative;

	input {
		opacity: 0;
		visibility: hidden;
	}

	.checker,
	.checker span,
	.checker input {
		width: 14px;
		height: 14px;
		vertical-align: top;
	}

	.checker {
		position: absolute;
		top: 5px;
		left: 0;

		span {
			border: 2px solid;
			color: @color-main-text; /* theme */
			border-color: @color-forms-checkbox; /* theme */
			border-radius: 0;
			position: relative;

			&.checked {
				border-color: @color-forms-checkbox-checked; /* theme */

				&:before {
					color: @color-forms-checkbox-checked; /* theme */
					font-family: @font-awesome;
					font-size: 8px;
					content: '\f00c';
					.centerer(true, true);
				}
			}

			span {
				display: none;
			}
		}
		
		&.hover,
		&.focus {
			span {
				border-color: @color-forms-checkbox; /* theme */

				&.checked {
					border-color: @color-forms-checkbox-checked; /* theme */
				}
			}
		}
	}
}

/*  RADIO
-------------------------------------------------------------------------------*/

._radio {
	min-height: 14px;
	padding-left: 20px;
	position: relative;

	input {
		opacity: 0;
		visibility: hidden;
	}

	.radio,
	.radio span,
	.radio input {
		width: 14px;
		height: 14px;
		vertical-align: top;
	}

	.radio {
		position: absolute;
		top: 5px;
		left: 0;

		span {
			border: 2px solid;
			border-color: @color-forms-radio; /* theme */
			border-radius: 50%;
			width: 14px;
			height: 14px;
			position: relative;

			&.checked {
				border-color: @color-forms-radio-checked; /* theme */

				&:before {
					content: '';
					background-color: @color-forms-radio-checked; /* theme */
					border-radius: 50%;
					width: 6px;
					height: 6px;
					.centerer(true, true);
				}
			}
		}
	}
}

/*  RANGE SLIDER
-------------------------------------------------------------------------------*/

._range-slider {
	background: transparent;
	background-color: @color-forms-range-slider; /* theme */
	border-radius: 0;
	height: 6px;
	border: none;
	margin-bottom: 5px;

	&.ui-widget-content {
		border: 0;
	}

	[class*='ui-corner-'] {
		border-radius: 0
	}

	.ui-slider-range {
		background-color: @color-forms-range-slider; /* theme */
	}

	.ui-slider-handle {
		background-color: @color-forms-range-slider-sliders; /* theme */
		border: none;
		width: 14px;
		height: 14px;
		top: -4px;
		margin-left: 0;

		&:last-child {
			transform: translate(-100%, 0);
		}
	}
}