/*=============================================================================*\
    VENDOR
\*=============================================================================*/

/*  BOX
-------------------------------------------------------------------------------*/

._vendor,
._showcase-item {
	opacity: 0;
	transition: .5s;
	transform: scale(0);
	font-size: calc(@font-size-main ~'-' 1px); /* theme */
	line-height: @line-height-medium;

	&.revealed {
		opacity: 1;
		transform: scale(1);
	}

	&-link {
		color: @color-main-text; /* theme */
	}

	&-image {
		display: block;
		border-width: 1px;
		border-style: solid;
		border-color: @color-brands-borders; /* theme */
		margin-bottom: 10px;
		padding: 15px;

		&-thumb {
			display: block;
			padding-bottom: 100%;
			position: relative;

			img {
				width: auto;
				height: auto;
				max-width: 100%;
				max-height: 100%;
				.centerer(true, true);
			}
		}
	}

	&-info {
		display: block;
	}

	&-name {
		display: block;
		font-weight: bold;
		color: @color-brands-title; /* theme */
	}

	@media @hover {
		&:hover {
			._vendor-name {
				color: @color-brands-hovers; /* theme */
			}
		}
	}
}

._vendor-description {
	display: none;
}

/*  LIST
-------------------------------------------------------------------------------*/

._vendors-list {
	margin-left: -15px;
	margin-right: -15px;
	.clearfix();
	
	._vendor {
		float: left;
		width: 25%;
		padding-left: 15px;
		padding-right: 15px;
		margin-bottom: @separator;

		&:nth-child(4n+1) {
			clear: both;
		}
	}
}