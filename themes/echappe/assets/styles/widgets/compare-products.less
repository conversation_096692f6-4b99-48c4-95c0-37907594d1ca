/*=============================================================================*\
    COMPARE PRODUCTS
\*=============================================================================*/

._compare-products {
    background-color: @color-main-background; /* theme */
    border-top: 2px solid;
    border-color: @color-main-highlight; /* theme */
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3;

    &.open {
        ._compare-box-toggle {
            ._figure-stack-icon {
                &:before {
                    content: "\f107";
                }
            }
        }

        ._compare-box {
            display: block;
        }
    }

    ._section-title {
        border: 0;
        margin: 0;
        padding: 10px 0;
        position: relative;
        .clearfix();

        &:before {
            display: none;
        }
    }

    ._section-title-meta {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
    }
}

._compare-box-toggle {
    width: 20px;
    height: 20px;
    color: @color-main-highlight; /* theme */
    font-size: 18px;
    line-height: 20px;
    text-align: center;
    right: 0;
    .centerer(false, true);

    &:focus {
        color: @color-main-highlight; /* theme */
    }

    ._figure-stack-icon {
        &:before {
            content: "\f106";
        }
    }
}

/*  COMPARE BOX
-------------------------------------------------------------------------------*/

._compare-box {
    display: none;
    border-top: 1px solid;
    border-color: @color-main-borders; /* theme */
    padding: 15px 0;
    .clearfix();
}

._compare-box-items {
    width: 80%;
    float: left;
}

._compare-box-items-list {
    list-style-type: none;
    .clearfix();
}

._compare-box-item {
    float: left;
    width: 33.33%;
    padding-right: 30px;
    position: relative;

    ._remove {
        display: none;
        color: @color-main-text; /* theme */
        position: absolute;
        top: 0;
        right: 20px;

        @media @hover {
            &:hover {
                color: @color-main-highlight; /* theme */
            }
        }
    }

    @media @hover {
        &:hover {
            ._remove {
                display: block;
            }
        }
    }
}

._compare-box-item-link {
    display: table;
    width: 100%;
    color: @color-main-text; /* theme */

    @media @hover {
        &:hover {
            color: @color-main-highlight; /* theme */
        }
    }
}

._compare-box-item-image,
._compare-box-item-info {
    display: table-cell;
    vertical-align: top;
}
    
._compare-box-item-image {
    width: 60px;
    vertical-align: middle;
    text-align: center;
}

._compare-box-item-image-thumb {
    img {
        max-width: calc(100% ~'-' 6px);
        max-height: calc(100% ~'-' 6px);
    }
}

._compare-box-item-info {
    padding-left: 15px;
}

._compare-box-item-title,
._compare-box-item-price {
    display: block;
}

._compare-box-item-title {
    padding-right: 15px;
    font-weight: bold;
    font-size: calc(@font-size-main ~'-' 2px);
}

._compare-box-item-price {
    margin-top: 5px;
    font-size: calc(@font-size-main ~'-' 2px);

    & when (@rtl) {
        direction: ltr;
    }
}

._compare-box-actions {
    float: right;
    width: 20%;

    ._button {
        width: 100%;
        margin-bottom: 5px;
        ._button-small();

        &:last-child {
            margin-bottom: 0;
        }
    }
}

/*  COMPARE COLUMNS
-------------------------------------------------------------------------------*/

._compare-columns-products {
    table {
        table-layout: fixed;
        width: calc(100% ~'-' 20px);
        min-width: 700px;
        margin: 0 auto;
    }

    th {
        ._h4();
        border: none;
        padding: 10px 15px;
        color: @color-main-titles; /* theme */

        &:first-child {
            vertical-align: middle;
            padding-left: 0;
        }
    }

    td {
        padding: 10px 20px;
        border: none;
    }

    ._compare-box-item {
        float: none;
        width: auto;
        padding-left: 0;
        padding-right: 0;
        vertical-align: top;

        ._remove {
            top: 0;
            right: 0;
            z-index: 3;
        }
    }

    ._compare-columns-products-actions {
        background-color: transparent;
    }
}