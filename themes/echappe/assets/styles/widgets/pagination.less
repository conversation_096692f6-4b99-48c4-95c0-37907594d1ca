/*=============================================================================*\
    PAGINATION
\*=============================================================================*/

._pagination {
	font-size: 0;
	._section-separator();
}

.pagination {
	border: none;
	font-weight: bold;

	li {
		display: inline-block;
		font-size: @font-size-main; /* theme */
		margin-right: 15px;

		&:last-child {
			margin-right: 0;
		}

		a {
			color: @color-pagination-text; /* theme */
			background-color: transparent;
			border-radius: 0;
			border: none;
			padding: 0;
			margin-left: 0;

			&:hover {
				color: @color-pagination-highlight; /* theme */
				background: transparent;
			}

			&:focus {
				color: @color-pagination-text; /* theme */
				background: transparent;
			}
		}

		&.active {
			a {
				color: @color-pagination-highlight; /* theme */
				background-color: transparent;

				&:hover,
				&:focus {
					color: @color-pagination-highlight; /* theme */
					background: transparent;
				}
			}
		}

		&.disabled {
			a {
				color: @color-pagination-disabled; /* theme */

				&:hover,
				&:focus {
					color: @color-pagination-disabled; /* theme */
				}
			}
		}
	}
}