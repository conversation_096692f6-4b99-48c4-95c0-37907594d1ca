/*=============================================================================*\
    LAYOUT
\*=============================================================================*/

._wrapper {
	padding-top: 76px;
}

/*  SECTIONS
-------------------------------------------------------------------------------*/

._section-separator {
	margin-bottom: @separator;
}

._section-separator-large {
	margin-bottom: @separatorLarge;
}

._section-title {
	text-align: center;
	margin-bottom: @separator;
	.uppercase();

	&._carousel-title {
		padding: 0 50px;
	}
}

._secondary-title {
	text-align: center;
	margin-bottom: @separator-small;
	.uppercase();
}

._section-title-separator {
    margin-top: 30px;
}

/*  IMAGES ORIENTATION
-------------------------------------------------------------------------------*/

._product-image-thumb-holder,
._product-details-image-thumb,
._cart-product-image-thumb-holder,
._order-details-product-image-thumb,
._cart-compact-products-list-item-image-thumb,
._cart-summary-products-list-item-image-thumb,
._wishlist-compact-products-list-item-image-thumb,
._compare-box-item-image-thumb {
	display: block;
	position: relative;
	padding-bottom: @image-orientation; /* theme */

	img {
		width: auto;
		height: auto;
		max-width: 100%;
		max-height: 100%;
		.centerer(true, true);
	}

	@media (max-width: @screen-xxxs-max) {
		padding: 0; /* theme */

		img {
			position: static;
			transform: translate(0, 0);
		}
	}
}