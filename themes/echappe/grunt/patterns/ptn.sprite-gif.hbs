{{#spritesheet}}
.sprite-gif {
    display: inline-block;
    background-image: url({{{escaped_image}}});
    background-size: {{px.width}} {{px.height}};
}
{{/spritesheet}}
{{#retina_spritesheet}}
@media (min--moz-device-pixel-ratio: 1.5), (-o-min-device-pixel-ratio: 3/2), (-webkit-min-device-pixel-ratio: 1.5), (min-device-pixel-ratio: 1.5), (min-resolution: 1.5dppx) {
	.sprite-gif {
		background-image: url({{{escaped_image}}});
	}
}
{{/retina_spritesheet}}
{{#sprites}}
.sprite-gif-{{name}} {
    width: {{px.width}};
    height: {{px.height}};
    background-position: {{px.offset_x}} {{px.offset_y}};
}
{{/sprites}}
