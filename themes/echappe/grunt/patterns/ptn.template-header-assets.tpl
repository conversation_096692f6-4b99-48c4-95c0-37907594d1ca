<script type="text/javascript">
    (function (w) {
        "use strict";
        var loadCSS = function (href, before, media) {
            var doc = w.document;
            var ss = doc.createElement("link");
            var ref;
            if (before) {
                ref = before;
            }
            else {
                var refs = ( doc.body || doc.getElementsByTagName("head")[0] ).childNodes;
                ref = refs[refs.length - 1];
            }
            var sheets = doc.styleSheets;
            ss.rel = "stylesheet";
            ss.href = href;
            ss.media = "only x";
            function ready(cb) {
                if (doc.body) {
                    return cb();
                }
                setTimeout(function () {
                    ready(cb);
                });
            }
            ready(function () {
                ref.parentNode.insertBefore(ss, ( before ? ref : ref.nextSibling ));
            });
            var onloadcssdefined = function (cb) {
                var resolvedHref = ss.href;
                var i = sheets.length;
                while (i--) {
                    if (sheets[i].href === resolvedHref) {
                        return cb();
                    }
                }
                setTimeout(function () {
                    onloadcssdefined(cb);
                });
            };
            function loadCB() {
                if (ss.addEventListener) {
                    ss.removeEventListener("load", loadCB);
                }
                ss.media = media || "all";
            }
            if (ss.addEventListener) {
                ss.addEventListener("load", loadCB);
            }
            ss.onloadcssdefined = onloadcssdefined;
            onloadcssdefined(loadCB);
            return ss;
        };
        if (typeof exports !== "undefined") {
            exports.loadCSS = loadCSS;
        }
        else {
            w.loadCSS = loadCSS;
        }
    }(typeof global !== "undefined" ? global : this));
    (function (w) {
        if (!w.loadCSS) {
            return;
        }
        var rp = loadCSS.relpreload = {};
        rp.support = function () {
            try {
                return w.document.createElement("link").relList.supports("preload");
            } catch (e) {

            }
        };
        rp.poly = function () {
            var links = w.document.getElementsByTagName("link");
            for (var i = 0; i < links.length; i++) {
                var link = links[i];
                if (link.rel === "preload" && link.getAttribute("as") === "style") {
                    w.loadCSS(link.href, link);
                    link.rel = null;
                }
            }
        };
        if (!rp.support()) {
            rp.poly();
            var run = w.setInterval(rp.poly, 300);
            if (w.addEventListener) {
                w.addEventListener("load", function () {
                    w.clearInterval(run);
                })
            }
        }
    }(this));
</script>
<link rel="shortcut icon" type="image/x-icon" href="{favicon()}">

<!--(if target dev)><!-->
{if !$rtl}
    <link rel="stylesheet" href="<%- styles_vendors %>">
{else}
    <link rel="stylesheet" href="<%- styles_vendors_rtl %>">
{/if}
<link rel="stylesheet" href="<%- styles_fonts_google %>">
<link rel="stylesheet" href="<%- styles_fonts_embed %>">
<link rel="stylesheet" href="<%- styles_sprite_gif %>">
<link rel="stylesheet" href="<%- styles_sprite_jpg %>">
<link rel="stylesheet" href="<%- styles_sprite_png %>">
<link rel="stylesheet" href="<%- styles_sprite_svg %>">
{if !$rtl}
    <link rel="stylesheet" href="<%- styles_custom %>">
{else}
    <link rel="stylesheet" href="<%- styles_custom_rtl %>">
{/if}
<link rel="preload" href="{$img_url}site/css/build.css?{app('last_build')}" as="style" onload="this.rel='stylesheet'">
<!--<!(endif)-->
<!--(if target dist)>
{if !$rtl}
	<link rel="stylesheet" type="text/css" media="screen" href="<%- styles_min %>?{app('last_build')}">
{else}
	<link rel="stylesheet" type="text/css" media="screen" href="<%- styles_rtl_min %>?{app('last_build')}">
{/if}
<link rel="stylesheet" type="text/css" media="screen" href="{$img_url}site/css/build.min.css?{app('last_build')}">
<!--<!(endif)-->
<link rel="stylesheet" type="text/css" media="screen" href="{config('url.storage')}{site('site_id')}/stylesheets/theme.css?{setting('stylesheet_version')}">