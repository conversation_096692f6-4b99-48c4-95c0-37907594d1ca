{if !empty($showcase)}
    {$widget = $showcase}
{/if}

{if !$widget instanceof \App\Helper\Widget}
    {$grid_class = $widget->getClassName()}
{/if}

{if !empty($widget_products)}
    {$grid_class = Widget::get('product.filters')->getClassName()}
    {$widget = $widget_products}
{/if}

{$is_default_content = false}
{if !empty($widget) && $widget->isDefaultContent()}
    {$is_default_content = true}
{/if}

{if isset($image_size)}
    {$image_size = $image_size}
{else}
    {$image_size = '600x600'}
{/if}

{$show_quick_view = setting('show_product_quick_view', 'yes') == 'yes'}

{if !isset($product_name_tag)}
    {$product_name_tag = 'h3'}
{/if}

{if empty($product_bar)}
    {$product_bar = false}
{/if}

{if !empty($widget->getClassName())}
    {$grid_class = $widget->getClassName()}
{/if}

{$list_widget_settings = Widget::get('product.filters')->getSettings()}
{if !$products->isEmpty()}
    {strip}
    <div class="_products-list{if !$list_widget_settings['listing_show_buy']} no-button{/if}{if !$list_widget_settings['show_quick_view']} no-quick-view{/if}{if $is_default_content} _has-helper{/if}">
        {foreach $products as $product}
            <div class="_product {$grid_class|default} {if $product->overlay}overlay-product-disable-sell{/if}" data-box="product">
                <div class="_product-inner">
                    <div class="_product-image">
                        <a class="_product-image-thumb" href="{$product->url()}" title="{$product->name}|{$product->category->name|default}">
                            <span class="_product-image-thumb-holder">
                                <img src="{noImage($image_size)}" alt="{$product->name}" title="{$product->name}">
                            </span>
                        </a>

                        {if $product->new === 'yes' || $product->sale === 'yes' || $product->featured || $custom_labels|default || (isset($product->discount) && $discount_inside|default) || !empty($product->vendor)}
                            <div class="_product-ribbon-holder">
                                {if !empty($product->vendor)}
                                    <span class="_product-ribbon _product-ribbon-vendor">{$product->vendor->name}</span>
                                {/if}
                                {if $product->new === 'yes'}
                                    <span class="_product-ribbon _product-ribbon-new">{t}sf.global.label.new{/t}</span>
                                {/if}
                                {if $product->sale === 'yes'}
                                    <span class="_product-ribbon _product-ribbon-sale">{t}sf.global.label.on_sale{/t}</span>
                                {/if}
                                {if isset($product->discount) && $discount_inside|default}
                                    <span {if $product->discount->style}style="{$product->discount->style}"{/if} class="_product-ribbon _product-ribbon-discount _product-ribbon-discount-{$product->discount->type}" title="{t}sf.widget.product.tip.discount_target{/t}: {$product->discount->target_type}"><span class="rtl-ltr">-&nbsp;{$product->discount->type_value_formatted}</span></span>
                                {/if}
                                {if $product->featured}
                                    <span class="_product-ribbon _product-ribbon-featured">{t}sf.global.label.featured{/t}</span>
                                {/if}
                                {if $custom_labels|default}
                                    {if $product->free_shipping}
                                        <span class="_product-ribbon _product-ribbon-delivery">{t}sf.global.free.delivery{/t}</span>
                                    {/if}
                                    {foreach from=$product->labels item=label}
                                        <span class="_product-ribbon _product-ribbon-custom" {if $label->style}style="{$label->style}"{/if}>{$label->name nofilter}</span>
                                    {/foreach}
                                {/if}
                            </div>
                        {/if}

                        {if isset($product->discount) && !$discount_inside|default}
                            {include file="widgets/product/discount_amount_in_label.tpl"}
                        {/if}

                        {foreach from=$product->banners item=pbanner}
                            <span class="_product-ribbon-banner {$pbanner->banner_position}">
                                <img class="_product-ribbon-banner-image" src="{$pbanner->getImage($image_size)}" title="{$pbanner->name|default}" alt="{$pbanner->name|default}">
                            </span>
                        {/foreach}

                        <div class="_product-actions">
                            {if $list_widget_settings['show_quick_view']}
                                <a class="_product-quick-view" href="{$product->url()}" title="{$product->name}|{$product->category->name|default}" data-ajax-modal="true" data-modal-class="product-details">
                                    <i class="fa fa-search" title="{t}sf.widget.product.act.quick_view{/t}"></i>
                                </a>
                            {/if}

                            {if ($product_bar == true) && ($list_widget_settings['listing_show_wishlist'] || $list_widget_settings['listing_show_compare'])}
                                <div class="_product-interactions">
                                    {if $list_widget_settings['listing_show_wishlist']}
                                        <div class="_wishlist-container">
                                            <a href="#" data-id="{$product->id}" data-widget="product-wishlist" class="_wishlist-button{if $product->favorite} active{/if}">
                                                <i class="_figure-stack-icon fa fa-heart"></i>
                                            </a>
                                        </div>
                                    {/if}

                                    {if Widget::has('productCompare') && $list_widget_settings['listing_show_compare']}
                                        <div class="_compare-container">
                                            <div class="_compare-button">
                                                {include file="widgets/compare/add.tpl" product=$product}
                                            </div>
                                        </div>
                                    {/if}

                                    <div class="_share-container js-product-share">
                                        <a class="_share-button js-button-share" href="#">
                                            <i class="fa fa-share-alt"></i>
                                        </a>

                                        <div class="_share-dropdown">
                                            {include file="widgets/extra/share.tpl" widget=Widget::get('extra.addThisShare') link=$product->url}
                                        </div>
                                    </div>
                                </div>
                            {/if}

                            {include file="widgets/product/listBuyButton.tpl"}
                        </div>
                    </div>

                    <div class="_product-info">
                        {if $list_widget_settings['listing_show_price'] || $list_widget_settings['listing_show_buy']}
                            <div class="_product-options">
                                {if $list_widget_settings['listing_show_price'] && showPriceForUser()}
                                    <div class="_product-price">
                                        <div class="_product-price-inner">
                                            {if !empty($product->price_from_discounted_units_formatted)}
                                                <span class="_product-price-compare">{if $product->diff_prices}<span class="_product-price-diff">{t}sf.product.price_from{/t} </span>{/if}{$product->price_from_discounted_units_formatted}</span>
                                                <del class="_product-price-old">{$product->price_from_units_formatted}</del>
                                                {$price = $product->price_from_discounted_input}
                                            {else}
                                                <span class="price">{if $product->diff_prices}<span class="_product-price-diff">{t}sf.product.price_from{/t} </span>{/if}{$product->price_from_units_formatted}</span>
                                                {$price = $product->price_from_input}
                                            {/if}
                                        </div>
                                    </div>
                                {/if}
                            </div>
                        {/if}

                        <div class="_product-name">
                            <{$product_name_tag} class="_product-name-tag">
                                <a href="{$product->url()}" title="{$product->name}|{$product->category->name|default}">{if !empty($truncate_names)}{$product->name|truncate:$truncate_names:"...":true}{else}{$product->name}{/if}</a>
                            </{$product_name_tag}>

                        {if $product->variant->unit|default && $product->variant->unit_text}
                                <div class="_product-unit-text">
                                    <span class="_button _button_unit">{$product->variant->unit_text}</span>
                                </div>
                            {/if}
                        </div>

                        {if !empty($product->short_description) && $list_widget_settings['show_short_description']}
                            <div class="_product-short-description">{$product->short_description nofilter}</div>
                        {/if}

                        {if $product->category_properties|default && $list_widget_settings['enable_category_properties']}
                            <div class="_product-properties">
                                <ul>
                                    {foreach $product->category_properties as $property}
                                        <li>
                                            <span class="_product-properties-title">{$property['name']}:</span>
                                            <span class="_product-properties-value">{$property['value']}</span>
                                        </li>
                                    {/foreach}
                                </ul>
                            </div>
                        {/if}

                        {if $product->countDownList}
                            <div class="_product-countdown">
                                {include file="widgets/common/countdown.tpl" endDate="{$product->countDownList}"}
                            </div>
                        {/if}
                    </div>
                </div>
            </div>
        {/foreach}
        {if $is_default_content}
            {include file="widgets/common/helper.tpl"}
        {/if}
        </div>
    {/strip}
{else}
    <div class="_notification">
        <p>{t}sf.widget.product.warn.no_products{/t}</p>
    </div>
{/if}
{if !empty($widget_products)}
    {if !empty(W::filters()->getCategory())}
        {include file="widgets/common/microdata/category.tpl" widget=$widget_products}
    {/if}
    {if !empty(W::filters()->getVendor())}
        {include file="widgets/common/microdata/vendor.tpl" widget=$widget_products}
    {/if}
{/if}
