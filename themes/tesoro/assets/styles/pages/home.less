/*=============================================================================*\
    HOME
\*=============================================================================*/

.page-home {
    ._wrapper {
        padding-top: @header-height;
    }

    ._content {
        padding: 0;
    }

    ._header {
        box-shadow: 0 0 100px -20px rgba(0, 0, 0, 0.35);
        border: 0;
    }
}

/*  Intro
-------------------------------------------------------------------------------*/

._section-intro {
    padding: 0;
    overflow: hidden;
}

._intro {
    display: table;
    table-layout: fixed;
    border-collapse: collapse;
}

._intro-image,
._intro-text {
    display: table-cell;
}

._intro-image {
    vertical-align: top;
}

._intro-image-inner {
    position: relative;
}

._intro-image-play {
    .centerer(true, true);
    margin-top: -55px;
    margin-left: -92px;
}

._intro-image-play-image {
    transition: .3s ease;
    animation: pulse .6s ease infinite alternate;
}

._intro-image-play-text {
    position: absolute;
    bottom: 22%;
    left: 0;
    right: 0;
    text-align: center;
    color: #939393;
    font-weight: bold;
    font-size: 20px;

    span {
        color: #fff;
    }
}

._intro-text {
    vertical-align: middle;
    padding: 100px 0 160px;
    color: #9b9a9a;

    ._text {
        width: 100%;
        max-width: 820px;
    }

    .table-responsive {
        overflow: visible;
    }

    ._textbox,
    ._text-description {
        h1,h2,h3 {
            margin-bottom: 3px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        h4,h5,h6 {
            margin-bottom: 10px;

            &:last-child {
                margin-bottom: 0;
            }

            strong {
                font-weight: 900;
            }
        }

        p {
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }

            strong {
                color: #9b9a9a;
            }
        }

        table {
            border-collapse: collapse;
            font-size: 14px;
            margin-top: 10px;

            table {
                margin-top: 0;
            }

            h6 {
                font-size: 14px; /*theme*/
            }
        }

        td {
            padding: 8px 10px;
            max-width: 410px;
        }
    }
}

._intro-table-cell-rumb {
    width: 115px;
}

._intro-rumb {
    width: 94px;
    height: 94px;
    padding: 17px;
    position: relative;

    &:before {
        content: '';
        background-color: #262626;
        border-radius: 4px;
        width: 70px;
        height: 70px;
        z-index: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        transform-origin: 0 0;
        transform: rotate(45deg) translate(-50%, -50%);
        box-shadow: 8px 0 30px 5px rgba(0, 0, 0, 0.23);
    }

    img {
        position: relative;
        z-index: 1;
        .centerer(true, true);
    }
}

._rumb {
    position: absolute;

    &:before {
        content: '';
        display: flex;
        width: 100%;
        height: 100%;
        transform-origin: center center;
        transform: rotate(45deg);
    }
}

._intro-rumb1 {
    width: 100vw;
    height: 100vw;
    top: 0;
    left: 0;
    transform: translate(-64%, -50%);

    &:before {
        background-color: #e2e2e2;
    }
}

._intro-rumb2 {
    width: 100vw;
    height: 100vw;
    transform: translate(-53%, 75%);
    bottom: 0;
    left: 0;

    &:before {
        background: #d6d6d6;
        background: -moz-linear-gradient(right, #d6d6d6 0%, #d6d6d6 50%, #e2e2e2 55%, #e2e2e2 100%);
        background: -webkit-linear-gradient(right, #d6d6d6 0%,#d6d6d6 50%,#e2e2e2 55%,#e2e2e2 100%);
        background: linear-gradient(to left, #d6d6d6 0%,#d6d6d6 50%,#e2e2e2 55%,#e2e2e2 100%);
    }
}

._intro-rumb3 {
    width: 100vw;
    height: 100vw;
    transform: translate(-45.5%, 110%);
    bottom: 0;
    left: 0;

    &:before {
        background-color: #a2a2a2;
    }
}

._intro-rumb4 {
    width: 470px;
    height: 470px;
    top: -450px;
    right: 6%;

    &:before {
        border: 27px solid #fafa90;
    }
}

._intro-rumb5 {
    width: 70px;
    height: 70px;
    border: 5px solid #dedede;
    border-radius: 4px;
    transform-origin: top top;
    transform: rotate(-45deg);
    position: absolute;
    top: 95px;
    right: 9%;

    &:before {
        content: '';
        background-color: #191919;
        border-radius: 2px;
        width: 26px;
        height: 26px;
        position: absolute;
        bottom: -3px;
        left: -3px;
        transform: translate(-50%, 50%);
    }
}

._intro-rumb6 {
    width: 34px;
    height: 34px;
    bottom: 130px;
    .centerer(true, false);

    &:before {
        border: 2px solid #191919;
    }
}

._intro-rumb6-arrow {
    background-color: #191919;
    border-radius: 2px;
    width: 2px;
    height: 25px;
    top: calc(100% ~'+' 5px);
    .centerer(true, false);

    &:before,
    &:after {
        content: '';
        background-color: #191919;
        width: 2px;
        height: 10px;
        position: absolute;
        top: 100%;
        left: 0;
        transform-origin: center 0;
    }

    &:before {
        transform: rotate(-157deg);
    }

    &:after {
        transform: rotate(157deg);
    }
}

/*  Tesoro club
-------------------------------------------------------------------------------*/

._club-logo {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 144px;
    transform: skewY(-2deg);
    transform-origin: top left;
}

._club-logo-image {
    background-color: #262626;
    padding: 33px 33px 16px;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, 0);

    img {
        transform: skewY(2deg);
    }
}

._club-textbox {
    margin-top: 150px;
    position: relative;

    .container {
        width: 1400px;
        max-width: 100%;
        position: relative;
        z-index: 1;
    }
}

._club-textbox-title {
    margin-bottom: 90px;
    position: relative;
}

._club-textbox-content {
    display: table;
    table-layout: fixed;
    width: 100%;
}

._club-textbox-image,
._club-textbox-text {
    display: table-cell;
    vertical-align: middle;
    position: relative;
}

._club-textbox-image {
    z-index: 0;
}

._club-textbox-image-thumb {
    .centerer(true, true);

    img {
        max-width: initial;
        position: relative;
        z-index: 1;
    }
}

._club-textbox-image-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;

    .tesoro-diamond {
        height: 100%;
        .centerer(true, true);
    }

    .tesoro-diamond-icon {
        fill: #ececec;
    }
}

._club-textbox-text {
    padding: 60px 0;
    z-index: 1;

    ._textbox {
        max-width: 580px;
        margin-left: auto;
        position: relative;
        padding-bottom: 14%;
        font-size: 16px;

        h6, ._h6 {
            margin-bottom: 11px;

            &:before {
                content: '';
                background-color: @color-main-titles; /* theme */
                display: inline-block;
                width: 18px;
                height: 18px;
                transform: rotate(45deg);
                margin-right: 15px;
                position: relative;
                top: 1px;
            }
        }
    }
}

._club-textbox-title-rumb {
    width: 52px;
    height: 52px;
    border: 2px solid #959595;
    border-radius: 4px;
    transform-origin: center center;
    transform: rotate(45deg);
    position: absolute;
    top: -60px;
    right: 0;

    &:before {
        content: '';
        background-color: #fafa90;
        border-radius: 2px;
        width: 28px;
        height: 28px;
        position: absolute;
        bottom: -3px;
        left: -3px;
        transform: translate(-50%, 50%);
    }
}

._club-textbox-image-rumb {
    width: 36px;
    height: 36px;
    position: absolute;
    bottom: 40px;
    left: 60px;

    &:before {
        content: '';
        display: flex;
        border: 2px solid #191919;
        border-radius: 2px;
        width: 100%;
        height: 100%;
        transform-origin: center center;
        transform: rotate(45deg);
    }
}

._club-textbox-image-rumb-arrow {
    background-color: #191919;
    border-radius: 2px;
    width: 95px;
    height: 2px;
    left: calc(100% ~'+' 4px);
    .centerer(false, true);

    &:before,
    &:after {
        content: '';
        background-color: #191919;
        width: 10px;
        height: 2px;
        position: absolute;
        top: 0;
        left: 100%;
        transform-origin: 0 center;
    }

    &:before {
        transform: rotate(-157deg);
    }

    &:after {
        transform: rotate(157deg);
    }
}

/*  Partners
-------------------------------------------------------------------------------*/

._club-partners {
    margin-bottom: 70px;

    ._textbox {
        max-width: 750px;
        margin: 0 auto 60px;
    }

    ._showcase-list {
        margin: 0;
        text-align: center;
    }

    ._showcase-item {
        float: none;
        display: inline-block;
        vertical-align: middle;
        width: auto;
        margin: 22px;
        min-width: 220px;
    }

    ._showcase-item-link {
        &:hover {
            ._showcase-item-image {
                img {
                    transform: scale(1);
                }
            }
        }
    }
}

/*  Brands
-------------------------------------------------------------------------------*/

._brands {
    .container {
        width: 1500px;
        max-width: 100%;
    }

    ._textbox {
        max-width: 750px;
        margin: 0 auto 60px;
    }
}

._brands-slider {
    position: relative;
}

._brands-slider-items {
    overflow: hidden;
    white-space: nowrap;

    ._showcase {
        padding: 15px 0;
    }

    ._showcase-list {
        margin: 0;
        font-size: 0;
    }

    ._showcase-item {
        float: none;
        display: inline-block;
        vertical-align: middle;
        padding: 0 15px 0 16px;
        margin: 0;
        text-align: center;
        position: relative;

        &:before {
            content: '';
            border-left: 1px solid #eaeaea;
            height: 74px;
            position: absolute;
            left: 0;
            .centerer(false, true);
            transition: .8s;
        }

        &.swiper-slide-active {
            &:before {
                opacity: 0;
            }
        }
    }

    ._showcase-item-link {
        &:hover {
            ._showcase-item-image {
                img {
                    transform: scale(1);
                }
            }
        }
    }
}

._brands-slider-pagination {
    bottom: -70px;
    .centerer(true, false);

    .swiper-pagination-bullet {
        margin-top: 0 !important;
    }
}

/*  New
-------------------------------------------------------------------------------*/

._new {
    overflow: hidden;
    padding-bottom: 50px;

    .container {
        position: relative;
        z-index: 3;
    }
}

._new-text {
    padding: 55px 0 160px;
    margin: 100px 0 30px;
    position: relative;

    ._textbox {
        position: relative;
    }

    ._button {
        min-width: 130px;
    }
}

._new-text-rumb {
    width: 86px;
    height: 86px;
    position: absolute;
    bottom: 160px;
    left: 5%;

    &:before {
        content: '';
        display: flex;
        border: 2px solid #191919;
        border-radius: 4px;
        width: 100%;
        height: 100%;
        transform-origin: center center;
        transform: rotate(45deg);
    }
}

._new-text-background {
    color: #e5e5e5;
    font-weight: bold;
    font-size: 28vw;
    line-height: 1;
    z-index: 0;
    .centerer(true, true);

    &:before {
        content: '';
        width: 30px;
        height: 30px;
        border: 6px solid #191919;
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: rotate(45deg);
        margin-left: 4.2%;
        margin-bottom: -1.5%;
    }
}

._new-rumb1 {
    width: 320px;
    height: 320px;
    bottom: 40px;
    right: -210px;
    z-index: 1;

    &:before {
        border: 18px solid #e0e0e0;
        border-radius: 13px;
    }
}

._new-rumb2 {
    width: 48px;
    height: 48px;
    bottom: 400px;
    right: 50px;
    z-index: 1;

    &:before {
        background-color: #fafa90;
    }
}

._new-text-rumb-arrow {
    background-color: #191919;
    border-radius: 2px;
    width: 95px;
    height: 2px;
    left: calc(100% ~'-' 25px);
    .centerer(false, true);

    &:before,
    &:after {
        content: '';
        background-color: #191919;
        width: 10px;
        height: 2px;
        position: absolute;
        top: 0;
        left: 100%;
        transform-origin: 0 center;
    }

    &:before {
        transform: rotate(-157deg);
    }

    &:after {
        transform: rotate(157deg);
    }
}

._new-image {
    position: absolute;
    bottom: 0;
    right: 13%;
    height: 100%;
    z-index: 2;
    display: flex;
    align-items: flex-end;

    img {
        max-height: 100%;
        width: auto;
    }
}

._section-bundles {
    position: relative;
    padding: 0 50px;
    z-index: 2;
}

._bundles-tabs-text {
    margin: 0 auto 37px;
}

._bundles-tabs-nav-box:before,
._bundles-tabs-nav-box:after,
._bundles-tabs-nav-box-inner:before,
._bundles-tabs-nav-box-inner:after,
._bundles-tabs-nav-item:before,
._bundles-tabs-nav-item:after {
    content: '';
    position: absolute;
    top: 0;
}

._bundles-tabs-nav-box:before,
._bundles-tabs-nav-box-inner:before,
._bundles-tabs-nav-item:before {
    right: 100%;
    border-right-style: solid;
}

._bundles-tabs-nav-box:after,
._bundles-tabs-nav-box-inner:after,
._bundles-tabs-nav-item:after {
    left: 100%;
    border-left-style: solid;
}

._bundles-tabs-nav {
    text-align: center;
}

._bundles-tabs-nav-box {
    display: inline-block;
    background-color: @color-button-secondary-borders; /* theme */
    padding: 2px 1px;
    color: @color-button-secondary-text; /* theme */
    font-family: @font-family-buttons; /* theme */
    font-size: calc(@font-size-buttons ~'-' 6px); /* theme */
    font-weight: @font-weight-buttons; /* theme */
    font-style: @font-style-buttons; /* theme */
    line-height: @line-height-button;
    text-align: center;
    cursor: pointer;
    position: relative;

    &:before,
    &:after {
        border-top: 23px solid transparent;
        border-bottom: 23px solid transparent;
        z-index: 0;
    }

    &:before {
        border-right-width: 14px;
        border-right-color: @color-button-secondary-borders; /* theme */
    }

    &:after {
        border-left-width: 14px;
        border-left-color: @color-button-secondary-borders; /* theme */
    }
}

._bundles-tabs-nav-box-inner {
    display: block;
    position: relative;
    background-color: @color-second-background; /* theme */

    &:before,
    &:after {
        border-top: 21px solid transparent;
        border-bottom: 21px solid transparent;
        z-index: 1;
    }

    &:before {
        border-right-width: 13px;
        border-right-color: @color-second-background; /* theme */
    }

    &:after {
        border-left-width: 13px;
        border-left-color: @color-second-background; /* theme */
    }
}

._bundles-tabs-nav-item {
    display: inline-block;
    vertical-align: middle;
    padding: 14px 18px;
    margin: 0 2px;
    margin-right: 12px;
    position: relative;
    z-index: 2;

    &:last-child {
        margin-right: 0;
    }

    &:before,
    &:after {
        border-top: 23px solid transparent;
        border-bottom: 23px solid transparent;
        z-index: 2;
        .centerer(false, true);
    }

    &:before {
        margin-right: 2px;
        border-right-width: 14px;
        border-right-color: transparent;
    }

    &:after {
        margin-left: 2px;
        border-left-width: 14px;
        border-left-color: transparent;
    }

    &.active {
        background-color: #675d84;
        box-shadow: 0 0 0 2px #675d84;
        color: #fff;
        z-index: 3;
        cursor: default;

        &:before {
            border-right-color: #675d84;
        }

        &:after {
            border-left-color: #675d84;
        }
    }
}

._bundles-tabs {
    max-width: 1700px;
    margin: 0 auto;
}

._bundles-tab {
    display: none;

    &:first-child {
        display: block;
    }
}

.container-bundles {
    max-width: 1700px;
    margin: 0 auto;
}

/*  Discounted
-------------------------------------------------------------------------------*/

._section-discounted {
    padding: 0;
    overflow: hidden;

    .container {
        width: 1340px;
        max-width: 100%;
        position: relative;
        z-index: 2;
    }
}

._discounted-rumb1 {
    width: 230px;
    height: 230px;
    bottom: -145px;
    left: 56%;

    &:before {
        border: 20px solid #d9d9d9;
        border-radius: 10px;
    }
}

._discounted-rumb2 {
    width: 28px;
    height: 28px;
    position: absolute;
    bottom: 80px;
    left: 55.8%;

    &:before {
        border: 3px solid #191919;
    }
}

._discounted-rumb2-arrow {
    background-color: #191919;
    border-radius: 2px;
    width: 60px;
    height: 2px;
    right: calc(100% ~'+' 4px);
    .centerer(false, true);

    &:before,
    &:after {
        content: '';
        background-color: #191919;
        width: 10px;
        height: 2px;
        position: absolute;
        top: 0;
        right: 100%;
        transform-origin: right center;
    }

    &:before {
        transform: rotate(-157deg);
    }

    &:after {
        transform: rotate(157deg);
    }
}

._discounted-content {
    display: table;
    width: 100%;
    table-layout: fixed;
}

._discounted-image,
._discounted-text {
    display: table-cell;
}

._discounted-image {
    vertical-align: bottom;
}

._discounted-text {
    vertical-align: middle;
    padding: 160px 50px 100px;
    text-align: center;

    ._textbox {
        vertical-align: middle;
        display: inline-block;
        text-align: left;
    }

    ._button {
        min-width: 130px;
    }
}

._discounted-text-background {
    color: #e5e5e5;
    font-weight: bold;
    font-size: 15.5vw;
    line-height: 1;
    z-index: 0;
    .centerer(true, true);
}

/*  Product showcases
-------------------------------------------------------------------------------*/

._showcase-product-slider-container {
    padding-bottom: 70px;
    margin-bottom: 70px;
    position: relative;

    &:after {
        content: '';
        background: #f5f5f5;
        background: -moz-linear-gradient(left, #f5f5f5 0%, #d7d7d7 20%, #d7d7d7 81%, #f5f5f5 100%);
        background: -webkit-linear-gradient(left, #f5f5f5 0%,#d7d7d7 20%,#d7d7d7 81%,#f5f5f5 100%);
        background: linear-gradient(to right, #f5f5f5 0%,#d7d7d7 20%,#d7d7d7 81%,#f5f5f5 100%);
        width: 1500px;
        height: 1px;
        max-width: 90%;
        position: absolute;
        top: 100%;
        .centerer(true, false);
    }

    &:last-child {
        padding-bottom: 0;
        margin-bottom: 0;

        &:after {
            display: none;
        }
    }

    .container-products {
        position: relative;
        z-index: 1;
    }
}

._showcase-product {
    ._showcase-product-title {
        display: none;
    }
}

._showcase-product-title {
    margin-bottom: 70px;
}

._showcase-product-list {
    overflow: hidden;
}

._showcase-product-slider {
    position: relative;

    ._products-list {
        white-space: nowrap;
        margin: 0;
    }

    ._product {
        float: none;
        display: inline-block;
        vertical-align: top;
        white-space: normal;
        clear: none !important;
        margin: 0;
    }
}

._showcase-product-slider-prev,
._showcase-product-slider-next {
    background: transparent;
    border: 0;
    width: 50px;
    height: 50px;
    font-size: 50px;
    line-height: 50px;
    color: #d5d4d7;
    text-align: center;
    .centerer(false, true);

    &.swiper-button-disabled {
        opacity: 0;
        cursor: default;
    }

    &:before {
        font-family: @font-awesome;
    }
}

._showcase-product-slider-prev {
    right: 100%;
    margin-right: 50px;

    &:before {
        content:"\f104";
    }
}

._showcase-product-slider-next {
    left: 100%;
    margin-left: 50px;

    &:before {
        content:"\f105";
    }
}

._showcase-product1-rumb1 {
    width: 180px;
    height: 180px;
    position: absolute;
    left: -37px;
    top: 30%;

    &:before {
        border: 20px solid #e8e8e8;
        border-radius: 10px;
    }
}

._showcase-product1-rumb2 {
    width: 34px;
    height: 34px;
    position: absolute;
    top: 30%;
    left: 155px;
    margin-top: 160px;

    &:before {
        border: 5px solid #191919;
        border-radius: 2px;
    }
}

._showcase-product2-rumb1 {
    width: 46px;
    height: 46px;
    position: absolute;
    top: 63%;
    left: 89.7%;

    &:before {
        border: 2px solid #191919;
        border-radius: 2px;
    }
}

._showcase-product2-rumb1-arrow {
    background-color: #191919;
    border-radius: 2px;
    width: 2px;
    height: 60px;
    top: calc(100% ~'-' 10px);
    .centerer(true, false);

    &:before,
    &:after {
        content: '';
        background-color: #191919;
        width: 2px;
        height: 10px;
        position: absolute;
        top: 100%;
        left: 0;
        transform-origin: center 0;
    }

    &:before {
        transform: rotate(-157deg);
    }

    &:after {
        transform: rotate(157deg);
    }
}

/*  Recent articles
-------------------------------------------------------------------------------*/

._blog-showcase {
    padding: 240px 30px 160px;
    overflow: hidden;

    .container {
        width: 1560px;
        max-width: 100%;
        margin: 0 auto;
    }
}

._blog-showcase-inner {
    display: table;
    width: 100%;
    table-layout: fixed;
}

._blog-showcase-small,
._blog-showcase-large {
    display: table-cell;
    vertical-align: top;
}

._blog-showcase-small {
    padding: 50px 0;
    padding-right: 40px;
}

._blog-showcase-large {
    width: 930px;
}

._blog-showcase-text {
    max-width: 400px;
    position: relative;

    &:before {
        content: '';
        background-color: #fafa90;
        width: 150px;
        height: 150px;
        position: absolute;
        top: 0;
        left: 0;
        transform-origin: left top;
        transform: rotate(45deg) translate(-60%, -40%);
        z-index: 0;
    }

    ._text {
        position: relative;
        z-index: 1;
    }
}

._blog-showcase-slider {
    padding-right: 40px;
    margin-right: -40px;
    overflow: hidden;

    ._blog-list-articles {
        white-space: nowrap;
        overflow: visible;
        margin: 0;
    }

    ._blog-list-article {
        white-space: normal;
        margin: 0;
    }
}

._blog-showcase-rumb1 {
    width: 450px;
    height: 450px;
    bottom: 18%;
    right: 0;
    transform: translate(33%, 0);

    &:before {
        background-color: #e7e7e7;
    }
}

._blog-showcase-rumb2 {
    width: 290px;
    height: 290px;
    top: 0;
    left: 50%;
    transform: translate(0, -55%);

    &:before {
        border: 20px solid #e0e0e0;
    }
}

._blog-showcase-rumb2-inner {
    border: 2px solid #191919;
    width: 46px;
    height: 46px;
    position: absolute;
    bottom: -30px;
    right: -10px;
    transform: rotate(45deg);
}

._blog-showcase-tsr {
    width: 46%;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-38%, 34%);
    z-index: 1;

    img {
        width: 100%;
        height: auto;
    }

    &:before {
        content: '';
        position: absolute;
        top: 27%;
        left: -80px;
        border: 5px solid #191919;
        width: 32px;
        height: 32px;
        transform: rotate(45deg);
        transform-origin: top left;
    }
}

/*  Instagram
-------------------------------------------------------------------------------*/

._section-instagram {
    .container {
        width: 1600px;
        max-width: 100%;
    }

    ._section-title {
        text-align: center;
    }
}

._instagram-feed-head {
    display: table;
    margin-bottom: 25px;
}

._instagram-feed-author {
    color: #212121;
}

._instagram-feed-author-icon {
    font-size: 24px;
    margin-right: 0;
}

._instagram-feed-author-username {
    font-size: 20px;
}

._instagram-feed-counter {
    font-size: 24px;
    font-weight: 900;
}

._instagram-feed-info {
    font-size: 18px;
}

._instagram-feed-tags {
    font-size: 14px;
}

._instagram-feed-tag {
    margin-left: 20px;

    &:first-child {
        margin-left: 0;
    }
}

._instagram-feed-comments {
    display: none;
}













