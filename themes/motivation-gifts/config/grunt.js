module.exports = {

	theme: {
        name: 'motivation-gifts', // This name is used as a public directory name for assets. Use lowercase letters.
        variables: { // Copy/Paste all less variables with "_" prefix for replacing by php
            "color-main-background": "_color-main-background_",
            "color-main-borders": "_color-main-borders_",
            "color-main-text": "_color-main-text_",
            "color-main-secondary-text": "_color-main-secondary-text_",
            "color-main-meta-text": "_color-main-meta-text_",
            "color-main-titles": "_color-main-titles_",
            "color-main-highlight": "_color-main-highlight_",
            "color-main-icons": "_color-main-icons_",

            "color-second-background": "_color-second-background_",
            "color-second-borders": "_color-second-borders_",
            "color-second-text": "_color-second-text_",
            "color-second-secondary-text": "_color-second-secondary-text_",
            "color-second-meta-text": "_color-second-meta-text_",
            "color-second-titles": "_color-second-titles_",
            "color-second-highlight": "_color-second-highlight_",
            "color-second-image-box": "_color-second-image-box_",
            "color-second-button-background": "_color-second-button-background_",
            "color-second-button-borders": "_color-second-button-borders_",
            "color-second-button-text": "_color-second-button-text_",
            "color-second-button-background-hover": "_color-second-button-background-hover_",
            "color-second-button-borders-hover": "_color-second-button-borders-hover_",
            "color-second-button-text-hover": "_color-second-button-text-hover_",
            "color-second-button-secondary-background": "_color-second-button-secondary-background_",
            "color-second-button-secondary-borders": "_color-second-button-secondary-borders_",
            "color-second-button-secondary-text": "_color-second-button-secondary-text_",
            "color-second-button-secondary-background-hover": "_color-second-button-secondary-background-hover_",
            "color-second-button-secondary-borders-hover": "_color-second-button-secondary-borders-hover_",
            "color-second-button-secondary-text-hover": "_color-second-button-secondary-text-hover_",

            "color-header-background": "_color-header-background_",
            "color-header-borders": "_color-header-borders_",
            "color-header-text": "_color-header-text_",
            "color-header-secondary-text": "_color-header-secondary-text_",

            "color-header-icons": "_color-header-icons_",
            "color-header-icons-hover": "_color-header-icons-hover_",
            "color-header-icons-bubble-background": "_color-header-icons-bubble-background_",
            "color-header-icons-bubble-text": "_color-header-icons-bubble-text_",

            "color-navigation-background": "_color-navigation-background_",
            "color-navigation-text": "_color-navigation-text_",
            "color-navigation-hover-background": "_color-navigation-hover-background_",
            "color-navigation-hover-text": "_color-navigation-hover-text_",

            "color-dropdowns-background": "_color-dropdowns-background_",
            "color-dropdowns-borders": "_color-dropdowns-borders_",
            "color-dropdowns-text": "_color-dropdowns-text_",
            "color-dropdowns-secondary-text": "_color-dropdowns-secondary-text_",
            "color-dropdowns-meta-text": "_color-dropdowns-meta-text_",
            "color-dropdowns-highlight": "_color-dropdowns-highlight_",
            "color-dropdowns-image-box": "_color-dropdowns-image-box_",
            "color-dropdowns-button-background": "_color-dropdowns-button-background_",
            "color-dropdowns-button-borders": "_color-dropdowns-button-borders_",
            "color-dropdowns-button-text": "_color-dropdowns-button-text_",
            "color-dropdowns-button-background-hover": "_color-dropdowns-button-background-hover_",
            "color-dropdowns-button-borders-hover": "_color-dropdowns-button-borders-hover_",
            "color-dropdowns-button-text-hover": "_color-dropdowns-button-text-hover_",

            "color-footer-background": "_color-footer-background_",
            "color-footer-borders": "_color-footer-borders_",
            "color-footer-text": "_color-footer-text_",
            "color-footer-titles": "_color-footer-titles_",
            "color-footer-highlight": "_color-footer-highlight_",
            "color-footer-socials-background": "_color-footer-socials-background_",
            "color-footer-socials-icon": "_color-footer-socials-icon_",
            "color-footer-socials-icon-background": "_color-footer-socials-icon-background_",
            "color-footer-socials-icon-hover": "_color-footer-socials-icon-hover_",
            "color-footer-socials-icon-background-hover": "_color-footer-socials-icon-background-hover_",
            "color-footer-bottom-bar-background": "_color-footer-bottom-bar-background_",
            "color-footer-bottom-bar-text": "_color-footer-bottom-bar-text_",
            "color-footer-bottom-bar-links": "_color-footer-bottom-bar-links_",
            "color-footer-bottom-bar-highlight": "_color-footer-bottom-bar-highlight_",

            "color-topbar-background": "_color-topbar-background_",
            "color-topbar-borders": "_color-topbar-borders_",
            "color-topbar-text": "_color-topbar-text_",
            "color-topbar-icons": "_color-topbar-icons_",
            "color-topbar-hovers": "_color-topbar-hovers_",

            "color-slider-background": "_color-slider-background_",
            "color-slider-text": "_color-slider-text_",
            "color-slider-titles": "_color-slider-titles_",
            "color-slider-arrows": "_color-slider-arrows_",
            "color-slider-dots": "_color-slider-dots_",
            "color-slider-button-background": "_color-slider-button-background_",
            "color-slider-button-borders": "_color-slider-button-borders_",
            "color-slider-button-text": "_color-slider-button-text_",
            "color-slider-button-background-hover": "_color-slider-button-background-hover_",
            "color-slider-button-borders-hover": "_color-slider-button-borders-hover_",
            "color-slider-button-text-hover": "_color-slider-button-text-hover_",

            "color-product-listing-borders": "_color-product-listing-borders_",
            "color-product-listing-title": "_color-product-listing-title_",
            "color-product-listing-highlight": "_color-product-listing-highlight_",
            "color-product-listing-price": "_color-product-listing-price_",
            "color-product-listing-price-old": "_color-product-listing-price-old_",
            "color-product-listing-actions": "_color-product-listing-actions_",
            "color-product-listing-actions-highlight": "_color-product-listing-actions-highlight_",

            "color-product-image-background": "_color-product-image-background_",
            "color-product-image-borders": "_color-product-image-borders_",
            "color-product-price": "_color-product-price_",
            "color-product-icons": "_color-product-icons_",
            "color-product-icons-highlight": "_color-product-icons-highlight_",
            "color-product-gallery-background": "_color-product-gallery-background_",

            "color-blog-home-background": "_color-blog-home-background_",
            "color-blog-home-borders": "_color-blog-home-borders_",
            "color-blog-home-titles": "_color-blog-home-titles_",
            "color-blog-home-highlight": "_color-blog-home-highlight_",

            "color-blog-home-article-background": "_color-blog-home-article-background_",
            "color-blog-home-article-borders": "_color-blog-home-article-borders_",
            "color-blog-home-article-text": "_color-blog-home-article-text_",
            "color-blog-home-article-meta-text": "_color-blog-home-article-meta-text_",
            "color-blog-home-article-titles": "_color-blog-home-article-titles_",
            "color-blog-home-article-highlight": "_color-blog-home-article-highlight_",

            "color-forms-fields-background": "_color-forms-fields-background_",
            "color-forms-fields-borders": "_color-forms-fields-borders_",
            "color-forms-fields-text": "_color-forms-fields-text_",
            "color-forms-fields-placeholder": "_color-forms-fields-placeholder_",
            "color-forms-fields-icons": "_color-forms-fields-icons_",

            "color-forms-checkbox-background": "_color-forms-checkbox-background_",
            "color-forms-checkbox-borders": "_color-forms-checkbox-borders_",
            "color-forms-checkbox-check": "_color-forms-checkbox-check_",

            "color-forms-radio-background": "_color-forms-radio-background_",
            "color-forms-radio-borders": "_color-forms-radio-borders_",
            "color-forms-radio-check": "_color-forms-radio-check_",

            "color-forms-range-slider": "_color-forms-range-slider_",
            "color-forms-range-slider-highlight": "_color-forms-range-slider-highlight_",
            "color-forms-range-slider-sliders": "_color-forms-range-slider-sliders_",

            "color-button-background": "_color-button-background_",
            "color-button-borders": "_color-button-borders_",
            "color-button-text": "_color-button-text_",
            "color-button-background-hover": "_color-button-background-hover_",
            "color-button-borders-hover": "_color-button-borders-hover_",
            "color-button-text-hover": "_color-button-text-hover_",
            "color-button-secondary-background": "_color-button-secondary-background_",
            "color-button-secondary-borders": "_color-button-secondary-borders_",
            "color-button-secondary-text": "_color-button-secondary-text_",
            "color-button-secondary-background-hover": "_color-button-secondary-background-hover_",
            "color-button-secondary-borders-hover": "_color-button-secondary-borders-hover_",
            "color-button-secondary-text-hover": "_color-button-secondary-text-hover_",
            "color-button-thertiary-background": "_color-button-thertiary-background_",
            "color-button-thertiary-borders": "_color-button-thertiary-borders_",
            "color-button-thertiary-text": "_color-button-thertiary-text_",
            "color-button-thertiary-background-hover": "_color-button-thertiary-background-hover_",
            "color-button-thertiary-borders-hover": "_color-button-thertiary-borders-hover_",
            "color-button-thertiary-text-hover": "_color-button-thertiary-text-hover_",

            "color-text-box-background": "_color-text-box-background_",
            "color-text-box-borders": "_color-text-box-borders_",
            "color-text-box-text": "_color-text-box-text_",
            "color-text-box-titles": "_color-text-box-titles_",
            "color-text-box-tooltip-background": "_color-text-box-tooltip-background_",
            "color-text-box-tooltip-borders": "_color-text-box-tooltip-borders_",
            "color-text-box-tooltip-text": "_color-text-box-tooltip-text_",

            "color-text1-background": "_color-text1-background_",
            "color-text1-text": "_color-text1-text_",
            "color-text1-titles": "_color-text1-titles_",
            "color-text1-highlight": "_color-text1-highlight_",
            "color-text2-background": "_color-text2-background_",
            "color-text2-text": "_color-text2-text_",
            "color-text2-titles": "_color-text2-titles_",
            "color-text2-highlight": "_color-text2-highlight_",
            "color-text3-background": "_color-text3-background_",
            "color-text3-text": "_color-text3-text_",
            "color-text3-titles": "_color-text3-titles_",
            "color-text3-highlight": "_color-text3-highlight_",

            "color-promo-bar-background": "_color-promo-bar-background_",
            "color-promo-bar-text": "_color-promo-bar-text_",
            "color-promo-bar-close": "_color-promo-bar-close_",
            "color-promo-bar-button-background": "_color-promo-bar-button-background_",
            "color-promo-bar-button-borders": "_color-promo-bar-button-borders_",
            "color-promo-bar-button-text": "_color-promo-bar-button-text_",
            "color-promo-bar-button-background-hover": "_color-promo-bar-button-background-hover_",
            "color-promo-bar-button-borders-hover": "_color-promo-bar-button-borders-hover_",
            "color-promo-bar-button-text-hover": "_color-promo-bar-button-text-hover_",

            "color-breadcrumb-background": "_color-breadcrumb-background_",
            "color-breadcrumb-text": "_color-breadcrumb-text_",
            "color-breadcrumb-text-active": "_color-breadcrumb-text-active_",
            "color-breadcrumb-text-hover": "_color-breadcrumb-text-hover_",

            "color-pagination-borders": "_color-pagination-borders_",
            "color-pagination-text": "_color-pagination-text_",
            "color-pagination-highlight": "_color-pagination-highlight_",
            "color-pagination-disabled": "_color-pagination-disabled_",

            "color-tooltips-background": "_color-tooltips-background_",
            "color-tooltips-borders": "_color-tooltips-borders_",
            "color-tooltips-text": "_color-tooltips-text_",

            "color-label-new-background": "_color-label-new-background_",
            "color-label-new-text": "_color-label-new-text_",
            "color-label-sale-background": "_color-label-sale-background_",
            "color-label-sale-text": "_color-label-sale-text_",
            "color-label-discount-background": "_color-label-discount-background_",
            "color-label-discount-text": "_color-label-discount-text_",
            "color-label-leasing-background": "_color-label-leasing-background_",
            "color-label-leasing-text": "_color-label-leasing-text_",
            "color-label-delivery-background": "_color-label-delivery-background_",
            "color-label-delivery-text": "_color-label-delivery-text_",
            "color-label-custom-background": "_color-label-custom-background_",
            "color-label-custom-text": "_color-label-custom-text_",
            "color-label-featured-background": "_color-label-featured-background_",
            "color-label-featured-text": "_color-label-featured-text_",

            "color-popups-background": "_color-popups-background_",
            "color-popups-borders": "_color-popups-borders_",
            "color-popups-text": "_color-popups-text_",
            "color-popups-titles": "_color-popups-titles_",
            "color-popups-highlight": "_color-popups-highlight_",
            "color-popups-button-background": "_color-popups-button-background_",
            "color-popups-button-borders": "_color-popups-button-borders_",
            "color-popups-button-text": "_color-popups-button-text_",
            "color-popups-button-background-hover": "_color-popups-button-background-hover_",
            "color-popups-button-borders-hover": "_color-popups-button-borders-hover_",
            "color-popups-button-text-hover": "_color-popups-button-text-hover_",
            "color-popups-button-secondary-background": "_color-popups-button-secondary-background_",
            "color-popups-button-secondary-borders": "_color-popups-button-secondary-borders_",
            "color-popups-button-secondary-text": "_color-popups-button-secondary-text_",
            "color-popups-button-secondary-background-hover": "_color-popups-button-secondary-background-hover_",
            "color-popups-button-secondary-borders-hover": "_color-popups-button-secondary-borders-hover_",
            "color-popups-button-secondary-text-hover": "_color-popups-button-secondary-text-hover_",

            //fonts
            "font-family-main": "_font-family-main_",
            "font-family-titles": "_font-family-titles_",
            "font-family-buttons": "_font-family-buttons_",
            "font-family-product-list-title": "_font-family-product-list-title_",

            "font-size-main":"_font-size-main_",
            "font-size-heading-1": "_font-size-heading-1_",
            "font-size-heading-2": "_font-size-heading-2_",
            "font-size-heading-3": "_font-size-heading-3_",
            "font-size-heading-4": "_font-size-heading-4_",
            "font-size-heading-5": "_font-size-heading-5_",
            "font-size-heading-6": "_font-size-heading-6_",
            "font-size-buttons": "_font-size-buttons_",
            "font-size-product-list-title": "_font-size-product-list-title_",

            "font-weight-main": "_font-weight-main_",
            "font-weight-titles": "_font-weight-titles_",
            "font-weight-buttons": "_font-weight-buttons_",
            "font-weight-product-list-title": "_font-weight-product-list-title_",

            "font-style-main": "_font-style-main_",
            "font-style-titles": "_font-style-titles_",
            "font-style-buttons": "_font-style-buttons_",
            "font-style-product-list-title": "_font-style-product-list-title_",
            
            //other
            "image-orientation": "_image-orientation_"
        }
    },
	server: {
		http: 'http://test1234.ccdev.pro' // Write http address of the theme
	},
	fonts: {
		google: [ // Array of all google fonts
			{
				family: 'Roboto',
				subsets: ['latin', 'cyrillic', 'greek'],
				styles: [300, 400, 700, 900, '300italic', '400italic', '700italic']
			}
		],
		embed: { // Array of all embed fonts. If you don't have embed fonts, leave this object empty
		}
	},
	vendors: { // Array of all vendors. Example: "bower-pkg-name": "version"
        devDep: {
            "jquery-breakpoint-check": "*",
            "toastr": "2.0.3",
            "twbs-pagination": "*",
            "theia-sticky-sidebar": "*",
            "jquery.scrollbar": "*"
        },
        styles: [ // All styles of the selected vendors
            '<%= _PATH.src_vendors %>/custom_pkg/jquery-ui.custom/jquery-ui.css',
            '<%= _PATH.src_vendors %>/custom_pkg/select2.custom/select2.css',
            '<%= _PATH.src_vendors %>/custom_pkg/jquery.uniform.custom/uniform.default.css',
            '<%= _PATH.src_vendors %>/custom_pkg/jquery.uniform.custom/uniformjs.css',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/less/bootstrap.less',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/less/font-awesome.less',
            '<%= _PATH.src_vendors %>/jquery.scrollbar/jquery.scrollbar.css'
        ],
        scripts: [ // All scripts of the selected vendors
            '<%= _PATH.src_vendors %>/jquery-breakpoint-check/js/jquery-breakpoint-check.js',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/dist/js/bootstrap.js',
            '<%= _PATH.src_vendors %>/custom_pkg/select2.custom/select2.js',
            '<%= _PATH.src_vendors %>/custom_pkg/resize-sensor/ResizeSensor.js',
            '<%= _PATH.src_vendors %>/bootstrap-touchspin/src/jquery.bootstrap-touchspin.js',
            '<%= _PATH.src_vendors %>/toastr/toastr.js',
            '<%= _PATH.src_vendors %>/twbs-pagination/jquery.twbsPagination.js',
            '<%= _PATH.src_vendors %>/jquery.scrollbar/jquery.scrollbar.js',
            '<%= _PATH.src_vendors %>/theia-sticky-sidebar/js/theia-sticky-sidebar.js'
        ],
        fonts: [
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.eot',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.svg',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.ttf',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.woff',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.woff2',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.eot',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.svg',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.ttf',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.woff',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.woff2'
        ]
    }
};
