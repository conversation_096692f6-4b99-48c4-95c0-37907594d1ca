[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 1, "url_handle": "kolie-s-vgradena-zapalka", "name": "Колие с вградена запалка", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p><strong>Lorem Ipsum</strong><span>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>\n<p><span><strong>Lorem Ipsum</strong>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>", "seo_title": "Колие с вградена запалка", "seo_description": "Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, ког", "category_id": 1, "vendor_id": null, "image_id": 1, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 9740, "price_to": 9740, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 2, "short_description": null, "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 2, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 9740, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 1, "name": "Колие с вградена запалка", "parent_id": 1, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "kolie-s-v<PERSON>na-zapalka-image_61e55731a97ef.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 1, "color_id": 146, "percents": 69, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 2, "color_id": 50, "percents": 14, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 3, "color_id": 73, "percents": 10, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 132558}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 3, "name": "Колие с вградена запалка", "parent_id": 1, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "kolie-s-v<PERSON>na-zapalka-image_61e5585fbd82b.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 6, "color_id": 146, "percents": 69, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 7, "color_id": 50, "percents": 13, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 8, "color_id": 73, "percents": 9, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 9, "color_id": 130, "percents": 6, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 133501}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 1, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 2, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 3, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "stenna-otvaryachka-za-bira", "name": "Стенна отварячка за бира", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p><strong>Lorem Ipsum</strong><span>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>\n<p><span><strong>Lorem Ipsum</strong>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>", "seo_title": "Колие с вградена запалка", "seo_description": "Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, ког", "category_id": 1, "vendor_id": null, "image_id": 4, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 4420, "price_to": 4420, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 4, "short_description": null, "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 4, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 4420, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 4, "name": "Колие с вградена запалка-Copy", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "kolie-s-v<PERSON>na-zapalka-copy-image_61e558b86866e.png", "background": "#DCDCDC", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 10, "color_id": 50, "percents": 46, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 11, "color_id": 138, "percents": 15, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 12, "color_id": 130, "percents": 11, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 13, "color_id": 26, "percents": 7, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 14, "color_id": 73, "percents": 6, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 15, "color_id": 146, "percents": 5, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 16, "color_id": 115, "percents": 5, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 1269299}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 5, "name": "Колие с вградена запалка-Copy", "parent_id": 2, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "kolie-s-v<PERSON>na-zapalka-copy-image_61e558d31038e.png", "background": "#556B2F", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 17, "color_id": 30, "percents": 67, "R": 85, "G": 107, "B": 47, "hex": "#556B2F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 18, "color_id": 13, "percents": 9, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 19, "color_id": 138, "percents": 8, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 720872}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 3, "url_handle": "de<PERSON><PERSON>-ha<PERSON>-akula", "name": "Детски халат Акула", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p><strong>Lorem Ipsum</strong><span>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>\n<p><span><strong>Lorem Ipsum</strong>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>", "seo_title": "Колие с вградена запалка", "seo_description": "Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, ког", "category_id": 1, "vendor_id": null, "image_id": 6, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 10370, "price_to": 10370, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 6, "short_description": null, "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 6, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 10370, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 6, "name": "Стенна отварячка за бира-Copy", "parent_id": 3, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "stenna-otvaracka-za-bira-copy-image_61e55915b25e2.jpeg", "background": "#DCDCDC", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 20, "color_id": 50, "percents": 31, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 21, "color_id": 73, "percents": 23, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 22, "color_id": 146, "percents": 16, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 23, "color_id": 80, "percents": 11, "R": 119, "G": 136, "B": 153, "hex": "#778899"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 24, "color_id": 130, "percents": 7, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 444004}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 7, "name": "Стенна отварячка за бира-Copy", "parent_id": 3, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "stenna-otvaracka-za-bira-copy-image_61e559191ba16.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 25, "color_id": 146, "percents": 33, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 26, "color_id": 81, "percents": 11, "R": 176, "G": 196, "B": 222, "hex": "#B0C4DE"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 27, "color_id": 122, "percents": 9, "R": 188, "G": 143, "B": 143, "hex": "#BC8F8F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 28, "color_id": 44, "percents": 8, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 29, "color_id": 50, "percents": 8, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 409305}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 7, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 8, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 9, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 4, "url_handle": "darvena-postavka-za-byuro", "name": "Дървена поставка за бюро", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p><strong>Lorem Ipsum</strong><span>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>\n<p><span><strong>Lorem Ipsum</strong>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>", "seo_title": "Колие с вградена запалка", "seo_description": "Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, ког", "category_id": 1, "vendor_id": null, "image_id": 8, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 12700, "price_to": 12700, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 8, "short_description": null, "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 8, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 12700, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 8, "name": "Детски халат Акула-Copy", "parent_id": 4, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "de<PERSON>i-ha<PERSON>-a<PERSON><PERSON>-copy-image_61e5594964b76.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 30, "color_id": 146, "percents": 47, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 31, "color_id": 50, "percents": 29, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 245430}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 9, "name": "Детски халат Акула-Copy", "parent_id": 4, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "de<PERSON>i-ha<PERSON>-a<PERSON><PERSON>-copy-image_61e5594d96b27.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 32, "color_id": 146, "percents": 47, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 33, "color_id": 50, "percents": 27, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 246926}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 10, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 11, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 12, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 5, "url_handle": "darvena-postavka-za-byuro-copy", "name": "Подаръчен комплект - Детокс", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p><strong>Lorem Ipsum</strong><span>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>\n<p><span><strong>Lorem Ipsum</strong>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>", "seo_title": "Колие с вградена запалка", "seo_description": "Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, ког", "category_id": 2, "vendor_id": null, "image_id": 12, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 22700, "price_to": 22700, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 10, "short_description": null, "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 10, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 22700, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 12, "name": "Дървена поставка за бюро-Copy", "parent_id": 5, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "darvena-postavka-za-buro-copy-image_61e5709bea66e.png", "background": "#F5F5F5", "width": 720, "height": 720, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 46, "color_id": 146, "percents": 53, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 47, "color_id": 50, "percents": 8, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 48, "color_id": 138, "percents": 7, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 49, "color_id": 114, "percents": 6, "R": 255, "G": 218, "B": 185, "hex": "#FFDAB9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 50, "color_id": 73, "percents": 6, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 483206}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 11, "name": "Дървена поставка за бюро-Copy", "parent_id": 5, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "darvena-postavka-za-buro-copy-image_61e570622acb0.png", "background": "#DCDCDC", "width": 720, "height": 720, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 41, "color_id": 50, "percents": 20, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 42, "color_id": 146, "percents": 15, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 43, "color_id": 2, "percents": 10, "R": 250, "G": 235, "B": 215, "hex": "#FAEBD7"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 44, "color_id": 138, "percents": 6, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 45, "color_id": 85, "percents": 6, "R": 250, "G": 240, "B": 230, "hex": "#FAF0E6"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 439723}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 10, "name": "Дървена поставка за бюро-Copy", "parent_id": 5, "sort_order": 3, "active": "yes", "max_thumb_size": 800, "image_id": "darvena-postavka-za-buro-copy-image_61e570543039a.png", "background": "#DCDCDC", "width": 720, "height": 720, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 34, "color_id": 50, "percents": 17, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 35, "color_id": 114, "percents": 13, "R": 255, "G": 218, "B": 185, "hex": "#FFDAB9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 36, "color_id": 146, "percents": 10, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 37, "color_id": 138, "percents": 9, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 38, "color_id": 13, "percents": 8, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 39, "color_id": 44, "percents": 6, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 40, "color_id": 73, "percents": 5, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 492362}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 13, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 14, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 15, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 6, "url_handle": "podarachen-komplekt-vreme-za-chay", "name": "Подаръчен комплект - Време за Чай", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p><strong>Lorem Ipsum</strong><span>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>\n<p><span><strong>Lorem Ipsum</strong>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>", "seo_title": "Колие с вградена запалка", "seo_description": "Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, ког", "category_id": 2, "vendor_id": null, "image_id": 14, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 18400, "price_to": 18400, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 12, "short_description": null, "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 12, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 18400, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 14, "name": "Подаръчен комплект - Детокс-Copy", "parent_id": 6, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "podaracen-komplekt-detoks-copy-image_61e570dd5757d.png", "background": "#F5F5F5", "width": 720, "height": 720, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 55, "color_id": 146, "percents": 76, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 56, "color_id": 50, "percents": 6, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 57, "color_id": 138, "percents": 5, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 394994}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 13, "name": "Подаръчен комплект - Детокс-Copy", "parent_id": 6, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "podaracen-komplekt-detoks-copy-image_61e570cdb8444.png", "background": "#F5F5F5", "width": 720, "height": 720, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 51, "color_id": 146, "percents": 38, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 52, "color_id": 50, "percents": 11, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 53, "color_id": 44, "percents": 7, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 54, "color_id": 73, "percents": 7, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 425303}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 16, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 17, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 18, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 7, "url_handle": "podarachen-komplekt-welcome", "name": "Подаръчен комплект - Welcome", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p><strong>Lorem Ipsum</strong><span>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>\n<p><span><strong>Lorem Ipsum</strong>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>", "seo_title": "Колие с вградена запалка", "seo_description": "Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, ког", "category_id": 2, "vendor_id": null, "image_id": 16, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 13650, "price_to": 13650, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 14, "short_description": null, "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 14, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 13650, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 16, "name": "Подаръчен комплект - Време за Чай-Copy", "parent_id": 7, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "podaracen-komplekt-vreme-za-caj-copy-image_61e5712f51b35.png", "background": "#F5F5F5", "width": 720, "height": 720, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 65, "color_id": 146, "percents": 55, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 66, "color_id": 2, "percents": 12, "R": 250, "G": 235, "B": 215, "hex": "#FAEBD7"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 527338}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 15, "name": "Подаръчен комплект - Време за Чай-Copy", "parent_id": 7, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "podaracen-komplekt-vreme-za-caj-copy-image_61e57126a61d0.png", "background": "#F5F5F5", "width": 720, "height": 720, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 58, "color_id": 146, "percents": 24, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 59, "color_id": 2, "percents": 11, "R": 250, "G": 235, "B": 215, "hex": "#FAEBD7"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 60, "color_id": 34, "percents": 9, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 61, "color_id": 129, "percents": 9, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 62, "color_id": 138, "percents": 9, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 63, "color_id": 50, "percents": 7, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 64, "color_id": 85, "percents": 6, "R": 250, "G": 240, "B": 230, "hex": "#FAF0E6"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 579965}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 19, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 20, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 21, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 8, "url_handle": "podarachen-komplekt-za-neya", "name": "Подаръчен комплект - За нея", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p><strong>Lorem Ipsum</strong><span>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>\n<p><span><strong>Lorem Ipsum</strong>&nbsp;е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, когато неизвестен печатар взема няколко печатарски букви и ги разбърква, за да напечата с тях книга с примерни шрифтове. Този начин не само е оцелял повече от 5 века, но е навлязъл и в публикуването на електронни издания като е запазен почти без промяна. Популяризиран е през 60те години на 20ти век със издаването на Letraset листи, съдържащи Lorem Ipsum пасажи, популярен е и в наши дни във софтуер за печатни издания като Aldus PageMaker, който включва различни версии на Lorem Ipsum.</span></p>", "seo_title": "Колие с вградена запалка", "seo_description": "Lorem Ipsum е елементарен примерен текст, използван в печатарската и типографската индустрия. Lorem Ipsum е индустриален стандарт от около 1500 година, ког", "category_id": 2, "vendor_id": null, "image_id": 18, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 15520, "price_to": 15520, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 16, "short_description": null, "featured": 1, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 16, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 15520, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 18, "name": "Подаръчен комплект - Welcome-Copy", "parent_id": 8, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "podaracen-komplekt-welcome-copy-image_61e57172331ce.png", "background": "#F5F5F5", "width": 720, "height": 720, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 71, "color_id": 146, "percents": 65, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 72, "color_id": 50, "percents": 18, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 346107}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 17, "name": "Подаръчен комплект - Welcome-Copy", "parent_id": 8, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "podaracen-komplekt-welcome-copy-image_61e571659c4f2.png", "background": "#F5F5F5", "width": 720, "height": 720, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 67, "color_id": 146, "percents": 53, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 68, "color_id": 85, "percents": 12, "R": 250, "G": 240, "B": 230, "hex": "#FAF0E6"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 69, "color_id": 50, "percents": 12, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 70, "color_id": 138, "percents": 6, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 456516}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 22, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 23, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 24, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}]