@media (max-width: @screen-xs) {

	._header {
		padding: 5px 0;
	}

	._button, a._button {
		height: 40px;
	}

	.js-sidebar-filters {
		padding: 10px 10px 30px 10px;
	}

	._breadcrumb-title {
		font-size: @font-size-heading-2 * @golden-ratio-ngt;
	}

	._home-showcase {
		padding-top: 30px;

		&__products {

			._showcase-product-title h2 {
				font-size: @font-size-heading-2 * @golden-ratio-ngt;
			}

			._showcase-product ._products-list {

				._product {

					._product-info ._product-name {
						min-height: 50px;
					}
				}
			} 
		}

		&__brands {
			padding-top: 30px;

			._view-all {
				top: -5px;
			}
		 
			._showcase {
				margin-bottom: 30px;
				
				._showcase-list ._showcase-item {
					width: 50%;
				}

				._showcase-title h2 {
					font-size: @font-size-heading-2 * @golden-ratio-ngt;
					padding-right: 70px;
				}
			}
		}

		&__main {
			padding: 30px 0;

			&__categories ._showcase-list {

				._showcase-item-info ._showcase-item-name {
					font-size: @font-size-home-main-category-showcase-title * @golden-ratio-ngt;
				}

				._showcase-item {

					a {
						flex-flow: column;
					}
					
					&-image {
						order: 2;
						max-width: 100%;
					}

					&-info {
						max-width: 100%;
						margin: 0 0 20px 0;
					}
				}
			}

			&__products {
				margin-top: 30px;
			}
		}
	}

	._home-latestblog {

		&__title ._view-all {
			top: 0;
		}
	
		._blog-list-article-info {
			
			._blog-list-article-meta {
				left: 0;
				width: 100%;
				margin-top: 0;
				top: -65px;

				._blog-list-article-time {
					padding: 10px;
				}
			}
		}
	}

	._home-testimonials {
		margin-bottom: 30px;

		._slider .slides .slide .slide-image {
			width: 100px;
			height: 100px;
		}
	}

	._product-details__gallery .swiper-button {

		&-next {
			right: 10px;

			& when (@rtl) {
				right: -10px;
			}
		}

		&-prev {
			left: 10px;

			& when (@rtl) {
				left: -10px;
			}
		}
	}

	._product {

		._product-image-thumb-holder {
			text-align: center;
		}

		._product-info {

			._product-options ._product-price {
				width: 95%;
				font-size: @font-size-product-price - 4;

				._product-price-old {
					margin: 0 10px;
				}
			}

			._product-name h3 {
				font-size: @font-size-product-listing-title * @golden-ratio-ngt + 2 !important;
			}
		}
	}

	._pagination {
		padding: 20px 0 30px;

		.pagination {
			font-size: @font-size-pagination * @golden-ratio-ngt; 

			li a {
				padding: 0;
			}
		}
	}

	._products-list-sidebar ._filter-box {
		padding: 15px 0;

		&__title {
			font-size: @font-size-heading-5 * @golden-ratio-ngt;
		}
	}

	._products-info-head-item {
		
		&-title {
			text-align: center;
		}

		&-image {
			float: none;
			margin: 0 auto;

			&-thumb {
				width: auto;
				height: auto;

				img {
					position: static;
				}
			}
		}

		&-description {
			margin-top: 20px;
			text-align: center;
		}
	}

	._vendor-image-thumb {
		height: auto;
		margin: 20px;
		text-align: center;

		img {
			position: static;
			transform: none;
		}
	}

	._vendor-info ._vendor-name {
		padding: 0 10px;
		font-size: @font-size-heading-5 * @golden-ratio-ngt;
	}

	._blog-list-article-info {

		._blog-list-article-meta {
			display: flex;
			flex-flow: row;
			margin: 10px 0;

			._blog-list-article-author {
				position: static;
				left: 0;
				bottom: 0;
				order: 2;
				flex: 1 1 auto;
				max-width: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 10px;

				&:before {
					display: none;
				}
			}

			._blog-list-article-time {
				margin-top: 0;
				flex: 1 1 auto;
				max-width: 50%;
				padding: 15px 10px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		._blog-list-article-text {
			padding-left: 0;
			margin-top: 0;
			margin-bottom: 15px;
		}

		._blog-list-article-title h3 {
			font-size: @font-size-blog-single-title - 4;
		}
	} 

	._blog-article {

		&-title {
			font-size: @font-size-blog-single-title * @golden-ratio-ngt;
		}

		&-text {
			font-size: @font-size-blog-article-description - 2;
		}

		&-meta {
			display: flex;
			flex-flow: row wrap;
			margin: 10px 0;
			float: none;

			&-author {
				position: static;
				left: 0;
				bottom: 0;
				order: 2;
				flex: 1 1 auto;
				max-width: 50%;
				display: flex;
				flex-flow: row wrap;
				align-items: center;
				justify-content: center;
				padding: 10px;

				span {
					padding-right: 5px;
				}

				&:before {
					display: none;
				}
			}

			&-time {
				margin-top: 0;
				flex: 1 1 auto;
				max-width: 50%;
				padding: 15px 10px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		&-comments ._blog-article-comment {
			padding: 20px 0;
		}

		._section-title h3 {
			font-size: @font-size-blog-single-title - 4;
		}
	}

	._textbox {
		font-size: @font-size-base - 2;
	}

	._faq ul li {
		
		._faq-title h4 {
			font-size: @font-size-faq-titles - 2;
		}
		
		._faq-text {
			max-width: 100%;
			font-size: @font-size-base - 2;
		}
	}

	._static-holder {
		margin: 20px 0 45px 0;
	}

	._blog-filters {
		bottom: 0;
		font-size: @font-size-base - 2;
	}

	._account {

		&__main {

			&__orders {
				font-size: @font-size-base - 2;
			}

			._button ._figure-stack-label {
				font-size: @font-size-account-buttons - 4;
				padding: 0;
			}
		} 

		&__sidebar__navigation ul li a {
			padding: 7px 0;
			font-size: @font-size-base - 2;
		}
	}

	._order-details-meta {
		
		._date-status {
			margin-bottom: 20px;
		}
	
		._addresses {
			flex-flow: column;
			margin-bottom: 10px;

			> div {
				max-width: 100%;
				margin-bottom: 10px;
			}
		} 
	}

	._oder-details-item ._order-details-info h4 {
		font-size: @font-size-base - 2;
	}

	._orders-accordion div[data-click] > span {
		max-width: 100%;
		flex: 0 1 auto;
	}

	._field {
		height: 40px;
	}

	._notification {
		padding: 15px 55px 15px 15px;
		font-size: @font-size-base - 2;
	}

	.table-responsive {
		font-size: @font-size-base - 2;
	}

	._cart-summary-holder {
		padding: 15px;
	}

	._cart-sidebar-summary {
		font-size: @font-size-base - 2;

		> ._section-title h3 {
			font-size: @font-size-base - 2;
			padding: 10px 5px;
		}
	}

	._cart-totals-box ._cart-totals-box-row > span._cart-totals-box-row-value {
		font-size: @font-size-base - 2;
	}

	._cart {
		margin-bottom: 30px;

		&-products-holder ._cart-products-list ._cart-product {
			padding: 15px;
			margin-bottom: 15px;

			> div {

				&._cart-product-details ._cart-product-info {
					flex-flow: column;
					align-items: center;

					._cart-product-image ._cart-product-image-thumb {
						margin-right: 0;
						height: auto;
						width: 100%;

						img {
							position: relative;
						}
					}

					._cart-product-box {
						margin-top: 10px;
						padding-bottom: 5px;
						text-align: center;

						._cart-product-single-price {
							text-align: center;
						}
					}
				}

				&._cart-product-quantity {
					width: 100%;
					position: static;
					text-align: center;
				}

				&._cart-product-total-price {
					position: static;
					text-align: center;
					margin-top: 10px;
				}
			}
		}
	}

	._checkout-main {
		padding: 15px;
		font-size: @font-size-base - 2;
	}

	._checkout-express {

		._section-title {

			h2,h3 {
				font-size: @font-size-heading-5 - 4;
			}
		}
	}

	._checkout-account {

		&-tabs {

			label {
				font-size: @font-size-heading-5 - 6;
			}

		}

		&-forms {

			._checkout-account-credentials {
				font-size: @font-size-heading-5 - 4;
			}
		}
	}

	._checkout-shipping-providers ul li {
		width: 100%;

		._radio {
			width: ~'calc(100% - 60px)';
			text-align: right;
		}

		._checkout-shipping-provider-features {
			text-align: center;

			._checkbox {
				display: inline-block;
			}
		}
	}

	._footer {

		&__top {
			padding: 30px 15px;
			
			&__navigation ._navigation-footer > ul {
				flex-flow: column;

				> li {
					width: 100%;
					padding-right: 0;

					&.item-collapse > a {
						font-size: @font-size-heading-5 - 4;
					}

					a {
						font-size: @font-size-nav - 4;
						height: auto;
					}
				}
			}

			&__contacts  {

				._text-title h6, ._newsletter-title h6 {
					font-size: @font-size-heading-5 - 4;
				}

				._text-description {
					font-size: @font-size-footer-text-description - 4;
				}

				&__newsletter {
					margin-top: 30px;
				}
			}
		}

		&__middle {

			&__social {
				width: 100%;
				justify-content: center;
			}

			h4 {
				font-size: @font-size-base - 2;
			}
		}

		&__bottom aside {
			font-size: @font-size-base - 2;
			flex-flow: column;

			._powered:before {
				display: none;
			}
		}
	}

	._account {
		.table-responsive {

            thead {
                display: none;

                border-collapse:separate; 
                border-spacing:5em;
            }

            tr {
                margin-top: 30px;
                display: block;

                &:first-of-type {
                    margin: 0;
                }
            }

            td {
                display: block;
                text-align: right;
                overflow: hidden;
                line-height: 25px;
                max-width: 100%;

                &:first-of-type {
                    border: none;
                }

                &:before {
                    content: attr(data-title);
                    float: left;
                }

                a {
                    display: inline-block;
                    float: right;
                }
            }
        }
	}

	._form ._form-row {
		
		& + ._form-row, & + ._form-row-group {
			margin-top: 15px;
		}
	}

	._popup-title h4 {
		font-size: @font-size-heading-4 - 4;
	}

	.input-group .input-group-btn .btn {
		width: 40px;
		height: 40px;
	}

	._product-details-price ._product-details-price-bar {
		min-height: 45px;

		> span {
			
			&._product-details-price-new {
				font-size: @font-size-product-details-price * @golden-ratio-ngt + 2;
			}
			&._product-details-price-old {
				font-size: @font-size-product-details-price * @golden-ratio-ngt ;
			} 
		}
	}

	._checkout-payment-providers {

		._form ._form-row {
			flex-flow: column;

			._form-col {
				width: 100%;
				padding-right: 0;
			}
		} 
	}

	._product-summary {
		padding-top: 5px;
		padding-bottom: 5px;
		
		&__main {

			&__image {
				width: 35px;
				height: 35px;
			}

			&__info {
				margin-left: 2px;
				padding-right: 2px;

				&__title {
					font-size: @font-size-heading-6 - 3;
				}

				&__price {
					font-size: @font-size-base - 3;

					&.old {
						font-size: @font-size-base - 4;
					}
				}
			}
		}

		&__add {
			height: auto;
			padding: 5px;
			min-width: 75px;

			&._button {
				font-size: @font-size-base - 4;
			}
		}
	}
}