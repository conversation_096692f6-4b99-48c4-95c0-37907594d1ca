/*=============================================================================*\
    SLIDER
\*=============================================================================*/

/*  OWLCAROUSEL
-------------------------------------------------------------------------------*/

.owl-carousel .animated {
	-webkit-animation-duration: 1000ms;
			animation-duration: 1000ms;
	-webkit-animation-fill-mode: both;
			animation-fill-mode: both;
}

.owl-carousel .owl-animated-in {
	z-index: 0;
}

.owl-carousel .owl-animated-out {
	z-index: 1;
}

.owl-carousel .fadeOut {
	-webkit-animation-name: fadeOut;
			animation-name: fadeOut;
}

@-webkit-keyframes fadeOut {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}

@keyframes fadeOut {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0;
	}
}

.owl-height {
	-webkit-transition: height 500ms ease-in-out;
	        transition: height 500ms ease-in-out;
}

.owl-carousel {
	display: none;
	width: 100%;
	-webkit-tap-highlight-color: transparent; /* theme */
	position: relative;
	z-index: 1;

	.owl-stage {
		position: relative;
		-ms-touch-action: pan-Y;
	}

	.owl-stage:after {
		content: ".";
		display: block;
		clear: both;
		visibility: hidden;
		line-height: 0;
		height: 0;
	}

	.owl-stage-outer {
		position: relative;
		overflow: hidden;
		-webkit-transform: translate3d(0px, 0px, 0px);
	}

	.owl-item {
		position: relative;
		min-height: 1px;
		float: left;
		-webkit-backface-visibility: hidden;
		-webkit-tap-highlight-color: transparent; /* theme */
		-webkit-touch-callout: none;
		
		img {
			display: block;
			width: 100%;
			-webkit-transform-style: preserve-3d;
					transform-style: preserve-3d;
		}

		.owl-lazy {
			opacity: 0;
			-webkit-transition: opacity 400ms ease;
			        transition: opacity 400ms ease;
		}
	}

	.owl-nav.disabled,
	.owl-dots.disabled {
		display: none;
	}

	.owl-nav .owl-prev,
	.owl-nav .owl-next,
	.owl-dot {
		cursor: pointer;
		cursor: hand;
		-webkit-user-select: none;
		 -khtml-user-select: none;
		   -moz-user-select: none;
		    -ms-user-select: none;
		        user-select: none;
	}

	.owl-video-wrapper {
		background: #000;
		position: relative;
		height: 100%;
	}

	.owl-video-play-icon {
		position: absolute;
		height: 80px;
		width: 80px;
		left: 50%;
		top: 50%;
		margin-left: -40px;
		margin-top: -40px;
		cursor: pointer;
		z-index: 1;
		-webkit-backface-visibility: hidden;
		-webkit-transition: scale 100ms ease;
		        transition: scale 100ms ease;

		&:hover {
			-webkit-transition: scale(1.3, 1.3);
			        transition: scale(1.3, 1.3);
		}
	}

	.owl-video-playing {
		.owl-video-tn,
		.owl-video-play-icon {
			display: none;
		}
	}

	.owl-video-tn {
		opacity: 0;
		height: 100%;
		background-position: center center;
		background-repeat: no-repeat;
		background-size: contain;
		-webkit-transition: opacity 400ms ease;
		        transition: opacity 400ms ease;
	}

	.owl-video-frame {
		position: relative;
		z-index: 1;
		height: 100%;
		width: 100%;
	}
	
	&.owl-loaded {
		display: block;
	}
	
	&.owl-loading {
		opacity: 0;
		display: block;
	}
	
	&.owl-hidden {
		opacity: 0;
	}
	
	&.owl-refresh {
		.owl-item {
			display: none;
		}
	}
	
	&.owl-drag {
		.owl-item {
			-webkit-user-select: none;
			   -moz-user-select: none;
			    -ms-user-select: none;
			        user-select: none;
		}
	}
	
	&.owl-grab {
		cursor: move;
		cursor: -webkit-grab;
		cursor:     -ms-grab;
		cursor:      -o-grab;
		cursor:         grab;
	}
	
	&.owl-rtl {
		direction: rtl;

		.owl-item {
			float: right;
		}
	}
}

.no-js {
	.owl-carousel {
		display: block;
	}
}

.owl-carousel {
    .owl-item {
        .owl-video-tn {
            background-size: cover;
            padding-bottom: 56.25%;
        }

        .owl-video-play-icon {
            background-image: url(../img/png/video-play.png);
            background-size: contain;
        }
    }
}

/*  SLIDER
-------------------------------------------------------------------------------*/

._slider {
	background-color: @color-slider-background; /* theme */
	position: relative;
	z-index: @z-slider;

	&.slider-loaded {
		.loader-container {
			display: none;
		}
	}

	&.slider-video-only {
		.owl-controls {
			display: none;
		}
	}

	.slide {
		.slide-text {
			img {
				width: auto;
			}
		}
	}
}

.slides {
	overflow: hidden;

	&.owl-loaded {
		height: auto;
		overflow: visible;
	}
}

.slide {
	position: relative;

	&.slide-link {
		cursor: pointer;
	}
}

.slide-image {
	display: block;
	background-size: cover;
	background-position: center center;
	background-repeat: no-repeat;
	text-align: center;
}

.slide-text {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;

	&.topleft {
		.slide-html {
			vertical-align: top;
			text-align: left;
		}
	}
	&.topright {
		.slide-html {
			vertical-align: top;
			text-align: right;
		}
	}
	&.topcenter {
		.slide-html {
			vertical-align: top;
			text-align: center;
		}
	}
	&.middleleft {
		.slide-html {
			vertical-align: middle;
			text-align: left;
		}
	}
	&.middleright {
		.slide-html {
			vertical-align: middle;
			text-align: right;
		}
	}
	&.middlecenter {
		.slide-html {
			vertical-align: middle;
			text-align: center;
		}
	}
	&.bottomleft {
		.slide-html {
			vertical-align: bottom;
			text-align: left;
		}
	}
	&.bottomright {
		.slide-html {
			vertical-align: bottom;
			text-align: right;
		}
	}
	&.bottomcenter {
		.slide-html {
			vertical-align: bottom;
			text-align: center;
		}
	}
}

.slide-text-outer,
.slide-text-inner {
	height: 100%;
}

.slide-text-wrapper {
	display: table;
	width: 100%;
	height: 100%;
	max-width: 1340px;
	margin: 0 auto;
}

.slide-html-tablet,
.slide-html-mobile {
	display: none;
}

.slide-html {
	display: table-cell;
	color: @color-slider-text; /* theme */
	padding: 40px 100px;

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		margin-bottom: 10px;
		color: @color-slider-titles; /* theme */
		line-height: 1.1;
		font-weight: 300;

		&:last-child {
			margin-bottom: 0;
		}
	}

	h1 {
		font-size: 100px;
	}

	h2 {
		font-size: 80px;
	}

	h3 {
		font-size: 40px;
	}

	h4 {
		font-size: 30px;
	}

	h5 {
		font-size: 24px;
	}

	h6 {
		font-size: 20px;
	}

	p {
		margin-bottom: 16px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	._button {
		.button-colors(@color-slider-button-background, @color-slider-button-borders, @color-slider-button-text);

		@media @hover {
			&:hover {
				.button-colors(@color-slider-button-background-hover, @color-slider-button-borders-hover, @color-slider-button-text-hover);
			}
		}
	}
}

.owl-nav {
	font-size: @font-size-slider-nav;
	color: @color-slider-arrows; /* theme */
}

.owl-prev,
.owl-next {
	position: absolute;
	top: 0;
	bottom: 0;
	width: 80px;
	transition: .2s;

	&.disabled {
		display: none !important;
	}

	@media @hover {
		&:hover {
			opacity: .7;
		}
	}

	.glyphicon {
		.centerer(true, true);
		font-family: @font-awesome;
	}
}

.owl-prev {
	left: 0;

	.glyphicon {
		text-shadow: 0 0 5px rgba(0, 0, 0, .3);

		&:before {
			content: '\f104';

		    & when (@rtl) {
		        content: '\f105';
		    }
		}
	}
}

.owl-next {
	right: 0;

	.glyphicon {
		text-shadow: 0 0 5px rgba(0, 0, 0, .3);

		&:before {
			content: '\f105';

		    & when (@rtl) {
		        content: '\f104';
		    }
		}
	}
}

.owl-dots {
	.clearfix();
	.centerer(true, false);

	bottom: 20px;
}

.owl-dot {
	float: left;
	border-width: 3px;
	border-style: solid;
	border-color: @color-slider-dots; /* theme */
	border-radius: 0;
	box-shadow: 0 0 3px rgba(0, 0, 0, .3);
	width: 15px;
	height: 15px;
	margin: 0 5px;
	-webkit-transition: .2s;
	        transition: .2s;

	&.active {
		background-color: @color-slider-dots; /* theme */
	}

	@media @hover {
		&:hover {
			opacity: .7;
		}
	}
}