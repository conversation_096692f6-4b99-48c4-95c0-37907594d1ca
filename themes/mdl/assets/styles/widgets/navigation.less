/*=============================================================================*\
    NAVIGATION
\*=============================================================================*/

/*  MAIN
-------------------------------------------------------------------------------*/

._navbar {
	position: relative;
	z-index: 6;
}

._navigation {
	font-size: @font-size-nav;
	line-height: @line-height-low;
	font-family: @font-family-secondary; /* theme */
	color: @color-header-text; /* theme */

	ul {
		list-style-type: none;
	}

	.active {
		> a {
			color: @color-header-hovers; /* theme */
		}
	}

	.collapse-icon {
		display: none;
	}

	._navigation-hamburger {
		display: none;
	}
}

._navigation-main-list-item {
	display: inline-block;
	margin-right: 45px;

	&:last-child {
		margin-right: 0;
	}

	&.active {
		> a {
			color: @color-header-hovers; /* theme */
		}
	}

	@media @hover {
		&:hover {
			> ._navigation-main-list-item-link {
				color: @color-header-hovers; /* theme */
			}

			> ._navigation-dropdown {
				opacity: 1;
				visibility: visible;
			}
		}
	}
}

._navigation-main-list-item-link {
	display: block;
	padding: 37px 0;
	color: @color-header-text; /* theme */
	font-weight: bold;
	position: relative;
}

._navigation-dropdown {
	._navigation-dropdown {
		._navigation-dropdown {
			padding-left: 15px;
		}
	}
}

._navigation-dropdown-level-1 {
	background-color: @color-header-background; /* theme */
	border-bottom: 1px solid;
	border-top: 1px solid;
	border-color: @color-header-borders; /* theme */
	padding: 15px 0;
	max-height: calc(100vh ~'-' 90px);
	overflow: auto;
	position: absolute;
	top: 100%;
	left: 0;
	width: 100%;
	opacity: 0;
	visibility: hidden;
	transition: .2s;

	> ._navigation-dropdown-list {
		max-width: @container-width;
		margin: 0 auto;
		overflow: hidden;

		> ._navigation-dropdown-list-item {
			float: left;
			width: 25%;
			padding-right: 15px;

			> ._navigation-dropdown-list-item-link {
				font-weight: 600;
			}

			> ._navigation-dropdown {
				margin-top: 20px;
			}
		}
	}
}

._navigation-dropdown-list-item {
	margin: 15px 0;
}

._navigation-dropdown-list-item-link {
	color: @color-header-text; /* theme */

	@media @hover {
		&:hover {
			color: @color-header-hovers; /* theme */
		}
	}
}

._navigation-hamburger {
	display: block;
	width: 22px;

	span {
		background-color: @color-header-text; /* theme */
		display: block;
		height: 2px;
		position: relative;

		&:nth-child(1) {
			transform-origin: 0 0;
		}

		&:nth-child(2) {
			margin: 5px 0;
		}

		&:nth-child(3) {
			transform-origin: 0 0;
		}
	}

	&.active {
		span {
			&:nth-child(1) {
				top: 0;
				left: 5px;
				transform: rotate(45deg);
			}

			&:nth-child(2) {
				opacity: 0;
			}

			&:nth-child(3) {
				top: 2px;
				left: 3px;
				transform: rotate(-45deg);
			}
		}
	}
}

._navigation-hamburger-sidebar {
	display: none;
}

/*  LINKS
-------------------------------------------------------------------------------*/

._navigation-main-links {
	ul {
		list-style-type: none;
		font-weight: 600;
		.uppercase();
	}

	li {
		display: inline-block;
		font-size: @font-size-nav; /* theme */
		margin-right: 45px;

		&:last-child {
			margin-right: 0;
		}

		&.active {
			> a {
				color: @color-header-hovers; /* theme */
			}
		}
	}

	a {
		display: inline-block;
		color: @color-header-text; /* theme */

		@media @hover {
			&:hover {
				text-decoration: none;
				color: @color-header-hovers; /* theme */
			}
		}
	}
}

/*  FOOTER
-------------------------------------------------------------------------------*/

._navigation-footer {
	> ul {
		> li {
			> a {
				display: inline-block;
				font-family: @font-family-secondary; /* theme */
				font-size: @font-size-main; /* theme */
				font-weight: 600;
			}
		}
	}

	ul {
		.clearfix();
		list-style-type: none;

		ul {
			margin-top: 8px;

			li {
				float: none;
				width: auto;
				padding-right: 0;
				margin-bottom: 10px;

				&:last-child {
					margin-bottom: 0;
				}
			}

			a {
				color: @color-footer-text; /* theme */
				text-transform: initial;

				@media @hover {
					&:hover {
						color: @color-footer-hovers; /* theme */
						text-decoration: underline;
					}
				}
			}
		}
	}

	li {
		float: left;
		width: 25%;
		padding-right: 30px;
		margin-bottom: 20px;

		&:nth-child(4n+1){
			clear: both;
		}

		a {
			color: @color-footer-titles; /* theme */
			.uppercase();

			@media @hover {
				&:hover {
					color: @color-footer-hovers; /* theme */
					text-decoration: none;
				}
			}
		}
	}

	._figure-stack-icon {
		display: none;
	}
}

._navigation-footer-list {
	margin-bottom: -20px;
	overflow: hidden;
}