{"name": "zora-new", "version": "0.1", "page_builder": true, "assets": {"default_images": {"logo": "{$img_url}/themes/zora-new/img/svg/logo.svg?{$last_build}", "favicon": "", "product": {"50": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "100": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "150": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "300": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "600": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "800": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "1024": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "1280": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "1920": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}"}, "vendor": {"50": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "100": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "150": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "300": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "600": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "800": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "1024": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "1280": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "1920": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}"}, "category": {"50": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "100": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "150": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "300": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "600": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "800": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "1024": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "1280": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}", "1920": "{$img_url}/themes/zora-new/img/defaults/noimage/noimage.svg?{$last_build}"}}, "default_content_images": {"product": ["{$img_url}/themes/zora-new/img/defaults/products/product-01.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/products/product-02.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/products/product-03.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/products/product-04.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/products/product-05.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/products/product-06.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/products/product-07.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/products/product-08.svg?{$last_build}"], "category": ["{$img_url}/themes/zora-new/img/defaults/categories/category-01.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/categories/category-02.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/categories/category-03.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/categories/category-04.svg?{$last_build}"], "vendor": ["{$img_url}/themes/zora-new/img/defaults/brands/brand-01.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/brands/brand-02.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/brands/brand-03.svg?{$last_build}", "{$img_url}/themes/zora-new/img/defaults/brands/brand-04.svg?{$last_build}"]}}, "eloquent": {"products": {"list": {"with": ["image", "category", "vendor", "banners", "favorite", "categoryPropertiesOptions.property.categoriesIds"], "scope": ["quantity"]}, "box": {"with": ["image", "category", "vendor", "banners", "favorite"]}}}, "functions": {"discount": {"color": {"status": true}}, "category": {"icon": {"status": true, "images": ["themes/zora-new/img/icons/category/1.svg?{$last_build}", "themes/zora-new/img/icons/category/2.svg?{$last_build}", "themes/zora-new/img/icons/category/3.svg?{$last_build}", "themes/zora-new/img/icons/category/4.svg?{$last_build}", "themes/zora-new/img/icons/category/5.svg?{$last_build}", "themes/zora-new/img/icons/category/6.svg?{$last_build}", "themes/zora-new/img/icons/category/7.svg?{$last_build}", "themes/zora-new/img/icons/category/8.svg?{$last_build}", "themes/zora-new/img/icons/category/9.svg?{$last_build}", "themes/zora-new/img/icons/category/10.svg?{$last_build}", "themes/zora-new/img/icons/category/11.svg?{$last_build}", "themes/zora-new/img/icons/category/12.svg?{$last_build}", "themes/zora-new/img/icons/category/13.svg?{$last_build}", "themes/zora-new/img/icons/category/14.svg?{$last_build}"]}, "color": {"status": true}}, "product_showcase": {"icon": {"status": true, "images": ["themes/zora-new/img/icons/category/1.svg?{$last_build}", "themes/zora-new/img/icons/category/2.svg?{$last_build}", "themes/zora-new/img/icons/category/3.svg?{$last_build}", "themes/zora-new/img/icons/category/4.svg?{$last_build}", "themes/zora-new/img/icons/category/5.svg?{$last_build}", "themes/zora-new/img/icons/category/6.svg?{$last_build}", "themes/zora-new/img/icons/category/7.svg?{$last_build}", "themes/zora-new/img/icons/category/8.svg?{$last_build}", "themes/zora-new/img/icons/category/9.svg?{$last_build}", "themes/zora-new/img/icons/category/10.svg?{$last_build}", "themes/zora-new/img/icons/category/11.svg?{$last_build}", "themes/zora-new/img/icons/category/12.svg?{$last_build}"]}, "color": {"status": true}}, "navigations": {"icon": {"status": true}}}, "translations": "translations", "widgets": {"system_config": {"map": "zora-new.config", "class": "\\App\\Integration\\Zora\\Widgets\\Configuration", "name": {"en": "System configuration", "bg": "Настройки на системата"}, "description": {"en": "Main settings", "bg": "Основни настройки"}, "category": "custom"}, "shops": {"map": "extra.shops"}, "navigationMain": {"map": "navigation.main"}, "navigationMainMobile": {"map": "navigation.main"}, "productCompare": {"map": "product.compare"}, "productQuantity": {"map": "product.quantity"}, "navigationFooter": {"map": "navigation.footer"}, "headerText": {"map": "extra.text", "name": {"en": "Header Text", "bg": "<PERSON><PERSON><PERSON><PERSON><PERSON> текст"}, "settings": {"defaults": {"title": "", "text": "<p><i style=\" line-height: 26px; border-radius: 100%; border: 1px solid #ededed; display: flex; align-items: center; justify-content: center; padding: 11px; margin-right: 18px; margin-top: -7px;\" class=\"alignleft fal fa-phone-alt\" >&nbsp;</i><div><span style=\"font-size: 24px; color: #3D3D3D;\">*3030</span><br><span style=\"font-size: 12px; color: #3D3D3D; font-weight: 400;\">Консултация по телефон</span></div></p><style>.header-text-widget-wrap i{font-size: 0;}.header-text-widget-wrap i:before{font-size: 26px;}</style>"}}}, "navigationLinks": {"map": "navigation.links", "group": "top_bar", "name": {"en": "Navigation Links", "bg": "Навигационни връзки"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"links": [{"link_type": "section", "link_value": "blog", "link_caption": "14 дни право на връщане", "target": "", "class": ""}, {"link_type": "section", "link_value": "contacts", "link_caption": "Безплатен доставка", "target": "", "class": ""}]}}}, "productsExtra": {"map": "extra.text", "group": "text_fields", "name": {"en": "Register - Text", "bg": "Продуктова страница - допълнителна информация"}, "settings": {"defaults": {"title": "", "text": "<div class=\"widget-extra-text\"><p><a href=\"https://zora.bg/page/delivery\" target=\"_blank\" rel=\"noopener\"> <i class=\"fal fa-truck\"></i> <strong> Условия за безплатна доставка </strong> </a> <br/> <a href=\"https://zora.bg/page/delivery\" target=\"_blank\" rel=\"noopener\"> <i class=\"fas fa-clock\"></i> <strong> Срок на доставка от 2 до 5 работни дни. </strong> </a> <br/> <a href=\"https://zora.bg/page/otkaz-onlain\" target=\"_blank\" rel=\"noopener\"> <i class=\"fal fa-calendar-minus\"></i> <strong> Право на връщане в срок до 14 дни </strong> </a> <br/> <a href=\"https://zora.bg/page/magazini-zora\" target=\"_blank\" rel=\"noopener\"> <i class=\"fal fa-map-marker\"></i> <strong> Магазини </strong> </a> <br/> <a href=\"#\" target=\"_blank\" rel=\"noopener\"> <i class=\"fal fa-money-check-edit-alt\"></i> <strong> На изплащане </strong> </a></p></div>"}}}, "htmlLine": {"map": "extra.htmlLine", "group": "top_bar", "name": {"en": "Promo Bar", "bg": "Промо лента"}, "description": {"en": "Promo Bar Description", "bg": "Промо лента - описание"}, "settings": {"defaults": {"text": "", "enabled": false, "period": {"from": "", "to": ""}, "button": {"float": "right", "enabled": true, "link": "", "target": "_blank", "text": "View More"}}}}, "wishlistMenu": {"map": "wishlist.menu", "name": {"en": "Menu for latests favorites products", "bg": "Меню за последни любими продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "limit": 5}}}, "wishlist": {"map": "wishlist.listing"}, "showcaseList": {"map": "product.showcaseList"}, "productsDetails": {"map": "product.productsDetails", "name": {"en": "Detail information for product", "bg": "Детайлна информация за продукт"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {}}}, "filters": {"map": "product.filters", "mode": "range_slider", "settings": {"defaults": {"per_page": "9", "per_page_options": [9, 18, 36, 72], "products_price_ranges": [[100, 100000]], "price_range_step": "500"}}, "name": {"en": "Product listing settings", "bg": "Настройки на продуктов каталог"}, "description": {"en": "", "bg": ""}}, "categoryProperties": {"map": "product.categoryProperties", "name": {}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true}}}, "discounts": {"map": "product.discounts", "name": {"en": "Products on sale", "bg": "Продукти на разпродажба"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "title": "Промоции", "products": 4}}}, "productsRelated": {"map": "product.related", "name": {"en": "Accessories", "bg": "Съвместими аксесоари"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Съвместими аксесоари", "type": "tag"}}}, "productsRelatedRow2": {"map": "product.related", "name": {"en": "Related products", "bg": "Подобни продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Подобни продукти", "type": "tag"}}}, "linkedProducts": {"map": "product.linked", "name": {"en": "Linked products", "bg": "Свързани продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Linked products", "enabled": true}}}, "selectedProducts": {"map": "product.selectedProducts", "name": {"en": "Selected products to category", "bg": "Избрани продукти по категория"}, "description": {"en": "", "bg": ""}, "settings": {}}, "showcaseProductsFirst": {"map": "product.productShowcase", "name": {"en": "Products Showcase - First Row", "bg": "Витрина с продукти на индекса - първи ред"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Телевизори", "products": 4, "filter": "category", "filter_value": [1]}}}, "showcaseProductsSecond": {"map": "product.productShowcase", "name": {"en": "Products Showcase - Second Row", "bg": "Витрина с продукти на индекса - втори ред"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Перални и миялни", "products": 4, "filter": "category", "filter_value": [9]}}}, "showcaseProductsThird": {"map": "product.productShowcase", "name": {"en": "Products Showcase - Third Row", "bg": "Витрина с продукти на индекса - трети ред"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Компютри и аксесоари", "products": 4, "filter": "category", "filter_value": [4]}}}, "showcaseProductsFourth": {"map": "product.productShowcase", "name": {"en": "Products Showcase - Fourth Row", "bg": "Витрина с продукти на индекса - четвърти ред"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Пакетни предложения", "products": 4, "filter": "all", "type": "bundle", "enabled": false}}}, "lastViewedProducts": {"map": "product.lastViewed", "name": {"en": "Latest viewed products", "bg": "Последно видяни продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Последно видяни", "products": 4}}}, "showcaseCategory": {"map": "product.showcase", "name": {"en": "Category showcase", "bg": "Витрина с категории"}, "settings": {"defaults": {"header": "Categories", "type": "category", "amount": 4, "show_name": true, "show_description": true}}}, "showcaseBrand": {"map": "product.showcase", "name": {"en": "Brands showcase", "bg": "Витрина с марки"}, "settings": {"defaults": {"header": "Нашите марки", "type": "vendor", "amount": 12, "show_name": false, "show_description": false}}}, "promoProducts": {"map": "product.productShowcase", "name": {"en": "Promotions", "bg": "Промоции"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Промоции", "products": 2, "filter": "all", "sale": "yes", "new": "no"}}}, "page": {"map": "extra.page"}, "vendors": {"map": "product.vendors"}, "search": {"map": "extra.search", "group": "header", "name": {"en": "Global Search", "bg": "Глобално търсене"}, "description": {"en": "A search form for your site", "bg": "Форма за търсене"}}, "carousel": {"map": "extra.carousel", "group": "slider", "name": {"en": "Slide<PERSON>", "bg": "Слайдер"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 1, "autoplay": "no", "interval": 5000, "caption": "no", "controls": "yes", "indicators": "yes", "pause": "no", "cycle": "no", "slides": {"1": {"img_type": "external", "src": "{$img_url}/themes/zora-new/img/png/carousel/slide.png", "link_type": "section", "link_value": "products", "link_caption": "", "html": "", "horizontal_position": "center", "vertical_position": "middle", "target": "_self"}}}}}, "bannersCarousel": {"map": "extra.banner", "group": "slider", "name": {"en": "Banners Under Carousel", "bg": "Банери под слайдера"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 3, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/carousel/banner-carousel-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/carousel/banner-carousel-02.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "3": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/carousel/banner-carousel-03.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "4": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/carousel/banner-carousel-04.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "5": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/carousel/banner-carousel-05.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "6": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/carousel/banner-carousel-06.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "bannersProductsFirst": {"map": "extra.banner", "group": "banners", "name": {"en": "Banners - Home - First Row", "bg": "Банери на индекса - първи ред"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 2, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/productsfirst/banner-productsfirst-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/productsfirst/banner-productsfirst-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "bannersProductsSecond": {"map": "extra.banner", "group": "banners", "name": {"en": "Banners - Home - Second Row", "bg": "Банери на индекса - втори ред"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 3, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/productssecond/banner-productssecond-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/productssecond/banner-productssecond-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "3": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/productssecond/banner-productssecond-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "bannersProductsThird": {"map": "extra.banner", "group": "banners", "name": {"en": "Banners - Home - Third Row", "bg": "Банери на индекса - трети ред"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 1, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/productsthird/banner-productsthird-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "banners": {"map": "extra.bannerGlobal"}, "logo": {"map": "extra.logo", "editable": "no"}, "userControls": {"map": "user.controls"}, "googleMap": {"map": "contact.googleMap", "name": {"en": "Google Map", "bg": "<PERSON>у<PERSON><PERSON>л карта"}, "description": {"en": "", "bg": ""}}, "contactForm": {"map": "contact.form"}, "contactInformation": {"map": "contact.information", "name": {"en": "Contact information", "bg": "Контактна информация"}, "description": {"en": "", "bg": ""}}, "authorize": {"map": "user.authorize"}, "social": {"map": "extra.social", "group": "footer", "name": {"en": "Social links", "bg": ""}, "description": {"en": "", "bg": ""}}, "providers": {"map": "payment.providers"}, "article": {"map": "blog.article"}, "blog": {"map": "blog.blog", "name": {"en": "Blog", "bg": ""}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"per_page": 5}}}, "recentArticlesHome": {"map": "blog.recentArticles", "name": {"en": "Latest News - Home", "bg": "Последни новини на индекса"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"count": 3}}}, "recentArticles": {"map": "blog.recentArticles", "name": {"en": "Latest News", "bg": "Последни новини - глобално"}, "description": {"en": "", "bg": ""}}, "recentComments": {"map": "blog.recentComments", "name": {"en": "Recent comments", "bg": "Последни коментари"}, "description": {"en": "", "bg": ""}}, "utilities": {"map": "base.utilities"}, "share": {"map": "extra.addThisShare", "name": {"en": "Social Share", "bg": ""}, "description": {"en": "", "bg": ""}}, "sharrre": {"map": "extra.sharrreShare"}, "navigationLinksPage": {"map": "navigation.links", "name": {"en": "Navigation Links - Pages", "bg": "Навигационни връзки - статични страници"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"links": [{"link_type": "section", "link_value": "blog", "link_caption": "За търговска верига ЗОРА", "target": ""}]}}}, "cartText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Cart - Text", "bg": "Количка - текст"}, "settings": {"defaults": {"title": "Информация за доставка", "text": "<p>БЕЗПЛАТНА ДОСТАВКА ЗА УРЕДИ НА СТОЙНОСТ НАД 30.00лв.</p><p>Поръчка за артикули на стойност под 10лв. - не се обработват онлайн<br>Такса верификация при покупка на кредит - 6.00 лв.<br>Доставка на продукти закупени онлайн се извършва след уточняване начина на плащане:<ol><li>Куриерски услуги на фирма СПИДИ</li><li>Чрез транспорт ЗОРА за едрогабаритна техника в населените места, в които има магазини ЗОРА /София, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ки, Го<PERSON>е Делчев, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>л<PERSON><PERSON><PERSON><PERSON>, <PERSON>т.<PERSON><PERSON>гора,  <PERSON>а<PERSON><PERSON><PERSON>о, <PERSON><PERSON>р<PERSON>жали, <PERSON>арманл<PERSON>, <PERSON>в<PERSON><PERSON>е<PERSON>г<PERSON>а<PERSON>,  <PERSON><PERSON>о<PERSON><PERSON><PERSON>, <PERSON>у<PERSON>га<PERSON>, <PERSON>ар<PERSON>, Шу<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>г<PERSON><PERSON><PERSON>е, <PERSON>усе, <PERSON>ле<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>во, <PERSON><PERSON><PERSON><PERSON><PERSON>о, Вр<PERSON><PERSON><PERSON>, <PERSON>о<PERSON><PERSON>а<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>/  в чертите на града.</li></ol><p>Клиентът е длъжен да осигури присъствие за приемане на стоката.</p><p>* Уреди закупени онлайн чрез сайта не се доставят за получаване в магазини ЗОРА</p>"}}}, "registerText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Register - Text", "bg": "Регистрация - текст"}, "settings": {"defaults": {"title": "Направата на профил ще Ви помогне за:", "text": "<p>Бързи и лесни онлайн покупки<br> Отбелязване на любими стоки<br> Проследяване статус на поръчка<br> Пълна история на поръчки</p>"}}}, "bannersProduct": {"map": "extra.banner", "group": "products", "name": {"en": "Product - Banners", "bg": "Банери на продуктова страница"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 3, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/product/banner-product-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/product/banner-product-02.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "3": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/product/banner-product-03.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "contactText1": {"map": "extra.text", "group": "text_fields", "category": "contact", "name": {"en": "Contacts - Text 1", "bg": "Страница за контакти - текст 1"}, "settings": {"defaults": {"title": "Имате въпроси?", "text": "<p>Национален информационнен телефон за обслужване на клиенти на Търговска верига ЗОРА: <span class='_figure-stack _phone-large'><i class='_figure-stack-icon fa fa-phone'></i><span class='_figure-stack-label'>0700­18­222</span></span></p>"}}}, "contactText2": {"map": "extra.text", "group": "text_fields", "category": "contact", "name": {"en": "Contacts - Text 2", "bg": "Страница за контакти - текст 2"}, "settings": {"defaults": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> магазин", "text": "<p>Телефон: 02­813­03­94<br>Телефон: 02­813­03­78<br>Мобилен: 0879­99­68­76<br>Мобилен: 0879­99­73­72<br>Мобилен: 0879­99­91­55<br>Мобилен: 0879 99 91 56</p><p><strong>Работно време:</strong><br>Понеделник-Петък: 9:00 - 18:30<br>Събота: 9:00 - 15:30</p><p>E-mail: k.ka<PERSON><PERSON><PERSON>@zora.bg<br>E-mail: <EMAIL><br>E-mail: n.z<PERSON><PERSON><PERSON>@zora.bg<br>E-mail: <EMAIL><br>E-mail: j.angel<PERSON>@zora.bg</p>"}}}, "contactText3": {"map": "extra.text", "group": "text_fields", "category": "contact", "name": {"en": "Contacts - Text 3", "bg": "Страница за контакти - текст 3"}, "settings": {"defaults": {"title": "Магазини в страната", "text": "<p><a href='/stores' class='_button _button-full'><span class='_figure-stack'><span class='_figure-stack-label'>Магазини</span></span></a></p>"}}}, "contactText4": {"map": "extra.text", "group": "text_fields", "category": "contact", "name": {"en": "Contacts - Text 4", "bg": "Страница за контакти - текст 4"}, "settings": {"defaults": {"title": "Отдели", "text": "<p>СПРАВКИ И ВЪПРОСИ<br>Тел: 02 813 03 86<br>E-mail: s.<PERSON><PERSON><PERSON>@zora.bg</p><p>ОТДЕЛ ЧОВЕШКИ РЕСУРСИ<br>Тел.: 02 813 03 76<br>GSM: 0879 999 068<br>E-mail: <EMAIL></p><p>IT ОТДЕЛ<br>Тел.: 02 813 03 72<br>Факс: 02 936 61 57<br>E-mail: <EMAIL>; <EMAIL></p><p>ТЪРГОВСКА ДИРЕКЦИЯ<br>Тел: 02 813 03 46</p><p>ОТДЕЛ ДОСТАВКИ И ЕКСПЕДИЦИЯ<br>Тел.: 02 813 03 47, 0879 998 993<br>E-mail: <EMAIL></p><p>PR И РЕКЛАМА<br>Тел.: 02 813 03 62<br>E-mail: <EMAIL>, <EMAIL></p><p>ОТДЕЛ ТЪРГОВИЯ<br>Тел: 02 813 03 43 (48,70,80,81)<br>E-mail: <EMAIL></p><p>ИКОНОМИЧЕСКА ДИРЕКЦИЯ<br>Тел.: 02 813 03 45 (52, 60, 67, 75)<br>Факс: 02 93 66 157<br>E-mail: <EMAIL></p><p>ОТДЕЛ ЕДРОВИ ПРОДАЖБИ<br>Тел: 02 813 03 71 (68,89,91,92,93)<br>E-mail: <EMAIL></p><p>ОТДЕЛ ВЪТРЕШНА ЛОГИСТИКА<br>Тел. 02 813 03 51 (53,57,59)<br>E-mail: <EMAIL></p><p>ЛИЗИНГОВ ОТДЕЛ<br>Тел.: 02 813 03 63<br>E-mail: <EMAIL></p><p>ЦЕНТРАЛЕН СЕРВИЗ ЗОРА<br>Тел.: 02 813 03 54<br>GSM: Черна техника: 0879 999 161<br>GSM Бяла техника: 0879 998 986<br>E-mail: <EMAIL></p>"}}}, "shopsText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Shops text", "bg": "Страница за магазини"}, "settings": {"defaults": {"title": "Магазини", "text": "<p>ЗОРА е специализирана верига магазини за бяла, че<PERSON><PERSON>, климатична, IT и офис техника. Фирмата има 34 търговски обекти  и 5 локални складови бази. Магазините са в София 8, Пловдив - 2, В<PERSON><PERSON>на - 2, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 2, Стара Загора 2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ков<PERSON>, Паз<PERSON>рджик, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>р<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Севлиево, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Търговище, <PERSON><PERSON><PERSON><PERSON><PERSON>,  Гоце Делчев, Бл<PERSON>г<PERSON><PERSON>вград, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ки, <PERSON><PERSON>рдж<PERSON><PERSON>и, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Харманли и Свиленград. Складови бази в Пловдив, <PERSON><PERSON><PERSON><PERSON>, <PERSON>у<PERSON>га<PERSON>, Хасково и Севлиево.</p><p>През 2014 г. ЗОРА откри 4 нови обекта – Смолян, Бургас, Благоевград и Стара Загора  и промени локацията на 2 – Плевен и Габрово.</p>"}}}, "productDetailText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Delivery time in product page", "bg": "Срок на доставка в продуктовата страница"}, "settings": {"defaults": {"title": "Срок на доставка: 3 работни дни", "text": "Цена на доставка за цялата страна 10.00 лв без допълнително оскъпяване. За стоки на стойност над 30лв. доставката е безплатна."}}}, "bannersFooter": {"map": "extra.banner", "group": "footer", "name": {"en": "Banners Over Footer", "bg": "Банери над футъра"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 3, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/productssecond/banner-productssecond-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/productssecond/banner-productssecond-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "3": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zora-new/img/defaults/banners/productssecond/banner-productssecond-01.png", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "footerTop": {"map": "extra.text", "group": "footer", "name": {"en": "Footer top", "bg": "<PERSON>о<PERSON>на част на футъра"}, "settings": {"defaults": {"title": "", "text": "<div class=\"contacts-block\">\n<p class=\"contact-info text-alignleft\"><span style=\"font-size: 14pt;\"><span style=\"color: #3D3D3D;\" class=\"contact-info-title\">Ние сме тук, за да ви помогнем</span> <span style=\"font-size: 10pt; color: #999999; line-height: 1.2;\" class=\"contact-info-text\">Свържете се с нас при възникнали въпроси или проблеми</span></span></p>\n<div class=\"contact-box\">\n<div class=\"icon-wrap\"><a href=\"tel:*3030\"><i class=\"fal fa-phone\"></i></a></div>\n<div class=\"contact-text\">\n<p class=\"contact-text-name\"><a href=\"tel:*3030\">Национален телефон</a></p>\n<a href=\"tel:*3030\"><strong><span class=\"contact-text-info\">*3030</span></strong></a></div>\n</div>\n<div class=\"contact-box\">\n<div class=\"icon-wrap\"><a href=\"mailto:<EMAIL>\"><i class=\"fal fa-envelope-open\"></i></a></div>\n<div class=\"contact-text\">\n<p class=\"contact-text-name\"><a href=\"mailto:<EMAIL>\">E-mail</a></p>\n<a href=\"mailto:<EMAIL>\"><strong><span class=\"contact-text-info\"><EMAIL></span></strong></a></div>\n</div>\n<div class=\"contact-box\">\n<div class=\"icon-wrap\"><a href=\"/page/services\"><i class=\"fal fa-tools\"></i></a></div>\n<div class=\"contact-text\">\n<p class=\"contact-text-name\"><a href=\"/page/services\">Услуги за монтаж</a></p>\n<a href=\"/page/services\"><strong><span class=\"contact-text-info\">УСЛУГИ</span></strong></a></div>\n</div>\n<div class=\"contact-box\">\n<div class=\"icon-wrap\"><a href=\"/page/dostavka\"><i class=\"fal fa-truck\"></i></a></div>\n<div href=\"/page/dostavka\" class=\"contact-text\">\n<p class=\"contact-text-name\"><a href=\"/page/dostavka\">Условия за доставка</a></p>\n<a href=\"/page/dostavka\"><strong><span class=\"contact-text-info\">ДОСТАВКА</span></strong></a></div>\n</div>\n</div>"}}}, "freeShipping": {"map": "extra.text", "group": "text_fields", "name": {"en": "Free shipping", "bg": "Безплатна доставка"}, "settings": {"defaults": {"title": "Безплатна доставка", "text": "<p>При поръчка на стоки на стойност над 100лв. доставката за цялата страна е безплатна.</p>"}}}, "orderReturns": {"map": "extra.text", "group": "text_fields", "name": {"en": "Order returns", "bg": "Връщане на поръчка"}, "settings": {"defaults": {"title": "Връщане на поръчка", "text": "<p>При поръчка на стоки на стойност над 100лв. доставката за цялата страна е безплатна.</p>"}}}, "newsletter": {"map": "mailchimp.newsletter", "name": {"en": "Newsletter", "bg": "Абонамент"}, "settings": {"defaults": {"title": "Абонамент", "form": "true", "delay": 30}}}, "leasing": {"map": "store.leasing"}}, "globals": {"widgets": {"product": {"listing": {}, "details": {}, "showcase": {"settings": {"defaults": {"products": 4}}}}}}, "settings": {"groups": {"main": {"en": {"name": "Main"}, "bg": {"name": "Основни"}}, "promo_bar": {"en": {"name": "Promo Bar"}, "bg": {"name": "Промо лента"}}, "top_bar": {"en": {"name": "Top Bar"}, "bg": {"name": "Топ лента"}}, "header": {"en": {"name": "Header"}, "bg": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "footer": {"en": {"name": "Footer"}, "bg": {"name": "<PERSON>ут<PERSON>р"}}, "slider": {"en": {"name": "Slide<PERSON>"}, "bg": {"name": "Слайдер"}}, "products-listing": {"en": {"name": "Product listing"}, "bg": {"name": "Продуктов каталог"}}, "breadcrumb": {"en": {"name": "Breadcrumb"}, "bg": {"name": "Пътечка с линкове"}}, "pagination": {"en": {"name": "Pagination"}, "bg": {"name": "Странициране"}}, "buttons": {"en": {"name": "Buttons"}, "bg": {"name": "Бутони"}}, "labels": {"en": {"name": "Labels"}, "bg": {"name": "Етикети"}}}, "variables": {"color-main-titles": {"type": "color", "group": "main", "default": "#3D3D3D", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-main-text": {"type": "color", "group": "main", "default": "#3D3D3D", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-main-secondary-text": {"type": "color", "group": "main", "default": "#3D3D3D", "translations": {"en": {"name": "Secondary Text"}, "bg": {"name": "Второстепенен текст"}}}, "color-main-tertiary-text": {"type": "color", "group": "main", "default": "#999", "translations": {"en": {"name": "Tertiary Text"}, "bg": {"name": "Третостепенен текст"}}}, "color-main-highlight": {"type": "color", "group": "main", "default": "#0397aa", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-main-meta-text": {"type": "color", "group": "main", "default": "#999", "translations": {"en": {"name": "Meta <PERSON>t"}, "bg": {"name": "Помощен текст"}}}, "color-main-background": {"type": "color", "group": "main", "default": "#eceff1", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-main-secondary-background": {"type": "color", "group": "main", "default": "#ECEFF1", "translations": {"en": {"name": "Secondary Background"}, "bg": {"name": "Второстепенен фон"}}}, "color-main-borders": {"type": "color", "group": "main", "default": "#f3f3f3", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамки"}}}, "color-main-fields-text": {"separator": "true", "type": "color", "group": "main", "default": "#0397aa", "translations": {"en": {"name": "Fields Text"}, "bg": {"name": "Полета - текст"}}}, "color-main-fields-background": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Fields Background"}, "bg": {"name": "Полета - фон"}}}, "color-main-fields-border": {"type": "color", "group": "main", "default": "#ccc", "translations": {"en": {"name": "Fields Border"}, "bg": {"name": "Полета - рамка"}}}, "color-main-fields-secondary-text": {"type": "color", "group": "main", "default": "#ccc", "translations": {"en": {"name": "Secondary Fields Text"}, "bg": {"name": "Второстепенни полета - текст"}}}, "color-main-fields-secondary-background": {"type": "color", "group": "main", "default": "#f3f3f3", "translations": {"en": {"name": "Secondary Fields Background"}, "bg": {"name": "Второстепенни полета - фон"}}}, "color-main-fields-secondary-border": {"type": "color", "group": "main", "default": "#3D3D3D", "translations": {"en": {"name": "Secondary Fields Border"}, "bg": {"name": "Второстепенни полета - рамка"}}}, "color-product-details-gallery-background": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Lighbox gallery background"}, "bg": {"name": "Popup галерия на продукта"}}}, "color-promo-bar-background": {"type": "color", "group": "promo_bar", "default": "#0397aa", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-promo-bar-button-text": {"separator": "true", "type": "color", "group": "promo_bar", "default": "#fff", "translations": {"en": {"name": "Button Text"}, "bg": {"name": "Бутон - текст"}}}, "color-promo-bar-button-text-hover": {"type": "color", "group": "promo_bar", "default": "#fff", "translations": {"en": {"name": "Button Text Hover"}, "bg": {"name": "Бутон - текст - акцент"}}}, "color-promo-bar-button-background": {"type": "color", "group": "promo_bar", "default": "#2d2d2d", "translations": {"en": {"name": "<PERSON>ton Background"}, "bg": {"name": "Бутон - фон"}}}, "color-promo-bar-button-background-hover": {"type": "color", "group": "promo_bar", "default": "#ffffff", "translations": {"en": {"name": "Button Background Hover"}, "bg": {"name": "Бутон - фон - акцент"}}}, "color-promo-bar-button-border": {"type": "color", "group": "promo_bar", "default": "#2d2d2d", "translations": {"en": {"name": "Button Border"}, "bg": {"name": "Бутон - рамка"}}}, "color-promo-bar-button-border-hover": {"type": "color", "group": "promo_bar", "default": "#2d2d2d", "translations": {"en": {"name": "Button Border Hover"}, "bg": {"name": "Бутон - рамка - акцент"}}}, "color-promo-bar-close": {"separator": "true", "type": "color", "group": "promo_bar", "default": "#fff", "translations": {"en": {"name": "Close"}, "bg": {"name": "Затвори"}}}, "color-top-bar-background": {"type": "color", "group": "top_bar", "default": "#3d3d3d", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-top-bar-menu-text": {"separator": "true", "type": "color", "group": "top_bar", "default": "#fff", "translations": {"en": {"name": "Menu Text"}, "bg": {"name": "Меню - текст"}}}, "color-top-bar-menu-text-hover": {"type": "color", "group": "top_bar", "default": "#fff", "translations": {"en": {"name": "Menu Text Hover"}, "bg": {"name": "Меню - текст - акцент"}}}, "color-top-bar-menu-text-background-hover": {"type": "color", "group": "top_bar", "default": "#4b4b4b", "translations": {"en": {"name": "<PERSON><PERSON>"}, "bg": {"name": "Меню - фон - акцент"}}}, "color-top-bar-menu-text-background-highlight": {"type": "color", "group": "top_bar", "default": "#f64d7d", "translations": {"en": {"name": "<PERSON><PERSON> Highlight"}, "bg": {"name": "Мен<PERSON> - акцент"}}}, "color-top-bar-submenu-text": {"separator": "true", "type": "color", "group": "top_bar", "default": "#3d3d3d", "translations": {"en": {"name": "Submenu Text"}, "bg": {"name": "Подменю - текст"}}}, "color-top-bar-submenu-background": {"type": "color", "group": "top_bar", "default": "#fff", "translations": {"en": {"name": "Submenu Background"}, "bg": {"name": "Подменю - фон"}}}, "color-header-background": {"type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-header-search-text": {"separator": "true", "type": "color", "group": "header", "default": "#999", "translations": {"en": {"name": "Search Text"}, "bg": {"name": "Търсене - текст"}}}, "color-header-search-background": {"type": "color", "group": "header", "default": "#ffffff", "translations": {"en": {"name": "Search Background"}, "bg": {"name": "Търсене - фон"}}}, "color-header-search-border": {"type": "color", "group": "header", "default": "#ededed", "translations": {"en": {"name": "Search Border"}, "bg": {"name": "Търсене - рамка"}}}, "color-header-search-button": {"type": "color", "group": "header", "default": "#0097a8", "translations": {"en": {"name": "Search Icon"}, "bg": {"name": "Търсене - иконка"}}}, "color-header-user-panel-text": {"separator": "true", "type": "color", "group": "header", "default": "#3D3D3D", "translations": {"en": {"name": "User Panel Text"}, "bg": {"name": "Потреб. панел - текст"}}}, "color-header-user-panel-background": {"type": "color", "group": "header", "default": "#ffffff", "translations": {"en": {"name": "User Panel Background"}, "bg": {"name": "Потреб. панел - фон"}}}, "color-header-user-panel-background-hover": {"type": "color", "group": "header", "default": "#ffffff", "translations": {"en": {"name": "User Panel Background Hover"}, "bg": {"name": "Потреб. панел - фон - акцент"}}}, "color-header-user-panel-submenu-text": {"type": "color", "group": "header", "default": "#3D3D3D", "translations": {"en": {"name": "User Panel Submenu Text"}, "bg": {"name": "Потреб. панел - подменю - текст"}}}, "color-header-user-panel-submenu-background": {"type": "color", "group": "header", "default": "#ffffff", "translations": {"en": {"name": "User Panel Submenu Background"}, "bg": {"name": "Потреб. панел - подменю - фон"}}}, "color-header-user-panel-submenu-separator": {"type": "color", "group": "header", "default": "#ffffff", "translations": {"en": {"name": "User Panel Submenu Separator"}, "bg": {"name": "Потреб. панел - подменю - сепаратор"}}}, "color-footer-titles": {"type": "color", "group": "footer", "default": "#3D3D3D", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-footer-text": {"type": "color", "group": "footer", "default": "#656565", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-footer-secondary-text": {"type": "color", "group": "footer", "default": "#3D3D3D", "translations": {"en": {"name": "Secondary Text"}, "bg": {"name": "Второстепенен текст"}}}, "color-footer-background": {"type": "color", "group": "footer", "default": "#ffffff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-footer-social-icons-text": {"separator": "true", "type": "color", "group": "footer", "default": "#666666", "translations": {"en": {"name": "Social Icons"}, "bg": {"name": "Социални икони"}}}, "color-footer-social-icons-background": {"type": "color", "group": "footer", "default": "#fff", "translations": {"en": {"name": "Social Icons Background"}, "bg": {"name": "Социални икони - фон"}}}, "color-footer-social-icons-border": {"type": "color", "group": "footer", "default": "#e0e0e0", "translations": {"en": {"name": "Color social icons border"}, "bg": {"name": "Социалните икони - рамка"}}}, "color-footer-providers-background": {"separator": "true", "type": "color", "group": "footer", "default": "#fafafa", "translations": {"en": {"name": "Providers Background"}, "bg": {"name": "Провайдери - фон"}}}, "color-footer-copyright-text": {"separator": "true", "type": "color", "group": "footer", "default": "#666", "translations": {"en": {"name": "Copyright Text"}, "bg": {"name": "Авторско право - текст"}}}, "color-footer-copyright-separator": {"type": "color", "group": "footer", "default": "#f3f3f3", "translations": {"en": {"name": "Copyright Text Separator"}, "bg": {"name": "Авторско право - сепаратор"}}}, "color-slider-text": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-slider-arrows": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Arrows"}, "bg": {"name": "Стрелки"}}}, "color-slider-dots-active": {"type": "color", "group": "slider", "default": "#fff", "translations": {"en": {"name": "Dots Active"}, "bg": {"name": "Точки - акцент"}}}, "color-product-listing-background": {"type": "color", "group": "products-listing", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-product-listing-borders": {"type": "color", "group": "products-listing", "default": "#f3f3f3", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамки и линии"}}}, "color-product-listing-text": {"type": "color", "group": "products-listing", "default": "#3D3D3D", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-product-listing-meta-text": {"type": "color", "group": "products-listing", "default": "#999", "translations": {"en": {"name": "Meta text"}, "bg": {"name": "Помощен текст"}}}, "color-product-listing-highlight": {"type": "color", "group": "products-listing", "default": "#0397aa", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-product-listing-price": {"type": "color", "group": "products-listing", "default": "#3D3D3D", "translations": {"en": {"name": "Price"}, "bg": {"name": "Цена"}}}, "color-product-listing-actions-background": {"type": "color", "group": "products-listing", "default": "#ffffff", "translations": {"en": {"name": "Actions - background"}, "bg": {"name": "Действия - фон"}}}, "color-product-listing-actions-text": {"type": "color", "group": "products-listing", "default": "#3D3D3D", "translations": {"en": {"name": "Actions - text"}, "bg": {"name": "Действия - текст"}}}, "color-product-listing-actions-highlight": {"type": "color", "group": "products-listing", "default": "#f26d7d", "translations": {"en": {"name": "Actions - highlight"}, "bg": {"name": "Действия - акцент"}}}, "color-breadcrumb-text": {"type": "color", "group": "breadcrumb", "default": "#999", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-breadcrumb-active-text": {"type": "color", "group": "breadcrumb", "default": "#666", "translations": {"en": {"name": "Active Text"}, "bg": {"name": "Активен текст"}}}, "color-pagination-text": {"type": "color", "group": "pagination", "default": "#3D3D3D", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-pagination-background": {"type": "color", "group": "pagination", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-pagination-border": {"type": "color", "group": "pagination", "default": "transparent", "translations": {"en": {"name": "Border"}, "bg": {"name": "Рамка"}}}, "color-pagination-disabled-text": {"type": "color", "group": "pagination", "default": "#ccc", "translations": {"en": {"name": "Disabled Text"}, "bg": {"name": "Неактивен текст"}}}, "color-pagination-disabled-bakground": {"type": "color", "group": "pagination", "default": "#f2f2f2", "translations": {"en": {"name": "Disabled Background"}, "bg": {"name": "Неактивен фон"}}}, "color-pagination-active-text": {"type": "color", "group": "pagination", "default": "#0397aa", "translations": {"en": {"name": "Active Text"}, "bg": {"name": "Активен текст"}}}, "color-pagination-active-background": {"type": "color", "group": "pagination", "default": "#fff", "translations": {"en": {"name": "Active Text Background"}, "bg": {"name": "Активен текст - фон"}}}, "color-button-text": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Primary - Text"}, "bg": {"name": "Основен - текст"}}}, "color-button-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Primary - Text Hover"}, "bg": {"name": "Основен - текст - акцент"}}}, "color-button-border": {"type": "color", "group": "buttons", "default": "#3D3D3D", "translations": {"en": {"name": "Primary - Border"}, "bg": {"name": "Основен - рамка"}}}, "color-button-border-hover": {"type": "color", "group": "buttons", "default": "#000", "translations": {"en": {"name": "Primary - <PERSON> Hover"}, "bg": {"name": "Основен - рамка - акцент"}}}, "color-button-background": {"type": "color", "group": "buttons", "default": "#3D3D3D", "translations": {"en": {"name": "Primary - Background"}, "bg": {"name": "Основен - фон"}}}, "color-button-background-hover": {"type": "color", "group": "buttons", "default": "#000", "translations": {"en": {"name": "Primary - <PERSON>ver"}, "bg": {"name": "Основен - фон - акцент"}}}, "color-button-active-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Active - Text"}, "bg": {"name": "Активен - текст"}}}, "color-button-active-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Active - Text Hover"}, "bg": {"name": "Активен - текст - акцент"}}}, "color-button-active-border": {"type": "color", "group": "buttons", "default": "#0397aa", "translations": {"en": {"name": "Active - Border"}, "bg": {"name": "Активен - рамка"}}}, "color-button-active-border-hover": {"type": "color", "group": "buttons", "default": "#0098aa", "translations": {"en": {"name": "Active - Border Hover"}, "bg": {"name": "Активен - рамка - акцент"}}}, "color-button-active-background": {"type": "color", "group": "buttons", "default": "#0397aa", "translations": {"en": {"name": "Active - Background"}, "bg": {"name": "Активен - фон"}}}, "color-button-active-background-hover": {"type": "color", "group": "buttons", "default": "#008898", "translations": {"en": {"name": "Active - Background Hover"}, "bg": {"name": "Активен - фон - акцент"}}}, "color-button-secondary-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#000", "translations": {"en": {"name": "Secondary - Text"}, "bg": {"name": "Второстепенен - текст"}}}, "color-button-secondary-text-hover": {"type": "color", "group": "buttons", "default": "#0397aa", "translations": {"en": {"name": "Secondary - Text Hover"}, "bg": {"name": "Второстепенен - текст - акцент"}}}, "color-button-tertiary-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Tertiary - Text"}, "bg": {"name": "Третостепенен - текст"}}}, "color-button-tertiary-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Tertiary - Text Hover"}, "bg": {"name": "Третостепенен - текст - акцент"}}}, "color-button-tertiary-border": {"type": "color", "group": "buttons", "default": "#7cc814", "translations": {"en": {"name": "Tertiary - Border"}, "bg": {"name": "Третостепенен - рамка"}}}, "color-button-tertiary-border-hover": {"type": "color", "group": "buttons", "default": "#66ae04", "translations": {"en": {"name": "Tertiary - Border Hover"}, "bg": {"name": "Третостепенен - рамка - акцент"}}}, "color-button-tertiary-background": {"type": "color", "group": "buttons", "default": "#7cc814", "translations": {"en": {"name": "Tertiary - Background"}, "bg": {"name": "Третостепенен - фон"}}}, "color-button-tertiary-background-hover": {"type": "color", "group": "buttons", "default": "#66ae04", "translations": {"en": {"name": "Tertiary - <PERSON> Hover"}, "bg": {"name": "Третостепенен - фон - акцент"}}}, "color-button-quaternary-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Quaternary - Text"}, "bg": {"name": "Четиристепенен - текст"}}}, "color-button-quaternary-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Quaternary - Text Hover"}, "bg": {"name": "Четиристепенен - текст - акцент"}}}, "color-button-quaternary-border": {"type": "color", "group": "buttons", "default": "#00aeef", "translations": {"en": {"name": "Quaternary - Border"}, "bg": {"name": "Четиристепенен - рамка"}}}, "color-button-quaternary-border-hover": {"type": "color", "group": "buttons", "default": "#00a1de", "translations": {"en": {"name": "Quaternary - Border Hover"}, "bg": {"name": "Четиристепенен - рамка - акцент"}}}, "color-button-quaternary-background": {"type": "color", "group": "buttons", "default": "#00aeef", "translations": {"en": {"name": "Quaternary - Background"}, "bg": {"name": "Четиристепенен - фон"}}}, "color-button-quaternary-background-hover": {"type": "color", "group": "buttons", "default": "#00a1de", "translations": {"en": {"name": "Quaternary - <PERSON>"}, "bg": {"name": "Четиристепенен - фон - акцент"}}}, "color-button-quinary-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Quinary - Text"}, "bg": {"name": "Петостепенен - текст"}}}, "color-button-quinary-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Quinary - Text Hover"}, "bg": {"name": "Петостепенен - текст - акцент"}}}, "color-button-quinary-border": {"type": "color", "group": "buttons", "default": "#e0e0e0", "translations": {"en": {"name": "Quinary - Border"}, "bg": {"name": "Петостепенен - рамка"}}}, "color-button-quinary-border-hover": {"type": "color", "group": "buttons", "default": "#d5d5d5", "translations": {"en": {"name": "Quinary - Border Hover"}, "bg": {"name": "Петостепенен - рамка - акцент"}}}, "color-button-quinary-background": {"type": "color", "group": "buttons", "default": "#e0e0e0", "translations": {"en": {"name": "Quinary - Background"}, "bg": {"name": "Петостепенен - фон"}}}, "color-button-quinary-background-hover": {"type": "color", "group": "buttons", "default": "#d5d5d5", "translations": {"en": {"name": "Quinary - <PERSON>"}, "bg": {"name": "Петостепенен - фон - акцент"}}}, "color-button-senary-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#3D3D3D", "translations": {"en": {"name": "Senary - Text"}, "bg": {"name": "Шестостепенен - текст"}}}, "color-button-senary-text-hover": {"type": "color", "group": "buttons", "default": "#3D3D3D", "translations": {"en": {"name": "Senary - Text Hover"}, "bg": {"name": "Шестостепенен - текст - акцент"}}}, "color-button-senary-border": {"type": "color", "group": "buttons", "default": "#e0e0e0", "translations": {"en": {"name": "Senary - Border"}, "bg": {"name": "Шестостепенен - рамка"}}}, "color-button-senary-border-hover": {"type": "color", "group": "buttons", "default": "#d5d5d5", "translations": {"en": {"name": "Senary - Border Hover"}, "bg": {"name": "Шестостепенен - рамка - акцент"}}}, "color-button-senary-background": {"type": "color", "group": "buttons", "default": "#e0e0e0", "translations": {"en": {"name": "Senary - Background"}, "bg": {"name": "Шестостепенен - фон"}}}, "color-button-senary-background-hover": {"type": "color", "group": "buttons", "default": "#d5d5d5", "translations": {"en": {"name": "Senary - <PERSON>ver"}, "bg": {"name": "Шестостепенен - фон - акцент"}}}, "color-label-product-a_": {"type": "color", "group": "labels", "default": "#149C39", "translations": {"en": {"name": "A+"}, "bg": {"name": "A+"}}}, "color-label-product-a__": {"type": "color", "group": "labels", "default": "#52AD29", "translations": {"en": {"name": "A++"}, "bg": {"name": "A++"}}}, "color-label-product-a___": {"type": "color", "group": "labels", "default": "#CBD005", "translations": {"en": {"name": "A+++"}, "bg": {"name": "A+++"}}}, "color-label-product-a": {"type": "color", "group": "labels", "default": "#FBEF10", "translations": {"en": {"name": "A"}, "bg": {"name": "A"}}}, "color-label-product-b": {"type": "color", "group": "labels", "default": "#F7BD08", "translations": {"en": {"name": "B"}, "bg": {"name": "B"}}}, "color-label-product-c": {"type": "color", "group": "labels", "default": "#EB6708", "translations": {"en": {"name": "C"}, "bg": {"name": "C"}}}, "color-label-product-d": {"type": "color", "group": "labels", "default": "#DE0418", "translations": {"en": {"name": "D"}, "bg": {"name": "D"}}}, "color-label-product-e": {"type": "color", "group": "labels", "default": "#DE0418", "translations": {"en": {"name": "E"}, "bg": {"name": "E"}}}, "color-label-product-f": {"type": "color", "group": "labels", "default": "#DE0418", "translations": {"en": {"name": "F"}, "bg": {"name": "F"}}}, "color-label-product-g": {"type": "color", "group": "labels", "default": "#E70021", "translations": {"en": {"name": "G"}, "bg": {"name": "G"}}}, "color-label-new-background": {"title": {"en": "Products", "bg": "Продукти"}, "type": "color", "group": "labels", "default": "#ffb400", "translations": {"en": {"name": "NEW - Background"}, "bg": {"name": "НОВ - Фон"}}}, "color-label-new-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "NEW - Text"}, "bg": {"name": "НОВ - Текст"}}}, "color-label-sale-background": {"separator": "true", "type": "color", "group": "labels", "default": "#ffb400", "translations": {"en": {"name": "SALE - Background"}, "bg": {"name": "SALE - Фон"}}}, "color-label-sale-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "SALE - Text"}, "bg": {"name": "SALE - Текст"}}}, "color-label-discount-background": {"separator": "true", "type": "color", "group": "labels", "default": "#c8ebf0", "translations": {"en": {"name": "Discount - Background"}, "bg": {"name": "Отстъпка - Фон"}}}, "color-label-discount-text": {"type": "color", "group": "labels", "default": "#FF8F27", "translations": {"en": {"name": "Discount - Text"}, "bg": {"name": "Отстъпка - Текст"}}}, "color-label-discount-text-percent": {"type": "color", "group": "labels", "default": "#FF8F27", "translations": {"en": {"name": "Discount - Text percent"}, "bg": {"name": "Отстъпка - Текст процент"}}}, "color-label-free-delivery-background": {"separator": "true", "type": "color", "group": "labels", "default": "#0397aa", "translations": {"en": {"name": "Free delivery - Background"}, "bg": {"name": "Безплатна доставка - Фон"}}}, "color-label-free-delivery-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Free delivery - Text"}, "bg": {"name": "Безплатна доставка - Текст"}}}, "color-label-featured-background": {"separator": "true", "type": "color", "group": "labels", "default": "#0397aa", "translations": {"en": {"name": "Featured - Background"}, "bg": {"name": "Препоръчан - Фон"}}}, "color-label-featured-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Featured - Text"}, "bg": {"name": "Препоръчан - Текст"}}}, "color-label-custom-background": {"separator": "true", "type": "color", "group": "labels", "default": "#ffd9e4", "translations": {"en": {"name": "Custom label - Background"}, "bg": {"name": "Персонализи<PERSON><PERSON>н - Фон"}}}, "color-label-custom-text": {"type": "color", "group": "labels", "default": "#f64d7d", "translations": {"en": {"name": "Custom label - Text"}, "bg": {"name": "Персонал<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - Текст"}}}, "color-label-date-background": {"title": {"en": "Blog", "bg": "Блог"}, "separator": "true", "type": "color", "group": "labels", "default": "#0397aa", "translations": {"en": {"name": "Date - Background"}, "bg": {"name": "Дата - Фон"}}}, "color-label-date-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Date - Text"}, "bg": {"name": "Дата - Текст"}}}}}}