<style>
    .tabbed-content .item-nav {
        position: relative;
    }

    .tabbed-content .item-nav.active .item-nav-content {
        -webkit-transition: opacity 0.3s ease-in-out;
        -moz-transition: opacity 0.3s ease-in-out;
        -o-transition: opacity 0.3s ease-in-out;
        -ms-transition: opacity 0.3s ease-in-out;
        transition: opacity 0.3s ease-in-out;
    }

    .tabbed-content .tabs-nav {
        display: none;
    }

    .tabbed-content .item-nav-trigger {
        display: block;
        font-size: 14px;
        padding-left: 0;
        padding-right: 15px;
        height: 48px;
        border-bottom: 1px solid #D6D6D6;
        font-weight: 600;
        line-height: 40px;
        cursor: pointer;
        width: 100%;
        text-align: left;
    }

    .tabbed-content .item-nav.active .item-nav-trigger {
        position: relative;
    }

    .tabbed-content .item-nav.active .item-nav-trigger:before {
        content: '';
        background: #0B98A9;
        position: absolute;
        left: 0;
        top: auto;
        bottom: -2px;
        width: 42px;
        height: 3px;
    }

    .tabbed-content .item-nav .item-nav-trigger::after {
        font-family: "Font Awesome 5 Pro";
        content: "\f078";
        position: absolute;
        -ms-transform: translateX(-50%);
        transform: translateX(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
        top: 20px;
        right: 0;
        -ms-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        font-size: 16px;
        font-weight: 400;
        color: #3D3D3D;
    }

    .tabbed-content .item-nav.active .item-nav-trigger::after {
        content: "\f077";
    }

    /*
    .tabbed-content .item-nav::before {
        content: attr(data-title);
    }
     */
    .tabbed-content .item-nav .item-nav-content {
        opacity: 0;
        visibility: hidden;
        height: 0;
    }

    .tabbed-content .item-nav.active .item-nav-content {
        opacity: 1;
        visibility: visible;
        height: auto;
    }

    .item-nav-content {
        max-height: 400px;
        overflow-y: auto;
    }

    @media all and (min-width: 991px) {
        .tabbed-content .tabs-nav {
            display: block;
        }

        .tabbed-content .tabs-nav li {
            display: inline-block;
        }

        .tabbed-content .tabs-nav li a {
            display: block;
        }

        .tabbed-content .item-nav {
            min-height: 0;
            display: none;
        }

        .tabbed-content .item-nav.active {
            display: block;
        }

        .tabbed-content .item-nav::before {
            display: none;
        }

        .tabbed-content .item-nav-trigger {
            display: none;
        }
    }

    @media all and (max-width: 990px) {
        .tabbed-content .item-nav {
            margin-bottom: 5px;
        }

        .tabbed-content .item-nav.active .item-nav-content {
            padding: 15px 0;
        }

        ._product-details-properties-wrapper {
            display: block !important;
            width: 100% !important;
        }

        ._product-details-tabs ._product-details-characteristics ._product-details-properties ul {
            width: 100%;
            display: block;
            padding: 0 !important;
            margin-top: 0;
        }
    }

    @media all and (max-width: 767px) {
        .item-nav-content {
            max-height: 240px;
            overflow: hidden;
            position: relative;
        }

        .item-nav-content:before {
            content: '';
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 92.71%);
            display: block;
            position: absolute;
            bottom: 0;
            left: 0;
            height: 158px;
            width: 100%;
            z-index: 2;
        }

        .item-nav-content:after {
            content: '\f078';
            color: #0B98A9;
            font-family: 'Font Awesome 5 Pro';
            font-weight: 400;
            font-size: 24px;
            line-height: 28px;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateY(-50%);
            width: 21px;
            height: 28px;
            z-index: 3;
        }

        .item-nav-content.open {
            max-height: initial;
            overflow: visible;
        }

        .item-nav-content.open:before,
        .item-nav-content.open:after {
            content: none;
        }
    }
</style>
{capture append="js"}
    <script>
        $(function () {
            var tabs = $('.tabbed-content').find('.tabs-nav');
            if (!tabs.is(':visible')) {
                // $('.item-nav').removeClass('active');
            }
            $('.item-nav').each(function () {
                $('<div class="item-nav-trigger">' + $(this).data('title') + '</div>').prependTo($(this));
            })
            tabControl();
        });

        $(window).on('resize', function (e) {
            tabControl();
        });

        function tabControl() {
            var tabs = $('.tabbed-content').find('.tabs-nav');
            if (tabs.is(':visible')) {
                tabs.find('a').off('click').on('click', function (event) {
                    event.preventDefault();
                    var target = $(this).attr('href'),
                        tabs = $(this).parents('.tabs-nav'),
                        item = tabs.parents('.tabbed-content').find('.item-nav');
                    tabs.find('li').removeClass('active');
                    item.removeClass('active');
                    $(this).parent().addClass('active');
                    $(target).addClass('active');
                });
            } else {
                $(document).off('click', '.item-nav-trigger').on('click', '.item-nav-trigger', function () {
                    var $this = $(this)
                    container = $this.closest('.tabbed-content'),
                        currId = $this.closest('.item-nav').attr('id'),
                        items = container.find('.item-nav');
                    $this.closest('.item-nav').siblings().removeClass('active');
                    $this.closest('.item-nav').toggleClass('active');
                    container.find('.tabs-nav a').parent().removeClass('active');
                    container.find('.tabs-nav a[href$="#' + currId + '"]').parent().addClass('active');
                });
            }
        }

        $(document).on('click', '.item-nav-content', function () {
            $(this).addClass('open');
        });
    </script>
{/capture}

<div class="tabbed-content _product-details-tabs">
    <nav class="tabs-nav">
        <ul class="_product-details-tabs-menu">
            {if !empty($product->description_replace)}
                <li class="active">
                    <a href="#product-tab-description_replace" class="_button">
                        <span class="_figure-stack">
                            <span class="_figure-stack-label">{t}sf.global.replace_tab{/t}</span>
                        </span>
                    </a>
                </li>
            {/if}
            {if W::ProductsDetails('show_product_description') && $product->description}
                <li {if empty($product->description_replace)}class="active"{/if}>
                    <a href="#product-description" class="_button">
                    <span class="_figure-stack">
                        <span class="_figure-stack-label">{$product->description_title|default:"{t}sf.global.label.description{/t}"}</span>
                    </span>
                    </a>
                </li>
            {/if}
            {if W::ProductsDetails('show_categories_characteristics') && !W::categoryProperties()->getPropertiesForProduct($product)->isEmpty()}
                <li {if W::ProductsDetails('show_product_description') && empty($product->description) && empty($product->description_replace)}class="active"{/if}>
                    <a href="#product-characteristics" class="_button">
                    <span class="_figure-stack">
                        <span class="_figure-stack-label">{t}sf.product.details.characteristics{/t}</span>
                    </span>
                    </a>
                </li>
            {/if}
            {if $widget->productQuantity->hasQuantity($product->id)}
                <li>
                    <a href="#product-availability" class="_button">
                        <span class="_figure-stack">
                            <span class="_figure-stack-label">{t}sf.global.availability{/t}</span>
                        </span>
                    </a>
                </li>
            {/if}
            {$tabFiles = $product->getPublicFilesByPosition(['tab', null])}
            {if $tabFiles->isNotEmpty()}
                <li>
                    <a href="#product-manual" class="_button">
                        <span class="_figure-stack">
                            <span class="_figure-stack-label">{t}sf.global.manual{/t}</span>
                        </span>
                    </a>
                </li>
            {/if}
            {foreach from=$product->tabs item=tab}
                {$i = 0}
                <li>
                    <a href="#product-tab-{$tab->id}-{$i}" class="_button">
                        <span class="_figure-stack">
                            <span class="_figure-stack-label">{$tab->name}</span>
                        </span>
                    </a>
                </li>
            {/foreach}
            {if Apps::enabled('facebook_comments') || Apps::enabled('disqus_comments') || Apps::enabled('yotpo')}
                <li>
                    <a href="#product-comments" class="_button">
                        <span class="_figure-stack">
                            <span class="_figure-stack-label">{t}sf.global.comments{/t}</span>
                        </span>
                    </a>
                </li>
            {/if}
        </ul>
    </nav>
    {if !empty($product->description_replace)}
        <section id="product-tab-description_replace" class="item-nav" data-title="{t}sf.global.replace_tab{/t}">
            <div class="item-nav-content _product-details-tabs-item">
                <div class="_product-details-manual">
                    <div>
                        {$product->description_replace nofilter}
                    </div>
                </div>
            </div>
        </section>
    {/if}
    {if W::ProductsDetails('show_product_description') && $product->description}
        <section id="product-description"
                 class="item-nav {if empty($product->description_replace)}active{/if}"
                 data-title="{$product->description_title|default:"{t}sf.global.label.description{/t}"}">
            <div class="item-nav-content _product-details-tabs-item">
                <div class="_product-details-description">
                    <div class="_textbox" data-article-content data-language="{locale()}">
                        {$product->description nofilter}
                    </div>
                </div>
            </div>
        </section>
    {/if}
    {if W::ProductsDetails('show_categories_characteristics') && !W::categoryProperties()->getPropertiesForProduct($product)->isEmpty()}
        <section id="product-characteristics" class="item-nav {if W::ProductsDetails('show_product_description') && empty($product->description) && empty($product->description_replace)}active{/if}"
                 data-title="{t}sf.product.details.characteristics{/t}">
            <div class="item-nav-content _product-details-tabs-item">
                <div class="_product-details-characteristics">
                    <div class="_textbox">
                        <div class="_product-details-properties-wrapper hidden-sm hidden-xs">
                            {include file="./../../widgets/product/details/category_properties.tpl" properties=W::categoryProperties()->getPropertiesForProduct($product)}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    {/if}
    {if $widget->productQuantity->hasQuantity($product->id)}
        <section id="product-availability" class="item-nav" data-title="{t}sf.global.availability{/t}">
            <div class="item-nav-content _product-details-tabs-item">
                <div class="_product-details-availability">
                    <div class="_textbox hidden visible-md visible-lg">
                        {include file="widgets/product/quantity.tpl" quantity=$widget->productQuantity}
                    </div>

                    {$availability = $widget->productQuantity->getQuantity($product->id)}
                    <div class="hidden-lg hidden-md">
                        <div class="_availability-mobile-wrap">
                            {foreach from=$availability item=qty}
                                <a class="_availability-mobile-link" href="{$qty->url}">
                                    <i class="fa-thin fa-location-dot fa-lg _mr-5"></i>
                                    <span>{$qty->title}</span> -
                                    <span>{$qty->city}</span>
                                </a>
                            {/foreach}
                        </div>
                    </div>
                    <style>
                        ._availability-mobile-wrap {
                            display: flex;
                            flex-direction: column;
                        }
                        ._availability-mobile-link {
                            border-bottom: 1px solid #D6D6D6;
                            position: relative;
                            padding: 15px 25px;
                        }
                        ._availability-mobile-link i {
                            position: absolute;
                            left: 0;
                            top: 50%;
                            transform: translateY(-50%);
                        }
                    </style>
                </div>
            </div>
        </section>
    {/if}
    {if $tabFiles->isNotEmpty()}
        <section id="product-manual" class="item-nav" data-title="{t}sf.global.manual{/t}">
            <div class="item-nav-content _product-details-tabs-item">
                <div class="_product-details-manual">
                    <div class="_textbox">
                        <ul>
                            {foreach $tabFiles as $group}
                                {if $tabFiles->count() > 1}
                                    <li>
                                        <strong>{$group->first()->group_name}</strong>
                                    </li>
                                {/if}
                                {foreach from=$group item=file}
                                    <li>
                                        <a target="_blank"
                                           href="{route('site.download.public', [$product->url_handle, $file->mask])}">{$file->name}</a>
                                    </li>
                                {/foreach}
                            {/foreach}
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    {/if}
    {foreach from=$product->tabs item=tab}
        {$i = 0}
        <section id="product-tab-{$tab->id}-{$i}" class="item-nav" data-title="{$tab->name}">
            <div class="item-nav-content _product-details-tabs-item">
                <div class="_product-details-manual">
                    <div{if $tab->id != 0} class="_textbox"{/if}>
                        {$tab->description nofilter}
                    </div>
                </div>
            </div>
        </section>
    {/foreach}
    {if Apps::enabled('facebook_comments') || Apps::enabled('disqus_comments') || Apps::enabled('yotpo')}
        <section id="product-comments" class="item-nav" data-title="{t}sf.global.comments{/t}">
            <div class="item-nav-content _product-details-tabs-item">
                <div class="_product-details-comments">
                    {if Apps::enabled('yotpo')}
                        {\App\Models\System\AppsManager::getManager('yotpo')->render($product) nofilter}
                    {/if}
                    {if Apps::enabled('facebook_comments')}
                        {include file="widgets/product/details/comments-facebook.tpl"}
                    {/if}
                    {if Apps::enabled('disqus_comments')}
                        {include file="widgets/product/details/comments-disqus.tpl"}
                    {/if}
                </div>
            </div>
        </section>
    {/if}
</div>