/*=============================================================================*\
    PRODUCT
\*=============================================================================*/
/*  LIST
-------------------------------------------------------------------------------*/

._products-list-info {
    margin-top: @separator;
}

._products-list-info-item {
    overflow: hidden;
    color: @color-main-meta-text; /* theme */
    margin-bottom: @separator-small;

    &:last-child {
        margin-bottom: 0;
    }
}

._products-list-info-item-body {
    display: table;
    width: 100%;
}

._products-list-info-item-image {
    display: table-cell;
    vertical-align: top;
    width: 1%;
    padding: 5px 0;
    padding-right: 30px;

    img {
        max-width: 300px;
    }
}

._products-list-info-item-description {
    display: table-cell;
    vertical-align: top;
}

/*  BOX
-------------------------------------------------------------------------------*/

._product {
    ._product-inner {
        background-color: @color-product-listing-background; /* theme */
        border: 1px solid @color-product-listing-borders; /* theme */
        color: @color-product-listing-text; /* theme */
        transition: border-color .4s, box-shadow .4s;
        padding: 0 15px;
        @media (min-width: @screen-lg-desktop) {
            border: 1px solid transparent;
            &:hover {
                border: 1px solid @color-product-listing-borders; /* theme */
                //box-shadow: 4px 4px 0 0 @color-main-box-shadow;
                ._product-image {
                    ._product-image-thumb {
                        ._product-image-thumb-holder {
                            img {
                                //opacity: .6 !important;
                            }
                        }
                    }

                    ._product-quick-view {
                        display: block;
                    }
                }
                ._product-meta {
                    opacity: 1;
                    visibility: visible;
                }
            }
        }
        ._product-image {
            position: relative;
            ._product-image-thumb {
                display: block;
                ._product-image-thumb-holder {
                    display: block;
                    //height: 231px;
                    height: auto;
                    position: relative;
                    padding-bottom: 100%;
                    img {
                        bottom: 0;
                        height: auto;
                        left: 0;
                        margin: auto;
                        max-height: 100%;
                        max-width: 100%;
                        position: absolute;
                        right: 0;
                        top: 0;
                        width: auto;
                    }
                }
            }
            ._product-quick-view {
                position: absolute;
                top: 10px;
                left: 10px;
                display: none;
                height: 36px;
                background-color: @color-product-listing-actions-background; /* theme */
                border: 1px solid @color-product-listing-borders; /* theme */
                padding-left: 10px;
                padding-right: 10px;
                @media (min-width: @screen-lg-desktop) {
                    &:hover {
                        background-color: @color-button-active-background; /* theme */
                        border-color: @color-button-active-background; /* theme */
                        > span {
                            color: @color-button-active-text; /* theme */
                        }
                    }
                }
                > span {
                    color: @color-product-listing-actions-text; /* theme */
                    font-size: @font-size-xsmall;
                    text-transform: uppercase;
                    font-weight: 600;
                    i {
                        font-size: @font-size-medium;
                    }
                }
            }
            ._product-ribbon-holder {
                position: relative;
                margin-top: 18px;
                height: 25px;
                z-index: 1;
            }
            ._product-ribbon {
                display: block;
                background-color: @color-button-background; /* theme */
                border-radius: 18px;
                color: @color-button-text; /* theme */
                font-size: @font-size-xsmall;
                font-weight: 600;
                text-transform: uppercase;
                text-align: right;
                padding: 3px 10px;
                position: relative;
                display: table;
                margin-right: 0px;
                margin-left: auto;
                height: 25px;
                line-height: 1.7;
                //&:before {
                //    position: absolute;
                //    left: -7px;
                //    top: 0;
                //    .triangle(left, 24px, 7px, @color-button-background);
                //    border-top: 24px solid transparent;
                //    border-bottom: 0 solid transparent;
                //}
                &._product-ribbon-bundle {
                    background-color: #ffc600; /* theme */
                    margin-bottom: 2px;
                    &:before {
                        border-right-color: #ffc600; /* theme */
                    }
                }
                &._product-ribbon-new {
                    background-color: @color-label-new-background;
                    color: @color-label-new-text;
                    margin-bottom: 2px;
                    &:before {
                        border-right-color: @color-label-new-background; /* theme */
                    }
                }
                &._product-ribbon-featured {
                    background-color: @color-label-featured-background;
                    color: @color-label-featured-text;
                    &:before {
                        border-right-color: @color-label-featured-background; /* theme */
                    }
                }
                &._product-ribbon-sale {
                    display: none;
                }
                &._product-ribbon-leasing {
                    display: none;
                }
                //& + ._product-ribbon {
                //    margin-top: 5px;
                //}
                & when (@rtl) {
                    white-space: nowrap;
                }
            }
            ._product-ribbon-banner {
                max-width: 25%;
                position: absolute;
                z-index: 1;

                img {
                    max-width: 100%;
                    max-height: 100px;
                }
            }
            ._product-energy-labels {
                display: none;
                position: absolute;
                bottom: 15px;
                left: 0;
                ._product-energy-label {
                    display: inline-block;
                    position: relative;
                    min-width: 54px;
                    font-size: @font-size-xlarge;
                    line-height: 1;
                    text-transform: uppercase;
                    color: @color-main-secondary-text; /* theme */
                    padding-top: 3px;
                    padding-bottom: 3px;
                    padding-left: 10px;
                    padding-right: 5px;
                    font-weight: 600;
                    &:after {
                        position: absolute;
                        right: -7px;
                        top: 0;
                        .triangle(right, 24px, 7px, @color-main-text);
                    }
                    &._label-product-a_ {
                        background-color: @color-label-product-a_; /* theme */
                        &:after {
                            border-left-color: @color-label-product-a_; /* theme */
                        }
                    }
                    &._label-product-a__ {
                        background-color: @color-label-product-a__; /* theme */
                        &:after {
                            border-left-color: @color-label-product-a__; /* theme */
                        }
                    }
                    &._label-product-a___ {
                        background-color: @color-label-product-a___; /* theme */
                        &:after {
                            border-left-color: @color-label-product-a___; /* theme */
                        }
                    }
                    &._label-product-a {
                        background-color: @color-label-product-a; /* theme */
                        &:after {
                            border-left-color: @color-label-product-a; /* theme */
                        }
                    }
                    &._label-product-b {
                        background-color: @color-label-product-b; /* theme */
                        &:after {
                            border-left-color: @color-label-product-b; /* theme */
                        }
                    }
                    &._label-product-c {
                        background-color: @color-label-product-c; /* theme */
                        &:after {
                            border-left-color: @color-label-product-c; /* theme */
                        }
                    }
                    &._label-product-d {
                        background-color: @color-label-product-d; /* theme */
                        &:after {
                            border-left-color: @color-label-product-d; /* theme */
                        }
                    }
                    &._label-product-e {
                        background-color: @color-label-product-e; /* theme */
                        &:after {
                            border-left-color: @color-label-product-e; /* theme */
                        }
                    }
                    &._label-product-f {
                        background-color: @color-label-product-f; /* theme */
                        &:after {
                            border-left-color: @color-label-product-f; /* theme */
                        }
                    }
                    &._label-product-g {
                        background-color: @color-label-product-g; /* theme */
                        &:after {
                            border-left-color: @color-label-product-g; /* theme */
                        }
                    }
                }
            }
            ._remove {
                position: absolute;
                right: 6px;
                top: 5px;
                &:before {
                    font-size: @font-size-medium;
                    color: @color-product-listing-meta-text; /* theme */
                }
            }
        }
        ._product-info {
            padding: 0 0 10px;
            text-align: left;
            ._product-name {
                margin-top: 10px;
                margin-bottom: 10px;
                h3 {
                    height: 32px;
                    font-size: @font-size-medium;
                    text-transform: none;
                    font-weight: 600;
                    letter-spacing: normal;
                    white-space: normal;
                    overflow: hidden;
                    line-height: 16px;

                    a {
                        color: @color-product-listing-text; /* theme */
                        font-weight: 400;

                        @media (min-width: 1025px) {
                            &:hover {
                                color: @color-product-listing-highlight; /* theme */
                            }
                        }
                    }
                }
                & + ._product-description {
                    margin-top: 10px;
                }
            }
            ._product-description,
            ._product-details-properties {
                display: none;
            }
        }
        ._product-countdown {
            margin-top: 10px;
            text-align: center;

            ._countdown-label {
                display: none;
            }

            ._countdown-timer-digit {
                font-size: 21px;
            }
        }
        ._product-options {
            min-height: 57px;
            .clearfix();
            ._product-price {
                //font-size: @font-size-xlarge;
                font-size: @font-size-large;
                color: @color-product-listing-price; /* theme */
                //font-weight: 600;
                font-weight: 800;
                direction: ltr;
                margin-top: 7px;
                position: relative;
                display: flex;
                align-items: center;
                flex-flow: wrap;
                gap: 0 10px;

                & when (@rtl) {
                    text-align: left;
                }
            }
            ._product-discount,
            ._product-energy-label {
                //position: absolute;
                display: inline-block;
                font-weight: bold;
                position: static;
                right: -11px;
                top: 0;
                padding: 4px 6px 3px;
                background-color: @color-label-discount-background; /* theme */
                color: @color-label-discount-text; /* theme */
                text-align: center;
                font-size: @font-size-xxsmall;
                line-height: 1;
                margin-right: 10px;
                + ._product-energy-label {
                   top: 40px;
                }
            }
            ._product-price-discounted {
                font-size: 14px;
                font-weight: 400;

                //min-height: 17px;
                del {
                    display: inline-block;
                    color: @color-second-secondary-text; /* theme */
                    text-decoration-color: @color-second-secondary-text; /* theme */
                }

                ._product-discount {
                    color: @color-label-discount-text-percent; /* theme */
                    font-weight: 700;
                    font-size: 14px;
                    margin-right: 0;
                    margin-left: 9px;
                    background-color: transparent;
                }
            }
            ._product-price-saved {
                color: @color-product-listing-price; /* theme */
            }
        }
        ._product-meta {
            border-top: 1px dotted @color-product-details-borders;
            background-color: @color-product-listing-actions-background; /* theme */
            font-size: @font-size-small;
            //padding: 10px;
            padding: 10px 0;
            color: @color-product-listing-actions-text; /* theme */
            display: table;
            position: relative;
            left: -15px;
            width: ~'calc(100% + 30px)';
            @media (min-width: @screen-lg-desktop) {
                opacity: 1;
                visibility: visible;
                transition: opacity .4s, visibility .4s;
            }
            ._checkbox {
                padding-left: 26px;
            }
            ._product-meta-favorites {
                display: table-cell;
                vertical-align: top;
                text-align: center;
                position: relative;
                a {
                    display: block;
                    font-size: 0;
                    ._figure-stack {
                        font-size: @font-size-small;
                    }
                    &.active {
                        color: @color-product-listing-actions-highlight; /* theme */
                    }
                }

                &:before {
                    content: '';
                    background-color: @color-product-details-borders;
                    position: absolute;
                    top: -10px;
                    left: 0;
                    width: 1px;
                    height: ~'calc(100% + 20px)';
                }
            }
            ._product-meta-compare {
                display: table-cell;
                vertical-align: top;
                text-align: center;
                ._product-meta-compare-wrapper {
                    font-size: 0;
                    //._checkbox {
                    //    font-size: @font-size-small;
                    //    line-height: normal;
                    //    font-weight: 500;
                    //}

                    ._checkbox {
                        font-size: @font-size-small;
                        padding-left: 20px;
                        min-height: 21px;
                        .checker {
                            margin-right: 0;
                            span {
                                background-color: transparent;
                                border: none !important;
                                position: relative;
                                display: block;
                                input {
                                    display: none;
                                }
                                &:before {
                                    font-family: 'Font Awesome 5 Pro';
                                    color: @color-main-tertiary-text;
                                    content:"\f0ec";
                                    font-size: @font-size-base;
                                    font-weight: 300;
                                }
                                &.checked {
                                    &:before {
                                        color: @color-main-highlight; /* theme */
                                    }
                                }
                            }
                        }
                        &.active {
                            .checker {
                                top: 4px;
                            }
                        }
                    }
                }
            }
            ._rating {
                display: none;
            }
        }
        ._product-rating {
            padding: 10px;
            ._rating {
                ._rating-score {
                    color: @color-product-listing-meta-text; /* theme */
                }
            }
        }
        ._product-labels {
            text-align: center;
            position: absolute;
            top: auto;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: ~'calc(100% + 25px)';

            ._label-product {
                font-size: @font-size-xsmall;
                font-weight: 600;
                background-color: @color-label-custom-background; /* theme */
                border-radius: 28px;
                color: @color-label-custom-text; /* theme */
                padding: 5px 10px 5px;
                text-align: center;
                display: inline-block;
                margin-bottom: 1px;
                width: auto;
                line-height: 13px;
                & + ._label-product {
                    margin-left: 1px;
                }
            }
        }
        [class*='-max'] {
            display: none;
        }
        ._product-ribbon-banner {
            margin-bottom: 15px;
            width: 100%;
            text-align: center;
            span {
                background-color: @color-main-box-shadow;
                padding: 3px 5px;
                font-size: @font-size-xsmall;
                color: @color-main-highlight;
                display: inline-block;
                + span {
                    margin-left: 5px;
                }
            }
        }
        ._product-buttons {
            margin-bottom: 12px;
            ._button {
                border-radius: 18px;
                font-weight: 500;
                width: 100%;
                color: @color-button-active-text; /* theme */
                background-color: @color-button-active-background; /* theme */
                border-color: @color-button-active-border-hover; /* theme */

                &:hover {
                    color: @color-button-active-text-hover; /* theme */
                    background-color: @color-button-active-background-hover; /* theme */
                    border-color: @color-button-active-border-hover; /* theme */
                }
            }
        }
    }
    &-max {
        ._product-inner {
            //padding: 0;
            padding: 20px 0;
            border-left-color: transparent;
            border-right-color: transparent;
            &:hover {
                box-shadow: none;
            }
            .clearfix();
            ._product-image {
                float: left;
                width: 33.3%;
            }
            ._product-info {
                text-align: left;
                float: left;
                width: 33.3%;
                padding: 25px 30px;
                ._product-name {
                    h3 {
                        overflow: visible;
                        text-overflow: clip;
                        white-space: normal;
                    }
                }
                ._product-details-properties {
                    display: block;
                    margin-top: 12px;
                    ul {
                        display: block;
                        li {
                            display: block;
                            ._product-details-properties-title {
                                display: inline-block;
                                color: @color-main-tertiary-text; /* theme */
                            }

                            ._product-details-properties-value {
                                display: inline-block;
                                color: @color-main-secondary-text; /* theme */
                            }
                        }
                    }
                }
                ._product-rating-max {
                    margin-top: 12px;
                    display: block;
                    ._rating {
                        ._rating-score {
                            color: @color-main-meta-text; /* theme */
                        }
                    }
                }
                ._product-meta-max {
                    border-top: 0;
                    margin-top: 30px;
                    display: table;
                    background-color: transparent;
                    padding: 0;
                    opacity: 1;
                    visibility: visible;
                    ._product-meta-compare {
                        text-align: left;
                    }
                }
                ._product-options {
                    display: none;
                }
            }
            ._product-options-panel-max {
                display: block;
                float: left;
                width: 33.3%;
                position: relative;
                height: 192px;
                &:before {
                    position: absolute;
                    top: 30px;
                    height: 100%;
                    width: 1px;
                    background-color: @color-main-borders; /* theme */
                    content: '';
                }
                ._product-options {
                    display: block;
                    padding: 20px;
                    ._product-price {
                        ._product-discount {
                            right: -22px;
                            left: auto;
                        }
                    }
                    ._product-price-saved {
                        font-size: 12px;
                    }
                }
                ._product-buttons {
                    padding: 0 0 0 20px;
                    border-top: 1px solid @color-main-borders; /* theme */
                    position: absolute;
                    bottom: 0;
                    width: 100%;
                    ._button {
                        width: 100%;
                        &:first-child {
                            &:extend(._button._button-transparent all);
                        }
                        & + ._button {
                            margin-top: 20px;
                        }
                    }
                }
            }
            ._product-meta,
            ._product-rating {
                display: none;
            }
            ._product-labels {
                clear: both;
                margin-bottom: 0;
                display: none;
            }
            ._product-ribbon-banner {
                display: none;
            }
        }
    }
    ._checkbox {
        min-height: 17px;
        .checker, .checker>span, .checker>span>input {
            border-radius: 3px;
            width: 14px;
            height: 14px;
        }
    }

    ._product-zero-interest {
        height: 28px;

        img {
            height: 100%;
        }
    }
}

/*  LIST
-------------------------------------------------------------------------------*/

/*  Head */

._products-list-head {
    .flexy-row(30px);
    ._products-list-head-item {
        .flexy-col();
        ._products-list-head-item-title {
            h2 {
                .text-overflow();
            }
        }
        ._products-list-head-item-image {
            float: left;
            border: 1px dotted @color-main-borders; /* theme */
            margin-right: 20px;
            ._products-list-head-item-image-thumb {
                height: 130px;
                width: 130px;
                position: relative;
                margin: 10px;
                img {
                    bottom: 0;
                    height: auto;
                    left: 0;
                    margin: auto;
                    max-height: 100%;
                    max-width: 100%;
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: auto;
                }
            }
        }
    }
    & + ._section-title {
        ._section-separator();
    }
}

/*  DETAILS
-------------------------------------------------------------------------------*/

/*  Title */

._product-details-title {
    & + ._product-details-topbar {
        //margin-top: 10px;
        border-top: 0;
    }
    .js-product-title {
        font-size: 30px;
        font-weight: 400;
    }
}

/*  Top Bar */

._product-details-topbar {
    margin-top: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    > * {
        //margin-right: 15px;
        //margin-top: 20px;
    }
    font-size: @font-size-small;
    color: @color-main-meta-text; /* theme */
    ._product-details-topbar-brand {
        float: left;
        a {
            color: @color-main-highlight; /* theme */
        }
        & + ._product-details-topbar-rating {
            margin-left: 25px;
        }
    }
    ._product-details-topbar-rating {
        float: left;
    }
    ._product-details-topbar-compare {
        //float: right;
        float: left;
        ._product-meta-compare-wrapper {
            font-size: 0;
            //._checkbox {
            //    font-size: @font-size-small;
            //    line-height: normal;
            //    padding-left: 26px;
            //    font-weight: 500;
            //}
            ._checkbox {
                font-size: @font-size-medium;
                padding-left: 36px;
                min-height: 21px;
                font-weight: 400;
                .checker {
                    margin-right: 0;
                    top: -3px;
                    span {
                        background-color: transparent;
                        border: none !important;
                        position: relative;
                        display: block;
                        input {
                            display: none;
                        }
                        &:before {
                            font-family: 'Font Awesome 5 Pro';
                            color: @color-main-highlight; /* theme */
                            content:"\f0ec";
                            font-size: @font-size-xxlarge;
                            font-weight: 300;
                        }
                        &.checked {
                            &:before {
                                color: @color-main-highlight; /* theme */
                                font-weight: bold;
                                top: auto;
                                left: 0;
                                transform: none;
                            }
                        }
                    }
                }
            }
        }
        & + ._product-details-topbar-favorites {
            margin-right: 25px;
        }
    }
    ._product-details-topbar-favorites {
        //float: right;
        float: left;
        a {
            display: block;
            font-size: 0;
            color: @color-main-tertiary-text;
            ._figure-stack {
                font-size: @font-size-medium;

                ._figure-stack-icon {
                    font-size: @font-size-xxlarge;
                    color: @color-main-highlight; /* theme */
                }
            }
            &.active {
                //color: @color-top-bar-menu-text-background-highlight; /* theme */
                ._figure-stack {
                    ._figure-stack-icon {
                        font-weight: bold;
                    }
                }
            }
        }
        & + ._product-details-topbar-compare {
            margin-left: 30px;
        }
    }
}

/*  Image */

._product-details-image {
    position: relative;
    //border: 1px solid @color-main-borders; /* theme */
    cursor: pointer;
    ._product-details-image-thumb {
        border: 1px solid;
        border-color: @color-product-details-borders; /* theme */
        text-align: center;
        padding: 20px;
        display: block;
        height: 455px;
        position: relative;
        &.swiper-slide {
            width: 100% !important;
        }
        img {
            bottom: auto;
            height: auto;
            left: 50%;
            margin: 0 auto;
            max-height: 100%;
            max-width: 100%;
            position: absolute;
            right: auto;
            top: 50%;
            width: auto;
            transform: translate(-50%, -50%);
        }
    }
    ._product-details-ribbon-holder {
        position: absolute;
        right: 8px;
        top: 15px;
        z-index: 1;
    }
    ._product-details-ribbon {
        background-color: @color-button-background; /* theme */;
        border-radius: 18px;
        color: @color-button-text; /* theme */
        font-size: @font-size-xsmall;
        font-weight: 600;
        text-transform: uppercase;
        text-align: right;
        padding: 3px 10px;
        position: relative;
        display: table;
        margin-right: 0px;
        margin-left: auto;
        height: 24px;
        & + ._product-details-ribbon {
            margin-top: 5px;
        }
        & when (@rtl) {
            white-space: nowrap;
        }
        &._product-details-discount {
            position: absolute;
            right: -1px;
            bottom: 15px;
            padding: 2px 6px 3px;
            background-color: @color-label-discount-background; /* theme */
            color: @color-label-discount-text; /* theme */
            font-size: @font-size-xlarge;
            text-align: center;
            line-height: 1;
            &:before {
                display: none;
            }
        }
        &._product-details-ribbon-new {
            background-color: @color-label-new-background;
            color: @color-label-new-text;
        }
        &._product-details-ribbon-featured {
            background-color: @color-label-featured-background;
            color: @color-label-featured-text;
        }
        &._product-details-ribbon-sale {
            background-color: @color-label-sale-background;
            color: @color-label-sale-text;
        }
        &._product-details-leasing {
            //display: none;
        }
    }
    ._product-details-ribbon-banner {
        position: absolute;
        z-index: 1;
        width: 100px;
    }
    //._product-details-energy-labels {
    //    position: absolute;
    //    bottom: 15px;
    //    left: 0;
    //    ._product-details-energy-label {
    //        display: inline-block;
    //        position: relative;
    //        min-width: 54px;
    //        font-size: @font-size-xlarge;
    //        line-height: 1;
    //        text-transform: uppercase;
    //        color: @color-main-secondary-text; /* theme */
    //        padding-top: 3px;
    //        padding-bottom: 3px;
    //        padding-left: 10px;
    //        padding-right: 5px;
    //        font-weight: 600;
    //        &:after {
    //            position: absolute;
    //            right: -7px;
    //            top: 0;
    //            .triangle(right, 24px, 7px, @color-main-text);
    //        }
    //        &._label-product-a_ {
    //            background-color: @color-label-product-a_; /* theme */
    //            &:after {
    //                border-left-color: @color-label-product-a_; /* theme */
    //            }
    //        }
    //        &._label-product-a__ {
    //            background-color: @color-label-product-a__; /* theme */
    //            &:after {
    //                border-left-color: @color-label-product-a__; /* theme */
    //            }
    //        }
    //        &._label-product-a___ {
    //            background-color: @color-label-product-a___; /* theme */
    //            &:after {
    //                border-left-color: @color-label-product-a___; /* theme */
    //            }
    //        }
    //        &._label-product-a {
    //            background-color: @color-label-product-a; /* theme */
    //            &:after {
    //                border-left-color: @color-label-product-a; /* theme */
    //            }
    //        }
    //        &._label-product-b {
    //            background-color: @color-label-product-b; /* theme */
    //            &:after {
    //                border-left-color: @color-label-product-b; /* theme */
    //            }
    //        }
    //        &._label-product-c {
    //            background-color: @color-label-product-c; /* theme */
    //            &:after {
    //                border-left-color: @color-label-product-c; /* theme */
    //            }
    //        }
    //        &._label-product-d {
    //            background-color: @color-label-product-d; /* theme */
    //            &:after {
    //                border-left-color: @color-label-product-d; /* theme */
    //            }
    //        }
    //        &._label-product-e {
    //            background-color: @color-label-product-e; /* theme */
    //            &:after {
    //                border-left-color: @color-label-product-e; /* theme */
    //            }
    //        }
    //        &._label-product-f {
    //            background-color: @color-label-product-f; /* theme */
    //            &:after {
    //                border-left-color: @color-label-product-f; /* theme */
    //            }
    //        }
    //        &._label-product-g {
    //            background-color: @color-label-product-g; /* theme */
    //            &:after {
    //                border-left-color: @color-label-product-g; /* theme */
    //            }
    //        }
    //    }
    //}
}

._product-details-countdown {
    padding-top: 15px;
    ._countdown-icon {
        font-size: 24px;
        margin-right: 7px;
    }

    ._countdown-timer-digit {
        font-size: 24px;
    }
}

/*  Labels */

._product-details-labels {
    //margin-top: 10px;
    //margin-left: -5px;
    ._label-product {
        font-size: @font-size-xsmall;
        text-transform: uppercase;
        font-weight: 700;
        background-color: @color-label-custom-background; /* theme */
        border-radius: 18px;
        color: @color-label-custom-text; /* theme */
        padding: 6px 10px 5px;
        text-align: center;
        display: inline-block;
        width: 100%;
        +._label-product {
           margin: 5px 0;
        }
    }
}

/*  Gallery */

//._product-details-gallery {
//    padding-top: 10px;
//
//    .slick-track {
//        //max-width: 200px;
//        //width: 70px !important;
//    }
//    .slick-slide {
//        //float: left;
//        width: 85px;
//        + .slick-slide {
//            margin-left: 8px;
//        }
//    }
//    ul {
//        //.clearfix();
//        list-style: none;
//        overflow: hidden;
//        li {
//            //float: left;
//            width: 85px;
//            border: 1px solid @color-main-borders; /* theme */
//            padding: 5px;
//            margin-bottom: 5px;
//            &.slick-active {
//                display: block !important;
//            }
//            a {
//                display: block;
//                height: 73px;
//                position: relative;
//                img {
//                    bottom: 0;
//                    height: auto;
//                    left: 0;
//                    margin: auto;
//                    max-height: 100%;
//                    max-width: 100%;
//                    position: absolute;
//                    right: 0;
//                    top: 0;
//                    width: auto;
//                }
//            }
//            & + li {
//                //margin-left: 8px;
//                margin-bottom: 5px;
//            }
//        }
//        .slick-arrow {
//            position: absolute;
//            background-color: rgba(0, 0, 0, .5);
//            text-align: center;
//            width: 85px;
//            height: 20px;
//            z-index: 10;
//            &:before {
//                font-family: @font-awesome;
//                font-size: @font-size-large;
//                opacity: 1;
//            }
//            &.slick-prev {
//                top: auto;
//                right: 0;
//                bottom: 12px;
//                left: 0;
//                &:before {
//                    content: '\f077';
//                }
//            }
//            &.slick-next {
//                top: auto;
//                right: 0;
//                bottom: -10px;
//                left: 0;
//                &:before {
//                    content: '\f078'
//                }
//            }
//        }
//    }
//}

@gallery-thumb-size: 68px;
@gallery-thumb-offset: 10px;

._product-details-gallery-container {
    display: table-cell;
    vertical-align: top;
    width: 1px;
    position: relative;
    margin-bottom: 50px;

    &._hide-nav {
        .swiper-button {
            display: none;
        }
    }

    .loader-container {
        display: none;
    }

    .swiper-button {
        background: @color-button-background; /* theme */
        color: @color-button-text; /* theme */
        font-size: 20px;
        width: @gallery-thumb-size;
        height: 20px;
        margin: 10px 0 0;
        padding: 0;
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        bottom: auto;
        visibility: hidden;
        z-index: 1;

        &.swiper-button-disabled {
            opacity: 0;

            &.swiper-button-prev {
                display: none;

                + .swiper-button-next {
                    margin-top: 32px;
                }
            }

            &.swiper-button-next {
                display: none;
            }
        }

        + .swiper-button {
            margin-top: 2px;
        }

        .fa {
            .centerer(true, true);
        }
    }
}

._product-details-gallery {
    overflow: hidden;
    visibility: hidden;

    &.swiper-container-vertical {
        visibility: visible;

        ~ .swiper-button {
            visibility: visible;
        }
    }

    .swiper-wrapper {
        height: @gallery-thumb-size*5 + @gallery-thumb-offset*4;
    }

    .cc-active {
        a {
            &:before {
                border-width: 2px;
                border-color: @color-main-highlight; /* theme */
            }
        }
    }

    ul {
        list-style-type: none;
        width: @gallery-thumb-size;
        margin-right: 20px;
        justify-content: flex-start;
        align-content: flex-start;
    }

    li {
        height: auto !important;
    }

    a {
        display: block;
        background-color: @color-product-image-background; /* theme */
        padding-bottom: 100%;
        position: relative;

        &:before {
            content: '';
            border: 1px solid;
            border-color: @color-product-image-borders; /* theme */
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }
    }

    img {
        max-width: 100%;
        max-height: 100%;
        .centerer(true, true);
    }
}

/*  Price */

._product-details-price {
    display: flex;
    align-items: center;
    flex-flow: wrap;
    gap: 0 20px;
    padding-bottom: 10px;
    position: relative;
    ._product-discount {
        color: @color-label-discount-text; /* theme */
        font-size: 23px;
        font-weight: 700;
        text-align: center;
        line-height: 1;
        &._dis-class {
            top: 0;
        }
        +._product-details-sku {
            top: 40px;
        }
        +._product-details-energy-labels {
            margin-top: 30px;
        }
    }
    ._product-details-price-bar {
        > span {
            display: inline-block;
            &._product-details-price-new {
                display: inline-block;
                font-size: @font-size-product-price;
                color: @color-main-text; /* theme */
                font-weight: 700;
                line-height: 1;
                & when (@rtl) {
                    direction: ltr;
                }
            }
            &._product-details-price-calc {
                font-size: 23px;
                text-align: left;
                color: @color-main-meta-text; /* theme */

                span {
                    display: block;
                    &.text-old-price {
                        display: inline;
                    }
                    &._product-details-price-old {
                        display: inline;
                        text-decoration: line-through;
                        color: @color-main-meta-text; /* theme */
                        position: relative;
                        margin-left: 12px;
                        &:before {
                            position: absolute;
                            right: ~'calc(100% + 6px)';
                            line-height: 20px;
                            top: 0;
                            //content: 'Стара цена:';
                            content: none;
                            white-space: nowrap;
                        }
                        > i {
                            font-style: normal;
                        }
                    }
                    &._product-details-price-saved {
                        position: relative;
                        &:before {
                            position: absolute;
                            right: ~'calc(100% + 6px)';
                            line-height: 20px;
                            top: 0;
                            //content: 'Спестявате:';
                            content: none;
                            white-space: nowrap;
                        }
                        strong {
                            font-weight: normal;
                            color: @color-main-highlight; /* theme */
                        }
                    }
                }
            }
        }
        ._product-details-price-calc {
            display: inline-block;
        }
    }
    ._product-details-choose {
        display: none;
        color: @color-main-meta-text; /* theme */
        padding-top: 5px;
    }
    ._product-details-sku {
        position: absolute;
        top: 0;
        right: 0;
        font-size: @font-size-small;
        color: @color-main-tertiary-text; /* theme */
        max-width: 150px;
        word-break: break-word;
        text-align: right;
        > i {
            font-style: normal;
        }
    }
}
._product-details-title {
    ._product-details-sku,
    ._product-details-meta-title,
    ._product-details-meta-value {
        font-size: @font-size-xsmall;
        word-break: break-word;
        font-style: normal;

        > i {
            font-style: normal;
        }
    }
    ._product-details-sku,
    ._product-details-meta-value {
        margin-right: 15px;
    }
    ._product-details-sku {
        color: @color-main-tertiary-text; /* theme */

        > i {
            color: @color-main-secondary-text; /* theme */
        }
    }
    ._product-details-meta-title {
        color: @color-main-tertiary-text; /* theme */
    }
    ._product-details-meta-value {
        color: @color-main-secondary-text; /* theme */
    }
}

._product-details-zero-interest {
    padding: 10px 0;
    text-align: center;
}

/*  Parameters */

._product-details-parameters {
    margin-top: 30px;
    ._product-out-of-stock {
        font-size: @font-size-small;
        color: @color-main-meta-text; /* theme */
    }
    .input-group {
        display: table;
    }
    & + ._product-details-leasing {
        margin-top: 15px;
    }
    ._button {
        &:extend(._button._button-active all);
    }
}

._parameter-radio-values {
    margin-bottom: -3px;
    .clearfix();
}

._parameter-radio-value {
    float: left;
    display: table;
    height: 36px;
    min-width: 44px;
    margin-right: 3px;
    margin-bottom: 3px;

    ._radio {
        display: table-cell;
        vertical-align: middle;
        border: 1px solid;
        border-color: @color-main-borders; /* theme */
        padding: 5px;
        text-align: center;
        font-weight: normal;
        font-size: 13px;

        &.active {
            border-width: 2px;
            padding: 4px;
            border-color: @color-main-highlight; /* theme */
        }

        .radio {
            display: none;
        }
    }
}

._parameter-image-values {
    margin-left: -1%;
    margin-bottom: -1%;
    .clearfix();
}

._parameter-image-value {
    float: left;
    width: 24%;
    max-width: 100px;
    border: 1px solid @color-main-borders; /* theme */
    margin-left: 1%;
    margin-bottom: 1%;
    word-break: break-word;

    &.active {
        border-width: 2px;
        border-color: @color-main-highlight; /* theme */
    }

    ._radio {
        display: block;
        height: 0;
        padding: 0;
        padding-bottom: 100%;
        font-size: @font-size-small;
        position: relative;
        overflow: hidden;

        .radio {
            top: 5px;
            left: 5px;
        }

        .radio-label {
            display: inline-block;
            padding-top: 4px;
            padding-left: 30px;
            padding-right: 5px;
        }

        img {
            .centerer(true, true);

            ~ .radio,
            ~ .radio-label {
                display: none;
            }
        }
    }
}

._parameter-color-values {
    margin-right: -5px;
    margin-bottom: -5px;
    .clearfix();
}

._parameter-color-value {
    float: left;
    margin-right: 5px;
    margin-bottom: 5px;

    &.active {
        ._radio {
            border-color: @color-main-highlight; /* theme */
        }
    }

    ._radio {
        display: block;
        width: 34px;
        height: 34px;
        border: 2px solid transparent;
        padding: 0;
        margin: 0;
        font-size: 0;
        line-height: 0;

        .radio {
            display: none;
        }

        .radio-label {
            display: none;
        }
    }
}

/*  Delvery */

._product-details-delivery {
    font-size: @font-size-small;
    color: @color-main-meta-text; /* theme */
    ._product-details-delivery-time {
        color: @color-main-tertiary-text; /* theme */
    }
    ._product-details-delivery-text {
    }
}

/*  Share */

._product-details-share {
    background-color: @color-main-secondary-background; /* theme */
    margin-top: 40px;
    padding: 15px 20px;
    ._share {
        a {
            margin-bottom: 0;
            font-size: 0;
        }
    }
}

/*  Meta */

._product-details-meta {
    margin-top: 15px;
    font-size: @font-size-medium;
    ul {
        list-style: none;
        li {
            padding: 4px 10px 3px;
            > span {
                &._product-details-meta-title {
                    color: @color-main-meta-text; /* theme */
                    &:after {
                        content: ':';
                    }
                }
                &._product-details-meta-value {
                    & when (@rtl) {
                        direction: ltr;
                    }
                }
            }
            &:nth-child(2n+1) {
                background-color: @color-main-secondary-background; /* theme */
            }
            & + li {
                margin-top: 5px;
            }
        }
    }
    & + ._product-details-properties-wrapper {
        margin-top: 15px;
        border-top: 1px dotted @color-main-borders; /* theme */
        padding-top: 15px;
    }
}

/*  Properties */
.page-product {
    ._product-sidebar {
        ._product-details-properties-wrapper {
            color: #333;
            margin-bottom: 20px;
            ul {
                li {
                    background-color: @color-main-background;
                    margin: 0;
                    &:nth-child(2n+1) {
                        background-color: @color-main-background;
                    }
                    ._product-details-properties-title {
                        color: @color-main-meta-text;
                        position: relative;
                        &:before {
                            content: "";
                            background-color: #3D3D3D;
                            border-radius: 50%;
                            width: 5px;
                            height: 5px;
                            position: absolute;
                            left: -12px;
                            top: 50%;
                            transform: translateY(-50%);
                        }
                    }
                    ._product-details-properties-value {
                        color: @color-main-secondary-text;
                    }
                }
            }
        }
        ._form-row-actions {
            ._form-col-button {
                padding-left: calc(10px / 2);
                padding-right: calc(10px / 2);
                ._button {
                    float: left;
                    height: 44px;
                    max-width: 190px;
                    font-size: 20px;
                    font-weight: 300;

                    i {
                        font-size: 18px;
                    }

                    ._figure-stack {
                        margin-left: -10px;
                    }
                }
            }
        }
    }
    ._section-title {
        border-bottom: 0;
        margin-bottom: 15px;
    }
    ._product-details-energy-labels {
        //display: none;
        position: absolute;
        top: 0;
        right: 0;
        text-align: center;
        ._product-details-energy-label {
            cursor: pointer;
            display: inline-block;
            position: relative;
            font-size: @font-size-medium;
            line-height: 1;
            text-transform: uppercase;
            color: @color-main-background; /* theme */
            text-align: center;
            font-weight: bold;
            padding: 5px 3px;
            &:before {
                position: absolute;
                left: -7px;
                top: 0;
                .triangle(left, 24px, 7px, @color-main-text);
                border-top: 24px solid transparent;
                border-bottom: 0 solid transparent;
            }
            &._label-product-a_ {
                background-color: @color-label-product-a_; /* theme */
                &:before {
                    border-right-color: @color-label-product-a_; /* theme */
                }
            }
            &._label-product-a__ {
                background-color: @color-label-product-a__; /* theme */
                &:before {
                    border-right-color: @color-label-product-a__; /* theme */
                }
            }
            &._label-product-a___ {
                background-color: @color-label-product-a___; /* theme */
                &:before {
                    border-right-color: @color-label-product-a___; /* theme */
                }
            }
            &._label-product-a {
                background-color: @color-label-product-a; /* theme */
                &:before {
                    border-right-color: @color-label-product-a; /* theme */
                }
            }
            &._label-product-b {
                background-color: @color-label-product-b; /* theme */
                &:after {
                    border-right-color: @color-label-product-b; /* theme */
                }
            }
            &._label-product-c {
                background-color: @color-label-product-c; /* theme */
                &:before {
                    border-right-color: @color-label-product-c; /* theme */
                }
            }
            &._label-product-d {
                background-color: @color-label-product-d; /* theme */
                &:before {
                    border-right-color: @color-label-product-d; /* theme */
                }
            }
            &._label-product-e {
                background-color: @color-label-product-e; /* theme */
                &:before {
                    border-right-color: @color-label-product-e; /* theme */
                }
            }
            &._label-product-f {
                background-color: @color-label-product-f; /* theme */
                &:before {
                    border-right-color: @color-label-product-f; /* theme */
                }
            }
            &._label-product-g {
                background-color: @color-label-product-g; /* theme */
                &:before {
                    border-right-color: @color-label-product-g; /* theme */
                }
            }
        }
    }
    ._product-details-properties {
        &._energy-label {
            position: relative;
            display: flex;
            align-items: center;
            gap: 0 12px;
            margin-top: 10px;

            ._z-property-sidebar,
            ._product-info-list {
                list-style: none;
            }

            ._z-property-sidebar {
                max-width: 45px;
            }

            ._product-info-list {
                a {
                    color: #3D3D3D;
                    font-weight: 300;
                    font-size: 14px;
                    line-height: 17px;
                }
            }
        }
    }
}

._product-details-properties-wrapper {
    //border-top: 1px dotted @color-product-details-borders; /* theme */
    margin-top: 0;
    padding-top: 10px;
    ._product-details-properties {
        font-size: @font-size-medium;
        ul {
            list-style: none;
            li {
                padding: 4px 10px 3px;
                > span {
                    &._product-details-properties-title {
                        color: @color-main-meta-text; /* theme */
                    }
                    &._product-details-properties-value {
                        & when (@rtl) {
                            direction: ltr;
                        }
                    }
                }
                &:nth-child(2n+1) {
                    background-color: @color-main-secondary-background; /* theme */
                }
                & + li {
                    margin-top: 5px;
                }
            }
        }
    }
}

/*  Tabs */

._product-details-tabs {
    margin-bottom: 30px;
    ._product-details-tabs-menu {
        border-bottom: 1px solid @color-product-listing-borders; /* theme */
        list-style: none;
        display: inline-block;
        .clearfix();
        li {
            float: left;
            a {
                &._button {
                    background-color: transparent;
                    border-top: 3px solid transparent;
                    border-color: transparent;
                    color: @color-main-text; /* theme */
                    font-size: @font-size-xlarge;
                    font-weight: 400;
                    height: 60px;
                    padding-left: 0;
                    padding-right: 0;
                    text-transform: none;
                }
            }
            &.active {
                a {
                    &._button {
                        border-bottom: 3px solid #0497aa;
                        color: @color-main-highlight; /* theme */
                        font-weight: 700;
                    }
                }
            }

            & + li {
                a {
                    &._button {
                        margin-left: 55px;
                    }
                }
            }
        }
    }
    ._product-details-tabs-item {
        //border: 1px solid @color-main-borders; /* theme */
        //padding: 20px;
        border: 0;
        padding: 0;
    }
}

/*	Accordion
*******/

._product-details-accordion {
    > ul {
        list-style: none;
        > li {
            position: relative;
            > ._button {
                width: 100%;
                text-align: left;
                background-color: @color-main-highlight;
                border-color: @color-main-highlight;
                color: @color-main-background; /* theme */
            }
            > ._toggle {
                position: absolute;
                top: 0;
                right: 0;
                width: 40px;
                height: 40px;
                &:before {
                    font-family: "Glyphicons Halflings";
                    content:"\2b";
                    .centerer(true, true);
                    font-size: @font-size-small;
                    font-weight: 300;
                    color: @color-main-background;
                }
            }
            ._product-details-accordion-txt {
                display: none;
                padding: 15px 0;
                ._product-details-meta {
                    margin-top: 0;
                }
            }
            &.active {
                > ._button {
                    &:extend(._button);
                    text-align: left;
                }
                > ._toggle {
                    &:before {
                        content:"\2212";
                        color: @color-main-background; /* theme */
                    }
                }
            }
            & + li {
                margin-top: 1px;
            }
        }
    }
}

/*	Manuals
*******/

._product-details-manual {
    ul {
        list-style: none;
        li {
            a {
                position: relative;
                display: inline-block;
                padding-left: 20px;
                &:before {
                    position: absolute;
                    left: 0;
                    top: 0;
                    font-family: FontAwesome;
                    content:"\f0f6";
                }
            }
            & + li {
                margin-top: 10px;
            }
        }
    }
}

/*  Comments */

._facebook-comments {
    & + ._disqus-comments {
        margin-top: 20px;
    }
}

/*  DETAILS - POPUP
-------------------------------------------------------------------------------*/

.modal-dialog.product-details {
    width: 985px;
    .modal-body {
        padding-left: 5px;
        padding-right: 5px;
    }
}

._product-details-tabs {
    ._product-details-characteristics {
        ._textbox {
            display: table;
            width: 100%;
            ._product-details-meta,
            ._product-details-properties-wrapper {
                display: table-cell;
                width: 50%;
            }
            ._product-details-meta {
                + ._product-details-properties-wrapper {
                    padding-left: 20px;
                    border-top: 0;
                }
            }
        }

        ._product-details-properties {
            display: table;
            width: 100%;
            ul {
                display: table-cell;
                width: 50%;
                + ul {
                   padding-left: 20px;
                }
                li {
                    display: table;
                    width: 100%;
                    span {
                        display: table-cell;
                        width: 50%;
                    }
                }
            }
            ._product-details-properties-title {
                color: @color-main-tertiary-text; /* theme */
            }
            ._product-details-properties-value {
                color: @color-main-secondary-text; /* theme */
            }
        }
    }
}

._product-details-short-description {
    color: @color-main-tertiary-text;
}

._product-details-extra-banner {
    +._product-details-extra-banner {
        margin-top: 10px;
    }
}

._section-related-products,
._section-bundle-list {
    //background-color: #f7f7f7;
    padding-bottom: 20px;
    padding-top: 20px;
}

._section-related-products,
._section-bundle-list {
    margin-top: 30px;
    + ._section-related-products{
        margin-top: 0;
    }
}

._product-list-product-fish a {
    text-decoration: underline;
    font-size: 9px;
}

._product-list-energy-label a {
    cursor: pointer;
    display: inline-block;
    position: relative;
    font-size: @font-size-medium;
    line-height: 1;
    text-transform: uppercase;
    color: @color-main-background; /* theme */
    text-align: center;
    font-weight: 700;
    padding: 5px 3px;

    &:hover {
        color: @color-main-background; /* theme */
    }

    &:before {
        position: absolute;
        left: -7px;
        top: 0;
        content: '';
        display: block;
        width: 0;
        height: 0;
        border-right: 7px solid transparent;
        border-top: 24px solid transparent;
        border-bottom: 0 solid transparent;
    }

    &._label-product-a_ {
        background-color: @color-label-product-a_; /* theme */
        &:before {
            border-right-color: @color-label-product-a_; /* theme */
        }
    }
    &._label-product-a__ {
        background-color: @color-label-product-a__; /* theme */
        &:before {
            border-right-color: @color-label-product-a__; /* theme */
        }
    }
    &._label-product-a___ {
        background-color: @color-label-product-a___; /* theme */
        &:before {
            border-right-color: @color-label-product-a___; /* theme */
        }
    }
    &._label-product-a {
        background-color: @color-label-product-a; /* theme */
        &:before {
            border-right-color: @color-label-product-a; /* theme */
        }
    }
    &._label-product-b {
        background-color: @color-label-product-b; /* theme */
        &:before {
            border-right-color: @color-label-product-b; /* theme */
        }
    }
    &._label-product-c {
        background-color: @color-label-product-c; /* theme */
        &:before {
            border-right-color: @color-label-product-c; /* theme */
        }
    }
    &._label-product-d {
        background-color: @color-label-product-d; /* theme */
        &:before {
            border-right-color: @color-label-product-d; /* theme */
        }
    }
    &._label-product-e {
        background-color: @color-label-product-e; /* theme */
        &:before {
            border-right-color: @color-label-product-e; /* theme */
        }
    }
    &._label-product-f {
        background-color: @color-label-product-f; /* theme */
        &:before {
            border-right-color: @color-label-product-f; /* theme */
        }
    }
    &._label-product-g {
        background-color: @color-label-product-g; /* theme */
        &:before {
            border-right-color: @color-label-product-g; /* theme */
        }
    }
}

._product-details-properties._energy-label .image-popup img {
    max-height: 30px;
}

._product-list-energy-label-product-fish img {
    max-height: 14px;
}

._product-list-product-fish a {
    color: @color-product-listing-actions-text; /* theme */
    font-weight: 300;
    font-size: 8px;
    margin-left: 9px;
    text-decoration: none;
}

._product {
    &:not(._product-max) {
        ._product-list-energy-label-product-fish {
            height: 26px;
            display: flex;
            align-items: center;
        }
    }
}

/* Rating line */
._product-details-wrap {
    ._rating-line {
        display: block;
        margin-bottom: 20px;
    }

    .simple-rating {
        font-size: 20px;

        b {
            font-weight: 400;
        }
    }
}

/* Sticky buy button */
@media (min-width: 768px) {
    .sticky-buy-button,
    ._form ._form-row ._form-actions ._button.sticky-buy-button {
        display: none !important;
    }
}

@media (max-width: 767px) {
    .sticky-buy-button,
    ._form ._form-row ._form-actions ._button.sticky-buy-button {
        position: fixed;
        left: 20px;
        bottom: 20px;
        width: calc(100% ~'-' 40px);
        z-index: 200;
    }
}