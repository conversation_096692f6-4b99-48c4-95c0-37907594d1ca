._type-megamenu {
  &._navbar {
    perspective: 1000px;
    position: relative;
    z-index: 4;
  }
  ._navigation-main-list {
    height: 100%;
  }

  ._navigation-main-list-item {
    @media (min-width: (@screen-lg + 1)) {
      > ._navigation-dropdown {
        transform-origin: 0 0;
        transition: .5s;
        transform: rotateX(-90deg);
        opacity: 0;
        visibility: hidden;
      }

      &:hover {
        > a {
          background-color: @color-navigation-hover-background; /* theme */
          color: @color-navigation-hover-text; /* theme */

          &:focus {
            color: @color-navigation-hover-text; /* theme */
          }
        }

        > ._navigation-dropdown {
          transition: transform .5s;
          visibility: visible;
          opacity: 1;
          transform: rotateX(0);
        }
      }
    }
  }

  ._navigation-main-list-item-link {
    display: flex;
    align-items: center;
    height: 100%;
    //min-height: 76px;
    padding: 20px 15px;
  }

  ._navigation {
    text-align: center;

    ul {
      list-style-type: none;
    }

    li {
      display: inline-block;
      line-height: @line-height-low;
      .uppercase();
      position: static;
    }

    a {
      @media @hover {
        &:hover {
          text-decoration: none;
        }
      }
    }

    .active {
      > a {
        color: @color-navigation-hover-text; /* theme */
      }
    }

    .collapse-icon {
      display: none;
    }

    ._navigation-hamburger {
      display: none;
      font-size: 20px;
      line-height: @line-height-low;

      ._figure-stack-label {
        display: none;
      }
    }

    &-dropdown {
      background-color: @color-dropdowns-background; /* theme */
      border-width: 1px 0;
      border-style: solid;
      border-color: @color-dropdowns-borders; /* theme */
      text-align: left;
      padding: 30px 0;
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      visibility: visible;
      opacity: 1;

      ul {
        .clearfix();

        @media (min-width: (@screen-lg + 1)) {
          max-width: 1140px;
          margin: 0 auto;
        }
      }

      li {
        float: left;
        width: 25%;
        display: block;
        padding: 10px 15px;

        &:nth-child(4n+1) {
          clear: both;
        }
      }

      a {
        min-height: initial;
        display: inline-block;
        padding: 0;

        @media @hover {
          &:hover {
            color: @color-dropdowns-highlight; /* theme */

            &:focus {
              color: @color-dropdowns-highlight; /* theme */
            }
          }
        }
      }

      ._navigation-dropdown {
        display: block;
        font-family: @font-family-main; /* theme */
        position: static;
        top: 0;
        padding: 0;
        border: 0;

        li {
          float: none;
          width: auto;
          border-left: 0;
          padding: 0;
          font-weight: normal;
          text-transform: initial;
          font-size: calc(@font-size-main ~'-' 1px); /* theme */
          padding: 3px 0;

          &:last-child {
            margin-bottom: 0;
          }
        }

        ul {
          margin-top: 7px;
        }

        @media @hover {
          ._navigation-dropdown {
            padding-left: 10px;
          }
        }
      }
    }
  }
}