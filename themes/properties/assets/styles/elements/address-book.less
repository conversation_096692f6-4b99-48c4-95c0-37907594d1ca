/*=============================================================================*\
    ADDRESS BOOK
\*=============================================================================*/

._address-book-list {
    position: relative;
    overflow: hidden;

    &.hidden {
        display: block !important;
    }

    ul {
        font-size: 0;
        list-style: none;
        margin: -15px;
    }

    li {
        width: 50%;
        display: inline-block;
        font-size: @font-size-base; /* theme */
        padding: 15px;
        vertical-align: top;

        &.active {
            ._address-book-list-action-mdefault {
                border-color: @color-main-highlight; /* theme */
            }
        }
    }
}

._address-book-list-item {
    position: relative;
    
    ._radio {
        display: block;
        padding: 25px 30px;

        .radio {
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            height: auto;
            width: auto;
            margin: 0;

            > span {
                background-color: transparent;
                border-width: 2px;
                border-radius: 0;
                height: 100%;
                width: 100%;

                &.checked {
                    background: transparent;
                    border-color: @color-main-highlight; /* theme */

                    &:before {
                        display: none;
                    }
                }
            }
        }
    }
}

._address-book-list-action-mdefault {
    display: block;
    padding: 25px 30px;
    border: 2px solid @color-main-borders; /* theme */
    position: relative;
}

._address-book-list-item-data {
    display: block;
    position: relative;
    z-index: 1;
}

._address-book-list-item-data-name {
    color: @color-main-titles; /* theme */
    display: block;
    font-weight: bold;
    margin-bottom: 8px;
}

._address-book-list-item-data-item {
    display: block;
    color: @color-main-meta-text; /* theme */
    margin-bottom: 8px;

    &:last-child {
        margin-bottom: 0;
    }

    strong {
        color: @color-main-text; /* theme */
        font-weight: 300;
    }
}

._address-book-list-item-edit {
    font-size: 12px;
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1;

    @media (min-width: @screen-desktop) {
        &:hover {
            color: @color-main-highlight; /* theme */
        }
    }
}

._address-book-list-actions {
    font-size: 12px;
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1;

    > ._figure-stack {
        @media (min-width: @screen-desktop) {
            &:hover {
                color: @color-main-highlight; /* theme */
            }
        }

        & + ._figure-stack {
            margin-left: 5px;
        }
    }
}

._address-book-controls {
    ._checkbox {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        text-align: center;
        text-transform: uppercase;
        color: @color-main-meta-text; /* theme */
        border-width: 2px;
        border-style: dashed;
        border-color: @color-main-borders; /* theme */
        padding-left: 0;
        min-height: 234px;
        user-select: none;

        &:before {
            font-family: FontAwesome;
            content: "\f055";
            font-size: 60px;
        }
        
        input,
        .checker {
            display: none;
        }

        &.active {
            border-style: solid;
            border-color: @color-main-highlight; /* theme */
            color: @color-main-highlight; /* theme */

            &:before {
                content: "\f057";
            }
        }
    }
}

._address-book-add {
    margin-top: 30px;
}

.region-holder {
    margin-bottom: 15px;

    &:last-child {
        margin-bottom: 0;
    }
}