[{"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 1, "mapping": "main", "name": "Главно меню", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 11, "parent_id": null, "order": 2, "type": "category", "link_type": "category", "link_id": 2, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Sofas and couches"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 12, "parent_id": null, "order": 3, "type": "category", "link_type": "category", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Textiles"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 13, "parent_id": 40, "order": 1, "type": "category", "link_type": "category", "link_id": 3, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Tables"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 14, "parent_id": 40, "order": 2, "type": "category", "link_type": "category", "link_id": 4, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Chairs"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 15, "parent_id": null, "order": 4, "type": "category", "link_type": "category", "link_id": 5, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Lighting"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 16, "parent_id": 11, "order": 1, "type": "category", "link_type": "category", "link_id": 14, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Couches"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 17, "parent_id": 11, "order": 2, "type": "category", "link_type": "category", "link_id": 15, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "<PERSON><PERSON><PERSON>"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 18, "parent_id": 12, "order": 1, "type": "category", "link_type": "category", "link_id": 6, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Pillows"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 19, "parent_id": 12, "order": 2, "type": "category", "link_type": "category", "link_id": 13, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Curtains"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 22, "parent_id": 40, "order": 4, "type": "category", "link_type": "category", "link_id": 9, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Dining chairs"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 23, "parent_id": 40, "order": 3, "type": "category", "link_type": "category", "link_id": 10, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Bar chairs"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 24, "parent_id": 15, "order": 1, "type": "category", "link_type": "category", "link_id": 11, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Chandeliers"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 25, "parent_id": 15, "order": 2, "type": "category", "link_type": "category", "link_id": 12, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "<PERSON><PERSON>"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 40, "parent_id": null, "order": 1, "type": "selection", "link_type": "selection", "link_id": 2, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Furnitures"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 41, "parent_id": null, "order": 5, "type": "selection", "link_type": "selection", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "SALE"}, "relations": []}]}}, {"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 2, "mapping": "footer", "name": "Долно меню", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 26, "parent_id": null, "order": 1, "type": "page", "link_type": "page", "link_id": 2, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "History"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 27, "parent_id": 26, "order": 1, "type": "page", "link_type": "page", "link_id": 9, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Leatherwork"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 28, "parent_id": 26, "order": 2, "type": "page", "link_type": "page", "link_id": 10, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Textile production"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 29, "parent_id": 26, "order": 2, "type": "page", "link_type": "page", "link_id": 8, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Features of velvet"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 30, "parent_id": null, "order": 2, "type": "page", "link_type": "page", "link_id": 2, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "TERMS"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 31, "parent_id": 30, "order": 3, "type": "page", "link_type": "page", "link_id": 5, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Returns & Refunds"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 32, "parent_id": 30, "order": 2, "type": "page", "link_type": "page", "link_id": 6, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Payment and Delivery"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 33, "parent_id": 30, "order": 1, "type": "page", "link_type": "page", "link_id": 7, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Frequently asked questions"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 34, "parent_id": null, "order": 3, "type": "page", "link_type": "page", "link_id": 2, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Interior recommendations"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 35, "parent_id": 34, "order": 1, "type": "page", "link_type": "page", "link_id": 11, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Spaces up to 20m2"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 36, "parent_id": 34, "order": 3, "type": "page", "link_type": "page", "link_id": 12, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Attic spaces"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 37, "parent_id": 34, "order": 2, "type": "page", "link_type": "page", "link_id": 13, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Spaces with many doors"}, "relations": []}]}}, {"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 3, "mapping": "navigationLinksPage", "name": "Странично меню - Страници", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 39, "parent_id": null, "order": 1, "type": null, "link_type": null, "link_id": null, "route": "blog.list", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Блог"}, "relations": []}]}}]