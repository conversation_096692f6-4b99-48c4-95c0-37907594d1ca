[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 1, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-17", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 15, "vendor_id": null, "image_id": 2, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 145000, "price_to": 145000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 2, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 2, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 145000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 2, "name": "Двуместен диван от естествено дърво син текстил", "parent_id": 1, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "dvumesten-divan-ot-estestveno-darvo-sin-tekstil-image_618438ddc8d3d.jpeg", "background": "#696969", "width": 4800, "height": 2700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 7, "color_id": 44, "percents": 36, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 8, "color_id": 26, "percents": 15, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 9, "color_id": 55, "percents": 15, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 10, "color_id": 130, "percents": 12, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 11, "color_id": 38, "percents": 9, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 687300}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 1, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 2, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 3, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-16", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 15, "vendor_id": null, "image_id": 4, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 395000, "price_to": 395000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 4, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 4, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 395000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 4, "name": "Триместен диван с дървени крачета и зелена тапицерия", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "trimesten-divan-s-darveni-kraceta-i-zelena-tapiceria-image_61843ae0418f3.jpeg", "background": "#800000", "width": 4070, "height": 2300, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 19, "color_id": 87, "percents": 38, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 20, "color_id": 12, "percents": 17, "R": 165, "G": 42, "B": 42, "hex": "#A52A2A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 21, "color_id": 122, "percents": 13, "R": 188, "G": 143, "B": 143, "hex": "#BC8F8F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 22, "color_id": 55, "percents": 10, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 23, "color_id": 60, "percents": 7, "R": 205, "G": 92, "B": 92, "hex": "#CD5C5C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 24, "color_id": 44, "percents": 5, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 561619}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 3, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-15", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": "Материал", "p3": "Цвят на крачетата", "p1_id": 2, "p2_id": 3, "p3_id": 4, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 14, "vendor_id": null, "image_id": 5, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 75000, "price_to": 98000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 106, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 26, "v1": "Чер<PERSON><PERSON>н", "v2": "Кожа", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 4, "v2_id": 7, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 27, "v1": "Чер<PERSON><PERSON>н", "v2": "Кожа", "v3": "<PERSON>ин", "v1_id": 4, "v2_id": 7, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 28, "v1": "Чер<PERSON><PERSON>н", "v2": "Кожа", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 4, "v2_id": 7, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 29, "v1": "Чер<PERSON><PERSON>н", "v2": "Кожа", "v3": "Сребърен", "v1_id": 4, "v2_id": 7, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 30, "v1": "Чер<PERSON><PERSON>н", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 4, "v2_id": 8, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 31, "v1": "Чер<PERSON><PERSON>н", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON>ин", "v1_id": 4, "v2_id": 8, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 32, "v1": "Чер<PERSON><PERSON>н", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 4, "v2_id": 8, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 33, "v1": "Чер<PERSON><PERSON>н", "v2": "Па<PERSON><PERSON>к", "v3": "Сребърен", "v1_id": 4, "v2_id": 8, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 34, "v1": "Чер<PERSON><PERSON>н", "v2": "Вел<PERSON>р", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 4, "v2_id": 9, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 35, "v1": "Чер<PERSON><PERSON>н", "v2": "Вел<PERSON>р", "v3": "<PERSON>ин", "v1_id": 4, "v2_id": 9, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 36, "v1": "Чер<PERSON><PERSON>н", "v2": "Вел<PERSON>р", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 4, "v2_id": 9, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 37, "v1": "Чер<PERSON><PERSON>н", "v2": "Вел<PERSON>р", "v3": "Сребърен", "v1_id": 4, "v2_id": 9, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 38, "v1": "Чер<PERSON><PERSON>н", "v2": "Кадифе", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 4, "v2_id": 12, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 39, "v1": "Чер<PERSON><PERSON>н", "v2": "Кадифе", "v3": "<PERSON>ин", "v1_id": 4, "v2_id": 12, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 40, "v1": "Чер<PERSON><PERSON>н", "v2": "Кадифе", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 4, "v2_id": 12, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 41, "v1": "Чер<PERSON><PERSON>н", "v2": "Кадифе", "v3": "Сребърен", "v1_id": 4, "v2_id": 12, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 42, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Кожа", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 5, "v2_id": 7, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 43, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Кожа", "v3": "<PERSON>ин", "v1_id": 5, "v2_id": 7, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 44, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Кожа", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 5, "v2_id": 7, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 45, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Кожа", "v3": "Сребърен", "v1_id": 5, "v2_id": 7, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 46, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 5, "v2_id": 8, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 47, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON>ин", "v1_id": 5, "v2_id": 8, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 48, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 5, "v2_id": 8, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 49, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Па<PERSON><PERSON>к", "v3": "Сребърен", "v1_id": 5, "v2_id": 8, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 50, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Вел<PERSON>р", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 5, "v2_id": 9, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 51, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Вел<PERSON>р", "v3": "<PERSON>ин", "v1_id": 5, "v2_id": 9, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 52, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Вел<PERSON>р", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 5, "v2_id": 9, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 53, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Вел<PERSON>р", "v3": "Сребърен", "v1_id": 5, "v2_id": 9, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 54, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Кадифе", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 5, "v2_id": 12, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 55, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Кадифе", "v3": "<PERSON>ин", "v1_id": 5, "v2_id": 12, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 56, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Кадифе", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 5, "v2_id": 12, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 57, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": "Кадифе", "v3": "Сребърен", "v1_id": 5, "v2_id": 12, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 58, "v1": "<PERSON>ин", "v2": "Кожа", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 6, "v2_id": 7, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 59, "v1": "<PERSON>ин", "v2": "Кожа", "v3": "<PERSON>ин", "v1_id": 6, "v2_id": 7, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 60, "v1": "<PERSON>ин", "v2": "Кожа", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 6, "v2_id": 7, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 61, "v1": "<PERSON>ин", "v2": "Кожа", "v3": "Сребърен", "v1_id": 6, "v2_id": 7, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 62, "v1": "<PERSON>ин", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 6, "v2_id": 8, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 63, "v1": "<PERSON>ин", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON>ин", "v1_id": 6, "v2_id": 8, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 64, "v1": "<PERSON>ин", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 6, "v2_id": 8, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 65, "v1": "<PERSON>ин", "v2": "Па<PERSON><PERSON>к", "v3": "Сребърен", "v1_id": 6, "v2_id": 8, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 66, "v1": "<PERSON>ин", "v2": "Вел<PERSON>р", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 6, "v2_id": 9, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 67, "v1": "<PERSON>ин", "v2": "Вел<PERSON>р", "v3": "<PERSON>ин", "v1_id": 6, "v2_id": 9, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 68, "v1": "<PERSON>ин", "v2": "Вел<PERSON>р", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 6, "v2_id": 9, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 69, "v1": "<PERSON>ин", "v2": "Вел<PERSON>р", "v3": "Сребърен", "v1_id": 6, "v2_id": 9, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 70, "v1": "<PERSON>ин", "v2": "Кадифе", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 6, "v2_id": 12, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 71, "v1": "<PERSON>ин", "v2": "Кадифе", "v3": "<PERSON>ин", "v1_id": 6, "v2_id": 12, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 72, "v1": "<PERSON>ин", "v2": "Кадифе", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 6, "v2_id": 12, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 73, "v1": "<PERSON>ин", "v2": "Кадифе", "v3": "Сребърен", "v1_id": 6, "v2_id": 12, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 106, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Кожа", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 17, "v2_id": 7, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": null, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 107, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Кожа", "v3": "<PERSON>ин", "v1_id": 17, "v2_id": 7, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": null, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 108, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Кожа", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 17, "v2_id": 7, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": null, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 109, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Кожа", "v3": "Сребърен", "v1_id": 17, "v2_id": 7, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": null, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 110, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 17, "v2_id": 8, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 111, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON>ин", "v1_id": 17, "v2_id": 8, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 112, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Па<PERSON><PERSON>к", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 17, "v2_id": 8, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 113, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Па<PERSON><PERSON>к", "v3": "Сребърен", "v1_id": 17, "v2_id": 8, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 114, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Вел<PERSON>р", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 17, "v2_id": 9, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": null, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 115, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Вел<PERSON>р", "v3": "<PERSON>ин", "v1_id": 17, "v2_id": 9, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": null, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 116, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Вел<PERSON>р", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 17, "v2_id": 9, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": null, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 117, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Вел<PERSON>р", "v3": "Сребърен", "v1_id": 17, "v2_id": 9, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": null, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 118, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Кадифе", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v1_id": 17, "v2_id": 12, "v3_id": 13, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 119, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Кадифе", "v3": "<PERSON>ин", "v1_id": 17, "v2_id": 12, "v3_id": 14, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 120, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Кадифе", "v3": "<PERSON><PERSON><PERSON><PERSON><PERSON>н", "v1_id": 17, "v2_id": 12, "v3_id": 15, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 121, "v1": "<PERSON><PERSON><PERSON><PERSON>", "v2": "Кадифе", "v3": "Сребърен", "v1_id": 17, "v2_id": 12, "v3_id": 16, "quantity": null, "sku": "", "barcode": "", "price": 75000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 5, "name": "Маслено зелено кадифено кресло", "parent_id": 3, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "masleno-zeleno-kadifeno-kreslo-image_6184627a17b78.jpeg", "background": "#2F4F4F", "width": 5738, "height": 3287, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 25, "color_id": 38, "percents": 55, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 26, "color_id": 30, "percents": 17, "R": 85, "G": 107, "B": 47, "hex": "#556B2F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 27, "color_id": 124, "percents": 11, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 28, "color_id": 44, "percents": 6, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 560258}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 7, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 8, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 9, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 105, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 41}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 106, "parameter_id": 3, "parameter_option_id": 7, "variant_id": 109}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 107, "parameter_id": 4, "parameter_option_id": 13, "variant_id": 118}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 108, "parameter_id": 4, "parameter_option_id": 14, "variant_id": 119}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 109, "parameter_id": 4, "parameter_option_id": 15, "variant_id": 120}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 110, "parameter_id": 4, "parameter_option_id": 16, "variant_id": 121}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 111, "parameter_id": 3, "parameter_option_id": 8, "variant_id": 113}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 112, "parameter_id": 3, "parameter_option_id": 9, "variant_id": 117}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 113, "parameter_id": 3, "parameter_option_id": 12, "variant_id": 121}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 114, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 57}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 115, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 73}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 116, "parameter_id": 2, "parameter_option_id": 17, "variant_id": 121}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 4, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-14", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 14, "vendor_id": null, "image_id": 6, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 65000, "price_to": 65000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 8, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 8, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 65000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 6, "name": "Жълто кресло с текстилна тапицерия и дървена основа", "parent_id": 4, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "zalto-kreslo-s-tekstilna-tapiceria-i-darvena-osnova-image_61846323307de.jpeg", "background": "#696969", "width": 4800, "height": 2700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 29, "color_id": 44, "percents": 39, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 30, "color_id": 134, "percents": 13, "R": 112, "G": 128, "B": 144, "hex": "#708090"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 31, "color_id": 38, "percents": 12, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 32, "color_id": 80, "percents": 12, "R": 119, "G": 136, "B": 153, "hex": "#778899"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 33, "color_id": 138, "percents": 11, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 347132}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 10, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 11, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 12, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 5, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-13", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 7, "vendor_id": null, "image_id": 9, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 98000, "price_to": 98000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 10, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 10, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 98000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 9, "name": "90/60 Бежова холна маса от естествено дърво", "parent_id": 5, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "9060-bezova-holna-masa-ot-estestveno-darvo-image_61846d7f8c6f5.jpeg", "background": "#696969", "width": 3520, "height": 2017, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 43, "color_id": 44, "percents": 49, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 44, "color_id": 38, "percents": 21, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 45, "color_id": 55, "percents": 9, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 46, "color_id": 130, "percents": 6, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 47, "color_id": 26, "percents": 6, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 609816}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 13, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 14, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 15, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 6, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-12", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 7, "vendor_id": null, "image_id": 8, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 48000, "price_to": 48000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 12, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 12, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 48000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 8, "name": "50/50 Квад<PERSON><PERSON><PERSON>на холна маса от бяло дърво", "parent_id": 6, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "5050-kva<PERSON><PERSON>na-holna-masa-ot-balo-darvo-image_61846c6117c07.jpeg", "background": "#2F4F4F", "width": 4000, "height": 2250, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 39, "color_id": 38, "percents": 40, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 40, "color_id": 44, "percents": 14, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 41, "color_id": 55, "percents": 7, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 42, "color_id": 26, "percents": 6, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 933826}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 16, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 17, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 18, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 7, "url_handle": "lenena-vazglavnica-za-divan-4040", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 6, "vendor_id": null, "image_id": 10, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 3500, "price_to": 3500, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 14, "short_description": "Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон. ", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 14, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 3500, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 10, "name": "Ленена възглавница за диван 40/40", "parent_id": 7, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "lenena-vazglavnica-za-divan-4040-image_61882da3515e5.jpeg", "background": "#D2B48C", "width": 5000, "height": 3334, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 48, "color_id": 138, "percents": 56, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 49, "color_id": 2, "percents": 20, "R": 250, "G": 235, "B": 215, "hex": "#FAEBD7"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 50, "color_id": 55, "percents": 6, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 51, "color_id": 144, "percents": 6, "R": 245, "G": 222, "B": 179, "hex": "#F5DEB3"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 1143348}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 19, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 20, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 21, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 8, "url_handle": "pamuchna-vazglavnica-za-divan-4040sm", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 6, "vendor_id": null, "image_id": 11, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 3500, "price_to": 3500, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 16, "short_description": "Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон. ", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 16, "v1": "Бежово", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 3500, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 17, "v1": "Розово", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 3500, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 11, "name": "Памучна възглавница за диван 40/40см", "parent_id": 8, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "pamucna-vazglavnica-za-divan-4040sm-image_61882e244330c.jpeg", "background": "#D2B48C", "width": 5000, "height": 3334, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 52, "color_id": 138, "percents": 37, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 53, "color_id": 44, "percents": 18, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 54, "color_id": 55, "percents": 18, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 55, "color_id": 26, "percents": 11, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 56, "color_id": 130, "percents": 7, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 1245262}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 13, "name": "Памучна възглавница за диван 40/40см", "parent_id": 8, "sort_order": 2, "active": "yes", "max_thumb_size": 1920, "image_id": "pamucna-vazglavnica-za-divan-4040sm-image_61882e5268522.jpeg", "background": "#A9A9A9", "width": 5000, "height": 3333, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 57, "color_id": 26, "percents": 26, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 58, "color_id": 130, "percents": 21, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 59, "color_id": 55, "percents": 18, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 60, "color_id": 44, "percents": 14, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 61, "color_id": 122, "percents": 7, "R": 188, "G": 143, "B": 143, "hex": "#BC8F8F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 1361062}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 22, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 23, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 24, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 91, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 16}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 92, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 17}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 9, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-11", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 15, "vendor_id": null, "image_id": 14, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": null, "price_to": null, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 18, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 18, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": null, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 14, "name": "Бежови памучни завеси 2200х2500мм", "parent_id": 9, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "bezovi-pamucni-zavesi-2200h2500mm-image_618833461747e.jpeg", "background": "#A9A9A9", "width": 5760, "height": 3840, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 62, "color_id": 26, "percents": 26, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 63, "color_id": 44, "percents": 22, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 64, "color_id": 130, "percents": 11, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 65, "color_id": 55, "percents": 11, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 66, "color_id": 73, "percents": 7, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 67, "color_id": 51, "percents": 7, "R": 248, "G": 248, "B": 255, "hex": "#F8F8FF"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 627861}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 64, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 65, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 66, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 10, "url_handle": "bejovi-pamuchni-zavesi-2200h2500mm-1", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 13, "vendor_id": null, "image_id": 15, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 23000, "price_to": 23000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 19, "short_description": "Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон. ", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 19, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 23000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 15, "name": "Бежови памучни завеси 2200х2500мм", "parent_id": 10, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "bezovi-pamucni-zavesi-2200h2500mm-image_6188337d4512d.jpeg", "background": "#A9A9A9", "width": 5760, "height": 3840, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 68, "color_id": 26, "percents": 26, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 69, "color_id": 44, "percents": 22, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 70, "color_id": 130, "percents": 11, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 71, "color_id": 55, "percents": 11, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 72, "color_id": 73, "percents": 7, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 73, "color_id": 51, "percents": 7, "R": 248, "G": 248, "B": 255, "hex": "#F8F8FF"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 627861}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 25, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 26, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 27, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 11, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-10", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 13, "vendor_id": null, "image_id": 16, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 18000, "price_to": 18000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 21, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 21, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 18000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 16, "name": "Бели памучни завеси 1800х2500мм", "parent_id": 11, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "beli-pamucni-zavesi-1800h2500mm-image_618833f3bd46b.jpeg", "background": "#F5F5F5", "width": 4200, "height": 2923, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 74, "color_id": 146, "percents": 47, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 75, "color_id": 50, "percents": 18, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 76, "color_id": 38, "percents": 6, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 817122}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 28, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 29, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 30, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 12, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-9", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 8, "vendor_id": null, "image_id": 17, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 125000, "price_to": 125000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 23, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 23, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 125000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 17, "name": "Трапезна маса", "parent_id": 12, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "trapezna-masa-image_6188344535c6c.jpeg", "background": "#696969", "width": 4000, "height": 2292, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 77, "color_id": 44, "percents": 41, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 78, "color_id": 38, "percents": 15, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 79, "color_id": 55, "percents": 10, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 80, "color_id": 80, "percents": 7, "R": 119, "G": 136, "B": 153, "hex": "#778899"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 81, "color_id": 134, "percents": 7, "R": 112, "G": 128, "B": 144, "hex": "#708090"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 82, "color_id": 138, "percents": 6, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 83, "color_id": 26, "percents": 6, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 618338}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 31, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 32, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 33, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 13, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-8", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 8, "vendor_id": null, "image_id": 18, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 230000, "price_to": 230000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 25, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 25, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 230000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 18, "name": "Tъмна трапезна маса 2000х900мм", "parent_id": 13, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "tamna-trapezna-masa-2000h900mm-image_618835bcaa6ae.jpeg", "background": "#696969", "width": 5000, "height": 2865, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 84, "color_id": 44, "percents": 45, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 85, "color_id": 138, "percents": 22, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 86, "color_id": 38, "percents": 11, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 87, "color_id": 55, "percents": 10, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 88, "color_id": 134, "percents": 5, "R": 112, "G": 128, "B": 144, "hex": "#708090"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 544902}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 34, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 35, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 36, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 14, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-7", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 9, "vendor_id": null, "image_id": 19, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 43000, "price_to": 43000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 123, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 123, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 43000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 19, "name": "Трапезен стол от метал и дърво с контрастна възглавница", "parent_id": 14, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "trapezen-stol-ot-metal-i-darvo-s-kontrastna-vazglavnica-image_61899281343ec.jpeg", "background": "#696969", "width": 4200, "height": 2800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 89, "color_id": 44, "percents": 63, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 90, "color_id": 38, "percents": 20, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 422730}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 37, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 38, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 39, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 15, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-6", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 9, "vendor_id": null, "image_id": 20, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 39000, "price_to": 39000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 125, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 125, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 39000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 20, "name": "Тъмен трапезен стол с  тапицирана възглавница", "parent_id": 15, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "tamen-trapezen-stol-s-tapicirana-vazglavnica-image_618992d665961.jpeg", "background": "#2F4F4F", "width": 3806, "height": 2538, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 91, "color_id": 38, "percents": 55, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 92, "color_id": 44, "percents": 39, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 379765}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 40, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 41, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 42, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 16, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-5", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 10, "vendor_id": null, "image_id": 21, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 27000, "price_to": 27000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 128, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 128, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 27000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 21, "name": "Дървен бар стол", "parent_id": 16, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "darven-bar-stol-image_618994a2e6d12.jpeg", "background": "#8B4513", "width": 4200, "height": 2800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 93, "color_id": 124, "percents": 32, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 94, "color_id": 44, "percents": 29, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 95, "color_id": 55, "percents": 13, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 96, "color_id": 138, "percents": 11, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 97, "color_id": 122, "percents": 8, "R": 188, "G": 143, "B": 143, "hex": "#BC8F8F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 427277}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 43, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 44, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 45, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 17, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-4", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 10, "vendor_id": null, "image_id": 22, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 29000, "price_to": 29000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 130, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 130, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 29000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 22, "name": "Бар стол от дърво и сива кожа", "parent_id": 17, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "bar-stol-ot-darvo-i-siva-koza-image_618994e5c61e6.jpeg", "background": "#DCDCDC", "width": 4256, "height": 2832, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 98, "color_id": 50, "percents": 24, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 99, "color_id": 73, "percents": 22, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 100, "color_id": 130, "percents": 20, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 101, "color_id": 146, "percents": 17, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 102, "color_id": 26, "percents": 10, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 329079}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 46, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 47, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 48, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 18, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-3", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 11, "vendor_id": null, "image_id": 29, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 85000, "price_to": 85000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 132, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 132, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 85000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 29, "name": "Изчистен масивен полилей с приглушена светлина", "parent_id": 18, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "izcisten-masiven-polilej-s-p<PERSON><PERSON><PERSON>-svet<PERSON>-image_6189a76a85bbc.jpeg", "background": "#696969", "width": 4500, "height": 2767, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 129, "color_id": 44, "percents": 35, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 130, "color_id": 38, "percents": 10, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 131, "color_id": 138, "percents": 8, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 132, "color_id": 55, "percents": 7, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 133, "color_id": 124, "percents": 7, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 937017}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 49, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 50, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 51, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 19, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-2", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 11, "vendor_id": null, "image_id": 28, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 21000, "price_to": 21000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 133, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 133, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 21000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 28, "name": "Единичен полилей от стъкло и метал", "parent_id": 19, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "edinicen-polilej-ot-staklo-i-metal-image_6189a73e7e64b.jpeg", "background": "#191970", "width": 6000, "height": 3689, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 125, "color_id": 97, "percents": 28, "R": 25, "G": 25, "B": 112, "hex": "#191970"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 126, "color_id": 38, "percents": 22, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 127, "color_id": 14, "percents": 10, "R": 95, "G": 158, "B": 160, "hex": "#5F9EA0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 128, "color_id": 139, "percents": 8, "R": 0, "G": 128, "B": 128, "hex": "#008080"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 598719}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 61, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 62, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 63, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 21, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-1", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 12, "vendor_id": null, "image_id": 25, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 29000, "price_to": 29000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 137, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 137, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 29000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 25, "name": "З<PERSON>атна висока лампа за хол или трапезария", "parent_id": 21, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "zlatna-visoka-lampa-za-hol-ili-trapezaria-image_6189a52ae328f.jpeg", "background": "#778899", "width": 5690, "height": 3455, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 111, "color_id": 80, "percents": 39, "R": 119, "G": 136, "B": 153, "hex": "#778899"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 112, "color_id": 81, "percents": 11, "R": 176, "G": 196, "B": 222, "hex": "#B0C4DE"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 113, "color_id": 134, "percents": 11, "R": 112, "G": 128, "B": 144, "hex": "#708090"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 114, "color_id": 2, "percents": 9, "R": 250, "G": 235, "B": 215, "hex": "#FAEBD7"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 115, "color_id": 85, "percents": 6, "R": 250, "G": 240, "B": 230, "hex": "#FAF0E6"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 1234076}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 55, "parameter": "width", "value": "290"}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 56, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 57, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 22, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 12, "vendor_id": null, "image_id": 27, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 27000, "price_to": 27000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 139, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 139, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 27000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 27, "name": "Гра<PERSON>и<PERSON><PERSON>на висока лампа от метал", "parent_id": 22, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "grafitena-visoka-lampa-ot-metal-image_6189a6d5ed13e.jpeg", "background": "#DCDCDC", "width": 5000, "height": 3074, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 121, "color_id": 50, "percents": 28, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 122, "color_id": 73, "percents": 22, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 123, "color_id": 26, "percents": 19, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 124, "color_id": 130, "percents": 17, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 377403}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 58, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 59, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 60, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}]