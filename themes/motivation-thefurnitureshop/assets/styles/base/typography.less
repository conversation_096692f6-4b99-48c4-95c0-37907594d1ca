/*=============================================================================*\
    TYPOGRAPHY
\*=============================================================================*/

/*  HEADINGS
-------------------------------------------------------------------------------*/


h1, ._h1,
h2, ._h2,
h3, ._h3,
h4, ._h4,
h5, ._h5,
h6, ._h6 {
	font-family: @font-family-titles; /* theme */
	font-weight: @font-weight-titles; /* theme */
	font-style: @font-style-titles; /* theme */
	color: @color-main-titles; /* theme */
	line-height: 1.1;
}

h1, ._h1 {
	font-size: @font-size-heading-1; /* theme */
}
h2, ._h2 {
	font-size: @font-size-heading-2; /* theme */
}
h3, ._h3 {
	font-size: @font-size-heading-3; /* theme */
}
h4, ._h4 {
	font-size: @font-size-heading-4; /* theme */
}
h5, ._h5 {
	font-size: @font-size-heading-5; /* theme */
}
h6, ._h6 {
	font-size: @font-size-heading-6; /* theme */
}

/*  PARAGRAPHS
-------------------------------------------------------------------------------*/

p {}

/*  LISTS
-------------------------------------------------------------------------------*/

/*  Unordered */

ul {}

/*  Ordered */

ol {}

/*  Description */

dl {
	dt {}
	dd {}
}

/*  TEXT-LEVEL SEMANTICS
-------------------------------------------------------------------------------*/

a {}
em {}
strong {}
del {}
small {}

/*  HORIZONTAL RULE
-------------------------------------------------------------------------------*/

hr {
	border-color: @color-main-borders; /* theme */
}

/*  BLOCKQUOTES
-------------------------------------------------------------------------------*/

blockquote {
	font-size: @font-size-main; /* theme */

	:before {
		content: '\201E';
	}

	:after {
		content: '\201D';
	}

	p {
		font-style: italic;
	}
}

/*  CODE BLOCKS
-------------------------------------------------------------------------------*/

pre {
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	code {}
}

/*  TEXTBOX
-------------------------------------------------------------------------------*/

._text,
._textbox {
	word-break: break-word;

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		margin-bottom: 5px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	p,
	ul,
	ol,
	.textbox-iframe,
	.table-responsive {
		margin-bottom: 20px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	a {
		@media @hover {
			&:hover {
				text-decoration: underline;
			}

			&._button {
				&:hover {
					text-decoration: none;
				}
			}
		}
	}

	ul {
		list-style-type: square;

		li {
			margin-bottom: 10px;
		}
	}

	ul,
	ol {
		padding-left: 20px;
	}

	blockquote {
		font-style: italic;
		font-size: calc(@font-size-main ~'+' 2px); /* theme */
		position: relative;
		overflow: hidden;
		padding: 10px 30px;
		margin-bottom: 25px;

		&:last-child {
			margin-bottom: 0;
		}

		&:before {
			content: '"';
			position: absolute;
			top: 10px;
			left: 0;
			font-size: 40px;
			line-height: 1;
		}
	}

	img {
		max-width: 100%;
		height: auto;
	}

	table {
		border-color: @color-main-borders; /* theme */
		max-width: 100%;
	}

	th,
	td {
		border-color: @color-main-borders; /* theme */
		border-collapse: collapse;
		padding: 2px 8px;
	}

    .aligncenter {
        display: block;
        max-width: 100%;
        margin: 0 auto;
    }

	.alignleft {
		float: left;
		margin-top: 5px;
		margin-bottom: 5px;
		margin-right: 20px;
	}

	.alignright {
		float: right;
		margin-top: 5px;
		margin-bottom: 5px;
		margin-left: 20px;
	}

    .text-aligncenter {
        text-align: center;
    }

    .text-alignleft {
        text-align: left;
    }

    .text-alignright {
        text-align: right;
    }

    .text-alignjustify {
        text-align: justify;
    }

    .image-popup {
    	cursor: pointer;
    }

    .table-break {
    	table-layout: fixed;
    	width: 100%;

		th,
		td {
			padding: 0 10px;

			&:first-child {
				padding-left: 0;
			}

			&:last-child {
				padding-right: 0;
			}
		}

    	@media (max-width: @screen-xs-max) {
    		thead,
    		tbody,
    		tfoot,
    		tr,
    		th,
    		td {
    			display: block;
    		}

    		th,
    		td {
    			&:first-child,
    			&:last-child {
    				padding: 0;
    			}
    		}

    		td {
    			margin-bottom: 25px;
    		}

    		tr {
    			&:last-child {
    				td {
    					&:last-child {
    						margin-bottom: 0;
    					}
    				}
    			}
    		}
    	}
    }
}

.textbox-iframe {
	height: 0;
	padding-bottom: 56.25%;
	position: relative;

	video,
	iframe {
		border: 0;
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
	}
}

sub, sup {
	font-size: 60%;
}