/*=============================================================================*\
    CATEGORIES MENU
\*=============================================================================*/

@menu-horizontal-offsets: 20px;
@menu-vertical-offsets: 19px;
@menu-vertical-offsets-bottom: @menu-vertical-offsets - 2px;
@menu-line-height: @line-height-base;
@menu-font-size: @font-size-main;
@menu-dropdown-width: 253px;
@menu-dropdown-width-small: 203px;
@menu-dropdown-items-offset: 10px;
@menu-dropdown-border-width: 1px;
@menu-dropdown-horizontal-offsets: @menu-horizontal-offsets + 10px;

._categories-menu {
	display: table-cell;
	vertical-align: middle;
	width: 262.5px;
	min-height: 50px;
	position: relative;

	&.open {
		._categories-menu-dropdown {
			opacity: 1;
			visibility: visible;
		}
	}
}

._categories-menu-button {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: @color-dropdowns-background; /* theme */
	color: @color-dropdowns-text; /* theme */
	width: 100%;
	height: 100%;
	padding: 2px @menu-horizontal-offsets 0;
	font-weight: bold;
	line-height: 1;
	position: absolute;
	top: 0;
	left: 0;
	.uppercase();
}

._categories-menu-dropdown {
	background-color: @color-dropdowns-background; /* theme */
	border: 1px solid;
	border-color: @color-dropdowns-borders; /* theme */
	padding: 0 @menu-horizontal-offsets;
	font-size: @menu-font-size;
	line-height: @menu-line-height;
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	opacity: 0;
	visibility: hidden;
	.uppercase();

	&.hovered {
		border-bottom-right-radius: 0;
	}

	._filter-categories-list {
		> ul {
			> li {
				border-top: 1px solid;
				border-top-color: @color-dropdowns-borders; /* theme */

				@media @hover {
					&:hover {
						> a,
						> ._collapse {
							color: @color-dropdowns-highlight; /* theme */
						}

						> ul {
							opacity: 1;
							visibility: visible;
						}
					}
				}
				
				&:first-child {
					border-top: 0;
				}

				&.item-collapse {
					> a {
						position: relative;
						padding-right: 2*@menu-horizontal-offsets;

						&:before {
							content:"\f067";
							font-family: @font-awesome;
							font-size: 10px;
							line-height: @menu-font-size*@menu-line-height;
							position: absolute;
							top: @menu-vertical-offsets;
							right: @menu-horizontal-offsets;
							z-index: 1;
						}
					}
				}

				> a {
					display: block;
					margin: 0 -@menu-horizontal-offsets;
					padding: @menu-vertical-offsets @menu-horizontal-offsets @menu-vertical-offsets-bottom;
					color: @color-dropdowns-text; /* theme */
				}

				> ul {
					background-color: @color-dropdowns-background; /* theme */
					border: @menu-dropdown-border-width solid;
					border-color: @color-dropdowns-borders; /* theme */
					width: @menu-dropdown-width + @menu-dropdown-horizontal-offsets*2 + @menu-dropdown-border-width*2;
					height: calc(100% ~'+' @menu-dropdown-border-width*2);
					min-height: calc(100% ~'+' @menu-dropdown-border-width*2);
					padding: @menu-vertical-offsets @menu-dropdown-horizontal-offsets (@menu-vertical-offsets-bottom - @menu-dropdown-items-offset);
					font-size: 0;
					position: absolute;
					top: -@menu-dropdown-border-width;
					left: 100%;
					overflow: hidden;
					opacity: 0;
					visibility: hidden;
					
					&:before {
						content: '';
						background-color: @color-dropdowns-background; /* theme */
						position: absolute;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						z-index: 0;
					}

					&:after {
						content:"\f110";
						width: 30px;
						height: 30px;
						line-height: 30px;
						font-family: @font-awesome;
						font-size: 20px;
						text-align: center;
						color: @color-dropdowns-highlight; /* theme */
						animation: button-spin 1.5s linear infinite;
						position: absolute;
						top: 50%;
						left: 50%;
						margin-top: -15px;
						margin-left: -15px;
						z-index: 1;
					}

					&.loaded {
						&:before,
						&:after {
							display: none;
						}
					}

					/* &.width-2,
					&.width-3 {
						display: flex;
						flex-direction: column;
						flex-wrap: wrap;
						align-content: flex-start;
						padding-right: 0;

						> li {
							margin-right: @menu-dropdown-horizontal-offsets;
						}
					} */

					&.width-2 {
						width: @menu-dropdown-width*2 + @menu-dropdown-horizontal-offsets*3 + @menu-dropdown-border-width*2;
						column-count: 2;
						column-gap: @menu-dropdown-horizontal-offsets;
					}

					&.width-3 {
						width: @menu-dropdown-width*3 + @menu-dropdown-horizontal-offsets*4 + @menu-dropdown-border-width*2;
						column-count: 3;
						column-gap: @menu-dropdown-horizontal-offsets;
					}

					> li {
						display: inline-block;
						width: @menu-dropdown-width;
						padding-bottom: @menu-dropdown-items-offset;
						font-size: 13px;
					}

					ul {
						padding-left: 15px;
					}
				}
			}
		}

		ul {
			list-style-type: none;

			ul {
				._collapse {
					display: none;
				}

				a {
					color: @color-dropdowns-text; /* theme */

					@media @hover {
						&:hover {
							color: @color-dropdowns-highlight; /* theme */
						}
					}
				}

				ul {
					text-transform: initial;

					a {
						color: @color-dropdowns-meta-text; /* theme */
					}
				}
			}
		}
	}

	._filter-category-list {
		._collapse {
			display: none;
		}
	}
}