[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 1, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-7", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и инструкции за ползване/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 2, "vendor_id": null, "image_id": 1, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 749, "price_to": 749, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 2, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 2, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 749, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 1, "name": "ХАСТАРНА МАЗИЛКА ЗА ВЪТРЕШНО ПРИЛОЖЕНИЕ WEBER 201P", "parent_id": 1, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "hastarna-mazilka-za-vatresno-prilozenie-weber-201p-image_61dd6605c5422.jpeg", "background": "#FFFF00", "width": 915, "height": 915, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 1, "color_id": 147, "percents": 20, "R": 255, "G": 255, "B": 0, "hex": "#FFFF00"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 2, "color_id": 55, "percents": 16, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 3, "color_id": 28, "percents": 12, "R": 189, "G": 183, "B": 107, "hex": "#BDB76B"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 4, "color_id": 146, "percents": 12, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 5, "color_id": 63, "percents": 7, "R": 240, "G": 230, "B": 140, "hex": "#F0E68C"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 271205}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 1, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 2, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 3, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-6", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и инструкции за ползване/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 2, "vendor_id": null, "image_id": 2, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 1049, "price_to": 1049, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 4, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 4, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 1049, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 2, "name": "БЪРЗОСВЪРЗВАЩ ЦИМЕНТ CERESIT CX 5", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "barzosvarzvas-ciment-ceresit-cx-5-image_61dd662819f45.jpeg", "background": "#F5F5F5", "width": 815, "height": 815, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 6, "color_id": 146, "percents": 17, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 7, "color_id": 50, "percents": 13, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 8, "color_id": 52, "percents": 12, "R": 255, "G": 215, "B": 0, "hex": "#FFD700"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 9, "color_id": 147, "percents": 9, "R": 255, "G": 255, "B": 0, "hex": "#FFFF00"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 10, "color_id": 28, "percents": 7, "R": 189, "G": 183, "B": 107, "hex": "#BDB76B"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 11, "color_id": 53, "percents": 6, "R": 218, "G": 165, "B": 32, "hex": "#DAA520"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 12, "color_id": 73, "percents": 5, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 197606}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 3, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-5", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и инструкции за ползване/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 4, "vendor_id": null, "image_id": 3, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 1149, "price_to": 1149, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 6, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 6, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 1149, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 3, "name": "ТЕРАКОТ KAI КАТАНИЯ", "parent_id": 3, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "terakot-kai-katania-image_61dd6672dbd0e.jpeg", "background": "#FDF5E6", "width": 815, "height": 815, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 13, "color_id": 103, "percents": 85, "R": 253, "G": 245, "B": 230, "hex": "#FDF5E6"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 14, "color_id": 2, "percents": 14, "R": 250, "G": 235, "B": 215, "hex": "#FAEBD7"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 154883}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 7, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 8, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 9, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 4, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-4", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и инструкции за ползване/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 4, "vendor_id": null, "image_id": 4, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 1049, "price_to": 1049, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 8, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 8, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 1049, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 4, "name": "ТЕРАКОТ KAI МАРБЕЛА БЕЖОВ", "parent_id": 4, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "terakot-kai-ma<PERSON><PERSON>-bezov-image_61dd669bf0571.jpeg", "background": "#FFDAB9", "width": 815, "height": 815, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 15, "color_id": 114, "percents": 56, "R": 255, "G": 218, "B": 185, "hex": "#FFDAB9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 16, "color_id": 138, "percents": 37, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 17, "color_id": 7, "percents": 6, "R": 255, "G": 228, "B": 196, "hex": "#FFE4C4"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 449825}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 10, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 11, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 12, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 5, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-3", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и инструкции за ползване/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": null, "image_id": 7, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 37900, "price_to": 37900, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 10, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 10, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 37900, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 7, "name": "АКУМУЛАТОРЕН КОМПЛЕКТ DEWALT DCK211C2", "parent_id": 5, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "akumulatoren-komplekt-dewalt-dck211c2-image_61dd66d84d034.jpeg", "background": "#696969", "width": 815, "height": 815, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 29, "color_id": 44, "percents": 14, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 30, "color_id": 53, "percents": 12, "R": 218, "G": 165, "B": 32, "hex": "#DAA520"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 31, "color_id": 28, "percents": 9, "R": 189, "G": 183, "B": 107, "hex": "#BDB76B"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 32, "color_id": 38, "percents": 9, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 33, "color_id": 146, "percents": 7, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 34, "color_id": 55, "percents": 5, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 35, "color_id": 63, "percents": 5, "R": 240, "G": 230, "B": 140, "hex": "#F0E68C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 36, "color_id": 52, "percents": 5, "R": 255, "G": 215, "B": 0, "hex": "#FFD700"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 277953}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 6, "name": "АКУМУЛАТОРЕН КОМПЛЕКТ DEWALT DCK211C2", "parent_id": 5, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "akumulatoren-komplekt-dewalt-dck211c2-image_61dd66d53ed55.jpeg", "background": "#696969", "width": 815, "height": 815, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 24, "color_id": 44, "percents": 20, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 25, "color_id": 52, "percents": 12, "R": 255, "G": 215, "B": 0, "hex": "#FFD700"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 26, "color_id": 63, "percents": 9, "R": 240, "G": 230, "B": 140, "hex": "#F0E68C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 27, "color_id": 55, "percents": 7, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 28, "color_id": 38, "percents": 5, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 225701}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 5, "name": "АКУМУЛАТОРЕН КОМПЛЕКТ DEWALT DCK211C2", "parent_id": 5, "sort_order": 3, "active": "yes", "max_thumb_size": 1280, "image_id": "akumulatoren-komplekt-dewalt-dck211c2-image_61dd66d3b3572.jpeg", "background": "#696969", "width": 815, "height": 815, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 18, "color_id": 44, "percents": 17, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 19, "color_id": 53, "percents": 7, "R": 218, "G": 165, "B": 32, "hex": "#DAA520"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 20, "color_id": 55, "percents": 7, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 21, "color_id": 38, "percents": 6, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 22, "color_id": 52, "percents": 6, "R": 255, "G": 215, "B": 0, "hex": "#FFD700"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 23, "color_id": 124, "percents": 5, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 204739}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 13, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 14, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 15, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 6, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-2", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и инструкции за ползване/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": null, "image_id": 8, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 59900, "price_to": 59900, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 12, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 12, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 59900, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 8, "name": "АКУМУЛАТОРЕН КОМПЛЕКТ БЕЗЧЕТКОВ DEWALT DCK2060D2T-QW", "parent_id": 6, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "akumulatoren-komplekt-bezcetkov-dewalt-dck2060d2t-qw-image_61dd67022140e.jpeg", "background": "#696969", "width": 915, "height": 915, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 37, "color_id": 44, "percents": 20, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 38, "color_id": 38, "percents": 9, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 39, "color_id": 55, "percents": 6, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 245262}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 16, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 17, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 18, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 7, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-1", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и инструкции за ползване/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 3, "vendor_id": null, "image_id": 9, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 54900, "price_to": 54900, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 14, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 14, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 54900, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 9, "name": "РАЗТЕГАТЕЛНА ТРАПЕЗНА МАСА LILOVI КАЛИФОРНИЯ ЗАФИРА", "parent_id": 7, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "raztegatelna-trapezna-masa-lilovi-kalifornia-zafira-image_61dd678235005.jpeg", "background": "#A0522D", "width": 915, "height": 915, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 46, "color_id": 129, "percents": 19, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 47, "color_id": 124, "percents": 16, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 48, "color_id": 34, "percents": 14, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 49, "color_id": 138, "percents": 11, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 50, "color_id": 47, "percents": 9, "R": 255, "G": 250, "B": 240, "hex": "#FFFAF0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 51, "color_id": 122, "percents": 6, "R": 188, "G": 143, "B": 143, "hex": "#BC8F8F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 100757}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 10, "name": "РАЗТЕГАТЕЛНА ТРАПЕЗНА МАСА LILOVI КАЛИФОРНИЯ ЗАФИРА", "parent_id": 7, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "raztegatelna-trapezna-masa-lilovi-kalifornia-zafira-image_61dd678c6c23e.jpeg", "background": "#8B4513", "width": 915, "height": 915, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 40, "color_id": 124, "percents": 23, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 41, "color_id": 129, "percents": 13, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 42, "color_id": 34, "percents": 13, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 43, "color_id": 138, "percents": 7, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 44, "color_id": 55, "percents": 6, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 45, "color_id": 130, "percents": 5, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 120231}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 19, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 20, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 21, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 8, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и инструкции за ползване/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 3, "vendor_id": null, "image_id": 11, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 3900, "price_to": 3900, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 16, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 16, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 3900, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 11, "name": "СГЪВАЕМ ДЪРВЕН СТОЛ FLEX ОРЕХ 42,5Х47,5Х79 СМ.", "parent_id": 8, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "sgavaem-darven-stol-flex-oreh-425h475h79-sm-image_61dd67a834953.jpeg", "background": "#8B4513", "width": 915, "height": 915, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 52, "color_id": 124, "percents": 24, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 53, "color_id": 146, "percents": 13, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 54, "color_id": 50, "percents": 9, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 55, "color_id": 47, "percents": 9, "R": 255, "G": 250, "B": 240, "hex": "#FFFAF0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 154826}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 22, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 23, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 24, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}]