[{"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 1, "mapping": "main", "name": "Main menu", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 4, "parent_id": null, "order": 3, "type": "section", "link_type": null, "link_id": null, "route": "blog.list", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Blog"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 11, "parent_id": null, "order": 1, "type": "category", "link_type": "category", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Αξεσ<PERSON><PERSON><PERSON><PERSON> γάμου"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 12, "parent_id": 11, "order": 1, "type": "category", "link_type": "category", "link_id": 2, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Τελετουργικά"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 13, "parent_id": 11, "order": 2, "type": "category", "link_type": "category", "link_id": 3, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Σετ με αξεσουάρ"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 14, "parent_id": 11, "order": 3, "type": "category", "link_type": "category", "link_id": 5, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Ποτήρια και διακόσμηση σαμπάνιας"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 15, "parent_id": 11, "order": 4, "type": "category", "link_type": "category", "link_id": 6, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Κουμπώματα και βραχιόλια για τις παρανύμφους"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 16, "parent_id": 11, "order": 5, "type": "category", "link_type": "category", "link_id": 7, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Δαχτυλίδια γάμου, μαξιλάρια και κουτιά"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 17, "parent_id": null, "order": 2, "type": "category", "link_type": "category", "link_id": 4, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "βάπτιση"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 27, "parent_id": null, "order": 4, "type": "section", "link_type": null, "link_id": null, "route": "contacts", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Επικοινωνία"}, "relations": []}]}}, {"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 2, "mapping": "footer", "name": "Footer menu", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 18, "parent_id": null, "order": 3, "type": "group", "link_type": null, "link_id": null, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "GDPR"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 19, "parent_id": null, "order": 2, "type": "group", "link_type": null, "link_id": null, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Πληροφορίες"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 20, "parent_id": null, "order": 1, "type": "group", "link_type": null, "link_id": null, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Προϊόντα"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 21, "parent_id": 18, "order": 1, "type": "page", "link_type": "page", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Όροι & Προϋποθέσεις"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 22, "parent_id": 18, "order": 2, "type": "page", "link_type": "page", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Πολιτική Απορρήτου"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 23, "parent_id": 19, "order": 1, "type": "page", "link_type": "page", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Παράδοση και αλλαγές"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 24, "parent_id": 19, "order": 2, "type": "page", "link_type": "page", "link_id": 22, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "FAQ"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 25, "parent_id": 20, "order": 1, "type": "category", "link_type": "category", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Αξεσ<PERSON><PERSON><PERSON><PERSON> γάμου"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 26, "parent_id": 20, "order": 2, "type": "category", "link_type": "category", "link_id": 4, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Βάπτιση"}, "relations": []}]}}, {"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 3, "mapping": "navigationLinks", "name": "Σύνδεσμοι πλοήγησης", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 28, "parent_id": null, "order": 1, "type": null, "link_type": null, "link_id": null, "route": "blog.list", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Blog"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 29, "parent_id": null, "order": 1, "type": null, "link_type": null, "link_id": null, "route": "contacts", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Contacts"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 30, "parent_id": null, "order": 2, "type": null, "link_type": null, "link_id": null, "route": "site.vendors", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Brands"}, "relations": []}]}}]