[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-5", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add size/advantages/more information about the material through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 7, "vendor_id": null, "image_id": 5, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 25400, "price_to": 25400, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 4, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 4, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 25400, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 5, "name": "AT Bon Air спинер 55 см, черен цвят", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "at-bon-air-spiner-55-sm-ceren-cvat-image_6204ffce184cf.jpeg", "background": "#696969", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 16, "color_id": 44, "percents": 61, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 17, "color_id": 55, "percents": 10, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 18, "color_id": 146, "percents": 8, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 19, "color_id": 50, "percents": 6, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 181593}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 4, "name": "AT Bon Air спинер 55 см, черен цвят", "parent_id": 2, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "at-bon-air-spiner-55-sm-ceren-cvat-image_6204ffccd90e2.jpeg", "background": "#696969", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 13, "color_id": 44, "percents": 65, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 14, "color_id": 55, "percents": 14, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 15, "color_id": 146, "percents": 7, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 260494}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 3, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-4", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add size/advantages/more information about the material through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 3, "vendor_id": null, "image_id": 8, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 26600, "price_to": 26600, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 6, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 6, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 26600, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 8, "name": "Дамска чанта Sphinx бордо", "parent_id": 3, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "damska-canta-sphinx-bordo-image_6205006b48e5b.jpeg", "background": "#800000", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 25, "color_id": 87, "percents": 63, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 26, "color_id": 135, "percents": 10, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 27, "color_id": 12, "percents": 5, "R": 165, "G": 42, "B": 42, "hex": "#A52A2A"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 134414}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 6, "name": "Дамска чанта Sphinx бордо", "parent_id": 3, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "damska-canta-sphinx-bordo-image_62050066b4bba.jpeg", "background": "#800000", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 20, "color_id": 87, "percents": 42, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 21, "color_id": 12, "percents": 26, "R": 165, "G": 42, "B": 42, "hex": "#A52A2A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 22, "color_id": 135, "percents": 9, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 139235}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 7, "name": "Дамска чанта Sphinx бордо", "parent_id": 3, "sort_order": 3, "active": "yes", "max_thumb_size": 1280, "image_id": "damska-canta-sphinx-bordo-image_62050068a803c.jpeg", "background": "#800000", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 23, "color_id": 87, "percents": 83, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 24, "color_id": 12, "percents": 12, "R": 165, "G": 42, "B": 42, "hex": "#A52A2A"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 394451}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 7, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 8, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 9, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 4, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-3", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add size/advantages/more information about the material through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 4, "vendor_id": null, "image_id": 9, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 36200, "price_to": 36200, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 8, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 8, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 36200, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 9, "name": "Bleisure сак 50см черен цвят", "parent_id": 4, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "bleisure-sak-50sm-ceren-cvat-image_620500c468c33.jpeg", "background": "#696969", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 28, "color_id": 44, "percents": 16, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 29, "color_id": 129, "percents": 11, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 30, "color_id": 38, "percents": 10, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 31, "color_id": 124, "percents": 8, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 32, "color_id": 126, "percents": 7, "R": 244, "G": 164, "B": 96, "hex": "#F4A460"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 33, "color_id": 16, "percents": 6, "R": 210, "G": 105, "B": 30, "hex": "#D2691E"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 34, "color_id": 87, "percents": 6, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 183225}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 10, "name": "Bleisure сак 50см черен цвят", "parent_id": 4, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "bleisure-sak-50sm-ceren-cvat-image_620500c5d8781.jpeg", "background": "#2F4F4F", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 35, "color_id": 38, "percents": 64, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 36, "color_id": 44, "percents": 21, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 37, "color_id": 146, "percents": 5, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 106355}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 10, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 11, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 12, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 5, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-2", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add size/advantages/more information about the material through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": null, "image_id": 12, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 6800, "price_to": 6800, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 10, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 10, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 6800, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 12, "name": "Раница Urban Groove Lifestyle Black/Grey", "parent_id": 5, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "ranica-urban-groove-lifestyle-blackgrey-image_62050106820ec.jpeg", "background": "#2F4F4F", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 41, "color_id": 38, "percents": 38, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 42, "color_id": 44, "percents": 30, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 43, "color_id": 55, "percents": 25, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 108387}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 11, "name": "Раница Urban Groove Lifestyle Black/Grey", "parent_id": 5, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "ranica-urban-groove-lifestyle-blackgrey-image_6205010544f64.jpeg", "background": "#696969", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 38, "color_id": 44, "percents": 74, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 39, "color_id": 38, "percents": 6, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 40, "color_id": 146, "percents": 6, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 217656}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 13, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 14, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 15, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 6, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-1", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add size/advantages/more information about the material through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 5, "vendor_id": null, "image_id": 13, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 4500, "price_to": 4500, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 12, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 12, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 4500, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 13, "name": "Възглавничка за път, с мемори пяна цвят черен", "parent_id": 6, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "vazglavnicka-za-pat-s-memori-pana-cvat-ceren-image_6205014660985.jpeg", "background": "#2F4F4F", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 44, "color_id": 38, "percents": 44, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 45, "color_id": 44, "percents": 21, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 46, "color_id": 146, "percents": 15, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 47, "color_id": 26, "percents": 6, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 105328}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 16, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 17, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 18, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 7, "url_handle": "suitcase-with-gift-backpack", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add size/advantages/more information about the material through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist. ", "category_id": null, "vendor_id": null, "image_id": null, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 25400, "price_to": 25400, "price_percent": null, "individual_price": 0, "price_type": "price", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 13, "short_description": "Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?\nSince the product page is a step away from the cart, you should gain your customer’s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.\n", "featured": 0, "description_title": null, "type": "bundle", "is_hidden": 0, "per_row": 2, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": null}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 13, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": null, "barcode": null, "price": 25400, "delivery_price": null, "weight": null}, "relations": []}], "images": [], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 19, "parameter": "timer_list", "value": "on"}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 20, "parameter": "timer_details", "value": "on"}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 8, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add size/advantages/more information about the material through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 3, "vendor_id": null, "image_id": 16, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 21200, "price_to": 21200, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 15, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 15, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 21200, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 16, "name": "Дамска чанта Royal", "parent_id": 8, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "damska-canta-royal-image_6206569ed083a.jpeg", "background": "#696969", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 55, "color_id": 44, "percents": 50, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 56, "color_id": 55, "percents": 21, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 57, "color_id": 26, "percents": 7, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 58, "color_id": 146, "percents": 5, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 253294}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 15, "name": "Дамска чанта Royal", "parent_id": 8, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "damska-canta-royal-image_6206569cbed84.jpeg", "background": "#696969", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 52, "color_id": 44, "percents": 51, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 53, "color_id": 55, "percents": 25, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 54, "color_id": 26, "percents": 11, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 331052}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 14, "name": "Дамска чанта Royal", "parent_id": 8, "sort_order": 3, "active": "yes", "max_thumb_size": 1280, "image_id": "damska-canta-royal-image_6206569b24160.jpeg", "background": "#696969", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 48, "color_id": 44, "percents": 58, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 49, "color_id": 55, "percents": 12, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 50, "color_id": 26, "percents": 7, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 51, "color_id": 130, "percents": 6, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 285369}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 21, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 22, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 23, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}]