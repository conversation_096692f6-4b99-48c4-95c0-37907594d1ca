[{"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 1, "mapping": "main", "name": "Главно меню", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 4, "parent_id": null, "order": 6, "type": "section", "link_type": null, "link_id": null, "route": "blog.list", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Блог"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 5, "parent_id": null, "order": 7, "type": "section", "link_type": null, "link_id": null, "route": "contacts", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Контакти"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 11, "parent_id": null, "order": 1, "type": "category", "link_type": "category", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "<PERSON>у<PERSON><PERSON><PERSON>и"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 12, "parent_id": 11, "order": 1, "type": "category", "link_type": "category", "link_id": 6, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Меки куфари"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 13, "parent_id": 11, "order": 2, "type": "category", "link_type": "category", "link_id": 7, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Твърди куфари"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 14, "parent_id": null, "order": 2, "type": "category", "link_type": "category", "link_id": 4, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Сакове"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 15, "parent_id": null, "order": 3, "type": "category", "link_type": "category", "link_id": 2, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Раници"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 16, "parent_id": null, "order": 4, "type": "category", "link_type": "category", "link_id": 3, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Дамски чанти"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 17, "parent_id": null, "order": 5, "type": "category", "link_type": "category", "link_id": 5, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Аксесоари"}, "relations": []}]}}, {"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 2, "mapping": "footer", "name": "Долно меню", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 18, "parent_id": null, "order": 3, "type": "group", "link_type": null, "link_id": null, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "GDPR политика"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 19, "parent_id": null, "order": 2, "type": "group", "link_type": null, "link_id": null, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Информация"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 20, "parent_id": null, "order": 1, "type": "group", "link_type": null, "link_id": null, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Продукти"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 21, "parent_id": 18, "order": 1, "type": "page", "link_type": "page", "link_id": 2, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Общи условия"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 22, "parent_id": 19, "order": 1, "type": "page", "link_type": "page", "link_id": 3, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Методи за плащане"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 23, "parent_id": 18, "order": 2, "type": "page", "link_type": "page", "link_id": 4, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Политика за поверителност"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 24, "parent_id": 18, "order": 4, "type": "page", "link_type": "page", "link_id": 5, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Ние използваме \"Бисквитки\""}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 25, "parent_id": 19, "order": 3, "type": "page", "link_type": "page", "link_id": 6, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Връщане и замяна"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 26, "parent_id": 18, "order": 3, "type": "page", "link_type": "page", "link_id": 7, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Декларация за лични данни"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 27, "parent_id": 19, "order": 2, "type": "page", "link_type": "page", "link_id": 8, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Доставка"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 28, "parent_id": 19, "order": 4, "type": "page", "link_type": "page", "link_id": 9, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Право на отказ на поръчка"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 29, "parent_id": 20, "order": 1, "type": "category", "link_type": "category", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "<PERSON>у<PERSON><PERSON><PERSON>и"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 30, "parent_id": 20, "order": 2, "type": "category", "link_type": "category", "link_id": 4, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Сакове"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 31, "parent_id": 20, "order": 3, "type": "category", "link_type": "category", "link_id": 2, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Раници"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 32, "parent_id": 20, "order": 4, "type": "category", "link_type": "category", "link_id": 3, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Дамски чанти"}, "relations": []}]}}, {"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 3, "mapping": "navigationLinksPage", "name": "Странично меню - Страници", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 33, "parent_id": null, "order": 1, "type": null, "link_type": null, "link_id": null, "route": "blog.list", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Блог"}, "relations": []}]}}]