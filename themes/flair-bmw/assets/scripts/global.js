var $doc = $(document);
var $win = $(window);

function navigationOpen() {
	$('.js-navigation-hamburger').on('click', function (e) {
		e.preventDefault();
		$(this).toggleClass('active');
		$('.js-navigation-hamburger-dropdown').toggleClass('block');
	});
}

function sidebarOpen() {
	$('.js-sidebar-toggler').on('click', function(e){
		e.preventDefault();

		var $productsBox = $('.js-products-box');
		var wideClass    = 'col-md-12';
		var narrowClass  = 'col-md-9';
		var openClass;

		if ( $win.width() > 991 ) {
			if ( $productsBox.hasClass(wideClass) ) {
				$productsBox.removeClass(wideClass).addClass(narrowClass);
				Cookies.set('sidebar', 'open', { expires: 365 });
			} else if ( $productsBox.hasClass(narrowClass) ) {
				$productsBox.removeClass(narrowClass).addClass(wideClass);
				Cookies.set('sidebar', 'closed', { expires: 365 });
			}

			openClass = 'open';
		} else {
			openClass = 'mobile-open';
		}

		$('.js-sidebar, .js-sidebar-buttons').toggleClass(openClass);
	});

	if ($win.width() < 992) {
		$('.js-sidebar-buttons').removeClass('open');
	}
}

function listCollapse() {
	$doc.on('click', '.js-nolink, ._collapse', function (e){
        e.preventDefault();

        var $el = $(this);

        if ($el.parents('.js-nolink').length) {
            return
        }

        $el.closest('.item-collapse').toggleClass('opener');

        $el.toggleClass('_collapse-active');
    });
}

function sidebarAjaxSuccess() {
	$(document).on('cc.filters.filters.after', function(e, filters) {
		categoryPropertiesToggle(filters);
	});
}

function categoryPropertiesToggle(selector) {
	$('._filter-category-property', selector).each(function(){
		var $el = $(this),
			name = $el.data('filter-box'),
			exists = $.inArray(Cookies.get('products.list.filter.' + name), ['0','1']) > -1,
			opened2 = Cookies.get('products.list.filter.' + name) > 0;

		if(exists) {
			$el[opened2 ? 'addClass' : 'removeClass']('open');
		} else {
			if ($el.find('.checked').length) {
				$el.addClass('open');
			}
		}
		var opened = $el.hasClass('open') ? 1 : 0;
		Cookies.set('products.list.filter.' + name, opened, { expires: 7 });
	});

	$('._filter-category-property-title').on('click', function(e){
		e.preventDefault();

		var $el = $(this).closest('._filter-category-property').toggleClass('open'),
			name = $el.data('filter-box'),
			opened = $el.hasClass('open') ? 1 : 0;
		Cookies.set('products.list.filter.' + name, opened, { expires: 7 });
	});
}

function shareToggle() {
	$doc.on('click', '.js-button-share', function(e){
		e.preventDefault();

		$(this).closest('.js-product-share').toggleClass('open');
	});
}

function checkoutRadios() {
	$('.page-checkout')
		.on('change', 'input[type="radio"]', function(e){
			var radioName = $(this).attr('name');

			$('input[name="' + radioName + '"]').closest('._radio').removeClass('radio-active');
			$(this).closest('._radio').addClass('radio-active');
		})
		.find('._radio').each(function(){
			var $this = $(this);

			if ( $this.find('.checked').length ) {
				$this.addClass('radio-active');
			}
		});
}

function swiperChangeImage() {
	$('._product-details-gallery-container').find('.swiper-button').on('click', function(e){
		e.preventDefault();

		var $this    = $(this);
		var $current = $this.closest('._product-details-gallery-container').find('.cc-active');

		if ($this.hasClass('swiper-button-prev')) {
			var $prev = $current.prev();

			if ($prev.hasClass('hide')) {
				$prev = $current.prevUntil(':not(.hide)').prev();
			}

			$prev.find('a').click();
		} else {
			var $next = $current.next();

			if ($next.hasClass('hide')) {
				$next = $current.nextUntil(':not(.hide)').next();
			}

			$next.find('a').click();
		}
	});
}

function blogGrid() {
	var $articles = $('._blog-list-articles');
	var $article = $articles.find('._blog-list-article');

	if ( $articles.length ) {
		$article.addClass('ordered');
		$articles.siblings('.js-loader-articles').addClass('hide');
		$articles.masonry();

		var count = $article.length;
		var i     = 0;

		var revealImages = setInterval(function(){
			$article.eq(i).addClass('visible');

			if ( i == count - 1) {
				clearInterval(revealImages);
			}

			i++
		}, 100);
	}
}

function orderDetailsToggle() {
	var isClickable = true;

	$('.js-order-details-button').on('click', function(e){
		e.preventDefault();

		var $this = $(this);
		var $row = $this.closest('tr');

		if ( !$row.hasClass('active') ) {
			$this.addClass('loading');

			if (isClickable) {
				isClickable = false;
		
				$.ajax({
					url: $this.attr('href'),
					success: function(data){
						var html = data.html;
						var $detailsRow = $('<tr class="_order-details-row"></tr>').insertAfter($row).html(html);
						$row.addClass('active');
						$this.removeClass('loading');
						isClickable = true;
					}
				});
			}
		} else {
			$row.removeClass('active').next('._order-details-row').remove();
		}
	});
}

function itemRevealInit( $item ) {
	if ( $item.length ) {
		var itemHeight = $item.outerHeight();
		
		itemReveal( $item, itemHeight );

		$win.on('scroll', function(){
			itemReveal( $item, itemHeight );
		});
	}
}

function itemReveal( $item, itemHeight ){
	var revealPosition = $win.scrollTop() + $win.height() - itemHeight/2;

	$item.each(function(){
		var $this = $(this);

		if ($this.offset().top < revealPosition ) {
			$this.addClass('revealed');
		}
	});
}

var width = $win.width();

$doc.ready(function () {
	navigationOpen();
	sidebarOpen();
	listCollapse();
	shareToggle();
	checkoutRadios();
	orderDetailsToggle();
	swiperChangeImage();
	categoryPropertiesToggle();
	itemRevealInit( $('._product') );
	itemRevealInit( $('._vendor') );
	itemRevealInit( $('._showcase-item') );
	sidebarAjaxSuccess();
});

$win.on({
	load: function () {
		blogGrid();
	},
	resize: function () {
		if ($win.width()==width) return;
		width = $win.width();
		$('.js-navigation-hamburger-dropdown, .js-products-list-sidebar').removeClass('block');
		$('.js-navigation-hamburger, .js-products-list-sidebar-toggle').removeClass('active');
		if (!$.isLg()) {
			$('.js-checkout-sidebar').trigger('detach');
		}
	}
});