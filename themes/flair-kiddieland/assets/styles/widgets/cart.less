/*=============================================================================*\
    CART
\*=============================================================================*/

/*  COMPACT
-------------------------------------------------------------------------------*/

._cart-compact,
._wishlist-compact {
	display: inline-block;
	vertical-align: middle;
	position: relative;
	font-size: @font-size-main; /* theme */
	line-height: @line-height-low;
	text-align: left;
	margin-left: 20px;
	perspective: 1000px;

	@media @hover {
		&:hover {
			._cart-compact-dropdown,
			._wishlist-compact-dropdown {
				opacity: 1;
				visibility: visible;
				transition: transform .4s;
				transform: rotateX(0);
			}
		}
	}

	&-dropdown {
		position: absolute;
		top: 100%;
		right: -20px;
		padding-top: 9px;
		opacity: 0;
		visibility: hidden;
		transition: .3s;
		transform-origin: 0 0;
		transform: rotateX(-90deg);

		&:before {
			content: '';
			border-left: 7px solid transparent;
			border-right: 7px solid transparent;
			border-bottom: 7px solid @color-cart-background; /* theme */
			position: absolute;
			top: 3px;
			right: 20px;
		}
	}

	&-products {
		&-wrapper {
			background-color: @color-cart-background; /* theme */
			width: 350px;
			max-height: 500px;
			padding: 20px;
			color: @color-cart-text; /* theme */
			overflow: auto;
			position: relative;
		}
		&-title {
			border-bottom: 1px solid;
			border-color: @color-cart-borders; /* theme */
			padding-bottom: 16px;
			margin-bottom: 20px;
			.uppercase();

			h2 {
				color: @color-cart-titles; /* theme */
			}
		}
		&-list {
			list-style-type: none;
			border-bottom: 1px solid;
			border-color: @color-cart-borders; /* theme */
			margin-bottom: 20px;

			&-item {
				display: block;
				position: relative;
				margin-bottom: 20px;

				@media @hover {
					&:hover {
						._remove {
							opacity: 1;
							visibility: visible !important;
						}
					}
				}

				&-link {
					display: table;
					width: 100%;
					transition: .2s;
					color: @color-cart-text; /* theme */

					&:hover {
						color: @color-cart-button-background; /* theme */
					}
				}

				&-image {
					display: table-cell;
					vertical-align: top;
					text-align: center;
					width: 65px;
					
					&-thumb {
						display: block;
						position: relative;

						img {
							max-width: 100%;
							max-height: 100%;
							.centerer(true, true);
						}
					}
				}

				&-info {
					display: table-cell;
					vertical-align: middle;
					padding-left: 10px;
					padding-right: 20px;
				}

				&-title {
					display: block;
					margin-bottom: 11px;
				}

				&-price {
					display: block;
					color: @color-cart-button-background; /* theme */
					font-weight: bold;
				}

				._remove {
					color: @color-cart-button-background; /* theme */
					position: absolute;
					top: 0;
					right: 0;
					opacity: 0;
					visibility: hidden !important;
					transition: .2s;
				}
			}
		}

		&-subtotal {
			margin-bottom: 20px;
			font-weight: bold;
			.uppercase();

			&-num {
				float: right;
			}
		}

		._button {
			display: block;
		}
	}

	> ._figure-stack {
		display: inline-block;
		color: @color-topbar-icons; /* theme */

		&:hover {
			color: @color-topbar-hovers; /* theme */
		}

		._figure-stack {
			&-label {
				display: none;
			}

			&-icon {
				display: inline-block;
				font-size: 16px;
				position: relative;
			}
		}

		._bubble {
			position: absolute;
			top: -4px;
			left: 7px;
		}
	}

	._cart-compact {
		margin: 0;
	}

	._button {
		background-color: @color-cart-button-background; /* theme */
		color: @color-cart-button-text; /* theme */

		&.loading {
			color: @color-cart-button-background; /* theme */
		}

		&-secondary {
			background-color: @color-cart-button-secondary-background; /* theme */
			color: @color-cart-button-secondary-text; /* theme */

			&.loading {
				color: @color-cart-button-secondary-background; /* theme */
			}
		}
	}

	._checkbox {
		.checker {
			span {
				border-color: @color-cart-meta-text; /* theme */

				&.checked {
					border-color: @color-cart-meta-text; /* theme */

					&:before {
						color: @color-cart-meta-text; /* theme */
					}
				}
			}
			
			&.hover,
			&.focus {
				span {
					border-color: @color-cart-meta-text; /* theme */

					&.checked {
						border-color: @color-cart-meta-text; /* theme */
					}
				}
			}
		}

		a {
			color: @color-cart-text; /* theme */

			&:hover {
				opacity: .7;
			}
		}
	}

	._radio {
		.radio {
			span {
				border-color: @color-cart-meta-text; /* theme */

				&.checked {
					border-color: @color-cart-meta-text; /* theme */

					&:before {
						background-color: @color-cart-meta-text; /* theme */
					}
				}
			}
		}
	}
}

._bubble {
	display: inline-block;
	background-color: @color-topbar-cart-indicator-background; /* theme */
	color: @color-topbar-cart-indicator-text; /* theme */
	padding: 0;
	border-radius: 100%;
	font-size: @font-size-xsmall; /* theme */
	text-align: center;
	font-weight: 400;
	width: 16px;
	height: 16px;
	line-height: 16px;
}

/*  PRODUCTS LIST
-------------------------------------------------------------------------------*/

._cart-product {
	border-bottom: 1px solid;
	border-bottom-color: @color-main-borders; /* theme */
	padding-bottom: 30px;
	margin-bottom: 30px;
	position: relative;
	
	&:last-child {
		padding-bottom: 0;
		border-bottom: 0;
		margin-bottom: 0;

		._cart-product-quantity,
		._cart-product-total-price {
			bottom: 0;
		}
	}

	&:hover {
		._remove {
			opacity: 1;
			visibility: visible !important;
		}
	}

	&-info {
		display: table;
		width: 100%;
	}

	&-image {
		display: table-cell;
		vertical-align: top;
		width: 250px;
		padding-top: 5px;
	
		&-thumb {
			position: relative;
		
			img {
				max-width: 100%;
				max-height: 100%;
				.centerer(true, true);
			}
		}
	}

	&-box {
		padding-left: 30px;
		padding-bottom: 55px;
		display: table-cell;
		vertical-align: top;
		color: @color-main-meta-text; /* theme */
		font-size: calc(@font-size-main ~'-' 1px); /* theme */
		line-height: @line-height-medium;
	}

	&-name {
		margin-bottom: 13px;
		padding-right: 25px;
		.uppercase();

		h4 {
			._h1();
		}
	}
	
	&-parameters {
		margin-top: 5px;
	}
	
	&-parameter {
		display: block;
		
		& when (@rtl) {
			direction: ltr;
		}
	}
	
	&-single-price {
		font-family: @font-family-secondary; /* theme */
		font-weight: bold;
		font-size: 16px;
		color: @color-product-price; /* theme */
		margin-top: 7px;
		text-align: left;
		
		& when (@rtl) {
			direction: ltr;
		}
	}

	&-quantity {
		position: absolute;
		bottom: 30px;
		left: 280px;
		font-size: 0;
		
		.input-group {
			width: 150px;
		}

		&-total {
			font-size: @font-size-main; /* theme */
			
			&:before {
				content: 'x ';
			}
		}
	}

	&-total-price {
		font-family: @font-family-secondary; /* theme */
		font-size: 24px;
		font-weight: bold;
		color: @color-product-price; /* theme */
		position: absolute;
		right: 0;
		bottom: 30px;
		
		& when (@rtl) {
			direction: ltr;
		}
	}

    ._remove {
        position: absolute;
        top: 0;
        right: 0;
        color: @color-main-highlight; /* theme */
        opacity: 0;
        visibility: hidden !important;

        &:before {
        	font-size: 16px;
        	margin-right: 5px;
        }

        &:hover {
        	opacity: .7;
        }
    }
}