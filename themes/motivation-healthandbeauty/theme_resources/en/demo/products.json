[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-10", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": 2, "image_id": 2, "tracking": "yes", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 3279, "price_to": 3279, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 4, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 4, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 10, "sku": "", "barcode": "", "price": 3279, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 2, "name": "Normaderm Anti-Age", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "normaderm-anti-age-image_6180f460e49f8.jpeg", "background": "#F5FFFA", "width": 328, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 7, "color_id": 98, "percents": 27, "R": 245, "G": 255, "B": 250, "hex": "#F5FFFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 8, "color_id": 118, "percents": 17, "R": 176, "G": 224, "B": 230, "hex": "#B0E0E6"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 9, "color_id": 70, "percents": 11, "R": 224, "G": 255, "B": 255, "hex": "#E0FFFF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 10, "color_id": 111, "percents": 10, "R": 175, "G": 238, "B": 238, "hex": "#AFEEEE"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 11, "color_id": 146, "percents": 10, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 12, "color_id": 58, "percents": 9, "R": 240, "G": 255, "B": 240, "hex": "#F0FFF0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 49298}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 10, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-11", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Size", "p2": null, "p3": null, "p1_id": 4, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": 5, "image_id": 17, "tracking": "yes", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 900, "price_to": 2945, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 23, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 21, "v1": "400 мл.", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": 10, "sku": "", "barcode": "", "price": 2945, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 22, "v1": "200 мл.", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": 10, "sku": "", "barcode": "", "price": 2290, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 23, "v1": "100 мл.", "v2": null, "v3": null, "v1_id": 12, "v2_id": null, "v3_id": null, "quantity": 10, "sku": "", "barcode": "", "price": 900, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 17, "name": "Effaclar Ultra", "parent_id": 10, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "effaclar-ultra-image_618103fced2f3.jpeg", "background": "#F0FFFF", "width": 260, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 79, "color_id": 5, "percents": 36, "R": 240, "G": 255, "B": 255, "hex": "#F0FFFF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 80, "color_id": 1, "percents": 12, "R": 240, "G": 248, "B": 255, "hex": "#F0F8FF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 81, "color_id": 118, "percents": 7, "R": 176, "G": 224, "B": 230, "hex": "#B0E0E6"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 82, "color_id": 70, "percents": 7, "R": 224, "G": 255, "B": 255, "hex": "#E0FFFF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 83, "color_id": 68, "percents": 6, "R": 173, "G": 216, "B": 230, "hex": "#ADD8E6"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 68548}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 18, "name": "Effaclar Ultra", "parent_id": 10, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "effaclar-ultra-image_6181040066838.jpeg", "background": "#5F9EA0", "width": 257, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 84, "color_id": 14, "percents": 27, "R": 95, "G": 158, "B": 160, "hex": "#5F9EA0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 85, "color_id": 5, "percents": 11, "R": 240, "G": 255, "B": 255, "hex": "#F0FFFF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 86, "color_id": 68, "percents": 9, "R": 173, "G": 216, "B": 230, "hex": "#ADD8E6"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 87, "color_id": 118, "percents": 9, "R": 176, "G": 224, "B": 230, "hex": "#B0E0E6"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 88, "color_id": 98, "percents": 7, "R": 245, "G": 255, "B": 250, "hex": "#F5FFFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 89, "color_id": 135, "percents": 5, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 84581}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 28, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 29, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 30, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 23, "parameter_id": 4, "parameter_option_id": 10, "variant_id": 21}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 24, "parameter_id": 4, "parameter_option_id": 11, "variant_id": 22}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 25, "parameter_id": 4, "parameter_option_id": 12, "variant_id": 23}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 15, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-12", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 11, "vendor_id": 9, "image_id": 48, "tracking": "yes", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 4429, "price_to": 4429, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 39, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 39, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 8, "sku": "", "barcode": "", "price": 4429, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 48, "name": "Résistance Fondant Extentioniste", "parent_id": 15, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "resistance-fondant-extentioniste-image_61813e67b49f1.jpeg", "background": "#20B2AA", "width": 344, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 217, "color_id": 77, "percents": 33, "R": 32, "G": 178, "B": 170, "hex": "#20B2AA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 218, "color_id": 23, "percents": 12, "R": 0, "G": 139, "B": 139, "hex": "#008B8B"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 219, "color_id": 135, "percents": 9, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 220, "color_id": 95, "percents": 9, "R": 72, "G": 209, "B": 204, "hex": "#48D1CC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 221, "color_id": 5, "percents": 6, "R": 240, "G": 255, "B": 255, "hex": "#F0FFFF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 222, "color_id": 38, "percents": 5, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 67169}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 47, "name": "Résistance Fondant Extentioniste", "parent_id": 15, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "resistance-fondant-extentioniste-image_61813e663bf61.jpeg", "background": "#DCDCDC", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 213, "color_id": 50, "percents": 58, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 214, "color_id": 146, "percents": 9, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 215, "color_id": 95, "percents": 8, "R": 72, "G": 209, "B": 204, "hex": "#48D1CC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 216, "color_id": 26, "percents": 7, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 82513}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 43, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 44, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 45, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 19, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-13", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 11, "vendor_id": 11, "image_id": 54, "tracking": "yes", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 1019, "price_to": 1019, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 47, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 47, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 10, "sku": "", "barcode": "", "price": 1019, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 54, "name": "Wella Professionals Performance", "parent_id": 19, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "wella-professionals-performance-image_6181418f2f01c.jpeg", "background": "#708090", "width": 190, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 248, "color_id": 134, "percents": 21, "R": 112, "G": 128, "B": 144, "hex": "#708090"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 249, "color_id": 1, "percents": 16, "R": 240, "G": 248, "B": 255, "hex": "#F0F8FF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 250, "color_id": 26, "percents": 12, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 251, "color_id": 146, "percents": 11, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 252, "color_id": 38, "percents": 8, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 253, "color_id": 50, "percents": 7, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 254, "color_id": 68, "percents": 6, "R": 173, "G": 216, "B": 230, "hex": "#ADD8E6"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 255, "color_id": 80, "percents": 6, "R": 119, "G": 136, "B": 153, "hex": "#778899"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 46350}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 55, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 56, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 57, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 20, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-14", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 11, "vendor_id": 12, "image_id": 55, "tracking": "yes", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 1590, "price_to": 1590, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 49, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 49, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 10, "sku": "", "barcode": "", "price": 1590, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 55, "name": "L’Oréal Professionnel Infinium Extra Strong", "parent_id": 20, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "loreal-professionnel-infinium-extra-strong-image_6181428767a21.jpeg", "background": "#696969", "width": 188, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 256, "color_id": 44, "percents": 19, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 257, "color_id": 130, "percents": 17, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 258, "color_id": 50, "percents": 12, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 259, "color_id": 146, "percents": 8, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 260, "color_id": 13, "percents": 6, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 261, "color_id": 135, "percents": 6, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 262, "color_id": 98, "percents": 6, "R": 245, "G": 255, "B": 250, "hex": "#F5FFFA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 63456}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 58, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 59, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 60, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 22, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-15", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 11, "vendor_id": 11, "image_id": 58, "tracking": "yes", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 1350, "price_to": 1350, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 53, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 53, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 10, "sku": "", "barcode": "", "price": 1350, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 58, "name": "Wella Professionals Eimi Sugar Lift", "parent_id": 22, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "wella-professionals-eimi-sugar-lift-image_6181478bb695a.jpeg", "background": "#BC8F8F", "width": 200, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 275, "color_id": 122, "percents": 22, "R": 188, "G": 143, "B": 143, "hex": "#BC8F8F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 276, "color_id": 44, "percents": 16, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 277, "color_id": 85, "percents": 12, "R": 250, "G": 240, "B": 230, "hex": "#FAF0E6"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 278, "color_id": 55, "percents": 12, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 279, "color_id": 73, "percents": 11, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 35039}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 57, "name": "Wella Professionals Eimi Sugar Lift", "parent_id": 22, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "wella-professionals-eimi-sugar-lift-image_6181478a498c2.jpeg", "background": "#FFA07A", "width": 800, "height": 760, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 271, "color_id": 76, "percents": 16, "R": 255, "G": 160, "B": 122, "hex": "#FFA07A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 272, "color_id": 34, "percents": 16, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 273, "color_id": 135, "percents": 7, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 274, "color_id": 6, "percents": 5, "R": 245, "G": 245, "B": 220, "hex": "#F5F5DC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 118011}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 64, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 65, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 66, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 23, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-16", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": 7, "image_id": 59, "tracking": "yes", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 2250, "price_to": 2250, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 55, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 55, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 10, "sku": "45G684", "barcode": "", "price": 2250, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 59, "name": "Bioderma Atoderm Cream", "parent_id": 23, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "bioderma-atoderm-cream-image_6181492e8d882.jpeg", "background": "#F5F5F5", "width": 287, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 280, "color_id": 146, "percents": 23, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 281, "color_id": 130, "percents": 11, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 282, "color_id": 50, "percents": 10, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 283, "color_id": 135, "percents": 7, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 284, "color_id": 1, "percents": 7, "R": 240, "G": 248, "B": 255, "hex": "#F0F8FF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 285, "color_id": 73, "percents": 6, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 286, "color_id": 5, "percents": 5, "R": 240, "G": 255, "B": 255, "hex": "#F0FFFF"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 76712}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 67, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 68, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 69, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 24, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-17", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Size", "p2": null, "p3": null, "p1_id": 4, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": 7, "image_id": 60, "tracking": "yes", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 1990, "price_to": 3565, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 60, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 58, "v1": "500 мл.", "v2": null, "v3": null, "v1_id": 20, "v2_id": null, "v3_id": null, "quantity": 10, "sku": "8J787F45", "barcode": "", "price": 3565, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 59, "v1": "200 мл.", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": 10, "sku": "45GE6J7", "barcode": "", "price": 3169, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 60, "v1": "75 мл.", "v2": null, "v3": null, "v1_id": 21, "v2_id": null, "v3_id": null, "quantity": 10, "sku": "346G456", "barcode": "", "price": 1990, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 60, "name": "Bioderma Atoderm Intensive Baume", "parent_id": 24, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "bioderma-atoderm-intensive-baume-image_61814a53b4c31.jpeg", "background": "#F8F8FF", "width": 310, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 287, "color_id": 51, "percents": 26, "R": 248, "G": 248, "B": 255, "hex": "#F8F8FF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 288, "color_id": 1, "percents": 20, "R": 240, "G": 248, "B": 255, "hex": "#F0F8FF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 289, "color_id": 64, "percents": 18, "R": 230, "G": 230, "B": 250, "hex": "#E6E6FA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 290, "color_id": 81, "percents": 10, "R": 176, "G": 196, "B": 222, "hex": "#B0C4DE"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 291, "color_id": 50, "percents": 6, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 72420}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 70, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 71, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 72, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 26, "parameter_id": 4, "parameter_option_id": 20, "variant_id": 58}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 27, "parameter_id": 4, "parameter_option_id": 11, "variant_id": 59}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 28, "parameter_id": 4, "parameter_option_id": 21, "variant_id": 60}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 25, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-9", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": 5, "image_id": 61, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 127500, "price_to": 127500, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 62, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product/service? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?\\nSince the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.\\n&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\" data-sheets-textstyleruns=\"{&quot;1&quot;:0}{&quot;1&quot;:177,&quot;2&quot;:{&quot;5&quot;:1}}{&quot;1&quot;:192}\">Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?<br />Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.<br /></span></p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 62, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 127500, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 61, "name": "LA PRAIRIE PLATINUM RARE CELLULAR LIFE-LOTION ДНЕВЕН ЛОСИОН", "parent_id": 25, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "la-prairie-platinum-rare-cellular-life-lotion-dneven-losion-622a2125b41a3.jpeg", "background": "#696969", "width": 1500, "height": 2244, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 292, "color_id": 44, "percents": 20, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 293, "color_id": 119, "percents": 17, "R": 128, "G": 0, "B": 128, "hex": "#800080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 294, "color_id": 122, "percents": 15, "R": 188, "G": 143, "B": 143, "hex": "#BC8F8F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 295, "color_id": 146, "percents": 13, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 296, "color_id": 96, "percents": 8, "R": 199, "G": 21, "B": 133, "hex": "#C71585"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 297, "color_id": 112, "percents": 7, "R": 216, "G": 112, "B": 147, "hex": "#D87093"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 182264}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 73, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 74, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 75, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 26, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-8", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": null, "image_id": 62, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 96500, "price_to": 96500, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 64, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product/service? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?\\nSince the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.\\n&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\" data-sheets-textstyleruns=\"{&quot;1&quot;:0}{&quot;1&quot;:177,&quot;2&quot;:{&quot;5&quot;:1}}{&quot;1&quot;:192}\">Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?<br />Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item. Therefore, the information must be presented in an appropriate tone for your consumer groups.<br /></span></p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 64, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 96500, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 62, "name": "LA PRAIRIE SKIN CAVIAR LUXE CREAM 24 - часов крем", "parent_id": 26, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "la-prairie-skin-caviar-luxe-cream-24-casov-krem-622a221b8af25.jpeg", "background": "#191970", "width": 1500, "height": 2244, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 298, "color_id": 97, "percents": 28, "R": 25, "G": 25, "B": 112, "hex": "#191970"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 299, "color_id": 36, "percents": 17, "R": 72, "G": 61, "B": 139, "hex": "#483D8B"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 300, "color_id": 146, "percents": 14, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 301, "color_id": 50, "percents": 5, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 302514}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 76, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 77, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 78, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 27, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-7", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": null, "image_id": 63, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 17200, "price_to": 17200, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 66, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product/service? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?\\nSince the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.\\n&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\" data-sheets-textstyleruns=\"{&quot;1&quot;:0}{&quot;1&quot;:177,&quot;2&quot;:{&quot;5&quot;:1}}{&quot;1&quot;:192}\">Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?<br />Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item. Therefore, the information must be presented in an appropriate tone for your consumer groups.<br /></span></p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 66, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 17200, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 63, "name": "LA PRAIRIE PLATINUM RARE HAUTE-REJUVENATION EYE CREAM", "parent_id": 27, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "la-prairie-platinum-rare-haute-rejuvenation-eye-cream-622a23761957e.jpeg", "background": "#F5F5F5", "width": 1347, "height": 1920, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 302, "color_id": 146, "percents": 12, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 303, "color_id": 50, "percents": 11, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 304, "color_id": 44, "percents": 8, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 305, "color_id": 117, "percents": 8, "R": 221, "G": 160, "B": 221, "hex": "#DDA0DD"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 306, "color_id": 122, "percents": 7, "R": 188, "G": 143, "B": 143, "hex": "#BC8F8F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 307, "color_id": 96, "percents": 7, "R": 199, "G": 21, "B": 133, "hex": "#C71585"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 308, "color_id": 140, "percents": 6, "R": 216, "G": 191, "B": 216, "hex": "#D8BFD8"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 309, "color_id": 119, "percents": 6, "R": 128, "G": 0, "B": 128, "hex": "#800080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 310, "color_id": 73, "percents": 6, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 311, "color_id": 26, "percents": 5, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 247270}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 79, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 80, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 81, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 28, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": null, "image_id": 64, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 7200, "price_to": 7200, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 68, "short_description": "<p><span>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the </span><span>product</span><span>? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</span></p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 68, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 7200, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 64, "name": "Intensive moisturizing cream", "parent_id": 28, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "intensive-moisturizing-cream-622a23b8554c8.jpeg", "background": "#5F9EA0", "width": 700, "height": 1055, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 312, "color_id": 14, "percents": 24, "R": 95, "G": 158, "B": 160, "hex": "#5F9EA0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 313, "color_id": 139, "percents": 14, "R": 0, "G": 128, "B": 128, "hex": "#008080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 314, "color_id": 38, "percents": 7, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 315, "color_id": 137, "percents": 7, "R": 70, "G": 130, "B": 180, "hex": "#4682B4"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 316, "color_id": 135, "percents": 5, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 140039}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 82, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 83, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 84, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 29, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-5", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": null, "image_id": 65, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 39000, "price_to": 39000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 70, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product/service? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?\\nSince the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.\\n&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\" data-sheets-textstyleruns=\"{&quot;1&quot;:0}{&quot;1&quot;:177,&quot;2&quot;:{&quot;5&quot;:1}}{&quot;1&quot;:192}\">Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?<br />Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.<br /></span></p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 70, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 39000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 65, "name": "Day rejuvenating cream for all skin types", "parent_id": 29, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "day-rejuvenating-cream-for-all-skin-types-622a23e49fd46.jpeg", "background": "#DCDCDC", "width": 797, "height": 1213, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 317, "color_id": 50, "percents": 24, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 318, "color_id": 146, "percents": 17, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 319, "color_id": 44, "percents": 15, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 320, "color_id": 130, "percents": 11, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 321, "color_id": 73, "percents": 9, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 322, "color_id": 26, "percents": 7, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 323, "color_id": 55, "percents": 7, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 324, "color_id": 38, "percents": 6, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 143125}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 85, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 86, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 87, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 30, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-4", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": null, "image_id": 66, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 3200, "price_to": 3200, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 72, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product/service? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?\\nSince the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.\\n&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\" data-sheets-textstyleruns=\"{&quot;1&quot;:0}{&quot;1&quot;:177,&quot;2&quot;:{&quot;5&quot;:1}}{&quot;1&quot;:192}\">Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?<br />Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item. Therefore, the information must be presented in an appropriate tone for your consumer groups.<br /></span></p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 72, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 3200, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 66, "name": "Avene <PERSON>en for sensitive skin SPF 30", "parent_id": 30, "sort_order": 1, "active": "yes", "max_thumb_size": 600, "image_id": "avene-sunscreen-for-sensitive-skin-spf-30-622a240f785f7.png", "background": "#D2691E", "width": 262, "height": 480, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 325, "color_id": 16, "percents": 49, "R": 210, "G": 105, "B": 30, "hex": "#D2691E"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 326, "color_id": 50, "percents": 11, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 327, "color_id": 146, "percents": 8, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 328, "color_id": 129, "percents": 8, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 177463}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 88, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 89, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 90, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 31, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-3", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 11, "vendor_id": null, "image_id": 67, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 670, "price_to": 670, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 74, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product/service? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?\\nSince the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.\\n&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\" data-sheets-textstyleruns=\"{&quot;1&quot;:0}{&quot;1&quot;:177,&quot;2&quot;:{&quot;5&quot;:1}}{&quot;1&quot;:192}\">Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?<br />Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item. Therefore, the information must be presented in an appropriate tone for your consumer groups.<br /></span></p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 74, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 670, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 67, "name": "Garnier Botanic Therapy Oil & Almond Repairing Cream for Weak Hair with Castor and Almond Oils", "parent_id": 31, "sort_order": 1, "active": "yes", "max_thumb_size": 600, "image_id": "garnier-botanic-therapy-oil--almond-repairing-cream-for-weak-hair-with-castor-and-almond-oils-622a2449b1a80.png", "background": "#FFDEAD", "width": 212, "height": 480, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 329, "color_id": 101, "percents": 16, "R": 255, "G": 222, "B": 173, "hex": "#FFDEAD"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 330, "color_id": 114, "percents": 14, "R": 255, "G": 218, "B": 185, "hex": "#FFDAB9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 331, "color_id": 115, "percents": 10, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 332, "color_id": 96, "percents": 8, "R": 199, "G": 21, "B": 133, "hex": "#C71585"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 333, "color_id": 12, "percents": 6, "R": 165, "G": 42, "B": 42, "hex": "#A52A2A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 334, "color_id": 138, "percents": 5, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 335, "color_id": 7, "percents": 5, "R": 255, "G": 228, "B": 196, "hex": "#FFE4C4"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 121287}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 91, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 92, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 93, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 32, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-2", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 11, "vendor_id": null, "image_id": 68, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 5000, "price_to": 5000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 76, "short_description": "<p><span>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the </span><span>product</span><span>? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item. Therefore, the information must be presented in an appropriate tone for your consumer groups.</span></p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 76, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 5000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 68, "name": "Kérastase Résistance Masque Thérapiste", "parent_id": 32, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "kerastase-resistance-masque-therapiste-622a249923e5c.jpeg", "background": "#2F4F4F", "width": 800, "height": 602, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 336, "color_id": 38, "percents": 41, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 337, "color_id": 139, "percents": 14, "R": 0, "G": 128, "B": 128, "hex": "#008080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 338, "color_id": 77, "percents": 9, "R": 32, "G": 178, "B": 170, "hex": "#20B2AA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 339, "color_id": 23, "percents": 8, "R": 0, "G": 139, "B": 139, "hex": "#008B8B"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 113409}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 94, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 95, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 96, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 33, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-1", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 11, "vendor_id": null, "image_id": 69, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 1700, "price_to": 1700, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 78, "short_description": "<p><span>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the </span><span>product</span><span>? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item. Therefore, the information must be presented in an appropriate tone for your consumer groups.</span></p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 78, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 1700, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 69, "name": "Wella Professionals Oil Reflections", "parent_id": 33, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "wella-professionals-oil-reflections-622a24b3cb143.jpeg", "background": "#F5F5F5", "width": 683, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 340, "color_id": 146, "percents": 42, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 341, "color_id": 50, "percents": 20, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 342, "color_id": 73, "percents": 9, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 88088}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 97, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 98, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 99, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 34, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add ingredients/more information about the product through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 2, "vendor_id": null, "image_id": 70, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 6600, "price_to": 6600, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 80, "short_description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product/service? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?\\nSince the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.\\n&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\" data-sheets-textstyleruns=\"{&quot;1&quot;:0}{&quot;1&quot;:177,&quot;2&quot;:{&quot;5&quot;:1}}{&quot;1&quot;:192}\">Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors?<br />Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item. Therefore, the information must be presented in an appropriate tone for your consumer groups.<br /></span></p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 80, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 6600, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 70, "name": "Face Cleansing", "parent_id": 34, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "face-cleansing-622a24f58c321.jpeg", "background": "#D3D3D3", "width": 429, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 343, "color_id": 73, "percents": 31, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 344, "color_id": 50, "percents": 27, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 345, "color_id": 146, "percents": 22, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 346, "color_id": 130, "percents": 11, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 44589}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 100, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 101, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 102, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}]