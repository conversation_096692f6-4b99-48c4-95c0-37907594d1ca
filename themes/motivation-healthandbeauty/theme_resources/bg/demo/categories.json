[{"model": "App\\Models\\Product\\Category", "attributes": {"id": 1, "name": "Заглавие обобщаващо продуктите в дадената категория (1/2 думи)", "order": 0, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": null, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "zaglavie-obobshtavashto-produktite-v-dadenata-kategoriya-12-dumi", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 2, "name": "Категория 2", "order": 0, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 1, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "kategoriya-2", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 3, "name": "Категория 3", "order": 0, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 2, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "kategoriya-3", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 4, "name": "Подкатегория 1", "order": 0, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 3, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "podkategoriya-1", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 5, "name": "Подкатегория 2", "order": 1, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 3, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "podkategoriya-2", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 6, "name": "Подкатегория 3", "order": 2, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 3, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "podkategoriya-3", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 7, "name": "Подкатегория 4", "order": 3, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 3, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "podkategoriya-4", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 8, "name": "Категория 4", "order": 1, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 2, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "kategoriya-4", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 9, "name": "Подкатегория 1", "order": 0, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 8, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "podkategoriya-1-1", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 10, "name": "Подкатегория 2", "order": 1, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 8, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "podkategoriya-2-1", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 11, "name": "Категория 5", "order": 1, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 1, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "kategoriya-5", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 12, "name": "Категория 6", "order": 0, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 11, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "kategoriya-6", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 13, "name": "Подкатегория 1", "order": 0, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 12, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "podkategoriya-1-2", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 14, "name": "Подкатегория 2", "order": 1, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 12, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "podkategoriya-2-2", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 15, "name": "Подкатегория 3", "order": 2, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 12, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "podkategoriya-3-1", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 16, "name": "Категория 7", "order": 1, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 11, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "kategoriya-7", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 17, "name": "Подкатегория 1", "order": 0, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 16, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "podkategoriya-1-3", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 18, "name": "Подкатегория 2", "order": 1, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 16, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "podkategoriya-2-3", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 19, "name": "Категория 8", "order": 2, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 1, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "kategoriya-8", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 20, "name": "Категория 9", "order": 0, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 19, "seo_title": "Заглавие, описващо типа на предлаганите в категорията продукти (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "kategoriya-9", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}, {"model": "App\\Models\\Product\\Category", "attributes": {"id": 22, "name": "Subcategory 1", "order": 0, "description": "<style><!--\ntd {border: 1px solid #ccc;}br {mso-data-placement:same-cell;}\n--></style>\n<p><span data-sheets-value=\"{&quot;1&quot;:2,&quot;2&quot;:&quot;Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.&quot;}\" data-sheets-userformat=\"{&quot;2&quot;:8961,&quot;3&quot;:{&quot;1&quot;:0},&quot;11&quot;:4,&quot;12&quot;:0,&quot;16&quot;:11}\">Категорийното описание е ключово в определянето на Google позицията на магазина ви. Потребителят почти никога не стига до втора страница при Google търсене. Добре оптимизиранато вътрешно SEO ще доведе всеки клиент към точния продукт. Разкажете, в няколко изречения каква комбинация от продукти предлага категорията, използвайте думи на база предварителна информация от потребителносто търсене, която можете да получите при анализ от SEO експерти, като нашите партньори.</span></p>", "parent_id": 20, "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "url_handle": "subcategory-1-4", "display_child": 0, "image": null, "max_thumb_size": null, "width": null, "height": null, "image_processed": 1}, "relations": []}]