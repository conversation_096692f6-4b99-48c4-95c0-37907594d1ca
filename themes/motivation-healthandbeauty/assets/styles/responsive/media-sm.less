/*=============================================================================*\
    SMALL DEVICES / TABLETS
\*=============================================================================*/

@media (max-width: @screen-sm-max) {

	._copyright,
	._powered,
	._opc {
		display: block;
		text-align: center;
	}

	._copyright,
	._powered {
		margin-right: 0;
	}

	._compare-box-item {
		._remove {
			display: block;
		}
	}

	._compare-box-item-image,
	._compare-box-item-info {
		display: block;
	}

	._compare-box-item-image {
		margin-bottom: 10px;
	}

	._compare-box-item-info,
	._compare-box-item-title {
		padding: 0;
	}

	._header-variant-two {
		._user-controls {
			._user-controls-list-item-logout {
				display: none;
			}
		}
	}

	/* #Home
	-------------------------------------------------------------------------------*/

	._homepage-text {
		padding: 60px 0;

		._text {
			//h1 {
			//	font-size: 60px; /* theme */
			//	margin-bottom: 20px;
			//}

			//h2 {
			//	font-size: 40px; /* theme */
			//	margin-bottom: 20px;
			//}

			//h3 {
			//	font-size: 32px; /* theme */
			//}

			//h4 {
			//	font-size: 26px; /* theme */
			//}

			//h5 {
			//	font-size: 22px; /* theme */
			//}

			//h6 {
			//	font-size: 18px; /* theme */
			//}
		}
	}

	._showcase-brands {
		._showcase-item {
			width: 180px;
		}
	}

	._showcase-categories {
		._showcase {
			overflow: hidden;
			margin-left: -15px;
			margin-right: -15px;
		}

		._showcase-list {
			margin: 0;
			white-space: nowrap;
			font-size: 0;
		}

		._showcase-item {
			float: none;
			display: inline-block;
			margin: 0;
			width: calc(50% ~'-' 30px);
			font-size: @font-size-main; /* theme */
			white-space: normal;
		}
	}

	/* #Banners
	-------------------------------------------------------------------------------*/

	._banner-caption {
		padding: 7px;
		font-size: calc(@font-size-main ~'-' 1px); /* theme */
	}

	/* #Product list
	-------------------------------------------------------------------------------*/

	._product-bar {
		display: block;
		opacity: 1;
		visibility: visible;
		padding: 13px 20px;
	}

	._list-one,
	._list-horizontal {
		._product-bar {
			display: block;
			opacity: 1;
			visibility: visible;
		}
	}

	._filters-one {
		._filters-col-sidebar-button {
			display: block;
		}
	}

	._sidebar-main-button {
		display: block;
	}

	._sidebar-products,
	._sidebar-blog {
		display: none;
		background-color: @color-main-background; /* theme */
		border-right: 1px solid;
		border-color: @color-main-borders; /* theme */
		position: fixed;
		top: 0;
		left: 0;
		width: 340px;
		height: 100%;
		padding: 15px;
		z-index: @z-fixed-sidebar;
		overflow-y: scroll;
		-webkit-overflow-scrolling: touch;

		&.open {
			display: block;
		}

		._sidebar-button {
			display: block;
		}
	}

	/* #Product details
	-------------------------------------------------------------------------------*/

	._products-list-info {
		&-item {
			&-image {
				img {
					max-width: 218px;
				}
			}
		}
	}

	._product-details-title {
		display: none;
	}

	._product-details-pictures-container {
		margin-bottom: 12px;

		._product-details-title {
			display: block;
		}
	}

	._product-details-meta {
		border-bottom: 1px solid;
		border-color: @color-main-borders; /* theme */
		padding-bottom: 8px;
		margin-bottom: 10px;
	}

	._product-details-meta-item {
		margin-bottom: 0;
	}

	._product-details-price {
		font-size: 12px;
	}

	._product-details-price-saved {
		margin: 0;
	}

	._product-details-price-new {
		font-size: 30px;
	}

	._product-details-sticky-info {
		._product-details-sticky-button,
		._product-details-price-new {
			margin-left: 12px;
		}

		._product-details-price-new {
			font-size: 24px;
		}
	}

	._product-details-sticky-button {
		._button {
			._button-small();
		}
	}

	._product-details-relative {
		._products-list {
			._product {
				width: 50%;

				&:nth-child(3n+1) {
					clear: none;

					&:before {
						display: block;
					}
				}

				&:nth-child(2n+1) {
					clear: both;

					&:before {
						display: none;
					}
				}
			}
		}
	}

	._product-details-gallery-container {
		margin-top: 20px;
	}

	._product-details-gallery {
		ul {
			li {
				width: calc(14.28% ~'-' 15px);
			}
		}
	}

	._product-details-image {
		.lSAction > a {
			top: 50%;
		}
	}

	/* #Vendors
	-------------------------------------------------------------------------------*/

	._vendors-list {
		._vendor {
			width: 25%;

			&:nth-child(5n+1) {
				clear: none;
			}

			&:nth-child(4n+1) {
				clear: both;
			}
		}
	}

	/* #Account
	-------------------------------------------------------------------------------*/

	._sidebar-account {
		margin-bottom: @separator-small;
	}

	._sidebar-list-account {
		ul {
			display: table;
			table-layout: fixed;
			vertical-align: top;
			width: 100%;
		}

		li {
			display: table-cell;
			vertical-align: top;
			padding-right: 30px;
		}
	}

	/* #Page
	-------------------------------------------------------------------------------*/

	._sidebar-page {
		margin-top: 30px;
	}


	/* #Cart
	-------------------------------------------------------------------------------*/

	._cart-sidebar {
		margin-top: 25px;
	}

	/* #Checkout
	-------------------------------------------------------------------------------*/

	._checkout-express,
	._checkout-cart {
		[class*='col-'].pull-left,
		[class*='col-'].pull-right {
			float: none !important;
		}
	}

	._checkout-sidebar {
		margin-top: 25px;
	}

	/* #Footer
	-------------------------------------------------------------------------------*/

	._navigation-footer-list-item {
		width: 50%;
		
		&:nth-child(2n+1) {
			clear: both;
		}
	}

	._footer-legal-inner {
		display: block;
	}

	._footer-legal-left,
	._footer-legal-right {
		display: block;
		width: auto;
		text-align: center;
	}

	/* #Footer Variant Two
-------------------------------------------------------------------------------*/
	._footer-variant-two {
		._footer-cols {
			._text {
				width: 33.33%;

				+ ._navigation-footer {
					width: 66.66%;

					li {
						width: 50%;

						&:nth-child(3n+1) {
							clear: none;
						}

						&:nth-child(2n+1) {
							clear: both;
						}
					}
				}
			}
		}

		._navigation-footer {
			li {
				width: 33.33%;

				&:nth-child(4n+1) {
					clear: none;
				}

				&:nth-child(3n+1) {
					clear: both;
				}
			}
		}
	}
}