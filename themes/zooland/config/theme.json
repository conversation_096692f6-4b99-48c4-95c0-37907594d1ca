{"name": "zooland", "version": "0.1", "assets": {"default_images": {"logo": "", "favicon": "", "product": {"50": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "100": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "150": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "300": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "600": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "800": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "1024": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "1280": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "1920": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg"}, "vendor": {"50": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "100": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "150": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "300": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "600": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "800": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "1024": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "1280": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "1920": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg"}, "category": {"50": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "100": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "150": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "300": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "600": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "800": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "1024": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "1280": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg", "1920": "{$img_url}/themes/zooland/img/defaults/noimage/noimage.svg"}}, "default_content_images": {"product": ["{$img_url}/themes/zooland/img/defaults/products/product-01.png", "{$img_url}/themes/zooland/img/defaults/products/product-02.png", "{$img_url}/themes/zooland/img/defaults/products/product-03.png", "{$img_url}/themes/zooland/img/defaults/products/product-04.png", "{$img_url}/themes/zooland/img/defaults/products/product-05.png", "{$img_url}/themes/zooland/img/defaults/products/product-06.png", "{$img_url}/themes/zooland/img/defaults/products/product-07.png", "{$img_url}/themes/zooland/img/defaults/products/product-08.png"], "category": ["{$img_url}/themes/zooland/img/defaults/categories/category-01.jpg", "{$img_url}/themes/zooland/img/defaults/categories/category-02.jpg", "{$img_url}/themes/zooland/img/defaults/categories/category-03.jpg"], "vendor": ["{$img_url}/themes/zooland/img/defaults/brands/brand-01.jpg", "{$img_url}/themes/zooland/img/defaults/brands/brand-02.jpg", "{$img_url}/themes/zooland/img/defaults/brands/brand-03.jpg", "{$img_url}/themes/zooland/img/defaults/brands/brand-04.jpg", "{$img_url}/themes/zooland/img/defaults/brands/brand-05.jpg", "{$img_url}/themes/zooland/img/defaults/brands/brand-06.jpg"]}}, "functions": {"discount": {"color": {"status": true}}}, "widgets": {"navigationMain": {"map": "navigation.main"}, "navigationFooter": {"map": "navigation.footer"}, "carousel": {"map": "extra.carousel", "group": "slider", "name": {"en": "Slide<PERSON>", "bg": "Слайдер"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 2, "autoplay": "no", "interval": 5000, "caption": "no", "controls": "yes", "indicators": "yes", "pause": "no", "cycle": "no", "slides": {"1": {"img_type": "external", "src": "{$img_url}/themes/zooland/img/defaults/carousel/slider-1.png", "link_type": "section", "link_value": "products", "link_caption": "", "html": "", "horizontal_position": "center", "vertical_position": "middle", "target": "_self"}, "2": {"img_type": "external", "src": "{$img_url}/themes/zooland/img/defaults/carousel/slider-2.png", "link_type": "section", "link_value": "products", "link_caption": "", "html": "", "horizontal_position": "center", "vertical_position": "middle", "target": "_self"}}}}}, "categoryProperties": {"map": "product.categoryProperties", "name": {"en": "Category Properties", "bg": ""}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true}}}, "filters": {"map": "product.filters", "mode": "range_slider", "settings": {"defaults": {"per_page": "9", "per_page_options": [9, 18, 36, 72], "products_price_ranges": [[100, 100000]], "price_range_step": "500"}}, "name": {"en": "Product listing settings", "bg": "Настройки на продуктов каталог"}, "description": {"en": "", "bg": ""}}, "productsRelated": {"map": "product.related", "name": {"en": "Related Products 1", "bg": "Свързани продукти 1"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Related products", "products": 4, "type": "category"}}}, "firstShowcaseProducts": {"map": "product.productShowcase", "name": {"en": "First Products Showcase", "bg": "Първа витрина с продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "New Products for Cats", "products": 8}}}, "secondShowcaseProducts": {"map": "product.productShowcase", "name": {"en": "Second Products Showcase", "bg": "Втора витрина с продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "New Products for Dogs", "products": 8}}}, "dealsShowcaseProductsBackground": {"map": "extra.backgroundImage", "group": "banners", "name": {"en": "Home today deals section background image ", "bg": "Картинка на секцията с днешните промоции на началата страница."}, "settings": {"defaults": {"img_type": "external", "src": "{$img_url}/themes/zooland/img/defaults/backgrounds/deals.jpg"}}}, "dealsShowcaseProducts": {"map": "product.productShowcase", "name": {"en": "Deals Products Showcase", "bg": "Витрина оферти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"title": "Today Deals", "products": 6}}}, "productsDetails": {"map": "product.productsDetails", "name": {"en": "Detail information for product", "bg": "Детайлна информация за продукт"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {}}}, "showcaseCategory": {"map": "product.showcase", "name": {"en": "Category Showcase", "bg": "Витрина с категории"}, "settings": {"defaults": {"header": "Categories", "type": "category", "amount": 3, "show_name": true, "show_description": true}}}, "homeWelcomeBackground": {"map": "extra.backgroundImage", "group": "banners", "name": {"en": "Home welcome section background image", "bg": "Картинка на секцията с \"Добре дошли\" на началата страница."}, "settings": {"defaults": {"img_type": "external", "src": "{$img_url}/themes/zooland/img/defaults/backgrounds/welcome.jpg"}}}, "homeWelcome": {"map": "extra.text", "group": "text_fields", "name": {"en": "Welcome to our online store", "bg": "Добре дошли"}, "settings": {"defaults": {"title": "Welcome to handmade shop", "text": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus ut nibh vitae justo dignissim consequat eu a nunc. Ut placerat sem id tincidunt fermentum. In dictum fermentum dolor sit amet efficitur. Phasellus sed vehicula ex, eu finibus felis. Etiam vel augue eu tellus vulputate maximus id ac orci. Integer sagittis felis quis gravida commodo. Pellentesque ac bibendum mauris, vitae efficitur dolor.</p><ul><li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li><li>Vivamus ut nibh vitae justo dignissim consequat eu eu finibus felisa nunc. </li><li>Ut placerat sem id tincidunt fermentum.</li></ul><p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus ut nibh vitae justo dignissim consequat eu a nunc. Ut placerat sem id tincidunt fermentum.</p>"}}}, "productCustomText1": {"map": "extra.text", "group": "text_fields", "name": {"en": "Product page custom text 1", "bg": "Дета<PERSON><PERSON>на страница допълнителен текст 1"}, "settings": {"defaults": {"title": "Not showing", "text": "<p>Buy for <span style=\"color: #80af00;\"><strong>$47.78</strong></span> more to<br /> get your free delivery</p>"}}}, "productCustomText2": {"map": "extra.text", "group": "text_fields", "name": {"en": "Product page custom text 2", "bg": "Дета<PERSON><PERSON>на страница допълнителен текст 2"}, "settings": {"defaults": {"title": "Not showing", "text": "<p><span style=\"color: #00aeef;\"><strong>24/7</strong></span> free support<br />Call us: 1 800-456-789-22</p>"}}}, "showcaseBrand": {"map": "product.showcase", "name": {"en": "Brands Showcase", "bg": "Витрина с марки"}, "settings": {"defaults": {"header": "", "type": "vendor", "amount": 8, "show_name": true, "show_description": false}}}, "page": {"map": "extra.page"}, "vendors": {"map": "product.vendors"}, "search": {"map": "extra.search", "group": "header", "name": {"en": "Global Search", "bg": "Глобално търсене"}, "description": {"en": "A search form for your site", "bg": "Форма за търсене"}}, "banners": {"map": "extra.banner", "group": "banners", "name": {"en": "Banners", "bg": "Банери"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 2, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zooland/img/defaults/banners/banner-01.jpg", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zooland/img/defaults/banners/banner-02.jpg", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "faqBanners": {"map": "extra.banner", "group": "banners", "name": {"en": "Banners on faq page", "bg": "Банери на FAQ страница"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 1, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zooland/img/defaults/banners/banner-page-01.jpg", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "pageBanners": {"map": "extra.banner", "group": "banners", "name": {"en": "Banners on page", "bg": "Банери на страница"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 1, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zooland/img/defaults/banners/banner-page-02.jpg", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zooland/img/defaults/banners/banner-page-03.jpg", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "productSideText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Product page sidebar text", "bg": "Текст в страничната колона на продукта"}, "settings": {"defaults": {"title": "Title", "text": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc consectetur rutrum urna, et viverra nisi facilisis non"}}}, "productPageBanners": {"map": "extra.banner", "group": "banners", "name": {"en": "Banners on product page", "bg": "Банери на продуктова страница"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "amount": 1, "banners": {"1": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zooland/img/defaults/banners/banner-page-02.jpg", "link_type": "", "link_value": "", "caption": "", "target": "_self"}, "2": {"type": "image", "img_type": "external", "src": "{$img_url}/themes/zooland/img/defaults/banners/banner-page-03.jpg", "link_type": "", "link_value": "", "caption": "", "target": "_self"}}}}}, "logo": {"map": "extra.logo", "editable": "no"}, "headerSlogan": {"map": "extra.text", "group": "header", "name": {"en": "Slogan text", "bg": "Дев<PERSON><PERSON>"}, "settings": {"defaults": {"title": "Not visible", "text": "online shop for PET FOOD & ACCESSORIES"}}}, "headerFreeShipping": {"map": "extra.text", "group": "header", "name": {"en": "Header text field 1", "bg": "Заглавна част тескст 1"}, "settings": {"defaults": {"title": "Free shipping", "text": "on orders over $99"}}}, "headerReturns": {"map": "extra.text", "group": "header", "name": {"en": "Header text field 2", "bg": "Заг<PERSON><PERSON>вна част тескст 2"}, "settings": {"defaults": {"title": "30 day returns", "text": "30 days money back guarantee"}}}, "headerSupport": {"map": "extra.text", "group": "header", "name": {"en": "Header text field 3", "bg": "Заг<PERSON>авна част тескст 3"}, "settings": {"defaults": {"title": "24/7 support", "text": "Call us: 1 800-456-789-22"}}}, "userControls": {"map": "user.controls"}, "wishlistMenu": {"map": "wishlist.menu", "name": {"en": "Menu for latest favorites products", "bg": "Меню за последни любими продукти"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"enabled": true, "limit": 10}}}, "wishlist": {"map": "wishlist.listing"}, "googleMap": {"map": "contact.googleMap", "name": {"en": "Google Map", "bg": "<PERSON>у<PERSON><PERSON>л карта"}, "description": {"en": "", "bg": ""}}, "contactForm": {"map": "contact.form"}, "contactInformation": {"map": "contact.information", "name": {"en": "Contact Information", "bg": "Контактна информация"}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"show_form": "yes", "show_custom_information": "yes", "custom_information": "<h3>email</h3><p><a href=\"#\"><EMAIL></a></p><h3>phone</h3><p>TOLL-FREE at 855-HNM-SHOP&nbsp;<br />(************)</p><h3>Opening hours</h3><p>Monday - Sunday: 8am - 01am EST</p>"}}}, "authorize": {"map": "user.authorize"}, "social": {"map": "extra.social", "group": "footer", "name": {"en": "Footer Social links", "bg": "Социални връзки в заключителната част на страницата"}, "description": {"en": "", "bg": ""}}, "headerSocial": {"map": "extra.social", "group": "header", "name": {"en": "Header Social links", "bg": "Социални връзки в заглавната част на страницата"}, "description": {"en": "", "bg": ""}}, "summaryAdditionalInfo": {"map": "extra.text", "group": "text_fields", "name": {"en": "Summary box text", "bg": "Информация в количката за цените"}, "settings": {"defaults": {"title": "Prices information", "text": "Prices and delivery costs are not confirmed until youe reached the checkout. 30 days withdrawal. Read about our return and refund policy here return and refund."}}}, "checkoutSideText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Cart side text", "bg": "Текст в страничната колона в количката"}, "settings": {"defaults": {"title": "RETURN AND REFUND", "text": "You will need to arrange the return shipment to us yourself and bear the cost for the courier. Find out more about returns and refunds here. You will not get a fa piao unless you request it. Find out more about fa piao here."}}}, "checkoutSignInLoginText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Checkout sing in - Login text", "bg": "Текст в количката при Login"}, "settings": {"defaults": {"title": "Not visible", "text": "Welcome back! Please sign in using your email address and password below."}}}, "checkoutSignInRegisterText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Checkout sing in - Register text", "bg": "Текст в количката при Register"}, "settings": {"defaults": {"title": "Not visible", "text": "Create an account to check out faster in the future and receive emails about your order new products, events and special offers!"}}}, "checkoutSignInGuestText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Checkout sing in - Guest text", "bg": "Текст в количката при Guest"}, "settings": {"defaults": {"title": "Not visible", "text": "You do not need to create an account to check out. You can check out as a Guest and create an account later."}}}, "providers": {"map": "payment.providers"}, "article": {"map": "blog.article"}, "blog": {"map": "blog.blog", "name": {"en": "Blog", "bg": ""}, "description": {"en": "", "bg": ""}, "settings": {"defaults": {"per_page": 6}}}, "recentArticles": {"map": "blog.recentArticles", "name": {"en": "Recent Articles", "bg": "Последни статии"}, "description": {"en": "", "bg": ""}}, "recentComments": {"map": "blog.recentComments", "name": {"en": "Recent Comments", "bg": "Последни коментари"}, "description": {"en": "", "bg": ""}}, "utilities": {"map": "base.utilities"}, "share": {"map": "extra.addThisShare", "group": "products", "name": {"en": "Social Share", "bg": ""}, "description": {"en": "", "bg": ""}}, "sharrre": {"map": "extra.sharrreShare"}, "footerText": {"map": "extra.text", "group": "text_fields", "name": {"en": "Footer text", "bg": "Футър текст"}, "settings": {"defaults": {"title": "Contact Us", "text": "<p>9350 Wilshire Blvd #203, Beverly Hills, CA 90212</p><p><a href='mailto:<EMAIL>'><EMAIL></a></p><p>Telephone: <tel>+359 886 77 18 77</tel></p><p>Telephone: <tel>+359 886 77 18 77</tel></p>"}}}, "newsletter": {"map": "mailchimp.newsletter", "name": {"en": "Newsletter (Mailchimp)", "bg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>н"}, "description": {"en": "Newsletter form for your site", "bg": "Форма за бюлетин"}}, "leasing": {"map": "store.leasing"}}, "globals": {"widgets": {"product": {"listing": {}, "details": {}, "showcase": {"settings": {"defaults": {"products": 4}}}}}}, "settings": {"groups": {"main": {"en": {"name": "Main"}, "bg": {"name": "Основни"}}, "header": {"en": {"name": "Header"}, "bg": {"name": "Заг<PERSON>авна лента"}}, "footer": {"en": {"name": "Footer"}, "bg": {"name": "Долна лента"}}, "topbar": {"en": {"name": "Topbar"}, "bg": {"name": "Най-горна лента"}}, "cart": {"en": {"name": "<PERSON><PERSON>"}, "bg": {"name": "Количка"}}, "slider": {"en": {"name": "Slide<PERSON>"}, "bg": {"name": "Слай<PERSON>ър"}}, "showcases": {"en": {"name": "Showcases"}, "bg": {"name": "Витрини"}}, "deals-showcase": {"en": {"name": "Deals Showcase"}, "bg": {"name": "Витрина с отстъпки"}}, "products": {"en": {"name": "Products"}, "bg": {"name": "Продукти"}}, "homepage": {"en": {"name": "Homepage"}, "bg": {"name": "Нача<PERSON>на страница"}}, "sidebar": {"en": {"name": "Sidebar"}, "bg": {"name": "Стран<PERSON><PERSON>на колона"}}, "buttons": {"en": {"name": "Buttons"}, "bg": {"name": "Бутони"}}, "labels": {"en": {"name": "Labels"}, "bg": {"name": "Етикети"}}}, "variables": {"color-main-titles": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "Titles"}, "bg": {"name": "Заглавия"}}}, "color-main-text": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-main-highlight": {"type": "color", "group": "main", "default": "#fc6205", "translations": {"en": {"name": "Highlight"}, "bg": {"name": "Акцент"}}}, "color-main-secondary-text": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Secondary Text"}, "bg": {"name": "Второстепенен текст"}}}, "color-main-tertiary-text": {"type": "color", "group": "main", "default": "#666", "translations": {"en": {"name": "Tertiary Text"}, "bg": {"name": "Третостепенен текст"}}}, "color-main-meta-text": {"type": "color", "group": "main", "default": "#999", "translations": {"en": {"name": "Meta <PERSON>t"}, "bg": {"name": "Помощен текст"}}}, "color-main-background": {"separator": "true", "type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-main-secondary-background": {"type": "color", "group": "main", "default": "#f7f7f7", "translations": {"en": {"name": "Secondary Background"}, "bg": {"name": "Второстепенен фон"}}}, "color-main-borders": {"type": "color", "group": "main", "default": "#dcdcdc", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамки"}}}, "color-main-icons": {"type": "color", "group": "main", "default": "#cccccc", "translations": {"en": {"name": "Icons"}, "bg": {"name": "Икони"}}}, "color-main-fields-text": {"separator": "true", "type": "color", "group": "main", "default": "#999999", "translations": {"en": {"name": "Input Fields Text"}, "bg": {"name": "Полета за въвеждане - текст"}}}, "color-main-fields-background": {"type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Input Fields Background"}, "bg": {"name": "Полета за въвеждане - фон"}}}, "color-main-fields-border": {"type": "color", "group": "main", "default": "#e3e3e3", "translations": {"en": {"name": "Input Fields Border"}, "bg": {"name": "Полета за въвеждане - рамка"}}}, "color-main-fields-active": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "Active field"}, "bg": {"name": "Избрано поле"}}}, "color-main-welcome-text": {"separator": "true", "type": "color", "group": "main", "default": "#fff", "translations": {"en": {"name": "Image section - text"}, "bg": {"name": "Секция със снимка - текст"}}}, "color-main-welcome-accent": {"type": "color", "group": "main", "default": "#fc6205", "translations": {"en": {"name": "Image section - accent"}, "bg": {"name": "Секция със снимка - акцент"}}}, "color-main-blog-latest-text": {"separator": "true", "type": "color", "group": "main", "default": "#666", "translations": {"en": {"name": "Latest articles - text"}, "bg": {"name": "Последни статии - текст"}}}, "color-main-blog-latest-title": {"type": "color", "group": "main", "default": "#333", "translations": {"en": {"name": "Latest articles - title"}, "bg": {"name": "Последни статии - заглавия"}}}, "color-main-blog-latest-highlight": {"type": "color", "group": "main", "default": "#fc6205", "translations": {"en": {"name": "Latest articles - highlight"}, "bg": {"name": "Последни статии - акцент"}}}, "color-main-blog-latest-background": {"type": "color", "group": "main", "default": "#f7f7f7", "translations": {"en": {"name": "Latest articles - background"}, "bg": {"name": "Последни статии - фон"}}}, "color-main-blog-latest-borders": {"type": "color", "group": "main", "default": "#dcdcdc", "translations": {"en": {"name": "Latest articles - borders"}, "bg": {"name": "Последни статии - линии"}}}, "color-main-breadcrumbs-text": {"separator": "true", "type": "color", "group": "main", "default": "#cccccc", "translations": {"en": {"name": "Breadcrumbs - text"}, "bg": {"name": "Пътечка с линкове - текст"}}}, "color-main-breadcrumbs-text-active": {"type": "color", "group": "main", "default": "#989898", "translations": {"en": {"name": "Breadcrumbs - active text"}, "bg": {"name": "Пътечка с линкове - активен текст"}}}, "color-main-order-status-pending": {"separator": "true", "type": "color", "group": "main", "default": "#ffa800", "translations": {"en": {"name": "Order status - pending text"}, "bg": {"name": "Статус на поръчка - изчакващ"}}}, "color-main-order-status-complete": {"type": "color", "group": "main", "default": "#79b12d", "translations": {"en": {"name": "Order status - complete text"}, "bg": {"name": "Статус на поръчка - изпълнена"}}}, "color-header-background": {"type": "color", "group": "header", "default": "#ffffff", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-header-text": {"type": "color", "group": "header", "default": "#666666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-header-title": {"type": "color", "group": "header", "default": "#00aeef", "translations": {"en": {"name": "Title"}, "bg": {"name": "Заглавия"}}}, "color-header-secondary-title": {"type": "color", "group": "header", "default": "#9ad202", "translations": {"en": {"name": "Secondary title"}, "bg": {"name": "Второстепени заглавия"}}}, "color-header-tertiary-title": {"type": "color", "group": "header", "default": "#fc6205", "translations": {"en": {"name": "Tertiary title"}, "bg": {"name": "Третостепени заглавия"}}}, "color-header-navigation-background": {"separator": "true", "type": "color", "group": "header", "default": "#fc6205", "translations": {"en": {"name": "Navigation background"}, "bg": {"name": "Навигация фон"}}}, "color-header-navigation-background-active": {"type": "color", "group": "header", "default": "#ff7928", "translations": {"en": {"name": "Navigation background - active"}, "bg": {"name": "Навигация фон - активен"}}}, "color-header-navigation-text": {"type": "color", "group": "header", "default": "#ffffff", "translations": {"en": {"name": "Navigation text"}, "bg": {"name": "Навигация текст"}}}, "color-header-navigation-text-active": {"type": "color", "group": "header", "default": "#fff", "translations": {"en": {"name": "Navigation text - active"}, "bg": {"name": "Навигация текст - активен"}}}, "color-header-navigation-submenu-background": {"separator": "true", "type": "color", "group": "header", "default": "#f7f7f7", "translations": {"en": {"name": "Submenu - background"}, "bg": {"name": "Подменю - фон"}}}, "color-header-navigation-submenu-title": {"type": "color", "group": "header", "default": "#333333", "translations": {"en": {"name": "Submenu - titles"}, "bg": {"name": "Подменю - заглавия"}}}, "color-header-navigation-submenu-text": {"type": "color", "group": "header", "default": "#666666", "translations": {"en": {"name": "Submenu - text"}, "bg": {"name": "Подменю - текст"}}}, "color-header-navigation-submenu-text-active": {"type": "color", "group": "header", "default": "#fc6205", "translations": {"en": {"name": "Submenu - active text"}, "bg": {"name": "Подменю - активен текст"}}}, "color-header-mobile-background": {"separator": "true", "type": "color", "group": "header", "default": "#f7f7f7", "translations": {"en": {"name": "Mobile menu - background"}, "bg": {"name": "Мобилно меню - фон"}}}, "color-header-mobile-text": {"type": "color", "group": "header", "default": "#333333", "translations": {"en": {"name": "Mobile menu - text"}, "bg": {"name": "Мобилно меню - текст"}}}, "color-footer-text": {"type": "color", "group": "footer", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-footer-active": {"type": "color", "group": "footer", "default": "#fc6205", "translations": {"en": {"name": "Text - hover"}, "bg": {"name": "Текст - активен"}}}, "color-footer-separator": {"type": "color", "group": "footer", "default": "#bae053", "translations": {"en": {"name": "Separator"}, "bg": {"name": "Разделител"}}}, "color-footer-background": {"type": "color", "group": "footer", "default": "#9ad202", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-topbar-text": {"type": "color", "group": "topbar", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-topbar-text-active": {"type": "color", "group": "topbar", "default": "#fff", "translations": {"en": {"name": "Text - active"}, "bg": {"name": "Текст - активен"}}}, "color-topbar-background": {"type": "color", "group": "topbar", "default": "#9ad202", "translations": {"en": {"name": "Background"}, "bg": {"name": "Фон"}}}, "color-cart-bubble-background": {"type": "color", "group": "cart", "default": "#fc6205", "translations": {"en": {"name": "Bubble background"}, "bg": {"name": "Фон на броя"}}}, "color-cart-bubble-text": {"type": "color", "group": "cart", "default": "#fff", "translations": {"en": {"name": "Bubble text"}, "bg": {"name": "Текст на броя"}}}, "color-cart-dropdown-background": {"separator": "true", "type": "color", "group": "cart", "default": "#f7f7f7", "translations": {"en": {"name": "Dropdown - background"}, "bg": {"name": "Подменю - фон"}}}, "color-cart-dropdown-text": {"type": "color", "group": "cart", "default": "#666666", "translations": {"en": {"name": "Dropdown - text"}, "bg": {"name": "Подменю - текст"}}}, "color-cart-dropdown-price": {"type": "color", "group": "cart", "default": "#333333", "translations": {"en": {"name": "Dropdown - price"}, "bg": {"name": "Подменю - цена"}}}, "color-cart-dropdown-remove": {"type": "color", "group": "cart", "default": "#cccccc", "translations": {"en": {"name": "Dropdown - remove"}, "bg": {"name": "Подменю - изтрий"}}}, "color-cart-dropdown-separator": {"type": "color", "group": "cart", "default": "#e7e5e3", "translations": {"en": {"name": "Dropdown - separator"}, "bg": {"name": "Подменю - разделител"}}}, "color-cart-dropdown-button-background": {"separator": "true", "type": "color", "group": "cart", "default": "#fc6205", "translations": {"en": {"name": "Dropdown button - background"}, "bg": {"name": "Подменю бутон - фон"}}}, "color-cart-dropdown-button-text": {"type": "color", "group": "cart", "default": "#fff", "translations": {"en": {"name": "Dropdown button - text"}, "bg": {"name": "Подменю бутон - текст"}}}, "color-cart-dropdown-button-background-active": {"type": "color", "group": "cart", "default": "#e85700", "translations": {"en": {"name": "Dropdown button - active background"}, "bg": {"name": "Подменю бутон - активен фон"}}}, "color-cart-dropdown-button-text-active": {"type": "color", "group": "cart", "default": "#fff", "translations": {"en": {"name": "Dropdown button - active text"}, "bg": {"name": "Подменю бутон - активен текст"}}}, "color-slider-text": {"type": "color", "group": "slider", "default": "#999999", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-slider-title": {"type": "color", "group": "slider", "default": "#fc6205", "translations": {"en": {"name": "Title"}, "bg": {"name": "Заглавие"}}}, "color-slider-button": {"type": "color", "group": "slider", "default": "#fc6205", "translations": {"en": {"name": "Action button"}, "bg": {"name": "Основен бутон"}}}, "color-slider-navigation-button-arrow": {"type": "color", "group": "slider", "default": "#fc6205", "translations": {"en": {"name": "Navigation arrow"}, "bg": {"name": "Навигацио<PERSON>на стрелка"}}}, "color-slider-navigation-button": {"type": "color", "group": "slider", "default": "#e2e2e2", "translations": {"en": {"name": "Navigation button"}, "bg": {"name": "Навигационен бутон"}}}, "color-slider-navigation-button-hover": {"type": "color", "group": "slider", "default": "#9ad202", "translations": {"en": {"name": "Navigation button - active"}, "bg": {"name": "Навигацио<PERSON><PERSON>н бутон - активен"}}}, "color-showcases-category-text": {"type": "color", "group": "showcases", "default": "#fff", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-showcases-slider-arrow-background": {"type": "color", "group": "showcases", "default": "#000", "translations": {"en": {"name": "Navigation slider - background of the arrow"}, "bg": {"name": "Навигация на слайдъра - фон на стрелка"}}}, "color-showcases-slider-arrow": {"type": "color", "group": "showcases", "default": "#fff", "translations": {"en": {"name": "Navigation slider - arrow"}, "bg": {"name": "Навигация на слайдъра - стрелка"}}}, "color-deals-products-title": {"type": "color", "group": "deals-showcase", "default": "#fff", "translations": {"en": {"name": "Title"}, "bg": {"name": "Заглавие"}}}, "color-deals-products-accent": {"type": "color", "group": "deals-showcase", "default": "#fc6205", "translations": {"en": {"name": "Accent"}, "bg": {"name": "Акцент"}}}, "color-deals-products-single-background": {"type": "color", "group": "deals-showcase", "default": "#fff", "translations": {"en": {"name": "Product - background"}, "bg": {"name": "Продукт - фон"}}}, "color-deals-products-single-title": {"type": "color", "group": "deals-showcase", "default": "#666666", "translations": {"en": {"name": "Product - title"}, "bg": {"name": "Продукт - заглавие"}}}, "color-deals-products-single-price": {"type": "color", "group": "deals-showcase", "default": "#333333", "translations": {"en": {"name": "Product - price"}, "bg": {"name": "Продукт - цена"}}}, "color-deals-products-single-old-price-text": {"type": "color", "group": "deals-showcase", "default": "#999999", "translations": {"en": {"name": "Product - old price"}, "bg": {"name": "Продукт - стара цена"}}}, "color-deals-products-single-new-price-text": {"type": "color", "group": "deals-showcase", "default": "#fc6205", "translations": {"en": {"name": "Product - new price"}, "bg": {"name": "Продукт - нова цена"}}}, "color-products-list-title": {"type": "color", "group": "products", "default": "#666666", "translations": {"en": {"name": "Title"}, "bg": {"name": "Заглавие"}}}, "color-products-list-text": {"type": "color", "group": "products", "default": "#333333", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-products-list-old-price-text": {"type": "color", "group": "products", "default": "#999999", "translations": {"en": {"name": "Old price"}, "bg": {"name": "Стара цена"}}}, "color-products-list-new-price-text": {"type": "color", "group": "products", "default": "#fc6205", "translations": {"en": {"name": "New price"}, "bg": {"name": "Нова цена"}}}, "color-products-list-image-background": {"type": "color", "group": "products", "default": "#fafafa", "translations": {"en": {"name": "Image background"}, "bg": {"name": "Фон на снимката"}}}, "color-products-list-box-border": {"type": "color", "group": "products", "default": "#fc6205", "translations": {"en": {"name": "Borders"}, "bg": {"name": "Рамки"}}}, "color-products-details-specifications-title": {"type": "color", "group": "products", "default": "#534741", "translations": {"en": {"name": "Specifications - title"}, "bg": {"name": "Спецификации - заглавия"}}}, "color-sidebar-text": {"type": "color", "group": "sidebar", "default": "#666", "translations": {"en": {"name": "Text"}, "bg": {"name": "Текст"}}}, "color-sidebar-range-slider-background": {"type": "color", "group": "sidebar", "default": "#e5e5e5", "translations": {"en": {"name": "Price range - background"}, "bg": {"name": "Филтър с цена - фон"}}}, "color-sidebar-select-text": {"type": "color", "group": "sidebar", "default": "#666666", "translations": {"en": {"name": "Select - text"}, "bg": {"name": "Падащо меню - текст"}}}, "color-sidebar-select-icon": {"type": "color", "group": "sidebar", "default": "#989898", "translations": {"en": {"name": "Select - arrow"}, "bg": {"name": "Падащо меню - стрелка"}}}, "color-sidebar-select-border": {"type": "color", "group": "sidebar", "default": "#dedede", "translations": {"en": {"name": "Select - border"}, "bg": {"name": "Падащо меню - рамки"}}}, "color-sidebar-select-background": {"type": "color", "group": "sidebar", "default": "#f1f1f1", "translations": {"en": {"name": "Select - background"}, "bg": {"name": "Падащо меню - фон"}}}, "color-button-text": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Primary - Text"}, "bg": {"name": "Основен - текст"}}}, "color-button-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Primary - Text Hover"}, "bg": {"name": "Основен - текст акцент"}}}, "color-button-background": {"type": "color", "group": "buttons", "default": "#fc6205", "translations": {"en": {"name": "Primary - Background"}, "bg": {"name": "Основен - фон"}}}, "color-button-background-hover": {"type": "color", "group": "buttons", "default": "#e85700", "translations": {"en": {"name": "Primary - <PERSON>ver"}, "bg": {"name": "Основен - фон акцент"}}}, "color-button-border": {"type": "color", "group": "buttons", "default": "#fc6205", "translations": {"en": {"name": "Primary - Border"}, "bg": {"name": "Основен - Рамка"}}}, "color-button-border-hover": {"type": "color", "group": "buttons", "default": "#e85700", "translations": {"en": {"name": "Primary - <PERSON> Hover"}, "bg": {"name": "Основен - рамка акцент"}}}, "color-button-secondary-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Secondary - Text"}, "bg": {"name": "Вторичен - текст"}}}, "color-button-secondary-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Secondary - Text Hover"}, "bg": {"name": "Вторичен - текст акцент"}}}, "color-button-secondary-background": {"type": "color", "group": "buttons", "default": "#9ad202", "translations": {"en": {"name": "Secondary - Background"}, "bg": {"name": "Вторичен - фон"}}}, "color-button-secondary-background-hover": {"type": "color", "group": "buttons", "default": "#8cbf00", "translations": {"en": {"name": "Secondary - Background Hover"}, "bg": {"name": "Вторичен - фон акцент"}}}, "color-button-secondary-border": {"type": "color", "group": "buttons", "default": "#9ad202", "translations": {"en": {"name": "Secondary - Border"}, "bg": {"name": "Вторичен - рамка"}}}, "color-button-secondary-border-hover": {"type": "color", "group": "buttons", "default": "#8cbf00", "translations": {"en": {"name": "Secondary - Border Hover"}, "bg": {"name": "Вторичен - рамка акцент"}}}, "color-button-tertiary-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#fc6205", "translations": {"en": {"name": "Tertiary - Text"}, "bg": {"name": "Третостепенен - текст"}}}, "color-button-tertiary-text-hover": {"type": "color", "group": "buttons", "default": "#ffffff", "translations": {"en": {"name": "Tertiary - Text Hover"}, "bg": {"name": "Третостепенен - текст акцент"}}}, "color-button-tertiary-border": {"type": "color", "group": "buttons", "default": "#fc6205", "translations": {"en": {"name": "Tertiary - Border"}, "bg": {"name": "Третостепенен - рамка"}}}, "color-button-tertiary-border-hover": {"type": "color", "group": "buttons", "default": "#fc6205", "translations": {"en": {"name": "Tertiary - Border Hover"}, "bg": {"name": "Третостепенен - рамка акцент"}}}, "color-button-tertiary-background-hover": {"type": "color", "group": "buttons", "default": "#fc6205", "translations": {"en": {"name": "Tertiary - <PERSON> Hover"}, "bg": {"name": "Третостепенен - фон акцент"}}}, "color-button-disable-background": {"separator": "true", "type": "color", "group": "buttons", "default": "#f1f1f1", "translations": {"en": {"name": "Disable - Background"}, "bg": {"name": "Неактивен - фон"}}}, "color-button-disable-background-hover": {"type": "color", "group": "buttons", "default": "#dddddd", "translations": {"en": {"name": "Disable - <PERSON>ver"}, "bg": {"name": "Неактивен - фон акцент"}}}, "color-button-disable-border": {"type": "color", "group": "buttons", "default": "#f1f1f1", "translations": {"en": {"name": "Disable - Border"}, "bg": {"name": "Неактивен - рамки"}}}, "color-button-disable-border-hover": {"type": "color", "group": "buttons", "default": "#dddddd", "translations": {"en": {"name": "Disable - Border Hover"}, "bg": {"name": "Неактивен - рамки акцент"}}}, "color-button-disable-text": {"type": "color", "group": "buttons", "default": "#999999", "translations": {"en": {"name": "Disable - Text"}, "bg": {"name": "Неактивен - текст"}}}, "color-button-disable-text-hover": {"type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Disable - Text Hover"}, "bg": {"name": "Неактивен - текст акцент"}}}, "color-button-active-text": {"separator": "true", "type": "color", "group": "buttons", "default": "#fff", "translations": {"en": {"name": "Active - Text"}, "bg": {"name": "Активен - текст"}}}, "color-button-active-background": {"type": "color", "group": "buttons", "default": "#e85700", "translations": {"en": {"name": "Active - Background"}, "bg": {"name": "Активен - фон"}}}, "color-button-active-border": {"type": "color", "group": "buttons", "default": "#e85700", "translations": {"en": {"name": "Active - Border"}, "bg": {"name": "Активен - рамка"}}}, "color-label-new-text": {"type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "New - Text"}, "bg": {"name": "Нов - текст"}}}, "color-label-new-background": {"type": "color", "group": "labels", "default": "#00aeef", "translations": {"en": {"name": "New - Background"}, "bg": {"name": "Нов - фон"}}}, "color-label-sale-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Sale - Text"}, "bg": {"name": "Разпродажба - текст"}}}, "color-label-sale-background": {"type": "color", "group": "labels", "default": "#9ad202", "translations": {"en": {"name": "Sale - Background"}, "bg": {"name": "Разпродажба - фон"}}}, "color-label-discount-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Discount - Text"}, "bg": {"name": "Отстъпка - текст"}}}, "color-label-discount-background": {"type": "color", "group": "labels", "default": "#fc6205", "translations": {"en": {"name": "Discount - Background"}, "bg": {"name": "Отстъпка - фон"}}}, "color-label-leasing-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Leasing - Text"}, "bg": {"name": "Лизин<PERSON> - текст"}}}, "color-label-leasing-background": {"type": "color", "group": "labels", "default": "#fc6205", "translations": {"en": {"name": "Leasing - Background"}, "bg": {"name": "Лизинг - фон"}}}, "color-label-featured-text": {"separator": "true", "type": "color", "group": "labels", "default": "#fff", "translations": {"en": {"name": "Featured - Text"}, "bg": {"name": "Препоръчан - текст"}}}, "color-label-featured-background": {"type": "color", "group": "labels", "default": "#fc6205", "translations": {"en": {"name": "Featured - Background"}, "bg": {"name": "Препоръчан - фон"}}}}}}