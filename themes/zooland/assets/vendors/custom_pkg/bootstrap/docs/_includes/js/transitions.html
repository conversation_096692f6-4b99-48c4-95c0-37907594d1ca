<div class="bs-docs-section">
  <h1 id="transitions" class="page-header">Transitions <small>transition.js</small></h1>

  <h2>About transitions</h2>
  <p>For simple transition effects, include <code>transition.js</code> once alongside the other JS files. If you're using the compiled (or minified) <code>bootstrap.js</code>, there is no need to include this&mdash;it's already there.</p>

  <h2>What's inside</h2>
  <p>Transition.js is a basic helper for <code>transitionEnd</code> events as well as a CSS transition emulator. It's used by the other plugins to check for CSS transition support and to catch hanging transitions.</p>

  <h2>Disabling transitions</h2>
  <p>Transitions can be globally disabled using the following JavaScript snippet, which must come after <code>transition.js</code> (or <code>bootstrap.js</code> or <code>bootstrap.min.js</code>, as the case may be) has loaded:</p>
{% highlight js %}
$.support.transition = false
{% endhighlight %}
</div>
