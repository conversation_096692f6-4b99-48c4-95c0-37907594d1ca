/*=============================================================================*\
    FIGURES
\*=============================================================================*/

._figure-stack {
    display: inline-table;
    height: 100%;
    ._figure-stack-icon,
    ._figure-stack-label {
        display: table-cell;
        vertical-align: middle;
        line-height: normal;
        & + [class*='-label'],
        & + [class*='-icon'] {
            padding-left: 10px;
        }
    }
    ._figure-stack-icon {
        text-align: center;
    }
}

/*  FONT AWESOME ICONS
-------------------------------------------------------------------------------*/

.fa,
.glyphicon {
    &:before {
        & when (@rtl) {
            transform: scaleX(-1);
            display: inline-block;
        }
    }
}