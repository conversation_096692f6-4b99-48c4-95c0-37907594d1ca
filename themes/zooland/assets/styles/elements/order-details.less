/*=============================================================================*\
    ORDER DETAILS
\*=============================================================================*/


/* Orders accordion */
._orders-accordion {
    &__single-row {
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: @color-main-borders; /* theme */
    }

    &__title {
        display: flex;
        position: relative;
        padding: 15px 0 14px;
        cursor: pointer;
        font-weight: @normal;
        align-items: center;

        &:after {
            font-family: FontAwesome;
            content: '\f078';
            color: @color-main-icons; /* theme */
            font-weight: @normal;
            display: block;
            .centerer(false, true);
            right: 0;
            transition: transform .2s ease;
        }

        span {
            color: @color-main-meta-text; /* theme */

            .time {
                display: none;
            }

            &._order-date {
                flex: 1 1 25%;
                min-width: 25%;
                max-width: 25%;

                & when (@rtl) {
                    text-align: left;
                }
            }

            &[class*="status-fulfilment-"] {
                flex: 1 1 25%;
                min-width: 25%;
                max-width: 25%;
                text-align: right;
                padding-right: 65px;
            }

            &._order-id {
                flex: 1 1 50%;
                min-width: 50%;
                max-width: 50%;
                -ltr-text-transform: uppercase;

                & when (@rtl) {
                    text-align: left;
                }
            }
        }

        &.opened {
            &:after {
                transform: translateY(-50%) scale(-1);
            }

            span {
                color: @color-main-text; /* theme */
            }
        }

        span {
            &.status-fulfilment{
                &-red {
                    color: @color-main-order-status-pending; /* theme */
                }
                &-green {
                    color: @color-main-order-status-complete; /* theme */
                }
            }
        }
    }
}
._order-details {
    color: @color-main-meta-text; /* theme */
    font-weight: @normal;
    padding-bottom: 40px;

    &__row {
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: @color-main-borders; /* theme */

        &:last-of-type {
            border-bottom: none;
        }
    }

    &__meta {
        display: flex;
        flex-flow: row wrap;

        > div {
            flex: 1 1 50%;
            max-width: 50%;
            padding-top: 15px;
            padding-bottom: 12px;
            padding-right: 10px;
        }

        h4 {
            font-size: @font-size-heading-3; /* theme */
            color: @color-main-titles; /* theme */
            font-weight: @normal;
            -ltr-text-transform: uppercase;
            padding-bottom: 7px;
        }
    }

    &-products {
        padding-top: 30px;
    }

    &__product {
        display: flex;
        flex-flow: row nowrap;
        position: relative;
        margin-bottom: 30px;

        &__image {
            min-width: 255px;
            width: 255px;
            height: 255px;
            margin-right: 29px;

            img {
                max-width: 100%;
                max-height: 100%;
                .centerer(true, true);
                position: relative;
            }
        }

        &__title {
            margin-bottom: 8px;
            font-weight: @normal;
        }

        &__parameters {
            margin-bottom: 18px;

            .parameter {
                display: table-row;
                font-size: @font-size-small; /* theme */

                .name {
                    display: table-cell;
                    min-width: 105px;
                    padding-right: 5px;
                }

                .value {
                    display: table-cell;
                }
            }
        }

        &__single-price {
            font-size: @font-size-heading-1; /* theme */
            color: @color-main-highlight; /* theme */
            font-weight: @bold;
        }
    }

    &-summary {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        padding-top: 15px;

        & when (@rtl) {
            align-items: flex-start;
        }

        ul {
            display: inline-table;
            font-size: @font-size-small-s; /* theme */
            color: @color-main-text; /* theme */

            li {
                display: table-row;
                //font-size: @base-xsmall-font-size;
                line-height: 1.67;
                -ltr-text-transform: uppercase;
            }
        }

        &-title,
        &-value {
            display: table-cell;
            text-align: right;
            width: auto;

            & when (@rtl) {
                text-align: left;
            }
        }

        &-title {
            padding-right: 10px;
            position: relative;

            &:after {
                content: ':';
            }
        }

        &-value {
            min-width: 170px;

            & when (@rtl) {
                text-align: right;
            }
        }

        &__total {
            border-top-width: 1px;
            border-top-style: solid;
            border-top-color: @color-main-borders; /* theme */
            width: 100%;
            display: flex;
            justify-content: flex-end;
            -ltr-text-transform: uppercase;
            margin-top: 14px;
            padding: 18px 0 16px 20px;

            h4 {
                font-weight: @bold;
            }
        }
    }
}