/*=============================================================================*\
    FILTERS
\*=============================================================================*/

/*  PRICE RANGE
-------------------------------------------------------------------------------*/

._filter-price-range {
    ._filter-price-range-list {
        list-style: none;
        li {
            padding: 15px 0;
            & + li {
                border-top-width: 1px;
                border-top-style: dotted;
                border-top-color: @color-main-borders; /* theme */
            }
            &:first-child {
                padding-top: 0;
            }
            &:last-child {
                padding-bottom: 0;
            }
            a {
                display: block;
            }
            &.active {
                a {
                    color: @color-main-highlight; /* theme */
                }
            }
        }
    }
}

/*  VENDORS
-------------------------------------------------------------------------------*/

._filter-vendors {
    ._filter-vendors-list {
        list-style: none;

        li {
            margin-top: 9px;
            position: relative;

            &:first-child {
                padding-top: 0;
            }

            &:last-child {
                padding-bottom: 0;
            }

            > a:not(._remove) {
                display: block;
                font-weight: @bold;
                color: @color-main-text; /* theme */
                -ltr-text-transform: uppercase;
            }

            ._remove {
                position: absolute;
                top: 5px;
                right: 0;
            }

            &.active {
                > a:not(._remove) {
                    color: @color-main-highlight; /* theme */
                }
            }
        }
    }
}

/*  TYPE
-------------------------------------------------------------------------------*/

._filter-type {
    .clearfix();

    ._button {
        float: left;
        min-width: inherit;

        & + ._button {
            margin-left: 10px;
        }
    }
}

/*  CATEGORIES
-------------------------------------------------------------------------------*/

._filter-categories-list {
    ul {
        list-style: none;

        li {
            margin-top: 9px;
            position: relative;

            &.item-collapse {
                > a {
                    padding-right: 20px;
                }

                ul {
                    display: none;
                    overflow: hidden;
                    transition: max-height .4s ease;
                }
            }

            ._filter-category-list-item {
                display: block;
                color: @color-main-text; /* theme */
            }

            ._collapse {
                position: absolute;
                right: 0;
                top: 0;
                width: 17px;
                height: 17px;
                cursor: pointer;

                &:before,
                &:after {
                    content: '';
                    display: block;
                    background: @color-main-text; /* theme */
                    //position: absolute;
                    //left: 50%;
                    //top: 50%;
                    //transform: translate(-50%, -50%);
                    .centerer(true, true);
                }

                &:before {
                    width: 8px;
                    height: 2px;
                }

                &:after {
                    width: 2px;
                    height: 8px;
                    transition: transform .2s ease;
                    transform: rotate(0deg) translate(-50%, -50%);
                    transform-origin: 0 0;
                }
            }

            &.active {
                > ._filter-category-list-item {
                    color: @color-main-highlight; /* theme */
                }

                > ._collapse {
                    &:after,
                    &:before {
                        background: @color-main-highlight; /* theme */
                    }
                }
            }

            &.opener {
                > ul {
                    display: block;
                }

                > ._collapse {
                    &:after {
                        transform: rotate(90deg) translate(-50%, -50%);
                        transform-origin: 0 0;
                    }
                }
            }
        }
    }

    &-all {
        display: none;
        background: transparent;
        outline: 0;
        margin-bottom: 13px;
        transition: .2s;
        font-weight: bold;

        &:before {
            content:"\f104";
            font-family: 'FontAwesome', sans-serif;
            margin-right: 7px;
        }

        @media (min-width: @screen-desktop) {
            &:hover {
                color: @color-main-highlight; /* theme */
            }
        }
    }

    &.collapsed {
        ._filter-categories-list-all {
            display: block;
        }

        li {
            display: none;

            &.open {
                display: block;

                li {
                    display: block;
                }
            }
        }
    }
}

._filter-box {
    > ._filter-categories-list {
        > ul {
            margin-top: 10px;
            display: block;

            > li {
                &:first-of-type {
                    margin-top: 0;
                }

                > ._filter-category-list-item {
                    font-weight: @bold;
                    -ltr-text-transform: uppercase;
                }
            }
        }
    }
}

/*  Properties */

._filter-category-properties {
    ._filter-category-property {
        ._filter-category-property-title {
            margin-bottom: 20px;
        }
        & + ._filter-category-property {
            margin-top: 30px;
        }

        ._form-row {
            line-height: 1.2;

            & + ._form-row {
                margin-top: 8px;
            }
        }
    }

    ._filter-category-property-image,
    ._filter-category-property-color {
        ._form-row,
        ._form-col {
            display: block;
            width: auto;
            margin: 0;
            padding: 0;
        }

        ._form-inner {
            margin-left: -1%;
            margin-bottom: -1%;
            .clearfix();

            ._form-row {
                float: left;
                margin-left: 1%;
                margin-bottom: 1%;

                + ._form-row {
                    margin-top: 0;
                }
            }
        }

        ._checkbox {
            display: block;
            height: 0;
            padding: 0;
            font-size: 0; /* theme */
            line-height: 0;
            position: relative;
            overflow: hidden;
            word-break: break-word;

            &.active {
                border-color: @color-main-highlight; /* theme */
            }

            .checker {
                display: none;
            }
        }
    }

    ._filter-category-property-image {
        ._form-row {
            width: 24%;
            max-width: 100px;
        }

        ._checkbox {
            border: 1px solid;
            border-color: @color-main-borders; /* theme */
            padding-bottom: calc(100% ~'-' 2px);

            &.active {
                border-width: 2px;
                padding-bottom: calc(100% ~'-' 4px);
            }

            img {
                .centerer(true, true);
            }
        }
    }

    ._filter-category-property-color {
        ._form-row {
            width: 11.5%;
            max-width: 40px;
        }

        ._checkbox {
            border: 2px solid transparent;
            padding-bottom: calc(100% ~'-' 4px);
        }
    }
}

/*  ORDER
-------------------------------------------------------------------------------*/
._products-list-sidebar {
    .select2-container {

        .select2-choice {
            height: 48px;
        }

        .select2-chosen {
            font-size: @font-size-small;
            height: 48px;
            line-height: 48px;
            color: @color-sidebar-select-text; /* theme */
            border-color: @color-sidebar-select-border; /* theme */
            background-color: @color-sidebar-select-background; /* theme */

        }

        .select2-arrow {
            b {

                &:before {
                    font-size: @font-size-small;
                    content: "\f107";
                }
            }
        }
    }
}