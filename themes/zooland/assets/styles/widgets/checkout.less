/*=============================================================================*\
    CHECKOUT
\*=============================================================================*/

/*  SHIPPING PROVIDERS
-------------------------------------------------------------------------------*/

._checkout-shipping-providers-controls {
    font-size: 0;
    position: relative;

    &:after {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: @color-main-borders; /* theme */
        content: '';
        z-index: -1;
    }

    ._radio {
        border-width: 1px;
        border-style: solid;
        border-color: @color-main-borders; /* theme */
        padding: 10px 15px;
        text-align: center;
        position: relative;
        z-index: 1;
        color: @color-main-meta-text; /* theme */

        &.active {
            border-color: @color-main-highlight; /* theme */
        }

        .radio {
            display: none;
        }

        > span {
            font-size: @font-size-base;
        }
        
        & + ._radio {
            margin-left: 10px;
        }
    }
}

._checkout-shipping-provider-service {
    margin-bottom: 10px;

    &:last-child {
        margin-bottom: 0;
    }
}

._checkout-shipping-providers-list {
    list-style: none;
    font-size: 0;
    width: ~'calc(100% + 30px)';
    margin-left: -15px;
    margin-right: -15px;
}

._checkout-shipping-provider {
    display: inline-block;
    font-size: @font-size-base;
    width: 33.3%;
    vertical-align: top;
    padding-left: 15px;
    padding-right: 15px;
    margin-top: 15px;
}

._checkout-shipping-provider-details {
    text-align: center;

    ._radio {
        width: 100%;
        border-width: 1px;
        border-style: solid;
        border-color: @color-main-borders; /* theme */
        font-size: @font-size-base;
        text-transform: none;
        font-weight: bold;
        padding: 7px 20px;
        padding-left: 48px;
        min-height: 80px;
        text-align: left;
        display: flex;
        align-content: center;
        align-items: center;
        flex-wrap: wrap;
        
        &.active {
            border-color: @color-main-highlight; /* theme */
        }

        .radio {
            .centerer(false, true);
            left: 20px;
        }

        img {
            height: 40px;
        }
    }
}

._checkout-shipping-provider-details-name {
    width: 100%;
}

._checkout-shipping-provider-image {
    width: 100%;
    margin-bottom: 5px;
}

._checkout-shipping-provider-features {
    margin-top: 20px;
}

._checkout-shipping-provider-feature {
    & + ._checkout-shipping-provider-feature {
        margin-top: 10px;
    }
}

._checkout-shipping-provider-features-form {
    .row {
        margin-top: 15px;
    }

    ._buttons-col {
        text-align: right;

        ._button {
            &:extend(._button._button-tertiary all);
        }
    }
}

._checkout-shipping-provider-price {
    font-weight: 400;
}

._checkout-shipping-provider-service {
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: @color-main-borders; /* theme */
    padding-bottom: 15px;
    margin-bottom: 15px;

    &:last-child {
        border-bottom: 0;
        padding-bottom: 0;
        margin-bottom: 0;
    }

    ._radio {
        display: block;
    }
}

/*  PAYMENT PROVIDERS
-------------------------------------------------------------------------------*/

._checkout-payment-providers {
    ._form {
        ._form-row {
            .flexy-row-mobile(15px);
            ._form-col {
                .flexy-col-mobile();
                & + ._form-col {
                    border-top-width: 1px;
                    border-top-style: solid;
                    border-top-color: @color-main-borders; /* theme */
                    padding-top: 15px;
                }
                ._radio {
                    display: block;
                }
            }
        }
    }
}