/*=============================================================================*\
    BLOG
\*=============================================================================*/

/*  ARTICLE
-------------------------------------------------------------------------------*/

._blog-article {
    position: relative;

    ._blog-article-meta {
        position: relative;
        margin-top: -34px;
        z-index: 2;

        ._blog-article-author,
        ._blog-article-category {
            display: none;
        }

        ._blog-article-time {
            display: inline-block;
            background: @color-main-highlight; /* theme */
            font-size: @font-size-small-s;
            line-height: 1.4;
            color: @color-main-secondary-text; /* theme */
            -ltr-text-transform: uppercase;
            padding: 4px 5px 4px 10px;
            position: relative;

            &:after {
                content: '';
                display: block;
                position: absolute;
                left: 100%;
                top: 0;
                height: 100%;
                border-top: 12px solid transparent;
                border-bottom: 12px solid transparent;
                border-left-width: 7px;
                border-left-style: solid;
                border-left-color: @color-main-highlight; /* theme */
            }
        }
    }

    ._blog-article-text {
        margin-top: 30px;
        text-align: justify;
        .clearfix;
    }

    ._blog-article-tags {
        margin-top: 30px;
    }

    ._blog-article-comments {
        ._blog-article-comment {
            .clearfix();
            padding: 30px 0;
            & + ._blog-article-comment {
                border-top-width: 1px;
                border-top-style: dotted;
                border-top-color: @color-main-borders; /* theme */
            }
            &:first-child {
                padding-top: 0;
            }
            &:last-child {
                padding-bottom: 0;
            }
            ._blog-article-comment-image {
                float: left;
                margin-right: 15px;
            }
            ._blog-article-comment-info {
                ._blog-article-comment-author {
                }
                ._blog-article-comment-time {
                    font-size: @font-size-small; /* theme */
                    color: @color-main-meta-text; /* theme */
                    &:before {
                        content: '\2014';
                        margin-right: 4px;
                    }
                }
                ._blog-article-comment-text {
                    display: block;
                    margin-top: 10px;
                    font-style: italic;
                }
            }
        }
    }

    ._section-title {
        ._section-separator();
        ._second-title-style;
    }
}

/*  LIST
-------------------------------------------------------------------------------*/

/*  Article */

._blog-list-article {
    position: relative;

    & + ._blog-list-article {
        margin-top: 40px;
    }

    ._blog-list-article-inner {
        display: flex;
    }

    ._blog-list-article-image {
        width: 100%;
        max-width: 255px;

        .blog-list-article-image-thumb {
            display: block;

            img {
                display: block;
            }
        }
    }

    ._blog-list-article-info {
        width: 100%;
        padding-left: 30px;
        font-size: @font-size-small-s;
        line-height: 1.8;
        font-weight: @light;

        ._blog-list-article-title {
            h3 {
                ._h5;
                line-hieght: 1.4;
                font-weight: @normal;
            }
        }

        ._blog-list-article-meta {

            ._blog-list-article-author,
            ._blog-list-article-category {
                display: none;
            }

            ._blog-list-article-time {
                position: absolute;
                left: 0;
                bottom: 10px;
                background: @color-main-highlight; /* theme */
                font-size: @font-size-small-s;
                line-height: 1.4;
                color: @color-main-secondary-text; /* theme */
                -ltr-text-transform: uppercase;
                padding: 4px 5px 4px 10px;

                &:after {
                    content: '';
                    display: block;
                    position: absolute;
                    left: 100%;
                    top: 0;
                    height: 100%;
                    border-top: 12px solid transparent;
                    border-bottom: 12px solid transparent;
                    border-left-width: 7px;
                    border-left-style: solid;
                    border-left-color: @color-main-highlight; /* theme */
                }
            }
        }

        ._blog-list-article-text {
            margin: 9px 0;
        }

        ._blog-list-article-viewmore {
            -ltr-text-transform: uppercase;

            a {
                color: @color-main-highlight; /* theme */
            }

        }

    }
}

/*  LATEST
-------------------------------------------------------------------------------*/
._latest-blog {
    background: @color-main-blog-latest-background; /* theme */
    padding: 60px 0;

    ._latest-blog-title {
        ._main-title-style();
        ._main-title-style--paw();
        border-bottom-color: @color-main-blog-latest-borders; /* theme */

        &:before {
            color: @color-main-blog-latest-highlight; /* theme */
        }

        &:after {
            background-color: @color-main-blog-latest-highlight; /* theme */
        }

        h2 {
            color: @color-main-blog-latest-title; /* theme */
        }
    }

    ._blog-list-articles {
        display: flex;
        align-items: flex-start;
        margin: 0 -15px;

        ._blog-list-article {
            max-width: 50%;
            width: 50%;
            padding: 0 15px;
            //opacity: 0;

            & + ._blog-list-article {
                margin-top: 0;
            }

            ._blog-list-article-info {
                width: 100%;
                padding-left: 30px;
                font-size: @font-size-small-s;
                color: @color-main-blog-latest-text; /* theme */
                line-height: 1.8;
                font-weight: @light;

                ._blog-list-article-title {
                    h3 {
                        ._h5;
                        line-hieght: 1.4;
                        font-weight: @normal;

                        a {
                            color: @color-main-blog-latest-title; /* theme */
                        }
                    }
                }

                ._blog-list-article-meta {

                    ._blog-list-article-time {
                        left: 15px;
                    }
                }
            }

            ._blog-list-article-viewmore {
                a {
                    color: @color-main-blog-latest-highlight; /*theme*/
                }
            }
        }
    }
}

/*  SIDEBAR
-------------------------------------------------------------------------------*/

/*  Categories */
._blog-categories {
    ul {
        display: block;
        list-style: none;

        li {
            margin-top: 9px;
            position: relative;

            &:first-of-type {
                margin-top: 0;
            }

            a {
                .clearfix();
                display: block;

                ._blog-category-name {
                    display: block;
                    font-weight: @bold;
                    color: @color-main-text; /* theme */
                    -ltr-text-transform: uppercase;
                }

                ._blog-category-image {
                    display: none;
                }
            }

            ._filter-remove {
                display: none;
                position: absolute;
                right: 0;
                top: 0;
                font-weight: @bold;
                transform: rotate(45deg);
                transform-origin: 0 0;
            }

            &.active {
                a {
                    &, ._blog-category-name {
                        color: @color-main-highlight; /* theme */
                    }
                }

                ._filter-remove {
                    display: inline-block;
                }
            }
        }
    }
}

/*  Recent articles */

._blog-recent-articles {

    ul {
        list-style: none;

        li {
            padding: 0 0 20px 0;

            &:first-child {
                padding-top: 0;
            }

            &:last-child {
                padding-bottom: 0;
            }

            a {
                display: table;
                width: 100%;
                position: relative;
                margin-top: -3px;

                > span {
                    display: table-cell;

                    &:first-child {
                        width: 80px;
                    }

                    &:last-child {
                        padding-left: 10px;
                    }

                    span {
                        display: block;
                        font-size: @font-size-small; /* theme */

                        &._blog-recent-article-time {
                            font-size: @font-size-small-s; /* theme */
                            color: @color-main-meta-text; /* theme */
                            font-weight: @light;

                            & when (@rtl) {
                                direction: ltr;
                            }
                        }
                    }
                }
            }
        }
    }
}

/*  Recent comments */

._blog-recent-comments {

    ul {
        list-style: none;

        li {
            padding: 0 0 20px 0;

            &:first-child {
                padding-top: 0;
            }
            &:last-child {
                padding-bottom: 0;
            }

            a {
                display: table;
                width: 100%;

                > span {
                    display: table-cell;

                    &:first-child {
                        width: 50px;
                    }

                    &:last-child {
                        padding-left: 10px;
                    }

                    span {
                        display: block;
                        font-size: @font-size-small; /* theme */

                        &._blog-recent-comments-article {
                            font-size: @font-size-small-s; /* theme */
                            color: @color-main-meta-text; /* theme */
                            font-weight: @light;

                            & when (@rtl) {
                                direction: ltr;
                            }
                        }
                    }
                }
            }
        }
    }
}