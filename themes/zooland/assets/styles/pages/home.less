/*=============================================================================*\
    HOME
\*=============================================================================*/

._home-welcome {
    position: relative;

    ._background-image {
        background-repeat: no-repeat;
        background-size: cover;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }

    ._home-welcome__content {
        position: relative;
        z-index: 2;
        padding: 68px 0;
        max-width: 60%;
        font-size: @font-size-small;
        color: @color-main-welcome-text; /* themes */

        ._text-title {
            ._main-title-style(@color-main-welcome-text, @color-main-welcome-accent, @color-main-welcome-text);
            ._main-title-style--type2();

            h6 {
                ._h1;
                font-weight: @normal;
                color: @color-main-welcome-text; /* theme */
            }
        }

        ._text-description {

            p:not(:last-of-type) {
                margin-bottom: 25px;
            }

            ul {
                list-style-type: none;
                margin-bottom: 15px;

                li {
                    position: relative;
                    font-size: @font-size-base-l;
                    padding-left: 20px;

                    &:before {
                        content: "\f1b0";
                        display: block;
                        position: absolute;
                        left: 0;
                        top: 2px;
                        font-family: 'FontAwesome';
                        font-size: 14px;
                        color: @color-main-welcome-accent; /* theme */
                    }

                }
            }
        }
    }
}

._special-product-showcase__holder {
    position: relative;

    ._background-image {
        background-repeat: no-repeat;
        //background-size: 100% auto;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }

    ._showcase-product {
        padding-top: 52px;

        ._showcase-product-title {
            ._main-title-style(@color-deals-products-title, @color-deals-products-accent, @color-deals-products-title);
            ._main-title-style--paw(@color-deals-products-accent);
        }

        ._showcase-product-list {
            ._products-list {

                ._product {
                    position: relative;

                    &:before {
                        opacity: 1;
                        border: none;
                        background: @color-deals-products-single-background; /* theme */
                    }

                    ._product-image {
                        padding: 25px;
                        background-color: transparent; /* theme */

                        ._product-image-thumb {
                            padding: 0;
                        }

                        ._product-ribbon-holder {
                            left: 15px;
                            top: 15px;
                        }

                        ._product-ribbon {

                            &._product-discount {
                                left: 25px;
                                bottom: 25px;
                            }

                            &._product-leasing {
                                right: 25px;
                                bottom: 25px;
                            }
                        }
                    }

                    ._product-info {

                        ._product-name {
                            h3 {
                                ._h2;
                                color: @color-deals-products-single-title; /* theme */
                                height: auto;
                            }
                        }

                        ._product-options {
                            margin-top: 0;

                            ._product-price {
                                ._h2;
                                font-weight: @bold;
                                color: @color-deals-products-single-price; /* theme */

                                ._product-price-compare {
                                    ._h2;
                                    font-weight: @bold;
                                    color: @color-deals-products-single-new-price-text; /* theme */
                                }

                                ._product-price-old {
                                    ._h3;
                                    margin-left: 10px;
                                    font-weight: @light;
                                    color: @color-deals-products-single-old-price-text; /* theme */
                                }
                            }
                        }

                        ._product-add,
                        ._product-description {
                            display: none;
                        }
                    }
                }
            }
        }
    }
}
