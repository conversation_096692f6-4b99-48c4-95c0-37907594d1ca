/*=============================================================================*\
    EXTRA SMALL DEVICES / PHONES
\*=============================================================================*/

@media (max-width: @screen-xs-max) {

	/* #Main
	-------------------------------------------------------------------------------*/

	h1, ._h1 {
		font-size: 24px;
	}

	h2, ._h2 {
		font-size: 22px;
	}

	h3, ._h3 {
		font-size: 20px;
	}

	h4, ._h4 {
		font-size: 18px;
	}

	h5, ._h5 {
		font-size: 16px;
	}

	h6, ._h6 {
		font-size: 14px;
	}

	._button,
	._meta-link-login,
	._meta-link-register,
	._meta-link-forgotten-password {
		width: 100%;
	}

	._meta-links {
		padding-top: 15px;
		margin-top: 15px;

		a {
			+ a {
				margin: 10px 0 0;
			}
		}
	}

	._content {
		padding-top: 25px;
		padding-bottom: 25px;

		&-no-padding-bottom {
			padding-bottom: 0;
		}

		&-no-padding-top {
			padding-top: 0;
		}

		> ._breadcrumb-container {
			&:first-child {
				margin-top: -25px;
			}
		}
	}

	._section {
		padding-top: 25px;
		padding-bottom: 25px;
	}

	._notification {
		font-size: @font-size-small;
	}

	._title,
	._text-title,
	._showcase-title,
	._showcase-product-title {
		h1,
		h2,
		h3,
		h4,
		h5,
		h6 {
			font-size: 20px;
		}
	}

	._text-box {
		._text-title {
			h6 {
				font-size: 16px;
			}
		}
	}

	@page-intro-height: 300px;

	._page {
		&-intro {
			height: @page-intro-height;

			&-inner {
				padding-bottom: 20px;
			}

			._title {
				h1 {
					font-size: 20px;
					padding-bottom: 16px;
					margin-bottom: 14px;
				}
			}
		}
	}

	._breadcrumb-container {
		display: none;
	}

	/* #Textbox
	-------------------------------------------------------------------------------*/

	._textbox {
		.alignleft,
		.alignright {
			float: none;
			width: auto;
			display: block;
			margin: 0 0 20px;
		}
	}

	._text-section {
	    flex-flow: row wrap;

	    & + ._text-section {
	    	margin-top: 20px;
	    }

	    &-text,
	    &-image {
	    	width: 100%;

	    	&:first-child,
	    	&:last-child {
	    		.translate(0, 0);
	    	}
	    }

		&-text {
			order: 1;
			margin-bottom: 20px;
		}

		&-image {
			order: 2;
		}
	}

	/* #Forms
	-------------------------------------------------------------------------------*/
	
	._form-row {
		display: block;
		margin-bottom: 10px;

		._form-col {
			float: none;
			display: block;
			margin-bottom: 10px;
			width: auto;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	._form-row-secondary {
		display: block;
		margin-bottom: 10px;

		._form-col-secondary {
			float: none;
			display: block;
			margin-bottom: 10px;
			width: auto;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	._block-title {
		margin-bottom: 10px;
	}

	.input-group {
		.input-group-btn {
			.btn {
				width: 30px;
			}
		}
	}

	[id^="recaptcha"] {
		-webkit-transform-origin: 0 0;
		        transform-origin: 0 0;
		-webkit-transform: scale(.9);
		        transform: scale(.9);
	}

	/* #Separator
	-------------------------------------------------------------------------------*/
	._section-separator,
	._checkout-payment-providers,
	._checkout-shipping-provider-services-holder {
		padding-bottom: 25px;
	}

	._checkout-shipping-provider-features {
		margin-top: 25px;
	}

	/* #Header
	-------------------------------------------------------------------------------*/

	._user-controls {
		._figure-stack-label {
			display: none;
		}
	}

	._navigation-links {
		display: none;
		position: relative;
		left: 0;
		padding: @navigation-mobile-sections-vertical-offsets 0 0;
		margin: @navigation-mobile-sections-vertical-offsets 0 0;
		text-transform: initial;

		&:before {
			content: '';
			border-top: 1px solid @color-nav-dropdown-text; /* theme */
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			opacity: .2;
		}

		li {
			display: block;
			font-size: @font-size-base;
			line-height: @line-height-medium;
			padding: 0;
			margin: 0 0 @navigation-mobile-vertical-offsets;
		}

		a {
			color: @color-nav-dropdown-text; /* theme */
		}
	}

	._navbar {
		._navigation-links {
			display: block;
		}
	}

	/* #Footer
	-------------------------------------------------------------------------------*/

	._footer {
		padding: 20px 0 5px;

		&-utilities {
			display: block;
			text-align: center;
		}

		&-socials {
			display: block;
			margin-bottom: 15px;
		}

		&-legal {
			width: auto;
			display: block;
			margin-bottom: 15px;
		}

		&-providers {
			display: block;
			margin-bottom: 15px;
			text-align: center;
		}

		&-cols {
			padding-bottom: 0;

			._text {
				float: none;
				width: auto;
				margin-bottom: 20px;

				& + ._navigation-footer {
					float: none;
					width: auto;
					.clearfix();

					li {

					}
				}
			}
		}
	}

	._navigation-footer {
		margin-bottom: 0;

		li {
			width: 50%;
			padding-right: 15px;
			margin-bottom: 20px;

			&:nth-child(3n+1) {
				clear: none;
			}

			&:nth-child(2n+1) {
				clear: both;
			}
		}
	}

	/* #Slider
	-------------------------------------------------------------------------------*/

	._slider {
		padding: 0;
	}

	._slider,
	.slide,
	.slide-image-thumb {
		height: 300px;
	}

	.slide-text {
		display: none;
	}

	.owl-prev,
	.owl-next {
		width: 50px;
		height: 50px;
	}

	.owl-prev {
		left: 20px;
	}

	.owl-next {
		right: 20px;
	}

	.owl-dots {
		bottom: 10px;
	}

	/* #Banners
	-------------------------------------------------------------------------------*/

	._banner {
		display: block;
		float: none;
		margin-bottom: 20px;

		&:last-child {
			margin-bottom: 0;
		}

		&-caption {
			display: none;
		}
	}

	._banners {
		&-list {
			display: block;
		}

		&-product-listing,
		&-product-details {
			margin-top: 25px;
		}

		._banner {
			width: 100%;
		}
	}


	/* #Products list
	-------------------------------------------------------------------------------*/

	._filters {
		&-row {
			float: none;
			display: block;	
		}

		&-col {
			display: block;
			width: auto;
			padding: 0;
			margin-bottom: 10px;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	.col-md-9,
	.col-md-12 {
		._products-list {
			margin-left: -8px;
			margin-right: -8px;
			margin-bottom: -16px;

			._product {
				width: calc(50% ~'-' 16px);

				&:nth-child(3n+1) {
					clear: none;
				}

				&:nth-child(2n+1) {
					clear: both;
				}
			}
		}
	}

	._product {
		margin-left: 8px;
		margin-right: 8px;
		margin-bottom: 16px;

		&-out-of-stock {
			display: block;
			position: static;
			text-align: center;
		}

		&-share {
			display: inline-block;
		}
	}

	._products-list-head {
		&-item {
			&-body {
				display: block;
			}

			&-image {
				display: block;
				width: auto;
				padding: 0;
				text-align: center;
			}

			&-description {
				margin-top: 20px;
				display: block;
			}
		}
	}

	._related-products-bottom {
		padding-top: 30px;
		margin-top: 0;
	}

	._pagination {
		text-align: center;

		.first,
		.last {
			display: none;
		}
	}

	._sidebar-products,
	._sidebar-blog {
		width: 100%;
		border: 0;
	}

	/* #Showcase
	-------------------------------------------------------------------------------*/

	._showcase {
		&-list {
			margin-left: -8px;
			margin-right: -8px;
			margin-bottom: -20px;
		}

		&-title {
			margin-bottom: 25px;
		}

		&-item {
			width: 50%;
			padding: 0 8px;
			margin-bottom: 20px;

			&:nth-child(3n+1) {
				clear: none;
			}

			&:nth-child(2n+1) {
				clear: both;
			}
		}
	}

	/* #Vendors
	-------------------------------------------------------------------------------*/

	._vendors-list {
		margin-left: -8px;
		margin-right: -8px;
		margin-bottom: -20px;

		._vendor {
			width: 50%;
			margin-bottom: 20px;
			padding-left: 8px;
			padding-right: 8px;

			&:nth-child(3n+1) {
				clear: none;
			}

			&:nth-child(2n+1) {
				clear: both;
			}
		}
	}

	/* #Blog
	-------------------------------------------------------------------------------*/

	._blog {
		&-article {
			&-image {
				float: none;
				max-width: 100%;
				margin: 0 0 20px;
			}

			&-title {
				padding-bottom: 13px;
				margin-bottom: 12px;
			}
		}
	}

	/* #Contacts
	-------------------------------------------------------------------------------*/

	._contact {
		&-info {
			&-container {
				margin: 15px 0;
				float: none;
				width: auto;
			}
		}
		
		&-form {
			h2 {
				margin-bottom: 15px;
			}

			._button {
				width: 100%;
			}
		}
	}

	._google-map {
		position: static;
		height: 300px !important;
	}

	/* #Account
	-------------------------------------------------------------------------------*/

	._sidebar-list-account {
		li {
			display: block;
			float: left;
			width: 50%;
			padding: 0;
			padding-right: 10px;

			&:nth-child(2n+1) {
				clear: both;
			}
		}
	}

	.region-holder {
		margin-bottom: 10px;
	}

	._order-details {
		&-product {
			&-image {
				margin-top: 0;
				margin-right: 15px;
				width: 25%;
			}

			&-title {
				margin-bottom: 5px;

				h2 {
					font-size: 18px;
				}
			}

			&-price {
				&-total {
					margin-top: 5px;
				}
			}
		}

		&-summary {
			li {
				width: 250px;
			}

			&-value {
				padding-right: 0;
			}
		}
	}

	/* #Cart
	-------------------------------------------------------------------------------*/

	._cart {
		&-product {
			&-image {
				padding-top: 3px;
				width: 150px;
			}

			&-name {
				h4 {
					font-size: 20px;
				}
			}

			&-total-price {
				font-size: 20px;
				line-height: 42px;
			}

			&-box {
				padding-left: 15px;
			}

			&-quantity {
				left: 165px;

				.input-group {
					width: 120px;
				}
			}
		}

		&-totals-box {
			font-size: @font-size-xsmall;

			.final-total {
				font-size: @font-size-medium;
			}
		}
	}

	/* #Checkout
	-------------------------------------------------------------------------------*/

	._checkout {
		&-payment-providers {
			._form-col {
				margin-bottom: 0;
			}
		}

		&-account {
			display: block;

			&-tabs {
				display: block;
			}

			&-forms {
				display: block;
			}

			._form-row {
				margin-bottom: 10px;
			}
		}
	}

    ._checkout-shipping-providers-list {
        width: auto;
        margin-left: 0;
        margin-right: 0;

        li {
            width: 100%;
            padding-left: 0;
            padding-right: 0;
        }
    }

    ._checkout-shipping-provider-features-form {
        [class*='col'] {
			margin-top: 15px;

			&:first-child {
				margin-top: 0;
			}
        }
    }

    ._checkout-shipping-providers-controls {
    	border-bottom: 0; /* theme */

    	._radio {
    		width: auto;
    		display: block;
    		margin: 0 0 5px;

    		&:last-child {
    			margin-bottom: 0;
    		}
    	}
    }
}

