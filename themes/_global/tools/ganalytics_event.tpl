{literal}
<script>window.ga_debug = {trace: true};</script>
{/literal}
{if Apps::enabled('google_analytics') && !Auth::adminId()}
    {if App::make('google_analytics')->isGVersion()}
<script>
    function isValidNumber(value) {
        return !isNaN(parseFloat(value)) && isFinite(value);
    }

    $('body').on('click','._product a', function() {
        var index = $(this).index();
        var product = cc_page_data.products[index];
        var discount_for_product = 0.00;
        if(product.discount_price > 0){
            discount_for_product = product.price - product.discount_price;
        }
        if(product.sku != null || product.sku != ''){
            var unique_id = product.sku;
        } else if(product.barcode != null || product.barcode != '') {
            var unique_id = product.barcode;
        } else {
            var unique_id = product.parameter_id;
        }
        if(cc_page_data.name != 'undefined')
        {
            var page_name = cc_page_data.name;
        } else {
            var page_name = '';
        }

        gtag('event', 'select_item', {
            send_to: '{Apps::setting('google_analytics', 'code')}',
            items: [{
                item_id: unique_id,
                item_name: product.name,
                discount: discount_for_product,
                index: index,
                item_list_name: cc_page_data.name,
                item_list_id: 'related_products',
                affiliation: '{setting('site_name')}',
                item_brand: product.brand,
                item_category: product.category,
                price: product.discount_price,
                currency: product.currency
            }],

            item_list_name: page_name,
            item_list_id: cc_page_data.type
        });

    });
    $(document).onLast('cc.cart.product.removed', function (e, json) {
        if(json.product.variant.v1 != null) { var variant_item = json.product.variant.v1; }
        if(json.product.variant.v2 != null) { var variant_item = variant_item+','+json.product.variant.v2; }
        if(json.product.variant.v3 != null) { var variant_item = variant_item+','+json.product.variant.v3; }
        if(json.product.variant.sku != null || json.product.variant.sku != ''){
            var unique_id = json.product.variant.sku;
        } else if(json.product.variant.barcode != null || json.product.variant.barcode != ''){
            var unique_id = json.product.variant.barcode;
        } else {
            unique_id = product.parameter_id;
        }

        gtag('event', 'remove_from_cart', {
            send_to: '{Apps::setting('google_analytics', 'code')}',
            currency: '{setting("currency")}',
            items: [{
                item_id: unique_id,
                item_name: json.product.name,
                item_brand: json.product.vendor ? json.product.vendor.name : '',
                item_category: json.product.category.name,
                quantity: json.product.pivot.quantity,
                price: isValidNumber(json.product.variant.price_input) ? parseFloat(json.product.variant.price_input) : json.product.variant.price_input,
                item_variant: variant_item
            }],
            value: json.product.variant.price_input*json.product.pivot.quantity
        });
    });
    $(document).onLast('cc.wishlist.product.added', function (e, json) {
        gtag('event', 'add_to_wishlist', {
            send_to: '{Apps::setting('google_analytics', 'code')}',
            currency: '{site('currency')}',
            items: [{
                id: json.product.id,
                name: json.product.name,
            }],
        });
    });
    {if activeRoute() == 'category.view' || activeRoute() == 'site.vendor.view' || activeRoute() == 'products.search'}
    let products = cc_page_data.products;
    var product_array = [];
    for(let i = 0; i < products.length; i++){
        if(products[i].sku != null || products[i].sku != ''){
            var unique_id = products[i].sku;
        } else if(products[i].barcode != null || products[i].barcode != '') {
            var unique_id = products[i].barcode;
        } else {
            var unique_id = products[i].parameter_id;
        }
        product_array.push(
            {
                id: unique_id,
                name: products[i].name,
                brand: products[i].brand,
                category: products[i].category,
                list_name: cc_page_data.name,
                list_position: products[i],
                price:  isValidNumber(products[i].price) ? parseFloat(products[i].price) : products[i].price,
            }
        );
    }

    gtag('event', 'view_item_list', {
        send_to: '{Apps::setting('google_analytics', 'code')}',
        items: product_array
    });
    {/if}
    {if activeRoute() == 'product.view' && isset($product) && !empty($product)}
    {if !is_null($product->price_from_discounted)}
    {$price = $product->price_from_discounted_input}
    {$price_old = $product->price_from_input}
    {$price_save = $product->price_from_saved_input}
    {else}
    {$price = $product->price_from_input}
    {/if}

    gtag('event', 'view_item', {
        send_to: '{Apps::setting('google_analytics', 'code')}',
        currency: '{site('currency')}',
        items: [{
                item_id: '{$product->id}',
                item_name: '{$product->name}',
                {if !empty($product->vendor)}
                item_brand:  '{$product->vendor->name}',
                {/if}
                {if !empty($product->category)}
                item_category: '{$product->category->name}',
                {/if}
                quantity: {$product->quantity},
                {if $price > 0}
                price: {$price}
                {/if}
        }]
    });

    {/if}
    {if activeRoute() == 'checkout'}
    var products = cc_page_data.products;
    var product_in_checkout = [];
    for(let i = 0; i < products.length; i++){
        var unique_id = products[i].parameter_id,
            discount_price = 0.00;

        if(products[i].discount_price > 0){
            discount_price =  products[i].single_price-products[i].discount_single_price;
        }

        if(products[i].sku){
            unique_id = products[i].sku;
        } else if(products[i].barcode) {
            unique_id = products[i].barcode;
        }

        product_in_checkout.push(
            {
                id: unique_id,
                name: products[i].name,
                discount: discount_price,
                brand: products[i].brand,
                category: products[i].category,
                price:  products[i].single_price,
                quantity: products[i].quantity,
            }
        )
    }

    gtag("event", "begin_checkout", {
        send_to: '{Apps::setting('google_analytics', 'code')}',
        value: cc_page_data.subtotal,
        currency: cc_page_data.currency,
        items: product_in_checkout,
    });
    {/if}
    {if activeRoute() == 'cart.list'}
    var cartProducts = cc_page_data.products,
        product_in_cartpage = [];

    for(var i = 0; i < cartProducts.length; i++) {
        var cart_discount_price = '',
            cart_unique_id = cartProducts[i].parameter_id;

        if(cartProducts[i].discount_price > 0){
            cart_discount_price = cartProducts[i].single_price - cartProducts[i].discount_price;
        } else {
            cart_discount_price = '';
        }
        if(cartProducts[i].sku){
            cart_unique_id = cartProducts[i].sku;
        } else if(cartProducts[i].barcode) {
            cart_unique_id = cartProducts[i].barcode;
        }

        product_in_cartpage.push(
            {
                id: cart_unique_id,
                name: cartProducts[i].name,
                discount: cart_discount_price,
                brand: cartProducts[i].brand,
                category: cartProducts[i].category,
                price:  cartProducts[i].single_price,
                quantity: cartProducts[i].quantity,
            }
        )
    }

    gtag('event', 'view_cart', {
        send_to: '{Apps::setting('google_analytics', 'code')}',
        currency: cc_page_data.currency,
        items: product_in_cartpage,
        value: cc_page_data.subtotal
    });

    {/if}
    {if activeRoute() == 'checkout.return'}
    function cookieGaOrderCheck() {
        {if $order->payment->hash|default}
        Cookies.set('_ga_ordersend_{$order->payment->hash}', '{md5($order->id)}', {
            expires: 14
        });
        {/if}
    }

    if(Cookies.get('_ga_ordersend_{$order->payment->hash|default:md5(mt_rand())}') !== '{md5($order->id|default:mt_rand())}') {
        var checkoutProducts = cc_page_data.products,
            product_in_finished = [],
            totals = cc_page_data.totals,
            total_tax = 0.00,
            discount_code = '',
            discount_price = 0.00;

        for (let i = 0; i < totals.length; i++) {
            if (totals[i].key === 'discount') {
                var price = totals[i].price;
                    price = price.replace("-", "");

                if (totals[i].value != null && totals[i].value[0] !== 'undefined') {
                    discount_code = totals[i].value[0];
                }
            }

            discount_price = discount_price + parseFloat(price);
            if (totals[i].key === 'tax') {
                total_tax = total_tax + parseFloat(totals[i].price);
            }
        }

        for (let i = 0; i < checkoutProducts.length; i++) {
            // Product discount
            var discountprice = null,
                checkout_unique_id = checkoutProducts[i].parameter_id,
                total_discoun = 0;

            if (checkoutProducts[i].discount_single_price > 0) {
                discountprice = checkoutProducts[i].single_price - checkoutProducts[i].discount_single_price;
            }
            if (checkoutProducts[i].sku) {
                checkout_unique_id = checkoutProducts[i].sku;
            } else if (checkoutProducts[i].barcode) {
                checkout_unique_id = checkoutProducts[i].barcode;
            }

            if (discountprice > 0 && discountprice !== 'undefined') {
                total_discoun = discountprice + discount_price;
                total_discoun = total_discoun.toFixed(2);
            }

            if (checkout_unique_id != null && checkoutProducts[i].name != null && checkoutProducts[i].name !== 'undefined') {
                product_in_finished.push(
                    {
                        item_id: checkout_unique_id,
                        item_name: checkoutProducts[i].name,
                        coupon: discount_code,
                        discount: total_discoun,
                        affiliation: '{setting('site_name')}',
                        item_brand: checkoutProducts[i].brand,
                        item_category: checkoutProducts[i].category,
                        price: checkoutProducts[i].single_price,
                        quantity: checkoutProducts[i].quantity,
                        currency: checkoutProducts[i].currency
                    }
                )
            }
        }

        if (product_in_finished.length > 0) {
            gtag('event', 'purchase', {
                send_to: '{Apps::setting('google_analytics', 'code')}',
                affiliation: '{setting('site_name')}',
                coupon: discount_code,
                currency: cc_page_data.currency,
                items: product_in_finished,
                transaction_id: cc_page_data.id,
                shipping: cc_page_data.chosen_shipping.price,
                value: cc_page_data.total,
                tax: total_tax
            })
            cookieGaOrderCheck();
        }
    }
    {/if}
    $('body').on('click','.cc-button-google', function() {
        gtag('event', 'login', {
            send_to: '{Apps::setting('google_analytics', 'code')}',
            method: 'google'
        });
    }).on('click','.cc-button-facebook', function() {
        gtag('event', 'login', {
            send_to: '{Apps::setting('google_analytics', 'code')}',
            method: 'facebook'
        });
    }).on('click','form.cc-form-login .cc-form-actions button.cc-button', function() {
        gtag('event', 'login', {
            send_to: '{Apps::setting('google_analytics', 'code')}',
            method: 'website'
        });
    });
    {if activeRoute() == 'site.auth.register'}
    $('body').on('click','.cc-form-actions button.cc-button', function() {
        gtag('event', 'sign_up', {
            send_to: '{Apps::setting('google_analytics', 'code')}',
            method: 'website'
        });
    });
    {/if}
</script>
{/if}
{/if}