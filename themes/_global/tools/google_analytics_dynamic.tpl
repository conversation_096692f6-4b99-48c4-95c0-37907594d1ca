<!-- 
    Google Analytics Dynamic scripts 
    Note: These scripts are included directly here and should NOT be included through JsResponse 
    to prevent nested script tags and JavaScript errors.
-->
{if GDPR::hasAcceptedCookieGroup('performance')}
    {$analyticsManager = App::make('google_analytics')}
    {$google_analytics_dynamic = null}
    {$isLoggedAdmin = Auth::adminId()}
    {if \App\Models\System\AppsManager::isActive('google_dynamic') && (\App\Models\System\AppsManager::isActive('google_analytics') && $analyticsManager->getV4Code())}
        {$google_analytics_dynamic = ['ga'=>$analyticsManager->getV4Code(), 'gd'=> \Modules\Apps\Google\GoogleDynamic\GoogleDynamicManager::getCode()]}
    {elseif \App\Models\System\AppsManager::isActive('google_dynamic')}
        {$google_analytics_dynamic = ['gd'=>\Modules\Apps\Google\GoogleDynamic\GoogleDynamicManager::getCode()]}
    {elseif \App\Models\System\AppsManager::isActive('google_analytics') && $analyticsManager->getV4Code()}
        {$google_analytics_dynamic = ['ga'=>$analyticsManager->getV4Code()]}
    {/if}

    {if $google_analytics_dynamic}
        <script type="text/javascript"
                src="//www.googletagmanager.com/gtag/js?id={array_first($google_analytics_dynamic)}"></script>
        <script>
            gtag('js', new Date());
            {foreach $google_analytics_dynamic as $gtype => $gcode}
            {if $gtype=='gd'}
            gtag('config', '{$gcode}', {
                'send_page_view': true
            });
            {else}
            {if Apps::setting('google_analytics', 'debug') == 1}
            gtag('config', '{$gcode}', {
                'debug_mode': true
            });
            {else}
            gtag('config', '{$gcode}');
            {/if}
            {/if}
            {/foreach}
        </script>
    {/if}
{/if}

