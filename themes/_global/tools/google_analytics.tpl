<!-- 
    Google Analytics scripts 
    Note: These scripts are included directly here and should NOT be included through JsResponse 
    to prevent nested script tags and JavaScript errors.
    See: app/Integration/GoogleAnalytics/GoogleAnalyticsJsResponse.php
-->
<!-- Google Analytics -->
<script>
    CCGDPR.callOnAcceptedPerformance(() => {
        var ga_src = 'https://www.google-analytics.com/analytics.js';

        (function (i, s, o, g, r, a, m) {
            i['GoogleAnalyticsObject'] = r;
            i[r] = i[r] || function () {
                (i[r].q = i[r].q || []).push(arguments)
            }, i[r].l = 1 * new Date();
            a = s.createElement(o),
                m = s.getElementsByTagName(o)[0];
            a.async = 1;
            a.src = g;
            m.parentNode.insertBefore(a, m)
        })(window, document, 'script', ga_src, 'ga');

        ga('system.send', 'pageview');

        {if Apps::enabled('google_analytics') && !App::make('google_analytics')->isGVersion()}
        ga('create', '{Apps::setting('google_analytics', 'code')}', 'auto', 'client');
        /* customer tracker */
        ga('client.set', 'anonymizeIp', true);
        ga('client.require', 'ec');
        ga('client.set', {
            {if Auth::logged()}
            dimension1: 'logged',
            dimension2: '{Auth::customer()->group_id}'
            {else}
            dimension1: 'guest'
            {/if}
        });

        {if $order|default && !$order->google_analitycs_tracking}
            {$smarty.capture.client_analytics|default nofilter}
        {/if}

        ga('client.send', 'pageview');
        {/if}

    });
</script>
<!-- End Google Analytics -->