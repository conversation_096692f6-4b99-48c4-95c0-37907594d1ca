input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type="number"] {
    -moz-appearance: textfield;
}

.cc-form-section,
.cc-form-actions {
    margin-bottom: 30px;

    &:last-child {
        margin-bottom: 0;
    }
}

.cc-form-section-separator {
    padding-top: 30px;
    border-top: 1px solid @main-border-color;
}

.cc-form-section-step {
    padding-top: 25px;
}

.cc-form-actions {
    .cc-button {
        min-width: 260px;
    }
}

.cc-form-group-highlight {
    background-color: @field-bg-color-secondary;
    border: @field-border-width solid @field-border-color;
    padding: @field-padding;

    //.cc-form-map {
    //    margin-top: @form-vertical-offset;
    //}
}

.cc-text-transperant {
    color: transparent;
}

.cc-form-group-highlight-text {
    color: @field-text-color-secondary;
    margin: 5px @field-padding/2 @form-vertical-offset;
    font-style: italic;
    font-size: @font-size - 1px;

    &:last-child {
        margin-bottom: 0;
    }
}

.cc-form-checklist {
    .cc-form-group {
        margin-bottom: 10px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.cc-form-group {
    &.required {
        .cc-form-field-note-required {
            opacity: 1;
        }

        &.filled {
            .cc-form-field-note-required {
                transform: scale(0);
                opacity: 0;
            }

            .cc-form-field-note-success {
                transform: scale(1);
                opacity: 1;
            }
        }
    }

    &.focused,
    &.filled {
        .cc-form-field-label {
            font-size: @label-font-size;
            top: 7px;
        }
    }

    &.filled {
        .cc-select.select2-container {
            .select2-choice {
                padding-top: 12px + @field-border-width*2;
                line-height: @field-height - @field-border-width*2 - 17px;
            }
        }
    }

    &.allow-clear,
    &.allow-geolocate {
        .cc-form-field,
        .cc-form-field.form-control {
            padding-right: @field-padding*2;
        }

        &.allow-clear {
            .cc-form-field,
            .cc-form-field.form-control {
                padding-right: @field-padding*3;
            }

            .cc-form-field-clear {
                right: @field-padding*2;
            }
        }
    }

    &.allow-geolocate {
        .cc-form-field-note {
            display: none;
        }
    }

    .intl-tel-input {
        display: block;

        .country-list {
            font-size: 12px;

            &.fixed-width {
                white-space: normal;
            }
        }
    }

    .help-block-error {
        font-size: 12px;
        color: @error-color;
    }
}

.cc-form-group-tel {
    .cc-form-field-label {
        left: 52px;
    }
}

.cc-form-group-select {
    .cc-form-field-note {
        top: 12px;
        right: @select-arrow-width + 8px;
    }
}

.cc-form-field-container {
    position: relative;
    margin-bottom: 7px;

    &:last-child {
        margin-bottom: 0;
    }

    &.has-error {
        .cc-form-field,
        .cc-form-field.form-control,
        .cc-select,
        .select2-choice,
        .cc-checkbox-check {
            border-color: @error-color;
        }

        .cc-radio-check {
            box-shadow: 0 0 0 @error-color;
        }

        .cc-form-field-note-required,
        .cc-form-field-note-success {
            display: none;
        }

        .cc-form-field-note-warning {
            opacity: 1;
        }
    }
}

/*ToDo: remove .cc-form-label when refactor forms */
.cc-form-label {
    color: @label-text-color;
    font-size: @field-font-size;
    margin-bottom: 5px;

    label {
        font-weight: inherit;
        cursor: text;
    }
}

.cc-form-field-label {
    color: @label-text-color;
    font-size: @field-font-size;
    position: absolute;
    font-weight: 300;
    top: 15px;
    left: @field-padding;
    transition: @transition-duration;
    pointer-events: none;
    z-index: 1;

    label {
        font-weight: inherit;
        cursor: text;
    }
}

.cc-form-field,
.cc-form-field.form-control {
    background-color: @field-bg-color;
    border: @field-border-width solid @field-border-color;
    border-radius: 0;
    width: 100%;
    height: @field-height;
    padding: 0 @field-padding;
    padding-top: 16px;
    color: @field-text-color;
    font-size: @field-font-size;
    font-weight: normal;
    transition: border @transition-duration;
    box-shadow: none;

    &:focus {
        box-shadow: none;
        border-color: @highlight-color;
        outline: 0;
    }

    &::-webkit-input-placeholder { color: @field-bg-color; }
    &:-moz-placeholder { color: @field-bg-color; }
    &::-moz-placeholder { color: @field-bg-color; }
    &:-ms-input-placeholder { color: @field-bg-color; }
}

.cc-form-field-small,
.cc-form-field-small.form-control {
    height: @field-height-small;
    padding-top: 0;

    &::-webkit-input-placeholder { color: @label-text-color; }
    &:-moz-placeholder { color: @label-text-color; }
    &::-moz-placeholder { color: @label-text-color; }
    &:-ms-input-placeholder { color: @label-text-color; }
}

textarea.cc-form-field {
    padding-top: 24px;
    padding-bottom: @field-padding;
    height: auto;
}

.cc-form-description {
    font-style: italic;
    color: @field-description-color;
    margin-bottom: 7px;

    &:last-child {
        margin-bottom: 0;
    }
}

.cc-form-error {
    color: @error-color;
    margin-bottom: 3px;

    &:last-child {
        margin-bottom: 0;
    }
}

.cc-form-field-clear,
.cc-form-field-geolocate {
    position: absolute;
    top: (@field-height - @field-padding)/2;
    right: @field-padding;
    width: @field-padding;
    height: @field-padding;
    line-height: @field-padding;
    text-align: center;
    cursor: pointer;
    color: @field-text-color;

    &:before {
        display: inline-block;
    }
}

.cc-form-field-clear {
    &:before {
        content: "\f00d";
        font-family: @font-awesome;
    }
}

.cc-form-field-geolocate {
    right: @field-geolocate-button-position;

    &:before {
        font-size: 24px;
        content:"\f05b";
        font-family: @font-awesome;
    }

    &:after {
        content: '';
        border-left: 1px solid @field-border-color;
        position: absolute;
        top: 0;
        bottom: 0;
        left: -@field-geolocate-button-position;
    }

    .cc-tooltip {
        display: block;
        opacity: 1;
        visibility: visible;
        animation: jump 1s infinite;
    }
}

.cc-form-field-note {
    font-family: 'FontAwesome', sans-serif;
    font-size: 13px;
    line-height: 1;
    position: absolute;
    top: 7px;
    right: 6px;
    opacity: 0;
    transition: @transition-duration;
}

.cc-form-field-note-required {
    color: @warning-color;
    font-size: 8px;
    top: 9px;
    right: 8px;

    &:before {
        content:"\f069";
    }
}

.cc-form-field-note-warning {
    color: @error-color;

    &:before {
        content:"\f071";
    }
}

.cc-form-field-note-success {
    color: @success-color;

    &:before {
        content:"\f058";
    }
}

._changer-target {
    display: none;

    &.open {
        display: block;

        &._row {
            display: table;
        }

        &._col {
            display: table-cell;
        }
    }
}

/* input group */
.cc-input-group {
    @spinner-button-width: 28px;

    .bootstrap-touchspin {
        border: 0;
        border-radius: 0;
    }

    .input-group {
        display: block;
        background: transparent;
        height: auto;

        &:before {
            content: '';
            border-left: 1px solid lighten(@field-border-color, 8%);
            position: absolute;
            top: 8px;
            bottom: 8px;
            right: @spinner-button-width;
            z-index: 4;
        }

        .cc-form-field,
        .cc-form-field.form-control {
            float: none;
            padding-left: @field-padding/2;
            padding-right: @field-padding/2 + @spinner-button-width;
            text-align: center;
        }

        .input-group-btn-vertical {
            position: static;
            display: block;
            width: 0;
            height: 0;
        }

        .btn {
            background: transparent;
            border: 0;
            min-width: initial;
            min-height: initial;
            width: @spinner-button-width;
            height: 19px;
            padding: 0;
            margin: 0;
            font-size: 14px;
            color: @field-text-color-secondary;
            text-align: center;
            position: absolute;
            right: 1px;
            z-index: 3;
            outline: 0;

            
            text-wrap: nowrap;


            &:hover,
            &:focus,
            &:active {
                color: @main-strong-text-color;
                background-color: transparent;
                box-shadow: none;
                outline: 0;
            }

            &.bootstrap-touchspin-up {
                top: 1px;

                .fa {
                    top: 2px;
                }
            }

            &.bootstrap-touchspin-down  {
                bottom: 1px;

                .fa {
                    top: -2px;
                }
            }

            .fa {
                position: relative;
            }
        }
    }
}

/* checkbox and radio */
.cc-checkbox-button {
    padding: 5px;
    color: black;
    position: relative;
    transition: all 0.1s ease; /* Makes the changes animate smoothly */
    border: 1px solid #ccc;
    overflow: hidden;
    text-align: left;
    background: #ededed;
    display: block;
    font-size: 12px;

    .logo {
        img {
            transition: all 0.1s ease;
            filter: grayscale(1);
            max-height: 22px;
            max-width: 100px;
        }
    }

    .cc-checkbox-button-text {
        display: block;
        height: 22px;
        margin-bottom: 10px;
        text-align: center;

        .logo {
            display: block;
        }

        .text {
            padding-left: 5px;
            white-space: nowrap;
            overflow: hidden;
            display: none;
        }
    }

    &:after {
        content: '';
        display: block;
        width: 30%;
        height: 2px;
        background: #ff4238;
        position: absolute;
        bottom: 5px;
        left: 50%;
        transition: all 0.1s ease;
        -webkit-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        transform: translateX(-50%)
    }
}

.cc-checkbox-button-input {
    display: none;

    &:checked + .cc-checkbox-button {
        background: #ffffff;
        color: #999;

        &:after {
            background: limegreen;
            box-shadow: 0 0 10px limegreen;
        }

        .logo {
            img {
                transition: all 0.1s ease;
                filter: grayscale(0);
            }
        }
    }
}

.cc-checkbox,
.cc-radio {
    display: inline-block;
    vertical-align: middle;
    min-height: @checkbox-size;
    padding-left: @checkbox-size + @checkbox-offset;
    padding-top: 3px;
    margin: 0;
    font-weight: inherit;
    position: relative;
    cursor: pointer;
}

.cc-checkbox-check,
.cc-radio-check {
    background-color: @field-bg-color;
    position: absolute;
    top: 0;
    left: 0;
}

.cc-checkbox-check {
    border: @field-border-width solid @field-border-color;
    color: @field-text-color;
    width: @checkbox-size;
    height: @checkbox-size;
    line-height: @checkbox-size;
    text-align: center;
    font-size: 14px;

    &:before {
        display: none;
        font-family: 'FontAwesome', sans-serif;
        content: '\f00c';
    }
}

.cc-checkbox-input {
    display: none;

    &:checked {
        & ~ .cc-checkbox-check {
            &:before {
                display: block;
            }
        }

        & ~ .cc-checkbox-text {
            color: @checkbox-active-color;
        }
    }
}

.cc-radio-check {
    border: 6px solid @field-bg-color;
    box-shadow: 0 0 0 @field-border-width @field-border-color;
    border-radius: 50%;
    width: @checkbox-size - @field-border-width*2;
    height: @checkbox-size - @field-border-width*2;
    margin: @field-border-width;
}

.cc-radio-input {
    display: none;

    &:checked {
        & ~ .cc-radio-check {
            background-color: @field-text-color;
        }

        & ~ .cc-radio-text {
            color: @checkbox-active-color;
        }
    }

    &.permanently:disabled {
        & ~ .cc-radio-check {
            background-color: @label-text-color;
            border-color: @label-text-color;
        }
    }
}

.cc-radio-image {
    text-align: center;

    cc-radio {
        padding: 0;
        cursor: pointer;
        transition: .2s;
        position: relative;

        @media (min-width: 1025px) {
            &:hover {
                &:before {
                    opacity: .2;
                }
            }

            &:active {
                &:before {
                    opacity: .4;
                }
            }
        }

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: @highlight-color;
            opacity: 0;
            transition: .2s;
        }
    }

    cc-radio-check {
        display: none;
    }
}

/* #Switch button
-------------------------------------------------------------------------------*/

@switch-on-bg-color: #3cc2db;
@switch-on-bg-color-secondary: #40444d;
@switch-on-text-color: #fff;
@switch-on-button-bg-color: #fff;
@switch-on-button-icon-color: #3cc2db;
@switch-off-bg-color: #cdd3d6;
@switch-off-bg-color-secondary: #40444d;
@switch-off-text-color: #fff;
@switch-off-button-bg-color: #fff;
@switch-off-button-icon-color: #cdd3d6;

@switch-offset: 3px;
@switch-width: 70px;
@switch-button-size: 30px;

._switchbox {
    display: inline-block;
    border-radius: (@switch-button-size + @switch-offset*2)/2;
    vertical-align: middle;
    width: @switch-width;
    height: @switch-button-size + @switch-offset*2;;
    position: relative;
    cursor: pointer;

    &:before,
    &:after {
        width: 50%;
        height: @switch-button-size + @switch-offset*2;
        font-size: 13px;
        line-height: @switch-button-size + @switch-offset*2;
        font-weight: bold;
        text-align: center;
        position: absolute;
        top: 0;
        z-index: 1;
    }

    &:before {
        content: 'ON';
        color: @switch-on-text-color;
        left: 2px;
    }

    &:after {
        content: 'OFF';
        color: @switch-off-text-color;
        right: 2px;
    }

    + ._switchbox-label {
        margin-left: 7px;
    }
}

._switchbox-background {
    background-color: @switch-off-bg-color;
    border-radius: 3px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

._switchbox-button {
    background-color: @switch-off-button-bg-color;
    border-radius: 3px;
    width: @switch-button-size;
    height: @switch-button-size;
    line-height: @switch-button-size;
    text-align: center;
    position: absolute;
    top: @switch-offset;
    left: @switch-offset;
    z-index: 2;
    transition: .2s;
}

._switchbox-input {
    display: none;

    &:checked {
        & ~ ._switchbox-button {
            background-color: @switch-on-button-bg-color;
            left: @switch-width - @switch-button-size - @switch-offset;
        }

        & ~ ._switchbox-background {
            background-color: @switch-on-bg-color;
        }
    }
}

._switchbox-label {
    display: inline-block;
    vertical-align: middle;

    + ._switchbox {
        margin-left: 7px;
    }
}

/* Select2
-------------------------------------------------------------------------------*/

select.cc-select {
    background-color: @field-bg-color;
    border: @field-border-width solid @field-border-color;
    color: @field-text-color;
    width: 100%;
    height: @field-height;

    &[multiple] {
        height: @field-height;
        font-size: 0;
        overflow: hidden;
    }

    option {
        &:disabled {
            display: none;
        }
    }
}

.cc-select.select2-container {
    display: block;
    background-color: @field-bg-color;
    border: @field-border-width solid @field-border-color;
    padding: 0;
    height: auto;

    &.select2-dropdown-open {
        .select2-choice,
        .select2-choices {
            background: @field-bg-color;
            border-color: @field-border-color;
            border-radius: 0;
        }
    }

    &.select2-drop-above {
        .select2-choice  {
            border: 0;
        }
    }

    .select2-choice {
        background: transparent;
        border: 0;
        border-radius: 0;
        box-shadow: none;
        font-size: @field-font-size;
        padding: 0;
        height: @field-height - @field-border-width*2;
        line-height: @field-height - @field-border-width*2;
        color: @label-text-color;

        .select2-chosen {
            margin: 0;
            padding-left: @field-padding;
            padding-right: @select-arrow-width + @field-padding + 3px;
            padding-top: 3px;
            color: @field-text-color;

            .image {
                float: right;
                margin-left: 10px;
                position: relative;
                top: -3px;

                img {
                    max-height: 20px;
                }
            }
        }

        .select2-arrow {
            background: transparent;
            border: none;
            border-radius: 0;
            width: @select-arrow-width;

            &:before {
                content: '';
                border-left: @field-border-width solid @field-border-color;
                position: absolute;
                top: 11px;
                bottom: 11px;
                left: 0;
            }

            b {
                background: transparent;

                &:before {
                    content: '\f107';
                    font-family: @font-awesome;
                    font-size: @field-font-size;
                    font-weight: normal;
                    color: @field-border-color;
                    .centerer(true, true);
                }
            }
        }
    }

    &.select2-allowclear {
        .select2-choice {
            .select2-chosen {
                margin: 0;
                padding-right: @field-height + @field-padding*2;
            }

            abbr {
                .centerer(false, true);
                background: transparent;
                height: auto;
                width: auto;
                color: @field-text-color;
                line-height: 1;
                right: @field-padding;
            }
        }

        &.cc-shipping-provider-container {
            abbr {
                width: 15px;
                height: 15px;
                right: 45px;
                z-index: 2;

                &:before {
                    content: "\f00d";
                    font-family: "Font Awesome 5 Pro";
                    font-weight: 300;
                    font-size: 15px;
                    color: #999;
                }
            }
        }
    }
}

.cc-select-drop.select2-drop {
    background-color: @field-bg-color;
    border-width: @field-border-width;
    border-style: solid;
    border-color: @field-border-color;
    border-radius: 0;
    box-shadow: none;
    line-height: 1.5;
    overflow: hidden;
    margin-top: 2px;

    &.select2-drop-above {
        border: @field-border-width solid @field-border-color;
        box-shadow: none;
        border-radius: 0;
        margin: -2px 0 0;

        &.select2-drop-active {
            border-color: @field-border-color;
        }
    }

    .select2-search {
        padding: @field-padding;
        border-bottom: 1px solid #e4e4e4;

        input {
            background: @field-bg-color;
            border: @field-border-width solid @field-border-color;
            color: @field-text-color;
        }
    }

    .select2-results {
        padding: 0;
        margin: 0;
        max-height: 204px;
        position: relative;

        .select2-result {
            border-bottom: 1px solid #e4e4e4;
            color: @field-text-color;

            &:last-child {
                border-bottom: 0;
            }

            &.select2-highlighted {
                background-color: @secondary-bg-color;
                color: @field-text-color;
            }

            .select2-result-label {
                font-size: @field-font-size;
                padding: 14px @field-padding;
                color: inherit !important;
            }
        }

        .select2-no-results,
        .select2-searching {
            background-color: transparent;
            padding: 14px @field-padding;
            font-size: @field-font-size - 2px;
        }
    }

    &.cc-shipping-provider-drop {
        .select2-results {
            .image {
                float: right;
                margin-left: 10px;

                img {
                    max-height: 20px;
                }
            }

            .cc-shipping-provider-option {
                display: table;
                width: 100%;

                > .text, > .image {
                    display: table-cell;
                }

                > .image {
                    width: 80px;
                    margin-left: auto;
                    float: none;
                    text-align: right;

                    .distance {
                        display: block;
                        margin-top: 5px;
                        color: #999;
                        text-align: right;

                        > .far {
                            margin-right: 5px;
                        }
                    }
                }
            }
        }
    }
}

.cc-form-map {
    padding-bottom: 35%;
    position: relative;

    @media (min-width: @screen-sm) {
        &-higher {
            padding-bottom: 53.633%;
        }
    }
}

@media (max-width: @screen-md-max) {
    .cc-form-row {
        margin-left: -10px;
        margin-right: -10px;
    }

    [class^="cc-form-col-"] {
        padding-left: 10px;
        padding-right: 10px;
    }
}

@media (max-width: @screen-xs-max) {
    .cc-form-group-highlight {
        padding: @field-padding-mobile;
    }

    .cc-form-group-highlight-text {
        margin-left: 3px;
        margin-right: 3px;
    }

    .cc-form-field-label {
        left: @field-padding-mobile;
    }

    .cc-form-field,
    .cc-form-field.form-control {
        padding-left: @field-padding-mobile;
        padding-right: @field-padding-mobile;
    }

    .cc-select.select2-container {
        .select2-choice {
            .select2-chosen {
                padding-right: @select-arrow-width + @field-padding + 3px;
            }
        }

        &.select2-allowclear {
            .select2-choice {
                abbr {
                    right: @field-padding-mobile;
                }
            }
        }
    }

    .cc-select-drop.select2-drop {
        .select2-search {
            padding: @field-padding-mobile;
        }

        .select2-results {
            .select2-result {
                .select2-result-label {
                    padding-left: @field-padding-mobile;
                    padding-right: @field-padding-mobile;
                }
            }

            .select2-no-results,
            .select2-searching {
                padding-left: @field-padding-mobile;
                padding-right: @field-padding-mobile;
            }
        }
    }

    .cc-form-map {
        padding-bottom: 50%;
    }
}

.cc-form-field-note-success-hidden {
    .cc-form-field-note-success {
        display: none;
    }
}