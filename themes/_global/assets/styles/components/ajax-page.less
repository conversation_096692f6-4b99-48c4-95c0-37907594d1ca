@ajax-page-bg-color: #fff;

.ajax-page-open {
    overflow: hidden;

    .select2-drop-mask {
        z-index: 9999999999999999999999999999999999999999999999999999992;
    }

    .select2-drop,
    .intl-tel-input.iti-container {
        z-index: 9999999999999999999999999999999999999999999999999999993;
    }
}

.ajax-page {
    background-color: @ajax-page-bg-color;
    position: fixed !important;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    z-index: 9999999999999999999999999999999999999999999999999999991;
}

.ajax-page.left-right {
    transition: 1s cubic-bezier(0.77, 0, 0.175, 1);
    transform: translate(100%, 0);
}

.ajax-page.left-right.animated {
    transform: translate(0, 0);
}

@media (max-width: @screen-tablet-max) {
    .ajax-page {
        -webkit-overflow-scrolling: touch;
    }
}