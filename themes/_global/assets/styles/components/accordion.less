.cc-accordion-simple {
    .cc-accordion-section {
        border-color: transparent;

        &:first-child {
            border-color: transparent;
        }

        &:last-child {
            .cc-accordion-section-body {
                border-bottom: 0;
            }
        }

        .cc-accordion-section-head {
            height: auto;
        }
    }

    .cc-accordion-section-body {
        border-top: 1px solid @main-border-color;
        border-bottom: 1px solid @main-border-color;
        padding: @accordion-vertical-offset 0;
        margin: @accordion-head-vertical-offset 0;
    }
}

.cc-accordion-section {
    border: 1px solid transparent;
    border-top-color: @main-border-color;
    border-bottom-color: @main-border-color;
    margin-top: -1px;
    transition: .2s;
    position: relative;

    &:first-child {
        border-top-color: @main-border-color;
    }

    .cc-accordion {
        margin-bottom: -@accordion-vertical-offset - 1px;

        &.cc-accordion-simple {
            margin-bottom: 0;
        }

        > .cc-accordion-section {
            &:last-child {
                border-bottom-color: transparent;
            }
        }

        .cc-accordion-section-body {
            padding-top: @accordion-vertical-offset/2;
            margin-left: @checkbox-size + @checkbox-offset;
            margin-right: 0;
        }
    }

    .cc-accordion-section {
        @media @hover {
            &:hover {
                background: transparent;
                border-color: @main-border-color;
                border-left-color: transparent;
                border-right-color: transparent;
            }
        }

        &.active {
            background: transparent;
            border-color: @main-border-color;
            border-left-color: transparent;
            border-right-color: transparent;

            > .cc-accordion-section-head {
                font-weight: bold;

                label {
                    font-weight: bold;
                }
            }
        }

        .cc-accordion-section-head-item {
            &:first-child {
                padding-left: 0;
            }

            &:last-child {
                padding-right: 0;
            }
        }
    }

    .cc-accordion-simple {
        .cc-accordion-section-body {
            margin-left: 0;
        }
    }

    @media @hover {
        &:hover {
            background-color: @smooth-highlight-color;
            border-color: @main-border-color;
        }
    }

    &.active {
        background-color: @secondary-bg-color;
        border-color: @secondary-border-color;
        z-index: 1;
    }
}

.cc-accordion-section-inactive {
    @media @hover {
        &:hover {
            background: transparent;
            border-left-color: transparent;
            border-right-color: transparent;
        }
    }

    .cc-accordion-section-head {
        cursor: default;
    }
}

.cc-accordion-section-head {
    display: table;
    width: 100%;
    height: 55px;
    cursor: pointer;

    &.active {
        color: @checkbox-active-color;
    }
}

.cc-accordion-section-head-item {
    display: table-cell;
    vertical-align: middle;
    padding: @accordion-head-vertical-offset @accordion-horizontal-offset;

    .cc-edit-icon {
        position: absolute;
        top: 0;
        right: 0;
    }
}

.cc-accordion-section-head-item-addon {
    width: 1px;
    white-space: nowrap;
}

.cc-accordion-section-head-item-radio,
.cc-accordion-section-head-item-image,
.cc-accordion-section-head-item-text {
    display: inline-block;
    vertical-align: middle;
}

.cc-accordion-section-head-item-image {
    margin-right: 10px;
    
    img {
        max-height: @checkbox-size;
    }
}

.cc-accordion-section-head-item-meta-text {
    font-size: @font-size-small;
    font-weight: 300;
    color: @main-meta-text-color;
}

.cc-accordion-section-head-item-price {
    text-align: right;
    color: @main-strong-text-color;
}

.cc-accordion-section-head-item-price-value {
    white-space: nowrap;
    font-weight: bold;
}

.cc-accordion-section-body {
    padding-bottom: @accordion-vertical-offset;
    margin-left: @checkbox-size + @checkbox-offset + @accordion-horizontal-offset;
    margin-right: @accordion-horizontal-offset;
}

@media (max-width: @screen-xs-max) {
    .cc-accordion-section-body {
        //margin-left: @checkbox-size + @table-offset;
        margin-left: 15px;
    }

    .cc-accordion-address {
        .cc-accordion-section-head {
            display: block;
            padding: 7px 0;
            height: auto !important;
        }

        .cc-accordion-section-head-item {
            display: block;
        }

        .cc-accordion-section-head-item-addon {
            width: auto;
            white-space: normal;
            text-align: right;
        }
    }

    .cc-accordion-section {
        .cc-accordion {
            .cc-accordion-section-body {
                margin-left: 0;
            }
        }
    }
}