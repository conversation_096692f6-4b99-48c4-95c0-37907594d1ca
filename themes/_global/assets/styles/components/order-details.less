/*=============================================================================*\
    ORDER DETAILS
\*=============================================================================*/

.cc-order-details {
    font-size: @font-size;
    font-family: @font-family;
    max-width: 768px;
    margin: 0 auto;
    line-height: 1.3;
    text-align: left;

    h1,h2,h3,h4,h5,h6 {
        font-family: @font-family;
        color: @main-titles-color;
        line-height: 1.3;
    }

    a {
        color: @highlight-color;
    }
}

.cc-order-details-meta,
.cc-order-details-products {
    background-color: @main-bg-color;
    border: 1px solid @main-border-color;
    box-shadow: 0 0 3px rgba(0, 0, 0, .1);
    margin-bottom: 10px;
    color: @main-text-color;

    &:last-child {
        margin-bottom: 0;
    }
}

/* Meta */
.cc-order-details-meta {
    padding: 15px;
    overflow: hidden;
}

.cc-order-details-meta-col {
    float: left;
    width: 50%;
    padding: 15px;

    &:nth-child(2n+1) {
        clear: both;
    }
}

.cc-order-details-meta-col-multi {
    .cc-order-details-meta-item {
        float: left;
        width: 50%;
        padding-right: 15px;

        &:nth-child(2n+1) {
            clear: both;
        }
    }
}

.cc-order-details-meta-col-summary {
    float: right;
}

.cc-order-details-meta-item {
    display: block;
}

.cc-order-details-meta-item-title {
    margin-bottom: 5px;
    font-family: @font-family;
    font-size: inherit;
    font-weight: bold;
    color: @main-titles-color;
}

.cc-order-details-meta-item-value {
    margin-bottom: 5px;

    &:last-child {
        margin-bottom: 0;
    }
}

.cc-order-details-meta-item-date {
    display: block;
}

/* Payments */
.cc-order-details-payments {
    table {
        width: 100%;
        border-collapse: collapse;
        white-space: nowrap;
    }

    th,
    td {
        border-width: 0 0 1px;
        border-style: solid;
        border-color: @main-border-color;
        padding: 8px;

        &:first-child {
            padding-left: 0;
        }

        &:last-child {
            padding-right: 0;
        }
    }

    th {
        border-top-width: 1px;
    }

    .text-alignright {
        text-align: right;
    }
}

/* Product */
.cc-order-details-products {
    padding: 0 30px;
}

.cc-order-details-product {
    border-bottom: 1px solid @main-border-color;
    padding: 30px 0;
    position: relative;

    &:last-child {
        border-bottom: 0;
    }
}

.cc-order-details-product-details {
    display: table;
    width: 100%;
}

.cc-order-details-product-image {
    display: table-cell;
    vertical-align: middle;
    width: 102px;
}

.cc-order-details-product-image-thumb {
    display: block;
    border: 1px solid @main-border-color;
}

.cc-order-details-product-image-thumb-holder {
    display: block;
    padding-bottom: 100%;
    text-align: center;
    position: relative;

    img {
        max-width: 100%;
        max-height: 100%;
        .centerer(true, true);
    }
}

.cc-order-details-product-info {
    display: table-cell;
    vertical-align: middle;
    color: @main-text-color;
    padding: 0 20px;
}

.cc-order-details-product-name {
    margin-bottom: 8px;

    &:last-child {
        margin-bottom: 0;
    }

    h5 {
        color: @main-titles-color;
        font-weight: normal;
        font-size: 16px;
    }

    a {
        color: inherit;
    }
}

.cc-order-details-product-parameters {
    font-size: 13px;
    margin-bottom: 10px;
    color: @main-meta-text-color;

    &:last-child {
        margin-bottom: 0;
    }
}

.cc-order-details-product-parameter {
    display: table;
    margin-bottom: 3px;

    &.cc-order-details-product-parameter-files {
        display: flex;
        flex-direction: column;
        gap: 5px;
        padding-block: 6px;
    }

    &:last-child {
        margin-bottom: 0;
    }
}

.cc-order-details-product-parameter-name {
    display: table-cell;
    vertical-align: top;
}

.cc-order-details-product-parameter-value {
    display: table-cell;
    vertical-align: top;
    color: @main-text-color;
    word-break: break-word;
    padding-left: 4px;
}

.cc-order-details-product-price {
    display: table-cell;
    vertical-align: middle;
    text-align: right;
    white-space: nowrap;
}

.cc-order-details-product-price-old {
    text-decoration: line-through;
    font-size: 12px;
}

.cc-order-details-product-price-value {
    font-weight: bold;
}

/* Summary */
.cc-order-details-summary {
    color: @main-text-color;

    ul {
        list-style-type: none;
    }
}

.cc-order-details-summary-item {
    display: table;
    width: 100%;
}

.cc-order-details-summary-item-title,
.cc-order-details-summary-item-value {
    display: table-cell;
    vertical-align: top;
    padding: 3px 0;
}

.cc-order-details-summary-item-title {
    padding-right: 10px;
    color: @main-titles-color;
    font-weight: bold;
}

.cc-order-details-summary-item-value {
    text-align: right;
}

.cc-order-details-summary-total {
    border-top: 1px solid @main-border-color;
    font-size: 14px;
    font-weight: bold;
    color: @main-strong-text-color;
    padding-top: 5px;
    margin-top: 5px;
}

.cc-order-details-actions {
    border-top: 1px solid @main-border-color;
    padding-top: 15px;
    margin-top: 5px;
    font-size: 13px;
    text-align: right;
}

.cc-order-details-action {
    margin-left: 10px;
}

@media (max-width: @screen-sm) {
    .cc-order-details-meta {
        padding: 0;
    }

    .cc-order-details-meta-row {
        display: block;
        overflow: hidden;
    }

    .cc-order-details-meta-col-multi {
        float: none;
        width: auto;
        margin: 0 -15px;
        overflow: hidden;

        .cc-order-details-meta-item {
            padding: 0 15px;
        }
    }

    .cc-order-details-meta-col-summary {
        width: 100%;
    }

    .cc-order-details-products {
        padding: 0 15px;
    }

    .cc-order-details-product {
        padding: 15px 0;
    }

    .cc-order-details-product-info {
        padding: 0 15px;
    }
}

@media (max-width: @screen-xs) {
    .cc-order-details-product-details {
        display: flex;
        flex-flow: row wrap;
    }

    .cc-order-details-product-image {
        display: block;
        width: 72px;
        margin-bottom: 10px;
    }

    .cc-order-details-product-info {
        display: block;
        align-self: center;
        width: calc(100% ~'-' 82px);
        padding: 0;
        margin-left: auto;
        margin-bottom: 10px;
    }

    .cc-order-details-product-name {
        margin-bottom: 0;

        h5 {
            font-size: 14px;
        }
    }

    .cc-order-details-product-price {
        display: flex;
        width: 100%;
        text-align: right;
    }

    .cc-order-details-product-price-value {
        order: 2;
        margin-left: auto;
    }

    .cc-order-details-product-price-quantity {
        order: 1;
        margin-right: 10px;
    }
}