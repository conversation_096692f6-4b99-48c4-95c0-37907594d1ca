.cc-account-table {
    .text-alignleft {
        text-align: left;
    }

    .text-alignright {
        text-align: right;
    }
    
    .text-aligncenter {
        text-align: center;
    }

    .align-top {
        vertical-align: top;
    }

    .align-bottom {
        vertical-align: bottom;
    }

    .align-middle {
        vertical-align: middle;
    }

    table {
        width: 100%;
    }

    th,
    td {
        padding: 7px;
        border-style: solid;
        border-width: 0 0 1px;
        border-color: @main-border-color;
    }

    th {
        border-width: 1px 0;
    }

    .cc-order-details {
        padding: 15px 0;
    }
}

.cc-account-table-toggle {
    color: @main-strong-text-color;

    &.loading {
        .fa {
            animation: spin .5s linear infinite;

            &:before {
                content: "\f01e";
            }
        }
    }
}

.status-green {
    color: @success-color;
}

.status-orange {
    color: @warning-color;
}

.status-red {
    color: @error-color;
}

.status-grey {
    color: @meta-color;
}

.cc-account-table-order-tracker {
    margin-top: 30px;

    table {
        td {
            padding: 17px 7px;
        }
    }
}

.cc-account-table-order-tracker-date {
    color: @secondary-text-color;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
}

.cc-account-table-order-tracker-date {
    strong {
        color: @black-color;
        font-weight: 700;
        font-size: 18px;
        line-height: 22px;
    }
}

.cc-account-table-order-tracker-value {
    color: @black-color;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
}

@media (max-width: @screen-sm) {
    .cc-account-table-orders {
        table,
        thead,
        tbody,
        th,
        td,
        tr {
            display: block;
        }

        tr:not(.cc-account-table-empty-row) td {
            display: flex;
            align-items: center;
            justify-content: end;
            gap: 10px;
            padding-right: 15px;
            border-bottom: 0;
        }

        thead tr {
            display: none;
        }

        tr {
            margin-bottom: 20px;
        }

        td {
            text-align: right;
            padding-left: 50%;
            position: relative;
            min-height: 50px;

            &:before {
                content: attr(data-label);
                position: absolute;
                top: 50%;
                left: 0;
                transform: translateY(-50%);
                width: 50%;
                padding-left: 15px;
                font-weight: bold;
                text-align: left;
                line-height: 1.2;
            }
        }

        .text-aligncenter {
            text-align: right;
        }

        .cc-order-details {
            padding-top: 0;
        }

        tr:not(.cc-account-table-empty-row) {
            border: 1px solid #e8e8e8;
        }

        .cc-account-table-empty-row > td {
            padding-left: 0;
            padding-bottom: 15px;
        }
    }
}