
/*  Modal
-------------------------------------------------------------------------------*/

.modal.medium {
    .modal-dialog {
        width: 90%;
        margin: 20px auto;
    }

    @media (min-width: 900px) {
        .modal-dialog {
            width: 800px;
        }
    }
}

.modal-tabs {
    background-color: #fff;
    color: #586169;
    font-size: 14px;
    font-weight: normal;
    font-style: normal;
    line-height: 1.5;

    * {
        outline: 0;
    }

    h1, h2, h3, h4, h5, h6 {
        font-weight: 600;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: 0;
        color: #40444d;
        text-align: left;
    }
}

.modal-tabs-title,
.modal-tabs-footer {
    background-color: #fafafa;
    position: relative;
}

.modal-tabs-title {
    display: table;
    width: 100%;
    table-layout: fixed;
    border-bottom: 1px solid #d5d5d6;
    position: relative;

    h3 {
        font-size: 24px;
    }
}

.modal-tabs-title-logo,
.modal-tabs-title-text {
    display: table-cell;
    vertical-align: middle;
    padding: 20px;
}

.modal-tabs-title-logo {
    width: 240px;

    img {
        max-width: 200px;
        max-height: 200px;
    }
}

.modal-tabs-title-text {
    position: relative;
    padding-right: 50px;
}

.modal-tabs-title-close {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translate(0, -50%);
    font-size: 18px;
    line-height: 1;
    color: #ccc;
    cursor: pointer;
}

.modal-tabs-footer {
    border-top: 1px solid #d5d5d6;
    text-align: right;
    padding: 20px;
    overflow: hidden;
    display: flex;
    width: 100%;
    align-content: center;
    align-items: center;
    justify-content: space-between;
}

.modal-tabs-footer-left {
    order: 1;
}

.modal-tabs-footer-right {
    order: 2;
}

.modal-tabs-body {
    display: table;
    table-layout: fixed;
    width: 100%;
}

.modal-tabs-nav,
.modal-tabs-group {
    display: table-cell;
    vertical-align: top;
}

.modal-tabs-nav {
    background-color: #fafafa;
    width: 240px;
    font-weight: 500;

    a {
        display: block;
        padding: 15px 20px;
        color: #586169;

        &.active {
            background-color: #fff;
            color: #40444d;
        }
    }
}

.modal-tabs-group {
    padding: 20px;
}

.modal-tab {
    display: none;

    &:first-child {
        display: block;
    }
}

.modal-tab-title {
    display: table;
    width: 100%;
    margin-bottom: 10px;

    h4 {
        font-size: 18px;
    }
}

.modal-tab-title-main,
.modal-tab-title-addon {
    display: table-cell;
    vertical-align: middle;
}

.modal-tab-title-main {
    padding-right: 20px;
}

.modal-tab-title-addon {
    width: 1px;
    white-space: nowrap;
}

.modal-tab-description {
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }

    p,
    ul,
    ol {
        margin-bottom: 10px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.cookies-used {
    h5 {
        font-size: 14px;
        margin-bottom: 5px;
    }

    ul {
        list-style-type: none;
        border-top: 1px solid #d5d5d6;
    }

    li {
        border-bottom: 1px solid #d5d5d6;
        padding: 3px 0;
    }
}

#gdpr_popup.modal.in {
    z-index: 1100000;
}

#gdpr_popup {
    .modal-tabs-body {
        display: block;
        overflow: auto;
        max-height: 500px;
    }
}

@media (max-width: 767px) {
    .modal-tabs-body,
    .modal-tabs-nav,
    .modal-tabs-group,
    .modal-tabs-title,
    .modal-tabs-title-logo,
    .modal-tabs-title-text,
    .modal-tabs-footer {
        display: block;
    }

    .modal-tabs-title-logo,
    .modal-tabs-nav {
        width: auto;
    }

    .modal-tabs-title-logo,
    .modal-tabs-title-text,
    .modal-tabs-group,
    .modal-tabs-footer {
        padding: 15px;
    }

    .modal-tabs-title-logo {
        padding-bottom: 0;
        padding-right: 30px;
    }

    .modal-tabs-title-text {
        position: static;
    }

    .modal-tabs-title-close {
        top: 20px;
        transform: none;
    }

    .modal-tabs-nav {
        a {
            padding: 7px 15px;
        }
    }

    .modal-tabs-footer-left,
    .modal-tabs-footer-right {
        float: none;
        margin-bottom: 15px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    #gdpr_popup {
        .modal-tabs-body {
            overflow: auto;
            max-height: 420px;
        }
    }
}

@media (max-width: 479px) {
    #gdpr_popup {
        .modal-tabs-body {
            max-height: 56vh;
        }
    }
}