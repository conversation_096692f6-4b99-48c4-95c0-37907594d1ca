@item-width1: 420px;
@item-width2: 350px;
@item-width3: 300px;
@item-width4: 250px;
@item-width5: 220px;
@item-width-small: 180px;
@item-spacing: 15px;
@popup-horizontal-spacing: 30px;
@item-spacing-small: 7px;

.modal-dialog {
    &.modal-dialog-crosssell {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        width: auto;
        padding: 30px 15px;
        margin: 0;
        min-height: 100%;

        .modal-content {
            background: #fff;
            color: #666;
            border: 0;
            margin: 0;

            .modal-body {
                padding: 0;
            }
        }
    }
}

.cc-crosssell {
    padding: @item-spacing*2;
    font-size: 14px;
    overflow: hidden;
    max-width: 4*@item-width4 + 6*@item-spacing + 2*@popup-horizontal-spacing;
}

.cc-crosssell-count-1 {
    max-width: @item-width1 + 2*@popup-horizontal-spacing;

    .cc-crosssell-item {
        width: @item-width1;
    }
}

.cc-crosssell-count-2 {
    max-width: 2*@item-width2 + 2*@item-spacing + 2*@popup-horizontal-spacing;

    .cc-crosssell-item {
        width: @item-width2;
    }
}

.cc-crosssell-count-3,
.cc-crosssell-count-6,
.cc-crosssell-count-9 {
    max-width: 3*@item-width3 + 4*@item-spacing + 2*@popup-horizontal-spacing;

    .cc-crosssell-item {
        width: @item-width3;
    }
}

.cc-crosssell-count-4 {
    max-width: 4*@item-width4 + 6*@item-spacing + 2*@popup-horizontal-spacing;

    .cc-crosssell-item {
        width: @item-width4;
    }
}

.cc-crosssell-count-5,
.cc-crosssell-count-10,
.cc-crosssell-count-15 {
    max-width: 5*@item-width5 + 8*@item-spacing + 2*@popup-horizontal-spacing;

    .cc-crosssell-item {
        width: @item-width5;
    }
}

.cc-crosssell-header {
    border-bottom: 1px solid #ddd;
    padding-bottom: 20px;
    margin-bottom: 25px;
    font-size: 18px;
    text-align: center;

    h2 {
        font-size: 36px;
    }
}

.cc-crosssell-items {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: flex-start;
    margin: -2*@item-spacing -@item-spacing 20px;
    overflow: hidden;
}

.cc-crosssell-item {
    border: 1px solid #ddd;
    background-color: #fff;
    box-shadow: 0 0 13px rgba(0, 0, 0, .1);
    padding: 15px;
    margin: 0;
    text-align: center;
    transition: 1s;
    transform-origin: 50% 0;
    width: @item-width1 - 2*@item-spacing;
    margin: 2*@item-spacing @item-spacing 0;
    .parameters-small();

    ._parameter {
        text-align: left;
    }

    ._parameter-select {
        max-width: 100%;
    }

    .cc-button {
        padding-left: 15px;
        padding-right: 15px;
        height: auto;
    }
}

.cc-crosssell-item-image {
    margin-bottom: 15px;
}

.cc-crosssell-item-image-thumb {
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    padding-bottom: 100%;
    position: relative;

    img {
        max-width: 100%;
        max-height: 100%;
        .centerer(true, true);
    }
}

.cc-crosssell-item-info {
    margin-bottom: 10px;
}

.cc-crosssell-item-title {
    margin-bottom: 5px;
}

.cc-crosssell-item-price-new,
.cc-crosssell-item-price-old {
    display: inline-block;
}

.cc-crosssell-item-price-new {
    font-size: 24px;
    font-weight: bold;
}

.cc-crosssell-item-price-old {
    font-size: 12px;
    text-decoration: line-through;
}

.cc-crosssell-item-actions {
    margin-bottom: 10px;
}

.cc-crosssell-item-options {
    ._button {
        .cc-button();
        .cc-button-small();
        padding-left: 15px;
        padding-right: 15px;
        height: auto;

        ._figure-stack {
            vertical-align: middle;
        }
    }
}

.cc-crosssell-footer {
    text-align: right;
}

.cc-crosssell-footer-no-thanks {
    margin-right: 10px;
}

@media (max-width: 1601px) {
    .cc-crosssell-header {
        font-size: 14px;

        h2 {
            font-size: 26px;
        }
    }
}

@media (max-width: 1439px) {
    .cc-crosssell-header {
        h2 {
            font-size: 22px;
        }
    }

    .cc-crosssell {
        max-width: 5*@item-width5 + 8*@item-spacing + 2*@popup-horizontal-spacing;

        .cc-crosssell-item {
            width: @item-width5;
        }
    }

    .cc-crosssell-count-1,
    .cc-crosssell-count-2 {
        max-width: 2*@item-width2 + 2*@item-spacing + 2*@popup-horizontal-spacing;

        .cc-crosssell-item {
            width: @item-width2;
        }
    }

    .cc-crosssell-count-3 {
        max-width: 3*@item-width5 + 4*@item-spacing + 2*@popup-horizontal-spacing;
    }
}

@media (max-width: @screen-sm-max) {
    .cc-crosssell {
        padding: 20px;
    }

    .cc-crosssell-items {
        margin-top: -2*@item-spacing-small;
        margin-left: -@item-spacing-small;
        margin-right: -@item-spacing-small;

        .cc-crosssell-item {
            margin: 2*@item-spacing-small @item-spacing-small 0;
            width: calc(50% ~'-' 2*@item-spacing-small);
            max-width: @item-width2;
        }
    }
}

@media (max-width: @screen-xs-max) {
    .modal-dialog {
        &.modal-dialog-crosssell {
            padding: 0;
            align-items: flex-start;
            min-height: 100%;

            .modal-content {
                width: 100%;
                height: 100%;
                box-shadow: none;
            }
        }
    }

    .cc-crosssell-header {
        padding-bottom: 15px;

        h2 {
            font-size: 20px;
        }
    }

    .cc-crosssell-items {
        .cc-crosssell-item {
            padding: @item-spacing-small;
            max-width: @item-width5;
        }
    }

    .cc-crosssell-items-1 {
        .cc-crosssell-item {
            width: 90%;
            max-width: initial;
        }
    }
}

@media (max-width: @screen-xxs-max) {
    .cc-crosssell {
        padding: 2*@item-spacing-small @item-spacing-small;
    }

    .cc-crosssell-items {
        max-width: initial;
        margin-top: -@item-spacing-small;
        margin-left: -@item-spacing-small/2;
        margin-right: -@item-spacing-small/2;

        .cc-crosssell-item {
            margin: 2*@item-spacing-small/2 @item-spacing-small/2 0;
            width: calc(50% ~'-' @item-spacing-small);
        }
    }

    .cc-crosssell-items-1 {
        .cc-crosssell-item {
            width: 90%;
        }
    }
}

@media (max-width: @screen-xxxs-max) {
    .cc-crosssell {
        padding: 20px;
    }

    .cc-crosssell-items {
        .cc-crosssell-item {
            width: 100%;
            max-width: initial;
            padding: @item-spacing;
        }
    }
}

