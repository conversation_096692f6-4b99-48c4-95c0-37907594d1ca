/** Auto-completion menu */
.fs-autocomplete {
    width: 100% !important;
    //max-width: 1140px;
    left: 50% !important;
    overflow: auto;
    transform: translate(-50%, 0);
    -webkit-transform: translate(-50%, 0);
    z-index: 9999;
    display: block;
}

.fs-dropdown .fs-dropdown-listbox .before_special {
    color: #aaaaaa;
    text-decoration: line-through;
    font-size: 12px;
}

.fs-dropdown .fs-dropdown-listbox .tier_price {
    color: #666666;
    font-size: 10px;
}

.fs-dropdown .fs-dropdown-listbox .tier_price .tier_value {
    color: #3cc2db;
    font-size: 12px;
}

.fs-dropdown .fs-dropdown-listbox .info-without-thumb .category-tag {
    color: #3284b6;
}

.fs-dropdown .fs-dropdown-listbox .info-without-thumb .details {
    font-size: 10px;
    color: #666;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.fs-dropdown .fs-dropdown-listbox .info-without-thumb .details em {
    color: #222222;
}

.fs-dropdown .fs-dropdown-listbox {
    position: absolute;
    margin-top: -1px;
    right: 0;
    width: 100%;
    z-index: 1000 !important;
    border: 1px solid #BBB;
    border-top: 3px solid #8EB4D0;
    border-radius: 1px;
    background: white;
}

@media (min-width: 992px) {
    .fs-dropdown .fs-dropdown-listbox {
        width: 71.1%;
        min-width: 800px;
    }
}

.fs-dropdown .fs-dropdown-listbox .col-2 {
    position: relative;
}

.fs-dropdown .fs-dropdown-listbox .col-2 .col-left {
    width: 67%;
}

.fs-dropdown .fs-dropdown-listbox .col-2 .col-right {
    width: 33%;
}

.fs-dropdown .fs-dropdown-listbox .fs-no-results-products {
    padding: 40px 40px;
    min-height: 250px;
}

.fs-dropdown .fs-dropdown-listbox .fs-no-results-products .title {
    font-weight: bold;
    margin-bottom: 30px;
    font-size: 16px;
}

.fs-dropdown .fs-dropdown-listbox .fs-no-results-products .suggestions {
    margin-bottom: 30px;
}

.fs-dropdown .fs-dropdown-listbox .fs-no-results-products .see-all a {
    color: #636363;
    font-weight: bold;
}

.fs-dropdown .fs-dropdown-listbox .fs-no-results {
    padding: 5px 10px;
    font-style: italic;
}

.fs-dropdown .fs-dropdown-listbox .category {
    padding: 4px;
    color: rgb(166, 166, 166);
    text-align: left;
    font-size: 0.8em;
    text-transform: uppercase;
    font-weight: bold;
}

.fs-dropdown .fs-dropdown-listbox .category-suggestions {
    padding: 4px;
    color: #3cc2db;
    text-align: left;
    font-size: 0.7em;
    text-transform: uppercase;
}

.fs-dropdown .fs-dropdown-listbox .fs-suggestion-hit {
    display: block;
    position: relative;
    padding: 5px 10px;
    color: #000;
    text-align: left;
    text-decoration: none;
}

.fs-dropdown .fs-dropdown-listbox .fs-dataset-suggestions .fs-suggestion-hit {
    padding-left: 30px;
}

.fs-dropdown .fs-suggestions svg.algolia-glass-suggestion.magnifying-glass {
    position: absolute;
    right: auto;
    left: 5px;
    top: 7px;
    fill: #A6A6A6;
    stroke: #A6A6A6;
}

.fs-dropdown .fs-dropdown-listbox.fs-without-products .fs-dropdown-products {
    background-color: #F9F9F9;
}

.fs-dropdown .fs-dropdown-listbox.fs-without-products #autocomplete-products-footer {
    display: none;
}

.fs-dropdown .fs-dropdown-listbox .fs-dataset-products .fs-suggestion {
    display: inline-block;
    vertical-align: top;
    width: 100%;
}

@media (min-width: 768px) {
    .fs-dropdown .fs-dropdown-listbox .fs-dataset-products .fs-suggestion {
        display: inline-block;
        vertical-align: top;
        width: 50%;
    }
}

.fs-dropdown .fs-dropdown-listbox .fs-dataset-products .fs-suggestion-hit {
    padding: 10px;
}

.fs-dropdown-list {
    width: 100%;
    display: flex;
    flex-direction: row-reverse;
    flex-wrap: wrap;
}

.fs-dropdown-list:after {
    clear: both;
    content: '';
}

.fs-dropdown .fs-dropdown-products {
    box-sizing: border-box;
}

.fs-dropdown .fs-dropdown-meta {
    box-sizing: border-box;
}

.fs-dropdown.reverse .fs-dropdown-meta {
    float: left;
    min-width: 100%;
}

.fs-dropdown.reverse .fs-dropdown-products {
    min-width: 100%;
    display: flex;
    height: 100%;
    flex: 1;
}

@media (min-width: 768px) {
    .fs-dropdown-products {
        border-left: solid 1px #eee;
    }


    .fs-dropdown.reverse .fs-dropdown-products {
        border-right: solid 1px #eee;
    }
}

.fs-dataset-suggestions {
    display: none;
}

@media (min-width: 768px) {
    .fs-dataset-suggestions {
        display: block;
    }
}

@media (min-width: 768px) {
    .fs-dataset-products .fs-suggestions {
        margin: 10px auto 50px auto;
    }
}

.fs-dataset-products .fs-suggestions:after {
    content:'';
    display:block;
    clear: both;
}

.fs-dropdown .fs-dropdown-listbox .fs-suggestion-hit {
    padding-left: 10px;
}

.fs-dropdown .fs-dropdown-listbox .fs-suggestions {
    margin-bottom: 20px;
}

.fs-dropdown .fs-dropdown-listbox .fs-cursor .fs-suggestion-hit {
    background-color: #f2f2f2;
}

.fs-dropdown .fs-dropdown-listbox .fs-suggestion-hit em {
    font-weight: bold;
    font-style: normal;
}

.fs-dropdown .fs-dropdown-listbox .fs-suggestion-price {
    font-size: 1.1em;
    color: #333;
    font-weight: bold;
    height: 22px;
}

.fs-dropdown .fs-dropdown-listbox .fs-suggestion-hit .fs-suggestion-thumb {
    float: left;
}

.fs-dropdown .fs-dropdown-listbox .fs-suggestion-hit .fs-suggestion-thumb img {
    width: 50px;
}

.fs-dropdown .fs-dropdown-listbox .fs-suggestion-hit .fs-suggestion-info {
    margin-left: 60px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.fs-dropdown .fs-dropdown-listbox .fs-suggestion-hit .fs-suggestion-info .fs-suggestion-match {
    font-size: 0.8em;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    max-width: 100%;
    text-overflow: ellipsis;
}

.fs-dropdown .fs-dropdown-listbox .fs-suggestion-hit .fs-suggestion-info .fs-suggestion-match em {
    color: #222;
}

.fs-dropdown .fs-dropdown-listbox .footer_algolia {
    position: absolute;
    width: 100%;
    padding: 10px;
    text-align: center;
    bottom: 0;
    left: 4px;
    font-size: 13px;
}

.fs-dropdown.reverse .fs-dropdown-listbox .footer_algolia {
    left: auto;
    right: 4px;
}

@media (min-width: 768px) {
    .fs-dropdown .fs-dropdown-listbox .footer_algolia {
        width: 30%;
    }
}

.fs-dropdown .fs-dropdown-listbox .footer_algolia span {
    color: #B8B8B8;
    font-size: 10px;
}

.fs-dropdown .fs-dropdown-listbox .footer_algolia img {
    display: inline;
    height: 1.5em;
    vertical-align: bottom;
    max-width: 130px;
}

/** Search Box */

#algolia-searchbox {
    position: relative;
}

#algolia-searchbox .clear-cross, #algolia_instant_selector .clear-cross {
    position: absolute;
    display: none;
    background: url("data:image/svg+xml;utf8,<svg width=\'12\' height=\'12\' viewBox=\'0 0 12 12\' xmlns=\'http://www.w3.org/2000/svg\' opacity=\'0.6\'><path d=\'M.566 1.698L0 1.13 1.132 0l.565.566L6 4.868 10.302.566 10.868 0 12 1.132l-.566.565L7.132 6l4.302 4.3.566.568L10.868 12l-.565-.566L6 7.132l-4.3 4.302L1.13 12 0 10.868l.566-.565L4.868 6 .566 1.698z\'></path></svg>") no-repeat center center / contain;
    cursor: pointer;
    width: 16px;
    height: 16px;
}

#algolia-searchbox .clear-query-autocomplete {
    bottom: 22px;
    right: 9px;
}

#algolia_instant_selector .cross-wrapper .clear-refinement {
    display: block;
    position: relative;
    top: 5px;
    left: 5px;
}

#algolia-searchbox .magnifying-glass {
    position: absolute;
    bottom: 21px;
    right: 7px;
    width: 20px;
    height: 20px;
    display: block;
    background: url("data:image/svg+xml;utf8,<svg width=\'40\' height=\'40\' viewBox=\'0 0 40 40\'  fill=\'%23A6A6A6\' xmlns=\'http://www.w3.org/2000/svg\'><path d=\'M15.553 31.107c8.59 0 15.554-6.964 15.554-15.554S24.143 0 15.553 0 0 6.964 0 15.553c0 8.59 6.964 15.554 15.553 15.554zm0-3.888c6.443 0 11.666-5.225 11.666-11.668 0-6.442-5.225-11.665-11.668-11.665-6.442 0-11.665 5.223-11.665 11.665 0 6.443 5.223 11.666 11.665 11.666zm12.21 3.84a2.005 2.005 0 0 1 .002-2.833l.463-.463a2.008 2.008 0 0 1 2.833-.003l8.17 8.168c.78.78.78 2.05-.004 2.833l-.462.463a2.008 2.008 0 0 1-2.834.004l-8.168-8.17z\' fill-rule=\'evenodd\'/></svg>") no-repeat center right / 20px;
}

@media (min-width: 768px) {
    #algolia-searchbox .magnifying-glass {
        bottom: 6px;
    }
}

@media (min-width: 768px) {
    #algolia-searchbox .clear-query-autocomplete {
        bottom: 8px;
    }
}


#algolia-searchbox .algolia-search-input:focus:not([value=""]) {
    background: transparent;
}

#algolia-searchbox .algolia-search-input {
    position: static !important;
}

#algolia-searchbox .algolia-search-input:focus {
    outline: 0;
    box-shadow: none;
    border: solid 1px #3cc2db;
}

/** Instant Search */

#algolia_instant_selector {
    margin: 10px;
}

#algolia_instant_selector a,
#algolia_instant_selector a:hover,
#algolia_instant_selector a:focus {
    outline: 0;
}

#algolia_instant_selector:after {
    content: "";
    clear: both;
    display: block;
}

#algolia_instant_selector .title img {
    width: 200px;
}

#algolia_instant_selector .text-ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

#algolia_instant_selector #instant-search-bar {
    float: left;
    width: calc(100% - 140px);
    height: 40px;
    font-family: "Raleway", "Helvetica Neue", Verdana, Arial, sans-serif;
    border: solid 2px #3cc2db;
    border-left: none;
    padding-left: 6px;
    padding-top: 2px;
}

#algolia_instant_selector.with-facets #algolia-left-container {
    min-height: 1px;
}

#algolia_instant_selector .hits .infos {
    margin: 0 15px;
    padding: 6px 10px;
    color: #aaa;
    text-align: center;
    background: #F4F4F4;
    font-size: 12px;
    clear: both;
    line-height: 32px;
}

#algolia_instant_selector .hits .infos::after {
    margin: 0 15px;
    padding: 6px 10px;
    color: #aaa;
    text-align: center;
    background: #F4F4F4;
    font-size: 12px;
    clear: both;
}

#algolia_instant_selector .hits .infos strong {
    color: #555;
}

#algolia_instant_selector .hits .sort-by-label {
    margin-right: 8px;
}

#algolia_instant_selector .hits .no-results {
    margin: 40px 25px;
    color: #636363;
    font-size: 16px;
}

#algolia_instant_selector a:hover {
    color: #666666;
}

#algolia_instant_selector .hits .no-results strong {
    color: #555;
}

#algolia_instant_selector .hits .no-results .clear-button {
    cursor: pointer;
}

#algolia_instant_selector .hits .no-results .popular-searches {
    text-align: left;
    margin-top: 20px;
    margin-bottom: 30px;
}

#algolia_instant_selector .ais-hits--item .product-reviews-summary {
    text-align: center;
    margin-top: 5px;
    margin-bottom: 5px;
}

.ais-infinite-hits--showmore {
    clear: both;
}

/**
 * Facets
 */

#algolia_instant_selector .infos .algolia-logo {
    font-size: 10px;
}

#algolia_instant_selector .facets {
    padding-top: 50px;
}

@media (max-width: 992px) {
    #algolia_instant_selector .facets {
        padding-top: 10px;
    }
}

#algolia_instant_selector .ais-current-refined-values.facet .ais-current-refined-values--body {
    padding-left: 8px;
}

#algolia_instant_selector .facet {
    margin-bottom: 15px;
    padding-bottom: 6px;
    background-color: white;
    border: solid 1px #efefef;
}

#algolia_instant_selector .facet:not(:first-child) {
    margin-top: 10px;
}

#algolia_instant_selector .ais-header {
    margin-bottom: 7px;
}

#algolia_instant_selector .ais-header.ais-current-refined-values--header {
    margin-bottom: 13px;
}

#algolia_instant_selector .facet .name {
    padding: 4px 8px;
    font-weight: 500;
    background-color: #f4f4f4;
    color: #757575;
    text-align: left;
    text-transform: uppercase;
}

.ais-current-refined-values.facet {
    position: relative;
}

#algolia_instant_selector .as-range-slider--base {
    margin-top: 45px;
}

#algolia_instant_selector .hierarchical .ais-hierarchical-menu--list {
    padding-left: 10px;
}

#algolia_instant_selector .hierarchical .ais-hierarchical-menu--list.ais-hierarchical-menu--list__lvl0 {
    padding-left: 0;
}

#algolia_instant_selector .facet .sub_facet:not(:last-child) {
    border-bottom: 1px dotted #e0e0e0;
}

#algolia_instant_selector .text-center {
    text-align: center;
}

#algolia_instant_selector em {
    background-color: #CFEAFA;
    font-style: normal;
}

#algolia_instant_selector h1 {
    margin: 0;
}

#algolia_instant_selector .result {
    display: block;
    margin: 15px 0;
    padding: 16px 24px;
    border: solid 1px #EDEDED;
    background-color: white;
}

#algolia_instant_selector .price-wrapper {
    height: 25px;
}

#algolia_instant_selector .result:hover {
    border-color: #b9b9b9;
    text-decoration: none;
}

#algolia_instant_selector .result .result-thumbnail {
    text-align: center;
}

#algolia_instant_selector .result .result-thumbnail img {
    display: inline;
    height: 148px;
    width: auto;
}

#algolia_instant_selector .result .result-thumbnail .no-image {
    display: block;
    height: 148px;
    background: #FAFAFA;
}

#algolia_instant_selector .result .ratings .rating-box {
    margin: 0 auto 10px;
    float: inherit;
}

#algolia_instant_selector h3
{
    margin-bottom: 0;
}

#algolia_instant_selector .result .price {
    text-align: center;
    font-size: 1.2em;
    color: #3cc2db;
}

#algolia_instant_selector .result .result-title {
    font-size: 14px;
    text-align: center;
    color: #636363;
    line-height: 1.3;
}

#algolia_instant_selector .ui-widget-header {
    background-color: #bcbcbc;
}

#algolia_instant_selector .result-thumbnail img {
    width: 100%;
}


/** INSTANT SEARCH BAR **/

#algolia_instant_selector #instant-search-bar-container {
    padding-top: 0;
    margin-bottom: 15px;
    width: 100%;
    position: relative;
}

#algolia_instant_selector #instant-search-bar-container:after {
    clear: both;
    content: "";
    display: block;
}

#algolia_instant_selector .as-search-box--input {
    width: 100%;
    height: 40px;
    font-family: "Raleway", "Helvetica Neue", Verdana, Arial, sans-serif;
}

#algolia_instant_selector #instant-search-bar:focus {
    outline: 0;
    box-shadow: none;
}

#algolia_instant_selector #instant-search-box label {
    display: none;
}

#algolia_instant_selector .instant-search-bar-label {
    background-color: #3cc2db;
    height: 40px;
    line-height: 24px;
    padding: 8px 12px;
    color: white;
    float: left;
    width: 140px;
    text-align: right;
}

.ais-search-box--magnifier {
    background: transparent;
    position: absolute;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    top: 8px;
    left: 7px;
}

.ais-search-box--magnifier svg {
    display: block;
    vertical-align: middle;
    height: 23px;
    width: 23px;
    fill: #FFFFFF;
    stroke: #FFFFFF;
}

.ais-search-box--reset {
    background: none !important;
    box-shadow: none !important;
    cursor: pointer;
    position: absolute;
    top: 5px;
    right: 5px;
    margin: 0;
    border: 0;
    padding: 7px 8px !important;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.ais-search-box--reset svg {
    display: block;
    width: 17px;
    height: 17px;
    fill: #636363;
}

#algolia_instant_selector .before_special {
    color: #aaaaaa;
    text-decoration: line-through;
    font-size: 12px;
}

#algolia_instant_selector .tier_price {
    color: #666666;
    font-size: 12px;
}

#algolia_instant_selector .tier_price .tier_value {
    color: #3cc2db;
    font-size: 1.4rem;
}

#algolia_instant_selector button:hover {
    background: #2E8AB8 none repeat scroll 0 0;
    cursor: pointer;
}

.ais-price-ranges--button {
    background: #39C none repeat scroll 0 0;
    padding: 5px 15px;
    margin-left: 10px;
    border: 0 none;
    color: #FFF;
    font-size: 13px;
    font-weight: normal;
    font-family: "Raleway","Helvetica Neue",Verdana,Arial,sans-serif;
    line-height: 19px;
    text-align: center;
    text-transform: uppercase;
    vertical-align: middle;
}

#algolia_instant_selector button:not(.ais-price-ranges--button) {
    background: #f4f4f4 none repeat scroll 0 0;
    display: block;
    margin: 0 auto auto;
    padding: 7px 15px;
    border: 0 none;
    color: #666666;
    font-size: 13px;
    font-weight: normal;
    font-family: "Raleway","Helvetica Neue",Verdana,Arial,sans-serif;
    line-height: 19px;
    text-align: center;
    text-transform: uppercase;
    vertical-align: middle;
    white-space: nowrap;
}

#algolia_instant_selector button:not(.ais-price-ranges--button):hover {
    background: rgb(53, 153, 204) none repeat scroll 0 0;
    color: white;
}

#algolia_instant_selector .pull-left {
    float: left;
}

#algolia_instant_selector .pull-right {
    float: right;
}

#algolia_instant_selector #refine-toggle {
    font-family: "Raleway", "Helvetica Neue", Verdana, Arial, sans-serif;
    text-align: center;
    height: 40px;
    line-height: 40px;
    border: solid 1px #EDEDED;
    margin: 10px 0 20px 0;
    font-weight: bold;
    cursor: pointer;
}

#algolia-static-content {
    margin-bottom: 20px;
}

/** GRID */

#algolia_instant_selector, #algolia_instant_selector *,
#search_mini_form, #search_mini_form * {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
#algolia_instant_selector:before, #algolia_instant_selector:after,
#algolia_instant_selector *:before, #algolia_instant_selector *:after,
#search_mini_form:before, #search_mini_form:after,
#search_mini_form *:before, #search_mini_form *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

#search_mini_form label {
    display: none;
}

#algolia_instant_selector .row {
    margin-left: -15px;
    margin-right: -15px;
}

#algolia_instant_selector .col-md-3, #algolia_instant_selector .col-md-4, #algolia_instant_selector .col-md-9, #algolia_instant_selector .col-md-12 {
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
}

@media (min-width: 768px) {
    #algolia_instant_selector .col-sm-6 {
        width: 50%;
        float: left;
    }
}

@media (min-width: 992px) {
    #algolia_instant_selector .col-md-3, #algolia_instant_selector .col-md-4, #algolia_instant_selector .col-md-9, #algolia_instant_selector .col-md-12 {
        float: left;
    }

    #algolia_instant_selector .col-md-12 {
        width: 100%;
    }

    #algolia_instant_selector .col-md-9 {
        width: 75%;
    }

    #algolia_instant_selector .col-md-4 {
        width: 33.33333333%;
    }

    #algolia_instant_selector .col-md-3 {
        width: 25%;
    }
}
#algolia_instant_selector .row:before,
#algolia_instant_selector .row:after {
    content: " ";
    display: table;
}

#algolia_instant_selector .row:after {
    clear: both;
}

#algolia_instant_selector .visible-xs, #algolia_instant_selector .visible-sm {
    display: none!important
}

#algolia_instant_selector .hidden-xs, #algolia_instant_selector .hidden-sm {
    display: block !important
}

@media (max-width: 767px) {
    #algolia_instant_selector .visible-xs {
        display: block !important
    }

    #algolia_instant_selector .hidden-xs {
        display: none!important
    }

    .algolia-search-block {
        clear: both;
    }
}

@media (max-width: 992px) {
    #algolia_instant_selector .visible-sm {
        display: block !important
    }

    #algolia_instant_selector .hidden-sm {
        display: none!important
    }
}

.fs-dropdown, .fs-dropdown-listbox, .fs-dropdown-products, .fs-dataset-products, .fs-dropdown-meta, .fs-dropdown-meta-lists, .fs-dropdown-meta-list, .fs-dropdown-info, .fs-suggestion-match, .fs-suggestion-hit, .fs-suggestion-thumb, .fs-suggestion-info, .fs-suggestion-title, .fs-suggestion-price {
    display: block;
}

.fs-suggestion-title {
    overflow: hidden;
}

.fs-dropdown {
    font-size: 14px;
}

.fs-dropdown h6 {
    font-size: 14px !important;
    margin-bottom: 5px;
}

.fs-dropdown-products {
    padding: 10px;
}

.fs-dropdown-meta {
    padding: 20px 20px 40px;
    position: relative;
}

.algolia-clearfix {
    clear: both;
}

.fs-dropdown:after,
.fs-dropdown-list:after {
    clear: both;
    content: '';
}

.fs-dropdown-meta-lists,
.fs-dropdown-meta-list {
    margin-bottom: 20px;
}

.fs-dropdown-meta-lists:last-child,
.fs-dropdown-meta-list:last-child {
    margin-bottom: 0;
}

.fs-powered-by {
    font-size: 12px;
    color: #999;
    position: absolute;
    bottom: 20px;
    left: 20px;

    img,
    svg {
        height: 14px;
        width: auto;
        vertical-align: middle;
        position: relative;
        top: -1px;
        margin-left: 3px;
        fill: #5468ff;
    }
}

.algolia-autocomplete {
    width: 100% !important;
    max-width: 1140px;
    left: 50% !important;
    overflow: auto;
    transform: translate(-50%, 0);
    -webkit-transform: translate(-50%, 0);
}

.algolia-autocomplete-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .5);
}

.fs-dropdown .fs-dropdown-listbox {
    width: auto;
    min-width: initial;
    margin: 0 auto;
    position: static;
    border: 1px solid #ccc;
}

.fs-no-results {
    font-size: 11px;
    text-transform: uppercase;
    color: #999;
}

@media (min-width: 480px) and (max-width: 767px) {
    .fs-dropdown .fs-dropdown-listbox .fs-dataset-products .fs-suggestion {
        width: 50%;
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .fs-dropdown .fs-dropdown-products {
        width: 70%;
    }

    .fs-dropdown .fs-dropdown-meta {
        width: 30%;
    }
    .fs-dropdown .fs-dropdown-listbox .fs-dataset-products .fs-suggestion {
        width: 50%;
    }
}

@media (min-width: 992px) {
    .fs-dropdown .fs-dropdown-products {
        width: 75%;
    }

    .fs-dropdown .fs-dropdown-meta {
        width: 25%;
    }

    .fs-dropdown .fs-dropdown-listbox .fs-dataset-products .fs-suggestion {
        width: 33.33%;
    }
}

@media (max-width: 767px) {
    .fs-dropdown-list {
        display: block;
    }

    .fs-dropdown-meta-lists {
        display: flex;
        margin-left: -15px;
        margin-right: -15px;
        width: calc(100% ~'+' 30px);
        flex-wrap: wrap;
    }

    .fs-dropdown-meta-list {
        flex-basis: 50%;
        max-width: 50%;
        padding: 0 15px;
    }
}