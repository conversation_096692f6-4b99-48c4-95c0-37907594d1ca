//ToDo: .cc-button is set to separate the styles for checkout. must remove it.

.cc-button {
    .loader-dots {
        font-size: 0;
        line-height: 0;
        text-align: center;
        white-space: nowrap;
    }

    .loader-dot {
        display: inline-block;
        background-color: @highlight-color;
        border-radius: 100%;
        width: 12px;
        height: 12px;
        margin: 2px;
        -webkit-animation: dot-pulse 1s infinite linear;
        animation: dot-pulse 1s infinite linear;

        &:nth-child(1) {
            -webkit-animation-delay: -.5s;
            animation-delay: -.5s;
        }

        &:nth-child(2) {
            -webkit-animation-delay: -.25s;
            animation-delay: -.25s;
        }

        &:nth-child(3) {
            -webkit-animation-delay: 0;
            animation-delay: 0;
        }
    }
}

.loader-dots-inline {
    font-size: 0;
    line-height: 0;
    text-align: center;
    white-space: nowrap;

    .loader-dot {
        display: inline-block;
        background-color: @highlight-color;
        border-radius: 100%;
        width: 12px;
        height: 12px;
        margin: 2px;
        -webkit-animation: dot-pulse 1s infinite linear;
        animation: dot-pulse 1s infinite linear;

        &:nth-child(1) {
            -webkit-animation-delay: -.5s;
            animation-delay: -.5s;
        }

        &:nth-child(2) {
            -webkit-animation-delay: -.25s;
            animation-delay: -.25s;
        }

        &:nth-child(3) {
            -webkit-animation-delay: 0;
            animation-delay: 0;
        }
    }
}

.js-box-loader-container {
    position: relative;
    min-height: 100px;

    &:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        content: '';
        z-index: 1000;
        background-color: rgba(255, 255, 255, .9);
    }
}

body.js-box-loader-container {
    > .js-box-loader {
        position: fixed;
    }
}

.js-box-loader {
    z-index: 1001;
    .centerer(true, true);
}

.js-box-loader-animation {
    display: block;
    border-width: 25px;
    border-style: solid;
    border-color: @highlight-color;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    margin: 0 auto;
    animation: fade-unveil 1.2s infinite;
    transform: scale(0);
    opacity: 0;
}

.js-box-loader-text {
    display: block;
    margin-top: 10px;
    color: @highlight-color;
    font-size: 16px;
    animation: fade-pulse 1.2s infinite;
    text-align: center;
}