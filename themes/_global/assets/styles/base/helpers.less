.cc-inactive {
    opacity: .3 !important;
}

.cc-refreshing {
    transition: @refresh-transition-end-duration linear !important;
}

.cc-refresh {
    background-color: @refresh-color !important;

    .cc-checkout-table-row,
    .cc-checkout-table-row.active {
        background-color: @refresh-color !important;
    }
}

@import 'spacings';

@media (min-width: 992px) {
    ._grid-row {
        ._vmiddle {
            display: inline-block;
            vertical-align: middle;
            float: none;
        }
        
        ._vbottom {
            display: inline-block;
            vertical-align: bottom;
            float: none;
        }
    }
}