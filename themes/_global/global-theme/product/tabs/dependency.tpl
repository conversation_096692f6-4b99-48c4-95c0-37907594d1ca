<script>
    var sf = {
        'review_confirmed_order': '{t}sf.review.confirmed_order{/t}',
        'review_answers': '{t}sf.reviews.answers_add{/t}',
        'review_show_answers': '{t}sf.reviews.show_answers{/t}',
        'review_add_new_answer': '{t}sf.review.add_new_answer{/t}',
        'review_global_act_cancel': '{t}sf.global.act.cancel{/t}',
        'review_publish': '{t}sf.review.publish{/t}',
        'review_success_submit_answer': '{t}sf.review.success.submit.answer{/t}',
        'answer_need_approve': '{t}sf.review.answer_need_approve{/t}',
        'sf.review_answer_error': '{t}sf.review.answer.error{/t}',
    };
    // if click ._review-head .nav-pills  li
    var tab = getActiveTab().tab;

    let nextPageUrlReview = null;
    let nextPageUrlQuestion = null;

    function getNextPage(page = null) {
        var productId = $('[data-product-id]').attr('data-product-id');

        if (page == null) {
            if (getActiveTab().tab == '#tab-review') {
                return "/product_preview/reviews/" + productId + "?page=2";
            }

            if (getActiveTab().tab == '#tab-questions') {
                return "/product_preview/questions/" + productId + "?page=2";
            }
        }
        return page;
    }

    $(function () {
        let userAvatars = $(getActiveTab().tab + ' .user-avatar .thumbnail:not(.default)');
        setAvatarBgColors(userAvatars);

        $('.js-select2').select2({
            minimumResultsForSearch: -1
        });
    });

    function getActiveTab(type = null, other = null) {
        let urlEndpoint = '';
        let activeTab = $('._review-head ul .active').attr('href');
        if (activeTab == '#tab-review') {
            switch (type) {
                case 'get_answers':
                    urlEndpoint = '{route('product_preview.comment.comment', ['comment_id' => 'COMMENTID'])}';
                    break;
                case 'create_answer':
                    urlEndpoint = "/product_preview/create/answer/" + other;
                    break;
                case 'get_list':
                    urlEndpoint = "/product_preview/reviews/" + other;
                    break;
                case 'sort_by':
                    urlEndpoint =  "/product_preview/reviews/" + other
                    break;
            }
        }

        if (activeTab == '#tab-questions') {
            switch (type) {
                case 'get_answers':
                    urlEndpoint = '{route('product_preview.comment.comment.question', ['comment_id' => 'COMMENTID'])}';
                    break;
                case 'create_answer':
                    urlEndpoint = "/product_preview/create/question/answer/" + other;
                    break;
                case 'get_list':
                    urlEndpoint = "/product_preview/questions/" + other;
                    break;
                case 'sort_by':
                    urlEndpoint =  "/product_preview/questions/" + other
                    break;
            }
        }

        return {
            'url': urlEndpoint,
            'tab': activeTab
        }

    }

    function reviewBody(results, reviewsList) {
        results.result.map(function (review) {
            $('<li class="comment-item clearfix" data-comment-id="' + review.id + '">' +
                '<div class="row">' +
                '<div class="col-md-2">' +
                '<div class="comment-head">' +
                '<div class="comment-user-avatar">' +
                '<div class="user-avatar user-avatar-sm">' +
                '<div class="thumbnail">' +
                '<span class="ini">' + review.author.avatar + '</span>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<div class="comment-user-meta">' +
                '<div class="comment-user-name">' + review.author.name + '</div>' +
                '<div class="created-at">' + review.created +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<div class="col-md-10">' +
                '<div class="comment-body">' +
                '<h3 class="comment-title">' + review.title + '</h3>' +
                '<span class="comment-meta">' +
                '<input class="js-review-rating-ajax" value="' + review.rating + '">' +
                '</span>' +
                '<small class="checked-order _tooltips">' +
                // '<span class="_tooltip"><span' +
                // ' class="_tooltip-inner"><span>'+sf.review_confirmed_order+'</span></span></span>' +
                '<i class="fas fa-badge-check"></i>' + sf.review_confirmed_order +
                '</small>' +
                '<div class="comment-value">' + review.comment + '</div>' +
                '<div class="comment-add-answers-wrap">' +
                '<a href="javascript:;" class="comment-view-answers js-view-answers" rel="noindex nofollow"><i class="far fa-comment-alt-lines"></i>&nbsp;' + sf.review_show_answers + ' (<span class ="js-answers-count">' + review.answers + '</span>)</a>' +
                '<a href="javascript:;" class="comment-add-answer js-add-answer" rel="noindex nofollow"><i class="far fa-comment-alt-plus"></i>&nbsp;' + sf.review_answers + '</a>' +
                '</div>' +
                '<div class="comments comments-add-answer js-add-answer-body" style="display: none;">' +
                '<div class="_mb-20">' +
                '<div class="comment-user-avatar">' +
                '<div class="user-avatar user-avatar-sm">' +
                '<div class="thumbnail default" style="background-color: #aaa;">' +
                '<span class="ini"><i class="far fa-user"></i></span>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<span class="comment-answer-username">' + sf.review_add_new_answer + '</span>' +
                '<br/>' +
                '</div>' +
                '<div class="js-add-comment-form">' +
                '<div class="cc-form-group">' +
                '<textarea class="_textarea" name="comment" cols="30" rows="10" placeholder="' + sf.review_add_new_answer + '..."></textarea>' +
                '</div>' +
                '<br/>' +
                '<div class="text-right">' +
                '<a href="javascript:;"' +
                ' class="_button _button-secondary js-disabled js-loading js-cancel-answer">' +
                '<span class="_figure-stack">' +
                '<span class="_figure-stack-label">' + sf.review_global_act_cancel + '</span>' +
                '</span>' +
                '</a>' +
                '<a href="javascript:;"' +
                ' class="_button js-disabled js-loading submit-js js-submit-answer _md-mt-0 _md-ml-10 _mt-10">' +
                '<span class="_figure-stack">' +
                '<span class="_figure-stack-label">' + sf.review_publish + '</span>' +
                '</span>' +
                '</a>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<ul class="comments comments-answers js-comments-answers"' +
                'id="comment-' + review.id + '" style="display: none;">' +
                '</ul>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</li>').appendTo(reviewsList);
            let acceptAnswers = {Apps::setting('product_review', 'accept_answers')};
            let $addAnswer = $('.js-add-answer');
            if ((cc_customer_data.id == null && acceptAnswers == 0) ||
                (cc_customer_data.id != null && acceptAnswers == 0) ||
                cc_customer_data.id == null) {
                $addAnswer.hide();
            }
            if (review.answers == 0) {
                $('[data-comment-id=' + review.id + ']').find('.js-view-answers').hide();
            }
        });
    }

    function questionBody(results, reviewsList) {
        results.result.map(function (review) {
            $('<li class="comment-item clearfix" data-comment-id="' + review.id + '">' +
                '<div class="row">' +
                '<div class="col-md-2">' +
                '<div class="comment-head">' +
                '<div class="comment-user-avatar">' +
                '<div class="user-avatar user-avatar-sm">' +
                '<div class="thumbnail">' +
                '<span class="ini">' + review.author.avatar + '</span>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<div class="comment-user-meta">' +
                '<div class="comment-user-name">' + review.author.name + '</div>' +
                '<div class="created-at">' + review.created +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<div class="col-md-10">' +
                '<div class="comment-body">' +
                '<h3 class="comment-title">' + review.title + '</h3>' +
                '<span class="comment-meta">' +
                '</span>' +
                '<small class="checked-order _tooltips">' +
                // '<span class="_tooltip"><span' +
                // ' class="_tooltip-inner"><span>'+sf.review_confirmed_order+'</span></span></span>' +
                '<i class="fas fa-badge-check"></i>' + sf.review_confirmed_order +
                '</small>' +
                '<div class="comment-value">' + review.comment + '</div>' +
                '<div class="comment-add-answers-wrap">' +
                '<a href="javascript:;" class="comment-view-answers js-view-answers" rel="noindex nofollow"><i class="far fa-comment-alt-lines"></i>&nbsp;' + sf.review_show_answers + ' (<span class ="js-answers-count">' + review.answers + '</span>)</a>' +
                '<a href="javascript:;" class="comment-add-answer js-add-answer" rel="noindex nofollow"><i class="far fa-comment-alt-plus"></i>&nbsp;' + sf.review_answers + '</a>' +
                '</div>' +
                '<div class="comments comments-add-answer js-add-answer-body" style="display: none;">' +
                '<div class="_mb-20">' +
                '<div class="comment-user-avatar">' +
                '<div class="user-avatar user-avatar-sm">' +
                '<div class="thumbnail default" style="background-color: #aaa;">' +
                '<span class="ini"><i class="far fa-user"></i></span>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<span class="comment-answer-username">' + sf.review_add_new_answer + '</span>' +
                '<br/>' +
                '</div>' +
                '<div class="js-add-comment-form">' +
                '<div class="cc-form-group">' +
                '<textarea class="_textarea" name="comment" cols="30" rows="10" placeholder="' + sf.review_add_new_answer + '..."></textarea>' +
                '</div>' +
                '<br/>' +
                '<div class="text-right">' +
                '<a href="javascript:;"' +
                ' class="_button _button-secondary js-disabled js-loading js-cancel-answer">' +
                '<span class="_figure-stack">' +
                '<span class="_figure-stack-label">' + sf.review_global_act_cancel + '</span>' +
                '</span>' +
                '</a>' +
                '<a href="javascript:;"' +
                ' class="_button js-disabled js-loading submit-js js-submit-answer _md-mt-0 _md-ml-10 _mt-10">' +
                '<span class="_figure-stack">' +
                '<span class="_figure-stack-label">' + sf.review_publish + '</span>' +
                '</span>' +
                '</a>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<ul class="comments comments-answers js-comments-answers"' +
                'id="comment-' + review.id + '" style="display: none;">' +
                '</ul>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</li>').appendTo(reviewsList);
            let $addAnswer = $('.js-add-answer');
            if (cc_customer_data.id == null) {
                $addAnswer.hide();
            }
            if (review.answers == 0) {
                $('[data-comment-id=' + review.id + ']').find('.js-view-answers').hide();
            }

        });
    }

    function answerBody(results, answersList) {
        answersList.html('');
        results.map(function (answer) {
            $('<li class="comment-body comment-body-answer">' +
                '<div class="comment-answer-title">' +
                '<div class="comment-user-avatar">' +
                '<div class="user-avatar user-avatar-sm">' +
                '<div class="thumbnail">' +
                '<span class="ini">' + answer["author"]["avatar"] + '</span>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '<span class="comment-answer-username">' + answer["author"]["name"] + '</span>' +
                '<span class="comment-answer-date pull-right">' + answer["created"] + '</span>' +
                '</div>' +
                '<div class="comment-answer-value">' + answer["comment"] + '</div>' +
                '</li>').prependTo(answersList);
        });
    }

    function setRaiting() {
        $('.js-review-rating-ajax').map(function () {
            let $this = $(this);
            let value = $this.val();
            if (!$this.next('.simple-rating').length) {
                $this.rating({
                    value: value,
                    locked: true,
                });
            }
        })
    }

    function updateReviews(results, reviewsList, userAvatars) {
        reviewsList.html('');
        if (getActiveTab().tab == '#tab-review') {
            reviewBody(results, reviewsList);
            setRaiting();
        }
        if (getActiveTab().tab == '#tab-questions') {
            questionBody(results, reviewsList);
        }


        userAvatars = $(getActiveTab().tab + ' .review-comments>ul>li .user-avatar .thumbnail:not(.default)');
        setAvatarBgColors(userAvatars);
        CC.removeLoader($(getActiveTab().tab + ' .comments'));
    }

    //Generate random avatar's background-color
    function setAvatarBgColors(selector) {
        selector.each(function () {
            let randomColor = Math.floor(Math.random() * 16777215).toString(16);
            $(this).css('background-color', '#' + randomColor);
        });
    }

    function addMore(url, reviewsList, el) {
        CC.ajax({
            url: url,
            type: 'GET',
            success: function (res) {
                if (getActiveTab().tab == '#tab-review') {
                    reviewBody(res, reviewsList);
                }
                if (getActiveTab().tab == '#tab-questions') {
                    questionBody(res, reviewsList);
                }

                userAvatars = el.closest(getActiveTab().tab + ' .review-comments').find('.user-avatar .thumbnail:not(.default)');
                console.log(getActiveTab().tab);
                if (getActiveTab().tab == '#tab-review') {
                    setAvatarBgColors(userAvatars);
                    setRaiting();
                }
                if (res.pagination.next) {
                    if (getActiveTab().tab == '#tab-review') {
                        nextPageUrlReview = getNextPage(res.pagination.next);
                    } else {
                        nextPageUrlQuestion = getNextPage(res.pagination.next);
                    }
                    CC.removeLoader($(getActiveTab().tab + ' .comments'));
                } else {
                    el.slideUp();
                    CC.removeLoader($(getActiveTab().tab + ' .comments'));
                }
                el.trigger('loading.end');
            }
        })
    }

    $(document).off('click').on('click', '.js-add-answer', function (e) {
        e.preventDefault();
        $(this).closest('.comment-body').find('.js-add-answer-body').slideToggle();
    });

    // VIEW MORE REVIEWS
    $('.js-show-more-comments').on('click', function () {
        let $this = $(this);
        let reviewsList = $this.closest(getActiveTab().tab + ' .review-comments').find('>.comments');
        let userAvatars = '';
        let hasAcceptAnswers = false;
        if (getActiveTab().tab == '#tab-review') {
            {if Auth::customer() && Apps::setting('product_review', 'accept_answers')}
            hasAcceptAnswers = true;
            {/if}
            if (!hasAcceptAnswers) {
                {literal}
                $('head').append('<style>.comment-add-answer{ display: none; }</style>');
                {/literal}
            }
        } else {
            hasAcceptAnswers = true;
        }
        $this.trigger('loading.start');
        CC.addLoader($(getActiveTab().tab + ' .comments'));
        if (getActiveTab().tab == '#tab-review') {
            if (nextPageUrlReview === null) {
                addMore(getNextPage(), reviewsList, $this);
            } else {
                addMore(nextPageUrlReview, reviewsList, $this);
            }
        } else {
            if (nextPageUrlQuestion === null) {
                addMore(getNextPage(), reviewsList, $this);
            } else {
                addMore(nextPageUrlQuestion, reviewsList, $this);
            }
        }
    });

    // VIEW COMMENTS for reviews
    $(document).on('click', '.js-view-answers', function (e) {
        e.preventDefault();
        let $this = $(this);
        let commentId = $this.closest(getActiveTab('get_answers').tab + ' [data-comment-id]').attr('data-comment-id');
        let answersList = $this.closest(getActiveTab('get_answers').tab + ' .comment-body').find('.js-comments-answers');
        let $answers = $this.closest(getActiveTab('get_answers').tab + ' .comment-body').find('.js-comments-answers');
        let userAvatars = '';
        if (commentId !== '' && parseInt(commentId) !== NaN) {
            if (!$this.hasClass('active')) {
                CC.addLoader($answers);
                CC.ajax({
                    url: getActiveTab('get_answers').url.replace('COMMENTID', commentId),
                    type: 'GET',
                    success: function (res) {
                        answerBody(res, answersList);
                        userAvatars = $this.closest(getActiveTab('get_answers').tab + ' .comment-body').find(getActiveTab('get_answers').tab + ' .user-avatar .thumbnail:not(.default)');
                        setAvatarBgColors(userAvatars);
                        CC.removeLoader($answers);
                    }
                });
            }
        }
        $answers.slideToggle();
        $this.toggleClass('active');
    });


    // POST ANSWER
    $(document).on('click', '.js-submit-answer', function (e) {
        if (cc_customer_data.id != null) {
            let $this = $(this);
            let answersList = $this.closest(getActiveTab().tab + ' .comment-body').find('.js-comments-answers');
            let $answers = $this.closest(getActiveTab().tab + ' .comment-body').find('.js-comments-answers');
            let userAvatars = '';
            let data = {
                user_name: cc_customer_data.first_name + ' ' + cc_customer_data.last_name,
                user_email: cc_customer_data.email,
                comment: $this.closest(getActiveTab().tab + ' .js-add-comment-form').find('[name*="comment"]').val(),
            };
            let commentId = $this.closest(getActiveTab().tab + ' [data-comment-id]').attr('data-comment-id');
            let $textarea = $this.closest(getActiveTab().tab + ' .js-add-comment-form').find('textarea[name*="comment"]');
            $this.trigger('loading.start');

            function updateAnswers(callback) {
                CC.ajax({
                    url: getActiveTab('create_answer', commentId).url,
                    type: 'POST',
                    data: data,
                    dataType: 'json',
                    success: function (res) {
                        if (res.status == 'success') {
                            $this.closest(getActiveTab().tab + ' .js-add-answer-body').slideUp();
                            $textarea.val('').trigger('change');
                            toastr.success(sf.review_success_submit_answer);
                            callback();
                        } else {
                            if (!$textarea.next('.help-block-error').length) {
                                $('<span class="help-block-error">' + res["field"]["comment"] + '<span>').insertAfter($textarea);
                            } else {
                                $textarea.next('.help-block-error').show();
                            }
                            $textarea.trigger('focus');
                            $textarea.css('border-color', 'red');
                            toastr.success(sf.review_answer_error);
                        }
                        $this.trigger('loading.end');
                    }
                });
            }

            updateAnswers(function () {
                let commentsCount = parseInt($this.closest('.comment-body').find('.js-answers-count').text());
                let $commentBody = $this.closest('.comment-body');
                let approvedAnswer = {Apps::setting('product_review', 'approved_answers')};
                CC.addLoader($answers);
                if (commentId !== '' && parseInt(commentId) !== NaN) {
                    CC.ajax({
                        url: "{route('product_preview.comment.comment', ['comment_id' => 'COMMENTID'])}".replace('COMMENTID', commentId),
                        type: 'GET',
                        success: function (res) {
                            answerBody(res, answersList);
                            userAvatars = $this.closest('.comment-body').find('.user-avatar .thumbnail:not(.default)');
                            setAvatarBgColors(userAvatars);
                            $this.closest('.comment-body').find('.js-answers-count').text((commentsCount += 1).toString());
                            if (!($commentBody.find('.js-view-answers')).length) {
                                $('<a href="javascript:;" class="comment-view-answers js-view-answers" rel="noindex nofollow"><i class="far fa-comment-alt-lines"></i>&nbsp;' + sf.review_show_answers + ' (<span class ="js-answers-count">1</span>)</a>')
                                    .insertBefore($commentBody.find('.js-add-answer'));
                            } else {
                                $commentBody.find('.js-view-answers').show();
                            }
                            if (!approvedAnswer) {
                                $commentBody.find('.js-view-answers').text(sf.answer_need_approve);
                            }
                            CC.removeLoader($answers);
                        }
                    });
                }
            });
        }
    });

    // Clear Error massage
    $(getActiveTab().tab + ' textarea[name*="comment"]').on('keyup', function () {
        $(this).next('.help-block-error').hide();
        $(this).css('border-color', '#e5e5e5');
    });

    //Cancel answer
    $(document).on('click', getActiveTab().tab + ' .js-cancel-answer', function (e) {
        e.preventDefault();
        $(this).closest(getActiveTab().tab + ' .js-add-answer-body').slideUp();
    });

    // Sort reviews
    $(document).on('change', '.js-sort-reviews', function () {
        let $this = $(this);
        let productId = $('[data-product-id]').attr('data-product-id');
        let reviewsList = $(getActiveTab().tab + ' .review-comments>ul');
        let userAvatars = '';
        var url = getActiveTab('sort_by', productId).url;
        CC.addLoader($(getActiveTab().tab + ' .comments'));
        if ($this.val() == 'high-to-low') {
            CC.ajax({
                url: url + "?order=rating&sort=desc",
                type: 'GET',
                success: function (res) {
                    updateReviews(res, reviewsList, userAvatars);
                }
            })
        } else if ($this.val() == 'low-to-high') {
            CC.ajax({
                url: url  + "?order=rating&sort=asc",
                type: 'GET',
                success: function (res) {
                    updateReviews(res, reviewsList, userAvatars);
                }
            })
        } else if ($this.val() == 'date-asc') {
            CC.ajax({
                url: url + "?order=date&sort=desc",
                type: 'GET',
                success: function (res) {
                    updateReviews(res, reviewsList, userAvatars);
                }
            })
        } else if ($this.val() == 'date-desc') {
            CC.ajax({
                url: url + "?order=date&sort=asc",
                type: 'GET',
                success: function (res) {
                    updateReviews(res, reviewsList, userAvatars);
                }
            })
        }
    });

    // Filter by star
    $(document).on('change', getActiveTab().tab + ' .js-filter-by-star', function () {
        let $this = $(this);
        let productId = $('[data-product-id]').attr('data-product-id');
        let reviewsList = $(getActiveTab().tab + ' .review-comments>ul');
        let userAvatars = '';
        CC.addLoader($(getActiveTab().tab + ' .comments'));
        if ($this.val() == 'all') {
            CC.ajax({
                url: "/product_preview/reviews/" + productId,
                type: 'GET',
                success: function (res) {
                    updateReviews(res, reviewsList, userAvatars);
                }
            })
        } else {
            CC.ajax({
                url: "/product_preview/reviews/" + productId + "?filter=" + $this.val(),
                type: 'GET',
                success: function (res) {
                    updateReviews(res, reviewsList, userAvatars);
                }
            });
        }
    });

    // Filter by star on bar click
    $(document).on('click', getActiveTab().tab + ' .js-rating-bar', function (e) {
        e.preventDefault();
        let $this = $(this);
        let dataValue = $this.data('value');
        let $select = $this.closest('._product-details-review').find('select.js-filter-by-star.js-select2');
        $select.val(dataValue).trigger('change');
    });

    // Reload reviews
    $(document).on('cc.submit.review', function () {
        let productId = $('[data-product-id]').attr('data-product-id');
        let reviewsList = $(getActiveTab().tab + ' .review-comments>ul');
        let userAvatars = $('.review-comments>ul>li .user-avatar .thumbnail:not(.default)');
        CC.addLoader($(getActiveTab().tab + ' .comments'));
        CC.ajax({
            url: getActiveTab('get_list', productId).url,
            type: 'GET',
            success: function (res) {
                updateReviews(res, reviewsList, userAvatars);

                if (getActiveTab().tab == '#tab-review') {
                    let average = res.summary.average;
                    let total = res.summary.total;
                    let rating_text = res.summary.rating;
                    let reviews = '{t}sf.review.reviews{/t}';
                    let options = {
                        value: average,
                        locked: true,
                        msg: "<b>" + rating_text + "<b> ( <b>" + total + reviews + "</b> )"
                    };
                    let options_total = {
                        value: average,
                        locked: true,
                        msg: "<b><small class='reviews-rating-count'>" + total + reviews + " </small></b>"
                    };
                    $('.rating').map(function () {
                        let $this = $(this);
                        $this.next('.simple-rating').remove();
                        if (!$this.next('.simple-rating').length) {
                            $this.rating(options);
                        }
                    });
                    $('.js-rating-total').map(function () {
                        let $this = $(this);
                        $this.next('.simple-rating').remove();
                        if (!$this.next('.simple-rating').length) {
                            $this.rating(options_total);
                        }
                    });
                    $('.js-review-rating').text(rating_text);
                    $('.reviews-count').text('(' + total + reviews + ')');
                    $(' .js-progress').map(function () {
                        let $this = $(this);
                        let dataValue = $this.data('value');
                        let $progressBar = $this.find('.progress-bar');
                        let $totalsByRating = $this.next('.js-totals-by-rating');
                        switch (dataValue) {
                            case 1:
                                $progressBar.css('width', res.summary[dataValue] / total * 100 + '%');
                                $totalsByRating.text('(' +  res.summary[dataValue] + ')');
                                break;
                            case 2:
                                $progressBar.css('width', res.summary[dataValue] / total * 100 + '%');
                                $totalsByRating.text('(' + res.summary[dataValue] + ')');
                                break;
                            case 3:
                                $progressBar.css('width', res.summary[dataValue] / total * 100 + '%');
                                $totalsByRating.text('(' + res.summary[dataValue] + ')');
                                break;
                            case 4:
                                $progressBar.css('width', res.summary[dataValue] / total * 100 + '%');
                                $totalsByRating.text('(' + res.summary[dataValue] + ')');
                                break;
                            case 5:
                                $progressBar.css('width', res.summary[dataValue] / total * 100 + '%');
                                $totalsByRating.text('(' + res.summary[dataValue] + ')');
                                break;
                            default:
                                $progressBar.css('width', 0);
                                $totalsByRating.text('(0)');
                        }
                    });
                }
            }
        });
    });
</script>
<style>
    .tab-point {
        cursor: pointer;
        margin: 10px;
    }

    li.tab-point.active {
        border-bottom: 2px solid #fa5400;
        padding-bottom: 10px;
    }
</style>