<table class="table table-striped">
    <tbody>
    {foreach from=$models item=model}
        <tr>
            <td><a href="{Linker::category($product->category->url_handle, ['brand' => [$model[0]->brand->url_handle => '@all']])}" target="_blank">{$model[0]->brand->title}</a></td>
            <td>
                {foreach from=$model item=m}
                    <a href="{Linker::category($product->category->url_handle, ['brand' => [$m->brand->url_handle => $m->url_handle]])}" target="_blank">{$m->title}</a>{if !$smarty.foreach.products.last}, {/if}
                {/foreach}
            </td>
        </tr>
    {/foreach}
    </tbody>
</table>