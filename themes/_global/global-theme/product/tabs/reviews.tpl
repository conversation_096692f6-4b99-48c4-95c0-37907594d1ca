{if !Request::ajax()}
    <div class="_product-details-review">
        <div class="row _review-head">
            <div class="col-md-12">
                <ul class="nav nav-pills">
                    <li href="#tab-review" data-toggle="tab" class="tab-point active">
                        <h2 class="section-title text-left">{t}sf.review.reviews.title{/t}
                            <small class="reviews-count hidden-xs">({$comments['pagination']['total']} {t}sf.review.reviews{/t}
                                )</small>
                        </h2>
                    </li>
                    {if $is_question == 1 }
                        <li href="#tab-questions" data-toggle="tab" class="tab-point">
                            <h2 class="section-title text-left">{t}sf.review.questions.tab.title{/t}
                                <small class="reviews-count hidden-xs">({$questions['pagination']['total']} {t}sf.review.questions{/t}
                                    )</small>
                            </h2>
                            </a>
                        </li>
                    {/if}
                </ul>
                <hr class="border-separator">
            </div>
        </div>
        <div class="tab-content clearfix">
            {include file="./tab-reviews.tpl"}
            {if $is_question == 1 }
                {include file="./tab-questions.tpl"}
            {/if}
        </div>
    </div>
    {include file="./dependency.tpl"}
{/if}
