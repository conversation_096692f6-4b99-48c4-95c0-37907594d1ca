<div class="tab-pane active" id="tab-review">
    <div class="row _review-summary _pb-20">
        <div class="col-md-2">
            <div class="reviews-rating-wrap text-center">
                <p class="review-rating js-review-rating"></p>
                <input class="js-rating-total" type="hidden">
            </div>
        </div>
        <div class="col-md-10">
            <div class="reviews-summary-container">
                {$total_rating = array_sum($rating_progress)}

                {for $rating_steps=1 to 5}
                    {$rating_step = 6 - $rating_steps}
                    <div class="reviews-summary-bars rating-{$rating_step}-stars">
                        <a href="#" class="js-rating-bar" data-value="{$rating_step}">
                            {t}sf.review.{$rating_step}.stars{/t}
                        </a>
                        <a href="#" class="js-rating-bar js-progress" data-value="{$rating_step}">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar"
                                     style="width: {if $rating_progress[$rating_step] > 0}{$rating_progress[$rating_step]/$total_rating*100}{else}0{/if}%"></div>
                            </div>
                        </a>
                        <a href="#" class="js-rating-bar js-totals-by-rating"
                           data-value="{$rating_step}">({$rating_progress[$rating_step]})</a>
                    </div>
                {/for}

            </div>
        </div>
    </div>


    <div class="row _review-create _pb-20">
        <div class="col-md-12 text-center">
            <div>
                <a href="{route('product_review.create', $product_id)}" data-ajax-panel
                   class="_button _button-small js-disabled js-loading submit-js">
                    <span class="_figure-stack">
                        <span class="_figure-stack-label">{t}sf.review.add.rating{/t}</span>
                    </span>
                </a>
            </div>
        </div>
    </div>
    {if !Apps::setting('product_review', 'only_summary')}
        {if $comments['result']->count() > 0}
            <div class="row _review-sort">
                <div class="col-md-12">
                    <div class="comments-sort-options">
                        <div class="row">
                            <div class="col-md-6">
                                <label>{t}sf.review.sort_by{/t}</label>
                                <select name="type" class="_select js-select2 js-sort-reviews"
                                        data-allow-clear="true" data-no-input="true">
                                    <option value="high-to-low" class="js-high-star"
                                            {if Apps::setting('product_review', 'order_by') == "rating"}selected="selected"{/if}>{t}sf.review.sort.desc_rating{/t}</option>
                                    <option value="low-to-high"
                                            class="js-low-star">{t}sf.review.sort.asc_rating{/t}</option>
                                    <option value="date-asc" class="js-newest"
                                            {if Apps::setting('product_review', 'order_by') == "created_at"}selected="selected"{/if}>{t}sf.review.sort.desc_date{/t}</option>
                                    <option value="date-desc"
                                            class="js-newest">{t}sf.review.sort.asc_date{/t}</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label>{t}sf.review.filter_by{/t}</label>
                                <select class="_select js-select2 js-filter-by-star" data-allow-clear="true"
                                        data-no-input="true">
                                    <option value="all">{t}sf.review.all{/t}</option>
                                    {for $rating_steps=1 to 5}
                                        {$rating_step = 6 - $rating_steps}
                                        <option value="{$rating_step}">{t}sf.review.{$rating_step}.stars{/t}</option>
                                    {/for}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {/if}
        <div class="row _review-comments-wrap">
            <div class="col-md-12">
                <div class="review-comments">
                    <ul class="comments">
                        {foreach $comments['result'] as $comment}
                            <li class="comment-item clearfix" data-comment-id="{$comment['id']}">
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="comment-head">
                                            <div class="comment-user-avatar">
                                                <div class="user-avatar user-avatar-sm">
                                                    <div class="thumbnail">
                                                        <span class="ini">{$comment['author']['avatar']}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="comment-user-meta">
                                                <div class="comment-user-name">{$comment['author']['name']}</div>
                                                <div class="created-at">
                                                    {$comment["created"]}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-10">
                                        <div class="comment-body">
                                            <h3 class="comment-title">{$comment["title"] nofilter}</h3>
                                            <span class="comment-meta">
                                             {for $x = 1; $x <= 5; $x++}
                                                 {if $comment['rating'] >= $x}
                                                     <i class="fa fa-star manual"></i>






{else}






                                                     <i class="fa fa-star-o manual"></i>
                                                 {/if}
                                             {/for}
                                        </span>
                                            <small class="checked-order _tooltips">
                                                {*<span class="_tooltip"><span*}
                                                {*class="_tooltip-inner"><span>{t}sf.review.confirmed_order{/t}</span></span></span>*}
                                                <i class="fas fa-badge-check"></i>{t}sf.review.confirmed_order{/t}
                                            </small>
                                            <div class="comment-value">{$comment["comment"] nofilter}</div>
                                            <div class="comment-add-answers-wrap">
                                                {if $comment['answers'] > 0}
                                                    <a href="javascript:;"
                                                       class="comment-view-answers js-view-answers"
                                                       rel="noindex nofollow"><i
                                                            class="far fa-comment-alt-lines"></i>&nbsp;{t}sf.reviews.show_answers{/t}
                                                        (<span
                                                            class="js-answers-count">{$comment['answers']}</span>)</a>
                                                {/if}
                                                {if Auth::customer() && Apps::setting('product_review', 'accept_answers')}
                                                    <a href="javascript:;"
                                                       class="comment-add-answer js-add-answer"
                                                       rel="noindex nofollow"><i
                                                            class="far fa-comment-alt-plus"></i>&nbsp;{t}sf.reviews.answers{/t}
                                                    </a>
                                                {/if}
                                            </div>
                                            <div class="comments comments-add-answer js-add-answer-body"
                                                 style="display: none;">
                                                <div class="_mb-20">
                                                    <div class="comment-user-avatar">
                                                        <div class="user-avatar user-avatar-sm">
                                                            <div class="thumbnail default"
                                                                 style="background-color: #aaa;">
                                                                            <span class="ini"><i
                                                                                    class="far fa-user"></i></span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <span
                                                        class="comment-answer-username">{t}sf.review.add_new_answer{/t}</span>
                                                    <br/>
                                                </div>
                                                <div class="js-add-comment-form">
                                                    <div class="cc-form-group">
                                                            <textarea class="_textarea" name="comment" cols="30"
                                                                      rows="10"
                                                                      placeholder="{t}sf.review.add_comment{/t}"></textarea>
                                                    </div>
                                                    <br/>
                                                    <div class="text-right">
                                                        <a href="javascript:;"
                                                           class="_button _button-secondary js-disabled js-loading js-cancel-answer">
                                                        <span class="_figure-stack">
                                                            <span
                                                                class="_figure-stack-label">{t}sf.global.act.cancel{/t}</span>
                                                        </span>
                                                        </a>
                                                        <a href="javascript:;"
                                                           class="_button js-disabled js-loading submit-js js-submit-answer _md-mt-0 _md-ml-10 _mt-10">
                                                        <span class="_figure-stack">
                                                            <span
                                                                class="_figure-stack-label">{t}sf.review.publish{/t}</span>
                                                        </span>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            <ul class="comments comments-answers js-comments-answers"
                                                id="comment-{$comment["id"]}" style="display: none;">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        {/foreach}
                    </ul>
                    {if $comments['pagination']['next']}
                        <div class="pull-right">
                            <a href="javascript:;" class="_button js-show-more-comments">
                    <span class="_figure-stack">
                        <span class="_figure-stack-label">
                            {t}sf.show.more{/t}
                        </span>
                    </span>
                            </a>
                        </div>
                    {/if}
                </div>
            </div>
        </div>
    {/if}
</div>
