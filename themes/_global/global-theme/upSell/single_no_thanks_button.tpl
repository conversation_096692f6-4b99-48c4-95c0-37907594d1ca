<span class="js-cancel-button-{$record->record_type}-{$record->id}">
    <a href="javascript:void(0);" class="js-latest-level-click-no-action">{if $record->cancel_button_name_formatted|default:$record->cancel_button_name|trim}{$record->cancel_button_name_formatted|default:$record->cancel_button_name}{else}{t}sf.upSell.popup.text.no_thanks{/t}{/if}</a>
</span>

{capture append="js"}
    <script type="text/javascript">
        (function(){
            var $modal = $('.modal-dialog-{if $record->record_type == 'cross_sell'}crosssell{else}upsell{/if}-{$record->id}').closest('.modal');
            $modal.on('show.bs.modal', function(){
                {include file="../dependancies/confetti.tpl" effect=$record->popup_effect}
                CC.init($(this));
            }).on('shown.bs.modal', function(){
                $modal.addClass('opened');
            });

            $modal.on('click', '.js-cancel-button-{$record->record_type}-{$record->id} a', function() {
                Ajax.post({
                    url: '{route("site.{$record->record_type}.discard", $record->id)}'
                });
            });

            $modal.on('click', '.js-latest-level-click-no-action', function() {
                $('.modal').modal('hide');
                return false;
            });

            $(document).off('.up_sell_cancel').on('keydown.up_sell_cancel', function(e) {
                var code = e.keyCode || e.which;
                if(code == 27) {
                    $modal.find('.js-process-to-no-action, .js-latest-level-click-no-action').trigger('click');
                }
            });

            $(document).one('cc.addToCart.product.added', function(e) {
                $('.modal').modal('hide');
            });
        }());
    </script>
{/capture}