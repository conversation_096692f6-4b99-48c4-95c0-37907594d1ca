<script type="text/javascript">
    var countdownDiscountExists = {if $discount->valid_countdown|default}true{else}false{/if};

    $(document).on('click', '#countdown-discount-popup-submit-button', function(){
        var holder = $('.countdown-discount-total');
        if(holder.length) {
            $countdownModal.animateTo(holder, {
                complete: function () {
                    $countdownModal.modal('hide');
                    if (typeof crossSellProductsCount !== 'undefined') {
                        $(document).trigger('show.cross-sell');
                    }
                }
            });
        } else {
            $countdownModal.modal('hide');
            if (typeof crossSellProductsCount !== 'undefined') {
                $(document).trigger('show.cross-sell');
            }
        }
    });

    CC.ajaxModal({
        target: "{route('checkout.countdown_discount_popup')}",
        hide_close: true,
        onTop: true,
        cls: 'modal-dialog-countdown-discount'
    });
</script>