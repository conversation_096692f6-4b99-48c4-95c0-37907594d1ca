<div class="cc-crosssell cc-crosssell-count-{$crossSell->products->count()} js-cross-sell-box">
    <div class="cc-crosssell-header">
        <h2 class="cc-crosssell-text-color">{$crossSell->offer_title}</h2>
        <p>{$crossSell->description nofilter}</p>

        {if $single_popup}
            {include file="upSell/single_no_thanks.tpl" record=$crossSell}
        {else}
            {include file="upSell/no_thanks.tpl" record=$crossSell}
        {/if}
    </div>

    {if $crossSell->products->count()}
        {W::ProductsDetails()->setSetting('choose_quantity', false)}
        <div class="cc-crosssell-items cc-crosssell-items-{$crossSell->products->count()}">
            {foreach $crossSell->products AS $product}
                <div class="cc-crosssell-item product-details-js" data-product-id="{$product->id}">
                    <div class="cc-crosssell-section">
                        <div class="cc-crosssell-item-image">
                            <div class="cc-crosssell-item-image-thumb" style="background-image: url({$product->getImage('600x600')});">
                            </div>
                        </div>

                        <div class="cc-crosssell-item-info">
                            <div class="cc-crosssell-item-title">
                                <h6 class="cc-crosssell-text-color">{$product->name}</h6>
                            </div>

                            <div class="cc-crosssell-item-price product-details-price-js{if $product->price_from_discounted} has-discount{/if}">
                                {if $product->diff_prices}
                                    {t}sf.crossSell.price_from{/t}
                                {/if}

                                {if $product->discount}
                                    <span class="cc-crosssell-item-price-new price-new-js">{$product->price_from_discounted_formatted nofilter}</span>
                                    <span class="cc-crosssell-item-price-old price-old-js"><i class="rtl-ltr">{$product->price_from_formatted nofilter}</i></span>
                                {else}
                                    <span class="cc-crosssell-item-price-new price-new-js">{$product->price_from_formatted nofilter}</span>
                                {/if}
                            </div>
                        </div>
                    </div>

                    <div class="cc-crosssell-section">
                        <div class="cc-crosssell-item-actions">
                            {if $product->total_variants}
                                <div class="cc-crosssell-item-select-options">
                                    <a class="cc-button cc-button-small cc-button-full js-show-options" onclick="$('.js-crosssell-product-options-{$product->id}').toggle();" href="javascript:void(0);" style="{if $crossSell->button_background}background-color: {$crossSell->button_background}; border-color: {$crossSell->button_background};{/if} {if $crossSell->button_text_color}color: {$crossSell->button_text_color};{/if}">{t}sf.crossSell.select.option{/t}</a>
                                </div>
                            {else}
                                <div class="cc-crosssell-item-options">
                                    <form class="add-to-cart-form-js js-no-scroll js-form-submit-ajax" action="{route('cart.add')}" method="POST">
                                        <input type="hidden" name="cross_sell" value="{$crossSell->id}" readonly="readonly">
                                        <input type="hidden" name="cart_items" value="{$item_key}" readonly="readonly">
                                        {include file="widgets/product/details/choose_variant.tpl" quantity_uicontrol="spinner" select_uicontrol="select2" printLabels=true tooltips=true}
                                    </form>
                                </div>
                            {/if}
                        </div>

                        {if $product->total_variants}
                            <div class="cc-crosssell-item-options js-crosssell-product-options-{$product->id}" style="display: none;">
                                <form class="add-to-cart-form-js js-no-scroll js-form-submit-ajax" action="{route('cart.add')}" method="POST">
                                    <input type="hidden" name="cross_sell" value="{$crossSell->id}" readonly="readonly">
                                    <input type="hidden" name="cart_items" value="{$item_key}" readonly="readonly">
                                    {include file="widgets/product/details/choose_variant.tpl" quantity_uicontrol="spinner" select_uicontrol="select2" printLabels=true tooltips=true}
                                </form>
                            </div>
                        {/if}
                    </div>
                </div>

            {if !empty($crossSell->button_name)}
            {capture append="js"}
                <script type="text/javascript">
                    $('.js-cross-sell-box .product-details-js[data-product-id="{$product->id}"] form.add-to-cart-form-js')
                            .on('cc.product.details.init', function(e, $form) {
                                $form.pdc.setFormSubmitButtonText('{$crossSell->button_name}');
                            });
                </script>
            {/capture}
            {/if}
            {capture append="js"}
                <script type="text/javascript">
                    if(window.location.pathname == '/checkout') {
                        $(document).off('click', '.js-go-to-checkout').on('click', '.js-go-to-checkout', function() {
                            $('.modal').modal('hide');
                            $('.js-checkout-summary').trigger('cc.ajax.reload');
                            return false;
                        });
                    }
                </script>
            {/capture}

            {if $order}
            {capture append="js"}
                <script type="text/javascript">
                    $('.js-cross-sell-box .product-details-js[data-product-id="{$product->id}"] form.add-to-cart-form-js')
                            .append('<input type="hidden" value="{$order->id}" name="order_id">')
                            .append('<input type="hidden" value="{$crossSell->event}" name="cross_sell_event">');
                </script>
            {/capture}
            {/if}

            {/foreach}
        </div>
    {/if}

    <div class="cc-crosssell-footer">
        {if $crossSell->count_down}
            {include file="widgets/common/countdown.tpl" endDate=$crossSell->count_down}
        {/if}

        {if $single_popup}
            {include file="upSell/single_no_thanks_button.tpl" record=$crossSell}
        {else}
            {include file="upSell/no_thanks_button.tpl" record=$crossSell}
        {/if}
    </div>
</div>

<style type="text/css">
    .modal-dialog-crosssell-{$crossSell->id} a {
        {if $crossSell->text_color}color: {$crossSell->text_color};{/if}
    }
    .modal-dialog-crosssell-{$crossSell->id} .cc-crosssell-text-color {
        {if $crossSell->text_color}color: {$crossSell->text_color};{/if}
    }
    .modal-dialog-crosssell-{$crossSell->id} .cc-crosssell {
        {if $crossSell->background}background-color: {$crossSell->background};{/if}
        {if $crossSell->text_color}color: {$crossSell->text_color};{/if}
    }
    .modal-dialog-crosssell-{$crossSell->id} .cc-crosssell-item-options ._button,
    .modal-dialog-crosssell-{$crossSell->id} .cc-crosssell-item-options ._button:hover {
        {if $crossSell->button_background}background-color: {$crossSell->button_background}; border-color: {$crossSell->button_background};{/if}
        {if $crossSell->button_text_color}color: {$crossSell->button_text_color};{/if}"
    }
    .cc-crosssell .cc-button.disabled,
    .cc-crosssell .cc-button.disabled:hover {
        background-color: #dcdcdc !important;
        border-color: #dcdcdc !important;
        color: #fff !important;
    }

    .modal-dialog-crosssell-{$crossSell->id} .cc-crosssell-item-options ._button.loading {
        {if $crossSell->button_background}color: {$crossSell->button_background} !important;{/if}"
    }

    .modal-dialog-crosssell-{$crossSell->id} .cc-crosssell-item-options ._button.loading .loader-dots span {
        {if $crossSell->button_text_color}background-color: {$crossSell->button_text_color};{/if}"
    }

    .modal-dialog-crosssell-{$crossSell->id} .cc-crosssell-item-price-new {
        {if $crossSell->text_color}color: {$crossSell->text_color};{/if}"
    }

    .modal-dialog-crosssell-{$crossSell->id} ._countdown-container {
        float: left;
    }

    .modal-dialog-crosssell-{$crossSell->id} ._countdown-container ._countdown-label,
    .modal-dialog-crosssell-{$crossSell->id} ._countdown-container ._countdown {
        display: inline-block;
    }

    @media (max-width: 767px) {
        .modal {
            height: 100%;
        }

        .modal-dialog-crosssell {
            {if $crossSell->background}background-color: {$crossSell->background};{/if}
        }
    }
</style>

{capture append="js"}
    <script type="text/javascript">
        (function(){
            var $modal = $('.modal-dialog-crosssell-{$crossSell->id}').closest('.modal');

            $(document).on('click', '.js-show-options', function(){
                $(this).toggleClass('disabled');
            });

            {*{if !$crossSell->childes->filter()->count()}*}
            $modal.find('.add-to-cart-form-js').addClass('no-submit-dismiss');
            $modal.on('cc.ajax.success', '.add-to-cart-form-js', function(e, json) {
                $(e.target || e.currentTarget).parent()
                        .parent().html(json.message).addClass('cc-crosssell-item-actions');

                {if !$crossSell->child_yes}
                if(window.location.pathname === '/checkout' && json.status === 'success') {
                    setTimeout(function() {
                        $modal.modal('hide');
                    }, 2000);
                }
                {/if}
            });
            {*{/if}*}

            $('body').addClass('modal-open');
        }());
    </script>
{/capture}

