{if $blog->hasImage()}
    {$image = $blog->getImage('600x600')}
{else}
    {$image = setting('og_image_url', logo())|default:logo()}
{/if}
{*{$sizes = getimagesize($image)}*}

<!-- Schema.org markup for Google+ -->
<meta itemprop="name" content="{$blog->name}" />
<meta itemprop="description" content="{$blog->seo_description}" />
<meta itemprop="image" content="{$image}" />

<!-- Open Graph data -->
<meta property="og:title" content="{$blog->name}" />
<meta property="og:type" content="blog" />
<meta property="og:url" content="{Linker::fullLink($blog->url()) nofilter}" />
<meta property="og:image" content="{$image}" />
{if isset($sizes.0)}
<meta property="og:image:width" content="{$sizes.0}" />
{/if}
{if isset($sizes.1)}
<meta property="og:image:height" content="{$sizes.1}" />
{/if}
<meta property="og:description" content="{$blog->seo_description}" />
<meta property="og:site_name" content="{setting('site_name')}" />

<!-- Twitter Card data -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:site" content="{setting('site_name')}" />
<meta name="twitter:creator" content="https://cloudcart.com" />
<meta name="twitter:title" content="{$blog->name}" />
<meta name="twitter:description" content="{$blog->seo_description}" />
<meta name="twitter:image:src" content="{$image}" />
{*<meta name="twitter:image:width" content="600">*}
{*<meta name="twitter:image:height" content="600">*}