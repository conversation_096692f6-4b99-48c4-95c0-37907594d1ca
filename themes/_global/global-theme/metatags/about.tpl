{$cantact_page_text = $widget->getSetting('page_text')}
{$custom_contact_info = $widget->getSetting('custom_information')}
{$seo = $widget->getSeo()}
{$name = setting('company_name', setting('site_name'))}
{if !empty($cantact_page_text)}
    {$description = $cantact_page_text|strip_tags:true|unescape:"html"}
{elseif !empty($custom_contact_info)}
    {$description = $custom_contact_info|strip_tags:true|unescape:"html"}
{else}
    {$description = $seo.description}
{/if}
{$image = setting('og_image_url', logo())|default:logo()}
<!-- Schema.org markup for Google+ -->
<meta itemprop="name" content="{$name}" />
<meta itemprop="description" content="{$description}" />
{if !empty($image)}
    <meta itemprop="image" content="{$image}" />
{/if}

<!-- Open Graph data -->
<meta property="og:title" content="{$name}" />
<meta property="og:type" content="website" />
<meta property="og:url" content="{route('contacts')}" />
{if !empty($image)}
    <meta property="og:image" content="{$image}" />
    {if isset($sizes.0)}
    <meta property="og:image:width" content="{$sizes.0}" />
    {/if}
    {if isset($sizes.1)}
    <meta property="og:image:height" content="{$sizes.1}" />
    {/if}
{/if}
<meta property="og:description" content="{$description}" />
<meta property="og:site_name" content="{setting('site_name')}" />

<!-- Twitter Card data -->
<meta name="twitter:card" content="summary" />
<meta name="twitter:site" content="{setting('site_name')}" />
<meta name="twitter:creator" content="https://cloudcart.com" />
<meta name="twitter:title" content="{$name}" />
<meta name="twitter:description" content="{$description}" />
{if !empty($image)}
    <meta name="twitter:image:src" content="{$image}" />
{/if}
{*<meta name="twitter:image:width" content="600">*}
{*<meta name="twitter:image:height" content="600">*}