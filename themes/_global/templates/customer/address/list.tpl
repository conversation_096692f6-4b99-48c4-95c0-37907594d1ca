<div class="js-address-{$type}-holder" data-ajax-box="{route("site.account.address.{$type}.list", ['page'=>get('page')])}">
    <div class="_section-title">
        {if $type == 'shipping'}
            <h3>{t}sf.account.act.addresses.shipping{/t}</h3>
        {else}
            <h3>{t}sf.account.act.addresses.billing{/t}</h3>
        {/if}
    </div>

    <div class="cc-address-book">
        {if $addresses->count()}
            {foreach $addresses as $address}
                <div class="cc-address-book-item{if $address->is_default} active{/if}">
                    <a class="cc-address-book-item-data" href="{if $address->is_default}javascript:;{else}{route("site.account.address.{$type}.default", $address->id)}{/if}" {if !$address->is_default} data-ajax="toast"{/if} data-confirm="{t}sf.account.details.confirm.set_default_address{/t}" data-placement="top" data-title="{t}sf.account.details.tip.set_as_default{/t}" title="{t}sf.account.details.tip.set_as_default{/t}">
                        <span class="cc-address-book-item-data-name">{$address->full_name}</span>
                        <span class="cc-address-book-item-data-item">{t}sf.address.country{/t}: <strong>{$address->country_name}</strong></span>
                        <span class="cc-address-book-item-data-item">{t}sf.address.state{/t}: <strong>{$address->state_name}</strong></span>
                        <span class="cc-address-book-item-data-item">{t}sf.address.city{/t}: <strong>{$address->city_name}</strong></span>
                        <span class="cc-address-book-item-data-item">{t}sf.address.street{/t}: <strong>{$address->street_name} #{$address->street_number}</strong></span>
                        {if $address->address1}
                            <span class="cc-address-book-item-data-item">{t}sf.address.address{/t}: <strong>{$address->address1}</strong></span>
                        {/if}
                        <span class="cc-address-book-item-data-item">{t}sf.address.postal_code{/t}: <strong>{$address->post_code}</strong></span>
                        <span class="cc-address-book-item-data-item">{t}sf.address.phone{/t}: <strong>{$address->phone_national}</strong></span>
                        {if $address->company}
                            <span class="cc-address-book-item-data-item">{t}sf.global.label.company_name{/t}: <strong>{$address->company_name}</strong></span>
                            {if $address->company_vat}
                                <span class="cc-address-book-item-data-item">{t}sf.global.label.company_vat{/t}: <strong>{$address->company_vat}</strong></span>
                            {/if}
                        {/if}
                    </a>

                    <div class="cc-address-book-item-actions">
                        <a class="cc-address-book-item-action cc-address-book-item-action-edit address_action js-action" href="{route("site.account.address.{$type}.edit", $address->id)}" data-ajax-panel="true" data-panel-class="js-checkout-{$type}-address-holder" title="{t}sf.account.details.tip.edit{/t}">
                            <i class="fa fa-pencil"></i>
                        </a>

                        <a class="cc-address-book-item-action cc-address-book-item-action-remove address_action js-action" href="{route("site.account.address.{$type}.remove", $address->id)}" data-ajax="toast" data-confirm="{t}sf.account.details.confirm.remove_address{/t}" title="{t}sf.account.details.confirm.remove_address{/t}">
                            <i class="fa fa-times"></i>
                        </a>
                    </div>
                </div>
            {/foreach}
        {*
        {else}
            <div class="_notification">
                <p>{t}sf.account.details.warn.no_addresses_entered{/t}</p>
            </div>
        *}
        {/if}
        <div class="cc-address-book-item js-address-book-controls">
            <a class="cc-address-book-item-add js-address-book-form-button" href="{route("site.account.address.{$type}.create")}" data-ajax-panel="true" data-panel-class="js-checkout-{$type}-address-holder" title="{t}sf.account.details.label.add_new_address{/t}">
                <span class="cc-address-book-item-add-text">{t}sf.account.details.label.add_new_address{/t}</span>
                <i class="cc-address-book-item-add-icon fa fa-plus-circle"></i>
            </a>
        </div>
    </div>
</div>

{script}
    <script type="text/javascript">
        jQuery(document).off('cc.user.address.setDefault').on('cc.user.address.setDefault', function() {
            jQuery('.js-address-{$type}-holder:first').trigger('cc.ajax.reload');
        });
        jQuery(document).off('cc.user.address.removed').on('cc.user.address.removed', function() {
            jQuery('.js-address-{$type}-holder:first').trigger('cc.ajax.reload');
        });
        jQuery(document).off('submit.user.address.edited', '.js-checkout-{$type}-address-holder form').on('submit.user.address.edited', '.js-checkout-{$type}-address-holder form', function() {
            var $form = jQuery(this);
            $form.on('cc.ajax.success', function() {
                jQuery('.js-address-{$type}-holder:first').trigger('cc.ajax.reload');
            });
        });
    </script>
{/script}