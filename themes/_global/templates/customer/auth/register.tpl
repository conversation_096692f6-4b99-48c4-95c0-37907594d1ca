<form class="cc-form cc-form-register js-form-submit-ajax-new" data-submit-loader="true" role="form" method="POST" action="{$action|default:route('site.auth.register')}">
    {if $hash|default}
        <input type="hidden" name="hash" value="{$hash}">
    {/if}

    <div class="cc-form-section">
        <div class="cc-form-row">
            <div class="cc-form-col-6">
                {include file=base_path('themes/_global/templates/form-components/input.tpl') value=$social_name|default type="text" name="first_name" id="register-fist_name" label="{t}sf.global.ph.first_name{/t}" attributes=["data-password" => "#register-password"] required=true}
            </div>

            <div class="cc-form-col-6">
                {include file=base_path('themes/_global/templates/form-components/input.tpl') value=$social_name|default type="text" name="last_name" id="register-last_name" label="{t}sf.global.ph.last_name{/t}" attributes=["data-password" => "#register-password"] required=true}
            </div>
        </div>
        <div class="cc-form-row">

            <div class="cc-form-col-6">
                {if $hash|default}
                    {include file=base_path('themes/_global/templates/form-components/input.tpl') value=$social_email|default type="email" name="email" id="register-email" label="{t}sf.global.ph.email{/t}" attributes=["data-password" => "#register-password"] required=true readonly=true }
                {else}
                    {include file=base_path('themes/_global/templates/form-components/input.tpl') value=$social_email|default type="email" name="email" id="register-email" label="{t}sf.global.ph.email{/t}" attributes=["data-password" => "#register-password"] required=true }
                {/if}
            </div>

            <div class="cc-form-col-6">
                {include file=base_path('themes/_global/templates/form-components/input.tpl') type="password" name="password" id="register-password" label=__('sf.global.ph.password') required=true}
            </div>
        </div>
        <div class="cc-form-row">
            <div class="cc-form-col-6">
                {include file=base_path('themes/_global/templates/form-components/input.tpl')
                    type="tel"
                    name="alternative_phone"
                    id="checkout-shipping-marketplace-phone"
                    class="cc-form-group-tel"
                    label="{t}sf.global.ph.phone{/t}"
                    value=$address->alternative_phone|default
                    required=true
                    input_class='js-phone-intl'
                    input_attributes=[
                        'data-error-label' => __('sf.address.err.invalid_phone_number'),
                        'data-ip' => Request::ip()
                    ]
                }
            </div>
        </div>

        {if $fields|default}
            {$chunked=$fields->chunk(2)}
            {foreach $chunked AS $rows}
                <div class="cc-form-row">
                    {foreach $rows AS $field}
                    <div class="cc-form-col-6">
                        {include file=base_path("themes/_global/templates/customer/custom-form-component/{$field->type}.tpl") field=$field}
                    </div>
                    {/foreach}
                </div>
            {/foreach}
        {/if}

        {if setting('require_registration_shipping_address')}
        <div class="_content js-checkout-shipping-address-holder">
            <div class="_section-title">
                <h2 data-panel-title="main">{t}sf.widget.checkout.header.shipping_address{/t}</h2>
            </div>
            {include file=base_path("themes/_global/templates/customer/address/_form.tpl") input_prefix='shipping' has_guest_email=false type="shipping" container_selector=".js-checkout-shipping-address-holder" input_selector=".js-checkout-shipping-address-holder [data-google-place]" force_hide_map=false}
        </div>
        {/if}

        {if setting('require_registration_billing_address')}
        <div class="_content js-checkout-billing-address-holder">
            <div class="_section-title">
                <h2 data-panel-title="main">{t}sf.seo.account.addresses.billing.title{/t}</h2>
            </div>
            {include file=base_path("themes/_global/templates/customer/address/_form.tpl") input_prefix='billing' has_guest_email=false type="billing" container_selector=".js-checkout-billing-address-holder" input_selector=".js-checkout-billing-address-holder [data-google-place]" force_hide_map=true}
        </div>
        {/if}

        {include file=Illuminate\Support\Facades\View::make('global::checkout.include.gdpr')->getPath() gdpr_form='register' chunk_form=true}
    </div>

    <div class="cc-form-actions">
        <div class="cc-form-row">
            <div class="cc-form-col-12 cc-form-col-align-right">
                <button class="cc-button js-loading" type="submit" disabled>{t}sf.widget.user.register.act.register{/t}</button>
            </div>
        </div>
    </div>

    {*<div class="cc-form-section cc-form-section-separator">*}
        {*<div class="cc-form-row">*}
            {*<div class="cc-form-col-6">*}
                {*<a href="#" class="cc-button cc-button-google cc-button-full cc-button-has-icon"><span class="cc-button-icon fa fa-google"></span>{t}sf.global.act.register_with_google{/t}</a>*}
            {*</div>*}

            {*<div class="cc-form-col-6">*}
                {*<a href="#" class="cc-button cc-button-facebook cc-button-full cc-button-has-icon"><span class="cc-button-icon fa fa-facebook"></span>{t}sf.global.act.register_with_facebook{/t}</a>*}
            {*</div>*}
        {*</div>*}
    {*</div>*}

    <div class="cc-form-section cc-form-section-separator">
        <div class="cc-checkout-meta-links">
            <a class="cc-checkout-meta-link" href="{route('site.auth.login')}"{if Request::ajax()} data-dismiss="panel"{/if}>{t}sf.widget.user.act.back_to_login{/t}</a>
        </div>
    </div>
</form>
