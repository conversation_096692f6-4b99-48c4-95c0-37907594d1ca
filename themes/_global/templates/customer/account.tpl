{include file=View::path('layout.header')}

<main class="_content">
    <div class="_breadcrumb-wrapper">
        <div class="container">
            <div class="_section-separator">
                <div class="row">
                    <div class="col-md-12">
                        {include file="widgets/common/breadcrumbs.tpl" breadcrumbs=$breadcrumbs.breadcrumbs active=$breadcrumbs.active}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container _section-separator">
        <div class="row">
            {if $customerIsSelf|default:true}
            <div class="col-md-4 col-lg-3 col-md-push-8 col-lg-push-9">
                <div class="cc-account-sidebar">
                    <div class="_section-title">
                        <h3>{t}sf.account.header.my_account{/t}</h3>
                    </div>

                    <div class="cc-account-navigation">
                        <ul>
                            <li{if activeRoute('site.account')} class="active"{/if}>
                                <a href="{route('site.account')}">{t}sf.account.act.my_details{/t}</a>
                            </li>
                            <li{if activeRoute('site.account.password')} class="active"{/if}>
                                <a href="{route('site.account.password')}">{t}sf.account.act.password{/t}</a>
                            </li>
                            <li{if activeRoute('site.account.address.shipping.list')} class="active"{/if}>
                                <a href="{route('site.account.address.shipping.list')}">{t}sf.account.act.addresses.shipping{/t}</a>
                            </li>
                            {if !setting('checkout_hide_billing_address')}
                                <li{if activeRoute('site.account.address.billing.list')} class="active"{/if}>
                                    <a href="{route('site.account.address.billing.list')}">{t}sf.account.act.addresses.billing{/t}</a>
                                </li>
                            {/if}
                            <li{if activeRoute('site.account.orders') || activeRoute('site.account.order')} class="active"{/if}>
                                <a href="{route('site.account.orders')}">{t}sf.account.act.my_orders{/t}</a>
                            </li>
                            <li{if activeRoute('site.account.payments')} class="active"{/if}>
                                <a href="{route('site.account.payments')}">{t}sf.account.act.my_payments{/t}</a>
                            </li>
                            <li{if activeRoute('site.account.files')} class="active"{/if}>
                                <a href="{route('site.account.files')}">{t}sf.account.act.my_files{/t}</a>
                            </li>
                            {if Apps::installed('membership')}
                                <li{if activeRoute('site.account.pages')} class="active"{/if}>
                                    <a href="{route('site.account.pages')}">{t}sf.account.header.pages{/t}</a>
                                </li>
                            {/if}
                            {if Widget::has('wishlist')}
                                <li{if activeRoute('site.account.wishlist')} class="active"{/if}>
                                    <a href="{route('site.account.wishlist')}">{t}sf.global.favorite.products{/t}</a>
                                </li>
                            {/if}
                            {if GDPR::isActive()}
                                <li{if activeRoute('site.account.gdpr')} class="active"{/if}>
                                    <a href="{route('site.account.gdpr')}">{t}sf.account.act.gdpr{/t}</a>
                                </li>
                            {/if}
                            {if $links|default}
                                {foreach $links AS $link}
                                    <li>
                                        <a href="{$link->value}" target="_blank">{$link->field->storefront_name}</a>
                                    </li>
                                {/foreach}
                            {/if}
                            <li>
                                <a href="{route('site.auth.logout')}">{t}sf.global.act.logout{/t}</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            {/if}

            <div class="{if $customerIsSelf|default:true}col-md-8 col-lg-9 col-md-pull-4 col-lg-pull-3{else}col-lg-12{/if}">
                <div class="cc-account-main">
                    {include file=$load_content}
                </div>
            </div>
        </div>
    </div>
    <!-- LOAD: seo -->
</main>

{include file=View::path('layout.footer')}