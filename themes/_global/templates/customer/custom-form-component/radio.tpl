<div class="cc-form-group{if $field->required|default} required{/if}">
    <div class="cc-form-field-container skip-has-error">
        <label class="cc-checkbox-multiple" for="">
            <span class="cc-checkbox-text">{$field->storefront_name nofilter}</span>
        </label>
    </div>

    <div class="cc-form-description">
        {foreach $field->options AS $option}
            <div class="cc-form-row">
                <div class="cc-form-col-12">
                    {include file=base_path("themes/_global/templates/form-components/radio.tpl") name=inputPrefix("custom[{$field->id}]", $custom_prefix|default) class=inputJsError("custom[{$field->id}]") id="custom-field-{uniqid()}-{$field->id}-{$option->id}" label=$option->name required=$field->required value=$option->id checked=($value|default == $option->id)}
                </div>
            </div>
        {/foreach}
    </div>
</div>
