<div class="_section-title">
    <h2>{t}sf.account.orders.header.my_gdpr{/t}</h2>
</div>

{if $logs->count()}
    <div class="cc-account-table cc-account-table-orders">
        <div class="table-responsive">
            <table>
                <thead>
                    <tr>
                        <th>{t}sf.account.gdpr.th.name{/t}</th>
                        <th>{t}sf.account.gdpr.th.date.created{/t}</th>
                        <th>{t}sf.account.gdpr.th.date.updated{/t}</th>
                        <th class="text-aligncenter">{t}sf.global.details{/t}</th>
                    </tr>
                </thead>

                <tbody>
                    {foreach $logs as $log}
                        <tr>
                            <td data-label="{t}sf.account.gdpr.th.name{/t}"><span class="rtl-ltr">{$log->content->title}</span></td>
                            <td data-label="{t}sf.account.gdpr.th.date.created{/t}"><span class="rtl-ltr">{$log->created_at_formatted nofilter}</span></td>
                            <td data-label="{t}sf.account.gdpr.th.date.updated{/t}"><span class="rtl-ltr">{$log->updated_at_formatted nofilter}</span></td>
                            <td data-label="{t}sf.global.details{/t}" class="text-aligncenter">
                                <a class="cc-account-table-toggle js-load-order" href="{route('site.account.gdpr.view', $log->id)}">
                                    <i class="fa fa-chevron-down"></i>
                                </a>

                                <a class="cc-account-table-toggle cc-account-table-remove js-load-order-remove" href="javascript:void(0)" style="display: none;">
                                    <i class="fa fa-chevron-up"></i>
                                </a>
                            </td>
                        </tr>

                        <tr class="cc-account-table-empty-row js-load-order-content" style="display: none;">
                            <td colspan="4"></td>
                        </tr>
                    {/foreach}
                </tbody>
            </table>
        </div>

        {include file="widgets/common/pagging.tpl" page=$logs->currentPage() pages=$logs->lastPage() link="{route('site.account.gdpr')}?"}
    </div>
{else}
    <div class="_notification">
        <p>{t}sf.account.orders.warn.no_gdpr_log_made{/t}</p>
    </div>
{/if}