<div class="_section-title">
	<h2>{t}sf.account.files.header.my_files{/t}</h2>
</div>

{if $files->count()}
	<div class="cc-account-table cc-account-table-files">
		<div class="table-responsive">
			<table>
				<tr>
					<th>{t}sf.account.files.th.product_name{/t}</th>
					<th>{t}sf.account.files.th.file_name{/t}</th>
					<th>{t}sf.account.files.th.vendor{/t}</th>
					<th>{t}sf.account.files.th.order_date{/t}</th>
					<th>{t}sf.account.files.th.order#{/t}</th>
					<th class="text-aligncenter">{t}sf.account.files.th.download_link{/t}</th>
				</tr>

				{foreach $files as $digital_product_file}
					<tr>
						<td>{$digital_product_file->order_product_name}</td>
						<td>{$digital_product_file->name}</td>
						<td>{$digital_product_file->order_product_vendor_name}</td>
						<td>{$digital_product_file->date_added_formatted nofilter}</td>
						<td>{$digital_product_file->order_id}</td>
						<td class="text-aligncenter">
							<a href="{route('site.account.file.download', $digital_product_file->id)}" target="_blank">
								<i class="fa fa-download"></i>
							</a>
						</td>
					</tr>
				{/foreach}
			</table>
		</div>

		{include file="widgets/common/pagging.tpl" page=$files->currentPage() pages=$files->lastPage() link="{route('site.account.files')}?"}
	</div>
{else}
	<div class="_notification">
		<p>{t}sf.account.files.warn.no_digital_files{/t}</p>
	</div>
{/if}