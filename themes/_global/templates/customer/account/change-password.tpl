<div class="_section-title">
    <h2>{t}sf.account.act.password{/t}</h2>
</div>

<form class="cc-form cc-form-change-password js-form-submit-ajax" method="post" role="form" action="{route('site.account.password')}">
    <div class="cc-form-section">
        <div class="cc-form-row">
            {$hasPassword = !empty($customer->password)}
            {if $hasPassword}
                <div class="cc-form-col-4">
                    <input type="password" class="_field" name="password_old" id="changePassOld" placeholder="{t}sf.widget.user.account.ph.old_password{/t}*" required>
                </div>
            {/if}

            <div class="cc-form-col-{if $hasPassword}4{else}6{/if}">
                <input type="password" class="_field" name="password" id="changePassPassword" placeholder="{t}sf.global.ph.password{/t}*" required>
            </div>

            <div class="cc-form-col-{if $hasPassword}4{else}6{/if}">
                <input type="password" class="_field" name="password_confirmation" id="changePassRepeat" placeholder="{t}sf.widget.user.account.ph.password_repeat{/t}*" required>
            </div>
        </div>
    </div>

    <div class="cc-form-actions">
        <div class="cc-form-row">
            <div class="cc-form-col-12 cc-form-col-align-right">
                <button class="_button js-loading" type="submit" name="submit" id="changePassSubmit">
                    <span class="_figure-stack">
                        <span class="_figure-stack-label">{t}sf.global.act.save{/t}</span>
                    </span>
                </button>
            </div>
        </div>
    </div>
</form>