<div class="_section-title">
    <h2>{t}sf.global.act.wishlists{/t}</h2>
    {if $widget->wishlist->getProducts()->count() > 0 && App\Helper\Plan::enabled('checkout')}
        <button class="_button add-cart-product js-disabled js-loading submit-to-cart margin-top-20">
        <span class="_figure-stack">
            <span class="_figure-stack-label">{t}sf.wishlist.added_products.button{/t}</span>
        </span>
        </button>
    {/if}
</div>
{$list_class = ''}
{if isset(Widget::get('filters')->getSetting('list_class'))}
    {$list_class = Widget::get('filters')->getSetting('list_class')}
{/if}
<div class="cc-wishlist">
    <div class="_products-list-main js-products-container js-empty-on-ajax js-wishlist">
        {include file="widgets/product/list.tpl" list_class=$list_class|default:"_list-one" utilities=W::utilities() products=$widget->wishlist->getProducts() widget_products=$widget->wishlist truncate_names=50 image_size='600x600' product_bar=true custom_labels=true}
    </div>

    {include file="widgets/common/pagging.tpl" page=$widget->wishlist->getPage() pages=$widget->wishlist->getPages() link=$widget->wishlist->getLink()}
</div>
{capture append="js"}
    <script type="text/javascript">
        var msg_1 = '{t}sf.wishlist.added_products.one{/t}';
        var msg_2 = '{t}sf.wishlist.added_products.multi{/t}';
        $(function () {
            $('.submit-to-cart').on('click', function (e) {
                var requests = document.querySelectorAll('[data-request]');
                for (var i in requests) if (requests.hasOwnProperty(i)) {
                    $.get(
                        '/cart/add/' + requests[i].getAttribute('data-request'),
                    );
                }
                if (requests.length == 1) {
                    var msg = requests.length + msg_1
                } else {
                    var msg = requests.length + msg_2
                }
                $('._cart-compact').trigger('cc.ajax.reload');
                CCHelper.toastrSuccess(msg);
            });
        });
    </script>
{/capture}
<style>
    .cc-wishlist ._products-list ._product ._product-add {
        display: block;
    }

    .margin-top-20 {
        margin-top: 20px;
    }

    ._product-bar-col-compare {
        display: none;
    }
</style>
