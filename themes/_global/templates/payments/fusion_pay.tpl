<style>
    .fusion-pay-button {
        padding: 10px;
        border-radius: 10px;
        width: 100%;
        max-width: 235px;
        margin-top: 0.5rem;
        margin-bottom: 0.5rem;
        display: block;
        height: 60px;
    }

    .fusion-pay-button-1 {
        background-color: #ff6600 !important;
        color: #fff !important;
    }

    .fusion-pay-button-2 {
        background-color: #ff6600 !important;
        color: #000 !important;
    }

    .fusion-pay-button-3 {
        background-color: #000 !important;
        color: #fff !important;
    }

    .fusion-pay-button-4 {
        background-color: #000 !important;
        color: #fff !important;
    }

    .fusion-pay-button.translate-custom-button {
        position: relative;
        transform: translate(0, -50%);
    }

    .fusion-pay-button .fusion-pay-button-logo {
        margin-left: 5px;
        width: 100%;
        max-width: 50px !important;
        height: 20px;
        vertical-align: middle;
    }
</style>
<div style="display: flex;">
    <a href="" data-url="" data-ajax-panel=""
       class="fusion-pay-button hidden">
        <div class="text-center">
            {t}payment.button.buy_with{/t}
            <img class="fusion-pay-button-logo"
                 src="" alt="logo"/>
        </div>
        <div class="text-center">
            <span class="month"></span> {t}payment.button.contributions{/t} <span class="price"></span>
        </div>
    </a>
</div>
{if is_array($promo_button_id)}
    {$promo_button_id = array_first($promo_button_id)}
{/if}

<script>
    window.addEventListener('load', function () {
        const creditorsButton = $('#credit-calculator-js');

        function updatePrice() {
            let priceInput = creditorsButton.attr('data-credit-price');
            if (isNaN(priceInput)) {
                priceInput = '{$product->price_input}';
            }
            if (priceInput > 0) {
                let currentPrice = priceInput * 100;
                let calculateUrl = '/creditor/button-info/{$provider}/' + currentPrice;

                let images = {
                    1: '{$img_url}sitecp/img/app-icons/payment/fusion-pay-white.png?{app('last_build')}',
                    2: '{$img_url}sitecp/img/app-icons/payment/fusion-pay-black.png?{app('last_build')}',
                    3: '{$img_url}sitecp/img/app-icons/payment/fusion-pay-white.png?{app('last_build')}',
                    4: '{$img_url}sitecp/img/app-icons/payment/fusion-pay-orange.png?{app('last_build')}'
                };
                $.ajax({
                    url: calculateUrl,
                    type: 'GET',
                    success: function (response) {
                        if (response.month > 0) {
                            let panelUrl = '/creditor/select/{$product->id}/' + priceInput + '/?provider={$provider}&month=' + response.month;
                            $('.fusion-pay-button').attr('href', panelUrl);
                            $('.fusion-pay-button').attr('data-url', panelUrl);
                            $('.fusion-pay-button').addClass('fusion-pay-button-{$promo_button_id}');
                            $('.fusion-pay-button').removeClass('hidden')
                            $('.fusion-pay-button-{$promo_button_id} .month').text(response.month);
                            $('.fusion-pay-button-{$promo_button_id} .price').html(response.price);
                            $('.fusion-pay-button-logo').attr('src', images[{$promo_button_id}]);
                        } else {
                            $('.fusion-pay-button-{$promo_button_id}').addClass('hidden');
                        }
                    }
                });
            }
        }

        const observer = new MutationObserver(function (mutations) {
            mutations.forEach(function (mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'data-credit-price') {
                    updatePrice();
                }
            });
        });

        // Observe the button for attribute changes
        observer.observe(creditorsButton[0], {
            attributes: true // Listen to attribute changes
        });
        updatePrice();
    });
</script>
