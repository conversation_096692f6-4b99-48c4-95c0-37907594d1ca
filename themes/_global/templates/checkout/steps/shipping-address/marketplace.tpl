{$has_guest_email=$cc_cart->hasGuestEmailInShippingForm()}
<div class="js-checkout-shipping-marketplace-holder">
    <div class="cc-form-row">
        {if setting('checkout_hide_first_name', 'required') != 'hidden'}
            <div class="cc-form-col-{if $has_guest_email|default}6{else}4{/if}">
                {include file=base_path('themes/_global/templates/form-components/input.tpl')
                    type="text"
                    name="checkout[shipping][address][first_name]"
                    id="checkout-shipping-marketplace-fist-name"
                    input_class=inputIdPrefix("address-first-name", "js-fillable-checkout[shipping][address]")
                    label="{t}sf.global.ph.first_name{/t}"
                    value=$address->first_name|default:$cc_cart->customer->first_name|default:$cc_guest->first_name|default
                    required="{if setting('checkout_hide_first_name', 'required') == 'required'}true{else}false{/if}"
                }
            </div>
        {/if}
        {if setting('checkout_hide_last_name', 'required') != 'hidden'}
            <div class="cc-form-col-{if $has_guest_email|default}6{else}4{/if}">
                {include file=base_path('themes/_global/templates/form-components/input.tpl')
                    type="text"
                    name="checkout[shipping][address][last_name]"
                    id="checkout-shipping-marketplace-last-name"
                    input_class=inputIdPrefix("address-last-name", "js-fillable-checkout[shipping][address]")
                    label="{t}sf.global.ph.last_name{/t}"
                    value=$address->last_name|default:$cc_cart->customer->last_name|default:$cc_guest->last_name|default
                    required="{if setting('checkout_hide_last_name', 'required') == 'required'}true{else}false{/if}"
                }
            </div>
        {/if}

        {if $has_guest_email|default}
    </div>
    <div class="cc-form-row">
        <div class="cc-form-col-6">
            {include file=base_path('themes/_global/templates/form-components/input.tpl')
                type="email"
                name="email"
                id="marketplace-email"
                input_class=inputIdPrefix("email", "js-fillable-checkout[shipping][address]")
                label="{t}sf.global.ph.email{/t}"
                value="{$cc_cart->customer->email|default:$cc_guest->email|default}"
                required=true
            }
        </div>
        {/if}
        {if setting('checkout_hide_phone', 'required') != 'hidden'}
            <div class="cc-form-col-{if $has_guest_email|default}6{else}4{/if}">
                {include file=base_path('themes/_global/templates/form-components/input.tpl')
                    type="tel"
                    name="checkout[shipping][address][phone]"
                    id="checkout-shipping-marketplace-phone"
                    class="cc-form-group-tel"
                    label="{t}sf.global.ph.phone{/t}"
                    value=$address->phone|default:$cc_guest->alternative_phone|default
                    required="{if setting('checkout_hide_phone', 'required') == 'required'}true{else}false{/if}"
                    input_class="js-phone-intl {inputIdPrefix("address-phone", "js-fillable-checkout[shipping][address]")}"
                    input_attributes=[
                        'data-error-label' => __('sf.address.err.invalid_phone_number'),
                        "data-ip" => Request::ip()
                    ]
                }
            </div>
        {/if}
    </div>

    {if $has_guest_email|default && $fields|default}
        {foreach $fields->chunk(2) AS $rows}
            <div class="cc-form-row">
                {foreach $rows AS $field}
                    <div class="cc-form-col-6">
                        {include file=base_path("themes/_global/templates/customer/custom-form-component/{$field->type}.tpl") field=$field value=$fields_value->get($field->id) custom_prefix=$fields_prefix|default}
                    </div>
                {/foreach}
            </div>
        {/foreach}
    {/if}

    {if $has_guest_email|default}
        {include file=Illuminate\Support\Facades\View::make('global::checkout.include.gdpr')->getPath() gdpr_form='register' chunk_form=true}
    {/if}

    <div class="cc-form-section">
        <div class="cc-form-row">
            <div class="cc-form-col-12">
                <input type="hidden" id="checkout_hidden_marketplace"
                       name="checkout[shipping][address][marketplace][name]"
                       {if $address && $address->marketplace}value="{$address->marketplace_name}"{/if}>

                {include file=base_path('themes/_global/templates/form-components/select.tpl')
                    name="checkout[shipping][address][marketplace][id]"
                    id="checkout-marketplace-select"
                    class="cc-form-group-select cc-shipping-provider"
                    input_class="js-checkout-marketplace-select"
                    label=__('sf.checkout.label.marketplace')
                    placeholder=true
                    value=$address->marketplace_id|default
                    options=$marketplaces->pluck('title_with_address', 'id')
                    input_attributes=[
                        "data-dd-class" => "cc-select-drop cc-shipping-provider-drop",
                        "data-event-select-change" => "#checkout_hidden_marketplace"
                    ]
                    required=true
                }
            </div>
        </div>
    </div>

    {if !$hideMap|default}
        <div class="cc-form-section">
            <div class="cc-form-row">
                <div class="cc-form-col-12">
                    <div class="cc-form-map js-marketplaces-map"></div>
                </div>
            </div>
        </div>
    {/if}
</div>
{script}
    <script>
        var select = $('.js-checkout-marketplace-select');
        {if !$hideMap|default}
        var map = null,
            addresses = {$marketplaces->pluck('address', 'id')->toJson() nofilter},
            bounds;

        function initMap() {
            var mapInstance = new google.maps.Map(jQuery('.js-marketplaces-map').get(0), {
                zoom: 10,
                center: {
                    lat: {$marketplaces[0]->address->latitude|default:0},
                    lng: {$marketplaces[0]->address->longitude|default:0}
                }
            });

            bounds = new google.maps.LatLngBounds();
            {foreach $marketplaces AS $marketplace}
            var myLatLng = new google.maps.LatLng({$marketplace->address->latitude}, {$marketplace->address->longitude});
            var marker = new google.maps.Marker({
                position: myLatLng,
                map: mapInstance,
                title: '{$marketplace->title}'
            });
            marker.addListener('click', function () {
                select.val({$marketplace->id}).trigger('change')
            });
            bounds.extend(myLatLng);
            {/foreach}

            {if $marketplaces->count() == 1}
            mapInstance.setZoom(17);
            {else}
            mapInstance.fitBounds(bounds);
            {/if}

            return mapInstance;
        }

        if (jQuery('.cc-form-shipping-type > [data-widget="accordion"]').length > 0) {
            jQuery('.cc-form-shipping-type > [data-widget="accordion"]').on('accordion.open', function (e, $head, $content) {
                if ($content.hasClass('js-accordion-shipping-type-marketplace')) {
                    map = initMap();
                }
            });
        } else if ($('.js-marketplaces-map').length > 0) {
            map = initMap();
        }
        select.on('change', function () {
            var value = $(this).val();
            if (!value || !(value in addresses) || !map) {
                {if $marketplaces->count() > 1}
                if (map && bounds && !value) {
                    map.fitBounds(bounds);
                }
                {/if}
                return null;
            }
            var address = addresses[value];

            map.setCenter(
                new google.maps.LatLng(address.latitude, address.longitude)
            );
            map.setZoom(17);
        }).trigger('change');
        {/if}
        if(typeof(select.on) === 'function') {
            select.on('select2-opening', function () {
                $('input.select2-input').prop('placeholder', '');
            });
        }
    </script>
{/script}
