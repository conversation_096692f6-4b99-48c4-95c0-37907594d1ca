<div class="cc-checkout-step cc-checkout-step-shipping-address js-checkout-shipping-address js-checkout-hash-reload" data-ajax-box="{route('checkout.shipping.address')}">
    {if $types->count()}
        <div class="cc-checkout-title">
            <div class="cc-checkout-title-main">
                <h2>{t}sf.checkout.header.shipping_address{/t}</h2>
            </div>

            {if $allowed_tabs->has('login') && $cc_cart->hasGuestEmailInShippingForm()}
                <div class="cc-checkout-title-addon" data-panel-title="addon">
                    {if $cc_cart->customer}
                        {$history_link = route('checkout.shipping.address')}
                    {else}
                        {$history_link = route('checkout')}
                    {/if}

                    {t}sf.widget.checkout.steps.you_have_an_account{/t} <a class="js-action" href="{route('checkout.authorize.login')}" data-ajax-panel="true" data-ajax-panel-history="{json_encode(['old' => $history_link,'new' => "{route('checkout.authorize.login')}"])}">{t}sf.global.act.log_in{/t}</a>
                </div>
            {/if}
        </div>

        {$hasActive = $types->where('active', true)->count()}

        <div class="cc-checkout-section">
            <form class="cc-form cc-form-shipping-type js-form-submit-ajax-new" action="{route('checkout.shipping.address')}" role="form" method="post" data-submit-loader="true">
                {if $types->count()>1}
                    <div class="cc-form-section cc-accordion" {if $types->count()>1}data-widget="accordion"{/if}>
                        {foreach $types AS $type}
                            {$isActive = $type['active']}
                            <div class="cc-accordion-section skip-has-error{if $isActive} active{/if}">
                                <div class="cc-accordion-section-head skip-has-error{if $isActive} active{/if}" data-accordion-title="true" data-accordion-content=".js-accordion-shipping-type-{$type['key']}">
                                    <div class="cc-accordion-section-head-item skip-has-error">
                                        {include file=base_path('themes/_global/templates/form-components/radio.tpl')
                                            name="checkout[shipping][type]"
                                            id="checkout-shipping-type-{$type['key']}"
                                            class="cc-accodrion-section-head-item-radio skip-has-error"
                                            input_class="js-accordion-item-head-radio"
                                            label=$type['name']
                                            value=$type['key']
                                            checked=$isActive
                                            disabled=true
                                        }
                                    </div>
                                </div>

                                {if $type['html']|default}
                                    <div class="cc-accordion-section-body js-accordion-shipping-type-{$type['key']}{if $isActive} open{/if}"{if !$isActive} style="display: none;"{/if}>
                                        {$type['html'] nofilter}
                                    </div>
                                {/if}
                            </div>
                        {/foreach}
                    </div>
                {else}
                    {foreach $types AS $type}
                        <input type="hidden" name="checkout[shipping][type]" value="{$type['key']}" readonly="readonly">
                        {if $type['html']|default}
                            <div class="cc-form-section js-single-shipping-type-{$type['key']}">
                                {$type['html'] nofilter}
                            </div>
                        {/if}
                    {/foreach}
                {/if}

                {if !setting('checkout_hide_billing_address') && !setting('checkout_require_billing_address')}
                    <div class="cc-form-section">
                        <div class="cc-form-row">
                            <div class="cc-form-col-12">
                                {include file=base_path('themes/_global/templates/form-components/checkbox.tpl')
                                    name="checkout[billing][has]"
                                    id="checkout-has-billing-address"
                                    label=__('sf.checkout.label.use_different_billing_address')
                                    value=1
                                    checked=$cc_cart->hasBillingAddress()
                                    input_attributes=[
                                        'data-toggle-box1' => '.js-checkout-billing-address'
                                    ]
                                }
                            </div>
                        </div>
                    </div>
                {elseif setting('checkout_require_billing_address') && !setting('checkout_hide_billing_address')}
                    <input type="hidden" name="checkout[billing][has]" value="1">
                {/if}
                {if Apps::installed('bumper_offer') && Apps::enabled('bumper_offer')}
                    {$bumper_products = App\Models\System\AppsManager::getManager('bumper_offer')->getBumperProducts()}
                    {if !empty($bumper_products)}
                        <div class="_text-box-checkout hidden-lg">
                            {include file="widgets/extra/bumper_offer.tpl" data=$bumper_products}
                        </div>
                    {/if}
                {/if}
                <div class="cc-form-actions">
                    <div class="cc-form-row">
                        <div class="cc-form-col-12 cc-form-col-align-right">
                            <button class="cc-button js-loading" type="submit"{if !$hasActive} disabled{/if}>{t}sf.global.act.save_and_continue{/t}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    {else}
        <div class="cc-notification">
            <p>{t}sf.widget.checkout.nfy.no_shipping_types_available{/t}</p>
        </div>
    {/if}
</div>

{script}
<script type="text/javascript">
(() => {
    let fields = [
        '.js-fillable-checkout-shipping-address-address-first-name',
        '.js-fillable-checkout-shipping-address-address-last-name',
        '.js-fillable-checkout-shipping-address-email',
        '.js-fillable-checkout-shipping-address-address-phone'
    ];

    fields.forEach((selector) => {
        $(selector).off('change.mf input.mf keyup.mf keydown.mf').on('change.mf input.mf keyup.mf keydown.mf', function() {
            let otherFields = $(selector).not(this),
                that = $(this);

            otherFields.val(that.val())
            if(that.closest('.js-complex-field').hasClass('filled')) {
                otherFields.closest('.js-complex-field').addClass('filled');
            } else {
                otherFields.closest('.js-complex-field').removeClass('filled');
            }
        });
    })
})();
</script>
{/script}
