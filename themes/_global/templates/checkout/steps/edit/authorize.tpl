<div class="cc-checkout-table cc-checkout-passed-step js-checkout-authorize" data-ajax-box="{route('checkout.authorize')}" data-effect="refresh">
    <div class="cc-checkout-table-row active">
        <div class="cc-checkout-table-cell cc-checkout-table-cell-title">
            {t}sf.widget.checkout.steps.client_info{/t}

            {if $customer->isGuest()}
                <a class="cc-edit-icon fa fa-pencil js-action" href="{route('checkout.authorize.change')}" data-ajax-panel data-ajax-panel-history="{json_encode(['old' => route('checkout'),'new' => "{route('checkout.authorize.login')}"])}"></a>
            {else}
                <a class="cc-edit-icon fa fa-sign-out js-action" href="{route('site.auth.logout')}"></a>
            {/if}
        </div>

        <div class="cc-checkout-table-cell cc-checkout-edit-table-cell-text">
            <div class="cc-checkout-table-text">
                <div class="cc-checkout-table-text-inner">
                    {$customer->email}
                </div>
            </div>
        </div>

        <div class="cc-checkout-table-cell cc-checkout-table-cell-addon cc-edit-box">
            {if $customer->isGuest()}
                <a class="js-action" href="{route('checkout.authorize.change')}" data-ajax-panel>{t}sf.global.act.edit{/t}</a>
            {else}
                <a class="js-action" href="{route('site.auth.logout')}">{t}sf.global.act.logout{/t}</a>
            {/if}
        </div>
    </div>
</div>