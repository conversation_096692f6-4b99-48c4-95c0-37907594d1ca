{$shipping_address = $cc_cart->getShippingAddress()}
<div class="cc-checkout-step cc-checkout-step-shipping js-checkout-shipping js-checkout-hash-reload" data-ajax-box="{route('checkout.shipping.shipping')}">
    <div class="cc-checkout-title">
        <div class="cc-checkout-title-main">
            <h2>{t}sf.widget.checkout.header.delivery{/t}</h2>
        </div>
    </div>

    {$hasActive = false}
    {foreach $managers AS $m}
        {if $manager && $manager->id == $m->getId()}
            {$hasActive = true}
        {elseif setting('default_shipping_provider') == $m->getKey()}
            {$hasActive = true}
        {/if}
    {/foreach}

    <div class="cc-checkout-section">
        <form class="cc-form cc-form-shipping-provider js-form-submit-ajax-new" action="{route('checkout.shipping.shipping')}" role="form" method="post" data-submit-loader="true">

            {if $managers->count()}
            <div class="cc-form-section cc-accordion" data-widget="accordion">
                {script}
                    <script>
                    jQuery('.js-checkout-shipping').on('shipping_provider_quotes_success_{$managers->count()}', function(e, has_delivery_dates) {
                        {if Request::ajax()}{$chipping_change=intval(activeRoute('checkout.shipping.shipping.change'))}{else}{$chipping_change=1}{/if}
                        BaseCheckout.shippingLoad({intval(setting('checkout_hide_single_shipping'))}, {$chipping_change}, has_delivery_dates);
                    });
                    </script>
                {/script}
                {foreach $managers AS $m}
                    {include file=base_path('themes/_global/templates/checkout/include/_shipping_provider_quotes.tpl') total_managers=$managers->count() provider=$m->getProvider([])}
                {/foreach}
            </div>

            <div class="cc-form-section js-shipping-quotes-box-loading">
                <p>{t}sf.global.loading{/t}</p>
            </div>
            <div class="cc-form-section js-shipping-quotes-box-no-shipping" style="display: none;">
                <p>{t}sf.widget.checkout.nfy.no_shipping_method_available{/t}</p>
            </div>
                {if Apps::installed('bumper_offer') && Apps::enabled('bumper_offer')}
                    {$bumper_products = App\Models\System\AppsManager::getManager('bumper_offer')->getBumperProducts()}
                    {if !empty($bumper_products)}
                        <div class="_text-box-checkout hidden-lg">
                            {include file="widgets/extra/bumper_offer.tpl" data=$bumper_products}
                        </div>
                    {/if}
                {/if}
            <div class="cc-form-actions">
                <div class="cc-form-row">
                    <div class="cc-form-col-12 cc-form-col-align-right">
                        <button class="cc-button js-loading" type="submit"{if !$hasActive} disabled{/if} disabled>{t}sf.global.act.save_and_continue{/t}</button>
                    </div>
                </div>
            </div>
            {else}
                <div class="cc-notification">
                    <p>{t}sf.widget.checkout.nfy.no_shipping_method_available{/t}</p>
                </div>
            {/if}
        </form>
    </div>
</div>
