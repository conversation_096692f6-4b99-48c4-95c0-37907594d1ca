<div class="cc-checkout-step cc-checkout-step-shipping js-checkout-payment js-checkout-hash-reload js-checkout-hash-reload"
     data-ajax-box="{route('checkout.payment')}">
    <div class="cc-checkout-title">
        <div class="cc-checkout-title-main">
            <h3>{t}sf.global.payment.providers{/t}</h3>
        </div>
    </div>

    {$hasActive = false}
    {if $managers->count()}
        <div class="cc-checkout-section">
            <form class="cc-form cc-form-payment-provider js-checkout-payment-form js-form-submit-ajax-new" action="{route('checkout.payment')}"
                  role="form" method="post" data-submit-loader="true">
                <div class="cc-form-section cc-accordion js-payments" data-widget="accordion">
                    {if $managers->has('regular')}
                        {foreach $managers->get('regular') AS $m}
                            {$isActive = $manager->provider|default:setting('default_payment_provider') == $m->provider}
                            {if $isActive}{$hasActive = true}{/if}
                            <div class="cc-accordion-section skip-has-error{if $isActive} active{/if}">
                                <div class="cc-accordion-section-head skip-has-error{if $isActive} active{/if}"
                                     data-accordion-title
                                     data-accordion-content=".js-accordion-payment-provider-{$m->provider}">
                                    {$input_attributes = []}
                                    {if $m->support_recalculate_shipping || 1}
                                        {$input_attributes = ["data-shipping-recalculate"=>route('checkout.shipping.recalculate')]}
                                    {/if}

                                    <div class="cc-accordion-section-head-item skip-has-error">
                                        {include file=base_path('themes/_global/templates/form-components/radio.tpl')
                                        name="checkout[payment][provider]"
                                        id="checkout-shipping-{$m->provider}"
                                        class="cc-accordion-section-head-item-radio skip-has-error"
                                        input_class="js-accordion-item-head-radio"
                                        value=$m->provider checked=$isActive
                                        input_attributes=$input_attributes
                                        disabled=true
                                        }

                                        <span class="cc-accordion-section-head-item-image">
                                            <img src="{$m->getImage('150x150')}" alt="{$m->storefront_name}"
                                                 title="{$m->storefront_name}"/>
                                        </span>

                                        <span class="cc-accordion-section-head-item-text">{$m->storefront_name}</span>
                                        {$input_attributes = []}
                                    </div>
                                </div>
                                {if  setting('payment_description', 1) == 1 && !empty($m->configuration) && !empty($m->configuration['payment_description'])}
                                    <div class="cc-accordion-section-body js-accordion-payment-provider-{$m->provider}{if $isActive} open{/if}"{if !$isActive} style="display: none;"{/if} style="word-wrap: break-word;">
                                        {$m->configuration['payment_description']|unescape: "html" nofilter}
                                    </div>
                                {/if}

                                {if $m->supportCustomerDetails()}
                                    {$cd = $m->renderCustomerDetails()}
                                    {if $cd}
                                        <div class="cc-accordion-section-body js-accordion-payment-provider-{$m->provider}{if $isActive} open{/if}"{if !$isActive} style="display: none;"{/if}>
                                            {$cd nofilter}
                                        </div>
                                    {/if}
                                {/if}
                            </div>
                        {/foreach}
                    {/if}

                    {if $managers->has('credit')}
                        {$isActive=$managers->get('credit')->has($manager->provider|default:setting('default_payment_provider'))}
                        <div class="cc-accordion-section skip-has-error{if $isActive} active{/if}">
                            <div class="cc-accordion-section-head skip-has-error{if $isActive} active{/if}"
                                 data-accordion-title data-accordion-content=".js-accordion-payment-provider-credit">
                                <div class="cc-accordion-section-head-item skip-has-error">
                                    {include file=base_path('themes/_global/templates/form-components/radio.tpl')
                                    name="checkout_payment_provider_container"
                                    label=__('sf.leasing.buy.label.submit')
                                    id="checkout-shipping-credit"
                                    class="cc-accordion-section-head-item-radio skip-has-error"
                                    input_class="js-accordion-item-head-radio"
                                    value='__'
                                    checked=$isActive
                                    disabled=true
                                    input_attributes=[
                                    'data-creditor' => '__'
                                    ]
                                    }
                                </div>
                            </div>

                            <div class="cc-accordion-section-body js-accordion-payment-provider-credit{if $isActive} open{/if}"{if !$isActive} style="display: none;"{/if}>
                                <div class="cc-accordion" data-widget="accordion">
                                    {foreach $managers->get('credit') AS $m}
                                        {$isActive = $manager->provider|default:setting('default_payment_provider') == $m->provider}
                                        {if $isActive}{$hasActive = true}{/if}
                                        <div class="cc-accordion-section skip-has-error{if $isActive} active{/if}">
                                            <div class="cc-accordion-section-head skip-has-error{if $isActive} active{/if}"
                                                 data-accordion-title="{$m->provider}"
                                                 data-accordion-content=".js-accordion-payment-provider-{$m->provider}">
                                                {$input_attributes = [
                                                'data-creditor' => $m->provider
                                                ]}

                                                {if $m->support_recalculate_shipping || 1}
                                                    {$input_attributes["data-shipping-recalculate"] = route('checkout.shipping.recalculate')}
                                                {/if}

                                                <div class="cc-accordion-section-head-item skip-has-error">
                                                    {include file=base_path('themes/_global/templates/form-components/radio.tpl')
                                                    name="checkout[payment][provider]"
                                                    id="checkout-shipping-{$m->provider}"
                                                    class="cc-accordion-section-head-item-radio skip-has-error"
                                                    input_class="js-accordion-item-head-radio"
                                                    value=$m->provider checked=$isActive
                                                    input_attributes=$input_attributes
                                                    }

                                                    <span class="cc-accordion-section-head-item-image">
                                                        <img src="{$m->getImage('150x150')}" alt="{$m->storefront_name}"
                                                             title="{$m->storefront_name}">
                                                    </span>

                                                    <span class="cc-accordion-section-head-item-text">{$m->storefront_name}</span>
                                                </div>

                                                {$input_attributes = []}
                                            </div>

                                            <div class="cc-accordion-section-body js-accordion-payment-provider-{$m->provider}{if $isActive} open{/if}"{if !$isActive} style="display: none;"{/if}>
                                                <div class="cc-form-section">
                                                    <div class="cc-form-row">
                                                        <div class="cc-form-col-6 js-creditor-customer">
                                                            {include file="widgets/payment/creditor_checkout_form_customer.tpl" provider=$m->provider}
                                                        </div>

                                                        <div class="cc-form-col-6">
                                                            {if $m->html['details']|default}
                                                                <div class="js-leasing-options-table">
                                                                    {$m->html['details'] nofilter}
                                                                </div>
                                                            {/if}
                                                        </div>
                                                    </div>
                                                    {if $m->html['conditions']|default}
                                                        {$m->html['conditions'] nofilter}
                                                    {/if}

                                                    {if $m->terms && $m->terms->count()}
                                                        {foreach $m->terms AS $payment_term}
                                                            <div class="cc-form-row">
                                                                <div class="cc-form-col-12">
                                                                    <div class="js-leasing-options-table">
                                                                        {include file="widgets/payment/creditor_checkout_form_terms_multy.tpl" provider=$m->provider providerName=$m->storefront_name term=$payment_term}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        {/foreach}
                                                    {else}
                                                        <div class="cc-form-row">
                                                            <div class="cc-form-col-12">
                                                                <div class="js-leasing-options-table">
                                                                    {include file="widgets/payment/creditor_checkout_form_terms.tpl" provider=$m->provider providerName=$m->storefront_name}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    {/if}
                                                </div>
                                            </div>
                                        </div>
                                    {/foreach}
                                </div>
                            </div>
                        </div>
                    {/if}
                </div>

                <div class="cc-form-section cc-form-section-step">
                    <div class="cc-checkout-title">
                        <div class="cc-checkout-title-main">
                            <h3>{t}sf.checkout.ph.order_note{/t}</h3>
                        </div>
                    </div>

                    {include file=base_path('themes/_global/templates/form-components/textarea.tpl')
                    id='checkout-note'
                    name='checkout[note]'
                    label="{t}sf.checkout.ph.order_note{/t}"
                    rows="4"
                    input_attributes=[]
                    }
                </div>

                {include file=Illuminate\Support\Facades\View::make('global::checkout.include.terms')->getPath()}
                {include file=Illuminate\Support\Facades\View::make('global::checkout.include.gdpr')->getPath() gdpr_form='submit_payment'}
                {if Apps::installed('bumper_offer') && Apps::enabled('bumper_offer')}
                    {$bumper_products = App\Models\System\AppsManager::getManager('bumper_offer')->getBumperProducts()}
                    {if !empty($bumper_products)}
                        <div class="_text-box-checkout hidden-lg">
                            {include file="widgets/extra/bumper_offer.tpl" data=$bumper_products}
                        </div>
                    {/if}
                {/if}
                <div class="cc-form-actions">
                    <div class="cc-form-row">
                        <div class="cc-form-col-12 cc-form-col-align-right">
                            <button class="cc-button cc-button-highlight js-loading"
                                    type="submit"{if !$hasActive} disabled{/if}>{t}sf.global.act.finish_order{/t}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    {if $total_managers|default == 1 && $hasActive}
    {script}
        <script>
            setTimeout(function () {
                // jQuery('.js-checkout-payment-form input[name="checkout[payment][provider]"]:checked').trigger('change.shipping.recalculate');
            }, 100);
        </script>
    {/script}
    {/if}
    {else}
        <div class="_notification">
            <p>{t}sf.global.nfy.no_payment_method_available{/t}</p>
        </div>
    {/if}
</div>
