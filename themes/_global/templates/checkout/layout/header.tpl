<!-- BEGIN: captures -->
{capture name="navigation_main"}
    {include file="widgets/navigation/main.tpl" widget=W::mainNavigation()}
{/capture}
{capture name="navigation_links"}
    {include file="widgets/navigation/links.tpl" widget=$widget->navigationLinks}
{/capture}
{capture name="cart_compact"}
    {include file="../cart/compact.tpl"}
{/capture}
{capture name="wishlist_compact"}
    {if Auth::logged()}
        {include file="widgets/wishlist/compact.tpl" widget=$widget->wishlistMenu products=$widget->wishlistMenu->getProducts()}
    {/if}
{/capture}
{capture name="user_controls"}
    {include file="widgets/user/controls.tpl" widget=W::authorize()}
{/capture}
{capture name="search_form"}
    {if $widget->search->isEnabled()}
        {include file="widgets/extra/search_form.tpl"}
    {/if}
{/capture}
<!--// END: captures -->
<!-- BEGIN: header mobile slide -->
<div class="_header-mobile-slide js-header-mobile-slide js-scrollable hidden-lg hidden-md">
    <div class="_header-mobile-slide-box">
        {$smarty.capture.search_form nofilter}
    </div>
    <div class="_header-mobile-slide-box">
        {$smarty.capture.navigation_main nofilter}
    </div>
    <div class="_header-mobile-slide-box">
        {$smarty.capture.navigation_links nofilter}
    </div>
    <div class="_header-mobile-slide-box">
        {$smarty.capture.user_controls nofilter}
    </div>
</div>
<!--// END: header mobile slide -->
<!-- BEGIN: wrapper -->
<div class="_wrapper js-wrapper">
    <!-- BEGIN: promobar -->
    {if $widget->htmlLine->getData()->get('enabled')}
        <div class="_promo-bar-wrapper js-promo-bar hidden" data-cookie-name="{$widget->htmlLine->getData()->get('cookie_name')}">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        {include file="widgets/extra/promobar.tpl" data=$widget->htmlLine->getData()}
                    </div>
                </div>
            </div>
        </div>
    {/if}
    <!--// END: promobar -->
    <!-- BEGIN: header mobile -->
    <div class="_header-mobile hidden-lg hidden-md js-header-fixed">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="_header-mobile-inner">
                        <div class="_header-mobile-inner-cell">
                            <a class="_figure-stack _header-mobile-toggle js-header-mobile-toggle" href="javascript:void(0)">
                                <i class="_figure-stack-icon fa fa-bars"></i>
                                <span class="_figure-stack-label">{t}sf.global.menu{/t}</span> </a>
                        </div>
                        <div class="_header-mobile-inner-cell">
                            {include file="widgets/extra/logo.tpl" widget=W::logo()}
                        </div>
                        <div class="_header-mobile-inner-cell">
                            {$smarty.capture.cart_compact nofilter}
                            {$smarty.capture.wishlist_compact nofilter}
                            {$smarty.capture.user_controls nofilter}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--// END: header mobile -->
    <!-- BEGIN: header -->
    <header class="_header hidden-sm hidden-xs">
        <div class="_top-bar">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="_top-bar-inner">
                            <div class="_top-bar-inner-cell">
                                {$smarty.capture.navigation_links nofilter}
                            </div>
                            <div class="_top-bar-inner-cell">
                                {$smarty.capture.search_form nofilter}
                                {$smarty.capture.cart_compact nofilter}
                                {$smarty.capture.wishlist_compact nofilter}
                                {$smarty.capture.user_controls nofilter}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="_header-inner">
                        <div class="_header-inner-cell">
                            {if empty(segment(1))}
                            <h1 class="_logo-wrapper">{$logo_block = false}{else}
                                <div class="_logo-wrapper">{$logo_block = true}{/if}
                                    {include file="widgets/extra/logo.tpl" widget=W::logo() logo_block=$logo_block}
                                {if empty(segment(1))}</h1>{else}</div>{/if}
                    </div>
                    <div class="_header-inner-cell">
                        {$smarty.capture.navigation_main nofilter}
                    </div>
                </div>
            </div>
        </div>
    </header><!--// END: header -->