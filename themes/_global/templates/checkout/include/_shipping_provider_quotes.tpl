{if !isset($loaded[$m->getKey()])}
    {$loaded[$m->getKey()] = false}
{/if}

{$isActive = false}
{if $manager}
    {$isActive = $manager->id == $m->getId()}
{elseif setting('default_shipping_provider') == $m->getKey()}
    {$isActive = true}
{/if}

{if $total_managers == 1 && !$isActive && $provider->has_delivery_dates}
    {$isActive = true}
{/if}

{$hideBox = $total_managers == 1 && $isActive && $provider->has_delivery_dates}

{$disabled = !$loaded[$m->getKey()] || !$m->hasCheckoutQuotes()}
<div class="cc-accordion-section skip-has-error{if $hideBox} active{/if}{if $disabled} cc-inactive{/if} js-shipping-quotes-box {if $loaded[$m->getKey()]}js-shipping-quotes-box-loaded{/if} js-shipping-quotes-box-holder-{$m->getKey()}"
     data-ajax-box="{route('checkout.shipping.quotes', $m->getKey())}" data-effect="replace" style="display: none;">
    <div class="cc-accordion-section-head skip-has-error{if $isActive} active{/if}"{if $disabled} data-inactive="true"{/if}
         data-accordion-title
         data-accordion-content=".js-accordion-shipping-provider-{$m->getKey()}" {if $hideBox}style="display: none" {/if}>
        <div class="cc-accordion-section-head-item skip-has-error">
            {include file=base_path('themes/_global/templates/form-components/radio.tpl')
            name="checkout[shipping][provider]"
            id="checkout-shipping-provider-{$m->getKey()}"
            class="cc-accordion-section-head-item-radio skip-has-error"
            input_class="js-accordion-item-head-radio"
            value=$m->getKey()
            checked=$isActive
            disabled=$disabled
            }

            <div class="cc-accordion-section-head-item-image">
                <img src="{$m->getImage('150x150')}" alt="" title="{$m->getName()}">
            </div>

            <span class="cc-accordion-section-head-item-text">{$m->getName()}{if $m->hasCheckoutQuotes() && $m->getCheckoutQuotes()->count() == 1 && $m->getCheckoutQuotes()->first()->getName() && $m->getCheckoutQuotes()->first()->getName() != $m->getName()} ({$m->getCheckoutQuotes()->first()->getName()}){/if}
                    {if empty($m->getProvider()->integration) && !empty($m->getCheckoutQuotes()->first())}
                    <br/>
                    {$m->getCheckoutQuotes()->first()->getDescription()}
                {/if}
            </span>
        </div>

        {if $m->hasCheckoutQuotes() && $m->getCheckoutQuotes()->where('hidden', 1)->count() == 0}
            {$sorted_quotes = $m->getCheckoutQuotes()->sortBy('price', SORT_ASC)}
            {$quote = $sorted_quotes->first()}
            <div class="cc-accordion-section-head-item">
                <div class="cc-accordion-section-head-item-price">
                    {if $sorted_quotes->count() == 1}
                        <input name="checkout[shipping][{$m->getKey()}][service_id]" type="hidden"
                               value="{$quote->getId()}" class="js-shipping-service-hidden">
                    {/if}
                    <span class="cc-accordion-section-head-item-price-value">{if $sorted_quotes->count() > 1}<span
                                class="cc-accordion-section-head-item-meta-text">{t}sf.global.from{/t}</span> {/if}{$quote->getPriceFormatted() nofilter}</span>
                </div>
            </div>
            {script}
                CCHelper.refresh(jQuery('.js-shipping-quotes-box-holder-{$m->getKey()}'));
            {/script}
        {else}
            <div class="cc-accordion-section-head-item">
                <div class="cc-accordion-section-head-item-price">
                    {if !$loaded[$m->getKey()]}
                        <span class="loader-dots-inline">
                            <span class="loader-dot"></span>
                            <span class="loader-dot"></span>
                            <span class="loader-dot"></span>
                        </span>
                    {else}
                        {t}sf.shipping.not.supported{/t}
                    {/if}
                </div>
            </div>
        {if !$loaded[$m->getKey()]}
        {script}
            <script>
                jQuery('.js-shipping-quotes-box-holder-{$m->getKey()}').trigger('cc.ajax.reload');
            </script>
        {/script}
        {/if}
        {/if}

        {script}
            <script>
                var shipping_boxes_loaded = jQuery('.js-shipping-quotes-box.js-shipping-quotes-box-loaded');
                jQuery('.js-checkout-shipping').trigger('shipping_provider_quotes_success_' + shipping_boxes_loaded.length, [{if $provider->has_delivery_dates}1{else}0{/if}]);
            </script>
        {/script}

    </div>
    {if $hideBox}
        <div class="cc-accordion-section-head skip-has-error active">
            <div class="cc-accordion-section-head-item skip-has-error">
                <h3>{t}sf.shipping.waybill.ph.priority_datetime{/t}</h3>
            </div>
        </div>
    {/if}

    {if $provider->has_delivery_dates && $provider->delivery_dates}
        <div class="cc-tabs cc-accordion-section-head-item js-accordion-shipping-provider-{$m->getKey()}{if $isActive} open{/if}"{if !$isActive} style="display: none;"{/if}
             data-widget="accordion">
            <div data-widget="tabs">
                <div class="cc-tabs-items">
                    {if $cc_cart->getMeta('delivery_date_key')}
                        {if $has = $provider->delivery_dates->map->firstWhere('key', $cc_cart->getMeta('delivery_date_key'))}
                            {if $has = $has->filter()->first()}
                                {$isActiveDate = $has->key}
                            {/if}
                        {/if}
                    {/if}

                    {foreach $provider->delivery_dates as $row=>$dates}
                        {$first = $dates->first()}
                        {if $isActiveDate|default}
                            {$isActiveTab = $dates->firstWhere('key', $isActiveDate)}
                        {else}
                            {$isActiveTab = $row < 1}
                        {/if}
                        <a href="#js-accordion-{$m->getKey()}-tab-{$first['key']}" data-tab
                           class="{if $isActiveTab}active{/if}" title="{$first['title']}">
                            <span class="tab-text">{$first['day_of_week_formatted']}</span><br>
                            <span class="tab-date">{$first['title']}</span>
                        </a>
                    {/foreach}
                </div>

                <div class="cc-tabs-content" data-error-field="checkout[shipping][{$m->getKey()}][delivery_date_key]">
                    {foreach $provider->delivery_dates as $row=>$dates}
                        {$first = $dates->first()}
                        {if $isActiveDate|default}
                            {$isActiveTab = $dates->firstWhere('key', $isActiveDate)}
                        {else}
                            {$isActiveTab = $row < 1}
                        {/if}
                        <div class="cc-tab-content cc-accordion cc-accordion-simple"
                             id="js-accordion-{$m->getKey()}-tab-{$first['key']}"
                             {if !$isActiveTab}style="display: none"{/if}>
                            {foreach $dates AS $date}
                                {$isActiveDeliveryDate = $isActiveDate|default == $date->key}
                                <div class="cc-accordion-section skip-has-error" style="border-color: transparent">
                                    <div class="cc-accordion-section-head skip-has-error{if $isActiveDeliveryDate} active{/if}">
                                        <div class="cc-accordion-section-head-item skip-has-error">
                                            {$priceExt=''}
                                            {$description=''}
                                            {if $hideBox && $quote|default}
                                                {$priceExt=" - <strong>{$quote->getPriceFormatted() nofilter}</strong>"}
                                            {/if}
                                            {if $date['disabled']}
                                                {$description=__('sf.shipping_hours.text.full')}
                                            {/if}
                                            {include file=base_path('themes/_global/templates/form-components/radio.tpl')
                                            name="checkout[shipping][{$m->getKey()}][delivery_date_key]"
                                            id="checkout-{$m->getKey()}-service-{$date['key']}"
                                            class="cc-accordion-section-head-item-radio skip-has-error"
                                            input_class="js-accordion-item-head-radio js-delivery-dates-date without-status-change"
                                            label=sprintf('%s - %s, %s%s', $date['from']->format('H:i'), $date['to']->format('H:i'), $m->getName(), $priceExt|default)
                                            value=$date['key']
                                            checked=$isActiveDeliveryDate
                                            disabled=$date['disabled']
                                            description=$description
                                            description_class='text text-danger small'
                                            input_attributes=[
                                            'data-provider-id' => $m->getId()
                                            ]
                                            }
                                        </div>
                                    </div>
                                </div>
                            {/foreach}
                        </div>
                    {/foreach}

                    {script}
                        <script>
                            $('.js-delivery-dates-date').off('change').on('change', function () {
                                var element = $(this),
                                    provider_id = element.data('provider-id');

                                if (element.is(':checked')) {
                                    return false;
                                }

                                $('#checkout-shipping-provider-internal_' + provider_id)
                                    .attr('checked', true).prop('checked', true).trigger('change')
                                    .closest('[data-accordion-title]').trigger('click');
                            }).filter(':checked').trigger('change');
                        </script>
                    {/script}
                </div>
            </div>
        </div>
    {elseif $m->hasCheckoutQuotes() && $m->getCheckoutQuotes()->count() > 1}
        <div class="cc-accordion-section-body js-accordion-shipping-provider-{$m->getKey()}{if $isActive} open{/if}"{if !$isActive} style="display: none;"{/if}
             data-widget="accordion">
            {if $m->getCheckoutQuotes()->count() > 1}
                <div class="cc-accordion cc-accordion-simple">
                    {foreach $m->getCheckoutQuotes() AS $quote}
                        {$isActiveService = $manager && $manager->service_id == $quote->getId()}
                        <div class="cc-accordion-section skip-has-error">
                            <div class="cc-accordion-section-head skip-has-error{if $isActiveService} active{/if}"
                                 data-accordion-title
                                 data-accordion-content=".js-accordion-{$m->getKey()}-service-{$quote->getId()}">
                                <div class="cc-accordion-section-head-item skip-has-error">
                                    {include file=base_path('themes/_global/templates/form-components/radio.tpl')
                                    name="checkout[shipping][{$m->getKey()}][service_id]"
                                    id="checkout-{$m->getKey()}-service-{$quote->getId()}"
                                    class="cc-accordion-section-head-item-radio skip-has-error"
                                    input_class="js-accordion-item-head-radio"
                                    label=$quote->getName()
                                    value=$quote->getId()
                                    checked=$isActiveService
                                    }
                                </div>

                                <div class="cc-accordion-section-head-item">
                                    <div class="cc-accordion-section-head-item-price">
                                        <span>{$quote->getPriceFormatted() nofilter}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {*
                        {if ($m->supportsInsurance() && $quote->getAllowanceInsurance()) || ($m->supportPriorityTime() && $quote->getAllowanceFixedTimeDelivery())}
                        <div class="js-accordion-{$m->getKey()}-service-{$quote->getId()}">
                        {if $m->supportsInsurance() && $quote->getAllowanceInsurance()}
                            <div class="row">
                                <div class="col-xs-12">
                                    {include file=base_path('themes/_global/templates/form-components/checkbox.tpl')
                                        name="checkout[shipping][{$m->getKey()}][insurance]"
                                        label=__('sf.widget.cart.label.insurance')
                                        value=1
                                        id="checkout-{$m->getKey()}-insurance-{$quote->getId()}"
                                    }
                                </div>
                            </div>
                        {/if}
                        {if $m->supportPriorityTime() && $quote->getAllowanceFixedTimeDelivery()}
                            <div class="row">
                                <div class="col-xs-12">
                                    {include file=base_path('themes/_global/templates/form-components/checkbox.tpl')
                                        name="checkout[shipping][{$m->getKey()}][priority_time_enable]"
                                        label=__('sf.shipping.waybill.ph.fixed_time')
                                        value=1
                                        id="checkout-{$m->getKey()}-priority-time-enable-{$quote->getId()}"
                                        input_attributes=[
                                            'data-toggle-box'=>".js-checkout-{$m->getKey()}-priority-time-{$quote->getId()}"
                                        ]
                                    }
                                </div>
                            </div>
                            <div class="row js-checkout-{$m->getKey()}-priority-time-{$quote->getId()}">
                                {$m->getPriorityTimeTemplate($quote) nofilter}
                            </div>
                        {/if}
                        </div>
                        {/if}
                        *}
                    {/foreach}
                </div>
            {/if}
        </div>
    {/if}
</div>
