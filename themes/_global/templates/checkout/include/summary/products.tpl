{if $isEnabled|default:true && $cc_cart|default && $cc_cart->has_products}
    <div class="cc-checkout-sidebar-section cc-summary-products js-checkout-summary-products" data-ajax-box="{route('checkout.summary.products')}">
		{foreach $cc_cart->products as $product}
			<div class="cc-summary-product" data-summary-product-key="{$product->key}">
				<a class="cc-summary-product-link" href="{$product->url()}" title="{$product->name}|{$product->category->name|default}" rel="noindex nofollow">
					<span class="cc-summary-product-image">
						<span class="cc-summary-product-image-thumb">
							<img class="{"product-cart-product-image `$classes['product-cart-product-image']|default`"|rtrim}" src="{$product->getImage('150x150')}" alt="{$product->name}" title="{$product->name}"/>
						</span>
					</span>

					<span class="cc-summary-product-text">
						<span class="cc-summary-product-text-inner">
							<span class="cc-summary-product-info">
								<span class="cc-summary-product-title">
									{if !empty($truncate_names)}{$product->name|truncate:$truncate_names:"...":true}{else}{$product->name}{/if}
								</span>

								<span class="cc-summary-product-parameters">
									<span class="cc-summary-product-parameter">
										<span class="cc-summary-product-parameter-value">
											{$product->getQuantityFormatted()} x {if $product->isHideDiscountedPrice()}{money($product->getDiscount()->getProductAmount()) nofilter}{else}{money($product->getPrice()->getPrice()) nofilter}{/if}
										</span>
									</span>
								</span>

								{if $product->parameters}
									<span class="cc-summary-product-parameters">
										{foreach $product->parameters AS $parameter}
											<span class="cc-summary-product-parameter">
												<span class="cc-summary-product-parameter-value">{$parameter['value'] nofilter}</span>
											</span>
										{/foreach}
									</span>
								{/if}
								{if $product->hasOptions()}
                                    <span class="cc-summary-product-parameters">
										{foreach $product->getOptions() AS $option}
                                            <span class="cc-summary-product-parameter">
												<span class="cc-summary-product-parameter-value">
													{if "{$option->getPrice()->getPrice() > 0 || $option->getDiscountedPrice()->getPrice() > 0}"}
														<span>{if $option->isPerItem()}{$product->getQuantity()}{else}1{/if} x {money("{if $product->hasDiscounts()}{$option->getDiscountedPrice()->getPrice()}{else}{$option->getPrice()->getPrice()}{/if}") nofilter} </span>
													{/if}

													({$option->name}:
													{if $option->isFile()}
														{$option->getFile()->getName()}
													{else}
														{$option->getValueFormatted() nofilter}
													{/if})
												</span>
											</span>
                                        {/foreach}
									</span>
								{/if}

								{include file=base_path('themes/_global/templates/checkout/include/discount_display.tpl')}
								{include file=base_path('themes/_global/templates/checkout/include/discount_code_display.tpl')}
								{include file=base_path('themes/_global/templates/checkout/include/discount_modificators.tpl')}
							</span>

							<span class="cc-summary-product-price">
								{include file=base_path('themes/_global/templates/cart/include/cartItemPrice.tpl')}
							</span>
						</span>
					</span>
				</a>

				<button class="cc-summary-product-remove cc-remove" href="{route('cart.remove', $product->key)}" data-ajax="toast" rel="noindex nofollow"><i class="fa fa-times"></i></button>

				<button class="cc-summary-product-edit" href="{route('cart.panel.edit', $cc_cart_key)}" data-ajax-panel="true" rel="noindex nofollow"><span class="cc-summary-product-edit-inner">{t}sf.global.act.edit{/t}<i class="fa fa-pencil"></i></span></button>
            </div>
		{/foreach}
    </div>
{/if}

{if $cc_cart|default && 1 == 0}
	{capture append="js"}
		<script type="text/javascript">
			var ccCartHashCheck = '{$cc_cart->hash_check}';

			function checkIsActiveTab() {
				var props = ['hidden', 'msHidden', 'webkitHidden'];

				for(var i in props) {
					if(typeof(document[props[i]]) != 'undefined') {
						return !document[props[i]];
					}
				}

				return true;
			}

			function ccHashCartCheck() {
				var isActiveTab = checkIsActiveTab();

				if(Cookies.get('ccchc') !== ccCartHashCheck) {
					if(!isActiveTab) {
						if($('body').hasClass('cc-checkout-page')) {
							$('.cc-checkout .js-checkout-hash-reload[data-ajax-box]').trigger('cc.ajax.reload');
						} else {
							$('[data-widget="cart"], [data-widget="cart-compact"]').trigger('cc.ajax.reload');
						}
					}
					ccCartHashCheck = Cookies.get('ccchc');
				}
				$('.js-checkout-total-formatted').text($('.js-checkout-summary-totals .cc-totals-row-total .cc-totals-value').text());
				setTimeout(ccHashCartCheck, 50);
			}

			setTimeout(ccHashCartCheck, 50);
		</script>
	{/capture}
{/if}
