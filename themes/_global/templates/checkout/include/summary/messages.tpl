{if $isEnabled|default:true && $cc_cart|default && $cc_cart->has_products}
    {$messages = $cc_cart->getMessages()->where('type', 'total')->values()}
    {if $messages->isNotEmpty()}
    <div class="cc-totals cc-checkout-summary-messages js-checkout-summary-messages">
        {foreach $messages as $row => $message}
            {if $row >= 1}
            <div style="margin-bottom: 5px; text-align: center; margin-top: -10px;">{t}sf.global.or{/t}</div>
            {/if}
            <div class="cc-box dashed warning">
                <span class="cc-amount-to-free-shipping">{$message->getMessage() nofilter}</span>
            </div>
        {/foreach}
    </div>
    {/if}
{/if}
