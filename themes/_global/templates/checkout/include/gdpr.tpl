{$gdpr_form = $gdpr_form|default:'empty'}

{if GDPR::getFormPolicies($gdpr_form)->count()}
    {if $chunk_form|default}
        {foreach GDPR::getFormPolicies($gdpr_form)->chunk(2) AS $rows}
            {foreach $rows AS $field}
                <div class="cc-form-row">
                    <div class="cc-form-col-6">
                        {include file=base_path("themes/_global/templates/form-components/checkbox.tpl") name="gdpr[{$field->id}]" id="{$gdpr_form}-form-gdpr-input-{$field->id}{uniqid()}" label="{t}sf.checkout.label.agree_terms{/t} <a href='{route('page', $field->url_handle)}' data-ajax-panel='true' data-modal-class='modal-lg'>{$field->name}</a>" required=!$field->optional}
                    </div>
                </div>
            {/foreach}
        {/foreach}
    {else}
        {foreach GDPR::getFormPolicies($gdpr_form) AS $field}
            <div class="cc-form-row">
                <div class="cc-form-col-12">
                    {include file=base_path("themes/_global/templates/form-components/checkbox.tpl") name="gdpr[{$field->id}]" id="{$gdpr_form}-form-gdpr-input-{$field->id}{uniqid()}" label="{t}sf.checkout.label.agree_terms{/t} <a href='{route('page', $field->url_handle)}' data-ajax-panel='true' data-modal-class='modal-lg'>{$field->name}</a>" required=!$field->optional}
                </div>
            </div>
        {/foreach}
    {/if}
{/if}
