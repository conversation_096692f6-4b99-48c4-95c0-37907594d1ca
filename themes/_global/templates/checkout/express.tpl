{capture "body_class"}cc-checkout-page{if $cc_cart->customer|default} js-body-reload{/if}{/capture}

{$hasFooter = setting('checkout_enable_footer') && !Request::ajax()}
{$hasAnimations = setting('checkout_animation')}
<main class="cc-checkout cc-checkout-style js-cc-checkout has-logo{if $hasFooter} has-footer{/if}{if $hasAnimations} has-animation{/if}">
    <div class="cc-checkout-header-mobile">
        {include file="./include/logo.tpl"}
    </div>

    <div class="cc-checkout-cart-button cc-stack" data-toggle-target=".js-checkout-sidebar-toggle"
         data-toggle-effect="slide">
        <span class="cc-checkout-cart-button-text cc-stack-main">
            <i class="fa fa-shopping-cart"></i> <span>{t}sf.widget.checkout.label.cart_details{/t}</span>
        </span>

        {include file=Illuminate\Support\Facades\View::make('global::cart.include.total-formatted')->getPath()}
    </div>
    {if $cc_cart|default && $cc_cart->getFreeShippingAmountLeft()}
        <div class="cc-box dashed warning cc-display-mobile {if Apps::installed('bumper_offer') && Apps::enabled('bumper_offer') && App::get('bumper_offer')->getSetting('goal', 'null') == 'free_shipping'} hidden {/if}">
            <span class="cc-amount-to-free-shipping">{sprintf(__('sf.widget.cart.totals.nfy.%s_left_to_free_shipping'), $cc_cart->getFreeShippingAmountLeft('formatted')) nofilter}</span>
        </div>
    {/if}

    <div class="cc-checkout-steps">
        <div class="cc-checkout-steps-inner">
            {include file="./include/logo.tpl"}

            <div class="js-checkout-container">
                {if Session::has('payment_canceled')}
                    <div class="cc-box dashed error center">
                        {Session::get('payment_canceled')}
                    </div>
                {/if}

                {foreach $steps AS $step}
                    {$step nofilter}
                {/foreach}
            </div>
        </div>
    </div>

    <div class="cc-checkout-sidebar js-checkout-sidebar-toggle">
        <div class="cc-checkout-sidebar-inner js-checkout-sidebar">
            {include file=Illuminate\Support\Facades\View::make('global::checkout.include.summary')->getPath() submit=false}
{*            {if Apps::installed('bumper_offer') && Apps::enabled('bumper_offer')}*}
{*                {$bumper_products = App\Models\System\AppsManager::getManager('bumper_offer')->getBumperProducts()}*}
{*                {if !empty($bumper_products)}*}
{*                    <div id="only_reload" class="_text-box-checkout {if segment(1) != 'cart'}hidden-xs hidden-sm hidden-md{/if}">*}
{*                        {include file="widgets/extra/bumper_offer.tpl" data=$bumper_products}*}
{*                    </div>*}
{*                {/if}*}
{*            {/if}*}
            {if $widget->has('checkoutText') && $widget->checkoutText->isEnabled()}
                <div class="_text-box _text-box-checkout">
                    {include file="widgets/extra/text.tpl" widget=$widget->checkoutText}
                </div>
            {/if}

            {if $widget->has('checkoutSideText') && $widget->checkoutSideText->isEnabled()}
                <div class="_text-box _text-box-checkout">
                    {include file="widgets/extra/text.tpl" widget=$widget->checkoutSideText}
                </div>
            {/if}
        </div>
    </div>
</main>

{if $hasFooter}
    {include file=base_path('themes/_global/templates/checkout/layout/footer.tpl')}
{/if}

<!-- Load the Drop-in component -->
<script src='https://js.braintreegateway.com/web/dropin/1.37.0/js/dropin.min.js'></script>
