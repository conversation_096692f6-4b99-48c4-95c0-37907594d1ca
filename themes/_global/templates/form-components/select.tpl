<div class="cc-form-group cc-form-group-select js-complex-field{if $value|default} filled{/if}{if $class|default} {$class}{/if}{if $required|default} required{/if}"{foreach $attributes|default:[] as $attribute_name => $attribute_value} {$attribute_name}="{$attribute_value}"{/foreach}>
    <div class="cc-form-field-container">
        {if $label|default}
            <div class="cc-form-field-label">
                <label for="{$id|default}">{$label}</label>
            </div>
        {/if}

        <select name="{$name|default}"
                class="cc-select {if $input_class|default} {$input_class}{/if}"
                id="{$id|default}"
                data-dd-class="cc-select-drop{if $class|default} {$class|default}-drop{/if}"
                data-uicontrol="select2"
                {foreach $input_attributes|default:[] as $attribute_name => $attribute_value}
                    {$attribute_name}="{$attribute_value}"
                {/foreach}
                {if $multiple|default} multiple{/if}
                {if $disabled|default} disabled{/if}
                {if $required|default} required{/if}>
            {if $placeholder|default}
                <option value="">{t}global.select{/t}</option>
            {/if}

            {foreach $options|default:[] as $option_value => $option_text}
                {if is_array($option_text)}
                    {foreach $option_text AS $group => $group_options}
                    <optgroup label="{$group}">
                        {foreach $group_options AS $key => $val}
                        <option value="{$key}" {if $key==$value|default} selected="selected"{/if} {if $option_disabled[$key]|default} disabled="disabled"{/if}>
                            {$option_text}
                        </option>
                        {/foreach}
                    </optgroup>
                    {/foreach}
                {else}
                    <option value="{$option_value}" {if $option_value==$value|default} selected="selected"{/if} {if $option_disabled[$option_value]|default} disabled="disabled"{/if} {if $input_attributes['data-type']|default == 'country'}data-flag="{$option_value}"{/if}>
                        {$option_text}
                    </option>
                {/if}
            {/foreach}
        </select>

        <span class="cc-form-field-note cc-form-field-note-required"></span>
        <span class="cc-form-field-note cc-form-field-note-warning"></span>
        <span class="cc-form-field-note cc-form-field-note-success"></span>
    </div>

    {if $description|default}
        <div class="cc-form-description">
            <p>{$description}</p>
        </div>
    {/if}
</div>