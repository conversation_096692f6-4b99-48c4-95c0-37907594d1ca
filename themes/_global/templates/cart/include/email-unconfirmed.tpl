{if !Request::ajax()}
    <div class="_modal modal fade" id="ConfirmMailModalJs" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="_popup">
                        <div class="_popup-title text-center">
                            <p>{t}sf.cart.warn.customer_email_not_yet_confirmed{/t}</p>
                        </div>

                        <div class="_form">
                            <div class="_form-row">
                                <div class="_form-col">
                                    <button class="_button _button-secondary _button-full" type="button" data-dismiss="modal">
                                        <span class="_figure-stack">
                                            <span class="_figure-stack-label">{t}sf.global.act.close{/t}</span>
                                        </span>
                                    </button>
                                </div>
                                <div class="_form-col">
                                    <a class="_button _button-full" href="{route('site.account.confirm.email')}" data-ajax="toast">
                                        <span class="_figure-stack">
                                            <span class="_figure-stack-label">{t}sf.global.act.request_new_confirm_link{/t}</span>
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{else}
	{t}sf.cart.warn.customer_email_not_yet_confirmed{/t}.

    <a href="{route('site.account.confirm.email')}" data-ajax="toast"><strong>{t}sf.global.act.request_new_confirm_link{/t}</strong></a>
{/if}