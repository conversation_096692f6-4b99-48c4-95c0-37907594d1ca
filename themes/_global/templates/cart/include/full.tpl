<div data-ajax-box="{route('cart.list', $cc_cart_key)}" data-widget="cart" data-effect="populate">
    {if $cc_cart|default && $cc_cart->has_products}
        <div class="container">
            <div class="row">
                <div class="col-md-{if $summary|default:true}8{else}12{/if}">
                    <div class="_cart-main">
                        <div class="_cart-products">
                            <form class="cart-form-js js-form-submit-ajax" id="cartForm" action="{route('cart.checkout')}" method="POST">
                                <div class="_cart-products-holder">
                                    <div class="_cart-products-list">
                                        {foreach $cc_cart->products as $product}
                                            {strip}
                                                <div class="_cart-product" id="variant-{$product->variant_id}" data-error-position="append">
                                                    <div class="_cart-product-details">
                                                        <div class="_cart-product-info">
                                                            <div class="_cart-product-image">
                                                                <div class="_cart-product-image-thumb">
                                                                    <a class="_cart-product-image-thumb-holder" href="{$product->url()}" title="{$product->name}|{$product->category->name|default}">
                                                                        <img src="{$product->getImage('300x300')}" alt="{$product->name}" title="{$product->name}">
                                                                    </a>
                                                                </div>
                                                            </div>

                                                            <div class="_cart-product-box">
                                                                <div class="_cart-product-name">
                                                                    <h4>{$product->name}</h4>

                                                                    {if $product->unit_main_total_value_formatted}
                                                                        <div class="_text-left">
                                                                            <span class="_button _button_unit">
                                                                                {$product->unit_main_total_value_formatted}
                                                                            </span>
                                                                        </div>
                                                                    {/if}

                                                                    {if $product->max_quantity_reached}
                                                                        <span class="help-block-error">{$product->max_quantity_reached}</span>
                                                                    {/if}
                                                                </div>

                                                                {if $product->hasParameters() || $product->getWeight() || $product->hasOptions() || $product->bundle_products}
                                                                    <div class="_cart-product-parameters">
                                                                        {foreach $product->getParameters() AS $parameter}
                                                                            <span class="_cart-product-parameter">
                                                                                <span class="_cart-product-parameter-name">{$parameter['name']}:</span> <span class="_cart-product-parameter-value">{$parameter['value'] nofilter}</span>
                                                                            </span>
                                                                        {/foreach}

                                                                        {foreach $product->getOptions() AS $option}
                                                                            <span class="_cart-product-parameter">
                                                                                <span class="_cart-product-parameter-name">{$option->getName()}:</span> <span class="_cart-product-parameter-value">{if $option->isFile()}<a href="{$option->getFile()->getUrl()}">{$option->getFile()->getName()}</a>{else}{$option->getValueFormatted() nofilter}{/if} {if !$option->isPerItem() && "{$option->getPrice()->getPrice() > 0 || $option->getDiscountedPrice()->getPrice() > 0}"} (+ {money("{if $product->hasDiscounts()}{$option->getDiscountedPrice()->getPrice()}{else}{$option->getPrice()->getPrice()}{/if}") nofilter}){/if}</span>
                                                                            </span>
                                                                        {/foreach}

                                                                        {if $product->getWeight()}
                                                                            <span class="_cart-product-parameter">
                                                                                {weightInput($product->getWeight())}
                                                                            </span>
                                                                        {/if}
                                                                        {if $product->bundle_products}
                                                                            {foreach $product->bundle_products AS $bp}
                                                                                {if $bp->setting('visible_cart')}
                                                                                    {include file=base_path('themes/_global/templates/cart/include/bundle-details.tpl') product = $bp}
                                                                                {/if}
                                                                            {/foreach}
                                                                        {/if}
                                                                    </div>
                                                                {/if}

                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="_cart-product-quantity">
                                                        {if isset($quantity_format)}
                                                            <span class="_cart-product-quantity-total">{$product->quantity}</span>
                                                        {else}
                                                            {if $product->allow_quantity_change}
                                                                <input class="_field" type="text" name="cart[variants][{$product->key}]" value="{$product->quantity}" data-uicontrol="spinner" data-options="{json_encode(["verticalbuttons" => true, "verticalupclass" => "glyphicon glyphicon-plus", "verticaldownclass" => "glyphicon glyphicon-minus"])}"{if $product->tracking == 'yes' && $product->continue_selling == 'no'} data-spinner-max="{$product->product_quantity}"{/if}>
                                                            {else}
                                                                <input type="hidden" name="cart[variants][{$product->key}]" value="{$product->quantity}">
                                                            {/if}
                                                            {if $product->tracking == 'yes' && $product->continue_selling == 'no'}
                                                                <span class="_cart-product-outofstock glyphicon glyphicon-info-sign tooltips" title="{if empty($product->quantity)}{t}sf.widget.cart.product.nfy.out_of_stock{/t}{/if}">
                                                                   {if empty($product->product_quantity)}
                                                                       {t}sf.widget.cart.product.nfy.out_of_stock{/t}
                                                                   {/if}
                                                                </span>
                                                            {/if}
                                                        {/if}
                                                    </div>

                                                    <div class="_cart-product-total-price">
                                                        {if $product->isHideDiscountedPrice()}
                                                            {if $product->hasDiscountCode()}
                                                                {money($product->getTotalPriceWithOptionsAfterDiscountsWithModification()) nofilter}
                                                            {else}
                                                                {money($product->getTotalPriceWithOptionsAfterGlobalDiscountWithModification()) nofilter}
                                                            {/if}
                                                        {else}
                                                            {if $product->hasDiscounts()}
                                                                {money($product->getTotalPriceWithOptionsAfterDiscountsWithModification()) nofilter}
                                                            {elseif $product->hasDiscountsModifications()}
                                                                {money($product->getTotalPriceWithOptionsWithModification()) nofilter}
                                                            {else}
                                                                {money($product->getTotalPriceWithOptions()) nofilter}
                                                            {/if}
                                                        {/if}
                                                    </div>

                                                    <a class="_remove js-cart-product-remove" href="{route('cart.remove', $product->key)}" data-ajax="toast">
                                                        {t}sf.global.remove_label{/t}
                                                    </a>
                                                </div>
                                            {/strip}
                                        {/foreach}
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                {if $summary|default:true}
                    <div class="col-md-4">
                        <div class="cc-cart-sidebar">
                            <div class="cc-cart-sidebar-inner">
                                {include file=Illuminate\Support\Facades\View::make('global::checkout.include.summary')->getPath() title="{t}sf.cart.header.summary{/t}" products=false}
                            </div>
                        </div>
                    </div>
                {/if}
            </div>
        </div>
    {else}
        <div class="container">
            <div class="_section-separator">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="_notification">
                            <p>{t}sf.widget.cart.nfy.cart_is_empty{/t}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {/if}
</div>
