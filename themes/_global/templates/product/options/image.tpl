<div class="_form-row _form-row-variant js-no-scroll js-parameter-custom-option">
    <div class="_form-col">
        <div class="_parameter _parameter-image _parameter-radio clearfix {inputJsError("option-{$field->id}", $input_prefix|default)}">
            {if isset($printLabels)}
                <div class="_parameter-label _parameter-radio-label">
                    <label class="_label">{$field->storefront_name|default:$field->name}:</label>
                </div>
            {/if}

            <div class="_parameter-values _parameter-image-values _parameter-radio-values">
                {foreach $field->options as $option}
                    <div class="_parameter-value _parameter-image-value w-10 _parameter-radio-value _tooltips">
                        <span class="_tooltip">
                            <span class="_tooltip-inner">
                                {$option->name_amount nofilter}<br>
                                <img src='{$option->getImage('300x300')}' title="{strip_tags($option->name_amount)}">
                            </span>
                        </span>
                        <label class="_radio{if $option->hasImage()} _radio-has-image{/if}" style="padding: 0 0 100%;">
                            <input type="radio" name="{inputPrefix("option[{$field->id}]", $input_prefix|default)}"
                                   value="{$option->id}"
                                   data-uicontrol="uniform"
                                   data-amount="{if !empty($option->amount)}{$option->amount}{else}0{/if}"
                                   data-amount-type="{$field->amount_type|default}"
                                   data-per-item="{$field->per_item|default:0}"
                                   data-apply-over-price-type="{$field->apply_over_price_type|default:'discounted'}">
                            {if $option->hasImage()}
                                <img src="{$option->getImage('150x150')}" title="{strip_tags($option->name_amount)}">
                            {else}
                                {$option->name_amount nofilter}
                            {/if}
                        </label>
                    </div>
                {/foreach}
            </div>
        </div>
    </div>
</div>

{capture append="js"}
    <style>
        ._tooltips._inline {
            display: inline-block;
        }
        ._tooltip._bottom {
            bottom: auto !important;
            top: calc(100% + 16px) !important;
        }
        ._tooltip._bottom ._tooltip-inner:after, ._tooltip-inner:before {
            /*border-bottom-color: #51546c;*/
            border-top-color: transparent;
            top: -16px !important;
        }
        ._tooltip._bottom ._tooltip-inner:before {
            border-bottom-color: #51546c;
            border-width: 8px 8px 8px;
        }

        ._tooltip._bottom ._tooltip-inner:after {
            border-width: 7px 7px 9px;
            border-top-color: transparent;
            border-bottom-color: #212331;
        }
        ._parameter-image-value.w-10 {
            width: 9%;
        }
    </style>
{/capture}