<div class="_form-row _form-row-variant js-no-scroll js-parameter-custom-option" {if $field->product_label}data-geometry-product-label="{$field->product_label}"{/if} data-product-symbol="{$field->symbol_product}" data-value-symbol="{$field->symbol_value}" data-type="{$field->type}" data-min-size="{$field->min_square}">
    <div class="_form-col">
        <div class="_parameter _parameter-select {inputJsError("option-{$field->id}", $input_prefix|default)}">
            <div class="_parameter-label _parameter-select-label">
                <label class="_label">{$field->name_amount nofilter} ({$field->symbol}):</label>
            </div>

            <div class="_parameter-values _parameter-select-values">
                <input type="number" step="{$field->step}" min="{$field->min}" class="_field js-option-geometry-input" name="{inputPrefix("option[{$field->id}]", $input_prefix|default)}" value="{$field->default_value}" data-uicontrol="spinner" data-spinner-max="1000000" data-options="{json_encode(['min' => $field->min,'step' => $field->step])}">
            </div>
        </div>
    </div>
</div>