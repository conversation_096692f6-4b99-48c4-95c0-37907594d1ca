<style>
    body, html {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
    }

    html {
        min-height: 100%;
    }

    .cloudcart-embed-cart-panel {
        background-color: #fff;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
        height: 100%;
        width: calc(100% - 45px);
        position: absolute;
        right: 0;
        overflow: auto;
    }

    .cloudcart-embed-cart-panel > div {
        padding: 20px;
        width: 100%;
    }

    .side-panel-close {
        left: 3px;
    }

    .side-panel-close:hover {
        transform: scale(1.2);
    }

    ::-webkit-scrollbar {
        /*width: 0;*/
    }

    .fb_dialog {
        display: none !important;
    }

    .cc-cart-product-image-thumb-holder {
        cursor: auto;
    }

    .cc-button.loading, .cc-button.loading:hover {
        color: transparent !important;
    }

    .cc-summary-submit .cc-button {
        font-size: 16px;
    }

    .cc-form-field.form-control:focus, .cc-form-field:focus {
        border: 1px solid #ccc;
    }

    .cc-form-col-6 ._button._checkout_button {
        width: 100%;
    }

    @media only screen and (max-device-width: 767px) {
        .side-panel-close {
            left: inherit;
            right: 18px;
            color: #ccc;
            text-shadow: none;
        }
        .cloudcart-embed-cart-panel {
            width: 100%;
        }
    }
</style>
{include './css/parameters_styles.tpl'}
{include './css/theme_styles.tpl'}
<button type="button" class="side-panel-close" data-dismiss="panel">✖</button>
<div class="cloudcart-embed-cart-panel">
    {include './include/_cart-panel.tpl'}
    {if $cc_cart|default && $cc_cart->has_products}
    <div class="cc-summary-submit">
        <div class="cc-form-actions">
            <div class="cc-form-row">
                <div class="cc-form-col-6">
                </div>
                <div class="cc-form-col-6">
                    {*<a href="{route('embed.checkout')}" target="_blank" type="button" class="_button btn-block">*}
                        {*<span class="_figure-stack">*}
                            {*<span class="_figure-stack-label" style="font-size: {$query['font_size']}px;">{t}sf.widget.cart.act.checkout{/t}</span>*}
                        {*</span>*}
                    {*</a>*}
                    {include "./include/checkout_button.tpl"}
                </div>
            </div>
        </div>
    </div>
    {/if}
</div>
<div id="cc-cart-count" style="display: none;">
    {include file="./cart.tpl"}
</div>
<script>
    $(function () {
        window.onload = function () {
            parent.postMessage({
                type: 'cart-count',
                html: $('#cc-cart-count').html()
            }, '*');
        }

        $('.side-panel-close').on('click', function () {
            parent.postMessage({
                type: 'panel-close'
            }, '*');
        });

        $('.js-cart-product-remove').on('cc.ajax.success', function () {
            parent.postMessage({
                type: 'panel-reload'
            }, '*');
        });

        $('#cartForm').on('cc.ajax.success', function () {
            parent.postMessage({
                type: 'panel-reload'
            }, '*');
        });

        window.addEventListener('message', function (event) {
            if (event.data.type === 'loading') {
                CCHelper.addLoader($('.cloudcart-embed-cart-panel'));
            }
        });
    });
</script>