<div class="_product-sidebar js-product-details-sidebar">
{if $query['template'] != 'button'}
	{*<div class="_product-details-title xs-hidden">*}
		{*<h2 class="js-product-title">{$product->name}</h2>*}
	{*</div>*}

	<div class="_product-details-price product-details-price-js{if $product->price_from_discounted} has-discount{/if}">
		{if W::ProductsDetails('show_price') && showPriceForUser()}
			<div class="_product-details-price-bar">
                {if $product->is_bundle}
                    {$price = $product->price_from_formatted}
                    {$price_old = $product->products_price_from_formatted}
                    {$price_save = money($product->products_price_from - $product->price_from)}
                {else}
                    {if !is_null($product->price_from_discounted)}
                        {$price = $product->price_from_discounted_formatted}
                        {$price_old = $product->price_from_formatted}
                        {$price_save = $product->price_from_saved_formatted}
                    {else}
                        {$price = $product->price_from_formatted}
                    {/if}
                {/if}

				<span class="_product-details-price-new price-new-js">{$price}</span>
				<span class="_product-details-price-old price-old-js"><i class="rtl-ltr">{$price_old|default}</i></span>
			</div>
		{/if}

		{if $query['template'] != 'button' && isset($query['display_sku']) && W::ProductsDetails('show_SKU')}
			<div class="_product-details-sku variant-sku-js hide">{t}sf.global.label.sku{/t} <i></i></div>
		{/if}
	</div>

	{if W::ProductsDetails('show_product_status')}
		<span class="_product-details-stock-status-bar js-status-bar {if $product->total_variants}hide{/if} {$product->status_type|replace:'_':'-'}" data-status="{$product->status_type|replace:'_':'-'}">{$product->status_name}</span>
	{/if}
{/if}


	{if $query['template'] == 'enhanced'}
		{$hasFiles = $product->publicFiles && $product->publicFiles->count()}
		{$hasCategory = W::ProductsDetails('show_category') && !empty($product->category)}
		{$hasVendor = W::ProductsDetails('show_brand') && !empty($product->vendor)}
		{$hasPage = W::ProductsDetails('show_page') && W::ProductsDetails()->getInformationPage()}
		{$hasMeta = $hasCategory || $hasVendor || $hasPage}
		{if $hasMeta}
			<div class="_product-details-meta">
				<ul>
					{if !empty($product->vendor) && W::ProductsDetails('show_brand')}
						<li>
							<span class="_product-details-meta-title">{t}sf.global.label.vendor{/t}</span>

							<span class="_product-details-meta-value">
								<a href="{$product->vendor->url()}">
									{$product->vendor->name}
								</a>
							</span>
						</li>
					{/if}

					{if $product->publicFiles && $product->publicFiles->count()}
						{foreach from=$product->publicFiles item=file}
							<li>
								<span class="_product-details-meta-title">{t}sf.global.files.for.download{/t}</span>
								<span class="_product-details-meta-value"><a target="_blank" href="{Linker::product_file($product->url_handle, $file->mask)}" >{$file->name}</a></span>
							</li>
						{/foreach}
					{/if}

					{if W::ProductsDetails('show_page')}
						{$page = W::ProductsDetails()->getInformationPage()}
						{if $page}
							<li>
								{if W::ProductsDetails('show_link_as_popup')}
									{$popup_url = $page->url()}
									<span class="_product-details-meta-value"><a href="{$popup_url}" data-ajax-panel="true">{$page->name}</a></span>
								{else}
									{$page_url = $page->url()}
									<span class="_product-details-meta-value"><a href="{$page_url}">{$page->name}</a></span>
								{/if}
							</li>
						{/if}
					{/if}
				</ul>
			</div>
		{/if}

		{if $product->countDownDetails}
			<div class="_product-details-countdown">
				{include file="widgets/common/countdown.tpl" endDate="{$product->countDownDetails}"}
			</div>
		{/if}
	{/if}

	<div class="_product-details-choose choose-variant-msg-js hide">{t}sf.global.nfy.choose_variant_first_pls{/t}</div>

	<div class="_product-details-parameters">
		{include file="./choose_variant.tpl" quantity_uicontrol="spinner" select_uicontrol="select2" printLabels=true tooltips=true}
	</div>

	{if 0 && !Request::ajax() && W::leasing()->getActivePaymentProvidersForProduct($product)->count()}
		<div class="_product-details-leasing-button">
			<a id="credit-calculator-js" href="{route('creditor.select', $product->id)}" data-url="{route('creditor.select', $product->id)}" data-ajax-panel>
				<span class="_figure-stack">
					<span class="_figure-stack-label">{t}sf.leasing.btn.calculator{/t}</span>
				</span>
			</a>
		</div>
		<div class="_textbox">
			{W::leasing()->getPromoHtml($product) nofilter}
		</div>
		<div class="credtor_button">
			{W::leasing()->getPromoButton($product) nofilter}
		</div>
		{foreach $product->labels|default:[] AS $label}
			{if !empty($label->description)}
				<div class="_textbox">{$label->description nofilter}</div>
			{/if}
		{/foreach}
		{foreach $product->banners|default:[] AS $_banner}
			{if !empty($_banner->description)}
				<div class="_textbox">{$_banner->description nofilter}</div>
			{/if}
		{/foreach}

		{include file="_global/templates/product/discounts/volume.tpl" product=$product}

		{$productText = $widget->productText}
		{if $productText->isEnabled()}
			<div class="_product-details-text" style="margin-bottom: 20px;">
				{include file="widgets/extra/text.tpl" widget=$productText}
			</div>
		{/if}
	{/if} {* end if 0 *}

	{if $query['template'] != 'button' && isset($query['display_properties']) && W::ProductsDetails('show_categories_characteristics') && !W::categoryProperties()->getPropertiesForProduct($product)->isEmpty()}
		{include file="./category_properties.tpl" properties=W::categoryProperties()->getPropertiesForProduct($product)}
	{/if}

</div>

{if $query['template'] == 'enhanced' && isset($query['display_description']) && !empty($product->description) && W::ProductsDetails('show_product_description')}
	<div class="_product-details-description">
		<div class="_product-details-description js-tabs">
			<div class="_product-details-description-tabs-nav">
				<a class="_product-details-description-tabs-nav-link js-tabs-link" href="#product-details-description">{$product->description_title|default:"{t}sf.global.label.description{/t}"}</a>

				{if $product->tabs->count()}
					{foreach $product->tabs AS $dinamic_tab}
						<a class="_product-details-description-tabs-nav-link js-tabs-link" href="#product-details-dynamic-tab-{$dinamic_tab->id}">{$dinamic_tab->name}</a>
					{/foreach}
				{/if}
			</div>

			<div class="_product-details-description-tabs">
				<div class="_product-details-description-tab js-tab" id="product-details-description">
					<div class="_textbox" data-article-content data-language="{locale()}">
						{$product->description nofilter}
					</div>
				</div>

				{if $product->tabs->count()}
					{foreach $product->tabs AS $dinamic_tab}
						<div class="_product-details-description-tab js-tab" id="product-details-dynamic-tab-{$dinamic_tab->id}" style="display: none;">
							<div class="_textbox" data-article-content data-language="{locale()}">
								{$dinamic_tab->description nofilter}
							</div>
						</div>
					{/foreach}
				{/if}
			</div>
		</div>
	</div>
{/if}
