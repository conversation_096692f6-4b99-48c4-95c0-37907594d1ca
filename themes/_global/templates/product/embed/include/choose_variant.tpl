{function name="add_to_cart_button"}
    {if showPriceForUser()}
        <div class="_form-row _form-row-actions js-addtocart-actions">
            {$should_show_quantity =  W::ProductsDetails('choose_quantity')}
            <div class="_form-col _form-col-quantity{if !$should_show_quantity || !isset($query['display_quantity'])} hidden{/if}">
                {if isset($printLabels)}
                    <label class="_label">{t}sf.widget.choose_variant.quantity_label{/t}:</label>
                {/if}

                <input class="_field" data-uicontrol="{$quantity_uicontrol}"{if !empty($quantity_uicontrol_options)}
                data-options="{$quantity_uicontrol_options}"{/if} type="text"
                       name="quantity" value="1"
                       data-continue-selling="{$product->continue_selling}"{if !$should_show_quantity} disabled{/if} />
            </div>

            <div class="_form-col _form-col-button {if isset($printLabels)}_form-col-bottom{/if}"{if $query['action'] == 'checkout'} style="display: none;"{/if}>
                <div class="_form-actions">
                    {if W::ProductsDetails('details_show_buy')}
                    <button class="_button _button-full js-disabled js-loading submit-js"
                            data-product-id="{$product->id}" type="submit" disabled>
                        <span class="_figure-stack">
                            <span class="_figure-stack-label" style="font-size: {$query['font_size']}px;">
                                {if $query['action'] == 'cart'}
                                {t}embed.add_to_cart{/t}
                                {else}
                                {t}sf.widget.cart.act.checkout{/t}
                                {/if}
                            </span>
                        </span>
                    </button>
                    {/if}
                </div>
            </div>
        </div>
    {/if}

    {if $product->digital == 'no'}
        <div class="_form-row _form-row-out-of-stock quantity-left-js out-of-stock-js hide">
            <div class="_form-col">
                <div class="_form-actions">
                    <span class="_product-out-of-stock">{t}sf.global.label.out_of_stock{/t}</span>
                </div>
            </div>
        </div>

        <div class="_form-row js-request-status hide"{if $query['action'] == 'checkout'} style="display: none;"{/if}>
            <div class="_form-col">
                <span class="_button _button-full js-disabled js-loading"
                        data-url="{Linker::contacts($product->url_handle)}"
                        data-original-href="{Linker::contacts($product->url_handle)}"
                        data-ajax-modal="true"
                        rel="noindex nofollow">
                    <span class="_figure-stack">
                        <span class="_figure-stack-label js-request-status-label">{$product->status_button}</span>
                    </span>
                </span>
            </div>
        </div>
    {/if}
{/function}

{if $product->total_variants && $product->status_type === 'out_stock'}
    <div class="_form-row _form-row-out-of-stock" style="display: block">
        <div class="_form-col">
            <div class="_form-actions">
                <span class="_product-out-of-stock">{t}sf.global.label.out_of_stock{/t}</span>
            </div>
        </div>
    </div>
{elseif $product->is_imos3d}
    {include file=base_path('modules/Apps/Administration/Imos3d/resources/views/product/sf-modal.tpl') product=$product}
{elseif !$product->is_bundle}
    <input type="hidden" name="product_id" value="{$product->id}">
    <div class="_form{if $tooltips|default} js-has-tooltips{/if} js-product-details-plugin"
         data-product-parameters="{$product->total_variants|default:0}" data-variant-pre-selected="{W::ProductsDetails('pre_selected_variant')|default}" data-variant-default="{CatalogProduct::getDefaultOrSelectedVariantId($product)}" data-variant-campaign="{CatalogProduct::getCampaignVariant($product)}" data-variant-selection="{CatalogProduct::getVariantsJson($product)}" data-product-id="{$product->id}">
        <input class="js-variant-chosen-js" type="hidden" name="variant_id" value="" />
        {include file="widgets/product/details/variants_type_generate.tpl" tooltips=$tooltips|default}

        {Messenger::detailsPagePlugin() nofilter}

        {if $query['template'] != 'button'}
        {include file="_global/templates/product/discounts/quantity.tpl" product=$product}
        {/if}
        {include './submit_button.tpl'}
    </div>
{else}
    <div class="_form">
        {include './submit_button.tpl'}
    </div>
{/if}
{capture append='js'}
    <script>
        $(function () {
            $('._cart-submit ._button').on('click', function (e) {
                e.preventDefault();
                $('._button._button-full').trigger('click');
            });
        });
    </script>
{/capture}

