{if $product->is_bundle}
    {$parameters = false}
    <div class="_bundle _form{if $tooltips|default} js-has-tooltips{/if} js-bundle-products-holder js-product-details-plugin" data-bundle-variant="{CatalogProduct::getVariants<PERSON>son($product)}" data-bundle-id="{$product->id}">
        <input type="hidden" name="bundle_id" value="{$product->id}">
        <div class="_bundle-items">
            {foreach CatalogProduct::getBundleProducts($product) AS $item}
                <div class="_bundle-item js-bundle-product {$product->class_name}" data-product-parameters="{$item->total_variants}" data-variant-pre-selected="{W::ProductsDetails('pre_selected_variant')|default}" data-bundle-variant-default="{CatalogProduct::getDefaultOrSelectedVariantId($item)}" data-bundle-variant-selection="{CatalogProduct::getVariants<PERSON><PERSON>($item)}" data-product-id="{$item->id}">
                    <input type="hidden" name="bundle[{$item->id}][product_id]" value="{$item->id}">
                    <input class="js-variant-chosen" type="hidden" name="bundle[{$item->id}][variant_id]" value="">

                    <div class="_bundle-item-inner">
                        {if !$item->bundle_hide_thumb && $query['template'] != 'button'}
                            <div class="_bundle-item-image{if $item->bundle_hide_thumb}-placeholder{/if}">
                                {include file="widgets/product/details/primary_image.tpl" zoom_uicontrol="jqueryZoom" custom_labels=true image_size="1280x1280" product=$item}
                            </div>
                        {/if}

                        {if $item->visible_product_details}
                            <div class="_bundle-item-info">
                                <div class="_bundle-item-title">
                                    <h4>{$item->name}</h4>
                                </div>

                                {if !empty($item->short_description) && W::ProductsDetails('short_product_description')}
                                    <div class="_bundle-item-short-description _textbox">
                                        {$item->short_description nofilter}
                                    </div>
                                {/if}

                                {if Widget::get('product.productsDetails')->getSetting('show_price') && $item->bundle_price_visible_product_details}
                                    <div class="_bundle-item-price price-new-js">
                                        {if $item->price_from_discounted}
                                            {$item->price_from_discounted_formatted nofilter}
                                        {else}
                                            {$item->price_from_formatted}
                                        {/if}
                                    </div>
                                {/if}
                            </div>
                        {/if}
                        {if !CatalogProduct::getParameters($item)->isEmpty()}
                            {$parameters = true}
                        {/if}
                        {if !CatalogProduct::getParameters($item)->isEmpty() || !CatalogProduct::getProductOptions($item)->isEmpty()}
                            <div class="_bundle-item-parameters">
                                {include file="widgets/product/details/variants_type_generate.tpl" product=$item tooltips=$tooltips|default input_prefix="bundle.{$item->id}" printLabels=true}
                            </div>
                        {/if}
                    </div>
                </div>
            {/foreach}
        </div>

        {if $display_button|default:true}
            <div class="_bundle-actions js-addtocart-actions{if $parameters} hide{/if}">
                {$show_quantity = W::ProductsDetails('choose_quantity')}
                <div class="_bundle-quantity{if !$show_quantity} hidden{/if}">
                    <input class="_field" data-uicontrol="{$quantity_uicontrol}"{if !empty($quantity_uicontrol_options)} data-options="{$quantity_uicontrol_options}"{/if} type="text" name="quantity" value="1" data-continue-selling="{$product->continue_selling}"{if !$show_quantity} disabled{/if}>
                </div>

                <div class="_bundle-button">
                    {if isset($modal) && !empty($modal) && isset($button)}
                        <a class="_button js-loading" href="{$button}">
                            <span class="_figure-stack">
                                <span class="_figure-stack-label">{t}sf.global.act.view_details{/t}</span>
                            </span>
                        </a>
                    {/if}

                    <button class="_button js-loading submit-js" data-product-id="{$product->id}" type="submit" disabled>
                        <span class="_figure-stack">
                            <span class="_figure-stack-label">{t}embed.add_to_cart{/t}</span>
                        </span>
                    </button>
                </div>
            </div>


            <div class="_form-row js-request-status hide">
                <div class="_form-col">
                    <span class="_button _button-full js-disabled js-loading" data-url="{Linker::contacts($product->url_handle)}" data-original-href="{Linker::contacts($product->url_handle)}" data-ajax-modal="true" rel="noindex nofollow">
                        <span class="_figure-stack">
                            <span class="_figure-stack-label js-request-status-label">{$product->status_button}</span>
                        </span>
                    </span>
                </div>
            </div>
        {/if}
    </div>
{/if}