{assign var="rating" value=$product->meta->get('average_rating')|floatval|number_format:1:'.':''}
{assign var="average" value=$product->meta->get('average_rating')|floatval|round}
{assign var="total" value=$product->meta->get('total_reviews')}
{assign var="summary" value=[
'rating' => $rating,
'average' => $average,
'total' => $total
]}

{$setting_is_hidden = \App\Models\System\AppsManager::getManager('product_review')->getSetting('hidden_rating', 0)}
<div class="{if $setting_is_hidden == 1 && $summary['rating'] == 0}hidden{/if}">
    {if $is_js}
        <a href="javascript:;" class="js-open-comments">
            <input class="rating" type="hidden">
        </a>
        {*        <script src="{{$img_url}}site/js/plugins/simple-rating.js"></script>*}
        <script type="text/javascript">
            $(document).ready(function () {
                var average = '';
                var total = '';
                var rating_text = '';
                var reviews = '{t}sf.review.reviews{/t}';
                {if $average}
                average = {$summary['average']};
                total = {$summary['total']};
                rating_text = '{$summary['rating']}';
                {/if}
                {literal}
                options = {
                    value: average,
                    locked: true,
                    msg: "<b>" + rating_text + "<b> ( <b>" + total + " " + reviews + "</b> )"
                };
                {/literal}
                {literal}
                options_total = {
                    value: average,
                    locked: true,
                    msg: "<b><small class='reviews-rating-count'>" + total + reviews + " </small></b>"
                };
                {/literal}
                {if $summary}
                // $('.rating').rating(options);
                $('.rating').map(function () {
                    let $this = $(this);
                    if (!$this.next('.simple-rating').length) {
                        $this.rating(options);
                    }
                });
                // $('.js-rating-total').rating(options_total);
                $('.js-rating-total').map(function () {
                    let $this = $(this);
                    if (!$this.next('.simple-rating').length) {
                        $this.rating(options_total);
                    }
                });

                {else}
                // $('.rating').rating();
                $('.rating').map(function () {
                    let $this = $(this);
                    if (!$this.next('.simple-rating').length) {
                        $this.rating();
                    }
                });
                {/if}
                $('.js-review-rating').text(rating_text);

                $(document).on('click', '.js-open-comments', function () {
                    let $ratingsTrigger = $('.js-ratings-trigger');
                    $ratingsTrigger.trigger('click');
                    {literal}
                    $ratingsTrigger[0].scrollIntoView({behavior: "smooth", inline: "nearest"});
                    {/literal}
                });
            });
        </script>
    {else}
    {for $x = 1; $x <= 5; $x++}
    {if $summary['rating'] >= $x}
        <i class="fa fa-star manual"></i>
    {else}
        <i class="fa fa-star-o manual"></i>
    {/if}
    {/for}
        ({{$summary['rating']}})

    {/if}
</div>
