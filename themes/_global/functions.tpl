{function name="widgets_css"}
    <!-- BEGIN WIDGETS SPECIFIC STYLES INJECT -->
    {foreach $widget->getDependency('css') as $item}
        <link href="{$item}" rel="stylesheet" type="text/css">
    {/foreach}
    <!-- <PERSON><PERSON> WIDGETS SPECIFIC STYLES INJECT -->
{/function}

{function name="page_css"}
    <!-- BEGIN PAGE SPECIFIC STYLES INJECT -->
    {if !empty($dependency)}
        {foreach $dependency as $item}
            {$item nofilter}
        {/foreach}
    {/if}
    <!-- END PAGE SPECIFIC STYLES INJECT -->
{/function}

{function name="translations"}
    <script type="text/javascript">
        var Translations = {
            'choose_country': '{lang('sf.global.label.choose_country')}',
            'choose_county': '{lang('sf.global.label.choose_county')}',
            'choose_city': '{lang('sf.global.label.choose_city')}',
            'choose_region': '{lang('sf.global.label.choose_region')}',
            'Confirm': '{lang('sf.global.act.confirm')}',
            'Cancel': '{lang('sf.global.act.cancel')}',
            'Quantity_left': '{lang('sf.global.label.quantity_left')}',
            'In_cart': '{lang('sf.global.nfy.already_in_cart')}',
            'continue_shopping': '{lang('sf.global.act.continue_shopping')}',
            'checkout': '{lang('sf.global.act.checkout')}',
            'added_to_cart': '{lang('sf.global.succ.item_added_to_cart')}',
            'In_your_cart': '{lang('sf.global.label.in_your_cart')}',
            'pagging_first': '{lang('sf.global.pagging.first')}',
            'pagging_previous': '{lang('sf.global.pagging.previous')}',
            'pagging_next': '{lang('sf.global.pagging.next')}',
            'pagging_last': '{lang('sf.global.pagging.last')}',
            'select': '{lang('sf.global.select')}'
        };
    </script>
{/function}

{function name="widgets_js"}
    <!-- BEGIN WIDGETS SPECIFIC PLUGINS INJECT -->
    {foreach $widget->getDependency('js') as $item}
        <script type="text/javascript" src="{$item}"></script>
    {/foreach}
    <!-- END WIDGETS SPECIFIC PLUGINS INJECT -->
{/function}

{function name="page_js"}
    <!-- BEGIN PAGE SPECIFIC PLUGINS INJECT -->
    {if !empty($js)}
        {foreach $js as $item}
            {$item nofilter}
        {/foreach}
    {/if}
    <!-- END PAGE SPECIFIC PLUGINS INJECT -->
{/function}