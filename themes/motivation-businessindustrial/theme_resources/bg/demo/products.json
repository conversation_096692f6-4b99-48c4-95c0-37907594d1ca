[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 1, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-9", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Размер", "p2": null, "p3": null, "p1_id": 1, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добави повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 5, "vendor_id": null, "image_id": 1, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 1920, "price_to": 1920, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 3, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 3, "v1": "S", "v2": null, "v3": null, "v1_id": 1, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 1920, "delivery_price": null, "weight": 1000}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 4, "v1": "M", "v2": null, "v3": null, "v1_id": 2, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 1920, "delivery_price": null, "weight": 1000}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 5, "v1": "L", "v2": null, "v3": null, "v1_id": 3, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 1920, "delivery_price": null, "weight": 1000}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 6, "v1": "XL", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 1920, "delivery_price": null, "weight": 1000}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 1, "name": "Медицински ръкавици 100бр.", "parent_id": 1, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "medicinski-r<PERSON><PERSON>i-100br-image_61f78d2577f7f.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 1, "color_id": 146, "percents": 68, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 2, "color_id": 78, "percents": 10, "R": 135, "G": 206, "B": 250, "hex": "#87CEFA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 155182}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 2, "name": "Медицински ръкавици 100бр.", "parent_id": 1, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "medicinski-r<PERSON><PERSON><PERSON>-100br-image_61f78d2dc7e81.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 3, "color_id": 146, "percents": 80, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 4, "color_id": 14, "percents": 5, "R": 95, "G": 158, "B": 160, "hex": "#5F9EA0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 135886}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 1, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 2, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 3, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 25, "parameter_id": 1, "parameter_option_id": 1, "variant_id": 3}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 26, "parameter_id": 1, "parameter_option_id": 2, "variant_id": 4}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 27, "parameter_id": 1, "parameter_option_id": 3, "variant_id": 5}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 28, "parameter_id": 1, "parameter_option_id": 10, "variant_id": 6}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-8", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добави повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 5, "vendor_id": null, "image_id": 3, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "yes", "price_from": 1000, "price_to": 1000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 8, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 8, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 1000, "delivery_price": null, "weight": 1000}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 3, "name": "Хипоалергенна медицинска маска 50бр.", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "hipoalergenna-medicinska-maska-50br-image_61f7901106015.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 5, "color_id": 146, "percents": 80, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 6, "color_id": 14, "percents": 5, "R": 95, "G": 158, "B": 160, "hex": "#5F9EA0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 136006}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 4, "name": "Хипоалергенна медицинска маска 50бр.", "parent_id": 2, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "hipoalergenna-medicinska-maska-50br-image_61f7901cf3e01.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 7, "color_id": 146, "percents": 86, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 70628}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 3, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-7", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добави повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 5, "vendor_id": null, "image_id": 6, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 780, "price_to": 780, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 11, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 11, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 780, "delivery_price": null, "weight": 1000}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 6, "name": "Дезинфектант за ръце", "parent_id": 3, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "dezinfektant-za-race-image_61f7908dd6de1.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 9, "color_id": 146, "percents": 84, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 164710}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 5, "name": "Дезинфектант за ръце", "parent_id": 3, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "dezinfektant-za-race-image_61f7908b39ed0.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 8, "color_id": 146, "percents": 86, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 70460}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 7, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 8, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 9, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 4, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-6", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добави повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": null, "image_id": 7, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "yes", "price_from": 899, "price_to": 899, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 13, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 13, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 899, "delivery_price": null, "weight": 1000}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 7, "name": "Дигит<PERSON><PERSON><PERSON>н термометър", "parent_id": 4, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "digitalen-termometar-image_61f791343b48b.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 10, "color_id": 146, "percents": 86, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 70466}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 10, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 11, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 12, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 5, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-5", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добави повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": null, "image_id": 8, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 1030, "price_to": 1030, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 15, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 15, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 1030, "delivery_price": null, "weight": 1000}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 8, "name": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> бинт", "parent_id": 5, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "lasticen-bint-image_61f791bacac15.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 11, "color_id": 146, "percents": 82, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 12, "color_id": 50, "percents": 7, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 135731}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 9, "name": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> бинт", "parent_id": 5, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "lasticen-bint-image_61f791c32306c.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 13, "color_id": 146, "percents": 80, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 14, "color_id": 14, "percents": 5, "R": 95, "G": 158, "B": 160, "hex": "#5F9EA0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 135760}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 13, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 14, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 15, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 6, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-4", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добави повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": null, "image_id": 10, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 1560, "price_to": 1560, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 17, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 17, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 1560, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 10, "name": "Хирургични ножици комплект", "parent_id": 6, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "hirurg<PERSON><PERSON>-nozici-komplekt-image_61f793c56e2ca.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 15, "color_id": 146, "percents": 92, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 113695}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 11, "name": "Хирургични ножици комплект", "parent_id": 6, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "hirurg<PERSON>ni-nozici-komplekt-image_61f79467dccce.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 16, "color_id": 146, "percents": 80, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 134639}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 16, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 17, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 18, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 7, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-3", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добави повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 3, "vendor_id": null, "image_id": 13, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "no", "price_from": 18999, "price_to": 18999, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 19, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 19, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 18999, "delivery_price": null, "weight": 1000}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 13, "name": "Стетоск<PERSON><PERSON> Littmann", "parent_id": 7, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "stetoskop-littmann-image_61f7959428e3e.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 18, "color_id": 146, "percents": 80, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 134501}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 12, "name": "Стетоск<PERSON><PERSON> Littmann", "parent_id": 7, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "ste<PERSON><PERSON><PERSON>-littmann-image_61f79591b4ce8.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 17, "color_id": 146, "percents": 86, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 70394}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 19, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 20, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 21, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 8, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-2", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Номер", "p2": null, "p3": null, "p1_id": 4, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добави повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 4, "vendor_id": null, "image_id": 15, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "yes", "price_from": 2370, "price_to": 2370, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 22, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 22, "v1": "37", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 2370, "delivery_price": null, "weight": 1000}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 23, "v1": "38", "v2": null, "v3": null, "v1_id": 12, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 2370, "delivery_price": null, "weight": 1000}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 24, "v1": "39", "v2": null, "v3": null, "v1_id": 13, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 2370, "delivery_price": null, "weight": 1000}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 25, "v1": "40", "v2": null, "v3": null, "v1_id": 14, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 2370, "delivery_price": null, "weight": 1000}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 15, "name": "Медицински чехли сабо", "parent_id": 8, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "medicinski-cehli-sabo-image_61f7962bcbf13.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 21, "color_id": 146, "percents": 89, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 22, "color_id": 50, "percents": 5, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 117494}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 14, "name": "Медицински чехли сабо", "parent_id": 8, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "medicinski-cehli-sabo-image_61f7960e671d1.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 19, "color_id": 146, "percents": 68, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 20, "color_id": 78, "percents": 10, "R": 135, "G": 206, "B": 250, "hex": "#87CEFA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 155146}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 22, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 23, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 24, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 17, "parameter_id": 4, "parameter_option_id": 11, "variant_id": 22}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 18, "parameter_id": 4, "parameter_option_id": 12, "variant_id": 23}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 19, "parameter_id": 4, "parameter_option_id": 13, "variant_id": 24}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 20, "parameter_id": 4, "parameter_option_id": 14, "variant_id": 25}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 9, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-1", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добави повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": null, "image_id": 17, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 78900, "price_to": 78900, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 28, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 28, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 78900, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 17, "name": "Тринокулярен микроскоп", "parent_id": 9, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "trinokularen-mikroskop-image_61f797693e1d4.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 25, "color_id": 146, "percents": 89, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 104882}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 16, "name": "Тринокулярен микроскоп", "parent_id": 9, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "trinokularen-mikroskop-image_61f79766ea12d.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 23, "color_id": 146, "percents": 78, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 24, "color_id": 64, "percents": 5, "R": 230, "G": 230, "B": 250, "hex": "#E6E6FA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 118014}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 25, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 26, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 27, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 10, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добави повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 2, "vendor_id": null, "image_id": 19, "tracking": "yes", "threshold": null, "shipping": "yes", "digital": "no", "sale": "no", "new": "yes", "price_from": 25899, "price_to": 25899, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 30, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава пордуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 30, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": 4, "sku": "", "barcode": "", "price": 25899, "delivery_price": null, "weight": 1000}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 19, "name": "Преносим небулизатор", "parent_id": 10, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "prenosim-nebulizator-image_61f7985387114.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 27, "color_id": 146, "percents": 78, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 28, "color_id": 64, "percents": 5, "R": 230, "G": 230, "B": 250, "hex": "#E6E6FA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 117990}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 18, "name": "Преносим небулизатор", "parent_id": 10, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "prenosim-nebulizator-image_61f798504bc9e.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 26, "color_id": 146, "percents": 86, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 70466}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 28, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 29, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 30, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}]