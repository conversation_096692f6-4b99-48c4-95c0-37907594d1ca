/*=============================================================================*\
    POPUP
\*=============================================================================*/

/*  MODAL - BOOTSTRAP
-------------------------------------------------------------------------------*/

.modal-dialog {
	margin: 30px auto;
	max-width: 100%;
}

.modal-content {
	background-color: @color-popups-background; /* theme */
	border: 1px solid;
	border-color: @color-second-borders; /* theme */
	border-radius: 0;
	margin: 0 15px;
	color: @color-popups-text; /* theme */

	h1,h2,h3,h4,h5,h6 {
		color: @color-popups-titles; /* theme */
	}

	a {
		color: @color-popups-highlight; /* theme */
	}

	._button {
		.button-colors(@color-popups-button-background, @color-popups-button-borders, @color-popups-button-text);

		@media @hover {
			&:hover {
				.button-colors(@color-popups-button-background-hover, @color-popups-button-borders-hover, @color-popups-button-text-hover);
			}
		}

		&[disabled] {
			.button-colors(darken(@color-button-disabled-background, 5%), darken(@color-button-disabled-background, 5%), darken(@color-button-disabled-text, 5%));
		}
	}

	._button-secondary {
		.button-colors(@color-popups-button-secondary-background, @color-popups-button-secondary-borders, @color-popups-button-secondary-text);

		@media @hover {
			&:hover {
				.button-colors(@color-popups-button-secondary-background-hover, @color-popups-button-secondary-borders-hover, @color-popups-button-secondary-text-hover);
			}
		}
	}

	._checkbox {
		.checker {
			span {
				border-color: @color-popups-text; /* theme */

				&.checked {
					border-color: @color-popups-text; /* theme */

					&:before {
						color: @color-popups-text; /* theme */
					}
				}
			}
			
			&.hover,
			&.focus {
				span {
					border-color: @color-popups-text; /* theme */

					&.checked {
						border-color: @color-popups-text; /* theme */
					}
				}
			}
		}
	}

	._radio {
		.radio {
			span {
				border-color: @color-popups-text; /* theme */

				&.checked {
					border-color: @color-popups-text; /* theme */

					&:before {
						background-color: @color-popups-text; /* theme */
					}
				}
			}
		}
	}

	._meta-links {
		border-color: @color-popups-borders;

		a {
			color: @color-popups-text; /* theme */

			@media @hover {
				&:hover {
					color: @color-popups-highlight; /* theme */
				}
			}
		}

		._button-register {
			background-color: transparent; /* theme */
			border-color: @color-popups-borders; /* theme */
			color: @color-popups-highlight; /* theme */

			@media @hover {
				&:hover {
					background-color: @color-popups-button-background; /* theme */
					border-color: @color-popups-button-borders; /* theme */
					color: @color-popups-button-text; /* theme */
				}
			}
		}
	}

	._forgotten-password-link {
		a {
			color: @color-popups-text; /* theme */

			@media @hover {
				&:hover {
					color: @color-popups-highlight; /* theme */
				}
			}
		}
	}

	._form-row {
		&:last-child {
			margin-bottom: 0;
		}
	}

	._notification {
		background-color: @color-popups-background; /* theme */
	}
}

.modal-body {
	padding: 30px;
}

.modal-footer {
	padding: 30px;
	border-top-width: 1px;
	border-top-style: solid;
	border-top-color: @color-popups-borders; /* theme */
}

.close {
	position: absolute;
	top: 0;
	right: 0;
	width: 40px;
	height: 40px;
	opacity: 1;

	&:hover {
		opacity: .7;
	}

	&:before {
		content: '\e014';
		font-family: @font-glyphicons;
		text-shadow: none;
		font-size: calc(@font-size-main ~'+' 1px); /* theme */
		color: @color-popups-text; /* theme */
		.centerer(true, true);
	}
}

/*  POPUP
-------------------------------------------------------------------------------*/

._popup-title {
	color: @color-popups-titles; /* theme */
	margin-bottom: 30px;
	text-align: center;
	.uppercase();

	h1,h2,h3,h4,h5,h6 {
		._h2();
		font-weight: bold;
	}
}

/*  DETAILS - POPUP
-------------------------------------------------------------------------------*/

.modal-dialog.product-details {
	width: 1180px;

	.modal-body {
		padding-left: 5px;
		padding-right: 5px;
	}

	._button-share,
	._product-details-wishlist-button,
	._product-details-compare .checker>span {
		border-color: @color-popups-borders; /* theme */
		color: @color-popups-text; /* theme */
	}
}

.modal {
	.tags {
		a {
			border-color: @color-popups-borders; /* theme */
			color: @color-popups-text; /* theme */
		}
	}
}