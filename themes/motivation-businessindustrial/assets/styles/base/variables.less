/*=============================================================================*\
    VARIABLES
\*=============================================================================*/

/*  FONTS
-------------------------------------------------------------------------------*/

/*  Fonts changeble */
@font-family-main: 'Montserrat';
@font-family-titles: 'Montserrat';
@font-family-buttons: 'Montserrat';
@font-family-product-list-title: 'Montserrat';

@font-size-main: 14px;
@font-size-buttons: 16px;
@font-size-heading-1: 30px;
@font-size-heading-2: 24px;
@font-size-heading-3: 20px;
@font-size-heading-4: 18px;
@font-size-heading-5: 16px;
@font-size-heading-6: 14px;
@font-size-product-list-title: 14px;

@font-weight-main: 400;
@font-weight-titles: 700;
@font-weight-buttons: 400;
@font-weight-product-list-title: 400;

@font-style-main: normal;
@font-style-titles: normal;
@font-style-buttons: normal;
@font-style-product-list-title: normal;

/*  Family */
@font-awesome: 'FontAwesome', sans-serif;
@font-glyphicons: 'Glyphicons Halflings', sans-serif;

/*  Size */
@font-size-xsmall: 10px;
@font-size-nav: 16px;
@font-size-forms: 14px;
@font-size-slider-nav: 68px;
@font-size-notification-icon: 32px;
@font-size-loader-list: 50px;
@font-size-promo-bar-close: 16px;

/*  Line height */
@line-height-base: 1.72;
@line-height-medium: 1.5;
@line-height-low: 1.2;
@line-height-button: 1.2;


/*  THEME COLORS
-------------------------------------------------------------------------------*/

/*  Main */
@color-main-background: #fff;
@color-main-borders: #e8e8e8;
@color-main-text: #666;
@color-main-secondary-text: #333;
@color-main-meta-text: #999;
@color-main-titles: #333;
@color-main-highlight: #ffcd26;
@color-main-icons: #ffcd26;

/* Second */
@color-second-background: #f8f8f8;
@color-second-borders: #e8e8e8;
@color-second-text: #333;
@color-second-secondary-text: #666;
@color-second-meta-text: #999999;
@color-second-titles: #333;
@color-second-highlight: #ffcd26;
@color-second-image-box: #545454;

@color-second-button-background: #ffcd26;
@color-second-button-borders: #ffcd26;
@color-second-button-text: #fff;
@color-second-button-background-hover: #3d3d3d;
@color-second-button-borders-hover: #3d3d3d;
@color-second-button-text-hover: #fff;

@color-second-button-secondary-background: #fff;
@color-second-button-secondary-borders: #ffcd26;
@color-second-button-secondary-text: #ffcd26;
@color-second-button-secondary-background-hover: #ffcd26;
@color-second-button-secondary-borders-hover: #ffcd26;
@color-second-button-secondary-text-hover: #fff;

/*  Header */
@color-header-background: #000;
@color-header-borders: #000;
@color-header-text: #949494;
@color-header-secondary-text: #fff;

@color-header-icons: #ffffff;
@color-header-icons-hover: #ffcd26;
@color-header-icons-bubble-background: #ffcd26;
@color-header-icons-bubble-text: #000000;

@color-navigation-background: #000;
@color-navigation-text: #fff;
@color-navigation-hover-background: #000;
@color-navigation-hover-text: #ffcd26;

@color-dropdowns-background: #000;
@color-dropdowns-borders: #191919;
@color-dropdowns-text: #fff;
@color-dropdowns-secondary-text: #fff;
@color-dropdowns-meta-text: #999;
@color-dropdowns-highlight: #ffcd26;
@color-dropdowns-image-box: #fff;

@color-dropdowns-button-background: #ffcd26;
@color-dropdowns-button-borders: #ffcd26;
@color-dropdowns-button-text: #000;
@color-dropdowns-button-background-hover: #000;
@color-dropdowns-button-borders-hover: #ffcd26;
@color-dropdowns-button-text-hover: #fff;

/*  Footer */
@color-footer-background: #000000;
@color-footer-borders: #454545;
@color-footer-text: #cfcfcf;
@color-footer-titles: #ffffff;
@color-footer-highlight: #ffcd26;

@color-footer-socials-background: #f7f7f7;
@color-footer-socials-icon: #000000;
@color-footer-socials-icon-background: #ffcd26;
@color-footer-socials-icon-hover: #000000;
@color-footer-socials-icon-background-hover: #ffffff;

@color-footer-bottom-bar-background: #000000;
@color-footer-bottom-bar-text: #c2c2c2;
@color-footer-bottom-bar-links: #828282;
@color-footer-bottom-bar-highlight: #ffcd26;

/*  Topbar */
@color-topbar-background: #000;
@color-topbar-borders: #000;
@color-topbar-text: #fff;
@color-topbar-icons: #999;
@color-topbar-hovers: #ffcd26;

/*  Slider */
@color-slider-background: #fff;
@color-slider-text: #fff;
@color-slider-titles: #ffcd26;
@color-slider-arrows: #ffcd26;
@color-slider-dots: #ffcd26;

@color-slider-button-background: #ffcd26;
@color-slider-button-borders: #ffcd26;
@color-slider-button-text: #fff;
@color-slider-button-background-hover: #3d3d3d;
@color-slider-button-borders-hover: #3d3d3d;
@color-slider-button-text-hover: #fff;

/*  Product Listing */
@color-product-listing-borders: #e8e8e8;
@color-product-listing-title: #333;
@color-product-listing-highlight: #ffcd26;
@color-product-listing-price: #000000;
@color-product-listing-price-old: #ccc;
@color-product-listing-actions: #999;
@color-product-listing-actions-highlight: #ffcd26;

/*  Product Details */
@color-product-image-background: #fff;
@color-product-image-borders: #e8e8e8;
@color-product-price: #000;
@color-product-icons: #ccc;
@color-product-icons-highlight: #ffcd26;
@color-product-gallery-background: #f7f7f7;

/*  Forms */
@color-forms-fields-background: #fff;
@color-forms-fields-borders: #e8e8e8;
@color-forms-fields-text: #999;
@color-forms-fields-placeholder: #ccc;
@color-forms-fields-icons: #ffcd26;

@color-forms-checkbox-background: #fff;
@color-forms-checkbox-borders: #e8e8e8;
@color-forms-checkbox-check: #ffcd26;

@color-forms-radio-background: #fff;
@color-forms-radio-borders: #e8e8e8;
@color-forms-radio-check: #ffcd26;

@color-forms-range-slider: #e8e8e8;
@color-forms-range-slider-highlight: #ffcd26;
@color-forms-range-slider-sliders: #666;

/*  Buttons */
@color-button-background: #fff;
@color-button-borders: #ffcd26;
@color-button-text: #000;
@color-button-background-hover: #ffcd26;
@color-button-borders-hover: #ffcd26;
@color-button-text-hover: #fff;

@color-button-secondary-background: #fff;
@color-button-secondary-borders: #ffcd26;
@color-button-secondary-text: #ffcd26;
@color-button-secondary-background-hover: #ffd42a;
@color-button-secondary-borders-hover: #ffe722;
@color-button-secondary-text-hover: #fff;

@color-button-thertiary-background: #fff;
@color-button-thertiary-borders: #ededed;
@color-button-thertiary-text: #ffcd26;

@color-button-thertiary-background-hover: #ffcd26;
@color-button-thertiary-borders-hover: #ffcd26;
@color-button-thertiary-text-hover: #fff;

@color-button-disabled-background: #f7f7f7;
@color-button-disabled-text: #ccc;

/*  Text boxes */
@color-text-box-background: #fff;
@color-text-box-borders: #e8e8e8;
@color-text-box-text: #999;
@color-text-box-titles: #333;

@color-text-box-tooltip-background: #393d50;
@color-text-box-tooltip-borders: #4f526a;
@color-text-box-tooltip-text: #fff;

/*  Text sections */
@color-text1-background: #fff;
@color-text1-text: #999;
@color-text1-titles: #666;
@color-text1-highlight: #ffcd26;

@color-text2-background: #fff;
@color-text2-text: #666;
@color-text2-titles: #333;
@color-text2-highlight: #ffcd26;

@color-text3-background: #fff;
@color-text3-text: #666;
@color-text3-titles: #333;
@color-text3-highlight: #ffcd26;

/*  Promo bar */
@color-promo-bar-background: #ffcd26;
@color-promo-bar-text: #fff;
@color-promo-bar-close: #fff;

@color-promo-bar-button-background: #ffcd26;
@color-promo-bar-button-borders: #fff;
@color-promo-bar-button-text: #fff;
@color-promo-bar-button-background-hover: #3d3d3d;
@color-promo-bar-button-borders-hover: #3d3d3d;
@color-promo-bar-button-text-hover: #fff;

/*  Breadcrumb */
@color-breadcrumb-background: #f8f8f8;
@color-breadcrumb-text: #666;
@color-breadcrumb-text-active: #999;
@color-breadcrumb-text-hover: #ffcd26;

/*  Pagination */
@color-pagination-borders: #e8e8e8;
@color-pagination-text: #666;
@color-pagination-highlight: #ffcd26;
@color-pagination-disabled: #ccc;

/*  Tooltips */
@color-tooltips-background: #404040;
@color-tooltips-borders: #666;
@color-tooltips-text: #fff;

/*  Labels */
@color-label-new-background: #ffcd26;
@color-label-new-text: #fff;

@color-label-sale-background: #02a4f1;
@color-label-sale-text: #fff;

@color-label-discount-background: #ffcd26;
@color-label-discount-text: #fff;

@color-label-delivery-background: #ffcd26;
@color-label-delivery-text: #fff;

@color-label-custom-background: #02a4f1;
@color-label-custom-text: #fff;

@color-label-featured-background: #ffcd26;
@color-label-featured-text: #fff;

@color-label-leasing-background: #02a4f1;
@color-label-leasing-text: #fff;

/*  Popups */
@color-popups-background: #fff;
@color-popups-borders: #e8e8e8;
@color-popups-text: #666;
@color-popups-titles: #333;
@color-popups-highlight: #ffcd26;

@color-popups-button-background: #ffcd26;
@color-popups-button-borders: #ffcd26;
@color-popups-button-text: #fff;
@color-popups-button-background-hover: #3d3d3d;
@color-popups-button-borders-hover: #3d3d3d;
@color-popups-button-text-hover: #fff;

@color-popups-button-secondary-background: #fff;
@color-popups-button-secondary-borders: #ffcd26;
@color-popups-button-secondary-text: #ffcd26;
@color-popups-button-secondary-background-hover: #3d3d3d;
@color-popups-button-secondary-borders-hover: #3d3d3d;
@color-popups-button-secondary-text-hover: #fff;


/*  STATIC COLORS
-------------------------------------------------------------------------------*/

/*  Error Text */
@color-error-text: #ed1c24;

/*  Notifications */
@color-notification-text: #ff0000;
@color-notification-background: #fff;
@color-notification-border: #ff0000;

@color-notification-note-text: #ffa800;
@color-notification-note-background: #fff;
@color-notification-note-border: #ffa800;

@color-notification-error-text: #ed1c24;
@color-notification-error-background: #fff;
@color-notification-error-border: #ed1c24;

@color-notification-success-text: #79b12d;
@color-notification-success-background: #fff;
@color-notification-success-border: #79b12d;

/*  Toastr */
@color-toastr-text: #ff0000;
@color-toastr-background: #fff;
@color-toastr-border: #ff0000;

@color-toastr-note-text: #fff;
@color-toastr-note-background: #ffa800;
@color-toastr-note-border: #ffa800;

@color-toastr-error-text: #fff;
@color-toastr-error-background: #ed1c24;
@color-toastr-error-border: #ed1c24;

@color-toastr-success-text: #fff;
@color-toastr-success-background: #79b12d;
@color-toastr-success-border: #79b12d;

/*  Product Status */
@color-status-instock-text: #fff;
@color-status-instock-background: #3c3;

@color-status-outofstock-text: #fff;
@color-status-outofstock-background: #f00;

@color-status-twodays-text: #fff;
@color-status-twodays-background: #f93;

@color-status-preorder-text: #fff;
@color-status-preorder-background: #3370cc;

@color-status-request-text: #fff;
@color-status-request-background: #c600d4;


/*  Z-INDEX
-------------------------------------------------------------------------------*/

@z-page-loader: 200;
@z-nav-mobile: 140;
@z-fixed-sidebar: 130;
@z-header-fixed: 120;
@z-compare: 110;
@z-to-top: 100;
@z-text-boxes: 90;
@z-topbar: 80;
@z-navbar: 60;
@z-slider: 30;
@z-loader: 20;


/*  OTHER
-------------------------------------------------------------------------------*/

@separator: 60px;
@separator-small: 30px;


/*  RATIO
-------------------------------------------------------------------------------*/

@golden-ratio-ngt: 0.618%;
@golden-ratio-pst: 1.618%;
@image-orientation: 133.33%;


/*  SCREEN
-------------------------------------------------------------------------------*/

/*  XXX small screen: phone */
@screen-xxxs: 320px;
@screen-xxxs-min: @screen-xxxs;
@screen-small-phone: @screen-xxxs-min;

/*  XX small screen: phone */
@screen-xxs: 375px;
@screen-xxs-min: @screen-xxs;
@screen-small-phone: @screen-xxs-min;

/*  Extra small screen: phone */
@screen-xs: 480px;
@screen-xs-min: @screen-xs;
@screen-phone: @screen-xs-min;

/*  Small screen: tablet */
@screen-sm: 768px;
@screen-sm-min: @screen-sm;
@screen-tablet: @screen-sm-min;

/*  Medium screen: tablet-landscape */
@screen-md: 992px;
@screen-md-min: @screen-md;
@screen-tablet-landscape: @screen-md-min;

/*  Medium screen: desktop */
@screen-desktop: 1025px;
@screen-desktop-min: @screen-desktop;

/* Hover screen resolution */
@screen-desktop-hover: @screen-desktop;

/*  Large screen: wide desktop */
@screen-lg: 1200px;
@screen-lg-min: @screen-lg;
@screen-lg-desktop: @screen-lg-min;

/*  So media queries don't overlap when required, provide a maximum */
@screen-xxxs-max: (@screen-xxs-min - 1);
@screen-xxs-max: (@screen-xs-min - 1);
@screen-xs-max: (@screen-sm-min - 1);
@screen-sm-max: (@screen-md-min - 1);
@screen-md-max: (@screen-lg-min - 1);
@screen-tablet-max: (@screen-desktop-min - 1);

@hover: ~'(min-width: @{screen-desktop})';