<div class="_product-details-price product-details-price-js{if $product->price_from_discounted} has-discount{/if}">
    {if !is_null($product->price_from_discounted)}
        {$price = $product->price_from_discounted_formatted}
        {$price_old = $product->price_from_formatted}
        {$price_save = $product->price_from_saved_formatted}
    {else}
        {$price = $product->price_from_formatted}
    {/if}

{*    <div class="_product-details-price-meta">*}
{*        <div class="_product-details-price-old price-old-js {if !$price_old|default}hide{/if}">*}
{*            <i class="_product-details-price-value rtl-ltr">{$price_old|default nofilter}</i>*}
{*        </div>*}
{*    </div>*}

{*    <div class="_product-details-price-main">*}
{*        {if W::ProductsDetails('show_price') && showPriceForUser()}*}
{*            <span class="_product-details-price-new price-new-js rtl-ltr">{$price nofilter}</span>*}
{*        {/if}*}
{*    </div>*}

    {if $list_widget_settings['listing_show_price'] && showPriceForUser()}
        <div class="_product-price{if $product->price_from_discounted} has-discount{/if} text-left">
            <div class="_product-price-inner">
                {if !empty($product->price_from_discounted_units_formatted)}
                    <span class="_product-price-compare price-new-js">{if $product->diff_prices}<span class="_product-price-diff">{t}sf.product.price_from{/t} </span>{/if}{$product->price_from_discounted_units_formatted nofilter}</span>
                    <del class="_product-price-old" price-old-js>{$product->price_from_units_formatted nofilter}</del>
                {else}
                    <span class="price">{if $product->diff_prices}<span class="_product-price-diff">{t}sf.product.price_from{/t} </span>{/if}{$product->price_from_units_formatted nofilter}</span>
                {/if}
            </div>
        </div>
    {/if}

    {if W::ProductsDetails('show_product_status')}
        <span class="_product-details-stock-status-bar js-status-bar {if $product->total_variants}hide{/if} {$product->status_type|replace:'_':'-'}" data-status="{$product->status_type|replace:'_':'-'}">{$product->status_name}</span>
    {/if}
</div>