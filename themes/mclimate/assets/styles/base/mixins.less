/*=============================================================================*\
    MIXINS
\*=============================================================================*/

/*  GENERAL
-------------------------------------------------------------------------------*/

.clearfix() {
    &:before,
    &:after {
        display: table;
        content: '';
        clear: both;
    }
}

.centerer(@h: true, @v: true) {
    position: absolute;
    & when (@h = true) and (@v = false) {
        left: 50%;
        transform: translateX(-50%);

        & when (@rtl) {
            transform: translateX(50%);
        }
    }
    & when (@v = true) and (@h = false) {
        top: 50%;
        transform: translateY(-50%);
    }
    & when (@h = true) and (@v = true) {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        & when (@rtl) {
            transform: translate(50%, -50%);
        }
    }
}

.placeholder(@color) {
    &:-moz-placeholder {
        color: @color;
    }
    &:-ms-input-placeholder {
        color: @color;
    }
    &::-webkit-input-placeholder {
        color: @color;
    }
}

.main-titles(@shape-color: @color-main-highlight, @border-color: @color-main-borders) {
    position: relative;
    font-size: @font-size-heading-2; /* theme */
    font-family: @font-family-section-titles; /* theme */
    font-weight: @font-weight-section-titles; /* theme */
    font-style: @font-style-section-titles; /* theme */
    -ltr-text-transform: uppercase;
    display: inline-block;

    &:before {
        position: absolute;
        content: "\f103";
        bottom: -20px;
        margin: auto;
        font: normal normal normal 18px/1 @font-awesome;
        text-rendering: auto;
        color: @shape-color; /* theme */
        width: 30px;
        height: 20px;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        .centerer(true, false);
    }

    &:after {
        position: absolute;
        content: '';
        width: 30px;
        height: 1px;
        bottom: -10px;
        margin: auto;
        box-sizing: content-box;
        border-left-width: 70px;
        border-left-style: solid;
        border-left-color: @border-color; /* theme */
        border-right-width: 70px;
        border-right-style: solid;
        border-right-color: @border-color; /* theme */
        .centerer(true, false);
    }
}

.mobile-main-titles() {
    padding-bottom: 10px;
    margin-bottom: 25px;
    font-size: @font-size-heading-3; /* theme */
}

.button-loading(@bg, @text, @bg-hover, @text-hover) {
    &.loading {
        color: @bg; /* theme */
        .loader-dots {
            span {
                background-color: @text; /* theme */
            }
        }
        @media (min-width: @screen-desktop) {
            &:hover {
                color: @bg-hover; /* theme */
                .loader-dots {
                    span {
                        background-color: @text-hover; /* theme */
                    }
                }
            }
        }
    }
}

/*  FLEXY ROW - GRID
-------------------------------------------------------------------------------*/

.flexy-row(@space) {
    display: table;
    table-layout: fixed;
    width: calc(~'100% + @{space}');
    margin-left: calc(~'-@{space} / 2');
    .flexy-col {
        display: table-cell;
        padding-left: calc(~'@{space} / 2');
        padding-right: calc(~'@{space} / 2');
    }
}

.flexy-row-mobile(@space) {
    display: block;
    width: auto;
    margin-left: 0;
    .flexy-col-mobile {
        &:first-child {
            margin-top: 0;
        }
        display: block;
        padding-left: 0;
        padding-right: 0;
        margin-top: @space;
    }
}

/*  FORMS
-------------------------------------------------------------------------------*/

.field-icon(@width, @color, @position) {
    display: inline-block;
    position: relative;
    & ._field {
        & when (@position = left) {
            padding-left: @width;
        }
        & when (@position = right) {
            padding-right: @width;
        }
    }
    & ._button {
        position: absolute;
        top: 0;
        bottom: 0;
        background-color: transparent;
        border-color: transparent;
        color: @color;
        padding-left: 0;
        padding-right: 0;
        width: @width;
        & when (@position = left) {
            left: 0;
        }
        & when (@position = right) {
            right: 0;
        }
    }
}

/*  SHAPES - http://fahad19.github.io/shape.less/
-------------------------------------------------------------------------------*/

.shape-circle(@width) {
    .shape-square(@width);
    border-radius: 50%;
}

.shape-oval(@width, @height) {
    .shape-rectangle(@width, @height);
    border-radius: 50%;
}

.shape-rectangle(@width, @height) {
    width: @width;
    height: @height;
}

.shape-square(@width) {
    .shape-rectangle(@width, @width);
}

.shape-triangle-up(@width, @color) {
    .shape-square(0);
    border-left: @width/2 solid transparent;
    border-right: @width/2 solid transparent;
    border-bottom: @width solid @color;
}

.shape-triangle-down(@width, @color) {
    .shape-square(0);
    border-left: @width/2 solid transparent;
    border-right: @width/2 solid transparent;
    border-top: @width solid @color;
}

.shape-triangle-left(@width, @color) {
    .shape-square(0);
    border-top: @width/2 solid transparent;
    border-right: @width solid @color;
    border-bottom: @width/2 solid transparent;
}

.shape-triangle-right(@width, @color) {
    .shape-square(0);
    border-top: @width/2 solid transparent;
    border-left: @width solid @color;
    border-bottom: @width/2 solid transparent;
}

.shape-triangle-top-left(@width, @color) {
    .shape-square(0);
    border-top: @width solid @color;
    border-right: @width solid transparent;
}

.shape-triangle-top-right(@width, @color) {
    .shape-square(0);
    border-top: @width solid @color;
    border-left: @width solid transparent;
}

.shape-triangle-bottom-left(@width, @color) {
    .shape-square(0);
    border-bottom: @width solid @color;
    border-right: @width solid transparent;
}

.shape-triangle-bottom-right(@width, @color) {
    .shape-square(0);
    border-bottom: @width solid @color;
    border-left: @width solid transparent;
}

/*  TRIANGLES - https://github.com/stijnj/less-triangle
-------------------------------------------------------------------------------*/

.triangle-base() {
    content: '';
    display: block;
    width: 0;
    height: 0;
    -moz-transform: scale(.9999);
}

.triangle(@direction, @size, @color) {
    .triangle(@direction, @size * 2, @size, @color);
}

.triangle(@direction, @width, @height, @color) when (@direction = up) {
    .triangle-base();
    border-left: (@width / 2) solid transparent;
    border-right: (@width / 2) solid transparent;
    border-bottom: @height solid @color;
}

.triangle(@direction, @width, @height, @color) when (@direction = down) {
    .triangle-base();
    border-left: (@width / 2) solid transparent;
    border-right: (@width / 2) solid transparent;
    border-top: @height solid @color;
}

.triangle(@direction, @width, @height, @color) when (@direction = left) {
    .triangle-base();
    border-top: (@width / 2) solid transparent;
    border-bottom: (@width / 2) solid transparent;
    border-right: @height solid @color;
}

.triangle(@direction, @width, @height, @color) when (@direction = right) {
    .triangle-base();
    border-top: (@width / 2) solid transparent;
    border-bottom: (@width / 2) solid transparent;
    border-left: @height solid @color;
}

/*  ASPECT RATIO
-------------------------------------------------------------------------------*/

.aspect-ratio(@width, @height) {
    position: relative;
    &:before {
        display: block;
        content: "";
        width: 100%;
        padding-top: (@height / @width) * 100%;
    }
}