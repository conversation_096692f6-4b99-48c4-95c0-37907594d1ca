{$language = site()}
{$current = $languages->where('language', $language->language)->first()}
{if !empty($current->language) && $languages->count() > 1}
    <li class="_nav-lang-menu _navigation-main-list-item item-collapse">
        <a class="_navigation-main-list-item-link _figure-stack js-nolink" itemprop="url" href="javascript:;">
            <img src="{$img_url}site/img/flags/{{$current->language}}.png?{app('last_build')}" alt="{{$current->language_name}}">

            <i class="_figure-stack-icon fa fa-angle-down collapse-icon"></i>
        </a>
        {if site('template') != 'virtuoso'
            && site('template') != 'construction'
            && site('template') != 'construction-inlabs'
            && site('template') != 'gameofdrones'
            && site('template') != 'gameofdrones-living'
            && site('template') != 'properties'
            && site('template') != 'properties-shadower'
            && site('template') != 'themex'}
        <div class="_navigation-dropdown _navigation-dropdown-level-1">
        {/if}
            <ul class="_navigation-dropdown-list">
                {foreach $languages as $lang}
                    {if $language->language != $lang->language}
                        <li class="_navigation-dropdown-list-item">
                            <a href="{{$lang->url}}" class="_navigation-dropdown-list-item-link _figure-stack">
                                <img src="{$img_url}site/img/flags/{{$lang->language}}.png?{app('last_build')}"
                                     alt="{{$lang->language_name}}">
                                <span class="_figure-stack-label">{{$lang->language_name}}</span>
                            </a>
                        </li>
                    {/if}
                {/foreach}
            </ul>
            {if site('template') != 'virtuoso'
                && site('template') != 'construction'
                && site('template') != 'construction-inlabs'
                && site('template') != 'gameofdrones'
                && site('template') != 'gameofdrones-living'
                && site('template') != 'properties'
                && site('template') != 'properties-shadower'
                && site('template') != 'themex'}
            </div>
            {/if}
    </li>
{/if}