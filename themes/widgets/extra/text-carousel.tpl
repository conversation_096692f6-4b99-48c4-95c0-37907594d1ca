{if $widget->isEnabled() && !empty($widget->getSlides())}
	{$isAutoplay = $widget->getSetting('autoplay') == 'yes'}
	{$hasControls = $widget->getSetting('controls') == 'yes'}
	{$isLoop = $widget->getSetting('cycle') == 'yes'}

	<div class="_slider _text-carousel js-slider{if $widget->getSetting('full_width') == 'no'} container{/if}{if $widget->isDefaultContent()} _has-helper{/if}">
		<div class="slides js-slides"
			 {if isset($items)}data-items="{$items}"{/if}
			 {if isset($itemsDesktop)}data-items-desktop="{$itemsDesktop}"{/if}
			 data-interval="{$widget->getSetting('interval')}"
			 data-pause="{if $widget->getSetting('pause') == 'yes'}true{else}false{/if}"
			 data-dots="{if $widget->getSetting('indicators') == 'yes'}true{else}false{/if}"
			 data-nav="{if $hasControls}true{else}false{/if}"
			 data-autoplay="{if $isAutoplay}true{else}false{/if}"
			 data-cycle="{if $isLoop}true{else}false{/if}"
			 data-rtl="{if $rtl}true{else}false{/if}"
			 {if isset($widget->getSetting('slides_per_view'))}data-items="{$widget->getSetting('slides_per_view')|default}"{/if}
			 {if isset($widget->getSetting('space_between'))}data-margin="{$widget->getSetting('space_between')|default}"{/if}>

			{foreach $widget->getSlidesNew() as $slide}
				<div class="slide js-slide {if $slide.whole_link|default}slide-link{/if}{if $slide@first} current{/if}"{if $slide.whole_link|default} data-href="{$slide.link_formatted}" data-target="{$slide.target}"{/if} itemscope itemtype="https://schema.org/ImageObject">
					{if $slide.html || $widget->getSetting('caption') == 'yes' || ($slide.link_type && $slide.link_caption|default)}
					<span class="_textbox">
						{if $widget->getSetting('caption') == 'yes'}
							<h2 class="_caption">{$slide.caption}</h2>
						{/if}

						{$slide.html|default nofilter}

						{if $slide.link_type && $slide.link_caption|default }
							<p class="text-align{$slide.horizontal_position|default}">
								<a href="{$slide.link_formatted}" class="_button" target="{$slide.target}">
									<span class="_figure-stack">
										<span class="_figure-stack-label">{$slide.link_caption}</span>
									</span>
								</a>
							</p>
						{/if}
					</span>
					{/if}
				</div>
			{/foreach}
		</div>

		<div class="loader-container">
			<div class="loader">
				<span></span>
				<span></span>
				<span></span>
				<span></span>
				<span></span>
				<span></span>
				<span></span>
				<span></span>
			</div>
		</div>

		{if $widget->isDefaultContent()}
			{include file="widgets/common/helper.tpl"}
		{/if}
	</div>
{/if}