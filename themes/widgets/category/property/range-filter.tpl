<div class="_form-inner js-property-filter">
    <div class="_form">
        <div class="_form-row">
            <div class="_form-col">
                <span class="_filter-price-range-holder js-property-range-{$property->id}">
                    <span class="_filter-price-range-from">{$property->values[0]|default}</span>
                    <span class="_filter-price-range-dash">-</span>
                    <span class="_filter-price-range-to">{$property->values[1]|default}</span>
                </span>
            </div>
        </div>
        <div class="_form-row">
            <div class="_form-col">
                <div class="_range-slider js-property-filter-{$property->id}"></div>
                <input type="hidden" readonly="readonly" data-vendor-handle="{W::categoryProperties()->getVendorHandle()}" data-category-handle="{W::categoryProperties()->getCategoryHandle()}" data-property-name="{$property->url_handle}" data-type="range" value="{$property->active_range}" class="category-property-filter" >
            </div>
        </div>
    </div>
</div>
{script}
    <script type="text/javascript">
        $(".js-property-filter-{$property->id}").each(function() {
            var $el = $(this);
            return $el.slider({
                range : true,
                step  : {$property->step},
                min   : {$property->min},
                max   : {$property->max},
                values: {json_encode($property->values) nofilter},
                slide : function (event, ui) {
                    $el.closest('.js-property-filter').find('._filter-price-range-from').text(ui.values[0].toFixed({(int)$property->dec_points}));
                    $el.closest('.js-property-filter').find('._filter-price-range-to').text(ui.values[1].toFixed({(int)$property->dec_points}));
                },
                stop: function( event, ui ) {
                    var value = ui.values[0].toFixed({(int)$property->dec_points}) + ':' + ui.values[1].toFixed({(int)$property->dec_points});
                    if(value == '{$property->min}:{$property->max}') {
                        value = '';
                    }
                    $el.closest('.js-property-filter').find('.category-property-filter').val(value).trigger('change');
                }
            });
        });
    </script>
{/script}