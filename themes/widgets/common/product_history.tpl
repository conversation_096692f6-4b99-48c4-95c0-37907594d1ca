{if activeRoute('product.view') && empty(segment(3))}
<script>
    $(function ()
    {
        var back = false;

        if (typeof (history.pushState) != "undefined") {
            var obj = {
                title: "{$product->name}",
                url: "{$product->url()}{if segment(3)}{/if}"
            };

            history.pushState(obj, obj.title, obj.url);

            $(document).one('hide.bs.modal', function ()
            {
                if (!back) {
                    history.back();
                }
            });

            $(window).one('popstate', function ()
            {
                back = true;
                var $modal = $('.modal.in');

                if ($modal.length) {
                    $modal.modal('toggle');
                }
            });
        }
    });
</script>
{/if}