{if !empty($pages) && $pages > 1}
	<div class="_pagination">
		<ul class="pagination js-products-pagination disable-twbs" data-link="{$link}" {if $ajax_link|default}data-ajax-link="{$ajax_link}"{/if} data-pages="{$pages}" data-page="{$page}">
			{foreach $elements AS $element}
				{if $element['link']|default}
					{if $element['active']}
						<li class="active {$element['type']}"><a href="{$link}{$element['link']}" data-ajax-page="{$element['ajax_page']}">{$element['page']}</a></li>
					{else}
						<li class="{$element['type']}"><a href="{$link}{$element['link']}" data-ajax-page="{$element['ajax_page']}">{$element['page']}</a></li>
					{/if}
				{else}
					<li class="disabled {$element['type']}"><a href="javascript:void(0);" data-ajax-page="0">{$element['page']}</a></li>
				{/if}
			{/foreach}
		</ul>
	</div>
{/if}