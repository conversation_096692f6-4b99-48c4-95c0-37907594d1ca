<div class="_bundle-list-box">
    <div class="_bundle-list">
        <div class="_bundle-list-items">
            {if $bundle->name|default}
                <div class="_bundle-list-title">
                    <h3>{$bundle->name}</h3>
                </div>
            {/if}

            <div class="_bundle-list-items-inner">
                {foreach $bundle->bundle_products as $product}
                    <div class="_bundle-list-item {$bundle->class_name|default:$grid_class|default}">
                        <a class="_bundle-list-item-inner" href="{$product->url()}" title="{$product->name}|{$product->category->name|default}">
                            <span class="_bundle-list-item-image">
                                <span class="_bundle-list-item-image-inner">
                                    <img class="_bundle-list-item-image-thumb lazyload-image lazyload-{$product->getOrientation()}"
                                         src="{$product->getImage('600x600')}"

                                         data-src="{$product->getImage('600x600')}"
                                         alt="{$product->name}" title="{$product->name}">
                                </span>
                            </span>

                            <span class="_bundle-list-item-info">
                                <span class="_bundle-list-item-title">{$product->name}</span>

                                <span class="_bundle-list-item-price">
                                    <span class="_bundle-list-item-price-new">{$product->price_from_formatted nofilter}</span>
                                </span>
                            </span>
                        </a>
                    </div>
                {/foreach}
            </div>
        </div>

        <div class="_bundle-list-actions">
            <div class="_bundle-list-actions-title">
                <h5>{t}sf.product.bundle_price{/t}</h5>
            </div>

            <div class="_bundle-list-price">
                <div class="_bundle-list-price-old">
                    <span class="_bundle-list-price-old-text">{t}sf.product.price_without_bundle{/t}:</span>
                    <span class="_bundle-list-price-old-value">{$bundle->products_price_from_formatted nofilter}</span>
                </div>

                <div class="_bundle-list-price-new">
                    <span class="_bundle-list-price-new-text">{t}sf.product.price_with_bundle{/t}:</span>
                    <span class="_bundle-list-price-new-value">{$bundle->price_from_formatted nofilter}</span>
                </div>
            </div>

            {if $bundle->products_has_variants}
                <button class="_button _button-small add-cart-product js-disabled js-loading" data-url="{$bundle->url()}" data-ajax-modal="true" data-modal-class="product-details" title="{strip_tags(__('sf.global.act.view_product_details'))}" disabled>
                    <span class="_figure-stack">
                        <span class="_figure-stack-label">{t}sf.global.act.view_product_details{/t}</span>
                    </span>
                </button>
            {else}
                {include file="widgets/product/listBuyButton.tpl" product=$bundle smButton=true list_widget_settings=['listing_show_buy' => true]}
            {/if}
        </div>
    </div>
</div>
