<div class="rate-product-wrap">
    {foreach $products as $product}
    <div class="row">
        <div class="col-md-4">
            <a href="{$product['url']}">
                <div>
                    <img src="{$product['image']}" alt="{$product['name']}" title="{$product['name']}"/>
                </div>
                <div class="product-name">
                    {$product['name']}
                </div>
            </a>
        </div>
        <div class="col-md-8">
            <div class="add-review-form js-add-review-form">
                {if !Auth::customer()}
                <div class="cc-form-group _mb-10">
                    <input type="text" placeholder="{t}sf.review.client_name{/t}" name="user_name" class="_field">
                </div>
                <div class="cc-form-group _mb-10">
                    <input type="text" placeholder="{t}sf.review.client_email{/t}" name="user_email" class="_field">
                </div>
                {/if}
                <div class="cc-form-group _mb-10">
                    <input type="text" placeholder="{t}sf.review.title{/t}" name="title" class="_field">
                </div>
                <div class="cc-form-group _mb-10">
                    <textarea class="_textarea" name="comment" id="" cols="30" rows="10" placeholder="{t}sf.review.add_comment{/t}"></textarea>
                    {if Auth::customer()}
                    <input type="hidden" name="user_name" value="{Auth::customer()->first_name} {Auth::customer()->last_name}">
                    <input type="hidden" name="user_email" value="{Auth::customer()->email}">
                    {/if}
                </div>
                <br/>
                <div class="text-right">
                    <span data-dismiss="panel" class="_button _button-secondary">
                        <span class="_figure-stack">
                            <span class="_figure-stack-label">{t}sf.global.act.cancel{/t}</span>
                        </span>
                    </span>
                    <a href="javascript:;" class="_button js-disabled js-loading js-submit-review _md-mt-0 _md-ml-10 _mt-10" rel="noindex nofollow">
                        <span class="_figure-stack">
                            <span class="_figure-stack-label">{t}sf.review.publish{/t}</span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    {/foreach}
</div>
<script>
    $('.js-submit-review:not(.loading)').on('click', function() {
        let $this = $(this);
        let data = {
            user_name: $this.closest('.js-add-review-form').find('[name="user_name"]').val(),
            user_email: $this.closest('.js-add-review-form').find('[name="user_email"]').val(),
            rating: $this.closest('.js-add-review-form').find('[name="rating"]').val(),
            comment: $this.closest('.js-add-review-form').find('[name="comment"]').val(),
            title: $this.closest('.js-add-review-form').find('[name="title"]').val(),
            product_id: {$product['id']},
        };
        $this.trigger('loading.start');
        CC.ajax({
            url: "/product_preview/questions/submit",
            type: 'POST',
            data: data,
            success: function (res) {
                if(res.status == 'success') {
                    let productIdForRate = data.product_id;
                    let slide = $('[data-slide-id='+productIdForRate+']');
                    let requestReviewWrap = $('._request-review-wrap');
                    $this.prev('[data-dismiss]').trigger('click');
                    toastr.success(res.msg);
                    $(document).trigger('cc.submit.review');
                    if(requestReviewWrap.length) {
                        if(!slide.closest('.owl-item').siblings().length) {
                            slide.closest('._grid-row').remove();
                        }
                        slide.closest('.owl-item').remove();
                        requestReviewWrap.find('.owl-carousel')
                                          .trigger('destroy.owl.carousel')
                                          .removeClass('owl-carousel owl-loaded');
                        requestReviewWrap.find('.owl-stage-outer')
                                          .children()
                                          .unwrap();
                        carouselInit(requestReviewWrap.find('.js-slider'));
                    }
                } else if (res.status == 'error') {
                    if(res.msg) {
                        toastr.error(res.msg);
                        $this.trigger('loading.end');
                        return;
                    }
                    if(!res.field) {
                        toastr.error("{t}sf.review.error.no.buy{/t}");
                        $this.trigger('loading.end');
                        return;
                    }
                    if(res.field["user_name"]) {
                        toastr.error(res.field["user_name"]);
                    }
                    if(res.field["user_email"]) {
                        toastr.error(res.field["user_email"]);
                    }
                    if(res.field["rating"]) {
                        toastr.error(res.field["rating"]);
                    }
                    if(res.field["title"]) {
                        toastr.error(res.field["title"]);
                    }
                    if(res.field["comment"]) {
                        toastr.error(res.field["comment"]);
                    }
                }
                $this.trigger('loading.end');
            },
            error: function (err) {
                let errors = err.responseJSON.errors;
                if(err.responseJSON.msg) {
                    toastr.error(err.responseJSON.msg);
                    $this.trigger('loading.end');
                    return;
                }
                if(!errors) {
                    toastr.error("{t}sf.review.error.no.buy{/t}");
                    $this.trigger('loading.end');
                    return;
                }
                if(errors["user_name"]) {
                    toastr.error(errors["user_name"][0]);
                }
                if(errors["user_email"]) {
                    toastr.error(errors["user_email"][0]);
                }
                if(errors["rating"]) {
                    toastr.error(errors["rating"][0]);
                }
                if(errors["title"]) {
                    toastr.error(errors["title"][0]);
                }
                if(errors["comment"]) {
                    toastr.error(errors["comment"][0]);
                }
                $this.trigger('loading.end');
            }
        });
    });

    function carouselInit($container) {
        var $slider = $('.js-slides', $container);

        $slider.each(function () {
            var $this = $(this);

            if ($this.find('.js-slide').length > 1) {
                $this.find('.js-slide:first-child').imagesLoaded(function () {
                    var hasNav = $this.data('nav');
                    var hasDots = $this.data('dots');
                    var hasHoverPause = $this.data('pause');
                    var interval = $this.data('interval');
                    var isAutoplaying = $this.data('autoplay');
                    var isCycling = $this.data('cycle');
                    var isRTL = $this.data('rtl');
                    var items = typeof $this.data('items') !== 'undefined' ? $this.data('items') : 1;
                    var itemsDesktop = typeof $this.data('items-desktop') !== 'undefined' ? $this.data('items-desktop') : items;
                    var margin = typeof $this.data('margin') !== 'undefined' ? $this.data('margin') : 0;
                    var slideSpeed = typeof $this.data('paginationSpeed') !== 'undefined' ? $this.data('paginationSpeed') : 700;
                    var iconPrev = !isRTL ? 'left' : 'right';
                    var iconNext = !isRTL ? 'right' : 'left';

                    $this.owlCarousel({
                        items: items,
                        video: true,
                        videoWidth: false,
                        videoHeight: false,
                        mouseDrag: false,
                        autoHeight: true,
                        rtl: isRTL,
                        nav: hasNav,
                        navRewind: false,
                        dots: hasDots,
                        margin: margin,
                        loop: isCycling,
                        autoplay: isAutoplaying,
                        autoplayTimeout: interval,
                        smartSpeed: slideSpeed,
                        responsive: {
                            0: {
                                items: 1
                            },
                            768: {
                                items: 1
                            },
                            960: {
                                items: 1
                            },
                            1200: {
                                items: itemsDesktop
                            }
                        },
                        autoplayHoverPause: hasHoverPause,
                        navText: [
                            '<span class="glyphicon glyphicon-chevron-' + iconPrev + '"></span>',
                            '<span class="glyphicon glyphicon-chevron-' + iconNext + '"></span>'
                        ],
                        onResized: function () {
                            $this.find('.owl-stage-outer').height($this.find('.active').height());
                            setOwlStageHeight($this);
                        },
                        onInitialized: function () {
                            $this.closest('.js-slider').addClass('slider-loaded');
                            setOwlStageHeight($this);
                        },
                        onTranslated: function(){
                            setOwlStageHeight($this);
                        }
                    });
                });

                if ($this.find('.js-slide-video').length && $this.find('.js-slide').length === 1) {
                    $this.closest('.js-slider').addClass('slider-video-only');
                }
            } else {
                $this.closest('.js-slider').addClass('slider-loaded no-slider');
            }
        });

        $('.js-slider', $container).on('click', '.js-slide', function (e) {
            var $this = $(this);
            var target = $this.data('href');

            if (target) {
                if ($this.data('target') !== '_blank') {
                    window.location.href = target;
                } else {
                    window.open(target, '_blank');
                }

                e.preventDefault();
            }
        });

        function setOwlStageHeight(el) {
            var maxHeight = 0;
            $(el.find('.owl-item.active')).each(function () { // LOOP THROUGH ACTIVE ITEMS
                var thisHeight = parseInt( $(this).height() );
                maxHeight=(maxHeight>=thisHeight?maxHeight:thisHeight);
            });
            $(el.find('.owl-carousel')).css('height', maxHeight );
            $(el.find('.owl-stage-outer')).css('height', maxHeight ); // CORRECT DRAG-AREA SO BUTTONS ARE CLICKABLE
        }
    }
</script>