{literal}
	<div class="_disqus-comments">
		<div id="disqus_thread"></div>
	</div>
<script>
	var disqus_config = function () {{/literal}
		this.page.url = '{Linker::fullLink('/product/'|cat:$product->url_handle)}';
		this.page.identifier = '{md5('cloudcart'|cat:$product->id)}';
		{literal}
	};
	(function () {
		var d = document, s = d.createElement('script');
		{/literal}
		s.src = '//{Apps::setting('disqus_comments', 'url')}.disqus.com/embed.js';
		{literal}
		s.setAttribute('data-timestamp', +new Date());
		(d.head || d.body).appendChild(s);
	})();
</script>
	<noscript>Please enable JavaScript to view the <a href="https://disqus.com/?ref_noscript" rel="noindex nofollow">comments powered by Disqus.</a></noscript>
{/literal}