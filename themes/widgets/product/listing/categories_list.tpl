{$parent_ids = W::productsListing()->getCategoryParentIds($widget->filters->getFilter('category_id'))}
{foreach $items as $item}
    {$extra_class = ""}

    {if $item->id == $widget->filters->getFilter('category_id')|| in_array($item->id, $parent_ids)}
        {$active_class = " active opener"}
        {if $level == 0}
            {$extra_class = " _list-current"}
        {else}
            {$extra_class = ""}
        {/if}
        {assign "categories_collapsed_class" " _list-collapsed" scope=root}
    {else}
        {$active_class = ""}
    {/if}

    <li class="_filter-category-list{$active_class}{if !empty($item->child)} item-collapse{/if}{$extra_class}">
        <a class="_filter-category-list-item"
           href="{if W::productsListing()->isDefaultContent('category')}javascript:;{else}{W::filters()->generateLink('category', $item->url_handle)}{/if}">{$item->name}</a>
        {if !empty($item->child)}

        	{if $item->id == $widget->filters->getFilter('category_id') || in_array($item->id, $parent_ids)}
                <span class="_collapse _collapse-active js-collapse js-filter-category-list-item-toggle">&nbsp;</span>
            {else}
                <span class="_collapse js-collapse js-filter-category-list-item-toggle">&nbsp;</span>
            {/if}
			<ul>
                {include file="widgets/product/listing/categories_list.tpl" items=$item->child level=$level+1}
            </ul>
        {/if}
    </li>
{/foreach}