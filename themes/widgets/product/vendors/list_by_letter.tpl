{if $vendors_by_letter->count()}
    <div class="_vendors-list">
        {if isset($thumb_size)}
            {$image_size = $thumb_size}
        {else}
            {$image_size = $image_size|default:"600x600"}
        {/if}
        {foreach $vendors_list as $vendor}
            <div class="_vendor">
                {if !isset($vendor_name_tag)}
                    <a class="_vendor-link" href="{$vendor->url()}">
                        <span class="_vendor-image">
                            <span class="_vendor-image-thumb"{if $backgroundImage|default} style="background-image: url({$vendor->getImage($thumb_size)})"{/if}>
                                <img
                                        src="{noImageVendor($image_size)}" class="lazyload-image lazyload-{$vendor->getOrientation()}" data-src="{$vendor->getImage($image_size)}"
                                        {if !empty($image_srcset)}
                                            {$default_width = "x"|explode:$image_size}
                                            srcset="{foreach from=$image_srcset item=$size}
                                                   {$width="x"|explode:$size}
                                                   {$vendor->getImage($size)} {$width[0]}w{if not $size@last}, {/if}
                                                {/foreach}"
                                            sizes="(max-width: {$default_width[0]}px) 100vw, {$default_width[0]}px"
                                        {/if}
                                        alt="{$vendor->name}" title="{$vendor->name}">
                            </span>
                        </span> <span class="_vendor-info">
                            <span class="_vendor-name">{$vendor->name}</span>
                            {if $description|default && $vendor->description|default}
                                <span class="_vendor-description">{$vendor->description nofilter}</span>
                            {/if}
                        </span> </a>
                {else}
                    <div class="_vendor-image">
                        <a class="_vendor-link" href="{$vendor->url()}">
                            <span class="_vendor-image-thumb"{if $backgroundImage|default} style="background-image: url({$vendor->getImage($thumb_size)})"{/if}>
                                <img
                                        src="{noImageVendor($image_size)}" class="lazyload-image lazyload-{$vendor->getOrientation()}" data-src="{$vendor->getImage($image_size)}"
                                        {if !empty($image_srcset)}
                                            {$default_width = "x"|explode:$image_size}
                                            srcset="{foreach from=$image_srcset item=$size}
                                                   {$width="x"|explode:$size}
                                                   {$vendor->getImage($size)} {$width[0]}w{if not $size@last}, {/if}
                                                {/foreach}"
                                            sizes="(max-width: {$default_width[0]}px) 100vw, {$default_width[0]}px"
                                        {/if}
                                        alt="{$vendor->name}" title="{$vendor->name}">
                            </span>
                        </a>
                    </div>
                    <div class="_vendor-info">
                        <{$vendor_name_tag} class="_vendor-name">
                            <a href="{$vendor->url()}">{$vendor->name}</a>
                        </{$vendor_name_tag}>
                        {if $description|default && $vendor->description|default}
                            <span class="_vendor-description">{$vendor->description nofilter}</span>
                        {/if}
                    </div>
                {/if}
            </div>
        {/foreach}
    </div>
{else}
    <div class="_notification">
        <p>{t}sf.global.no_results{/t}</p>
    </div>
{/if}