{$small = null}
{if $smButton|default}
    {$small = '_button-small'}
{/if}

{if $list_widget_settings['listing_show_buy'] && showPriceForUser()}
    <div class="_product-add {if !empty($product->price_from_discounted_formatted) || $product->diff_prices} variable-price{/if} {if $buttonWrapClasses|default}{$buttonWrapClasses|default}{/if}">
        {if $product->total_variants <= 0}
            {if $product->variant && $product->variant->price}
                {if supportNewSpinner() && $product->status_type == 'in_stock'}
                    <div class="js-list-quantity-change js-list-quantity-change-{$product->id}" data-spinner-id="{$product->id}" style="display: none">
                    {include file=base_path("themes/_global/templates/product/quantity-select/listing.tpl") product=$product idSuffix="{$product->id}"}
                    </div>
                {/if}
                {call renderProductButton}
            {else}
                {call renderQuickPreviewButton}
            {/if}
        {elseif $product->overlay || $product->status_type === 'out_stock'}
            {call renderOutOfStockButton}
        {else}
            {if !$product->variant_id}
                {call renderDetailsButton}
            {else}
                {call renderVariantSelectedButton}
            {/if}
        {/if}
    </div>
{/if}

{* Define reusable functions below *}

{function name=renderProductButton}
    {if in_array($product->status_type, ['request', 'subscribe'])}
        <span class="_button {$small} {$buttonClasses|default} js-disabled js-loading"
              {if $product->status_type == 'subscribe'} data-url="/subscribe-for-missing-product/{$product->url_handle}?variant_id={$product->default_variant_id}"
              {else} data-url="{Linker::contacts($product->url_handle)}" {/if}
              title="{$product->status_name}" data-ajax-modal="true">
            <span class="_figure-stack">
                <span class="_figure-stack-label">{$product->status_button}</span>
            </span>
        </span>
    {elseif $product->overlay || $product->status_type == 'out_stock'}
        {call renderOutOfStockButton}
    {else}
        <button class="_button {$small} {$buttonClasses|default} add-cart-product js-disabled js-loading{if supportNewSpinner()} js-list-quantity-change-button-{$product->id}{/if}"
                data-url="{route('cart.add', $product->variant->id)}"
                data-request="{$product->variant->id}" data-ajax="toast"
                title="{strip_tags(__('sf.global.act.add_to_cart'))}" rel="noindex nofollow" disabled>
            <span class="_figure-stack">
                <span class="_figure-stack-label">{$add_content|default:"{t}sf.global.act.add_to_cart{/t}" nofilter}</span>
            </span>
        </button>
    {/if}
{/function}

{function name=renderQuickPreviewButton}
    <button class="_button {$small} {$buttonClasses|default} add-cart-product quick-preview js-disabled js-loading"
            data-url="{$product->url()}" data-ajax-modal="true" data-modal-class="product-details"
            title="{strip_tags(__('sf.global.act.quick_view'))}" rel="noindex nofollow" disabled>
        <span class="_figure-stack">
            <span class="_figure-stack-label">{t}sf.global.act.quick_view{/t}</span>
        </span>
    </button>
{/function}

{function name=renderOutOfStockButton}
    <span class="_button {$small} {$buttonClasses|default} _button-secondary">
        <span class="_figure-stack">
            <span class="_figure-stack-label">{t}sf.global.label.out_of_stock{/t}</span>
        </span>
    </span>
{/function}

{function name=renderDetailsButton}
    <button class="_button {$small} {$buttonClasses|default} js-disabled js-loading" data-url="{$product->url()}" data-ajax-modal="true"
            data-modal-class="product-details" title="{strip_tags(__('sf.global.act.view_product_details'))}"
            disabled>
        <span class="_figure-stack">
            <span class="_figure-stack-label">{t}sf.global.act.view_product_details{/t}</span>
        </span>
    </button>
{/function}

{function name=renderVariantSelectedButton}
    <button class="_button {$small} {$buttonClasses|default} add-cart-product js-disabled js-loading{if supportNewSpinner()} js-list-quantity-change-button-{$product->id}{/if} js-variant-is-selected"
            data-variant_id="{$product->variant_id}" data-v1_id="{$product->v1_id}" data-v2_id="{$product->v2_id}" data-v3_id="{$product->v3_id}" data-ajax="toast"
            title="{strip_tags(__('sf.global.act.view_product_details'))}" rel="noindex nofollow" disabled>
        <span class="_figure-stack">
            <span class="_figure-stack-label">{t}sf.global.act.view_product_details{/t}</span>
        </span>
    </button>
{/function}
