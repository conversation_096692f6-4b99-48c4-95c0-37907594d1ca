{if !empty($widget->getBlogs())}
	<div class="_blog-categories">
		<ul data-class="selectTest">
			{foreach $widget->getBlogs() as $blog}
				<li{if !empty($widget->getBlog()) && $widget->getBlog()->id == $blog->id} class="active"{/if}>
					<a href="{$blog->url()}">
						{if $printImage|default}
							{if $blog->hasImage()}
								<span class="_blog-category-image">
									<img src="{$blog->getImage('150x150')}" alt="{$blog->name}" title="{$blog->name}"/>
								</span>
							{/if}
						{/if}
						<span class="_blog-category-name {if isset($printImage)}_blog-category-name-image{/if}">
							{$blog->name}
						</span>
					</a>
					{if !empty($widget->getBlog()) && $widget->getBlog()->id == $blog->id}
						<a {route('blog.list')} class="_filter-remove">+</a>
					{/if}
				</li>
			{/foreach}
		</ul>
	</div>
{else}
	<div class="_notification">
		<p>{t}sf.widget.blog.nfy.no_categories{/t}</p>
	</div>
{/if}