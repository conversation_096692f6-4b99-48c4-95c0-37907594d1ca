{if $checkoutProvider == 'iute'}
    <input type="hidden" name="payment_variant_id" value="3">
{else}
    <div class="cc-form-row">
        <div class="cc-form-col-12">
            <div class="cc-box">
                <div class="cc-totals">
                    {if !$error|default && $payment_variant_id}
                        <div class="cc-totals-row">
                        <span class="cc-totals-title">
                            <span class="cc-totals-name">{t}sf.widget.leasing.num_payments{/t}</span>
                        </span>

                            <span class="cc-totals-value">{$scheme->Maturity nofilter} {t}sf.global.months{/t}</span>
                        </div>
                        <div class="cc-totals-row">
                        <span class="cc-totals-title">
                            <span class="cc-totals-name">{t}sf.widget.leasing.monthly_payment{/t}</span>
                        </span>

                            <span class="cc-totals-value">{$scheme->InstallmentAmountFormatted nofilter}</span>
                        </div>
                        {if $scheme->CorrectDownPaymentAmount}
                            <div class="cc-totals-row">
                            <span class="cc-totals-title">
                                <span class="cc-totals-name">{t}sf.leasing.buy.ph.initial{/t}</span>
                            </span>

                                <span class="cc-totals-value">{$scheme->CorrectDownPaymentAmountFormatted nofilter}</span>
                            </div>
                        {/if}
                        <div class="cc-totals-row">
                        <span class="cc-totals-title">
                            <span class="cc-totals-name">{t}sf.widget.leasing.air{/t}</span>
                        </span>

                            <span class="cc-totals-value">{$scheme->NIRFormatted nofilter}</span>
                        </div>
                        <div class="cc-totals-row">
                        <span class="cc-totals-title">
                            <span class="cc-totals-name">{t}sf.widget.leasing.alf{/t}</span>
                        </span>

                            <span class="cc-totals-value">{$scheme->APRFormatted nofilter}</span>
                        </div>
                        <div class="cc-totals-row">
                        <span class="cc-totals-title">
                            <span class="cc-totals-name">{t}sf.widget.leasing.total_payment{/t}</span>
                        </span>

                            <span class="cc-totals-value"><strong>{$scheme->TotalRepaymentAmountFormatted nofilter}</strong></span>
                        </div>
                    {elseif $error|default}
                        <div class="cc-box note warning">
                            <ul>
                                <li>{$error}</li>
                            </ul>
                        </div>
                    {/if}

                    <div class="cc-totals-row">
                        <span class="cc-totals-title">&nbsp;</span>

                        <span class="cc-totals-value">
                        {if $payment_variant_id}
                            {$edit_link = __('sf.global.act.edit')}
                            {$class = ''}
                            {$trigger = false}
                        {else}
                            {$edit_link = __('sf.widget.leasing.select_plan')}
                            {$class = 'js-creditor-select-plan'}
                            {$trigger = true}
                        {/if}

                        <a class="{$class}" href="{route('checkout.creditor.edit', $checkoutProvider)}" data-ajax-panel
                           data-ajax-panel-history="{json_encode(['old' => route('checkout'),'new' => "{route('checkout.creditor.edit', $checkoutProvider)}"])}">{$edit_link}</a>
                    </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {if $trigger}
        {script}
            <script>
                jQuery(document).off('.creditor.select.plan.{$checkoutProvider}').on('click.creditor.select.plan.{$checkoutProvider}', '.js-accordion-payment-provider-credit.open [data-accordion-title="{$checkoutProvider}"]', function () {
                    jQuery('.js-accordion-payment-provider-{$checkoutProvider} .js-creditor-select-plan').trigger('click');
                });
            </script>
        {/script}
    {/if}
{/if}