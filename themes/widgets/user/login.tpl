{capture "forgotten_password"}
    {if !isset($hide_forgotten_password_link)}
		<a class="_meta-link-forgotten-password" href="{route('site.auth.forgotten')}" {if Request::ajax()}data-ajax-box-load=".popup-login-box"{/if}>{t}sf.widget.user.login.act.forgotten_password_link{/t}</a>
    {/if}
{/capture}
<div class="_login">
	<div class="_form">
		<form class="js-form-submit-ajax" role="form" method="POST" action="{route('site.auth.login')}">
            {if $checkout_mode|default == "express"}
				<input type="hidden" name="checkout_express" value="1">
            {/if}
			<div class="_form-row">
				<div class="_form-col">
					<input type="email" class="_field" name="email" placeholder="{t}sf.global.ph.email{/t}" required>
				</div>
				<div class="_form-col">
					<input type="password" class="_field" name="password" placeholder="{t}sf.global.ph.password{/t}" required>
				</div>
			</div>
			<div class="_form-row _form-row-actions">
				<div class="_form-col">
					<label class="_checkbox">
						{*<input type="checkbox" name="remember" value="1"{if isset($rememberme_uicontrol)} data-uicontrol="{$rememberme_uicontrol}"{/if}>*}
						<input type="checkbox" name="remember" value="1" data-uicontrol="uniform">
						{t}sf.widget.user.login.label.remember_me{/t}
					</label>
				</div>
				<div class="_form-col">
					<div class="_form-actions">
						<button class="_button js-loading" type="submit">
							<span class="_figure-stack">
								<span class="_figure-stack-label">{t}sf.widget.user.login.act.sign_in{/t}</span>
								<i class="_figure-stack-icon fa fa-arrow-circle-right"></i>
							</span>
						</button>
					</div>
				</div>
			</div>
		</form>
	</div>
	<div class="_meta-links _meta-links-right">
        {if !isset($hide_register_link)}
			<a class="_meta-link-register" href="{route('site.auth.register')}" {if Request::ajax()}data-ajax-box-load=".popup-login-box"{/if}>{t}sf.widget.user.login.act.register_link{/t}</a>
        {/if}
        {if $forgotten_position|default != 'above'}
            {$smarty.capture.forgotten_password nofilter}
        {/if}
	</div>
</div>
