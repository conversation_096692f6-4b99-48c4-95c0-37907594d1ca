{if !Request::ajax()}




	<!-- BEGIN: wrapper -->
	<div class="_wrapper">

		<!-- BEGIN: header -->
		<div class="_header">
			<div class="_header-top-line-wrapper">
				<div class="container">
					<div class="row">
						<div class="col-xs-12">
							<div class="_header-top-line">
								{*<a class="_navigation-hamburger js-navigation-hamburger" data-target-menu="js-header-top-links" href="javascript:void(0)">*}
									{*<span class="_figure-stack">*}
										{*<i class="_figure-stack-icon glyphicon glyphicon-option-vertical"></i>*}
									{*</span>*}
								{*</a>*}
								<div class="_user-menus">
									{include file="widgets/user/controls.tpl" widget=W::authorize()}
								</div>
								{*<div class="_header-top-links js-header-top-links">*}
								<div class="_header-top-links">
									{include file="widgets/navigation/links.tpl" widget=$widget->navigationLinks}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="_header-middle-line-wrapper">
				<div class="container">
					<div class="row">
						<div class="col-xs-12">
							<div class="_header-middle-line">
								{if $widget->search->isEnabled()}
									{include file="widgets/extra/search_form.tpl"}
								{/if}
								{if $widget->headerLeft->isEnabled()}
									{include file="widgets/extra/text.tpl" widget=$widget->headerLeft}
								{/if}
								{include file="widgets/extra/logo.tpl" widget=W::logo()}
								{if $widget->headerRight->isEnabled()}
									{include file="widgets/extra/text.tpl" widget=$widget->headerRight}
								{/if}
								<div class="_cart-wrapper">
									{include file="../cart/compact.tpl"}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="_header-bottom-line-wrapper">
				<a class="_navigation-hamburger _main-navigation-hamburger js-navigation-hamburger" data-target-menu="js-header-main-navigation" href="javascript:void(0)">
					<span class="_figure-stack">
						{*<i class="_figure-stack-icon glyphicon glyphicon-menu-hamburger"></i>*}
						<i class="_figure-stack-icon glyphicon glyphicon-option-vertical"></i>
					</span>
				</a>
				<div class="_cart-mobile">
					{include file="../cart/compact.tpl"}
				</div>
				<div class="_main-navigation js-header-main-navigation">
					{if $widget->search->isEnabled()}
						{include file="widgets/extra/search_form.tpl"}
					{/if}
					{include file="widgets/navigation/main.tpl" widget=W::mainNavigation() dropdown_wrapper=true}
					{include file="widgets/navigation/links.tpl" widget=$widget->navigationLinks}
					{include file="widgets/user/controls.tpl" widget=W::authorize()}
				</div>
			</div>
		</div>
		<!--// END: header -->
{/if}