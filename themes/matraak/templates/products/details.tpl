{if Request::ajax() || $include|default}
	<!-- LOAD: popup widgets -->

	{if $include|default}
		{$product = $include}
	{/if}

	{include file="widgets/common/product_popup.tpl"}

	<!-- BEGIN: popup -->
	<div class="_popup _popup-product-details">
		<div class="container-fluid product-details-js" data-product-id="{$product->id}">
			<form class="add-to-cart-form-js js-form-submit-ajax" action="{route('cart.add')}" method="POST">
				<div class="row">
					<div class="col-md-6">
						<div class="_product-details">
							<div class="row">
								{if $product->is_bundle}
									<div class="_product-details-bundle js-product-fixed-treshold">
										{include file=base_path('themes/_global/templates/product/bundle/choose.tpl') display_button=false product=$product quantity_uicontrol="spinner" select_uicontrol="select2"}
									</div>
								{else}
									{if $product->images->count() > 1}
										<div class="col-xs-4 col-sm-2">
											<div class="swiper-gallery-holder">
												{include file="widgets/product/details/gallery_images_slider.tpl" initSlider=true sliderNavigation=true sliderSlidesPerView="6" sliderDirection="vertical"}
											</div>
										</div>
										<div class="col-xs-8 col-sm-10">
									{else}
										<div class="col-xs-12">
									{/if}
										{include file="widgets/product/details/primary_image.tpl" zoom_uicontrol="jqueryZoom" custom_labels=true}
									</div>
                                {/if}
							</div>
						</div>
					</div>
					<div class="col-md-6">
						<h2 class="js-product-title">{$product->name}</h2>
						{include file="./sidebar.tpl"}
					</div>
				</div>
			</form>
		</div>
	</div>
	<!--// END: popup -->
{else}
	{capture append="js"}
		<script type="text/javascript">
			$('.swiper-button').on('click', function(e) {
				var $sliderWrapper = $('._product-details-gallery'),
					$currentActive = $sliderWrapper.find('.cc-active'),
					$target = $(this);

				if ($sliderWrapper.is(':visible')) {
					return true;
				}

				if ($sliderWrapper.find('.cc-active').length == 0) {
					$currentActive = $sliderWrapper.find('ul li:first-of-type').addClass('cc-active');
				}

				if($target.hasClass('swiper-button-prev')) {
					if ($currentActive.prev().length == 0) {
						$sliderWrapper.find('ul li:last-of-type > a').trigger('click');
					}
					$currentActive.prev().find('> a').trigger('click');
				} else if ($target.hasClass('swiper-button-next')) {
					if ($currentActive.next().length == 0) {
						$sliderWrapper.find('ul li:first-of-type > a').trigger('click');
					}
					$currentActive.next().find('> a').trigger('click');
				}
			});
		</script>
	{/capture}


	{include file="../layout/header.tpl"}
	<!-- BEGIN: content -->
	<div class="_content">
		<div class="container">
			<div class="_section-separator _section-separator--big">
				{include file="./../layout/page-title.tpl" breadcrumbs=$product->breadcrumb active=$product->name}
			</div>
			<div class="product-details-js" data-product-id="{$product->id}">
				<form class="add-to-cart-form-js js-form-submit-ajax" action="{route('cart.add')}" method="POST">
					<div class="row">
						<div class="col-md-9">
							<div class="_product-details">
								<div class="row">
									<div class="col-sm-8">
                                        {if $product->is_bundle}
											<div class="_product-details-bundle js-product-fixed-treshold">
												{include file=base_path('themes/_global/templates/product/bundle/choose.tpl') display_button=false product=$product quantity_uicontrol="spinner" select_uicontrol="select2"}
											</div>
                                        {else}
											{include file="widgets/product/details/primary_image.tpl" zoom_uicontrol="jqueryZoom" custom_labels=true}
											{if $product->images->count() > 1}
												<div class="swiper-gallery-holder">
													{include file="widgets/product/details/gallery_images_slider.tpl" initSlider=true sliderNavigation=true sliderSlidesPerView="7" breakpoints="767" itemsPerBreakpoint="1" spaceBetweenItems="10"}
												</div>
											{/if}
										{/if}
									</div>
									<div class="col-sm-4">
										{include file="./sidebar.tpl" printLabels=true}
									</div>
								</div>
							</div>
							<div class="_product-information">
								{if !empty($product->description)}
									<div class="row">
										<div class="col-sm-12">
											<div class="_product-details-desription">
												<div class="_product-tabber__wrapper">
													{capture append="js"}
														<script type="text/javascript">
															$(window).on('load', function() {
																$('._product-tabber__title-row h6 label').on('click touchstart', function(e) {
																	var $target = $(e.target);

																	$target.parent().addClass('active').siblings().removeClass('active');
																})
															});
														</script>
													{/capture}
													<div class="_product-tabber__title-row">
														{if W::ProductsDetails('show_product_description')}
															<h6 class="active">
																<label for="_tabber-product-description">{$product->description_title|default:"{t}sf.product.details.description_title{/t}"}</label>
															</h6>
														{/if}
														{if W::ProductsDetails('show_SKU') || W::ProductsDetails('show_brand') || W::ProductsDetails('show_category') || W::ProductsDetails('show_page')}
														<h6>
															<label for="_tabber-product-details">{t}sf.global.details{/t}</label>
														</h6>
														{/if}
														{if $product->tags->count()}
															<h6>
																<label for="_tabber-product-tags">{t}sf.global.tags{/t}</label>
															</h6>
														{/if}
														{if W::ProductsDetails('social_media_share')}
														<h6>
															<label for="_tabber-product-share">{t}sf.global.share{/t}</label>
														</h6>
														{/if}
													</div>
													<div class="_product-tabber__content-row">
														<input type="radio" name="tabber-radio" class="_tabber-radio-buttons" id="_tabber-product-description" checked>
														<div data-article-content data-language="{locale()}">
															{$product->description nofilter}
														</div>
														<input type="radio" name="tabber-radio" class="_tabber-radio-buttons" id="_tabber-product-details">
														<div>
															<div class="_product-details-meta">
																<ul>
																	{if W::ProductsDetails('show_SKU')}
																		<li>
																			<div class="_product-details-sku variant-sku-js">{t}sf.global.label.sku{/t} <i></i></div>
																		</li>
																	{/if}
																	{if !empty($product->vendor) && W::ProductsDetails('show_brand')}
																		<li>
																			<span class="_product-details-meta-title">{t}sf.global.label.vendor{/t}</span>
																			<span class="_product-details-meta-value">
																				<a href="{$product->vendor->url()}">
																					{$product->vendor->name}
																				</a>
																			</span>
																		</li>
																	{/if}
																	{if !empty($product->category) && W::ProductsDetails('show_category')}
																		<li>
																			<span class="_product-details-meta-title">{t}sf.global.label.category{/t}</span>
																			<span class="_product-details-meta-value">
																				<a href="{$product->category->url()}">
																					{$product->category->name}
																				</a>
																			</span>
																		</li>
																	{/if}
																	{if W::ProductsDetails('show_page')}
																		{$page = W::ProductsDetails()->getInformationPage()}
																		{if $page}
																			<li>
																				<span class="_product-details-meta-title"></span>
																				{if W::ProductsDetails('show_link_as_popup')}
																					{$popup_url = $page->url()}
																					<span class="_product-details-meta-value"><a href="{$popup_url}" data-ajax-panel="true">{$page->name}</a></span>
																				{else}
																					{$page_url = $page->url()}
																					<span class="_product-details-meta-value"><a href="{$page_url}">{$page->name}</a></span>
																				{/if}
																			</li>
																		{/if}
																	{/if}
																	{if !empty($product->size_chart)}
																		<li>
																			<a href="{route('page', $product->size_chart['url_handle'])}" class="_product-details-size-chart" data-ajax-panel  target="_blank">{$product->size_chart['name']}</a>
																		</li>
																	{/if}
																</ul>
															</div>

															{if W::ProductsDetails('show_categories_characteristics') && !W::categoryProperties()->getPropertiesForProduct($product)->isEmpty()}
																{include file="widgets/product/details/category_properties.tpl" properties=W::categoryProperties()->getPropertiesForProduct($product)}
															{/if}
														</div>
														<input type="radio" name="tabber-radio" class="_tabber-radio-buttons" id="_tabber-product-tags">
														<div>
															{if $product->tags->count()}
																{include file="widgets/common/tags.tpl" tags=$product->tags}
															{/if}
														</div>
														{if W::ProductsDetails('social_media_share')}
															<input type="radio" name="tabber-radio" class="_tabber-radio-buttons" id="_tabber-product-share">
															<div>
																{include file="widgets/extra/share.tpl" widget=W::share()}
															</div>
														{/if}
													</div>
												</div>
											</div>
										</div>
									</div>
								{/if}
								{if Apps::enabled('yotpo')}
									<div class="_section-separator">
										<div class="row">
											<div class="col-md-12">
												{\App\Models\System\AppsManager::getManager('yotpo')->render($product) nofilter}
											</div>
										</div>
									</div>
								{/if}
								{if Apps::enabled('facebook_comments')}
									<div class="_section-separator">
										<div class="row">
											<div class="col-sm-12">
												{include file="widgets/product/details/comments-facebook.tpl"}
											</div>
										</div>
									</div>
								{/if}
								{if Apps::enabled('disqus_comments')}
									<div class="_section-separator">
										<div class="row">
											<div class="col-sm-12">
												{include file="widgets/product/details/comments-disqus.tpl"}
											</div>
										</div>
									</div>
								{/if}
							</div>
						</div>
						<div class="col-md-3">
							{if W::productsRelated()->isEnabled() && W::productsRelated()->getProducts($product)->count()}
								<div class="_related-products-wrapper">
									<div class="row">
										<div class="col-sm-12">
											<div class="_product-details-relative">
												{include file="widgets/product/list.tpl" utilities=W::utilities() products=W::productsRelated()->getProducts($product) widget=W::productsRelated() custom_labels=true}
											</div>
										</div>
									</div>
								</div>
							{/if}
							{if	$widget->bannerInSidebar->isEnabled()}
								<div class="_multiple-banners">
									{include file="widgets/extra/banner.tpl" widget=$widget->bannerInSidebar}
								</div>
							{/if}
						</div>
					</div>

					{if Apps::setting('instagram', 'active_in_details_page')}
						<div class="container _section-separator">
							<div class="row">
								<div class="col-md-12">
									{Instagram::images($product) nofilter}
								</div>
							</div>
						</div>
					{/if}

                    {if Widget::has('productInBundles') && Widget::get('productInBundles')->isEnabled() && Widget::get('productInBundles')->getBundles()->count()}
						<div class="container _section-separator">
							<div class="row">
								<div class="col-md-12">
									<div class="_section-title">
										<h2>{t}sf.product.product_in_bundle{/t}</h2>
									</div>

                                    {include file="{base_path('themes/widgets/product/bundleProducts.tpl')}" bundles=Widget::get('productInBundles')}
								</div>
							</div>
						</div>
                    {/if}
				</form>
			</div>
		</div>

		<!-- LOAD: seo -->
		{*
{include file="widgets/common/microdata/product.tpl" related_products_widget=W::productsRelated()}
*}
		{include file="widgets/common/microdata/breadcrumb.tpl" breadcrumbs=$product->breadcrumb active=$product->name}

	</div>
	<!--// END: content -->
	{include file="../layout/footer.tpl"}
{/if}
