{include file="../layout/header.tpl"}

{$widget->setSeo("products")}
<!-- BEGIN: content -->
<div class="_content">
	<div class="container">
		<div class="_section-separator _section-separator--big">
			{$category = W::filters()->getCategory()}
			{$vendor = W::filters()->getVendor()}
			{$breadcrumbs = W::productsListing()->getBreadcrumbs()}
			{$active = W::productsListing()->getBreadcrumbsActive()}
			{$catAndVend = (!empty($category->description) && !empty($vendor->description))}

			{if	$catAndVend}
				{$title = {$category->name}|cat: ' & '|cat: {$vendor->name}}
			{elseif	!empty($category) && !empty($category->description)}
				{$title = $category->name}
			{elseif	!empty($vendor) && !empty($vendor->description)}
				{$title = $vendor->name}
			{else}
				{$title = "{t}sf.global.act.products{/t}"}
			{/if}

			{include file="./../layout/page-title.tpl" breadcrumbs=$breadcrumbs active=null difference=$title}
		</div>
		{if !empty($category->description) || !empty($vendor->description)}
			<div class="row">
				<div class="col-md-12">
					<div class="_products-list-head">
						{if !empty($category->description)}
							<div class="_products-list-head-item">
								{if $category->hasImage()}
									<div class="_products-list-head-item-image">
										<div class="_products-list-head-item-image-thumb">
											<img src="{$category->getImage("300x300")}" alt="{$category->name} {t}sf.global.image{/t}" title="{$category->name}" />
										</div>
									</div>
								{/if}
								<div class="_products-list-head-item-description _textbox">
									{$category->description nofilter}
								</div>
							</div>
						{/if}
						{if !empty($vendor->description)}
							<div class="_products-list-head-item">
								<div class="_products-list-head-item-description _textbox">
									{$vendor->description nofilter}
								</div>
							</div>
						{/if}
					</div>
				</div>
			</div>
		{/if}
		<div class="_filters__wrapper">
			<div class="row">
				<div class="col-sm-12">
					<div class="_filters-row js-sidebar-filters">
						<div class="_button-secondary _filter-button js-sidebar-trigger js-active">
							<p>{t}sf.products.act.filters{/t}</p>
						</div>
						<div class="_products-list-options">
							{if $widget->filters->shouldShowFilter('sort') || $widget->filters->shouldShowFilter('per_page_filter')}
								{if $widget->filters->shouldShowFilter('sort') && !activeRoute('products.search')}
									<div class="_option-box">
                                        {include file="widgets/product/listing/orderbysort_ajax.tpl"}
									</div>
								{/if}
								{if $widget->filters->shouldShowFilter('per_page_filter')}
									<div class="_option-box">
										{include file="widgets/product/listing/perpage_ajax.tpl"}
									</div>
								{/if}
							{/if}
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-md-12">
				<div class="_products-list-main">
					<div class="_products-list-wrapper js-sidebar-open">
						<div class="_filter-sidebar js-filter-sidebar">
							<div class="_button-secondary _filter-button js-sidebar-trigger js-active">
								<p>{t}sf.global.act.close{/t}</p>
							</div>

                            {include file="./list-sidebar.tpl"}
						</div>
						<div class="_product-list-holder js-products-container js-empty-on-ajax">
							{include file="widgets/product/list.tpl" utilities=W::utilities() products=W::productsListing()->getProducts() widget_products=$widget->products custom_labels=true}
							{include file="widgets/common/pagging-new.tpl" page=W::filters()->getPage() pages=W::productsListing()->getPages() link=W::productsListing()->getLink() elements=W::productsListing()->getPaginator() ajax_link=W::productsListing()->getPaggingLink()}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- LOAD: seo -->
	{include file="widgets/common/microdata/breadcrumb.tpl" breadcrumbs=W::productsListing()->getBreadcrumbs() active=null}


</div><!--// END: content -->
{include file="../layout/footer.tpl"}