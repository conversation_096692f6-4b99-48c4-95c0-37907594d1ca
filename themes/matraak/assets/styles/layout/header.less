/*=============================================================================*\
    HEADER
\*=============================================================================*/

._header {
  background: @color-header-background; /* theme */;
  position: relative;
  z-index: 3;

  &-top-line {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    font-size: @base-xxsmall-font-size;  /* theme */;
    font-weight: @light;
    color: @color-header-top-line-text; /* theme */
    -ltr-text-transform: uppercase;

    a {
      &.active {
        text-decoration: underline;
      }
    }

    &-wrapper {
        background: @color-header-top-line-background; /* theme */
        position: relative;
        padding: 8px 0;

        &:after {
          content: "";
          display: block;
          position: absolute;
          bottom: -5px;
          left:0;
          right:0;
          height: 10px;
          /* TODO Add browser prefixes */
          background:
                  linear-gradient(
                          45deg, transparent 33.333%,
                          @color-header-top-line-background 33.333%, @color-header-top-line-background 66.667%,
                          transparent 66.667%
                  ),
                  linear-gradient(
                          -45deg, transparent 33.333%,
                          @color-header-top-line-background 33.333%, @color-header-top-line-background 66.667%,
                          transparent 66.667%
                  ); /* theme */
          background-size: 8px 20px !important;
          background-position: 0 -10px !important;
        }
    }

    > div {
        flex: none;
    }

    ._header-top-links {
        max-width: 48%;
    }

    ._navigation-hamburger {
      display: none;
    }
  }

  &-middle-line {
    display: flex;
    align-items: center;

    &-wrapper {
      padding: 25px 0 22px;
    }

    > div {
      flex: 1 1 20%;
      max-width: 20%;
    }

    ._text {
      text-align: center;
      padding: 0 10px;

      &-title h6 {
        color: @color-main-accent; /* theme */
        font-size: @font-size-heading-5;
      }

      ._text-description {
        margin-top: 0;
      }
    }

    ._cart-wrapper, ._search-form {
      max-width: 170px;
    }
  }

  ._logo {
    max-width: ~'calc(60% - 340px)';
  }

  ._cart-mobile {
    display: none;
  }

  ._search-form {

    ._field-icon {
      max-width: 165px;

      .special-round-button;

      input {
        background: transparent;
        border: none;
        height: 34px;
        border-radius: 17px;
        font-size: @base-xsmall-font-size;
        color: @color-forms-fields-secondary-placeholder; /* theme */
        font-weight: @normal;
        position: relative;
        z-index: 2;

        &._field {
          &:-moz-placeholder,
          &:-ms-input-placeholder,
          &::-webkit-input-placeholder {
            color: @color-forms-fields-secondary-placeholder; /* theme */
          }
        }
      }

      button {
        border: none;
        width: 36px;
        height: 34px;
        z-index: 2;
        color: @color-main-accent; /* theme */
      }
    }

  }

  ._cart-wrapper {
    text-align: right;
  }

  &-bottom-line-wrapper {
      ._dashed-section(@color-header-navigation-background, @color-header-navigation-border);
  }
}

._main-navigation-hamburger {
  display: none;
}

._main-navigation {

  ._search-form,
  ._navigation-links,
  ._user-controls {
    display: none;
  }
}