/*=============================================================================*\
    FOOTER
\*=============================================================================*/
._horizontal-banners {

  &._multiple-banners {
    ._banners {
      ._banners-list {
          flex-flow: row nowrap;

          ._banner {

            & + ._banner {
              margin-top: 0;
            }

            ._banner-caption {
                font-size: @banner-title;
            }
          }
      }
    }
  }
}

._footer {
  flex: none;
  padding-top: 40px;

  &__banners {
      background-color: @color-footer-banners-background; /* theme */
      padding: 30px 0;

      &.spaceBottom {
        padding-bottom: 65px;
      }
  }

  &__cnt {
    background-color: @color-footer-content-background; /* theme */

    &._no-newsletter {
      
      ._footer__cnt__info {
        padding: 0; 
      }

      ._footer__cnt__navigation, ._footer__cnt__holder {
        padding: 20px 0; 
      }
    }

    &__info {
      padding: 0 65px;
      .row-separator(@color-footer-content-separator);
      font-size: @base-xsmall-font-size;
      line-height: 1.2;
      color: @color-footer-content-text; /* theme */
      text-align: center;

      ._text {
        padding: 10px 0 20px;

        ._text-title {
          margin-top: 10px;
          h6 {
            color: @color-footer-content-title; /* theme */
            ._h4();
          }

        }
      }
    }

    &__navigation {
      padding: 20px 65px 0;
      .row-separator(@color-footer-content-separator);
    }
  }

  &__overlapping {

    &__section {
      ._dashed-section(@color-newsletter-background, @color-newsletter-border);
      background-color: @color-newsletter-background; /* theme */
      position: relative;
      margin-top: 25px;

      > *  {
        margin: 0 auto;
      }
    }

    &__inner {
      margin: 0 20px;
      position: relative;
      top: -20px;
      padding: 0 20px;
      background-color: @color-newsletter-content-background; /* theme */

      &:before,
      &:after {
        content: '';
        display: block;
        position: absolute;
        bottom: -20px;
      }

      &:before {
        border-right-width: 20px;
        border-right-style: solid;
        border-right-color: @color-newsletter-shadow; /* theme */
        border-bottom: 20px solid transparent;
        left: 0;
      }

      &:after {
        border-top-width: 20px;
        border-top-style: solid;
        border-top-color: @color-newsletter-shadow; /* theme */
        border-right: 20px solid transparent;
        right: 0;
      }
    }

    &__content {
      position: relative;
      padding: 35px 30px 35px;

      &:after {
        content: '';
        display: block;
        width: 100%;
        height: 20px;
        position: absolute;
        left: 0;
        bottom: -20px;
        background-color: @color-footer-content-background; /* theme */
      }
    }
  }

  ._newsletter {
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
    align-items: center;
    color: @color-newsletter-text; /* theme */

    ._newsletter-title {
      flex: none;
      order: 1;
      text-align: right;
      margin-right: 15px;

      h6 {
        font-size: @font-size-xs-heading-3;
        color: @color-newsletter-title; /* theme */
      }
    }

    ._newsletter-form {
      flex: none;
      order: 2;
      width: 400px;

      ._field {
        background: @color-newsletter-field-background; /* theme */
        border-color: @color-newsletter-field-border; /* theme */
        color: @color-newsletter-field-text; /* theme */

        &:-moz-placeholder {
          color: @color-newsletter-field-placeholder; /* theme */
        }
        &:-ms-input-placeholder {
          color: @color-newsletter-field-placeholder; /* theme */
        }
        &::-webkit-input-placeholder {
          color: @color-newsletter-field-placeholder; /* theme */
        }
      }

      ._button {
        background: @color-newsletter-button-background; /* theme */
        border-color: @color-newsletter-button-background; /* theme */
        color: @color-newsletter-button-text; /* theme */

      }
    }

    ._newsletter-description {
      .row-top-separator(@color-newsletter-content-border);
      padding-top: 25px;
      order: 3;
      width: 100%;
      text-align: center;
    }
  }

  &__copy {

    &__holder {
      padding: 20px 65px;
      color: @color-footer-copy-text; /* theme */

      a {
        color: @color-footer-copy-link; /* theme */
      }
    }
  }

  ._footer-boxes {
    .flexy-row(30px);

    ._footer-box {

      .flexy-col();

      h6 {
        font-weight: @bold;
        //color: @color-footer-secondary-text; /* theme */
        text-transform: uppercase;
        & when (@rtl) {
          text-transform: none;
        }
      }

      ._opc,
      ._copyright {
        text-align: center;
      }

      &-powered {
        text-align: right;
      }

      ._copyright,
      ._powered,
      ._opc {
        vertical-align: top;
        font-size: @base-small-font-size;
      }

      &-social,
      &-copy,
      &-powered {
        vertical-align: middle;
      }
    }

    &._additional-info {
      h6 {
        ._h5();
        //color: @color-footer-secondary-text; /* theme */
        font-weight: @bold;
      }
    }
  }

  ._footer-info {
    //color: @color-footer-navigation-link; /* theme */
    padding-bottom: 30px;
    .cl;

    a {
      //color: @color-footer-navigation-link; /* theme */
    }
  }

  ._navigation-footer {
    padding-bottom: 10px;

    ul {
      margin-left: 0;

      li {
        line-height: 1;

        a {
          display: inline-block;
          color: @color-footer-navigation-link; /* theme */

          span {
            display: inline-block;
            line-height: 1.8;
          }
        }
      }
    }

    > ul {
      display: flex;
      flex-flow: row wrap;
      align-items: flex-start;

      > li {
        margin-bottom: 20px;
        width: 33.33%;
        padding-right: 15px;

        &:nth-child(3n) {
          padding-right: 0;
        }

        > a {
          display: inline-block;
          ._h6;
          line-height: 0.9;
          cursor: default;
          color: @color-footer-navigation-title; /* theme */
          font-weight: @medium;
          margin-bottom: 5px;

          span {
            line-height: 1.1;
          }
        }
      }
    }
  }

  ._footer-contacts {

    h6 {
      font-weight: @medium;
      //color: @color-footer-navigation-link; /* theme */
      margin-bottom: 5px;
      line-height: 1;
      text-transform: uppercase;

      & when (@rtl) {
        text-transform: none;
      }
    }

    ._text ._text-description {
      margin-top: 8px;
    }

    p {
      padding-bottom: 10px;
    }
  }

  ._social-icons {

    ul {
      display: flex;
      flex-flow: row wrap;

      li {
        float: none;

        a {
          color: @color-footer-copy-link; /* theme */
        }
      }
    }
  }
}