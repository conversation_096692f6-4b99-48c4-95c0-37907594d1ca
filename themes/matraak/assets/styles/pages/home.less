/*=============================================================================*\
    HOME
\*=============================================================================*/
._latest-blog {
    ._dashed-section(@color-latest-blog-section-wrapper-background, @color-latest-blog-section-wrapper-dashes);
    margin: 60px 0 2px;

    ._section-separator {
        padding: 54px 0 16px;
        margin-top: 0;
    }

    ._latest-blog-title-wrap {
      margin-bottom: 52px;
    }



    ._border-title {

      &:before {
        border-color: @color-latest-blog-section-title-border; /* theme */
      }

      > * {
        font-size: @font-size-heading-1;
        background-color: @color-latest-blog-section-wrapper-background; /* theme */
        color: @color-latest-blog-section-title; /* theme */
      }
    }

    ._text-description,
    ._text-title {
      text-align: center;
    }

    ._text-title {
      h6 {
        ._h2();
        color: @color-latest-blog-section-title; /* theme */
      }
    }

    ._text {
      ._text-description {
        -ltr-text-transform: uppercase;
        font-size: @home-latest-section-article-cnt;
        margin-top: 0;
      }
    }

    ._latest-blog-slides-wrap {
        margin: 0 -15px;
        opacity: 0;
        transform: translate(0, 50px);
        transition: .7s;

        &.revealed {
            opacity: 1;
            transform: translate(0, 0);
        }
    }

    ._blog-list-articles {
        display: flex;
        flex-flow: row nowrap;
        justify-content: center;
        z-index: 1;

        ._blog-list-article {
            display: block;
            min-width: initial;
            max-width: initial;
            flex: initial;
            margin-bottom: 35px;
            margin-top: 0;
            margin-right: 0 !important;

            & + ._blog-list-article {
                margin: 0 0 30px;
            }

            ._blog-list-article-image {
                  padding-bottom: 62.86%;
                  display: flex;
                  text-align: center;
                  overflow: hidden;
                  margin-bottom: 21px;
                  position: relative;

              .blog-list-article-image-thumb {
                    position: absolute;
                    left: 0;
                    top: 0;
                    text-align: center;
                    width: 100%;
                    height: 100%;
              }

              img {
                    display: inline-block;
                    width: auto;
                    max-width: 100%;
                    max-height: 100%;
                  .centerer(true, true);
              }
            }

            ._blog-list-article-text {
              font-size: @base-font-size;
              color: @color-latest-blog-section-text; /* theme */
              margin-top: 6px;
              line-height: 1.5;
            }

            ._blog-list-article-info {
                display: flex;
                flex-flow: column nowrap;
                align-items: center;
                //color: @color-contrast-section-title; /* theme */

                > div {
                    width: 100%;
                }

                ._blog-list-article-title {
                    order: 2;

                    h3 {
                        font-family: @font-family-base;
                        ._h5;
                        line-height: 1.5;
                        color: @color-latest-blog-section-title; /* theme */
                        font-weight: @medium;

                        & when (@rtl) {
                            text-transform: none;
                        }
                        a:hover {
                          text-decoration: underline;
                        }
                    }
                }

                ._blog-list-article-meta {
                    display: none;
                }

                ._blog-list-article-viewmore {
                    order: 3;
                    font-size: @base-xxsmall-font-size;
                    margin-top: 7px;

                    a {
                        font-size: @base-xxsmall-font-size;
                        font-weight: @light;
                        position: relative;
                        margin-top: 10px;
                        text-transform: uppercase;

                        & when (@rtl) {
                            text-transform: none;
                        }

                        &:before {
                          display: none;
                        }
                    }
                }

                ._blog-list-article-meta span,
                ._blog-list-article-title h3,
                ._blog-list-article-viewmore {
                    text-align: center;
                    a {
                        color: @color-latest-blog-section-secondary-text; /* theme */
                    }
                }
            }

          @media (min-width: @screen-desktop) {
            &:hover {
              ._blog-list-article-image {
                .blog-list-article-image-thumb {
                  img {
                    opacity: 1 !important;
                  }
                }
              }
            }
          }
        }
    }

    .swiper-container {
        .swiper-button {
            .centerer(false, true);
            margin: 0;
            height: 40px;
            width: 40px;
            background: transparent;
            display: none;
            text-align: center;
            font-size: @swiper-arrows-size;
            line-height: 25px;
            color: @color-button-text; /* theme */
            overflow: hidden;
            z-index: 2;

            &:before {
                content: '';
                display: block;
                position: absolute;
                left: 0;
                top: 0;
                background: @color-product-swiper-button-background; /* theme */
                opacity: .35;
                width: 100%;
                height: 100%;
            }

            &-prev {
                left: 0;
            }

            &-next {
                right: 0;
            }

            i {
                position: absolute;
                .centerer(true, true);
            }
        }
    }
}

._home-welcome {
    ._dashed-section(@color-welcome-wrapper-background, @color-welcome-wrapper-dashes);
    position: relative;
    z-index: 2;

    &__holder {
        position: relative;
        top: -20px;
        background: @color-welcome-background; /* theme */
        padding: 0 20px;
        margin: 0 20px;

        &:before,
        &:after {
            content: '';
            display: block;
            position: absolute;
            bottom: -20px;
        }

        &:before {
            border-right-width: 20px;
            border-right-style: solid;
            border-right-color: @color-welcome-shadow; /* theme */
            border-bottom: 20px solid transparent;
            left: 0;
        }

        &:after {
            border-top-width: 20px;
            border-top-style: solid;
            border-top-color: @color-welcome-shadow; /* theme */
            border-right: 20px solid transparent;
            right: 0;
        }
    }

    &__content {
        position: relative;
        z-index: 2;

        &:before,
        &:after {
            content: '';
            display: block;
            width: 100%;
            position: absolute;
        }

        &:after {
            height: 20px;
            left: 0;
            bottom: -20px;
            background: @color-main-background; /* theme */
        }

        &.revealed {
            ._text {
                opacity: 1;
                transform: translate(0, 0);
            }
        }
    }

    ._text {
        padding: 33px 30px 35px;
        opacity: 0;
        transform: translate(0, 50px);
        transition: .7s;

        ._text-description {
            display: block;
            color: @color-welcome-text; /* theme */
        }

        ._text-title {
            ._border-title-mxn(@color-welcome-border, @color-welcome-background);

            h6 {
                ._h3;
                color: @color-welcome-text; /* theme */
                -ltr-text-transform: uppercase;
            }
        }

        ._text-description {
            color: @color-welcome-text; /* theme */
        }
    }
}

._home-about {
    ._dashed-section(@color-contrast-section-wrapper-background, @color-contrast-section-wrapper-dashes);
    padding: 54px 0 44px;
    margin-top: 30px;

    ._text {
        opacity: 0;
        transform: translate(0, 50px);
        transition: .4s;

        &.revealed {
            transform: translate(0, 0);
            opacity: 1;
        }
    }

    ._text-title {
        ._border-title-mxn(@color-contrast-section-title-border, @color-contrast-section-wrapper-background);

        > * {
            font-size: @font-size-heading-1;
            color: @color-contrast-section-title; /* theme */
            -ltr-text-transform: uppercase;
        }
    }

    ._text-description {
        color: @color-contrast-section-text; /* theme */
    }
}

._home-bestproducts,
._home-bestsales {
    ._product {
        opacity: 0;
        transition: .4s;

        &.revealed {
            animation: bounceIn 1s;
            opacity: 1;
        }
    }
}