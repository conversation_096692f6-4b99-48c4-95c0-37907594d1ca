/*=============================================================================*\
    BREADCRUMB
\*=============================================================================*/
._breadcrumb-wrapper {
	text-align: center;
	margin: 6px 0 50px;
}

._breadcrumb {
    position: relative;
    display: inline-block;
    padding: 0 20px;
    background: @color-main-background; /* theme */
    top: -2px;

	& when (@rtl) {
		top: -3px;
	}

	ul {
		.clearfix();
		font-size: @base-xsmall-font-size;
        line-height: 1;
		color: @color-breadcrumb-text; /* theme */
		font-weight: @light;
		list-style: none;
        display: flex;
        flex-flow: row wrap;
        justify-content: center;

		li {
            position: relative;
			margin: 0 10px 5px 0;

			&:after {
				content: "\f105";
				float: right;
				margin-left: 10px;
				font-family: FontAwesome;

				& when (@rtl) {
					content: "\f104";
				}
			}
			&:last-child {
				margin-right: 0;
				&:after {
					display: none;
				}
			}
			&.active {
				color: @color-breadcrumb-text-active; /* theme */
			}
		}
	}
}