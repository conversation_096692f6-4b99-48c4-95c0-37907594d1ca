/*=============================================================================*\
    HELPER
\*=============================================================================*/

/*  HELPER PARENT
-------------------------------------------------------------------------------*/

._has-helper {
	position: relative;
	@media (min-width: @screen-desktop) {
		&:hover {
			._helper {
				display: block;
			}
		}
	}
}

/*  HELPER
-------------------------------------------------------------------------------*/

._helper {
	display: none;
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background: rgba(255, 255, 255, .9);
	content: '';
	z-index: 1000;
	._helper-content {
		.centerer(true, true);
		width: 100% * @golden-ratio-ngt;
		background-color: @color-main-helper-background; /* theme */
		padding: 20px 30px 25px;
		text-align: center;
		[class*='helper-'] {
			& + [class*='helper-'] {
				margin-top: 15px;
			}
		}
		._helper-title {}
		._helper-text {}
		._helper-actions {
			._button {
				& + ._button {
					margin-left: 10px;
				}
			}
		}
	}
}