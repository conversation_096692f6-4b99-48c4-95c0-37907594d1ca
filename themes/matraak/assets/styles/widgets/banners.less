/*=============================================================================*\
    BANNERS
\*=============================================================================*/

._banners {
	._banners-list {
		.flexy-row(30px);
		._banner {
			.flexy-col();
			text-align: center;

			._banner-caption {
				margin-top: 10px;
			}
		}
	}
}

.page-home {
    ._banners {
        ._banners-list {
            ._banner {
                transition: .4s;
                opacity: 0;

                &.revealed {
                  animation: bounceIn 1s;
                  opacity: 1;
                }
            }
        }
    }
}

._side-banners {

  ._banners-list {
    display: flex;
    flex-direction: column;

    ._banner {
      display: block;
      margin-bottom: 20px;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }
}