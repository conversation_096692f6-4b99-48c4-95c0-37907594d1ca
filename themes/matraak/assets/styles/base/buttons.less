/*=============================================================================*\
    BUTTONS
\*=============================================================================*/

/*  DEFAULT
-------------------------------------------------------------------------------*/
._button.js-loading {
	.loader-dots {
		display: none;
	}
}

.btn {
	&:active, &.active {
		box-shadow: none;
	}
}

._button, a._button {
	display: inline-block;
	padding-left: 25px;
	padding-right: 25px;
	height: 40px;
	background-color: @color-button-background; /* theme */
	border-width: 1px;
	border-style: solid;
	border-color: @color-button-background; /* theme */
	border-radius: 20px;
	color: @color-button-text; /* theme */
    font-size: @button-size;
	font-weight: bold;
	line-height: normal;
	text-align: center;
    text-transform: uppercase;
	outline: none;

	& when (@rtl) {
		white-space: nowrap;
        text-transform: none;
	}
	@media (min-width: @screen-hover) {
		&:hover {
			color: @color-button-text; /* theme */
			background-color: @color-button-background-hover; /* theme */
			border-color: @color-button-background-hover; /* theme */
		}
	}
	&-active {
		color: @color-button-text; /* theme */
		background-color: @color-main-accent; /* theme */
		border-color: @color-main-accent; /* theme */
	}
	&-secondary {
		background-color: @color-button-second-background; /* theme */
		border-color: @color-button-second-background; /* theme */
		color: @color-button-second-text; /* theme */
		font-weight: @bold;
		@media (min-width: @screen-desktop) {
			&:hover {
              background-color: @color-button-second-background-hover; /* theme */
              border-color: @color-button-second-background-hover; /* theme */
              color: @color-button-second-text; /* theme */
			}
		}
	}
	&-full {
		width: 100%;
		padding-left: 10px;
		padding-right: 10px;
	}
	&.loading {
		._figure-stack {
			._figure-stack-icon {
				&:before {
					display: inline-block;
					content: '\f021';
					animation: button-spin 1s linear infinite;
				}
			}
		}
	}
	&[disabled] {
		background-color: @color-button-disable-background; /* theme */
		border-color: @color-button-disable-background; /* theme */
		color: @color-button-disable-text; /* theme */
	}
}

/*  REMOVE
-------------------------------------------------------------------------------*/

._remove {
	display: inline-block;
	font-size: 0;
	&:before {
		font-family: 'Glyphicons Halflings';
		font-size: @font-size-xsmall;
		content: '\e014';
	}
}

