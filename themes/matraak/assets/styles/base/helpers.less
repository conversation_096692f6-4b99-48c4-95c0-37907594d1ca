/*=============================================================================*\
    HELPERS
\*=============================================================================*/

.absolute {
    position: absolute !important
}

.block {
    display: block !important
}

.bold {
    font-weight: bold !important
}

.bottom {
    vertical-align: bottom !important
}

.capitalize {
    text-transform: capitalize !important
}

.overflow-y-hidden {
    overflow-y: hidden;
}

.center {
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important
}

.center-block {
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important
}

.clearfix {
    &:after {
        clear: both;
        content: '';
        display: table;
    }
}

.fixed {
    position: fixed !important
}

.hide {
    display: none !important
}

.inline-block {
    display: inline-block !important
}

.invisible {
    visibility: hidden !important
}

.italic {
    font-style: italic !important
}

.left {
    float: left !important
}

.light {
    font-weight: 300 !important
}

.lowercase {
    text-transform: lowercase !important
}

.middle {
    vertical-align: middle !important
}

.normal {
    font-weight: normal !important
}

.nowrap {
    white-space: nowrap !important
}

.relative {
    position: relative !important
}

.right {
    float: right !important
}

.rtl-fixer {
    display: inline-block;
}

.rtl-ltr {
    direction: ltr;
    display: inline-block;
}

.semibold {
    font-weight: 600 !important
}

.static {
    position: static !important
}

.strike {
    text-decoration: line-through !important
}

.text-center {
    text-align: center !important
}

.text-hide {
    background-color: transparent;
    border: 0;
    color: transparent;
    font: ~'0/0' a;
    text-shadow: none;
}

.text-justify {
    text-align: justify !important
}

.text-left {
    text-align: left !important
}

.text-overflow {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.text-right {
    text-align: right !important
}

.top {
    vertical-align: top !important
}

.underline {
    text-decoration: underline !important
}

.uppercase {
    text-transform: uppercase !important
}

.visible {
    visibility: visible !important
}

.pos-top {
    top: 0 !important;
}

.pos-bottom {
    bottom: 0 !important;
}

.pos-left {
    left: 0 !important;
}

.pos-right {
    right: 0 !important;
}