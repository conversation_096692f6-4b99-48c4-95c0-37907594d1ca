/*=============================================================================*\
    RESET
\*=============================================================================*/

/*  MAIN
-------------------------------------------------------------------------------*/

* {
  	-webkit-user-drag: none;
}
a {
	background-color: transparent;
}
a, a:active, a:hover, a:focus {
	color: inherit;
	text-decoration: none;
	outline: none
}
blockquote:before, blockquote:after, q:before, q:after {
	content: ''
}
body {
	line-height: 1;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
fieldset {
	border: none
}
hr {
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	height: 0
}
img {
	-ms-interpolation-mode: bicubic
}
img, object, iframe, embed {
	border: none;
	max-width: 100%;
	vertical-align: top
}
input:-webkit-autofill,
textarea:-webkit-autofill {
	outline: none;
}
input {
  	-webkit-appearance: none;
}
input::-moz-focus-inner, button::-moz-focus-inner {
	border: 0;
	padding: 0;
}
input[type=checkbox],
input[type=radio] {
	padding: 0
}
input[type=button], input[type=submit], input[type=reset], button {
	-webkit-appearance: button;
	border-style: none;
	border-width: none;
	max-width: auto;
	overflow: visible;
	width: auto
}
input[type=search]::-webkit-search-decoration, input[type=search]::-webkit-search-cancel-button {
	-webkit-appearance: none
}
label, input[type-submit], input[type=button], button {
	cursor: pointer
}
pre {
	white-space: pre-wrap;
	word-wrap: break-word
}
th {
	font-weight: normal
}
textarea {
	overflow: auto;
	resize: none;
	vertical-align: top
}

/*  BOOTSTRAP
-------------------------------------------------------------------------------*/

h1, h2, h3, h4, h5, h6, h1 small, h2 small, h3 small, h4 small, h5 small, h6 small, address, abbr[title], abbr[data-original-title], caption, p, blockquote, ul, ol, dl, dt, dd, pre, pre code, code, kbd, pre, samp, legend {
	background-color: transparent;
	border: none;
	border-radius: 0;
	color: inherit;
	font-family: inherit;
	font-style: normal;
	list-style-position: inside;
	margin: 0;
	padding: 0;
	text-decoration: none
}
hr {
	margin: 0;
}
pre {
	overflow: visible;
}
label {
	font-weight: normal;
	margin: 0
}
input, textarea, button {
	padding: 0;
	line-height: normal;
}
input[type=radio], input[type=checkbox] {
	margin: 0;

    &:focus {
        outline: none;
    }
}
.table-responsive {
	border: none;
}
.modal {
	padding-left: 0 !important;
}
