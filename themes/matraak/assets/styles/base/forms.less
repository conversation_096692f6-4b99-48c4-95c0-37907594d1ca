/*=============================================================================*\
    FORMS
\*=============================================================================*/

._form {

	._form-row {
        .flexy-row(16px);
        ._form-col {
          .flexy-col();
          vertical-align: top;
          .help-block-error {
            margin-top: 5px;
          }
        }

        & + ._form-row,
        & + ._form-row-group {
          margin-top: 20px;
        }

		._help-text {
			margin-top: 5px;
		}

		._form-actions {
          .clearfix();
          text-align: right;
          ._button {
            float: right;
            margin-left: 10px;
            &:last-child {
              margin-left: 0;
            }
          }
          &._form-actions-reverse {
            text-align: left;
            ._button {
              float: left;
              margin-left: 0;
              margin-right: 10px;
              &:last-child {
                margin-right: 0;
              }
            }
          }
        }
	}

    ._form-row-secondary {
        .flexy-row(16px);
        margin-bottom: 15px;

        ._form-col-secondary {
            .flexy-col();
            vertical-align: top;

            &._form-col-bottom {
                vertical-align: bottom;
            }

            ._help-text {
                margin-top: 5px;
            }
        }
    }

	._form-row-group {
		& + ._form-row {
			margin-top: 15px;
		}
	}
}

._login,
._register {
    ._form {
        ._form-row {
            display: flex;

            ._form-col {
                display: block;
                margin-bottom: 10px;

                .help-block-error {
                    margin-top: 5px;
                }
            }

            & + ._form-row,
            & + ._form-row-group {
              margin-top: 0;
            }

            ._form-actions {

                ._button {
                   width: 100%;
                    margin-left: 10px;
                    &:last-child {
                        margin-left: 0;
                    }
                }

                &._form-actions-reverse {
                    ._button {
                        margin-left: 0;
                        margin-right: 10px;
                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }
            }
        }
    }
}

._forgotten-password {

	._form {
		margin-bottom: 10px;
	}

	form {
		display: flex;

		._form-row {
			display: block;
			flex: 1 1 auto;
			width: 100%;
			margin-left: -15px;

			& + ._form-row {
				flex: none;
				width: auto;
				margin: 0 -15px;
			}

			._form-col {
				display: block;
				padding: 0 15px;
			}
		}
	}
}

.page-register ._register,
.page-login ._login {
	width: 540px;
	margin: 0 auto;
	align-items: center;

	._form ._form-row ._form-col {
		width: 100%;

		& + ._form-col {
			max-width: 50%;
		}
	}
}

.page-forgotten-password ._forgotten-password {
	width: 540px;
	margin: 0 auto;

	._form ._form-row ._form-col {
		width: 100%;
	}
}

._popup-login ._login,
._popup-register ._register 	{

	._form {

		._form-row {
			.flexy-row(30px);
			display: flex;
			flex-flow: row nowrap;
			align-items: center;
			margin-bottom: 20px;

			._form-col {
				flex: 1 1 auto;
				max-width: 100%;
				padding-left: 15px;
				padding-right: 15px;
				margin-bottom: 0;

				& + ._form-col {
					max-width: 50%;
				}

				.help-block-error {
					margin-top: 5px;
				}
			}
		}
	}
}


._checkout-express ._login,
._checkout-express ._register,
._product-details-parameters {
	._form {

		._form-row {
			flex-flow: column wrap;

			._form-col {
				max-width: 100%;

				& + ._form-col {
					max-width: 100%;
				}
			}
			._form-actions {
				text-align: left;
			}
		}
	}
}

/*  HELP TEXT
-------------------------------------------------------------------------------*/

._help-text {
	display: block;
	color: @color-forms-fields-placeholder; /* theme */
	font-size: @font-size-small;
}

/*  ERRORS
-------------------------------------------------------------------------------*/

.help-block-error {
	display: block;
	margin-top: 5px;
	font-size: @font-size-small;
	color: @color-error-text; /* theme */
}

/*  FIELDS
-------------------------------------------------------------------------------*/

/*  Input */

input,
textarea {
    outline: none;
}

._field {
	font-size: @base-small-font-size;
	display: inline-block;
	background-color: @color-forms-fields-background; /* theme */
	&:-webkit-autofill {
		-webkit-box-shadow: 0 0 0 1000px @color-forms-fields-background inset; /* theme */
	}
	border-width: 1px;
	border-style: solid;
	border-color: @color-forms-fields-border; /* theme */
	border-radius: 20px;
	color: @color-forms-fields-text; /* theme */
	font-weight: @light;
	padding-left: 24px;
	padding-right: 24px;
	height: 40px;
	width: 100%;

	&:-moz-placeholder {
		color: @color-forms-fields-placeholder; /* theme */
	}
	&:-ms-input-placeholder {
		color: @color-forms-fields-placeholder; /* theme */
	}
	&::-webkit-input-placeholder {
		color: @color-forms-fields-placeholder; /* theme */
	}
}

textarea._field {
    height: auto;
    padding: 15px;
}

.bootstrap-touchspin {
  	max-width: 255px;
  	background: @color-forms-fields-background; /* theme */
	border-width: 1px;
	border-style: solid;
	border-color: @color-forms-fields-border; /* theme */
	border-radius: 20px;
	overflow: hidden;

  ._field {
    width: 175px;
    height: 40px;
  	font-size: @base-font-size;
  	line-height: 39px;
	font-weight: @light;
    color: @color-forms-fields-secondary-placeholder; /* theme */
  	background: transparent;
  	border: none;
    padding: 0;
  }
  &.input-group {
    .input-group-btn {
      .btn {
        width: 40px;
        height: 40px;
	  	line-height: 38px;
        background: transparent;
	  	border: none;
        color: @color-forms-fields-secondary-placeholder; /* theme */
        font-weight: @bold;
		outline: none;

        &.bootstrap-touchspin-down {
          margin-right: 5px;
        }

        &:before {
          font-size: @base-font-size;
			font-weight: @normal;
        }
      }
    }
  }
}
/*  Textarea */

._textarea {
	padding: 15px;
	._field();
	height: 170px;
}

/*  Stack - Field + Button */

._field-stack {
	display: inline-table;

	._field {
		border-radius: 20px;
	}

	& ._field-stack-addon {
		display: table-cell;
		vertical-align: top;
		width: 1%;
		white-space: nowrap;
		padding-left: 15px;

        &:first-child {
            padding-left: 0;
        }
	}

	._button {
		border-radius: 20px;
	}
}

/*  Stack - Field + Icon */

._field-icon {
	.field-icon(38px, @color-button-background, right);

	& ._button {
		color: @color-button-background; /* theme */
	}
}

/*  SELECT
-------------------------------------------------------------------------------*/

._select {
	width: 100%;
	&.select2-dropdown-open {
		.select2-choice {
			border-color: @color-forms-fields-border; /* theme */
			background-image: none;
		}
	}
	.select2-choice {
        background-color: @color-forms-fields-background; /* theme */
        background-image: none;
        border-width: 1px;
        border-style: solid;
        border-color: @color-forms-fields-border; /* theme */
		color: @color-forms-fields-text; /* theme */
		border-radius: 20px;
		font-weight: @light;
		padding: 0;
		height: 40px;
		line-height: 40px;
		box-shadow: none;
		.select2-chosen {
			margin: 0;
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			padding-left: 24px;
			padding-right: 62px;
		}
		.select2-arrow {
			background-image: none;
			border: none;
			width: 38px;
			background-color: transparent;
			b {
				background-image: none;
				color: @color-breadcrumb-text; /* theme */
				&:before {
					font-family: FontAwesome;
					font-weight: @normal;
					//content: '\f078';
					content: '\f107';
					.centerer(true, true);
				}
			}
		}
	}
	&.select2-allowclear {
		.select2-choice {
			abbr {
				._remove();
				background-image: none;
				color: @color-breadcrumb-text; /* theme */
				height: auto;
				width: auto;
				line-height: 1;
				right: 38px;
				.centerer(false, true);
			}
		}
	}
}
.select2-drop {
	border-width: 1px;
	border-style: solid;
	border-color: @color-forms-fields-border; /* theme */
	border-radius: 20px;
	box-shadow: none;
	margin-top: -1px;
	overflow: hidden;

	&.select2-drop-above {
		box-shadow: none;
		margin: 0;
		&.select2-drop-active {
			border-top-color: @color-forms-fields-border; /* theme */
		}
	}
	.select2-search {
		padding: 15px 24px;
	}
	.select2-results {
		padding: 0;
		margin: 0;
		max-height: 204px;
		.select2-result {
			color: @color-forms-fields-text; /* theme */

			.select2-result-label {
				padding: 5px 24px;
                color: @color-main-text; /* theme */
			}

			&.select2-highlighted {
				background-color: @color-main-accent; /* theme */
				color: @color-button-text; /* theme */
				.select2-result-label {
					color: @color-main-accent-text; /* theme */
				}
				@media (min-width: @screen-desktop) {
					&:hover,
					&:focus {
						.select2-result-label {
							color: @color-main-accent-text; /* theme */
						}
					}
				}
			}
		}
		.select2-no-results,
		.select2-searching {
			font-size: @font-size-small;
			color: @color-main-text; /* theme */
			padding: 5px 15px;
			background-color: transparent;
		}
	}
}

/*  INPUT GROUP - BOOTSTRAP
-------------------------------------------------------------------------------*/

.input-group {
	display: inline-table;
	.input-group-btn {
		.btn {
			border-radius: 0;
			border: none;
			margin: 0;
			width: 38px;
			height: 38px;
			padding: 0;
			background-color: @color-forms-fields-border; /* theme */
			font-size: 0;
			position: relative;
			&:before {
				font-size: @font-size-small;
				font-family: 'Glyphicons Halflings';
				.centerer(true, true);
			}
			&.bootstrap-touchspin-down {
				&:before {
					content: '\2212';
				}
			}
			&.bootstrap-touchspin-up {
				&:before {
					content: '\2b';
				}
			}
		}
	}
	._field {
		box-shadow: none;
		text-align: center;
	}
}

/*  CHECKBOX
-------------------------------------------------------------------------------*/

._checkbox {
	position: relative;
	padding-left: 20px;
    display: flex;
    align-items: center;
	font-size: @base-xsmall-font-size;
    line-height: 15px;
	font-weight: @light;

	.checker, .checker > span, .checker > span > input {
		width: 14px;
		height: 14px;
	}

	.checker {
		position: absolute;
		top: 0;
		left: 0;

		> span {
            display: block;
			border-radius: 0;
			background-color: @color-forms-checkbox-check-background; /* theme */
			border-width: 2px;
			border-style: solid;
			border-color: @color-forms-checkbox-check; /* theme */
			position: relative;

			&.checked {
				&:before {
					font-family: FontAwesome;
					font-size: @font-size9;
					line-height: 1;
					height: 10px;
					width: 8px;
					content: '\f00c';
					.centerer(true, true);
                    color: @color-forms-checkbox-check; /* theme */
				}
			}
		}
	}

	div.checker.hover > span,
	div.checker.focus > span {
		border-color: @color-forms-checkbox-check; /* theme */
	}

	a {
		margin-left: 5px;
		color: @color-forms-fields-placeholder; /* theme */
		text-decoration: underline;
	}
}

/*  RADIO
-------------------------------------------------------------------------------*/

._radio {
    font-size: @button-size;
	font-weight: @bold;
	position: relative;
    line-height: 15px;
	outline: none;
	-ltr-text-transform: uppercase;
	padding-left: 26px;

	.radio, .radio > span, .radio > span > input {
        width: 14px;
        height: 14px;
        background-position: 0 0;
	}
	.radio {
		position: absolute;
		left: 0;
		top: 0;

		> span {
			position: relative;
            border-width: 2px;
            border-style: solid;
            border-color: @color-forms-radio-check-border; /* theme */
            border-radius: 50%;
			background: @color-forms-radio-check-background; /* theme */
			&.checked {
                border-color: @color-checkout-radio-active; /* theme */

				&:before {
                    content: '';
                    display: block;
                    width: 6px;
                    height: 6px;
					.centerer(true, true);
                    border-radius: 50%;
                    background: @color-checkout-radio-active; /* theme */
				}
			}
		}
	}
	a {
		margin-left: 5px;
		color: @color-main-accent; /* theme */
	}
}

/*  RANGE SLIDER
-------------------------------------------------------------------------------*/

#apply_price_filter {
  margin: 0 10px 10px 0;
}

._range-slider {
	background-image: none;
	border-radius: 0;
	height: 6px;
	background-color: @color-main-range-slider; /* theme */
	border: none;
    margin-bottom: 10px;
	margin-right: 14px;

	&.ui-widget-content {
		border: 0;
	}

	[class*='ui-corner-'] {
		border-radius: 0
	}

	.ui-slider-range {
		background-color: @color-main-range-slider-range; /* theme */
	}

	.ui-slider-handle {
		background-color: @color-main-range-slider-handle; /* theme */
		cursor: pointer;
		border: none;
		width: 14px;
		height: 14px;
		top: -4px;
		margin-left: 0;

        &:last-of-type {
            z-index: 2;
        }
	}
}