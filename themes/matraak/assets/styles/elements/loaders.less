/* Loaders
------------------------------------------------------ */

.loader-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;

  ~ .product-list,
  ~ .alert {
    opacity: .2;
  }

  &.hide {
    ~ .product-list,
    ~ .alert {
      opacity: 1;
    }
  }
}

.loader {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);

  span {
    border-radius: 100%;
    width: 15px;
    height: 15px;
    margin: 2px;
    position: absolute;
    -webkit-animation: dot-spin-fade 1s infinite linear;
    animation: dot-spin-fade 1s infinite linear;

    &:nth-child(1) {
      top: 25px;
      left: 0;
      -webkit-animation-delay: -0.96s;
      animation-delay: -0.96s;
    }
    &:nth-child(2) {
      top: 17.04545px;
      left: 17.04545px;
      -webkit-animation-delay: -0.84s;
      animation-delay: -0.84s;
    }
    &:nth-child(3) {
      top: 0;
      left: 25px;
      -webkit-animation-delay: -0.72s;
      animation-delay: -0.72s;
    }
    &:nth-child(4) {
      top: -17.04545px;
      left: 17.04545px;
      -webkit-animation-delay: -0.6s;
      animation-delay: -0.6s;
    }
    &:nth-child(5) {
      top: -25px;
      left: 0;
      -webkit-animation-delay: -0.48s;
      animation-delay: -0.48s;
    }
    &:nth-child(6) {
      top: -17.04545px;
      left: -17.04545px;
      -webkit-animation-delay: -0.36s;
      animation-delay: -0.36s;
    }
    &:nth-child(7) {
      top: 0;
      left: -25px;
      -webkit-animation-delay: -0.24s;
      animation-delay: -0.24s;
    }
    &:nth-child(8) {
      top: 17.04545px;
      left: -17.04545px;
      -webkit-animation-delay: -0.12s;
      animation-delay: -0.12s;
    }
  }
}

@keyframes dot-spin-fade {
  50% {
    opacity: 0.3;
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@-webkit-keyframes dot-spin-fade {
  50% {
    opacity: 0.3;
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
  }

  100% {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

/* #Tripple dot
------------------------------------------------------ */

.loader-dots {
  font-size: 0;
  line-height: 0;
  text-align: center;

  span {
    display: inline-block;
    border-radius: 100%;
    width: 8px;
    height: 8px;
    margin: 2px;
    -webkit-animation: dot-pulse 1s infinite linear;
    animation: dot-pulse 1s infinite linear;

    &:nth-child(1) {
      -webkit-animation-delay: -.5s;
      animation-delay: -.5s;
    }
    &:nth-child(2) {
      -webkit-animation-delay: -.25s;
      animation-delay: -.25s;
    }
    &:nth-child(3) {
      -webkit-animation-delay: 0;
      animation-delay: 0;
    }
  }
}

@keyframes dot-pulse {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@-webkit-keyframes dot-pulse {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}