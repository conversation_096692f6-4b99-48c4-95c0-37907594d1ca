/*=============================================================================*\
    FAQ
\*=============================================================================*/

._faq {
	ul {
		list-style: none;
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: @color-main-border; /* theme */
		li {
			._faq-title {
                border-top-width: 1px ;
                border-top-style: solid;
                border-top-color: @color-main-border; /* theme */
                padding: 16px 45px 14px 0;
                -ltr-text-transform: uppercase;
                position: relative;
                cursor: pointer;
                transition: transform .3s ease;

                & when  (@rtl) {
                  padding: 15px 0 15px 45px;
                }

              &:after {
                  content: '';
                  display: block;
                  .centerer(false, true);
                  right: 20px;
                  .arrow-bottom(4px, @color-main-title);
                  border-top-color: @color-main-title; /* theme */
                }

                h4 {
                  margin-bottom: 0;
                  font-size: @base-font-size;
                  line-height: 1.2;
                  font-weight: @bold;
                }

                &.active {
                  &:after {
                    transform: scale(-1);
                    transform-origin: center;
                  }
                }
			}
			._faq-text {
				._textbox();
                padding-bottom: 20px;
			}
		}
	}
}

._popup {
  ._faq {
    ._js-accordion {
      &:first-of-type {
        ._faq-title {
          border-top: 0;
        }
      }
    }
  }
}