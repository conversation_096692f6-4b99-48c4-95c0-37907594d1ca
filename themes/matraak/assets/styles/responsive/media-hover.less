/*=============================================================================*\
    HOVER EVENTS
\*=============================================================================*/

@media (min-width: @screen-hover) {
    ._header-top-line {
        a {
            &:hover {
                text-decoration: underline;
                &._figure-stack {
                   text-decoration: none;
                    ._figure-stack-label {
                      text-decoration: underline;
                    }
                }
            }
        }
    }

    ._user-menus {
      order: 3;
    }

    ._header-top-links {
        order: 1;
    }

    .continue-shopping {
      margin: 290px auto 0;
    }

  ._search-form {

    ._field-icon {
      width: 242px;

      button {
        &:hover {
          background: transparent;
        }
      }
    }
  }

  ._navigation {

    > ul {

      > li {

        > ._navigation-dropdown {
          display: block;
          .animated;
          animation-duration: .5s;
          .boingOutUp;

          > ul {
            > li {
              &.item-collapse {
                > a[href="javascript:;"] {
                  &, & > span {
                    cursor: default;
                    &:hover {
                      cursor: default;
                    }
                  }
                }
              }
            }
            li {
              &:not(.item-collapse) {
                > a:hover {
                  color: @color-main-accent; /* theme */
                  text-decoration: underline;
                }
              }
            }
          }
        }

        &:hover {
          > a {
            background: @color-header-navigation-link-background; /* theme */
          }
          > ._navigation-dropdown
          {
            visibility: visible;
            display: block;
            .boingInUp;
          }
        }

      }
    }
  }

  ._account-navigation {
    ul {
      list-style: none;
      li {
        a {
          &:hover {
            color: @color-main-accent; /* theme */
          }
        }
      }
    }
  }

  .table-responsive {
    a {
      &:hover {
        color: @color-main-accent; /* theme */
      }
    }
  }

  ._cart-products ._cart-products-holder ._cart-products-list ._cart-product,
  ._cart-summary-products ._cart-summary-products-list > li {
    ._remove {
      &:hover {
        color: @color-main-accent; /* theme */
      }
    }
  }

  ._cart-additional-text {
    a {
      &:hover {
        text-decoration: none;
      }
    }
  }

  ._checkbox {
    a {
      &:hover {
        text-decoration: none;
      }
    }
  }

  ._vendor {
      &:hover {
        ._vendor-info {
          opacity: 1;
        }
      }
  }

  ._footer {

    a:hover {
      &, & span {
        text-decoration: underline;
      }
    }

    ._newsletter {
      ._newsletter-form {
        ._button {
          &:hover {
            background: @color-newsletter-button-background-hover; /* theme */
            border-color: @color-newsletter-button-background-hover; /* theme */
            color: @color-newsletter-button-text-hover; /* theme */
          }
        }
      }
    }

    ._navigation-footer {
      ul {
        li {
          a {
            span {
            }
          }
        }
      }

      > ul {
        > li {
          &.item-collapse {
            > a:hover {
              &, & span {
                text-decoration: none;
              }
            }
          }
        }
      }
    }

    ._footer-contacts {
      ._text-description {
        a {
          text-decoration: none;

          &:hover {
            &, & span {
              text-decoration: underline;
            }
          }
        }
      }
    }

    ._social-icons {
      ul {
        li {
          a:hover {
            color: @color-footer-social-hover; /* theme */
            text-decoration: none;
          }
        }
      }
    }
  }

  .modal-dialog {

    .modal-content {

      .modal-body {

        .close {
          &:hover {
            &:before {
              color: @color-button-background; /* theme */
            }
          }
        }
      }
    }
  }

  ._filter-categories-list {
    ul {
      li {
        &:hover {
          > a {
            text-decoration: underline;
          }
        }
      }
    }
  }
  ._filter-vendors {
    ._filter-vendors-list {
      li {
        &:hover {
          > a {
            &:not(._remove) {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }

  ._navigation-footer {
    ul {
      li {

        ul {
          li {
            a {
              &:hover {
                color: @color-footer-navigation-link-active; /* theme */
              }
            }
          }
        }
      }
    }
  }
  ._footer-contacts {
    a {
      &:hover {
        color: @color-footer-navigation-link-active; /* theme */
      }
    }
  }
}