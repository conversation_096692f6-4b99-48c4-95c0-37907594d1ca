/*=============================================================================*\
    SMALL DEVICES / TABLETS
\*=============================================================================*/

@media (max-width: @screen-sm-max) {
    html {
      font-size: 14px;
    }

    .noScroll {
      overflow: hidden;
    }

    ._products-list {
		._product {
			width: calc(33.33% ~'-' 30px);
		
			&:nth-child(4n+1) {
				clear: none;
			}
		
			&:nth-child(3n+1) {
				clear: both;
			}
		}
	}

	._filters-row {
		._filter-button {
			margin: 0 auto;
		}
		._products-list-options {
			display: none;
		}
	}

	._blog-content-wrapper ._filter-sidebar,
	._products-list-wrapper ._filter-sidebar {
		display: block;
		width: 300px;
		height: 100%;
		padding: 0;
		position: fixed;
		top: 0;
		left: 0;
		overflow: auto;
		z-index: 100;
		padding: 20px;

		._order-sort-wrapper {
			display: block;
		}
	}

	.js-sidebar-open {
		._filter-sidebar {
			display: none;
		}
	}

	._filter-button {
		p {
			&:before {
				opacity: 1;

				& when (@rtl) {
					opacity: 0;
				}
			}

			&:after {
				opacity: 0;

				& when (@rtl) {
					opacity: 1;
				}
			}
		}

		&.js-active {
			p {
				&:before {
					opacity: 0;

					& when (@rtl) {
						opacity: 1;
					}
				}

				&:after {
					opacity: 1;

					& when (@rtl) {
						opacity: 0;

					}
				}
			}
		}
	}

	._filter-sidebar {
		._filter-button {
			display: block;
		}
	}

	._blog-content-wrapper .col-md-10 {
		padding: 0;
	}

	._home-carousel {
		._slider {
			.slides {
				.slide {
					.slide-text {
						.slide-html {
							display: none;
						}
					}
				}
			}
		}
	}

    .contact-page {
      ._google-map {
        height: 300px !important;
      }
    }

    ._contact-form {
      ._form-row {
        ._form-col {
          & + ._form-col {
            margin-left: 0;
          }
        }
      }
    }

    ._blog-article {
      ._blog-article-comments {
        ._blog-article-comment {
          flex-flow: column;
          ._blog-article-comment-image {
            margin-bottom: 10px;
          }
        }
      }
    }

    ._blog-article-comments-form {
      ._form-row {
        ._form-col {
          & + ._form-col {
            margin-left: 0;
          }
        }
      }
    }

	._checkout-main {
		._address-book {
			._address-book-tabs {
				._address-book-list {
					ul {
						li {
							._address-book-list-item {
								width: calc(~'100% - 2px');
							}
						}
					}
				}
			}
		}
	}
  /*  DETAILS
  -------------------------------------------------------------------------------*/

  ._product-details {
      margin-bottom: 30px;
  }

  ._product-details-image {
	  ._product-details-ribbon-banner {
		  max-width: 25%;

		  img {
			  max-width: 100%;
			  max-height: 100px;
		  }
	  }
  }

  ._has-helper {
		._helper-logo {
			display: none;
		}
	}

	._textbox {
		img {
			float: none !important;
			margin: 0 !important;
			height: auto !important;
			width: 100% !important;
			height: auto !important;
		}
	}

	._product-sidebar {
		margin-top: 26px;
	}

	._product-information {
		padding: 0 0 17px;
	}

	._product-details-parameters {
		._form ._form-row ._form-col-bottom {
			margin-top: 0;
		}
	}
	._section-separator {
		margin-top: 30px;
	}

	._logo {
		> a {
			._logo-image {
				max-width: 200px;
			}
		}
	}
	._pagination {
		.pagination {
			li {
				&.page {
					display: none;
				}
			}
		}
	}

	._header {
		._logo {
			text-align: center;
			max-width: 100%;
			
			> a {
				._logo-text {
					font-size: @font-size-heading-1 * @golden-ratio-ngt;
				}
			}
		}
		._header-bar {
			display: block;
			._header-bar-left,
			._header-bar-right {
				display: block;
			}
			._header-bar-left {
				._search-form {
					text-align: center;
				}
				& + ._header-bar-right {
					margin-top: 30px;
				}
			}
			._header-bar-right {
				text-align: center;
				._cart-compact {
					> ._figure-stack {
						._figure-stack-icon {
							border-width: 1px;
							border-style: solid;
							border-color: @color-main-border; /* theme */
							width: 40px;
							height: 40px;
						}
						._figure-stack-label {
							display: none;
						}
					}
					._cart-compact-products {
						display: none;
					}
				}
              ._top-line {
                ._user-controls {
                  ul {
                    li {
                      & + li {
                        margin-left: 15px;
                      }
                    }
                  }
                  ._figure-stack {
                    &-icon {
                      display: none;
                    }
                  }
                }
              }
				._user-controls {
					ul {
						li {
							._figure-stack {
								._figure-stack-icon {
									border-width: 1px;
									border-style: solid;
									border-color: @color-main-border; /* theme */
									width: 40px;
									height: 40px;
								}
								._figure-stack-label {
									display: none;
								}
							}
						}
					}
				}

			}
		}
	}
	._home-carousel {
		._slider {
			.slides {
				.slide {
					.slide-text {
						.slide-html {
							//display: none;
						}
					}
				}
				.owl-controls {
					.owl-nav {
						font-size: @font-size-slider-nav * @golden-ratio-ngt;
						color: @color-button-text; /* theme */
						.owl-prev,
						.owl-next {
							width: 82px * @golden-ratio-ngt;
						}
					}
					.owl-dots {
						display: none !important;
					}
				}
			}
		}
	}

	._product {
		._product-image {
			._product-image-thumb {
				img {
					opacity: 1 !important;
				}
			}
			._product-quick-view {
				//display: block !important;
			}
		}
	}
	.page-products,
	.page-category,
	.page-vendor,
	.page-tags {
		._col-reverse {
			&[class*='-3'] {
				float: none;
			}
			&[class*='-9'] {
				float: none;
				._section-separator();
			}
		}
	}
	._products-list-head {
		.flexy-row-mobile(15px);
		._products-list-head-item {
			.flexy-col-mobile();
			text-align: center;
			._products-list-head-item-image {
				float: none;
				display: inline-block;
				margin-right: 0;
				._products-list-head-item-image-thumb {
					height: 130px;
					width: 130px;
					position: relative;
					margin: 10px;
					img {
						bottom: 0;
						height: auto;
						left: 0;
						margin: auto;
						max-height: 100%;
						max-width: 100%;
						position: absolute;
						right: 0;
						top: 0;
						width: auto;
					}
				}
				& + ._products-list-head-item-description {
					margin-top: 15px;
				}
			}
			& + ._products-list-head-item {
				margin-top: 30px;
			}
		}
		& + ._section-title {
			._section-separator();
		}
	}

	._products-list-sidebar {
		> ._section-title {
			._section-separator();
			&:first-child {
				margin-bottom: 20px;
			}
		}
	}

	._product-sidebar {
		> ._section-title {
			._section-separator();
			&:first-child {
				margin-bottom: 20px;
			}
		}
	}
	.modal-dialog.product-details {
		width: auto;
		margin: 15px;
	}
	._blog-article {
		._section-title {
			._section-separator();
		}
	}
	._blog-sidebar {
		padding-top: 0;
		> ._section-title {
			._section-separator();
			&:first-child {
				margin-bottom: 20px;
			}
		}
	}

	._vendors-list {
		._vendor {
			width: 100%;
			&:nth-child(-n+2) {
				margin-top: 30px;
			}
			&:nth-child(2n+3) {
				clear: none;
			}
			&:nth-child(-n+1) {
				margin-top: 0;
			}
		}
	}
	.page-account {
		._col-reverse {
			&[class*='-4'] {
				float: none;
			}
			&[class*='-8'] {
				float: none;
				._section-separator();
			}
		}
	}
	._account-main {
		._section-title {
			._section-separator();
		}
	}
	._account-sidebar {
		padding-top: 0;
		> ._section-title {
			._section-separator();
			&:first-child {
				margin-bottom: 20px;
			}
		}
	}
	._checkout-steps {
		ul {
			li {
				._checkout-step-name {
					padding: 42px 0 0;
					font-size: 0;
				}
			}
		}
	}

	._vendors-list {
		._vendor {
			width: 47.828%;

			&:nth-child(-n+3) {
				margin-top: 30px;
			}

			&:nth-child(-n+2) {
				margin-top: 0;
			}

			&:nth-child(3n+4) {
				clear: none;
				margin-left: 30px;
			}

			&:nth-child(2n+3) {
				clear: left;
				margin-left: 0;
			}
		}
	}

	._leasing-creditors {
		._leasing-creditor {
			._leasing-creditor-details {
				._leasing-creditor-details-name {
					._leasing-creditor-details-image {
						display: none;
					}
				}
			}
			._leasing-creditor-actions {
				._button {
					width: 100%;
				}
			}
		}
	}
	._leasing-form {
		._button {
			width: 100% !important;
		}
	}

	._horizontal-banners {
		&._multiple-banners {
			._banners {
				._banners-list {
					flex-flow: row nowrap;

					._banner {
						//margin-bottom: 0;

						._banner-caption {
							font-size: @banner-title * @golden-ratio-ngt;
						}
					}
				}
			}
		}
	}

	._blog-list-articles {

		._blog-list-article {
			flex: 1 1 50%;
			width: 50%;
			max-width: 50%;
		}
	}

	.page-forgotten-password ._forgotten-password,
	.page-register ._register,
	.page-login ._login {
		width: 100%;
	}

	.page-register ._register,
	.page-login ._login {
		._form ._form-row {
			flex-flow: column wrap;

			._form-col {
				width: 100%;

				& + ._form-col {
					max-width: 100%;
					margin-top: 0;
				}
			}
		}
	}
	._popup {
		._popup-title {

			h4 {
				._h4;
			}
		}
		._meta-links {
			flex-flow: column wrap;
			align-items: center;
			a {
				& + a {
					margin-left: 0;
					margin-top: 15px;
				}
			}
		}
	}

	._popup-forgotten-password ._forgotten-password,
	._popup-login ._login,
	._popup-register ._register,
	._register,
	._forgotten-password,
	._login {
		._form {
			._form-row {
				align-items: flex-start;
				flex-flow: column wrap;

				._form-col {
					width: 100%;

					& + ._form-col {
						max-width: 100%;
					}
				}
			}
		}
	}

    ._checkout-main {
        ._checkout-shipping-providers-controls {
            &:after {
                display: none;
            }
            ._radio {
                display: block;
                & + ._radio {
                    margin-left: 0;
                    margin-top: 5px;
                }
            }
        }
    }

    ._address-book {
        ._address-book-list {
            ul {
                margin: 0;
                li {
                    width: 100%;
                    padding: 0;
                    & + li {
                        margin-top: 15px;
                    }
                }
            }
        }
    }
}