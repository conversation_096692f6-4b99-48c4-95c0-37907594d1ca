/*=============================================================================*\
    EXTRA SMALL DEVICES / PHONES
\*=============================================================================*/

@media (max-width: @screen-ls-max) {
  ._user-menus {
    ._user-controls {
      ul {
        margin-right: 3px;

        li {
          margin-left: 15px;
          &:first-of-type {
            margin-left: 0;
          }
        }
      }
    }
  }

  ._products-list {
    ._product {
      width: 100%;
      &:before {
        display: none;
      }
      ._product-image {
        position: relative;
        ._product-image-thumb-holder {
          text-align: center;

          img {
            display: inline-block;
            position: static;
            transform: none;
            left: 0;
            top: 0;
          }
        }
      }
    }
  }

  ._product {
    ._product-image {
      position: relative;

      &:before {
        display: none;
      }

      ._product-image-thumb {
          position: static;
          transform: none;
          left: 0;
          top: 0;

        ._product-image-thumb-holder {
          padding-bottom: 0;
          height: auto;
          img {
            position: relative;
          }
        }
      }
    }

    ._product-info {
      position: static;
      text-align: center;
      opacity: 1;
      padding-top: 16px;

      &:before,
      &:after {
        display: none;
      }

      ._product-name {
          padding-bottom: 9px;
          flex: 0 1 auto;

          h3 {
            color: @color-main-text-bolder; /* theme */
          }
      }

      ._product-options {
          padding-top: 0;
          flex: 0 1 auto;

          ._product-price,
          ._product-price ._product-price-compare {
              color: @color-main-text-bolder; /* theme */
          }
          ._product-price ._product-price-old {
            color: @color-main-secondary-text; /* theme */
          }
      }
    }
  }

  ._showcase {
    ._showcase-list {
      ._showcase-item {
        width: 100%;
        display: inline-block;
        a {
          &:before {
            display: none;
          }
        }

        ._showcase-item-image {
          display: block;
          position: static;

          ._showcase-item-image-thumb {
            height: auto;

            img {
              position: relative;
              display: block;
            }
          }
        }
      }
    }
  }

  ._vendors-list {
    ._vendor {
      max-width: 100%;
      width: 100%;
    }
  }

  ._latest-blog {
    margin-top: 60px;

    ._section-separator {
      padding: 70px 0;
      margin-top: 0;
    }

    ._latest-blog-title-wrap {
      margin-bottom: 30px;
    }

    ._blog-list-articles {

      ._blog-list-article {
        width: 100%;
        ._blog-list-article-image {
          padding-bottom: 0;
          display: block;
          .blog-list-article-image-thumb {
            position: static;
            img {
              position: static;
              transform: none;
            }
          }
        }
      }
    }
  }

  ._banners {
    ._banners-list {
      display: block;
      width: 100%;
      margin: 0;

      ._banner {
        display: block;
        padding: 0;

        & + ._banner {
          //margin-top: 30px;
        }
      }
    }
  }

  ._footer__banners {
    padding-bottom: 30px;
  }

  ._cart-products {
    ._cart-products-holder {
      ._cart-products-list {
        ._cart-product {
          position: relative;
          margin-bottom: 15px;
          padding: 0 0 5px 0;
          text-align: center;

          & when (@rtl) {
            padding: 15px;
          }

          > div {
            &._cart-product-details {
              ._cart-product-info {
                min-height: inherit;

                ._cart-product-image {
                  position: static;
                  left: auto;
                  display: block;
                  ._cart-product-image-thumb {
                    height: auto;
                    width: auto;
                    margin-bottom: 20px
                  }
                }
                ._cart-product-parameters {
                  margin: 0 auto;
                  text-align: left;
                }

                ._cart-product-box {
                  ._cart-product-single-price {
                    text-align: center !important;
                  }
                }
              }
            }
            &._cart-product-quantity {
              position: static;
              bottom: auto;
              left: auto;
              margin-top: 10px;
            }
            &._cart-product-total-price {
              position: static;
              right: auto;
              bottom: auto;
              margin-top: 10px;
            }
          }

          ._remove {
            position: static;
            margin-top: 15px;
          }
        }
      }
    }
  }

  ._cart-summary {
    ._section-separator();
    ._cart-discount-code {
      margin-top: 20px;
    }
    ._cart-summary-actions {
      margin-top: 20px;
    }
  }

  ._checkout-account-tabs {
    padding-bottom: 0;
  }

  ._checkout-account {

    &-tabs {

      ._form-row {
        display: flex;
        flex-flow: column;
      }
      ._form-col {
        &:not(:last-of-type) {
          border-bottom-width: 1px;
          border-bottom-style: solid;
          border-bottom-color: @color-main-border; /* theme */
        }
      }
    }
  }

  ._checkout-main {
    ._section-title {
      ._section-separator();
    }
  }
  ._checkout-verify-addresses {
    .flexy-row-mobile(15px);
    ._checkout-verify-address-row {
      .flexy-col-mobile();
    }
  }
  ._checkout-return {
    ._checkout-return-icon {
      font-size: @font-size-thanks-icon * @golden-ratio-ngt;
      & + ._textbox {
        ._section-separator();
      }
    }
  }

  ._checkout-main--verify {
    ._cart-products {
      ._cart-products-holder {
        ._cart-products-list {
          ._cart-product {
            padding: 15px;
            & when (@rtl) {
              padding: 15px;
            }
          }
        }
      }
    }
  }

  ._checkout-verify-addresses {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    ._checkout-verify-address {

      &-row {
        flex: 1 1 auto;
        min-width: inherit;
        max-width: none;
        padding: 0;

        &:first-of-type {
          padding-right: 0;
        }

        &:last-of-type {
          padding-left: 0;
        }
      }
    }
  }

  ._footer {
    ._newsletter {
      ._newsletter-form {
        ._form {
          ._field-stack {
            display: block;

            ._field-stack-addon{
              padding: 20px 0 0;
            }

            ._button {
              display: block;
              width: 100%;
            }
          }
        }
      }
    }
  }


  ._blog-list-articles {

    ._blog-list-article {
      flex: 1 1 100%;
      width: 100%;
      max-width: 100%;
    }
  }

  ._home-welcome__holder ._text-description {
      
      table, td, tr {
        display: block;
        width: 100% !important;
        height: auto !important;
        text-align: center;
        padding: 5px 0 !important;
      }
  }


  ._forgotten-password {

    form {
      flex-flow: column wrap;

      ._form-row {
        margin: 0;

        & + ._form-row {
          margin: 20px 0 0;
        }

        ._form-col {
          padding: 0;
        }
      }
    }
  }
}