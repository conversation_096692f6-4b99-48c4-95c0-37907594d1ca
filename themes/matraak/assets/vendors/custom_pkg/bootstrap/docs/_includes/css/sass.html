<div class="bs-docs-section">
  <h1 id="sass" class="page-header">Using Sass</h1>
  <p class="lead">While Bootstrap is built on Less, it also has an <a href="{{ site.sass_repo }}">official Sass port</a>. We maintain it in a separate GitHub repository and handle updates with a conversion script.</p>

  <h2 id="sass-contents">What's included</h2>
  <p>Since the Sass port has a separate repo and serves a slightly different audience, the contents of the project differ greatly from the main Bootstrap project. This ensures the Sass port is as compatible with as many Sass-based systems as possible.</p>

  <div class="table-responsive">
    <table class="table table-bordered table-striped">
      <thead>
        <tr>
          <th>Path</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <th scope="row"><code>lib/</code></th>
          <td>Ruby gem code (Sass configuration, Rails and Compass integrations)</td>
        </tr>
        <tr>
          <th scope="row"><code>tasks/</code></th>
          <td>Converter scripts (turning upstream Less to Sass)</td>
        </tr>
        <tr>
          <th scope="row"><code>test/</code></th>
          <td>Compilation tests</td>
        </tr>
        <tr>
          <th scope="row"><code>templates/</code></th>
          <td>Compass package manifest</td>
        </tr>
        <tr>
          <th scope="row"><code>vendor/assets/</code></th>
          <td>Sass, JavaScript, and font files</td>
        </tr>
        <tr>
          <th scope="row"><code>Rakefile</code></th>
          <td>Internal tasks, such as rake and convert</td>
        </tr>
      </tbody>
    </table>
  </div>
  <p>Visit the <a href="{{ site.sass_repo }}">Sass port's GitHub repository</a> to see these files in action.</p>


  <h2 id="sass-installation">Installation</h2>
  <p>For information on how to install and use Bootstrap for Sass, consult the <a href="{{ site.sass_repo }}">GitHub repository readme</a>. It's the most up to date source and includes information for use with Rails, Compass, and standard Sass projects.</p>
  <p>
    <a class="btn btn-lg btn-outline" href="{{ site.sass_repo }}">Bootstrap for Sass</a>
  </p>
</div>
