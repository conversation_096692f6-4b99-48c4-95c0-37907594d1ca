<li>
  <a href="#js-overview">Overview</a>
  <ul class="nav">
    <li><a href="#js-individual-compiled">Individual or compiled</a></li>
    <li><a href="#js-data-attrs">Data attributes</a></li>
    <li><a href="#js-programmatic-api">Programmatic API</a></li>
    <li><a href="#js-noconflict">No conflict</a></li>
    <li><a href="#js-events">Events</a></li>
    <li><a href="#js-version-nums">Version numbers</a></li>
    <li><a href="#js-disabled">When JavaScript is disabled</a></li>
    <li><a href="#callout-third-party-libs">Third-party libraries</a></li>
  </ul>
</li>
<li><a href="#transitions">Transitions</a></li>
<li>
  <a href="#modals">Modal</a>
  <ul class="nav">
    <li><a href="#modals-examples">Examples</a></li>
    <li><a href="#modals-sizes">Sizes</a></li>
    <li><a href="#modals-remove-animation">Remove animation</a></li>
    <li><a href="#modals-related-target">Varying content based on trigger button</a></li>
    <li><a href="#modals-usage">Usage</a></li>
    <li><a href="#modals-options">Options</a></li>
    <li><a href="#modals-methods">Methods</a></li>
    <li><a href="#modals-events">Events</a></li>
  </ul>
</li>
<li>
  <a href="#dropdowns">Dropdown</a>
  <ul class="nav">
    <li><a href="#dropdowns-examples">Examples</a></li>
    <li><a href="#dropdowns-usage">Usage</a></li>
    <li><a href="#dropdowns-methods">Methods</a></li>
    <li><a href="#dropdowns-events">Events</a></li>
  </ul>
</li>
<li>
  <a href="#scrollspy">Scrollspy</a>
  <ul class="nav">
    <li><a href="#scrollspy-examples">Examples</a></li>
    <li><a href="#scrollspy-usage">Usage</a></li>
    <li><a href="#scrollspy-methods">Methods</a></li>
    <li><a href="#scrollspy-options">Options</a></li>
    <li><a href="#scrollspy-events">Events</a></li>
  </ul>
</li>
<li>
  <a href="#tabs">Tab</a>
  <ul class="nav">
    <li><a href="#tabs-examples">Examples</a></li>
    <li><a href="#tabs-usage">Usage</a></li>
    <li><a href="#tabs-methods">Methods</a></li>
    <li><a href="#tabs-events">Events</a></li>
  </ul>
</li>
<li>
  <a href="#tooltips">Tooltip</a>
  <ul class="nav">
    <li><a href="#tooltips-examples">Examples</a></li>
    <li><a href="#tooltips-usage">Usage</a></li>
    <li><a href="#tooltips-options">Options</a></li>
    <li><a href="#tooltips-methods">Methods</a></li>
    <li><a href="#tooltips-events">Events</a></li>
  </ul>
</li>
<li>
  <a href="#popovers">Popover</a>
  <ul class="nav">
    <li><a href="#popovers-examples">Examples</a></li>
    <li><a href="#popovers-usage">Usage</a></li>
    <li><a href="#popovers-options">Options</a></li>
    <li><a href="#popovers-methods">Methods</a></li>
    <li><a href="#popovers-events">Events</a></li>
  </ul>
</li>
<li>
  <a href="#alerts">Alert</a>
  <ul class="nav">
    <li><a href="#alerts-examples">Examples</a></li>
    <li><a href="#alerts-usage">Usage</a></li>
    <li><a href="#alerts-methods">Methods</a></li>
    <li><a href="#alerts-events">Events</a></li>
  </ul>
</li>
<li>
  <a href="#buttons">Button</a>
  <ul class="nav">
    <li><a href="#buttons-stateful">Stateful</a></li>
    <li><a href="#buttons-single-toggle">Single toggle</a></li>
    <li><a href="#buttons-checkbox-radio">Checkbox / Radio</a></li>
    <li><a href="#buttons-methods">Methods</a></li>
  </ul>
</li>
<li>
  <a href="#collapse">Collapse</a>
  <ul class="nav">
    <li><a href="#collapse-example">Example</a></li>
    <li><a href="#collapse-example-accordion">Accordion example</a></li>
    <li><a href="#collapse-usage">Usage</a></li>
    <li><a href="#collapse-options">Options</a></li>
    <li><a href="#collapse-methods">Methods</a></li>
    <li><a href="#collapse-events">Events</a></li>
  </ul>
</li>
<li>
  <a href="#carousel">Carousel</a>
  <ul class="nav">
    <li><a href="#carousel-examples">Examples</a></li>
    <li><a href="#carousel-usage">Usage</a></li>
    <li><a href="#carousel-options">Options</a></li>
    <li><a href="#carousel-methods">Methods</a></li>
    <li><a href="#carousel-events">Events</a></li>
  </ul>
</li>
<li>
  <a href="#affix">Affix</a>
  <ul class="nav">
    <li><a href="#affix-examples">Examples</a></li>
    <li><a href="#affix-usage">Usage</a></li>
    <li><a href="#affix-options">Options</a></li>
    <li><a href="#affix-methods">Methods</a></li>
    <li><a href="#affix-events">Events</a></li>
  </ul>
</li>
