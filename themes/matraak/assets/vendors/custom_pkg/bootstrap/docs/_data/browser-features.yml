-
  browser: >
    Microsoft Edge
  summary: >
    Implement the [`:dir()` pseudo-class](https://developer.mozilla.org/en-US/docs/Web/CSS/:dir) from Selectors Level 4
  upstream_bug: >
    UserVoice#12299532
  origin: >
    Bootstrap#19984

-
  browser: >
    Microsoft Edge
  summary: >
    Implement [sticky positioning](http://html5please.com/#position:sticky) from CSS Positioned Layout Level 3
  upstream_bug: >
    UserVoice#6263621
  origin: >
    Bootstrap#17021

-
  browser: >
    Firefox
  summary: >
    Fire a [`transitioncancel` event](https://developer.mozilla.org/en-US/docs/Web/Events/transitioncancel) when a CSS transition is canceled
  upstream_bug: >
    Mozilla#1264125
  origin: >
    Mozilla#1182856

-
  browser: >
    Firefox
  summary: >
    Implement the [`of <selector-list>` clause](http://caniuse.com/#feat=css-nth-child-of) of the `:nth-child()` pseudo-class
  upstream_bug: >
    Mozilla#854148
  origin: >
    Bootstrap#20143

-
  browser: >
    Firefox
  summary: >
    Implement the HTML5 [`<dialog>` element](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/dialog)
  upstream_bug: >
    Mozilla#840640
  origin: >
    Bootstrap#20175

-
  browser: >
    Chrome
  summary: >
    Implement the [`of <selector-list>` clause](http://caniuse.com/#feat=css-nth-child-of) of the `:nth-child()` pseudo-class
  upstream_bug: >
    Chromium#304163
  origin: >
    Bootstrap#20143

-
  browser: >
    Chrome
  summary: >
    Implement the [`:dir()` pseudo-class](https://developer.mozilla.org/en-US/docs/Web/CSS/:dir) from Selectors Level 4
  upstream_bug: >
    Chromium#576815
  origin: >
    Bootstrap#19984

-
  browser: >
    Chrome
  summary: >
    Implement [sticky positioning](http://html5please.com/#position:sticky) from CSS Positioned Layout Level 3
  upstream_bug: >
    Chromium#231752
  origin: >
    Bootstrap#17021

-
  browser: >
    Safari
  summary: >
    Implement the [`:dir()` pseudo-class](https://developer.mozilla.org/en-US/docs/Web/CSS/:dir) from Selectors Level 4
  upstream_bug: >
    WebKit#64861
  origin: >
    Bootstrap#19984

-
  browser: >
    Safari
  summary: >
    Implement the HTML5 [`<dialog>` element](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/dialog)
  upstream_bug: >
    WebKit#84635
  origin: >
    Bootstrap#20175
