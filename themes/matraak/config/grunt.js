module.exports = {

    theme: {
        name: 'matraak', // This name is used as a public directory name for assets. Use lowercase letters.
        variables: { // Copy/Paste all less variables with "_" prefix for replacing by php
            "color-main-background": "_color-main-background_",
            "color-main-border": "_color-main-border_",
            "color-main-secondary-border": "_color-main-secondary-border_",
            "color-main-tertiary-border": "_color-main-tertiary-border_",
            "color-main-title": "_color-main-title_",
            "color-main-text": "_color-main-text_",
            "color-main-text-bolder": "_color-main-text-bolder_",
            "color-main-secondary-text": "_color-main-secondary-text_",
            "color-main-accent": "_color-main-accent_",
            "color-main-accent-text": "_color-main-accent-text_",
            "color-forms-fields-placeholder": "_color-forms-fields-placeholder_",
            "color-forms-fields-secondary-placeholder": "_color-forms-fields-secondary-placeholder_",
            "color-forms-fields-text": "_color-forms-fields-text_",
            "color-forms-fields-background": "_color-forms-fields-background_",
            "color-forms-fields-border": "_color-forms-fields-border_",
            "color-forms-checkbox-check": "_color-forms-checkbox-check_",
            "color-forms-checkbox-check-background": "_color-forms-checkbox-check-background_",
            "color-forms-radio-check-background": "_color-forms-radio-check-background_",
            "color-forms-radio-check-border": "_color-forms-radio-check-border_",
            "color-breadcrumb-text": "_color-breadcrumb-text_",
            "color-breadcrumb-text-active": "_color-breadcrumb-text-active_",
            "color-product-list-info-separator": "_color-product-list-info-separator_",
            "color-product-tab-text": "_color-product-tab-text_",
            "color-product-tab-text-active": "_color-product-tab-text-active_",
            "color-main-range-slider": "_color-main-range-slider_",
            "color-main-range-slider-range": "_color-main-range-slider-range_",
            "color-main-range-slider-handle": "_color-main-range-slider-handle_",
            "color-product-swiper-button-background": "_color-product-swiper-button-background_",
            "color-product-details-price": "_color-product-details-price_",
            "color-product-details-out-of-stock": "_color-product-details-out-of-stock_",
            "color-main-blog-title": "_color-main-blog-title_",
            "color-main-order-details-parameter-value": "_color-main-order-details-parameter-value_",
            "color-main-order-status-pending": "_color-main-order-status-pending_",
            "color-main-order-status-complete": "_color-main-order-status-complete_",
            "color-header-background": "_color-header-background_",
            "color-header-top-line-background": "_color-header-top-line-background_",
            "color-header-top-line-text": "_color-header-top-line-text_",

            "color-header-compact-cart-background": "_color-header-compact-cart-background_",
            "color-header-fields-outer-border": "_color-header-fields-outer-border_",
            "color-header-fields-inner-border": "_color-header-fields-inner-border_",
            "color-header-fields-background": "_color-header-fields-background_",
            "color-footer-banners-background": "_color-footer-banners-background_",
            "color-footer-banners-text": "_color-footer-banners-text_",

            "color-testimonial-background": "_color-testimonial-background_",
            "color-testimonial-title": "_color-testimonial-title_",
            "color-testimonial-title-border": "_color-testimonial-title-border_",
            "color-testimonial-caption": "_color-testimonial-caption_",
            "color-testimonial-text": "_color-testimonial-text_",
            "color-newsletter-background": "_color-newsletter-background_",
            "color-newsletter-border": "_color-newsletter-border_",
            "color-newsletter-shadow": "_color-newsletter-shadow_",
            "color-newsletter-content-background": "_color-newsletter-content-background_",
            "color-newsletter-content-border": "_color-newsletter-content-border_",

            "color-footer-navigation-title": "_color-footer-navigation-title_",
            "color-footer-navigation-link": "_color-footer-navigation-link_",
            "color-footer-navigation-link-active": "_color-footer-navigation-link-active_",
            "color-newsletter-text": "_color-newsletter-text_",
            "color-newsletter-title": "_color-newsletter-title_",
            "color-newsletter-field-background": "_color-newsletter-field-background_",
            "color-newsletter-field-border": "_color-newsletter-field-border_",
            "color-newsletter-field-text": "_color-newsletter-field-text_",
            "color-newsletter-field-placeholder": "_color-newsletter-field-placeholder_",
            "color-newsletter-button-background": "_color-newsletter-button-background_",
            "color-newsletter-button-background-hover": "_color-newsletter-button-background-hover_",
            "color-newsletter-button-text-hover": "_color-newsletter-button-text-hover_",
            "color-newsletter-button-text": "_color-newsletter-button-text_",
            "color-footer-content-background": "_color-footer-content-background_",
            "color-footer-content-separator": "_color-footer-content-separator_",
            "color-footer-content-title": "_color-footer-content-title_",
            "color-footer-content-text": "_color-footer-content-text_",
            "color-header-navigation-text": "_color-header-navigation-text_",
            "color-header-navigation-background": "_color-header-navigation-background_",
            "color-header-navigation-border": "_color-header-navigation-border_",
            "color-header-navigation-link-background": "_color-header-navigation-link-background_",
            "color-header-navigation-submenu-background": "_color-header-navigation-submenu-background_",
            "color-header-navigation-submenu-text": "_color-header-navigation-submenu-text_",
            "color-footer-copy-text": "_color-footer-copy-text_",
            "color-footer-copy-link": "_color-footer-copy-link_",
            "color-footer-social-hover": "_color-footer-social-hover_",
            "color-button-background": "_color-button-background_",
            "color-button-text": "_color-button-text_",
            "color-button-background-hover": "_color-button-background-hover_",
            "color-button-second-background": "_color-button-second-background_",
            "color-button-second-text": "_color-button-second-text_",
            "color-button-second-background-hover": "_color-button-second-background-hover_",
            "color-button-disable-background": "_color-button-disable-background_",
            "color-button-disable-text": "_color-button-disable-text_",
            "color-label-new-text": "_color-label-new-text_",
            "color-label-new-background": "_color-label-new-background_",
            "color-label-sale-text": "_color-label-sale-text_",
            "color-label-sale-background": "_color-label-sale-background_",
            "color-label-delivery-text": "_color-label-delivery-text_",
            "color-label-delivery-background": "_color-label-delivery-background_",
            "color-label-discount-text": "_color-label-discount-text_",
            "color-label-discount-background": "_color-label-discount-background_",
            "color-label-leasing-text": "_color-label-leasing-text_",
            "color-label-leasing-background": "_color-label-leasing-background_",
            "color-label-featured-text": "_color-label-featured-text_",
            "color-label-featured-background": "_color-label-featured-background_",
            "color-label-delivery-text": "_color-label-delivery-text_",
            "color-label-delivery-background": "_color-label-delivery-background_",
            "color-welcome-wrapper-background": "_color-welcome-wrapper-background_",
            "color-welcome-wrapper-dashes": "_color-welcome-wrapper-dashes_",
            "color-welcome-background": "_color-welcome-background_",
            "color-welcome-shadow": "_color-welcome-shadow_",
            "color-welcome-border": "_color-welcome-border_",
            "color-welcome-text": "_color-welcome-text_",
            "color-slider-arrow-button-background": "_color-slider-arrow-button-background_",
            "color-slider-arrow-button-text": "_color-slider-arrow-button-text_",
            "color-contrast-section-wrapper-background": "_color-contrast-section-wrapper-background_",
            "color-contrast-section-wrapper-dashes": "_color-contrast-section-wrapper-dashes_",
            "color-contrast-section-title": "_color-contrast-section-title_",
            "color-contrast-section-title-border": "_color-contrast-section-title-border_",
            "color-contrast-section-text": "_color-contrast-section-text_",
            "color-latest-blog-section-wrapper-background": "_color-latest-blog-section-wrapper-background_",
            "color-latest-blog-section-wrapper-dashes": "_color-latest-blog-section-wrapper-dashes_",
            "color-latest-blog-section-title": "_color-latest-blog-section-title_",
            "color-latest-blog-section-title-border": "_color-latest-blog-section-title-border_",
            "color-latest-blog-section-text": "_color-latest-blog-section-text_",
            "color-latest-blog-section-secondary-text": "_color-latest-blog-section-secondary-text_",
            "color-checkout-radio-text": "_color-checkout-radio-text_",
            "color-checkout-signin-radio-text-active": "_color-checkout-signin-radio-text-active_",
            "color-checkout-radio-active": "_color-checkout-radio-active_",
            "color-checkout-radio-additional-text": "_color-checkout-radio-additional-text_",
            "color-checkout-radio-price-text": "_color-checkout-radio-price-text_",
            "color-checkout-summary-cart-background": "_color-checkout-summary-cart-background_",
            "color-popup-border": "_color-popup-border_",
            "color-popup-background": "_color-popup-background_"
        }
    },
    server: {
        http: 'http://tolevcheto.ccdev.pro' // Write http address of the theme
    },
    fonts: {
        google: [ // Array of all google fonts
            {
                family: 'Roboto',
                subsets: ['latin', 'cyrillic', 'greek'],
                styles: [300, 400, 500, 700, 900]
            }
        ],
        embed: { // Array of all embed fonts. If you don't have embed fonts, leave this object empty
        }
    },
    vendors: { // Array of all vendors. Example: "bower-pkg-name": "version"
        devDep: {
            "jquery-breakpoint-check": "*",
            "bootstrap-touchspin": "3.0.1",
            "toastr": "2.0.3",
            "twbs-pagination": "1.2.4",
            "theia-sticky-sidebar": "*",
            "lightgallery": "1.2.21",
            "jquery.scrollbar": "*"
        },
        styles: [ // All styles of the selected vendors
            '<%= _PATH.src_vendors %>/custom_pkg/jquery-ui.custom/jquery-ui.css',
            '<%= _PATH.src_vendors %>/custom_pkg/select2.custom/select2.css',
            '<%= _PATH.src_vendors %>/custom_pkg/jquery.uniform.custom/uniform.default.css',
            '<%= _PATH.src_vendors %>/custom_pkg/jquery.uniform.custom/uniformjs.css',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/less/bootstrap.less',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/less/font-awesome.less',
            '<%= _PATH.src_vendors %>/lightgallery/dist/css/lightgallery.css',
            '<%= _PATH.src_vendors %>/jquery.scrollbar/jquery.scrollbar.css'
        ],
        scripts: [ // All scripts of the selected vendors
            '<%= _PATH.src_vendors %>/jquery-breakpoint-check/js/jquery-breakpoint-check.js',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/dist/js/bootstrap.js',
            '<%= _PATH.src_vendors %>/custom_pkg/select2.custom/select2.js',
            '<%= _PATH.src_vendors %>/custom_pkg/resize-sensor/ResizeSensor.js',
            '<%= _PATH.src_vendors %>/bootstrap-touchspin/src/jquery.bootstrap-touchspin.js',
            '<%= _PATH.src_vendors %>/toastr/toastr.js',
            '<%= _PATH.src_vendors %>/twbs-pagination/jquery.twbsPagination.js',
            '<%= _PATH.src_vendors %>/lightgallery/dist/js/lightgallery-all.js',
            '<%= _PATH.src_vendors %>/jquery.scrollbar/jquery.scrollbar.js',
            '<%= _PATH.src_vendors %>/theia-sticky-sidebar/js/theia-sticky-sidebar.js'
        ],
        fonts: [
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.eot',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.svg',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.ttf',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.woff',
            '<%= _PATH.src_vendors %>/custom_pkg/bootstrap/fonts/glyphicons-halflings-regular.woff2',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.eot',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.svg',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.ttf',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.woff',
            '<%= _PATH.src_vendors %>/custom_pkg/fontawesome/fonts/fontawesome-webfont.woff2'
        ]
    }
};
