/*=============================================================================*\
    SMALL DEVICES / TABLETS
\*=============================================================================*/

@media (max-width: @screen-sm-max) {
	.sm-hidden {
		display: none;
	}

	.sm-visible {
		display: block;
	}

    ._promo-bar-wrapper {
        display: none;
    }

	/* #Footer
	-------------------------------------------------------------------------------*/

	._footer-cols {
		._text {
			width: 33.33%;

			+ ._navigation-footer {
				width: 66.66%;

				li {
					width: 50%;

					&:nth-child(3n+1) {
						clear: none;
					}

					&:nth-child(2n+1) {
						clear: both;
					}
				}
			}
		}
	}

	._navigation-footer {
		li {
			width: 33.33%;

			&:nth-child(4n+1) {
				clear: none;
			}

			&:nth-child(3n+1) {
				clear: both;
			}
		}
	}

	._compare-box-item {
		._remove {
			display: block;
		}
	}

	._compare-box-item-image,
	._compare-box-item-info {
		display: block;
	}

	._compare-box-item-image {
		margin-bottom: 10px;
	}

	._compare-box-item-info,
	._compare-box-item-title {
		padding: 0;
	}

	/* #Banners
	-------------------------------------------------------------------------------*/

	._banner-caption {
		padding: 7px;
		font-size: calc(@font-size-main ~'-' 1px); /* theme */
	}

	/* #Product details
	-------------------------------------------------------------------------------*/

	._products-list-head-item-image {
		img {
			max-width: 218px;
		}
	}

	._product-details-pictures-container {
		margin-bottom: @separator;
	}

	._product-details-leasing-button {
		text-align: center;
	}

	/* #Vendors
	-------------------------------------------------------------------------------*/

	._vendors-list {
		._vendor {
			width: 33.33%;

			&:nth-child(4n+1) {
				clear: none;
			}

			&:nth-child(3n+1) {
				clear: both;
			}
		}
	}

	/* #Account
	-------------------------------------------------------------------------------*/

	._sidebar-account {
		margin-bottom: @separator;
	}

	._sidebar-list-account {
		ul {
			display: table;
			table-layout: fixed;
			vertical-align: top;
			width: 100%;
		}

		li {
			display: table-cell;
			padding-right: 30px;
		}
	}

	/* #Cart
	-------------------------------------------------------------------------------*/

	._cart-sidebar {
		margin-top: 25px;
	}

	/* #Checkout
	-------------------------------------------------------------------------------*/

	._checkout-express,
	._checkout-cart {
		[class*='col-'].pull-left,
		[class*='col-'].pull-right {
			float: none !important;
		}
	}

	._checkout-sidebar {
		margin-top: 25px;
	}

    ._address-book {
        ._address-book-list {
            ul {
                margin: 0;

                li {
                    width: 100%;
                    padding: 0;

                    + li {
                        margin-top: 15px;
                    }
                }
            }
        }
    }
}