[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 1, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-5", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и допълнителни характерситики/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 1, "image_id": 2, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 1740, "price_to": 1740, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 2, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 2, "v1": "Чер<PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 1740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 3, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 1740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 4, "v1": "Сив", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 1740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 5, "v1": "<PERSON>ин", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 1740, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 2, "name": "Тефтер с твърда корица", "parent_id": 1, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "tefter-s-tvarda-korica-image_620b9524278fe.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 3, "color_id": 146, "percents": 68, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 4, "color_id": 77, "percents": 7, "R": 32, "G": 178, "B": 170, "hex": "#20B2AA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 102857}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 1, "name": "Тефтер с твърда корица", "parent_id": 1, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "tefter-s-tvarda-korica-image_620b95232bf63.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 1, "color_id": 146, "percents": 70, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 2, "color_id": 23, "percents": 15, "R": 0, "G": 139, "B": 139, "hex": "#008B8B"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 54840}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 1, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 2, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 3, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 121, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 2}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 122, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 3}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 123, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 4}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 124, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 5}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-4", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и допълнителни характерситики/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 3, "image_id": 4, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 4290, "price_to": 4290, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 10, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 7, "v1": "Чер<PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 4290, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 8, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 4290, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 9, "v1": "Сив", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 4290, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 10, "v1": "<PERSON>ин", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 4290, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 4, "name": "Тефтер с твърда корица-Copy", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "tefter-s-tvarda-korica-copy-image_620b973263dfb.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 6, "color_id": 146, "percents": 58, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 7, "color_id": 148, "percents": 9, "R": 154, "G": 205, "B": 50, "hex": "#9ACD32"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 8, "color_id": 63, "percents": 5, "R": 240, "G": 230, "B": 140, "hex": "#F0E68C"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 115670}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 3, "name": "Тефтер с твърда корица-Copy", "parent_id": 2, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "tefter-s-tvarda-korica-copy-image_620b973082e51.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 5, "color_id": 146, "percents": 60, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 105126}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 117, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 7}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 118, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 8}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 119, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 9}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 120, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 10}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 3, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-3", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и допълнителни характерситики/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 4, "image_id": 6, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 920, "price_to": 920, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 12, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 12, "v1": "Чер<PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 920, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 13, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 920, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 14, "v1": "Сив", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 920, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 15, "v1": "<PERSON>ин", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 920, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 6, "name": "Органайзер за химикалки-Copy", "parent_id": 3, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "organajzer-za-him<PERSON><PERSON><PERSON>-copy-image_620b977ad4f37.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 11, "color_id": 146, "percents": 58, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 12, "color_id": 2, "percents": 22, "R": 250, "G": 235, "B": 215, "hex": "#FAEBD7"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 63660}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 5, "name": "Органайзер за химикалки-Copy", "parent_id": 3, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "organajzer-za-him<PERSON>lki-copy-image_620b97790f8d0.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 9, "color_id": 146, "percents": 56, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 10, "color_id": 103, "percents": 33, "R": 253, "G": 245, "B": 230, "hex": "#FDF5E6"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 95109}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 7, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 8, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 9, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 113, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 12}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 114, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 13}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 115, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 14}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 116, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 15}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 4, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-2", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и допълнителни характерситики/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 2, "image_id": 7, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 2240, "price_to": 2240, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 17, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 17, "v1": "Чер<PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 2240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 18, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 2240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 19, "v1": "Сив", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 2240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 20, "v1": "<PERSON>ин", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 2240, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 7, "name": "Тефтер с меки корици-Copy", "parent_id": 4, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "tefter-s-meki-korici-copy-image_620b97cfdecd2.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 13, "color_id": 146, "percents": 86, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 51910}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 8, "name": "Тефтер с меки корици-Copy", "parent_id": 4, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "tefter-s-meki-korici-copy-image_620b97d167fcf.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 14, "color_id": 146, "percents": 95, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 31659}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 10, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 11, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 12, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 109, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 17}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 110, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 18}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 111, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 19}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 112, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 20}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 5, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-1", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и допълнителни характерситики/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 5, "image_id": 9, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 47230, "price_to": 47230, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 22, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 22, "v1": "Чер<PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 47230, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 23, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 47230, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 24, "v1": "Сив", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 47230, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 25, "v1": "<PERSON>ин", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 47230, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 9, "name": "Модерна писалка VPen-Copy", "parent_id": 5, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "moderna-pisalka-vpen-copy-image_620b98107b58a.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 15, "color_id": 146, "percents": 71, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 16, "color_id": 44, "percents": 6, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 17, "color_id": 50, "percents": 5, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 89874}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 10, "name": "Модерна писалка VPen-Copy", "parent_id": 5, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "moderna-pisalka-vpen-copy-image_620b98164f062.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 18, "color_id": 146, "percents": 87, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 19, "color_id": 50, "percents": 8, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 56861}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 13, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 14, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 15, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 105, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 22}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 106, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 23}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 107, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 24}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 108, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 25}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 6, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и допълнителни характерситики/повече информация за материал чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 6, "image_id": 12, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 2770, "price_to": 2770, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 27, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 27, "v1": "Чер<PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 2770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 28, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 2770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 29, "v1": "Сив", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 2770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 30, "v1": "<PERSON>ин", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "YG78DY", "barcode": "", "price": 2770, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 12, "name": "Принтер HP OfficeJet Pro 7730-Copy", "parent_id": 6, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "printer-hp-officejet-pro-7730-copy-image_620b988c55983.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 24, "color_id": 146, "percents": 67, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 25, "color_id": 53, "percents": 17, "R": 218, "G": 165, "B": 32, "hex": "#DAA520"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 93028}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 11, "name": "Принтер HP OfficeJet Pro 7730-Copy", "parent_id": 6, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "printer-hp-officejet-pro-7730-copy-image_620b988a7c13c.jpeg", "background": "#F5F5F5", "width": 800, "height": 800, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 20, "color_id": 146, "percents": 42, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 21, "color_id": 13, "percents": 18, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 22, "color_id": 50, "percents": 11, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 23, "color_id": 24, "percents": 5, "R": 184, "G": 134, "B": 11, "hex": "#B8860B"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 181923}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 16, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 17, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 18, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 101, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 27}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 102, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 28}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 103, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 29}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 104, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 30}, "relations": []}]}}]