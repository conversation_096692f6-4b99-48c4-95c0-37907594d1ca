/*=============================================================================*\
    LARGE DEVICES / DESKTOPS
\*=============================================================================*/

@media (max-width: @screen-tablet-max) {

	._gutter-sizer {
		width: 16px;
	}

	._header {
		&-mobile {
			display: block;
			z-index: 6;
		}

		._search-form-container {
			display: none;
		}

		._user-state {
			display: none;
		}
	}

	._navbar {
		order: 1;
	}

	._slider-main {
		order: 2;
	}

	/* #Header
	-------------------------------------------------------------------------------*/
	._navbar {
		display: none;
		padding: 0;

		.container {
			width: auto;
			margin: 0;
			padding: 0;
		}

		.col-md-12 {
			padding: 0;
			min-height: initial;
		}
	}

	._navigation {
		text-align: left;

		&-hamburger {
			display: block;
		}

		> ul {
			margin-top: -1px;
			border-width: 1px 0;
			border-style: solid;
			border-color: @color-nav-dropdown-border; /* theme */

			> li {
				margin: 0;
			}
		}

		&.block {
			> ul {
				display: block;
			}
		}

		li {
			display: block;
			position: relative;
			border-bottom: 1px solid;
			border-color: @color-nav-dropdown-border; /* theme */

			&:last-child {
				border-bottom: 0;
			}
		}

		a {
			min-height: 1px;
			padding: 12px 15px;
			position: relative;
		}

		.item-collapse {
			a {
				padding-right: 50px;
			}
		}

		.collapse-icon {
			display: block;
			width: 44px;
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			font-size: 0;
			border-left: 1px solid;
			border-color: @color-nav-dropdown-border; /* theme */

			&:before {
				font-family: @font-awesome;
				content:"\f067";
				font-size: 20px;
				.centerer(true, true);
			}

			&-active {
				&:before {
					content:"\f068";
				}
			}
		}

		&-dropdown {
			display: none;
			background: transparent !important;
			position: static;
			padding: 0;
			border-width: 1px 0 0;
			font-family: @font-family-base;
			border-color: @color-nav-dropdown-border; /* theme */

			ul {
				margin-bottom: 0;
			}

			li {
				float: none;
				width: auto;
				padding: 0;
				margin-bottom: 0;
				font-size: @font-size-medium;
				font-weight: normal;
				text-transform: initial;
			}

			a {
				display: block;
				color: @color-nav-text; /* theme */
			}

			._navigation-dropdown {
				border-top: 1px solid;
				border-color: @color-nav-dropdown-border; /* theme */
				display: none;

				ul {
					margin-top: 0;
				}

				li {
					padding: 0;

					a {
						color: @color-nav-text; /* theme */
					}
				}
			}

			.loop(0);

			.loop(@i) when (@i < 10) {
		        &-level-@{i} {
		        	background-color: #ccc;
		        	> ul {
		        		> li {
		        			> a {
		        				padding-left: (@i+1)*12px;
		        			}
		        		}
		        	}
			    }
  				
  				.loop((@i + 1));
	        }
		}


		.cc-open {
			> ._navigation-dropdown {
				display: block;
			}
		}
	}

	._navigation-links {
		margin-right: 0;

		li {
			&:last-child {
				&:after {
					display: none;
				}
			}
		}
	}

	._user-state {
		&.open {
			._user-state-dropdown {
				opacity: 1;
				visibility: visible;
				.translate(0, 0);
			}
		}
	}

	/* #Slider
	-------------------------------------------------------------------------------*/

	.slide-html {		
		h1 {
			font-size: 34px;
		}

		h2 {
			font-size: 32px;
		}

		h3 {
			font-size: 30px;
		}

		h4 {
			font-size: 26px;
		}

		h5 {
			font-size: 22px;
		}

		h6 {
			font-size: 18px;
		}
	}

	/* #Product
	-------------------------------------------------------------------------------*/

	._product {
		margin-bottom: 16px;

		&-quick-view {
			display: none;
		}
	}

	.col-md-8,
	.col-md-12,
	._product-details-relative {
		._products-list {
			._product {
				width: calc(~'33.33% - 11px');
			}
		}
	}

	/* #Vendors
	-------------------------------------------------------------------------------*/

	._vendors-list {
		._vendor {
			width: calc(~'33.33% - 11px');
			margin-bottom: 16px;

			&:nth-child(4n+1) {
				clear: none;
			}

			&:nth-child(3n+1) {
				clear: both;
			}
		}
	}

	/* #Cart
	-------------------------------------------------------------------------------*/

	._cart-compact-dropdown {
		display: none;
	}

	._cart-product {
		._remove {
			opacity: 1;
			visibility: visible !important;
		}
	}
}