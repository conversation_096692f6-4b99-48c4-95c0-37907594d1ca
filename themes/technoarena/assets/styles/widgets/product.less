/*=============================================================================*\
    PRODUCT
\*=============================================================================*/

/*  LIST
-------------------------------------------------------------------------------*/

._products-list-info {
	margin-bottom: @separator-small;
}

._products-list-info-item {
	overflow: hidden;
	color: @color-main-meta-text; /* theme */
	margin-bottom: @separator-small;

	&:last-child {
		margin-bottom: 0;
	}
}

._products-list-info-item-body {
	display: table;
	width: 100%;
}

._products-list-info-item-image {
	display: table-cell;
	vertical-align: top;
	width: 1%;
	padding: 5px 0;
	padding-right: 30px;

	img {
		max-width: 300px;
	}
}

._products-list-info-item-description {
	display: table-cell;
	vertical-align: top;
}

/*  Box
-------------------------------------------------------------------------------*/

._product {
	border-radius: 10px;
	padding: 15px 15px 0;
	position: relative;

	&:before {
		content: '';
		position: absolute;
		top: 15px;
		left: 0;
		bottom: 47px;
		border-left: 1px solid;
		border-color: @color-product-listing-borders; /* theme */
		transition: .2s;
	}

	@media @hover {
		&:hover {
			box-shadow: 0 0 6px rgba(0, 0, 0, .3);

			&:before {
				display: none;
			}

			+ ._product {
				&:before {
					display: none;
				}
			}

			._product-bar {
				display: block;
			}

			._product-quick-view {
				opacity: 1;
				visibility: visible;
			}
		}
	}
}

._product-image {
	margin-bottom: 10px;
	position: relative;
}

._product-image-thumb {
	display: block;
}

._product-image-thumb-holder {
	display: block;
	position: relative;

	img {
		width: auto;
		height: auto;
		max-height: 100%;
		max-width: 100%;
		.centerer(true, true);
	}
}

._product-ribbon-holder,
._product-details-ribbon-holder {
	position: absolute;
	bottom: 0;
	left: 0;
}

._product-ribbon,
._product-details-ribbon {
	display: block;
	line-height: 1;
	padding: 6px 10px 5px;
	font-size: 11px;
	margin-bottom: 1px;
	text-align: center;
}

._product-ribbon-new,
._product-details-ribbon-new {
	background-color: @color-label-new-background; /* theme */
	color: @color-label-new-text; /* theme */
}

._product-ribbon-sale,
._product-details-ribbon-sale {
	background-color: @color-label-sale-background; /* theme */
	color: @color-label-sale-text; /* theme */
}

._product-ribbon-delivery,
._product-details-ribbon-delivery {
	background-color: @color-label-delivery-background; /* theme */
	color: @color-label-delivery-text; /* theme */
}

._product-ribbon-custom,
._product-details-ribbon-custom {
	background-color: @color-label-custom-background; /* theme */
	color: @color-label-custom-text; /* theme */
}

._product-ribbon-featured,
._product-details-ribbon-featured {
	background-color: @color-label-featured-background; /* theme */
	color: @color-label-featured-text; /* theme */
}

._product-discount,
._product-leasing,
._product-ribbon-banner,
._product-details-ribbon-banner {
    position: absolute;
    z-index: 1;
}

._product-ribbon-banner {
    position: absolute;
    z-index: 1;
    max-width: 25%;

    img {
        max-width: 100%;
        max-height: 100px;
    }
}

._product-discount,
._product-details-discount {
	display: table;
	background-color: @color-label-discount-background; /* theme */
	border-radius: 50%;
	width: 62px;
	height: 62px;
	color: @color-label-discount-text; /* theme */
	white-space: nowrap;
	padding: 0;
	margin: 0;
	position: absolute;
	top: 0;
	left: 0;

	span {
		display: table-cell;
		vertical-align: middle;
		padding: 0 2px;
	}
}

._product-leasing,
._product-details-leasing {
	background-color: @color-label-leasing-background; /* theme */
	color: @color-label-leasing-text; /* theme */
}

._product-info {
	padding-bottom: 47px;
	text-align: center;
	position: relative;
}

._product-name {
	display: block;
	min-height: 50px;
}

._product-name-tag {
	font-size: @font-size-base;
	line-height: 1.15;

	a {
		color: @color-product-listing-title; /* theme */

		@media @hover {
			&:hover {
				color: @color-product-listing-highlight; /* theme */
			}
		}
	}
}

._product-price {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
	margin: 0 -5px;
	font-weight: 900;
	font-size: 18px;
	color: @color-product-listing-price; /* theme */
}

._product-price-old {
	order: 1;
	margin: 0 5px;
	font-size: 14px;
	color: @color-product-listing-price-old; /* theme */
	position: relative;
	top: 1px;
}

._product-price-compare {
	order: 2;
	margin: 0 5px;
}

._product-bar {
	display: none;
	border-top: 1px solid;
	border-color: @color-product-listing-borders; /* theme */
	font-size: 12px;
	line-height: 1;
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 13px 0;
}

._product-bar-col-favorite {
	float: left;
}

._product-bar-col-compare {
	float: right;
}

._product-compare,
._product-add-to-favorite {
	color: @color-product-listing-actions; /* theme */

	&:focus {
		color: @color-product-listing-actions; /* theme */
	}

	&.active {
		.fa {
			color: @color-product-listing-actions-highlight; /* theme */	
		}
	}

	@media @hover {
		&:hover {
			color: @color-product-listing-actions-highlight; /* theme */
		}
	}
}

._product-compare {
	._checkbox {
		padding: 0;
		min-height: initial;
		font-size: inherit;
		line-height: inherit;
	}

	.checker {
		position: static;
		top: 0;
		width: auto;
		height: auto;

		span,
		span.checked {
			width: auto;
			height: auto;
			border: 0;
			color: inherit; /* theme */

			&:before {
				content:"\f021";
				font-family: FontAwesome, sans-serif;
				font-size: 14px;
				vertical-align: middle;
				position: relative;
				top: -1px;
				left: 0;
				transform: translate(0, 0);
			}
		}
		
		span.checked {
			color: @color-product-listing-actions-highlight; /* theme */
		}

		input {
			display: none;
		}
	}
}

._product-add-to-favorite {
	.fa {
		font-size: 14px;
		vertical-align: middle;
		position: relative;
		top: -1px;
	}
}

//._product-add,
//._product-quick-view,
//._product-description {
//	display: none;
//}

._product-add {
	margin: 10px 0;

	._button {
		width: 100%;
		._button-small();
	}
}

._product-quick-view {
	position: absolute;
	top: 50%;
	left: 50%;
	._button();
	._button-small();
	padding: 0;
	width: 32px;
	line-height: 32px;
	font-size: 15px;
	.centerer(true, true);
	opacity: 0;
	transition: .5s;
	visibility: hidden;
}

._product-description {
	display: none;
}

/*  DETAILS
-------------------------------------------------------------------------------*/

/* Details */
._product-details {
	position: relative;
	z-index: 2;
}

._product-details-pictures-container {
	._product-details-title {
		display: none;
	}
}

._product-details-pictures {
	display: table;
	width: 100%;
}

._product-details-image-container {
	display: table-cell;
	vertical-align: top;
	width: 100%;
}

._product-details-image {
	border: 1px solid;
	border-color: @color-main-borders; /* theme */
	position: relative;
	overflow: hidden;
}

._product-details-image-thumb {
	padding-bottom: 100%;
	position: relative;

	.image {
		max-width: 100%;
		max-height: 100%;
		.centerer(true, true);
	}
}

@gallery-thumb-size: 68px;
@gallery-thumb-offset: 10px;

._product-details-gallery-container {
	display: table-cell;
	vertical-align: top;
	width: 1px;
	position: relative;
	margin-bottom: 50px;

	&._hide-nav {
		.swiper-button {
			display: none;
		}
	}

	.loader-container {
		display: none;
	}

	.swiper-button {
		background: @color-button-background; /* theme */
		color: @color-button-text; /* theme */
		font-size: 20px;
		width: @gallery-thumb-size;
		height: 20px;
		margin: 10px 0 0;
		padding: 0;
		position: relative;
		top: auto;
		left: auto;
		right: auto;
		bottom: auto;
		visibility: hidden;
		z-index: 1;

		&.swiper-button-disabled {
			opacity: 0;

			&.swiper-button-prev {
				display: none;

				+ .swiper-button-next {
					margin-top: 32px;
				}
			}

			&.swiper-button-next {
				display: none;
			}
		}

		+ .swiper-button {
			margin-top: 2px;
		}

		.fa {
			.centerer(true, true);
		}
	}
}

._product-details-gallery {
	overflow: hidden;
	visibility: hidden;

	&.swiper-container-vertical {
		visibility: visible;

		~ .swiper-button {
			visibility: visible;
		}
	}

	.swiper-wrapper {
		height: @gallery-thumb-size*5 + @gallery-thumb-offset*4;
	}

	.cc-active {
		a {
			&:before {
				content: '';
				border: 2px solid;
				border-color: @color-main-highlight; /* theme */
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				z-index: 1;
			}
		}
	}

	ul {
		list-style-type: none;
		width: @gallery-thumb-size;
		margin-right: 20px;
		justify-content: flex-start;
		align-content: flex-start;
	}

	li {
		height: auto !important;
	}

	a {
		display: block;
		background-color: @color-second-background; /* theme */
		padding-bottom: 100%;
		position: relative;
	}

	img {
		max-width: 100%;
		max-height: 100%;
		.centerer(true, true);
	}
}

._product-details-ribbon-holder {
	bottom: 10px;
	left: 10px;
}

._product-details-discount {
	top: 10px;
	left: 10px;
}

/*  Product details
-------------------------------------------------------------------------------*/

._product-details-title {
	border-bottom: 1px solid;
	border-color: @color-main-borders; /* theme */
	padding-bottom: 15px;
	margin-bottom: 24px;
}

._product-details-meta {
	color: @color-main-meta-text; /* theme */
}

._product-details-meta-item {
	display: inline-block;
	margin-right: 20px;
	margin-bottom: 8px;

	&:last-child {
		margin-right: 0;
	}
}

._product-details-meta-value {
	font-style: normal;
}

._product-details-info {
	display: table;
	width: 100%;
	margin-bottom: 25px;
	color: @color-main-meta-text; /* theme */
}

._product-details-text,
._product-details-price {
	display: table-cell;
}

._product-details-text {
	vertical-align: top;
	padding-right: 30px;
}

._product-details-price {
	vertical-align: bottom;
	width: 220px;
	line-height: @line-height-medium;
}

._product-details-price-value {
	font-style: normal;
	font-weight: bold;
}

._product-details-price-old {
	display: block;
	font-size: @font-size-large;

	._product-details-price-value {
		color: @color-main-text; /* theme */
		text-decoration: line-through;
	}
}

._product-details-price-saved {
	display: block;
	margin-bottom: 11px;

	._product-details-price-value {
		color: @color-product-price; /* theme */
	}
}

._product-details-price-new {
	display: block;
	color: @color-product-price; /* theme */
	font-size: 36px;
	line-height: @line-height-low;
	font-weight: 900;
}

._product-details-stock-status-bar {
	display: inline-block;
	padding: 2px 7px;
	font-size: calc(@font-size-base ~'-' 2px); /* theme */
	position: relative;
	margin-top: 10px;

	&.in-stock {
		background-color: @color-status-instock-background;
		color: @color-status-instock-text;
	}
	&.out-stock {
		background-color: @color-status-outofstock-background;
		color: @color-status-outofstock-text;
	}
	&.two-days {
		background-color: @color-status-twodays-background;
		color: @color-status-twodays-text;
	}
	&.pre-order {
		background-color: @color-status-preorder-background;
		color: @color-status-preorder-text;
	}
	&.request {
		background-color: @color-status-request-background;
		color: @color-status-request-text;
	}

	+ ._product-details-sku {
		float: right;
	}
}

._product-details-countdown {
	margin-top: 15px;
}

._product-details-parameters {
	background-color: @color-second-background; /* theme */
	border-radius: 10px;
	padding: 20px 30px;
	margin-bottom: 25px;
	color: @color-second-text; /* theme */

	._label {
		display: block;
		margin-bottom: 2px;
		font-weight: bold;
		.uppercase();
	}

	._form-row {
		&:last-child {
			margin-bottom: 0;
		}
	}

	._button {
		padding-left: 10px;
		padding-right: 10px;
	}
}

._product-details-parameters-row {
	margin-bottom: 20px;

	&:last-child {
		margin-bottom: 0;
	}
}

._product-details-parameters-out-of-stock {
	display: block;
	margin-top: 10px;
	font-size: @font-size-xlarge;
	color: @color-second-highlight; /* theme */
	text-align: center;
}

._parameter-radio-values {
	margin-bottom: -3px;
	.clearfix();
}

._parameter-radio-value {
	float: left;
	display: table;
	height: 36px;
	min-width: 44px;
	margin-right: 3px;
    margin-bottom: 3px;

    ._radio {
    	display: table-cell;
    	vertical-align: middle;
		border: 1px solid;
		border-color: @color-second-borders; /* theme */
		padding: 5px;
	    text-align: center;
	    font-weight: normal;
	    font-size: 13px;

        &.active {
        	border-width: 2px;
        	padding: 4px;
			border-color: @color-second-highlight; /* theme */
        }

        .radio {
        	display: none;
        }
    }
}

._parameter-image-values {
    margin-left: -1%;
    margin-bottom: -1%;
    .clearfix();
}

._parameter-image-value {
    float: left;
    width: 24%;
    max-width: 100px;
    border: 1px solid @color-second-borders; /* theme */
    margin-left: 1%;
    margin-bottom: 1%;
    word-break: break-word;

    &.active {
        border-width: 2px;
        border-color: @color-second-highlight; /* theme */
    }

    ._radio {
        display: block;
        height: 0;
        padding: 0;
        padding-bottom: 100%;
        font-size: @font-size-small; /* theme */
        position: relative;
        overflow: hidden;

        .radio {
            top: 5px;
            left: 5px;
        }

        .radio-label {
            display: inline-block;
            padding-top: 4px;
            padding-left: 30px;
            padding-right: 5px;
        }

        img {
            .centerer(true, true);

            ~ .radio,
            ~ .radio-label {
                display: none;
            }
        }
    }
}

._parameter-color-values {
    margin-right: -5px;
    margin-bottom: -5px;
    .clearfix();
}

._parameter-color-value {
    float: left;
    margin-right: 5px;
    margin-bottom: 5px;

    &.active {
        ._radio {
			border-width: 2px;
            border-color: @color-second-highlight; /* theme */
        }
    }

    ._radio {
        display: block;
        width: 34px;
        height: 34px;
        border: 1px solid;
		border-color: @color-second-borders; /* theme */
        padding: 0;
        margin: 0;
        font-size: 0;
        line-height: 0;

        .radio {
            display: none;
        }

        .radio-label {
            display: none;
        }
    }
}

._form-row-actions {
	._form-col {
		vertical-align: top;
	}

	._form-col-button {
		vertical-align: bottom;
	}
}

._product-details-parameters-quantity,
._cart-product-quantity {
	.input-group {
		display: block;
		max-width: 165px;

		&:before {
			content: '';
			border-left: @forms-border-width solid;
			border-color: @color-forms-fields-borders; /* theme */
			position: absolute;
			top: 11px;
			bottom: 11px;
			right: @select-arrow-width - @forms-border-width;
			z-index: 2;
		}

		._field {
			float: none;
			border-radius: 5px !important;
			text-align: left;
			z-index: 1;
		}
	}

	.input-group-btn {
		position: static;

		.btn {
			background: transparent; /* theme */
			width: @select-arrow-width;
			height: @forms-height/2 - @forms-border-width;
			color: @color-forms-fields-text; /* theme */
			position: absolute;
			right: 0;
			z-index: 2;

			&:focus,
			&:active {
				box-shadow: none;
			}

			&:before {
				font-family: @font-awesome, sans-serif;
				font-size: @font-size-xlarge;
			}

			&.bootstrap-touchspin-up {
				top: @forms-border-width;

				&:before {
					content:"\f106";
					margin-top: 3px;
				}
			}

			&.bootstrap-touchspin-down {
				bottom: @forms-border-width;

				&:before {
					content:"\f107";
					margin-top: -3px;
				}
			}
		}
	}
}

.quantity-left-js {
	margin-bottom: 0;

	._form-actions {
		position: relative;
	}
}

/*  Tabs
-------------------------------------------------------------------------------*/

._product-details-tabs-nav {
	padding: 0 30px;
	margin-bottom: -2px;
	font-size: @font-size-large;
	font-weight: bold;
	position: relative;
	z-index: 1;
	.uppercase();
}

._product-details-tabs-nav-link {
	display: inline-block;
	border-bottom: 2px solid;
	border-color: transparent; /* theme */
	padding: 0 10px 15px;
	margin-right: 20px;
	color: @color-main-text; /* theme */

	&:last-child {
		margin-right: 0;
	}

	@media @hover {
		&:hover {
			color: @color-main-titles; /* theme */
		}
	}

	&.active {
		border-color: @color-main-highlight; /* theme */
		color: @color-main-titles; /* theme */
	}
}

._product-details-tabs {
	border: 1px solid;
	border-color: @color-main-borders; /* theme */
	border-radius: 10px;
	padding: 30px;
}

._product-details-tab {
	display: none;

	&:first-child {
		display: block;
	}

	._facebook-comments {
		margin-bottom: @separator;

		&:last-child {
			margin-bottom: 0;
		}
	}
}

._product-details-files,
._product-details-properties ul {
	list-style-type: none;

	li {
		display: table;
		width: 100%;
		color: @color-main-text; /* theme */
		padding: 8px 25px;

		&:nth-child(odd) {
			background-color: @color-second-background; /* theme */
			color: @color-second-text; /* theme */

			a {
				color: @color-second-text; /* theme */

				@media @hover {
					&:hover {
						color: @color-second-highlight; /* theme */
					}
				}
			}
		}
	}

	a {
		color: @color-main-text; /* theme */

		@media @hover {
			&:hover {
				color: @color-main-highlight; /* theme */
			}
		}
	}
}

._product-details-properties-title,
._product-details-properties-value {
	display: table-cell;
	vertical-align: top;
}

._product-details-properties-title {
	width: 360px;
}

._product-details-files {
	a {
		&:before {
			content:"\f019";
			font-family: FontAwesome, sans-serif;
			margin-right: 10px;
		}
	}
}

._product-details-actions {
	font-size: 0;
	text-align: right;
}

._product-details-wishlist,
._product-details-compare,
._product-details-share {
	display: inline-block;
	vertical-align: top;
	margin-right: 10px;

	&:last-child {
		margin-right: 0;
	}
}

._button-share,
._product-details-wishlist-button,
._product-details-compare .checker > span {
	display: inline-block;
	border: 1px solid;
	border-color: @color-product-icons; /* theme */
	border-radius: 5px;
	width: @forms-height;
	height: @forms-height;
	line-height: @forms-height;
	text-align: center;
	color: @color-product-icons; /* theme */
	font-size: @font-size-xlarge;

	&:focus {
		color: @color-product-icons; /* theme */
	}
}

._product-details-share.open ._button-share,
._product-details-wishlist-button.active,
._product-details-compare .checker > span.checked {
	border-color: @color-product-icons-highlight; /* theme */
	color: @color-product-icons-highlight; /* theme */
}

._product-details-compare {
	._checkbox {
		padding: 0;
		font-size: 0;

		.checker {
			width: auto;
			height: auto;
			position: relative;
			margin: 0;

			> span {
				&:before {
					content:"\f021";
					font-family: @font-awesome, sans-serif;
					font-size: inherit;
					position: static;
					top: 0;
					left: 0;
					transform: translate(0, 0);
				}

				input {
					display: none;
				}
			}
		}
	}
}

._product-details-share {
	position: relative;

	&.open {
		._product-details-share-dropdown {
			display: block;
		}
	}
}

._product-details-share-dropdown {
	display: none;
	background-color: @color-main-background; /* theme */
	border: 1px solid;
	border-color: @color-product-icons; /* theme */
	border-radius: 5px;
	position: absolute;
	top: calc(100% ~'+' 13px);
	right: 0;
	font-size: 0;
	padding: 15px;

	&:before,
	&:after {
		content: '';
		position: absolute;
		bottom: 100%;
		right: 15px;
		border-left: 10px solid transparent;
		border-right: 10px solid transparent;
		border-bottom: 10px solid @color-main-background; /* theme */
		z-index: 1;
	}

	&:after {
		border-left-width: 12px;
		border-right-width: 12px;
		border-bottom-width: 12px;
		border-bottom-color: @color-product-icons; /* theme */
		right: 13px;
		z-index: 0;
	}

	a {
		display: inline-block;
		margin-bottom: 0 !important;
		float: none !important;
	}
}

/*  Sticky
-------------------------------------------------------------------------------*/

._product-details-sticky-container {
	background-color: @color-second-background; /* theme */
	border-bottom: 1px solid;
	border-color: @color-second-borders; /* theme */
	color: @color-second-text; /* theme */
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: @z-header-fixed - 1;
	transform: translate(0, -100%);
	transition: .5s;

	&.open {
		transform: translate(0, 0);
	}
}

._product-details-sticky {
	display: table;
	width: 100%;
}

._product-details-sticky-image,
._product-details-sticky-title,
._product-details-sticky-info {
	display: table-cell;
	vertical-align: middle;
}

._product-details-sticky-image {
	width: 40px;
	min-width: 40px;
	padding: 10px 0;
}

._product-details-sticky-image-thumb {
	display: block;
	background-color: @color-second-image-box; /* theme */
	box-shadow: inset 0 0 0 1px @color-second-borders; /* theme */
	padding-bottom: 100%;
	position: relative;

	img {
		max-width: calc(100% ~'-' 2px);
		max-height: calc(100% ~'-' 2px);
		.centerer(true, true);
	}
}

._product-details-sticky-title {
	width: 400px;
	padding: 10px 15px;
	color: @color-second-titles; /* theme */
}

._product-details-sticky-info {
	padding: 10px 0;
	text-align: right;
	white-space: nowrap;

	._product-details-price,
	._product-details-price-meta,
	._product-details-price-new,
	._product-details-sticky-button {
		display: inline-block;
		vertical-align: middle;
		margin-left: 23px;

		&:first-child {
			margin-left: 0;
		}
	}

	._product-details-price {
		width: auto;
	}

	._product-details-price-meta {
		color: @color-second-meta-text; /* theme */
		line-height: @line-height-low;
		text-align: left;
	}

	._product-details-price-old {
		._product-details-price-value {
			color: @color-second-text; /* theme */
		}
	}

	._product-details-price-saved {
		margin-bottom: 0;

		._product-details-price-value {
			color: @color-second-highlight; /* theme */
		}
	}

	._product-details-price-new {
		color: @color-second-highlight; /* theme */
	}
}