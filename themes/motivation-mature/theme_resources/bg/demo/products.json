[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 1, "url_handle": "sablaznyavasht-komplekt-cottelli", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Размер", "p2": "Цвят", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/характеристики/повече информация за материал чрез текст, стандартни видеа и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 1, "image_id": 13, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 12740, "price_to": 12740, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 10, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 1, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 2, "v1": "S", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 1, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 3, "v1": "S", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 1, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 4, "v1": "S", "v2": "<PERSON>ин", "v3": null, "v1_id": 1, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 5, "v1": "S", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 1, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 6, "v1": "S", "v2": "Тъмно сив", "v3": null, "v1_id": 1, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 7, "v1": "S", "v2": "Светло сив", "v3": null, "v1_id": 1, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 8, "v1": "S", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 1, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 9, "v1": "M", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 2, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 10, "v1": "M", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 2, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 11, "v1": "M", "v2": "<PERSON>ин", "v3": null, "v1_id": 2, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 12, "v1": "M", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 2, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 13, "v1": "M", "v2": "Тъмно сив", "v3": null, "v1_id": 2, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 14, "v1": "M", "v2": "Светло сив", "v3": null, "v1_id": 2, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 15, "v1": "M", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 2, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 16, "v1": "L", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 3, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 17, "v1": "L", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 3, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 18, "v1": "L", "v2": "<PERSON>ин", "v3": null, "v1_id": 3, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 19, "v1": "L", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 3, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 20, "v1": "L", "v2": "Тъмно сив", "v3": null, "v1_id": 3, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 21, "v1": "L", "v2": "Светло сив", "v3": null, "v1_id": 3, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 22, "v1": "L", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 3, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 23, "v1": "XL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 10, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 24, "v1": "XL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 10, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 25, "v1": "XL", "v2": "<PERSON>ин", "v3": null, "v1_id": 10, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 26, "v1": "XL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 10, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 27, "v1": "XL", "v2": "Тъмно сив", "v3": null, "v1_id": 10, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 28, "v1": "XL", "v2": "Светло сив", "v3": null, "v1_id": 10, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 29, "v1": "XL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 10, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 30, "v1": "XXL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 11, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 31, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 11, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 32, "v1": "XXL", "v2": "<PERSON>ин", "v3": null, "v1_id": 11, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 33, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 11, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 34, "v1": "XXL", "v2": "Тъмно сив", "v3": null, "v1_id": 11, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 35, "v1": "XXL", "v2": "Светло сив", "v3": null, "v1_id": 11, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 36, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 11, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 37, "v1": "XXXL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 12, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 38, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 12, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 39, "v1": "XXXL", "v2": "<PERSON>ин", "v3": null, "v1_id": 12, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 40, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 12, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 41, "v1": "XXXL", "v2": "Тъмно сив", "v3": null, "v1_id": 12, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 42, "v1": "XXXL", "v2": "Светло сив", "v3": null, "v1_id": 12, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 43, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 12, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 12740, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 13, "name": "Дамски Елек AMG", "parent_id": 1, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "damski-elek-amg-image_620e0599da3bd.jpeg", "background": "#F4A460", "width": 683, "height": 1024, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 54, "color_id": 126, "percents": 10, "R": 244, "G": 164, "B": 96, "hex": "#F4A460"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 55, "color_id": 115, "percents": 9, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 56, "color_id": 34, "percents": 8, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 57, "color_id": 114, "percents": 7, "R": 255, "G": 218, "B": 185, "hex": "#FFDAB9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 58, "color_id": 124, "percents": 7, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 59, "color_id": 138, "percents": 6, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 60, "color_id": 129, "percents": 6, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 61, "color_id": 13, "percents": 6, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 62, "color_id": 44, "percents": 6, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 260986}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 14, "name": "Дамски Елек AMG", "parent_id": 1, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "damski-elek-amg-image_620e059d0050a.jpeg", "background": "#CD853F", "width": 669, "height": 1024, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 63, "color_id": 115, "percents": 17, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 64, "color_id": 34, "percents": 16, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 65, "color_id": 126, "percents": 11, "R": 244, "G": 164, "B": 96, "hex": "#F4A460"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 66, "color_id": 124, "percents": 10, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 67, "color_id": 129, "percents": 8, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 229891}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 1, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 2, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 3, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 664, "parameter_id": 1, "parameter_option_id": 1, "variant_id": 8}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 665, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 37}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 666, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 38}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 667, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 39}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 668, "parameter_id": 2, "parameter_option_id": 13, "variant_id": 40}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 669, "parameter_id": 2, "parameter_option_id": 14, "variant_id": 41}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 670, "parameter_id": 2, "parameter_option_id": 15, "variant_id": 42}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 671, "parameter_id": 2, "parameter_option_id": 16, "variant_id": 43}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 672, "parameter_id": 1, "parameter_option_id": 2, "variant_id": 15}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 673, "parameter_id": 1, "parameter_option_id": 3, "variant_id": 22}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 674, "parameter_id": 1, "parameter_option_id": 10, "variant_id": 29}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 675, "parameter_id": 1, "parameter_option_id": 11, "variant_id": 36}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 676, "parameter_id": 1, "parameter_option_id": 12, "variant_id": 43}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 13, "url_handle": "komb<PERSON><PERSON>", "name": "Комбинирай:", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "", "seo_title": "Комбинирай:", "seo_description": null, "category_id": null, "vendor_id": null, "image_id": null, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 37500, "price_to": 37500, "price_percent": null, "individual_price": 0, "price_type": "price", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 343, "short_description": null, "featured": 0, "description_title": null, "type": "bundle", "is_hidden": 0, "per_row": 4, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": null}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 343, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": null, "barcode": null, "price": 37500, "delivery_price": null, "weight": null}, "relations": []}], "images": [], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 37, "parameter": "timer_list", "value": "on"}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 14, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-7", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Размер", "p2": "Цвят", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/характеристики/повече информация за материал чрез текст, стандартни видеа и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 1, "image_id": 15, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 9770, "price_to": 9770, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 345, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 345, "v1": "S", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 1, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 346, "v1": "S", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 1, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 347, "v1": "S", "v2": "<PERSON>ин", "v3": null, "v1_id": 1, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 348, "v1": "S", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 1, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 349, "v1": "S", "v2": "Тъмно сив", "v3": null, "v1_id": 1, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 350, "v1": "S", "v2": "Светло сив", "v3": null, "v1_id": 1, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 351, "v1": "S", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 1, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 352, "v1": "M", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 2, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 353, "v1": "M", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 2, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 354, "v1": "M", "v2": "<PERSON>ин", "v3": null, "v1_id": 2, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 355, "v1": "M", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 2, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 356, "v1": "M", "v2": "Тъмно сив", "v3": null, "v1_id": 2, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 357, "v1": "M", "v2": "Светло сив", "v3": null, "v1_id": 2, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 358, "v1": "M", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 2, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 359, "v1": "L", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 3, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 360, "v1": "L", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 3, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 361, "v1": "L", "v2": "<PERSON>ин", "v3": null, "v1_id": 3, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 362, "v1": "L", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 3, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 363, "v1": "L", "v2": "Тъмно сив", "v3": null, "v1_id": 3, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 364, "v1": "L", "v2": "Светло сив", "v3": null, "v1_id": 3, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 365, "v1": "L", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 3, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 366, "v1": "XL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 10, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 367, "v1": "XL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 10, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 368, "v1": "XL", "v2": "<PERSON>ин", "v3": null, "v1_id": 10, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 369, "v1": "XL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 10, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 370, "v1": "XL", "v2": "Тъмно сив", "v3": null, "v1_id": 10, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 371, "v1": "XL", "v2": "Светло сив", "v3": null, "v1_id": 10, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 372, "v1": "XL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 10, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 373, "v1": "XXL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 11, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 374, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 11, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 375, "v1": "XXL", "v2": "<PERSON>ин", "v3": null, "v1_id": 11, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 376, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 11, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 377, "v1": "XXL", "v2": "Тъмно сив", "v3": null, "v1_id": 11, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 378, "v1": "XXL", "v2": "Светло сив", "v3": null, "v1_id": 11, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 379, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 11, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 380, "v1": "XXXL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 12, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 381, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 12, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 382, "v1": "XXXL", "v2": "<PERSON>ин", "v3": null, "v1_id": 12, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 383, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 12, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 384, "v1": "XXXL", "v2": "Тъмно сив", "v3": null, "v1_id": 12, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 385, "v1": "XXXL", "v2": "Светло сив", "v3": null, "v1_id": 12, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 386, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 12, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9770, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 15, "name": "Съблазняващ комплект Cottelli-Copy", "parent_id": 14, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "sablaznavas-komplekt-cottelli-copy-image_620e071382c70.jpeg", "background": "#696969", "width": 683, "height": 1024, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 68, "color_id": 44, "percents": 11, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 69, "color_id": 115, "percents": 10, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 70, "color_id": 34, "percents": 7, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 71, "color_id": 138, "percents": 6, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 72, "color_id": 114, "percents": 5, "R": 255, "G": 218, "B": 185, "hex": "#FFDAB9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 73, "color_id": 129, "percents": 5, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 256188}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 16, "name": "Съблазняващ комплект Cottelli-Copy", "parent_id": 14, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "sablaz<PERSON><PERSON>-komplekt-cottelli-copy-image_620e0716979a8.jpeg", "background": "#F4A460", "width": 669, "height": 1024, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 74, "color_id": 126, "percents": 10, "R": 244, "G": 164, "B": 96, "hex": "#F4A460"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 75, "color_id": 115, "percents": 9, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 76, "color_id": 44, "percents": 8, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 77, "color_id": 34, "percents": 8, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 78, "color_id": 124, "percents": 7, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 79, "color_id": 129, "percents": 6, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 80, "color_id": 146, "percents": 6, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 81, "color_id": 13, "percents": 6, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 82, "color_id": 138, "percents": 5, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 191060}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 38, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 39, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 40, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 651, "parameter_id": 1, "parameter_option_id": 1, "variant_id": 351}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 652, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 380}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 653, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 381}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 654, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 382}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 655, "parameter_id": 2, "parameter_option_id": 13, "variant_id": 383}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 656, "parameter_id": 2, "parameter_option_id": 14, "variant_id": 384}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 657, "parameter_id": 2, "parameter_option_id": 15, "variant_id": 385}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 658, "parameter_id": 2, "parameter_option_id": 16, "variant_id": 386}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 659, "parameter_id": 1, "parameter_option_id": 2, "variant_id": 358}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 660, "parameter_id": 1, "parameter_option_id": 3, "variant_id": 365}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 661, "parameter_id": 1, "parameter_option_id": 10, "variant_id": 372}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 662, "parameter_id": 1, "parameter_option_id": 11, "variant_id": 379}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 663, "parameter_id": 1, "parameter_option_id": 12, "variant_id": 386}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 15, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-6", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Размер", "p2": "Цвят", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/характеристики/повече информация за материал чрез текст, стандартни видеа и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 1, "image_id": 17, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 17240, "price_to": 17240, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 388, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 388, "v1": "S", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 1, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 389, "v1": "S", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 1, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 390, "v1": "S", "v2": "<PERSON>ин", "v3": null, "v1_id": 1, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 391, "v1": "S", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 1, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 392, "v1": "S", "v2": "Тъмно сив", "v3": null, "v1_id": 1, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 393, "v1": "S", "v2": "Светло сив", "v3": null, "v1_id": 1, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 394, "v1": "S", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 1, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 395, "v1": "M", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 2, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 396, "v1": "M", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 2, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 397, "v1": "M", "v2": "<PERSON>ин", "v3": null, "v1_id": 2, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 398, "v1": "M", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 2, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 399, "v1": "M", "v2": "Тъмно сив", "v3": null, "v1_id": 2, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 400, "v1": "M", "v2": "Светло сив", "v3": null, "v1_id": 2, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 401, "v1": "M", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 2, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 402, "v1": "L", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 3, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 403, "v1": "L", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 3, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 404, "v1": "L", "v2": "<PERSON>ин", "v3": null, "v1_id": 3, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 405, "v1": "L", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 3, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 406, "v1": "L", "v2": "Тъмно сив", "v3": null, "v1_id": 3, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 407, "v1": "L", "v2": "Светло сив", "v3": null, "v1_id": 3, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 408, "v1": "L", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 3, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 409, "v1": "XL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 10, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 410, "v1": "XL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 10, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 411, "v1": "XL", "v2": "<PERSON>ин", "v3": null, "v1_id": 10, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 412, "v1": "XL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 10, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 413, "v1": "XL", "v2": "Тъмно сив", "v3": null, "v1_id": 10, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 414, "v1": "XL", "v2": "Светло сив", "v3": null, "v1_id": 10, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 415, "v1": "XL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 10, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 416, "v1": "XXL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 11, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 417, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 11, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 418, "v1": "XXL", "v2": "<PERSON>ин", "v3": null, "v1_id": 11, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 419, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 11, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 420, "v1": "XXL", "v2": "Тъмно сив", "v3": null, "v1_id": 11, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 421, "v1": "XXL", "v2": "Светло сив", "v3": null, "v1_id": 11, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 422, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 11, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 423, "v1": "XXXL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 12, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 424, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 12, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 425, "v1": "XXXL", "v2": "<PERSON>ин", "v3": null, "v1_id": 12, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 426, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 12, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 427, "v1": "XXXL", "v2": "Тъмно сив", "v3": null, "v1_id": 12, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 428, "v1": "XXXL", "v2": "Светло сив", "v3": null, "v1_id": 12, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 429, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 12, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 17240, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 17, "name": "Еротичен комплект Alcyone-Copy", "parent_id": 15, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "eroticen-komplekt-alcyone-copy-image_620e077858f00.jpeg", "background": "#DEB887", "width": 683, "height": 1024, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 83, "color_id": 13, "percents": 16, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 84, "color_id": 124, "percents": 12, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 85, "color_id": 115, "percents": 10, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 86, "color_id": 44, "percents": 9, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 87, "color_id": 114, "percents": 7, "R": 255, "G": 218, "B": 185, "hex": "#FFDAB9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 88, "color_id": 129, "percents": 7, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 89, "color_id": 138, "percents": 6, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 90, "color_id": 87, "percents": 5, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 91, "color_id": 34, "percents": 5, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 262500}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 18, "name": "Еротичен комплект Alcyone-Copy", "parent_id": 15, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "eroticen-komplekt-alcyone-copy-image_620e07845e3f5.jpeg", "background": "#CD853F", "width": 669, "height": 1024, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 92, "color_id": 115, "percents": 13, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 93, "color_id": 44, "percents": 12, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 94, "color_id": 13, "percents": 9, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 95, "color_id": 138, "percents": 8, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 96, "color_id": 124, "percents": 8, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 97, "color_id": 126, "percents": 7, "R": 244, "G": 164, "B": 96, "hex": "#F4A460"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 98, "color_id": 34, "percents": 7, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 99, "color_id": 135, "percents": 5, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 100, "color_id": 114, "percents": 5, "R": 255, "G": 218, "B": 185, "hex": "#FFDAB9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 101, "color_id": 47, "percents": 5, "R": 255, "G": 250, "B": 240, "hex": "#FFFAF0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 225037}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 41, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 42, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 43, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 638, "parameter_id": 1, "parameter_option_id": 1, "variant_id": 394}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 639, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 423}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 640, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 424}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 641, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 425}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 642, "parameter_id": 2, "parameter_option_id": 13, "variant_id": 426}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 643, "parameter_id": 2, "parameter_option_id": 14, "variant_id": 427}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 644, "parameter_id": 2, "parameter_option_id": 15, "variant_id": 428}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 645, "parameter_id": 2, "parameter_option_id": 16, "variant_id": 429}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 646, "parameter_id": 1, "parameter_option_id": 2, "variant_id": 401}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 647, "parameter_id": 1, "parameter_option_id": 3, "variant_id": 408}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 648, "parameter_id": 1, "parameter_option_id": 10, "variant_id": 415}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 649, "parameter_id": 1, "parameter_option_id": 11, "variant_id": 422}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 650, "parameter_id": 1, "parameter_option_id": 12, "variant_id": 429}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 16, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-5", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Размер", "p2": "Цвят", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/характеристики/повече информация за материал чрез текст, стандартни видеа и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 1, "image_id": 19, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 14490, "price_to": 14490, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 431, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 431, "v1": "S", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 1, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 432, "v1": "S", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 1, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 433, "v1": "S", "v2": "<PERSON>ин", "v3": null, "v1_id": 1, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 434, "v1": "S", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 1, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 435, "v1": "S", "v2": "Тъмно сив", "v3": null, "v1_id": 1, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 436, "v1": "S", "v2": "Светло сив", "v3": null, "v1_id": 1, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 437, "v1": "S", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 1, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 438, "v1": "M", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 2, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 439, "v1": "M", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 2, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 440, "v1": "M", "v2": "<PERSON>ин", "v3": null, "v1_id": 2, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 441, "v1": "M", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 2, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 442, "v1": "M", "v2": "Тъмно сив", "v3": null, "v1_id": 2, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 443, "v1": "M", "v2": "Светло сив", "v3": null, "v1_id": 2, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 444, "v1": "M", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 2, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 445, "v1": "L", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 3, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 446, "v1": "L", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 3, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 447, "v1": "L", "v2": "<PERSON>ин", "v3": null, "v1_id": 3, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 448, "v1": "L", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 3, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 449, "v1": "L", "v2": "Тъмно сив", "v3": null, "v1_id": 3, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 450, "v1": "L", "v2": "Светло сив", "v3": null, "v1_id": 3, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 451, "v1": "L", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 3, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 452, "v1": "XL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 10, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 453, "v1": "XL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 10, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 454, "v1": "XL", "v2": "<PERSON>ин", "v3": null, "v1_id": 10, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 455, "v1": "XL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 10, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 456, "v1": "XL", "v2": "Тъмно сив", "v3": null, "v1_id": 10, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 457, "v1": "XL", "v2": "Светло сив", "v3": null, "v1_id": 10, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 458, "v1": "XL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 10, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 459, "v1": "XXL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 11, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 460, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 11, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 461, "v1": "XXL", "v2": "<PERSON>ин", "v3": null, "v1_id": 11, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 462, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 11, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 463, "v1": "XXL", "v2": "Тъмно сив", "v3": null, "v1_id": 11, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 464, "v1": "XXL", "v2": "Светло сив", "v3": null, "v1_id": 11, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 465, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 11, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 466, "v1": "XXXL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 12, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 467, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 12, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 468, "v1": "XXXL", "v2": "<PERSON>ин", "v3": null, "v1_id": 12, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 469, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 12, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 470, "v1": "XXXL", "v2": "Тъмно сив", "v3": null, "v1_id": 12, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 471, "v1": "XXXL", "v2": "Светло сив", "v3": null, "v1_id": 12, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 472, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 12, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 14490, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 19, "name": "Секси бондидж комплект Echo-Copy", "parent_id": 16, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "seksi-bondidz-komplekt-echo-copy-image_620e07f216e85.jpeg", "background": "#696969", "width": 683, "height": 1024, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 102, "color_id": 44, "percents": 13, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 103, "color_id": 115, "percents": 9, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 104, "color_id": 34, "percents": 9, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 105, "color_id": 126, "percents": 7, "R": 244, "G": 164, "B": 96, "hex": "#F4A460"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 106, "color_id": 129, "percents": 7, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 107, "color_id": 13, "percents": 6, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 108, "color_id": 114, "percents": 5, "R": 255, "G": 218, "B": 185, "hex": "#FFDAB9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 109, "color_id": 138, "percents": 5, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 281828}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 20, "name": "Секси бондидж комплект Echo-Copy", "parent_id": 16, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "seksi-bondidz-komplekt-echo-copy-image_620e07f3e3f74.jpeg", "background": "#CD853F", "width": 669, "height": 1024, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 110, "color_id": 115, "percents": 13, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 111, "color_id": 44, "percents": 12, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 112, "color_id": 129, "percents": 10, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 113, "color_id": 47, "percents": 8, "R": 255, "G": 250, "B": 240, "hex": "#FFFAF0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 114, "color_id": 126, "percents": 8, "R": 244, "G": 164, "B": 96, "hex": "#F4A460"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 115, "color_id": 34, "percents": 8, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 116, "color_id": 124, "percents": 7, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 117, "color_id": 13, "percents": 6, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 267509}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 44, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 45, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 46, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 625, "parameter_id": 1, "parameter_option_id": 1, "variant_id": 437}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 626, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 466}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 627, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 467}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 628, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 468}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 629, "parameter_id": 2, "parameter_option_id": 13, "variant_id": 469}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 630, "parameter_id": 2, "parameter_option_id": 14, "variant_id": 470}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 631, "parameter_id": 2, "parameter_option_id": 15, "variant_id": 471}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 632, "parameter_id": 2, "parameter_option_id": 16, "variant_id": 472}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 633, "parameter_id": 1, "parameter_option_id": 2, "variant_id": 444}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 634, "parameter_id": 1, "parameter_option_id": 3, "variant_id": 451}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 635, "parameter_id": 1, "parameter_option_id": 10, "variant_id": 458}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 636, "parameter_id": 1, "parameter_option_id": 11, "variant_id": 465}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 637, "parameter_id": 1, "parameter_option_id": 12, "variant_id": 472}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 17, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-4", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Размер", "p2": "Цвят", "p3": null, "p1_id": 1, "p2_id": 2, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/характеристики/повече информация за материал чрез текст, стандартни видеа и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 1, "image_id": 21, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 9890, "price_to": 9890, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 474, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 474, "v1": "S", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 1, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 475, "v1": "S", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 1, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 476, "v1": "S", "v2": "<PERSON>ин", "v3": null, "v1_id": 1, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 477, "v1": "S", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 1, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 478, "v1": "S", "v2": "Тъмно сив", "v3": null, "v1_id": 1, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 479, "v1": "S", "v2": "Светло сив", "v3": null, "v1_id": 1, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 480, "v1": "S", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 1, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 481, "v1": "M", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 2, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 482, "v1": "M", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 2, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 483, "v1": "M", "v2": "<PERSON>ин", "v3": null, "v1_id": 2, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 484, "v1": "M", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 2, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 485, "v1": "M", "v2": "Тъмно сив", "v3": null, "v1_id": 2, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 486, "v1": "M", "v2": "Светло сив", "v3": null, "v1_id": 2, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 487, "v1": "M", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 2, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 488, "v1": "L", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 3, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 489, "v1": "L", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 3, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 490, "v1": "L", "v2": "<PERSON>ин", "v3": null, "v1_id": 3, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 491, "v1": "L", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 3, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 492, "v1": "L", "v2": "Тъмно сив", "v3": null, "v1_id": 3, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 493, "v1": "L", "v2": "Светло сив", "v3": null, "v1_id": 3, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 494, "v1": "L", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 3, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 495, "v1": "XL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 10, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 496, "v1": "XL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 10, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 497, "v1": "XL", "v2": "<PERSON>ин", "v3": null, "v1_id": 10, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 498, "v1": "XL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 10, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 499, "v1": "XL", "v2": "Тъмно сив", "v3": null, "v1_id": 10, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 500, "v1": "XL", "v2": "Светло сив", "v3": null, "v1_id": 10, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 501, "v1": "XL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 10, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 502, "v1": "XXL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 11, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 503, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 11, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 504, "v1": "XXL", "v2": "<PERSON>ин", "v3": null, "v1_id": 11, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 505, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 11, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 506, "v1": "XXL", "v2": "Тъмно сив", "v3": null, "v1_id": 11, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 507, "v1": "XXL", "v2": "Светло сив", "v3": null, "v1_id": 11, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 508, "v1": "XXL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 11, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 509, "v1": "XXXL", "v2": "Чер<PERSON><PERSON>н", "v3": null, "v1_id": 12, "v2_id": 4, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 510, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON><PERSON>н", "v3": null, "v1_id": 12, "v2_id": 5, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 511, "v1": "XXXL", "v2": "<PERSON>ин", "v3": null, "v1_id": 12, "v2_id": 6, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 512, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v3": null, "v1_id": 12, "v2_id": 13, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 513, "v1": "XXXL", "v2": "Тъмно сив", "v3": null, "v1_id": 12, "v2_id": 14, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 514, "v1": "XXXL", "v2": "Светло сив", "v3": null, "v1_id": 12, "v2_id": 15, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 515, "v1": "XXXL", "v2": "<PERSON><PERSON><PERSON>", "v3": null, "v1_id": 12, "v2_id": 16, "v3_id": null, "quantity": null, "sku": "ER5684E", "barcode": "", "price": 9890, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 21, "name": "Секси бельо с жартиери и прашки-Copy", "parent_id": 17, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "seksi-belo-s-zartieri-i-praski-copy-image_620e0855dfaf2.jpeg", "background": "#696969", "width": 683, "height": 1024, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 118, "color_id": 44, "percents": 15, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 119, "color_id": 115, "percents": 8, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 120, "color_id": 138, "percents": 8, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 121, "color_id": 126, "percents": 7, "R": 244, "G": 164, "B": 96, "hex": "#F4A460"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 122, "color_id": 114, "percents": 7, "R": 255, "G": 218, "B": 185, "hex": "#FFDAB9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 123, "color_id": 124, "percents": 6, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 124, "color_id": 34, "percents": 6, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 125, "color_id": 13, "percents": 6, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 126, "color_id": 129, "percents": 6, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 336654}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 22, "name": "Секси бельо с жартиери и прашки-Copy", "parent_id": 17, "sort_order": 2, "active": "yes", "max_thumb_size": 1280, "image_id": "seksi-belo-s-zartieri-i-praski-copy-image_620e085b6bc31.jpeg", "background": "#CD853F", "width": 669, "height": 1024, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 127, "color_id": 115, "percents": 12, "R": 205, "G": 133, "B": 63, "hex": "#CD853F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 128, "color_id": 126, "percents": 9, "R": 244, "G": 164, "B": 96, "hex": "#F4A460"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 129, "color_id": 44, "percents": 9, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 130, "color_id": 34, "percents": 8, "R": 233, "G": 150, "B": 122, "hex": "#E9967A"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 131, "color_id": 138, "percents": 7, "R": 210, "G": 180, "B": 140, "hex": "#D2B48C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 132, "color_id": 135, "percents": 6, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 133, "color_id": 129, "percents": 6, "R": 160, "G": 82, "B": 45, "hex": "#A0522D"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 134, "color_id": 13, "percents": 6, "R": 222, "G": 184, "B": 135, "hex": "#DEB887"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 135, "color_id": 47, "percents": 5, "R": 255, "G": 250, "B": 240, "hex": "#FFFAF0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 270927}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 47, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 48, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 49, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 612, "parameter_id": 1, "parameter_option_id": 1, "variant_id": 480}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 613, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 509}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 614, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 510}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 615, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 511}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 616, "parameter_id": 2, "parameter_option_id": 13, "variant_id": 512}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 617, "parameter_id": 2, "parameter_option_id": 14, "variant_id": 513}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 618, "parameter_id": 2, "parameter_option_id": 15, "variant_id": 514}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 619, "parameter_id": 2, "parameter_option_id": 16, "variant_id": 515}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 620, "parameter_id": 1, "parameter_option_id": 2, "variant_id": 487}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 621, "parameter_id": 1, "parameter_option_id": 3, "variant_id": 494}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 622, "parameter_id": 1, "parameter_option_id": 10, "variant_id": 501}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 623, "parameter_id": 1, "parameter_option_id": 11, "variant_id": 508}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 624, "parameter_id": 1, "parameter_option_id": 12, "variant_id": 515}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 18, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-3", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/характеристики/повече информация за материал чрез текст, стандартни видеа и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 9, "vendor_id": 1, "image_id": 23, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 8920, "price_to": 8920, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 559, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 559, "v1": "Чер<PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 8920, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 560, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 8920, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 561, "v1": "<PERSON>ин", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 8920, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 562, "v1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v2": null, "v3": null, "v1_id": 13, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 8920, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 563, "v1": "Тъмно сив", "v2": null, "v3": null, "v1_id": 14, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 8920, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 564, "v1": "Светло сив", "v2": null, "v3": null, "v1_id": 15, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 8920, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 565, "v1": "<PERSON><PERSON><PERSON>", "v2": null, "v3": null, "v1_id": 16, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 8920, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 23, "name": "Секси бельо с жартиери Gina-Copy", "parent_id": 18, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "seksi-belo-s-zartieri-gina-copy-image_620e09767b75f.jpeg", "background": "#483D8B", "width": 779, "height": 779, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 136, "color_id": 36, "percents": 40, "R": 72, "G": 61, "B": 139, "hex": "#483D8B"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 137, "color_id": 121, "percents": 13, "R": 102, "G": 51, "B": 153, "hex": "#663399"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 138, "color_id": 91, "percents": 12, "R": 147, "G": 112, "B": 216, "hex": "#9370D8"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 139, "color_id": 132, "percents": 7, "R": 106, "G": 90, "B": 205, "hex": "#6A5ACD"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 140, "color_id": 51, "percents": 6, "R": 248, "G": 248, "B": 255, "hex": "#F8F8FF"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 141, "color_id": 135, "percents": 6, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 60285}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 24, "name": "Секси бельо с жартиери Gina-Copy", "parent_id": 18, "sort_order": 2, "active": "yes", "max_thumb_size": 800, "image_id": "seksi-belo-s-zartieri-gina-copy-image_620e0977dcefc.png", "background": "#556B2F", "width": 688, "height": 688, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 142, "color_id": 30, "percents": 81, "R": 85, "G": 107, "B": 47, "hex": "#556B2F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 143, "color_id": 61, "percents": 16, "R": 75, "G": 0, "B": 130, "hex": "#4B0082"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 85606}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 50, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 51, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 52, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 605, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 559}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 606, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 560}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 607, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 561}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 608, "parameter_id": 2, "parameter_option_id": 13, "variant_id": 562}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 609, "parameter_id": 2, "parameter_option_id": 14, "variant_id": 563}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 610, "parameter_id": 2, "parameter_option_id": 15, "variant_id": 564}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 611, "parameter_id": 2, "parameter_option_id": 16, "variant_id": 565}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 19, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-2", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/характеристики/повече информация за материал чрез текст, стандартни видеа и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 9, "vendor_id": 1, "image_id": 28, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 7640, "price_to": 7640, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 567, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 567, "v1": "Чер<PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 7640, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 568, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 7640, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 569, "v1": "<PERSON>ин", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 7640, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 570, "v1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v2": null, "v3": null, "v1_id": 13, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 7640, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 571, "v1": "Тъмно сив", "v2": null, "v3": null, "v1_id": 14, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 7640, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 572, "v1": "Светло сив", "v2": null, "v3": null, "v1_id": 15, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 7640, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 573, "v1": "<PERSON><PERSON><PERSON>", "v2": null, "v3": null, "v1_id": 16, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 7640, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 28, "name": "Вибратор за двойки Partner-Copy", "parent_id": 19, "sort_order": 1, "active": "yes", "max_thumb_size": 600, "image_id": "vibrator-za-dvojki-partner-copy-image_620e0a195b0fe.png", "background": "#FF1493", "width": 600, "height": 600, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 147, "color_id": 41, "percents": 68, "R": 255, "G": 20, "B": 147, "hex": "#FF1493"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 148, "color_id": 96, "percents": 14, "R": 199, "G": 21, "B": 133, "hex": "#C71585"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 149, "color_id": 59, "percents": 7, "R": 255, "G": 105, "B": 180, "hex": "#FF69B4"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 69764}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 27, "name": "Вибратор за двойки Partner-Copy", "parent_id": 19, "sort_order": 2, "active": "yes", "max_thumb_size": 600, "image_id": "vibrator-za-dvojki-partner-copy-image_620e0a143ad9c.png", "background": "#FF1493", "width": 600, "height": 600, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 144, "color_id": 41, "percents": 68, "R": 255, "G": 20, "B": 147, "hex": "#FF1493"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 145, "color_id": 96, "percents": 15, "R": 199, "G": 21, "B": 133, "hex": "#C71585"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 146, "color_id": 59, "percents": 6, "R": 255, "G": 105, "B": 180, "hex": "#FF69B4"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 73878}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 53, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 54, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 55, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 598, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 567}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 599, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 568}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 600, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 569}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 601, "parameter_id": 2, "parameter_option_id": 13, "variant_id": 570}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 602, "parameter_id": 2, "parameter_option_id": 14, "variant_id": 571}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 603, "parameter_id": 2, "parameter_option_id": 15, "variant_id": 572}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 604, "parameter_id": 2, "parameter_option_id": 16, "variant_id": 573}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 20, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-1", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/характеристики/повече информация за материал чрез текст, стандартни видеа и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 9, "vendor_id": 1, "image_id": 29, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 12200, "price_to": 12200, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 575, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 575, "v1": "Чер<PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 12200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 576, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 12200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 577, "v1": "<PERSON>ин", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 12200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 578, "v1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v2": null, "v3": null, "v1_id": 13, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 12200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 579, "v1": "Тъмно сив", "v2": null, "v3": null, "v1_id": 14, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 12200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 580, "v1": "Светло сив", "v2": null, "v3": null, "v1_id": 15, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 12200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 581, "v1": "<PERSON><PERSON><PERSON>", "v2": null, "v3": null, "v1_id": 16, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 12200, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 29, "name": "Малък вибратор My First Teddy-Copy", "parent_id": 20, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "malak-vibrator-my-first-teddy-copy-image_620e0a598cefb.webp", "background": null, "width": 750, "height": 750, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 12640}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 30, "name": "Малък вибратор My First Teddy-Copy", "parent_id": 20, "sort_order": 2, "active": "yes", "max_thumb_size": 1920, "image_id": "malak-vibrator-my-first-teddy-copy-image_620e0a59d744e.webp", "background": null, "width": 600, "height": 600, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 11926}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 56, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 57, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 58, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 591, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 575}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 592, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 576}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 593, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 577}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 594, "parameter_id": 2, "parameter_option_id": 13, "variant_id": 578}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 595, "parameter_id": 2, "parameter_option_id": 14, "variant_id": 579}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 596, "parameter_id": 2, "parameter_option_id": 15, "variant_id": 580}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 597, "parameter_id": 2, "parameter_option_id": 16, "variant_id": 581}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 21, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": "Цвят", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структура, но да предоставя повече информация. Тук може да се добавят и размер/характеристики/повече информация за материал чрез текст, стандартни видеа и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 9, "vendor_id": 1, "image_id": 32, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 6290, "price_to": 6290, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 583, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 583, "v1": "Чер<PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 6290, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 584, "v1": "<PERSON><PERSON><PERSON><PERSON>н", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 6290, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 585, "v1": "<PERSON>ин", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 6290, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 586, "v1": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v2": null, "v3": null, "v1_id": 13, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 6290, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 587, "v1": "Тъмно сив", "v2": null, "v3": null, "v1_id": 14, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 6290, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 588, "v1": "Светло сив", "v2": null, "v3": null, "v1_id": 15, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 6290, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 589, "v1": "<PERSON><PERSON><PERSON>", "v2": null, "v3": null, "v1_id": 16, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 6290, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 32, "name": "Вибратор от медицински силикон Gaia-Copy", "parent_id": 21, "sort_order": 1, "active": "yes", "max_thumb_size": 1920, "image_id": "vibrator-ot-medicinski-silikon-gaia-copy-image_620e0a9d2e14c.webp", "background": null, "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 15612}, "relations": []}]}}, {"model": "App\\Models\\Product\\Image", "attributes": {"id": 31, "name": "Вибратор от медицински силикон Gaia-Copy", "parent_id": 21, "sort_order": 2, "active": "yes", "max_thumb_size": 1920, "image_id": "vibrator-ot-medicinski-silikon-gaia-copy-image_620e0a9cd4fb5.webp", "background": null, "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 20394}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 59, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 60, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 61, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 584, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 583}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 585, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 584}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 586, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 585}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 587, "parameter_id": 2, "parameter_option_id": 13, "variant_id": 586}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 588, "parameter_id": 2, "parameter_option_id": 14, "variant_id": 587}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 589, "parameter_id": 2, "parameter_option_id": 15, "variant_id": 588}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 590, "parameter_id": 2, "parameter_option_id": 16, "variant_id": 589}, "relations": []}]}}]