[{"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 1, "mapping": "main", "name": "Главно меню", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 11, "parent_id": 19, "order": 1, "type": "category", "link_type": "category", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "<i class=\"fas fa-apple-alt\"></i>&nbsp; Φρούτα & Λαχανικά"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 12, "parent_id": 19, "order": 2, "type": "category", "link_type": "category", "link_id": 2, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "<i class=\"fas fa-cheese-swiss\"></i>&nbsp; Γαλακτοκομικά προϊόντα & Αυγά"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 13, "parent_id": 19, "order": 3, "type": "category", "link_type": "category", "link_id": 3, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "<i class=\"fas fa-sausage\"></i>&nbsp; Κρέας & Λουκάνικα"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 14, "parent_id": 19, "order": 4, "type": "category", "link_type": "category", "link_id": 4, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "<i class=\"fas fa-croissant\"></i>&nbsp; Ψωμί & Αρτοσκευάσματα"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 15, "parent_id": 19, "order": 5, "type": "category", "link_type": "category", "link_id": 5, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "<i class=\"fas fa-glass-cheers\"></i>&nbsp; Ποτά"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 19, "parent_id": null, "order": 2, "type": "group", "link_type": null, "link_id": null, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Κατηγορίες"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 20, "parent_id": null, "order": 3, "type": "selection", "link_type": "selection", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Προσφορές"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 21, "parent_id": null, "order": 4, "type": "selection", "link_type": "selection", "link_id": 2, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Νέα Προϊόντα"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 36, "parent_id": null, "order": 5, "type": "section", "link_type": null, "link_id": null, "route": "site.vendors", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Μάρ<PERSON><PERSON>ς"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 37, "parent_id": null, "order": 6, "type": "page", "link_type": "page", "link_id": 3, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Αποστολή & Παράδοση"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 38, "parent_id": null, "order": 1, "type": "selection", "link_type": "selection", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Προσφορές"}, "relations": []}]}}, {"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 2, "mapping": "footer", "name": "Долно меню", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 23, "parent_id": null, "order": 1, "type": "group", "link_type": null, "link_id": null, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Σχετικά με εμάς"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 24, "parent_id": null, "order": 2, "type": "group", "link_type": null, "link_id": null, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Όροι"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 25, "parent_id": 24, "order": 1, "type": "page", "link_type": "page", "link_id": 3, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Πληρωμή & Παράδοση"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 26, "parent_id": 24, "order": 2, "type": "page", "link_type": "page", "link_id": 4, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "FAQ"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 27, "parent_id": 23, "order": 1, "type": "page", "link_type": "page", "link_id": 5, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Καριέρα"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 28, "parent_id": 23, "order": 2, "type": "section", "link_type": null, "link_id": null, "route": "site.vendors", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Μάρ<PERSON><PERSON>ς"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 29, "parent_id": null, "order": 3, "type": "group", "link_type": null, "link_id": null, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Πληροφορίες"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 30, "parent_id": 29, "order": 3, "type": "page", "link_type": "page", "link_id": 6, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Προϊόντα παραγωγής"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 31, "parent_id": 29, "order": 2, "type": "selection", "link_type": "selection", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Προσφορές"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 32, "parent_id": 24, "order": 3, "type": "page", "link_type": "page", "link_id": 9, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Συνεταιρισμοί"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 33, "parent_id": 23, "order": 3, "type": "page", "link_type": "page", "link_id": 8, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Ωράριο λειτουργίας"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 34, "parent_id": 29, "order": 1, "type": "page", "link_type": "page", "link_id": 10, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Οφέλη loyalty"}, "relations": []}]}}, {"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 3, "mapping": "navigationLinks", "name": "Навигационни връзки", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 42, "parent_id": null, "order": 1, "type": null, "link_type": null, "link_id": null, "route": "blog.list", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Blog"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 43, "parent_id": null, "order": 1, "type": null, "link_type": null, "link_id": null, "route": "contacts", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Επικοινωνία"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 44, "parent_id": null, "order": 2, "type": null, "link_type": null, "link_id": null, "route": "site.vendors", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Μάρ<PERSON><PERSON>ς"}, "relations": []}]}}, {"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 4, "mapping": "navigationLinksPage", "name": "Странично меню - Страници", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 35, "parent_id": null, "order": 1, "type": null, "link_type": null, "link_id": null, "route": "blog.list", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Блог"}, "relations": []}]}}]