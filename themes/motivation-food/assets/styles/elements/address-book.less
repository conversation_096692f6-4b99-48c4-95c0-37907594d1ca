/*=============================================================================*\
    ADDRESS BOOK
\*=============================================================================*/

._address-book-list {
    position: relative;
    overflow: hidden;
    margin-bottom: 30px;

    &.hidden {
        display: block !important;
    }

    ul {
        display: flex;
        flex-wrap: wrap;
        align-content: stretch;
        list-style-type: none;
        margin: -15px;
        font-size: 0;
    }

    li {
        display: inline-block;
        border: 2px solid;
        border-color: @color-main-borders; /* theme */
        min-height: 200px;
        width: calc(50% ~'-' 30px);
        margin: 15px;
        font-size: @font-size-main; /* theme */
        font-weight: 300;
        vertical-align: top;
        position: relative;

        &:last-child {
            border: 0;
        }

        &.active {
            background-color: @color-second-background; /* theme */
            border-color: @color-second-highlight; /* theme */

            ._address-book-list-item-data-name {
                color: @color-second-titles; /* theme */
            }

            ._address-book-list-item-edit,
            ._address-book-list-actions ._figure-stack {
                color: @color-second-titles; /* theme */
            }

            ._address-book-list-item-data-item {
                color: @color-second-meta-text; /* theme */

                strong {
                    color: @color-second-text; /* theme */
                }
            }
        }
    }
}

._address-book-list-item {
    position: relative;

    ._radio {
        .radio {
            height: auto;
            width: auto;
            margin: 0;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;

            > span {
                background-color: transparent;
                border: 0;
                border-radius: 0;
                height: 100%;
                width: 100%;

                &.checked {
                    &:before {
                    	display: none;
                    }
                }
            }
        }
    }

    .help-block-error {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        margin-top: 5px;
    }
}

._address-book-list-item ._radio,
._address-book-list-action-mdefault {
    display: block;
    padding: 21px 30px;
    position: relative;
}

._address-book-list-item-data {
    display: block;
    position: relative;
    z-index: 1;
}

._address-book-list-item-data-name {
    ._h4();
    display: block;
    margin-bottom: 5px;
    color: @color-main-titles; /* theme */;
    font-weight: 900;
}

._address-book-list-item-data-item {
    display: block;
    margin-bottom: 6px;
    color: @color-main-meta-text; /* theme */

    &:last-child {
    	margin-bottom: 0;
    }

    strong {
        font-weight: normal;
        color: @color-main-secondary-text; /* theme */
    }
}

._address-book-list-item-edit,
._address-book-list-actions {
    font-size: @font-size-main; /* theme */
    position: absolute;
    right: 9px;
    top: 4px;
    z-index: 1;
}

._address-book-list-item-edit {
    color: @color-main-titles; /* theme */

    @media @hover {
        &:hover {
            color: @color-main-highlight; /* theme */
        }
    }
}

._address-book-list-actions {
    > ._figure-stack {
        color: @color-main-titles; /* theme */

        @media @hover {
            &:hover {
                color: @color-main-highlight; /* theme */
            }
        }

        & + ._figure-stack {
            margin-left: 5px;
        }
    }
}

._address-book-controls {
    ._checkbox {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-width: 2px;
        border-style: dashed;
        border-color: @color-main-borders; /* theme */
        padding: 0;
        position: relative;
        color: @color-main-meta-text; /* theme */
        text-align: center;
        text-transform: uppercase;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        user-select: none;

        &:before {
            font-family: @font-awesome;
            content: "\f055";
            font-size: 60px;
        }
		
		input,
        .checker {
            display: none;
        }

        &.active {
            border-style: solid;
            border-color: @color-main-highlight; /* theme */
            color: @color-main-highlight; /* theme */

            &:before {
                content: "\f057";
            }
        }
    }
}

._address-book-add {
    ._button-secondary {
        margin-right: 0;
    }
}

.region-wrapper {
	margin-bottom: -@forms-spacing;
}

.region-holder {
	margin-bottom: @forms-spacing;
}