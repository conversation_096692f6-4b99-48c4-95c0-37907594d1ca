/*=============================================================================*\
    FORMS
\*=============================================================================*/

@forms-font-size: 12px;
@forms-spacing: 20px;
@forms-border-width: 2px;
@forms-height: 50px;

._form-row {
	.flexy-row(16px);
	margin-bottom: @forms-spacing;

	._form-col {
		.flexy-col();
		vertical-align: top;

        &._form-col-bottom {
            vertical-align: bottom;
        }
        ._help-text {
            margin-top: 5px;
        }
	}
}

._form-row-secondary {
    .flexy-row(16px);
	margin-bottom: @forms-spacing;

	._form-col-secondary {
	    .flexy-col();
	    vertical-align: top;

	    &._form-col-bottom {
	        vertical-align: bottom;
	    }

	    ._help-text {
	        margin-top: 5px;
	    }
	}
}

._form-row-group {
	margin-bottom: @forms-spacing;
}

._form-actions {
    .clearfix();
    text-align: right;

	._button {
		margin-right: 6px;

		&:last-child {
			margin-right: 0;
		}
	}

    &._form-actions-reverse {
        text-align: left;

        ._button {
            float: left;
            margin-left: 0;
            margin-right: 10px;
            &:last-child {
                margin-right: 0;
            }
        }
    }
}

._form-row-group-secondary {
    & + ._form-row-secondary {
        margin-top: @forms-spacing;
    }
}

/*  HELP TEXT
-------------------------------------------------------------------------------*/

._help-text {
    display: block;
    color: @color-main-meta-text; /* theme */
    font-size: calc(@font-size-main ~'-' 2px); /* theme */
}

/*  ERRORS
-------------------------------------------------------------------------------*/

.help-block-error {
	display: inline-block;
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	color: @color-error-text; /* theme */
}

/*  FIELDS
-------------------------------------------------------------------------------*/

/*  Input */
._field {
	display: block;
	background-color: @color-forms-fields-background; /* theme */
	border: @forms-border-width solid;
	border-color: @color-forms-fields-borders; /* theme */
	font-size: @forms-font-size;
	color: @color-forms-fields-text; /* theme */
	padding: 0 15px;
	height: @forms-height;
	width: 100%;
	transition: border .2s;

	&:focus {
		border-color: @color-main-highlight; /* theme */
		outline: 0;
	}

	.placeholder(@color-forms-fields-placeholder);
}

/*  Textarea */
._textarea {
	._field();
	padding: 15px;
	height: 174px;
}

textarea {
	&._field {
		height: auto;
		padding: 15px;
	}
}

/*  Stack - Field + Button */
._field-stack {
	display: inline-table;

	& ._field-stack-addon {
		display: table-cell;
		vertical-align: top;
		width: 1%;
		white-space: nowrap;
	}
}

/*  Stack - Field + Icon */
._field-icon {
	.field-icon(@forms-height, @color-main-text, right);
}

/*  SELECT
-------------------------------------------------------------------------------*/

@select-arrow-width: 36px;

select {
	width: 100%;
	height: @forms-height;
	padding: 0 15px;
	border-color: @color-forms-fields-borders; /* theme */
}

._select {
	display: block;

	&.select2-dropdown-open {
		.select2-choice,
		.select2-choices {
			background: @color-forms-fields-background; /* theme */
			border-color: @color-forms-fields-borders; /* theme */
			border-radius: 0;
		}
	}

	.select2-choice {
		background: transparent;
		border: @forms-border-width solid @color-forms-fields-borders; /* theme */
		border-radius: 0;
		box-shadow: none;
		font-size: @forms-font-size;
		background-color: @color-forms-fields-background; /* theme */
		padding: 0;
		height: @forms-height;
		line-height: @forms-height - @forms-border-width*2;

		.select2-chosen {
			margin: 0;
			padding-left: 15px;
			padding-right: 45px;
			color: @color-forms-fields-text; /* theme */
		}

		.select2-arrow {
			background: transparent;
			border: none;
			border-radius: 0;
			width: @select-arrow-width;

			&:before {
				content: '';
				border-left: @forms-border-width solid;
				border-color: @color-forms-fields-borders; /* theme */
				position: absolute;
				top: 11px;
				bottom: 11px;
				left: 0;
			}

			b {
				background: transparent;

				&:before {
					content: '\f107';
					font-family: @font-awesome;
					font-size: calc(@font-size-main ~'+' 4px); /* theme */
					font-weight: normal;
					color: @color-forms-fields-text; /* theme */
					.centerer(true, true);
				}
			}
		}
	}

	&.select2-allowclear {
		.select2-choice {
			.select2-chosen {
				margin: 0;
				padding-right: 65px;
			}

			abbr {
				._remove();
				.centerer(false, true);
				background: transparent;
				height: auto;
				width: auto;
				color: @color-forms-fields-icons; /* theme */
				line-height: 1;
				right: 45px;
			}
		}
	}
}

.select2-drop {
	background-color: @color-forms-fields-background; /* theme */
	border-width: @forms-border-width;
	border-top-width: 0;
	border-style: solid;
	border-color: @color-forms-fields-borders; /* theme */
	border-radius: 0;
	box-shadow: none;
	line-height: @line-height-medium;
	overflow: hidden;

	&.select2-drop-above {
		box-shadow: none;
		border-radius: 0;
		margin: 0;

		&.select2-drop-active {
			border-color: @color-forms-fields-borders; /* theme */
		}
	}
}

.select2-search {
	padding: 15px;

	input {
		background-color: @color-forms-fields-background; /* theme */
		border-color: @color-forms-fields-borders; /* theme */
		color: @color-forms-fields-text; /* theme */
	}
}
	
.select2-results {
	padding: 0;
	margin: 0;
	max-height: 204px;

	.select2-result {
		color: @color-forms-fields-text; /* theme */

		&.select2-highlighted {
			background-color: @color-button-background; /* theme */
			color: @color-button-text; /* theme */
		}
	}

	.select2-result-label {
		font-size: @forms-font-size;
		padding: 7px 15px;
	}

	.select2-no-results,
	.select2-searching {
		background-color: transparent;
		padding: 5px 15px;
		font-size: calc(@font-size-main ~'-' 2px); /* theme */
	}
}

.select2-suboption {
	.select2-result-label {
		padding-left: 26px;
		position: relative;

		&:before {
			display: inline-block;
			vertical-align: middle;
			content:"\f054";
			font-family: @font-awesome;
			margin-right: 3px;
			font-size: calc(@font-size-main ~'-' 4px); /* theme */
			position: absolute;
			top: 9px;
			left: 15px;

			& when (@rtl) {
				content:"\f053";
			}
		}
	}
}

.select2-suboption-2 {
	.select2-result-label {
		padding-left: 37px;

		&:before {
			left: 26px;
		}
	}
}

.select2-suboption-3 {
	.select2-result-label {
		padding-left: 48px;

		&:before {
			left: 37px;
		}
	}
}

/*  INPUT GROUP - BOOTSTRAP
-------------------------------------------------------------------------------*/

.input-group {
	display: inline-table;
	
	.input-group-btn {
		.btn {
			border-radius: 0;
			border: none;
			margin: 0;
			width: @forms-height;
			height: @forms-height;
			padding: 0;
			background-color: @color-button-background; /* theme */
			color: @color-button-text; /* theme */
			font-size: 0;
			position: relative;
			&:before {
				font-size: calc(@font-size-main ~'-' 2px); /* theme */
				font-family: @font-glyphicons;
				.centerer(true, true);
			}
			&.bootstrap-touchspin-down {
				&:before {
					content: '\2212';
				}
			}
			&.bootstrap-touchspin-up {
				&:before {
					content: '\2b';
				}
			}
		}
	}
	
	._field {
		box-shadow: none;
		text-align: center;
	}
}

body {
	.input-group {
		display: block;
		max-width: 165px;

		&:before {
			content: '';
			border-left: @forms-border-width solid;
			border-color: @color-forms-fields-borders; /* theme */
			position: absolute;
			top: 11px;
			bottom: 11px;
			right: @select-arrow-width - @forms-border-width;
			z-index: 2;
		}

		._field {
			float: none;
			border-radius: 0 !important;
			text-align: left;
			z-index: 1;

			&:focus {
				z-index: 1;
			}
		}

		.input-group-btn {
			position: static;

			.btn {
				background: transparent; /* theme */
				width: @select-arrow-width;
				height: @forms-height/2 - @forms-border-width;
				color: @color-forms-fields-text; /* theme */
				position: absolute;
				right: 0;
				z-index: 2;

				&:focus,
				&:active {
					box-shadow: none;
				}

				&:before {
					font-family: @font-awesome;
					font-size: calc(@font-size-main ~'+' 4px); /* theme */
				}

				&.bootstrap-touchspin-up {
					top: @forms-border-width;

					&:before {
						content:"\f106";
						margin-top: 3px;
					}
				}

				&.bootstrap-touchspin-down {
					bottom: @forms-border-width;

					&:before {
						content:"\f107";
						margin-top: -3px;
					}
				}
			}
		}
	}
}

/*  CHECKBOX
-------------------------------------------------------------------------------*/

@checkbox-size: 22px;
@checkbox-border-width: 2px;
@checkbox-offset: @checkbox-size + 10px;
@checkbox-label-line-height: @line-height-low;

._checkbox {
	min-height: @checkbox-size;
	padding-left: @checkbox-offset;
	padding-top: 4px;
	position: relative;
	font-size: calc(@font-size-main ~'-' 1px); /* theme */
	line-height: @checkbox-label-line-height;

	input {
		opacity: 0;
		visibility: hidden;
	}

	.checker,
	.checker span,
	.checker input {
		width: @checkbox-size;
		height: @checkbox-size;
		vertical-align: top;
	}

	.checker {
		position: absolute;
		top: 0;
		left: 0;

		span {
			background-color: @color-forms-checkbox-background; /* theme */
			border: @checkbox-border-width solid;
			border-color: @color-forms-checkbox-borders; /* theme */
			color: @color-forms-checkbox-check; /* theme */
			border-radius: 0;
			position: relative;

			&:before {
				display: none;
				content: '\f00c';
				font-family: @font-awesome;
				font-size: 14px;
				.centerer(true, true);
			}

			&.checked {
				&:before {
					display: block;
				}
			}

			span {
				display: none;
			}
		}
	}
}

/*  RADIO
-------------------------------------------------------------------------------*/

@radio-size: 22px;
@radio-dot-size: 8px;
@radio-border-width: 2px;
@radio-offset: @radio-size + 10px;

._radio {
	min-height: @radio-size;
	padding-left: @radio-offset;
	padding-top: 4px;
	position: relative;

	input {
		opacity: 0;
		visibility: hidden;
	}

	.radio,
	.radio span,
	.radio input {
		width: @radio-size;
		height: @radio-size;
		vertical-align: top;
	}

	.radio {
		position: absolute;
		top: 0;
		left: 0;

		span {
			background-color: @color-forms-radio-background; /* theme */
			border: @radio-border-width solid;
			border-radius: 50%;
			border-color: @color-forms-radio-borders; /* theme */
			width: @radio-size;
			height: @radio-size;
			position: relative;

			&:before {
				display: none;
				background-color: @color-forms-radio-check; /* theme */
				content: '';
				border-radius: 50%;
				width: @radio-dot-size;
				height: @radio-dot-size;
				.centerer(true, true);
			}

			&.checked {
				&:before {
					display: block;
				}
			}
		}
	}
}

/*  RANGE SLIDER
-------------------------------------------------------------------------------*/

@range-slider-height: 6px;
@range-slider-handle-size: 20px;

._range-slider {
	background: transparent;
	background-color: @color-forms-range-slider; /* theme */
	border-radius: 0;
	height: @range-slider-height;
	border: none;
	margin-bottom: 5px;

	&.ui-widget-content {
		border: 0;
	}

	[class*='ui-corner-'] {
		border-radius: 0
	}

	.ui-slider-range {
		background-color: @color-forms-range-slider-highlight; /* theme */
	}

	.ui-slider-handle {
		background-color: @color-forms-range-slider-sliders; /* theme */
		border: none;
		border-radius: 0;
		width: @range-slider-handle-size;
		height: @range-slider-handle-size;
		top: -(@range-slider-handle-size - @range-slider-height)/2;
		margin-left: 0;
		outline: 0;

		&:last-child {
			transform: translate(-100%, 0);
		}
	}
}