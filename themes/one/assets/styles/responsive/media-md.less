/*=============================================================================*\
    MEDIUM DEVICES / DESKTOPS
\*=============================================================================*/

@media (max-width: @screen-md-max) {

	._navigation ._main-menu__nav > li {

		&:first-of-type:before {
			left: 10px;
		    width: ~'calc(100% - 10px)';
		}

		&:last-of-type:before {
		    width: ~'calc(100% - 10px)';
		}

		> a {
			margin: 5px 10px;
		}
	}

	._home-banners {

		._banners {
			padding: 30px 0;
			
			._banners-list {
				flex-flow: row wrap;
				height: auto;

				&._banners-count-4 {
					flex-flow: row wrap;

					._banner {
						width: 50%;
						max-width: 50%;
						height: 300px;
					}
				}

				._banner {
					width: 50%;
					max-width: 50%;
					flex: 1 0 50%;
					height: 300px;
				}
			}
		}
	}

	.product-details-js {
		position: static;
	}

	._product {
		._product-info {
			._product-options {
				._product-price {
					width: auto;
				}
			}
		}
	}

	._products-list {
		._product {
			margin-top: 30px;
			width: 50%;
			&:nth-child(-n+4) {
				margin-top: 30px;
			}
			&:nth-child(4n+5) {
				clear: none;
			}
			&:nth-child(-n+2) {
				margin-top: 0;
			}
			&:nth-child(2n+3) {
				clear: left;
			}
		}
	}
	
	.modal-dialog.product-details {
		width: 980px;
	}

	._showcase {
		._showcase-list {
			._showcase-item {
				margin-top: 30px;
				width: 50%;
				&:nth-child(-n+4) {
					margin-top: 30px;
				}
				&:nth-child(4n+5) {
					clear: none;
				}
				&:nth-child(-n+2) {
					margin-top: 0;
				}
				&:nth-child(2n+3) {
					clear: left;
				}
			}
		}
	}
	
	._blog-list-article ._blog-list-article-image {
		height: 180px;
	}

	._products-list-main {
		._products-list {
			._product {
				margin-top: 30px;
				&:nth-child(-n+4) {
					margin-top: 30px;
				}
				&:nth-child(4n+5),
				&:nth-child(3n+4) {
					clear: none;
				}
				&:nth-child(-n+2) {
					margin-top: 0;
				}
				&:nth-child(2n+3) {
					clear: left;
				}
			}
		}
	}

	._single-blog ._section-title h3 {
		//font-size: 1.6rem;
		font-size: @font-size-heading-6; // Responsive
	}

	._products-head__item__title {
		//font-size: 2.5rem;
		font-size: @font-size-heading-3;  // Responsive
	}

    ._product-details-desription {
        max-width: none
    }
}