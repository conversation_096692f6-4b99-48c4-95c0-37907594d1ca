(function($){
	var $doc = $(document);
	var $win = $(window);
	var $headerFixed = $('.js-header-fixed');
	var $headerFixedThree = $('.js-header-fixed._header-variant-three');
	var $toTop = $('.js-to-top');
	var $productFixed = $('.js-product-fixed');
	var isHeaderFixed = false;
	var isProductFixed = false;
	var isToTopFixed = false;

	function headerFixed() {
		if ($win.width() > 1199 && $headerFixed.length && !$productFixed.length) {
			if ($win.scrollTop() == 0) {
				if (isHeaderFixed) {
					$headerFixed.removeClass('open');
					$headerFixed.find('.js-search-form-wrapper').removeClass('open');
					isHeaderFixed = false;
				}
			} else {
				if (!isHeaderFixed) {
					$headerFixed.addClass('open');
					isHeaderFixed = true;
				}
			}
		}
	}

	function productFixed() {
		if ($productFixed.length) {
			var $productTreshold = $('.js-product-fixed-treshold');
			var treshold = $productTreshold.offset().top + $productTreshold.outerHeight();
			var $promobar = $('._promo-bar-container:not(.hidden)');

			if ($win.scrollTop() + $headerFixed.outerHeight() < treshold) {
				if (isProductFixed) {
					$productFixed.removeClass('open');
					isProductFixed = false;
					$promobar.css('top', 0);
				}
			} else {
				if (!isProductFixed) {
					$productFixed.addClass('open');
					isProductFixed = true;
					$promobar.css('top', -$promobar.height());
				}
			}
		}
	}

	function toTopFixed() {
		if ($toTop.length) {
			if ($win.scrollTop() === 0) {
				if (isToTopFixed) {
					$toTop.removeClass('open');
					isToTopFixed = false;
				}
			} else {
				if (!isToTopFixed) {
					$toTop.addClass('open');
					isToTopFixed = true;
				}
			}
		}
	}

	function toTop(){
		$toTop.on('click', function(e){
			e.preventDefault();

			$('html, body').animate({ scrollTop: 0 });
		});
	}

	function navigationOpen() {
		$('.js-navigation-hamburger').on('click', function (e) {
			e.preventDefault();

			$(this).toggleClass('open active');
			$('.js-navigation-hamburger-dropdown').toggleClass('block');
		});
	}

	function searchToggle() {
		$('.js-search-button').on('click', function (e) {
			e.preventDefault();

			$('.js-search-form-wrapper').toggleClass('open');
		});
	}

	function categoriesMobileToggle() {
		$('.js-nav-mobile').on('click', '._filter-category-list-item', function (e) {
			var $this = $(this);

			if ( $this.parent('.item-collapse').length) {
				e.preventDefault();
				$this.parent('.item-collapse').toggleClass('opener')
				$this.siblings('js-filter-category-list-toggle').toggleClass('_collapse-active');
			}
		});
	}

	function navMobileOpen() {
		$('.js-nav-mobile-button, .js-nav-mobile-backdrop').on('click', function (e) {
			e.preventDefault();

			$('.js-nav-mobile').toggleClass('open');
			$('.js-nav-mobile-backdrop').toggleClass('open');
		});
	}

	function sidebarOpen() {
		$('.js-sidebar-toggler').on('click', function(e){
			e.preventDefault();

			var $productsBox = $('.js-products-box');
			var wideClass    = 'col-md-12';
			var narrowClass  = 'col-md-9';
			var openClass;

			if ( $win.width() > 1200 ) {
				if ( $productsBox.hasClass(wideClass) ) {
					$productsBox.removeClass(wideClass).addClass(narrowClass);
					Cookies.set('sidebar', 'open', { expires: 365 });
				} else if ( $productsBox.hasClass(narrowClass) ) {
					$productsBox.removeClass(narrowClass).addClass(wideClass);
					Cookies.set('sidebar', 'closed', { expires: 365 });
				}

				openClass = 'open';
			} else {
				openClass = 'mobile-open';
			}

			$('.js-sidebar, .js-sidebar-buttons').toggleClass(openClass);
		});

		if ($win.width() < 992) {
			$('.js-sidebar-buttons').removeClass('open');
		}
	}

	function listCollapse() {
		$doc.on('click', '.js-nolink:not(.js-collapse-load-more), ._collapse:not(.js-collapse-load-more)', function (e){
			e.preventDefault();

			var $el = $(this);

			if ($el.parents('.js-nolink').length) {
				return
			}

			$el.closest('.item-collapse').toggleClass('opener');

			$el.toggleClass('_collapse-active');
		});
	}

	function faqCollapse() {
		$('.js-faq-list-item-title').on('click', function (e){
			e.preventDefault();

			var $el = $(this);

			$el.closest('.js-faq-list-item').toggleClass('open').siblings().removeClass('open');
		});
	}

	function sidebarToggle(selector) {
		$('.js-sidebar-toggle', selector).on('click', function(e) {
			e.preventDefault();

			var $el = $('.js-sidebar').toggleClass('open'),
				name = $el.data('filter-box'),
				opened = $el.hasClass('open') ? 1 : 0;

			Cookies.set('products.list.filter.' + name, opened, { expires: 7 });
		});
	}

	function sidebarBoxToggle(selector) {
		$('.js-sidebar-box-toggle', selector).on('click', function(e){
			e.preventDefault();

			var $el = $(this).closest('.js-sidebar-box').toggleClass('open'),
				name = $el.data('filter-box'),
				opened = $el.hasClass('open') ? 1 : 0;

			Cookies.set('products.list.filter.' + name, opened, { expires: 7 });
		});
	}

	function sidebarAjaxSuccess() {
		$(document).on('cc.filters.filters.after', function(e, filters) {
			categoryPropertiesToggle(filters);
			sidebarToggle(filters);
			sidebarBoxToggle(filters);

			$('.js-sidebar-box[data-filter-box]', filters).each(function() {
				var $el = $(this),
					name = $el.data('filter-box'),
					opened = Cookies.get('products.list.filter.' + name) > 0;

				$el[opened ? 'addClass' : 'removeClass']('open');
			});
		});

		$('.js-sidebar-ajax .js-sidebar-box[data-filter-box]').each(function() {
			var $el = $(this),
				name = $el.data('filter-box'),
				opened = $el.hasClass('open') ? 1 : 0,
				exists = $.inArray(Cookies.get('products.list.filter.' + name), ['0','1']) > -1,
				opened2 = Cookies.get('products.list.filter.' + name) > 0;

			if(exists) {
				$el[opened2 ? 'addClass' : 'removeClass']('open');
				Cookies.set('products.list.filter.' + name, opened2 ? 1 : 0, { expires: 7 });
			} else {
				Cookies.set('products.list.filter.' + name, opened, { expires: 7 });
			}
		});
	}

	function categoryPropertiesToggle(selector) {
		$('._filter-category-property', selector).each(function(){
			var $el = $(this),
				name = $el.data('filter-box'),
				exists = $.inArray(Cookies.get('products.list.filter.' + name), ['0','1']) > -1,
				opened2 = Cookies.get('products.list.filter.' + name) > 0;

			if(exists) {
				$el[opened2 ? 'addClass' : 'removeClass']('open');
			} else {
				if ($el.find('.checked').length) {
					$el.addClass('open');
				}
			}
			var opened = $el.hasClass('open') ? 1 : 0;
			Cookies.set('products.list.filter.' + name, opened, { expires: 7 });
		});

		$('.js-filter-category-property-toggle').on('click', function(e){
			e.preventDefault();

			var $el = $(this).closest('._filter-category-property').toggleClass('open'),
				name = $el.data('filter-box'),
				opened = $el.hasClass('open') ? 1 : 0;
			Cookies.set('products.list.filter.' + name, opened, { expires: 7 });
		});
	}

	function productDetailsSidebar() {
		var $sidebar = $('.js-product-details-sidebar');
		var headerHeight = $('.js-header-fixed').outerHeight(true) || 0;
		var $sidebar_init = function () {
			if ($.isLg()) {
				$sidebar.theiaStickySidebar({
					additionalMarginTop: headerHeight + 10,
					additionalMarginBottom: 10,
					containerSelector: $sidebar.closest('.js-product-details-container')
				});
			}
		};

		$sidebar_init();
	}

	function handleTabs() {
		var $tabs = $('.js-tabs');

		if ($tabs.length) {
			$tabs.find('.js-tabs-link:first-child').addClass('active');

			$tabs.on('click', '.js-tabs-link', function(e){
				var $this = $(this);
				var $target = $($this.attr('href'));

				$this.addClass('active').siblings().removeClass('active');
				$tabs.find('.js-tab').hide();
				$target.fadeIn();

				e.preventDefault();
			});
		}

	}

	function handleTabsMobile() {
		var $tabsMobile = $('.js-tabs-mobile');

		if ($tabsMobile.length) {
		
			$tabsMobile.on('click', '.js-tabs-link-mobile', function(e){
				var $this = $(this);
				var $target = $($this.next());

				$this.toggleClass('active');
				$target.slideToggle(200);

				e.preventDefault();
			});
		}
	}

	function shareToggle() {
		$doc.on('click', '.js-button-share', function(e){
			e.preventDefault();

			$(this).closest('.js-product-share').toggleClass('open');
		});
	}

	function checkoutAddressBook() {
		$('.page-checkout')
			.on('change', 'input[type="radio"]', function(e){
				var radioName = $(this).attr('name');

				$('input[name="' + radioName + '"]').closest('li').removeClass('active');
				$(this).closest('li').addClass('active');
			})
			.find('._radio').each(function(){
				var $this = $(this);

				if ( $this.find('.checked').length ) {
					$this.closest('li').addClass('active');
				}
			});
	}

	function swiperChangeImage() {
		$('._product-details-gallery-container').find('.swiper-button').on('click', function(e){
			e.preventDefault();

			var $this    = $(this);
			var $current = $this.closest('._product-details-gallery-container').find('.cc-active');

			if ($this.hasClass('swiper-button-prev')) {
				var $prev = $current.prev();

				if ($prev.hasClass('hide')) {
					$prev = $current.prevUntil(':not(.hide)').prev();
				}

				$prev.find('a').click();
			} else {
				var $next = $current.next();

				if ($next.hasClass('hide')) {
					$next = $current.nextUntil(':not(.hide)').next();
				}

				$next.find('a').click();
			}
		});
	}

	function orderDetailsToggle() {
		var isClickable = true;

		$('.js-order-details-button').on('click', function(e){
			e.preventDefault();

			var $this = $(this);
			var $row = $this.closest('tr');

			if ( !$row.hasClass('active') ) {
				$this.addClass('loading');

				if (isClickable) {
					isClickable = false;

					$.ajax({
						url: $this.attr('href'),
						success: function(data){
							var html = data.html;
							var $detailsRow = $('<tr class="_order-details-row"></tr>').insertAfter($row).html(html);
							$row.addClass('active');
							$this.removeClass('loading');
							isClickable = true;
						}
					});
				}
			} else {
				$row.removeClass('active').next('._order-details-row').remove();
			}
		});
	}

	$(document).on("cc.variant.changed", function(e, variant) {
		var $productSticky = $('._product-details-sticky');
		var $statusBtn = $('._product-details-sticky .js-request-status');
		var $AddToCartBtn = $('._product-details-sticky .js-addtocart-actions');

		if($productSticky.length && !$('body.modal-open').length) {
			if (variant.stock_status_key == 'request') {
				$statusBtn.removeClass('hide');
				$AddToCartBtn.addClass('hide');
			} else {
				$statusBtn.addClass('hide');
				$AddToCartBtn.removeClass('hide');
			}
			if(variant.price_discounted_formatted) {
				$productSticky.find('.price-new-js').html(variant.price_discounted_formatted);
				$productSticky.find('.price-old-js ._product-details-price-value').html(variant.price_formatted);
				$productSticky.find('.price-discount-js ._product-details-price-value').html(variant.price_saved_formatted);
				$productSticky.find('._product-details-price-meta').removeClass('hide');
			} else {
				$productSticky.find('.price-new-js').html(variant.price_formatted);
				$productSticky.find('._product-details-price-meta').addClass('hide');
			}
		}
	});

	function handleHomeCarousels() {
		if ($('.js-text-boxes').length) {
            new Swiper('.js-text-boxes', {
                wrapperClass: '_text-boxes',
                slideClass: '_text-box',
                pagination: '._text-boxes-pagination',
                paginationClickable: true,
                paginationHide: false,
                simulateTouch: false,
                slidesPerView: 4,
                spaceBetween: 0,
                speed: 800,
                breakpoints: {
                    1199: {
                        slidesPerView: 3
                    },
                    767: {
                        slidesPerView: 2
                    },
                    479: {
                        slidesPerView: 1
                    }
                },
                paginationBulletRender: function (index, className) {
                    return '<span class="' + className + '"></span>';
                }
            });
        }

        if ($('.js-showcase-brands1').length) {
            new Swiper('.js-showcase-brands1 ._showcase', {
                wrapperClass: '_showcase-list',
                slideClass: '_showcase-item',
                pagination: $('.js-showcase-brands1 ._showcase-brands-pagination'),
                paginationClickable: true,
                paginationHide: false,
                simulateTouch: false,
                slidesPerView: 6,
                spaceBetween: 0,
                speed: 800,
                breakpoints: {
                    1199: {
                        slidesPerView: 4
                    },
                    767: {
                        slidesPerView: 2
                    },
                    479: {
                        slidesPerView: 1
                    }
                },
                paginationBulletRender: function (index, className) {
                    return '<span class="' + className + '"></span>';
                }
            });
        }

        if ($('.js-showcase-brands2').length) {
            new Swiper('.js-showcase-brands2 ._showcase', {
                wrapperClass: '_showcase-list',
                slideClass: '_showcase-item',
                pagination: $('.js-showcase-brands2 ._showcase-brands-pagination'),
                paginationClickable: true,
                paginationHide: false,
                simulateTouch: false,
                slidesPerView: 6,
                spaceBetween: 0,
                speed: 800,
                breakpoints: {
                    1199: {
                        slidesPerView: 4
                    },
                    767: {
                        slidesPerView: 2
                    },
                    479: {
                        slidesPerView: 1
                    }
                },
                paginationBulletRender: function (index, className) {
                    return '<span class="' + className + '"></span>';
                }
            });
        }

		if ($('.js-showcase-categories').length && $(window).width() < 993) {
			new Swiper('.js-showcase-categories ._showcase', {
				wrapperClass: '_showcase-list',
				slideClass: '_showcase-item',
				pagination: $('.js-showcase-categories ._showcase-categories-pagination'),
				paginationClickable: true,
				paginationHide: false,
				simulateTouch: false,
				slidesPerView: 2,
				spaceBetween: 0,
				speed: 800,
				breakpoints: {
					479: {
						slidesPerView: 1
					}
				},
				paginationBulletRender: function (index, className) {
					return '<span class="' + className + '"></span>';
				}
			});
		}

        if ($('.js-showcase-product-1').length) {
            new Swiper('.js-showcase-product-1 ._showcase-product-list', {
                wrapperClass: '_products-list',
                slideClass: '_product',
                slidesPerView: 4,
                spaceBetween: 0,
                simulateTouch: false,
                prevButton: '.js-showcase-product-1-prev',
                nextButton: '.js-showcase-product-1-next',
                speed: 800,
                breakpoints: {
                    1199: {
                        slidesPerView: 3
                    },
                    767: {
                        slidesPerView: 2
                    },
                    379: {
                        slidesPerView: 1
                    }
                }
            });
        }

        if ($('.js-showcase-product-3').length) {
            new Swiper('.js-showcase-product-3 ._showcase-product-list', {
                wrapperClass: '_products-list',
                slideClass: '_product',
                slidesPerView: 4,
                spaceBetween: 0,
                simulateTouch: false,
                prevButton: '.js-showcase-product-3-prev',
                nextButton: '.js-showcase-product-3-next',
                speed: 800,
                breakpoints: {
                    1199: {
                        slidesPerView: 3
                    },
                    767: {
                        slidesPerView: 2
                    },
                    379: {
                        slidesPerView: 1
                    }
                }
            });
        }

        if ($('.js-showcase-product-4').length) {
            new Swiper('.js-showcase-product-4 ._showcase-product-list', {
                wrapperClass: '_products-list',
                slideClass: '_product',
                slidesPerView: 4,
                spaceBetween: 0,
                simulateTouch: false,
                prevButton: '.js-showcase-product-4-prev',
                nextButton: '.js-showcase-product-4-next',
                speed: 800,
                breakpoints: {
                    1199: {
                        slidesPerView: 3
                    },
                    767: {
                        slidesPerView: 2
                    },
                    379: {
                        slidesPerView: 1
                    }
                }
            });
        }

        if ($('.js-blog-showcase').length) {
            new Swiper('.js-blog-showcase', {
                wrapperClass: '_blog-list-articles',
                slideClass: '_blog-list-article',
                slidesPerView: 3,
                spaceBetween: 30,
                simulateTouch: false,
                prevButton: '.js-blog-showcase-prev',
                nextButton: '.js-blog-showcase-next',
                speed: 800,
                breakpoints: {
                    992: {
                        slidesPerView: 2
                    },
                    479: {
                        slidesPerView: 1
                    }
                }
            });
        }
	}

	function megaMenu() {
		$('.js-categories-menu').find('.js-filter-list > ul > .item-collapse').each(function(){
			var $this = $(this);
			var $child = $this.children('ul');
			var $items = $child.children('li');
			var childHeight = $child.height();
			var itemsHeight = 0;
			var itemsHighest = 0;
			var colCount = 0;
			var columns = {};
			var ratio, itemOffset;

			$items.each(function(){
				var itemHeight = $(this).outerHeight(true);

				itemsHeight += itemHeight;

				if (childHeight < itemHeight) {
					childHeight = itemHeight;
					$child.height(childHeight);
				}
			});

			ratio = Math.ceil(itemsHeight/childHeight);

			/*brutally hardcoded*/
			if (ratio > 1 && childHeight*ratio - itemsHeight < 35) {
				$child.height(childHeight + 35);
			}

			$child.addClass('width-' + (ratio > 3 ? 3 : ratio));

			if (ratio > 3) {
				$child.height('auto');
			}

			/*if (ratio > 1) {
				$items.each(function(){
					var $item = $(this);

					if ($item.offset().left != itemOffset) {
						if (childHeight > columns[colCount]) {
							$item.prev().css({ 'margin-bottom': childHeight - columns[colCount] })
						}

						itemOffset = $item.offset().left;
						colCount++;
						columns[colCount] = 0;
					}

					columns[colCount] += $item.outerHeight(true);
				});

				for (var i in columns) {
					if (itemsHighest < columns[i]) {
						itemsHighest = columns[i]
					}
				}

				if (itemsHighest > childHeight) {
					$child.height(itemsHighest);
				}

				$child.removeClass('width-2 width-3').addClass('width-' + (colCount > 3 ? 3 : colCount));
			}*/

			$child.addClass('loaded');
		});

		$('.js-filter-list > ul > .item-collapse')
			.on('mouseenter', function(){
				$(this).closest('._categories-menu-dropdown').addClass('hovered');
			})
			.on('mouseleave', function(){
				$(this).closest('._categories-menu-dropdown').removeClass('hovered');
			});
	}

	function productStickyButton() {
		$('.js-sticky-button-buy').on('click', function () {
			$('.add-to-cart-form-js').trigger('submit');
		});
	}

	function promoBarMobileOld() {
		// if($(window).width() < 1025) {
			var $promobar = $('._promo-bar-container:not(.hidden)');
			var $fixedHeader = $('._header-fixed');
			var $wrapper = $('._wrapper');
			if($promobar.length) {
				$fixedHeader.css({
					'top': $promobar.css('height')
				});
				$promobar.css({
					'position': 'fixed',
					'top': 0,
					'left': 0,
					'width': '100%',
					'z-index': 5
				});
				$wrapper.css({
					'paddingTop': (parseInt($promobar.css('height')) + parseInt($fixedHeader.css('height')))
				});
			}
		// }
	}

	function promoBarMobile() {
		var headerHeight = 0;
		var $promobar = $('._promo-bar-container:not(.hidden)');
		var $fixedHeaderOne = $('._header-fixed._header-variant-one:not(._header-variant-three)');
		var $fixedHeaderTwo = $('._header-fixed._header-variant-two');
		var $fixedHeaderThree = $('._header-fixed._header-variant-one._header-variant-three');
		var $headerOne = $('._header._header-variant-one:not(._header-variant-three)');
		var $headerThree = $('._header._header-variant-one._header-variant-three');
		var $navBarDropdown = $('._header-variant-one._header._dropdown-menu-mobile ._navbar');
		var $content = $('._header-fixed._header-variant-two ~ ._content');
		if($promobar.length) {
			$fixedHeaderThree.css({
				'top': $promobar.height() + 'px'
			});
			if ($(window).width() <= 1200) {
				$fixedHeaderOne.css({
					'top': $promobar.height() + 'px'
				});
				if ($headerOne.length) {
					headerHeight = ($promobar.height() + $fixedHeaderOne.height()) + 'px';
					$headerOne.css({
						'height': headerHeight,
					});
					$navBarDropdown.css({
						'top': headerHeight,
					})
				}
				if ($headerThree.length) {
					headerHeight = ($promobar.height() + $fixedHeaderThree.height()) + 'px';
					$headerThree.css({
						'height': headerHeight,
					});
				}
			} else {
				if ($headerOne.length) {
					$fixedHeaderOne.css({
						'top': 0
					});
					$navBarDropdown.css({
						'top': 0,
					});
				}
			}

			if ($fixedHeaderTwo.length) {
				headerHeight = $fixedHeaderTwo.height() + 'px';
				$promobar.css({
					'margin-top': headerHeight,
				});
			}
		} else {
			if ($fixedHeaderTwo.length) {
				headerHeight = $fixedHeaderTwo.height() + 'px';
				$content.css({
					'margin-top': headerHeight,
				});
			}
		}
	}

	function handlePromoBarRemove() {
		$('.js-promo-bar-remove').on('click', function () {
			var $fixedHeader = $('._header-fixed');
			var $header = $('._header');
			var $headerTwo = $('._header-variant-two._header');
			var $headerThree = $('._header-variant-three._header');
			var $content = $('._content');
			$fixedHeader.css({
				'top': 0
			});
			if ($(window).width() <= 1200 || $headerThree.length) {
				$header.css({
					'height': parseFloat($fixedHeader.css('height'))
				});
			}
			if($headerTwo.length) {
				$content.css({
					'margin-top': parseFloat($fixedHeader.css('height'))
				});
			}
		});
	}

	function handleListingImageHover() {
		var imageHolder = '._product ._product-image';
		var delay = 100;

		$doc.on('mouseenter', imageHolder, function() {
			var $holder = $(this).find('._product-image-thumb-holder');
			var $image = $holder.find('img');
			if ($image.data('second-src')) {
				$holder.css('opacity', 0);
				setTimeout(function() {
					$image.attr('src', $image.data('second-src'));
				}, delay - 50);
				setTimeout(function() {
					$holder.css('opacity', 1);
				}, delay);
			}
		});

		$doc.on('mouseleave', imageHolder, function() {
			var $holder = $(this).find('._product-image-thumb-holder');
			var $image = $holder.find('img');
			if ($image.data('second-src')) {
				$holder.css('opacity', 0);
				setTimeout(function() {
					$image.attr('src', $image.data('first-src'));
				}, delay - 50);
				setTimeout(function() {
					$holder.css('opacity', 1);
				}, delay);
			}
		});
	}

	function handleToggleOnScroll() {
		var didScroll;
		var lastScrollTop = 0;
		var delta = 5;
		var $header = $('.js-toggle-on-scroll');
		var headerHeight = $('.js-toggle-on-scroll').outerHeight();

		$(window).scroll(function(event){
			didScroll = true;
		});

		setInterval(function() {
			if (didScroll && !$('._product-details-sticky-container').length) {
				hasScrolled();
				didScroll = false;
			}
		}, 250);

		function hasScrolled() {
			var scrollTop = $(this).scrollTop();

			if(Math.abs(lastScrollTop - scrollTop) <= delta)
				return;

			if (scrollTop > lastScrollTop && scrollTop > headerHeight){
				$header.css('top', -$header.height());
				$header.find('._navigation-main-list-item').removeClass('cc-open');
			} else {
				if(scrollTop + $win.height() < $doc.height()) {
					$header.css('top', 0);
				}
			}
			lastScrollTop = scrollTop;
		}
	}

	function handleMenuOpenClick() {
		if($.isLg()) {
			$('._type-megamenu-click._navbar ._navigation li.item-collapse>a').attr('href', 'javascript:;');
			$doc.on('click', '._type-megamenu-click._navbar ._navigation ._navigation-dropdown li.item-collapse', function (e) {
				var $this = $(this);
				$this.siblings().removeClass('cc-open cc-open-dd');
				$this.toggleClass('cc-open cc-open-dd');
			});
			$(document).on('click', '._type-megamenu-click ._navigation>._navigation-main-list>li', function () {
				$(this).siblings().removeClass('cc-open');
			});
		}
	}

	function handleNavigationDdClick() {
		$doc.on('click', '._type-megamenu-click._navbar ._navigation ._navigation-dropdown', function(e) {
			e.stopPropagation();
		});
	}

	function closeDdMenuOnBodyClick() {
		$doc.on('click', function (e) {
			var $menu = $('._type-megamenu-click._navbar ._navigation ._navigation-dropdown');
			var $link = $('._type-megamenu-click._navbar ._navigation ._navigation-dropdown li.item-collapse, ._navigation ._navigation-main-list>li.item-collapse');
			e.stopPropagation();
			if (!$(e.target).closest($link).length && $(window).width() >= 1025) {
				if ($menu.is(":visible")) {
					$link.removeClass('cc-open cc-open-dd');
				}
			}
		});
	}

	function setBackHtml() {
		$('._type-megamenu-click ._navbar-inner ._navigation-main-list>._navigation-main-list-item ._navigation-dropdown._navigation-dropdown-level-1>ul>li>._navigation-dropdown')
			.append('<div class="js-back back-to-prev-menu _navigation-dropdown-list-item"><a href="javascript:;"><i class="fal fa-chevron-left"></i>&nbsp;</a></div>')
	}

	function subMenuHeadHtml(categoryTitle, categoryUrl) {
		return '<div class="row submenu-head">'+
			'<div class="col-sm-4 text-left" style="padding-left: 0;">'+
			'<a href="javascript:;" class="back-btn js-back">'+
			'<i class="fal fa-chevron-left"></i>'+
			'</a>'+
			'</div>'+
			'<div class="col-sm-5 text-left">'+
			'<a href="'+ categoryUrl +'" class="category-title">'+
			categoryTitle+
			'</a>'+
			'</div>'+
			'<div class="col-sm-3 text-right" style="padding-right: 0;">'+
			'<a href="javascript:;" class="close-btn js-close-dropdown">'+
			'<i class="fal fa-times fa-lg"></i>'+
			'</a>'+
			'</div>'+
			'</div>';
	}

	function setNavCategoryHead() {
		$('._navbar._type-megamenu-click ._navbar-inner ._navigation-main-list>._navigation-main-list-item:first-child ._navigation-dropdown._navigation-dropdown-level-1>ul>li>._navigation-dropdown').map(function() {
			var categoryTitle = $(this).prev('a').find('._figure-stack-label').text();
			var categoryUrl = $(this).prev('a').attr('href');
			$(this).prepend(subMenuHeadHtml(categoryTitle, categoryUrl));
		});
	}

	function handleBack() {
		$doc.on('click', '.js-back', function() {
			$(this).closest('li').removeClass('cc-open');
		});
	}

	function handleClose() {
		$doc.on('click', '.js-close-dropdown', function (e) {
			var $menu = $('._navbar._type-megamenu-click ._navigation ._navigation-dropdown');
			var $link = $('._navbar._type-megamenu-click ._navbar-inner>._navigation>ul>li:first-child');
			e.stopPropagation();
			if ($(window).width() >= 1025) {
				if ($menu.is(":visible")) {
					$link.removeClass('cc-open');
				}
			}
		});
	}

	function megaMenu() {
		$win.on('load', function(){
			if ($(window).width() > 1024) {
				$('.js-header-bar-inner').find('._navigation-dropdown-level-1 > ul').each(function(){
					var $this = $(this);
					var $header = $this.closest('.js-header-bar-inner');
					var treshold = 250;
					var dropdownLeftCSS = 50;
					var $items = $this.children('li');
					var offsetLeft = $this.offset().left;
					var headerRight = $header.offset().left + $header.outerWidth();
					var itemsHeight = 0;
					var ratio;

					$items.each(function(){
						var itemHeight = $(this).outerHeight(true);

						itemsHeight += itemHeight;

						if (treshold < itemHeight) {
							treshold = itemHeight;
							/* hardcoded with 20px */
							$this.height(treshold + 20);
						}
					});

					ratio = Math.ceil(itemsHeight/treshold);

					$this.addClass('width-' + (ratio > 4 ? 4 : ratio));

					if (ratio > 4) {
						$this.height('auto');
					}

					var offsetRight = offsetLeft + $this.outerWidth(true);

					if (offsetRight > headerRight) {
						var left = -(offsetRight - headerRight) - dropdownLeftCSS;

						$this.closest('._navigation-dropdown').css({ 'left': left });
					}

					$this.addClass('loaded');
				});
			}
		});
	}

	function handleHeaderVariantThree() {
		var $promoBar = $('._promo-bar-container:not(.hidden)');
		var $headerThree = $('._header-variant-three._header');
		if($promoBar.length) {
			$headerThree.css('height', parseFloat($promoBar.height() + $headerFixedThree.height()) + 'px');
		} else {
			$headerThree.css('height', $headerFixedThree.height() + 'px');
		}
	}

	function handleStaticPage() {
		var $staticPage = $('._static-page');
		var $headerTransparent = $('._header-transparent');
		if($staticPage.length && $headerTransparent.length) {
			$staticPage.css({
				'padding-top': parseFloat($headerTransparent.height() + 60) + 'px'
			});
		}
	}

	function setListDescriptionAccordingPageNumber () {
		var $productsListDescription = $('._products-list-info');
		if ((window.location.pathname.includes('category') ||
			window.location.pathname.includes('vendor')) && $productsListDescription.length) {
			if ((!window.location.search.includes('page=1') && window.location.search.includes('page='))
				|| !window.location.search.includes('')) {
				$productsListDescription.addClass('hidden');
			} else {
				$productsListDescription.removeClass('hidden');
			}
		}
	}

	function fixedSidebar() {
		var $sidebar = $('.js-fixed-sidebar');
		marginTop = $('.js-header-fixed').length ? $('.js-header-fixed').outerHeight(true) + 10 : 10;
		var $sidebar_init = function () {
			if ($.isLg()) {
				$sidebar.theiaStickySidebar({
					additionalMarginTop: marginTop,
					additionalMarginBottom: 10,
					containerSelector: $sidebar.closest('.js-fixed-sidebar-container')
				});
			}
		};

		$sidebar_init();
	}

	$doc.ready(function(){
		toTop();
		navigationOpen();
		searchToggle();
		categoriesMobileToggle();
		navMobileOpen();
		sidebarOpen();
		listCollapse();
		faqCollapse();
		shareToggle();
		checkoutAddressBook();
		orderDetailsToggle();
		swiperChangeImage();
		sidebarToggle();
		sidebarBoxToggle();
		categoryPropertiesToggle();
		handleHomeCarousels();
		productDetailsSidebar();
		handleTabs();
		handleTabsMobile();
		productStickyButton();
		sidebarAjaxSuccess();
		promoBarMobile();
		handlePromoBarRemove();
		handleListingImageHover();
		if($.isLg()) {
			handleToggleOnScroll();
		}
		handleMenuOpenClick();
		handleNavigationDdClick();
		closeDdMenuOnBodyClick();
		setNavCategoryHead();
		handleBack();
		handleClose();
		megaMenu();
		handleHeaderVariantThree();
		// handleStaticPage();
		fixedSidebar()
	});

	$doc.on('shown.bs.modal', '.modal', function () {
		handleTabs();
	});

	$doc.on('cc.category-properties.successAjax', function () {
		handleListingImageHover();
	});

	$doc.on('cc.category-properties.after', function () {
		setListDescriptionAccordingPageNumber();
	});

	$win.on({
		load: function () {
			megaMenu();
			$('.js-wrapper').addClass('_page-loaded');
		},
		scroll: function () {
			headerFixed();
			productFixed();
			toTopFixed();
		},
		resize: function () {
			promoBarMobile();
			handleHeaderVariantThree();
		}
	});
})(jQuery);