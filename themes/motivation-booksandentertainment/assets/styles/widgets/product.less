/*=============================================================================*\
    PRODUCT
\*=============================================================================*/

/*  LIST
-------------------------------------------------------------------------------*/

._products-list-info {
	margin-top: @separator;
}

._products-list-info-item {
	overflow: hidden;
	color: @color-main-meta-text; /* theme */
	margin-bottom: @separator-small;

	&:last-child {
		margin-bottom: 0;
	}
}

._products-list-info-item-body {
	display: table;
	width: 100%;
}

._products-list-info-item-image {
	display: table-cell;
	vertical-align: top;
	width: 1%;
	padding: 5px 0;
	padding-right: 30px;

	img {
		max-width: 300px;
	}
}

._products-list-info-item-description {
	display: table-cell;
	vertical-align: top;
}

/*  Box
-------------------------------------------------------------------------------*/

._product-ribbon-holder,
._product-details-ribbon-holder {
	position: absolute;
	top: 0;
	left: 0;
	pointer-events: none;
}

._product-discount,
._product-details-discount {
	position: absolute;
	top: 0;
	right: 0;
}

._product-ribbon,
._product-details-ribbon,
._product-discount,
._product-details-discount {
	display: block;
	line-height: 1;
	padding: 6px 10px 5px;
	font-size: 11px;
	margin-bottom: 1px;
	text-align: center;
	pointer-events: none;
}

._product-ribbon-new,
._product-details-ribbon-new {
	background-color: @color-label-new-background; /* theme */
	color: @color-label-new-text; /* theme */
}

._product-ribbon-sale,
._product-details-ribbon-sale {
	background-color: @color-label-sale-background; /* theme */
	color: @color-label-sale-text; /* theme */
}

._product-ribbon-delivery,
._product-details-ribbon-delivery {
	background-color: @color-label-delivery-background; /* theme */
	color: @color-label-delivery-text; /* theme */
}

._product-ribbon-custom,
._product-details-ribbon-custom {
	background-color: @color-label-custom-background; /* theme */
	color: @color-label-custom-text; /* theme */
}

._product-ribbon-featured,
._product-details-ribbon-featured {
	background-color: @color-label-featured-background; /* theme */
	color: @color-label-featured-text; /* theme */
}

._product-discount,
._product-details-discount {
	background-color: @color-label-discount-background; /* theme */
	color: @color-label-discount-text; /* theme */
}

._product-discount,
._product-leasing,
._product-ribbon-banner,
._product-details-ribbon-banner {
	position: absolute;
	z-index: 1;
	pointer-events: none;
}

._product-ribbon-banner {
	position: absolute;
	z-index: 1;
	max-width: 25%;

	img {
		max-width: 100%;
		max-height: 100px;
	}
}

._product-leasing,
._product-details-leasing {
	background-color: @color-label-leasing-background; /* theme */
	color: @color-label-leasing-text; /* theme */
}

._product-details-leasing-button {
	text-align: center;
	margin-bottom: 25px;

	a {
		._button();
		._button-small();
		._button-small-ghost();
	}
}

._product-name-tag {
	font-family: @font-family-product-list-title; /* theme */
	font-size: @font-size-product-list-title; /* theme */
	font-weight: @font-weight-product-list-title; /* theme */
	font-style: @font-style-product-list-title; /* theme */
	line-height: 1.15;

	a {
		color: @color-product-listing-title; /* theme */

		@media @hover {
			&:hover {
				color: @color-product-listing-highlight; /* theme */
			}
		}
	}
}

._product-price {
	font-size: calc(@font-size-main ~'+' 8px); /* theme */
	color: @color-product-listing-price; /* theme */

	._medal-info {
		margin: 5px;
	}

	.TiditOfferIcon {
		.TiditOfferLayer {
			left: auto;
			top: auto;
			right: 0;
			bottom: 0;
			padding-left: 0;
			padding-right: 19px;
		}
	}
}

._product-price-old {
	color: @color-product-listing-price-old; /* theme */
}

._product-compare,
._product-add-to-favorite {
	color: @color-product-listing-actions; /* theme */

	&:focus {
		color: @color-product-listing-actions; /* theme */
	}

	&.active {
		.fa {
			color: @color-product-listing-actions-highlight; /* theme */
		}
	}

	@media @hover {
		&:hover {
			color: @color-product-listing-actions-highlight; /* theme */
		}
	}
}

._product-compare {
	.checker {
		span.checked {
			color: @color-product-listing-actions-highlight; /* theme */
		}
	}
}

._product-description {
	display: none;
}

/*  Box default from theme knowledge
-------------------------------------------------------------------------------*/

._list-one {
	._product {
		position: relative;
		transition: .5s;

		&:before {
			content: '';
			position: absolute;
			top: 20px;
			left: 0;
			bottom: 47px;
			border-left: 1px solid;
			border-color: @color-product-listing-borders; /* theme */
			transition: .5s;
		}

		@media @hover {
			&:hover {
				box-shadow: 0 0 6px rgba(0, 0, 0, .3);

				&:before {
					opacity: 0;
					visibility: hidden;
				}

				+ ._product {
					&:before {
						opacity: 0;
						visibility: hidden;
					}
				}

				._product-quick-view,
				._product-bar {
					opacity: 1;
					visibility: visible;
				}

				._product-image-thumb-holder {
					//transform: scale(1.05);
				}
			}
		}
	}

	._product-image {
		margin-bottom: 10px;
		position: relative;
	}

	._product-image-thumb {
		display: block;
		overflow: hidden;
	}

	._product-image-thumb-holder {
		transition: .5s;
	}

	._product-ribbon-holder,
	._product-details-ribbon-holder {
		position: absolute;
		top: 0;
		left: 0;
		pointer-events: none;
	}

	._product-discount,
	._product-details-discount {
		position: absolute;
		top: 0;
		right: 0;
	}

	._product-ribbon,
	._product-details-ribbon,
	._product-discount,
	._product-details-discount {
		display: block;
		line-height: 1;
		padding: 6px 10px 5px;
		font-size: 11px;
		margin-bottom: 1px;
		text-align: center;
		pointer-events: none;
	}

	._product-discount,
	._product-leasing,
	._product-ribbon-banner,
	._product-details-ribbon-banner {
		position: absolute;
		z-index: 1;
		pointer-events: none;
	}

	._product-ribbon-banner {
		position: absolute;
		z-index: 1;
		max-width: 25%;

		img {
			max-width: 100%;
			max-height: 100px;
		}
	}


	._product-details-leasing-button {
		text-align: center;
		margin-bottom: 25px;

		a {
			._button();
			._button-small();
			._button-small-ghost();
		}
	}

	._product-info {
		padding-bottom: 47px;
		text-align: center;
		position: relative;
	}

	._product-name {
		display: block;
		min-height: 50px;
	}

	._product-countdown {
		margin-top: 10px;
		text-align: center;

		._countdown-label {
			display: none;
		}
	}

	._product-price {
		line-height: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		margin: 0 -5px;
		padding: 4px 0;
		font-weight: 500;
		font-size: calc(@font-size-main ~'+' 4px); /* theme */
		color: @color-product-listing-price; /* theme */

		._medal-info {
			margin: 5px;
		}

		.TiditOfferIcon {
			.TiditOfferLayer {
				left: auto;
				top: auto;
				right: 0;
				bottom: 0;
				padding-left: 0;
				padding-right: 19px;
			}
		}
	}

	._product-price-old {
		order: 1;
		display: block;
		vertical-align: middle;
		margin: 5px;
		font-size: 14px;
		color: @color-product-listing-price-old; /* theme */
		position: relative;
		top: 1px;
	}

	._product-price-compare {
		order: 2;
		display: block;
		vertical-align: middle;
		margin: 0 5px;
	}

	._product-bar {
		border-top: 1px solid;
		border-color: @color-product-listing-borders; /* theme */
		font-size: 12px;
		line-height: 1;
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 13px 0;
		opacity: 0;
		visibility: hidden;
		transition: .5s;
	}

	._product-bar-col-favorite {
		float: left;
	}

	._product-bar-col-compare {
		float: right;
	}

	._product-compare {
		._checkbox {
			padding: 0;
			min-height: initial;
			font-size: inherit;
			line-height: inherit;

			.checker {
				span,
				span.checked {
					background-color: transparent;
				}
			}
		}

		.checker {
			position: static;
			top: 0;
			width: auto;
			height: auto;

			span,
			span.checked {
				background-color: transparent;
				width: auto;
				height: auto;
				border: 0;
				color: inherit; /* theme */

				&:before {
					display: block;
					content: "\f021";
					font-family: @font-awesome;
					font-size: 14px;
					vertical-align: middle;
					position: relative;
					top: -1px;
					left: 0;
					transform: translate(0, 0);
				}
			}

			span.checked {
				color: @color-product-listing-actions-highlight; /* theme */
			}

			input {
				display: none;
			}
		}
	}

	._product-add-to-favorite {
		.fa {
			font-size: 14px;
			vertical-align: middle;
			position: relative;
			top: -1px;
		}
	}

	._product-add {
		margin: 10px 0;

		._button {
			width: 100%;
			._button-small();
		}
	}

	._product-quick-view {
		position: absolute;
		top: 50%;
		left: 50%;
		._button();
		._button-small();
		padding: 0;
		width: 32px;
		line-height: 32px;
		font-size: 15px;
		.centerer(true, true);
		opacity: 0;
		transition: .5s;
		visibility: hidden;
	}
}


/*  BOX from Flair
-------------------------------------------------------------------------------*/

._list-two {
	._product {
		border: 1px solid transparent;
		padding-top: 15px;
		padding-bottom: 10px;
		transition: border-color .2s;
		//color: @color-product-listing-text; /* theme */

		@media @hover {
			&:hover {
				//border-color: @color-product-listing-borders; /* theme */

				._product-image-thumb {
					&:before {
						opacity: .3;
						transform: scale(1);
					}
				}

				._product-quick-view {
					opacity: 1;
					visibility: visible;
				}

				._product-price {
					//color: @color-product-listing-hovers; /* theme */

					&-old {
						//color: @color-product-listing-price-old; /* theme */
					}
				}
			}
		}

		&-image {
			margin-bottom: 10px;
			position: relative;
			perspective: 1000px;

			&-thumb {
				display: block;
				position: relative;

				&:before {
					content: '';
					//background-color: @color-product-listing-quick-view-background; /* theme */
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					opacity: 0;
					z-index: 1;
					transition: .3s;
					transform: scale(0);
				}
			}
		}

		&-quick-view {
			background-color: transparent;
			width: 50px;
			height: 50px;
			opacity: 0;
			visibility: hidden;
			z-index: 1;
			.centerer(true, true);

			@media @hover {
				&:hover {
					&:before {
						background-color: @color-button-text; /* theme */
					}

					i {
						color: @color-button-background; /* theme */
					}
				}
			}

			&:before {
				content: '';
				background-color: @color-button-background; /* theme */
				opacity: .7;
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				-webkit-transition: .3s;
				transition: .3s;
			}

			span {
				.centerer(true, true);
				font-size: 20px;
			}

			i {
				//color: @color-product-listing-quick-view-icon; /* theme */
				-webkit-transition: .3s;
				transition: .3s;
			}
		}

		&-ribbon-banner {
			position: absolute;
			z-index: 1;
			max-width: 25%;

			img {
				max-width: 100%;
				max-height: 100px;
			}
		}

		&-name {
			border-bottom: 1px solid;
			border-color: @color-product-listing-borders; /* theme */
			padding-bottom: 6px;
			margin-bottom: 4px;

			h3 {
				font-family: @font-family-product-list-title; /* theme */
				font-size: @font-size-product-list-title; /* theme */
				font-weight: @font-weight-product-list-title; /* theme */
				font-style: @font-style-product-list-title; /* theme */
				line-height: @line-height-medium;
				min-height: calc(@font-size-product-list-title ~'*' 2);

				a {
					display: inline-block;
					//color: @color-product-listing-title; /* theme */

					&:hover {
						//color: @color-product-listing-hovers; /* theme */
						text-decoration: none;
					}
				}
			}

			& + ._product-description {
				margin-top: 10px;
			}
		}

		&-description {
			margin-bottom: 10px;
			display: none;
		}

		&-short-description {
			margin-bottom: 10px;
		}

		&-meta {
			display: none;
			margin-bottom: 5px;
		}

		&-bar {
			display: table;
			width: 100%;
			margin-bottom: 10px;
			font-size: calc(@font-size-main ~'-' 2px); /* theme */

			&-col {
				display: table-cell;
				vertical-align: top;

				&:last-child {
					text-align: right;
					padding-left: 10px;
				}

				._checkbox {
					&.active {
						//color: @color-product-listing-hovers; /* theme */
					}

					.checker {
						top: 3px;

						span {
							//border-color: @color-product-listing-text; /* theme */

							&.checked {
								//border-color: @color-product-listing-hovers; /* theme */

								&:before {
									//color: @color-product-listing-hovers; /* theme */
								}
							}
						}
					}
				}
			}
		}

		&-compare {
			._checkbox {
				padding-left: 20px;
				.checker {
					width: 14px;
					height: 14px;
					input,
					span {
						width: 14px;
						height: 14px;
					}
					span {
						&:before {
							font-size: 8px;
						}
					}
				}
			}
		}

		&-countdown {
			margin-top: 10px;

			._countdown-label {
				display: none;
			}
		}

		&-options {
			display: table;
			width: 100%;
		}

		&-price {
			display: table-cell;
			vertical-align: middle;
			font-size: calc(@font-size-main ~'-' 2px); /* theme */
			font-weight: bold;
			direction: ltr;
			text-align: left;
			transition: .2s;

			&-inner {
				vertical-align: middle;
				display: inline-block;
				margin-right: 5px;

				&:last-child {
					margin-right: 0;
				}
			}

			&-compare {
				display: inline-block;
				margin-right: 10px;
			}

			&-old {
				display: inline-block;
				font-weight: normal;
			}

			._medal-info {
				display: inline-block;
				vertical-align: middle;
			}
		}

		&-add {
			text-align: right;
			display: table-cell;
			vertical-align: middle;
			padding-left: 15px;

			._button {
				padding: 8px 12px 6px;
				font-size: 14px;
			}
		}

		&.revealed {
			._product-ribbon {
				opacity: 1;
				visibility: visible;
			}
		}

		&-ribbon,
		&-details-ribbon {
			float: left;
			clear: both;
			font-size: calc(@font-size-main ~'-' 2px); /* theme */
			text-align: center;
			font-weight: bold;
			padding: 5px 15px;
			pointer-events: none;
			.uppercase();

			&-holder {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				pointer-events: none;

				._product-ribbon,
				._product-details-ribbon {
					margin-bottom: 1px;
				}
			}

			&-new {
				position: absolute;
				top: 0;
				right: 0;
			}

			&-banner {
				position: absolute;
				z-index: 1;
				pointer-events: none;
			}

			& when (@rtl) {
				white-space: nowrap;
			}
		}

		&-discount,
		&-details-discount {
			 position: absolute;
			 bottom: 0;
			 left: 0;
			 top: auto;
			 right: auto;
		}

		&-ribbon,
		&-discount,
		&-details-discount {
			line-height: initial;
		}
	}
}

/*  Box horizontal
-------------------------------------------------------------------------------*/
._list-horizontal {
	._list-one();
}

@media (min-width: @screen-lg-min) {
	._products-list-main,
	._showcase-product:not(.js-carousel-container) {
		._list-horizontal {
			._product-inner {
				display: flex;
				align-items: flex-start;
			}

			._product {
				position: relative;
				border: 1px solid @color-product-listing-borders; /* theme */;
				border-radius: 20px;
				width: 100% !important;
				text-align: left;
				padding-bottom: 10px;
				padding-top: 10px;
				margin-bottom: 10px;
				transition: .5s;

				&:before {
					content: none;
				}

				@media @hover {
					&:hover {
						box-shadow: 0 0 6px rgba(0, 0, 0, .3);

						._product-quick-view {
							opacity: 1;
							visibility: visible;
						}
					}
				}
			}

			._product-image {
				width: 20%;
				margin-bottom: 0;
				position: relative;
			}

			._product-image-thumb {
				display: block;
				overflow: hidden;
			}

			._product-image-thumb-holder {
				transition: .5s;
			}

			._product-ribbon-holder,
			._product-details-ribbon-holder {
				position: absolute;
				top: 0;
				left: 0;
				pointer-events: none;
			}

			._product-discount,
			._product-details-discount {
				position: absolute;
				top: 0;
				right: 0;
			}

			._product-ribbon,
			._product-details-ribbon,
			._product-discount,
			._product-details-discount {
				display: block;
				line-height: 1;
				padding: 6px 10px 5px;
				font-size: 11px;
				margin-bottom: 1px;
				text-align: center;
				pointer-events: none;
			}

			._product-discount,
			._product-leasing,
			._product-ribbon-banner,
			._product-details-ribbon-banner {
				position: absolute;
				z-index: 1;
				pointer-events: none;
			}

			._product-ribbon-banner {
				position: absolute;
				z-index: 1;
				max-width: 25%;

				img {
					max-width: 100%;
					max-height: 100px;
				}
			}

			._product-details-leasing-button {
				text-align: center;
				margin-bottom: 25px;

				a {
					._button();
					._button-small();
					._button-small-ghost();
				}
			}

			._product-info {
				display: flex;
				align-items: center;
				align-self: normal;
				justify-content: space-between;
				padding-left: 20px;
				padding-top: 47px;
				padding-bottom: 47px;
				text-align: center;
				position: relative;
				width: 80%;
				min-height: 142px;
			}

			._product-name {
				display: block;
				text-align: left;
				width: 40%;
				min-height: auto;
			}

			._product-countdown {
				margin-top: 0;
				text-align: left;

				._countdown-label {
					display: none;
				}
			}

			._product-price {
				line-height: 1;
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				justify-content: center;
				margin: 0;
				padding: 4px 0;
				font-weight: 500;
				font-size: calc(@font-size-main ~'+' 4px); /* theme */
				color: @color-product-listing-price; /* theme */

				._medal-info {
					margin: 5px;
				}

				.TiditOfferIcon {
					.TiditOfferLayer {
						left: auto;
						top: auto;
						right: 0;
						bottom: 0;
						padding-left: 0;
						padding-right: 19px;
					}
				}
			}

			._product-price-old {
				order: 1;
				display: inline-block;
				vertical-align: middle;
				margin: 0 5px;
				font-size: 14px;
				color: @color-product-listing-price-old; /* theme */
				position: relative;
				top: 1px;
			}

			._product-price-compare {
				order: 2;
				vertical-align: middle;
				margin: 0 5px;
			}

			._product-bar {
				border: 0; /* theme */
				font-size: 12px;
				line-height: 1;
				position: absolute;
				bottom: 0;
				left: 20px;
				right: 0;
				padding: 13px 0;
				transition: .5s;
				width: 200px;
				opacity: 1;
				visibility: visible;
			}

			._product-bar-col-favorite {
				float: left;
			}

			._product-bar-col-compare {
				float: right;
			}

			._product-compare {
				._checkbox {
					padding: 0;
					min-height: initial;
					font-size: inherit;
					line-height: inherit;

					.checker {
						span,
						span.checked {
							background-color: transparent;
						}
					}
				}

				.checker {
					position: static;
					top: 0;
					width: auto;
					height: auto;

					span,
					span.checked {
						background-color: transparent;
						width: auto;
						height: auto;
						border: 0;
						color: inherit; /* theme */

						&:before {
							display: block;
							content: "\f021";
							font-family: @font-awesome;
							font-size: 14px;
							vertical-align: middle;
							position: relative;
							top: -1px;
							left: 0;
							transform: translate(0, 0);
						}
					}

					span.checked {
						color: @color-product-listing-actions-highlight; /* theme */
					}

					input {
						display: none;
					}
				}
			}

			._product-add-to-favorite {
				.fa {
					font-size: 14px;
					vertical-align: middle;
					position: relative;
					top: -1px;
				}
			}

			._product-add {
				margin: 10px 0;

				._button {
					width: 100%;
					._button-small();
				}
			}

			._product-quick-view {
				position: absolute;
				top: 50%;
				left: 50%;
				._button();
				._button-small();
				padding: 0;
				width: 32px;
				line-height: 32px;
				font-size: 15px;
				.centerer(true, true);
				opacity: 0;
				transition: .5s;
				visibility: hidden;
			}

			._product-short-description {
				text-align: left;
				padding: 0 30px;
				line-height: 1.2;
				width: 40%;
			}

			._product-options {
				display: flex;
				justify-content: space-between;
				width: 275px;
				padding-top: 0;
			}

			._product-list-colors {
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
			}

			._product-list-vendor-image {
				position: absolute;
				right: 10px;
				top: 10px;
				max-width: 60px;
			}

			&._list-horizontal-normal {
				._product-image {
					width: 20%;
				}
				._product-info {
					width: 40%;
				}
				.product-variants-wrap,
				._product-options {
					width: 40%
				}
			}

			&._list-horizontal-large {
				._product-image {
					width: 33.33%;
				}
				._product-info {
					width: 33.33%;
				}
				.product-variants-wrap,
				._product-options {
					width: 33.33%;
				}
			}

			&._list-horizontal-small {
				._product-image {
					width: 15%;
				}
				._product-info {
					width: 42.50%;
				}
				.product-variants-wrap,
				._product-options {
					width: 42.50%;
				}
			}
		}
	}
}

/*  DETAILS
-------------------------------------------------------------------------------*/

/* Details */
._product-details {
	position: relative;
	z-index: 2;
}

._product-details-pictures-container {
	._product-details-title {
		display: none;
	}
}

._product-details-pictures {
	display: block;
	width: 100%;
}

._product-details-image-container {
	display: block;
	vertical-align: top;
	width: 100%;
}

._product-details-image {
	background-color: @color-product-image-background; /* theme */
	//border: 1px solid;
	//border-color: @color-product-image-borders; /* theme */
	position: relative;
	overflow: hidden;
}

._product-details-image {
	._product-details-image-thumb {
		padding-bottom: 0;

		.zoomImg {
			max-width: initial;
			max-height: initial;
			transform: none;
		}

		img {
			position: relative;
			top: 0;
			left: 50%;
			transform: translate(-50%, 0);
		}
	}
}

@gallery-thumb-size: 68px;
@gallery-thumb-offset: 10px;

//._product-details-gallery-container {
//	display: table-cell;
//	vertical-align: top;
//	width: 1px;
//	position: relative;
//	margin-bottom: 50px;

//	&._hide-nav {
//		.swiper-button {
//			display: none;
//		}
//	}

//	.loader-container {
//		display: none;
//	}

//	.swiper-button {
//		background: @color-button-background; /* theme */
//		color: @color-button-text; /* theme */
//		font-size: 20px;
//		width: @gallery-thumb-size;
//		height: 20px;
//		margin: 10px 0 0;
//		padding: 0;
//		position: relative;
//		top: auto;
//		left: auto;
//		right: auto;
//		bottom: auto;
//		visibility: hidden;
//		z-index: 1;

//		&.swiper-button-disabled {
//			opacity: 0;

//			&.swiper-button-prev {
//				display: none;

//				+ .swiper-button-next {
//					margin-top: 32px;
//				}
//			}

//			&.swiper-button-next {
//				display: none;
//			}
//		}

//		+ .swiper-button {
//			margin-top: 2px;
//		}

//		.fa {
//			.centerer(true, true);
//		}
//	}
//}

//._product-details-gallery {
//	overflow: hidden;
//	visibility: hidden;

//	&.swiper-container-vertical {
//		visibility: visible;

//		~ .swiper-button {
//			visibility: visible;
//		}
//	}

//	.swiper-wrapper {
//		height: @gallery-thumb-size*5 + @gallery-thumb-offset*4;
//	}

//	.cc-active {
//		a {
//			&:before {
//				border-width: 2px;
//				border-color: @color-main-highlight; /* theme */
//			}
//		}
//	}

//	ul {
//		list-style-type: none;
//		width: @gallery-thumb-size;
//		margin-right: 20px;
//		justify-content: flex-start;
//		align-content: flex-start;
//	}

//	li {
//		height: auto !important;
//	}

//	a {
//		display: block;
//		background-color: @color-product-image-background; /* theme */
//		padding-bottom: 100%;
//		position: relative;

//		&:before {
//			content: '';
//			border: 1px solid;
//			border-color: @color-product-image-borders; /* theme */
//			position: absolute;
//			top: 0;
//			left: 0;
//			right: 0;
//			bottom: 0;
//			z-index: 1;
//		}
//	}

//	img {
//		max-width: 100%;
//		max-height: 100%;
//		.centerer(true, true);
//	}
//}

._product-details-gallery {
	overflow: hidden;

	ul {
		.clearfix();
		list-style: none;
		li {
			width: calc(16.66% ~'-' 15px);
			margin-right: 15px;
			max-width: 100px;

			a {
				display: block;
				padding-bottom: 100%;
				position: relative;
				img {
					height: auto;
					margin: auto;
					max-height: 100%;
					max-width: 100%;
					width: auto;
					.centerer(true, true);
				}

				&:before {
					content: '';
					border: 1px solid;
					border-color: transparent;
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					z-index: 1;
				}
			}

			&.cc-active {
				a {
					&:before {
						border-width: 2px;
						border-color: @color-main-highlight; /* theme */
					}
				}
			}
		}
	}
}

._product-details-gallery-container {
	position: relative;
	margin-top: 15px;

	&._hide-nav {
		.swiper-button {
			display: none;
		}
	}

	.swiper-button {
		background: transparent;
		background-color: rgba(0, 0, 0, .5);
		width: 40px;
		height: 40px;
		color: #fff;
		position: absolute;
		z-index: 10;

		&.swiper-button-disabled {
			pointer-events: initial;
		}

		&.swiper-button-prev {
			left: 0;

			.fa {
				&:before {
					content: '\f053';
				}
			}
		}
		&.swiper-button-next {
			right: 0;

			.fa {
				&:before {
					content: '\f054';
				}
			}
		}

		.fa {
			.centerer(true, true);

			&:before {
				font-family: FontAwesome;
				font-size: calc(@font-size-main ~'+' 4px); /* theme */
				opacity: 1;
			}
		}
	}

	.swiper-loaded {
		.loader-container {
			display: none;
		}
	}
}

._product-details-ribbon-holder {
	top: 10px;
	left: 10px;
}

._product-details-discount {
	top: 10px;
	right: 0;
}

._product-list-colors {
	margin-top: 10px;
}
._product-list-colors ul {
	line-height: 1;
}

/*  Product details
-------------------------------------------------------------------------------*/

._product-details-title {
	border-bottom: 1px solid;
	border-color: @color-main-borders; /* theme */
	padding-bottom: 15px;
	margin-bottom: 5px;
}

._product-details-title-bundle {
	padding-bottom: 15px;
}

._product-details-meta {
	color: @color-main-meta-text; /* theme */
	margin-bottom: 5px;
}

._product-details-meta-item {
	display: inline-block;
	margin-right: 20px;
	margin-bottom: 8px;

	&:last-child {
		margin-right: 0;
	}
}

._product-details-meta-value {
	font-style: @font-style-main; /* theme */
}

//._product-details-info {
//	display: table;
//	width: 100%;
//	margin-bottom: 25px;
//}

._product-details-info {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	flex-flow: wrap;
	width: 100%;
	margin-bottom: 25px;
}

._product-details-price {
	display: table-cell;
}

._product-details-text {
	margin-bottom: 25px;
	vertical-align: top;
}

._product-details-price {
	vertical-align: middle;
	width: 220px;
	line-height: @line-height-medium;
}

._product-details-price-value {
	font-style: @font-style-main; /* theme */
	font-weight: 500;
	white-space: nowrap;
}

._product-details-price-old {
	display: block;
	font-size: calc(@font-size-main ~'+' 2px); /* theme */

	._product-details-price-value {
		color: @color-main-meta-text; /* theme */
		text-decoration: line-through;
	}
}

._product-details-price-saved {
	display: block;
	margin-bottom: 11px;

	._product-details-price-value {
		color: @color-product-price; /* theme */
	}
}

._product-details-price-new {
	color: @color-product-price; /* theme */
	font-size: 36px;
	line-height: @line-height-low;
	font-weight: 500;
	margin-right: 10px;
	white-space: nowrap;
}

._product-details-stock-status-bar {
	display: inline-block;
	padding: 2px 7px;
	font-size: calc(@font-size-main ~'-' 2px); /* theme */
	position: relative;
	top: -5px;
	margin-top: 5px;

	&.in-stock {
		background-color: @color-status-instock-background;
		color: @color-status-instock-text;
	}
	&.out-stock {
		background-color: @color-status-outofstock-background;
		color: @color-status-outofstock-text;
	}
	&.two-days {
		background-color: @color-status-twodays-background;
		color: @color-status-twodays-text;
	}
	&.pre-order {
		background-color: @color-status-preorder-background;
		color: @color-status-preorder-text;
	}
	&.request {
		background-color: @color-status-request-background;
		color: @color-status-request-text;
	}

	+ ._product-details-sku {
		float: right;
	}
}

._product-details-countdown {
	padding-top: 3px;
	right: 0;

	._countdown-label {
		display: inline-block;
		line-height: 1.2;
	}

	._countdown {
		white-space: nowrap;
		line-height: 1.2;
	}

	._countdown-icon {
		color: @color-main-highlight; /* theme */
		font-size: calc(@font-size-main ~'+' 10px); /* theme */
	}

	._countdown-timer {
		font-size: 0;
	}

	._countdown-timer-digit {
		font-size: calc(@font-size-main ~'+' 10px); /* theme */
		font-weight: bold;
	}
}

._product-details-parameters {
	background-color: @color-second-background; /* theme */
	padding: 20px 30px;
	margin-bottom: 25px;
	color: @color-second-text; /* theme */

	._label {
		display: block;
		margin-bottom: 2px;
		font-weight: bold;
		.uppercase();
	}

	._form-row {
		&:last-child {
			margin-bottom: 0;
		}
	}

	._button {
		padding-left: 10px;
		padding-right: 10px;
	}

	._medal-info {
		margin-top: 20px;
	}
}

._product-details-parameters-row {
	margin-bottom: 20px;

	&:last-child {
		margin-bottom: 0;
	}
}

._product-details-parameters-out-of-stock {
	display: block;
	margin-top: 10px;
	font-size: calc(@font-size-main ~'+' 4px); /* theme */
	color: @color-second-highlight; /* theme */
}

._parameter-radio-values {
	margin-bottom: -3px;
	.clearfix();
}

._parameter-radio-value {
	float: left;
	display: table;
	height: 36px;
	min-width: 44px;
	margin-right: 3px;
    margin-bottom: 3px;

    ._radio {
    	display: table-cell;
    	vertical-align: middle;
		border: 1px solid;
		border-color: @color-second-borders; /* theme */
		padding: 5px;
	    text-align: center;
	    font-weight: normal;
	    font-size: 13px;

        &.active {
        	border-width: 2px;
        	padding: 4px;
			border-color: @color-second-highlight; /* theme */
        }

        .radio {
        	display: none;
        }
    }
}

._parameter-image-values {
    margin-left: -1%;
    margin-bottom: -1%;
    .clearfix();
}

._parameter-image-value {
    float: left;
    width: 24%;
    max-width: 100px;
    border: 1px solid @color-second-borders; /* theme */
    margin-left: 1%;
    margin-bottom: 1%;
    word-break: break-word;

    &.active {
        border-width: 2px;
        border-color: @color-second-highlight; /* theme */
    }

    ._radio {
        display: block;
        height: 0;
        padding: 0;
        padding-bottom: 100%;
        font-size: calc(@font-size-main ~'-' 2px); /* theme */
        position: relative;
        overflow: hidden;

        .radio {
            top: 5px;
            left: 5px;
        }

        .radio-label {
            display: inline-block;
            padding-top: 4px;
            padding-left: 30px;
            padding-right: 5px;
        }

        img {
            .centerer(true, true);

            ~ .radio,
            ~ .radio-label {
                display: none;
            }
        }
    }
}

._parameter-color-values {
    margin-right: -5px;
    margin-bottom: -5px;
    .clearfix();
}

._parameter-color-value {
    float: left;
    margin-right: 5px;
    margin-bottom: 5px;

    &.active {
        ._radio {
			border-width: 2px;
            border-color: @color-second-highlight; /* theme */
        }
    }

    ._radio {
        display: block;
        width: 34px;
        height: 34px;
        border: 1px solid;
		color: @color-second-borders; /* theme */
        padding: 0;
        margin: 0;
        font-size: 0;
        line-height: 0;

        .radio {
            display: none;
        }

        .radio-label {
            display: none;
        }
    }
}

._parameter-table-value {
	._radio {
		&.active {
			background-color: @color-button-background; /* theme */
		}
	}
}

._form-row-actions {
	margin-bottom: 0;
	
	._form-col {
		vertical-align: top;
	}

	._form-col-button {
		vertical-align: bottom;
	}
}

._form-row-out-of-stock {
	display: none;
}

.quantity-left-js {
	margin-bottom: 0;

	._form-actions {
		position: relative;
	}
}

/*  Tabs
-------------------------------------------------------------------------------*/

._product-details-tabs-nav {
	padding: 0 30px;
	margin-bottom: -2px;
	font-size: calc(@font-size-main ~'+' 2px); /* theme */
	font-weight: bold;
	position: relative;
	z-index: 1;
	.uppercase();
}

._product-details-tabs-nav-link {
	display: inline-block;
	border-bottom: 2px solid;
	border-color: transparent; /* theme */
	padding: 0 10px 15px;
	margin-right: 20px;
	color: @color-main-text; /* theme */

	&:last-child {
		margin-right: 0;
	}

	@media @hover {
		&:hover {
			color: @color-main-titles; /* theme */
		}
	}

	&.active {
		border-color: @color-main-highlight; /* theme */
		color: @color-main-titles; /* theme */
	}
}

._product-details-tabs {
	border: 1px solid;
	border-color: @color-main-borders; /* theme */
	padding: 30px;
}

._product-details-tab {
	display: none;

	&:first-child {
		display: block;
	}

	._facebook-comments {
		margin-bottom: @separator;

		&:last-child {
			margin-bottom: 0;
		}
	}
}

._product-details-files,
._product-details-properties {
	ul {
		display: table;
		width: 100%;
		list-style-type: none;
	}

	li {
		display: table-row;
		color: @color-main-text; /* theme */

		&:nth-child(odd) {
			background-color: @color-second-background; /* theme */
			color: @color-second-text; /* theme */

			a {
				color: @color-second-text; /* theme */

				@media @hover {
					&:hover {
						color: @color-second-highlight; /* theme */
					}
				}
			}
		}
	}

	a {
		color: @color-main-text; /* theme */

		@media @hover {
			&:hover {
				color: @color-main-highlight; /* theme */
			}
		}
	}
}

._product-details-properties-title,
._product-details-properties-value,
._product-details-files-title,
._product-details-files-value {
	display: table-cell;
	vertical-align: top;
	padding: 8px 20px;
}

._product-details-files {
	a {
		&:before {
			content:"\f019";
			font-family: @font-awesome;
			margin-right: 10px;
		}
	}
}

._product-details-actions {
	font-size: 0;
	text-align: right;
}

._product-details-wishlist,
._product-details-compare,
._product-details-share {
	display: inline-block;
	vertical-align: top;
	margin-right: 10px;

	&:last-child {
		margin-right: 0;
	}
}

._button-share,
._product-details-wishlist-button,
._product-details-compare .checker > span {
	display: inline-block;
	background-color: transparent; /* theme */
	border: 1px solid;
	border-color: @color-product-icons; /* theme */
	width: @forms-height;
	height: @forms-height;
	line-height: @forms-height;
	text-align: center;
	color: @color-product-icons; /* theme */
	font-size: calc(@font-size-main ~'+' 4px); /* theme */
	transition: .2s;

	&:focus {
		color: @color-product-icons; /* theme */
	}

	@media (min-width: @screen-desktop) {
		&:hover {
			border-color: @color-main-highlight; /* theme */
			color: @color-main-highlight; /* theme */
		}
	}
}

._product-details-share.open ._button-share,
._product-details-wishlist-button.active,
._product-details-compare .checker > span.checked {
	background-color: transparent; /* theme */
	border-color: @color-product-icons-highlight; /* theme */
	color: @color-product-icons-highlight; /* theme */
}

._product-details-compare {
	._checkbox {
		padding: 0;
		font-size: 0;

		.checker {
			width: auto;
			height: auto;
			position: relative;
			margin: 0;

			> span {
				&:before {
					display: block;
					content:"\f021";
					font-family: @font-awesome;
					font-size: inherit;
					color: inherit; /* theme */
					position: static;
					top: 0;
					left: 0;
					transform: translate(0, 0);
				}

				input {
					display: none;
				}
			}
		}
	}
}

._product-details-share {
	position: relative;

	&.open {
		._product-details-share-dropdown {
			display: block;
		}
	}
}

._product-details-share-dropdown {
	display: none;
	background-color: @color-main-background; /* theme */
	border: 1px solid;
	border-color: @color-product-icons; /* theme */
	position: absolute;
	top: calc(100% ~'+' 13px);
	right: 0;
	font-size: 0;
	padding: 15px;

	&:before,
	&:after {
		content: '';
		position: absolute;
		bottom: 100%;
		right: 15px;
		border-left: 10px solid transparent;
		border-right: 10px solid transparent;
		border-bottom: 10px solid @color-main-background; /* theme */
		z-index: 1;
	}

	&:after {
		border-left-width: 12px;
		border-right-width: 12px;
		border-bottom-width: 12px;
		border-bottom-color: @color-product-icons; /* theme */
		right: 13px;
		z-index: 0;
	}

	a {
		display: inline-block;
		margin-bottom: 0 !important;
		float: none !important;
	}
}

/*  Sticky
-------------------------------------------------------------------------------*/

._product-details-sticky-container {
	background-color: @color-second-background; /* theme */
	border-bottom: 1px solid;
	border-color: @color-second-borders; /* theme */
	color: @color-second-text; /* theme */
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: @z-header-fixed - 1;
	transform: translate(0, -100%);
	transition: .5s;

	&.open {
		transform: translate(0, 0);
	}
}

._product-details-sticky {
	display: table;
	width: 100%;
}

._product-details-sticky-image,
._product-details-sticky-title,
._product-details-sticky-info {
	display: table-cell;
	vertical-align: middle;
}

._product-details-sticky-image {
	width: 40px;
	min-width: 40px;
	padding: 10px 0;
}

._product-details-sticky-image-thumb {
	display: block;
	background-color: @color-second-image-box; /* theme */
	box-shadow: inset 0 0 0 1px @color-second-borders; /* theme */
	padding-bottom: 100%;
	position: relative;

	img {
		max-width: calc(100% ~'-' 2px);
		max-height: calc(100% ~'-' 2px);
		.centerer(true, true);
	}
}

._product-details-sticky-title {
	width: 400px;
	padding: 10px 15px;
	color: @color-second-titles; /* theme */
}

._product-details-sticky-info {
	padding: 10px 0;
	text-align: right;
	white-space: nowrap;

	._product-details-price,
	._product-details-price-meta,
	._product-details-price-new,
	._product-details-sticky-button {
		display: inline-block;
		vertical-align: middle;
		margin-left: 23px;

		&:first-child {
			margin-left: 0;
		}
	}

	._product-details-price {
		width: auto;

		._product-details-stock-status-bar {
			display: none;
		}
	}

	._product-details-price-main {
		display: inline-block;
	}

	._product-details-price-meta {
		color: @color-second-meta-text; /* theme */
		line-height: @line-height-low;
		text-align: left;
	}

	._product-details-price-old {
		._product-details-price-value {
			color: @color-second-meta-text; /* theme */
		}
	}

	._product-details-price-saved {
		margin-bottom: 0;

		._product-details-price-value {
			color: @color-second-highlight; /* theme */
		}
	}

	._product-details-price-new {
		color: @color-second-highlight; /* theme */
		margin: 0;
	}
}

._product-details-image {
	.lightslider-image-holder {
		position: relative;
	}

	.lSSlideWrapper {
		overflow: visible;
	}

	.lSSlideOuter .lSPager.lSGallery li {
		max-width: initial;
		max-height: initial;
	}

    .lSSlideOuter .lSPager.lSGallery>li>a {
      display: block;
      position: relative;
      padding-bottom: 100%;
    }

	.lSSlideOuter .lSPager.lSGallery img {
      height: auto;
      margin: auto;
      max-height: 100%;
      max-width: 100%;
      width: auto;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
	}

	.lSAction > a {
		top: calc(100% ~'+' 65px);
	}

	.lSPrev,
	.lSNext {
		background: transparent;
		background-color: rgba(0, 0, 0, .5);
		background-image: none;
		font-size: 14px;
		width: 40px;
		height: 40px;
		color: #fff;
		position: absolute;
		z-index: 10;

		&:before {
			font-family: "Font Awesome 5 Pro";
			font-weight: bold;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
	}

	.lSAction {
		> .lSPrev {
			left: 0;

			&:before {
				content: '\f053';
			}
		}

		> .lSNext {
			right: 0;

			&:before {
				content: '\f054';
			}
		}
	}
}

._product-details-pictures {
	.slider {
		.lSSlideOuter {
			.lSPager{
				&.lSGallery {
					li {
						border-color: transparent;
					}
				}
			}
		}
	}
}
