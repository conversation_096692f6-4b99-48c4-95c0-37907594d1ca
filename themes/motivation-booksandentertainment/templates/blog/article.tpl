{$article = W::article()->getArticle(segment(2))}
{$blog = W::article()->getBlog()}
{if !empty($blog)}
    {W::blog()->handle($blog->url_handle)}
{/if}
{$widget->setSeo('article')}
{include file="../layout/header.tpl"}
<!-- BEGIN: content -->
<div class="_content">
    <div class="_breadcrumb-container _section-separator">
        <div class="container">
            <div class="row">
                <div class="col-sm-12">
                    {if !empty($blog)}
                        {$breadcrumbs = [['name' => "{t}sf.global.act.blog{/t}", "link" => route('blog.list')], ['name' => $blog->name, "link" => $blog->url()]]}
                    {else}
                        {$breadcrumbs = []}
                    {/if}
                    {include file="widgets/common/breadcrumbs.tpl" breadcrumbs=$breadcrumbs active=$article->name}
                </div>
            </div>
        </div>
    </div>
    
    {if W::blog()->isEnabled()}
        <div class="container">
            <div class="row">
                <div class="col-md-9 col-md-push-3">
                    {if $article->active == 'yes'}
                        <div class="_blog-main">
                            <div class="_blog-article">
                                {if $article->hasImage()}
                                    <div class="_blog-article-image">
                                        <img src="{noImageArticle('1280x1280')}" class="lazyload-image lazyload-{$article->getOrientation()}" data-src="{$article->getImage('1280x1280')}" alt="{$article->name}" title="{$article->name}">
                                    </div>
                                {/if}

                                <p class="_blog-article-time">{siteDateTime($article->created_at)}</p>

                                <div class="_blog-article-title">
                                    <h1 class="_h2">{$article->name}</h1>
                                </div>

                                <div class="_blog-article-text">
                                    <div class="_textbox">
                                        {$article->content nofilter}
                                    </div>
                                </div>

                                {if W::article()->commentsAllowed()}
                                    <div class="_secondary-title">
                                        <h3 class="_h2">{t}sf.article.header.article_comments{/t}</h3>
                                    </div>

                                    <div class="_blog-article-comments">
                                        <div data-ajax-box="{$article->url()}">
                                            {include file="widgets/blog/article/comments.tpl" widget=W::article()}
                                        </div>
                                    </div>

                                    <div class="_secondary-title">
                                        <h3 class="_h2">{t}sf.global.leave.a.comment{/t}</h3>
                                    </div>

                                    <div class="_blog-article-comments-form">
                                        {include file="widgets/blog/article/comment_form.tpl" widget=W::article()}
                                    </div>
                                {/if}
                            </div>
                        </div>
                    {else}
                        {include file="../notifications/error-include.tpl" error="{t}sf.widget.blog.article.err.article_no_longer_active{/t}"}
                    {/if}
                </div>

                <div class="col-md-3 col-md-pull-9">
                    {include file="./sidebar.tpl"}
                </div>
            </div>
        </div>
    {else}
        {include file="../notifications/error-include.tpl" error="{t}sf.widget.blog.err.blog_is_disabled{/t}"}
    {/if}
    <!-- LOAD: seo -->
    {include file="widgets/common/microdata/article.tpl" widget=W::article() article=$article blog=$blog}
    {include file="widgets/common/microdata/breadcrumb.tpl" breadcrumbs=$breadcrumbs active=$article->name}

</div><!--// END: content -->
{include file="../layout/footer.tpl"}