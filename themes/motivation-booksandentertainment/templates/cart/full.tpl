{capture "cart"}
	{if $cc_cart|default && $cc_cart->has_products}
		<div class="container _checkout-cart">
			<div class="_section-separator">
				<div class="row js-checkout-container">
					<div class="col-md-8">
						<div class="_section-title">
							<h1 class="_h2">{t}sf.cart.header.shopping_cart{/t}</h1>
						</div>
						
						<div class="_cart-main">
							{$css_classes = ['cart-product-out-of-stock' => 'glyphicon glyphicon-info-sign tooltips']}
							{$quantity_ui_options = json_encode(["verticalbuttons" => true, "verticalupclass" => "glyphicon glyphicon-plus", "verticaldownclass" => "glyphicon glyphicon-minus"])}
							{include file="widgets/store/cart/product_list.tpl" quantity_uicontrol="spinner" classes=$css_classes product_img_size="600x600"}
						</div>
					</div>

					<div class="col-md-4">
						<div class="_cart-sidebar js-checkout-sidebar" data-bottom-offset="80">
							<div class="_cart-summary">
								<div class="_section-title">
									<h3>{t}sf.cart.header.summary{/t}</h3>
								</div>

								{include file="widgets/store/cart/totals.tpl"}

								<div class="_cart-summary-actions">
									<div class="_form">
										<div class="_form-row">
											<div class="_form-col">
												{include file="widgets/store/cart/form_submit.tpl"}
											</div>
										</div>
									</div>
								</div>

								{if $widget->checkoutText->isEnabled()}
									<div class="_text-box _text-box-checkout">
										{include file="widgets/extra/text.tpl" widget=$widget->checkoutText}
									</div>
								{/if}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	{else}
		<div class="container">
			<div class="_section-separator">
				<div class="row">
					<div class="col-sm-12">
						<div class="_notification">
							<p>{t}sf.widget.cart.nfy.cart_is_empty{/t}</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	{/if}
{/capture}

{if Request::ajax()}
	{$smarty.capture.cart nofilter}
{else}
	{include file="../layout/header.tpl"}

	<!-- BEGIN: content -->
	{if !empty($smarty.get.email_unconfirmed)}
		{capture append="js"}
			<script type="text/javascript">
				$(function () {
					$('#ConfirmMailModalJs').modal('show');
				});
			</script>
		{/capture}
		{include file="./email-unconfirmed.tpl"}
	{/if}
	<div class="_content">
		<div class="_breadcrumb-container _section-separator">
			<div class="container">
				<div class="row">
					<div class="col-sm-12">
						{include file="widgets/common/breadcrumbs.tpl" active="{t}sf.global.act.cart{/t}"}
					</div>
				</div>
			</div>
		</div>
		
		<div class="cart-overview-js" data-ajax-box="{route('cart.list', $cc_cart_key)}" data-widget="cart" data-effect="populate">
			{$smarty.capture.cart nofilter}
		</div>
	</div>
	<!--// END: content -->
	{include file="../layout/footer.tpl"}
{/if}