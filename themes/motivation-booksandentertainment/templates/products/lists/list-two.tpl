{$category_childs = W::productsListing()->getNewCategoryChildren()}
{$list_class = ''}
{$list_horizontal_size = ''}
{$is_list_horizontal = false}
{if isset(Widget::get('filters')->getSetting('list_class'))}
    {$list_class = Widget::get('filters')->getSetting('list_class')}
{/if}
{if isset(Widget::get('filters')->getSetting('list_horizontal_size'))}
    {$list_horizontal_size = Widget::get('filters')->getSetting('list_horizontal_size')}
{/if}
{if Widget::get('filters')->getSetting('list_class') == '_list-horizontal'}
    {$is_list_horizontal = true}
{/if}
<!-- BEGIN: content -->

<div class="_content">
    <div class="_breadcrumb-container _section-separator">
        <div class="container">
            <div class="row">
                <div class="col-sm-12">
                    {include file="widgets/common/breadcrumbs.tpl" breadcrumbs=W::productsListing()->getBreadcrumbs() active=null}
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        {$category = W::filters()->getCategory()}
        {$vendor = W::filters()->getVendor()}
        {if !empty($category) || !empty($vendor)}
            <div class="row">
                <div class="col-md-12">
                    <div class="_products-list-head">
                        {if !empty($category)}
                            <div class="_products-list-head-item">
                                <div class="_products-list-head-item-title _section-title">
                                    <h1>{$category->name}</h1>
                                </div>
                            </div>
                        {/if}

                        {if !empty($vendor)}
                            <div class="_products-list-head-item">
                                <div class="_products-list-head-item-title _section-title">
                                    <h1>{$vendor->name}</h1>
                                </div>
                            </div>
                        {/if}
                    </div>
                </div>
            </div>
        {else}
            <div class="row">
                <div class="col-md-12">
                    <div class="_section-title">
                        <h1>
                            {if !empty(W::filters()->getSelection())}
                                {W::filters()->getSelection()->name}
                            {else}
                                {W::filters()->getProductsOrBundlesTitle()}
                            {/if}
                        </h1>
                    </div>
                </div>
            </div>
        {/if}
        {if $category_childs}
            <div class="row _section-separator">
                <div class="col-md-12">
                    <div class="_showcase-list">
                        {foreach from=$category_childs item=child}
                            <div class="_showcase-item one-third">
                                <a href="{$child->url()}" class="_showcase-item-image">
                                    <div class="_showcase-item-image-thumb">
                                        <img src="{$child->getImage('600x600')}" alt="{$child->name}" title="{$child->name}">
                                    </div>

                                    <div class="_showcase-item-info">
                                        <div class="_showcase-item-name">
                                            <span>{$child->name}</span>
                                        </div>

                                        <span class="_button _button-small">
												<span class="_figure-stack">
												<span class="_figure-stack-label">{t}sf.global.act.view_more{/t}</span>
												</span>
                                        </span>
                                    </div>
                                </a>
                            </div>
                        {/foreach}
                    </div>
                </div>
            </div>
        {/if}
        <div class="row _section-separator">
            <div class="col-md-12">
                <div class="_filters _filters-two">
                    <div class="_filters-left">
                        <div class="_sidebar-buttons _sidebar-buttons-products js-sidebar-buttons">
                            <a href="javascript:void(0)" class="_button _button-open js-sidebar-toggler">
								<span class="_figure-stack">
									<span class="_figure-stack-label">{t}sf.products.act.show_filters{/t}</span>
									<i class="_figure-stack-icon fa fa-chevron-right"></i>
								</span>
                            </a>

                            <a href="javascript:void(0)" class="_button _button-close js-sidebar-toggler">
								<span class="_figure-stack">
									<i class="_figure-stack-icon fa fa-chevron-left"></i>
									<span class="_figure-stack-label">{t}sf.products.act.hide_filters{/t}</span>
								</span>
                            </a>
                        </div>

                        {include file="../list-sidebar/list-sidebar-two.tpl"}
                    </div>

                    {if $widget->filters->shouldShowFilter('sort') || $widget->filters->shouldShowFilter('per_page_filter')}
                        <div class="_filters-right">
                            <div class="_filters-row">
                                {if $widget->filters->shouldShowFilter('sort') && !activeRoute('products.search')}
                                    <div class="_filters-col">
                                        {include file="widgets/product/listing/orderbysort_ajax.tpl"}
                                    </div>
                                {/if}

                                {if $widget->filters->shouldShowFilter('per_page_filter')}
                                    <div class="_filters-col _filters-col-small">
                                        {include file="widgets/product/listing/perpage_ajax.tpl"}
                                    </div>
                                {/if}
                            </div>
                        </div>
                    {/if}
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12 js-products-box">
                <div class="js-product-list">
                    <div class="js-loading-products loader-container hide">
						<span class="loader">
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
						</span>
                    </div>

                    <div class="_products-list-main js-products-container js-empty-on-ajax">
                        {if $is_list_horizontal}
                        {include file="./../../../widgets/product/list-horizontal.tpl" list_class=$list_class|default list_horizontal_size=$list_horizontal_size|default show_vendor_logo=$productListConfiguration->showManufacturerLogo() show_variants=$productListConfiguration->showVariants() utilities=W::utilities() products=W::productsListing()->getProducts() widget_products=$widget->products custom_labels=true product_bar=true image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                        {else}
                        {include file="./../../../widgets/product/list.tpl" list_class=$list_class|default list_horizontal_size=$list_horizontal_size|default show_vendor_logo=$productListConfiguration->showManufacturerLogo() show_variants=$productListConfiguration->showVariants() utilities=W::utilities() products=W::productsListing()->getProducts() widget_products=$widget->products custom_labels=true product_bar=true image_size='600x600' image_srcset=['1200' => '300x300', '992' => '300x300', '768' => '600x600', '320' => '600x600']}
                        {/if}
                        {include file="widgets/common/pagging-new.tpl" page=W::filters()->getPage() pages=W::productsListing()->getPages() link=W::productsListing()->getLink() elements=W::productsListing()->getPaginator() ajax_link=W::productsListing()->getPaggingLink()}
                    </div>
                    {if (!empty($category->description)) && W::filters()->getPage() == 1}
                        <div class="_products-list-head-item-body">
                            {if $category->hasImage()}
                                <div class="_products-list-head-item-image">
                                    <div class="_products-list-head-item-image-thumb">
                                        <img src="{noImageCategory("600x600")}" class="lazyload-image lazyload-{$category->getOrientation()}" data-src="{$category->getImage("600x600")}" alt="{$category->name} {t}sf.global.image{/t}" title="{$category->name}" />
                                    </div>
                                </div>
                            {/if}

                            {if !empty($category->description)}
                                <div class="_products-list-head-item-description _textbox">
                                    {$category->description nofilter}
                                </div>
                            {/if}
                        </div>
                    {/if}
                    {if (!empty($vendor->description)) && W::filters()->getPage() == 1}
                        <div class="_products-list-head-item-body">
                            {*{if $vendor->hasImage()}*}
                            <div class="_products-list-head-item-image">
                                <div class="_products-list-head-item-image-thumb">
                                    <img src="{noImageVendor("600x600")}" class="lazyload-image lazyload-{$vendor->getOrientation()}" data-src="{$vendor->getImage("600x600")}" alt="{$vendor->name} {t}sf.global.image{/t}" title="{$vendor->name}" />
                                </div>
                            </div>
                            {*{/if}*}

                            {if !empty($vendor->description)}
                                <div class="_products-list-head-item-description _textbox">
                                    {$vendor->description nofilter}
                                </div>
                            {/if}
                        </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>
    <!-- LOAD: seo -->
    {include file="widgets/common/microdata/breadcrumb.tpl" breadcrumbs=W::productsListing()->getBreadcrumbs() active=null}


</div><!--// END: content -->