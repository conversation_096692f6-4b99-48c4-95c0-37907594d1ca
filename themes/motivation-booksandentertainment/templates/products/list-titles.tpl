{$category = W::filters()->getCategory()}
{$vendor = W::filters()->getVendor()}

{if (!empty($category) && !empty($vendor)) || (empty($category) && empty($vendor))}
	<div class="_section-title">
		<h1 class="_h2">
			{if !empty(W::filters()->getSelection())}
				{W::filters()->getSelection()->name}
			{else}
				{W::filters()->getProductsOrBundlesTitle()}
			{/if}
		</h1>
	</div>
{else}
	{if !empty($category)}
		<div class="_section-title">
			<h1 class="_h2">{$category->name}</h1>
		</div>
	{/if}

	{if !empty($vendor)}
		<div class="_section-title">
			<h1 class="_h2">{$vendor->name}</h1>
		</div>
	{/if}
{/if}