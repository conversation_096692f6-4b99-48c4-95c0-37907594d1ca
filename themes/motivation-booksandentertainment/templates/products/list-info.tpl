{$category = W::filters()->getCategory()}
{$vendor = W::filters()->getVendor()}

{if (!empty($category->description) || !empty($vendor->description)) && W::filters()->getPage() == 1}
	<div class="_products-list-info">
		{if !empty($category->description)}
			<div class="_products-list-info-item">
				<div class="_products-list-info-item-body">
					{if $category->hasImage()}
						<div class="_products-list-info-item-image">
							<div class="_products-list-info-item-image-thumb">
								<img src="{noImageCategory("600x600")}" class="lazyload-image lazyload-{$category->getOrientation()}" data-src="{$category->getImage("600x600")}" alt="{$category->name} {t}sf.global.image{/t}" title="{$category->name}" />
							</div>
						</div>
					{/if}
					
					{if !empty($category->description)}
						<div class="_products-list-info-item-description _textbox">
							{$category->description nofilter}
						</div>
					{/if}
				</div>
			</div>
		{/if}

		{if !empty($vendor->description)}
			<div class="_products-list-info-item">
				<div class="_products-list-info-item-body">
					{if $vendor->hasImage()}
						<div class="_products-list-info-item-image">
							<div class="_products-list-info-item-image-thumb">
								<img src="{noImageVendor("600x600")}" class="lazyload-image lazyload-{$vendor->getOrientation()}" data-src="{$vendor->getImage("600x600")}" alt="{$vendor->name} {t}sf.global.image{/t}" title="{$vendor->name}" />
							</div>
						</div>
					{/if}

					{if !empty($vendor->description)}
						<div class="_products-list-info-item-description _textbox">
							{$vendor->description nofilter}
						</div>
					{/if}
				</div>
			</div>
		{/if}
	</div>
{/if}