<div class="_product-details product-details-js" data-product-id="{$product->id}">
    <form class="add-to-cart-form-js js-form-submit-ajax" action="{route('cart.add')}" method="POST">
        {if $product->is_bundle}
            <div class="row">
                <div class="col-md-12">
                    <div class="_product-details-title-bundle">
                        <h1 class="_h2 js-product-title">{$product->name}</h1>

                        {if !empty($product->short_description) && W::ProductsDetails('short_product_description')}
                            <div class="_product-details-short-description">
                                <div class="_textbox">
                                    {$product->short_description nofilter}
                                </div>
                            </div>
                        {/if}
                    </div>
                </div>
            </div>
        {/if}

        <div class="row js-product-details-container">
            {if $product->is_bundle}
                <div class="col-md-9">
                    {if $product->meta->get('show-image')}
                        <div class="row">
                            <div class="col-md-12">
                                {include file="./details-images.tpl"}
                            </div>
                        </div>
                    {/if}
                    {include file="./details-bundle.tpl"}
                </div>

                <div class="col-md-3">
            {else}
                <div class="col-md-6">
                    {include file="./details-images.tpl"}
                </div>

                <div class="col-md-6">
            {/if}

                {include file="./details-sidebar.tpl"}
            </div>
        </div>
    </form>
</div>