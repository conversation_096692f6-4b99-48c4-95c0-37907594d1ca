<div class="_sidebar _filters-one _sidebar-products js-sidebar js-sidebar-ajax js-fixed-sidebar">
	<div class="_sidebar-button">
		<a class="_button js-sidebar-toggle" href="#">{t}sf.products.act.hide_filters{/t}</a>
	</div>

	{$filters = \Illuminate\Support\Collection::make($widget->filters->sortFilters())->sort()}
	{foreach $filters as $filterKey => $filterSort}
		{call name="$filterKey"}
	{/foreach}

	{function name="sort"}
		{if $widget->filters->shouldShowFilter('sort') || $widget->filters->shouldShowFilter('per_page_filter')}
			<div class="_sidebar-box _sidebar-box-sort js-sidebar-box open">
				<div class="_sidebar-box-title js-sidebar-box-toggle">
					<h4>{t}sf.products.header.sorting{/t}</h4>
				</div>

				<div class="_sidebar-box-body">
					{if !activeRoute('products.search')}
						{include file="widgets/product/listing/orderbysort_ajax.tpl"}
					{/if}
					{include file="widgets/product/listing/perpage_ajax.tpl"}
				</div>
			</div>
		{/if}
	{/function}

	{function name="brand_model"}
		{if $widget->filters->shouldShowFilter('brand_model') && !empty($widget->filters->getBrandsAndModelsFilter()) && $widget->filters->getBrandsAndModelsFilter()->count()}
			<div class="_sidebar-box js-sidebar-box open" data-filter-box="brand_model">
				<div class="_sidebar-box-title js-sidebar-box-toggle">
					<h4>{t}sf.widget.brand_model.filter.title{/t}</h4>
				</div>

				<div class="_sidebar-box-body">
					{\Illuminate\Support\Facades\View::make("brand_model::widgets.product.listing.filter", ['data' => $widget->filters->getBrandsAndModelsFilter()]) nofilter}
				</div>
			</div>
		{/if}
	{/function}

	{function name="categories"}
		{if $categories|default && $widget->filters->shouldShowFilter('categories')}
			<div class="_sidebar-box js-sidebar-box open" data-filter-box="categories">
				<div class="_sidebar-box-title js-sidebar-box-toggle">
					<h4>{t}sf.products.header.category{/t}</h4>
				</div>

				<div class="_sidebar-box-body _sidebar-list">
					{include file="widgets/product/listing/categories.tpl" list=true collapsing=true}
				</div>
			</div>
		{/if}
	{/function}

	{function name="vendors"}
		{if $widget->filters->shouldShowFilter('vendors') && !$widget->filters->getVendors()->isEmpty()}
			<div class="_sidebar-box js-sidebar-box open" data-filter-box="vendors">
				<div class="_sidebar-box-title js-sidebar-box-toggle">
					<h4>{t}sf.products.header.vendors{/t}</h4>
				</div>

				<div class="_sidebar-box-body">
					{include file="widgets/product/listing/vendors.tpl" checkbox=true}
				</div>
			</div>
		{/if}
	{/function}

	{function name="new"}
		{if $widget->filters->shouldShowFilter('new') || $widget->filters->shouldShowFilter('sale')}
			<div class="_sidebar-box js-sidebar-box" data-filter-box="new">
				<div class="_sidebar-box-title js-sidebar-box-toggle">
					<h4>{t}sf.products.header.type{/t}</h4>
				</div>

				<div class="_sidebar-box-body">
					<div class="_filter-type">
						{if $widget->filters->shouldShowFilter('new')}
							{include file="widgets/product/listing/new.tpl" checkbox=true}
						{/if}

						{if $widget->filters->shouldShowFilter('sale')}
							{include file="widgets/product/listing/sale.tpl" checkbox=true}
						{/if}
					</div>
				</div>
			</div>
		{/if}
	{/function}

	{function name="price_ranges"}
		{if $widget->filters->shouldShowFilter('price_ranges') && !empty($widget->filters->getPriceRanges())}
			<div class="_sidebar-box js-sidebar-box" data-filter-box="price_ranges">
				<div class="_sidebar-box-title js-sidebar-box-toggle">
					<h4>{t}sf.products.header.price{/t}</h4>
				</div>

				<div class="_sidebar-box-body">
					{include file="widgets/product/listing/price_ranges.tpl"}
				</div>
			</div>
		{/if}
	{/function}

	{function name="category_properties"}
		{if $widget->filters->shouldShowFilter('category_properties') && !W::categoryProperties()->getForCurrentCategory()->isEmpty()}
			{include file="widgets/product/listing/category_properties.tpl"}
		{/if}
	{/function}

	{function name="variants"}
		{if $widget->filters->shouldShowFilter('variants') && !$widget->filters->getVariants()->isEmpty()}
			{include file="widgets/product/listing/variants.tpl"}
		{/if}
	{/function}
</div>
