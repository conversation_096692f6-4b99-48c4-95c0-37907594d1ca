/*=============================================================================*\
    NAVIGATION
\*=============================================================================*/

/*  MAIN
-------------------------------------------------------------------------------*/

@nav-font-size: 13px;
@nav-line-height: @line-height-low;
@nav-bullet-size: 4px;
@nav-a-horizontal-padding: 20px;
@nav-a-vertical-padding: 18px;

._navbar {
  background-color: @color-navigation-background; /* theme */
  border-top: 1px solid;
  border-color: @color-header-borders; /* theme */
  position: relative;
  z-index: @z-navbar;

  &.no-slider {
    border-bottom-width: 1px;
    border-bottom-style: solid;
  }
}

._navigation-main-list-item-link {
  display: block;
  color: @color-navigation-text; /* theme */
  padding: @nav-a-vertical-padding @nav-a-horizontal-padding;
  transition: .2s;

  &:focus {
    color: @color-navigation-text; /* theme */
  }
}

._navigation-dropdown-list-item {
  font-size: @nav-font-size;

  @media @hover {
    > a {
      &:hover {
        color: @color-dropdowns-highlight; /* theme */
      }

      &:focus {
        color: @color-dropdowns-highlight; /* theme */
      }
    }
  }

  &:last-child {
    border-bottom: 0;
  }
}

._navigation-dropdown-list-item-link {
  display: block;
  color: @color-dropdowns-text; /* theme */

  &:focus {
    color: @color-dropdowns-text; /* theme */
  }
}



@import 'navigations/navigation-type-dropdown';
@import 'navigations/navigation-type-megamenu';
@import 'navigations/navigation-type-megamenu-two';
@import 'navigations/navigation-type-megamenu-header-four';
@import 'navigations/navigation-type-vertical-menu';
@import 'navigations/navigation-type-megamenu-click';

._navbar {
  ._navigation-main-list-item {
    @media (min-width: (@screen-lg + 1)) {
      > a {
        position: relative;
        &:after {
          content: '';
          display: block;
          width: 0;
          height: 2px;
          background: @color-navigation-hover-text; /* theme */
          position: absolute;
          bottom: 0;
          left: 0;
          transition: width .3s;
        }
      }
      &:hover {
        > a {
          &:after {
            width: 100%;
          }
        }
      }
    }
  }
}
._navigation {
  li {
    .uppercase();
  }
  &-dropdown {
    ._navigation-dropdown {
      li {
        text-transform: none;
      }
    }
  }
  &-hamburger {
    width: 23px;

    span {
      background-color: @color-header-icons; /* theme */
      display: block;
      height: 2px;
      position: relative;
      transition: transform .5s ease-in-out, opacity .5s ease-in-out;

      &:nth-child(1) {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
      }

      &:nth-child(2) {
        margin: 5px 0;
      }

      &:nth-child(3) {
        -webkit-transform-origin: 0 0;
        transform-origin: 0 0;
      }
    }

    &.active {
      span {
        &:nth-child(1) {
          top: -1px;
          left: 2px;
          -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
        }

        &:nth-child(2) {
          opacity: 0;
        }

        &:nth-child(3) {
          top: 1px;
          -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
        }
      }
    }
  }
}

._navigation {
  ul {
    list-style: none;
  }
  ._navigation {
    &-hamburger {
      display: none;
    }
  }
}

._navbar-mobile-button {
  ._navigation {
    &-hamburger {
      display: block;
    }
  }
}

/*  LINKS
-------------------------------------------------------------------------------*/

._navigation-links {
  display: table-cell;
  vertical-align: middle;

  ul {
    list-style-type: none;
    border-width: 0;
    border-left-width: 1px;
    border-style: solid;
    border-color: @color-topbar-borders; /* theme */
    font-weight: 300;
    font-size: 0;
    line-height: 0;
    .uppercase();
  }

  li {
    font-size: calc(@font-size-main ~'-' 2px); /* theme */
    line-height: 36px;
    display: inline-block;
    border-width: 0;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-style: solid;
    border-color: @color-topbar-borders; /* theme */
    margin-bottom: -1px;
  }

  a {
    display: inline-block;
    color: @color-topbar-text; /* theme */
    padding: 0 20px;

    @media @hover {
      &:hover {
        text-decoration: none;
        color: @color-topbar-hovers; /* theme */
      }
    }
  }
}

/*  FOOTER
-------------------------------------------------------------------------------*/

._navigation-footer {
    ul {
        list-style-type: none;
	}

    .collapse-icon {
        display: none;
    }
}

._navigation-footer-list {
	margin: 0 -15px -30px;
    .clearfix();
}

._navigation-footer-list-item {
    float: left;
    width: 25%;
    padding: 0 15px;
    margin-bottom: 30px;

    &:nth-child(4n+1) {
        clear: both;
    }
}

._navigation-footer-list-item-link {
	._footer-title();

	&:focus {
		color: @color-footer-titles; /* theme */
	}
}

._navigation-footer-dropdown-list-item-link {
	color: @color-footer-text; /* theme */
}

._navigation-footer-list-item-link,
._navigation-footer-dropdown-list-item-link {
	@media @hover {
        &:hover {
            color: @color-footer-highlight; /* theme */
        }
    }
}

/*  FOOTER VARIANT TWO
-------------------------------------------------------------------------------*/
._footer-variant-two {
  ._navigation-footer {
    margin-bottom: -19px;

    > ul {
      > li {
        > a {
          display: inline-block;
          font-family: @font-family-main; /* theme */
          font-size: @font-size-main; /* theme */
          font-weight: bold;
        }
      }
    }

    ul {
      .clearfix();
      list-style-type: none;

      ul {
        margin-top: 8px;

        li {
          float: none;
          width: auto;
          padding-right: 0;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        a {
          color: @color-footer-text; /* theme */
          text-transform: initial;

          &:hover {
            color: @color-topbar-hovers; /* theme */
            text-decoration: none;
          }
        }
      }
    }

    li {
      float: left;
      width: 25%;
      padding-right: 30px;
      margin-bottom: 19px;

      &:nth-child(4n+1) {
        clear: both;
      }

      a {
        color: @color-footer-titles; /* theme */
        .uppercase();

        &:hover {
          color: @color-topbar-hovers; /* theme */
          text-decoration: none;
        }
      }
    }

    ._figure-stack-icon {
      display: none;
    }
  }
}

._type-megamenu-click {
  .back-to-prev-menu {
    font-size: 12px;
    position: absolute;
    left: 0;
    top: 0;
    padding: 15px 20px;
    text-transform: none;
    i {
      font-size: 12px;
    }
  }
}