[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 1, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-5", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и технически характеристики/повече информация чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 1, "image_id": 1, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 72400, "price_to": 72400, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 2, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 2, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 72400, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 1, "name": "Спирачни дискове AS/PRO", "parent_id": 1, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "spiracni-diskove-aspro-image_61c18e1cc4b3b.jpeg", "background": "#696969", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 1, "color_id": 44, "percents": 28, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 2, "color_id": 146, "percents": 8, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 3, "color_id": 26, "percents": 7, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 4, "color_id": 55, "percents": 6, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 5, "color_id": 20, "percents": 5, "R": 220, "G": 20, "B": 60, "hex": "#DC143C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 6, "color_id": 50, "percents": 5, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 305729}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 1, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 2, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 3, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-4", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и технически характеристики/повече информация чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 3, "image_id": 2, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 54200, "price_to": 54200, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 4, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 4, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 54200, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 2, "name": "Спирачни дискове AS/PRO-Copy", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "spiracni-diskove-aspro-copy-image_61c19484dee2f.jpeg", "background": "#F5F5F5", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 7, "color_id": 146, "percents": 21, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 8, "color_id": 44, "percents": 14, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 9, "color_id": 26, "percents": 8, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 10, "color_id": 50, "percents": 8, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 11, "color_id": 73, "percents": 6, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 12, "color_id": 55, "percents": 6, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 13, "color_id": 87, "percents": 6, "R": 128, "G": 0, "B": 0, "hex": "#800000"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 14, "color_id": 51, "percents": 5, "R": 248, "G": 248, "B": 255, "hex": "#F8F8FF"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 194240}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 3, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-3", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и технически характеристики/повече информация чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 2, "image_id": 3, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 82500, "price_to": 82500, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 6, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 6, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 82500, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 3, "name": "Спирачни дискове AS/PRO-Copy", "parent_id": 3, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "spiracni-diskove-aspro-copy-image_61c194d0021ab.jpeg", "background": "#696969", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 15, "color_id": 44, "percents": 23, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 16, "color_id": 55, "percents": 22, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 17, "color_id": 26, "percents": 18, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 18, "color_id": 130, "percents": 10, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 19, "color_id": 73, "percents": 8, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 20, "color_id": 146, "percents": 8, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 21, "color_id": 50, "percents": 6, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 348644}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 7, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 8, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 9, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 4, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-2", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и технически характеристики/повече информация чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 2, "image_id": 4, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 56600, "price_to": 56600, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 8, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 8, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 56600, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 4, "name": "Всмукателна помпа AIP-1255-Copy", "parent_id": 4, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "vsmu<PERSON>elna-pompa-aip-1255-copy-image_61c1950b88f45.jpeg", "background": "#696969", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 22, "color_id": 44, "percents": 22, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 23, "color_id": 130, "percents": 19, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 24, "color_id": 146, "percents": 13, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 25, "color_id": 50, "percents": 10, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 26, "color_id": 73, "percents": 9, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 27, "color_id": 38, "percents": 9, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 28, "color_id": 26, "percents": 8, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 184773}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 10, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 11, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 12, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 5, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola-1", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и технически характеристики/повече информация чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 3, "image_id": 5, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 97400, "price_to": 97400, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 10, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 10, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 97400, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 5, "name": "Спирачни дискове РS342-Copy", "parent_id": 5, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "spiracni-diskove-rs342-copy-image_61c195454b6a9.jpeg", "background": "#F08080", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 29, "color_id": 69, "percents": 14, "R": 240, "G": 128, "B": 128, "hex": "#F08080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 30, "color_id": 141, "percents": 10, "R": 255, "G": 99, "B": 71, "hex": "#FF6347"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 31, "color_id": 60, "percents": 8, "R": 205, "G": 92, "B": 92, "hex": "#CD5C5C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 32, "color_id": 135, "percents": 7, "R": 255, "G": 250, "B": 250, "hex": "#FFFAFA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 33, "color_id": 20, "percents": 6, "R": 220, "G": 20, "B": 60, "hex": "#DC143C"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 34, "color_id": 65, "percents": 6, "R": 255, "G": 240, "B": 245, "hex": "#FFF0F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 35, "color_id": 120, "percents": 6, "R": 255, "G": 0, "B": 0, "hex": "#FF0000"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 36, "color_id": 125, "percents": 5, "R": 250, "G": 128, "B": 114, "hex": "#FA8072"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 157621}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 13, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 14, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 15, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 6, "url_handle": "zaglavie-opredelyashto-vid-cvyat-i-material-na-produkta-preporachitelno-do-60-simvola", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и технически характеристики/повече информация чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 2, "image_id": 6, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 34400, "price_to": 34400, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 12, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 12, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 34400, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 6, "name": "Предна и задна броня-Copy", "parent_id": 6, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "predna-i-zadna-brona-copy-image_61c1958c8bd53.jpeg", "background": "#696969", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 37, "color_id": 44, "percents": 15, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 38, "color_id": 73, "percents": 10, "R": 211, "G": 211, "B": 211, "hex": "#D3D3D3"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 39, "color_id": 146, "percents": 9, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 40, "color_id": 124, "percents": 8, "R": 139, "G": 69, "B": 19, "hex": "#8B4513"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 41, "color_id": 130, "percents": 8, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 42, "color_id": 26, "percents": 7, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 43, "color_id": 50, "percents": 7, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 195226}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 16, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 17, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 18, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 7, "url_handle": "predn<PERSON>-farove", "name": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>Клиентът вече е видял краткото описание и следва детайлното да има подобна структира, но да предоставя повече информация. Тук може да се добавят и технически характеристики/повече информация чрез текст, стандартни видеа/360 градуса преглед и/или снимки. Работете с еднакъв шрифт и при необходимост подсилете цвета на някой думи, за да акцентирате върху тях. Не използвайте множество шрифтове, защото веднъж стигнал до продуктовата страница, клиентът вече обмисля купуването на артикула и не бива да го разсейвате/разколебавате с трудна за проследяване информация.</p>", "seo_title": "Заглавие определящо вид, цвят и материал на продукта (препоръчително до 60 символа)", "seo_description": "Важно е SEO описанието да е изградено на база предварителен анализ на потребителското търсене, изготвен от SEO специалист. Предвид пренаситения пазар и множеството реклами, правилният изказ е от изключително значение за дългосрочния успех на магазина ви.", "category_id": 1, "vendor_id": 1, "image_id": 7, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 55400, "price_to": 55400, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 14, "short_description": "<p>Представете краткото описание, чрез изброяване на ключови предимства, както и до две изречения обща информация. При създаване на текста е важно, да се замислим какъв проблем решава прoдуктът и какво го прави по-добър не само от тези на конкурентните фирми, но и от остналите ви артикули? Продуктовата страница е последната стъпка преди количката и вашият шанс, да затвърдите клиентското доверие, а това ще доведе и до завършена поръчка. Информацията задължително се поднася на достъпен за целевата ви група език, който да спазва сигурен и насърчителен тон.</p>", "featured": 0, "description_title": "", "type": "simple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 14, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 55400, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 7, "name": "Мас<PERSON><PERSON>н филтър TR7-Copy", "parent_id": 7, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "maslen-filtar-tr7-copy-image_61c195c29fb1d.jpeg", "background": "#2F4F4F", "width": 1000, "height": 1000, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 44, "color_id": 38, "percents": 22, "R": 47, "G": 79, "B": 79, "hex": "#2F4F4F"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 45, "color_id": 44, "percents": 19, "R": 105, "G": 105, "B": 105, "hex": "#696969"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 46, "color_id": 146, "percents": 18, "R": 245, "G": 245, "B": 245, "hex": "#F5F5F5"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 47, "color_id": 55, "percents": 12, "R": 128, "G": 128, "B": 128, "hex": "#808080"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 48, "color_id": 50, "percents": 6, "R": 220, "G": 220, "B": 220, "hex": "#DCDCDC"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 49, "color_id": 26, "percents": 5, "R": 169, "G": 169, "B": 169, "hex": "#A9A9A9"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 50, "color_id": 130, "percents": 5, "R": 192, "G": 192, "B": 192, "hex": "#C0C0C0"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 205742}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 19, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 20, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 21, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}]