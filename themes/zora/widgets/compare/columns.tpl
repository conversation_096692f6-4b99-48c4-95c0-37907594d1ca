{if isset($image_size)}
    {$image_size = $image_size}
{else}
    {$image_size = '300x300'}
{/if}
<div class="_compare-columns">
    <div class="_compare-columns-products">
        <div class="table-responsive">
            <table class="js-compare-boxes-holder">
                <tr class="_compare-columns-products-list">
                    <th>{t}sf.global.act.products{/t}</th>
                    {foreach from=$products item=$product}
                    <td class="js-compare-box-item js-compare-box-item-{$product->id}">
                        <div class="_product">
                            <div class="_product-inner">
                                <div class="_product-image">
                                    <a href="{$product->url()}" title="{$product->name}|{$product->category->name|default}" class="_product-image-thumb">
                                        <span class="_product-image-thumb-holder">
                                            <img src="{$product->getImage($image_size)}" alt="{$product->name}" title="{$product->name}">
                                        </span>
                                    </a>
                                    <a href="javascript:void(0);" class="_remove js-remove-compare-box" data-id="{$product->id}">x</a>
                                </div>
                                <div class="_product-info">
                                    <div class="_product-name">
                                        <h3>
                                            <a href="{$product->url()}" title="{$product->name}|{$product->category->name|default}">{$product->name}</a>
                                        </h3>
                                    </div>
                                    <div class="_product-options">
                                        <div class="_product-price">
                                            {if !empty($product->price_from_discounted_formatted)}
                                                {$product->price_from_discounted_formatted}
                                            {else}
                                                {$product->price_from_formatted}
                                            {/if}
                                            {if isset($product->discount) && $product->discount}
                                                <span {if $product->discount->style}style="{$product->discount->style}"{/if} class="_product-discount _product-discount-{$product->discount->type}" title="{t}sf.widget.product.tip.discount_target{/t}: {$product->discount->target_type}"> - {$product->discount->type_value_formatted}</span>
                                            {/if}
                                        </div>
                                        <div class="_product-price-discounted">
                                            {if !empty($product->price_from_discounted_formatted)}
                                                <del>{$product->price_from_formatted}</del> &nbsp; {t}sf.global.label.you_save{/t}: <span class="_product-price-saved">{$product->price_from_saved_formatted}</span>
                                            {/if}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                    {/foreach}
                </tr>
                <tr>
                    <th>{t}sf.global.act.vendor{/t}</th>
                    {foreach from=$products item=$product}
                    <td class="js-compare-box-item-{$product->id}">{if $product->vendor}{$product->vendor->name}{/if}</td>
                    {/foreach}
                </tr>
                {foreach from=$compare_groups item=group}
                <tr>
                    <th>{$group}</th>
                    {foreach from=$products item=$product}
                        <td class="js-compare-box-item-{$product->id}">
                            {if $compares->has($product->id) && $compares->get($product->id)->has($group)}
                                {$compares->get($product->id)->get($group)->implode(', ')}
                            {/if}
                        </td>
                    {/foreach}
                </tr>
                {/foreach}

                <tr class="_compare-columns-products-actions">
                    <th>&nbsp;</th>
                    {foreach from=$products item=$product}
                    <td class="js-compare-box-item-{$product->id}">
                        <a class="_button _button-full" href="{$product->url()}" title="{$product->name}|{$product->category->name|default}">
                            <span class="_figure-stack">
                                <span class="_figure-stack-label">{t}sf.global.details{/t}</span>
                            </span>
                        </a>
                    </td>
                    {/foreach}
                </tr>
            </table>
        </div>
    </div>
</div>