{"version": 3, "sources": ["docs/assets/css/ie10-viewport-bug-workaround.css", "docs/assets/css/src/pygments-manni.css", "docs/assets/css/src/docs.css"], "names": [], "mappings": ";;;;AAUA,cAAoB,MAAO,aAC3B,aAAoB,MAAO,aAC3B,UAAoB,MAAO,aCZ3B,KAAO,iBAAkB,KAEzB,GAAK,MAAO,KACZ,KAAO,MAAO,KAAS,iBAAkB,KACzC,GAAK,MAAO,KACZ,GAAK,MAAO,KACZ,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,iBAAkB,KAAS,OAAQ,IAAI,MAAM,KACnD,IAAM,WAAY,OAClB,IAAM,MAAO,IACb,IAAM,MAAO,KACb,IAAM,iBAAkB,KAAS,OAAQ,IAAI,MAAM,KACnD,IAAM,MAAO,KACb,IAAM,MAAO,KAEb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,GAAK,MAAO,KACZ,GAAK,MAAO,QACZ,IAAM,MAAO,QACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,QACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,GAAK,MAAO,KACZ,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KAAS,WAAY,OAClC,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KACb,IAAM,MAAO,KAIb,aAFA,QACA,YACiB,MAAO;;;;;AC7CxB,KACE,SAAU,SAIZ,YACE,UAAW,KACX,YAAa,IAIf,QACA,QACA,QACE,iBAAkB,QAIpB,aACE,MAAO,QACP,iBAAkB,YAClB,aAAc,QAIhB,oBADA,mBADA,mBAGE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAIhB,qBACE,MAAO,KACP,iBAAkB,YAClB,aAAc,QAIhB,4BADA,2BADA,2BAGE,MAAO,QACP,YAAa,KACb,iBAAkB,KAClB,aAAc,KAIhB,kBACE,QAAS,MACT,YAAa,IACb,MAAO,KACP,WAAY,OACZ,OAAQ,QACR,iBAAkB,QAClB,cAAe,IAEjB,qBACE,MAAO,KACP,OAAQ,KACR,UAAW,KACX,YAAa,KAEf,qBACE,MAAO,MACP,OAAQ,MACR,UAAW,MACX,YAAa,MAEf,0BACE,MAAO,QACP,iBAAkB,KAEpB,0BACE,iBAAkB,YAClB,OAAQ,IAAI,MAAM,QAWpB,QACE,QAAS,MACT,QAAS,IACT,MAAO,KACP,iBAAkB,QAClB,QAAS,EAGX,uBACE,QAAS,KACT,QAAS,IAAI,OAGf,eACE,QAAS,EAUX,aACE,cAAe,EACf,iBAAkB,KAClB,cAAe,EAEjB,uBACE,QAAS,KAEX,2BACA,8BACE,YAAa,IACb,MAAO,QAGT,mCACA,yCAFA,oCAGE,MAAO,QACP,iBAAkB,QAEpB,sCACE,iBAAkB,QAEpB,2CACE,aAAc,KAGhB,iDADA,iDAEE,iBAAkB,QAClB,aAAc,QAUhB,gBACE,YAAa,KACb,eAAgB,KAChB,WAAY,MACZ,MAAO,QACP,WAAY,OACZ,iBAAkB,QAEpB,kBACE,MAAO,KAET,sBACE,aAAc,EACd,cAAe,KAEjB,yBACE,QAAS,aAEX,4BACE,YAAa,KAGf,yBACE,gBACE,WAAY,KAEd,kBACE,cAAe,GAanB,gBADA,kBAEE,SAAU,SACV,QAAS,KAAK,EACd,MAAO,QACP,WAAY,OACZ,YAAa,EAAE,IAAI,EAAE,eACrB,iBAAkB,QAClB,iBAAkB,wEAClB,iBAAkB,oDAClB,iBAAiB,+CACjB,iBAAiB,kDACjB,OAAQ,2GACR,kBAAmB,SAIrB,oCACE,OAAQ,EAAE,KAAK,KAEjB,qBACE,YAAa,IACb,YAAa,EACb,MAAO,KAET,wBACE,OAAQ,EAAE,KAAK,KACf,UAAW,KACX,MAAO,KAET,2BACE,WAAY,MACZ,cAAe,KACf,MAAO,QAET,uBACE,MAAO,KACP,QAAS,KAAK,KACd,UAAW,KAGb,yBACE,uBACE,MAAO,MAIX,yBACE,kBACE,QAAS,KAAK,EAEhB,qBACE,UAAW,KAEb,wBACE,UAAW,MAIf,yBACE,wBACE,MAAO,IACP,UAAW,MAYf,gBACE,cAAe,KACf,UAAW,KAEb,mBACE,WAAY,EACZ,MAAO,KAET,kBACE,cAAe,EACf,YAAa,IACb,YAAa,IAEf,2BACE,SAAU,SAGZ,yBACE,gBACE,YAAa,KACb,eAAgB,KAChB,UAAW,KACX,WAAY,KAEd,mBACE,UAAW,KACX,YAAa,GAIjB,yBACE,mBACA,kBACE,aAAc,OAYlB,UACE,MAAO,eACP,OAAQ,eACR,QAAS,eACT,OAAQ,KAAK,MAAM,gBACnB,SAAU,OACV,UAA6B,eAC7B,YAAa,eACb,WAAY,KACZ,WAAY,cACZ,OAAQ,MAAM,kBACd,aAAc,IAAI,YAEpB,cACE,OAAQ,YAGV,cADA,eAEE,QAAS,gBACT,MAAO,eACP,MAAO,eACP,OAAQ,eACR,YAAa,gBACb,YAAa,iBAAkB,UAAW,MAAO,qBAEnD,eACE,YAAa,YAEf,cACE,MAAO,kBACP,WAAY,eAGd,gBADA,iBAEE,MAAO,eAET,wBACE,QAAS,KAGX,yBACE,UACE,MAAO,gBACP,OAAQ,KAAK,eACb,aAAc,cACd,cAAe,IAEjB,4BACE,OAAQ,KAAK,KAAK,aAItB,yBACE,UACE,aAAc,YACd,YAAa,aAIjB,yBACE,UACE,SAAU,SACV,IAAK,EACL,MAAO,KACP,MAA+G,gBAC/G,QAAS,eACT,OAAQ,YAEV,4BACE,SAAU,QAWd,oBACE,YAAa,KACb,eAAgB,KAChB,UAAW,KACX,YAAa,IACb,MAAO,KACP,WAAY,OACZ,iBAAkB,KAClB,cAAe,IAAI,MAAM,QAE3B,oCACE,WAAY,EACZ,WAAY,EAGd,0BACE,cAAe,IACf,UAAW,KACX,YAAa,IACb,MAAO,KAET,WACE,MAAO,MACP,OAAQ,KAAK,KAEf,uBACE,cAAe,IACf,YAAa,IACb,MAAO,KAET,wBACE,QAAS,MACT,cAAe,KACf,MAAO,KAET,8BACE,MAAO,QACP,gBAAiB,KAEnB,4BACE,QAAS,MACT,cAAe,KAGjB,yBACE,oCACE,WAAY,MAGhB,yBACE,oBACE,YAAa,MACb,eAAgB,MAElB,0BACE,UAAW,KAEb,0BACE,UAAW,IACX,aAAc,KACd,YAAa,KAEf,oCACE,WAAY,GAWhB,wBACE,aAAc,KACd,YAAa,KAEf,kCACE,QAAS,IAEX,wCACE,WAAY,EAGd,yBACE,kDACE,uBAAwB,IACxB,0BAA2B,IAE7B,iDACE,wBAAyB,IACzB,2BAA4B,KAWhC,wBACE,cAAe,KAEjB,gBACE,cAAe,IAEjB,eACE,cAAe,KAGjB,yBACE,aACE,aAAc,MACd,YAAa,MAEf,2BACE,cAAe,KACf,aAAc,MAalB,uBACE,SAAU,OAEZ,yBACE,iBACE,aAAc,MAKlB,iBACE,WAAY,KACZ,cAAe,KAIjB,2BACE,QAAS,MACT,QAAS,IAAI,KACb,UAAW,KACX,YAAa,IACb,MAAO,QAGT,iCADA,iCAEE,aAAc,KACd,MAAO,QACP,gBAAiB,KACjB,iBAAkB,YAClB,YAAa,IAAI,MAAM,QAIzB,sCADA,sCADA,gCAGE,aAAc,KACd,YAAa,IACb,MAAO,QACP,iBAAkB,YAClB,YAAa,IAAI,MAAM,QAIzB,2BACE,QAAS,KACT,eAA6N,KAE/N,gCACE,YAAa,IACb,eAAgB,IAChB,aAAc,KACd,UAAW,KACX,YAAa,IAGf,sCADA,sCAEE,aAAc,KAIhB,2CADA,2CADA,qCAGE,aAAc,KACd,YAAa,IAIf,aACA,sBACE,QAAS,KACT,QAAS,IAAI,KACb,WAAY,KACZ,YAAa,KACb,UAAW,KACX,YAAa,IACb,MAAO,KAET,mBACA,4BACE,MAAO,QACP,gBAAiB,KAEnB,sBACE,WAAY,EAGd,yBACE,aACA,sBACE,QAAS,OAKb,yBACE,iCACE,QAAS,MAGX,uBACA,8BACE,MAAO,MAET,uBACE,SAAU,MACV,IAA6X,KAE/X,8BACE,SAAU,SAGZ,wCADA,+CAEE,WAAY,EACZ,cAAe,GAGnB,0BAGE,uBADA,8BAEE,MAAO,OAYX,iBACE,cAAe,KAEjB,4BACE,cAAe,EAGjB,OACE,YAAa,KACb,WAAY,EAYd,YACE,QAAS,KACT,OAAQ,KAAK,EACb,OAAQ,IAAI,MAAM,KAClB,kBAAmB,IACnB,cAAe,IAEjB,eACE,WAAY,EACZ,cAAe,IAEjB,yBACE,cAAe,EAEjB,iBACE,cAAe,IAIjB,wBACE,WAAY,KAId,mBACE,kBAAmB,QAErB,sBACE,MAAO,QAET,oBACE,kBAAmB,QAErB,uBACE,MAAO,QAET,iBACE,kBAAmB,QAErB,oBACE,MAAO,QAUT,gBACE,OAAQ,EAAE,KACV,SAAU,OAEZ,cACE,MAAO,KACP,MAAO,KACP,OAAQ,KACR,OAAQ,EAAE,IACV,cAAe,IAGjB,yBACE,cACE,MAAO,MACP,OAAQ,OAKZ,6BACE,iBAAkB,KAEpB,2BACE,iBAAkB,KAEpB,sBACE,iBAAkB,KAEpB,4BACE,iBAAkB,KAEpB,8BACE,iBAAkB,KAEpB,+BACE,iBAAkB,QAEpB,+BACE,iBAAkB,QAEpB,+BACE,iBAAkB,QAEpB,8BACE,iBAAkB,QAEpB,4BACE,iBAAkB,QAIpB,2BACE,iBAAkB,QAEpB,iCACE,iBAAkB,QAEpB,mCACE,iBAAkB,QAEpB,yBACE,iBAAkB,QAUpB,sBACE,YAAa,KACb,MAAO,KAET,4BACE,MAAO,KACP,gBAAiB,KAEnB,qBACE,MAAO,MACP,MAAO,MACP,OAAQ,KACR,WAAY,IACZ,OAAQ,KAEV,aACE,MAAO,KACP,MAAO,KACP,aAAc,KACd,cAAe,IAUjB,2BACE,cAAe,EAGjB,qCACE,MAAO,IAWT,WACE,cAAe,KAEjB,yBACE,YAAa,KACb,eAAgB,KAChB,iBAAkB,KAClB,iBAAkB,oBAClB,OAAQ,IAAI,MAAM,KAClB,OAAQ,IAAI,MAAM,mBAWpB,YACE,SAAU,SACV,QAAS,KAAK,KAAK,KACnB,OAAQ,EAAE,MAAM,KAChB,aAAc,QAAQ,KAAK,KAC3B,aAAc,MACd,aAAc,IAAI,EAClB,mBAAoB,MAAM,EAAE,IAAI,IAAI,gBAC5B,WAAY,MAAM,EAAE,IAAI,IAAI,gBAGtC,kBACE,SAAU,SACV,IAAK,KACL,KAAM,KACN,UAAW,KACX,YAAa,IACb,MAAO,QACP,eAAgB,UAChB,eAAgB,IAChB,QAAS,UAGX,0BACE,eAAgB,KAIlB,uBACA,uCACE,OAAQ,MAAM,MAAM,KACpB,aAAc,EAAE,EAAE,IAClB,cAAe,EAIjB,yBACE,YACE,aAAc,EACd,YAAa,EACb,iBAAkB,KAClB,aAAc,KACd,aAAc,IACd,cAAe,IAAI,IAAI,EAAE,EACzB,mBAAoB,KACZ,WAAY,KAEtB,uBACA,uCACE,WAAY,MACZ,aAAc,EACd,YAAa,EACb,aAAc,IACd,2BAA4B,IAC5B,0BAA2B,IAE7B,uBACE,cAAe,KAKnB,uBACE,MAAO,KAYT,8BAJA,qCAGA,kCAGA,mCAJA,+BAGA,8BAGA,iCACA,gDARA,8BAMA,6BARA,kCADA,0BAFA,yBACA,0BAaE,cAAe,EAEjB,qBACE,MAAO,KAIT,mCACE,MAAO,QACP,eAAgB,OAElB,2BACE,QAAS,KAAK,EACd,aAAc,KAEhB,0CACE,WAAY,EAEd,oBACA,oBACA,oBACA,oBACA,oBACA,oBACE,OAAQ,EAIV,yBACE,QAAS,KAIX,wBACA,yBACA,2BACE,OAAQ,IAIV,qCACE,iBAAkB,KAIpB,iBACA,uBACE,WAAY,IACZ,cAAe,IAEjB,sCACE,WAAY,KAKd,6DADA,kCAEE,WAAY,KAEd,8BACE,cAAe,KAEjB,kCACE,OAAQ,SAIV,wBACE,UAAW,MAIb,+BACE,cAAe,EAGjB,0BADA,uBAEE,QAAS,EACT,QAAS,EACT,SAAU,OAGZ,yCADA,sCAEE,YAAa,EAGf,+CADA,yCAEE,SAAU,SACV,aAAc,EACd,YAAa,EAEf,uBACE,eAAgB,KAElB,6BACE,IAAK,KACL,OAAQ,KAEV,yCACE,IAAK,KAEP,0BACE,YAAa,KAEf,+CACE,OAAQ,KAEV,kCACE,cAAe,EAEjB,yBAEE,+CADA,yCAEE,SAAU,UAKd,wBACE,WAAY,KACZ,cAAe,KAIjB,mBACE,WAAY,EAId,kBACE,iBAAkB,QAEpB,yBACE,SAAU,SACV,IAAK,KACL,MAAO,KACP,OAAQ,KACR,KAAM,KACN,QAAS,EACT,QAAS,MAEX,gCACE,KAAM,KACN,aAAc,KACd,YAAa,KAIf,uCACE,MAAO,KAET,qCACE,SAAU,OACV,QAAS,MACT,cAAe,IACf,MAAO,KAIT,2BACE,cAAe,KAIjB,qBACE,WAAY,OAEd,0BACE,WAAY,IACZ,cAAe,IAEjB,6BACE,SAAU,SACV,QAAS,aACT,OAAQ,KAAK,KACb,QAAS,EAIX,oBACE,eAAgB,KAChB,iBAAkB,QAEpB,6BACE,SAAU,SACV,QAAS,MACT,MAAO,KACP,MAAO,MACP,OAAQ,KAIV,mBACE,SAAU,SACV,OAAQ,MACR,WAAY,KACZ,SAAU,KAGZ,uCACE,UAAW,MAIb,uBACE,cAAe,EAKjB,yCADA,yCAEE,YAAa,OAGf,yCACE,MAAO,MAGT,2CACA,2CACE,MAAO,MAGT,2CACE,MAAO,KAST,WACE,QAAS,IAAI,KACb,cAAe,KACf,iBAAkB,QAClB,OAAQ,IAAI,MAAM,QAClB,cAAe,IAEjB,eACE,QAAS,EACT,WAAY,EACZ,cAAe,EACf,WAAY,OACZ,YAAa,OACb,iBAAkB,YAClB,OAAQ,EAEV,oBACE,UAAW,QACX,MAAO,KAET,gCACE,QAAS,aACT,cAAe,KAWjB,iCACE,YAAa,OAIf,mBACA,+BACE,QAAS,MACT,YAAa,IACb,MAAO,KAET,+BACE,YAAa,IAEf,yBACE,WAAY,OAEd,oCACE,MAAO,QACP,iBAAkB,kBAEpB,mCACE,MAAO,KACP,iBAAkB,kBAIpB,2BACE,WAAY,IAEd,qCACE,cAAe,KAEjB,gCACE,QAAS,MACT,QAAS,KAAK,KACd,UAAW,KACX,YAAa,IACb,YAAa,IACb,WAAY,OACZ,cAAe,IASjB,gCADA,gCADA,gCADA,gCADA,iCADA,iCADA,iCADA,iCAQE,MAAO,KACP,OAAQ,IAAI,MAAM,KASpB,uCADA,uCADA,uCADA,uCADA,wCADA,wCADA,wCADA,wCAQE,MAAO,QACP,iBAAkB,QAClB,OAAQ,IAAI,MAAM,QAUpB,eACE,OAAQ,EAAE,MAAM,KAChB,SAAU,OAEZ,oBACE,aAAc,EACd,WAAY,KAEd,kBACE,MAAO,KACP,MAAO,IACP,OAAQ,MACR,QAAS,KACT,UAAW,KACX,YAAa,IACb,WAAY,OACZ,iBAAkB,QAClB,OAAQ,IAAI,MAAM,KAEpB,0BACE,WAAY,IACZ,cAAe,KACf,UAAW,KAEb,gCACE,QAAS,MACT,WAAY,OACZ,UAAW,WAEb,wBACE,MAAO,KACP,iBAAkB,QAGpB,yBACE,eACE,aAAc,EACd,YAAa,EAEf,kBACE,MAAO,MACP,UAAW,MAaf,uBACE,MAAO,MACP,WAAY,KAId,qBACE,WAAY,KACZ,YAAa,IACb,MAAO,KAET,kBACE,YAAa,KACb,WAAY,EACZ,cAAe,IAEjB,kBACE,cAAe,EAEjB,kBACE,WAAY,KACZ,cAAe,EAEjB,8BACE,WAAY,EACZ,cAA0G,IAE5G,gCACE,YAAa,MAAO,OAAQ,SAAU,cAAe,UACrD,iBAAkB,QAEpB,2BACE,cAAe,IACf,UAAW,KAIb,oBACE,YAAa,IAIf,oCACE,QAAS,KAIX,qBACE,SAAU,MACV,IAAK,EACL,MAAO,EACP,KAAM,EACN,QAAS,KACT,QAAS,KAAK,EACd,MAAO,KACP,iBAAkB,QAClB,cAAe,IAAI,MAAM,QACzB,mBAAoB,MAAM,EAAE,IAAI,EAAE,sBAC1B,WAAY,MAAM,EAAE,IAAI,EAAE,sBAEpC,4BACE,WAAY,KACZ,UAAW,KAEb,uBACE,cAAe,EAEjB,gCACE,aAAc,IAEhB,yBACE,OAAQ,KAAK,EAAE,EACf,MAAO,KACP,iBAAkB,QAClB,aAAc,QACd,mBAAoB,MAAM,EAAE,IAAI,IAAI,gBAAiB,EAAE,IAAI,EAAE,qBACrD,WAAY,MAAM,EAAE,IAAI,IAAI,gBAAiB,EAAE,IAAI,EAAE,qBAG/D,aACE,SAAU,SACV,QAAS,KACT,cAAe,KACf,MAAO,KACP,WAAY,OACZ,OAAQ,IAAI,OAAO,KACnB,cAAe,IAEjB,4BACE,cAAe,IAEjB,qCACE,UAAW,KAEb,gBACE,MAAO,MAET,mBACE,cAAe,KACf,YAAa,IACb,MAAO,KAEqL,uBAC5L,OAAQ,QAEV,0BACE,cAAe,EAUjB,gBACE,QAAS,MACT,MAAO,KACP,cAAe,KACf,SAAU,OACV,MAAO,QACP,iBAAkB,QAClB,cAAe,IAIjB,eACE,QAAS,KAAK,EACd,WAAY,OAEd,8BACE,WAAY,IAAI,MAAM,KAExB,yBACE,MAAO,KACP,iBAAkB,QAIpB,kBACA,kBACE,WAAY,EACZ,cAAe,EAEjB,iCACE,aAAc,KACd,YAAa,KAIf,0BACE,MAAO,KACP,OAAQ,KACR,OAAQ,KAAK,KAAK,MAClB,YAAa,KACb,MAAO,KACP,cAAe,IAEjB,6BACE,iBAAkB,QAEpB,iCACE,iBAAkB,QAGpB,yBACE,eACE,QAAS,WACT,MAAO,GAET,8BACE,WAAY,EACZ,YAAa,IAAI,MAAM,KAEzB,kBACE,UAAW,MASf,gBACE,SAAU,SACV,QAAS,KAEX,eACE,SAAU,SACV,IAAK,EACL,MAAO,EACP,QAAS,GACT,QAAS,MACT,QAAS,IAAI,IACb,UAAW,KACX,MAAO,QACP,OAAQ,QACR,iBAAkB,KAClB,OAAQ,IAAI,MAAM,QAClB,cAAe,EAAE,IAAI,EAAE,IAEzB,qBACE,MAAO,KACP,iBAAkB,QAClB,aAAc,QAGhB,yBACE,gBACE,QAAS,MAEX,2CACE,IAAK,MACL,wBAAyB,GAO7B,eACE,MAAO,QAGT,yBACE,eACE,QAAS,MAIb,sBACE,QAAS,IACT,mBAAoB,MAAM,KAAK,OAC1B,cAAe,MAAM,KAAK,OACvB,WAAY,MAAM,KAAK,OAIjC,qBADA,4BAEE,gBAAiB,KACjB,QAAS,EAUX,cACE,aAAc,KACd,aAAoK,oBACpK,QAAS,EACT,QAAS,KAAK,SACd,mBAAsL,EAAE,EAAE,IAAI,oBACtL,WAAY,EAAE,EAAE,IAAI,oBAI9B,UACE,QAAS,MACT,QAAS,KAAK,KACd,YAAa,IACb,MAAO,KACP,WAAY,OACZ,iBAAkB,QAEpB,gBACE,MAAO,KACP,gBAAiB,KACjB,iBAAkB,QAIpB,aACE,cACE,QAAS", "sourcesContent": ["/*!\n * IE10 viewport hack for Surface/desktop Windows 8 bug\n * Copyright 2014-2015 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n */\n\n/*\n * See the Getting Started docs for more information:\n * http://getbootstrap.com/getting-started/#support-ie10-width\n */\n@-ms-viewport     { width: device-width; }\n@-o-viewport      { width: device-width; }\n@viewport         { width: device-width; }\n", ".hll { background-color: #ffffcc }\n /*{ background: #f0f3f3; }*/\n.c { color: #999; } /* Comment */\n.err { color: #AA0000; background-color: #FFAAAA } /* Error */\n.k { color: #006699; } /* Keyword */\n.o { color: #555555 } /* Operator */\n.cm { color: #999; } /* Comment.Multiline */ /* Edited to remove italics and make into comment */\n.cp { color: #009999 } /* Comment.Preproc */\n.c1 { color: #999; } /* Comment.Single */\n.cs { color: #999; } /* Comment.Special */\n.gd { background-color: #FFCCCC; border: 1px solid #CC0000 } /* Generic.Deleted */\n.ge { font-style: italic } /* Generic.Emph */\n.gr { color: #FF0000 } /* Generic.Error */\n.gh { color: #003300; } /* Generic.Heading */\n.gi { background-color: #CCFFCC; border: 1px solid #00CC00 } /* Generic.Inserted */\n.go { color: #AAAAAA } /* Generic.Output */\n.gp { color: #000099; } /* Generic.Prompt */\n.gs { } /* Generic.Strong */\n.gu { color: #003300; } /* Generic.Subheading */\n.gt { color: #99CC66 } /* Generic.Traceback */\n.kc { color: #006699; } /* Keyword.Constant */\n.kd { color: #006699; } /* Keyword.Declaration */\n.kn { color: #006699; } /* Keyword.Namespace */\n.kp { color: #006699 } /* Keyword.Pseudo */\n.kr { color: #006699; } /* Keyword.Reserved */\n.kt { color: #007788; } /* Keyword.Type */\n.m { color: #FF6600 } /* Literal.Number */\n.s { color: #d44950 } /* Literal.String */\n.na { color: #4f9fcf } /* Name.Attribute */\n.nb { color: #336666 } /* Name.Builtin */\n.nc { color: #00AA88; } /* Name.Class */\n.no { color: #336600 } /* Name.Constant */\n.nd { color: #9999FF } /* Name.Decorator */\n.ni { color: #999999; } /* Name.Entity */\n.ne { color: #CC0000; } /* Name.Exception */\n.nf { color: #CC00FF } /* Name.Function */\n.nl { color: #9999FF } /* Name.Label */\n.nn { color: #00CCFF; } /* Name.Namespace */\n.nt { color: #2f6f9f; } /* Name.Tag */\n.nv { color: #003333 } /* Name.Variable */\n.ow { color: #000000; } /* Operator.Word */\n.w { color: #bbbbbb } /* Text.Whitespace */\n.mf { color: #FF6600 } /* Literal.Number.Float */\n.mh { color: #FF6600 } /* Literal.Number.Hex */\n.mi { color: #FF6600 } /* Literal.Number.Integer */\n.mo { color: #FF6600 } /* Literal.Number.Oct */\n.sb { color: #CC3300 } /* Literal.String.Backtick */\n.sc { color: #CC3300 } /* Literal.String.Char */\n.sd { color: #CC3300; font-style: italic } /* Literal.String.Doc */\n.s2 { color: #CC3300 } /* Literal.String.Double */\n.se { color: #CC3300; } /* Literal.String.Escape */\n.sh { color: #CC3300 } /* Literal.String.Heredoc */\n.si { color: #AA0000 } /* Literal.String.Interpol */\n.sx { color: #CC3300 } /* Literal.String.Other */\n.sr { color: #33AAAA } /* Literal.String.Regex */\n.s1 { color: #CC3300 } /* Literal.String.Single */\n.ss { color: #FFCC33 } /* Literal.String.Symbol */\n.bp { color: #336666 } /* Name.Builtin.Pseudo */\n.vc { color: #003333 } /* Name.Variable.Class */\n.vg { color: #003333 } /* Name.Variable.Global */\n.vi { color: #003333 } /* Name.Variable.Instance */\n.il { color: #FF6600 } /* Literal.Number.Integer.Long */\n\n.css .o,\n.css .o + .nt,\n.css .nt + .nt { color: #999; }\n", "/*!\n * Bootstrap Docs (http://getbootstrap.com)\n * Copyright 2011-2016 Twitter, Inc.\n * Licensed under the Creative Commons Attribution 3.0 Unported License. For\n * details, see https://creativecommons.org/licenses/by/3.0/.\n */\n\n\n/*\n * Bootstrap Documentation\n * Special styles for presenting Bootstrap's documentation and code examples.\n */\n\n\n/*\n * Scaffolding\n *\n * Update the basics of our documents to prep for docs content.\n */\n\nbody {\n  position: relative; /* For scrollspy */\n}\n\n/* Keep code small in tables on account of limited space */\n.table code {\n  font-size: 13px;\n  font-weight: normal;\n}\n\n/* Inline code within headings retain the heading's background-color */\nh2 code,\nh3 code,\nh4 code {\n  background-color: inherit;\n}\n\n/* Outline button for use within the docs */\n.btn-outline {\n  color: #563d7c;\n  background-color: transparent;\n  border-color: #563d7c;\n}\n.btn-outline:hover,\n.btn-outline:focus,\n.btn-outline:active {\n  color: #fff;\n  background-color: #563d7c;\n  border-color: #563d7c;\n}\n\n/* Inverted outline button (white on dark) */\n.btn-outline-inverse {\n  color: #fff;\n  background-color: transparent;\n  border-color: #cdbfe3;\n}\n.btn-outline-inverse:hover,\n.btn-outline-inverse:focus,\n.btn-outline-inverse:active {\n  color: #563d7c;\n  text-shadow: none;\n  background-color: #fff;\n  border-color: #fff;\n}\n\n/* Bootstrap \"B\" icon */\n.bs-docs-booticon {\n  display: block;\n  font-weight: 500;\n  color: #fff;\n  text-align: center;\n  cursor: default;\n  background-color: #563d7c;\n  border-radius: 15%;\n}\n.bs-docs-booticon-sm {\n  width: 30px;\n  height: 30px;\n  font-size: 20px;\n  line-height: 28px;\n}\n.bs-docs-booticon-lg {\n  width: 144px;\n  height: 144px;\n  font-size: 108px;\n  line-height: 140px;\n}\n.bs-docs-booticon-inverse {\n  color: #563d7c;\n  background-color: #fff;\n}\n.bs-docs-booticon-outline {\n  background-color: transparent;\n  border: 1px solid #cdbfe3;\n}\n\n\n/*\n * Fancy skip link\n *\n * Make it look a bit less \"bare bones\"\n * Also includes focus suppression for the Chrome tabindex=\"-1\" workaround\n */\n\n#skippy {\n  display: block;\n  padding: 1em;\n  color: #fff;\n  background-color: #6f5499;\n  outline: 0;\n}\n\n#skippy .skiplink-text {\n  padding: .5em;\n  outline: 1px dotted;\n}\n\n#content:focus {\n  outline: none;\n}\n\n\n/*\n * Main navigation\n *\n * Turn the `.navbar` at the top of the docs purple.\n */\n\n.bs-docs-nav {\n  margin-bottom: 0;\n  background-color: #fff;\n  border-bottom: 0;\n}\n.bs-home-nav .bs-nav-b {\n  display: none;\n}\n.bs-docs-nav .navbar-brand,\n.bs-docs-nav .navbar-nav > li > a {\n  font-weight: 500;\n  color: #563d7c;\n}\n.bs-docs-nav .navbar-nav > li > a:hover,\n.bs-docs-nav .navbar-nav > .active > a,\n.bs-docs-nav .navbar-nav > .active > a:hover {\n  color: #463265;\n  background-color: #f9f9f9;\n}\n.bs-docs-nav .navbar-toggle .icon-bar {\n  background-color: #563d7c;\n}\n.bs-docs-nav .navbar-header .navbar-toggle {\n  border-color: #fff;\n}\n.bs-docs-nav .navbar-header .navbar-toggle:hover,\n.bs-docs-nav .navbar-header .navbar-toggle:focus {\n  background-color: #f9f9f9;\n  border-color: #f9f9f9;\n}\n\n\n/*\n * Footer\n *\n * Separated section of content at the bottom of all pages, save the homepage.\n */\n\n.bs-docs-footer {\n  padding-top: 50px;\n  padding-bottom: 50px;\n  margin-top: 100px;\n  color: #99979c;\n  text-align: center;\n  background-color: #2a2730;\n}\n.bs-docs-footer a {\n  color: #fff;\n}\n.bs-docs-footer-links {\n  padding-left: 0;\n  margin-bottom: 20px;\n}\n.bs-docs-footer-links li {\n  display: inline-block;\n}\n.bs-docs-footer-links li + li {\n  margin-left: 15px;\n}\n\n@media (min-width: 768px) {\n  .bs-docs-footer {\n    text-align: left;\n  }\n  .bs-docs-footer p {\n    margin-bottom: 0;\n  }\n}\n\n\n/*\n * Homepage\n *\n * Tweaks to the custom homepage and the masthead (main jumbotron).\n */\n\n/* Share masthead with page headers */\n.bs-docs-masthead,\n.bs-docs-header {\n  position: relative;\n  padding: 30px 0;\n  color: #cdbfe3;\n  text-align: center;\n  text-shadow: 0 1px 0 rgba(0,0,0,.1);\n  background-color: #6f5499;\n  background-image: -webkit-gradient(linear, left top, left bottom, from(#563d7c), to(#6f5499));\n  background-image: -webkit-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image:      -o-linear-gradient(top, #563d7c 0%, #6f5499 100%);\n  background-image:         linear-gradient(to bottom, #563d7c 0%, #6f5499 100%);\n  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#563d7c', endColorstr='#6F5499', GradientType=0);\n  background-repeat: repeat-x;\n}\n\n/* Masthead (headings and download button) */\n.bs-docs-masthead .bs-docs-booticon {\n  margin: 0 auto 30px;\n}\n.bs-docs-masthead h1 {\n  font-weight: 300;\n  line-height: 1;\n  color: #fff;\n}\n.bs-docs-masthead .lead {\n  margin: 0 auto 30px;\n  font-size: 20px;\n  color: #fff;\n}\n.bs-docs-masthead .version {\n  margin-top: -15px;\n  margin-bottom: 30px;\n  color: #9783b9;\n}\n.bs-docs-masthead .btn {\n  width: 100%;\n  padding: 15px 30px;\n  font-size: 20px;\n}\n\n@media (min-width: 480px) {\n  .bs-docs-masthead .btn {\n    width: auto;\n  }\n}\n\n@media (min-width: 768px) {\n  .bs-docs-masthead {\n    padding: 80px 0;\n  }\n  .bs-docs-masthead h1 {\n    font-size: 60px;\n  }\n  .bs-docs-masthead .lead {\n    font-size: 24px;\n  }\n}\n\n@media (min-width: 992px) {\n  .bs-docs-masthead .lead {\n    width: 80%;\n    font-size: 30px;\n  }\n}\n\n\n/*\n * Page headers\n *\n * Jumbotron-esque headers at the top of every page that's not the homepage.\n */\n\n/* Page headers */\n.bs-docs-header {\n  margin-bottom: 40px;\n  font-size: 20px;\n}\n.bs-docs-header h1 {\n  margin-top: 0;\n  color: #fff;\n}\n.bs-docs-header p {\n  margin-bottom: 0;\n  font-weight: 300;\n  line-height: 1.4;\n}\n.bs-docs-header .container {\n  position: relative;\n}\n\n@media (min-width: 768px) {\n  .bs-docs-header {\n    padding-top: 60px;\n    padding-bottom: 60px;\n    font-size: 24px;\n    text-align: left;\n  }\n  .bs-docs-header h1 {\n    font-size: 60px;\n    line-height: 1;\n  }\n}\n\n@media (min-width: 992px) {\n  .bs-docs-header h1,\n  .bs-docs-header p {\n    margin-right: 380px;\n  }\n}\n\n\n/*\n * Carbon ads\n *\n * Single display ad that shows on all pages (except homepage) in page headers.\n * The hella `!important` is required for any pre-set property.\n */\n\n.carbonad {\n  width: auto !important;\n  height: auto !important;\n  padding: 20px !important;\n  margin: 30px -15px -31px !important;\n  overflow: hidden; /* clearfix */\n  font-size: 13px !important;\n  line-height: 16px !important;\n  text-align: left;\n  background: transparent !important;\n  border: solid #866ab3 !important;\n  border-width: 1px 0 !important;\n}\n.carbonad-img {\n  margin: 0 !important;\n}\n.carbonad-text,\n.carbonad-tag {\n  display: block !important;\n  float: none !important;\n  width: auto !important;\n  height: auto !important;\n  margin-left: 145px !important;\n  font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif !important;\n}\n.carbonad-text {\n  padding-top: 0 !important;\n}\n.carbonad-tag {\n  color: inherit !important;\n  text-align: left !important;\n}\n.carbonad-text a,\n.carbonad-tag a {\n  color: #fff !important;\n}\n.carbonad #azcarbon > img {\n  display: none; /* hide what I assume are tracking images */\n}\n\n@media (min-width: 480px) {\n  .carbonad {\n    width: 330px !important;\n    margin: 20px auto !important;\n    border-width: 1px !important;\n    border-radius: 4px;\n  }\n  .bs-docs-masthead .carbonad {\n    margin: 50px auto 0 !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .carbonad {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n}\n\n@media (min-width: 992px) {\n  .carbonad {\n    position: absolute;\n    top: 0;\n    right: 15px; /* 15px instead of 0 since box-sizing */\n    width: 330px !important;\n    padding: 15px !important;\n    margin: 0 !important;\n  }\n  .bs-docs-masthead .carbonad {\n    position: static;\n  }\n}\n\n\n/*\n * Homepage featurettes\n *\n * Reasons to use Bootstrap, entries from the Expo, and more.\n */\n\n.bs-docs-featurette {\n  padding-top: 40px;\n  padding-bottom: 40px;\n  font-size: 16px;\n  line-height: 1.5;\n  color: #555;\n  text-align: center;\n  background-color: #fff;\n  border-bottom: 1px solid #e5e5e5;\n}\n.bs-docs-featurette + .bs-docs-footer {\n  margin-top: 0;\n  border-top: 0;\n}\n\n.bs-docs-featurette-title {\n  margin-bottom: 5px;\n  font-size: 30px;\n  font-weight: normal;\n  color: #333;\n}\n.half-rule {\n  width: 100px;\n  margin: 40px auto;\n}\n.bs-docs-featurette h3 {\n  margin-bottom: 5px;\n  font-weight: normal;\n  color: #333;\n}\n.bs-docs-featurette-img {\n  display: block;\n  margin-bottom: 20px;\n  color: #333;\n}\n.bs-docs-featurette-img:hover {\n  color: #337ab7;\n  text-decoration: none;\n}\n.bs-docs-featurette-img img {\n  display: block;\n  margin-bottom: 15px;\n}\n\n@media (min-width: 480px) {\n  .bs-docs-featurette .img-responsive {\n    margin-top: 30px;\n  }\n}\n@media (min-width: 768px) {\n  .bs-docs-featurette {\n    padding-top: 100px;\n    padding-bottom: 100px;\n  }\n  .bs-docs-featurette-title {\n    font-size: 40px;\n  }\n  .bs-docs-featurette .lead {\n    max-width: 80%;\n    margin-right: auto;\n    margin-left: auto;\n  }\n  .bs-docs-featurette .img-responsive {\n    margin-top: 0;\n  }\n}\n\n\n/*\n * Featured sites\n *\n * Homepage thumbnails from the Expo.\n */\n\n.bs-docs-featured-sites {\n  margin-right: -1px;\n  margin-left: -1px;\n}\n.bs-docs-featured-sites .col-xs-6 {\n  padding: 1px;\n}\n.bs-docs-featured-sites .img-responsive {\n  margin-top: 0;\n}\n\n@media (min-width: 768px) {\n  .bs-docs-featured-sites .col-sm-3:first-child img {\n    border-top-left-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-docs-featured-sites .col-sm-3:last-child img {\n    border-top-right-radius: 4px;\n    border-bottom-right-radius: 4px;\n  }\n}\n\n\n/*\n * Examples\n *\n * Linked docs examples.\n */\n\n.bs-examples .thumbnail {\n  margin-bottom: 10px;\n}\n.bs-examples h4 {\n  margin-bottom: 5px;\n}\n.bs-examples p {\n  margin-bottom: 20px;\n}\n\n@media (max-width: 480px) {\n  .bs-examples {\n    margin-right: -10px;\n    margin-left: -10px;\n  }\n  .bs-examples > [class^=\"col-\"] {\n    padding-right: 10px;\n    padding-left: 10px;\n  }\n}\n\n\n/*\n * Side navigation\n *\n * Scrollspy and affixed enhanced navigation to highlight sections and secondary\n * sections of docs content.\n */\n\n/* By default it's not affixed in mobile views, so undo that */\n.bs-docs-sidebar.affix {\n  position: static;\n}\n@media (min-width: 768px) {\n  .bs-docs-sidebar {\n    padding-left: 20px;\n  }\n}\n\n/* First level of nav */\n.bs-docs-sidenav {\n  margin-top: 20px;\n  margin-bottom: 20px;\n}\n\n/* All levels of nav */\n.bs-docs-sidebar .nav > li > a {\n  display: block;\n  padding: 4px 20px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #767676;\n}\n.bs-docs-sidebar .nav > li > a:hover,\n.bs-docs-sidebar .nav > li > a:focus {\n  padding-left: 19px;\n  color: #563d7c;\n  text-decoration: none;\n  background-color: transparent;\n  border-left: 1px solid #563d7c;\n}\n.bs-docs-sidebar .nav > .active > a,\n.bs-docs-sidebar .nav > .active:hover > a,\n.bs-docs-sidebar .nav > .active:focus > a {\n  padding-left: 18px;\n  font-weight: bold;\n  color: #563d7c;\n  background-color: transparent;\n  border-left: 2px solid #563d7c;\n}\n\n/* Nav: second level (shown on .active) */\n.bs-docs-sidebar .nav .nav {\n  display: none; /* Hide by default, but at >768px, show it */\n  padding-bottom: 10px;\n}\n.bs-docs-sidebar .nav .nav > li > a {\n  padding-top: 1px;\n  padding-bottom: 1px;\n  padding-left: 30px;\n  font-size: 12px;\n  font-weight: normal;\n}\n.bs-docs-sidebar .nav .nav > li > a:hover,\n.bs-docs-sidebar .nav .nav > li > a:focus {\n  padding-left: 29px;\n}\n.bs-docs-sidebar .nav .nav > .active > a,\n.bs-docs-sidebar .nav .nav > .active:hover > a,\n.bs-docs-sidebar .nav .nav > .active:focus > a {\n  padding-left: 28px;\n  font-weight: 500;\n}\n\n/* Back to top (hidden on mobile) */\n.back-to-top,\n.bs-docs-theme-toggle {\n  display: none;\n  padding: 4px 10px;\n  margin-top: 10px;\n  margin-left: 10px;\n  font-size: 12px;\n  font-weight: 500;\n  color: #999;\n}\n.back-to-top:hover,\n.bs-docs-theme-toggle:hover {\n  color: #563d7c;\n  text-decoration: none;\n}\n.bs-docs-theme-toggle {\n  margin-top: 0;\n}\n\n@media (min-width: 768px) {\n  .back-to-top,\n  .bs-docs-theme-toggle {\n    display: block;\n  }\n}\n\n/* Show and affix the side nav when space allows it */\n@media (min-width: 992px) {\n  .bs-docs-sidebar .nav > .active > ul {\n    display: block;\n  }\n  /* Widen the fixed sidebar */\n  .bs-docs-sidebar.affix,\n  .bs-docs-sidebar.affix-bottom {\n    width: 213px;\n  }\n  .bs-docs-sidebar.affix {\n    position: fixed; /* Undo the static from mobile first approach */\n    top: 20px;\n  }\n  .bs-docs-sidebar.affix-bottom {\n    position: absolute; /* Undo the static from mobile first approach */\n  }\n  .bs-docs-sidebar.affix-bottom .bs-docs-sidenav,\n  .bs-docs-sidebar.affix .bs-docs-sidenav {\n    margin-top: 0;\n    margin-bottom: 0;\n  }\n}\n@media (min-width: 1200px) {\n  /* Widen the fixed sidebar again */\n  .bs-docs-sidebar.affix-bottom,\n  .bs-docs-sidebar.affix {\n    width: 263px;\n  }\n}\n\n\n/*\n * Docs sections\n *\n * Content blocks for each component or feature.\n */\n\n/* Space things out */\n.bs-docs-section {\n  margin-bottom: 60px;\n}\n.bs-docs-section:last-child {\n  margin-bottom: 0;\n}\n\nh1[id] {\n  padding-top: 20px;\n  margin-top: 0;\n}\n\n\n/*\n * Callouts\n *\n * Not quite alerts, but custom and helpful notes for folks reading the docs.\n * Requires a base and modifier class.\n */\n\n/* Common styles for all types */\n.bs-callout {\n  padding: 20px;\n  margin: 20px 0;\n  border: 1px solid #eee;\n  border-left-width: 5px;\n  border-radius: 3px;\n}\n.bs-callout h4 {\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-callout p:last-child {\n  margin-bottom: 0;\n}\n.bs-callout code {\n  border-radius: 3px;\n}\n\n/* Tighten up space between multiple callouts */\n.bs-callout + .bs-callout {\n  margin-top: -5px;\n}\n\n/* Variations */\n.bs-callout-danger {\n  border-left-color: #ce4844;\n}\n.bs-callout-danger h4 {\n  color: #ce4844;\n}\n.bs-callout-warning {\n  border-left-color: #aa6708;\n}\n.bs-callout-warning h4 {\n  color: #aa6708;\n}\n.bs-callout-info {\n  border-left-color: #1b809e;\n}\n.bs-callout-info h4 {\n  color: #1b809e;\n}\n\n\n/*\n * Color swatches\n *\n * Color swatches and associated values for our grayscale and brand colors.\n */\n\n.color-swatches {\n  margin: 0 -5px;\n  overflow: hidden; /* clearfix */\n}\n.color-swatch {\n  float: left;\n  width: 60px;\n  height: 60px;\n  margin: 0 5px;\n  border-radius: 3px;\n}\n\n@media (min-width: 768px) {\n  .color-swatch {\n    width: 100px;\n    height: 100px;\n  }\n}\n\n/* Framework colors */\n.color-swatches .gray-darker {\n  background-color: #222;\n}\n.color-swatches .gray-dark {\n  background-color: #333;\n}\n.color-swatches .gray {\n  background-color: #555;\n}\n.color-swatches .gray-light {\n  background-color: #999;\n}\n.color-swatches .gray-lighter {\n  background-color: #eee;\n}\n.color-swatches .brand-primary {\n  background-color: #337ab7;\n}\n.color-swatches .brand-success {\n  background-color: #5cb85c;\n}\n.color-swatches .brand-warning {\n  background-color: #f0ad4e;\n}\n.color-swatches .brand-danger {\n  background-color: #d9534f;\n}\n.color-swatches .brand-info {\n  background-color: #5bc0de;\n}\n\n/* Docs colors */\n.color-swatches .bs-purple {\n  background-color: #563d7c;\n}\n.color-swatches .bs-purple-light {\n  background-color: #c7bfd3;\n}\n.color-swatches .bs-purple-lighter {\n  background-color: #e5e1ea;\n}\n.color-swatches .bs-gray {\n  background-color: #f9f9f9;\n}\n\n\n/*\n * Team members\n *\n * Avatars, names, and usernames for core team.\n */\n\n.bs-team .team-member {\n  line-height: 32px;\n  color: #555;\n}\n.bs-team .team-member:hover {\n  color: #333;\n  text-decoration: none;\n}\n.bs-team .github-btn {\n  float: right;\n  width: 180px;\n  height: 20px;\n  margin-top: 6px;\n  border: none;\n}\n.bs-team img {\n  float: left;\n  width: 32px;\n  margin-right: 10px;\n  border-radius: 4px;\n}\n\n\n/*\n * Wall of Browser Bugs\n *\n * Better display for the responsive table on the Wall of Browser Bugs.\n */\n\n.bs-docs-browser-bugs td p {\n  margin-bottom: 0;\n}\n\n.bs-docs-browser-bugs th:first-child {\n  width: 18%;\n}\n\n\n/*\n * Grid examples\n *\n * Highlight the grid columns within the docs so folks can see their padding,\n * alignment, sizing, etc.\n */\n\n.show-grid {\n  margin-bottom: 15px;\n}\n.show-grid [class^=\"col-\"] {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  background-color: #eee;\n  background-color: rgba(86,61,124,.15);\n  border: 1px solid #ddd;\n  border: 1px solid rgba(86,61,124,.2);\n}\n\n\n/*\n * Examples\n *\n * Isolated sections of example content for each component or feature. Usually\n * followed by a code snippet.\n */\n\n.bs-example {\n  position: relative;\n  padding: 45px 15px 15px;\n  margin: 0 -15px 15px;\n  border-color: #e5e5e5 #eee #eee;\n  border-style: solid;\n  border-width: 1px 0;\n  -webkit-box-shadow: inset 0 3px 6px rgba(0,0,0,.05);\n          box-shadow: inset 0 3px 6px rgba(0,0,0,.05);\n}\n/* Echo out a label for the example */\n.bs-example:after {\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  font-size: 12px;\n  font-weight: bold;\n  color: #959595;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  content: \"Example\";\n}\n\n.bs-example-padded-bottom {\n  padding-bottom: 24px;\n}\n\n/* Tweak display of the code snippets when following an example */\n.bs-example + .highlight,\n.bs-example + .zero-clipboard + .highlight {\n  margin: -15px -15px 15px;\n  border-width: 0 0 1px;\n  border-radius: 0;\n}\n\n/* Make the examples and snippets not full-width */\n@media (min-width: 768px) {\n  .bs-example {\n    margin-right: 0;\n    margin-left: 0;\n    background-color: #fff;\n    border-color: #ddd;\n    border-width: 1px;\n    border-radius: 4px 4px 0 0;\n    -webkit-box-shadow: none;\n            box-shadow: none;\n  }\n  .bs-example + .highlight,\n  .bs-example + .zero-clipboard + .highlight {\n    margin-top: -16px;\n    margin-right: 0;\n    margin-left: 0;\n    border-width: 1px;\n    border-bottom-right-radius: 4px;\n    border-bottom-left-radius: 4px;\n  }\n  .bs-example-standalone {\n    border-radius: 4px;\n  }\n}\n\n/* Undo width of container */\n.bs-example .container {\n  width: auto;\n}\n\n/* Tweak content of examples for optimum awesome */\n.bs-example > p:last-child,\n.bs-example > ul:last-child,\n.bs-example > ol:last-child,\n.bs-example > blockquote:last-child,\n.bs-example > .form-control:last-child,\n.bs-example > .table:last-child,\n.bs-example > .navbar:last-child,\n.bs-example > .jumbotron:last-child,\n.bs-example > .alert:last-child,\n.bs-example > .panel:last-child,\n.bs-example > .list-group:last-child,\n.bs-example > .well:last-child,\n.bs-example > .progress:last-child,\n.bs-example > .table-responsive:last-child > .table {\n  margin-bottom: 0;\n}\n.bs-example > p > .close {\n  float: none;\n}\n\n/* Typography */\n.bs-example-type .table .type-info {\n  color: #767676;\n  vertical-align: middle;\n}\n.bs-example-type .table td {\n  padding: 15px 0;\n  border-color: #eee;\n}\n.bs-example-type .table tr:first-child td {\n  border-top: 0;\n}\n.bs-example-type h1,\n.bs-example-type h2,\n.bs-example-type h3,\n.bs-example-type h4,\n.bs-example-type h5,\n.bs-example-type h6 {\n  margin: 0;\n}\n\n/* Contextual background colors */\n.bs-example-bg-classes p {\n  padding: 15px;\n}\n\n/* Images */\n.bs-example > .img-circle,\n.bs-example > .img-rounded,\n.bs-example > .img-thumbnail {\n  margin: 5px;\n}\n\n/* Tables */\n.bs-example > .table-responsive > .table {\n  background-color: #fff;\n}\n\n/* Buttons */\n.bs-example > .btn,\n.bs-example > .btn-group {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example > .btn-toolbar + .btn-toolbar {\n  margin-top: 10px;\n}\n\n/* Forms */\n.bs-example-control-sizing select,\n.bs-example-control-sizing input[type=\"text\"] + input[type=\"text\"] {\n  margin-top: 10px;\n}\n.bs-example-form .input-group {\n  margin-bottom: 10px;\n}\n.bs-example > textarea.form-control {\n  resize: vertical;\n}\n\n/* List groups */\n.bs-example > .list-group {\n  max-width: 400px;\n}\n\n/* Navbars */\n.bs-example .navbar:last-child {\n  margin-bottom: 0;\n}\n.bs-navbar-top-example,\n.bs-navbar-bottom-example {\n  z-index: 1;\n  padding: 0;\n  overflow: hidden; /* cut the drop shadows off */\n}\n.bs-navbar-top-example .navbar-header,\n.bs-navbar-bottom-example .navbar-header {\n  margin-left: 0;\n}\n.bs-navbar-top-example .navbar-fixed-top,\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  position: relative;\n  margin-right: 0;\n  margin-left: 0;\n}\n.bs-navbar-top-example {\n  padding-bottom: 45px;\n}\n.bs-navbar-top-example:after {\n  top: auto;\n  bottom: 15px;\n}\n.bs-navbar-top-example .navbar-fixed-top {\n  top: -1px;\n}\n.bs-navbar-bottom-example {\n  padding-top: 45px;\n}\n.bs-navbar-bottom-example .navbar-fixed-bottom {\n  bottom: -1px;\n}\n.bs-navbar-bottom-example .navbar {\n  margin-bottom: 0;\n}\n@media (min-width: 768px) {\n  .bs-navbar-top-example .navbar-fixed-top,\n  .bs-navbar-bottom-example .navbar-fixed-bottom {\n    position: absolute;\n  }\n}\n\n/* Pagination */\n.bs-example .pagination {\n  margin-top: 10px;\n  margin-bottom: 10px;\n}\n\n/* Pager */\n.bs-example > .pager {\n  margin-top: 0;\n}\n\n/* Example modals */\n.bs-example-modal {\n  background-color: #f5f5f5;\n}\n.bs-example-modal .modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block;\n}\n.bs-example-modal .modal-dialog {\n  left: auto;\n  margin-right: auto;\n  margin-left: auto;\n}\n\n/* Example dropdowns */\n.bs-example > .dropdown > .dropdown-toggle {\n  float: left;\n}\n.bs-example > .dropdown > .dropdown-menu {\n  position: static;\n  display: block;\n  margin-bottom: 5px;\n  clear: left;\n}\n\n/* Example tabbable tabs */\n.bs-example-tabs .nav-tabs {\n  margin-bottom: 15px;\n}\n\n/* Tooltips */\n.bs-example-tooltips {\n  text-align: center;\n}\n.bs-example-tooltips > .btn {\n  margin-top: 5px;\n  margin-bottom: 5px;\n}\n.bs-example-tooltip .tooltip {\n  position: relative;\n  display: inline-block;\n  margin: 10px 20px;\n  opacity: 1;\n}\n\n/* Popovers */\n.bs-example-popover {\n  padding-bottom: 24px;\n  background-color: #f9f9f9;\n}\n.bs-example-popover .popover {\n  position: relative;\n  display: block;\n  float: left;\n  width: 260px;\n  margin: 20px;\n}\n\n/* Scrollspy demo on fixed height div */\n.scrollspy-example {\n  position: relative;\n  height: 200px;\n  margin-top: 10px;\n  overflow: auto;\n}\n\n.bs-example > .nav-pills-stacked-example {\n  max-width: 300px;\n}\n\n/* Simple collapse example */\n#collapseExample .well {\n  margin-bottom: 0;\n}\n\n/* Don't wrap event names in Events tables in JS plugin docs */\n.bs-events-table > thead > tr > th:first-child,\n.bs-events-table > tbody > tr > td:first-child {\n  white-space: nowrap;\n}\n\n.bs-events-table > thead > tr > th:first-child {\n  width: 150px;\n}\n\n.js-options-table > thead > tr > th:nth-child(1),\n.js-options-table > thead > tr > th:nth-child(2) {\n  width: 100px;\n}\n\n.js-options-table > thead > tr > th:nth-child(3) {\n  width: 50px;\n}\n\n/*\n * Code snippets\n *\n * Generated via Pygments and Jekyll, these are snippets of HTML, CSS, and JS.\n */\n\n.highlight {\n  padding: 9px 14px;\n  margin-bottom: 14px;\n  background-color: #f7f7f9;\n  border: 1px solid #e1e1e8;\n  border-radius: 4px;\n}\n.highlight pre {\n  padding: 0;\n  margin-top: 0;\n  margin-bottom: 0;\n  word-break: normal;\n  white-space: nowrap;\n  background-color: transparent;\n  border: 0;\n}\n.highlight pre code {\n  font-size: inherit;\n  color: #333; /* Effectively the base text color */\n}\n.highlight pre code:first-child {\n  display: inline-block;\n  padding-right: 45px;\n}\n\n\n/*\n * Responsive tests\n *\n * Generate a set of tests to show the responsive utilities in action.\n */\n\n/* Responsive (scrollable) doc tables */\n.table-responsive .highlight pre {\n  white-space: normal;\n}\n\n/* Utility classes table  */\n.bs-table th small,\n.responsive-utilities th small {\n  display: block;\n  font-weight: normal;\n  color: #999;\n}\n.responsive-utilities tbody th {\n  font-weight: normal;\n}\n.responsive-utilities td {\n  text-align: center;\n}\n.responsive-utilities td.is-visible {\n  color: #468847;\n  background-color: #dff0d8 !important;\n}\n.responsive-utilities td.is-hidden {\n  color: #ccc;\n  background-color: #f9f9f9 !important;\n}\n\n/* Responsive tests */\n.responsive-utilities-test {\n  margin-top: 5px;\n}\n.responsive-utilities-test .col-xs-6 {\n  margin-bottom: 10px;\n}\n.responsive-utilities-test span {\n  display: block;\n  padding: 15px 10px;\n  font-size: 14px;\n  font-weight: bold;\n  line-height: 1.1;\n  text-align: center;\n  border-radius: 4px;\n}\n.visible-on .col-xs-6 .hidden-xs,\n.visible-on .col-xs-6 .hidden-sm,\n.visible-on .col-xs-6 .hidden-md,\n.visible-on .col-xs-6 .hidden-lg,\n.hidden-on .col-xs-6 .hidden-xs,\n.hidden-on .col-xs-6 .hidden-sm,\n.hidden-on .col-xs-6 .hidden-md,\n.hidden-on .col-xs-6 .hidden-lg {\n  color: #999;\n  border: 1px solid #ddd;\n}\n.visible-on .col-xs-6 .visible-xs-block,\n.visible-on .col-xs-6 .visible-sm-block,\n.visible-on .col-xs-6 .visible-md-block,\n.visible-on .col-xs-6 .visible-lg-block,\n.hidden-on .col-xs-6 .visible-xs-block,\n.hidden-on .col-xs-6 .visible-sm-block,\n.hidden-on .col-xs-6 .visible-md-block,\n.hidden-on .col-xs-6 .visible-lg-block {\n  color: #468847;\n  background-color: #dff0d8;\n  border: 1px solid #d6e9c6;\n}\n\n\n/*\n * Glyphicons\n *\n * Special styles for displaying the icons and their classes in the docs.\n */\n\n.bs-glyphicons {\n  margin: 0 -10px 20px;\n  overflow: hidden;\n}\n.bs-glyphicons-list {\n  padding-left: 0;\n  list-style: none;\n}\n.bs-glyphicons li {\n  float: left;\n  width: 25%;\n  height: 115px;\n  padding: 10px;\n  font-size: 10px;\n  line-height: 1.4;\n  text-align: center;\n  background-color: #f9f9f9;\n  border: 1px solid #fff;\n}\n.bs-glyphicons .glyphicon {\n  margin-top: 5px;\n  margin-bottom: 10px;\n  font-size: 24px;\n}\n.bs-glyphicons .glyphicon-class {\n  display: block;\n  text-align: center;\n  word-wrap: break-word; /* Help out IE10+ with class names */\n}\n.bs-glyphicons li:hover {\n  color: #fff;\n  background-color: #563d7c;\n}\n\n@media (min-width: 768px) {\n  .bs-glyphicons {\n    margin-right: 0;\n    margin-left: 0;\n  }\n  .bs-glyphicons li {\n    width: 12.5%;\n    font-size: 12px;\n  }\n}\n\n\n/*\n * Customizer\n *\n * Since this is so form control heavy, we have quite a few styles to customize\n * the display of inputs, headings, and more. Also included are all the download\n * buttons and actions.\n */\n\n.bs-customizer .toggle {\n  float: right;\n  margin-top: 25px;\n}\n\n/* Headings and form contrls */\n.bs-customizer label {\n  margin-top: 10px;\n  font-weight: 500;\n  color: #555;\n}\n.bs-customizer h2 {\n  padding-top: 30px;\n  margin-top: 0;\n  margin-bottom: 5px;\n}\n.bs-customizer h3 {\n  margin-bottom: 0;\n}\n.bs-customizer h4 {\n  margin-top: 15px;\n  margin-bottom: 0;\n}\n.bs-customizer .bs-callout h4 {\n  margin-top: 0; /* lame, but due to specificity we have to duplicate */\n  margin-bottom: 5px;\n}\n.bs-customizer input[type=\"text\"] {\n  font-family: Menlo, Monaco, Consolas, \"Courier New\", monospace;\n  background-color: #fafafa;\n}\n.bs-customizer .help-block {\n  margin-bottom: 5px;\n  font-size: 12px;\n}\n\n/* For the variables, use regular weight */\n#less-section label {\n  font-weight: normal;\n}\n\n/* Downloads */\n.bs-customize-download .btn-outline {\n  padding: 20px;\n}\n\n/* Error handling */\n.bs-customizer-alert {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1030;\n  padding: 15px 0;\n  color: #fff;\n  background-color: #d9534f;\n  border-bottom: 1px solid #b94441;\n  -webkit-box-shadow: inset 0 1px 0 rgba(255,255,255,.25);\n          box-shadow: inset 0 1px 0 rgba(255,255,255,.25);\n}\n.bs-customizer-alert .close {\n  margin-top: -4px;\n  font-size: 24px;\n}\n.bs-customizer-alert p {\n  margin-bottom: 0;\n}\n.bs-customizer-alert .glyphicon {\n  margin-right: 5px;\n}\n.bs-customizer-alert pre {\n  margin: 10px 0 0;\n  color: #fff;\n  background-color: #a83c3a;\n  border-color: #973634;\n  -webkit-box-shadow: inset 0 2px 4px rgba(0,0,0,.05), 0 1px 0 rgba(255,255,255,.1);\n          box-shadow: inset 0 2px 4px rgba(0,0,0,.05), 0 1px 0 rgba(255,255,255,.1);\n}\n\n.bs-dropzone {\n  position: relative;\n  padding: 20px;\n  margin-bottom: 20px;\n  color: #777;\n  text-align: center;\n  border: 2px dashed #eee;\n  border-radius: 4px;\n}\n.bs-dropzone .import-header {\n  margin-bottom: 5px;\n}\n.bs-dropzone .glyphicon-download-alt {\n  font-size: 40px;\n}\n.bs-dropzone hr {\n  width: 100px;\n}\n.bs-dropzone .lead {\n  margin-bottom: 10px;\n  font-weight: normal;\n  color: #333;\n}\n/*.bs-dropzone*/ #import-manual-trigger {\n  cursor: pointer;\n}\n.bs-dropzone p:last-child {\n  margin-bottom: 0;\n}\n\n/*\n * Brand guidelines\n *\n * Extra styles for displaying wordmarks, logos, etc.\n */\n\n/* Logo series wrapper */\n.bs-brand-logos {\n  display: table;\n  width: 100%;\n  margin-bottom: 15px;\n  overflow: hidden;\n  color: #563d7c;\n  background-color: #f9f9f9;\n  border-radius: 4px;\n}\n\n/* Individual items */\n.bs-brand-item {\n  padding: 60px 0;\n  text-align: center;\n}\n.bs-brand-item + .bs-brand-item {\n  border-top: 1px solid #fff;\n}\n.bs-brand-logos .inverse {\n  color: #fff;\n  background-color: #563d7c;\n}\n\n/* Heading content within */\n.bs-brand-item h1,\n.bs-brand-item h3 {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.bs-brand-item .bs-docs-booticon {\n  margin-right: auto;\n  margin-left: auto;\n}\n\n/* Make the icons stand out on what is/isn't okay */\n.bs-brand-item .glyphicon {\n  width: 30px;\n  height: 30px;\n  margin: 10px auto -10px;\n  line-height: 30px;\n  color: #fff;\n  border-radius: 50%;\n}\n.bs-brand-item .glyphicon-ok {\n  background-color: #5cb85c;\n}\n.bs-brand-item .glyphicon-remove {\n  background-color: #d9534f;\n}\n\n@media (min-width: 768px) {\n  .bs-brand-item {\n    display: table-cell;\n    width: 1%;\n  }\n  .bs-brand-item + .bs-brand-item {\n    border-top: 0;\n    border-left: 1px solid #fff;\n  }\n  .bs-brand-item h1 {\n    font-size: 60px;\n  }\n}\n\n\n/*\n * ZeroClipboard styles\n */\n\n.zero-clipboard {\n  position: relative;\n  display: none;\n}\n.btn-clipboard {\n  position: absolute;\n  top: 0;\n  right: 0;\n  z-index: 10;\n  display: block;\n  padding: 5px 8px;\n  font-size: 12px;\n  color: #767676;\n  cursor: pointer;\n  background-color: #fff;\n  border: 1px solid #e1e1e8;\n  border-radius: 0 4px 0 4px;\n}\n.btn-clipboard-hover {\n  color: #fff;\n  background-color: #563d7c;\n  border-color: #563d7c;\n}\n\n@media (min-width: 768px) {\n  .zero-clipboard {\n    display: block;\n  }\n  .bs-example + .zero-clipboard .btn-clipboard {\n    top: -16px;\n    border-top-right-radius: 0;\n  }\n}\n\n/*\n * AnchorJS Styles\n */\n.anchorjs-link {\n  color: inherit;\n}\n\n@media (max-width: 480px) {\n  .anchorjs-link {\n    display: none;\n  }\n}\n\n*:hover > .anchorjs-link {\n  opacity: .75;\n  -webkit-transition: color .16s linear;\n       -o-transition: color .16s linear;\n          transition: color .16s linear;\n}\n\n*:hover > .anchorjs-link:hover,\n.anchorjs-link:focus {\n  text-decoration: none;\n  opacity: 1;\n}\n\n/*\n * Miscellaneous\n *\n * Odds and ends for optimum docs display.\n */\n\n/* Pseudo :focus state for showing how it looks in the docs */\n#focusedInput {\n  border-color: rgb(204,204,204); /* Restate unfocused value to make CSSLint happy that there's a pre-CSS3 fallback*/\n  border-color: rgba(82,168,236,.8);\n  outline: 0;\n  outline: thin dotted \\9; /* IE6-9 */\n  -webkit-box-shadow: 0 0 8px rgba(82,168,236,.6);\n          box-shadow: 0 0 8px rgba(82,168,236,.6);\n}\n\n/* v4 notice above main navbar */\n.v4-tease {\n  display: block;\n  padding: 15px 20px;\n  font-weight: bold;\n  color: #fff;\n  text-align: center;\n  background-color: #0275d8;\n}\n.v4-tease:hover {\n  color: #fff;\n  text-decoration: none;\n  background-color: #0269c2;\n}\n\n/* Nullify ill-advised printing of hrefs; see #18711 */\n@media print {\n  a[href]:after {\n    content: \"\" !important;\n  }\n}\n"]}