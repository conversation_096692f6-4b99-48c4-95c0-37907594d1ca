[{"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 1, "mapping": "main", "name": "Main menu", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 11, "parent_id": null, "order": 1, "type": "page", "link_type": "page", "link_id": 3, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "За компанията"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 12, "parent_id": null, "order": 3, "type": "page", "link_type": "page", "link_id": 5, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Услуги"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 13, "parent_id": null, "order": 4, "type": "page", "link_type": "page", "link_id": 6, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Абонаменти"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 14, "parent_id": null, "order": 5, "type": "section", "link_type": null, "link_id": null, "route": "site.vendors", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Партньори"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 19, "parent_id": null, "order": 2, "type": "category", "link_type": "category", "link_id": 1, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Продукти"}, "relations": []}]}}, {"model": "App\\Models\\StoreFront\\NavigationGroups", "attributes": {"id": 2, "mapping": "footer", "name": "Footer menu", "description": null}, "relations": {"links": [{"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 15, "parent_id": null, "order": 1, "type": "page", "link_type": "page", "link_id": 3, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "За компанията"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 16, "parent_id": null, "order": 2, "type": "page", "link_type": "page", "link_id": 4, "route": null, "active_find": null, "url": null, "blank": 0, "class": null, "name": "Условия за ползване"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 17, "parent_id": null, "order": 3, "type": "section", "link_type": null, "link_id": null, "route": "blog.list", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Помощен център"}, "relations": []}, {"model": "App\\Models\\StoreFront\\Navigations", "attributes": {"id": 18, "parent_id": null, "order": 4, "type": "section", "link_type": null, "link_id": null, "route": "contacts", "active_find": null, "url": null, "blank": 0, "class": null, "name": "Контакти"}, "relations": []}]}}]