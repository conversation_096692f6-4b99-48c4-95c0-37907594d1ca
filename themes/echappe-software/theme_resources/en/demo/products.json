[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 1, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-5", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add characteristics /more information through text, video materials and work examples.The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 1, "vendor_id": null, "image_id": 1, "tracking": "no", "threshold": null, "shipping": "no", "digital": "yes", "sale": "no", "new": "no", "price_from": 3600, "price_to": 3600, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 3, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "digital", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 3, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 3600, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 1, "name": "Video Editting Softwa", "parent_id": 1, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "video-editting-softwa-image_6218eaa024237.jpeg", "background": "#B0C4DE", "width": 880, "height": 880, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 1, "color_id": 81, "percents": 49, "R": 176, "G": 196, "B": 222, "hex": "#B0C4DE"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 2, "color_id": 140, "percents": 37, "R": 216, "G": 191, "B": 216, "hex": "#D8BFD8"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 3, "color_id": 64, "percents": 5, "R": 230, "G": 230, "B": 250, "hex": "#E6E6FA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 61363}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 1, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 2, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 3, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-4", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add characteristics /more information through text, video materials and work examples.The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 1, "vendor_id": null, "image_id": 2, "tracking": "no", "threshold": null, "shipping": "no", "digital": "yes", "sale": "no", "new": "no", "price_from": 3200, "price_to": 3200, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 6, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "digital", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 6, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 3200, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 2, "name": "Sound Editing Software", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "sound-editing-software-image_6218ec87f0677.jpeg", "background": "#B0C4DE", "width": 880, "height": 880, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 4, "color_id": 81, "percents": 49, "R": 176, "G": 196, "B": 222, "hex": "#B0C4DE"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 5, "color_id": 140, "percents": 37, "R": 216, "G": 191, "B": 216, "hex": "#D8BFD8"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 6, "color_id": 64, "percents": 5, "R": 230, "G": 230, "B": 250, "hex": "#E6E6FA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 7, "color_id": 132, "percents": 5, "R": 106, "G": 90, "B": 205, "hex": "#6A5ACD"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 61343}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 3, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-3", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add characteristics /more information through text, video materials and work examples.The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 1, "vendor_id": null, "image_id": 3, "tracking": "no", "threshold": null, "shipping": "no", "digital": "yes", "sale": "no", "new": "no", "price_from": 3400, "price_to": 3400, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 9, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "digital", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 9, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 3400, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 3, "name": "Animation Software", "parent_id": 3, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "animation-software-image_6218ed04a005c.jpeg", "background": "#B0C4DE", "width": 880, "height": 880, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 8, "color_id": 81, "percents": 50, "R": 176, "G": 196, "B": 222, "hex": "#B0C4DE"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 9, "color_id": 140, "percents": 37, "R": 216, "G": 191, "B": 216, "hex": "#D8BFD8"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 10, "color_id": 132, "percents": 5, "R": 106, "G": 90, "B": 205, "hex": "#6A5ACD"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 57434}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 7, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 8, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 9, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 4, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-2", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add characteristics /more information through text, video materials and work examples.The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 1, "vendor_id": null, "image_id": 4, "tracking": "no", "threshold": null, "shipping": "no", "digital": "yes", "sale": "no", "new": "no", "price_from": 3000, "price_to": 3000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 11, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "digital", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 11, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 3000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 4, "name": "3d software", "parent_id": 4, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "3d-software-image_6218ed60cf154.jpeg", "background": "#B0C4DE", "width": 880, "height": 880, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 11, "color_id": 81, "percents": 49, "R": 176, "G": 196, "B": 222, "hex": "#B0C4DE"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 12, "color_id": 140, "percents": 37, "R": 216, "G": 191, "B": 216, "hex": "#D8BFD8"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 13, "color_id": 132, "percents": 5, "R": 106, "G": 90, "B": 205, "hex": "#6A5ACD"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 62884}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 10, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 11, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 12, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 5, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add characteristics /more information through text, video materials and work examples.The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 1, "vendor_id": null, "image_id": 5, "tracking": "no", "threshold": null, "shipping": "no", "digital": "yes", "sale": "no", "new": "no", "price_from": 3000, "price_to": 3000, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 14, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "digital", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 14, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 3000, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 5, "name": "Interior Design Software", "parent_id": 5, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "interior-design-software-image_6218ede89451a.jpeg", "background": "#B0C4DE", "width": 880, "height": 880, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 14, "color_id": 81, "percents": 48, "R": 176, "G": 196, "B": 222, "hex": "#B0C4DE"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 15, "color_id": 140, "percents": 37, "R": 216, "G": 191, "B": 216, "hex": "#D8BFD8"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 16, "color_id": 64, "percents": 5, "R": 230, "G": 230, "B": 250, "hex": "#E6E6FA"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 17, "color_id": 132, "percents": 5, "R": 106, "G": 90, "B": 205, "hex": "#6A5ACD"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 62867}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 13, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 14, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 15, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 6, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-1", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add characteristics /more information through text, video materials and work examples.The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist.", "category_id": 1, "vendor_id": null, "image_id": 6, "tracking": "no", "threshold": null, "shipping": "no", "digital": "yes", "sale": "no", "new": "no", "price_from": 3300, "price_to": 3300, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 17, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "", "type": "digital", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 17, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": "", "barcode": "", "price": 3300, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 6, "name": "Graphic & Illustration Software", "parent_id": 6, "sort_order": 1, "active": "yes", "max_thumb_size": 1280, "image_id": "graphic--illustration-software-image_6218ee4b0e0e5.jpeg", "background": "#B0C4DE", "width": 880, "height": 880, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [{"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 18, "color_id": 81, "percents": 48, "R": 176, "G": 196, "B": 222, "hex": "#B0C4DE"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 19, "color_id": 140, "percents": 38, "R": 216, "G": 191, "B": 216, "hex": "#D8BFD8"}, "relations": []}, {"model": "App\\Models\\Product\\ImageColors", "attributes": {"id": 20, "color_id": 64, "percents": 5, "R": 230, "G": 230, "B": 250, "hex": "#E6E6FA"}, "relations": []}], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 66075}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 16, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 17, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 18, "parameter": "height", "value": ""}, "relations": []}], "options_stat": []}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 7, "url_handle": "creative-box", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": null, "p2": null, "p3": null, "p1_id": null, "p2_id": null, "p3_id": null, "description": "The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add characteristics /more information through text, video materials and work examples.The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Since there are a large number of active eStores, your long-term success requires a well-considered business approach. The SEO description should be based on a thorough analysis done by a SEO specialist. ", "category_id": null, "vendor_id": null, "image_id": null, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 18900, "price_to": 18900, "price_percent": null, "individual_price": 0, "price_type": "price", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 18, "short_description": "Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer’s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.", "featured": 0, "description_title": null, "type": "bundle", "is_hidden": 0, "per_row": 3, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": null}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 18, "v1": null, "v2": null, "v3": null, "v1_id": null, "v2_id": null, "v3_id": null, "quantity": null, "sku": null, "barcode": null, "price": 18900, "delivery_price": null, "weight": null}, "relations": []}], "images": [], "meta_data": [], "options_stat": []}}]