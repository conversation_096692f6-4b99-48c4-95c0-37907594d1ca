[{"model": "App\\Models\\Product\\Product", "attributes": {"id": 1, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-11", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 1, "vendor_id": 1, "image_id": 35, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 84700, "price_to": 84700, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 3, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": 0, "seo_generated_through_spinner": 0, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 3, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 84700, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 4, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 84700, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 5, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 84700, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 6, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 84700, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 7, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 84700, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 35, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 1, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e866b1615.png", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 236670}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 1, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 2, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 3, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 436, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 3}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 437, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 4}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 438, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 5}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 439, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 6}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 440, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 7}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 2, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-10", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 1, "vendor_id": 1, "image_id": 34, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 147200, "price_to": 147200, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 10, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 9, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 147200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 10, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 147200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 11, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 147200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 12, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 147200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 13, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 147200, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 34, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 2, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e84438a75.png", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 258924}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 4, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 5, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 6, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 431, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 9}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 432, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 10}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 433, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 11}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 434, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 12}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 435, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 13}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 3, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-9", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 1, "vendor_id": 1, "image_id": 33, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 92400, "price_to": 92400, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 15, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 15, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 92400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 16, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 92400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 17, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 92400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 18, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 92400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 19, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 92400, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 33, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 3, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e8238c684.jpeg", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 144374}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 7, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 8, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 9, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 426, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 15}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 427, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 16}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 428, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 17}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 429, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 18}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 430, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 19}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 4, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-8", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 1, "vendor_id": 1, "image_id": 32, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 121400, "price_to": 121400, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 21, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 21, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 121400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 22, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 121400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 23, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 121400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 24, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 121400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 25, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 121400, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 32, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 4, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e803779ac.jpeg", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 133394}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 10, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 11, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 12, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 421, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 21}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 422, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 22}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 423, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 23}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 424, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 24}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 425, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 25}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 5, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-7", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 1, "vendor_id": 1, "image_id": 31, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "yes", "price_from": 72700, "price_to": 72700, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 27, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 27, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 72700, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 28, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 72700, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 29, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 72700, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 30, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 72700, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 31, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 72700, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 31, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 5, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e7d9869fc.jpeg", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 149061}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 13, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 14, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 15, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 416, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 27}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 417, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 28}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 418, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 29}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 419, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 30}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 420, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 31}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 6, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 2, "vendor_id": 1, "image_id": 30, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 37600, "price_to": 37600, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 33, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 33, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 37600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 34, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 37600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 35, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 37600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 36, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 37600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 37, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 37600, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 30, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 6, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e7b7e5f30.jpeg", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 94163}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 16, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 17, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 18, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 411, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 33}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 412, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 34}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 413, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 35}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 414, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 36}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 415, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 37}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 7, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-5", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 2, "vendor_id": 1, "image_id": 29, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 42300, "price_to": 42300, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 39, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 39, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 42300, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 40, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 42300, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 41, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 42300, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 42, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 42300, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 43, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 42300, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 29, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 7, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e794478a3.png", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 236670}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 19, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 20, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 21, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 406, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 39}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 407, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 40}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 408, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 41}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 409, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 42}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 410, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 43}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 8, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-4", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 2, "vendor_id": 1, "image_id": 28, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 27500, "price_to": 27500, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 45, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 45, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27500, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 46, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27500, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 47, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27500, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 48, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27500, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 49, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27500, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 28, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 8, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e76c88edc.png", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 258924}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 22, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 23, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 24, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 401, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 45}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 402, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 46}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 403, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 47}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 404, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 48}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 405, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 49}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 9, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-3", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 2, "vendor_id": 1, "image_id": 27, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 43200, "price_to": 43200, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 51, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 51, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 43200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 52, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 43200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 53, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 43200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 54, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 43200, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 55, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 43200, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 27, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 9, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e73f5e73b.jpeg", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 144374}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 25, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 26, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 27, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 396, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 51}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 397, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 52}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 398, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 53}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 399, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 54}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 400, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 55}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 10, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-2", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 2, "vendor_id": 1, "image_id": 26, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 27600, "price_to": 27600, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 57, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 57, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 58, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 59, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 60, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 61, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27600, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 26, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 10, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e71fcef5c.jpeg", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 133394}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 28, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 29, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 30, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 391, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 57}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 392, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 58}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 393, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 59}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 394, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 60}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 395, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 61}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 11, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-1", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 3, "vendor_id": 1, "image_id": 25, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 27600, "price_to": 27600, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 63, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 63, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 64, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 65, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 66, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27600, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 67, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 27600, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 25, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 11, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e6e21bc0e.jpeg", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 149061}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 31, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 32, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 33, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 386, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 63}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 387, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 64}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 388, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 65}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 389, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 66}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 390, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 67}, "relations": []}]}}, {"model": "App\\Models\\Product\\Product", "attributes": {"id": 12, "url_handle": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters", "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "p1": "Color", "p2": null, "p3": null, "p1_id": 2, "p2_id": null, "p3_id": null, "description": "<p>The client is already familiar with the short description and its structure, so it is preferable to provide more details about the points which you have already mentioned. You can add more information about the material/specifications through text, standard videos/360 degree review and / or photos. The customer is already at the product page, considering buying the item and he should be able to interpret all information effortlessly. It is recommended to not use more than two fonts.</p>", "seo_title": "Create a title describing the type, color and material of the item (up to 60 characters)", "seo_description": "Create a title describing the type, color and material of the item (up to 60 characters)", "category_id": 3, "vendor_id": 1, "image_id": 24, "tracking": "no", "threshold": null, "shipping": "no", "digital": "no", "sale": "no", "new": "no", "price_from": 30400, "price_to": 30400, "price_percent": null, "individual_price": 0, "price_type": "variant", "active": "yes", "continue_selling": "no", "imported": "no", "draft": "no", "default_variant_id": 69, "short_description": "<p>Provide a brief product description by listing key benefits and adding up to two sentences of general information. You should consider the problems which could be solved by the product? Also, what makes the item more valuable than the other ones listed in your store as well as those offered by competitors? Since the product page is a step away from the cart, you should gain your customer&rsquo;s trust by the time he has looked through the selected item.Therefore, the information must be presented in an appropriate tone for your consumer groups.</p>", "featured": 0, "description_title": "Information", "type": "multiple", "is_hidden": 0, "per_row": 0, "sort_order": null, "seo_generated_through_spinner": null, "minimum": 1, "type_digital": "file"}, "relations": {"variants": [{"model": "App\\Models\\Product\\Variant", "attributes": {"id": 69, "v1": "Red", "v2": null, "v3": null, "v1_id": 4, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 30400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 70, "v1": "Green", "v2": null, "v3": null, "v1_id": 5, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 30400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 71, "v1": "Blue", "v2": null, "v3": null, "v1_id": 6, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 30400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 72, "v1": "White", "v2": null, "v3": null, "v1_id": 10, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 30400, "delivery_price": null, "weight": null}, "relations": []}, {"model": "App\\Models\\Product\\Variant", "attributes": {"id": 73, "v1": "Black", "v2": null, "v3": null, "v1_id": 11, "v2_id": null, "v3_id": null, "quantity": null, "sku": "TR435645", "barcode": "", "price": 30400, "delivery_price": null, "weight": null}, "relations": []}], "images": [{"model": "App\\Models\\Product\\Image", "attributes": {"id": 24, "name": "Create a title describing the type, color and material of the item (up to 60 characters)", "parent_id": 12, "sort_order": 1, "active": "yes", "max_thumb_size": 800, "image_id": "create-a-title-describing-the-type-color-and-material-of-the-item-up-to-60-characters-6304e6a53034e.jpeg", "background": null, "width": 700, "height": 700, "type": "image", "gallery_id": null, "video_url": null, "image_processed": 1}, "relations": {"colors": [], "storage": [{"model": "App\\Models\\System\\Storage", "attributes": {"size": 94163}, "relations": []}]}}], "meta_data": [{"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 34, "parameter": "width", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 35, "parameter": "depth", "value": ""}, "relations": []}, {"model": "App\\Models\\Product\\ProductMeta", "attributes": {"id": 36, "parameter": "height", "value": ""}, "relations": []}], "options_stat": [{"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 381, "parameter_id": 2, "parameter_option_id": 4, "variant_id": 69}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 382, "parameter_id": 2, "parameter_option_id": 5, "variant_id": 70}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 383, "parameter_id": 2, "parameter_option_id": 6, "variant_id": 71}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 384, "parameter_id": 2, "parameter_option_id": 10, "variant_id": 72}, "relations": []}, {"model": "App\\Models\\Product\\ProductsParametersOptionsStat", "attributes": {"id": 385, "parameter_id": 2, "parameter_option_id": 11, "variant_id": 73}, "relations": []}]}}]