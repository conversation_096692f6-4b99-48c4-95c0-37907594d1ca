<div class="bs-docs-section">
  <h1 id="examples" class="page-header">Examples</h1>

  <p class="lead">Build on the basic template above with Bootstrap's many components. We encourage you to customize and adapt Bootstrap to suit your individual project's needs.</p>

  <p>Get the source code for every example below by <a href="{{ site.download.source }}">downloading the Bootstrap repository</a>. Examples can be found in the <code>docs/examples/</code> directory.</p>

  <h2 id="examples-framework">Using the framework</h2>
  <div class="row bs-examples">
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/starter-template/">
        <img src="../examples/screenshots/starter-template.jpg" alt="Starter template example">
      </a>
      <h3>Starter template</h3>
      <p>Nothing but the basics: compiled CSS and JavaScript along with a container.</p>
    </div>
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/theme/">
        <img src="../examples/screenshots/theme.jpg" alt="Bootstrap theme example">
      </a>
      <h3>Bootstrap theme</h3>
      <p>Load the optional Bootstrap theme for a visually enhanced experience.</p>
    </div>
    <div class="clearfix visible-xs"></div>

    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/grid/">
        <img src="../examples/screenshots/grid.jpg" alt="Multiple grids example">
      </a>
      <h3>Grids</h3>
      <p>Multiple examples of grid layouts with all four tiers, nesting, and more.</p>
    </div>
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/jumbotron/">
        <img src="../examples/screenshots/jumbotron.jpg" alt="Jumbotron example">
      </a>
      <h3>Jumbotron</h3>
      <p>Build around the jumbotron with a navbar and some basic grid columns.</p>
    </div>
    <div class="clearfix visible-xs"></div>

    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/jumbotron-narrow/">
        <img src="../examples/screenshots/jumbotron-narrow.jpg" alt="Narrow jumbotron example">
      </a>
      <h3>Narrow jumbotron</h3>
      <p>Build a more custom page by narrowing the default container and jumbotron.</p>
    </div>
  </div>

  <h2 id="examples-navbars">Navbars in action</h2>
  <div class="row bs-examples">
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/navbar/">
        <img src="../examples/screenshots/navbar.jpg" alt="Navbar example">
      </a>
      <h3>Navbar</h3>
      <p>Super basic template that includes the navbar along with some additional content.</p>
    </div>
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/navbar-static-top/">
        <img src="../examples/screenshots/navbar-static.jpg" alt="Static top navbar example">
      </a>
      <h3>Static top navbar</h3>
      <p>Super basic template with a static top navbar along with some additional content.</p>
    </div>
    <div class="clearfix visible-xs"></div>

    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/navbar-fixed-top/">
        <img src="../examples/screenshots/navbar-fixed.jpg" alt="Fixed navbar example">
      </a>
      <h3>Fixed navbar</h3>
      <p>Super basic template with a fixed top navbar along with some additional content.</p>
    </div>
  </div>

  <h2 id="examples-custom">Custom components</h2>
  <div class="row bs-examples">
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/cover/">
        <img src="../examples/screenshots/cover.jpg" alt="A one-page template example">
      </a>
      <h3>Cover</h3>
      <p>A one-page template for building simple and beautiful home pages.</p>
    </div>
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/carousel/">
        <img src="../examples/screenshots/carousel.jpg" alt="Carousel example">
      </a>
      <h3>Carousel</h3>
      <p>Customize the navbar and carousel, then add some new components.</p>
    </div>
    <div class="clearfix visible-xs"></div>

    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/blog/">
        <img src="../examples/screenshots/blog.jpg" alt="Blog layout example">
      </a>
      <h3>Blog</h3>
      <p>Simple two-column blog layout with custom navigation, header, and type.</p>
    </div>
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/dashboard/">
        <img src="../examples/screenshots/dashboard.jpg" alt="Dashboard example">
      </a>
      <h3>Dashboard</h3>
      <p>Basic structure for an admin dashboard with fixed sidebar and navbar.</p>
    </div>
    <div class="clearfix visible-xs"></div>

    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/signin/">
        <img src="../examples/screenshots/sign-in.jpg" alt="Sign-in page example">
      </a>
      <h3>Sign-in page</h3>
      <p>Custom form layout and design for a simple sign in form.</p>
    </div>
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/justified-nav/">
        <img src="../examples/screenshots/justified-nav.jpg" alt="Justified nav example">
      </a>
      <h3>Justified nav</h3>
      <p>Create a custom navbar with justified links. Heads up! <a href="../components/#nav-justified">Not too Safari friendly.</a></p>
    </div>
    <div class="clearfix visible-xs"></div>

    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/sticky-footer/">
        <img src="../examples/screenshots/sticky-footer.jpg" alt="Sticky footer example">
      </a>
      <h3>Sticky footer</h3>
      <p>Attach a footer to the bottom of the viewport when the content is shorter than it.</p>
    </div>
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/sticky-footer-navbar/">
        <img src="../examples/screenshots/sticky-footer-navbar.jpg" alt="Sticky footer with navbar example">
      </a>
      <h3>Sticky footer with navbar</h3>
      <p>Attach a footer to the bottom of the viewport with a fixed navbar at the top.</p>
    </div>
  </div>

  <h2 id="examples-experiments">Experiments</h2>
  <div class="row bs-examples">
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/non-responsive/">
        <img src="../examples/screenshots/non-responsive.jpg" alt="Non-responsive example">
      </a>
      <h3>Non-responsive Bootstrap</h3>
      <p>Easily disable the responsiveness of Bootstrap <a href="#disable-responsive">per our docs</a>.</p>
    </div>
    <div class="col-xs-6 col-md-4">
      <a class="thumbnail" href="../examples/offcanvas/">
        <img src="../examples/screenshots/offcanvas.jpg" alt="Off-canvas navigation example">
      </a>
      <h3>Off-canvas</h3>
      <p>Build a toggleable off-canvas navigation menu for use with Bootstrap.</p>
    </div>
  </div>
</div>
