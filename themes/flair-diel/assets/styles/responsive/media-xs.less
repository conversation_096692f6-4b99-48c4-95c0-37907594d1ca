/*=============================================================================*\
    EXTRA SMALL DEVICES / PHONES
\*=============================================================================*/

@media (max-width: @screen-xs-max) {

	/* #Main
	-------------------------------------------------------------------------------*/

	h1, ._h1 {
		font-size: 24px;
	}

	h2, ._h2 {
		font-size: 22px;
	}

	h3, ._h3 {
		font-size: 20px;
	}

	h4, ._h4 {
		font-size: 18px;
	}

	h5, ._h5 {
		font-size: 16px;
	}

	h6, ._h6 {
		font-size: 14px;
	}

	._button,
	._meta-link-login,
	._meta-link-register,
	._meta-link-forgotten-password {
		width: 100%;
	}

	._meta-links {
		padding-top: 15px;
		margin-top: 15px;

		a {
			+ a {
				margin: 10px 0 0;
			}
		}
	}

	._breadcrumb-container {
		display: none;
	}

	._content {
		padding-top: 25px;
		padding-bottom: 25px;

		> ._breadcrumb-container {
			&:first-child {
				margin-top: -25px;
			}
		}
	}

	._notification {
		font-size: calc(@font-size-main ~'-' 2px); /* theme */
	}

	/* #Textbox
	-------------------------------------------------------------------------------*/

	._textbox {
		.alignleft,
		.alignright {
			float: none;
			width: auto;
			display: block;
			margin: 0 0 20px;
		}
	}

	/* #Forms
	-------------------------------------------------------------------------------*/
	
	._form-row {
		display: block;
		margin-bottom: 10px;

		._form-col {
			float: none;
			display: block;
			margin-bottom: 10px;
			width: auto;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	._form-row-secondary {
		display: block;
		margin-bottom: 10px;

		._form-col-secondary {
			float: none;
			display: block;
			margin-bottom: 10px;
			width: auto;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	._block-title {
		margin-bottom: 10px;
	}

	[id^="recaptcha"] {
		-webkit-transform-origin: 0 0;
		        transform-origin: 0 0;
		-webkit-transform: scale(.9);
		        transform: scale(.9);
	}

	._to-top {
		width: 30px;
		height: 30px;
		line-height: 28px;
		font-size: 14px;
		bottom: 10px;
		right: 10px;
	}

	._compare-products {
		~ ._to-top {
			bottom: 50px;
		}

		&.hide {
			~ ._to-top {
				bottom: 10px;
			}
		}
	}

	/* #Separator
	-------------------------------------------------------------------------------*/

	._section-separator-large {
		margin-bottom: 40px;
	}

	._section-separator,
	._section-title,
	._showcase-title,
	._showcase-product-title,
	._pagination,
	._home-text ._text-title,
	._product-details-pictures-container {
		margin-bottom: 25px;
	}

	/* #Header
	-------------------------------------------------------------------------------*/

	._navigation-links {
		display: none;
	}

	._topbar-inner {
		padding: 12px 0;
	}

	._navbar {
		z-index: 5;
	}

	._logo {
		width: auto;

		img {
			max-height: 60px;
		}
	}

	._search-form-container {
		.container {
			width: 100%;
		}
	}

	._search-form {
		._field-icon {
			._field {
				float: none;
				height: 32px;
				padding-left: 32px;


				&:focus {
					+ ._button {
						.translate(0, 0);
					}
				}
			}

			._button {
				width: 32px;
			}
		}

		.scroll-wrapper {
			left: 0;
			right: auto;

			.ui-autocomplete {
				right: auto;
				left: 0 !important;
			}
		}
	}

	._navigation {
		a {
			padding-top: 10px;
			padding-bottom: 10px;
		}
	}

	._cart-compact,
	._wishlist-compact,
	._user-controls li + li {
		margin-left: 13px;
	}

	/* #Footer
	-------------------------------------------------------------------------------*/

	._footer {
		padding: 20px 0 5px;

		&-utilities {
			display: block;
			text-align: center;
		}

		&-socials {
			display: block;
			margin-bottom: 15px;
		}

		&-legal {
			width: auto;
			display: block;
			margin-bottom: 15px;
		}

		&-providers {
			display: block;
			margin-bottom: 15px;
			text-align: center;
		}

		&-cols {
			padding-bottom: 0;

			._text {
				float: none;
				width: auto;
				margin-bottom: 20px;

				& + ._navigation-footer {
					float: none;
					width: auto;
					.clearfix();

					li {

					}
				}
			}
		}
	}

	._navigation-footer {
		margin-bottom: 0;

		li {
			width: 50%;
			padding-right: 15px;
			margin-bottom: 20px;

			&:nth-child(3n+1) {
				clear: none;
			}

			&:nth-child(2n+1) {
				clear: both;
			}
		}
	}

	._compare-box-items,
	._compare-box-item,
	._compare-box-actions {
		float: none;
		width: auto;
	}

	._compare-box-item {
		padding: 0;
		margin-bottom: 10px;

		._remove {
			right: 0;
			.centerer(false, true);
		}
	}

	._compare-box-item-image,
	._compare-box-item-info {
		display: table-cell;
		vertical-align: middle;
	}

	._compare-box-item-info {
		padding: 0;
		padding-left: 10px;
		padding-right: 20px;
	}

	/* #Slider
	-------------------------------------------------------------------------------*/

	._slider {
		padding: 0;
		margin-bottom: 40px;

		+ ._banners {
			margin-top: -40px;
		}
	}

	.slide-text {
		display: none;
	}

	.owl-prev,
	.owl-next {
		width: 50px;
	}

	.owl-dots {
		bottom: 10px;
	}

	/* #Banners
	-------------------------------------------------------------------------------*/

	._banner-caption {
		display: none;
	}

	/* #Products list
	-------------------------------------------------------------------------------*/

	._sidebar-products,
	._sidebar-blog {
		width: 100%;
	}

	._sidebar-buttons {
		&-products {
			float: none;
			margin-bottom: 10px;
		}

		._button {
			width: 100%;
		}
	}

	._filters {
		display: block;

		&-left,
		&-right {
			display: block;
			width: auto;
		}

		&-row {
			float: none;
			display: block;
		}

		&-col {
			display: block;
			width: auto;
			padding: 0;
			margin-bottom: 10px;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	._product {
		&-out-of-stock {
			display: block;
			position: static;
			text-align: center;
		}

		&-details-share {
			display: inline-block;
		}

		._product-name {
			text-align: center;
		}

		&-price,
		&-add {
			display: block;
			padding: 0;
			text-align: center;
		}

		&-add {
			margin-top: 5px;
		}
	}

	._products-list-head {
		&-item {
			&-body {
				display: block;
			}

			&-image {
				display: block;
				width: auto;
				padding: 0;
				text-align: center;
			}

			&-description {
				margin-top: 20px;
				display: block;
			}
		}
	}

	._product-details-ribbon-banner {
		max-width: 33.33%;
	}

	._product-details-ribbon-banner-image {
		max-height: 100px;
	}

	._product-details {
		&-gallery {
			.swiper-wrapper {
				height: 264px;
				margin-right: 10px;
			}
		}

		&-useful-items {
			display: none;
		}

		&-properties {
			margin-bottom: 20px;
		}

		&-actions {
			display: block;
			white-space: normal;
			width: auto;
		}

		&-description-tabs-nav-link {
			display: block;
			margin: 0;
		}
	}

	._product-sidebar {
		&-actions {
			display: block;
		}

		._tags {
			display: block;
			margin-bottom: 20px;
			padding-left: 0;
			text-align: left;
		}
	}

	._related-products-bottom {
		padding-top: 30px;
		margin-top: 0;
	}

	._pagination {
		text-align: center;

		.first,
		.last {
			display: none;
		}
	}

	._showcase-item-info {
		text-align: center;
	}

	/* #Home Text
	-------------------------------------------------------------------------------*/

	._home-text ._text-title {
		h6 {
			._h2();
		}
	}

	/* #Vendors
	-------------------------------------------------------------------------------*/

	._vendors-list {
		._vendor {
			width: 50.00%;
			margin-bottom: 25px;

			&:nth-child(3n+1) {
				clear: none;
			}

			&:nth-child(2n+1) {
				clear: both;
			}
		}
	}

	/* #Blog
	-------------------------------------------------------------------------------*/

	._blog {
		&-list {
			&-article {
				width: 50%;
			}
		}

		&-article {
			&-image {
				float: none;
				max-width: 100%;
				margin: 0 0 20px;
			}

			&-title {
				padding-bottom: 13px;
				margin-bottom: 12px;
			}
		}
	}

	/* #Contacts
	-------------------------------------------------------------------------------*/

	.page-contacts {
		._content {
			padding-top: 0;
		}
	}

	._contact {
		&-info {
			&-container {
				margin: 15px 0;
				float: none;
				width: auto;
			}
		}
		
		&-form {
			._button {
				width: 100%;
			}
		}
	}

	._google-map {
		position: relative;
		height: 300px !important;
	}

	/* #Account
	-------------------------------------------------------------------------------*/

	._sidebar-list-account {
		li {
			display: block;
			float: left;
			width: 50%;
			padding: 0;
			padding-right: 10px;

			&:nth-child(2n+1) {
				clear: both;
			}
		}
	}

	.region-holder {
		margin-bottom: 10px;
	}

	._order-details {
		&-product {
			&-image {
				margin-top: 0;
				margin-right: 15px;
				width: 25%;
			}

			&-title {
				margin-bottom: 5px;

				h2 {
					font-size: 18px;
				}
			}

			&-price {
				&-total {
					margin-top: 5px;
				}
			}
		}

		&-summary {
			li {
				width: 250px;
			}

			&-value {
				padding-right: 0;
			}
		}
	}

	/* #Cart
	-------------------------------------------------------------------------------*/

	._cart {
		&-product {
			&-image {
				padding-top: 3px;
				width: 150px;
			}

			&-name {
				h4 {
					font-size: 20px;
				}
			}

			&-box {
				padding-left: 15px;
			}

			&-quantity {
				left: 165px;

				.input-group {
					width: 120px;
				}
			}
		}

		&-totals-box {
			font-size: @font-size-xsmall; /* theme */

			.final-total {
				font-size: calc(@font-size-main ~'-' 1px); /* theme */
			}
		}
	}

	/* #Checkout
	-------------------------------------------------------------------------------*/

	._checkout {
		&-payment-providers {
			._form-col {
				margin-bottom: 0;
			}
		}

		&-account {
			display: block;

			&-tabs {
				display: block;
			}

			&-forms {
				display: block;
			}

			._form-row {
				margin-bottom: 10px;
			}
		}
	}

	._checkout-account-tabs, 
	._address-book-controls,
	._checkout-payment-providers,
	._checkout-shipping-providers-main {
		._radio {
			font-size: 16px;

			.radio {
				top: 7px;
			}
		}
	}

	._checkout-shipping-provider-details {
		._radio {
			.radio {
				top: 50%;
			}
		}
	}

	._checkout-shipping-providers-main {
		padding-bottom: 10px;
		margin-bottom: 10px;
	}

	._checkout-payment-providers {
		._form {
			._form-row {
				._form-col {
					+ ._form-col {
						padding-top: 10px;
						margin-top: 10px;
					}
				}
			}
		}
	}

    ._checkout-shipping-providers-list {
        width: auto;
        margin-left: 0;
        margin-right: 0;

        li {
            width: 100%;
            padding-left: 0;
            padding-right: 0;
        }
    }

    ._checkout-shipping-provider-features-form {
        [class*='col'] {
			margin-top: 15px;

			&:first-child {
				margin-top: 0;
			}
        }
    }

    ._checkout-shipping-providers-controls {
    	border-bottom: 0;

    	._radio {
    		width: auto;
    		display: block;
    		margin: 0 0 5px;

    		&:last-child {
    			margin-bottom: 0;
    		}
    	}
    }


	._blog-list-article-image img {
		height: auto;
	}

	._blog-list-articles {
		display: flex;
		flex-wrap: wrap;

		&:before {
			content: none;
		}

		&:after {
			content: '';
		}
	}
}

