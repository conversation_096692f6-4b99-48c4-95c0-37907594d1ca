/*=============================================================================*\
    EXTRA SMALL DEVICES / PHONES
\*=============================================================================*/

@media (max-width: @screen-xs-max) {
	.modal-dialog {
		margin: 15px;

		._wrapper {
			padding: 10px 0;
		}
	}

	._header {
		transition: none;

		&__logo ._logo {
			margin: 15px 0;
		}
	}

	._breadcrumb {
		display: none;
	}

	._form {
		._form-row {
			.flexy-row-mobile(16px);
			._form-col {
				float: none;
				width: auto;
				.flexy-col-mobile();
			}
			._form-actions {
				text-align: center;
				._button {
					float: none;
					margin-left: 0;
					width: 100%;
					& + ._button {
						margin-top: 15px;
					}
				}
			}
		}

        ._form-row-secondary {
            display: block;
            margin-bottom: 10px;

            ._form-col-secondary {
                float: none;
                display: block;
                margin-bottom: 10px;
                width: auto;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
	}

	._product {
		._product-info {
			._product-options {
				._product-price {
					position: static;
					top: 0;
					left: 0;
					transform: none;
					._product-price-old {
					}
				}
				._product-add {
					float: none;
					margin-top: 15px;
					._button {
						width: 100%;
					}
				}
			}
		}
	}

	._product-details-properties {
		ul {
			display: block;
			width: auto;
			li {
				display: block;
				> span {
					display: block;
					&._product-details-properties-value {
						text-align: left;
					}
				}
				& + li {
					margin-top: 10px;
				}
			}
		}
	}

	._address-details {
		ul {
			list-style: none;
			li {
				._address-details-label {
					float: none;
					display: block;
				}
				._address-details-value {
					float: none;
					display: block;
				}
				& + li {
					margin-top: 10px;
				}
			}
		}
	}

	#toast-container {
		&.toast-top-left {
			right: 10px;
		}
		&.toast-top-right {
			left: 10px;
		}
		&.toast-bottom-right {
			left: 10px;
		}
		&.toast-bottom-left {
			right: 10px;
		}
	}

	._mobile-menu {
		max-width: 61.8%;
	}

	._home-slider ._scroll-down {
		width: 45px;
		height: 45px;
		bottom: 20px;

		i {
			font-size: 28px;
		}
	}

	._slider {
		padding: 0;
	}

	._home-text-description {

		._text {
			flex-flow: column;
			align-items: flex-start;
			padding: 0;

			._text-title h6 {
				padding: 0 0 0 10px;

				& when (@rtl) {
					padding: 0 10px 0 0;
				}
			}

			._text-description {
				width: 100%;
				margin-top: 25px;
				padding-left: 0px;
			}
		}
	}

    ._home-banners {
        ._banners {
            &:before {
                display: none;
            }
            ._banners-list {
                position: static;
                columns: auto;
                ._banner {
                    height: auto;
                    img {
                        position: static;
                        transform: translate(0, 0);
                        height: auto;
                        width: 100%;
                    }
                }
                &[class*='count-1'],
                &[class*='count-3'],
                &[class*='count-5'] {
                    ._banner {
                        &:last-child {
                            height: auto;
                        }
                    }
                }
            }
        }
    }

	._home-products ._showcase-product {
		._product ._product-info {
			
			._product-name h3 {
				font-size: calc(@font-size-heading-2 ~'*' @golden-ratio-ngt ~'-' 6px); /* theme */
			}

			._product-options {
				._product-price {
					font-size: @font-size-product-price * @golden-ratio-ngt;
				}

				._product-add ._button {
					width: auto;
				}
			}	
		} 
	}

	._blog-main {
		padding: 40px 0;

		._blog-list-article {
			width: 100%;
		}

		._blog-list-article-info {
			padding: 0;
		}
	}

	._product-details-price ._product-details-price-bar > span {
		&._product-details-price-new {
			font-size: @font-size-product-price * @golden-ratio-ngt;
		}
	}

	._product-details-desription > h4 {
		font-size: calc(@font-size-heading-4 ~'*' @golden-ratio-ngt); /* theme */
	}

	._breadcrumb-holder {
		padding: 10px 0;

		&__titles {

			&--cart {
				padding: 0;
			}

			> h3 {
				font-size: calc(@font-size-heading-4 ~'*' @golden-ratio-ngt); /* theme */
			}
		}
	}

	._vendors-list {
		margin: 0;
	}

	._pagination {
		text-align: center;
	}

	._contactus-holder {
		padding-bottom: 40px;
	}

	._page-banners {
		margin-top: 30px;
	}

	._static-page {
		padding: 30px 15px;

		._theme-titles {
			margin-bottom: 30px;
		}
	}

	._single-blog {
		margin-bottom: 10px;

		._blog-main ._blog-article ._section-title {
			font-size: @font-size-blog-title * @golden-ratio-ngt;
			margin-top: 50px;
		} 
	}
	
	._order-details {
		._order-details-meta {
			._addresses, ._date-status {
				flex-flow: column;
				align-items: flex-start;
				margin-bottom: 15px;

				> div {
					margin-bottom: 10px;
				}
			}
		}
	}

	.modal-dialog {
		._popup-title h4 {
			font-size: @font-size-heading-5; /* theme */
			padding-right: 15px;
		}

		._newsletter-popup ._field {
			padding: 0 10px;
		}

		&.continue-shopping {
			width: 100%;
			max-width: 500px;
			margin: 0;

			._popup-title h4 {
				font-size: calc(@font-size-heading-5 ~'*' @golden-ratio-ngt); /* theme */
			}
		}

		&.confirm {
			width: ~'calc(100% - 30px)';
			max-width: 450px;
			margin: 0 auto;
		}

		&.address-edit, &.product-details {

			.modal-content .modal-body .close {
				top: 0;
			}
		}
	}

	._related-products ._home-text-description h6 {
		font-size: calc(@font-size-heading-4 ~'*' @golden-ratio-ngt); /* theme */
	}

	._checkout-cart {
		padding: 40px 15px;
	}

	._cart-products ._cart-products-holder ._cart-products-list ._cart-product {
		padding: 20px;
		margin-bottom: 20px;

		._remove {
			top: 20px;
			right: 20px;
		}

		> div {
			&._cart-product-details ._cart-product-info {
				height: auto;

				._cart-product-box {
					padding-bottom: 0;
				}
			}

			&._cart-product-quantity {
				position: relative;
				width: 150px;
				left: 0;
				bottom: 0;
				text-align: center;
				margin-top: 10px;
			}

			&._cart-product-total-price {
				right: auto;
				left: 195px;
			}
		}
	}

    ._checkout-shipping-providers-list {
        width: auto;
        margin-left: 0;
        margin-right: 0;

        li {
            width: 100%;
            padding-left: 0;
            padding-right: 0;
        }
    }

    ._checkout-shipping-provider-features-form {
        [class*='col'] {
            margin-top: 15px;

            &:first-child {
                margin-top: 0;
            }
        }
    }

    ._checkout-payment-providers {
        ._form {
            ._form-row {
                ._form-col {
                    padding-bottom: 0;
                }
            }
        }
    }

	._google-map {
		height: 400px !important;
	}

	._products-head__item {

		&__title, &__titleBrand {
			font-size: calc(@font-size-heading-4 ~'*' @golden-ratio-ngt ~'+' 2px); /* theme */
		}

		&__image {
			@media all and (-ms-high-contrast: none) {
				height: 250px;
			}

			min-height: 250px;
			background-size: cover;
		}
	}

	._home-advertise ._text-title h6 {
		font-size: calc(@font-size-heading-2 ~'*' @golden-ratio-ngt ~'-' 5px); /* theme */
	}

	._home-video {

		._text-title h6 {
			font-size: calc(@font-size-heading-2 ~'*' @golden-ratio-ngt ~'-' 5px); /* theme */
		}

		table {
			display: block;
			height: auto !important;
			border: none !important;

			td, tr {
				display: block;
				width: 100% !important;
			}
		}
	}

	._footer {
		padding-bottom: 0;

		&__links {

			._navigation-footer > ul > li {
				width: 100%;
				max-width: 60%;
				margin-bottom: 15px;
				text-align: center;
				padding: 0;
			}
		}

		&__newsletter ._newsletter-form {
			max-width: 75%;
		}

		&__meta {
			flex-flow: column;
			justify-content: center;
			align-items: center;

			&__payments, &__text, ._social-icons {
				width: auto;
				max-width: 60%;
			}

			&__text {
				margin-top: 10px;
				flex-flow: column;
				text-align: center;

				._powered {
					padding-left: 0;
					margin-left: 0;
					border: none;
				}
			}
		}
	}
}
