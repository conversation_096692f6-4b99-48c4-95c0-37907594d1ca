<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="460px" preserveAspectRatio="none" style="width:3862px;height:460px;background:#FFFFFF;" version="1.1" viewBox="0 0 3862 460" width="3862px" zoomAndPan="magnify"><defs/><g><!--MD5=[ea532a13346633c7f26d88d279da24a3]
cluster settings--><rect fill="#FFFFFF" height="271" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="3849" x="7" y="183"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="304" x="1779.5" y="209.8516">CloudCart Hosted e-Shop Settings</text><text fill="#444444" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="55" x="1904" y="224.7637">[System]</text><!--MD5=[95d95589164a1b5e63f208bfe495213e]
cluster msets--><rect fill="#FFFFFF" height="178" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="1842" x="31" y="252"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="120" x="892" y="278.8516">Main settings</text><!--MD5=[6a113c0af3742ae6330d419961e3944c]
cluster set_others--><rect fill="#FFFFFF" height="178" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="1935" x="1897" y="252"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="131" x="2799" y="278.8516">Other settings</text><!--MD5=[852837f99792766f0c6c57cfe39a6dab]
entity mains--><rect fill="#438DD5" height="115.4531" style="stroke:#3C7FC0;stroke-width:1.5;" width="217" x="47.5" y="299"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="119" y="320.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="74" x="119" y="337.8203">Settings</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="40" x="136" y="352.7324">[main]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="158" y="368.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="189" x="61.5" y="384.8545">Manage configuration basic</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="95" x="108.5" y="401.1514">shop settings</text><!--MD5=[5816f402582fb3aba51d93eaddd35e6b]
entity carts--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="169" x="299.5" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="347" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="118" x="325" y="345.8203">Cart Settings</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="35" x="366.5" y="360.7324">[cart]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="386" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="145" x="313.5" y="392.8545">Manage cart settings</text><!--MD5=[f1b4b0af7e8f60ee326ab922b086315b]
entity invoices--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="125" x="503.5" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="529" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="73" x="529.5" y="345.8203">Invoices</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="59" x="536.5" y="360.7324">[invoices]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="568" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="101" x="517.5" y="392.8545">Setup invoices</text><!--MD5=[102ed5e1371977dc78c8e105c53633a9]
entity payment_s--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="137" x="663.5" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="695" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="88" x="688" y="345.8203">Payments</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="71" x="696.5" y="360.7324">[payments]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="734" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="113" x="677.5" y="392.8545">Setup payments</text><!--MD5=[80643ff6d6574530eccd95f72bd3083a]
entity shipping_s--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="128" x="836" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="863" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="74" x="863" y="345.8203">Shipping</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="68" x="866" y="360.7324">[shippings]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="902" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="104" x="850" y="392.8545">Setup shipping</text><!--MD5=[8209cc7f5f0a18206b668b487cce6ac4]
entity zones_s--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="168" x="999" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="1046" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="96" x="1035" y="345.8203">Geo Zones</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="46" x="1060" y="360.7324">[zones]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="1085" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="144" x="1013" y="392.8545">Setup delivery zones</text><!--MD5=[6a427d0191be5f82b8006da270c391b3]
entity taxes_s--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="209" x="1202.5" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="1270" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="52" x="1281" y="345.8203">Taxes</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="44" x="1285" y="360.7324">[taxes]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="1309" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="185" x="1216.5" y="392.8545">Setup shop taxes and fees</text><!--MD5=[8b5e480064da38e3dc29205f097ab5b2]
entity domain_s--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="167" x="1446.5" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="1493" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="75" x="1492.5" y="345.8203">Domains</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="62" x="1499" y="360.7324">[domains]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="1532" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="143" x="1460.5" y="392.8545">Setup shop domains</text><!--MD5=[915f5931b569308fae8342bdd5974320]
entity emails_s--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="208" x="1649" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="1716" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="62" x="1722" y="345.8203">e-mails</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="49" x="1728.5" y="360.7324">[emails]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="1755" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="184" x="1663" y="392.8545">Setup and manage e-mails</text><!--MD5=[7db9829d5a9baadc696e67ebb2da8ccf]
entity translations--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="193" x="1913.5" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="1973" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="108" x="1956" y="345.8203">Translations</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="83" x="1968.5" y="360.7324">[translations]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="2012" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="169" x="1927.5" y="392.8545">Manage text translations</text><!--MD5=[f153cf1bc1c3aeb2fc55d24ad41a1c04]
entity files_s--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="149" x="2141.5" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="2179" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="86" x="2173" y="345.8203">Shop files</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="64" x="2184" y="360.7324">[shopfiles]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="2218" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="125" x="2155.5" y="392.8545">Manage shop files</text><!--MD5=[63283ec5261d26b2b0219aa80d2c1146]
entity statuses--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="143" x="2325.5" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="2360" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="79" x="2357.5" y="345.8203">Statuses</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="82" x="2356" y="360.7324">[orderstatus]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="2399" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="119" x="2339.5" y="392.8545">Manage statuses</text><!--MD5=[e803e4feaa440f299ec14a603035bb55]
entity custfiles--><rect fill="#438DD5" height="115.4531" style="stroke:#3C7FC0;stroke-width:1.5;" width="220" x="2504" y="299"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="2577" y="320.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="129" x="2549.5" y="337.8203">Customer files</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="60" x="2584" y="352.7324">[custfiles]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="2616" y="368.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="192" x="2518" y="384.8545">View and manage customer</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="55" x="2586.5" y="401.1514">uploads</text><!--MD5=[4b6979a1ad1995f7d57bb3bcbd42a7d8]
entity apikeys--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="140" x="2759" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="2792" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="78" x="2790" y="345.8203">API Keys</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="57" x="2800.5" y="360.7324">[apikeys]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="2831" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="116" x="2773" y="392.8545">Manage API keys</text><!--MD5=[b0477f5457adcd28cf10ad3fbae138d7]
entity webhooks--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="159" x="2934.5" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="2977" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="101" x="2963.5" y="345.8203">Web Hooks</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="73" x="2977.5" y="360.7324">[webhooks]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="3016" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="135" x="2948.5" y="392.8545">Manage web hooks</text><!--MD5=[a8faa48a8c0cc65b4e804b4b29012dbd]
entity deliveryboxes--><rect fill="#438DD5" height="115.4531" style="stroke:#3C7FC0;stroke-width:1.5;" width="215" x="3128.5" y="299"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="3199" y="320.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="130" x="3171" y="337.8203">Delivery boxes</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="95" x="3188.5" y="352.7324">[deliveryboxes]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="3238" y="368.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="187" x="3142.5" y="384.8545">Manage delivery boxes and</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="83" x="3194.5" y="401.1514">dementions</text><!--MD5=[2f6556df18f7c7c6e5d7b1d38c46f5ca]
entity myshops--><rect fill="#438DD5" height="115.4531" style="stroke:#3C7FC0;stroke-width:1.5;" width="220" x="3379" y="299"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="3452" y="320.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="54" x="3462" y="337.8203">Shops</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="60" x="3459" y="352.7324">[custfiles]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="3491" y="368.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="192" x="3393" y="384.8545">View and manage customer</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="55" x="3461.5" y="401.1514">uploads</text><!--MD5=[db7a3aea0a3a703e34bd1f1e8de0074c]
entity queuejobs--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="182" x="3634" y="307"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="3688" y="328.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="38" x="3706" y="345.8203">Jobs</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="75" x="3687.5" y="360.7324">[queuejobs]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="3727" y="376.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="158" x="3648" y="392.8545">View current shop jobs</text><!--MD5=[8fc3522a43f8c7199df5e09e5bb0188e]
entity user--><rect fill="#08427B" height="100.5938" style="stroke:#073B6F;stroke-width:1.5;" width="177" x="1749.5" y="7"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="57" x="1809.5" y="28.1387">«person»</text><image height="48" width="48" x="1814" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=" y="30.9688"/><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="157" x="1759.5" y="93.8203">Murchant / Admin</text><!--MD5=[96ac4182009f63c769a2652dc2c69561]
link user to mains--><path d="M1749.22,58.56 C1528.11,59.79 935.4,69.75 447,138 C372.57,148.4 344.41,133.13 282,175 C239.28,203.67 206.64,252.1 185.3,291.58 " fill="none" id="user-to-mains" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="181.45,298.84,187.8576,293.1882,182.5614,290.3686,181.45,298.84" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="451.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="448" y="164.1074">[https]</text><!--MD5=[59da84808e1bf5c28c7fe38dd63e7ab9]
link user to carts--><path d="M1749.12,61.49 C1504.49,70.17 824.89,97.32 605,138 C550.54,148.07 529.1,140.22 486,175 C446.21,207.1 419.18,259.14 402.99,299.12 " fill="none" id="user-to-carts" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="399.9,306.94,405.6356,300.6073,400.0573,298.3974,399.9,306.94" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="609.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="606" y="164.1074">[https]</text><!--MD5=[15499f196ed13a2300494370bb68f90e]
link user to invoices--><path d="M1749.22,59.16 C1566.06,61.85 1133.52,74.95 777,138 C717.42,148.54 691.71,135.36 646,175 C609.22,206.9 588.63,258.96 577.58,299 " fill="none" id="user-to-invoices" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="575.5,306.83,580.4568,299.8708,574.6587,298.3275,575.5,306.83" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="781.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="778" y="164.1074">[https]</text><!--MD5=[6eb2139081fc29d1667d8b27c40e24a1]
link user to payment_s--><path d="M1749.36,61.04 C1588.76,66.84 1239.46,84.94 950,138 C890.07,148.99 864.61,135.76 818,175 C780.28,206.75 757.95,258.83 745.53,298.91 " fill="none" id="user-to-payment_s" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="743.18,306.75,748.3546,299.9512,742.6082,298.2251,743.18,306.75" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="954.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="951" y="164.1074">[https]</text><!--MD5=[419b0d35762eeb194e62fe15d8821cf7]
link user to shipping_s--><path d="M1749.28,59.2 C1554.49,62.49 1097.47,80.38 981,175 C943.01,205.86 922.22,258.29 911.25,298.72 " fill="none" id="user-to-shipping_s" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="909.18,306.63,914.0975,299.643,908.2908,298.1324,909.18,306.63" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="1046.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="1043" y="164.1074">[https]</text><!--MD5=[c4209364ad2bd68a7f2ca1b801c71305]
link user to zones_s--><path d="M1749.21,65.02 C1592.39,78.04 1274.5,111.58 1185,175 C1142.79,204.91 1115.82,258.14 1100.29,299.07 " fill="none" id="user-to-zones_s" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1097.43,306.82,1103.0169,300.3558,1097.3889,298.2761,1097.43,306.82" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="1275.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="1272" y="164.1074">[https]</text><!--MD5=[d36ca4a545239e63b0dc203fa0d15659]
link user to taxes_s--><path d="M1749.36,66.07 C1660.94,77.43 1524.81,105.59 1429,175 C1385.31,206.65 1352.44,259.06 1331.89,299.27 " fill="none" id="user-to-taxes_s" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1328.19,306.63,1334.4695,300.8363,1329.1116,298.1358,1328.19,306.63" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="1492.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="1489" y="164.1074">[https]</text><!--MD5=[3bcf76e7d731e5443f0643a5055a8d3d]
link user to domain_s--><path d="M1749.25,95.44 C1709.98,114.91 1665.26,141.73 1632,175 C1596.57,210.44 1569.51,260.72 1552.19,299.21 " fill="none" id="user-to-domain_s" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1548.86,306.74,1554.845,300.6425,1549.3599,298.2106,1548.86,306.74" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="1680.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="1677" y="164.1074">[https]</text><!--MD5=[115a2e478e04d1910c8d9bb10697bf41]
link user to emails_s--><path d="M1823.77,108.21 C1808.73,160.78 1785.03,243.58 1769.26,298.69 " fill="none" id="user-to-emails_s" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1766.99,306.62,1772.0757,299.7545,1766.3074,298.1033,1766.99,306.62" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="1819.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="1816" y="164.1074">[https]</text><!--MD5=[d1295b3a86679e8b54d97a9fb6a178b2]
link user to translations--><path d="M1866.79,108.21 C1897.36,161 1945.58,244.26 1977.49,299.37 " fill="none" id="user-to-translations" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1981.69,306.62,1980.2677,298.1952,1975.0788,301.2078,1981.69,306.62" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="1902.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="1899" y="164.1074">[https]</text><!--MD5=[7cdd9b6f8e4f0253250752f378a36c89]
link user to files_s--><path d="M1926.58,77.21 C1988.09,94.03 2068.63,124.12 2124,175 C2160.42,208.47 2184.59,259.63 2198.98,298.95 " fill="none" id="user-to-files_s" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="2201.72,306.65,2201.8432,298.1069,2196.1955,300.1324,2201.72,306.65" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="2115.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="2112" y="164.1074">[https]</text><!--MD5=[26b0c77a638ef7c4f5488cea9eed7d53]
link user to statuses--><path d="M1926.63,58.76 C2029.16,63.55 2197.97,85.79 2308,175 C2346.67,206.36 2369.87,258.73 2382.84,299.01 " fill="none" id="user-to-statuses" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="2385.29,306.9,2385.77,298.3695,2380.0425,300.1573,2385.29,306.9" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="2291.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="2288" y="164.1074">[https]</text><!--MD5=[91205d7f59d2df8bc6873849f8e9b7f6]
link user to custfiles--><path d="M1926.52,65.45 C2082.03,79.11 2396.28,113.58 2486,175 C2528.53,204.11 2561.63,252.28 2583.51,291.54 " fill="none" id="user-to-custfiles" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="2587.47,298.76,2586.2572,290.3025,2580.9952,293.1854,2587.47,298.76" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="2473.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="2470" y="164.1074">[https]</text><!--MD5=[af7f4ece4dcb7a3f9ce816c50b9ddea1]
link user to apikeys--><path d="M1926.51,59.09 C2128.24,62.1 2614.56,79.37 2742,175 C2781.99,205.01 2804.5,258.22 2816.55,299.12 " fill="none" id="user-to-apikeys" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="2818.76,306.87,2819.4573,298.3545,2813.6861,299.9957,2818.76,306.87" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="2723.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="2720" y="164.1074">[https]</text><!--MD5=[decb7ef992633265b56220c3c7128efe]
link user to webhooks--><path d="M1926.72,61.24 C2093.25,67.45 2463.6,86.31 2771,138 C2837.01,149.1 2864.24,133.8 2917,175 C2957.04,206.26 2982.59,258.81 2997.36,299.17 " fill="none" id="user-to-webhooks" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="3000.08,306.81,3000.2227,298.2672,2994.5704,300.2798,3000.08,306.81" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="2908.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="2905" y="164.1074">[https]</text><!--MD5=[47297395e2b78eb848a410fe30141c65]
link user to deliveryboxes--><path d="M1926.6,59.89 C2116.4,63.95 2575.92,79.52 2956,138 C3026,148.77 3052.53,135.03 3111,175 C3153.21,203.86 3185.51,252.04 3206.69,291.37 " fill="none" id="user-to-deliveryboxes" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="3210.52,298.6,3209.429,290.1259,3204.1261,292.9328,3210.52,298.6" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="3101.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="3098" y="164.1074">[https]</text><!--MD5=[fdf3de0dc7ec5921992a6b29b6247f0d]
link user to myshops--><path d="M1926.54,60.44 C2138.38,65.83 2690.96,84.31 3149,138 C3244,149.13 3280.13,123.92 3361,175 C3404.88,202.72 3438.09,251.44 3459.68,291.29 " fill="none" id="user-to-myshops" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="3463.57,298.62,3462.4657,290.1477,3457.1671,292.9629,3463.57,298.62" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="3350.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="3347" y="164.1074">[https]</text><!--MD5=[a5bb3532a3649d7426f25d8d548168e7]
link user to queuejobs--><path d="M1926.68,59.41 C2165.24,62.63 2840.32,76.89 3398,138 C3496.13,148.75 3535.5,119.3 3617,175 C3660.43,204.68 3689.18,258.1 3706.02,299.15 " fill="none" id="user-to-queuejobs" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="3709.02,306.66,3708.8343,298.118,3703.2634,300.3464,3709.02,306.66" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="3606.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="3603" y="164.1074">[https]</text><!--MD5=[c74cc76603cce8c110ad6dc33b2b9e09]
@startuml LAYOUT_TOP_DOWN Sample
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml
!define DEVICONS https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/devicons
!define FONTAWESOME https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/font-awesome-5
!include FONTAWESOME/users.puml


Person(user, "Murchant / Admin")

System_Boundary(settings, 'CloudCart Hosted e-Shop Settings') {
    Boundary(msets, 'Main settings') {
    Container(mains, "Settings", "main", "Manage configuration basic shop settings")
    Container(carts, "Cart Settings", "cart", "Manage cart settings")
    Container(invoices, "Invoices", "invoices", "Setup invoices")
    Container(payment_s, "Payments", "payments", "Setup payments")
    Container(shipping_s, "Shipping", "shippings", "Setup shipping")
    Container(zones_s, "Geo Zones", "zones", "Setup delivery zones")
    Container(taxes_s, "Taxes", "taxes", "Setup shop taxes and fees")
    Container(domain_s, "Domains", "domains", "Setup shop domains")
    Container(emails_s, "e-mails", "emails", "Setup and manage e-mails")
    }
    Boundary(set_others, 'Other settings') {
        Container(translations, "Translations", "translations", "Manage text translations")
        Container(files_s, "Shop files", "shopfiles", "Manage shop files")
        Container(statuses, "Statuses", "orderstatus", "Manage statuses")
        Container(custfiles, "Customer files", "custfiles", "View and manage customer uploads")
        Container(apikeys, "API Keys", "apikeys", "Manage API keys")
        Container(custfiles, "Customer files", "custfiles", "View and manage customer uploads")
        Container(webhooks, "Web Hooks", "webhooks", "Manage web hooks")
        Container(deliveryboxes, "Delivery boxes", "deliveryboxes", "Manage delivery boxes and dementions")
        Container(myshops, "Shops", "custfiles", "View and manage customer uploads")
        Container(queuejobs, "Jobs", "queuejobs", "View current shop jobs")
    }
}


Rel(user, mains, "Uses", "https")
Rel(user, carts, "Uses", "https")
Rel(user, invoices, "Uses", "https")
Rel(user, payment_s, "Uses", "https")
Rel(user, shipping_s, "Uses", "https")
Rel(user, zones_s, "Uses", "https")
Rel(user, taxes_s, "Uses", "https")
Rel(user, domain_s, "Uses", "https")
Rel(user, emails_s, "Uses", "https")
Rel(user, translations, "Uses", "https")
Rel(user, files_s, "Uses", "https")
Rel(user, statuses, "Uses", "https")
Rel(user, custfiles, "Uses", "https")
Rel(user, apikeys, "Uses", "https")
Rel(user, webhooks, "Uses", "https")
Rel(user, deliveryboxes, "Uses", "https")
Rel(user, myshops, "Uses", "https")
Rel(user, queuejobs, "Uses", "https")



@enduml

@startuml LAYOUT_TOP_DOWN Sample
















skinparam defaultTextAlignment center

skinparam wrapWidth 200
skinparam maxMessageSize 150

skinparam LegendBorderColor transparent
skinparam LegendBackgroundColor transparent
skinparam LegendFontColor #FFFFFF

skinparam shadowing<<legendArea>> false
skinparam rectangle<<legendArea>> {
    backgroundcolor #00000000
    bordercolor #00000000
}

skinparam rectangle {
    StereotypeFontSize 12
    shadowing false
}

skinparam database {
    StereotypeFontSize 12
    shadowing false
}

skinparam queue {
    StereotypeFontSize 12
    shadowing false
}

skinparam arrow {
    Color #666666
    FontColor #666666
    FontSize 12
}

skinparam actor {
    StereotypeFontSize 12
    shadowing false
    style awesome
}

skinparam person {
    StereotypeFontSize 12
    shadowing false
}

skinparam package {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    FontStyle plain
    BackgroundColor transparent
}

skinparam rectangle<<boundary>> {
    Shadowing false
    StereotypeFontSize 6
    StereotypeFontColor transparent
    FontColor #444444
    BorderColor #444444
    BackgroundColor transparent
    BorderStyle dashed
}






































































































skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}


skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}


skinparam rectangle<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam database<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam queue<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam actor<<system>> {
    StereotypeFontColor #1168BD
    FontColor #1168BD
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam person<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}


skinparam rectangle<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam database<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam queue<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam actor<<external_system>> {
    StereotypeFontColor #999999
    FontColor #999999
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam person<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}





sprite $person [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000025788300000000005886410000000000000
000000000007DFFFFFFD9643347BFFFFFFFB400000000000
0000000004EFFFFFFFFFFFFFFFFFFFFFFFFFFB1000000000
000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD200000000
00000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE10000000
0000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB0000000
000000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF5000000
000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000
000009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF200000
00000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF600000
00000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF800000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00000EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF700000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
0000008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD3000000
000000014555555555555555555555555555555300000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $person2 [48x48/16] {
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000002578888300000000005888864100000000000
0000000007DFFFFFFFFD9643347BFFFFFFFFFB4000000000
00000004EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB10000000
0000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD2000000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
00003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
0000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF50000
0003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD0000
0009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
0009FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF2000
0003FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFD0000
0000BFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF50000
00003FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFB00000
000006FFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFE100000
0000007FFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFD2000000
00000004EFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFB10000000
0000000007DF8FFFFFFFFFFFFFFFFFFFFFF8FB4000000000
000000000002578888888888888888888864100000000000
}


























skinparam rectangle<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam database<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam queue<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam actor<<container>> {
    StereotypeFontColor #438DD5
    FontColor #438DD5
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam person<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}


skinparam rectangle<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam database<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam queue<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam actor<<external_container>> {
    StereotypeFontColor #B3B3B3
    FontColor #B3B3B3
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam person<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
















sprite $users [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000024310000000000000000000000
000000000000000000018EFFFFC400000000000000000000
0000000000000000003EFFFFFFFF90000000000000000000
000000000000000002EFFFFFFFFFF9000000000000000000
00000000000000000CFFFFFFFFFFFF400000000000000000
0000006DFE8000004FFFFFFFFFFFFFC000004BFEA2000000
000009FFFFFC00009FFFFFFFFFFFFFF10005FFFFFE200000
00003FFFFFFF8000BFFFFFFFFFFFFFF3001FFFFFFFC00000
00009FFFFFFFD000CFFFFFFFFFFFFFF4005FFFFFFFF10000
0000AFFFFFFFE000AFFFFFFFFFFFFFF2006FFFFFFFF20000
00007FFFFFFFC0006FFFFFFFFFFFFFE0004FFFFFFFF00000
00001FFFFFFF50001FFFFFFFFFFFFF80000DFFFFFF900000
000004FFFFF8000007FFFFFFFFFFFE100002DFFFFB000000
000000179830000000AFFFFFFFFFF3000000069940000000
00000000000000000007FFFFFFFC20000000000000000000
0000012333310000000016ACB94000000000233332000000
0001BFFFFFFFD3000000000000000000008FFFFFFFE60000
000DFFFFFFFFE400000000000000000000AFFFFFFFFF6000
007FFFFFFFFD1006BEFE8310025BFFD92006FFFFFFFFF000
00CFFFFFFFE102DFFFFFFFFFFFFFFFFFF8007FFFFFFFF400
00DFFFFFFF302FFFFFFFFFFFFFFFFFFFFFA00BFFFFFFF500
00DFFFFFFB00EFFFFFFFFFFFFFFFFFFFFFF603FFFFFFF500
00CFFFFFF407FFFFFFFFFFFFFFFFFFFFFFFE00CFFFFFF400
006FFFFFF00DFFFFFFFFFFFFFFFFFFFFFFFF508FFFFFD000
00037777601FFFFFFFFFFFFFFFFFFFFFFFFF902777761000
00000000002FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000000FFFFFFFFFFFFFFFFFFFFFFFFF800000000000
000000000009FFFFFFFFFFFFFFFFFFFFFFFF200000000000
0000000000007DEEEEEEEEEEEEEEEEEEEEB2000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<FA5 USERS>> White


rectangle "<$person>\n==Murchant / Admin" <<person>> as user 

rectangle "==CloudCart Hosted e-Shop Settings\n<size:12>[System]</size>" <<boundary>> as settings  {
    rectangle "==Main settings" <<boundary>> as msets  {
    rectangle "==Settings\n//<size:12>[main]</size>//\n\n Manage configuration basic shop settings" <<container>> as mains 
    rectangle "==Cart Settings\n//<size:12>[cart]</size>//\n\n Manage cart settings" <<container>> as carts 
    rectangle "==Invoices\n//<size:12>[invoices]</size>//\n\n Setup invoices" <<container>> as invoices 
    rectangle "==Payments\n//<size:12>[payments]</size>//\n\n Setup payments" <<container>> as payment_s 
    rectangle "==Shipping\n//<size:12>[shippings]</size>//\n\n Setup shipping" <<container>> as shipping_s 
    rectangle "==Geo Zones\n//<size:12>[zones]</size>//\n\n Setup delivery zones" <<container>> as zones_s 
    rectangle "==Taxes\n//<size:12>[taxes]</size>//\n\n Setup shop taxes and fees" <<container>> as taxes_s 
    rectangle "==Domains\n//<size:12>[domains]</size>//\n\n Setup shop domains" <<container>> as domain_s 
    rectangle "==e-mails\n//<size:12>[emails]</size>//\n\n Setup and manage e-mails" <<container>> as emails_s 
    }
    rectangle "==Other settings" <<boundary>> as set_others  {
        rectangle "==Translations\n//<size:12>[translations]</size>//\n\n Manage text translations" <<container>> as translations 
        rectangle "==Shop files\n//<size:12>[shopfiles]</size>//\n\n Manage shop files" <<container>> as files_s 
        rectangle "==Statuses\n//<size:12>[orderstatus]</size>//\n\n Manage statuses" <<container>> as statuses 
        rectangle "==Customer files\n//<size:12>[custfiles]</size>//\n\n View and manage customer uploads" <<container>> as custfiles 
        rectangle "==API Keys\n//<size:12>[apikeys]</size>//\n\n Manage API keys" <<container>> as apikeys 
        rectangle "==Customer files\n//<size:12>[custfiles]</size>//\n\n View and manage customer uploads" <<container>> as custfiles 
        rectangle "==Web Hooks\n//<size:12>[webhooks]</size>//\n\n Manage web hooks" <<container>> as webhooks 
        rectangle "==Delivery boxes\n//<size:12>[deliveryboxes]</size>//\n\n Manage delivery boxes and dementions" <<container>> as deliveryboxes 
        rectangle "==Shops\n//<size:12>[custfiles]</size>//\n\n View and manage customer uploads" <<container>> as myshops 
        rectangle "==Jobs\n//<size:12>[queuejobs]</size>//\n\n View current shop jobs" <<container>> as queuejobs 
    }
}


user - ->> mains : **Uses**\n//<size:12>[https]</size>//
user - ->> carts : **Uses**\n//<size:12>[https]</size>//
user - ->> invoices : **Uses**\n//<size:12>[https]</size>//
user - ->> payment_s : **Uses**\n//<size:12>[https]</size>//
user - ->> shipping_s : **Uses**\n//<size:12>[https]</size>//
user - ->> zones_s : **Uses**\n//<size:12>[https]</size>//
user - ->> taxes_s : **Uses**\n//<size:12>[https]</size>//
user - ->> domain_s : **Uses**\n//<size:12>[https]</size>//
user - ->> emails_s : **Uses**\n//<size:12>[https]</size>//
user - ->> translations : **Uses**\n//<size:12>[https]</size>//
user - ->> files_s : **Uses**\n//<size:12>[https]</size>//
user - ->> statuses : **Uses**\n//<size:12>[https]</size>//
user - ->> custfiles : **Uses**\n//<size:12>[https]</size>//
user - ->> apikeys : **Uses**\n//<size:12>[https]</size>//
user - ->> webhooks : **Uses**\n//<size:12>[https]</size>//
user - ->> deliveryboxes : **Uses**\n//<size:12>[https]</size>//
user - ->> myshops : **Uses**\n//<size:12>[https]</size>//
user - ->> queuejobs : **Uses**\n//<size:12>[https]</size>//



@enduml

PlantUML version 1.2022.0(Tue Jan 11 18:16:42 EET 2022)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Default Encoding: UTF-8
Language: en
Country: US
--></g></svg>