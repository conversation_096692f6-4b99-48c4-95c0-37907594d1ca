<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="1359px" preserveAspectRatio="none" style="width:1228px;height:1359px;background:#FFFFFF;" version="1.1" viewBox="0 0 1228 1359" width="1228px" zoomAndPan="magnify"><defs/><g><!--MD5=[84dda40acb3410cad7262261daba2aaf]
cluster c1--><rect fill="#FFFFFF" height="1170.5" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="1025" x="7" y="183"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="196" x="421.5" y="209.8516">CloudCart Store Front</text><text fill="#444444" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="55" x="492" y="224.7637">[System]</text><!--MD5=[6611947f43423d921fd9e0708bbb2c6f]
cluster shopping--><rect fill="#FFFFFF" height="855.5" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="461" x="39" y="260"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="159" x="190" y="286.8516">Shopping Process</text><text fill="#444444" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="55" x="242" y="301.7637">[System]</text><!--MD5=[b015c0452092eefbebb331f6ad363d20]
cluster purch--><rect fill="#FFFFFF" height="810.5" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="232" x="540" y="511"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="163" x="574.5" y="537.8516">Checkout Process</text><text fill="#444444" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="55" x="628.5" y="552.7637">[System]</text><!--MD5=[b6a8a527e089c61936ec397c184afb58]
entity ccapi--><rect fill="#438DD5" height="115.4531" style="stroke:#3C7FC0;stroke-width:1.5;" width="219" x="796.5" y="776"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="869" y="797.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="77" x="867.5" y="814.8203">Rest API</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="29" x="891.5" y="829.7324">[api]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="908" y="845.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="195" x="810.5" y="861.8545">Communicates with external</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="58" x="877" y="878.1514">systems</text><!--MD5=[402f463fad944137e3b8ebede364ad65]
entity ccdb--><path d="M811,990 C811,980 906,980 906,980 C906,980 1001,980 1001,990 L1001,1094.4531 C1001,1104.4531 906,1104.4531 906,1104.4531 C906,1104.4531 811,1104.4531 811,1094.4531 L811,990 " fill="#438DD5" style="stroke:#3C7FC0;stroke-width:1.5;"/><path d="M811,990 C811,1000 906,1000 906,1000 C906,1000 1001,1000 1001,990 " fill="none" style="stroke:#3C7FC0;stroke-width:1.5;"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="869" y="1015.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="83" x="864.5" y="1032.8203">Database</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="50" x="881" y="1047.7324">[MySQL]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="908" y="1063.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="162" x="825" y="1079.8545">Holds products, orders,</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="163" x="824.5" y="1096.1514">invoice information, etc.</text><!--MD5=[0f8765db939d02cb095dc298532a191f]
entity ccpages--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="220" x="85" y="329"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="158" y="350.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="168" x="111" y="367.8203">Start/Landing page</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="88" x="151" y="382.7324">[e-Commerce]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="197" y="398.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="196" x="99" y="414.8545">View purchaseable products</text><!--MD5=[1fae857a1580b493364d3c9adf183c9d]
entity cclisting--><rect fill="#438DD5" height="115.4531" style="stroke:#3C7FC0;stroke-width:1.5;" width="166" x="151" y="572"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="197" y="593.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="129" x="169.5" y="610.8203">View Products</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="52" x="208" y="625.7324">[listings]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="236" y="641.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="138" x="165" y="657.8545">List of purchaseable</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="62" x="203" y="674.1514">products</text><!--MD5=[fe58252dba6ee369379437012bd50792]
entity prdct--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="139" x="214.5" y="784"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="247" y="805.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="119" x="224.5" y="822.8203">View Product</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="59" x="254.5" y="837.7324">[product]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="286" y="853.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="90" x="241" y="869.8545">View Product</text><!--MD5=[96f1f7967bb119dffec10ebaf5189f0e]
entity addproduct--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="200" x="100" y="992.5"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="163" y="1013.6387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="180" x="110" y="1031.3203">Add Product to Cart</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="83" x="158.5" y="1046.2324">[addproduct]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="202" y="1062.0576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="85" x="159.5" y="1078.3545">Add Product</text><!--MD5=[246412cfef43e795f3901c6cc4d04627]
entity cccart--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="110" x="597" y="580"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="615" y="601.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="56" x="624" y="618.8203">e-Cart</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="43" x="630.5" y="633.7324">[ecart]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="654" y="649.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="86" x="611" y="665.8545">Virtual e-cart</text><!--MD5=[483fe0b9850f321f8901cefa68f917f5]
entity shipping--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="172" x="566" y="784"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="615" y="805.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="152" x="576" y="822.8203">Shipping Process</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="62" x="621" y="837.7324">[shipping]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="654" y="853.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="60" x="624" y="869.8545">Shipping</text><!--MD5=[b9b1bc63773a118685e9b5feeb56bf85]
entity payment--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="176" x="564" y="992.5"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="615" y="1013.6387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="156" x="574" y="1031.3203">Payment Process</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="65" x="619.5" y="1046.2324">[payment]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="654" y="1062.0576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="60" x="624" y="1078.3545">Payment</text><!--MD5=[23c4e36b5c18ada95ae0f29f05c8534f]
entity storeorder--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="175" x="564.5" y="1198.5"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="615" y="1219.6387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="105" x="599.5" y="1237.3203">Place Order</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="50" x="627" y="1252.2324">[orders]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="654" y="1268.0576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="151" x="578.5" y="1284.3545">Complete Store Order</text><!--MD5=[8fc3522a43f8c7199df5e09e5bb0188e]
entity user--><rect fill="#08427B" height="100.5938" style="stroke:#073B6F;stroke-width:1.5;" width="117" x="433.5" y="7"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="57" x="463.5" y="28.1387">«person»</text><image height="48" width="48" x="468" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=" y="30.9688"/><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="97" x="443.5" y="93.8203">Customers</text><!--MD5=[d112aecbd7ccc75573a6ee2503a173b3]
entity email--><rect fill="#1168BD" height="52.5938" style="stroke:#3C7FC0;stroke-width:1.5;" width="77" x="1050.5" y="807"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="57" x="1060.5" y="828.1387">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="52" x="1063" y="845.8203">e-Mail</text><!--MD5=[3f23bcfe73498be80b56330100b2b1fc]
entity extsys--><rect fill="#1168BD" height="52.5938" style="stroke:#3C7FC0;stroke-width:1.5;" width="174" x="1048" y="1015.5"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="57" x="1106.5" y="1036.6387">«system»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="154" x="1058" y="1054.3203">External Systems</text><!--MD5=[891fc423142b63550eba75daddd8b575]
link user to shopping--><path d="M492,108.34 C492,130.42 492,157.9331 492,186.8681 C492,201.3356 492,216.1586 492,230.8356 C492,238.1741 492,245.4762 492,252.679 C492,254.4798 492,256.2743 492,258.0616 C492,258.5085 492,258.9548 492,259.4008 " fill="none" id="user-to-shopping" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="492,259.4008,495,251.4008,489,251.4008,492,259.4008" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="496.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="493" y="164.1074">[https]</text><!--MD5=[211c87fe88e1d8d995d5a84a0b1f8c91]
link ccpages to cclisting--><path d="M202.61,428.1 C208.67,466.79 217.21,521.3 223.85,563.7 " fill="none" id="ccpages-to-cclisting" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="225.11,571.74,226.8303,563.371,220.9032,564.3034,225.11,571.74" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="216.5" y="470.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="213" y="484.1074">[https]</text><!--MD5=[51c177dbfd89a09d1d43de07eba19c37]
link cclisting to prdct--><path d="M247.99,687.04 C254.86,714.76 263.1,748.07 269.97,775.82 " fill="none" id="cclisting-to-prdct" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="271.96,783.86,272.9448,775.3729,267.1215,776.8181,271.96,783.86" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="266.5" y="729.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="263" y="743.1074">[https]</text><!--MD5=[e7be7d2917a34cc703c1459666bad4ab]
link prdct to addproduct--><path d="M269.19,883.08 C262.43,903.92 253.94,928.39 245,950 C240.24,961.49 234.69,973.56 229.14,985.01 " fill="none" id="prdct-to-addproduct" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="225.51,992.44,231.7197,986.5714,226.3297,983.9354,225.51,992.44" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="260.5" y="933.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="257" y="947.1074">[https]</text><!--MD5=[533382a5f923e61efa3518ce161a484e]
link addproduct to prdct--><path d="M187.4,992.25 C183.99,969.74 183.49,943.15 193,921 C197.87,909.66 205.03,899.11 213.23,889.55 " fill="none" id="addproduct-to-prdct" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="218.76,883.41,211.1753,887.3434,215.6318,891.3608,218.76,883.41" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="197.5" y="933.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="194" y="947.1074">[https]</text><!--MD5=[04c25eab65a132c541d311b0ede3d566]
link cclisting to addproduct--><path d="M306.74,687.3 C332.11,711.57 357.57,742.14 371,776 C389.85,823.51 391.33,844.11 371,891 C354.14,929.89 321.35,962.54 288.96,987.35 " fill="none" id="cclisting-to-addproduct" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="282.49,992.2,290.69,989.8,287.09,985,282.49,992.2" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="390.5" y="831.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="387" y="845.1074">[https]</text><!--MD5=[02347ec4c3ea76108a7c1bd2c6d43a66]
link addproduct to cclisting--><path d="M178.88,992.15 C158.6,938.6 133.69,850.4 153,776 C160.41,747.47 174.76,718.6 189.35,694.17 " fill="none" id="addproduct-to-cclisting" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="193.67,687.07,186.9475,692.3433,192.0725,695.4633,193.67,687.07" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="157.5" y="831.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="154" y="845.1074">[https]</text><!--MD5=[8ce2b75d5741cea85d15fb810f27c2da]
link addproduct to ccpages--><path d="M169.52,992.27 C162.23,978.96 155.1,964.26 150,950 C118.95,863.16 120.48,837.84 112,746 C101.71,634.53 100.62,602.16 133,495 C139.16,474.61 149.1,453.7 159.24,435.36 " fill="none" id="addproduct-to-ccpages" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="163.27,428.21,156.7289,433.7067,161.9561,436.6524,163.27,428.21" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="116.5" y="729.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="113" y="743.1074">[https]</text><!--MD5=[0a8a240694af84c8a74d43fd7336a0c2]
link shopping to purch--><path d="M500.1081,390.6806 C500.8052,391.6006 501.5441,392.5675 502.3237,393.5772 C508.5606,401.655 517.4,412.4788 528.2225,424.07 C549.8675,447.2525 579.445,473.505 612,487 C626.8,493.14 744.64,483.69 756,495 C757.2963,496.29 758.3947,499.2838 759.3244,503.5424 C759.7892,505.6718 760.2118,508.1174 760.5959,510.8243 " fill="none" id="shopping-to-purch" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="760.5959,510.8243,762.4423,502.4822,756.5018,503.3251,760.5959,510.8243" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="616.5" y="470.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="613" y="484.1074">[https]</text><!--MD5=[88f32f57b125e03aa98666c51c01b90b]
link cccart to shipping--><path d="M629.12,679.41 C624.52,691.44 620.37,704.48 618,717 C614.31,736.5 618.03,757.42 624.17,776.03 " fill="none" id="cccart-to-shipping" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="626.94,783.85,627.0971,775.3074,621.4413,777.3105,626.94,783.85" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="622.5" y="729.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="619" y="743.1074">[https]</text><!--MD5=[3aae2a23b5d9ed2c786456b0f0fb7a4a]
link shipping to payment--><path d="M629.12,883.41 C624.52,895.44 620.37,908.48 618,921 C614.01,942.04 618.18,964.72 624.79,984.65 " fill="none" id="shipping-to-payment" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="627.54,992.43,627.6971,983.8874,622.0413,985.8905,627.54,992.43" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="622.5" y="933.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="619" y="947.1074">[https]</text><!--MD5=[7239b567f319a3f058db5e9eaf9d233d]
link payment to storeorder--><path d="M652,1091.6 C652,1121.25 652,1159.27 652,1190.28 " fill="none" id="payment-to-storeorder" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="652,1198.42,655,1190.42,649,1190.42,652,1198.42" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="656.5" y="1151.6387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="653" y="1165.6074">[https]</text><!--MD5=[06398de93155def7d9b5c48edfce04ea]
link shopping to ccapi--><path d="M500.2039,380.3882 C500.8638,380.4673 501.5606,380.5522 502.2933,380.6432 C508.1552,381.3716 516.3164,382.4911 526.2422,384.1228 C546.0938,387.3862 573.0038,392.6987 602.695,401.03 C662.0775,417.6925 732.585,446.43 780,495 C853.66,570.46 884.95,692.92 897.73,767.74 " fill="none" id="shopping-to-ccapi" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="899.05,775.72,900.694,767.3357,894.7756,768.322,899.05,775.72" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="883.5" y="627.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="880" y="641.1074">[https]</text><!--MD5=[3a8b243c9230703972b1c14a2d5a7f2b]
link purch to ccapi--><path d="M772.1024,695.688 C772.5848,696.6453 773.0757,697.5998 773.5749,698.5516 C775.5716,702.3584 777.7007,706.12 779.9439,709.8313 C784.4303,717.2538 789.3731,724.475 794.6263,731.455 C805.1325,745.415 816.88,758.41 828.7,770.12 " fill="none" id="purch-to-ccapi" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="834.7,775.97,831.047,768.2463,826.8692,772.5527,834.7,775.97" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="807.5" y="729.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="804" y="743.1074">[https]</text><!--MD5=[764b3e8b38fb6ee3a280f20bc1531da7]
link ccapi to ccdb--><path d="M906,891.2 C906,916.09 906,945.54 906,971.71 " fill="none" id="ccapi-to-ccdb" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="906,979.72,909,971.72,903,971.72,906,979.72" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="930.5" y="933.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="82" x="907" y="947.1074">[PHP/Laravel]</text><!--MD5=[d9721e3f7dd3ac737855fff47a97bf4d]
link ccapi to extsys--><path d="M1015.63,884.05 C1053.21,901.41 1086.75,917.48 1090,921 C1112.05,944.91 1123.59,980.64 1129.44,1006.9 " fill="none" id="ccapi-to-extsys" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1131.14,1015.06,1132.4508,1006.6172,1126.5762,1007.837,1131.14,1015.06" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="1113" y="933.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="41" x="1110" y="947.1074">[REST]</text><!--MD5=[e0b93726e4a7781d30ee94562599ce92]
link extsys to ccapi--><path d="M1106.6,1015.39 C1073.92,985.92 1019.1,936.49 975.02,896.74 " fill="none" id="extsys-to-ccapi" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="968.88,891.2,972.8134,898.7847,976.8308,894.3282,968.88,891.2" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="1031" y="933.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="41" x="1028" y="947.1074">[REST]</text><!--MD5=[65143ce8d1b20756568cc33e7dc48979]
link purch to email--><path d="M772.2143,690.7404 C773.7446,691.9035 775.3979,692.9846 777.1661,693.989 C780.7025,695.9978 784.6987,697.6995 789.0911,699.1356 C797.876,702.0079 808.2457,703.8176 819.6914,704.8964 C842.5828,707.0541 869.7781,706.2881 897.2063,705.2513 C952.0625,703.1775 1007.85,700.02 1032,717 C1059.19,736.11 1074.02,771.96 1081.68,798.67 " fill="none" id="purch-to-email" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="1083.83,806.64,1084.642,798.1347,1078.8492,799.698,1083.83,806.64" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="1060.5" y="729.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="40" x="1058" y="743.1074">[smtp]</text><!--MD5=[51589dfa42d0ebc48f52ab73754532b8]
@startuml LAYOUT_TOP_DOWN Sample
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml
!define DEVICONS https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/devicons
!define FONTAWESOME https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/font-awesome-5
!include FONTAWESOME/users.puml


Person(user, "Customers")
System(email, "e-Mail")
System(extsys, "External Systems")


System_Boundary(c1, 'CloudCart Store Front') {
    System_Boundary(shopping, 'Shopping Process') {
        Container(ccpages, "Start/Landing page", "e-Commerce", "View purchaseable products")
        Container(cclisting, "View Products", "listings", "List of purchaseable products")
        Container(prdct, "View Product", "product", "View Product")
        Container(addproduct, "Add Product to Cart", "addproduct", "Add Product")
    }

    System_Boundary(purch, 'Checkout Process') {
        Container(cccart, "e-Cart", "ecart", "Virtual e-cart")
        Container(shipping, "Shipping Process", "shipping", "Shipping")
        Container(payment, "Payment Process", "payment", "Payment")
        Container(storeorder, "Place Order", "orders", "Complete Store Order")
    }

    Container(ccapi, "Rest API", "api", "Communicates with external systems")
    ContainerDb(ccdb, "Database", "MySQL", "Holds products, orders, invoice information, etc.")

}


Rel(user, shopping, "Uses", "https")
Rel(ccpages, cclisting, "Uses", "https")
Rel(cclisting, prdct, "Uses", "https")
Rel(prdct, addproduct, "Uses", "https")
Rel(addproduct, prdct, "Uses", "https")
Rel(cclisting, addproduct, "Uses", "https")
Rel(addproduct, cclisting, "Uses", "https")
Rel(addproduct, ccpages, "Uses", "https")

Rel(shopping, purch, "Uses", "https")
Rel(cccart, shipping, "Uses", "https")
Rel(shipping, payment, "Uses", "https")
Rel(payment, storeorder, "Uses", "https")

Rel(shopping, ccapi, "Uses", "https")
Rel(purch, ccapi, "Uses", "https")

Rel(ccapi, ccdb, "Uses", "PHP/Laravel")

Rel(ccapi, extsys, "Uses", "REST")
Rel(extsys, ccapi, "Uses", "REST")
Rel(purch, email, "Uses", "smtp")


@enduml

@startuml LAYOUT_TOP_DOWN Sample
















skinparam defaultTextAlignment center

skinparam wrapWidth 200
skinparam maxMessageSize 150

skinparam LegendBorderColor transparent
skinparam LegendBackgroundColor transparent
skinparam LegendFontColor #FFFFFF

skinparam shadowing<<legendArea>> false
skinparam rectangle<<legendArea>> {
    backgroundcolor #00000000
    bordercolor #00000000
}

skinparam rectangle {
    StereotypeFontSize 12
    shadowing false
}

skinparam database {
    StereotypeFontSize 12
    shadowing false
}

skinparam queue {
    StereotypeFontSize 12
    shadowing false
}

skinparam arrow {
    Color #666666
    FontColor #666666
    FontSize 12
}

skinparam actor {
    StereotypeFontSize 12
    shadowing false
    style awesome
}

skinparam person {
    StereotypeFontSize 12
    shadowing false
}

skinparam package {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    FontStyle plain
    BackgroundColor transparent
}

skinparam rectangle<<boundary>> {
    Shadowing false
    StereotypeFontSize 6
    StereotypeFontColor transparent
    FontColor #444444
    BorderColor #444444
    BackgroundColor transparent
    BorderStyle dashed
}






































































































skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}


skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}


skinparam rectangle<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam database<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam queue<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam actor<<system>> {
    StereotypeFontColor #1168BD
    FontColor #1168BD
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam person<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}


skinparam rectangle<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam database<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam queue<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam actor<<external_system>> {
    StereotypeFontColor #999999
    FontColor #999999
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam person<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}





sprite $person [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000025788300000000005886410000000000000
000000000007DFFFFFFD9643347BFFFFFFFB400000000000
0000000004EFFFFFFFFFFFFFFFFFFFFFFFFFFB1000000000
000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD200000000
00000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE10000000
0000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB0000000
000000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF5000000
000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000
000009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF200000
00000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF600000
00000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF800000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00000EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF700000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
0000008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD3000000
000000014555555555555555555555555555555300000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $person2 [48x48/16] {
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000002578888300000000005888864100000000000
0000000007DFFFFFFFFD9643347BFFFFFFFFFB4000000000
00000004EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB10000000
0000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD2000000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
00003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
0000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF50000
0003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD0000
0009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
0009FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF2000
0003FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFD0000
0000BFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF50000
00003FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFB00000
000006FFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFE100000
0000007FFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFD2000000
00000004EFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFB10000000
0000000007DF8FFFFFFFFFFFFFFFFFFFFFF8FB4000000000
000000000002578888888888888888888864100000000000
}


























skinparam rectangle<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam database<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam queue<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam actor<<container>> {
    StereotypeFontColor #438DD5
    FontColor #438DD5
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam person<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}


skinparam rectangle<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam database<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam queue<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam actor<<external_container>> {
    StereotypeFontColor #B3B3B3
    FontColor #B3B3B3
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam person<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
















sprite $users [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000024310000000000000000000000
000000000000000000018EFFFFC400000000000000000000
0000000000000000003EFFFFFFFF90000000000000000000
000000000000000002EFFFFFFFFFF9000000000000000000
00000000000000000CFFFFFFFFFFFF400000000000000000
0000006DFE8000004FFFFFFFFFFFFFC000004BFEA2000000
000009FFFFFC00009FFFFFFFFFFFFFF10005FFFFFE200000
00003FFFFFFF8000BFFFFFFFFFFFFFF3001FFFFFFFC00000
00009FFFFFFFD000CFFFFFFFFFFFFFF4005FFFFFFFF10000
0000AFFFFFFFE000AFFFFFFFFFFFFFF2006FFFFFFFF20000
00007FFFFFFFC0006FFFFFFFFFFFFFE0004FFFFFFFF00000
00001FFFFFFF50001FFFFFFFFFFFFF80000DFFFFFF900000
000004FFFFF8000007FFFFFFFFFFFE100002DFFFFB000000
000000179830000000AFFFFFFFFFF3000000069940000000
00000000000000000007FFFFFFFC20000000000000000000
0000012333310000000016ACB94000000000233332000000
0001BFFFFFFFD3000000000000000000008FFFFFFFE60000
000DFFFFFFFFE400000000000000000000AFFFFFFFFF6000
007FFFFFFFFD1006BEFE8310025BFFD92006FFFFFFFFF000
00CFFFFFFFE102DFFFFFFFFFFFFFFFFFF8007FFFFFFFF400
00DFFFFFFF302FFFFFFFFFFFFFFFFFFFFFA00BFFFFFFF500
00DFFFFFFB00EFFFFFFFFFFFFFFFFFFFFFF603FFFFFFF500
00CFFFFFF407FFFFFFFFFFFFFFFFFFFFFFFE00CFFFFFF400
006FFFFFF00DFFFFFFFFFFFFFFFFFFFFFFFF508FFFFFD000
00037777601FFFFFFFFFFFFFFFFFFFFFFFFF902777761000
00000000002FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000000FFFFFFFFFFFFFFFFFFFFFFFFF800000000000
000000000009FFFFFFFFFFFFFFFFFFFFFFFF200000000000
0000000000007DEEEEEEEEEEEEEEEEEEEEB2000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<FA5 USERS>> White


rectangle "<$person>\n==Customers" <<person>> as user 
rectangle "==e-Mail" <<system>> as email 
rectangle "==External Systems" <<system>> as extsys 


rectangle "==CloudCart Store Front\n<size:12>[System]</size>" <<boundary>> as c1  {
rectangle "==Shopping Process\n<size:12>[System]</size>" <<boundary>> as shopping  {
        rectangle "==Start/Landing page\n//<size:12>[e-Commerce]</size>//\n\n View purchaseable products" <<container>> as ccpages 
        rectangle "==View Products\n//<size:12>[listings]</size>//\n\n List of purchaseable products" <<container>> as cclisting 
        rectangle "==View Product\n//<size:12>[product]</size>//\n\n View Product" <<container>> as prdct 
        rectangle "==Add Product to Cart\n//<size:12>[addproduct]</size>//\n\n Add Product" <<container>> as addproduct 
    }

rectangle "==Checkout Process\n<size:12>[System]</size>" <<boundary>> as purch  {
        rectangle "==e-Cart\n//<size:12>[ecart]</size>//\n\n Virtual e-cart" <<container>> as cccart 
        rectangle "==Shipping Process\n//<size:12>[shipping]</size>//\n\n Shipping" <<container>> as shipping 
        rectangle "==Payment Process\n//<size:12>[payment]</size>//\n\n Payment" <<container>> as payment 
        rectangle "==Place Order\n//<size:12>[orders]</size>//\n\n Complete Store Order" <<container>> as storeorder 
    }

    rectangle "==Rest API\n//<size:12>[api]</size>//\n\n Communicates with external systems" <<container>> as ccapi 
    database "==Database\n//<size:12>[MySQL]</size>//\n\n Holds products, orders, invoice information, etc." <<container>> as ccdb 

}


user - ->> shopping : **Uses**\n//<size:12>[https]</size>//
ccpages - ->> cclisting : **Uses**\n//<size:12>[https]</size>//
cclisting - ->> prdct : **Uses**\n//<size:12>[https]</size>//
prdct - ->> addproduct : **Uses**\n//<size:12>[https]</size>//
addproduct - ->> prdct : **Uses**\n//<size:12>[https]</size>//
cclisting - ->> addproduct : **Uses**\n//<size:12>[https]</size>//
addproduct - ->> cclisting : **Uses**\n//<size:12>[https]</size>//
addproduct - ->> ccpages : **Uses**\n//<size:12>[https]</size>//

shopping - ->> purch : **Uses**\n//<size:12>[https]</size>//
cccart - ->> shipping : **Uses**\n//<size:12>[https]</size>//
shipping - ->> payment : **Uses**\n//<size:12>[https]</size>//
payment - ->> storeorder : **Uses**\n//<size:12>[https]</size>//

shopping - ->> ccapi : **Uses**\n//<size:12>[https]</size>//
purch - ->> ccapi : **Uses**\n//<size:12>[https]</size>//

ccapi - ->> ccdb : **Uses**\n//<size:12>[PHP/Laravel]</size>//

ccapi - ->> extsys : **Uses**\n//<size:12>[REST]</size>//
extsys - ->> ccapi : **Uses**\n//<size:12>[REST]</size>//
purch - ->> email : **Uses**\n//<size:12>[smtp]</size>//


@enduml

PlantUML version 1.2022.0(Tue Jan 11 18:16:42 EET 2022)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Default Encoding: UTF-8
Language: en
Country: US
--></g></svg>