<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentScriptType="application/ecmascript" contentStyleType="text/css" height="381px" preserveAspectRatio="none" style="width:920px;height:381px;background:#FFFFFF;" version="1.1" viewBox="0 0 920 381" width="920px" zoomAndPan="magnify"><defs/><g><!--MD5=[b18fed640a461eac9367d53cd6dcf632]
cluster shopconf--><rect fill="#FFFFFF" height="192" style="stroke:#444444;stroke-width:1.0;stroke-dasharray:7.0,7.0;" width="907" x="7" y="183"/><text fill="#444444" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="169" x="376" y="209.8516">Shop Configuration</text><text fill="#444444" font-family="sans-serif" font-size="12" lengthAdjust="spacing" textLength="55" x="433" y="224.7637">[System]</text><!--MD5=[3b69dcb9f3aff8449519c0fe49f19b82]
entity shopdesign--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="167" x="22.5" y="252"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="69" y="273.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="130" x="41" y="290.8203">Choose design</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="81" x="65.5" y="305.7324">[shopdesign]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="108" y="321.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="143" x="36.5" y="337.8545">Switch shop designs</text><!--MD5=[3d9d6a8271e159437df880fa0553ffc5]
entity menunavigation--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="203" x="224.5" y="252"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="289" y="273.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="183" x="234.5" y="290.8203">Menu and navigation</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="110" x="271" y="305.7324">[menunavigation]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="328" y="321.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="129" x="263.5" y="337.8545">Manage navigation</text><!--MD5=[75d3509458a766ce4229d205fbcef17b]
entity widgets--><rect fill="#438DD5" height="115.4531" style="stroke:#3C7FC0;stroke-width:1.5;" width="182" x="463" y="244"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="517" y="265.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="73" x="517.5" y="282.8203">Widgets</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="58" x="525" y="297.7324">[widgets]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="556" y="313.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="154" x="477" y="329.8545">Manage and configure</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="54" x="527" y="346.1514">widgets</text><!--MD5=[4c5dbad53ab8fcb5e9e028c9c0aab354]
entity edittemplates--><rect fill="#438DD5" height="99.1563" style="stroke:#3C7FC0;stroke-width:1.5;" width="217" x="680.5" y="252"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="74" x="752" y="273.1387">«container»</text><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="116" x="731" y="290.8203">UI Templates</text><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="96" x="741" y="305.7324">[edittemplates]</text><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="0" x="791" y="321.5576"/><text fill="#FFFFFF" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="193" x="694.5" y="337.8545">Customize current template</text><!--MD5=[8fc3522a43f8c7199df5e09e5bb0188e]
entity user--><rect fill="#08427B" height="100.5938" style="stroke:#073B6F;stroke-width:1.5;" width="177" x="315.5" y="7"/><text fill="#FFFFFF" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="57" x="375.5" y="28.1387">«person»</text><image height="48" width="48" x="380" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=" y="30.9688"/><text fill="#FFFFFF" font-family="sans-serif" font-size="16" font-weight="bold" lengthAdjust="spacing" textLength="157" x="325.5" y="93.8203">Murchant / Admin</text><!--MD5=[f08d742ad98440d578ebafae7d6ac512]
link user to shopdesign--><path d="M315.13,102.13 C279.57,121.79 239.44,146.91 207,175 C183.42,195.41 161.22,221.86 143.67,245.2 " fill="none" id="user-to-shopdesign" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="138.85,251.7,146.0319,247.0717,141.2177,243.4906,138.85,251.7" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="256.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="253" y="164.1074">[https]</text><!--MD5=[c769c869cbe983cb3ab04ed74d804659]
link user to menunavigation--><path d="M388.03,108.06 C375.3,147.55 357.47,202.86 344.19,244.06 " fill="none" id="user-to-menunavigation" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="341.68,251.84,346.9919,245.1479,341.2818,243.3053,341.68,251.84" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="380.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="377" y="164.1074">[https]</text><!--MD5=[ff274bb031135b66df5faa437a77a3a4]
link user to widgets--><path d="M434.72,108.06 C457.81,145.31 489.63,196.64 514.59,236.92 " fill="none" id="user-to-widgets" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="518.85,243.8,517.1857,235.4197,512.0857,238.5804,518.85,243.8" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="474.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="471" y="164.1074">[https]</text><!--MD5=[a22d05a05d7ad711aeaacda1d474e278]
link user to edittemplates--><path d="M492.7,89.2 C544.75,109.4 610.41,138.79 663,175 C691.95,194.94 720.07,221.96 742.38,245.83 " fill="none" id="user-to-edittemplates" style="stroke:#666666;stroke-width:1.0;"/><polygon fill="#666666" points="747.92,251.82,744.6918,243.9093,740.2862,247.9826,747.92,251.82" style="stroke:#666666;stroke-width:1.0;"/><text fill="#666666" font-family="sans-serif" font-size="12" font-weight="bold" lengthAdjust="spacing" textLength="35" x="654.5" y="150.1387">Uses</text><text fill="#666666" font-family="sans-serif" font-size="12" font-style="italic" lengthAdjust="spacing" textLength="42" x="651" y="164.1074">[https]</text><!--MD5=[fca1a9757689bec8a1a43f9f5f25387a]
@startuml LAYOUT_TOP_DOWN Sample
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml
!define DEVICONS https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/devicons
!define FONTAWESOME https://raw.githubusercontent.com/tupadr3/plantuml-icon-font-sprites/master/font-awesome-5
!include FONTAWESOME/users.puml


Person(user, "Murchant / Admin")


System_Boundary(shopconf, 'Shop Configuration') {

    Container(shopdesign, "Choose design", "shopdesign", "Switch shop designs")
    Container(menunavigation, "Menu and navigation", "menunavigation", "Manage navigation")
    Container(widgets, "Widgets", "widgets", "Manage and configure widgets")
    Container(edittemplates, "UI Templates", "edittemplates", "Customize current template")

}


Rel(user, shopdesign, "Uses", "https")
Rel(user, menunavigation, "Uses", "https")
Rel(user, widgets, "Uses", "https")
Rel(user, edittemplates, "Uses", "https")


@enduml

@startuml LAYOUT_TOP_DOWN Sample
















skinparam defaultTextAlignment center

skinparam wrapWidth 200
skinparam maxMessageSize 150

skinparam LegendBorderColor transparent
skinparam LegendBackgroundColor transparent
skinparam LegendFontColor #FFFFFF

skinparam shadowing<<legendArea>> false
skinparam rectangle<<legendArea>> {
    backgroundcolor #00000000
    bordercolor #00000000
}

skinparam rectangle {
    StereotypeFontSize 12
    shadowing false
}

skinparam database {
    StereotypeFontSize 12
    shadowing false
}

skinparam queue {
    StereotypeFontSize 12
    shadowing false
}

skinparam arrow {
    Color #666666
    FontColor #666666
    FontSize 12
}

skinparam actor {
    StereotypeFontSize 12
    shadowing false
    style awesome
}

skinparam person {
    StereotypeFontSize 12
    shadowing false
}

skinparam package {
    StereotypeFontSize 6
    StereotypeFontColor transparent
    FontStyle plain
    BackgroundColor transparent
}

skinparam rectangle<<boundary>> {
    Shadowing false
    StereotypeFontSize 6
    StereotypeFontColor transparent
    FontColor #444444
    BorderColor #444444
    BackgroundColor transparent
    BorderStyle dashed
}






































































































skinparam rectangle<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam database<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam queue<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam actor<<person>> {
    StereotypeFontColor #08427B
    FontColor #08427B
    BackgroundColor #08427B
    BorderColor #073B6F
}
skinparam person<<person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #08427B
    BorderColor #073B6F
}


skinparam rectangle<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam database<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam queue<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam actor<<external_person>> {
    StereotypeFontColor #686868
    FontColor #686868
    BackgroundColor #686868
    BorderColor #8A8A8A
}
skinparam person<<external_person>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #686868
    BorderColor #8A8A8A
}


skinparam rectangle<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam database<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam queue<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam actor<<system>> {
    StereotypeFontColor #1168BD
    FontColor #1168BD
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}
skinparam person<<system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #1168BD
    BorderColor #3C7FC0
}


skinparam rectangle<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam database<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam queue<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam actor<<external_system>> {
    StereotypeFontColor #999999
    FontColor #999999
    BackgroundColor #999999
    BorderColor #8A8A8A
}
skinparam person<<external_system>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #999999
    BorderColor #8A8A8A
}





sprite $person [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000025788300000000005886410000000000000
000000000007DFFFFFFD9643347BFFFFFFFB400000000000
0000000004EFFFFFFFFFFFFFFFFFFFFFFFFFFB1000000000
000000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFD200000000
00000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE10000000
0000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB0000000
000000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF5000000
000003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD000000
000009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF200000
00000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF600000
00000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF800000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
00001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA00000
00000EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF700000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
0000008FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD3000000
000000014555555555555555555555555555555300000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}

sprite $person2 [48x48/16] {
0000000000000000000049BCCA7200000000000000000000
0000000000000000006EFFFFFFFFB3000000000000000000
00000000000000001CFFFFFFFFFFFF700000000000000000
0000000000000001EFFFFFFFFFFFFFF80000000000000000
000000000000000CFFFFFFFFFFFFFFFF6000000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
00000000000001FFFFFFFFFFFFFFFFFFF900000000000000
00000000000006FFFFFFFFFFFFFFFFFFFF00000000000000
0000000000000BFFFFFFFFFFFFFFFFFFFF40000000000000
0000000000000EFFFFFFFFFFFFFFFFFFFF70000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000FFFFFFFFFFFFFFFFFFFFF80000000000000
0000000000000DFFFFFFFFFFFFFFFFFFFF60000000000000
0000000000000AFFFFFFFFFFFFFFFFFFFF40000000000000
00000000000006FFFFFFFFFFFFFFFFFFFE00000000000000
00000000000000EFFFFFFFFFFFFFFFFFF800000000000000
000000000000007FFFFFFFFFFFFFFFFFF100000000000000
000000000000000BFFFFFFFFFFFFFFFF5000000000000000
0000000000000001DFFFFFFFFFFFFFF70000000000000000
00000000000000000BFFFFFFFFFFFF500000000000000000
0000000000000000005DFFFFFFFFA1000000000000000000
0000000000000000000037ABB96100000000000000000000
000000000002578888300000000005888864100000000000
0000000007DFFFFFFFFD9643347BFFFFFFFFFB4000000000
00000004EFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB10000000
0000007FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD2000000
000006FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE100000
00003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB00000
0000BFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF50000
0003FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFD0000
0009FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB000
001FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA000
000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF8000
000DFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF6000
0009FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFF2000
0003FFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFFD0000
0000BFFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFF50000
00003FFFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFFB00000
000006FFFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFFE100000
0000007FFFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFFD2000000
00000004EFFF8FFFFFFFFFFFFFFFFFFFFFF8FFFB10000000
0000000007DF8FFFFFFFFFFFFFFFFFFFFFF8FB4000000000
000000000002578888888888888888888864100000000000
}


























skinparam rectangle<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam database<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam queue<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam actor<<container>> {
    StereotypeFontColor #438DD5
    FontColor #438DD5
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}
skinparam person<<container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #438DD5
    BorderColor #3C7FC0
}


skinparam rectangle<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam database<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam queue<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam actor<<external_container>> {
    StereotypeFontColor #B3B3B3
    FontColor #B3B3B3
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
skinparam person<<external_container>> {
    StereotypeFontColor #FFFFFF
    FontColor #FFFFFF
    BackgroundColor #B3B3B3
    BorderColor #A6A6A6
}
















sprite $users [48x48/16] {
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000024310000000000000000000000
000000000000000000018EFFFFC400000000000000000000
0000000000000000003EFFFFFFFF90000000000000000000
000000000000000002EFFFFFFFFFF9000000000000000000
00000000000000000CFFFFFFFFFFFF400000000000000000
0000006DFE8000004FFFFFFFFFFFFFC000004BFEA2000000
000009FFFFFC00009FFFFFFFFFFFFFF10005FFFFFE200000
00003FFFFFFF8000BFFFFFFFFFFFFFF3001FFFFFFFC00000
00009FFFFFFFD000CFFFFFFFFFFFFFF4005FFFFFFFF10000
0000AFFFFFFFE000AFFFFFFFFFFFFFF2006FFFFFFFF20000
00007FFFFFFFC0006FFFFFFFFFFFFFE0004FFFFFFFF00000
00001FFFFFFF50001FFFFFFFFFFFFF80000DFFFFFF900000
000004FFFFF8000007FFFFFFFFFFFE100002DFFFFB000000
000000179830000000AFFFFFFFFFF3000000069940000000
00000000000000000007FFFFFFFC20000000000000000000
0000012333310000000016ACB94000000000233332000000
0001BFFFFFFFD3000000000000000000008FFFFFFFE60000
000DFFFFFFFFE400000000000000000000AFFFFFFFFF6000
007FFFFFFFFD1006BEFE8310025BFFD92006FFFFFFFFF000
00CFFFFFFFE102DFFFFFFFFFFFFFFFFFF8007FFFFFFFF400
00DFFFFFFF302FFFFFFFFFFFFFFFFFFFFFA00BFFFFFFF500
00DFFFFFFB00EFFFFFFFFFFFFFFFFFFFFFF603FFFFFFF500
00CFFFFFF407FFFFFFFFFFFFFFFFFFFFFFFE00CFFFFFF400
006FFFFFF00DFFFFFFFFFFFFFFFFFFFFFFFF508FFFFFD000
00037777601FFFFFFFFFFFFFFFFFFFFFFFFF902777761000
00000000002FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000003FFFFFFFFFFFFFFFFFFFFFFFFFB00000000000
00000000000FFFFFFFFFFFFFFFFFFFFFFFFF800000000000
000000000009FFFFFFFFFFFFFFFFFFFFFFFF200000000000
0000000000007DEEEEEEEEEEEEEEEEEEEEB2000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
000000000000000000000000000000000000000000000000
}


skinparam folderBackgroundColor<<FA5 USERS>> White


rectangle "<$person>\n==Murchant / Admin" <<person>> as user 


rectangle "==Shop Configuration\n<size:12>[System]</size>" <<boundary>> as shopconf  {

    rectangle "==Choose design\n//<size:12>[shopdesign]</size>//\n\n Switch shop designs" <<container>> as shopdesign 
    rectangle "==Menu and navigation\n//<size:12>[menunavigation]</size>//\n\n Manage navigation" <<container>> as menunavigation 
    rectangle "==Widgets\n//<size:12>[widgets]</size>//\n\n Manage and configure widgets" <<container>> as widgets 
    rectangle "==UI Templates\n//<size:12>[edittemplates]</size>//\n\n Customize current template" <<container>> as edittemplates 

}


user - ->> shopdesign : **Uses**\n//<size:12>[https]</size>//
user - ->> menunavigation : **Uses**\n//<size:12>[https]</size>//
user - ->> widgets : **Uses**\n//<size:12>[https]</size>//
user - ->> edittemplates : **Uses**\n//<size:12>[https]</size>//


@enduml

PlantUML version 1.2022.0(Tue Jan 11 18:16:42 EET 2022)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Default Encoding: UTF-8
Language: en
Country: US
--></g></svg>