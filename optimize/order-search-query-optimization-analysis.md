# Анализ на производителността на заявката за търсене на поръчки

## Заявка за анализ

```sql
select `orders`.*, 
       (select count(*) from `orders` as `laravel_reserved_0` 
        where `orders`.`customer_id` = `laravel_reserved_0`.`customer_id`) as `customer_orders_count` 
from `orders` 
where (
    `orders`.`id` = '899126741' 
    or `orders`.`invoice_number` = '899126741' 
    or `orders`.`receipt_number` = '899126741' 
    or exists (
        select * from `order_shipping_addresses` 
        where `orders`.`id` = `order_shipping_addresses`.`order_id` 
        and `phone` like '%899126741'
    ) 
    or exists (
        select * from `order_billing_addresses` 
        where `orders`.`id` = `order_billing_addresses`.`order_id` 
        and `phone` like '%899126741'
    ) 
    or exists (
        select * from `orders_products` 
        where `orders`.`id` = `orders_products`.`order_id` 
        and (`sku` = '899126741' or `barcode` = '899126741')
    ) 
    or exists (
        select * from `orders_meta` 
        where `orders`.`id` = `orders_meta`.`order_id` 
        and `parameter` = 'bol_id' 
        and `value` = '899126741'
    ) 
    or exists (
        select * from `orders_payments` 
        where `orders`.`id` = `orders_payments`.`order_id` 
        and (`hash` = '899126741' or `provider_reference_id` = '899126741')
    ) 
    or `orders`.`usn` = '899126741'
) 
order by `orders`.`id` desc 
limit 25 offset 0
```

## Анализ на индексите по таблици

### ✅ Таблица `orders` - Всички полета имат индекси
- `id` - PRIMARY KEY ✅
- `invoice_number` - UNIQUE INDEX ✅
- `receipt_number` - UNIQUE INDEX ✅
- `usn` - UNIQUE INDEX ✅
- `customer_id` - INDEX ✅ (за subquery)

### ✅ Таблица `orders_products` - Всички полета имат индекси
- `order_id` - INDEX ✅
- `sku` - INDEX ✅
- `barcode` - INDEX ✅

### ✅ Таблица `orders_payments` - Всички полета имат индекси
- `order_id` - INDEX ✅
- `hash` - INDEX ✅
- `provider_reference_id` - INDEX ✅

### ⚠️ Таблица `order_shipping_addresses` - Липсва индекс
- `order_id` - INDEX ✅
- `phone` - **ЛИПСВА ИНДЕКС** ❌

### ⚠️ Таблица `order_billing_addresses` - Липсва индекс
- `order_id` - INDEX ✅
- `phone` - **ЛИПСВА ИНДЕКС** ❌

### ⚠️ Таблица `orders_meta` - Липсват индекси
- `order_id` - FOREIGN KEY ✅
- `parameter` - INDEX ✅
- `value` - **ЛИПСВА ИНДЕКС** ❌
- Композитен индекс `(parameter, value)` - **ЛИПСВА** ❌

## Проблеми с производителността

### 1. LIKE заявки без индекси
```sql
`phone` like '%899126741'
```
- Полетата `phone` в `order_shipping_addresses` и `order_billing_addresses` нямат индекси
- LIKE заявки с wildcard в началото (`%term`) не могат да използват индекси ефективно
- Въпреки това, индексът ще помогне за точни търсения и LIKE заявки без wildcard в началото

### 2. Търсене в LONGTEXT поле без индекс
```sql
`value` = '899126741'
```
- Полето `value` в `orders_meta` е LONGTEXT и няма индекс
- Точните търсения ще бъдат бавни без индекс

### 3. Липса на композитен индекс
```sql
`parameter` = 'bol_id' and `value` = '899126741'
```
- Няма композитен индекс за `(parameter, value)` в `orders_meta`
- MySQL може да използва само един индекс на заявка, така че ще използва само индекса за `parameter`

## Решения

### Миграция: `2025_01_27_000000_add_missing_indexes_for_order_search_optimization.php`

1. **Добавяне на индекс за `phone` полета:**
   ```sql
   ALTER TABLE order_shipping_addresses ADD INDEX idx_phone (phone);
   ALTER TABLE order_billing_addresses ADD INDEX idx_phone (phone);
   ```

2. **Добавяне на индекс за `value` поле:**
   ```sql
   ALTER TABLE orders_meta ADD INDEX idx_value (value(255));
   ```
   - Използваме първите 255 символа заради LONGTEXT ограничението

3. **Добавяне на композитен индекс:**
   ```sql
   ALTER TABLE orders_meta ADD INDEX idx_parameter_value (parameter, value(255));
   ```

## Очаквани подобрения

### Преди оптимизацията:
- Пълно сканиране на таблиците за `phone` полетата
- Пълно сканиране на `orders_meta.value`
- Неефективно използване на индексите в `orders_meta`

### След оптимизацията:
- Бързо търсене в `phone` полетата (особено за точни търсения)
- Бързо търсене в `orders_meta.value`
- Оптимално използване на композитния индекс за `orders_meta`
- Значително намаляване на времето за изпълнение на заявката

## Препоръки за допълнителна оптимизация

1. **Full-text search индекси** за `phone` полетата ако се правят чести LIKE търсения
2. **Кеширане** на резултатите за често търсени термини
3. **Elasticsearch** за сложни търсения в множество полета
4. **Партициониране** на големи таблици по дата

## Стъпки за внедряване

1. Изпълнете миграцията:
   ```bash
   php artisan migrate
   ```

2. Анализирайте производителността преди и след:
   ```sql
   EXPLAIN SELECT ...
   ```

3. Мониторирайте заявките за подобрения в производителността
