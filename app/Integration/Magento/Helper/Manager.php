<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 7.12.2016 г.
 * Time: 17:12 ч.
 */

namespace App\Integration\Magento\Helper;

use App\Integration\Magento\Helper\Manager\All;
use App\Integration\Magento\Helper\Manager\Customers;
use App\Integration\Magento\Helper\Manager\Products;
use App\Integration\Magento\Magento;
use Carbon\Carbon;
use App\Exceptions\Error;
use App\Integration\Magento\Soap\Client;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use App\Helper\YesNo;

class Manager
{
    public const APP_KEY = Magento::APP_KEY;

    /**
     * @var array
     */
    protected static $step_columns = [
        'get_variants',
        'get_info',
        'get_images',
        'get_tags',
        'get_stock',
        'get_options',
        'get_files',
        'get_addresses',
    ];

    protected $first_step;

    /**
     * @var Client
     */
    protected $client;

    /**
     * @return mixed|null
     * @throws Error
     * @throws \Throwable
     */
    public function execute()
    {
        if ($this->getSetting('finish') || !$this->getSetting('magento_import')) {
            return;
        }

        if (!$this->getSetting('start_task')) {
            return;
        }

        /** @var Customers|Products|All $object */
        $object = app('\\App\\Integration\\Magento\\Helper\\Manager\\' . ucfirst(strtolower($this->getSetting('magento_import'))));
        return $object->execute();
    }

    /**
     * @return mixed
     * @throws Error
     * @throws \Throwable
     */
    protected function _execute()
    {
        $step = $this->getSetting('step') ?: $this->first_step;

        if (!method_exists($this, $step)) {
            throw new Error(sprintf('Method "%s" is not implement in "%s"', $step, static::class));
        }

        \Illuminate\Support\Facades\DB::transaction(function () use ($step): void {
            $this->updateSettings([
                'working' => YesNo::Yes,
                'step' => $this->$step(),
                'last_execute' => Carbon::now('UTC')->format('Y-m-d H:i:s'),
            ]);
        }, 5);

        $this->client = null;

        return $this->getSetting('step');
    }

    /**
     * @return Client
     * @throws Error
     * @throws \SoapFault
     */
    public function getClient()
    {
        if (is_null($this->client)) {
            $this->client = new Client(
                $this->getSetting('base_url'),
                $this->getSetting('api_user'),
                $this->getSetting('api_key')
            );
        }

        return $this->client;
    }

    /**
     * @return null
     * @throws Error
     * @throws \Throwable
     */
    protected function finish(): null
    {
        $this->updateSettings([
            'finish' => 1,
            'working' => null
        ]);
        (new Magento())->reset();
        return null;
    }

    /**
     * @param Model $model
     * @param $data
     * @return array
     */
    protected function _fillDefaults(Model $model, $data)
    {
        $arr = [];
        $fillableColumns = $model->getFillable();
        foreach ($fillableColumns as $column) {
            if (!in_array($column, static::$step_columns)) {
                $arr[$column] = null;
            }
        }

        foreach ($data as $k => $v) {
            $arr[$k] = $v;
        }

        return Arr::only($arr, $model->getFillable());
    }
}
