<?php

declare(strict_types=1);

namespace App\Integration\Magento\Helper;

use App\Exceptions\Error;
use App\Helper\ArrayCache;
use App\Integration\Magento\Models\Category;
use App\Integration\Magento\Models\Products;
use App\Integration\Magento\Models\ProductsAttribute;
use App\Integration\Magento\Models\ProductsImages;
use App\Integration\Magento\Models\ProductsStock;
use App\Integration\Magento\Models\ProductsTags;
use App\Models\Category\Property;
use App\Models\Category\PropertyOption;
use App\Models\Product\Parameter;
use App\Models\Product\ParameterOption;
use App\Models\Product\Product;
use App\Models\Product\Vendor;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Exception;
use App\Models\Product\Category as BaseCategory;
use Illuminate\Support\Str;
use App\Helper\YesNo;

class ConvertProduct
{
    /**
     * @param \App\Integration\Magento\Models\Products $product
     * @param \App\Integration\Magento\Helper\Manager $manager
     * @return mixed
     */
    public function __construct(protected \App\Integration\Magento\Models\Products $product, Manager $manager)
    {
        $this->manager = $manager;
    }

    /**
     * Get product vendor
     * @return null|string
     */
    public function getVendor()
    {
        $vendor = $this->_getProductAttributes()
            ->where('code', '!=', 'country_of_manufacture')
            ->where('code', 'like', '%' . 'manufacturer' . '%')
            ->first();
        return $vendor ? $vendor['value'] : null;
    }

    /**
     * Return category properties
     * @return Collection
     */
    public function getCategoryProperties()
    {
        return $this->_getProductAttributes()->filter(fn ($attribute): bool => $attribute['code'] != 'manufacturer');
    }

    /**
     * @return Collection
     */
    public function getCategoryPath()
    {
        //@todo add system category
        $emptyCategory = (fn () => Collection::make([(object)[
            'self_id' => null,
            'name' => 'System Category',
            'description' => null,
            'meta_title' => null,
            'meta_description' => null,
            'url_key' => 'system-category'
        ]]));

        if (empty($this->product['category_ids'])) {
            return $emptyCategory();
        }

        return $this->cache('category_' . md5(json_encode($this->product['category_ids'])), function () use ($emptyCategory) {
            $category = Category::with('ppath')->whereIn('category_id', $this->product['category_ids'])
                ->orderBy('level', 'desc')->first();

            $arr = [];
            if ($category) {
                array_unshift($arr, $category);
                $path = $category->ppath;
                while ($path) {
                    array_unshift($arr, $path);
                    $path = $path->ppath;
                }
            }

            if (!$arr) {
                return $emptyCategory();
            }

            return Collection::make($arr);
        });
    }

    /**
     * @return Collection
     */
    public function getImages()
    {
        return $this->cache('images_' . $this->product->product_id, fn () => ProductsImages::where('product_id', $this->product->product_id)
            ->orderBy('default', 'desc')->get());
    }

    /**
     * @return Collection
     */
    public function getTags()
    {
        return $this->cache('tags_' . $this->product->product_id, fn () => ProductsTags::where('product_id', $this->product->product_id)
            ->get()->unique('tag_id'));
    }

    /**
     * @return Collection
     */
    public function getStock()
    {
        return $this->cache('stock_' . $this->product->product_id, fn () => ProductsStock::where('product_id', $this->product->product_id)->first());
    }

    /**
     * @return void
     * @throws Error
     * @throws \Throwable
     */
    public function execute(): void
    {
        try {
            \Illuminate\Support\Facades\DB::transaction(function (): void {
                $category_id = $this->getCategoryId();
                $category_properties = $this->getCategoryPropertiesIds($category_id);
                $vendor_id = $this->getVendorId();
                $stock = $this->getStock();
                $images = $this->getImages();

                $_POST['seo_title'] = Str::substr($this->product->meta_title ?: $this->product->name, 0, 191);
                // $_POST['seo_description'] = Str::substr($this->product->meta_description ? : $this->product->short_description ? : $this->product->description, 0, 2000);
                if ($this->product->meta_description) {
                    $_POST['seo_description'] = Str::substr($this->product->meta_description, 0, 2000);
                } elseif ($this->product->short_description) {
                    $_POST['seo_description'] = Str::substr($this->product->short_description, 0, 2000);
                } elseif ($this->product->description) {
                    $_POST['seo_description'] = Str::substr($this->product->description, 0, 2000);
                }

                $_POST['seo_description'] = Str::substr(
                    ($this->product->meta_description ?: $this->product->short_description) ?: $this->product->description,
                    0,
                    2000
                );

                $data = [
                    'name' => trim((string) $this->product->name),
                    'url_handle' => $this->product->url_key ?: Str::substr(trim((string) $this->product->name), 0, 191),
                    'old_url_handle' => $this->product->url_key ?: Str::substr(trim((string) $this->product->name), 0, 191),
                    'description' => $this->product->description,
                    'category_id' => $category_id,
                    'vendor_id' => $vendor_id,
                    'type' => Product::TYPE_SIMPLE,
                    'imported' => YesNo::True,
                    'app_import' => 'magento-' . $this->product->product_id,
                    'tags' => $this->getTags()->pluck('name')->all(),
                    'short_description' => $this->product->short_description,
                    'variant' => $this->getProductVariant(),
                    'variants' => $this->getProductVariants(),
                ];

                if ($this->product->weight > 0) {
                    $data['require_shipping_address'] = YesNo::True;
                }

                $draft = false;
                if (empty($data['variants']) && empty($data['variant'])) {
                    $draft = true;
                    $data['variant'] = [
                        'price' => null,
                        'sku' => null,
                        'weight' => $this->product->weight,
                        'quantity' => null,
                    ];
                }

                $data['track_inventory'] = YesNo::True;

                //                $data['track_inventory'] = null;
                //                $data['continue_selling'] = true;
                //                if($stock) {
                //                    if((int)$stock->qty > 0) {
                //                        $data['track_inventory'] = true;
                //                        $data['continue_selling'] = null;
                //                    }
                //                }

                //                if($stock && (int)$stock->qty > 0) {
                //                    $data['track_inventory'] = YesNo::True;
                //                    $data['continue_selling'] = YesNo::False;
                //                }
                //                if($stock && $stock->is_in_stock) {
                //                    $data['track_inventory'] = (int)$stock->qty > 0 ? YesNo::True : YesNo::False;
                //                    $data['continue_selling'] = YesNo::True;
                //                }

                if ($this->product->status == 1) {
                    $data['extra_fill']['active'] = YesNo::True;
                } elseif ($this->product->status == 2) {
                    $data['extra_fill']['active'] = YesNo::False;
                }

                $new = false;

                $product = Product::whereAppImport('magento-' . $this->product->product_id)
                    ->lockForUpdate()->first();

                if (!$product) {
                    $new = true;
                    $product = Product::add($data, $draft);
                } else {
                    $product = $product->editModel($data, $draft);
                }

                if ($images && $new) {

                    foreach ($images as $image) {
                        /**
                         * @todo remove this
                         */
                        //$image['url'] = str_replace('://magento.io/', '://www.6plus.bg/', $image['url']);
                        $product->uploadImageFromUrl($image['url'], 'file', ['name' => $image['label']]);
                    }
                }

                if ($category_properties && $new) {
                    foreach ($category_properties as $data) {
                        $data['option']->products()->attach([$product->id => ['property_id' => $data['property']->id]]);
                    }
                }

                $this->product->delete();

            }, 5);
        } catch (Exception $exception) {
            $this->product->delete();
            throw new Error($exception->getMessage());
        }
    }

    /**
     * @return float|int
     */
    protected function getProductPrice(): float|int
    {
        return isset($this->product->product['price']) ? round($this->product->product['price'], 2) : 0;
    }

    /**
     * @return array|null
     */
    private function getProductVariant(): ?array
    {
        if (!$this->getProductVariants()) {
            return [
                'price' => $this->getProductPrice(),
                'sku' => $this->product->sku,
                'weight' => round($this->_weightFormat($this->product->weight), 3),
                'quantity' => (int)($this->getStock()->qty ?? 0)
            ];
        }

        return null;
    }

    /**
     * @return array|null
     */
    private function getProductVariants(): array
    {
        $result = [];
        if ($this->product->type == 'configurable' && $this->product->configurable_data) {
            $simpleProducts = json_decode((string) $this->product->configurable_data);

            $variants = [];
            foreach ($simpleProducts as $simpleProduct) {
                $i = 1;
                foreach ($simpleProduct->configurable_attributes as $attribute) {
                    $attribute->label = trim((string) $attribute->label);
                    $attribute->value = trim((string) $attribute->value);

                    $parameter = Parameter::where('name', 'like', $attribute->label)->first();
                    if (!$parameter) {
                        $parameter = Parameter::create(['name' => $attribute->label]);
                    }

                    $parameterOption = ParameterOption::where('parameter_id', $parameter->id)
                        ->where('name', 'like', $attribute->value)->first();
                    if (!$parameterOption) {
                        $parameterOption = ParameterOption::create([
                            'name' => $attribute->value,
                            'parameter_id' => $parameter->id,
                        ]);
                    }

                    $variants[$attribute->label][$attribute->value] = $parameterOption;

                    $i++;
                    if ($i == 3) {
                        break;
                    }
                }

                $stock = $this->getStock();
                if (count($variants) == 3) {
                    $v1 = array_shift($variants);
                    $v2 = array_shift($variants);
                    $v3 = array_shift($variants);
                    foreach ($v1 as $values1) {
                        foreach ($v2 as $values2) {
                            foreach ($v3 as $values3) {
                                $key = implode('_', [
                                    $values1->name,
                                    $values2->name,
                                    $values3->name,
                                ]);
                                $result[$key] = [
                                    'v1' => $values1->name,
                                    'v1_id' => $values1->id,
                                    'v2' => $values2->name,
                                    'v2_id' => $values2->id,
                                    'v3' => $values3->name,
                                    'v3_id' => $values3->id,
                                    'price' => number_format(
                                        (float)$simpleProduct->price,
                                        2,
                                        '.',
                                        ''
                                    ),
                                    'sku' => $simpleProduct->sku,
                                    'barcode' => $simpleProduct->sku,
                                    'quantity' => $stock ? (int)$stock->qty : 0,
                                    'weight' => round($this->_weightFormat($this->product->weight), 3),
                                    'image_ids' => null,
                                ];
                            }
                        }
                    }
                } elseif (count($variants) == 2) {
                    $v1 = array_shift($variants);
                    $v2 = array_shift($variants);
                    foreach ($v1 as $values1) {
                        foreach ($v2 as $values2) {
                            $key = implode('_', [
                                $values1->name,
                                $values2->name,
                            ]);
                            $result[$key] = [
                                'v1' => $values1->name,
                                'v1_id' => $values1->id,
                                'v2' => $values2->name,
                                'v2_id' => $values2->id,
                                'price' => number_format(
                                    (float)$simpleProduct->price,
                                    2,
                                    '.',
                                    ''
                                ),
                                'sku' => $simpleProduct->sku,
                                'barcode' => $simpleProduct->sku,
                                'quantity' => $stock ? $stock->qty : 0,
                                'weight' => round($this->_weightFormat($this->product->weight), 3),
                                'image_ids' => null,
                            ];
                        }
                    }
                } elseif (count($variants) == 1) {
                    $v1 = array_shift($variants);
                    foreach ($v1 as $values1) {
                        $result[$values1->name] = [
                            'v1' => $values1->name,
                            'v1_id' => $values1->id,
                            'price' => number_format(
                                (float)$simpleProduct->price,
                                2,
                                '.',
                                ''
                            ),
                            'sku' => $simpleProduct->sku,
                            'barcode' => $simpleProduct->sku,
                            'quantity' => $stock ? $stock->qty : 0,
                            'weight' => round($this->_weightFormat($this->product->weight), 3),
                            'image_ids' => null,
                        ];
                    }
                }

                Products::where('product_id', $simpleProduct->simple_product_id)->delete();
            }
        }

        return $result;
    }

    private function getVendorId()
    {
        $name = $this->getVendor();
        if (!$name) {
            return;
        }

        $vendor = Vendor::where('name', 'like', trim($name))->first();
        if (!$vendor) {
            $_POST['seo_title'] = trim($name);
            $_POST['seo_description'] = trim($name);
            $vendor = Vendor::add([
                'name' => trim($name),
                'url_handle' => trim($name)
            ]);
        }

        return $vendor->id;
    }

    /**
     * @return array{property: mixed, option: mixed}[]
     * @param mixed $category_id
     */
    private function getCategoryPropertiesIds($category_id): array
    {
        $properties = $this->getCategoryProperties();

        $properties = $properties->map(function (array $p): array {
            if (is_numeric($p['value']) && (string)$p['value'] == (int)$p['value']) {
                $p['value'] = (int)$p['value'];
            }

            return $p;
        });

        $array = [];
        foreach ($properties as $property) {
            /** @var $selfp Property */
            $selfp = $this->cache('property_' . $property['name'], fn () => Property::getByNameOrCreate($property['name']));
            $selfp->syncCategories($category_id);

            /** @var $np PropertyOption */
            $np = $this->cache('option_' . $property['value'] . '_' . $selfp->id, fn () => PropertyOption::getByNameOrCreate($property['value'], $selfp->id));
            $array[$np->id] = ['property' => $selfp, 'option' => $np];
        }

        return $array;
    }

    private function getCategoryId()
    {
        $categories = $this->getCategoryPath();
        $parent_id = null;
        $total = $categories->count();
        foreach ($categories as $row => $category) {
            if ($category->self_id) {
                $parent_id = $category->self_id;
                continue;
            }

            $self = BaseCategory::where('parent_id', $parent_id)
                ->where('name', 'like', trim((string) $category->name))
                ->first();
            if (!$self) {
                $_POST['seo_title'] = $category->meta_title ?: $category->name;
                $_POST['seo_description'] = $category->meta_description ?: $category->description;
                $self = BaseCategory::add([
                    'name' => trim((string) $category->name),
                    'parent_id' => $parent_id,
                    'description' => $category->description,
                    'url_handle' => $category->url_key
                ], true);
            } else {
                $parent_id = $self->id;
            }

            if ($category instanceof Model) {
                $category->self_id = $self->id;
                $parent_id = $self->id;
                $category->save();
            } else {
                $parent_id = $self->id;
            }
        }

        return $parent_id;
    }

    /**
     * @param mixed $weight
     * @return mixed
     */
    protected function _weightFormat($weight): float
    {
        if ($this->manager->getSetting('product_weight') == 'kilogram') {
            return (float)$weight;
        } elseif ($this->manager->getSetting('product_weight') == 'pound') {
            return (float)($weight * 2.20462);
        }

        return (float)$weight;
    }

    /**
     * @return Collection
     */
    protected function _getProductAttributes()
    {
        $attributes = $this->cache('attributes_' . $this->product->set, fn () => ProductsAttribute::where('set_id', $this->product->set)
            ->with('options')->where('scope', 'global')->get());

        $array = [];
        foreach ($attributes as $a) {
            if (isset($this->product->product[$a['code']]) && ($option_value = $this->product->product[$a['code']])) {
                $value = $a->options->where('value', $option_value)->first();
                $array[] = [
                    'code' => $a['code'],
                    'name' => $a->frontend_label[0]['label'],
                    'value' => $value ? $value->label : $option_value
                ];
            }
        }

        return Collection::make($array)->filter(fn ($a) => $a['value']);
    }

    /**
     * @return mixed
     */
    private function cache(string $key, \Closure $callback)
    {
        return ArrayCache::remember($key, $callback);
    }
}
