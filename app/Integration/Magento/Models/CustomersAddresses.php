<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 7.12.2016 г.
 * Time: 14:42 ч.
 */

namespace App\Integration\Magento\Models;

use Illuminate\Database\Eloquent\Model;

class CustomersAddresses extends Model
{
    protected $table = '@app_magento_customers_addresses';

    protected $fillable = [
        'customer_address_id', 'customer_id', 'created_at',
        'updated_at', 'get_info', 'city', 'company',
        'country_id', 'firstname', 'lastname',
        'postcode', 'region', 'street',
        'telephone', 'fax', 'vat_id', 'vat_is_valid',
        'is_default_billing', 'is_default_shipping'
    ];

    public $timestamps = false;

}
