<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 7.12.2016 г.
 * Time: 14:22 ч.
 */

namespace App\Integration\Magento\Models;

use Illuminate\Database\Eloquent\Model;

class ProductsAttributeOptions extends Model
{
    protected $table = '@app_magento_products_attribute_options';

    protected $fillable = [
        'value', 'attribute_id', 'label'
    ];

    public $timestamps = false;

    protected function casts(): array
    {
        return [
            'value' => 'json',
        ];
    }

}
