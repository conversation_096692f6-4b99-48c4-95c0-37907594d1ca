<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 7.12.2016 г.
 * Time: 14:37 ч.
 */

namespace App\Integration\Magento\Models;

use Illuminate\Database\Eloquent\Model;

class Customers extends Model
{
    /**
     * @var string
     */
    protected $table = '@app_magento_customers';

    /**
     * @var array
     */
    protected $fillable = [
        'customer_id', 'created_at', 'updated_at',
        'get_info', 'store_id', 'website_id',
        'created_in', 'default_billing', 'default_shipping',
        'email', 'firstname', 'lastname', 'group_id',
        'get_addresses',
    ];

    /**
     * @var bool
     */
    public $timestamps = false;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function group()
    {
        return $this->hasOne(CustomerGroups::class, 'customer_group_id', 'group_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function addresses()
    {
        return $this->hasMany(CustomersAddresses::class, 'customer_id', 'customer_id');
    }
}
