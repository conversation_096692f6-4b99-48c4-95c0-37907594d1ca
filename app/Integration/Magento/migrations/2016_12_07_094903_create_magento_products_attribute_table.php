<?php

declare(strict_types=1);

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateMagentoProductsAttributeTable extends Migration
{
    protected $table = '@app_magento_products_attribute';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create($this->table, function (Blueprint $table): void {
            $table->bigIncrements('id');
            $table->integer('attribute_id', false, true)->index()->nullable()->default(null);
            $table->integer('set_id', false, true)->index()->nullable()->default(null);
            $table->string('code')->nullable()->default(null);
            $table->string('type')->nullable()->default(null);
            $table->boolean('get_info')->index()->default(0);
            $table->string('frontend_input')->nullable()->default(null);
            $table->string('default_value')->nullable()->default(null);
            $table->text('frontend_label')->nullable()->default(null);
            $table->string('scope')->nullable()->default(null);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists($this->table);
    }
}
