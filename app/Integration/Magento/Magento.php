<?php

declare(strict_types=1);

namespace App\Integration\Magento;

use App\Models\Queue\SiteQueue;
use Modules\Apps\Abstractions\Managers\AbstractImportsExportsManager;

class Magento extends AbstractImportsExportsManager
{
    public const APP_KEY = 'magento';

    protected $tables_prefix = '@app_magento_';

    protected $tables = [
        'stores', 'categories', 'products_set',
        'products_attribute', 'products_attribute_options',
        'products', 'products_images', 'products_tags',
        'products_stock', 'customer_group', 'customers',
        'customers_addresses', 'products_options',
        'products_options_values'
    ];

    /**
     * @param $is_install
     * @return null|string
     */
    #[\Override]
    public function getMigrationsPath($is_install): ?string
    {
        return str_replace(base_path(), '', __DIR__) . '/migrations';
    }

    /**
     * @throws \App\Exceptions\Error
     */
    #[\Override]
    public function uninstall(): void
    {
        SiteQueue::removeQueueByMapping('magento_sync');
    }

    /**
     * @throws \App\Exceptions\Error
     * @throws \Throwable
     */
    public function reset(): void
    {
        \Illuminate\Support\Facades\DB::transaction(function ($db): void {
            $db->statement('SET FOREIGN_KEY_CHECKS=0');
            $this->removeSettings([
                'step', 'finish', 'last_execute',
                'total_stores', 'total_categories',
                'total_categories_elapsed', 'total_sets',
                'total_sets_elapsed', 'total_products',
                'total_products_elapsed', 'total_products_elapsed_create',
                'total_customer_groups', 'total_customers', 'total_customers_elapsed',
                'total_customers_elapsed_create', 'total_attributes',
                'total_attributes_elapsed', 'working', 'start_task',
                'total_products_info_elapsed', 'total_products_images_elapsed',
                'total_products_tags_elapsed', 'total_products_stock_elapsed',
            ]);
            foreach ($this->tables as $table) {
                \Illuminate\Support\Facades\DB::table($this->tables_prefix . $table)->truncate();
            }

            $db->statement('SET FOREIGN_KEY_CHECKS=1');
            $this->uninstall();
        }, 5);
    }

    /**
     * @inheritdoc
     */
    #[\Override]
    public function isWorking(): bool
    {
        if ($this->isInstalled()) {
            return !!$this->getSetting('working');
        }

        return false;
    }

    public function appInfo(): array
    {
        return [static::APP_KEY => [
            'direct' => 'apps/magento',
            'uninstall' => 'apps/magento/uninstall',
            'icon' => 'app-transfer-magento.png',
            'name' => __('magento.importer'),
            'description' => __('magento.importer_description'),
        ]];
    }

    /**
     * @return SiteQueue|bool
     * @throws \Exception
     */
    public static function startSync()
    {
        return SiteQueue::createQueueByMapping('magento_sync');
    }
}
