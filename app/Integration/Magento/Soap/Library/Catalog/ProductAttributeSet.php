<?php

declare(strict_types=1);

namespace App\Integration\Magento\Soap\Library\Catalog;

use App\Integration\Magento\Soap\Library\Catalog;

class ProductAttributeSet
{
    /**
     * @var array
     */
    protected $set;

    /**
     * ProductAttributeSet constructor.
     * @param Catalog $catalog
     */
    public function __construct(protected \App\Integration\Magento\Soap\Library\Catalog $catalog)
    {
    }

    /**
     * @return array
     */
    public function set()
    {
        if (is_null($this->set)) {
            $this->set = $this->catalog->getClient()->call('catalog_product_attribute_set.list');
        }

        return $this->set;
    }
}
