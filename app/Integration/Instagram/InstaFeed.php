<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: user
 * Date: 8/1/2017
 * Time: 4:00 PM
 */

namespace App\Integration\Instagram;

use App\Helper\ArrayCache;
use App\Helper\Cache\CcCache;
use App\Models\Oauth\FacebookPage;
use App\Models\Oauth\SocialAccount;
use App\Models\Router\Exceptions;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\RequestOptions;
use Illuminate\Support\Str;

/**
 * Class Olx
 * @package App\Applications\Managers
 */
class InstaFeed
{
    /**
     * @var Client
     */
    protected $client;

    /**
     * @var SocialAccount
     */
    protected $account;

    /**
     * InstaFeed constructor.
     */
    public function initAccount()
    {
        if (empty($this->account)) {
            if ($this->account = $this->getAccount()) {
                $this->client = new Client();
            }
        }

        return $this->account;
    }

    /**
     * @param $method
     * @param $endpoint
     * @param array $data
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function send($method, $endpoint, array $data = [])
    {
        $data = array_merge([
            RequestOptions::TIMEOUT => 1,
            RequestOptions::CONNECT_TIMEOUT => 1,
            RequestOptions::READ_TIMEOUT => 1,
        ], $data);

        try {
            return $this->client->request(strtoupper((string) $method), $endpoint, $data);
        } catch (ClientException | Exception $e) {
            $this->handleException($e);
            return;
        }
    }

    /**
     * @param $endpoint
     * @param array $data
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function get($endpoint, array $data = [])
    {
        return $this->send('get', $endpoint, $data);
    }

    /**
     * @param $limit
     * @return mixed
     */
    public function getAll($limit)
    {
        return CcCache::remember('instagram.media', config('cache.ttl_1h'), function () use ($limit): null|array|bool {
            $businessAccountId = $this->getBusinessAccountId();
            if ($businessAccountId) {
                $query = [
                    'fields' => 'username,media_url,caption,like_count,comments_count,timestamp,media_type',
                    'access_token' => $this->initAccount()->token,
                ];

                $response = $this->get(
                    'https://graph.facebook.com/v3.2/' . $businessAccountId . '/media',
                    [
                    'query' => $query,
                ]
                );

                if ($response) {
                    $data = json_decode((string) $response->getBody()->getContents(), true)['data'];

                    $data = array_filter($data, fn ($media): bool => in_array($media['media_type'], ['CAROUSEL_ALBUM', 'IMAGE']));

                    return array_slice($data, 0, intval($limit));
                }
            }

            return false;
        }, ['social_accounts']) ? : null;
    }

    /**
     * @return string|null
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getBusinessAccountId()
    {
        if ($this->initAccount()) {
            $query = [
                'fields' => 'instagram_business_account',
                'access_token' => $this->initAccount()->token,
            ];

            $page = ArrayCache::remember('insta-feed-page', fn () => FacebookPage::where('site_id', site('site_id'))->first());

            if ($page) {
                $response = $this->get(
                    'https://graph.facebook.com/v3.2/' . $page->page_id,
                    [
                    'query' => $query,
                ]
                );

                if (empty($response)) {
                    return;
                }

                $response = json_decode((string) $response->getBody()->getContents(), true);

                if (isset($response['instagram_business_account'])) {
                    return $response['instagram_business_account']['id'];
                }
            }
        }

        return;
    }

    /**
     * @return SocialAccount|null
     */
    protected function getAccount()
    {
        if (!$account = SocialAccount::findBySiteOwner(InstagramManager::SOCIAL_ACCOUNT_KEY)) {
            return;
        }

        return $account;
    }

    /**
     * @param Exception $e
     * @return void|null
     * @throws Exception
     */
    protected function handleException(\Throwable $e)
    {
        if ($e instanceof ConnectException) {
            //time outed
            return;
        }

        if ($e instanceof ClientException) {
            if (!$this->account) {
                return;
            }

            $this->account->update(['active' => 0]);
        }

        if (Str::contains($e->getMessage(), ['access token'])) {
            $token = \FacebookPage::refreshToken();
            if (!empty($token)) {
                return;
            }
        }

        Exceptions::createFromThrowable($e, 'Instagram ERROR');
    }
}
