<?php

declare(strict_types=1);

namespace App\Integration\Instagram\Providers;

use App\Integration\Instagram\InstagramManager;
use Illuminate\Support\ServiceProvider;

/**
 * Class MessengerServiceProvider
 * @package App\Integration\Facebook\Providers
 */
class InstagramServiceProvider extends ServiceProvider
{
    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = false;

    #[\Override]
    public function register(): void
    {
        $this->app->singleton('instagram', fn (): \App\Integration\Instagram\InstagramManager => new InstagramManager());
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    #[\Override]
    public function provides()
    {
        return [
            InstagramManager::APP_KEY
        ];
    }
}
