<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 30.8.2017 г.
 * Time: 13:32
 */

namespace App\Integration\Instagram;

use App\Models\Product\Product;
use FacebookPage;
use Illuminate\Support\Collection;
use Modules\Apps\Abstractions\Managers\AbstractAppManager;
use Illuminate\Support\Facades\View;

class InstagramManager extends AbstractAppManager
{
    /**
     *
     */
    public const APP_KEY = 'instagram';

    public const INSTAGRAM_URL = 'https://www.instagram.com/';

    public const SOCIAL_ACCOUNT_KEY = 'facebook';

    public const PERMISSION_KEY = 'instagram_basic';

    protected \App\Integration\Instagram\InstaFeed $client;

    public function __construct()
    {
        $this->client = new InstaFeed();
    }

    /**
     * @return array
     */
    public function appInfo(): array
    {
        return [static::APP_KEY => [
            'direct' => 'apps/instagram',
            'uninstall' => 'apps/instagram/uninstall',
            'icon' => 'icon-instagram.png',
            'name' => __('instagram.info.title'),
            'description' => __('instagram.help.install'),
        ]];
    }

    /**
     * @return string
     */
    public function getViewsDir()
    {
        return app_path('Integration/Instagram/resources/views/');
    }

    /**
     * @param Product $product
     * @return null|string
     * @throws \App\Exceptions\Error
     * @throws \SmartyException
     */
    public function images($product = null)
    {
        if (!$this->isInstalled()) {
            return;
        }

        if (!FacebookPage::configured()) {
            return;
        }

        $settings = $this->getSettings();

        if (activeRoute('product.view') && !$settings->get('show_in_footer_in_details_page') && is_null($product)) {
            return;
        }

        $sort_by = is_null($product) ? $settings->get('sort_by') : $settings->get('details_page_sort_by');
        $limit = is_null($product) ? $settings->get('limit') : $settings->get('details_page_limit');
        $active = is_null($product) ? $settings->get('active') : $settings->get('active_in_details_page');
        $section_title = is_null($product) ? $settings->get('section_title') : $settings->get('details_page_section_title');

        $data = $this->client->getAll($limit);

        if ($active && !empty($data)) {
            $data = static::sortImages($data, $sort_by);
            $settings = [
                'show_info' => is_null($product) ? $settings->get('show_info') : $settings->get('show_info_in_details'),
                'grid_class' => is_null($product) ? $settings->get('per_row') : $settings->get('per_row_in_details'),
                'section_title' => $section_title,
            ];

            return View::addNamespace('ig', $this->getViewsDir())->fetch('ig::images', [
                'data' => $data,
                'settings' => $settings,
                'instagramUrl' => static::INSTAGRAM_URL
            ]);
        }

        return;
    }

    /**
     * @param $data
     * @param $sort_by
     * @return Collection
     */
    public static function sortImages($data, $sort_by)
    {
        $sort = explode('-', (string) $sort_by);
        if ($sort[0] !== 'none') {
            $type = '';
            switch (empty($sort[1]) ? 'recent' : $sort[1]) {
                case 'liked':
                    $type = 'like_count';
                    break;
                case 'commented':
                    $type = 'comments_count';
                    break;
                case 'recent':
                    $type = 'timestamp';
                    break;
            }

            $data = Collection::make($data);
            if ($sort[0] == 'least') {
                return $data->sortBy($type);
            } else {
                return $data->sortByDesc($type);
            }
        }

        return $data;
    }
}
