<?php

declare(strict_types=1);

namespace App\Integration\Mobica\Models;

use App\Helper\Format;
use App\Models\Customer\Customer;
use App\Traits\Crudling;
use App\Traits\DbTimezone;
use Carbon\Carbon;
use Schema;

/**
 * Class MobicaSentMessage
 * @package App\Integration\Mobica\Models
 * @property string $message_id
 * @property int $status
 * @property Carbon $date_delivered
 */
class MobicaSentMessage extends \Eloquent
{
    use Crudling;

    use DbTimezone;
    public const STATUS_NOT_SENT = 0;

    public const STATUS_SENT = 1004;

    public const STATUS_DELIVERED = 1000;

    public const STATUS_SEEN = 1001;

    public const STATUS_UNDELIVERED = 999;

    /**
     * @var string
     */
    protected $table = '@app_mobica_sent_messages';

    /**
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * @var array
     */
    protected $fillable = [
        'message_id',
        'infobip_message_id',
        'event',
        'title',
        'status',
        'phone',
        'channel',
        'language',
        'viber_message',
        'mobile_message',
        'url',
        'button',
        'image',
        'tag',
        'customer_id',
        'subscriber_id',
        'group_id',
        'segment_id',
        'date_sent',
        'date_delivered',
        'created_at',
        'updated_at',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'status_translated',
        'date_delivered_formatted',
    ];

    /**
     * @param array $attributes
     * @return mixed
     */
    public function __construct(array $attributes = [])
    {
        if (!Schema::hasTable($this->table)) {
            $this->table = 'campaigns_viber_sent_messages';
        }

        parent::__construct($attributes);
    }

    /**
     * @param string|null $status
     * @return int
     */
    public static function getStatusValue(?string $status)
    {
        return match ($status) {
            'PENDING' => static::STATUS_SENT,
            'DELIVERED' => static::STATUS_DELIVERED,
            'SEEN' => static::STATUS_SEEN,
            'EXPIRED', 'UNDELIVERED', 'UNDELIVERABLE' => static::STATUS_UNDELIVERED,
            default => static::STATUS_NOT_SENT,
        };
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function customer()
    {
        return $this->hasOne(Customer::class, 'id', 'customer_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function message()
    {
        return $this->hasOne(MobicaMessage::class, 'id', 'message_id');
    }

    protected function casts(): array
    {
        return ['date_sent' => 'datetime', 'date_delivered' => 'datetime', 'created_at' => 'datetime', 'updated_at' => 'datetime'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStatusTranslatedAttribute($value): string
    {
        return match ($this->status) {
            static::STATUS_SENT => '<span style="color: #edc628;">' . __('mobica.status.sent') . '</span>',
            static::STATUS_DELIVERED => '<span style="color: #28a745;">' . __('mobica.status.delivered') . '</span>',
            static::STATUS_SEEN => '<span style="color: #28a745;">' . __('mobica.status.seen') . '</span>',
            static::STATUS_UNDELIVERED => '<span style="color: #fc4f4e;">' . __('mobica.status.undelivered') . '</span>',
            default => '<span style="color: #fc4f4e;">' . __('mobica.status.not_sent') . '</span>',
        };
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDateDeliveredFormattedAttribute($value): ?string
    {
        if (empty($this->date_delivered)) {
            return null;
        }
        return Format::datetime($this->date_delivered);
    }
}
