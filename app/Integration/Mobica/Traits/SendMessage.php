<?php

declare(strict_types=1);

namespace App\Integration\Mobica\Traits;

use App\Common\Status;
use App\Exceptions\Error;
use App\Helper\Plan;
use App\Helper\YesNo;
use App\Integration\Mobica\Mobica;
use App\Integration\Mobica\Models\MobicaMessage;
use App\Models\Customer\Customer;
use Linker;

trait SendMessage
{
    /**
     * @param string $event
     * @param string $phone
     * @param array $attributes
     * @throws Error
     */
    public function send(string $event, string $phone, array $attributes): void
    {
        if (!app('mobica')->isInstalled() || !app('mobica')->isActive()
            || !Plan::allowCreate(Mobica::FEATURE_KEY_VIBER)) {
            return;
        }

        $message = MobicaMessage::where([
            'event' => $event,
            'language' => site('language'),
            'status' => MobicaMessage::STATUSES['active'],
        ])->first();

        $customer = Customer::find($attributes['customer_id']);

        if ($message && $phone && $customer->marketing == YesNo::True) {

            $mobile = $message->mobile_message;
            $viber = $message->viber_message;

            $attributes['customer_phone'] = $phone;
            foreach ($attributes as $attribute => $value) {
                if (is_string($value) || is_int($value) || $value) {

                    if ($attribute == 'status' || $attribute == 'status_fulfillment') {
                        $attribute = 'order_status';
                        $value = Status::order($value);
                    }

                    $mobile = str_replace('{$' . $attribute . '}', $value, $mobile);
                    $viber = str_replace('{$' . $attribute . '}', $value, $viber);
                }
            }

            if (isset($attributes['total'])) {
                $mobile = str_replace('{$total}', $attributes['total'], $mobile);
                $viber = str_replace('{$total}', $attributes['total'], $viber);
            }

            if (isset($attributes['order_id'])) {
                $mobile = str_replace(
                    '{$site_order_link}',
                    site()->getSiteUrl() . Linker::account('orders'),
                    $mobile
                );
                $viber = str_replace(
                    '{$site_order_link}',
                    site()->getSiteUrl() . Linker::account('orders'),
                    $viber
                );
            }

            $mobile = str_replace('{$shop_url}', site()->getSiteUrl(), $mobile);
            $viber = str_replace('{$shop_url}', site()->getSiteUrl(), $viber);

            $data = [
                'viber_message' => $viber,
                'mobile_message' => $mobile,
                'event' => $event,
                'phone' => $phone,
                'customer_id' => $attributes['customer_id'] ?? null,
                'language' => $message->language,
                'message_id' => $message->id,
                'url' => $message->url,
                'button' => $message->button,
            ];

            app('mobica')->sendViberMessage($data);

            $message->increment('count');
        }
    }
}
