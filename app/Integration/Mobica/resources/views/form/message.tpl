<div class="box">
	<div class="box-title">
		<div class="box-title-text">
			<h5>{t}mobica.box.message.title{/t}</h5>
		</div>
	</div>
	<div class="box-section">
		<div class="row form-group">
			<div class="col-xs-12">
				<textarea id="viber_message" name="viber_message" class="form-control js-message-text"
						  data-message-target="#text-target" rows="7">{$message->viber_message|default}</textarea>
				<div id="mobile_message_counter" class="box-title-text pull-right">
					{t}mobica.remaining_characters{/t}:
					<strong><span id="remaining"></span></strong>
				</div>
			</div>
		</div>
	</div>
	{if 0}
	<div class="box-title">
		<div class="box-title-text">
			<h5>{t}mobica.box.mobile_message.title{/t}</h5>
			<p>{t}mobica.box.mobile_message.text{/t}</p>
			<hr />
			<div class="row form-group">
				<div class="col-md-12">
					<div class="form-group">
						<div class="pull-right">
							<input id="mobile_message_required" name="mobile_message_required"
								   type="checkbox" class="switch"
								   onkeyup="countChar(this)"
								   onchange="countChar(this)"
								   value="1"{if isset($message) && $message->mobile_message} checked="checked"{/if} />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="box-section"{if !isset($message) || !$message->mobile_message} style="display:none;"{/if}
		 id="messages-message">
		<div class="row form-group">
			<div class="col-xs-12">
				<textarea id="mobile_message" name="mobile_message" class="form-control"
						  data-message-target="#text-target" rows="7">{$message->mobile_message|default}</textarea>
				<div id="mobile_message_counter" class="box-title-text pull-right">
					{t}mobica.remaining_characters{/t}:
					<strong><span class="remaining"></span></strong>
				</div>
			</div>
		</div>
	</div>
	{/if}
</div>
<script>
	{if 0}
	$('#mobile_message').countSms('#mobile_message_counter');
	{/if}
	function countChar(val) {
		var len = val.value.length;
		if (len >= 1000) {
			val.value = val.value.substring(0, 1000);
		} else {
			$('#remaining').text(1000 - len);
		}
	};
	countChar(document.getElementById('viber_message'));
	$('textarea').on("input", function() {
		countChar(document.getElementById('viber_message'));
	});

	$('#mobile_message_required').on('change', function() {
		if($(this).is(':checked')) {
			$('#messages-message').slideToggle('show');
			$('#mobile_message').removeAttr('disabled');
			$('#mobile_message').removeProp('disabled');
		} else {
			$('#messages-message').slideToggle('hide');
			$('#messages-message').attr('disabled', 'disabled');
			$('#messages-message').prop('disabled', true);
		}
	});
</script>