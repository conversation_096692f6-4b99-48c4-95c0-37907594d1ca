{include file="./_daterange.tpl"}

{capture name="body_class"}white-panel{/capture}

<div class="page-breadcrumb clearfix">
	{$breadcrumb = [
	['title' => "{t}sidebar.apps{/t}", 'href' => "{route('admin.apps')}"],
	['title' => "{t}mobica.info.title{/t}", 'href' => "{route('apps.mobica')}"],
	['title' => "{t}mobica.marketing_messages{/t}"]
	]}
	{include file="includes/breadcrumb.tpl"}
	<form data-filters="{$search_object->getInitFilters()}" method="post" class="filters_form"
		  action="{route('apps.mobica.custom')}" data-module="{$search_object->getModule()}"
		  data-filter="{$search_object->getFilter()}">
		<div class="pull-right">
			<a href="{route('apps.mobica.custom.form')}" class="btn btn-primary"
			   data-panel-class="wide" data-ajax-panel="true">
				{t}mobica.new_campaign{/t}
			</a>
			<input name="daterange"{if isset($daterange)} value="{$daterange}"{/if} class="daterangepicker" />
		</div>
	</form>
</div>
<div class="content-padding clearfix">
	<div class="grid-wrapper grid-products padding-top-0" id="table_sales"
		 data-url="{route('apps.mobica.custom')}" data-form=".filters_form" data-chart="yes">
		<div class="grid-controls no-bulk"></div>
		<div class="content-padding padding-top-0">
			<table class="listing">
				<thead>
				<tr>
					<th data-field="title_formatted" class="text-left" data-align="left" data-sort="no">
						{t}mobica.title{/t}
					</th>
					<th data-field="message" class="text-left" data-align="left" data-sort="no" style="min-width: 300px;">
						{t}mobica.message{/t}
					</th>
					<th data-field="sent_count" class="text-left" data-align="left" data-sort="no">
						{t}mobica.count{/t}
					</th>
					{if 0}
					<th data-field="groups_formatted" class="text-left" data-align="left" data-sort="no" style="min-width: 300px;">
						{t}mobica.groups{/t}
					</th>
					{/if}
					<th data-field="segments_formatted" class="text-left" data-align="left" data-sort="no" style="min-width: 300px;">
						{t}mobica.segments{/t}
					</th>
					<th data-field="created_at" class="text-left sorting" data-align="left">
						{t}mobica.created_at{/t}
					</th>
					<th data-field="link" class="text-left" data-align="left" data-sort="no"></th>
					<th data-field="send" class="text-left" data-align="left" data-sort="no"></th>
				</tr>
				</thead>
			</table>
		</div>
	</div>
</div>
