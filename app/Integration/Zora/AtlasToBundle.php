<?php

declare(strict_types=1);

namespace App\Integration\Zora;

use App\Events\Models\ProductSaved;
use App\Helper\Format;
use App\Helper\MakeAlert;
use App\Helper\Temp\ProductTemp;
use App\Helper\YesNo;
use App\Integration\Zora\Exceptions\MissingArtProduct;
use App\Integration\Zora\Models\Bundles;
use App\Integration\Zora\Models\WebBundles;
use App\Models\Product\Product;
use App\Models\Router\Exceptions;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Modules\Apps\Administration\Bundles\Helpers\BundleFormatter;
use Modules\Apps\Administration\Bundles\Model\BundleProduct;
use Modules\Core\Core\Models\Alerts;
use stdClass;
use Throwable;

class AtlasToBundle
{
    public const IMG_SERVICE_275 = 'https://cdncloudcart.com/402/products/images/29317/montaz-na-klimatici-image_5ce26c7eb7af1_300x300.jpeg?1564764480'; //Монтаж на климатици
    /**
     * @param \App\Integration\Zora\Models\WebBundles $webBundle
     * @return mixed
     */
    public function transformToBundle(WebBundles $webBundle): true|string
    {
        try {
            Bundles::where('promotion_id', $webBundle->promotion_id)->delete();

            $bundle = $this->getBundleProductFormatter($webBundle);

            $bundle->push();

            $webBundle->delete();

            return true;
        } catch (MissingArtProduct $e) {
            $message = sprintf(
                'Can`t make bundle "%s" with ID: %d. Missing product "%s" with ID: %d in CC database',
                $webBundle->data->name,
                $webBundle->promotion_id,
                $e->getMessage(),
                $e->getCode()
            );

            MakeAlert::mapping('atlas_bundle_' . $webBundle->promotion_id)
                ->setMessage($message)->setType(Alerts::TYPE_ERROR)
                ->save();

            return $message;
        } catch (Throwable $e) {
            Exceptions::createFromThrowable($e);

            $webBundle->update([
                'exception' => $e->getMessage(),
                'try' => \Illuminate\Support\Facades\DB::raw('try + 1')
            ]);

            return $e->getMessage();
        }
    }

    /**
     * @param \App\Integration\Zora\Models\WebBundles $webBundle
     * @return mixed
     */
    protected function getBundleProductFormatter(WebBundles $webBundle): BundleFormatter
    {
        $exists = $this->getModel()
            ->where('app_import', 'bundle-' . $webBundle->promotion_id)->first();

        $formatter = $this->bundleFormatter($exists);

        $products = $this->getBundleProducts($webBundle->data->products);
        $existsProducts = ($exists->clear_bundles ?? collect())->pluck('id', 'product_id');

        if (!$exists && $products->isNotEmpty()) {
            try {
                $firstProduct = $products->sortByDesc('model.price_from')->first();
                $formatter->setCategoryId($firstProduct->model->category_id);
            } catch (Throwable) {
                //
            }
        }

        $formatter->forceFill([
            'app_import' => 'bundle-' . $webBundle->promotion_id,
        ])->fill([
            "exists" => $exists,
            "name" => $exists->name ?? $webBundle->data->name,
            "url_handle" => $exists->url_handle ?? null,
            "active" => $exists ? intval($exists->active == YesNo::True) : 0,
            "draft" => $exists ? intval($exists->draft == YesNo::True) : 0,
            "hidden" => $exists->is_hidden ?? 0,
            "featured" => $exists->featured ?? 0,
            "new" => $exists ? intval($exists->new == YesNo::True) : 0,
            "publish_date" => optional($exists->publish_date ?? $this->msDateFormatCorrection($webBundle->data->FromDate))->toIso8601String(),
            "active_to" => optional($exists->active_to ?? $this->msDateFormatCorrection($webBundle->data->ToDate))->toIso8601String(),
            'per_row' => $products->count(),
            "bundle_products" => $products->map(function (stdClass $product) use ($exists, $existsProducts, $webBundle): array {
                $this->attachToBundlesInfo($product, $webBundle);
                return [
                    "id" => $existsProducts->get($product->model->id),
                    "sort_order" => $product->index,
                    "optional" => false,
                    "individual_price_enabled" => true,
                    "individual_price" => $product->PriceLv,
                    "discount" => 0,
                    "visible_product_details" => true,
                    "visible_cart" => true,
                    "visible_order_details" => true,
                    "price_visible_product_details" => true,
                    "price_visible_cart" => true,
                    "price_visible_order_details" => true,
                    "hide_thumb" => false,
                    "override_title" => false,
                    "title" => null,
                    "override_short_description" => false,
                    "short_description" => null,
                    "individual_qty_enabled" => ($qty = ($product->Qty ?? 1)) > 1,
                    "qty" => $qty,
                    "product" => [
                        "id" => $product->model->id,
                        "name" => $product->model->name,
                        "price_from" => moneyFloat($product->model->price_from),
                        "price_to" => moneyFloat($product->model->price_to)
                    ]
                ];
            })->all(),
            'meta' => [
                "show_image" => boolval($exists ? $exists->meta_data->get('show_image') : true),
                "timer_list" => boolval($exists ? $exists->meta_data->get('timer_list') : false),
                "timer_details" => boolval($exists ? $exists->meta_data->get('timer_details') : false),
            ],
            'variant' => [
                "id" => $exists->default_variant_id ?? null,
                "price" => $products->sum('PriceLv'),
                "type" => "price"
            ]
        ]);

        return $formatter;
    }

    /**
     * @param stdClass $product
     * @param \App\Integration\Zora\Models\WebBundles $webBundle
     * @return mixed
     */
    protected function attachToBundlesInfo(stdClass $product, WebBundles $webBundle)
    {
        Bundles::create([
            'promotion_id' => $webBundle->promotion_id,
            'art_id' => $product->ArtID,
            'service_id' => $product->UslID,
            'cc_id' => $product->model->id,
            'price' => Format::toIntegerPrice($product->PriceLv),
            'name' => is_numeric($product->UslID) ? $product->UslText : $product->ArtName,
        ]);
    }

    /**
     * @param array $products
     * @return mixed
     */
    protected function getBundleProducts(array $products): Collection
    {
        return collect($products)->map(function (stdClass $product, $index): \stdClass {
            if ($product->UslID) {
                $product->model = $this->getService($product);
            } else {
                $product->model = Product::withoutGlobalScopes()->where([
                    'xml_import_id' => config('integration.zora.xml.id'),
                    'xml_import_product_id' => $product->ArtID,
                ])->first();

                if (!$product->model) {
                    throw new MissingArtProduct($product->ArtName, intval($product->ArtID));
                }
            }

            $product->index = $index;

            return $product;
        });
    }

    /**
     * @param stdClass $service
     * @return mixed
     */
    protected function getService(stdClass $service): Product
    {
        $price = Format::toIntegerPrice($service->PriceLv);

        $serviceModel = Product::withoutGlobalScopes()
            ->where('app_import', 'service-' . $service->UslID)
            ->whereHas('variant', function ($query) use ($service, $price): void {
                $query->where('price', $price);
            })
            ->first();

        if (!$serviceModel) {
            $serviceModel = Product::create([
                'name' => $service->UslText,
                'type' => Product::TYPE_SIMPLE,
                'is_hidden' => 1,
                'active' => YesNo::True,
                'draft' => YesNo::False,
                "price_from" => $price,
                "price_to" => $price,
                "tracking" => YesNo::True,
                "continue_selling" => YesNo::False,
                "app_import" => 'service-' . $service->UslID,
            ]);

            $variant = $serviceModel->variant()->firstOrCreate([], [
                'price' => $price,
                'item_id' => $serviceModel->id,
                'quantity' => 99999,
            ]);

            $serviceModel->update([
                'default_variant_id' => $variant->id,
            ]);

            if ($service->UslID == 275) {
                $serviceModel->uploadImageFromUrl(self::IMG_SERVICE_275);
            }

            event(new ProductSaved($serviceModel));

            ProductTemp::updateTempTableAndPopulateByProduct($serviceModel->id);
        } else {
            $serviceModel->update([
                "tracking" => YesNo::True,
                "continue_selling" => YesNo::False,
                "price_from" => $price,
                "price_to" => $price,
            ]);

            $serviceModel->variant()->updateOrCreate([], [
                'price' => $price,
                'item_id' => $serviceModel->id,
                'quantity' => 99999,
            ]);

            ProductTemp::updateTempTableAndPopulateByProduct($serviceModel->id);
        }

        return $serviceModel;
    }

    /**
     * @param mixed $date
     * @return mixed
     */
    protected function msDateFormatCorrection($date): mixed
    {
        [$date] = explode(' ', (string) $date);
        if (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $date)) {
            return Carbon::createFromFormat('Y-m-d', $date);
        }

        return null;
    }

    /**
     * Get the model for bundle products with related data.
     *
     * @return Builder|BundleProduct The model builder instance.
     */
    protected function getModel()
    {
        return BundleProduct::with([
            'bundle_products_cp', 'variant', 'meta_data',
        ]);
    }

    /**
     * @param BundleProduct|null $bundle
     * @return BundleFormatter
     */
    protected function bundleFormatter(?BundleProduct $bundle): BundleFormatter
    {
        if ($bundle === null) {
            /** @var BundleProduct $bundle*/
            $bundle = with(new BundleProduct(), function (BundleProduct $bundle): \Modules\Apps\Administration\Bundles\Model\BundleProduct {
                $bundle->setRelation('bundle_products_cp', collect());
                $bundle->setRelation('meta_data', collect());
                $bundle->setRelation('variant', null);
                return $bundle;
            });
        }

        return BundleFormatter::make($bundle);
    }
}
