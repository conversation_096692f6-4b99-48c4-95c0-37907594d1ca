<?php

declare(strict_types=1);

namespace App\Integration\Zora\Jobs;

use App\Helper\DataBase\Importer;
use Carbon\Carbon;
use App\Integration\Zora\CategoryMap;
use App\Integration\Zora\Traits\ProductImport;
use App\Jobs\Job;
use App\Models\Category\Property;
use App\Models\Category\PropertyOption;
use App\Models\Product\Product;
use App\Models\Product\Vendor;
use Illuminate\Support\Collection;
use Illuminate\Bus\Batchable;
use Modules\Core\Core\Traits\BatchResult;

class GetProductsFromAtlasExecute extends Job
{
    use ProductImport;
    use Batchable;
    use BatchResult;

    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'import9';

    /**
     * @var array $cache
     */
    public $cache = [];

    /**
     * @var array $artIds
     */
    public $artIds = [];

    /**
     * GetProductsFromAtlas constructor.
     */
    public function __construct(array $artIds)
    {
        $this->site_id = site('site_id');
        $this->artIds = $artIds;
    }

    /**
     * {@inheritdoc}
     */
    public function execute()
    {
        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return static::SITE_MAINTENANCE;
        }

        // move job to another platform
        if (!allowSiteByPlatform()) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', platform(), sitePlatform()));
            return static::WRONG_PLATFORM;
        }

        if (!isZora()) {
            return static::EXECUTE_DESTROY;
        }

        /** @var $db \Illuminate\Database\SqlServerConnection */
        $db = app('db.atlas');

        $this->info('Begin get Products from Atlas');

        $fields = [
            'WEB_ARTS_V2.*',
            $this->field('ArtName'),
            $this->field('Model'),
            $this->field('Marka', 'marka'),
            $this->field('grp1Name'),
            $this->field('grp2Name'),
            $this->field('grp3Name'),
            'WEB_ARTS.PercDisc',
            'WEB_ARTS_V2.BarCode',
            \Illuminate\Support\Facades\DB::raw('(SELECT SUM(qty) FROM WEB_QTIES WHERE ArtId = WEB_ARTS_V2.ArtID) AS quantity'), // OUTER APPLY equivalent
        ];

        // Build the query
        $query = $db->table('WEB_ARTS_V2')
            ->leftJoin('WEB_ARTS', 'WEB_ARTS.artID', '=', 'WEB_ARTS_V2.ArtID')
            ->select($fields)
            ->whereIn('WEB_ARTS_V2.ArtID', $this->artIds);

        // Get results
        $products = $query->get();

        //        /** @var Collection $charts */
        $charts = collect();

        if (true) {
//        if (!inDevelopment() || 1) {
            $groups = $products->pluck('ArtID')->chunk(500);
            foreach ($groups as $group) {
                $chart_results = $db->table('WEB_ARTS_CHARS')
                    ->whereRaw('ArtID IN (' . $group->implode(',') . ')')
                    //@todo if use order not get results!!!
                    //->orderBy('TabOrderID', 'asc')
                    ->get();
                $this->info(sprintf('Get %d charts for products', $chart_results->count()));
                $charts = $charts->merge($chart_results);
            }

            $charts = $charts->map(function ($property) {
                $property_name = trim(trim((string) $nvalue = $property->{$this->fieldTranslation('FieldName')}) ? $nvalue : 'Други');
                $option_value = trim((string) $property->{$this->fieldTranslation('Val')});

                return (object)[
                    'ArtID' => $property->ArtID,
                    'name' => $property_name,
                    'value' => $option_value,
                ];
            })->groupBy('ArtID');
        }

        $this->info(sprintf('zora:synchronisation get for update/import %d products for site ID %d', $products->count(), $this->site_id));

        $xml_import_id = config('integration.zora.xml.id');
        $categoryMap = new CategoryMap();
        $products = $products->map(function ($product, $key) use ($xml_import_id, $categoryMap, $charts): ?array {
            if (!isset($product->BarCode)) {
                $product->BarCode = null;
            }

            if ($product->grp3ID) {
                $product->category_id = $categoryMap->getCategoryByKey($product->grp3ID);
            } elseif ($product->grp2ID) {
                $product->category_id = $categoryMap->getCategoryByKey($product->grp2ID);
            } elseif ($product->grp1ID) {
                $product->category_id = $categoryMap->getCategoryByKey($product->grp1ID);
            } else {
                $product->category_id = null;
            }

            if (empty($product->category_id)) {
                if (!\Illuminate\Support\Facades\DB::table('@products_without_category')->where('art_id', $product->ArtID)->exists()) {
                    \Illuminate\Support\Facades\DB::table('@products_without_category')->insert([
                        'art_id' => $product->ArtID,
                        'grp1' => $product->grp1ID,
                        'grp2' => $product->grp2ID,
                        'grp3' => $product->grp3ID,
                    ]);
                }

                return null;
            } else {
                \Illuminate\Support\Facades\DB::table('@products_without_category')->where('art_id', $product->ArtID)->delete();
            }

            $product = (object)array_map('trim', json_decode(json_encode($product), true));
            $exists_product = Product::withHiddenItems()->where('xml_import_product_id', $product->ArtID)
                ->where('xml_import_id', $xml_import_id)->first();
            $product->id = $exists_product ? $exists_product->id : null;
            $product->internal_id = $product->id;

            $product->vendor_id = $this->_findVendorId($product->marka);
            //$properties = Collection::make(AtlasToProduct::parseParameters($product));

            if (!inDevelopment() || 1) {
                $properties = $charts->has($product->ArtID) ? $charts->get($product->ArtID) : collect();

                \Illuminate\Support\Facades\DB::table('@products_properties')->where('art_id', $product->ArtID)->delete();
                \Illuminate\Support\Facades\DB::table('@products_properties')->insert([
                    'art_id' => $product->ArtID,
                    'properties' => $properties->toJson()
                ]);

                if ((int)$product->garanc > 0) {
                    $properties = $properties->prepend((object)[
                        'name' => 'Гаранция',
                        'value' => sprintf('%d м.', (int)$product->garanc),
                    ]);
                }

                if ($product->isVgraden) {
                    $properties = $properties->prepend((object)[
                        'name' => 'Други',
                        'value' => 'Вграден',
                    ]);
                }

                if ((int)$product->garanc > 0 || $product->isVgraden) {
                    \Illuminate\Support\Facades\DB::table('@products_properties')->where('art_id', $product->ArtID)->update([
                        'properties_extended' => $properties->toJson()
                    ]);
                }

                $exists_properties = Property::with('categoriesIds')
                    ->whereIn('name', $properties->pluck('name'))->get()->keyBy('name');

                $mapping = new CategoryMap();
                $properties = $properties->map(function ($property) use ($exists_properties, $mapping, $product) {
                    $property->property_id = null;
                    $cat = [];
                    $property->categories = [];
                    if ($exist = $exists_properties->get($property->name)) {
                        $property->property_id = $exist->id;
                        $cat = $exist->categoriesIds->pluck('category_id')->all();
                    }

                    if (!empty($product->grp3ID) && !is_null($map = $mapping->getCategoryByKey($product->grp3ID))) {
                        $cat[] = (int)$map;
                    } elseif (!empty($product->grp2ID) && !is_null($map = $mapping->getCategoryByKey($product->grp2ID))) {
                        $cat[] = (int)$map;
                    } elseif (!empty($product->grp1ID) && !is_null($map = $mapping->getCategoryByKey($product->grp1ID))) {
                        $cat[] = (int)$map;
                    }

                    $property->categories = array_unique($cat);
                    return $property;
                });
                $exists_options = PropertyOption::whereIn('value', $properties->pluck('value'))
                    ->whereIn('property_id', $properties->pluck('property_id'))
                    ->get()->groupBy('property_id')->map(fn ($options) =>
                        /** @var PropertyOption[]|Collection $options */
                        $options->pluck('id', 'value'));
                /** @var Collection $properties */
                $properties = $properties->map(function ($property) use ($exists_options) {
                    $property->option_id = null;
                    if ($property->property_id && $exists_options->has($property->property_id)) {
                        $property->option_id = $exists_options->get($property->property_id)->get($property->value);
                    }

                    return $property;
                });
                $product->properties = $properties->all();
            } else {
                $product->properties = collect();
            }

            if ($key % 250 === 0) {
                $this->info(sprintf('Total formatted: %d', $key + 250));
            }

            return [
                'ArtId' => $product->ArtID,
                'data' => json_encode($product),
                'product_id' => $product->id,
                'created_at' => Carbon::now()->setTimezone('Europe/Sofia')->format('Y-m-d H:i:s'),
            ];
        })->filter();

        $this->info(sprintf('Total formatted: %d', $products->count()));

        $this->info(sprintf('zora:synchronisation formatted for update/import %d products for site ID %d', $products->count(), $this->site_id));

        \Illuminate\Support\Facades\DB::reconnect();

        $this->info('Init import for web arts');
        $import = new Importer('utf8', 'utf8_unicode_ci');
        $this->info('Set web arts');
        $import->setData('@_zora_web_arts', $products->all());

        $this->info('Begin execute sql for add web arts');
        $import->execute();

        //        $this->setBatchJobResult($products->pluck('ArtId')->values()->all());

        return static::EXECUTE_DESTROY;
    }




    /**
     * @param $name
     * @return null|int
     */
    private function _findVendorId($name)
    {
        /*$key = sprintf('vendor_%s', $name);
        if(isset($this->cache[$key])) {
            return $this->cache[$key];
        }

        $id = Vendor::where('name', 'like', $name)->value('id');
        if(is_numeric($id)) {
            return $this->cache[$key] = $id;
        }

        return $id;*/
        return Vendor::where('name', 'like', $name)->value('id');
    }

}
