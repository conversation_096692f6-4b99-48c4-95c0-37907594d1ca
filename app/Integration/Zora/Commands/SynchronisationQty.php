<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 16.6.2016 г.
 * Time: 17:28 ч.
 */

namespace App\Integration\Zora\Commands;

use App\Helper\DataBase\Importer;
use App\Integration\Zora\AtlasToProduct;
use App\Integration\Zora\Jobs\UpdateTempTables;
use App\Integration\Zora\Manager;
use App\Models\Router\Site;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class SynchronisationQty extends AbstractCommand
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'zora:synchronisation-qty';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronisation products quantity by shop with Atlas ERP for zora.bg';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->exec(function (Site $site): void {
            if (!isZora()) {
                return;
            }

            if (AtlasToProduct::lang() == 'Bul') {
                $this->execUpdateProductsQuantityBul($site);
            } elseif (AtlasToProduct::lang() == 'Eng') {
                $this->execUpdateProductsQuantityEng($site);
            }

        }, config('integration.zora.id', []));
    }

    /**
     * @param \App\Models\Router\Site $site
     * @return mixed
     */
    protected function execUpdateProductsQuantityBul(Site $site)
    {
        $manager = new Manager(app(), site('site_id'));
        if (!$manager || !$manager->is('Zora')) {
            return;
        }

        $productsLocal = \Illuminate\Support\Facades\DB::table('products')->whereNotNull('xml_import_product_id')
            ->where('xml_import_id', config('integration.zora.xml.id'))->pluck('id', 'xml_import_product_id');

        if (!$productsLocal->count()) {
            return;
        }

        $this->info(sprintf('Get %d local products for zora update quantity for site ID %d', $productsLocal->count(), $site->site_id));

        $shops_map = \Illuminate\Support\Facades\DB::table('@_zora_store_map')->pluck('shop_id', 'storeID');

        /** @var $db \Illuminate\Database\SqlServerConnection */
        $db = app('db.atlas');

        $products = new Collection();
        /** @var Collection $groups */
        $parts = $productsLocal->chunk(1000);
        foreach ($parts as $groups) {
            $products = $products->merge($db->table('WEB_QTIES')
                ->whereRaw('ArtID IN (' . $groups->keys()->implode(',') . ')')
                ->get());
        }

        $this->info(sprintf('Get %d atlas products in stores for zora update quantity for site ID %d', $products->count(), $site->site_id));

        $new_products = [];
        foreach ($products as $product) {
            $product_id = $productsLocal->get($product->ArtID);
            $shop_id = $shops_map->get($product->storeID ?? $product->StoreID);
            if ($product_id && $shop_id) {
                if (!isset($new_products[$shop_id . '_' . $product_id])) {
                    $new_products[$shop_id . '_' . $product_id] = [
                        'product_id' => $product_id,
                        'shop_id' => $shop_id,
                        'qty' => $product->qty,
                        'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                        'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
                    ];
                } else {
                    $new_products[$shop_id . '_' . $product_id]['qty'] += $product->qty;
                }
            }
        }

        $new_products = array_values($new_products);

        $import = new Importer();
        $import->setDelete('products_quantities');
        $import->setInsertUpdate('products_quantities', $new_products);

        $parts = $productsLocal->chunk(100);
        foreach ($parts as $p) {
            $import->setRaw(/** @lang text */
                'UPDATE `products_variants` SET `quantity` = (SELECT COALESCE(SUM(`qty`),0) FROM `products_quantities` WHERE `product_id` = `products_variants`.`item_id` LIMIT 1) WHERE item_id IN (' . $p->implode(',') . ');'
            );
        }

        $this->info('Begin execute sql for products quantities');
        $import->execute(false);

        if (inDevelopment() !== true) {
            static::sendHook(sprintf('Complete %s %s', 'zora:synchronisation-qty', AtlasToProduct::lang()));
        }

        $this->info('Complete quantity update');
    }

    /**
     * @param \App\Models\Router\Site $site
     * @return mixed
     */
    protected function execUpdateProductsQuantityEng(Site $site)
    {
        $manager = new Manager(app(), site('site_id'));
        if (!$manager || !$manager->is('Zora')) {
            return;
        }

        $productsLocal = \Illuminate\Support\Facades\DB::table('products')->whereNotNull('xml_import_product_id')
            ->where('xml_import_id', config('integration.zora.xml.id'))->pluck('id', 'xml_import_product_id');

        if (!$productsLocal->count()) {
            return;
        }

        $this->info(sprintf('Get %d local products for zora update quantity for site ID %d', $productsLocal->count(), $site->site_id));

        /** @var $db \Illuminate\Database\SqlServerConnection */
        $db = app('db.atlas');

        $products = $db->select('SELECT ArtID, SUM(qty) AS total_qty FROM WEB_QTIES GROUP BY ArtID;');

        $this->info(sprintf('Get %d atlas products in stores for zora update quantity for site ID %d', count($products), $site->site_id));

        $new_products = [];
        foreach ($products as $product) {
            $product_id = $productsLocal->get($product->ArtID);
            if ($product_id) {
                $new_products[] = [
                    'product_id' => $product_id,
                    'shop_id' => null,
                    'qty' => $product->total_qty,
                    'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
                    'updated_at' => Carbon::now()->format('Y-m-d H:i:s'),
                ];
            }
        }

        $import = new Importer();
        $import->setDelete('products_quantities');
        $import->setInsertUpdate('products_quantities', $new_products);

        $parts = $productsLocal->chunk(100);
        foreach ($parts as $p) {
            $import->setRaw(/** @lang text */
                'UPDATE `products_variants` SET `quantity` = (SELECT COALESCE(SUM(`qty`),0) FROM `products_quantities` WHERE `product_id` = `products_variants`.`item_id` LIMIT 1) WHERE item_id IN (' . $p->implode(',') . ');'
            );
        }

        $this->info('Begin execute sql for products quantities');
        $import->execute(false);

        if (!empty($new_products)) {
            $ids = array_unique(array_column($new_products, 'product_id'));
            $groups = array_chunk($ids, 100);
            foreach ($groups as $ids) {
                UpdateTempTables::dispatch(site('site_id'), $ids);
            }
        }

        if (inDevelopment() !== true) {
            static::sendHook(sprintf('Complete %s %s', 'zora:synchronisation-qty', AtlasToProduct::lang()));
        }

        $this->info('Complete quantity update');
    }
}
