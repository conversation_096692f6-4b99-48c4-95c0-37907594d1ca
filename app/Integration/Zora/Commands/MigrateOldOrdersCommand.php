<?php

declare(strict_types=1);

namespace App\Integration\Zora\Commands;

use App\Integration\Zora\Models\ClientLog;
use App\Integration\Zora\Models\OrderSendLog;
use Illuminate\Support\Collection;

class MigrateOldOrdersCommand extends AbstractCommand
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'zora:migrate-log';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Make bundles from Atlas ERP for zora.bg';

    /**
     * Execute the console command.
     *
     * @throws \Exception
     */
    public function handle(): void
    {
        $this->exec(function (): void {
            if (!isZora()) {
                return;
            }

            $this->makeLogOrderTable();

        }, [config('integration.zora.id.Bul', 0)]);

    }

    protected function makeLogOrderTable()
    {
        ClientLog::orderBy('id', 'asc')->where('order_id', '<=', 651118)->chunkById(500, function (Collection $orders): void {
            $orders->map(function (ClientLog $log): void {
                OrderSendLog::unguarded(function () use ($log): void {
                    $request = $this->decodeMessage($log->request);
                    $response = $this->decodeMessage($log->response);
                    [$error, $errorCode] = $this->guessError($this->decodeMessage($log->error), $response);

                    OrderSendLog::updateOrCreate([
                        'order_id' => $log->order_id,
                        'destination' => is_null($log->destination) ? 'econt' : $log->destination,
                    ], [
                        'request' => $request,
                        'response' => $response,
                        'errorCode' => $errorCode,
                        'errorMessage' => $error,
                        'status' => $error ? 'error' : 'success',
                        'created_at' => $log->created_at,
                        'updated_at' => $log->updated_at,
                    ]);
                });
            });

            $this->info(sprintf('Migrated %d records', $orders->count()));
        });
    }

    /**
     * @param mixed $message
     * @return mixed
     */
    protected function decodeMessage($message)
    {
        if (is_string($message) && str_contains($message, '{') && str_contains($message, '}')) {
            return $this->decodeMessage(json_decode($message, true));
        }

        return $message;
    }

    /**
     * @param mixed $error
     * @param mixed $response
     * @return mixed
     */
    protected function guessError($error, $response): array
    {
        if (!empty($error['ErrorMessage'])) {
            return [$error['ErrorMessage'], $error['ErrorCode'] ?? null];
        }

        return [$response['ErrorMessage'] ?? null, $response['ErrorCode'] ?? null];
    }
}
