<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 22.6.2017 г.
 * Time: 16:39 ч.
 */

namespace App\Integration\OmniShip\Internal;

use App\Exceptions\Error;
use App\Helper\Format;
use App\Helper\OmniShip\Address;
use App\Models\Gateway\Currency;
use App\Models\Order\Order;
use App\Models\Order\OrderFulfillment;
use App\Models\Router\Logs;
use App\Models\Shipping\ShippingMeta;
use App\Models\Shipping\ShippingProvider;
use App\Models\Store\Shop;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\Apps\Shippings\Omniship\AbstractManager;
use Omniship\Address\Country;
use Omniship\Common\ItemBag;
use Omniship\Common\ShippingQuote;
use Omniship\Common\ShippingQuoteBag;
use Omniship\Dhl\Gateway;
use Omniship\Dhl\Helper\Data;
use Omniship\Omniship;

class Manager extends AbstractManager
{
    /**
     * @var Gateway
     */
    protected $manager;

    /**
     * @var int
     */
    protected $total_shops;

    /**
     * Manager constructor.
     * @param $provider
     * @throws Error
     */
    public function __construct(ShippingProvider $provider)
    {
        $this->provider = $provider;
        $this->provider_id = $provider->id;
        $this->_app_key = $provider->omniship_key;
        $this->manager = Omniship::create(\App\Integration\OmniShip\Internal\Gateway::class)->initialize([
            'provider' => $provider
        ]);
        parent::__construct();
    }

    /**
     * @inheritdoc
     */
    public function getDescription(): ?string
    {
        return $this->provider->description;
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function getImage($thumb_size = null): string
    {
        return $this->provider->getImage($thumb_size);
    }

    /**
     * {@inheritdoc}
     */
    public function isActive($mapping = null): bool
    {
        if ($mapping) {
            return parent::isActive($mapping);
        }

        return true;
    }

    /**
     * @param array $with
     *
     * @return ShippingProvider
     * @throws Error
     */
    #[\Override]
    public function getProvider($with = ['regions'])
    {
        return $this->provider;
    }

    /**
     * @return string
     * @throws Error
     */
    #[\Override]
    public function getOmnishipKey()
    {
        return $this->provider->omniship_key;
    }

    /**
     * {@inheritdoc}
     */
    public function isActiveSession($refreshSession = true): bool
    {
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function getQuotesRequest(array $parameters): ?\Omniship\Message\AbstractRequest
    {
        return null;
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function getQuotes(array $parameters, ?\stdClass $request_response = null, array $categories = []): ?\Omniship\Common\ShippingQuoteBag
    {
        if (empty($parameters['other_provider']) && ShippingMeta::where(['parameter' => 'use_other_provider_'.$this->getProvider()->id, 'value' => 1])->exists()) {
            return null;
        }

        if ($this->getProvider([])->type == 'marketplace') {
            $quote = new ShippingQuote([
                'id' => $this->provider->id,
                'name' => $this->getName(),
                'description' => $this->getDescription(),
                'price' => Format::moneyInput(0),
                'insurance' => null,
                'currency' => site('currency'),
                'exchange_rate' => 1,
                'allowance_cash_on_delivery' => true,
                'allowance_insurance' => !is_null($this->provider->insurance),
                'type' => 'internal',
            ]);
            if (is_null($quote->getPrice())) {
                $quote->setPrice(0.00);
            }

            return new ShippingQuoteBag([$quote]);
        }

        if (empty($parameters['sender_address']) || !($parameters['sender_address'] instanceof Address)) {
            $parameters['sender_address'] = $this->getSenderAddress(empty($parameters['sender_address']) ? null : $parameters['sender_address']);
        }

        if (empty($parameters['items']) || !($parameters['items'] instanceof ItemBag) || !$parameters['items']->count()) {
            throw new Error(__('omniship.err.required.items'));
        }

        if (empty($parameters['weight'])) {
            $parameters['weight'] = $this->getWeightFromItems($parameters['items'], $this->getDefaultWeight());
        }

        if (empty($parameters['sub_total'])) {
            $parameters['sub_total'] = round($parameters['items']->sum(fn (\Omniship\Common\Item $a) => $a->getPrice() * $a->getQuantity()));
        }

        $this->provider = $this->provider->getRate(
            Format::toIntegerPrice($parameters['sub_total']),
            Format::toIntegerWeight($parameters['weight']),
            $categories
        );

        if (!$this->provider->rate) {
            return null;
        }

        $insurance = null;
        $amount = is_null($this->provider->rate->amount) ? 0 : $this->provider->rate->amount;
        if (!empty($parameters['sub_total']) && !is_null($this->provider->insurance)) {
            $insurance = Format::toIntegerPrice(round($parameters['sub_total'] * $this->provider->insurance / 10000, 2));
            $amount += $insurance;
        }

        $quote = new ShippingQuote([
            'id' => $this->provider->rate->id,
            'name' => $this->getName(),
            'description' => $this->getDescription(),
            'price' => (float)Format::moneyInput($amount),
            'insurance' => (float)Format::moneyInput($insurance),
            'currency' => site('currency'),
            'exchange_rate' => 1,
            'allowance_cash_on_delivery' => true,
            'allowance_insurance' => !is_null($this->provider->insurance),
            'type' => 'internal',
        ]);

        return new ShippingQuoteBag([$quote]);
    }

    /**
     * {@inheritdoc}
     */
    public function createBillOfLadingRequest(array $parameters): null
    {
        return null;
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function createBillOfLading(array $parameters, ?\stdClass &$request_response = null)
    {
        if (empty($parameters['sender_address']) || !($parameters['sender_address'] instanceof Address)) {
            $parameters['sender_address'] = $this->getSenderAddress(empty($parameters['sender_address']) ? null : $parameters['sender_address']);
        }

        return parent::createBillOfLading($parameters, $request_response);
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function getSenderAddress($id = null)
    {
        $address = null;
        if ($id) {
            $address = parent::getSenderAddress($id);
        } elseif (!is_null($default = parent::getSenderAddress($this->getSetting('default_address_id')))) {
            $address = $default;
        }

        return $address ?: [];
    }

    /**
     * {@inheritdoc}
     */
    public function findCountries($name)
    {
        $result = $this->cache(__FUNCTION__ . '.' . $name, function () use ($name) {
            $countries = \Omniship\Helper\Data::countries();
            return Data::countries()->filter(fn ($country): bool => Str::contains($country['Country'], $name) || Str::lower($country['CountryCode']) == Str::lower($name) || ($countries->has($country['CountryCode']) && !empty($countries->get($country['CountryCode'])['Iso3']) && Str::lower($countries->get($country['CountryCode'])['Iso3']) == Str::lower($name)))->map(fn ($country): \Omniship\Address\Country => new Country([
                'id' => $country['CountryCode'],
                'name' => $country['Country'],
                'iso2' => $country['CountryCode'],
                'iso3' => $countries->has($country['CountryCode']) && !empty($countries->get($country['CountryCode'])['Iso3']) ? $countries->get($country['CountryCode'])['Iso3'] : null,
            ]));
        });
        return $this->cacheForget(__FUNCTION__ . '.' . $name, $result);
    }

    /**
     * @param $name
     * @return Country|null
     * @throws Error
     */
    public function getCountryByIso2($name)
    {
        $result = $this->cache(__FUNCTION__ . '.' . $name, function () use ($name) {
            $countries = \Omniship\Helper\Data::countries();
            return Data::countries()->filter(fn ($country): bool => Str::lower($country['CountryCode']) == Str::lower($name))->map(fn ($country): \Omniship\Address\Country => new Country([
                'id' => $country['CountryCode'],
                'name' => $country['Country'],
                'iso2' => $country['CountryCode'],
                'iso3' => $countries->has($country['CountryCode']) && !empty($countries->get($country['CountryCode'])['Iso3']) ? $countries->get($country['CountryCode'])['Iso3'] : null,
            ]))->first();
        });
        return $this->cacheForget(__FUNCTION__ . '.' . $name, $result);
    }

    /**
     * @param $name
     * @return Country|null
     * @throws Error
     */
    public function getCountryByIso3($name)
    {
        $result = $this->cache(__FUNCTION__ . '.' . $name, function () use ($name) {
            $countries = \Omniship\Helper\Data::countries();
            return Data::countries()->filter(fn ($country): bool => $countries->has($country['CountryCode']) && !empty($countries->get($country['CountryCode'])['Iso3']) && Str::lower($countries->get($country['CountryCode'])['Iso3']) == Str::lower($name))->map(fn ($country): \Omniship\Address\Country => new Country([
                'id' => $country['CountryCode'],
                'name' => $country['Country'],
                'iso2' => $country['CountryCode'],
                'iso3' => $countries->has($country['CountryCode']) && !empty($countries->get($country['CountryCode'])['Iso3']) ? $countries->get($country['CountryCode'])['Iso3'] : null,
            ]))->first();
        });
        return $this->cacheForget(__FUNCTION__ . '.' . $name, $result);
    }

    /**
     * @param $name
     * @return Country|null
     * @throws Error
     */
    public function findCountryByName($name)
    {
        $result = $this->cache(__FUNCTION__ . '.' . $name, function () use ($name) {
            $countries = \Omniship\Helper\Data::countries();
            return Data::countries()->filter(fn ($country) => Str::contains($country['Country'], $name))->map(fn ($country): \Omniship\Address\Country => new Country([
                'id' => $country['CountryCode'],
                'name' => $country['Country'],
                'iso2' => $country['CountryCode'],
                'iso3' => $countries->has($country['CountryCode']) && !empty($countries->get($country['CountryCode'])['Iso3']) ? $countries->get($country['CountryCode'])['Iso3'] : null,
            ]))->first();
        });
        return $this->cacheForget(__FUNCTION__ . '.' . $name, $result);
    }

    /**
     * {@inheritdoc}
     */
    public function getCountryById($country_id)
    {
        return $this->getCountryByIso2($country_id);
    }

    /**
     * {@inheritdoc}
     */
    public function findStates($name, $country_id): \Illuminate\Support\Collection
    {
        return new Collection();
    }

    /**
     * {@inheritdoc}
     */
    public function findCities($name, $country_id, $state = null): \Illuminate\Support\Collection
    {
        return new Collection();
    }

    /**
     * {@inheritdoc}
     */
    public function findCityByName($name, $country_id): null
    {
        return null;
        //        return new City([
        //            'name' => $name,
        //        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function findCityByPostCode($post_code, $name, $country_id): null
    {
        return null;
        //        return new City([
        //            'name' => $name,
        //        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function getCityById($city_id): null
    {
        return null;
        //        return new City([
        //            'name' => $name,
        //        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function findStreets($name, $city_id): \Illuminate\Support\Collection
    {
        return new Collection();
    }

    /**
     * {@inheritdoc}
     */
    public function findQuarters($name, $city_id): \Illuminate\Support\Collection
    {
        return new Collection();
    }

    /**
     * {@inheritdoc}
     */
    public function findOffices($name, $city_id = null): \Illuminate\Support\Collection
    {
        return new Collection();
    }

    /**
     * {@inheritdoc}
     */
    public function officesAutocomplete($name): \Illuminate\Support\Collection
    {
        return new Collection();
    }

    /**
     * {@inheritdoc}
     */
    public function getOfficeById($office_id): null
    {
        return null;
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function orderWaybillUrl(Order $order): ?string
    {
        return route('admin.internal.print_waybill', [$order->id]);
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function orderWaybillUrlAttr(Order $order): ?string
    {
        return null;
    }

    /**
     * {@inheritdoc}
     */
    public function getSupportType(): array
    {
        if ($this->getProvider([])->type == static::SUPPORT_MARKETPLACE) {
            if ($this->getTotalShops()) {
                return [
                    static::SUPPORT_MARKETPLACE
                ];
            }

            return [];
        } else {
            return [
                static::SUPPORT_ADDRESS
            ];
        }
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function isExternal(): bool
    {
        return false;
    }

    /**
     * Supports Cash On Marketplace
     *
     * @return boolean
     */
    #[\Override]
    public function supportsPayOnPlace(): bool
    {
        return $this->getProvider()->type == AbstractManager::SUPPORT_MARKETPLACE;
    }

    /**
     * Supports Cash On Delivery
     *
     * @return boolean True if this gateway supports the Cash On Delivery
     */
    #[\Override]
    public function supportsCashOnDelivery()
    {
        if ($this->getProvider()->type == AbstractManager::SUPPORT_MARKETPLACE) {
            return false;
        } else {
            return $this->compareMaxCodPrice();
        }
    }

    /**
     * @return bool
     */
    protected function compareMaxCodPrice()
    {
        $sub_total = $this->getOrder() ? $this->getOrder()->getSubTotal('input') : 0;
        if (!$sub_total) {
            return true;
        }

        if (site('currency') == 'BGN') {
            $max_cod = static::BG_MAX_COD;
        } else {
            $max_cod = static::UNLIMITED_MAX_COD;
        }

        $sub_total = Currency::convert($sub_total, site('currency'), 'BGN');
        $cod_allow_price = $this->getSetting('cd_max') > 0 ? min($this->getSetting('cd_max'), $max_cod) : $max_cod;
        return $sub_total <= $cod_allow_price;
    }

    /**
     * @return int
     */
    protected function getTotalShops()
    {
        if (is_null($this->total_shops)) {
            $this->total_shops = Shop::whereHas('address')
                ->whereIn('id', $this->getProvider()->marketplaces)->active()->count();
        }

        return $this->total_shops;
    }

    /**
     * @param Order $order
     * @param $data
     * @return Response|void
     * @throws \Throwable
     */
    public function generateOrder(Order $order, array $data = [])
    {
        try {
            $data['order_id'] = $order->id;
            $data['products_ids'] = $order->products->pluck('id')->toArray();
            $data['email_notification'] = 'yes';
            \Illuminate\Support\Facades\DB::transaction(fn (): \App\Models\Order\OrderFulfillment => OrderFulfillment::add($data));
        } catch (\Exception $exception) {
            if (!($exception instanceof Error)) {
                Logs::createFromThrowable($exception, 'Shipping waybill error', null, [
                    'data' => request()->all(),
                ]);
            }

            return new Response([
                'status' => 'error',
                'field' => $exception instanceof Error ? $exception->getField() : null,
                'msg' => $exception->getMessage()
            ]);
        }
    }


    /**
     * @param mixed $name
     * @param mixed $city_id
     * @return mixed
     */
    public function findLockers($name, $city_id = null): void
    {
        // TODO: Implement findLockers() method.
    }

    /**
     * @param mixed $office_id
     * @return mixed
     */
    public function getLockerById($office_id): void
    {
        // TODO: Implement getLockerById() method.
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    public function lockersAutocomplete($name): void
    {
        // TODO: Implement lockersAutocomplete() method.
    }

    /**
     * @param mixed $isMachine
     * @return mixed
     */
    public function allOffices($isMachine): Collection
    {
        return collect();
    }

}
