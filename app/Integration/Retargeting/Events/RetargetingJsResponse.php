<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.6.2019 г.
 * Time: 16:01 ч.
 */

namespace App\Integration\Retargeting\Events;

use Cookie;
use App\Helper\Format;
use App\Integration\GDPR\Models\PolicyAcceptanceLog;
use App\Integration\Retargeting\JsEvents;
use App\Integration\Retargeting\RetargetingManager;
use App\Models\Customer\Customer;
use App\Models\Discount\Discount;
use App\Models\Order\OrderProduct;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\System\AppsManager;
use App\Traits\CheckoutReturn;
use Illuminate\Support\Collection;
use Modules\JsResponse\AbstractJsResponse;
use Modules\JsResponse\Formatters\JsRow;
use Modules\JsResponse\Manager;
use App\Models\Store\Cart AS CartModel;

class RetargetingJsResponse extends AbstractJsResponse
{

    use CheckoutReturn;

    /**
     * @var RetargetingManager $_manager
     */
    protected $_manager;

    /**
     * @var JsEvents $_events
     */
    protected $_events;

    /**
     * {@inheritdoc}
     * @throws \App\Exceptions\Error
     * @throws \Throwable
     */
    public function getJsData(): ?array
    {
        if (!$this->getManager()->isInstalled() || !$this->getManager()->getSetting('tracking_api')) {
            return null;
        }

        $this->getEventBuilder()->setCartUrl(['url' => route('checkout')]);

        if (activeRoute('category.view') && ($category = $this->_getCategoryDetails())) {
            $this->getEventBuilder()->sendCategory([
                "id" => $category->id,
                "name" => $category->name,
                "parent" => $category->parent_id ?: false,
                "url" => $category->url,
                "breadcrumb" => $category->path->where('id', '!=', $category->id)->map(function (Category $breadcrumb) {
                    return [
                        "id" => $breadcrumb->id,
                        "name" => $breadcrumb->name,
                        "parent" => $breadcrumb->parent_id ?: false,
                    ];
                })->all()
            ]);
        } elseif (activeRoute('site.vendor.view') && ($vendor = $this->_getVendorDetails())) {
            $this->getEventBuilder()->sendBrand([
                "id" => $vendor->id,
                "name" => $vendor->name,
            ]);
        } elseif (activeRoute('product.view') && ($product = $this->getProduct())) {
            $data = [
                "id" => $product->id,
                "name" => $product->name,
                "url" => $product->url(),
                "img" => $product->getImage('1920x1920'),
                "brand" => false,
                'category' => [],
                'inventory' => [
                    'variations' => !!$product->total_variants,
                    'stock' => $product->enable_sell
                ]
            ];

            if(showPriceForUser()) {
                $data = array_merge($data, [
                    "price" => (float)$product->price_from_input,
                    "promo" => (float)($product->getAttribute('price_from_discounted_input') ? $product->getAttribute('price_from_discounted_input') : 0),
                ]);
            }

            if ($product->vendor_id && $product->vendor) {
                $data['brand'] = [
                    'id' => $product->vendor->id,
                    'name' => $product->vendor->name,
                ];
            }
            if ($product->category_id && $product->category) {
                $data['category'][] = $this->formatCategory($product->category);
            }

            $this->getEventBuilder()->sendProduct($data);
            $this->getEventBuilder()->trigger('click.imagePrimary', function (JsEvents $e) use ($product) {
                $e->clickImage(['product_id' => $product->id]);
            }, [], '.image.primary');

            if ($product->total_variants > 0) {
                $this->_variantChange();
            }

        } elseif (activeRoute('cart.list cart.panel checkout') && ($checkout = $this->getCheckout())) {
            $products = $checkout->getProducts();
            $this->getEventBuilder()->checkoutIds($products->pluck('product_id')->all());
        } elseif (activeRoute('checkout.return') && routeParameter('status') != 'cancel' && $order = $this->getReturnOrder()) {
            $products = $this->getReturnOrderProducts();
            if ($products) {
                $discount_codes = $this->getReturnOrderCoupon();
                $discounts = $this->getReturnOrderDiscounts();
                /** @var Discount[]|Collection $discounts_products */
                $discounts_products = $discounts->where('target_product_id', '!=', null)->keyBy('id');
                /** @var Discount[]|Collection $discounts_without_products */
                $discounts_without_products = $discounts->where('target_product_id', null);
                $discount = 0;
                foreach ($products AS $p) {
                    if ($p->order_discount_id && $discounts_products->has($p->order_discount_id)) {
                        $d = $discounts_products->get($p->order_discount_id);
                        $discount += $d->calculateDiscountAmount($p->price);
                    }
                }
                foreach ($discounts_without_products AS $d) {
                    $discount += $d->calculate($order->price_products_subtotal);
                }

                $address = null;
                if ($shipping_address = $order->shippingAddress) {
                    $address = $shipping_address;
                } elseif ($billing_address = $order->billingAddress) {
                    $address = $billing_address;
                }
                $this->getEventBuilder()->saveOrder([
                    "order_no" => $order->id,
                    "lastname" => $order->customer_last_name,
                    "firstname" => $order->customer_first_name,
                    "email" => $order->customer_email,
                    "phone" => $address ? $address->phone : '',
                    "state" => $address ? (string)$address->state_name : '',
                    "city" => $address ? $address->city_name : '',
                    "address" => $address ? $address->street : '',
                    "discount" => (float)Format::moneyInput($discount),
                    "discount_code" => $discount_codes ? [$discount_codes] : '',
                    "shipping" => (float)Format::moneyInput($this->getReturnOrderShipping() ? $this->getReturnOrderShipping()->order_amount : 0),
                    "total" => (float)Format::moneyInput($order->price_total)
                ], $products->map(function (OrderProduct $product) {
                    $return = [
                        "id" => $product->product_id,
                        "quantity" => $product->quantity,
                        "price" => (float)Format::moneyInput($product->order_price)
                    ];
                    if ($product->v1) {
                        $return['variation_code'] = ($product->v1 ? ($product->v1 . ($product->v2 ? ('-' . $product->v2 . ($product->v3 ? ('-' . $product->v3) : '')) : '')) : '');
                    } else {
                        $return['variation_code'] = '';
                    }
                    return $return;
                })->all());
            }
        } elseif (activeRoute('page')) {
            $this->getEventBuilder()->visitHelpPage();
        }

        $this->_addAndRemoveCartEvent();
        $this->_addToWishlistEvent();

        return [
            (new JsRow())->setRaw($this->getEventBuilder()->headClear())->setPosition(Manager::POSITION_TOP),
            (new JsRow())->setRaw($this->getEventBuilder()->renderClear()),
        ];
    }

    /**
     * @param Product $product
     * @return array
     * @throws \App\Exceptions\Error
     */
    protected function formatProduct(Product $product)
    {
        $data = [
            "id" => $product->id,
            "name" => $product->name,
            "url" => $product->url(),
            "img" => $product->getImage('1920x1920'),
            "price" => (float)$product->price_from_input,
            "promo" => (float)($product->getAttribute('price_from_discounted_input') ? $product->getAttribute('price_from_discounted_input') : 0),
            "brand" => false,
            'category' => [],
            'inventory' => [
                'variations' => !!$product->total_variants,
                'stock' => $product->enable_sell
            ]
        ];
        if ($product->vendor_id && $product->vendor) {
            $data['brand'] = [
                'id' => $product->vendor->id,
                'name' => $product->vendor->name,
            ];
        }
        if ($product->category_id && $product->category) {
            $data['category'][] = $this->formatCategory($product->category);
        }

        if($product->categories->isNotEmpty()) {
            $data['category'] = array_merge($data['category'], $product->categories->map(function($c) {
                return $this->formatCategory($c);
            })->all());
        }

        if ($data['inventory']['variations'] && $variants = \CatalogProduct::getVariants($product)) {
            $data['inventory']['stock'] = $variants->map(function (Variant $variant) use ($product) {
//                    $stock = true;
//                    if (is_null($variant->quantity)) {
//                        $stock = true;
//                    } elseif ($product->tracking == YesNo::True && $product->continue_selling == YesNo::False) {
//
//                        $stock = is_null($variant->quantity) ? true : ($variant->quantity > 0);
//                    }
                return [
                    'key' => $variant->text_key,
//                        'quantity' => $stock,
                    'quantity' => !!$variant->enable_sell,
                ];
            })->pluck('quantity', 'key')->all();
        }

        return $data;
    }

    /**
     * @param Category $category
     * @return array
     */
    protected function formatCategory(Category $category)
    {
        $categories = $category->path->reverse();
        if($categories->isEmpty()) {
            return [];
        }
        $last = $categories->shift();

        return [
            "id" => $last->id,
            "name" => $last->name,
            "parent" => $last->parent_id ?: false,
            "breadcrumb" => $categories->map(function (Category $breadcrumb) {
                return [
                    "id" => $breadcrumb->id,
                    "name" => $breadcrumb->name,
                    "parent" => $breadcrumb->parent_id ?: false,
                ];
            })->all()
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getFiles(): ?array
    {
        return null;
    }

    /**
     * {@inheritdoc}
     * @return array|null
     * @throws \App\Exceptions\Error
     * @throws \Throwable
     */
    public function getJsDynamic(): ?array
    {
        if (!$this->getManager()->isInstalled() || !$this->getManager()->getSetting('tracking_api')) {
            return null;
        }

        if ($customer = $this->getCustomer()) {
            $this->getEventBuilder()->setEmail($customer);
        }

        if (AppsManager::isActive('gdpr') && !empty($customer)) {
            $this->getEventBuilder()->subscribeEmail([
                'email' => $customer->email,
                'status' => $this->isPolicyAcceptance($customer)
            ]);
        }

        return [
            (new JsRow())->setRaw($this->getEventBuilder()->renderClear()),
        ];

    }

    /**
     * @param Customer $customer
     * @return bool
     */
    protected function isPolicyAcceptance(Customer $customer) : bool
    {
        if(request()->cookie('policy-acceptance')) {
            return true;
        }

        $acceptance = PolicyAcceptanceLog::where('customer_id', $customer->id)
                ->orWhere('email', $customer->email)->value('id') > 0;

        if($acceptance) {
            Cookie::queue(Cookie::make('policy-acceptance', (int)$acceptance, 10));
        }

        return $acceptance;
    }

    /**
     * {@inheritdoc}
     */
    public function getAjaxJsData(): ?array
    {
        return null;
    }

    /**
     * @return RetargetingManager
     */
    protected function getManager(): RetargetingManager
    {
        if (is_null($this->_manager)) {
            $this->_manager = new RetargetingManager();
        }

        return $this->_manager;
    }

    /**
     * @return JsEvents
     * @throws \App\Exceptions\Error
     */
    protected function getEventBuilder(): JsEvents
    {
        if (is_null($this->_events)) {
            $this->_events = new JsEvents($this->getManager());
        }

        return $this->_events;
    }

    /**
     * @return Customer|\Customer|\Illuminate\Contracts\Auth\Authenticatable|null
     * @throws \Throwable
     */
    protected function getCustomer()
    {
        return \Auth::customerId() ? \Auth::customer() : $this->getCartCustomer();
    }

    /**
     * @return \App\Models\Customer\Customer|null
     * @throws \Throwable
     */
    protected function getCartCustomer()
    {
        if(!($cartInstance = $this->getCart())) {
            return null;
        }

        return $cartInstance->customer;
    }

    /**
     * @return CartModel|bool
     * @throws \Throwable
     */
    protected function getCart()
    {
        return CartModel::instance();
    }

    // protected methods

    /**
     * @throws \App\Exceptions\Error
     */
    protected function _addAndRemoveCartEvent()
    {
        $this->getEventBuilder()->trigger('cc.cart.product.addToCart', function (JsEvents $e) {
            $e->addToCart([
                'product_id' => 'js:json.product.product_id',
                'quantity' => 'js:json.quantity',
            ]);
        }, ['json']);

        $this->getEventBuilder()->trigger('cc.cart.product.removed', function (JsEvents $e) {
            $e->removeFromCart([
                'product_id' => 'js:json.product.product_id',
                'quantity' => 'js:json.quantity',
            ]);
        }, ['json']);
    }

    // protected methods

    /**
     * @throws \App\Exceptions\Error
     */
    protected function _addToWishlistEvent()
    {
        $this->getEventBuilder()->trigger('cc.wishlist.product.added', function (JsEvents $e) {
            $e->addToWishlist([
                'product_id' => 'js:json.product.product_id',
            ]);
        }, ['json']);
    }

    // protected methods

    /**
     * @throws \App\Exceptions\Error
     */
    protected function _variantChange()
    {
        $this->getEventBuilder()->trigger('cc.variant.changed', function (JsEvents $e) {
            $e->setVariation([
                'product_id' => 'js:json.item_id',
                'variation' => "js:{'code':json.text_key,'stock':json.enable_sell,'details':{}}"
            ]);
        }, ['json']);
    }

}
