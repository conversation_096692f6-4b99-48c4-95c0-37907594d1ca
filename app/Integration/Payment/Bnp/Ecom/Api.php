<?php

declare(strict_types=1);

namespace App\Integration\Payment\Bnp\Ecom;

use App\Models\Router\Certificate;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class Api
{
    public const string TOKEN_ENDPOINT_TEST = "https://api-test.pbpf.bg/postbank/pbpf-ecommerce/jwt/gen";

    public const string TOKEN_ENDPOINT_LIVE = "https://apiecom.postbank.bg/postbank/pbpf-ecommerce/jwt/gen";

    public const string REQUEST_ENDPOINT_TEST = "https://api-test.pbpf.bg/postbank/pbpf-ecommerce";

    public const string REQUEST_ENDPOINT_LIVE = "https://apiecom.postbank.bg/postbank/pbpf-ecommerce";

    protected bool $testMode = false;

    protected string $clientId;

    protected string $clientSecret;

    protected array $pfx;

    protected string $password;

    protected Client $client;

    /**
     * @param array $config
     * @throws Exception
     */
    public function __construct(array $config)
    {
        $this->testMode = $config['test_mode'];
        $this->clientId = $config['ecom_client_id'];
        $this->clientSecret = $config['ecom_client_secret'] ?? '';
        $this->password = $config['ecom_password'];
        $this->pfx = self::loadPfx(base64_decode((string)$config['ecom_certificate']), $this->password);
        $this->client = $this->initClient();
    }

    /**
     * @return Client
     */
    protected function initClient(): Client
    {
        // Create temporary files
        $certPath = tempnam(sys_get_temp_dir(), 'cert_');
        $keyPath = tempnam(sys_get_temp_dir(), 'key_');

        // Write contents to files
        file_put_contents($certPath, $this->pfx['cert']);
        file_put_contents($keyPath, $this->pfx['pkey']);

        return new Client([
            'headers' => [
                'User-Agent' => 'CloudCart PBPF ECOM API',
                'X-IBM-Client-Id' => $this->clientId,
                'X-IBM-Client-Secret' => $this->clientSecret,
            ],
            'cert' => [$certPath, ''],
            'ssl_key' => [$keyPath, ''],
            'verify' => false,
            'timeout' => 3,
        ]);
    }

    /**
     * @param string $pfx
     * @param string $password
     * @return mixed
     * @throws Exception
     */
    public static function loadPfx(string $pfx, string $password): array
    {
        $status = openssl_pkcs12_read($pfx, $certInfo, $password);

        if (!$status) {
            throw new Exception('Invalid PFX file or password');
        }

        $pub = openssl_pkey_get_public($certInfo['cert']);
        $details = openssl_pkey_get_details($pub);
        $certInfo['pub'] = $details['key'];

        return $certInfo;
    }

    /**
     * @param array $data
     * @return mixed
     * @throws GuzzleException
     * @throws Exception
     */
    public function createRequest(array $data): mixed
    {
        $access = $this->getAccessToken();

        $response = $this->client->post($this->getRequestEndpoint() . '/api/CreateRequest', [
            'headers' => [
                'Authorization' => 'Bearer ' . $access->token,
            ],
            'json' => $data
        ]);

        return $response->getBody()->getContents();
    }

    /**
     * @param array $data
     * @return mixed
     * @throws GuzzleException
     * @throws Exception
     */
    public function getRequestInfo(array $data): mixed
    {
        $access = $this->getAccessToken();

        $response = $this->client->get($this->getRequestEndpoint() . '/api/GetRequestInfo', [
            'headers' => [
                'Authorization' => 'Bearer ' . $access->token,
            ],
            'json' => $data
        ]);

        return json_decode($response->getBody()->getContents());
    }

    public function getParsedCert(): array
    {
        return Certificate::parseAcme($this->pfx['cert']);
    }

    /**
     * @throws GuzzleException
     * @throws Exception
     */
    public function getAccessToken()
    {
        $response = $this->client->post($this->getTokenEndpoint(), [
            'json' => [
                "iss-claim" => $this->testMode ? "PBPF eCommerce test" : "PBPF eCommerce",
                "aud-claim" => $this->testMode ? "eCommerce Merchants test" : "eCommerce Merchants",
            ]
        ]);

        return json_decode($response->getBody()->getContents());
    }

    protected function getTokenEndpoint(): string
    {
        return $this->testMode ? self::TOKEN_ENDPOINT_TEST : self::TOKEN_ENDPOINT_LIVE;
    }

    protected function getRequestEndpoint(): string
    {
        return $this->testMode ? self::REQUEST_ENDPOINT_TEST : self::REQUEST_ENDPOINT_LIVE;
    }

}
