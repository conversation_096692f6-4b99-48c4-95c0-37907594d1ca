<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 23.3.2017 г.
 * Time: 15:40
 */
Route::group(['prefix' => 'api/payment_providers/bnp', 'namespace' => 'Bnp\Http\Controllers\Sitecp'], function ($route): void {
    $route->get('mapping', ['as' => 'admin.bnp.mapping', 'uses' => 'BnpController@mapping']);
    $route->get('export', ['as' => 'admin.bnp.export', 'uses' => 'BnpController@export']);
    $route->get('export-promo', ['as' => 'admin.bnp.export-promo', 'uses' => 'BnpController@exportPromo']);
    $route->post('import-promo', ['as' => 'admin.bnp.import-promo', 'uses' => 'BnpController@importPromo']);
    $route->any('promotions', ['as' => 'admin.bnp.promo', 'uses' => 'BnpController@promotions']);
    $route->get('promotion/edit/{promotionId}', ['as' => 'admin.bnp.promo.edit', 'uses' => 'BnpController@promotionEdit']);
    $route->get('promotion/delete/{promotionId}', ['as' => 'admin.bnp.promo.delete', 'uses' => 'BnpController@delete']);
    $route->post('promotion/save/{promotionId}', ['as' => 'admin.bnp.promo.save', 'uses' => 'BnpController@promotionSave']);
    $route->post('promotion/html/save', ['as' => 'admin.bnp.promo-html.save', 'uses' => 'BnpController@promotionHtmlSave']);
    $route->get('pricing/{productId?}/{goodTypeId?}', ['as' => 'admin.bnp.pricing', 'uses' => 'BnpController@pricingTable']);
    $route->post('mapping/save', ['as' => 'admin.bnp.mapping.save', 'uses' => 'BnpController@saveMapping']);
});
