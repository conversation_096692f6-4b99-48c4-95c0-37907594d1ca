<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 13.3.2017 г.
 * Time: 12:57
 */

namespace App\Integration\Payment\Bnp;

use App\Contracts\ValidatorContract;

/**
 * Class ConfigurationValidator
 * @package App\Integration\Payment\Bnp
 * @method  attributes()
 */
class ConfigurationValidator implements ValidatorContract
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'configuration.email' => 'required|email',
            'configuration.code' => 'required|string|min:10|max:10',
            'configuration.min_price' => 'required',
            'configuration.merchant_id_test' => 'required_if:configuration.mode,test',
            'configuration.merchant_id_live' => 'required_if:configuration.mode,live',
            'configuration.ecom_client_id' => 'required_if:configuration.ecom_enabled,1',
            'configuration.ecom_client_secret' => 'required_if:configuration.ecom_enabled,1',
        ];
    }

    /**
     * @return array
     */
    public function messages(): array
    {
        return [
            'configuration.email.required' => "Email is required",
            'configuration.email.email' => "Email is not valid",
            'configuration.min_price.required' => "Minimum price is required",
            'configuration.merchant_id_test.required_if' => "POS ID is required",
            'configuration.merchant_id_live.required_if' => "POS ID is required",
        ];
    }

    /**
     * @param mixed $name
     * @param mixed $arguments
     * @return mixed
     */
    public function __call($name, $arguments)
    {
        // TODO: Implement @method  attributes()
    }
}
