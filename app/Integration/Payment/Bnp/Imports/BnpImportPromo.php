<?php

declare(strict_types=1);

namespace App\Integration\Payment\Bnp\Imports;

use App\Integration\Payment\Bnp\Models\BnpPromotion;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class BnpImportPromo implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection): void
    {
        $items = [];
        foreach ($collection as $row) {
            $items[] = [
                'product_id' => $row['product_id'],
                'bnp_type_id' => $row['bnp_type_id'],
//                'variants' => [],
                'start_date' => $row['start_date'],
                'end_date' => $row['end_date'],
            ];
        }

        BnpPromotion::truncate();
        foreach ($items as $item) {
            BnpPromotion::create($item);
        }
    }
}
