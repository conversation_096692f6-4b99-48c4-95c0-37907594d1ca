{include file="../../views/provider_dependency.tpl"}

<form action="{route('admin.payment_providers.save', $provider->provider)}" id="editProviderForm" role="form" class="ajaxForm" data-multipart="true">
    {include file="../../views/title_logo.tpl"}

    <div class="box">
        <div class="box-title">
            <div class="box-title-text">
                <h5>{t}global.settings{/t}</h5>
            </div>
        </div>

        <div class="box-section">
            <div class="row form-group">
                <div class="col-xs-12">
                    <div class="stack">
                        <div class="stack-main">
                            <label class="form-control-check">{t}payment_provider.label.bnp.send_email_after_checkout{/t}</label>
                        </div>

                        <div class="stack-addon">
                            <input type="checkbox" class="switch" value="1" name="configuration[send_email_after_checkout]"
                                   {if $provider.configuration.send_email_after_checkout|default}checked{/if}>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="box-section">
            <div class="row form-group">
                <div class="col-xs-12">
                    <label class="control-label">{t}payment_provider.label.tbi.email{/t}</label>

                    <input type="email" name="configuration[email]" class="form-control"
                           data-autofocus="" placeholder="{t}payment_provider.ph.tbi.email{/t}"
                           value="{$provider.configuration.email|default:$meta['email']|default}" autocomplete="off"/>

                    <span class="help-block">{t}payment_provider.help.tbi.email{/t}</span>
                </div>
            </div>

            <div class="row form-group">
                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.tbi.id{/t}</label>

                    <input type="text" name="configuration[id]" class="form-control"
                           placeholder="{t}payment_provider.ph.tbi.id{/t}"
                           value="{$provider.configuration.id|default}" autocomplete="off"/>

                    <span class="help-block">{t}payment_provider.help.tbi.id{/t}</span>
                </div>

                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.tbi.start_price{/t}</label>

                    <input type="text" name="configuration[min_price]" class="form-control"
                           placeholder="{t}payment_provider.label.tbi.start_price{/t}"
                           value="{$provider.configuration.min_price|default:$meta.min_price}" autocomplete="off"/>

                    <span class="help-block">{t}payment_provider.help.tbi.start_price{/t}</span>
                </div>
            </div>

            <div class="row form-group">
                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.tbi.min{/t}</label>

                    <input type="number" name="configuration[min]" class="form-control"
                           placeholder="{t}payment_provider.ph.tbi.min{/t}"
                           value="{$provider.configuration.min|default:$meta.step.min}" autocomplete="off"/>

                    <span class="help-block">{t}payment_provider.help.tbi.min{/t}</span>
                </div>

                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.tbi.max{/t}</label>

                    <input type="number" name="configuration[max]" class="form-control"
                           placeholder="{t}payment_provider.ph.tbi.max{/t}"
                           value="{$provider.configuration.max|default:$meta.step.max}" autocomplete="off"/>

                    <span class="help-block">{t}payment_provider.help.tbi.max{/t}</span>
                </div>
            </div>

            <div class="row form-group">
                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.tbi.step{/t}</label>

                    <input type="number" step="{$meta.step.min|default}" min="{$meta.step.size|default}" max="{$meta.step.max|default}" name="configuration[step]" class="form-control"
                           placeholder="{t}payment_provider.ph.tbi.step{/t}"
                           value="{$provider.configuration.step|default:$meta.step.size}" autocomplete="off"/>

                    <span class="help-block">{t}payment_provider.help.tbi.step{/t}</span>
                </div>

                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.percent_per_month{/t}</label>

                    <input type="text" name="configuration[percentPerMonth]" class="form-control"
                           placeholder="{t}payment_provider.help.percent_per_month{/t}"
                           value="{$provider.configuration.percentPerMonth|default:$meta.percentPerMonth}" autocomplete="off"/>

                    <span class="help-block">{t}payment_provider.help.percent_per_month{/t}</span>
                </div>
            </div>

            <div class="row form-group">
                <div class="col-xs-12">
                    <label class="control-label">{t}payment_provider.label.tbi.description{/t}</label>

                    <div class="skip-has-error">
                        <textarea name="configuration[description]" id="tinymce-{uniqid()}" class="tinymce"
                                  data-height="256" data-tool-table="true" data-tool-image="true" data-tool-media="true"
                                  placeholder="{t}payment_provider.ph.tbi.description{/t}">{$provider.configuration.description|default}</textarea>
                    </div>
                </div>
            </div>

            <div class="row form-group">
                <div class="col-xs-12">
                    <label class="control-label">{t}payment_provider.label.tbi.text{/t}</label>
                </div>
            </div>
        </div>
    </div>



    <div class="box">
        <div class="box-title">
            <div class="box-title-text">
                <h5>{t}payment_provider.label.free_leasing{/t}</h5>
            </div>
        </div>


        <div class="box-section">

            <div class="row form-group">
                <div class="col-xs-12">
                    <label class="control-label">{t}payment_provider.label.free_leasing_title{/t}</label>
                    <input type="text" name="configuration[free_leasing_title]" class="form-control"
                           placeholder="{t}payment_provider.label.free_leasing_title{/t}"
                           value="{$provider.configuration.free_leasing_title|default}" autocomplete="off"/>

                    <span class="help-block">{t}payment_provider.label.free_leasing_title.help{/t}</span>
                </div>
            </div>
            <div class="row form-group">
                <div class="col-xs-12">
                    <label class="control-label">{t}payment_provider.label.free_leasing{/t}</label>
                    <input type="text" name="configuration[free_leasing]" class="form-control"
                           placeholder="{t}payment_provider.ph.free_leasing{/t}"
                           value="{$provider.configuration.free_leasing|default}" autocomplete="off"/>

                    <span class="help-block">{t}payment_provider.help.free_leasing{/t}</span>
                </div>
            </div>
        </div>
    </div>


</form>
