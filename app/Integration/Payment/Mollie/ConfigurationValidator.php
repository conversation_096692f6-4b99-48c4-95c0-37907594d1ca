<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 13.3.2017 г.
 * Time: 12:57
 */

namespace App\Integration\Payment\Mollie;

use App\Contracts\ValidatorContract;

class ConfigurationValidator implements ValidatorContract
{
    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'configuration.test_api_key' => 'required',
            'configuration.live_api_key' => 'required',
            'configuration.profile_id' => 'required',
        ];
    }

    /**
     * @return array
     */
    public function messages(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function attributes(): array
    {
        return [
            'configuration.test_api_key' => "Test API key is required",
            'configuration.live_api_key' => "Live API key is required",
            'configuration.profile_id' => "Profile id is required",
        ];
    }
}
