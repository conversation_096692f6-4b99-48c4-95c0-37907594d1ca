<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 8.3.2017 г.
 * Time: 14:24
 */

namespace App\Integration\Payment;

/**
 * Class CreditCalculator
 *
 * @package App\Integration\Payment
 */
class CreditCalculatorOC
{
    public const FINANCIAL_MAX_ITERATIONS = 128;

    public const FINANCIAL_PRECISION = 1.0e-08;

    public const COMPATIBILITY_EXCEL = 'Excel';

    public const COMPATIBILITY_GNUMERIC = 'Gnumeric';

    public const RETURNDATE_EXCEL = 'E';

    public const CALENDAR_WINDOWS_1900 = 1900;

    /**
     * XIRR
     *
     * Returns the internal rate of return for a schedule of cash flows
     * that is not necessarily periodic. To calculate the internal rate
     * of return for a series of periodic cash flows, use the IRR function.
     *
     * Adapted from routine in Numerical Recipes in C, and translated
     * from the Bernt A Oedegaard algorithm in C
     *
     * @param       $values
     * @param       $dates
     * @param float $guess
     *
     * @return float|mixed|string
     */
    public static function XIRR($values, $dates, $guess = 0.1): string|float
    {
        if ((!is_array($values)) && (!is_array($dates))) {
            return 'values';
        }

        $values = static::flattenArray($values);
        $dates = static::flattenArray($dates);
        $guess = static::flattenSingleValue($guess);
        if (count($values) != count($dates)) {
            return 'Not equal dates and values number';
        }

        // create an initial range, with a root somewhere between 0 and guess
        $x1 = 0.0;
        $x2 = $guess;
        $f1 = static::XNPV($x1, $values, $dates);
        $f2 = static::XNPV($x2, $values, $dates);
        for ($i = 0; $i < static::FINANCIAL_MAX_ITERATIONS; ++$i) {
            if (($f1 * $f2) < 0.0) {
                break;
            }

            if (abs($f1) < abs($f2)) {
                $f1 = static::XNPV($x1 += 1.6 * ($x1 - $x2), $values, $dates);
            } else {
                $f2 = static::XNPV($x2 += 1.6 * ($x2 - $x1), $values, $dates);
            }
        }

        if (($f1 * $f2) > 0.0) {
            return 'values';
        }

        $f = static::XNPV($x1, $values, $dates);
        if ($f < 0.0) {
            $rtb = $x1;
            $dx = $x2 - $x1;
        } else {
            $rtb = $x2;
            $dx = $x1 - $x2;
        }

        for ($i = 0; $i < static::FINANCIAL_MAX_ITERATIONS; ++$i) {
            $dx *= 0.5;
            $x_mid = $rtb + $dx;
            $f_mid = static::XNPV($x_mid, $values, $dates);
            if ($f_mid <= 0.0) {
                $rtb = $x_mid;
            }

            if ((abs($f_mid) < static::FINANCIAL_PRECISION) || (abs($dx) < static::FINANCIAL_PRECISION)) {
                return $x_mid;
            }
        }

        return 'values';
    }

    /**
     * XNPV
     *
     * Returns the net present value for a schedule of cash flows that
     * is not necessarily periodic. To calculate the net present value
     * for a series of cash flows that is periodic, use the NPV function.
     *
     *        n   /                values(i)               \
     * NPV = SUM | ---------------------------------------- |
     *       i=1 |           ((dates(i) - dates(1)) / 365)  |
     *            \ (1 + rate)                             /
     *
     * @param    float $rate The discount rate to apply to the cash flows.
     * @param          array of float    $values        A series of cash flows that corresponds to a schedule of payments in dates. The first payment is optional and corresponds to a cost or payment that occurs at the beginning of the investment. If the first value is a cost or payment, it must be a negative value. All succeeding payments are discounted based on a 365-day year. The series of values must contain at least one positive value and one negative value.
     * @param          array of mixed    $dates        A schedule of payment dates that corresponds to the cash flow payments. The first payment date indicates the beginning of the schedule of payments. All other dates must be later than this date, but they may occur in any order.
     *
     * @return    float
     */
    public static function XNPV($rate, $values, $dates): string|float
    {
        $rate = static::flattenSingleValue($rate);
        if (!is_numeric($rate)) {
            return 'values';
        }

        if ((!is_array($values)) || (!is_array($dates))) {
            return 'values';
        }

        $values = static::flattenArray($values);
        $dates = static::flattenArray($dates);
        $valCount = count($values);
        if ($valCount != count($dates)) {
            return 'Not equal dates and values number';
        }

        if ((min($values) > 0) || (max($values) < 0)) {
            return 'values';
        }

        $xnpv = 0.0;
        for ($i = 0; $i < $valCount; ++$i) {
            if (!is_numeric($values[$i])) {
                return 'values';
            }

            $xnpv += $values[$i] / (1 + $rate) ** (static::DATEDIF($dates[0], $dates[$i], 'd') / 365);
        }

        return (is_finite($xnpv)) ? $xnpv : 'values';
    }

    /**
     * Convert an array to a single scalar value by extracting the first element
     *
     * @param    mixed $value Array or scalar value
     *
     * @return    mixed
     */
    public static function flattenSingleValue($value = '')
    {
        while (is_array($value)) {
            $value = array_pop($value);
        }

        return $value;
    }

    /**
     * Convert a multi-dimensional array to a simple 1-dimensional array
     *
     * @param    array $array Array to be flattened
     *
     * @return    array    Flattened array
     */
    public static function flattenArray($array): array
    {
        if (!is_array($array)) {
            return (array)$array;
        }

        $arrayValues = [];
        foreach ($array as $value) {
            if (is_array($value)) {
                foreach ($value as $val) {
                    if (is_array($val)) {
                        foreach ($val as $v) {
                            $arrayValues[] = $v;
                        }
                    } else {
                        $arrayValues[] = $val;
                    }
                }
            } else {
                $arrayValues[] = $value;
            }
        }

        return $arrayValues;
    }

    /**
     * DATEDIF
     *
     * @param    mixed  $startDate        Excel date serial value, PHP date/time stamp, PHP DateTime object
     *                                    or a standard date string
     * @param    mixed  $endDate          Excel date serial value, PHP date/time stamp, PHP DateTime object
     *                                    or a standard date string
     * @param    string $unit
     *
     * @return    integer    Interval between the dates
     */
    public static function DATEDIF($startDate = 0, $endDate = 0, $unit = 'D')
    {
        $startDate = static::flattenSingleValue($startDate);
        $endDate = static::flattenSingleValue($endDate);
        $unit = strtoupper((string) static::flattenSingleValue($unit));

        if (is_string($startDate = static::_getDateValue($startDate))) {
            return 'values';
        }

        if (is_string($endDate = static::_getDateValue($endDate))) {
            return 'values';
        }

        // Validate parameters
        if ($startDate >= $endDate) {
            return 'None';
        }

        // Execute function
        $difference = $endDate - $startDate;

        $PHPStartDateObject = static::ExcelToPHPObject($startDate);
        $startDays = $PHPStartDateObject->format('j');
        $startMonths = $PHPStartDateObject->format('n');
        $startYears = $PHPStartDateObject->format('Y');

        $PHPEndDateObject = static::ExcelToPHPObject($endDate);
        $endDays = $PHPEndDateObject->format('j');
        $endMonths = $PHPEndDateObject->format('n');
        $endYears = $PHPEndDateObject->format('Y');

        switch ($unit) {
            case 'D':
                $retVal = intval($difference);
                break;
            case 'M':
                $retVal = intval($endMonths - $startMonths) + (intval($endYears - $startYears) * 12);
                //	We're only interested in full months
                if ($endDays < $startDays) {
                    --$retVal;
                }

                break;
            case 'Y':
                $retVal = intval($endYears - $startYears);
                //	We're only interested in full months
                if ($endMonths < $startMonths) {
                    --$retVal;
                } elseif (($endMonths == $startMonths) && ($endDays < $startDays)) {
                    --$retVal;
                }

                break;
            case 'MD':
                if ($endDays < $startDays) {
                    $retVal = $endDays;
                    $PHPEndDateObject->modify('-' . $endDays . ' days');
                    $adjustDays = $PHPEndDateObject->format('j');
                    if ($adjustDays > $startDays) {
                        $retVal += ($adjustDays - $startDays);
                    }
                } else {
                    $retVal = $endDays - $startDays;
                }

                break;
            case 'YM':
                $retVal = intval($endMonths - $startMonths);
                if ($retVal < 0) {
                    $retVal = 12 + $retVal;
                }

                //	We're only interested in full months
                if ($endDays < $startDays) {
                    --$retVal;
                }

                break;
            case 'YD':
                $retVal = intval($difference);
                if ($endYears > $startYears) {
                    while ($endYears > $startYears) {
                        $PHPEndDateObject->modify('-1 year');
                        $endYears = $PHPEndDateObject->format('Y');
                    }

                    $retVal = $PHPEndDateObject->format('z') - $PHPStartDateObject->format('z');
                    if ($retVal < 0) {
                        $retVal += 365;
                    }
                }

                break;
            default:
                $retVal = 'None';
        }

        return $retVal;
    }

    /**
     * _getDateValue
     *
     * @param    string $dateValue
     *
     * @return    mixed    Excel date/time serial value, or string if error
     */
    private static function _getDateValue($dateValue): int|string|float
    {
        if (!is_numeric($dateValue)) {
            if ((is_string($dateValue)) && (static::COMPATIBILITY_EXCEL == static::COMPATIBILITY_GNUMERIC)) {
                return 'values';
            }

            if ((is_object($dateValue)) && ($dateValue instanceof \DateTime)) {
                $dateValue = static::PHPToExcel($dateValue);
            } else {
                $dateValue = static::DATEVALUE($dateValue);
            }
        }

        return $dateValue;
    }

    /**
     * DATEVALUE
     *
     * Returns a value that represents a particular date.
     * Use DATEVALUE to convert a date represented by a text string to an Excel or PHP date/time stamp
     * value.
     *
     * NOTE: When used in a Cell Formula, MS Excel changes the cell format so that it matches the date
     * format of your regional settings. PHPExcel does not change cell formatting in this way.
     *
     * Excel Function:
     *        DATEVALUE(dateValue)
     *
     * @access    public
     * @category  Date/Time Functions
     *
     * @param    string $dateValue        Text that represents a date in a Microsoft Excel date format.
     *                                    For example, "1/30/2008" or "30-Jan-2008" are text strings within
     *                                    quotation marks that represent dates. Using the default date
     *                                    system in Excel for Windows, date_text must represent a date from
     *                                    January 1, 1900, to December 31, 9999. Using the default date
     *                                    system in Excel for the Macintosh, date_text must represent a date
     *                                    from January 1, 1904, to December 31, 9999. DATEVALUE returns the
     *                                    #VALUE! error value if date_text is out of this range.
     *
     * @return    mixed    Excel date/time serial value, PHP date/time serial value or PHP date/time object,
     *                        depending on the value of the ReturnDateType flag
     */
    public static function DATEVALUE($dateValue = 1): string|float
    {
        $dateValue = trim((string) static::flattenSingleValue($dateValue), '"');
        //	Strip any ordinals because they're allowed in Excel (English only)
        $dateValue = preg_replace('/(\d)(st|nd|rd|th)([ -\/])/Ui', '$1$3', $dateValue);
        //	Convert separators (/ . or space) to hyphens (should also handle dot used for ordinals in some countries, e.g. Denmark, Germany)
        $dateValue = str_replace(['/', '.', '-', '  '], [' ', ' ', ' ', ' '], $dateValue);

        $yearFound = false;
        $t1 = explode(' ', $dateValue);
        foreach ($t1 as &$t) {
            if ((is_numeric($t)) && ($t > 31)) {
                if ($yearFound) {
                    return 'values';
                } else {
                    if ($t < 100) {
                        $t += 1900;
                    }

                    $yearFound = true;
                }
            }
        }

        if ((count($t1) == 1) && (str_contains($t, ':'))) {
            //	We've been fed a time value without any date
            return 0.0;
        } elseif (count($t1) == 2) {
            //	We only have two parts of the date: either day/month or month/year
            if ($yearFound) {
                array_unshift($t1, 1);
            } else {
                array_push($t1, \Carbon\Carbon::now()->format('Y'));
            }
        }

        unset($t);
        $dateValue = implode(' ', $t1);

        $PHPDateArray = date_parse($dateValue);
        if (($PHPDateArray === false) || ($PHPDateArray['error_count'] > 0)) {
            $testVal1 = strtok($dateValue, '- ');
            if ($testVal1 !== false) {
                $testVal2 = strtok('- ');
                if ($testVal2 !== false) {
                    $testVal3 = strtok('- ');
                    if ($testVal3 === false) {
                        $testVal3 = strftime('%Y');
                    }
                } else {
                    return 'values';
                }
            } else {
                return 'values';
            }

            $PHPDateArray = date_parse($testVal1 . '-' . $testVal2 . '-' . $testVal3);
            if (($PHPDateArray === false) || ($PHPDateArray['error_count'] > 0)) {
                $PHPDateArray = date_parse($testVal2 . '-' . $testVal1 . '-' . $testVal3);
                if (($PHPDateArray === false) || ($PHPDateArray['error_count'] > 0)) {
                    return 'values';
                }
            }
        }

        if (($PHPDateArray !== false) && ($PHPDateArray['error_count'] == 0)) {
            // Execute function
            if ($PHPDateArray['year'] == '') {
                $PHPDateArray['year'] = strftime('%Y');
            }

            if ($PHPDateArray['year'] < 1900) {
                return 'values';
            }

            if ($PHPDateArray['month'] == '') {
                $PHPDateArray['month'] = strftime('%m');
            }

            if ($PHPDateArray['day'] == '') {
                $PHPDateArray['day'] = strftime('%d');
            }

            $excelDateValue = floor(static::FormattedPHPToExcel(
                $PHPDateArray['year'],
                $PHPDateArray['month'],
                $PHPDateArray['day'],
                $PHPDateArray['hour'],
                $PHPDateArray['minute'],
                $PHPDateArray['second']
            ));

            return (float)$excelDateValue;
        }

        return 'values';
    }

    /**
     *    Convert a date from PHP to Excel
     *
     * @param    mixed   $dateValue                       PHP serialized date/time or date object
     * @param    boolean $adjustToTimezone                Flag indicating whether $dateValue should be treated as
     *                                                    a UST timestamp, or adjusted to UST
     * @param    string  $timezone                        The timezone for finding the adjustment from UST
     *
     * @return    mixed        Excel date/time value
     *                            or boolean FALSE on failure
     */
    public static function PHPToExcel($dateValue = 0, $adjustToTimezone = false, $timezone = null): float|false
    {
        $saveTimeZone = date_default_timezone_get();
        date_default_timezone_set('UTC');
        $retValue = false;
        if ((is_object($dateValue)) && ($dateValue instanceof \DateTime)) {
            $retValue = static::FormattedPHPToExcel(
                $dateValue->format('Y'),
                $dateValue->format('m'),
                $dateValue->format('d'),
                $dateValue->format('H'),
                $dateValue->format('i'),
                $dateValue->format('s')
            );
        } elseif (is_numeric($dateValue)) {
            $retValue = static::FormattedPHPToExcel(
                date('Y', $dateValue),
                date('m', $dateValue),
                date('d', $dateValue),
                date('H', $dateValue),
                date('i', $dateValue),
                date('s', $dateValue)
            );
        }

        date_default_timezone_set($saveTimeZone);

        return $retValue;
    }

    /**
     * FormattedPHPToExcel
     *
     * @param    int $year
     * @param    int $month
     * @param    int $day
     * @param    int $hours
     * @param    int $minutes
     * @param    int $seconds
     *
     * @return  int                Excel date/time value
     */
    public static function FormattedPHPToExcel($year, $month, $day, $hours = 0, $minutes = 0, $seconds = 0): float
    {
        $_excelBaseDate = static::CALENDAR_WINDOWS_1900;
        if ($_excelBaseDate == static::CALENDAR_WINDOWS_1900) {
            //
            //	Fudge factor for the erroneous fact that the year 1900 is treated as a Leap Year in MS Excel
            //	This affects every date following 28th February 1900
            //
            $excel1900isLeapYear = true;
            if (($year == 1900) && ($month <= 2)) {
                $excel1900isLeapYear = false;
            }

            $my_excelBaseDate = 2415020;
        } else {
            $my_excelBaseDate = 2416481;
            $excel1900isLeapYear = false;
        }

        //	Julian base date Adjustment
        if ($month > 2) {
            $month -= 3;
        } else {
            $month += 9;
            --$year;
        }

        //	Calculate the Julian Date, then subtract the Excel base date (JD 2415020 = 31-Dec-1899 Giving Excel Date of 0)
        $century = substr($year, 0, 2);
        $decade = substr($year, 2, 2);
        $excelDate = floor((146097 * $century) / 4) + floor((1461 * $decade) / 4) + floor((153 * $month + 2) / 5) + $day + 1721119 - $my_excelBaseDate + $excel1900isLeapYear;

        $excelTime = (($hours * 3600) + ($minutes * 60) + $seconds) / 86400;

        return (float)$excelDate + $excelTime;
    }

    /**
     * Convert a date from Excel to a PHP Date/Time object
     *
     * @param    integer $dateValue Excel date/time value
     *
     * @return    integer                        PHP date/time object
     */
    public static function ExcelToPHPObject($dateValue = 0): \DateTime|false
    {
        $dateTime = static::ExcelToPHP($dateValue);
        $days = floor($dateTime / 86400);
        $time = round((($dateTime / 86400) - $days) * 86400);
        $hours = round($time / 3600);
        $minutes = round($time / 60) - ($hours * 60);
        $seconds = round($time) - ($hours * 3600) - ($minutes * 60);

        $dateObj = date_create('1-Jan-1970+' . $days . ' days');
        $dateObj->setTime($hours, $minutes, $seconds);

        return $dateObj;
    }

    /**
     *    Convert a date from Excel to PHP
     *
     * @param        int     $dateValue                   Excel date/time value
     * @param        boolean $adjustToTimezone            Flag indicating whether $dateValue should be treated as
     *                                                    a UST timestamp, or adjusted to UST
     * @param        string  $timezone                    The timezone for finding the adjustment from UST
     *
     * @return        int        PHP serialized date/time
     */
    public static function ExcelToPHP($dateValue = 0, $adjustToTimezone = false, $timezone = null): int|float
    {
        $_excelBaseDate = static::CALENDAR_WINDOWS_1900;
        if ($_excelBaseDate == static::CALENDAR_WINDOWS_1900) {
            $my_excelBaseDate = 25569;
            //	Adjust for the spurious 29-Feb-1900 (Day 60)
            if ($dateValue < 60) {
                --$my_excelBaseDate;
            }
        } else {
            $my_excelBaseDate = 24107;
        }

        // Perform conversion
        if ($dateValue >= 1) {
            $utcDays = $dateValue - $my_excelBaseDate;
            $returnValue = round($utcDays * 86400);
            if (($returnValue <= PHP_INT_MAX) && ($returnValue >= -PHP_INT_MAX)) {
                $returnValue = (int)$returnValue;
            }
        } else {
            $hours = round($dateValue * 24);
            $mins = round($dateValue * 1440) - round($hours * 60);
            $secs = round($dateValue * 86400) - round($hours * 3600) - round($mins * 60);
            $returnValue = (int)gmmktime($hours, $mins, $secs);
        }

        $timezoneAdjustment = ($adjustToTimezone) ? static::getTimeZoneAdjustment($timezone, $returnValue) : 0;

        // Return
        return $returnValue + $timezoneAdjustment;
    }

    /**
     *    Return the Timezone offset used for date/time conversions to/from UST
     *    This requires both the timezone and the calculated date/time to allow for local DST
     *
     * @param        string  $timezone  The timezone for finding the adjustment to UST
     * @param        integer $timestamp PHP date/time value
     *
     * @return        integer                Number of seconds for timezone adjustment
     */
    public static function getTimeZoneAdjustment($timezone, $timestamp)
    {
        if (is_null($timezone)) {
            $timezone = date_default_timezone_get();
        }

        if ($timezone == 'UST') {
            return 0;
        }

        $objTimezone = new \DateTimeZone($timezone);
        $transitions = $objTimezone->getTransitions($timestamp, $timestamp);

        return (count($transitions) > 0) ? $transitions[0]['offset'] : 0;
    }

    /**
     * @param mixed $tbi_price
     * @return mixed
     */
    public static function calc($tbi_price): void
    {
        $obshto = $tbi_price;
        $vnoski = 12;
        $oskapiavane = 0.015;
        $zastrahovka_input = 'n';

        $is3m = false;
        $is6m = false;

        $zstrp = 0.00;
        switch ($zastrahovka_input) {
            case 'n':
                $zstrp = 0.00;
                break;
            case 'z':
                $zstrp = 0.0039;
                break;
            case 'i':
                $zstrp = 0.003978;
                break;
            case 'zi':
                $zstrp = 0.006666;
                break;
            case 'zb':
                $zstrp = 0.0066;
                break;
            case 'zbi':
                $zstrp = 0.010578;
                break;
        }

        $vnoska = (($obshto + ($zstrp * $vnoski * $obshto)) * (1 + $oskapiavane * $vnoski)) / $vnoski;
        $vnoskabezz = ($obshto * (1 + $oskapiavane * $vnoski)) / $vnoski;
        if ($vnoski == 3) {
            if ($is3m) {
                $vnoska = ($obshto + ($zstrp * $vnoski * $obshto)) / $vnoski;
            }
        }

        if ($vnoski == 6) {
            if ($is6m) {
                $vnoska = ($obshto + ($zstrp * $vnoski * $obshto)) / $vnoski;
            }
        }

        $obshto_za_plashtane = $vnoski * $vnoska;

        // gpr
        $array_value = [];
        $array_value[0] = -($tbi_price + $zstrp * $vnoski * $tbi_price);
        for ($i = 1; $i <= $vnoski; $i++) {
            $array_value[$i] = $vnoska;
        }

        $parva_vnoska = '';
        $posledna_vnoska = '';
        if (\Carbon\Carbon::now()->format('d') > 15) {
            if (\Carbon\Carbon::now()->format('d') <= 25) {
                $day_parva_vnoska = '25';
                $day_posledna_vnoska = '25';
            } else {
                $day_parva_vnoska = '5';
                $day_posledna_vnoska = '5';
            }
        } else {
            if (\Carbon\Carbon::now()->format('d') < 6) {
                $day_parva_vnoska = '5';
                $day_posledna_vnoska = '5';
            } else {
                $day_parva_vnoska = '15';
                $day_posledna_vnoska = '15';
            }
        }

        if (\Carbon\Carbon::now()->format('d') > 25) {
            $parva_vnoska = \Carbon\Carbon::parse(\Carbon\Carbon::now()->format('Y') . '-' . \Carbon\Carbon::now()->format('m') . '-' . $day_parva_vnoska . ' + ' . '2 months')->format('Y-m-d');
            $posledna_vnoska = \Carbon\Carbon::parse(\Carbon\Carbon::now()->format('Y') . '-' . \Carbon\Carbon::now()->format('m') . '-' . $day_parva_vnoska . ' + ' . ($vnoski + 1) . ' months')->format('Y-m-d');
        } else {
            $parva_vnoska = \Carbon\Carbon::parse(\Carbon\Carbon::now()->format('Y') . '-' . \Carbon\Carbon::now()->format('m') . '-' . $day_parva_vnoska . ' + ' . '1 months')->format('Y-m-d');
            $posledna_vnoska = \Carbon\Carbon::parse(\Carbon\Carbon::now()->format('Y') . '-' . \Carbon\Carbon::now()->format('m') . '-' . $day_parva_vnoska . ' + ' . $vnoski . ' months')->format('Y-m-d');
        }

        $array_date = [];
        $array_date[0] = \Carbon\Carbon::now()->format('Y-m-d');
        $array_date[1] = $parva_vnoska;
        for ($i = 2; $i < $vnoski; $i++) {
            $array_date[$i] = \Carbon\Carbon::parse($parva_vnoska . ' + ' . ($i - 1) . ' months')->format('Y-m-d');
        }

        $array_date[$vnoski] = $posledna_vnoska;

        $gpr = static::XIRR($array_value, $array_date, 0.0001);
        $gpr = $gpr * 100;
    }

}
