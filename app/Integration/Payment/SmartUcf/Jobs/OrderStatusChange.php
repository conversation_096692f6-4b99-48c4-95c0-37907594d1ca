<?php

declare(strict_types=1);

namespace App\Integration\Payment\SmartUcf\Jobs;

use App\Integration\Payment\SmartUcf\SmartUcfService;
use App\Jobs\Job;
use App\Models\Order\Order;
use App\Traits\DuplicateEntryCheck;
use PaymentGateway;

class OrderStatusChange extends Job
{
    use DuplicateEntryCheck;

    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'order-events5';

    /**
     * @param int $order_id
     * @return mixed
     */
    public function __construct(protected int $order_id)
    {
        $this->site_id = site('site_id');
    }

    public function execute()
    {
        $site = $this->getSite();
        if ($site === null) {
            return static::DESTROY;
        }

        // move job to another platform
        if (!allowSiteByPlatform()) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', platform(), sitePlatform()));
            return static::WRONG_PLATFORM;
        }

        $order = Order::find($this->order_id);
        if (!$order) {
            $this->info(sprintf('Missing order #%d', $this->order_id));
            return static::DESTROY;
        }

        $payment = $order->payments->last();
        if ($order->status != 'completed' || 'completed' != $payment->status || 'smart_ucf' != $payment->provider) {
            return static::DESTROY;
        }

        /** @var SmartUcfService $gateway */
        $gateway = PaymentGateway::provider('smart_ucf');
        $gateway->sendInvoice($payment);

        return static::DESTROY;
    }
}
