{include file="./dependency.tpl"}
<div class="page-breadcrumb clearfix">
    {$breadcrumb = [
    [
    'title' => "{t}payment_provider.header.payment_providers{/t}",
    'href' => "{route('admin.payment_providers')}"
    ],
    [
    'title' => "{$paymentMethod->name}"
    ]
    ]}
    {include file="includes/breadcrumb.tpl"}
</div>

<div class="wrapper">
    <ul class="tabs-large margin-bottom-0">
        <li class="active">
            <a class="tabs-large-item" href="{route('admin.ucf.promo')}">{t}discount.label.discount_leasing{/t}</a>
        </li>

        <li>
            <a class="tabs-large-item" href="{route('admin.ucf.mapping')}">{t}category.header.categories{/t}</a>
        </li>
    </ul>

    <div class="container">
        <div id="fileupload" class="padding-top-30 fileupload-hidden" data-url="{route('admin.ucf.import-promo')}"
             data-multiple="false" data-browse-button=".upload_browse"
             data-max-size="20mb" data-extensions="xls"></div>

        <div class="row box margin-top-0 margin-bottom-0">
            <div class="box-title">
                <div class="box-title-text">
                    <a data-modal-size="small" data-modal-ajax="{route('admin.ucf.promo.edit', 0)}" href="#"
                       class="btn btn-primary">
                        {t}discount.action.add{/t}
                    </a>
                </div>
                <div class="box-title-text pull-right">
                    <a href="{route('admin.ucf.export-promo')}" target="_blank" class="btn btn-default">
                        {t}global.export{/t}
                    </a>
                    {if !disabledUpload()}
                        <a href="javascript:" class="btn btn-primary upload_browse"
                           id="btn-files-add">{t}customer.action.import{/t}</a>
                    {else}
                        <div class="text text-danger">File upload is temporary disabled! Please wait some time.</div>
                    {/if}
                </div>
            </div>
        </div>

        <div id="noResults" class="row box hidden">
            <div class="content-padding padding-top-10">
                <div class="empty-box product-stock">
                    <span class="title">{t}discount.notify.no_records_yet{/t}</span>
                    <p>{t}discount.notify.no_records_info{/t}</p>
                    <div class="empty-image-holder container">
                        {*<img src="{$img_url}sitecp/img/empty/blank-image.svg?{app('last_build')}">*}
                    </div>
                </div>
            </div>

            <div class="help-box">
                <i class="fal fa-life-ring fa-3x cc-grey"></i>
                <div class="help-container">
                    <span>{t}discount.notify.no_records_help{/t}</span>
                    <a href="{app('support_url')}" target="_blank">{t}discount.notify.no_records_help_link{/t}</a>
                </div>
            </div>
        </div>

        <div id="promotions_wrapper" class="grid-wrapper padding-top-0 hidden"
             data-url="{route('admin.ucf.promo')}">
            <div class="row">
                <table class="listing">
                    <thead>
                    <tr>
                        <th data-field="name_formatted" data-sort="no">{t}discount.th.details{/t}</th>
                        <th data-field="ucf_cop" data-sort="no">{t}global.category{/t}</th>
                        <th data-field="start_date_formatted" data-sort="no">{t}discount.th.date_start{/t}</th>
                        <th data-field="end_date_formatted" data-sort="no">{t}discount.th.date_end{/t}</th>
                        <th data-field="action" data-sort="no" width="50" data-align="right">

                        </th>
                    </tr>
                    </thead>
                </table>
            </div>

            <form id="save-html" action="{route('admin.ucf.promo-html.save')}" class="ajaxForm">
                <div class="row box margin-top-30">
                    <div class="box-title">
                        <div class="box-title-text">
                            <h5>{t}discount.leasing_promotion_html{/t}</h5>
                        </div>
                        <div class="box-title-text pull-right">
                            <button class="btn btn-primary submit">{t}global.save{/t}</button>
                        </div>
                    </div>

                    <div class="box-section">
                        <div class="col-xs-12">
                            <textarea class="tinymce" id="tinymce-{uniqid()}" data-height="256" data-tool-table="true"
                                      data-tool-image="true" data-tool-media="true" name="promo_html"
                                      placeholder="{t}payment_provider.ph.bwt.description{/t}">{$paymentMethod.configuration.configuration.promo_html|default}</textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

{capture append="js"}
    <script type="text/javascript">
        var $wrapper = $('#promotions_wrapper');

        $wrapper.on('cc.ajax.success', function (e, data) {
            if (!$(e.target).is('.delete') && !$(e.target).is('#save-html')) {
                resultsUpdate($wrapper, data);
            }
        });

        $wrapper.on('cc.ajax.success', '.delete', function () {
            $wrapper.trigger('cc.ajax.reload');
        });

        function resultsUpdate($wrapper, data) {
            if (data.custom_data.records == false) {
                $('#noResults').removeClass('hidden');
                $wrapper.addClass('hidden');
            } else {
                $('#noResults').addClass('hidden');
                $wrapper.removeClass('hidden');
            }
        }
    </script>
{/capture}