<?php

declare(strict_types=1);

namespace App\Integration\Payment\TbiBank;

use App\Exceptions\PaymentBadRequest;
use Illuminate\Http\Request;
use App\Integration\Payment\AbstractService;
use App\Models\Gateway\PaymentLogs;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\Payments;
use Illuminate\Http\Response;

class TbiBankService extends AbstractService
{
    protected \App\Integration\Payment\TbiBank\Client $client;

    /**
     * Service constructor.
     */
    public function __construct()
    {
        $this->config = $this->loadConfig();
        $this->client = new Client($this->config);
    }

    /**
     * @return array
     */
    protected function loadConfig()
    {
        $provider = PaymentProviderConfiguration::findByProvider('tbi_bank');
        if (!empty($provider->configuration)) {
            return $provider->configuration;
        }

        return [];
    }

    /**
     * @param Payments $payment
     * @return array|Response
     * @throws \SmartyException
     */
    public function purchase(Payments $payment): array
    {
        $response = $this->client->purchase($payment);
        if (isset($response['errorCode'])) {
            return [
                'status' => Payments::STATUS_FAILED,
                'action' => false,
                'message' => $response['errorMessage']
            ];
        }

        $payment->provider_reference_id = $response['orderId'];

        $payment->provider_data = $response;
        PaymentLogs::siteToProvider($payment, [], $response);
        $payment->sync();

        return [
            'payment' => [
                'payment_id' => $payment->id,
            ],
            'status' => $payment->status,
            'action' => [
                'type' => 'redirect',
                'url' => $response['formUrl'],
            ],
        ];
    }

    /**
     * @param Request $request
     * @return Payments
     */
    public static function getPaymentFromRequest(Request $request)
    {
        $paymentId = $request->input('pid');
        if (empty($paymentId)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        $payment = Payments::find($paymentId);
        if (empty($payment)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        return $payment;

    }

    /**
     * @param Payments $payment
     * @param array $data
     */
    public function capturePurchase(Payments $payment, array $data = []): void
    {
        $this->sync($payment);
    }

    /**
     * @param Payments $payment
     * @param array $data
     */
    public function completePurchase(Payments $payment, array $data = []): void
    {
        $this->sync($payment);
    }

    /**
     * @param Payments $payment
     * @return void
     */
    public function refund(Payments $payment): void
    {
        $response = $this->client->refund($payment->provider_reference_id, $payment->provider_data->amount);
        PaymentLogs::siteToProvider($payment, [$payment->provider_reference_id], $response, 'refund');
        $this->sync($payment);
    }

    /**
     * @param Payments $payment
     */
    public function sync(Payments $payment): void
    {
        $response = $this->client->sync($payment->provider_reference_id, $payment->order_payment->order_id);
        PaymentLogs::siteToProvider($payment, [], $response, 'sync');
        if (isset($response['orderStatus'])) {
            $payment->status = $this->ReturnStatus($response['orderStatus']);
            $payment->provider_data = $response;
            $payment->sync();
        }
    }

    /**
     * @param $response_status
     * @return string
     */
    protected function ReturnStatus($response_status): string
    {
        switch ($response_status) {
            case 0:
                return Payments::STATUS_REQUESTED;
            case 1:
            case 2:
                return Payments::STATUS_COMPLETED;
            case 4:
                return Payments::STATUS_REFUNDED;
            default:
                $status = Payments::STATUS_FAILED;
        }

        return $status;
    }
}
