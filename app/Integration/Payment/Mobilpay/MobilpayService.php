<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 6.4.2017 г.
 * Time: 12:30
 */

namespace App\Integration\Payment\Mobilpay;

use App\Exceptions\PaymentBadRequest;
use App\Models\Gateway\Currency;
use Exception;
use Illuminate\Http\Request;
use App\Exceptions\Error;
use App\Integration\Payment\AbstractService;
use App\Models\Gateway\PaymentLogs;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\Payments;
use Illuminate\Http\Response;
use Omnipay\Common\Exception\InvalidRequestException;
use Omnipay\Common\GatewayFactory;
use Omnipay\MobilPay\Exception\MissingKeyException;
use Omnipay\MobilPay\Gateway;
use Omnipay\MobilPay\Message\CompletePurchaseResponse;
use Omnipay\MobilPay\Message\PurchaseRequest;
use Omnipay\MobilPay\Message\PurchaseResponse;

/**
 * Class MobilpayService
 *
 * @package App\Integration\Payment\Mobilpay
 * @method  refund(Payments $payment)
 * @method  sync(Payments $payment)
 */
class MobilpayService extends AbstractService
{
    /** @var Gateway */
    protected $omnipay;

    /**
     * Service constructor.
     */
    public function __construct()
    {
        $factory = new GatewayFactory();

        $this->omnipay = $factory->create('MobilPay');
        $this->loadConfig();
    }

    /**
     * @param Request $request
     * @return Payments|void
     */
    public static function getPaymentFromRequest(Request $request)
    {
        $paymentId = $request->input('pid');
        if (empty($paymentId)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        $payment = Payments::find($paymentId);
        if (empty($payment)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        return $payment;
    }

    /**
     *
     */
    protected function loadConfig()
    {
        $provider = PaymentProviderConfiguration::findByProvider('mobilpay');

        if (!empty($provider->configuration)) {
            $suffix = 'live' == $provider->configuration['mode'] ? '_live' : '_test';

            //            $publicKeyFile = tmpfile();
            //            fwrite($publicKeyFile, $provider->configuration['certificate' . $suffix]);
            //            $publicKeyFilePath = stream_get_meta_data($publicKeyFile)['uri'];
            //
            //            $privateKeyFile = tmpfile();
            //            fwrite($privateKeyFile, $provider->configuration['certificate_private' . $suffix]);
            //            $privateKeyFilePath = stream_get_meta_data($privateKeyFile)['uri'];

            file_put_contents(
                $publicKeyFilePath = tempnam(sys_get_temp_dir(), 'mobilpay-public-'),
                $provider->configuration['certificate' . $suffix]
            );

            file_put_contents(
                $privateKeyFilePath = tempnam(sys_get_temp_dir(), 'mobilpay-private-'),
                $provider->configuration['certificate_private' . $suffix]
            );

            register_shutdown_function(function () use ($publicKeyFilePath, $privateKeyFilePath): void {
                unlink($publicKeyFilePath);
                unlink($privateKeyFilePath);
            });

            $this->omnipay->setTestMode($provider->configuration['mode'] == 'test');
            $this->omnipay->setMerchantId($provider->configuration['merchant_id']);
            $this->omnipay->setPublicKey($publicKeyFilePath);
            $this->omnipay->setPrivateKey($privateKeyFilePath);
        }
    }

    /**
     * @param Payments $payment
     * @return array
     * @throws Error
     * @throws InvalidRequestException
     * @throws MissingKeyException
     * @throws Exception
     */
    public function purchase(Payments $payment): array
    {
        if ('RON' != $payment->currency) {
            $payment->amount = round(Currency::convert(
                $payment->amount,
                $payment->currency,
                'RON'
            ), 0);
            $payment->currency = 'RON';
        }

        $order_id = (setting('order_id_display') == 'increment_hash') ? $payment->orderPayment->order->increment_hash : $payment->orderPayment->order_id;
        /** @var PurchaseRequest $request */
        $request = $this->omnipay->purchase([
            'amount' => $payment->amount / 100,
//            'currency' => $payment->currency,
            'currency' => 'RON',
            'orderId' => $payment->id,
            'details' => "Order #" . $order_id . ' | ' . site('host'),
            'confirmUrl' => route('payments.webhook', ['provider' => 'mobilpay', 'pid' => $payment->id]),
            'returnUrl' => route('site.payment.return', $payment->id),
//            'cancelUrl' => route('site.payment.cancel', $payment->id),
            'billingAddress' => $this->getAddress($payment),
        ]);

        try {
            /** @var PurchaseResponse $response */
            $response = $request->send();
        } catch (Exception $exception) {
            throw new Error($exception->getMessage());
        }

        PaymentLogs::siteToProvider($payment, $request->getData(), $response->getData());

        if ($response->isRedirect()) {
            $payment->provider_reference_id = $payment->getKey();
            $payment->provider_data = [
                'form' => base64_encode((string) $response->getRedirectResponse()->getContent())
            ];
            $payment->sync();
        } else {
            throw new Error($response->getMessage());
        }

        return [
            'payment' => [
                'payment_id' => $payment->id,
            ],
            'status' => Payments::STATUS_REQUESTED,
            'htmlRedirectForm' => $response->getRedirectResponse()->getContent(),
//            'action' => [
//                'type' => 'redirect',
//                'url' => route('site.payment.redirect', $payment->id),
//            ]
        ];
    }

    /**
     * @param Payments $payment
     * @return Response
     */
    public function redirect(Payments $payment)
    {
        return response(base64_decode((string) $payment->provider_data->form));
    }

    /**
     * @param Payments $payment
     * @param array $data
     */
    public function completePurchase(Payments $payment, array $data = []): void
    {
        if ('requested' == $payment->status) {
            $payment->status = 'pending';
            $payment->sync();
        }
    }

    /**
     * MobilPay IPN
     *
     * @param Payments $payment
     * @param array $data
     * @return void
     */
    public function capturePurchase(Payments $payment, array $data = []): void
    {
        /** @var CompletePurchaseResponse $response */
        $response = $this->omnipay->completePurchase($data)->send();

        PaymentLogs::providerToSite($payment, $data, $response->getData(), 'ipn');

        $payment->provider_data = $response->getData();
        $payment->status = $this->getTransactionState($response);
        $payment->sync();

        $response->sendResponse();
    }

    /**
     * @param CompletePurchaseResponse $response
     * @return string
     */
    protected function getTransactionState(CompletePurchaseResponse $response): string
    {
        if ($response->isSuccessful()) {
            return Payments::STATUS_COMPLETED;
        }

        if ($response->isPending()) {
            return Payments::STATUS_PENDING;
        }

        if ($response->isCancelled()) {
            //            return Payments::STATUS_CANCELED;
            return Payments::STATUS_FAILED;
        }

        if ($response->isRefunded()) {
            return Payments::STATUS_REFUNDED;
        }

        return Payments::STATUS_REQUESTED;
    }

    /**
     * @param Payments $payment
     * @return mixed
     */
    protected function getAddress(Payments $payment)
    {
        $paymentAddress = $payment->address ?: $payment->shipping_address;
        if (empty($paymentAddress)) {
            $paymentAddress = [
                'company' => false,
                'first_name' => $payment->orderPayment->order->customer_first_name,
                'last_name' => $payment->orderPayment->order->customer_last_name,
                'country' => $payment->country,
                'state' => $payment->country,
                'city' => $payment->country,
                'address_formatted' => $payment->country,
                'postal_code' => null,
                'phone' => null,
            ];
        }

        $address['type'] = $paymentAddress['company'] ? 'company' : 'person'; // person or company
        $address['firstName'] = $paymentAddress['first_name'];
        $address['lastName'] = $paymentAddress['last_name'];
        $address['fiscalNumber'] = null;
        $address['identityNumber'] = null;
        $address['country'] = $paymentAddress['country'];
        $address['county'] = $paymentAddress['state'];
        $address['city'] = $paymentAddress['city'];
        $address['zipCode'] = $paymentAddress['postal_code'];
        $address['address'] = $paymentAddress['address_formatted'];
        $address['email'] = $payment->client_email;
        $address['mobilePhone'] = $paymentAddress['phone'];
        $address['bank'] = null;
        $address['iban'] = null;

        return $address;
    }
}
