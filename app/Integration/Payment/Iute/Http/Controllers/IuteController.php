<?php

declare(strict_types=1);

namespace App\Integration\Payment\Iute\Http\Controllers;

use App\Exceptions\Error;
use App\Integration\Payment\Iute\Exports\IuteExport;
use App\Integration\Payment\Iute\Imports\IuteImport;
use App\Models\Product\Product;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;
use Iute;
use Maatwebsite\Excel\Facades\Excel;

/**
 * IuteController
 */
class IuteController extends Controller
{
    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Error
     * @throws ValidationException
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = Iute::getProductMappings([
                'page' => $request->get('page', 1) - 1,
                'size' => $request->get('perpage', 25),
                'sort' => $request->get('sort', 'id'),
            ]);
        } catch (ClientException $e) {
            throw ValidationException::withMessages([
                json_decode($e->getResponse()->getBody()->getContents(), true),
            ]);
        } catch (\Exception $e) {
            throw ValidationException::withMessages([
                $e->getMessage(),
            ]);
        }

        $items = $data['content'];
        $productIds = Arr::pluck($items, 'sku');
        $products = Product::whereIn('id', $productIds)->get()->keyBy('id');
        foreach ($items as &$item) {
            $item['id'] = $item['productId'] . '|' . $item['sku'];
            /** @var Product $product */
            if ($product = $products->get($item['sku'])) {
                $item['name'] = $product->name;
                $item['img'] = $product->getImage('150x150');
                $item['url'] = $product->url;
            } else {
                $item['name'] = '<span style="color: red">Not Found</span>';
                $item['img'] = null;
                $item['url'] = null;
            }
        }

        $response = new LengthAwarePaginator($items, $data['totalElements'], $data['size']);
        $response->setPath($request->url());

        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     * @throws ValidationException
     */
    public function store(Request $request): Response
    {
        $this->validate($request, [
            'schemeId' => 'required',
            'products' => 'required|array',
        ]);

        $schemeId = $request->input('schemeId');
        $productIds = $request->input('products');

        $products = [];
        foreach ($productIds as $productId) {
            $products[] = [
                'productId' => $schemeId,
                'sku' => $productId,
            ];
        }

        try {
            Iute::createProductMappings($products);
        } catch (ClientException $clientException) {
            throw ValidationException::withMessages([
                json_decode($clientException->getResponse()->getBody()->getContents(), true),
            ]);
        }

        return response()->noContent(201);
    }

    /**
     * @return array
     * @throws Error
     * @throws ValidationException
     */
    public function getLoanProducts()
    {
        try {
            return Iute::getLoanProducts();
        } catch (ClientException $e) {
            throw ValidationException::withMessages([
                json_decode($e->getResponse()->getBody()->getContents(), true),
            ]);
        } catch (\Exception $e) {
            throw new Error($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return Response
     * @throws ValidationException
     */
    public function delete(Request $request): Response
    {
        $ids = $request->get('ids');

        $data = [];
        foreach ($ids as $id) {
            [$productId, $sku] = explode('|', (string) $id);
            $data[] = compact('productId', 'sku');
        }

        try {
            Iute::deleteProductMappings($data);
        } catch (ClientException $clientException) {
            throw ValidationException::withMessages([
                json_decode($clientException->getResponse()->getBody()->getContents(), true),
            ]);
        }

        return response()->noContent();
    }

    /**
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     * @throws ValidationException
     */
    public function export()
    {
        try {
            $data = Iute::getProductMappings([
                'size' => 1000,
            ]);
        } catch (ClientException $clientException) {
            throw ValidationException::withMessages([
                json_decode($clientException->getResponse()->getBody()->getContents(), true),
            ]);
        }

        $items = collect();
        $records = $data['content'] ?? [];
        foreach ($records as $record) {
            $items->push(Arr::only($record, ['productId', 'sku']));
        }

        return Excel::download(new IuteExport($items), 'cloudcart-iute-product-mapping.csv');
    }

    /**
     * @param Request $request
     * @return Response
     * @throws ValidationException
     */
    public function import(Request $request)
    {
        if ($request->hasFile('file')) {
            try {
                Excel::import(new IuteImport(), $request->file('file'));
            } catch (ClientException $e) {
                throw ValidationException::withMessages([
                    json_decode($e->getResponse()->getBody()->getContents(), true),
                ]);
            }
        }

        return response()->noContent(201);
    }
}
