<div id="iute-trigger" class="iute-as-low-as" data-id="{{ $product_id }}"
     data-amount="{{ $price }}" data-page-type="product"
     data-sku="{{ $product_id }}"
     data-learnmore-show="false"></div>
<input type="hidden" name="payment_variant_id" value="3">
<script type="text/javascript">
    $(function () {
        $.getScript('{{Iute::getBaseUrl()}}/iutepay.js', function () {
            iute.configure('{{Iute::getApiKey()}}', '{{Iute::getLang()}}');
            iute.onFastCheckout(function (data) {
                console.log(data);
                let $form = $('.add-to-cart-form-js');
                $form.append('<input type="hidden" name="leasing-options-provider" value="iute" />');
                $form.append('<input type="hidden" name="payment_variant_id" value="3" />');
                $form.submit();
            })

            let $btn = $('#iute-trigger');
            iute.openPromoWindowModal($btn[0]);
        });

        function loadCSS() {
            let cssLink = $("<link>");
            $("head").append(cssLink); // Append to head
            cssLink.attr({
                rel: "stylesheet",
                type: "text/css",
                href: "{{Iute::getBaseUrl()}}/iutepay.css"
            });
        }

        loadCSS();
    });
</script>