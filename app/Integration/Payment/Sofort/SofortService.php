<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 9.5.2017 г.
 * Time: 13:04
 */

namespace App\Integration\Payment\Sofort;

use App\Exceptions\Error;
use App\Exceptions\PaymentBadRequest;
use App\Integration\Payment\AbstractService;
use App\Models\Gateway\Currency;
use App\Models\Gateway\PaymentLogs;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\Payments;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Omnipay\Common\GatewayFactory;
use Omnipay\Sofort\Gateway;
use Omnipay\Sofort\Message\NotifyRequest;
use Omnipay\Sofort\Message\NotifyResponse;
use Omnipay\Sofort\Message\PurchaseRequest;
use Omnipay\Sofort\Message\PurchaseResponse;
use SimpleXMLElement;

/**
 * @method  completePurchase(Payments $payment, array $data = array())
 * @method  refund(Payments $payment)
 */
class SofortService extends AbstractService
{
    /**
     * An array with supported currencies.
     * @var array
     */
    public static $supportedCurrencies = [
        'CHF' => 'Swiss Franc',
        'CZK' => 'Czech Koruna',
        'EUR' => 'Euro',
        'GBP' => 'British Pound',
        'HUF' => 'Hungarian Forint',
        'PLN' => 'Polish Złoty',
    ];

    /** @var \Omnipay\Sofort\Gateway */
    protected $omnipay;

    /**
     * @var
     */
    protected $config;

    /**
     * @var
     */
    protected $siteCurrency;

    /**
     * @var
     */
    protected $currency;

    /**
     * @param Request $request
     * @return Payments
     */
    public static function getPaymentFromRequest(Request $request)
    {
        try {
            $data = new SimpleXMLElement(file_get_contents('php://input'));
            $provider_reference_id = (string)$data->transaction;
        } catch (\Exception) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        $payment = Payments::findByProviderReferenceId('sofort', $provider_reference_id);

        if (!$payment) {
            throw new ModelNotFoundException();
        }

        return $payment;
    }

    /**
     * Service constructor.
     */
    public function __construct()
    {
        $factory = new GatewayFactory();
        $this->omnipay = $factory->create('Sofort');
        $this->loadConfig();
        $this->loadCurrency();
    }

    /**
     *
     */
    protected function loadConfig()
    {
        $config = $this->omnipay->getDefaultParameters();
        $config['currency'] = 'EUR';

        $provider = PaymentProviderConfiguration::findByProvider('sofort');

        if (!empty($provider->configuration)) {
            $config['username'] = $provider->configuration['customer_number'];
            $config['password'] = $provider->configuration['api_key'];
            $config['projectId'] = $provider->configuration['project_id'];
            $config['currency'] = $provider->configuration['currency'];
        }

        $reflection = new \ReflectionClass(Gateway::class);

        foreach ($config as $optionName => $value) {
            $method = 'set' . ucfirst($optionName);

            if ($reflection->hasMethod($method)) {
                $this->omnipay->{$method}($value);
            }
        }

        $this->config = $config;
    }

    /**
     *
     */
    protected function loadCurrency()
    {
        $currency = site('currency');
        $this->siteCurrency = $currency;
        if (!in_array($currency, array_keys(static::$supportedCurrencies))) {
            $currency = $this->config['currency'];
        }

        $this->currency = $currency;
    }

    /**
     * @param Payments $payment
     * @return float
     */
    protected function getAmount(Payments $payment): int|float
    {
        $amount = $payment->amount / 100;
        if ($this->siteCurrency != $this->currency) {
            $amount = round(Currency::convert($amount, $this->siteCurrency, $this->currency), 2);
        }

        return $amount;
    }

    /**
     * @param Payments $payment
     * @return array
     * @throws Error
     */
    public function purchase(Payments $payment): array
    {
        /** @var PurchaseRequest $request */
        $request = $this->omnipay->purchase([
            'amount' => $this->getAmount($payment),
            'currency' => $this->currency,
            'description' => 'Payment ' . $payment->id,
            'returnUrl' => route('site.payment.return', $payment->id),
            'cancelUrl' => route('site.payment.cancel', $payment->id),
            'notifyUrl' => route('payments.webhook', 'sofort'),
//            'redirect_url' => route('payments.return', 'instamojo'),
        ]);

        /** @var PurchaseResponse $response */
        $response = $request->send();

        PaymentLogs::siteToProvider($payment, $request->getData(), $response->getData());

        if ($response->isRedirect()) {
            $payment->provider_reference_id = $response->getTransactionReference();
            $payment->provider_data = $response->getData();
            $payment->status = $response->getTransactionStatus();
            $payment->sync();
        } else {
            throw new Error($response->getMessage());
        }

        return [
            'payment' => [
                'payment_id' => $payment->id,
                'site_reference_id' => $payment->site_reference_id,
            ],
            'status' => $payment->status,
            'action' => [
                'type' => 'redirect',
                'url' => $response->getRedirectUrl(),
            ]
        ];
    }

    /**
     * @param Payments $payment
     */
    public function sync(Payments $payment): void
    {
        /** @var NotifyRequest $request */
        $request = $this->omnipay->acceptNotification([
            'transactionReference' => $payment->provider_reference_id,
        ]);

        /** @var NotifyResponse $response */
        $response = $request->send();

        PaymentLogs::siteToProvider($payment, $request->getParameters(), $response->getData(), 'sync');

        $payment->provider_data = $response->getData();
        $payment->status = $response->getTransactionStatus();
        $payment->sync();
    }

    /**
     * @param \App\Models\Gateway\Payments $payment
     * @param array $data
     * @return mixed
     */
    public function capturePurchase(Payments $payment, array $data = []): void
    {
        /** @var NotifyRequest $request */
        $request = $this->omnipay->acceptNotification();

        /** @var NotifyResponse $response */
        $response = $request->send();

        PaymentLogs::providerToSite($payment, $request->getParameters(), $response->getData(), 'ipn');

        $payment->provider_data = $response->getData();
        $payment->status = $response->getTransactionStatus();
        $payment->sync();
    }

    /**
     * @param mixed $name
     * @param mixed $arguments
     * @return mixed
     */
    #[\Override]
    public function __call($name, $arguments)
    {
        // TODO: Implement @method  completePurchase(Payments $payment, array $data = array())
        // TODO: Implement @method  refund(Payments $payment)
    }
}
