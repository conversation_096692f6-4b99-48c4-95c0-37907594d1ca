{include file="../../views/provider_dependency.tpl"}

<form action="{route('admin.payment_providers.save', $provider->provider)}" id="editProviderForm" role="form" class="ajaxForm" data-multipart="true">
    {include file="../../views/title_logo.tpl"}

    <div class="box">
        <div class="box-title">
            <div class="box-title-text">
                <h5>{t}payment_provider.label.settle.mode{/t}</h5>
            </div>
        </div>

        <div class="box-section">
            <div class="row form-group">
                <div class="col-xs-12">
                    <span class="help-block">{t}payment_provider.help.settle.mode{/t}</span>

                    <div class="form-control-box">
                        <div class="form-control-box-inner">
                            <input name="configuration[mode]" type="checkbox"{if $provider.configuration.mode|default == 'live'} checked="checked"{/if} class="switch" value="live" data-off="{t}payment_provider.switch.live{/t}" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="box">
        <div class="box-title">
            <div class="box-title-text">
                <h5>{t}global.settings{/t}</h5>
            </div>
        </div>

        <div class="box-section">
            <div class="row form-group">
                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.settle.merchant_id{/t}</label>

                    <input type="text" name="configuration[merchant_id]" class="form-control" value="{$provider.configuration.merchant_id|default}"  data-autofocus="" placeholder="{t}payment_provider.ph.settle.merchant_id{/t}" />

                    <span class="help-block">{t}payment_provider.help.settle.merchant_id{/t}</span>
                </div>
                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.settle.merchant_key{/t}</label>

                    <textarea name="configuration[merchant_key]" class="form-control" data-autofocus="">{$provider.configuration.merchant_key|default}</textarea>

                    <span class="help-block">{t}payment_provider.help.settle.merchant_key{/t}</span>
                </div>
            </div>
            <div class="row form-group">
                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.settle.merchant_settle_user{/t}</label>

                    <input type="text" name="configuration[merchant_settle_user]" class="form-control" value="{$provider.configuration.merchant_settle_user|default}" data-autofocus="" placeholder="{t}payment_provider.ph.settle.merchant_settle_user{/t}" />

                    <span class="help-block">{t}payment_provider.help.settle.merchant_settle_user{/t}</span>
                </div>
            </div>
        </div>
    </div>

    {include file="../../views/from-to.tpl"}

    {include file="../../views/discount.tpl"}
</form>