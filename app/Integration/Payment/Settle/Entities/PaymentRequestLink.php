<?php

declare(strict_types=1);

namespace App\Integration\Payment\Settle\Entities;

use App\Integration\Payment\Settle\Contracts\PaymentRequestLinkShowOn;
use Illuminate\Contracts\Support\Arrayable;

final readonly class PaymentRequestLink implements Arrayable
{
    /**
     * @param string|null $uri
     * @param string|null $caption
     * @param string|null $show_on
     * @return mixed
     */
    private function __construct(private ?string $uri = null, private ?string $caption = null, private ?string $show_on = null)
    {
    }

    /**
     * @param string|null $uri
     * @param string|null $caption
     * @return mixed
     */
    public static function showOnPending(
        ?string $uri = null,
        ?string $caption = null
    ): PaymentRequestLink {
        return new self($uri, $caption, PaymentRequestLinkShowOn::PENDING);
    }

    /**
     * @param string|null $uri
     * @param string|null $caption
     * @return mixed
     */
    public static function showOnOk(
        ?string $uri = null,
        ?string $caption = null
    ): PaymentRequestLink {
        return new self($uri, $caption, PaymentRequestLinkShowOn::OK);
    }

    /**
     * @param string|null $uri
     * @param string|null $caption
     * @return mixed
     */
    public static function showOnFail(
        ?string $uri = null,
        ?string $caption = null
    ): PaymentRequestLink {
        return new self($uri, $caption, PaymentRequestLinkShowOn::FAIL);
    }


    public function toArray(): array
    {
        return get_object_vars($this);
    }

    /**
     * @return string|null
     */
    public function getUri(): ?string
    {
        return $this->uri;
    }

    /**
     * @return string|null
     */
    public function getCaption(): ?string
    {
        return $this->caption;
    }

    /**
     * @return string|null
     */
    public function getShowOn(): ?string
    {
        return $this->show_on;
    }
}
