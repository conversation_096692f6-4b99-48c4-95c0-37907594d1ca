<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 13.3.2017 г.
 * Time: 12:57
 */

namespace App\Integration\Payment\Epay;

use App\Contracts\ValidatorContract;

class ConfigurationValidator implements ValidatorContract
{
    public function rules(): array
    {
        return [
            'configuration.kin' => 'required',
            'configuration.secret' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'configuration.kin.required' => "EPay: KIN is required.",
            'configuration.secret.required' => "EPay: secret is required."
        ];
    }

}
