<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 6.4.2017 г.
 * Time: 12:30
 */

namespace App\Integration\Payment\Epay;

use App\Exceptions\Error;
use App\Exceptions\PaymentBadRequest;
use App\Integration\Payment\AbstractService;
use App\Models\Gateway\Currency;
use App\Models\Gateway\PaymentLogs;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\Payments;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Omnipay\Common\GatewayFactory;
use Omnipay\Epay\Gateway;
use Omnipay\Epay\Message\NotifyRequest;
use Omnipay\Epay\Message\NotifyResponse;
use Omnipay\Epay\Message\PurchaseRequest;
use Omnipay\Epay\Message\PurchaseResponse;

/**
 * Class EpayService
 * @package App\Integration\Payment\Epay
 * @method  refund(Payments $payment)
 * @method  sync(Payments $payment)
 */
class EpayService extends AbstractService
{
    /**
     * @var array
     */
    protected $config;

    /** @var \Omnipay\Epay\Gateway */
    protected $omnipay;

    /**
     * @param Request $request
     * @return Payments
     */
    public static function getPaymentFromRequest(Request $request)
    {
        $encoded = $request->input('encoded');
        if (empty($encoded)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        $invoice = static::decode($encoded);
        if (empty($invoice)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        $payment = Payments::findOrFail($invoice);
        if ($payment->provider != 'epay') {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        return $payment;
    }

    /**
     * @param mixed $encoded
     * @return mixed
     */
    protected static function decode($encoded): string
    {
        $data = base64_decode((string) $encoded);
        $lines_arr = explode("\n", $data);
        $invoice = '';

        foreach ($lines_arr as $line) {
            if (preg_match("/^INVOICE=(\d+):STATUS=(PAID|DENIED|EXPIRED)(:PAY_TIME=(\d+):STAN=(\d+):BCODE=([0-9a-zA-Z]+))?$/", $line, $regs)) {
                $invoice = $regs[1] ?? null;
            }
        }

        return $invoice;
    }

    /**
     * Service constructor.
     */
    public function __construct()
    {
        $factory = new GatewayFactory();
        $this->omnipay = $factory->create('Epay');
        $this->loadConfig();
    }

    /**
     *
     */
    protected function loadConfig()
    {
        $config = [];
        $provider = PaymentProviderConfiguration::findByProvider('epay');
        if (!empty($provider->configuration)) {
            $config['min'] = $provider->configuration['kin'];
            $config['signature'] = $provider->configuration['secret'];
            $config['testMode'] = 'live' != $provider->configuration['mode'];
            $config['enable_iframe'] = $provider->configuration['enable_iframe'] ?? false;
        }

        $this->config = $config;
        $reflection = new \ReflectionClass(Gateway::class);

        foreach ($this->config as $optionName => $value) {
            $method = 'set' . ucfirst($optionName);

            if ($reflection->hasMethod($method)) {
                $this->omnipay->{$method}($value);
            }
        }
    }

    /**
     * @param Payments $payment
     * @return array
     * @throws Error
     * @throws \SmartyException
     */
    public function purchase(Payments $payment): array
    {
        $amount = $payment->amount / 100;
        $siteCurrency = $payment->currency;
        if ('BGN' != $siteCurrency) {
            $amount = round(Currency::convert($amount, $siteCurrency, 'BGN'), 2);
        }

        /** @var PurchaseRequest $request */
        $request = $this->omnipay->purchase([
            'amount' => $amount,
            'transactionId' => $payment->id,
            'returnUrl' => route('site.payment.return', $payment->id),
            'cancelUrl' => route('site.payment.cancel', $payment->id),
        ]);

        /** @var PurchaseResponse $response */
        $response = $request->send();

        PaymentLogs::siteToProvider($payment, $request->getData(), $response->getData());

        if ($response->isRedirect()) {
            $payment->provider_reference_id = $request->getTransactionId();
            $payment->sync();
        } else {
            throw new Error($response->getMessage());
        }

        return [
            'payment' => [
                'payment_id' => $payment->id,
                'site_reference_id' => $payment->site_reference_id,
                'provider_reference_id' => $payment->provider_reference_id,
                'status' => $payment->status
            ],
            'status' => 'requested',
            'action' => [
                'type' => 'redirect',
                'url' => $response->getRedirectUrl(),
            ]
        ];
    }

    /**
     * @param Payments $payment
     * @param array $data
     */
    public function completePurchase(Payments $payment, array $data = []): void
    {
        if (Payments::STATUS_REQUESTED == $payment->status) {
            $payment->status = Payments::STATUS_PENDING;
            $payment->sync();
        }
    }

    /**
     * @param \App\Models\Gateway\Payments $payment
     * @param array $data
     * @return mixed
     */
    public function capturePurchase(Payments $payment, array $data = []): \Illuminate\Http\Response
    {
        /** @var NotifyRequest $request */
        $request = $this->omnipay->acceptNotification();

        /** @var NotifyResponse $response */
        $response = $request->send();

        PaymentLogs::providerToSite($payment, $request->getContent(), $response->getData(), 'ipn');

        $payment->provider_data = $response->getData();
        $payment->status = $response->getTransactionStatus();
        $payment->sync();

        return new Response($response->getData()['notify_text']);
    }

    /**
     * @param $method
     * @param $parameters
     * @return mixed
     * @throws Error
     */
    #[\Override]
    public function __call($method, $parameters)
    {
        $callable = [$this->omnipay, $method];

        if (method_exists($this->omnipay, $method)) {
            return call_user_func_array($callable, $parameters);
        }

        //        throw new \BadMethodCallException("Method [$method] is not supported by the gateway [{$this->omnipay->getName()}].");
        throw new Error(sprintf('Method [%s] is not supported by the gateway [%s].', $method, $this->omnipay->getName()));
    }

}
