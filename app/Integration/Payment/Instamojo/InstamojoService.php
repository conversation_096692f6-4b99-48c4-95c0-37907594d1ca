<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 4.5.2017 г.
 * Time: 17:22
 */

namespace App\Integration\Payment\Instamojo;

use App\Exceptions\Error;
use App\Exceptions\PaymentBadRequest;
use App\Integration\Payment\AbstractService;
use App\Models\Gateway\PaymentLogs;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\Payments;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Omnipay\Common\GatewayFactory;
use Omnipay\Instamojo\Gateway;
use Omnipay\Instamojo\Message\CompletePurchaseRequest;
use Omnipay\Instamojo\Message\CompletePurchaseResponse;
use Omnipay\Instamojo\Message\NotifyRequest;
use Omnipay\Instamojo\Message\NotifyResponse;
use Omnipay\Instamojo\Message\PurchaseRequest;
use Omnipay\Instamojo\Message\RefundRequest;
use Omnipay\Instamojo\Message\RefundResponse;
use Omnipay\Instamojo\Message\Response as InstamojoResponse;

/**
 * Class InstamojoService
 * @package App\Integration\Payment\Instamojo
 */
class InstamojoService extends AbstractService
{
    /** @var \Omnipay\Instamojo\Gateway */
    protected $omnipay;

    /**
     * @var
     */
    protected $config;

    /**
     * @param Request $request
     * @return Payments
     */
    public static function getPaymentFromRequest(Request $request)
    {
        $provider_reference_id = $request->input('payment_request_id');
        if (empty($provider_reference_id)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        $payment = Payments::findByProviderReferenceId('instamojo', $provider_reference_id);
        if (!$payment && $provider_reference_id = $request->input('payment_id')) {
            $payment = Payments::findByProviderReferenceId('instamojo', $provider_reference_id);
        }

        if (!$payment) {
            throw new ModelNotFoundException();
        }

        return $payment;
    }

    /**
     * Service constructor.
     */
    public function __construct()
    {
        $factory = new GatewayFactory();
        $this->omnipay = $factory->create('Instamojo');
        $this->loadConfig();
    }

    /**
     *
     */
    protected function loadConfig()
    {
        $config = [
            'testMode' => true,
            'apiKey' => '',
            'authToken' => '',
            'salt' => ''
        ];

        $provider = PaymentProviderConfiguration::findByProvider('instamojo');

        if (!empty($provider->configuration)) {
            $prefix = 'live' == $provider->configuration['mode'] ? 'live_' : 'test_';
            $config['testMode'] = 'live' != $provider->configuration['mode'];
            $config['apiKey'] = $provider->configuration[$prefix . 'api_key'];
            $config['authToken'] = $provider->configuration[$prefix . 'auth_token'];
            $config['salt'] = $provider->configuration[$prefix . 'salt'];
            $config['enable_iframe'] = $provider->configuration['enable_iframe'] ?? false;
        }

        $reflection = new \ReflectionClass(Gateway::class);

        foreach ($config as $optionName => $value) {
            $method = 'set' . ucfirst($optionName);

            if ($reflection->hasMethod($method)) {
                $this->omnipay->{$method}($value);
            }
        }

        $this->config = $config;
    }

    /**
     * @param Payments $payment
     * @return array
     * @throws Error
     * @throws \SmartyException
     */
    public function purchase(Payments $payment): array
    {
        //digital without billing address
        $hide_billing_address = is_null($payment->address);

        /** @var PurchaseRequest $request */
        $request = $this->omnipay->purchase([
            'amount' => round($payment->amount / 100, 2),
            'purpose' => 'Payment #' . $payment->id,
            'email' => $payment->client_email,
            'buyer_name' => $hide_billing_address ? null : $payment->address['first_name'] . ' ' . $payment->address['last_name'],
            'redirect_url' => route('payments.return', 'instamojo'),
            'webhook' => route('payments.webhook', 'instamojo'),
        ]);

        /** @var InstamojoResponse $response */
        $response = $request->send();

        PaymentLogs::siteToProvider($payment, $request->getData(), $response->getData());

        if ($response->isSuccessful() && $response->isRedirect()) {
            $payment->provider_reference_id = $response->getTransactionReference();
            $payment->provider_data = $response->getData();
            $payment->status = $response->getTransactionStatus();
            $payment->sync();
        } else {
            throw new Error($response->getMessage());
        }

        return [
            'payment' => [
                'payment_id' => $payment->id,
                'site_reference_id' => $payment->site_reference_id,
            ],
            'status' => $payment->status,
            'action' => [
                'type' => 'redirect',
                'url' => $response->getRedirectUrl(),
            ]
        ];
    }

    /**
     * @param Payments $payment
     * @param array $data
     * @throws Error
     */
    public function completePurchase(Payments $payment, array $data = []): void
    {
        /** @var CompletePurchaseRequest $request */
        $request = $this->omnipay->completePurchase(['transactionReference' => $data['payment_id']]);

        /** @var CompletePurchaseResponse $response */
        $response = $request->send();

        PaymentLogs::providerToSite($payment, $request->getData(), $response->getData(), 'ipn');

        if ($response->isSuccessful()) {
            $payment->provider_reference_id = $response->getTransactionReference();
            $payment->provider_data = $response->getData();
            $payment->status = $response->getTransactionStatus();
            $payment->sync();
        } else {
            throw new Error($response->getMessage());
        }
    }

    /**
     * @param Payments $payment
     */
    public function sync(Payments $payment): void
    {
        $data = [
            'transactionReference' => $payment->provider_reference_id
        ];

        if (str_starts_with($payment->provider_reference_id, 'MOJO')) {
            $request = $this->omnipay->completePurchase($data);
        } else {
            $request = $this->omnipay->fetchPaymentRequest($data);
        }

        /** @var InstamojoResponse $response */
        $response = $request->send();

        PaymentLogs::siteToProvider($payment, $request->getData(), $response->getData(), 'sync');

        if ($response->isSuccessful()) {
            $payment->status = $response->getTransactionStatus();
            $payment->provider_reference_id = $response->getTransactionReference();
            $payment->provider_data = $response->getData();
            $payment->sync();
        }
    }

    /**
     * @param Payments $payment
     * @throws Error
     */
    public function refund(Payments $payment): void
    {
        $data = [
            'transactionReference' => $payment->provider_reference_id,
//            'amount' => round($payment->amount / 100, 2)
        ];

        /** @var RefundRequest $request */
        $request = $this->omnipay->refund($data);

        /** @var RefundResponse $response */
        $response = $request->send();

        PaymentLogs::siteToProvider($payment, $request->getData(), $response->getData(), 'refund');

        if ($response->isSuccessful()) {
            $payment->status = $response->getTransactionStatus();
            $payment->provider_reference_id = $response->getTransactionReference();
            $payment->provider_data = $response->getData();
            $payment->sync();
        } else {
            throw new Error($response->getMessage());
        }
    }

    /**
     * @param \App\Models\Gateway\Payments $payment
     * @param array $data
     * @return mixed
     */
    public function capturePurchase(Payments $payment, array $data = []): void
    {
        /** @var NotifyRequest $request */
        $request = $this->omnipay->acceptNotification();

        /** @var NotifyResponse $response */
        $response = $request->send();

        PaymentLogs::providerToSite($payment, $request->getData(), [], 'ipn');

        $payment->provider_reference_id = $response->getTransactionReference();
        $payment->provider_data = $response->getData();
        $payment->status = $response->getTransactionStatus();
        $payment->sync();
    }

}
