<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 13.3.2017 г.
 * Time: 12:57
 */

namespace App\Integration\Payment\EpayOneTouch;

use App\Contracts\ValidatorContract;

class ConfigurationValidator implements ValidatorContract
{
    public function rules(): array
    {
        return [
            'configuration.app_id' => 'required',
            'configuration.secret' => 'required',
            'configuration.kin' => 'required',
        ];
    }

    public function messages(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function attributes(): array
    {
        return [
            'configuration.app_id' =>  "APP ID is required",
            'configuration.secret' => "Secret is required",
            'configuration.kin' => "KIN is required",
        ];
    }
}
