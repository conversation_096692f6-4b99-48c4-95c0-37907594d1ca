{include file="../../views/provider_dependency.tpl"}

<form action="{route('admin.payment_providers.save', $provider->provider)}" id="editProviderForm" role="form" class="ajaxForm" data-multipart="true">
    {include file="../../views/title_logo.tpl"}

    <div class="box">
        <div class="box-title">
            <div class="box-title-text">
                <h5>{t}global.settings{/t}</h5>
            </div>
        </div>

        <div class="box-section">
            <div class="row form-group">
                <div class="col-xs-6">
                    <div class="form-group padding-top-0">
                        <label class="control-label">{t}payment_provider.label.skrill.email{/t}</label>
                        
                        <input type="text" name="configuration[email]" value="{$provider.configuration.email|default}" class="form-control" id="category_name" data-autofocus="" placeholder="{t}payment_provider.ph.skrill.email{/t}" />

                        <span class="help-block">{t}payment_provider.help.skrill.email{/t}</span>
                    </div>
                </div>

                <div class="col-xs-6">
                    <div class="form-group padding-top-0">
                        <label class="control-label">{t}payment_provider.label.skrill.merchant_id{/t}</label>
                        
                        <input type="text" name="configuration[merchant_id]" value="{$provider.configuration.merchant_id|default}" class="form-control" id="category_name" data-autofocus="" placeholder="{t}payment_provider.ph.skrill.merchant_id{/t}" />
                        
                        <span class="help-block">{t}payment_provider.help.skrill.merchant_id{/t}</span>
                    </div>
                </div>
            </div>

            <div class="row form-group">
                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.skrill.api_password{/t}</label>

                    <input type="text" name="configuration[api_password]" value="{$provider.configuration.api_password|default}" class="form-control" placeholder="{t}payment_provider.ph.skrill.api_password{/t}" />

                    <span class="help-block">{t}payment_provider.help.skrill.api_password{/t}</span>
                </div>

                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.skrill.signature_secret{/t}</label>

                    <input type="text" name="configuration[signature_secret]" value="{$provider.configuration.signature_secret|default}" class="form-control" placeholder="{t}payment_provider.ph.skrill.signature_secret{/t}" />

                    <span class="help-block">{t}payment_provider.help.skrill.signature_secret{/t}</span>
                </div>
            </div>

            <div class="row form-group">
                <div class="col-xs-6">
                    <label class="control-label">{t}payment_provider.label.skrill.signature_hash_algorithm{/t}</label>

                    <select name="configuration[signature_hash_algorithm]" class="form-control select2me" data-placeholder="{t}payment_provider.ph.skrill.signature_hash_algorithm{/t}">
                        <option value="md5"{if $provider.configuration.signature_hash_algorithm|default == 'md5'} selected="selected"{/if}>{t}payment_provider.option.skrill.md5{/t}</option>
                        <option value="sha256"{if $provider.configuration.signature_hash_algorithm|default == 'sha256'} selected="selected"{/if}>{t}payment_provider.option.skrill.sha256{/t}</option>
                    </select>

                    <span class="help-block">{t}payment_provider.help.skrill.signature_hash_algorithm{/t}</span>
                </div>
            </div>
        </div>
    </div>

    {include file="../../views/from-to.tpl"}

    {include file="../../views/discount.tpl"}
</form>