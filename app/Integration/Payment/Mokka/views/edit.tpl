{include file="../../views/provider_dependency.tpl"}


<form action="{route('admin.payment_providers.save', $provider->provider)}" id="editProviderForm" role="form"
      class="ajaxForm" data-multipart="true">
    <div>
        {include file="../../views/title_logo.tpl"}

        <div class="box">
            <div class="box-title">
                <div class="box-title-text">
                    <h5>{t}payment_provider.label.stripe.mode{/t}</h5>
                </div>
            </div>

            <div class="box-section">
                <div class="row form-group">
                    <div class="col-xs-12">
                        <span class="help-block">{t}payment_provider.help.stripe.mode{/t}</span>

                        <div class="form-control-box">
                            <div class="form-control-box-inner">
                                <input id="providerMode" name="configuration[mode]"
                                       type="checkbox"{if $provider.configuration.mode|default == 'live'} checked="checked"{/if}
                                       class="switch" value="live" data-off="{t}payment_provider.switch.live{/t}"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="box">
            <div class="box-title">
                <div class="box-title-text">
                    <h5>{t}global.settings{/t}</h5>
                </div>
            </div>
            <div class="box-section" id="real">
                <div class="row form-group">
                    <div class="col-xs-12 mb-10">
                        <label class="control-label">{t}payment_provider.label.mokka.store_id{/t}</label>
                        <input type="text" name="configuration[store_id]"
                               value="{$provider.configuration.store_id|default}" class="form-control"
                               id="store_id" data-autofocus=""
                        />
                    </div>
                    <div class="col-xs-12 mb-10">
                        <label class="control-label">{t}payment_provider.label.mokka.store_key{/t}</label>
                        <input type="text" name="configuration[store_key]"
                               value="{$provider.configuration.store_key|default}" class="form-control"
                               id="store_key" data-autofocus=""
                        />
                    </div>
                    <div class="col-xs-12 mb-10">
                        <label class="control-label">{t}payment_provider.label.mokka.select_country{/t}</label>
                        <select class="form-control select2me" name="configuration[store_endpoint]"  id="store_endpoint" data-no-input="true">
                            <option value="bg" {if $provider.configuration.store_endpoint|default:'bg' == 'bg'}selected="selected"{/if}>{t}payment_provider.label.mokka.country.bg{/t}</option>
                            <option value="ro" {if $provider.configuration.store_endpoint|default:'bg' == 'ro'}selected="selected"{/if}>{t}payment_provider.label.mokka.country.ro{/t}</option>
                            <option value="pl" {if $provider.configuration.store_endpoint|default:'bg' == 'pl'}selected="selected"{/if}>{t}payment_provider.label.mokka.country.pl{/t}</option>
                        </select>
                    </div>
                </div>
                <div class="row form-group">
                    {*                <div class="col-xs-12">*}
                    {*                    <div class="form-control-box">*}
                    {*                        <div class="form-control-box-inner">*}
                    {*                            <input name="configuration[banner]"*}
                    {*                                   type="checkbox"{if $provider.configuration.banner|default} checked="checked"{/if}*}
                    {*                                   class="switch" value="1" data-off="{t}payment_provider.label.mokka.banner{/t}"/>*}
                    {*                        </div>*}
                    {*                    </div>*}
                    {*                </div>*}
                </div>
            </div>
            <div class="box-section" id="test">
                <div class="row form-group">
                    <div class="col-xs-12 mb-10">
                        <label class="control-label">{t}payment_provider.label.mokka.store_id{/t} {t}payment_provider.label.test{/t}</label>
                        <input type="text" name="configuration[store_test_id]"
                               value="{$provider.configuration.store_test_id|default}" class="form-control"
                               id="store_test_id" data-autofocus=""
                        />
                    </div>
                    <div class="col-xs-12 mb-10">
                        <label class="control-label">{t}payment_provider.label.mokka.store_key{/t} {t}payment_provider.label.test{/t}</label>
                        <input type="text" name="configuration[store_test_key]"
                               value="{$provider.configuration.store_test_key|default}" class="form-control"
                               id="store_test_key" data-autofocus=""
                        />
                    </div>
                    <div class="col-xs-12 mb-10">
                        <label class="control-label">{t}payment_provider.label.mokka.select_country{/t}</label>
                        <select class="form-control select2me" name="configuration[store_test_endpoint]"  id="store_test_endpoint" data-no-input="true">
                            <option value="bg" {if $provider.configuration.store_endpoint|default:'bg' == 'bg'}selected="selected"{/if}>{t}payment_provider.label.mokka.country.bg{/t}</option>
                            <option value="ro" {if $provider.configuration.store_endpoint|default:'bg' == 'ro'}selected="selected"{/if}>{t}payment_provider.label.mokka.country.ro{/t}</option>
                            <option value="pl" {if $provider.configuration.store_endpoint|default:'bg' == 'pl'}selected="selected"{/if}>{t}payment_provider.label.mokka.country.pl{/t}</option>
                        </select>
                    </div>
                </div>
                <div class="row form-group">
                    {*                <div class="col-xs-12">*}
                    {*                    <div class="form-control-box">*}
                    {*                        <div class="form-control-box-inner">*}
                    {*                            <input name="configuration[banner]"*}
                    {*                                   type="checkbox"{if $provider.configuration.banner|default} checked="checked"{/if}*}
                    {*                                   class="switch" value="1" data-off="{t}payment_provider.label.mokka.banner{/t}"/>*}
                    {*                        </div>*}
                    {*                    </div>*}
                    {*                </div>*}
                </div>
            </div>
        </div>

        {include file="../../views/from-to.tpl"}

        {include file="../../views/discount.tpl"}
    </div>
</form>
<style>
    .text-black {
        color: #000000;
    }

    .mokka-color {
        color: #ff5a1e;
    }

    .mb-20 {
        margin-bottom: 20px;
    }

    .mb-10 {
        margin-bottom: 10px;
    }

    .disabledContent {
        pointer-events: none;
        opacity: 0.4;
    }
</style>
{capture append="js"}
    <script type="text/javascript">
        $(document).ready(function () {
            if($('#providerMode').is(':checked')){
                $('#test').hide();
            } else {
                $('#real').hide();
            }
            $('#providerMode').on('change', function () {
                if ($(this).is(':checked')) {
                    $('#real').show();
                    $('#test').hide();
                } else {
                    $('#real').hide();
                    $('#test').show();
                }
            }).trigger('change');
        });
    </script>
{/capture}