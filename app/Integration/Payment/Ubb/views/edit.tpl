{include file="../../views/provider_dependency.tpl"}

<form action="{route('admin.payment_providers.save', $provider->provider)}" id="editProviderForm" role="form"
      class="ajaxForm" data-multipart="true">

    {include file="../../views/title_logo.tpl"}

    <div class="box">
        <div class="box-title">
            <div class="box-title-text">
                <h5>{t}global.settings{/t}</h5>
            </div>
        </div>

        <div class="box-section">
            <div class="row form-group">
                <div class="col-xs-12">
                    <label class="control-label">{t}payment_provider.label.ubb.resource_file{/t}</label>

                    <div class="fileupload fileupload-new input-group" data-provides="fileupload">
                        <span class="form-control" data-trigger="fileupload">
                           <span class="uneditable-input">
                               <i class="fal fa-file fileupload-exists"></i>
                               <span class="fileupload-preview">{$provider.configuration.resource_name|default}</span>
                           </span>
                        </span>

                        <span class="input-group-addon btn btn-white btn-file input-group-addon-right">
                           <span class="fileupload-new"><i
                                       class="fal fa-cloud-upload fa-lg"></i> {t}global.action.select_file{/t}</span>
                           <span class="fileupload-exists"><i class="fal fa-cloud-upload fa-lg"></i></span>
                           <input type="file" name="resource" class="default"/>
                        </span>

                        <a href="#" class="btn-revert fileupload-exists" data-dismiss="fileupload">
                            <i class="fal fa-times-circle fa-lg cc-grey"></i>
                        </a>
                    </div>

                    <span class="help-block">{t}payment_provider.help.ubb.resource_file{/t}</span>
                </div>
            </div>

            <div class="row form-group">
                <div class="col-xs-12">
                    <label class="control-label">{t}payment_provider.label.ubb.payment_type{/t}</label>

                    <select name="configuration[action]" class="form-control select2me" data-no-input="true">
                        <option value="1" {if $provider.configuration.action|default == 1} selected="selected"{/if}>
                            {t}payment_provider.label.ubb.payment_type_1{/t}
                        </option>
                        <option value="4" {if $provider.configuration.action|default == 4} selected="selected"{/if}>
                            {t}payment_provider.label.ubb.payment_type_4{/t}
                        </option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    {include file="../../views/from-to.tpl"}

    {include file="../../views/discount.tpl"}
</form>
