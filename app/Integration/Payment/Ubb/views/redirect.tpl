<html>
<head>
</head>
<body OnLoad="OnLoadEvent();">
<form action="{$url}" method="post" name="form1" autocomplete="off">
    <input type="hidden" name="PaymentID" value="{$paymentId}"  />
</form>
<script language="JavaScript">

    function OnLoadEvent() {
        document.form1.submit();
        timVar = setTimeout("procTimeout()",300000);
    }

    function procTimeout() {
        location = 'http://enter.a.timeout.url.here';
    }

    //
    // disable page duplication -> CTRL-N key
    //
    if (document.all) {
        document.onkeydown = function () {
            if (event.ctrlKey && event.keyCode == 78) {
                return false;
            }
        }
    }
</script>
</body>
</html>