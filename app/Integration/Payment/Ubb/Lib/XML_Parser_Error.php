<?php

declare(strict_types=1);

namespace App\Integration\Payment\Ubb\Lib;

/**
 * error class, replaces PEAR_Error
 *
 * An instance of this class will be returned
 * if an error occurs inside XML_Parser.
 *
 * There are three advantages over using the standard PEAR_Error:
 * - All messages will be prefixed
 * - check for XML_Parser error, using is_a( $error, 'XML_Parser_Error' )
 * - messages can be generated from the xml_parser resource
 *
 * @package XML_Parser
 * @access  public
 * @see     PEAR_Error
 */
class XML_Parser_Error extends PEAR_Error
{
    /**
     * prefix for all messages
     *
     * @var      string
     */
    public $error_message_prefix = 'XML_Parser: ';

    /**
     * construct a new error instance
     *
     * You may either pass a message or an xml_parser resource as first
     * parameter. If a resource has been passed, the last error that
     * happened will be retrieved and returned.
     *
     * @access   public
     * @param    string|resource message or parser resource
     * @param    integer             error code
     * @param    integer             error handling
     * @param    integer             error level
     * @param mixed $msgorparser
     * @param mixed $code
     * @param mixed $mode
     * @param mixed $level
     */
    public function __construct($msgorparser = 'unknown error', $code = 0, $mode = PEAR_ERROR_RETURN, $level = E_USER_NOTICE)
    {
        if (is_resource($msgorparser)) {
            $code = xml_get_error_code($msgorparser);
            $msgorparser = sprintf(
                '%s at XML input line %d:%d',
                xml_error_string($code),
                xml_get_current_line_number($msgorparser),
                xml_get_current_column_number($msgorparser)
            );
        }

        $this->PEAR_Error($msgorparser, $code, $mode, $level);
    }
}
