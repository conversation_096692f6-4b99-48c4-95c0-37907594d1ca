<?php

declare(strict_types=1);

/*
 * Copyright (c) 2013 - 2022 MasterCard International Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are
 * permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice, this list of
 * conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright notice, this list of
 * conditions and the following disclaimer in the documentation and/or other materials
 * provided with the distribution.
 * Neither the name of the MasterCard International Incorporated nor the names of its
 * contributors may be used to endorse or promote products derived from this software
 * without specific prior written permission.
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY
 * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED
 * TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER
 * IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 */
class Simplify_InvoiceItem extends Simplify_Object
{
    /**
     * Creates an Simplify_InvoiceItem object
     * @param     array $hash a map of parameters; valid keys are:<dl style="padding-left:10px;">
     *     <dt><tt>amount</tt></dt>    <dd>Amount of the invoice item in the smallest unit of your currency. Example: 100 = $1.00 <strong>required </strong></dd>
     *     <dt><tt>description</tt></dt>    <dd>Individual items of an invoice [max length: 1024] </dd>
     *     <dt><tt>invoice</tt></dt>    <dd>The ID of the invoice this item belongs to. </dd>
     *     <dt><tt>product</tt></dt>    <dd>Product ID this item relates to. </dd>
     *     <dt><tt>quantity</tt></dt>    <dd>Quantity of the item.  This total amount of the invoice item is the amount * quantity. [min value: 1, max value: 999999, default: 1] </dd>
     *     <dt><tt>reference</tt></dt>    <dd>User defined reference field. [max length: 255] </dd>
     *     <dt><tt>tax</tt></dt>    <dd>The tax ID of the tax charge in the invoice item. </dd></dl>
     * @param     $authentication -  information used for the API call.  If no value is passed the global keys Simplify::public_key and Simplify::private_key are used.  <i>For backwards compatibility the public and private keys may be passed instead of the authentication object.<i/>
     * @return    InvoiceItem a InvoiceItem object.
     */
    public static function createInvoiceItem($hash, $authentication = null)
    {

        $args = func_get_args();
        $authentication = Simplify_PaymentsApi::buildAuthenticationObject($authentication, $args, 2);

        $instance = new Simplify_InvoiceItem();
        $instance->setAll($hash);

        $object = Simplify_PaymentsApi::createObject($instance, $authentication);
        return $object;
    }




    /**
     * Deletes an Simplify_InvoiceItem object.
     *
     * @param     $authentication -  information used for the API call.  If no value is passed the global keys Simplify::public_key and Simplify::private_key are used.  <i>For backwards compatibility the public and private keys may be passed instead of the authentication object.</i>
     */
    public function deleteInvoiceItem($authentication = null): bool
    {

        $args = func_get_args();
        $authentication = Simplify_PaymentsApi::buildAuthenticationObject($authentication, $args, 1);

        $obj = Simplify_PaymentsApi::deleteObject($this, $authentication);
        $this->properties = null;
        return true;
    }


    /**
     * Retrieve a Simplify_InvoiceItem object from the API
     *
     * @param     string id  the id of the InvoiceItem object to retrieve
     * @param     $authentication -  information used for the API call.  If no value is passed the global keys Simplify::public_key and Simplify::private_key are used.  <i>For backwards compatibility the public and private keys may be passed instead of the authentication object.</i>
     * @param mixed $id
     * @return    InvoiceItem a InvoiceItem object
     */
    public static function findInvoiceItem($id, $authentication = null)
    {

        $args = func_get_args();
        $authentication = Simplify_PaymentsApi::buildAuthenticationObject($authentication, $args, 2);

        $val = new Simplify_InvoiceItem();
        $val->id = $id;

        $obj = Simplify_PaymentsApi::findObject($val, $authentication);

        return $obj;
    }


    /**
     * Updates an Simplify_InvoiceItem object.
     *
     * The properties that can be updated:
     * <dl style="padding-left:10px;">
     *     <dt><tt>amount</tt></dt>    <dd>Amount of the invoice item in the smallest unit of your currency. Example: 100 = $1.00 [min value: 1] </dd>
     *     <dt><tt>description</tt></dt>    <dd>Individual items of an invoice </dd>
     *     <dt><tt>quantity</tt></dt>    <dd>Quantity of the item.  This total amount of the invoice item is the amount * quantity. [min value: 1, max value: 999999] </dd>
     *     <dt><tt>reference</tt></dt>    <dd>User defined reference field. </dd>
     *     <dt><tt>tax</tt></dt>    <dd>The tax ID of the tax charge in the invoice item. </dd></dl>
     * @param     $authentication -  information used for the API call.  If no value is passed the global keys Simplify::public_key and Simplify::private_key are used.  <i>For backwards compatibility the public and private keys may be passed instead of the authentication object.</i>
     * @return    InvoiceItem a InvoiceItem object.
     */
    public function updateInvoiceItem($authentication = null)
    {

        $args = func_get_args();
        $authentication = Simplify_PaymentsApi::buildAuthenticationObject($authentication, $args, 1);

        $object = Simplify_PaymentsApi::updateObject($this, $authentication);
        return $object;
    }

    /**
     * @ignore
     */
    public function getClazz(): string
    {
        return "InvoiceItem";
    }
}
