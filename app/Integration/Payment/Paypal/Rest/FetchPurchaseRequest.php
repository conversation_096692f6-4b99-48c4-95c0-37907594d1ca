<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 10.4.2017 г.
 * Time: 15:58
 */

namespace App\Integration\Payment\Paypal\Rest;

use Omnipay\PayPal\Message\RestFetchPurchaseRequest;

/**
 * Class FetchPurchaseRequest
 * @package App\Integration\Payment\Paypal\Rest
 */
class FetchPurchaseRequest extends RestFetchPurchaseRequest
{
    /**
     * @param $data
     * @param $statusCode
     * @return PurchaseResponse
     */
    #[\Override]
    protected function createResponse($data, $statusCode)
    {
        return $this->response = new PurchaseResponse($this, $data, $statusCode);
    }

}
