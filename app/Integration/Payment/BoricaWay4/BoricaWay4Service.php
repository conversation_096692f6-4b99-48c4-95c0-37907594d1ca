<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 13.4.2017 г.
 * Time: 14:18
 */

namespace App\Integration\Payment\BoricaWay4;


use App\Exceptions\Error;
use App\Exceptions\PaymentBadRequest;
use App\Integration\Payment\AbstractService;
use App\Integration\Payment\BankCard;
use App\Integration\Payment\SaveCard;
use App\Models\Customer\Customer;
use App\Models\Gateway\PaymentLogs;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\Payments;
use App\Models\Router\PaymentsLog;
use Exception;
use Throwable;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Omnipay\Borica\Gateway;
use Omnipay\Borica\Message\AbstractRequest;
use Omnipay\Borica\Message\FetchTransactionResponse;
use Omnipay\Common\Exception\InvalidRequestException;
use Omnipay\Common\Exception\InvalidResponseException;
use Omnipay\Common\GatewayFactory;
use ReflectionClass;


/**
 * @method  capturePurchase(Payments $payment, array $data = array())
 */
class BoricaWay4Service extends AbstractService
{
    use SaveCard;

    public const KEY = 'borica_way4';

    /**
     * @var Gateway
     */
    protected $omnipay;

    protected $merchantId;

    /**
     * @param Request $request
     * @return Payments
     * @throws Exception
     */
    public static function getPaymentFromRequest(Request $request)
    {
        $reference = $request->input('NONCE');
        if (empty($reference)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        /** @var Payments $payment */
        $payment = Payments::where('provider_reference_id', $reference)->first();
        if (empty($payment)) {
            throw new PaymentBadRequest('Bad Request', $request);
        }

        return $payment;
    }

    /**
     *
     */
    public function loadConfig(): void
    {
        $factory = new GatewayFactory();
        $curlClient = new \Symfony\Component\HttpClient\CurlHttpClient([
            //            'timeout' => 5, // maximum number of seconds that the request should wait for a response
            'max_duration' => 10, // maximum number of seconds that the request can take, including the time to establish the connection, send the request, and receive the response
        ]);
        $httpClient = new \Symfony\Component\HttpClient\HttplugClient($curlClient);
        $client = new \Omnipay\Common\Http\Client($httpClient);
        //        dd($client);

        //        $this->omnipay = $factory->create('Borica');
        $this->omnipay = $factory->create('Borica', $client);

        $this->config = [];
        $provider = PaymentProviderConfiguration::findByProvider(self::KEY);

        if (!empty($provider->configuration)) {
            if ($provider->configuration['terminal_id'] == 'V1800001') { // Borica test terminal
                $this->config['terminalId'] = 'V1800001';
                $this->config['privateKey'] = file_get_contents(__DIR__ . '/docs/V1800001-key.pem');
                $this->config['certificate'] = null;
                $this->config['gatewayCertificate'] = null;
                $this->config['testMode'] = true;
            } else {
                $postfix = 'test' == $provider->configuration['mode'] ? '_test' : '_real';
                $this->config['testMode'] = 'test' == $provider->configuration['mode'];
                $this->config['terminalId'] = $provider->configuration['terminal_id'];
                $this->config['privateKey'] = $provider->configuration['private_key' . $postfix];
                $this->config['certificate'] = $provider->configuration['certificate' . $postfix];
                if ('test' == $provider->configuration['mode']) {
                    $this->config['gatewayCertificate'] = file_get_contents(__DIR__ . '/cer/MPI_OW_APGW_D_2026.cer');
                } else {
                    $this->config['gatewayCertificate'] = file_get_contents(__DIR__ . '/cer/MPI_OW_APGW_P_2026.cer');
                }
            }

            $this->config['currency'] = site('currency');
            $this->config['mpay_enabled'] = $provider->configuration['mpay_enabled'] ?? null;
            $this->merchantId = $provider->configuration['merchant_id'] ?? null;
            $this->saveCard = 'yes' == ($provider->configuration['save_card'] ?? null);
            if (in_array($provider->configuration['authorize_payment'] ?? null, self::AUTHORIZE_TYPES)) {
                $this->authorizePayment = $provider->configuration['authorize_payment'];
            }
        }

        $reflection = new ReflectionClass(Gateway::class);

        foreach ($this->config as $optionName => $value) {
            $method = 'set' . ucfirst($optionName);

            if ($reflection->hasMethod($method)) {
                $this->omnipay->{$method}($value);
            }
        }

        if (($provider->configuration['security'] ?? '') == 'MAC_GENERAL') {
            $this->omnipay->useMacGeneral();
        }

        // Disable curl call for transaction status on return url
        $this->omnipay->setValidateStatusData(false);
    }

    /**
     * @param Payments $payment
     * @return array
     * @throws Exception
     */
    public function purchase(Payments $payment): array
    {
        $this->loadConfig();

        if ($this->isAuthorizePayment()) {
            $payment->update([
                'authorize_amount' => $payment->amount,
            ]);
        }

        $returnUrl = route('payments.return', self::KEY);
        $order = $payment->orderPayment->order;

        $order_id = (setting('order_id_display') == 'increment_hash') ?
            $order->increment_hash :
            $order->getKey();

        $mInfo = json_encode([
            'email' => $order->customer_email,
            'cardholderName' => Str::ascii($order->customer_full_name),
        ]);

        $requestData = [
            'amount' => $payment->amount / 100,
            'currency' => $payment->currency,
            //                'order' => date('His'),
            'order' => substr((string) $payment->getKey(), -6),
            'description' => "Order #" . $order_id,
            'merchant' => $this->merchantId ?: site('site_id'),
            'merchantUrl' => site()->getSiteUrl(),
            'merchantName' => site('host'),
            'returnUrl' => $returnUrl,
            'orderId' => \Carbon\Carbon::now()->format('md') . '|' . $payment->orderPayment->order_id,
            'M_INFO' => base64_encode($mInfo),
            // 'MPAY' => 'G', // Y - only gpay and apple pay, N - only card, G - direct gpay, A - direct apple pay
        ];

        if ($mpay = session()->get('mpay')) {
            $requestData['MPAY'] = $mpay;
        }

        if ($this->saveCard) {
            $customer = $payment->orderPayment->order->customer;
            if ($card = $this->getCustomerCard($customer)) {
                return $this->autoPurchase($payment, $card->card, $requestData);
            }

            $requestData['MERCH_TRAN_STATE'] = 'S';
        }

        if ($this->isAuthorizePayment()) {
            $request = $this->omnipay->authorize($requestData);
        } else {
            $request = $this->omnipay->purchase($requestData);
        }

        //        $response = $request->send();
        $response = $this->retryIdle(fn() => $request->send(), $request);

        PaymentLogs::siteToProvider($payment, $request->getData(), $response->getData());

        $payment->provider_reference_id = $response->getTransactionId();
        $payment->provider_data = $this->removeSignature($response->getData());
        $payment->sync();

        $redirectHtml = $response->getRedirectResponse()->getContent();

        //        return response($redirectHtml);
        return [
            'payment' => [
                'payment_id' => $payment->id,
            ],
            'status' => Payments::STATUS_REQUESTED,
            'htmlRedirectForm' => $redirectHtml,
        ];
    }

    /**
     * @param Payments $payment
     * @param $cardData
     * @param $requestData
     * @return array
     * @throws Exception
     */
    public function autoPurchase(Payments $payment, $cardData, $requestData): array
    {
        $requestData = array_merge($requestData, [
            'MERCH_TRAN_STATE' => 'M',
            'MERCH_TOKEN_ID' => $cardData->token,
            'MERCH_RN_ID' => $cardData->MERCH_RN_ID,
        ]);

        if ($this->isAuthorizePayment()) {
            $request = $this->omnipay->authorizeByToken($requestData);
        } else {
            $request = $this->omnipay->payByToken($requestData);
        }

        try {
            //            $response = $request->send();
            $response = $this->retryIdle(fn() => $request->send(), $request);
            //            dd($response->getData());
            PaymentLogs::siteToProvider($payment, $request->getData(), $response->getData());

            $responseData = $this->removeSignature(array_merge($request->getData(), $response->getData()));
            $payment->status = $this->getTransactionState($response);
            $payment->provider_reference_id = $response->getTransactionId();
            $payment->provider_data = $responseData;
            $payment->sync();
            if (!in_array($payment->status, [Payments::STATUS_PENDING, Payments::STATUS_AUTHORIZED, Payments::STATUS_COMPLETED])) {
                throw new Error($response->getMessage());
            }
        } catch (\Throwable $throwable) {
            if (!$payment->provider_reference_id) {
                $data = !empty($response) ? $response->getData() : (!empty($request) ? $request->getData() : []);
                if (!empty($data['NONCE'])) {
                    $payment->provider_reference_id = $data['NONCE'];
                    $payment->provider_data = $this->removeSignature($data);
                    $payment->sync();
                }
            }

            $errorData = $this->logErrorData(
                $payment->getKey() . ': Checkout Payments::autoPurchase-way4',
                $throwable,
                $request,
                !empty($response) ? $response->getData() : []
            );
            $this->sendWebhookLog($errorData);
            throw new Error($throwable->getMessage());
        }

        return [
            'action' => false,
            'status' => $payment->status,
            'payment' => [
                'payment_id' => $payment->id,
            ],
        ];
    }

    /**
     * @param Payments $payment
     * @param array $data
     * @throws Exception
     */
    public function completePurchase(Payments $payment, array $data = []): void
    {
        // Do not process completed payments
        if ($payment->status == Payments::STATUS_COMPLETED) {
            return;
        }

        $this->loadConfig();

        $data = array_merge((array) $payment->provider_data, $data);
        if ($this->isAuthorizePayment()) {
            $request = $this->omnipay->completeAuthorize($data);
        } else {
            $request = $this->omnipay->completePurchase($data);
        }

        try {
            $response = $request->send();
            PaymentLogs::providerToSite($payment, $request->getGatewayData(), $response->getData(), 'return');
        } catch (Exception $exception) {
            $errorData = $this->logErrorData(
                $payment->getKey() . ': Payments return::completePurchase-way4',
                $exception,
                $request,
                !empty($response) ? $response->getData() : []
            );
            $this->sendWebhookLog($errorData);
            throw $exception;
        }

        $responseData = $this->removeSignature($response->getData());

        $payment->status = $this->getTransactionState($response);
        $payment->provider_data = $responseData;
        $payment->sync();

        if ($payment->status == ($this->isAuthorizePayment() ? Payments::STATUS_AUTHORIZED : Payments::STATUS_COMPLETED)) {
            $this->saveCard($payment);
        }
    }

    /**
     * @param Payments $payment
     * @throws Exception
     */
    public function captureAuthorization(Payments $payment): void
    {
        // Do not process completed payments
        if (!in_array($payment->status, [Payments::STATUS_AUTHORIZED])) {
            // Sync status just in case of previous sync failure
            if ($payment->status != $payment->orderPayment->status) {
                $payment->sync();
            }

            return;
        }

        $this->loadConfig();

        $data = array_merge((array) $payment->provider_data, [
            'AMOUNT' => moneyInput($payment->amount, $payment->order_payment->order->getCurrency(), $payment->order_payment->order->getLanguage())
        ]);
        $request = $this->omnipay->capture($data);

        try {
            //            $response = $request->send();
            $response = $this->retryIdle(fn() => $request->send(), $request);
            PaymentLogs::siteToProvider($payment, $data, $response->getData(), 'capture');
        } catch (Exception $exception) {
            $errorData = $this->logErrorData(
                $payment->getKey() . ': Checkout Payments::captureAuthorization-way4',
                $exception,
                $request,
                !empty($response) ? $response->getData() : []
            );
            $this->sendWebhookLog($errorData);
            throw $exception;
        }

        $responseData = $this->removeSignature($response->getData());

        $payment->status = $this->getTransactionState($response);
        $payment->provider_data = $responseData;
        //$payment->authorize_amount = null;
        $payment->sync();

        if ($payment->status == Payments::STATUS_COMPLETED) {
            $this->saveCard($payment);
        }
    }

    /**
     * @param Payments $payment
     * @throws Exception
     */
    public function cancelAuthorization(Payments $payment): void
    {
        // Do not process completed payments
        if (!in_array($payment->status, [Payments::STATUS_AUTHORIZED])) {
            return;
        }

        $this->loadConfig();

        $data = (array) $payment->provider_data;
        $request = $this->omnipay->cancel($data);

        try {
            //            $response = $request->send();
            $response = $this->retryIdle(fn() => $request->send(), $request);
            PaymentLogs::siteToProvider($payment, $data, $response->getData(), 'cancel');
        } catch (Exception $exception) {
            $errorData = $this->logErrorData(
                $payment->getKey() . ': Checkout Payments::cancelAuthorization-way4',
                $exception,
                $request,
                !empty($response) ? $response->getData() : []
            );
            $this->sendWebhookLog($errorData);
            throw $exception;
        }

        $responseData = $this->removeSignature($response->getData());

        $payment->status = $this->getTransactionState($response);
        $payment->provider_data = $responseData;
        //$payment->authorize_amount = null;
        $payment->sync();
    }

    /**
     * @param Payments $payment
     * @throws Exception
     */
    public function captureAutomaticAuthorization(Payments $payment): void
    {
        // Do not process completed payments
        if ($payment->status != Payments::STATUS_AUTHORIZED) {
            return;
        }

        $this->loadConfig();

        if ($this->getAuthorizePaymentType() == static::AUTHORIZE_TYPE_AUTO) {
            $this->captureAuthorization($payment);
        }
    }

    /**
     * @param Payments $payment
     * @return void
     * @throws Exception
     */
    public function sync(Payments $payment): void
    {
        $this->loadConfig();

        $providerData = (array) $payment->provider_data;
        $requestData = array_merge($providerData, [
            'transactionType' => $payment->provider_data->TRTYPE,
        ]);

        $request = $this->omnipay->fetchTransaction($requestData);

        try {
            //            $response = $request->send();
            $response = $this->retryIdle(fn() => $request->send(), $request);
        } catch (InvalidRequestException $e) {
            throw new Error($e->getMessage());
        } catch (InvalidResponseException $e) {
            PaymentLogs::siteToProvider($payment, $request->getData(), $request->getInvalidResponseData(), 'sync');
            if ($payment->status == Payments::STATUS_CANCELED) {
                return;
            }

            if ($e->getCode() == -24 && in_array($payment->status, [Payments::STATUS_INITIATED, Payments::STATUS_REQUESTED, Payments::STATUS_PENDING])) {
                $providerData['RC'] = $e->getCode();
                $providerData['RC_MSG'] = $e->getMessage();
                $payment->provider_data = $providerData;
                $payment->status = Payments::STATUS_CANCELED;
                $payment->sync();
                return;
            }

            throw new Error($e->getMessage() . ': ' . $e->getCode());
        } catch (\Throwable $e) {
            $errorData = $this->logErrorData(
                $payment->getKey() . ': Checkout Payments::sync-way4',
                $e,
                $request,
                !empty($response) ? $response->getData() : []
            );
            $this->sendWebhookLog($errorData);
        }

        if (empty($response)) {
            PaymentLogs::siteToProvider($payment, $request->getData(), [], 'sync');
            return;
        }

        PaymentLogs::siteToProvider($payment, $request->getData(), $response->getData(), 'sync');

        $paymentStatus = $payment->status;
        if ($paymentStatus != $this->getTransactionState($response)) {
            $payment->status = $this->getTransactionState($response);
            $payment->provider_data = $this->removeSignature($response->getData());
            $payment->sync();
        }
    }

    /**
     * @param Payments $payment
     * @throws Exception
     */
    public function refund(Payments $payment): void
    {
        $this->loadConfig();

        $data = (array) $payment->provider_data;
        $request = $this->omnipay->refund($data);

        try {
            //            $response = $request->send();
            $response = $this->retryIdle(fn() => $request->send(), $request);
            PaymentLogs::siteToProvider($payment, $request->getData(), $response->getData(), 'refund');
        } catch (\Throwable $throwable) {
            $errorData = $this->logErrorData(
                $payment->getKey() . ': Checkout Payments::refund-way4',
                $throwable,
                $request,
                !empty($response) ? $response->getData() : []
            );
            $this->sendWebhookLog($errorData);
            throw $throwable;
        }

        if (!$response->isSuccessful()) {
            throw new Error($response->getMessage());
        }

        $paymentStatus = $payment->status;
        if ($paymentStatus != $this->getTransactionState($response)) {
            $payment->status = $this->getTransactionState($response);
            $payment->provider_data = $this->removeSignature($response->getData());
            $payment->sync();
        }
    }

    /**
     * @param App\Models\Gateway\Payments $payment
     * @return mixed
     */
    public function ping(Payments $payment)
    {
        //        $this->loadConfig();

        $providerData = (array) $payment->provider_data;
        $requestData = array_merge($providerData, [
            'transactionType' => $payment->provider_data->TRTYPE,
        ]);

        $request = $this->omnipay->fetchTransaction($requestData);

        try {
            $response = $request->send();
        } catch (InvalidResponseException $e) {
            //            $this->sendWebhookLog([
//                'request' => $request->getData(),
//                'response' => $request->getInvalidResponseData(),
//            ]);
            return [$e->getMessage()];
        } catch (\Throwable $e) {
            $errorData = $this->logErrorData(
                $payment->getKey() . ': borica::test-ping',
                $e,
                $request,
                !empty($response) ? $response->getData() : []
            );
            $this->sendWebhookLog($errorData);
            throw $e;
        }

        return $response->getData();
    }

    /**
     * @param Closure $callback
     * @param Omnipay\Borica\Message\AbstractRequest $request
     * @param int $attempts
     * @return mixed
     */
    protected function retryIdle(\Closure $callback, AbstractRequest $request, int $attempts = 3)
    {
        for ($a = 1; $a <= $attempts; $a++) {
            try {
                $response = $callback();
            } catch (\Throwable $e) {
                if ($this->isIdleTimeoutError($e) && $a < $attempts) {
                    $errorData = $this->logErrorData(
                        'Borica attempt ' . $a,
                        $e,
                        $request,
                        []
                    );
                    $this->sendWebhookLog($errorData);
                    continue;
                }

                throw $e;
            }

            if ($a > 1) {
                $errorData = $this->logErrorData(
                    'Borica SUCCESSFUL attempt ' . $a,
                    new Error('Success'),
                    $request,
                    $response->getData()
                );
                $this->sendWebhookLog($errorData);
            }

            return $response;
        }

        return null;
    }

    /**
     * @param Throwable $e
     * @return mixed
     */
    protected function isIdleTimeoutError(\Throwable $e): bool
    {
        return strpos($e->getMessage(), 'timeout reached') || strpos($e->getMessage(), 'timed out');
    }

    /**
     * @param array $data
     * @return mixed
     */
    protected function removeSignature(array $data): array
    {
        unset($data['P_SIGN']);

        return $data;
    }

    /**
     * @param mixed $e
     * @return array{message: non-falsy-string, request: mixed, response: mixed}[]
     */
    protected function logErrorData(string $prefix, $e, AbstractRequest $request, array $response): array
    {
        if (strpos((string) $e->getMessage(), 'No errors')) {
            return [];
        }

        if ($e instanceof InvalidResponseException) {
            $response = $request->getInvalidResponseData();
        }

        $data = [
            'borica_data' => [
                'message' => $prefix . ': ' . $e->getMessage(),
                'request' => $request->getData(),
                'response' => $response,
            ],
        ];

        PaymentsLog::createFromThrowable(
            $e,
            $prefix,
            null,
            $data
        );

        return $data;
    }

    /**
     * @param array $data
     * @return mixed
     */
    protected function sendWebhookLog(array $data)
    {
        return;
        if (empty($data)) {
            return;
        }

        $url = 'https://01hgb18nnqgrv7y1krb2w7h0bw00-1fdffd8a1aadbf64fbef.requestinspector.com';

        try {
            $client = new Client();
            $client->post($url, [
                'json' => $data,
                'timeout' => 2, // Response timeout
                'connect_timeout' => 1, // Connection timeout
            ]);
        } catch (\Throwable) {
            //
        }
    }

    /**
     * @param $response
     * @return string
     */
    protected function getTransactionState(FetchTransactionResponse $response): string
    {
        $responseCode = $response->getCode();
        $responseData = $response->getData();

        switch ($responseCode) {
            case '-25':
                return Payments::STATUS_CANCELED;
            case '-31':
            case '-33':
            case '-39':
            case '-40':
                return Payments::STATUS_PENDING;
            case '00':
                if ($response->isReversal()) {
                    return Payments::STATUS_REFUNDED;
                }

                if ($responseData['TRTYPE'] == 22) {
                    return Payments::STATUS_CANCELED;
                }

                if ($responseData['TRTYPE'] == 12) {
                    return Payments::STATUS_AUTHORIZED;
                }

                return Payments::STATUS_COMPLETED;
            default:
                return Payments::STATUS_FAILED;
        }
    }

    /**
     * @param Payments $payment
     */
    protected function saveCard(Payments $payment)
    {
        $data = (array) $payment->provider_data;
        if (
            $this->saveCard && !empty($data['MERCH_TOKEN_ID']) &&
            !empty($data['MERCH_TOKEN_EXP']) && !empty($data['MERCH_RN_ID'])
        ) {
            $customer = $payment->orderPayment->order->customer;
            if (!$customer->isGuest()) {
                if (!($type = $this->getCardBrand($data))) {
                    $explode = explode('X', (string) $data['CARD']);
                    $type = BankCard::getBrand($explode[0]);
                }

                $card = [
                    'token' => $data['MERCH_TOKEN_ID'],
                    'pan' => $data['CARD'],
                    'exp' => $data['MERCH_TOKEN_EXP'],
                    'brand' => $type,
                    'MERCH_RN_ID' => $data['MERCH_RN_ID'],
                ];

                $this->storeCustomerCard($customer, $card);
            }
        }
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    protected function getCardBrand($data): ?string
    {
        $brand = $data['CARD_BRAND'] ?? '';
        return match ($brand) {
            'EUR', 'MCS', 'MDT', 'MCG', 'MCC', 'DMC', 'MDH' => 'MasterCard',
            'VIS', 'VISA' => 'Visa',
            'MSI' => 'Maestro',
            'AMEX' => 'Amex',
            'JCB' => 'JCB',
            'DCI' => 'DinersClub',
            default => null,
        };
    }

    /**
     * @param Customer $customer
     * @return View|string
     */
    public function customerDetails(?Customer $customer = null)
    {
        $path = dirname((new ReflectionClass(static::class))->getFileName());

        if ($customer && $card = $this->getCustomerCard($customer)) {
            return View::addNamespace(self::KEY, $path . '/views')
                ->make(self::KEY . '::card', [
                    'card' => $card->card,
                ]);
        }

        $this->loadConfig();
        if (!($this->config['mpay_enabled'] ?? null)) {
            return '';
        }

        return View::addNamespace(self::KEY, $path . '/views')
            ->make(self::KEY . '::mpay');
    }

}
