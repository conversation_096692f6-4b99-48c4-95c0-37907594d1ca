<?php

declare(strict_types=1);

namespace App\Integration\Payment\Klear;

use Illuminate\Support\ServiceProvider as IlluminateServiceProvider;

/**
 * Class ServiceProvider
 *
 * @package App\Integration\Payment\FusionPay
 */
class ServiceProvider extends IlluminateServiceProvider implements \Illuminate\Contracts\Support\DeferrableProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    #[\Override]
    public function register(): void
    {
        $this->app->bind('klear', fn ($app): \App\Integration\Payment\Klear\KlearService => new KlearService());
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    #[\Override]
    public function provides()
    {
        return ['klear'];
    }

}
