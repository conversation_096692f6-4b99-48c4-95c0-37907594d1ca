<div class="box">
    <div class="box-title">
        <div class="box-title-text">
            <h5>{t}payment_provider.head.amount_from_to{/t}</h5>
        </div>
    </div>

    <div class="box-section">
        <div class="row form-group">
            <div class="col-xs-6">
                <label class="control-label">{t}payment_provider.label.amount_from{/t}</label>
                <div class="input-group">
                    {if $currency[1] == 'before'}
                        <span class="input-group-addon gray input-group-addon-left">
                        {$currency[0]}
                    </span>
                    {/if}
                    <input type="text" class="form-control mask-currency" name="configuration[amount_from]" placeholder="{t}payment_provider.ph.amount_from{/t}" value="{$provider.configuration.amount_from|default:0}">
                    {if $currency[1] == 'after'}
                        <span class="input-group-addon gray input-group-addon-right">
                        {$currency[0]}
                    </span>
                    {/if}
                </div>
            </div>
            <div class="col-xs-6">
                <label class="control-label">{t}payment_provider.label.amount_to{/t}</label>
                <div class="input-group">
                    {if $currency[1] == 'before'}
                        <span class="input-group-addon gray input-group-addon-left">
                        {$currency[0]}
                    </span>
                    {/if}
                    <input type="text" class="form-control mask-currency" name="configuration[amount_to]" placeholder="{t}payment_provider.ph.amount_to{/t}" value="{$provider.configuration.amount_to|default}">
                    {if $currency[1] == 'after'}
                        <span class="input-group-addon gray input-group-addon-right">
                        {$currency[0]}
                    </span>
                    {/if}
                </div>
            </div>
        </div>

        <div class="row form-group">
            <div class="col-xs-12">
                {t}payment_provider.help.from_to{/t}
            </div>
        </div>
    </div>
</div>