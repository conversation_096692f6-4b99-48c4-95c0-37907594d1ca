<?php

declare(strict_types=1);

namespace App\Integration\Payment\Dsk;

use Illuminate\Support\Facades\Facade as IlluminateFacade;

/**
 * Class Facade
 *
 * @package App\Integration\Payment\Tbi
 */
class Facade extends IlluminateFacade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'dsk';
    }
}
