<script>
    CCHelper.addLoader('body', i18n.__('global.please_wait_processing'));
    $('#Cardinal-collector').remove();

    var $form = $('.js-checkout-payment form:first');
    var $popup = $('#braintree-popup');

    if ($popup.length) {
        $popup.remove();
    }

    $('body').append($('{$data.popup nofilter}'));
    var $btForm = $('#braintree-form');
    var $button = $('.js-braintree-submit');
    $button.trigger('loading.start');
    $popup = $('#braintree-popup');
    $popup.modal();
    CCHelper.removeLoader('body');

    braintree.dropin.create({
        authorization: '{$data.clientToken}',
        {if $data.enable3DS}
        threeDSecure: {
            amount: {$data.verifyAmount}
        },
        {/if}
        {if $data.trans}
        translations: {$data.trans nofilter},
        {/if}
        card: {
            vault: {
                vaultCard: false
            }
        },
        container: '#dropin-container'
    }, function (createErr, instance) {
        $button.trigger('loading.end');
        if (createErr) {
            return console.log(createErr);
        }

        $btForm.on('submit', function (e) {
            e.preventDefault();
            $button.trigger('loading.start');

            instance.requestPaymentMethod(function (err, payload) {
                console.log(err, payload);
                if (err) {
                    return handleError(instance);
                }

                {if $data.enable3DS}
                if (!payload.liabilityShifted) {
                    toastr.error(err ? err.message : '3D Secure Verification Failed');
                    return handleError(instance);
                }
                {/if}

                submitPayment(payload);
            });

            return false;
        });
    });

    var handleError = function (dropin) {
        $button.trigger('loading.end');
        dropin.clearSelectedPaymentMethod();
        return false;
    }

    var submitPayment = function (response) {
        $popup.modal('hide');
        var $input = $('<input name="payment_method_nonce" type="hidden" value="' + response.nonce + '">');
        $form.append($input);
        $form.submit();
        CCHelper.addLoader('body', i18n.__('global.please_wait_processing'));
    }
</script>