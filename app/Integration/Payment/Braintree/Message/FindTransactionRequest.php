<?php

declare(strict_types=1);

namespace App\Integration\Payment\Braintree\Message;

use Braintree\Result\Error;
use Braintree\Result\Successful;
use Braintree\Transaction;
use Omnipay\Braintree\Message\FindRequest;
use Omnipay\Braintree\Message\Response;

/**
 * Find Transaction Request
 *
 * @method Response send()
 */
class FindTransactionRequest extends FindRequest
{
    /**
     * Send the request with specified data
     *
     * @param  mixed $data The data to send
     * @return Response
     */
    #[\Override]
    public function sendData($data)
    {
        $response = $this->braintree->transaction()->find($data['transactionReference']);
        if (!$response instanceof Transaction) {
            $data = new Error((array)$response);
        } else {
            $data = new Successful($response);
        }

        return $this->createResponse($data);
    }

}
