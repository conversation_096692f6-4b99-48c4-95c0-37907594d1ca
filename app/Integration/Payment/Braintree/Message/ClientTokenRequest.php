<?php

declare(strict_types=1);

namespace App\Integration\Payment\Braintree\Message;

use Omnipay\Braintree\Message\ClientTokenRequest as AbstractRequest;

class ClientTokenRequest extends AbstractRequest
{
    #[\Override]
    public function getData()
    {
        $data = [];
        if ($merchantAccountId = $this->getMerchantAccountId()) {
            $data['merchantAccountId'] = $merchantAccountId;
        }

        if ($customerId = $this->getCustomerId()) {
            $data['customerId'] = $customerId;
        }

        $data += $this->getOptionData();

        return $data;
    }
}
