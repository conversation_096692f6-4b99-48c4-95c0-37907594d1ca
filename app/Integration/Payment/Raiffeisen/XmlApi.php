<?php

declare(strict_types=1);

namespace App\Integration\Payment\Raiffeisen;

use GuzzleHttp\Client;
use <PERSON><PERSON><PERSON><PERSON>\XMLSecLibs\XMLSecurityDSig;
use Rob<PERSON><PERSON><PERSON>\XMLSecLibs\XMLSecurityKey;

class XmlApi
{
    public static function captureXml(): never
    {
        $request = new \GuzzleHttp\Psr7\Request(
            'post',
            'https://ecg.test.upc.ua/go/service/02',
            [
                'Accept' => 'application/xml',
                'Content-type' => 'application/xml',
            ],
            self::buildRequestXml()
        );

        $response = (new Client())->send($request);
        dd($response->getBody()->getContents());
    }

    public static function buildRequestXml(): string|false
    {
        $serviceXmlNode = "<ECommerceConnect/>";
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?>' . $serviceXmlNode);
        $xml->addAttribute(
            'xsi:noNamespaceSchemaLocation',
            'https://secure.upc.ua/go/pub/schema/xmlpay-1.4.xsd',
            'http://www.w3.org/2001/XMLSchema-instance'
        );

        $message = $xml->addChild('Message');
        $message->addAttribute('id', \Carbon\Carbon::now()->timestamp);
        $message->addAttribute('version', '1.0');

        $requestData = $message->addChild('XMLPayRequest')
            ->addChild('RequestData');
        $requestData->addChild('MerchantID', '1756548');
        $requestData->addChild('TerminalID', 'E7884388');

        $postauthorization = $requestData->addChild('Transactions')
            ->addChild('Transaction')
            ->addChild('Postauthorization')
            ->addChild('PostauthorizationData');

        $invoice = $postauthorization->addChild('Invoice');
        $invoice->addChild('OrderID', 'OrderID');
        $invoice->addChild('Date', 'Date');
        $invoice->addChild('TotalAmount', 1000);
        $invoice->addChild('Currency', 975);
        $invoice->addChild('Description', 'Description');

        $ref = $postauthorization->addChild('PreauthorizationRef');
        $ref->addChild('ApprovalCode', 'ApprovalCode');
        $ref->addChild('Rrn', '234717643718');

        $postauthorization->addChild('PostauthorizationAmount', 100);

        //        $xml->addChild('Signature', 'sign');
        $privateKey = file_get_contents(__DIR__ . '/crt/cc.pem');
        return self::signRequestXml($xml->saveXML(), $privateKey);
    }

    /**
     * @param mixed $xml
     * @param mixed $pk
     * @return mixed
     */
    public static function signRequestXml($xml, $pk): string|false
    {
        $doc = new \DOMDocument();
        $doc->loadXML($xml);

        $sig = new XMLSecurityDSig('');
        $sig->setCanonicalMethod(XMLSecurityDSig::EXC_C14N);
        $sig->addReference($doc, XMLSecurityDSig::SHA1, [
            XMLSecurityDSig::XMLDSIGNS . 'enveloped-signature'
        ], ['force_uri' => true]);

        $key = new XMLSecurityKey(XMLSecurityKey::RSA_SHA1, ['type' => 'private']);
        $key->loadKey($pk);

        $sig->sign($key);
        $sig->appendSignature($doc->documentElement);

        return $doc->saveXML();
    }

}
