<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 14.3.2017 г.
 * Time: 17:07
 */

namespace App\Integration\Payment\Authorize;

use App\Contracts\PaymentConfigurationPreparationContract;
use App\Http\Request\Sitecp\Payment\ProviderConfigurationRequest;
use App\Models\Gateway\PaymentProviderConfiguration;

class ConfigurationPreparation implements PaymentConfigurationPreparationContract
{
    /**
     * @param \App\Http\Request\Sitecp\Payment\ProviderConfigurationRequest $request
     * @param \App\Models\Gateway\PaymentProviderConfiguration $paymentMethod
     * @return mixed
     */
    public static function prepare(ProviderConfigurationRequest $request, PaymentProviderConfiguration $paymentMethod): array
    {
        $configuration = $request->input('configuration');
        $configuration['mode'] = isset($configuration['mode']) ? 'live' : 'test';

        return array_merge($paymentMethod->configuration, $configuration);
    }
}
