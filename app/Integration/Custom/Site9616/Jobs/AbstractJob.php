<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 11.6.2018 г.
 * Time: 17:00 ч.
 */

namespace App\Integration\Custom\Site9616\Jobs;

use App\Integration\Custom\Site9616\Models\ErpProduct;
use App\Integration\Custom\Jobs\AbstractJob as BaseAbstractJob;

abstract class AbstractJob extends BaseAbstractJob
{
    protected $errors = [];

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function error($string, $file = null, $line = null, ?\Throwable $e = null): string
    {
        if ($string instanceof ErpProduct) {
            $message = sprintf('Error in product "%s" with message "%s"', $string->name, $string->exception);
        } else {
            $message = sprintf('Error in job for import with message "%s"', parent::error($string, $file, $line));
        }

        $this->errors[] = $message;

        return $message;
    }

    #[\Override]
    public function handle(): bool
    {
        $result = parent::handle();

        if (count($this->errors)) {
            //send errors
        }

        return $result;
    }

}
