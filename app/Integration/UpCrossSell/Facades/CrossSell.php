<?php

declare(strict_types=1);

namespace App\Integration\UpCrossSell\Facades;

use App\Integration\UpCrossSell\Manager;
use Illuminate\Support\Facades\Facade;

/**
 * Class Request
 *
 * @package App\Facades
 */
class CrossSell extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return Manager::APP_KEY;
    }

}
