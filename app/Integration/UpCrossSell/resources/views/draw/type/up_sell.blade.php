<div class="box" style="width: 400px;">
    <div class="box-title" style="background-color: #fffbe6">
        <div class="box-title-text">
            <h5>{{$record->name}}</h5>
        </div>

        <div class="box-title-addon">
            <a href="{{route("admin.up_sell.edit", [$record->id, 'diagram' => 1, 'parent' => ['action' => $group,'id' => $parent->id ?? null,'type' => $parent->record_type ?? null]])}}" data-panel-class="medium" data-ajax-panel="true">
                <i class="fal fa-pencil cc-purple"></i>
            </a>

            <a href="{{route('admin.up_sell.delete', [$record->id, $parent->id ?? 0, $group, $parent->record_type ?? null])}}" class="js-tree-delete-record-up-cross-sell" data-confirm="@lang('global.confirm.delete')">
                <i class="fal fa-times-circle cc-red"></i>
            </a>
        </div>
    </div>

    <div class="box-section">
        <p>@lang('up_sell.diagram.text.trigger.'.($group??'yes'), ['trigger'=>$record->trigger_variant->item->name??'', 'offer' => $record->offer_variant->item->name??__('global.N/A'), 'trigger_variant' => $record->trigger_variant->parameters_as_string??__('global.N/A'), 'offer_variant' => $record->offer_variant->parameters_as_string??__('global.N/A')])</p>
        <p>@lang('up_sell.diagram.text.offer.'.($group??'yes'), ['trigger'=>$record->trigger_variant->item->name??'', 'offer' => $record->offer_variant->item->name??__('global.N/A'), 'trigger_variant' => $record->trigger_variant->parameters_as_string??__('global.N/A'), 'offer_variant' => $record->offer_variant->parameters_as_string??__('global.N/A')])</p>
    </div>

    {{--
    @if(!$record->childes->get('yes'))
        @include('cross_up_sell::draw.type.new-record-click', ['record' => $record, 'group' => 'yes'])
    @endif
    @if(!$record->childes->get('no'))
        @include('cross_up_sell::draw.type.new-record-click', ['record' => $record, 'group' => 'no'])
    @endif
    --}}
</div>