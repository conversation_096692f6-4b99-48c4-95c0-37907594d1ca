<?php

declare(strict_types=1);

namespace App\Integration\WooCommerce\Requests;

use App\Http\AbstractRequest;

class SettingsRequest extends AbstractRequest
{
    public function rules(): array
    {
        return [
            'csv_file' => 'required_if:active,1'
        ];
    }

    #[\Override]
    public function messages(): array
    {
        return [
            'csv_file.required_if' => __('import.error.choose.file')
        ];
    }
}
