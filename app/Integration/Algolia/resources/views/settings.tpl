<div class="page-breadcrumb clearfix">
    {$breadcrumb = [
    [
    'title' => "{t}sidebar.apps{/t}",
    'href' => "{route('admin.apps')}"
    ],
    [
    'title' => "{t}algolia.info.title{/t}"
    ]
    ]}

    {include file="includes/breadcrumb.tpl"}
</div>
<div class="wrapper">
    <div class="wrapper1">
        <div class="app-wrapper">
            {include file="{Apps::templatesPath()}includes/app-intro.tpl" title=__('algolia.header.setting') text=__('algolia.help.setting')}
            {include file="{Apps::templatesPath()}includes/app-icons.tpl" app_icon='icon-algolia.png'}
            {include file="{Apps::templatesPath()}includes/app-addition.tpl" addition=$smarty.capture.app_addition}
        </div>
    </div>

    <div class="container-small">
        <form class="ajaxForm" role="form" action="{route('algolia.settings')}" method="POST">
            <div class="box">
                {if $settings.error_info_quota_exceeded}
                    <div class="box-section">
                        <span class="help-block-error">{t}algolia.error.quota_exceeded{/t}</span>
                    </div>
                {/if}

                <div class="box-section">
                    <div class="form-group">
                        <div class="stack">
                            <div class="stack-main">
                                <label class="control-label">{t}apps.label.enabled{/t}</label>
                            </div>

                            <div class="stack-addon">
                                <input type="checkbox" class="switch" value="1"
                                       name="active"{if $active|default} checked="checked"{/if}>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-xs-4">
                            <div class="form-control-box">
                                <div class="form-control-box-inner">
                                    <label class="control-label">{t}algolia.label.appId{/t}</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-xs-8">
                            <input name="appId" type="text" class="form-control handler-input"
                                   value="{$settings.appId|default}"/>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-xs-4">
                            <div class="form-control-box">
                                <div class="form-control-box-inner">
                                    <label class="control-label">{t}algolia.label.apiKey{/t}</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-xs-8">
                            <input name="apiKey" type="text" class="form-control handler-input"
                                   value="{$settings.apiKey|default}"/>
                        </div>
                    </div>
                </div>
                <div class="box-section">
                    <div class="form-group">
                        <div class="row form-group">
                            <div class="col-xs-10">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="control-label">{t}algolia.label.show_price{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-2">
                                <input type="checkbox" class="switch" value="1"
                                       name="showPrice" {if $settings.showPrice|default} checked="checked"{/if}>
                            </div>
                        </div>


                        <div class="row form-group">
                            <div class="col-xs-8">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="control-label">{t}algolia.label.count.products{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-4">
                                <input name="shownProductsCount" type="text" class="form-control handler-input"
                                       value="{$settings.shownProductsCount|default}"/>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-8">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="control-label">{t}algolia.label.count.categories{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-4">
                                <input name="shownCategoriesCount" type="text" class="form-control handler-input"
                                       value="{$settings.shownCategoriesCount|default}"/>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-8">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="control-label">{t}algolia.label.count.vendors{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-4">
                                <input name="shownVendorsCount" type="text" class="form-control handler-input"
                                       value="{$settings.shownVendorsCount|default}"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group text-right">
                <a id="start-indexing" href="#" data-ajax="{route('algolia.start_indexing')}"
                   class="btn btn-white">{t}algolia.btn.start_indexing{/t}</a>

                {*
                {include file="imports/includes/status_button.tpl" route={route('apps.algolia.status')}}
                *}
                <button type="submit" class="btn btn-primary">{t}global.save{/t}</button>
            </div>
        </form>
    </div>
</div>

{capture append="js"}
    <script type="text/javascript">
        {literal}
        $(function () {
            $('#start-indexing').on('cc.ajax.success', function (e, response) {
                CC.modal(response, {
                    size: 'small'
                });
            });
        });
        {/literal}
    </script>
{/capture}
