<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 11/29/2018
 * Time: 6:26 PM
 */

namespace App\Integration\Algolia\Jobs;

use App\Models\Router\Logs;
use App\Models\Router\Site;
use Throwable;

class UnMakeSearchable extends AbstractJob
{
    /**
     * Create a new job instance.
     *
     * @param  \Illuminate\Database\Eloquent\Collection $modelsIds
     * @param $class
     */
    public function __construct(/**
     * The models to be made searchable.
     */
        public $modelsIds,
        protected $class
    ) {
        $this->site_id = site('site_id');
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function execute()
    {
        $site = parent::execute();
        if (!($site instanceof Site)) {
            return $site;
        }

        if ($site->maintenance) {
            return [static::SITE_MAINTENANCE, ['modelsIds' => $this->modelsIds, 'class' => $this->class]];
        }

        if ($this->modelsIds->isEmpty()) {
            return static::EXECUTE_DESTROY;
        }

        $models = $this->class::query()
            ->whereIn('id', $this->modelsIds)
            ->get();

        try {
            $models->first()->searchableUsing()->delete($models);
        } catch (Throwable $throwable) {
            Logs::createFromThrowable($throwable, 'Algolia', null, [
                'site_id' => $site->site_id,
            ]);
        }

        return static::EXECUTE_DESTROY;
    }
}
