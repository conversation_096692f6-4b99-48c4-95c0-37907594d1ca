<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 11/29/2018
 * Time: 6:26 PM
 */

namespace App\Integration\Algolia\Jobs;

use App\Models\Product\Product;
use App\Models\Router\Logs;
use App\Models\Router\Site;
use Throwable;

class MakeSearchable extends AbstractJob
{
    /**
     * Create a new job instance.
     *
     * @param  \Illuminate\Database\Eloquent\Collection $modelsIds
     * @param $class
     */
    public function __construct(/**
     * The models to be made searchable.
     */
        public $modelsIds,
        protected $class
    ) {
        $this->site_id = site('site_id');
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function execute()
    {
        $site = parent::execute();
        if (!($site instanceof Site)) {
            return $site;
        }

        if ($this->modelsIds->isEmpty()) {
            return static::EXECUTE_DESTROY;
        }

        if ($site->maintenance) {
            return [static::SITE_MAINTENANCE, ['modelsIds' => $this->modelsIds, 'class' => $this->class]];
        }

        $models = $this->class::query();

        if ($this->class == Product::class) {
            $models->with([
                'category.path', 'categories',
                'variants', 'product_to_discount_cp',
                'image', 'vendor',
                'categoryPropertiesOptions'
            ]);
        }

        $models = $models->whereIn('id', $this->modelsIds)->get();

        $start = microtime(true);
        try {
            $models->first()->searchableUsing()->update($models);
        } catch (Throwable $throwable) {
            Logs::createFromThrowable($throwable, 'Algolia', null, [
                'site_id' => $site->site_id,
            ]);
        }

        $this->info('Finish upload to algolia for %d seconds!', microtime(true) - $start);

        return static::EXECUTE_DESTROY;
    }
}
