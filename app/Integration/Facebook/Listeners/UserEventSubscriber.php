<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 5.4.2017 г.
 * Time: 11:04 ч.
 */

namespace App\Integration\Facebook\Listeners;

use App\Integration\Facebook\Models\MessengerSubscriber;
use Facebook;
use App\Models\Store\Cart as CartModel;
use App\Models\Customer\Customer;
use Illuminate\Auth\Events\Registered;
use Illuminate\Events\Dispatcher;

class UserEventSubscriber
{
    /**
     * Handle user logout events.
     * @param Registered $registered
     * @throws \Throwable
     */
    public function onUserRegisterGuest(Registered $registered): void
    {
        if (app_namespace() != 'site') {
            return;
        }

        if ($registered->user instanceof Customer
            && \Apps::installed('messenger_bot')
            && ($cart_id = ($cartInstance = CartModel::instance()) ? $cartInstance->id : null)
            && Facebook::connected()) {
            $subscriber = MessengerSubscriber::where('cart_id', $cart_id)->first();
            if ($subscriber) {
                $subscriber->customer()->associate($registered->user)->update([
                    'cart_id' => null,
                ]);
            }
        }
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param  Dispatcher $events
     */
    public function subscribe($events): void
    {
        $events->listen(
            [
                'Illuminate\Auth\Events\Register',
                'Illuminate\Auth\Events\RegisterGuest',
            ],
            'App\Integration\Facebook\Listeners\UserEventSubscriber@onUserRegisterGuest'
        );
    }
}
