<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 1.1.2017 г.
 * Time: 22:33 ч.
 */

namespace App\Integration\Facebook\Models;

use App\Models\Customer\Customer;
use App\Models\Store\AbandonedCart;
use App\Models\Store\Cart as CartModel;
use App\Traits\Crudling;
use Illuminate\Database\Query\Builder;
use App\Traits\Model as ModelTrait;
use App\Helper\Grid;
use Eloquent;

/**
 * Class MessengerSubscriber
 * @package App\Integration\Facebook\Models
 * @property string $fb_ref_number
 * @property Customer|null $customer
 * @property CartModel|null $cart
 * @property int $id
 * @property int $customer_id
 * @property int $cart_id
 * @property string $cart_key
 * @property string $username
 * @property string $first_name
 * @property string $last_name
 * @property bool $bot_should_answer
 * @property mixed $psid
 * @method static MessengerSubscriber first($columns = [])
 * @method static Builder|MessengerSubscriber where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static MessengerSubscriber firstOrNew(array $attributes, array $values = array())
 * @method static MessengerSubscriber firstOrCreate(array $attributes, array $values = array())
 */
class MessengerSubscriber extends Eloquent
{
    use Crudling;

    use ModelTrait;
    /**
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cart()
    {
        return $this->belongsTo(CartModel::class, 'cart_key', 'key');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * @return mixed
     */
    public function guestAbandonedCart()
    {
        return $this->hasOne(AbandonedCart::class, 'id', 'cart_id')->whereHas('items')->with('items');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customerAbandonedCart()
    {
        return $this->belongsTo(AbandonedCart::class, 'customer_id', 'user_id');
    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function lastCustomMessage()
    {
        return $this->hasMany(MessengerSentMessage::class, 'subscriber_id', 'id')
            ->where('type', MessengerSetting::SUBSCRIBERS_TYPE)
            ->orderBy('date_sent', 'desc')
            ->limit(1);
    }

    /**
     * @param Builder $query
     * @param $date
     * @param $operator
     */
    public function scopeWhereCreatedAt($query, $date, $operator): void
    {
        $query->whereRaw(sprintf("created_at %s '%s'", $operator, $date));

        if ($operator == '<') {
            $query->orWhereNull('created_at');
        }
    }

    /**
     * @param Builder $query
     * @param $date
     * @param $operator
     */
    public function scopeWhereLastCustomMessage($query, $date, $operator): void
    {
        $query->whereHas('lastCustomMessage', function ($q) use ($date, $operator): void {
            $q->whereRaw(sprintf("date_sent %s '%s'", $operator, $date));
        });
    }

    /**
     * @param null $where
     * @param null $join
     * @param Grid|null $grid
     * @param null $with
     * @param \Closure|null $callbackQuery
     * @param array $columns
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Contracts\Pagination\LengthAwarePaginator|\Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection
     */
    public static function getOnlySubscribersList($where = null, $join = null, ?Grid $grid = null, $with = null, ?\Closure $callbackQuery = null, $columns = ['*'])
    {
        /** @var \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder $query */
        $query = static::query();
        $query->whereNull('customer_id');

        if (!empty($where)) {
            if (is_array($where)) {
                foreach ((array)$where as $key => $value) {
                    if (is_callable($value)) {
                        $query->where($value);
                    } else {
                        $query->where($key, $value);
                    }
                }
            } else {
                $query->whereRaw($where);
            }
        }

        if (!empty($join)) {
            $query->joinRaw($join);
        }

        if ($callbackQuery !== null) {
            $callbackQuery($query);
        }

        if (!empty($with)) {
            $query->with($with);
        }

        if (!empty($grid->order)) {
            $query->orderBy($grid->order, $grid->direction);
        } elseif (($m = new static()) && ($g = $m->getGuarded()) && array_search($m->getKeyName(), $g, true) !== false) {
            $query->orderBy($m->getTable() . '.' . $m->getKeyName(), 'desc');
        }

        if (!empty($grid->pagging)) {
            return $query->paging($grid->pagging, $columns)->keyBy($query->getModel()->getKeyName());
        }

        return $query->get($columns)->keyBy($query->getModel()->getKeyName());
    }
}
