<script type="text/javascript">
    $(document).onLast('cc.addToCart.product.added cc.addToCart.product.updated', function(e, json) {
        if(json && json.status === 'success') {
            Ajax.ajax({
                url: ccRoutes.fbPixel.replace('@EVENT@', 'addToCart'),
                data: {data: json, _token: "{{csrf_token()}}"},
                type: 'post'
            });
        }
    });

    $(document).onLast('cc.wishlist.added', function(e, json) {
        if(json && json.status === 'success') {
            Ajax.ajax({
                url: ccRoutes.fbPixel.replace('@EVENT@', 'addToWishlist'),
                data: {data: json.product, _token: "{{csrf_token()}}"},
                type: 'post'
            });
        }
    });
</script>