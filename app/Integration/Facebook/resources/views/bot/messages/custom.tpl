{include file="../dependency.tpl"}

<div class="form">
    <form class="messenger-form ajaxForm" role="form" action="{route('messenger.subscribers.message', ['messageId' => $message->id|default])}">
        <div class="box-title fixed-top">
            <div class="side-panel-header">
                <div class="left-controls">
                    <div class="close" data-dismiss="panel"></div>
                </div>
                <div class="right-controls">
                    <button class="btn btn-default" data-dismiss="panel">{t}global.cancel{/t}</button>
                    <button class="btn btn-primary submit">{t}global.save{/t}</button>
                </div>
            </div>
        </div>

        <div class="row form-group">
            <div class="col-md-12 col-lg-7">
                <div class="box">
                    <div class="box-title">
                        <div class="box-title-text">
                            <div class="stack form-group">
                                <div class="stack-main">
                                    <h5>{t}facebook.box.title.subscribers.message{/t}</h5>
                                    <p>{t}facebook.box.title.subscribers.message.description{/t}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-12">
                                <textarea class="form-control js-message-text" maxlength="2000" data-message-target="#text-target"" name="message" rows="7">{$message->message|default}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="box">
                    <div class="box-title">
                        <div class="box-title-text">
                            <div class="stack form-group">
                                <div class="stack-main">
                                    <div class="stack-main">
                                        <div class="form-control-box">
                                            <div class="form-control-box-inner">
                                                <label class="form-control-check">{t}mobica.box.periodical.text{/t} <i class="glyphicon glyphicon-info-sign tooltips" title="" data-placement="top" data-original-title="{t}mobica.box.periodical.note{/t}"></i></label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="stack-addon">
                                        <input id="periodical" name="periodical" type="checkbox" class="switch" {if $message->periodical|default} checked="checked"{/if} value="1" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="box-section" {if $message->periodical|default != '1'} style="display:none;"{/if} id="periodical_dates" >
                        <div class="row form-group">
                            <div class="col-md-12">
                                <label class="control-label" for="periodical">{t}mobica.box.periodical_hours.text{/t}</label>
                            </div>
                        </div>

                        <div class="row form-group margin-bottom-30">
                            <div class="col-xs-6">
                                <div class="form-group">
                                    <label class="control-label" for="periodical_start">{t}mobica.from{/t}</label>
                                    <input type="text" name="periodical_start" value="{$message->periodical_start|default}" class="form-control form_datetime no_dates" data-format="H:mm" />
                                </div>
                            </div>
                            <div class="col-xs-6">
                                <div class="form-group">
                                    <label class="control-label" for="periodical_end">{t}mobica.to{/t}</label>
                                    <input type="text" name="periodical_end" value="{$message->periodical_end|default}" class="form-control form_datetime no_dates" data-format="H:mm" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="box">
                    <div class="box-title">
                        <div class="box-title-text">
                            <div class="stack form-group">
                                <div class="stack-main">
                                    <h5>{t}segments::segment.label.title{/t}</h5>
                                    <p>{t}segments::segment.label.description{/t}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-12">
                                <input name="segment_id" class="form-control select2_ajax" value="{$selectedSegments}" data-multiple="true" data-url="{route('admin.autocomplete.segment', 'messenger')}"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {include './phone_template.tpl'}
        </div>
    </form>
</div>


{capture append="js"}
    <script type="text/javascript">
        $(function ()
        {
            $(".messenger-form").on('cc.ajax.success', function ()
            {
                $('#messages_table').trigger('cc.ajax.reload');
            });

            $('#periodical').on('change', function() {
                var periodicalStartField = $('input[name="periodical_start"]');
                var periodicalEndField = $('input[name="periodical_end"]');

                if($(this).is(':checked')) {
                    $('#periodical_dates').slideToggle('show');
                    periodicalStartField.removeAttr('disabled').removeProp('disabled');
                    periodicalEndField.removeAttr('disabled').removeProp('disabled');
                } else {
                    $('#periodical_dates').slideToggle('hide');
                    periodicalStartField.attr('disabled', 'disabled').prop('disabled', true);
                    periodicalEndField.attr('disabled', 'disabled').prop('disabled', true);
                }
            });
        });
    </script>
{/capture}
