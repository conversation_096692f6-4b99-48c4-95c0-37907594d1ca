{capture name="body_class"}white-panel{/capture}

<div class="page-breadcrumb clearfix">
    {$breadcrumb = [
    [
    'title' => "{t}sidebar.apps{/t}",
    'href' => "{route('admin.apps')}"
    ],
    [
    'title' => "{t}facebook.messenger_bot.info.title{/t}"
    ]
    ]}

    {include file="includes/breadcrumb.tpl"}

    <div class="pull-right">
        <a href="{route('admin.facebook-page.settings', ['next' => $next])}" class="btn btn-default">
            {t}facebook.page.settings{/t}
        </a>

        <a href="{route('messenger.subscribers.message')}" class="btn btn-primary" data-ajax-panel data-panel-class="wide">{t}facebook.info.subscribers.message{/t}</a>
    </div>
</div>
<div class="content-padding clearfix">
    <div class="grid-wrapper grid-products padding-top-0" id="messages_table" data-url="{route('messenger.subscribers.custom_messages')}" data-chart="yes">
        <div class="grid-controls no-bulk"></div>
        <div class="content-padding padding-top-0">
            <table class="listing">
                <thead>
                    <tr>
                        <th data-field="message" class="text-left" data-align="left" data-sort="no" width="50">
                            {t}facebook.subscribers.title.old_messages{/t}
                        </th>

                        <th data-field="sent_count" class="text-center" data-align="center" data-sort="no" width="50">
                            {t}facebook.info.dashboard.sent_messages{/t}
                        </th>

                        <th data-field="segments_formatted" class="text-left" data-align="left" data-sort="no" width="50">
                            {t}segments::segment.label.title{/t}
                        </th>

                        <th class="text-center" data-align="center" data-field="created_at" data-field-display="date_added_formatted" width="50">{t}global.date_added{/t}</th>

                        <th data-field="sent_messages" class="text-left" data-align="left" data-sort="no" width="50"></th>

                        <th data-field="active" class="text-center" data-align="center" data-sort="no" width="50">{t}global.active{/t}</th>

                        <th data-field="delete" class="text-center" data-align="center" data-sort="no" width="50"></th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

{capture append="js"}
    <script type="text/javascript">
        var $wrapper = $('#messages_table');

        $wrapper.on('cc.ajax.success', '.delete', function ()
        {
            $wrapper.trigger('cc.ajax.reload');
        });
    </script>
{/capture}