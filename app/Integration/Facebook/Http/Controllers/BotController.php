<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: user
 * Date: 11/24/2017
 * Time: 1:27 PM
 */

namespace App\Integration\Facebook\Http\Controllers;

use App\Exceptions\Error;
use App\Integration\Facebook\Apps\MessengerBot;
use App\Integration\Facebook\Models\MessengerSetting;
use App\Integration\Facebook\Requests\BotGeneralMessageRequest;
use App\Integration\Facebook\Requests\PersistentMenuItemRequest;
use App\Integration\Facebook\Requests\QuickReplyRequest;
use App\Models\Discount\Discount;
use Exception;
use Facebook;
use FacebookPage;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response as ResponseAlias;
use Illuminate\Support\Facades\Validator;
use Messenger;
use Modules\Apps\Abstractions\Controllers\AbstractAppsController;
use Illuminate\Support\Facades\View;

/**
 * Class BotController
 * @package App\Integration\Facebook\Http\Controllers
 */
class BotController extends AbstractAppsController
{
    /**
     * @var
     */
    protected $configFacebookUrl;

    /**
     * ChatController constructor.
     * @param MessengerBot $manager
     * @throws Error
     */
    public function __construct(MessengerBot $manager)
    {
        parent::__construct($manager);

        $next = 'messenger.bot.settings';
        if (!Facebook::connected()) {
            $this->configFacebookUrl = route('facebook.connect', ['next' => $next]);
        } elseif (!FacebookPage::configured()) {
            $this->configFacebookUrl = route('admin.facebook-page.settings', ['next' => $next]);
        }
    }

    /**
     * @return RedirectResponse|\Illuminate\Routing\Redirector|View
     * @throws \App\Exceptions\Error
     */
    public function index()
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }
    }


    /**Bot
     * @return RedirectResponse|\Illuminate\Routing\Redirector|View
     * @throws \App\Exceptions\Error
     */
    public function abandonedCartMessagesSettings()
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        $settings = MessengerSetting::settings(MessengerSetting::ABANDONED_CART_TYPE);

        $discounts = Discount::where('active', 1)->whereNotNull('code')->get();

        return View::mainResponse('facebook::bot.settings.abandoned_cart', [
            'settings' => $settings, //$this->manager->getSettings(),
            'next' => 'messenger.abandoned-cart.settings',
            'discounts' => $discounts
        ]);
    }

    /**
     * @return RedirectResponse|\Illuminate\Routing\Redirector|View
     * @throws \App\Exceptions\Error
     */
    public function completeOrdersMessagesSettings()
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        $settings = MessengerSetting::settings(MessengerSetting::COMPLETE_ORDERS_TYPE);

        return View::mainResponse('facebook::bot.settings.complete_orders', [
            'settings' => $settings, //$this->manager->getSettings(),
            'next' => 'messenger.complete-orders.settings',
        ]);
    }

    /**
     * @return View
     */
    protected function showInstall()
    {
        return View::mainResponse('facebook::bot.install');
    }

    /**
     * @param Request $request
     * @param $type
     * @return ResponseAlias
     * @throws Exception
     */
    public function saveSettings(Request $request, $type): \Illuminate\Http\Response
    {
        MessengerSetting::saveSettings($type, $request);

        return new ResponseAlias([
            'msg' => __('apps.successfully.configured'),
            'status' => 'success',
        ]);
    }


    /**
     * @param $type
     * @return RedirectResponse|ResponseAlias|\Illuminate\Routing\Redirector|View
     * @throws Error
     */
    public function welcomeMessage($type)
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        $settings = MessengerSetting::settings($type);

        return View::modal('facebook::bot.messages.welcome', [
            'settings' => $settings,
            'type' => $type,
        ]);
    }

    /**
     * @param Request $request
     * @param $type
     * @return ResponseAlias
     * @throws Exception
     */
    public function saveWelcomeMessageSettings(Request $request, $type): \Illuminate\Http\Response
    {
        MessengerSetting::saveSettings($type, $request);

        return new ResponseAlias([
            'msg' => __('apps.successfully.configured'),
            'status' => 'success'
        ]);
    }

    /**
     * @param $message
     * @return RedirectResponse|ResponseAlias|View
     * @throws Error
     */
    public function abandonedCartMessage(string $message)
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        switch ($message) {
            case 'first-message':
                $settings_description = __('facebook.box.title.first_message');
                $message_description = __('facebook.info.abandoned.first_message.description');
                break;
            case 'second-message':
                $settings_description = __('facebook.box.title.second_message');
                $message_description = __('facebook.info.abandoned.second_message.description');
                break;
            case 'third-message':
                $settings_description = __('facebook.box.title.third_message');
                $message_description = __('facebook.info.abandoned.third_message.description');
                break;
            default:
                $settings_description = '';
                $message_description = '';
        }

        $settings = MessengerSetting::settings($message . '-' . MessengerSetting::ABANDONED_CART_TYPE);
        $discounts = Discount::where('active', 1)->whereNotNull('code')->get();

        $data = ['message' => $message,
            'settings_description' => $settings_description,
            'message_description' => $message_description,
            'settings' => $settings,
            'discounts' => $discounts];

        return View::modal('facebook::bot.messages.abandoned_cart', $data);
    }

    /**
     * @param Request $request
     * @param $message
     * @return ResponseAlias
     * @throws Exception
     */
    public function saveAbandonedCartMessageSettings(Request $request, string $message)
    {
        MessengerSetting::saveSettings($message . '-' . MessengerSetting::ABANDONED_CART_TYPE, $request);

        return ResponseAlias::create([
            'msg' => __('apps.successfully.configured'),
            'status' => 'success'
        ]);
    }

    /**
     * @return RedirectResponse|ResponseAlias|View
     * @throws Error
     */
    public function orderStatusChangeMessage()
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        $settings = MessengerSetting::settings(MessengerSetting::ORDER_STATUS_CHANGE_MESSAGE_TYPE);

        return View::modal('facebook::bot.messages.order_status_change', [
            'settings' => $settings
        ]);
    }


    /**
     * @param Request $request
     * @return ResponseAlias
     * @throws Exception
     */
    public function saveOrderStatusChangeMessageSettings(Request $request): \Illuminate\Http\Response
    {
        MessengerSetting::saveSettings(MessengerSetting::ORDER_STATUS_CHANGE_MESSAGE_TYPE, $request);

        return new ResponseAlias([
            'msg' => __('apps.successfully.configured'),
            'status' => 'success'
        ]);
    }

    /**
     * @return RedirectResponse|\Illuminate\Routing\Redirector|string|View
     * @throws Error
     * @throws \SmartyException
     */
    public function loadBotSettingsForm()
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        $persistent_menu_items = array_filter(Messenger::getPersistentMenu());
        $quick_replies = array_filter(Messenger::getQuickReplies());
        $settings = $this->manager->getSettings()->all();

        View::assign([
            'persistent_menu_items' => $persistent_menu_items,
            'quick_replies' => $quick_replies,
        ]);

        return View::fetchHtmlJsonResponse('facebook::bot.settings.bot-form', [
            'persistent_menu_items' => $persistent_menu_items,
            'quick_replies' => $quick_replies,
            'more_products_button' => $settings['more_products_button'] ?? '',
            'show_related_products' => $settings['show_related_products'] ?? 0,
            'intro_message' => $settings['intro_message'] ?? '',
        ]);
    }

    /**
     * @return RedirectResponse|ResponseAlias|\Illuminate\Routing\Redirector|View
     * @throws Error
     */
    public function botSettings()
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        $settings = $this->manager->getSettings()->all();

        return View::mainResponse('facebook::bot.settings.bot', [
            'persistent_menu_items' => Messenger::getPersistentMenu(),
            'quick_replies' => Messenger::getQuickReplies(),
            'settings' => $settings,
            'next' => 'messenger.bot.settings',
        ]);
    }

    /**
     * @param Request $request
     * @return RedirectResponse|\Illuminate\Routing\Redirector|View|static
     * @throws \Throwable
     */
    public function saveBotSettings(Request $request)
    {
        $params = array_merge(
            [
            'show_related_products' => 0,
            'bot_active' => 0,
            'bot_active_in_shop_chat' => 0,
            'checkout_in_bot' => 0
        ],
            $request->all()
        );

        try {
            if (!$params['bot_active'] && !$params['bot_active_in_shop_chat']) {
                $this->manager->deletePersistentMenu();
            } else {
                $json = $this->manager->getSetting(MessengerBot::PERSISTENT_MENU);
                $persistentMenuParams = (array)json_decode((string) $json);
                Messenger::updatePersistentMenuInFacebook($persistentMenuParams);
            }

            $this->manager->updateSettings($params);

            return ResponseAlias::create([
                'msg' => __('apps.successfully.configured'),
                'status' => 'success'
            ]);
        } catch (Exception $exception) {
            return ResponseAlias::create([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }
    }


    /**
     * @param null $type
     * @return RedirectResponse|ResponseAlias|\Illuminate\Routing\Redirector|View
     * @throws Error
     */
    public function addPersistentMenuItemView($type = null)
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        if (!Messenger::getPersistentMenuFreeType() && is_null($type)) {
            throw new Error(__('facebook.messenger_bot.tooltip_info.allowed_persistent_menu_items'));
        }

        if ($type) {
            $settings = Messenger::getPersistentMenu($type);
        }

        return View::modal('facebook::bot.messages.persistent_menu', [
            'settings' => $settings ?? null,
            'type' => $type
        ]);
    }

    /**
     * @param PersistentMenuItemRequest $request
     * @param null $type
     * @return ResponseAlias
     * @throws \SmartyException
     * @throws \Throwable
     */
    public function savePersistentMenuItem(PersistentMenuItemRequest $request, $type = null)
    {
        $settings = Messenger::getPersistentMenu();
        $newItemType = $type ?: Messenger::getPersistentMenuFreeType();

        if ($newItemType) {
            $settings[$newItemType] = (object)$request->all();
            $json = json_encode($settings);

            try {
                \Illuminate\Support\Facades\DB::transaction(function () use ($json): void {
                    $this->manager->updateSetting(MessengerBot::PERSISTENT_MENU, $json);
                    $accessToken = FacebookPage::accessToken();

                    $params = [
                        'get_started' => [
                            'payload' => site('site_id') . '-get_started'
                        ]
                    ];

                    Facebook::graph()->post('/me/messenger_profile', $params, $accessToken);

                    $persistent_menu_items = (array)json_decode($json);
                    Messenger::updatePersistentMenuInFacebook($persistent_menu_items);
                });
            } catch (Exception $e) {
                return ResponseAlias::create([
                    'msg' => $e->getMessage(),
                    'status' => 'error'
                ]);
            }
        }

        return ResponseAlias::create([
            'msg' => __('apps.successfully.configured'),
            'status' => 'success',
            'hasFreeSlot' => (bool)Messenger::getPersistentMenuFreeType(),
            'html' => View::fetch('facebook::bot.settings.persistent-menu', [
                'persistent_menu_items' => $settings
            ])
        ]);
    }

    /**
     * @param $type
     * @return ResponseAlias
     * @throws \SmartyException
     * @throws \Throwable
     */
    public function removePersistentMenuItem($type)
    {
        $settings = Messenger::getPersistentMenu();
        unset($settings[$type]);
        $json = json_encode($settings);

        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($json): void {
                $this->manager->updateSetting(MessengerBot::PERSISTENT_MENU, $json);

                $params = (array)json_decode($json);
                Messenger::updatePersistentMenuInFacebook($params);
            });
        } catch (Exception $exception) {
            return ResponseAlias::create([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }

        return ResponseAlias::create([
            'msg' => __('apps.successfully.configured'),
            'status' => 'success',
            'hasFreeSlot' => (bool)Messenger::getPersistentMenuFreeType(),
            'html' => View::fetch('facebook::bot.settings.persistent-menu', [
                'persistent_menu_items' => $settings
            ])
        ]);
    }

    /**
     * @param null $type
     * @return RedirectResponse|ResponseAlias|\Illuminate\Routing\Redirector|View
     * @throws Error
     */
    public function addQuickReplyItemView($type = null)
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        $settings = [];
        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        if (!Messenger::getQuickRepliesFreeType()) {
            throw new Error(__('facebook.messenger_bot.tooltip_info.allowed_quick_replies'));
        }

        if ($type) {
            $settings = Messenger::getQuickReplies($type);
        }

        return View::modal('facebook::bot.messages.quick_reply', [
            'settings' => $settings ?? null,
            'type' => $type,
            'key_words' => !empty($settings) ? $settings->key_words : null
        ]);
    }

    /**
     * @param QuickReplyRequest $request
     * @param null $type
     * @return static
     * @throws Error
     * @throws \SmartyException
     */
    public function saveQuickReply(QuickReplyRequest $request, $type = null)
    {
        $settings = Messenger::getQuickReplies();
        $newItemType = $type ?: Messenger::getQuickRepliesFreeType();

        if ($newItemType) {
            $settings[$newItemType] = (object)$request->all();
            $json = json_encode($settings);

            $this->manager->updateSetting(MessengerBot::QUICK_REPLY, $json);

            return ResponseAlias::create([
                'msg' => __('apps.successfully.configured'),
                'status' => 'success',
                'hasFreeSlot' => (bool)Messenger::getQuickRepliesFreeType(),
                'html' => View::fetch('facebook::bot.settings.quick-replies', [
                    'quick_replies' => $settings
                ])
            ]);
        }
    }

    /**
     * @param $type
     * @return static
     * @throws Error
     * @throws \SmartyException
     */
    public function removeQuickReply($type)
    {
        $quick_replies = array_filter(Messenger::getQuickReplies());
        unset($quick_replies[$type]);
        $json = json_encode($quick_replies, JSON_UNESCAPED_UNICODE);

        $this->manager->updateSetting(MessengerBot::QUICK_REPLY, $json);

        return ResponseAlias::create([
            'msg' => __('apps.successfully.configured'),
            'status' => 'success',
            'hasFreeSlot' => (bool)Messenger::getQuickRepliesFreeType(),
            'html' => View::fetch('facebook::bot.settings.quick-replies', [
                'quick_replies' => $quick_replies
            ])
        ]);
    }

    /**
     * @param $type
     * @return RedirectResponse|ResponseAlias|\Illuminate\Routing\Redirector|View
     * @throws Error
     */
    public function botMessage($type)
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        $settings = json_decode((string) $this->manager->getSetting($type));

        if (is_null($settings)) {
            $quick_replies_result = [];
        } else {
            $quick_replies = (array)json_decode((string) $this->manager->getSetting(MessengerBot::QUICK_REPLY));
            $selected_quick_replies = explode(',', (string) $settings->quick_replies);

            $quick_replies_result = [];
            foreach ($selected_quick_replies as $quick_reply) {
                if (array_key_exists($quick_reply, $quick_replies) && !empty($quick_replies[$quick_reply])) {
                    $quick_replies_result[] = [
                        'id' => $quick_reply,
                        'name' => $quick_replies[$quick_reply]->button_title,
                    ];
                }
            }
        }

        switch ($type) {
            case MessengerBot::INTRO_MESSAGE :
                $title = __('facebook.box.title.introductory_message');
                $description = __('facebook.box.title.introductory_message.description');
                break;
            case MessengerBot::FALLBACK_MESSAGE :
                $title = __('facebook.info.bot.fallback_message');
                $description = __('facebook.info.bot.fallback_message.description');
                break;
            default:
                $title = '';
                $description = '';
        }

        return View::modal('facebook::bot.messages.general', [
            'type' => $type,
            'message' => is_null($settings) ? '' : $settings->message,
            'quick_replies' => json_encode($quick_replies_result, JSON_UNESCAPED_UNICODE),
            'title' => $title,
            'description' => $description,
        ]);
    }


    /**
     * @param BotGeneralMessageRequest $request
     * @param $type
     * @return ResponseAlias
     * @throws Error
     */
    public function saveBotMessage(BotGeneralMessageRequest $request, $type): \Illuminate\Http\Response
    {
        $json = json_encode($request->all());
        $this->manager->updateSetting($type, $json);

        return new ResponseAlias([
            'msg' => __('facebook.succ.subscribers.messages_sent'),
            'status' => 'success'
        ]);
    }

    /**
     * @return RedirectResponse|ResponseAlias|\Illuminate\Routing\Redirector|View
     * @throws Error
     */
    public function quickMessages()
    {
        if (!$this->manager->isInstalled()) {
            return $this->showInstall();
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        $quick_replies = array_filter(Messenger::getQuickReplies());
        $quick_replies_result = [];

        foreach ($quick_replies as $quick_reply_key => $quick_reply_value) {
            $quick_replies_result[] = [
                'id' => $quick_reply_key,
                'name' => $quick_reply_value->button_title,
            ];
        }

        $params = [
            'results' => $quick_replies_result,
            'more' => false
        ];

        return response($params);
    }

    /**
     * @param $type
     * @return mixed
     * @throws Error
     */
    public function messagesSettings($type)
    {
        $path = '';
        switch ($type) {
            case 'email':
                $path = 'facebook::bot.messages.checkout.email_requests';
                break;
            case 'name':
                $path = 'facebook::bot.messages.checkout.name_requests';
                break;
            case 'address':
                $path = 'facebook::bot.messages.checkout.address_requests';
                break;
            case 'phone':
                $path = 'facebook::bot.messages.checkout.phone_requests';
                break;
            case 'cart_details':
                $path = 'facebook::bot.messages.checkout.cart_details_requests';
                break;
            case 'product_details':
                $path = 'facebook::bot.messages.checkout.products_details';
                break;
        }

        $settings = $this->manager->getSettings()->all();

        return View::modal($path, [
            'settings' => $settings,
            'type' => $type,
            'next' => 'messenger.bot.settings',
        ]);
    }

    /**
     * @param Request $request
     * @param $type
     * @return RedirectResponse|\Illuminate\Routing\Redirector|View|static
     * @throws \Throwable
     */
    public function saveCheckoutMessages(Request $request, $type)
    {
        $rules = [];
        switch ($type) {
            case 'email':
                $rules = MessengerBot::EMAIL_MESSAGES_REQUEST_RULES;
                break;
            case 'phone':
                $rules = MessengerBot::PHONE_MESSAGES_REQUEST_RULES;
                break;
            case 'address':
                $rules = MessengerBot::ADDRESS_MESSAGES_REQUEST_RULES;
                break;
            case 'name':
                $rules = MessengerBot::NAME_MESSAGES_REQUEST_RULES;
                break;
            case 'cart_details':
                $rules = MessengerBot::CART_DETAILS_MESSAGES_REQUEST_RULES;
                break;
            case 'product_details':
                $rules = MessengerBot::PRODUCTS_DETAILS_MESSAGES_REQUEST_RULES;
                break;
        }

        try {
            if (!empty($rules)) {
                $validation = Validator::make($request->all(), $rules);
                if ($validation->fails()) {
                    throw new Error(__('global.all_fields_required'));
                }
            }

            \Illuminate\Support\Facades\DB::transaction(function () use ($request): void {
                $this->manager->updateSettings($request->all());
            });

            return ResponseAlias::create([
                'msg' => __('apps.successfully.configured'),
                'status' => 'success'
            ]);
        } catch (Exception $exception) {
            return ResponseAlias::create([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }
    }
}
