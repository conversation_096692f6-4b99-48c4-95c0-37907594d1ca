<?php

declare(strict_types=1);

namespace App\Integration\Facebook\Http\Controllers;

use App\Exceptions\Error;
use App\Helper\Plan;
use App\Http\Controllers\Controller;
use App\Models\Apps\Applications;
use App\Models\Order\Order;
use App\Models\Router\Logs;
use Auth;
use Carbon\Carbon;
use Closure;
use FacebookAds\Api;
use FacebookAds\Logger\CurlLogger;
use FacebookAds\Object\ServerSide\ActionSource;
use FacebookAds\Object\ServerSide\Content;
use FacebookAds\Object\ServerSide\CustomData;
use FacebookAds\Object\ServerSide\Event;
use FacebookAds\Object\ServerSide\EventRequestAsync;
use FacebookAds\Object\ServerSide\UserData;
use GDPR;
use Illuminate\Config\Repository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Optional;
use Illuminate\Support\Str;
use Modules\Apps\Exports\XmlFeed\Models\Feed;
use Modules\Apps\Exports\XmlFeed\XmlFeed;
use stdClass;
use Throwable;

class PixelController extends Controller
{
    public const GDPR_COOKIE_GROUP = 'targeting';

    /**
     * @var null|XmlFeed $manager
     */
    protected ?\App\Contracts\AppsContract $manager = null;

    /**
     * @var null|string $pixelKey
     */
    protected $pixelKey;

    /**
     * @var null|string $token
     */
    protected $token;

    /**
     * @var bool $allowCapi
     */
    protected $allowCapi;

    /**
     * @var null|string $token
     */
    protected $testEventCode;

    public function __construct()
    {
        $this->middleware(function (Request $request, Closure $next) {
            if (empty($request->input('_token'))) {
                return response()
                    ->json([])->withHeaders([
                        'X-Robots-Tag' => 'noindex, nofollow',
                    ]);
            }

            return $next($request);
        });
    }

    /**
     * @param Illuminate\Http\Request $request
     * @param mixed $event
     * @return mixed
     */
    public function index(Request $request, $event): JsonResponse
    {
        $response = [];
        if (
            $event &&
            is_scalar($event) &&
            method_exists($this, $method = sprintf('_formatEvent%s', \Illuminate\Support\Str::studly($event))) &&
            $this->allowModifyPixelResponse()
        ) {

            $response = $this->$method($request->input('data'));
        }

        return response()
            ->json(['js-callback' => $response])
            ->withHeaders([
                'X-Robots-Tag' => 'noindex, nofollow',
            ]);
    }

    /****************************************** helpers ******************************************/

    /**
     * @return null|XmlFeed
     */
    protected function getManager(): ?XmlFeed
    {
        if (is_null($this->manager)) {
            $this->manager = Applications::with('feed')->where('key', Feed::FEED_PREFIX . 'facebook')->first()->feed->manager ?? false;
        }

        return $this->manager ?: null;
    }

    /**
     * @return null|string
     */
    protected function getPixelKey(): ?string
    {
        if (is_null($this->pixelKey)) {
            try {
                $this->pixelKey = ($manager = $this->getManager()) && $manager->getSetting('pixel') ? $manager->getSetting('pixel') : false;
            } catch (Throwable) {
                $this->pixelKey = false;
            }
        }

        return $this->pixelKey ?: null;
    }

    /**
     * @return null|string
     */
    protected function getToken()
    {
        if (is_null($this->token)) {
            try {
                $this->token = ($manager = $this->getManager()) && $manager->getSetting('token') ? $manager->getSetting('token') : false;
            } catch (Throwable) {
                $this->token = false;
            }
        }

        return $this->token ?: null;
    }

    /**
     * @return null|string
     */
    protected function allowCapi()
    {
        if (!$this->getToken()) {
            return false;
        }

        if (is_null($this->allowCapi)) {
            try {
                $this->allowCapi = ($manager = $this->getManager()) &&
                $manager->getSetting('capi_status') &&
                GDPR::hasAcceptedCookieGroup(self::GDPR_COOKIE_GROUP) ?
                    $manager->getSetting('capi_status') : false;
            } catch (Throwable) {
                $this->allowCapi = false;
            }
        }

        return $this->allowCapi ?: null;
    }

    /**
     * @return null|string
     */
    protected function getTestEventCode()
    {
        if (is_null($this->testEventCode)) {
            try {
                $this->testEventCode = ($manager = $this->getManager()) && $manager->getSetting('test_event_code') ? $manager->getSetting('test_event_code') : false;
            } catch (Throwable) {
                $this->testEventCode = false;
            }
        }

        return $this->testEventCode ?: null;
    }

    /**
     * @return bool
     */
    protected function allowModifyPixelResponse(): bool
    {
        try {
            return ($manager = $this->getManager()) &&
                $manager->isInstalled() &&
                $this->getPixelKey() &&
                GDPR::hasAcceptedCookieGroup(self::GDPR_COOKIE_GROUP);
        } catch (Error) {
            return false;
        }
    }

    /****************************************** formatters ******************************************/
    /**
     * @param mixed $data
     * @return mixed
     */
    protected function _formatEventViewPage($data): array
    {
        if (empty($data['url'])) {
            return [];
        }

        $eventId = md5(microtime() . mt_rand());

        //        $this->fireEvent(new Repository([
        //            'eventName' => 'PageView',
        //            'eventID' => $eventId,
//                    'external_id' => $this->getExternalId(),
        //            'url' => $data['url'] ?? null,
        ////            'currency' => site('currency'),
        ////            'language' => site('language'),
        //        ]));

        return [
            'FbPixel' => [
                [
                    'method' => 'track',
                    'arguments' => [
                        'PageView', [
                            'eventID' => $eventId,
                            'external_id' => $this->getExternalId(),
//                            'currency' => site('currency'),
//                            'language' => site('language'),
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    protected function _formatEventProductView($data): array
    {
        $contents = $this->generateContents(['products' => [$data]], 'view');
        /** @var Content $content */
        $content = \Illuminate\Support\Arr::first($contents);
        if (empty($content)) {
            return [];
        }

        $contentsArray = array_map(fn (Content $content) => with(clone $content)->normalize(), $contents);

        $eventId = md5($content->getProductId() . microtime() . mt_rand());

        $this->fireEvent(new Repository([
            'contents' => [$content],
            'content_ids' => [$content->getProductId()],
            'eventName' => 'ViewContent',
            'url' => $data['url'] ?? null,
            'eventID' => $eventId,
            'external_id' => $this->getExternalId(),
            'content_name' => $content->getTitle(),
            'content_type' => 'product',
            'currency' => site('currency'),
            'language' => site('language'),
        ]));

        return [
            'FbPixel' => [
                [
                    'method' => 'track',
                    'arguments' => [
                        'ViewContent', [
                            'eventID' => $eventId,
                            'external_id' => $this->getExternalId(),
                            'value' => $content->getItemPrice(),
                            'currency' => site('currency'),
                            'language' => site('language'),
                            'content_name' => $content->getTitle(),
                            'content_category' => $content->getCategory(),
                            'content_ids' => [$content->getProductId()],
                            'content_type' => 'product',
                            'contents' => $contentsArray,
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    protected function _formatEventProductsSearch($data): array
    {
        $contents = $this->generateContents($data);
        if (empty($contents)) {
            return [];
        }

        $contentsArray = array_map(fn (Content $content) => with(clone $content)->normalize(), $contents);

        $eventId = md5(microtime() . mt_rand());

        $ids = array_column($contentsArray, 'id');

        if (empty($ids)) {
            return [];
        }

        $this->fireEvent(new Repository([
            'contents' => $contents,
            'content_ids' => $ids,
            'eventName' => 'Search',
            'url' => $data['url'] ?? null,
            'eventID' => $eventId,
            'external_id' => $this->getExternalId(),
            'search_string' => $data['query_string'] ?? null,
            'content_type' => 'product_group',
//            'currency' => site('currency'),
//            'language' => site('language'),
        ]));

        return [
            'FbPixel' => [
                [
                    'method' => 'track',
                    'arguments' => [
                        'Search', [
                            'eventID' => $eventId,
                            'external_id' => $this->getExternalId(),
//                            'currency' => site('currency'),
//                            'language' => site('language'),
                            'search_string' => $data['query_string'] ?? null,
                            'content_type' => 'product_group',
                            'content_ids' => $ids,
                            'contents' => $contentsArray,
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    protected function _formatEventProductsGroups($data): array
    {
        $contents = $this->generateContents($data);
        if (empty($contents)) {
            return [];
        }

        $contentsArray = collect(array_map(fn (Content $content) => with(clone $content)->normalize(), $contents));

        $eventId = md5(microtime() . mt_rand());

        $ids = $contentsArray->pluck('id')->all();

        if (empty($ids)) {
            return [];
        }

        $value = $contentsArray->sum(function ($content): int|float {
            if (!empty($content['item_price']) && !empty($content['quantity']) && is_numeric($content['item_price']) && is_numeric($content['quantity'])) {
                return $content['item_price'] * $content['quantity'];
            }

            return 0;
        });

        $this->fireEvent(new Repository([
            'contents' => $contents,
            'content_ids' => $ids,
            'eventName' => 'ViewContent',
            'url' => $data['url'] ?? null,
            'eventID' => $eventId,
            'external_id' => $this->getExternalId(),
            'content_name' => $data['name'] ?? null,
            'content_type' => 'product_group',
//            'currency' => site('currency'),
//            'language' => site('language'),
            'value' => $value,
        ]));

        return [
            'FbPixel' => [
                [
                    'method' => 'track',
                    'arguments' => [
                        'ViewContent', [
                            'eventID' => $eventId,
                            'external_id' => $this->getExternalId(),
//                            'currency' => site('currency'),
//                            'language' => site('language'),
                            'content_name' => $data['name'] ?? null,
                            'content_type' => 'product_group',
                            'content_ids' => $ids,
                            'contents' => $contentsArray,
                            'value' => $value,
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    protected function _formatEventCheckout($data): array
    {
        if (empty($data['products'])) {
            return [];
        }

        $contents = $this->generateContents(['products' => $data['products']], 'checkout');
        if (empty($contents)) {
            return [];
        }

        $contentsArray = collect(array_map(fn (Content $content) => with(clone $content)->normalize(), $contents));

        $ids = $contentsArray->pluck('id')->all();

        $eventId = md5($contentsArray->implode('id', '_') . '_' . ($data['id'] ?? null));

        $this->fireEvent(new Repository([
            'contents' => $contents,
            'content_ids' => $ids,
            'eventName' => 'InitiateCheckout',
            'url' => route('checkout'),
            'eventID' => $eventId,
            'external_id' => $this->getExternalId(),
            'content_type' => 'product',
            'content_name' => count($contents) == 1 ? $contents[0]->getTitle() : null,
            'currency' => site('currency'),
            'language' => site('language'),
            'value' => $data['total'] ?? 0,
            'customer_email' => $data['customer']['email'] ?? null,
        ]));

        return [
            'FbPixel' => [
                [
                    'method' => 'track',
                    'arguments' => [
                        'InitiateCheckout', [
                            'eventID' => $eventId,
                            'external_id' => $this->getExternalId(),
                            'value' => $data['total'] ?? 0,
                            'currency' => site('currency'),
                            'language' => site('language'),
                            'content_ids' => $ids,
                            'content_name' => count($contents) == 1 ? $contents[0]->getTitle() : null,
                            'content_type' => 'product',
                            'contents' => $contentsArray->all(),
                            'num_items' => count($ids),
                            'customer_email' => hash('sha256', Str::lower((string) (Auth::customer()->email ?? $data['customer']['email'] ?? null))),
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param mixed $data
     * @param mixed $event
     * @return mixed
     */
    protected function _formatEventInitiateCheckout($data, $event = 'InitiateCheckout'): array
    {
        if (empty($data['products'])) {
            return [];
        }

        $contents = $this->generateContents(['products' => $data['products']], 'checkout');
        if (empty($contents)) {
            return [];
        }

        $contentsArray = collect(array_map(fn (Content $content) => with(clone $content)->normalize(), $contents));

        $ids = $contentsArray->pluck('id')->all();

        $eventId = md5($contentsArray->implode('id', '_') . '_' . ($data['id'] ?? null));

        $this->fireEvent(new Repository([
            'contents' => $contents,
            'content_ids' => $ids,
            'eventName' => $event,
            'url' => route('checkout'),
            'eventID' => $eventId,
            'external_id' => $this->getExternalId(),
            'content_type' => 'product',
            'content_name' => count($contents) == 1 ? $contents[0]->getTitle() : null,
            'currency' => site('currency'),
            'language' => site('language'),
            'value' => $data['total'] ?? 0,
            'customer_email' => $data['customer']['email'] ?? null,
        ]));

        return [
            'FbPixel' => [
                [
                    'method' => 'track',
                    'arguments' => [
                        $event, [
                            'eventID' => $eventId,
                            'external_id' => $this->getExternalId(),
                            'value' => $data['total'] ?? 0,
                            'currency' => site('currency'),
                            'language' => site('language'),
                            'content_ids' => $ids,
                            'content_name' => count($contents) == 1 ? $contents[0]->getTitle() : null,
                            'content_type' => 'product',
                            'contents' => $contentsArray->all(),
                            'num_items' => count($ids),
                            'customer_email' => hash('sha256', Str::lower((string) (Auth::customer()->email ?? $data['customer']['email'] ?? null))),
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    protected function _formatEventInitiateFastCheckout($data): array
    {
        return $this->_formatEventInitiateCheckout($data, 'InitiateFastCheckout');
    }

    /**
     * @param mixed $data
     * @param mixed $event
     * @return mixed
     */
    protected function _formatEventPurchase($data, $event = 'Purchase'): array
    {
        if (empty($data['products'])) {
            return [];
        }

        $contents = $this->generateContents(['products' => $data['products']], 'checkout');
        if (empty($contents)) {
            return [];
        }

        $contentsArray = collect(array_map(fn (Content $content) => with(clone $content)->normalize(), $contents));

        $ids = $contentsArray->pluck('id')->all();

        $eventId = md5($contentsArray->implode('id', '_') . '_' . ($data['id'] ?? null));

        $this->fireEvent(new Repository([
            'contents' => $contents,
            'content_ids' => $ids,
            'eventName' => $event,
            'url' => route('checkout'),
            'eventID' => $eventId,
            'external_id' => $this->getExternalId(),
            'content_type' => 'product',
            'content_name' => count($contents) == 1 ? $contents[0]->getTitle() : null,
            'currency' => site('currency'),
            'language' => site('language'),
            'value' => $data['total'] ?? 0,
            'customer_email' => $data['customer']['email'] ?? null,
        ]));

        return [
            'FbPixel' => [
                [
                    'method' => 'track',
                    'arguments' => [
                        $event, [
                            'eventID' => $eventId,
                            'external_id' => $this->getExternalId(),
                            'value' => $data['total'] ?? 0,
                            'currency' => site('currency'),
                            'language' => site('language'),
                            'content_ids' => $ids,
                            'content_name' => count($contents) == 1 ? $contents[0]->getTitle() : null,
                            'content_type' => 'product',
                            'contents' => $contentsArray->all(),
                            'num_items' => count($ids),
                            'customer_email' => hash('sha256', Str::lower((string) (Auth::customer()->email ?? $data['customer']['email'] ?? null))),
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    protected function _formatEventFastPurchase($data): array
    {
        return $this->_formatEventPurchase($data, 'FastPurchase');
    }

    /**
     * @return array{array{method: 'track', arguments: array{'Purchase', array{eventID: (lowercase-string & non-falsy-string), value: mixed, language: mixed, currency: mixed, content_ids: mixed, content_name: mixed, content_type: 'product', contents: mixed, num_items: int<0, max>}}}}[]
     * @param mixed $data
     */
    protected function _formatEventCheckoutReturn($data): array
    {
        if (($data['status'] ?? 'cancel') == 'cancel' || empty($data['id'])) {
            return [];
        }

        if (empty($data['products'])) {
            return [];
        }

        $contents = $this->generateContents(['products' => $data['products']], 'checkout');
        if (empty($contents)) {
            return [];
        }

        $order = $this->checkOrderMeta($data['id']);
        if (!$order || $order->meta_single) {
            return [];
        }

        $order->createMeta('facebook_pixel_tracking', 'track');

        $contentsArray = collect(array_map(fn (Content $content) => with(clone $content)->normalize(), $contents));

        $ids = $contentsArray->pluck('id')->all();

        $eventId = md5((string) $data['id']);
        $this->fireEvent(new Repository([
            'contents' => $contents,
            'content_ids' => $ids,
            'eventName' => 'Purchase',
            'url' => $data['url'] ?? null,
            'eventID' => $eventId,
            'external_id' => $this->getExternalId(),
            'content_type' => 'product',
            'content_name' => count($contents) == 1 ? $contents[0]->getTitle() : null,
            'language' => $data['locale'] ?? site('language'),
            'currency' => $data['currency'] ?? site('currency'),
            'value' => $data['total'] ?? 0,
            'customer_phone' => $data['chosen_shipping']['phone'] ?? null,
            'customer_email' => $data['customer']['email'] ?? null,
        ]));

        return [
            'FbPixel' => [
                [
                    'method' => 'track',
                    'arguments' => [
                        'Purchase', [
                            'eventID' => $eventId,
                            'external_id' => $this->getExternalId(),
                            'value' => $data['total'] ?? 0,
                            'language' => $data['locale'] ?? site('language'),
                            'currency' => $data['currency'] ?? site('currency'),
                            'content_ids' => $ids,
                            'content_name' => count($contents) == 1 ? $contents[0]->getTitle() : null,
                            'content_type' => 'product',
                            'contents' => $contentsArray->all(),
                            'num_items' => count($ids),
                            'customer_email' => hash('sha256', Str::lower((string) (Auth::customer()->email ?? $data['customer']['email'] ?? null))),
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    protected function _formatEventAddToCart($data): array
    {
        $bundle = $data['type'] == 'bundle';
        if (empty($data['product'])) {
            return [];
        }

        $products = $bundle ? ($data['product'] ?? []) : [($data['product'] ?? [])];

        $contents = $this->generateContents(['products' => $products], 'cart');
        if (empty($contents)) {
            return [];
        }

        $contentsArray = collect(array_map(fn (Content $content) => with(clone $content)->normalize(), $contents));

        $eventId = md5(microtime() . mt_rand());

        $ids = $contentsArray->pluck('id')->all();
        $value = $contentsArray->sum(function ($content): int|float {
            if (!empty($content['item_price']) && !empty($content['quantity'])) {
                return $content['item_price'] * $content['quantity'];
            }

            return 0;
        });

        $this->fireEvent(new Repository([
            'contents' => $contents,
            'content_ids' => $ids,
            'eventName' => 'AddToCart',
            'url' => $data['url'] ?? $products[0]['url'] ?? null,
            'eventID' => $eventId,
            'external_id' => $this->getExternalId(),
            'content_type' => 'product',
            'content_name' => count($contents) == 1 ? $contents[0]->getTitle() : null,
            'currency' => site('currency'),
            'value' => moneyInput($value * 100)
        ]));

        return [
            'FbPixel' => [
                [
                    'method' => 'AddToCart',
                    'arguments' => [
                        [
                            'eventID' => $eventId,
                            'external_id' => $this->getExternalId(),
                            'value' => moneyInput($value * 100),
                            'currency' => site('currency'),
                            'content_ids' => $ids,
                            'content_name' => count($contents) == 1 ? $contents[0]->getTitle() : null,
                            'content_type' => 'product',
                            'contents' => $contentsArray->all(),
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    protected function _formatEventAddToWishlist($data): array
    {
        if (empty($data['id']) || empty($data['name'])) {
            return [];
        }

        $contents = $this->generateContents(['products' => [$data]]);
        /** @var Content $content */
        $content = \Illuminate\Support\Arr::first($contents);
        if (empty($content)) {
            return [];
        }

        $contentsArray = array_map(fn (Content $content) => with(clone $content)->normalize(), $contents);

        $eventId = md5($content->getProductId() . microtime() . mt_rand());

        $this->fireEvent(new Repository([
            'contents' => [$content],
            'content_ids' => [$content->getProductId()],
            'eventName' => 'AddToWishlist',
            'url' => $data['url'] ?? null,
            'eventID' => $eventId,
            'external_id' => $this->getExternalId(),
            'content_name' => $content->getTitle(),
            'content_type' => 'product',
            'currency' => site('currency'),
        ]));

        return [
            'FbPixel' => [
                [
                    'method' => 'AddToWishlist',
                    'arguments' => [
                        [
                            'eventID' => $eventId,
                            'external_id' => $this->getExternalId(),
                            'value' => $content->getItemPrice(),
                            'currency' => site('currency'),
                            'content_name' => $content->getTitle(),
                            'content_category' => $content->getCategory(),
                            'content_ids' => [$content->getProductId()],
                            'content_type' => 'product',
                            'contents' => $contentsArray,
                        ]
                    ]
                ]
            ]
        ];
    }

    /****************************************** server side ******************************************/

    /**
     * @return array|Content[]
     * @param mixed $data
     * @param null|mixed $formatType
     */
    protected function generateContents($data, $formatType = null): array
    {
        $contents = [];
        foreach (is_array($data['products'] ?? null) ? $data['products'] : [] as $product) {
            /** @var stdClass $product */
            $product = new Optional(json_decode(json_encode($product)));
            $content = (new Content())
//                ->setProductId(in_array($formatType, ['cart']) ? ($product->parameter_id ? : $product->product_id) : ($product->parameter_id ?? $product->id))
                ->setProductId(in_array($formatType, ['cart']) ? $product->product_id : $product->id)
                ->setTitle($product->name)
                ->setBrand($product->brand)
                ->setCategory($product->category_path)
                ->setQuantity(is_numeric($product->quantity) ? (int)$product->quantity : 1);

            if (in_array($formatType, ['cart', 'checkout'])) {
                $content->setItemPrice(moneyFloat($this->validateNumeric($product->total_price_per_item->price ?? 0)));
            } else {
                $content->setItemPrice($this->validateNumeric($product->discount_price > 0 ? $product->discount_price : $product->price));
            }

            $contents[] = $content;
        }

        return $contents;
    }

    /**
     * @param Repository $repo
     */
    protected function fireEvent(Repository $repo)
    {
        if (
            !Plan::enabled('facebook.capi') ||
            !$this->allowCapi() ||
            (!($repo->get('eventName') == 'PageView') && empty($repo->get('contents')) && empty($repo->get('content_ids'))) ||
            empty($repo->get('eventName')) ||
            empty($repo->get('eventName'))
        ) {
            return;
        }

        //register_shutdown_function(function () use ($repo) {
        try {
            $api = Api::init(null, null, $this->getToken(), inDevelopment());
            $api->setLogger(new CurlLogger());

            $customer = Auth::customer();

            $user_data = (new UserData())
                ->setClientIpAddress(request()->ip())
//                    ->setClientUserAgent(request()->userAgent())
                ->setFbp($this->getFbp());

            if ($repo->get('external_id')) {
                $user_data->setExternalId($repo->get('external_id'));
            }

            // Check for email from authenticated customer first, then from order data for guest users
            $email = $customer->email ?? $repo->get('customer_email');
            if ($email) {
                $user_data->setEmail($email);
            }

            if ($repo->get('customer_phone')) {
                $user_data->setPhone($repo->get('customer_phone'));
            }

            if ($_COOKIE['_fbc'] ?? null) {
                $user_data->setFbc($_COOKIE['_fbc']);
            }

            $userAgent = request()->userAgent();
            if (preg_match('/(iPhone OS (\d+)_(\d+))/i', (string) $userAgent, $match)) {
                if ($match[2] >= 14) {
                    $userAgent = str_ireplace($match[0], 'iPhone OS 13_2', $userAgent);
                }
            }

            $user_data->setClientUserAgent($userAgent);
            //                if($userAgent && strpos(strtolower($userAgent), 'ios') !== false) {
            //                    $userAgent = "Mozilla/5.0 (Linux; Android 10; motorola one vision Build/QSAS30.62-33-14; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.210 Mobile Safari/537.36 GSA/**********.arm64";
            //                }
            //                $user_data->setClientUserAgent($userAgent);

            $custom_data = (new CustomData())
                ->setCurrency($repo->get('currency', site('currency')));

            if ($repo->get('contents')) {
                $custom_data->setContents($repo->get('contents'));
            }

            if ($repo->get('content_ids')) {
                $custom_data->setContentIds($repo->get('content_ids'));
            }

            if ($repo->get('content_name')) {
                $custom_data->setContentName($repo->get('content_name'));
            }

            if ($repo->get('content_type')) {
                $custom_data->setContentType($repo->get('content_type'));
            }

            if ($repo->get('search_string')) {
                $custom_data->setSearchString($repo->get('search_string'));
            }

            if ($repo->get('value')) {
                $custom_data->setValue($repo->get('value'));
            }

            $event = (new Event())
                ->setEventId($repo->get('eventID'))
                ->setEventName($repo->get('eventName'))
                ->setEventTime(Carbon::now()->timestamp)
                ->setEventSourceUrl($repo->get('url'))
                ->setUserData($user_data)
                ->setCustomData($custom_data)
                ->setActionSource(ActionSource::WEBSITE);

            // get my ip
            $events = [];
            array_push($events, $event);

            $request = (new EventRequestAsync($this->getPixelKey()))
                ->setEvents($events);

            if ($this->getTestEventCode()) {
                $request->setTestEventCode($this->getTestEventCode());
            }

            $request->execute();

            //                if(site('site_id') == (inDevelopment() ? 2160 : 1846)) {
            //                    Logs::create([
            //                        'message' => 'Facebook CAPI log: ' . $repo->get('eventName'),
            //                        'file' => __FILE__,
            //                        'trace' => var_export(with(clone $event)->normalize(), true),
            //                        'data' => request()->all(),
            //                    ]);
            //                }

        } catch (Throwable $throwable) {
            $this->logError($throwable);
        }

        // });
    }

    /**
     * @param Throwable $e
     * @return mixed
     */
    protected function logError(\Throwable $e)
    {
        Logs::createFromThrowable($e, 'Facebook CAPI error', null, [
            'data' => request()->all(),
        ]);
    }

    protected function getFbp()
    {
        if ($_COOKIE['_fbp'] ?? null) {
            return $_COOKIE['_fbp'] ?? null;
        } elseif (($fbp = session('_fbp')) && $this->validateFbp($fbp)) {
            return $this->storeFbp($fbp);
        } else {
            return $this->storeFbp(sprintf(
                'fb.1.%s.%s',
                number_format(microtime(true) * 1000, 0, '', ''),
                $this->mt_rand(10)
            ));
        }
    }

    /**
     * @param mixed $fbp
     * @return mixed
     */
    protected function validateFbp($fbp): int|false
    {
        return preg_match('/^fb\.1\.([\d]{13})\.([\d]{10})$/', (string) $fbp);
    }

    /**
     * @param mixed $fbp
     * @return mixed
     */
    protected function storeFbp($fbp)
    {
        session()->put('_fbp', $fbp);
        @setcookie("_fbp", (string) $fbp, ['expires' => \Carbon\Carbon::now()->timestamp + (86400 * 14), 'path' => '/']);

        return $fbp;
    }

    /**
     * @param mixed $length
     * @return mixed
     */
    protected function mt_rand($length): string
    {
        $out = [];
        for ($i = 0; $i < $length; $i++) {
            if ($i) {
                $out[] = mt_rand(0, 9);
            } else {
                $out[] = mt_rand(1, 9);
            }
        }

        return implode('', $out);
    }

    /**
     * @return null|Order
     * @param mixed $id
     */
    protected function checkOrderMeta($id)
    {
        return Order::with(['meta_single' => function ($query): void {
            $query->where('parameter', 'facebook_pixel_tracking');
        }])->find($id, ['id']);
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    protected function validateNumeric($data)
    {
        if (is_numeric($data)) {
            return $data;
        }

        return 0;
    }

    /**
     * @return null|string
     */
    protected function getExternalId(): ?string
    {
        return request()->cookie('uuid');
    }
}
