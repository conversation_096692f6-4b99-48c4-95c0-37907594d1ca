<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: user
 * Date: 11/24/2017
 * Time: 1:27 PM
 */

namespace App\Integration\Facebook\Http\Controllers;

use App\Helper\Format;
use App\Helper\Grid;
use App\Helper\SiteCp\Tools;
use App\Helper\YesNo;
use App\Http\Controllers\Controller;
use App\Integration\Facebook\Apps\MessengerBot;
use App\Integration\Facebook\Models\MessengerMessage;
use App\Integration\Facebook\Models\MessengerSentMessage;
use App\Integration\Facebook\Models\MessengerSetting;
use App\Integration\Facebook\Requests\CustomMessageRequest;
use Modules\Marketing\Segments\Core\Models\SubscribersSegment;
use Illuminate\Http\Request;
use App\Integration\Facebook\Models\MessengerSubscriber;
use Illuminate\Http\Response;
use Exception;
use Illuminate\Support\Facades\View;
use Illuminate\Http\RedirectResponse;
use Carbon\Carbon;
use FacebookPage;
use Facebook;
use App\Exceptions\Error;

/**
 * Class MessengerController
 * @package App\Integration\Facebook\Http\Controllers
 */
class SubscribersController extends Controller
{
    /**
     * @var
     */
    protected $configFacebookUrl;

    /**
     * SubscribersController constructor.
     * @param MessengerBot $manager
     */
    public function __construct(protected \App\Integration\Facebook\Apps\MessengerBot $manager)
    {
        $next = 'messenger.bot.settings';
        if (!Facebook::connected()) {
            $this->configFacebookUrl = route('facebook.connect', ['next' => $next]);
        } elseif (!FacebookPage::configured()) {
            $this->configFacebookUrl = route('admin.facebook-page.settings', ['next' => $next]);
        }
    }

    /**
     * @return \Illuminate\Contracts\View\View
     */
    public function subscribersMenu()
    {
        return \Illuminate\Support\Facades\View::mainResponse('facebook::subscribers', []);
    }

    /**
     * @param Request $request
     * @return Response
     */
    public function removeSubscriber(Request $request): \Illuminate\Http\Response
    {
        try {
            $customers_ids = $request->input('ids');

            foreach ($customers_ids as $customer_id) {
                MessengerSubscriber::where('customer_id', $customer_id)->delete();
            }

            return new Response([
                'msg' => __('facebook.succ.subscriber.remove'),
                'status' => 'success',
            ]);
        } catch (Exception $exception) {
            return new Response([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }
    }

    /**
     * @return \Illuminate\Contracts\View\View|RedirectResponse|\Illuminate\Routing\Redirector
     * @throws Error
     */
    public function customMessagesInit()
    {
        if ($redirect = $this->validateUrl()) {
            return $redirect;
        }

        return \Illuminate\Support\Facades\View::mainResponse('facebook::bot.settings.custom', [
            'active' => $this->manager->isActive(),
            'next' => 'messenger.subscribers.custom_messages',
        ]);
    }

    /**
     * @return Response
     */
    public function customMessagesGrid()
    {
        $grid = new Grid();

        if ($grid->order == 'name_list' || $grid->order == 'name_grid') {
            $grid->order = 'name';
        }

        $subscribersMessages = MessengerMessage::get()
            ->map(function ($message) {
                $periodical = '';
                if ($message->periodical) {
                    $periodical = ' <i data-original-title="' . __('facebook.tooltip.subscribers.message.periodical') . '" class="fal fa-history tooltips"></i>';
                }

                $message->created_at = new Carbon($message->created_at);
                $messageText = strlen((string) $message->message) > 125 ? mb_substr((string) $message->message, 0, 125) . ' ...' : $message->message;

                $message->message = '<a href="' . route('messenger.subscribers.message', $message->id) . '" data-ajax-panel data-panel-class="wide">' .
                    $messageText . $periodical
                    . '</a>';

                $message->delete = Tools::delete(route('messenger.subscribers.delete_message', $message->id));
                $message->active = Grid::generateBool(route('messenger.subscribers.message.status', $message->id), $message->active);
                $message->date_added_formatted = '<span class="date">' . Format::date($message->created_at) . '</span><br />' .
                    '<span class="time">' . Format::time($message->created_at) . '</span>';

                $message->sent_messages = '<a href="' . route('messenger.subscribers.sent_messages', ['messageId' => $message->id])
                    . '" class="btn btn-white" target="_blank">' . __('facebook.btn.custom_message_sent_to') . '</a>';

                $segments = SubscribersSegment::whereIn('id', explode(',', (string) $message->segments))->pluck('name');
                $message->segments_formatted = '';
                if ($message->segments) {
                    foreach ($segments as $segment) {
                        $message->segments_formatted .= '<span class="count-holder" style="display: inline-table;">' . $segment . '</span>&nbsp;';
                    }
                }

                return $message;
            });

        return $grid->generateIlluminate($subscribersMessages, null, ['records' => $subscribersMessages->count()]);
    }

    /**
     * @param null $messageId
     * @return RedirectResponse|Response|\Illuminate\Routing\Redirector|View
     * @throws Error
     */
    public function showCustomMessage($messageId = null)
    {
        if ($redirect = $this->validateUrl()) {
            return $redirect;
        }

        $message = !is_null($messageId) ? MessengerMessage::find($messageId) : new MessengerMessage();
        $selectedSegments = [];
        if ($messageId) {
            $selectedSegments = SubscribersSegment::whereIn('id', explode(',', (string) $message->segments))->get()->map(fn ($segment): array => [
                'id' => $segment->id,
                'name' => $segment->name,
            ])->toArray();
        }

        return \Illuminate\Support\Facades\View::panel('facebook::bot.messages.custom', [
            'message' => $message,
            'selectedSegments' => json_encode($selectedSegments)
        ]);
    }

    /**
     * @param CustomMessageRequest $request
     * @param null $messageId
     * @return Response
     * @throws Exception
     */
    public function sendCustomMessage(CustomMessageRequest $request, $messageId = null)
    {
        $data = [
            'message' => $request->input('message'),
            'sent_count' => 0,
            'periodical' => $request->input('periodical') ?: 0,
            'periodical_start' => $request->input('periodical_start'),
            'periodical_end' => $request->input('periodical_end'),
            'segments' => $request->input('segment_id'),
        ];

        MessengerMessage::startCampaign($data, $messageId);

        return response([
            'status' => 'success',
            'msg' => __('facebook.succ.subscribers.messages_sent'),
        ]);
    }

    /**
     * @param $id
     * @param $status
     * @return Response
     */
    public function messageStatus($id, $status): \Illuminate\Http\Response
    {
        $active = YesNo::toBool($status);

        MessengerMessage::where('id', $id)->update([
            'active' => $active == true ? 1 : 0
        ]);

        return new Response([
            'status' => 'success',
            'active' => $active
        ]);
    }

    /**
     * @param integer $id
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Symfony\Component\HttpFoundation\Response
     */
    public function deleteMessage($id)
    {
        MessengerMessage::where('id', $id)->delete();

        return response([
            'status' => 'success',
            'msg' => __('customer_group.succ.edit')
        ]);
    }

    /**
     * @param $messageId
     * @return \Illuminate\Contracts\View\View|RedirectResponse|\Illuminate\Routing\Redirector
     * @throws Error
     */
    public function sentMessagesInit($messageId)
    {
        if ($redirect = $this->validateUrl()) {
            return $redirect;
        }

        return \Illuminate\Support\Facades\View::mainResponse('facebook::bot.messages.sent_messages', [
            'active' => $this->manager->isActive(),
            'next' => 'messenger.subscribers.custom_messages',
            'messageId' => $messageId
        ]);
    }

    /**
     * @param $messageId
     * @return Response
     */
    public function sentMessagesGrid($messageId)
    {
        $grid = new Grid();

        if ($grid->order == 'name_list' || $grid->order == 'name_grid') {
            $grid->order = 'name';
        }

        $subscribersMessages = MessengerSentMessage::where('cc_message_id', $messageId)
            ->where('type', MessengerSetting::SUBSCRIBERS_TYPE)
            ->with('subscriber')
            ->get()
            ->map(function ($message): \App\Integration\Facebook\Models\MessengerSentMessage {
                $message->subscriber_username = $message->subscriber->username;
                $message->date_sent_formatted = '<span class="date">' . Format::date($message->date_sent) . '</span><br />' .
                    '<span class="time">' . Format::time($message->date_sent) . '</span>';

                return $message;
            });

        return $grid->generateIlluminate($subscribersMessages, null, ['records' => $subscribersMessages->count()]);
    }

    /**
     * @return \Illuminate\Contracts\View\View|RedirectResponse|\Illuminate\Routing\Redirector
     * @throws Error
     */
    protected function validateUrl()
    {
        if (!$this->manager->isInstalled()) {
            return \Illuminate\Support\Facades\View::mainResponse('facebook::bot.install');
        }

        if ($this->configFacebookUrl) {
            return redirect($this->configFacebookUrl);
        }

        return;
    }
}
