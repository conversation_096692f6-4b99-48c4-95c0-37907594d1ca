<?php

declare(strict_types=1);

namespace App\Integration\Facebook\Providers;

use App\Integration\Facebook\Services\Page;
use Illuminate\Support\ServiceProvider;

/**
 * Class PageServiceProvider
 * @package App\Integration\Facebook\Providers
 */
class PageServiceProvider extends ServiceProvider implements \Illuminate\Contracts\Support\DeferrableProvider
{
    /**
     *
     */
    #[\Override]
    public function register(): void
    {
        $this->app->singleton('fb:page', fn (): \App\Integration\Facebook\Services\Page => new Page(true));
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    #[\Override]
    public function provides()
    {
        return ['fb:page'];
    }
}
