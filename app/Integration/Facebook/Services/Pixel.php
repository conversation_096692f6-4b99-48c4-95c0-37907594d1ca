<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 26.1.2017 г.
 * Time: 23:52 ч.
 */

namespace App\Integration\Facebook\Services;

use Apps;
use App\Helper\Encoders\JavaScript;

//https://developers.facebook.com/docs/facebook-pixel/api-reference#events
//https://www.facebook.com/ads/manager/pixel/facebook_pixel/?act=53393706&pid=p1

//$p = new \App\Integration\Facebook\Pixel();
//dd($p->ViewContent([
//    "content_name" => 'Really Fast Running Shoes',
//    "content_category" => 'Apparel &amp; Accessories > Shoes',
//    "value" => 149.99,
//    "currency" => 'USD'
//]));

class Pixel
{
    protected $active;

    protected $pixel_key;

    protected $code = [];

    protected $noscript = [];

    protected $triggers = [];

    protected $js = [];

    protected $parameters = [
        'value', 'currency', 'content_name',
        'content_category', 'content_ids',
        'content_type', 'num_items',
        'search_string', 'status', 'language'
    ];

    public function __construct()
    {
        $this->active = Apps::installed('app.xml_feed.facebook');
        $this->pixel_key = Apps::setting('app.xml_feed.facebook', 'pixel');
        if (!trim($this->pixel_key)) {
            $this->active = false;
        }
    }

    //    public function ViewPage(...$attr) {
    //        return $this->call(__FUNCTION__, $attr);
    //    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function ViewContent(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function PageView(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function Search(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function ViewCategory(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function AddToCart(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function AddToWishlist(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function InitiateCheckout(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function InitiateFastCheckout(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function AddPaymentInfo(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function Purchase(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function FastPurchase(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param array ...$attr
     * @return Pixel
     */
    public function Lead(...$attr): static
    {
        return $this->call(__FUNCTION__, $attr);
    }

    /**
     * @param $name
     * @param \Closure $callback
     * @param array $args
     * @param string $selector
     * @return $this
     */
    public function trigger($name, \Closure $callback, array $args = [], $selector = 'js:document'): static
    {
        if ($this->active) {
            $this->triggers[] = [
                'name' => $name,
                'callback' => $callback,
                'args' => $args,
                'selector' => $selector
            ];
        }

        return $this;
    }

    /**
     * @param mixed $js
     * @return mixed
     */
    public function js($js): static
    {
        if ($this->active) {
            $this->js[] = $js;
        }

        return $this;
    }

    /**
     * @return null|string
     */
    public function render(): ?string
    {
        if (!$this->code) {
            return null;
        }

        $code = $this->head();
        $code .= $this->renderClear();
        $code .= $this->foot();

        return $code;
    }

    /**
     * @return null|string
     */
    public function renderClear(): ?string
    {
        if (!$this->code) {
            return null;
        }

        $code = $this->renderCode();
        $code .= $this->_renderTriggers();
        $code .= $this->_renderJs();

        return $code;
    }

    /**
     * @return string
     */
    public function renderCode(): string
    {
        return "if(window.fbq_init) {\n" . implode("\n", $this->code) . "\n}\n";
    }

    /**
     * @param $event
     * @param array $params
     * @return $this
     */
    private function call(string $event, array $params = []): static
    {
        if ($this->active) {
            if (isset($params[0])) {
                $params = $params[0];
            }

            if ($params) {
                $this->code[$event] = "fbq('track', '" . $event . "', " . JavaScript::encode($params) . ");\n";
            } else {
                $this->code[$event] = "fbq('track', '" . $event . "');\n";
            }

            $this->noscript[$event] = $params;
        }

        return $this;
    }

    private function head(): string
    {
        return '<!— Facebook Pixel Code -->
<script type="text/javascript">
!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;
n.push=n;n.loaded=!0;n.version=\'2.0\';n.agent=\'plcloudcart\';n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window,
document,\'script\',\'//connect.facebook.net/en_US/fbevents.js\');
window.fbq_init = false;
if(fbq) {
window.fbq_init = true;
fbq(\'init\', ' . json_encode($this->pixel_key) . ');' . "\n}\n";
    }

    /**
     * @return string
     */
    private function foot(): string
    {
        return '</script>' . $this->_noScript() . '<!— End Facebook Pixel Code -->' . "\n";
    }

    /**
     * @return string
     */
    private function _noScript(): ?string
    {
        return null;
        $return = '<noscript>' . "\n";
        foreach ($this->noscript as $event => $parameters) {
            $parameters = $this->_parametersToString($parameters);
            $return .= "<img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id={$this->pixel_key}&ev={$event}&amp;{$parameters}\"/>\n";
        }

        $return .= '</noscript>';
        return $return;
    }

    /**
     * @return string
     */
    private function _renderTriggers(): string
    {
        $return = '';
        foreach ($this->triggers as $r => $trigger) {
            $instance = new static();
            $trigger['callback']($instance);
            $return .= "\$(" . JavaScript::encode($trigger['selector']) . sprintf(").off('%s.f%s').on('%s.f%s', function(", $trigger['name'], $r, $trigger['name'], $r) . implode(', ', array_merge(['e'], $trigger['args'])) . ") {
                " . $instance->renderCode() . "
            });\n";
        }

        return $return;
    }

    /**
     * @return string
     */
    private function _renderJs(): string
    {
        return implode("\n", $this->js);
    }

    /**
     * @param array $parameters
     * @return string
     */
    private function _parametersToString($parameters): string
    {
        $return = '';
        if (is_array($parameters) && $parameters) {
            $return .= '&' . str_replace([' ', '&'], ['%20', '&amp;'], urldecode(http_build_query(['cd' => $parameters])));
        }

        return $return;
    }

}
