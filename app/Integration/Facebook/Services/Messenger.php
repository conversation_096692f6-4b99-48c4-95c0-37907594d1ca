<?php

declare(strict_types=1);

/**
 * Created by <PERSON>p<PERSON>tor<PERSON>.
 * User: Gen
 * Date: 29.11.2017 г.
 * Time: 15:00
 */

namespace App\Integration\Facebook\Services;

use App\Helper\Widget;
use App\Integration\Facebook\Apps\MessengerBot;
use App\Integration\Facebook\Apps\MessengerChat;
use App\Integration\Facebook\Models\MessengerSentMessage;
use App\Integration\Facebook\Models\MessengerSetting;
use App\Integration\Facebook\Models\MessengerSubscriber;
use App\Models\Mongo\MessengerMessage;
use App\Models\Oauth\MessengerCommand;
use App\Models\Order\Order;
use App\Models\Order\OrderProduct;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\Queue\SiteQueue;
use Illuminate\Database\Eloquent\Collection;
use Carbon\Carbon;
use Auth;
use Illuminate\Support\Facades\View;
use Facebook;
use FacebookPage;
use App\Exceptions\Error;
use App\Common\Status;
use App\Models\Store\Cart as CartModel;

/**
 * Class Messenger
 * @package App\Integration\Facebook\Services
 */
class Messenger
{
    /**
     *
     */
    public const KEY = 'messenger';

    /**
     *
     */
    public const UTM = '?utm_source=CloudIO&utm_medium=messenger&utm_campaign=conversation&utm_content=';

    /**
     *
     */
    public const SIMPLE_MESSAGE_TYPE = 'simple_message';

    /**
     *
     */
    public const PRODUCTS_TYPE = 'products';

    /**
     *
     */
    public const VENDORS_TYPE = 'vendors';

    /**
     *
     */
    public const CATEGORIES_TYPE = 'categories';

    /**
     *
     */
    public const CATEGORY_PRODUCTS_TYPE = 'category_products';

    /**
     *
     */
    public const SUBCATEGORIES_TYPE = 'subcategories';

    /**
     * @var \Illuminate\Config\Repository|mixed
     */
    protected $appId;

    /**
     * @var mixed
     */
    protected $pageId;

    /**
     * @var mixed
     */
    protected $pageAccessToken;

    /**
     * @var \Illuminate\Config\Repository|mixed
     */
    protected $graphVersion;

    protected array $persistentMenu = [
        'first_item' => null,
        'second_item' => null,
    ];

    protected array $quickReplies = [
        'first_item' => null,
        'second_item' => null,
        'third_item' => null,
        'fourth_item' => null,
        'fifth_item' => null,
        'sixth_item' => null,
        'seventh_item' => null,
        'eighth_item' => null,
        'ninth_item' => null,
        'tenths_item' => null,
        'eleventh_item' => null,
    ];

    /**
     * @var MessengerChat
     */
    protected $chatManager;

    /**
     * @var MessengerBot
     */
    protected $botManager;


    /**
     * Messenger constructor.
     * @throws Error
     */
    public function __construct()
    {
        $this->appId = Facebook::graph()->getApp()->getId();
        $this->graphVersion = Facebook::graph()->getDefaultGraphVersion();
        $this->pageId = FacebookPage::pageId();
        $this->pageAccessToken = FacebookPage::accessToken();

        $this->botManager = app('messenger_bot');
        $this->chatManager = app('messenger_chat');

        $persistentMenu = (array)json_decode((string) $this->botManager->getSetting(MessengerBot::PERSISTENT_MENU));
        $this->persistentMenu = $persistentMenu + $this->persistentMenu;

        $quickReplies = (array)json_decode((string) $this->botManager->getSetting(MessengerBot::QUICK_REPLY));
        $this->quickReplies = $quickReplies + $this->quickReplies;
    }

    /**
     * @return MessengerChat
     */
    public function getChatManager()
    {
        return $this->chatManager;
    }

    /**
     * @return MessengerBot
     */
    public function getBotManager()
    {
        return $this->botManager;
    }

    /**
     * @param null $type
     * @return array|mixed
     */
    public function getPersistentMenu($type = null)
    {
        return $type ? $this->persistentMenu[$type] : $this->persistentMenu;
    }

    /**
     * @return int|null|string
     */
    public function getPersistentMenuFreeType(): int|string|null
    {
        foreach ($this->persistentMenu as $type => $value) {
            if (!$value) {
                return $type;
            }
        };

        return null;
    }

    /**
     * @param null $type
     * @return array|mixed
     */
    public function getQuickReplies($type = null)
    {
        return $type ? $this->quickReplies[$type] : $this->quickReplies;
    }

    /**
     * @return int|null|string
     */
    public function getQuickRepliesFreeType(): int|string|null
    {
        foreach ($this->quickReplies as $type => $value) {
            if (!$value) {
                return $type;
            }
        };

        return null;
    }

    /**
     * @return null|string
     * @throws \SmartyException
     */
    public function detailsPagePlugin()
    {
        //if (!empty($this->detailsPagePluginData())) {
        return View::fetch('facebook::storefront.send-to-messenger-plugin.load-in-details-page');
        //}

        //return null;
    }

    /**
     * @return array
     * @throws Error
     * @throws \Throwable
     */
    public function detailsPagePluginData(): ?array
    {
        if (!$this->botManager->isActive()) {
            return [];
        }

        $settings = MessengerSetting::settings(MessengerSetting::ABANDONED_CART_TYPE);

        if ($this->userHasSubscribed() || !$settings['show_plugin']) {
            return null;
        }

        return [
            'appId' => $this->appId,
            'pageId' => $this->pageId,
            'pageAccessToken' => $this->pageAccessToken,
            'graphVersion' => $this->graphVersion,
            'ref' => site('site_id') . '-' . CartModel::getNewCartKey() . '-' . (int)Auth::customerId() . '-' . 'details_page',
            'settings' => $settings
        ];
    }

    /**
     * @return null|string
     * @throws Error
     * @throws \SmartyException
     * @throws \Throwable
     */
    public function chatInit()
    {
        if (!$this->chatManager->isActive()) {
            return;
        }

        return View::fetch('facebook::storefront.init-chat');
    }

    /**
     * @return null|string
     * @throws Error
     * @throws \SmartyException
     * @throws \Throwable
     */
    public function chat()
    {
        if (!$this->chatManager->isActive()) {
            return;
        }

        $settings = $this->chatManager->getSettings();

        return View::fetch('facebook::storefront.load-chat', [
            'pageId' => $this->pageId,
            'minimized' => $settings->get('minimized'),
            'plugin_title_for_logged_in' => $settings->get('plugin_title_for_logged_in'),
            'plugin_title_for_logged_out' => $settings->get('plugin_title_for_logged_out'),
            'plugin_color' => $settings->get('plugin_color'),
            'ref' => site('site_id') . '-' . CartModel::getNewCartKey() . '-' . (int)Auth::customerId(),
        ]);
    }

    /**
     * @return array|string
     * @throws Error
     * @throws \SmartyException
     * @throws \Throwable
     */
    public function thankYouPagePlugin()
    {
        if (!$this->botManager->isActive()) {
            return;
        }

        $settings = MessengerSetting::settings(MessengerSetting::COMPLETE_ORDERS_TYPE);

        if ($this->userHasSubscribed() || !$settings['show_send_to_messenger_plugin']) {
            return;
        }

        return View::fetch('facebook::storefront.send-to-messenger-plugin.thank-you-page', [
            'appId' => $this->appId,
            'pageId' => $this->pageId,
            'pageAccessToken' => $this->pageAccessToken,
            'graphVersion' => $this->graphVersion,
            'ref' => site('site_id') . '-' . CartModel::getNewCartKey() . '-' . (int)Auth::customerId() . '-' . 'thank_you_page',
            'settings' => $settings
        ]);
    }

    /**
     * @return array|string
     * @throws Error
     * @throws \SmartyException
     * @throws \Throwable
     */
    public function checkoutPlugin()
    {
        if (!$this->botManager->isActive()) {
            return;
        }

        $settings = MessengerSetting::settings(MessengerSetting::COMPLETE_ORDERS_TYPE);
        if ($this->userHasSubscribed() || !$settings['show_plugin_in_checkout']) {
            return;
        }

        $customer_id = ($cartInstance = CartModel::instance()) ? $cartInstance->getCustomerId() : null;
        return View::fetch('facebook::storefront.load-checkbox', [
            'appId' => $this->appId,
            'pageId' => $this->pageId,
            'pageAccessToken' => $this->pageAccessToken,
            'graphVersion' => $this->graphVersion,
            'user_ref' => site('site_id') . '-' . CartModel::getNewCartKey() . '-' . $customer_id . '-' . \Carbon\Carbon::now()->timestamp,
            'settings' => $settings
        ]);
    }

    /**
     * @return bool
     * @throws \Throwable
     */
    protected function userHasSubscribed()
    {
        return MessengerSubscriber::where('customer_id', $this->getCustomerId())
            ->orWhere('cart_id', $this->getCart()->id ?? null)
            ->orWhere('cart_key', CartModel::getNewCartKey())->exists();
    }

    /**
     * @return null|integer
     * @throws \Throwable
     */
    protected function getCustomerId()
    {
        return Auth::customerId() ?: ($this->getCartCustomer()->id ?? null);
    }

    /**
     * @return \App\Models\Customer\Customer|null
     * @throws \Throwable
     */
    protected function getCartCustomer()
    {
        if (!($cartInstance = $this->getCart())) {
            return;
        }

        return $cartInstance->customer;
    }

    /**
     * @return CartModel|bool
     * @throws \Throwable
     */
    protected function getCart()
    {
        return CartModel::instance();
    }

    /**
     * @param \App\Models\Order\Order $order
     * @param bool $order_fulfill_notification
     * @throws Error
     * @throws Facebook\Exceptions\FacebookSDKException
     */
    public static function sendMessagesForOrderStatusChange($order, $order_fulfill_notification): void
    {
        $subscriber = MessengerSubscriber::where('customer_id', $order->customer_id)->first();

        if ($subscriber) {
            $message_settings = MessengerSetting::settings(MessengerSetting::ORDER_STATUS_CHANGE_MESSAGE_TYPE);

            if ($message_settings['should-send-previous-message'] && !empty($message_settings['previous-message'])) {
                $prev_msg_params = static::getSimpleMessageParameters($message_settings['previous-message'], $subscriber->psid, $subscriber->username);
                FacebookPage::sendMessage($prev_msg_params, $subscriber->id);
            }

            $main_msg_params = static::getMainMessageParametersForOrderStatusChange($order, $subscriber->psid, $order_fulfill_notification);
            $response = FacebookPage::sendMessage($main_msg_params, $subscriber->id);

            if ($response) {
                $main_message_response = $response->getDecodedBody();
                $message_id = $main_message_response['message_id'] ?? null;

                $data = [
                    'date_sent' => Carbon::now('UTC')->toDateTimeString(),
                    'type' => MessengerSetting::ORDER_STATUS_CHANGE_MESSAGE_TYPE,
                    'order_id' => $order->id,
                    'mid' => $message_id,
                    'messenger_subscriber_id' => $subscriber->id,
                ];

                $collection = collect([$data]);

                static::saveMessagesData($collection);
            }
        }
    }

    /**
     * @param $response
     * @return null
     */
    public static function getMainMessageId(Facebook\FacebookBatchResponse $response)
    {
        $main_message_response = count($response->getResponses()) == 2 ? $response[1] : $response[0];
        $main_message_response = $main_message_response->getDecodedBody();

        return $main_message_response['message_id'] ?? null;
    }

    /**
     * @param $psid
     * @param $message_type
     * @param $username
     * @param null $cart_id
     * @throws Facebook\Exceptions\FacebookSDKException
     */
    public function sendWelcomeMessageAfterOptIn($psid, $message_type, $username, $cart_id = null): void
    {
        $welcome_message = MessengerSetting::settings($message_type)['message'];
        if (strlen((string) $welcome_message)) {
            $welcome_message_params = static::getSimpleMessageParameters($welcome_message, $psid, $username);
            \FacebookPage::sendMessage($welcome_message_params);

            if ($cart_id) {
                $discount_code_params = static::getDiscountCodeParams($psid, $message_type, $cart_id);
                if (!empty($discount_code_params)) {
                    \FacebookPage::sendMessage($discount_code_params);
                }
            }
        }
    }

    /**
     * @param $settings
     * @return array
     */
    public function getSelectedQuickReplies($settings): array
    {
        $quick_replies = array_filter(explode(',', !empty($settings->quick_replies) ? $settings->quick_replies : ''));
        $all_quick_replies = array_filter($this->getQuickReplies());

        $final_quick_replies = [];
        foreach ($quick_replies as $quick_reply) {
            if (array_key_exists($quick_reply, $all_quick_replies)) {
                $final_quick_replies[$quick_reply] = $all_quick_replies[$quick_reply];
            }
        }

        return $final_quick_replies;
    }

    /**
     * @param $psid
     * @param $message_type
     * @param $cart_id
     * @return array
     */
    protected static function getDiscountCodeParams($psid, $message_type, $cart_id)
    {
        if ($message_type == MessengerSetting::ABANDONED_CART_WELCOME_MESSAGE_TYPE) {
            $abandoned_carts_settings = MessengerSetting::settings(MessengerSetting::ABANDONED_CART_TYPE);

            if ($abandoned_carts_settings['plugin_view_mode'] == 'with-discount' && !empty($abandoned_carts_settings['discount_code'])) {
                $cart = CartModel::where('key', $cart_id)->orWhere('id', $cart_id)->first();

                if ($cart->has_products ?? false) {
                    $discount_info = \Linker::getCartWithDiscountUrl($abandoned_carts_settings['discount_code']);
                } else {
                    $discount_info = $abandoned_carts_settings['discount_code'];
                }

                $message = $abandoned_carts_settings['message_for_discount'];
                $message = str_replace('{$cart_url}', $discount_info, $message);

                return \Messenger::getSimpleMessageParameters($message, $psid);
            }
        }

        return [];
    }


    /**
     * @param $message
     * @param $psId
     * @param null $username
     * @return array
     */
    public static function getSimpleMessageParameters($message, $psId, $username = null): array
    {
        $message = static::replaceVariablesInMessage($psId, $message, $username);

        return [
            'messaging_type' => 'NON_PROMOTIONAL_SUBSCRIPTION',
            'recipient' => [
                'id' => $psId,
            ],
            'message' => [
                'text' => $message,
            ],
        ];
    }

    /**
     * @param $psId
     * @param $quick_replies
     * @param $message
     * @param $username
     * @return array
     */
    public static function getGeneralMessageParameters($psId, $quick_replies, $message, $username = null): array
    {
        $params = static::getSimpleMessageParameters($message, $psId, $username);
        $params['message']['quick_replies'] = static::getGeneralMessageQuickReplies($quick_replies);

        return $params;
    }

    /**
     * @param $message
     * @param $user_ref
     * @return array
     */
    public static function getCheckboxWelcomeMessageParameters($message, $user_ref): array
    {
        return [
            'messaging_type' => 'NON_PROMOTIONAL_SUBSCRIPTION',
            'recipient' => [
                'user_ref' => $user_ref,
            ],
            'message' => [
                'text' => $message
            ]
        ];
    }

    /**
     * @param Product[]|\Illuminate\Support\Collection $products
     * @param $source_type
     * @param $source
     * @param MessengerBot $bot_manager
     * @param $psId
     * @param int $start_index
     * @return array
     * @throws Error
     */
    public static function getProductsParameters($products, string $source_type, string $source, $bot_manager, string $psId, $start_index = 0): array
    {
        if ($products->count()) {
            app()->offsetSet('widget', new Widget());

            $payload = [
                'template_type' => 'generic',
                'image_aspect_ratio' => 'SQUARE',
                'sharable' => true,
            ];

            $products_first_count = $products->count();
            $all_products = $products;

            if ($products_first_count > 8) {
                $products = $all_products->slice(0, 8);
            }

            foreach ($products as $product) {
                $product->format();
                $product_params = array_filter([$product->v1, $product->v2, $product->v3]);

                if (!empty($product->price_from_discounted_formatted)) {
                    $price_formatted = $product->price_from_discounted_formatted;
                } else {
                    $price_from_to_formatted = __('global.from') . ' ' . $product->price_from_formatted . ' ' . __('global.to') . ' ' . $product->price_to_formatted;
                    $price_formatted = $product->price_from != $product->price_to ? $price_from_to_formatted : __('global.from') . ' ' . $product->price_from_formatted;
                }

                $url = $product->url() . static::UTM . $psId;

                $params = [
                    'title' => $product->name,
                    'subtitle' => !empty($product_params) ? implode(',', $product_params) . ' - ' . $price_formatted : $price_formatted,
                    'image_url' => $product->getImage('1280x1280'),
                    'default_action' => [
                        'type' => 'web_url',
                        'url' => $url,
                    ]
                ];

                if ($bot_manager->getSetting('checkout_in_bot')) {
                    $params['buttons'][] = [
                        'type' => 'postback',
                        'title' => $bot_manager->getSetting('add_to_cart_button_title'),
                        'payload' => site('site_id') . '-add_to_cart-' . $product->id,
                    ];
                } else {
                    $params['buttons'][] = [
                        'type' => 'web_url',
                        'url' => $url,
                        'title' => __('facebook.btn.chat_bot.product.more_info_button'),
                    ];
                }

                if ($bot_manager->getSetting('show_related_products')) {
                    $params['buttons'][] =
                        [
                            'type' => 'postback',
                            'title' => __('widget.product.related.ph.title'),
                            'payload' => site('site_id') . '-related_products-' . $product->id . '-' . $start_index,
                        ];
                }

                $payload['elements'][] = $params;
            }

            $more_quick_reply_payload = site('site_id') . '-more-product-' . $source_type . '-' . $source;
            $quick_replies[] = static::getMoreQuickReplies($products_first_count, $start_index, $all_products, $more_quick_reply_payload, $bot_manager->getSetting('more_products_button'));

            return [
                'messaging_type' => 'NON_PROMOTIONAL_SUBSCRIPTION',
                'recipient' => [
                    'id' => $psId,
                ],
                'message' => [
                    'attachment' => [
                        'type' => 'template',
                        'payload' => $payload
                    ],
                    'quick_replies' => array_values(array_filter($quick_replies))
                ]
            ];
        }

        return [];
    }

    /**
     * @param \Illuminate\Support\Collection $objects
     * @param $type
     * @param $message
     * @param $button_more_title
     * @param $psid
     * @param $source
     * @param $item_type
     * @param int $start_index
     * @return array
     */
    public static function getMessageWithQuickRepliesByObject($objects, string $type, $message, $button_more_title, $psid, string $source, string $item_type, $start_index = 0): array
    {
        $objects_first_count = $objects->count();
        if ($objects_first_count && !empty($message) && !empty($button_more_title)) {
            $all_objects = $objects;

            if ($start_index || $objects_first_count > 8) {
                $objects = $all_objects->slice($start_index, 8);
            }

            $quick_replies = [];
            foreach ($objects as $object) {
                $quick_reply_payload = site('site_id') . '-' . $type . '-' . $object->id . '-' . $source . '-' . $item_type;
                $quick_replies[] = static::getQuickReplyParams($quick_reply_payload, $object->name);
            }

            $payload = null;
            switch ($type) {
                case 'vendor' :
                    $payload = site('site_id') . '-more-' . $type . '-' . $item_type;
                    break;
                case 'category':
                    $parent_id = $objects->first()->parent->id ?? 0;
                    $payload = site('site_id') . '-more-' . $type . '-' . $parent_id . '-' . $source . '-' . $item_type;
                    break;
                default:
                    $payload = '';
            }

            $quick_replies[] = static::getMoreQuickReplies($objects_first_count, $start_index, $all_objects, $payload, $button_more_title);

            $message = static::replaceVariablesInMessage($psid, $message);

            return [
                'messaging_type' => 'NON_PROMOTIONAL_SUBSCRIPTION',
                'recipient' => [
                    'id' => $psid,
                ],
                'message' => [
                    'text' => $message,
                    'quick_replies' => array_values(array_filter($quick_replies))
                ]
            ];
        }

        return [];
    }

    /**
     * @param Category $category_id
     * @param $message
     * @param $show_products_button
     * @param $show_subcategories_button
     * @param $psid
     * @param $source
     * @param $item_type
     * @return array
     */
    public static function pickProductsOrSubcategoryMessageParams($category_id, $message, $show_products_button, $show_subcategories_button, $psid, string $source, string $item_type): array
    {
        $quick_replies = [];
        if (!empty($show_subcategories_button)) {
            $payload = site('site_id') . '-' . static::SUBCATEGORIES_TYPE . '-' . $category_id . '-' . $source . '-' . $item_type;
            $quick_replies[] = static::getQuickReplyParams($payload, $show_subcategories_button);
        }

        if (!empty($show_products_button)) {
            $payload = site('site_id') . '-' . static::CATEGORY_PRODUCTS_TYPE . '-' . $category_id . '-' . $source . '-' . $item_type;
            $quick_replies[] = static::getQuickReplyParams($payload, $show_products_button);
        }

        return [
            'messaging_type' => 'NON_PROMOTIONAL_SUBSCRIPTION',
            'recipient' => [
                'id' => $psid,
            ],
            'message' => [
                'text' => $message,
                'quick_replies' => array_values(array_filter($quick_replies))
            ]
        ];
    }

    /**
     * @param $filter
     * @param $start_index
     * @return Collection|\Illuminate\Support\Collection|static[]
     */
    public static function getFilteredProducts($filter, $start_index = 0)
    {
        $params = [
            'search' => $filter
        ];

        $searched_products = Product::makeWhereFilters($params);
        $products = Product::listing()->offset($start_index)->limit(20);
        if ($searched_products) {
            foreach ($searched_products as $filter) {
                $products->where($filter);
            }
        } else {
            $products->whereId(null)->limit(1);
        }

        return $products->where('products.is_hidden', 0)->orderByMatchByWords('desc')->groupBy('products.id')->get();
    }

    /**
     * @param $items_params
     * @throws Facebook\Exceptions\FacebookSDKException
     */
    public static function updatePersistentMenuInFacebook($items_params): void
    {
        $accessToken = FacebookPage::accessToken();

        if ($accessToken) {
            $get_started_button_params = [
                'get_started' => [
                    'payload' => site('site_id') . '-get_started'
                ]
            ];

            Facebook::graph()->post('/me/messenger_profile', $get_started_button_params, $accessToken);

            $call_to_actions = [];
            foreach ($items_params as $param_key => $param_value) {
                if ($param_value) {
                    switch ($param_value->action) {
                        //                    case 'search' :
                        //                        $call_to_actions[] = Messenger::getPostbackParams($param_value->button_title, site('site_id') . '-search-persistent_menu-' . $param_key);
                        //                        break;
                        case 'start_over':
                            $call_to_actions[] = Messenger::getPostbackParams($param_value->button_title, site('site_id') . '-start_over-persistent_menu-' . $param_key);
                            break;
                        case 'categories':
                            $call_to_actions[] = Messenger::getPostbackParams($param_value->button_title, site('site_id') . '-categories-persistent_menu-' . $param_key);
                            break;
                        case static::SIMPLE_MESSAGE_TYPE:
                            $call_to_actions[] = Messenger::getPostbackParams($param_value->button_title, site('site_id') . '-' . static::SIMPLE_MESSAGE_TYPE . '-persistent_menu-' . $param_key);
                            break;
                    }
                }
            }

            $utm = http_build_query([
                'utm_medium' => 'powered_by',
                'utm_source' => 'messenger',
            ]);

            $call_to_actions[] = [
                'type' => 'web_url',
                'title' => 'CloudIO - bot by CloudCart',
                'url' => 'https://cloudcart.com?' . $utm,
                'webview_height_ratio' => 'full',
            ];

            $params = [
                'persistent_menu' => [
                    [
                        'locale' => 'default',
                        'composer_input_disabled' => 'false',
                        'call_to_actions' => $call_to_actions
                    ]
                ]
            ];

            Facebook::graph()->post('/me/messenger_profile', $params, $accessToken);
        }
    }

    /**
     * @param Order $order
     * @param $psId
     * @param bool $order_fulfill_notification
     * @return array
     * @throws Error
     */
    protected static function getMainMessageParametersForOrderStatusChange($order, string $psId, $order_fulfill_notification): array
    {
        if ($shipping_address = $order->shippingAddress) {
            $lat_and_long = $shipping_address->latitude . ',' . $shipping_address->longitude;

            $image_url = 'https://maps.googleapis.com/maps/api/staticmap?key='
                . getGoogleMapKey()
                . '&center='
                . $lat_and_long
                . '&zoom=15&size=450x210&markers='
                . $lat_and_long
                . '&sensor=false&scale=2';
        } else {
            $image_url = logo();
        }

        $order_url = \Linker::returnPageUrl($order->status, $order->payment->hash) . static::UTM . $psId;
        if ($order_fulfill_notification) {
            $button_url = $order->fulfillment->shipping_tracking_url ?? $order_url;
            $status = isset($order->status_fulfillment) ? Status::shipping($order->status_fulfillment) : '';
            $button_title = __('sf.account.order.details.label.tracking');
        } else {
            $button_url = \Linker::returnPageUrl($order->status, $order->payment->hash) . static::UTM . $psId;
            $status = $order->status_formatted;
            $button_title = __('sf.account.orders.tip.view_details');
        }

        $payload = [
            'template_type' => 'generic',
            'image_aspect_ratio' => 'SQUARE',
            'sharable' => true,
            'elements' => [
                [
                    'title' => (sprintf(__('facebook.msg.description.order_status_changed'), $order->id, $status)),
                    'image_url' => $image_url,
                    'default_action' => [
                        'type' => 'web_url',
                        'url' => $button_url,
                    ],
                    'buttons' => [
                        [
                            'type' => 'web_url',
                            'url' => $button_url,
                            'title' => $button_title,
                        ],
                    ]
                ]
            ]
        ];

        /**@var OrderProduct[] $products */
        $products = $order->products->take(10);
        foreach ($products as $product) {
            $params = array_filter([$product->v1, $product->v2, $product->v3]);

            $price = money(round($product->getTotalPriceWithOptionsAfterDiscountsWithModification() / $product->getQuantity()), $product->order->getCurrency(), $product->order->getLanguage());
            $payload['elements'][] = [
                'title' => $product->name,
                'subtitle' => !empty($params) ? implode(',', $params) . ' - ' . $price : $price,
                'image_url' => $product->getImage('1280x1280'),
                'default_action' => [
                    'type' => 'web_url',
                    'url' => $order_url,
                ]
            ];
        }

        return [
            'messaging_type' => 'NON_PROMOTIONAL_SUBSCRIPTION',
            'recipient' => [
                'id' => $psId,
            ],
            'message' => [
                'attachment' => [
                    'type' => 'template',
                    'payload' => $payload
                ],
            ]
        ];
    }

    /**
     * @param $objects_first_count
     * @param $start_index
     * @param \Illuminate\Support\Collection $all_objects
     * @param $payload
     * @param $button_more_title
     * @return array
     */
    public static function getMoreQuickReplies($objects_first_count, $start_index, $all_objects, string $payload, $button_more_title): array
    {
        if ($objects_first_count > 8 && strlen((string) $button_more_title)) {
            $start_index += 8;
            $next_objects = $all_objects->slice($start_index);

            if ($next_objects->count() > 0) {
                $payload .= '-' . $start_index;
                return static::getQuickReplyParams($payload, $button_more_title);
            }
        }

        return [];
    }

    /**
     * @param $payload
     * @param $title
     * @return array
     */
    public static function getQuickReplyParams($payload, $title): array
    {
        return [
            "content_type" => 'text',
            "title" => !empty($title) ? $title : '',
            "payload" => $payload,
        ];
    }

    /**
     * @param $psId
     * @return void
     */
    public static function stopBotAnswers($psId): void
    {
        $subscriber = MessengerSubscriber::where('psid', $psId)->first();

        if ($subscriber->cart_id) {
            CartModel::where('id', $subscriber->cart_id)->delete();
        } elseif ($subscriber->cart_key) {
            CartModel::where('key', $subscriber->cart_key)->delete();
        }

        $subscriber->update([
            'bot_should_answer' => 0,
            'customer_id' => null,
            'cart_id' => null,
        ]);

        $commands = MessengerCommand::where('recipient_id', $psId)->where('site_id', site('site_id'))->get();

        if ($commands->count()) {
            $commands->map(function (MessengerCommand $command): void {
                $command->delete();
            });
        }
    }

    /**
     * @param $psId
     * @param $isOn
     * @throws Facebook\Exceptions\FacebookSDKException
     */
    public static function configTyping($psId, $isOn): void
    {
        $params = [
            'recipient' => [
                'id' => $psId
            ],
            'sender_action' => $isOn ? 'typing_on' : 'typing_off'
        ];

        FacebookPage::sendMessage($params);
    }

    /**
     * @param $psId
     * @return Facebook\FacebookResponse
     * @throws Facebook\Exceptions\FacebookSDKException
     */
    public function getSubscriber($psId)
    {
        return FacebookPage::graphRequest('get', $psId);
    }

    /**
     * @return SiteQueue|bool
     * @throws \Exception
     */
    public static function startMessengerAbandonedCart()
    {
        return SiteQueue::createQueueByMapping('fb_messenger_abandoned_cart');
    }

    /**
     * @param \Illuminate\Support\Collection $collection
     */
    public static function saveMessagesData(\Illuminate\Support\Collection $collection): void
    {
        $chunkedMessages = $collection->chunk(50);

        $chunkedMessages->map(function ($collection): void {
            /** @var \Illuminate\Support\Collection $collection */
            MessengerSentMessage::insert(\Illuminate\Support\Arr::only($collection->toArray(), (new MessengerSentMessage())->getFillable()));

            $collection = $collection->map(fn ($data): array => MessengerMessage::prepareDataForInsert($data));

            MessengerMessage::insert($collection->toArray());
        });
    }

    /**
     * @param $title
     * @param $payload
     * @return array
     */
    protected static function getPostbackParams($title, $payload): array
    {
        return [
            'type' => 'postback',
            'title' => $title,
            'payload' => $payload,
        ];
    }

    /**
     * @param $quick_replies
     * @return array
     */
    protected static function getGeneralMessageQuickReplies($quick_replies): array
    {
        $quick_replies_params = [];
        foreach ($quick_replies as $quick_reply_key => $quick_reply_value) {
            $payload = '';
            switch ($quick_reply_value->action) {
                case 'categories':
                    $payload = site('site_id') . '-' . static::CATEGORIES_TYPE . '-' . MessengerBot::QUICK_REPLY . '-' . $quick_reply_key;
                    break;
                case 'vendors':
                    $payload = site('site_id') . '-' . static::VENDORS_TYPE . '-' . MessengerBot::QUICK_REPLY . '-' . $quick_reply_key;
                    break;
                case 'latest':
                    $payload = site('site_id') . '-' . static::PRODUCTS_TYPE . '-' . "latest-" . MessengerBot::QUICK_REPLY . '-' . $quick_reply_key;
                    ;
                    break;
                case 'featured':
                    $payload = site('site_id') . '-' . static::PRODUCTS_TYPE . '-' . "featured-" . MessengerBot::QUICK_REPLY . '-' . $quick_reply_key;
                    ;
                    break;
                case 'on_sale':
                    $payload = site('site_id') . '-' . static::PRODUCTS_TYPE . '-' . "sale-" . MessengerBot::QUICK_REPLY . '-' . $quick_reply_key;
                    ;
                    break;
                case static::SIMPLE_MESSAGE_TYPE:
                    $payload = site('site_id') . '-' . static::SIMPLE_MESSAGE_TYPE . '-' . MessengerBot::QUICK_REPLY . '-' . $quick_reply_key;
                    ;
                    break;
            }

            $quick_replies_params[] = self::getQuickReplyParams($payload, $quick_reply_value->button_title);
        }

        return $quick_replies_params;
    }

    /**
     * @param $psId
     * @param $message
     * @param $username
     * @return mixed
     */
    protected static function replaceVariablesInMessage($psId, $message, $username = null)
    {
        $contains_username = strstr((string) $message, '{$username}');
        $contains_first_name = strstr((string) $message, '{$first_name}');
        $contains_last_name = strstr((string) $message, '{$last_name}');

        if ($contains_username || $contains_first_name || $contains_last_name) {
            if (is_null($username)) {
                $subscriber = MessengerSubscriber::where('psid', $psId)->first();
                $username = $subscriber->username ?? null;
            }

            if (!is_null($username)) {
                if ($contains_first_name || $contains_last_name) {
                    [$first_name, $last_name] = explode(' ', $username);
                    $message = str_replace('{$first_name}', $first_name, $message);
                    $message = str_replace('{$last_name}', $last_name, $message);
                } else {
                    $message = str_replace('{$username}', $username, $message);
                }
            }
        }

        if (strstr((string) $message, '{$shop_url}')) {
            $message = str_replace('{$shop_url}', site()->getSiteUrl('primary'), $message);
        }

        if (strstr((string) $message, '{$shop_name}')) {
            $message = str_replace('{$shop_name}', setting('site_name'), $message);
        }

        return $message;
    }
}
