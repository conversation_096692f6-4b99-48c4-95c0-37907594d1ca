<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 9.10.2017 г.
 * Time: 11:43 ч.
 */

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class HtmlSanitize extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    #[\Override]
    public function register(): void
    {
        if (!in_array(app_namespace(), ['sitecp'])) {
            $this->sanitizePost();
        }
    }

    /**
     *
     */
    protected function sanitizePost()
    {
        if (!empty($_POST)) {
            $_POST = $this->_sanitize($_POST);
        }
    }

    /**
     * @param $data
     * @return array|string
     */
    protected function _sanitize($data): array|string
    {
        if (is_array($data)) {
            $return = [];
            foreach ($data as $key => $value) {
                $return[$key] = $this->_sanitize($value);
            }

            return $return;
        } else {
            //            return filter_var($data, FILTER_SANITIZE_STRING);
            return strip_tags((string) $data);
        }
    }

}
