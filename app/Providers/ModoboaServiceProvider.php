<?php

declare(strict_types=1);

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\ModoboaService;

class ModoboaServiceProvider extends ServiceProvider implements \Illuminate\Contracts\Support\DeferrableProvider
{
    #[\Override]
    public function register(): void
    {
        $this->app->singleton('modoboa', fn($app): \App\Services\ModoboaService => new ModoboaService());
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    #[\Override]
    public function provides()
    {
        return ['modoboa'];
    }
}
