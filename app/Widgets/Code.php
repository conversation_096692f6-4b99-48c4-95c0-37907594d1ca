<?php

declare(strict_types=1);

namespace App\Widgets;

use App\Traits\TextVariablesReplace;
use lib\widget\WidgetAbstract;

/**
 * Class Code
 *
 * @package lib\widget\extra
 */
class Code extends WidgetAbstract
{
    use TextVariablesReplace;

    /**
     * @var array
     */
    protected static $_restrictions = [
        'enabled' => 'bool',
        'code' => 'char:1,3000000',
    ];

    /**
     * @var array
     */
    protected static $_default_settings = [
        'enabled' => true,
        'code' => '',
    ];

    /**
     * @param mixed $map
     * @return mixed
     */
    public function __construct($map)
    {
        $name = __('widget.code.name');
        parent::__construct($map, $name);
        $this->_widget_description = __('widget.code.description');
    }

    /**
     *
     */
    public function init()
    {
    }

    /**
     * @return string
     */
    public function getCode(): ?string
    {
        return $this->replaceTextVariables($this->_settings['code']);
    }

    /**
     * @return string
     */
    #[\Override]
    public function getTemplatePath(): string
    {
        if(view()->exists('theme_widgets::extra.code')) {
            return 'theme_widgets::extra.code';
        }

        return 'global_widgets::extra.code';
    }

}
