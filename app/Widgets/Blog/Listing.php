<?php

declare(strict_types=1);

namespace App\Widgets\Blog;

use App\Exceptions\HttpNotFound;
use App\Models\Blog\Article;
use App\Models\Blog\Blog;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use lib\widget\WidgetAbstract;
use App\Models\Blog\Tag;

/**
 * Class Listing for blog
 *
 * @package lib\widget\product
 */
class Listing extends WidgetAbstract
{
    /**
     * @var array
     */
    protected static $_restrictions = [
        'enabled' => false,
        'per_page' => "int:2,50"
    ];

    /**
     * @var array
     */
    protected static $_default_settings = [
        'enabled' => true,
        'per_page' => 10
    ];

    protected $_join = '';

    /**
     * @var Tag
     */
    protected $_tag;

    protected $_filters = [];

    /**
     * @var
     */
    private int $_page = 1;

    /**
     * @var
     */
    private $_pages;

    /**
     * @var
     */
    private $_total;

    /**
     * @var Blog
     */
    private $_blog;

    /**
     * @var Collection
     */
    private $_blogs;

    /**
     * @var LengthAwarePaginator|Article[]
     */
    private $_articles;

    private string $_where = "1";

    /**
     * @param mixed $map
     * @return mixed
     */
    public function __construct($map)
    {
        $name = __('widget.blog.list.name');
        parent::__construct($map, $name);
        $this->_widget_description = __('widget.blog.list.description');
    }

    /**
     *
     */
    public function init(): void
    {
        if (($page = (int)request()->query('page')) > 0) {
            $this->_page = $page;
        }
    }

    /**
     * @param $filter
     */
    public function handle($filter): void
    {
        if ($filter == 'tag') {
            $this->_filters['tag'] = request()->segment(3);
        } elseif ($filter == 'category') {
            $this->_filters['blog'] = request()->segment(3);
        }

        $this->_handleFilters();
    }

    /**
     *
     */
    public function _handleFilters(): void
    {

        if (!empty($this->_filters['blog'])) {

            $this->_blog = Blog::getByUrl($this->_filters['blog']);

            if (!$this->_blog) {
                throw new HttpNotFound();
            }

            \Linker::handleUrlRedirect($this->_filters['blog'], $this->_blog->url_handle);

            $this->_filters['blog_id'] = $this->_blog->id;

            $this->_where .= " AND `" . BlogConfig::$table_articles . sprintf("`.`blog_id` = '%s' ", $this->_filters['blog_id']);

        }

        if (!empty($this->_filters['tag'])) {
            $this->_tag = Tag::getByUrl($this->_filters['tag']);

            if (!$this->_tag) {
                throw new HttpNotFound();
            }

            $this->_filters['tag_id'] = $this->_tag->id;
            $this->_join .= ' LEFT JOIN `tags__articles_tags__items` as `ati` ON `blogs_articles`.`id` = `ati`.`item_id`';
            $this->_where .= " AND `ati`.`tag_id` = '" . $this->_tag->id . "'";
        }

    }

    /**
     * @return array
     */
    #[\Override]
    public function getSeo(): array
    {

        if ($this->_blog) {

            if (isset($this->_blog->seo_title)) {
                $title = $this->_blog->seo_title;
            } else {
                $title = $this->_blog->name;
            }

            if (isset($this->_blog->seo_description)) {
                $description = $this->_blog->seo_description;
            } else {
                $description = $this->_blog->name;
            }

        } else {

            $title = __('seo.blog.title');
            $description = __('seo.blog.description');

        }

        return [
            'title' => $title,
            'description' => $description
        ];

    }

    //getters

    /**
     * @return Collection|Blog[]
     */
    public function getBlogs()
    {
        if (!empty($this->_blogs)) {
            return $this->_blogs;
        }

        return $this->_blogs = Blog::cache()
            ->get();
    }

    /**
     * @return mixed
     */
    public function getBlog()
    {
        return $this->_blog;
    }

    /**
     * @return LengthAwarePaginator|Article[]
     */
    public function getArticles()
    {
        if (!empty($this->_articles)) {
            return $this->_articles;
        }

        $this->_articles = Article::countComments()->visible()->with(['author', 'blog'])
            ->whereHas('blog')->paginate($this->_settings['per_page']);

        $this->_total = $this->_articles->total();
        $this->_pages = $this->_articles->lastPage();

        return $this->_articles;
    }

    /**
     * @return mixed
     */
    public function getPage(): int
    {
        return $this->_page;
    }

    /**
     * @return mixed
     */
    public function getPages()
    {
        return $this->_pages;
    }

    /**
     * @return mixed
     */
    public function getTotal()
    {
        return $this->_total;
    }

    /**
     * @return bool|mixed|string
     */
    public function getHeaderString()
    {

        if (isset($this->_blog)) {
            return $this->_blog->name;
        }

        return __('sf.widget.blog.header.all_blogs');

    }

}
