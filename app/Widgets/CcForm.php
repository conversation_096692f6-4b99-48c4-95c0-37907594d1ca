<?php

declare(strict_types=1);

namespace App\Widgets;

use Illuminate\Support\Collection;
use lib\widget\WidgetAbstract;
use Modules\Marketing\Segments\Core\Models\SubscriberForm;

/**
 * Class CcForm
 *
 * @package App\Widgets
 */
class CcForm extends WidgetAbstract
{
    protected static $_restrictions = [
        'enabled' => false,
        'form_id' => 'required',
    ];

    /**
     * @var array
     */
    protected static $_default_settings = [
        'enabled' => true,
        'form_id' => '',
    ];

    /**
     * @param mixed $map
     * @return mixed
     */
    public function __construct($map)
    {
        $name = __('segments::form.widget.title');
        parent::__construct($map, $name);
        $this->_widget_description = __('segments::form.widget.description');
    }

    /**
     * @return mixed
     */
    public function init(): void
    {
        // TODO: Implement init() method.
    }


    public function getHeaderString()
    {
        return $this->_settings['title'];
    }

    /**
     * @param mixed $settings
     * @return mixed
     */
    #[\Override]
    public function saveSettings(&$settings): void
    {
        $settings['enabled'] = intval(isset($settings['enabled']));
    }

    /**
     * @return string
     */
    #[\Override]
    public function getTemplatePath(): string
    {
        if(view()->exists('theme_widgets::extra.cc_form')) {
            return 'theme_widgets::extra.cc_form';
        }

        return 'global_widgets::extra.cc_form';
    }

    /**
     * @return Collection
     */
    public function getForms(): Collection
    {
        return SubscriberForm::owner()->active()
            ->where('type', 'form')
            ->where('draft', '<>', true)
            ->where('embedded', true)
            ->orderBy('name', 'asc')
            ->get(['_id', 'name']);
    }
}
