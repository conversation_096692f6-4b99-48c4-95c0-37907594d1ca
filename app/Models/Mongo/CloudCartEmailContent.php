<?php

declare(strict_types=1);

namespace App\Models\Mongo;

use MongoDB\Laravel\Eloquent\Model as MongoEloquent;

class CloudCartEmailContent extends MongoEloquent
{
    public $timestamps = false;

    protected $connection = 'mongodb-logging-google-cloud';

    protected $table = 'cloudcart_emails_content';

    protected $guarded = ['_id'];

    protected $hidden = ['_id'];

    protected $appends = ['id'];
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getBodyAttribute($value): string|false
    {
        return gzdecode(base64_decode((string) $this->attributes['body']));
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setBodyAttribute($value): void
    {
        $this->attributes['body'] = base64_encode(gzencode((string) $value, 9));
    }

}
