<?php

declare(strict_types=1);

namespace App\Models\Mongo;

use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use MongoDB\Laravel\Eloquent\Model as MongodbModel;

abstract class AbstractModel extends MongodbModel
{
    /**
     * @param $column
     * @param array $value
     * @param array|null $remove
     */
    public function assign($column, array $value, ?array $remove = null): void
    {
        if ($remove) {
            $this->pull($column, $remove);
        }

        $this->push($column, [$value]);
    }

    /**
     * Convert the model instance to an array.
     *
     * @return array
     */
    #[\Override]
    public function toArray()
    {
        return array_merge($this->attributesToArraySafe(), $this->relationsToArray());
    }

    /**
     * @inheritdoc
     */
    public function attributesToArraySafe()
    {
        // If an attribute is a date, we will cast it to a string after converting it
        // to a DateTime / Carbon instance. This is so we will get some consistent
        // formatting while accessing attributes vs. arraying / JSONing a model.
        $attributes = $this->addDateAttributesToArray(
            $attributes = $this->getArrayableAttributes()
        );

        $attributes = $this->addMutatedAttributesToArray(
            $attributes,
            $mutatedAttributes = $this->getMutatedAttributes()
        );

        // Next we will handle any casts that have been setup for this model and cast
        // the values to their appropriate type. If the attribute has a mutator we
        // will not perform the cast on those attributes to avoid any confusion.
        $attributes = $this->addCastAttributesToArray(
            $attributes,
            $mutatedAttributes
        );

        // Here we will grab all of the appended, calculated attributes to this model
        // as these attributes are not really in the attributes array, but are run
        // when we need to array or JSON the model for convenience to the coder.
        foreach ($this->getArrayableAppends() as $key) {
            $attributes[$key] = $this->mutateAttributeForArray($key, null);
        }

        // Convert dot-notation dates.
        foreach ($this->getDates() as $key) {
            if (Str::contains($key, '.') && Arr::has($attributes, $key)) {
                Arr::set($attributes, $key, (string)$this->asDateTime(Arr::get($attributes, $key)));
            }
        }

        return $attributes;
    }

}
