<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 20.2.2019 г.
 * Time: 09:58 ч.
 */

namespace App\Models\Mongo\Analytics\Traits;

trait ArrayAttribute
{
    /**
     * @param $model_key
     * @param mixed $value
     * @param null $key
     * @param bool $unique
     */
    public function setArrayData($model_key, $value, $key = null, $unique = false): void
    {
        $data = $this->$model_key ?: [];
        if ($key) {
            $data[$key] = $value;
        } else {
            $data[] = $value;
        }

        if ($unique) {
            $data = array_unique($data);
            sort($data);
        }

        $this->$model_key = $data;
    }

    /**
     * @param $model_key
     * @param null $key
     * @return array|null
     */
    public function getArrayData($model_key, $key = null)
    {
        $data = parent::getAttribute($model_key);
        if ($key) {
            return $data[$key] ?? null;
        }

        return $data;
    }

}
