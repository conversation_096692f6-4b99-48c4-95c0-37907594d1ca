<?php

declare(strict_types=1);

namespace App\Models\Discount\Traits;

use App\Helper\ArrayCache;
use App\Models\Discount\Discount;
use App\Models\Discount\DiscountToTarget;
use App\Models\Discount\ProductToDiscount;
use App\Models\Product\Product;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Modules\Core\SmartCollections\Models\ProductSelection;

trait ProductToDiscountPopulate_BACKUP123
{
    /**
     * @param Discount $discount
     * @param Product|null $product
     * @param bool $delete_products
     * @return null|Builder
     * @throws Exception
     */
    public static function addProductToDiscountsByTargets(Discount $discount, ?Product $product = null, bool $delete_products = false): ?Builder
    {
        if ($discount->targetsClear->isEmpty() || $discount->meta->get('countdown_minutes')) {
            return null;
        }

        $deleteProducts = function () use ($product, $discount) {
            if ($product !== null) {
                return ProductToDiscount::where('discount_id', $discount->id)
                    ->where('product_id', $product->id)->get()->map(function (ProductToDiscount $discount) {
                        $discount->delete();
                        return $discount->discount_id;
                    });
            } else {
                ProductToDiscount::where('discount_id', $discount->id)->delete();
            }

            return collect();
        };

        $deleted = collect();
        if ($delete_products || !in_array($discount->type, ['flat', 'percent', 'shipping', 'fixed'])) {
            $deleted = $deleteProducts();
            if (!in_array($discount->type, ['flat', 'percent', 'shipping', 'fixed'])) {
                return null;
            }
        }

        if ($discount->code || $discount->is_container || $discount->type == 'shipping' || $discount->targetsClear->first()->type == 'shipping') {
            return null;
        }

        $query = null;
        $discount->targetsClear->groupBy('customer_group_id')->map(function (Collection $targets, $customer_group_id) use (&$query, $product, $discount): void {
            /** @var DiscountToTarget $target */
            $target = $targets->first();

            $p = self::initSqlQuery($target, $targets, $discount, $product->id ?? null)
                ->join('products_variants', function (JoinClause $clause): void {
                    $clause->on('products_variants.item_id', 'products.id');
                });

            $price = 'products_variants.price';
            $save = '0';
            if ($discount->type == 'percent') {
                $save = 'ROUND(products_variants.price * (' . (int)$discount->type_value . '/10000))';
                $price = 'products_variants.price - (' . $save . ')';
            } elseif ($discount->type == 'flat') {
                $save = (int)$discount->type_value;
                $price = 'products_variants.price - (' . $save . ')';
            }

            if ($discount->type == 'fixed') {
                $save = $target->real_price - $target->price;
                $price = $target->price;
            }

            $p->addSelect([
                \Illuminate\Support\Facades\DB::raw($target->id),
                'products_variants.id',
                \Illuminate\Support\Facades\DB::raw(empty($customer_group_id) ? 'NULL' : $target->customer_group_id),
                \Illuminate\Support\Facades\DB::raw($price),
                \Illuminate\Support\Facades\DB::raw($save),
                \Illuminate\Support\Facades\DB::raw('"' . $discount->type . '"'),
            ]);

            if ($discount->type == 'flat') {
                $p->whereRaw('products.price_from >= ' . $save);
            }

            if ($discount->type == 'shipping') {
                $p->where('products_variants.price', '>=', intval($discount->order_over));
            }

            if ($price != 'products_variants.price') {
                $p->whereRaw('products_variants.price > (' . $price . ')');
            }

            if ($discount->type == 'flat' && $discount->type_value > 0) {
                $p->where('products_variants.price', '>=', $discount->type_value);
            }

            if ($query !== null) {
                $query->union($p);
            } else {
                $query = $p;
            }
        });

        if (empty($query)) {
            return null;
        }

        $result = ProductToDiscount::insertIgnoreUsing(
            [
                'product_id', 'discount_id', 'date_start', 'date_end', 'created_at', 'updated_at',
                'target_id', 'variant_id', 'customer_group_id', 'price', 'save', 'type',
            ],
            $query
        )->where('discount_id', $discount->id);

        if ($product !== null) {
            $inserts = $result->where('product_id', $product->id)
                ->with('discount')->get()->keyBy('discount_id');

            $realInserts = $inserts->forget($deleted->all());
            $log = [];
            if ($realInserts->isNotEmpty()) {
                $log['attached'] = $realInserts->map(fn (ProductToDiscount $discount): array => [
                    'key' => $discount->discount_id,
                    'name' => sprintf('%s (#%d)', $discount->discount->name, $discount->discount_id),
                    'price' => $discount->price,
                    'save' => $discount->save,
                    'date_start' => optional($discount->date_start)->format('Y-m-d H:i:s'),
                    'date_end' => optional($discount->date_end)->format('Y-m-d H:i:s'),
                ])->all();
            }

            if ($deleted->isNotEmpty()) {
                $deleted = $deleted->diff($inserts->pluck('discount_id'));
                if ($deleted->isNotEmpty() && ($deleted = Discount::whereKey($deleted)->get()) && $deleted->isNotEmpty()) {
                    $log['detached'] = $deleted->map(fn (Discount $discount): array => [
                        'key' => $discount->id,
                        'name' => sprintf('%s (#%d)', $discount->name, $discount->id),
                    ])->all();
                }
            }

            if ($log) {
                $product->changeLogRegisterExtend([
                    'relations' => [
                        'discounts' => $log
                    ]
                ]);
            }
        }

        return $result;
    }

    /**
     * @param Discount $discount
     * @param array $products
     * @param bool $delete_products
     * @return null|Builder
     * @throws Exception
     */
    public static function addProductsToDiscountsByTargets(Discount $discount, array $products, bool $delete_products = false): ?Builder
    {
        if ($discount->targetsClear->isEmpty() || empty($products) || $discount->meta->get('countdown_minutes')) {
            return null;
        }

        $deleteProducts = (fn () => ProductToDiscount::where('discount_id', $discount->id)
            ->whereIn('product_id', $products)->delete());

        if ($delete_products || !in_array($discount->type, ['flat', 'percent', 'shipping', 'fixed'])) {
            $deleteProducts();
            if (!in_array($discount->type, ['flat', 'percent', 'shipping', 'fixed'])) {
                return null;
            }
        }

        if ($discount->code || $discount->is_container || $discount->type == 'shipping' || $discount->targetsClear->first()->type == 'shipping') {
            if ($delete_products) {
                $deleteProducts();
            }

            return null;
        }

        $query = null;
        $discount->targetsClear->groupBy('customer_group_id')->map(function (Collection $targets, $customer_group_id) use (&$query, $products, $discount): void {
            /** @var DiscountToTarget $target */
            $target = $targets->first();

            $p = self::initSqlQuery($target, $targets, $discount, $products)
                ->join('products_variants', function (JoinClause $clause): void {
                    $clause->on('products_variants.item_id', 'products.id');
                });

            $price = 'products_variants.price';
            $save = '0';
            if ($discount->type == 'percent') {
                $save = 'ROUND(products_variants.price * (' . (int)$discount->type_value . '/10000))';
                $price = 'products_variants.price - (' . $save . ')';
            } elseif ($discount->type == 'flat') {
                $save = (int)$discount->type_value;
                $price = 'products_variants.price - (' . $save . ')';
            }

            if ($discount->type == 'fixed') {
                $save = $target->real_price - $target->price;
                $price = $target->price;
            }

            $p->addSelect([
                \Illuminate\Support\Facades\DB::raw($target->id),
                'products_variants.id',
                \Illuminate\Support\Facades\DB::raw(empty($customer_group_id) ? 'NULL' : $target->customer_group_id),
                \Illuminate\Support\Facades\DB::raw($price),
                \Illuminate\Support\Facades\DB::raw($save),
                \Illuminate\Support\Facades\DB::raw('"' . $discount->type . '"'),
            ]);

            if ($discount->type == 'flat') {
                $p->whereRaw('products.price_from >= ' . $save);
            }

            if ($discount->type == 'shipping') {
                $p->where('products_variants.price', '>=', intval($discount->order_over));
            }

            if ($price != 'products_variants.price') {
                $p->whereRaw('products_variants.price > (' . $price . ')');
            }

            if ($discount->type == 'flat' && $discount->type_value > 0) {
                $p->where('products_variants.price', '>=', $discount->type_value);
            }

            if ($query !== null) {
                $query->union($p);
            } else {
                $query = $p;
            }
        });

        if (empty($query)) {
            return null;
        }

        $result = ProductToDiscount::insertIgnoreUsing(
            [
                'product_id', 'discount_id', 'date_start', 'date_end', 'created_at', 'updated_at',
                'target_id', 'variant_id', 'customer_group_id', 'price', 'save', 'type',
            ],
            $query
        )->where('discount_id', $discount->id);

        return $result->whereIn('product_id', $products);
    }

    /**
     * @param DiscountToTarget $target
     * @param Collection $targets
     * @param Discount $discount
     * @param null $product_id
     * @return Product|Builder|\Illuminate\Database\Query\Builder
     */
    private static function initSqlQuery(DiscountToTarget $target, Collection $targets, Discount $discount, $product_id = null)
    {
        $p = Product::withoutGlobalScopes()->where('type', '<>', Product::TYPE_BUNDLE)
            //product_id, discount_id, date_start, date_end, created_at, updated_at
            ->select([
                'products.id',
                \Illuminate\Support\Facades\DB::raw($discount->id),
                \Illuminate\Support\Facades\DB::raw(is_null($discount->date_start) ? 'NULL' : '"' . $discount->date_start . '"'),
                \Illuminate\Support\Facades\DB::raw(is_null($discount->date_end) ? 'NULL' : '"' . $discount->date_end . '"'),
                \Illuminate\Support\Facades\DB::raw('NOW()'),
                \Illuminate\Support\Facades\DB::raw('NOW()'),
            ]);

        if ($product_id) {
            $p->whereIn('products.id', is_array($product_id) ? $product_id : [$product_id]);
        }

        if ($target->type == 'category_vendor') {
            $p->whereRaw(
                sprintf(
                    'products.category_id IN (SELECT category_id FROM category_path WHERE path_id IN(%s))',
                    $targets->unique('item_id')->implode('item_id', ',') ?: '0'
                )
            );
            $p->whereIn('products.vendor_id', $targets->unique('sub_item_id')->pluck('sub_item_id'));
        } elseif ($target->type == 'vendor') {
            $p->whereIn('products.vendor_id', $targets->unique('item_id')->pluck('item_id'));
        } elseif ($target->type == 'category') {
            $p->whereRaw(
                sprintf(
                    'products.category_id IN (SELECT category_id FROM category_path WHERE path_id IN(%s))',
                    $targets->unique('item_id')->implode('item_id', ',') ?: '0'
                )
            );
        } elseif ($target->type == 'product') {
            $p->whereIn('products.id', $targets->unique('item_id')->pluck('item_id'));
        } elseif ($target->type == 'selection') {
            $selection_id = $discount->targetsClear->unique('item_id')->pluck('item_id')->all();
            $filter = ArrayCache::remember('selection.filter.' . json_encode($selection_id), fn (): ?string => ProductSelection::filters((array)$selection_id));
            if ($filter) {
                $p->whereRaw($filter);
            } else {
                $p->where('products.id', 0);
            }

            $p->setFilterByType('selection', $target->item_id);
        } elseif ($target->type != 'all') {
            $p->where('products.id', 0);
        }

        return $p;
    }
}
