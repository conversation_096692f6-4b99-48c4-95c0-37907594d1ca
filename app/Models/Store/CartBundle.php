<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.1.2018 г.
 * Time: 09:16 ч.
 */

namespace App\Models\Store;

use App\Models\Base\AbstractCartBundles;
use App\Models\Product\Product;
use Illuminate\Support\Collection;

/**
 * @property CartBundleItem[]|Collection $cart_bundle_items
 */
class CartBundle extends AbstractCartBundles
{
    /**
     *
     */
    #[\Override]
    protected static function boot()
    {
        parent::boot();

        static::deleting(function (self $bundle): void {
            $bundle->cart_bundle_items->map->delete();
        });
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function cart()
    {
        return $this->hasOne(Cart::class, 'id', 'cart_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cart_bundle_items()
    {
        return $this->hasMany(CartBundleItem::class, 'cart_bundle_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function bundle()
    {
        return $this->hasOne(Product::class, 'id', 'bundle_product_id');
    }

}
