<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.4.2017 г.
 * Time: 17:10 ч.
 */

namespace App\Models\Store;

use App\Helper\Store\Abstraction\Concerns\Item\Options\ItemOption;
use App\Helper\Store\Contracts\Concerns\Item\Options\ItemOptionContract;
use App\Models\Base\AbstractCartItemOptions;
use App\Models\Layout\FormFieldOptions;
use App\Models\Layout\FormFields;
use App\Models\Queue\SiteQueue;
use App\Models\System\Storage as StorageSize;

/**
 * @property null|FormFieldOptions $field_value
 * @property null|FormFields $field
 * @property CartItem $item
 */
class CartItemOption extends AbstractCartItemOptions implements ItemOptionContract
{
    //
    use ItemOption;

    public $hidden = [
        'id', 'vat', 'field', 'field_value'
    ];

    public $appends = [
        'file_url', 'file_name', 'file_size', 'file_path',
        'file_size_formatted', 'file_mime', 'file_extension',
        'value_formatted'
    ];

    protected $data = [];

    #[\Override]
    protected static function boot()
    {
        parent::boot();

        static::created(function (CartItemOption $option): void {
            if ($option->type == 'file') {
                $value = json_decode($option->value, true);
                $option->storage()->create([
                    'size' => $value['size']
                ]);
            }
        });

        static::deleting(function (CartItemOption $option): void {
            if ($option->type == 'file') {
                $option->storage()->delete();
            }
        });

        static::deleted(function (CartItemOption $option): void {
            if ($option->type == 'file') {
                SiteQueue::executeQueueTask('delete_storage_file', ['file' => $option->file_path]);
            }
        });
    }

    public function item()
    {
        return $this->hasOne(CartItem::class, 'id', 'item_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|FormFields
     */
    public function field()
    {
        return $this->hasOne(FormFields::class, 'id', 'field_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|FormFieldOptions
     */
    public function field_value()
    {
        return $this->hasOne(FormFieldOptions::class, 'id', 'field_option_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphOne|StorageSize
     */
    public function storage()
    {
        return $this->morphOne(StorageSize::class, 'item');
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getFileUrlAttribute($value)
    {
        if (!$this->value || $this->type != 'file') {
            return null;
        }
        if (empty($this->data['file_url'])) {
            if ($value = json_decode($this->value) && $this->id) {
                //                $this->data['file_url'] = config('url.storage') . $value->path . $value->storage_name;
//                    $this->data['file_url'] = route('cart.file', $this->id);
                $this->data['file_url'] = \Linker::fullLink(\Linker::cart('file/' . $this->id));
            } else {
                $this->data['file_url'] = null;
            }
        }
        return $this->data['file_url'];
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getFileNameAttribute($value)
    {
        if (!$this->value || $this->type != 'file') {
            return null;
        }
        if (empty($this->data['file_name'])) {
            if ($value = json_decode($this->value)) {
                $this->data['file_name'] = $value->name;
            } else {
                $this->data['file_name'] = null;
            }
        }
        return $this->data['file_name'];
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getFileSizeAttribute($value)
    {
        if (!$this->value || $this->type != 'file') {
            return null;
        }
        if (empty($this->data['file_size'])) {
            if ($value = json_decode($this->value)) {
                $this->data['file_size'] = $value->size;
            } else {
                $this->data['file_size'] = null;
            }
        }
        return $this->data['file_size'];
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getFileSizeFormattedAttribute($value)
    {
        if (!$this->value || $this->type != 'file') {
            return null;
        }
        if (empty($this->data['file_size_formatted'])) {
            $this->data['file_size_formatted'] = format_bytes((int) $this->file_size);
        }
        return $this->data['file_size_formatted'];
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getFileMimeAttribute($value)
    {
        if (!$this->value || $this->type != 'file') {
            return null;
        }
        if (empty($this->data['file_mime'])) {
            if ($value = json_decode($this->value)) {
                $this->data['file_mime'] = $value->mime;
            } else {
                $this->data['file_mime'] = null;
            }
        }
        return $this->data['file_mime'];
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getFileExtensionAttribute($value)
    {
        if (!$this->value || $this->type != 'file') {
            return null;
        }
        if (empty($this->data['file_extension'])) {
            if ($value = json_decode($this->value)) {
                $this->data['file_extension'] = $value->extension;
            } else {
                $this->data['file_extension'] = null;
            }
        }
        return $this->data['file_extension'];
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getFilePathAttribute($value)
    {
        if (!$this->value || $this->type != 'file') {
            return null;
        }
        if (empty($this->data['file_path'])) {
            if ($value = json_decode($this->value)) {
                $this->data['file_path'] = $value->path . $value->storage_name;
            } else {
                $this->data['file_path'] = null;
            }
        }
        return $this->data['file_path'];
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getValueFormattedAttribute($value)
    {
        if (in_array($this->type, ['length', 'weight'])) {
            return implode(' ', [$this->value, $this->field->symbol]);
        }
        if (in_array($this->type, ['square'])) {
            [$length, $width] = explode('x', $this->value);
            return implode(' ', [__('sf.length'), $length, $this->field->symbol ?? '', __('sf.width'), $width, $this->field->symbol ?? '']);
        }
        return $this->value;
    }

}
