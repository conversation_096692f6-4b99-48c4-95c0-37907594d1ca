<?php

declare(strict_types=1);

namespace App\Models\Product;

use App\Helper\ArrayCache;
use App\Traits\Crudling;
use App\Traits\Image as TraitImage;
use Linker;
use App\Models\Base\AbstractTagsProductsTags;
use App\Traits\Tags;
use App\Exceptions\Fault;

class Tag extends AbstractTagsProductsTags
{
    use Tags;
    use Crudling;
    use TraitImage;

    /**
     * Get the items associated with given tag
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function items()
    {
        return $this->belongsToMany(Product::class, 'tags__products_tags__items', 'tag_id', 'item_id');
    }

    /**
     * @param $item_id
     *
     * @return null
     */
    protected function _getItemTagsCount($item_id)
    {
        return ProductTag::whereItemId($item_id)->count();
    }

    /**
     * Adds new tags for item by tag ids provided in array.
     *
     * @param int $item_id
     * @param array $tag_ids
     *
     * @throws Fault
     */
    public function addItemTagsByTagIds($item_id, array $tag_ids): void
    {
        if (empty($tag_ids)) {
            throw new Fault('tag_ids cannot be empty');
        }

        foreach ($tag_ids as $tag_id) {
            ProductTag::firstOrCreate([
                'item_id' => $item_id,
                'tag_id' => $tag_id
            ]);
        }
    }

    /**
     * Link generate.
     *
     * @return string
     */
    public function url()
    {
        return Linker::fullLink('/tags/' . $this->url_handle);
    }

    //filters
    /**
     * @param mixed $urlHandler
     * @return mixed
     */
    public static function getTagForFilter($urlHandler)
    {
        return ArrayCache::remember(__FUNCTION__ . json_encode($urlHandler), fn () => static::urlHandle($urlHandler)->first());
    }
}
