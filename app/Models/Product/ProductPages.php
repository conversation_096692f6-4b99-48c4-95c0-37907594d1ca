<?php

declare(strict_types=1);

namespace App\Models\Product;

use App\Models\Base\AbstractProductsPages;
use App\Models\Page\Page;
use App\Traits\Crudling;

class ProductPages extends AbstractProductsPages
{
    use Crudling;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|Product
     */
    public function product()
    {
        return $this->hasOne(Product::class, 'id', 'product_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|Page
     */
    public function page()
    {
        return $this->belongsTo(Page::class);
    }

    protected function casts(): array
    {
        return [
            'days' => 'int',
        ];
    }


}
