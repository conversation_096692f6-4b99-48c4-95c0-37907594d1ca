<?php

declare (strict_types=1);
namespace App\Models\Product;

use Api\Traits\Categories;
use App;
use App\Contracts\ApiModelContract;
use App\Events\Models\CategoryCreated;
use App\Events\Models\CategoryDeleted;
use App\Events\Models\CategoryUpdated;
use App\Exceptions\Error;
use App\Helper\ArrayCache;
use App\Helper\ArrayTree;
use App\Helper\SiteCp\Tools;
use App\Helper\StorageUrl;
use App\Helper\Text;
use App\Integration\Algolia\Traits\Searchable;
use App\Models\Apps\GoogleProductCategory;
use App\Models\Base\AbstractTypeProductsCategories;
use App\Models\Category\CategoryPath;
use App\Models\Category\CategoryRestriction;
use App\Models\Category\Property;
use App\Models\Discount\Discount;
use App\Models\Setting\UrlHandleHistory;
use App\Models\System\ExternalMetaData;
use App\Scopes\CategoryWithBundles;
use App\Scopes\CategoryWithProducts;
use App\Traits\AdminLogging;
use App\Traits\Api as ApiTrait;
use App\Traits\Crudling;
use App\Traits\DescriptionImageToStorage;
use App\Traits\Icon;
use App\Traits\Image as TraitImage;
use App\Traits\JqTree;
use App\Traits\Navigations;
use App\Traits\PlanUsage;
use App\Traits\Redirect301;
use Cache;
use Carbon\Carbon;
use App\Events\HookEvent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\Apps\Administration\ProductOptions\Model\Options;
use Modules\Apps\Imports\XmlImport\Models\XmlImportTask;
use Pagging;
/**
 * Class Category
 *
 * @property Category|null $parent
 * @property CategoryPath[]|Collection $categoryPath
 * @property Collection|Category[] $children
 * @property Collection|Product[] $items
 * @property Category[]|Collection $path
 * @property Collection|Property[] $properties
 * @property Category|null $parent_lite
 * @property int $properties_count
 * @property null|GoogleProductCategory $google_taxonomy
 * @property null|int $real_products_count
 * @package App\Models\Product
 * @method static Category find($id, $columns = [])
 */
class Category extends AbstractTypeProductsCategories implements ApiModelContract
{
    use Categories;
    use Searchable;
    use JqTree;
    use TraitImage;
    use Crudling;
    use Navigations;
    use Icon;
    use PlanUsage;
    use Redirect301;
    use AdminLogging;
    use ApiTrait {
        ApiTrait::api as traitApi;
    }
    use DescriptionImageToStorage;
    /** set url handle has field for edit */
    protected $has_edit_handle_field = true;

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['img', 'url'];

    /**
     * @var array
     */
    protected $_data = [];

    protected $updatedAttributes;

    public const API_KEYS = ['id', 'url', 'name', 'description', 'date_modified', 'img', 'parent', 'seo_title', 'seo_description', 'parent_id'];

    public static $category_max_level = 6;

    /**
     *
     */
    #[\Override]
    protected static function boot()
    {
        parent::boot();
        static::creating(function (Category $category): void {
            /* sort order */
            $last = static::whereParentId($category->parent_id)->orderBy('order', 'desc')->first(['order']);
            if ($last) {
                $order_index = $last->order + 1;
            } else {
                $order_index = 0;
            }

            $category->order = $order_index;
        });
        static::created(function (Category $category): void {
            CategoryPath::onInsertCategory($category);
            event(new HookEvent(HookEvent::CategoryCreated, $category->api()));
            event(new CategoryCreated($category));
        });
        static::updating(function (Category $category): void {
            $category->updatedAttributes = $category->getDirty();
        });
        static::updated(function (Category $category): void {
            CategoryPath::onUpdateCategory($category);
            event(new HookEvent(HookEvent::CategoryUpdated, $category->api()));
            event(new CategoryUpdated($category, $category->updatedAttributes));
        });
        static::deleting(function (Category $category): void {
            if ($category->items()->count()) {
                throw new Error(__('category.err.cannot_delete_category_has_products'));
            }
        });
        static::deleted(function (Category $category): void {
            event(new HookEvent(HookEvent::CategoryDeleted, $category));
            event(new CategoryDeleted($category));
            $category->categoryPath()->delete();
            $category->restrictions()->delete();
        });
        if (app_namespace() == 'site') {
            if (activeRoute('bundles.list.*')) {
                static::addGlobalScope(new CategoryWithBundles());
                //                static::withoutGlobalScope(new CategoryWithProducts());
            } else {
                static::addGlobalScope(new CategoryWithProducts());
                //                static::withoutGlobalScope(new CategoryWithBundles());
            }
        }
    }

    /**
     * Parent Name: Category
     * Name: scopeWithTreeNode
     * Type: public
     *
     * DESCRIPTION: This method modifies the query for the category model.
     * It adds additional relationships to retrieve the count of child categories and properties, as well as includes the associated google taxonomy.
     * The method also appends a subquery that counts the distinct product IDs associated with the category, enabling efficient retrieval of related product data.
     * The resulting query is structured to support complex filtering and counting, enhancing the backend capabilities for categories.
     *
     * BUSINESS_LOGIC: The purpose of 'scopeWithTreeNode' is to extend the Laravel query builder for the category model, allowing developers to fetch categories along with relevant hierarchical information and related product counts.
     * This method optimizes the retrieval of data necessary for displaying complex category trees in the eCommerce platform.
     *
     * SUMMARY: Extend category query to include relationship counts and product information.
     *
     * @param Builder $query The query builder instance that is being modified by this scope.
     * @return void This method does not return a value but modifies the query builder directly.
     */
    public function scopeWithTreeNode($query): void
    {
        /** @var Builder $query */
        $query->withCount(['children', 'properties'])->with('google_taxonomy')->selectSub('(
                SELECT COUNT(DISTINCT id) FROM (
                    (SELECT id, category_id FROM products)
                    UNION
                    (SELECT product_id as id, category_id FROM product_to_category)
                ) AS tmp WHERE category_id IN(SELECT category_id FROM category_path WHERE path_id = type__products_categories.id)
            )', 'real_products_count');
    }

    /**
     * Parent Name: Category
     * Name: scopeWhereHasProducts
     * Type: public
     *
     * DESCRIPTION: This method scopes the query to filter categories that have associated products based on a complex subquery. It checks if there are products related to the current category by utilizing a raw SQL query that counts distinct product IDs from two different sources: a direct products table and a product-to-category association table. The resultant count is used to determine if the category has any products linked to it, effectively filtering out categories without products.
     * BUSINESS_LOGIC: The purpose of this scope is to provide an easy way to filter categories in an Eloquent query that have products associated with them. It leverages raw SQL for performance by directly checking the underlying data relationships in the database.
     * SUMMARY: Filters categories that have associated products.
     *
     * @param Builder $query The query builder instance to apply the scope to.
     * @return void This method does not return a value, it modifies the query in place.
     */
    public function scopeWhereHasProducts($query): void
    {
        /** @var Builder $query */
        $query->whereRaw('(
                SELECT COUNT(DISTINCT id) FROM (
                    (SELECT id, category_id FROM products)
                    UNION
                    (SELECT product_id as id, category_id FROM product_to_category)
                ) AS tmp WHERE category_id IN(SELECT category_id FROM category_path WHERE path_id = type__products_categories.id)
            ) > 0');
    }

    /**
     * @param Builder $query
     */
    public function scopeWhereDoesntHaveProducts($query): void
    {
        /** @var Builder $query */
        $query->whereRaw('(
                SELECT COUNT(DISTINCT id) FROM (
                    (SELECT id, category_id FROM products)
                    UNION
                    (SELECT product_id as id, category_id FROM product_to_category)
                ) AS tmp WHERE category_id IN(SELECT category_id FROM category_path WHERE path_id = type__products_categories.id)
            ) = 0');
    }

    /**
     * Scope a query to only include active categories.
     * 
     * TODO: Implement proper active status filtering when a status column is added to the table.
     * Currently, all categories are considered active since there's no status column in type__products_categories.
     *
     * @param Builder $query The query builder instance
     * @return void This method does not return a value, it modifies the query in place.
     */
    public function scopeActive($query): void
    {
        // No modifications needed as all categories are considered active
    }

    /**
     * Category taxonomy
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|GoogleProductCategory
     */
    public function google_taxonomy()
    {
        return $this->hasOne(GoogleProductCategory::class, 'id', 'taxonomy_id');
    }

    /**
     * Category taxonomy
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function restrictions()
    {
        return $this->hasMany(CategoryRestriction::class);
    }


    /**
     * $this->>products() alias
     *
     * @return HasMany|\App\Models\Product\Product
     */
    public function items()
    {
        return $this->products();
    }

    /**
     * Category Products
     *
     * @return HasMany|\App\Models\Product\Product
     */
    public function products()
    {
        return $this->hasMany(Product::class, 'category_id', 'id');
    }

    /**
     * Category Products
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany|Product
     */
    public function product_to_category()
    {
        return $this->belongsToMany(Product::class, 'product_to_category', 'category_id', 'product_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphToMany|Options
     */
    public function fields()
    {
        return $this->morphToMany(Options::class, 'item', 'form_field_mapping', null, 'field_id')->with(['options' => function ($query): void {
            /** @var App\Models\Layout\FormFieldOptions $query */
            $query->orderBy('sort_order', 'asc');
        }])->active();
    }

    /**
     * Category Products
     *
     * @return \App\Models\Product\Product
     */
    public function activeItems()
    {
        return $this->items()->visible();
    }

    /**
     * Category parent
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|Category
     */
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id', 'id');
    }

    /**
     * Category parent
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|Category
     */
    public function parent_lite()
    {
        return $this->belongsTo(Category::class, 'parent_id', 'id')->withoutGlobalScopes();
    }

    /**
     * Category parent
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|Category
     */
    public function parent_reqursive()
    {
        return $this->belongsTo(Category::class, 'parent_id', 'id')->withoutGlobalScopes()->with('parent_reqursive');
    }

    /*
     * Category children
     *
     * @return \App\Models\Product\Category
     */
    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|Category
     */
    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id', 'id')->orderBy('order', 'asc');
    }

    /*
     * Category children
     *
     * @return \App\Models\Product\Category
     */
    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|Category
     */
    public function children_no_order()
    {
        return $this->hasMany(Category::class, 'parent_id', 'id');
    }

    /*
     * Category children
     *
     * @return \App\Models\Product\Category
     */
    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|CategoryPath
     */
    public function pathChilds()
    {
        return $this->hasMany(CategoryPath::class, 'path_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|CategoryPath
     */
    public function pathParents()
    {
        return $this->hasMany(CategoryPath::class, 'category_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|CategoryPath
     */
    public function categoryPath()
    {
        return $this->hasMany(CategoryPath::class, 'category_id', 'id')->orderBy('level', 'asc');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany|Category
     */
    public function path()
    {
        return $this->belongsToMany(Category::class, 'category_path', 'category_id', 'path_id')->withoutGlobalScope(new CategoryWithProducts())->orderBy('category_path.level', 'asc')->withPivot(['level']);
    }

    /**
     * @return HasMany|CategoryPath
     */
    public function child_by_path()
    {
        return $this->hasMany(CategoryPath::class, 'path_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|CategoryPath
     */
    public function categoryPathWithPath()
    {
        return $this->categoryPath()->with('path');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|CategoryPath
     */
    public function categoryPathWithFields()
    {
        return $this->categoryPath()->with('path.fields');
    }

    /**
     * Category children
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|Category
     */
    public function childrenReqursive()
    {
        return $this->hasMany(Category::class, 'parent_id', 'id')->with('childrenReqursive');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany|Property
     */
    public function properties()
    {
        return $this->belongsToMany(Property::class, 'property_to_category', 'category_id', 'property_id', 'id');
    }

    /**
     * @param null $order
     * @param string $direction
     *
     * @param \Closure $where
     * @return mixed
     */
    public static function loadCategoriesByOrder($order = null, string $direction = 'asc', ?\Closure $where = null)
    {
        static $cache = [];
        if (isset($cache[$order . $direction])) {
            return $cache[$order . $direction];
        }

        /** @var Category $model */
        $model = static::query();
        if ($order) {
            $model = $model->orderBy($order, $direction);
        }

        if ($where !== null) {
            $where($model);
        }

        return $cache[$order . $direction] = $model->get();
    }

    /**
     * @param null $order
     * @param string $direction
     *
     * @return mixed|static
     */
    public static function getArrayRecursive($order = null, string $direction = 'asc')
    {
        static $cache = [];
        return $cache[$order . $direction] ?? $cache[$order . $direction] = Collection::make(ArrayTree::createTree(static::loadCategoriesByOrder($order, $direction)));
    }

    /**
     * Product link generate.
     *
     * @return string
     */
    public function url()
    {
        return \Linker::fullLink('/category/' . $this->url_handle);
    }

    /**
     * @param      $id
     *
     * @return array|null
     */
    public static function parentIds($id)
    {
        return CategoryPath::where('category_id', $id)->orderBy('level', 'asc')->pluck('path_id')->all();
    }

    //
    //    /**
    //     * @param $id
    //     *
    //     * @return bool
    //     */
    //    public static function exists($id)
    //    {
    //        return static::where('id', $id)->count() ? true : false;
    //    }
    /**
     * gets products matching the provided string
     *
     * @param string $name
     * @param \Pagging $pagging_object
     *
     * @return array
     */
    public static function getByNameAutocomplete($name, ?Pagging $pagging_object = null): array
    {
        $model = static::orderBy('id', 'desc')->where('name', 'like', sprintf('%%%s%%', $name));
        if ($pagging_object !== null) {
            $records = $model->paging($pagging_object)->keyBy('id');
        } else {
            $records = $model->get()->keyBy('id');
        }

        $more = $pagging_object->pages > $pagging_object->page ? true : false;
        return ['items' => $records, 'more' => $more];
    }

    /**
     * @param array $fields
     *
     * @return array
     * @throws Error
     */
    public static function getOrInsertByName(array $fields): array
    {
        if (!empty($fields['parent_id'])) {
            $model = static::where('parent_id', $fields['parent_id'])->where('name', 'like', $fields['name'])->withoutGlobalScope(new CategoryWithProducts())->first();
        } else {
            $model = static::where('name', 'like', $fields['name'])->withoutGlobalScope(new CategoryWithProducts())->first();
        }

        if ($model) {
            return [$model->id, false, $model];
        }

        // returning whether the category is new for order incrementation
        // does not exist, add new
        $model = static::add($fields, true, false);
        return [$model->id, true, $model];
    }

    /**
     * @param array $fields
     *
     * @return Category
     * @throws Error
     */
    public static function getOrInsertByNameAndParent(array $fields)
    {
        if (!isset($fields['parent_id'])) {
            $fields['parent_id'] = null;
        }

        $model = static::where('parent_id', $fields['parent_id'])->where('name', 'like', $fields['name'])->withoutGlobalScope(new CategoryWithProducts())->first();
        if ($model) {
            return $model;
        }

        // returning whether the category is new for order incrementation
        // does not exist, add new
        return static::add($fields, true, false);
    }

    /**
     * gets products matching the provided string
     *
     * @param $integration
     * @param $externalRecordKey
     * @return Category|null
     */
    public static function getByExternalMeta($integration, $externalRecordKey)
    {
        $meta = ExternalMetaData::whereIntegration($integration)->where('record_type', 'category')->whereExternalRecordKey($externalRecordKey)->first();
        return $meta->external_meta_data ?? null;
    }

    /**
     * Parent Name: Category
     * Name: add
     * Type: public static
     *
     * DESCRIPTION: This method adds a new category to the system by receiving category data through an associative array.
     * It validates the data if the validation flag is set to true, ensuring the integrity of the category being created.
     * Upon successful validation, it sets common attributes for the category and saves it to the database.
     * Depending on the value of the return_model flag, it can return the model instance of the newly created category or just the ID of the category.
     * When validation rules are violated, it raises an error exception to prevent the insertion of invalid categories.
     * This method is crucial for maintaining organized category structures within the eCommerce platform.
     *
     * BUSINESS_LOGIC: The purpose of this method is to facilitate the creation of new category entries by validating input data and assigning appropriate values to each category attribute before saving the entry.
     * It ensures that all necessary data rules are adhered to, which is essential for reliable product organization and navigation.
     * The method consolidates various functions into a streamlined process for adding categories, ultimately enhancing user experience.
     *
     * SUMMARY: Adds a new category to the system after validation and setting attributes.
     *
     * @param array $fields An associative array containing the data fields for the category to be created, including required fields like name and optional fields as needed.
     * @param bool $return_model Flag indicating whether to return the full category model instance. Defaults to false.
     * @param bool $validate_data Flag to indicate whether to perform data validation before category creation. Defaults to true.
     * @return integer|Category Returns the ID of the newly created category or the category model instance based on the return_model flag.
     * @throws Error Throws an error exception when validation fails, providing feedback on the validation issue related to the category data.
     */
    public static function add(array $fields, $return_model = false, $validate_data = true)
    {
        if ($validate_data) {
            static::_validateCategoryData(null, $fields);
        }

        $category = new static();
        static::_setCategoryCommonAttributes($category, $fields);
        $category->save();
        if ($return_model) {
            return $category;
        }

        return $category->id;
    }

    /**
     * @param array $fields
     *
     * @return \App\Models\Product\Category
     * @throws \App\Exceptions\Error
     */
    public function edit(array $fields): static
    {
        static::_validateCategoryData($this->id, $fields);
        static::_setCategoryCommonAttributes($this, $fields);
        $this->save();
        return $this;
    }

    /**
     * Parent Name: Product\Category
     * Name: remove
     * Type: public static
     *
     * DESCRIPTION: Deletes a category and any associated child categories if they do not contain products or XML import tasks.
     * This method first checks if the provided category ID(s) is valid, then retrieves child category IDs using either a single ID or an array. It validates that the category (or its children) does not have any products and does not participate in active XML import tasks. If all conditions are met, it proceeds to delete all related records, including discounts and URL handle history.
     * BUSINESS_LOGIC: The purpose of this method is to safely remove a category from the database, ensuring that no associated products or tasks prevent deletion. This is critical for maintaining database integrity and efficient management of category records within the eCommerce platform.
     * SUMMARY: Safely deletes a category and its children from the database if no products or tasks are associated.
     *
     * @param integer|array $id The ID or array of IDs of the category or categories to be deleted.
     * @return int The ID(s) of the deleted category or categories.
     * @throws \App\Exceptions\Error If the provided ID is invalid, or if the category has associated products or XML imports.
     * @throws \App\Exceptions\Fault If a fault occurs during the deletion process.
     * @todo name in Crud is ->delete($id)
     */
    public static function remove($id)
    {
        if (empty($id)) {
            throw new Error(__('global.err.invalid_request'));
        }

        // checking if category or children categories have products
        $child_ids = is_array($id) ? static::getChildIdsFromArray($id) : static::getChildIds($id);
        if (\MetaData::getSchema('@app_xml_import_tasks')->_loaded && ($xml = XmlImportTask::whereIn('category_id', (array) $id)->get(['name'])) && $xml->isNotEmpty()) {
            throw new Error(__('category.err.cannot_delete_category_has_xml_import', ['names' => $xml->implode('name', ', ')]));
        }

        if (Product::whereIn('category_id', $child_ids)->count()) {
            throw new Error(__('category.err.cannot_delete_category_has_products'));
        }

        Discount::deleteByItemId($child_ids, 'category');
        static::destroy($child_ids);
        // delete url handle history
        UrlHandleHistory::where('type', 'category')->whereIn('model_id', $child_ids)->delete();
        return $id;
    }

    /**
     * Parent Name: Category
     * Name: _setCategoryCommonAttributes
     * Type: private static
     *
     * DESCRIPTION: This method sets common attributes for a category during insertion and editing operations. It updates the category model's properties based on the provided fields array, ensuring that required and optional data is correctly assigned. It handles data formatting, such as converting to integer for parent ID and providing defaults for optional fields.
     *
     * BUSINESS_LOGIC: The primary purpose of this method is to streamline the setting of category attributes, encapsulating the logic required to transform input data into model properties. It ensures that any essential fields are populated while providing fallback values for optional details, enhancing data integrity during product category management.
     *
     * SUMMARY: Updates category model attributes for create and edit operations based on input fields.
     *
     * @param self $category The category model instance that will be modified.
     * @param array $fields An associative array containing the attributes to be set on the category model.
     * @return void This method does not return a value.
     */
    private static function _setCategoryCommonAttributes(self $category, array $fields = []): void
    {
        //required data
        $category->name = $fields['name'];
        if (isset($fields['make_interval'])) {
            $category->make_interval = $fields['make_interval'];
        }

        //--------------------------------
        //optional data
        if (!empty($fields['order'])) {
            $category->order = $fields['order'];
        }

        $category->parent_id = !empty($fields['parent_id']) ? intval($fields['parent_id']) : null;
        $category->description = !empty($fields['description']) ? $fields['description'] : null;
        $category->url_handle = !empty($fields['url_handle']) ? $fields['url_handle'] : null;
        $category->color = !empty($fields['color']) ? $fields['color'] : null;
        $category->icon = !empty($fields['icon']) ? $fields['icon'] : null;
        $category->icon_data = static::_getIconData(!empty($fields['icon']) ? $fields['icon'] : null);
        if (activeRoute('admin.categories.add admin.categories.edit admin.api.product_categories.store admin.api.product_categories.update')) {
            $category->display_child = $fields['display_child'] ?? 0;
        }

        if (activeRoute('admin.categories.add admin.categories.edit') || !empty($fields['taxonomy_id'])) {
            $category->taxonomy_id = $fields['taxonomy_id'] ?? null;
        }
    }

    /**
     * Parent Name: Category
     * Name: _validateCategoryData
     * Type: private static
     *
     * DESCRIPTION: This method validates the category data for creating or editing a category. It checks for the presence of required fields, such as the category name, and ensures that the values adhere to defined constraints, including length restrictions and uniqueness requirements based on the category's parent.
     * The method raises exceptions when validation rules are violated to prevent invalid category data from being processed further. Specific checks include ensuring the category name is not empty, does not exceed the maximum character limit, and that the parent-child relationship does not lead to circular references.
     * It utilizes helper methods to verify the length of strings and the availability of category names, ensuring integrity of category data in the system.
     *
     * BUSINESS_LOGIC: The purpose of this method is to enforce data integrity when creating or modifying category entries by validating essential attributes. It ensures that categories are appropriately structured, maintain unique identifiers, and follow applicable constraints to avoid conflicts within the eCommerce platform.
     * This process is crucial for maintaining organized category hierarchies and a smooth user experience when navigating products.
     *
     * SUMMARY: Validates category data for creation or modification.
     *
     * @param null|int $category_id Used when validating edit data; it refers to the current category ID to avoid conflicts during validation.
     * @param array $fields An associative array of category data fields to validate, including name, description, and parent ID.
     * @throws \App\Exceptions\Error Throws an error exception when validation fails, providing feedback on the nature of the validation issue shared with the field in question.
     */
    private static function _validateCategoryData($category_id = null, array $fields = []): void
    {
        //name
        if (empty($fields['name'])) {
            throw new Error(__('category.err.name_requred'), 'name');
        }

        if (Text::length($fields['name']) > 191) {
            throw new Error(sprintf(__('category.err.name_max_chars_%1$s'), 191), 'name');
        }

        if (!empty($fields['parent_id'])) {
            if ((int) $fields['parent_id'] === (int) $category_id) {
                throw new Error(__('category.err.parent_cannot_be_itself'), 'parent_id');
            }

            if (App::runningInConsole() ? false : !static::whereKey($fields['parent_id'])->exists()) {
                throw new Error(__('category.err.parent_no_longer_exists'), 'parent_id');
            }

            if (!static::_nameAvailable($fields['name'], $category_id, $fields['parent_id'])) {
                throw new Error(__('category.err.name_taken'), 'name');
            }
        } else if (!static::_nameAvailable($fields['name'], $category_id)) {
            throw new Error(__('category.err.name_taken'), 'name');
        }

        //description
        if (!empty($fields['description']) && Text::length($fields['description']) > 250000) {
            throw new Error(sprintf(__('category.err.description_max_chars_%1$s'), 250000), 'description');
        }
    }

    /**
     * Parent Name: Product
     * Name: _nameAvailable
     * Type: private static
     *
     * DESCRIPTION: This method checks whether a category name is available for a new or existing category. It verifies that the name does not already exist within the specified category's scope by excluding the current category ID (if provided) and considering the parent category ID. The method ensures the uniqueness of category names in relation to their hierarchical organization.
     *
     * BUSINESS_LOGIC: The purpose of this method is to determine if a given name can be assigned to a category without conflicting with existing names. It aids in maintaining unique category names, which is crucial for proper organization and user experience within the eCommerce platform.
     *
     * SUMMARY: Checks for the availability of a category name.
     *
     * @param string $name The name to check for availability.
     * @param null|int $category_id The ID of the category to exclude from the check (if applicable).
     * @param null|int $parent_id The parent ID of the category, used to refine the scope of the name check.
     * @return bool Returns true if the name is available, false otherwise.
     */
    private static function _nameAvailable($name, $category_id = null, $parent_id = null): bool
    {
        $model = static::where('name', $name);
        if ($category_id != null) {
            $model = $model->where('id', '<>', $category_id);
        }

        if ($parent_id != null) {
            $model = $model->where('parent_id', $parent_id);
        } else {
            $model = $model->whereNull('parent_id');
        }

        if ($model->count() == 0) {
            return true;
        }

        return false;
    }

    /**
     * Parent Name: Category
     * Name: getChildIds
     * Type: public static
     *
     * DESCRIPTION: This method retrieves the child category IDs for a given category ID or an array of category IDs. It utilizes caching to optimize performance, ensuring that repeated requests for the same category IDs retrieve results from the cache rather than executing the database query each time.
     * The input category ID(s) is transformed into an array for uniformity, and the `remember` method of the `ArrayCache` class is called to fetch the cached results. If the value is not cached, it triggers a query to the `CategoryPath` model, which fetches the relevant category IDs based on the provided path IDs.
     * BUSINESS_LOGIC: The purpose of this method is to streamline the process of retrieving child category IDs by reducing database calls through effective caching. It aims to enhance performance in scenarios where the same child category ID data might be requested multiple times, thereby ensuring resource efficiency and faster response times for eCommerce operations.
     * SUMMARY: Retrieves child category IDs from cache or database based on the provided parent category ID(s).
     *
     * @param mixed $category_id A single category ID or an array of category IDs for which to fetch the child category IDs.
     * @return array An array of child category IDs associated with the specified category ID(s).
     */
    public static function getChildIds($category_id)
    {
        $category_id = (array) $category_id;
        return ArrayCache::remember('category.path.' . implode('.', $category_id), fn() => CategoryPath::whereIn('path_id', $category_id)->select('category_id')->get()->pluck('category_id')->all());
    }

    /**
     * Parent Name: Product\Category
     * Name: getChildIdsFromArray
     * Type: public
     *
     * DESCRIPTION: This method retrieves child category IDs based on an input array of category IDs. It first constructs a unique key by imploding the category IDs, which is then used for caching the results. If the results for the given category IDs are already cached, they are returned. Otherwise, a query is made to the CategoryPath model to fetch child category IDs associated with the provided category IDs. The results are then cached for future use, improving performance by reducing database queries.
     * BUSINESS_LOGIC: The purpose of this method is to efficiently retrieve child category IDs from a given array of category IDs, utilizing caching to optimize repeated calls with the same parameters. This assists in rapid data access and enhances the overall performance of category-related operations within the application.
     * SUMMARY: Retrieves and caches child category IDs from an array of category IDs.
     *
     * @param array $category_ids An array of category IDs whose child IDs are to be fetched.
     * @return array An array of child category IDs corresponding to the provided category IDs.
     *
     */
    public static function getChildIdsFromArray($category_ids)
    {
        $key = implode('.', $category_ids);
        static $cache = [];
        return $cache[$key] ?? $cache[$key] = CategoryPath::whereIn('path_id', $category_ids)->select('category_id')->get()->pluck('category_id')->all();
    }


    //// event helpers api trigger
    /**
     * @return string
     */
    protected function getUrlAttribute()
    {
        return $this->url();
    }




    /**
     * @param bool $full
     * @return array
     */
    public function api($full = false)
    {
        $data = $this->traitApi($full);
        if (!empty($data['parent'])) {
            $data['parent'] = is_array($data['parent']) ? $data['parent']['name'] : $data['parent']->name;
        }

        if (!empty($data['description'])) {
            $data['description'] = strip_tags(html_entity_decode((string) $data['description'], ENT_QUOTES, 'utf-8'));
        }

        return $data;
    }

    /**
     * @param $type_id
     * @param int $count
     * @return int
     */
    public static function getParentCount($type_id, &$count = 0)
    {
        $category = static::find($type_id);
        if ($category && !is_null($category->parent_id)) {
            $count++;
            static::getParentCount($category->parent_id, $count);
        }

        return $count;
    }

    /**
     * @param $type_id
     * @return int
     */
    public static function getMaxChildCount($type_id)
    {
        $count = 0;
        $types = static::select(['id'])->where('parent_id', intval($type_id))->get();
        if ($types->isEmpty()) {
            return $count;
        }

        foreach ($types as $type) {
            $count = max(1 + static::getMaxChildCount($type->id), $count);
        }

        return $count;
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public static function autoComplete($name, ?\Closure $where = null, ?\Closure $callback = null, string $key = 'results'): array
    {
        $instance = new static();
        /** @var $model Category */
        $model = static::select(['id', $instance->_getNameField() . ' AS name']);
        if ($where !== null) {
            $where($model);
        }

        $results = Collection::make(ArrayTree::createAutocomplete(static::loadCategoriesByOrder('order', 'asc', $where)))->filter(fn(Category $category) => Text::length($name) > 0 ? Str::contains(Str::lower($category->name), Str::lower($name)) : true)->map(fn(Category $category) => (object) ['id' => $category->id, 'name' => $category->name])->values();
        return [$key => $callback ? $callback($results) : $results, 'more' => false];
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs(): string
    {
        return 'categories';
    }

    /**
     * @return int
     */
    public function getScoutKey()
    {
        return $this->id;
    }

    /**
     * @return array
     */
    public function toSearchableArray(): array
    {
        return [
            'name' => $this->name,
            //            'description' => $this->description,
            'url' => $this->url,
        ];
    }

    /**
     * @param true|null|integer $default
     * @return mixed
     */
    public function formatNode($default = null): array
    {
        $response = ['label' => '', 'isDefault' => $default === true || $default == $this->id];
        if ($this->children_count != 0) {
            $response['label'] .= ' <span class="children_count item-counter">(' . $this->children_count . ' ' . __('global.subcategories') . ')</span>';
            $response['load_on_demand'] = true;
        } else {
            $response['load_on_demand'] = false;
        }

        $response['label'] .= '<a class="editable" href="' . route('admin.categories.edit', $this->id) . '" data-panel-class="wide" data-ajax-panel="true">' . $this->name . '</a>' . Tools::dataHolder('x fal fa-truck-loading', __('global.products'), $this->real_products_count, route('admin.products.list', ['filters' => ['category' => $this->id]]), '_blank');
        $response['label'] .= '<a target="_blank" href="' . route('admin.property.list', ['', 'filters' => ['category' => $this->id]]) . '" class="count-holder action js-cat-properties-button"><span><i class="fal fa-sliders-v"></i> ' . __('category.properties.label.properties') . ' (' . (int) $this->properties_count . ')</span></a>';
        if ($this->taxonomy_id && $this->google_taxonomy) {
            $style = ' style="border-color: #28a745;" ';
            $spanStyle = '';
            //$label = __('category.label.taxonomy') . ': (' . trim(last(explode('>', $this->google_taxonomy->name))) . ')';
            $label = trim((string) last(explode('>', (string) $this->google_taxonomy->name)));
        } else {
            $style = ' style="border-color: #cc0000; background-color: #ff6666;" ';
            $spanStyle = ' style="color: #ffffff;"';
            $label = __('category.label.define.taxonomy');
        }

        //        $response['label'] .= '<a target="_blank" href="#" class="count-holder"'
        //            . $style . 'data-modal-size="medium" data-modal-ajax="'
        //            . route('admin.categories.taxonomy.laod', ['id' => $this->id]) . '">'
        //            . '<span' . $spanStyle . '>' . $label . '</span></a>';
        $response['label'] .= '<a target="_blank" href="' . route('admin.categories.taxonomy.laod', ['id' => $this->id]) . '" class="count-holder"' . $style . 'data-panel-class="medium" data-ajax-panel="true">' . '<span' . $spanStyle . '>' . $label . '</span></a>';
        $response['id'] = $this->id;
        $response['controls'] = Tools::delete(route('admin.categories.delete', $this->id), true);
        return $response;
    }

    //@todo for trait JqTree::toJqTree()
    /**
     * Category children
     *
     * @return HasMany|Category
     */
    public function jqTreeChild()
    {
        return $this->hasMany(Category::class, 'parent_id', 'id')->with('jqTreeChild')->orderBy('order', 'asc')->withTreeNode();
    }

    /**
     * @return null|string
     */
    protected function getJqTreeName(): ?string
    {
        return $this->formatNode()['label'];
    }

    /**
     * @return null|string
     */
    protected function getJqTreeControls(): ?string
    {
        return Tools::delete(route('admin.categories.delete', $this->id), true);
    }

    //////////////////////////////////// reorder category ////////////////////////////////////
    /**
     * Move category up to category
     *
     * @param Category $category
     * @return Category
     * @throws Error
     */
    public function rePositionBefore(Category $category): static
    {
        $this->validateRePosition($category, $this->parent_id != $category->parent_id);
        $this->setAttribute('old_parent_id', $this->parent_id)->syncOriginal();
        static::whereParentId($category->parent_id)->where('order', '>=', $category->order)->update(['order' => \Illuminate\Support\Facades\DB::raw('`order`+1')]);
        $this->update(['order' => $category->order, 'parent_id' => $category->parent_id]);
        //@todo fix broken orders
        \Illuminate\Support\Facades\DB::statement('SET @category_row:=0;');
        static::whereParentId($category->parent_id)->orderBy('order', 'asc')->update(['order' => \Illuminate\Support\Facades\DB::raw('(SELECT @category_row:=(@category_row+1))')]);
        if ($this->getAttribute('old_parent_id') != $this->parent_id) {
            CategoryPath::repairCategories();
        }

        return $this;
    }

    /**
     * Move category down from category
     *
     * @param Category $category
     * @return Category
     * @throws Error
     */
    public function rePositionAfter(Category $category): static
    {
        $this->validateRePosition($category, $this->parent_id != $category->parent_id);
        $this->setAttribute('old_parent_id', $this->parent_id)->syncOriginal();
        static::whereParentId($category->parent_id)->where('order', '>', $category->order)->update(['order' => \Illuminate\Support\Facades\DB::raw('`order`+1')]);
        $this->update(['order' => $category->order + 1, 'parent_id' => $category->parent_id]);
        //@todo fix broken orders
        \Illuminate\Support\Facades\DB::statement('SET @category_row:=0;');
        static::whereParentId($category->parent_id)->orderBy('order', 'asc')->update(['order' => \Illuminate\Support\Facades\DB::raw('(SELECT @category_row:=(@category_row+1))')]);
        if ($this->getAttribute('old_parent_id') != $this->parent_id) {
            CategoryPath::repairCategories();
        }

        return $this;
    }

    /**
     * Move category in category
     *
     * @param Category $category
     * @return Category
     * @throws Error
     */
    public function rePositionInside(Category $category): static
    {
        $this->validateRePosition($category, $this->parent_id != $category->parent_id);
        $this->setAttribute('old_parent_id', $this->parent_id)->syncOriginal();
        //@todo fix broken orders
        \Illuminate\Support\Facades\DB::statement('SET @category_row:=1;');
        static::whereParentId($category->id)->orderBy('order', 'asc')->update(['order' => \Illuminate\Support\Facades\DB::raw('(SELECT @category_row:=(@category_row+1))')]);
        $this->update(['order' => 1, 'parent_id' => $category->id]);
        if ($this->getAttribute('old_parent_id') != $this->parent_id) {
            CategoryPath::repairCategories();
        }

        return $this;
    }

    /**
     * @param Category $category
     * @param bool $parent_changed
     * @return bool
     * @throws Error
     */
    protected function validateRePosition(Category $category, $parent_changed = true): bool
    {
        if (!$this->exists) {
            throw new Error(__('category.err.moved_no_longer_exists'));
        }

        if (!$category->exists) {
            throw new Error(__('category.err.moved_no_longer_exists'));
        }

        if ($this->id == $category->id) {
            throw new Error(__('category.err.moved_and_target_cannot_be_the_same'));
        }

        if ($parent_changed) {
            // checking child parent constraint
            $child_ids = static::getChildIds($this->id);
            if (in_array($category->id, $child_ids)) {
                throw new Error(__('category.err.target_is_child_of_moved'));
            }

            // MAX DEPTH CHECK
            $depth = static::getParentCount($category->id) + static::getMaxChildCount($this->id);
            if ($depth > static::$category_max_level) {
                throw new Error(sprintf(__('category.err.max_depth_is_%1$s'), static::$category_max_level));
            }

            // validating name in scope
            if (!static::_nameAvailable($this->name, $this->id, $category->id)) {
                throw new Error(__('category.err.name_taken'), 'name');
            }
        }

        return true;
    }

    /**
     * @param array $category_ids
     *
     * @return array|bool
     */
    public static function checkCategoryIds(array $category_ids)
    {
        if (empty($category_ids)) {
            return false;
        }

        $categories = static::withoutGlobalScopes()->whereIn('id', $category_ids)->get()->keyBy('id');
        if ($categories->isEmpty()) {
            return false;
        }

        $all_categories = Category::withoutGlobalScopes()->get()->keyBy('id')->all();
        $existing = [];
        foreach ($categories as $category) {
            if (empty($category->parent_id)) {
                continue;
            }

            static::_handleCheckCategoryRecursions($category, $all_categories, $category_ids, $existing);
        }

        if (empty($existing)) {
            return false;
        }

        return $existing;
    }

    /**
     * @param      $parent_category
     * @param      $categories
     * @param      $category_ids
     * @param      $existing
     * @param null $child_category
     */
    protected static function _handleCheckCategoryRecursions(&$parent_category, &$categories, &$category_ids, &$existing, $child_category = null)
    {
        if (in_array($parent_category->parent_id, $category_ids)) {
            $category_id = $parent_category->id;
            if ($child_category != null) {
                $category_id = $child_category->id;
            }

            if (isset($existing[$parent_category->parent_id])) {
                if (!in_array($category_id, $existing[$parent_category->parent_id])) {
                    $existing[$parent_category->parent_id][] = $category_id;
                }
            } else {
                $existing[$parent_category->parent_id][] = $category_id;
            }

            unset($categories[$category_id]);
        } elseif (isset($categories[$parent_category->parent_id])) {
            if ($child_category != null && $parent_category->id == $child_category->parent_id) {
                static::_handleCheckCategoryRecursions($categories[$parent_category->parent_id], $categories, $category_ids, $existing, $child_category);
            } else {
                static::_handleCheckCategoryRecursions($categories[$parent_category->parent_id], $categories, $category_ids, $existing, $parent_category);
            }
        }
    }

    //filters

    /**
     * @param mixed $urlHandler
     * @return Category|null
     */
    public static function getCategoryForFilter($urlHandler): ?self
    {
        return ArrayCache::remember(__FUNCTION__ . json_encode($urlHandler), fn() => static::urlHandle($urlHandler)->with('path', 'child_by_path')->withCount('properties')->first());
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getGoogleTaxonomyAttribute($value)
    {
        if (!$this->taxonomy_id) {
            return;
        }

        if ($this->relationLoaded('google_taxonomy')) {
            return $this->getRelationValue('google_taxonomy');
        }

        return Cache::remember('google_taxonomy_' . $this->taxonomy_id, config('cache.ttl_1d'), fn() => GoogleProductCategory::getAll()->firstWhere('id', $this->taxonomy_id));
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDescriptionAttribute($value): mixed
    {
        return StorageUrl::read(empty($this->attributes['description']) ? null : $this->attributes['description']);
    }
    /**
     * @param mixed $description
     * @return mixed
     */
    public function setDescriptionAttribute($description): mixed
    {
        $this->attributes['description'] = StorageUrl::write($description);
        return $this;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getImgAttribute($value)
    {
        return $this->getImage();
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getHasPropertiesAttribute($value)
    {
        if (array_key_exists('has_properties', $this->_data)) {
            return $this->_data['has_properties'];
        }

        return $this->_data['has_properties'] = !!(is_null($this->properties_count) ? $this->properties()->first(['property_to_category.id']) : $this->properties_count);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDateModifiedAttribute($value)
    {
        return $this->updated_at ?: $this->asDateTime($this->attributes['date_modified'] ?? null);
    }
}
