<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 1.1.2017 г.
 * Time: 22:33 ч.
 */

namespace App\Models\Apps\Zapier;

use App\Models\Apps\Applications;
use Illuminate\Database\Eloquent\Model as Eloquent;
use Illuminate\Support\Collection;

/**
 *
 * @method Zapier first()
 * @method Collection get()
 *
 * @property mixed $id
 * @property mixed $key
 * @property mixed $created_at
 * @property mixed $updated_at
 * @property mixed $icon
 * @property mixed $name
 * @property mixed $description
 * @property Applications $app
 */
class Zapier extends Eloquent
{
    public const ZAPIER_PREFIX = 'zapier.apps.';

    protected $connection = 'apps';

    protected $table = 'cc_apps.zapier_apps';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    public function app()
    {
        return $this->belongsTo(Applications::class, 'app_id');
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getKeyAttribute($value)
    {
        return \Illuminate\Support\Arr::last(explode('.', (string) $this->app->key));
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getNameAttribute($value)
    {
        return $this->app->name;
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDescriptionAttribute($value)
    {
        return $this->app->description;
    }
}
