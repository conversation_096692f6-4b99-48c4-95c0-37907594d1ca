<?php

declare(strict_types=1);

namespace App\Models\Apps;

use App\Locale\Country;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Model as Eloquent;

/**
 * Class Discount
 *
 * @property int $id
 * @property int $record_id
 * @property string $record_type
 * @property string $type
 * @property string $country
 */
class CountryLimitations extends Eloquent
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'apps';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'cc_apps.country_limitations';


    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    public const string TYPE_FILTER = 'filter';

    public const string TYPE_SHOW = 'show';

    public const string TYPE_HIDE = 'hide';

    /**
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if (app_namespace() === 'sitecp') {
            $this->append(['country_name']);
        }
    }

    /**
     * @return MorphTo
     */
    public function record(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getCountryNameAttribute($value)
    {
        if (!$this->country) {
            return null;
        }
        return Country::get($this->country, site('language_cp'))['name'] ?? null;
    }

}
