<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 4.11.2016 г.
 * Time: 12:44 ч.
 */

namespace App\Models\StoreFront;

use App\Models\Base\AbstractFrontWidgets;

class FrontWidget extends AbstractFrontWidgets
{
    protected function casts(): array
    {
        return [
            'settings' => 'array'
        ];
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getSettingsAttribute($value)
    {
        if (array_key_exists('settings', $this->attributes) && is_array($this->attributes['settings'])) {
            return $this->attributes['settings'];
        } elseif (!is_array($settings = ($this->attributes['settings'] ?? null))) {
            return $settings ? $this->fromJson($settings) : [];
        }
        return [];
    }

}
