<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 31.8.2017 г.
 * Time: 09:48 ч.
 */

namespace App\Models\GeoZone;

use App\Models\Base\AbstractGeoPolygons;
use App\Traits\Crudling;
use Cloudcart\LaravelMysqlSpatial\Eloquent\SpatialTrait;
use Cloudcart\LaravelMysqlSpatial\Types\MultiPolygon;
use Cloudcart\LaravelMysqlSpatial\Types\Polygon;
use Illuminate\Support\Collection;

/**
 * @property Polygon $area
 */
class GeoPolygons extends AbstractGeoPolygons
{
    use SpatialTrait;
    use Crudling;

    protected $spatialFields = [
        'area'
    ];

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPolygonPathsAttribute($value)
    {
        if ($this->area instanceof Polygon && ($coordinates = $this->area->jsonSerialize()->getCoordinates()) && !empty($coordinates[0])) {
            return collect($coordinates[0])->map(fn ($point): array => [
                'lat' => $point[1],
                'lng' => $point[0],
            ]);
        } elseif ($this->area instanceof MultiPolygon && ($coordinates = $this->area->jsonSerialize()->getCoordinates()) && !empty($coordinates[0])) {
            return collect($coordinates)->map(fn ($points) => collect($points[0])->map(fn ($point): array => [
                'lat' => $point[1],
                'lng' => $point[0],
            ])->all());
        }
        return null;
    }

}
