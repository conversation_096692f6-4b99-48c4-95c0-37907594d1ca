<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 31.8.2017 г.
 * Time: 09:48 ч.
 */

namespace App\Models\GeoZone;

use App\Helper\GeoZone\GeoZoneFilterInformation;
use App\Models\Base\AbstractGeoZones;
use App\Traits\AutoComplete;
use App\Traits\Crudling;
use Cloudcart\LaravelMysqlSpatial\Types\Point;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

/**
 * @property Collection|GeoZoneValues[] $values
 */
class GeoZones extends AbstractGeoZones
{
    use Crudling;
    use AutoComplete;

    public $appends = [
        'values_count'
    ];

    public $data = [];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|\App\Models\GeoZone\GeoZoneValues
     */
    public function values()
    {
        return $this->hasMany(GeoZoneValues::class, 'zone_id', 'id')
            ->with('post_codes');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|\App\Models\GeoZone\GeoZoneValues
     */
    public function totalValuesCount()
    {
        /** @var \Illuminate\Database\Eloquent\Relations\HasOne|\App\Models\GeoZone\GeoZoneValues $query */
        $query = $this->hasOne(GeoZoneValues::class, 'zone_id', 'id');
        $query = $query->groupBy('zone_id');
        return $query->selectRaw('*, COUNT(id) AS total');
    }

    /**
     * @param null $operation
     * @return string
     */
    public function getOperationPlaceType($operation = null): string
    {
        if (in_array($operation, [GeoZoneValues::OPERATION_ALL_CITY, GeoZoneValues::OPERATION_ALL_NOT_CITY])) {
            return '(cities)';
        }

        if (in_array($operation, [GeoZoneValues::OPERATION_ALL_STREET, GeoZoneValues::OPERATION_ALL_NOT_STREET])) {
            return 'geocode';
        }

        return '(regions)';
    }

    /**
     * @param GeoZoneFilterInformation|null $geo_zone_information
     * @return boolean
     */
    public function compareGeoZone(?GeoZoneFilterInformation $geo_zone_information = null): bool
    {
        if (!$this->exists || $this->values->isEmpty() || !$geo_zone_information) {
            return false;
        }

        $_lambdaCityFilter = (fn (GeoZoneValues $value): bool => $value->country_iso2 == $geo_zone_information->getCountry() && $value->admin_zone_1_iso == $geo_zone_information->getLevel1() && ($value->locality ? $value->locality == $geo_zone_information->getLocality() : true) && (!in_array($value->country_iso2, ['US']) ? ($value->city_id == $geo_zone_information->getCityId()) : ($value->city_ascii_name && $value->city_ascii_name == $geo_zone_information->getCityAscii())));

        $_lambdaStreetFilter = (fn (GeoZoneValues $value): bool => $value->country_iso2 == $geo_zone_information->getCountry() &&
            $value->admin_zone_1_iso == $geo_zone_information->getLevel1() &&
            //($value->locality ? $value->locality == $geo_zone_information->getLocality() : true) &&
            ($value->city_id && $geo_zone_information->getCityId() ? $value->city_id == $geo_zone_information->getCityId() : true) &&
            $value->neighborhood && $value->neighborhood == $geo_zone_information->getNeighborhood());

        $_lambdaPolygonFilter = function (GeoZoneValues $value) use ($geo_zone_information) {
            if (!$value->polygon_id || !$value->polygon || empty($geo_zone_information) || empty($geo_zone_information->getLat()) || empty($geo_zone_information->getLng())) {
                return false;
            }

            $polygon = $value->polygon->area->toWKT();
            $point = (new Point($geo_zone_information->getLat(), $geo_zone_information->getLng()))->toWKT();

            return \Illuminate\Support\Facades\DB::selectOne('SELECT ST_Contains(ST_GeomFromText(?), ST_GeomFromText(?)) as contains', [$polygon, $point])->contains ?? false;
        };

        $_lambdaDistanceFilter = function (GeoZoneValues $value) use ($geo_zone_information): bool {
            if (
                !$value->distance_id ||
                !$value->distance ||
                empty($geo_zone_information) ||
                empty($geo_zone_information->getLat()) ||
                empty($geo_zone_information->getLng()) ||
                in_array(site('database_id'), [3,7,10])
            ) {
                return false;
            }

            $homePoint = $value->distance->point->toWKT();
            $point = (new Point($geo_zone_information->getLat(), $geo_zone_information->getLng()))->toWKT();

            $check = \Illuminate\Support\Facades\DB::selectOne('SELECT ST_Distance_Sphere(ST_GeomFromText(?), ST_GeomFromText(?)) as distance', [$homePoint, $point]);
            if (!$check) {
                return false;
            }

            return $check->distance <= $value->distance->distance_in_meters;
        };

        $_lambdaPostCodeFilter = function (GeoZoneValues $value) use ($geo_zone_information): bool {
            if (
                $value->post_codes->isEmpty() ||
                empty($geo_zone_information) ||
                empty($geo_zone_information->getPostCode())
            ) {
                return false;
            }

            return $value->post_codes->first(function (GeoZoneValuePostCodes $post_code) use ($geo_zone_information) {
                if ($post_code->type == 'equal') {
                    return $post_code->post_code == $geo_zone_information->getPostCode();
                } elseif ($post_code->type == 'match') {
                    return Str::is(str_replace('%', '*', $post_code->post_code), $geo_zone_information->getPostCode());
                } elseif ($post_code->type == 'range') {
                    return $post_code->post_code <= $geo_zone_information->getPostCode() && $post_code->post_code_to >= $geo_zone_information->getPostCode();
                }

                return false;
            }) instanceof GeoZoneValuePostCodes;
        };

        $grouped = $this->values->groupBy('operation');
        /** @var Collection|GeoZoneValues[] $values */
        foreach ($grouped as $operation => $values) {
            if ($operation == GeoZoneValues::OPERATION_ALL_IN_COUNTRY) {
                if ($values->firstWhere('country_iso2', $geo_zone_information->getCountry())) {
                    return true;
                }
            } elseif ($operation == GeoZoneValues::OPERATION_ONLY_REGION) {
                $values = $values->where('country_iso2', $geo_zone_information->getCountry());
                if ($values->isEmpty()) {
                    return false;
                }

                if ($values->firstWhere('admin_zone_1_iso', $geo_zone_information->getLevel1())) {
                    return true;
                }
            } elseif ($operation == GeoZoneValues::OPERATION_ALL_IN_COUNTRY_WITHOUT_REGION) {
                $values = $values->where('country_iso2', $geo_zone_information->getCountry());
                if ($values->isEmpty()) {
                    return false;
                }

                if (!$values->firstWhere('admin_zone_1_iso', $geo_zone_information->getLevel1())) {
                    return true;
                }
            } elseif ($operation == GeoZoneValues::OPERATION_ALL_NOT_COUNTRY) {
                if (!$values->firstWhere('country_iso2', $geo_zone_information->getCountry())) {
                    return true;
                }
            } elseif ($operation == GeoZoneValues::OPERATION_ALL_CITY) {
                $values = $values->where('country_iso2', $geo_zone_information->getCountry());
                if ($values->isEmpty()) {
                    return false;
                }

                if ($values->first($_lambdaCityFilter)) {
                    return true;
                }
            } elseif ($operation == GeoZoneValues::OPERATION_ALL_NOT_CITY) {
                if (!$values->first($_lambdaCityFilter)) {
                    return true;
                }
            } elseif ($operation == GeoZoneValues::OPERATION_ALL_STREET) {
                $values = $values->where('country_iso2', $geo_zone_information->getCountry());
                if ($values->isEmpty()) {
                    return false;
                }

                if ($values->first($_lambdaStreetFilter)) {
                    return true;
                }
            } elseif ($operation == GeoZoneValues::OPERATION_ALL_NOT_STREET) {
                if (!$values->first($_lambdaStreetFilter)) {
                    return true;
                }
            } elseif (in_array($operation, [GeoZoneValues::OPERATION_POLYGON])) {
                if ($values->first($_lambdaPolygonFilter)) {
                    return true;
                }
            } elseif (in_array($operation, [GeoZoneValues::OPERATION_DISTANCE])) {
                if ($values->first($_lambdaDistanceFilter)) {
                    return true;
                }
            } elseif (in_array($operation, [GeoZoneValues::OPERATION_POST_CODE])) {
                if ($values->first($_lambdaPostCodeFilter)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * This method use for init addresses types show
     * @param GeoZoneFilterInformation|null $geo_zone_information
     * @return boolean
     */
    public function compareGeoZoneToCity(?GeoZoneFilterInformation $geo_zone_information = null): bool
    {
        if (!$this->exists || $this->values->isEmpty() || !$geo_zone_information) {
            return false;
        }

        $_lambdaCityFilter = (fn (GeoZoneValues $value): bool => $value->country_iso2 == $geo_zone_information->getCountry() && $value->admin_zone_1_iso == $geo_zone_information->getLevel1());

        $_lambdaPolygonFilter = function (GeoZoneValues $value) use ($geo_zone_information) {
            if (!$value->polygon_id || !$value->polygon || empty($geo_zone_information) || empty($geo_zone_information->getLat()) || empty($geo_zone_information->getLng())) {
                return false;
            }

            $polygon = $value->polygon->area->toWKT();
            $point = (new Point($geo_zone_information->getLat(), $geo_zone_information->getLng()))->toWKT();

            return \Illuminate\Support\Facades\DB::selectOne('SELECT ST_Contains(ST_GeomFromText(?), ST_GeomFromText(?)) as contains', [$polygon, $point])->contains ?? false;
        };

        $_lambdaDistanceFilter = function (GeoZoneValues $value) use ($geo_zone_information): bool {
            if (
                !$value->distance_id ||
                !$value->distance ||
                empty($geo_zone_information) ||
                empty($geo_zone_information->getLat()) ||
                empty($geo_zone_information->getLng()) ||
                in_array(site('database_id'), [3,7,10])
            ) {
                return false;
            }

            $homePoint = $value->distance->point->toWKT();
            $point = (new Point($geo_zone_information->getLat(), $geo_zone_information->getLng()))->toWKT();

            $check = \Illuminate\Support\Facades\DB::selectOne('SELECT ST_Distance_Sphere(ST_GeomFromText(?), ST_GeomFromText(?)) as distance', [$homePoint, $point]);
            if (!$check) {
                return false;
            }

            return $check->distance <= $value->distance->distance_in_meters;
        };

        $_lambdaPostCodeFilter = function (GeoZoneValues $value) use ($geo_zone_information): bool {
            if (
                $value->post_codes->isEmpty() ||
                empty($geo_zone_information) ||
                empty($geo_zone_information->getPostCode())
            ) {
                return false;
            }

            return $value->post_codes->first(function (GeoZoneValuePostCodes $post_code) use ($geo_zone_information) {
                if ($post_code->type == 'equal') {
                    return $post_code->post_code == $geo_zone_information->getPostCode();
                } elseif ($post_code->type == 'match') {
                    return Str::is($post_code->post_code, $geo_zone_information->getPostCode());
                } elseif ($post_code->type == 'range') {
                    return $post_code->post_code <= $geo_zone_information->getPostCode() && $post_code->post_code_to >= $geo_zone_information->getPostCode();
                }

                return false;
            }) instanceof GeoZoneValuePostCodes;
        };

        $grouped = $this->values->groupBy('operation');
        /** @var Collection|GeoZoneValues[] $values */
        foreach ($grouped as $operation => $values) {
            if ($operation == GeoZoneValues::OPERATION_ALL_IN_COUNTRY) {
                if ($values->firstWhere('country_iso2', $geo_zone_information->getCountry())) {
                    return true;
                }
            } elseif ($operation == GeoZoneValues::OPERATION_ALL_NOT_COUNTRY) {
                if (!$values->firstWhere('country_iso2', $geo_zone_information->getCountry())) {
                    return true;
                }
            } elseif (in_array($operation, [GeoZoneValues::OPERATION_ONLY_REGION, GeoZoneValues::OPERATION_ALL_CITY, GeoZoneValues::OPERATION_ALL_STREET])) {
                $values = $values->where('country_iso2', $geo_zone_information->getCountry());
                if ($values->isEmpty()) {
                    return false;
                }

                if ($values->first($_lambdaCityFilter)) {
                    return true;
                }
            } elseif (in_array($operation, [GeoZoneValues::OPERATION_ALL_IN_COUNTRY_WITHOUT_REGION, GeoZoneValues::OPERATION_ALL_NOT_CITY, GeoZoneValues::OPERATION_ALL_NOT_STREET])) {
                if (!$values->first($_lambdaCityFilter)) {
                    return true;
                }
            } elseif (in_array($operation, [GeoZoneValues::OPERATION_POLYGON])) {
                if ($values->first($_lambdaPolygonFilter)) {
                    return true;
                }
            } elseif (in_array($operation, [GeoZoneValues::OPERATION_DISTANCE])) {
                if ($values->first($_lambdaDistanceFilter)) {
                    return true;
                }
            } elseif (in_array($operation, [GeoZoneValues::OPERATION_POST_CODE])) {
                if ($values->first($_lambdaPostCodeFilter)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @param mixed $q
     * @return mixed
     */
    public function scopeCountries($q): void
    {
        /** @var GeoZones $q */
        $q->where(function ($q): void {
            $allValues = GeoZoneValues::selectRaw('COUNT(*)')
                ->whereColumn('geo_zone_values.zone_id', 'geo_zones.id');

            $countryValues = (clone $allValues)
                ->whereRaw('`operation` = ' . GeoZoneValues::OPERATION_ALL_IN_COUNTRY);

            /** @var GeoZones $q */
            $q->whereHas('values')
                ->whereColumn(\Illuminate\Support\Facades\DB::raw('(' . $allValues->toSql() . ')'), \Illuminate\Support\Facades\DB::raw('(' . $countryValues->toSql() . ')'));
        });
    }

    /**
     * @param mixed $q
     * @return mixed
     */
    public function scopeNoCountries($q): void
    {
        /** @var GeoZones $q */
        $q->where(function ($q): void {
            $allValues = GeoZoneValues::selectRaw('COUNT(*)')
                ->whereColumn('geo_zone_values.zone_id', 'geo_zones.id');

            $countryValues = (clone $allValues)
                ->whereRaw('`operation` != ' . GeoZoneValues::OPERATION_ALL_IN_COUNTRY);

            /** @var GeoZones $q */
            $q->whereHas('values')
                ->whereColumn(\Illuminate\Support\Facades\DB::raw('(' . $allValues->toSql() . ')'), \Illuminate\Support\Facades\DB::raw('(' . $countryValues->toSql() . ')'));
        });
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getValuesCountAttribute($value)
    {
        if (!empty($this->data['values_count'])) {
            return $this->data['values_count'];
        }
        $this->data['values_count'] = 0;
        if ($this->relationLoaded('totalValuesCount')) {
            $value = $this->getRelationValue('totalValuesCount');
            $this->data['values_count'] = $value ? $value->total : 0;
        }
        return $this->data['values_count'];
    }
    /**
     * @param mixed $values_count
     * @return mixed
     */
    public function setValuesCountAttribute($values_count)
    {
        $this->data['values_count'] = $values_count;
    }
}
