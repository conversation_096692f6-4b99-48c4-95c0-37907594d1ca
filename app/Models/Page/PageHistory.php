<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 23.6.2017 г.
 * Time: 18:21
 */

namespace App\Models\Page;

use App\Models\Base\AbstractPagesHistory;

/**
 * @property Page $page
 */
class PageHistory extends AbstractPagesHistory
{
    /** set url handle has field for edit */
    protected $has_edit_handle_field = true;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function page()
    {
        return $this->belongsTo(Page::class);
    }

    /**
     * The attributes that should be cast to native types.
     *
     * @return array
     */
    protected function casts(): array
    {
        return [
            'content' => 'object'
        ];
    }
}
