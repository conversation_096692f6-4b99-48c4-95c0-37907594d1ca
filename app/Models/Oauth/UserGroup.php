<?php

declare(strict_types=1);

namespace App\Models\Oauth;

use Illuminate\Database\Eloquent\Model as Eloquent;
use Illuminate\Support\Collection;

/**
 * Class UserGroup
 * @package App\Models\Oauth
 * @property string $name
 * @property User[]|Collection $users
 */
class UserGroup extends Eloquent
{
    /**
     * @var string
     */
    protected $connection = 'oauth';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'cc_oauth.user_groups';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function users()
    {
        return $this->belongsToMany(
            User::class,
            'user_has_groups',
            'group_id',
            'user_id',
        );
    }

}
