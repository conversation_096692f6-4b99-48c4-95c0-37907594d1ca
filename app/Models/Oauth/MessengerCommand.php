<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 1.1.2017 г.
 * Time: 22:33 ч.
 */

namespace App\Models\Oauth;

use App\Traits\DbTimezone;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model as Eloquent;

/**
 * Class MessengerCommand
 * @method static Builder|MessengerCommand  where($column, $operator = null, $value = null, $boolean = 'and')
 */
class MessengerCommand extends Eloquent
{
    use DbTimezone;

    public const NAME_REQUEST = 'name-request';

    public const EMAIL_REQUEST = 'email-request';

    public const PHONE_REQUEST = 'phone-request';

    public const ADDRESS_REQUEST = 'address-request';

    /**
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * @var bool
     */
    public $timestamps = false;

    /**
     * @var string
     */
    protected $connection = 'oauth';
}
