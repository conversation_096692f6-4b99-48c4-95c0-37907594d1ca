<?php

declare(strict_types=1);

namespace App\Models\Oauth;

use App\Models\Customer\Customer;
use App\Models\Router\Site;
use App\Models\Setting\Admin;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model as Eloquent;

/**
 * Class User
 * @property string $name
 * @property integer $admin_id
 * @property integer $customer_id
 * @property SocialAccount $socialAccount
 * @property Customer $customer
 * @method static Builder|ConnectedSite  where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder|ConnectedSite whereProvider($value)
 * @method static Builder|ConnectedSite whereProviderUserId($value)
 * @method static Builder|ConnectedSite firstOrCreate(array $attributes, array $values = [])
 * @package App\Models\Oauth
 */
class ConnectedSite extends Eloquent
{
    /**
     * @var string
     */
    protected $connection = 'oauth';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function socialAccount()
    {
        return $this->belongsTo(SocialAccount::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * @param $adminId
     * @param $provider
     */
    public function connectAdmin($adminId, $provider): void
    {
        static::where('site_id', site('site_id'))
            ->where('admin_id', $adminId)
            ->whereHas('socialAccount', function ($query) use ($provider): void {
                /** @var SocialAccount $query */
                $query->whereProvider($provider);
            })
            ->update(['admin_id' => null]);

        // $this is not dirty, use static call!
        static::where('id', $this->getKey())->update(['admin_id' => $adminId]);

        static::where('site_id', site('site_id'))
            ->whereNull('customer_id')
            ->whereNull('admin_id')
            ->delete();

        SocialAccount::clearSACache();
    }

    /**
     * @param $customerId
     * @return $this
     */
    public function connectCustomer($customerId): static
    {
        $this->update(['customer_id' => $customerId]);
        return $this;
    }

    /**
     * @return $this
     */
    public function disconnectCustomer(): static
    {
        $this->update(['customer_id' => null]);

        static::where('site_id', site('site_id'))
            ->whereNull('customer_id')
            ->whereNull('admin_id')
            ->delete();

        return $this;
    }

    /**
     * @return $this
     */
    public function disconnectAdmin(): static
    {
        $this->update(['admin_id' => null]);

        static::where('site_id', site('site_id'))
            ->whereNull('customer_id')
            ->whereNull('admin_id')
            ->delete();

        return $this;
    }
}
