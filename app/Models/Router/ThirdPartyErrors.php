<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 1.6.2016 г.
 * Time: 10:07 ч.
 */

namespace App\Models\Router;

use MongoEloquent;
use Throwable;

/**
 * @property int $site_id
 * @property string $hostname
 * @property string $platform
 * @property null|array $data
 * @property null|string $group
 * @property null|string $error
 * @property null|string $key
 * @property null|string $file
 * @property null|string $class
 * @property null|int $line
 */
class ThirdPartyErrors extends MongoEloquent
{
    protected $connection = 'mongodb-logging-google-cloud';

    protected $table = 'third_party_errors';

    protected $guarded = ['_id'];

    #[\Override]
    protected static function boot()
    {
        parent::boot();

        static::creating(function (ThirdPartyErrors $log): void {
            if (empty($log->site_id)) {
                $log->site_id = site('site_id');
            }

            $log->hostname = gethostname();
            $log->platform = platform();
        });
    }

    /**
     * @param $attributes
     * @param array $values
     * @param int $retry
     * @param float $interval
     * @return $this|null
     */
    public static function updateOrCreateRetry($attributes, array $values = [], int $retry = 3, float $interval = 500): ?ThirdPartyErrors
    {
        try {
            return retry($retry, fn () => static::updateOrCreate($attributes, $values), $interval);
        } catch (Throwable) {
            return null;
        }
    }

    protected function casts(): array
    {
        return [
            'site_id' => 'int',
            'line' => 'int',
            'data' => 'json',
        ];
    }

}
