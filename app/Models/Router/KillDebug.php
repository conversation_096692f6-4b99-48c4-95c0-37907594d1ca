<?php

declare(strict_types=1);

namespace App\Models\Router;

use MongoEloquent;

class KillDebug extends MongoEloquent
{
    protected $connection = 'mongodb-logging-local';

    protected $table = 'kill-debug';

    protected $guarded = ['_id'];

    /**
     * @param mixed $message
     * @return mixed
     */
    public static function info($message): void
    {
        static::create([
            'message' => $message,
            'host' => gethostname(),
        ]);
    }
}
