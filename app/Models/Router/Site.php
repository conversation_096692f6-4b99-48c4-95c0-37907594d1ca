<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 1.6.2016 г.
 * Time: 10:07 ч.
 */

namespace App\Models\Router;

use App;
use App\Common\Theme;
use App\Exceptions\Site\HttpNotFoundSite;
use App\GlobalServiceProviders\DatabaseSetMaxStatementTimeServiceProvider;
use App\Helper\Cache\CcCache;
use App\Helper\Cache\RouterCache;
use App\Jobs\MigrateSite;
use App\Locale\Country;
use App\Models\Apps\GoogleProductCategory;
use App\Models\Gate\SiteSubscription;
use App\Models\Gate\Template;
use App\Models\Gate\User;
use App\Models\Gate\UserSiteInvoicing;
use App\Models\Gate\UsersSites;
use App\Models\Router\Database as SiteDatabase;
use App\Traits\Cacheable;
use App\Traits\Model;
use Cache;
use Carbon\Carbon;
use Eloquent;
use Illuminate\Database\DatabaseManager;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\CcSegments\Models\SiteEventLog;
use Modules\CcSegments\Models\SiteSegment;
use App\Models\Gate\Reseller;
use DB;

$aliases = [
    '\Eloquent' => \Illuminate\Database\Eloquent\Model::class,
];

foreach ($aliases as $alias => $className) {
    if (!class_exists($alias)) {
        class_alias($className, $alias);
    }
}

/**
 * Class Site
 *
 * @property int $site_id
 * @property string $ssl
 * @property string $host
 * @property int $id
 * @property int|array $industry
 * @property int|null $main_industry
 * @property string $industry_list
 * @property array $industry_array
 * @property int $industry_migrated
 * @property int $primary_host_id
 * @property int $main_host_id
 * @property null|User $user
 * @property SiteDatabase $database
 * @property integer $database_id
 * @property string $platform
 * @property string $template
 * @property string $progress
 * @property int $disable_html_cache
 * @property UsersSites $gate
 * @property Host $primaryHost
 * @property Host $mainHost
 * @property SiteStatistic[]|Collection $statistic
 * @property Collection $stat
 * @property integer $maintenance
 * @property null|string $maintenance_reason
 * @property Collection|Host[] $hosts
 * @property Collection $statistics
 * @property string $plan
 * @property string $status
 * @property string $language
 * @property string $language_cp
 * @property string $currency
 * @property string $timezone
 * @property int $manual_maintenance
 * @property int $sand_box
 * @property int $force_gc
 * @property null|string $period
 * @property Carbon $next_billing_date
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property bool $plan_expired
 * @property float $site_plan_remaining_days
 * @property int|null $notify_count
 * @property string $expire_reason
 * @property string $expire_condition
 * @property string|null $operation_country
 * @property SiteSegment[]|Collection $segments
 * @property int $suspended
 * @property Template $theme
 * @package App\Models\Router
 *
 * @method static Site findOrFail($id)
 * @method static Site first()
 * @method static Builder where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder orWhere($column, $operator = null, $value = null)
 * @method static Site find($id)
 * @method static Builder join($table, $one, $operator = null, $two = null, $type = 'inner', $where = false)
 */
class Site extends Eloquent
{
    use Cacheable;
    use Model;

    /**
     * @var string
     */
    protected $connection = 'router';

    /**
     * @var string
     */
    protected $table = 'eu_cc_router.sites';

    /**
     * @var string
     */
    protected $primaryKey = 'site_id';

    /**
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $guarded = [
        'site_id'
    ];

    /**
     * Check if site is booted
     *
     * @var bool
     */
    public static $_is_booted = false;

    public static $statusMap = [
        'paid' => SiteSubscription::ACTIVE,
        'billing' => SiteSubscription::PAST_DUE,
        'not_paid' => SiteSubscription::EXPIRED,
        'cancelled' => SiteSubscription::CANCELED,
    ];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    #[\Override]
    protected static function boot()
    {
        parent::boot();

        //        static::saving(function (self $site) {
        //            if (!app()->runningInConsole()) {
        //                $utmData = session('utmData', []);
        //                if (!empty($utmData['referral'])) { // fix storefront conflict with 'utmData.referer'
        //                    if ($site->isDirty(['plan', 'next_billing_date'])) {
        //                        $site->fill($utmData);
        //                        session()->forget('utmData');
        //                    }
        //                }
        //            }
        //        });

        static::saving(function (self $item): void {
            if ($item->isDirty('primary_host_id')) {
                $primaryHost = $item->primaryHost;
                $url = ($primaryHost->ssl === 'yes' ? 'https://' : 'http://') . $primaryHost->host;
                $item->gate->update(['url' => $url]);
            }

            if ($item->isDirty('status') &&
                $item->getOriginal('status') == array_search(SiteSubscription::EXPIRED, self::$statusMap, true)
            ) {
                dispatch(new MigrateSite($item->getKey()));
            }

            try {
                $after = $item->getDirty();
                $before = Arr::only($item->getOriginal(), array_keys($after));
                if ($before != $after) {
                    SiteEventLog::add(
                        $item->getKey(),
                        SiteEventLog::TYPE_SITE_UPDATED,
                        $item,
                        [
                            'before' => $before,
                            'after' => $after,
                        ]
                    );
                }
            } catch (\Throwable $throwable) {
                Exceptions::createFromThrowable($throwable);
            }
        });

        static::saved(function (self $site): void {
            RouterCache::invalidateDomain($site->site_id);
            RouterCache::invalidateSite($site->site_id);
            RouterCache::invalidateSites();
        });

        static::deleting(function (self $site): void {
            RouterCache::invalidateDomain($site->site_id);
            RouterCache::invalidateSite($site->site_id);
        });

        static::deleted(function (self $item): void {
            RouterCache::invalidateSites();
            SiteEventLog::add(
                $item->getKey(),
                SiteEventLog::TYPE_SITE_DELETED,
                $item
            );
        });
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function database()
    {
        return $this->belongsTo(SiteDatabase::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function theme()
    {
        return $this->belongsTo(Template::class, 'template', 'mapping');
    }

    public function getDatabaseAttribute()
    {
        if ($this->relationLoaded('database')) {
            return $this->getRelation('database');
        }

        $this->setRelation('database', Cache::tags([config('cache.router.database-key')])->remember($this->database_id, config('cache.router.database-ttl'), fn () => SiteDatabase::find($this->database_id)));

        return $this->getRelation('database');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function certificates()
    {
        return $this->hasMany(Certificate::class, 'site_id', 'site_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|SiteStatistic
     */
    public function statistic()
    {
        return $this->hasMany(SiteStatistic::class, 'site_id', 'site_id');
    }

    public function formatStatistics()
    {
        $map = [
            'statistic.count.product' => [
                'group' => 'products',
                'key' => 'total',
            ],
            'statistic.count.product_active' => [
                'group' => 'products',
                'key' => 'active',
            ],
            'statistic.count.order' => [
                'group' => 'orders',
                'key' => 'total',
            ],
            'statistic.count.order_completed' => [
                'group' => 'orders',
                'key' => 'completed',
            ],
            'statistic.count.order_cancelled' => [
                'group' => 'orders',
                'key' => 'cancelled',
            ],
            'statistic.count.order_pending' => [
                'group' => 'orders',
                'key' => 'pending',
            ],
            'statistic.count.order_abandoned' => [
                'group' => 'orders',
                'key' => 'abandoned',
            ],
            'statistic.count.vendor' => [
                'group' => 'vendors',
                'key' => 'total',
            ],
            'statistic.count.category' => [
                'group' => 'categories',
                'key' => 'total',
            ],
            'statistic.count.category_property' => [
                'group' => 'categories',
                'key' => 'properties',
            ],
            'statistic.count.category_property_active' => [
                'group' => 'categories',
                'key' => 'properties_active',
            ],
            'statistic.count.blog' => [
                'group' => 'blog',
                'key' => 'categories',
            ],
            'statistic.count.article' => [
                'group' => 'blog',
                'key' => 'total_articles',
            ],
            'statistic.count.article_active' => [
                'group' => 'blog',
                'key' => 'active_articles',
            ],
            'statistic.count.customer' => [
                'group' => 'customers',
                'key' => 'total',
            ],
            'statistic.count.discount' => [
                'group' => 'discounts',
                'key' => 'total',
            ],
            'statistic.count.discount_active' => [
                'group' => 'discounts',
                'key' => 'active',
            ],
            'statistic.count.page' => [
                'group' => 'pages',
                'key' => 'total',
            ],
            'statistic.count.page_active' => [
                'group' => 'pages',
                'key' => 'active',
            ],
            'statistic.count.redirect' => [
                'group' => 'redirects',
                'key' => 'total',
            ],
        ];

        $statistics = [];
        foreach ($this->statistic as $item) {
            if (isset($map[$item->parameter])) {
                $statistics[$map[$item->parameter]['group']][$map[$item->parameter]['key']] = $item->value;
            }
        }

        $collection = [];
        foreach ($statistics as $group => $values) {
            $item = [
                'group' => $group,
                'values' => []
            ];
            foreach ($values as $name => $value) {
                $item['values'][] = [
                    'name' => $name,
                    'value' => $value,
                ];
            }

            $collection[] = $item;
        }

        return collect($collection);
    }

    public function getGateSite()
    {
        return $this->gate;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function gate()
    {
        return $this->belongsTo(UsersSites::class, 'site_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function segments()
    {
        return $this->belongsToMany(
            SiteSegment::class,
            'cc_gate.site_to_segments',
            'site_id',
            'segment_id'
        );
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function hosts()
    {
        return $this->hasMany(Host::class, 'site_id', 'site_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function mainHost()
    {
        return $this->belongsTo(Host::class, 'main_host_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function primaryHost()
    {
        return $this->belongsTo(Host::class, 'primary_host_id', 'id');
    }

    /* ============================ moved form Host Model ============================ */

    /**
     * @param string $use_host_type
     *
     * @return string
     * @throws \App\Exceptions\Site\HttpNotFoundSite
     */
    public function getSiteUrl($use_host_type = 'current'): string
    {
        if (!$this->exists) {
            throw new HttpNotFoundSite();
        }

        if (app()->runningInConsole()) {
            $use_host_type = 'primary';
        }

        $port = null;
        if (!in_array(request()->getPort(), [80, 443])) {
            $port = request()->getPort();
        }

        if ($use_host_type == 'primary') {
            $primary_host = $this->getPrimaryHost();
            if (!$primary_host) {
                return $this->getScheme() . $this->host . ($port ? ':' . $port : '');
            } else {
                return $this->getScheme($primary_host) . $primary_host->host . ($port ? ':' . $port : '');
            }
        } elseif ($use_host_type == 'main') {
            $main_host = $this->getMainHost();
            return $this->getScheme($main_host) . $main_host->host . ($port ? ':' . $port : '');
        } else {
            return $this->getScheme() . $this->host . ($port ? ':' . $port : '');
        }
    }

    /**
     * @return Host
     * @throws HttpNotFoundSite
     * @todo Load site primary and main hosts only one time on app boot
     *
     */
    public function getPrimaryHost()
    {
        if (!$this->exists) {
            throw new HttpNotFoundSite();
        }

        if ($this->relationLoaded('hosts')) {
            return $this->hosts->where('id', $this->primary_host_id)->first();
        }

        if ($host = RouterCache::getHost($this->primary_host_id)) {
            return $host;
        }

        return RouterCache::setHost($this->primaryHost);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function invoicing()
    {
        return $this->hasOne(UserSiteInvoicing::class, 'site_id', 'site_id')->orderBy('id', 'desc');
    }

    /**
     * @return Host|null
     * @throws HttpNotFoundSite
     * @todo Load site primary and main hosts only one time on app boot
     *
     */
    public function getMainHost()
    {
        if (!$this->exists) {
            throw new HttpNotFoundSite();
        }

        if ($this->relationLoaded('hosts')) {
            return $this->hosts->where('id', $this->main_host_id)->first();
        }

        return $this->mainHost;
    }

    /**
     * @param null|Site|Host $host
     *
     * @return bool
     */
    public function hasSsl($host = null)
    {
        if (request()->is('.well-known/acme-challenge/*')) {
            return false;
        }

        if (!$host || !($host instanceof Host)) {
            $host = $this;
        }

        if ($host instanceof Site) {
            if (!$this->relationLoaded('hosts') || !($host = $this->hosts->where('host', $this->host)->first())) {
                $host = $this->getPrimaryHost();
            }
        }

        return $host->hasSsl();
    }

    /**
     * @param Host|Site|null $host
     *
     * @return string
     */
    public function getScheme(Host|Site|null $host = null): string
    {
        return $this->hasSsl($host) ? 'https://' : 'http://';
    }

    /**
     * Gets host information by host name.
     *
     * @param string $host
     *
     * @return bool|\Illuminate\Database\Eloquent\Model
     */
    public static function getByHostName($host)
    {
        if (ip2long($host) !== false) {
            return;
        }

        return Cache::tags([config('cache.router.domain-key')])
            ->remember(Str::lower($host), config('cache.router.domain-ttl'), function () use ($host) {
                $query = static::with(['hosts', 'user'])->where('host', 'like', $host)
                    ->join('hosts', function (JoinClause $joinClause): void {
                        $joinClause->on('hosts.site_id', '=', 'sites.site_id');
                    })->limit(1);

                if (stripos($host, 'www.') === 0) {
                    $query->orWhere('host', 'like', preg_replace('/^www\./i', '', $host));
                } else {
                    $query->orWhere('host', 'like', 'www.' . $host);
                }

                $model = $query->first();
                if (!$model) {
                    return;
                }

                foreach ($model->hosts as $item) {
                    RouterCache::setHost($item);
                }

                return $model;
            });
    }

    /**
     * @throws \App\Exceptions\Site\HttpNotFoundSite
     */
    public function connect(): void
    {
        if (!$this->exists) {
            throw new HttpNotFoundSite();
        }

        if (app()->runningInConsole()) {
            $mysqlUser = 'cli_' . $this->site_id;
        } else {
            $mysqlUser = 'web_' . $this->site_id;
        }

        // set default connection
        $database = $this->getSiteConnectionConfig($mysqlUser);
        $app = app();
        $app->make('config')->set('database.connections.default', $database);
        $app->offsetSet('db.connection', $connection = $app->make('db')->connection());

        if(app()->runningInConsole()) {
            $connection->statement(
                sprintf(
                    'SET SESSION max_statement_time=%d',
                    DatabaseSetMaxStatementTimeServiceProvider::MAX_STATEMENT_TIME_CLI
                )
            );
        }
    }

    /**
     * @throws \App\Exceptions\Site\HttpNotFoundSite
     */
    public function disconnect(): void
    {
        if (!$this->exists) {
            throw new HttpNotFoundSite();
        }

        $app = App::getInstance();

        if ($app->bound('db.connection')) {
            $app->offsetUnset('db.connection');
        }

        $connections = \Illuminate\Support\Facades\DB::getConnections();
        //        foreach (['default', 'payments'] as $connection) {
        foreach (['default'] as $connection) {
            if (in_array('default', array_keys($connections))) {
                /** @var $db DatabaseManager */
                $db = $app->make('db');
                $db->purge($connection);
                $db->disconnect();
            }
        }
    }

    /**
     * @param bool $install
     *
     * @return Site
     * @throws \App\Exceptions\Error
     * @throws \App\Exceptions\Fault
     */
    public function bootDB($install = false): static
    {
        if (!$this->exists) {
            throw new HttpNotFoundSite();
        }

        if (!$install) {
            $this->bootDbConnection();
        }

        $app = App::getInstance();

        if ($app->offsetExists('site')) {
            $app->offsetUnset('site');
            $app->offsetUnset('site_id');
        }

        $app->offsetSet('site', $this);
        $app->offsetSet('site_id', $this->site_id);

        static::$_is_booted = true;

        $translator = $app->make('translator');
        if (!$install) {
            //@todo reset language loaded for queue
            $translator->setLoaded([]);
        }

        if (app_namespace() == 'sitecp') {
            $language = $this->language_cp ?: ($this->language ?: config('app.locale'));
        } else {
            $language = $this->language ?: config('app.locale');
        }

        $app->setLocale($language);
        $translator->setLocale($language);
        // fix astrotomic/laravel-translatable
        $app->make('config')->set('translatable.locale', $language);

        if ($this->theme->engine == 'smarty' && in_array(app_namespace(), ['site', 'sitecp'])) {
            if (($tanslation = Theme::bool('translations')) !== false) {
                $translator->addNamespace(Theme::get('name'), dirname(site('dir')) . '/' . $tanslation);
            }
        }

        return $this;
    }

    /**
     *
     */
    public function bootDbConnection(): void
    {
        // disconnect previous if called
        $this->disconnect();
        //connect
        $this->connect();
    }

    /**
     * @return bool|null
     */
    public function getCacheEnabled(): bool
    {
        if (!$this->exists) {
            return false;
        }

        // Disable MySQL Redis cache:
        return false;
    }

    /**
     * @return bool
     */
    public function getHtmlCacheEnabled(): int|bool
    {
        if (!$this->exists || $this->disable_html_cache) {
            return false;
        }

        if (request()->cookie('html-cache-disable')) {
            return false;
        }

        return inDevelopment() ? (int)request()->cookie('html-cache-enabled') : true;
    }

    /**
     * Retrieves the contents of the store's robots txt file.
     *
     * @return array the store robots.txt contents
     */
    public function getRobotsTxt(): array
    {
        if (!$this->exists) {
            throw new HttpNotFoundSite();
        }

        $max_timestamp = max(app('last_build'), setting('stylesheet_version'));
        if (inDevelopment() || $this->plan_expired || $this->plan == 'trial') {
            return [
                file_get_contents(base_path('public/robots_trial.txt')),
                $max_timestamp
            ];
        }

        if ($robots = setting('robots.txt')) {
            return [
                $robots,
                setting('update_robots', $max_timestamp)
            ];
        } else {
            return [
                file_get_contents(base_path('public/robots_site.txt')),
                $max_timestamp
            ];
        }
    }

    /**
     * @return array
     */
    public function getRouterConnectionConfig()
    {
        return ['database' => 'cc_site_' . $this->site_id] + $this->database->getRouterConnectionConfig();
    }

    /**
     * @param $username
     * @return array
     */
    public function getSiteConnectionConfig($username)
    {
        return ['database' => 'cc_site_' . $this->site_id, 'username' => $username] + $this->database->getDefaultConnectionConfig();
    }

    public function getOperationCountry(): ?object
    {
        $country = $this->operation_country;
        if (empty($country)) {
            $country = $this->user->country ?? null;
        }

        return $country && Country::has($country) ? (object)Country::get($country, $this->language_cp) : null;
    }

    /**
     * The attributes that should be cast to native types.
     *
     * @return array
     */
    protected function casts(): array
    {
        return [
            'industry' => 'json',
            'next_billing_date' => 'datetime'
        ];
    }
    public function getStatusAttribute()
    {
        if (!isset($this->attributes['status'])) {
            return null;
        }

        if(is_numeric($this->attributes['status'])) {
            return $this->attributes['status'];
        }

        return static::$statusMap[$this->attributes['status']];
    }

    public function setStatusAttribute($value)
    {
        if(!is_numeric($value)) {
            $this->attributes['status'] = $value;
        } else {
            $statusMap = array_flip(self::$statusMap);
            $this->attributes['status'] = $statusMap[$value];
        }
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPlatformAttribute($value)
    {
        if ($this->relationLoaded('database')) {
            return $this->getRelation('database')->platform;
        }
        $database = Cache::tags(config('cache.router.database-key'))->remember($this->database_id, config('cache.router.database-ttl'), fn () => SiteDatabase::find($this->database_id));
        $this->setRelation('database', $database);
        return $this->getRelation('database')->platform;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getIndustryAttribute($value)
    {
        if(is_array($this->attributes['industry'] ?? null)) {
            return $this->attributes['industry'];
        }

        if (!is_null($industry = $this->castAttribute('industry', $this->attributes['industry'] ?? null))) {
            return $industry;
        }
        return [];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getIndustryListAttribute($value): ?string
    {
        if ($this->industry) {
            return implode(
                ';',
                GoogleProductCategory::whereIn('id', $this->industry)
                    ->pluck('name')
                    ->toArray()
            );
        }
        return null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getMainIndustryNameAttribute($value)
    {
        if (!$this->main_industry) {
            return;
        }
        return App\Helper\ArrayCache::remember('main_industry_name', fn () => GoogleProductCategory::getBase()->firstWhere('id', $this->main_industry)->name ?? null);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getIndustryArrayAttribute($value)
    {
        if ($this->industry) {
            return GoogleProductCategory::whereIn('id', $this->industry)
                ->pluck('name')->all();
        }
        return [];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPeriodAttribute($value)
    {
        return $this->gate->period;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPlanExpiredAttribute($value): bool
    {
        return $this->status == SiteSubscription::EXPIRED;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getSitePlanRemainingDaysAttribute($value): ?float
    {
        if (!is_null($this->next_billing_date)) {
            return ceil(($this->next_billing_date->timestamp - Carbon::now('UTC')->timestamp) / 86400);
        }
        return null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStatisticsAttribute($value)
    {
        if (!$this->relationLoaded('statistic')) {
            return [];
        }
        return $this->formatStatistics();
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getUserIdAttribute($value)
    {
        return $this->gate->user_id;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStatAttribute($value)
    {
        return CcCache::remember('records.statistic', config('cache.ttl_1h'), fn () => $this->statistic)->pluck('value', 'parameter');
    }

    /**
     * @param $version
     * @return integer
     */
    public function engineCompare($version)
    {
        return version_compare($this->engine_version, $version);
    }

    /**
     * @param $version
     * @return bool
     */
    public function engineGt($version)
    {
        return $this->engineCompare($version) > 0;
    }

    /**
     * @param $version
     * @return bool
     */
    public function engineGe($version)
    {
        return $this->engineCompare($version) >= 0;
    }

    /**
     * @param $version
     * @return bool
     */
    public function engineLt($version)
    {
        return $this->engineCompare($version) < 0;
    }

    /**
     * @param $version
     * @return bool
     */
    public function engineLe($version)
    {
        return $this->engineCompare($version) <= 0;
    }

    /**
     * @param $version
     * @return bool
     */
    public function engineEq($version)
    {
        return $this->engineCompare($version) == 0;
    }

    /**
     * @param $version
     * @return bool
     */
    public function engineNe($version)
    {
        return $this->engineCompare($version) <> 0;
    }
}
