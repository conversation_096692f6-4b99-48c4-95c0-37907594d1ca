<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 1.6.2016 г.
 * Time: 10:07 ч.
 */

namespace App\Models\Router;

use MongoEloquent;

/**
 * @property string $id
 * @property integer $site_id
 * @property string $domain
 * @property string $ip
 * @property string $ua
 */
class BotDetectLog extends MongoEloquent
{
    protected $connection = 'mongodb-logging-local';

    protected $table = 'bot_detect_log';

    protected $guarded = ['_id'];

    public const CREATED_AT = self::UPDATED_AT;
}
