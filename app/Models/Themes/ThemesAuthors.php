<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 1.6.2016 г.
 * Time: 10:07 ч.
 */

namespace App\Models\Themes;

use App\Traits\Cacheable;
use Illuminate\Database\Eloquent\Model as Eloquent;

/**
 * Class Templates
 *
 * @property int id
 * @property string key
 * @property string name
 * @package App\Models\Router
 *
 */
class ThemesAuthors extends Eloquent
{
    use Cacheable;

    /**
     * @var string
     */
    protected $connection = 'themes';

    /**
     * @var string
     */
    protected $table = 'themes_authors';

    /**
     * @var array
     */
    protected $guarded = [
        'id',
    ];

}
