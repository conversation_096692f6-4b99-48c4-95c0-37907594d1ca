#!/usr/bin/php -q
<?php

include '/var/www/html/Class/defines.php';
include '/var/www/html/Class/Database.class.php';
$sql = new Database();


$sql->query("SELECT rec_id,record,id FROM cdr_pbx WHERE rec_id!='' AND send_stat=1 ORDER by id desc Limit 1");
$sql->execute();

if ($sql->rowCount() == 0) {
    exit;
}
$result = $sql->single();

//$record=$result['record'];
//$MIX="/var/spool/asterisk/monitor/".$record.".wav";
//$FINAL="/var/spool/asterisk/monitor/".$record.".mp3";
//exec ("lame --quiet --preset voice -v -B 32 -a ".$MIX." ".$FINAL."");
/*
$file='/var/spool/asterisk/monitor/'.$result['record'].'.mp3';
if(file_exists($file)){
$db->query("UPDATE cdr_pbx SET send_rec=1 WHERE id=:id");
$db->bind('id',$res['id'],PDO::PARAM_INT);
$db->execute();
}
else{
exit;
}

*/

$data = [
    'file' => curl_file_create('/var/spool/asterisk/monitor/' . $result['record'] . '.mp3', "audio/mp3"),
    'id' => $result['rec_id'],
];

print_r($result);

$ch = curl_init();
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_URL, "https://webhook.site/1971e9c3-e14a-4ee7-a202-972c0c5e6b21");

curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_TIMEOUT, 50);
curl_setopt($ch, CURLINFO_HEADER_OUT, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt(
    $ch,
    CURLOPT_HTTPHEADER,
    [
        'Accept:  application/json',
    ]
);

$json = curl_exec($ch);


$text = $json;

$json = (array)json_decode($json, true);

print_r($json);
if ($json['success'] == true) {
    $sql->query("Update cdr_pbx SET send_stat=2 WHERE id=" . $result['id']);
    $sql->execute();
} elseif ($json['error'] == "Recording for this call already exists") {
    $sql->query("Update cdr_pbx SET send_stat=2 WHERE id=" . $result['id']);
    $sql->execute();
} else {

}


return $json;
