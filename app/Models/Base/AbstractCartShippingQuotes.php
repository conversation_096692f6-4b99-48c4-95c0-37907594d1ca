<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Store\CartShippingQuotes as CartShippingQuotesModel;

/**
* @property integer $id
* @property integer $cart_id
* @property integer $shipping_provider_id
* @property string $service_id
* @property string $name
* @property string $description
* @property string $currency
* @property string $type
* @property string $payer
* @property integer $price
* @property integer $tax
* @property integer $insurance
* @property integer $cash_on_delivery
* @property integer $exchange_rate
* @property \Carbon\Carbon $pickup_date
* @property \Carbon\Carbon $pickup_time
* @property \Carbon\Carbon $delivery_date
* @property \Carbon\Carbon $delivery_time
* @property integer $allowance_fixed_time_delivery
* @property integer $allowance_cash_on_delivery
* @property integer $allowance_insurance
* @property \Carbon\Carbon $created_at
* @property \Carbon\Carbon $updated_at
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\Store\CartShippingQuotes $array
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereCartId($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereShippingProviderId($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereServiceId($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereName($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereDescription($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereCurrency($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereType($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel wherePayer($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel wherePrice($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereTax($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereInsurance($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereCashOnDelivery($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereExchangeRate($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel wherePickupDate($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel wherePickupTime($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereDeliveryDate($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereDeliveryTime($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereAllowanceFixedTimeDelivery($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereAllowanceCashOnDelivery($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereAllowanceInsurance($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereCreatedAt($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereUpdatedAt($value)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|CartShippingQuotesModel whereDoesntHaveMorph($type, \Closure $callable = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractCartShippingQuotes extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    protected $table = 'cart_shipping_quotes';

    protected $connection = 'default';

    protected $fillable = [
        'cart_id', 'shipping_provider_id', 'service_id',
        'name', 'description', 'currency',
        'type', 'payer', 'price',
        'tax', 'insurance', 'cash_on_delivery',
        'exchange_rate', 'pickup_date', 'pickup_time',
        'delivery_date', 'delivery_time', 'allowance_fixed_time_delivery',
        'allowance_cash_on_delivery', 'allowance_insurance'
    ];

    protected $guarded = [
        'id'
    ];


}
