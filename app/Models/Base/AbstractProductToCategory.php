<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

/**
* Foreign Keys in table: product_to_category
* --------- fk_product_to_category_category_id ---------
* Field: category_id
* Reference table: type__products_categories
* Reference field: id
* Actions: On delete - CASCADE; On update - NO ACTION
* --------- fk_product_to_category_product_id ---------
* Field: product_id
* Reference table: products
* Reference field: id
* Actions: On delete - CASCADE; On update - NO ACTION
*/

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Product\ProductCategories as ProductCategoriesModel;
use App\Traits\Cacheable;

/**
* @property integer $id
* @property integer $product_id
* @property integer $category_id
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\Product\ProductCategories $array
* @method static QueryBuilder|EloquentBuilder|ProductCategoriesModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|ProductCategoriesModel whereProductId($value)
* @method static QueryBuilder|EloquentBuilder|ProductCategoriesModel whereCategoryId($value)
* @method static QueryBuilder|EloquentBuilder|ProductCategoriesModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|ProductCategoriesModel whereDoesntHaveMorph($type, \Closure $callable = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractProductToCategory extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    use Cacheable;
    protected $table = 'product_to_category';

    protected $connection = 'default';

    protected $fillable = [
        'product_id', 'category_id'
    ];

    protected $guarded = [
        'id'
    ];


    public $timestamps = false;



}
