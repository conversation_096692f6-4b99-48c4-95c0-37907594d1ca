<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

/**
* Foreign Keys in table: customers_shipping_addresses_mapping
* --------- customers_shipping_addresses_mapping_address_id ---------
* Field: address_id
* Reference table: customers_shipping_addresses
* Reference field: id
* Actions: On delete - CASCADE; On update - NO ACTION
*/

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Customer\CustomerShippingAddressMapping as CustomerShippingAddressMappingModel;

/**
* @property integer $id
* @property integer $address_id
* @property string $integration
* @property string $country_id
* @property string $state_id
* @property string $city_id
* @property string $quarter_id
* @property string $street_id
* @property string $office_id
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\Customer\CustomerShippingAddressMapping $array
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressMappingModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressMappingModel whereAddressId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressMappingModel whereIntegration($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressMappingModel whereCountryId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressMappingModel whereStateId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressMappingModel whereCityId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressMappingModel whereQuarterId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressMappingModel whereStreetId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressMappingModel whereOfficeId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressMappingModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressMappingModel whereDoesntHaveMorph($type, \Closure $callable = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractCustomersShippingAddressesMapping extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    protected $table = 'customers_shipping_addresses_mapping';

    protected $connection = 'default';

    protected $fillable = [
        'address_id', 'integration', 'country_id',
        'state_id', 'city_id', 'quarter_id',
        'street_id', 'office_id'
    ];

    protected $guarded = [
        'id'
    ];


    public $timestamps = false;



}
