<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

/**
* Foreign Keys in table: customers_shipping_addresses
* --------- customers_shipping_addresses_ibfk_1 ---------
* Field: customer_id
* Reference table: customers
* Reference field: id
* Actions: On delete - CASCADE; On update - NO ACTION
* --------- customers_shipping_addresses_marketplace_id ---------
* Field: marketplace_id
* Reference table: shops
* Reference field: id
* Actions: On delete - SET NULL; On update - NO ACTION
*/

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Customer\CustomerShippingAddress as CustomerShippingAddressModel;
use App\Traits\AutoComplete;
use App\Traits\ExternalMetaData;

/**
* @property integer $id
* @property integer $customer_id
* @property string $integration
* @property string $country_id
* @property string $country_name
* @property string $country_iso2
* @property string $country_iso3
* @property string $state_id
* @property string $state_name
* @property string $state_iso2
* @property string $city_id
* @property string $city_name
* @property string $geo_name_city_id
* @property string $city_ascii_name
* @property string $quarter_id
* @property string $quarter_name
* @property string $street_id
* @property string $street_name
* @property string $office_id
* @property string $office_name
* @property integer $marketplace_id
* @property string $marketplace_name
* @property string $street_number
* @property string $post_code
* @property string $address1
* @property string $address2
* @property string $address3
* @property string $note
* @property string $phone
* @property string $building
* @property string $entrance
* @property string $floor
* @property string $apartment
* @property string $first_name
* @property string $last_name
* @property string $company_name
* @property string $company_vat
* @property string $timezone
* @property string $text
* @property float $latitude
* @property float $longitude
* @property string $neighborhood
* @property string $locality
* @property integer $office_type (0 - office, 1 - machine)
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\Customer\CustomerShippingAddress $array
* @property \Illuminate\Support\Collection $emd
* @property null|array $country
* @property null|array $state
* @property null|array $city
* @property mixed|null|string $office
* @property mixed $street
* @property mixed $quarter
* @property mixed $marketplace
* @property \App\Helper\OmniShip\Address $address
* @property string $phone_country_iso2
* @property string $phone_e164
* @property string $phone_international
* @property string $phone_national
* @property string $phone_rfc3966
* @property bool $company
* @property bool $company_yes_no
* @property string $formatted
* @property string $full_name
* @property mixed|null|string $timezone_id
* @property mixed|null|string $is_default
* @property mixed|null|string $city_name_full
* **** from trait App\Traits\AutoComplete ****
* @method static array autoComplete($name, \Closure $where = null, \Closure $callback = null, $key = 'results')
* @method static \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder|\App\Models\Customer\CustomerShippingAddress newAutoComplete($name, $field_name_formatted = null)
* @method static string|null getAutocompleteForSingle($recordId = null)
* @method static string|null getAutocompleteForMultiple($recordIds = [])
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereCustomerId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereIntegration($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereCountryId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereCountryName($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereCountryIso2($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereCountryIso3($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereStateId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereStateName($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereStateIso2($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereCityId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereCityName($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereGeoNameCityId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereCityAsciiName($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereQuarterId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereQuarterName($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereStreetId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereStreetName($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereOfficeId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereOfficeName($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereMarketplaceId($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereMarketplaceName($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereStreetNumber($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel wherePostCode($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereAddress1($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereAddress2($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereAddress3($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereNote($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel wherePhone($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereBuilding($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereEntrance($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereFloor($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereApartment($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereFirstName($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereLastName($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereCompanyName($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereCompanyVat($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereTimezone($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereText($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereLatitude($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereLongitude($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereNeighborhood($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereLocality($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereOfficeType($value)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel realAddress()
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel officeAddress($integration)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel marketplaceAddress()
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereActiveByGeoZones()
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel whereDoesntHaveMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel newAutoComplete($name, $field_name_formatted = null)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel setFiltersWhere($where = null)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel setFiltersJoins($join = null)
* @method static \Illuminate\Support\Collection|CustomerShippingAddressModel[] getRelationList($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel getRelationModel($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel vueGetRelationList($where = null, $join = null, \Modules\Core\Core\Helpers\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|CustomerShippingAddressModel getSqlQuery($where = null, $join = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractCustomersShippingAddresses extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    use ExternalMetaData;
    use AutoComplete;
    protected $table = 'customers_shipping_addresses';

    protected $connection = 'default';

    protected $fillable = [
        'customer_id', 'integration', 'country_id',
        'country_name', 'country_iso2', 'country_iso3',
        'state_id', 'state_name', 'state_iso2',
        'city_id', 'city_name', 'geo_name_city_id',
        'city_ascii_name', 'quarter_id', 'quarter_name',
        'street_id', 'street_name', 'office_id',
        'office_name', 'marketplace_id', 'marketplace_name',
        'street_number', 'post_code', 'address1',
        'address2', 'address3', 'note',
        'phone', 'building', 'entrance',
        'floor', 'apartment', 'first_name',
        'last_name', 'company_name', 'company_vat',
        'timezone', 'text', 'latitude',
        'longitude', 'neighborhood', 'locality',
        'office_type'
    ];

    protected $guarded = [
        'id'
    ];


    public $timestamps = false;



}
