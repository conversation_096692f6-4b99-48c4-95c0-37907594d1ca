<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Modules\Core\Core\Models\Imports\CsvModel as CsvModelModel;

/**
* @property integer $id
* @property string $type
* @property string $tableName
* @property string $filename
* @property string $settings
* @property \Carbon\Carbon $created_at
* @property \Carbon\Carbon $updated_at
* @property string $mapping
* @property string $status
* @property integer $total_products
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \Modules\Core\Core\Models\Imports\CsvModel $array
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereType($value)
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereTableName($value)
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereFilename($value)
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereSettings($value)
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereCreatedAt($value)
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereUpdatedAt($value)
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereMapping($value)
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereStatus($value)
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereTotalProducts($value)
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|CsvModelModel whereDoesntHaveMorph($type, \Closure $callable = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractCsvTasks extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    protected $table = 'csv_tasks';

    protected $connection = 'default';

    protected $fillable = [
        'type', 'tableName', 'filename',
        'settings', 'mapping', 'status',
        'total_products'
    ];

    protected $guarded = [
        'id'
    ];


    public $_status_column = "status";

    protected $_has_publish_date = false;


}
