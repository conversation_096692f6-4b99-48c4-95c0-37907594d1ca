<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

/**
* Foreign Keys in table: hooks
* --------- fk_hooks_api_key_id ---------
* Field: api_key_id
* Reference table: api_keys
* Reference field: id
* Actions: On delete - NO ACTION; On update - NO ACTION
*/

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Setting\Hook as HookModel;

/**
* @property integer $id
* @property integer $api_key_id
* @property string $url
* @property string $event
* @property \Carbon\Carbon $created_at
* @property \Carbon\Carbon $updated_at
* @property integer $active
* @property integer $new_version
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\Setting\Hook $array
* @property mixed|null|string $request_headers
* @property mixed|null|string $name
* @method static QueryBuilder|EloquentBuilder|HookModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|HookModel whereApiKeyId($value)
* @method static QueryBuilder|EloquentBuilder|HookModel whereUrl($value)
* @method static QueryBuilder|EloquentBuilder|HookModel whereEvent($value)
* @method static QueryBuilder|EloquentBuilder|HookModel whereCreatedAt($value)
* @method static QueryBuilder|EloquentBuilder|HookModel whereUpdatedAt($value)
* @method static QueryBuilder|EloquentBuilder|HookModel whereActive($value)
* @method static QueryBuilder|EloquentBuilder|HookModel whereNewVersion($value)
* @method static QueryBuilder|EloquentBuilder|HookModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|HookModel whereDoesntHaveMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|HookModel setFiltersWhere($where = null)
* @method static QueryBuilder|EloquentBuilder|HookModel setFiltersJoins($join = null)
* @method static \Illuminate\Support\Collection|HookModel[] getRelationList($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|HookModel getRelationModel($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|HookModel vueGetRelationList($where = null, $join = null, \Modules\Core\Core\Helpers\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|HookModel getSqlQuery($where = null, $join = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractHooks extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    protected $table = 'hooks';

    protected $connection = 'default';

    protected $fillable = [
        'api_key_id', 'url', 'event',
        'active', 'new_version'
    ];

    protected $guarded = [
        'id'
    ];


    public $_status_column = "active";

    protected $_has_publish_date = false;


}
