<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Setting\AdminPermission as AdminPermissionModel;

/**
* @property integer $admin_id
* @property string $section
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\Setting\AdminPermission $array
* @method static QueryBuilder|EloquentBuilder|AdminPermissionModel whereAdminId($value)
* @method static QueryBuilder|EloquentBuilder|AdminPermissionModel whereSection($value)
* @method static QueryBuilder|EloquentBuilder|AdminPermissionM<PERSON>l whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|AdminPermissionModel whereDoesntHaveMorph($type, \Closure $callable = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractAdminsPermissions extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    protected $table = 'admins_permissions';

    protected $connection = 'default';

    protected $fillable = [
        'admin_id', 'section'
    ];

    public $timestamps = false;



}
