<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Modules\Marketing\Segments\Core\Models\SubscriberEvents as SubscriberEventsModel;

/**
* @property integer $id
* @property integer $customer_id
* @property integer $subscriber_id
* @property string $uuid
* @property string $record_type
* @property integer $record_id
* @property integer $category_id
* @property integer $vendor_id
* @property integer $price
* @property \Carbon\Carbon $updated_at (Delete record after 3 months)
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \Modules\Marketing\Segments\Core\Models\SubscriberEvents $array
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel whereCustomerId($value)
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel whereSubscriberId($value)
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel whereUuid($value)
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel whereRecordType($value)
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel whereRecordId($value)
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel whereCategoryId($value)
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel whereVendorId($value)
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel wherePrice($value)
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel whereUpdatedAt($value)
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|SubscriberEventsModel whereDoesntHaveMorph($type, \Closure $callable = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractSubscribersEvents extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    protected $table = 'subscribers_events';

    protected $connection = 'default';

    protected $fillable = [
        'customer_id', 'subscriber_id', 'uuid',
        'record_type', 'record_id', 'category_id',
        'vendor_id', 'price'
    ];

    protected $guarded = [
        'id'
    ];


    public const CREATED_AT = 'updated_at';



}
