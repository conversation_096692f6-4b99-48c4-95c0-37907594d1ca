<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Customer\Mail as MailModel;

/**
* @property integer $id
* @property string $label
* @property integer $active
* @property \Carbon\Carbon $created_at
* @property \Carbon\Carbon $last_edited
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\Customer\Mail $array
* @property mixed|null|string $template
* @method static QueryBuilder|EloquentBuilder|MailModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|MailModel whereLabel($value)
* @method static QueryBuilder|EloquentBuilder|MailModel whereActive($value)
* @method static QueryBuilder|EloquentBuilder|MailModel whereCreatedAt($value)
* @method static QueryBuilder|EloquentBuilder|MailModel whereLastEdited($value)
* @method static QueryBuilder|EloquentBuilder|MailModel active()
* @method static QueryBuilder|EloquentBuilder|MailModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|MailModel whereDoesntHaveMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|MailModel translatedIn($locale = null)
* @method static QueryBuilder|EloquentBuilder|MailModel translated()
* @method static QueryBuilder|EloquentBuilder|MailModel listsTranslations($translationField)
* @method static QueryBuilder|EloquentBuilder|MailModel withTranslation()
* @method static QueryBuilder|EloquentBuilder|MailModel whereTranslation($key, $value, $locale = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractMailsCustomers extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    protected $table = 'mails__customers';

    protected $connection = 'default';

    protected $fillable = [
        'label', 'active'
    ];

    protected $guarded = [
        'id'
    ];


    public const UPDATED_AT = 'last_edited';


    public $_status_column = "active";

    protected $_has_publish_date = false;


}
