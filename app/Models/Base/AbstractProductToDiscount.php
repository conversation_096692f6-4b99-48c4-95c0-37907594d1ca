<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

/**
* Foreign Keys in table: product_to_discount
* --------- fk_product_to_discount_customer_group_id ---------
* Field: customer_group_id
* Reference table: type__customer_groups
* Reference field: id
* Actions: On delete - CASCADE; On update - NO ACTION
* --------- fk_product_to_discount_discount_id ---------
* Field: discount_id
* Reference table: discounts
* Reference field: id
* Actions: On delete - CASCADE; On update - NO ACTION
* --------- fk_product_to_discount_product_id ---------
* Field: product_id
* Reference table: products
* Reference field: id
* Actions: On delete - CASCADE; On update - NO ACTION
* --------- fk_product_to_discount_target_id ---------
* Field: target_id
* Reference table: discounts_to_targets
* Reference field: id
* Actions: On delete - CASCADE; On update - NO ACTION
* --------- fk_product_to_discount_variant_id ---------
* Field: variant_id
* Reference table: products_variants
* Reference field: id
* Actions: On delete - CASCADE; On update - NO ACTION
*/

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Discount\ProductToDiscount as ProductToDiscountModel;
use App\Traits\Cacheable;

/**
* @property integer $id
* @property integer $product_id
* @property integer $discount_id
* @property integer $target_id
* @property integer $variant_id
* @property integer $customer_group_id
* @property integer $active
* @property integer $price
* @property integer $save
* @property integer $msrp_price (Manufacturer''s Suggested Retail Price)
* @property \Carbon\Carbon $date_start
* @property \Carbon\Carbon $date_end
* @property string $type
* @property \Carbon\Carbon $created_at
* @property \Carbon\Carbon $updated_at
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\Discount\ProductToDiscount $array
* @property null|float $price_input
* @property string $price_formatted
* @property string $price_plus_save_formatted
* @property mixed|null|string $discount_type
* @property mixed|null|string $save_formatted
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereProductId($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereDiscountId($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereTargetId($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereVariantId($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereCustomerGroupId($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereActive($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel wherePrice($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereSave($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereMsrpPrice($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereDateStart($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereDateEnd($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereType($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereCreatedAt($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereUpdatedAt($value)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel active(\Closure $has = null, $with = true)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel activeWithoutCustomerGroup()
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel activeDate()
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel activeDateWithShipping()
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel activeCp()
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel productVariant($product_id, $variant_id)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel whereDoesntHaveMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel setFiltersWhere($where = null)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel setFiltersJoins($join = null)
* @method static \Illuminate\Support\Collection|ProductToDiscountModel[] getRelationList($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel getRelationModel($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel vueGetRelationList($where = null, $join = null, \Modules\Core\Core\Helpers\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|ProductToDiscountModel getSqlQuery($where = null, $join = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractProductToDiscount extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    use Cacheable;
    protected $table = 'product_to_discount';

    protected $connection = 'default';

    protected $fillable = [
        'product_id', 'discount_id', 'target_id',
        'variant_id', 'customer_group_id', 'active',
        'price', 'save', 'msrp_price',
        'date_start', 'date_end', 'type'
    ];

    protected $guarded = [
        'id'
    ];

    public $_status_column = "active";

    protected $_has_publish_date = false;


}
