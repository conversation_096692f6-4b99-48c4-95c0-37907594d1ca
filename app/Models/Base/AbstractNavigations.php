<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

/**
* Foreign Keys in table: navigations
* --------- navigations_group_id_foreign ---------
* Field: group_id
* Reference table: navigations_groups
* Reference field: id
* Actions: On delete - CASCADE; On update - NO ACTION
*/

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\StoreFront\Navigations as NavigationsModel;
use App\Traits\Cacheable;

/**
* @property integer $id
* @property integer $group_id
* @property integer $parent_id
* @property integer $order
* @property string $type (url,product,category,vendor,blog,article,page,section,group,selection,widget)
* @property string $link_type
* @property integer $link_id
* @property string $route
* @property string $active_find
* @property string $url
* @property integer $blank
* @property string $class
* @property string $name
* @property string $widget
* @property string $widget_text
* @property array $widget_config
* @property array $permissions
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\StoreFront\Navigations $array
* @property mixed|null|string $extension
* @property mixed|null|string $allow_edit
* @property string $aspect_ratio
* @property mixed|null|string $link_formatted
* @property mixed|null|string $auto_complete_value
* @property mixed|null|string $active
* @property mixed|null|string $children_count
* @property mixed|null|string $widget_instance
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereGroupId($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereParentId($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereOrder($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereType($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereLinkType($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereLinkId($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereRoute($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereActiveFind($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereUrl($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereBlank($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereClass($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereName($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereWidget($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereWidgetText($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereWidgetConfig($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel wherePermissions($value)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereGroup($group)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel whereDoesntHaveMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel fileName($filename)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel setFiltersWhere($where = null)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel setFiltersJoins($join = null)
* @method static \Illuminate\Support\Collection|NavigationsModel[] getRelationList($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel getRelationModel($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel vueGetRelationList($where = null, $join = null, \Modules\Core\Core\Helpers\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|NavigationsModel getSqlQuery($where = null, $join = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractNavigations extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    use Cacheable;
    protected $table = 'navigations';

    protected $connection = 'default';

    protected $fillable = [
        'group_id', 'parent_id', 'order',
        'type', 'link_type', 'link_id',
        'route', 'active_find', 'url',
        'blank', 'class', 'name',
        'widget', 'widget_text', 'widget_config',
        'permissions'
    ];

    protected $guarded = [
        'id'
    ];


    public $timestamps = false;



}
