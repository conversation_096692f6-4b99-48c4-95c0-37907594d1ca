<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Product\Status as StatusModel;
use App\Traits\Cacheable;
use App\Traits\AutoComplete;

/**
* @property integer $id
* @property integer $quantity_operator_id
* @property integer $quantity
* @property string $type
* @property string $name
* @property string $button_text
* @property integer $sorting
* @property \Carbon\Carbon $created_at
* @property \Carbon\Carbon $updated_at
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\Product\Status $array
* @property mixed|null|string $system_key
* @property mixed|null|string $system_name
* @property mixed|null|string $button_label
* **** from trait App\Traits\AutoComplete ****
* @method static array autoComplete($name, \Closure $where = null, \Closure $callback = null, $key = 'results')
* @method static \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder|\App\Models\Product\Status newAutoComplete($name, $field_name_formatted = null)
* @method static string|null getAutocompleteForSingle($recordId = null)
* @method static string|null getAutocompleteForMultiple($recordIds = [])
* @method static QueryBuilder|EloquentBuilder|StatusModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|StatusModel whereQuantityOperatorId($value)
* @method static QueryBuilder|EloquentBuilder|StatusModel whereQuantity($value)
* @method static QueryBuilder|EloquentBuilder|StatusModel whereType($value)
* @method static QueryBuilder|EloquentBuilder|StatusModel whereName($value)
* @method static QueryBuilder|EloquentBuilder|StatusModel whereButtonText($value)
* @method static QueryBuilder|EloquentBuilder|StatusModel whereSorting($value)
* @method static QueryBuilder|EloquentBuilder|StatusModel whereCreatedAt($value)
* @method static QueryBuilder|EloquentBuilder|StatusModel whereUpdatedAt($value)
* @method static QueryBuilder|EloquentBuilder|StatusModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|StatusModel whereDoesntHaveMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|StatusModel newAutoComplete($name, $field_name_formatted = null)
* @method static QueryBuilder|EloquentBuilder|StatusModel setFiltersWhere($where = null)
* @method static QueryBuilder|EloquentBuilder|StatusModel setFiltersJoins($join = null)
* @method static \Illuminate\Support\Collection|StatusModel[] getRelationList($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|StatusModel getRelationModel($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|StatusModel vueGetRelationList($where = null, $join = null, \Modules\Core\Core\Helpers\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|StatusModel getSqlQuery($where = null, $join = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractProductStatuses extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    use AutoComplete;
    use Cacheable;
    protected $table = 'product_statuses';

    protected $connection = 'default';

    protected $fillable = [
        'quantity_operator_id', 'quantity', 'type',
        'name', 'button_text', 'sorting'
    ];

    protected $guarded = [
        'id'
    ];



}
