<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

/**
* Foreign Keys in table: admins_2fa_codes
* --------- admins_2fa_codes_admin_id ---------
* Field: admin_id
* Reference table: admins
* Reference field: id
* Actions: On delete - CASCADE; On update - NO ACTION
*/

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Setting\Admins2faCodes as Admins2faCodesModel;

/**
* @property integer $id
* @property integer $admin_id
* @property string $code
* @property integer $used
* @property \Carbon\Carbon $created_at
* @property \Carbon\Carbon $updated_at
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\Setting\Admins2faCodes $array
* @method static QueryBuilder|EloquentBuilder|Admins2faCodesModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|Admins2faCodesModel whereAdminId($value)
* @method static QueryBuilder|EloquentBuilder|Admins2faCodesModel whereCode($value)
* @method static QueryBuilder|EloquentBuilder|Admins2faCodesModel whereUsed($value)
* @method static QueryBuilder|EloquentBuilder|Admins2faCodesModel whereCreatedAt($value)
* @method static QueryBuilder|EloquentBuilder|Admins2faCodesModel whereUpdatedAt($value)
* @method static QueryBuilder|EloquentBuilder|Admins2faCodesModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|Admins2faCodesModel whereDoesntHaveMorph($type, \Closure $callable = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractAdmins2faCodes extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    protected $table = 'admins_2fa_codes';

    protected $connection = 'default';

    protected $fillable = [
        'admin_id', 'code', 'used'
    ];

    protected $guarded = [
        'id'
    ];



}
