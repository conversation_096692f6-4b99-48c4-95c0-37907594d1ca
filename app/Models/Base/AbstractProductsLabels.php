<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Modules\Marketing\ProductsLabels\Models\Labels as LabelsModel;

/**
* @property integer $id
* @property string $name
* @property string $description
* @property string $color
* @property string $text_color
* @property integer $status
* @property string $position
* @property \Carbon\Carbon $active_from
* @property \Carbon\Carbon $active_to
* @property \Carbon\Carbon $created_at
* @property \Carbon\Carbon $updated_at
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \Modules\Marketing\ProductsLabels\Models\Labels $array
* @property \Illuminate\Support\Collection $meta
* @property mixed|null|string $style
* @property mixed|null|string $label_position
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereName($value)
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereDescription($value)
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereColor($value)
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereTextColor($value)
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereStatus($value)
* @method static QueryBuilder|EloquentBuilder|LabelsModel wherePosition($value)
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereActiveFrom($value)
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereActiveTo($value)
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereCreatedAt($value)
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereUpdatedAt($value)
* @method static QueryBuilder|EloquentBuilder|LabelsModel active()
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|LabelsModel whereDoesntHaveMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|LabelsModel setFiltersWhere($where = null)
* @method static QueryBuilder|EloquentBuilder|LabelsModel setFiltersJoins($join = null)
* @method static \Illuminate\Support\Collection|LabelsModel[] getRelationList($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|LabelsModel getRelationModel($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|LabelsModel vueGetRelationList($where = null, $join = null, \Modules\Core\Core\Helpers\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|LabelsModel getSqlQuery($where = null, $join = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractProductsLabels extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    protected $table = 'products_labels';

    protected $connection = 'default';

    protected $fillable = [
        'name', 'description', 'color',
        'text_color', 'status', 'position',
        'active_from', 'active_to'
    ];

    protected $guarded = [
        'id'
    ];

    public $_status_column = "status";

    protected $_has_publish_date = false;


}
