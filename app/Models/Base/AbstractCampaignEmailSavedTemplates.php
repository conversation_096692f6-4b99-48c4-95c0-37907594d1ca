<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Modules\Marketing\Campaign\Channels\Email\Models\CampaignEmailSavedTemplate as CampaignEmailSavedTemplateModel;

/**
* @property integer $id
* @property string $name
* @property string $subject
* @property string $message_html
* @property string $template_json
* @property string $image
* @property \Carbon\Carbon $created_at
* @property \Carbon\Carbon $updated_at
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \Modules\Marketing\Campaign\Channels\Email\Models\CampaignEmailSavedTemplate $array
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel whereName($value)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel whereSubject($value)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel whereMessageHtml($value)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel whereTemplateJson($value)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel whereImage($value)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel whereCreatedAt($value)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel whereUpdatedAt($value)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel whereDoesntHaveMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel setFiltersWhere($where = null)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel setFiltersJoins($join = null)
* @method static \Illuminate\Support\Collection|CampaignEmailSavedTemplateModel[] getRelationList($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel getRelationModel($where = null, $join = null, \App\Helper\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel vueGetRelationList($where = null, $join = null, \Modules\Core\Core\Helpers\Grid $grid = null)
* @method static QueryBuilder|EloquentBuilder|CampaignEmailSavedTemplateModel getSqlQuery($where = null, $join = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractCampaignEmailSavedTemplates extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    protected $table = 'campaign_email_saved_templates';

    protected $connection = 'default';

    protected $fillable = [
        'name', 'subject', 'message_html',
        'template_json', 'image'
    ];

    protected $guarded = [
        'id'
    ];



}
