<?php

declare(strict_types=1);

/**
 * Created by <PERSON><PERSON> (CloudCart).
 * User: <PERSON><PERSON> <<EMAIL>>
 * Date: 2025
 */

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model as Eloquent;
use App\Traits\Model as ModelTrait;
use App\Traits\DbTimezone;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use App\Models\Store\CartBundle as CartBundleModel;

/**
* @property integer $id
* @property integer $cart_id
* @property integer $bundle_product_id
* @property integer $quantity
* @property mixed $class_castable
* @property mixed $enum_castable
* @property mixed $mutated
* @property mixed $attribute_marked_mutated
* @property \App\Models\Store\CartBundle $array
* @method static QueryBuilder|EloquentBuilder|CartBundleModel whereId($value)
* @method static QueryBuilder|EloquentBuilder|CartBundleModel whereCartId($value)
* @method static QueryBuilder|EloquentBuilder|CartBundleModel whereBundleProductId($value)
* @method static QueryBuilder|EloquentBuilder|CartBundleModel whereQuantity($value)
* @method static QueryBuilder|EloquentBuilder|CartBundleModel whereHasMorph($type, \Closure $callable = null)
* @method static QueryBuilder|EloquentBuilder|CartBundleModel whereDoesntHaveMorph($type, \Closure $callable = null)
*
* @category   Base Abstract Models
* @package    App\Models\Base
* <AUTHOR> <<EMAIL>>
* @version    Release: 11.42.1
*/

abstract class AbstractCartBundles extends Eloquent
{
    use ModelTrait;
    use DbTimezone;
    protected $table = 'cart_bundles';

    protected $connection = 'default';

    protected $fillable = [
        'cart_id', 'bundle_product_id', 'quantity'
    ];

    protected $guarded = [
        'id'
    ];


    public $timestamps = false;



}
