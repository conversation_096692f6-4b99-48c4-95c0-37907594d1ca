<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 24.4.2018 г.
 * Time: 10:48 ч.
 */

namespace App\Models\Setting;

use App\Models\Base\AbstractAdminsLoggingMeta;

class AdminLoggingMeta extends AbstractAdminsLoggingMeta
{
    /**
     * The attributes that should be cast to native types.
     *
     * @return array
     */
    protected function casts(): array
    {
        return [
            'value' => 'object'
        ];
    }

}
