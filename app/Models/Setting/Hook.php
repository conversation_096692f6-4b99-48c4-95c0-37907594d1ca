<?php

declare(strict_types=1);

namespace App\Models\Setting;

use App\Common\DateTimeFormat;
use App\Helper\Grid;
use App\Models\Base\AbstractHooks;
use App\Traits\Crudling;
use App\Events\HookEvent;
use Illuminate\Support\Collection;
use Modules\Core\Core\Models\Alerts;

/**
 * @property HookHeaders[]|Collection $headers
 * @property ApiKey $key
 */
class Hook extends AbstractHooks
{
    use Crudling;

    #[\Override]
    protected static function boot()
    {
        parent::boot();

        self::saving(function (Hook $hook): void {
            unset($hook->event_link, $hook->url_link, $hook->actions, $hook->created, $hook->updated);

            if($hook->isDirty('active') && $hook->active) {
                Alerts::where('mapping', sprintf('web_hooks_%s', md5((string) $hook->id)))->update([
                    'notified_at' => null,
                    'mail_sent' => null,
                ]);
            }
        });
    }

    public function key()
    {
        return $this->belongsTo(ApiKey::class, 'api_key_id');
    }

    public function headers()
    {
        return $this->hasMany(HookHeaders::class);
    }

    public function format(): static
    {
        //        $this->setAttribute('event_link', '<a href="javascript:void(0)" data-modal-size="small" data-modal-ajax="'
        //            . route('admin.hooks.edit', $this->id) . "\">".__($this->event)."</a>");

        $this->setAttribute('event_link', '<a href="' . route('admin.hooks.edit', $this->id) . '" data-panel-class="medium" data-ajax-panel="true">' . $this->name . "</a>");

        $this->setAttribute('url_link', '<a href="' . route('admin.hooks.edit', $this->id) . '" data-panel-class="medium" data-ajax-panel="true">' . $this->url . "</a>");

        $this->setAttribute('actions', sprintf(
            '<a href="#" class="delete" data-ajax="%s" data-confirm="%s" data-placement="left" title="%s"></a>',
            route('admin.hooks.delete', $this->id),
            __('integration.confirm.api_key.delete'),
            __('integration.action.delete')
        ));

        $this->setAttribute('key_name', $this->key ? $this->key->name : __('global.N/A'));
        $this->setAttribute('created', $this->created_at->format(DateTimeFormat::$date_formats[setting('date_format')]['format']));
        $this->setAttribute('updated', $this->updated_at->format(DateTimeFormat::$date_formats[setting('date_format')]['format']));
        $this->setAttribute('active', Grid::generateBool(route('admin.hooks.status', $this->id), $this->active));

        return $this;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getRequestHeadersAttribute($value)
    {
        $headers = [];
        if ($this->headers->isNotEmpty()) {
            $headers = $this->headers->pluck('value', 'key')->all();
        }
        return $headers;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getNameAttribute($value)
    {
        $events = fast_collapse(HookEvent::types());
        return $events[$this->event] ?? $this->event;
    }

}
