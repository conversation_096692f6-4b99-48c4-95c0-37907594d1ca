<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 26.7.2016 г.
 * Time: 14:23 ч.
 */

namespace App\Models\Setting;

use App\Models\Base\AbstractAdminsImports;

class AdminImport extends AbstractAdminsImports
{

    protected function casts(): array
    {
        return ['date_started' => 'datetime', 'date_stopped' => 'datetime', 'date_ended' => 'datetime'];
    }

    /**
     * @param $type
     * @param null $admin_id
     * @param array|null $fields
     * @param null $active
     * @return array
     */
    public static function getImports(string $type, $admin_id = null, ?array $fields = null, $active = null)
    {
        $admin_addon = "";
        if ($admin_id != null) {
            $admin_addon = " AND `admin_id` = '" . intval($admin_id) . "'";
        }

        $active_clause = '';
        if ($active === true) {
            $active_clause = " AND `active` = 'yes'";
        } elseif ($active === false) {
            $active_clause = " AND `active` = 'no'";
        }

        return static::whereRaw("`type` = '" . $type . sprintf("' %s%s", $active_clause, $admin_addon))->get()->keyBy('id')->all();

    }

}
