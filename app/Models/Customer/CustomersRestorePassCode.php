<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 24.6.2016 г.
 * Time: 10:44 ч.
 */

namespace App\Models\Customer;

use App\Models\Base\AbstractCustomersRestorePassCodes;
use App\Traits\RestorePassCode;

class CustomersRestorePassCode extends AbstractCustomersRestorePassCodes
{
    use RestorePassCode;

    /**
     * @var string
     */
    protected $table = 'customers_restore_pass_codes';

    /**
     * @var array
     */
    protected $fillable = [
        'customer_id', 'code', 'date_sent'
    ];

    protected function casts(): array
    {
        return ['date_sent' => 'datetime'];
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|Customer|null
     */
    public function user()
    {
        return $this->hasOne(Customer::class, 'id', 'customer_id');
    }

}
