<?php

declare(strict_types=1);

namespace App\Models\Customer;

use App\Models\Base\AbstractCustomersCustomFields;
use App\Models\Layout\FormFields;
use App\Traits\AutoComplete;

class CustomField extends AbstractCustomersCustomFields
{
    use AutoComplete;

    protected $name_field = 'value';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|FormFields
     */
    public function field()
    {
        return $this->hasOne(FormFields::class, 'id', 'field_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|FormFields
     */
    public function customer()
    {
        return $this->hasOne(Customer::class, 'id', 'customer_id');
    }

    protected function casts(): array
    {
        return [
            'value' => 'json',
        ];
    }

}
