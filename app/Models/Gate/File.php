<?php

declare(strict_types=1);

namespace App\Models\Gate;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class File
 * @property Carbon $created_at
 * @property string $name
 * @method static Builder|\App\Models\Gate\File public()
 * @method static Builder|\App\Models\Gate\File hidden()
 * @method static void  truncate()
 * @method static bool  insert($values)
 * @package App\Models
 */
class File extends BaseModel
{
    /**
     * @var string
     */
    protected $connection = 'gate';

    /**
     * @var string
     */
    protected $table = 'cc_gate.files';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'size', 'mime', 'hidden'];

    /**
     * Attributes dynamically-appended to the model
     *
     * @var array
     */
    protected $appends = [
        'url',
        'var_url',
        'datetime'
    ];

    /**
     * @param $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublic($query)
    {
        return $query->where('hidden', 0);
    }

    /**
     * @param $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeHidden($query)
    {
        return $query->where('hidden', 1);
    }

    /**
     * The attributes that should be casted to native types.
     *
     * @return array
     */
    protected function casts(): array
    {
        return [
            'id' => 'int',
            'size' => 'int',
        ];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getUrlAttribute($value): string
    {
        return config('url.cdn_gate') . $this->name;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getVarUrlAttribute($value): string
    {
        return '{cdn_gate}' . $this->name;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDatetimeAttribute($value)
    {
        return $this->created_at->toDateTimeString();
    }

}
