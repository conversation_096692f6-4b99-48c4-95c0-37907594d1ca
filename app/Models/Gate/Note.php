<?php

declare(strict_types=1);

namespace App\Models\Gate;

use App\Models\Oauth\User as OauthUser;

/**
 * Class Note
 * @property \Illuminate\Database\Eloquent\Model $model
 * @property int $user_id
 * @property int $cc_user_id
 * @property User $user
 * @property UsersSites $site
 * @property string $content
 *
 * @package App\Models
 */
class Note extends BaseModel
{
    protected $connection = 'gate';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'cc_gate.notes';

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
    ];

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id')->withTrashed();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function site()
    {
        return $this->belongsTo(UsersSites::class, 'site_id')->withTrashed();
    }

    /**
     * CloudCart employee user id
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|OauthUser
     */
    public function ccUser()
    {
        return $this->belongsTo(OauthUser::class, 'cc_user_id', 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function model()
    {
        return $this->morphTo();
    }
}
