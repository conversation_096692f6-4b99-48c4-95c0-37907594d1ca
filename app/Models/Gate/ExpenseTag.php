<?php

declare(strict_types=1);

namespace App\Models\Gate;

use Carbon\Carbon;

/**
 * Class ExpenseTag
 * @package App\Models\Gate
 * @property string $content
 * @property string $filename
 * @property Carbon $period
 */
class ExpenseTag extends BaseModel
{
    /**
     * @var string
     */
    protected $connection = 'gate';

    /**
     * @var string
     */
    protected $table = 'expense_tags';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

}
