<?php

declare(strict_types=1);

namespace App\Models\Gate;

use App\Contracts\InvoiceItemContract;
use App\Helper\CloudcartCurrency;
use App\Helper\Gate\Format;
use App\Helper\Mail\ElasticEmail;
use App\Mail\ExpertNewServiceJob;
use App\Models\Oauth\User as OauthUser;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Mpdf\Mpdf;
use DNS2D;

/**
 * Class Invoice
 * @property UsersSites $site
 * @property integer $id
 * @property integer $amount
 * @property integer $amount_without_discount
 * @property integer $number
 * @property string $currency
 * @property User $user
 * @property Collection|InvoiceItem[] $items
 * @property string $type
 * @property integer $recipient_id
 * @property UserSiteInvoicing $recipient
 * @property Carbon $created_at
 * @property integer $canceled
 * @property integer $issuer_id
 * @property string $issuer_name
 * @property integer $user_id
 * @property integer $site_id
 * @property Transaction $transaction
 * @property array $issuer_data
 * @property string $payment_provider
 * @property string $payment_method
 * @property string $payment_status
 * @property int $invoice_id
 * @property Invoice $invoice
 * @property string $subscription_id
 * @property SiteSubscription $subscription
 * @property OauthUser $issuer
 * @property int $discount_amount
 * @property Discount $discount
 * @property string $invoice_number
 * @property int $renew
 * @property int $reseller_id
 * @property int $site_first_invoice
 * @property int $include_vat
 * @property int|null $lta_contract_id
 * @property LtaContract|null $ltaContract
 * @property IssuerCompany $issuerCompany
 * @property int $issuer_company_id
 * @property int $amount_vat
 * @property int $amount_excluding_vat
 * @property int|null $scope_reseller_id
 * @method static Invoice|Builder owner($user_id)
 * @method static Invoice|Builder forSite($site_id)
 * @method static Builder console()
 * @method static Builder consoleReseller()
 * @method static Builder resellerSite()
 * @package App\Models
 */
class Invoice extends BaseModel
{
    public const INVOICE = 'invoice';

    public const CREDIT_NOTE = 'credit';

    public const DEBIT_NOTE = 'debit';

    public const PROFORMA = 'proforma';

    public const PAID = 'paid';

    public const UNPAID = 'unpaid';

    public const CARD = 'card';

    public const BANK = 'bank';

    public const DEFAULT_ISSUER_COMPANY_ID = 5;

    public const DE_ISSUER_COMPANY_ID = 7;

    protected $connection = 'gate';

    protected $table = 'cc_gate.invoices';

    //    protected $table = 'cc_gate.invoices_prod'; // only for dev testing

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [
        'id',
        'number',
        'created_at',
        'updated_at',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'invoice_number',
        'recipient_type',
        'amount_formatted',
        'date_formatted'
    ];

    /**
     * @param mixed $siteId
     * @return mixed
     */
    public static function getSiteTotalSpent($siteId): int|float
    {
        $total = 0;
        $invoices = static::forSite($siteId)
            ->where('canceled', 0)
            ->get(['amount_excluding_vat', 'currency']);

        foreach ($invoices as $invoice) {
            /** @var Invoice $invoice */
            $total += $invoice->currency == 'EUR' ? $invoice->amount_excluding_vat :
                CloudcartCurrency::convert($invoice->amount_excluding_vat, 'EUR', $invoice->currency);
        }

        return $total;
    }

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    #[\Override]
    protected static function boot()
    {
        parent::boot();

        static::creating(function (Invoice $invoice): void {
            $invoice->issuer_company_id ??= $invoice->user->issuer_company_id;
            $invoice->number = static::generateNumber($invoice);

            if (!$invoice->canceled && !self::forSite($invoice->site_id)
                    ->where('canceled', 0)
                    ->where('site_first_invoice', 1)
                    ->where('created_at', '<', now()->utc())
                    ->exists()
            ) {
                $invoice->site_first_invoice = 1;
            }
        });

        static::saving(function (Invoice $invoice): void {
            if ($invoice->isDirty('recipient_id')) {
                $invoice->include_vat = (int)$invoice->recipient->include_vat;
            }
        });
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $builder
     * @return \Illuminate\Database\Eloquent\Builder|null
     */
    public function scopeConsole($builder)
    {
        /** @var OauthUser $user */
        $user = auth()->user();
        $role = $user->getConsoleRole();

        switch ($role) {
            case OauthUser::CONSOLE_PARTNER:
                if ($user->keyAccount->isDefaultAccount()) {
                    //                return $builder->whereHas('user', function (Builder $query) use ($user) {
                    //                    return $query->where('reseller_id', $user->keyAccount->reseller_id);
                    //                });
                    return $builder->where('scope_reseller_id', $user->keyAccount->reseller_id);
                }

                //                return $builder->whereHas('user', function (Builder $query) use ($user) {
                //                    return $query->where('key_account_id', $user->keyAccount->getKey());
                //                });
                return $builder->where('scope_key_account_id', $user->keyAccount->getKey());
            case OauthUser::CONSOLE_CLOUD_CART:
                if (!empty($user->countries)) {
                    return $builder->whereHas('user', function ($q) use ($user): void {
                        $q->whereIn('country', $user->countries);
                    });
                }

                return;
            default:
                return $builder->where('id', 0);
        }
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $builder
     * @return \Illuminate\Database\Eloquent\Builder|null
     */
    public function scopeConsoleReseller($builder)
    {
        /** @var OauthUser $user */
        $user = auth()->user();
        $role = $user->getConsoleRole();

        switch ($role) {
            case OauthUser::CONSOLE_PARTNER:
                if ($user->keyAccount->isDefaultAccount()) {
                    return $builder->where('reseller_id', $user->keyAccount->reseller_id);
                }

                return $builder->where('reseller_id', 0); // Key accounts can't see reseller invoices
            default:
                return;
        }
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $builder
     * @return \Illuminate\Database\Eloquent\Builder|null
     */
    public function scopeResellerSite($builder)
    {
        return $builder->whereHas('site', fn (Builder $query) => $query->whereNotNull('reseller_id'));
    }

    /**
     * @param string $type
     * @return int
     */
    public static function generateNumber(Invoice $invoice): int|string
    {
        if ($invoice->issuer_company_id == self::DEFAULT_ISSUER_COMPANY_ID) {
            $lastInvoice = static::whereNotNull('number')
                ->where('issuer_company_id', $invoice->issuer_company_id)
                ->where('type', $invoice->type)
                ->orderBy('id', 'desc')
                ->first(['number']);

            return $lastInvoice ? (int)$lastInvoice->number + 1 : 1;
        }

        $prefix = $invoice->type == self::CREDIT_NOTE ? 'GU-' : 'RE-';
        while (true) {
            $number = $prefix . now()->format('ym') . '-' . random_int(10000, 99999);
            $check = static::where('number', $number)
                ->where('issuer_company_id', $invoice->issuer_company_id)
                ->where('type', $invoice->type)
                ->first(['number']);

            if (is_null($check)) {
                break;
            }
        }

        return $number;
    }

    /**
     * @param Builder $query
     * @param $user_id
     * @return mixed
     */
    public function scopeOwner($query, $user_id)
    {
        return $query->where('user_id', $user_id);
    }

    /**
     * @param Builder $query
     * @param $site_id
     * @return mixed
     */
    public function scopeForSite($query, $site_id)
    {
        return $query->where('site_id', $site_id);
    }

    public function issuerCompany()
    {
        return $this->belongsTo(IssuerCompany::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function transaction()
    {
        return $this->hasOne(Transaction::class, 'invoice_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function subscription()
    {
        return $this->belongsTo(SiteSubscription::class, 'subscription_id', 'unique_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function items()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function recipient()
    {
        return $this->belongsTo(UserSiteInvoicing::class, 'recipient_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function site()
    {
        return $this->belongsTo(UsersSites::class)->withTrashed();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function reseller()
    {
        return $this->belongsTo(Reseller::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function issuer()
    {
        return $this->belongsTo(OauthUser::class, 'issuer_id', 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function discount()
    {
        return $this->belongsTo(Discount::class);
    }

    public function ltaContract()
    {
        return $this->belongsTo(LtaContract::class, 'lta_contract_id');
    }

    /**
     * @param null $resellerCurrency
     * @return int
     */
    public function calculateResellerCommission($resellerCurrency): int|float
    {
        $amount = 0;
        if ($this->canceled || !$this->site->reseller_id) {
            return $amount;
        }

        foreach ($this->items as $item) {
            if ($percent = $this->site->reseller->getCommissionPercent($item->model_type)) {
                $totalAmount = CloudcartCurrency::convert($item->total_excluding_vat, $resellerCurrency, $item->currency);
                $amount += percent_of_number($totalAmount, $percent);
            }
        }

        return $amount;
    }

    protected function shouldCreateResellerPayoutItems(): bool
    {
        //        if ($this->issuer_id) { // the invoice is from a CloudCart employee
        //            return false;
        //        }

        if (!$this->site->reseller_id || !$this->site->reseller->active) {
            return false;
        }

        /** @link https://cloudcartad.atlassian.net/browse/GATE-141 */
        if ($this->site->reseller->level === Reseller::LEVEL_1) {
            if ($this->site_first_invoice && $this->site->created_at < now()->subDays(60)) {
                return false;
            }

            if ($this->site->created_at < now()->subMonths(18)) {
                return false;
            }

            /** @link https://cloudcartad.atlassian.net/browse/GATE-229 */
            /** @var InvoiceItem $invoiceModel */
            $invoiceModel = $this->items->first();
            if ($invoiceModel->model_type == 'plan_details' && $this->site->created_at < now()->subMonths(12)) {
                return false;
            }
        }

        /** @link https://cloudcartad.atlassian.net/browse/GATE-143 */
        if ($this->site->reseller->level === Reseller::LEVEL_2) {
            /** Logic for level2 checked, see: @link https://cloudcartad.atlassian.net/browse/GATE-226 */
            if ($this->transaction && $this->transaction->offer_id && $this->transaction->offer->reseller_id) {
                return true;
            }

            return false;
        }

        return true;
    }

    public function createResellerPayoutItems(): void
    {
        if (!$this->shouldCreateResellerPayoutItems()) {
            return;
        }

        //        dd($this);
        foreach ($this->items as $item) {
            // disable reseller payouts for ecosystem services
            if ($item->expert_service_id) {
                continue;
            }

            $salePrice = $item->total_excluding_vat;
            $discountAmount = $item->discount_amount;
            if ($discountAmount && $item::getActualClassNameForMorph($item->model_type) == PlanDetails::class) {
                if ($item->model->original_price) {
                    $periodDiscount = $item->model->original_price - $item->model->price;
                    $discountAmount -= $periodDiscount;
                }
            }

            $originalPrice = $salePrice + $discountAmount;
            //            $payoutPrice = $salePrice - $discountAmount;
            $payoutPrice = $salePrice;
            $percent = $this->site->reseller->getCommissionPercent($item->model_type);

            if ($discountAmount && $this->site->reseller->level > Reseller::LEVEL_1) {
                $percent -= round(
                    $discountAmount /
                    $originalPrice * 100
                );
            }

            if ($percent <= 0) {
                continue;
            }

            $payoutAmount = percent_of_number($payoutPrice, $percent);

            $attributes = [
                'reseller_id' => $this->site->reseller_id,
                'site_id' => $this->site->getKey(),
                'invoice_id' => $this->getKey(),
                'invoice_number' => $this->number,
                'invoice_item_id' => $item->getKey(),
                'subscription_id' => $this->subscription ? $this->subscription->getKey() : null,
                'transaction_id' => $this->transaction ? $this->transaction->getKey() : null,
                'model_id' => $item->model_id,
                'model_type' => $item->model_type,
                'sale_price' => $salePrice,
                'original_price' => $originalPrice,
                'discount_amount' => (int)$discountAmount,
                'payout_price' => $payoutPrice,
                'payout_amount' => $payoutAmount,
                'currency' => 'EUR',
                'percent' => $percent,
            ];

            ResellerPayoutItem::create($attributes);

            $this->update([
                'scope_reseller_id' => $this->site->reseller_id,
                'scope_key_account_id' => $this->site->key_account_id,
            ]);
        }

    }

    public function createPayoutItemsFromExpertJob(): void
    {
        if ($this->invoice_id) {
            // Do not process invoice credit note
            return;
        }

        $job = $this->subscription->expertServiceJob;

        if (!$job || !$job->expertService->active) {
            return;
        }

        foreach ($this->items as $item) {
            if ($item::getActualClassNameForMorph($item->model_type) != Service::class ||
                $item->model_id != $job->expertService->service_id
            ) {
                continue;
            }

            $attributes = [
                'expert_service_job_id' => $job->getKey(),
                'expert_service_id' => $job->expert_service_id,
                'reseller_id' => $job->expert_id,
                'site_id' => $this->site->getKey(),
                'invoice_id' => $this->getKey(),
                'invoice_number' => $this->number,
                'invoice_item_id' => $item->getKey(),
                'subscription_id' => $this->subscription ? $this->subscription->getKey() : null,
                'transaction_id' => $this->transaction ? $this->transaction->getKey() : null,
                'model_id' => $item->model_id,
                'model_type' => $item->model_type,
                'sale_price' => $job->payout_price,
                'original_price' => $job->payout_price,
                'discount_amount' => 0,
                'payout_price' => $job->payout_price,
                'payout_amount' => $job->payout_amount,
                'currency' => 'EUR',
                'percent' => 100 - $job->commission,
            ];

            ResellerPayoutItem::create($attributes);

            $item->update([
                'expert_service_job_id' => $job->getKey(),
                'expert_service_id' => $job->expert_service_id,
            ]);
            //            $this->update([
            //                'scope_reseller_id' => $this->site->reseller_id,
            //                'scope_key_account_id' => $this->site->key_account_id,
            //            ]);
        }

    }

    public function createExpertServiceJobs(): void
    {
        if ($this->invoice_id) {
            // Do not process invoice credit note
            return;
        }

        foreach ($this->items as $item) {
            /** @var InvoiceItem $item */
            if ($this->subscription->expertServiceJob) {
                $this->createPayoutItemsFromExpertJob();
                continue;
            }

            if (!$item->expert_service_id || !$item->expertService) {
                continue;
            }

            $salePrice = $item->total_excluding_vat;
            $payoutPrice = $item->expertService->price;
            $percent = 100 - $item->expertService->commission;
            $payoutAmount = percent_of_number($payoutPrice, $percent);

            $attributes = [
                'expert_service_id' => $item->expertService->getKey(),
                'service_id' => $item->expertService->service_id,
                'expert_id' => $item->expertService->expert_id,
                'site_id' => $this->site->getKey(),
                'invoice_id' => $this->getKey(),
                'invoice_item_id' => $item->getKey(),
                'subscription_id' => $this->subscription ? $this->subscription->getKey() : null,
                'transaction_id' => $this->transaction ? $this->transaction->getKey() : null,
                'sale_price' => $salePrice,
//                'original_price' => $item->expertService->price,
                'execution_time' => $item->expertService->execution_time,
                'payout_price' => $payoutPrice,
                'payout_amount' => $payoutAmount,
                'commission' => $item->expertService->commission,
                'status' => ExpertServiceJob::STATUS_PENDING,
            ];

            $job = ExpertServiceJob::create($attributes);

            ElasticEmail::changeAccount('elastic_email_sub');
            \Illuminate\Support\Facades\Mail::to($item->expertService->expert->keyAccount)
                ->send(new ExpertNewServiceJob($item->expertService->expert->keyAccount, $job));

            //            $this->update([
            //                'scope_reseller_id' => $this->site->reseller_id,
            //                'scope_key_account_id' => $this->site->key_account_id,
            //            ]);
        }

    }

    /**
     * @return string
     * @throws FileNotFoundException
     * @throws \Mpdf\MpdfException
     * @throws \Throwable
     */
    public function toPdf()
    {
        $data = $this->toInvoiceArray();

        $mpdf = new Mpdf(['utf8', 'A4', '', '', 20, 20, 40, 20, 10, 10]);

        $mpdf->SetProtection(['print']);
        $mpdf->SetTitle(__("invoice.title_invoice"));
        $mpdf->SetAuthor(__("invoice.author"));

        $mpdf->showWatermarkText = true;
        $mpdf->watermark_font = 'Arial';
        $mpdf->watermarkTextAlpha = 0.1;

        $mpdf->SetDisplayMode('fullpage');
        //        $mpdf->SetWatermarkText('yes' === trans($this->approved ? 'invoice.paid' : 'invoice.not_paid'));
        //        $mpdf->SetDirectionality(\Localization::direction());

        if ('console' == app_namespace()) {
            $view = view('invoice', $data);
        } else {
            $view = \Illuminate\Support\Facades\View::make('invoice', $data);
        }

        $mpdf->WriteHTML($view->render());

        return $mpdf->Output('', 'S');
    }

    /**
     * @return array
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     */
    public function toInvoiceArray(): array
    {
        if ($this->issuer_company_id == self::DE_ISSUER_COMPANY_ID) {
            app()->setLocale('de');
        }

        $invoice_data = array_merge($this->recipient->toArray(), [
            'customer_id' => $this->user ? $this->user->unique_id : null,
            'issuer_company_id' => $this->issuer_company_id,
        ]);

        $total = $this->amount;
        $vat = $this->amount_vat;
        $subtotal = $this->amount_excluding_vat;
        $items = clone $this->items;

        if ($this->discount_amount) {
            $items->push(new InvoiceItem([
                'description' => __('invoice.pricing.discount') . ($this->discount ? ' ' . $this->discount->name : ''),
                'price' => $this->discount_amount * -1,
                'quantity' => 1,
                'total_excluding_vat' => $this->discount_amount * -1,
            ]));
        }

        $items->map(function ($item) {
            /** @var InvoiceItem $item */
            $price = $item->original_price;
            $item->price_formatted = Format::money($price, $this->currency);
            $total = $item->original_total;
            $item->total_formatted = Format::money($total, $this->currency);

            return $item;
        });

        try {
            $raw = \File::get(storage_path('logo_invoice.png'));
        } catch (FileNotFoundException) {
            $raw = \File::get(base_path('storage/logo_invoice.png'));
        }

        $relatedInvoice = [];
        if ($this->invoice_id) {
            $relatedInvoice = [
                'credit_number' => $this->invoice_number,
                'invoice_number' => $this->invoice->invoice_number,
                'tax_date' => $this->invoice->created_at->format(trans('common.format.date')),
            ];
        }

        $this->setAppends([
            'invoice_number',
            'n18_audit_qr_image_svg_html',
            'payment_reference',
        ]);

        $data = array_merge($this->toArray(), [
//            'invoice_number' => $this->invoice_number,
            'invoice_date' => $this->created_at->format(trans('common.format.date')),
            'tax_date' => $this->created_at->format(trans('common.format.date')),
            'customer' => $invoice_data['name'],
            'subtotal' => Format::money($subtotal, $this->currency),
            'vat' => Format::money($vat, $this->currency),
            'amount' => Format::money($subtotal, $this->currency),
            'total' => Format::money($total, $this->currency),
            'lang' => $this->recipient->lang,
            'status_type' => $this->canceled ? 'canceled' : 'original',
            'recipient' => $invoice_data,
            'issuer' => $this->issuer_name ?: $this->issuer_data['issuer'],
            'logo' => base64_encode($raw),
            'items' => $items,
            'issuerData' => $this->issuer_data,
            'contract_number' => $this->lta_contract_id ? $this->ltaContract->unique_id : null,
        ], $relatedInvoice);

        if ($this->issuer_company_id == self::DEFAULT_ISSUER_COMPANY_ID &&
            $this->recipient->country == 'BG' &&
            $this->currency != 'BGN'
        ) {
            $currency = 'BGN';
            $subtotal = CloudcartCurrency::convert($subtotal, $currency, $this->currency);
            $vat = CloudcartCurrency::convert($vat, $currency, $this->currency);
            $total = CloudcartCurrency::convert($total, $currency, $this->currency);

            // 1 stotinka fix
            //            if (($subtotal + $vat + 1) == $total || ($subtotal + $vat - 1) == $total) {
            //                $total = $subtotal + $vat;
            //            }

            $data['local_currency'] = [
                'rate' => '1 ' . $this->currency . ' = ' . CloudcartCurrency::rate($currency, $this->currency) . ' ' . $currency,
                'subtotal' => Format::money($subtotal, $currency),
                'vat' => Format::money($vat, $currency),
                'total' => Format::money($total, $currency),
            ];
        }

        return $data;
    }

    /**
     * @return string
     * @throws FileNotFoundException
     * @throws \Throwable
     */
    public function toHtml()
    {
        $data = $this->toInvoiceArray();

        if ($this->issuer_company_id == self::DEFAULT_ISSUER_COMPANY_ID) {
            return view('invoice-html', $data)->render();
        }

        return view('invoice-html-de', $data)->render();
    }

    /**
     * @param Model|InvoiceItemContract $model
     * @param array $params
     * @return InvoiceItem
     * @throws \Exception
     */
    public function createItem(InvoiceItemContract $model, array $params = [])
    {
        if (!array_key_exists('description', $params) || !array_key_exists('billing_cycle', $params)) {
            throw new \Exception('description & billing_cycle params are required');
        }

        $vat_rate = $this->include_vat ? $this->site->taxPercentage() : 0;
        $quantity = $params['quantity'] ?? 1;
        $amount = $params['amount'] ?? $model->price;
        $total_excluding_vat = $quantity * $amount;
        $total_vat = $total_excluding_vat * ($vat_rate / 100);

        $attributes = [
            'invoice_id' => $this->getKey(),
            'model_id' => $model->getKey(),
            'model_type' => $model->getMorphClass(),
            'description' => $params['description'],
            'quantity' => $quantity,
            'price' => $amount,
            'total' => $total_excluding_vat + $total_vat,
            'currency' => $this->currency,
            'billing_cycle' => $params['billing_cycle'],
            'reseller_credit_id' => $params['reseller_credit_id'] ?? null,
            'discount_amount' => $params['discount_amount'] ?? null,
            'expert_service_id' => $params['expert_service_id'] ?? null,
            'vat_rate' => $vat_rate,
            'total_excluding_vat' => $total_excluding_vat,
            'total_vat' => $total_vat,
        ];

        return InvoiceItem::create($attributes);
    }

    /**
     * @param OauthUser|Authenticatable $user
     * @return $this
     */
    public function setIssuer(OauthUser $user): static
    {
        $this->issuer_id = $user->user_id;
        $this->issuer_name = $user->name;

        return $this;
    }

    /**
     * @return array
     */
    public static function getCloudCartInvoiceDetails(): array
    {
        $issuerCompany = IssuerCompany::find(self::DEFAULT_ISSUER_COMPANY_ID);

        return [
            'company' => $issuerCompany->company,
            'country' => $issuerCompany->country_name,
            'address' => $issuerCompany->address,
            'eik' => $issuerCompany->company_id,
            'vat' => $issuerCompany->vat_id,
            'responsible' => $issuerCompany->contact_name,
        ];
    }

    public function syncTotals(): void
    {
        $total_excluding_vat = $this->items->sum('total_excluding_vat');
        $total_vat = $this->items->sum('total_vat');
        $discount_amount = $this->items->sum('discount_amount') ?? null;

        $this->update([
            'discount_amount' => $discount_amount,
            'amount_excluding_vat' => $total_excluding_vat,
            'amount_vat' => $total_vat,
            'amount' => $total_excluding_vat + $total_vat,
        ]);
    }

    /**
     * @return Transaction
     * @throws \Stripe\Exception\ApiErrorException|\Exception
     */
    public function refund()
    {
        if (!$this->transaction) {
            throw new \Exception('Transaction not found');
        }

        return $this->transaction->refund();
    }

    /**
     * @return Transaction
     * @throws \Stripe\Exception\ApiErrorException
     */
    public function void()
    {
        if (!$this->transaction && $this->payment_method == self::BANK) {
            // Void bank transfer
            return new Transaction([
                'approved' => 1,
            ]);
        }

        if (!$this->transaction) {
            throw new \Exception('Transaction not found');
        }

        return $this->transaction->void();
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getIssuerDataAttribute($value): array
    {
        return [
            'company' => $this->issuerCompany->company,
            'country' => $this->issuerCompany->country_name,
            'address' => $this->issuerCompany->address,
            'eik' => $this->issuerCompany->company_id,
            'vat' => $this->issuerCompany->vat_id,
            'responsible' => $this->issuerCompany->contact_name,
            'issuer' => $this->issuerCompany->contact_name,
            'bank' => $this->issuerCompany->bank,
            'vat_rate' => $this->issuerCompany->vat_rate,
        ];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getAmountFormattedAttribute($value)
    {
        return Format::money($this->amount, $this->currency);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDateFormattedAttribute($value)
    {
        return $this->created_at->format('d.m.Y');
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getInvoiceNumberAttribute($value)
    {
        if ($this->issuer_company_id != self::DEFAULT_ISSUER_COMPANY_ID) {
            return $this->number;
        }
        return $this->type == Invoice::CREDIT_NOTE ?
            Format::invoiceCreditNumber($this->number) :
            Format::invoiceNumber($this->number);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getRecipientTypeAttribute($value): string
    {
        return $this->reseller_id ? 'reseller' : 'site';
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getN18AuditQrStringAttribute($value): string
    {
        $currency = 'BGN';
        $total = Format::moneyInput(CloudcartCurrency::convert($this->amount, $currency, $this->currency), $currency);
        return sprintf(
            '%s*%s*%s*%s*%s*%s',
            'RF0000461',
            $this->invoice_number,
            $this->transaction ? $this->transaction->reference_id : '',
            $this->created_at->format('Y-m-d'),
            $this->created_at->format('H:i:s'),
            $total
        );
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getN18AuditQrImagePngAttribute($value)
    {
        return DNS2D::getBarcodePng($this->n18_audit_qr_string, 'QRCODE');
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getN18AuditQrImageSvgAttribute($value)
    {
        return DNS2D::getBarcodeSvg($this->n18_audit_qr_string, 'QRCODE');
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getN18AuditQrImageSvgHtmlAttribute($value)
    {
        if ($this->created_at->year < 2021) {
            return '';
        }
        return str_replace('<?xml version="1.0" standalone="no"?>', '', $this->n18_audit_qr_image_svg);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPaymentReferenceAttribute($value)
    {
        return $this->transaction ? $this->transaction->reference_id : null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getAmountWithoutDiscountAttribute($value): float|int|array
    {
        return $this->amount_excluding_vat + $this->discount_amount;
    }
}
