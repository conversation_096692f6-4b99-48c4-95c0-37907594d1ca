<?php

declare(strict_types=1);

namespace App\Models\Gate;

use App\Models\Oauth\User as OauthUser;
use Carbon\Carbon;

/**
 * Class Todo
 * @property null|\App\Models\Gate\Note $note
 * @property string $action_type
 * @property int $user_id
 * @property int $site_id
 * @property int $cc_user_id
 * @property User $user
 * @property UsersSites $site
 * @property int $status
 * @property Carbon $date
 * @property string $action_type_formatted
 *
 * @package App\Models
 */
class Todo extends BaseModel
{
    public const STATUS_PENDING = 1;

    public const STATUS_COMPLETED = 2;

    public const STATUS_CANCELED = 3;

    public const ACTION_MAKE_CALL = 'make_call';

    protected $connection = 'gate';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'cc_gate.todos';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'action_type_formatted',
        'status_text',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id')->withTrashed();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function site()
    {
        return $this->belongsTo(UsersSites::class, 'site_id')->withTrashed();
    }

    /**
     * CloudCart employee user id
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|OauthUser
     */
    public function ccUser()
    {
        return $this->belongsTo(OauthUser::class, 'cc_user_id', 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphOne
     */
    public function note()
    {
        return $this->morphOne(Note::class, 'model');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function model()
    {
        return $this->morphTo();
    }

    protected function casts(): array
    {
        return ['date' => 'datetime'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getActionTypeFormattedAttribute($value)
    {
        return match ($this->action_type) {
            'make_call' => 'Make a call (Follow Up)',
            default => $this->action_type,
        };
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStatusTextAttribute($value): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_COMPLETED => 'Completed',
            default => 'Canceled',
        };
    }
}
