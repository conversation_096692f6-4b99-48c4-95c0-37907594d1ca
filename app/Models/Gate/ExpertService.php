<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 6/14/2018
 * Time: 1:12 PM
 */

namespace App\Models\Gate;

use App\Contracts\InvoiceItemContract;
use App\Helper\Format;
use App\Models\Oauth\User as OauthUser;
use App\Traits\Invoiceable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Spatie\Translatable\HasTranslations;

/**
 * @property integer $expert_id
 * @property integer $price
 * @property integer $billing_cycle
 * @property string $billing_period
 * @property null|string $billing_period_text
 * @property int $price_without_vat
 * @property float $price_input
 * @property string $description
 * @property string $description_html
 * @property string $description_translated
 * @property \Illuminate\Support\Collection $operation_countries_codes
 * @property \Illuminate\Support\Collection $language_codes
 * @property Collection|ExpertServiceOperationCountry[] $operationCountries
 * @property Collection|ExpertServiceLanguage[] $languages
 * @property int $service_id
 * @property Service $service
 * @property Reseller $expert
 * @property bool $active
 * @method static self|Builder active
 * @method static self|Builder console()
 */
class ExpertService extends BaseModel implements InvoiceItemContract
{
    use HasTranslations;
    use Invoiceable;

    public $translatable = [
        'description',
    ];

    /**
     * @var string
     */
    protected $connection = 'gate';

    /**
     * @var string
     */
    protected $table = 'cc_gate.expert_services';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'description_translated',
        'price_input',
        'price_formatted',
        'billing_period',
    ];

    /**
     * @param Request $request
     * @return void
     * @throws ValidationException
     */
    public static function checkPricesOverride(Request $request): void
    {
        $service = ExpertService::findOrFail($request->input('expert_service_id'));
        $price = $request->input('price');
        $maxPrice = $service->price / 100;
        if ($price < $maxPrice) {
            throw ValidationException::withMessages(['price' => sprintf('Max discounted price is %s.', $maxPrice)]);
        }

        if ($service->billing_cycle) {
            $price = $request->input('next_billing_amount');
            if ($price < $maxPrice) {
                throw ValidationException::withMessages(['next_billing_amount' => sprintf('Max discounted price is %s.', $maxPrice)]);
            }
        }
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder|null
     */
    public function scopeConsole($query)
    {
        /** @var OauthUser $user */
        $user = auth()->user();
        $role = $user->getConsoleRole();

        return match ($role) {
            OauthUser::CONSOLE_PARTNER => $query->where('expert_id', $user->keyAccount->reseller_id),
            OauthUser::CONSOLE_CLOUD_CART => null,
            default => $query->where('id', 0),
        };
    }

    /**
     * @param Builder $query
     *
     * @return mixed
     */
    public function scopeActive($query)
    {
        return $query->where('active', 1);
    }

    public function expert()
    {
        return $this->belongsTo(Reseller::class, 'expert_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function category()
    {
        return $this->belongsTo(ServiceGroup::class, 'category_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function service()
    {
        return $this->belongsTo(Service::class, 'service_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function operationCountries()
    {
        return $this->hasMany(ExpertServiceOperationCountry::class, 'expert_service_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function languages()
    {
        return $this->hasMany(ExpertServiceLanguage::class, 'expert_service_id');
    }

    /**
     * @param array $countryCodes
     * @return $this
     */
    public function syncOperationCountries(array $countryCodes): static
    {
        $this->operationCountries()->delete();
        if (empty($countryCodes)) {
            return $this;
        }

        $countries = [];
        foreach ($countryCodes as $country) {
            $countries[] = new ExpertServiceOperationCountry(['country' => $country]);
        }

        $this->operationCountries()->saveMany($countries);
        $this->load('operationCountries');

        return $this;
    }

    /**
     * @param array $languageCodes
     * @return $this
     */
    public function syncLanguages(array $languageCodes): static
    {
        $this->languages()->delete();
        if (empty($languageCodes)) {
            return $this;
        }

        $languages = [];
        foreach ($languageCodes as $code) {
            $languages[] = new ExpertServiceLanguage(['lang' => $code]);
        }

        $this->languages()->saveMany($languages);
        $this->load('languages');

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function invoiceItemDescription($params = [])
    {
        return $this->service->name_translated;
    }

    /**
     * @inheritDoc
     */
    public function getGoogleTagManagerData(): array
    {
        return [
            'name' => $this->service->getTranslation('name', 'en'),
            'id' => $this->getKey(),
            'variant' => $this->billing_period,
            'price' => $this->price_input,
            'quantity' => 1,
            'brand' => 'Ecosystem',
            'category' => 'Services',
        ];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getOperationCountriesCodesAttribute($value)
    {
        return $this->operationCountries->pluck('country');
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getLanguageCodesAttribute($value)
    {
        return $this->languages->pluck('lang');
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDescriptionTranslatedAttribute($value): mixed
    {
        return $this->translate('description', app()->getLocale());
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDescriptionHtmlAttribute($value): string
    {
        return nl2br($this->description);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceInputAttribute($value): string|int
    {
        $currency = config('billing.currency.default');
        return Format::moneyInput($this->price, $currency);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceFormattedAttribute($value): string
    {
        return Format::money($this->price, true, $this->currency);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceWithoutVatAttribute($value)
    {
        return $this->price;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPriceVatInputAttribute($value): string|int
    {
        $currency = config('billing.currency.default');
        $vat = $this->price - $this->price_without_vat;
        return Format::moneyInput($vat, $currency);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getBillingPeriodTextAttribute($value)
    {
        switch ($this->billing_cycle) {
            case null:
                return __('plan.period.once');
            case 1:
                return __('plan.period.month');
            case 12:
                return __('plan.period.year');
            case 24:
                return __('plan.period.2years');
            default:
                return trans_choice('plan.period.period', $this->billing_cycle, ['period' => $this->billing_cycle]);
        }
    }
}
