<?php

declare(strict_types=1);

namespace App\Models\Gate;

use App\Helper\Plan;
use App\Helper\Translation\Translator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Modules\CcSegments\Models\SiteEventLog;

/**
 * @property string $subject
 * @property string $body_html
 * @property string $vars_type
 * @property string $name
 * @property array $body_json
 */
class EmailTemplate extends BaseModel
{
    /**
     * @var string
     */
    protected $connection = 'gate';

    /**
     * @var string
     */
    protected $table = 'email_templates';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    protected $appends = [
    ];

    /**
     * @param \Modules\CcSegments\Models\SiteEventLog $event
     * @return array
     */
    public static function getVarsForEvent(SiteEventLog $event): array
    {
        if ($event->event_type === SiteEventLog::TYPE_SITE_ALERT) {
            return self::getVarsForModel($event);
        } elseif ($event->model) {
            return self::getVarsForModel($event->model);
        }

        return Arr::dot($event->data, '$' . $event->event_type . '.');
    }

    /**
     * @param \Illuminate\Database\Eloquent\Model $model
     * @return array
     */
    public static function getVarsForModel(Model $model): array
    {
        switch ($model::class) {
            case User::class:
                return Arr::dot(Arr::only($model->toArray(), [
                    "id",
                    "unique_id",
                    "first_name",
                    "last_name",
                    "phone",
                    "email",
                    "country",
                    "city",
                    "zip",
                    "lang",
                    "name",
                ]), '$user.');
            case UsersSites::class:
                return Arr::dot(Arr::only($model->toArray(), [
                    "id",
                    "plan",
                    "period",
                    "status",
                    "next_billing_date",
                    "url",
                    "main_host",
                    "primary_host"
                ]), '$site.');
            case Transaction::class:
                /** @var \App\Models\Gate\Transaction $model */
                $model->loadMissing('details');
                $arr = explode(' | ', $model->description);
                foreach ($arr as $key => $item) {
                    $model->{'desc' . $key} = $item;
                }

                return Arr::dot(Arr::only($model->toArray(), [
                    "id",
                    "payment_method",
                    "reference_id",
                    "description",
                    "desc0",
                    "desc1",
                    "approved",
                    "subscription_id",
                    "created_at",
                    "amount_formatted",
                    "status_name",
                    "details",
                ]), '$transaction.');
            case SiteSubscription::class:
                return Arr::dot(Arr::only($model->toArray(), [
                    "id",
                    "unique_id",
                    "model_id",
                    "mapping",
                    "last_try_at",
                    "failed_attempts",
                    "billing_cycle",
                    "value",
                    "price_formatted",
                    "billing_period_formatted",
                    "next_billing_date_formatted",
                    "next_billing_amount_formatted",
                    "name",
                    "type_name",
                    "status_name",
                ]), '$subscription.');
            case SiteEventLog::class:
                return Arr::dot($model->data ?: [], '$alert.');
            case Offer::class:
                /** @var \App\Models\Gate\Offer $model */
                $model->email_content = str_replace('<p>', '<p style="margin-top: 16px; margin-bottom: 16px">', $model->email_content);
                return Arr::dot(Arr::only($model->toArray(), [
                    'email_content',
                    'purchase_link',
                ]), '$offer.');
            default:
                return [];
        }
    }

    /**
     * @param $type
     * @return array
     * @throws \App\Exceptions\Error
     * @throws \App\Exceptions\Fault
     */
    public static function getVarsForEventType($type): array
    {
        $site = UsersSites::latest()->first();

        switch ($type) {
            case SiteEventLog::TYPE_RESELLER_CHANGE:
                $item = Reseller::latest()->first();
                $items = self::getVarsForModel($item);
                break;
            case SiteEventLog::TYPE_TRANSACTION_SUCCESS:
            case SiteEventLog::TYPE_TRANSACTION_FAIL:
                $item = Transaction::failed()->latest()->first();
                $items = self::getVarsForModel($item);
                break;
            case SiteEventLog::TYPE_SUBSCRIPTION_NEW:
            case SiteEventLog::TYPE_SUBSCRIPTION_EXTEND:
            case SiteEventLog::TYPE_SUBSCRIPTION_TRIAL:
            case SiteEventLog::TYPE_SUBSCRIPTION_UPCOMING_PAYMENT:
            case SiteEventLog::TYPE_SUBSCRIPTION_EXPIRE:
            case SiteEventLog::TYPE_SUBSCRIPTION_CANCEL:
            case SiteEventLog::TYPE_SUBSCRIPTION_RENEW_FAIL:
            case SiteEventLog::TYPE_SUBSCRIPTION_RENEW_SUCCESS:
                $item = SiteSubscription::whereNotNull('next_billing_date')->latest()->first();
                $items = self::getVarsForModel($item);
                break;
            case SiteEventLog::TYPE_SITE_ALERT:
                $event = SiteEventLog::where('event_type', SiteEventLog::TYPE_SITE_ALERT)->latest()->first();
                $items = self::getVarsForModel($event);
                break;
            case SiteEventLog::TYPE_SITE_EXPIRED:
                $site->routerSite->bootDB();
                /** @var \Illuminate\Translation\Translator $translator */
                $translator = app('translator');
                $translator->addPath(base_path('resources/lang'));
                $items = self::getVarsForEvent(new SiteEventLog([
                    'site_id' => $site->getKey(),
                    'event_type' => SiteEventLog::TYPE_SITE_EXPIRED,
                    'data' => ['reason' => Plan::getExpireNotification('plan_not_paid', [])],
                ]));
                break;
            case SiteEventLog::TYPE_CC_USER_LOGIN_REQUEST:
                $items = self::getVarsForEvent(new SiteEventLog([
                    'site_id' => $site->getKey(),
                    'event_type' => SiteEventLog::TYPE_CC_USER_LOGIN_REQUEST,
                    'data' => [
                        'cc_user_name' => 'CC Employee Name',
                        'cc_user_email' => '<EMAIL>',
                        'confirm' => $site->url . '/admin/login-request/accept/' . uniqid(),
                        'reject' => $site->url . '/admin/login-request/reject/' . uniqid(),
                    ],
                ]));
                break;
            case 'confirm_email':
                $items = self::getVarsForEvent(new SiteEventLog([
                    'event_type' => 'confirm_email',
                    'data' => ['link' => 'https://' . config('url.gate') . '/confirm-email/' . uniqid()],
                ]));
                break;
            case 'offer':
                $item = Offer::whereNotNull('email_content')->latest()->first();
                $items = self::getVarsForModel($item);
                break;
            case 'global':
            default:
                $items = [];
        }

        $items += self::getVarsForModel($site) + self::getVarsForModel($site->user);

        return $items;
    }

    /**
     * @param array $vars
     * @return string
     */
    public function replaceVars(array $vars = []): string
    {
        $html = preg_replace_callback(
            '~(' . implode('|', array_map('preg_quote', array_keys($vars))) . ')~',
            fn ($match) => value($vars[$match[0]] ?? null),
            $this->body_html
        );

        return preg_replace('/\$\S+/', '', $html);
    }

    /**
     * @param array $vars
     * @return string
     */
    public function replaceSubjectVars(array $vars = []): string
    {
        $html = preg_replace_callback(
            '~(' . implode('|', array_map('preg_quote', array_keys($vars))) . ')~',
            fn ($match) => value($vars[$match[0]] ?? null),
            $this->subject
        );

        return preg_replace('/\$\S+/', '', $html);
    }

    protected function casts(): array
    {
        return [
            'body_json' => 'json',
        ];
    }

}
