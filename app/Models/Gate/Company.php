<?php

declare(strict_types=1);

namespace App\Models\Gate;

use App\Models\Oauth\User as OauthUser;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Company
 * @property string $company_id
 * @property string $vat_id
 * @property string $lang
 * @property string $name
 * @property string $contact_name
 * @property string $country_name
 * @property string $country
 * @property string $address
 * @property int $default_user_id
 * @property User $user
 * @property bool $include_vat
 * @property Collection|User[] $users
 * @property Collection|UsersSites[] $sites
 *
 * @package App\Models
 */
class Company extends BaseModel
{
    protected $connection = 'gate';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'cc_gate.companies';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'country_name',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'default_user_id')->withTrashed();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(
            User::class,
            'cc_gate.company_users',
            'company_id',
            'user_id'
        )->withTrashed();
    }

    /**
     * CloudCart employee user id
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|OauthUser
     */
    public function ccUser()
    {
        return $this->belongsTo(OauthUser::class, 'cc_user_id', 'user_id');
    }

    /**
     * CloudCart employee user id
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|OauthUser
     */
    public function ccBsaUser()
    {
        return $this->belongsTo(OauthUser::class, 'cc_bsa_user_id', 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function sites(): HasMany
    {
        return $this->hasMany(UsersSites::class, 'company_id', 'id')->withTrashed();
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function getCountryNameAttribute($value)
    {
        return locale_get_display_region('-' . $this->country, $this->lang);
    }

}
