<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 24.10.2018 г.
 * Time: 13:00 ч.
 */

namespace App\Models\Log;

use App\Commands\DatabaseOutput;
use App\Models\Gate\Import;
use App\Models\Oauth\User;
use App\Traits\DbTimezone;
use Symfony\Component\Console\Application;
use Symfony\Component\Console\Formatter\OutputFormatter;
use Illuminate\Database\Eloquent\Model as Eloquent;

/**
 * @property integer $time
 * @property string $command
 * @property string $output
 * @property array $params
 * @property Import $import
 */
class ConsoleCommand extends Eloquent
{
    use DbTimezone;

    /**
     * @var string
     */
    protected $connection = 'logs-global';

    /**
     * @var string
     */
    protected $table = 'console_commands';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|User
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function import()
    {
        return $this->hasOne(Import::class, 'command_id');
    }

    /**
     * @param bool $closeOutput
     * @return $this
     */
    public function run($closeOutput = true): static
    {
        set_time_limit(0);
        //        if (function_exists('putenv')) {
        //            putenv("APP_RUNNING_IN_CONSOLE=true");
        //        }
        //        $_ENV['APP_RUNNING_IN_CONSOLE'] = 'true';
        //        $_SERVER['APP_RUNNING_IN_CONSOLE'] = 'true';

        if ($closeOutput) {
            @ini_set('zlib.output_compression', 0);
            @ini_set('implicit_flush', 1);
            ob_implicit_flush(true);
            $response = \Response::make([], 204);
            $response->send();
        }

        $output = new DatabaseOutput(
            DatabaseOutput::VERBOSITY_NORMAL,
            true,
            new OutputFormatter(true)
        );
        $output->setDbModel($this);

        try {
            $code = \Artisan::call($this->command, $this->params, $output);
            // save output to db
            $output->fetch();
        } catch (\Throwable $throwable) {
            $code = 1;
            $output->setLogInterval(0);
            (new Application())->renderThrowable($throwable, $output);
        }

        $this->update([
            'status' => $code,
        ]);

        return $this;
    }

    /**
     * The attributes that should be cast to native types.
     *
     * @return array
     */
    protected function casts(): array
    {
        return [
            'params' => 'json',
        ];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getOutputAttribute($value)
    {
        if (0 === mb_strpos((string) $this->attributes['output'], "\x1f" . "\x8b" . "\x08")) {
            return gzdecode($this->attributes['output']);
        }
        return $this->attributes['output'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setOutputAttribute($value): void
    {
        $this->attributes['output'] = gzencode((string) $value, 9);
    }

}
