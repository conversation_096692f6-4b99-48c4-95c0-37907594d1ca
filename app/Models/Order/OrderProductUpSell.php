<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.7.2018 г.
 * Time: 12:28 ч.
 */

namespace App\Models\Order;

use App\Models\Base\AbstractOrdersProductsUpSell;
use App\Models\Marketing\UpSell\UpSell;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class OrderProductUpSell extends AbstractOrdersProductsUpSell
{
    /**
     * @return HasOne
     */
    public function product(): HasOne
    {
        return $this->hasOne(OrderProduct::class, 'id', 'order_product_id');
    }

    /**
     * @return BelongsTo
     */
    public function up_sell(): BelongsTo
    {
        return $this->belongsTo(UpSell::class);
    }

}
