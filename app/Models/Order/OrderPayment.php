<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 18.7.2016 г.
 * Time: 09:38 ч.
 */

namespace App\Models\Order;

use App\Common\DateTimeFormat;
use App\Common\Media;
use App\Common\Status;
use App\Contracts\ApiModelContract;
use App\Helper\ArrayCache;
use App\Helper\Store\Contracts\AddressContract;
use App\Helper\Store\Contracts\Concerns\Item\Options\ItemOptionContract;
use App\Helper\Store\Contracts\ItemContract;
use App\Models\Gateway\PaymentProviders;
use App\Models\Gateway\Payments;
use App\Traits\Api;
use App\Traits\Crudling;
use App\Traits\DateTemplate;
use Exception;
use Illuminate\Support\Str;
use App\Events\PaymentSync;
use App\Models\Base\AbstractOrdersPayments;
use App\Helper\Format;
use App\Helper\FilterSearch\Order\FilterPayment;
use App\Facades\MaxMind;
use Money\Money;
use Throwable;

/**
 * Class OrderPayment
 *
 * @package App\Models\Order
 * @property Order $order
 * @property Payments $payment
 * @proprty float down_payment
 * @property PaymentProviders $payment_provider
 * @property string $provider_name
 * @property string|bool $allow_capture_authorization
 */
class OrderPayment extends AbstractOrdersPayments implements ApiModelContract
{
    use Crudling;
    use DateTemplate;
    use Api {
        Api::api as traitApi;
    }

    public const API_KEYS = [
        'id', 'provider', 'amount', 'provider_reference_id',
        'status', 'date_added', 'date_last_update', 'provider_name'
    ];

    protected $appends = [
        'amount_formatted', 'status_formatted', 'status_color',
        'date_added_formatted', 'provider_name', 'date_template',
        'cnt'
    ];

    public static $statuses_payments = [
        'authorized',
        'initiated',
        'requested',
        'pending',
        'held',
        'completed',
        'failed',
        'refunded',
        'voided',
        'cancelled',
        'timeouted',
        'chargebacked',
        'disputed'
    ];

    protected $data = [];

    #[\Override]
    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub

        static::creating(function (OrderPayment $payment): void {
            $payment->hash = $payment->generateHash();
        });
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|Order
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|Payments
     */
    public function payment()
    {
        return $this->belongsTo(Payments::class, 'payment_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo|PaymentProviders
     */
    public function payment_provider()
    {
        return $this->belongsTo(PaymentProviders::class, 'provider', 'provider');
    }

    /**
     * @param $type
     * @return bool
     */
    public function isType($type): bool
    {
        return $this->payment_provider && in_array($type, $this->payment_provider->type);
    }

    /**
     * @param $order_id
     *
     * @return \Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection
     */
    public static function getByOrderId($order_id)
    {
        return static::where('order_id', (int)$order_id)->get();
    }

    /**
     * @param $payment_server_id
     * @param $provider_reference_id
     * @param $status
     */
    public function sync($payment_server_id, $provider_reference_id, $status): void
    {
        if (is_array($provider_reference_id)) {
            $provider_reference_id = json_encode($provider_reference_id, JSON_PRETTY_PRINT);
        }

        $this->fill([
            'provider_reference_id' => $provider_reference_id,
            'payment_id' => $payment_server_id,
            'status' => $status,
        ]);

        if ($this->isDirty('status') && !in_array($this->status, [
                Payments::STATUS_INITIATED,
                Payments::STATUS_REQUESTED,
            ])
        ) {
            $this->save();
            // force reload order payments relation
            if ($this->relationLoaded('order') && $this->order->relationLoaded('payments')) {
                unset($this->order->payments);
            }

            event(new PaymentSync($this));
        } else {
            $this->save();
        }
    }

    /**
     * @param $order_id
     *
     * @return array
     */
    public static function getLastAndTotalAmount($order_id): array
    {
        $payments = static::getByOrderId($order_id);

        $payment_amount_total = $payments->where('status', 'completed')->sum('amount');

        $payment_amount_total_formatted = Format::money($payment_amount_total);

        $total['amount'] = $payment_amount_total;
        $total['amount_formatted'] = $payment_amount_total_formatted;

        $last_payment = $payments->last();

        return [$last_payment, $total];
    }

    /**
     * @param FilterPayment $filter
     *
     * @return int
     */
    public static function getCount(FilterPayment $filter)
    {
        [$where] = $filter->get();

        return static::whereRaw($where)->count();
    }

    /**
     * @param $payment_id
     * @param $customer_email
     *
     * @return string
     */
    public static function generatePayLink($payment_id, $customer_email)
    {
        $expire = \Carbon\Carbon::now()->timestamp + 3600;
        $params = [$customer_email, $payment_id, $expire];
        $server_secret = '~f~:x]d=xp9#DY';
        $hash = static::genereteSignedLink($params, $server_secret);

        //generating payment link here
        return \LinkerCp::siteFullLink(\Linker::orderPay([
            'e' => $customer_email,
            'p' => $payment_id,
            't' => $expire,
            'h' => $hash
        ]));
    }

    /**
     * @param bool $full
     * @return array
     */
    public function api($full = false)
    {
        $data = $this->traitApi($full);

        $data['amount'] = (float)Format::money($this->payment->amount ?? $this->amount, false);
        return $data;
    }

    /**
     * @param Order $order
     * @param float|null $executionTime
     * @return array
     * @throws Exception
     */
    public function paymentServerRequest(Order $order, ?float $executionTime = null): array
    {
        $invoice = [
            'items' => [],
            'subtotal' => moneyInput($order->price_subtotal, $order->getCurrency(), $order->getLanguage()),
            'total' => moneyInput($order->payment->amount, $order->getCurrency(), $order->getLanguage()),
        ];

        if (!is_null($executionTime)) {
            $invoice['executionTime'] = $executionTime;
        }

        // Products
        /** @var ItemContract $item */
        foreach ($order->getProducts() as $item) {
            $invoice['items'][] = [
                'id' => $item->getId(),
                'name' => $item->getName(),
                'quantity' => $item->getQuantity(),
                'amount' => moneyInput(round($item->getTotalPriceWithNonSingleOptionsAfterDiscounts() / $item->getQuantity()), $order->getCurrency(), $order->getLanguage()),
                'image' => $item->getImage('600x600')
            ];

            if ($item->hasSingleOptions()) {
                /** @var OrderProductOptions $option */
                foreach ($item->getSingleOptions() as $option) {
                    $invoice['items'][] = [
                        'id' => $option->getKey(),
                        'name' => sprintf(
                            '%s: %s',
                            $option->getName(),
                            $option->isFile() ? $option->getFile()->getName() : $option->getValueFormatted()
                        ),
                        'quantity' => 1,
                        'amount' => moneyInput($item->hasDiscounts() ? $option->getDiscountedPrice()->getPrice() : $option->getPrice()->getPrice(), $order->getLanguage()),
                        'image' => null
                    ];
                }
            }
        }

        $itemsSubTotal = collect($invoice['items'])->sum(fn (array $data) => $data['amount'] * $data['quantity']);

        if (floatval($invoice['subtotal']) != floatval($itemsSubTotal)) {
            $diff = floatval(moneyInput(($invoice['subtotal'] - $itemsSubTotal) * 100, $order->getCurrency(), $order->getLanguage()));
            foreach ($invoice['items'] as $itemKey => $itemData) {
                if (floatval($itemData['quantity']) == floatval(1) && $itemData['amount'] + $diff >= 0) {
                    $invoice['items'][$itemKey]['amount'] = $itemData['amount'] + $diff;
                    break;
                }
            }
        }

        $discounts = $order->totals
            ->whereIn('group', ['discount.before', 'discount.after']);
        foreach ($discounts as $discount) {
            /** @var OrderTotals $discount */
            $discountAmount = moneyInput(intval($discount->price) * -1, $order->getCurrency(), $order->getLanguage());
            $invoice['items'][] = [
                'name' => $discount->name,
                'quantity' => 1,
                'amount' => $discountAmount,
            ];

            $invoice['subtotal'] += floatval($discountAmount);
        }

        $invoice['shipping'] = moneyInput($order->totals->where('group', 'shipping')->sum(fn (OrderTotals $total) => $total->key == 'shipping' ? $total->price : $total->price * -1), $order->getCurrency(), $order->getLanguage());

        // Taxes
        $taxes = $order->totals
            ->whereIn('group', ['tax.before', 'tax.after'])
            ->where('use_for_total', 1);

        if ($taxes->isNotEmpty()) {
            $invoice['tax'] = moneyInput($taxes->sum(fn (OrderTotals $total) => $total->price), $order->getCurrency(), $order->getLanguage());
        }

        //digital without billing address
        $hide_billing_address = $order->isHideBillingAddress();
        // Address
        $billing_address = null;
        if (!$hide_billing_address) {
            $billing_address = $order->billingAddress;
            if (empty($billing_address->country_iso2) && $order->shippingAddress) {
                $billing_address = $order->shippingAddress;
            }
        }

        $request = [
            'site_id' => site('site_id'),
            'site_reference_id' => $this->id,
            'site_lang' => site('language'),
            'provider' => $this->provider,
            'country' => $billing_address->country_iso2 ?? null,
            'client_ip_country' => MaxMind::getCountryIso2(),
            'currency' => $order->getCurrency(),
            'client_ip' => sprintf("%u", ip2long(request()->ip())),
            'client_email' => $order->customer_email,
            'amount' => $invoice['total'],
            'invoice' => $invoice,
            'status' => $this->status,
        ];

        if ($billing_address) {
            $request['address'] = [
                'country' => $billing_address->getCountryName(),
                'state' => $billing_address->getStateName(),
                'city' => $billing_address->getCityName(),
                'street' => $billing_address->getStreetName(),
                'street_number' => $billing_address->getStreetNumber(),
                'address' => $billing_address->getAddressLine1(),
                'address_formatted' => $billing_address->getFormat(),
                'first_name' => $billing_address->getFirstName(),
                'last_name' => $billing_address->getLastName(),
                'phone' => $billing_address->getPhone(),
                'postal_code' => $billing_address->getPostCode(),
                'company' => $billing_address->isCompany()
            ];

            if ($request['address']['company']) {
                $request['address']['company_name'] = $billing_address->getCompanyName();
            }

            if ($billing_address->getCompanyVat()) {
                $request['address']['company_vat'] = $billing_address->getCompanyVat();
            }
        } else {
            $request['address'] = null;
        }

        if ($order->hasShippable() && $order->shippingAddress) {
            $request['shipping_address'] = [
                'country' => $order->shippingAddress->getCountryName(),
                'state' => $order->shippingAddress->getStateName(),
                'city' => $order->shippingAddress->getCityName(),
                'street' => $order->shippingAddress->getStreetName(),
                'street_number' => $order->shippingAddress->getStreetNumber(),
                'address' => $order->shippingAddress->getAddressLine1(),
                'address_formatted' => $order->shippingAddress->getFormat(),
                'first_name' => $order->shippingAddress->getFirstName(),
                'last_name' => $order->shippingAddress->getLastName(),
                'phone' => $order->shippingAddress->getPhone(),
                'postal_code' => $order->shippingAddress->getPostCode(),
                'company' => $order->shippingAddress->isCompany(),
            ];
            if ($request['shipping_address']['company']) {
                $request['shipping_address']['company_name'] = $order->shippingAddress->getCompanyName();
            }

            if ($order->shippingAddress->getCompanyVat()) {
                $request['shipping_address']['company_vat'] = $order->shippingAddress->getCompanyVat();
            }
        } else {
            $request['shipping_address'] = null;
        }

        return $request;
    }

    //    /**
    //     * @param Order $order
    //     *
    //     * @return array
    //     * @throws Throwable
    //     */
    //    public function paymentServerRequest(Order $order): array
    //    {
    //        $invoice = [
    //            'items' => [],
    //        ];
    //
    //        $totals = $order->getTotals();
    //        // Products
    //        /** @var ItemContract $item */
    //        foreach ($order->getProducts() as $item) {
    //            $invoice['items'][] = [
    //                'id' => $item->getId(),
    //                'name' => $item->getName(),
    //                'quantity' => $item->getQuantity(),
    //                'amount' => moneyInput(round($item->getTotalPriceWithNonSingleOptionsAfterDiscounts() / $item->getQuantity()), $order->getCurrency()),
    //                'image' => $item->getImage('600x600')
    //            ];
    //            if ($item->hasSingleOptions()) {
    //                /** @var ItemOptionContract $option */
    //                foreach ($item->getSingleOptions() as $option) {
    //                    $invoice['items'][] = [
    //                        'id' => $option->getKey(),
    //                        'name' => sprintf(
    //                            '%s: %s',
    //                            $option->getName(),
    //                            $option->isFile() ? $option->getFile()->getName() : $option->getValueFormatted()
    //                        ),
    //                        'quantity' => 1,
    //                        'amount' => moneyInput($item->hasDiscounts() ? $option->getDiscountedPrice()->getPrice() : $option->getPrice()->getPrice()),
    //                        'image' => null
    //                    ];
    //                }
    //            }
    //        }
    //
    //        $invoice['subtotal'] = collect($invoice['items'])->sum(function (array $data) {
    //            return $data['amount'] * $data['quantity'];
    //        });
    //
    //        if ($invoice['subtotal'] != (float)$order->getSubTotal('input')) {
    //            $diff = floatval(moneyInput(($order->getSubTotal('input') - $invoice['subtotal']) * 100, $order->getCurrency()));
    //            foreach ($invoice['items'] as $itemKey => $itemData) {
    //                if (floatval($itemData['quantity']) == floatval(1) && $itemData['amount'] + $diff >= 0) {
    //                    $invoice['items'][$itemKey]['amount'] = $itemData['amount'] + $diff;
    //                    break;
    //                }
    //            }
    //            $invoice['subtotal'] = collect($invoice['items'])->sum(function (array $data) {
    //                return $data['amount'] * $data['quantity'];
    //            });
    //        }
    //
    //        $discounts = $totals->only(['discount.before', 'discount.after'])->collapse();
    //
    //        //$total_discount = 0;
    //        foreach ($discounts as $discount) {
    //            /** @var \App\Helper\Store\CartTotal $discount */
    //            $discountAmount = $discount->getPriceInput();
    //            $invoice['items'][] = [
    //                'name' => $discount->getName(),
    //                'quantity' => 1,
    //                'amount' => is_numeric($discountAmount) ? $discountAmount : 0,
    //            ];
    //            if (is_numeric($discountAmount)) {
    //                $invoice['subtotal'] += $discountAmount;
    //            }
    //        }
    //
    //        // Totals
    //        $invoice['shipping'] = $totals->only(['shipping'])->collapse()->sum('price_input');
    //        $invoice['total'] = moneyInput($order->getInvoiceTotal(), $order->getCurrency());
    //
    //        // Taxes
    //        $taxes = $totals->only(['tax.before', 'tax.after'])
    //            ->collapse()->where('use_for_total', true);
    //        if ($taxes->isNotEmpty()) {
    //            $invoice['tax'] = $taxes->sum('price_input');
    //        }
    //
    //        //digital without billing address
    //        $hide_billing_address = $order->hide_billing_address ?? false;
    //        // Address
    //        if (!$hide_billing_address) {
    //            /** @var AddressContract $billing_address */
    //            $billing_address = $order->getBillingAddress();
    //            if ((empty($billing_address) || empty($billing_address->getCountryIso2())) && !empty($shipping_address = $order->getShippingAddress())) {
    //                $billing_address = $shipping_address;
    //            }
    //        }
    //
    //        $request = [
    //            'site_id' => site('site_id'),
    //            'site_reference_id' => $this->id,
    //            'site_lang' => site('language'),
    //            'provider' => $this->provider,
    //            'country' => $hide_billing_address ? null : $billing_address->getCountryIso2(),
    //            'client_ip_country' => \MaxMind::getCountry() ? \MaxMind::getCountry()->getIsoCode() : null,
    //            'currency' => site('currency'),
    //            'client_ip' => sprintf("%u", ip2long(request()->ip())),
    //            'client_email' => $order->getCustomer()->email,
    //            'amount' => $invoice['total'],
    //            'invoice' => $invoice,
    //            'status' => $this->status,
    //        ];
    //
    //        if (!$hide_billing_address) {
    //            $request['address'] = [
    //                'country' => $billing_address->getCountryName(),
    //                'state' => $billing_address->getStateName(),
    //                'city' => $billing_address->getCityName(),
    //                'street' => $billing_address->getStreetName(),
    //                'street_number' => $billing_address->getStreetNumber(),
    //                'address' => $billing_address->getAddressLine1(),
    //                'address_formatted' => $billing_address->getFormat(),
    //                'first_name' => $billing_address->getFirstName(),
    //                'last_name' => $billing_address->getLastName(),
    //                'phone' => $billing_address->getPhone(),
    //                'postal_code' => $billing_address->getPostCode(),
    //                'company' => $billing_address->isCompany()
    //            ];
    //
    //            if ($request['address']['company']) {
    //                $request['address']['company_name'] = $billing_address->getCompanyName();
    //            }
    //
    //            if ($billing_address->company_vat) {
    //                $request['address']['company_vat'] = $billing_address->getCompanyVat();
    //            }
    //
    //            if ($order->hasShippable() && !is_null($address = $order->getShippingAddress())) {
    //                $request['shipping_address'] = [
    //                    'country' => $address->getCountryName(),
    //                    'state' => $address->getStateName(),
    //                    'city' => $address->getCityName(),
    //                    'street' => $address->getStreetName(),
    //                    'street_number' => $address->getStreetNumber(),
    //                    'address' => $address->getAddressLine1(),
    //                    'address_formatted' => $address->getFormat(),
    //                    'first_name' => $address->getFirstName(),
    //                    'last_name' => $address->getLastName(),
    //                    'phone' => $address->getPhone(),
    //                    'postal_code' => $address->getPostCode(),
    //                    'company' => $address->isCompany(),
    //                ];
    //                if ($request['shipping_address']['company']) {
    //                    $request['address']['company_name'] = $address->getCompanyName();
    //                }
    //                if ($address->company_vat) {
    //                    $request['address']['company_vat'] = $address->getCompanyVat();
    //                }
    //            }
    //        } else {
    //            $request['address'] = null;
    //        }
    //
    //        return $request;
    //    }

    /**
     * @return string
     */
    public function generateHash()
    {
        $hash = Str::random(16);
        while (static::whereHash($hash)->count()) {
            $hash = Str::random(16);
        }

        return $hash;
    }

    /**
     * @param $hash
     * @return null|OrderPayment
     */
    public static function getForReturnPage(string $hash): ?OrderPayment
    {
        return ArrayCache::remember('order.payment.' . $hash, function () use ($hash) {
            $orderPayment = static::with([
                'order' => function ($query): void {
                    /** @var Order $query */
                    $query->forReturnPage();
                },
                'payment_provider.configuration',
            ])->whereHash($hash)->first();

            if ($orderPayment) {
                $orderPayment->order->setRelation('payment', (clone $orderPayment)->unsetRelation('order'));
                $orderPayment->order->setRelation('payments', collect([$orderPayment->order->payment]));
                if ($orderPayment->order->status == 'cancelled' && (OrderBannedIp::where('ip', request()->ip())->exists())) {
                    $orderPayment->order->status = 'pending';
                    $orderPayment->order->syncOriginal();
                }
            }

            return $orderPayment;
        });
        //        return ArrayCache::remember('order.payment.' . $hash, function () use ($hash) {
        //            $orderPayment = static::with([
        //                'order' => function ($query) {
        //                    /** @var Order $query */
        //                    $query->allOrderData();
        //                },
        //                'payment_provider.configuration',
        //            ])->whereHash($hash)->first();
        //
        //            if ($orderPayment) {
        //                if ($orderPayment->order->status == 'cancelled' && (OrderBannedIp::where('ip', request()->ip())->exists())) {
        //                    $orderPayment->order->status = 'pending';
        //                    $orderPayment->order->syncOriginal();
        //                }
        //            }
        //
        //            return $orderPayment;
        //        });
    }

    /**
     * @param array $params
     * @param       $server_secret_key
     *
     * @return string
     */
    protected static function genereteSignedLink(array $params, $server_secret_key): string
    {
        $hash = hash_hmac('sha256', implode('|', $params), (string) $server_secret_key, true);
        return $hash;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getProviderDataAttribute($value)
    {
        return $this->payment ? $this->payment->provider_data : null;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getAmountFormattedAttribute($value)
    {
        if (!isset($this->data['amount_formatted'])) {
            $this->data['amount_formatted'] = Format::money($this->amount);
        }
        return $this->data['amount_formatted'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setAmountFormattedAttribute($value)
    {
        $this->data['amount_formatted'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getAmountInputAttribute($value): string|int
    {
        return Format::moneyInput($this->amount);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getCntAttribute($value)
    {
        return !empty($this->data['cnt']) ? $this->data['cnt'] : (!empty($this->attributes['cnt']) ? $this->attributes['cnt'] : 0);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setCntAttribute($value)
    {
        $this->data['cnt'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getIconAttribute($value)
    {
        if (!isset($this->attributes['icon'])) {
            if (!empty($this->payment_provider->configuration)) {
                $this->attributes['icon'] = $this->payment_provider->configuration->getImage();
            } else {
                $this->attributes['icon'] = Media::payment_provider($this->provider);
            }
        }
        return $this->attributes['icon'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getProviderNameAttribute($value)
    {
        if (!isset($this->data['provider_name'])) {
            $this->data['provider_name'] = $this->payment_provider ? $this->payment_provider->storefront_name : $this->provider;
        }
        return $this->data['provider_name'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setProviderNameAttribute($value)
    {
        $this->data['provider_name'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStatusFormattedAttribute($value)
    {
        if (empty($this->data['status_formatted'])) {
            $this->data['status_formatted'] = Status::payment($this->status);
        }
        return $this->data['status_formatted'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setStatusFormattedAttribute($value)
    {
        $this->data['status_formatted'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDateAddedFormattedAttribute($value)
    {
        if (empty($this->data['date_added_formatted'])) {
            $this->data['date_added_formatted'] = $this->date_added ? $this->date_added->format(DateTimeFormat::getFormatByTemplate($this->date_template)) : null;
        }
        return $this->data['date_added_formatted'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setDateAddedFormattedAttribute($value)
    {
        $this->data['date_added_formatted'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setDateTemplateAttribute($value): void
    {
        $this->data['date_template'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getStatusColorAttribute($value)
    {
        if (empty($this->data['status_color'])) {
            $this->data['status_color'] = 'orange';
            if (in_array($this->status, ['completed', Payments::STATUS_AUTHORIZED])) {
                $this->data['status_color'] = 'green';
            } elseif (in_array($this->status, ['refunded', 'cancelled', 'failed', 'voided', 'timeouted', 'chargebacked'])) {
                $this->data['status_color'] = 'red';
            }
        }
        return $this->data['status_color'];
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function setStatusColorAttribute($value)
    {
        $this->data['status_color'] = $value;
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getAllowCaptureAuthorizationAttribute($value)
    {
        if ($this->payment->authorize_amount >= $this->order->price_total) {
            return true;
        }
        return __('order.error.authorize_amount', [
                'amount' => money($this->payment->authorize_amount, $this->order->getCurrency(), $this->order->getLanguage()),
                'total' => money($this->order->price_total, $this->order->getCurrency(), $this->order->getLanguage()),
        ]);
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getDownPaymentAttribute($value): ?string
    {
        if ($this->payment && $this->payment->provider_data && (isset($this->payment->provider_data->downpayment) || isset($this->payment->provider_data->initial))) {
            if (isset($this->payment->provider_data->downpayment) && $this->payment->provider_data->downpayment > 0) {
                return money($this->payment->provider_data->downpayment * 100);

            } elseif (isset($this->payment->provider_data->initial) && $this->payment->provider_data->initial > 0) {
                return money($this->payment->provider_data->initial * 100);
            }
        }
        return null;
    }

}
