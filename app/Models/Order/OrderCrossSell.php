<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 20.9.2017 г.
 * Time: 11:40 ч.
 */

namespace App\Models\Order;

use App\Models\Base\AbstractOrdersCrossSell;
use App\Models\Marketing\CrossSell\CrossSell;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderCrossSell extends AbstractOrdersCrossSell
{
    /**
     * @return BelongsTo
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * @return BelongsTo
     */
    public function cross_sell(): BelongsTo
    {
        return $this->belongsTo(CrossSell::class);
    }
}
