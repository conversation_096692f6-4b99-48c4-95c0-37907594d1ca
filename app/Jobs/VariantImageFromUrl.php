<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Helper\UrlImageToFile;
use App\Models\Product\Image;
use App\Models\Product\ImageVariant;
use App\Models\Product\Variant;

class VariantImageFromUrl extends Job
{
    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'product-images';

    /**
     * @param int $variant_id
     * @param string $image_url
     * @param string $field_name
     */
    public function __construct(protected $variant_id, protected $image_url, protected $field_name = 'file', protected array $fields = [])
    {
        $this->site_id = site('site_id');
    }

    /**
     * {@inheritdoc}
     */
    public function execute()
    {
        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return [static::SITE_MAINTENANCE, ['variant_id' => $this->variant_id, 'image_url' => $this->image_url, 'field_name' => $this->field_name, 'fields' => $this->fields]];
        }

        //migrate job from one to other platform
        if (($platform = platform()) != ($sitePlatform = sitePlatform())) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', $platform, $sitePlatform));
            return [static::WRONG_PLATFORM, ['variant_id' => $this->variant_id, 'image_url' => $this->image_url, 'field_name' => $this->field_name, 'fields' => $this->fields]];
        }

        if (!empty($variant = Variant::find($this->variant_id))) {
            $downloader = new UrlImageToFile($this->image_url, $this->field_name);
            if ($downloader->download()) {
                /** @var Image $img */
                $img = $variant->item->uploadImage();
                if ($img) {
                    ImageVariant::create([
                        'parent_id' => $this->variant_id,
                        'active' => 'yes',
                        'product_image_id' => $img->id,
                    ]);
                    if ($this->fields) {
                        $img->update($this->fields);
                    }
                }
            }
        }

        return static::DESTROY;
    }

}
