<?php

declare(strict_types=1);

namespace App\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\InteractsWithSockets;
use App\Models\Order\Order;

class PostOrderStatusChange
{
    use InteractsWithSockets;
    use SerializesModels;

    /** @var  Order $order */
    public $order;

    /**
     * Create a new event instance.
     *
     * @param \App\Models\Order\Order $order
     * @param string $previous
     */
    public function __construct(Order $order, public $previous)
    {
        $this->order = $order;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return PrivateChannel
     */
    public function broadcastOn(): PrivateChannel
    {
        return new PrivateChannel('order-status');
    }
}
