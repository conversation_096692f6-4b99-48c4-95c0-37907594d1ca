<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Helper\Catalog\Pages;
use App\Helper\Catalog\Products;
use App\Helper\CustomHomePage;
use App\Models\Blog\Article;
use App\Models\Blog\Blog;
use App\Models\Page\Page;
use App\Models\Product\Category;
use App\Models\Product\Tag;
use App\Models\Product\Vendor;
use App\Traits\ResponseType;
use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Core\SmartCollections\Models\ProductSelection;
use Throwable;
use W;

class OgMetaMiddleware
{
    use ResponseType;

    protected $cart;

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     * @throws Throwable
     */
    public function handle(Request $request, Closure $next)
    {
        /** @var Response $response */
        $response = $next($request);

        if ($this->shouldModifyResponse($response, $request)) {
            return $this->modifyResponse($response);
        }

        return $response;
    }

    /**
     * @param $response
     * @param $request
     * @return bool
     * @throws Throwable
     */
    protected function shouldModifyResponse($response, $request): bool
    {
        if (isLiquidEngine()) {
            return false;
        }

        return
            app_namespace() == 'site' &&
            !$request->ajax() &&
            $response instanceof Response &&
            $response->isSuccessful() &&
            $this->getResponseType($response) == 'html';
    }

    /**
     * @param Response $response
     * @return Response
     * @throws Throwable
     */
    protected function modifyResponse(Response $response): Response
    {
        $content = $response->getContent();

        $replaceContent = null;
        $namespaces = 'prefix="og: http://ogp.me/ns# twitter: http://ogp.me/ns/twitter# fb: http://ogp.me/ns/fb#"';
        switch (true) {
            case activeRoute('contacts'):
                $replaceContent = $this->__getContactsOgData();
                break;
            case activeRoute('site.vendor.view'):
                $replaceContent = $this->__getVendorOgData();
                break;
            case activeRoute('category.view'):
                $replaceContent = $this->__getCategoryOgData();
                break;
            case activeRoute('selection'):
                $replaceContent = $this->__getSelectionOgData();
                break;
            case activeRoute('site.tag'):
                $replaceContent = $this->__getTagOgData();
                break;
            case activeRoute('product.view'):
                $replaceContent = $this->__getProductOgData();
                $namespaces = 'prefix="og: http://ogp.me/ns# twitter: http://ogp.me/ns/twitter# product: http://ogp.me/ns/product#"';
                break;
            case activeRoute('page'):
                $replaceContent = $this->__getPageOgData();
                break;
            case activeRoute('products.search'):
                $replaceContent = $this->__getSearchOgData();
                break;
            case activeRoute('blog.view'):
                $replaceContent = $this->__getBlogViewOgData();
                //                if(!empty($replaceContent = $this->__getBlogViewOgData())) {
                //                    $replaceContent .= $this->__getBlogPagingOgData();
                //                }
                break;
            case activeRoute('blog.article.view'):
                $replaceContent = $this->__getArticleOgData();
                $namespaces = 'prefix="og: http://ogp.me/ns# article: http://ogp.me/ns/article# twitter: http://ogp.me/ns/twitter#"';
                break;
            case activeRoute('blog.list site.vendors site.home'):
                if (!empty($replaceContent = $this->__getSeoOgData()) && activeRoute('blog.list')) {
                    $replaceContent .= $this->__getBlogPagingOgData();
                }

                break;
            default:
                $replaceContent = $this->__getDefaultOgData();
                break;
        }

        if (!empty($replaceContent) && activeRoute('site.vendor.view category.view selection site.tag')) {
            $replaceContent .= $this->__getPagingOgData();
        }

        $content = str_replace([
            '<!-- HEAD_OG_TAGS -->',
            '<head>',
        ], [
            $replaceContent,
            sprintf('<head %s>', $namespaces),
        ], $content);
        $response->setContent($content);

        return $response;
    }

    /**
     * @return string|null
     * @throws Throwable
     */
    protected function __getPagingOgData()
    {
        return view('metatags.product_list')->render();
    }

    /**
     * @return string|null
     * @throws Throwable
     */
    protected function __getBlogPagingOgData()
    {
        return view('metatags.blog_list')->render();
    }

    /**
     * @return string|null
     * @throws Throwable
     */
    protected function __getSeoOgData()
    {
        if (activeRoute('site.home') && CustomHomePage::showCustomHomePage($request = request()) && ($page = CustomHomePage::getSystemHomePage()) && $page instanceof Page) {
            return view('metatags.page', [
                'page' => $page,
                'modify_url' => route('site.home')
            ])->render();
        }

        $widget = null;
        if (activeRoute('blog.list')) {
            $widget = W::blog();
        } elseif (activeRoute('site.vendors')) {
            $widget = W::vendors();
        } elseif (activeRoute('site.home')) {
            $widget = W::utilities();
        }

        return $widget ? view('metatags.seo', ['seo' => $widget->getSeo()])->render() : null;
    }

    /**
     * @return string
     * @throws Throwable
     */
    protected function __getDefaultOgData()
    {
        return view('metatags.default')->render();
    }

    /**
     * @return string
     * @throws Throwable
     */
    protected function __getProductOgData()
    {
        if (!($product = Products::getProductByUrl())) {
            return;
        }

        return view('metatags.product', ['product' => $product])->render();
    }

    /**
     * @return string
     * @throws Throwable
     */
    protected function __getBlogViewOgData()
    {
        if ($blog = $this->getBlog()) {
            return view('metatags.blog', ['blog' => $blog])->render();
        }

        if ($tag = $this->getBlogTag()) {
            return view('metatags.tag', ['tag' => $tag])->render();
        }

        return;
    }

    /**
     * @return string
     * @throws Throwable
     */
    protected function __getSelectionOgData()
    {
        if (!($selection = $this->getSelection()) || !($selection instanceof ProductSelection)) {
            return;
        }

        return view('metatags.selection', ['selection' => $selection])->render();
    }

    /**
     * @return string
     * @throws Throwable
     */
    protected function __getArticleOgData()
    {
        if (!($article = $this->getArticle())) {
            return;
        }

        return view('metatags.article', ['article' => $article])->render();
    }

    /**
     * @return string
     * @throws Throwable
     */
    protected function __getTagOgData()
    {
        if (!($tag = $this->getTag())) {
            return;
        }

        return view('metatags.tag', ['tag' => $tag])->render();
    }

    /**
     * @return string
     * @throws Throwable
     */
    protected function __getVendorOgData()
    {
        if (!($vendor = $this->getVendor())) {
            return;
        }

        return view('metatags.vendor', ['vendor' => $vendor])->render();
    }

    /**
     * @return string
     * @throws Throwable
     */
    protected function __getCategoryOgData()
    {
        if (!($category = $this->getCategory())) {
            return;
        }

        return view('metatags.category', ['category' => $category])->render();
    }

    /**
     * @return string
     * @throws Throwable
     */
    protected function __getPageOgData()
    {
        if (!($page = Pages::getPageByUrl())) {
            return;
        }

        return view('metatags.page', ['page' => $page])->render();
    }

    /**
     * @return string
     * @throws Throwable
     */
    protected function __getContactsOgData()
    {
        if (!($widget = W::contactInformation())) {
            return;
        }

        return view('metatags.about', ['widget' => $widget])->render();
    }

    /**
     * @return string
     * @throws Throwable
     */
    protected function __getSearchOgData()
    {
        return view('metatags.search')->render();
    }

    /**
     * @return Vendor|null
     */
    protected function getVendor()
    {
        try {
            return W::filters()->getVendor();
        } catch (Exception) {
        }

        return;
    }

    /**
     * @return Category|null
     */
    protected function getCategory()
    {
        try {
            return W::filters()->getCategory();
        } catch (Exception) {
        }

        return;
    }

    /**
     * @return \Modules\Core\SmartCollections\Models\ProductSelection|null
     */
    protected function getSelection()
    {
        try {
            return W::filters()->getSelection();
        } catch (Exception) {
        }

        return;
    }

    /**
     * @return Tag|null
     */
    protected function getTag()
    {
        try {
            return W::filters()->getTag();
        } catch (Exception) {
        }

        return;
    }

    /**
     * @return Blog
     */
    protected function getBlog()
    {
        try {
            return W::blog()->getBlog();
        } catch (Exception) {
        }

        return;
    }

    /**
     * @return \App\Models\Blog\Tag
     */
    protected function getBlogTag()
    {
        try {
            return W::blog()->getTag();
        } catch (Exception) {
        }

        return;
    }

    /**
     * @return Article
     */
    protected function getArticle()
    {
        try {
            return W::article()->getArticle(routeParameter('slug'));
        } catch (Exception) {
        }

        return;
    }

}
