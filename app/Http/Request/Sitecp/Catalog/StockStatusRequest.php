<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 22.3.2017 г.
 * Time: 17:56 ч.
 */

namespace App\Http\Request\Sitecp\Catalog;

use App\Http\AbstractRequest;
use App\Models\Product\Status;
use Validation;

class StockStatusRequest extends AbstractRequest
{
    protected $status_id;

    public function init(): void
    {
        $this->status_id = (int)routeParameter('status_id');

        parent::init(); // TODO: Change the autogenerated stub

        // custom validation
        Validation::extendImplicit('quantity_operator', fn ($attribute, $value, $parameters, \Illuminate\Contracts\Validation\Validator $validator): true =>
            //            $quantity = (int)$this->input('quantity');
            //            if($value == 3) {
            //                if($check = Status::where('quantity_operator_id', 4)->where('quantity', '<', $quantity - 1)->first()) {
            //                    $validator->setCustomMessages([
            //                        $attribute . '.quantity_operator' => __('product.stock.err.quantity_operator_custom', [
            //                            'name' => $check->name,
            //                            'condition' => sprintf('%s %s', $check->getOperator(), $check->quantity)
            //                        ])
            //                    ]);
            //                    return false;
            //                } elseif($check = Status::where('quantity_operator_id', 4)->where('quantity', '<', $quantity - 1)->first()) {
            //                    $validator->setCustomMessages([
            //                        $attribute . '.quantity_operator' => __('product.stock.err.quantity_operator_custom', [
            //                            'name' => $check->name,
            //                            'condition' => sprintf('%s %s', $check->getOperator(), $check->quantity)
            //                        ])
            //                    ]);
            //                    return false;
            //                }
            //            }
            true);
    }

    public function rules(): array
    {
        $unique = activeRoute('admin.product_statuses.edit') ?
            'unique:product_statuses,quantity_operator_id,' . $this->status_id :
            'unique:product_statuses,quantity_operator_id';

        return [
            'name' => 'required|max:191',
            'quantity_operator_id' => 'max:191|quantity_operator',
//            'quantity_operator_id' => 'max:191|' . $unique . '|quantity_operator',
            'quantity' => 'max:191',
            'stock_status_system' => 'max:191',
        ];
    }

    #[\Override]
    public function attributes(): array
    {
        return [
            'name' => __('product.stock.label.name'),
            'quantity_operator_id' =>  __('order.Product_quantity'),
            'quantity' =>  __('product.filter.quantity'),
            'stock_status_system' => __('order.th.actions'),
        ];
    }

    #[\Override]
    public function messages(): array
    {
        return [
            'quantity_operator_id.unique' => __('product.stock.err.quantity_operator_unique'),
        ];
    }
}
