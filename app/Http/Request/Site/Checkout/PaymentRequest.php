<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 16.8.2017 г.
 * Time: 11:30 ч.
 */

namespace App\Http\Request\Site\Checkout;

use App\Helper\Store\Contracts\OrderContract;
use App\Http\AbstractRequest;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\PaymentProviders;
use App\Models\Shipping\ShippingProvider;
use App\Models\Store\Cart;
use App\Traits\Request\CheckoutTermsRequest;
use Illuminate\Support\Collection;
use Throwable;
use Validation;

/**
 * Class PaymentRequest
 * @package App\Http\Request\Site\Checkout
 */
class PaymentRequest extends AbstractRequest
{
    use CheckoutTermsRequest;

    protected $payment_providers;

    protected $shipping_provider;

    protected $cart;

    public function init(): void
    {
        // custom validation provider
        Validation::extendImplicit('validate_payment_provider_cod', function ($attribute, $value) {
            if ('cod' == $value && !is_null($shipping = $this->getShippingProvider())) {
                $manager = $shipping->manager;
                $manager->setOrder($this->getCart());
                return $manager->supportsCashOnDelivery();
            }

            return true;
        });
    }

    /**
     * @return array
     * @throws Throwable
     */
    public function rules(): array
    {
        $rules = [
            'checkout.payment.provider' => 'required|in:' . $this->getPaymentProviders()->implode('provider', ',') . '|validate_payment_provider_cod'
        ];

        return array_merge(
            $rules,
            $this->getTermRules()
        );
    }

    /**
     * @return array
     */
    #[\Override]
    public function messages(): array
    {
        $messages = [
            'checkout.payment.provider.required' => __('sf.widget.checkout.err.payment_method_not_chosen'),
            'checkout.payment.provider.in' => __('sf.widget.order.err.invalid_payment_provider'),
            'checkout.payment.provider.validate_payment_provider_cod' => __('sf.widget.order.err.invalid_payment_provider')
        ];

        return array_merge(
            $messages,
            $this->getTermMessages(),
        );
    }

    /**
     * @return array
     * @throws Throwable
     */
    #[\Override]
    public function attributes(): array
    {
        $attributes = [];

        return array_merge(
            $attributes,
            $this->getTermAttributes(),
        );
    }

    /**
     * @return Collection<PaymentProviderConfiguration>
     */
    protected function getPaymentProviders(): Collection
    {
        if (is_null($this->payment_providers)) {
            $this->payment_providers = PaymentProviders::getConfigurations();
        }

        return $this->payment_providers;
    }

    /**
     * @return ShippingProvider|null
     * @throws Throwable
     */
    protected function getShippingProvider(): ?ShippingProvider
    {
        if (is_null($this->shipping_provider) && ($cartInstance = $this->getCart())) {
            $this->shipping_provider = $cartInstance->getShipping();
        }

        return $this->shipping_provider;
    }

    /**
     * @return OrderContract|Cart
     * @throws Throwable
     */
    protected function getCart()
    {
        if (is_null($this->cart)) {
            $this->cart = Cart::instance();
        }

        return $this->cart;
    }

}
