<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 30.11.2016 г.
 * Time: 00:09 ч.
 */

namespace App\Http\Controllers\Site;

use App;
use App\Events\CustomerShippingAddressAdd;
use App\Events\OrderCreated;
use App\Events\OrderStatusChangeReturn;
use App\Events\PreOrderCreated;
use App\Exceptions\Error;
use App\Exceptions\Errors;
use App\Exceptions\Fault;
use App\Exceptions\HttpNotFound;
use App\Helper\Conversion\Unit;
use App\Helper\Format;
use App\Helper\GeoIpInformation;
use App\Helper\JsHistory;
use App\Helper\Mail\SendCustomerNotification;
use App\Helper\Registry;
use App\Helper\Store\CheckoutSystemPages;
use App\Helper\Store\OrderModelPopulate;
use App\Helper\YesNo;
use App\Http\Request\Site\Checkout\AuthCodeLoginRequest;
use App\Http\Request\Site\Checkout\BillingAddressRequest;
use App\Http\Request\Site\Checkout\CreditorRequest;
use App\Http\Request\Site\Checkout\DiscountCodeRequest;
use App\Http\Request\Site\Checkout\GuestAuthorizeRequest;
use App\Http\Request\Site\Checkout\PaymentRequest;
use App\Http\Request\Site\Checkout\RecalculateShippingRequest;
use App\Http\Request\Site\Checkout\ShippingAddressRequest;
use App\Http\Request\Site\Checkout\ShippingOfficeAddressRequest;
use App\Http\Request\Site\Checkout\ShippingOfficeAddressSubmitRequest;
use App\Http\Request\Site\Checkout\ShippingRequest;
use App\Http\Request\Site\Customer\LoginRequest;
use App\Http\Request\Site\Customer\RegisterRequest;
use App\Integration\Payment\BoricaWay4\BoricaWay4Service;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerBillingAddress;
use App\Models\Customer\CustomerShippingAddress;
use App\Models\Discount\Discount;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\PaymentProviders;
use App\Models\Gateway\Payments;
use App\Models\Layout\FormFields;
use App\Models\Mongo\LogShippingQuotes;
use App\Models\Order\Order;
use App\Models\Order\OrderPayment;
use App\Models\Order\OrderStatus;
use App\Models\Page\Page;
use App\Models\Router\Exceptions;
use App\Models\Router\Logs;
use App\Models\Router\PaymentsLog;
use App\Models\Shipping\ShippingProvider;
use App\Models\Store\Cart as CartModel;
use App\Models\Store\Shop;
use App\Traits\IController;
use Apps;
use Carbon\Carbon;
use Closure;
use Crisu83\Conversion\Quantity\Length\Unit as LengthUnit;
use Exception;
use GDPR;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Jaybizzle\CrawlerDetect\CrawlerDetect as CrawlerDetectCore;
use lib\widget\store\Leasing;
use MaxMind;
use Modules\Apps\Administration\StoreLocations\Models\ZoneStores;
use Modules\Apps\Administration\StoreLocations\StoreLocationsManager;
use Modules\Apps\Marketing\BumperOffer\BumperOfferManager;
use Modules\Apps\Shippings\Omniship\AbstractManager;
use Modules\Apps\Shippings\Omniship\OmniShipService;
use Modules\Apps\Shippings\ShippingHours\ShippingHoursManager;
use Modules\Core\Core\Helpers\FunctionalityStatus;
use Modules\Core\Core\Models\MongoDb\CC2FaCustomerTasks;
use Modules\CustomIntegrations\Facades\CustomIntegrationModule;
use OmniShip;
use Omniship\Address\Office;
use Omniship\Common\ShippingQuote;
use Omniship\Common\ShippingQuoteBag;
use PaymentGateway;
use Throwable;
use Widget;

//use App\Integration\OmniShip\Models\OmniOffice;

class CheckoutController extends Controller
{

    use IController;

    /**
     * @var CartModel
     */
    protected $cart;

    /**
     * CheckoutController constructor.
     * @throws Throwable
     */
    public function __construct()
    {
        if (app()->runningInConsole()) {
            return;
        }

        $this->middleware(function (Request $request, Closure $next) {
            if (!App\Helper\Plan::enabled('checkout')) {
                //                return redirect('/');
                throw new Error(__('checkout.disabled'));
            }

            return $next($request);
        });

        if ($this->cart = CartModel::instance()) {
            \Illuminate\Support\Facades\View::share('cc_cart', $this->cart);
        }

        $except = [
            'index',
            'authorize',
            'login',
            'loginPost',
            'officesAutocomplete',
            'lockersAutocomplete',
            'forgotten',
            'register',
            'registerPost',
            'guestPost',
            'authCode',
            'authCodePost',
            'unconfirmedAccountsRestrict',
            'unconfirmedAccountsRestrictUpdate',
            'submitDiscountCode',
            'removeDiscountCode',
            'returnPage',
            'summary',
            'summaryDiscountCode',
            'summaryTotals',
            'summaryProducts',
            'countdownDiscountPopup',
            'newOfficesAutocomplete',
        ];
        if ($this->cart && $this->cart->hasGuestEmailInShippingForm()) {
            $except[] = 'submitShippingAddress';
            $except[] = 'shippingAddress';
            $except[] = 'changeShippingAddress';
            $except[] = 'shippingAddressQuotes';
            $except[] = 'submitShippingQuotes';
        }

        $except2 = $except;
        if ($this->cart && $this->cart->digital_count == $this->cart->products_count) {
            $except2[] = 'shippingAddress';
        }

        //https://trello.com/c/5wfaO71W/2238-%D1%81%D1%83%D0%BF%D0%B5%D1%80-%D1%81%D1%82%D1%80%D0%B0%D0%BD%D0%B5%D0%BD-%D0%B5%D1%84%D0%B5%D0%BA%D1%82
        $except2[] = 'shipping';
        $except2[] = 'payment';
        $except2[] = 'billingAddress';
        //end trello url

        $this->middleware('cart_customer', ['except' => $except2]);
        $this->middleware('checkout_steps', ['except' => $except]);
        $this->middleware('unconfirmed_accounts_restrict', [
            'except' => array_merge($except, [
                'changeShippingAddress'
            ])
        ]);

        $this->middleware(function (Request $request, Closure $next) {
            $active_route = activeRoute();
            if ($request->ajax() && (($min_price = setting('checkout_min_price', 0)) > 0 && $this->cart && $this->cart->getSubTotal('input') < $min_price)) {
                return $this->route('checkout');
            }

            return $next($request);
        }, ['except' => ['returnPage']]);

        $this->middleware(function (Request $request, Closure $next) {
            if ($this->cart) {
                if ($this->cart->max_quantity_reached) {
                    return $this->to(route('cart.list', $this->cart->key ?: $this->cart->getNewCartKey()));
                } elseif ($this->cart->has_minimum_stock) {
                    return $this->to(route('cart.list', $this->cart->key ?: $this->cart->getNewCartKey()));
                }
            }

            return $next($request);
        }, ['except' => ['returnPage']]);

        Widget::setSeo($this);
    }

    /**
     * @param Request $request
     * @return RedirectResponse|Response
     * @throws Throwable
     */
    public function index(Request $request)
    {
        if (!$this->cart) {
            return $this->to(route('cart.list', CartModel::getNewCartKey()));
        }


        if (($min_price = setting('checkout_min_price', 0)) > 0 && $this->cart->getSubTotal('input') < $min_price) {
            $steps = [
                'min_price' => \Illuminate\Support\Facades\View::make('global::checkout.include.checkout_min_price', [
                    'message' => sprintf(
                        __('sf.checkout.err.checkout_min_price_%1$s_allowed_checkout'),
                        Format::money(Format::toIntegerPrice($min_price))
                    )
                ])->render()
            ];
        } elseif (($max_price = setting('checkout_max_price', 0)) > 0 && $this->cart->getSubTotal('input') > $max_price) {
            $steps = [
                'min_price' => \Illuminate\Support\Facades\View::make('global::checkout.include.checkout_min_price', [
                    'message' => sprintf(__('sf.store.err.max_price_%1$s_allowed_in_cart'), Format::money(Format::toIntegerPrice(setting('checkout_max_price', 0))))
                ])->render()
            ];
        } else {
            $steps = $this->_getSteps();
        }


        if ($request->ajax()) {
            return \Illuminate\Support\Facades\View::panel('global::checkout.express', [
                'steps' => $steps,
                'history' => new JsHistory(array_merge([
                    'href' => route('checkout'),
                ], $this->getSeo()))
            ]);
        }

        if ($this->cart->products_count <= 0) {
            return $this->to(route('cart.list', CartModel::getNewCartKey()));
        }

        if (!$this->cart->checkout) {
            $this->cart->update([
                'checkout' => 1
            ]);
        }

        return \Illuminate\Support\Facades\View::mainResponse('global::checkout.express', [
            'steps' => $steps
        ]);
    }

    /**
     * @return Response
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function login(Request $request)
    {
        if (!$request->ajax()) {
            return $this->route('checkout');
        }

        return \Illuminate\Support\Facades\View::panel('global::checkout.authorize.login', [
            'login_code' => FunctionalityStatus::get('checkout_login_code'),
            '_email' => ($email = $request->query('email')) && is_string($email) && filter_var($email, FILTER_VALIDATE_EMAIL) ? $email : null,
        ], __('sf.global.act.login'));
    }

    /**
     * @return Response
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function register()
    {
        if (!request()->ajax()) {
            return $this->route('checkout');
        }

        return \Illuminate\Support\Facades\View::panel('global::checkout.authorize.register', [
            'fields' => FormFields::getByForm('register')
        ], __('sf.global.act.register'));
    }

    /**
     * @return Response
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function forgotten()
    {
        if (!request()->ajax()) {
            return $this->route('checkout');
        }

        return \Illuminate\Support\Facades\View::panel('global::checkout.authorize.forgotten-password', [], __('sf.global.act.forgotten_password'));
    }

    /**
     * @return Response
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function authCode(Request $request)
    {
        if (!request()->ajax()) {
            return $this->route('checkout');
        }

        return \Illuminate\Support\Facades\View::panel('global::checkout.authorize.access-code', [
            '_email' => ($email = $request->query('email')) && is_string($email) && filter_var($email, FILTER_VALIDATE_EMAIL) ? $email : null,
            'log' => $email ? CC2FaCustomerTasks::getActiveCodeByEmail(CC2FaCustomerTasks::ACTION_CHECKOUT_LOGIN, $email) : null,
        ], __('sf.checkout.label.access_code'));
    }

    /**
     * @param AuthCodeLoginRequest $request
     * @return Response
     * @throws Error
     * @throws Throwable
     */
    public function authCodePost(AuthCodeLoginRequest $request): ?Response
    {
        if ($request->input('action') == 'init') {
            return response([
                'status' => 'success',
                'type' => 'init',
                'code' => CC2FaCustomerTasks::createCodeByEmail(CC2FaCustomerTasks::ACTION_CHECKOUT_LOGIN, $request->input('email')),
            ]);
        }

        $customer = Customer::customerEmail($request->input('email'))->first();
        if (!$customer->active) {
            throw new Error(__('sf.err.account.inactive'), 'email');
        }

        $customer = Customer::loginById($customer->id);

        if ($this->cart) {
            $this->cart->setRelation('customer', $customer);
        }

        optional($request->input('__log'))->update([
            'status' => CC2FaCustomerTasks::STATUS_VERIFIED,
            'customer_id' => $customer->id,
        ]);

        return $this->authorizeResponse($request, 'cc.user.sign.in');
    }

    /**
     * @param boolean $include
     * @return Response
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function authorize($include = false)
    {
        if ($this->cart && $this->cart->hasGuestEmailInShippingForm()) {
            $html = $this->authorizeEmpty();
        } else {
            $customer = $this->cart->customer ?? null;
            if (!$customer || !$customer->email) {
                $html = $this->changeAuthorize(true);
            } else {
                $html = \Illuminate\Support\Facades\View::make('global::checkout.steps.edit.authorize', [
                    'customer' => $customer
                ])->render();
            }
        }

        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param boolean $include
     * @return Response
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function changeAuthorize($include = false)
    {
        if ($this->cart && $this->cart->hasGuestEmailInShippingForm()) {
            $html = $this->shippingAddress(true);
        } else {
            $html = \Illuminate\Support\Facades\View::make('global::checkout.steps.authorize', [
                'allowed_tabs' => $this->cart ? $this->cart->getAllowedLoginMethods() : ((new CartModel())->getAllowedLoginMethods()),
                'fields' => FormFields::getByForm('register')
            ])->render();
        }

        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param boolean $include
     * @return Response
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function shippingAddress($include = false)
    {
        if ($include) {
            $this->cart = CartModel::find($this->cart->id) ?: $this->cart;
        }

        if (!$include && !request()->ajax()) {
            return $this->route('checkout');
        }

        $html = $this->shippingAddressEmpty();
        if ($this->cart->has_shippable) {
            if ($this->cart->shipping_type && $this->cart->getShippingAddress()) {
                $address = $this->cart->getShippingAddress();

                $html = \Illuminate\Support\Facades\View::make('global::checkout.steps.edit.shipping-address', [
                    'type' => $this->cart->shipping_type,
                    'address' => $address,
                    'allowed_tabs' => $this->cart->getAllowedLoginMethods(),
                    'fields' => FormFields::getByForm('register'),
                    'hideFormat' => $address && $address->marketplace_id && CustomIntegrationModule::supportGetMarketplaces(),
                ])->render();
            } else {
                $html = $this->changeShippingAddress(true);
            }
        }

        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param boolean $include
     * @return Response|string
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function changeShippingAddress($include = false)
    {
        $shippingTemplates = [];
        if ($this->cart && $this->cart->customer) {
            $customData = $this->cart->customer->custom_data->pluck('value', 'field_id');
        } else {
            $customData = collect();
        }

        $customFields = FormFields::getByForm('register');
        $shippingTemplates[AbstractManager::SUPPORT_ADDRESS] = function () use ($customData, $customFields) {
            $address = $this->cart->getShippingAddress();
            if (!$address && Apps::installed('store_locations') && ($cookie = StoreLocationsManager::getCookieData())) {
                unset($cookie->get_zone_id);
                $address = (new CustomerShippingAddress())->newFromBuilder($cookie);
            }

            //            if (!$this->cart->isCustomer() && $address && $this->cart->customer) {
//                $address = $this->cart->customer->shipping_addresses()->find($address->id);
//            }

            $cc_guest = null;
            if (!$address && !Auth::customerId() && !($this->cart->customer ?? null)) {
                try {
                    $cc_guest = json_decode(base64_decode(\Cookie::get(Customer::GUEST_REMEMBER)));
                } catch (Throwable) {
                    //
                }
            }

            return \Illuminate\Support\Facades\View::make('global::checkout.steps.shipping-address.address', [
                'is_customer' => $this->cart->isCustomer(),
                'addresses' => $this->cart->getShippingAddresses(),
                'address' => $address,
                'fields' => $customFields,
                'fields_value' => $customData,
                'fields_prefix' => 'address',
                'cc_guest' => $cc_guest,
                'hide_map' => setting('checkout_hide_address_map', false),
            ])->render();
        };
        $shippingTemplates[AbstractManager::SUPPORT_OFFICE] = function () use ($customData, $customFields) {
            $address = $this->cart->getShippingAddress();
            if (!$address && Auth::customer()) {
                $address = Auth::customer()->shipping_addresses()->whereNotNull('office_id')->latest('id')->first();
            }

            if ($address && $address->office_type == 1) {
                $address = null;
            }

            $cc_guest = null;
            if (!$address && !Auth::customerId() && !($this->cart->customer ?? null)) {
                try {
                    $cc_guest = json_decode(base64_decode(\Cookie::get(Customer::GUEST_REMEMBER)));
                } catch (Throwable) {
                    //
                }
            }

            return \Illuminate\Support\Facades\View::make('global::checkout.steps.shipping-address.office', [
                'address' => $address,
                //                'address' => $this->cart->customer->shipping_addresses()->whereNotNull('office_id')->latest('id')->first(),
                'fields' => $customFields,
                'fields_value' => $customData,
                'fields_prefix' => 'office',
                'cc_guest' => $cc_guest,
                'show_map' => !setting('checkout_hide_office_map', false),
                'ipPosition' => ['lat' => optional(MaxMind::getLocation())->getLatitude() ?? 0, 'lng' => optional(MaxMind::getLocation())->getLongitude() ?? 0]
            ])->render();
        };


        $shippingTemplates[AbstractManager::SUPPORT_LOCKERS] = function () use ($customData, $customFields) {
            $address = $this->cart->getShippingAddress();
            if (!$address && Auth::customer()) {
                $address = Auth::customer()->shipping_addresses()->whereNotNull('office_id')->latest('id')->first();
            }

            if ($address && $address->office_type == 0) {
                $address = null;
            }

            $cc_guest = null;
            if (!$address && !Auth::customerId() && !($this->cart->customer ?? null)) {
                try {
                    $cc_guest = json_decode(base64_decode(\Cookie::get(Customer::GUEST_REMEMBER)));
                } catch (Throwable) {
                    //
                }
            }

            return \Illuminate\Support\Facades\View::make('global::checkout.steps.shipping-address.locker', [
                'address' => $address,
                'fields' => $customFields,
                'fields_value' => $customData,
                'fields_prefix' => 'locker',
                'cc_guest' => $cc_guest,
                'show_map' => !setting('checkout_hide_locker_map', false),
                'ipPosition' => ['lat' => optional(MaxMind::getLocation())->getLatitude() ?? 0, 'lng' => optional(MaxMind::getLocation())->getLongitude() ?? 0]
            ])->render();
        };

        $shippingTemplates[AbstractManager::SUPPORT_MARKETPLACE] = function () use ($customData, $customFields) {
            if (!empty($provider = ShippingProvider::whereType(AbstractManager::SUPPORT_MARKETPLACE)->active()->first())) {
                $address = $this->cart->getShippingAddress();
                if (!$address && Auth::customer()) {
                    $address = Auth::customer()->shipping_addresses()->whereNotNull('marketplace_id')->latest('id')->first();
                }

                $shops = Shop::whereHas('address')->with('address')->active()->whereIn('id', $provider->marketplaces)->get()->map(fn(Shop $shop) => $shop->setAttribute('title_with_address', implode(' - ', [$shop->title, $shop->address->text ?: $shop->address->format(false, false)]))
                    ->syncOriginal());

                $hideMap = false;
                if (CustomIntegrationModule::supportGetMarketplaces()) {
                    $shops = CustomIntegrationModule::getMarketplaces($shops)->map(fn(Shop $shop) => $shop->setAttribute('title_with_address', $shop->title)
                        ->syncOriginal());
                    $hideMap = true;
                }

                $cc_guest = null;
                if (!$address && !Auth::customerId() && !($this->cart->customer ?? null)) {
                    try {
                        $cc_guest = json_decode(base64_decode(\Cookie::get(Customer::GUEST_REMEMBER)));
                    } catch (Throwable) {
                        //
                    }
                }

                return \Illuminate\Support\Facades\View::make('global::checkout.steps.shipping-address.marketplace', [
                    'marketplaces' => $shops,
                    'address' => $address,
                    //                    'address' => $this->cart->customer->shipping_addresses()->whereNotNull('marketplace_id')->latest('id')->first(),
                    'fields' => $customFields,
                    'fields_value' => $customData,
                    'fields_prefix' => 'marketplace',
                    'hideMap' => $hideMap ?: !hasGoogleMapKey(),
                    'cc_guest' => $cc_guest,
                ])->render();
            }

            return null;
        };


        $types = $this->_checkSupportsTypes()->map(function (array $tab, $key) use ($shippingTemplates) {
            $tab['html'] = value($shippingTemplates[$key]);
            return $tab;
        })->filter(fn($tab): bool => !empty($tab['html']));

        $html = \Illuminate\Support\Facades\View::make('global::checkout.steps.shipping-address', [
            'types' => $types,
            'allowed_tabs' => $this->cart->getAllowedLoginMethods(),
            'fields' => FormFields::getByForm('register')
        ])->render();

        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param ShippingAddressRequest $request
     * @return Response
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function submitShippingAddress(ShippingAddressRequest $request)
    {
        if ($this->cart->hasGuestEmailInShippingForm()) {
            //App::call(CheckoutController::class . '@guestPost');
            $this->cart->setGuest(array_merge([
                'email' => $request->input('email'),
            ], array_filter([
                    'first_name' => $request->input('checkout.shipping.address.first_name'),
                    'last_name' => $request->input('checkout.shipping.address.last_name'),
                ])));

            if (setting('hide_marketing') && !GDPR::isActive()) {
                $this->cart->customer->update([
                    'marketing' => YesNo::False
                ]);
            }
        }

        if (isset($this->cart->customer->banned) && $this->cart->customer->banned == YesNo::True) {
            throw new Error(sprintf(
                __('sf.widget.global.err.account_suspended_due_to_%1$s_on_%2$s'),
                $this->cart->customer->banned_reason,
                Format::datetime($this->cart->customer->date_banned)
            ));
        }

        $this->cart->setShippingType($request->input('checkout.shipping.type'), false);

        $this->cart->customer->save();

        $address_model = $this->cart->customer->shipping_addresses();
        if ($this->cart->getShippingType() == AbstractManager::SUPPORT_OFFICE) {
            //if integration has address office, get and update
            /** @var CustomerShippingAddress $address_model */
            /** @var CustomerShippingAddress $address */
            $address = $address_model->whereNotNull('office_id')->firstOrNew([
                'integration' => $request->input('checkout.shipping.address.integration'),
                'customer_id' => $this->cart->customer->id,
                'office_type' => 0
            ])->fill($request->input('checkout.shipping.address'));
        } elseif ($this->cart->getShippingType() == AbstractManager::SUPPORT_LOCKERS) {
            //if integration has address locker, get and update
            /** @var CustomerShippingAddress $address_model */
            /** @var CustomerShippingAddress $address */
            $address = $address_model->whereNotNull('office_id')->firstOrNew([
                'integration' => $request->input('checkout.shipping.address.integration'),
                'customer_id' => $this->cart->customer->id,
                'office_type' => 1
            ])->fill($request->input('checkout.shipping.address'));
        } elseif ($this->cart->getShippingType() == AbstractManager::SUPPORT_MARKETPLACE) {
            //if integration has address office, get and update
            /** @var CustomerShippingAddress $address_model */
            /** @var CustomerShippingAddress $address */
            $address = $address_model->whereNotNull('marketplace_id')->firstOrNew([
                'integration' => $this->cart->getShippingType(),
                'customer_id' => $this->cart->customer->id
            ])->fill($request->input('checkout.shipping.address'));
        } else {
            if (!is_null($address_id = $request->input('checkout.shipping.address.id'))) {
                $address = $this->cart->customer->shipping_addresses()->findOrFail($address_id);
            } else {
                if ($this->cart->isCustomer()) {
                    //create new address
                    $address = $address_model->firstOrNew(['id' => null])
                        ->fill($request->input('checkout.shipping.address'));
                } else {
                    /** @var CustomerShippingAddress $address_model */
                    /** @var CustomerShippingAddress $address */
                    $address = $address_model->whereNull('office_id')->whereNull('marketplace_id')->first();
                    if ($address) {
                        $address->setRawAttributes(['id' => $address->id, 'customer_id' => $address->customer_id]);
                    } else {
                        $address = $address_model->findOrNew(null);
                    }

                    $address->fill($request->input('checkout.shipping.address'));
                }
            }
        }

        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($address, $request) {
                $address->save();

                $update_customer = [];
                if (!$this->cart->customer->first_name && !$this->cart->customer->last_name) {
                    $update_customer = array_merge($update_customer, [
                        'first_name' => $address->first_name,
                        'last_name' => $address->last_name
                    ]);
                }

                if (!$this->cart->customer->default_address_id && $this->cart->getShippingType() == AbstractManager::SUPPORT_ADDRESS) {
                    $update_customer = array_merge($update_customer, [
                        'default_address_id' => $address->id
                    ]);
                }

                if ($update_customer) {
                    $this->cart->customer->fill($update_customer)
                        ->save();
                }

                if ($request->has($this->cart->getShippingType() . '.custom')) {
                    $this->cart->customer->attachCustomFields($request->input($this->cart->getShippingType() . '.custom'));
                }

                $this->cart->setShippingAddress($address->id, false)
                    ->setShipping(null, false);

                $this->cart->hasBillingAddress($has_billing_address = (bool) $request->input('checkout.billing.has'), false);

                if (!$has_billing_address) {
                    $this->cart->setBillingAddress(null, false);
                }

                $this->cart->setStep('shippingAddress');

                $this->cart->shipping_quotes()->delete();

                //validate only for address where not integrated and not marketplace
                if (empty($address->integration)) {
                    $this->getStore('change_shipping');
                }

                return $address;
            }, 5);
        } catch (Exception $exception) {
            throw new Error($exception->getMessage());
        }

        //        $this->getStore('change_shipping');
//        if (setting('checkout_hide_single_shipping') && !is_null($single_check = $this->_checkSingleQuoteShippingManager())) {
//            return $single_check;
//        }

        try {
            if ($address && $address->wasRecentlyCreated) {
                event(new CustomerShippingAddressAdd($address));
            }
        } catch (Throwable) {
            //
        }

        $response = [
            'status' => 'success',
            'replaces' => [
                [
                    'selector' => '.js-checkout-shipping-address',
                    'html' => $this->shippingAddress(true)
                ],
                //                [
//                    'selector' => '.js-checkout-billing-address',
//                    'html' => $this->billingAddress(true)
//                ]
            ],
            'reload' => array_filter([
                //                '.js-checkout-shipping-address',
//                '.js-checkout-billing-address',
//                $has_billing_address ? null : '.js-checkout-shipping',
                '.js-checkout-authorize',
                '.js-checkout-billing-address',
                '.js-checkout-shipping',
                '.js-checkout-summary',
                '.js-checkout-summary-products',
                '.js-checkout-payment',
                '.js-checkout-total-formatted',
            ]),
            'events' => ['cc.checkout.step', 'cc.overlay.hide'],
            'step' => $this->cart->getStep(),
        ];

        return response($response);
    }

    /**
     * @param boolean $include
     * @return Response
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function billingAddress($include = false)
    {
        if (!$include && !request()->ajax()) {
            return $this->route('checkout');
        }

        if ($include && $this->cart) {
            $this->cart = CartModel::find($this->cart->id) ?: $this->cart;
        }

        $html = $this->billingAddressEmpty();
        if (setting('checkout_hide_billing_address')) {
            $this->cart->hasBillingAddress(false);
        }

        if (/*$include ? true : */
            $this->cart->hasBillingAddress()
        ) {
            $address = $this->cart->getBillingAddress();
            if ($address) {
                $html = \Illuminate\Support\Facades\View::make('global::checkout.steps.edit.billing-address', [
                    'address' => $address
                ])->render();
            } else {
                $html = $this->changeBillingAddress(true);
            }
        }

        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param boolean $include
     * @return Response|string
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function changeBillingAddress($include = false)
    {
        $html = \Illuminate\Support\Facades\View::make('global::checkout.steps.billing-address.address', [
            'is_customer' => $this->cart->isCustomer(),
            'addresses' => $this->cart->isCustomer() ? $this->cart->getBillingAddresses() : collect(),
            'address' => $this->cart->getBillingAddress()
        ])->render();

        $html = \Illuminate\Support\Facades\View::make('global::checkout.steps.billing-address', [
            'html' => $html
        ])->render();

        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param BillingAddressRequest $request
     * @return Response
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpUnused
     */
    public function submitBillingAddress(BillingAddressRequest $request)
    {
        if (!is_null($address_id = $request->input('checkout.billing.address.id'))) {
            $address = $this->cart->customer->billing_addresses()->findOrFail($address_id);
        } else {
            $address_model = $this->cart->customer->billing_addresses();
            if ($this->cart->isCustomer()) {
                //create new address
                $address = $address_model->firstOrNew(['id' => null])->fill($request->input('checkout.billing.address', []));
            } else {
                /** @var CustomerBillingAddress $address */
                $address = $address_model->first();
                if ($address) {
                    $address->setRawAttributes(['id' => $address->id, 'customer_id' => $address->customer_id]);
                } else {
                    $address = $address_model->findOrNew(null);
                }

                $address->fill($request->input('checkout.billing.address', []));
            }
        }

        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($address) {
                $address->save();

                $update_customer = [];
                if (!$this->cart->customer->first_name && !$this->cart->customer->last_name) {
                    $update_customer = array_merge($update_customer, [
                        'first_name' => $address->first_name,
                        'last_name' => $address->last_name
                    ]);
                }

                if (!$this->cart->customer->default_billing_address_id) {
                    $update_customer = array_merge($update_customer, [
                        'default_billing_address_id' => $address->id
                    ]);
                }

                if ($update_customer) {
                    $this->cart->customer->fill($update_customer)
                        ->save();
                }

                $this->cart->setBillingAddress($address->id, false);
                $this->cart->setStep('billingAddress');

                return $address;
            });
        } catch (Exception $exception) {
            throw new Error($exception->getMessage());
        }

        $response = [
            'status' => 'success',
            'replace' => [
                'selector' => '.js-checkout-billing-address',
                'html' => $this->billingAddress(true)
            ],
            'reload' => [
                //                '.js-checkout-billing-address',
                '.js-checkout-shipping',
                '.js-checkout-summary-totals',
                '.js-checkout-payment',
                '.js-checkout-summary-products'
            ],
            'events' => ['cc.checkout.step', 'cc.overlay.hide'],
            'step' => $this->cart->getStep(),
        ];

        return response($response);
    }

    /**
     * @param boolean $include
     * @return Response
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function shipping($include = false)
    {
        if (!$include && !request()->ajax()) {
            return $this->route('checkout');
        }

        $html = $this->shippingEmpty();
        if ($this->cart->has_shippable && (!$this->cart->hasBillingAddress() || $this->cart->getBillingAddress())) {
            if ($shipping = $this->cart->getShipping()) {
                $shipping_price = Format::money(0);
                if ($this->cart->total('shipping.shipping') > 0) {
                    $shipping_price = $this->cart->total('shipping.shipping', 'formatted');
                }

                $html = \Illuminate\Support\Facades\View::make('global::checkout.steps.edit.shipping', [
                    'shipping' => $shipping,
                    'shipping_price' => $shipping_price
                ])->render();
            } elseif ($this->cart->getShippingAddress()) {
                $html = $this->changeShipping(true);
            }
        }

        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param boolean $include
     * @return Response|string
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function changeShipping($include = false)
    {
        $managers = OmniShip::getManagersGroupByType($this->cart->zone_information_shipping);
        if ($this->cart->shipping_type && $managers->has($this->cart->getShippingType())) {
            $managers = $managers->get($this->cart->shipping_type)->each(function (AbstractManager $manager): void {
                $manager->setOrder($this->cart);
            });
        } else {
            $managers = collect();
        }

        foreach ($this->cart->products as $item) {
            $restrictions = ($item->product->category->restrictions ?? collect())
                ->where('restriction_type', 'shipping')->pluck('restriction_id', 'restriction_id');
            if ($restrictions->isEmpty()) {
                continue;
            }

            $managers = $managers->filter(fn(AbstractManager $manager) => $restrictions->has($manager->getId()));
        }

        //@todo check payments providers if is only COD active check shipping`s where support COD
        if (($payments_managers = PaymentProviders::getConfigurations()) && $payments_managers->count() == 1 && $payments_managers->has('cod')) {
            $managers = $managers->filter(fn(AbstractManager $manager) => $manager->supportsCashOnDelivery());
        }

        /** @var CustomerShippingAddress $address */
        if (!is_null($address = $this->cart->getShippingAddress()) && !is_null($address->office_id)) {
            $managers = $managers->filter(fn(AbstractManager $manager): bool => $manager->getKey() == $address->integration);
        }

        $loaded = [];
        /** @var Collection $managers */
        $managers = $managers->map(function (AbstractManager $manager) use (&$loaded): \Modules\Apps\Shippings\Omniship\AbstractManager {
            if (
                ($quotes = $this->cart->getShippingQuotes($manager->provider_id)) &&
                !$quotes->isEmpty() &&
                ($provider = $manager->getProvider()) &&
                $provider->setAttribute('quotes', $quotes) &&
                $provider->quotes_formatted
            ) {
                $manager->setCheckoutQuotes($provider->quotes_formatted);
            }

            $loaded[$manager->getKey()] = $manager->hasCheckoutQuotes();
            return $manager;
        });

        if ($managers->count() == 1 && !activeRoute('checkout.shipping.shipping.change')) {
            $manager = $managers->first();
            if (Apps::installed(ShippingHoursManager::APP_KEY)) {
                $provider = $manager->getProvider([]);
                if (!$provider->has_delivery_dates) {
                    $this->cart->setShipping($manager->provider_id);
                }
            } else {
                $this->cart->setShipping($manager->provider_id);
            }
        }

        $html = \Illuminate\Support\Facades\View::make('global::checkout.steps.shipping', [
            'managers' => $managers,
            'loaded' => $loaded,
            'manager' => $this->cart->getShipping(),
        ])->render();

        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param $provider
     * @param string $type
     * @return JsonResponse
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function quotes($provider, $type = 'html')
    {
        $managers = OmniShip::getManagersGroupByType($this->cart->zone_information_shipping);
        if ($this->cart->shipping_type && $managers->has($this->cart->getShippingType())) {
            $managers = $managers->get($this->cart->shipping_type)->each(function (AbstractManager $manager): void {
                $manager->setOrder($this->cart);
            });
        } else {
            $managers = collect();
        }

        //@todo check payments providers if is only COD active check shipping`s where support COD
        if (($payments_managers = PaymentProviders::getConfigurations()) && $payments_managers->count() == 1 && $payments_managers->has('cod')) {
            $managers = $managers->filter(fn(AbstractManager $manager) => $manager->supportsCashOnDelivery());
        }

        /** @var CustomerShippingAddress $address */
        if (!is_null($address = $this->cart->getShippingAddress()) && !is_null($address->office_id)) {
            $managers = $managers->filter(fn(AbstractManager $manager): bool => $manager->getKey() == $address->integration);
        }

        if (!$managers->has($provider)) {
            throw new HttpNotFound;
        }

        /** @var AbstractManager $manager */
        $manager = $managers->get($provider);

        $this->cart->setShippingQuotes($manager->provider_id);

        $quotes = $manager->getQuotesForProvider($request_response);
        $this->cart->setShippingQuotes($manager->provider_id, $quotes);

        if (($_COOKIE['quote-logs'] ?? null) == $manager->getKey() && $request_response && (!empty($request_response->request) || !empty($request_response->response))) {
            try {
                LogShippingQuotes::create([
                    'site_id' => (int) site('site_id'),
                    'cart' => $this->cart->getKey(),
                    'request' => $request_response->request ?? null,
                    'response' => $request_response->response ?? null,
                    'type' => $request_response->type ?? null,
                ]);
            } catch (Throwable) {
            }
        }

        if ($quotes) {
            $manager->setCheckoutQuotes(
                $manager->formatQuotesForDisplay($quotes)
            );
        }

        if ($manager->getCheckoutQuotes()->where('hidden', 1)->count() > 0) {
            $manager->setCheckoutQuotes(new ShippingQuoteBag());
        }

        if ($type == 'json') {
            return response()->json([
                'status' => 'success',
                'count' => $manager->getCheckoutQuotes()->count(),
                'quotes' => $manager->getCheckoutQuotes()->sortBy('price', SORT_ASC)
            ]);
        }

        $html = \Illuminate\Support\Facades\View::make('global::checkout.include._shipping_provider_quotes', [
            'm' => $manager,
            'provider' => $manager->getProvider([]),
            'loaded' => [
                $manager->getKey() => true
            ],
            'manager' => $this->cart->getShipping(),
            'total_managers' => $managers->count()
        ])->render();

        return response()->json([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param ShippingOfficeAddressRequest $request
     * @param $provider
     * @return JsonResponse
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function shippingAddressQuotes(ShippingOfficeAddressRequest $request, $provider)
    {
        $officeType = $request->input('checkout.shipping.type');
        $address = (new CustomerShippingAddress())->newFromBuilder([
            'integration' => $request->input('checkout.shipping.address.integration'),
            'customer_id' => $this->cart->customer->id ?? null,
            'office_type' => intval($officeType == AbstractManager::SUPPORT_LOCKERS)
        ])->fill($request->input('checkout.shipping.address'));

        $managers = OmniShip::getManagersGroupByType(/*$this->cart->zone_information_shipping*/);
        if ($officeType && $managers->has($officeType)) {
            $managers = $managers->get($officeType)->each(function (AbstractManager $manager) use ($address): void {
                $manager->setOrder($this->cart);
                $manager->setShippingAddress($address);
            });
        } else {
            $managers = collect();
        }

        //@todo check payments providers if is only COD active check shipping`s where support COD
        if (($payments_managers = PaymentProviders::getConfigurations()) && $payments_managers->count() == 1 && $payments_managers->has('cod')) {
            $managers = $managers->filter(fn(AbstractManager $manager) => $manager->supportsCashOnDelivery());
        }

        /** @var CustomerShippingAddress $address */
        $managers = $managers->filter(fn(AbstractManager $manager): bool => $manager->getKey() == $address->integration);

        if (!$managers->has($provider)) {
            throw new HttpNotFound;
        }

        /** @var AbstractManager $manager */
        $manager = $managers->get($provider);

        $parameters = $manager->getCheckoutCalculationParameters();
        $parameters['receiver_address'] = $address->address;

        $quotes = $manager->getQuotesForProvider($request_response, $parameters);

        if (($_COOKIE['quote-logs'] ?? null) == $manager->getKey() && $request_response && (!empty($request_response->request) || !empty($request_response->response))) {
            try {
                LogShippingQuotes::create([
                    'site_id' => (int) site('site_id'),
                    'cart' => $this->cart->getKey(),
                    'request' => $request_response->request ?? null,
                    'response' => $request_response->response ?? null,
                    'type' => $request_response->type ?? null,
                ]);
            } catch (Throwable) {
            }
        }

        if ($quotes) {
            $quotes = $manager->formatQuotesForDisplay($quotes);
        } else {
            $quotes = new ShippingQuoteBag();
        }

        if ($quotes->where('hidden', 1)->count() > 0) {
            $quotes = new ShippingQuoteBag();
        }

        return response()->json([
            'status' => 'success',
            'count' => $quotes->count(),
            'quotes' => $quotes->sortBy('price', SORT_ASC)
        ]);
    }

    /**
     * @param ShippingOfficeAddressSubmitRequest $request
     * @param $provider
     * @return JsonResponse
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function submitShippingQuotes(ShippingOfficeAddressSubmitRequest $request, $provider)
    {

        //        if ($this->cart->has_shippable && !$this->cart->getShippingAddress()) {
//            $this->cart->setStep('authorize');
//            return response()->json([
//                'status' => 'success',
//                'redirect' => route('checkout'),
//            ]);
//        }

        if ($this->cart->hasGuestEmailInShippingForm()) {
            //App::call(CheckoutController::class . '@guestPost');
            $this->cart->setGuest(array_merge([
                'email' => $request->input('email'),
            ], array_filter([
                    'first_name' => $request->input('checkout.shipping.address.first_name'),
                    'last_name' => $request->input('checkout.shipping.address.last_name'),
                ])));

            if (setting('hide_marketing') && !GDPR::isActive()) {
                $this->cart->customer->update([
                    'marketing' => YesNo::False
                ]);
            }
        }

        if (isset($this->cart->customer->banned) && $this->cart->customer->banned == YesNo::True) {
            throw new Error(sprintf(
                __('sf.widget.global.err.account_suspended_due_to_%1$s_on_%2$s'),
                $this->cart->customer->banned_reason,
                Format::datetime($this->cart->customer->date_banned)
            ));
        }

        $this->cart->setShippingType($request->input('checkout.shipping.type'), false);

        $this->cart->customer->save();

        $address_model = $this->cart->customer->shipping_addresses();

        /** @var CustomerShippingAddress $address_model */
        /** @var CustomerShippingAddress $address */
        $address = $address_model->whereNotNull('office_id')->firstOrNew([
            'integration' => $request->input('checkout.shipping.address.integration'),
            'customer_id' => $this->cart->customer->id,
            'office_type' => intval($this->cart->getShippingType() == AbstractManager::SUPPORT_LOCKERS)
        ])->fill($request->input('checkout.shipping.address'));

        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($address, $request) {
                $address->save();

                $update_customer = [];
                if (!$this->cart->customer->first_name && !$this->cart->customer->last_name) {
                    $update_customer = array_merge($update_customer, [
                        'first_name' => $address->first_name,
                        'last_name' => $address->last_name
                    ]);
                }

                if (!$this->cart->customer->default_address_id && $this->cart->getShippingType() == AbstractManager::SUPPORT_ADDRESS) {
                    $update_customer = array_merge($update_customer, [
                        'default_address_id' => $address->id
                    ]);
                }

                if ($update_customer) {
                    $this->cart->customer->fill($update_customer)
                        ->save();
                }

                if ($request->has($this->cart->getShippingType() . '.custom')) {
                    $this->cart->customer->attachCustomFields($request->input($this->cart->getShippingType() . '.custom'));
                }

                $this->cart->setShippingAddress($address->id, false)
                    ->setShipping(null, false);

                $this->cart->hasBillingAddress($has_billing_address = (bool) $request->input('checkout.billing.has'), false);

                if (!$has_billing_address) {
                    $this->cart->setBillingAddress(null, false);
                }

                $this->cart->setStep('shippingAddress');

                $this->cart->shipping_quotes()->delete();

                //validate only for address where not integrated and not marketplace
                if (empty($address->integration)) {
                    $this->getStore('change_shipping');
                }

                return $address;
            }, 5);
        } catch (Exception $exception) {
            throw new Error($exception->getMessage());
        }

        //        $this->getStore('change_shipping');
//        if (setting('checkout_hide_single_shipping') && !is_null($single_check = $this->_checkSingleQuoteShippingManager())) {
//            return $single_check;
//        }

        try {
            if ($address && $address->wasRecentlyCreated) {
                event(new CustomerShippingAddressAdd($address));
            }
        } catch (Throwable) {
            //
        }

        $managers = OmniShip::getManagersGroupByType(/*$this->cart->zone_information_shipping*/);
        if ($this->cart->shipping_type && $managers->has($this->cart->getShippingType())) {
            $managers = $managers->get($this->cart->shipping_type)->each(function (AbstractManager $manager): void {
                $manager->setOrder($this->cart);
            });
        } else {
            $managers = collect();
        }

        //@todo check payments providers if is only COD active check shipping`s where support COD
        if (($payments_managers = PaymentProviders::getConfigurations()) && $payments_managers->count() == 1 && $payments_managers->has('cod')) {
            $managers = $managers->filter(fn(AbstractManager $manager) => $manager->supportsCashOnDelivery());
        }

        $managers = $managers->filter(fn(AbstractManager $manager): bool => $manager->getKey() == $address->integration);

        if (!$managers->has($provider)) {
            throw new HttpNotFound;
        }

        /** @var AbstractManager $manager */
        $manager = $managers->get($provider);

        $this->cart->setShippingQuotes($manager->provider_id);

        $parameters = $manager->getCheckoutCalculationParameters();
        $parameters['receiver_address'] = $address->address;

        $quotes = $manager->getQuotesForProvider($request_response, $parameters);
        $this->cart->setShippingQuotes($manager->provider_id, $quotes);

        if (($_COOKIE['quote-logs'] ?? null) == $manager->getKey() && $request_response && (!empty($request_response->request) || !empty($request_response->response))) {
            try {
                LogShippingQuotes::create([
                    'site_id' => (int) site('site_id'),
                    'cart' => $this->cart->getKey(),
                    'request' => $request_response->request ?? null,
                    'response' => $request_response->response ?? null,
                    'type' => $request_response->type ?? null,
                ]);
            } catch (Throwable) {
            }
        }

        if ($quotes) {
            $manager->setCheckoutQuotes(
                $manager->formatQuotesForDisplay($quotes)
            );
        }

        if ($manager->getCheckoutQuotes()->where('hidden', 1)->count() > 0) {
            $manager->setCheckoutQuotes(new ShippingQuoteBag());
        }

        $this->cart->setShipping($manager->getProvider([])->id)
            ->setShippingQuote($manager->getCheckoutQuotes()->first(fn($r): bool => $r->getId() == $request->input('checkout.shipping.' . $provider . '.service_id')));

        $this->cart = CartModel::find($this->cart->id);

        $this->getStore('submit_shipping');

        $this->cart->setStep('shipping');

        $response = [
            'status' => 'success',
            //            'replace' => [
//                'selector' => '.js-checkout-shipping',
//                'html' => $this->shipping(true),
//            ],
            'reload' => [
                '.js-checkout-shipping',
                '.js-checkout-summary-totals',
                '.js-checkout-summary-products',
                '.js-checkout-payment',
            ],
            'events' => ['cc.checkout.step'],
            'step' => $this->cart->getStep(),
        ];

        return response()->json($response);
    }

    /**
     * @param RecalculateShippingRequest $request
     * @return Response|array
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpUnused
     */
    public function recalculateShipping(RecalculateShippingRequest $request)
    {
        if (isZora() && $request->input('checkout.payment.provider') != 'bnp' && session('zora_card')) {
            session()->forget('zora_card');
            session()->forget('bnp_pos2');
            $this->cart->payment_variant_id = null;
        }

        /** @var AbstractManager $manager */
        if (!$this->cart->has_shippable || !($manager = $this->cart->getShippingManager())) {
            return []; //error
        }

        $manager->setOrder($this->cart);
        $payment_provider = PaymentProviders::whereProvider($request->input('checkout.payment.provider'))->firstOrFail();

        $parameters = $manager->getCheckoutCalculationParameters(false);

        if ($request->input('checkout.payment.provider') == 'cod' && $manager->supportsCashOnDelivery()) {
            if (!is_null($total = $this->cart->getTotalWithoutShipping('input'))) {
                $parameters['cash_on_delivery_amount'] = $total;
            }
        }

        $shipping = $this->cart->getShipping();
        $quotes = $manager->getQuotesForProvider($request_response, $parameters);
        if (!$quotes || !$quotes->has($shipping->service_id)) {
            return response()->json([
                'status' => 'error',
                'msg' => 'Shipping quote service is invalid!',
            ]);
        }

        if (($_COOKIE['quote-logs'] ?? null) == $manager->getKey() && $request_response && (!empty($request_response->request) || !empty($request_response->response))) {
            try {
                LogShippingQuotes::create([
                    'site_id' => (int) site('site_id'),
                    'cart' => $this->cart->getKey(),
                    'request' => $request_response->request ?? null,
                    'response' => $request_response->response ?? null,
                    'type' => $request_response->type ?? null,
                ]);
            } catch (Throwable) {
            }
        }

        if ($payment_provider->is_online_payment) {
            $quotes = $quotes->map(function (ShippingQuote $quote): \Omniship\Common\ShippingQuote {
                $quote->setType('online_payment');
                return $quote;
            });
        }

        $quote = $quotes->get($shipping->service_id);
        if ($quote->hasParameter('formatted')) {
            $this->cart->setStep('shippingAddress');
            return response([
                'status' => 'success',
                'redirect' => route('checkout')
            ]);
        }

        $this->cart->resetShippingQuotes($manager->provider_id, $quotes);

        $manager->setCheckoutQuotes($quotes);

        /** @var ShippingQuote $quote */
        $this->cart->setShippingQuote($quote, false);

        $this->cart->setStep('payment', false);
        $this->cart->setPayment($payment_provider->id);

        $response = [
            'status' => 'success',
            'reload' => [
                '.js-checkout-shipping',
                '.js-checkout-summary'
            ],
        ];

        return response($response);
    }

    /**
     * @param ShippingRequest $request
     * @param bool $include
     * @return Response|array
     * @throws Throwable
     * @noinspection PhpUnusedParameterInspection PhpMissingParamTypeInspection
     */
    public function submitShipping(ShippingRequest $request, $include = false)
    {
        if ($this->cart->has_shippable && !$this->cart->getShippingAddress()) {
            $this->cart->setStep('authorize');
            return $this->route('checkout');
        }

        $this->getStore('submit_shipping');

        $this->cart->setStep('shipping');

        $response = [
            'status' => 'success',
            'replace' => [
                'selector' => '.js-checkout-shipping',
                'html' => $this->shipping(true),
            ],
            'reload' => [
                //                '.js-checkout-shipping',
                '.js-checkout-summary-totals',
                '.js-checkout-summary-products',
                '.js-checkout-payment',
            ],
            'events' => ['cc.checkout.step'],
            'step' => $this->cart->getStep(),
        ];
        if ($include) {
            return $response;
        }

        return response($response);
    }

    /**
     * @param boolean $include
     * @return Response
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingParamTypeInspection
     */
    public function payment($include = false)
    {
        $html = $this->changePayment(true);
        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param boolean $include
     * @return Response|string
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingParamTypeInspection
     */
    public function changePayment($include = false)
    {
        if ($this->cart->hasBillingAddress() && !$this->cart->getBillingAddress()) {
            return $this->paymentEmpty();
        }

        if ($this->cart->has_shippable) {
            /** @var AbstractManager $shipping_manager */
            $shipping = $this->cart->getShipping();
            if (!$shipping || !$shipping->service_id) {
                return $this->paymentEmpty();
            }

            $shipping_manager = $shipping->manager ? $shipping->manager->setOrder($this->cart) : null;
        } else {
            $shipping_manager = null;
            $shipping = null;
            if (!$this->cart->getBillingAddress()) {
                if (!$this->cart->hide_billing_address || empty($this->cart->customer)) {
                    return $this->paymentEmpty();
                }
            }
        }

        $orderTotal = $this->cart->getTotal('input');

        $hide_billing_address = $this->cart->hide_billing_address;
        $managers = PaymentProviders::getConfigurations()->filter(function (PaymentProviderConfiguration $manager) use ($orderTotal, $hide_billing_address): bool {
            if ($manager->min_price && $manager->min_price > $orderTotal) {
                return false;
            }

            if (!$manager->isAllowedByOrderAmount($orderTotal * 100)) {
                return false;
            }

            if ('credit' == $manager->payment_provider->group && $hide_billing_address) {
                return false;
            }

            return true;
        });

        foreach ($this->cart->products as $item) {
            $restrictions = ($item->product->category->restrictions ?? collect())
                ->where('restriction_type', 'payment')->pluck('restriction_id', 'restriction_id');
            if ($restrictions->isEmpty()) {
                continue;
            }

            $managers = $managers->filter(fn(PaymentProviderConfiguration $manager) => $restrictions->has($manager->provider));
        }


        if ($shipping_manager) {
            $map_managers = function (PaymentProviderConfiguration $manager) use ($shipping_manager, $orderTotal): \App\Models\Gateway\PaymentProviderConfiguration {
                if ('credit' == $manager->payment_provider->group) {
                    /** @var Collection $products */
                    $products = $this->cart->products->pluck('product_id', 'product_id');
                    /** @var Leasing $leasing */
                    $leasing = Widget::get('store.leasing');
                    $active_manager = $this->cart->getPayment();

                    if ($active_manager) {
                        $details = $leasing->renderCheckoutDetailedForm($manager->provider, $orderTotal, $products->all(), ($manager->provider == $active_manager->provider ? $active_manager->payment_variant_id : 0), $active_manager->initial, $active_manager->installment);
                    } else {
                        $details = $leasing->renderCheckoutDetailedForm($manager->provider, $orderTotal, $products->all(), 0);
                    }

                    $manager->setAttribute('html', $details);
                }

                $manager->setAttribute('support_recalculate_shipping', $shipping_manager->supportsRecalculateShippingOnPaymentChange());
                return $manager;
            };

            if (!$shipping_manager->supportsCashOnDelivery() || !$shipping->quote || !$shipping->quote->getAllowanceCashOnDelivery()) {
                /** @var Collection $managers */
                $managers = $managers->forget('cod');
            }

            if (!$shipping_manager->supportsPayOnPlace() || !$shipping->quote) {
                /** @var Collection $managers */
                $managers = $managers->forget('pop');
            }

            if (($provider = $shipping_manager->getProvider()) && $provider->payments->isNotEmpty()) {
                /** @var Collection $managers */
                $managers = $managers->whereIn('provider', $provider->payments->pluck('payment_provider'));
            }

            /** @var Collection $managers */
            $managers = $managers->map($map_managers);

        } elseif (!$this->cart->has_shippable) {
            /** @var Collection $managers */
            $managers = $managers->forget('cod')
                ->forget('pop');
            $managers = $managers->filter(fn(PaymentProviderConfiguration $manager): bool => $manager->payment_provider->group != 'credit');
        }

        $managers_groups = $managers->groupBy('payment_provider.group', true);

        $html = \Illuminate\Support\Facades\View::make('global::checkout.steps.payment', [
            'managers' => $managers_groups,
            'total_managers' => $managers->count(),
            'manager' => $this->cart->getPayment()
        ])->render();

        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param PaymentRequest $request
     * @return JsonResponse|Response
     * @throws Fault
     * @throws Throwable
     * @noinspection PhpUnused PhpMissingReturnTypeInspection
     */
    public function submitPayment(PaymentRequest $request)
    {
        // Check for existing order / payment
        if ($orderId = $request->input('order_id')) {
            $orderModel = OrderModelPopulate::initFromCart($request, $this->cart, [
                'note_customer' => is_string($request->input('checkout.note')) ? $request->input('checkout.note') : null,
            ]);

            if (!$orderModel->validate()) {
                throw new Errors($orderModel->getErrors());
            }

            $order = Order::with('payment')->findOrFail($orderId);
            $payment = Payments::findOrFail($order->payment->payment_id);
            if (!in_array($payment->status, [Payments::STATUS_INITIATED, Payments::STATUS_REQUESTED, Payments::STATUS_PENDING])) {
                return response()->json([
                    'status' => 'error',
                    'msg' => 'Invalid payment status',
                    'js' => ' ',
                ]);
            }

            $payment_provider = PaymentProviders::whereProvider($request->input('checkout.payment.provider'))
                ->firstOrFail();

            if ($payment->provider != $payment_provider->provider) {
                $order->payment->update(['provider' => $payment_provider->provider]);
                $payment->update(['provider' => $payment_provider->provider]);
            }
        }

        if (empty($order)) {
            if ($this->cart->has_shippable && !$this->cart->getShippingAddress()) {
                Logs::createFromThrowable(new Exception('Submit payment with missing shipping address'), 'Checkout Error');

                $this->cart->setStep('authorize');
                //session()->flash('payment_canceled', __('sf.checkout.header.error'));
                return $this->route('checkout');
            }

            if ($this->cart->getShippingAddress() && !$this->cart->getShipping()) {
                Logs::createFromThrowable(new Exception('Submit payment with missing shipping'), 'Checkout Error');

                $this->cart->setStep('shippingAddress');
                //session()->flash('payment_canceled', __('sf.checkout.header.error'));
                return $this->route('checkout');
            }

            $this->cart->setStep('payment', false);

            $payment_provider = PaymentProviders::whereProvider($request->input('checkout.payment.provider'))
                ->firstOrFail();

            $this->cart->setPayment($payment_provider->id);

            //$this->cart = CartModel::findOrFail($this->cart->id);

            event(new PreOrderCreated($this->cart));

            if (!setting('hide_marketing') && !GDPR::isActive() && $this->cart->customer) {
                $marketing = strtolower((string) $request->input('marketing', 'no'));
                if ($marketing == YesNo::True) {
                    $this->cart->customer->update([
                        'marketing' => $marketing
                    ]);
                }
            }

            $orderModel = OrderModelPopulate::initFromCart($request, $this->cart, [
                'note_customer' => is_string($request->input('checkout.note')) ? $request->input('checkout.note') : null,
            ]);

            if (!$orderModel->validate()) {
                throw new Errors($orderModel->getErrors());
            }

            $order = null;
            try {
                $order = $orderModel->create();
            } catch (Error $e) {
                $this->logOrderRollBack($e, $order);
                return response([
                    'status' => 'error',
                    'msg' => $e->getMessage(),
                    'field' => $e->getField()
                ]);
            } catch (Errors $e) {
                $this->logOrderRollBack($e, $order);
                return response([
                    'status' => 'error',
                    'field' => $e->getErrors()
                ]);
            } catch (Exception $e) {
                $this->logOrderRollBack($e, $order);
                Exceptions::createFromThrowable($e);

                if (inDevelopment() !== false) {
                    throw $e;
                }

                $message = $e->getMessage();
                if (str_contains($message, 'SQL')) {
                    $message = __('sf.err.unexpected');
                }

                return response([
                    'status' => 'error',
                    'msg' => $message
                ]);
            }

            try {
                $paymentRequest = $order->payment->paymentServerRequest($order, $orderModel->getExecutionTime());
                $payment = Payments::createFromRequest($paymentRequest);
            } catch (Throwable $e) {
                $order->fill([
                    'note_administrator' => $e->getMessage(),
                ])->changeStatus(OrderStatus::CANCELLED, true);
                PaymentsLog::createFromThrowable($e, 'Checkout Payments::createFromRequest');
                throw $e;
            }
        }

        try {
            $gateway = PaymentGateway::provider($payment_provider->provider);
            $response = $gateway->purchase($payment);
        } catch (Throwable $throwable) {
            $order->fill([
                'note_administrator' => $throwable->getMessage(),
            ]); // saved on payment sync
            $payment->status = Payments::STATUS_FAILED;
            $payment->sync();

            // Do not store logs from Borica, they are handled internally
            if ($payment_provider->provider != BoricaWay4Service::KEY) {
                PaymentsLog::createFromThrowable($throwable, 'Checkout Payments::purchase');
            }

            if (str_contains($throwable->getMessage(), 'timed out')) {
                PaymentsLog::createFromThrowable($throwable, 'Checkout Payments::timed_out');
                $throwable = new Error(__('sf.checkout.gateway.no_response'));
            }

            throw $throwable;
        }

        if (empty($response)) {
            $e = new Error(__('sf.checkout.gateway.no_response'));
            PaymentsLog::createFromThrowable($e, 'Checkout Payments::no_response');
            throw $e;
        }

        if (is_array($response) && !empty($response['payment']['payment_id'])) {
            $order->payment->update([
                'payment_id' => $response['payment']['payment_id'],
                'status' => $response['status'],
            ]);
        }

        try {
            event(new OrderCreated($order));
        } catch (Throwable $throwable) {
            PaymentsLog::createFromThrowable($throwable, 'Checkout Payments::OrderCreated');
        }

        if ($response instanceof Response) {
            return $response;
        }

        if ($response['htmlRedirectForm'] ?? false) {
            return new Response([
                'status' => 'success',
                'htmlRedirectForm' => $response['htmlRedirectForm'],
            ]);
        }

        //@todo if $response['action'] === false return error in this check
        if (($response['action']['type'] ?? null) === 'redirect') {
            return response([
                'status' => 'success',
                'redirect' => $response['action']['url']
            ]);
        } elseif ($response['action'] === false) {
            return response([
                'status' => 'success',
                'redirect' => route('checkout.return', [$response['status'], $order->payment->hash])
            ]);
        }

        $e = new Fault('Could not resolve checkout action from payment server! Response: ' . var_export(
            $response,
            true
        ));

        PaymentsLog::createFromThrowable($throwable, 'Checkout Payments::shutdown');

        throw $throwable;
    }

    /**
     * @param DiscountCodeRequest $request
     * @return Response
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function submitDiscountCode(DiscountCodeRequest $request)
    {
        $code = $request->input('discount_code');

        if (!is_null(Discount::findByAll($code, $this->cart))) {
            $this->cart->setDiscountCode($code);
        } else {
            $this->cart->setDiscountContainerCode($code);
        }

        $reload = [
            '.js-checkout-summary-totals',
            '.js-checkout-summary-products',
            '.js-checkout-summary-discount-code',
            '.js-cc-cart-panel',
        ];
        $step = null;
        if ($this->cart->getStep() != 'authorize') {
            $this->cart->setStep('shippingAddress');
            $reload = [
                '.js-checkout-summary-totals',
                '.js-checkout-shipping-address',
                '.js-checkout-shipping',
                '.js-checkout-payment',
                '.js-checkout-summary-products',
                '.js-checkout-summary-discount-code',
                '.js-cc-cart-panel',
            ];
            $step = $this->cart->getStep();
        }

        //        if ($this->cart->getStep() != 'authorize' && setting('checkout_hide_single_shipping') && !is_null($single_check = $this->_checkSingleQuoteShippingManager())) {
//            return $single_check;
//        }

        $response = [
            'status' => 'success',
            //            'replaces' => [
//                [
//                    'selector' => '.js-checkout-summary-discount-code',
//                    'html' => $this->summaryDiscountCode(true)
//                ],
//            ],
            'reload' => $reload,
            'events' => ['cc.checkout.step'],
            'step' => $step,
        ];

        if (str_contains((string) $request->header('referer'), '/cart/')) {
            $response['reload'][] = '[data-widget="cart"], [data-widget="cart-compact"]';
            //            $response['cart'] = $this->cart->resetProducts();
        }

        return response($response);
    }

    /**
     * @param null|string $code
     * @return Response
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function removeDiscountCode($code = null)
    {
        if (!$this->cart) {
            return null;
        }

        $this->cart->removeDiscountCode($code);

        $reload = [
            '.js-checkout-summary-totals',
            '.js-checkout-summary-products',
            '.js-checkout-summary-discount-code',
            '.js-cc-cart-panel',
        ];
        $step = null;
        if ($this->cart->getStep() != 'authorize') {
            $this->cart->setStep('shippingAddress');
            $reload = [
                '.js-checkout-summary-totals',
                '.js-checkout-shipping-address',
                '.js-checkout-shipping',
                '.js-checkout-payment',
                '.js-checkout-summary-products',
                '.js-checkout-summary-discount-code',
                '.js-cc-cart-panel',
            ];
            $step = $this->cart->getStep();
        }

        //        if ($this->cart->getStep() != 'authorize' && setting('checkout_hide_single_shipping') && !is_null($single_check = $this->_checkSingleQuoteShippingManager())) {
//            return $single_check;
//        }

        $response = [
            'status' => 'success',
            'replaces' => [
                //                [
//                    'selector' => '.js-checkout-summary-discount-code',
//                    'html' => $this->summaryDiscountCode(true)
//                ],
//                [
//                    'selector' => '.js-checkout-summary-totals',
//                    'html' => $this->summaryTotals(true)
//                ],
            ],
            'reload' => $reload,
            'events' => ['cc.checkout.step'],
            'step' => $step,
        ];

        if (str_contains(request()->header('referer'), '/cart/')) {
            $response['reload'][] = '[data-widget="cart"], [data-widget="cart-compact"]';
            //            $response['cart'] = $this->cart->resetProducts();
        }

        return response($response);
    }

    /**
     * @param boolean $include
     * @return Response|string
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function summary($include = false)
    {
        $html = \Illuminate\Support\Facades\View::make('global::checkout.include.summary')->render();
        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param boolean $include
     * @return Response|string
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function summaryTotals($include = false)
    {
        $html = \Illuminate\Support\Facades\View::make('global::checkout.include.summary.totals')->render();
        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param boolean $include
     * @return Response|string
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function summaryDiscountCode($include = false)
    {
        $html = \Illuminate\Support\Facades\View::make('global::checkout.include.summary.discount_code')->render();
        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param boolean $include
     * @return Response|string
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function summaryProducts($include = false)
    {
        $html = \Illuminate\Support\Facades\View::make('global::checkout.include.summary.products')->render();
        if ($include) {
            return $html;
        }

        return response([
            'status' => 'success',
            'html' => $html
        ]);
    }

    /**
     * @param LoginRequest $request
     * @return Response|null
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function loginPost(LoginRequest $request)
    {
        $old_customer = $this->cart->customer ?? null;
        $customer = Customer::customerEmail($request->input('email'))->first();
        if (!$customer->active) {
            throw new Error(__('sf.err.account.inactive'), 'email');
        }

        $customer = $customer->loginByPassword($request->input('password'), $request->input('remember'));

        if ($this->cart) {
            $this->cart->setRelation('customer', $customer);
        }

        if ($old_customer && $old_customer->isEmpty()) {
            $old_customer->delete();
        }

        return $this->authorizeResponse($request, 'cc.user.sign.in');
    }


    /**
     * @param RegisterRequest $request
     * @return Response|null
     * @throws Error
     * @throws Fault
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function registerPost(RegisterRequest $request)
    {
        $old_customer = $this->cart->customer ?? null;
        $customer = Customer::register($request->input(), true, true);

        if ($this->cart) {
            $this->cart->setRelation('customer', $customer);
            $this->cart->update(['user_id' => $customer->id]);
        }

        if ($old_customer && $old_customer->isEmpty()) {
            $old_customer->delete();
        }

        return $this->authorizeResponse($request, 'cc.user.sign.in');
    }

    /**
     * @param GuestAuthorizeRequest $request
     * @return Response|null
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function guestPost(GuestAuthorizeRequest $request)
    {
        $this->cart = $this->cart ?: CartModel::instance(true);
        $this->cart->setGuest(array_merge([
            'email' => $request->input('email'),
        ], array_filter([
                'first_name' => $request->input('checkout.shipping.address.first_name'),
                'last_name' => $request->input('checkout.shipping.address.last_name'),
                'phone' => $request->input('checkout.shipping.address.phone'),
            ])));

        return $this->authorizeResponse($request, 'cc.guest.sign.in');
    }

    /**
     * @param Request $request
     * @return Response
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpUnused
     */
    public function officesAutocomplete(Request $request)
    {
        $query = $request->query('query');

        $results = collect();
        /** @var AbstractManager[] $managers */
        $cart = $this->cart ?: (new CartModel());
        //        $managers = \OmniShip::getManagersGroupByType($cart->zone_information_shipping)
//            ->get(AbstractManager::SUPPORT_OFFICE);
        $managers = OmniShip::getManagersGroupByType()
            ->get(AbstractManager::SUPPORT_OFFICE);

        if ($managers) {
            foreach ($managers as $manager) {
                try {
                    /** @var Collection $query */
                    if (!empty($result = $manager->findOffices($query))) {
                        $results = $results->merge($result);
                    }
                } catch (Throwable $e) {
                    Logs::createFromThrowable($e, 'Shipping Error', null, [
                        'data' => [
                            'query' => $query,
                            'manager' => $manager->getKey(),
                            'cart' => $cart->getNewCartKey()
                        ],
                    ]);
                }
            }
        }


        /** @var Collection $results */
        $results = $results->filter(fn(Office $office): bool => $office->getMaxWeight() >= ($this->cart->weight_input ?? 0))->map(function (Office $office): array {
            $image = OmniShip::get($office->getProvider())->getProvider([])->getImage('150x150');
            if (!str_contains($image, '?')) {
                $image .= '?' . app('last_build');
            }

            return [
                'key' => $office->getId(),
                'provider' => $office->getProvider(),
                'id' => $office->getProvider() . '-' . $office->getId(),
                'name' => $office->getName() . ' (' . $office->getAddressString() . ')',
                'image' => $image,
                'country_id' => $office->getCountry()->getId(),
                'country_iso' => $office->getCountry()->getIso2(),
                'country_name' => $office->getCountry()->getName(),
                'city_id' => $office->getCity()->getId(),
                'city_name' => $office->getCity()->getName(),
            ];
        })->sortBy('name')->values();

        return response([
            'results' => $results,
            'more' => false
        ]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|Response
     * @throws Error
     * @throws Throwable
     */
    public function lockersAutocomplete(Request $request)
    {
        $query = $request->query('query');
        $ipInfo = (new GeoIpInformation())->getZoneInformation();
        $lat = $request->query('lat', $ipInfo->getLat());
        $lng = $request->query('lng', $ipInfo->getLng());
        $provider = $request->query('provider');


        $results = collect();
        /** @var AbstractManager[] $managers */
        $cart = $this->cart ?: (new CartModel());
        $managers = OmniShip::getManagersGroupByType()
            ->get(AbstractManager::SUPPORT_LOCKERS);

        if (!empty($provider)) {
            $managers = $managers->filter(function (AbstractManager $manager) use ($provider) {
                if (in_array($manager->getKey(), $provider)) {
                    return $manager;
                }
            });
        }

        if ($managers) {
            foreach ($managers as $manager) {
                try {
                    /** @var Collection $query */
                    if (!empty($result = $manager->findLockers($query, null))) {
                        $results = $results->merge($result);
                    }
                } catch
                (Throwable $e) {
                    Logs::createFromThrowable($e, 'Shipping Error', null, [
                        'data' => [
                            'query' => $query,
                            'manager' => $manager->getKey(),
                            'cart' => $cart->getNewCartKey()
                        ],
                    ]);
                }
            }
        }


        /** @var Collection $results */
        $results = $results->filter(fn(Office $office): bool => $office->getMaxWeight() >= ($this->cart->weight_input ?? 0))->map(function (Office $office) use ($lat, $lng): array {
            $image = OmniShip::get($office->getProvider())->getProvider([])->getImage('150x150');
            if (!str_contains($image, '?')) {
                $image .= '?' . app('last_build');
            }

            return [
                'key' => $office->getId(),
                'provider' => $office->getProvider(),
                'id' => $office->getProvider() . '-' . $office->getId(),
                'name' => $office->getName(),
                'nameAddress' => $office->getName() . ' (' . $office->getAddressString() . ')',
                'image' => $image,
                'country_id' => $office->getCountry()->getId(),
                'country_iso' => $office->getCountry()->getIso2(),
                'country_name' => $office->getCountry()->getName(),
                'city_id' => $office->getCity()->getId(),
                'city_name' => $office->getCity()->getName(),
                'latitude' => $office->getLatitude(),
                'longitude' => $office->getLongitude(),
                'address' => $office->getAddressString(),
                'distance' => geoDistance($office->getLatitude(), $office->getLongitude(), $lat, $lng, site('unit_system')),
                'unit_system' => site('unit_system'),
                'unit_system_text' => Unit::getLength()[LengthUnit::KILOMETRE],
            ];
        })->sortBy('distance')->values();

        return response([
            'results' => $results,
            'more' => false,
            'position' => [
                'lat' => $lat,
                'lng' => $lng
            ]
        ]);
    }

    //    /**
//     * @param Request $request
//     * @param $machine
//     * @return
//     * @throws Error
//     * @throws Throwable
//     */
//    public function newOfficesAutocomplete(Request $request, $machine)
//    {
//        $ipInfo = (new GeoIpInformation())->getZoneInformation();
//        $lat = $request->query('lat', $ipInfo->getLat());
//        if(!is_numeric($lat)) {
//            $lat = $ipInfo->getLat();
//        }
//        $lng = $request->query('lng', $ipInfo->getLng());
//        if(!is_numeric($lng)) {
//            $lng = $ipInfo->getLat();
//        }
//
//        /** @var AbstractManager[] $managers */
//        $cart = $this->cart ?: (new CartModel());
//        $managers = \OmniShip::getManagersGroupByType()
//            ->get($machine ? AbstractManager::SUPPORT_LOCKERS : AbstractManager::SUPPORT_OFFICE);
//
//        $images = $managers->map(function(AbstractManager $manager) {
//            $image = $manager->getProvider()->getImage('150x150');
//            if (strpos($image, '?') === false) {
//                $image .= '?' . app('last_build');
//            }
//            return $image;
//        });
//
//        $providers = collect();
//        $results = collect();
//        if ($managers && $managers->isNotEmpty()) {
//            $language = site('language');
//            $unitSystem = site('unit_system');
//            $unitSystemText = Unit::getLength()[LengthUnit::KILOMETRE];
//            $results = OmniOffice::getFromProviders($managers, $machine)->map(function (OmniOffice $office) use ($images, $providers, $managers, $language, $unitSystem, $unitSystemText) {
//                if($office->max_weight < ($this->cart->weight_input ?? 0)) {
//                   return null;
//                }
//
//                if(!is_numeric($office->latitude) || !is_numeric($office->longitude)) {
//                    return null;
//                }
//
//                if (!$providers->has($office->provider)) {
//                    $providers->put($office->provider, [
//                        'provider' => $office->provider,
//                        'name' => optional($managers->get($office->provider))->getProvider()->name ?? (site('language') == 'bg' ? $office->name : $office->name_en),
//                        'image' => $images->get($office->provider),
//                        'support' => optional($managers->get($office->provider))->getSupportType()
//                    ]);
//                }
//
//                return [
//                    'key' => $office->key,
//                    'provider' => $office->provider,
//                    'id' => $office->office_id,
//                    'hash' => $office->hash,
//                    'name' => ($name = $language == 'bg' ? $office->name : $office->name_en),
//                    'address' => ($address = $language == 'bg' ? $office->address : $office->address_en),
//                    'nameAddress' => $name . ' (' . $address . ')',
//                    'image' => $images->get($office->provider),
//                    'latitude' => $office->latitude,
//                    'longitude' => $office->longitude,
//                    'unit_system' => $unitSystem,
//                    'unit_system_text' => $unitSystemText,
//                    'meta' => $office->meta,
//                ];
//            })->filter()->values();
//        }
//
//        return response()->json([
//            'results' => $results,
//            'more' => false,
//            'position' => [
//                'lat' => $lat,
//                'lng' => $lng
//            ],
//            'providers' => $providers
//        ]);
//    }

    /**
     * @param Request $request
     * @param $machine
     * @return JsonResponse
     * @throws Error
     * @throws Throwable
     */
    public function newOfficesAutocomplete(Request $request, $machine): JsonResponse
    {
        $ipInfo = (new GeoIpInformation())->getZoneInformation();
        $lat = $request->query('lat', $ipInfo->getLat());
        if (!is_numeric($lat)) {
            $lat = $ipInfo->getLat();
        }

        $lng = $request->query('lng', $ipInfo->getLng());
        if (!is_numeric($lng)) {
            $lng = $ipInfo->getLat();
        }

        $results = collect();
        /** @var AbstractManager[] $managers */
        $cart = $this->cart ?: (new CartModel());
        $managers = OmniShip::getManagersGroupByType()
            ->get($machine ? AbstractManager::SUPPORT_LOCKERS : AbstractManager::SUPPORT_OFFICE);

        foreach ($cart->products as $item) {
            $restrictions = ($item->product->category->restrictions ?? collect())
                ->where('restriction_type', 'shipping')->pluck('restriction_id', 'restriction_id');
            if ($restrictions->isEmpty()) {
                continue;
            }

            $managers = $managers->filter(fn(AbstractManager $manager) => $restrictions->has($manager->getId()));
        }

        if ($managers && $managers->isNotEmpty()) {
            foreach ($managers as $manager) {
                try {
                    /** @var Collection $query */
                    if (!empty($result = $manager->allOffices($machine))) {
                        $results = $results->merge($result);
                    }
                } catch (Throwable $e) {
                    Logs::createFromThrowable($e, 'Shipping Error', null, [
                        'data' => [
                            'manager' => $manager->getKey(),
                            'cart' => $cart->getNewCartKey()
                        ],
                    ]);
                }
            }
        }

        $providers = collect();
        $unit_system = site('unit_system');
        $unit_system_text = Unit::getLength()[LengthUnit::KILOMETRE];

        /** @var Collection $results */
        $results = $results->filter(fn(Office $office): bool => $office->getMaxWeight() >= ($this->cart->weight_input ?? 0))->map(function (Office $office) use ($lat, $lng, &$providers, $managers, $machine, $unit_system, $unit_system_text) {
            if (!is_numeric($office->getLatitude()) || !is_numeric($office->getLongitude())) {
                return;
            }

            if (!$providers->has($office->getProvider())) {
                $image = OmniShip::get($office->getProvider())->getProvider([])->getImage('150x150');
                if (!str_contains($image, '?')) {
                    $image .= '?' . app('last_build');
                }

                $providers->put($office->getProvider(), [
                    'provider' => $office->getProvider(),
                    'name' => optional($managers->get($office->getProvider()))->getProvider()->name ?? $office->getName(),
                    'image' => $image,
                    'support' => optional($managers->get($office->getProvider()))->getSupportType()
                ]);
            }

            return [
                'key' => $office->getId(),
                'provider' => $office->getProvider(),
                'id' => $office->getProvider() . '-' . $office->getId(),
                'hash' => md5($office->getProvider() . '-' . $office->getId() . '-' . intval($machine)),
                'name' => $office->getName(),
                'address' => $office->getAddressString(),
                'nameAddress' => $office->getName() . ' (' . $office->getAddressString() . ')',
                'image' => $providers->get($office->getProvider())['image'],
                'country' => optional($office->getCountry())->toArray(),
                'city' => optional($office->getCity())->toArray(),
                'latitude' => $office->getLatitude(),
                'longitude' => $office->getLongitude(),
                'unit_system' => $unit_system,
                'unit_system_text' => $unit_system_text,
                'meta' => optional($office->getMeta())->toArray(),
                'maps_icon' => $office->getParameter('maps_icon'),
            ];
        })->filter()->values();

        return response()->json([
            'results' => $results,
            'more' => false,
            'position' => [
                'lat' => $lat,
                'lng' => $lng
            ],
            'providers' => $providers
        ]);
    }


    /**
     * @return string
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function authorizeEmpty()
    {
        return \Illuminate\Support\Facades\View::make('global::checkout.steps.empty.authorize')->render();
    }

    /**
     * @return string
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function shippingAddressEmpty()
    {
        return \Illuminate\Support\Facades\View::make('global::checkout.steps.empty.shipping-address')->render();
    }

    /**
     * @return string
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function billingAddressEmpty()
    {
        return \Illuminate\Support\Facades\View::make('global::checkout.steps.empty.billing-address')->render();
    }

    /**
     * @return string
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function shippingEmpty()
    {
        return \Illuminate\Support\Facades\View::make('global::checkout.steps.empty.shipping')->render();
    }

    /**
     * @return string
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function paymentEmpty()
    {
        return \Illuminate\Support\Facades\View::make('global::checkout.steps.empty.payment')->render();
    }

    /**
     * @param Request $request
     * @param string $event
     * @return Response|null
     * @throws Error
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection PhpUnusedParameterInspection
     */
    public function authorizeResponse(Request $request, $event)
    {
        if (!$this->cart) {
            $this->cart = CartModel::instance(true);
        }

        //@todo set step
        $this->cart->setStep('authorize');
        $response = [
            'status' => 'success',
            //            'replaces' => [
//                [
//                    'selector' => '.js-checkout-authorize',
//                    'html' => $this->authorize(true),
//                ]
//            ],
            'reload' => [
                '.js-checkout-authorize',
                '.js-checkout-container .js-checkout-shipping-address',
                '.js-checkout-container .js-checkout-billing-address',
                '.js-checkout-shipping',
                '.js-checkout-payment',
                '.js-checkout-summary',
            ],
            'events' => [$event, 'cc.checkout.step'],
        ];

        if (session('policies_popup')) {
            $response['replaces'][] = [
                'selector' => '.js-checkout-policies-popup',
                'html' => GDPR::policiesPopup()
            ];
        }

        if (!$this->cart->customer->isGuest()) {
            $check = $this->_checkSupportsTypes();
            if ($check->has(AbstractManager::SUPPORT_ADDRESS)) {
                if ($this->cart->has_shippable && $this->cart->customer->default_address_id) {
                    $this->cart->setStep('shippingAddress', false);
                    $this->cart->setShippingAddress($this->cart->customer->shipping_address->id, false);
                    $this->cart->setShippingType(AbstractManager::SUPPORT_ADDRESS);

                    $response['replaces'][] = [
                        'selector' => '.js-checkout-shipping-address',
                        'html' => $this->shippingAddress(true)
                    ];

                    if (($index = array_search('.js-checkout-container .js-checkout-shipping-address', $response['reload'], true)) !== false) {
                        unset($response['reload'][$index]);
                    }

                    //unset($response['reload'][0]);
//                    if (setting('checkout_hide_single_shipping') && !is_null($single_check = $this->_checkSingleQuoteShippingManager())) {
//                        $response['replaces'][] = [
//                            'selector' => '.js-checkout-shipping',
//                            'html' => $this->shipping(true)
//                        ];
//                        unset($response['reload'][2]);
//                        $response['events'][] = 'cc.overlay.hide';
//                    }
                    $response['events'][] = 'cc.overlay.hide';

                    $response['reload'] = array_values($response['reload']);
                } elseif (!$this->cart->has_shippable && $this->cart->has_digital && $this->cart->customer->default_billing_address_id) {
                    $this->cart->setStep('billingAddress', false);
                    $this->cart->setBillingAddress($this->cart->customer->billing_address->id);

                    $response['replaces'][] = [
                        'selector' => '.js-checkout-billing-address',
                        'html' => $this->billingAddress(true)
                    ];

                    if (($index = array_search('.js-checkout-container .js-checkout-billing-address', $response['reload'], true)) !== false) {
                        unset($response['reload'][$index]);
                    }

                    //unset($response['reload'][1]);
                    $response['reload'] = array_values($response['reload']);
                    $response['events'][] = 'cc.overlay.hide';
                }
            }
        }

        $response['step'] = $this->cart->getStep();

        if ($response['status'] == 'success' && ($messageCart = session('cart_merge_message'))) {
            $response['msg'] = $messageCart;
        }

        return response($response);
    }

    protected function isBotPod(): bool
    {
        return str_contains(gethostname(), 'builder-google') || new CrawlerDetectCore()->isCrawler();
    }

    /**
     * @param string $status
     * @param string $payment_hash
     * @return Response|View
     * @todo get from widget checkout emails to send on return page!
     */
    public function returnPage(string $status, string $payment_hash)
    {
        if ($this->isBotPod()) {
            app()->abort(404, __('sf.global.err.order_no_longer_exists'));
        }

        //        if(inDevelopment()) {
//            return redirect()->route('checkout');
//        }

        $order_payment = OrderPayment::getForReturnPage($payment_hash);

        if ($order_payment === null) {
            throw new HttpNotFound(__('sf.global.err.order_no_longer_exists'));
        }

        Registry::set('checkout.order_payment', $order_payment);

        $order = $order_payment->order;

        //        dd(
//            $order->returnArray()
//        );

        $isOrderDraft = $order->meta_pluck->get('is_draft');
        if ($status !== 'cancel') {
            if ($isOrderDraft) {
                $order->removeMeta('is_draft');
            }

            if ($this->cart) {
                if (Apps::installed('bumper_offer') && Apps::enabled('bumper_offer')) {
                    BumperOfferManager::orderCompletion($this->cart, $order->id);
                }

                if ($this->cart->key == $order->meta_pluck->get('cart_key')) {
                    $this->cart->delete();
                    CartModel::whereUserId($this->cart->user_id)->delete();
                }
            }
        } else {
            $cancel_message = __('sf.checkout.header.status_cancelled');
            if ($order->payment->provider === 'cib_bank') {
                $providerData = $order->payment->payment->provider_data;

                if (isset($providerData->TRID)) {
                    $cancel_message .= ' | TRID: ' . $providerData->TRID;
                }
                if (isset($providerData->RC)) {
                    $cancel_message .= ' | RC: ' . $providerData->RC;
                }
                if (isset($providerData->RT)) {
                    $cancel_message .= ' | RT: ' . $providerData->RT;
                }
            }

            session()->flash('payment_canceled', $cancel_message);

            if ($isOrderDraft) {
                return $this->to(route('checkout.order', ['order' => $order->id, 'hash' => encrypt($order->order_number)]));
            }

            return $this->to(route('checkout'));
        }

        //Peter Iliev  02.11.2021 9:58 AM
        //@g.nachev може ли да спреш временно инвалидирането на кеша при поръчка
        //$order::clearCache();

        if (YesNo::False == $order->email_sent) {
            SendCustomerNotification::sendCreateNewOrder($order);
            if (in_array($order_payment->status, [Payments::STATUS_AUTHORIZED, Payments::STATUS_COMPLETED])) {
                SendCustomerNotification::sendCreateNewOrderCompleted($order);
                /** @var Collection $digital */
                if (($digital = $order->products->where('digital', YesNo::True)) && $digital->count()) {
                    SendCustomerNotification::sendOrderFilesDownloadLink($order);
                }
            }

            if (!in_array($order_payment->status, [Payments::STATUS_AUTHORIZED, Payments::STATUS_COMPLETED]) && !in_array($order_payment->provider, ['bwt', 'cod', 'pop']) && $order_payment->payment_provider->group != 'credit') {
                SendCustomerNotification::sendCreateNewOrderPaymentError($order);
            }

            if ('cancel' != $status && $order->status != 'pending') {
                try {
                    event(new OrderStatusChangeReturn($order, 'pending'));
                } catch (Throwable) {
                }
            }

            $order->update(['email_sent' => YesNo::True]);
        }

        if (Apps::installed('membership')) {
            $order->products->map(function ($product): void {
                if ($product->digital == 'yes' && $product->product && $product->product->type_digital == 'page') {
                    $pages = $product->product->pages()->pluck('days', 'page_id')->toArray();
                    $product->pages = Page::whereIn('id', array_keys($pages))->pluck('name', 'url_handle');
                    $product->days = isset(array_values($pages)[0]) ? array_values($pages)[0] * $product->quantity : 0;
                    $product->date_expired = $product->days > 0 ? Carbon::now()->addDays($product->days)->format('d.m.Y') : null;
                }
            });
        }

        if ($status !== 'cancel') {
            $order->setAttribute('google_analitycs_tracking', $order->meta_pluck->get('google_analitycs_tracking'));
            if (!$order->getAttribute('google_analitycs_tracking')) {
                $order->createMeta('google_analitycs_tracking', 'track');
            }
        }

        if ('cancel' != $status && ($page = CheckoutSystemPages::getThankYouPage($order))) {
            return $page;
        }

        $order_details = null;
        if ($status != 'cancel') {
            $order_details = \Illuminate\Support\Facades\View::make('global::checkout.return.order.details', [
                'customer' => $order->customer,
                'order' => $order,
                'TRACK17' => ShippingProvider::TRACK17,
            ])->render();
        }

        $order_digital_products_pages = 0;
        $order_digital_products_files = [];
        if ($order_payment->status === 'completed') {
            $order_digital_products_pages = $order->products()->where('digital', YesNo::True)
                ->whereHas('product', fn($product) => $product->where('type_digital', 'page'))->count();

            $order_digital_products_files = $order->products()->where('digital', YesNo::True)
                ->whereHas('product', fn($product) => $product->where('type_digital', 'file'))->pluck('id')->all();
        }

        return \Illuminate\Support\Facades\View::mainResponse('global::checkout.return', [
            'order_payment' => $order_payment,
            'order' => $order,
            'products' => $order->products,
            'order_details' => $order_details,
            'order_digital_products_files' => $order_digital_products_files,
            'order_digital_products_pages' => $order_digital_products_pages,
        ]);
    }

    /**
     * @param Request $request
     * @param $creditor
     * @return RedirectResponse|Response
     * @throws Throwable
     */
    public function creditor(Request $request, $creditor)
    {
        if (!$request->ajax()) {
            return $this->route('checkout');
        }

        $facade = \Illuminate\Support\Str::studly($creditor);
        if (!class_exists($facade)) {
            return response('Invalid request', 422);
        }

        /** @var $widget Leasing */
        $widget = Widget::get('store.leasing');
        /** @var Collection $products */
        $products = $this->cart->products->pluck('product_id', 'product_id');
        $form = $widget->renderCheckoutForm(
            $creditor,
            $this->cart->getTotal('input'),
            $products->all(),
            0,
            0,
            true
        );
        $name = PaymentProviderConfiguration::findByProvider($creditor)->storefront_name ?? $creditor;
        return response([
            'status' => 'success',
            'html' => $form,
            'title' => $name,
        ]);
    }

    /**
     * @param CreditorRequest $request
     * @param $provider
     * @return Response
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpMissingParamTypeInspection
     */
    public function submitCreditor(CreditorRequest $request, $provider)
    {
        $this->cart->setStep('payment', false);

        $payment_provider = PaymentProviders::whereProvider($provider)->firstOrFail();
        $this->cart->setLeasing($payment_provider->id, $request->input());

        $reload = [
            '.js-checkout-payment',
            '.js-checkout-summary-totals',
        ];

        if (isZora() && \Bnp::isPos2Enabled()) {
            $reload[] = '.js-checkout-summary-products';
        }

        return response([
            'status' => 'success',
            'reload' => $reload,
        ]);
    }

    /**
     * @return Response
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function unconfirmedAccountsRestrict()
    {
        return \Illuminate\Support\Facades\View::modal('global::checkout.authorize.unconfirmed-accounts-restrict', [
            'customer' => $this->cart->customer
        ], null, false);
    }

    /**
     * @return Response
     * @noinspection PhpMissingReturnTypeInspection PhpUnused
     */
    public function unconfirmedAccountsRestrictUpdate()
    {
        try {
            $confirm_mail_sent = $this->cart->customer->unconfirmedAccountsRestrict();

            if ($confirm_mail_sent[1] ?? null) {
                $success_msg = __('sf.widget.account.succ.details_changed_and_mail_confirmation_sent_to_new_email');
            } else {
                $success_msg = __('sf.widget.account.succ.details_changed');
            }

            return response([
                'status' => 'success',
                'events' => ['cc.user.details.updated'],
                'msg' => $success_msg
            ]);
        } catch (Exception $exception) {
            return response([
                'status' => 'error',
                'msg' => $exception->getMessage()
            ]);
        }
    }

    /**
     * @return array
     * @noinspection PhpMissingReturnTypeInspection
     */
    public function getSeo()
    {
        if (activeRoute('checkout.return')) {
            return [
                'title' => __('seo.checkout.return.title'),
                'description' => __('seo.checkout.return.description')
            ];
        } else {
            return [
                'title' => __('seo.checkout.checkout.title'),
                'description' => __('seo.checkout.checkout.description')
            ];
        }
    }

    /**
     * @return Response
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection PhpUnused
     */
    public function countdownDiscountPopup()
    {
        /**@var Discount $countdownDiscount */
        $countdownDiscount = $this->cart ? $this->cart->getCountdownDiscount() : false;
        if ($countdownDiscount && !$this->cart->getMeta('countdown_discount_popup_was_shown')) {
            $this->cart->setMeta('countdown_discount_popup_was_shown', 1);
            $this->cart->setMeta('countdown_popup_first_showing', Carbon::now('UTC')->toDateTimeString());

            return \Illuminate\Support\Facades\View::modal('discounts.countdown.checkout_popup', [
                'message' => $countdownDiscount->meta->get('countdown_description'),
                'popup_effect' => $countdownDiscount->meta->get('countdown_popup_effect'),
            ], null, false);
        }

        return null;
    }

    /**
     * @return array
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection
     */
    private function _getSteps(): array
    {
        $steps_array = [
            'authorize',
            'shippingAddress',
            'billingAddress',
            'shipping',
            'payment',
        ];

        $steps = [];
        $empty = null;
        foreach ($steps_array as $step) {
            //remove shipping step if no shippable products
            if (!$this->cart->has_shippable && in_array($step, ['shippingAddress', 'shipping'])) {
                continue;
            }

            if ($this->cart->hasGuestEmailInShippingForm() && $step == 'authorize') {
                $steps[$step] = $this->authorizeEmpty();
                continue;
            }

            if ($step == 'billingAddress' && $this->cart->hide_billing_address) {
                if ($this->cart->customer) {
                    $this->cart->setStep('billingAddress');
                }

                $empty = true;
            }

            if ($this->cart->getStep() == 'billingAddress' && $step == 'payment' && $this->cart->has_shippable && $this->cart->getShipping()) {
                $empty = true;
            }

            $steps[$step] = $this->{$step . ($empty !== true ? $empty : '')}(true);
            if ($empty === true) {
                $empty = 'Empty';
                if ($step == 'billingAddress' && !$this->cart->hasBillingAddress()) {
                    $empty = true;
                }
            }

            if ($step == 'authorize' && $step == $this->cart->getStep() && !$this->cart->customer) {
                $empty = 'Empty';
            }

            if ($empty != 'Empty' && $step == $this->cart->getStep()) {
                $empty = true;
            }

            if ($step == 'billingAddress' && $step == $this->cart->getStep() && $this->cart->hasBillingAddress()) {
                $empty = true;
            } elseif (!$this->cart->hasBillingAddress() && $step == $this->cart->getStep() && $step == 'shippingAddress') {
                $empty = true;
            } elseif ($this->cart->hasBillingAddress() && $this->cart->getBillingAddress() && $step == $this->cart->getStep() && $step == 'shippingAddress') {
                $empty = null;
            }
        }

        return $steps;
    }

    /**
     * @return Collection
     * @throws Throwable
     * @noinspection PhpMissingReturnTypeInspection
     */
    private function _checkSupportsTypes()
    {
        //https://cloudcartad.atlassian.net/jira/software/projects/BUG/boards/18?assignee=70121%3Ad931f9e9-cbbd-467a-859b-1a9a0128d033&selectedIssue=BUG-978
//        $types = \OmniShip::getSupportTypes($this->cart->zone_information_shipping);
        $types = OmniShip::getSupportTypes();
        //@todo if no methods, add to address
        if (!$types->count()) {
            $types = OmniShip::getSupportTypes()->where('key', AbstractManager::SUPPORT_ADDRESS);
        }

        return $types;
    }

    /**
     * @param Order|null $order
     * @param Error|Exception $e
     * @return void|null
     */
    protected function logOrderRollBack(\Throwable $e, ?Order $order = null)
    {
        if ($order === null) {
            return null;
        }

        Logs::createFromThrowable($e, null, sprintf('Order rollback #%d - %s', $order->id, $e->getMessage()), [
            'data' => request()->all(),
        ]);
    }

    /**
     * @param mixed $type
     * @return mixed
     */
    protected function getStore($type)
    {
        if (\Apps::installed('store_locations') && \Cookie::has('store_location')) {
            $cart = CartModel::findOrFail($this->cart->id ?? null);
            $CheckZone = ZoneStores::getByZone($geoZoneInformation = $cart->getZoneInformationShipping());
            if (empty($CheckZone)) {
                Logs::createFromThrowable(new Exception('Unable to find store by location (' . strval($type) . ')'), 'Checkout Error - Zone Stores', null, [
                    'data' => [
                        'cart' => $geoZoneInformation ? $geoZoneInformation->toArray() : null,
                        'cookie' => (array) StoreLocationsManager::getCookieData(),
                    ],
                ]);

                if ($type == 'change_shipping') {
                    throw new Error(__('store_locations::store_locations.checkout.address.error'));
                } elseif ($type == 'submit_shipping') {
                    return response([
                        'status' => 'error',
                        'msg' => __('store_locations::store_locations.checkout.address.error')
                    ]);
                }
            }

            $actualZone = StoreLocationsManager::getCookieData()->geo_zone_id ?? null;
            if ($CheckZone != $actualZone) {
                $address = $cart->getShippingAddress();
                if (!empty($address)) {
                    $UpdateCookie = [
                        'geo_zone_id' => $CheckZone,
                        'country_name' => $address->country_name,
                        'country_iso2' => $address->country_iso2,
                        'country_iso3' => $address->country_iso3,
                        'state_id' => $address->state_id,
                        'state_name' => $address->state_name,
                        'state_iso2' => $address->state_iso2,
                        'city_id' => $address->city_id,
                        'city_name' => $address->city_name,
                        'geo_name_city_id' => $address->geo_name_city_id,
                        'quarter_id' => $address->quarter_id,
                        'quarter_name' => $address->quarter_name,
                        'street_id' => $address->street_id,
                        'street_name' => $address->street_name,
                        'street_number' => $address->street_number,
                        'post_code' => $address->post_code,
                        'neighborhood' => $address->neighborhood,
                        'building' => $address->building,
                        'entrance' => $address->entrance,
                        'floor' => $address->floor,
                        'apartment' => $address->apartment,
                        'locality' => $address->locality,
                        'text' => $address->text,
                        'latitude' => $address->latitude,
                        'longitude' => $address->longitude,
                        'address1' => $address->address1
                    ];
                    $cookie = \Cookie::make('store_location', json_encode($UpdateCookie), 1440);
                    \Cookie::queue($cookie);
                }
            }
        }

        return true;
    }
}
