<div class="sidebar">
    <div class="box" id="widget-slider-options">
        <div class="box-title">
            <div class="box-title-text">
                <h5>{$widget->getWidgetName()}</h5>
            </div>

            <div class="box-title-addon">
                {if $widget->canDisable()}
                    {include file="./includes/enabled.tpl"}
                {/if}
            </div>
        </div>

        <div class="box-section">
            <div class="row form-group-dynamic">
                <div class="col-lg-12 col-xs-4 form-group">
                    <label class="control-label">{t}widget.extra.carousel.label.amount{/t} <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}widget.extra.carousel.help.amount{/t}"></i></label>

                    <div class="skip-has-error">
                        <select name="amount" class="form-control select2me" data-no-input="true">
                            {for $amount=1 to 20}
                            <option value="{$amount}"{if $widget->getSetting('amount') == $amount} selected="selected"{/if}>{$amount}</option>
                            {/for}
                        </select>
                    </div>
                </div>

                <div class="col-lg-12 col-xs-4 form-group">
                    <label class="control-label">{t}widget.extra.carousel.label.full_width{/t} <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}widget.extra.carousel.help.full_width{/t}"></i></label>

                    <div class="skip-has-error">
                        <select name="full_width" class="form-control select2me" data-no-input="true">
                            <option value="yes"{if $widget->getSetting('full_width') == 'yes'} selected="selected"{/if}>{t}global.yes{/t}</option>
                            <option value="no"{if $widget->getSetting('full_width') == 'no'} selected="selected"{/if}>{t}global.no{/t}</option>
                        </select>
                    </div>
                </div>
                <div class="col-lg-12 col-xs-4 form-group">
                    <label class="control-label">{t}widget.extra.carousel.label.slides_per_view{/t} <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}widget.extra.carousel.help.slides_per_view{/t}"></i></label>

                    <div class="skip-has-error">
                        <select name="slides_per_view" class="form-control select2me" data-no-input="true">
                            <option value="1"{if $widget->getSetting('slides_per_view') === '1'} selected="selected"{/if}>1</option>
                            <option value="2"{if $widget->getSetting('slides_per_view') === '2'} selected="selected"{/if}>2</option>
                            <option value="3"{if $widget->getSetting('slides_per_view') === '3'} selected="selected"{/if}>3</option>
                            <option value="4"{if $widget->getSetting('slides_per_view') === '4'} selected="selected"{/if}>4</option>
                            <option value="5"{if $widget->getSetting('slides_per_view') === '5'} selected="selected"{/if}>5</option>
                            <option value="6"{if $widget->getSetting('slides_per_view') === '6'} selected="selected"{/if}>6</option>
                            <option value="7"{if $widget->getSetting('slides_per_view') === '7'} selected="selected"{/if}>7</option>
                            <option value="8"{if $widget->getSetting('slides_per_view') === '8'} selected="selected"{/if}>8</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="box-section">
            <div class="row form-group-dynamic">
                <div class="col-lg-12 col-xs-4 form-group">
                    <label class="control-label">{t}widget.extra.carousel.label.caption{/t} <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}widget.extra.carousel.help.caption{/t}"></i></label>

                    <div class="skip-has-error">
                        <select name="caption" class="form-control select2me" data-no-input="true">
                            <option value="yes"{if $widget->getSetting('caption') === 'yes'} selected="selected"{/if}>{t}global.yes{/t}</option>
                            <option value="no"{if $widget->getSetting('caption') === 'no'} selected="selected"{/if}>{t}global.no{/t}</option>
                        </select>
                    </div>
                </div>

                <div class="col-lg-12 col-xs-4 form-group">
                    <label class="control-label">{t}widget.extra.carousel.label.controls{/t} <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}widget.extra.carousel.help.controls{/t}"></i></label>

                    <div class="skip-has-error">
                        <select name="controls" class="form-control select2me" data-no-input="true">
                            <option value="yes"{if $widget->getSetting('controls') === 'yes'} selected="selected"{/if}>{t}global.yes{/t}</option>
                            <option value="no"{if $widget->getSetting('controls') === 'no'} selected="selected"{/if}>{t}global.no{/t}</option>
                        </select>
                    </div>
                </div>

                <div class="col-lg-12 col-xs-4 form-group">
                    <label class="control-label">{t}widget.extra.carousel.label.indicators{/t} <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}widget.extra.carousel.help.indicators{/t}"></i></label>

                    <div class="skip-has-error">
                        <select name="indicators" class="form-control select2me" data-no-input="true">
                            <option value="yes"{if $widget->getSetting('indicators') === 'yes'} selected="selected"{/if}>{t}global.yes{/t}</option>
                            <option value="no"{if $widget->getSetting('indicators') === 'no'} selected="selected"{/if}>{t}global.no{/t}</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="box-section">
            <div class="form-group-dynamic">
                <div class="form-group">
                    <label class="control-label">{t}widget.extra.carousel.label.autoplay{/t} <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}widget.extra.carousel.help.autoplay{/t}"></i></label>

                    <div class="skip-has-error">
                        <select name="autoplay" class="form-control select2me" data-no-input="true">
                            <option value="yes"{if $widget->getSetting('autoplay') === 'yes'} selected="selected"{/if}>{t}global.yes{/t}</option>
                            <option value="no"{if $widget->getSetting('autoplay') === 'no'} selected="selected"{/if}>{t}global.no{/t}</option>
                        </select>
                    </div>
                </div>

                <div class="row js-autoplay-holder">
                    <div class="col-lg-12 col-xs-4 form-group">
                        <label class="control-label">{t}widget.extra.carousel.label.interval{/t} <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}widget.extra.carousel.help.interval{/t}"></i></label>

                        <div class="skip-has-error">
                            <input name="interval" class="form-control" value="{$widget->getSetting('interval')}" placeholder="{t}widget.extra.carousel.ph.interval{/t}"/>
                        </div>
                    </div>

                    <div class="col-lg-12 col-xs-4 form-group">
                        <label class="control-label">{t}widget.extra.carousel.label.cycle{/t} <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}widget.extra.carousel.help.cycle{/t}"></i></label>

                        <div class="skip-has-error">
                            <select name="cycle" class="form-control select2me" data-no-input="true">
                                <option value="yes"{if $widget->getSetting('cycle') === 'yes'} selected="selected"{/if}>{t}global.yes{/t}</option>
                                <option value="no"{if $widget->getSetting('cycle') === 'no'} selected="selected"{/if}>{t}global.no{/t}</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-lg-12 col-xs-4 form-group">
                        <label class="control-label">{t}widget.extra.carousel.label.pause{/t} <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}widget.extra.carousel.help.pause{/t}"></i></label>

                        <div class="skip-has-error">
                            <select name="pause" class="form-control select2me" data-no-input="true">
                                <option value="yes"{if $widget->getSetting('pause') === 'yes'} selected="selected"{/if}>{t}global.yes{/t}</option>
                                <option value="no"{if $widget->getSetting('pause') === 'no'} selected="selected"{/if}>{t}global.no{/t}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-12 col-xs-4 form-group">
                        <label class="control-label">{t}widget.extra.carousel.label.margin{/t} <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}widget.extra.carousel.help.margin{/t}"></i></label>

                        <div class="skip-has-error">
                            <input name="space_between" class="form-control" value="{$widget->getSetting('space_between')}" placeholder="{t}widget.extra.carousel.ph.margin{/t}"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content">
    {$slides = $widget->getSlides()}
    {$pages = $widget->getPages()}
    {$sections = $widget->getSections()}

    {for $i=1 to 15}
        {if !empty($slides[$i])}
            {$slide = $slides[$i]}
        {else}
            {$slide = null}
        {/if}

        <div id="slide_{$i}" class="box js-slide-holder">
            <div class="box-title">
                <div class="box-title-text">
                    <h3>
                        {t}widget.extra.carousel.label.slide{/t} {$i}
                    </h3>
                </div>
            </div>

            <div class="box-section js-caption">
                <div class="form-group">
                    <div class="skip-has-error">
                        <label class="control-label">{t}widget.extra.carousel.label.caption{/t}</label>

                        <input name="slides[{$i}][caption]" class="form-control" value="{$slide['caption']|default}" placeholder="{t}widget.extra.carousel.ph.caption{/t}"/>
                    </div>
                </div>
            </div>

            <div class="tabs-container tabs-slider js-tabs-container">
                <div class="tabs widget-slider-text">
                    <div class="tab js-slide-text-desktop active">
                        <div class="skip-has-error">
                            <textarea name="slides[{$i}][html]" class="tinymce" id="tinymce-{uniqid()}" data-tool-image="true" data-height="300">{$slide.html|default}</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="box-section">
                <div class="row form-group">
                    <div class="col-xs-12">
                        <div class="skip-has-error" id="widget-slider-link-type">
                            <label class="control-label">{t}widget.extra.carousel.label.sorting{/t}</label>

                            <input class="form-control" name="slides[{$i}][sorting]" value="{$slide.sorting|default:0}" type="number" step="1">
                        </div>
                    </div>
                </div>
            </div>

            <div class="box-section">
                <div class="row form-group">
                    <div class="col-xs-6">
                        <label class="control-label">{t}widget.extra.text.label.period.from{/t}</label>

                        <div class="skip-has-error">
                            <input type="text" name="slides[{$i}][from]" class="form_datetime form-control" data-format="{$widget->dateFormat(true)}" value="{$slide.from|default}" placeholder="{t}widget.extra.text.label.period.from{/t}" />
                        </div>
                    </div>

                    <div class="col-xs-6">
                        <label class="control-label">{t}widget.extra.text.label.period.to{/t}</label>

                        <div class="skip-has-error">
                            <input type="text" name="slides[{$i}][to]" class="form_datetime form-control" data-format="{$widget->dateFormat(true)}" value="{$slide.to|default}" placeholder="{t}widget.extra.text.label.period.to{/t}" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {/for}
</div>

<script type="text/javascript">
    $(function () {
        var Parser = {
            youtube: function($el) {
                var val        = $el.val(),
                    matchBegin = '?v=',
                    startIndex = val.indexOf(matchBegin) + matchBegin.length,
                    endIndex   = val.indexOf('&') > startIndex ? val.indexOf('&') : val.length,
                    video_id   = val.substring(startIndex, endIndex);

                return '//youtube.com/embed/' + video_id;
            },
            wistia: function($el) {
                var parse = /wistia.com(.*)wvideoid=([^\&]*)/.exec($el.val());
                if(parse) {
                    return '//fast.wistia.net/embed/iframe/' + parse[2];
                }

                parse = /wistia.com\/medias\/([^\\]*)/.exec($el.val());
                if(parse) {
                    return '//fast.wistia.net/embed/iframe/' + parse[1];
                }
            },
            vimeo: function($el) {
                var parse = /https?:\/\/vimeo.com\/([^\/]*)/.exec($el.val());

                return '//player.vimeo.com/video/' + parse[1];
            },
            init: function($el) {
                if(/https?:\/\/vimeo.com\/([^\/]*)/.exec($el.val())) {
                    return this.vimeo($el);
                } else if(/youtube.([^\/]*)\/watch\?v=/.exec($el.val())) {
                    return this.youtube($el);
                } else if(/wistia.com(.*)wvideoid=([^\&]*)/.exec($el.val()) || /wistia.com\/medias\/([^\\]*)/.exec($el.val())) {
                    return this.wistia($el);
                }

                return '';
            }
        };

        var $widgetForm = $('#WidgetForm'),
            $slide_holders = $('.js-slide-holder'),
            default_img_src = '{App\Common\Media::getDefaultImage("150x150")}';

        $slide_holders.on('change', '.js-slide-choose-image-variant', function (e) {
            e.preventDefault();

            var $el = $(this),
                $choose_img_holder = $el.closest('.js-slide-choose-image-holder'),
                $image_src = $choose_img_holder.find('.js-slide-image-src'),
                $image_preview_image = $choose_img_holder.find('.js-slide-image-preview-image'),
                $image_preview_video = $choose_img_holder.find('.js-slide-image-preview-video'),
                $internal_src_holder_link = $choose_img_holder.find('.js-slide-internal-src-holder-link');

            if ($el.val() == 'internal') {
                $image_src.attr('readonly', true);
                $internal_src_holder_link.removeClass('hidden');
                $image_preview_video.addClass('hidden');
                $image_preview_image.removeClass('hidden');
            } else if ($el.val() == 'external') {
                $image_src.attr('readonly', false);
                $internal_src_holder_link.addClass('hidden');
                $image_preview_video.addClass('hidden');
                $image_preview_image.removeClass('hidden');
            } else {
                $image_src.attr('readonly', false);
                $internal_src_holder_link.addClass('hidden');
                $image_preview_image.addClass('hidden');
                $image_preview_video.removeClass('hidden');
            }

            //App.initAjax($choose_img_holder);
        }).on('change keyup', '.js-slide-image-src', function () {
            var $el = $(this),
                $choose_img_holder = $el.closest('.js-slide-choose-image-holder'),
                $image_variant = $choose_img_holder.find('select.js-slide-choose-image-variant'),
                $image_preview_image = $choose_img_holder.find('.js-slide-image-preview-image'),
                $image_preview_video = $choose_img_holder.find('.js-slide-image-preview-video');

            if ($image_variant.val() == 'video') {
                $image_preview_video.find('iframe').prop('src', Parser.init($el));
            } else {
                if ($el.val() != '') {
                    $image_preview_image.prop('src', $el.val());
                } else {
                    $image_preview_image.prop('src', default_img_src);
                }
            }
        });

        $('.js-slide-image-src').trigger('change');


        $(':input[name="autoplay"]').on('change', function ()
        {
            var $autoplay_holder = $('.js-autoplay-holder');
            if ($(this).val() === 'yes') {
                $autoplay_holder.removeClass('hidden');
            } else {
                $autoplay_holder.addClass('hidden');
                if (!$(':input[name="interval"]').val()) {
                    $(':input[name="interval"]').val({$widget->getSetting('interval')});
                }
            }

        }).trigger('change');

        $(':input[name="caption"]').on('change', function ()
        {
            var $caption_holder = $('.js-caption');
            if ($(this).val() === 'yes') {
                $caption_holder.removeClass('hidden');
            } else {
                $caption_holder.addClass('hidden');
            }
        });

        $(':input[name$="[link_type]"]').on('change', function ()
        {

            var $el = $(this),
                $slide_holder = $el.closest('.js-slide-holder'),
                $active = $slide_holder.find('[data-link-type="' + $el.val() + '"]');

            $active.removeClass('hidden').prop('disabled', false);
            $slide_holder.find('[data-link-type]').not($active).prop('disabled', true).addClass('hidden');

            if ($el.val() === '') {
                $active.addClass('hidden');
                $slide_holder.find('.js-link-holder').addClass('hidden');
            } else {
                $slide_holder.find('.js-link-holder').removeClass('hidden');
            }

            $slide_holder.find('.has-error').removeClass('has-error');
            $slide_holder.find('.help-block-error').remove();

        }).trigger('change');

        $(':input[name="amount"]').on('change', function ()
        {

            var value = $(this).val(), $current, i;

            for (i = 1; i <= 15; i++) {
                $current = $('#slide_' + i);

                if (i <= value) {
                    $current.show();
                    $current.find(':input').prop('disabled', false);
                    $current.find(':input[name$="[link_type]"]').trigger('change');
                    $current.find(':input[name$="[img_type]"]').trigger('change');
                } else {
                    $current.hide();
                    $current.find(':input').prop('disabled', true);
                }
            }

            if (value == 1) {
                $(':input[name="cycle"]').val('no').trigger('change');
            }

            $(':input[name="caption"]').trigger('change');

        }).trigger('change');

        $(".js-toggle-whole-link").on("change", function ()
        {
            var $field = $(this).closest(".row").find("input.link-caption-js");

            if ($(this).is(":checked")) {
                return $field.val("").attr("disabled", "disabled");
            }

            $field.removeAttr("disabled");
        }).trigger("change");

        $widgetForm.on('submit', function (e)
        {
            e.preventDefault();

            tinymceSubmit();
        });
    });
</script>