<div class="sidebar sidebar-mobile-title">
    <div class="box">
        <div class="box-title">
            <div class="box-title-text">
                <h5></h5>
            </div>

            <div class="box-title-addon" id="widget-showcase-active">
                {if $widget->canDisable()}
                    {include file="./includes/enabled.tpl"}
                {/if}
            </div>
        </div>

        <div class="box-section">
            <div class="row">
                <div class="col-lg-12 col-xs-6 form-group" id="widget-showcase-per-row">
                    <label class="control-label">{t}widget.product.showcase.label.items_per_row{/t}</label>

                    <select name="per_row" class="form-control select2me" data-no-input="true">
                        <option value="1"{if $widget->getSetting('per_row') == 1} selected="selected"{/if}>1</option>
                        <option value="2"{if $widget->getSetting('per_row') == 2} selected="selected"{/if}>2</option>
                        <option value="3"{if $widget->getSetting('per_row') == 3} selected="selected"{/if}>3</option>
                        <option value="4"{if $widget->getSetting('per_row') == 4} selected="selected"{/if}>4</option>
                        <option value="5"{if $widget->getSetting('per_row') == 5} selected="selected"{/if}>5</option>
                        <option value="6"{if $widget->getSetting('per_row') == 6} selected="selected"{/if}>6</option>
                        <option value="7"{if $widget->getSetting('per_row') == 7} selected="selected"{/if}>7</option>
                        <option value="8"{if $widget->getSetting('per_row') == 8} selected="selected"{/if}>8</option>
                        <option value="9"{if $widget->getSetting('per_row') == 9} selected="selected"{/if}>9</option>
                        <option value="10"{if $widget->getSetting('per_row') == 10} selected="selected"{/if}>10</option>
                        <option value="11"{if $widget->getSetting('per_row') == 11} selected="selected"{/if}>11</option>
                        <option value="12"{if $widget->getSetting('per_row') == 12} selected="selected"{/if}>12</option>
                    </select>
                </div>

                <div class="col-lg-12 col-xs-6 form-group" id="widget-showcase-checkboxes">
                    <div class="form-heading">
                        <h5>{t}widget.product.showcase.label.items_options{/t}</h5>
                    </div>

                    <div class="checklist">
                        <div class="skip-has-error">
                            <div class="checklist-item">
                                <label class="form-control-check">
                                    <input name="show_name" type="checkbox"
                                           value="1"{if $widget->getSetting('show_name')} checked="checked"{/if} />
                                    {t}product.showcase.label.show_name{/t}
                                </label>
                            </div>

                            <div class="checklist-item">
                                <label class="form-control-check">
                                    <input name="show_description" type="checkbox"
                                           value="1"{if $widget->getSetting('show_description')} checked="checked"{/if} />
                                    {t}product.showcase.label.show_description{/t}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-12 col-xs-6 form-group margin-top-15">
                    <div class="form-heading">
                        <h5>{t}widget.product.showcase.label.carousel_options{/t}</h5>
                    </div>

                    <div class="stack">
                        <div class="stack-main">
                            <label class="form-control-check">{t}widget.product.showcase.label.enable_slider{/t}</label>
                        </div>

                        <div class="stack-addon">
                            <input type="checkbox" name="enable_slider" value="yes" class="switch"{if $widget->getSetting('enable_slider')} checked="checked"{/if}/>
                        </div>
                    </div>
                </div>
                <div class="col-lg-12 col-xs-6 form-group" id="widget-showcase-enable-arrows">
                    <div class="stack">
                        <div class="stack-main">
                            <label class="form-control-check">{t}widget.product.showcase.label.enable_arrows{/t}</label>
                        </div>
                        <div class="stack-addon">
                            <input type="checkbox" name="enable_arrows" value="yes" class="switch"{if $widget->getSetting('enable_arrows')} checked="checked"{/if}/>
                        </div>
                    </div>
                </div>
                <div class="col-lg-12 col-xs-6 form-group" id="widget-showcase-enable-pagination">
                    <div class="stack">
                        <div class="stack-main">
                            <label class="form-control-check">{t}widget.product.showcase.label.enable_pagination{/t}</label>
                        </div>
                        <div class="stack-addon">
                            <input type="checkbox" name="enable_pagination" value="yes" class="switch"{if $widget->getSetting('enable_pagination')} checked="checked"{/if}/>
                        </div>
                    </div>
                </div>
                <div class="col-lg-12 col-xs-6 form-group" id="widget-showcase-arrows-position">
                    <label class="control-label">{t}widget.product.showcase.label.arrows_position{/t}</label>

                    <select name="arrows_position" class="form-control select2me" data-no-input="true">
                        <option value="top"{if $widget->getSetting('arrows_position') == 'top'} selected="selected"{/if}>{t}widget.product.showcase.label.arrows_position.top{/t}</option>
                        <option value=center{if $widget->getSetting('arrows_position') == 'center'} selected="selected"{/if}>{t}widget.product.showcase.label.arrows_position.center{/t}</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content">
    <div class="box">
        <div class="box-title">
            <div class="box-title-text">
                <h5>{$widget->getWidgetName()}</h5>
            </div>
        </div>

        <div class="box-section">
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group" id="widget-showcase-title">
                        <label class="control-label">{t}widget.product.showcase.label.title{/t}</label>

                        <div class="skip-has-error">
                            <input name="header" class="form-control" value="{$widget->getSetting('header')}"
                                   placeholder="{t}widget.product.showcase.ph.header{/t}"/>
                        </div>
                    </div>
                </div>

                <div class="col-xs-6">
                    <div class="form-group" id="widget-showcase-type">
                        <label class="control-label">{t}widget.product.showcase.label.type{/t}</label>

                        <div class="skip-has-error">
                            <select name="type" class="form-control select2me" data-no-input="true"
                                    data-placeholder="{t}widget.product.showcase.ph.filter{/t}">
                                <option value="category"{if $widget->getSetting('type') == 'category'} selected="selected"{/if}>{t}widget.product.showcase.category{/t}</option>
                                <option value="vendor"{if $widget->getSetting('type') == 'vendor'} selected="selected"{/if}>{t}widget.product.showcase.vendor{/t}</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="box-section">
            <div class="form-group" id="widget-showcase-items">
                <label class="control-label">{t}widget.product.showcase.label.items{/t}</label>

                <div class="skip-has-error">
                    <div id="showcase_category">
                        {include file="categories/tree_select.tpl" multiple=true select_name="showcase" select_placeholder="{t}widget.product.showcase.label.select{/t}" category_id=$widget->getFilterValueAutocomplete()}
                    </div>

                    <div id="showcase_vendor">
                        <input name="showcase"{if $widget->getSetting('type') === 'vendor'} value="{$widget->getFilterValueAutocomplete()}"{/if}
                               class="form-control select2_ajax"
                               data-url="{route('admin.autocomplete.vendor')}" data-multiple="true"
                               data-autowidth="false" data-placeholder="{t}widget.product.showcase.label.select{/t}"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(function () {
        $(':input[name="type"]').on('change', function () {
            var $active = $('#showcase_' + $(this).val()),
                $disabled = $('*[id^="showcase_"]').not($active);

            $active.removeClass('hidden');
            $disabled.addClass('hidden');

            $active.find(':input, select').prop('disabled', false);
            $disabled.find(':input, select').prop('disabled', true);
        }).trigger('change');

        $(':input[name="enable_slider"]').on('change', function () {
            let sliderOptions = $('#widget-showcase-enable-arrows, #widget-showcase-enable-pagination, #widget-showcase-arrows-position');

            if($(this).is(':checked')) {
                sliderOptions.removeClass('hidden');
            } else {
                sliderOptions.addClass('hidden');
            }
        }).trigger('change');

        $('#showcase_vendor input.select2_ajax').each(function () {
            var $el = $(this),
                select2me = $el.data('select2');

            if (select2me) {
                $('#showcase_vendor .select2-choices').sortable({
                    items: "> li",
                    stop: function (event, ui) {
                        var elements = [];
                        $('>li', this).each(function () {
                            var select2me = $(this).data('select2Data');
                            if (select2me) {
                                elements.push(select2me.id);
                            }
                        });
                        $el.val(elements.join(','));
                    }
                }).disableSelection();
            }
        });
    });

</script>
