<div class="_section">
    <div class="_section-head">
        <div class="_stack">
            <div class="_stack-main">
                <h4>{$widget->getWidgetName()}</h4>
            </div>

            <div class="_stack-addon">
                {if $widget->canDisable()}
                    <div class="_sidebar-head-addon">
                        {include file="builder-parts/fields/field-switchbox.tpl" input_name="enabled" id="enable-widget-text" value="yes" checked=$widget->isEnabled()}
                    </div>
                {/if}
            </div>
        </div>
    </div>

    <div class="_section-body">
        <div class="_row">
            <div class="_col">
                {include file="builder-parts/fields/field-text.tpl" input_name="title" id="title" label="{t}widget.extra.text.label.title{/t}" value="{$widget->getSetting('title')}" placeholder="{t}widget.extra.text.ph.title{/t}"}
            </div>
        </div>

        <div class="_row">
            <div class="_col">
                {include file="builder-parts/fields/field-textarea.tpl" input_name="text" id="tinymce-{uniqid()}" label="{t}widget.extra.text.label.text{/t}" value="{$widget->getSetting('text')}" placeholder="{t}article.ph.content{/t}" editor=true custom_data='data-height="300" data-tool-table="true" data-tool-image="true" data-tool-media="true"'}
            </div>
        </div>
    </div>
</div>