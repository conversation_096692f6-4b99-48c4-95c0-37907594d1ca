<?php

declare(strict_types=1);

namespace App\LiquidEngine\Traits\Http\Customer;

use App\Helper\Format;
use App\Helper\YesNo;
use App\LiquidEngine\Assetics\Subscribers\Customer\Account;
use App\LiquidEngine\Http\Requests\Customer\Auth\LoginRequest;
use App\Models\Customer\Group;
use App\Models\Customer\Customer;
use App\Models\Store\Cart;
use App\LiquidEngine\Traits\Customer\SocialConnect;
use Guest;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Contracts\View\Factory;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;
use Throwable;

trait Login
{

    use SocialConnect, AuthenticatesUsers {
        AuthenticatesUsers::login AS loginTrait;
        AuthenticatesUsers::logout AS logoutTrait;
    }

    /**
     * @var null|string $complete_redirect
     */
    public $complete_redirect;

    /**
     * @param Request $request
     * @return Factory|View
     */
    public function showLoginForm(Request $request)
    {

        $data = [
            'connections' => $this->getSocialLogin(),
        ];

        if (($redirect = $request->query('_redirect')) && Str::startsWith($redirect, site()->getSiteUrl())) {
            $data['redirect'] = $redirect;
        }

        return view('checkout::authorize.login', $data);
    }

    /**
     * Handle a login request to the application.
     * @param LoginRequest $request
     * @return JsonResponse|RedirectResponse|Response
     * @throws ValidationException
     */
    public function login(LoginRequest $request)
    {
        return $this->loginTrait($request);
    }

    /**
     * @return RedirectResponse|Response
     */
    public function logout()
    {
        /** @var Customer $customer if no logged customer init empty object */
        $this->guard()->logout();

        session()->forget('policies_popup');

        return redirect()->route('site.home');
    }

    /**
     * Redirect the user after determining they are locked out.
     *
     * @param Request $request
     * @return void
     * @throws ValidationException
     */
    protected function sendLockoutResponse(Request $request): never
    {
        $seconds = $this->limiter()->availableIn(
            $this->throttleKey($request)
        );

        throw ValidationException::withMessages([
            $this->username() => [__('customer.error.auth.throttle', ['seconds' => $seconds])],
        ])->status(429);
    }

    /**
     * Get the failed login response instance.
     *
     * @return void
     * @throws ValidationException
     */
    protected function sendFailedLoginResponse(): never
    {
        throw ValidationException::withMessages([
            $this->username() => [activeRoute('site.auth.login') ? __('customer.error.auth.failed') : __('customer.error.auth.failed_register')],
        ]);
    }

    /**
     * Validate the user login request.
     *
     * @param Request $request
     * @return void
     *
     */
    protected function validateLogin(Request $request)
    {
        //
    }

    /**
     * Attempt to log the user into the application.
     *
     * @param Request $request
     * @return bool
     */
    protected function attemptLogin(Request $request)
    {
        return $this->guard()->attempt(
            $this->credentialsWithGroup($request), $request->filled('remember')
        );
    }

    /**
     * Get the guard to be used during authentication.
     *
     * @return StatefulGuard
     */
    protected function guard()
    {
        return Auth::guard('customer');
    }

    /**
     * Get the needed authorization credentials from the request with customer group.
     *
     * @param Request $request
     * @return array
     */
    protected function credentialsWithGroup(Request $request): array
    {
        //@todo use simple hack group
        return array_merge($this->credentials($request), [
            'group_id' => Group::where('id', '<>', (int)Guest::getGroupId())->pluck('id'),
        ]);
    }

    /**
     * The user has been authenticated.
     *
     * @param Request $request
     * @param Customer $user
     * @return mixed
     * @throws Throwable
     */
    protected function authenticated(Request $request, $user)
    {
        if($user->banned == YesNo::True) {
            $this->logout();

            if($request->isMethod('get')) {
                return redirect(null, 422)
                    ->route('site.auth.login')
                    ->withErrors(__('customer.error.auth.banned', ['reason' => $user->banned_reason, 'date' => Format::datetime($user->date_banned)]));
            }

            throw ValidationException::withMessages([
                'email' => [__('customer.error.auth.banned', ['reason' => $user->banned_reason, 'date' => Format::datetime($user->date_banned)])],
            ])->redirectTo(route('site.auth.login'));
        }

        if(!$user->active) {
            $this->logout();

            if($request->isMethod('get')) {
                return redirect(null, 422)
                    ->route('site.auth.login')
                    ->withErrors(__('customer.error.inactive'));
            }

            throw ValidationException::withMessages([
                'email' => [__('customer.error.inactive')],
            ])->redirectTo(route('site.auth.login'));
        }

        $response = [
            'redirect' => route('site.account')
        ];

        // Added strpos check to pass McAfee PCI compliance test (http://forum.opencart.com/viewtopic.php?f=10&t=12043&p=151494#p151295)
        if(($redirect = $request->input('_redirect')) && Str::startsWith($redirect, site()->getSiteUrl())) {
            $response['redirect'] = $redirect;
        } elseif (($cart = Cart::instance()) && $cart->hasProducts()) {
            $response['redirect'] = route('checkout');
        }

        if($eventDatas = event('controller:customer:authenticated', new Account($user))) {
            foreach($eventDatas as $eventData) {
                if(is_array($eventData)) {
                    $response = array_merge_recursive($response, $eventData);
                }
            }
        }

        if(!$request->ajax()) {
            return redirect($response['redirect']);
        }

        return response($response)
            ->setStatusCode(278);
    }

    /**
     * @param $redirect
     * @return void
     */
    public function setRedirect($redirect): void
    {
        if ($redirect && Str::startsWith($redirect, site()->getSiteUrl())) {
            $this->complete_redirect = $redirect;
        }
    }
}
