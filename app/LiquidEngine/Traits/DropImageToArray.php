<?php

declare(strict_types=1);

namespace App\LiquidEngine\Traits;

use App\Exceptions\Error;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;

trait DropImageToArray
{

    protected static $_cache = [];

    /**
     * @return array
     * @throws Error
     */
    protected function getImageArray()
    {
        if($this->has_image()) {
            return (new UrlImageFormat($this->image()))->imagesFormatArray();
        }

        $className = get_class($this);
        if(!array_key_exists($className, static::$_cache)) {
            static::$_cache[$className] = (new UrlImageFormat($this->image()))->imagesFormatArray();
        }

        return static::$_cache[$className];
    }

}
