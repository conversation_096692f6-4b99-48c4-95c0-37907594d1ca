<?php

declare(strict_types=1);

namespace App\LiquidEngine\Providers\FormRequestExtends\Store\Cart;

use App\Exceptions\Error;
use App\Integration\Payment\CreditorService;
use App\LiquidEngine\Traits\Request\AddToCartProduct;
use Illuminate\Validation\Validator;
use PaymentGateway;
use Validation;

trait ExtendLeasing
{
    use AddToCartProduct;

    /**
     * @var CreditorService
     */
    private $leasing_provider;

    protected function bootExtendLeasing()
    {
        $this->leasingProviderValidate();
        $this->leasingProvider();
    }

    private function leasingProviderValidate(): void
    {
        Validation::extendImplicit('leasing_provider_validate', function (string $attribute, $value, /** @noinspection PhpUnusedParameterInspection */ $parameters, \Illuminate\Contracts\Validation\Validator $validator): bool {
            if (empty($value)) {
                return true;
            }

            if (($instance = PaymentGateway::provider($value)) instanceof CreditorService) {
                $this->leasing_provider = $instance;
                return true;
            } else {
                $validator->setCustomMessages([
                    $attribute . '.leasing_provider_validate' => __('sf.widget.checkout.err.no_such_payment')
                ]);
                return false;
            }
        });
    }

    private function leasingProvider(): void
    {
        Validation::extendImplicit('leasing_provider', function (string $attribute, $value, /** @noinspection PhpUnusedParameterInspection */ $parameters, \Illuminate\Contracts\Validation\Validator $validator): bool {
            $request = request();
            if (empty($request->input('leasing-options-provider'))) {
                return true;
            }

            if (empty($value)) {
                $validator->setCustomMessages([
                    $attribute . '.leasing_provider' => __('sf.validation.required')
                ]);
                return false;
            }

            $product = $this->getCartItem();
            if ((!$product || !$product->product) && empty($bundle = $this->getBundle())) {
                $validator->setCustomMessages([
                    $attribute . '.leasing_provider' => __('sf.widget.cart.err.choose_variant')
                ]);
                return false;
            }

            try {
                $schema = $this->leasing_provider->getPriceVariantId($value, [$product->product_id ?? $bundle->id ?? null], $product ? ($product->discount_price ? $product->discount_price_input : $product->price_input) : ($bundle->price_from_input ?? 0), (float)$request->input('initial'), (float)$request->input('installment'));
                if (!$schema) {
                    $validator->setCustomMessages([
                        $attribute . '.leasing_provider' => __('sf.global.creditor.err.recalculate')
                    ]);
                    return false;
                }
            } catch (Error $error) {
                $validator->setCustomMessages([
                    $attribute . '.leasing_provider' => $error->getMessage()
                ]);
                return false;
            }

            return true;
        });
    }

}
