<?php

declare(strict_types=1);

namespace App\LiquidEngine\Providers\FormRequestExtends\Customer;

use App\Models\Customer\Customer;
use Illuminate\Validation\Validator;
use Validation;

trait ExtendExistCustomerEmail
{

    /**
     * Validate customer exists by email
     */
    protected function bootExtendExistCustomerEmail()
    {
        Validation::extendImplicit('exist_customer_email', function (string $attribute, $value, $parameters, \Illuminate\Contracts\Validation\Validator $validator): bool {
            $customer_id = !empty($parameters[0]) ? $parameters[0] : null;
            if (!Customer::existsByEmail($value, $customer_id)) {
                $validator->setCustomMessages([
                    $attribute . '.exist_customer_email' => __('customer.error.no_customer_with_this_mail')
                ]);
                return false;
            }

            return true;
        });
    }

}
