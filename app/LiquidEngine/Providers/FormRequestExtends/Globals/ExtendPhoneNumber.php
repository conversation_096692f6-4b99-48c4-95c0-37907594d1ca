<?php

declare(strict_types=1);

namespace App\LiquidEngine\Providers\FormRequestExtends\Globals;

use App\Locale\Country;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Validator;
use libphonenumber\PhoneNumberUtil;
use Validation;

trait ExtendPhoneNumber
{

    /**
     * Validate customer password equal
     */
    protected function bootExtendPhoneNumber()
    {
        Validation::extendImplicit('phone_number', function (string $attribute, $value, $parameters, \Illuminate\Contracts\Validation\Validator $validator) {
            /** @var Request $request */
            $request = $this->app['request'];
            if(!empty($parameters[0]) && ($_c = $request->input($parameters[0])) && Country::has($_c)) {
                $locale = Str::upper($_c);
            } elseif(!empty($_c = $request->input('c_' . $attribute)) && Country::has($_c)) {
                $locale = Str::upper($_c);
            } else {
                $locale = null;
            }

            try {
                $phoneUtil = PhoneNumberUtil::getInstance();
                $numberProto = $phoneUtil->parse($value, $locale);

                if (!($valid = $phoneUtil->isValidNumber($numberProto))) {
                    $validator->setCustomMessages([
                        $attribute . '.phone_number' => __('sf.address.err.invalid_phone_number')
                    ]);
                }

                return $valid;
            } catch (\Exception $exception) {
                $validator->setCustomMessages([
                    $attribute . '.phone_number' => __('sf.address.err.invalid_phone_number')
                ]);
                return false;
            }
        });
    }
}
