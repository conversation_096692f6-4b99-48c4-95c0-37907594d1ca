<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers;

use App\LiquidEngine\Services\TemplateContextProvider;
use App\LiquidEngine\Services\TemplateLoader;
use App\Models\Page\Page;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

/**
 * Controller for handling static pages
 */
class PagesController extends LiquidController
{
    /**
     * Show a specific page
     *
     * @param Request $request
     * @param string $handle
     * @return Response|JsonResponse
     */
    public function show(Request $request, string $handle): Response|JsonResponse
    {
        // Find the page by handle
        $page = Page::where('url_handle', $handle)->active()->first();
        
        if (!$page) {
            return $this->notFound('Page not found');
        }
        
        // Prepare data for the template
        $data = [
            'title' => $page->name,
            'page' => $page,
            'page_description' => $page->seo_description,
        ];
        
        // Check for custom template
        $template = $this->getTemplateFromRequest($request, 'templates/page');
        
        // Render the template
        return $this->renderTemplate($template, $data, [
            "templates/page.{$handle}",
            "templates/page"
        ]);
    }
} 