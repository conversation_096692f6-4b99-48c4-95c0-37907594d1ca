<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 15.12.2016 г.
 * Time: 22:21 ч.
 */
namespace App\LiquidEngine\Http\Controllers\Site\Feed;

use App\Exceptions\HttpNotFound;
use App\Models\Apps\Applications;
use App\Models\Apps\Feed\Feed;
use App\LiquidEngine\Http\Controllers\Site\AbstractEngineController;
use Exception;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class XmlFeedController extends AbstractEngineController
{

    /**
     * @param Request $request
     * @param $type
     * @param $key
     * @param int $page
     * @return Response|StreamedResponse
     * @throws Exception
     */
    public function index(Request $request, $type, $key, $page = 1)
    {
        $key = Feed::FEED_PREFIX . $key;
        $app = Applications::with('feed')->where('key',$key)->firstOrFail();
        /** @var Feed $feed */
        $feed = $app->feed;

        if (is_null($feed) || $feed->type != $type || !$feed->manager->isInstalled()) {
            app()->abort(404, __('error.no_longer_exists.page'));
        }

        if (!$feed->manager->getSetting('pages.' . $page) || !Storage::exists($file_path = site_dir(config('upload.files_folder') . '/' . $feed->manager->getSetting('pages.' . $page . '.feed')))) {
            app()->abort(404, __('error.no_longer_exists.page'));
        }

        if (isset($_SERVER['HTTP_IF_MODIFIED_SINCE'])) {
            $file_modified = Storage::lastModified($file_path);

            if (\Carbon\Carbon::parse($_SERVER['HTTP_IF_MODIFIED_SINCE'])->getTimestamp() == $file_modified) {
                return Response::create()
                    ->header('Last-Modified', gmdate('D, d M Y H:i:s \G\M\T', $file_modified))
                    ->header($_SERVER['SERVER_PROTOCOL'], '304 Not Modified');
            }
        }

        set_time_limit(0);

        if($request->query('action') == 'download') {
            return $this->_download($feed, $file_path);
        }

        return $this->_output($feed, $file_path);
    }

    /**
     * @param Feed $feed
     * @param $file_path
     * @return Response
     * @throws FileNotFoundException
     */
    protected function _output(Feed $feed, $file_path)
    {
        $headers = [];
        if ($feed->type == 'json') {
            $headers = [
                'content-type' => 'application/json;charset=utf-8'
            ];
        } elseif ($feed->type == 'csv') {
            $headers = [
                'content-type' => 'application/csv;charset=utf-8',
                'content-transfer-encoding' => 'utf-8',
                'content-disposition' => 'attachment; filename=' . $feed->key . '.csv',
                'pragma' => 'no-cache',
                'expires' => '0',
            ];
        } elseif ($feed->type == 'xml') {
            $headers = [
                'content-type' => 'text/xml;charset=utf-8'
            ];
        }

        $result = Storage::get($file_path);
        return response($result, 200, array_merge($headers, [
            "content-length" => Storage::size($file_path)
        ]));
    }

    /**
     * @param Feed $feed
     * @param $file_path
     * @return Response|StreamedResponse
     * @throws FileNotFoundException
     */
    protected function _download(Feed $feed, $file_path)
    {
        switch ($feed->type) {
            case 'json' :
                $content_type = 'application/json;charset=utf-8';
                break;
            case 'csv' :
                $content_type = 'application/csv;charset=utf-8';
                break;
            default:
                $content_type = 'text/xml;charset=utf-8';
        }

        $headers = [
            "Pragma" => "no-cache",
            "Expires" => "Mon, 26 Jul 1997 05:00:00 GMT",
            "Cache-Control" => "no-store, no-cache, must-revalidate",
            "Content-Disposition" => "attachment; filename=" . urlencode(pathinfo($feed->key, PATHINFO_BASENAME) . '.' . $feed->type),
            "Content-Type" => $content_type,
            "Content-Description" => "File Transfer",
            "Content-Transfer-Encoding" => "binary",
            "Connection" => "Keep-Alive",
            "Content-Length" => Storage::size($file_path),
        ];

        if ($feed->type == 'json') {
            return response(Storage::get($file_path), 200, $headers);
        }

        $temp_file = @fopen(config('url.storage') . str_replace([' '], ['%20'], $file_path), "r");
        if (!$temp_file) {
            throw new HttpNotFound;
        }

        $callback = function () use ($file_path, $temp_file): void {
            while (!feof($temp_file)) {
                echo fread($temp_file, 65536);
                flush(); // this is essential for large downloads
            }

            fclose($temp_file);
        };

        return new StreamedResponse($callback, 200, $headers);
    }
}
