<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Controllers\Site\Product;

use App\Helper\YesNo;
use App\LiquidEngine\Assetics\Subscribers\ViewProduct;
use App\LiquidEngine\Helpers\SeoReplace;
use App\LiquidEngine\Http\Controllers\Site\AbstractEngineController;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Product\Product;
use App\LiquidEngine\LiquidHelpers\Drop\ProductDetailsByType;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;

class ProductController extends AbstractEngineController
{

    /**
     * @param Request $request
     * @param $slug
     * @param string|null $cart_key
     * @return View|\Illuminate\Foundation\Application|\Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|Response|\Illuminate\Routing\Redirector
     */
    public function index(Request $request, $slug, $cart_key = null)
    {
        if(!$this->jsonUrlHandle($slug)){
            return response()
                ->json(['message' => __('error.no_longer_exists.product')], 404, [
                    'content-type' => 'text/javascript'
                ]);
        }

        $template = new Template('templates.product');

        /** @var Product $product */
        $product = Product::listing(true,true)
            ->withCollections()->urlHandle($slug)->first();

        if (!$product) {
            app()->abort(404, __('error.no_longer_exists.product'));
        } elseif ($product->active === YesNo::False) {
            $response = $this->templateJson(new Template('templates.404'), [
                'code' => $product->name,
                'message' => __('item.errors.inactive')
            ]);
            if($response instanceof View) {
                $response = response($response);
            }

            return $response
                ->setStatusCode(404);
        }

        if($slug != $product->url_handle) {
            return redirect($product->url(), 301);
        }

        // load data for bundle
        if($product->type == $product::TYPE_BUNDLE) {
            $products = $product->bundle_products()->listing(true,true)
                ->orderBy('products_bundle.sort_order', 'asc')
                ->addSelect('products.id AS id')->get();

            $product->setRelation('bundle_products', $products);
        }

        $dropProduct = ProductDetailsByType::get($product);

        if($this->isJsonResponse()) {
            return response()
                ->json($dropProduct);
        }

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        foreach($product->breadcrumb AS $breadcrumb) {
            $this->breadcrumbs()->add(
                $breadcrumb['name'],
                $breadcrumb['link']
            );
        }

        $this->breadcrumbs()->add(
            $product->name,
            $product->url
        );

        event('controller:viewProduct', new ViewProduct($product));

        return $this->template($template, [
            'product' => $dropProduct,
        ], SeoReplace::get('product', $product));
    }

}
