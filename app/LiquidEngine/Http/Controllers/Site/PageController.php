<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 30.11.2016 г.
 * Time: 00:09 ч.
 */

namespace App\LiquidEngine\Http\Controllers\Site;

use App\Helper\PageBuilderContent;
use App\LiquidEngine\Assetics\Subscribers\HomePage;
use App\Models\Order\Order;
use App\Models\Page\PageContent;
use GDPR;
use Illuminate\Contracts\View\Factory;
use Illuminate\View\View;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use App\Helper\YesNo;
use App\Models\Page\PageHistory;
use App\Models\Page\Page;
use Illuminate\Http\Response;
use App\LiquidEngine\LiquidHelpers\Drop\Page as DropPage;
use App\LiquidEngine\LiquidHelpers\Drop\Order as DropOrder;
use App\LiquidEngine\Assetics\Subscribers\Page as PageSubscriber;

/**
 * Class PageController
 * @package App\Http\Controllers\Site
 */
class PageController extends AbstractEngineController
{

    /**
     * @var Page
     */
    protected $page;

    protected \App\LiquidEngine\LiquidHelpers\Drop\Settings\Template $template;

    /**
     * @var Order
     */
    protected $order;

    public function __construct()
    {
//        $this->template = new Template('templates.page');
        $this->template = new Template('page');
    }

    /**
     * @param Page $page
     * @return $this
     */
    public function setPage(Page $page): static
    {
        $this->page = $page;
        return $this;
    }

    /**
     * @param Order|null $order
     * @return $this
     */
    public function setOrder(?Order $order = null): static
    {
        if ($order !== null) {
            if ($order instanceof DropOrder) {
                $this->order = $order;
            } elseif ($order instanceof Order) {
                $this->order = new DropOrder($order);
            }
        }

        return $this;
    }

    /**
     * @param null $slug
     * @return Factory|\Illuminate\Contracts\View\View|\Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|Response|\Illuminate\Routing\Redirector|View|LView
     */
    public function get($slug = null)
    {
        if (!$this->jsonUrlHandle($slug)) {
            return response()
                ->json(['message' => __('error.no_longer_exists.page')], 404, [
                    'content-type' => 'text/javascript'
                ]);
        }

        if (!$this->page) {
            $this->page = Page::urlHandle($slug)->first();
            if (!$this->page) {
                app()->abort(404, __('error.no_longer_exists.page'));
            }

            //redirect if link is 301
            if ($slug !== $this->page->url_handle && false === strpos($slug, '?')) {
                return redirect($this->page->url(), 301);
            }
        }

        if ($this->page->active != YesNo::True) {
            app()->abort(404, __('error.no_longer_exists.page'));
        }

        $this->breadcrumbs()->add(
            __('pages.home'),
            route('site.home')
        );

        $this->breadcrumbs()->add(
            $this->page->name,
            $this->page->url()
        );

        if ($this->template->data_key() == 'maintenance') {
            $this->breadcrumbs()->reset()->add(
                __('pages.home'),
                route('site.home')
            );

            $this->breadcrumbs()->add(
                __('maintenance.title'),
                request()->fullUrlWithQuery([])
            );
        }

        if ($this->page->type == 'builder') {
            return $this->preview($this->page);
        }

        if ($this->page->type == 'landing') {
            $this->sendEventForPage();
            return response($this->page->content);
        }

        // if page is FAQ
        if ($this->page->type == 'faq') {
            $this->page->setAttribute('faq', $this->page->contents->map(function (PageContent $content) {
                return (object)[
                    'question' => $content->name,
                    'answer' => $content->content
                ];
            })->all());
        }

        if ($this->page->type == 'regular') {
            $this->page = GDPR::renderCookiesTable($this->page);
        }

        $dropPage = new DropPage($this->page);

        if ($this->isJsonResponse()) {
            return response()
                ->json($dropPage);
        }

        $data = [
            'seo_title' => $this->page->seo_title ?: $this->page->name,
            'seo_description' => $this->page->seo_description,
            'page' => $dropPage,
            'order' => $this->order,
        ];

        if ($this->template->data_key() == 'maintenance') {
            $data = array_merge($data, $this->getSeoData('maintenance'));
        }

        $this->sendEventForPage();

        return $this->template($this->template, $data);
    }

    /**
     * @param Page|int $page
     * @param null|int $historyId
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\JsonResponse|Response
     */
    public function preview($page, $historyId = null)
    {
        $this->page = $page instanceof Page ? $page : Page::findOrFail($page);
        if ($historyId) {
            /** @var PageHistory $pageHistory */
            $pageHistory = PageHistory::findOrFail($historyId);
            $this->page->content = json_encode($pageHistory->content);
        }

        $pageBuilder = (new PageBuilderContent($this->page->content));
        if (request()->ajax()) {
            $content = $pageBuilder->toJSON();
        } else {
            $content = $pageBuilder->toHTML();
        }

        $this->page->content = $content;

        $this->breadcrumbs()->reset();
        if ($this->page->system_page != 'home') {
            $this->breadcrumbs()->add(
                __('pages.home'),
                route('site.home')
            );

            $this->breadcrumbs()->add(
                $this->page->name,
                $this->page->url()
            );
        }

        $dropPage = new DropPage($this->page);

        if ($this->isJsonResponse()) {
            $this->sendEventForPage();
            return response()
                ->json($dropPage);
        }

        $data = [
            'seo_title' => $this->page->seo_title ?: $this->page->name,
            'seo_description' => $this->page->seo_description,
            'page' => $dropPage,
            'order' => $this->order,
            'disable_header' => !((int)$pageBuilder->getOption('enable_header')),
            'disable_footer' => !((int)$pageBuilder->getOption('enable_footer'))
        ];

        if ($this->template->data_key() == 'maintenance') {
            $data = array_merge($data, $this->getSeoData('maintenance'));
        }

        $this->sendEventForPage();

        return $this->templateJson($this->template, $data);
    }

    protected function sendEventForPage()
    {
        if (in_array($this->page->system_page, ['thank_you', 'error.404'])) {
            return;
        }

        if ($this->template->data_key() == 'maintenance') {
            event('controller:maintenance', new PageSubscriber($this->page));
        } elseif ($this->page->system_page == 'home') {
            event('controller:homePage', new HomePage($this->page));
        } else {
            event('controller:page', new PageSubscriber($this->page));
        }
    }

}
