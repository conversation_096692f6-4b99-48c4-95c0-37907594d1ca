<?php

declare(strict_types=1);

namespace App\LiquidEngine\Http\Middleware\Site\Store\Cart;

use App\Models\Marketing\UpSell\UpSell;
use App\Models\Product\Product;
use App\LiquidEngine\LiquidHelpers\Drop\Marketing\UpSell AS DropUpSell;
use App\Models\Store\Cart;
use Apps;
use Closure;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\LiquidEngine\LiquidHelpers\Drop\Settings\Template;
use App\LiquidEngine\Traits\ThemeResponse;
use Throwable;

class AddProductUpSellPopup extends AbstractAddToCart
{

    use ThemeResponse;

    /**
     * @param Request $request
     * @param Closure $next
     * @return ResponseFactory|Response|mixed
     * @throws Throwable
     */
    public function handle($request, Closure $next)
    {
        if($request->ajax() && $this->installed() && ($variant_id = $request->input('variant_id')) && !$request->input('up_sell_popup') && ($cartInstance = Cart::instance(true))) {
            /** @var UpSell $upSell */
            $upSell = UpSell::active()->whereTriggerVariantId($variant_id)
                ->where('id', '<>', $request->input('up_sell_id'))
                ->whereNotIn('offer_variant_id', $cartInstance ? $cartInstance->items()->pluck('variant_id') : [])
                ->whereHas('trigger_variant')->whereHas('offer_variant')
                ->whereHas('trigger_product', function ($query): void {
                    /** @var Product $query */
                    $query->listing();
                })->whereHas('offer_product', function ($query): void {
                    /** @var Product $query */
                    $query->listing();
                })->inStockOfferCheck()->outStockTriggerCheck()->with([
                    'child_no_detailed', 'child_yes_detailed',
                    'trigger_variant.detailed_discount', 'offer_variant.detailed_discount',
                    'trigger_variant.item' => function ($query): void {
                    /** @var Product $query */
                    $query->listing();
                }, 'offer_variant.item' => function ($query): void {
                    /** @var Product $query */
                    $query->listing();
                }])->first();

            if($upSell) {
                $data = [
                    'status' => 'success',
                    'events' => ['cc.cart.product.addToCart', 'cc.cart.up_sell.open'],
                    'up_sell' => new DropUpSell($upSell)
                ];

                $template = new Template('templates.upSell');

                return $this->responseWithSettings($template, $data);
            }
        }

        return $next($request);
    }

    /**
     * @return bool
     */
    protected function installed()
    {
        return Apps::installed('up_cross_sell');
    }

}
