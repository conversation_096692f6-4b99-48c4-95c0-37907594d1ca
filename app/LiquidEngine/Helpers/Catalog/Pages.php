<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 10.1.2018 г.
 * Time: 15:28 ч.
 */
namespace App\LiquidEngine\Helpers\Catalog;

use App\Helper\Catalog\AbstractCatalog;
use App\Helper\Text;
use App\Models\Page\Page;

class Pages extends AbstractCatalog
{

    protected static $_pages = [];

    protected static $_variants = [];

    /**
     * @param string|null $url
     * @return Page|\Illuminate\Database\Eloquent\Model|null
     */
    public static function getPageByUrl($url = null)
    {
        if (is_null($url) && activeRoute('page')) {
            $url = routeParameter('slug');
            if (empty(static::$_pages['url'][$url])) {
                static::$_pages['url'][$url] = Page::getByUrl($url);
            }

            return static::$_pages['url'][$url];
        } elseif (is_null($url) && activeRoute('site.preview.page')) {
            $id = routeParameter('page_id');
            if (empty(static::$_pages['id'][$id])) {
                static::$_pages['id'][$id] = Page::find($id);
            }

            return static::$_pages['id'][$id];
        }

        return static::$_pages['url'][$url] = Page::getByUrl($url);
    }

    /**
     * @return null|Page
     */
    public static function getCurrently()
    {
        return static::getPageByUrl();
    }

    /**
     * @return null|Page
     */
    public static function getPage()
    {
        return static::getCurrently();
    }

    /**
     * {@inheritdoc}
     */
    public static function getHeaderString($page = null)
    {
        if (is_null($page) || !($page instanceof Page)) {
            $page = static::getCurrently();
        }

        if (!$page) {
            return parent::getHeaderString($page);
        }

        return $page->name;
    }

    /**
     * @param Page $page
     * @return array
     */
    public static function getSeo(Page $page = null): array
    {
        $page = $page ?: static::getCurrently();
        if (!$page) {
            return [];
        }

        if (!empty($page->seo_title)) {
            $title = $page->seo_title;
        } else {
            $title = $page->name;
        }

        if (!empty($page->seo_description)) {
            $description = $page->seo_description;
        } elseif (in_array($page->type, ['regular', 'landing']) && !empty(Text::stripTags($page->content, true))) {
            $description = Text::stripTags($page->content, true);
        } else {
            $description = $page->name;
        }

        return [
            'title' => $title,
            'description' => $description
        ];
    }

}
