<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\ThemeSettingsParser;

use App\Models\Blog\Blog as BlogModel;
use App\Models\Product\Vendor;
use Illuminate\View\View;

class Image extends AbstractThemeSettingsParser
{

    public function render() : ?View
    {
        if(!$this->getId()) {
            return null;
        }

        $value = $this->config->get($this->getId(), $this->settings['default'] ?? null);

        return view('storefront.theme.sections.settings.image', [
            'name' => $this->getId(),
            'value' => $value,
            'required' => $this->settings['required'] ?? false,
            'label' => $this->getLabel() ? : 'Image',
            'info' => $this->getInfo(),
            'no_image' => (new Vendor())->getImage('150x150'),
            'is_block' => $this->isBlock(),
        ]);
    }

    /**
     * @param array $data
     * @return array
     */
    public function update(array $data) : array
    {
        if(!$this->getId()) {
            return [];
        }

        return [
            $this->getId() => $data[$this->getId()] ?? null
        ];
    }

}
