<?php

declare(strict_types=1);

namespace App\LiquidEngine\Helpers\Customer;

class AddressSectionCore
{
    /**
     * @return string
     */
    protected function company_name()
    {
        return $this->_validateOptional(setting('checkout_hide_company_name'));
    }

    /**
     * @return string
     */
    protected function company_bulstat()
    {
        return $this->_validateOptional(setting('checkout_hide_company_bulstat'));
    }

    /**
     * @return string
     */
    protected function company_vat()
    {
        return $this->_validateOptional(setting('checkout_hide_company_vat'));
    }

    /**
     * @return string
     */
    protected function company_owner()
    {
        return $this->_validateOptional(setting('checkout_hide_company_mol'));
    }

    /**
     * @return string
     */
    protected function street_name()
    {
        return $this->_validateOptional(setting('checkout_hide_street_name'));
    }

    /**
     * @return string
     */
    protected function street_number()
    {
        return $this->_validateOptional(setting('checkout_hide_street_number'));
    }

    /**
     * @return string
     */
    protected function additional_information()
    {
        return $this->_validateOptional(setting('checkout_hide_additional_information'));
    }

    /**
     * @return boolean
     */
    protected function post_code_required(): bool
    {
        return !setting('post_code_not_required');
    }

    /**
     * {@inheritDoc}
     */
    public function toArray(): array
    {
        return [
            'company_name' => $this->company_name(),
            'company_bulstat' => $this->company_bulstat(),
            'company_vat' => $this->company_vat(),
            'company_owner' => $this->company_owner(),
            'street_name' => $this->street_name(),
            'street_number' => $this->street_number(),
            'additional_information' => $this->additional_information(),
            'post_code_required' => $this->post_code_required(),
        ];
    }

    /**
     * {@inheritDoc}
     */
    protected function _validateOptional($value, $default = 'optional')
    {
        if(in_array($value, ['hidden', 'optional', 'required'])) {
            return $value;
        }

        if(in_array($default, ['hidden', 'optional', 'required'])) {
            return $default;
        }

        return 'optional';
    }
}
