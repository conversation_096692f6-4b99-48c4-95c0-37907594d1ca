<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Subscribers;

use App\Models\Collections\Collections;
use App\Models\Product\Product;
use Illuminate\Pagination\LengthAwarePaginator;

class ViewCollection
{
    /**
     * @var Collections
     */
    public $collection;

    /**
     * @var LengthAwarePaginator|Product[]
     */
    public $products;

    /**
     * @param mixed $collection
     * @param mixed $products
     * @return mixed
     */
    public function __construct($collection, $products)
    {
        $this->collection = $collection;
        $this->products = $products;
    }

}
