<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Applications;

use App\Contracts\OrderContract;
use App\Helper\Format;
use App\Helper\Store\CartTotal;
use App\Helper\YesNo;
use App\Models\Blog\Article;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerBillingAddress;
use App\Models\Customer\CustomerShippingAddress;
use App\Models\Discount\Discount;
use App\Models\Layout\FormFieldOptions;
use App\Models\Product\Category;
use App\Models\Product\Variant;
use App\Models\Product\Vendor;
use App\Models\Store\Cart as CartModel;
use App\Models\Store\CartItem;
use App\Models\Tax\Tax;
use App\LiquidEngine\Assetics\Subscribers\ListArticles;
use App\Models\Order\Order;
use App\Models\Product\Product;
use App\Models\Store\Cart;
use Apps;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Arr;
use Omniship\Common\Address;
use Auth;

class DataLayerEventSubscriber extends AbstractEventSubscriber
{

    /**
     * @inheritDoc
     */
    public function allListen(): void
    {
        if ($this->isGet()) {
            $this->addPost(view('resources::apps.dataLayer.customer-init')->render());
        }
    }

    /**
     * @inheritDoc
     */
    public function homePage($event): void
    {
        $this->addPost(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults(['type' => 'home'])
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function viewProduct($event): void
    {
        if (empty($event->product)) {
            return;
        }

        $method = $this->isAjax() ? 'addEnd' : 'addPost';
        $this->$method(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults($this->formatProduct($event->product))
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function listCategories($event): void
    {
        if (empty($event->categories) || $event->categories->isEmpty()) {
            return;
        }

        $this->addPost(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults(['type' => 'categories', 'categories' => $event->categories->map(function ($category): array {
                return $this->formatCategory($category);
            })->all()])
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function viewCategory($event): void
    {
        if (empty($event->category)) {
            return;
        }

        $method = $this->isAjax() ? 'addEnd' : 'addPost';
        $this->$method(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults($this->formatCategory($event->category) + ['products' => $event->products ? $event->products->map(function ($product): array {
                    return $this->formatProduct($product);
                }) : []])
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function listVendors($event): void
    {
        if (empty($event->vendors) || $event->vendors->isEmpty()) {
            return;
        }

        $this->addPost(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults(['type' => 'vendors', 'vendors' => $event->vendors->map(function ($vendor): array {
                return $this->formatVendor($vendor);
            })->all()])
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function viewVendor($event): void
    {
        if (empty($event->vendor)) {
            return;
        }

        $method = $this->isAjax() ? 'addEnd' : 'addPost';
        $this->$method(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults($this->formatVendor($event->vendor)) + ['products' => $event->products ? $event->products->map(function ($product): array {
                    return $this->formatProduct($product);
                }) : []]
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function page($event): void
    {
        if (empty($event->page)) {
            return;
        }

        $this->addPost(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults([
                'type' => 'page',
                'id' => $event->page->id,
                'name' => $event->page->name,
                'url' => $event->page->url(),
            ])
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function contacts($event): void
    {
        if (!empty($event->product)) {
            $data = array_merge($this->formatProduct($event->product), ['type' => 'product-request']);
        } else {
            $data = ['type' => 'contacts'];
        }

        $method = $this->isAjax() ? 'addEnd' : 'addPost';
        $this->$method(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults($data)
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function listArticles($event): void
    {
        if (empty($event->articles)) {
            return;
        }

        $this->addPost(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults($this->formatBlog($event) + ['articles' => $event->articles->map(function ($vendor): array {
                    return $this->formatArticle($vendor);
                })->all()])
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function viewArticle($event): void
    {
        if (empty($event->article)) {
            return;
        }

        $this->addPost(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults($this->formatArticle($event->article))
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function viewCart($event): void
    {
        if (empty($event->cart)) {
            return;
        }

        $this->addPost(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults($this->formatCart($event->cart) + ['type' => 'cart'])
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function checkout($event): void
    {
        if (empty($event->cart)) {
            return;
        }

        $method = $this->isAjax() ? 'addEnd' : 'addPost';
        $this->$method(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults($this->formatCart($event->cart) + ['type' => 'checkout'])
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function returnPageOrder($event): void
    {
        if (empty($event->order)) {
            return;
        }

        $method = $this->isAjax() ? 'addEnd' : 'addPost';
        $this->$method(view('resources::apps.dataLayer.page-data', [
            'data' => $this->withDefaults($this->formatOrder($event->order) + ['type' => 'checkout'])
        ])->render());
    }

//    /**
//     * @inheritDoc
//     */
//    public function fastCheckout($event)
//    {
//
//    }
//
//    /**
//     * @inheritDoc
//     */
//    public function fastCheckoutSubmit($event)
//    {
//
//    }

    /**
     * @inheritDoc
     */
    public function customerListAll($key, $events)
    {
        if ($this->isGet()) {
            $method = $this->isAjax() ? 'addEnd' : 'addPost';
            $this->$method(view('resources::apps.dataLayer.page-data', [
                'data' => $this->withDefaults(['type' => 'account'])
            ])->render());
        } elseif ($this->isPost()) {
            return [
                'js-callback' => [
                    'dataLayer' => [
                        [
                            'arguments' => ['cc_customer_data' => $this->getCustomerDataFormatted()],
                            'method' => 'push'
                        ]
                    ]
                ]
            ];
        }
    }

    /**
     * @param array $data
     * @return array
     */
    protected function withDefaults(array $data): array
    {
        return array_merge($data, [
            'iid' => site('industry'),
            'ga_enabled' => Apps::enabled('google_analytics')
        ]);
    }

    /**
     * @param Product $product
     * @return array
     */
    protected function formatProduct($product): array
    {
        $data = [
            'type' => 'product',
            'id' => $product->id,
            'name' => $product->name,
            'price' => $product->price_from_input,
            'discount_price' => $product->price_from_discounted_input ?? '',
            'currency' => $product->currency_code,
            'category' => $product->category->name ?? '',
            'category_path' => $product->category ? $product->category->path->implode('name', ' > ') : null,
            'brand' => $product->vendor->name ?? '',
            'sku' => $product->variant->sku ?? '',
            'barcode' => $product->variant->barcode ?? '',
            'digital' => $product->digital,
            'url' => $product->url,
            'has_image' => ($hasImage = $product->hasImage()),
            'image_url' => $hasImage ? $product->getImage() : '',
            'product_type' => $product->type
        ];

        if ($product->relationLoaded('variants') && $product->total_variants > 0) {
            $data['variants'] = $product->variants->map(function (Variant $variant) use ($product): array {
                $parameters = [];
                for ($i = 1; $i <= $product->total_variants; $i++) {
                    $parameters[] = [
                        'param_name' => $product->getAttribute('p' . $i),
                        'param_value' => $variant->getAttribute('v' . $i)
                    ];
                }

                return [
                    'id' => $variant->id,
                    'parameters' => $parameters,
                    'price' => $variant->price_input,
                    'discount_price' => $variant->price_discounted_input,
                    'availability' => $variant->stock_status_key,
                    'sku' => $variant->sku,
                    'barcode' => $variant->barcode,
                ];
            })->all();
        }

        return $data;
    }

    /**
     * @param Category $category
     * @return array
     */
    protected function formatCategory($category): array
    {
        $data = [
            'type' => 'category',
            'name' => $category->name,
            'id' => $category->id,
            'url' => $category->url,
            'has_image' => ($hasImage = $category->hasImage()),
            'image_url' => $hasImage ? $category->getImage() : '',
            'breadcrumb' => [],
            'child' => []
        ];

        if ($category->relationLoaded('path')) {
            $data['breadcrumb'] = $category->path->map(function (Category $category): array {
                return [
                    'name' => $category->name,
                    'url' => $category->url,
                ];
            })->all();
        }

        if ($category->relationLoaded('childrenRecursive') && $category->childrenRecursive->isNotEmpty()) {
            $data['child'] = $category->childrenRecursive->map(function (Category $category): array {
                return $this->formatCategory($category);
            })->all();
        }

        return $data;
    }

    /**
     * @param Vendor $vendor
     * @return array
     */
    protected function formatVendor($vendor): array
    {
        return [
            'type' => 'vendor',
            'name' => $vendor->name,
            'id' => $vendor->id,
            'url' => $vendor->url,
            'has_image' => ($hasImage = $vendor->hasImage()),
            'image_url' => $hasImage ? $vendor->getImage() : '',
        ];
    }

    /**
     * @param ListArticles $event
     * @return array
     */
    protected function formatBlog($event): array
    {
        return [
            'type' => 'blog',
            'subtype' => $event->blog ? 'category' : ($event->tag ? 'tag' : ''),
            'name' => $event->blog->name ?? $event->tag->tag ?? '',
            'id' => $event->blog->id ?? $event->tag->id ?? '',
            'url' => $event->blog->url ?? $event->tag->url ?? route('blog.list'),
            'has_image' => ($hasImage = ($event->blog ? $event->blog->hasImage() : false)),
            'image_url' => $hasImage ? $event->blog->getImage() : '',
        ];
    }

    /**
     * @param Article $article
     * @return array
     */
    protected function formatArticle($article): array
    {
        return [
            'type' => 'article',
            'id' => $article->id,
            'blog_id' => $article->blog_id,
            'name' => $article->name,
            'url' => $article->url,
            'has_image' => ($hasImage = $article->hasImage()),
            'image_url' => $hasImage ? $article->getImage() : '',
        ];
    }

    /**
     * @param Cart $cart
     * @return array
     */
    protected function formatCart($cart): array
    {
        return [
            'id' => $cart->id,
            'subtotal' => $cart->getSubTotal('input'),
            'discount' => $cart->getCheckout()->getTotalsSimple()
                ->filter(function ($key): bool {
                    return strpos($key->key, 'discount.') !== false;
                })->sum('price_input'),
            'total' => $cart->getTotal('input'),
            'currency' => site('currency'),
            'products_count' => $cart->products_count,
            'products' => $cart->products->map(function (CartItem $item): array {
                return $this->formatCartProduct($item);
            })->all(),
            'discounts' => $cart->getCheckout()->getDiscounts()->map(function (Discount $discount): array {
                return
                    [
                        'type' => 'discount',
                        'id' => $discount->id,
                        'name' => !empty($discount->name) ? $discount->name : '',
                        'discount_type' => !empty($discount->type) ? $discount->type : '',
                        'date_start' => !empty($discount->date_start) ? Format::datetime($discount->date_start) : '',
                        'date_end' => !empty($discount->date_end) ? Format::datetime($discount->date_end) : '',
                        'active' => $discount->active == YesNo::True ? true : false,
                        'value' => $discount->type_value_formatted,
                    ];
            })->all(),
            'chosen_shipping' => $this->formatCheckoutShipping($cart->getCheckout()),
            'shipping_addresses' => $cart->getShippingAddresses()->map(function ($address): array {
                return $this->formatAddress($address, 'shipping_address');
            })->all(),
            'chosen_billing_address' => $this->formatAddress($cart->getBillingAddress(), 'billing_address'),
            'billing_addresses' => $cart->getBillingAddresses()->map(function ($address): array {
                return $this->formatAddress($address, 'billing_address');
            })->all(),
//            'taxes' => $cart->getCheckout()->getTaxes()->map(function (Tax $tax) {
//                return [
//                    'type' => 'tax',
//                    'id' => $tax->id,
//                    'name' => !empty($tax->name) ? $tax->name : '',
//                    'tax_type' => !empty($tax->type) ? $tax->type : '',
//                    'value' => $tax->tax_formatted,
//                ];
//            }),
            'totals' => $cart->getCheckout()->getTotalsSimple()->map(function (CartTotal $total) {
                return $this->formatTotals($total);
            })->values()
        ];
    }

    /**
     * @param Order $order
     * @return array
     */
    protected function formatOrder($order): array
    {

        $products = $order->manager->getProducts();

        $discounts = $order->manager->getTotalsSimple()
            ->filter(function ($key): bool {
                return strpos($key->key, 'discount.') !== false;
            });

        $discountsFormatted = $discounts->map(function (CartTotal $total) {
            $data = Arr::only($total->toArray(), [
                'name', 'description', 'currency'
            ]);

            $data['price'] = number_format($total->price_input * -1, 2, '.', '');

            return $data;
        })->values();

        return [
            'type' => 'checkout',
            'id' => $order->id,
            'status' => $order->status,
            'status_formatted' => $order->status_formatted,
            'fulfillment_status' => $order->status_fulfillment,
            'fulfillment_status_formatted' => $order->status_fulfillment_formatted,
            'subtotal' => $order->manager->getSubTotal('input'),
            'discount' => number_format($discounts->sum('price_input') * -1, 2, '.', ''),
            'total' => $order->manager->getTotal('input'),
            'currency' => site('currency'),
            'products_count' => $products->count(),
            'products' => $products->map(function (CartItem $item): array {
                return $this->formatCartProduct($item);
            })->values()->all(),
            'discounts' => $discountsFormatted->toArray(),
            'chosen_shipping' => $this->formatCheckoutShipping($order->manager),
            'shipping_addresses' => $order->manager->getShippingAddresses()->map(function ($address): array {
                return $this->formatAddress($address, 'shipping_address');
            })->all(),
            'chosen_billing_address' => $this->formatAddress($order->manager->getBillingAddress(), 'billing_address'),
            'billing_addresses' => $order->manager->getBillingAddresses()->map(function ($address): array {
                return $this->formatAddress($address, 'billing_address');
            })->all(),
            'taxes' => $order->manager->getNotVatTaxes()->map(function (Tax $tax): array {
                return [
                    'type' => 'tax',
                    'id' => $tax->id,
                    'name' => !empty($tax->name) ? $tax->name : '',
                    'tax_type' => !empty($tax->type) ? $tax->type : '',
                    'value' => $tax->tax_formatted,
                ];
            }),
            'payment' => [
                'status' => $order->payment->status,
                'status_formatted' => $order->payment->status_formatted,
                'provider' => $order->payment->provider,
                'provider_name' => $order->payment->provider_name
            ],
            'created_at' => $order->date_added->toIso8601String(),
            'vat_included' => $order->manager->hasVatIncluded(),
            'totals' => $order->manager->getTotalsSimple()->map(function (CartTotal $total) {
                return $this->formatTotals($total);
            })->values()
        ];
    }

    /**
     * @param CartTotal $total
     * @return array
     */
    protected function formatTotals(CartTotal $total)
    {
        $data = Arr::only($total->toArray(), [
            'name', 'description', 'currency', 'value'
        ]);

        $key = explode('.', $total->key);
        $data['key'] = $key[0];
        $data['subkey'] = $key[1] ?? '';
        $data['price'] = $total->price_input;

        return $data;
    }

    /**
     * @param CustomerShippingAddress|CustomerBillingAddress|Address|null $address
     * @param $type
     * @return array
     */
    protected function formatAddress($address, $type): array
    {
        if (empty($address)) {
            return [];
        }

        if ($address instanceof Address) {
            $address = (object)$address->toArray();
        }

        return [
            'type' => $type,
            'id' => $address->id,
            'address' => !empty($address->text) ? $address->text : '',
            'post_code' => !empty($address->post_code) ? $address->post_code : '',
            'phone' => !empty($address->phone) ? $address->phone : '',
        ];
    }

    /**
     * @param CartItem $item
     * @return array
     */
    protected function formatCartProduct(CartItem $item): array
    {
        return [
            'type' => 'product',
            'id' => $item->product_id,
            'name' => $item->name,
            'category' => !empty($item->category_name) ? $item->category_name : '',
            'sku' => !empty($item->sku) ? $item->sku : '',
            'barcode' => !empty($item->barcode) ? $item->barcode : '',
            'brand' => !empty($item->vendor_name) ? $item->vendor_name : '',
            'price' => $item->total_input,
            'discount_price' => !is_null($item->discount_total_input) ? $item->discount_total_input : '',
            'single_price' => $item->price_input,
            'discount_single_price' => !is_null($item->discount_price_input) ? $item->discount_price_input : '',
            'quantity' => $item->quantity,
            'currency' => $item->currency,
            'digital' => $item->is_digital ? true : false,
            'availability' => !empty($item->variant->item->active) && $item->variant->item->active == YesNo::True ? 'active' : 'inactive',
            'url' => $item->url,
            'image_url' => $item->image,
            'parameters' => $item->parameters,
        ];
    }

    /**
     * @param OrderContract $order
     * @return array
     */
    protected function formatCheckoutShipping($order): array
    {
        if (!($shipping = $order->getShipping())) {
            return [];
        }

        $data = [
            'name' => $shipping->name,
            'service_id' => $shipping->service_id,
            'service_name' => $shipping->service_name,
            'key' => $shipping->key,
            'price' => $shipping->price,
            'price_input' => Format::moneyInput($shipping->price),
            'price_formatted' => Format::money($shipping->price),
        ];

        if ($address = $order->getShippingAddress()) {
            $data = array_merge($data, $this->formatAddress($address, 'shipping_address'));
        }

        return $data;
    }

    /**
     * @return array|Customer|Authenticatable|null
     * @throws Throwable
     */
    protected function _getCustomer()
    {
        if (Auth::customerId()) {
            return Auth::customer();
        } elseif ($cartInstance = $this->getCart()) {
            return $cartInstance->customer;
        }

        return null;
    }

    /**
     * @return CartModel|bool
     * @throws Throwable
     */
    protected function getCart()
    {
        return CartModel::instance();
    }

    /**
     * @return array|null
     * @throws Throwable
     */
    public function getCustomerDataFormatted(): array
    {
        /** @var Customer $customer */
        if (!is_null($customer = Auth::customer())) {
            return $this->getCustomerData($customer, 'customer');
        } elseif (!is_null($customer = $this->_getCustomer()) && $customer->isGuest()) {
            return $this->getCustomerData($customer, 'guest');
        } else {
            return $this->getCustomerData(Auth::guest(), 'guest');
        }
    }

    /**
     * @param Customer $customer
     * @param $type
     * @return array
     */
    protected function getCustomerData($customer, $type): array
    {
        $customFields = [];

        if ($customer->custom_data) {
            $options = FormFieldOptions::get()->keyBy('id');
            foreach ($customer->custom_data as $data) {
                // TODO: zahi check this
                if (is_array($data->value)) {
                    continue;
                }

                $option = $options->get($data->value);
                if ($option && $data->field) {
                    $customFields[$data->field->name] = $option->name;
                }
            }
        }

        return array_merge([
            'id' => $customer->id,
            'group_id' => $customer->group_id,
            'group_name' => $customer->group_id && $customer->group ? $customer->group->name : null,
            'first_name' => $customer->first_name,
            'last_name' => $customer->last_name,
            'full_name' => $customer->full_name,
            'email' => $customer->email,
            'type' => $type,
            'cpadm' => (int)!!Auth::adminId()
        ], $customFields);
    }
}
