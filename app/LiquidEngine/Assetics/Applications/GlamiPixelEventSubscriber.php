<?php

declare(strict_types=1);

namespace App\LiquidEngine\Assetics\Applications;

use App\Helper\Html;
use App\Models\Order\OrderProduct;
use Apps;

class GlamiPixelEventSubscriber extends AbstractEventSubscriber
{

    protected $_pixel_key;

    /**
     * @inheritDoc
     */
    public function allListen(): void
    {
        if(!$this->allowJsData()) {
            return;
        }

        if(!$this->isAjax() && $this->isGet()) {
            $this->addPost(Html::script($this->_initScript()));
        }

        $this->addEnd(view('resources::apps.glami', [
            'arguments' => ['track', 'PageView']
        ])->render());
    }

    /**
     * @inheritDoc
     */
    public function returnPageOrder($event): void
    {
        if ($this->isAllowed() && !empty($event->order)) {
            $data = [
                'content_type' => 'product',
                'content_ids' => $event->order->products->pluck('product_id')->all(),
                'transaction_id' => $event->order->id,
                'value' => $event->order->price_total_input,
                'currency' => site('currency'),
                'email' => $event->order->customer_email
            ];

            $this->addEnd(view('resources::apps.glami', ['arguments' => ['track', 'Purchase', $data]])->render());
        }
    }

    //ajax
    /**
     * @inheritDoc
     */
    public function addItemCart($event): array
    {
        if(!$this->allowJsData()) {
            return [];
        }

        if($item = $event->item) {
            $data = [
                'item_ids' => [$item->p1 ? sprintf('%d-%d', $item->product_id, $item->variant_id) : $item->product_id],
                'product_names' => [$item->name],
                'value' => $item->discount ? $item->discount_price_input : $item->price_input,
                'currency' => site('currency'),
            ];
            return [
                'js-callback' => [
                    '' => [
                        [
                            'arguments' => ['track', 'AddToCart', $data],
                            'method' => 'glami'
                        ]
                    ]
                ]
            ];
        }

        return [];
    }

    /**
     * @inheritDoc
     */
    public function addBundleCart($event): array
    {
        if(!$this->allowJsData()) {
            return [];
        }

        if($bundle = $event->bundle) {
            $data = [
                'item_ids' => [$bundle->id],
                'product_names' => [$bundle->name],
                'value' => $bundle->price_input,
                'currency' => site('currency'),
            ];

            return [
                'js-callback' => [
                    '' => [
                        [
                            'arguments' => ['track', 'AddToCart', $data],
                            'method' => 'glami'
                        ]
                    ]
                ]
            ];
        }

        return [];
    }

    /**
     * @inheritDoc
     */
    public function viewProduct($event): void
    {
        if ($this->isAllowed() && !empty($event->product)) {
            $this->addEnd(view('resources::apps.glami', [
                'arguments' => ['track', 'ViewContent', [
                    'content_type' => 'product',
                    'content_ids' => [$event->product->id],
                    'content_name' => $event->product->name,
                    'content_category' => $event->product->category->path->implode('name', ' > '),
                    'value' => $event->product->price_from_input,
                    'currency' => site('currency')
                ]]
            ])->render());
        }
    }

    /**
     * @return bool
     */
    protected function allowJsData(): bool
    {
        return Apps::installed('app.xml_feed.glami') && $this->getPixelKey();
    }

    /**
     * @return bool|string
     */
    protected function getPixelKey()
    {
        if (is_null($this->_pixel_key)) {
            $this->_pixel_key = Apps::setting('app.xml_feed.glami', 'pixel') ?: false;
        }

        return $this->_pixel_key;
    }

    /**
     * @return string
     */
    protected function _initScript(): string
    {
        return '(function(f, a, s, h, i, o, n) {f[\'GlamiTrackerObject\'] = i;
f[i]=f[i]||function(){(f[i].q=f[i].q||[]).push(arguments)};o=a.createElement(s),
n=a.getElementsByTagName(s)[0];o.async=1;o.src=h;n.parentNode.insertBefore(o,n)
})(window, document, \'script\', \'//www.glami.bg/js/compiled/pt.js\', \'glami\');

if(glami && typeof glami == \'function\') {
glami(\'create\', \'' . $this->getPixelKey() . "', '" . app()->getLocale() . '\');
}';
    }
}
