# CloudCart Theme Assets Loading Debug Plan

## Important Rule
**Customer themes must not be modified.** Our approach must focus exclusively on improving the Liquid Engine implementation to properly handle existing themes without altering their structure or content.

## Investigation Plan

1. **Verify theme configuration**
   - Check if the Horizon theme is properly registered in the system
   - Confirm theme directory structure is correct
   - Verify theme registration in database or configuration

2. **Inspect asset URL generation**
   - Examine how asset URLs are being generated
   - Check for any errors in the URL paths
   - Verify the `UrlGenerator` service is working correctly
   - Test asset URL generation for theme.css, theme.js, etc.

3. **Debug asset loading in browser**
   - Check browser console for errors
   - Inspect network tab for failed requests
   - Verify correct HTTP responses for asset URLs
   - Check request/response headers for clues

4. **Analyze asset delivery system**
   - Inspect the `AssetsController` that handles asset requests
   - Check if assets exist in the correct locations
   - Verify file permissions on theme assets
   - Test file access through the controller directly

5. **Test asset loading directly**
   - Test accessing theme.css and theme.js directly
   - Check if individual assets can be loaded correctly
   - Verify content type headers and response bodies

6. **Review template rendering**
   - Check if asset URLs are correctly injected into templates
   - Verify if theme.liquid layout includes the proper asset tags
   - Examine the layout file for proper inclusion of assets

7. **Implement solution**
   - Based on findings, create a targeted fix in the Liquid Engine
   - Ensure solution works without modifying customer themes
   - Test the solution on both regular pages and 404 page
   - Document the solution for future reference

## Implementation Constraints
- All fixes must be implemented in the CloudCart Liquid Engine
- No modifications to customer themes are allowed
- Solutions must be backward compatible with existing themes
- Any changes must follow CloudCart coding standards 