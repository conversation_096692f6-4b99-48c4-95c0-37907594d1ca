<?php

declare(strict_types=1);

namespace App\LiquidEngine\Services;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\LiquidEngine\Services\ThemeAssetsMapper;

/**
 * Service for handling theme assets
 */
class ThemeAssetsService
{
    /**
     * @var ThemeAssetsMapper
     */
    protected ThemeAssetsMapper $assetsMapper;
    
    /**
     * Constructor
     * 
     * @param ThemeAssetsMapper|null $assetsMapper
     */
    public function __construct(?ThemeAssetsMapper $assetsMapper = null)
    {
        $this->assetsMapper = $assetsMapper ?? app()->make(ThemeAssetsMapper::class);
    }
    
    /**
     * Get an asset by path
     *
     * @param string $path
     * @return array|null
     */
    public function getAsset(string $path): ?array
    {
        // Check in current theme
        $themeKey = site('theme_key');
        $themePath = resource_path("themes/{$themeKey}/assets/{$path}");
        
        if (File::exists($themePath)) {
            return $this->createAssetResponse($themePath);
        }
        
        // Check in global theme
        $globalPath = resource_path("themes/_global/assets/{$path}");
        
        if (File::exists($globalPath)) {
            return $this->createAssetResponse($globalPath);
        }
        
        return null;
    }
    
    /**
     * Process an image with transformations
     *
     * @param string $path
     * @param array $options
     * @return array|null
     */
    public function processImage(string $path, array $options = []): ?array
    {
        // Get the original image
        $asset = $this->getAsset($path);
        
        if (!$asset || !Str::startsWith($asset['mime_type'], 'image/')) {
            return null;
        }
        
        // Create an image from the content
        $sourceImage = imagecreatefromstring($asset['content']);
        if (!$sourceImage) {
            return null;
        }
        
        // Get original dimensions
        $srcWidth = imagesx($sourceImage);
        $srcHeight = imagesy($sourceImage);
        
        // Calculate new dimensions
        $width = !empty($options['width']) ? (int) $options['width'] : $srcWidth;
        $height = !empty($options['height']) ? (int) $options['height'] : $srcHeight;
        
        // Maintain aspect ratio if only one dimension is specified
        if (!empty($options['width']) && empty($options['height'])) {
            $height = (int) round(($width / $srcWidth) * $srcHeight);
        } elseif (empty($options['width']) && !empty($options['height'])) {
            $width = (int) round(($height / $srcHeight) * $srcWidth);
        }
        
        // Apply scale if specified
        if (!empty($options['scale'])) {
            $scalePercent = min(100, max(1, (int) $options['scale']));
            $scaleFactor = (int) ($scalePercent / 100 * 100); // Convert to integer percentage
            $width = (int) (($width * $scaleFactor) / 100);
            $height = (int) (($height * $scaleFactor) / 100);
        }
        
        // Create destination image
        $destImage = imagecreatetruecolor($width, $height);
        
        // Preserve transparency for PNG images
        if ($asset['mime_type'] === 'image/png') {
            imagealphablending($destImage, false);
            imagesavealpha($destImage, true);
            $transparent = imagecolorallocatealpha($destImage, 255, 255, 255, 127);
            imagefilledrectangle($destImage, 0, 0, $width, $height, $transparent);
        }
        
        // Handle crop if specified
        $crop = $options['crop'] ?? null;
        if ($crop && $width !== $srcWidth && $height !== $srcHeight) {
            // Calculate source dimensions and offsets
            $srcX = 0;
            $srcY = 0;
            
            // Calculate aspect ratios
            $srcRatio = $srcWidth / $srcHeight;
            $destRatio = $width / $height;
            
            if ($srcRatio > $destRatio) {
                // Source is wider than destination
                $srcNewWidth = round($srcHeight * $destRatio);
                $srcX = round(($srcWidth - $srcNewWidth) / 2);
                
                // Adjust based on crop position
                if ($crop === 'left') {
                    $srcX = 0;
                } elseif ($crop === 'right') {
                    $srcX = $srcWidth - $srcNewWidth;
                }
                
                $srcWidth = $srcNewWidth;
            } elseif ($srcRatio < $destRatio) {
                // Source is taller than destination
                $srcNewHeight = round($srcWidth / $destRatio);
                $srcY = round(($srcHeight - $srcNewHeight) / 2);
                
                // Adjust based on crop position
                if ($crop === 'top') {
                    $srcY = 0;
                } elseif ($crop === 'bottom') {
                    $srcY = $srcHeight - $srcNewHeight;
                }
                
                $srcHeight = $srcNewHeight;
            }
            
            // Resize and crop the image
            imagecopyresampled($destImage, $sourceImage, 0, 0, $srcX, $srcY, $width, $height, $srcWidth, $srcHeight);
        } else {
            // Simple resize without cropping
            imagecopyresampled($destImage, $sourceImage, 0, 0, 0, 0, $width, $height, $srcWidth, $srcHeight);
        }
        
        // Apply filters if specified
        if (!empty($options['grayscale'])) {
            imagefilter($destImage, IMG_FILTER_GRAYSCALE);
        }
        
        if (!empty($options['brightness'])) {
            $brightness = min(255, max(-255, (int) $options['brightness']));
            imagefilter($destImage, IMG_FILTER_BRIGHTNESS, $brightness);
        }
        
        if (!empty($options['contrast'])) {
            $contrast = min(100, max(-100, (int) $options['contrast']));
            imagefilter($destImage, IMG_FILTER_CONTRAST, $contrast);
        }
        
        // Start output buffering
        ob_start();
        
        // Output the image based on the original mime type or requested format
        $format = $options['format'] ?? null;
        $quality = isset($options['quality']) ? (int) $options['quality'] : 90;
        
        if ($format === 'webp') {
            // Check if WebP is supported
            if (function_exists('imagewebp')) {
                imagewebp($destImage, null, $quality);
                $mimeType = 'image/webp';
            } else {
                // Fall back to JPEG if WebP is not supported
                imagejpeg($destImage, null, $quality);
                $mimeType = 'image/jpeg';
            }
        } elseif ($format === 'png' || (!$format && $asset['mime_type'] === 'image/png')) {
            // PNG compression level is 0-9 (9 is max compression)
            $pngQuality = (int)(9 - round($quality / 10));
            imagepng($destImage, null, $pngQuality);
            $mimeType = 'image/png';
        } elseif ($format === 'gif' || (!$format && $asset['mime_type'] === 'image/gif')) {
            imagegif($destImage);
            $mimeType = 'image/gif';
        } else {
            // Default to JPEG
            imagejpeg($destImage, null, $quality);
            $mimeType = 'image/jpeg';
        }
        
        // Get the image data
        $imageData = ob_get_clean();
        
        // Free up memory
        imagedestroy($sourceImage);
        imagedestroy($destImage);
        
        return [
            'content' => $imageData,
            'mime_type' => $mimeType,
        ];
    }
    
    /**
     * Create an asset response array
     *
     * @param string $path
     * @return array
     */
    protected function createAssetResponse(string $path): array
    {
        $content = File::get($path);
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        
        // Set proper MIME types based on file extension
        $mimeType = match($extension) {
            'css' => 'text/css',
            'js' => 'application/javascript',
            'svg' => 'image/svg+xml',
            'json' => 'application/json',
            'ttf' => 'font/ttf',
            'otf' => 'font/otf',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'eot' => 'application/vnd.ms-fontobject',
            default => File::mimeType($path)
        };
        
        return [
            'content' => $content,
            'mime_type' => $mimeType,
        ];
    }
    
    /**
     * Handle special theme asset requests
     * 
     * This method handles theme-specific asset loading, particularly for themes
     * that use a different structure than the standard theme.css/theme.js files.
     * 
     * @param string $path The requested asset path
     * @return array|null The asset data or null if not found
     */
    public function getSpecialThemeAsset(string $path): ?array
    {
        // Get all possible asset paths for the requested path
        $possiblePaths = $this->assetsMapper->getPossibleAssetPaths($path);
        
        // Try each possible path
        foreach ($possiblePaths as $candidatePath) {
            // Skip the original path since we'll try it later in the standard flow
            if ($candidatePath === $path) {
                continue;
            }
            
            $asset = $this->getAsset($candidatePath);
            if ($asset) {
                return $asset;
            }
        }
        
        return null;
    }
} 