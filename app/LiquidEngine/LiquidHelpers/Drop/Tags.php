<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.4.2019 г.
 * Time: 12:47 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use ArrayIterator;
use IteratorAggregate;
use Liquid\Drop;

class Tags extends AbstractDrop implements IteratorAggregate
{

    protected array $_tags;

    protected $_last_call = null;

    /**
     * Product constructor.
     * @param array $tags
     */
    public function __construct(array $tags)
    {
        $this->_tags = $tags;
    }

    public function size(): int
    {
        $this->_last_call = null;
        return count($this->_tags);
    }

    /**
     * @return ArrayIterator|\Traversable
     */
    public function getIterator()
    {
        $this->_last_call = null;
        return new ArrayIterator($this->_tags);
    }

    /**
     * @param mixed $name
     * @param mixed $arguments
     * @return mixed
     */
    public function __call($name, $arguments)
    {
        if (is_numeric($name)) {
            $this->_last_call = null;
            return $this->_tags[$name] ?? null;
        } elseif (in_array($name, ['implode', 'pluck'])) {
            $this->_last_call = $name;
            return $this;
        }

        if ($this->_last_call && method_exists($this, $method = '_' . \Illuminate\Support\Str::camel('get_' . $this->_last_call))) {
            $this->_last_call = null;
            return $this->$method($name);
        }

        return $this;
    }

    public function toArray(): array
    {
        return $this->_tags;
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    protected function _getPluck($name): array
    {
        return array_filter(array_map(function (Tag $tag) use ($name) {
            if (method_exists($tag, $name)) {
                return $tag->$name();
            }

            return null;
        }, $this->_tags));
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    protected function _getImplode($name): string
    {
        return implode(', ', $this->_getPluck($name));
    }

}
