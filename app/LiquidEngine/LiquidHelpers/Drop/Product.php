<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.3.2019 г.
 * Time: 18:14 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop;

use App\Exceptions\Error;
use App\Helper\Format;
use App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute;
use App\Helper\YesNo;
use App\Models\Category\PropertyOption as PropertyOptionModel;
use App\Models\Discount\ProductBanners;
use App\Models\Discount\ProductLabels;
use App\Models\Product\Image;
use App\Models\Product\Options;
use App\Models\Product\Tag as TagModel;
use App\Models\System\AppsManager;
use Carbon\Carbon;
use App\Models\Product\Product as ProductModel;
use App\LiquidEngine\LiquidHelpers\Drop\Category\PropertyOption;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Banner;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Discount;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Label;
use App\Models\Discount\Discount as DiscountModel;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Option;
use App\LiquidEngine\LiquidHelpers\Drop\Product\Variant;
use App\LiquidEngine\LiquidHelpers\Format\PriceParts;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\LiquidHelpers\Drop\Category\Category as LiquidCategory;
use App\LiquidEngine\LiquidHelpers\Drop\Variants\Variant as Parameter;
use App\LiquidEngine\Traits\DropImageToArray;
use App\Models\Product\Variant as VariantModel;

class Product extends AbstractDrop
{

    use DropImageToArray;

    /**
     * @var ProductModel $_product
     */
    protected $_product;

    /**
     * @var bool|int|null
     */
    protected $_selected_variant = false;

    /**
     * @var Variant
     */
    protected $_selected_or_first_available_variant;

    /**
     * @var VariantModel
     */
    protected $_variant;

    /**
     * Product constructor.
     * @param ProductModel|null $product
     * @throws Error
     */
    public function __construct(ProductModel $product)
    {
        $this->_product = $product->format();
    }

    /**
     * @return int
     */
    public function id()
    {
        return $this->_product->id;
    }

    /**
     * @return string
     */
    public function name()
    {
        return $this->_product->name;
    }

    /**
     * @return string
     */
    public function url()
    {
        return $this->_product->url;
    }

    /**
     * @return string
     */
    public function add_to_cart()
    {
        return route('cart.add', ['variant_id' => $this->_product->default_variant_id]);
    }

    /**
     * @return string
     */
    public function wishlist_url()
    {
        return route('add-to-wishlist', ['id' => $this->id()]);
    }

    /**
     * @return string
     */
    public function url_handle()
    {
        return $this->_product->url_handle;
    }

    /**
     * @return string
     */
    public function p1()
    {
        return $this->_product->p1;
    }

    /**
     * @return string
     */
    public function p2()
    {
        return $this->_product->p2;
    }

    /**
     * @return string
     */
    public function p3()
    {
        return $this->_product->p3;
    }

    /**
     * @return Parameter|null
     */
    public function parameter1(): ?\App\LiquidEngine\LiquidHelpers\Drop\Variants\Variant
    {
        return $this->p1() && $this->_product->relationLoaded('p1r') ? new Parameter($this->_product->p1r) : null;
    }

    /**
     * @return Parameter|null
     */
    public function parameter2(): ?\App\LiquidEngine\LiquidHelpers\Drop\Variants\Variant
    {
        return $this->p2() && $this->_product->relationLoaded('p2r') ? new Parameter($this->_product->p2r) : null;
    }

    /**
     * @return Parameter|null
     */
    public function parameter3(): ?\App\LiquidEngine\LiquidHelpers\Drop\Variants\Variant
    {
        return $this->p3() && $this->_product->relationLoaded('p3r') ? new Parameter($this->_product->p3r) : null;
    }

    /**
     * @return int
     */
    public function total_variants()
    {
        return $this->_product->total_variants ?? 0;
    }

    /**
     * @return bool
     */
    public function has_required_data(): bool
    {
        if ($this->total_variants()) {
            return true;
        }

        if (
            AppsManager::isInstalled('product_options') &&
            (
                $this->_product->getAttribute('required_fields_count') > 0 ||
                ($this->category() && $this->_product->category->getAttribute('required_fields_count') > 0) ||
                ($this->vendor() && $this->_product->vendor->getAttribute('required_fields_count') > 0)
            )
        ) {
            return true;
        }

        if (AppsManager::isInstalled('product_options') && $this->_product->fields->firstWhere('required', true)) {
            return true;
        }

        return false;
    }

    /**
     * @return LiquidCategory|null
     */
    public function category(): ?\App\LiquidEngine\LiquidHelpers\Drop\Category\Category
    {
        return $this->_product->category_id && $this->_product->relationLoaded('category') ? new LiquidCategory($this->_product->category) : null;
    }

    /**
     * @return Vendor|null
     */
    public function vendor(): ?\App\LiquidEngine\LiquidHelpers\Drop\Vendor
    {
        return $this->_product->vendor_id && $this->_product->relationLoaded('vendor') ? new Vendor($this->_product->vendor) : null;
    }

    /**
     * @return int
     */
    public function views()
    {
        return $this->_product->views;
    }

    /**
     * @return bool
     */
    public function tracking(): bool
    {
        return $this->_product->tracking == YesNo::True;
    }

    /**
     * @return int
     */
    public function threshold()
    {
        return $this->_product->threshold;
    }

    /**
     * @return bool
     */
    public function shipping(): bool
    {
        return $this->_product->shipping == YesNo::True;
    }

    /**
     * @return bool
     */
    public function digital(): bool
    {
        return $this->_product->digital == YesNo::Yes;
    }

    /**
     * @return bool
     */
    public function sale(): bool
    {
        return !!$this->_product->product_to_discount_id;
    }

    /**
     * @return bool
     */
    public function new(): bool
    {
        return $this->_product->new == YesNo::True;
    }

    /**
     * @return bool
     */
    public function free_shipping(): bool
    {
        return !!$this->_product->free_shipping;
    }

    /**
     * @return bool
     */
    public function featured(): bool
    {
        return !!$this->_product->featured;
    }

    /**
     * @return bool
     */
    public function diff_price(): bool
    {
        return $this->_product->price_from != $this->_product->price_from;
    }

    /**
     * @return int
     */
    public function price_from()
    {
        return $this->_product->price_from;
    }

    public function price_from_input()
    {
        return $this->_product->price_from_input;
    }

    public function price_from_formatted()
    {
        return $this->_product->price_from_formatted;
    }

    public function price_from_parts(): array
    {
        return PriceParts::format($this->price_from_input());
    }

    public function price_from_discounted()
    {
        return $this->_product->getAttribute('price_from_discounted');
    }

    public function price_from_discounted_input()
    {
        return $this->_product->getAttribute('price_from_discounted_input');
    }

    public function price_from_discounted_formatted()
    {
        return $this->_product->getAttribute('price_from_discounted_formatted');
    }

    public function price_from_discounted_parts(): ?array
    {
        if ($price = $this->price_from_discounted_input()) {
            return PriceParts::format($price);
        }

        return null;
    }

    public function price_saved(): int|float|null
    {
        return $this->price_from_discounted() > 0 ? $this->price_from() - $this->price_from_discounted() : null;
    }

    public function price_saved_formatted(): ?string
    {
        if ($price = $this->price_saved()) {
            return Format::money($price);
        }

        return null;
    }

    public function price_saved_input(): string|int|null
    {
        if ($price = $this->price_saved()) {
            return Format::moneyInput($price);
        }

        return null;
    }

    public function price_saved_parts(): ?array
    {
        if ($price = $this->price_saved_input()) {
            return PriceParts::format($price);
        }

        return null;
    }

    public function price_to()
    {
        return $this->_product->price_to;
    }

    public function price_to_input()
    {
        return $this->_product->price_to_input;
    }

    public function price_to_parts(): array
    {
        return PriceParts::format($this->price_to_input());
    }

    public function price_to_formatted()
    {
        return $this->_product->price_to_formatted;
    }

    public function price_percent()
    {
        return $this->_product->price_percent;
    }

    public function price_type()
    {
        return $this->_product->price_type;
    }

    public function continue_selling(): bool
    {
        return $this->_product->continue_selling == YesNo::True;
    }

    public function date_added(): \App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return new Date($this->_product->date_added);
    }

    public function date_modified(): \App\LiquidEngine\LiquidHelpers\Drop\Date
    {
        return new Date($this->_product->date_modified);
    }

    public function active_to(): \App\LiquidEngine\LiquidHelpers\Drop\Date|array|int|float|string|false|null
    {
        if ($this->_product->active_to) {
            return new Date($this->_product->active_to);
        }

        return $this->_product->active_to;
    }

    public function short_description()
    {
        return $this->_product->short_description;
    }

    public function type()
    {
        return $this->_product->type;
    }

    public function is_discounted()
    {
        return $this->_product->getAttribute('is_discounted');
    }

    public function product_to_discount_price()
    {
        return $this->_product->getAttribute('product_to_discount_price');
    }

    public function product_to_discount_price_to()
    {
        return $this->_product->getAttribute('product_to_discount_price_to');
    }

    public function min_price_with_discounted()
    {
        return $this->_product->getAttribute('min_price_with_discounted');
    }

    public function max_price_with_discounted()
    {
        return $this->_product->getAttribute('max_price_with_discounted');
    }

    public function quantity()
    {
        return $this->_product->quantity;
    }

    public function enable_online_selling()
    {
        if (in_array(site('site_id'), [402, 8766])) {
            return $this->_product->getAttribute('enable_online_selling');
        }

        return true;
    }

    /**
     * @return bool|Product\Label
     */
    public function overlay(): \App\LiquidEngine\LiquidHelpers\Drop\Product\Label|false
    {
        return $this->_product->getAttribute('overlay') ? new Product\Label($this->_product->getAttribute('overlay')) : false;
    }

    public function favorite(): bool
    {
        return !!$this->_product->favorite;
    }

    /**
     * @return Variant
     */
    public function variant(): \App\LiquidEngine\LiquidHelpers\Drop\Product\Variant
    {
        if ($this->_variant) {
            return new Variant($this->_variant, $this->_product);
        }

        return new Variant($this->_product->variant, $this->_product);
    }

    public function variants()
    {
        $variants = [];
        if ($this->total_variants() > 0 && $this->_product->relationLoaded('variants')) {
            $variants = $this->_product->variants->map(function ($variant): \App\LiquidEngine\LiquidHelpers\Drop\Product\Variant {
                return new Variant($variant, $this->_product);
            })->all();
        }

        return $variants;
    }

    /**
     * @return array|PropertyOption[]
     */
    public function category_properties()
    {
        $cp = collect();
        if ($this->category() && $this->_product->relationLoaded('categoryPropertiesOptions')) {
            $cp = $this->_product->categoryPropertiesOptions->filter(function (PropertyOptionModel $option): bool {
                return $option->relationLoaded('property') && $option->property->relationLoaded('categoriesIds') && $option->property->categoriesIds->where('category_id', $this->category()->id())->count();
            });
        }

        if ($cp->isEmpty()) {
            return [];
        }

        return $cp->map(function (PropertyOptionModel $option): \App\LiquidEngine\LiquidHelpers\Drop\Category\PropertyOption {
            return new PropertyOption($option, $this->category());
        })->all();
    }

    /**
     * @return array{property: mixed, options: non-empty-list<Modules\LiquidBuilder\LiquidEngine\LiquidHelpers\Drop\Category\PropertyOption>}[]
     */
    public function category_properties_grouped(): array
    {
        $cp = $this->category_properties();

        if (empty($cp)) {
            return [];
        }

        $results = [];
        foreach ($cp as $option) {
            if ($property = $option->property()) {
                $property_id = $property->id();
                if (!array_key_exists($property_id, $results)) {
                    $results[$property_id] = [
                        'property' => $property,
                        'options' => [$option]
                    ];
                } else {
                    $results[$property_id]['options'][] = $option;
                }
            }
        }

        return array_values($results);
    }

    /**
     * @return LiquidImageAttribute
     */
    public function image()
    {
        $formatter = new UrlImageFormat($this->_product->image ?: new Image());
        return $formatter->getLiquidImage();
    }

    /**
     * @return bool
     */
    public function has_image(): bool
    {
        return !!$this->_product->hasImage();
    }

    /**
     * @return bool
     */
    public function is_hidden(): bool
    {
        return !!$this->_product->is_hidden;
    }

    public function first_two_images()
    {
        $images = [];
        if ($this->_product->relationLoaded('first_two_images')) {
            $images = $this->_product->first_two_images->map(function ($image) {
                $formatter = new UrlImageFormat($image);
                return $formatter->getLiquidImage();
            })->all();
        }

        return $images;
    }

    public function tags()
    {
        return $this->_product->relationLoaded('tags') ? $this->_product->tags->map(function (TagModel $tag): \App\LiquidEngine\LiquidHelpers\Drop\Tag {
            return new Tag($tag);
        })->all() : [];
    }

    public function banners()
    {
        return $this->_product->banners->map(function ($banner): ?\App\LiquidEngine\LiquidHelpers\Drop\Product\Banner {
            if ($banner instanceof ProductBanners) {
                return new Banner($banner->discount);
            } elseif ($banner instanceof DiscountModel) {
                return new Banner($banner);
            }

            return null;
        })->filter()->all();
    }

    public function labels()
    {
        return $this->_product->labels->map(function ($label): ?\App\LiquidEngine\LiquidHelpers\Drop\Product\Label {
            if ($label instanceof ProductLabels) {
                if (is_null($label->discount_id)) {
                    return new Label(new DiscountModel($label->toArray()));
                } else {
                    return new Label($label->discount);
                }
            } elseif ($label instanceof DiscountModel) {
                return new Label($label);
            }

            return null;
        })->filter()->all();
    }

    public function discount(): ?\App\LiquidEngine\LiquidHelpers\Drop\Product\Discount
    {
        if (!$this->_product->product_to_discount_id) {
            return null;
        }

        if ($this->_product->product_to_discount->discount->type == 'fixed') {
            if ($this->selected_variant() && ($variant = $this->selected_or_first_available_variant())) {
                if (!$variant->discounted()) {
                    return null;
                }

                $this->_product->product_to_discount->discount->setAttribute('type_value_fixed', $variant->price_saved());
            } else {
                $this->_product->product_to_discount->discount->setAttribute('type_value_fixed', $this->price_saved());
            }
        }

        return new Discount($this->_product->product_to_discount->discount);
    }

    public function options()
    {
        $fields = collect();
        if (AppsManager::isInstalled('product_options')) {
            $fields = $this->_product->fields
                ->sortBy('sort_order', SORT_NUMERIC)->values();
        }

        return $fields->map(function (Options $option): \App\LiquidEngine\LiquidHelpers\Drop\Product\Option {
            return new Option($option);
        })->all();
    }

    public function total_options(): int
    {
        return count($this->options());
    }

    /**
     * @return bool|int|null
     */
    public function selected_variant()
    {
        if ($this->_selected_variant !== false) {
            return $this->_selected_variant;
        }

        if (
            !is_array($variant = request()->get('variant')) ||
            empty($variant) ||
            count($variant) != $this->_product->total_variants ||
            !$this->_product->relationLoaded('variants')
        ) {
            return $this->_selected_variant = null;
        }

        $filters = [];
        for ($i = 1; $i <= $this->_product->total_variants; $i++) {
            $parameter_id = $this->_product->getAttribute(sprintf('p%d_id', $i));
            if (!empty($variant[$parameter_id])) {
                $filters[] = [
                    sprintf('v%d_id', $i),
                    explode(',', $variant[$parameter_id])
                ];
            }
        }

        if (count($filters) != $this->_product->total_variants) {
            return $this->_selected_variant = null;
        }

        $variants = $this->_product->variants;
        foreach ($filters as $filter) {
            $variants = $variants->whereIn($filter[0], $filter[1]);
        }

        if ($variants->count() > 0) {
            return $this->_selected_variant = $variants->sortBy('price')
                ->first()->id;
        }

        return $this->_selected_variant = null;
    }

    /**
     * @return Variant
     */
    public function selected_or_first_available_variant()
    {
        if (!is_null($this->_selected_or_first_available_variant)) {
            return $this->_selected_or_first_available_variant;
        }

        if ($variant_id = $this->selected_variant() ?: $this->variant()->id()) {
            return $this->_selected_or_first_available_variant = new Variant($this->_product->variants->firstWhere('id', $variant_id) ?: $this->_product->variant, $this->_product);
        }

        return $this->_selected_or_first_available_variant = $this->variant();
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        return [
            'active_to' => $this->active_to(),
            'add_to_cart' => $this->add_to_cart(),
            'wishlist_url' => $this->wishlist_url(),
            'banners' => $this->banners(),
            'category' => $this->category(),
            'category_properties' => $this->category_properties(),
            'category_properties_grouped' => $this->category_properties_grouped(),
            'continue_selling' => $this->continue_selling(),
            'date_added' => $this->date_added(),
            'date_modified' => $this->date_modified(),
            'digital' => $this->digital(),
            'discount' => $this->discount(),
            'enable_online_selling' => $this->enable_online_selling(),
            'favorite' => $this->favorite(),
            'featured' => $this->featured(),
            'free_shipping' => $this->free_shipping(),
            'has_image' => $this->has_image(),
            'id' => $this->id(),
            'image' => $this->getImageArray(),
            'first_two_images' => array_map(function ($image): array {
                $formatter = new UrlImageFormat($image);
                return $formatter->imagesFormatArray();
            }, $this->first_two_images()),
            'is_discounted' => $this->is_discounted(),
            'labels' => $this->labels(),
            'max_price_with_discounted' => $this->max_price_with_discounted(),
            'min_price_with_discounted' => $this->min_price_with_discounted(),
            'name' => $this->name(),
            'new' => $this->new(),
            'options' => $this->options(),
            'total_options' => $this->total_options(),
            'overlay' => $this->overlay(),
            'p1' => $this->p1(),
            'p2' => $this->p2(),
            'p3' => $this->p3(),
            'parameter1' => $this->parameter1(),
            'parameter2' => $this->parameter2(),
            'parameter3' => $this->parameter3(),
            'price_from' => $this->price_from(),
            'price_from_discounted' => $this->price_from_discounted(),
            'price_from_discounted_formatted' => $this->price_from_discounted_formatted(),
            'price_from_discounted_input' => $this->price_from_discounted_input(),
            'price_from_discounted_parts' => $this->price_from_discounted_parts(),
            'price_from_formatted' => $this->price_from_formatted(),
            'price_from_input' => $this->price_from_input(),
            'price_from_parts' => $this->price_from_parts(),
            'price_percent' => $this->price_percent(),
            'price_saved' => $this->price_saved(),
            'price_saved_formatted' => $this->price_saved_formatted(),
            'price_saved_input' => $this->price_saved_input(),
            'price_saved_parts' => $this->price_saved_parts(),
            'price_to' => $this->price_to(),
            'price_to_formatted' => $this->price_to_formatted(),
            'price_to_input' => $this->price_to_input(),
            'price_to_parts' => $this->price_to_parts(),
            'price_type' => $this->price_type(),
            'product_to_discount_price' => $this->product_to_discount_price(),
            'product_to_discount_price_to' => $this->product_to_discount_price_to(),
            'quantity' => $this->quantity(),
            'sale' => $this->sale(),
            'shipping' => $this->shipping(),
            'short_description' => $this->short_description(),
            'tags' => $this->tags(),
            'threshold' => $this->threshold(),
            'total_variants' => $this->total_variants(),
            'tracking' => $this->tracking(),
            'type' => $this->type(),
            'url' => $this->url(),
            'url_handle' => $this->url_handle(),
            'variant' => $this->variant(),
            'variants' => $this->variants(),
            'vendor' => $this->vendor(),
            'views' => $this->views(),
            'is_hidden' => $this->is_hidden(),
            'diff_price' => $this->diff_price(),
            'has_required_data' => $this->has_required_data(),
            'selected_variant' => $this->selected_variant(),
            'selected_or_first_available_variant' => $this->selected_or_first_available_variant(),
        ];
    }

}
