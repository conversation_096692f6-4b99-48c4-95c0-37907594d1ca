<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Support\Facades\Request as RequestFacade;
use Liquid\Drop;

/**
 * Request Drop
 * 
 * Provides access to the current request information in Liquid templates.
 * This follows Shopify's request object pattern.
 */
class Request extends Drop
{
    /**
     * Get the current page URL
     *
     * @return string
     */
    public function url(): string
    {
        return RequestFacade::url();
    }
    
    /**
     * Get the current page path
     *
     * @return string
     */
    public function path(): string
    {
        return RequestFacade::path();
    }
    
    /**
     * Get the request host
     *
     * @return string
     */
    public function host(): string
    {
        return RequestFacade::getHost();
    }
    
    /**
     * Get the request HTTP method
     *
     * @return string
     */
    public function method(): string
    {
        return RequestFacade::method();
    }
    
    /**
     * Get the locale of the current request
     *
     * @return string
     */
    public function locale(): string
    {
        return app()->getLocale();
    }
    
    /**
     * Check if the current page is the home page
     *
     * @return bool
     */
    public function is_home_page(): bool
    {
        return RequestFacade::path() === '/' || RequestFacade::path() === '';
    }
    
    /**
     * Check if the current page is the cart page
     *
     * @return bool
     */
    public function is_cart(): bool
    {
        return RequestFacade::path() === 'cart';
    }
    
    /**
     * Check if the current page is a product page
     *
     * @return bool
     */
    public function is_product(): bool
    {
        return str_starts_with(RequestFacade::path(), 'products/');
    }
    
    /**
     * Check if the current page is a collection page
     *
     * @return bool
     */
    public function is_collection(): bool
    {
        return str_starts_with(RequestFacade::path(), 'collections/');
    }
    
    /**
     * Check if the current page is a blog page
     *
     * @return bool
     */
    public function is_blog(): bool
    {
        return str_starts_with(RequestFacade::path(), 'blogs/');
    }
    
    /**
     * Check if the current page is a search page
     *
     * @return bool
     */
    public function is_search(): bool
    {
        return RequestFacade::path() === 'search';
    }
    
    /**
     * Check if the current page is an account page
     *
     * @return bool
     */
    public function is_account(): bool
    {
        return str_starts_with(RequestFacade::path(), 'account');
    }
    
    /**
     * Check if the current page is a checkout page
     *
     * @return bool
     */
    public function is_checkout(): bool
    {
        return str_starts_with(RequestFacade::path(), 'checkout');
    }
    
    /**
     * Get all query parameters
     *
     * @return array
     */
    public function query_parameters(): array
    {
        return RequestFacade::query();
    }
    
    /**
     * Get a specific query parameter
     *
     * @param string $key
     * @return mixed
     */
    public function param($key)
    {
        return RequestFacade::query($key);
    }
    
    /**
     * Check if the request is AJAX
     *
     * @return bool
     */
    public function is_ajax(): bool
    {
        return RequestFacade::ajax();
    }
    
    /**
     * Check if the request is for a JSON response
     *
     * @return bool
     */
    public function is_json(): bool
    {
        return RequestFacade::wantsJson() || 
               str_ends_with(RequestFacade::path(), '.json');
    }
    
    /**
     * Get the design mode status
     *
     * @return bool
     */
    public function design_mode(): bool
    {
        return RequestFacade::has('design_mode') && 
               RequestFacade::query('design_mode') === 'true';
    }
} 