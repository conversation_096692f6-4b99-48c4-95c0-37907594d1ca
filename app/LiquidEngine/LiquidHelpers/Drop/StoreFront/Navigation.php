<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 9.4.2019 г.
 * Time: 16:24 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\StoreFront;

use App\Exceptions\Error;
use App\Models\StoreFront\Navigations;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use App\LiquidEngine\Traits\DropImageToArray;

class Navigation extends AbstractDrop
{

    use DropImageToArray;

    protected \App\Models\StoreFront\Navigations $navigation;

    /**
     * Navigation constructor.
     * @param Navigations $navigation
     */
    public function __construct(Navigations $navigation)
    {
        $this->navigation = $navigation;
    }

    /**
     * @return string
     */
    public function name()
    {
        return $this->navigation->name;
    }

    /**
     * @return string
     */
    public function tooltip()
    {
        return $this->navigation->title;
    }

    /**
     * @return array
     */
    public function dropdown()
    {
        $children = [];
        if ($this->navigation->relationLoaded('children')) {
            $children = $this->navigation->children->map(function ($nav): static {
                return new static($nav);
            })->all();
        }

        return $children;
    }

    /**
     * @return null|string
     */
    public function addon(): ?string
    {
        if ($this->navigation->addon) {
            return sprintf('<i class="%s"></i>', $this->navigation->addon);
        }

        return null;
    }

    /**
     * @return null|string
     */
    public function suffix(): ?string
    {
        if ($this->navigation->suffix) {
            return sprintf('<i class="%s"></i>', $this->navigation->suffix);
        }

        return null;
    }

    /**
     * @return bool
     */
    public function active()
    {
        return $this->navigation->active;
    }

    /**
     * @return string
     */
    public function href()
    {
        return $this->navigation->link_formatted;
    }

    /**
     * @return boolean
     */
    public function divider(): bool
    {
        return (bool)$this->navigation->divider;
    }

    public function image()
    {
        $formatter = new UrlImageFormat($this->navigation);
        return $formatter->getLiquidImage();
    }

    /**
     * @return bool
     */
    public function has_image(): bool
    {
        return !!$this->navigation->hasImage();
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        return [
            'name' => $this->name(),
            'tooltip' => $this->tooltip(),
            'addon' => $this->addon(),
            'suffix' => $this->suffix(),
            'href' => $this->href(),
            'active' => $this->active(),
            'dropdown' => $this->dropdown(),
            'divider' => $this->divider(),
            'has_image' => $this->has_image(),
            'image' => $this->getImageArray(),
        ];
    }

}
