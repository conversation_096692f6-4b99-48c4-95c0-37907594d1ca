<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop;

use Illuminate\Contracts\Support\Arrayable;
use JsonSerializable;
use Liquid\Drop;

abstract class AbstractDrop extends Drop implements Arrayable, JsonSerializable
{

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    abstract public function toArray();

    /**
     * @return array
     */
    public function jsonSerialize()
    {
        return $this->toArray();
    }

}
