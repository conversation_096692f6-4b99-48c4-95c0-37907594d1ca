<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.4.2019 г.
 * Time: 12:47 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Product;

use App\Models\Discount\Discount;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;

class Label extends AbstractDrop
{

    protected \App\Models\Discount\Discount $_label;

    /**
     * Product constructor.
     * @param Discount $discount
     */
    public function __construct(Discount $discount)
    {
        $this->_label = $discount;
    }

    public function id()
    {
        return $this->_label->id;
    }

    public function name()
    {
        return $this->_label->name;
    }

    public function color()
    {
        return $this->_label->color;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'color' => $this->color(),
        ];
    }

}
