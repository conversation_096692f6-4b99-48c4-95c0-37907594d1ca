<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.4.2019 г.
 * Time: 12:02 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType;

use App\Models\Product\Category as CategoryModel;
use App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Category;

class Categories extends AbstractFilter
{

    protected $_categories;

    /**
     * {@inheritDoc}
     */
    protected function items(): array
    {
        if (!is_null($this->_categories)) {
            return $this->_categories;
        }

        return $this->_categories = CategoryModel::whereParentId(null)->with(['childrenRecursive'])->get()->map(function (CategoryModel $category): \App\LiquidEngine\LiquidHelpers\Drop\Filters\ByType\Helper\Category {
            return new Category($category);
        })->all();
    }

}
