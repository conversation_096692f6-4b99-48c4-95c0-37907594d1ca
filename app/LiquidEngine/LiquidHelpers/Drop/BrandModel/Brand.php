<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Drop\BrandModel;

use App\Exceptions\Error;
use App\LiquidEngine\LiquidHelpers\Format\LiquidImageAttribute;
use Modules\BrandModel\Models\Brand as BrandModel;
use App\LiquidEngine\LiquidHelpers\Drop\AbstractDrop;
use App\LiquidEngine\LiquidHelpers\Format\UrlImageFormat;
use Modules\BrandModel\Models\Model as BrandModelModel;
use App\LiquidEngine\Traits\DropImageToArray;

class Brand extends AbstractDrop
{

    use DropImageToArray;

    protected \Modules\BrandModel\Models\Brand $_brand;

    /**
     * Product constructor.
     * @param BrandModel $brand
     */
    public function __construct(BrandModel $brand)
    {
        $this->_brand = $brand;
    }

    public function id()
    {
        return $this->_brand->id;
    }

    public function name()
    {
        return $this->_brand->title;
    }

    public function url_handle()
    {
        return $this->_brand->url_handle;
    }

    public function url()
    {
        return $this->_brand->url;
    }

    /**
     * @return LiquidImageAttribute
     */
    public function image()
    {
        $formatter = new UrlImageFormat($this->_brand);
        return $formatter->getLiquidImage();
    }

    /**
     * @return bool
     */
    public function has_image(): bool
    {
        return !!$this->_brand->hasImage();
    }

    public function models()
    {
        $models = [];
        if ($this->_brand->relationLoaded('models')) {
            $models = $this->_brand->models->map(function (BrandModelModel $model): \App\LiquidEngine\LiquidHelpers\Drop\BrandModel\Model {
                $model->setRelation('brand', (clone $this->_brand)->clearRelations());
                return new Model($model);
            })->values()->all();
        }

        return $models;
    }

    /**
     * Get the collection of items as a plain array.
     *
     * @return array
     * @throws Error
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'url_handle' => $this->url_handle(),
            'url' => $this->url(),
            'has_image' => $this->has_image(),
            'image' => $this->getImageArray(),
            'models' => $this->models(),
        ];
    }

}
