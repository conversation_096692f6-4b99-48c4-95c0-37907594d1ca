<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 5.4.2019 г.
 * Time: 10:50 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Tags;

use Illuminate\Contracts\View\Factory;
use Illuminate\View\View;
use Liquid\AbstractTag;
use Liquid\Context;
use Liquid\Exceptions\SyntaxError;
use Liquid\LiquidCompiler;
use Liquid\Regexp;
use Liquid\LiquidException;
use App\Models\Router\Exceptions;
use App\Models\Router\Logs;

//{% function "itemClass" %}
//{% if per_rows == 4 %}
//asasasasas4444
//{% elsif per_rows == 2 %}
//    {% call 'itemClass' per_rows:4 %}
//{% endif %}
//{% endfunction %}
//
//{% call 'itemClass' per_rows:2 %}

class TagCall extends AbstractTag
{
    /**
     * @var string The name of the template
     */
    private ?string $functionName = null;

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     * @param LiquidCompiler $compiler
     * @throws SyntaxError
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $regex = new Regexp('/^("[^"\\\\]*(?:\\\\.[^"\\\\]*)*"|\'[^\'\\\\]*(?:\\\\.[^\'\\\\]*)*\')(?:\s+(' . $compiler::QUOTED_FRAGMENT . '+))?$/');

        if ($regex->match($markup)) {
            $this->functionName = trim(trim($regex->matches[1], '"'), "'");

            $this->extractAttributes($markup);
        } else {
            throw new SyntaxError("Error in tag 'section' - Valid syntax: call '[functionName]' [attribute:attribute_value ... \n]", -1, $compiler);
        }

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Renders the node
     *
     * @param Context $context
     *
     * @return string|Factory|View
     * @throws LiquidException
     */
    public function render(Context $context): string
    {
        try {
            // Validate function name
            if (empty($this->functionName)) {
                throw new LiquidException("Function name is required for call tag");
            }

            // Check if function exists
            $functionName = sprintf('functionBodyContent%s', $this->functionName);
            $document = $context->registers['functions'][$functionName] ?? null;

            if (empty($document)) {
                $errorMsg = "Function '{$this->functionName}' not found";
                $this->logError($errorMsg, $context);

                if ($context->get('strict_variables', false)) {
                    throw new LiquidException($errorMsg);
                }
                return '';
            }

            if (!($document instanceof TagFunction)) {
                $errorMsg = "Invalid function type for '{$this->functionName}'";
                $this->logError($errorMsg, $context);

                if ($context->get('strict_variables', false)) {
                    throw new LiquidException($errorMsg);
                }
                return '';
            }

            $context->push();

            // Validate and set attributes
            foreach ($this->attributes as $key => $value) {
                try {
                    $resolvedValue = $context->get($value);
                    $context->set($key, $resolvedValue);
                } catch (\Exception $e) {
                    $this->logError("Error setting attribute '{$key}': " . $e->getMessage(), $context);
                    // Continue with null value
                    $context->set($key, null);
                }
            }

            $result = $document->renderFunction($context);
            $context->pop();

            return trim($result);

        } catch (LiquidException $e) {
            throw $e; // Re-throw Liquid exceptions
        } catch (\Exception $e) {
            $errorMsg = "Error rendering function '{$this->functionName}': " . $e->getMessage();
            $this->logError($errorMsg, $context);

            if ($context->get('strict_variables', false)) {
                throw new LiquidException($errorMsg);
            }
            return '';
        }
    }

    /**
     * Log error using CloudCart's logging system
     *
     * @param string $message
     * @param Context $context
     * @return void
     */
    protected function logError(string $message, Context $context): void
    {
        try {
            Logs::create([
                'type' => 'liquid_tag_call_error',
                'message' => $message,
                'context' => [
                    'function_name' => $this->functionName,
                    'attributes' => $this->attributes,
                    'site_id' => $context->get('site_id'),
                ]
            ]);
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }
}
