<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 5.4.2019 г.
 * Time: 10:50 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Tags;

use App\Models\Store\Cart;
use Liquid\AbstractTag;
use Liquid\Context;
use Liquid\Exceptions\SyntaxError;
use Liquid\LiquidCompiler;
use Liquid\Regexp;
use Liquid\LiquidException;
use Illuminate\Support\Facades\Route;
use App\Models\Router\Exceptions;
use App\Models\Router\Logs;

//use Route;

class TagRoute extends AbstractTag
{
    /**
     * @var string The name of the template
     */
    private ?string $routeName = null;

    protected $_allowed = [
        //rss
        'rss' => 'site.feed',
        //product links
        'product.search' => 'search',
        'product.compare' => 'compare',
        //customer links
        'customer.logout' => 'site.auth.logout',
        'customer.login' => 'site.auth.login',
        'customer.register' => 'site.auth.register',
        'customer.forgotten' => 'site.auth.forgotten',
        'customer.verification.resend' => 'site.auth.confirm.email.send',
        'customer.account' => 'site.account',
        'customer.password' => 'site.account.password',
        'customer.address.shipping.create' => 'site.account.address.shipping.create',
        'customer.address.billing.create' => 'site.account.address.billing.create',
        //cart
        'wishlist' => 'wishlist',
        //cart
        'cart' => 'cart.site',
        'cart.add' => 'cart.add',
        'cart.update' => 'cart.update-bulk',
        //checkout
        'checkout' => 'checkout',
        'discount_code.add' => 'checkout.discount.code',
        'discount_code.remove' => 'checkout.discount.code.remove',
        //other
        'contacts' => 'contacts',
    ];

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     *
     * @param $token
     * @param LiquidCompiler $compiler
     * @throws SyntaxError
     */
    public function __construct($markup, array &$tokens, ?\Liquid\LiquidCompiler $compiler)
    {
        $regex = new Regexp('/^("[^"\\\\]*(?:\\\\.[^"\\\\]*)*"|\'[^\'\\\\]*(?:\\\\.[^\'\\\\]*)*\')(?:\s+(' . $compiler::QUOTED_FRAGMENT . '+))?$/');

        if ($regex->match($markup)) {
            $this->routeName = substr($regex->matches[1], 1, strlen($regex->matches[1]) - 2);

            $this->extractAttributes($markup);
        } else {
            throw new SyntaxError("Error in tag 'route' - Valid syntax: route '[route name]' [replace:replace_with ... \n]", -1, $compiler);
        }

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Renders the node
     *
     * @param Context $context
     * @return mixed|string
     * @throws LiquidException
     */
    public function render(Context $context)
    {
        try {
            // Validate route name
            if (empty($this->routeName)) {
                throw new LiquidException("Route name is required for route tag");
            }

            // Check if route is allowed
            if (!array_key_exists($this->routeName, $this->_allowed)) {
                $errorMsg = "Route '{$this->routeName}' is not allowed";
                $this->logError($errorMsg, $context);

                if ($context->get('strict_variables', false)) {
                    throw new LiquidException($errorMsg);
                }
                return '#';
            }

            $routeName = $this->_allowed[$this->routeName];

            // Check if Laravel route exists
            if (!Route::has($routeName)) {
                $errorMsg = "Laravel route '{$routeName}' does not exist for liquid route '{$this->routeName}'";
                $this->logError($errorMsg, $context);

                if ($context->get('strict_variables', false)) {
                    throw new LiquidException($errorMsg);
                }
                return '#';
            }

            $context->push();

            // Validate and set attributes
            foreach ($this->attributes as $key => $value) {
                try {
                    $resolvedValue = $context->get($value);
                    $context->set($key, $resolvedValue);
                } catch (\Exception $e) {
                    $this->logError("Error setting route attribute '{$key}': " . $e->getMessage(), $context);
                    // Continue with null value
                    $context->set($key, null);
                }
            }

            $attributes = null;
            if ($this->attributes) {
                $attributes = array_map(function ($attribute) use ($context) {
                    return $context->get($attribute);
                }, $this->attributes);
            }

            $context->pop();

            // Generate route URL
            try {
                if ($attributes) {
                    return call_user_func('route', $routeName, $attributes);
                } else {
                    if ($routeName == 'cart.site' && app_namespace() == 'site') {
                        return route('cart.list', ['cart_key' => Cart::getNewCartKey()]);
                    } else {
                        return route($routeName);
                    }
                }
            } catch (\Exception $e) {
                $errorMsg = "Error generating route URL for '{$this->routeName}': " . $e->getMessage();
                $this->logError($errorMsg, $context);

                if ($context->get('strict_variables', false)) {
                    throw new LiquidException($errorMsg);
                }
                return '#';
            }

        } catch (LiquidException $e) {
            throw $e; // Re-throw Liquid exceptions
        } catch (\Exception $e) {
            $errorMsg = "Unexpected error in route tag '{$this->routeName}': " . $e->getMessage();
            $this->logError($errorMsg, $context);

            if ($context->get('strict_variables', false)) {
                throw new LiquidException($errorMsg);
            }
            return '#';
        }
    }

    /**
     * Log error using CloudCart's logging system
     *
     * @param string $message
     * @param Context $context
     * @return void
     */
    protected function logError(string $message, Context $context): void
    {
        try {
            Logs::create([
                'type' => 'liquid_tag_route_error',
                'message' => $message,
                'context' => [
                    'route_name' => $this->routeName,
                    'attributes' => $this->attributes,
                    'site_id' => $context->get('site_id'),
                ]
            ]);
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }
}
