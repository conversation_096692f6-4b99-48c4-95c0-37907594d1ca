<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 5.4.2019 г.
 * Time: 10:50 ч.
 */

namespace App\LiquidEngine\LiquidHelpers\Tags;

use Illuminate\Support\Str;
use Liquid\AbstractTag;
use Liquid\Context;
use Liquid\Exceptions\SyntaxError;
use Liquid\LiquidCompiler;
use Liquid\Regexp;
use Liquid\LiquidException;
use App\Models\Router\Exceptions;
use App\Models\Router\Logs;

class TagGoogleReCaptcha extends AbstractTag
{
    /**
     * @var string The name of the template
     */
    private ?string $action = null;

    private bool $hidden = false;

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     *
     * @param $token
     * @param LiquidCompiler $compiler
     * @throws SyntaxError
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $regex = new Regexp('/^("[^"\\\\]*(?:\\\\.[^"\\\\]*)*"|\'[^\'\\\\]*(?:\\\\.[^\'\\\\]*)*\')(?:\s+(' . LiquidCompiler::QUOTED_FRAGMENT . '+))?$/');

        if ($regex->match($markup)) {
            $this->action = substr($regex->matches[1], 1, strlen($regex->matches[1]) - 2);

            if (!empty($regex->matches[3])) {
                $this->hidden = $regex->matches[3] == '"hidden"';
            }

        } else {
            throw new SyntaxError("Error in tag 'googleReCaptcha' - Valid syntax: googleReCaptcha 'init|field' [\"hidden\"]", -1, $compiler);
        }

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Renders the node
     *
     * @param Context $context
     * @return mixed|string
     */
    public function render(Context $context)
    {
        try {
            // Validate action
            if (empty($this->action)) {
                throw new LiquidException("Action is required for Google ReCaptcha tag");
            }

            // Validate action is allowed
            $allowedActions = ['init', 'field'];
            if (!in_array($this->action, $allowedActions)) {
                $errorMsg = "Invalid action '{$this->action}' for Google ReCaptcha tag. Allowed: " . implode(', ', $allowedActions);
                $this->logError($errorMsg, $context);

                if ($context->get('strict_variables', false)) {
                    throw new LiquidException($errorMsg);
                }
                return '';
            }

            // Set language configuration
            try {
                config()->set('googlerecaptchav3.language', setting('language'));
            } catch (\Exception $e) {
                $this->logWarning("Could not set ReCaptcha language: " . $e->getMessage(), $context);
            }

            // Check if method exists and call it
            $method = sprintf('_render' . Str::studly($this->action));
            if (method_exists($this, $method)) {
                return $this->$method($context);
            } else {
                $errorMsg = "Method '{$method}' not found for action '{$this->action}'";
                $this->logError($errorMsg, $context);

                if ($context->get('strict_variables', false)) {
                    throw new LiquidException($errorMsg);
                }
                return '';
            }

        } catch (LiquidException $e) {
            throw $e; // Re-throw Liquid exceptions
        } catch (\Exception $e) {
            $errorMsg = "Unexpected error in Google ReCaptcha tag: " . $e->getMessage();
            $this->logError($errorMsg, $context);

            if ($context->get('strict_variables', false)) {
                throw new LiquidException($errorMsg);
            }
            return '';
        }
    }

    /**
     * Render ReCaptcha field
     *
     * @param Context $context
     * @return string
     */
    protected function _renderField(Context $context): string
    {
        try {
            $style = '';
            if ($this->hidden) {
                $style = 'display: none;';
            }

            // Check if GoogleReCaptchaV3 facade exists
            if (!class_exists('\GoogleReCaptchaV3')) {
                $errorMsg = "GoogleReCaptchaV3 facade not available";
                $this->logError($errorMsg, $context);
                return '';
            }

            $field = \GoogleReCaptchaV3::renderField('contact_us_id', 'contact_us', 'js-error-field-g-recaptcha-response', $style);

            if ($field && method_exists($field, 'render')) {
                return $field->render();
            }

            return '';

        } catch (\Exception $e) {
            $errorMsg = "Error rendering ReCaptcha field: " . $e->getMessage();
            $this->logError($errorMsg, $context);
            return '';
        }
    }

    /**
     * Render ReCaptcha init script
     *
     * @param Context $context
     * @return string
     */
    protected function _renderInit(Context $context): string
    {
        try {
            // Check if GoogleReCaptchaV3 facade exists
            if (!class_exists('\GoogleReCaptchaV3')) {
                $errorMsg = "GoogleReCaptchaV3 facade not available";
                $this->logError($errorMsg, $context);
                return '';
            }

            $view = \GoogleReCaptchaV3::init();

            if ($view && method_exists($view, 'render')) {
                return $view->render();
            }

            return '';

        } catch (\Exception $e) {
            $errorMsg = "Error rendering ReCaptcha init: " . $e->getMessage();
            $this->logError($errorMsg, $context);
            return '';
        }
    }

    /**
     * Log error using CloudCart's logging system
     *
     * @param string $message
     * @param Context $context
     * @return void
     */
    protected function logError(string $message, Context $context): void
    {
        try {
            Logs::create([
                'type' => 'liquid_tag_recaptcha_error',
                'message' => $message,
                'context' => [
                    'action' => $this->action,
                    'hidden' => $this->hidden,
                    'site_id' => $context->get('site_id'),
                ]
            ]);
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }

    /**
     * Log warning using CloudCart's logging system
     *
     * @param string $message
     * @param Context $context
     * @return void
     */
    protected function logWarning(string $message, Context $context): void
    {
        try {
            Logs::create([
                'type' => 'liquid_tag_recaptcha_warning',
                'message' => $message,
                'context' => [
                    'action' => $this->action,
                    'hidden' => $this->hidden,
                    'site_id' => $context->get('site_id'),
                ]
            ]);
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }
}
