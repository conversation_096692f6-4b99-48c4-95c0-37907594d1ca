<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Tags;

use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\Document;
use Liquid\Exceptions\SyntaxError;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;
use Modules\Apps\Others\Gdpr\Models\Policy as PolicyModel;
use App\LiquidEngine\LiquidHelpers\Drop\GDPR\Policy as PolicyDrop;
use App\Models\Router\Exceptions;
use App\Models\Router\Logs;

class TagGdpr extends AbstractBlock
{
    /**
     * @var string The name of the template
     */
    private $form;

    /**
     * @var string
     */
    private $as = 'gdpr';

    /**
     * @var Document The Document that represents the included template
     */
    private $document;

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     * @param LiquidCompiler $compiler
     * @throws SyntaxError
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        $regex = new Regexp('/^[\'\"]([a-zA-Z_][\w]*)[\'\"]?(?:\s+as\s+[\'\"]([a-zA-Z_][\w]*)[\'\"])?$/');

        if ($regex->match($markup)) {
            $this->form = $regex->matches[1];

            if (!empty($regex->matches[3])) {
                $this->as = $regex->matches[3];
            }
        } else {
            throw new SyntaxError("Error in tag 'section' - Valid syntax: gdpr '[form]' [as 'variable']", -1, $compiler);
        }

        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Renders the block
     *
     * @param Context $context
     *
     * @return string
     * @throws LiquidException
     */
    public function render(Context $context): string
    {
        try {
            // Validate form name
            if (empty($this->form)) {
                throw new LiquidException("Form name is required for GDPR tag");
            }

            $policies = collect();

            // Check if GDPR app is enabled
            if (!\Apps::enabled('gdpr')) {
                $this->logWarning("GDPR app is not enabled", $context);
                return '';
            }

            // Get GDPR policies for the form
            try {
                $policies = \GDPR::getFormPolicies($this->form);
            } catch (\Exception $e) {
                $errorMsg = "Error getting GDPR policies for form '{$this->form}': " . $e->getMessage();
                $this->logError($errorMsg, $context);

                if ($context->get('strict_variables', false)) {
                    throw new LiquidException($errorMsg);
                }
                return '';
            }

            if ($policies->isEmpty()) {
                $this->logInfo("No GDPR policies found for form '{$this->form}'", $context);
                return '';
            }

            // Convert policies to drops
            try {
                $policies = $policies->map(function (PolicyModel $policy): \App\LiquidEngine\LiquidHelpers\Drop\GDPR\Policy {
                    return new PolicyDrop($policy);
                })->all();
            } catch (\Exception $e) {
                $errorMsg = "Error converting GDPR policies to drops: " . $e->getMessage();
                $this->logError($errorMsg, $context);

                if ($context->get('strict_variables', false)) {
                    throw new LiquidException($errorMsg);
                }
                return '';
            }

            $context->push();

            // Validate variable name
            if (empty($this->as)) {
                $this->as = 'gdpr';
            }

            $context->set($this->as, $policies);

            $output = parent::render($context);

            $context->pop();

            return $output;

        } catch (LiquidException $e) {
            throw $e; // Re-throw Liquid exceptions
        } catch (\Exception $e) {
            $errorMsg = "Unexpected error in GDPR tag for form '{$this->form}': " . $e->getMessage();
            $this->logError($errorMsg, $context);

            if ($context->get('strict_variables', false)) {
                throw new LiquidException($errorMsg);
            }
            return '';
        }
    }

    /**
     * Log error using CloudCart's logging system
     *
     * @param string $message
     * @param Context $context
     * @return void
     */
    protected function logError(string $message, Context $context): void
    {
        try {
            Logs::create([
                'type' => 'liquid_tag_gdpr_error',
                'message' => $message,
                'context' => [
                    'form' => $this->form,
                    'as' => $this->as,
                    'site_id' => $context->get('site_id'),
                ]
            ]);
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }

    /**
     * Log warning using CloudCart's logging system
     *
     * @param string $message
     * @param Context $context
     * @return void
     */
    protected function logWarning(string $message, Context $context): void
    {
        try {
            Logs::create([
                'type' => 'liquid_tag_gdpr_warning',
                'message' => $message,
                'context' => [
                    'form' => $this->form,
                    'as' => $this->as,
                    'site_id' => $context->get('site_id'),
                ]
            ]);
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }

    /**
     * Log info using CloudCart's logging system
     *
     * @param string $message
     * @param Context $context
     * @return void
     */
    protected function logInfo(string $message, Context $context): void
    {
        try {
            Logs::create([
                'type' => 'liquid_tag_gdpr_info',
                'message' => $message,
                'context' => [
                    'form' => $this->form,
                    'as' => $this->as,
                    'site_id' => $context->get('site_id'),
                ]
            ]);
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }

}
