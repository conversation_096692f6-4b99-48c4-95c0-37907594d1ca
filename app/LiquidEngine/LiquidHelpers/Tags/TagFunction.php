<?php

declare(strict_types=1);

namespace App\LiquidEngine\LiquidHelpers\Tags;

use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\Exceptions\SyntaxError;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;
use App\Models\Router\Exceptions;
use App\Models\Router\Logs;

/**
 * Marks a section of a template as being reusable.
 *
 * Example:
 *
 *     {% function foo %} bar {% endfunction %}
 */
class TagFunction extends AbstractBlock
{

    protected static $_functions = [];

    /**
     * The variable to assign to
     *
     * @var string
     */
    private $block;

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     * @param $token
     * @param LiquidCompiler $compiler
     * @throws SyntaxError
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        try {
            // Validate markup is not empty
            if (empty(trim($markup))) {
                throw new SyntaxError("Function name is required - Valid syntax: function [name]", -1, $compiler);
            }

            $syntaxRegexp = new Regexp('/^[a-zA-Z_][a-zA-Z0-9_]*$/');

            if ($syntaxRegexp->match(trim($markup))) {
                $this->block = $syntaxRegexp->matches[1];

                // Validate function name length
                if (strlen($this->block) > 50) {
                    throw new SyntaxError("Function name too long (max 50 characters) - Valid syntax: function [name]", -1, $compiler);
                }

                parent::__construct($markup, $tokens, $compiler);
            } else {
                throw new SyntaxError("Invalid function name '{$markup}' - Valid syntax: function [name] (alphanumeric and underscore only)", -1, $compiler);
            }
        } catch (SyntaxError $e) {
            throw $e; // Re-throw syntax errors
        } catch (\Exception $e) {
            throw new SyntaxError("Error in function tag: " . $e->getMessage(), -1, $compiler);
        }
    }

    /**
     * Renders the node
     *
     * @param Context $context
     *
     * @return string
     */
    public function render(Context $context): string
    {
        try {
            // Validate function name
            if (empty($this->block)) {
                $this->logError("Function name is empty", $context);
                return '';
            }

            $functionKey = sprintf('functionBodyContent%s', $this->block);

            // Check if function already exists (warn about override)
            if (isset($context->registers['functions'][$functionKey])) {
                $this->logWarning("Function '{$this->block}' is being redefined", $context);
            }

            $context->registers['functions'][$functionKey] = $this;

            return '';

        } catch (\Exception $e) {
            $errorMsg = "Error registering function '{$this->block}': " . $e->getMessage();
            $this->logError($errorMsg, $context);

            if ($context->get('strict_variables', false)) {
                throw new LiquidException($errorMsg);
            }
            return '';
        }
    }

    /**
     * Renders the function content
     *
     * @param Context $context
     * @return string
     */
    public function renderFunction(Context $context): string
    {
        try {
            // Validate nodelist exists
            if (empty($this->nodelist)) {
                $this->logWarning("Function '{$this->block}' has no content", $context);
                return '';
            }

            return $this->renderAll($this->nodelist, $context);

        } catch (\Exception $e) {
            $errorMsg = "Error rendering function '{$this->block}': " . $e->getMessage();
            $this->logError($errorMsg, $context);

            if ($context->get('strict_variables', false)) {
                throw new LiquidException($errorMsg);
            }
            return '';
        }
    }

    /**
     * Log error using CloudCart's logging system
     *
     * @param string $message
     * @param Context $context
     * @return void
     */
    protected function logError(string $message, Context $context): void
    {
        try {
            Logs::create([
                'type' => 'liquid_tag_function_error',
                'message' => $message,
                'context' => [
                    'function_name' => $this->block,
                    'site_id' => $context->get('site_id'),
                ]
            ]);
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }

    /**
     * Log warning using CloudCart's logging system
     *
     * @param string $message
     * @param Context $context
     * @return void
     */
    protected function logWarning(string $message, Context $context): void
    {
        try {
            Logs::create([
                'type' => 'liquid_tag_function_warning',
                'message' => $message,
                'context' => [
                    'function_name' => $this->block,
                    'site_id' => $context->get('site_id'),
                ]
            ]);
        } catch (\Exception $e) {
            // Silently fail if logging fails
        }
    }

    /**
     * Returns the string that delimits the end of the block
     *
     * @return string
     */
    protected function blockDelimiter(): string
    {
        return "endfunction";
    }
}
