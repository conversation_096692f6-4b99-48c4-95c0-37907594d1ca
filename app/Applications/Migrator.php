<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 16.1.2017 г.
 * Time: 16:37 ч.
 */

namespace App\Applications;

use App\Commands\Db\Migrate\DatabaseMigrationRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Database\Migrations\Migrator as BaseMigrator;

class Migrator
{
    protected $migrator;

    /**
     * Run the outstanding migrations at a given path.
     *
     * @param  array|string  $paths
     * @param  array  $options
     * @return array
     */
    public function run($paths = [], array $options = [])
    {
        $repository = $this->getMigrator()->getRepository();

        $files = $this->getMigrator()->getMigrationFiles($paths);

        // Once we grab all of the migration files for the path, we will compare them
        // against the migrations that have already been run for this package then
        // run each of the outstanding migrations against a database connection.
        $ran = $repository->getRan();

        $migrations = Collection::make($files)
            ->reject(fn ($file): bool => in_array($this->getMigrator()->getMigrationName($file), $ran))->values()->all();

        $this->getMigrator()->requireFiles($migrations);

        $this->runMigrationList($migrations, $options);

        return $migrations;
    }

    /**
     * Rollback the last migration operation.
     *
     * @param  array|string $paths
     * @param  array  $options
     * @return array
     */
    public function rollback($paths = [], array $options = []): array
    {
        $rolledBack = [];

        $repository = $this->getMigrator()->getRepository();

        $files = $this->getMigrator()->getMigrationFiles($paths);

        $migrations = $repository->getByKeys(array_keys($files));

        $count = $migrations->count();

        if ($count > 0) {
            // Next we will run through all of the migrations and call the "down" method
            // which will reverse each migration in order. This getLast method on the
            // repository already returns these migration's names in reverse order.
            $this->getMigrator()->requireFiles($files);

            foreach ($migrations as $migration) {
                $rolledBack[] = $files[$migration->migration];

                $this->runDown($files[$migration->migration], $migration);
            }
        }

        return $rolledBack;
    }

    /**
     * Run "down" a migration instance.
     *
     * @param  string  $file
     * @param  object  $migration
     * @return void
     */
    protected function runDown($file, $migration)
    {
        $file = $this->getMigrator()->getMigrationName($file);

        // First we will get the file name of the migration so we can resolve out an
        // instance of the migration. Once we get an instance we can either run a
        // pretend execution of the migration or we can run the real migration.
        $instance = $this->getMigrator()->resolve($file);

        $instance->down();

        // Once we have successfully run the migration "down" we will remove it from
        // the migration repository so it will be considered to have not been run
        // by the application then will be able to fire by any later operation.
        $this->getMigrator()->getRepository()->delete($migration);
    }

    /**
     * Run an array of migrations.
     *
     * @param  array  $migrations
     * @param  array  $options
     * @return void
     */
    protected function runMigrationList($migrations, array $options = [])
    {
        // First we will just make sure that there are any migrations to run. If there
        // aren't, we will just make a note of it to the developer so they're aware
        // that all of the migrations have been run against this database system.
        if (count($migrations) == 0) {
            return;
        }

        $batch = $this->getMigrator()->getRepository()->getNextBatchNumber();

        $step = Arr::get($options, 'step', false);

        // Once we have the array of migrations, we will spin through them and run the
        // migrations "up" so the changes are made to the databases. We'll then log
        // that the migration was run so we don't repeat it next time we execute.
        foreach ($migrations as $file) {
            $this->runUp($file, $batch);

            // If we are stepping through the migrations, then we will increment the
            // batch value for each individual migration that is run. That way we
            // can run "artisan migrate:rollback" and undo them one at a time.
            if ($step) {
                $batch++;
            }
        }
    }

    /**
     * Run "up" a migration instance.
     *
     * @param  string  $file
     * @param  int     $batch
     * @return void
     */
    protected function runUp($file, $batch)
    {
        $file = $this->getMigrator()->getMigrationName($file);

        // First we will resolve a "real" instance of the migration class from this
        // migration file name. Once we have the instances we can run the actual
        // command such as "up" or "down", or we can just simulate the action.
        $migration = $this->getMigrator()->resolve($file);

        $migration->up();

        // Once we have run a migrations class, we will log that it was run in this
        // repository so that we don't try to run it next time we do a migration
        // in the application. A migration repository keeps the migrate order.
        $this->getMigrator()->getRepository()->log($file, $batch);
    }

    /**
     * @return BaseMigrator
     */
    protected function getMigrator()
    {
        if ($this->migrator) {
            return $this->migrator;
        }

        $connectionResolver = Model::getConnectionResolver();
        $repository = new DatabaseMigrationRepository($connectionResolver, config('database.migrations'));
        return $this->migrator = new BaseMigrator($repository, $connectionResolver, app('files'));
    }

}
