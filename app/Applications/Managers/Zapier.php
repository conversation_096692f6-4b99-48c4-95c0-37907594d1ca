<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 16.1.2017 г.
 * Time: 12:36 ч.
 */

namespace App\Applications\Managers;

use Modules\Apps\Abstractions\Managers\AbstractAppManager;

class Zapier extends AbstractAppManager
{
    public const APP_KEY = 'zapier';

    public function appInfo(): array
    {
        return [static::APP_KEY => [
            'direct' => 'apps/zapier',
            'uninstall' => 'apps/zapier/uninstall',
            'icon' => 'icon-zapier.png',
            'name' => __('zapier.info.title'),
            'description' => __('zapier.help.install'),
        ]];
    }

}
