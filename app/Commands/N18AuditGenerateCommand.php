<?php

declare(strict_types=1);

namespace App\Commands;

use App\Helper\Gate\XmlAuditGenerate;
use App\Models\Gate\N18Audit;
use Carbon\Carbon;
use Illuminate\Console\Command;

class N18AuditGenerateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'n18:audit
                            {--period= : Audit period (YYYY-MM)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate CloudCart N18 audit';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        date_default_timezone_set('Europe/Sofia');
        $period = $this->option('period') ?: now()->subMonth()->format('Y-m');
        $period = Carbon::createFromFormat('Y-m', $period)->lastOfMonth();

        $generator = new XmlAuditGenerate($period);

        $record = N18Audit::firstOrNew(['period' => $period->utc()]);
        $record->fill([
            'content' => $generator->output(),
            'count' => $generator->getPeriodCount(),
            'amount' => $generator->getPeriodTotal(),
            'amount_vat' => $generator->getPeriodTotalVat(),
            'currency' => 'BGN',
        ]);
        $record->save();

        $this->info('Generated N18 Audit for 1 month to ' . $period->toDateString());
    }
}
