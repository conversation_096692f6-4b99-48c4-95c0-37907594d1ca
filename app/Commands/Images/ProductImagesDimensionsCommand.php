<?php

declare(strict_types=1);

// php artisan image:dimensions product
// php artisan image:dimensions category
// php artisan image:dimensions vendor
// php artisan image:dimensions blog
// php artisan image:dimensions article
// php artisan image:dimensions shipping
// php artisan image:dimensions page

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 8.9.2016 г.
 * Time: 19:32 ч.
 */

namespace App\Commands\Images;

use App\Console\Traits\TargetSites;
use App\Models\Blog\Article;
use App\Models\Blog\Blog;
use App\Models\Page\Page;
use App\Models\Product\Category;
use App\Models\Product\Image;
use App\Models\Product\Vendor;
use App\Models\Shipping\ShippingProvider;
use Eloquent;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ProductImagesDimensionsCommand extends Command
{
    use TargetSites;

    protected $signature = 'image:dimensions {model : Model}
                {--site= : The site ID.}
                {--platform= : Platform.}
                {--include-expired : Include expired sites.}';

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'image:dimensions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate product image dimensions';

    protected $_max_id = [];

    protected $allowed = [
        'category', 'vendor', 'blog', 'article',
        'shipping', 'page', 'product',
    ];

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $models = collect(config('image.map'))->whereIn('type', $this->allowed);
        $config = $models->where('type', $model = $this->argument('model'));
        if ($config->isEmpty()) {
            $this->error(sprintf('Invalid model selected "%s". Allowed types is: %s', $model, $models->implode('type', ', ')));
            return;
        }

        $this->exec(function () use ($config): void {
            /** @var Eloquent $modelName */
            $modelName = $config->keys()->first();
            $model = new $modelName();
            $modelKey = $model->getKeyName();
            $modelTable = $model->getTable();

            $totalImages = $this->getImagesTotal($config);

            $bar = $this->output->createProgressBar($totalImages);

            while (true) {
                $images = $this->getImages($config);
                if ($images->isEmpty()) {
                    $bar->finish();
                    return;
                }

                $images->map(function ($image) use ($modelName, $modelKey, $modelTable, $bar) {
                    if ($info = $this->getImageSize($image->getImage())) {
                        //$image->update($info);
                        if (\Illuminate\Support\Facades\DB::table($modelTable)->where($modelKey, $image->{$modelKey})->update($info)) {
                            $bar->advance();
                            return $image;
                        }
                    }

                    $bar->advance();
                    return;
                })->filter();

                $this->setMaxId($images->max($modelKey));
            }

        });
    }

    /**
     * @return Collection|Image[]|Category[]|Vendor[]|Page[]|Blog[]|Article[]|ShippingProvider[]
     */
    protected function getImages(Collection $config)
    {
        /** @var Eloquent $modelName */
        $modelName = $config->keys()->first();
        $modelKey = (new $modelName())->getKeyName();
        $minSize = $modelName == Image::class ? 150 : 75;

        return $modelName::whereNotNull('max_thumb_size')->where(function ($query) use ($minSize): void {
            /** @var Eloquent $query */
            $query->whereNull('width')
                ->orWhereNull('height')
                ->orWhere('width', '<', $minSize)
                ->orWhere('height', '<', $minSize);
        })->where(function ($query) use ($config): void {
            /** @var Eloquent $query */
            $query->where($config->first()['image_field'], 'like', '%.jpg')
                ->orWhere($config->first()['image_field'], 'like', '%.jpeg')
                ->orWhere($config->first()['image_field'], 'like', '%.png')
                ->orWhere($config->first()['image_field'], 'like', '%.gif');
        })->where($modelKey, '>', $this->getMaxId())->limit(50)->get();
    }

    /**
     * @return int
     */
    protected function getImagesTotal(Collection $config)
    {
        /** @var Eloquent $modelName */
        $modelName = $config->keys()->first();
        $minSize = $modelName == Image::class ? 150 : 75;

        return $modelName::whereNotNull('max_thumb_size')->where(function ($query) use ($minSize): void {
            /** @var Eloquent $query */
            $query->whereNull('width')
                ->orWhereNull('height')
                ->orWhere('width', '<', $minSize)
                ->orWhere('height', '<', $minSize);
        })->where(function ($query) use ($config): void {
            /** @var Eloquent $query */
            $query->where($config->first()['image_field'], 'like', '%.jpg')
                ->orWhere($config->first()['image_field'], 'like', '%.jpeg')
                ->orWhere($config->first()['image_field'], 'like', '%.png')
                ->orWhere($config->first()['image_field'], 'like', '%.gif');
        })->count();
    }

    /**
     * @return int
     */
    protected function getMaxId()
    {
        return $this->_max_id[site('site_id')] ?? 0;
    }

    /**
     * @param $value
     * @return void
     */
    protected function setMaxId($value)
    {
        $this->_max_id[site('site_id')] = $value;
    }

    /**
     * @param $image
     * @return array|null
     */
    protected function getImageSize($image): ?array
    {
        //        $img = new Getimagesize($image);
        //        if(!$sizes = $img->getSize()) {
        //            $sizes = @getimagesize($image);
        //        }
        $sizes = @getimagesize($image);

        return $sizes ? [
            'width' => $sizes[0],
            'height' => $sizes[1],
        ] : null;
    }
}
