<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 8.9.2016 г.
 * Time: 19:32 ч.
 */

namespace App\Commands\Images;

use App\Console\Traits\TargetSites;
use App\Models\Product\Image;
use App\Models\Product\ImageVariant;
use App\Models\Product\Product;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class RemoveDuplicateImageFromProductsCommand extends Command
{
    use TargetSites;

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'image:remove-duplicated';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove duplicated images from products';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->exec(function (): void {
            $left = new Collection();

            $images = Product::withoutGlobalScopes()->whereHas('images', function ($query) use (&$left): void {
                /** @var Image|Builder $query */
                $query->join('storage', function (JoinClause $join): void {
                    $join->on('storage.item_id', 'images__products.id')
                        ->where('item_type', Image::class);
                })->select('images__products.*')->groupBy('storage.size')->having(\Illuminate\Support\Facades\DB::raw('COUNT(`images__products`.`id`)'), '>', 1);
            })->with('images.storage')->orderBy('id', 'desc')->get()->map(function (Product $product) use (&$left) {
                $images = $product->images->groupBy('storage.size')->filter(fn (Collection $collection): bool => $collection->count() > 1);

                $left = $images->map->shift();
                return $images;
            })->collapse()->collapse()->values();

            $total_images = $images->count();
            $total_products = $images->groupBy('parent_id')->count();

            $this->info(sprintf('Begin delete %d images for %d products', $total_images, $total_products));

            $variantsImages = ImageVariant::get()->groupBy('product_image_id');

            $images->map(function (Image $image, $row) use ($variantsImages, $left): void {
                if ($row % 100 == 0 && $row) {
                    $this->info(sprintf('%d images deleted', $row));
                }

                if ($variantsImages->has($image->getKey())) {
                    foreach ($variantsImages->get($image->getKey(), []) as $variantImage) {
                        /**@var ImageVariant $variantImage */
                        if ($leftImage = $left->get($image->storage->size, null)) {
                            $variantImage->update([
                                'product_image_id' => $leftImage->getKey()
                            ]);
                        }
                    }
                }

                $image->delete();
            });

            $this->info(sprintf('%d Total images deleted', $total_images));

            $update = Image::whereColumn('parent_id', 'products.id')->orderBy('sort_order', 'asc')
                ->limit(1)->select('id')->toFullSql();
            $total_updates = Product::whereHas('images')->whereNull('image_id')->update([
                'image_id' => \Illuminate\Support\Facades\DB::raw('(' . $update . ')')
            ]);

            $this->info(sprintf('%d Total products updated primary image', $total_updates));
        });
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handleOld(): void
    {
        $this->exec(function (): void {

            $images = Product::withoutGlobalScopes()->whereHas('images', function ($query): void {
                /** @var Image|Builder $query */
                $query->join('storage', function (JoinClause $join): void {
                    $join->on('storage.item_id', 'images__products.id')
                        ->where('item_type', Image::class);
                })->select('images__products.*')->groupBy('storage.size')->having(\Illuminate\Support\Facades\DB::raw('COUNT(`images__products`.`id`)'), '>', 1);
            })->with('images.storage')->orderBy('id', 'desc')->get()->map(function (Product $product) {
                $images = $product->images->groupBy('storage.size')->filter(fn (Collection $collection): bool => $collection->count() > 1);
                $images->map->shift();
                return $images;
            })->collapse()->collapse()->values();

            $total_images = $images->count();
            $total_products = $images->groupBy('parent_id')->count();

            $this->info(sprintf('Begin delete %d images for %d products', $total_images, $total_products));

            $images->map(function (Image $image, $row): void {
                if ($row % 100 == 0 && $row) {
                    $this->info(sprintf('%d images deleted', $row));
                }

                $image->delete();
            });

            $this->info(sprintf('%d Total images deleted', $total_images));

            $update = Image::whereColumn('parent_id', 'products.id')->orderBy('sort_order', 'asc')
                ->limit(1)->select('id')->toFullSql();
            $total_updates = Product::whereHas('images')->whereNull('image_id')->update([
                'image_id' => \Illuminate\Support\Facades\DB::raw('(' . $update . ')')
            ]);

            $this->info(sprintf('%d Total products updated primary image', $total_updates));
        });
    }
}
