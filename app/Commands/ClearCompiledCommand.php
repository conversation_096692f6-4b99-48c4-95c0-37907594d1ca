<?php

declare(strict_types=1);

namespace App\Commands;

use Illuminate\Console\Command;

class ClearCompiledCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'clear-compiled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove the compiled class file';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $files = array_merge(
            glob(substr((string) $this->laravel->getCachedServicesPath(), 0, -11) . '*.php'),
            glob(substr((string) $this->laravel->getCachedPackagesPath(), 0, -11) . '*.php')
        );

        foreach ($files as $file) {
            @unlink($file);
        }

        $this->info('Compiled services and packages files removed!');
    }
}
