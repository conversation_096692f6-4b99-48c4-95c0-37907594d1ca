<?php

declare(strict_types=1);

namespace App\Commands;

use App\Models\Gate\LtaContract;
use App\Models\Gate\PlanDetails;
use App\Models\Gate\SiteSubscription;
use App\Models\Router\Exceptions;
use Illuminate\Console\Command;

/**
 * Class RenewSubscriptionsCommand
 *
 * @package App\Commands
 */
class RenewSubscriptionsCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'renew:subscriptions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Renew CloudCart Site Subscriptions';

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Throwable
     */
    public function handle(): void
    {
        $this->info('Handling CloudCart recurring payments ...');

        /** @var \Illuminate\Database\Eloquent\Collection|SiteSubscription[] $items */
        $items = SiteSubscription::forRenewal()->get();
        $this->info('Subscriptions for renewal: ' . $items->count());

        foreach ($items as $item) {
            $this->info(sprintf('Renewing Subscription #%s (%s) | Site #%s ...', $item->getKey(), $item->unique_id, $item->site_id));

            if (SiteSubscription::getActualClassNameForMorph($item->model_type) == PlanDetails::class) {
                if (!$item->model->active) {
                    $this->warn(sprintf('Site #%s is using inactive plan, setting past due status ...', $item->site_id));
                    $item->setPastDue();
                    $item->updateSiteStatus();
                    continue;
                }
            }

            try {
                $invoice = $item->renew();
            } catch (\Throwable $e) {
                Exceptions::createFromThrowable($e);
                $invoice = null;
            }

            $this->info('Status: ' . ($invoice ? 'success' : 'failed'));
        }

        $this->ltaContractPayments();
        $this->ltaContractExtend();
        //        $this->reseller();

        $this->info('Done');
    }

    protected function ltaContractPayments()
    {
        $this->info('Handling CloudCart LTA Contracts recurring payments ...');

        /** @var \Illuminate\Database\Eloquent\Collection|LtaContract[] $items */
        $items = LtaContract::forRenewal()->get();
        $this->info('Contracts for renewal: ' . $items->count());

        foreach ($items as $item) {
            $this->info(sprintf('Renewing Contract #%s (%s) | Site #%s ...', $item->getKey(), $item->unique_id, $item->site_id));

            try {
                $item->renew();
                $this->info('Status: success');
            } catch (\Throwable $e) {
                Exceptions::createFromThrowable($e);
                $this->info('Status: failed');
            }
        }

        $this->info('Done');
    }

    protected function ltaContractExtend()
    {
        $this->info('Handling CloudCart LTA Contracts extend (auto_renew) ...');

        /** @var \Illuminate\Database\Eloquent\Collection|LtaContract[] $items */
        $items = LtaContract::forExtend()->get();
        $this->info('Contracts for extend: ' . $items->count());

        foreach ($items as $item) {
            $this->line('Extend Contract #' . $item->getKey() . ' | Ends at: ' . $item->ends_at->toDateString());

            try {
                $item->extend();
                $this->info('Status: success' . ' | Ends at: ' . $item->ends_at->toDateString());
            } catch (\Throwable $e) {
                Exceptions::createFromThrowable($e);
                $this->warn('Status: failed');
            }
        }

        $this->info('Done');
    }

    protected function reseller()
    {
        $this->info('Handling CloudCart recurring payments from Resellers ...');

        /** @var \Illuminate\Database\Eloquent\Collection|SiteSubscription[] $items */
        $items = SiteSubscription::forRenewalReseller()->get();
        $this->info('Reseller Subscriptions for renewal: ' . $items->count());

        foreach ($items as $item) {
            $this->info('Renewing Site Subscription #' . $item->getKey());

            $credit = $item->renewReseller();
            $item->updateSiteStatus();

            $this->info('Status: ' . ($credit ? 'success: reseller credit #' . $credit->getKey() : 'failed'));
        }
    }
}
