<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 14.6.2018 г.
 * Time: 09:42 ч.
 */

namespace App\Commands\Queue;

use App\Integration\Etsy\EtsyManager;
use Modules\Apps\Others\Olx\OlxManager;
use App\Models\Queue\SiteQueue;
use Illuminate\Console\Command;

class QueueInitSingleCommand extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'queue:init-single';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Init queue for single jobs (Global System Jobs)';

    /**
     * Execute the console command.
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        // Run only on one platform
        $allowedPlatform = 'google-cloud';

        SiteQueue::createQueueByMapping('check_products_lists_tables');
        $this->info('Check temp tables list exists');

        if (platform() != $allowedPlatform) {
            $this->error(sprintf('This command is allowed to run only on %s platform!', $allowedPlatform));
            return;
        }

        // TODO: Review all jobs and remove unused/useless
        $jobs = [
            // job mapping => job description
            'hosts_refresh_ns_records' => 'CloudCart Get current ns, ips, whois for all active external domains',
            'site_statistics' => 'CloudCart Generate Site Statistics',
            'plan_turnover_job' => 'CloudCart Plan Turnover',
            'aggregate_segments' => 'CloudCart Aggregate Site Segments',
            'cleanup_segments' => 'CloudCart Cleanup Site Segments',
            'ssl_cloudcart' => "CloudCart Let's Encrypt Renew",
            'ssl_sites' => "Sites Let's Encrypt Renew",
            'ssl_cclink' => "ccl.ink Let's Encrypt Renew",
            'settlement_batch' => 'Braintree settlement batch sync job initialized',
            'currency_sync' => 'Currency synchronisation job initialized',
//            'analytics_sync' => 'Google Analytics synchronisation job initialized',
//            'calls_speech' => 'Calls Speech job initialized',
            'subscription_payments' => 'Subscription payments job initialized',
            'subscription_payments_notify' => 'Subscription payments notification job initialized',
            'expire_subscriptions' => 'Expire subscriptions job initialized',
            'expire_free_sites_notify' => 'Expire Free Sites Notify job initialized',
            'expire_offers' => 'Expire offers job initialized',
            'offer_tasks' => 'Offer Tasks job initialized',
            'borica_way4_status' => 'borica_way4_status job initialized',
//            'handle_primary_domains' => 'Handle Primary Domains job initialized',
            'handle_site_status_and_db' => 'Handle Site Status And Db job initialized',
            'domain_payments' => 'Domain payments job initialized',
            'sync_modoboa' => 'Modoboa sync job initialized',
            'uninstall_un_paid_apps' => 'Uninstall unpaid apps',
            'delete_cart_safe' => 'Force delete soft deleted carts', // run on both platforms: TODO: manually add to hetzner-cloud
            'clear_all_old_carts' => 'Clear old carts', // run on both platforms: TODO: manually add to hetzner-cloud
            'campaign_channel_reputation' => 'Campaigns channels reputations',
            'subscribers_statistics' => 'Subscribers reputations',
//            'subscribers_rfm' => 'Subscribers reputations',  // run on only hetzner-cloud (mongo analytics location): TODO: manually add to hetzner-cloud
        ];

        foreach ($jobs as $mapping => $description) {
            SiteQueue::createQueueByMapping($mapping);
            $this->info($description);
        }

        OlxManager::startSync();
        $this->info('OLX synchronisation job initialized');

        EtsyManager::startSyncCategories();
        $this->info('Etsy categories synchronisation job initialized');
    }
}
