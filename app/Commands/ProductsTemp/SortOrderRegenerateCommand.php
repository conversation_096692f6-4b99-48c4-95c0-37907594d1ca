<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 20.12.2018 г.
 * Time: 16:21 ч.
 */

namespace App\Commands\ProductsTemp;

use App\Console\Traits\TargetSites;
use App\Helper\Temp\ProductTemp;
use App\Models\Product\Product;
use App\Models\Router\Logs;
use Illuminate\Console\Command;
use Schema;
use Throwable;

class SortOrderRegenerateCommand extends Command
{
    use TargetSites;

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'products:tmp-order-regenerate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerate order columns for tmp product';

    /**
     * Execute the console command.
     * @return void
     * @throws Throwable
     */
    public function handle(): void
    {
        $this->exec(function ($site): void {
            $s = microtime(true);
            \Illuminate\Support\Facades\DB::transaction(function (): void {
                $this->executeForSite();
            });

            $message = sprintf('Temp table sort regenerate for site ID: %d for %s sec.', $site->site_id, round(microtime(true) - $s, 3));
            Logs::createFromThrowable(new \Exception($message));

            Product::clearCache();
        });
    }

    protected function executeForSite()
    {
        $groups = ProductTemp::getCustomersGroups();
        if (!$groups) {
            $groups = [null];
        }

        $tables = [];
        if ($groups) {
            foreach ($groups as $group) {
                if ($table = ProductTemp::getTempTable($group)) {
                    $tables[$table] = $table;
                }
            }
        }

        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                //product orders
                $start = microtime(true);

                $sql = \Illuminate\Support\Facades\DB::table($table);
                $maxId = (int)with(clone $sql)->max('id');
                $maxSortOrder = (int)with(clone $sql)->max('sort_order');
                $maxMinPrice = (int)with(clone $sql)->max('min_price_with_discounted');

                \Illuminate\Support\Facades\DB::table($table)->update([
                    'order_min_price_with_discounted_asc_id_asc' => \Illuminate\Support\Facades\DB::raw('CAST(CONCAT(COALESCE(min_price_with_discounted, ' . $maxMinPrice . '),".",' . $maxId . '-id) as DOUBLE)'),
                    'order_min_price_with_discounted_desc_id_asc' => \Illuminate\Support\Facades\DB::raw('CAST(CONCAT(' . $maxMinPrice . ' - COALESCE(min_price_with_discounted, 0),".",' . $maxId . '-id) as DOUBLE)'),
                    'order_is_discounted_desc_min_price_with_discounted_asc' => \Illuminate\Support\Facades\DB::raw('CAST(CONCAT(IF(is_discounted, 10, 11), IF(featured, 10, 11), LPAD( COALESCE(min_price_with_discounted, ' . $maxMinPrice . '), 11, "0"), ".", ' . $maxId . '-id) as DOUBLE)'),
                    'order_featured_desc_min_price_with_discounted_asc' => \Illuminate\Support\Facades\DB::raw('CAST(CONCAT(IF(featured, 10, 11),LPAD( COALESCE(min_price_with_discounted, ' . $maxMinPrice . '), 13, "0"), ".", ' . $maxId . '-id) as DOUBLE)'),
                    'order_sort_order_asc_featured_desc_id_asc' => \Illuminate\Support\Facades\DB::raw('CAST(CONCAT(CAST(COALESCE(sort_order, ' . ($maxSortOrder + 1) . ') as INTEGER), IF(featured, 10, 11), ".", ' . $maxId . '-id)  as DOUBLE)'),
                    'order_sort_order_desc_featured_desc_id_asc' => \Illuminate\Support\Facades\DB::raw('CAST(CONCAT(CAST(' . $maxSortOrder . '-COALESCE(sort_order, -1) as INTEGER), IF(featured, 10, 11), ".", ' . $maxId . '-id)  as DOUBLE)'),
                ]);

                $this->info(sprintf('Update product orders for: %s - %s', $table, microtime(true) - $start));
            } else {
                $this->warn(sprintf('There is no table "%s"', $table));
            }
        }
    }

}
