<?php

declare(strict_types=1);

namespace App\Commands\Other;

use App\Console\Traits\TargetSites;
use App\Models\System\AppsManager;
use App\Setup\Install;
use Exception;
use Illuminate\Console\Command;
use Symfony\Component\Console\Input\InputOption;
use Apps;

/**
 * Class PreInstalledAppsCommand
 * @package App\Commands\Other
 */
class PreInstalledAppsCommand extends Command
{
    use TargetSites {
        TargetSites::getOptions as traitOptions;
    }

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'apps:preinstall';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Install preinstalled apps';

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        $filter = $this->option('filter');
        $this->exec(function () use ($filter): void {
            if ($filter) {
                $this->warn('Installing app ' . $filter);
            } else {
                $this->warn('Installing apps ...');
            }

            $installApps = Install::$installApps;
            if ($filter) {
                $installApps = array_intersect($installApps, explode(',', $filter));
            }

            foreach ($installApps as $app) {
                if (!Apps::installed($app)) {
                    $this->info('Installing ' . $app);
                    AppsManager::install($app);
                }
            }

            $this->info('All apps are installed');
        });
    }

    protected function getOptions()
    {
        $options = $this->traitOptions();
        $options[] = ['filter', null, InputOption::VALUE_REQUIRED, 'App filter.'];
        return $options;
    }
}
