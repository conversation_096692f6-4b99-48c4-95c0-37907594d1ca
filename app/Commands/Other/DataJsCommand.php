<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: user
 * Date: 05-09-2018
 * Time: 16:12
 */

namespace App\Commands\Other;

use App\Console\Traits\TargetSites;
use App\Helper\SiteCp\FileUploader;
use App\Helper\Translation\Translator;
use App\Locale\Currency;
use App\Models\Setting\Filemanager;
use App\Models\Setting\Setting;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;

class DataJsCommand extends Command
{
    use TargetSites;

    public const BOM = "\xEF\xBB\xBF";

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'js:data-generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate and upload i18n & currency data to js /must execute on change front language or change translation label/';

    protected $_allowed_keys = [
        'global.redirect',
        'global.please_wait',
        'global.localize_me',
        'global.please_wait_processing',
        'global.label.sku',
        'global.no_results',
        'global.act.categories',
        'global.act.vendors',
        'store.err.variant_no_more_than_%1$s_allowed_in_cart',
        'global.powered.by',
        'global.label.out_of_stock',
        'err.server_500',
    ];

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->exec(function (): void {

            $previous = $this->_previousDataFile();

            $content = $this->_generateContent();

            $upload = FileUploader::uploadContent(static::BOM . $content, 'cc_data_' . mt_rand(), 'js', true);

            if ($upload) {
                Setting::updateOrInsert([
                    'parameter' => 'data.js'
                ], [
                    'value' => $upload->path
                ]);

                $this->info(sprintf('Successfully upload file with language "%s" and currency "%s" url "%s"', site('language', config('app.fallback_locale')), site('currency'), $upload->url));

                if ($previous) {
                    $previous->delete();
                    $this->info('Successfully delete previous file');
                }

                //Setting::clearCache();
            } else {
                $this->warn('Unable to upload file!');
            }

        });
    }

    /**
     * @return string
     */
    protected function _generateContent(): string
    {
        //i18n
        $data = [
            'i18n_data' => [],
            'ccsettings' => [
                'currency' => Currency::locale(site('language'))->get(site('currency')),
            ]
        ];

        $file = 'sf';

        $trans = new Translator(app('translation.loader'), site('language'));
        $trans->setFallback(config('app.fallback_locale'));

        foreach ((array)$trans->get($file) as $key => $value) {
            if (!empty($key) && in_array($key, $this->_allowed_keys)) {
                Arr::set($data['i18n_data'], $key, $value);
            }
        }

        $response = [];
        foreach ($data as $key => $value) {
            $response[] = 'window.' . $key . ' = ' . json_encode($value, JSON_UNESCAPED_UNICODE) . ';';
        }

        return implode("\n", $response);
    }

    /**
     * @return null|Filemanager
     */
    protected function _previousDataFile()
    {
        return Filemanager::whereSystem(1)->where(function ($query): void {
            /** @var Filemanager $query */
            $query->where('name', 'like', 'cc_data_%.js')
                ->orWhere('name', 'cc_data.js');
        })->first();
    }
}
