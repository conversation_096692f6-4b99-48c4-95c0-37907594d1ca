<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 8.9.2016 г.
 * Time: 19:32 ч.
 */

namespace App\Commands\Other;

use App\Models\Category\CategoryPath;
use App\Console\Traits\TargetSites;
use Illuminate\Console\Command;

class CategoryPathRebulidCommand extends Command
{
    use TargetSites;

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'category:path-rebuild';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Rebuild categories paths';

    protected $_max_id = [];

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->exec(function (): void {
            $total = CategoryPath::repairCategories();

            $this->info(sprintf('Repaired %d categories', $total));
        });
    }
}
