<?php

declare(strict_types=1);

namespace App\Commands\Other;

use File;
use Illuminate\Console\Command;

class LangShortArray extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'lang:short-array';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Convert translations to short array';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $locales = File::directories(app()->langPath());

        foreach ($locales as $locale) {
            $this->info('Converting translations to short array for locale: ' . $locale);
            $files = File::allFiles($locale);
            foreach ($files as $file) {
                $array = include $file->getPathname();
                ksort($array);
                file_put_contents($file->getPathname(), '<?php return ' . varExport($array, true) . ';');
            }
        }

        $this->info('Done!');
    }

}
