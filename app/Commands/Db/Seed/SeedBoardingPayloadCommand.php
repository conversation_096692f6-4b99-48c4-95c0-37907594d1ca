<?php

declare(strict_types=1);

namespace App\Commands\Db\Seed;

use App\Exceptions\Error;
use App\Helper\DataBase\MySqlDbImporter;
use App\Helper\SiteCp\FileUploader;
use App\Helper\Theme;
use App\Helper\YesNo;
use App\Locale\Currency;
use App\Models\Apps\Applications;
use App\Models\Apps\GoogleProductCategory;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\PaymentProviders;
use App\Models\Oauth\SocialAccount;
use App\Models\Page\Page;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\Product\Vendor;
use App\Models\Router\Host;
use App\Models\Router\Site;
use App\Models\Setting\Admin;
use App\Models\Setting\Logo;
use App\Models\Shipping\ShippingProvider;
use App\Models\System\AppsManager;
use App\Traits\UrlHandle;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Modules\CcSegments\Models\SiteEventLog;
use Modules\Core\Core\Helpers\Settings\GeneralSettingsFormatter;
use Symfony\Component\Console\Input\InputOption;

/**
 *
 */
class SeedBoardingPayloadCommand extends Command
{
    use UrlHandle;

    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'site:seed-boarding-payload';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed site db based on boarding payload';

    /**
     * @var Site
     */
    protected $site;

    /** @var int */
    protected $timeout = 500;

    /** @var array */
    protected $payload = [];

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Throwable
     */
    public function handle(): void
    {
        $siteId = $this->option('site');
        if (!$siteId) {
            $this->error('Site is required');
            return;
        }

        $this->site = Site::find($siteId);
        if (!$this->site) {
            $this->error('There is no site with #' . $siteId);
            return;
        }

        try {
            $log = SiteEventLog::where('site_id', (int)$this->site->site_id)
                ->where('event_type', SiteEventLog::TYPE_SITE_CREATED)
                ->firstOrFail();

            $payload = Arr::get($log->data, 'install_data.boarding_payload');
            throw_if(!$payload, 'There is no boarding payload for site #' . $siteId);

            $this->payload = $payload;

            $this->site->bootDB();

            setting()->set('onboarding_progress', 1)->save();

            $this->connectSocialAccount();
            $this->setSiteIndustry();
            $this->setSiteLanguage();
            $this->seedDbDefaults();
            $this->setThemeFonts();
            $this->setThemeColors();
            $this->logoUpload();
            $this->createSimpleProductAI();
            $this->installPaymentMethods();
            $this->installShippingIntegrations();
            $this->installShippingManual('price');
            $this->installShippingManual('weight');
            $this->setHomePageContent();
            $this->addExternalDomain();
        } catch (\Throwable $throwable) {
            SiteEventLog::add(
                $siteId,
                SiteEventLog::TYPE_INSTALL_ERROR,
                null,
                [
                    'error' => $throwable->getMessage(),
                    'trace' => explode("\n", $throwable->getTraceAsString()),
                ]
            );
        }

        $this->site->update([
            'progress' => 'completed',
        ]);

        $this->info('Done!');
    }

    /**
     * @return void
     * @throws \Exception
     */
    protected function logoUpload()
    {
        $this->line('Begin logos upload');

        $logo = Arr::get($this->payload, 'LogoUpload.logo');
        if (!$logo) {
            $file = $this->defaultLogoToRequestFile();
        } else {
            $file = $this->base64ImageToRequestFile($logo, 'logo');
        }

        $logoTypes = [
//            'main',
            'default_image',
            'checkout',
            'invoice',
            'mail',
        ];

        if (is_array($file)) {
            $logo = Logo::fileName('logo')->firstOrCreate([
                'type' => 'main'
            ])->uploadImage();

            if ($logo) {
                foreach ($logoTypes as $logoType) {
                    Logo::firstOrNew([
                        'type' => $logoType
                    ])->forceFill([
                        'image' => $logo->image,
                        'max_thumb_size' => $logo->max_thumb_size,
                    ])->save();
                }
            }
        }

        $this->line('End logos upload');
    }

    /**
     * @return void
     * @throws Error
     * @throws \Throwable
     */
    protected function createSimpleProductAI()
    {
        $data = Arr::get($this->payload, 'AiAddSimpleProduct');
        if (!$data) {
            return;
        }

        $this->line('Begin create product');

        try {
            $category = Category::add([
                'name' => $data['product_category'],
                'url_handle' => $this->slug($data['product_category']),
            ], true);
        } catch (Error $error) {
            $category = Category::whereName($data['product_category'])->firstOrFail(['id']);
        }

        try {
            if (!empty($data['product_brand'])) {
                $vendor = Vendor::add([
                    'name' => $data['product_brand'],
                    'url_handle' => $this->slug($data['product_brand']),
                ]);
            }
        } catch (Error $error) {
            $vendor = Vendor::whereName($data['product_brand'])->first(['id']);
        }

        $attributes = [
            'type' => 'simple',
            'category_id' => $category->getKey(),
            'vendor_id' => !empty($vendor) ? $vendor->getKey() : null,
            'name' => $data['product_title'],
            'url_handle' => $this->slug($data['product_title']),
            'description' => $data['product_description'],
            'new' => 'yes',
            'featured' => 'yes',
            'seo_title' => Arr::get($data, 'seo_meta_title'),
            'seo_description' => Arr::get($data, 'seo_meta_description'),
            'variant' => [
                'price' => Arr::get($this->payload, 'AiSetInventorySimple.product_price', 1),
                'quantity' => Arr::get($this->payload, 'AiSetInventorySimple.product_quantity', 1),
            ]
        ];

        if (Arr::get($this->payload, 'AiSetInventorySimple.track_quantity')) {
            $attributes['track_inventory'] = 1;
        }

        $product = Product::add($attributes);

        $this->base64ImageToRequestFile($data['product_image'], 'file');

        $product->uploadImage();

        $this->line('End create product');
    }

    /**
     * @return void
     */
    protected function installPaymentMethods()
    {
        $data = Arr::get($this->payload, 'PaymentMethods.payment_methods');
        if (!$data) {
            return;
        }

        $this->line('Begin install payment methods');

        $apps = Applications::with(['payment'])->whereIn('key', $data)->get();

        $apps->map(function ($app): void {
            $this->warn('Installing ' . $app->key);
            $paymentMethod = PaymentProviders::findWithConfig($app->key);

            if (!$paymentMethod->configuration) {
                $settings = PaymentProviderConfiguration::createNewConfiguration($app->key);
//                $settings->update(['active' => YesNo::True]);
            }

            AppsManager::install($app->key);
        });

        $this->line('End install payment methods');
    }

    /**
     * @return void
     */
    protected function installShippingIntegrations()
    {
        $data = Arr::get($this->payload, 'ShippingMethodsSelectIntegration.selected_integration');
        if (!$data) {
            return;
        }

        $this->line('Begin install shipping integrations');

        $keys = Arr::pluck($data, 'id');
        $apps = Applications::whereIn('key', $keys)->get();

        $apps->map(function ($app): void {
            $this->warn('Installing ' . $app->key);
            AppsManager::install($app->key);
        });

        $this->line('End install shipping integrations');
    }

    /**
     * @return void
     * @throws \Throwable
     */
    protected function installShippingManual(string $type)
    {
        $unitMultiplier = $type == 'weight' ? 1000 : 100;
        $key = $type == 'weight' ?
            'ShippingMethodsManualSetRateWeightBased' :
            'ShippingMethodsManualSetRatePriceBased';

        $data = Arr::get($this->payload, $key);
        if (!$data) {
            return;
        }

        $this->line(sprintf('Begin install %s based shipping', $type));

        $rates = [];
        $from = 0;
        foreach (Arr::get($data, 'shipping_prices', []) as $shipping) {
            $rates[] = [
                'from' => $from,
                'to' => $shipping['unit_to'] * $unitMultiplier,
                'amount' => $shipping['shipping_price'] * 100,
            ];
            $from = $shipping['unit_to'] * $unitMultiplier;
        }

        $attributes = [
            'type' => $type,
            'target' => 'restofworld',
            'active' => 1,
            'name' => Arr::get($data, 'shipping_method_name', 'Shipping'),
            "___rates___" => [
                "address" => $type . "_rates",
            ],
            $type . "_rates" => [
                "address" => $rates,
            ]
        ];

        $provider = \Illuminate\Support\Facades\DB::transaction(function () use ($attributes) {
            $provider = ShippingProvider::create($attributes);

            $provider->attachRates($attributes);
            //            $provider->attachPayments(false, []);

            return $provider;
        });

        $this->line(sprintf('End install %s based shipping', $type));
    }

    /**
     * @return void
     */
    protected function connectSocialAccount()
    {
        $owner = Admin::getOwner();

        foreach (['google', 'facebook'] as $provider) {
            $account = SocialAccount::whereProvider($provider)
                ->where('email', $owner->email)
                ->first();

            if ($account) {
                $account->site()->connectAdmin($owner->getKey(), $provider);
            }
        }
    }

    /**
     * @return void
     */
    protected function setSiteIndustry()
    {
        if ($siteName = Arr::get($this->payload, 'BrandInfo.brand_name')) {
            setting()->set('site_name', $siteName)->save();
        }

        $data = Arr::get($this->payload, 'DescribeProducts.business_info.niche');
        if (!$data) {
            return;
        }

        $taxonomy = GoogleProductCategory::where('name', $data)->first();
        if ($taxonomy) {
            $this->site->update([
                'main_industry' => $taxonomy->getKey(),
                'industry' => [$taxonomy->getKey()],
            ]);
        }
    }

    /**
     * @return void
     */
    protected function addExternalDomain()
    {
        $value = Arr::get($this->payload, 'BrandInfo.existing_store_domain');
        if (!$value) {
            return;
        }

        if (Host::getHostByName($value)) {
            return;
        }

        $domain = new Host([
            'host' => $value,
            'site_id' => $this->site->getKey(),
            'external' => YesNo::True,
            'active' => YesNo::False,
            'ssl' => YesNo::False,
        ]);

        try {
            $domain->ns = $domain->getCurrentNameservers();
            $domain->ips = $domain->getCurrentIps();
            $domain->fillWhois();
        } catch (\Throwable $throwable) {
        }

        $domain->save();
    }

    /**
     * @return void
     */
    protected function setSiteLanguage()
    {
        $data = Arr::get($this->payload, 'LanguageInfo');
        if (!$data) {
            return;
        }

        $settings = new GeneralSettingsFormatter();
        $settings->fill([
            'language' => $data['store_front_language'],
            'language_cp' => $data['admin_panel_language'],
            'timezone' => $data['time_zone'],
            'currency' => $data['currency'] ?? Currency::fromCountryCode(),
        ])->save();
    }

    /**
     * @return void
     * @throws \Exception
     */
    protected function setHomePageContent()
    {
        $data = Arr::get($this->payload, 'DescribeProducts.home_page_content.hero');
        if (!$data) {
            return;
        }

        $heading = Arr::get($data, 'heading');
        $image = Arr::get($data, 'image');

        $file = $this->base64ImageToRequestFile($image, 'file');
        if (is_array($file)) {
            $hero = FileUploader::upload($file, 'hero', $file['ext']);
            if ($page = Page::where('system_page', 'home')->first()) {
                $content = json_decode((string) $page->content);

                $shopBtn = '';
                if ($category = Category::first()) {
                    //                    $shopBtn = '<p class="text-aligncenter _mt-50"><a href="' . $category->url . '" class="_button">Shop Now</a></p>';
                    $content->children[2]->children[0]->children[1]->settings->buttons->{"1"}->link = $category->url;
                    //                    $content->children[2]->children[0]->children[1]->settings->buttons->{"1"}->text = __('sf.global.act.quick_view');
                    $content->children[2]->children[0]->children[1]->settings->buttons->{"1"}->text = $category->name;
                }

                $content->children[0]->children[0]->children[0]->settings->text = '<h1 class="text-alignleft"><span style="font-size: 36pt; font-weight: 600; color: #fff;">' .
                    $heading . "</span></h1>\n" . $shopBtn;
                $content->children[1]->children[0]->children[0]->settings->text = $content->children[0]->children[0]->children[0]->settings->text;
                $content->children[0]->options->src = $hero->url;
                $content->children[1]->options->src = $hero->url;

                if ($intro = Arr::get($this->payload, 'DescribeProducts.home_page_content.intro')) {
                    $content->children[2]->children[0]->children[0]->settings->text = "<h2>{$intro['title']}</h2>\n<p></p>\n<p>{$intro['description']}</p>";
                }

                $page->update(['content' => json_encode($content)]);
            }
        }
    }

    /**
     * @return void
     * @throws \Exception
     */
    protected function seedDbDefaults()
    {
        $directory = base_path('database/boarding'); // Specify the directory path
        $files = glob($directory . '/*.sql'); // Get all files in the directory

        foreach ($files as $file) {
            $this->importDb($file);
        }
    }

    /**
     * @param $importFile
     * @return void
     * @throws \Exception
     */
    protected function importDb(string $importFile)
    {
        $this->info(sprintf('Importing %s to site #', $importFile) . $this->site->site_id);
        $databaseConfig = $this->site->getRouterConnectionConfig();
        $dbName = 'cc_site_' . $this->site->site_id;

        //        DynamicConnection::reconnect($databaseConfig);

        MySqlDbImporter::create()
            ->setTimeout(30)
            ->setHost($databaseConfig['host'])
            ->setPort((int)$databaseConfig['port'])
            ->setDbName($dbName)
            ->setUserName($databaseConfig['username'])
            ->setPassword($databaseConfig['password'])
            ->importFromFile($importFile);
    }

    /**
     * @return void
     * @throws Error
     */
    protected function setThemeFonts()
    {
        $data = Arr::get($this->payload, 'FontPairing.font_pairing');
        if (!$data) {
            return;
        }

        $this->line('Begin set theme fonts');

        $theme = new Theme();
        $variables = $theme->getVariables(false, false);
        $request['variable'] = array_merge($variables, [
            'font-family-main' => Arr::get($data, 'body.title'),
            'font-family-titles' => Arr::get($data, 'heading.title'),
        ]);
        $theme->unloadVariables();
        $theme->editParameters($request);

        $this->line('End set theme fonts');
    }

    /**
     * @return void
     * @throws Error
     */
    protected function setThemeColors()
    {
        $data = Arr::get($this->payload, 'ColorPalette.accent_color');
        if (!$data) {
            return;
        }

        $darkerColor = $this->darkenColor($data);
        //        dd($data, $darkerColor);
        $this->line('Begin set theme colors');

        $theme = new Theme();
        $variables = $theme->getVariables(false, false);
        $main_color = $variables['color-main-highlight'];
        $change_colors = array_keys($variables, $main_color);
        $request['variable'] = $variables;

        foreach ($change_colors as $change_color) {
            $request['variable'][$change_color] = $data;
        }

        if (!empty($request['variable'])) {
            $request['variable'] = array_merge($request['variable'], [
                'color-button-background-hover' => $darkerColor,
                'color-button-borders-hover' => $darkerColor,
                'color-second-button-background' => $data,
                'color-second-button-borders' => $data,
                'color-second-button-background-hover' => $darkerColor,
                'color-second-button-borders-hover' => $darkerColor,
            ]);
            $theme->unloadVariables();
            $theme->editParameters($request);
        }

        $this->line('End set theme colors');
    }

    /**
     * @param mixed $hex
     * @param mixed $factor
     * @return mixed
     */
    protected function darkenColor($hex, $factor = 0.1): string
    {
        $hex = str_replace('#', '', $hex);

        // Make sure the factor is between 0 and 1
        $factor = max(0, min(1, $factor));

        // Convert hex to RGB
        $red = hexdec(substr($hex, 0, 2));
        $green = hexdec(substr($hex, 2, 2));
        $blue = hexdec(substr($hex, 4, 2));

        // Calculate darker color
        $red = round($red * (1 - $factor));
        $green = round($green * (1 - $factor));
        $blue = round($blue * (1 - $factor));

        // Make sure the values are valid
        $red = intval(max(0, min(255, $red)));
        $green = intval(max(0, min(255, $green)));
        $blue = intval(max(0, min(255, $blue)));

        // Convert RGB back to hex
        return '#' . str_pad((string) dechex($red), 2, '0', STR_PAD_LEFT)
            . str_pad((string) dechex($green), 2, '0', STR_PAD_LEFT)
            . str_pad((string) dechex($blue), 2, '0', STR_PAD_LEFT);
    }

    /**
     * @param string $image
     * @param $requestKey
     * @return array
     */
    protected function base64ImageToRequestFile(string $image, $requestKey): array
    {
        preg_match('/^data:(image\/(.*));base64,/', $image, $type);
        $data = substr($image, strpos($image, ',') + 1);
        list(, $mime, $ext) = $type;
        $imageData = base64_decode($data);

        $tempFilePath = sys_get_temp_dir() . '/' . uniqid();
        file_put_contents($tempFilePath, $imageData);
        //        $info = getimagesize($tempFilePath);

        return $_FILES[$requestKey] = [
            "name" => implode('_', [
                    'temp',
                    $requestKey,
                    uniqid(),
                ]) . '.' . $ext,
            "type" => $mime,
//            "type" => $info['mime'],
            "tmp_name" => $tempFilePath,
            "error" => 0,
            "size" => filesize($tempFilePath),
            "ext" => $ext,
            "from_url" => true
        ];
    }

    protected function defaultLogoToRequestFile(): array
    {
        $mime = 'image/webp';
        $ext = 'webp';
        $imageData = file_get_contents(base_path('database/boarding/logo.webp'));

        $tempFilePath = sys_get_temp_dir() . '/' . uniqid();
        file_put_contents($tempFilePath, $imageData);
        //        $info = getimagesize($tempFilePath);

        return $_FILES['logo'] = [
            "name" => implode('_', [
                    'temp',
                    'logo',
                    uniqid(),
                ]) . '.' . $ext,
            "type" => $mime,
//            "type" => $info['mime'],
            "tmp_name" => $tempFilePath,
            "error" => 0,
            "size" => filesize($tempFilePath),
            "ext" => $ext,
            "from_url" => true
        ];
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return [
            ['site', null, InputOption::VALUE_REQUIRED, 'Site ID.'],
        ];
    }
}
