<?php

declare(strict_types=1);

namespace App\Commands\Db;

use App\Commands\CommandSafeMode;
use App\Models\Gate\Call;
use App\Models\Gate\Note;
use App\Models\PBX\PbxCallLog;
use OpenAI;
use Throwable;

/**
 * Class CallSpeechCommand
 *
 * @package App\Commands\Db
 * @deprecated
 */
class CallSpeechCommand extends CommandSafeMode
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'call:speech {--call= : Call ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Download calls speech';

    /**
     * Execute the console command.
     *
     * @return void
     * @throws Throwable
     */
    #[\Override]
    public function handle(): void
    {
        $this->input->setOption('execute', true);
        parent::handle();

        $id = $this->input->getOption('call');
        if ($id) {
            $query = Call::with('note')->where('id', $id);
        } else {
            $query = Call::with('note')->where('speech_synced', 0);
        }

        $items = $query->get();
        $this->syncSpeech($items);
        $this->line('Done!');

        //        if ($id) {
        //            $query = Call::with('note')->where('id', $id);
        //        } else {
        //            $query = Call::with('note')
        //                ->where('speech_synced', 1)
        //                ->where('speech_processed', 0)
        //                ->where('speech', '!=', 'empty');
        //        }
        //
        //        $items = $query->get();
        //        $this->processSpeech($items);
        //        $this->line('Done!');
    }

    /**
     * @param $items
     * @return void
     */
    protected function syncSpeech($items)
    {
        $this->line('Syncing call speech for ' . $items->count() . ' items ...');
        foreach ($items as $item) {
            /** @var Call $item */
            $this->line('Downloading speech for call #' . $item->getKey());
            $pbxLog = PbxCallLog::where('call_id', $item->getKey())->first();
            if ($pbxLog) {
                if (empty($pbxLog->Speech)) {
                    $this->warn('Speech is empty, skipping ...');
                    continue;
                }

                $item->update([
                    'speech' => $pbxLog->Speech,
                    'speech_synced' => 1,
                ]);

                if ($item->note) {
                    $item->note->update(['content' => $item->speech]);
                } else {
                    Note::create([
                        'cc_user_id' => $item->cc_user_id,
                        'user_id' => $item->user_id,
                        'site_id' => $item->site_id,
                        'model_id' => $item->getKey(),
                        'model_type' => $item->getMorphClass(),
                        'content' => $item->speech,
                    ]);
                }

                $this->info('Done');
            } else {
                $this->warn('No pbx record found for call #' . $item->getKey());
                if (!$item->record_filename) {
                    continue;
                }

                // fix missing call_id link
                $pbxLog = PbxCallLog::where('record', $item->record_filename)->first();
                if ($pbxLog && !$pbxLog->call_id) {
                    $pbxLog->update(['call_id' => $item->getKey()]);
                    $this->info('Call #' . $item->getKey() . ' now is linked to pbx record #' . $pbxLog->getKey());
                }
            }
        }
    }

    /**
     * @param $items
     * @return void
     */
    protected function processSpeech($items)
    {
        $this->line('OpenAI processing call speech for ' . $items->count() . ' items ...');
        $command = "summarize the conversation, get the tone of it and make conclusion in Bulgarian in max 80 words: \n";

        foreach ($items as $item) {
            /** @var Call $item */
            $this->line('Processing speech for call #' . $item->getKey());
            $note = OpenAI::prompt($command . $item->speech);
            if ($note) {
                $item->update([
                    'speech_processed' => 1,
                ]);
                if ($item->note) {
                    $item->note->update(['content' => $note]);
                } else {
                    Note::create([
                        'cc_user_id' => $item->cc_user_id,
                        'user_id' => $item->user_id,
                        'site_id' => $item->site_id,
                        'model_id' => $item->getKey(),
                        'model_type' => $item->getMorphClass(),
                        'content' => $note,
                    ]);
                }

                $this->info('Done');
            } else {
                $this->warn('Empty response from OpenAI');
            }
        }
    }
}
