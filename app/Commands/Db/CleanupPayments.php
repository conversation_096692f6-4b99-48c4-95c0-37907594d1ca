<?php

declare(strict_types=1);

namespace App\Commands\Db;

use App\Commands\CommandSafeMode;
use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Gateway\Payments;
use App\Models\Router\Site;
use Symfony\Component\Console\Input\InputOption;

/**
 * Class CleanupPayments
 *
 * @package App\Commands\Db
 */
class CleanupPayments extends CommandSafeMode
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:payments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup Payments DB (destroy deleted sites related data)';

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Exception
     */
    #[\Override]
    public function handle(): void
    {
        parent::handle();
        $processed = 0;
        $destroyed = 0;

        $siteIds = Payments::select(['site_id'])->distinct()->pluck('site_id');
        $existingIds = Site::whereIn('site_id', $siteIds)->pluck('site_id')->toArray();

        foreach ($siteIds as $siteId) {
            $processed++;
            if (!in_array($siteId, $existingIds)) {
                $this->info(sprintf('Site #%s is deleted!', $siteId));
                $this->line(sprintf('Destroying records for site #%s ...', $siteId));
                if (!$this->inSafeMode()) {
                    Payments::where('site_id', $siteId)->delete();
                    PaymentProviderConfiguration::where('site_id', $siteId)->delete();
                }

                $destroyed++;
            }

            $this->warn(sprintf('Done | processed: %d | destroyed: %d', $processed, $destroyed));
        }
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return [
            ['execute', null, InputOption::VALUE_NONE, 'Destroy unused payments data.'],
        ];
    }
}
