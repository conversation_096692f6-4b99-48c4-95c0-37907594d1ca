<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.7.2018 г.
 * Time: 11:07 ч.
 */

namespace App\Commands\Db\ModelsGenerate;

use Illuminate\Database\Query\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use ReflectionClass;
use ReflectionMethod;
use ReflectionFunctionAbstract;
use App\Traits\AutoComplete;
use App\Traits\SeoFields;
use App\Traits\UrlHandle;

class Params
{
    protected $object;

    protected $ignore_methods = [
        '__construct', '__get', '__set',
        '__isset', '__unset', '__call',
        '__callstatic', '__tostring',
        '__wakeup'
    ];

    /**
     * @param mixed $object
     * @return mixed
     */
    public function __construct($object)
    {
        $this->object = is_object($object) ? $object::class : $object;
    }

    /**
     * @param mixed $type
     * @return mixed
     */
    public function parse($type = 'all')
    {
        if ($type == 'p') {
            $data = $this->parseProperties();
        } elseif ($type == 'm') {
            $data = $this->parseMethods();
        } else {
            $properties = $this->parseProperties();
            $methods = $this->parseMethods();
            $data = $properties + $methods;
        }

        return $data;
    }

    /**
     * @return array{group: '@property', type: string, value: non-falsy-string}[]
     */
    protected function parseProperties(): array
    {
        $reflection = new ReflectionClass($this->object);
        $props = $reflection->getDefaultProperties();
        $data = [];
        foreach ($props as $key => $value) {
            $type = gettype($value);
            if ($type == 'object') {
                $data['$' . $key] = ['group' => '@property', 'type' => '\\' . $value::class, 'value' => '$' . $key];
            } else {
                $data['$' . $key] = ['group' => '@property', 'type' => $type, 'value' => '$' . $key];
            }
        }

        return $data;
    }

    /**
     * @return array{group: non-falsy-string, type: '', value: non-falsy-string}[]
     */
    protected function parseMethods(): array
    {
        $reflection = new ReflectionClass($this->object);
        $methods = $reflection->getMethods(ReflectionMethod::IS_PUBLIC);
        $data = [];
        foreach ($methods as $method) {
            $name = $method->getName();
            $method_prefix = $this->parsePrefix($method, $reflection);

            if (!in_array(strtolower($name), $this->ignore_methods)) {
                $data[$name] = ['group' => '@method' . $method_prefix, 'type' => '', 'value' => $name . '(' . $this->parameters($method) . ')'];
            }
        }

        return $data;
    }

    /**
     * @param ReflectionMethod $method
     * @param ReflectionClass $reflection
     * @return mixed
     */
    private function parseReturn(ReflectionMethod $method, ReflectionClass $reflection): ?string
    {
        $object_name = $reflection->getName();
        $prefix = '';
        if ($method->getDocComment() && preg_match('~@return\s(.*)~i', $method->getDocComment(), $m)) {
            $uses = $this->getUses($method);
            $p = explode('|', $m[1]);
            $p = array_map('trim', $p);
            foreach ($p as $k => $v) {
                if (isset($uses[$v])) {
                    $p[$k] = $uses[$v];
                }
            }

            if (in_array('static', $p) || in_array('static[]', $p) || in_array('$this', $p)) {
                foreach ($p as $e => $t) {
                    if ($t == 'static') {
                        $p[$e] = '\\' . Model::class;
                    } elseif ($t == 'static[]') {
                        $p[$e] = '\\' . Collection::class;
                    } elseif ($t == '$this') {
                        if ($object_name == \Illuminate\Database\Query\Builder::class) {
                            $object_name = 'Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder';
                        }

                        $p[$e] = '\\' . $object_name;
                    } elseif ($t == \Illuminate\Database\Query\Builder::class) {
                        $p[$e] = '\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder';
                    } elseif (!empty($uses[$t])) {
                        $p[$e] = $uses[$t];
                    }
                }
            } else {
                foreach ($p as $e => $t) {
                    if (str_ends_with((string) $t, '[]')) {
                        $key = substr((string) $t, 0, -2);
                        if (array_key_exists($key, $uses)) {
                            $match = $uses[$key];
                        } else {
                            $parts = explode('\\', $method->class);
                            array_pop($parts);
                            if (!class_exists($match = implode('\\', array_merge($parts, [$key])))) {
                                $match = $key;
                            } else {
                                $match = '\\' . $match;
                            }
                        }

                        $p[$e] = $match . '[]';
                    }
                }
            }

            $m[1] = implode('|', $p);
            if ($m[1] == \Illuminate\Database\Query\Builder::class) {
                $m[1] = '\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder';
            }

            $prefix .= ' ' . $m[1];
        }

        return preg_replace('~([ ]{2,})~', ' ', $prefix);
    }

    /**
     * @return non-falsy-string[]
     */
    private function getUses(ReflectionMethod $method): array
    {
        $content = explode('class ' . Arr::last(explode('\\', $method->class)), file_get_contents($method->getFileName()))[0];
        preg_match_all("/use\s+(.*);\n?/i", $content, $u);

        $return = [];
        foreach ($u[1] ?? [] as $m) {
            $t = preg_split('/ as /i', $m);
            if (count($t) == 2) {
                $k = $t[1];
                $m = $t[0];
            } else {
                $k = Arr::last(explode('\\', $m));
            }

            if (!str_starts_with($m, '\\')) {
                $m = '\\' . $m;
            }

            $return[$k] = $m;
        }

        return $return;
    }

    /**
     * @param ReflectionMethod $method
     * @param ReflectionClass $reflection
     * @return mixed
     */
    private function parsePrefix(ReflectionMethod $method, ReflectionClass $reflection): ?string
    {
        $object_name = $reflection->getName();
        $prefix = '';
        if ($method->isStatic() || $object_name == Builder::class) {
            $prefix = ' static ';
        }

        if ($object_name == Builder::class) {
            $methods = $this->getModelPrivateMethods();
            if ($methods->has($method->getName())) {
                $prefix = '';
            }
        } else {
            if ($method->isStatic()) {
                $prefix = ' static ';
            }
        }

        if ($ret = $this->parseReturn($method, $reflection)) {
            $prefix .= ' ' . $ret;
        }

        return preg_replace('~([ ]{2,})~', ' ', $prefix);
    }

    private function getModelPrivateMethods()
    {
        static $methods = null;
        if (!is_null($methods)) {
            return $methods;
        }

        $mreflection = new ReflectionClass(Model::class);
        $methods = \Illuminate\Support\Collection::make($mreflection->getMethods());
        return $methods = $methods->filter(fn (ReflectionMethod $method): bool => ($method->isPrivate() || $method->isProtected()) && !$method->isStatic())->keyBy('name');
    }

    /**
     * @param ReflectionFunctionAbstract $method
     * @param bool $call
     * @param int $start_from
     * @return mixed
     */
    private function parameters(ReflectionFunctionAbstract $method, bool $call = false, int $start_from = 0): string
    {
        $parameters = $method->getParameters();
        for ($i = 0; $i < $start_from; $i++) {
            array_shift($parameters);
        }

        if (!$parameters) {
            return '';
        }

        if ($call) {
            $par = [];
            foreach ($parameters as $p) {
                $par[] = '$' . $p->getName();
            }

            return implode(', ', $par);
        }

        $par = [];
        foreach ($parameters as $t => $p) {
            $par[$t] = '';
            if ($p->getClass() !== null) {
                if (is_object($p->getClass())) {
                    if ($p->getClass() instanceof \ReflectionClass) {
                        $class_name = $p->getClass()->getName();
                    } else {
                        $class_name = $p->getClass()::class;
                    }
                } else {
                    $class_name = $p->getClass();
                }

                $par[$t] .= '\\' . $class_name . ' ';
            }

            $par[$t] .= '$' . $p->getName();
            if ($p->isDefaultValueAvailable()) {
                $par[$t] .= ' = ' . $this->parType($p->getDefaultValue());
            }
        }

        return implode(', ', $par);
    }

    /**
     * @param mixed $type
     * @return mixed
     */
    protected function parType($type)
    {
        if (is_array($type)) {
            return '[]';
        }

        if (is_null($type)) {
            return 'null';
        }

        if ($type === '') {
            return "''";
        }

        if (is_string($type)) {
            return "'" . $type . "'";
        }

        if ($type === false) {
            return 'false';
        }

        if ($type === true) {
            return 'true';
        }

        if (is_int($type)) {
            return (string)(int)$type;
        }

        if (!$type) {
            return "''";
        }

        return $type;
    }

    /**
     * @return array<int|string, \non-empty-list<string>>
     * @param mixed $phpcode
     */
    public static function getPhpClasses($phpcode): array
    {
        $classes = [];

        $namespace = 0;
        $tokens = token_get_all($phpcode);
        $count = count($tokens);
        $dlm = false;
        for ($i = 2; $i < $count; $i++) {
            if ((isset($tokens[$i - 2][1]) && ($tokens[$i - 2][1] == "phpnamespace" || $tokens[$i - 2][1] == "namespace")) ||
                ($dlm && $tokens[$i - 1][0] == T_NS_SEPARATOR && $tokens[$i][0] == T_STRING)
            ) {
                if (!$dlm) {
                    $namespace = 0;
                }

                if (isset($tokens[$i][1])) {
                    $namespace = $namespace ? $namespace . "\\" . $tokens[$i][1] : $tokens[$i][1];
                    $dlm = true;
                }
            } elseif ($dlm && ($tokens[$i][0] != T_NS_SEPARATOR) && ($tokens[$i][0] != T_STRING)) {
                $dlm = false;
            }

            if (($tokens[$i - 2][0] == T_CLASS || (isset($tokens[$i - 2][1]) && $tokens[$i - 2][1] == "phpclass"))
                && $tokens[$i - 1][0] == T_WHITESPACE && $tokens[$i][0] == T_STRING
            ) {
                $class_name = $tokens[$i][1];
                if (!isset($classes[$namespace])) {
                    $classes[$namespace] = [];
                }

                $classes[$namespace][] = $class_name;
            }
        }

        return $classes;
    }

    /**
     * @param mixed $name
     * @param mixed $privacy
     * @return mixed
     */
    public static function getPhpClassData($name, $privacy = ReflectionMethod::IS_PUBLIC): ?array
    {
        $ref = new ReflectionClass($name);
        $trait = false;
        if (!($parent = $ref->getParentClass()) || !($pparent = $parent->getParentClass()) || $pparent->getName() != \Illuminate\Database\Eloquent\Model::class) {
            if (!$ref->isTrait()) {
                return null;
            } else {
                $trait = true;
            }
        }

        $static = new static($name);
        /** @var \Illuminate\Support\Collection $methods */
        $methods = \Illuminate\Support\Collection::make($privacy ? $ref->getMethods($privacy) : $ref->getMethods())
            ->filter(function (ReflectionMethod $method) use ($name, $trait): bool {
                if ($trait) {
                    return true;
                }

                if ($method->class == $name) {
                    return false;
                }

                if (str_starts_with($method->getName(), 'scope')) {
                    return true;
                }

                if (preg_match('!(get|set)(.+)(Attributes?)!', $method->getName(), $match)) {
                    if ($match[3] == 'Attributes') {
                        return false;
                    }

                    return true;
                }

                return false;
            })->map(function (ReflectionMethod $method) use ($static, $trait, $ref): ?array {
                if (str_starts_with($method->getName(), 'scope')) {
                    $name = substr($method->getName(), 5);
                    $return = [strtolower(substr($name, 0, 1)) . substr($name, 1), $static->parameters($method, false, 1)];
                    if ($trait) {
                        $return[] = 'static Builder';
                    }

                    return $return;
                } elseif (!$trait && preg_match('!(get|set)(.+)Attribute!', $method->getName(), $m)) {
                    return ['@getset@', Str::snake($m[2]), trim(((string) (new static($ref))->parseReturn($method, $ref)))];
                } elseif ($trait) {
                    $name = $method->getName();
                    return [$name, $static->parameters($method, false, 0), trim((string) (new static($ref))->parsePrefix($method, $ref))];
                }

                return null;
            });
        $methods = $methods->filter()->unique();
        if (!$methods->count()) {
            return null;
        }

        return [$trait ? null : $parent->getName(), $methods->all()];
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    public static function getExtended($name): ?string
    {
        $ref = new ReflectionClass($name);
        if (!($parent = $ref->getParentClass()) || !($pparent = $parent->getParentClass()) || $pparent->getName() != \Illuminate\Database\Eloquent\Model::class) {
            return null;
        }

        return $parent->getName();
    }

    /**
     * @param mixed $column
     * @param mixed $string
     * @return mixed
     */
    public static function translateType($column, $string): string
    {
        if (in_array($column, ['date_sent', 'date_added', 'created_at', 'date_created', 'date_modified', 'updated_at', 'last_edited', 'date_last_updated', 'date_last_update', 'date_last_update', 'date_locking'])) {
            return \Carbon\Carbon::class;
        } elseif (in_array($string, ['json'])) {
            return 'array';
        } elseif (preg_match('/((?:var)?char)/i', (string) $string, $matches)) {
            return 'string';
        } elseif (preg_match('/decimal/i', (string) $string, $matches)) {
            return 'float';
        } elseif (preg_match('/float/i', (string) $string, $matches)) {
            return 'float';
        } elseif (preg_match('/double/i', (string) $string, $matches)) {
            return 'float';
        } elseif (preg_match('/((?:big|medium|small|tiny)?int)/i', (string) $string, $matches)) {
            return 'integer';
        } elseif (preg_match('/(enum|set)/i', (string) $string, $matches)) {
            return 'string';
        } elseif (preg_match('/((?:big|medium|small|tiny|long)?text)/i', (string) $string, $matches)) {
            return 'string';
        } elseif (preg_match('/((?:big|medium|small|tiny|long)?blob)/i', (string) $string, $matches)) {
            return 'string';
        } elseif (preg_match('/(datetime|date|timestamp)/i', (string) $string, $matches)) {
            return \Carbon\Carbon::class;
        } elseif (preg_match('/(time)/i', (string) $string, $matches)) {
            return 'string';
        } elseif ($string == 'object') {
            return \stdClass::class;
        }

        return 'string';
    }

    /**
     * @return array<'App\\Traits\\AutoComplete'|'App\\Traits\\SeoFields'|'App\\Traits\\UrlHandle', non-empty-list>
     */
    public static function parseTraites(): array
    {
        $traites = [
            UrlHandle::class,
            SeoFields::class,
            AutoComplete::class
        ];
        $methods = [];
        foreach ($traites as $trait) {
            $results = Params::getPhpClassData($trait);
            if (isset($results[1])) {
                foreach ($results[1] as $m) {
                    //if(!in_array($m[0], ['boot'])) {
                    $methods[$trait][] = $m;
                    //}
                }
            }
        }

        return $methods;
    }

    /**
     * @param mixed $a
     * @param string $ret
     * @return mixed
     */
    public static function traitFix($a, string $ret)
    {
        if ($a == 'static Model') {
            return 'static ' . $ret;
        } elseif ($a == 'static Collection') {
            return 'static \Illuminate\Support\Collection';
        } elseif ($a == 'Builder') {
            return '\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder|' . $ret;
        } elseif ($a == 'static Builder') {
            return 'static \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder|' . $ret;
        }

        return $a;
    }

}
