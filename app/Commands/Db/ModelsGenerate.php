<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.7.2018 г.
 * Time: 11:01 ч.
 */

namespace App\Commands\Db;

use App\Traits\UrlHandle;
use App\Commands\Db\ModelsGenerate\getRelations;
use App\Commands\Db\ModelsGenerate\Params;
use App\Helper\DataBase\Schema\TableSchema;
use File;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use ReflectionClass;
use Illuminate\Console\Command;
use App;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Symfony\Component\Finder\SplFileInfo;
use App\Traits\SeoFields;
use App\Traits\AutoComplete;
use App\Models\Customer\CustomerShippingAddress;
use App\Models\Customer\CustomerBillingAddress;
use App\Models\Order\ShippingAddress;
use App\Models\Order\BillingAddress;
use App\Traits\Translatable;
use App\Models\Router\Site;
use Symfony\Component\Process\Process;

class ModelsGenerate extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $name = 'make:base-models';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate base models';


    /**
     * Execute the console command.
     *
     * @throws Exception
     */
    public function handle(): void
    {
        //        /** @var Site $site */
        //        if (!inDevelopment() || !($site = Site::find(2160))) {
        //            return;
        //        }
        //
        //        $site->bootDB();

        if (!inDevelopment()) {
            return;
        }

        config()->set('database.connections.default', array_merge(config('database.connections.router'), ['database' => 'cc_system_install_database_bg']));

        /** @var SplFileInfo[] $objects */
        $objects = array_merge(
            File::allFiles(app_path('Models/')),
            File::allFiles(base_path('modules/*/Models/')),
            File::allFiles(base_path('modules/*/*/Models/')),
            File::allFiles(base_path('modules/*/*/*/Models/')),
            File::allFiles(base_path('modules/*/*/*/*/Models/')),
            File::allFiles(base_path('app/Integration/*/Models/'))
        );

        $classes = [];
        $extends = [];
        foreach ($objects as $o) {
            if (in_array(basename($o->getPath()), ['Base'])) {
                continue;
            }

            $class = Params::getPhpClasses(file_get_contents($o->getPathname()));
            foreach ($class as $ns => $cl) {
                foreach ($cl as $c) {
                    if (!is_null($p = Params::getPhpClassData('\\' . $ns . '\\' . $c, null))) {
                        $classes[$p[0]] = $p[1];
                    }

                    if (!is_null($p = Params::getExtended('\\' . $ns . '\\' . $c))) {
                        $e = key($class);
                        $extends[$p] = $e . '\\' . $class[$e][0];
                    }
                }
            }
        }

        $tables = Collection::make(\Illuminate\Support\Facades\DB::select('show tables'))
            ->filter(fn ($table): bool => !str_starts_with((string) \Illuminate\Support\Arr::first((array)$table), '@'));

        $model_data = [];//(new Params($model))->parse();
        $builder_data = [];//(new Params($builder))->parse('m');
        $data = array_merge($builder_data, $model_data);

        $url_handle_columns = [
            'url_handle',
        ];
        $seo_columns = [
            'seo_title', 'seo_description'
        ];
        $autocomplete = [
            'products', 'type__products_vendors', 'blogs',
            'blogs_articles', 'pages', 'product_selections',
            'properties_options', 'type__products_categories',
            'properties', 'banners_groups', 'discounts',
            'tags__products_tags', 'tags__articles_tags',
            'products_parameters', 'products_parameters_options',
            'customers', 'type__customer_groups', 'orders_meta',
            'customers_shipping_addresses', 'tags__customers__items',
            'tags__customers', 'product_statuses', 'form_field_options',
            'subscribers_segments', 'shipping_providers', 'subscribers',
        ];

        $ignored_tables = [
            'supplier_addresses', 'supplier_products', 'suppliers',
            'messenger_messages', 'subscribers_rfm_orders',
        ];

        //enable object to clear cache
        $enable_object_to_clear_cache = [
            'apps', 'apps_settings', 'banners', 'shops',
            'banners_groups', 'blogs', 'blogs_articles',
            'bnp_promotions', 'category_path', 'comment__articles_comments',
            'cross_sell', 'discount_label_description', 'discounts',
            'discounts_to_customer_groups', 'discounts_to_targets',
            'form_field_mapping', 'form_field_options', 'form_fields',
            'front_theme', 'front_widgets', 'images__products',
            'images__products_colors', 'images__products_variants', 'logos',
            'navigations', 'navigations_groups', 'orders',
            'orders_products_up_sell', 'pages', 'product_banners',
            'product_labels', 'product_selections', 'product_selections_criteria',
            'product_statuses', 'product_to_category', 'product_to_discount',
            'product_to_leasing', 'products', 'products_bundle', 'products_files',
            'products_meta', 'products_parameters', 'products_parameters_options',
            'products_parameters_options_stat', 'products_quantities', 'products_tabs',
            'products_variants', 'properties', 'properties_options',
            'property_option_to_product', 'property_to_category', 'tags__products_tags',
            'tags__products_tags__items', 'translations', 'type__products_categories',
            'type__products_vendors', 'up_sell'
        ];

        $global_meta_data = [
            'products', 'type__products_vendors', 'type__products_categories',
            'properties', 'properties_options', 'products_parameters', 'products_parameters_options',
            'customers', 'customers_billing_addresses', 'customers_shipping_addresses',
            'type__customer_groups', 'products_variants', 'discounts', 'blogs_articles', 'blogs'
        ];

        $exclude_tables = ['gdpr_', '@', '[migrations]', 'csv_import_', 'products_lists_temp_'];

        $traits = Params::parseTraites();

        $image_map = config('image.map');

        foreach ($tables as $table) {
            $table = \Illuminate\Support\Arr::first((array)$table);

            if (Str::startsWith($table, $exclude_tables)) {
                continue;
            }

            if (in_array($table, $ignored_tables)) {
                continue;
            }

            $schema = new TableSchema($table);
            if (Str::lower($schema->engine) == 'memory') {
                continue;
            }

            $has_url_handle = false;
            $has_seo_fields = null;
            $bootTrait = null;
            if ($table != 'url_handle_history') {
                foreach ($url_handle_columns as $c2) {
                    if ($schema->hasColumn($c2)) {
                        $has_url_handle = $c2;
                        break;
                    }
                }
            }

            foreach ($seo_columns as $c2) {
                if ($schema->hasColumn($c2)) {
                    $has_seo_fields[] = $c2;
                }
            }

            $fulltextSearch = [];
            if ($schema->fulltextSearchAll) {
                $fulltextSearch = $schema->fulltextSearchAll;
            }

            $model_name = ucfirst(Str::camel(preg_replace('~[^0-9a-z-_]~i', '_', (string) $table)));

            $out = ['<?php', ''];
            $out[] = $this->addTab(-2) . '/**';
            $out[] = $this->addTab(-2) . '* Created by Georgi Nachev (CloudCart).';
            $out[] = $this->addTab(-2) . '* User: Georgi Nachev <<EMAIL>>';
            $out[] = $this->addTab(-2) . '* Date: ' . \Carbon\Carbon::now()->format('Y');
            $out[] = $this->addTab(-2) . '*/';
            $out[] = '';

            $out = array_merge($out, ['namespace App\Models\Base;', '']);
            if ($schema->foreignKeys) {
                $out[] = $this->addTab(-2) . '/**';
                $out[] = $this->addTab(-2) . '* Foreign Keys in table: ' . $table;
                foreach ($schema->foreignKeys as $name => $fdata) {
                    $out[] = $this->addTab(-2) . '* --------- ' . $name . ' ---------';
                    $out[] = $this->addTab(-2) . '* Field: ' . $fdata['key'];
                    $out[] = $this->addTab(-2) . '* Reference table: ' . $fdata['ref'];
                    $out[] = $this->addTab(-2) . '* Reference field: ' . $fdata['id'];
                    $out[] = $this->addTab(-2) . '* Actions: On delete - ' . $fdata['delete'] . '; On update - ' . $fdata['update'];
                }

                $out[] = $this->addTab(-2) . '*/';
                $out[] = '';
            }

            $out = array_merge($out, ['use Eloquent;', 'use App\Traits\Model AS ModelTrait;', 'use App\Traits\DbTimezone;', /*'use Carbon\Carbon;',*/]);
            $out = array_merge($out, ['use Illuminate\Database\Query\Builder AS QueryBuilder;', 'use Illuminate\Database\Eloquent\Builder AS EloquentBuilder;']);

            $ret_use = 'Abstract' . $model_name;
            if (isset($extends['App\Models\Base\Abstract' . $model_name])) {
                $out[] = 'use ' . $extends['App\Models\Base\Abstract' . $model_name] . ' AS ' . ($ret_use = Arr::last(explode('\\', $extends['App\Models\Base\Abstract' . $model_name])) . 'Model') . ';';
            }

            if ($schema->hasColumn('active') || $schema->hasColumn('status')) {
                $out[] = 'use App\Helper\YesNo;';
            }

            if (in_array($table, $enable_object_to_clear_cache)) {
                $out[] = 'use App\Traits\Cacheable;';
            }

            if ($has_url_handle) {
                $out[] = 'use App\Traits\UrlHandle;';
                $out[] = 'use App\Models\Setting\UrlHandleHistory;';
            }

            if ($has_seo_fields) {
                $out[] = 'use App\Traits\SeoFields;';
                $out[] = 'use App\Contracts\SeoFieldContract;';
            }

            if (in_array($table, $autocomplete)) {
                $out[] = 'use App\Traits\AutoComplete;';
            }

            if ($schema->getColumn('geo_zone_id')) {
                $out[] = 'use App\Traits\GeoZone;';
            }

            if ($schema->getColumn('sandbox')) {
                $out[] = 'use App\Traits\SandBoxModel;';
                $out[] = 'use App\Contracts\SandBoxContract;';
            }

            if (in_array($table, $global_meta_data)) {
                $out[] = 'use App\Traits\ExternalMetaData;';
            }

            $out[] = '';
            $out[] = $this->addTab(-2) . '/**';
            //from db
            $existings = [];
            $casts = [];
            if (isset($extends['App\Models\Base\Abstract' . $model_name])) {
                $casts = \App::call($extends['App\Models\Base\Abstract' . $model_name] . '@getCasts');
            }

            foreach ($schema->columns as $column => $cd) {
                $type = $cd->type;
                if (isset($casts[$column])) {
                    $type = $casts[$column];
                }

                $out[] = $this->addTab(-2) . '* @property ' . Params::translateType($column, $type) . ' ' . $column . ($cd->comment ? ' (' . $cd->comment . ')' : '');
                $existings[] = $column;
            }

            if (isset($classes['App\Models\Base\Abstract' . $model_name])) {
                foreach ($classes['App\Models\Base\Abstract' . $model_name] as $m) {
                    if (is_array($m)) {
                        if (Arr::first($m) == '@getset@' && !in_array($m[1], $existings)) {
                            $out[] = $this->addTab(-2) . '* @property '.($m[2] ?: 'mixed|null|string') . ' ' . $m[1];
                            $existings[] = $m[1];
                        }
                    } elseif (!$schema->hasColumn($m) && !in_array($m, $existings)) {
                        $out[] = $this->addTab(-2) . '* @property mixed|null|string ' . $m;
                        $existings[] = $m;
                    }
                }
            }

            if (isset($extends['App\Models\Base\Abstract' . $model_name])) {
                $d = new getRelations($extends['App\Models\Base\Abstract' . $model_name]);
                foreach ($d->getProperties() as $name => $property) {
                    $name = '$' . $name;
                    if (array_key_exists($name, $schema->columns)) {
                        continue;
                    }

                    if ($property['read'] && $property['write']) {
                        $attr = 'property';
                    } elseif ($property['write']) {
                        $attr = 'property-write';
                    } else {
                        $attr = 'property-read';
                    }

                    if (config('ide-helper.model_camel_case_properties', false)) {
                        $name = \Illuminate\Support\Str::camel($name);
                    }

                    $out[] = $this->addTab(-2) . '* ' . trim(sprintf('@%s %s %s %s', $attr, $property['type'], $name, $property['comment']));
                    if ($property['multiple']) {
                        $out[] = $this->addTab(-2) . '* ' . trim(sprintf('@%s integer ', $attr) . \Illuminate\Support\Str::snake($name) . "_count");
                    }
                }

                try {
                    /** @var $class \Illuminate\Database\Eloquent\Model */
                    $class = app($extends['App\Models\Base\Abstract' . $model_name]);
                    if ($mutated = $class->getMutatedAttributes()) {
                        foreach ($mutated as $mut) {
                            if (!in_array($mut, $existings)) {
                                $out[] = $this->addTab(-2) . '* @property mixed|null|string ' . $mut;
                            }
                        }
                    }
                } catch (\Exception) {
                }

            }

            if ($has_url_handle && isset($traits[UrlHandle::class])) {
                $out[] = $this->addTab(-2) . '* **** from trait App\Traits\UrlHandle ****';
                if (isset($extends['App\Models\Base\Abstract' . $model_name])) {
                    $ret = '\\' . $extends['App\Models\Base\Abstract' . $model_name];
                } else {
                    $ret = 'Abstract' . $model_name;
                }

                foreach ($traits[UrlHandle::class] as $m) {
                    if (is_array($m) && !in_array($m[0], ['boot', 'urlHandle'])) {
                        $out[] = $this->addTab(-2) . '* @method ' . Params::traitFix($m[2], $ret) . ' ' . $m[0] . '(' . $m[1] . ')';
                    }
                }
            }

            if ($has_seo_fields && isset($traits[SeoFields::class])) {
                $out[] = $this->addTab(-2) . '* **** from trait App\Traits\SeoFields ****';
                if (isset($extends['App\Models\Base\Abstract' . $model_name])) {
                    $ret = '\\' . $extends['App\Models\Base\Abstract' . $model_name];
                } else {
                    $ret = 'Abstract' . $model_name;
                }

                foreach ($traits[SeoFields::class] as $m) {
                    if (is_array($m) && !in_array($m[0], ['boot'])) {
                        $out[] = $this->addTab(-2) . '* @method ' . Params::traitFix($m[2], $ret) . ' ' . $m[0] . '(' . $m[1] . ')';
                    }
                }
            }

            if (in_array($table, $autocomplete) && isset($traits[AutoComplete::class])) {
                $out[] = $this->addTab(-2) . '* **** from trait App\Traits\AutoComplete ****';
                if (isset($extends['App\Models\Base\Abstract' . $model_name])) {
                    $ret = '\\' . $extends['App\Models\Base\Abstract' . $model_name];
                } else {
                    $ret = 'Abstract' . $model_name;
                }

                foreach ($traits[AutoComplete::class] as $m) {
                    if (is_array($m) && !in_array($m[0], ['boot'])) {
                        $out[] = $this->addTab(-2) . '* @method ' . Params::traitFix($m[2], $ret) . ' ' . $m[0] . '(' . $m[1] . ')';
                    }
                }
            }

            $existing_models = [];
            foreach ($schema->columns as $column => $cd) {
                $existing_models[] = 'where' . ucfirst(Str::camel($column));
                $out[] = $this->addTab(-2) . '* @method static QueryBuilder|EloquentBuilder|' . $ret_use . ' where' . ucfirst(Str::camel($column)) . '($value)';
            }

            $last = 'method';
            foreach ($data as $method => $d) {
                if (trim($d['group']) != $last) {
                    $out[] = $this->addTab(-2) . '* ';
                }

                if (in_array($method, $existing_models)) {
                    continue;
                }

                if (in_array($method, ['find', 'first', 'value'])) {
                    $out[] = $this->addTab(-2) . '* @method static ' . $ret_use . ' ' . trim($d['value']);
                    if ($method == 'find') {
                        $out[] = $this->addTab(-2) . '* @method static ' . $ret_use . ' ' . preg_replace('~^find\(~', 'findOrFail(', $d['value']);
                        $out[] = $this->addTab(-2) . '* @method static ' . $ret_use . ' ' . preg_replace('~^find\(~', 'findOrNew(', $d['value']);
                    }

                    if ($method == 'first') {
                        $out[] = $this->addTab(-2) . '* @method static ' . $ret_use . ' ' . preg_replace('~^first\(~', 'firstOrFail(', $d['value']);
                    }
                } elseif (in_array($method, ['get'])) {
                    $out[] = $this->addTab(-2) . '* @method static \Illuminate\Support\Collection ' . trim($d['value']);
                } else {
                    if (trim($d['group']) == '@method static \Illuminate\Database\Query\Builder' && $ret_use != 'Abstract' . $model_name) {
                        $d['group'] = '@method static QueryBuilder|EloquentBuilder|' . $ret_use;
                    }

                    $out[] = $this->addTab(-2) . '* ' . str_replace(\Illuminate\Database\Eloquent\Model::class, $ret_use, trim($d['group'])) . ' ' . trim($d['type']) . ' ' . trim($d['value']);
                }

                if ($method == 'create') {
                    $out[] = $this->addTab(-2) . '* @method static ' . $ret_use . ' ' . preg_replace('~^create\(([^\)]*)\)~', 'firstOrNew(array $attributes, array $values = array())', $d['value']);
                    $out[] = $this->addTab(-2) . '* @method static ' . $ret_use . ' ' . preg_replace('~^create\(([^\)]*)\)~', 'firstOrCreate(array $attributes, array $values = array())', $d['value']);
                    $out[] = $this->addTab(-2) . '* @method static ' . $ret_use . ' ' . preg_replace('~^create\(([^\)]*)\)~', 'updateOrCreate(array $attributes, array $values = array())', $d['value']);
                }

                $last = trim($d['group']);
            }

            if (isset($classes['App\Models\Base\Abstract' . $model_name])) {
                foreach ($classes['App\Models\Base\Abstract' . $model_name] as $m) {
                    if (is_array($m) && Arr::first($m) != '@getset@') {
                        if (in_array($m[0], $existing_models)) {
                            continue;
                        }

                        if ($m[0] == 'getRelationList') {
                            $out[] = $this->addTab(-2) . '* @method static \Illuminate\Support\Collection|' . $ret_use . '[] ' . $m[0] . '(' . $m[1] . ')';
                        } else {
                            $out[] = $this->addTab(-2) . '* @method static QueryBuilder|EloquentBuilder|' . $ret_use . ' ' . $m[0] . '(' . $m[1] . ')';
                        }
                    }
                }
            }

            $implements = [];
            if ($has_seo_fields) {
                $implements[] = 'SeoFieldContract';
            }

            if ($schema->getColumn('sandbox')) {
                $implements[] = 'SandBoxContract';
            }

            $out[] = $this->addTab(-2) . '*';
            $out[] = $this->addTab(-2) . '* @category   Base Abstract Models';
            $out[] = $this->addTab(-2) . '* @package    App\Models\Base';
            $out[] = $this->addTab(-2) . '* <AUTHOR> <<EMAIL>>';
            //            $out[] = $this->addTab(-2) . '* @version    Release: ' . exec('git describe --tags --abbrev=0');
            $out[] = $this->addTab(-2) . '* @version    Release: ' . app()->version();
            $out[] = $this->addTab(-2) . '*/';
            $out[] = '';

            $out[] = 'abstract class Abstract' . $model_name . ' extends Eloquent' . ($implements ? ' implements ' . implode(', ', $implements) : '') . ' { ';
            $out[] = '';

            $use = ['ModelTrait', 'DbTimezone'];
            if (in_array($table, $global_meta_data)) {
                $use[] = 'ExternalMetaData';
            }

            if (in_array($table, $autocomplete)) {
                $use[] = 'AutoComplete';
            }

            if ($schema->getColumn('geo_zone_id')) {
                $use[] = 'GeoZone';
            }

            if ($schema->getColumn('sandbox')) {
                $use[] = 'SandBoxModel';
            }

            //enable object to clear cache
            if (in_array($table, $enable_object_to_clear_cache)) {
                $use[] = 'Cacheable';
            }

            if ($has_url_handle) {
                $use[] = 'UrlHandle';
            }

            if ($has_seo_fields) {
                $use[] = 'SeoFields';
            }

            if ($use) {
                $out[] = $this->addTab(1) . 'use ' . implode(', ', $use) . ';';
            }

            if ($has_url_handle) {
                $out[] = '';
                $out[] = $this->addTab(1) . 'protected $url_handle_field = \'' . $has_url_handle . "';";
                $out[] = '';
                if ($schema->hasColumn('name')) {
                    $out[] = $this->addTab(1) . 'protected $name_field = \'name\';';
                } elseif ($schema->hasColumn('title')) {
                    $out[] = $this->addTab(1) . 'protected $name_field = \'title\';';
                } elseif ($schema->hasColumn('value')) {
                    $out[] = $this->addTab(1) . 'protected $name_field = \'value\';';
                } elseif ($schema->hasColumn('tag')) {
                    $out[] = $this->addTab(1) . 'protected $name_field = \'tag\';';
                } else {
                    throw new Exception('Missing name_field for "UrlHandle" ' . $model_name . '!');
                }
            }

            if ($has_seo_fields && !in_array($model_name, ['SeoData'])) {
                $out[] = '';
                if (!$has_url_handle) {
                    if ($schema->hasColumn('name')) {
                        $out[] = $this->addTab(1) . 'protected $name_field = \'name\';';
                    } elseif ($schema->hasColumn('title')) {
                        $out[] = $this->addTab(1) . 'protected $name_field = \'title\';';
                    } else {
                        throw new Exception('Missing name_field for "SeoFields" ' . $model_name . '!');
                    }
                }

                if ($schema->hasColumn('description')) {
                    $out[] = $this->addTab(1) . 'protected $description_field = \'description\';';
                } elseif ($schema->hasColumn('content')) {
                    $out[] = $this->addTab(1) . 'protected $description_field = \'content\';';
                } elseif ($schema->hasColumn('name')) {
                    $out[] = $this->addTab(1) . 'protected $description_field = \'name\';';
                } elseif ($schema->hasColumn('title')) {
                    $out[] = $this->addTab(1) . 'protected $description_field = \'title\';';
                } else {
                    throw new Exception('Missing description_field for "SeoFields" ' . $model_name . '!');
                }

                $out[] = '';
                $out[] = $this->addTab(1) . 'protected $_seo_fields = [\'' . implode("', '", $has_seo_fields) . "'];";
            }

            $casts = [];
            foreach ($schema->columns as $column => $cd) {
                if ($cd->type === 'decimal') {
                    $casts[$column] = 'float';
                }
            }

            if (!empty($casts)) {
                $out[] = $this->addTab(1) . 'protected $casts = ' . var_export($casts, true) . ";";
            }

            $out[] = '';
            $out[] = $this->addTab(1) . 'protected $table = \'' . $table . "';";
            $out[] = '';
            $fillable = $this->addTab(1) . 'protected $fillable = [';
            $r = 0;

            $created_at = null;
            $updated_at = null;
            foreach ($schema->columns as $column => $cd) {
                if (in_array($column, ['date_added', 'created_at', 'date_created'])) {
                    $created_at = $column;
                    if ($updated_at) {
                        break;
                    }
                }

                if (in_array($column, ['date_modified', 'updated_at', 'last_edited', 'date_last_updated', 'date_last_update', 'date_last_update'])) {
                    $updated_at = $column;
                    if ($created_at) {
                        break;
                    }
                }
            }

            if ($updated_at && !$created_at) {
                $created_at = $updated_at;
            }

            $ret2 = null;
            if (isset($extends['App\Models\Base\Abstract' . $model_name])) {
                $ret2 = $extends['App\Models\Base\Abstract' . $model_name];
            }

            $guarded = [];
            foreach ($schema->columns as $column => $cd) {
                if ($cd->isPrimaryKey) {
                    continue;
                }

                if ($created_at && $updated_at && ($column == $created_at || $column == $updated_at)) {
                    continue;
                }

                if (isset($image_map[$ret2]) && $column == $image_map[$ret2]['image_field']) {
                    $guarded[] = $column;
                    continue;
                }

                if ($r) {
                    $fillable .= ', ';
                }

                if ($r % 3 === 0) {
                    $fillable .= "\n" . $this->addTab(2);
                }

                $fillable .= "'" . $column . "'";
                $r++;
            }

            if (isset($extends['App\Models\Base\Abstract' . $model_name])) {
                $fill_ret = $extends['App\Models\Base\Abstract' . $model_name];
                $appends_allow = [
                    CustomerShippingAddress::class,
                    CustomerBillingAddress::class,
                    ShippingAddress::class,
                    BillingAddress::class
                ];
                if (in_array($fill_ret, $appends_allow)) {
                    $appends = $this->accessProtected($obj = new $fill_ret(), 'appends');
                    foreach ($appends as $append) {
                        if (method_exists($obj, \Illuminate\Support\Str::camel(sprintf('set_%s_attribute', $append)))) {
                            if ($r) {
                                $fillable .= ', ';
                            }

                            if ($r % 3 === 0) {
                                $fillable .= "\n" . $this->addTab(2);
                            }

                            $fillable .= "'" . $append . "'";
                            $r++;
                        }
                    }
                }
            }

            $description = $ret2 . 'Description';
            if (class_exists($description)) {
                $reflection = new ReflectionClass($ret2);
                if (in_array(Translatable::class, $reflection->getTraitNames())) {
                    /** @var Model $model */
                    $model = new $description();
                    $fillables = $model->getFillable();
                    foreach ($fillables as $column) {
                        if ($r) {
                            $fillable .= ', ';
                        }

                        if ($r % 3 === 0) {
                            $fillable .= "\n" . $this->addTab(2);
                        }

                        $fillable .= "'" . $column . "'";
                        $r++;
                    }
                }
            }

            $out[] = $fillable;
            $out[] = $this->addTab(1) . '];';
            $primaryKey = null;
            if ($schema->primaryKey || $guarded) {
                $fillable = $this->addTab(1) . 'protected $guarded = [';
                $r = 0;
                foreach ($schema->primaryKey as $r => $column) {
                    if ($r) {
                        $fillable .= ', ';
                    }

                    if ($r % 3 === 0) {
                        $fillable .= "\n" . $this->addTab(2);
                    }

                    $fillable .= "'" . $column . "'";
                    $r++;
                    if ($column != 'id') {
                        $primaryKey = $column;
                    }
                }

                foreach ($guarded as $g) {
                    if ($r) {
                        $fillable .= ', ';
                    }

                    if ($r % 3 === 0) {
                        $fillable .= "\n" . $this->addTab(2);
                    }

                    $fillable .= "'" . $g . "'";
                    $r++;
                }

                $out[] = $fillable;
                $out[] = $this->addTab(1) . '];';
                $out[] = ' ';
            }

            if ($primaryKey) {
                $out[] = '';
                $out[] = $this->addTab(1) . 'protected $primaryKey = \'' . $primaryKey . "';";
                $out[] = '';
            }

            if (!$created_at || !$updated_at) {
                $out[] = '';
                $out[] = $this->addTab(1) . 'public $timestamps = false;';
                $out[] = '';
                $created_at = null;
                $updated_at = null;
            } else {
                $space = false;
                if ($created_at != 'created_at') {
                    $out[] = '';
                    $out[] = $this->addTab(1) . "const CREATED_AT = '" . $created_at . "';";
                    $space = true;
                }

                if ($updated_at != 'updated_at') {
                    $out[] = '';
                    $out[] = $this->addTab(1) . "const UPDATED_AT = '" . $updated_at . "';";
                    $space = true;
                }

                if ($space) {
                    $out[] = '';
                }
            }

            $dates = [];
            foreach ($schema->columns as $column => $cd) {
                if ((in_array($cd->type, ['datetime', 'date', 'time', 'timestamp']) && !in_array($column, [$created_at, $updated_at])) || in_array($column, ['date_locking', 'date_sent'])) {
                    $dates[] = $column;
                }
            }

            if ($dates) {
                $fillable = $this->addTab(1) . 'protected $dates = [';
                $r = 0;
                foreach ($dates as $column) {
                    if ($r) {
                        $fillable .= ', ';
                    }

                    if ($r % 3 === 0) {
                        $fillable .= "\n" . $this->addTab(2);
                    }

                    $fillable .= "'" . $column . "'";
                    $r++;
                }

                $out[] = $fillable;
                $out[] = $this->addTab(1) . '];';
            }

            $out[] = '';
            if ($fulltextSearch) {
                $out[] = $this->addTab(1) . 'public $_fulltext_search = [';
                foreach ($fulltextSearch as $group => $cols) {
                    $out[] = $this->addTab(2) . "'" . $group . "' => [";
                    $out[] = $this->addTab(3) . "'" . implode("','", $cols) . "'";
                    $out[] = $this->addTab(2) . '],';
                }

                $out[] = $this->addTab(1) . '];';
            }

            if ($schema->hasColumn('active') || $schema->hasColumn('status')) {
                $out[] = $this->addTab(1) . 'public $_status_column = "' . ($schema->hasColumn('status') ? 'status' : 'active') . '";';
                $out[] = '';
                $out[] = $this->addTab(1) . 'protected $_has_publish_date = ' . ($schema->hasColumn('publish_date') ? 'true' : 'false') . ';';
                $out[] = '';
            }

            if ($bootTrait) {
                $out[] = '';
                $out[] = $this->addTab(1) . '/**';
                $out[] = $this->addTab(1) . '* The "booting" method of the model.';
                $out[] = $this->addTab(1) . '*';
                $out[] = $this->addTab(1) . '* @return void';
                $out[] = $this->addTab(1) . '*/';
                $out[] = $this->addTab(1) . 'public static function boot()';
                $out[] = $this->addTab(1) . '{';
                $out[] = $this->addTab(2) . 'parent::boot();';
                foreach ($bootTrait as $bt) {
                    $out[] = $this->addTab(2) . 'static::' . $bt . '();';
                }

                $out[] = $this->addTab(1) . '}';
            }

            //    if ($all_dates) {
            //        $out[] = '';
            //        foreach ($all_dates as $group => $keys) {
            //            foreach ($keys as $key) {
            //                $out[] = '  protected function get' . \Illuminate\Support\Str::studly($key) . 'Attribute($value) {';
            //                $out[] = '      if(!$value) { return $value; }';
            //                $out[] = '      if (preg_match(\'/^(\d{4})-(\d{1,2})-(\d{1,2})$/\', $value)) {';
            //                $out[] = '          $date = Carbon::createFromFormat(\'Y-m-d\', $value, \'UTC\')->startOfDay();';
            //                $out[] = '      } else {';
            //                $out[] = '          $date = Carbon::createFromFormat($this->getDateFormat(), $value, \'UTC\');';
            //                $out[] = '      }';
            //                $out[] = '      return $date->setTimezone(date_default_timezone_get());';
            //                $out[] = '  }';
            //                $out[] = '';
            //                $out[] = '  protected function set' . \Illuminate\Support\Str::studly($key) . 'Attribute($value) {';
            //                $out[] = '      if(!$value) { return $this->attributes[\'' . $key . '\'] = $value; }';
            //                $out[] = '      if(is_string($value)) {';
            //                $out[] = '          if (preg_match(\'/^(\d{4})-(\d{1,2})-(\d{1,2})$/\', $value)) {';
            //                $out[] = '              $value = Carbon::createFromFormat(\'Y-m-d\', $value, \'UTC\')->startOfDay();';
            //                $out[] = '          } else {';
            //                $out[] = '              $value = Carbon::createFromFormat($this->getDateFormat(), $value, \'UTC\');';
            //                $out[] = '          }';
            //                $out[] = '      }';
            //                $out[] = '      $value = $this->asDateTime($value);';
            //                $out[] = '      if(strtolower($value->getTimezone()->getName()) == \'utc\') {';
            //                $out[] = '          return $this->attributes[\'' . $key . '\'] = $value->format($this->getDateFormat());';
            //                $out[] = '      }';
            //                $out[] = '      return $this->attributes[\'' . $key . '\'] = $value->setTimezone(\'UTC\')->format($this->getDateFormat());';
            //                $out[] = '  }';
            //                $out[] = '';
            //            }
            //        }
            //    }

            if ($has_url_handle) {
                $out[] = '';
                $out[] = $this->addTab(1) . '/**';
                $out[] = $this->addTab(1) . '* @return \Illuminate\Database\Eloquent\Relations\MorphMany|\App\Models\Setting\UrlHandleHistory';
                $out[] = $this->addTab(1) . '*/';
                $out[] = $this->addTab(1) . 'public function url_history()';
                $out[] = $this->addTab(1) . '{';
                $out[] = $this->addTab(2) . 'return $this->morphMany(UrlHandleHistory::class, \'url_history\', \'type\', \'model_id\', $this->getKeyName());';
                $out[] = $this->addTab(1) . '}';
                $out[] = '';
            }

            if ($schema->getColumn('sandbox')) {
                $out[] = '';
                $out[] = $this->addTab(1) . '/**';
                $out[] = $this->addTab(1) . '* {@inheritdoc}';
                $out[] = $this->addTab(1) . '*/';
                $out[] = $this->addTab(1) . 'public function getSandBoxFieldName()';
                $out[] = $this->addTab(1) . '{';
                $out[] = $this->addTab(2) . "return 'sandbox';";
                $out[] = $this->addTab(1) . '}';
                $out[] = '';
            }

            //            if ($schema->hasColumn('active') || $schema->hasColumn('status')) {
            //                $out[] = $this->addTab(1) . '/**';
            //                $out[] = $this->addTab(1) . '* Function update a single or multiple model/s active property.';
            //                $out[] = $this->addTab(1) . '* Depending on whether or not the parameter $id is an array, the function could update multiple articles';
            //                $out[] = $this->addTab(1) . '*';
            //                $out[] = $this->addTab(1) . '* @param integer|array $id     The id of the article. Can be an array containing multiple article ids';
            //                $out[] = $this->addTab(1) . '* @param boolean       $active The active state of the model/s. If true, function marks an article as active and if false, as inactive';
            //                $out[] = $this->addTab(1) . '*';
            //                $out[] = $this->addTab(1) . '* @return int';
            //                $out[] = $this->addTab(1) . '*/';
            //                $out[] = $this->addTab(1) . 'public static function changeActivity($id, $active)';
            //                $out[] = $this->addTab(1) . '{';
            //                if ($schema->hasColumn('publish_date')) {
            //                    $out[] = $this->addTab(2) . '$update[\'publish_date\'] = null;';
            //                    $out[] = $this->addTab(2) . '$update = [\'' . ($schema->hasColumn('status') ? 'status' : 'active') . '\' => YesNo::parse($active), \'publish_date\' => null];';
            //                } else {
            //                    $out[] = $this->addTab(2) . '$update = [\'' . ($schema->hasColumn('status') ? 'status' : 'active') . '\' => YesNo::parse($active)];';
            //                }
            //                $out[] = $this->addTab(2) . '/** @var ' . $ret_use . ' $instance */';
            //                $out[] = $this->addTab(2) . '$instance = new static();';
            //                $out[] = $this->addTab(2) . '$return = $instance->whereIn(\'id\', (array)$id)->update($update);';
            //                $out[] = $this->addTab(2) . '$instance->fireModelEvent(\'saved\', false);';
            //                $out[] = $this->addTab(2) . 'return $return;';
            //                $out[] = $this->addTab(1) . '}';
            //            }

            $out[] = '';
            $out[] = '}';
            $out[] = '';

            $old_content = null;
            if (is_file($file = app_path('Models/Base/Abstract' . $model_name . '.php'))) {
                $old_content = file_get_contents($file);
            }

            $content = implode("\n", $out);

            if (empty($old_content)) {
                file_put_contents(base_path($path = 'app/Models/Base/Abstract' . $model_name . '.php'), $content);
                $this->info('Created Abstract' . $model_name);
            } elseif (str_replace(["\n", "\r"], '', $old_content) != str_replace(["\n", "\r"], '', $content)) {
                file_put_contents(base_path($path = 'app/Models/Base/Abstract' . $model_name . '.php'), $content);
                $this->info('Generated Abstract' . $model_name);
            }
        }

        //        $this->info('run command: ' . ($com = base_path('vendor/bin/phpcbf')) . ' -p --encoding=utf-8 ' . ($path = app_path('Models/Base/')));
        //
        //        $process = new Process($com . ' -p --encoding=utf-8 ' . $path);
        //        $process->run(function() {
        //            var_dump(func_get_args());
        //        });

        $this->info('Completed');
    }

    /**
     * @param integer $e
     * @return string
     */
    protected function addTab($e): string
    {
        $t = '';
        if ($e >= 0) {
            for ($i = 0; $i < $e; $i++) {
                $t .= "\t";
            }
        } else {
            for ($i = 0; $i < abs($e); $i++) {
                $t .= " ";
            }
        }

        return $t;
    }

    /**
     * @param mixed $obj
     * @param mixed $prop
     * @return mixed
     */
    protected function accessProtected($obj, $prop): mixed
    {
        $reflection = new ReflectionClass($obj);
        $property = $reflection->getProperty($prop);
        $property->setAccessible(true);
        return $property->getValue($obj);
    }
}
