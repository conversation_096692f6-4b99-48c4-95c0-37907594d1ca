<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 3.6.2016 г.
 * Time: 15:27 ч.
 */

namespace App\Commands\Db\Generate\Generator;

/**
 * Class ForeignKeys
 *
 * @package App\Commands\Db\Generate\Generator
 */
class ForeignKeys
{
    /**
     * ForeignKeys constructor.
     *
     * @param $foreignKeys
     */
    public function __construct(
        /**
         * @var
         */
        protected $foreignKeys
    ) {
    }

    /**
     * @return string
     */
    public function create(): string
    {
        $return = '';
        foreach ($this->foreignKeys as $fk) {
            $return .= "        if (Schema::hasTable('" . $fk['table'] . "') && Schema::hasTable('" . $fk['ref'] . "') && Schema::hasColumn('" . $fk['table'] . "','" . $fk['key'] . "') && Schema::hasColumn('" . $fk['ref'] . "','" . $fk['id'] . "')) {" . "\n";
            $return .= "            Schema::table('" . $fk['table'] . '\', function(Blueprint $table) {' . "\n";
            $return .= "                if(!MetaData::hasForeignKey('" . $fk['table'] . "', '" . $fk['name'] . "')) { " . "\n";
            $return .= '                    if(app()->runningInConsole()) {'."\n";
            $return .= '                        //printf("\e[32m". __CLASS__ . ": creating foreign key\e[0m %s\n", \'' . $fk['name'] . "');" . "\n";
            $return .= '                    }'."\n";
            $return .= '                    $table->foreign(\'' . $fk['key'] . "','" . $fk['name'] . "')->references('" . $fk['id'] . "')->on('" . $fk['ref'] . "')->onDelete('" . $fk['delete'] . "')->onUpdate('" . $fk['update'] . "');" . "\n";
            $return .= '                }' . "\n";
            $return .= '            });' . "\n";
            $return .= '        }' . "\n\n";
        }

        return $return;
    }

    /**
     * @return string
     */
    public function drop(): string
    {
        $return = '';
        foreach ($this->foreignKeys as $fk) {
            $return .= "     if (Schema::hasTable('" . $fk['table'] . "')) {" . "\n";
            $return .= "            Schema::table('" . $fk['table'] . '\', function(Blueprint $table) {' . "\n";
            $return .= "                if (MetaData::hasForeignKey('" . $fk['table'] . "', '" . $fk['name'] . "')) { " . "\n";
            $return .= '                    $table->dropForeign(\'' . $fk['name'] . "');" . "\n";
            $return .= '                }' . "\n";
            $return .= '            });' . "\n";
            $return .= '     }' . "\n\n";
        }

        return $return;
    }

}
