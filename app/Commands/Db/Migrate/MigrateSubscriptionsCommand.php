<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 7.3.2019 г.
 * Time: 14:49
 */

namespace App\Commands\Db\Migrate;

use App\Commands\TimeInfoCommand;
use App\Models\Gate\PlanDetails;
use App\Models\Gate\SiteSubscription;
use App\Models\Gate\Transaction;
use Braintree\Subscription;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Database\Capsule\Manager;
use Illuminate\Database\Connection;

/**
 * Class MigrateSubscriptionsCommand
 * @package App\Commands\Db\Migrate
 * @deprecated
 */
class MigrateSubscriptionsCommand extends TimeInfoCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:subscriptions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Migrate braintree's subscriptions to site subscriptions";

    /**
     * @var Connection|Manager
     */
    protected $db;

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        $this->db = \Illuminate\Support\Facades\DB::connection('gate');

        $records = Subscription::search([
            \Braintree\SubscriptionSearch::status()->in([
                Subscription::ACTIVE,
                Subscription::PAST_DUE,
            ])
        ]);

        $this->info('Found ' . $records->maximumCount() . ' active subscriptions');
        $this->migrate($records);

        $records = Subscription::search([
            \Braintree\SubscriptionSearch::status()->is(Subscription::CANCELED),
            \Braintree\SubscriptionSearch::nextBillingDate()->greaterThanOrEqualTo(\Carbon\Carbon::now())
        ]);

        $this->info('Found ' . $records->maximumCount() . ' canceled but not expired subscriptions');
        $this->migrate($records);
    }

    /**
     * @param $records
     * @throws \Exception
     */
    protected function migrate($records)
    {
        foreach ($records as $record) {
            $this->info('Migrating ' . $record->status . ' subscription ' . $record->id);
            $transaction = Transaction::where('subscription_id', $record->id)
                ->orderByDesc('id')
                ->first();

            if ($transaction && $transaction->site_id) {
                $planSubscription = SiteSubscription::owner($transaction->site_id)
                    ->where('model_type', 'plan_details')
                    ->first();

                if ($planSubscription) {
                    $this->warn('Site #' . $transaction->site_id . ' already has a plan subscription #' . $planSubscription->unique_id);
                    continue;
                }

                $planDetails = PlanDetails::findByMap($record->planId);
                if (!$planDetails) {
                    $this->error('No plan details for mapping ' . $record->planId);
                    continue;
                }

                $status = match ($record->status) {
                    Subscription::ACTIVE => SiteSubscription::ACTIVE,
                    Subscription::PAST_DUE => SiteSubscription::PAST_DUE,
                    default => SiteSubscription::CANCELED,
                };

                try {
                    $subscription = SiteSubscription::firstOrCreate([
                        'unique_id' => $record->id,
                        'user_id' => $transaction->user_id,
                        'site_id' => $transaction->site_id,
                        'model_id' => $planDetails->getKey(),
                        'model_type' => $planDetails->getMorphClass(),
                        'mapping' => $planDetails->plan->mapping,
                        'price' => $record->price * 100,
                        'currency' => $record->transactions[0]->currencyIsoCode,
                        'next_billing_amount' => $record->nextBillingPeriodAmount * 100,
                        'next_billing_date' => Carbon::createFromTimestamp($record->nextBillingDate->getTimestamp()),
                        'billing_cycle' => $planDetails->billing_cycle,
                        'status' => $status,
                        'value' => $planDetails->mapping ?: $planDetails->plan->mapping,
                    ]);

                    $this->db->table('site_subscriptions')
                        ->where('id', $subscription->getKey())
                        ->update([
                            'created_at' => $record->createdAt->format('Y-m-d H:i:s'),
                            'updated_at' => $record->updatedAt->format('Y-m-d H:i:s'),
                        ]);

                } catch (\Exception $e) {
                    if (Str::contains($e->getMessage(), 'unique_id')) {
                        $this->warn('Subscription #' . $record->id . ' already exists');
                        continue;
                    }

                    throw $e;
                }

                if ($subscription->wasRecentlyCreated) {
                    $this->info('Created Subscription #' . $subscription->unique_id . ' for site #' . $subscription->site_id);
                } else {
                    $this->warn('Subscription #' . $subscription->unique_id . ' already exists');
                }

                $this->cancel($record);
            } else {
                $this->warn('No transactions found for subscription #' . $record->id);
                $this->cancel($record);
            }
        }
    }

    /**
     * @param mixed $record
     * @return mixed
     */
    protected function cancel($record)
    {
        if ($record->status != Subscription::CANCELED) {
            Subscription::cancel($record->id);
            $this->info('Subscription #' . $record->id . ' is now canceled');
        }
    }
}
