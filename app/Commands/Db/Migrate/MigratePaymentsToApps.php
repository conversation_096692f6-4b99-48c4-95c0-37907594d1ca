<?php

declare(strict_types=1);

namespace App\Commands\Db\Migrate;

use App\Commands\CommandSafeMode;
use App\Models\Apps\Applications;
use App\Models\Gateway\PaymentProviders;
use Exception;

/**
 * Class MigratePaymentsToApps
 * @package App\Commands\Db\Migrate
 */
class MigratePaymentsToApps extends CommandSafeMode
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = 'migrate:payments-to-apps';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Copy payment methods from payments to apps db';

    /**
     * Execute the console command.
     *
     * @return void
     * @throws Exception
     */
    #[\Override]
    public function handle(): void
    {
        parent::handle();

        $providers = PaymentProviders::active()->get();

        foreach ($providers as $provider) {
            /** @var PaymentProviders $provider */
            if (!Applications::where('key', $provider->map)->first(['key'])) {
                $app = new Applications([
                    'key' => $provider->map,
                    'group' => 'payment',
                    'category_id' => 13,
                    'currency' => 'EUR',
                    'icon_filename' => sprintf('payment/%s.png', $provider->map),
                ]);
                $app->fill([
                    'en' => [
                        'name' => $provider->name,
                        'short_description' => $provider->name,
                        'description' => $provider->name,
                    ]
                ]);

                //                if ('bwt' == $app->key) {
                $app->save();
                $this->line($app->name . ' added to apps db');
                //                }
            }
        }

        $this->info('Done!');
    }

}
