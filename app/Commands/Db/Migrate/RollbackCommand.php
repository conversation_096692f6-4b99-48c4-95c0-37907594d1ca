<?php

declare(strict_types=1);

namespace App\Commands\Db\Migrate;

use App\Console\Traits\TargetSites;
use Illuminate\Database\Console\Migrations\RollbackCommand as BaseRollbackCommand;

class RollbackCommand extends BaseRollbackCommand
{
    use TargetSites;

    /**
     * Execute the console command.
     *
     * @return void
     */
    #[\Override]
    public function handle(): void
    {
        $this->exec(function (): void {
            if ($this->pathCompareBase()) {
                $this->error(sprintf('Warning! Unable to rollback protected path "%s" database!', $this->_fixPath(\Illuminate\Support\Arr::first($this->getMigrationPaths()))));
                return;
            }

            parent::handle();
        });
    }

    protected function pathCompareBase(): bool
    {
        return $this->_fixPath(\Illuminate\Support\Arr::first($this->getMigrationPaths())) == $this->_fixPath(database_path('/migrations/'));
    }

    /**
     * @param mixed $path
     * @return mixed
     */
    protected function _fixPath($path): ?string
    {
        return preg_replace('([\/]{2,})', '/', rtrim(str_replace('\\', '/', $path), '/'));
    }
}
