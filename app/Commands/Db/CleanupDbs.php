<?php

declare(strict_types=1);

namespace App\Commands\Db;

use App\Commands\CommandSafeMode;
use App\Helper\Cache\RouterCache;
use App\Helper\DataBase\DynamicConnection;
use App\Helper\Slack;
use App\Models\Router\Database;
use App\Models\Router\Exceptions;
use App\Models\Router\Site;
use Illuminate\Database\Capsule\Manager;
use Illuminate\Database\Connection;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Symfony\Component\Console\Input\InputOption;
use App\GlobalServiceProviders\DatabaseSetMaxStatementTimeServiceProvider;

/**
 * Class MakeMysqlUsers
 *
 * @package App\Commands\Db
 */
class CleanupDbs extends CommandSafeMode
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:dbs';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleanup DB servers (destroy unused sites dbs)';

    /**
     * @var Connection|Manager
     */
    protected $db;

    protected $destroyedCount = 0;

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Exception
     */
    #[\Override]
    public function handle(): void
    {
        parent::handle();

        if (inDevelopment()) {
            $this->error('Development is currently not supported by this command. Review/refactor the code.');
            return;
        }

        $databases = Database::active()->get();

        foreach ($databases as $db) {
            $start_time = round(microtime(true), 3);
            $this->destroyedCount = 0;
            $serverDbs = $this->getServerDbs($db);
            $serverSites = $this->getDbServerSites($db);
            $this->info('Cleaning up DB' . $db->id);
            $this->line('Total site DBs on DB' . $db->id . ': ' . count($serverDbs));

            foreach ($serverDbs as $site_id) {
                if (!$serverSites->has($site_id)) {
                    try {
                        $this->destroy($site_id);
                    } catch (\Throwable $e) {
                        Exceptions::createFromThrowable($e);
                        $this->error($e->getMessage());
                    }
                }
            }

            RouterCache::invalidateSites();
            $this->line('Total destroyed DBs on DB' . $db->id . ': ' . $this->destroyedCount);
            $this->info('Done!');

            if (!$this->inSafeMode()) {
                $total_time = round(microtime(true) - $start_time, 3);
                Slack::sendWebHook(
                    "
                    *" . gethostname() . "*:
                    Job: CleanupDbs
                    Server: DB{$db->id}
                    Total destroyed dbs: {$this->destroyedCount}
                    Total time: {$total_time} sec",
                    'cc-system'
                );
            }
        }
    }

    /**
     * @param Database $db
     * @return array
     */
    protected function getServerDbs(Database $db): array
    {
        $routerConfig = ['database' => 'information_schema'] + config('database.connections.router', []);
        $dbConfig = Arr::only($db->toArray(), array_keys($routerConfig)) + $routerConfig;
        DynamicConnection::reconnect($dbConfig);
        $this->db = \Illuminate\Support\Facades\DB::connection('dynamic');

        return $this->loadServerDatabaseIds();
    }

    /**
     * @return array
     */
    protected function loadServerDatabaseIds(): array
    {
        $this->db->statement('SET SESSION max_statement_time=' . DatabaseSetMaxStatementTimeServiceProvider::MAX_STATEMENT_TIME_CLI);

        $ids = [];
        $databases = $this->db->select("SHOW DATABASES LIKE 'cc_site_%'");

        foreach ($databases as $db) {
            $ids[] = str_replace('cc_site_', '', $db->{"Database (cc_site_%)"});
        }

        $this->db->statement('SET SESSION max_statement_time=' . DatabaseSetMaxStatementTimeServiceProvider::MAX_STATEMENT_TIME);

        return $ids;
    }

    /**
     * @param Database $db
     * @return Collection
     */
    protected function getDbServerSites(Database $db)
    {
        return Site::where('database_id', $db->id)->get(['site_id'])->keyBy('site_id');
    }

    /**
     * @param int $site_id
     *
     * @throws \Exception
     */
    protected function destroy($site_id)
    {
        if (!$this->inSafeMode()) {
            $this->db->statement('DROP DATABASE cc_site_' . $site_id);
            $this->comment(sprintf('Destroyed database (cc_site_%d) for Site #%d.', $site_id, $site_id));
            RouterCache::invalidateDomain($site_id);
            RouterCache::invalidateSite($site_id);
        } else {
            $this->comment(sprintf('When --execute option is provided, database cc_site_%d will be destroyed.', $site_id));
        }

        $this->destroyedCount++;
    }

    /**
     * Get the console command options.
     *
     * @return array
     */
    protected function getOptions()
    {
        return [
            ['execute', null, InputOption::VALUE_NONE, 'Destroy unused databases.'],
        ];
    }
}
