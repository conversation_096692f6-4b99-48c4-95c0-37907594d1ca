<?php

declare(strict_types=1);

namespace App\Traits\Catalog\Product;

use App\Common\Theme;
use App\Helper\Plan;
use App\Helper\Temp\ProductTemp;
use App\Helper\YesNo;
use App\Integration\Custom\Facades\CustomIntegration;
use App\Models\Product\Category;
use App\Models\Product\Product;
use App\Models\Product\ProductMeta;
use App\Models\Product\Variant;
use App\Models\Queue\SiteQueue;
use Auth;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Modules\Apps\Administration\StoreLocations\StoreLocationsManager;
use Modules\CustomIntegrations\Facades\CustomIntegrationModule;
use Modules\SiteIntegrations\ListingColors\ListingColorsManager;
use Modules\SiteIntegrations\ListingImages\ListingImagesManager;
use Modules\SiteIntegrations\ListingVariants\ListingVariantsManager;
use Throwable;

trait StoreFrontListing
{
    protected static $_task_execute = [];

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return mixed
     */
    public function scopeListingNew(Builder $query): void
    {
        $tmpTableName = static::getTempTable() ?: static::getYesterdayTempTable();

        $query->listingNewLite();

        $scopes = array_merge(
            Theme::getWithDefault('eloquent.products.box.scope', []),
            Theme::getWithDefault('eloquent.products.list.scope', [])
        );
        foreach ($scopes as $scope) {
            if ($tmpTableName && $scope == 'quantity') {
                continue;
            }

            $query->{$scope}();
        }

        $callbacks = array_merge(
            Theme::getWithDefault('eloquent.theme.products.box', []),
            Theme::getWithDefault('eloquent.theme.products.list', [])
        );

        foreach ($callbacks as $callback) {
            call_user_func($callback, $query);
        }


        if (CustomIntegration::supportProductBoxExtend()) {
            CustomIntegration::productBoxExtend($query);
        }

        if (CustomIntegrationModule::supportProductBoxExtend()) {
            CustomIntegrationModule::productBoxExtend($query);
        }

        if (CustomIntegration::supportProductListingExtend()) {
            CustomIntegration::productListingExtend($query);
        }

        if (CustomIntegrationModule::supportProductListingExtend()) {
            CustomIntegrationModule::productListingExtend($query);
        }

        ListingColorsManager::productListingExtend($query);
        ListingImagesManager::productListingExtend($query);
        ListingVariantsManager::productListingExtend($query);

        $with = array_merge(
            Theme::getWithDefault('eloquent.products.list.with', []),
            Theme::getWithDefault('eloquent.products.box.with', []),
            [
                'variant' => function ($query): void {
                    $query->withoutGlobalScopes();
                },
                'labels',
                'banners',
                'product_to_discount' => function ($query): void {
                    $query->with(['target', 'discount']);
                },
            ],
            [
                'image',
                'vendor',
                'category' => function ($query): void {
                    /** @var Category $query */
                    $query->withExists('properties as properties_count')
                        ->with('path');
                },
                'meta_data' => function ($query): void {
                    /** @var ProductMeta $query */
                    $query->whereIn('parameter', static::ALLOWED_META_LISTING);
                },
            ],
            !app()->runningInConsole() && Auth::customerId() && Auth::customer()->favorites_count ? ['favorite'] : []
        );

        $query->with($with);
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return mixed
     */
    public function scopeListingNewLite(Builder $query): void
    {
        $tmpTableName = static::getTempTable($customer_group_id);

        $query->newQuery()->withoutGlobalScopes()->select('*');

        $aliasTableName = $this->getTableName($query);
        if ($tmpTableName) {
            $query->from($tmpTableName);
            $query->getModel()->setTable($tmpTableName);
        } elseif ($tmpTableName = static::getYesterdayTempTable($customer_group_id)) {
            $query->from($tmpTableName);
            $query->getModel()->setTable($tmpTableName);

            static::executeTaskListTemp($customer_group_id);
        } else {
            $sql = 'SELECT
                NULL AS is_discounted,
                NULL AS product_to_discount_id,
                NULL AS product_to_discount_discount_id,
                NULL AS product_to_discount_variant_id,
                NULL AS product_to_discount_product_id,
                NULL AS product_to_discount_price,
                NULL AS product_to_discount_price_to,
                NULL AS min_price_with_discounted,
                NULL AS product_id,
                NULL AS max_price_with_discounted';

            $query->joinSub($sql, '__no_tmp_table__', '__no_tmp_table__.product_id', '=', $aliasTableName . '.id', 'left');

            static::executeTaskListTemp($customer_group_id);
        }

        $instanceTableName = $tmpTableName ?: $aliasTableName;

        $query->where([
            $instanceTableName . '.active' => YesNo::True,
            $instanceTableName . '.is_hidden' => 0,
            $instanceTableName . '.draft' => YesNo::False,
        ]);

        if ($instanceTableName != 'products' && Plan::enabled('variants.listing')) {
            $query->whereIn('parameter_type', ['simple','variant']);
        }

        $query->where(function (Builder $query) use ($instanceTableName): void {
            $query->whereNull($instanceTableName . '.active_to')
                ->orWhere($instanceTableName . '.active_to', '>=', Carbon::now(site('timezone'))->tz('UTC')->startOfMinute());
        })->where(function (Builder $query) use ($instanceTableName): void {
            $query->whereNull($instanceTableName . '.publish_date')
                ->orWhere($instanceTableName . '.publish_date', '<=', Carbon::now(site('timezone'))->tz('UTC')->endOfMinute());
        });

        if (!setting('show_out_of_stock_products', 1)) {
            if ($tmpTableName) {
                $query->where('has_enable_sell', 1);
            } else {
                $query->where(function ($query) use ($aliasTableName): void {
                    /** @var Product $query */
                    $query->where(function ($query) use ($aliasTableName): void {
                        /** @var Product $query */
                        $query->where($aliasTableName . '.tracking', YesNo::True)
                            ->where($aliasTableName . '.continue_selling', YesNo::False)
                            ->whereHas('variants', function ($variant): void {
                                /** @var Variant $variant */
                                $variant->whereNotNull('price')->whereNotNull('quantity')
                                    ->where('quantity', '>', 0)->withoutGlobalScopes();
                            });
                    })->orWhere($aliasTableName . '.tracking', YesNo::False)
                        ->orWhere($aliasTableName . '.continue_selling', YesNo::True);
                });
            }
        }

        if (!$tmpTableName) {
            $query->quantity();
        }
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return mixed
     */
    public function scopeSelectionExists(Builder $query): void
    {
        $tmpTableName = static::getTempTable($customer_group_id);

        $query->newQuery()->withoutGlobalScopes()->select('*');

        $aliasTableName = $this->getTableName($query);
        if ($tmpTableName) {
            $query->from($tmpTableName);
            $query->getModel()->setTable($tmpTableName);
        } elseif ($tmpTableName = static::getYesterdayTempTable($customer_group_id)) {
            $query->from($tmpTableName);
            $query->getModel()->setTable($tmpTableName);

            static::executeTaskListTemp($customer_group_id);
        } else {
            $sql = 'SELECT
                NULL AS is_discounted,
                NULL AS product_to_discount_id,
                NULL AS product_to_discount_discount_id,
                NULL AS product_to_discount_variant_id,
                NULL AS product_to_discount_product_id,
                NULL AS product_to_discount_price,
                NULL AS product_to_discount_price_to,
                NULL AS min_price_with_discounted,
                NULL AS product_id,
                NULL AS max_price_with_discounted';

            $query->joinSub($sql, '__no_tmp_table__', '__no_tmp_table__.product_id', '=', $aliasTableName . '.id', 'left');

            static::executeTaskListTemp($customer_group_id);
        }

        $instanceTableName = $tmpTableName ?: $aliasTableName;

        $query->where([
            $instanceTableName . '.active' => YesNo::True,
            $instanceTableName . '.draft' => YesNo::False,
        ]);

        if ($instanceTableName != 'products' && Plan::enabled('variants.listing')) {
            $query->whereIn('parameter_type', ['simple','variant']);
        }

        if (app_namespace() != 'sitecp') {
            $query->where([
                $instanceTableName . '.is_hidden' => 0,
            ]);
        }

        $query->where(function (Builder $query) use ($instanceTableName): void {
            $query->whereNull($instanceTableName . '.active_to')
                ->orWhere($instanceTableName . '.active_to', '>=', Carbon::now(site('timezone'))->tz('UTC')->startOfMinute());
        })->where(function (Builder $query) use ($instanceTableName): void {
            $query->whereNull($instanceTableName . '.publish_date')
                ->orWhere($instanceTableName . '.publish_date', '<=', Carbon::now(site('timezone'))->tz('UTC')->endOfMinute());
        });

        if (!setting('show_out_of_stock_products', 1) && /*app_namespace() != 'sitecp'*/ app_namespace() == 'site') {
            if ($tmpTableName) {
                $query->where('has_enable_sell', 1);
            } else {
                $query->where(function ($query) use ($aliasTableName): void {
                    /** @var Product $query */
                    $query->where(function ($query) use ($aliasTableName): void {
                        /** @var Product $query */
                        $query->where($aliasTableName . '.tracking', YesNo::True)
                            ->where($aliasTableName . '.continue_selling', YesNo::False)
                            ->whereHas('variants', function ($variant): void {
                                /** @var Variant $variant */
                                $variant->whereNotNull('price')->whereNotNull('quantity')
                                    ->where('quantity', '>', 0)->withoutGlobalScopes();
                            });
                    })->orWhere($aliasTableName . '.tracking', YesNo::False)
                        ->orWhere($aliasTableName . '.continue_selling', YesNo::True);
                });
            }
        }

        if (!$tmpTableName) {
            $query->quantity();
        }
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return mixed
     */
    public function scopeXmlFeedNew(Builder $query): void
    {
        $tmpTableName = static::getTempTable($customer_group_id);

        $query->newQuery()->withoutGlobalScopes()->select('*');

        $aliasTableName = $this->getTableName($query);
        if ($tmpTableName) {
            $query->from($tmpTableName);
            $query->getModel()->setTable($tmpTableName);
        } elseif ($tmpTableName = static::getYesterdayTempTable($customer_group_id)) {
            $query->from($tmpTableName);
            $query->getModel()->setTable($tmpTableName);

            static::executeTaskListTemp($customer_group_id);
        } else {
            $sql = 'SELECT
                NULL AS is_discounted,
                NULL AS product_to_discount_id,
                NULL AS product_to_discount_discount_id,
                NULL AS product_to_discount_variant_id,
                NULL AS product_to_discount_product_id,
                NULL AS product_to_discount_price,
                NULL AS product_to_discount_price_to,
                NULL AS min_price_with_discounted,
                NULL AS product_id,
                NULL AS max_price_with_discounted';

            $query->joinSub($sql, '__no_tmp_table__', '__no_tmp_table__.product_id', '=', $aliasTableName . '.id', 'left');

            static::executeTaskListTemp($customer_group_id);
        }

        $instanceTableName = $tmpTableName ?: $aliasTableName;

        $query->where([
            $instanceTableName . '.active' => YesNo::True,
            $instanceTableName . '.draft' => YesNo::False,
        ]);

        $query->where(function (Builder $query) use ($instanceTableName): void {
            $query->whereNull($instanceTableName . '.active_to')
                ->orWhere($instanceTableName . '.active_to', '>=', Carbon::now(site('timezone'))->tz('UTC')->startOfMinute());
        })->where(function (Builder $query) use ($instanceTableName): void {
            $query->whereNull($instanceTableName . '.publish_date')
                ->orWhere($instanceTableName . '.publish_date', '<=', Carbon::now(site('timezone'))->tz('UTC')->endOfMinute());
        });

        $query->whereHas('variant', function ($q): void {
            $q->withoutGlobalScopes();
        });

        if (!$tmpTableName) {
            $query->quantity();
        }

        $query->with([
            'image', 'vendor', 'variant.detailed_discount.discount', 'category',
            'product_to_discount' => function ($q): void {
                $q->with(['discount', 'target']);
            }
        ]);
    }

    /**
     * @param Builder $query
     * @param string $direction
     */
    public function scopeOrderByOutOfStock(Builder $query, string $direction = 'desc'): void
    {
        $direction = $this->validateOrderDirection($direction);

        $table = $this->getTableName($query);
        if (!$this->isTempTable($table)) {
            $query->orderByRaw('IF(' . $table . ".tracking = 'no' OR " . $table . ".continue_selling = 'yes' OR (" . $table . ".tracking = 'yes' AND " . $table . ".continue_selling = 'no' AND (SELECT COALESCE(SUM(quantity),0) FROM products_variants WHERE item_id = " . $table . '.id AND price IS NOT NULL LIMIT 1) > 0), 1, 0) ' . $direction);
        } else {
            $query->orderBy($table . '.has_enable_sell', $direction);
        }
    }

    /**
     * @param Builder $query
     * @param string $direction
     */
    public function scopeOrderByMinPrice(Builder $query, string $direction = 'asc'): void
    {
        $direction = $this->validateOrderDirection($direction);

        $table = $this->getTableName($query);
        if (!$this->isTempTable($table)) {
            $query->orderBy($table . '.price_from', $direction);
        } else {
            if ($direction == 'asc') {
                $query->orderBy('order_min_price_with_discounted_asc_id_asc', 'asc');
            } else {
                $query->orderBy('order_min_price_with_discounted_desc_id_asc', 'asc');
            }
        }
    }

    /**
     * @param Builder $query
     * @param string $direction
     */
    public function scopeOrderByMaxPrice(Builder $query, string $direction = 'desc'): void
    {
        $direction = $this->validateOrderDirection($direction);

        $table = $this->getTableName($query);
        if (!$this->isTempTable($table)) {
            $query->orderBy($table . '.price_from', $direction);
        } else {
            $query->orderBy('max_price_with_discounted', $direction);
        }
    }

    /**
     * @param $query
     * @param string $direction
     */
    public function scopeOrderBySale($query, string $direction = 'desc'): void
    {
        $direction = $this->validateOrderDirection($direction);

        $table = $this->getTableName($query);
        if (!$this->isTempTable($table)) {
            $query->orderBy($table . '.id', $direction);//->orderByMinPrice();
        } else {
            //$query->orderBy('is_discounted', $direction);
            $query->orderBy('order_is_discounted_desc_min_price_with_discounted_asc', 'asc');
        }
    }

    /**
     * @param Builder|Product $query
     */
    public function scopeOrderByFeaturedAndMinPrice($query): void
    {
        $table = $this->getTableName($query);
        if (!$this->isTempTable($table)) {
            $query->orderBy(\Illuminate\Support\Facades\DB::raw(sprintf('-%s', $table . '.featured')), 'asc')->orderByMinPrice();
        } else {
            $query->orderBy($table . '.order_featured_desc_min_price_with_discounted_asc', 'asc');
        }
    }

    /**
     * @param Builder|Product $query
     * @param string $direction
     */
    public function scopeOrderByName($query, string $direction = 'desc'): void
    {
        $direction = $this->validateOrderDirection($direction);

        $table = $this->getTableName($query);

        $query->orderBy($table . '.name', $direction);
    }

    /**
     * @param Builder|Product $query
     * @param string $direction
     */
    public function scopeOrderByDate($query, string $direction = 'desc'): void
    {
        $direction = $this->validateOrderDirection($direction);

        $table = $this->getTableName($query);

        $query->orderBy($table . '.id', $direction);
    }

    /**
     * @param Builder|Product $query
     * @param string $direction
     */
    public function scopeOrderBySortOrder($query, string $direction = 'desc'): void
    {
        $direction = $this->validateOrderDirection($direction);

        $table = $this->getTableName($query);
        if (!$this->isTempTable($table)) {
            $query->orderBy($table . '.sort_order', $direction)
                ->orderBy($table . '.featured', 'desc')
                ->orderBy($table . '.id', 'asc');
        } else {
            if ($direction == 'asc') {
                $query->orderBy('order_sort_order_asc_featured_desc_id_asc', 'asc');
            } else {
                $query->orderBy('order_sort_order_desc_featured_desc_id_asc', 'asc');
            }
        }
    }

    /**
     * @param int|null $customer_group_id
     * @return bool
     */
    public static function getTempTable(?int &$customer_group_id = null)
    {
        if (is_null($customer_group_id)) {
            $customer_group_id = Auth::customerId() ? Auth::customer()->group_id : Auth::getGuestsGroupId();
        }

        $hasZone = \Apps::installed('store_locations') ? StoreLocationsManager::getCookieData() : null;

        return ProductTemp::hasTempTable($customer_group_id, $hasZone->geo_zone_id ?? null);
    }

    /**
     * @param int|null $customer_group_id
     * @return bool
     */
    public static function getYesterdayTempTable(?int &$customer_group_id = null)
    {
        if (is_null($customer_group_id)) {
            $customer_group_id = Auth::customerId() ? Auth::customer()->group_id : Auth::getGuestsGroupId();
        }

        $hasZone = \Apps::installed('store_locations') ? StoreLocationsManager::getCookieData() : null;

        return ProductTemp::hasTempTable($customer_group_id, $hasZone->geo_zone_id ?? null, 1);
    }

    /**
     * @param $direction
     * @param string $default
     * @return bool
     */
    protected function validateOrderDirection($direction, string $default = 'desc')
    {
        return in_array($direction = Str::lower($direction), ['asc', 'desc']) ? $direction : $default;
    }

    /**
     * @param Builder $query
     * @return string
     */
    protected function getTableName(Builder $query)
    {
        $tableName = $query->getModel()->getTable();
        if (method_exists($query, 'getTableAlias')) {
            $tableName = $query->getTableAlias();
        }

        return $tableName;
    }

    /**
     * @param string $tableName
     * @return bool
     */
    protected function isTempTable(string $tableName)
    {
        return Str::startsWith($tableName, 'products_lists_temp_');
    }

    /**
     * @param mixed $customer_group_id
     * @return mixed
     */
    private static function executeTaskListTemp($customer_group_id): void
    {
        try {
            $key = ProductTemp::getTempTable($customer_group_id);
            if (empty(static::$_task_execute[$key])) {
                $jobKey = md5(sprintf('%d_create_products_lists_tables_by_group_%s', site('site_id'), $key));
                $hasZone = \Apps::installed('store_locations') ? StoreLocationsManager::getCookieData() : null;
                static::$_task_execute[$key] = SiteQueue::executeQueueTask('create_products_lists_tables_by_group', ['group_id' => $customer_group_id, 'geo_zone_id' => $hasZone->geo_zone_id ?? null], $jobKey, 5);
            }
        } catch (Throwable) {
            //
        }
    }

}
