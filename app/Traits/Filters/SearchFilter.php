<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 5.7.2018 г.
 * Time: 17:00 ч.
 */

namespace App\Traits\Filters;

use App\Helper\Text;
use Illuminate\Database\Eloquent\Builder;

trait SearchFilter
{
    /**
     * @param $query
     */
    public function filterQuery($query): void
    {
        if (empty($query)) {
            return;
        }

        $words = Text::getKeywords($query);
        if (empty($words)) {
            return;
        }

        $this->setWhere('query', function ($query) use ($words): void {
            foreach ($words as $word) {
                /** @var Builder $query */
                $column = $this->getNameColumn();
                if (is_array($column)) {
                    $query->where(function ($query) use ($column, $word): void {
                        /** @var Builder $query */
                        foreach ($column as $columnKey => $columnName) {
                            if (is_numeric($columnKey)) {
                                $query->orWhere($columnName, 'like', sprintf('%%%s%%', $word));
                            } else {
                                $query->orWhere($columnKey, $columnName, $word);
                            }
                        }
                    });
                } else {
                    $query->where($column, 'like', sprintf('%%%s%%', $word));
                }
            }
        });

        if ($this->_return_string) {
            $this->_filters['query'] = $query;
        }
    }

    /**
     * @return string|array
     */
    protected function getNameColumn(): string|array
    {
        return !empty($this->name_column) ? $this->name_column : 'name';
    }
}
