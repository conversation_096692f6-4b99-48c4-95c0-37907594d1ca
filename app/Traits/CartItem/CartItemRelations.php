<?php

declare(strict_types=1);

namespace App\Traits\CartItem;

use App\Models\Marketing\CrossSell\CrossSell;
use App\Models\Marketing\UpSell\UpSell;
use App\Models\Product\Product;
use App\Models\Store\Cart;
use App\Models\Store\CartBundle;
use App\Models\Store\CartBundleItem;
use App\Models\Store\CartItemCrossSell;
use App\Models\Store\CartItemOption;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait CartItemRelations
{
    /**
     * @return BelongsTo|Cart
     */
    public function cart()
    {
        return $this->belongsTo(Cart::class);
    }

    /**
     * @return HasOne|CrossSell
     */
    public function cross_sell()
    {
        return $this->hasOne(CartItemCrossSell::class, 'cart_item_id', 'id')
            ->where('type', 'action');
    }

    /**
     * @return HasOne|CrossSell
     */
    public function cross_sell_item_target()
    {
        return $this->hasOne(CartItemCrossSell::class, 'cart_item_id', 'id')
            ->where('type', 'target');
    }

    /**
     * @return HasOne|CartBundle
     */
    public function cart_bundle()
    {
        return $this->hasOne(CartBundle::class, 'id', 'bundle_id');
    }

    /**
     * @return HasOne|CartBundleItem
     */
    public function cart_bundle_item()
    {
        return $this->hasOne(CartBundleItem::class, 'id', 'cart_item_id');
    }

    /**
     * @return HasOne|UpSell
     */
    public function up_sell()
    {
        return $this->hasOne(UpSell::class, 'trigger_variant_id', 'up_sell_trigger_variant_id')
            ->active();
    }

    /**
     * @return BelongsTo|CartItemOption
     */
    public function options()
    {
        return $this->hasMany(CartItemOption::class, 'item_id', 'id')
            ->with(['field', 'field_value'])
            ->orderBy('id', 'asc');
    }

    /**
     * This relation is used for segments only
     * @return HasOne|Product
     */
    public function simple_product()
    {
        return $this->hasOne(Product::class, 'id', 'product_id')
            ->withoutGlobalScopes();
    }

}
