<?php

declare(strict_types=1);

namespace App\Traits\CartItem\Attributes;

use App\Models\Product\Product;
use App\Models\Product\Variant;

/**
 * @property array $data
 * @property null|Variant $up_sell_trigger_variant
 * @property null|Product $up_sell_trigger_product
 * @property null|array $up_sell_for_create
 */
trait UpSellAttributes
{
    /**
     * Initialize trait and append getters
     */
    protected function initializeUpSellAttributes()
    {
        $this->append([
            'up_sell_trigger_variant', 'up_sell_trigger_product', 'up_sell_for_create'
        ]);
    }

    /**
     * @return null|\App\Models\Product\Variant
     */
    public function getUpSellTriggerVariantAttribute()
    {
        if (empty($this->data['up_sell_trigger_variant']) && $this->up_sell_trigger_variant_id && !empty($this->up_sell)) {
            $this->data['up_sell_trigger_variant'] = $this->up_sell->format()->trigger_variant ?? null;
        }

        return $this->data['up_sell_trigger_variant'] ?? null;
    }

    /**
     * @return null|\App\Models\Product\Product
     */
    public function getUpSellTriggerProductAttribute()
    {
        if (empty($this->data['up_sell_trigger_product']) && $this->up_sell_trigger_variant_id && !empty($this->up_sell)) {
            $this->data['up_sell_trigger_product'] = $this->up_sell->format()->trigger_product ?? null;
        }

        return $this->data['up_sell_trigger_product'] ?? null;
    }

    /**
     * @return string
     */
    public function getUpSellForCreateAttribute()
    {
        if (empty($this->data['up_sell_for_create']) && $this->up_sell_trigger_variant_id && !empty($this->up_sell_trigger_product->id)) {
            $this->data['up_sell_for_create'] = [
                'trigger_product_id' => $this->up_sell_trigger_product->id,
                'trigger_product_name' => $this->up_sell_trigger_product->name,
                'trigger_product_price' => $this->up_sell_trigger_variant->discounted ? $this->up_sell_trigger_variant->price_discounted : $this->up_sell_trigger_variant->price,
                'trigger_variant_id' => $this->up_sell_trigger_variant_id,
                'trigger_variant_p1' => $this->up_sell_trigger_product->p1,
                'trigger_variant_p2' => $this->up_sell_trigger_product->p2,
                'trigger_variant_p3' => $this->up_sell_trigger_product->p3,
                'trigger_variant_v1' => $this->up_sell_trigger_variant->v1,
                'trigger_variant_v2' => $this->up_sell_trigger_variant->v2,
                'trigger_variant_v3' => $this->up_sell_trigger_variant->v3,
                'offer_product_id' => $this->product_id,
                'offer_product_name' => $this->name,
                'offer_product_price' => ($price = is_null($this->discount_price) ? ($this->price + $this->options_price) : $this->discount_price),
                'offer_variant_id' => $this->variant_id,
                'offer_variant_p1' => $this->p1,
                'offer_variant_p2' => $this->p2,
                'offer_variant_p3' => $this->p3,
                'offer_variant_v1' => $this->v1,
                'offer_variant_v2' => $this->v2,
                'offer_variant_v3' => $this->v3,
                'total' => $this->up_sell->total > 0 ? $this->up_sell->total : 0,
                'only_additional_cost' => $this->up_sell->only_additional_cost,
                'name' => $this->up_sell->name,
                'offer_title' => $this->up_sell->offer_title,
                'up_sell_id' => $this->up_sell->id,
            ];
        }

        return $this->data['up_sell_for_create'] ?? null;
    }

}
