<?php

declare(strict_types=1);

namespace App\Traits\CartItem\Attributes;

use App\Helper\Store\Contracts\Concerns\Discount\DiscountContract;

/**
 * @property array $data
 * @property null|DiscountContract $discount
 */
trait DiscountAttributes
{
    /**
     * Initialize trait and append getters
     */
    protected function initializeDiscountAttributes()
    {
        $this->append(['discount']);
    }

    /**
     * @return null|DiscountContract
     */
    public function getDiscountAttribute(): ?DiscountContract
    {
        return $this->data['discount'] ?? null;
    }

    /**
     * @param $discount
     * @return $this
     */
    public function setDiscountAttribute($discount): self
    {
        $this->data['discount'] = $discount;

        return $this;
    }

}
