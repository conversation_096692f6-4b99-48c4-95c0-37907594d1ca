<?php

declare(strict_types=1);

namespace App\Traits;

use Illuminate\Database\Eloquent\Model as EloquentModel;
use Illuminate\Support\Collection;

/**
 * Self Nested trait is designed to contain the functionality for objects that are logically linked into hierarchical,
 * nested structure consisting of parent and children on each level. This trait should only contain generic definitions.
 * @name SelfNested
 * @namespace traits
 * @package Builder
 * @subpackage Traits
 */
trait SelfNested
{
    protected static $selfNestedCache = [];

    /**
     * @param mixed $id
     * @param mixed $parentColumn
     * @return mixed
     */
    public static function getParentIdsWithoutRecursion($id, $parentColumn = 'parent'): \Illuminate\Support\Collection
    {
        if (isset(static::$selfNestedCache[$id]) && static::$selfNestedCache[$id] instanceof Collection) {
            return static::$selfNestedCache[$id];
        }

        if (!(new static()) instanceof EloquentModel) {
            return new Collection();
        }

        $result = [];

        $child = static::find($id);

        if (!$child) {
            return new Collection();
        }

        while (isset($child->{$parentColumn}) && $child->{$parentColumn}) {
            $child = $child->{$parentColumn};
            $result[] = $child->id;
        }

        $result = new Collection($result);

        static::$selfNestedCache[$id] = $result;

        return $result;
    }

}
