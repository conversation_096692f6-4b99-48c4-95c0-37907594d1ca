<?php

declare(strict_types=1);

namespace App\Traits\Cart;

use App\Models\Store\Cart;

trait CartOnDeleting
{
    public static function bootCartOnDeleting(): void
    {
        static::deleting(function (Cart $cart): void {
            if (!$cart->deleted_at) {
                if (!app()->runningInConsole() && !activeRoute('cart.add apps.fast_order.save_order')) {
                    static::storeCartKey(null);
                }

                return;
            }

            //delete bundles
            $cart->cart_bundles->map(function ($cp): void {
                $cp->delete();
            });

            //delete shipping quotes
            $cart->shipping_quotes()->delete();

            //delete meta
            $cart->meta()->delete();

            //delete items
            $cart->items->map(function ($item): void {
                $item->delete();
            });

            //delete cross sells
            $cart->cross_sell->map(function ($crossSell): void {
                $crossSell->delete();
            });
        });
    }

}
