<?php

declare(strict_types=1);

namespace App\Traits\Cart\GetSet;

use App\Helper\YesNo;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Store\CartItem;
use Illuminate\Support\Collection;

trait CartProductsQuery
{
    /**
     * @return Collection
     */
    protected function getProductsFromItemsQuery(): Collection
    {
        if ($this->items->isEmpty()) {
            return collect();
        }

        $variants = $this->___getVariants();
        if ($variants->isEmpty()) {
            return collect();
        }

        $products = $this->___getProducts($variants);
        if ($products->isEmpty()) {
            return collect();
        }

        $showOutOfStockProducts = setting('show_out_of_stock_products', '1') == '1';
        $items = $this->items->map(function (CartItem $item) use ($products, $variants, $showOutOfStockProducts) {
            if (!$products->has($item->product_id) || !$variants->has($item->variant_id)) {
                return;
            }

            /** @var Product $product */
            $product = $products->get($item->product_id);
            /** @var Variant $variants */
            $variant = $variants->get($item->variant_id);
            $product->default_variant_id = $item->variant_id;
            $product->setRelation('pivot', $item);
            $product->setRelation('variant', $variant);
            $product->syncOriginal();

            $item->product = $product;
            $item->variant = $variant;

            if (!$showOutOfStockProducts) {
                if (
                    $product->tracking == YesNo::True &&
                    $product->continue_selling == YesNo::False &&
                    (
                        $product->variant->quantity <= 0 ||
                        is_null($product->variant->price)
                    )
                ) {
                    return;
                }
            }

            return $product;
        })->filter();

        dd($products);
    }

    /**
     * @return Collection
     */
    protected function ___getVariants(): Collection
    {
        if ($this->items->isEmpty()) {
            return collect();
        }

        return Variant::withoutGlobalScopes()
            ->whereIn('id', $this->items->pluck('variant_id'))->get()
            ->keyBy('id');
    }

    /**
     * @param Collection $variants
     * @return Collection
     */
    protected function ___getProducts(Collection $variants): Collection
    {
        if ($this->items->isEmpty()) {
            return collect();
        }

        return Product::withoutGlobalScopes()->where('active', YesNo::True)
            ->where('draft', YesNo::False)
            ->whereIn('id', $variants->pluck('item_id'))->get()
            ->keyBy('id');
    }

}
