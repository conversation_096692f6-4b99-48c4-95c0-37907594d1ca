<?php

declare(strict_types=1);

namespace App\Traits\Order\Write;

use App\Models\Order\OrdersModification;

trait WriteModifications
{
    protected function writeModifications()
    {
        $this->order->modifications->map(function (OrdersModification $modification): \App\Models\Order\OrdersModification {
            $modification->order_id = $this->order->id;
            $modification->save();
            return $modification;
        });

        //$this->order->load('modifications.modification');
    }
}
