<?php

declare(strict_types=1);

namespace App\Traits\Order;

use App\Helper\Store\Contracts\Concerns\TaxContract;
use App\Helper\Store\OrderModelPopulate;
use App\Helper\YesNo;
use App\Models\Order\OrderTax;
use Illuminate\Support\Collection;

trait SetVatRelation
{
    /**
     * @param \App\Helper\Store\Contracts\Concerns\TaxContract|null $tax
     * @return mixed
     */
    public function setVat(?TaxContract $tax): OrderModelPopulate
    {
        $this->vat = $tax;

        if ($tax === null) {
            $this->order->fill([
                'without_vat_reasons' => null,
            ]);
            return $this;
        }

        $this->order->fill([
            'without_vat_reasons' => $tax->getWithoutVatReason(),
        ]);

        if (
            !($orderTaxModel = $this->order->taxes->first(fn(OrderTax $orderTax): bool => $orderTax->tax_id == $tax->getId() && is_null($orderTax->product_id)))
        ) {
            $orderTaxModel = new OrderTax([
                'tax_id' => $tax->getId(),
                'tax_name' => $tax->getName(),
                'tax_description' => $tax->getDescription(),
                'tax_target' => $tax->getTarget(),
                'tax_geo_zone_id' => $tax->getGeoZoneId(),
                'tax_price_with_vat' => $tax->isPriceWithVat(),
                'tax_type' => $tax->getType(),
                'tax_shipping' => $tax->isBeforeShipping() ? YesNo::False : YesNo::True,
                'tax_tax' => $tax->getTaxValue(),
                'tax_vat' => $tax->isVat() ? YesNo::True : YesNo::False,
                'order_amount' => $tax->getAmount(),
                'override_id' => $tax->getOverrideId(),
                'oss_registration' => $tax->isOssRegistration(),
            ]);
        }

        $this->order->taxes->push($orderTaxModel);

        return $this;
    }

    /**
     * @param Illuminate\Support\Collection $taxes
     * @return mixed
     */
    public function setTaxes(Collection $taxes): OrderModelPopulate
    {
        $taxes->each(function (TaxContract $tax): void {
            $this->order->taxes->push(new OrderTax([
                'tax_id' => $tax->getId(),
                'tax_name' => $tax->getName(),
                'tax_description' => $tax->getDescription(),
                'tax_target' => $tax->getTarget(),
                'tax_geo_zone_id' => $tax->getGeoZoneId(),
                'tax_price_with_vat' => $tax->isPriceWithVat(),
                'tax_type' => $tax->getType(),
                'tax_shipping' => $tax->isBeforeShipping() ? YesNo::False : YesNo::True,
                'tax_tax' => $tax->getTaxValue(),
                'tax_vat' => $tax->isVat() ? YesNo::True : YesNo::False,
                'order_amount' => $tax->getAmount(),
                'override_id' => $tax->getOverrideId(),
                'oss_registration' => $tax->isOssRegistration(),
                'payment_provider' => $tax->getPaymentProvider(),
                'shipping_provider' => $tax->getShippingProvider(),
            ]));
        });

        return $this;
    }

    /**
     * @return null|TaxContract
     */
    public function getVat(): ?TaxContract
    {
        return $this->vat;
    }
}
