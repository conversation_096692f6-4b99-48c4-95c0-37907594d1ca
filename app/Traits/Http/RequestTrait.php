<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 25.1.2019 г.
 * Time: 17:33 ч.
 */

namespace App\Traits\Http;

trait RequestTrait
{
    /**
     * @param $spec
     * @param null $value
     * @return array|\Illuminate\Http\Request|string
     */
    public function inputSet($spec, $value = null)
    {
        $input = $this->getInputSource()->all();
        if (is_array($spec)) {
            foreach ($spec as $key => $value) {
                \Illuminate\Support\Arr::set($input, $key, $value);
            }
        } else {
            \Illuminate\Support\Arr::set($input, $spec, $value);
        }

        $this->getInputSource()->replace($input);

        return $this;
    }

}
