<?php

declare(strict_types=1);

namespace App\Traits\Widgets;

use App\Helper\Scripts;

trait JsAfterLoad
{
    /**
     * @return string
     * @throws \Exception
     */
    public function getHtml()
    {
        $code = $this->getCode();
        preg_match_all('/<script([^>]*)>(.*)<\/script>/imUs', (string) $code, $match);

        if ($match[0]) {
            foreach ($match[0] as $script) {
                if (strpos($script, 'cc-embedded-form') || strpos($script, 'document.write')) {
                    continue;
                }

                $code = str_replace($script, '', $code);
                Scripts::instance()->addScript($script, [], Scripts::POS_LOAD);
            }
        }

        return $code;
    }
}
