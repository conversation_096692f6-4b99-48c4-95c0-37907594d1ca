<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 18.10.2016 г.
 * Time: 11:56 ч.
 */

namespace App\Traits;

use App\Contracts\SeoFieldContract;
use App\Models\Page\Page;
use App\Exceptions\Error;
use Illuminate\Support\Str;
use Request;

trait SeoFields
{
    protected static $_seo_title_max_chars = 255;

    protected static $_seo_description_max_chars = 2000;

    /**
     * Boot trait
     *
     * @return void
     */
    public static function bootSeoFields(): void
    {
        static::saving(function (SeoFieldContract $item): void {
            if ($item->hasSeoField('title')) {
                $seo_title = static::populateSeoField($item, 'title');

                if ($seo_title && mb_strlen($seo_title) > static::$_seo_title_max_chars) {
                    throw new Error(
                        sprintf(__('seo.err.seo_title_max_chars_%1$s'), static::$_seo_title_max_chars),
                        'seo_title'
                    );
                }

                $item->seo_title = $seo_title;
            }

            if ($item->hasSeoField('description')) {
                if ($item instanceof Page && ($item->type == 'builder' || @json_decode((string) $item->content))) {
                    $seo_description = static::populateSeoField($item, 'title', 'description');
                } else {
                    $seo_description = static::populateSeoField($item, 'description');
                }

                if ($seo_description && mb_strlen($seo_description) > static::$_seo_description_max_chars) {
                    throw new Error(sprintf(
                        __('seo.err.seo_description_max_chars_%1$s'),
                        static::$_seo_description_max_chars
                    ), 'seo_description');
                }

                $item->seo_description = $seo_description;
            }
        });
    }

    /**
     * @param $name
     *
     * @return bool
     */
    public function hasSeoField(string $name): bool
    {
        return in_array('seo_' . $name, isset($this->_seo_fields) ? (array)$this->_seo_fields : []);
    }

    /**
     * @return string
     */
    public function getSeoNameField(): string
    {
        return $this->name_field ?? 'name';
    }

    /**
     * @return string
     */
    public function getSeoDescriptionField(): string
    {
        return $this->description_field ?? 'description';
    }

    /**
     * @param SeoFieldContract $item
     * @param      $name
     * @param bool $hack
     *
     * @return mixed
     */
    protected static function populateSeoField(SeoFieldContract $item, $name, $hack = false): ?string
    {
        $field = $name == 'title' ? 'seo_title' : 'seo_description';
        if (trim((string) $post = Request::input($hack ? ($hack == 'title' ? 'seo_title' : 'seo_description') : $field))) {
            return static::escapeHtml($post);
        }

        if ($item->{$field}) {
            return static::escapeHtml($item->{$field});
        }

        //        if ($item->exists && $item->{$field}) {
        //            return $item->{$field};
        //        }

        return trim(Str::limit(
            static::escapeHtml($item->{$name == 'title' ? $item->getSeoNameField() : $item->getSeoDescriptionField()}),
            static::${'_seo_' . ($hack ?: $name) . '_max_chars'},
            ''
        ));
    }

    /**
     * @param $html
     * @return mixed
     */
    protected static function escapeHtml($html): ?string
    {
        $html = str_replace(["\r\n", "\n\r", "\n", "\r", "\t"], ' ', html_entity_decode((string) $html, ENT_QUOTES, 'utf-8'));
        return preg_replace('~([\s]{2,})~', ' ', strip_tags((string) $html));
    }

    public function getSeoDescriptionAttribute(): ?string
    {
        if (!empty($this->attributes['seo_description'])) {
            return static::escapeHtml($this->attributes['seo_description']);
        }

        return null;
    }

}
