<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 28.2.2018 г.
 * Time: 13:11 ч.
 */

namespace App\Traits;

use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Request;

trait ResponseType
{
    /**
     * @param $response
     * @return string
     */
    public function getResponseType($response): string
    {
        $type = 'html';
        if ($response instanceof Response) {
            if (Str::contains($response->headers->get('content-type'), 'xml')) {
                $type = 'xml';
            } elseif (Str::contains($response->headers->get('content-type'), 'json')) {
                $type = 'json';
            } elseif (Str::contains($response->headers->get('content-type'), 'javascript')) {
                $type = 'javascript';
            } elseif (Str::contains($response->headers->get('content-type'), 'csv')) {
                $type = 'csv';
            }
        }

        return $type;
    }

    /**
     * @param mixed $request
     * @param mixed $response
     * @return mixed
     */
    public function allowModifyHtmlResponse($request, $response): bool
    {
        return str_contains($response->headers->get('Content-Type', 'text/html'), 'html')
            && !$this->isJsonRequest($request, $response)
            && $response->getContent() !== false
            && in_array($request->getRequestFormat(), [null, 'html'], true);
    }

    /**
     * @param Request $request
     * @param  \Symfony\Component\HttpFoundation\Response $response
     * @return bool
     */
    protected function isJsonRequest(Request $request, \Symfony\Component\HttpFoundation\Response $response): bool
    {
        // If XmlHttpRequest, Live or HTMX, return true
        if (
            $request->isXmlHttpRequest() ||
            $request->headers->has('X-Livewire') ||
            ($request->headers->has('Hx-Request') && $request->headers->has('Hx-Target'))
        ) {
            return true;
        }

        // Check if the request wants Json
        $acceptable = $request->getAcceptableContentTypes();
        if (isset($acceptable[0]) && in_array($acceptable[0], ['application/json', 'application/javascript'], true)) {
            return true;
        }

        // Check if content looks like JSON without actually validating
        $content = $response->getContent();
        if (is_string($content) && strlen($content) > 0 && in_array($content[0], ['{', '['], true)) {
            return true;
        }

        return false;
    }

}
