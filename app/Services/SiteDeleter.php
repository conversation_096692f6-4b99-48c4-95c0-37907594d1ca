<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Gate\UsersSites;
use App\Models\Router\Certificate;
use App\Models\Router\DeletedCertificate;
use App\Models\Router\Host;
use App\Models\Router\Site;
use Exception;
use Google\Cloud\Storage\StorageObject;
use League\Flysystem\Adapter\Ftp;
use League\Flysystem\Filesystem;
use Schema;
use Illuminate\Support\Facades\Storage;
use Google\Cloud\Core\Exception\NotFoundException;
use Superbalist\Flysystem\GoogleStorage\GoogleStorageAdapter;

/**
 * Class SiteDeleter
 * @package App\Services
 */
class SiteDeleter extends Deleter
{
    /**
     * @return \Illuminate\Database\Eloquent\Model|null|static
     */
    protected function getResource()
    {
        return \Illuminate\Support\Facades\DB::connection('gate')->table('users_sites')->where('id', $this->id)->first();
    }

    /**
     *
     */
    protected function deleteInStorage()
    {
        $this->warn('Deleting storage for site #' . $this->id);
        //        $this->deleteGoogleCloudStorage($this->id);

        /** @var Filesystem|Ftp $disk */
        $disk = Storage::disk(Storage::getDefaultDriver());
        try {
            $disk->deleteDirectory(strval($this->id));
        } catch (\ErrorException $errorException) {
            $this->warn(sprintf('ERROR: Removing storage for site #%d: ', $this->id) . $errorException->getMessage());
        }
    }

    /**
     * @param string $site_id
     * @return mixed
     */
    protected function deleteGoogleCloudStorage(string $site_id)
    {
        $filesRemoved = 0;
        $limit = 1000;
        /** @var Filesystem $disk */
        $disk = Storage::disk(Storage::getDefaultDriver());

        $hasFiles = true;
        while ($hasFiles) {
            $hasFiles = false;
            /** @var GoogleStorageAdapter $adapter */
            $adapter = $disk->getAdapter();
            $prefix = $adapter->getPathPrefix() . $site_id . '/';

            $objects = $adapter->getBucket()->objects([
//                'prefix' => $site_id . 'not-found',
                'prefix' => $prefix,
                'fields' => 'items/name',
                'resultLimit' => $limit
            ]);

            foreach ($objects as $object) {
                $hasFiles = true;
                /** @var StorageObject $object */
                try {
                    $object->delete();
                    $this->info('Destroyed: ' . $object->name());
                } catch (NotFoundException) {
                    //
                }

                $filesRemoved++;
            }
        }

        $this->warn('Destroyed ' . $filesRemoved . ' files');
    }

    /**
     *
     */
    protected function deleteInDatabases()
    {
        $this->info('Deleting site #' . $this->id);

        $site = Site::findOrFail($this->id);
        $site->connect();

        $this->deleteInPayment();
        $this->deleteClientDatabase();
        $this->deleteInRouter();
        $this->deleteInGate();
        //        $this->deleteInCache();
    }

    /**
     * @deprecated
     */
    protected function deleteInCache()
    {
        $this->warn('Removing records from CACHE');

        if (($connectionName = config('cache.stores.' . config('cache.default') . '.connection', 'mongodb-cache'))) {
            $schema = Schema::connection($connectionName);
            $db = $schema->getConnection();
            $db->enableQueryLog();
            $schema->dropIfExists('cc_site_cache_' . $this->id);

            $this->log($db);
        }

        $this->info('Done!');
    }

    /**
     *
     */
    protected function deleteInGate()
    {
        $this->warn('Removing records from GATE');

        //        $db = \Illuminate\Support\Facades\DB::connection('gate');
        //        $db->enableQueryLog();
        //
        //        $db->table('users_sites')
        //            ->where('id', $this->id)
        //            ->delete();
        //
        //        $this->log($db);
        $site = UsersSites::find($this->id);
        $site->delete();

        $this->info('Done!');
    }

    /**
     *
     */
    protected function deleteInRouter()
    {
        $this->warn('Removing records from ROUTER');

        $certs = Certificate::where('site_id', $this->id)->get();
        foreach ($certs as $cert) {
            /** @var Certificate $cert */
            DeletedCertificate::create([
                'site_id' => $cert->site_id,
                'domain' => $cert->common,
            ]);
        }

        $this->warn('Removing DNS records from Cloudflare ...');
        $hosts = Host::getHostsBySiteId($this->id);
        foreach ($hosts as $host) {
            /** @var Host $host */
            $this->info('Removing DNS record for: ' . $host->host);
            $host->removeDNS();
            $host->removeCustomHostname();
        }

        $site = Site::find($this->id);
        $site->delete();

        $this->info('Done!');
    }

    /**
     *
     */
    protected function deleteInPayment()
    {
        $this->warn('Removing records from PAYMENTS');

        $db = \Illuminate\Support\Facades\DB::connection('payments');
        $db->enableQueryLog();

        $db->table('payments')
            ->where('site_id', $this->id)
            ->delete();
        $this->log($db);

        $db->table('payment_configrations')
            ->where('site_id', $this->id)
            ->delete();
        $this->log($db);

        $this->info('Done!');
    }

    /**
     *
     */
    protected function deleteClientDatabase()
    {
        $this->warn('Destroying SITE #' . $this->id . ' database');

        $db = \Illuminate\Support\Facades\DB::connection();
        $db->enableQueryLog();

        try {
            $db->statement('drop database if exists cc_site_' . $this->id);
        } catch (Exception) {
            //
        }

        $this->log($db);

        $this->info('Done!');
    }

}
