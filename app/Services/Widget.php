<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 21.6.2017 г.
 * Time: 13:10
 */

namespace App\Services;

use App\Exceptions\Fault;
use App\Widgets\AddToCart;
use App\Widgets\Banner;
use App\Widgets\BundleProducts;
use App\Widgets\Button;
use App\Widgets\Carousel;
use App\Widgets\Code;
use App\Widgets\InstagramImages;
use App\Widgets\OrderDetails;
use App\Widgets\Product;
use App\Widgets\ProductShowcase;
use App\Widgets\RecentArticles;
use App\Widgets\Separator;
use App\Widgets\Showcase;
use App\Widgets\Text;
use App\Widgets\TextCarousel;
use App\Widgets\Title;
use App\Widgets\Video;
use App\Widgets\YotpoReviews;
use Apps;
use Illuminate\Support\Collection;
use Modules\Apps\Administration\BrandModel\Widgets\HomePageFilter;

/**
 * Class Widget
 * @package App\Services
 */
class Widget
{
    /**
     * @var array
     */
    protected $widgets_map = [
        'title' => Title::class,
        'text' => Text::class,
        'product-showcase' => ProductShowcase::class,
        'showcase' => Showcase::class,
        'banner' => Banner::class,
        'code' => Code::class,
        'separator' => Separator::class,
        'video' => Video::class,
        'carousel' => Carousel::class,
        'text-carousel' => TextCarousel::class,
        'order-details' => OrderDetails::class,
        'bundle-products' => BundleProducts::class,
        'instagram-images' => InstagramImages::class,
        'yotpo-reviews' => YotpoReviews::class,
        'product' => Product::class,
        'add-to-cart' => AddToCart::class,
        'button' => Button::class,
        'brand-model' => HomePageFilter::class,
//        'blog-list' => \App\Widgets\Blog\Listing::class, //@todo 50-60% ready
        'recent-articles' => RecentArticles::class,
        /*'recent-articles' => \App\Widgets\RecentArticles::class,
        'google-map' => \App\Widgets\GoogleMap::class,
        'addthis' => \App\Widgets\AddThisShare::class,
        'newsletter' => \App\Widgets\Newsletter::class,*/
        'product_review' => \App\Widgets\ProductReview::class,
        'request_review' => \App\Widgets\RequestReview::class,
        'store_locations' => \App\Widgets\StoreLocation::class,
        'cc_form' => \App\Widgets\CcForm::class,

    ];

    protected \Illuminate\Support\Collection $widgets;

    /**
     * Widget constructor.
     */
    public function __construct()
    {
        $this->widgets = new Collection();
        $this->load();
    }

    /**
     * @return Collection
     */
    public function getList(): \Illuminate\Support\Collection
    {
        return $this->widgets;
    }

    /**
     * @param $map
     * @return \lib\widget\WidgetAbstract
     * @throws Fault
     */
    public function get(string $map)
    {
        if (!in_array($map, array_keys($this->widgets_map))) {
            throw new Fault('Widget Not Found: ' . $map);
        }

        $class = $this->widgets_map[$map];

        return new $class($map);
    }

    /**
     *
     */
    protected function load()
    {
        foreach ($this->widgets_map as $map => $class) {
            switch ($map) {
                case 'instagram-images':
                    if (Apps::installed('instagram')) {
                        $this->widgets->put($map, new $class($map));
                    }

                    break;
                case 'yotpo-reviews':
                    if (Apps::installed('yotpo')) {
                        $this->widgets->put($map, new $class($map));
                    }

                    break;
                case 'brand-model':
                    if (Apps::installed('brand_model')) {
                        $this->widgets->put($map, new $class($map));
                    }

                    break;
                case 'product_review':
                    if (Apps::installed('product_review') && Apps::enabled('product_review')) {
                        $this->widgets->put($map, new $class($map));
                    }

                    break;
                case 'store_locations':
                    if (Apps::installed('store_locations')) {
                        $this->widgets->put($map, new $class($map));
                    }
                    // no break
                default: $this->widgets->put($map, new $class($map));
            }
        }
    }

}
