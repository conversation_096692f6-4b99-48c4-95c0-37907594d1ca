<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Router\Exceptions;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;

class OpenAI
{
    public const ENDPOINT = 'https://api.openai.com/v1/completions';

    protected \GuzzleHttp\Client $client;

    protected $model;

    protected $max_tokens;

    protected $temperature;

    /**
     * @param array $config
     * @return mixed
     */
    public function __construct(array $config)
    {
        $this->client = new Client([
//            RequestOptions::TIMEOUT => 3,
            RequestOptions::CONNECT_TIMEOUT => 3,
//            RequestOptions::READ_TIMEOUT => 3,
            RequestOptions::HEADERS => [
                'Authorization' => 'Bearer ' . $config['token'],
                'Content-Type' => 'application/json',
            ],
        ]);

        $this->model = $config['model'];
        $this->max_tokens = $config['max_tokens'];
        $this->temperature = $config['temperature'];
    }

    /**
     * @param mixed $prompt
     * @return mixed
     */
    public function prompt($prompt)
    {
        try {
            $params = [
                'prompt' => $prompt,
                'model' => $this->model,
                'temperature' => $this->temperature,
                'max_tokens' => $this->max_tokens,
                'top_p' => 1,
                'frequency_penalty' => 0,
                'presence_penalty' => 0,
            ];
            //        dd($params);
            $response = $this->client->post(self::ENDPOINT, [
                RequestOptions::JSON => $params
            ]);

            $result = json_decode((string) $response->getBody()->getContents(), true);
            if (!empty($result['choices'])) {
                return $result['choices'][0]['text'];
            }

            return;
        } catch (\Throwable $throwable) {
            Exceptions::createFromThrowable($throwable);
            return;
        }
    }
}
