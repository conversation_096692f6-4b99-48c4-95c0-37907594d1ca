<?php

declare(strict_types=1);

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @see \App\Services\ModoboaService
 * @method static changeAccountPassword(int $id, $input, $input1)
 * @method static updateAccountQuota(int $id, $input)
 * @method static deleteAccount(int $id)
 * @method static readAccount($account_id)
 * @method static createAccount(string $string, string $string1, $input, $input1, bool $false)
 * @method static createDomain($input)
 * @method static listDomains()
 */
class Modoboa extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'modoboa';
    }
}
