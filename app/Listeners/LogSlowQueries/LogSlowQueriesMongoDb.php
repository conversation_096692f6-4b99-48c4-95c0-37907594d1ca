<?php

declare(strict_types=1);

namespace App\Listeners\LogSlowQueries;

use App\Models\Log\SlowQuery;
use MongoDB\Driver\Monitoring\CommandFailedEvent;
use MongoDB\Driver\Monitoring\CommandStartedEvent;
use MongoDB\Driver\Monitoring\CommandSubscriber;
use MongoDB\Driver\Monitoring\CommandSucceededEvent;

class LogSlowQueriesMongoDb implements CommandSubscriber
{
    protected array $pending = [];
    protected array $command = [];
    protected ?string $log_key = null;

    public function commandStarted(CommandStartedEvent $event): void
    {
        $this->pending[$event->getRequestId()] = microtime(true);
        $this->command[$event->getRequestId()] = $event;
    }

    public function commandSucceeded(CommandSucceededEvent $event): void
    {
        $start = $this->pending[$event->getRequestId()] ?? null;
        /** @var CommandStartedEvent $command */
        $command = $this->command[$event->getRequestId()] ?? null;
        unset($this->pending[$event->getRequestId()]);
        unset($this->command[$event->getRequestId()]);

        if ($start) {
            $durationMs = (microtime(true) - $start) * 1000;

            if (($durationMs >= $this->getMongoMaxTime()) && ($formatted = $this->formatMongoCommand($command))) {
                try {
                    SlowQuery::create([
                        'key' => $this->getLogKey(),
                        'query' => null,
                        'bindings' => [],
                        'sql' => $formatted,
                        'time' => $durationMs / 1000,
                        'connection' => $command->getDatabaseName(),
                        'debug' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
                        'type' => 'mongodb',
                    ]);
                } catch (\Throwable $exception) {
                    //
                }
            }
        }
    }

    public function commandFailed(CommandFailedEvent $event): void
    {
        unset($this->pending[$event->getRequestId()]);
        unset($this->command[$event->getRequestId()]);
    }

    /**
     * @return string
     */
    protected function getLogKey(): string
    {
        if (app()->runningInConsole()) {
            return md5(site('site_id') . gethostname());
        } elseif (is_null($this->log_key)) {
            $this->log_key = md5(site('site_id') . microtime(true) . mt_rand());
        }

        return $this->log_key;
    }

    protected function getMongoMaxTime(): int
    {
        return app()->runningInConsole() ? 7000 : 2000;
    }

    protected function formatMongoCommand(CommandStartedEvent $event): ?string
    {
        $commandName = $event->getCommandName();
        $command = $event->getCommand();
        $collection = $command->{$commandName} ?? 'unknown_collection';

        if(in_array($collection, ['slow_queries'])) {
            return null;
        }

        $commandArray = $this->bsonToArray($command);
        unset($commandArray[$commandName]);

        $jsonOptions = JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE;
        $args = json_encode($commandArray, $jsonOptions);

        return sprintf("db.%s.%s(%s)", $collection, $commandName, $args);
    }

    protected function bsonToArray($bson): array
    {
        if(!empty($bson->lsid)) {
            unset($bson->lsid);
        }
        if(!empty($bson->{'$db'})) {
            unset($bson->{'$db'});
        }
        return json_decode(json_encode($bson), true);
    }
}
