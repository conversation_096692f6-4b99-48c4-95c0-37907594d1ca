<?php

namespace App\Listeners;

use Illuminate\Mail\Events\MessageSending;
use TijsVerkoyen\CssToInlineStyles\CssToInlineStyles;

class MailCssToInlineStyles
{
    /**
     * Handle the event.
     *
     * @param \Illuminate\Mail\Events\MessageSending $event
     * @return void
     */
    public function handle(MessageSending $event): void
    {
        $email = $event->message;
        $html = $email->getHtmlBody();
        if (!$html) {
            return;
        }

        $email->html($this->modifyHtml($html));
    }

    /**
     * Modify the html content.
     *
     * @param string $html
     * @return string
     */
    protected function modifyHtml(string $html): string
    {
        $cssInliner = new CssToInlineStyles;

        return $cssInliner->convert($html);
    }
}
