<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 5.4.2017 г.
 * Time: 11:04 ч.
 */

namespace App\Listeners;

use App\Events\AdminLogin;
use App\Helper\Plan;
use App\Models\Setting\Admin;
use App\Models\Store\Cart;
use App\Models\Store\Cart as CartModel;
use App\Models\Customer\Customer;
use Carbon\Carbon;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\Registered;
use Illuminate\Events\Dispatcher;
use Modules\Marketing\Segments\Core\Models\SubscriberToCustomer;
use Modules\Marketing\Segments\Core\SegmentsManager;
use Throwable;

class UserEventSubscriber
{
    /**
     * Handle user login events.
     * @param Login $event
     * @throws Throwable
     */
    public function onUserLogin($event): void
    {
        $user = null;
        if ($event instanceof Login) {
            $user = $event->user;
        } elseif ($event instanceof Registered) {
            $user = $event->user;
        }

        if ($user instanceof Customer && app_namespace() == 'site') {
            if (setting('merge_cart', 1)) {
                if (CartModel::mergeCarts($user->getAuthIdentifier())) {
                    session()->flash('cart_merge_message', __('sf.cart.merged'));
                }
            } elseif ($cart = Cart::instance()) {
                $cart->update(['user_id' => $user->getAuthIdentifier()]);
            }
        }

        if ($user instanceof Admin) {
            if (!request()->query('console')) {
                $user->update(['last_login' => Carbon::now()]);
                $site = site()->gate;
                if ($site->ga_client_id) {
                    session()->put('_gaClientId', $site->ga_client_id);
                }

                Plan::checkFreePlanExpireConditions();
                event(new AdminLogin($user));
            }
        } elseif ($user instanceof Customer) {
            if (($cart = Cart::instance()) && $cart->subscriber_id) {
                $subscriberId = $cart->subscriber_id;
            } else {
                $subscriberId = SubscriberToCustomer::whereCustomerId($user->id)
                    ->latest('id')->value('subscriber_id');
            }

            if ($subscriberId) {
                SegmentsManager::setSubscriberId($subscriberId);
                if ($cart && !$cart->subscriber_id) {
                    $cart->update(['subscriber_id' => $subscriberId]);
                }
            }
        }
    }

    /**
     * Handle user login events.
     * @param Registered $registered
     */
    public function onUserRegister(Registered $registered): void
    {
        if ($registered->user instanceof Customer) {
            //CartModel::mergeCarts($registered->user->getAuthIdentifier());
            $this->onUserRegisterGuest($registered);
        }
    }

    /**
     * Handle user logout events.
     * @param Logout $event
     * @throws Throwable
     */
    public function onUserLogout(Logout $event): void
    {
        if ($event->user instanceof Customer && ($cartInstance = CartModel::instance())) {
            $cartInstance->logout();
        }
    }

    /**
     * Handle user logout events.
     * @param Registered $registered
     */
    public function onUserRegisterGuest(Registered $registered): void
    {
        //
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param Dispatcher $events
     */
    public function subscribe($events): void
    {
        $events->listen(
            'Illuminate\Auth\Events\Register',
            'App\Listeners\UserEventSubscriber@onUserRegister'
        );

        $events->listen(
            \Illuminate\Auth\Events\Login::class,
            'App\Listeners\UserEventSubscriber@onUserLogin'
        );

        $events->listen(
            \Illuminate\Auth\Events\Logout::class,
            'App\Listeners\UserEventSubscriber@onUserLogout'
        );

        $events->listen(
            'Illuminate\Auth\Events\RegisterGuest',
            'App\Listeners\UserEventSubscriber@onUserRegisterGuest'
        );
    }
}
