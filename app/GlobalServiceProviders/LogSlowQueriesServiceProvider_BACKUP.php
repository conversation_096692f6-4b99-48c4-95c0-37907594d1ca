<?php

declare(strict_types=1);

namespace App\GlobalServiceProviders;

use App\Models\Log\SlowQuery;
use Exception;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;

class LogSlowQueriesServiceProvider_BACKUP extends ServiceProvider
{
    public const LOG = false;

    protected static $prev_query = [];

    protected $log_key;

    /**
     * Bootstrap the application events.
     *
     * @return void
     */
    public function boot(): void
    {
        if ($this->allowLogging()) {
            $this->callAfterResolving('db', function (\Illuminate\Database\DatabaseManager $db): void {
                $db->connection('router')->listen(
                    function ($query, $bindings = null, $time = null, $connectionName = null) use ($db): void {
                        if ($query instanceof QueryExecuted) {
                            $bindings = $query->bindings;
                            $time = $query->time;
                            $connectionName = $query->connectionName;
                            try {
                                $query = method_exists($query, 'toRawSql') ? $query->toSql() : $query->sql;
                            } catch (\Throwable $e) {
                                $query = '[UNAVAILABLE SQL]';
                            }
                        }

                        $query = str_replace("\0", '', $query);
                        $bindings = array_map(fn($b) => is_string($b) ? str_replace("\0", '', $b) : $b, (array)$bindings);

                        $isMongoConnection = str_contains($connectionName, 'mongo');
                        if ($isMongoConnection) {
                            $this->logMongoDbSlowQuery($query, $bindings, $time, $connectionName);
                        } else {
                            $this->logMySqlSlowQuery($query, $bindings, $time, $connectionName);
                        }

                        if (!Str::contains($query, 'slow_queries')) {
                            static::$prev_query[site('site_id')] = [
                                'sql' => (function ($type, $query, $bindings) {
                                    $fullSql = $query;
                                    if ($type == 'mysql') {
                                        try {
//                                            $fullSql = $query->toRawSql();
                                            $fullSql = $query->toSql();
                                        } catch (\Throwable) {
                                            //
                                        }
                                    }

                                    return $fullSql;
                                })($isMongoConnection ? 'mongodb' : 'mysql', $query, $bindings),
                                'time' => ($time / 1000)
                            ];
                        }
                    }
                );
            });
        }
    }

    /**
     * @param mixed $query
     * @param mixed $bindings
     * @param mixed $time
     * @param mixed $connectionName
     * @return void
     */
    protected function logMongoDbSlowQuery(mixed $query, mixed $bindings, mixed $time, mixed $connectionName): void
    {
        if (($seconds = round($time / 1000, 5)) >= $this->getMongoMaxTime()) {
            $this->logSlowQuery('mongodb', $seconds, $query, $bindings, $connectionName);
        }
    }

    /**
     * @param mixed $query
     * @param mixed $bindings
     * @param mixed $time
     * @param mixed $connectionName
     * @return void
     */
    protected function logMySqlSlowQuery(mixed $query, mixed $bindings, mixed $time, mixed $connectionName): void
    {
        if (($seconds = round($time / 1000, 5)) >= $this->getMaxTime()) {
            $this->logSlowQuery('mysql', $seconds, $query, $bindings, $connectionName);
        }
    }

    /**
     * @param mixed $type
     * @param mixed $seconds
     * @param mixed $query
     * @param mixed $bindings
     * @param mixed $connectionName
     * @return void
     */
    protected function logSlowQuery(mixed $type, mixed $seconds, mixed $query, mixed $bindings, mixed $connectionName): void
    {
        $query = str_replace("\0", '', $query);
        $bindings = array_map(fn($b): mixed => is_string($b) ? str_replace("\0", '', $b) : $b, (array)$bindings);

        $fullSql = $query;
        if ($type == 'mysql') {
            try {
                $fullSql = $query->toRawSql();
            } catch (\Throwable) {
                //
            }
        }

        try {
            if (!Str::contains($query, ['slow_queries'])) {
                SlowQuery::create([
                    'key' => $this->getLogKey(),
                    'query' => $query,
                    'bindings' => $bindings,
                    'sql' => $fullSql,
                    'time' => max($record->time ?? 0, $seconds),
                    'connection' => $connectionName,
                    'debug' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
                    'prev' => static::$prev_query[site('site_id')] ?? null,
                    'type' => $type,
                ]);
            }
        } catch (Exception) {
            //
        }
    }

    protected function allowLogging(): bool
    {
        return static::LOG && (app_namespace() == 'api2' || !str_contains(gethostname(), 'builder-google'));
    }

    public function getMaxTime(): int
    {
        return $this->app->runningInConsole() ? 7 : 3;
    }

    public function getMongoMaxTime(): int
    {
        return $this->app->runningInConsole() ? 3 : 2;
    }

    /**
     * @return string
     */
    protected function getLogKey(): string
    {
        if ($this->app->runningInConsole()) {
            return md5(site('site_id') . gethostname());
        } elseif (is_null($this->log_key)) {
            $this->log_key = md5(site('site_id') . microtime(true) . mt_rand());
        }

        return $this->log_key;
    }

}
