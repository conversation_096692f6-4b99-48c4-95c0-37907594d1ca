<?php

declare(strict_types=1);

namespace App\GlobalServiceProviders;

use App\Http\Request\Eik\EIKValidator;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Factory;

class EIKValidatorServiceProvider extends ServiceProvider
{
    #[\Override]
    public function register(): void
    {
        $this->callAfterResolving('validator', function (Factory $validator): void {
            $validator->extend('eik', fn($attribute, $value, $parameters): bool => $this->validate($value));

            $validator->extend('bulstat', fn($attribute, $value, $parameters): bool => $this->validate($value));
        });
    }

    /**
     * @param  mixed  $eik
     * @return mixed
     */
    private function validate($eik): bool
    {
        try {
            return (new EIKValidator())->isValid($eik);
        } catch (\Exception) {
            return false;
        }
    }
}
