<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: user
 * Date: 5/16/2018
 * Time: 1:58 PM
 */

namespace App\Helper\Import;

use App\Exceptions\Error;
use App\Models\Redirect\Redirect;
use Illuminate\Support\Collection;

class RedirectsFormatter extends AbstractErpFormatter
{
    /**
     * @var array
     */
    protected $variants_images = [];

    public static $redirects_import_fields = [
        'redirects' => [
            'redirect.old_url' => 'redirect_old_url',
            'redirect.new_url' => 'redirect_new_url',
        ],
    ];

    public static $redirects_import_required_fields = [
        'redirect.old_url',
        'redirect.new_url',
    ];

    /**
     * @param mixed $settings
     * @return mixed
     */
    public function __construct(protected $settings)
    {
    }

    /**
     * @param Collection $records
     * @return void
     * @throws Error
     */
    public function addRedirects($records): void
    {
        $formattedData = [];
        $redirectModel = new Redirect();
        foreach ($records as $record) {
            $formatted = $this->formatData($record);
            $formatted['old_url'] = $redirectModel->parseOldUrl($formatted['old_url']);
            $formattedData[$formatted['old_url']] = $formatted;
        }

        Redirect::whereIn('old_url', array_keys($formattedData))->delete();

        $data = array_chunk($formattedData, 100);
        foreach ($data as $redirects) {
            Redirect::insert($redirects);
        }
    }

    /**
     * @param $record
     * @return array
     * @throws Error
     */
    protected function formatData($record)
    {
        $rowData = $this->getMappedFieldsValues($record);
        $redirectData = [
            'location' => 'manual',
        ];

        if (str_starts_with((string) ($rowData['redirect']['new_url'] ?? null), 'https://') || str_starts_with((string) ($rowData['redirect']['new_url'] ?? null), 'http://')) {
            $redirectData['location'] = 'external';
        }

        return $rowData['redirect'] + $redirectData;
    }

    /**
     * @param $fields
     * @return array
     * @throws Error
     */
    protected function getMappedFieldsValues($fields): array
    {
        $import_field_binds = $this->parseFieldBinds();

        $data = [];
        foreach ($fields as $index => $value) {
            $import_field_binds_matches = array_filter(array_map(fn ($item) => $item['field_index'] == $index ? $item : null, $import_field_binds));

            foreach ($import_field_binds_matches as $import_field_binds_match) {
                $data[$import_field_binds_match['module']][$import_field_binds_match['field_name']] = $value;
            }
        }

        return $data;
    }

    /**
     * @return array
     * @throws Error
     */
    protected function parseFieldBinds(): array
    {
        $formatted = [];

        $plain_fields = [];
        foreach (static::$redirects_import_fields as $module) {
            foreach ($module as $key => $value) {
                $plain_fields[] = $key;
            }
        }

        $i = 0;
        foreach ($this->settings['import_field_binds'] as $key => $value) {
            if (empty($value) && $value !== '0') {
                continue;
            }

            if (!in_array($key, $plain_fields)) {
                throw new Error(__('import.err.invalid_field_bind'));
            }

            $data = explode('.', (string) $key);

            $formatted[$i]['module'] = $data[0];
            $formatted[$i]['field_name'] = $data[1];
            $formatted[$i]['field_index'] = $value;

            ++$i;
        }

        return $formatted;
    }
}
