<?php

declare (strict_types=1);
/**
 * Created by PhpStorm.
 * User: joro
 * Date: 15.1.2019 г.
 * Time: 08:58 ч.
 */
namespace App\Helper;

use Closure;
use Illuminate\Support\Str;
class ArrayCache
{
    protected static $_items = [];

    /**
     * Parent Name: ArrayCache
     * Name: has
     * Type: public static
     *
     * DESCRIPTION: This method checks the presence of a specified cache key in the internal cache storage. It uses the key provided to generate a unique cache key prefix using the `getSitePrefix` method. By verifying the existence of this prefixed key in the static `$_items` array, the method ensures that it accurately identifies whether the cache entry is present or not. The method is crucial for cache management within a multi-tenant architecture, supporting efficient caching strategies.
     * BUSINESS_LOGIC: The purpose of this method is to determine whether a cache entry exists for a given key, ensuring that cache lookups can be efficiently performed. It helps avoid unnecessary cache retrieval operations by first confirming the existence of the cache entry, directly influencing performance within the application.
     * SUMMARY: Checks if a cached item exists based on the provided key.
     *
     * @param string $key The key to be checked for existence in the cache storage.
     * @return bool Returns true if the cache entry exists; otherwise, false.
     */
    public static function has(string $key): bool
    {
        return array_key_exists(static::getSitePrefix($key), static::$_items);
    }

    /**
     * Parent Name: ArrayCache
     * Name: get
     * Type: public static
     *
     * DESCRIPTION: This method retrieves a value from the internal cache storage based on the specified key. It first checks the existence of the key using the static `has` method. If the key exists, it returns the corresponding cached value from the static `$_items` array. If the key does not exist, it returns the default value provided. This mechanism supports efficient data retrieval from cache, ensuring that missing keys do not lead to errors but instead return a predetermined fallback value.
     *
     * BUSINESS_LOGIC: The purpose of this method is to enable safe access to cached data by checking for its presence before attempting to retrieve it. This not only helps maintain the integrity of the cache retrieval process but also supports default value handling, which can enhance the user experience by providing fallback options. The method plays a vital role in managing cache within a multi-tenant architecture without risking data inconsistency.
     *
     * SUMMARY: Retrieves a cached value by key or returns a default if not found.
     *
     * @param string $key The key used to identify the cached value in the storage.
     * @param mixed|null $default The default value to return if the specified key does not exist in the cache.
     * @return mixed Returns the cached value associated with the specified key or the default value if the key is not found.
     */
    public static function get($key, $default = null)
    {
        if (static::has($key)) {
            return static::$_items[static::getSitePrefix($key)];
        }

        return $default;
    }

    /**
     * @return array
     */
    public static function all(): array
    {
        $result = [];
        foreach (static::$_items as $key => $value) {
            if (Str::startsWith($key, static::getSitePrefix(''))) {
                $result[$key] = $value;
            }
        }

        return $result;
    }

    /**
     * @return array
     */
    public static function keys(): array
    {
        $result = [];
        foreach (static::$_items as $key => $value) {
            if (Str::startsWith($key, static::getSitePrefix(''))) {
                $result[] = $key;
            }
        }

        return $result;
    }

    /**
     * Parent Name: ArrayCache
     * Name: set
     * Type: public static
     *
     * DESCRIPTION: This method sets a value in the internal cache storage by associating it with a specified cache key. It first generates a unique cache key prefix using the `getSitePrefix` method, ensuring that cache entries remain distinct across different sites within a multi-tenant environment. The method then stores the value in a static array of cached items and returns the result of a subsequent existence check via the `has` method, indicating whether the value was successfully cached.
     * 
     * BUSINESS_LOGIC: The purpose of this method is to enable the storage of data in cache, allowing for quicker access on future retrieval requests. By systematically verifying the existence of the cached entry after setting it, the method ensures both the integrity and operational certainty of cache management, contributing to overall performance within the application.
     * 
     * SUMMARY: Caches a value associated with a specified key and checks its existence within the cache storage.
     * 
     * @param string $key The unique identifier for the cache entry being set.
     * @param mixed $value The value to be stored in the cache associated with the specified key.
     * @return bool Returns true if the cache entry exists after the set operation; otherwise, false.
     */
    public static function set(string $key, $value): bool
    {
        static::$_items[static::getSitePrefix($key)] = $value;
        return static::has($key);
    }

    /**
     * Parent Name: ArrayCache
     * Name: remember
     * Type: public static
     *
     * DESCRIPTION: This method attempts to retrieve a value from the cache storage associated with a given key. If the key does not exist in the cache, it invokes the provided closure to generate the value, stores it in the cache using the `set` method, and then returns the newly generated value. This ensures efficient retrieval by minimizing calls to the closure once the value has been cached.
     *
     * BUSINESS_LOGIC: The purpose of this method is to provide a straightforward mechanism for caching dynamic values based on a key. By leveraging a closure, it allows for the lazy generation of values that are heavy to compute or fetch, while also supporting efficient data management in caching. It ensures that repeated requests for the same data do not invoke the potentially expensive closure more than necessary, thus optimizing performance.
     *
     * SUMMARY: Caches a generated value associated with a specified key if not already cached; retrieves from cache otherwise.
     *
     * @param string $key The unique identifier for the cache entry being retrieved or set.
     * @param Closure $value A closure that generates the value to be cached if it does not already exist.
     * @return mixed Returns the cached value associated with the specified key or the newly generated value if the key was not found in the cache.
     */
    public static function remember($key, Closure $value)
    {
        if (static::has($key)) {
            return static::get($key);
        }

        $value = $value();
        static::set($key, $value);
        return $value;
    }

    /**
     * @param string $key
     * @return bool
     */
    public static function forget($key): bool
    {
        $search = str_replace('*', '([^.]*)', static::getSitePrefix($key));
        foreach (static::$_items as $key => $value) {
            if (preg_match('~^' . $search . '$~', (string) $key, $m)) {
                unset(static::$_items[$key]);
            }
        }

        return true;
    }

    /**
     * @return bool
     */
    public static function flush(): bool
    {
        foreach (static::$_items as $key => $value) {
            if (Str::startsWith($key, site('site_id') . '.')) {
                unset(static::$_items[$key]);
            }
        }

        return true;
    }

    /**
     * @return bool
     */
    public static function flushAll(): bool
    {
        static::$_items = [];
        return true;
    }

    /**
     * Parent Name: ArrayCache
     * Name: getSitePrefix
     * Type: protected
     *
     * DESCRIPTION: This method generates and returns a cache key prefix specific to the site. The prefix consists of the site's unique identifier followed by a dot and the provided key string. It ensures that cache keys are uniquely qualified based on the current site's context, which is crucial for maintaining separate cache entries across different sites in a multi-tenant architecture.
     * BUSINESS_LOGIC: This is particularly useful in environments where multiple sites share the same application instance, allowing for efficient retrieval and storage of cached data without conflicts between sites.
     * SUMMARY: Returns a unique cache key prefix based on site ID and a provided key.
     *
     * @param string $key The key to be appended to the site ID to form the full cache key prefix.
     * @return string The formed cache key prefix, combining the site ID and the provided key.
     */
    protected static function getSitePrefix(string $key): string
    {
        return site('site_id') . '.' . $key;
    }
}