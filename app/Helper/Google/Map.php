<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 30.8.2017 г.
 * Time: 13:25 ч.
 */

namespace App\Helper\Google;

use App\Exceptions\Error;
use App\Helper\Curl;
use App\Helper\Google\Map\Address;
use Cache;
use Illuminate\Support\Collection;

class Map
{
    /**
     * @var string
     */
    public const URL = 'http%s://maps.googleapis.com/maps/api/geocode/json';

    /**
     * @var bool
     */
    protected $ssl = true;

    /**
     * @var integer
     */
    protected $timeout = 5;

    /**
     * @var string
     */
    protected $language;

    /**
     * Set use or not ssl link
     * @param $value
     * @return $this
     */
    public function setSsl($value): static
    {
        $this->ssl = (bool)$value;
        return $this;
    }

    /**
     * Get use or not ssl link
     * @return $this
     */
    public function getSsl()
    {
        return $this->ssl;
    }

    /**
     * Set timeout
     * @param $value
     * @return $this
     */
    public function setTimeout($value): static
    {
        $this->timeout = (int)$value;
        return $this;
    }

    /**
     * Get timeout
     * @return $this
     */
    public function getTimeout()
    {
        return $this->timeout;
    }

    /**
     * Set language
     * @param $value
     * @return $this
     */
    public function setLanguage($value): static
    {
        $this->language = $value;
        return $this;
    }

    /**
     * Get language
     * @return $this
     */
    public function getLanguage()
    {
        if (empty($this->language)) {
            $this->language = app_namespace() == 'sitecp' ? site('language_cp', site('language')) : site('language');
        }

        return $this->language;
    }

    /**
     * @return string
     */
    public function getUrl(): string
    {
        return sprintf(static::URL, $this->ssl ? 's' : '');
    }

    /**
     * @return array
     */
    public function getDefaultParameters(): array
    {
        return [
            'key' => getGoogleMapKey(),
            'sensor' => 'false',
            'language' => $this->getLanguage()
        ];
    }

    /**
     * @param $address
     * @return Collection|Address[]
     * @throws Error
     */
    public function getLocation($address)
    {
        return static::call(['address' => $address]);
    }

    /**
     * @param $address
     * @return null|Address
     * @throws Error
     */
    public function getFirstLocation($address)
    {
        return static::call(['address' => $address])->first();
    }

    /**
     * @param $lat
     * @param $lng
     * @return Map\Address[]|Collection
     * @throws Error
     */
    public function getLocationFromPoint(string $lat, string $lng)
    {
        return static::call(['latlng' => $lat . ',' . $lng]);
    }

    /**
     * @param array $params
     * @return Address[]|Collection
     */
    protected function call(array $params = [])
    {
        return Cache::remember('google-places:address-information.' . $this->makeKeyFromParameters($params), config('cache.ttl_1w'), function () use ($params) {
            $curl = new Curl();
            $curl->useCurl(function_exists('curl_init'));
            $curl->setTarget($this->getUrl());
            $curl->setTimeout($this->getTimeout());
            $curl->setParams(array_merge($this->getDefaultParameters(), $params));
            $curl->execute();

            if ($error = $curl->getError()) {
                throw new Error('Cannot make curl request! Error - ' . $error);
            }

            if ($curl->getStatus() != 200) {
                throw new Error('Curl status response is: ' . $curl->getStatus());
            }

            $result = json_decode($curl->getResult(), true);
            if (json_last_error()) {
                throw new Error(json_last_error_msg());
            }

            if ($result['status'] != 'OK') {
                throw new Error('Response status is: ' . $result['status']);
            }

            return Collection::make($result['results'])->map(fn ($address): \App\Helper\Google\Map\Address => new Address($address));
        });

    }

    /**
     * @param mixed $params
     * @return mixed
     */
    protected function makeKeyFromParameters($params): string
    {
        $key = '';
        foreach ($params as $k => $v) {
            $key .= $k . '.' . (is_array($v) ? json_encode($v) : $v);
        }

        return $key;
    }

}
