<?php

declare(strict_types=1);

namespace App\Helper\Store\Abstraction\Order;

use App\Helper\Store\Abstraction\Concerns\Item\DiscountItemMethods;
use App\Helper\Store\Abstraction\Concerns\Item\ItemGlobalMethods;
use App\Helper\Store\Abstraction\Concerns\Item\ItemImageMethods;
use App\Helper\Store\Abstraction\Concerns\Item\ItemToArray;
use App\Helper\Store\Abstraction\Concerns\Item\ModificationMethods;
use App\Helper\Store\Abstraction\Concerns\Item\OptionItemMethods;
use App\Helper\Store\Abstraction\Concerns\Item\Price\PriceMethods;
use App\Helper\Store\Abstraction\Concerns\Item\Price\TotalPriceMethods;
use App\Helper\Store\Abstraction\Concerns\Item\Price\TotalPriceWithModificationMethods;
use App\Helper\Store\Abstraction\Concerns\Item\QuantityItemMethods;
use App\Helper\Store\Abstraction\Concerns\Item\Vat\VatItemMethods;

trait OrderProductMethods
{
    use ItemGlobalMethods;
    use PriceMethods;
    use DiscountItemMethods;
    use QuantityItemMethods;
    use TotalPriceMethods;
    use VatItemMethods;
    use OptionItemMethods;
    use ItemToArray;
    use ItemImageMethods;
    use TotalPriceWithModificationMethods;
    use ModificationMethods;

    /**
     * @inheritDoc
     */
    public function getItemKey(): string
    {
        return md5(strval($this->id));
    }

    /**
     * @inheritDoc
     */
    public function isHideDiscountedPrice(): bool
    {
        if (!$this->hasDiscount()) {
            return false;
        }

        return $this->getDiscount()->isHideDiscountedPrice();
    }

    /**
     * @inheritDoc
     */
    public function isMsrp(): bool
    {
        if (!$this->hasDiscount()) {
            return false;
        }

        return $this->getDiscount()->isMsrp();
    }

    /**
     * @inheritDoc
     */
    public function getMsrp(): ?int
    {
        if (!$this->hasDiscount()) {
            return null;
        }

        return $this->getDiscount()->getMsrp();
    }

    /**
     * @inheritDoc
     */
    public function isIgnoreDiscountCode(?bool $ignore = null)
    {
        if (is_bool($ignore)) {
            $this->setAttribute('ignore_discount_code', $ignore);
            return $this->syncOriginal();
        }

        return (bool)$this->getAttribute('ignore_discount_code');
    }

    /**
     * @inheritDoc
     */
    public function getTagIds(): array
    {
        return optional((optional($this->product->tag_ids ?? null))->pluck('tag_id'))->all() ?? [];
    }

    /**
     * @inheritDoc
     */
    public function getSmartCollectionIds(): array
    {
        if ($this->smart_collection_ids->isNotEmpty()) {
            $ids = $this->smart_collection_ids->pluck('selection_id')->all();
        } elseif (!empty($this->product->smart_collection_ids) && $this->product->smart_collection_ids->isNotEmpty()) {
            $ids = $this->product->smart_collection_ids->pluck('selection_id')->all();
        }

        return $ids ?? [];
    }

    /**
     * @param array $collections
     * @return mixed
     */
    public function getValidSmartCollectionId(array $collections): ?int
    {
        $ids = $this->getSmartCollectionIds();
        if (empty($ids)) {
            return null;
        }

        $ids = array_intersect($collections, $ids);
        if (empty($ids)) {
            return null;
        }

        return \Illuminate\Support\Arr::first($ids);
    }
}
