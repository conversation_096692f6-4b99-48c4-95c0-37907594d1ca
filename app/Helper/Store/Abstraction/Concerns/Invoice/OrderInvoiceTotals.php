<?php

declare(strict_types=1);

namespace App\Helper\Store\Abstraction\Concerns\Invoice;

use App\Helper\Store\Abstraction\Invoice\InvoiceTotal;
use App\Models\Order\OrderTotals;
use Illuminate\Support\Collection;

trait OrderInvoiceTotals
{
    /**
     * @inheritDoc
     */
    public function getTotals(): Collection
    {
        return $this->totals;
    }

    /**
     * @inheritDoc
     */
    protected function transformInvoiceTotals(): Collection
    {
        $totals = $this->order->totals;
        if ($this->isShippingAddedToRow) {
            $totals = $totals->where('group', '<>', 'shipping');
        }

        $totalsRows = collect();

        $shippingItems = $totals->where('group', 'shipping');
        if (setting('invoice_hide_zero_shipping') && $shippingItems->count() == 1 && $shippingItems->sum('price') == 0) {
            $totals = $totals->where('group', '!=', 'shipping');
        }

        /** @var OrderTotals $total */
        foreach ($totals as $total) {
            if ($total->hide_in_invoice) {
                continue;
            }

            $totalsRows->push(with(new InvoiceTotal(), function (InvoiceTotal $invoiceTotal) use ($total): \App\Helper\Store\Abstraction\Invoice\InvoiceTotal {
                $price = $total->price_without_vat;
                $price_with_vat = $total->price;
                if ($this->shippingIncrement > 0 && $total->group == 'subtotal' && $total->key == 'subtotal') {
                    $price += $this->shippingIncrement;
                    $price_with_vat += $this->shippingIncrementWithVat;
                }

                if (str_starts_with($total->group, 'discount')) {
                    $price *= -1;
                    $price_with_vat *= -1;
                }

                $invoiceTotal->setGroup($total->group)
                    ->setKey($total->key)
                    ->setName($total->invoice_name ?: $total->name)
                    ->setDescription($total->description)
                    ->setPrice(intval($price))
                    ->setPriceWithVat(intval($price_with_vat))
                    ->setIsVat((bool)$total->is_vat);

                return $invoiceTotal;
            }));
        }

        return $totalsRows;
    }
}
