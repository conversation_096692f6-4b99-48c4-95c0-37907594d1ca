<?php

declare(strict_types=1);

namespace App\Helper\Store\Abstraction\Concerns\Item;

use App\Exceptions\Error;
use App\Models\Product\Product;

trait ItemImageMethods
{
    /**
     * @param null $size
     * @return string
     * @throws Error
     */
    public function getImage($size = null): string
    {
        if ($this->variant && !empty($this->variant->image->image)) {
            return $this->variant->image->image->getImage($size);
        } elseif ($this->product) {
            return $this->product->getImage($size);
        }

        return (new Product())->getImage($size);
    }

    /**
     * @return string
     */
    public function getOrientation(): string
    {
        if ($this->variant && !empty($this->variant->image->image)) {
            return $this->variant->image->image->getOrientation();
        } elseif ($this->product) {
            return $this->product->getOrientation();
        }

        return (new Product())->getOrientation();
    }

    /**
     * @return bool
     */
    public function hasImage(): bool
    {
        if ($this->variant && !empty($this->variant->image->image)) {
            return !!$this->variant->image->image->hasImage();
        } elseif ($this->product) {
            return !!$this->product->hasImage();
        }

        return false;
    }

    /**
     * @inheritDoc
     * @throws Error
     */
    public function getImages(): array
    {
        if (!empty($this->variant->image->image)) {
            return $this->variant->image->image->getImages();
        }

        if ($this->product) {
            return $this->product->getImages();
        }

        return (new Product())->getImages();
    }

}
