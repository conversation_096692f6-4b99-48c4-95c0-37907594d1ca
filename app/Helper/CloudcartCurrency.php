<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 8/30/2018
 * Time: 3:11 PM
 */

namespace App\Helper;

class CloudcartCurrency
{
    protected static $precision = 6;

    public static function default()
    {
        return config('billing.currency.default');
    }

    public static function rates()
    {
        return config('billing.currency.rates');
    }

    /**
     * @param mixed $to
     * @param mixed $from
     * @return mixed
     */
    public static function rate($to, $from = null): float
    {
        return round(static::convert(1, $to, $from), self::$precision);
    }

    /**
     * @param mixed $amount
     * @param mixed $to
     * @param mixed $from
     * @return mixed
     */
    public static function convert($amount, $to, $from = null)
    {
        $to = strtoupper((string) $to);
        $from = is_null($from) ? null : strtoupper($from);

        if ($from == $to) {
            return $amount;
        }

        $base = static::default();
        $rates = static::rates();

        if (is_null($from)) {
            $from = $base;
        }

        if ($from != $base) {
            $amount = $amount / $rates[$from];
        }

        $result = $amount * $rates[$to];

        return round($result, self::$precision);
    }
}
