<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 31.10.2017 г.
 * Time: 15:18 ч.
 */

namespace App\Helper\OmniShip;

use CommerceGuys\Addressing\Formatter\DefaultFormatter;
use CommerceGuys\Addressing\Address as AddressFormatter;
use CommerceGuys\Addressing\AddressFormat\AddressFormatRepository;
use CommerceGuys\Addressing\Country\CountryRepository;
use CommerceGuys\Addressing\Subdivision\SubdivisionRepository;
use Illuminate\Support\Arr;
use OmniShip;
use Omniship\Address\City;
use Omniship\Address\Country;
use Omniship\Address\State;
use Omniship\Common\Address as BaseAddress;

class Address extends BaseAddress
{
    /**
     * Create a new item with the specified parameters
     *
     * @param array|object|null $parameters An array of parameters to set on the new object
     */
    public function __construct(array|object|null $parameters = [])
    {
        parent::__construct($parameters);
        $this->setTemplateFullName(setting('customer_name_display', '{first_name} {last_name}'));
        $this->setTemplateStreet(setting('street_template', '{street_name} {street_number}'));
        $this->setLocal(site('language', site('language_cp')));
    }

    /**
     * @param mixed $timezone
     * @return mixed
     */
    #[\Override]
    public function setTimeZone($timezone): static
    {
        return $this;
    }

    /**
     * Get the Marketplace
     * @return Marketplace
     */
    public function getMarketplace(): mixed
    {
        return $this->getParameter('marketplace');
    }

    /**
     * Set the address city
     * @param Marketplace|array $city
     * @return $this
     * @throws \InvalidArgumentException
     */
    public function setMarketplace($city): static
    {
        if (empty($city)) {
            return $this;
        }

        if (!($city instanceof Marketplace)) {
            $city = new Marketplace($city);
        }

        if ($city->isEmpty()) {
            $this->invalidArguments('10003');
        }

        return $this->setParameter('marketplace', $city);
    }

    /**
     * Set the address country
     * @param Country|array $country
     * @return $this|BaseAddress
     */
    public function setCountry($country): static
    {
        if (!($country instanceof Country)) {
            $country = new Country($country ?: []);
        }

        return $this->setParameter('country', $country);
    }

    /**
     * Set the address city
     * @param City|array $city
     * @return $this|BaseAddress
     */
    public function setCity($city): static
    {
        if (!($city instanceof City)) {
            $city = new City($city ?: []);
        }

        return $this->setParameter('city', $city);
    }

    /**
     * Set the address state
     * @param State|array $state
     * @return $this
     */
    public function setState($state): static
    {
        if (!$state) {
            return $this;
        }

        if (!($state instanceof State)) {
            $state = new State($state ?: []);
        }

        return $this->setParameter('state', $state);
    }

    /**
     * Get the address neighborhood
     * @return string|mixed
     */
    public function getNeighborhood(): mixed
    {
        return $this->getParameter('neighborhood');
    }

    /**
     * Set the address neighborhood
     * @param $neighborhood
     * @return \Omniship\Common\Address
     */
    public function setNeighborhood($neighborhood): \Omniship\Common\Address
    {
        return $this->setParameter('neighborhood', $neighborhood);
    }

    /**
     * Get the address locality
     * @return string|mixed
     */
    public function getLocality(): mixed
    {
        return $this->getParameter('locality');
    }

    /**
     * Set the address locality
     * @param $locality
     * @return \Omniship\Common\Address
     */
    public function setLocality($locality): \Omniship\Common\Address
    {
        return $this->setParameter('locality', $locality);
    }

    /**
     * Get the address with vat
     * @return bool
     */
    public function getWithVat()
    {
        if ($this->hasParameter('with_vat')) {
            return $this->getParameter('with_vat');
        }

        return false;
    }

    /**
     * Set the address with vat
     * @param $value
     * @return \Omniship\Common\Address
     */
    public function setWithVat($value): \Omniship\Common\Address
    {
        return $this->setParameter('with_vat', $value);
    }

    /**
     * Get the address integration
     * @return string|mixed
     */
    public function getIntegration(): mixed
    {
        return $this->getParameter('integration');
    }

    /**
     * Set the address integration
     * @param $integration
     * @return \Omniship\Common\Address
     */
    public function setIntegration($integration): \Omniship\Common\Address
    {
        return $this->setParameter('integration', $integration);
    }

    public function toArray(): array
    {
        $array = parent::toArray();

        foreach (['id', 'name', 'iso2', 'iso3'] as $code) {
            if ($value = Arr::get($array, 'country.' . $code)) {
                $array['country_' . $code] = $value;
            }
        }

        foreach (['name', 'iso2'] as $code) {
            if ($value = Arr::get($array, 'state.' . $code)) {
                $array['state_' . $code] = $value;
            }
        }

        foreach (['name', 'id'] as $code) {
            if ($value = Arr::get($array, 'city.' . $code)) {
                $array['city_' . $code] = $value;
            }
        }

        return $array;
    }

    public function formatErp(): string
    {
        $addressFormatRepository = new AddressFormatRepository();
        $countryRepository = new CountryRepository();
        $subdivisionRepository = new SubdivisionRepository();
        $locale = $this->getLocal() ?: $this->getCountry()->getIso2();

        $formatter = new DefaultFormatter($addressFormatRepository, $countryRepository, $subdivisionRepository,  [
            'html' => false
        ]);
        // Options passed to the constructor or setOption / setOptions allow turning
        // off html rendering, customizing the wrapper element and its attributes.

        $address = new AddressFormatter($this->getCountry()->getIso2() ?: 'BG');

        //add state to address
        if (($state = $this->getState()) !== null) {
            $address = $address->withAdministrativeArea((string) $state->getIso2());
        }

        //add city to address
        if (($city = $this->getCity()) !== null) {
            $address = $address->withLocality((string) $city->getName());
        }

        //add post code to address
        if ($postal_code = $this->getPostCode()) {
            $address = $address->withPostalCode((string) $postal_code);
        }

        //if address is office return formatted address
        if (!is_null($office = $this->getOffice())) {
            $label = __('sf.global.label.office');
            if ($this->getIntegration() && OmniShip::has($this->getIntegration())) {
                $label = sprintf('%s %s', OmniShip::get($this->getIntegration())->getName(), __('sf.global.label.office'));
            }

            $address = $address->withAddressLine1(
                sprintf(
                    '%s "%s"',
                    $label,
                    $office->getName()
                )
            );
            return $formatter->format($address);
        }

        $line1 = '';
        if (!is_null($street = $this->getStreet())) {
            $number = $this->getStreetNumber();
            $line1 .= trim(str_replace([
                '{street_name}',
                '{street_number}'
            ], [
                $street->getName(),
                $number
            ], $this->getTemplateStreet()));
        } elseif (!is_null($quarter = $this->getQuarter())) {
            $line1 .= $quarter->getName();
            if (!empty($number = $this->getStreetNumber())) {
                $line1 .= ' ' . $number;
            }
        }

        if ($other = implode(' - ', array_filter([$this->getBuilding(), $this->getEntrance(), $this->getFloor(), $this->getApartment()]))) {
            $line1 .= ($line1 ? ' / ' : '') . $other;
        }

        if ($line1) {
            $address = $address->withAddressLine1((string) $line1);
        }

        if ($lines = implode("\n", array_filter([$this->getAddress1(), $this->getAddress2(), $this->getAddress3()]))) {
            $address = $address->withAddressLine2((string) $lines);
        }

        return $formatter->format($address);
    }

    /**
     * Get company Bulstat
     * @return string|mixed
     */
    public function getCompanyBulstat(): mixed
    {
        return $this->getParameter('company_bulstat');
    }

    /**
     * Set the company Bulstat
     * @param $locality
     * @param mixed $company_bulstat
     * @return \Omniship\Common\Address
     */
    public function setCompanyBulstat($company_bulstat): \Omniship\Common\Address
    {
        return $this->setParameter('company_bulstat', $company_bulstat);
    }

    /**
     * Get company Bulstat
     * @return string|mixed
     */
    public function getCompanyMol(): mixed
    {
        return $this->getParameter('company_mol');
    }

    /**
     * Set the company Bulstat
     * @param $locality
     * @param mixed $company_bulstat
     * @return \Omniship\Common\Address
     */
    public function setCompanyMol($company_bulstat): \Omniship\Common\Address
    {
        return $this->setParameter('company_mol', $company_bulstat);
    }

}
