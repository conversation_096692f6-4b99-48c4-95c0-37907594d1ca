<?php

declare(strict_types=1);

namespace App\Helper\Selection\Criteria;

use App\Models\Category\PropertyValue;

class CategoryProperty extends ExternalColumn
{
    #[\Override]
    public function __toString(): string
    {
        return 'EXISTS(' . PropertyValue::whereColumn($this->table . '.product_id', 'products.id')
                ->whereRaw(sprintf('`%s` %s', $this->column, $this->statement))->toFullSql() . ')';
    }

}
