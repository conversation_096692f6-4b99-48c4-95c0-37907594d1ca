<?php

declare(strict_types=1);

namespace App\Helper\Liquid\Drops;

use Liquid\Drop;

class DropLocalization extends Drop
{
    /**
     * The countries that are available on the store.
     *
     * @return array
     */
    public function available_countries(): array
    {
        return $this->context->getAssigns()['localization']['available_countries'] ?? [];
    }

    /**
     * The languages that are available on the store.
     *
     * @return array
     */
    public function available_languages(): array
    {
        return $this->context->getAssigns()['localization']['available_languages'] ?? [];
    }

    /**
     * The currently selected country on the storefront.
     *
     * @return array
     */
    public function country(): array
    {
        return $this->context->getAssigns()['localization']['country'] ?? [];
    }

    /**
     * The currently selected language on the storefront.
     *
     * @return array
     */
    public function language(): array
    {
        return $this->context->getAssigns()['localization']['language'] ?? [];
    }

    /**
     * The currently selected market on the storefront.
     *
     * @return array
     */
    public function market(): array
    {
        return $this->context->getAssigns()['localization']['market'] ?? [];
    }

    /**
     * {@inheritDoc}
     */
    public function toArray(): array
    {
        return [
            'available_countries' => $this->available_countries(),
            'available_languages' => $this->available_languages(),
            'country' => $this->country(),
            'language' => $this->language(),
            'market' => $this->market(),
        ];
    }
}
