<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 21.10.2016 г.
 * Time: 17:27 ч.
 */

namespace App\Helper;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

/**
 * Class UrlImageToFile
 * @package App\Helper
 */
class UrlImageToFile
{
    protected string $url;

    protected $error;

    protected $error_status = 0;

    protected $file_name = null;

    protected $headers;

    protected $ignore = [
        'Not Found', 'malformed', 'Time-out',
        'Forbidden', 'resolve host'
    ];

    /**
     * UrlImageToFile constructor.
     *
     * @param $image
     * @param $name
     * @param bool $allow_json
     */
    public function __construct($image, protected $name, protected $allow_json = false)
    {
        $this->url = urldecode((string) $image);
    }

    /**
     * @param int $size
     * @return array|bool
     */
    public function download($size = 150)
    {
        $allowed_mimetypes = config('image.allowed_mimetypes');
        $allowed_mimetypes[] = 'image/vnd.microsoft.icon';
        $allowed_mimetypes[] = 'image/x-icon';



        $tmp_dir = $this->getTmpDir();

        if (($base64Content = base64_decode((string) $this->url, true)) && @imagecreatefromstring($base64Content)) {
            $tmp_name = uniqid() . '.png';
            if (strpos($tmp_name, '?')) {
                $tmp_name = \Illuminate\Support\Arr::first(explode('?', $tmp_name));
            }

            // get curl response headers
            if (!file_put_contents($tmp_dir . $tmp_name, $base64Content)) {
                $this->error = sprintf('Unable to write file "%s"', $tmp_dir . $tmp_name);
                return false;
            }

            return $this->uploadSingle($tmp_dir . $tmp_name, $allowed_mimetypes, $size);
        }

        $image_content = $this->getImage();
        if (!$image_content) {
            $headers = null;
            try {
                $headers = implode("\n", array_map(fn ($v, $k): string => $k . ': ' . $v, $this->headers, array_keys($this->headers)));
            } catch (\Throwable) {
                //
            }

            $this->error = $this->error.$headers ?: 'Image content is empty'.$headers;
            return false;
        }

        if ($this->allow_json && is_array($fromJson = json_decode($image_content, true))) {
            $result = $this->uploadJson($fromJson, $tmp_dir, $allowed_mimetypes, $size);
        } else {
            $pathInfo = pathinfo((string) $this->url);
            $extension = (isset($pathInfo['extension']) && $pathInfo['extension'] ? $pathInfo['extension'] : 'tmp');
            if(str_contains((string) $extension, '?')) {
                $extension = Arr::first(explode('?', $extension));
            }

            $tmp_name = uniqid() . '.' . $extension;
            if (strpos($tmp_name, '?')) {
                $tmp_name = \Illuminate\Support\Arr::first(explode('?', $tmp_name));
            }

            if (!file_put_contents($tmp_dir . $tmp_name, $image_content)) {
                $this->error = sprintf('Unable to write file "%s"', $tmp_dir . $tmp_name);
                return false;
            }

            $result = $this->uploadSingle($tmp_dir . $tmp_name, $allowed_mimetypes, $size);
        }

        return $result;
    }

    /**
     *
     */
    public function deleteOldFiles(): void
    {
        try {
//            $files = array_filter(glob($this->getTmpDir() . '*.{jpg,jpeg,png,gif,JPG,JPEG,PNG,GIF}', \GLOB_BRACE), function ($file): bool {
//                if (!is_file($file)) {
//                    return false;
//                }
//
//                return filemtime($file) < (\Carbon\Carbon::now()->timestamp - 1200);
//            });

            $extensions = ['jpg', 'jpeg', 'png', 'gif', 'JPG', 'JPEG', 'PNG', 'GIF'];
            $files = [];

            foreach ($extensions as $ext) {
                $files = array_merge($files, glob($this->getTmpDir() . ('*.' . $ext)));
            }

            $files = array_filter($files, function ($file): bool {
                if (!is_file($file)) {
                    return false;
                }

                return filemtime($file) < (\Carbon\Carbon::now()->timestamp - 1200);
            });

            array_map('unlink', $files);
        } catch (Exception) {
        }
    }

    /**
     * @param $array
     * @param $tmp_dir
     * @param $allowed_mimetypes
     * @param $size
     * @return array
     */
    protected function uploadJson($array, $tmp_dir, $allowed_mimetypes, $size)
    {
        $images = $this->extractImagesBase64($array);

        $images = array_map(function ($image) use ($tmp_dir, $allowed_mimetypes, $size): false|array {
            $tmp_name = uniqid('json_') . '.tmp';
            if (!file_put_contents($tmp_dir . $tmp_name, base64_decode($image))) {
                return false;
            }

            return $this->uploadSingle($tmp_dir . $tmp_name, $allowed_mimetypes, $size);
        }, array_unique($images));

        $images = array_filter($images);

        return $_FILES[$this->name] = array_values($images);
    }

    /**
     * @param $array
     * @return array
     */
    protected function extractImagesBase64($array): array
    {
        $images = [];
        foreach ($array as $k => $v) {
            if (is_array($v)) {
                $images = array_merge($images, $this->extractImagesBase64($v));
            } elseif (is_string($v) && base64_encode(base64_decode($v)) === $v) {
                $images[] = $v;
            }
        }

        return $images;
    }

    /**
     * @param $image_path
     * @param $allowed_mimetypes
     * @param $size
     * @return array|bool
     */
    protected function uploadSingle($image_path, $allowed_mimetypes, $size): false|array
    {
        if (!is_array($info = @getimagesize($image_path))) {
            $this->error = sprintf('"%s" is not valid image', $image_path);
            return false;
        }

        if (!in_array($info['mime'], $allowed_mimetypes)) {
            $this->error = sprintf('Mime "%s" is not allowed', $info['mime']);
            return false;
        }

        $ext = image_type_to_extension($info[2], false);

        if (strpos($ext, '?')) {
            $ext = \Illuminate\Support\Arr::first(explode('?', $ext));
        }

        $allowed_extensions = config('image.allowed_extensions');
        $allowed_extensions[] = 'ico';

        if (!in_array(strtolower((string) $ext), $allowed_extensions)) {
            $this->error = sprintf('Extension "%s" is not allowed', $ext);
            return false;
        }

        if (($info[0] < $size && $info[1] < $size)) {
            $this->error = sprintf('Image size "%sx%s" is not allowed', $info[0], $info[1]);
            return false;
        }

//        $pathinfo = pathinfo((string) $this->url);
//        if(str_contains((string) $pathinfo['filename'], '?')) {
//            $pathinfo['filename'] = Arr::first(explode('?', $pathinfo['filename']));
//        }

        $filename = $this->file_name ? : (uniqid() . '-' . uniqid());

        return $_FILES[$this->name] = [
            "name" => $filename . '.' . $ext,
            "type" => $info['mime'],
            "tmp_name" => $image_path,
            "error" => 0,
            "size" => filesize($image_path),
            "from_url" => true
        ];
    }

    /**
     * @return string
     */
    private function getTmpDir(): string
    {
        return sys_get_temp_dir() . '/';
    }

    /**
     * @return string
     */
    public function getError()
    {
        return $this->error;
    }

    /**
     * @return int
     */
    public function getStatus()
    {
        return $this->error_status;
    }

    /**
     * @return bool|string
     */
    private function getImage(?string $image = null)
    {
        $image = $image ?: $this->url;
        $image = trim((string) $image);
        if (@is_file($image)) {
            return file_get_contents($image);
        }

        //        $http = new Client();
        //
        //        try {
        //            $result = $http->get($image, [
        //                RequestOptions::TIMEOUT => 15,
        //                RequestOptions::CONNECT_TIMEOUT => 15,
        //                RequestOptions::READ_TIMEOUT => 15,
        //                RequestOptions::ALLOW_REDIRECTS => ['strict' => true, 'max' => 3],
        //                RequestOptions::HEADERS => [
        //                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        //                ],
        //            ]);
        //        } catch (\Throwable $e) {
        //            $this->error = sprintf('cUrl error: %d', $e->getMessage());
        //            return false;
        //        }
        //
        //        $status = $result->getStatusCode();
        //        if($status != 200) {
        //            if($status > 0) {
        //                $this->error_status = $status;
        //            }
        //            $this->error = sprintf('cUrl status: %d', $status);
        //            return false;
        //        }
        //
        //        try {
        //            $result = $result->getBody()->getContents();
        //        } catch (\Throwable $e) {
        //            //
        //        }
        //
        //        if(empty($result)) {
        //            $this->error = 'CURL response is empty';
        //            return false;
        //        }
        //
        //        return $result;

        $validateHost = parse_url(str_replace(' ', '%20', $image), PHP_URL_HOST);
        if (empty($validateHost) || !str_contains($validateHost, '.')) {
            $this->error = sprintf('Image url is empty or is not valid: "%s"', str_replace(' ', '%20', $image));
            $this->error_status = -100;
            return false;
        }

        $http = new Curl();
        $http->useCurl(function_exists('curl_exec'));
        //        $http->setUseragent('Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/33.0.1750.154 Safari/537.36');
        $http->setUseragent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        $http->setMethod('GET');
        $http->setVerify(false);
        $http->followRedirects(true);
        $http->setMaxredirect(3);
        $http->setTarget(str_replace(' ', '%20', $image));
        $http->setTimeout(15);
        $http->execute();

        $this->headers = $http->getHeaders();
        $status = $http->getStatus();
        if ($status != 200) {
            if ($status > 0) {
                $this->error_status = $status;
            }

            $this->error = sprintf('cUrl status: %d', $status);
            return false;
        }

        $error = $http->getError();
        if ($error) {
            $this->error = $error;

            return false;
        }

        if (empty($result = $http->getResult())) {
            $this->error = 'CURL response is empty';
            return false;
        }

        return $result;
    }

    public function setFileName(null|string $file_name): UrlImageToFile
    {
        $this->file_name = when($file_name, fn($file_name) => (Str::lower(Str::substr(Str::slug($file_name), 0, 150)) . '-' . uniqid()));
        return $this;
    }

}
