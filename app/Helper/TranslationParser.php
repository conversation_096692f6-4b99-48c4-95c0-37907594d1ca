<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 27.10.2016 г.
 * Time: 11:00 ч.
 */

namespace App\Helper;

use File;
use Illuminate\Support\Str;
use Symfony\Component\Finder\SplFileInfo;
use Exception;

class TranslationParser
{
    /**
     * @var array
     */
    protected $languages = [];

    /**
     * @var null|string
     */
    protected $group;

    /**
     * @var boolean
     */
    protected $force;

    /**
     * @var array
     */
    protected $files = [];

    /**
     * @var array
     */
    protected $dirs = [];

    /**
     * @var array
     */
    protected $ignore = [];

    /**
     * @var array
     */
    protected $ignore_ns = [];

    /**
     * @var array
     */
    protected $cp = [];

    /**
     * @var array
     */
    protected $core = [];

    /**
     * @var array
     */
    protected $sf = [];

    /**
     * @var array
     */
    protected $log = ['front' => [], 'cp' => [], 'core' => []];

    /**
     * @var string
     */
    protected $defaultPattern = "/(__|lang|trans|trans_choice)\(([\"\'])([^\)\'\"]*)(\\2)(\s?,\s?([^\)]*))?\)/ismU";

    /**
     * @var string
     */
    protected $standardPatternTpl = "/\{\s*(t)\s*([^\}]*)\}([^\{]*)\{\/\\1\}/";

    /**
     * @var string
     */
    protected $pluralTpl = '/plural\s*=\s*["\']?\s*(.[^\"\']*)\s*["\']?/';

    /**
     * @var boolean
     */
    protected $remove_not_existing = false;

    public function __construct()
    {
    }

    /**
     * @param array $languages
     * @return $this
     */
    public function setLanguages(array $languages): static
    {
        $this->languages = $languages;
        return $this;
    }

    /**
     * @param boolean $force
     * @return $this
     */
    public function setForce($force): static
    {
        $this->force = $force;
        return $this;
    }

    /**
     * @param string|null $group
     * @return $this
     */
    public function setGroup($group): static
    {
        $this->group = $group;
        return $this;
    }

    /**
     * @param array|string $dir
     * @return $this
     */
    public function setDirs($dir): static
    {
        $this->dirs = array_merge($this->dirs, (array)$dir);
        return $this;
    }

    /**
     * @param array|string $dir
     * @return $this
     */
    public function setIgnore($dir): static
    {
        $this->ignore = array_merge($this->ignore, array_map(fn ($path) => str_replace(['\\','/'], DIRECTORY_SEPARATOR, $path), (array)$dir));
        return $this;
    }

    /**
     * @param array|string $ns
     * @return $this
     */
    public function setIgnoreNs($ns): static
    {
        $this->ignore_ns = array_merge($this->ignore_ns, (array)$ns);
        return $this;
    }

    /**
     * @return array
     */
    public function getIgnore()
    {
        return $this->ignore;
    }

    /**
     * @return array
     */
    public function getIgnoreNs()
    {
        return $this->ignore_ns;
    }

    /**
     * @param SplFileInfo $file
     * @return bool
     */
    public function isIgnored(SplFileInfo $file): bool
    {
        foreach ($this->getIgnore() as $path) {
            if (str_starts_with($file->getRealPath(), (string) $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param array|string $file
     * @return $this
     */
    public function setFiles($file): static
    {
        $this->files = array_merge($this->files, (array)$file);
        return $this;
    }

    /**
     * @param bool $remove_not_existing
     */
    public function parse($remove_not_existing = false): void
    {
        if ($this->dirs) {
            $this->files = array_merge($this->files, File::allFiles($this->dirs));
        }

        $this->remove_not_existing = $remove_not_existing;

        /** @var \Symfony\Component\Finder\SplFileInfo $file */
        foreach ($this->files as $file) {
            if ($this->isIgnored($file)) {
                continue;
            }

            $extension = Str::lower($file->getExtension());
            $method = '_parse' . Str::ucfirst($extension);
            if (method_exists($this, $method)) {
                $this->$method($file);
            }
        }

        foreach ($this->languages as $code => $language) {
            $this->_saveCore($code);
            $this->_saveFront($code);
            $this->_saveCp($code);
        }
    }

    /**
     * @return array
     */
    public function getLog()
    {
        return $this->log;
    }

    /**
     * @param SplFileInfo $file
     * @throws Exception
     */
    private function _parsePhp(SplFileInfo $file): void
    {
        if (($size = $file->getSize()) <= 0) {
            return;
        }

        $content = $file->openFile()->fread($size);
        if (!$content) {
            return;
        }

        if (preg_match_all($this->defaultPattern, $content, $newMatches)) {
            $matches = array_filter($newMatches[3]);
            foreach ($matches as $match) {
                $this->_add($this->_fs($match), $file->getPathname());
            }
        }
    }

    /**
     * @param SplFileInfo $file
     * @throws Exception
     */
    private function _parseTpl(SplFileInfo $file): void
    {
        if (($size = $file->getSize()) <= 0) {
            return;
        }

        $content = $file->openFile()->fread($size);
        if (!$content) {
            return;
        }

        if (preg_match_all($this->standardPatternTpl, $content, $standardMatches)) {
            foreach ($standardMatches[0] as $i => $match) {
                if (!preg_match($this->pluralTpl, $standardMatches[2][$i])) {
                    $this->_add($this->_fs($standardMatches[3][$i]), $file->getPathname());
                }
            }
        }

        if (preg_match_all($this->defaultPattern, $content, $newMatches)) {
            $matches = array_filter($newMatches[3]);
            foreach ($matches as $match) {
                $this->_add($this->_fs($match), $file->getPathname());
            }
        }
    }

    /**
     * @param $str
     *
     * @return mixed|string
     */
    private function _fs($str): string|array
    {
        $str = stripslashes((string) $str);
        $str = str_replace('"', '\"', $str);
        $str = str_replace("\n", '\n', $str);
        return $str;
    }

    /**
     * @param $val
     * @param $source
     *
     * @return mixed|void
     * @throws Exception
     */
    private function _add(string|array $val, $source): void
    {

        $val = stripslashes((string) $val);

        if (mb_strpos($val, '.') !== false) {
            [$file, $string] = explode('.', $val, 2);
        } else {
            if ($this->force) {
                return;
            } else {
                throw new Exception(sprintf('invalid string - %s in file %s', $val, $source));
            }
        }

        if (strpos($file, '::')) {
            return;
        }

        if (in_array($file, $this->getIgnoreNs())) {
            return;
        }

        if ($file == 'sf') {
            $this->sf[$string] = '';
        } elseif ($file == 'core') {
            $this->core[$string] = '';
        } else {
            $this->cp[$file][$string] = '';
            if (in_array($file, [/*'widget',*/ 'seo'])) {
                if ($file == 'seo' && str_contains($string, 'err.')) {
                    return;
                }

                $this->sf[$file . '.' . $string] = '';
            }
        }
    }

    /**
     * @param $string
     *
     * @return bool
     */
    private function _checkMatch($string): bool
    {

        if (empty($string)) {
            return false;
        }

        $pattern = '/\$(?!([ds]))/us';
        if (preg_match($pattern, (string) $string) > 0) {
            return false;
        } else {
            $pattern = '/\$[a-z]{2,}/us';
            if (preg_match($pattern, (string) $string) > 0) {
                return false;
            }

            return true;
        }

    }

    /**
     * @param $language
     */
    private function _saveCore(string $language): void
    {
        $file = lang_path($language . '/core.php');
        $data = is_file($file) ? include $file : [];

        $missing_translations = array_diff_key($this->core, $data);
        if ($missing_translations) {
            if ($this->groupCheck('core')) {
                $this->log['core'][] = [
                    'language' => $this->languages[$language],
                    'file' => 'core',
                    'missing' => $missing_translations,
                    'action' => 'add'
                ];
            }
        }

        $data = $missing_translations ? array_merge($data, $missing_translations) : $data;

        if ($this->remove_not_existing) {
            $removed_translations = array_diff_key($data, $this->core);
            if ($removed_translations) {
                \Illuminate\Support\Arr::forget($data, array_keys($removed_translations));
                if ($this->groupCheck('core')) {
                    $this->log['core'][] = [
                        'language' => $this->languages[$language],
                        'file' => 'core',
                        'missing' => $removed_translations,
                        'action' => 'remove'
                    ];
                }
            }
        }

        ksort($data);

        if (!$this->groupCheck('core')) {
            return;
        }

        File::makeDirectory(dirname($file), 0777, true, true);
        File::put($file, '<?php return ' . var_export($data, true) . ';');
    }

    /**
     * @param $language
     */
    private function _saveFront(string $language): void
    {
        $file = lang_path($language . '/sf.php');
        $data = is_file($file) ? include $file : [];

        $missing_translations = array_diff_key($this->sf, $data);
        if ($missing_translations) {
            if ($this->groupCheck('sf')) {
                $this->log['front'][] = [
                    'language' => $this->languages[$language],
                    'file' => 'sf',
                    'missing' => $missing_translations,
                    'action' => 'add'
                ];
            }
        }

        $data = $missing_translations ? array_merge($data, $missing_translations) : $data;

        if ($this->remove_not_existing) {
            $removed_translations = array_diff_key($data, $this->sf);
            if ($removed_translations) {
                \Illuminate\Support\Arr::forget($data, array_keys($removed_translations));
                if ($this->groupCheck('sf')) {
                    $this->log['front'][] = [
                        'language' => $this->languages[$language],
                        'file' => 'sf',
                        'missing' => $removed_translations,
                        'action' => 'remove'
                    ];
                }
            }
        }

        ksort($data);

        if (!$this->groupCheck('sf')) {
            return;
        }

        File::makeDirectory(dirname($file), 0777, true, true);
        File::put($file, '<?php return ' . var_export($data, true) . ';');
    }

    /**
     * @param $language
     */
    private function _saveCp(string $language): void
    {
        foreach ($this->cp as $filename => $items) {
            $file = lang_path($language . '/'.$filename.'.php');
            $data = is_file($file) ? include $file : [];
            if (!is_array($data)) {
                $data = [];
            }

            $missing_translations = array_diff_key($items, $data);
            if ($missing_translations) {
                if ($this->groupCheck($filename)) {
                    $this->log['cp'][] = [
                        'language' => $this->languages[$language],
                        'file' => $filename,
                        'missing' => $missing_translations,
                        'action' => 'add'
                    ];
                }
            }

            $data = $missing_translations ? array_merge($data, $missing_translations) : $data;

            if ($this->remove_not_existing) {
                $removed_translations = array_diff_key($data, $items);
                if ($removed_translations) {
                    \Illuminate\Support\Arr::forget($data, array_keys($removed_translations));
                    if ($this->groupCheck($filename)) {
                        $this->log['cp'][] = [
                            'language' => $this->languages[$language],
                            'file' => $filename,
                            'missing' => $removed_translations,
                            'action' => 'remove'
                        ];
                    }
                }
            }

            ksort($data);

            if (!$this->groupCheck($filename)) {
                continue;
            }

            File::makeDirectory(dirname($file), 0777, true, true);
            File::put($file, '<?php return ' . var_export($data, true) . ';');
        }
    }

    /**
     * @param $key
     * @return bool
     */
    private function groupCheck($key): bool
    {
        if (!$this->group) {
            return true;
        }

        if ($this->group != $key) {
            return false;
        }

        return true;
    }

}
