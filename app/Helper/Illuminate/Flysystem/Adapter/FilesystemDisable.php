<?php

declare(strict_types=1);

namespace App\Helper\Illuminate\Flysystem\Adapter;

use League\Flysystem\FilesystemAdapter;
use League\Flysystem\UnableToWriteFile;
use League\Flysystem\UnableToDeleteFile;
use League\Flysystem\UnableToRetrieveMetadata;
use League\Flysystem\UnableToReadFile;
use League\Flysystem\UnableToCreateDirectory;

class FilesystemDisable implements FilesystemAdapter
{
    /**
     * @param string $path
     * @return mixed
     */
    public function fileExists(string $path): bool
    {
        return false;
    }

    /**
     * @param string $path
     * @return mixed
     */
    public function directoryExists(string $path): bool
    {
        return false;
    }

    /**
     * @param string $path
     * @param string $contents
     * @param array|League\Flysystem\Config $config
     * @return mixed
     */
    public function write(string $path, string $contents, array|\League\Flysystem\Config $config): void
    {
        throw UnableToWriteFile::atLocation($path, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @param mixed $contents
     * @param array|League\Flysystem\Config $config
     * @return mixed
     */
    public function writeStream(string $path, $contents, array|\League\Flysystem\Config $config): void
    {
        throw UnableToWriteFile::atLocation($path, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @return mixed
     */
    public function read(string $path): string
    {
        throw UnableToReadFile::fromLocation($path, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @return mixed
     */
    public function readStream(string $path)
    {
        throw UnableToReadFile::fromLocation($path, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @return mixed
     */
    public function delete(string $path): void
    {
        throw UnableToDeleteFile::atLocation($path, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @return mixed
     */
    public function deleteDirectory(string $path): void
    {
        throw UnableToDeleteFile::atLocation($path, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @param array|League\Flysystem\Config $config
     * @return mixed
     */
    public function createDirectory(string $path, array|\League\Flysystem\Config $config): void
    {
        throw UnableToCreateDirectory::atLocation($path, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @param bool $deep
     * @return mixed
     */
    public function listContents(string $path, bool $deep): iterable
    {
        return [];
    }

    /**
     * @param string $source
     * @param string $destination
     * @param array|League\Flysystem\Config $config
     * @return mixed
     */
    public function move(string $source, string $destination, array|\League\Flysystem\Config $config): void
    {
        throw UnableToWriteFile::atLocation($destination, 'Storage is disabled.');
    }

    /**
     * @param string $source
     * @param string $destination
     * @param array|League\Flysystem\Config $config
     * @return mixed
     */
    public function copy(string $source, string $destination, array|\League\Flysystem\Config $config): void
    {
        throw UnableToWriteFile::atLocation($destination, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @return mixed
     */
    public function lastModified(string $path): \League\Flysystem\FileAttributes
    {
        throw UnableToRetrieveMetadata::lastModified($path, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @return mixed
     */
    public function fileSize(string $path): \League\Flysystem\FileAttributes
    {
        throw UnableToRetrieveMetadata::fileSize($path, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @return mixed
     */
    public function mimeType(string $path): \League\Flysystem\FileAttributes
    {
        throw UnableToRetrieveMetadata::mimeType($path, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @return mixed
     */
    public function visibility(string $path): \League\Flysystem\FileAttributes
    {
        throw UnableToRetrieveMetadata::visibility($path, 'Storage is disabled.');
    }

    /**
     * @param string $path
     * @param string $visibility
     * @return mixed
     */
    public function setVisibility(string $path, string $visibility): void
    {
        throw UnableToRetrieveMetadata::visibility($path, 'Storage is disabled.');
    }
}
