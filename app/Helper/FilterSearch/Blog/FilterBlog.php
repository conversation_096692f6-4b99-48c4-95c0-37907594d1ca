<?php

declare(strict_types=1);

namespace App\Helper\FilterSearch\Blog;

use App\Helper\FilterSearch\AbstractFilter;
use App\Models\Blog\Blog;
use App\Traits\Filters\SearchFilter;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class FilterBlog
 *
 * Class handles all blog filters for the administrative panel.
 * Class is used to construct complex select queries, which select only a specific set of blogs.
 * Class extends App\Helper\FilterSearch\AbstractFilter and sets relevant properties of the class.
 * Properties include, but may not be limited to the following: where clause, filters strings, etc.
 *
 * Class methods set a filter object's where and join clauses as a key => value array. This is done so that multiple joins or where clauses related with the same fields cannot occur.
 * After the class methods have build the query, filters are set in the $filter_object. These filters are used to track the currently applied filters and to display the filter pill in the item listings.
 *
 * @package lib\module\blog
 */
class FilterBlog extends AbstractFilter
{
    use SearchFilter;

    /**
     * Function filters blogs by their comment setting.
     *
     * @param $value
     *
     * @throws \App\Exceptions\Error
     */
    public function filterComments($value): void
    {
        if (empty($value) || !in_array($value, Blog::$blog_comment_types)) {
            return;
        }

        $this->setWhere('comments', function ($query) use ($value): void {
            /** @var Builder $query */
            $query->where('comments', $value);
        });

        if ($this->_return_string) {
            $this->_filters['comments'] = sprintf(__('blog.filter.comments_%1$s'), $value);
        }
    }

    /**
     * @return array
     */
    public function getSavedSearchQuery(): array
    {
        return [
            'module' => 'blog',
            'filter' => 'blog'
        ];
    }
}
