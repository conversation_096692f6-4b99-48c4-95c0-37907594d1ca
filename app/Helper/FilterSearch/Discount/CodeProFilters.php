<?php

declare(strict_types=1);

namespace App\Helper\FilterSearch\Discount;

use App\Common\DateTimeFormat;
use App\Traits\Filters\SearchFilter;
use App\Traits\Filters\StatusBoolFilter;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use App\Helper\FilterSearch\AbstractFilter;

class CodeProFilters extends AbstractFilter
{
    use SearchFilter;
    use StatusBoolFilter;

    protected $name_column = ['name', 'code'];

    protected $status_column = 'active';

    /**
     * @param mixed $data
     * @return mixed
     */
    public function filterTimesUsed($data): void
    {
        $operator = isset($data['operator']) ? intval($data['operator']) : null;
        $value = isset($data['value']) ? intval($data['value']) : null;

        if (!array_key_exists($operator, self::$_compare_operators)) {
            return;
        }

        $operator = self::$_compare_operators[$operator];

        $this->setWhere('timesUsed', function ($query) use ($operator, $value): void {
            /** @var Builder $query */
            $query->where('uses', $operator, $value);
        });

        if ($this->_return_string) {
            $this->_filters['timesUsed'] = sprintf(__('discount.filter.used_%1$s_%2$s_times'), $operator, $value);
        }
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    public function filterUsesLeft($data): void
    {
        $operator = isset($data['operator']) ? intval($data['operator']) : null;
        $value = isset($data['value']) ? intval($data['value']) : null;

        if (!array_key_exists($operator, self::$_compare_operators)) {
            return;
        }

        $operator = self::$_compare_operators[$operator];

        $this->setWhere('usesLeft', function ($query) use ($operator, $value): void {
            /** @var Builder $query */
            if ($operator == '>' || $operator == '<>') {
                $query->whereRaw(sprintf("(`discounts`.`max_uses` IS NULL OR (CAST(`discounts`.`max_uses` AS INT) - CAST(`discounts`.`uses` AS INT)) %s '%s')", $operator, $value));
            } else {
                $query->whereRaw(sprintf("(`discounts`.`max_uses` IS NOT NULL AND (CAST(`discounts`.`max_uses` AS INT) - CAST(`discounts`.`uses` AS INT)) %s '%s')", $operator, $value));
            }
        });

        if ($this->_return_string) {
            $this->_filters['usesLeft'] = sprintf(__('discount.filter.uses_left_%1$s_%2$s'), $operator, $value);
        }
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    public function filterStartDate($data): void
    {
        $operator = isset($data['operator']) ? intval($data['operator']) : null;
        $value = isset($data['value']) ? $this->quote($data['value']) : null;

        if (!array_key_exists($operator, self::$_compare_operators_date)) {
            return;
        }

        $operator_string = self::$_compare_operators_date[$operator];

        $dateTimeObject = Carbon::createFromFormat(DateTimeFormat::getSiteCurrentDateFormat()['format'], $value);
        $dateTimeErrors = Carbon::getLastErrors();

        if ($dateTimeErrors['error_count'] > 0) {
            return;
        }

        if (!empty($dateTimeErrors['warning_count'])) {
            return;
        }

        $this->setWhere('startDate', function ($query) use ($operator_string, $dateTimeObject): void {
            /** @var Builder $query */
            $query->where('date_start', $operator_string, $dateTimeObject);
        });

        if ($this->_return_string) {
            $this->_filters['startDate'] = sprintf(
                __('discount.filter.start_date_%1$s_%2$s'),
                self::$_compare_operators_strings[$operator],
                $value
            );
        }
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    public function filterEndDate($data): void
    {
        $operator = isset($data['operator']) ? intval($data['operator']) : null;
        $value = isset($data['value']) ? $this->quote($data['value']) : null;

        if (!array_key_exists($operator, self::$_compare_operators_date)) {
            return;
        }

        $operator_string = self::$_compare_operators_date[$operator];

        $dateTimeObject = Carbon::createFromFormat(DateTimeFormat::getSiteCurrentDateFormat()['format'], $value);
        $dateTimeErrors = Carbon::getLastErrors();

        if ($dateTimeErrors['error_count'] > 0) {
            return;
        }

        if (!empty($dateTimeErrors['warning_count'])) {
            return;
        }

        $this->setWhere('endDate', function ($query) use ($operator_string, $dateTimeObject): void {
            /** @var Builder $query */
            if ($operator_string == '>') {
                $query->where('date_end', $operator_string, $dateTimeObject)
                    ->orWherenull('date_end');
            } else {
                $query->where('date_end', $operator_string, $dateTimeObject);
            }
        });

        if ($this->_return_string) {
            $this->_filters['endDate'] = sprintf(
                __('discount.filter.start_date_%1$s_%2$s'),
                self::$_compare_operators_strings[$operator],
                $value
            );
        }

    }

    /**
     * @return array
     */
    public function getSavedSearchQuery(): array
    {
        return [
            'module' => static::class,
            'filter' => null
        ];
    }
}
