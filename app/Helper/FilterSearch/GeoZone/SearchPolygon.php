<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 4.9.2017 г.
 * Time: 12:00 ч.
 */

namespace App\Helper\FilterSearch\GeoZone;

use App\Helper\Text;
use App\Helper\FilterSearch\AbstractFilter;

class SearchPolygon extends AbstractFilter
{
    /**
     * @param mixed $query
     * @return mixed
     */
    public function filterQuery($query): void
    {
        if (empty($query)) {
            return;
        }

        $words = Text::getKeywords($query);
        if (empty($words)) {
            return;
        }

        $words = Text::generateEscaped($words);

        $where_clause = [''];
        foreach ($words as $word) {
            $where_clause[] .= sprintf("(`name` LIKE '%%%s%%')", $word);
        }

        $this->setWhere('query', implode(' AND ', $where_clause));
        if ($this->_return_string) {
            $this->_filters['query'] = $query;
        }
    }

    /**
     * @return array
     */
    public function getSavedSearchQuery(): array
    {
        return [
            'module' => static::class,
            'filter' => null
        ];
    }
}
