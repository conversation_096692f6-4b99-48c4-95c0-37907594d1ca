<?php

declare(strict_types=1);

namespace App\Helper\WebHooks\Order;

use App\Helper\WebHooks\AbstractWebHook;
use App\Models\Order\Order;
use App\Models\Order\OrderPayment;

class OrderPaymentWebHook extends AbstractWebHook
{
    /**
     * @param \App\Models\Order\OrderPayment $_orderPayment
     * @param \App\Models\Order\Order $_order
     * @return mixed
     */
    public function __construct(protected \App\Models\Order\OrderPayment $_orderPayment, protected \App\Models\Order\Order $_order)
    {
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public function toArray(): array
    {
        return [
            'id' => $this->_orderPayment->getKey(),
//            'id' => $this->_orderPayment->payment_provider->getKey(),
            'provider_reference_id' => $this->_orderPayment->provider_reference_id,
            'provider_name' => $this->_orderPayment->provider_name,
            'provider' => $this->_orderPayment->provider,
            'amount' => moneyFloat($this->_orderPayment->amount, $this->_order->getCurrency()),
            'status' => $this->_orderPayment->status,
            'created_at' => $this->carbonToIso8601($this->_orderPayment->date_added),
            'updated_at' => $this->carbonToIso8601($this->_orderPayment->date_last_update),

        ];
    }
}
