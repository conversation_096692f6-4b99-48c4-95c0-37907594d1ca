<?php

declare(strict_types=1);

namespace App\Helper\Catalog;

use Illuminate\Support\Collection;
use Modules\Apps\Administration\GroceryStore\Models\Units;

/**
 * UnitFormatter provides utility methods to format, convert, and calculate values
 * based on a hierarchical structure of measurement units.
 */
class UnitFormatter
{
    /**
     * Retrieve all unit steps grouped by their hierarchy.
     *
     * @return Collection|Units[][] Collection of unit steps.
     */
    public static function getSteps(): Collection
    {
        return Units::getSteps();
    }

    /**
     * Get all units within the same group as the given unit ID.
     *
     * @param int|null $unit_id The ID of the unit to retrieve the group for.
     * @return Collection Collection of units in the same group, indexed by their IDs.
     */
    public static function getUnitGroup(?int $unit_id): Collection
    {
        if (!$unit_id) {
            return collect();
        }

        $units = static::getSteps()->filter->firstWhere('id', $unit_id)->first();

        return with($units ?: collect())->keyBy('id');
    }

    /**
     * Retrieve a specific unit by its ID.
     *
     * @param int|null $unit_id The ID of the unit.
     * @return null|Units The unit instance or null if not found.
     */
    public static function getUnit(?int $unit_id): ?Units
    {
        return static::getUnitGroup($unit_id)->get($unit_id);
    }

    /**
     * Retrieve the main (root) unit of the given unit group.
     *
     * @param int $unit_id The ID of the unit.
     * @return null|Units The main unit or null if not found.
     */
    public static function getMainUnit(int $unit_id): ?Units
    {
        return static::getUnitGroup($unit_id)->first(fn (Units $unit): bool => is_null($unit->parent_id));
    }

    /**
     * Format a value with its associated unit.
     *
     * @param int $unit_id The ID of the unit.
     * @param float $value The value to format.
     * @return string|null Formatted value with the unit name or null if the unit is not found.
     */
    public static function formatValue(int $unit_id, float $value): ?string
    {
        if (($unit = static::getUnit($unit_id)) === null) {
            return null;
        }

        return sprintf('%s %s', number_format($value, $unit->decimals, '.', ''), $unit->short_name ?: $unit->name);
    }

    /**
     * Format a value and price relative to the main unit in the hierarchy.
     *
     * @param int|null $unit_id The ID of the unit.
     * @param float|null $value The value to convert and format.
     * @param int|null $price The price associated with the value.
     * @param int $base_value The base value for formatting.
     * @return string|null Formatted value and price or null if the unit is not found.
     */
    public static function formatValueToMain(?int $unit_id, ?float $value, ?int $price, int $base_value = 1): ?string
    {
        if (!$unit_id || $base_value < 1 || $base_value > PHP_INT_MAX || !$value || is_null($price)) {
            return null;
        }

        $units = static::getUnitGroup($unit_id);
        /** @var Units|null $unit */
        if (!($unit = $units->get($unit_id))) {
            return null;
        }

        $lastUnit = $unit;
        while ($unit) {
            $value /= $unit->steps;
            $unit = $unit->parent_id ? $units->get($unit->parent_id) : null;

            if ($unit) {
                $lastUnit = $unit;
            }
        }

        if ($base_value != 1) {
            return sprintf(
                '%s / %d %s',
                money($price / $value),
                $base_value,
                $lastUnit->short_name ?? $lastUnit->name
            );
        }

        return sprintf(
            '%s / %s',
            money($price / $value),
            $lastUnit->short_name ?? $lastUnit->name
        );
    }

    /**
     * Format a value and price relative to a specific base unit in the hierarchy.
     *
     * @param int|null $unit_id The ID of the unit.
     * @param int|null $base_unit_id The ID of the base unit to convert to.
     * @param float|null $value The value to convert and format.
     * @param int|null $price The price associated with the value.
     * @param int $base_value The base value for formatting.
     * @return string|null Formatted value and price or null if the unit is not found.
     */
    public static function formatValueToBase(?int $unit_id, ?int $base_unit_id, ?float $value, ?int $price, int $base_value = 1): ?string
    {
        if (!$unit_id || !$base_unit_id || $base_value < 1 || $base_value > PHP_INT_MAX || !$value || is_null($price)) {
            return null;
        }

        $units = static::getUnitGroup($unit_id);
        /** @var Units|null $unit */
        if (!($unit = $units->get($unit_id))) {
            return null;
        }

        $isDescending = static::isBaseUnitDescendant($unit, $base_unit_id);
        $lastUnit = $unit;
        if ($unit_id != $base_unit_id) {
            while ($unit) {
                if ($unit->id == $base_unit_id) {
                    break;
                }

                if ($isDescending) {
                    $value /= $unit->steps;
                    $unit = $unit->parent_id ? $units->get($unit->parent_id) : null;
                } else {
                    $value *= $unit->steps;
                    $unit = $units->firstWhere('parent_id', $unit->id);
                }

                if ($unit) {
                    $lastUnit = $unit;
                }
            }

            $price = $price / $value;
            if ($isDescending) {
                $price *= $base_value;
            } else {
                $price *= ($base_value / $lastUnit->steps);
            }
        } else {
            $price *= ($base_value / $value);
        }

        if ($base_value != 1) {
            return sprintf(
                '%s / %d %s',
                money($price),
                $base_value,
                $lastUnit->short_name ?? $lastUnit->name
            );
        }

        return sprintf(
            '%s / %s',
            money($price),
            $lastUnit->short_name ?? $lastUnit->name
        );
    }

    /**
     * Determine whether the base unit is an ancestor of the given unit.
     *
     * @param Units $unit The current unit.
     * @param int $baseUnitId The ID of the base unit.
     * @return bool True if the base unit is an ancestor; otherwise, false.
     */
    public static function isBaseUnitDescendant(Units $unit, int $baseUnitId): bool
    {
        $units = static::getUnitGroup($unit->id);

        while ($unit) {
            if ($unit->id == $baseUnitId) {
                return true;
            }

            // Move to the parent unit, if it exists
            $unit = $unit->parent_id ? $units->get($unit->parent_id) : null;
        }

        return false;
    }
}
