<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 2/19/2018
 * Time: 11:42 AM
 */

namespace App\Helper\DataBase;

use App\Models\Router\Site;
use Illuminate\Database\DatabaseManager;
use Illuminate\Database\QueryException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Schema;
use Throwable;

/**
 * Class DynamicConnection
 * @package App\Helper\DataBase
 */
class DynamicConnection
{
    /**
     * @param Site $site
     * @return bool
     */
    public static function databaseExists(Site $site)
    {
        static::reconnect($site->getRouterConnectionConfig());
        return static::databaseExistsByName('cc_site_' . $site->site_id);
    }

    /**
     * @param string $name
     * @return bool
     */
    public static function databaseExistsByName($name)
    {
        try {
            $sql = /** @lang mysql */
                sprintf("SELECT COUNT(*) AS total FROM INFORMATION_SCHEMA . SCHEMATA WHERE SCHEMA_NAME = '%s'", $name);

            return (bool)\Illuminate\Support\Facades\DB::connection('dynamic')->selectOne($sql)->total;
        } catch (QueryException) {
            return false;
        }
    }

    /**
     * Change router connection host and reconnect
     * @param $databaseConfig
     */
    public static function reconnect($databaseConfig): void
    {
        static::disconnect();
        static::connect($databaseConfig);
    }

    public static function disconnect(): void
    {
        if (in_array('dynamic', array_keys(\Illuminate\Support\Facades\DB::getConnections()))) {
            $app = app();
            /** @var $db DatabaseManager */
            $db = $app->make('db');
            $db->purge('dynamic');
            $db->disconnect();
        }
    }

    /**
     * @param mixed $databaseConfig
     * @return mixed
     */
    public static function connect($databaseConfig): void
    {
        $app = app();
        $app->make('config')->set('database.connections.dynamic', $databaseConfig);
    }

    public static function refresh(): void
    {
        $app = app();
        $app->make('config')->set('database.connections.dynamic', $app->make('config')->get('database.connections.dynamic-for-refresh'));
    }

    /**
     * @param $dbName
     */
    public static function createDatabaseIfNotExists($dbName): void
    {
        \Illuminate\Support\Facades\DB::connection('dynamic')->statement(sprintf('CREATE DATABASE IF NOT EXISTS `%s` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci', $dbName));
    }

    /**
     * @return Collection
     */
    public static function processList(): Collection
    {
        return collect(\Illuminate\Support\Facades\DB::connection('dynamic')->select("SHOW FULL PROCESSLIST"));
    }

    /**
     * @param int $processId
     * @return bool|string
     * @throws Throwable
     */
    public static function killProcess(int $processId)
    {
        try {
            return \Illuminate\Support\Facades\DB::connection('dynamic')->statement("KILL " . $processId);
        } catch (Throwable $throwable) {
            if (strpos($throwable->getMessage(), 'Unknown thread')) {
                return true;
            }

            return $throwable->getMessage();
        }
    }

    /**
     * @param $dbName
     */
    public static function dropDatabaseIfExists($dbName): void
    {
        \Illuminate\Support\Facades\DB::connection('dynamic')->statement(sprintf('DROP DATABASE IF EXISTS `%s`', $dbName));
    }

    /**
     * @param Site $site
     * @param string $db
     * @param $mysqlPassword
     */
    public static function createMysqlUserQuery(Site $site, $db, $mysqlPassword): void
    {
        // grant all on *.* to `router`@`%` identified by '.4xf3^U^p&[}?<g&' WITH GRANT OPTION
        \Illuminate\Support\Facades\DB::connection('dynamic')->statement(sprintf("grant all on `%s`.* to `web_%s`@`%%` identified by '%s'", $db, $site->site_id, $mysqlPassword));
        \Illuminate\Support\Facades\DB::connection('dynamic')->statement(sprintf("grant all on `%s`.* to `cli_%s`@`%%` identified by '%s'", $db, $site->site_id, $mysqlPassword));
        \Illuminate\Support\Facades\DB::connection('dynamic')->statement('flush privileges');
    }

    /**
     * @param Site $site
     * @return int
     * @throws \App\Exceptions\Error
     * @throws \App\Exceptions\Fault
     */
    public static function getSiteTotalRecords(Site $site): int|float
    {
        //        static::reconnect($site->getRouterConnectionConfig());
        //
        //        $sql = /** @lang mysql */
        //            "SELECT SUM(TABLE_ROWS) as total
        //             FROM INFORMATION_SCHEMA.TABLES
        //             WHERE TABLE_SCHEMA = 'cc_site_{$site->getKey()}'";
        //
        //        return (int)\Illuminate\Support\Facades\DB::connection('dynamic')->selectOne($sql)->total;
        $site->bootDB();

        $total = 0;
        $tables = Schema::getTableListing();
        foreach ($tables as $table) {
            $total += \Illuminate\Support\Facades\DB::table($table)->count();
        }

        return $total;
    }
}
