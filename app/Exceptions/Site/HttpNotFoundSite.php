<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 16.6.2016 г.
 * Time: 09:44 ч.
 */

namespace App\Exceptions\Site;

use Exception;

class HttpNotFoundSite extends AbstractSiteException
{
    /**
     * @param string $message
     * @param Exception|null $previous
     */
    public function __construct(string $message = "", ?Exception $previous = null)
    {
        parent::__construct(404, $message, $previous);
    }

}
