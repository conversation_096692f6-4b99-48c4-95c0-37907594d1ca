<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: Gen
 * Date: 23.8.2017 г.
 * Time: 18:03
 */

namespace App\Exceptions;

use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * Class PaymentBadRequest
 * @package App\Exceptions
 */
class PaymentBadRequest extends BadRequestHttpException
{
    /**
     * PaymentBadRequest constructor.
     * @param string $message
     * @param Request $request
     * @param \Exception|null $previous
     * @param int $code
     */
    public function __construct(string $message, protected \Illuminate\Http\Request $request, ?\Exception $previous = null, int $code = 0)
    {
        parent::__construct($message, $previous, $code);
    }

    /**
     * @return Request
     */
    public function getRequest(): \Illuminate\Http\Request
    {
        return $this->request;
    }
}
