<?php

declare(strict_types=1);

namespace App\Exceptions;

use App\Helper\Plan;
use App\Models\Gate\PlanFeature;
use Exception;

/**
 * Class SiteCpAccessDeniedByPlan
 * @package App\Exceptions
 */
class SiteCpAccessDeniedByPlan extends Exception
{
    /**
     * @var int
     */
    protected $code = 403;

    /**
     * @var string $btnText
     */
    protected $btnText;

    /**
     * @var string $btnLink
     */
    protected $btnLink;

    /**
     * @var PlanFeature|\Eloquent|\Illuminate\Database\Eloquent\Model|object|null
     */
    protected $feature;

    /**
     * SiteCpAccessDeniedByPlan constructor.
     * @param string|null $map
     */
    public function __construct($map = null)
    {
        parent::__construct();

        $this->message = $this->translate($map);
        $this->btnText = __('plan.view_price');//__('plan.upgrade_now');
        $this->btnLink = route('admin.plans');
    }

    /**
     * @return string
     */
    public function getBtnText()
    {
        return $this->btnText;
    }

    /**
     * @return string
     */
    public function getBtnLink()
    {
        return $this->btnLink;
    }

    /**
     * @param $message
     */
    public function setMessage($message): void
    {
        $this->message = $message;
    }

    /**
     * @param $link
     * @param null $text
     */
    public function setBtn($link, $text = null): void
    {
        $this->btnLink = $link;
        if ($text) {
            $this->btnText = $text;
        }
    }

    /**
     * @param $map
     *
     * @return mixed
     */
    protected function translate($map)
    {
        $translations = [
            'administrators' => __('restrict.administrators_limit_reached'),
            'products' => __('restrict.product_limit_reached'),
            'categories' => __('restrict.category_limit_reached'),
            'vendors' => __('restrict.vendor_limit_reached'),
            'customers' => __('restrict.customers_limit_reached'),
            'discounts' => __('restrict.discounts_limit_reached'),
            'selections' => __('restrict.selections_limit_reached'),
            'blogs' => __('restrict.blogs_limit_reached'),
            'articles' => __('restrict.articles_limit_reached'),
            'pages' => __('restrict.pages_limit_reached'),
            'products_import' => __('restrict.product_limit_reached'),
            'segments' => __('restrict.segments_limit_reached'),
        ];

        if (isset($translations[$map])) {
            return $translations[$map];
        }

        if ($this->feature && $featureValue = Plan::featureValueFormatted($this->feature)) {
            return __('restrict.feature_limit_reached', [
                'feature' => $this->feature->name,
                'limit' => $featureValue,
            ]);
        }

        return __('restrict.global_limit_reached');
    }

    /**
     * @param int $code
     * @return mixed
     */
    public function setCode(int $code): SiteCpAccessDeniedByPlan
    {
        $this->code = $code;
        return $this;
    }
}
