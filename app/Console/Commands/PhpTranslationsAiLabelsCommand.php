<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Modules\Core\Core\Traits\ChatGPTTranslator;
use Throwable;

class PhpTranslationsAiLabelsCommand extends Command
{
    use ChatGPTTranslator;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'php:translations:ai-labels {lang : Language for generate.} {--force : Force the operation to run when in production}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Php translations for labels by AI';

    protected $nestedArrays = [];

    protected $ignoredFiles = [
        'rfm.php'
    ];

    protected $force = false;

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $lang = $this->input->getArgument('lang');
        $languages = config('languages.languages', []);
        unset($languages['en']);
        $languages = array_keys($languages);

        if ($lang != 'all') {
            if (!in_array($lang, $languages)) {
                $this->error(sprintf('Language "%s" is not defined!', $lang));
                return;
            }
        }

        $langInstance = lang();
        $paths = array_merge([app()->langPath()], array_values($langInstance->getLoader()->namespaces()));
        $allTranslations = array_filter(array_map(fn($dir): array => array_filter(glob($dir . '/*/*.php'), fn($path): bool => !str_contains((string)$path, 'builder\vendor')), $paths));

        $enTranslations = array_map(fn($path): array => [
            'path' => dirname((string)$path, 2) . '/',
            'name' => basename((string)$path),
            'locale' => 'en',
            'full_path' => $path,
        ], fast_collapse(array_filter(array_map(fn($paths): ?array => array_filter($paths, fn($path): bool => str_contains((string)$path, '/en/')), $allTranslations))));

        $this->force = $this->option('force');

        $enTranslations = array_filter($enTranslations, fn($file): bool => !$this->isTranslated($file, $languages));

        $languages = $lang == 'all' ? $languages : [$lang];

        foreach ($languages as $language) {
            if ($language == 'en') {
                continue;
            }

            foreach ($enTranslations as $translationFile) {
                $this->translateFile($translationFile, $language);
            }
        }

        if ($this->nestedArrays) {
            file_put_contents(storage_path('nestedArraysTranslations.' . $lang . '.php'), $this->arrayToPhp($this->nestedArrays));
        }

    }

    /**
     * @param array $file
     * @param string $language
     * @throws Exception
     */
    protected function translateFile(array $file, string $language)
    {
        if ($this->isTranslated($file, [$language], true)) {
            return;
        }

        $en = include $file['full_path'];
        $path = $file['path'] . $language . '/' . $file['name'];

        if (!$this->force) {
            try {
                $check = include $path;
            } catch (Throwable) {
                $check = [];
            }

            if (!is_array($check)) {
                $check = [];
            }

            //        $empty = array_filter($en, function($value) {
            //            return empty($value) || trim($value) == '';
            //        });
            //        if($empty) {
            //            $check = array_merge($check, $empty);
            //        }

            $diff = array_diff_key($en, array_filter($check, fn($value): bool => !(empty($value) || (is_string($value) && trim($value) == ''))));

            if (!$diff) {
                return;
            }

            $enCheck = collect($en)->only(array_keys($diff))->all();
            $diffCheck = array_diff($enCheck, $diff);
            if (count($enCheck) == count($diffCheck)) {
                return;
            }
        } else {
            $diff = $en;
        }

        @mkdir($file['path'] . $language . '/', 0777, true);

        if (!in_array($file['name'], $this->ignoredFiles)) {
            $groups = collect($diff)->chunk(100);
            foreach ($groups as $parts) {
                $translation = $this->gptTranslateText($parts->all(), 'en', $language);
                if ($translation['status'] === false) {
                    throw new Exception($translation['message']);
                }

                $diff = array_merge($diff, $translation['texts'] ?? []);
            }
        }

        if (empty($diff)) {
            return;
        }

        $check = array_merge($diff, $check);
        ksort($check);

        file_put_contents($path, $this->arrayToPhp($check));
        $this->info(sprintf('Successfully write file "%s" with "%d" differences!', $path, count($diff)));
    }

    /**
     * @param array $data
     * @return mixed
     */
    protected function arrayToPhp(array $data): string
    {
        $labels = 'return ' . var_export($data, true) . ';';
        $labels = str_replace(
            ['return array (', ');', "  '"],
            ['return [', '];', "    '"],
            $labels
        );

        return '<?php ' . $labels;
    }

    /**
     * @param array $file
     * @param mixed $languages
     * @param bool $single
     * @return mixed
     */
    protected function isTranslated(array $file, $languages, bool $single = false): bool
    {
        if ($this->force) {
            $en = include $file['full_path'];
            if ($this->hasNestedArrays($en)) {
                $this->nestedArrays[] = $file['full_path'];
                return true;
            }

            return false;
        }

        if ($single) {
            $files = array_filter(glob($file['path'] . '*/' . $file['name']), fn($path): bool => !str_contains((string)$path, '/en/'));
            if (count($files) != count($languages)) {
                return false;
            }
        }

        $en = include $file['full_path'];
        if ($this->hasNestedArrays($en)) {
            $this->nestedArrays[] = $file['full_path'];
            return true;
        }

        foreach ($languages as $language) {
            try {
                $check = include $file['path'] . $language . '/' . $file['name'];
            } catch (Throwable) {
                return false;
            }

            if (!is_array($check)) {
                $check = [];
            }

            if ($this->hasNestedArrays($check)) {
                $this->nestedArrays[] = $file['path'] . $language . '/' . $file['name'];
                return true;
            }

            if (array_diff_key($en, $check) && count($en) > count($check)) {
                return false;
            }

            foreach ($en as $k => $v) {
                if (empty($v) || (is_string($v) && trim($v) == '')) {
                    continue;
                }

                if (empty($check[$k])) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * @param array $array
     * @return mixed
     */
    protected function hasNestedArrays(array $array): bool
    {
        foreach ($array as $element) {
            if (is_array($element)) {
                return true;
            }
        }

        return false;
    }
}
