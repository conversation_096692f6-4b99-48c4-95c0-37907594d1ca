<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 13.10.2016 г.
 * Time: 17:27 ч.
 */

namespace App\Locale;

use Cloudcart\Localization\Locale\Country as CoreCountry;
use Omniship\Helper\Data;

class Country extends CoreCountry
{
    /**
     * @param mixed $locale
     * @return mixed
     */
    #[\Override]
    public static function all($locale = null)
    {
        return collect(parent::all($locale))
            ->only(Data::countries()->keys()->all())->all();
    }

    /**
     * @param mixed $locale
     * @param mixed $euOnly
     * @return mixed
     */
    public static function vue($locale = 'en', $euOnly = false)
    {
        $EU = config('vat.EU');

        return collect(parent::all($locale))
            ->only(Data::countries()->keys()->all())->map(function (array $data) use ($euOnly, $EU): ?array {
                if ($euOnly && !in_array($data['code'], $EU)) {
                    return null;
                }

                return [
                    'id' => $data['code'],
                    'name' => $data['name'],
                    'localized_name' => $data['localized_name'],
                ];
            })->filter()->sortBy('name')->values()->all();
    }
}
