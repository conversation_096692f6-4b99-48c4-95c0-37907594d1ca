APP_ENV=production

EXTERNAL_IP_GOOGLE=**************
EXTERNAL_IP_HETZNER=***************
EXTERNAL_IP_HETZNER_CLOUD=**************

########## debugbar ##########
DEBUGBAR_ENABLED=false
########## end debugbar ##########

########## start cache ##########
CACHE_HTML=true
CACHE_MEMCACHED_HOST=**************
########## end cache ##########

########## start console ##########
CONSOLE_DOMAIN=https://console.cloudcart.com
CONSOLE_RESELLER_EXPENSE_CATEGORY_ID=34
CONSOLE_ECOSYSTEM_EXPENSE_CATEGORY_ID=35
########## end console ##########

########## start db ##########
DB_CONNECTION=default
DB_PORT=3306
DB_HOST=***********
DB_PASSWORD=.4xf3^U^p&[}?<g&

DB_ROUTER_PROD_HOST=***********
DB_ROUTER_PROD_PASSWORD=.4xf3^U^p&[}?<g&

DB_DYNAMIC_HOST=***********
DB_ROUTER_HOST=***********
DB_APPS_HOST=***********
DB_GATE_HOST=***********
DB_PAYMENTS_HOST=***********
DB_OAUTH_HOST=***********
DB_CCLINK_HOST=***********

DB_LOGS_GLOBAL_HOST=***********
DB_LOGS_GLOBAL_PASSWORD=.4xf3^U^p&[}?<g&

DB_QUEUE_HOST=***********
########## end db ##########

########## start mongo db ##########
MONGODB_PORT=27017

MONGODB_SESSION_LOCAL_HOST=**********
MONGODB_SESSION_HETZNER_HOST=**********
MONGODB_SESSION_GOOGLE_HOST=********,********,********
MONGODB_SESSION_DATABASE=builder
MONGODB_SESSION_USERNAME=builder
MONGODB_SESSION_PASSWORD=6zhky727kmnu623p
SESSION_CONNECTION=mongodb

MONGODB_QUEUE_LOCAL_HOST=***********
MONGODB_QUEUE_HETZNER_HOST=********
MONGODB_QUEUE_GOOGLE_HOST=***********
MONGODB_QUEUE_DATABASE=queue
MONGODB_QUEUE_USERNAME=queue
MONGODB_QUEUE_PASSWORD=Vg3Lpos4gw9g4nbld

MONGODB_APPLICATIONS_LOCAL_HOST=***********
MONGODB_APPLICATIONS_HETZNER_HOST=********
MONGODB_APPLICATIONS_GOOGLE_HOST=***********
MONGODB_APPLICATIONS_DATABASE=applications
MONGODB_APPLICATIONS_USERNAME=applications
MONGODB_APPLICATIONS_PASSWORD=Vg3Lpos4gw9g4nbld

MONGODB_LOGGING_LOCAL_HOST=***********
MONGODB_LOGGING_HETZNER_HOST=********
MONGODB_LOGGING_GOOGLE_HOST=***********
MONGODB_LOGGING_DATABASE=logging
MONGODB_LOGGING_USERNAME=logging
MONGODB_LOGGING_PASSWORD=Vg3Lpos4gw9g4nbld

MONGODB_MARKETING_LOCAL_HOST=***********
MONGODB_MARKETING_HETZNER_HOST=********
MONGODB_MARKETING_GOOGLE_HOST=***********
MONGODB_MARKETING_DATABASE=marketing
MONGODB_MARKETING_USERNAME=marketing
MONGODB_MARKETING_PASSWORD=Vg3Lpos4gw9g4nbld

MONGODB_ANALYTICS_HOST=********
MONGODB_ANALYTICS_DATABASE=analytics
MONGODB_ANALYTICS_USERNAME=analytics
MONGODB_ANALYTICS_PASSWORD=7z5ky619kfnx845g

MONGODB_ANALYTICS_SESSIONS_DATABASE=sessions
MONGODB_ANALYTICS_SESSIONS_USERNAME=sessions
MONGODB_ANALYTICS_SESSIONS_PASSWORD=7z5ky619kfnx845g

##### CACHE #####
MONGODB_CACHE_HOST=***********
MONGODB_CACHE_DATABASE=cache
MONGODB_CACHE_USERNAME=cache
MONGODB_CACHE_PASSWORD=7hb4A4zu67cSBh9L
# hetzner
MONGODB_CACHE_REMOTE_HOST=********
MONGODB_CACHE_REMOTE_PASSWORD=ccheNCSrvngpNjTyCZii

##### LOGS #####
MONGODB_LOGS_HOST=********
MONGODB_LOGS_DATABASE=logs
MONGODB_LOGS_USERNAME=logs
MONGODB_LOGS_PASSWORD=hetzguk8tyl7geb
########## end mongo db ##########

########## start demo ##########
DEMO_USER_ID=270
########## end demo ##########

########## start facebook ##########
FACEBOOK_APP_ID=1707359649482571
FACEBOOK_APP_SECRET=********************************
FACEBOOK_DOMAIN=https://facebook.cloudcart.com
########## end facebook ##########

########## start filesystems ##########
FTP_HOST=********
FTP_PORT=21
FTP_USER=cloudcart
FTP_PASS=JrruMAz22hCR3Q8Q
FTP_SSL=false
FTP_SITES_DIR=
FTP_GATE_DIR=storage

FTP_TRANSLATIONS_HOST=********
FTP_TRANSLATIONS_PORT=21
FTP_TRANSLATIONS_USER=cloudcart
FTP_TRANSLATIONS_PASS=JrruMAz22hCR3Q8Q
FTP_TRANSLATIONS_SSL=false
########## end filesystems ##########

########## start google ##########
GOOGLE_ANALYTICS_PROPERTY_ID=123456789
GOOGLE_VIEW_ID=105503619
GOOGLE_TRACKING_ID=UA-49725001-7
GOOGLE_GTM_ID=GTM-5H24CJ
GOOGLE_GTM_ID_GMBH=GTM-5HZD8SPJ
GOOGLE_MAP=AIzaSyDXc9PCs08OsONmc9os-d8zE4nk6BvxKI0
GA4_MEASUREMENT_ID=G-ETPFBCF6ZD
GA4_MEASUREMENT_ID_GMBH=G-5X496K0DG6
GA4_MP_API_SECRET=upuQKHhPRf6J90ti5oAGqQ
GA4_DEBUG=0
########## end google ##########

########## start integrations ##########
INTEGRATIONS_EBAY_SANDBOX=no
INTEGRATIONS_EBAY_TRADING_API_VERSION=889
INTEGRATIONS_EBAY_APP_ID=Cloudcar-76ca-446e-824f-b90876e72552
INTEGRATIONS_EBAY_CERT_ID=da3db72b-9fc0-4c9b-9f6b-06390a46ce1b
INTEGRATIONS_EBAY_RUNAME=Cloudcart_LLC-Cloudcar-76ca-4-zcjrq
########## end integrations ##########

########## start payments ##########
PAYMENTS_ADDRESS=https://payments.cloudcart.com
########## end payments ##########

########## start api ##########
API_DOMAIN_DOCS=https://apidocs.cloudcart.com
########## end api ##########

########## start websocket ##########
SOCKET_DOMAIN=wss://socket.cloudcart.com
SOCKET_PORT=443
########## end websocket ##########

########## start vuejs sitecp ##########
URL_CC_SITECP_VUE_JS=https://vue.cdncloudcart.com
########## end vuejs sitecp ##########

########## start proxy ##########
PROXY=*************/24
########## end proxy ##########

LTA_CONTRACT_PENALTY_SERVICE_ID=646

########## start elastic accounts ##########
#ELASTIC_ACCOUNT=907c2cd8-487b-4f2c-a9c2-ca482e161a87
ELASTIC_KEY=d48e6086-b469-4cd2-bfe6-80f80906bda8
#ELASTIC_SUB_ACCOUNT=c144e299-cd63-46d0-9e30-53af2259d7df
ELASTIC_SUB_KEY=E913A368E38B60314159AC71785CC5717134248A0380E4B71B982E6D41D2980BCD3AF7143303E77FB3C5E639DA66E510
#ELASTIC_2FA_ACCOUNT=4c832ca0-e2b5-4a83-a977-58ec820266ce
ELASTIC_2FA_KEY=1576D3C0CE4788D111E39F74F98EB7A6F128E8C0EC9864ECFF8829A19ED95A01E1B325D6AEC6C818EDE940A2DEE5D509
ELASTIC_CAMPAIGNS_API_KEY=EA662BFE0E44EDFFA801BCC3602D7F01EEDD1EFB9650F0911101429D1A902DDF464609FF2098F8D3E3B69B19AB36F516
MAIL_MAILER=elastic_email
########## end elastic accounts ##########

########## start socialite ##########
SOCIALITE_DOMAIN=https://socialite.cloudcart.com
########## end socialite ##########

########## start url ##########
URL_GATE=cloudcart.com
URL_CDN_GATE=https://cdncloudcart.com/storage/
URL_CC_ANALYTICS=cca.cloudcart.com
URL_SITES=cloudcart.net
URL_IMG=https://cdncloudcart.com/assets/
URL_STORAGE=https://cdncloudcart.com/
URL_STORAGE_LOCAL=https://cdncloudcart.com/
URL_CC_LINK=https://ccl.ink/
PBX_URL=https://cl-to-call-pbx.cloudcart.com:1718
PBX_WEBHOOK_SECRET=72b3af6f-30b1-46bc-ae88-217af687b936
PBX_HOST=*************
PBX_PORT=16759
URL_TRANSLATIONS=https://cdncloudcart.com/translations/
########## end url ##########

CLOUDFLARE_EMAIL=<EMAIL>
CLOUDFLARE_KEY=ffb8bd805ce182fb2b909c55a4159b863aa85
CLOUDFLARE_ZONE=d3fb3312ab053ca09bfabd12e3dd6c68

HOOKS_DOMAIN=https://hooks.cloudcart.com

#NAMECOM_MODE=dev
#NAMECOM_USERNAME=cloudcart-test
#NAMECOM_TOKEN=6dc82750574e5e15dfcff170e991209602d7fd37
#NAMECOM_NAMESERVERS=ns1vwx.name.com,ns3cpr.name.com
#NAMECOM_MARGIN_PERCENT=100

NAMECOM_MODE=live
NAMECOM_USERNAME=cloudcart
NAMECOM_TOKEN=471d8ba72ca9c9fdea9b8d0f230fb2cce1aeaf1a
NAMECOM_NAMESERVERS=ns1vwx.name.com,ns3cpr.name.com
NAMECOM_MARGIN_PERCENT=100

INFOBIP_HOST='https://jkzd4.api.infobip.com'
INFOBIP_USERNAME='cloudcart_transactional'
INFOBIP_PASSWORD='JXP.3Z6#pmjLwcM'
INFOBIP_KEY='56F990E7F4DF9C2124A06588E7287FF0'
INFOBIP_USERNAME_PROMO='cloudcart.promo'
INFOBIP_PASSWORD_PROMO='cloudcartDEV123!@#'
INFOBIP_KEY_PROMO='7C095532D51DE87E79A07E620B4EA0D4'

SMSNTH_HOST='https://multi.mobile-gw.com'
SMSNTH_PORT='9010'
SMSNTH_ENCODING='UTF-8'
SMSNTH_SYSTEM_TYPE='901317'
SMSNTH_USERNAME='cloudcart1'
SMSNTH_PASSWORD='3q(y=KaS'

APIS_USERNAME=cloudcartcom
APIS_PASSWORD=cloudcartcom

VAPID_PUBLIC_KEY=BLWcGzsLZbI6IuhTpk5jH3wjljaKKYpzAX0pGkrO8JJKhp4aRs9KjWHIW92uLAizRnEyjQm6mQMB81cPBtK_EHs
VAPID_PRIVATE_KEY=aDy6ro3VcS_2-65--TzC34nOCl00Eb_eGvAJ_4X7Mds

USERS_WEBHOOK_KEY=c5b1f77b-9da3-450a-ba7b-42a0dcde80e7

SENTRY_LARAVEL_DSN=https://<EMAIL>/4509084135456768
SENTRY_TRACES_SAMPLE_RATE=0

PROMETHEUS_NAMESPACE=app
PROMETHEUS_METRICS_ROUTE_ENABLED=true
PROMETHEUS_METRICS_ROUTE_PATH=metrics
PROMETHEUS_METRICS_ROUTE_MIDDLEWARE=null
PROMETHEUS_STORAGE_ADAPTER=memory

########## MAXMIND ##########
MAXMIND_LICENSE_KEY=****************
MAXMIND_USER_ID=93133

########## smarty ##########
SMARTY_FORCE_COMPILE=false
SMARTY_COMPILE_CHECK=false
