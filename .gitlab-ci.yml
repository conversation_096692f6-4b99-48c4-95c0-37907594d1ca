stages:
  - build
  - push
  - deploy:staging
  - test:staging
  - deploy:prod
  - test:prod
  - latest

variables:
  GIT_STRATEGY: none

build:
  stage: build
  tags:
    - builder
  variables:
    GIT_STRATEGY: clone
  script:
    - docker login -u "gitlab-ci-token" -p "$CI_JOB_TOKEN" $CI_REGISTRY
    - docker build --build-arg BUILD_TAG=${CI_COMMIT_TAG} -t ${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID .

push:
  stage: push
  tags:
    - builder
  script:
    - docker login -u "gitlab-ci-token" -p "$CI_JOB_TOKEN" $CI_REGISTRY
    - docker push ${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID


deploy:staging:gcp:
  stage: deploy:staging
  environment: staging/gcp
  when: manual
  tags:
    - google-cloud
  script:
    - kubectl -n default set image deployment/builder-bigrelease            nginx=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID php=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl rollout status deployment builder-bigrelease


test:staging:
  stage: test:staging
  tags:
    - builder
  variables:
    GIT_STRATEGY: clone
  needs: ["deploy:staging:gcp"]
  script:
    - sh ./tests/bash/test.sh

deploy:prod:gcp:
  stage: deploy:prod
  environment: production/gcp/builder
  when: manual
  tags:
    - google-cloud
  script:
    - kubectl -n default set image deployment/builder                       nginx=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID php=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/builder-22                    nginx=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID php=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/builder-23                    nginx=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID php=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/builder-24                    nginx=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID php=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
##    - kubectl -n default set image deployment/builder-25                    nginx=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID php=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/api                           nginx=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID php=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/admin                         nginx=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID php=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/uploads                       nginx=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID php=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/builder-google                nginx=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID php=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/console                       nginx=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID php=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID

deploy:prod:gcp-workers:
  stage: deploy:prod
  environment: production/gcp/workers
  when: manual
  tags:
    - google-cloud
  script:
    - kubectl -n default set image deployment/worker                        worker=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-product-images         worker-product-images=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-import                 worker-import=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-system                 worker-system=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-install                worker-install=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-export                 worker-export=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-order-events           worker-order-events=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-subscribers            worker-subscribers=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-segments               worker-segments=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-cc-system              worker-cc-system=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-campaigns              worker-campaigns=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-campaigns-messages     worker-campaigns-messages=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-campaigns-hooks        worker-campaigns-hooks=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-campaigns-process      worker-campaigns-process=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-translate              worker-translate=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-tmp                    worker-tmp=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/worker-cloudio                worker-cloudio=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/schedule                      schedule=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID

run:artisan:after-admin:
  stage: deploy:prod
  tags:
    - google-cloud
  needs: ["deploy:prod:gcp"]
  script:
    - echo "Waiting for all pods with label run=admin to be ready..."
    - |
      until [ "$(kubectl -n default get pods -l run=admin -o json | jq '[.items[] | select(all(.status.containerStatuses[]; .ready == true))] | length')" -eq "$(kubectl -n default get pods -l run=admin --no-headers | wc -l)" ]; do
        echo "Some pods are still not fully ready..."
        sleep 5
      done
    - echo "All admin pods are ready. Executing php artisan command..."
    - ADMIN_POD=$(kubectl -n default get pods -l run=admin -o jsonpath='{.items[0].metadata.name}')
    - kubectl -n default exec -it $ADMIN_POD -c php -- php artisan core:update-last-compiled-record

test:prod:
  stage: test:prod
  tags:
    - builder
  variables:
    GIT_STRATEGY: clone
  needs: ["deploy:prod:gcp"]
  script:
#    - kubectl rollout status deployment builder
#    - kubectl rollout status deployment api
#    - kubectl rollout status deployment admin
    - sh ./tests/bash/test.prod.sh

deploy:prod:hetzner-workers:
  stage: deploy:prod
  environment: production/hetzner
  when: manual
  tags:
    - hetzner-cloud
  script:
    - kubectl -n default set image deployment/worker-analytics              worker-analytics=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID
    - kubectl -n default set image deployment/schedule                      schedule=${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID

latest:
  stage: latest
  tags:
    - builder
  needs: ["test:prod"]
  script:
    - docker login -u "gitlab-ci-token" -p "$CI_JOB_TOKEN" $CI_REGISTRY
    - docker tag  ${CI_REGISTRY_IMAGE}:$CI_PIPELINE_ID ${CI_REGISTRY_IMAGE}:latest
    - docker push ${CI_REGISTRY_IMAGE}:latest


workflow:
  rules:
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
