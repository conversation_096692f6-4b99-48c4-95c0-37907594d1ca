<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: user
 * Date: 3/23/2018
 * Time: 2:56 PM
 */

namespace FacebookGW\Helpers;

use App\Events\Webhooks\MessengerSubscriberSaved;
use App\Helper\GoogleMap;
use App\Integration\Facebook\Apps\MessengerBot;
use App\Integration\Facebook\Models\MessengerSubscriber;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerShippingAddress;
use App\Models\Oauth\MessengerCommand;
use App\Models\Order\Order;
use App\Models\Order\OrderProduct;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Router\Exceptions;
use App\Models\Store\CartItem;
use App\Models\Store\Meta;
use App\Models\System\AppsManager;
use Exception;
use FacebookPage;
use Illuminate\Support\Collection;
use App\Helper\Widget;
use App\Models\Store\Cart as CartModel;
use Messenger;
use Linker;
use App\Exceptions\Error;

class Checkout
{
    public const PARAMETER_PICKED_TYPE = 'parameter_picked';

    public const CONFIRM_CUSTOMER_DATA_TYPE = 'confirm_customer_data';

    public const WRONG_CUSTOMER_DATA_TYPE = 'wrong_customer_data';

    public const CONFIRM_CUSTOMER_NAME_TYPE = 'confirm_customer_name';

    public const WRONG_CUSTOMER_NAME_TYPE = 'wrong_customer_name';

    public const SHIPPING_ADDRESS_TYPE = 'shipping_address';

    public const REMOVE_PRODUCT_FROM_CART_TYPE = 'remove_product_from_cart';

    public const FINISH_ORDER_TYPE = 'finish_order';

    public const CONFIRM_CUSTOMER_OLD_DATA_TYPE = 'confirm_customer_old_data';

    public const WRONG_CUSTOMER_OLD_DATA_TYPE = 'wrong_customer_old_data';

    /** @var MessengerBot $bot */
    protected static $bot;

    protected static function bot()
    {
        if (!static::$bot) {
            static::$bot = AppsManager::getManager('messenger_bot');
        }

        return static::$bot;
    }

    /**
     * @param $psId
     * @param $data
     * @throws Error
     * @throws \Throwable
     */
    public static function addToCart($psId, $data): void
    {
        if (!empty($data[0])) {
            $product_id = $data[0];
            $product = Product::find($product_id);

            static::sendMessageWithAdditionalQuestions($product, $psId, 1, []);
        }
    }

    /**
     * @param $psId
     * @param $data
     * @throws Error
     * @throws \Throwable
     */
    public static function parameterPicked($psId, $data): void
    {
        if (!empty($data[0])) {
            $product = Product::find($data[2]);
            // if isset($data[6]) -> two parameters have already been picked, else only one parameter was picked
            $parameter_index = !isset($data[6]) ? 2 : 3;

            static::sendMessageWithAdditionalQuestions($product, $psId, $parameter_index, $data);
        }
    }

    /**
     * @param $psId
     * @throws Error
     * @throws \Throwable
     */
    public static function checkIfMessengerSubscriberHasCart($psId): void
    {
        $subscriber = MessengerSubscriber::where('psid', $psId)->first();
        $merge = false;
        $cartInstance = CartModel::instance(true);
        if ($subscriber->cart_key) {
            $merge = $subscriber->cart_key != $cartInstance->key;
        } elseif ($subscriber->cart_id) {
            $merge = $subscriber->cart_id != $cartInstance->id;
        }

        if ($merge) {
            if ($subscriber->cart) {
                if (setting('merge_cart', 1)) {
                    $storeCart = $cartInstance->mergeCart($subscriber->cart);
                } else {
                    $storeCart = $cartInstance;
                }

                if (!empty($subscriber->cart->user_id) && !empty($subscriber->cart->shipping_address_id)) {
                    $storeCart_params = [
                        'user_id' => $subscriber->cart->user_id,
                        'shipping_address_id' => $subscriber->cart->shipping_address_id,
                        'shipping_provider_id' => null,
                        'payment_provider_id' => null,
                    ];

                    $storeCart->update($storeCart_params);

                    $subscriber->update([
                        'cart_id' => $storeCart->id,
                        'customer_id' => $storeCart->customer->id
                    ]);

                    try {
                        event(new MessengerSubscriberSaved($subscriber));
                    } catch (Exception) {
                    }

                    $subscriber->cart->delete();

                    $storeCart->setStep('shippingAddress');
                    $storeCart->setMeta('cart.source', 'messenger-bot');
                } else {
                    $storeCart->setStep('authorize');
                }
            }
        } else {
            if (!is_null($cartInstance->customer)) {
                $cartInstance->update([
                    'shipping_type' => 'address'
                ]);
            }
        }
    }

    /**
     * @param $data
     * @param $subscriber
     * @param $site_id
     * @param $psId
     * @return bool
     * @throws Exception
     */
    public static function messageIsResponse($data, $subscriber, $site_id, $psId)
    {
        if (isset($data['message'])) {
            $command = MessengerCommand::where('recipient_id', $psId)->where('site_id', $site_id)->first();
            if ($command) {
                return static::handleRequestedParameter($subscriber, $command, $data, $site_id, $psId);
            }
        }

        return false;
    }

    /**
     * @param $subscriber
     * @param MessengerCommand $command
     * @param $data
     * @param $site_id
     * @param $psId
     * @return bool
     * @throws Exception
     */
    public static function handleRequestedParameter($subscriber, $command, array $data, $site_id, $psId): bool
    {
        $cart = CartModel::find($subscriber->cart_id);

        if (!$cart) {
            $command->delete();
            return false;
        }

        $message = $data['message']['text'];
        switch ($command->command) {
            case MessengerCommand::NAME_REQUEST:
                static::handleName($psId, $site_id, $message, $cart);
                break;
            case MessengerCommand::EMAIL_REQUEST:
                static::handleEmail($psId, $site_id, $message, $cart, $subscriber);
                break;
            case MessengerCommand::PHONE_REQUEST:
                static::handlePhone($psId, $site_id, $message, $cart);
                break;
            case MessengerCommand::ADDRESS_REQUEST:
                static::handleAddressInvalidAnswer($psId, $site_id);
                break;
        }

        $command->delete();

        return true;
    }

    /**
     * @param $psId
     * @param $payload
     * @throws Error
     * @throws \Facebook\Exceptions\FacebookSDKException
     */
    public static function customerDataConfirmed($psId, $payload): void
    {
        $cart_id = $payload[3];
        $cart = CartModel::find($cart_id);

        $cart->update([
            'shipping_type' => 'address'
        ]);

        $message = static::bot()->getSetting('product_listing_message');
        if (!empty($message)) {
            $products_intro_message = Messenger::getSimpleMessageParameters($message, $psId);
            FacebookPage::sendMessage($products_intro_message);
        }

        $products = $cart->products;
        $params = static::getProductsParameters($cart, $products, $psId);
        if (!empty($params)) {
            FacebookPage::sendMessage($params);
        }
    }

    /**
     * @param $psId
     * @param $payload
     * @throws Error
     * @throws Exception
     */
    public static function customerOldDataConfirmed($psId, $payload): void
    {
        $cart_id = $payload[3];
        $cart = CartModel::find($cart_id);

        $cart->update([
            'shipping_type' => 'address'
        ]);

        $message = static::bot()->getSetting('old_data_confirmed_message');
        if (!empty($message)) {
            $params = Messenger::getSimpleMessageParameters($message, $psId);
            $response = FacebookPage::sendMessage($params);

            if ($response) {
                static::createCommand(site('site_id'), $psId, $response->getDecodedBody(), MessengerCommand::PHONE_REQUEST);
            }
        }
    }

    /**
     * @param $psId
     * @throws Error
     * @throws Exception
     */
    public static function wrongCustomerData($psId): void
    {
        $email_request_message = static::bot()->getSetting('mistake_in_cart_summary_message');
        static::sendMessageForEmailRequest($psId, $email_request_message, true);
    }

    /**
     * @param $psId
     * @param $payload
     * @throws Error
     * @throws \Facebook\Exceptions\FacebookSDKException
     */
    public static function wrongCustomerOldData($psId, $payload): void
    {
        $cart_id = $payload[3];
        $cart = CartModel::find($cart_id);
        static::confirmCustomerNamesMessage($psId, $cart);

    }

    /**
     * @param $psId
     * @param $payload
     * @throws Error
     * @throws Exception
     */
    public static function customerNameConfirmed($psId, $payload): void
    {
        if (isset($payload[0])) {
            $site_id = $payload[0];
            $message = static::bot()->getSetting('address_request_message');

            if (!empty($message)) {
                $params = Messenger::getSimpleMessageParameters($message, $psId);

                $quick_replies[] = ['content_type' => 'location'];
                $params['message']['quick_replies'] = $quick_replies;

                $response = FacebookPage::sendMessage($params);
                if ($response) {
                    static::createCommand($site_id, $psId, $response->getDecodedBody(), MessengerCommand::ADDRESS_REQUEST);
                }
            }
        }
    }

    /**
     * @param $psId
     * @throws Error
     * @throws Exception
     */
    public static function wrongCustomerName(string $psId): void
    {
        $site_id = site('site_id');
        $message = static::bot()->getSetting('choose_another_name_message');

        if (!empty($message)) {
            $params = Messenger::getSimpleMessageParameters($message, $psId);

            $payload = site('site_id') . "-stop_bot_answers-" . MessengerBot::QUICK_REPLY . '-' . $psId;
            $stop_bot_quick_reply = Messenger::getQuickReplyParams($payload, __('facebook.action.stop.bot'));
            $params['message']['quick_replies'][] = $stop_bot_quick_reply;

            $response = FacebookPage::sendMessage($params);
            if ($response) {
                static::createCommand($site_id, $psId, $response->getDecodedBody(), MessengerCommand::NAME_REQUEST);
            }
        }
    }

    /**
     * @param $psId
     * @param $payload
     * @throws Error
     * @throws \Facebook\Exceptions\FacebookSDKException
     */
    public static function handleMoreParameters($psId, $payload): void
    {
        if (count($payload) == 5) {
            [$product_id, $parameter_index, $v1_id, $v2_id, $start_index] = $payload;
            $product = Product::find($product_id);

            static::chooseProductParameterMessage($product, $parameter_index, $v1_id, $v2_id, $start_index, $psId);
        }
    }

    /**
     * @param $product
     * @param $parameter_index
     * @param $v1_id
     * @param $v2_id
     * @param $start_index
     * @param $psId
     * @return bool
     * @throws Error
     * @throws \Facebook\Exceptions\FacebookSDKException
     */
    protected static function chooseProductParameterMessage($product, string $parameter_index, $v1_id, $v2_id, $start_index, $psId): bool
    {
        $quick_replies = static::getProductParametersMessageQuickReplies($product, $parameter_index, $v1_id, $v2_id, $start_index);

        if ($product && !empty($quick_replies)) {
            $message = static::bot()->getSetting('choose_product_parameter_message');
            if (!empty($quick_replies && !empty($message))) {
                $param_index = 'p' . $parameter_index;
                $param_name = mb_convert_case((string) $product->$param_index, MB_CASE_LOWER, "UTF-8");
                ;

                $message = str_replace('{$parameter}', $param_name, $message);
                $params = Messenger::getSimpleMessageParameters($message, $psId);
                $params['message']['quick_replies'] = $quick_replies;

                FacebookPage::sendMessage($params);

                return true;
            }
        }

        return false;
    }

    /**
     * @param $attachments
     * @param $psId
     * @param $site_id
     * @throws Error
     */
    public static function handleLocation($attachments, $psId, $site_id): void
    {
        foreach ($attachments as $attachment) {
            if ($attachment['type'] == 'location') {
                $coordinates = $attachment['payload']['coordinates'];
                static::validateAndSaveAddress($psId, $coordinates, $site_id);
            }
        }
    }

    /**
     * @param $psId
     * @param $payload
     * @throws Error
     * @throws \Throwable
     */
    public static function removeProductFromCart($psId, $payload): void
    {
        if (isset($payload[2]) && isset($payload[3])) {
            $cart_id = $payload[2];
            $cart_item_id = $payload[3];

            $products = [];
            $cart = CartModel::find($cart_id);
            if ($cart) {
                $cart_item = CartItem::find($cart_item_id);
                $cart->remove($cart_item);

                $products = $cart->products;
            }

            if (count($products)) {
                $params = static::getProductsParameters($cart, $products, $psId);
            } else {
                $message = static::bot()->getSetting('empty_cart_message');
                $params = strlen((string) $message) > 0 ? Messenger::getSimpleMessageParameters($message, $psId) : [];
            }

            if (!empty($params)) {
                FacebookPage::sendMessage($params);
            }
        }
    }


    /**
     * @param MessengerSubscriber $subscriber
     * @param Order $order
     * @throws Error
     * @throws \Facebook\Exceptions\FacebookSDKException
     */
    public static function sendMessageForCompleteOrder($subscriber, $order): void
    {
        \FacebookPage::reload();

        $first_message = static::bot()->getSetting('order_summary_message');
        if (!empty($first_message)) {
            $params = Messenger::getSimpleMessageParameters($first_message, $subscriber->psid);
            FacebookPage::sendMessage($params, $subscriber->id);
        }

        $params = static::receiptMessageTemplate($subscriber->psid, $order);
        FacebookPage::sendMessage($params, $subscriber->id);
    }

    /**
     * @param $psId
     * @param $coordinates
     * @param $site_id
     * @throws Error
     * @throws Exception
     */
    protected static function validateAndSaveAddress($psId, array $coordinates, $site_id)
    {
        $address_found = false;

        try {
            $gmap_response = GoogleMap::getLocationsByPoint($coordinates['lat'], $coordinates['long'], true);
        } catch (Exception $exception) {
            Exceptions::createFromThrowable($exception);

            $gmap_response = null;
        }

        $subscriber = MessengerSubscriber::where('psid', $psId)->first();

        if ($subscriber) {
            $cart = CartModel::where('key', $subscriber->cart_key)->orWhere('id', $subscriber->cart_id)->first();
            if ($gmap_response && $cart) {
                $country = !empty($gmap_response['address_components']) ? GoogleMap::findComponent($gmap_response['address_components'], 'country') : null;
                $city = !empty($gmap_response['address_components']) ? GoogleMap::findComponent($gmap_response['address_components'], 'locality') : null;
                $street = !empty($gmap_response['address_components']) ? GoogleMap::findComponent($gmap_response['address_components'], 'route') : null;
                $street_number = !empty($gmap_response['address_components']) ? GoogleMap::findComponent($gmap_response['address_components'], 'street_number') : null;
                $post_code = !empty($gmap_response['address_components']) ? GoogleMap::findPostCode($gmap_response['address_components']) : null;
                $country_iso = !empty($gmap_response['address_components']) ? GoogleMap::findCountryIso($gmap_response['address_components']) : null;
                $gmap_address = $gmap_response['formatted_address'];

                $params = [
                    'customer_id' => $cart->customer->id,
                    'country_name' => $country,
                    'city_name' => $city,
                    'street_name' => $street,
                    'street_number' => $street_number,
                    'first_name' => $cart->customer->first_name,
                    'last_name' => $cart->customer->last_name,
                    'latitude' => $coordinates['lat'],
                    'longitude' => $coordinates['long'],
                    'text' => $gmap_address,
                    'post_code' => $post_code,
                    'country_iso2' => $country_iso
                ];

                $shipping_address = CustomerShippingAddress::create($params);

                $cart->update(['shipping_address_id' => $shipping_address->id]);
                $cart->customer->update(['default_address_id' => $shipping_address->id]);

                $message = static::bot()->getSetting('phone_request_message');
                if (!empty($message)) {
                    $params = Messenger::getSimpleMessageParameters($message, $psId);
                    $response = FacebookPage::sendMessage($params, $subscriber->id);
                    if ($response) {
                        static::createCommand($site_id, $psId, $response->getDecodedBody(), MessengerCommand::PHONE_REQUEST);
                        MessengerCommand::where('recipient_id', $psId)->where('command', MessengerCommand::ADDRESS_REQUEST)->delete();
                        $address_found = true;
                    }
                }
            }

            if (!$address_found) {
                $message = static::bot()->getSetting('invalid_address_message');
                if (!empty($message)) {
                    $params = Messenger::getSimpleMessageParameters($message, $psId);
                    FacebookPage::sendMessage($params, $subscriber->id);
                }
            }
        }
    }

    /**
     * @param $psId
     * @param $site_id
     * @param $message
     * @param CartModel $cart
     * @throws Error
     * @throws Error
     * @throws Exception
     */
    protected static function handleName($psId, $site_id, $message, $cart)
    {
        $full_name = explode(' ', (string) $message);
        if (isset($full_name[0]) && isset($full_name[1])) {
            $first_name = $full_name[0];
            $last_name = $full_name[1];

            $cart->customer->update([
                'first_name' => $first_name,
                'last_name' => $last_name,
            ]);

            $message = static::bot()->getSetting('address_request_message');
            if (!empty($message)) {
                $params = Messenger::getSimpleMessageParameters($message, $psId);
                $quick_replies[] = ['content_type' => 'location'];
                $params['message']['quick_replies'] = $quick_replies;

                $response = FacebookPage::sendMessage($params);

                if ($response) {
                    static::createCommand($site_id, $psId, $response->getDecodedBody(), MessengerCommand::ADDRESS_REQUEST);
                }
            }
        } else {
            $message = static::bot()->getSetting('invalid_name_format_message');

            if (!empty($message)) {
                $params = Messenger::getSimpleMessageParameters($message, $psId);
                $response = FacebookPage::sendMessage($params);
                if ($response) {
                    static::createCommand($site_id, $psId, $response->getDecodedBody(), MessengerCommand::NAME_REQUEST);
                }
            }
        }
    }

    /**
     * @param $psId
     * @param $site_id
     * @param $message
     * @param CartModel $cart
     * @param MessengerSubscriber $subscriber
     * @throws Error
     * @throws \Facebook\Exceptions\FacebookSDKException
     */
    protected static function handleEmail(string $psId, $site_id, $message, $cart, $subscriber)
    {
        $pattern = "/[a-zA-Z0-9_\\-.+]+@[a-zA-Z0-9-]+.[a-zA-Z]+/";
        preg_match($pattern, (string) $message, $matches);
        $email = reset($matches);

        if ($email) {
            if ($customer = Customer::customerEmail($email)->first()) {
                $bot_checkout_url = Linker::fullLink(Linker::messengerBotCheckout()) . \App\Integration\Facebook\Services\Messenger::UTM . $psId;

                $message = static::bot()->getSetting('email_exists_message');
                if (!empty($message)) {
                    $message = str_replace('{$checkout_url}', $bot_checkout_url, $message);

                    $incorrect_data_payload = site('site_id') . '-' . static::WRONG_CUSTOMER_DATA_TYPE;
                    $quick_replies[] = Messenger::getQuickReplyParams($incorrect_data_payload, __('facebook.btn.change.data'));

                    $params = Messenger::getSimpleMessageParameters($message, $psId);
                    $params['message']['quick_replies'] = $quick_replies;
                    FacebookPage::sendMessage($params, $subscriber->id);
                }
            } else {
                try {
                    static::updateCustomer($cart, $email, $subscriber);
                } catch (Error) {
                    static::sendWrongEmailMessage($site_id, $psId);
                    return;
                }

                $meta = $cart->meta()->firstOrNew([
                    'parameter' => 'cart.messenger.subscriber.email'
                ])->fill(['value' => $email]);

                $meta->save();

                if ($cart->customer && !is_null($cart->customer->first_name) && !is_null($cart->customer->last_name) && !is_null($cart->customer->shipping_address)) {
                    static::sendCustomerOldDataVerifyEmail($cart, $psId);
                } else {
                    static::confirmCustomerNamesMessage($psId, $cart);
                }
            }
        } else {
            static::sendWrongEmailMessage($site_id, $psId);
        }
    }

    /**
     * @param $site_id
     * @param $psId
     * @throws Error
     * @throws Exception
     */
    protected static function sendWrongEmailMessage($site_id, $psId)
    {
        $message = static::bot()->getSetting('invalid_email_message');
        if (!empty($message)) {
            $params = Messenger::getSimpleMessageParameters($message, $psId);
            $response = FacebookPage::sendMessage($params);
            if ($response) {
                static::createCommand($site_id, $psId, $response->getDecodedBody(), MessengerCommand::EMAIL_REQUEST);
            }
        }
    }

    /**
     * @param $psId
     * @param $site_id
     * @param $message
     * @param CartModel $cart
     * @throws Error
     * @throws Error
     * @throws Exception
     */
    protected static function handlePhone(string $psId, $site_id, $message, $cart)
    {
        $pattern = "/[0-9|\\+]{1,2}[0-9\\- ]*/";
        preg_match($pattern, (string) $message, $matches);
        $phone = reset($matches);

        if ($phone) {
            $meta = $cart->meta()->firstOrNew([
                'parameter' => 'cart.messenger.subscriber.phone'
            ])->fill(['value' => $phone]);

            $meta->save();
            $cart->customer->shipping_address->update(['phone' => $phone]);

            static::sendCustomerDataVerifyEmail($cart->customer->shipping_address->text, $cart, $psId);
        } else {
            $message = static::bot()->getSetting('invalid_phone_message');
            if (!empty($message)) {
                $payload = site('site_id') . "-stop_bot_answers-" . MessengerBot::QUICK_REPLY . '-' . $psId;
                $stop_bot_quick_reply = Messenger::getQuickReplyParams($payload, __('facebook.action.stop.bot'));

                $params = Messenger::getSimpleMessageParameters($message, $psId);
                $params['message']['quick_replies'][] = $stop_bot_quick_reply;
                $response = FacebookPage::sendMessage($params);

                if ($response) {
                    static::createCommand($site_id, $psId, $response->getDecodedBody(), MessengerCommand::PHONE_REQUEST);
                }
            }
        }
    }

    /**
     * @param $psId
     * @param $site_id
     * @throws Error
     * @throws Exception
     */
    protected static function handleAddressInvalidAnswer(string $psId, $site_id)
    {
        $message = static::bot()->getSetting('invalid_address_message');
        if (!empty($message)) {
            $params = Messenger::getSimpleMessageParameters($message, $psId);

            $payload = site('site_id') . "-stop_bot_answers-" . MessengerBot::QUICK_REPLY . '-' . $psId;
            $stop_bot_quick_reply = Messenger::getQuickReplyParams($payload, __('facebook.action.stop.bot'));

            $quick_replies = [
                ['content_type' => 'location'],
                $stop_bot_quick_reply
            ];

            $params['message']['quick_replies'] = $quick_replies;
            $response = FacebookPage::sendMessage($params);

            if ($response) {
                static::createCommand($site_id, $psId, $response->getDecodedBody(), MessengerCommand::ADDRESS_REQUEST);
            }
        }
    }

    /**
     * @param CartModel $cart
     * @param $key
     * @param $value
     */
    protected static function setMeta($cart, $key, $value)
    {
        /** @var Meta $meta */
        $meta = $cart->meta()->firstOrNew([
            'parameter' => $key
        ])->fill(['value' => $value]);
        $meta->save();
    }

    /**
     * @param Product $product
     * @param $psId
     * @param $parameter_index
     * @param array $data
     * @param int|null $start_index
     * @throws Error
     * @throws \Throwable
     */
    protected static function sendMessageWithAdditionalQuestions($product, $psId, $parameter_index, array $data = [], $start_index = 0)
    {
        $v1_id = $data[4] ?? null;
        $v2_id = $data[6] ?? null;
        $v3_id = $data[8] ?? null;

        $should_choose_parameter = is_null($v3_id) ? static::chooseProductParameterMessage($product, $parameter_index, $v1_id, $v2_id, $start_index, $psId) : false;

        if (!$should_choose_parameter) {
            $variant = Variant::where('item_id', $product->id)->where('v1_id', $v1_id)->where('v2_id', $v2_id)->where('v3_id', $v3_id)->first();
            $variant_id = !is_null($variant) ? $variant->id : null;

            static::addItemToCart($psId, $product, $variant_id);
        }
    }

    /**
     * @param Product $product
     * @param $parameter_index
     * @param $v1_id
     * @param $v2_id
     * @param int $start_index
     * @return array
     */
    protected static function getProductParametersMessageQuickReplies($product, string $parameter_index, string $v1_id, string $v2_id, $start_index = 0): array
    {
        $product_params = static::getParameters($product, $parameter_index, $v1_id, $v2_id);

        $product_params_first_count = count($product_params);
        $all_params = $product_params;

        if ($start_index || $product_params_first_count > 8) {
            $product_params = array_slice($all_params, $start_index, 8);
        }

        if (!empty($product_params)) {
            $quick_replies = [];
            foreach ($product_params as $param_value => $param_id) {
                $payload = match ((int) $parameter_index) {
                    2 => site('site_id') . '-' . static::PARAMETER_PICKED_TYPE . '-' . $product->id . '-v1-' . $v1_id . '-v2-' . $param_id,
                    3 => site('site_id') . '-' . static::PARAMETER_PICKED_TYPE . '-' . $product->id . '-v1-' . $v1_id . '-v2-' . $v2_id . '-v3-' . $param_id,
                    default => site('site_id') . '-' . static::PARAMETER_PICKED_TYPE . '-' . $product->id . '-v1-' . $param_id,
                };

                $quick_replies[] = Messenger::getQuickReplyParams($payload, $param_value);
            }

            if ($product_params_first_count > 8) {
                $start_index += 8;
                $next_objects = array_slice($all_params, $start_index);

                if ($next_objects !== []) {
                    $more_quick_reply_payload = site('site_id') . '-more-product_parameters-' . $product->id . '-' . $parameter_index . '-' . $v1_id . '-' . $v2_id . '-' . $start_index;
                    $quick_replies[] = Messenger::getQuickReplyParams($more_quick_reply_payload, __('global.action.view_more'));
                }
            }

            return $quick_replies;
        }

        return [];
    }

    /**
     * @param $product
     * @param $index
     * @param string $v1_id
     * @param string $v2_id
     * @return array
     */
    protected static function getParameters($product, string $index, $v1_id = '', $v2_id = ''): array
    {
        $parameters = [];

        $p = 'p' . $index;
        $v = 'v' . $index;
        $v_id = 'v' . $index . '_id';

        if (empty($v1_id)) {
            $variants = $product->variants ? $product->variants->keyBy('id') : null;
        } elseif (empty($v2_id)) {
            $variants = Variant::where('item_id', $product->id)->where('v1_id', $v1_id)->get();
        } else {
            $variants = Variant::where('item_id', $product->id)->where('v1_id', $v1_id)->where('v2_id', $v2_id)->get();
        }

        if ($variants) {
            foreach ($variants as $variant) {
                if (!empty($product->$p)) {
                    $parameters[$variant->$v] = $variant->$v_id;
                }
            }
        }

        return $parameters;
    }

    /**
     * @param $psId
     * @param Product $product
     * @param null $variant_id
     * @throws Error
     * @throws \Throwable
     */
    protected static function addItemToCart($psId, $product, $variant_id = null)
    {
        $subscriber = MessengerSubscriber::where('psid', $psId)->first();

        $cart = null;
        if ($subscriber->cart_id) {
            $cart = CartModel::find($subscriber->cart_id);
        }

        if (!$cart && $subscriber->cart_key) {
            $cart = CartModel::where('key', $subscriber->cart_key)->first();
        }

        if (!$cart) {
            $cart = CartModel::instance(true);
        }

        $subscriber->update(['cart_id' => $cart->id, 'cart_key' => $cart->key]);

        $item = CartItem::toTemporary($product->variant, [
            'quantity' => 1
        ], $cart);

        $cart->add($item);

        if ($cart->customer) {
            $params = static::getProductsParameters($cart, $cart->products, $psId);
            if (!empty($params)) {
                FacebookPage::sendMessage($params, $subscriber->id);
            }
        } else {
            $email_request_message = static::bot()->getSetting('email_request_message');
            if (strlen((string) $email_request_message)) {
                static::sendMessageForEmailRequest($psId, $email_request_message);
            }
        }
    }

    /**
     * @param $psId
     * @param CartModel $cart
     * @throws Error
     * @throws \Facebook\Exceptions\FacebookSDKException
     */
    protected static function confirmCustomerNamesMessage($psId, $cart)
    {
        $confirm_customer_names_message = static::bot()->getSetting('confirm_customer_names_message');

        if (!empty($confirm_customer_names_message)) {
            $confirm_customer_names_message = str_replace('{$first_name}', $cart->customer->first_name, $confirm_customer_names_message);
            $confirm_customer_names_message = str_replace('{$last_name}', $cart->customer->last_name, $confirm_customer_names_message);

            $site_id = site('site_id');
            $params = Messenger::getSimpleMessageParameters($confirm_customer_names_message, $psId);
            $customer_id = $cart->customer->id;

            $correct_data_payload = $site_id . '-' . static::CONFIRM_CUSTOMER_NAME_TYPE . '-' . $customer_id . '-' . $cart->id;
            $quick_replies[] = Messenger::getQuickReplyParams($correct_data_payload, __('global.yes'));

            $incorrect_data_payload = $site_id . '-' . static::WRONG_CUSTOMER_NAME_TYPE . '-' . $customer_id . '-' . $cart->id;
            $quick_replies[] = Messenger::getQuickReplyParams($incorrect_data_payload, __('global.no'));

            $params['message']['quick_replies'] = $quick_replies;
            FacebookPage::sendMessage($params);
        }
    }

    /**
     * @param $psId
     * @param $message
     * @param bool $customer_data_was_wrong
     * @throws Exception
     */
    protected static function sendMessageForEmailRequest(string $psId, $message, $customer_data_was_wrong = false)
    {
        $site_id = site('site_id');
        $params = Messenger::getSimpleMessageParameters($message, $psId);

        if ($customer_data_was_wrong) {
            $payload = site('site_id') . "-stop_bot_answers-" . MessengerBot::QUICK_REPLY . '-' . $psId;
            $stop_bot_quick_reply = Messenger::getQuickReplyParams($payload, __('facebook.action.stop.bot'));
            $params['message']['quick_replies'][] = $stop_bot_quick_reply;
        }

        $response = FacebookPage::sendMessage($params);

        if ($response) {
            static::createCommand($site_id, $psId, $response->getDecodedBody(), MessengerCommand::EMAIL_REQUEST);
        }
    }

    /**
     * @param $site_id
     * @param $psId
     * @param $params
     * @param $command_type
     * @throws Exception
     */
    protected static function createCommand($site_id, $psId, array $params, $command_type)
    {
        MessengerCommand::where('site_id', $site_id)
            ->where('recipient_id', $psId)
            ->delete();

        MessengerCommand::create([
            'site_id' => $site_id,
            'command' => $command_type,
            'recipient_id' => $psId,
            'message_id' => $params['message_id'],
        ]);
    }

    /**
     * @param CartModel $cart
     * @param $email
     * @param MessengerSubscriber $subscriber
     * @throws Error
     */
    protected static function updateCustomer($cart, $email, $subscriber)
    {
        [$first_name, $last_name] = !is_null($subscriber->username) ? explode(' ', (string) $subscriber->username) : null;

        $guest = Customer::guestEmail($email)->first();
        if (empty($guest)) {
            $guest = Customer::addGuest([
                'first_name' => $first_name,
                'last_name' => $last_name,
                'email' => $email
            ]);
        }

        $cart->update(['user_id' => $guest->id]);
        $subscriber->update(['customer_id' => $guest->id]);
    }

    /**
     * @param CartModel $cart
     * @param $psId
     * @throws Error
     * @throws \Facebook\Exceptions\FacebookSDKException
     */
    protected static function sendCustomerOldDataVerifyEmail($cart, $psId)
    {
        $success_message = static::bot()->getSetting('guest_email_exists_message');

        if (!empty($success_message)) {
            $success_message = str_replace('{$first_name}', $cart->customer->first_name, $success_message);
            $success_message = str_replace('{$last_name}', $cart->customer->last_name, $success_message);
            $success_message = str_replace('{$address}', $cart->customer->shipping_address->text, $success_message);

            $cart->update(['shipping_address_id' => $cart->customer->shipping_address->id]);

            $params = Messenger::getSimpleMessageParameters($success_message, $psId);

            $correct_data_payload = site('site_id') . '-' . static::CONFIRM_CUSTOMER_OLD_DATA_TYPE . '-' . $cart->customer->id . '-' . $cart->id;
            $quick_replies[] = Messenger::getQuickReplyParams($correct_data_payload, __('global.yes'));

            $incorrect_data_payload = site('site_id') . '-' . static::WRONG_CUSTOMER_OLD_DATA_TYPE . '-' . $cart->customer->id . '-' . $cart->id;
            $quick_replies[] = Messenger::getQuickReplyParams($incorrect_data_payload, __('global.no'));

            $params['message']['quick_replies'] = $quick_replies;
            FacebookPage::sendMessage($params);
        }
    }

    /**
     * @param $address
     * @param CartModel $cart
     * @param $psId
     * @throws Error
     * @throws \Facebook\Exceptions\FacebookResponseException
     * @throws \Facebook\Exceptions\FacebookSDKException
     */
    protected static function sendCustomerDataVerifyEmail($address, $cart, $psId)
    {
        $message = static::bot()->getSetting('cart_summary_message');
        if (!empty($message)) {
            $message = str_replace('{$first_name}', $cart->customer->first_name, $message);
            $message = str_replace('{$last_name}', $cart->customer->last_name, $message);
            $message = str_replace('{$email}', $cart->getMeta('cart.messenger.subscriber.email'), $message);
            $message = str_replace('{$phone}', $cart->getMeta('cart.messenger.subscriber.phone'), $message);
            $message = str_replace('{$address}', $address, $message);

            $params = Messenger::getSimpleMessageParameters($message, $psId);

            $customer_id = $cart->customer->id;

            $correct_data_payload = site('site_id') . '-' . static::CONFIRM_CUSTOMER_DATA_TYPE . '-' . $customer_id . '-' . $cart->id;
            $quick_replies[] = Messenger::getQuickReplyParams($correct_data_payload, __('global.yes'));

            $incorrect_data_payload = site('site_id') . '-' . static::WRONG_CUSTOMER_DATA_TYPE . '-' . $customer_id . '-' . $cart->id;
            $quick_replies[] = Messenger::getQuickReplyParams($incorrect_data_payload, __('global.no'));

            $params['message']['quick_replies'] = $quick_replies;
            FacebookPage::sendMessage($params);
        }
    }

    /**
     * @param $cart
     * @param Collection|CartItem[] $cart_items
     * @param $psId
     * @return array
     * @throws Error
     */
    protected static function getProductsParameters($cart, $cart_items, string $psId): array
    {
        if ($cart_items->count()) {
            app()->offsetSet('widget', new Widget());

            $payload = [
                'template_type' => 'generic',
                'image_aspect_ratio' => 'SQUARE',
                'sharable' => true,
            ];

            $bot_checkout_url = Linker::fullLink(Linker::messengerBotCheckout()) . \App\Integration\Facebook\Services\Messenger::UTM . $psId;
            ;

            foreach ($cart_items as $cart_item) {
                $params = [
                    'title' => $cart_item->product->name,
                    'subtitle' => $cart_item->price_formatted,
                    'image_url' => $cart_item->product->getImage('1280x1280'),
                    'default_action' => [
                        'type' => 'web_url',
                        'url' => $cart_item->product->url,
                    ]
                ];

                $params['buttons'] = [
                    [
                        'type' => 'postback',
                        'title' => static::bot()->getSetting('remove_from_cart_button_title'),
                        'payload' => site('site_id') . '-' . static::REMOVE_PRODUCT_FROM_CART_TYPE . '-' . $cart->id . '-' . $cart_item->id,
                    ],
                    [
                        'type' => 'web_url',
                        'url' => $bot_checkout_url,
                        'title' => static::bot()->getSetting('finish_order_button_title'),
                        'webview_height_ratio' => 'full',
                        'messenger_extensions' => 'false',
                    ],
                ];

                $payload['elements'][] = $params;
            }

            return [
                'messaging_type' => 'NON_PROMOTIONAL_SUBSCRIPTION',
                'recipient' => [
                    'id' => $psId,
                ],
                'message' => [
                    'attachment' => [
                        'type' => 'template',
                        'payload' => $payload
                    ],
                ]
            ];
        }

        return [];
    }

    /**
     * @param $psId
     * @param Order $order
     * @return array
     * @throws Error
     */
    protected static function receiptMessageTemplate(string $psId, $order): array
    {
        $discounts = $order->discounts;

        $adjustments = [];
        foreach ($discounts as $discount) {
            $adjustments[] = [
                'name' => $discount->name,
                'amount' => $discount->type_value / 100
            ];
        }

        $elements = [];

        /**@var OrderProduct[] $products */
        $products = $order->products->take(10);
        foreach ($products as $product) {
            $price = money($priceInt = round($product->getTotalPriceWithOptionsAfterDiscountsWithModification() / $product->getQuantity()), $product->order->getCurrency(), $product->order->getLanguage());
            $priceInput = moneyInput($priceInt, $product->order->getCurrency());
            $elements[] = [
                'title' => $product->getName(),
                'subtitle' => $price,
                'quantity' => $product->getQuantity(),
                'price' => $priceInput,
                'currency' => site('currency'),
                'image_url' => $product->getImage('1280x1280'),
            ];
        }

        $order_url = \Linker::returnPageUrl($order->status, $order->payment->hash) . \App\Integration\Facebook\Services\Messenger::UTM . $psId;

        $message = [
            'attachment' => [
                'type' => 'template',
                'payload' => [
                    'template_type' => 'receipt',
                    'recipient_name' => $order->customer_first_name . ' ' . $order->customer_last_name,
                    'order_number' => $order->id,
                    'currency' => site('currency'),
                    'payment_method' => $order->payment->provider_name,
                    'order_url' => $order_url,
                    'summary' => [
                        'subtotal' => $order->price_subtotal / 100,
                        'shipping_cost' => !is_null($order->shipping) ? $order->shipping->order_amount / 100 : 0,
                        'total_tax' => $order->sumTaxes() / 100,
                        'total_cost' => $order->price_total / 100,
                    ],
                    'adjustments' => $adjustments,
                    'elements' => $elements,
                ]
            ]
        ];

        if (!is_null($order->shippingAddress)
            && !empty($order->shippingAddress->street_name)
            && !empty($order->shippingAddress->city_name)
            && !empty($order->shippingAddress->post_code)
            && !empty($order->shippingAddress->state_name)
            && !empty($order->shippingAddress->country_name)
        ) {
            $message['attachment']['payload']['address'] = [
                'street_1' => $order->shippingAddress->street_name,
                'street_2' => '',
                'city' => $order->shippingAddress->city_name,
                'postal_code' => $order->shippingAddress->post_code,
                'state' => $order->shippingAddress->state_name,
                'country' => $order->shippingAddress->country_name,
            ];
        }

        return [
            'recipient' => [
                'id' => $psId,
            ],
            'message' => $message
        ];
    }
}
