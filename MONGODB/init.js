load("include/args.js");

//usage:
// mongo --quiet --nodb --eval 'let args={connection: "production"};' script.js
// mongo --quiet --nodb --eval 'let args={connection: "production", drop:false/true};' customer/new.js
let db = null;
if(cliArgs.get('connection') === 'production') {
    db = connect('analytics:7z5ky619kfnx845g@***********:27017/analytics');
} else {
    db = connect('razpusni.com:27017/analytics');
}

load("model/settings.js");
load("include/helpers.js");
load("include/collection.js");
load("include/date.js");