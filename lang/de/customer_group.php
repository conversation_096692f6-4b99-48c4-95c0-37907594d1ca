<?php

return [
    'action.add' => 'Kundengruppe hinzufügen',
    'confirm.delete' => '<PERSON><PERSON> du sicher, dass du diese Kundengruppe löschen möchtest?',
    'err.cannot_delete_default' => 'Die Standard-Kundengruppe kann nicht gelöscht werden',
    'err.cannot_delete_guests' => 'Gäste können nicht gelöscht werden',
    'err.cannot_delete_one_with_customers' => 'Die Kundengruppe mit zugewiesenen Kunden kann nicht gelöscht werden. Bearbeite die Kunden und versuche es erneut!',
    'err.cannot_delete_one_with_discounts' => 'Die Kundengruppe mit zugewiesenen Rabatten kann nicht gelöscht werden. Bearbeite die Rabatte und versuche es erneut!',
    'err.cannot_edit_default' => 'Die Standard-Kundengruppe kann nicht bearbeitet werden!',
    'err.cannot_name_like_default' => 'Der Name der Kundengruppe ist bereits vergeben!',
    'err.choose' => 'Wähle eine Kundengruppe aus!',
    'err.name_max_chars_%1$s' => 'Die Länge des Namens der Kundengruppe ist überschritten (%1$s)',
    'err.name_requred' => 'Der Name der Kundengruppe ist erforderlich!',
    'err.name_taken' => 'Der Name der Kundengruppe ist bereits vergeben!',
    'err.no_longer_exists' => 'Die Kundengruppe existiert nicht mehr!',
    'err.requred' => 'Die Kundengruppe ist erforderlich!',
    'filter.has_customers' => 'Hat Kunden',
    'filter.has_no_customers' => 'Hat keine Kunden',
    'filter.is_%1$s' => 'Die Kundengruppe ist %1$s',
    'header.add' => 'Kundengruppe hinzufügen',
    'header.customer_groups' => 'Kundengruppen',
    'header.edit' => 'Kundengruppe bearbeiten',
    'help.name' => 'Z.B. Loyal, VIP',
    'info.none_added_yet' => 'Noch keine Kundengruppen hinzugefügt!',
    'label.name' => 'Name',
    'misc.undefined' => 'Nicht definiert',
    'ph.name' => 'Gib den Namen der Kundengruppe ein',
    'succ.add' => 'Kundengruppe erfolgreich hinzugefügt',
    'succ.edit' => 'Kundengruppe erfolgreich bearbeitet',
    'th.customers' => 'Kunden',
    'th.name' => 'Name',
];
