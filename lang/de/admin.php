<?php

return [
    'err.admin_username_exists' => 'Ein Admin mit diesem Namen existiert bereits',
    'err.city_max_chars_%1$s' => 'Ort darf nicht länger als %1$s <PERSON><PERSON>chen sein',
    'err.email_already_taken' => 'Diese E-Mail wird bereits verwendet',
    'err.email_required' => 'E-Mail ist erforderlich',
    'err.first_name_max_chars_%1$s' => 'Vorname darf nicht länger als %1$s Zeichen sein',
    'err.last_name_max_chars_%1$s' => 'Vorname darf nicht kürzer als %1$s Zeichen sein',
    'err.no_such_admin' => 'Es gibt keinen solchen Admin',
    'err.password_cannot_be_empty' => 'Passwort ist erforderlich',
    'err.phone_max_chars_%1$s' => 'Telefon darf nicht länger als %1$s <PERSON><PERSON>chen sein',
    'err.postal_code_max_chars_%1$s' => '<PERSON><PERSON>itzahl darf nicht länger als %1$s Zeichen sein',
    'err.street_max_chars_%1$s' => 'Straße darf nicht länger als %1$s Zeichen sein',
    'err.two_passwords_mismatch' => 'Die beiden Passwörter stimmen nicht überein',
    'err.type_required' => 'Typ ist erforderlich',
    'err.unique_id_required' => 'Eindeutige ID ist erforderlich',
    'err.username_required' => 'Benutzername ist erforderlich',
];
