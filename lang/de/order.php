<?php

return [
    'Archive' => 'Archiv',
    'Barcode' => 'Barcode',
    'Created' => 'Erstellt',
    'Discount' => '<PERSON><PERSON><PERSON>',
    'Free_shipping' => 'Kostenloser Versand',
    'Guest' => 'Gast',
    'Manual_discount' => '<PERSON>ler Rabatt',
    'Mark as completed' => 'Als abgeschlossen markieren',
    'Marked_order_payment_as_received' => 'Zahlung der Bestellung als erhalten markiert',
    'Message' => 'Nachricht',
    'Order_added_with_id' => 'Bestellung mit ID hinzugefügt',
    'Order_customer_fields' => 'Kundenfelder der Bestellung',
    'Order_status' => 'Bestellstatus',
    'Product' => 'Produkt',
    'Product_name' => 'Produktname',
    'Product_price' => 'Produktpreis',
    'Product_quantity' => 'Produktmenge',
    'SKU' => 'Artikelnummer (SKU)',
    'Sale' => 'Verkauf',
    'Unarchive' => 'Wied<PERSON>herstellen',
    'Vendor' => 'Marke',
    'Weight' => 'Gewicht',
    'abandoned' => 'Verlassen',
    'abandoned.confirm.delete' => 'Bist du sicher, dass du die ausgewählten verlassenen Bestellungen entfernen möchtest?',
    'abandoned_count' => 'Insgesamt gesendete Nachrichten: :total',
    'action.abandoned_send_restore_link_subscriber' => 'Wiederherstellungs-E-Mail senden',
    'action.add_order' => 'Eine Bestellung erstellen',
    'action.add_order_discount' => 'Bestellrabatt hinzufügen',
    'action.add_product_discount' => 'Produkt-Rabatt hinzufügen',
    'action.archive' => 'Archivieren',
    'action.cancel_order' => 'Bestellung stornieren',
    'action.cancel_payment' => 'Zahlung stornieren',
    'action.choose_shipping_provider' => 'Versandmethode auswählen',
    'action.choose_status' => 'Status auswählen',
    'action.complete' => 'Abschließen',
    'action.create_credit_note' => 'Gutschrift erstellen',
    'action.download_credit_note' => 'Gutschrift herunterladen',
    'action.export' => 'Exportieren',
    'action.fulfill_products' => 'Produkte erfüllen',
    'action.history' => 'Verlauf',
    'action.mark_as_paid' => 'Als bezahlt markieren',
    'action.notify.credit_note_create_error' => 'Aktuell können wir keine Gutschrift erstellen.',
    'action.notify.credit_note_created' => 'Die Gutschrift wurde erstellt.',
    'action.notify.credit_note_send_error' => 'Aktuell können wir die Gutschrift nicht an den Kunden senden.',
    'action.notify.credit_note_sent' => 'Die Gutschrift wurde an den Kunden gesendet.',
    'action.payment_add' => 'Zahlung hinzufügen',
    'action.payment_authorize' => 'Zahlung erfassen (:amount)',
    'action.payment_cancel_authorization' => 'Autorisierung stornieren',
    'action.payment_complete' => 'Zahlung abschließen',
    'action.payment_lease' => 'Anfrage an die Bank senden',
    'action.payment_refund' => 'Rückerstattung',
    'action.payment_sync' => 'Synchronisieren',
    'action.payment_void' => 'Ungültig machen',
    'action.print_order' => 'Bestellung drucken',
    'action.product_add' => 'Produkt hinzufügen',
    'action.remove_order_discount' => 'Bestellrabatt entfernen',
    'action.remove_order_modifications' => 'Entfernen Sie den Rabatt aus den "Warenkorbregeln"',
    'action.remove_product_discount' => 'Produkt-Rabatt entfernen',
    'action.send_credit_note' => 'Gutschrift an den Kunden senden',
    'action.ship_items' => 'Produkte erfüllen',
    'action.success_cod' => 'Nachnahme erfolgreich bezahlt. Betrag:',
    'action.sync' => 'Zahlungsinformationen fehlen',
    'action.unarchive' => 'Wiederherstellen',
    'action.view_credit_note' => 'Gutschrift',
    'action.view_details' => 'Details anzeigen',
    'action.view_invoice' => 'Rechnung anzeigen',
    'action.view_more' => 'Mehr anzeigen',
    'add.new_address' => 'Neue Adresse hinzufügen',
    'add_address' => 'Adresse hinzufügen',
    'add_customer' => 'Kunden hinzufügen',
    'add_more_cod' => 'Zusätzliches Paket kaufen',
    'added' => 'Hinzugefügt',
    'added_to_order' => 'Zur Bestellung hinzugefügt',
    'added_to_order_product' => 'Produkt zur Bestellung hinzugefügt',
    'addon.percent_discount' => '%',
    'address' => 'Anschrift',
    'address.not_fount' => 'Keine Adressen für den ausgewählten Kunden gefunden.',
    'address_add_shipping' => 'Versandadresse hinzufügen',
    'address_change_billing' => 'Rechnungsadresse ändern',
    'address_change_shipping' => 'Versandadresse ändern',
    'address_edit_billing' => 'Rechnungsadresse bearbeiten',
    'address_edit_shipping' => 'Versandadresse bearbeiten',
    'address_reposition' => 'Neu positionieren',
    'address_reposition_billing' => 'Rechnungsadresse neu positionieren',
    'address_reposition_shipping' => 'Versandadresse neu positionieren',
    'admin_note' => 'Administrative Notiz',
    'aggregate.success' => 'Die Downloadanfrage war erfolgreich. In wenigen Minuten erhältst du eine E-Mail mit einem Downloadlink.',
    'aggregate.type' => 'Rechnungen',
    'alert.manual_order' => 'Kundenbestellabschlussadresse:',
    'alert.manual_order_help' => 'Gib einen Link an deinen Kunden, um die Bestellung abzuschließen oder',
    'alert.send_as_email' => 'als E-Mail senden.',
    'alert_is_draft' => 'Du musst <b> Produkte </b>, <b> Zahlungsmethode </b> und <b> Liefermethode </b> hinzufügen, um deine Bestellung zu speichern.',
    'alert_is_draft.and' => 'und',
    'alert_is_draft.end' => 'um deine Bestellung zu speichern.',
    'alert_is_draft.payment' => '<b> Zahlungsmethode </b>',
    'alert_is_draft.products' => '<b> Produkte </b>',
    'alert_is_draft.shipping' => '<b> Liefermethode </b>',
    'alert_is_draft.start' => 'Du musst hinzufügen',
    'amount' => 'Betrag',
    'archived' => 'Archiviert',
    'are_you_sure' => 'Wenn du diese Bestellung stornierst, kannst du sie nicht wiederherstellen',
    'banned_ip.add' => 'Neue IP blockieren',
    'banned_ip.add_from_order' => 'Blockieren',
    'banned_ip.ban' => 'IP-Adressen von Kunden blockieren',
    'banned_ip.ban_reason' => 'Die Bestellung wurde von einer blockierten IP-Adresse aus gemacht. Grund:',
    'banned_ip.ip_address' => 'IP-Adresse',
    'billing_address' => 'Rechnungsadresse',
    'button.allow_recalculate' => 'Neuberechnung des Versandpreises erlauben',
    'button.confirm' => 'Bestätigen',
    'button.disallow_recalculate' => 'Neuberechnung des Versandpreises nicht erlauben',
    'change.payment_success' => 'Du hast deine Zahlungsmethode erfolgreich geändert',
    'changed_to' => 'Geändert zu',
    'choose_another_address' => 'Adresse ändern',
    'choose_discount' => 'Rabatt auswählen',
    'choose_discount_target' => 'Rabattziel auswählen',
    'choose_existing_address' => 'Vorhandene Adresse auswählen',
    'confirm.archive' => 'Archivieren',
    'confirm.complete' => 'Abschließen',
    'confirm.mark_as_paid_confirm' => 'Wenn du die Bestellung als bezahlt markierst, kannst du ihren Status nicht mehr ändern',
    'confirm.order_product_delete_confirm' => 'Bist du sicher, dass du dieses Produkt aus der Bestellung löschen möchtest?',
    'confirm.remove_order_discount' => 'Bestellrabatt entfernen',
    'confirm.remove_order_modifications' => 'Sind Sie sicher, dass Sie den Rabatt aus den "Warenkorbregeln" entfernen möchten?',
    'confirm.remove_product_discount_confirm_message' => 'Rabatt entfernt',
    'confirm.unarchive' => 'Wiederherstellen',
    'confirm_order.title_modal' => 'Bestellung bestätigen in',
    'create_invoice' => 'Rechnung erstellen',
    'create_order' => 'Bestellung erstellen',
    'create_order_and_send_client' => 'Bestellung erstellen und an den Kunden senden',
    'created' => 'Bestellung wurde erstellt',
    'credit_create_date' => 'Ausstellungsdatum der Gutschrift',
    'credit_number' => 'Gutschriftnummer',
    'crossSell.discount' => 'Cross Sell',
    'customer' => 'Kunde',
    'customer_completed_orders' => 'Abgeschlossene Bestellungen',
    'customer_edit' => 'Kunden bearbeiten',
    'customer_email' => 'Kunden-E-Mail',
    'customer_group' => 'Kundengruppe',
    'customer_total_orders' => 'Gesamtbestellungen',
    'customer_view_profile' => 'Profil anzeigen',
    'date' => 'Datum',
    'date_added' => 'Hinzugefügt am',
    'date_archived' => 'Archiviert am',
    'date_fulfilled' => 'Erfüllt am',
    'date_paid' => 'Bezahlt am',
    'date_updated' => 'Zuletzt aktualisiert am',
    'deleted' => 'Bestellung wurde gelöscht',
    'discount' => 'Rabatt',
    'discount_type_flat' => 'Festbetrag',
    'discount_type_percent' => 'Prozentual',
    'down payment' => 'Anzahlung',
    'download.credit' => 'Gutschrift',
    'download.invoice' => 'Rechnung',
    'draft.button_1' => 'Das System speichert die Änderungen und erstellt Bestellungen',
    'draft.button_2' => 'Das System speichert die Änderungen und sendet eine E-Mail an den Kunden mit einem Link zum Abschluss der Bestellung.',
    'draft.button_3' => 'Das System sendet eine E-Mail an den Kunden mit einer Adresse zum Abschluss / Bezahlen der Bestellung',
    'edited_to' => 'Bearbeitet zu',
    'err.abandoned_no_emails_sent' => 'Keine E-Mails gesendet. Du kannst nur 1 E-Mail pro Kunde und Tag senden!',
    'err.address_no_longer_exists' => 'Die Adresse existiert nicht mehr',
    'err.begin_date_invalid_format' => 'Ungültiges Format für das Startdatum',
    'err.cannot_add_digital_product_already_in_cart' => 'Das digitale Produkt, das du hinzufügen möchtest, ist bereits in der Bestellung vorhanden.',
    'err.cannot_change_status_of_archived_order_unarchive_first' => 'Kann den Status der archivierten Bestellung nicht ändern. Zuerst wiederherstellen.',
    'err.cannot_complete_cancelled_order' => 'Kann die stornierte Bestellung nicht abschließen',
    'err.cannot_perform_this_operation_on_archived_order' => 'Kann diese Operation nicht auf der archivierten Bestellung durchführen',
    'err.cannot_ship_items_without_shipping_provider' => 'Produkte können ohne Versandmethode nicht erfüllt werden.',
    'err.choose_customer_adddress' => 'Kundenadresse nicht angegeben',
    'err.creation_failed' => 'Bestellung fehlgeschlagen',
    'err.customer_email_requred' => 'Kunden-E-Mail ist erforderlich',
    'err.customer_first_name_cannot_be_more_than_%1$s_characters' => 'Der Vorname des Kunden darf nicht mehr als %1$s Zeichen lang sein',
    'err.customer_first_name_empty' => 'Der Vorname des Kunden ist leer',
    'err.customer_has_no_addresses' => 'Der Kunde hat keine Adresse',
    'err.customer_last_name_cannot_be_more_than_%1$s_characters' => 'Der Nachname des Kunden darf nicht mehr als %1$s Zeichen lang sein',
    'err.customer_last_name_empty' => 'Der Nachname des Kunden ist leer',
    'err.daterange_required' => 'Datumsbereich ist erforderlich',
    'err.delete_status_orders' => 'Dieser Status hat angehängte: Gesamtbestellungen',
    'err.discount_negative_type_value' => 'Der Wert des Rabatts darf den Zwischensumme der Bestellung nicht überschreiten',
    'err.discount_no_longer_active' => 'Der Rabatt ist nicht mehr aktiv',
    'err.discount_no_longer_exists' => 'Der Rabatt existiert nicht mehr',
    'err.discount_not_enough_uses_left' => 'Nicht genügend Verwendungen des Rabatts übrig',
    'err.discount_target_for_different_category' => 'Dieser Rabatt ist für eine andere Kategorie vorgesehen',
    'err.discount_target_for_different_customer_group' => 'Dieser Rabatt ist für eine andere Kundengruppe vorgesehen',
    'err.discount_target_for_different_product' => 'Dieser Rabatt ist für ein anderes Produkt vorgesehen',
    'err.discount_target_for_different_region' => 'Dieser Rabatt ist für eine andere Region vorgesehen',
    'err.discount_target_is_for_specific_region' => 'Dieser Rabatt ist für eine bestimmte Region',
    'err.discount_target_no_longer_exists' => 'Dieses Rabattziel existiert nicht mehr',
    'err.discount_target_required' => 'Rabattziel ist erforderlich',
    'err.discount_type_required' => 'Rabattart ist erforderlich',
    'err.end_date_invalid_format' => 'Enddatum ist ungültig.',
    'err.flat_discount_must_be_less_than_order_subtotal' => 'Der Pauschalrabatt muss geringer sein als der Bestellsumme',
    'err.flat_discount_must_be_less_than_product_price' => 'Der Pauschalrabatt muss geringer sein als der Produktpreis',
    'err.flat_discount_value_cannot_be_empty' => 'Der Pauschalrabattwert darf nicht leer sein',
    'err.for_change_fulfillment_status_use_button' => 'Du kannst den Versandstatus von hier aus nicht ändern, da diese Bestellung mit einem Versanddienstleister erstellt wurde, der keine manuelle Änderung des Erfüllungsstatus erlaubt',
    'err.invalid_address' => 'Adresse ist ungültig.',
    'err.invalid_address_type' => 'Ungültiger Adresstyp',
    'err.invalid_amount' => 'Betrag ist ungültig.',
    'err.invalid_barcode' => 'Barcode ist ungültig.',
    'err.invalid_category' => 'Produktkategorie ist ungültig.',
    'err.invalid_customer' => 'Kunde ist ungültig.',
    'err.invalid_customer_group' => 'Kundengruppe ist ungültig.',
    'err.invalid_customers' => 'Kunden sind ungültig.',
    'err.invalid_daterange_format' => 'Datumsbereichsformat ist ungültig.',
    'err.invalid_discount_target' => 'Rabattziel ist ungültig.',
    'err.invalid_discount_type' => 'Rabattart ist ungültig.',
    'err.invalid_filter_compare_operator' => 'Filtervergleichsoperator ist ungültig.',
    'err.invalid_filter_value' => 'Filterwert ist ungültig.',
    'err.invalid_group' => 'Kundengruppe ist ungültig.',
    'err.invalid_invoice_number' => 'Ungültige Rechnungsnummer',
    'err.invalid_note_administrator' => 'Notiz an den Administrator ist ungültig.',
    'err.invalid_order' => 'Bestellung ist ungültig.',
    'err.invalid_order_id' => 'Bestellnummer ist ungültig.',
    'err.invalid_product_id' => 'Produkt-ID ist ungültig.',
    'err.invalid_provider' => 'Zahlungsanbieter ist ungültig.',
    'err.invalid_quantity' => 'Menge ist ungültig.',
    'err.invalid_region' => 'Region ist ungültig.',
    'err.invalid_request' => 'Anfrage ist ungültig.',
    'err.invalid_restore_link' => 'Wiederherstellungslink ist ungültig.',
    'err.invalid_shipping_provider' => 'Versandmethode ist ungültig.',
    'err.invalid_sku' => 'Artikelnummer (SKU) ist ungültig.',
    'err.invalid_status' => 'Bestellstatus ist ungültig.',
    'err.invalid_type' => 'Ungültiger Typ.',
    'err.invalid_value' => 'Wert der Variante ist ungültig.',
    'err.invalid_variant_id' => 'Variant-ID ist ungültig.',
    'err.invalid_vendor' => 'Marke ist ungültig.',
    'err.invoice_number_numeric' => 'Rechnungsnummer muss eine Zahl sein',
    'err.invoice_number_required' => 'Du hast keine Rechnungsnummer eingegeben',
    'err.invoice_number_unique' => 'Eine Rechnung mit der eingegebenen Nummer existiert bereits. Bitte gib eine eindeutige Nummer ein',
    'err.no_addresses_available' => 'Es sind keine Adressen verfügbar.',
    'err.no_discounts' => 'Es sind keine Rabatte verfügbar.',
    'err.no_products_for_fulfillment' => 'Es sind keine Produkte zur Erfüllung vorhanden.',
    'err.no_such_customer' => 'Es gibt keinen solchen Kunden.',
    'err.no_such_group' => 'Es gibt keine solche Kundengruppe.',
    'err.no_such_order' => 'Es gibt keine solche Bestellung.',
    'err.not_enough_quantity_for_%1$s' => 'Es gibt nicht genug Menge für %1$s.',
    'err.note_administrator_max_chars_%1$s' => 'Die maximal erlaubte Zeichenanzahl für die Notiz an den Administrator beträgt %1$s.',
    'err.nothing_changed' => 'Es wurde nichts geändert.',
    'err.only_completed_orders_can_be_archived' => 'Nur abgeschlossene Bestellungen können archiviert werden.',
    'err.only_paid_and_fulfilled_orders_can_be_completed' => 'Nur bezahlte und/oder erfüllte Bestellungen können als abgeschlossen markiert werden',
    'err.only_pending_orders_can_be_abandoned' => 'Nur offene Bestellungen können aufgegeben werden.',
    'err.only_pending_orders_can_be_canceled' => 'Nur offene Bestellungen können storniert werden.',
    'err.order_already_completed' => 'Diese Bestellung wurde bereits abgeschlossen.',
    'err.order_already_pending' => 'Diese Bestellung ist bereits ausstehend.',
    'err.order_complete_not_fulfilled' => 'Die Bestellung muss erfüllt werden, bevor sie als abgeschlossen markiert wird.',
    'err.order_discount_no_longer_exists' => 'Der Rabatt für die Bestellung existiert nicht mehr.',
    'err.order_does_not_exist' => 'Diese Bestellung existiert nicht.',
    'err.order_has_discount' => 'Diese Bestellung hat einen Rabatt.',
    'err.order_has_no_addresses' => 'Diese Bestellung hat keine Adresse.',
    'err.order_has_no_payments' => 'Diese Bestellung hat keine Zahlungen.',
    'err.order_must_have_at_least_one_product' => 'Bestellungen sollten mindestens ein Produkt enthalten.',
    'err.order_no_longer_exists' => 'Die Bestellung existiert nicht mehr.',
    'err.order_payment_does_not_exist' => 'Diese Zahlungsbestellung existiert nicht.',
    'err.order_payment_incomplete' => 'Zahlung fehlgeschlagen',
    'err.order_product_no_longer_exists' => 'Dieses Bestellprodukt existiert nicht mehr.',
    'err.original_product_variant_does_not_have_price_please_enter_override_price' => 'Die ursprüngliche Produktvariante hat keinen Preis. Bitte gib einen Preis ein',
    'err.payment_amount_differs_from_order_total' => 'Der Zahlungsbetrag unterscheidet sich von der Bestellsumme.',
    'err.payment_provider_already_set' => 'Ein Zahlungsanbieter wurde bereits festgelegt.',
    'err.percent_discount_value_cannot_be_empty' => 'Der Prozentwert des Rabatts darf nicht leer sein.',
    'err.product_add_not_enough_quantity' => 'Nicht genug Menge verfügbar.',
    'err.product_already_has_discount' => 'Dieses Produkt hat bereits einen Rabatt.',
    'err.product_cant_have_shipping_discount' => 'Produkte können keinen Versandrabatt haben',
    'err.product_no_longer_exists' => 'Dieses Produkt existiert nicht mehr',
    'err.product_no_longer_has_discount' => 'Dieses Produkt hat keinen Rabatt mehr',
    'err.product_variant_no_longer_exists' => 'Produktvariante existiert nicht mehr',
    'err.provider_config_mismatch' => 'Die Konfiguration des Anbieters stimmt nicht überein',
    'err.quantity_required' => 'Menge ist erforderlich',
    'err.shipping_products_required' => 'Bitte wähle mindestens ein Produkt zur Erfüllung aus',
    'err.shipping_provider_does_not_have_insurance' => 'Die Versandmethode hat keine Versicherung',
    'err.shipping_provider_no_matching_rate' => 'Die Versandmethode hat keinen passenden Tarif',
    'err.shipping_provider_no_matching_rate_price' => 'Der Gesamtwert deiner Bestellung (:total) liegt nicht im Preisbereich der Versandmethode. Der niedrigste Wert ist :min und der höchste ist :max',
    'err.shipping_provider_no_matching_rate_price_and_weight' => 'Der Gesamtwert und das Gesamtgewicht deiner Bestellung liegen nicht im Preis- und Gewichtbereich der Versandmethode',
    'err.shipping_provider_no_matching_rate_weight' => 'Das Gesamtgewicht deiner Bestellung (:total) liegt nicht im Gewichtbereich der Versandmethode. Das niedrigste Gewicht ist :min und das höchste ist :max',
    'err.shipping_tracking_url_max_chars_%1$s' => 'Die maximal erlaubte Zeichenanzahl für die Versandverfolgungs-URL beträgt %1$s',
    'err.some_orders_archived' => 'Einige der Bestellungen sind archiviert',
    'err.some_products_already_shipped' => 'Einige der Produkte sind bereits erfüllt',
    'err.some_products_no_longer_in_order' => 'Einige der Produkte sind nicht mehr in der Bestellung',
    'err.start_date_later_than_end_date' => 'Das Startdatum kann nicht früher sein als das Enddatum.',
    'err.target_cannot_be_related_to_product' => 'Das Ziel kann nicht mit dem Produkt verbunden sein',
    'err.target_not_related_to_product' => 'Das Ziel ist nicht mit dem Produkt verbunden',
    'err.variant_required' => 'Variante ist erforderlich',
    'err.waybill.out_of_stock_products' => 'Diese Bestellung kann nicht versendet werden, da sie nicht vorrätige Produkt(e) enthält.',
    'error.authorize_amount' => 'Der Bestellbetrag beträgt :gesamt und darf die autorisierte Zahlung :Betrag nicht überschreiten.',
    'error.billing_address' => 'Du hast keine Rechnungsadresse eingegeben',
    'error.generate_invoice' => 'Es wurde keine Rechnung im ausgewählten externen System erstellt',
    'existing_discount' => 'Dieser Rabatt existiert bereits',
    'fast_order' => 'Schnellbestellung',
    'filer.is_admin' => 'Von Admin erstellt',
    'filer.is_draft' => 'Entwurfsbestellungen',
    'filer.supplier' => 'Produktlieferant',
    'filter.barcode_%1$s' => 'Barcode %1$s',
    'filter.category_%1$s' => 'Kategorie %1$s',
    'filter.country_%1$s' => 'Land %1$s',
    'filter.credit_note' => 'Ausgestellte Gutschrift',
    'filter.customer_%1$s' => 'Kunde %1$s',
    'filter.customer_group_%1$s' => 'Kundengruppe %1$s',
    'filter.customers_in_%1$s' => 'Kunden in %1$s',
    'filter.date_added' => 'Hinzugefügt am',
    'filter.date_archived' => 'Archiviert am',
    'filter.date_archived_%1$s_%2$s' => 'Archiviert am %2$s',
    'filter.discount_code' => 'Rabattcode: ',
    'filter.discount_code.option' => 'Rabattcode',
    'filter.discount_type_' => '',
    'filter.discount_type_%1$s' => 'Rabattart %1$s',
    'filter.discount_type_fixed' => '',
    'filter.discount_type_flat' => 'Nach Pauschalbetrag filtern',
    'filter.discount_type_percent' => 'Nach Prozent filtern',
    'filter.discount_type_shipping' => 'Nach kostenlosem Versand filtern',
    'filter.fast_order' => 'Schnellbestellung',
    'filter.has_discount' => 'Hat Rabatt',
    'filter.invoice_number_%1$d' => 'Rechnungsnummer %1$d',
    'filter.made.through.messenger-bot' => 'Messenger-Bot',
    'filter.not_fast_order' => 'Keine Schnellbestellung',
    'filter.only_is_admin_message' => 'Es werden nur Bestellungen angezeigt, die über das Admin-Dashboard erstellt wurden',
    'filter.only_is_draft_message' => 'Es werden nur Entwurfsbestellungen angezeigt',
    'filter.order_added_%1$s_%2$s' => 'Bestellung hinzugefügt %1$s %2$s',
    'filter.order_added_%1$s_%2$s_4' => 'Bestellung hinzugefügt nach %1$s und vor %2$s',
    'filter.order_archived_%1$s' => 'Bestellung archiviert %1$s',
    'filter.order_fulfillment_status_%1$s' => 'Erfüllungsstatus der Bestellung %1$s',
    'filter.order_is_%1$s' => 'Bestellung ist %1$s',
    'filter.order_made_through_%1$s' => 'Bestellung wurde über %1$s erstellt',
    'filter.order_payment_status_%1$s_%2$s' => 'Zahlungsstatus %1$s %2$s',
    'filter.order_product_date_range_%1$s_%2$s' => 'Bestellung hinzugefügt von %1$s bis %2$s',
    'filter.order_product_id_range_%1$s_%2$s' => 'Bestell-ID von %1$s bis %2$s',
    'filter.order_product_supplier_%1$s' => 'Lieferant: %1$s',
    'filter.order_recovered_%1$s' => 'Bestellung wurde über %1$s wiederhergestellt',
    'filter.order_saleschannel_%1$s' => 'Vertriebskanal %1$s',
    'filter.order_status_%1$s_%2$s' => 'Bestellstatus %1$s %2$s',
    'filter.order_total_price_%1$s_%2$s' => 'Gesamtpreis der Bestellung %1$s %2$s',
    'filter.order_updated_%1$s_%2$s' => 'Bestellung aktualisiert %1$s %2$s',
    'filter.payment_amount' => 'Betrag',
    'filter.payment_amount_%1$s_%2$s' => 'Zahlungsbetrag %1$s %2$s',
    'filter.payment_date_added' => 'Datum hinzugefügt',
    'filter.payment_provider_%1$s_%2$s' => 'Zahlungsanbieter %1$s %2$s',
    'filter.payment_status' => 'Zahlungsstatus',
    'filter.payment_status_%1$s_%2$s' => 'Zahlungsstatus %1$s %2$s',
    'filter.product_fulfilled_%1$s' => 'Produkt erfüllt %1$s',
    'filter.product_price_%1$s_%2$s' => 'Produktpreis %1$s %2$s',
    'filter.product_quantity_%1$s_%2$s' => 'Produktmenge %1$s %2$s',
    'filter.product_sale_%1$s' => 'Produktverkauf %1$s',
    'filter.recovered_from_email' => 'von E-Mail',
    'filter.recovered_from_messenger' => 'von Messenger',
    'filter.region_%1$s' => 'Region %1$s',
    'filter.shipping_provider_%1$s_%2$s' => 'Versandanbieter %1$s %2$s',
    'filter.sku_%1$s' => 'SKU %1$s',
    'filter.status' => 'Status',
    'filter.status_' => '',
    'filter.status_fulfillment' => 'Erfüllungsstatus',
    'filter.supplier' => 'Produktlieferant:',
    'filter.vendor_%1$s' => 'Marke %1$s',
    'fulfillment_date' => 'Erfüllungsdatum',
    'fulfillment_tracking_url' => 'Erfüllungsverfolgungs-URL',
    'gmap_address' => 'Google-Map-Adresse',
    'go_to_manual' => 'Besuche unser Online-Handbuch.',
    'guest_first_name' => 'Vorname des Gastes.',
    'guest_last_name' => 'Nachname des Gastes.',
    'guest_no_email' => 'Keine E-Mail verfügbar.',
    'has_discount' => 'Hat Rabatt',
    'has_following_fields_edited_to' => 'Hat folgende Felder bearbeitet zu',
    'header.abandoned_#' => 'Verlassene #',
    'header.abandoned_orders' => 'Verlassene Bestellungen',
    'header.add_order' => 'Neue Bestellung erstellen',
    'header.add_order_discount' => 'Bestellrabatt hinzufügen',
    'header.add_order_payment' => 'Zahlung zur Bestellung hinzufügen',
    'header.add_product' => 'Produkt hinzufügen',
    'header.add_product_discount' => 'Produkt-Rabatt hinzufügen',
    'header.address_add' => 'Vorhandene Adresse auswählen',
    'header.address_change' => 'Adressänderung',
    'header.address_edit' => 'Adresse bearbeiten',
    'header.chargeback_order_payment_%1$s' => 'Rückbuchung der Bestellzahlung %1$s',
    'header.complete_order_payment_%1$s' => 'Bestellzahlung abschließen %1$s',
    'header.customer_edit' => 'Kunden bearbeiten',
    'header.edit_product' => 'Produkt bearbeiten',
    'header.fulfill_products_manual' => 'Produkte erfüllen',
    'header.order_history' => 'Bestellverlauf',
    'header.order_payments_list' => 'Liste der Bestellzahlungen',
    'header.orders' => 'Bestellungen',
    'header.product_fulfillment_info' => 'Produkt-Erfüllungsinfo',
    'header.void_order_payment_%1$s' => 'Bestellzahlung stornieren %1$s',
    'heed_help_abandoned' => 'Brauche Hilfe bei verlassenen Bestellungen?',
    'help.choose_discount_target' => 'Wähle das Rabattziel aus, das du anstrebst',
    'help.credit_template.css_classes.description' => 'Dies sind CSS-Klassen, die dir helfen, die Stile spezifischer Elemente zu ändern. Zum Beispiel: du kannst die Klasse product-name verwenden, um die Anzeige des Produktnamens zu ändern.',
    'help.discount_choose' => 'Wähle, ob du einen bestehenden Rabatt hinzufügen oder manuell einen hinzufügen möchtest.',
    'help.google_map_address' => 'Google Maps Adresse.',
    'help.order_payment_amount' => 'Zahlungsbetrag.',
    'help.order_payment_chargeback_reason' => 'Gib einen Grund für die Rückbuchung der Zahlung an',
    'help.order_payment_email_note' => 'Eine kurze Nachricht, die der User in der Zahlungs-E-Mail erhält.',
    'help.order_payment_provider_reference_id' => 'Anbieter Referenz-ID. (Dieses Feld ist optional)',
    'help.order_payment_refund' => 'Gib einen Grund für die Rückerstattung der Zahlung an.',
    'help.ordered_by_facebook' => 'Bestellt über Facebook',
    'help.print_template.css_classes.description' => 'Dies sind CSS-Klassen, die dir helfen, die Stile spezifischer Elemente zu ändern. Zum Beispiel: du kannst die Klasse product-name verwenden, um die Anzeige des Produktnamens zu ändern',
    'help.product_price' => 'Produktpreis',
    'help.product_quantity' => 'Produktmenge.',
    'help.update_customer_address_info' => 'Kundenadresse aktualisieren.',
    'help.user_map_address' => 'Das ist die Google Maps Adresse.',
    'history.error.details' => 'Fehler:',
    'history.error.text' => 'Beim Senden der Bestellung ist ein Fehler aufgetreten an',
    'history.label.' => '',
    'history.label.glovo_invlid_credentials' => 'Ungültige Glovo-Verbindungsdaten',
    'history.label.glovo_store_is_closed' => 'Die Bestellung wurde nicht versendet - der Standort ist außerhalb der Geschäftszeiten',
    'history.label.lock_order' => 'Der Versandpreis der Bestellung ist gesperrt',
    'history.label.modification_removed_from_order_product' => 'Rabatt für ":app" bei einem Produkt entfernt',
    'history.label.order_receipt_sent' => 'Quittung gesendet',
    'history.label.pick_and_pack' => 'Ändern über die Pick and Pack App',
    'history.label.send_erp_error' => 'Fehler beim Senden der Bestellung an ERP',
    'history.label.send_erp_success' => 'Bestellung erfolgreich an ERP gesendet',
    'history.label.unlock_order' => 'Versandpreis der Bestellung entsperrt',
    'history.order_receipt_number' => 'Quittungsnummer #',
    'history.success.details' => 'Bestellnummer in',
    'history.success.text' => 'Bestellung erfolgreich gesendet an',
    'history_no_address' => 'Hat keine Adresse',
    'history_no_longer_exists' => 'Verlauf existiert nicht mehr',
    'history_order_placed' => 'Bestellung aufgegeben',
    'history_order_was_recovered_through_%1$s' => 'Die Bestellung wurde nach gesendetem Hinweis %1$s wiederhergestellt',
    'info.abandoned_restore_last_sent_date' => 'Letzte E-Mail gesendet am',
    'info.edit_shipping' => 'Versandmethode bearbeiten',
    'info.no_products' => 'Keine Produkte',
    'info.period' => '\\"Berichtszeitraum: \\"',
    'info.period.remaining' => '\\"Anzahl der verbleibenden Synchronisierungen für den Zeitraum: \\"',
    'info.period.used' => '\\"Anzahl der Synchronisierungen, die für den Zeitraum durchgeführt wurden: \\"',
    'info.product_add.no_variants' => 'Nicht festgelegt',
    'info.shipping_notification' => 'E-Mail-Benachrichtigung an den Kunden senden',
    'info.text' => '\\"Das System überprüft bis zu <b>15 aufeinanderfolgende Tage</b> auf bezahlte Nachnahme für jede Bestellung, die einen gültigen Frachtbrief hat und <b>mehr als 3 Tage vor dem Überprüfungsdatum erstellt wurde</b>. <br /><b>Beispiel:</b> Wenn du heute einen Frachtbrief erstellt hast, beginnt das System nach 3 Tagen mit der Überprüfung des Zahlungsstatus.\\"',
    'invalid request' => 'Ungültige Anfrage',
    'invoice.type.original' => 'Original',
    'invoice_create_date' => 'Rechnungsdatum erstellen',
    'invoice_line' => 'Rechnungsstellung',
    'invoice_number' => 'Rechnungsnummer',
    'ip_address_not_verified' => 'IP-Adresse ist nicht verifiziert',
    'ip_address_verified' => 'IP-Adresse ist verifiziert',
    'ip_country' => 'IP-Land',
    'ip_info' => 'Betrugsprävention',
    'is_opened_from_other' => 'Bestellung wurde von :user geöffnet',
    'klear.confirm.order' => 'ZAHLUNG BESTÄTIGEN IN',
    'label.credit_template' => 'Vorlage zum Drucken einer Gutschrift',
    'label.credit_template.css_classes' => 'CSS-Klassen für die Standard-Elemente',
    'label.customer_address' => 'Kundenadresse',
    'label.discount_amount' => 'Rabattbetrag',
    'label.discount_choose' => 'Rabatt wählen',
    'label.discount_type' => 'Rabattart',
    'label.document.number' => 'Dokumentennummer eingeben',
    'label.invoice.number' => 'Rechnungsnummer eingeben',
    'label.invoice_template' => 'Vorlage für das Drucken der Rechnungen',
    'label.manual_discount' => 'Rabatt',
    'label.no_weight' => 'Kein Gewicht',
    'label.note_from_merchant' => 'Händlernotiz',
    'label.optional_tracking_url' => 'Optionale Tracking-URL',
    'label.order_payment_amount' => 'Betrag',
    'label.order_payment_chargeback_reason' => 'Rückbuchungsgrund',
    'label.order_payment_email_note' => 'Zahlungsnotiz',
    'label.order_payment_provider_reference_id' => 'Anbieter Referenz-ID',
    'label.order_payment_refund' => 'Rückerstattung',
    'label.orders' => 'Bestellungen',
    'label.print_template' => 'Vorlage für das Drucken des Lieferscheins',
    'label.print_template.css_classes' => 'CSS-Klassen für die Standard-Elemente',
    'label.print_template.sorter' => 'Produktsortierung',
    'label.print_template.sorter.help' => 'Um die Produkte in der Lagerliste zu sortieren, musst du den gewünschten Parameter zur Variablen $products hinzufügen. Beispiel $products:preis-absteigend',
    'label.product_parameter' => 'Parameter',
    'label.product_price' => 'Preis',
    'label.product_price_override' => 'Preisüberschreibung',
    'label.product_quantity' => 'Menge',
    'label.shipping_date_delivery' => 'Lieferdatum',
    'label.shipping_date_expedition' => 'Erfüllung',
    'label.tracking_number' => 'Tracking #',
    'label.update_customer_address' => 'Kundenadresse',
    'label.update_customer_address_info' => 'Kundenadressinfo',
    'label.update_customer_info' => 'Kundeninfo',
    'label.user_map_address' => 'User-Kartenadresse',
    'made.through' => 'Durchgeführt durch',
    'manual_discount' => 'Manueller Rabatt',
    'manual_discount_settings' => 'Einstellungen für manuellen Rabatt',
    'missing_ip_country' => 'nicht zutreffend',
    'mokka.confirm.order' => 'BESTELLUNG BESTÄTIGEN IN',
    'new.order.request_address' => 'Du hast keine Lieferadresse ausgewählt',
    'new.order.request_customer' => 'Du hast keinen Kunden ausgewählt',
    'new.order.step2' => 'Nächster Schritt',
    'new.order.success_create' => 'Du hast die Bestellung erfolgreich hinzugefügt',
    'new.success.saved' => 'Du hast deine Bestellung erfolgreich gespeichert',
    'no_address' => 'Keine Adresse',
    'no_comment' => 'Es gibt keinen Kommentar zu dieser Bestellung',
    'not_enought_can_sell' => 'Nicht verfügbar',
    'not_enought_cannot_sell' => 'Nicht verfügbar',
    'notify.abandoned_no_records_help' => 'Hast du Probleme mit verlassenen Bestellungen? Folge dem Link unten.',
    'notify.abandoned_no_records_help_link' => 'Hilfe bei verlassenen Bestellungen',
    'notify.abandoned_no_records_info' => 'Die verlassenen Bestellungen deines Shops werden hier angezeigt',
    'notify.abandoned_no_records_yet' => 'Es gibt noch keine verlassenen Bestellungen',
    'notify.no_records_help' => 'Hast du Probleme mit Bestellungen? Folge dem Link unten.',
    'notify.no_records_help_cod' => '\\"Du hast keine synchronisierten Bestellungen, um die auferlegten Zahlungen zu überprüfen.\\"',
    'notify.no_records_help_link' => 'Hilfe bei Bestellungen',
    'notify.no_records_info' => 'Die Bestellungen deines Shops werden hier angezeigt',
    'notify.no_records_yet' => 'Du hast noch keine Bestellungen erhalten',
    'notify.send.new_order' => 'Du hast den Kunden erfolgreich über die Bestellung informiert',
    'notify.shipping_address_is_required' => 'Du musst die Versandadresse ausfüllen, um diese Bestellung für den Versand vorzubereiten',
    'notify.shipping_address_is_required.is_fast_order' => 'Die Bestellung wurde über die',
    'notify_customer' => 'Kunden benachrichtigen',
    'notify_customer_help' => 'Wenn diese Option aktiv ist, wird der Kunde über Änderungen an der Bestellung informiert',
    'order.error.document_number' => 'Du hast keine Dokumentennummer eingegeben',
    'order.error.manual' => 'Die Bestellung konnte nicht bestätigt werden',
    'order.error.payment' => 'Du hast keine Zahlungsmethode für die Bestellung ausgewählt',
    'order.error.products' => 'Du hast keine Produkte zu deiner Bestellung hinzugefügt',
    'order.error.shipping' => 'Du hast keine Liefermethode für die Bestellung ausgewählt',
    'order.success.manual_confirm' => 'Bestellung erfolgreich bestätigt',
    'order_#' => 'Bestellung #',
    'order_archive_are_you_sure' => 'Dies wird deine Bestellung archivieren. Bitte bestätige.',
    'order_banned_ip' => 'Automatisches Stornieren von Bestellungen, die von bestimmten IP-Adressen gemacht wurden',
    'order_complete_are_you_sure' => 'Bist du sicher, dass du diese Bestellung abschließen möchtest? Wenn du sie als abgeschlossen markierst, kannst du ihren Status nie wieder ändern.',
    'order_date_interval' => 'Bestelldatum-Intervall',
    'order_has_no_products' => 'Bestellung hat keine Produkte',
    'order_id_interval' => 'Bestell-ID-Intervall',
    'order_placed_by' => 'Aufgegeben von',
    'order_products_fulfilled' => 'Produkte erfüllt',
    'order_shipping_insurance' => 'Versandversicherung',
    'order_total' => 'Gesamt',
    'order_unarchive_are_you_sure' => 'Möchtest du das Archiv aufheben?',
    'payment.authorized' => 'Autorisierte Zahlung (:Betrag)',
    'payment.confirm.authorize' => 'Bist du sicher, dass du diese Zahlung autorisieren möchtest?',
    'payment.confirm.cancel' => 'Bist du sicher, dass du diese Zahlung stornieren möchtest?',
    'payment.confirm.cancel_authorization' => 'Bist du sicher, dass du die Autorisierung stornieren möchtest?',
    'payment.confirm.complete' => 'Bist du sicher, dass du diese Zahlung als abgeschlossen markieren möchtest?',
    'payment.confirm.lease' => 'Die Referenz wird gesendet',
    'payment.confirm.refund' => 'Bist du sicher, dass du diese Zahlung zurückerstatten möchtest?',
    'payment.confirm.sync' => 'Bist du sicher, dass du diese Zahlung synchronisieren möchtest?',
    'payment.confirm.void' => 'Bist du sicher, dass du diese Zahlung stornieren möchtest?',
    'payment.status.not_payment' => 'Keine Zahlung erhalten',
    'payment_provider' => 'Zahlungsmethode',
    'payment_provider_reference_id' => 'Anbieter Referenz-ID',
    'payment_status' => 'Zahlungsstatus',
    'percent' => 'Prozent',
    'ph.type_here' => 'Hier eingeben',
    'plan_does_not_support_abandoned' => 'Dein aktueller Plan unterstützt keine verlassenen Bestellungen. Überlege, deinen Plan zu upgraden.',
    'product.add_product' => 'Produkt hinzufügen',
    'product.container.help_text' => '<small>Nachdem du einen Kunden und eine Lieferadresse ausgewählt hast, kannst du zum zweiten Schritt übergehen, um Produkte / Zahlungsmethode / Liefermethode hinzuzufügen</small>',
    'product_count' => 'Anzahl',
    'provider' => 'Versandmethode',
    'ready_for_pack' => 'Das Produkt ist gesammelt und bereit für die Verpackung',
    'recovered' => 'Wiederhergestellt',
    'referer' => 'Referrer',
    'region' => 'Region',
    'removed_from_order' => 'Von der Bestellung entfernt',
    'removed_from_order_product' => 'Produkt von der Bestellung entfernt',
    'saleschannel' => 'Vertriebskanal',
    'save_and_create_order_info' => 'Das System speichert die Änderungen und erstellt die Bestellung',
    'save_order' => 'Bestellung speichern',
    'select.address.help' => 'Sobald du einen Kunden auswählst, kannst du eine Lieferadresse auswählen / hinzufügen',
    'select.customer' => 'Kunden auswählen',
    'select_product_parameter' => 'Produktparameter auswählen',
    'send_abonded_order_error' => 'Dieses Feature ist verfügbar für Plan:',
    'ship_to.address' => 'Lieferung an die Adresse',
    'ship_to.locker' => 'An Schließfach versenden',
    'ship_to.marketplace' => 'Abholung im Laden',
    'ship_to.office' => 'Lieferung an die Filiale',
    'ship_to.title.delivery_method' => 'Liefermethode',
    'shipping' => 'Versand',
    'shipping_address' => 'Versandadresse',
    'shipping_change' => 'Änderung der Versandmethode',
    'shipping_discount' => 'Versandrabatt',
    'shipping_insurance:' => 'Versandversicherung:',
    'source_info' => 'Quelleninformation',
    'status' => 'Bestellstatus',
    'status.chage_status' => 'Status ändern',
    'status.error.invalid' => 'Ungültiger Status',
    'status.error.invalid_type' => 'Ungültiger Typ',
    'status.error.no_name' => 'Es gibt keinen Namen',
    'status.error.no_status' => 'Kein Status',
    'status.error.order_not_found' => 'Bestellung nicht gefunden',
    'status_authorized' => 'Autorisierte',
    'status_draft' => 'Entwurf',
    'status_fulfilled' => '',
    'status_fulfillment' => 'Erfüllung',
    'status_paid' => '',
    'status_pending' => '',
    'status_{$status}' => '',
    'statuses.title' => 'Status',
    'subtotal' => 'Zwischensumme',
    'subtotal_products' => 'Zwischensumme',
    'succ.abandoned_%1$s_emails_sent' => '%1$s E-Mails wurden erfolgreich gesendet',
    'succ.abandoned_email_sent_to_client' => 'Die E-Mail wurde erfolgreich an den Kunden gesendet',
    'succ.address_added_success' => '',
    'succ.address_change_success' => 'Adresse erfolgreich geändert',
    'succ.address_edit_success' => 'Adresse erfolgreich bearbeitet',
    'succ.customer_edit_success' => 'Kunde erfolgreich bearbeitet',
    'succ.order_discount_added' => 'Rabatt erfolgreich hinzugefügt',
    'succ.order_discount_removed' => 'Rabatt erfolgreich entfernt',
    'succ.order_notes_edit_success' => 'Notizen erfolgreich bearbeitet',
    'succ.order_payment_cancel_success' => 'Zahlung erfolgreich storniert',
    'succ.order_payment_chargeback_success' => 'Zahlung Rückbuchung erfolgreich',
    'succ.order_payment_complete_success' => 'Zahlung erfolgreich abgeschlossen',
    'succ.order_payment_email_sent_to_client' => 'E-Mail erfolgreich an den Kunden gesendet',
    'succ.order_payment_refund_success' => 'Zahlung erfolgreich erstattet',
    'succ.order_payment_sync_success' => 'Zahlung erfolgreich synchronisiert',
    'succ.order_payment_void_success' => 'Zahlung erfolgreich ungültig gemacht',
    'succ.order_product_add_success' => 'Produkt erfolgreich hinzugefügt',
    'succ.order_product_add_success_without_discount' => 'Das Produkt wurde erfolgreich hinzugefügt, aber kein Rabatt wurde angewendet, da der Preis niedriger als der Produktpreis ist',
    'succ.order_product_delete_success' => 'Produkt erfolgreich gelöscht',
    'succ.order_product_discount_added' => 'Produkt-Rabatt erfolgreich hinzugefügt',
    'succ.order_product_discount_removed' => 'Produkt-Rabatt erfolgreich entfernt',
    'succ.order_product_edit_success' => 'Produkt erfolgreich bearbeitet',
    'succ.order_products_fulfillment_success' => 'Produkte erfolgreich erfüllt',
    'succ.shipping_provider_changed' => 'Versanddienstleister erfolgreich geändert',
    'success.generate_invoice' => 'Rechnung erfolgreich erstellt',
    'table.action' => 'Status',
    'table.courier' => 'Kurier',
    'table.datetime' => 'Datum und Uhrzeit',
    'table.orderid' => 'Bestellung',
    'tax.vat_included' => 'Inklusive',
    'tax_amount' => 'Steuer',
    'th.abandoned_#' => 'Verlassene #',
    'th.actions' => 'Aktionen',
    'th.address' => 'Adresse',
    'th.amount' => 'Betrag',
    'th.collected' => 'gesammelt',
    'th.date' => 'Datum',
    'th.date_updated' => 'Datum zuletzt aktualisiert',
    'th.fulfillable_items' => 'Erfüllbare Artikel',
    'th.fulfillment' => 'Erfüllung',
    'th.order' => 'Bestellung',
    'th.order_#' => 'Bestellung #',
    'th.ordered' => 'bestellt',
    'th.ordered_product.order_ids' => 'Bestellungen, die das Produkt betreffen',
    'th.payment_amount' => 'Betrag',
    'th.payment_last_modified' => 'Zuletzt geändert',
    'th.payment_provider' => 'Anbieter',
    'th.payment_status' => 'Zahlungsstatus',
    'th.product_name' => 'Name',
    'th.product_total' => 'Gesamt',
    'th.products' => 'Produkte',
    'th.quantity' => 'Menge',
    'th.receiving' => 'Empfang',
    'th.single_product_price' => 'Einzelpreis',
    'th.status' => 'Bestellstatus',
    'th.total' => 'Gesamt',
    'title' => 'Bestellungen',
    'title.cart_time_life' => 'Lebensdauer des Warenkorbs vor der Bestellung',
    'title.cod' => 'Nachnahme',
    'to_use_abandoned_upgrade_advanced_or_professional' => 'Wenn du verlassene Bestellungen verfolgen möchtest, upgrade deinen Plan auf Advanced oder Professional.',
    'total' => 'Gesamt',
    'total_price' => 'Gesamt',
    'total_price_after_discount' => 'Gesamtpreis des Produkts nach Rabatt',
    'total_price_before_discount' => 'Gesamtpreis des Produkts vor Rabatt',
    'tracking' => 'Verfolgung',
    'tracking_return' => 'Rücksendeschein',
    'updated' => 'Bestellung wurde aktualisiert',
    'upgrade_plan' => 'Plan upgraden',
    'usn' => 'Eindeutige Verkaufsnummer (USN)',
    'utm_campaign' => 'UTM-Kampagne',
    'utm_medium' => 'UTM-Medium',
    'utm_source' => 'UTM-Quelle',
    'validation.address_id.required' => 'Du hast keine Adresse ausgewählt',
    'validation.delivery_to.required' => 'Du hast keine Liefermethode ausgewählt',
    'validation.office_id.required' => 'Du hast keine Filiale ausgewählt',
    'validation.store_id.required' => 'Du hast keinen Shop ausgewählt',
    'with_tracking_url' => 'Mit Verfolgungs-URL',
];
