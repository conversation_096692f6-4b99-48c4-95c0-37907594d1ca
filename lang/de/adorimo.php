<?php

return [
    'academy.header.install' => 'Installiere Adorimo Video Lessons',
    'academy.help.install' => '<PERSON><PERSON> dieser App hast Du Zugriff auf verschiedene Video Lektionen',
    'academy.info.description' => 'Mit Adorimo lernst Du:<br>
    - Wie Du Deine erste Anzeige auf Facebook erstellst<br>
    - Wie Du die richtige Zielgruppe auf Facebook erreichst<br>
    - Wie Du erfolgreich auf Facebook verkaufst',
    'academy.info.install' => '- Wie Du Deine erste Anzeige auf Facebook erstellst<br>
    - Wie Du die richtige Zielgruppe auf Facebook erreichst<br>
    - Wie Du erfolgreich auf Facebook verkaufst',
    'academy.info.title' => 'Adorimo Video Lessons',
    'academy.title.able_to' => 'Mit Adorimo lernst Du:',
    'action.back_to_admin_panel' => 'Zurück zum Admin Dashboard',
    'action.payment.add_provider' => 'Zahlungsmethode hinzufügen',
    'all_tasks_completed.head' => ', Du hast alle Schritte perfekt abgeschlossen. Wir wünschen Dir viel Glück beim Online-Verkauf!',
    'choose_option' => 'Wähle mindestens eine Option aus',
    'choose_service' => 'Wähle mindestens eine Dienstleistung aus',
    'header.install' => '',
    'help.btn_install' => 'Installieren',
    'help.hire_expert_btn' => 'Einen Experten einstellen',
    'help.hire_expert_btn_design' => 'Design',
    'help.hire_expert_text' => 'Wenn Du Probleme hast, kannst Du jederzeit unser Team von Fachleuten kontaktieren.',
    'help.hire_expert_text_design' => 'Du hast nicht die erforderlichen Designfähigkeiten, aber die Vision eines Online-Shops ist Dir wichtig.',
    'help.install' => '',
    'info.install' => '',
    'info.title' => '',
    'products.distributors' => 'Dein Online-Shop hat eine Integration und Synchronisierung mit den folgenden Distributoren:',
    'shipping.depending_on_price' => 'Je nach dem <strong>aktuellen Bestellwert</strong><br> Diese Lieferoption ist praktisch, wenn Du mit einem Kurier arbeiten möchtest, der nicht mit CloudCart integriert ist oder aus einem anderen Grund. Diese Liefermethode ist eine der am häufigsten verwendeten.
Durch die Preisbedingungen Von und Bis kannst Du die Lieferkosten kontrollieren.
    Zum Beispiel: Wenn die Bestellung zwischen 0 und 100 $ liegt - beträgt die Liefergebühr 5 $. Wenn es von 100 $ bis 1000 $ geht, beträgt die Liefergebühr 0 $.',
    'shipping.depending_on_price_and_weight' => 'Je nach dem <strong>Gesamtpreis und dem Gewicht aller Produkte</strong> in der Bestellung<br> Diese Liefermethode kombiniert sowohl Preis- als auch Gewichtparameter. Sie wird in geringeren Fällen verwendet, wenn Produkte mit niedrigem Volumen und hohem Preis sowie Produkte mit großem Volumen, aber niedrigem Preis verkauft werden. Zum Beispiel: Wenn die Gesamtbestellung von 0 BGN bis 100 BGN und das Gesamtgewicht von 0 bis 5 kg ist. - beträgt der Preis 5 leva.',
    'shipping.depending_on_weight' => 'Je nach dem <strong>Gesamtgewicht aller Produkte</strong> in der Bestellung <br> Die Liefermethode nach Gewicht der Produkte in der Bestellung ist praktisch, wenn Du voluminöse und schwere Produkte verkaufst. Dort kann der Preis erheblich variieren. Es ist sehr wichtig, daran zu denken, dass diese Liefermethode nur funktioniert, wenn Deine Produkte ein angegebenes Gewicht haben.',
    'shipping.pick_from_store' => 'Abholung des Produkts aus <strong>einem Ladengeschäft</strong><br> Diese Option ist nützlich, wenn Du mindestens einen physischen Standort hast. Das Modul bietet die Möglichkeit, die Waren in Deinen Geschäften abzuholen. Es ist wichtig zu wissen, dass Du diese Option anbieten musst, indem Du die Stores App verwendest.',
    'title.able_to' => '',
];
