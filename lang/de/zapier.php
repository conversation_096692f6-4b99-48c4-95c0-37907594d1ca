<?php

return [
    'button.connect' => '<PERSON><PERSON><PERSON> zu Zapier',
    'header.install' => 'Zapier installieren',
    'header.setting' => 'Zapier-Einstellungen',
    'help.api_key' => 'Bitte kopiere diesen API KEY und füge ihn in dein Zapier-Konto ein',
    'help.install' => 'Mit diesem leistungsstarken <a href="https://zapier.com/" target="_blank">Tool</a> kannst du deine Webanwendungen mit ein paar Klicks verbinden, sodass sie Daten austauschen können, du kannst Informationen zwischen deinen Apps weitergeben und Prozesse schneller erstellen, um mehr zu erledigen – kein Code erforderlich.',
    'help.setting' => 'Bitte befolge die folgenden Anweisungen, um Zapier richtig zu konfigurieren.',
    'help.site_id' => 'Bitte kopiere deine SITE ID und füge sie in dein Zapier-Konto ein',
    'info' => 'Um deinen Online-Shop mit Zapier zu verbinden, kopiere bitte deinen <b>API KEY</b> und <b>SITE ID</b>. Wenn du auf Schwierigkeiten stößt, schaue dir bitte dieses kurze Tutorial an oder kontaktiere uns.',
    'info.install' => '- Verbinde deinen Shop mit über 700 Anwendungen<br> - Füge automatisch Produkte hinzu, erstelle Kunden und vieles mehr<br> - Füge Produkte hinzu, aktualisiere Bestellungen oder füge Kunden in dein Spreadsheet, ActiveCampaign und andere ein',
    'info.title' => 'Zapier',
    'label.api_key' => 'API KEY',
    'label.site_id' => 'SITE ID',
    'title.able_to' => 'Mit Zapier kannst du:',
];
