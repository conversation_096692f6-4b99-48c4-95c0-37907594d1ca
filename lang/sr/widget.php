<?php

return [
    'add_to_cart.description' => 'With this widget you can visualize button "add to cart" for specific product',
    'add_to_cart.name' => 'Button "add to cart"',
    'addthis.description' => '',
    'addthis.name' => '',
    'banner.description' => 'Easily add images, banners or simple Javascript snippet to the page',
    'banner.label.products_per_row' => 'Images to show per row',
    'banner.name' => 'Show Images',
    'blog.blog.label.per_page' => 'Articles per page',
    'blog.blog.label.per_row' => 'Articles per row',
    'blog.blog.ph.per_page' => 'Enter per page',
    'blog.list.description' => '',
    'blog.list.name' => '',
    'blog.recentAtricles.label.count' => 'Recent articles count',
    'blog.recentAtricles.ph.count' => 'Enter recent articles count',
    'blog.recentComments.label.count' => 'Recent comments count',
    'blog.recentComments.ph.count' => 'Recent comments count',
    'bundle_products.description' => 'If you have created bundle products, this widget will visualize the ones you choose on your customized landing page.',
    'bundle_products.name' => 'Bundles Section',
    'button.description' => 'With this widget you can visualize simple button',
    'button.name' => 'Button',
    'carousel.description' => 'Carousel is a widget where you can show multiple images one after one',
    'carousel.name' => 'Carousel',
    'cart.err.choose_option' => 'Please choose an variation',
    'code.description' => 'Add a simple code to your page. You can add Javascript or HTML snippets to display it on your page.',
    'code.name' => 'Add Code Snippets',
    'contact.googleMap.action.add_marker' => 'Add your marker',
    'contact.googleMap.action.manual_add_pin' => 'Set your marker manually',
    'contact.googleMap.err.invalid_map_markers_format' => 'Map markets format is invalid.',
    'contact.googleMap.err.latitude_required_for_all_pins' => 'Latitude is required for all pins.',
    'contact.googleMap.err.longitude_required_for_all_pins' => 'Longitude is required for all pins.',
    'contact.googleMap.header.manual_map_marker_add' => 'Google map maker',
    'contact.googleMap.help.mapPreview' => 'Click on the desired place on the map and your marker will appear',
    'contact.googleMap.help.marker_caption' => 'Add a descriptive caption for that place',
    'contact.googleMap.label.backgroundColor' => 'Background color',
    'contact.googleMap.label.disableDefaultUI' => 'Disable default UI',
    'contact.googleMap.label.disableDoubleClickZoom' => 'Disable double-click zoom',
    'contact.googleMap.label.draggable' => 'Enable dragging of the map',
    'contact.googleMap.label.keyboardShortcuts' => 'Use keyboard short cuts',
    'contact.googleMap.label.mapKey' => 'Google API Key',
    'contact.googleMap.label.mapMaker' => 'Map Maker',
    'contact.googleMap.label.mapPreview' => 'Map Preview',
    'contact.googleMap.label.mapType' => 'Map Type',
    'contact.googleMap.label.mapTypeControl' => 'Display map type control',
    'contact.googleMap.label.marker_address' => 'Marker address',
    'contact.googleMap.label.marker_caption' => 'Marker caption',
    'contact.googleMap.label.noClear' => 'No clear',
    'contact.googleMap.label.overviewMapControl' => 'Overview map control',
    'contact.googleMap.label.panControl' => 'Pan control',
    'contact.googleMap.label.rotateControl' => 'Rotate control',
    'contact.googleMap.label.scaleControl' => 'Scale control',
    'contact.googleMap.label.scrollwheel' => 'Scroll wheel',
    'contact.googleMap.label.streetViewControl' => 'Street view control',
    'contact.googleMap.label.zoom' => 'Zoom map slider',
    'contact.googleMap.label.zoomControl' => 'Zoom control',
    'contact.googleMap.ph.backgroundColor' => 'Ie. white or the html code of it - #ffffff',
    'contact.googleMap.ph.disableDefaultUI' => 'Choose',
    'contact.googleMap.ph.disableDoubleClickZoom' => 'Choose',
    'contact.googleMap.ph.draggable' => 'Choose',
    'contact.googleMap.ph.keyboardShortcuts' => 'Choose',
    'contact.googleMap.ph.mapMaker' => 'Choose',
    'contact.googleMap.ph.mapType' => 'Choose',
    'contact.googleMap.ph.mapTypeControl' => 'Choose',
    'contact.googleMap.ph.noClear' => 'Choose',
    'contact.googleMap.ph.overviewMapControl' => 'Choose',
    'contact.googleMap.ph.panControl' => 'Choose',
    'contact.googleMap.ph.rotateControl' => 'Choose',
    'contact.googleMap.ph.scaleControl' => 'Choose',
    'contact.googleMap.ph.scrollwheel' => 'Choose',
    'contact.googleMap.ph.streetViewControl' => 'Choose',
    'contact.googleMap.ph.zoomControl' => 'Choose',
    'contact.information.label.custom_information' => 'Custom contact information',
    'contact.information.label.page_text' => 'Contact information text',
    'contact.information.label.show_custom_information' => 'Show custom contact information',
    'contact.information.label.show_form' => 'Show contact form',
    'contact.information.ph.custom_information' => 'Enter your custom contact information',
    'contact.information.ph.page_text' => 'Page text',
    'description.bannerInSidebar.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.banners.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.banners1.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.banners2.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.bannersHomePage.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.bannersTextPage.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.blog.blog.blog' => 'With this extension you control the number of blogs (article categories) that will be visible on a page',
    'description.blogHome.blog.blog' => 'With this extension you control the number of blogs (article categories) that will be visible on a page',
    'description.buttonToTop.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.carousel.extra.carousel' => 'With this extension you can create and control the type and properties of the sliders visible on the homepage of your online store',
    'description.cartText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.categoryShowcaseBackground.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.checkoutPrice.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.checkoutSideText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.checkoutSignInGuestText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.checkoutSignInLoginText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.checkoutSignInRegisterText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.checkoutText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.contact.googleMap' => 'This module allows you to preview a Google map with locations of your offices, shops or other sites',
    'description.contactInformation.contact.information' => 'With this extension you control the contact form for your customers, visible on the page',
    'description.extra.htmlLine' => 'Promo bar',
    'description.extra.search' => 'With this module you will allow users to receive hints about products they are looking for',
    'description.footerConfiguration.layout.footer' => '',
    'description.footerContacts.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.footerContent.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.footerLinks1.navigation.links' => 'With this extension, you control static links visible at the top of your store.',
    'description.footerLinks2.navigation.links' => 'With this extension, you control static links visible at the top of your store.',
    'description.footerLinks3.navigation.links' => 'With this extension, you control static links visible at the top of your store.',
    'description.footerText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.footerTextBackground.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.headerConfiguration.layout.header' => '',
    'description.headerImage1.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.headerImage2.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.headerRight.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.headerText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.homeSingleBanner.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.homeText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.homeText1.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.homeText1Background.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.homeText2.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.homeText2Background.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.homeText3.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.homeText3Background.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.homeTextBackground.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.homeTopAfterCategoryBackground.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.homeTopBackground.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.homeTopBanner.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.homeTopTextAfterCategoryShowcase.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.homeVideoBackgroundImage.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.homeVideoText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.homeWelcome.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.latestNewsBackground.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.mailchimp.newsletter' => 'This module allows you to make pop-up settings to collect emails',
    'description.navigationLinks.navigation.links' => 'With this extension, you control static links visible at the top of your store.',
    'description.navigationLinksPage.navigation.links' => 'With this extension, you control static links visible at the top of your store.',
    'description.newProductsBackground.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.newProductsBanners.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.pageLoader.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.pagesBanner.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.product.filters' => 'Settings for all pages displaying product lists. Settings can be Filters, Sort, Product Labels and more. others',
    'description.product.lastViewed' => 'The module loads the last few products that each user has viewed',
    'description.product.productInBundles' => 'Through this module, you can choose how many and how many products per package to display in the product detail page',
    'description.productShowcaseBanners.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.productText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.productsCombine.product.related' => 'Through this extension, you give your customers the opportunity to see similar products to the one they open once,',
    'description.productsDetails.product.productsDetails' => 'Include and exclude items that appear in the product detail page',
    'description.productsRelated.product.related' => 'Through this extension, you give your customers the opportunity to see similar products to the one they open once,',
    'description.productsRelated2.product.related' => 'Through this extension, you give your customers the opportunity to see similar products to the one they open once,',
    'description.recentArticles.blog.recentArticles' => 'With this extension you control the number of active and visible articles on the page',
    'description.recentComments.blog.recentComments' => 'Through this extension you control the number of comments that will be visible below the articles',
    'description.showcaseBestSellersProducts.product.productShowcase' => 'With this extension you control the type and arrangement of products that customers will see on the home page of your store',
    'description.showcaseBrand.product.showcase' => 'With this extension you can choose whether and how to present your categories and brands, which will be visible on the home page of your store',
    'description.showcaseBrands1.product.showcase' => 'With this extension you can choose whether and how to present your categories and brands, which will be visible on the home page of your store',
    'description.showcaseBrands2.product.showcase' => 'With this extension you can choose whether and how to present your categories and brands, which will be visible on the home page of your store',
    'description.showcaseCategories.product.showcase' => 'With this extension you can choose whether and how to present your categories and brands, which will be visible on the home page of your store',
    'description.showcaseCategory.product.showcase' => 'With this extension you can choose whether and how to present your categories and brands, which will be visible on the home page of your store',
    'description.showcaseProducts.product.productShowcase' => 'Through this extension you control the type and arrangement of products that customers will see on the home page of your store',
    'description.showcaseProducts1.product.productShowcase' => 'Through this extension you control the type and arrangement of products that customers will see on the home page of your store',
    'description.showcaseProducts2.product.productShowcase' => 'Through this extension you control the type and arrangement of products that customers will see on the home page of your store',
    'description.showcaseProducts3.product.productShowcase' => 'Through this extension you control the type and arrangement of products that customers will see on the home page of your store',
    'description.showcaseProducts4.product.productShowcase' => 'Through this extension you control the type and arrangement of products that customers will see on the home page of your store',
    'description.sidebarBanners1.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.sidebarBanners2.extra.banner' => 'With this extension you can add and manage banners in your store',
    'description.sidebarText.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.social.extra.social' => 'This extension allows quick and easy access for your customers to all other store profiles on the most used social networks',
    'description.summaryAdditionalInfo.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.testimonial.extra.carousel' => 'With this extension you can create and control the type and properties of the sliders visible on the homepage of your online store',
    'description.text1.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.text1Background.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.text2.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.text2Background.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.text3.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.text3Background.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.textBoxBackground_1.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.textBoxBackground_2.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.textBoxBackground_3.extra.backgroundImage' => 'With this extension you can set a basic image of the store, which will be visible as a background on each page and section of the store',
    'description.textBox_1.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.textBox_2.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.textBox_3.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.textbox1.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.textbox1_tooltip.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.textbox2.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.textbox2_tooltip.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.textbox3.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.textbox3_tooltip.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.textbox4.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'description.textbox4_tooltip.extra.text' => 'With this extension you can update the static text visible on every page of the store',
    'extra.addThisShare.label.custom_toolbar' => 'Custom toolbar code',
    'extra.addThisShare.label.layout' => 'Layout',
    'extra.addThisShare.label.og_image_url' => 'Cover image for sharing',
    'extra.addThisShare.label.show_compact' => 'Show compact',
    'extra.addThisShare.label.show_counter' => 'Show counter',
    'extra.addThisShare.label.show_top_services' => 'Show top services',
    'extra.addThisShare.label.ui_click' => 'UI click',
    'extra.addThisShare.label.ui_hover_direction' => 'Dropdown box direction',
    'extra.addThisShare.layout.custom' => 'Custom',
    'extra.addThisShare.layout.large' => 'Large',
    'extra.addThisShare.layout.small' => 'Small',
    'extra.addThisShare.ui_hover_direction.bottom' => 'Bottom',
    'extra.addThisShare.ui_hover_direction.top' => 'Top',
    'extra.backgroundImage.action.add_external_image' => 'External image',
    'extra.backgroundImage.action.add_image' => 'Add background image',
    'extra.backgroundImage.action.add_storage_image' => 'Image from storage',
    'extra.backgroundImage.label.choose_image_type' => 'Background type',
    'extra.backgroundImage.label.color' => 'Color picker',
    'extra.backgroundImage.label.src' => 'Background image source',
    'extra.banner.action.add_external_image' => 'External',
    'extra.banner.action.add_image' => 'Add image',
    'extra.banner.action.add_storage_image' => 'From storage',
    'extra.banner.blog' => 'Blog category',
    'extra.banner.blog_article' => 'Blog article',
    'extra.banner.err.script_required' => 'Snipped code is required',
    'extra.banner.external' => 'External',
    'extra.banner.image' => 'Image',
    'extra.banner.label.amount' => 'Number of images to show',
    'extra.banner.label.banner' => 'Image',
    'extra.banner.label.blank' => 'Open in new window',
    'extra.banner.label.caption' => 'Caption',
    'extra.banner.label.choose_image_type' => 'Choose an image',
    'extra.banner.label.enable_gallery' => 'Show images, that are without a link, in gallery',
    'extra.banner.label.link' => 'Link',
    'extra.banner.label.script' => 'Snippet code',
    'extra.banner.label.src' => 'URL address',
    'extra.banner.label.type' => 'Type',
    'extra.banner.no_link' => 'No link',
    'extra.banner.page' => 'Page',
    'extra.banner.ph.caption' => 'Enter caption',
    'extra.banner.ph.link' => 'Enter link',
    'extra.banner.ph.script' => 'Here you can add a simple snipped code like Javascript or HTML',
    'extra.banner.ph.select_article' => 'Select article',
    'extra.banner.ph.select_blog' => 'Select blog category',
    'extra.banner.ph.select_category' => 'Select category',
    'extra.banner.ph.select_external' => 'Select external',
    'extra.banner.ph.select_product' => 'Select product',
    'extra.banner.ph.select_vendor' => 'Select vendor',
    'extra.banner.ph.type' => 'Choose type',
    'extra.banner.product' => 'Product',
    'extra.banner.product_category' => 'Category',
    'extra.banner.script' => 'Snippet',
    'extra.banner.section' => 'Smart Collection',
    'extra.banner.vendor' => 'Brand',
    'extra.carousel.action.add_external_image' => 'Add external image',
    'extra.carousel.action.add_image' => 'Add image',
    'extra.carousel.action.add_storage_image' => 'Add storage image',
    'extra.carousel.action.add_video' => 'Add YouTube, Vimeo or Wistia video',
    'extra.carousel.blog' => 'Link to an article category',
    'extra.carousel.blog_article' => 'Link to a specific article',
    'extra.carousel.err.caption_required' => 'Carousel caption is required.',
    'extra.carousel.external' => 'External web address',
    'extra.carousel.help.amount' => 'Choose amount of images for the carousel.',
    'extra.carousel.help.animate' => 'Choose carousel slide change effect.',
    'extra.carousel.help.autoplay' => 'Enables/disables automatic image change (autoplay).',
    'extra.carousel.help.caption' => 'Enables/disables captions.',
    'extra.carousel.help.controls' => 'Show/hides the carousel controls (next, previous, etc.).',
    'extra.carousel.help.cycle' => 'Choose whether or not the carousel should loop all images.',
    'extra.carousel.help.full_width' => 'Enable full width of the slider',
    'extra.carousel.help.indicators' => 'Enable/disable indicators.',
    'extra.carousel.help.interval' => 'The interval between each image change (in milliseconds - ex. a value of 1000 is equal to 1 second).',
    'extra.carousel.help.margin' => 'Select the distance between the slides in px',
    'extra.carousel.help.pause' => 'Enable/disable pausing of the carousel.',
    'extra.carousel.label.amount' => 'Amount',
    'extra.carousel.label.animate' => 'Carousel animate effect',
    'extra.carousel.label.autoplay' => 'Autoplay',
    'extra.carousel.label.blank' => 'Open in new window',
    'extra.carousel.label.caption' => 'Caption',
    'extra.carousel.label.choose_image_type' => 'Choose image type',
    'extra.carousel.label.controls' => 'Controls',
    'extra.carousel.label.cycle' => 'Cycle',
    'extra.carousel.label.full_width' => 'Full width',
    'extra.carousel.label.horizontal_position' => 'Carousel content horizontal position',
    'extra.carousel.label.html' => 'Desktop carousel content',
    'extra.carousel.label.html_mobile' => 'Mobile carousel content',
    'extra.carousel.label.html_tablet' => 'Tablet carousel content',
    'extra.carousel.label.indicators' => 'Indicators',
    'extra.carousel.label.interval' => 'Interval',
    'extra.carousel.label.is_homepage_slider' => 'Is homepage slider',
    'extra.carousel.label.link' => 'Link',
    'extra.carousel.label.link_caption' => 'Caption',
    'extra.carousel.label.margin' => 'Spacing',
    'extra.carousel.label.mobile' => 'Form mobile',
    'extra.carousel.label.pause' => 'Pause',
    'extra.carousel.label.slide' => 'Slide',
    'extra.carousel.label.slides_per_view' => 'Slides per view',
    'extra.carousel.label.sorting' => 'Sorting',
    'extra.carousel.label.src' => 'Source',
    'extra.carousel.label.vertical_position' => 'Carousel content vertical position',
    'extra.carousel.label.whole_link' => 'Make the whole slide a link',
    'extra.carousel.no_link' => 'No link',
    'extra.carousel.page' => 'Link to a specific page',
    'extra.carousel.ph.caption' => 'Choose',
    'extra.carousel.ph.interval' => 'Set in milliseconds',
    'extra.carousel.ph.link' => 'Ie. Google',
    'extra.carousel.ph.link_caption' => 'Link caption',
    'extra.carousel.ph.margin' => 'px',
    'extra.carousel.ph.select_article' => 'Select article',
    'extra.carousel.ph.select_blog' => 'Select blog',
    'extra.carousel.ph.select_category' => 'Select category',
    'extra.carousel.ph.select_external' => 'Select external',
    'extra.carousel.ph.select_product' => 'Select product',
    'extra.carousel.ph.select_vendor' => 'Select vendor',
    'extra.carousel.position_bottom' => 'Position bottom',
    'extra.carousel.position_center' => 'Position center',
    'extra.carousel.position_left' => 'Position left',
    'extra.carousel.position_middle' => 'Position middle',
    'extra.carousel.position_right' => 'Position right',
    'extra.carousel.position_top' => 'Position top',
    'extra.carousel.product' => 'Link to specific product',
    'extra.carousel.product_category' => 'Link to a product category',
    'extra.carousel.section' => 'Link to a store section',
    'extra.carousel.vendor' => 'Link to a specific vendor',
    'extra.social.label.your_%1$s_page' => 'Your %1$s page',
    'extra.text.label.bottom' => 'Bottom',
    'extra.text.label.button.float' => 'Choose the position for the button in the promo bar',
    'extra.text.label.button.link' => 'Type the name of the button in the promo bar',
    'extra.text.label.button.text' => 'Type a text for the button in the promo bar',
    'extra.text.label.button.title' => 'Button in the promo bar',
    'extra.text.label.period.from' => 'Active from',
    'extra.text.label.period.to' => 'Active to',
    'extra.text.label.promobar_position' => 'Position relative to navigation',
    'extra.text.label.text' => 'Text',
    'extra.text.label.title' => 'Title',
    'extra.text.label.top' => 'Top',
    'extra.text.ph.title' => 'Enter text title',
    'global.err.article_no_longer_exists' => 'Article no longer exists.',
    'global.err.blog_no_longer_exists' => 'Article category no longer exists.',
    'global.err.caption_max_%1$s' => 'Caption cannot be more than %1$s characters long.',
    'global.err.caption_required' => 'Caption is required.',
    'global.err.category_no_longer_exists' => 'Product category no longer exists.',
    'global.err.invalid_request' => 'Invalid request.',
    'global.err.link_caption_required' => 'External web address caption is required.',
    'global.err.link_value_required' => 'External web address is required.',
    'global.err.page_no_longer_exists' => 'Page no longer exists.',
    'global.err.product_no_longer_exists' => 'Product no longer exists.',
    'global.err.src_required' => 'External image source is required.',
    'global.err.vendor_no_longer_exists' => 'Brand no longer exists.',
    'google_map.description' => '',
    'google_map.name' => '',
    'group.banners' => 'Images',
    'group.brands' => 'Vendors',
    'group.categories' => 'Categories',
    'group.footer' => 'Footer',
    'group.header' => 'Header',
    'group.menu' => 'Menu',
    'group.products' => 'Products',
    'group.show_all' => 'Show all',
    'group.slider' => 'Slider',
    'group.testimonials' => 'Testimonials',
    'group.text_fields' => 'Text fields',
    'group.top_bar' => 'Top bar',
    'instagram.description' => 'With this widget you can automatically visualize Instagram photos in your store',
    'instagram.name' => 'Instagram photos',
    'layout.buttons.label.border_bottom_left_radius' => 'Bottom left border radius',
    'layout.buttons.label.border_bottom_right_radius' => 'Bottom right border radius',
    'layout.buttons.label.border_top_left_radius' => 'Top left border radius',
    'layout.buttons.label.border_top_right_radius' => 'Top right border radius',
    'layout.buttons.title.border_radius' => 'Border radius',
    'layout.footer.footer_max_cols_count' => 'Footer max cols count',
    'layout.grid.label.grid_width' => 'Set grid width',
    'layout.grid.label.grid_width_full' => 'Set grid width full',
    'layout.grid.label.offset_desktop' => 'Set offset desktop',
    'layout.grid.label.offset_mobile' => 'Set offset mobile',
    'layout.grid.title.grid_settings' => 'Grid settings',
    'mailchimp.newsletter.configure_app_first' => 'Please, first configure application',
    'mailchimp.newsletter.label.automatic' => 'Automatically display',
    'mailchimp.newsletter.label.delay' => 'Delay',
    'mailchimp.newsletter.label.description' => 'Description',
    'mailchimp.newsletter.label.show_form' => 'Showing the form at the footer',
    'mailchimp.newsletter.label.status' => 'Subscriber\'s current status',
    'mailchimp.newsletter.label.title' => 'Newsletter title',
    'mailchimp.newsletter.ph.description' => 'Enter description',
    'mailchimp.newsletter.ph.title' => 'Enter name',
    'mailchimp.newsletter.status.pending' => 'Pending',
    'mailchimp.newsletter.status.subscribed' => 'Subscribed',
    'name.bannerInSidebar.extra.banner' => 'Banners in the sidebar',
    'name.banners.extra.banner' => 'Banners',
    'name.bannersHomePage.extra.banner' => 'Banners - Index',
    'name.bannersTextPage.extra.banner' => 'Banners - Text page',
    'name.blog.blog.blog' => 'Blog',
    'name.blogHome.blog.blog' => 'Blog Home',
    'name.buttonToTop.extra.text' => 'Up button',
    'name.buttonsConfiguration.layout.button' => 'Buttons settings',
    'name.carousel.extra.carousel' => 'Slider',
    'name.cartText.extra.text' => 'Text Cart',
    'name.categoryShowcaseBackground.extra.backgroundImage' => 'Showcase with categories - Background',
    'name.checkoutPrice.extra.text' => 'Text for price and delivery when sending an order',
    'name.checkoutSideText.extra.text' => 'Text in the sidebar of the cart',
    'name.checkoutSignInGuestText.extra.text' => 'Shopping cart text when Guest',
    'name.checkoutSignInLoginText.extra.text' => 'Cart text at Login',
    'name.checkoutSignInRegisterText.extra.text' => 'Cart text at Register',
    'name.checkoutText.extra.text' => 'Complete order - text',
    'name.contactInformation.contact.information' => 'Contact information',
    'name.extra.htmlLine' => 'Promo bar',
    'name.filters.product.filters' => 'Product Catalog Settings',
    'name.footerConfiguration.layout.footer' => 'Foot settings',
    'name.footerContacts.extra.text' => 'Footer contacts',
    'name.footerContent.extra.text' => 'Text in footer',
    'name.footerLinks1.navigation.links' => 'Footer navigation links 1',
    'name.footerLinks2.navigation.links' => 'Footer navigation links 2',
    'name.footerLinks3.navigation.links' => 'Footer navigation links 3',
    'name.footerText.extra.text' => 'Footer text',
    'name.footerTextBackground.extra.backgroundImage' => 'Footer text - Background',
    'name.googleMap.contact.googleMap' => 'Google Map',
    'name.headerConfiguration.layout.header' => 'Header settings',
    'name.headerImage1.extra.backgroundImage' => 'Heather Image 1',
    'name.headerImage2.extra.backgroundImage' => 'Heather Image 2',
    'name.headerLeft.extra.text' => 'Text before the logo',
    'name.headerRight.extra.text' => 'Text after the logo',
    'name.headerText.extra.text' => 'Heather Text',
    'name.homeSingleBanner.extra.banner' => 'Homepage banners',
    'name.homeText.extra.text' => 'Homepage text',
    'name.homeText1.extra.text' => 'Homepage text 1',
    'name.homeText1Background.extra.backgroundImage' => 'Homepage text 1 - photo',
    'name.homeText2.extra.text' => 'Homepage text 2',
    'name.homeText2Background.extra.backgroundImage' => 'Homepage text 2 - photo',
    'name.homeText3.extra.text' => 'Homepage text 3',
    'name.homeText3Background.extra.backgroundImage' => 'Homepage text 3 - photo',
    'name.homeTextBackground.extra.backgroundImage' => 'Homepage text - background',
    'name.homeTopAfterCategoryBackground.extra.backgroundImage' => 'Banner text background after the homepage with categories',
    'name.homeTopBackground.extra.backgroundImage' => 'Banner text background at the top of the homepage',
    'name.homeTopBanner.extra.text' => 'Text at the top of the homepage',
    'name.homeTopTextAfterCategoryShowcase.extra.text' => 'Text after the category window on the homepage',
    'name.homeVideoBackgroundImage.extra.backgroundImage' => 'Picture of the video section of the homepage.',
    'name.homeVideoText.extra.text' => 'Homepage text and video',
    'name.homeWelcome.extra.text' => 'Welcome text',
    'name.latestNewsBackground.extra.backgroundImage' => 'Latest News - Background',
    'name.layout.buttons' => 'Buttons settings',
    'name.layout.footer' => 'Footer configuration',
    'name.layout.grid' => 'Grid settings',
    'name.layout.header' => 'Header configuration',
    'name.navigationLinks.navigation.links' => 'Navigation links',
    'name.navigationLinksPage.navigation.links' => 'Page menu - Pages',
    'name.newProductsBackground.extra.backgroundImage' => 'New products - Background',
    'name.newProductsBanners.extra.banner' => 'Banners in new products',
    'name.newsletter.mailchimp.newsletter' => 'Newsletter (MailChimp)',
    'name.pageLoader.extra.backgroundImage' => 'Page load indicator',
    'name.pagesBanner.extra.banner' => 'Banner in static pages',
    'name.product.lastViewed' => 'Last viewed products',
    'name.product.linked' => 'Linked products',
    'name.product.productInBundles' => 'Product in packages',
    'name.productShowcaseBanners.extra.banner' => 'Banners',
    'name.productText.extra.text' => 'Text in product details',
    'name.productsCombine.product.related' => 'Match with',
    'name.productsDetails.product.productsDetails' => 'Detailed product information',
    'name.productsRelated.product.related' => 'Related Products',
    'name.productsRelated2.product.related' => 'Top products',
    'name.recentArticles.blog.recentArticles' => 'Latest Articles',
    'name.recentComments.blog.recentComments' => 'Last comments',
    'name.search.extra.search' => 'Search',
    'name.showcaseBestSellersProducts.product.productShowcase' => 'Showcase with the best-selling products',
    'name.showcaseBrand.product.showcase' => 'Showcase with stamps',
    'name.showcaseBrands1.product.showcase' => 'Showcase with stamps 1',
    'name.showcaseBrands2.product.showcase' => 'Showcase with stamps 2',
    'name.showcaseCategories.product.showcase' => 'Category showcase',
    'name.showcaseCategory.product.showcase' => 'Category showcase',
    'name.showcaseProducts.product.productShowcase' => 'Product Showcase',
    'name.showcaseProducts1.product.productShowcase' => 'Product Showcase 1',
    'name.showcaseProducts2.product.productShowcase' => 'Product Showcase 2',
    'name.showcaseProducts3.product.productShowcase' => 'Product Showcase 3',
    'name.showcaseProducts4.product.productShowcase' => 'Product Showcase 4',
    'name.sidebarBanners1.extra.banner' => 'Banners',
    'name.sidebarBanners2.extra.banner' => 'Banners',
    'name.sidebarText.extra.text' => 'Sidebar text',
    'name.social.extra.social' => 'Social media',
    'name.summaryAdditionalInfo.extra.text' => 'Information in the price cart',
    'name.text1.extra.text' => 'Homepage text 1',
    'name.text1Background.extra.backgroundImage' => 'Homepage text 1 - background',
    'name.text2.extra.text' => 'Homepage text 2',
    'name.text2Background.extra.backgroundImage' => 'Homepage text 2 - background',
    'name.text3.extra.text' => 'Homepage text 3',
    'name.text3Background.extra.backgroundImage' => 'Homepage text 3 - background',
    'name.textBoxBackground_1.extra.backgroundImage' => 'Text box 1 - background',
    'name.textBoxBackground_2.extra.backgroundImage' => 'Text box 2 - background',
    'name.textBoxBackground_3.extra.backgroundImage' => 'Text box 3 - background',
    'name.textBox_1.extra.text' => 'Text box 1',
    'name.textBox_2.extra.text' => 'Text box 2',
    'name.textBox_3.extra.text' => 'Text box 3',
    'name.textbox1.extra.text' => 'Text box 1',
    'name.textbox1_tooltip.extra.text' => 'Text box 1 - hint',
    'name.textbox2.extra.text' => 'Text box 2',
    'name.textbox2_tooltip.extra.text' => 'Text box 2 - hint',
    'name.textbox3.extra.text' => 'Text box 3',
    'name.textbox3_tooltip.extra.text' => 'Text box 3 - hint',
    'name.textbox4.extra.text' => 'Text box 4',
    'name.textbox4_tooltip.extra.text' => 'Text box 4 - hint',
    'navigation.links.action.add' => 'Add link',
    'navigation.links.blog' => 'Article category',
    'navigation.links.blog_article' => 'Specific Article',
    'navigation.links.external' => 'External address',
    'navigation.links.label.blank' => 'Open in new window',
    'navigation.links.label.icon' => 'Choose an icon for the link',
    'navigation.links.label.link' => 'Choose link',
    'navigation.links.label.link_caption' => 'Link caption',
    'navigation.links.page' => 'Static page',
    'navigation.links.ph.link' => 'Link',
    'navigation.links.ph.link_caption' => 'Link caption',
    'navigation.links.ph.select_article' => 'Select article',
    'navigation.links.ph.select_blog' => 'Select article category',
    'navigation.links.ph.select_category' => 'Select product category',
    'navigation.links.ph.select_external' => 'E.g. http://google.com',
    'navigation.links.ph.select_product' => 'Select product',
    'navigation.links.ph.select_vendor' => 'Select vendor',
    'navigation.links.product' => 'Specific product',
    'navigation.links.product_category' => 'Product category',
    'navigation.links.section' => 'Store section',
    'navigation.links.vendor' => 'Brand',
    'newsletter.description' => 'This widget helps you to collect your visitors emails',
    'newsletter.name' => 'Newsletter',
    'order-details.description' => 'You can add this widget to a landing page used as a Thank you page and each of your customers will see the exact details of their own orders.',
    'order-details.name' => 'Order details',
    'product.button.color.active' => 'Active',
    'product.button.color.default' => 'Default',
    'product.button.color.secondary' => 'Secondary',
    'product.button.label.amount' => 'Number of buttons to show',
    'product.button.label.attributes' => 'Additional attributes',
    'product.button.label.color' => 'Color',
    'product.button.label.full_width' => 'Full width',
    'product.button.label.link' => 'URL',
    'product.button.label.override_text' => 'Override button text',
    'product.button.label.position' => 'Position',
    'product.button.label.size' => 'Size',
    'product.button.label.target' => 'Open in new window',
    'product.button.label.text' => 'Text',
    'product.button.placeholder.attributes' => 'e.g. data-ajax-panel',
    'product.button.size.default' => 'Default',
    'product.button.size.large' => 'Large',
    'product.button.size.small' => 'Small',
    'product.button.text.center' => 'Center',
    'product.button.text.left' => 'Left',
    'product.button.text.right' => 'Right',
    'product.description' => 'With this widget you can visualize specific product with detailed information',
    'product.linked.label.position' => 'Position',
    'product.linked.position.recommended' => 'As recommended products under Buy section',
    'product.linked.position.section_recommended' => 'Like the Recommended Products section',
    'product.linked.position.variant' => 'As variant',
    'product.list.help.template_list_horizontal' => 'This option displays the items in product list on one line. Applies only to the desktop version and to products that are NOT in a slider.',
    'product.list.label.color_product_variants' => 'Show product colors',
    'product.list.label.manufacturer_logo_show' => 'Show manufacturer logo',
    'product.list.label.max_title_rows' => 'Set maximum number of product title lines',
    'product.list.label.per_row_desktop' => 'Products per row for desktop',
    'product.list.label.per_row_mobile' => 'Products per row for mobile',
    'product.list.label.second_image_show' => 'Show second image on hover',
    'product.list.label.variants' => 'Show product variants',
    'product.listing.action.add_per_page_option' => 'Add \'per page\' variants',
    'product.listing.action.add_price_range' => 'Add price ranges',
    'product.listing.action.remove_price_range' => 'Remove price range',
    'product.listing.brand_model' => 'Brand and Model',
    'product.listing.category_properties' => 'Category Properties',
    'product.listing.category_properties_limit' => 'Category Properties limit',
    'product.listing.cation.remove_per_page_option' => 'Remove per page',
    'product.listing.date' => 'Date',
    'product.listing.enable_category_properties' => 'Enable Category Properties',
    'product.listing.err.from_price_must_be_lower_than_to_price' => 'Price from must be lower than price to.',
    'product.listing.err.one_or_more_from_or_to_value_missing' => 'One or more from or to values are missing.',
    'product.listing.err.per_page_value_not_present_in_current_per_page_options' => 'Per page value is not present in the current per page options.',
    'product.listing.err.price_range_max_should_be_greater_than_price_range_min' => 'Max price range should be greater than min price range',
    'product.listing.err.price_range_step_should_be_lower_than_price_range_max' => 'Price range step should be lower than max price range',
    'product.listing.filters_options' => 'Filter order setting in a product listing page',
    'product.listing.filters_options_order' => 'Order filters in a product listing page',
    'product.listing.filters_options_order_info' => 'You can change the filtering order in the product catalog by changing the sort number. The lower the number, the higher the filter position.',
    'product.listing.hide_featured' => 'Hide Featured Label',
    'product.listing.hide_sale' => 'Hide SALE Label',
    'product.listing.list_one' => 'List one',
    'product.listing.list_two' => 'List two',
    'product.listing.mark_out_of_stock_products' => 'mark out of stock products',
    'product.listing.order_by' => 'Order by',
    'product.listing.order_by_options' => 'Add \'order by\' options',
    'product.listing.order_direction' => 'Order direction',
    'product.listing.order_latest_out_of_stock' => 'out of stock products in end of list',
    'product.listing.per_page' => 'Products per page',
    'product.listing.per_page_options' => 'Per page options',
    'product.listing.price' => 'Price',
    'product.listing.price_range_max' => 'Max price range',
    'product.listing.price_range_min' => 'Min price range',
    'product.listing.price_range_step' => 'Price range step',
    'product.listing.price_ranges_from' => 'Range from',
    'product.listing.price_ranges_to' => 'Range to',
    'product.listing.products_price_ranges' => 'Products price ranges',
    'product.listing.remove_price_range' => 'Remove price range',
    'product.listing.show_out_of_stock_products' => 'out of stock products',
    'product.listing.show_short_description' => 'Short description',
    'product.listing.sort_order' => 'Manual sort',
    'product.name' => 'Product',
    'product.productDetails.label.hide_tags' => 'Hide product tags',
    'product.productDetails.label.pre_selected_variant' => 'Default variant is selected',
    'product.productDetails.label.show_images_in_gallery' => 'Show product`s images in pop-up gallery',
    'product.productDetails.label.show_product_quantity_in_status' => 'Show product quantity in status',
    'product.productDetails.label.variant_in_name' => 'Show selected variant in product name',
    'product.productShowcase.filter_by_all' => 'All',
    'product.productShowcase.filter_by_category' => 'Filter by product category',
    'product.productShowcase.filter_by_product' => 'Filter by product',
    'product.productShowcase.filter_by_tag' => 'Filter by tag',
    'product.productShowcase.filter_by_type_bundle' => 'Bundle',
    'product.productShowcase.filter_by_type_digital' => 'Digital',
    'product.productShowcase.filter_by_type_physically' => 'Physically',
    'product.productShowcase.filter_by_vendor' => 'Filter by vendor',
    'product.productShowcase.help.color' => 'Type the color code in the field below in order to choose the color for the icon.',
    'product.productShowcase.help.icon' => 'The icon will be visible next to your showcase\'s name.',
    'product.productShowcase.label.color' => 'Choose a color for the icon',
    'product.productShowcase.label.featured' => 'Featured',
    'product.productShowcase.label.filter' => 'Filters',
    'product.productShowcase.label.icon' => 'Choose an icon for your showcase',
    'product.productShowcase.label.new' => 'New',
    'product.productShowcase.label.sale' => 'Sale',
    'product.productShowcase.label.select_categories' => 'Select product categories',
    'product.productShowcase.label.select_products' => 'Select products',
    'product.productShowcase.label.select_tags' => 'Select tags',
    'product.productShowcase.label.select_vendors' => 'Select vendors',
    'product.productShowcase.ph.color' => 'Color',
    'product.productShowcase.ph.featured' => 'Featured',
    'product.productShowcase.ph.filter' => 'Filter',
    'product.productShowcase.ph.new' => 'New',
    'product.productShowcase.ph.sale' => 'Sale',
    'product.related.filter_by_category' => 'Filter by category',
    'product.related.filter_by_tag' => 'Filter by tag',
    'product.related.filter_by_vendor' => 'Filter by vendor',
    'product.related.help.order_direction' => 'Choose order direction',
    'product.related.label.order_by' => 'Order by',
    'product.related.label.order_direction' => 'Order direction',
    'product.related.label.out_of_stock' => 'Out of stock',
    'product.related.label.product_count' => 'Product count',
    'product.related.label.title' => 'Title',
    'product.related.label.type' => 'Type',
    'product.related.order_by_id' => 'Order by ID',
    'product.related.order_by_match' => 'By match totals',
    'product.related.order_by_name' => 'Order by name',
    'product.related.order_by_price' => 'Price',
    'product.related.order_by_rand' => 'Random',
    'product.related.ph.order_by' => 'Order by',
    'product.related.ph.product_count' => 'Enter the number of products you want to appear as related',
    'product.related.ph.title' => 'Related products',
    'product.related.ph.type' => 'Choose type',
    'product.search.add_utm' => 'Add <b> utm_source </b> to the URL',
    'product.search.add_utm.help' => 'Ability to track in-store sales',
    'product.search.label.autocomplete' => 'Option for autocomplete the search for your customers',
    'product.search.label.search_in_product_description' => 'Search in products description',
    'product.selected.err.products_required' => 'The products are required',
    'product.showcase.category' => 'Product category',
    'product.showcase.err.filter_value_required' => 'Filter value is required.',
    'product.showcase.err.more_than_20_categories' => 'The selected categories cannot be more than :count ',
    'product.showcase.err.more_than_20_products' => 'The products can\'t be more than :count',
    'product.showcase.err.more_than_20_vendors' => 'Choose manufacturers that you can not continue more than :count',
    'product.showcase.err.one_or_more_categories_no_longer_exist' => 'Some or all of the chosen categories do not exist.',
    'product.showcase.err.one_or_more_products_no_longer_exist' => 'One or more of the chosen products do not exist.',
    'product.showcase.err.one_or_more_tags_no_longer_exist' => 'One or more of the chosen tags do not exist.',
    'product.showcase.err.one_or_more_vendors_no_longer_exist' => 'One or more of the chosen vendors do not exist.',
    'product.showcase.label.arrows_position' => 'Arrows Position',
    'product.showcase.label.arrows_position.center' => 'Center',
    'product.showcase.label.arrows_position.top' => 'Top',
    'product.showcase.label.carousel_options' => 'Carousel Options',
    'product.showcase.label.enable_arrows' => 'Еnable arrows',
    'product.showcase.label.enable_pagination' => 'Еnable pagination',
    'product.showcase.label.enable_slider' => 'Enable carousel',
    'product.showcase.label.header' => 'Header',
    'product.showcase.label.items' => 'Items',
    'product.showcase.label.items_options' => 'Item options',
    'product.showcase.label.items_per_row' => 'Items per row',
    'product.showcase.label.order_by' => 'Order by',
    'product.showcase.label.order_direction' => 'Order direction',
    'product.showcase.label.product_count' => 'Products count',
    'product.showcase.label.products_per_row' => 'Products per row',
    'product.showcase.label.select' => 'Select items to showcase',
    'product.showcase.label.space_between' => 'Space between the products in px',
    'product.showcase.label.space_between_mobile' => 'Space between the products for mobile in px',
    'product.showcase.label.title' => 'Title',
    'product.showcase.label.type' => 'Type',
    'product.showcase.order_by_id' => 'Order by date',
    'product.showcase.order_by_name' => 'Order by name',
    'product.showcase.order_by_rand' => 'Random order',
    'product.showcase.ph.filter' => 'Filter products by',
    'product.showcase.ph.header' => 'E.g. Featured products',
    'product.showcase.ph.order_by' => 'Choose order by',
    'product.showcase.ph.order_direction' => 'Choose order direction',
    'product.showcase.ph.product_count' => 'Enter product count',
    'product.showcase.ph.space_between' => 'Space in px',
    'product.showcase.ph.space_between_mobile' => 'Space in px',
    'product.showcase.ph.title' => 'Enter title',
    'product.showcase.vendor' => 'Brand',
    'product_showcase.description' => 'Showcase selected products. Select from product category, vendor, tag, or collection. You can even select specific products and order them',
    'product_showcase.name' => 'Product showcases',
    'recent_articles.description' => 'With this widget you can visualize articles from blog',
    'recent_articles.name' => 'Articles',
    'row_settings.background-attachment' => 'Parallax effect',
    'row_settings.background-position-x' => 'Horizontal background possition',
    'row_settings.background-position-y' => 'Vertical background possition',
    'row_settings.background-size' => 'Background style',
    'row_settings.class_name' => 'CSS class name',
    'row_settings.full_width' => 'Full width',
    'row_settings.joint_background' => 'Apply from previous row',
    'row_settings.transparent' => 'Transparent',
    'row_settings.margin_bottom' => 'Margin Bottom (px)',
    'row_settings.margin_side' => 'Margin Lateral (px)',
    'row_settings.margin_top' => 'Margin Top (px)',
    'row_settings.padding_bottom' => 'Padding Bottom (px)',
    'row_settings.padding_side' => 'Padding Lateral (px)',
    'row_settings.padding_top' => 'Padding Top (px)',
    'row_settings.reverse_column_order' => 'Reverse column order on desktop',
    'row_settings.show_mobile_or_desktop' => 'Show in',
    'row_settings.type_color' => 'Color',
    'row_settings.vertical_align_class' => 'Content alignment',
    'selectedProducts.label.please_add_products' => 'Please add products',
    'separator.description' => 'Line, dotted or dashed separator to separate different sections of your page',
    'separator.label.height' => 'Thickness (px)',
    'separator.label.style' => 'Separator style',
    'separator.label.width' => 'Width (%)',
    'separator.name' => 'Add Separator',
    'separator.position.center' => 'Center',
    'separator.position.left' => 'Left',
    'separator.position.right' => 'Right',
    'separator.style.dashed' => 'Dashed',
    'separator.style.dotted' => 'Dotted',
    'separator.style.double' => 'Double',
    'separator.style.solid' => 'Solid',
    'showcase.description' => 'Showcase categories or vendors with their logos or images, names and descriptions.',
    'showcase.name' => 'Category/Brand Showcases',
    'text-carousel.description' => 'Text Carousel is a widget where you can show multiple textarea fields',
    'text-carousel.name' => 'Text Carousel',
    'text.description' => 'Display a text edited by WYSIWYG editor. You can even add images or videos into the text area',
    'text.name' => 'Add Text',
    'title.description' => 'Add Titles to your page and choose from different headings types from H1 to H6',
    'title.label.tag' => 'Heading',
    'title.name' => 'Add Title',
    'user.account.help.orders_per_page' => 'Orders per page in account storefront section',
    'user.account.label.files_per_page' => 'Files per page',
    'user.account.label.orders_per_page' => 'Orders per page',
    'user.account.label.payments_per_page' => 'Payments per page',
    'user.account.ph.files_per_page' => 'Enter per page',
    'user.account.ph.payments_per_page' => 'Enter per page',
    'video.description' => 'Add YouTube, Vimeo or other video to your page',
    'video.label.autoplay' => 'Autoplay',
    'video.label.controls' => 'Controls',
    'video.label.loop' => 'Loop',
    'video.label.src' => 'Link/Embed code',
    'video.label.type' => 'Video type',
    'video.name' => 'Add Video',
    'yotpo.description' => 'With this widget you can automatically visualize Yotpo reviews in your store',
    'yotpo.name' => 'Yotpo reviews',
];
