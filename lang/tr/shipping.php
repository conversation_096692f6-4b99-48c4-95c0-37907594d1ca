<?php

return [
    'action.add' => 'Kargo yöntemi ekleyin',
    'action.delete' => 'Kargo yöntemi silin',
    'action.depending_on_marketplace' => '',
    'action.depending_on_price' => 'Fiyat bağlı olarak',
    'action.depending_on_weight' => 'Ağırlığa bağlı olarak',
    'addon.insurance_percent' => '%',
    'back_documents' => 'Geri belge isteği',
    'back_receipt' => 'Geri alındı bilgisi isteği',
    'cd_agreement' => 'Eğer teslimatta nakit şeklinde ise sigorta ekleyin',
    'cd_agreement_num' => 'Anlaşma',
    'cd_max' => '',
    'confirm.delete' => 'Nakliye yöntemini silmek istediğinizden emin misiniz?',
    'dc' => 'Sipariş kabulüne ilişkin makbuz hizmeti servisi eklendi <i>(gönderim fiyatını artırabilir)</i>',
    'dc_cp' => 'Makubuz/stok kabulü mekbuz hizmeti eklendi',
    'delivery_day' => 'Sabit teslimat günü',
    'documents_shipment' => 'Belge gönderimi',
    'empty_default_package_type' => 'Varsayılan paket türü',
    'entry_default_weight' => 'Bir öğe için varsayılan ağırlık',
    'err.allowed_methods_international.required' => 'Lütfen en az bir hizmet seçin',
    'err.can_not_include_parent_and_child_regions' => 'Nakliye yöntemi, ana ve alt bölgeleri içeremez!',
    'err.cannot_have_more_than_%1$s_rates_for_a_single_provider' => 'Nakliye yöntemi oranları aşıldı(%1$s)',
    'err.cd_agreement_num.required' => 'Lütfen bir anlaşma seçin',
    'err.city.required' => 'Lütfen bir şehir seçin',
    'err.connect' => 'Speedy sunucusu ile bağlantı kurulurken bir hata yaşandı',
    'err.country.required' => 'Lütfen bir ülke seçin',
    'err.default_weight.required' => 'Lütfen varsayılan ağırlık için bir değer ekleyin',
    'err.firm.required' => 'Şirket adı zorunludur',
    'err.firm.required_without_to_door' => 'Speedy ofisi',
    'err.firm.required_without_to_office' => 'Lütfen teslimat yöntemini seçin. Örneğin, ADRESE TESLİM',
    'err.free.not_in_services' => 'Bu hizmet aktif değil. Lütfen üst kısımdan bir hizmet seçin.',
    'err.incorrect_user_and_pass' => 'Yanlış kullanıcı adı veya parola',
    'err.invalid_address' => 'Geçersiz adres',
    'err.invalid_provider' => 'Geçersiz gönderim yöntemi!',
    'err.invalid_target' => 'Geçersiz gönderim yöntemi hedefi!',
    'err.invalid_type' => 'Geçersiz gönderim yöntemi türü!',
    'err.max_regions_allowed_is_%1$s' => 'Maksimum bölge aşıldı(%1$s)',
    'err.name.required' => 'Sorumlu kişinin adı',
    'err.name_max_chars_%1$s' => 'Gönderim yöntemi adı uzunluğu aşıldı(%1$s)',
    'err.name_required' => 'Gönderim yöntemi adı gerekiyor',
    'err.no_longer_exists' => 'Gönderim yöntemi artık mevcut değil',
    'err.not_installed' => 'Bu uygulamalar yüklenmedi',
    'err.office.not_exists' => '',
    'err.office.required' => '',
    'err.office_max_dimensions' => '',
    'err.office_max_weight' => '',
    'err.password.required' => 'Şifre gereklidir',
    'err.phone.required' => 'Telefon numarası gerekli',
    'err.post_code.required' => '',
    'err.price_from_must_be_less_than_price_to' => 'Fiyat, her bir gönderim yöntemi için belirlenmiş fiyattan düşük olan olmalı!',
    'err.provider_name.max' => 'İsim, 255 simgeden uzun olamaz',
    'err.provider_name.required' => 'İsim gereklidir',
    'err.range_overlap_existing' => 'Gönderim metodu aralığı, mevcut olan ile çakışıyor',
    'err.rates_required' => 'Gönderim yöntemi fiyatları gerekli',
    'err.region.in' => '',
    'err.region.required' => '',
    'err.some_regions_no_longer_exists' => 'Bazı bölgeler artık mevcut değil',
    'err.unable_to_connect' => 'Speedy sunucusuna bağlanılamadı',
    'err.unable_to_connect_to_service' => 'Hizmete bağlanılamıyor',
    'err.username.required' => 'Kullanıcı adı gerekiyor',
    'err.weight_from_must_be_less_than_weight_to' => 'Gönderim yöntemi ağırlık fiyatı, ağırlıktan daha az olmalı',
    'err.weight_from_must_start_from_largest_previous_weight_to' => 'Gönderim yöntemi ağırlık fiyatı, en büyük önceki ağırlıkdan başlamalı',
    'err.weight_to_must_end_at_smallest_previous_weight_from' => 'Gönderim yöntemi oranı ağırlığı, en küçük önceki ağırlıkta bitmelidir',
    'fragile_shipment' => 'Paket kırılabilir ürün içeriyor',
    'free' => 'Ücretsiz kargo',
    'free_method_city' => 'Ücretsiz şehir teslimatı için bir servis seçin',
    'free_method_intercity' => 'Şehirler arası ulaşım için bir servis seçin',
    'free_method_international' => 'Uluslararası dağıtım için bir servis seçin',
    'free_shipping_min_amount' => 'Ücretsiz uluslararası dağıtımda Minimum tutar için bir servis seçin',
    'generate_bill_of_lading' => 'Konşimento üreti',
    'header.add' => 'Gönderim yöntemi ekleyin',
    'header.add_rates' => 'Gönderim ücretleri ekleyin',
    'header.edit_rates' => 'Gönderim ücretlerini düzenle',
    'header.providers' => 'Gönderim yöntemleri',
    'help.additional_settings' => 'Additional settings',
    'help.depending_on_marketplace' => '',
    'help.depending_on_price' => 'Bu gönderim yöntemi fiyatları, siparişin alt toplamına bağlı olacaktır',
    'help.depending_on_weight' => 'Bu gönderim yöntemi fiyatları siparişin toplam ağırlığına bağlı olacaktır',
    'help.insurance' => 'Nakliye yöntemi sigortaya izin veriyor mu?',
    'help.label_size' => '',
    'help.provider_description' => 'Ödeme işlemi sırasında, gönderim yöntemiyle ilgili kısa bir açıklama mağazanızdan alışveriş yapan müşteriler için görünür olabilir.',
    'help.provider_image' => 'Gönderim yöntemi logosu',
    'help.provider_name' => 'Örneğin DHL, FedEx, TNT',
    'help.shipping_add_choose' => 'Gönderim yöntemi türünü seçiniz. Bir tür seçildikten sonra bunu tekrar değiştiremezsiniz',
    'help.sync_payments' => 'Kapıda ödemede kargo firmasından bilgi alıdığımızda sipariş durumunu otomatik olarak ödendi olarak ayarla.',
    'help.target' => 'Bu kargo firması ile çalışacağınız bölgeyi seçin.',
    'instruction_returns' => 'Sipariş iptal edilirse, iade bedeli satıcı tarafından ödenir',
    'instruction_shipping_returns' => 'Sipariş iptal edilirse, teslimat ve iade masrafı satıcı tarafından ödenir.',
    'integration.none' => 'Entegrasyon olmadan',
    'inventory' => 'Envanter',
    'inventory_type' => 'Envanter türü',
    'inventory_type_digital' => 'Dijital ürün',
    'inventory_type_loading' => 'Yükleniyor',
    'invoice_before_cd' => 'Kapıda ödeme hizmeti öncesi faturanın teslim edilmesi eklendi. <i>(may Gönderim ücretini arttırabilir)</i>',
    'label.add_new_rate' => 'Ücret ekle',
    'label.cost' => 'Maliyet',
    'label.insurance' => 'Fatura',
    'label.provider_description' => 'Açıklama',
    'label.provider_image' => 'Kargo yöntemi logosu',
    'label.provider_name' => 'İsim',
    'label.regions' => 'Gönderi bölgeleri',
    'label.target' => '',
    'label.the_whole_world' => 'Genel yap',
    'limited_by_provider' => 'Kargo servis operatörü tarafından sınırlandırılmıştır',
    'listing.marketplace_based' => '',
    'listing.price_based' => 'Fiyat temelli',
    'listing.weight_based' => 'Ağırlık temelli',
    'mark_as_unfulfilled' => 'Tamamlanmamış olarak işaretle',
    'marketplace' => '',
    'notify.no_records_help' => 'Gönderim yöntemleriyle ilgili sorun mu yaşıyorsunuz? Aşağıdaki bağlantıyı takip edin.',
    'notify.no_records_help_link' => 'Gönderim yöntemleri için yardım',
    'notify.no_records_info' => 'Gönderim yöntemleriniz burada gösterilecek',
    'notify.no_records_yet' => 'Henüz bir gönderi yöntemi eklemediniz',
    'oc' => 'Beyan edilen değer',
    'oc.tip' => 'Beyan edilen değer, siparişten sorumlu olduğunuz tutardır',
    'oc_total' => 'Beyan edilen değer üzerinden',
    'open_before_payment' => 'Ödeme öncesi aç',
    'options_before_payment' => 'Ödeme öncesi seçenekleri',
    'pack_count' => 'Paket sayısı',
    'package_type' => 'Bir metin girin',
    'ph.first_name' => 'İsim',
    'ph.free' => 'Ücretsiz kargo',
    'ph.insurance' => 'Sigorta yüzdesini giriniz',
    'ph.last_name' => 'Soyisim',
    'ph.phone' => 'Telefon numarası',
    'ph.provider_description' => 'Gönderi yöntemi açıklaması girin',
    'ph.provider_name' => 'Gönderi yönteminin adını girin',
    'pricing' => 'Teslimat fiyatlaması',
    'priority' => 'Öncelikli saat',
    'priority_time' => 'Öncelikli zaman',
    'processing' => 'Lütfen bekleyin...',
    'processing_fee' => 'Hizmet bedeli',
    'recalculate' => 'Recalculate',
    'rest_of_the_world' => 'Global',
    'return_loading' => 'Paketi geri getir',
    'shipping_fixed_time' => 'Müşterilerin teslimat için zaman seçmesine izin ver',
    'shipping_to_door' => 'Adrese teslim',
    'shipping_to_office' => 'Kargo ofisinden teslim',
    'side' => 'Kargo ücretini kim öder',
    'side_receiver' => 'Alıcı',
    'side_sender' => 'Gönderici',
    'sms' => 'SMS bildirimlerini etkinleştir',
    'sms_no' => 'SMS bildirimleri için cep telefonu numaranız',
    'speedy.error.calculation_result_invalid' => 'Bu hesaplamanın sonucu geçersiz',
    'speedy.error.calculation_result_malformed' => 'Bu hesaplamanın sonucu hatalı biçimlendirilmiştir.',
    'succ.add' => 'Gönderim yöntemi başarıyla etkinleştirildi',
    'succ.edit' => 'Gönderim yöntemi başarıyla düzenlendi',
    'succ.populate_successful' => 'Veri başarıyla oluşturuldu',
    'successfully_saved_settings' => 'Ayarlar başarıyla kaydedildi',
    'sync_payments' => 'Ödeme durumunu otomatik olarak ücretli şeklinde ayarla',
    'test_before_payment' => 'Ödeme öncesi test edin',
    'th.order_rate' => 'Sipariş oranı',
    'th.provider' => 'Gönderi yöntemi',
    'th.ships_to' => 'Kargola',
    'th.value' => 'Değer',
    'tip.cd_max' => '',
    'tip.entry_default_package_type' => 'Varsayılan paket türünü ekle. Örneğin: Kutu',
    'tip.entry_default_weight' => 'Varsayılan bir ağırlık ekle',
    'tip.free_international_serice' => 'Ücretsiz nakliye için uluslararası bir gönderim hizmeti seçin',
    'tip.order_rate_boundry_explain' => 'Değer',
    'tip.rate_value' => 'Herhangi bir değer yazmazsanız gönderiniz ücretsizdir',
    'to' => 'için',
    'to_door' => 'Adrese teslim',
    'to_office' => 'Kargo ofisinden teslim',
    'waybill.acceptance.title' => 'Kargoyu teslim almaya ilişkin ayarlar',
    'waybill.btn.test' => 'Birleştir',
    'waybill.button.print_label' => 'Etiket baskı makinesi',
    'waybill.checking.title' => 'İnceleme ve test etme',
    'waybill.decline.payer.delivery' => 'Reddedilme durumunda kargonun kim tarafından ödeneceği',
    'waybill.decline.payer.return' => 'Reddilme durumunda geri ödeme',
    'waybill.decline.title' => 'Reddedilen Teslimat',
    'waybill.error.name' => 'İsim gereklidir',
    'waybill.error.no_api_response' => 'CloudCart API sından yanıt yok',
    'waybill.error.office_code' => 'Seçilen ofis geçersiz',
    'waybill.error.order_not_found' => 'Sipariş bulunamadı.',
    'waybill.error.password' => 'Parola gereklidir',
    'waybill.error.phone' => 'Telefon numarası gereklidir',
    'waybill.error.pickup' => 'Bir seçim yapılması gereklidir - Lütfen, adresinizde veya kargo ofisinden teslim almayı seçiniz',
    'waybill.error.settlement' => 'Şehir gereklidir',
    'waybill.error.shipping_not_found' => 'Kargo bulunamadı',
    'waybill.error.street' => 'Cadde gereklidir',
    'waybill.error.street_num' => 'Cadde numarası gereklidir',
    'waybill.error.username' => 'Kullanıcı adı gereklidir',
    'waybill.help.password' => 'Kargo sağlayıcı hizmetine erişim için şifre',
    'waybill.help.session' => 'Test başarılı bir şekilde yapılırsa bu alan otomatik olarak dolacaktır.',
    'waybill.help.types' => 'Burada müşterilerinize sunmak istediğiniz hizmetleri seçebilirsiniz.',
    'waybill.help.username' => 'Kargo sağlayıcı hizmetine erişmek için kullanıcı adı',
    'waybill.info.contents' => '',
    'waybill.injunction.title' => 'İlave ayarlar',
    'waybill.instruction' => '',
    'waybill.label.address_from' => 'Müşteri adresi',
    'waybill.label.cod' => '',
    'waybill.label.contents' => '',
    'waybill.label.depth' => '',
    'waybill.label.height' => '',
    'waybill.label.office_from' => 'Ofis',
    'waybill.label.password' => 'Şifre',
    'waybill.label.session' => 'Oturum',
    'waybill.label.total' => '',
    'waybill.label.username' => 'Kullanıcı adı',
    'waybill.label.weight' => '',
    'waybill.label.width' => '',
    'waybill.origin.title' => 'Kargo alınmış olmalıdır',
    'waybill.package_number' => 'Paket numarası',
    'waybill.pay_after_accept' => 'Teslim aldıktan sonra ödeme',
    'waybill.pay_after_test' => 'Test ettikten sonra ödeme',
    'waybill.ph.cd_agreement_num' => 'Sözleşme No',
    'waybill.ph.company' => '',
    'waybill.ph.contents' => '',
    'waybill.ph.countries' => 'Ülkeler',
    'waybill.ph.firm' => 'Firma',
    'waybill.ph.key_word' => '',
    'waybill.ph.name' => 'Tam ad',
    'waybill.ph.oc_total' => 'Miktar ekle',
    'waybill.ph.office' => 'Ofis',
    'waybill.ph.pack_count' => 'Paket sayısı',
    'waybill.ph.password' => 'Kargo firması e-hizmetine erişmek için şifre',
    'waybill.ph.phone' => 'Telefon',
    'waybill.ph.post_code' => '',
    'waybill.ph.quarter' => 'Çeyrek',
    'waybill.ph.session' => 'Oturum',
    'waybill.ph.settlement' => 'Şehir',
    'waybill.ph.sms_no' => 'Cep telefon numarası',
    'waybill.ph.street' => 'Bir cadde seçin',
    'waybill.ph.street_ap' => 'Apartman numarası',
    'waybill.ph.street_bl' => 'Bina',
    'waybill.ph.street_et' => 'Kat',
    'waybill.ph.street_num' => 'Cadde numarası',
    'waybill.ph.street_other' => 'Ek adres bilgisi',
    'waybill.ph.street_vh' => 'Giriş',
    'waybill.ph.username' => 'Kargo firması e-hizmetine erişmek için kullanıcı adı',
    'waybill.send_date' => 'Bir gönderme tarihi belirtin',
    'waybill.sender.title' => 'Gönderen Verileri',
    'waybill.test_successful' => 'Bağlantı başarıyla test edildi.',
    'waybill.type.title' => 'Hizmet türü',
    'waybill.types.title' => 'Hizmet türleri',
];
