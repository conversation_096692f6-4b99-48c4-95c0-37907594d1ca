<?php

return [
    'err.%1$s_attribute_%2$s_requires_%3$s_parameters' => '%1$s attribute \'%2$s\' requires %3$s parameters.',
    'err.%1$s_attribute_callback_%2$s_is_not_callable' => '%1$s attribute \'callback\': %2$s is not callable.',
    'err.%1$s_attribute_callback_allows_max_%2$s_parameters' => '%1$s attribute \'callback\' allows max %2$s parameters.',
    'err.%1$s_attribute_callback_has_empty_callback' => '%1$s attribute \'callback\' has an empty callback supplied.',
    'err.%1$s_attribute_char_requires_first_parameter_to_be_char' => 'The %1$s attribute \'char\' requires the first parameter to be a string value.',
    'err.%1$s_attribute_char_requires_second_parameter_to_be_char' => 'The %1$s attribute \'char\' requires the second parameter to be a string value.',
    'err.%1$s_attribute_in_requires_at_least_2_parameters' => 'The %1$s attribute \'in\' requires the second parameter to be a string value.',
    'err.%1$s_attribute_int_requires_first_parameter_to_be_digit' => 'The %1$s attribute \'int\' requires the first parameter to be a digit.',
    'err.%1$s_attribute_int_requires_second_parameter_to_be_digit' => 'The %1$s attribute \'int\' requires the second parameter to be a digit.',
    'err.%1$s_attribute_invalid' => 'The %1$s attribute is invalid.',
    'err.%1$s_attribute_is_invalid_url' => '%1$s is not a valid URL.',
    'err.%1$s_attribute_regex_%2$s_does_not_exist' => '%1$s attribute \'regex\': `%2$s` does not exist.',
    'err.%1$s_elements_should_be_more_than_%2$s' => '%1$s should contain more than %2$s elements.',
    'err.%1$s_elements_should_be_no_more_than_%2$s' => '%1$s should not contain more than %2$s elements.',
    'err.%1$s_invalid_value' => '%1$s has an invalid value.',
    'err.%1$s_is_not_array' => '%1$s should be an array.',
    'err.%1$s_not_digit' => '%1$s must be a digit.',
    'err.%1$s_required' => '%1$s is required.',
    'err.%1$s_should_be_longer_than_%2$s' => '%1$s should be longer than %2$s.',
    'err.%1$s_should_be_more_than_%2$s' => '%1$s should be more than %2$s.',
    'err.%1$s_should_be_no_longer_than_%2$s' => '%1$s should not be longer than %2$s.',
    'err.%1$s_should_be_no_more_than_%2$s' => '%1$s should not be more than %2$s.',
];
