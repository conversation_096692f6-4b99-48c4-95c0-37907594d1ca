<?php

return [
    'err.authorize.app_login_key_required' => 'Autorizza: è richiesto il codice di accesso dell’app.',
    'err.authorize.transaction_key_required' => 'Autorizza: è richiesto il codice della transazione.',
    'err.borica.cannot_change_mode_missing_cert_or_private_key' => 'Borica: non è possibile cambiare in modalità live se non tutti i certificati sono convalidati',
    'err.borica.invalid.certificate_etlog' => 'Borica: certificato eTLog non valido',
    'err.borica.invalid.certificate_real' => 'Borica: certificato reale non valido',
    'err.borica.invalid.certificate_test' => 'Borica: certificato di prova non valido',
    'err.borica.invalid.commonName' => 'Borica: nome comune non valido',
    'err.borica.invalid.countryName' => 'Borica: nome del paese non valido',
    'err.borica.invalid.emailAddress' => 'Borica: indirizzo email non valido',
    'err.borica.invalid.localityName' => 'Borica: nome della località non valido',
    'err.borica.invalid.organizationName' => 'Borica: nome dell’organizzazione non valido',
    'err.borica.invalid.organizationalUnitName' => 'Borica: nome dell’unità organizzativa non valido',
    'err.borica.invalid.stateOrProvinceName' => 'Borica: nome dello stato o della provincia non valido',
    'err.borica.invalid.terminal_id' => 'Borica: ID terminale non valido',
    'err.borica.not_match.certificate_etlog' => 'Borica: la chiave privata eTLog e il certificato non corrispondono',
    'err.borica.not_match.certificate_real' => 'Borica: la chiave privata reale e il certificato non corrispondono',
    'err.borica.not_match.certificate_test' => 'Borica: la chiave privata di prova e il certificato non corrispondono',
    'err.bwt.description_max_length_is_%1$s' => 'Bonifico bancario: la descrizione deve essere inferiore a %1$s caratteri.',
    'err.bwt.description_required' => 'Bonifico bancario: la descrizione è obbligatoria.',
    'err.cod.description_max_length_is_%1$s' => 'Contrassegno: la descrizione deve essere inferiore a %1$s caratteri.',
    'err.cod.description_required' => 'Contrassegno: la descrizione è obbligatoria.',
    'err.epay.kin_required' => 'EPay: KIN è richiesto.',
    'err.epay.secret_required' => 'EPay: è richiesto il segreto.',
    'err.min_price_required' => 'È richiesto un prezzo minimo',
    'err.paypal.merchant_email_required' => 'PayPal: è richiesto l’email del commerciante.',
    'err.percent_per_month_required' => 'È richiesta la percentuale mensile',
    'err.skrill.api_password_required' => 'Skrill: è richiesta la password API.',
    'err.skrill.email_required' => 'Skrill: è richiesta l’email.',
    'err.skrill.merchant_id_required' => 'Skrill: è richiesto l’ID commerciante.',
    'err.skrill.signature_hash_algorithm_required' => 'Skrill: è richiesto l’algoritmo di hash della firma.',
    'err.skrill.signature_secret_required' => 'Skrill: è richiesto il segreto della firma.',
    'err.tbi.email_invalid' => 'L’email inserita non è valida.',
    'err.tbi.email_required' => 'L’email è obbligatoria.',
    'err.tbi.id_required' => 'Il numero dell’accordo è obbligatorio.',
    'err.tbi.max_required' => 'Il periodo massimo è obbligatorio.',
    'err.tbi.min_required' => 'Il periodo minimo è obbligatorio.',
    'err.tbi.step_required' => 'È richiesto l’intervallo di passaggio.',
    'err.title_max_length_is_%1$s' => 'Il titolo deve essere inferiore a %1$s caratteri.',
    'err.title_required' => 'Il titolo è obbligatorio.',
    'err.voucher.description_max_length_is_%1$s' => 'Voucher: la descrizione deve essere inferiore a %1$s caratteri.',
    'err.voucher.description_required' => 'Voucher: la descrizione è obbligatoria.',
    'name_' => '',
];
