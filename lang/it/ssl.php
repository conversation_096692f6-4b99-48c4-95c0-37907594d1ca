<?php

return [
    'action.auto' => 'Installazione automatica',
    'action.generate' => 'Genera',
    'action.install' => 'Installa',
    'action.manual' => 'Installazione manuale',
    'action.regenerate' => 'Rigenera',
    'confirm.generate' => 'Stai per generare un nuovo certificato SSL. Quello precedente andrà perso. Considera di annullare questa operazione se hai inviato la richiesta di firma del certificato con la richiesta del certificato attuale. Usa questa opzione per rigenerare completamente il certificato SSL esistente.',
    'confirm.install' => 'Stai per installare un nuovo certificato SSL. Quello precedente andrà perso.',
    'error.ca_invalid' => 'Autorità di Certificazione/CERTIFICATO IN CATENA',
    'error.cert_not_found' => 'Certificato non trovato',
    'error.common_invalid' => 'Comune non valido',
    'error.country_invalid' => 'Paese non valido',
    'error.crt_invalid' => 'Certificato non valido applicato',
    'error.csr_failed' => 'Certificato fallito',
    'error.email_invalid' => 'Email non valida',
    'error.export_failed' => 'Esportazione fallita',
    'error.host_id_invalid' => 'ID host non valido',
    'error.host_invalid' => 'Host non valido',
    'error.invalid_request' => 'Richiesta non valida',
    'error.keys_not_match' => 'CSR, Chiave Privata e Certificato non corrispondono',
    'error.organization_invalid' => 'Organizzazione non valida',
    'error.pk_failed' => 'Generazione della chiave privata fallita',
    'error.state_required' => 'Stato richiesto',
    'form.ph.ca' => 'Inserisci il certificato in catena qui',
    'form.ph.common' => 'Nome Comune (CN)',
    'form.ph.country' => 'Paese (C)',
    'form.ph.crt' => 'Certificato',
    'form.ph.csr' => 'Richiesta di Certificato',
    'form.ph.email' => 'Indirizzo Email',
    'form.ph.locality' => 'Località (L)',
    'form.ph.organization' => 'Organizzazione (O)',
    'form.ph.private' => '',
    'form.ph.state' => 'Nome dello Stato o della Provincia (ST)',
    'form.ph.unit' => '',
    'header.add' => 'Aggiunta',
    'header.certificates' => 'Certificati SSL',
    'heading.domain_details' => 'Dettagli del Dominio',
    'heading.install' => 'Installa',
    'heading.request' => 'Richiesta di Certificato',
    'help.app_required' => 'Questa azione richiede l&#39;applicazione <b>:app</b>!',
    'help.domain_details' => 'Dettagli delle impostazioni di base',
    'help.install' => 'Info - Certificato',
    'help.install_chained' => 'Inserisci il tuo Certificato in Catena qui',
    'help.private' => 'Chiave privata',
    'help.request' => 'Info sulla Richiesta di Certificato',
    'lets_encrypt_link' => 'Puoi installare certificati SSL gratuiti dalla sezione <a href=":route">Domini</a>',
    'success.generate' => 'Generazione riuscita!',
    'success.install' => 'Installazione riuscita!',
];
