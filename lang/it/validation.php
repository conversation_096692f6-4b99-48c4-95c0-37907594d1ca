<?php

return [
    'accepted' => 'Il :attribute deve essere accettato.',
    'active_url' => 'Il :attribute non è un URL valido.',
    'after' => 'Il :attribute deve essere una data successiva a :date.',
    'after_or_equal' => 'Il :attribute deve essere una data successiva o uguale a :date.',
    'alpha' => 'Il :attribute può contenere solo lettere.',
    'alpha_dash' => 'Il :attribute può contenere solo lettere, numeri e trattini.',
    'alpha_num' => 'Il :attribute può contenere solo lettere e numeri.',
    'array' => 'Il :attribute deve essere un array.',
    'before' => 'Il :attribute deve essere una data precedente a :date.',
    'before_or_equal' => 'Il :attribute deve essere una data precedente o uguale a :date.',
    'between.numeric' => 'Il :attribute deve essere compreso tra :min e :max.',
    'between.file' => 'Il :attribute deve essere compreso tra :min e :max kilobyte.',
    'between.string' => 'Il :attribute deve essere compreso tra :min e :max caratteri.',
    'between.array' => 'Il :attribute deve avere tra :min e :max elementi.',
    'boolean' => 'Il campo :attribute deve essere vero o falso.',
    'confirmed' => 'La conferma di :attribute non corrisponde.',
    'custom.attribute-name.rule-name' => 'Messaggio personalizzato',
    'date' => 'Il :attribute non è una data valida.',
    'date_format' => 'Il :attribute non corrisponde al formato :format.',
    'different' => 'Il :attribute e :other devono essere diversi.',
    'digits' => 'Il :attribute deve avere :digits cifre.',
    'digits_between' => 'Il :attribute deve avere tra :min e :max cifre.',
    'dimensions' => 'Il :attribute ha dimensioni di immagine non valide.',
    'distinct' => 'Il campo :attribute ha un valore duplicato.',
    'email' => 'Il :attribute deve essere un indirizzo email valido.',
    'exists' => 'Il :attribute selezionato non è valido.',
    'file' => 'Il :attribute deve essere un file.',
    'filled' => 'Il campo :attribute è obbligatorio.',
    'gt.numeric' => 'Il :attribute deve essere maggiore di :value.',
    'gt.file' => 'Il :attribute deve essere maggiore di :value kilobyte.',
    'gt.string' => 'Il :attribute deve essere maggiore di :value caratteri.',
    'gt.array' => 'Il :attribute deve avere più di :value elementi.',
    'gte.numeric' => 'Il :attribute deve essere maggiore o uguale a :value.',
    'gte.file' => 'Il :attribute deve essere maggiore o uguale a :value kilobyte.',
    'gte.string' => 'Il :attribute deve essere maggiore o uguale a :value caratteri.',
    'gte.array' => 'Il :attribute deve avere :value elementi o più.',
    'hex_color' => 'Il formato :attribute è non valido.',
    'image' => 'Il :attribute deve essere un immagine.',
    'in' => 'Il :attribute selezionato non è valido.',
    'in_array' => 'Il campo :attribute non esiste in :other.',
    'integer' => 'Il :attribute deve essere un numero intero.',
    'ip' => 'Il :attribute deve essere un indirizzo IP valido.',
    'json' => 'Il :attribute deve essere una stringa JSON valida.',
    'lt.numeric' => 'Il :attribute deve essere inferiore a :value.',
    'lt.file' => 'Il :attribute deve essere inferiore a :value kilobyte.',
    'lt.string' => 'Il :attribute deve essere inferiore a :value caratteri.',
    'lt.array' => 'Il :attribute deve avere meno di :value elementi.',
    'lte.numeric' => 'Il :attribute deve essere minore o uguale a :value.',
    'lte.file' => 'Il :attribute deve essere minore o uguale a :value kilobyte.',
    'lte.string' => 'Il :attribute deve essere minore o uguale a :value caratteri.',
    'lte.array' => 'Il :attribute non deve avere più di :value elementi.',
    'max.numeric' => 'Il :attribute non può essere maggiore di :max.',
    'max.file' => 'Il :attribute non può essere maggiore di :max kilobyte.',
    'max.string' => 'Il :attribute non può essere maggiore di :max caratteri.',
    'max.array' => 'Il :attribute non può avere più di :max elementi.',
    'mimes' => 'Il :attribute deve essere un file di tipo: :values.',
    'mimetypes' => 'Il :attribute deve essere un file di tipo: :values.',
    'min.numeric' => 'Il :attribute deve essere almeno :min.',
    'min.file' => 'Il :attribute deve essere almeno :min kilobyte.',
    'min.string' => 'Il :attribute deve essere almeno :min caratteri.',
    'min.array' => 'Il :attribute deve avere almeno :min elementi.',
    'not_in' => 'Il :attribute selezionato non è valido.',
    'numeric' => 'Il :attribute deve essere un numero.',
    'phone' => 'Il campo :attribute contiene un numero non valido.',
    'present' => 'Il campo :attribute deve essere presente.',
    'regex' => 'Il formato :attribute non è valido.',
    'required' => 'Il campo :attribute è obbligatorio.',
    'required_field' => 'Il campo è obbligatorio.',
    'required_if' => 'Il campo :attribute è obbligatorio quando :other è :value.',
    'required_unless' => 'Il campo :attribute è obbligatorio a meno che :other non sia in :values.',
    'required_with' => 'Il campo :attribute è obbligatorio quando :values è presente.',
    'required_with_all' => 'Il campo :attribute è obbligatorio quando :values è presente.',
    'required_without' => 'Il campo :attribute è obbligatorio quando :values non è presente.',
    'required_without_all' => 'Il campo :attribute è obbligatorio quando nessuno di :values è presente.',
    'same' => 'Il :attribute e :other devono corrispondere.',
    'size.numeric' => 'Il :attribute deve essere :size.',
    'size.file' => 'Il :attribute deve essere :size kilobyte.',
    'size.string' => 'Il :attribute deve essere :size caratteri.',
    'size.array' => 'Il :attribute deve contenere :size elementi.',
    'string' => 'Il :attribute deve essere una stringa.',
    'timezone' => 'Il :attribute deve essere una zona valida.',
    'unique' => 'Il :attribute è già stato preso.',
    'uploaded' => 'Il :attribute non è riuscito a caricare.',
    'url' => 'Il formato :attribute non è valido.',
    'latitude' => 'Il :attribute contiene una latitudine non valida.',
    'longitude' => 'Il :attribute contiene una longitudine non valida.',
    'header' => 'I dati forniti non erano validi.',
];
