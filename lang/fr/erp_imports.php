<?php

return [
    'action.create' => 'Créer des produits manquants et mettre à jour les produits existants',
    'action.update' => 'Mettre à jour uniquement les produits existants',
    'label.action' => 'Action',
    'label.compare_by' => 'Comparer par',
    'label.compare_by.nothing' => 'Rien',
    'label.compare_by.official_id' => 'ID officiel',
    'label.continue_selling' => 'Continuer à vendre',
    'label.images' => 'Importer des images',
    'label.price_field' => 'Champ prix',
    'label.publish_as_active' => 'Publier les produits importés',
    'label.publish_as_featured' => 'Publier les produits comme vedettes',
    'label.publish_as_new' => 'Publier les produits comme nouveaux',
    'label.quantity_tracking' => 'La quantité de chaque produit importé doit être suivie',
    'label.require_shipping' => 'Tous les produits importés nécessitent une expédition',
    'label.update_fields' => 'Champs à mettre à jour lors d’un changement dans l’ERP',
    'tip.action' => 'Action à appliquer aux produits entrants',
    'tip.compare_by' => 'Les produits seront comparés par le paramètre que tu choisis',
    'tip.price_field' => 'Champ à partir duquel le prix sera pris et vers lequel le prix sera envoyé',
    'label.simple' => 'Sans variations',
    'tip.simple' => 'Si le produit n’a qu’une seule variante, crée/mis à jour comme un produit sans variantes',
    'label.ten_minute_update' => 'Mise à jour dans 10 minutes',
    'tip.ten_minute_update' => 'Les modifications apportées à Gensoft seront reflétées dans la boutique toutes les 10 minutes',
    'feature.ten_minute_update' => 'Le service de mise à jour de 10 minutes a été payé',
    'paid_option' => 'Service payant',
    'label.compare_by.external_id' => 'Leur identifiant',
];
