<?php

return [
    'action.add' => 'Ajouter une nouvelle zone géographique',
    'btn.add_zone' => 'Ajouter un nouvel État, Pays ou Polygone',
    'confirm.delete' => 'La suppression d une zone géographique peut causer des problèmes lors de la finalisation d une commande par vos utilisateurs',
    'err.administrative_area_level_1_required' => 'Tu n as pas les permissions pour accéder à cette section',
    'err.invalid_operation_key' => 'Clé d opération incorrecte',
    'header.add' => 'Ajouter une nouvelle zone géographique',
    'header.edit' => 'Modifier',
    'header.geo_zones' => 'Zones géographiques',
    'label.operation' => 'opération',
    'label.title' => 'Nom de la zone géographique',
    'label.value_name' => 'Tapez un État ou un Pays',
    'label.value_polygon' => 'Tapez le polygone',
    'label.values' => 'État ou pays',
    'label.post_code' => 'codes postaux',
    'name' => 'Nom',
    'notify.no_records_help' => 'Besoin d aide pour mieux comprendre les zones géographiques ?',
    'notify.no_records_help_link' => 'En savoir plus sur les zones géographiques',
    'notify.no_records_info' => 'Avec les zones géographiques, tu peux définir les villes ou les pays où ton magasin en ligne opère. <br />Par défaut, si tu n ajoutes pas de zones géographiques, ton magasin est activé pour le monde entier. <br /><br />Si tu veux que ton magasin puisse accepter des commandes uniquement de pays spécifiques ou même de villes, alors tu devrais ajouter une zone géographique. <br /><br />Par exemple : Tu voudrais accepter des commandes uniquement des utilisateurs de Paris, France. <br />Tu devrais ajouter une zone géographique nommée France, puis définir que seuls les clients de Paris peuvent finaliser leurs commandes. <br /> Une fois que tu es prêt avec les zones géographiques, tu devrais aller chez les fournisseurs de livraison et sélectionner cette zone géographique. De cette manière, tu restreindras l accès aux utilisateurs en dehors de Paris.',
    'notify.no_records_yet' => 'Tu n as pas encore de zones géographiques',
    'operations.all' => 'Comprend',
    'operations.city' => 'Comprend uniquement pour la ville',
    'operations.in' => 'Comprend uniquement',
    'operations.not_city' => 'Comprend toutes les villes ou États sauf la ville',
    'operations.not_country' => 'Comprend tous les lieux sauf le pays',
    'operations.not_in' => 'Comprend tous les lieux sauf',
    'operations.not_street' => 'Comprend tous les quartiers sauf',
    'operations.street' => 'Comprend uniquement pour le quartier',
    'operations.polygon' => 'Comprend uniquement pour le polygone',
    'operations.distance' => 'Comprend uniquement pour la distance depuis un point',
    'operations.post_code' => 'Comprend uniquement les codes postaux du pays',
    'ph.geo_zone_title' => 'Ajoute le nom de la zone géographique. Exemple : Paris ou France',
    'values.ph.post_code' => 'Exemple : 1000....1999,80*,90*,ER9875',
    'total_values' => 'Total des zones géographiques',
    'values.ph.geo_name' => 'Commence à taper le nom de l État, du comté ou du pays',
    'error.post_code.range.numeric' => 'La valeur :value de :from doit être un entier.',
    'error.post_code.range.equal' => 'La valeur :value1 de :from doit être supérieure à :value2.',
    'values.help.post_code' => 'Codes postaux contenant un caractère générique (par exemple ER98*), des plages entièrement numériques (par exemple 1000....1999) ou correspondance complète (par exemple ER9875). Le champ peut contenir plusieurs valeurs séparées par des virgules'
];
