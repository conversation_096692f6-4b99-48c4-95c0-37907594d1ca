<?php

return [
    'err.%1$s_attribute_%2$s_requires_%3$s_parameters' => '%1$s l\'attribut \'%2$s\' nécessite %3$s paramètres.',
    'err.%1$s_attribute_callback_%2$s_is_not_callable' => '%1$s l\'attribut \'callback\': %2$s n\'est pas callable.',
    'err.%1$s_attribute_callback_allows_max_%2$s_parameters' => '%1$s l\'attribut \'callback\' permet un maximum de %2$s paramètres.',
    'err.%1$s_attribute_callback_has_empty_callback' => '%1$s l\'attribut \'callback\' a un callback vide fourni.',
    'err.%1$s_attribute_char_requires_first_parameter_to_be_char' => 'L\'attribut %1$s \'char\' nécessite que le premier paramètre soit une valeur de chaîne.',
    'err.%1$s_attribute_char_requires_second_parameter_to_be_char' => 'L\'attribut %1$s \'char\' nécessite que le deuxième paramètre soit une valeur de chaîne.',
    'err.%1$s_attribute_in_requires_at_least_2_parameters' => 'L\'attribut %1$s \'in\' nécessite que le deuxième paramètre soit une valeur de chaîne.',
    'err.%1$s_attribute_int_requires_first_parameter_to_be_digit' => 'L\'attribut %1$s \'int\' nécessite que le premier paramètre soit un chiffre.',
    'err.%1$s_attribute_int_requires_second_parameter_to_be_digit' => 'L\'attribut %1$s \'int\' nécessite que le deuxième paramètre soit un chiffre.',
    'err.%1$s_attribute_invalid' => 'L\'attribut %1$s est invalide.',
    'err.%1$s_attribute_is_invalid_url' => '%1$s n\'est pas une URL valide.',
    'err.%1$s_attribute_regex_%2$s_does_not_exist' => 'L\'attribut %1$s \'regex\': `%2$s` n\'existe pas.',
    'err.%1$s_elements_should_be_more_than_%2$s' => '%1$s devrait contenir plus de %2$s éléments.',
    'err.%1$s_elements_should_be_no_more_than_%2$s' => '%1$s ne devrait pas contenir plus de %2$s éléments.',
    'err.%1$s_invalid_value' => '%1$s a une valeur invalide.',
    'err.%1$s_is_not_array' => '%1$s devrait être un tableau.',
    'err.%1$s_not_digit' => '%1$s doit être un chiffre.',
    'err.%1$s_required' => '%1$s est requis.',
    'err.%1$s_should_be_longer_than_%2$s' => '%1$s devrait être plus long que %2$s.',
    'err.%1$s_should_be_more_than_%2$s' => '%1$s devrait être plus que %2$s.',
    'err.%1$s_should_be_no_longer_than_%2$s' => '%1$s ne devrait pas être plus long que %2$s.',
    'err.%1$s_should_be_no_more_than_%2$s' => '%1$s ne devrait pas être plus que %2$s.',
];
