<?php

return [
    'action.add' => 'Ajouter une adresse',
    'action.set_default' => 'Définir comme par défaut',
    'confirm.delete' => 'Êtes-vous sûr de vouloir supprimer cette adresse ?',
    'confirm.set_default' => 'Définir cette adresse comme une adresse par défaut ?',
    'err.cannot_delete_default' => 'L’adresse par défaut ne peut pas être supprimée',
    'err.cannot_have_more_than_%1$s' => 'Vous ne pouvez pas avoir plus de %1$s',
    'err.city_max_chars_%1$s' => 'Le nombre maximum de caractères autorisés est %1$s',
    'err.city_required' => 'Une ville est requise',
    'err.comapy_name_max_chars_%1$s' => 'Le nombre maximum de caractères autorisés pour le nom de l\'entreprise est de %1$s',
    'err.company_name_max_chars_%1$s' => 'Le nombre maximum de caractères autorisés pour le nom de la société est %1$s',
    'err.company_name_required' => 'Un nom de société est requis',
    'err.company_vat_max_chars_%1$s' => 'Le nombre maximum de caractères pour le numéro d’identification VAT de la société est %1$s',
    'err.company_vat_required_for_company' => 'Un numéro d’identification VAT de la société est requis',
    'err.first_name_max_chars_%1$s' => 'Le nombre maximum de caractères pour le prénom est %1$s',
    'err.first_name_required' => 'Le prénom est requis',
    'err.last_name_max_chars_%1$s' => 'Le nombre maximum de caractères pour le nom de famille est %1$s',
    'err.last_name_required' => 'Le nom de famille est requis',
    'err.locality_required' => '',
    'err.no_longer_exists' => 'Cette adresse n’existe plus',
    'err.note_administrator_max_chars_%1$s' => 'Le nombre maximum de caractères pour la note à l’administrateur est %1$s',
    'err.note_customer_max_chars_%1$s' => 'Le nombre maximum de caractères pour la note au client est %1$s',
    'err.phone_max_chars_%1$s' => 'Le nombre maximum de caractères pour le numéro de téléphone est %1$s',
    'err.phone_required' => 'Le numéro de téléphone est requis',
    'err.postal_code_max_chars_%1$s' => 'Le nombre maximum de caractères pour le code postal est %1$s',
    'err.postal_code_required' => 'Le code postal est requis',
    'err.region_does_not_exist' => 'La région n’existe pas.',
    'err.region_required' => 'La région est requise',
    'err.street_max_chars_%1$s' => 'Le nombre maximum de caractères pour la rue est %1$s',
    'err.street_required' => 'La rue est requise',
    'filter.is_company' => 'Adresse de l’entreprise',
    'header.add' => 'Créer une adresse',
    'header.edit' => 'Modifier',
    'help.address' => 'Tapez l’adresse ici. Vous pouvez taper votre code postal et le nom et le numéro de la rue',
    'help.address_additional_info' => 'Informations supplémentaires sur l’adresse',
    'help.address_other' => 'Informations supplémentaires concernant l’adresse',
    'help.administrator_note' => 'Le client ne verra PAS cette note',
    'help.city' => 'La ville de l’adresse',
    'help.company_bulstat' => '&nbsp;',
    'help.company_mol' => '&nbsp;',
    'help.company_name' => 'Indiquez le nom de la société ici',
    'help.company_vat' => 'Numéro d’identification de la taxe sur la valeur ajoutée de la société',
    'help.customer_note' => 'Note pour le client (le client verra cette note)',
    'help.latitude' => 'Latitude de l’adresse.',
    'help.longitude' => 'Longitude de l’adresse.',
    'help.phone' => 'Numéro de téléphone',
    'help.postal_code' => 'Code postal de la ville',
    'help.region' => 'Choisissez la région pour l’adresse de votre client',
    'help.street' => 'Ajoutez votre rue dans ce champ',
    'help.street_number' => 'Numéro de rue',
    'label.address' => 'Adresse',
    'label.address_other' => 'Informations supplémentaires',
    'label.administrator_note' => 'Note de l’administrateur',
    'label.city' => 'Ville',
    'label.company_bulstat' => 'Numéro d’enregistrement de la société',
    'label.company_mol' => 'Propriétaire de la société',
    'label.company_name' => 'Nom de la société',
    'label.company_vat' => 'Numéro d’identification VAT de la société',
    'label.country' => 'Pays',
    'label.customer_note' => 'Note pour le client',
    'label.first_name' => 'Prénom',
    'label.google_map_address' => 'Adresse Google Maps',
    'label.last_name' => 'Nom de famille',
    'label.latitude' => 'Latitude',
    'label.longitude' => 'Longitude',
    'label.phone' => 'Téléphone',
    'label.postal_code' => 'Code postal',
    'label.region' => 'Région',
    'label.state' => 'État',
    'label.street' => 'Adresse de la rue',
    'label.street_number' => '',
    'latitude' => 'Latitude',
    'listing.city' => 'Ville',
    'listing.default' => 'Adresse par défaut',
    'listing.details_edit' => 'Détails',
    'listing.tip_incomplete' => 'Cette adresse est incomplète. Soit la région n’existe pas, soit elle a des sous-régions.',
    'listing.zip' => 'CODE POSTAL :',
    'longitude' => 'Longitude',
    'ph.address' => 'Adresse',
    'ph.administrator_note' => 'Tapez un message à l’administrateur ici',
    'ph.city' => 'Ville',
    'ph.company_name' => 'Insérer le nom de la société',
    'ph.company_vat' => 'Insérer le numéro d’identification VAT',
    'ph.country' => 'Pays',
    'ph.customer_note' => 'La note pour le client va ici',
    'ph.first_name' => 'Par exemple. Michael',
    'ph.last_name' => 'Par exemple. Jordan',
    'ph.phone' => 'Insérer le numéro de téléphone',
    'ph.postal_code' => 'Insérer le code postal',
    'ph.state' => 'État',
    'ph.street' => 'Insérer la rue',
    'succ.add' => 'L’adresse a été ajoutée avec succès !',
    'succ.default_changed' => 'L’adresse par défaut a été changée avec succès !',
    'succ.edit' => 'L’adresse a été modifiée avec succès !',
    'th.address' => 'Adresse',
    'th.phone_zip' => 'Téléphone / code postal',
    'th.region' => 'Région',
    'th.type' => 'Type',
];
