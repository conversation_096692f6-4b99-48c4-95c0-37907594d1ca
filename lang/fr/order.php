<?php

return [
    'Archive' => 'Archive',
    'Barcode' => 'Code-barres',
    'Created' => 'Créé',
    'Discount' => 'Remise',
    'Free_shipping' => 'Livraison gratuite',
    'Guest' => 'Invité',
    'Manual_discount' => 'Remise manuelle',
    'Mark as completed' => 'Marquer comme terminé',
    'Marked_order_payment_as_received' => 'Paiement de la commande marqué comme reçu',
    'Message' => 'Message',
    'Order_added_with_id' => 'Commande ajoutée avec ID',
    'Order_customer_fields' => 'Champs client de la commande',
    'Order_status' => 'Statut de la commande',
    'Product' => 'Produit',
    'Product_name' => 'Nom du produit',
    'Product_price' => 'Prix du produit',
    'Product_quantity' => 'Quantité de produit',
    'SKU' => 'SKU',
    'Sale' => 'Vente',
    'Unarchive' => 'Déarchiver',
    'Vendor' => 'Marque',
    'Weight' => 'Poids',
    'abandoned' => 'Abandonné',
    'abandoned.confirm.delete' => 'Êtes-vous sûr de vouloir supprimer les commandes abandonnées sélectionnées ?',
    'abandoned_count' => 'Total des messages envoyés : :total',
    'action.abandoned_send_restore_link_subscriber' => 'Envoyer un email de restauration',
    'action.add_order' => 'Créer une commande',
    'action.add_order_discount' => 'Ajouter une remise sur la commande',
    'action.add_product_discount' => 'Ajouter une remise sur le produit',
    'action.archive' => 'Archiver',
    'action.cancel_order' => 'Annuler la commande',
    'action.cancel_payment' => 'Annuler le paiement',
    'action.choose_shipping_provider' => 'Choisissez la méthode d’expédition',
    'action.choose_status' => 'Choisir le statut',
    'action.complete' => 'Compléter',
    'action.create_credit_note' => 'Créer un avoir',
    'action.download_credit_note' => 'Télécharger l’avoir',
    'action.export' => 'Exporter',
    'action.fulfill_products' => 'Exécuter les produits',
    'action.history' => 'Historique',
    'action.mark_as_paid' => 'Marquer comme payé',
    'action.notify.credit_note_create_error' => 'Actuellement, nous ne pouvons pas créer d’avoir.',
    'action.notify.credit_note_created' => 'L’avoir a été créé.',
    'action.notify.credit_note_send_error' => 'Actuellement, nous ne pouvons pas envoyer l’avoir au client.',
    'action.notify.credit_note_sent' => 'L’avoir a été envoyé au client.',
    'action.payment_add' => 'Ajouter un paiement',
    'action.payment_authorize' => 'Capturer le paiement (:amount)',
    'action.payment_cancel_authorization' => 'Annuler l’autorisation',
    'action.payment_complete' => 'Compléter le paiement',
    'action.payment_lease' => 'Envoyer une demande à la banque',
    'action.payment_refund' => 'Remboursement',
    'action.payment_sync' => 'Synchroniser',
    'action.payment_void' => 'Annuler',
    'action.print_order' => 'Imprimer la commande',
    'action.product_add' => 'Ajouter un produit',
    'action.remove_order_discount' => 'Supprimer la remise de la commande',
    'action.remove_order_modifications' => 'Supprimer la réduction des « Règles du panier »',
    'action.remove_product_discount' => 'Supprimer la remise sur le produit',
    'action.send_credit_note' => 'Envoyer l’avoir au client',
    'action.ship_items' => 'Exécuter les produits',
    'action.success_cod' => 'Paiement à la livraison réussi. Montant : ',
    'action.sync' => 'Informations de paiement manquantes',
    'action.unarchive' => 'Déarchiver',
    'action.view_credit_note' => 'Avoir',
    'action.view_details' => 'Voir les détails',
    'action.view_invoice' => 'Voir la facture',
    'action.view_more' => 'Voir plus',
    'add.new_address' => 'Ajouter une nouvelle adresse ',
    'add_address' => 'Ajouter une adresse',
    'add_customer' => 'Ajouter un client',
    'add_more_cod' => 'Acheter un package supplémentaire',
    'added' => 'Ajouté',
    'added_to_order' => 'Ajouté à la commande',
    'added_to_order_product' => 'Produit ajouté à la commande',
    'addon.percent_discount' => '%',
    'address' => 'Adresse',
    'address.not_fount' => 'Aucune adresse trouvée pour le client sélectionné.',
    'address_add_shipping' => 'Ajouter une adresse de livraison',
    'address_change_billing' => 'Changer la facturation',
    'address_change_shipping' => 'Changer l’expédition',
    'address_edit_billing' => 'Modifier la facturation',
    'address_edit_shipping' => 'Modifier l’expédition',
    'address_reposition' => 'Repositionner',
    'address_reposition_billing' => 'Repositionner la facturation',
    'address_reposition_shipping' => 'Repositionner l’expédition',
    'admin_note' => 'Note administrative',
    'aggregate.success' => 'La demande de téléchargement a réussi. Dans quelques minutes, vous recevrez un email avec un lien de téléchargement.',
    'aggregate.type' => 'factures',
    'alert.manual_order' => 'Adresse de complétion de la commande client :',
    'alert.manual_order_help' => 'Fournissez un lien à votre client pour compléter la commande ou',
    'alert.send_as_email' => 'envoyer par email.',
    'alert_is_draft' => 'Vous devez ajouter <b> des produits </b>, <b> méthode de paiement </b>, et <b> méthode de livraison </b> pour enregistrer votre commande.',
    'alert_is_draft.and' => 'et',
    'alert_is_draft.end' => 'pour enregistrer votre commande.',
    'alert_is_draft.payment' => '<b> méthode de paiement </b>',
    'alert_is_draft.products' => '<b> des produits </b>',
    'alert_is_draft.shipping' => '<b> méthode de livraison </b>',
    'alert_is_draft.start' => 'Vous devez ajouter',
    'amount' => 'Montant',
    'archived' => 'Archivé',
    'are_you_sure' => 'En annulant cette commande, vous ne pourrez pas la restaurer',
    'banned_ip.add' => 'Bloquer une nouvelle IP',
    'banned_ip.add_from_order' => 'Bloquer',
    'banned_ip.ban' => 'Bloquer les adresses IP des clients',
    'banned_ip.ban_reason' => 'La commande a été effectuée à partir d’une adresse IP bloquée. Raison :',
    'banned_ip.ip_address' => 'Adresse IP',
    'billing_address' => 'Adresse de facturation',
    'button.allow_recalculate' => 'Autoriser le recalcul du prix d’expédition',
    'button.confirm' => 'Confirmer',
    'button.disallow_recalculate' => 'Interdire le recalcul du prix d’expédition',
    'change.payment_success' => 'Vous avez changé votre méthode de paiement avec succès',
    'changed_to' => 'Changé en',
    'choose_another_address' => 'Changer d’adresse',
    'choose_discount' => 'Choisir une remise',
    'choose_discount_target' => 'Choisir le cible de la remise',
    'choose_existing_address' => 'Choisir une adresse existante',
    'confirm.archive' => 'Archiver',
    'confirm.complete' => 'Compléter',
    'confirm.mark_as_paid_confirm' => 'Si vous marquez la commande comme payée, vous ne pourrez plus changer son statut',
    'confirm.order_product_delete_confirm' => 'Êtes-vous sûr de vouloir supprimer ce produit de la commande ?',
    'confirm.remove_order_discount' => 'Supprimer la remise de la commande',
    'confirm.remove_order_modifications' => 'Êtes-vous sûr de vouloir supprimer la réduction des « Règles du panier » ?',
    'confirm.remove_product_discount_confirm_message' => 'Remise supprimée',
    'confirm.unarchive' => 'Déarchiver',
    'confirm_order.title_modal' => 'Confirmer la commande dans ',
    'create_invoice' => 'Créer une facture',
    'create_order' => 'Créer une commande',
    'create_order_and_send_client' => 'Créer une commande et l’envoyer au client',
    'created' => 'Commande créée',
    'credit_create_date' => 'Date d’émission de l’avoir',
    'credit_number' => 'Numéro d’avoir',
    'crossSell.discount' => 'Vente croisée',
    'customer' => 'Client',
    'customer_completed_orders' => 'Commandes complétées',
    'customer_edit' => 'Modifier le client',
    'customer_email' => 'Email du client',
    'customer_group' => 'Groupe de clients',
    'customer_total_orders' => 'Nombre total de commandes',
    'customer_view_profile' => 'Voir le profil',
    'date' => 'Date',
    'date_added' => 'Date ajoutée',
    'date_archived' => 'Date archivée',
    'date_fulfilled' => 'Date exécutée',
    'date_paid' => 'Date de paiement',
    'date_updated' => 'Date de la dernière mise à jour',
    'deleted' => 'Commande supprimée',
    'discount' => 'Remise',
    'discount_type_flat' => 'Type plat',
    'discount_type_percent' => 'Type pourcentage',
    'down payment' => 'Paiement initial',
    'download.credit' => 'Avoir',
    'download.invoice' => 'Facture',
    'draft.button_1' => 'Le système enregistrera les modifications et créera des commandes',
    'draft.button_2' => 'Le système enregistrera les modifications et enverra un email au client avec un lien pour compléter la commande.',
    'draft.button_3' => 'Le système enverra un email au client avec une adresse pour compléter / payer la commande',
    'edited_to' => 'Modifié en',
    'err.abandoned_no_emails_sent' => 'Aucun email envoyé. Vous ne pouvez envoyer qu’un email par client par jour !',
    'err.address_no_longer_exists' => 'L’adresse n’existe plus',
    'err.begin_date_invalid_format' => 'Format de date de début invalide',
    'err.cannot_add_digital_product_already_in_cart' => 'Le produit numérique que vous essayez d’ajouter est déjà présent dans la commande.',
    'err.cannot_change_status_of_archived_order_unarchive_first' => 'Impossible de changer le statut de la commande archivée. Déarchiver d’abord.',
    'err.cannot_complete_cancelled_order' => 'Impossible de compléter la commande annulée',
    'err.cannot_perform_this_operation_on_archived_order' => 'Impossible de réaliser cette opération sur une commande archivée',
    'err.cannot_ship_items_without_shipping_provider' => 'Les produits ne peuvent pas être exécutés sans méthode d’expédition.',
    'err.choose_customer_adddress' => 'Adresse du client non fournie',
    'err.creation_failed' => 'Commande échouée',
    'err.customer_email_requred' => 'L’email du client est requis',
    'err.customer_first_name_cannot_be_more_than_%1$s_characters' => 'Le "prénom du client" ne peut pas dépasser %1$s caractères',
    'err.customer_first_name_empty' => 'Le "prénom du client" est vide',
    'err.customer_has_no_addresses' => 'Le client n’a pas d’adresse',
    'err.customer_last_name_cannot_be_more_than_%1$s_characters' => 'Le "nom de famille du client" ne peut pas dépasser %1$s caractères',
    'err.customer_last_name_empty' => 'Le "nom de famille du client" est vide',
    'err.daterange_required' => 'Plage de dates requise',
    'err.delete_status_orders' => 'Ce statut a des commandes attachées : total de commandes',
    'err.discount_negative_type_value' => 'La valeur de la remise ne peut pas dépasser le total de la commande',
    'err.discount_no_longer_active' => 'La remise n’est plus active',
    'err.discount_no_longer_exists' => 'La remise n’existe plus',
    'err.discount_not_enough_uses_left' => 'Pas assez d’utilisations restantes pour la remise',
    'err.discount_target_for_different_category' => 'Cette remise est destinée à une catégorie différente',
    'err.discount_target_for_different_customer_group' => 'Cette remise est destinée à un groupe de clients différent',
    'err.discount_target_for_different_product' => 'Cette remise est destinée à un produit différent',
    'err.discount_target_for_different_region' => 'Cette remise est destinée à une région différente',
    'err.discount_target_is_for_specific_region' => 'Cette remise est pour une région spécifique',
    'err.discount_target_no_longer_exists' => 'Ce ciblage de remise n’existe plus',
    'err.discount_target_required' => 'Cible de remise requise',
    'err.discount_type_required' => 'Type de remise requis',
    'err.end_date_invalid_format' => 'Date de fin invalide.',
    'err.flat_discount_must_be_less_than_order_subtotal' => 'La remise fixe doit être inférieure au sous-total de la commande',
    'err.flat_discount_must_be_less_than_product_price' => 'La remise fixe doit être inférieure au prix du produit',
    'err.flat_discount_value_cannot_be_empty' => 'La valeur de la remise fixe ne peut pas être vide',
    'err.for_change_fulfillment_status_use_button' => 'Vous ne pouvez pas changer le statut d’expédition d’ici car cette commande a été réalisée avec un fournisseur d’expédition qui n’autorise pas le changement du statut de remplissage manuellement',
    'err.invalid_address' => 'Adresse invalide.',
    'err.invalid_address_type' => 'Type d’adresse invalide',
    'err.invalid_amount' => 'Montant invalide.',
    'err.invalid_barcode' => 'Code-barres invalide.',
    'err.invalid_category' => 'Catégorie de produit invalide.',
    'err.invalid_customer' => 'Client invalide.',
    'err.invalid_customer_group' => 'Groupe de clients invalide.',
    'err.invalid_customers' => 'Clients invalides.',
    'err.invalid_daterange_format' => 'Format de plage de dates invalide.',
    'err.invalid_discount_target' => 'Cible de remise invalide.',
    'err.invalid_discount_type' => 'Type de remise invalide.',
    'err.invalid_filter_compare_operator' => 'L’opérateur de comparaison du filtre est invalide.',
    'err.invalid_filter_value' => 'Valeur du filtre invalide.',
    'err.invalid_group' => 'Groupe de clients invalide.',
    'err.invalid_invoice_number' => 'Numéro de facture invalide',
    'err.invalid_note_administrator' => 'Note à l’administrateur invalide.',
    'err.invalid_order' => 'Commande invalide.',
    'err.invalid_order_id' => 'ID de commande invalide.',
    'err.invalid_product_id' => 'ID de produit invalide.',
    'err.invalid_provider' => 'Fournisseur de paiement invalide.',
    'err.invalid_quantity' => 'Quantité invalide.',
    'err.invalid_region' => 'Région invalide.',
    'err.invalid_request' => 'Requête invalide.',
    'err.invalid_restore_link' => 'Le lien de restauration est invalide.',
    'err.invalid_shipping_provider' => 'Méthode d’expédition invalide.',
    'err.invalid_sku' => 'SKU invalide.',
    'err.invalid_status' => 'Statut de commande invalide.',
    'err.invalid_type' => 'Type invalide.',
    'err.invalid_value' => 'Valeur de variant invalide.',
    'err.invalid_variant_id' => 'ID de variant invalide.',
    'err.invalid_vendor' => 'Marque invalide.',
    'err.invoice_number_numeric' => 'Le numéro de la facture doit être un nombre',
    'err.invoice_number_required' => 'Vous n’avez pas entré de numéro de facture',
    'err.invoice_number_unique' => 'Une facture avec le numéro entré existe déjà. Veuillez entrer un numéro unique',
    'err.no_addresses_available' => 'Aucune adresse disponible.',
    'err.no_discounts' => 'Aucune remise disponible.',
    'err.no_products_for_fulfillment' => 'Aucun produit à exécuter.',
    'err.no_such_customer' => 'Il n’existe pas de tel client.',
    'err.no_such_group' => 'Il n’existe pas de tel groupe de clients.',
    'err.no_such_order' => 'Il n’existe pas de telle commande.',
    'err.not_enough_quantity_for_%1$s' => 'Il n’y a pas assez de quantité pour %1$s.',
    'err.note_administrator_max_chars_%1$s' => 'Le maximum de caractères autorisés pour la note de l’administrateur est de %1$s.',
    'err.nothing_changed' => 'Rien n’a été modifié.',
    'err.only_completed_orders_can_be_archived' => 'Seules les commandes complètes peuvent être archivées.',
    'err.only_paid_and_fulfilled_orders_can_be_completed' => 'Seules les commandes payées et/ou exécutées peuvent être marquées comme Complètes',
    'err.only_pending_orders_can_be_abandoned' => 'Seules les commandes ouvertes peuvent être abandonnées.',
    'err.only_pending_orders_can_be_canceled' => 'Seules les commandes ouvertes peuvent être annulées.',
    'err.order_already_completed' => 'Cette commande a déjà été complétée.',
    'err.order_already_pending' => 'Cette commande est déjà en attente.',
    'err.order_complete_not_fulfilled' => 'La commande doit être exécutée avant d’être marquée comme complète.',
    'err.order_discount_no_longer_exists' => 'La remise de la commande n’existe plus.',
    'err.order_does_not_exist' => 'Cette commande n’existe pas.',
    'err.order_has_discount' => 'Cette commande a une remise.',
    'err.order_has_no_addresses' => 'Cette commande n’a pas d’adresse.',
    'err.order_has_no_payments' => 'Cette commande n’a pas de paiements.',
    'err.order_must_have_at_least_one_product' => 'Les commandes doivent contenir au moins un produit.',
    'err.order_no_longer_exists' => 'La commande n’existe plus.',
    'err.order_payment_does_not_exist' => 'Ce paiement de commande n’existe pas.',
    'err.order_payment_incomplete' => 'Échec du paiement',
    'err.order_product_no_longer_exists' => 'Ce produit de commande n’existe plus.',
    'err.original_product_variant_does_not_have_price_please_enter_override_price' => 'La variante de produit originale n’a pas de prix. Veuillez entrer un prix de remplacement',
    'err.payment_amount_differs_from_order_total' => 'Le montant du paiement est différent du total de la commande.',
    'err.payment_provider_already_set' => 'Un fournisseur de paiement a déjà été défini.',
    'err.percent_discount_value_cannot_be_empty' => 'La valeur de remise pourcentage ne peut pas être vide.',
    'err.product_add_not_enough_quantity' => 'Quantité insuffisante disponible.',
    'err.product_already_has_discount' => 'Ce produit a déjà une remise.',
    'err.product_cant_have_shipping_discount' => 'Les produits ne peuvent pas avoir de remise sur l’expédition',
    'err.product_no_longer_exists' => 'Ce produit n’existe plus',
    'err.product_no_longer_has_discount' => 'Ce produit n’a plus de remise',
    'err.product_variant_no_longer_exists' => 'La variante de produit n’existe plus',
    'err.provider_config_mismatch' => 'La configuration du fournisseur ne correspond pas',
    'err.quantity_required' => 'Quantité requise',
    'err.shipping_products_required' => 'Veuillez choisir au moins un produit à exécuter',
    'err.shipping_provider_does_not_have_insurance' => 'La méthode d’expédition n’a pas d’assurance',
    'err.shipping_provider_no_matching_rate' => 'La méthode d’expédition n’a pas de tarif correspondant',
    'err.shipping_provider_no_matching_rate_price' => 'La valeur totale de votre commande (:total) ne se situe pas dans la fourchette de frais d’expédition pour la méthode d’expédition sélectionnée. La plus basse valeur est :min et la plus haute est :max',
    'err.shipping_provider_no_matching_rate_price_and_weight' => 'La valeur totale de votre commande et le poids total ne se situent pas dans la plage de frais d’expédition et de poids pour la méthode d’expédition sélectionnée',
    'err.shipping_provider_no_matching_rate_weight' => 'Le poids total de votre commande (:total) ne se situe pas dans la plage de poids d’expédition pour la méthode d’expédition sélectionnée. Le poids le plus bas est :min et le plus élevé est :max',
    'err.shipping_tracking_url_max_chars_%1$s' => 'Le maximum de caractères autorisés pour l’URL de suivi d’expédition est de %1$s',
    'err.some_orders_archived' => 'Certaines des commandes sont archivées',
    'err.some_products_already_shipped' => 'Certaines des produits sont déjà exécutés',
    'err.some_products_no_longer_in_order' => 'Certaines des produits ne sont plus dans la commande',
    'err.start_date_later_than_end_date' => 'La date de début ne peut pas être antérieure à la date de fin.',
    'err.target_cannot_be_related_to_product' => 'Le ciblage ne peut pas être lié au produit',
    'err.target_not_related_to_product' => 'Le ciblage n’est pas lié au produit',
    'err.variant_required' => 'La variante est requise',
    'err.waybill.out_of_stock_products' => 'Cette commande ne peut pas être expédiée car elle contient des produits en rupture de stock.',
    'error.authorize_amount' => 'Le montant de la commande est :total et ne peut pas dépasser le paiement autorisé :amount.',
    'error.billing_address' => 'Vous n’avez pas entré d’adresse de facturation',
    'error.generate_invoice' => 'Aucune facture n’a été créée dans le système externe sélectionné',
    'existing_discount' => 'Cette remise existe déjà',
    'fast_order' => 'Commande rapide',
    'filer.is_admin' => 'Créé par l’admin',
    'filer.is_draft' => 'Commandes brouillon ',
    'filer.supplier' => 'Fournisseur de produit',
    'filter.barcode_%1$s' => 'Code-barres %1$s',
    'filter.category_%1$s' => 'Catégorie %1$s',
    'filter.country_%1$s' => 'Pays %1$s',
    'filter.credit_note' => 'Avoir émis',
    'filter.customer_%1$s' => 'Client %1$s',
    'filter.customer_group_%1$s' => 'Groupe de clients %1$s',
    'filter.customers_in_%1$s' => 'Clients dans %1$s',
    'filter.date_added' => 'Date ajoutée',
    'filter.date_archived' => 'Date archivée',
    'filter.date_archived_%1$s_%2$s' => 'Date archivée %2$s',
    'filter.discount_code' => 'Code de remise : ',
    'filter.discount_code.option' => 'Code de remise',
    'filter.discount_type_' => '',
    'filter.discount_type_%1$s' => 'Type de remise %1$s',
    'filter.discount_type_fixed' => '',
    'filter.discount_type_flat' => 'Filtrer par taux fixe',
    'filter.discount_type_percent' => 'Filtrer par pourcentage',
    'filter.discount_type_shipping' => 'Filtrer par livraison gratuite',
    'filter.fast_order' => 'Commande rapide',
    'filter.has_discount' => 'A une remise',
    'filter.invoice_number_%1$d' => 'Numéro de facture %1$d',
    'filter.made.through.messenger-bot' => 'Bot Messenger',
    'filter.not_fast_order' => 'Pas commande rapide',
    'filter.only_is_admin_message' => 'Seules les commandes créées via le panneau d’administration sont affichées',
    'filter.only_is_draft_message' => 'Seules les commandes brouillon sont affichées',
    'filter.order_added_%1$s_%2$s' => 'Commande ajoutée %1$s %2$s',
    'filter.order_added_%1$s_%2$s_4' => 'Commande ajoutée après %1$s et avant %2$s',
    'filter.order_archived_%1$s' => 'Commande archivée %1$s',
    'filter.order_fulfillment_status_%1$s' => 'Statut de complétion de la commande %1$s',
    'filter.order_is_%1$s' => 'La commande est %1$s',
    'filter.order_made_through_%1$s' => 'La commande a été effectuée via %1$s',
    'filter.order_payment_status_%1$s_%2$s' => 'Statut de paiement %1$s %2$s',
    'filter.order_product_date_range_%1$s_%2$s' => 'Commande ajoutée de %1$s à %2$s',
    'filter.order_product_id_range_%1$s_%2$s' => 'ID de commande de %1$s à %2$s',
    'filter.order_product_supplier_%1$s' => 'Fournisseur : %1$s',
    'filter.order_recovered_%1$s' => 'Commande récupérée via %1$s',
    'filter.order_saleschannel_%1$s' => 'Canal de vente %1$s',
    'filter.order_status_%1$s_%2$s' => 'Statut de commande %1$s %2$s',
    'filter.order_total_price_%1$s_%2$s' => 'Prix total de la commande %1$s %2$s',
    'filter.order_updated_%1$s_%2$s' => 'Commande mise à jour %1$s %2$s',
    'filter.payment_amount' => 'Montant',
    'filter.payment_amount_%1$s_%2$s' => 'Montant du paiement %1$s %2$s',
    'filter.payment_date_added' => 'Date ajoutée',
    'filter.payment_provider_%1$s_%2$s' => 'Fournisseur de paiement %1$s %2$s',
    'filter.payment_status' => 'Statut de paiement',
    'filter.payment_status_%1$s_%2$s' => 'Statut de paiement %1$s %2$s',
    'filter.product_fulfilled_%1$s' => 'Produit exécuté %1$s',
    'filter.product_price_%1$s_%2$s' => 'Prix du produit %1$s %2$s',
    'filter.product_quantity_%1$s_%2$s' => 'Quantité de produit %1$s %2$s',
    'filter.product_sale_%1$s' => 'Vente de produit %1$s',
    'filter.recovered_from_email' => 'de l’email',
    'filter.recovered_from_messenger' => 'de Messenger',
    'filter.region_%1$s' => 'Région %1$s',
    'filter.shipping_provider_%1$s_%2$s' => 'Fournisseur de livraison %1$s %2$s',
    'filter.sku_%1$s' => 'SKU %1$s',
    'filter.status' => 'Statut',
    'filter.status_' => '',
    'filter.status_fulfillment' => 'Statut d’exécution',
    'filter.supplier' => 'Fournisseur de produit : ',
    'filter.vendor_%1$s' => 'Marque %1$s',
    'fulfillment_date' => 'Date d’exécution',
    'fulfillment_tracking_url' => 'URL de suivi d’exécution',
    'gmap_address' => 'Adresse Google map',
    'go_to_manual' => 'Visitez notre manuel en ligne.',
    'guest_first_name' => 'Prénom de l’invité.',
    'guest_last_name' => 'Nom de l’invité.',
    'guest_no_email' => 'Aucun email disponible.',
    'has_discount' => 'A une remise',
    'has_following_fields_edited_to' => 'A les champs suivants modifiés en',
    'header.abandoned_#' => 'Abandonné #',
    'header.abandoned_orders' => 'Commandes abandonnées',
    'header.add_order' => 'Créer une nouvelle commande',
    'header.add_order_discount' => 'Ajouter une remise sur la commande',
    'header.add_order_payment' => 'Ajouter un paiement de commande',
    'header.add_product' => 'Ajouter un produit',
    'header.add_product_discount' => 'Ajouter une remise sur le produit',
    'header.address_add' => 'Choisissez une adresse existante',
    'header.address_change' => 'Changement d’adresse',
    'header.address_edit' => 'Modifier l’adresse',
    'header.chargeback_order_payment_%1$s' => 'Paiement de commande de remboursement %1$s',
    'header.complete_order_payment_%1$s' => 'Compléter le paiement de la commande %1$s',
    'header.customer_edit' => 'Modifier le client',
    'header.edit_product' => 'Modifier le produit',
    'header.fulfill_products_manual' => 'Exécuter des produits',
    'header.order_history' => 'Historique des commandes',
    'header.order_payments_list' => 'Liste des paiements de commande',
    'header.orders' => 'Commandes',
    'header.product_fulfillment_info' => 'Informations sur l’exécution du produit',
    'header.void_order_payment_%1$s' => 'Annuler le paiement de la commande %1$s',
    'heed_help_abandoned' => 'Besoin d’aide avec les commandes abandonnées ?',
    'help.choose_discount_target' => 'Choisissez le cible de remise que vous visez',
    'help.credit_template.css_classes.description' => 'Ce sont des classes css qui vous aideront à changer le style d’éléments spécifiques. Par exemple : vous pouvez utiliser la classe product-name pour changer la façon dont le nom du produit est affiché.',
    'help.discount_choose' => 'Choisissez si vous voulez ajouter une remise existante ou ajouter manuellement une.',
    'help.google_map_address' => 'Adresse Google maps.',
    'help.order_payment_amount' => 'Montant du paiement.',
    'help.order_payment_chargeback_reason' => 'Fournissez une raison pour le remboursement du paiement',
    'help.order_payment_email_note' => 'Un court message que l’utilisateur recevra dans l’email de paiement.',
    'help.order_payment_provider_reference_id' => 'ID de référence du fournisseur. (Ce champ est facultatif)',
    'help.order_payment_refund' => 'Fournissez une raison pour le remboursement du paiement.',
    'help.ordered_by_facebook' => 'Commandé par Facebook',
    'help.print_template.css_classes.description' => 'Ce sont des classes css qui vous aideront à changer le style d’éléments spécifiques. Par exemple : vous pouvez utiliser la classe product-name pour changer la façon dont le nom du produit est affiché',
    'help.product_price' => 'Prix du produit',
    'help.product_quantity' => 'Quantité de produit.',
    'help.update_customer_address_info' => 'Mettre à jour les informations d’adresse du client.',
    'help.user_map_address' => 'Ceci est l’adresse Google maps.',
    'history.error.details' => 'Erreur : ',
    'history.error.text' => 'Une erreur est survenue lors de l’envoi de la commande à',
    'history.label.' => '',
    'history.label.glovo_invlid_credentials' => 'Identifiants de connexion Glovo invalides',
    'history.label.glovo_store_is_closed' => 'La commande n’a pas été envoyée - le lieu est fermé',
    'history.label.lock_order' => 'Le prix d’expédition de la commande est verrouillé',
    'history.label.modification_removed_from_order_product' => 'Réduction « :app » supprimée pour un produit',
    'history.label.order_receipt_sent' => 'Reçu envoyé',
    'history.label.pick_and_pack' => 'Changer via l’application Pick and Pack',
    'history.label.send_erp_error' => 'Erreur d’envoi de la commande à l’ERP',
    'history.label.send_erp_success' => 'Commande envoyée avec succès à l’ERP',
    'history.label.unlock_order' => 'Prix d’expédition de la commande déverrouillé',
    'history.order_receipt_number' => 'Numéro de reçu #',
    'history.success.details' => 'ID de commande dans ',
    'history.success.text' => 'Commande envoyée avec succès à ',
    'history_no_address' => 'N’a pas d’adresse',
    'history_no_longer_exists' => 'L’historique n’existe plus',
    'history_order_placed' => 'Commande passée',
    'history_order_was_recovered_through_%1$s' => 'La commande a été récupérée après avoir envoyé un rappel %1$s',
    'info.abandoned_restore_last_sent_date' => 'Dernier email envoyé le',
    'info.edit_shipping' => 'Modifier la méthode d’expédition',
    'info.no_products' => 'Aucun produit',
    'info.period' => 'Période de reporting : ',
    'info.period.remaining' => 'Nombre de synchronisations restantes pour la période : ',
    'info.period.used' => 'Nombre de synchronisations effectuées pour la période : ',
    'info.product_add.no_variants' => 'Non défini',
    'info.shipping_notification' => 'Envoyer une notification par email au client',
    'info.text' => 'Le système vérifiera jusqu’à <b>15 jours consécutifs</b> pour le paiement à la livraison de toute commande qui a un numéro de suivi valide et qui a été <b>créée il y a plus de 3 jours à partir de la date de vérification< /b>. <br /><b>Par exemple :</b> Si vous avez créé un numéro de suivi aujourd’hui, le système commencera à vérifier son statut de paiement après 3 jours.',
    'invalid request' => 'Requête invalide',
    'invoice.type.original' => 'Original',
    'invoice_create_date' => 'Date de création de la facture',
    'invoice_line' => 'Facturation',
    'invoice_number' => 'Numéro de facture',
    'ip_address_not_verified' => 'L’IP n’est pas vérifiée',
    'ip_address_verified' => 'L’IP est vérifiée',
    'ip_country' => 'Pays d’IP',
    'ip_info' => 'Protection contre la fraude',
    'is_opened_from_other' => 'Commande ouverte par :user',
    'klear.confirm.order' => 'CONFIRMER LE PAIEMENT DANS',
    'label.credit_template' => 'Modèle pour l’impression d’un avoir ',
    'label.credit_template.css_classes' => 'Classes CSS pour les éléments par défaut',
    'label.customer_address' => 'Adresse du client',
    'label.discount_amount' => 'Montant de la remise',
    'label.discount_choose' => 'Choisir une remise',
    'label.discount_type' => 'Type de remise',
    'label.document.number' => 'Entrer le numéro de document',
    'label.invoice.number' => 'Entrer le numéro de facture',
    'label.invoice_template' => 'Modèle pour l’impression de la facture de commande',
    'label.manual_discount' => 'Remise',
    'label.no_weight' => 'Aucun poids',
    'label.note_from_merchant' => 'Note du commerçant',
    'label.optional_tracking_url' => 'URL de suivi facultative',
    'label.order_payment_amount' => 'Montant',
    'label.order_payment_chargeback_reason' => 'Raison du remboursement',
    'label.order_payment_email_note' => 'Note de paiement',
    'label.order_payment_provider_reference_id' => 'ID de référence du fournisseur',
    'label.order_payment_refund' => 'Remboursement',
    'label.orders' => 'Commandes',
    'label.print_template' => 'Modèle pour l’impression de l’ordonnance',
    'label.print_template.css_classes' => 'Classes CSS pour les éléments par défaut',
    'label.print_template.sorter' => 'Tri des produits',
    'label.print_template.sorter.help' => 'Pour trier les produits dans la liste de stock, vous devez ajouter le paramètre de votre choix à la variable $products. Exemple $products:price-desc',
    'label.product_parameter' => 'Paramètre',
    'label.product_price' => 'Prix',
    'label.product_price_override' => 'Remplacement de prix',
    'label.product_quantity' => 'Quantité',
    'label.shipping_date_delivery' => 'Date de livraison',
    'label.shipping_date_expedition' => 'Exécution',
    'label.tracking_number' => 'Suivi #',
    'label.update_customer_address' => 'Adresse du client',
    'label.update_customer_address_info' => 'Infos d’adresse du client',
    'label.update_customer_info' => 'Infos du client',
    'label.user_map_address' => 'Adresse de la carte de l’utilisateur',
    'made.through' => 'Réalisé à travers',
    'manual_discount' => 'Remise manuelle',
    'manual_discount_settings' => 'Paramètres de remise manuelle',
    'missing_ip_country' => 'N/A',
    'mokka.confirm.order' => 'CONFIRMER LA COMMANDE DANS',
    'new.order.request_address' => 'Vous n’avez pas sélectionné d’adresse de livraison',
    'new.order.request_customer' => 'Vous n’avez pas sélectionné de client',
    'new.order.step2' => 'Prochaine étape',
    'new.order.success_create' => 'Vous avez ajouté la commande avec succès',
    'new.success.saved' => 'Vous avez enregistré votre commande avec succès',
    'no_address' => 'Aucune adresse',
    'no_comment' => 'Il n’y a pas de commentaire sur cette commande',
    'not_enought_can_sell' => 'Non disponible',
    'not_enought_cannot_sell' => 'Non disponible',
    'notify.abandoned_no_records_help' => 'Des problèmes avec les commandes abandonnées ? Suivez le lien ci-dessous.',
    'notify.abandoned_no_records_help_link' => 'Aide sur les commandes abandonnées',
    'notify.abandoned_no_records_info' => 'Les commandes abandonnées de votre magasin apparaîtront ici',
    'notify.abandoned_no_records_yet' => 'Il n’y a pas encore de commandes abandonnées',
    'notify.no_records_help' => 'Des problèmes avec les commandes ? Suivez le lien ci-dessous.',
    'notify.no_records_help_cod' => 'Vous n’avez pas de commandes synchronisées à vérifier pour les paiements imposés.',
    'notify.no_records_help_link' => 'Aide sur les commandes',
    'notify.no_records_info' => 'Les commandes de votre magasin apparaîtront ici',
    'notify.no_records_yet' => 'Vous n’avez pas encore reçu de commandes',
    'notify.send.new_order' => 'Vous avez informé avec succès le client de la commande',
    'notify.shipping_address_is_required' => 'Vous devez remplir l’adresse de livraison pour pouvoir préparer cette commande pour l’expédition',
    'notify.shipping_address_is_required.is_fast_order' => 'La commande a été effectuée via l’application "Commande rapide". Par conséquent, en tant qu’administrateur, vous devez entrer une adresse de livraison pour envoyer la commande.',
    'notify_customer' => 'Notifier le client',
    'notify_customer_help' => 'Lorsque cette option est activée, le client sera informé de tout changement concernant la commande',
    'order.error.document_number' => 'Vous n’avez pas entré de numéro de document',
    'order.error.manual' => 'La commande n’a pas pu être confirmée',
    'order.error.payment' => 'Vous n’avez pas sélectionné de méthode de paiement pour la commande',
    'order.error.products' => 'Vous n’avez ajouté aucun produit à votre commande',
    'order.error.shipping' => 'Vous n’avez pas sélectionné de méthode de livraison pour la commande',
    'order.success.manual_confirm' => 'Commande confirmée avec succès',
    'order_#' => 'Commande #',
    'order_archive_are_you_sure' => 'Cela archivera votre commande. Veuillez confirmer.',
    'order_banned_ip' => 'Annuler automatiquement les commandes passées par des adresses IP spécifiques',
    'order_complete_are_you_sure' => 'Êtes-vous sûr de vouloir compléter cette commande ? Si vous la marquez comme complétée, vous ne pourrez plus jamais changer son statut.',
    'order_date_interval' => 'Intervalle de date de commande',
    'order_has_no_products' => 'La commande n’a pas de produits',
    'order_id_interval' => 'Intervalle d’ID de commande',
    'order_placed_by' => 'Passé par',
    'order_products_fulfilled' => 'Produits exécutés',
    'order_shipping_insurance' => 'Assurance d’expédition',
    'order_total' => 'Total',
    'order_unarchive_are_you_sure' => 'Voulez-vous déarchiver ?',
    'payment.authorized' => 'Paiement autorisé (:amount)',
    'payment.confirm.authorize' => 'Êtes-vous sûr de vouloir autoriser ce paiement ?',
    'payment.confirm.cancel' => 'Êtes-vous sûr de vouloir annuler ce paiement ?',
    'payment.confirm.cancel_authorization' => 'Êtes-vous sûr de vouloir annuler l’autorisation ?',
    'payment.confirm.complete' => 'Êtes-vous sûr de vouloir marquer ce paiement comme complet ?',
    'payment.confirm.lease' => 'La référence sera envoyée',
    'payment.confirm.refund' => 'Êtes-vous sûr de vouloir rembourser ce paiement ?',
    'payment.confirm.sync' => 'Êtes-vous sûr de vouloir synchroniser ce paiement ?',
    'payment.confirm.void' => 'Êtes-vous sûr de vouloir annuler ce paiement ?',
    'payment.status.not_payment' => 'Aucun paiement reçu',
    'payment_provider' => 'Méthode de paiement',
    'payment_provider_reference_id' => 'ID de référence du fournisseur',
    'payment_status' => 'Statut de paiement',
    'percent' => 'Pourcentage',
    'ph.type_here' => 'Tapez ici',
    'plan_does_not_support_abandoned' => 'Votre plan actuel ne prend pas en charge les commandes abandonnées. Envisagez de mettre à niveau votre plan.',
    'product.add_product' => 'Ajouter un produit',
    'product.container.help_text' => '<small>Après avoir sélectionné un client et une adresse de livraison, vous pourrez passer à l’étape suivante pour ajouter des produits / méthode de paiement / méthode de livraison</small>',
    'product_count' => 'Nombre',
    'provider' => 'Méthode d’expédition',
    'ready_for_pack' => 'Le produit est collecté et prêt à être emballé',
    'recovered' => 'Récupéré',
    'referer' => 'Referrer',
    'region' => 'Région',
    'removed_from_order' => 'Supprimé de la commande',
    'removed_from_order_product' => 'Produit supprimé de la commande',
    'saleschannel' => 'Canal de vente',
    'save_and_create_order_info' => 'Le système enregistrera les modifications et créera une commande',
    'save_order' => 'Enregistrer la commande',
    'select.address.help' => 'Une fois que vous sélectionnez un client, vous pourrez sélectionner / ajouter une adresse de livraison ',
    'select.customer' => 'Sélectionner un client',
    'select_product_parameter' => 'Sélectionner le paramètre du produit',
    'send_abonded_order_error' => 'Cette fonctionnalité est disponible pour le plan : ',
    'ship_to.address' => 'Livraison à l’adresse',
    'ship_to.locker' => 'Expédier vers le casier',
    'ship_to.marketplace' => 'Ramassage en magasin',
    'ship_to.office' => 'Livraison au bureau',
    'ship_to.title.delivery_method' => 'Méthode de livraison',
    'shipping' => 'Expédition',
    'shipping_address' => 'Adresse de livraison',
    'shipping_change' => 'Changement de méthode d’expédition',
    'shipping_discount' => 'Remise sur l’expédition',
    'shipping_insurance:' => 'Assurance d’expédition :',
    'source_info' => 'Informations sur la source',
    'status' => 'Statut de commande',
    'status.chage_status' => 'Changer le statut',
    'status.error.invalid' => 'Statut invalide',
    'status.error.invalid_type' => 'Type invalide',
    'status.error.no_name' => 'Il n’y a pas de nom',
    'status.error.no_status' => 'Aucun statut',
    'status.error.order_not_found' => 'Commande non trouvée',
    'status_authorized' => 'Autorisé',
    'status_draft' => 'Brouillon',
    'status_fulfilled' => '',
    'status_fulfillment' => 'Exécution',
    'status_paid' => '',
    'status_pending' => '',
    'status_{$status}' => '',
    'statuses.title' => 'Statuts',
    'subtotal' => 'Sous-total',
    'subtotal_products' => 'Sous-total',
    'succ.abandoned_%1$s_emails_sent' => '%1$s emails ont été envoyés avec succès',
    'succ.abandoned_email_sent_to_client' => 'L’email a été envoyé avec succès au client',
    'succ.address_added_success' => '',
    'succ.address_change_success' => 'Adresse modifiée avec succès',
    'succ.address_edit_success' => 'Adresse modifiée avec succès',
    'succ.customer_edit_success' => 'Client modifié avec succès',
    'succ.order_discount_added' => 'Remise ajoutée avec succès',
    'succ.order_discount_removed' => 'Remise supprimée avec succès',
    'succ.order_notes_edit_success' => 'Notes modifiées avec succès',
    'succ.order_payment_cancel_success' => 'Paiement annulé avec succès',
    'succ.order_payment_chargeback_success' => 'Remboursement du paiement réussi',
    'succ.order_payment_complete_success' => 'Paiement complété avec succès',
    'succ.order_payment_email_sent_to_client' => 'Email envoyé avec succès au client',
    'succ.order_payment_refund_success' => 'Remboursement du paiement réussi',
    'succ.order_payment_sync_success' => 'Paiement synchronisé avec succès',
    'succ.order_payment_void_success' => 'Paiement annulé avec succès',
    'succ.order_product_add_success' => 'Produit ajouté avec succès',
    'succ.order_product_add_success_without_discount' => 'Le produit a été ajouté avec succès, mais aucune remise n’a été appliquée car son prix est inférieur au prix du produit',
    'succ.order_product_delete_success' => 'Produit supprimé avec succès',
    'succ.order_product_discount_added' => 'Remise sur le produit ajoutée avec succès',
    'succ.order_product_discount_removed' => 'Remise sur le produit supprimée avec succès',
    'succ.order_product_edit_success' => 'Produit modifié avec succès',
    'succ.order_products_fulfillment_success' => 'Produits exécutés avec succès',
    'succ.shipping_provider_changed' => 'Fournisseur d’expédition modifié avec succès',
    'success.generate_invoice' => 'Facture générée avec succès',
    'table.action' => 'Statut',
    'table.courier' => 'Livreur',
    'table.datetime' => 'Date et heure',
    'table.orderid' => 'Commande',
    'tax.vat_included' => 'Inclus',
    'tax_amount' => 'Taxe',
    'th.abandoned_#' => 'Abandonné #',
    'th.actions' => 'Actions',
    'th.address' => 'Adresse',
    'th.amount' => 'Montant',
    'th.collected' => ' collecté',
    'th.date' => 'Date',
    'th.date_updated' => 'Date de dernière mise à jour',
    'th.fulfillable_items' => 'Articles exécutables',
    'th.fulfillment' => 'Exécution',
    'th.order' => 'Commande',
    'th.order_#' => 'Commande #',
    'th.ordered' => ' commandé',
    'th.ordered_product.order_ids' => 'Commandes impliquant le produit',
    'th.payment_amount' => 'Montant',
    'th.payment_last_modified' => 'Dernière modification',
    'th.payment_provider' => 'Fournisseur',
    'th.payment_status' => 'Statut de paiement',
    'th.product_name' => 'Nom',
    'th.product_total' => 'Total',
    'th.products' => 'Produits',
    'th.quantity' => 'QTY',
    'th.receiving' => 'Réception',
    'th.single_product_price' => 'Prix unitaire',
    'th.status' => 'Statut de commande',
    'th.total' => 'Total',
    'title' => 'Commandes',
    'title.cart_time_life' => 'Temps de vie du panier avant la commande',
    'title.cod' => 'Paiement à la livraison',
    'to_use_abandoned_upgrade_advanced_or_professional' => 'Si vous souhaitez suivre les commandes abandonnées, passez à un plan avancé ou professionnel.',
    'total' => 'Total',
    'total_price' => 'Total',
    'total_price_after_discount' => 'Prix total du produit après remise',
    'total_price_before_discount' => 'Prix total du produit avant remise',
    'tracking' => 'Suivi',
    'tracking_return' => 'Retour de livraison',
    'updated' => 'Commande mise à jour',
    'upgrade_plan' => 'Mettre à niveau le plan',
    'usn' => 'Numéro de vente unique (USN)',
    'utm_campaign' => 'Campagne UTM',
    'utm_medium' => 'Média UTM',
    'utm_source' => 'Source UTM',
    'validation.address_id.required' => 'Vous n’avez pas sélectionné d’adresse',
    'validation.delivery_to.required' => 'Vous n’avez pas sélectionné de méthode de livraison',
    'validation.office_id.required' => 'Vous n’avez pas sélectionné de bureau',
    'validation.store_id.required' => 'Vous n’avez pas sélectionné de magasin',
    'with_tracking_url' => 'Avec URL de suivi',
];
