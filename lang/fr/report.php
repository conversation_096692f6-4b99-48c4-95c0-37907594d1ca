<?php

return [
    '%_from_all' => '% de tous',
    'amount' => 'Montant',
    'avg_order_value' => 'Valeur moyenne des commandes',
    'avg_payment_amount' => 'Montant moyen des paiements',
    'avg_product_value' => 'Valeur moyenne des produits',
    'avg_tax_value' => 'Valeur moyenne des taxes',
    'by' => 'Par',
    'chart.income' => 'Revenu',
    'chart.new_registrations' => 'Nouvelles inscriptions',
    'chart.payments' => 'Paiements',
    'chart.sales' => 'Ventes',
    'chart.tax_income' => 'Revenu fiscal',
    'chart.taxes' => 'Taxes',
    'chart.total_amount' => 'Montant total',
    'count' => 'Nombre',
    'customer_count' => 'Clients',
    'customer_group' => 'Groupes de clients',
    'customers' => 'Clients',
    'err.begin_date_later_than_end_date' => 'La date de début ne peut pas être après la date de fin.',
    'err.begin_invalid_format' => 'Format invalide pour la date de début.',
    'err.daterange_required' => 'Plage de dates requise.',
    'err.end_invalid_format' => 'Format invalide pour la date de fin.',
    'err.invalid_daterange_format' => 'Format de plage de dates invalide.',
    'filter.products_not_on_sale' => 'Produits non en promotion',
    'filter.products_on_sale' => 'Produits en promotion',
    'filter.total_price' => 'Prix total',
    'for_customers' => 'Rapport client',
    'for_customers_help' => 'Ce rapport affiche toutes les nouvelles inscriptions dans ta boutique en ligne.',
    'for_payments' => 'Rapport de paiement',
    'for_payments_help' => 'Ce rapport affiche tous les paiements dans ta boutique.',
    'for_products' => 'Rapport de produits',
    'for_products_help' => 'Ce rapport affiche toutes les ventes de produits et les revenus.',
    'for_sales' => 'Rapport de ventes',
    'for_sales.help' => 'Ce rapport affiche toutes les commandes et les ventes dans ta boutique en ligne.',
    'general_results' => 'Résultats généraux',
    'no_results' => 'Pas encore de résultats',
    'not_active' => 'Malheureusement, tu ne peux pas accéder à cette section, car ton plan est "Starter" maintenant. Les rapports pourraient te fournir des informations détaillées sur les produits, les commandes, les paiements et les clients de ta boutique en ligne. Si tu souhaites avoir accès aux rapports, merci de mettre à niveau ton plan.',
    'order_avg' => 'Moyenne des commandes',
    'orders' => 'Commandes',
    'parameter_1' => 'Premier paramètre',
    'parameter_2' => 'Deuxième paramètre',
    'parameter_3' => 'Troisième paramètre',
    'payment' => 'Paiement',
    'payment_avg' => 'Moyenne des paiements',
    'payments' => 'Paiements',
    'plan' => 'Plan',
    'product_avg' => 'Moyenne des produits',
    'product_total_price' => 'Total des commandes',
    'products' => 'Produits',
    'sales' => 'Ventes',
    'table_visualisation_of_results' => 'Afficher les résultats sous forme de tableau',
    'tax_count' => 'Nombre de taxes',
    'taxes' => 'Taxes',
    'total-products_income' => 'Revenu total des produits',
    'total_amount' => 'Montant total',
    'total_customers' => 'Total des clients',
    'total_income' => 'Revenu total',
    'total_order_income' => 'Revenu total des commandes',
    'total_orders' => 'Total des commandes',
    'total_orders_amount' => 'Montant total des commandes',
    'total_payments' => 'Total des paiements',
    'total_payments_amount' => 'Montant total des paiements',
    'total_price' => 'Prix total',
    'total_products' => 'Total des produits',
    'total_tax_count' => 'Total des comptes de taxes',
    'total_tax_income' => 'Revenu total des taxes',
    'type.customers' => 'Clients',
    'type.orders' => 'Commandes',
    'type.order.products' => 'Produits dans les commandes',
    'type.payments' => 'Paiements',
    'type.products' => 'Produits',
    'type.taxes' => 'Taxes',
    'variants' => 'Variantes',
    'view' => 'Voir',
    'view_in_table' => 'Voir dans le tableau',
];
