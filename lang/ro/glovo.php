<?php

return [
    'title' => 'Cutii pentru livrare',
    'install' => 'Instalează',
    'install_text' => 'Prin intermediul acestui modul veți putea prezenta automat scrisori de expediție, calculul tarifului de livrare, etc. ',
    'public_key' => 'Introduceți APiKey pentru a vă conecta la Glovo',
    'public_key.help' => 'A ta APiKey',
    'private_key' => 'Introduceți APISecret pentru a vă conecta la Glovo',
    'private_key.help' => 'Secretul dvs. API',
    'connect_to' => 'Conectați-vă la Fancourier',
    'calculator' => 'Calculator Fancourier ',
    'calculator_fixed' => 'Calculator Fancourier + taxa de procesare',
    'calculator_free' => 'Calculator Fancourier + transport gratuit ',
    'fixed_price' => 'Valoare fixă la preț fără calculator Fancourier ',
    'fixed_weight' => 'Valoarea fixă la greutate fără calculator Fancourier ',
    'price_and_weight' => 'Suma fixă pentru expediere conform totalului coșului și greutății produselor',
    'text_calculator' => 'Tarife la zi pentru costul de expediere',
    'text_calculator_fixed' => 'Calculator DHL + taxa de manipulare ',
    'text_fixed_price_price' => 'Suma fixă din totalul coșului ',
    'text_fixed_price_weight' => 'Suma fixă în funcție de greutatea produselor ',
    'text_free_shipping' => 'Calculator Econt + transport gratuit ',
    'print_waybill' => 'Tipărirea conosamentului ',
    'default_address' => 'Adresa expeditorului principal',
    'type.name.address' => 'adresa',
    'public_key.required' => 'Este necesară APiKey',
    'private_key.required' => 'Este necesar APISecret',
    'address.invalid' => 'Adresa pe care ați introdus-o nu este validă',
    'client.first_name.required' => 'Prenumele clientului este obligatoriu',
    'client.last_name.required' => 'Numele de familie al clientului este obligatoriu',
    'client.phone.required' => 'Este necesar telefonul clientului',
    'instruction.sender' => 'Instructiuni aditionale',
    'test_mode' => 'Modul de testare',
    'err.default_location_not_in_shops' => '<b>Locație implicită</b>, nu o locație activă în Glovo',
    'default_weight.required' => 'Greutatea este necesară',
    'default_weight.numeric' => 'Greutatea trebuie să fie un număr',
    'glovo.execution_order.required' => 'Choose a method of order execution',
    'err.default_location' => 'Nu ați selectat o locație implicită',
    'err.execution_order_metod' => 'Nu ați selectat o metodă de executare a ordinului',
    'type_send' => 'Trimiteți expedierea',
    'execution.order' => 'Executarea comenzii',
    'execution.order.help' => 'Dacă selectați <b>La comandă nouă</b>, de îndată ce un client plasează o comandă în magazinul dvs., aceasta va fi trimisă automat către Glovo.<br />Dacă selectați<b> Efectuarea manuală a comenzii</b>trebuie să procesați comanda înainte de a fi trimisă către Glovo.',
    'execution.order.auto' => 'La noua comanda',
    'execution.order.manual' => 'Executarea manuală a comenzii',
    'execution.order.method' => 'Pentru a executa comanda utilizați',
    'execution.order.method.default' => 'Locație implicită',
    'execution.order.method.min_price' => 'Cea mai apropiată locație de livrare',
    'locations' => 'Locații',
    'locations.help' => 'Select locations to fulfill orders from.',
    'location.invalid.address' => 'Locația nu are o adresă validă.<br />Locația trebuie să aibă o adresă și un program de lucru valide pentru a putea onora comenzile de la ea.',
    'default_location' => 'Locație implicită',
    'err.shops' => 'Nu au fost selectate locații',
    'cod' => 'Plata la livrare',
    'send_by' => 'Trimis de la',
    'kg' => 'kg',
    'shipping_price_km' => 'Introduceți prețul de transport pe kilometru',
    'km' => 'km',
    'price' => 'Preț',
    'waybill.acceptance.title' => 'Setări pentru primirea expedierii ',
    'select.distance.help' => 'Selectați o distanță de livrare.',
    'select.distance.amount.help' => 'Introduceți costul de transport',
    'err.price_km' => 'Nu ați introdus prețurile de livrare pentru toate distanțele selectate',
    'err.not_found_shop' => 'Nu s-au găsit locații care să poată servi comanda',
    'default.payer.party' => 'Țara plătitorului implicit pentru ramburs la livrare',
    'err.weight_from_must_start_from_largest_previous_weight_to' => 'Metoda de expediere, rata de greutate de la, trebuie să înceapă de la cea mai mare greutate anterioară până la ',
    'err.weight_to_must_end_at_smallest_previous_weight_from' => 'Metoda de expediere, greutatea de la, trebuie să se încheie la cea mai mică greutate anterioară de la ',
    'location.ordermeta' => 'Locație:',
    'install.error_store' => 'Trebuie să aveți instalată aplicația <a href="/admin/stores">Magazine</a> pentru a utiliza această aplicație.',
    'install.error_api_key' => 'Trebuie să aveți un <a href="https://help.cloudcart.com/en/support/solutions/articles/77000482921-create-a-google-maps-api-key-for-cloudcart" target="_blank">Cheia API Google</a>a fost introdusă pentru a utiliza această aplicație.',
    'action.add.location' => 'Adaugă locație',
    'title.pricelist' => 'Listă de prețuri',
    'shop_address_not_found' => 'Nu a fost introdusă nicio adresă de magazin',
    'shop_worktime_not_found' => 'Nu a fost introdus niciun orar al magazinului',
    'shop_pricelist_not_found' => 'Nu a fost introdusă nicio listă de prețuri pentru locații',
    'err.invalid_credentials' => 'Date de conectare Glovo nevalide',
    'type.package' => 'Tipul pachetului',
    'title.details' => 'Detalii',
];
