<?php

return [
    'accepted' => 'Atributul : trebuie acceptat.',
    'active_url' => ':attribute nu este o adresă URL validă.',
    'after' => 'După',
    'after_or_equal' => 'Atributul : trebuie să fie o dată ulterioară sau egală cu :date.',
    'alpha' => 'Atributul : poate conține doar litere.',
    'alpha_dash' => 'Atributul : poate conține numai litere, cifre și liniuțe.',
    'alpha_num' => 'Atributul : poate conține numai litere și cifre.',
    'array' => 'Atributul : trebuie să fie o matrice.',
    'before' => 'Înainte de',
    'before_or_equal' => ':attribute trebuie să fie o dată anterioară sau egală cu :date.',
    'between.numeric' => 'Atributul : trebuie să fie între :min și :max.',
    'between.file' => 'Atributul : trebuie să fie între :min și :max kilobytes.',
    'between.string' => 'Atributul : trebuie să aibă între caractere :min și :max.',
    'between.array' => 'Atributul : trebuie să aibă între elemente :min și :max.',
    'boolean' => 'Câmpul :attribute trebuie să fie adevărat sau fals',
    'confirmed' => 'Confirmarea :attribute nu se potrivește.',
    'custom.attribute-name.rule-name' => 'Mesaj personalizat',
    'date' => 'Data',
    'date_format' => ':attribute nu se potrivește cu formatul :format.',
    'different' => ':attribute și :other trebuie să fie diferite.',
    'digits' => ':attribute trebuie să fie :digits cifre.',
    'digits_between' => 'Atributul : trebuie să fie între cifrele :min și :max.',
    'dimensions' => 'Atributul : are dimensiuni nevalide ale imaginii.',
    'distinct' => 'Câmpul :attribute are o valoare duplicat.',
    'email' => 'Email',
    'exists' => 'Atributul : selectat este nevalid.',
    'file' => 'Fișier',
    'filled' => 'Câmpul :attribute este obligatoriu.',
    'gt.numeric' => ':attribute trebuie să fie mai mare decât :value.',
    'gt.file' => 'The :attribute must be greater than :value kilobytes.',
    'gt.string' => ':attribute trebuie să fie mai mare decât caracterele :value.',
    'gt.array' => ':attribute trebuie să aibă mai multe elemente decât :value.',
    'gte.numeric' => ':attribute trebuie să fie mai mare sau egal cu :value.',
    'gte.file' => ':attribute trebuie să fie mai mare sau egal cu :value kilobytes.',
    'gte.string' => 'Atributul : trebuie să fie mai mare sau egal cu caracterele :value.',
    'gte.array' => ':attribute trebuie să aibă elemente :value sau mai multe.',
    'hex_color' => 'Formatul :attribute este nevalid.',
    'image' => 'Imagine ',
    'in' => 'În',
    'in_array' => 'Câmpul :attribute nu există în :other.',
    'integer' => 'Atributul : trebuie să fie un număr întreg.',
    'ip' => ':attribute trebuie să fie o adresă IP validă.',
    'json' => 'Atributul : trebuie să fie un șir JSON valid.',
    'lt.numeric' => ':attribute trebuie să fie mai mic decât :value.',
    'lt.file' => ':attribute trebuie să fie mai mic de :value kilobytes.',
    'lt.string' => ':attribute trebuie să fie mai mic decât caractere :value.',
    'lt.array' => ':attribute trebuie să aibă mai puțin de elemente :value.',
    'lte.numeric' => ':atributul trebuie să fie mai mic sau egal cu :value.',
    'lte.file' => 'Atributul : trebuie să fie mai mic sau egal cu :value kilobytes.',
    'lte.string' => 'Atributul : trebuie să fie mai mic sau egal cu caractere :value.',
    'lte.array' => ':attribute nu trebuie să aibă mai mult de :value elemente.',
    'max.numeric' => 'Atributul : nu poate fi mai mare decât :max.',
    'max.file' => 'The :attribute may not be greater than :max kilobytes.',
    'max.string' => 'Atributul : nu poate fi mai mare decât caracterele :max.',
    'max.array' => ':attribute nu poate avea mai mult de :max elemente.',
    'mimes' => 'attribute trebuie să fie un fișier de tip: :values.',
    'mimetypes' => ':attribute trebuie să fie un fișier de tip: :values.',
    'min.numeric' => 'Atributul : trebuie să fie cel puțin :min',
    'min.file' => ':attribute trebuie să fie de cel puțin :min kilobytes.',
    'min.string' => 'Atributul : trebuie să conțină cel puțin caractere :min.',
    'min.array' => 'Atributul : trebuie să aibă cel puțin elemente :min.',
    'not_in' => 'Atributul : selectat este nevalid.',
    'numeric' => 'Atributul : trebuie să fie un număr.',
    'phone' => 'Telefon',
    'present' => 'Câmpul :attribute trebuie să fie prezent.',
    'regex' => 'Formatul :attribute este nevalid.',
    'required' => 'Necesar',
    'required_field' => 'Câmpul este obligatoriu.',
    'required_if' => 'Câmpul :attribute este obligatoriu când :other este :value.',
    'required_unless' => 'Câmpul :attribute este obligatoriu, cu excepția cazului în care :other se află în :values.',
    'required_with' => 'Câmpul :attribute este obligatoriu când :values este prezent.',
    'required_with_all' => 'Câmpul :attribute este obligatoriu când :values este prezent.',
    'required_without' => 'Câmpul :attribute este obligatoriu când :values nu este prezent.',
    'required_without_all' => 'Câmpul :attribute este obligatoriu atunci când niciuna dintre :value nu este prezentă.',
    'same' => ':attribute și :other trebuie să se potrivească.',
    'size.numeric' => ':attribute trebuie să fie :size.',
    'size.file' => ':attribute trebuie să fie :size kilobytes.',
    'size.string' => ':attribute trebuie să fie caractere :size.',
    'size.array' => ':attribute trebuie să conţină elemente :size.',
    'string' => 'Atributul : trebuie să fie un șir.',
    'timezone' => 'Atributul : trebuie să fie o zonă validă.',
    'unique' => 'Atributul : a fost deja luat',
    'uploaded' => 'Atributul : nu a putut fi încărcat.',
    'url' => 'Url',
    'latitude' => 'Latitudine',
    'longitude' => 'Longitudine',
    'header' => 'Datele date au fost nevalide.',
];
