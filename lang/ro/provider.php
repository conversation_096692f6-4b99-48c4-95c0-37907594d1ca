<?php

return [
    'err.authorize.app_login_key_required' => 'Autorizați: este necesară cheia de conectare la aplicație. ',
    'err.authorize.transaction_key_required' => 'Autorizați: este necesară cheia tranzacției. ',
    'err.borica.cannot_change_mode_missing_cert_or_private_key' => 'Borica: nu poate trece la modul live dacă nu toate certificatele sunt validate ',
    'err.borica.invalid.certificate_etlog' => 'Borica: certificat eTLog nevalid ',
    'err.borica.invalid.certificate_real' => 'Borica: certificat real invalid ',
    'err.borica.invalid.certificate_test' => 'Borica: certificat de test invalid ',
    'err.borica.invalid.commonName' => 'Borica: nume comun invalid ',
    'err.borica.invalid.countryName' => 'Borica: nume de țară invalid ',
    'err.borica.invalid.emailAddress' => 'Borica: adresă de e-mail nevalidă ',
    'err.borica.invalid.localityName' => 'Borica: numele localității este invalid ',
    'err.borica.invalid.organizationName' => 'Borica: numele organizației este invalid',
    'err.borica.invalid.organizationalUnitName' => 'Borica: numele unității organizației este invalid ',
    'err.borica.invalid.stateOrProvinceName' => 'Borica: nume invalid de stat sau provincie ',
    'err.borica.invalid.terminal_id' => 'Borica: ID terminal nevalid ',
    'err.borica.not_match.certificate_etlog' => 'Borica: cheia privată eTLog și certificatul nu se potrivesc ',
    'err.borica.not_match.certificate_real' => 'Borica: cheia privată reală și certificatul nu se potrivesc ',
    'err.borica.not_match.certificate_test' => 'Borica: cheia de testare privată și certificatul nu se potrivesc',
    'err.bwt.description_max_length_is_%1$s' => 'Transfer bancar: descrierea trebuie să aibă mai puțin de %1$s caractere. ',
    'err.bwt.description_required' => 'Transfer bancar: este necesară precizarea unei descrieri. ',
    'err.cod.description_max_length_is_%1$s' => 'Ramburs: descrierea trebuie să aibă mai puțin de %1$s caractere.',
    'err.cod.description_required' => 'Ramburs la livrare: este necesară precizarea unei descrieri. ',
    'err.voucher.description_max_length_is_%1$s' => 'Voucher: descrierea trebuie să aibă mai puțin de %1$s caractere.',
    'err.voucher.description_required' => 'Voucher: este necesară precizarea unei descrieri. ',
    'err.epay.kin_required' => 'EPay: este necesară precizarea KIN-ului. ',
    'err.epay.secret_required' => 'EPay: este necesară precizarea secretului.',
    'err.min_price_required' => 'Este necesară precizarea unui preț minim ',
    'err.paypal.merchant_email_required' => 'PayPal: este necesară precizarea adresei de email a comerciantului.',
    'err.percent_per_month_required' => 'Este necesară precizarea unui procent pe lună ',
    'err.skrill.api_password_required' => 'Skrill: parola API este necesară. ',
    'err.skrill.email_required' => 'Skrill: e-mail este necesar. ',
    'err.skrill.merchant_id_required' => 'Skrill: este necesară identificarea comerciantului. ',
    'err.skrill.signature_hash_algorithm_required' => 'Skrill: este necesar algoritmul de semnare hash. ',
    'err.skrill.signature_secret_required' => 'Skrill: semnătura secretă este necesară. ',
    'err.tbi.email_invalid' => 'E-mailul introdus nu este valid. ',
    'err.tbi.email_required' => 'E-mailul este obligatoriu. ',
    'err.tbi.id_required' => 'Este necesar un număr de acord. ',
    'err.tbi.max_required' => 'Perioada maximă este necesară. ',
    'err.tbi.min_required' => 'Este necesară perioada minimă. ',
    'err.tbi.step_required' => 'Este necesar intervalul de pași. ',
    'err.title_max_length_is_%1$s' => 'Titlul trebuie să aibă mai puțin de %1$s caractere.',
    'err.title_required' => 'Titlul este obligatoriu.',
    'name_' => '(câmp gol)',
];
