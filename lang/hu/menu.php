<?php

return [
    'add' => 'Több hozz<PERSON>ad<PERSON>a',
    'add_first' => 'Add hozzá az első menüpontodat',
    'err.%1$s_link_does_not_exist' => '%1$s link nem létezik',
    'err.invalid_link' => 'Érvénytelen link',
    'err.invalid_link_section' => 'Érvénytelen link szakasz',
    'err.invalid_link_type' => 'Érvénytelen link típus',
    'err.invalid_target' => 'Érvénytelen cél',
    'err.link_required' => 'A link kötelező',
    'err.link_target_required' => 'A link cél kötelező',
    'err.link_type_required' => 'A link típus kötelező',
    'err.max_depth_is_%1$s' => 'A maximális mélység %1$s',
    'err.name_max_chars_is_%1$s' => 'A név maximális karaktereinek száma %1$s',
    'err.name_required' => 'A név kötelező',
    'err.name_taken' => 'A név már foglalt',
    'err.parent_no_longer_exists' => 'A szülő már nem létezik',
    'footer' => 'Lábléc menü',
    'footer_choose_description' => 'Adj menüpontokat az áruház lábléc szakaszához.',
    'footer_help' => 'A tartalom itt látható lesz az áruház láblécében',
    'help.article_select' => 'Válaszd ki a cikket, ahova ez a menüpont irányít.',
    'help.blog_select' => 'Válaszd ki a cikk kategóriát, ahova ez a menüpont irányít.',
    'help.category_select' => 'Válaszd ki a termékkategóriát, ahova ez a menüpont irányít.',
    'help.external_link_address' => 'Add meg a webcímet, ahova át szeretnél irányítani. Példa: http://google.com',
    'help.group_name' => 'Válaszd ki a csoportosított menük nevét.',
    'help.icon' => 'Válassz egy képi ikont ehhez a navigációs menühöz. Ez a neve bal oldalán fog látszani.',
    'help.image' => 'Kép',
    'help.link_name' => 'Ez az a név, amit a felhasználók látnak a menüpontnál.',
    'help.open_in_new_window' => 'Ha be van jelölve, a menüpont új ablakba irányítja a felhasználót.',
    'help.page_select' => 'Válaszd ki a statikus oldalt, ahova ez a menüpont irányít.',
    'help.parent' => 'Válassz egy szülőt, ha szeretnéd, hogy ez a menüpont egy almennü legyen.',
    'help.product_select' => 'Válaszd ki a terméket, ahova ez a menüpont irányít.',
    'help.selection_select' => 'Válaszd ki a gyűjteményt, ahova ez a menüpont irányít.',
    'help.theme_section_select' => 'Válaszd ki az áruházi szakaszt, ahova ez a menüpont irányít.',
    'help.vendor_select' => 'Válaszd ki a szállítót, ahova ez a menüpont irányít.',
    'label.article_select' => 'Cikk',
    'label.blog_select' => 'Cikk kategória',
    'label.category_select' => 'Termékkategória',
    'label.external_link_address' => 'Külső cím',
    'label.group_name' => 'Menücsoport neve',
    'label.icon' => 'Ikona',
    'label.image' => 'Kép',
    'label.link_name' => 'Menüpont neve',
    'label.open_in_new_window' => 'Link megnyitása új ablakban',
    'label.page_select' => 'Statikus oldal',
    'label.parent' => 'Menü szülő',
    'label.product_select' => 'Termék',
    'label.selection_select' => 'Gyűjtemény',
    'label.snippet' => 'Snippet kód',
    'label.theme_section_select' => 'Áruházi szakasz',
    'label.vendor_select' => 'Márka',
    'link_blog' => 'Link egy konkrét cikk kategóriához',
    'link_blog_article' => 'Link egy konkrét cikkhez',
    'link_blog_article_help' => 'A menüpont egy konkrét cikkhez irányítja a felhasználót.',
    'link_blog_help' => 'A menüpont egy konkrét cikk kategóriához irányítja a felhasználót.',
    'link_group' => 'Csoportos menü',
    'link_group_help' => 'Ez a menü más menüket csoportosít. Használj ezt legördülős menük létrehozásához.',
    'link_mailchimp' => 'Feliratkozás a hírlevélre',
    'link_mailchimp_help' => 'A menü más menüket csoportosít. Használj ezt legördülős menük létrehozásához',
    'link_page' => 'Link egy konkrét statikus oldalra',
    'link_page_help' => 'A menüpont egy statikus oldalra irányítja a felhasználót.',
    'link_product' => 'Link egy konkrét termékhez',
    'link_product_category' => 'Link egy konkrét kategóriához',
    'link_product_category_help' => 'A menüpont egy konkrét termékkategóriához irányítja a felhasználót.',
    'link_product_help' => 'A menüpont egy konkrét termékhez irányítja a felhasználót.',
    'link_section' => 'Link egy konkrét áruházi szakaszhoz',
    'link_section_help' => 'A menü egy konkrét szakaszhoz irányítja a felhasználót az áruházban.',
    'link_selection' => 'Link egy konkrét Smart Collectionhöz',
    'link_selection_help' => 'A menüpont egy konkrét Smart Collectionhöz irányítja a felhasználót',
    'link_snippet' => 'Snippet kód integrálása',
    'link_snippet_help' => 'Ezzel az elemmel html vagy js kódot adhatsz hozzá a navigációdhoz',
    'link_vendor' => 'Link egy konkrét szállítóhoz',
    'link_vendor_help' => 'A menüpont egy konkrét szállítóhoz irányítja a felhasználót.',
    'main' => 'Főmenü',
    'main_choose_description' => 'Adj menüpontokat az áruház fő navigációjához.',
    'main_help' => 'A tartalom itt látható lesz az áruház főmenüjében',
    'max_depth_is_%1$s' => 'A maximális mélység %1$s',
    'moved_and_target_cannot_be_the_same' => 'A mozgatott és a cél nem lehet azonos',
    'moved_no_longer_exists' => 'A mozgatott már nem létezik',
    'name_taken' => 'A név már foglalt',
    'no_longer_exists' => 'Már nem létezik',
    'notify.footer_no_records_yet' => 'A lábléc menüpontok még nem érhetők el.',
    'notify.main_no_records_yet' => 'A főmenüpontok még nem érhetők el.',
    'ph.select_product' => 'Válassz egy terméket',
    'ph.snippet' => 'Itt elhelyezhetsz egy HTML vagy Javascript kódot, amely a rendszerből értelmezhető.',
    'target_no_longer_exists' => 'A cél már nem létezik',
    'targetis_child_of_moved' => 'A célzott a mozgatott gyermeke',
    'url' => 'Web link',
    'url_help' => 'Adj linket egy külső webhelyre',
];
