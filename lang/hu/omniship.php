<?php

return [
    'err.key_is_not_defined' => '',
    'err.provider_is_registered' => '',
    'err.provider_not_support_cod' => '',
    'err.required.items' => '',
    'err.required.receiver_address' => 'Érvénytelen szállítási cím',
    'err.required.sender_address' => '',
    'err.unable_to_find_city' => '',
    'fixed_price_price' => 'Fix összeg a szállításhoz, amelyet a kosár összértéke alapján becsültek meg',
    'fixed_price_weight' => 'Fix összeg a szállításhoz a termékek súlya alapján',
    'place.type.' => '',
    'place.type.city' => 'város :city',
    'place.type.village' => 'falu :city',
    'price_and_weight' => 'Fix összeg a szállításhoz a kosár összértéke és a termékek súlya alapján',
    'text.fallback' => 'Alternatív ár',
    'text.to_address' => 'a címre',
    'text.to_locker' => 'a záróba',
    'text.to_office' => 'a hivatalba',
    'waybill.enter.contents' => 'Megrendelés: :order_id',
];
