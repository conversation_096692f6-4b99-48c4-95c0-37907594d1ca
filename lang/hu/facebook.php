<?php

return [
    'action.stop.bot' => '<PERSON><PERSON> le<PERSON>llí<PERSON>',
    'bot.select_option.action.categories' => 'Kategóriák megjelenítése',
    'bot.select_option.action.featured' => 'Kiemelt termékek megjelenítése',
    'bot.select_option.action.latest' => 'Legújabb termékek megjelenítése',
    'bot.select_option.action.plain_text' => 'Egyszerű szöveg',
    'bot.select_option.action.sale' => 'Akciós termékek megjelenítése',
    'bot.select_option.action.search' => 'Termék keresése',
    'bot.select_option.action.start_over' => 'Újrakezdés',
    'bot.select_option.action.vendors' => 'Kereskedők megjelenítése',
    'box.description.abandoned_carts' => 'Ez az üzenet a vásárlók megrendeléseinek befejezésére ösztönöz',
    'box.description.bot.messages' => 'Gyors válaszok lehetővé te<PERSON>, hogy egy készletet bemu<PERSON>sunk, legfeljebb 11 gombbal beszélgetés során',
    'box.description.persistent_menu' => 'A tartós menü beállítható a bot számára, hogy segítse az emberek felfedezését és könnyebb hozzáférést biztosítson a funkcióihoz a beszélgetés során.',
    'box.description.persistent_menu.item' => 'Ez a gomb minden felhasználó tartós menüjének része lesz',
    'box.description.quick_reply' => 'Ez a gomb (gyors válasz) látható lesz a vásárlói számára közvetlenül azután, hogy elkezdik a beszélgetést a messengeren.',
    'box.title.abandoned-cart.discount.plugin.view' => 'A messenger bővítmény nézete elhagyott megrendelésekkel és kedvezményekkel',
    'box.title.abandoned_carts' => 'Üzenetek kezelése az elhagyott kosarakhoz',
    'box.title.bot.checkout.messages' => 'Pénztári üzenetek',
    'box.title.bot.checkout.messages.description' => 'Ez a funkció lehetővé teszi, hogy üzeneteket állítson be, amelyeken keresztül a vásárlói meg tudják vásárolni a termékeket, miközben a botjával csevegnek a messenger chaten',
    'box.title.bot.global_settings' => 'Globális beállítások',
    'box.title.bot.global_settings.description' => 'Ezeken a beállításokon keresztül határozza meg, mikor kell a botnak működnie és válaszolnia a vásárlóira.',
    'box.title.bot.messages' => 'Gyors válaszok',
    'box.title.bot.products_settings' => 'Globális beállítások a termékekhez',
    'box.title.bot.products_settings.description' => 'Ezek a beállítások információi használatosak a termékek megjelenítésekor a Messenger chaten.',
    'box.title.checkbox.welcome_message.description' => 'Ez az üzenet <b>kötelező</b> és automatikusan elküldésre kerül bárkinek, aki feliratkozik a Facebook Messengerre',
    'box.title.checkout.welcome_message' => 'Üdvözlő üzenet',
    'box.title.first_message' => 'Első üzenet',
    'box.title.introductory_message' => 'Bevezető üzenet',
    'box.title.introductory_message.description' => 'Ez az üzenet el lesz küldve a felhasználónak az első kapcsolatfelvételkor a bottal',
    'box.title.main_message' => 'Fő üzenet beállításai',
    'box.title.manual_messages' => 'Kézi üzenetek',
    'box.title.messages' => 'Üzenetek',
    'box.title.persistent_menu' => 'Tartós menü',
    'box.title.persistent_menu.item' => 'Tartós menü gomb',
    'box.title.preview' => 'Üzenet előnézet',
    'box.title.quick_reply' => 'Gyors válasz',
    'box.title.second_message' => 'Második üzenet',
    'box.title.status_changed' => 'Üzenet kezelése a rendelés állapotának megváltozásához',
    'box.title.subscribers' => 'Feliratkozók',
    'box.title.subscribers.message' => 'Üzenet küldése minden Messenger feliratkozónak',
    'box.title.subscribers.message.description' => 'Legyen óvatos a tömeges üzenetek küldésében a feliratkozóinak. A gyakori tömeges üzenetek küldése frusztrálhatja a vásárlókat.',
    'box.title.subscribers_message' => 'Feliratkozókhoz küldendő üzenet kezelése',
    'box.title.thank_you_page.welcome_message' => 'Üdvözlő üzenet a "Köszönjük" oldalról való kapcsolatfelvétel után',
    'box.title.third_message' => 'Harmadik üzenet',
    'box.title.welcome_message' => 'Üdvözlő üzenet',
    'box.title.welcome_message.description' => 'Ez az automatikus üzenet egyszer kerül elküldésre, közvetlenül azután, hogy valaki feliratkozott a Facebook Messengerre.',
    'btn.action' => 'Válassz egy akciót a gomb megnyomása után',
    'btn.add_to_cart.description' => 'Ez a gomb hozzáadja a terméket az ügyfél kosarához',
    'btn.change.data' => 'Adatok módosítása',
    'btn.chat_bot.product.more_info_button' => '',
    'btn.custom_message_sent_to' => 'Küldve:',
    'btn.finish_order.description' => 'Ez a gomb az ügyfelet a pénztári oldalra irányítja az online boltjában',
    'btn.info.generate_discount_with_code' => 'Generáld a kedvezménykódot, amely alkalmazva lesz az üzenetben',
    'btn.info.remove.subscriber' => 'Facebook messenger feliratkozás eltávolítása',
    'btn.remove_from_cart.description' => 'Ez a gomb eltávolítja a terméket az ügyfél kosarából',
    'btn.subscribers.send_message' => 'Küldés',
    'btn.text' => 'Gyors válasz gomb neve',
    'chat.avatar.view.minimized' => 'Minimalizált',
    'chat.avatar.view.opened' => 'Megnyitott',
    'checkout.example_message' => 'Az üzeneted így fog kinézni. 🤗',
    'checkout.message.cart_summary.description' => 'Az alábbi üzenet el lesz küldve az ügyfélnek, hogy ellenőrizzék, hogy a megadott információk érvényesek-e. Használhatod ezeket a változókat: {$first_name}, {$last_name}, {$email}, {$phone}, {$address}',
    'checkout.message.cart_summary.title' => 'Kosár összegzése',
    'checkout.message.choose_another_email.description' => 'Ha az ügyfél azt válaszolja, hogy nem szeretné használni a javasolt neveket, az alábbi üzenetet fogja megkapni.',
    'checkout.message.choose_another_email.title' => 'Az ügyfél másik nevet szeretne használni',
    'checkout.message.choose_parameter.description' => 'Ez az üzenet el lesz küldve az ügyfélnek, amikor a választott terméke különböző változatokkal rendelkezik (szín, méret stb.). Az opciók alatta gombok formájában lesznek, és az ügyfél választhatja ki azokat, amelyek neki tetszenek. Használja a {$parameter} változót a változat nevének meghatározásához.',
    'checkout.message.choose_parameter.title' => 'Válassz termékváltozat üzenet',
    'checkout.message.confirm_name.description' => 'Ez az üzenet el lesz küldve azután, hogy az ügyfél választott egy terméket. Használja ezt a mezőt, hogy kedvesen kérje az ügyfél email címét.',
    'checkout.message.confirm_name.title' => 'Ügyfél nevek megerősítése üzenet',
    'checkout.message.email_exists.description' => 'Ez az üzenet el lesz küldve, ha a felhasználó email címmel válaszol, amely már regisztrálva van az ön boltjában.',
    'checkout.message.email_exists.title' => 'Ugyanazzal az email címmel rendelkező ügyfél létezik',
    'checkout.message.email_request.description' => 'Ez az üzenet el lesz küldve azután, hogy az ügyfél választott egy terméket. Használja ezt a mezőt, hogy kedvesen kérje az ügyfél email címét.',
    'checkout.message.email_request.title' => 'Email cím kérése üzenet',
    'checkout.message.empty_cart.description' => 'Ez az üzenet el lesz küldve az ügyfélnek, ha minden terméket eltávolított a kosarából.',
    'checkout.message.empty_cart.title' => 'Üres kosár üzenet',
    'checkout.message.guest_email_exists.description' => 'Amikor a vásárló email címmel válaszol, a bot ellenőrzi, hogy már rendelkezel-e ezzel az email címmel az üzletedben. Ha az email egy létező vendégfiókhoz van csatlakoztatva, az üzleted már rendelkezik nevével és a szállítási címmel ennek a vendégnek. Használja az alábbi üzenetet, hogy megkérdezze az ügyfelet, hogy megerősíti-e, hogy felhasználhatja az ő korábbi rendeléseivel kapcsolatos információkat ezzel az email címmel.',
    'checkout.message.guest_email_exists.title' => 'Ugyanazzal az email címmel rendelkező vendég létezik üzenet',
    'checkout.message.invalid_address.description' => 'Ha az ügyfél bármilyen más választ ad, mint az, hogy a címet a Google Térképről választaná ki, a bot az alábbi üzenetet fogja küldeni neki.',
    'checkout.message.invalid_address.title' => 'Érvénytelen cím üzenet',
    'checkout.message.invalid_email.description' => 'Ez az üzenet el lesz küldve, ha az ügyfél érvénytelen vagy hiányzó email címmel válaszol. Használja ezt a mezőt, hogy tájékoztassa az ügyfelet, hogy meg kell adnia egy helyes email címet, hogy be tudja fejezni a rendelését.',
    'checkout.message.invalid_email.title' => 'Érvénytelen email üzenet',
    'checkout.message.invalid_name.description' => 'Ha az ügyfél bármilyen más választ ad, mint {first name} {last name} a bot az alábbi üzenetet fogja küldeni neki',
    'checkout.message.invalid_name.title' => 'Érvénytelen névformátum',
    'checkout.message.invalid_phone.description' => 'Ez az üzenet el lesz küldve, ha az ügyfél érvénytelen vagy hiányzó telefonszámmal válaszol. Használja ezt a mezőt, hogy tájékoztassa az ügyfelet, hogy meg kell adnia egy helyes telefonszámot, hogy be tudja fejezni a rendelését.',
    'checkout.message.invalid_phone.title' => 'Érvénytelen telefonszám üzenet',
    'checkout.message.mistake_in_cart_summary.description' => 'Ha az ügyfél azt válaszolja, hogy az információ nem helyes, újra összegyűjteni kell az összes információt a rendeléséről. Használja az alábbi üzenetet, hogy kedvesen kérje az email címét újra.',
    'checkout.message.mistake_in_cart_summary.title' => 'Hiba a kosár információjában üzenet',
    'checkout.message.old_data_confirmed.description' => 'Ha az ügyfél megerősíti, hogy felhasználhatja a meglévő adatokat a korábbi rendeléseiből, megkapja az alábbi üzenetet. Használja ezt a mezőt, hogy tájékoztassa az ügyfelet, hogy szüksége van a telefonszámára a válaszában.',
    'checkout.message.old_data_confirmed.title' => 'Megerősített meglévő információ vendégfiókból',
    'checkout.message.order_summary.description' => 'Ez az üzenet el lesz küldve a vásárlóknak közvetlenül azután, hogy sikeresen befejezték a rendelésüket az online boltjukban. Az üzenet részletes információkat fog tartalmazni a rendeléseikről.',
    'checkout.message.order_summary.title' => 'Rendelés összegzése üzenet',
    'checkout.message.product_listing.description' => 'Az ügyfél megkapja az alábbi üzenetet közvetlenül azután, hogy megerősítette az összes összegyűjtött információt a rendeléséről. A bot bemutatja a megrendelt termékeket és két gombot - egyet a termék eltávolítására és egy másikat a rendelés befejezésére.',
    'checkout.message.product_listing.title' => 'Terméklista',
    'checkout.message.request_address.description' => 'Ez az üzenet el lesz küldve a szállítási cím megkérésére az ön ügyfelétől. Ő láthat egy gombot, ahol választhat a Google Térképen a helyéről',
    'checkout.message.request_address.title' => 'Cím kérés üzenet',
    'checkout.message.request_phone.description' => 'Használja az alábbi mezőt, hogy megírja az automatizált telefonkérő üzenetet az ügyfelének.',
    'checkout.message.request_phone.title' => 'Telefonszám kérés üzenet',
    'checkout.messages.title.address' => 'Cím üzenetek',
    'checkout.messages.title.cart_details' => 'Kosár részletek üzenetek',
    'checkout.messages.title.email' => 'Email üzenetek',
    'checkout.messages.title.name' => 'Név üzenetek',
    'checkout.messages.title.phone' => 'Telefonszám üzenetek',
    'checkout.messages.title.products_details' => 'Termékek részletei üzenetek',
    'connect.title' => 'Kapcsolódás a Facebookhoz',
    'description' => 'A Facebook a világ legnagyobb ingyenes online közösségi hálózati oldala. Most pedig összekapcsolhatja online boltját, hogy gyorsabb és könnyebb kommunikációt biztosítson vásárlóival egy újdonság értékes módon. Az ügyfelei közvetlenül a boltjából tudják felvenni a kapcsolatot a Facebook oldalával.',
    'err.auth.could_not_send_request' => 'Nem tudtuk csatlakoztatni a Facebook fiókját. Kérlek, próbáld újra a következő linken: :connectionUrl.',
    'err.auth.not.connected' => 'Nem vagy csatlakozva az alkalmazáshoz',
    'err.no.pages' => 'Ennek a fióknak nincsenek oldalai',
    'err.only_owner_can_connect' => 'Csak a tulajdonos képes összekapcsolni az online boltját a Facebookkal',
    'err.page_not_published' => 'Nem rendelhetsz hozzá ezt az oldalt, mert ez még nem került közzétételre. Az oldal közzétételéhez menj a Facebook oldal beállításaiba, és tedd nyilvánossá.',
    'info.abandoned.discount' => 'Kedvezmény a kosárhoz',
    'info.abandoned.discount_description' => 'Ha úgy döntesz, hogy kedvezményt szeretnél kedvezménykóddal, a gomb linkje az üzenetben a megadott kedvezménnyel fogja átirányítani az ügyfelet a kosárhoz',
    'info.abandoned.first_message.description' => 'perccel az elhagyott kosár létrejötte után',
    'info.abandoned.message.will_be_sent' => 'Az üzenet el lesz küldve',
    'info.abandoned.second_message.description' => 'perccel az első üzenet elküldése után',
    'info.abandoned.third_message.description' => 'perccel a második üzenet elküldése után',
    'info.bot.action_message' => 'Itt írhatsz egy üzenetet, amelyet a bot mond majd, amikor egy ügyfél rákattint a gyors válasz gombra',
    'info.bot.choose_category_action_message' => 'Ha a kiválasztott kategóriának vannak alkategóriái, de vannak termékek is, akkor az ügyfélnek választania kell. Itt írhatsz egy üzenetet, amelyet a bot kérdez.',
    'info.bot.fallback_message' => 'Visszaesési üzenet',
    'info.bot.fallback_message.description' => 'Ez az üzenet el lesz küldve a felhasználónak, amikor a bot nem talál választ',
    'info.bot.is_active' => 'A botom válaszolni fog minden vásárló facebookos kérdésére',
    'info.bot.is_active_in_shop' => 'A botom akkor is tud válaszolni, amikor a vásárló az üzletemben van',
    'info.bot.key_words' => 'Írj be kulcsszavakat',
    'info.bot.key_words.description' => 'Írj be néhány kulcsszót az alábbi mezőbe, és amikor egy vásárló beír egyet közülük, az üzenet vagy termék el lesz küldve.',
    'info.bot.match_message.description' => 'Ez az üzenet el lesz küldve, ha a felhasználó egy terméket keresett, és van egyezés a keresésében.',
    'info.bot.message.products.button' => 'A gomb neve (gyors válasz) a termékek megjelenítéséhez',
    'info.bot.message.quick_replies' => 'Csatolj egy gyors választ ehhez az üzenethez',
    'info.bot.message.subcategories.button' => 'A gomb neve (gyors válasz) az alkategóriák megjelenítéséhez',
    'info.bot.more_button.description' => 'Minden vásárló egyszerre legfeljebb 8 eredményt láthat. Ha több eredményt szeretne látni, nyomnia kell egy gombot, amely elküldi neki a többi eredményt. Az alábbi mezőben meg kell írni a gomb nevét.',
    'info.bot.more_products_button' => 'A gomb szövege, amelyre az ügyfelek rákattinthatnak, ha több mint 8 terméket szeretnének látni',
    'info.bot.no_match.message' => 'Az üzenet, amit a felhasználód kap, ha nincsenek eredmények',
    'info.bot.persistent_menu_item.actions_description' => 'Ha a "Tiszta szöveget" választod, a vásárló csak az alábbi üzenetet fogja kapni. Ha a "Újrakezdés"-t választod, akkor ezt az üzenetet kapja, de az automatikus üzenetek is hozzá lesznek csatolva. Ha a "Kategóriák megjelenítése"-t választod, a bot meg fogja küldeni neki az összes fő kategóriát.',
    'info.bot.quick_reply_item.actions_description' => 'Ha a "Kategóriák megjelenítése"-t választod, a vásárló megkapja az összes fő kategóriát. A "Kereskedők megjelenítése" kiválasztása minden márkát elküld. Ha a cselekvés "Tiszta szöveget", akkor a felhasználó csak az alábbi üzenetet kapja. Ha a többi akciót választod, az ügyfél megkapja a megadott típust szűrő megadott terméket.',
    'info.bot.show_related' => 'Válaszd ki, ha szeretnéd megjeleníteni a kapcsolódó termékeket',
    'info.bot.subcategory_message' => 'Ez az üzenet elmagyarázza a vásárlóidnak, hogy alkategóriákat mutatsz be nekik',
    'info.dashboard.opened_messages' => 'Megnyitott üzenetek',
    'info.dashboard.purchased_orders' => 'Megvásárolt rendelések',
    'info.dashboard.sent_messages' => 'Elküldött üzenetek',
    'info.message.text' => 'Üzenet szövege',
    'info.messages.paid_with' => 'Fizetve:',
    'info.messages.reminder' => 'Az üzenet el lesz küldve ezt követően',
    'info.order_status_change.message' => 'Értesítési beállítások a rendelés állapotának megváltozásához',
    'info.previous.message.description' => 'Ez az üzenet bevezető, és el lesz küldve a fő üzenet előtt.',
    'info.settings.chat.display' => 'A csevegő avatar ilyen módon fog megjelenni boltjának minden oldalán',
    'info.settings.chat.logged_in_customers.plugin_title' => 'Csevegési cím a bejelentkezett Facebook felhasználók számára',
    'info.settings.chat.not_logged_in_customers.plugin_title' => 'Csevegési cím a Facebookról kijelentkezett felhasználók számára',
    'info.settings.chat.plugin_color' => 'Messenger csevegés háttérszíne',
    'info.settings.checkbox_plugin.display' => 'Ezzel a funkcióval értesítheted az ügyfeleket a rendelésük állapotváltozásáról közvetlenül a Messengeren. A feliratkozási forma a pénztári oldalon fog megjelenni.',
    'info.settings.checkout_page.plugin.mode' => 'Messenger jelölőnégyzet bővítmény a pénztári oldalon',
    'info.settings.checkout_page.plugin.mode.description' => 'Az ügyfelek ezt a formát fogják látni a pénztári oldaluk alján. Üzeneteket csak akkor tudsz küldeni Messengeren, ha bejelölik a jelölőnégyzetet a formában, és rákattintanak a rendelésük befejezésére.',
    'info.settings.choose_discount' => 'Válassz kedvezményt',
    'info.settings.discount_message_description' => 'Ez az üzenet az üdvözlő üzenet után el lesz küldve. A {$cart_url} automatikusan helyettesítésre kerül az ügyfél kosár URL-jével.',
    'info.settings.plugin.mode' => 'Bővítmény nézeti mód',
    'info.settings.plugin.mode.button.description' => 'A felhasználók a Messengerhez a bónusz gomb révén tudnak csatlakozni. Ez a bővítmény alatt fog megjelenni.',
    'info.settings.plugin.mode.description' => 'Ez a forma minden termék részletoldalán vizuálisan megjelenik. Ahhoz, hogy Messenger értesítéseket kapjanak, amikor a rendelésük állapota megváltozik, az ügyfeleknek meg kell nyomniuk a "Küldés Messengert" gombot.',
    'info.settings.plugin.mode.with.discount' => 'Kedvezménnyel',
    'info.settings.plugin.mode.with.discount.description' => 'Ha szeretnéd ösztönözni az ügyfeleidet a Facebook Messengerhez való csatlakozásra, kedvezményt is felajánlhat, amely automatikusan minden feliratkozott kosarára érvényesül.',
    'info.settings.plugin.mode.with.discount.preview' => 'CloudIO előnézet',
    'info.settings.plugin.mode.with.discount.text.description' => 'Ez a szöveg látható lesz a CloudIO fölött. Ezzel könnyen elmagyarázhatod az ügyfeleidnek, miért kellene csatlakozniuk a Facebook Messengerhez.',
    'info.settings.plugin.mode.without.discount' => 'Kedvezmény nélkül',
    'info.settings.recovered_orders_description' => 'Messengerről helyreállított rendelések',
    'info.settings.send_to_messenger.display' => 'Ezzel a funkcióval értesítheted az ügyfeleket a rendelésük állapotváltozásáról közvetlenül a Messengeren. A feliratkozási gomb a "Köszönjük" oldalán fog megjelenni.',
    'info.settings.thank_you__page.plugin.mode' => 'Küldés Messenger gomb a köszönöm oldalon',
    'info.subscribers.link' => 'Feliratkozott felhasználók a messengeren',
    'info.subscribers.message' => 'Új üzenet küldése',
    'info.welcome.message' => 'Üdvözlő üzenet kezelése',
    'info.welcome.message.description' => 'Írd be azt az üzenetet, amelyet szeretnél, hogy a felhasználók megkapjanak, amikor csatlakoznak a Facebook Messengerhez. Ha szeretnéd elküldeni a feliratkozók Facebookon található felhasználónevének üzenetét, akkor használd a {$username} változót.',
    'label.choose.page' => 'Kérlek, válassz ki egyet a Facebookon kezeled oldalak közül. Ez fog kapcsolódni az üzletedhez.',
    'messenger.connect' => 'Kapcsolódás a Facebookhoz',
    'messenger.description' => 'A CloudIO egy olyan eszköz, amely segít az ügyfeleidnek',
    'messenger.title' => 'CloudIO Messenger Csevegő Bot',
    'messenger_bot.header.install' => 'CloudIO Messenger Csevegő Bot telepítése',
    'messenger_bot.help.install' => 'Növeld az értékesítést és a kosárelhagyást a Messenger automatizálásával',
    'messenger_bot.info.description' => '',
    'messenger_bot.info.install' => 'Alakítsd az elhagyott kosarakat eladásokká<br><br>Küldj minden látogatónak személyre szabott üzenetet<br><br>Automatikusan alkalmazd a kedvezménykódot az ügyfél kosarában<br><br>Azonnali üzenetet küldj, ha a rendelés állapota megváltozott<br><br>Egyszerűen csatlakozhatnak a Messenger Chaten keresztül, elhagyott kosaro...Facebook Bejelentkezéssel, rendelésértesítésekkel',
    'messenger_bot.info.title' => 'CloudIO Messenger Csevegő Bot',
    'messenger_bot.title.able_to' => 'A CloudIO Messenger Csevegő Bóttal teheted',
    'messenger_bot.tooltip_info.allowed_persistent_menu_items' => 'Legfeljebb 2 elemet adhatsz hozzá a tartós menühöz',
    'messenger_bot.tooltip_info.allowed_quick_replies' => 'Legfeljebb 11 üzenetet adhatsz hozzá',
    'messenger_chat.header.install' => 'Messenger Csevegés telepítése',
    'messenger_chat.help.install' => 'A Facebook semmit sem hagy figyelmen kívül, hogy a Facebook Messengert fejlett üzleti eszközként bővítse chatbotokkal, Messenger hirdetésekkel, beépített NLP-vel és még sok mással!',
    'messenger_chat.info.description' => '',
    'messenger_chat.info.install' => ' 1. Ha a csevegési támogatásod nem érhető el, kérned kell a felhasználót, hogy adja meg a kapcsolattartási információit. De, ebben az esetben, még ha a csevegő támogatás nem érhető el, akkor a Facebook Messengeren keresztül kapcsolatba léphetsz velük. <br /><br /> 2. A csevegéseket a Messengerben tárolják az alkalmazottak és az ügyfelek könnyebb referenciaszámára. <br /><br /> 3. Chatbotokkal személyre szabhatod a valós csevegéseket, hogy az ügyfelek különleges ajánlatokat ellenőrizhessenek vagy bizonyos weboldalakra navigálhassanak. <br /><br /> 4. A valós csevegések nem segítenek a felhasználói információk megszerzésében, ha félbehagyják a csevegést. Azonban ezzel a bővítménnyel a potenciális ügyfél a messengeren lesz, és később újracélzhatod őket a Facebook közvetítéseivel vagy hirdetéseivel.',
    'messenger_chat.info.title' => 'Messenger Csevegés',
    'messenger_chat.title.able_to' => 'A Facebook Messenger Csevegés lehetővé teszi, hogy:',
    'msg.description.order_status_changed' => 'A %1$s számú rendelés állapota %2$s-ra változott',
    'page.settings' => 'Facebook oldal beállításai',
    'page_settings.description' => 'A Facebookkal való sikeres konfiguráció érdekében válassz egy oldalt az alábbi menüből. A kiválasztott oldal használatban lesz az összes Facebook alkalmazás esetében.',
    'pages.title' => 'Facebook oldalak',
    'send_to_messenger.icon.description' => 'Magyarázd el az ügyfeleidnek, miért kellene csatlakozniuk a Messengert. Ez a gomb csak akkor lesz látható, ha a rendelés állapota befejeződött vagy kérve van.',
    'send_to_messenger.icon.size' => 'Méret',
    'send_to_messenger.icon.size.large' => 'Nagy',
    'send_to_messenger.icon.size.standard' => 'Standard',
    'send_to_messenger.icon.size.xlarge' => 'XLarge',
    'send_to_messenger.icon.view.blue' => 'Kék',
    'send_to_messenger.icon.view.white' => 'Fehér',
    'subscribers.title.old_messages' => 'Üzenet',
    'subscribers.title.sent_date' => 'Elküldés dátuma',
    'subscribers.title.sent_to' => 'Küldve',
    'succ.integration.reset' => 'Az integrációt sikeresen visszaállították',
    'succ.subscriber.remove' => 'Feliratkozó eltávolítva',
    'succ.subscribers.messages_sent' => 'Üzenetek sikeresen elküldve',
    'title' => 'Facebook',
    'tooltip.subscribers.message.periodical' => 'Ez az üzenet rendszeres',
];
