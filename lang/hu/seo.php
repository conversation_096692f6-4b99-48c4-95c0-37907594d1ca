<?php

return [
    'account.addresses.billing.description' => '',
    'account.addresses.billing.title' => '',
    'account.addresses.shipping.description' => '',
    'account.addresses.shipping.title' => '',
    'account.details.description' => '<PERSON>z az oldal, ahol az ügyfelek megtekinthetik a fiókjuk részleteit',
    'account.details.title' => 'Fiók részletei',
    'account.files.description' => '<PERSON>z az oldal, ahol az ügyfelek a fájlok szekciójában találják magukat a fiókjukban',
    'account.files.title' => 'Fájlok',
    'account.order_detail.description' => 'Ez az oldal, ahol az ügyfelek az order details szekcióban találják magukat a fiókjukban',
    'account.order_detail.title' => 'Rendelési részletek',
    'account.orders.description' => 'Ez az oldal, ahol az ügyfelek az order listing szekcióban találj<PERSON> magukat a fiókjukban',
    'account.orders.title' => 'Rendelések',
    'account.payments.description' => 'Ez az oldal, ahol az ügyfelek a kifizetések szekciójában találják magukat a fiókjukban',
    'account.payments.title' => 'Kifizetések',
    'account.wishlist.description' => '',
    'account.wishlist.title' => '',
    'blog.description' => 'Ez az oldal, ahol az ügyfelek a boltod blog fő szekciójában vannak',
    'blog.title' => 'Blog',
    'cart.description' => 'Ezen az oldalon az ügyfelek látják az információkat a szállító kocsijukban',
    'cart.title' => 'Kocsi',
    'category.description' => '',
    'category.title' => '',
    'checkout.address.description' => 'Ez az oldal, ahol az ügyfelek kitöltik a szállítási címüket',
    'checkout.address.title' => 'Cím',
    'checkout.authorize.description' => 'Ez az oldal, ahol az ügyfelek engedélyezik a kifizetést a rendelésükhöz',
    'checkout.authorize.title' => 'Engedélyezés',
    'checkout.checkout.description' => '',
    'checkout.checkout.title' => '',
    'checkout.return.description' => 'Ez az oldal, ahol az ügyfelek láthatják, hogy a rendelésük sikeresen megtörtént',
    'checkout.return.title' => 'Visszatérés',
    'checkout.shipping.description' => 'Ez az oldal, ahol az ügyfelek választhatják ki a szállítási módot',
    'checkout.shipping.title' => 'Szállítás',
    'checkout.verify.description' => 'Ez az oldal, ahol az ügyfelek ellenőrizhetik a rendeléseiket',
    'checkout.verify.title' => 'Ellenőrzés',
    'contacts.description' => 'Ez az oldal, ahol az ügyfelek a boltod kapcsolatok fő szekciójában vannak',
    'contacts.title' => 'Kapcsolatok',
    'description' => 'SEO leírás',
    'err.seo_description_max_chars_%1$s' => 'A megengedett maximum karakterek száma az',
    'err.seo_title_max_chars_%1$s' => 'A \'SEO cím\' maximálisan megengedett karakterei %1$s',
    'err.url_handle_required' => 'URL szükséges',
    'header.seo_configuration' => 'SEO beállítások',
    'home.description' => 'Ez az Ön áruházának kezdőlapja',
    'home.title' => 'Kezdőlap',
    'label.categories' => 'Válassza ki a szerkesztendő kategóriákat. Figyelem! Ha nem választ legalább egyet, minden kategória szerkesztve lesz.',
    'label.products_categories' => 'Válassza ki azokat a kategóriákat, melyek termékeit szeretné szerkeszteni. Figyelem! Ha nem választ kategóriát, minden termék szerkesztve lesz',
    'label.seo_description' => 'SEO meta leírás',
    'label.seo_title' => 'SEO oldalcím',
    'label.vendors' => 'Válassza ki a szerkesztendő beszállítókat. Figyelem! Ha nem választ legalább egyet, minden beszállító szerkesztve lesz',
    'login.description' => 'Ez az oldal, ahol az ügyfelek bejelentkeznek az Ön áruházába',
    'login.title' => 'Bejelentkezés',
    'ph.seo_description' => 'Adjon meg egyedi SEO meta leírást',
    'ph.seo_title' => 'Adjon meg egyedi SEO oldalcímet',
    'product.description' => '',
    'product.title' => '',
    'products.description' => 'Ez az oldal, ahol az ügyfelek az Ön áruházának fő termékszekciójában vannak',
    'products.title' => 'Termékek',
    'register.description' => 'Ez az oldal, ahol az ügyfelek új fiókot hoznak létre az Ön áruházához',
    'register.title' => 'Regisztráció',
    'search.results.description' => 'Ez az oldal, ahol az ügyfelek a keresési szekcióban vannak',
    'search.results.title' => 'Keresés',
    'shops.description' => 'Ez az oldal, ahol az ügyfelek a boltjaik listájánál vannak',
    'shops.title' => 'Boltok',
    'title' => 'SEO cím',
    'vendor.description' => '',
    'vendor.title' => '',
    'vendors.description' => 'Ez az oldal, ahol az ügyfelek az Ön áruházának fő beszállítói szekciójában vannak',
    'vendors.title' => 'Márkák',
    'wishlists.description' => 'Ez az oldal, ahol az ügyfelek a kívánságlistájuknál vannak',
    'wishlists.title' => 'Kívánságlista',
];
