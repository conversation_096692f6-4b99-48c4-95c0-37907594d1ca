<?php

return [
    'add_to_cart.description' => 'С помощью этого виджета вы можете визуализировать кнопку «добавить в корзину» для конкретного продукта',
    'add_to_cart.name' => 'Кнопка «добавить в корзину»',
    'addthis.description' => '',
    'addthis.name' => '',
    'banner.description' => '',
    'banner.label.products_per_row' => '',
    'banner.name' => '',
    'blog.blog.label.per_page' => 'Статей на странице',
    'blog.blog.label.per_row' => 'Статьи в ряду',
    'blog.blog.ph.per_page' => 'войти на страницу',
    'blog.list.description' => '',
    'blog.list.name' => '',
    'blog.recentAtricles.label.count' => 'Количество недавних статей',
    'blog.recentAtricles.ph.count' => 'введите количество недавних статей',
    'blog.recentComments.label.count' => 'Количество последних комментариев',
    'blog.recentComments.ph.count' => 'количество последних комментариев',
    'bundle_products.description' => 'Если вы создали комплектные продукты, этот виджет визуализирует выбранные вами на вашей настраиваемой целевой странице.',
    'bundle_products.name' => 'Раздел комплектов',
    'button.description' => 'С помощью этого виджета вы можете визуализировать простую кнопку',
    'button.name' => 'Кнопка',
    'carousel.description' => '',
    'carousel.name' => '',
    'cart.err.choose_option' => '',
    'code.description' => '',
    'code.name' => '',
    'contact.googleMap.action.add_marker' => ' Добавьте ваш маркер',
    'contact.googleMap.action.manual_add_pin' => 'установите ваш маркер вручную',
    'contact.googleMap.err.invalid_map_markers_format' => 'Формат объекта на карте является недействительным.',
    'contact.googleMap.err.latitude_required_for_all_pins' => 'Широта требуется для всех выводов.',
    'contact.googleMap.err.longitude_required_for_all_pins' => 'Долгота требуется для всех выводов.',
    'contact.googleMap.header.manual_map_marker_add' => 'маркер google map',
    'contact.googleMap.help.mapPreview' => 'кликните в желаемом месте на карте и ваш маркер появится',
    'contact.googleMap.help.marker_caption' => 'добавить описательный заголовок для этого места',
    'contact.googleMap.label.backgroundColor' => 'цвет фона',
    'contact.googleMap.label.disableDefaultUI' => 'отключить интерфейс по умолчанию',
    'contact.googleMap.label.disableDoubleClickZoom' => 'отключить зум по двойному щелчку',
    'contact.googleMap.label.draggable' => 'разрешить перетаскивание карты',
    'contact.googleMap.label.keyboardShortcuts' => 'использовать сочетания клавиш',
    'contact.googleMap.label.mapKey' => '',
    'contact.googleMap.label.mapMaker' => 'картограф',
    'contact.googleMap.label.mapPreview' => 'Просмотр карты',
    'contact.googleMap.label.mapType' => 'Тип карты',
    'contact.googleMap.label.mapTypeControl' => 'Дисплей управления типом карты',
    'contact.googleMap.label.marker_address' => 'Маркер адреса',
    'contact.googleMap.label.marker_caption' => 'Маркер подписи',
    'contact.googleMap.label.noClear' => 'не очищено',
    'contact.googleMap.label.overviewMapControl' => 'Обзор управления картой',
    'contact.googleMap.label.panControl' => 'управление панорамированием',
    'contact.googleMap.label.rotateControl' => 'Повернуть контроль',
    'contact.googleMap.label.scaleControl' => 'Шкала контроля',
    'contact.googleMap.label.scrollwheel' => 'Колесо прокрутки',
    'contact.googleMap.label.streetViewControl' => 'Управление видом улицы',
    'contact.googleMap.label.zoom' => 'Зумировать слайдер карты',
    'contact.googleMap.label.zoomControl' => 'управление зумом',
    'contact.googleMap.ph.backgroundColor' => 'то есть, белый или его HTML-код - #ffffff',
    'contact.googleMap.ph.disableDefaultUI' => 'Выбрать',
    'contact.googleMap.ph.disableDoubleClickZoom' => 'Выбрать',
    'contact.googleMap.ph.draggable' => 'Выбрать',
    'contact.googleMap.ph.keyboardShortcuts' => 'Выбрать',
    'contact.googleMap.ph.mapMaker' => 'Выбрать',
    'contact.googleMap.ph.mapType' => 'Выбрать',
    'contact.googleMap.ph.mapTypeControl' => 'Выбрать',
    'contact.googleMap.ph.noClear' => 'Выбрать',
    'contact.googleMap.ph.overviewMapControl' => 'Выбрать',
    'contact.googleMap.ph.panControl' => 'Выбрать',
    'contact.googleMap.ph.rotateControl' => 'Выбрать',
    'contact.googleMap.ph.scaleControl' => 'Выбрать',
    'contact.googleMap.ph.scrollwheel' => 'выберите',
    'contact.googleMap.ph.streetViewControl' => 'выберите',
    'contact.googleMap.ph.zoomControl' => 'Выбрать',
    'contact.information.label.custom_information' => 'Контактная информация пользователя',
    'contact.information.label.page_text' => 'Текст контактной информации',
    'contact.information.label.show_custom_information' => 'Показать контактную информацию пользователя',
    'contact.information.label.show_form' => 'Показать контактную форму',
    'contact.information.ph.custom_information' => 'введите контактную информацию пользователя',
    'contact.information.ph.page_text' => 'текст страницы',
    'description.bannerInSidebar.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими.',
    'description.banners.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими',
    'description.banners1.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими',
    'description.banners2.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими',
    'description.bannersHomePage.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими',
    'description.bannersTextPage.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими',
    'description.blog.blog.blog' => 'С помощью этого расширения вы контролируете количество блогов (категорий статей), которые будут отображаться на странице',
    'description.blogHome.blog.blog' => 'С помощью этого расширения вы контролируете количество блогов (категорий статей), которые будут отображаться на странице',
    'description.buttonToTop.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.carousel.extra.carousel' => 'С помощью этого расширения вы можете создавать и контролировать тип и свойства слайдеров, отображаемых на главной странице вашего интернет-магазина',
    'description.cartText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.categoryShowcaseBackground.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.checkoutPrice.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.checkoutSideText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина.',
    'description.checkoutSignInGuestText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина.',
    'description.checkoutSignInLoginText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина.',
    'description.checkoutSignInRegisterText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина.',
    'description.checkoutText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.contact.googleMap' => 'Этот модуль позволяет вам просмотреть карту Google с расположением ваших офисов, магазинов или других объектов',
    'description.contactInformation.contact.information' => 'С помощью этого расширения вы управляете контактной формой для ваших клиентов, видимой на странице',
    'description.extra.htmlLine' => 'Промо лента',
    'description.extra.search' => 'С помощью этого модуля вы позволите пользователям получать подсказки о продуктах, которые они ищут',
    'description.filters.product.filters' => 'Настройки для всех страниц со списками товаров. Настройки могут быть Фильтры, Сортировка, Ярлыки продуктов и многое другое. ',
    'description.footerConfiguration.layout.footer' => '',
    'description.footerContacts.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина.',
    'description.footerContent.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина.',
    'description.footerLinks1.navigation.links' => 'С помощью этого расширения вы управляете статическими ссылками, видимыми в верхней части вашего магазина',
    'description.footerLinks2.navigation.links' => 'С помощью этого расширения вы управляете статическими ссылками, видимыми в верхней части вашего магазина',
    'description.footerLinks3.navigation.links' => 'С помощью этого расширения вы управляете статическими ссылками, видимыми в верхней части вашего магазина',
    'description.footerText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.footerTextBackground.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.googleMap.contact.googleMap' => 'Этот модуль позволяет вам просмотреть карту Google с расположением ваших офисов, магазинов или других объектов',
    'description.headerConfiguration.layout.header' => '',
    'description.headerImage1.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.headerImage2.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.headerRight.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина.',
    'description.headerText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.homeSingleBanner.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими.',
    'description.homeText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина.',
    'description.homeText1.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.homeText1Background.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.homeText2.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.homeText2Background.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.homeText3.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.homeText3Background.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.homeTextBackground.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и разделе магазина.',
    'description.homeTopAfterCategoryBackground.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.homeTopBackground.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.homeTopBanner.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.homeTopTextAfterCategoryShowcase.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.homeVideoBackgroundImage.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.homeVideoText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.homeWelcome.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина.',
    'description.latestNewsBackground.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.mailchimp.newsletter' => 'Этот модуль позволяет настроить всплывающие окна для сбора электронных писем',
    'description.navigationLinks.navigation.links' => 'С помощью этого расширения вы управляете статическими ссылками, видимыми в верхней части вашего магазина',
    'description.navigationLinksPage.navigation.links' => 'С помощью этого расширения вы управляете статическими ссылками, видимыми в верхней части вашего магазина',
    'description.newProductsBackground.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.newProductsBanners.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими',
    'description.newsletter.mailchimp.newsletter' => 'Этот модуль позволяет настроить всплывающие окна для сбора электронных писем',
    'description.pageLoader.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.pagesBanner.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими',
    'description.product.filters' => 'Настройки для всех страниц со списками товаров. Настройки могут быть Фильтры, Сортировка, Ярлыки продуктов и многое другое. другие,',
    'description.product.lastViewed' => 'Модуль загружает несколько последних товаров, которые просматривал каждый пользователь',
    'description.product.productInBundles' => 'С помощью этого модуля вы можете выбрать, сколько и сколько продуктов в упаковке будет отображаться на странице сведений о продукте',
    'description.product.productsDetails' => 'Включить и исключить элементы, отображаемые на странице сведений о продукте',
    'description.productShowcaseBanners.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими.',
    'description.productText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.productsCombine.product.related' => 'Благодаря этому расширению вы даете своим клиентам возможность увидеть продукты, похожие на тот, который они открыли однажды',
    'description.productsDetails.product.productsDetails' => 'Включить и исключить элементы, отображаемые на странице сведений о продукте',
    'description.productsRelated.product.related' => 'Благодаря этому расширению вы даете своим клиентам возможность увидеть продукты, похожие на тот, который они открыли однажды',
    'description.productsRelated2.product.related' => 'Благодаря этому расширению вы даете своим клиентам возможность увидеть продукты, похожие на тот, который они открыли однажды',
    'description.recentArticles.blog.recentArticles' => 'С помощью этого расширения вы контролируете количество активных и видимых статей на странице',
    'description.recentComments.blog.recentComments' => 'С помощью этого расширения вы контролируете количество комментариев, которые будут видны под статьями',
    'description.search.extra.search' => 'С помощью этого модуля вы позволите пользователям получать подсказки о продуктах, которые они ищут',
    'description.showcaseBestSellersProducts.product.productShowcase' => 'С помощью этого расширения вы управляете типом и расположением товаров, которые покупатели увидят на главной странице вашего магазина.',
    'description.showcaseBrand.product.showcase' => 'С помощью этого расширения вы можете выбрать, будут ли и как представлены ваши категории и бренды, которые будут отображаться на главной странице вашего магазина',
    'description.showcaseBrands1.product.showcase' => 'С помощью этого расширения вы можете выбрать, будут ли и как представлены ваши категории и бренды, которые будут отображаться на главной странице вашего магазина',
    'description.showcaseBrands2.product.showcase' => 'С помощью этого расширения вы можете выбрать, будут ли и как представлены ваши категории и бренды, которые будут отображаться на главной странице вашего магазина',
    'description.showcaseCategories.product.showcase' => 'С помощью этого расширения вы можете выбрать, будут ли и как представлены ваши категории и бренды, которые будут отображаться на главной странице вашего магазина',
    'description.showcaseCategory.product.showcase' => 'С помощью этого расширения вы можете выбрать, будут ли и как представлены ваши категории и бренды, которые будут отображаться на главной странице вашего магазина',
    'description.showcaseProducts.product.productShowcase' => 'С помощью этого расширения вы управляете типом и расположением товаров, которые покупатели увидят на главной странице вашего магазина',
    'description.showcaseProducts1.product.productShowcase' => 'С помощью этого расширения вы управляете типом и расположением товаров, которые покупатели увидят на главной странице вашего магазина',
    'description.showcaseProducts2.product.productShowcase' => 'С помощью этого расширения вы управляете типом и расположением товаров, которые покупатели увидят на главной странице вашего магазина',
    'description.showcaseProducts3.product.productShowcase' => 'С помощью этого расширения вы управляете типом и расположением товаров, которые покупатели увидят на главной странице вашего магазина',
    'description.showcaseProducts4.product.productShowcase' => 'С помощью этого расширения вы управляете типом и расположением товаров, которые покупатели увидят на главной странице вашего магазина',
    'description.sidebarBanners1.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими',
    'description.sidebarBanners2.extra.banner' => 'С помощью этого расширения вы можете добавлять баннеры в свой магазин и управлять ими',
    'description.sidebarText.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.social.extra.social' => 'Это расширение позволяет вашим покупателям быстро и легко получить доступ ко всем другим профилям магазинов в наиболее часто используемых социальных сетях',
    'description.summaryAdditionalInfo.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина.',
    'description.testimonial.extra.carousel' => 'С помощью этого расширения вы можете создавать и контролировать тип и свойства слайдеров, отображаемых на главной странице вашего интернет-магазина',
    'description.text1.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.text1Background.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.text2.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.text2Background.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.text3.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.text3Background.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.textBoxBackground_1.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.textBoxBackground_2.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.textBoxBackground_3.extra.backgroundImage' => 'С помощью этого расширения вы можете установить базовое изображение магазина, которое будет видно в качестве фона на каждой странице и в разделе магазина',
    'description.textBox_1.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.textBox_2.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.textBox_3.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.textbox1.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.textbox1_tooltip.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.textbox2.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.textbox2_tooltip.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.textbox3.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.textbox3_tooltip.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.textbox4.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'description.textbox4_tooltip.extra.text' => 'С помощью этого расширения вы можете обновить статический текст, видимый на каждой странице магазина',
    'extra.addThisShare.label.custom_toolbar' => 'Код панели инструментов пользователя',
    'extra.addThisShare.label.layout' => 'раскладка',
    'extra.addThisShare.label.og_image_url' => 'Обложка для публикации',
    'extra.addThisShare.label.show_compact' => 'Показать компактно',
    'extra.addThisShare.label.show_counter' => 'Показать счетчик',
    'extra.addThisShare.label.show_top_services' => 'Показать топ-услуги',
    'extra.addThisShare.label.ui_click' => 'UI клик',
    'extra.addThisShare.label.ui_hover_direction' => 'направление выпадающего меню',
    'extra.addThisShare.layout.custom' => 'изготовленный на заказ',
    'extra.addThisShare.layout.large' => 'большой',
    'extra.addThisShare.layout.small' => 'Маленький',
    'extra.addThisShare.ui_hover_direction.bottom' => 'Вниз',
    'extra.addThisShare.ui_hover_direction.top' => 'Вверх',
    'extra.backgroundImage.action.add_external_image' => 'Внешнее изображение',
    'extra.backgroundImage.action.add_image' => 'Добавить фоновое изображение',
    'extra.backgroundImage.action.add_storage_image' => 'Сохраненое изображение',
    'extra.backgroundImage.label.choose_image_type' => 'Тип изображения',
    'extra.backgroundImage.label.color' => '',
    'extra.backgroundImage.label.src' => 'Исходное фоновое изображение',
    'extra.banner.action.add_external_image' => 'внешнее',
    'extra.banner.action.add_image' => 'добавить изображение',
    'extra.banner.action.add_storage_image' => 'из уже имеющихся',
    'extra.banner.blog' => 'блог',
    'extra.banner.blog_article' => 'статья блога',
    'extra.banner.err.script_required' => 'Требуется описание банннера.',
    'extra.banner.external' => 'внешний',
    'extra.banner.image' => 'Изображение',
    'extra.banner.label.amount' => 'Количество',
    'extra.banner.label.banner' => '',
    'extra.banner.label.blank' => 'открыть в новом окне',
    'extra.banner.label.caption' => 'подпись',
    'extra.banner.label.choose_image_type' => 'Выберите изображение',
    'extra.banner.label.enable_gallery' => 'Показывать изображения без ссылки в галерее',
    'extra.banner.label.link' => 'Ссылка',
    'extra.banner.label.script' => 'Описание',
    'extra.banner.label.src' => 'URL-адрес',
    'extra.banner.label.type' => 'Тип баннера',
    'extra.banner.no_link' => 'Нет ссылки',
    'extra.banner.page' => 'страница',
    'extra.banner.ph.caption' => 'введите подпись',
    'extra.banner.ph.link' => 'введите ссылку',
    'extra.banner.ph.script' => 'введите скрипт',
    'extra.banner.ph.select_article' => 'выберите статью',
    'extra.banner.ph.select_blog' => 'выберите блог',
    'extra.banner.ph.select_category' => 'выберите категорию',
    'extra.banner.ph.select_external' => 'выберите внешний',
    'extra.banner.ph.select_product' => 'выберите товар',
    'extra.banner.ph.select_vendor' => 'выберите бренд',
    'extra.banner.ph.type' => 'выбрать тип',
    'extra.banner.product' => 'Товар',
    'extra.banner.product_category' => 'категория',
    'extra.banner.script' => 'скрипт',
    'extra.banner.section' => 'раздел магазина',
    'extra.banner.vendor' => 'марка',
    'extra.carousel.action.add_external_image' => 'добавить изображение из вне',
    'extra.carousel.action.add_image' => 'добавить изображение',
    'extra.carousel.action.add_storage_image' => 'добавить изображение из памяти',
    'extra.carousel.action.add_video' => '',
    'extra.carousel.blog' => 'Ссылка на категорию статьи',
    'extra.carousel.blog_article' => 'Ссылка на конкретную статью',
    'extra.carousel.err.caption_required' => 'Требуется подтверждение слайдера.',
    'extra.carousel.external' => 'Внешний веб-адрес',
    'extra.carousel.help.amount' => 'Выберите количество изображений для слайдера.',
    'extra.carousel.help.animate' => 'Выберите эффект смены слайдов карусели.',
    'extra.carousel.help.autoplay' => 'Включить/отключить автоматическую смену изображений (автовоспроизведение).',
    'extra.carousel.help.caption' => 'Включить/отключить надписи.',
    'extra.carousel.help.controls' => 'Показать/скрыть элементы управления слайдера (следующий, предидущий и т.д.).',
    'extra.carousel.help.cycle' => 'Выберите, должны ли в слайдере крутиться все изображения или нет',
    'extra.carousel.help.full_width' => '',
    'extra.carousel.help.indicators' => 'Включить/отключить индикаторы.',
    'extra.carousel.help.interval' => 'Интервал между каждой сменой изображения (в миллисекундах - учитывая, что значение 1000 равно 1 секунде).',
    'extra.carousel.help.margin' => 'Выберите расстояние между слайдами в пикселях',
    'extra.carousel.help.pause' => 'Включить/выключить паузу в слайдере.',
    'extra.carousel.label.amount' => 'количество',
    'extra.carousel.label.animate' => 'Эффект анимации карусели',
    'extra.carousel.label.autoplay' => 'автовоспроизведение',
    'extra.carousel.label.blank' => 'открыть в новом окне',
    'extra.carousel.label.caption' => 'надпись',
    'extra.carousel.label.choose_image_type' => 'выбрать тип изображения',
    'extra.carousel.label.controls' => 'управления',
    'extra.carousel.label.cycle' => 'цикл',
    'extra.carousel.label.full_width' => '',
    'extra.carousel.label.horizontal_position' => 'Горизонатальное положение содержания слайдера',
    'extra.carousel.label.html' => 'Содержание слайдера для рабочего стола',
    'extra.carousel.label.html_mobile' => 'Содержание слайдера для мобильного устройства',
    'extra.carousel.label.html_tablet' => 'Содержание слайдера для таблета',
    'extra.carousel.label.indicators' => 'показатели',
    'extra.carousel.label.interval' => 'интервал',
    'extra.carousel.label.is_homepage_slider' => 'Слайдер на главной странице',
    'extra.carousel.label.link' => 'ссылка',
    'extra.carousel.label.link_caption' => 'подпись',
    'extra.carousel.label.margin' => 'Расстояние',
    'extra.carousel.label.mobile' => 'Форма для мобильных',
    'extra.carousel.label.pause' => 'пауза',
    'extra.carousel.label.slide' => '',
    'extra.carousel.label.slides_per_view' => 'Слайдов на вид',
    'extra.carousel.label.sorting' => 'Сортировка',
    'extra.carousel.label.src' => 'источник',
    'extra.carousel.label.vertical_position' => 'Вертикальное положение содержания карусели',
    'extra.carousel.label.whole_link' => 'сделать весь слайд ссылкой',
    'extra.carousel.no_link' => 'Без ссылки',
    'extra.carousel.page' => 'Ссылка на конкретную страницу',
    'extra.carousel.ph.caption' => 'выберите',
    'extra.carousel.ph.interval' => 'установить в миллисекундах',
    'extra.carousel.ph.link' => 'то есть, Google',
    'extra.carousel.ph.link_caption' => 'Подпись ссылки',
    'extra.carousel.ph.margin' => 'пкс',
    'extra.carousel.ph.select_article' => 'выберите статью',
    'extra.carousel.ph.select_blog' => 'выберите блог',
    'extra.carousel.ph.select_category' => 'выберите категорию',
    'extra.carousel.ph.select_external' => 'выберите внешний',
    'extra.carousel.ph.select_product' => 'выберите товар',
    'extra.carousel.ph.select_vendor' => 'выберите бренд',
    'extra.carousel.position_bottom' => 'нижнее положение',
    'extra.carousel.position_center' => 'центральное положение',
    'extra.carousel.position_left' => 'Позиция слева',
    'extra.carousel.position_middle' => 'Положение посредине',
    'extra.carousel.position_right' => 'Положение справа',
    'extra.carousel.position_top' => 'Положение сверху',
    'extra.carousel.product' => 'Ссылка на конкретный товар',
    'extra.carousel.product_category' => 'Ссылка на категорию товара',
    'extra.carousel.section' => 'Ссылка на раздел магазина',
    'extra.carousel.vendor' => 'Ссылка на конкретный бренд',
    'extra.social.label.your_%1$s_page' => 'Ваша %1$s страница',
    'extra.text.label.bottom' => 'Низ',
    'extra.text.label.button.float' => '',
    'extra.text.label.button.link' => '',
    'extra.text.label.button.text' => '',
    'extra.text.label.button.title' => '',
    'extra.text.label.period.from' => '',
    'extra.text.label.period.to' => '',
    'extra.text.label.promobar_position' => 'Позиция относительно навигации',
    'extra.text.label.text' => 'Текст',
    'extra.text.label.title' => 'Заглавие',
    'extra.text.label.top' => 'Верх',
    'extra.text.ph.title' => 'введите текст заголовка',
    'global.err.article_no_longer_exists' => 'Статья больше не существует.',
    'global.err.blog_no_longer_exists' => 'Категория статьи больше не существует.',
    'global.err.caption_max_%1$s' => 'Надпись не может содержать более %1$s символов.',
    'global.err.caption_required' => 'Требуется подпись.',
    'global.err.category_no_longer_exists' => 'Категория товара больше не существует.',
    'global.err.invalid_request' => 'Неверный запрос.',
    'global.err.link_caption_required' => 'Требуется надпись для внешнего веб-адреса.',
    'global.err.link_value_required' => 'Требуется внешний веб-адрес.',
    'global.err.page_no_longer_exists' => 'Страница больше не существует.',
    'global.err.product_no_longer_exists' => 'Код товара больше не существует.',
    'global.err.src_required' => 'Требуется внешний источник изображения.',
    'global.err.vendor_no_longer_exists' => 'Бренд больше не существует.',
    'google_map.description' => '',
    'google_map.name' => '',
    'group.banners' => 'Баннеры',
    'group.brands' => 'Бренды',
    'group.categories' => 'Категории',
    'group.footer' => 'Нижний колонтитул',
    'group.header' => 'Заголовок',
    'group.menu' => 'Меню',
    'group.products' => 'Товары',
    'group.show_all' => 'Показать все',
    'group.slider' => 'Ползунок',
    'group.testimonials' => 'Отзывы',
    'group.text_fields' => 'Текстовые поля',
    'group.top_bar' => 'Верхняя панель',
    'instagram.description' => 'С помощью этого виджета вы можете автоматически визуализировать фотографии Instagram в вашем магазине',
    'instagram.name' => 'Фотографии Instagram',
    'layout.buttons.label.border_bottom_left_radius' => 'Радиус нижней левой границы',
    'layout.buttons.label.border_bottom_right_radius' => 'Радиус нижней правой границы',
    'layout.buttons.label.border_top_left_radius' => 'Радиус верхней левой границы',
    'layout.buttons.label.border_top_right_radius' => 'Радиус верхней правой границы',
    'layout.buttons.title.border_radius' => 'Радиус границы',
    'layout.footer.footer_max_cols_count' => 'Максимальное количество колонок в подвале',
    'layout.grid.label.grid_width' => 'Установить ширину сетки',
    'layout.grid.label.grid_width_full' => 'Установить полную ширину сетки',
    'layout.grid.label.offset_desktop' => 'Установить смещение для десктопа',
    'layout.grid.label.offset_mobile' => 'Установить смещение для мобильных',
    'layout.grid.title.grid_settings' => 'Настройки сетки',
    'mailchimp.newsletter.configure_app_first' => 'Пожалуйста, сначала настройте приложение',
    'mailchimp.newsletter.label.automatic' => 'Автоматическое отображение',
    'mailchimp.newsletter.label.delay' => 'Задержка',
    'mailchimp.newsletter.label.description' => 'Описание',
    'mailchimp.newsletter.label.show_form' => 'Показывать форму в футере',
    'mailchimp.newsletter.label.status' => 'Текущий статус подписчика',
    'mailchimp.newsletter.label.title' => 'Заглавие новостной рассылки',
    'mailchimp.newsletter.ph.description' => 'Введите описание',
    'mailchimp.newsletter.ph.title' => 'Введите имя',
    'mailchimp.newsletter.status.pending' => 'Ожидание',
    'mailchimp.newsletter.status.subscribed' => 'Подписан',
    'name.bannerInSidebar.extra.banner' => 'Баннеры в сайдбаре',
    'name.banners.extra.banner' => 'Баннеры',
    'name.bannersHomePage.extra.banner' => 'Баннеры - Индекс',
    'name.bannersTextPage.extra.banner' => 'Баннеры - Текстовая страница',
    'name.blog.blog.blog' => 'Блог',
    'name.blogHome.blog.blog' => 'Главная страница блога',
    'name.buttonToTop.extra.text' => 'Кнопка вверх',
    'name.buttonsConfiguration.layout.button' => 'Настройки кнопок',
    'name.carousel.extra.carousel' => 'Слайдер',
    'name.cartText.extra.text' => 'Текстовая корзина',
    'name.categoryShowcaseBackground.extra.backgroundImage' => 'Витрина с категориями - Фон',
    'name.checkoutPrice.extra.text' => 'Текст цены и доставки при отправке заказа',
    'name.checkoutSideText.extra.text' => 'Текст в боковой колонке корзины',
    'name.checkoutSignInGuestText.extra.text' => 'Текст корзины при Guest',
    'name.checkoutSignInLoginText.extra.text' => 'Текст корзины при входе',
    'name.checkoutSignInRegisterText.extra.text' => 'Текст корзины при регистрации',
    'name.checkoutText.extra.text' => 'Полный заказ - текст',
    'name.contactInformation.contact.information' => 'Контакты',
    'name.extra.htmlLine' => 'Промо-бар',
    'name.filters.product.filters' => 'Настройки каталога товаров',
    'name.footerConfiguration.layout.footer' => 'Настройки стопы',
    'name.footerContacts.extra.text' => 'Контакты нижнего колонтитула',
    'name.footerContent.extra.text' => 'Текст в футере',
    'name.footerLinks1.navigation.links' => 'Ссылки нижнего колонтитула 1',
    'name.footerLinks2.navigation.links' => 'Ссылки навигации в нижнем колонтитуле 2',
    'name.footerLinks3.navigation.links' => 'Ссылки нижнего колонтитула 3',
    'name.footerText.extra.text' => 'Текст нижнего колонтитула',
    'name.footerTextBackground.extra.backgroundImage' => 'Текст нижнего колонтитула - Фон',
    'name.googleMap.contact.googleMap' => 'Google Map',
    'name.headerConfiguration.layout.header' => 'Настройки заголовка',
    'name.headerImage1.extra.backgroundImage' => 'Изображение вереска 1',
    'name.headerImage2.extra.backgroundImage' => 'Изображение вереска 2',
    'name.headerLeft.extra.text' => 'Текст перед логотипом',
    'name.headerRight.extra.text' => 'Текст после логотипа',
    'name.headerText.extra.text' => 'Вересковой текст',
    'name.homeSingleBanner.extra.banner' => 'Баннеры на главной странице',
    'name.homeText.extra.text' => 'Текст главной страницы',
    'name.homeText1.extra.text' => 'Текст главной страницы 1',
    'name.homeText1Background.extra.backgroundImage' => 'Текст главной страницы 1 - фото',
    'name.homeText2.extra.text' => 'Текст главной страницы 2',
    'name.homeText2Background.extra.backgroundImage' => 'Текст главной страницы 2 - фото',
    'name.homeText3.extra.text' => 'Текст главной страницы 3',
    'name.homeText3Background.extra.backgroundImage' => 'Текст главной страницы 3 - фото',
    'name.homeTextBackground.extra.backgroundImage' => 'Текст главной страницы — фон',
    'name.homeTopAfterCategoryBackground.extra.backgroundImage' => 'Текстовый фон баннера после главной страницы с категориями',
    'name.homeTopBackground.extra.backgroundImage' => 'Текстовый фон баннера в верхней части главной страницы',
    'name.homeTopBanner.extra.text' => 'Текст вверху главной страницы',
    'name.homeTopTextAfterCategoryShowcase.extra.text' => 'Текст после окна категории на главной странице',
    'name.homeVideoBackgroundImage.extra.backgroundImage' => 'Изображение раздела видео на главной странице.',
    'name.homeVideoText.extra.text' => 'Текст и видео на главной странице',
    'name.homeWelcome.extra.text' => 'Приветственный текст',
    'name.htmlLine.extra.htmlLine' => 'Промо лента',
    'name.latestNewsBackground.extra.backgroundImage' => 'Последние новости - Предыстория',
    'name.layout.buttons' => 'Настройки кнопок',
    'name.layout.footer' => 'Конфигурация подвала',
    'name.layout.grid' => 'Настройки сетки',
    'name.layout.header' => 'Конфигурация заголовка',
    'name.navigationLinks.navigation.links' => 'Навигационные ссылки',
    'name.navigationLinksPage.navigation.links' => 'Меню Страница - Страницы',
    'name.newProductsBackground.extra.backgroundImage' => 'Новые продукты - Фон',
    'name.newProductsBanners.extra.banner' => 'Баннеры в новых продуктах',
    'name.newsletter.mailchimp.newsletter' => 'Информационный бюллетень (MailChimp)',
    'name.pageLoader.extra.backgroundImage' => 'Индикатор загрузки страницы',
    'name.pagesBanner.extra.banner' => 'Баннер на статических страницах',
    'name.product.lastViewed' => 'Последние просмотренные товары',
    'name.product.linked' => 'Связанные продукты',
    'name.product.productInBundles' => 'Товар в упаковках',
    'name.productShowcaseBanners.extra.banner' => 'Баннеры',
    'name.productText.extra.text' => 'Текст в описании товара',
    'name.productsCombine.product.related' => 'Совпадать с',
    'name.productsDetails.product.productsDetails' => 'Подробная информация о продукте',
    'name.productsRelated.product.related' => 'Сопутствующие товары',
    'name.productsRelated2.product.related' => 'Лучшие товары',
    'name.recentArticles.blog.recentArticles' => 'Последние статьи',
    'name.recentComments.blog.recentComments' => 'Последние комментарии',
    'name.search.extra.search' => 'Поиск',
    'name.showcaseBestSellersProducts.product.productShowcase' => 'Витрина с самыми продаваемыми продуктами',
    'name.showcaseBrand.product.showcase' => 'Витрина с марками',
    'name.showcaseBrands1.product.showcase' => 'Витрина с марками 1',
    'name.showcaseBrands2.product.showcase' => 'Витрина с марками 2',
    'name.showcaseCategories.product.showcase' => 'Витрина категорий',
    'name.showcaseCategory.product.showcase' => 'Витрина категорий',
    'name.showcaseProducts.product.productShowcase' => 'Витрина товаров',
    'name.showcaseProducts1.product.productShowcase' => 'Витрина продукта 1',
    'name.showcaseProducts2.product.productShowcase' => 'Витрина продукта 2',
    'name.showcaseProducts3.product.productShowcase' => 'Витрина продукта 3',
    'name.showcaseProducts4.product.productShowcase' => 'Витрина продукта 4',
    'name.sidebarBanners1.extra.banner' => 'Баннеры',
    'name.sidebarBanners2.extra.banner' => 'Баннеры',
    'name.sidebarText.extra.text' => 'Текст боковой панели',
    'name.social.extra.social' => 'Социальные медиа',
    'name.summaryAdditionalInfo.extra.text' => 'Информация в корзине цен',
    'name.text1.extra.text' => 'Текст главной страницы 1',
    'name.text1Background.extra.backgroundImage' => 'Текст главной страницы 1 – фон',
    'name.text2.extra.text' => 'Текст главной страницы 2',
    'name.text2Background.extra.backgroundImage' => 'Текст главной страницы 2 – фон',
    'name.text3.extra.text' => 'Текст главной страницы 3',
    'name.text3Background.extra.backgroundImage' => 'Текст главной страницы 3 – фон',
    'name.textBoxBackground_1.extra.backgroundImage' => 'Текстовое поле 1 - фон',
    'name.textBoxBackground_2.extra.backgroundImage' => 'Текстовое поле 2 - фон',
    'name.textBoxBackground_3.extra.backgroundImage' => 'Текстовое поле 3 - фон',
    'name.textBox_1.extra.text' => 'Текстовое поле 1',
    'name.textBox_2.extra.text' => 'Текстовое поле 2',
    'name.textBox_3.extra.text' => 'Текстовое поле 3',
    'name.textbox1.extra.text' => 'Текстовое поле 1',
    'name.textbox1_tooltip.extra.text' => 'Текстовое поле 1 - подсказка',
    'name.textbox2.extra.text' => 'Текстовое поле 2',
    'name.textbox2_tooltip.extra.text' => 'Текстовое поле 2 - подсказка',
    'name.textbox3.extra.text' => 'Текстовое поле 3',
    'name.textbox3_tooltip.extra.text' => 'Текстовое поле 3 - подсказка',
    'name.textbox4.extra.text' => 'Текстовое поле 4',
    'name.textbox4_tooltip.extra.text' => 'Текстовое поле 4 - подсказка',
    'navigation.links.action.add' => 'Добавить ссылку',
    'navigation.links.blog' => 'Категория статьи',
    'navigation.links.blog_article' => 'Конкретная статья',
    'navigation.links.external' => 'Внешний адрес',
    'navigation.links.label.blank' => 'Открыть в новом окне',
    'navigation.links.label.icon' => '',
    'navigation.links.label.link' => 'Выберите ссылку',
    'navigation.links.label.link_caption' => 'Подпись ссылки',
    'navigation.links.page' => 'Статическая страница',
    'navigation.links.ph.link' => 'ссылка',
    'navigation.links.ph.link_caption' => 'подпись ссылки',
    'navigation.links.ph.select_article' => 'Выберите статью',
    'navigation.links.ph.select_blog' => 'Выберите категорию статьи',
    'navigation.links.ph.select_category' => 'Выберите категорию товара',
    'navigation.links.ph.select_external' => 'например, http://google.com',
    'navigation.links.ph.select_product' => 'Выбрать товар',
    'navigation.links.ph.select_vendor' => 'Выбрать бренд',
    'navigation.links.product' => 'Конкретный товар',
    'navigation.links.product_category' => 'Категория товара',
    'navigation.links.section' => 'Раздел магазина',
    'navigation.links.vendor' => 'Бренд',
    'newsletter.description' => '',
    'newsletter.name' => '',
    'order-details.description' => 'Вы можете добавить этот виджет на целевую страницу, используемую как страницу благодарности, и каждый ваш клиент увидит точные детали своего заказа.',
    'order-details.name' => 'Детали заказа',
    'product.button.color.active' => 'Активно',
    'product.button.color.default' => 'По умолчанию',
    'product.button.color.secondary' => 'Второстепенный',
    'product.button.label.amount' => 'Количество кнопок для показа',
    'product.button.label.attributes' => 'Дополнительные атрибуты',
    'product.button.label.color' => 'Цвет',
    'product.button.label.full_width' => 'Полная ширина',
    'product.button.label.link' => 'URL',
    'product.button.label.override_text' => 'Переопределить текст кнопки',
    'product.button.label.position' => 'Позиция',
    'product.button.label.size' => 'Размер',
    'product.button.label.target' => 'Открыть в новом окне',
    'product.button.label.text' => 'Текст',
    'product.button.placeholder.attributes' => 'например, data-ajax-panel',
    'product.button.size.default' => 'По умолчанию',
    'product.button.size.large' => 'Большой',
    'product.button.size.small' => 'Маленький',
    'product.button.text.center' => 'Центр',
    'product.button.text.left' => 'Лево',
    'product.button.text.right' => 'Право',
    'product.description' => 'С помощью этого виджета вы можете визуализировать конкретный продукт с подробной информацией',
    'product.linked.label.position' => 'Позиция',
    'product.linked.position.recommended' => 'Как рекомендуемые продукты в разделе «Купить»',
    'product.linked.position.section_recommended' => 'Как раздел «Рекомендуемые продукты»',
    'product.linked.position.variant' => 'Как вариант',
    'product.list.help.template_list_horizontal' => 'Эта опция отображает элементы в списке продуктов в одну строку. Применяется только к версии для настольных компьютеров и к продуктам, которые НЕ находятся в слайдере.',
    'product.list.label.color_product_variants' => 'Показать цвета продуктов',
    'product.list.label.manufacturer_logo_show' => 'Показать логотип производителя',
    'product.list.label.max_title_rows' => 'Установить максимальное количество строк заголовка продукта',
    'product.list.label.per_row_desktop' => 'Продукты в ряду для настольных',
    'product.list.label.per_row_mobile' => 'Продукты в ряду для мобильных',
    'product.list.label.second_image_show' => 'Показать второе изображение при наведении',
    'product.list.label.variants' => 'Показать варианты продукта',
    'product.listing.action.add_per_page_option' => 'добавить варианты \'на страницу\'',
    'product.listing.action.add_price_range' => 'добавить диапазоны цен',
    'product.listing.action.remove_price_range' => 'удалить диапазон цен',
    'product.listing.brand_model' => 'Бренд и модель',
    'product.listing.category_properties' => 'Свойства категории',
    'product.listing.category_properties_limit' => '',
    'product.listing.cation.remove_per_page_option' => 'удалить по странице',
    'product.listing.date' => 'дата',
    'product.listing.enable_category_properties' => '',
    'product.listing.err.from_price_must_be_lower_than_to_price' => 'Цена из должна быть ниже, чем цена в.',
    'product.listing.err.one_or_more_from_or_to_value_missing' => 'Одно или более значений "из" или "в" отсутствуют.',
    'product.listing.err.per_page_value_not_present_in_current_per_page_options' => 'Значение на страницу отсутствует в текущих вариантах на страницу.',
    'product.listing.err.price_range_max_should_be_greater_than_price_range_min' => 'Максимальный диапазон цен должен быть больше, чем минимальный диапазон цен',
    'product.listing.err.price_range_step_should_be_lower_than_price_range_max' => 'Цена шага диапазон должна быть ниже максимального ценового диапазона',
    'product.listing.filters_options' => 'Фильтры для страницы со списком товаров',
    'product.listing.filters_options_order' => 'Сортировка фильтров на странице списка продуктов',
    'product.listing.filters_options_order_info' => 'Вы можете изменить порядок фильтрации в каталоге продуктов, изменив номер сортировки. Чем меньше число, тем выше позиция фильтра.',
    'product.listing.hide_featured' => '',
    'product.listing.hide_sale' => '',
    'product.listing.list_one' => 'Список один',
    'product.listing.list_two' => 'Список два',
    'product.listing.mark_out_of_stock_products' => 'отметить продукты, которых нет в наличии',
    'product.listing.order_by' => 'сортировать по',
    'product.listing.order_by_options' => 'добавить опции \'сортировать по\'',
    'product.listing.order_direction' => 'направление сортировки',
    'product.listing.order_latest_out_of_stock' => 'продукты, которых нет в наличии, в конце списка',
    'product.listing.per_page' => 'товаров на странице',
    'product.listing.per_page_options' => 'Параметры на странице',
    'product.listing.price' => 'цена',
    'product.listing.price_range_max' => 'Максимальный диапазон цен',
    'product.listing.price_range_min' => 'Минимальный диапазон цен',
    'product.listing.price_range_step' => 'шаг ценового диапазона',
    'product.listing.price_ranges_from' => 'диапазон из',
    'product.listing.price_ranges_to' => 'диапазон в',
    'product.listing.products_price_ranges' => 'диапазоны цен товаров',
    'product.listing.remove_price_range' => 'удалить диапазон цен',
    'product.listing.show_out_of_stock_products' => 'продукты, которых нет в наличии',
    'product.listing.show_short_description' => '',
    'product.listing.sort_order' => 'Ручная сортировка',
    'product.name' => 'Продукт',
    'product.productDetails.label.hide_tags' => 'Скрыть теги продукта',
    'product.productDetails.label.pre_selected_variant' => 'Выбран вариант по умолчанию',
    'product.productDetails.label.show_images_in_gallery' => 'Показать изображения продукта в всплывающей галерее',
    'product.productDetails.label.show_product_quantity_in_status' => 'Показать количество продукта в статусе',
    'product.productDetails.label.variant_in_name' => 'Показать выбранный вариант в названии продукта',
    'product.productShowcase.filter_by_all' => 'Все',
    'product.productShowcase.filter_by_category' => 'Фильтровать по категории товара',
    'product.productShowcase.filter_by_product' => 'Фильтровать по видам продукции',
    'product.productShowcase.filter_by_tag' => 'Фильтровать по тегам',
    'product.productShowcase.filter_by_type_bundle' => 'Комплект',
    'product.productShowcase.filter_by_type_digital' => 'Цифровой',
    'product.productShowcase.filter_by_type_physically' => 'Физический',
    'product.productShowcase.filter_by_vendor' => 'Фильтровать по брендам',
    'product.productShowcase.help.color' => '',
    'product.productShowcase.help.icon' => '',
    'product.productShowcase.label.color' => '',
    'product.productShowcase.label.featured' => '',
    'product.productShowcase.label.filter' => 'Фильтры',
    'product.productShowcase.label.icon' => '',
    'product.productShowcase.label.new' => 'Новая',
    'product.productShowcase.label.sale' => 'Продажа',
    'product.productShowcase.label.select_categories' => 'выбрать категории товаров',
    'product.productShowcase.label.select_products' => 'выбрать товары',
    'product.productShowcase.label.select_tags' => 'Выберите теги',
    'product.productShowcase.label.select_vendors' => 'Выберите бренды',
    'product.productShowcase.ph.color' => '',
    'product.productShowcase.ph.featured' => '',
    'product.productShowcase.ph.filter' => 'Фильтр',
    'product.productShowcase.ph.new' => 'новый',
    'product.productShowcase.ph.sale' => 'продажа',
    'product.related.filter_by_category' => 'Фильтровать по категориям',
    'product.related.filter_by_tag' => 'фильтровать по тегам',
    'product.related.filter_by_vendor' => 'Фильтровать по брендам',
    'product.related.help.order_direction' => 'выбрать направление сортировки',
    'product.related.label.order_by' => 'сортировать по',
    'product.related.label.order_direction' => 'направление заказа',
    'product.related.label.out_of_stock' => 'Нет на складе',
    'product.related.label.product_count' => 'Количество товаров',
    'product.related.label.title' => 'Заглавие',
    'product.related.label.type' => 'тип',
    'product.related.order_by_id' => 'упорядочить по ID',
    'product.related.order_by_match' => 'По совокупным итогам',
    'product.related.order_by_name' => 'сортировать по названию',
    'product.related.order_by_price' => 'Цена',
    'product.related.order_by_rand' => 'Случайный',
    'product.related.ph.order_by' => 'сортировать по',
    'product.related.ph.product_count' => 'введите количество товаров, которые, Вы бы хотели, чтобы появлялись как связанные',
    'product.related.ph.title' => 'Сопутствующие товары',
    'product.related.ph.type' => 'Выберите тип',
    'product.search.add_utm' => 'Добавить <b>utm_source</b> в URL',
    'product.search.add_utm.help' => 'Возможность отслеживания продаж в магазине',
    'product.search.label.autocomplete' => '',
    'product.search.label.search_in_product_description' => 'Поиск в описании продуктов',
    'product.selected.err.products_required' => '',
    'product.showcase.category' => 'Категория товара',
    'product.showcase.err.filter_value_required' => 'Требуется указать значение фильтра.',
    'product.showcase.err.more_than_20_categories' => 'Выбранные категории не могут быть более :count',
    'product.showcase.err.more_than_20_products' => 'Продуктов не может быть более :count',
    'product.showcase.err.more_than_20_vendors' => 'Выберите производителей, с которыми вы не можете продолжать более :count',
    'product.showcase.err.one_or_more_categories_no_longer_exist' => 'Некоторые или все выбранные категории не существуют.',
    'product.showcase.err.one_or_more_products_no_longer_exist' => 'Один или более из выбранных товаров не существует.',
    'product.showcase.err.one_or_more_tags_no_longer_exist' => 'Один или более из выбранных тегов не существует.',
    'product.showcase.err.one_or_more_vendors_no_longer_exist' => 'Один или более из выбранных брендов не существует.',
    'product.showcase.label.arrows_position' => 'Позиция стрелок',
    'product.showcase.label.arrows_position.center' => 'Центр',
    'product.showcase.label.arrows_position.top' => 'Верх',
    'product.showcase.label.carousel_options' => 'Опции карусели',
    'product.showcase.label.enable_arrows' => 'Включить стрелки',
    'product.showcase.label.enable_pagination' => 'Включить пагинацию',
    'product.showcase.label.enable_slider' => 'Включить карусель',
    'product.showcase.label.header' => 'заголовок',
    'product.showcase.label.items' => 'Предметы',
    'product.showcase.label.items_options' => 'Опции элемента',
    'product.showcase.label.items_per_row' => '',
    'product.showcase.label.order_by' => 'Сортировать по',
    'product.showcase.label.order_direction' => 'Направление сортировки',
    'product.showcase.label.product_count' => 'Количество товаров',
    'product.showcase.label.products_per_row' => '',
    'product.showcase.label.select' => 'Выбрать элементы витрины',
    'product.showcase.label.space_between' => 'Расстояние между продуктами в пикселях',
    'product.showcase.label.space_between_mobile' => 'Расстояние между продуктами для мобильных в пикселях',
    'product.showcase.label.title' => 'Заглавие',
    'product.showcase.label.type' => 'Тип',
    'product.showcase.order_by_id' => 'Сортировать по дате',
    'product.showcase.order_by_name' => 'Сортировать по названию',
    'product.showcase.order_by_rand' => '',
    'product.showcase.ph.filter' => 'Фильтровать товары по',
    'product.showcase.ph.header' => 'например, Рекомендуемые товары',
    'product.showcase.ph.order_by' => 'Выберите сортировку',
    'product.showcase.ph.order_direction' => 'Выберите направление сортировки',
    'product.showcase.ph.product_count' => 'Введите количество товаров',
    'product.showcase.ph.space_between' => 'Расстояние в пикселях',
    'product.showcase.ph.space_between_mobile' => 'Расстояние в пикселях',
    'product.showcase.ph.title' => 'Введите название',
    'product.showcase.vendor' => 'Бренд',
    'product_showcase.description' => '',
    'product_showcase.name' => '',
    'recent_articles.description' => '',
    'recent_articles.name' => '',
    'row_settings.background-attachment' => 'Эффект параллакса',
    'row_settings.background-position-x' => '',
    'row_settings.background-position-y' => '',
    'row_settings.background-size' => '',
    'row_settings.class_name' => 'Имя класса CSS',
    'row_settings.full_width' => 'Полная ширина',
    'row_settings.joint_background' => 'Применить из предыдущего ряда',
    'row_settings.transparent' => 'Прозрачный',
    'row_settings.margin_bottom' => '',
    'row_settings.margin_side' => 'Боковой отступ (пкс)',
    'row_settings.margin_top' => '',
    'row_settings.padding_bottom' => '',
    'row_settings.padding_side' => 'Боковой отступ (пкс)',
    'row_settings.padding_top' => '',
    'row_settings.reverse_column_order' => 'Обратный порядок столбцов на настольных',
    'row_settings.show_mobile_or_desktop' => 'Показать в',
    'row_settings.type_color' => '',
    'row_settings.vertical_align_class' => 'Выравнивание содержимого',
    'selectedProducts.label.please_add_products' => '',
    'separator.description' => '',
    'separator.label.height' => '',
    'separator.label.style' => '',
    'separator.label.width' => '',
    'separator.name' => '',
    'separator.position.center' => '',
    'separator.position.left' => '',
    'separator.position.right' => '',
    'separator.style.dashed' => '',
    'separator.style.dotted' => '',
    'separator.style.double' => '',
    'separator.style.solid' => '',
    'showcase.description' => '',
    'showcase.name' => '',
    'text-carousel.description' => 'Текстовая карусель - это виджет, где вы можете показывать несколько текстовых полей',
    'text-carousel.name' => 'Текстовая карусель',
    'text.description' => '',
    'text.name' => '',
    'title.description' => '',
    'title.label.tag' => '',
    'title.name' => '',
    'user.account.help.orders_per_page' => 'Заказов на сраницу в аккаунте электронной витрины раздела',
    'user.account.label.files_per_page' => 'Файлов на странице',
    'user.account.label.orders_per_page' => 'Заказов на странице',
    'user.account.label.payments_per_page' => 'Платежей на странице',
    'user.account.ph.files_per_page' => 'вход на страницу',
    'user.account.ph.payments_per_page' => 'вход на страницу',
    'video.description' => '',
    'video.label.autoplay' => 'Автовоспроизведение',
    'video.label.controls' => 'Управление',
    'video.label.loop' => 'Повтор',
    'video.label.src' => '',
    'video.label.type' => '',
    'video.name' => '',
    'yotpo.description' => 'С помощью этого виджета вы можете автоматически визуализировать отзывы Yotpo в вашем магазине',
    'yotpo.name' => 'Отзывы Yotpo',
];
