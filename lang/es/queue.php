<?php

return [
    'global.error.error' => 'El trabajo tiene un error: :error',
    'header.pages' => 'Trabajos en cola',
    'job.title.abandoned_cart_email' => 'Enviar carritos abandonados por correo',
    'job.title.algolia_content' => 'Subiendo contenido a Algolia',
    'job.title.bd_parse' => 'Sincronización de datos de BrandsDistribution',
    'job.title.colibri_parse' => 'Sincronización de datos de Colibri ERP',
    'job.title.currency_sync' => 'Sincronización de moneda',
    'job.title.custom:4910:sync' => 'Sincronización de datos de rivastyle.bg',
    'job.title.custom:9616:insert' => 'Creación y actualización de productos en profisport.bg',
    'job.title.custom:9616:parse' => 'Sincronización de datos de profisport.bg',
    'job.title.customers_import' => 'Formatear clientes desde csv',
    'job.title.delete_csv_tables' => 'Eliminar tablas de importación CSV antiguas',
    'job.title.erp_imports' => 'Importar productos',
    'job.title.etsy_categories' => 'Sincronizar categorías de etsy.com',
    'job.title.etsy_listing' => 'Sincronizar etsy.com',
    'job.title.fb_messenger_abandoned_cart' => 'Enviar carritos abandonados a Facebook Messenger',
    'job.title.gensoft_categories' => 'Formatear categorías desde Gensoft',
    'job.title.gensoft_products' => 'Formatear productos desde Gensoft',
    'job.title.it4profit_parse' => 'Sincronización de datos de Asbis B2B IT4profit',
    'job.title.magento_sync' => 'Sincronizar datos con la tienda de Magento',
    'job.title.mail' => 'Enviar correo electrónico',
    'job.title.mailchimp_newsletter' => 'Sincronización del boletín de MailChimp',
    'job.title.mailchimp_sync' => 'Sincronización de datos de MailChimp',
    'job.title.olx_sync' => 'Sincronización de OLX',
    'job.title.omniship_econt_sync_places' => 'Sincronizar lugares con econt.bg',
    'job.title.omniship_rapido_sync_places' => 'Sincronizar lugares con rapido.bg',
    'job.title.omniship_sync_payments' => 'Sincronizar pagos donde hay "Contrarreembolso"',
    'job.title.periodical_viber_messages' => 'Enviando mensajes de Viber',
    'job.title.polycomp_collect' => 'Sincronización de Polycomp',
    'job.title.polycomp_deleteque' => 'Eliminar productos de Polycomp',
    'job.title.polycomp_disable' => 'Deshabilitar productos de Polycomp',
    'job.title.product_image' => 'Subir imagen del producto',
    'job.title.product_image_color' => 'Parsear colores de la imagen del producto',
    'job.title.products_import' => 'Formatear productos desde csv',
    'job.title.remove_delete_files' => 'Eliminar archivos borrados del almacenamiento',
    'job.title.set_customer_to_segment' => 'Agregar clientes a segmentos',
    'job.title.shopify_products' => 'Formateo de productos de Shopify',
    'job.title.statistic' => 'Estadísticas para registros',
    'job.title.variant_image' => 'Subir imagen de variante',
    'job.title.versus_erp_collect' => 'Sincronizando productos de VersusERP',
    'job.title.viber_messages' => 'Enviando mensajes de Viber',
    'job.title.woocommerce_products' => 'Formatear productos de WooCommerce',
    'job.title.workflow_import_quantities' => 'Formateo de variantes de productos de Workflow',
    'job.title.workflow_order_export' => 'Exportar pedidos de CloudCart a Workflow',
    'job.title.workflow_products' => 'Formateo de productos de Workflow',
    'job.title.xml_feed_upload_arukereso' => 'Generando archivo XML de Arukereso.hu',
    'job.title.xml_feed_upload_channelsight' => 'Generando archivo XML de ChannelSight',
    'job.title.xml_feed_upload_cloudcart' => 'Generando archivo XML de CloudCart',
    'job.title.xml_feed_upload_compari' => 'Generando archivo XML de Compari.ro',
    'job.title.xml_feed_upload_criteo' => 'Generando archivo XML de Criteo',
    'job.title.xml_feed_upload_emag' => 'Generando archivo XML de Productos de CloudCart para eMAG',
    'job.title.xml_feed_upload_facebook' => 'Generando archivo XML de Anuncios Dinámicos de Facebook',
    'job.title.xml_feed_upload_google' => 'Generando archivo XML de Google Merchant',
    'job.title.xml_feed_upload_pazaruvaj' => 'Generando archivo XML de Pazaruvaj.com',
    'job.title.xml_feed_upload_profitshare' => 'Generando archivo CSV de Profitshare',
    'job.title.xml_feed_upload_retargeting' => 'Generando archivo CSV de Retargeting.biz',
    'job.title.xml_feed_upload_shopmania' => 'Generando archivo XML de Shopmania.com',
    'job.title.xml_feed_upload_shopzilla' => 'Generando archivo XML de ShopZilla.com',
    'job.title.xml_feed_upload_sravni' => 'Generando archivo XML de Sravni.bg',
    'job.title.xml_feed_upload_trendo' => 'Generando archivo XML de Trendo.bg',
    'job.title.xml_import_insert' => '"Importación XML" crear y actualizar productos',
    'job.title.xml_import_parse' => '"Importación XML" sincronización de datos',
    'job.title.xml_sync_insert' => '"Sincronización XML" actualizar productos',
    'job.title.xml_sync_parse' => '"Sincronización XML" sincronización de datos',
    'job.title.zora:active' => 'cambio de estado de productos de zora.bg desde Atlas ERP',
    'job.title.zora:qty' => 'cambio de cantidades de productos de zora.bg desde Atlas ERP',
    'job.title.zora:synchronisation' => 'sincronización de datos de productos de zora.bg',
    'job.title.zora:synchronisation_bundle' => 'sincronización de datos de paquetes de zora.bg',
    'job.title.zora:synchronisation_bundle_insert' => 'zora.bg crear y actualizar paquetes',
    'job.title.zora:synchronisation_insert' => 'zora.bg crear y actualizar productos',
    'success.re-init' => 'El trabajo se reinició con éxito',
    'th.is_running' => 'Está en ejecución',
    'th.last_run' => 'Última ejecución',
    'th.next_run' => 'Próxima ejecución',
    'th.title' => 'Título',
    'with_total_subtask' => ':total sub-tarea|:total sub-tareas',
];
