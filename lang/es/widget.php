<?php

return [
    'add_to_cart.description' => 'Con este widget puedes visualizar el botón "agregar al carrito" para un producto específico',
    'add_to_cart.name' => 'Botón "agregar al carrito"',
    'addthis.description' => '',
    'addthis.name' => '',
    'banner.description' => 'Agrega fácilmente imágenes, banners o un fragmento simple de Javascript a la página',
    'banner.label.products_per_row' => 'Imágenes a mostrar por fila',
    'banner.name' => 'Mostrar Imágenes',
    'blog.blog.label.per_page' => 'Artículos por página',
    'blog.blog.label.per_row' => 'Artículos por fila',
    'blog.blog.ph.per_page' => 'Ingresa por página',
    'blog.list.description' => '',
    'blog.list.name' => '',
    'blog.recentAtricles.label.count' => 'Cantidad de artículos recientes',
    'blog.recentAtricles.ph.count' => 'Ingresa la cantidad de artículos recientes',
    'blog.recentComments.label.count' => 'Cantidad de comentarios recientes',
    'blog.recentComments.ph.count' => 'Cantidad de comentarios recientes',
    'bundle_products.description' => 'Si has creado productos de paquete, este widget visualizará los que elijas en tu página de aterrizaje personalizada.',
    'bundle_products.name' => 'Sección de Paquetes',
    'button.description' => 'Con este widget puedes visualizar un botón simple',
    'button.name' => 'Botón',
    'carousel.description' => 'El carrusel es un widget donde puedes mostrar múltiples imágenes una tras otra',
    'carousel.name' => 'Carrusel',
    'cart.err.choose_option' => 'Por favor elige una variación',
    'code.description' => 'Agrega un código simple a tu página. Puedes agregar fragmentos de Javascript o HTML para mostrarlo en tu página.',
    'code.name' => 'Agregar Fragmentos de Código',
    'contact.googleMap.action.add_marker' => 'Añade tu marcador',
    'contact.googleMap.action.manual_add_pin' => 'Establece tu marcador manualmente',
    'contact.googleMap.err.invalid_map_markers_format' => 'El formato de los marcadores del mapa es inválido.',
    'contact.googleMap.err.latitude_required_for_all_pins' => 'Se requiere la latitud para todos los marcadores.',
    'contact.googleMap.err.longitude_required_for_all_pins' => 'Se requiere la longitud para todos los marcadores.',
    'contact.googleMap.header.manual_map_marker_add' => 'Creador de mapas de Google',
    'contact.googleMap.help.mapPreview' => 'Haz clic en el lugar deseado en el mapa y tu marcador aparecerá',
    'contact.googleMap.help.marker_caption' => 'Agrega un título descriptivo para ese lugar',
    'contact.googleMap.label.backgroundColor' => 'Color de fondo',
    'contact.googleMap.label.disableDefaultUI' => 'Desactivar UI predeterminado',
    'contact.googleMap.label.disableDoubleClickZoom' => 'Desactivar zoom de doble clic',
    'contact.googleMap.label.draggable' => 'Habilitar arrastre del mapa',
    'contact.googleMap.label.keyboardShortcuts' => 'Usar atajos de teclado',
    'contact.googleMap.label.mapKey' => 'Clave API de Google',
    'contact.googleMap.label.mapMaker' => 'Creador de mapas',
    'contact.googleMap.label.mapPreview' => 'Vista previa del mapa',
    'contact.googleMap.label.mapType' => 'Tipo de mapa',
    'contact.googleMap.label.mapTypeControl' => 'Mostrar control del tipo de mapa',
    'contact.googleMap.label.marker_address' => 'Dirección del marcador',
    'contact.googleMap.label.marker_caption' => 'Título del marcador',
    'contact.googleMap.label.noClear' => 'No limpiar',
    'contact.googleMap.label.overviewMapControl' => 'Control de vista general del mapa',
    'contact.googleMap.label.panControl' => 'Control de panorámica',
    'contact.googleMap.label.rotateControl' => 'Control de rotación',
    'contact.googleMap.label.scaleControl' => 'Control de escala',
    'contact.googleMap.label.scrollwheel' => 'Rueda de desplazamiento',
    'contact.googleMap.label.streetViewControl' => 'Control de vista de calle',
    'contact.googleMap.label.zoom' => 'Deslizador de zoom del mapa',
    'contact.googleMap.label.zoomControl' => 'Control de zoom',
    'contact.googleMap.ph.backgroundColor' => 'Ej. blanco o el código html de él - #ffffff',
    'contact.googleMap.ph.disableDefaultUI' => 'Elegir',
    'contact.googleMap.ph.disableDoubleClickZoom' => 'Elegir',
    'contact.googleMap.ph.draggable' => 'Elegir',
    'contact.googleMap.ph.keyboardShortcuts' => 'Elegir',
    'contact.googleMap.ph.mapMaker' => 'Elegir',
    'contact.googleMap.ph.mapType' => 'Elegir',
    'contact.googleMap.ph.mapTypeControl' => 'Elegir',
    'contact.googleMap.ph.noClear' => 'Elegir',
    'contact.googleMap.ph.overviewMapControl' => 'Elegir',
    'contact.googleMap.ph.panControl' => 'Elegir',
    'contact.googleMap.ph.rotateControl' => 'Elegir',
    'contact.googleMap.ph.scaleControl' => 'Elegir',
    'contact.googleMap.ph.scrollwheel' => 'Elegir',
    'contact.googleMap.ph.streetViewControl' => 'Elegir',
    'contact.googleMap.ph.zoomControl' => 'Elegir',
    'contact.information.label.custom_information' => 'Información de contacto personalizada',
    'contact.information.label.page_text' => 'Texto de información de contacto',
    'contact.information.label.show_custom_information' => 'Mostrar información de contacto personalizada',
    'contact.information.label.show_form' => 'Mostrar formulario de contacto',
    'contact.information.ph.custom_information' => 'Ingresa tu información de contacto personalizada',
    'contact.information.ph.page_text' => 'Texto de la página',
    'description.bannerInSidebar.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.banners.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.banners1.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.banners2.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.bannersHomePage.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.bannersTextPage.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.blog.blog.blog' => 'Con esta extensión controlas el número de blogs (categorías de artículos) que serán visibles en una página',
    'description.blogHome.blog.blog' => 'Con esta extensión controlas el número de blogs (categorías de artículos) que serán visibles en una página',
    'description.buttonToTop.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.carousel.extra.carousel' => 'Con esta extensión puedes crear y controlar el tipo y propiedades de los deslizadores visibles en la página de inicio de tu tienda en línea',
    'description.cartText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.categoryShowcaseBackground.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.checkoutPrice.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.checkoutSideText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.checkoutSignInGuestText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.checkoutSignInLoginText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.checkoutSignInRegisterText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.checkoutText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.contact.googleMap' => 'Este módulo te permite previsualizar un mapa de Google con las ubicaciones de tus oficinas, tiendas u otros sitios',
    'description.contactInformation.contact.information' => 'Con esta extensión controlas el formulario de contacto para tus clientes, visible en la página',
    'description.extra.htmlLine' => 'Barra promocional',
    'description.extra.search' => 'Con este módulo permitirás a los usuarios recibir sugerencias sobre productos que están buscando',
    'description.footerConfiguration.layout.footer' => '',
    'description.footerContacts.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.footerContent.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.footerLinks1.navigation.links' => 'Con esta extensión, controlas los enlaces estáticos visibles en la parte superior de tu tienda.',
    'description.footerLinks2.navigation.links' => 'Con esta extensión, controlas los enlaces estáticos visibles en la parte superior de tu tienda.',
    'description.footerLinks3.navigation.links' => 'Con esta extensión, controlas los enlaces estáticos visibles en la parte superior de tu tienda.',
    'description.footerText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.footerTextBackground.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.headerConfiguration.layout.header' => '',
    'description.headerImage1.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.headerImage2.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.headerRight.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.headerText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.homeSingleBanner.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.homeText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.homeText1.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.homeText1Background.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.homeText2.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.homeText2Background.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.homeText3.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.homeText3Background.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.homeTextBackground.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.homeTopAfterCategoryBackground.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.homeTopBackground.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.homeTopBanner.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.homeTopTextAfterCategoryShowcase.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.homeVideoBackgroundImage.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.homeVideoText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.homeWelcome.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.latestNewsBackground.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.mailchimp.newsletter' => 'Este módulo te permite hacer configuraciones emergentes para recoger correos electrónicos',
    'description.navigationLinks.navigation.links' => 'Con esta extensión, controlas los enlaces estáticos visibles en la parte superior de tu tienda.',
    'description.navigationLinksPage.navigation.links' => 'Con esta extensión, controlas los enlaces estáticos visibles en la parte superior de tu tienda.',
    'description.newProductsBackground.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.newProductsBanners.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.pageLoader.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.pagesBanner.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.product.filters' => 'Configuraciones para todas las páginas que muestran listas de productos. Las configuraciones pueden ser Filtros, Ordenar, Etiquetas de producto y más.',
    'description.product.lastViewed' => 'El módulo carga los últimos productos que cada usuario ha visto',
    'description.product.productInBundles' => 'A través de este módulo, puedes elegir cuántos y cuántos productos por paquete mostrar en la página de detalle del producto',
    'description.productShowcaseBanners.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.productText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.productsCombine.product.related' => 'A través de esta extensión, le das a tus clientes la oportunidad de ver productos similares al que abren una vez,',
    'description.productsDetails.product.productsDetails' => 'Incluir y excluir artículos que aparecen en la página de detalle del producto',
    'description.productsRelated.product.related' => 'A través de esta extensión, le das a tus clientes la oportunidad de ver productos similares al que abren una vez,',
    'description.productsRelated2.product.related' => 'A través de esta extensión, le das a tus clientes la oportunidad de ver productos similares al que abren una vez,',
    'description.recentArticles.blog.recentArticles' => 'Con esta extensión controlas la cantidad de artículos activos y visibles en la página',
    'description.recentComments.blog.recentComments' => 'A través de esta extensión controlas la cantidad de comentarios que serán visibles debajo de los artículos',
    'description.showcaseBestSellersProducts.product.productShowcase' => 'Con esta extensión controlas el tipo y disposición de productos que los clientes verán en la página principal de tu tienda',
    'description.showcaseBrand.product.showcase' => 'Con esta extensión puedes elegir si y cómo presentar tus categorías y marcas, que serán visibles en la página principal de tu tienda',
    'description.showcaseBrands1.product.showcase' => 'Con esta extensión puedes elegir si y cómo presentar tus categorías y marcas, que serán visibles en la página principal de tu tienda',
    'description.showcaseBrands2.product.showcase' => 'Con esta extensión puedes elegir si y cómo presentar tus categorías y marcas, que serán visibles en la página principal de tu tienda',
    'description.showcaseCategories.product.showcase' => 'Con esta extensión puedes elegir si y cómo presentar tus categorías y marcas, que serán visibles en la página principal de tu tienda',
    'description.showcaseCategory.product.showcase' => 'Con esta extensión puedes elegir si y cómo presentar tus categorías y marcas, que serán visibles en la página principal de tu tienda',
    'description.showcaseProducts.product.productShowcase' => 'A través de esta extensión controlas el tipo y disposición de productos que los clientes verán en la página principal de tu tienda',
    'description.showcaseProducts1.product.productShowcase' => 'A través de esta extensión controlas el tipo y disposición de productos que los clientes verán en la página principal de tu tienda',
    'description.showcaseProducts2.product.productShowcase' => 'A través de esta extensión controlas el tipo y disposición de productos que los clientes verán en la página principal de tu tienda',
    'description.showcaseProducts3.product.productShowcase' => 'A través de esta extensión controlas el tipo y disposición de productos que los clientes verán en la página principal de tu tienda',
    'description.showcaseProducts4.product.productShowcase' => 'A través de esta extensión controlas el tipo y disposición de productos que los clientes verán en la página principal de tu tienda',
    'description.sidebarBanners1.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.sidebarBanners2.extra.banner' => 'Con esta extensión puedes agregar y gestionar banners en tu tienda',
    'description.sidebarText.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.social.extra.social' => 'Esta extensión permite un acceso rápido y fácil para tus clientes a todos los demás perfiles de tienda en las redes sociales más utilizadas',
    'description.summaryAdditionalInfo.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.testimonial.extra.carousel' => 'Con esta extensión puedes crear y controlar el tipo y propiedades de los deslizadores visibles en la página de inicio de tu tienda en línea',
    'description.text1.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.text1Background.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.text2.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.text2Background.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.text3.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.text3Background.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.textBoxBackground_1.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.textBoxBackground_2.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.textBoxBackground_3.extra.backgroundImage' => 'Con esta extensión puedes establecer una imagen básica de la tienda, que será visible como fondo en cada página y sección de la tienda',
    'description.textBox_1.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.textBox_2.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.textBox_3.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.textbox1.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.textbox1_tooltip.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.textbox2.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.textbox2_tooltip.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.textbox3.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.textbox3_tooltip.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.textbox4.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'description.textbox4_tooltip.extra.text' => 'Con esta extensión puedes actualizar el texto estático visible en cada página de la tienda',
    'extra.addThisShare.label.custom_toolbar' => 'Código de barra de herramientas personalizada',
    'extra.addThisShare.label.layout' => 'Diseño',
    'extra.addThisShare.label.og_image_url' => 'Imagen de portada para compartir',
    'extra.addThisShare.label.show_compact' => 'Mostrar compacto',
    'extra.addThisShare.label.show_counter' => 'Mostrar contador',
    'extra.addThisShare.label.show_top_services' => 'Mostrar servicios destacados',
    'extra.addThisShare.label.ui_click' => 'Click UI',
    'extra.addThisShare.label.ui_hover_direction' => 'Dirección del cuadro desplegable',
    'extra.addThisShare.layout.custom' => 'Personalizado',
    'extra.addThisShare.layout.large' => 'Grande',
    'extra.addThisShare.layout.small' => 'Pequeño',
    'extra.addThisShare.ui_hover_direction.bottom' => 'Abajo',
    'extra.addThisShare.ui_hover_direction.top' => 'Arriba',
    'extra.backgroundImage.action.add_external_image' => 'Imagen externa',
    'extra.backgroundImage.action.add_image' => 'Agregar imagen de fondo',
    'extra.backgroundImage.action.add_storage_image' => 'Imagen de almacenamiento',
    'extra.backgroundImage.label.choose_image_type' => 'Tipo de fondo',
    'extra.backgroundImage.label.color' => 'Selector de color',
    'extra.backgroundImage.label.src' => 'Fuente de imagen de fondo',
    'extra.banner.action.add_external_image' => 'Externo',
    'extra.banner.action.add_image' => 'Agregar imagen',
    'extra.banner.action.add_storage_image' => 'Desde almacenamiento',
    'extra.banner.blog' => 'Categoría del blog',
    'extra.banner.blog_article' => 'Artículo del blog',
    'extra.banner.err.script_required' => 'Se requiere código recortado',
    'extra.banner.external' => 'Externo',
    'extra.banner.image' => 'Imagen',
    'extra.banner.label.amount' => 'Número de imágenes a mostrar',
    'extra.banner.label.banner' => 'Imagen',
    'extra.banner.label.blank' => 'Abrir en nueva ventana',
    'extra.banner.label.caption' => 'Título',
    'extra.banner.label.choose_image_type' => 'Elegir una imagen',
    'extra.banner.label.enable_gallery' => 'Mostrar imágenes, que están sin un enlace, en galería',
    'extra.banner.label.link' => 'Enlace',
    'extra.banner.label.script' => 'Código recortado',
    'extra.banner.label.src' => 'Dirección URL',
    'extra.banner.label.type' => 'Tipo',
    'extra.banner.no_link' => 'Sin enlace',
    'extra.banner.page' => 'Página',
    'extra.banner.ph.caption' => 'Ingresa el título',
    'extra.banner.ph.link' => 'Ingresa el enlace',
    'extra.banner.ph.script' => 'Aquí puedes agregar un simple código recortado como Javascript o HTML',
    'extra.banner.ph.select_article' => 'Seleccionar artículo',
    'extra.banner.ph.select_blog' => 'Seleccionar categoría de blog',
    'extra.banner.ph.select_category' => 'Seleccionar categoría',
    'extra.banner.ph.select_external' => 'Seleccionar externo',
    'extra.banner.ph.select_product' => 'Seleccionar producto',
    'extra.banner.ph.select_vendor' => 'Seleccionar proveedor',
    'extra.banner.ph.type' => 'Elegir tipo',
    'extra.banner.product' => 'Producto',
    'extra.banner.product_category' => 'Categoría',
    'extra.banner.script' => 'Fragmento',
    'extra.banner.section' => 'Colección Inteligente',
    'extra.banner.vendor' => 'Marca',
    'extra.carousel.action.add_external_image' => 'Agregar imagen externa',
    'extra.carousel.action.add_image' => 'Agregar imagen',
    'extra.carousel.action.add_storage_image' => 'Agregar imagen de almacenamiento',
    'extra.carousel.action.add_video' => 'Agregar video de YouTube, Vimeo o Wistia',
    'extra.carousel.blog' => 'Enlace a una categoría de artículo',
    'extra.carousel.blog_article' => 'Enlace a un artículo específico',
    'extra.carousel.err.caption_required' => 'La leyenda del carrusel es requerida.',
    'extra.carousel.external' => 'Dirección web externa',
    'extra.carousel.help.amount' => 'Elegir cantidad de imágenes para el carrusel.',
    'extra.carousel.help.animate' => 'Elegir efecto de cambio de diapositiva de carrusel.',
    'extra.carousel.help.autoplay' => 'Habilitar/deshabilitar cambio automático de imagen (autoplay).',
    'extra.carousel.help.caption' => 'Habilitar/deshabilitar leyendas.',
    'extra.carousel.help.controls' => 'Mostrar/ocultar controles del carrusel (siguiente, anterior, etc.).',
    'extra.carousel.help.cycle' => 'Elegir si el carrusel debe repetir todas las imágenes.',
    'extra.carousel.help.full_width' => 'Habilitar ancho completo del deslizador',
    'extra.carousel.help.indicators' => 'Habilitar/deshabilitar indicadores.',
    'extra.carousel.help.interval' => 'El intervalo entre cada cambio de imagen (en milisegundos - por ejemplo, un valor de 1000 es igual a 1 segundo).',
    'extra.carousel.help.margin' => 'Selecciona la distancia entre las diapositivas en px',
    'extra.carousel.help.pause' => 'Habilitar/deshabilitar pausa del carrusel.',
    'extra.carousel.label.amount' => 'Cantidad',
    'extra.carousel.label.animate' => 'Efecto de animación del carrusel',
    'extra.carousel.label.autoplay' => 'Autoplay',
    'extra.carousel.label.blank' => 'Abrir en nueva ventana',
    'extra.carousel.label.caption' => 'Título',
    'extra.carousel.label.choose_image_type' => 'Elegir tipo de imagen',
    'extra.carousel.label.controls' => 'Controles',
    'extra.carousel.label.cycle' => 'Ciclo',
    'extra.carousel.label.full_width' => 'Ancho completo',
    'extra.carousel.label.horizontal_position' => 'Posición horizontal del contenido del carrusel',
    'extra.carousel.label.html' => 'Contenido del carrusel para escritorio',
    'extra.carousel.label.html_mobile' => 'Contenido del carrusel para móvil',
    'extra.carousel.label.html_tablet' => 'Contenido del carrusel para tabletas',
    'extra.carousel.label.indicators' => 'Indicadores',
    'extra.carousel.label.interval' => 'Intervalo',
    'extra.carousel.label.is_homepage_slider' => 'Es deslizador de la página principal',
    'extra.carousel.label.link' => 'Enlace',
    'extra.carousel.label.link_caption' => 'Título',
    'extra.carousel.label.margin' => 'Espaciado',
    'extra.carousel.label.mobile' => 'Formulario móvil',
    'extra.carousel.label.pause' => 'Pausa',
    'extra.carousel.label.slide' => 'Diapositiva',
    'extra.carousel.label.slides_per_view' => 'Diapositivas por vista',
    'extra.carousel.label.sorting' => 'Ordenar',
    'extra.carousel.label.src' => 'Origen',
    'extra.carousel.label.vertical_position' => 'Posición vertical del contenido del carrusel',
    'extra.carousel.label.whole_link' => 'Hacer toda la diapositiva un enlace',
    'extra.carousel.no_link' => 'Sin enlace',
    'extra.carousel.page' => 'Enlace a una página específica',
    'extra.carousel.ph.caption' => 'Elegir',
    'extra.carousel.ph.interval' => 'Configurar en milisegundos',
    'extra.carousel.ph.link' => 'Ej. Google',
    'extra.carousel.ph.link_caption' => 'Título del enlace',
    'extra.carousel.ph.margin' => 'px',
    'extra.carousel.ph.select_article' => 'Seleccionar artículo',
    'extra.carousel.ph.select_blog' => 'Seleccionar blog',
    'extra.carousel.ph.select_category' => 'Seleccionar categoría',
    'extra.carousel.ph.select_external' => 'Seleccionar externo',
    'extra.carousel.ph.select_product' => 'Seleccionar producto',
    'extra.carousel.ph.select_vendor' => 'Seleccionar proveedor',
    'extra.carousel.position_bottom' => 'Posición abajo',
    'extra.carousel.position_center' => 'Posición centro',
    'extra.carousel.position_left' => 'Posición izquierda',
    'extra.carousel.position_middle' => 'Posición media',
    'extra.carousel.position_right' => 'Posición derecha',
    'extra.carousel.position_top' => 'Posición arriba',
    'extra.carousel.product' => 'Enlace a producto específico',
    'extra.carousel.product_category' => 'Enlace a una categoría de producto',
    'extra.carousel.section' => 'Enlace a una sección de la tienda',
    'extra.carousel.vendor' => 'Enlace a un proveedor específico',
    'extra.social.label.your_%1$s_page' => 'Tu página %1$s',
    'extra.text.label.bottom' => 'Abajo',
    'extra.text.label.button.float' => 'Elegir la posición del botón en la barra promocional',
    'extra.text.label.button.link' => 'Escribe el nombre del botón en la barra promocional',
    'extra.text.label.button.text' => 'Escribe un texto para el botón en la barra promocional',
    'extra.text.label.button.title' => 'Botón en la barra promocional',
    'extra.text.label.period.from' => 'Activo desde',
    'extra.text.label.period.to' => 'Activo hasta',
    'extra.text.label.promobar_position' => 'Posición relativa a la navegación',
    'extra.text.label.text' => 'Texto',
    'extra.text.label.title' => 'Título',
    'extra.text.label.top' => 'Arriba',
    'extra.text.ph.title' => 'Ingresa el título del texto',
    'global.err.article_no_longer_exists' => 'El artículo ya no existe.',
    'global.err.blog_no_longer_exists' => 'La categoría del artículo ya no existe.',
    'global.err.caption_max_%1$s' => 'La leyenda no puede ser más larga de %1$s caracteres.',
    'global.err.caption_required' => 'La leyenda es requerida.',
    'global.err.category_no_longer_exists' => 'La categoría del producto ya no existe.',
    'global.err.invalid_request' => 'Solicitud inválida.',
    'global.err.link_caption_required' => 'Se requiere título para la dirección web externa.',
    'global.err.link_value_required' => 'Se requiere dirección web externa.',
    'global.err.page_no_longer_exists' => 'La página ya no existe.',
    'global.err.product_no_longer_exists' => 'El producto ya no existe.',
    'global.err.src_required' => 'Se requiere fuente de imagen externa.',
    'global.err.vendor_no_longer_exists' => 'La marca ya no existe.',
    'google_map.description' => '',
    'google_map.name' => '',
    'group.banners' => 'Imágenes',
    'group.brands' => 'Proveedores',
    'group.categories' => 'Categorías',
    'group.footer' => 'Pie de página',
    'group.header' => 'Cabecera',
    'group.menu' => 'Menú',
    'group.products' => 'Productos',
    'group.show_all' => 'Mostrar todo',
    'group.slider' => 'Deslizador',
    'group.testimonials' => 'Testimonios',
    'group.text_fields' => 'Campos de texto',
    'group.top_bar' => 'Barra superior',
    'instagram.description' => 'Con este widget puedes visualizar automáticamente fotos de Instagram en tu tienda',
    'instagram.name' => 'Fotos de Instagram',
    'layout.buttons.label.border_bottom_left_radius' => 'Radio de borde inferior izquierdo',
    'layout.buttons.label.border_bottom_right_radius' => 'Radio de borde inferior derecho',
    'layout.buttons.label.border_top_left_radius' => 'Radio de borde superior izquierdo',
    'layout.buttons.label.border_top_right_radius' => 'Radio de borde superior derecho',
    'layout.buttons.title.border_radius' => 'Radio de borde',
    'layout.footer.footer_max_cols_count' => 'Conteo máximo de columnas en el pie de página',
    'layout.grid.label.grid_width' => 'Configurar ancho de cuadrícula',
    'layout.grid.label.grid_width_full' => 'Configurar ancho de cuadrícula completo',
    'layout.grid.label.offset_desktop' => 'Configurar desplazamiento de escritorio',
    'layout.grid.label.offset_mobile' => 'Configurar desplazamiento móvil',
    'layout.grid.title.grid_settings' => 'Configuraciones de cuadrícula',
    'mailchimp.newsletter.configure_app_first' => 'Por favor, primero configura la aplicación',
    'mailchimp.newsletter.label.automatic' => 'Mostrar automáticamente',
    'mailchimp.newsletter.label.delay' => 'Retraso',
    'mailchimp.newsletter.label.description' => 'Descripción',
    'mailchimp.newsletter.label.show_form' => 'Mostrar el formulario en el pie de página',
    'mailchimp.newsletter.label.status' => 'Estado actual del suscriptor',
    'mailchimp.newsletter.label.title' => 'Título del boletín',
    'mailchimp.newsletter.ph.description' => 'Ingresa la descripción',
    'mailchimp.newsletter.ph.title' => 'Ingresa el nombre',
    'mailchimp.newsletter.status.pending' => 'Pendiente',
    'mailchimp.newsletter.status.subscribed' => 'Suscrito',
    'name.bannerInSidebar.extra.banner' => 'Banners en la barra lateral',
    'name.banners.extra.banner' => 'Banners',
    'name.bannersHomePage.extra.banner' => 'Banners - Índice',
    'name.bannersTextPage.extra.banner' => 'Banners - Página de texto',
    'name.blog.blog.blog' => 'Blog',
    'name.blogHome.blog.blog' => 'Blog Inicio',
    'name.buttonToTop.extra.text' => 'Botón de arriba',
    'name.buttonsConfiguration.layout.button' => 'Configuraciones de botones',
    'name.carousel.extra.carousel' => 'Deslizador',
    'name.cartText.extra.text' => 'Texto del Carruaje',
    'name.categoryShowcaseBackground.extra.backgroundImage' => 'Muestra con categorías - Fondo',
    'name.checkoutPrice.extra.text' => 'Texto por precio y entrega al enviar un pedido',
    'name.checkoutSideText.extra.text' => 'Texto en la barra lateral del carrito',
    'name.checkoutSignInGuestText.extra.text' => 'Texto del carrito cuando se es invitado',
    'name.checkoutSignInLoginText.extra.text' => 'Texto del carrito al iniciar sesión',
    'name.checkoutSignInRegisterText.extra.text' => 'Texto del carrito al registrarse',
    'name.checkoutText.extra.text' => 'Completa el pedido - texto',
    'name.contactInformation.contact.information' => 'Información de contacto',
    'name.extra.htmlLine' => 'Barra promocional',
    'name.filters.product.filters' => 'Configuraciones del Catálogo de Productos',
    'name.footerConfiguration.layout.footer' => 'Configuraciones del pie de página',
    'name.footerContacts.extra.text' => 'Contactos del pie de página',
    'name.footerContent.extra.text' => 'Texto en el pie de página',
    'name.footerLinks1.navigation.links' => 'Enlaces de navegación del pie de página 1',
    'name.footerLinks2.navigation.links' => 'Enlaces de navegación del pie de página 2',
    'name.footerLinks3.navigation.links' => 'Enlaces de navegación del pie de página 3',
    'name.footerText.extra.text' => 'Texto del pie de página',
    'name.footerTextBackground.extra.backgroundImage' => 'Texto del pie de página - Fondo',
    'name.googleMap.contact.googleMap' => 'Mapa de Google',
    'name.headerConfiguration.layout.header' => 'Configuraciones de cabecera',
    'name.headerImage1.extra.backgroundImage' => 'Imagen de Cabecera 1',
    'name.headerImage2.extra.backgroundImage' => 'Imagen de Cabecera 2',
    'name.headerLeft.extra.text' => 'Texto antes del logo',
    'name.headerRight.extra.text' => 'Texto después del logo',
    'name.headerText.extra.text' => 'Texto de Cabecera',
    'name.homeSingleBanner.extra.banner' => 'Banners de la página principal',
    'name.homeText.extra.text' => 'Texto de la página principal',
    'name.homeText1.extra.text' => 'Texto de la página principal 1',
    'name.homeText1Background.extra.backgroundImage' => 'Texto de la página principal 1 - foto',
    'name.homeText2.extra.text' => 'Texto de la página principal 2',
    'name.homeText2Background.extra.backgroundImage' => 'Texto de la página principal 2 - foto',
    'name.homeText3.extra.text' => 'Texto de la página principal 3',
    'name.homeText3Background.extra.backgroundImage' => 'Texto de la página principal 3 - foto',
    'name.homeTextBackground.extra.backgroundImage' => 'Texto de la página principal - fondo',
    'name.homeTopAfterCategoryBackground.extra.backgroundImage' => 'Fondo de texto del banner después de la página principal con categorías',
    'name.homeTopBackground.extra.backgroundImage' => 'Fondo de texto del banner en la parte superior de la página principal',
    'name.homeTopBanner.extra.text' => 'Texto en la parte superior de la página principal',
    'name.homeTopTextAfterCategoryShowcase.extra.text' => 'Texto después de la ventana de la categoría en la página principal',
    'name.homeVideoBackgroundImage.extra.backgroundImage' => 'Imagen de la sección de video de la página principal.',
    'name.homeVideoText.extra.text' => 'Texto y video de la página principal',
    'name.homeWelcome.extra.text' => 'Texto de bienvenida',
    'name.latestNewsBackground.extra.backgroundImage' => 'Últimas Noticias - Fondo',
    'name.layout.buttons' => 'Configuraciones de botones',
    'name.layout.footer' => 'Configuración del pie de página',
    'name.layout.grid' => 'Configuraciones de cuadrícula',
    'name.layout.header' => 'Configuración de la cabecera',
    'name.navigationLinks.navigation.links' => 'Enlaces de navegación',
    'name.navigationLinksPage.navigation.links' => 'Menú de páginas - Páginas',
    'name.newProductsBackground.extra.backgroundImage' => 'Nuevos productos - Fondo',
    'name.newProductsBanners.extra.banner' => 'Banners en nuevos productos',
    'name.newsletter.mailchimp.newsletter' => 'Boletín informativo (MailChimp)',
    'name.pageLoader.extra.backgroundImage' => 'Indicador de carga de página',
    'name.pagesBanner.extra.banner' => 'Banner en páginas estáticas',
    'name.product.lastViewed' => 'Últimos productos vistos',
    'name.product.linked' => 'Productos vinculados',
    'name.product.productInBundles' => 'Producto en paquetes',
    'name.productShowcaseBanners.extra.banner' => 'Banners',
    'name.productText.extra.text' => 'Texto en detalles del producto',
    'name.productsCombine.product.related' => 'Coincidir con',
    'name.productsDetails.product.productsDetails' => 'Información detallada del producto',
    'name.productsRelated.product.related' => 'Productos relacionados',
    'name.productsRelated2.product.related' => 'Productos destacados',
    'name.recentArticles.blog.recentArticles' => 'Últimos Artículos',
    'name.recentComments.blog.recentComments' => 'Últimos comentarios',
    'name.search.extra.search' => 'Buscar',
    'name.showcaseBestSellersProducts.product.productShowcase' => 'Muestra con los productos más vendidos',
    'name.showcaseBrand.product.showcase' => 'Muestra con las marcas',
    'name.showcaseBrands1.product.showcase' => 'Muestra con marcas 1',
    'name.showcaseBrands2.product.showcase' => 'Muestra con marcas 2',
    'name.showcaseCategories.product.showcase' => 'Muestra de categorías',
    'name.showcaseCategory.product.showcase' => 'Muestra de categorías',
    'name.showcaseProducts.product.productShowcase' => 'Muestra de productos',
    'name.showcaseProducts1.product.productShowcase' => 'Muestra de productos 1',
    'name.showcaseProducts2.product.productShowcase' => 'Muestra de productos 2',
    'name.showcaseProducts3.product.productShowcase' => 'Muestra de productos 3',
    'name.showcaseProducts4.product.productShowcase' => 'Muestra de productos 4',
    'name.sidebarBanners1.extra.banner' => 'Banners',
    'name.sidebarBanners2.extra.banner' => 'Banners',
    'name.sidebarText.extra.text' => 'Texto de la barra lateral',
    'name.social.extra.social' => 'Redes sociales',
    'name.summaryAdditionalInfo.extra.text' => 'Información en el carrito de precios',
    'name.text1.extra.text' => 'Texto de la página principal 1',
    'name.text1Background.extra.backgroundImage' => 'Texto de la página principal 1 - fondo',
    'name.text2.extra.text' => 'Texto de la página principal 2',
    'name.text2Background.extra.backgroundImage' => 'Texto de la página principal 2 - fondo',
    'name.text3.extra.text' => 'Texto de la página principal 3',
    'name.text3Background.extra.backgroundImage' => 'Texto de la página principal 3 - fondo',
    'name.textBoxBackground_1.extra.backgroundImage' => 'Caja de texto 1 - fondo',
    'name.textBoxBackground_2.extra.backgroundImage' => 'Caja de texto 2 - fondo',
    'name.textBoxBackground_3.extra.backgroundImage' => 'Caja de texto 3 - fondo',
    'name.textBox_1.extra.text' => 'Caja de texto 1',
    'name.textBox_2.extra.text' => 'Caja de texto 2',
    'name.textBox_3.extra.text' => 'Caja de texto 3',
    'name.textbox1.extra.text' => 'Caja de texto 1',
    'name.textbox1_tooltip.extra.text' => 'Caja de texto 1 - aviso',
    'name.textbox2.extra.text' => 'Caja de texto 2',
    'name.textbox2_tooltip.extra.text' => 'Caja de texto 2 - aviso',
    'name.textbox3.extra.text' => 'Caja de texto 3',
    'name.textbox3_tooltip.extra.text' => 'Caja de texto 3 - aviso',
    'name.textbox4.extra.text' => 'Caja de texto 4',
    'name.textbox4_tooltip.extra.text' => 'Caja de texto 4 - aviso',
    'navigation.links.action.add' => 'Agregar enlace',
    'navigation.links.blog' => 'Categoría de artículo',
    'navigation.links.blog_article' => 'Artículo específico',
    'navigation.links.external' => 'Dirección externa',
    'navigation.links.label.blank' => 'Abrir en nueva ventana',
    'navigation.links.label.icon' => 'Elige un ícono para el enlace',
    'navigation.links.label.link' => 'Elegir enlace',
    'navigation.links.label.link_caption' => 'Título del enlace',
    'navigation.links.page' => 'Página estática',
    'navigation.links.ph.link' => 'Enlace',
    'navigation.links.ph.link_caption' => 'Título del enlace',
    'navigation.links.ph.select_article' => 'Seleccionar artículo',
    'navigation.links.ph.select_blog' => 'Seleccionar categoría de artículo',
    'navigation.links.ph.select_category' => 'Seleccionar categoría de producto',
    'navigation.links.ph.select_external' => 'Ej. http://google.com',
    'navigation.links.ph.select_product' => 'Seleccionar producto',
    'navigation.links.ph.select_vendor' => 'Seleccionar proveedor',
    'navigation.links.product' => 'Producto específico',
    'navigation.links.product_category' => 'Categoría de producto',
    'navigation.links.section' => 'Sección de tienda',
    'navigation.links.vendor' => 'Marca',
    'newsletter.description' => 'Este widget te ayuda a recopilar los correos electrónicos de tus visitantes',
    'newsletter.name' => 'Boletín',
    'order-details.description' => 'Puedes agregar este widget a una página de aterrizaje usada como página de agradecimiento y cada uno de tus clientes verá los detalles exactos de sus propios pedidos.',
    'order-details.name' => 'Detalles del pedido',
    'product.button.color.active' => 'Activo',
    'product.button.color.default' => 'Predeterminado',
    'product.button.color.secondary' => 'Secundario',
    'product.button.label.amount' => 'Número de botones a mostrar',
    'product.button.label.attributes' => 'Atributos adicionales',
    'product.button.label.color' => 'Color',
    'product.button.label.full_width' => 'Ancho completo',
    'product.button.label.link' => 'URL',
    'product.button.label.override_text' => 'Sobrescribir texto del botón',
    'product.button.label.position' => 'Posición',
    'product.button.label.size' => 'Tamaño',
    'product.button.label.target' => 'Abrir en nueva ventana',
    'product.button.label.text' => 'Texto',
    'product.button.placeholder.attributes' => 'ej. data-ajax-panel',
    'product.button.size.default' => 'Predeterminado',
    'product.button.size.large' => 'Grande',
    'product.button.size.small' => 'Pequeño',
    'product.button.text.center' => 'Centro',
    'product.button.text.left' => 'Izquierda',
    'product.button.text.right' => 'Derecha',
    'product.description' => 'Con este widget puedes visualizar un producto específico con información detallada',
    'product.linked.label.position' => 'Posición',
    'product.linked.position.recommended' => 'Como productos recomendados debajo de la sección Comprar',
    'product.linked.position.section_recommended' => 'Como la sección de productos recomendados',
    'product.linked.position.variant' => 'Como variante',
    'product.list.help.template_list_horizontal' => 'Esta opción muestra los elementos en la lista de productos en una línea. Aplica solo a la versión de escritorio y a productos que NO estén en un deslizador.',
    'product.list.label.color_product_variants' => 'Mostrar colores de productos',
    'product.list.label.manufacturer_logo_show' => 'Mostrar logo del fabricante',
    'product.list.label.max_title_rows' => 'Establecer el número máximo de líneas de título de producto',
    'product.list.label.per_row_desktop' => 'Productos por fila para escritorio',
    'product.list.label.per_row_mobile' => 'Productos por fila para móvil',
    'product.list.label.second_image_show' => 'Mostrar segunda imagen al pasar el cursor',
    'product.list.label.variants' => 'Mostrar variantes de producto',
    'product.listing.action.add_per_page_option' => 'Agregar opciones de "por página"',
    'product.listing.action.add_price_range' => 'Agregar rangos de precio',
    'product.listing.action.remove_price_range' => 'Eliminar rango de precio',
    'product.listing.brand_model' => 'Marca y Modelo',
    'product.listing.category_properties' => 'Propiedades de la categoría',
    'product.listing.category_properties_limit' => 'Límite de propiedades de la categoría',
    'product.listing.cation.remove_per_page_option' => 'Eliminar por página',
    'product.listing.date' => 'Fecha',
    'product.listing.enable_category_properties' => 'Habilitar propiedades de la categoría',
    'product.listing.err.from_price_must_be_lower_than_to_price' => 'El precio de origen debe ser menor que el precio de destino.',
    'product.listing.err.one_or_more_from_or_to_value_missing' => 'Uno o más valores de origen o destino faltan.',
    'product.listing.err.per_page_value_not_present_in_current_per_page_options' => 'El valor por página no está presente en las opciones actuales por página.',
    'product.listing.err.price_range_max_should_be_greater_than_price_range_min' => 'El rango de precio máximo debe ser mayor que el mínimo',
    'product.listing.err.price_range_step_should_be_lower_than_price_range_max' => 'El paso del rango de precio debe ser menor que el rango de precio máximo',
    'product.listing.filters_options' => 'Configuración del orden de filtrado en la página de listado de productos',
    'product.listing.filters_options_order' => 'Ordenar filtros en la página de listado de productos',
    'product.listing.filters_options_order_info' => 'Puedes cambiar el orden de filtrado en el catálogo de productos cambiando el número de clasificación. Cuanto menor sea el número, mayor será la posición del filtro.',
    'product.listing.hide_featured' => 'Ocultar etiqueta destacada',
    'product.listing.hide_sale' => 'Ocultar etiqueta de DESCUENTO',
    'product.listing.list_one' => 'Lista uno',
    'product.listing.list_two' => 'Lista dos',
    'product.listing.mark_out_of_stock_products' => 'marcar productos agotados',
    'product.listing.order_by' => 'Ordenar por',
    'product.listing.order_by_options' => 'Agregar opciones de "ordenar por"',
    'product.listing.order_direction' => 'Dirección del orden',
    'product.listing.order_latest_out_of_stock' => 'productos agotados al final de la lista',
    'product.listing.per_page' => 'Productos por página',
    'product.listing.per_page_options' => 'Opciones por página',
    'product.listing.price' => 'Precio',
    'product.listing.price_range_max' => 'Rango de precio máximo',
    'product.listing.price_range_min' => 'Rango de precio mínimo',
    'product.listing.price_range_step' => 'Paso del rango de precio',
    'product.listing.price_ranges_from' => 'Rango desde',
    'product.listing.price_ranges_to' => 'Rango hasta',
    'product.listing.products_price_ranges' => 'Rangos de precios de productos',
    'product.listing.remove_price_range' => 'Eliminar rango de precio',
    'product.listing.show_out_of_stock_products' => 'productos agotados',
    'product.listing.show_short_description' => 'Descripción corta',
    'product.listing.sort_order' => 'Orden manual',
    'product.name' => 'Producto',
    'product.productDetails.label.hide_tags' => 'Ocultar etiquetas de producto',
    'product.productDetails.label.pre_selected_variant' => 'La variante predeterminada está seleccionada',
    'product.productDetails.label.show_images_in_gallery' => 'Mostrar imágenes del producto en la galería emergente',
    'product.productDetails.label.show_product_quantity_in_status' => 'Mostrar cantidad de producto en estado',
    'product.productDetails.label.variant_in_name' => 'Mostrar variante seleccionada en el nombre del producto',
    'product.productShowcase.filter_by_all' => 'Todo',
    'product.productShowcase.filter_by_category' => 'Filtrar por categoría de producto',
    'product.productShowcase.filter_by_product' => 'Filtrar por producto',
    'product.productShowcase.filter_by_tag' => 'Filtrar por etiqueta',
    'product.productShowcase.filter_by_type_bundle' => 'Paquete',
    'product.productShowcase.filter_by_type_digital' => 'Digital',
    'product.productShowcase.filter_by_type_physically' => 'Físico',
    'product.productShowcase.filter_by_vendor' => 'Filtrar por proveedor',
    'product.productShowcase.help.color' => 'Escribe el código de color en el campo a continuación para elegir el color del ícono.',
    'product.productShowcase.help.icon' => 'El ícono será visible junto al nombre de tu vitrina.',
    'product.productShowcase.label.color' => 'Elige un color para el ícono',
    'product.productShowcase.label.featured' => 'Destacado',
    'product.productShowcase.label.filter' => 'Filtros',
    'product.productShowcase.label.icon' => 'Elige un ícono para tu vitrina',
    'product.productShowcase.label.new' => 'Nuevo',
    'product.productShowcase.label.sale' => 'Venta',
    'product.productShowcase.label.select_categories' => 'Seleccionar categorías de productos',
    'product.productShowcase.label.select_products' => 'Seleccionar productos',
    'product.productShowcase.label.select_tags' => 'Seleccionar etiquetas',
    'product.productShowcase.label.select_vendors' => 'Seleccionar proveedores',
    'product.productShowcase.ph.color' => 'Color',
    'product.productShowcase.ph.featured' => 'Destacado',
    'product.productShowcase.ph.filter' => 'Filtrar',
    'product.productShowcase.ph.new' => 'Nuevo',
    'product.productShowcase.ph.sale' => 'Venta',
    'product.related.filter_by_category' => 'Filtrar por categoría',
    'product.related.filter_by_tag' => 'Filtrar por etiqueta',
    'product.related.filter_by_vendor' => 'Filtrar por proveedor',
    'product.related.help.order_direction' => 'Elegir dirección de orden',
    'product.related.label.order_by' => 'Ordenar por',
    'product.related.label.order_direction' => 'Dirección del orden',
    'product.related.label.out_of_stock' => 'Agotado',
    'product.related.label.product_count' => 'Cantidad de productos',
    'product.related.label.title' => 'Título',
    'product.related.label.type' => 'Tipo',
    'product.related.order_by_id' => 'Ordenar por ID',
    'product.related.order_by_match' => 'Por totales de coincidencia',
    'product.related.order_by_name' => 'Ordenar por nombre',
    'product.related.order_by_price' => 'Precio',
    'product.related.order_by_rand' => 'Aleatorio',
    'product.related.ph.order_by' => 'Ordenar por',
    'product.related.ph.product_count' => 'Ingresa el número de productos que deseas que aparezcan como relacionados',
    'product.related.ph.title' => 'Productos relacionados',
    'product.related.ph.type' => 'Elegir tipo',
    'product.search.add_utm' => 'Agregar <b>utm_source</b> a la URL',
    'product.search.add_utm.help' => 'Capacidad para rastrear ventas en la tienda',
    'product.search.label.autocomplete' => 'Opción para completar automáticamente la búsqueda para tus clientes',
    'product.search.label.search_in_product_description' => 'Buscar en la descripción de productos',
    'product.selected.err.products_required' => 'Se requieren productos',
    'product.showcase.category' => 'Categoría de producto',
    'product.showcase.err.filter_value_required' => 'Se requiere valor de filtro.',
    'product.showcase.err.more_than_20_categories' => 'Las categorías seleccionadas no pueden ser más de :count ',
    'product.showcase.err.more_than_20_products' => 'Los productos no pueden ser más de :count',
    'product.showcase.err.more_than_20_vendors' => 'Elige fabricantes que no puedes continuar más de :count',
    'product.showcase.err.one_or_more_categories_no_longer_exist' => 'Algunas o todas las categorías elegidas no existen.',
    'product.showcase.err.one_or_more_products_no_longer_exist' => 'Uno o más de los productos elegidos no existen.',
    'product.showcase.err.one_or_more_tags_no_longer_exist' => 'Uno o más de las etiquetas elegidas no existen.',
    'product.showcase.err.one_or_more_vendors_no_longer_exist' => 'Uno o más de los proveedores elegidos no existen.',
    'product.showcase.label.arrows_position' => 'Posición de las flechas',
    'product.showcase.label.arrows_position.center' => 'Centro',
    'product.showcase.label.arrows_position.top' => 'Arriba',
    'product.showcase.label.carousel_options' => 'Opciones de carrusel',
    'product.showcase.label.enable_arrows' => 'Habilitar flechas',
    'product.showcase.label.enable_pagination' => 'Habilitar paginación',
    'product.showcase.label.enable_slider' => 'Habilitar carrusel',
    'product.showcase.label.header' => 'Cabecera',
    'product.showcase.label.items' => 'Artículos',
    'product.showcase.label.items_options' => 'Opciones de artículos',
    'product.showcase.label.items_per_row' => 'Artículos por fila',
    'product.showcase.label.order_by' => 'Ordenar por',
    'product.showcase.label.order_direction' => 'Dirección del orden',
    'product.showcase.label.product_count' => 'Conteo de productos',
    'product.showcase.label.products_per_row' => 'Productos por fila',
    'product.showcase.label.select' => 'Seleccionar artículos para mostrar',
    'product.showcase.label.space_between' => 'Espacio entre los productos en px',
    'product.showcase.label.space_between_mobile' => 'Espacio entre los productos para móvil en px',
    'product.showcase.label.title' => 'Título',
    'product.showcase.label.type' => 'Tipo',
    'product.showcase.order_by_id' => 'Ordenar por fecha',
    'product.showcase.order_by_name' => 'Ordenar por nombre',
    'product.showcase.order_by_rand' => 'Orden aleatorio',
    'product.showcase.ph.filter' => 'Filtrar productos por',
    'product.showcase.ph.header' => 'Ej. Productos destacados',
    'product.showcase.ph.order_by' => 'Elegir ordenar por',
    'product.showcase.ph.order_direction' => 'Elegir dirección de orden',
    'product.showcase.ph.product_count' => 'Ingresa conteo de productos',
    'product.showcase.ph.space_between' => 'Espacio en px',
    'product.showcase.ph.space_between_mobile' => 'Espacio en px',
    'product.showcase.ph.title' => 'Ingresa el título',
    'product.showcase.vendor' => 'Marca',
    'product_showcase.description' => 'Muestra productos seleccionados. Selecciona de la categoría de producto, proveedor, etiqueta o colección. Incluso puedes seleccionar productos específicos y ordenarlos',
    'product_showcase.name' => 'Tinas de productos',
    'recent_articles.description' => 'Con este widget puedes visualizar artículos del blog',
    'recent_articles.name' => 'Artículos',
    'row_settings.background-attachment' => 'Efecto parallax',
    'row_settings.background-position-x' => 'Posición horizontal del fondo',
    'row_settings.background-position-y' => 'Posición vertical del fondo',
    'row_settings.background-size' => 'Estilo de fondo',
    'row_settings.class_name' => 'Nombre de clase CSS',
    'row_settings.full_width' => 'Ancho completo',
    'row_settings.joint_background' => 'Aplicar desde la fila anterior',
    'row_settings.transparent' => 'Transparente',
    'row_settings.margin_bottom' => 'Margen Inferior (px)',
    'row_settings.margin_side' => 'Margen Lateral (px)',
    'row_settings.margin_top' => 'Margen Superior (px)',
    'row_settings.padding_bottom' => 'Relleno Inferior (px)',
    'row_settings.padding_side' => 'Relleno Lateral (px)',
    'row_settings.padding_top' => 'Relleno Superior (px)',
    'row_settings.reverse_column_order' => 'Invertir orden de columna en escritorio',
    'row_settings.show_mobile_or_desktop' => 'Mostrar en',
    'row_settings.type_color' => 'Color',
    'row_settings.vertical_align_class' => 'Alineación de contenido',
    'selectedProducts.label.please_add_products' => 'Por favor añade productos',
    'separator.description' => 'Línea, separador punteado o discontinuo para separar diferentes secciones de tu página',
    'separator.label.height' => 'Grosor (px)',
    'separator.label.style' => 'Estilo del separador',
    'separator.label.width' => 'Ancho (%)',
    'separator.name' => 'Agregar separador',
    'separator.position.center' => 'Centro',
    'separator.position.left' => 'Izquierda',
    'separator.position.right' => 'Derecha',
    'separator.style.dashed' => 'Discontinuo',
    'separator.style.dotted' => 'Punteado',
    'separator.style.double' => 'Doble',
    'separator.style.solid' => 'Sólido',
    'showcase.description' => 'Muestra categorías o proveedores con sus logos o imágenes, nombres y descripciones.',
    'showcase.name' => 'Muestras de Categorías/Marcas',
    'text-carousel.description' => 'El Carrusel de Texto es un widget donde puedes mostrar múltiples campos de área de texto',
    'text-carousel.name' => 'Carrusel de Texto',
    'text.description' => 'Mostrar un texto editado por el editor WYSIWYG. Incluso puedes agregar imágenes o videos en el área de texto',
    'text.name' => 'Agregar texto',
    'title.description' => 'Agrega Títulos a tu página y elige entre diferentes tipos de encabezados desde H1 hasta H6',
    'title.label.tag' => 'Encabezado',
    'title.name' => 'Agregar Título',
    'user.account.help.orders_per_page' => 'Pedidos por página en la sección de storefront de la cuenta',
    'user.account.label.files_per_page' => 'Archivos por página',
    'user.account.label.orders_per_page' => 'Pedidos por página',
    'user.account.label.payments_per_page' => 'Pagos por página',
    'user.account.ph.files_per_page' => 'Ingresa por página',
    'user.account.ph.payments_per_page' => 'Ingresa por página',
    'video.description' => 'Agrega video de YouTube, Vimeo u otro a tu página',
    'video.label.autoplay' => 'Autoplay',
    'video.label.controls' => 'Controles',
    'video.label.loop' => 'Repetir',
    'video.label.src' => 'Enlace/Código de incrustación',
    'video.label.type' => 'Tipo de video',
    'video.name' => 'Agregar Video',
    'yotpo.description' => 'Con este widget puedes visualizar automáticamente las reseñas de Yotpo en tu tienda',
    'yotpo.name' => 'Reseñas de Yotpo',
];
