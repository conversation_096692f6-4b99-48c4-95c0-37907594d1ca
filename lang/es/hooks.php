<?php

return [
    'header.add' => 'Añadir un webhook',
    'header.edit' => 'Editar',
    'headers' => 'Encabezados',
    'label.key' => 'Clave',
    'label.value' => 'Valor',
    'error.cant_resolve_host' => 'No se pudo resolver el host para el webhook con url: :url',
    'error.disable' => 'El Webhook (:name) ha sido desactivado porque recibimos un error del receptor con el mensaje: :message',
    'error.error' => 'El Webhook (:name) tiene un error del receptor con el mensaje: :message',
    'label.new_version' => 'Usar nueva estructura de webhook',
    'label.category' => 'Categoría',
    'label.vendor' => 'Vendedor',
    'label.product' => 'Producto',
    'label.discount' => 'Descuento',
    'label.customer' => 'Cliente',
    'label.order' => 'Pedido',
    'label.subscriber' => 'Suscriptor',
    'action.subscriber.created' => 'Suscriptor creado',
    'action.subscriber.updated' => 'Suscriptor actualizado',
    'action.subscriber.deleted' => 'Suscriptor eliminado',
];
