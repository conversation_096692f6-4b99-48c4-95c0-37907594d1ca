<?php

return [
    'action.delete' => 'Eliminar proveedor de pago',
    'configure' => 'Configurar',
    'confirm.delete' => '¿Estás seguro de que quieres eliminar este proveedor de pago?',
    'err.certificate_file_required' => 'Se requiere archivo de certificado',
    'err.certificate_password_required' => 'Se requiere contraseña de certificado',
    'err.invalid_certificate_file' => 'Archivo de certificado inválido',
    'err.invalid_certificate_or_password' => 'Certificado y/o contraseña inválidos',
    'err.invalid_resource_file' => 'Archivo de recurso inválido',
    'err.merchant_id_only_digits' => 'El ID de POS debe contener solo dígitos',
    'err.merchant_id_required' => 'Se requiere ID de POS',
    'err.merchant_name_required' => 'Por favor, introduce el nombre del comerciante',
    'err.only_alpha_num' => 'Solo se permiten caracteres alfanuméricos.',
    'err.pass_required' => 'Por favor, introduce la contraseña',
    'err.resource_file_required' => 'Se requiere archivo de recurso',
    'err.status_invalid' => '',
    'err.user_required' => 'Por favor, introduce el nombre de usuario',
    'head.amount_from_to' => 'Aceptación de pagos basada en el monto del pedido',
    'head.discount' => 'Descuento al pagar con',
    'header.payment_providers' => 'Métodos de pago',
    'help.authorize.app_login_key' => 'Clave de inicio de sesión de la aplicación Authorize.net',
    'help.authorize.transaction_key' => 'Clave de transacción Authorize.net',
    'help.authorize_payment' => 'Esta opción te permite reservar el monto del pedido y debitar la tarjeta de débito/crédito del cliente en una fecha posterior.',
    'help.authorize_payment_auto' => 'En este método, el sistema debitará automáticamente al usuario después de que el pedido se marque como Enviado.',
    'help.authorize_payment_manual' => 'Con este método, solo podrás capturar el pago manualmente.',
    'help.bnp.certificate_file' => 'Archivo de certificado BNP Paribas para etapa real',
    'help.bnp.certificate_file_test' => 'Archivo de certificado BNP Paribas para modo de prueba',
    'help.borica.certificate_file' => 'Archivo con certificados de Borica (.zip)',
    'help.cpay.merchant_id' => 'ID de comerciante proporcionado por cPay. Una vez almacenado no se puede cambiar.',
    'help.cpay.merchant_name' => 'El nombre del comerciante en cPay. Una vez almacenado no se puede cambiar.',
    'help.cpay.merchant_password' => 'Contraseña de pago para identificarse con cPay',
    'help.cpay.mode' => 'Utiliza el modo de prueba para probar tu conexión. El modo en vivo es para el procesamiento de pagos real. Usa el modo en vivo cuando hayas verificado tus credenciales.',
    'help.discount' => 'Puedes especificar una cantidad exacta o un porcentaje del descuento',
    'help.epay.kin' => 'Este es tu número de cliente ePay (KIN)',
    'help.epay.mode' => 'Utiliza el modo de prueba para probar tu conexión. El modo en vivo es para el procesamiento de pagos real. Usa el modo en vivo cuando hayas verificado tus credenciales.',
    'help.epay.secret' => 'Este es tu secreto de ePay.',
    'help.fibank.certificate_file' => 'Selecciona el archivo de certificado PKCS12 proporcionado por Fibank',
    'help.free_leasing' => 'Aquí puedes especificar qué meses se ofrecerán con cuotas gratuitas.',
    'help.from_to' => 'El método de pago estará activo solo si el monto del pedido cumple con las condiciones de monto mínimo y máximo.',
    'help.mobilpay.certificate_file' => 'Este es el certificado público (termina en .cer)',
    'help.mobilpay.certificate_private_file' => 'Esta es la clave privada (termina en .key)',
    'help.mobilpay.merchant_id' => 'Este es tu código de comerciante personal, que se verá algo así: XXXX-XXXX-XXXX-XXXX-XXXX',
    'help.mobilpay.mode' => 'Utiliza el modo de prueba para probar tu conexión. El modo en vivo es para el procesamiento de pagos real. Usa el modo en vivo cuando hayas verificado tus credenciales.',
    'help.myos.registration' => 'Si no tienes un registro myPOS, puedes hacerlo desde <a href="https://www.mypos.eu/en/enroll/ref:1001660" target="_blank">aquí</a>',
    'help.mypos.configuration_pack' => 'Obtén tu paquete de configuración en www.mypos.eu > menú eCommerce > Tiendas en línea.',
    'help.mypos.create_account' => 'Para usar esta opción de pago, necesitas crear una cuenta myPOS:',
    'help.mypos.keyindex' => 'El índice de clave asignado al certificado se puede revisar en tu banca en línea en www.mypos.eu > menú En línea > Tiendas en línea > Claves.',
    'help.mypos.private_key' => 'La clave privada para tu tienda se genera en tu banca en línea en www.mypos.eu > menú En línea > Tiendas en línea > Claves.',
    'help.mypos.public_certificate' => 'El certificado público de myPOS está disponible para descargar en tu banca en línea en www.mypos.eu > menú En línea > Tiendas en línea > Claves.',
    'help.mypos.store_id' => 'El ID de tienda se proporciona cuando agregas una nueva tienda en línea. Puede revisarse en tu banca en línea en www.mypos.eu > menú eCommerce > Tiendas en línea.',
    'help.mypos.test_configuration_pack' => 'El <a href="https://developers.mypos.eu/en/doc/online_payments/v1_4/226-test-data" target="_blank">paquete de configuración de prueba predeterminado</a> está prellenado. Solo puedes hacer clic en guardar y comenzar a probar myPOS.',
    'help.mypos.wallet_number' => 'Puedes ver tu número de cliente myPOS en tu banca en línea en www.mypos.eu',
    'help.newpay.api_key' => 'Introduce tu clave API de NewPay',
    'help.newpay.api_secret' => 'Introduce tu secreto API de NewPay',
    'help.newpay.mode' => 'Utiliza el modo de prueba para probar tu conexión. El modo en vivo es para el procesamiento de pagos real. Usa el modo en vivo cuando hayas verificado tus credenciales.',
    'help.paypal.email' => 'Este es el correo electrónico de tu cuenta de PayPal',
    'help.paypal.fallback_currency' => 'Si tu tienda está configurada con una moneda que PayPal no soporta, selecciona del menú desplegable la moneda predeterminada que has configurado en tu cuenta de PayPal.',
    'help.paypal.test_mode' => 'Utiliza el modo de prueba para probar tu conexión. El modo en vivo es para el procesamiento de pagos real. Usa el modo en vivo cuando hayas verificado tus credenciales.',
    'help.percent_per_month' => 'Porcentaje por mes',
    'help.provider_image' => 'Aquí puedes subir un logo o una imagen',
    'help.settle.merchant_id' => 'X-Settle-Merchant proporcionado por Settle cuando crear la clave API.',
    'help.settle.merchant_key' => 'Clave RSA secreta proporcionada por Settle cuando crear la clave API.',
    'help.settle.merchant_settle_user' => 'El X-Settle-User proporcionado por Settle cuando crear la clave API',
    'help.settle.mode' => 'Utiliza el modo de prueba para probar tu conexión. El modo en vivo es para el procesamiento de pagos real. Usa el modo en vivo cuando hayas verificado tus credenciales.',
    'help.skrill.api_password' => 'Esta es la contraseña de API de tu cuenta de Skrill.',
    'help.skrill.email' => 'Este es el correo electrónico de tu cuenta de Skrill.',
    'help.skrill.merchant_id' => 'Este es el ID de comerciante de tu cuenta de Skrill.',
    'help.skrill.signature_hash_algorithm' => 'Este es el algoritmo de hash de firma de tu cuenta de Skrill.',
    'help.skrill.signature_secret' => 'Este es el secreto de firma de tu cuenta de Skrill.',
    'help.stripe.mode' => 'Utiliza el modo de prueba para probar tu conexión. El modo en vivo es para el procesamiento de pagos real. Usa el modo en vivo cuando hayas verificado tus credenciales.',
    'help.tbi.email' => 'Por favor, introduce un correo electrónico válido para tu institución de leasing elegida',
    'help.tbi.id' => 'Por favor, introduce números de acuerdo',
    'help.tbi.max' => 'El periodo máximo es de 60 meses',
    'help.tbi.min' => 'El periodo mínimo es de 3 meses',
    'help.tbi.start_price' => 'Precio mínimo del producto',
    'help.tbi.step' => 'Por favor, introduce el intervalo de salto en meses',
    'help.ubb.resource_file' => 'Selecciona el archivo de recurso proporcionado por UBB',
    'label.amount_from' => 'Monto desde',
    'label.amount_to' => 'Monto hasta',
    'label.authorize.app_login_key' => 'Esta es la clave de inicio de sesión de la aplicación de tu cuenta Authorize.',
    'label.authorize.mode' => 'Utiliza el modo de prueba para probar tu conexión. El modo en vivo es para el procesamiento de pagos real. Usa el modo en vivo cuando hayas verificado tus credenciales.',
    'label.authorize.transaction_key' => 'Clave de transacción',
    'label.authorize_payment' => 'Autorizar pago',
    'label.bnp.certificate_password' => 'Contraseña para el certificado proporcionado por BNP Paribas para etapa real',
    'label.bnp.certificate_password_test' => 'Contraseña para el certificado proporcionado por BNP Paribas para Modo de Prueba',
    'label.bnp.code' => 'Código de comerciante en BNP Paribas',
    'label.bnp.merchant_id' => 'ID de POS',
    'label.bnp.merchant_id_ph' => 'Por favor, agrega tu ID de POS proporcionado por BNP Paribas',
    'label.bnp.merchant_id_test' => 'ID de POS para Modo de Prueba',
    'label.bnp.pos_id2' => 'ID de POS 2 (para tarjetas BNP)',
    'label.bnp.pos_id2_test' => 'ID de POS 2 (para tarjetas BNP) para Modo de Prueba',
    'label.bnp.send_email_after_checkout' => 'Enviar correo electrónico de solicitud después del pago',
    'label.borica.certificate_etlog' => 'Certificado eTLog',
    'label.borica.certificate_real' => 'Certificado real',
    'label.borica.certificate_test' => 'Certificado de prueba',
    'label.borica.certificates' => 'Certificados Borica',
    'label.borica.commonName' => 'Nombre (por ejemplo, dominio del sitio)',
    'label.borica.countryName' => 'País',
    'label.borica.downloads' => 'Descargas',
    'label.borica.emailAddress' => 'Dirección de correo electrónico',
    'label.borica.generate_csr' => 'Generar Solicitud de Firma de Certificado (CSR)',
    'label.borica.localityName' => 'Localidad (ciudad)',
    'label.borica.organizationName' => 'Organización (empresa)',
    'label.borica.organizationalUnitName' => 'Nombre de la unidad organizacional',
    'label.borica.save_certificates_reopen_window_to_download_p12' => 'Guarda los certificados y reabre la ventana de configuración para descargar el archivo .p12 para tu navegador',
    'label.borica.stateOrProvinceName' => 'Estado/Provincia',
    'label.borica.terminal_id' => 'ID de terminal',
    'label.bwt.description' => 'Descripción',
    'label.bwt.short_description' => 'Descripción al elegir el método de pago',
    'label.bwt.short_description.help' => 'La descripción ingresada arriba será visible para tus clientes cada vez que elijan el método de pago al finalizar el pedido.',
    'label.cod.description' => 'Descripción',
    'label.cod.text' => 'La descripción anterior será visible para tus clientes después de cada pedido realizado con esos proveedores de pago.<br></br>Si estás describiendo BWT, aquí puedes proporcionar los detalles bancarios para tus clientes.<br>Si estás describiendo COD, aquí puedes escribir a tus clientes que tienen que pagar en efectivo al proveedor de entrega.',
    'label.cpay.merchant_id' => 'ID de comerciante',
    'label.cpay.merchant_name' => 'Nombre del comerciante',
    'label.cpay.merchant_password' => 'Contraseña de comerciante',
    'label.cpay.mode' => 'Modo de prueba',
    'label.epay.app_id' => 'ID de APP',
    'label.epay.kin' => 'KIN',
    'label.epay.secret' => 'SECRETO',
    'label.everypay.public_key' => 'Clave pública',
    'label.everypay.public_key_placeholder' => 'Introduce tu clave pública para Everypay',
    'label.everypay.secret_key' => 'Clave secreta',
    'label.everypay.secret_key_placeholder' => 'Introduce tu clave secreta para Everypay',
    'label.fibank.certificate_password' => 'Contraseña del certificado',
    'label.free_leasing' => 'Cuotas gratuitas',
    'label.ibank.private_key' => 'Clave privada',
    'label.ibank.private_key_hosted' => 'Clave privada (alojada)',
    'label.ibank.private_key_hosted_test' => 'Clave privada (alojada, prueba)',
    'label.ibank.private_key_test' => 'Clave privada (prueba)',
    'label.ibank.public_key' => 'Clave pública',
    'label.ibank.public_key_hosted' => 'Clave pública (alojada)',
    'label.ibank.public_key_hosted_test' => 'Clave pública (alojada, prueba)',
    'label.ibank.public_key_test' => 'Clave pública (prueba)',
    'label.mobilpay.certificate_private_real' => 'Clave privada real',
    'label.mobilpay.certificate_private_test' => 'Clave privada de prueba',
    'label.mobilpay.certificate_real' => 'Certificado público real',
    'label.mobilpay.certificate_test' => 'Certificado público de prueba',
    'label.mobilpay.merchant_id' => 'ID de comerciante MobilPay',
    'label.mokka.banner' => 'Etiqueta para los productos en la tienda',
    'label.mokka.country.bg' => 'Bulgaria',
    'label.mokka.country.pl' => 'Polonia',
    'label.mokka.country.ro' => 'Rumanía',
    'label.mokka.endpoint' => 'URL base de API',
    'label.mokka.select_country' => 'Seleccionar país',
    'label.mokka.store_id' => 'ID de tienda',
    'label.mokka.store_key' => 'Clave secreta',
    'label.mollie.live_api_key' => 'Clave API en vivo',
    'label.mollie.profile_id' => 'ID de perfil',
    'label.mollie.test_api_key' => 'Clave API de prueba',
    'label.mypos.card_payment' => 'Pago con tarjeta',
    'label.mypos.card_token_request' => 'Solicitud de token de tarjeta',
    'label.mypos.card_token_request_none' => 'Ninguno',
    'label.mypos.card_token_request_only_store' => 'Solo tienda',
    'label.mypos.card_token_request_pay_and_store' => 'Pagar y almacenar',
    'label.mypos.checkout_form_type' => 'Tipo de formulario de pago',
    'label.mypos.configuration_pack' => 'Paquete de configuración',
    'label.mypos.full_payment_form' => 'Formulario de pago completo',
    'label.mypos.ideal' => 'iDeal',
    'label.mypos.payment_method' => 'Método de pago',
    'label.mypos.private_key' => 'Clave privada',
    'label.mypos.production_keyindex' => 'Índice de clave de producción',
    'label.mypos.production_url' => 'URL de producción',
    'label.mypos.public_certificate' => 'Certificado público myPOS',
    'label.mypos.signup' => 'registrarse',
    'label.mypos.simplified_payment_form' => 'Formulario de pago simplificado',
    'label.mypos.store_id' => 'ID de tienda',
    'label.mypos.test_keyindex' => 'Índice de clave de prueba',
    'label.mypos.test_url' => 'URL de prueba',
    'label.mypos.ultra_simplified_payment_form' => 'Formulario de pago ultra simplificado',
    'label.mypos.wallet_number' => 'Número de billetera',
    'label.newpay.api_key' => 'Clave API',
    'label.newpay.api_secret' => 'Secreto API',
    'label.newpay.mode' => 'Modo de prueba',
    'label.paypal.email' => 'Correo electrónico',
    'label.paypal.fallback_currency' => 'Esta es la moneda en la que recibirás tus pagos',
    'label.percent_per_month' => 'Porcentaje por mes',
    'label.provider_image' => 'Logo',
    'label.raiffeisen.download_crt' => 'Descarga de certificado',
    'label.raiffeisen.form_help' => 'Después de ingresar <b> ID de comerciante </b> y <b> Número de terminal </b>, descarga el certificado. <br /> Después de descargar el certificado, debes enviarlo al banco para su activación.',
    'label.raiffeisen.merchant_id' => 'ID de comerciante',
    'label.raiffeisen.terminal_id' => 'Número de terminal',
    'label.raiffeisen.title_form' => 'Identificación del comerciante',
    'label.settle.merchant_id' => 'X-Settle-Merchant',
    'label.settle.merchant_key' => 'Clave RSA secreta',
    'label.settle.merchant_settle_user' => 'X-Settle-User',
    'label.settle.mode' => 'Modo de prueba',
    'label.skrill.api_password' => 'Contraseña de API',
    'label.skrill.email' => 'Correo electrónico',
    'label.skrill.merchant_id' => 'ID de comerciante',
    'label.skrill.signature_hash_algorithm' => 'Algoritmo de hash de firma',
    'label.skrill.signature_secret' => 'Secreto de firma',
    'label.storefront_name' => 'Nombre del servicio',
    'label.stripe.mode' => 'Modo de prueba',
    'label.tbi.description' => 'Descripción',
    'label.tbi.email' => 'Correo electrónico',
    'label.tbi.id' => 'Número de acuerdo',
    'label.tbi.max' => 'Periodo máximo',
    'label.tbi.min' => 'Periodo mínimo',
    'label.tbi.start_price' => 'Precio',
    'label.tbi.step' => 'Intervalo escalonado',
    'label.tbi.text' => 'La descripción anterior será visible para tus clientes después de cada pedido realizado con esos proveedores de pago.',
    'label.test' => '( Modo de prueba )',
    'label.ubb.payment_type' => 'Método de Pago',
    'label.ubb.payment_type_1' => 'Venta directa',
    'label.ubb.payment_type_4' => 'Autorización + captura',
    'label.ubb.resource_file' => 'Archivo de recurso',
    'label.ucf.default_cop' => 'Código de producto en línea predeterminado',
    'label.voucher.description' => 'Descripción',
    'label.voucher.text' => 'La descripción anterior será visible para tus clientes después de cada pedido realizado con esos proveedores de pago.',
    'mokka.button.register' => '>Proceder al registro',
    'mokka.button.skip' => 'Omitir registro',
    'mokka.infobox.check_text' => '  <div class="row"><p class="col-md-6"><i class="fa fa-check-circle mokka-color"></i> 2-3 veces mayor cesta promedio</p><p class="col-md-6"><i class="fa fa-check-circle mokka-color"></i> Tasa de descuento del comerciante de 1% del valor de compra</p></div><div class="row mb-20"><p class="col-md-6"><i class="fa fa-check-circle mokka-color"></i> +25% de aumento promedio en la conversión</p><p class="col-md-6"><i class="fa fa-check-circle mokka-color"></i> Período de liquidación: semanal</p></div>',
    'mokka.infobox.text' => '<p class="text-black">¡Vende más con Mokka! La solución adaptativa de Mokka ofrecerá a tus compradores una elección de planes de pago convenientes, desde Pagar en 30 días hasta Pagos divididos de 3 a 12 meses. Más opciones de pago para tus compradores, más ventas para ti.</p>',
    'mokka.infobox.title' => 'Activar método de pago Mokka',
    'mokka.register.text' => 'Si ya tienes una cuenta con Mokka, puedes omitir el registro.',
    'mokka.store_endpoint.required' => 'No has introducido una URL base de API',
    'mokka.store_id.required' => 'Se requiere ID de tienda',
    'mokka.store_key.required' => 'Se requiere clave secreta',
    'option.skrill.md5' => 'Md5',
    'option.skrill.sha256' => 'Sha256',
    'ph.amount_from' => 'Monto desde',
    'ph.amount_to' => 'Monto hasta',
    'ph.authorize.app_login_key' => 'Introduce la clave de inicio de sesión de la aplicación',
    'ph.authorize.transaction_key' => 'Introduce la clave de transacción',
    'ph.bnp.live_secret_key' => 'Contraseña para el certificado proporcionado por BNP Paribas para etapa real',
    'ph.bnp.test_secret_key' => 'Contraseña para el certificado proporcionado por BNP Paribas para etapa de prueba',
    'ph.bwt.description' => 'Introduce la descripción',
    'ph.cod.description' => 'Introduce la descripción',
    'ph.cpay.merchant_id' => 'ID de comerciante',
    'ph.cpay.merchant_name' => 'Nombre del comerciante',
    'ph.cpay.merchant_password' => 'Contraseña',
    'ph.epay.kin' => 'Introduce el kin',
    'ph.epay.secret' => 'Introduce el secreto',
    'ph.free_leasing' => 'Ejemplo: 3,6,18',
    'ph.mobilpay.merchant_id' => 'Introduce tu ID de comerciante aquí',
    'ph.mypos.configuration_pack' => 'Pega el paquete de configuración codificado en base64',
    'ph.mypos.production_keyindex' => 'Introduce el índice de clave',
    'ph.mypos.production_url' => 'Introduce la URL de producción',
    'ph.mypos.store_id' => 'Introduce el ID de tienda',
    'ph.mypos.test_keyindex' => 'Introduce el índice de clave de prueba',
    'ph.mypos.test_url' => 'Introduce la URL de prueba',
    'ph.mypos.wallet_number' => 'Introduce el número de billetera',
    'ph.newpay.api_key' => 'Clave API',
    'ph.newpay.api_secret' => 'Secreto API',
    'ph.paypal.email' => 'Introduce el correo electrónico',
    'ph.settle.merchant_id' => 'X-Settle-Merchant',
    'ph.settle.merchant_key' => 'Clave RSA secreta',
    'ph.settle.merchant_settle_user' => 'X-Settle-User',
    'ph.skrill.api_password' => 'Introduce la contraseña de API',
    'ph.skrill.email' => 'Introduce el correo electrónico',
    'ph.skrill.merchant_id' => 'Introduce el ID de comerciante',
    'ph.skrill.signature_hash_algorithm' => 'Elige el algoritmo',
    'ph.skrill.signature_secret' => 'Introduce el secreto de firma',
    'ph.stripe.test_secret_key' => 'Introduce la clave secreta de prueba',
    'ph.tbi.description' => 'Introduce la descripción',
    'ph.tbi.email' => 'Introduce el correo electrónico',
    'ph.tbi.id' => 'Introduce el número de acuerdo',
    'ph.tbi.max' => 'Introduce el período máximo',
    'ph.tbi.min' => 'Introduce el período mínimo',
    'ph.tbi.step' => 'Selecciona el intervalo escalonado',
    'succ.delete' => '¡Método de pago eliminado con éxito!',
    'succ.edit' => '¡Método de pago editado con éxito!',
    'switch.live' => 'Modo en vivo',
    'text.auto' => 'Captura automática',
    'text.manual' => 'Captura manual',
    'th.name' => 'Método de pago',
];
