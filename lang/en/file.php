<?php

return [
    'Storage' => 'Storage',
    'action.storage_section' => 'Storage section',
    'action.upgrade' => 'Upgrade',
    'action.upload_files' => 'Upload files',
    'download' => 'Download',
    'edit' => 'Edit',
    'edit_image' => 'Edit image',
    'header.files' => 'Files',
    'info.nothing_to_choose' => 'There is nothing to choose',
    'info.now' => 'Now',
    'label.upload_from' => 'Upload form',
    'label.uploaded_count' => 'Uploaded count',
    'last_modified' => 'Last modified',
    'name' => 'Name',
    'no_longer_exists' => 'No longer exists',
    'none_selected' => 'None selected',
    'notify.no_records_help' => 'Having trouble with files? Follow the link below.',
    'notify.no_records_help_link' => 'Files help',
    'notify.no_records_info' => 'Your files will show up here',
    'notify.no_records_yet' => 'You have not added any files yet',
    'remove' => 'Remove',
    'remove_confirm_%1$s?' => 'Confirm removing %1$s?',
    'repository_delete_success' => 'Delete operation has been successful.',
    'size' => 'Size',
    'storage_free' => 'Free storage',
    'storage_used' => 'Used storage',
    'this_count_includes_all_images_and_files_from_store_sections' => 'This count includes all images and files from the store sections',
    'total_in_repository' => 'Total in repository',
    'upload_successful' => 'Data was successfully uploaded.',
    'uploaded_count' => 'Uploaded count',
    'url' => 'Url',
];
