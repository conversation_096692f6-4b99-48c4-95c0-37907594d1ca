<?php

return [
    'action.delete' => 'Delete payment provider',
    'configure' => 'Configure',
    'confirm.delete' => 'Are you sure you want yo remove this payment provider ?',
    'err.certificate_file_required' => 'Certificate file is required',
    'err.certificate_password_required' => 'Certificate password is required',
    'err.invalid_certificate_file' => 'Invalid certificate file',
    'err.invalid_certificate_or_password' => 'Invalid certificate and/or password',
    'err.invalid_resource_file' => 'Invalid resource file',
    'err.merchant_id_required' => 'POS ID is required',
    'err.merchant_id_only_digits' => 'POS ID should be only digits',
    'err.merchant_name_required' => 'Please enter merchant name',
    'err.only_alpha_num' => 'Only alphanumeric allowed.',
    'err.pass_required' => 'Please, enter password',
    'err.resource_file_required' => 'Resource file is required',
    'err.status_invalid' => '',
    'err.user_required' => 'Please, enter username',
    'head.amount_from_to' => 'Acceptance of payments based on the amount of the order',
    'head.discount' => 'Discount when paying with',
    'header.payment_providers' => 'Payment methods',
    'help.authorize.app_login_key' => 'Authorize.net app login key',
    'help.authorize.transaction_key' => 'Authorize.net transaction key',
    'help.bnp.certificate_file' => 'BNP Paribas Certificate file for Real stage',
    'help.bnp.certificate_file_test' => 'BNP Paribas Certificate file for Test mode',
    'help.borica.certificate_file' => 'Archive with certificates from Borica (.zip)',
    'help.discount' => 'You can specify an exact amount or percentage type of the discount',
    'help.epay.kin' => 'This is your ePay client number (KIN)',
    'help.epay.mode' => 'Use test mode to test your connection. Live mode is for the actual payment processing. Use live mode when you have verified your credentials.',
    'help.epay.secret' => 'This is your ePay secret.',
    'help.fibank.certificate_file' => 'Select PKCS12 certificate file provided from Fibank',
    'help.free_leasing' => 'Here you can specify which months will be offered with free instalments.',
    'help.from_to' => 'The payment method will be active only if the amount of the order meets the minimum and maximum amount conditions.',
    'help.mobilpay.certificate_file' => 'This is the public certificate (Ending with .cer)',
    'help.mobilpay.certificate_private_file' => 'This is the private key (Ending with .key)',
    'help.mobilpay.merchant_id' => 'This is your personal merchant code, looking something like this: XXXX-XXXX-XXXX-XXXX-XXXX',
    'help.mobilpay.mode' => 'Use test mode to test your connection. Live mode is for the actual payment processing. Use live mode when you have verified your credentials.',
    'help.myos.registration' => 'If you do not have a myPOS registration you can do it from <a href="https://www.mypos.eu/en/enroll/ref:1001660" target="_blank">here</a>',
    'help.mypos.create_account' => 'To use this payment option you need to create for a myPOS account:',
    'help.mypos.keyindex' => 'The Key Index assigned to the certificate could be reviewed in your online banking at www.mypos.eu > menu Online > Online stores > Keys.',
    'help.mypos.private_key' => 'The Private Key for your store is generated in your online banking at www.mypos.eu > menu Online > Online stores > Keys.',
    'help.mypos.public_certificate' => 'The myPOS Public Certificate is available for download in your online banking at www.mypos.eu > menu Online > Online stores > Keys.',
    'help.mypos.store_id' => 'Store ID is given when you add a new online store. It could be reviewed in your online banking at www.mypos.eu > menu eCommerce > Online stores.',
    'help.mypos.configuration_pack' => 'Get your configuration pack at www.mypos.eu > menu eCommerce > Online stores.',
    'help.mypos.wallet_number' => 'You can view your myPOS Client number in your online banking at www.mypos.eu',
    'help.mypos.test_configuration_pack' => 'The <a href="https://developers.mypos.eu/en/doc/online_payments/v1_4/226-test-data" target="_blank">default test mode configuration pack</a> is prefilled. You can just click save and start testing myPOS.',
    'help.paypal.email' => 'This is the email from your PayPal account',
    'help.paypal.fallback_currency' => 'If your Store is set with currency that PayPal does not support, please choose from the dropdown menu the default currency that you have set to your PayPal account.',
    'help.paypal.test_mode' => 'Use test mode to test your connection. Live mode is for the actual payment processing. Use live mode when you have verified your credentials.',
    'help.percent_per_month' => 'Percent per month',
    'help.provider_image' => 'Here you can upload a logo or an image',
    'help.skrill.api_password' => 'This is the API password from your Skrill account.',
    'help.skrill.email' => 'This is the email from your Skrill account.',
    'help.skrill.merchant_id' => 'This is the merchant ID from your Skrill account.',
    'help.skrill.signature_hash_algorithm' => 'This is the signature hash algorithm from your Skrill account.',
    'help.skrill.signature_secret' => 'This is the signature secret algorithm from your Skrill account.',
    'help.stripe.mode' => 'Use test mode to test your connection. Live mode is for the actual payment processing. Use live mode when you have verified your credentials.',
    'help.cpay.mode' => 'Use test mode to test your connection. Live mode is for the actual payment processing. Use live mode when you have verified your credentials.',
    'help.tbi.email' => 'Please,enter a valid email to your chosen leasing institution ',
    'help.tbi.id' => 'Please, enter numbers agreement',
    'help.tbi.max' => 'The maximum period is 60 months',
    'help.tbi.min' => 'The minimum period is  3 months',
    'help.tbi.start_price' => 'Minimum price of product',
    'help.tbi.step' => 'Please, enter stepping interval in months',
    'help.ubb.resource_file' => 'Select the resource file provided from UBB',
    'label.amount_from' => 'Amount from',
    'label.amount_to' => 'Amount to',
    'label.authorize.app_login_key' => 'This is the app login key from your Authorize account.',
    'label.authorize.mode' => 'Use test mode to test your connection. Live mode is for the actual payment processing. Use live mode when you have verified your credentials.',
    'label.authorize.transaction_key' => 'Transaction key',
    'label.bnp.certificate_password' => 'Password for the certificate provided by BNP Paribas for Real stage',
    'label.bnp.certificate_password_test' => 'Password for the certificate provided by BNP Paribas for Test Mode',
    'label.bnp.code' => 'Merchant code in BNP Paribas',
    'label.bnp.merchant_id' => 'POS ID',
    'label.bnp.merchant_id_ph' => 'Please add your POS ID provided by BNP Paribas',
    'label.bnp.merchant_id_test' => 'POS ID for Test Mode',
    'label.bnp.pos_id2' => 'POS ID 2 (for BNP cards)',
    'label.bnp.pos_id2_test' => 'POS ID 2 (for BNP cards) for Test Mode',
    'label.bnp.send_email_after_checkout' => 'Send request email after checkout',
    'label.borica.certificate_etlog' => 'Certificate eTLog',
    'label.borica.certificate_real' => 'Certificate real',
    'label.borica.certificate_test' => 'Certificate test',
    'label.borica.certificates' => 'Borica certificates',
    'label.borica.commonName' => 'Name (e.g. site domain)',
    'label.borica.countryName' => 'Country',
    'label.borica.downloads' => 'Downloads',
    'label.borica.emailAddress' => 'Email address',
    'label.borica.generate_csr' => 'Generate Certificate Signing Request (CSR)',
    'label.borica.localityName' => 'Locality (city)',
    'label.borica.organizationName' => 'Organization (company)',
    'label.borica.organizationalUnitName' => 'Organization unit name',
    'label.borica.save_certificates_reopen_window_to_download_p12' => 'Save the certificates and reopen the configuration window to download .p12 file for your browser',
    'label.borica.stateOrProvinceName' => 'State/Province',
    'label.borica.terminal_id' => 'Terminal ID',
    'label.bwt.description' => 'Description',
    'label.cod.description' => 'Description',
    'label.cod.text' => 'The description above will be visible for your customers after every successfull placed order with those payment providers.<br></br>If you\'re describing BWT, here you can provide the bank details for your customers.<br>If you\'re describing COD, here you can write to your customers that they would have to pay cash on the delivery provider.',
    'label.voucher.description' => 'Description',
    'label.voucher.text' => 'The description above will be visible for your customers after every successfull placed order with those payment providers.',
    'label.epay.app_id' => 'APP ID',
    'label.epay.kin' => 'KIN',
    'label.epay.secret' => 'SECRET',
    'label.fibank.certificate_password' => 'Certificate password',
    'label.free_leasing' => 'Free instalments',
    'label.mobilpay.certificate_private_real' => 'Real Private Key',
    'label.mobilpay.certificate_private_test' => 'Test Private Key',
    'label.mobilpay.certificate_real' => 'Real Public Certificate',
    'label.mobilpay.certificate_test' => 'Test Public Certificate',
    'label.mobilpay.merchant_id' => 'MobilPay Merchant ID',
    'label.mollie.live_api_key' => 'Live API key',
    'label.mollie.profile_id' => 'Profile id',
    'label.mollie.test_api_key' => 'Test API key',
    'label.mypos.card_payment' => 'Card Payment',
    'label.mypos.card_token_request' => 'Card token request',
    'label.mypos.card_token_request_none' => 'None',
    'label.mypos.card_token_request_only_store' => 'Only Store',
    'label.mypos.card_token_request_pay_and_store' => 'Pay and Store',
    'label.mypos.checkout_form_type' => 'Checkout Form Type',
    'label.mypos.configuration_pack' => 'Configuration Pack',
    'label.mypos.full_payment_form' => 'Full payment form',
    'label.mypos.ideal' => 'iDeal',
    'label.mypos.payment_method' => 'Payment method',
    'label.mypos.private_key' => 'Private Key',
    'label.mypos.production_keyindex' => 'Production Key Index',
    'label.mypos.production_url' => 'Production URL',
    'label.mypos.public_certificate' => 'myPOS Public Certificate',
    'label.mypos.signup' => 'sign up',
    'label.mypos.simplified_payment_form' => 'Simplified payment form',
    'label.mypos.store_id' => 'Store ID',
    'label.mypos.test_keyindex' => 'Test Key Index',
    'label.mypos.test_url' => 'Test URL',
    'label.mypos.ultra_simplified_payment_form' => 'Ultra-simplified payment form',
    'label.mypos.wallet_number' => 'Wallet Number',
    'label.paypal.email' => 'Email',
    'label.paypal.fallback_currency' => 'This is the currency that you will receive your paymnets in',
    'label.percent_per_month' => 'Percent per month',
    'label.provider_image' => 'Logo',
    'label.skrill.api_password' => 'API password',
    'label.skrill.email' => 'Email',
    'label.skrill.merchant_id' => 'Merchant ID',
    'label.skrill.signature_hash_algorithm' => 'Signature hash algorithm',
    'label.skrill.signature_secret' => 'Signature secret',
    'label.storefront_name' => 'Name of the service',
    'label.stripe.mode' => 'Test mode',
    'label.cpay.mode' => 'Test mode',
    'label.tbi.description' => 'Description',
    'label.tbi.email' => 'Email',
    'label.tbi.id' => 'Numbers agreement',
    'label.tbi.max' => 'Maximum period',
    'label.tbi.min' => 'Minimum period',
    'label.tbi.start_price' => 'Price',
    'label.tbi.step' => 'Stepwise interval',
    'label.tbi.text' => 'The description above will be visible for your customers after every successfull placed order with those payment providers.',
    'label.ubb.payment_type' => 'Payment Method',
    'label.ubb.payment_type_1' => 'Direct sale',
    'label.ubb.payment_type_4' => 'Authorization + capture',
    'label.ubb.resource_file' => 'Resource file',
    'label.ucf.default_cop' => 'Default online product code',
    'option.skrill.md5' => 'Md5',
    'option.skrill.sha256' => 'Sha256',
    'ph.amount_from' => 'Amount from',
    'ph.amount_to' => 'Amount to',
    'ph.authorize.app_login_key' => 'Enter app login key',
    'ph.authorize.transaction_key' => 'Enter transaction key',
    'ph.bnp.live_secret_key' => 'Password for the certificate provided by BNP Paribas for Real stage',
    'ph.bnp.test_secret_key' => 'Password for the certificate provided by BNP Paribas for Test stage',
    'ph.bwt.description' => 'Enter description',
    'ph.cod.description' => 'Enter description',
    'ph.epay.kin' => 'Enter kin',
    'ph.epay.secret' => 'Enter secret',
    'ph.free_leasing' => 'Example: 3,6,18',
    'ph.mobilpay.merchant_id' => 'Enter your merchant ID here',
    'ph.mypos.production_keyindex' => 'Enter Key Index',
    'ph.mypos.production_url' => 'Enter Production URL',
    'ph.mypos.store_id' => 'Enter Store ID',
    'ph.mypos.test_keyindex' => 'Enter Test Key Index',
    'ph.mypos.test_url' => 'Enter Test URL',
    'ph.mypos.wallet_number' => 'Enter Wallet Number',
    'ph.mypos.configuration_pack' => 'Paste base64 encoded configuration pack',
    'ph.paypal.email' => 'Enter email',
    'ph.skrill.api_password' => 'Enter API password',
    'ph.skrill.email' => 'Enter email',
    'ph.skrill.merchant_id' => 'Enter merchant ID',
    'ph.skrill.signature_hash_algorithm' => 'Choose algorithm',
    'ph.skrill.signature_secret' => 'Enter signature secret',
    'ph.stripe.test_secret_key' => 'Enter test secret key',
    'ph.tbi.description' => 'Enter description',
    'ph.tbi.email' => 'Enter email',
    'ph.tbi.id' => 'Enter number agreement',
    'ph.tbi.max' => 'Enter maximum period',
    'ph.tbi.min' => 'Enter minimum period',
    'ph.tbi.step' => 'Select stepwise interval',
    'succ.delete' => 'Payment method successfully deleted!',
    'succ.edit' => 'Payment method successfully edited!',
    'switch.live' => 'Live mode',
    'th.name' => 'Payment method',
    'label.everypay.public_key' => 'Public key',
    'label.everypay.public_key_placeholder' => 'Enter your public key for Everypay',
    'label.everypay.secret_key' => 'Secret key',
    'label.everypay.secret_key_placeholder' => 'Enter your secret key for Everypay',
    'label.raiffeisen.title_form' => 'Merchant identification',
    'label.raiffeisen.merchant_id' => 'Merchant ID',
    'label.raiffeisen.terminal_id' => 'Terminal number',
    'label.raiffeisen.form_help' => 'After entering <b> Merchant ID </b> and <b> Terminal Number </b>, download the certificate. <br /> After downloading the certificate, you need to send it to the bank for activation.',
    'label.raiffeisen.download_crt' => 'Certificate download',
    'label.cpay.merchant_id' => 'Merchant ID',
    'ph.cpay.merchant_id' => 'Merchant ID',
    'help.cpay.merchant_id' => 'Merchant ID given by cPay. Once stored can\'t be changed.',
    'label.cpay.merchant_name' => 'Merchant name',
    'ph.cpay.merchant_name' => 'Merchant name',
    'help.cpay.merchant_name' => 'The name of the merchant in cPay. Once stored can\'t be changed.',
    'label.cpay.merchant_password' => 'Merchant password',
    'ph.cpay.merchant_password' => 'Password',
    'help.cpay.merchant_password' => 'Payment password to identify with cPay',
    'label.settle.mode' => 'Test mode',
    'help.settle.mode' => 'Use test mode to test your connection. Live mode is for the actual payment processing. Use live mode when you have verified your credentials.',
    'label.settle.merchant_id' => 'X-Settle-Merchant',
    'help.settle.merchant_id' => 'X-Settle-Merchant given by Settle when you create API key.',
    'label.settle.merchant_key' => 'Secret RSA Key',
    'ph.settle.merchant_id' => 'X-Settle-Merchant',
    'ph.settle.merchant_key' => 'Secret RSA Key',
    'help.settle.merchant_key' => 'Secret RSA Key given by Settle when you create API key.',
    'label.settle.merchant_settle_user' => 'X-Settle-User',
    'ph.settle.merchant_settle_user' => 'X-Settle-User',
    'help.settle.merchant_settle_user' => 'The X-Settle-User given by Settle when you create API key',
    'label.newpay.mode' => 'Test mode',
    'help.newpay.mode' => 'Use test mode to test your connection. Live mode is for the actual payment processing. Use live mode when you have verified your credentials.',
    'label.newpay.api_key' => 'API Key',
    'label.newpay.api_secret' => 'API Secret',
    'ph.newpay.api_key' => 'API key',
    'ph.newpay.api_secret' => 'API secret',
    'help.newpay.api_key' => 'Enter your NewPay API Key',
    'help.newpay.api_secret' => 'Enter your NewPay API Secret',
    'label.bwt.short_description' => 'Description when choosing the payment methodе',
    'label.bwt.short_description.help' => 'The description entered above will be visible to your customers every time they choose the payment method when finalizing the order.',
    'label.ibank.public_key' => 'Public key',
    'label.ibank.private_key' => 'Private key',
    'label.ibank.public_key_hosted' => 'Public key (hosted)',
    'label.ibank.private_key_hosted' => 'Private key (hosted)',
    'label.ibank.public_key_test' => 'Public key (test)',
    'label.ibank.private_key_test' =>  'Private key (test)',
    'label.ibank.public_key_hosted_test' => 'Public key (hosted, test)',
    'label.ibank.private_key_hosted_test' => 'Private key (hosted, test)',
    'label.authorize_payment' => 'Authorize payment',
    'help.authorize_payment' => 'This option allows you to reserve the order amount and debit the customer\'s debit/credit card at a later date.',
    'help.authorize_payment_auto' => 'In this method, the system will automatically debit the user after the order is marked as Shipped.',
    'help.authorize_payment_manual' => 'With this method, you will only be able to capture the payment manually.',
    'text.auto' => 'Automatic Capture',
    'text.manual' => 'Manual Capture',
    'label.mokka.store_id' => 'Store ID',
    'label.mokka.store_key' => 'Secret Key',
    'label.mokka.banner' => 'Label to the products in the shop',
    'mokka.store_id.required' => 'Store ID is required',
    'mokka.store_key.required' => 'Secret key is required',
    'label.mokka.select_country' => 'Select country',
    'label.mokka.country.bg' => 'Bulgaria',
    'label.mokka.country.ro' => 'Romania',
    'label.mokka.country.pl' => 'Poland',
    'label.mokka.endpoint' => 'API base url',
    'label.test' => '( Test Mode )',
    'mokka.store_endpoint.required' => 'You have not entered an API base url',
    'mokka.infobox.title' => 'Activate Mokka payment method',
    'mokka.infobox.text' => '<p class="text-black">Sell more with Mokka! Mokka`s adaptive solution will offer your shoppers a choise of convenient payment plans from Pay in 30 days to Split payments from 3 to 12 months. More payment options for your shoppers, more sales for you.</p>',
    'mokka.infobox.check_text' => '  <div class="row"><p class="col-md-6"><i class="fa fa-check-circle mokka-color"></i> 2-3x higher average basket</p><p class="col-md-6"><i class="fa fa-check-circle mokka-color"></i> Merchant discount rate from 1% of purchase value</p></div><div class="row mb-20"><p class="col-md-6"><i class="fa fa-check-circle mokka-color"></i> +25% average increase in conversion</p><p class="col-md-6"><i class="fa fa-check-circle mokka-color"></i> Settlement period: weekly</p></div>',
    'mokka.register.text' => 'If you already have an account with Mokka, you can skip registration.',
    'mokka.button.skip' => 'Skip registration',
    'mokka.button.register' => '>Proceed to registration',
];
