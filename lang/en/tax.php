<?php

return [
    'act.include_vat' => 'Prices include taxes',
    'action.add_override' => 'Add override',
    'action.delete' => 'delete tax',
    'addon.percent_discount' => '%',
    'confirm.delete' => 'Are you sure you want to delete this tax?',
    'err.can_not_include_parent_and_child_regions' => 'Tax cannot include parent and child region!',
    'err.country_in_use' => 'You can`t set two vat taxes for same country',
    'err.invalid_target' => 'Invalid tax target',
    'err.invalid_taxes' => 'Invalid tax amount',
    'err.invalid_type' => 'Invalid tax type',
    'err.max_%1$s' => 'Maximum is %1$s',
    'err.max_regions_allowed_is_%1$s' => 'Maximum regions limit exceeded(%1$s)',
    'err.name_max_chars_%1$s' => 'Tax name length exceeded(%1$s)',
    'err.name_required' => 'Tax name is required!',
    'err.no_longer_exists' => 'Tax no longer exists!',
    'err.override.category_in_use' => 'You can`t set two taxes for same category',
    'err.some_regions_no_longer_exists' => 'Some of the chosen regions no longer exist!',
    'err.tax_required' => 'Tax amount required!',
    'err.the_following_regions_already_have_a_vat_tax_%1$s' => 'The following regions - %1$s, already have a VAT tax.',
    'err.type_required' => 'Tax type required!',
    'err.you_already_have_a_global_vat_tax_%1$s' => 'You already have a global store VAT tax - %1$s',
    'err.you_already_have_vat_tax_%1$s' => 'You already have a VAT tax - %1$s',
    'err.geo_zone_in_use' => 'You can`t set two vat taxes for same geo zone',
    'header.addTax' => 'Add Tax',
    'header.addFee' => 'Add Fee',
    'header.edit' => 'Edit tax',
    'header.taxes' => 'Taxes',
    'help.description' => 'A short description of the tax could be visible on your Storefront',
    'help.maps_required' => 'This functionality can only be used after you save a Google Maps API key',
    'help.name' => 'e.g. VAT, Service tax',
    'help.target' => 'Select a region where this tax will be applied or',
    'help.tax' => 'The amount of the tax',
    'help.vat_tax' => 'Is this tax the country tax?',
    'label.category_override' => 'Exceptions by categories',
    'label.country' => 'Country in which to apply this VAT',
    'label.description' => 'Description',
    'label.flat' => 'Flat',
    'label.geo_zone' => '',
    'label.name' => 'Name',
    'label.percent' => 'Percent',
    'label.region_override' => 'Exceptions by regions',
    'label.regions' => 'Tax regions',
    'label.target' => '',
    'label.tax' => 'Rate',
    'label.the_whole_world' => 'Make it Global',
    'label.type' => '',
    'label.vat_tax' => 'Country Tax',
    'notify.no_records_help' => 'Having trouble with taxes? Follow the link below.',
    'notify.no_records_help_link' => 'Taxes help',
    'notify.no_records_info' => 'Your taxes will show up here',
    'notify.no_records_yet' => 'You have not added any taxes yet',
    'ph.description' => 'Enter tax description',
    'ph.name' => 'Enter tax name',
    'ph.tax' => 'Enter tax amount',
    'succ.add' => 'Tax successfully added',
    'succ.edit' => 'Tax successfully edited',
    'switch.after_shipping' => 'Calculate tax after shipping costs',
    'switch.before_shipping' => 'Calculate tax before shipping costs',
    'switch.flat' => 'flat',
    'switch.include_price' => 'Yes, apply',
    'switch.percent' => 'percent',
    'switch.vat_tax' => 'Yes, it is',
    'th.applies_to' => 'applies to',
    'th.name' => 'name',
    'th.value' => 'Value',
    'tip.include_vat' => 'If the price of your products includes tax, the option must be included. If the prices of your products do not include tax, the option needs to be stopped.',
    'label.oss_registration' => 'One Stop Shop',
    'help.oss_registration' => 'Online sellers, including online marketplaces/platforms can register in one EU Member State and this is valid for the declaration and payment of VAT on all distance sales of goods and cross-border supplies of services to customers within the EU.',
    'link.oss_registration' => 'https://vat-one-stop-shop.ec.europa.eu/index_en',
    'label.payment.error' => 'You have not specified a payment method for which the fee will apply',
    'label.payments' => 'Payment Method Fee',
    'help.payment' => 'The fee will only be applied to',
    'label.shipping.error' => 'You have not selected a shipping method for which the fee will apply',
    'label.shippings' => 'Shipping Method Fee',
    'help.shipping' => 'The charge will only be applied to',
];
