<?php

return [
    'action.add' => 'Add redirect',
    'action.import' => 'Import redirects',
    'err.cannot_have_more_than_%1$s_concurrent_imports' => 'Caanot have more than %1$s concurrent imports',
    'err.new_url_required' => 'New URL is required',
    'err.no_records_added_or_changed' => 'No records have been added or changed',
    'err.old_url_exist_%1$s' => 'The old URL already exists',
    'err.old_url_required' => 'Old URL is required',
    'import.started' => 'The import started',
    'th.new_url' => 'New URL address',
    'th.old_url' => 'Old URL address',
    'th.type' => 'Type of the redirect',
    'type.external' => 'External',
    'type.manual' => 'Manual redirect',
    'filter.type.manual' => 'Manual',
    'filter.type.product' => 'Product',
    'filter.type.category' => 'Category',
    'filter.type.page' => 'Page',
    'filter.type.vendor' => 'Vendor',
    'filter.type.blog' => 'Blog',
    'filter.type.article' => 'blog article',
    'filter.type.section' => 'Section',
];
