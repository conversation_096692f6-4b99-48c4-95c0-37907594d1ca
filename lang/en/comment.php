<?php

return [
    'action.approve' => 'Approve',
    'action.manage' => 'Manage',
    'action.mark_approved' => 'Approve comment',
    'action.mark_as_not_spam' => 'Mark as pending',
    'action.mark_as_spam' => 'Mark as spam',
    'confirm.delete' => 'Are you sure this comment deserves a deletion?',
    'err.cannot_be_longer_than_%1$s_characters' => 'Comments can not be longer than %1$s characters',
    'err.comments_are_disabled' => 'The comments for this post are disabled',
    'err.content_required' => 'You forgot to type a message',
    'err.email_required' => 'Your email address is required',
    'err.name_required' => 'Your name is required',
    'filter.status_%1$s' => 'Status %1$s',
    'header.comments' => 'Blog comments',
    'header.manage' => 'Manage',
    'label.said' => 'Added a note',
    'not_linked_to_any_article' => 'Comment is not linked to any article',
    'notify.no_records_help' => 'Having trouble with article comments? Follow the link below.',
    'notify.no_records_help_link' => 'Article comments help',
    'notify.no_records_info' => 'Article comments will show up here',
    'notify.no_records_yet' => 'There are no article comments yet',
    'status.approved' => 'Approved',
    'status.spam' => 'Spam',
    'succ.delete' => 'The cloud is safe now',
    'th.comment' => 'Comment',
    'th.date' => 'Date',
    'th.name' => 'User',
    'th.status' => 'Status',
];
