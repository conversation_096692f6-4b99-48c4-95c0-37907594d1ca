<?php

return [
    'error.file.not.found' => 'File not found. Are you sure this is the correct path - ',
    'header.install' => 'Workflow installation',
    'header.setting' => 'Workflow settings',
    'help.install' => '',
    'help.setting' => 'Set up the settings below in order to configure correctly your online store with Workflow.',
    'info.install' => '- import products from Workflow in your online shop<br>
- export orders from CloudCart to Workflow',
    'info.title' => 'Workflow ERP',
    'label.has_ssl' => 'Has SSL',
    'label.host' => 'Host',
    'label.orders.export.file.path' => 'The path of the file where the orders will be imported from CloudCart',
    'label.password' => 'Password',
    'label.port' => 'Port',
    'label.products.file.path' => 'The path of the file from which the products will be imported',
    'label.quantities.file.path' => 'The path of the file from which the inventory will be imported',
    'label.username' => 'Username',
    'placeholder.orders.export.file.path' => 'Example: D:/CloudCart/orders.csv',
    'placeholder.products.file.path' => 'Example: D:/CloudCart/products.xml',
    'placeholder.quantities.file.path' => 'Example: D:\\CloudCart\\product_quantities.csv',
    'title.able_to' => 'With Workflow you will be able to:',
    'keyid' => 'Workflow ID'
];
