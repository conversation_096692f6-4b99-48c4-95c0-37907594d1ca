<?php return [
    'Archive' => 'Archive',
    'Barcode' => 'Barcode',
    'Created' => 'Created',
    'Discount' => 'Discount',
    'Free_shipping' => 'Free shipping',
    'Guest' => 'Guest',
    'Manual_discount' => 'Manual discount',
    'Mark as completed' => 'Mark as completed',
    'Marked_order_payment_as_received' => 'Marked order payment as received',
    'Message' => 'Message',
    'Order_added_with_id' => 'Order added with ID',
    'Order_customer_fields' => 'Order customer fields',
    'Order_status' => 'Order status',
    'Product' => 'Product',
    'Product_name' => 'Product name',
    'Product_price' => 'Product price',
    'total_price_before_discount' => 'Total price of product before discount',
    'total_price_after_discount' => 'Total price of the product after discount',
    'Product_quantity' => 'Product quantity',
    'SKU' => 'SKU',
    'Sale' => 'Sale',
    'Unarchive' => 'Unarchive',
    'Vendor' => 'Brand',
    'Weight' => 'Weight',
    'abandoned_count' => 'Total sent messages: :total',
    'abandoned' => 'Abandoned',
    'abandoned.confirm.delete' => 'Are you sure you want to remove the selected abandoned orders?',
    'action.abandoned_send_restore_link_subscriber' => 'Send restore email',
    'action.add_order' => 'Create an order',
    'action.add_order_discount' => 'Add order discount',
    'action.add_product_discount' => 'Add product discount',
    'action.archive' => 'Archive',
    'action.cancel_order' => 'Cancel order',
    'action.cancel_payment' => 'Cancel payment',
    'action.choose_shipping_provider' => 'Choose shipping method',
    'action.choose_status' => 'Choose status',
    'action.complete' => 'Complete',
    'action.export' => 'Export',
    'action.fulfill_products' => 'Fulfill products',
    'action.history' => 'History',
    'action.mark_as_paid' => 'Mark as paid',
    'action.payment_add' => 'Add payment',
    'action.payment_complete' => 'Complete payment',
    'action.payment_lease' => 'Send an inquiry to the bank',
    'action.payment_refund' => 'Refund',
    'action.payment_cancel_authorization' => 'Cancel authorization',
    'action.payment_sync' => 'Synchronize',
    'action.payment_authorize' => 'Capture payment (:amount)',
    'action.payment_void' => 'Void',
    'action.print_order' => 'Print order',
    'action.product_add' => 'Add product',
    'action.remove_order_discount' => 'Remove order discount',
    'action.remove_product_discount' => 'Remove product discount',
    'action.ship_items' => 'Fulfill products',
    'action.unarchive' => 'Unarchive',
    'action.view_details' => 'View details',
    'action.view_invoice' => 'View invoice',
    'action.view_more' => 'View more',
    'action.view_credit_note' => 'Credit note',
    'action.create_credit_note' => 'Create credit note',
    'action.download_credit_note' => 'Download credit note',
    'action.send_credit_note' => 'Send credit note to client',
    'action.notify.credit_note_created' => 'The credit note was created.',
    'action.notify.credit_note_create_error' => 'Currently we can\'t create credit note.',
    'action.notify.credit_note_sent' => 'The credit note was sent to the client.',
    'action.notify.credit_note_send_error' => 'Currently we can\'t send credit note to the client.',
    'add_address' => 'Add address',
    'add_customer' => 'Add customer',
    'added' => 'Added',
    'added_to_order' => 'Added to order',
    'added_to_order_product' => 'Product added to order',
    'addon.percent_discount' => '%',
    'address' => 'Address',
    'address_add_shipping' => 'Add shipping address',
    'address_change_billing' => 'Change billing',
    'address_change_shipping' => 'Change shipping',
    'address_edit_billing' => 'Edit billing',
    'address_edit_shipping' => 'Edit shipping',
    'address_reposition' => 'Reposition',
    'address_reposition_billing' => 'Reposition billing',
    'address_reposition_shipping' => 'Reposition shipping',
    'admin_note' => 'Administrative note',
    'aggregate.success' => 'The download request was successful. In a few minutes you will receive an email with a download link.',
    'aggregate.type' => 'invoices',
    'amount' => 'Amount',
    'percent' => 'Percent',
    'archived' => 'Archived',
    'are_you_sure' => 'By cancelling this order, you won\'t be able to restore it',
    'billing_address' => 'Billing address',
    'changed_to' => 'Changed to',
    'choose_another_address' => 'Change address',
    'choose_discount' => 'Choose discount',
    'choose_discount_target' => 'Choose discount target',
    'choose_existing_address' => 'Choose existing address',
    'confirm.archive' => 'Archive',
    'confirm.complete' => 'Complete',
    'confirm.mark_as_paid_confirm' => 'If you mark the order as paid, you won\'t be able to change it\'s status again',
    'confirm.order_product_delete_confirm' => 'Are you sure you want to delete this product from the order?',
    'confirm.remove_order_discount' => 'Remove order discount',
    'confirm.remove_product_discount_confirm_message' => 'Discount removed',
    'confirm.unarchive' => 'Unarchive',
    'created' => 'Order is created',
    'crossSell.discount' => 'Cross Sell',
    'customer' => 'Customer',
    'customer_completed_orders' => 'Completed orders',
    'customer_edit' => 'Edit customer',
    'customer_email' => 'Customer email',
    'customer_group' => 'Customer group',
    'customer_total_orders' => 'Total orders',
    'customer_view_profile' => 'View profile',
    'date' => 'Date',
    'date_added' => 'Date added',
    'date_archived' => 'Date archived',
    'date_fulfilled' => 'Date fulfilled',
    'date_paid' => 'Date paid',
    'date_updated' => 'Date last updated',
    'deleted' => 'Order is deleted',
    'discount' => 'Discount',
    'discount_type_flat' => 'Flat type',
    'discount_type_percent' => 'Percent type',
    'edited_to' => 'Edited to',
    'err.abandoned_no_emails_sent' => 'No emails sent. You can send only 1 email per customer per day!',
    'err.address_no_longer_exists' => 'The address no longer exists',
    'err.begin_date_invalid_format' => 'Invalid begin date format',
    'err.cannot_add_digital_product_already_in_cart' => 'The digital product you are trying to add is already present in the order.',
    'err.cannot_change_status_of_archived_order_unarchive_first' => 'Cannot change the status of archived order. Unarchive first.',
    'err.cannot_complete_cancelled_order' => 'Cannot complete cancelled order',
    'err.cannot_perform_this_operation_on_archived_order' => 'Cannot perform this operation on archived order',
    'err.cannot_ship_items_without_shipping_provider' => 'Products cannot be fulfilled without a shipping method.',
    'err.choose_customer_adddress' => 'Customer address not provided',
    'err.creation_failed' => 'Order failed',
    'err.customer_email_requred' => 'Customer email is required',
    'err.customer_first_name_cannot_be_more_than_%1$s_characters' => '\'customer first name\' cannot be more than %1$s characters',
    'err.customer_first_name_empty' => '\'customer first name\' is empty',
    'err.customer_has_no_addresses' => 'The customer has no address',
    'err.customer_last_name_cannot_be_more_than_%1$s_characters' => '\'customer last name\' cannot be more than %1$s characters',
    'err.customer_last_name_empty' => '\'customer last name\' is empty',
    'err.daterange_required' => 'Date range is required',
    'err.delete_status_orders' => 'This status has attached: total orders',
    'err.discount_no_longer_active' => 'The discount is no longer active',
    'err.discount_no_longer_exists' => 'The discount no longer exists',
    'err.discount_not_enough_uses_left' => 'Not enough discount uses left',
    'err.discount_target_for_different_category' => 'This discount is targeted for different category',
    'err.discount_target_for_different_customer_group' => 'This discount is targeted for different customer group',
    'err.discount_target_for_different_product' => 'This discount is targeted for different product',
    'err.discount_target_for_different_region' => 'This discount is targeted for different region',
    'err.discount_target_is_for_specific_region' => 'This discount is for specific region',
    'err.discount_target_no_longer_exists' => 'This discount target no longer exists',
    'err.discount_target_required' => 'Discount target is required',
    'err.discount_negative_type_value' => 'The value of the rebate can not exceed the subtotal of the order',
    'err.discount_type_required' => 'Discount type is required',
    'err.end_date_invalid_format' => 'End date is invalid.',
    'err.flat_discount_must_be_less_than_order_subtotal' => 'The flat discount must be lesser than the order subtotal',
    'err.flat_discount_must_be_less_than_product_price' => 'The flat discount must be lesser than the product price',
    'err.flat_discount_value_cannot_be_empty' => 'The flat discount value can not be empty',
    'err.for_change_fulfillment_status_use_button' => 'You can not change the shipping status from here because this order has been made with shipping provider that does not allow changing the fulfilment status manually',
    'err.invalid_address' => 'Address is invalid.',
    'err.invalid_address_type' => 'Invalid type of the address',
    'err.invalid_amount' => 'Amount is invalid.',
    'err.invalid_barcode' => 'Barcode is invalid.',
    'err.invalid_category' => 'Product category is invalid.',
    'err.invalid_customer' => 'Customer is invalid.',
    'err.invalid_customer_group' => 'Customer group is invalid.',
    'err.invalid_customers' => 'Customers are invalid.',
    'err.invalid_daterange_format' => 'Date range format is invalid.',
    'err.invalid_discount_target' => 'Discount target is invalid.',
    'err.invalid_discount_type' => 'Discount type is invalid.',
    'err.invalid_filter_compare_operator' => 'Filter compare operator is invalid.',
    'err.invalid_filter_value' => 'Filter value is invalid.',
    'err.invalid_group' => 'Customer group is invalid.',
    'err.invalid_invoice_number' => 'Invalid invoice number',
    'err.invalid_note_administrator' => 'Note to administrator is invalid.',
    'err.invalid_order' => 'Order is invalid.',
    'err.invalid_order_id' => 'Order ID is invalid.',
    'err.invalid_product_id' => 'Product ID is invalid.',
    'err.invalid_provider' => 'Payment provider is invalid.',
    'err.invalid_quantity' => 'Quantity is invalid.',
    'err.invalid_region' => 'Region is invalid.',
    'err.invalid_request' => 'Request is invalid.',
    'err.invalid_restore_link' => 'Restore link is invalid.',
    'err.invalid_shipping_provider' => 'Shipping method is invalid.',
    'err.invalid_sku' => 'SKU is invalid.',
    'err.invalid_status' => 'Order status is invalid.',
    'err.invalid_type' => 'Invalid type.',
    'err.invalid_value' => 'Variant`s value is invalid.',
    'err.invalid_variant_id' => 'Variant ID is invalid.',
    'err.invalid_vendor' => 'Brand is invalid.',
    'err.no_addresses_available' => 'There are no available addresses.',
    'err.no_discounts' => 'There are no discounts available.',
    'err.no_products_for_fulfillment' => 'There are no products for fulfillment.',
    'err.no_such_customer' => 'There is no such customer.',
    'err.no_such_group' => 'There is no such customer group.',
    'err.no_such_order' => 'There is no such order.',
    'err.not_enough_quantity_for_%1$s' => 'There is not enough quantity for %1$s.',
    'err.note_administrator_max_chars_%1$s' => 'The maximum allowed characters for administrator note are %1$s.',
    'err.nothing_changed' => 'Nothing has been changed.',
    'err.only_completed_orders_can_be_archived' => 'Only completed orders can be archived.',
    'err.only_paid_and_fulfilled_orders_can_be_completed' => 'Only paid and/or fulfilled orders can be marked as Completed',
    'err.only_pending_orders_can_be_abandoned' => 'Only open orders can be abandoned.',
    'err.only_pending_orders_can_be_canceled' => 'Only open orders can be canceled.',
    'err.order_already_completed' => 'This order has already been completed.',
    'err.order_already_pending' => 'This order is already pending.',
    'err.order_complete_not_fulfilled' => 'Order must be fulfilled before marked as complete.',
    'err.order_discount_no_longer_exists' => 'Order discount no longer exists.',
    'err.order_does_not_exist' => 'This order does not exists.',
    'err.order_has_discount' => 'This order has discount.',
    'err.order_has_no_addresses' => 'This order has no address.',
    'err.order_has_no_payments' => 'This order has no payments.',
    'err.order_must_have_at_least_one_product' => 'Orders should contain at least one product.',
    'err.order_no_longer_exists' => 'Order no longer exists.',
    'err.order_payment_does_not_exist' => 'This order payment does not exist.',
    'err.order_payment_incomplete' => 'Payment failed',
    'err.order_product_no_longer_exists' => 'This order product no longer exists.',
    'err.original_product_variant_does_not_have_price_please_enter_override_price' => 'The original product variant does not have price. Please, enter override price',
    'err.payment_amount_differs_from_order_total' => 'The payment amount is different than the order total.',
    'err.payment_provider_already_set' => 'A payment provider has already been set.',
    'err.percent_discount_value_cannot_be_empty' => 'The percent discount value cannot be empty.',
    'err.product_add_not_enough_quantity' => 'Not enough quantity available.',
    'err.product_already_has_discount' => 'This product already has a discount.',
    'err.product_cant_have_shipping_discount' => 'Products can not have shipping discount',
    'err.product_no_longer_exists' => 'This product no longer exists',
    'err.product_no_longer_has_discount' => 'This product no longer has a discount',
    'err.product_variant_no_longer_exists' => 'Product variant no longer exists',
    'err.provider_config_mismatch' => 'The configuration of the provider mismatch',
    'err.quantity_required' => 'Quantity is required',
    'err.shipping_products_required' => 'Please choose at least one product to fulfill',
    'err.shipping_provider_does_not_have_insurance' => 'The shipping method does not have an insurance',
    'err.shipping_provider_no_matching_rate' => 'The shipping method has no matching rate',
    'err.shipping_provider_no_matching_rate_price' => 'Your total order value (:total) does not fall within the shipping cost range for the selected shipping method. The lowest value is :min and the highest is :max',
    'err.shipping_provider_no_matching_rate_weight' => 'Your total order weight (:total) does not fall within the shipping weight range for the selected shipping method. The lowest weight is :min and the highest is :max',
    'err.shipping_provider_no_matching_rate_price_and_weight' => 'Your total order value and total weight does not fall within the shipping cost and weight range for the selected shipping method',
    'err.shipping_tracking_url_max_chars_%1$s' => 'The maximum allowed characters for shipping tracking URL is %1$s',
    'err.some_orders_archived' => 'Some of the orders are archived',
    'err.some_products_already_shipped' => 'Some of the products are already fulfilled',
    'err.some_products_no_longer_in_order' => 'Some of the products are no longer in the order',
    'err.start_date_later_than_end_date' => 'Start date cannot be earlier than end date.',
    'err.target_cannot_be_related_to_product' => 'The target can not be related to the product',
    'err.target_not_related_to_product' => 'The target is not related to the product',
    'err.variant_required' => 'Variant is required',
    'err.waybill.out_of_stock_products' => 'This order cannot be shipped because it contains out of stock product(s).',
    'existing_discount' => 'This discount already exists',
    'fast_order' => 'Fast order',
    'filter.barcode_%1$s' => 'Barcode %1$s',
    'filter.category_%1$s' => 'Category %1$s',
    'filter.country_%1$s' => 'Country %1$s',
    'filter.customer_%1$s' => 'Customer %1$s',
    'filter.customer_group_%1$s' => 'Customer group %1$s',
    'filter.customers_in_%1$s' => 'Customers in %1$s',
    'filter.date_added' => 'Date added',
    'filter.date_archived' => 'Date archived',
    'filter.date_archived_%1$s_%2$s' => 'Date archived %2$s',
    'filter.discount_type_' => '',
    'filter.discount_type_%1$s' => 'Discount type %1$s',
    'filter.discount_type_fixed' => '',
    'filter.discount_type_flat' => 'Filter by flat rate',
    'filter.discount_type_percent' => 'Filter by percent',
    'filter.discount_type_shipping' => 'Filter by free shipping',
    'filter.fast_order' => 'Fast order',
    'filter.has_discount' => 'Has discount',
    'filter.invoice_number_%1$d' => 'Invoice Number %1$d',
    'filter.made.through.messenger-bot' => 'Messenger bot',
    'filter.not_fast_order' => 'Not fast order',
    'filter.order_added_%1$s_%2$s' => 'Order added %1$s %2$s',
    'filter.order_added_%1$s_%2$s_4' => 'Добавена поръчка after %1$s and before %2$s',
    'filter.order_archived_%1$s' => 'Order archived %1$s',
    'filter.order_fulfillment_status_%1$s' => 'Order fulfillment status %1$s',
    'filter.order_is_%1$s' => 'Order is %1$s',
    'filter.order_made_through_%1$s' => 'Order was made through %1$s',
    'filter.order_payment_status_%1$s_%2$s' => 'Payment status %1$s %2$s',
    'filter.order_product_date_range_%1$s_%2$s' => 'Order added from %1$s to %2$s',
    'filter.order_product_id_range_%1$s_%2$s' => 'Order ID from %1$s to %2$s',
    'filter.order_product_supplier_%1$s' => 'Supplier: %1$s',
    'filter.order_recovered_%1$s' => 'Order was recovered through %1$s',
    'filter.order_saleschannel_%1$s' => 'Sales channel %1$s',
    'filter.order_status_%1$s_%2$s' => 'Order status %1$s %2$s',
    'filter.order_total_price_%1$s_%2$s' => 'Order total price %1$s %2$s',
    'filter.order_updated_%1$s_%2$s' => 'Order updated %1$s %2$s',
    'filter.payment_amount' => 'Amount',
    'filter.payment_amount_%1$s_%2$s' => 'Payment amount %1$s %2$s',
    'filter.payment_date_added' => 'Data added',
    'filter.payment_provider_%1$s_%2$s' => 'Payment provider %1$s %2$s',
    'filter.payment_status' => 'Payment Status',
    'filter.payment_status_%1$s_%2$s' => 'Payment status %1$s %2$s',
    'filter.product_fulfilled_%1$s' => 'Product fulfilled %1$s',
    'filter.product_price_%1$s_%2$s' => 'Product price %1$s %2$s',
    'filter.product_quantity_%1$s_%2$s' => 'Product quantity %1$s %2$s',
    'filter.product_sale_%1$s' => 'Product sale %1$s',
    'filter.recovered_from_email' => 'from email',
    'filter.recovered_from_messenger' => 'from Messenger',
    'filter.region_%1$s' => 'Region %1$s',
    'filter.shipping_provider_%1$s_%2$s' => 'Shipping provider %1$s %2$s',
    'filter.sku_%1$s' => 'SKU %1$s',
    'filter.status' => 'Status',
    'filter.status_' => '',
    'filter.status_fulfillment' => 'Fulfillment status',
    'filter.vendor_%1$s' => 'Brand %1$s',
    'fulfillment_date' => 'Fulfillment date',
    'fulfillment_tracking_url' => 'Fulfillment tracking URL',
    'gmap_address' => 'Google map address',
    'go_to_manual' => 'Visit our online manual.',
    'guest_first_name' => 'Guest first name.',
    'guest_last_name' => 'Guest last name.',
    'guest_no_email' => 'No email available.',
    'has_discount' => 'Has discount',
    'has_following_fields_edited_to' => 'Has following fields edited to',
    'header.abandoned_#' => 'Abandoned #',
    'header.abandoned_orders' => 'Abandoned Orders',
    'header.add_order' => 'Create new order',
    'header.add_order_discount' => 'Add order discount',
    'header.add_order_payment' => 'Add order payment',
    'header.add_product' => 'Add product',
    'header.add_product_discount' => 'Add product discount',
    'header.address_add' => 'Choose existing address',
    'header.address_change' => 'Address change',
    'header.address_edit' => 'Address edit',
    'header.chargeback_order_payment_%1$s' => 'Chargeback order payment %1$s',
    'header.complete_order_payment_%1$s' => 'Complete order payment %1$s',
    'header.customer_edit' => 'Customer edit',
    'header.edit_product' => 'Edit product',
    'header.fulfill_products_manual' => 'Fulfill products',
    'header.order_history' => 'Order history',
    'header.order_payments_list' => 'Order payments list',
    'header.orders' => 'Orders',
    'header.product_fulfillment_info' => 'Product fulfillment info',
    'header.void_order_payment_%1$s' => 'Void order payment %1$s',
    'heed_help_abandoned' => 'Need help with abandoned orders?',
    'help.choose_discount_target' => 'Choose the discount target you aim to',
    'help.discount_choose' => 'Choose whether you want to add an existing discount or manually add one.',
    'help.google_map_address' => 'Google maps address.',
    'help.order_payment_amount' => 'Payment amount.',
    'help.order_payment_chargeback_reason' => 'Provide a reason for the payment chargeback',
    'help.order_payment_email_note' => 'A short message which the user will receive in the payment email.',
    'help.order_payment_provider_reference_id' => 'Provider reference id. (This field is optional)',
    'help.order_payment_refund' => 'Provide a reason for the payment refund.',
    'help.ordered_by_facebook' => 'Ordered by Facebook',
    'help.print_template.css_classes.description' => 'These are css classes that will help you change the styles of specific elements. For example: you can use the product-name class to change the way the product name is displayed',
    'help.product_price' => 'Product price',
    'help.product_quantity' => 'Product quantity.',
    'help.update_customer_address_info' => 'Update customer address info.',
    'help.user_map_address' => 'This is the Google maps address.',
    'history.label.' => '',
    'history.label.order_receipt_sent' => 'Receipt sent',
    'history.order_receipt_number' => 'Receipt number #',
    'history_no_address' => 'Has no address',
    'history_no_longer_exists' => 'History no longer exists',
    'history_order_placed' => 'Order placed',
    'history_order_was_recovered_through_%1$s' => 'The order was recovered after sent reminder %1$s',
    'info.abandoned_restore_last_sent_date' => 'Last email sent on',
    'info.edit_shipping' => 'Edit shipping method',
    'info.no_products' => 'No products',
    'info.product_add.no_variants' => 'Not set',
    'info.shipping_notification' => 'Send email notification to customer',
    'invalid request' => 'Invalid request',
    'invoice.type.original' => 'Original',
    'invoice_create_date' => 'Invoice creation date',
    'invoice_number' => 'Invoice Number',
    'ip_address_not_verified' => 'IP address is not verified',
    'ip_address_verified' => 'IP address is verified',
    'ip_country' => 'IP country',
    'ip_info' => 'Fraud protection',
    'is_opened_from_other' => 'Order is opened from :user',
    'label.customer_address' => 'Customer address',
    'label.discount_amount' => 'Discount amount',
    'label.discount_choose' => 'Choose discount',
    'label.discount_type' => 'Discount type',
    'label.invoice_template' => 'Template for printing order`s invoice',
    'label.manual_discount' => 'Discount',
    'label.no_weight' => 'No weight',
    'label.note_from_merchant' => 'Merchant note',
    'label.optional_tracking_url' => 'Optional tracking URL',
    'label.order_payment_amount' => 'Amount',
    'label.order_payment_chargeback_reason' => 'Chargeback reason',
    'label.order_payment_email_note' => 'Payment note',
    'label.order_payment_provider_reference_id' => 'Provider reference ID',
    'label.order_payment_refund' => 'Refund',
    'label.orders' => 'Orders',
    'label.print_template' => 'Template for printing packing slip',
    'label.print_template.css_classes' => 'CSS classes for the default elements',
    'label.product_parameter' => 'Parameter',
    'label.product_price' => 'Price',
    'label.product_price_override' => 'Price override',
    'label.product_quantity' => 'Quantity',
    'label.shipping_date_delivery' => 'Delivery date',
    'label.shipping_date_expedition' => 'Fulfillment',
    'label.tracking_number' => 'Tracking #',
    'label.update_customer_address' => 'Customer address',
    'label.update_customer_address_info' => 'Customer address info',
    'label.update_customer_info' => 'Customer info',
    'label.user_map_address' => 'User map address',
    'made.through' => 'Made trough',
    'manual_discount' => 'Manual discount',
    'manual_discount_settings' => 'Manual discount settings',
    'missing_ip_country' => 'N/A',
    'no_address' => 'No address',
    'no_comment' => 'There is no comment on that order',
    'not_enought_can_sell' => 'Not available',
    'not_enought_cannot_sell' => 'Not available',
    'notify.abandoned_no_records_help' => 'Having trouble with abandoned orders? Follow the link below.',
    'notify.abandoned_no_records_help_link' => 'Abandoned orders help',
    'notify.abandoned_no_records_info' => 'Your store\'s abandoned orders will show up here',
    'notify.abandoned_no_records_yet' => 'There are no abandoned orders yet',
    'notify.no_records_help' => 'Having trouble with orders? Follow the link below.',
    'notify.no_records_help_link' => 'Orders help',
    'notify.no_records_info' => 'Your store\'s orders will show up here',
    'notify.no_records_yet' => 'You have not received any orders yet',
    'notify.shipping_address_is_required' => 'You have to fill in the shipping address in order to be able to prepare this order for shipment',
    'order_#' => 'Order #',
    'order_archive_are_you_sure' => 'This will archive your order. Please confirm.',
    'order_complete_are_you_sure' => 'Are you sure you want to complete this order? If you mark it as completed, you will not be able to change its status never again.',
    'order_date_interval' => 'Order date interval',
    'order_has_no_products' => 'Order has no products',
    'order_id_interval' => 'Order id interval',
    'order_placed_by' => 'Placed by',
    'order_products_fulfilled' => 'Products fulfilled',
    'order_shipping_insurance' => 'Shipping insurance',
    'order_total' => 'Total',
    'order_unarchive_are_you_sure' => 'Do you want to unarchive?',
    'payment.confirm.cancel' => 'Are you sure you want to cancel this payment?',
    'payment.confirm.complete' => 'Are you sure you want to mark this payment as completed?',
    'payment.confirm.lease' => 'The reference will be sent',
    'payment.confirm.refund' => 'Are yo sure you want to refund this payment?',
    'payment.confirm.cancel_authorization' => 'Are yo sure you want to cancel authorization?',
    'payment.confirm.sync' => 'Are you sure you want to synchronize this payment?',
    'payment.confirm.authorize' => 'Are you sure you want to authorize this payment?',
    'payment.confirm.void' => 'Are you sure you want to void this payment?',
    'payment_provider' => 'Payment method',
    'payment_provider_reference_id' => 'Provider reference ID',
    'payment_status' => 'Payment status',
    'ph.type_here' => 'Type here',
    'plan_does_not_support_abandoned' => 'Your current plan does not support abandoned orders. Consider upgrading your plan.',
    'product.add_product' => 'Add product',
    'product_count' => 'Count',
    'provider' => 'Shipping method',
    'recovered' => 'Recovered',
    'referer' => 'Referrer',
    'region' => 'Region',
    'removed_from_order' => 'Removed from order',
    'removed_from_order_product' => 'Product removed from order',
    'saleschannel' => 'Sales Channel',
    'select_product_parameter' => 'Select product parameter',
    'shipping' => 'Shipping',
    'shipping_address' => 'Shipping address',
    'shipping_change' => 'Shipping method change',
    'shipping_discount' => 'Shipping discount',
    'shipping_insurance:' => 'Shipping insurance:',
    'source_info' => 'Source information',
    'status' => 'Order status',
    'status.chage_status' => 'Change status',
    'status.error.invalid' => 'Invalid status',
    'status.error.invalid_type' => 'Invalid type',
    'status.error.no_name' => 'There is no name',
    'status.error.no_status' => 'No status',
    'status.error.order_not_found' => 'Order not found',
    'status_fulfilled' => '',
    'status_fulfillment' => 'Fulfillment',
    'status_authorized' => 'Authorized',
    'status_paid' => '',
    'status_pending' => '',
    'status_{$status}' => '',
    'statuses.title' => 'Statuses',
    'subtotal_products' => 'Subtotal',
    'succ.abandoned_%1$s_emails_sent' => '%1$s emails were successfully sent',
    'succ.abandoned_email_sent_to_client' => 'The email was successfully sent to customer',
    'succ.address_added_success' => '',
    'succ.address_change_success' => 'Address successfully changed',
    'succ.address_edit_success' => 'Address successfully edited',
    'succ.customer_edit_success' => 'Customer successfully edited',
    'succ.order_discount_added' => 'Discount successfully added',
    'succ.order_discount_removed' => 'Discount successfully removed',
    'succ.order_notes_edit_success' => 'Notes edited successfully',
    'succ.order_payment_cancel_success' => 'Payment canceled successfully',
    'succ.order_payment_chargeback_success' => 'Payment chargeback successfully',
    'succ.order_payment_complete_success' => 'Payment successfully completed',
    'succ.order_payment_email_sent_to_client' => 'Email successfully sent to the customer',
    'succ.order_payment_refund_success' => 'Payment successfully refunded',
    'succ.order_payment_sync_success' => 'Payment synchronized successfully',
    'succ.order_payment_void_success' => 'Payment voided successfully',
    'succ.order_product_add_success' => 'Product added successfully',
    'succ.order_product_add_success_without_discount' => 'The product has been added successfully, but no discount has been applied because its price is lower than the product price',
    'succ.order_product_delete_success' => 'Product deleted successfully',
    'succ.order_product_discount_added' => 'Product discount successfully added',
    'succ.order_product_discount_removed' => 'Product discount successfully removed',
    'succ.order_product_edit_success' => 'Product successfully edited',
    'succ.order_products_fulfillment_success' => 'Products successfully fulfilled',
    'succ.shipping_provider_changed' => 'Shipping provided successfully changed',
    'tax.vat_included' => 'Including',
    'tax_amount' => 'Tax',
    'th.abandoned_#' => 'Abandoned #',
    'th.actions' => 'Actions',
    'th.address' => 'Address',
    'th.amount' => 'Amount',
    'th.date' => 'Date',
    'th.date_updated' => 'Date last updated',
    'th.fulfillable_items' => 'Fulfillable items',
    'th.fulfillment' => 'Fulfillment',
    'th.order' => 'Order',
    'th.order_#' => 'Order #',
    'th.ordered_product.order_ids' => 'Orders involving the product',
    'th.payment_amount' => 'Amount',
    'th.payment_last_modified' => 'Last modified',
    'th.payment_provider' => 'Provider',
    'th.payment_status' => 'Payment status',
    'th.product_name' => 'Name',
    'th.product_total' => 'Total',
    'th.products' => 'Products',
    'th.quantity' => 'QTY',
    'th.receiving' => 'Receiving',
    'th.single_product_price' => 'Unit price',
    'th.status' => 'Order status',
    'th.total' => 'Total',
    'title' => 'Orders',
    'to_use_abandoned_upgrade_advanced_or_professional' => 'If you wish to track abandoned orders, upgrade your plan to advanced or professional.',
    'total_price' => 'Total',
    'tracking' => 'Tracking',
    'tracking_return' => 'Return waybill',
    'updated' => 'Order is updated',
    'upgrade_plan' => 'Upgrade plan',
    'usn' => 'Unique sale number (USN)',
    'utm_campaign' => 'UTM campaign',
    'utm_medium' => 'UTM medium',
    'utm_source' => 'UTM source',
    'with_tracking_url' => 'With tracking URL',
    'notify_customer' => 'Notify the client',
    'notify_customer_help' => 'When this option is active, the customer will be notified of any changes to the order',
    'download.invoice' => 'Invoice',
    'download.credit' => 'Credit note',
    'filter.credit_note' => 'Issued credit notice',
    'label.credit_template.css_classes' => 'CSS classes for the default elements',
    'label.credit_template' => 'Template for printing a credit notice ',
    'help.credit_template.css_classes.description' => 'These are css classes that will help you change the styles of specific elements. For example: you can use the product-name class to change the way the product name is displayed.s',
    'credit_number' => 'Credit notice number',
    'credit_create_date' => 'Date of issue of the credit notice',

    'new.order.request_customer' => 'You have not selected a customer',
    'new.order.request_address' => 'You have not selected a delivery address',
    'new.order.success_create' => 'You have successfully added the order',
    'change.payment_success' => 'You have successfully changed your payment method',
    'product.container.help_text' => '<small>After selecting a customer and delivery address you will be able to go to the second step to add products / payment method / delivery method</small>',
    'subtotal' => 'Subtotal',
    'total' => 'Total',
    'select.customer' => 'Select customer',
    'select.address.help' => 'Once you select a customer, you will be able to select / add a delivery address ',
    'add.new_address' => 'Add a new address ',
    'address.not_fount' => 'No addresses found for the selected customer.',
    'new.order.step2' => 'Next step',
    'alert.manual_order' => 'Customer order completion address:',
    'alert.manual_order_help' => 'Provide a link to your customer to complete the order or',
    'alert.send_as_email' => 'send as email.',
    'notify.send.new_order' => 'You have successfully notified the customer of the order',
    'order.error.products' => 'You have not added any products to your order',
    'order.error.payment' => 'You have not selected a payment method for the order',
    'order.error.shipping' => 'You have not selected a delivery method for the order',
    'new.success.saved' => 'You have successfully saved your order',
    'status_draft' => 'Draft',
    'alert_is_draft' => 'You need to add <b> products </b>, <b> payment method </b>, and <b> delivery method </b> to save your order.',
    'alert_is_draft.start' => 'You need to add',
    'alert_is_draft.products' => '<b> products </b>',
    'alert_is_draft.payment' => '<b> payment method </b>',
    'alert_is_draft.shipping' => '<b> delivery method </b>',
    'alert_is_draft.and' => 'and',
    'alert_is_draft.end' => 'to save your order.',
    'save_order' => 'Save order',
    'save_and_create_order_info' => 'The system will save the changes and will create order',
    'create_order' => 'Create order',
    'create_order_and_send_client' => 'Create order and send to client',
    'ship_to.office' => 'Delivery to office',
    'ship_to.address' => 'Delivery to address',
    'ship_to.marketplace' => 'In-store pickup',
    'ship_to.title.delivery_method' => 'Delivery method',
    'validation.delivery_to.required' => 'You have not selected a delivery method',
    'validation.office_id.required' => 'You have not selected an office',
    'validation.address_id.required' => 'You have not selected an address',
    'validation.store_id.required' => 'You have not selected a store',
    'filer.is_draft' => 'Draft orders ',
    'filter.only_is_draft_message' => 'Only draft orders are shown',
    'filer.is_admin' => 'Created by admin',
    'filter.only_is_admin_message' => 'Only orders created through the admin panel are shown',
    'draft.button_1' => 'The system will save the changes and create orders',
    'draft.button_2' => 'The system will save the changes and send an email to the customer with a link to complete the order.',
    'draft.button_3' => 'The system will send an email to the customer with an address for completing / paying for the order',
    'error.billing_address' => 'You have not entered an Billing Address',
    'notify.shipping_address_is_required.is_fast_order' => 'The order was made through the "Quick Order" application. Therefore, you as the administrator need to enter a delivery address to send the order.',
    'label.print_template.sorter' => 'Product sorting',
    'label.print_template.sorter.help' => 'To sort the products in the stock list, you need to add the parameter of your choice to the variable $products. Example $products:price-desc',
    'banned_ip.add' => 'Block new IP',
    'banned_ip.ip_address' => 'IP address',
    'banned_ip.add_from_order' => 'Block',
    'banned_ip.ban' => 'Block clients IP addresses',
    'banned_ip.ban_reason' => 'The order was made from a blocked IP address. Reason:',
    'order_banned_ip' => 'Automatic cancel orders made by specific IP addresses',
    'filter.discount_code' => 'Discount code: ',
    'filter.discount_code.option' => 'Discount code',
    'payment.status.not_payment' => 'No payment received',
    'title.cart_time_life' => 'Cart life time before order',
    'payment.authorized' => 'Authorized payment (:amount)',
    'error.authorize_amount' => 'The order amount is :total and cannot exceed the authorized payment :amount.',
    'history.label.glovo_store_is_closed' => 'The order has not been sent - the location is out of hours',
    'history.label.glovo_invlid_credentials' => 'Invalid Glovo connection credentials',
    'history.label.send_erp_success' => 'Order successfully sent to ERP',
    'history.label.send_erp_error' => 'Error sending order to ERP',
    'history.error.text' => 'An error occurred while sending the order to',
    'history.error.details' => 'Error: ',
    'history.success.text' => 'Order successfully sent to ',
    'history.success.details' => 'Order ID in ',
    'ready_for_pack' => 'The product is collected and ready for packaging',
    'history.label.pick_and_pack' => 'Change via the Pick and Pack app',
    'th.ordered' => ' ordered',
    'th.collected' => ' collected',
    'mokka.confirm.order' => 'CONFIRM ORDER IN',
    'klear.confirm.order' => 'CONFIRM PAYMENT IN',
    'button.confirm' => 'Confirm',
    'confirm_order.title_modal' => 'Confirm order in ',
    'order.error.manual' => 'The order could not be confirmed',
    'order.success.manual_confirm' => 'Order successfully confirmed',
    'label.document.number' => 'Enter document number',
    'order.error.document_number' => 'You have not entered a document number',
    'ship_to.locker' => 'Ship to locker',
    'filer.supplier' => 'Product Supplier',
    'filter.supplier' => 'Product Supplier: ',
    'title.cod' => 'Cash on delivery',
    "info.period" => "Reporting period: ",
    "info.period.used" => "Number of synchronizations performed for the period: ",
    "info.period.remaining" => "Number of synchronizations remaining for the period: ",
    "info.text" => "The system will check up to <b>15 consecutive days</b> for paid cash on delivery any order that has a valid waybill and it was <b>created more than 3 days ago from the check date< /b>. <br /><b>For example:</b> If you created a waybill today, the system will start checking for its payment status after 3 days.",
    "table.datetime" => 'Date and Time',
    "table.orderid" => 'Order',
    "table.action" => 'Status',
    "table.courier" => 'Courier',
    "notify.no_records_help_cod" => "You have no synchronized orders to check the payments imposed.",
    "action.sync" => 'Payment information is missing',
    "action.success_cod" => 'Cash on delivery successfully paid. Amount: ',
    'add_more_cod' => 'Buy additional package',
    'button.allow_recalculate' => 'Allow recalculation of shipping price',
    'button.disallow_recalculate' => 'Disallow recalculation of shipping price',
    'history.label.lock_order' => 'The shipping price of the order is locked',
    'history.label.unlock_order' => 'Order shipping price unlocked',
    'invoice_line' => 'Invoicing',
    'create_invoice' => 'Create Invoice',
    'label.invoice.number' => 'Enter invoice number',
    'err.invoice_number_required' => 'You have not entered an invoice number',
    'err.invoice_number_unique' => 'An invoice with the entered number already exists. Please enter a unique number',
    'err.invoice_number_numeric' => 'Invoice number must be a number',
    'success.generate_invoice' => 'Successfully generated invoice',
    'error.generate_invoice' => 'No invoice has been created in the selected external system',
    'send_abonded_order_error' => 'This feature is available for plan: ',
    'down payment' => 'Initial payment',
    'action.remove_order_modifications' => 'Remove the discount from "Cart rules"',
    'confirm.remove_order_modifications' => 'Are you sure you want to remove the discount from "Cart rules"?',
    'history.label.modification_removed_from_order_product' => '":app" discount removed for a product',
    'speedy.deprecate' => 'The Speedy app you are using will be available until March 31, 2025. To continue using Speedy services, you need to install and set up the DPD Bulgaria app <a href="/admin/shipping/dpdbulgaria/settings" target="_blank">Speedy v2</a>. Your login credentials remain the same.',
    'err.discount_lt_price' => 'The discount amount cannot be greater than the product price',
    'err.send_order' => 'The order was not sent successfully',
];