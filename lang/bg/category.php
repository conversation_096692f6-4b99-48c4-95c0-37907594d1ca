<?php

return [
    'action.add' => 'Добави категория',
    'action.import' => 'Импортирай',
    'action.set_properties' => 'Добави конкретни характеристики за категория',
    'confirm.image_delete' => 'Сигурни ли сте, че искате да изтриете изображението?',
    'created' => 'Категорията е създадена',
    'deleted' => 'Категорията е изтрита',
    'drag' => 'Хвани и влачи, за да преподредиш',
    'err.cannot_delete_category.used_in_segment' => 'Не можете да изтриете категория, която участва в сегмент',
    'err.cannot_delete_category_has_products' => 'Категория, съдържаща продукти, не може да бъде изтрита.',
    'err.cannot_delete_category_has_xml_import' => 'Категория, закачена към "XML импортиране на продукти" (:names), не може да бъде изтрита.',
    'err.cannot_have_more_than_%1$s_concurent_imports' => 'Не е възможно да имате повече от %1$s импорта на категории едновременно!',
    'err.choose' => 'Моля, избери категория.',
    'err.description_max_chars_%1$s' => 'Максималната дължина за описание е превишена(%1$s)',
    'err.max_depth_is_%1$s' => 'Максимумът за категории е %1$s.',
    'err.max_number_reached' => 'Достигнат е максималният брой категории. Обмислете обновяване на плана си.',
    'err.moved_and_target_cannot_be_the_same' => 'Преместената и общата категории не могат да бъдат еднакви!',
    'err.moved_no_longer_exists' => 'Преместената категория вече не съществува!',
    'err.name_max_chars_%1$s' => 'Максималната дължина за име е превишена(%1$s)',
    'err.name_requred' => 'Името на категория е задължително',
    'err.name_taken' => 'Името на категория вече се използва!',
    'err.no_longer_exists' => 'Категорията вече не съществува',
    'err.parent_cannot_be_itself' => 'Основната категория не може да бъде същата',
    'err.parent_no_longer_exists' => 'Основната категория вече не съществува',
    'err.required' => 'Категорията е задължителна!',
    'err.target_is_child_of_moved' => 'Общата категория е производна на преместената категория',
    'err.target_no_longer_exists' => 'Общата категория вече не съществува',
    'filter.has_no_subcategories' => 'Няма подкатегории',
    'filter.has_subcategories' => 'Има подкатегории',
    'header.add' => 'Добави нова категория',
    'header.categories' => 'Категории',
    'header.edit' => 'Редактирай категория',
    'header.properties_values_edit' => 'Редактиране стойностите на характеристиките',
    'help.color' => 'Това ще бъде цвета за иконата на категорията',
    'help.icon' => 'Изберете икона за категорията',
    'help.image' => 'Снимка на категорията се използва за показване във вашия магазин',
    'help.make_interval' => 'Задайте време което да се взима под внимание за приложението "Часови слот за доставка"',
    'help.name' => 'Например обувки; компютри; торти',
    'help.parent' => 'За да направите подкатегория, моля изберете основна категория към която да принадлежи.',
    'help.taxonomy.1' => 'Защо трябва да дефинирам категориите си? Като класифицирате категориите си с международния класификатор на категории, вашият продуктов каталог ще стане автоматично съвместим с всички услуги, ползващи този класификатор.',
    'help.taxonomy.2' => 'Ако искате да ползвате Facebook Dynamic Ads или Google Merchant, вашите категории ще трябва да са класифицирани според този стандарт.',
    'help.taxonomy.3' => 'Нещо повече, ако вашите продукти са в категории, които са класифицирани, алгоритмите на CloudCart ще  помогнат на клиентите ви да откриват по-подходящи продукти, спрямо техните нужди и желания.',
    'help.taxonomy.4' => 'Преди да е възможно добавяне на категории, моля изберете ниша, в която работи вашият бизнес от <a href=":link#site-industry" style="color: white;font-weight: bold;text-decoration: underline !important;">ТУК</a>',
    'help.taxonomy.for.example' => 'Например: ',
    'import.succ.started' => 'Импортирането на категории е успешно',
    'label.color' => 'Цвят',
    'label.define.taxonomy' => 'Дефиниране на категорията',
    'label.description' => 'Описание',
    'label.icon' => 'Икона',
    'label.image' => 'Снимка',
    'label.make_interval' => 'Технологично време за доставка в часове',
    'label.name' => 'Име',
    'label.parent' => 'Подкатегория',
    'label.taxonomy' => 'Дефиниция',
    'label.url_handle' => 'URL адрес',
    'notify.no_records_help' => 'Изпитвате затруднения относно продуктовите категории? Посетете линка отдолу.',
    'notify.no_records_help_link' => 'Помощ относно продуктови категории',
    'notify.no_records_info' => 'Категориите за продукти ще са видими тук',
    'notify.no_records_yet' => 'Все още нямате добавени категории за продукти',
    'ph.description' => 'Въведи описание на категория',
    'ph.make_interval' => 'Технологично време за доставка в часове',
    'ph.name' => 'Въведи име на категория',
    'ph.parent' => 'Избери основна категория',
    'ph.url_handle' => 'Въведи URL адрес',
    'properties.label.properties' => 'Характеристики',
    'properties.label.property_name' => 'Характеристика',
    'properties.label.property_values' => 'Стойности',
    'properties.show_in_storefront' => 'Показване на филтър за тази характеристика в магазина',
    'properties.tip.parameter_name' => 'Тук въведете името на характеристиката, което ще е видимо в магазина Ви. Ако например, магазинът Ви е за лаптопи: екран, процесор, памет',
    'properties.tip.parameter_values' => 'Тук въведете стойностите на характеристиката. Ако например, магазинът Ви е за лаптопи, за характеристика екран въвеждате 17.3-инчов (43.94 см.)',
    'properties.tip.show_in_storefront' => 'Показване на филтър за тази характеристика в магазина',
    'properties.update_error' => 'Възникна грешка при обновяване на списъка с характеристики',
    'properties.update_success' => 'Характеристиките са записани успешно',
    'select' => 'Изберете категория',
    'succ.add' => 'Категорията е добавена успешно!',
    'succ.edit' => 'Категорията е редактирана успешно!',
    'tip.seo_url_handle' => 'Това е URL адреса за достъп на записа, който правите в момента. Този адрес ще бъде достъпен за всички посетители на вашия сайт.',
    'updated' => 'Категорията е обновена',
    'label.display_child' => 'Показване на подкатегориите',
    'help.display_child' => 'При включена опция на страницата на категорията ще бъдат видими подкатегориите',
    'label.restriction_shipping' => 'Кои методи за доставка ще обслужват тази категория',
    'label.restriction_payment' => 'Кои методи за плащане ще обслужват тази категория',
    'err.shipping.required' => 'Метод за доставка е задължителен',
    'err.payment.required' => 'Метод за плащане е задължителен',
    'alert.restriction_shipping' => 'Внимание! Вече имате зададена рестрикция по метод на доставка към друга категория. Възможно е да се получи така, че клиент да има в количката си продукти от две категории с различни рестрикции. В този случай, клиента няма да може да завърши поръчката си.',
    'alert.restriction_payment' => 'Внимание! Вече имате зададена рестрикция по метод на плащане към друга категория. Възможно е да се получи така, че клиент да има в количката си продукти от две категории с различни рестрикции. В този случай, клиента няма да може да завърши поръчката си.',
    'alert.restriction_shipping_first' => 'Важно! Ако зададете рестрикция към тази категория по метод на доставка и ако клиент има в количката си поне един продукт от тази категория, системата ще му предостави като възможност за доставка само избраното.',
    'alert.restriction_payment_first' => 'Важно! Ако зададете рестрикция към тази категория по метод на плащане и ако клиент има в количката си поне един продукт от тази категория, системата ще му предостави като възможност за метод на плащане само избраното.',
    'topbar.title' => 'Разгледайте новата секция за Категории!',
    'topbar.description' => 'Обновихме дизайна, за да улесним и направим по-интуитивно управлението на вашите категории. За да разгледате новата секция, кликнете на бутона.',
    'topbar.button' => 'Към новия интерфейс',
    'topbar.confirm.title' => 'На път сте да преминете към новия интерфейс',
    'topbar.confirm.description' => 'Не се притеснявайте, можете да се върнете към стария интерфейс по всяко време, като използвате бутона в горната част на страницата.',
    'topbar.confirm.reject' => 'Откажи'
];
