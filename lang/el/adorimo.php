<?php

return [
    'academy.header.install' => 'Εγκαταστήστε τα μαθήματα βίντεο Adorimo',
    'academy.help.install' => 'Με αυτήν την εφαρμογή θα έχετε πρόσβαση σε διαφορετικά μαθήματα βίντεο',
    'academy.info.description' => 'Με το Adorimo θα μάθετε: <br>
- Π<PERSON>ς να δημιουργήσετε την πρώτη σας διαφήμιση στο facebook <br>
- Πώς να προσεγγίσετε το σωστό κοινό στο Facebook <br>
- <PERSON><PERSON><PERSON> να πουλήσετε επιτυχώς στο Facebook',
    'academy.info.install' => '- Πώς να δημιουργήσετε την πρώτη σας διαφήμιση στο facebook <br>
- <PERSON><PERSON><PERSON> να προσεγγίσετε το σωστό κοινό στο Facebook <br>
- Πώς να πουλήσετε επιτυχώς στο Facebook',
    'academy.info.title' => 'Μαθήματα βίντεο Adorimo',
    'academy.title.able_to' => 'Με το Adorimo θα μάθετε:',
    'action.back_to_admin_panel' => 'Επιστροφή στον πίνακα διαχείρισης',
    'action.payment.add_provider' => 'Προσθήκη τρόπου πληρωμής',
    'all_tasks_completed.head' => ', Κάνατε τέλεια όλα τα βήματα. Σας ευχόμαστε καλή τύχη στις διαδικτυακές πωλήσεις!',
    'choose_option' => 'Ορίστε τουλάχιστον μία επιλογή',
    'choose_service' => 'Επιλέξτε τουλάχιστον μία υπηρεσία',
    'header.install' => 'Algolia',
    'help.btn_install' => 'Εγκαθιστώ',
    'help.hire_expert_btn' => 'Προσλάβετε έναν ειδικό',
    'help.hire_expert_btn_design' => 'Σχέδιο',
    'help.hire_expert_text' => 'Εάν έχετε πρόβλημα, μπορείτε ανά πάσα στιγμή να επικοινωνήσετε με την ομάδα επαγγελματιών μας.',
    'help.hire_expert_text_design' => 'Δεν έχετε τις απαραίτητες δεξιότητες σχεδιασμού, αλλά το όραμα ενός διαδικτυακού καταστήματος είναι σημαντικό για εσάς.',
    'help.install' => 'Με αυτήν την εφαρμογή θα βελτιώσετε, βελτιστοποιήσετε και εξατομικεύσετε τα αποτελέσματα αναζήτησης στο CloudCart κατάστημα σας. Εάν θέλετε να δώσετε έναν μοναδικό τρόπο να απεικονίσετε άμεσα τα αποτελέσματα αναζήτησης και να δώσετε στους χρήστες σας μια εμπειρία ανακάλυψης αυτή η εφαρμογή είναι για εσάς.',
    'info.install' => ' ',
    'info.title' => 'Algolia',
    'products.distributors' => 'Το ηλεκτρονικό σας κατάστημα διαθέτει ενοποίηση και συγχρονισμό με τους ακόλουθους διανομείς:',
    'shipping.depending_on_price' => 'Ανάλογα με την <strong> τιμή τελικής παραγγελίας </strong> <br> Αυτή η επιλογή παράδοσης είναι βολική όταν θέλετε να εργαστείτε με έναν ταχυμεταφορέα(courier) που δεν ενσωματώνεται στο CloudCart ή για οποιονδήποτε άλλο λόγο. Αυτή η μέθοδος παράδοσης είναι μία από τις πιο συνήθεις.
Προσθέτοντας τους όρους τιμών Από και Μέχρι, μπορείτε να ελέγξετε την τιμή παράδοσης.
Για παράδειγμα: εάν η παραγγελία κυμαίνεται μεταξύ 0 και 100 $ - η τιμή παράδοσης είναι 5 $. Εάν είναι από $ 100 έως $ 1000, η τιμή της παράδοσης είναι 0 $.',
    'shipping.depending_on_price_and_weight' => 'Ανάλογα με την <strong> τελική τιμή και το βάρος όλων των προϊόντων </strong> στην παραγγελία <br> Αυτή η μέθοδος παράδοσης συνδυάζει τόσο τις παραμέτρους τιμής όσο και το βάρος. Χρησιμοποιείται σε μικρότερες περιπτώσεις κατά την πώληση προϊόντων χαμηλού όγκου και υψηλών τιμών και προϊόντων μεγάλου όγκου, αλλά χαμηλής τιμής. Για παράδειγμα: εάν η συνολική παραγγελία είναι από 0 BGN έως 100 BGN και το συνολικό βάρος είναι από 0 έως 5 kg. - η τιμή είναι 5 λέβα.',
    'shipping.depending_on_weight' => 'Ανάλογα με το <strong> συνολικό βάρος όλων των προϊόντων </strong> στην παραγγελία <br> Η μέθοδος παράδοσης κατά βάρος των προϊόντων στην παραγγελία είναι βολική κατά την πώληση ογκωδών και βαρέων προϊόντων. Εκεί η τιμή αναμένεται να ποικίλει πολύ. Είναι πολύ σημαντικό να θυμάστε ότι αυτή η μέθοδος παράδοσης θα λειτουργεί μόνο όταν τα προϊόντα σας έχουν επιπλέον βάρος.',
    'shipping.pick_from_store' => 'Συλλογή προϊόντος από <strong> φυσικά καταστήματα </strong> <br> Αυτή η επιλογή είναι χρήσιμη όταν έχετε τουλάχιστον μία φυσική τοποθεσία. Αυτή η ενότητα θα προσφέρει την ευκαιρία να παραλάβετε τα προϊόντα από τα καταστήματά σας. Είναι σημαντικό να γνωρίζετε ότι για να προσφέρετε αυτήν την επιλογή, πρέπει να χρησιμοποιήσετε την εφαρμογή Stores(Stores app).',
    'title.able_to' => ' ',
];
