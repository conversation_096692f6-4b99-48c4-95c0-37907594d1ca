# CloudCart Liquid Documentation

## Overview

This documentation covers the complete CloudCart Liquid template engine implementation, including all tags, filters, and drops available for theme development.

CloudCart Liquid is based on Shopify Liquid with CloudCart-specific enhancements and optimizations for e-commerce functionality.

## Documentation Structure

### Core Tags
- [Control Flow Tags](tags/control-flow-tags.md) - if, unless, case, etc.
- [Iteration Tags](tags/iteration-tags.md) - for, tablerow, cycle
- [Variable Tags](tags/variable-tags.md) - assign, capture, increment, decrement
- [Template Tags](tags/template-tags.md) - include, render, layout, extends
- [Theme Tags](tags/theme-tags.md) - section, schema, style, stylesheet
- [Form Tags](tags/form-tags.md) - form and form-related functionality
- [Utility Tags](tags/utility-tags.md) - comment, raw, liquid, echo
- [Advanced Tags](tags/advanced-tags.md) - paginate, javascript, content_for

### Custom CloudCart Tags
- [Authentication Tags](tags/auth-tags.md) - auth, guest
- [Navigation Tags](tags/navigation-tags.md) - route, breadcrumbs
- [Function Tags](tags/function-tags.md) - function, call
- [Integration Tags](tags/integration-tags.md) - gdpr, recaptcha, instagram
- [Asset Tags](tags/asset-tags.md) - script, style management
- [Search Tags](tags/search-tags.md) - search configuration and functionality

### Filters
- [String Filters](filters/string-filters.md) - Text manipulation and formatting
- [Array Filters](filters/array-filters.md) - Collection and array operations
- [Math Filters](filters/math-filters.md) - Mathematical operations
- [Date Filters](filters/date-filters.md) - Date and time formatting
- [URL Filters](filters/url-filters.md) - URL generation and manipulation
- [Asset Filters](filters/asset-filters.md) - Asset management and optimization
- [Color Filters](filters/color-filters.md) - Color manipulation
- [Escape Filters](filters/escape-filters.md) - Security and escaping
- [Helper Filters](filters/helper-filters.md) - Utility and convenience filters
- [Custom Filters](filters/custom-filters.md) - CloudCart-specific filters

### Drops (Data Objects)
- [Product Drops](drops/product-drops.md) - Product, Variant, Collection
- [Order Drops](drops/order-drops.md) - Order, OrderProduct, Payment
- [Customer Drops](drops/customer-drops.md) - Customer, Address
- [Cart Drops](drops/cart-drops.md) - Cart, CartItem
- [Content Drops](drops/content-drops.md) - Blog, Article, Page
- [Store Drops](drops/store-drops.md) - Shop, Settings, Currency
- [Navigation Drops](drops/navigation-drops.md) - Menu, Breadcrumbs
- [GDPR Drops](drops/gdpr-drops.md) - Policy, CookieGroup

## Quick Start

### Basic Template Structure
```liquid
<!DOCTYPE html>
<html>
<head>
    <title>{{ page_title | default: shop.name }}</title>
    {% stylesheet 'theme.css' %}
</head>
<body>
    {% section 'header' %}
    
    <main>
        {{ content_for_layout }}
    </main>
    
    {% section 'footer' %}
    {% javascript 'theme.js' %}
</body>
</html>
```

### Common Patterns

#### Product Loop
```liquid
{% for product in collection.products %}
    <div class="product-card">
        <img src="{{ product.featured_image | img_url: '300x300' }}" alt="{{ product.title }}">
        <h3>{{ product.title }}</h3>
        <p class="price">{{ product.price | money }}</p>
        <a href="{{ product.url }}">View Product</a>
    </div>
{% endfor %}
```

#### Conditional Content
```liquid
{% if customer %}
    <p>Welcome back, {{ customer.first_name }}!</p>
{% else %}
    <p><a href="{{ routes.account_login_url }}">Login</a> or <a href="{{ routes.account_register_url }}">Register</a></p>
{% endif %}
```

#### Form Handling
```liquid
{% form 'contact' %}
    {% if form.errors %}
        <div class="errors">
            {% for error in form.errors %}
                <p>{{ error }}</p>
            {% endfor %}
        </div>
    {% endif %}
    
    <input type="text" name="contact[name]" placeholder="Your Name" required>
    <input type="email" name="contact[email]" placeholder="Your Email" required>
    <textarea name="contact[message]" placeholder="Your Message" required></textarea>
    <button type="submit">Send Message</button>
{% endform %}
```

## Best Practices

### Performance
- Use `{% liquid %}` blocks for multiple operations
- Cache expensive operations with `{% assign %}`
- Limit loop iterations with `limit` parameter
- Use `{% break %}` and `{% continue %}` appropriately

### Security
- Always escape user input with `| escape`
- Use CSRF tokens in forms
- Validate input in custom tags
- Use `strict_variables` in development

### Maintainability
- Use meaningful variable names
- Comment complex logic
- Break large templates into partials
- Use consistent naming conventions

## CloudCart Enhancements

CloudCart Liquid includes several enhancements over standard Shopify Liquid:

### Advanced Features
- **Multi-site support** - Templates can be site-specific
- **Database templates** - Store templates in database
- **Advanced caching** - Multiple cache drivers and strategies
- **Enhanced error handling** - Production-ready error management
- **Custom drops** - CloudCart-specific data objects
- **Performance optimization** - Compiled template storage

### CloudCart-Specific Tags
- `{% auth %}` - Customer authentication logic
- `{% route %}` - URL generation with CloudCart routing
- `{% gdpr %}` - GDPR compliance integration
- `{% function %}` / `{% call %}` - Template functions
- `{% recaptcha %}` - Google ReCaptcha integration

### Enhanced Security
- Comprehensive input validation
- XSS protection with auto-escaping
- CSRF protection integration
- Secure file inclusion controls
- Protected variable system

## Migration from Shopify

CloudCart Liquid is highly compatible with Shopify Liquid. Most Shopify templates will work with minimal modifications:

### Compatible Features
- ✅ All core tags and filters
- ✅ Standard drops and objects
- ✅ Template syntax and structure
- ✅ Shopify-specific features like sections

### CloudCart Differences
- Enhanced error handling
- Additional CloudCart-specific drops
- Performance optimizations
- Multi-site capabilities
- Database template storage

## Support and Resources

- **GitHub Repository**: CloudCart Liquid source code
- **API Documentation**: Complete API reference
- **Examples**: Template examples and patterns
- **Community**: CloudCart developer community
- **Support**: Technical support and assistance

---

*This documentation is for CloudCart Liquid v2.0+. For older versions, please refer to the legacy documentation.*
