# CloudCart Liquid Documentation Configuration
# Compatible with mpociot/laravel-apidoc-generator

# Basic Information
title: "CloudCart Liquid Documentation"
description: "Complete documentation for CloudCart Liquid template engine including tags, filters, and drops."
base_url: "https://docs.cloudcart.com"
version: "2.0"

# Documentation Settings
output: "public/docs"
theme: "default"
logo: "assets/cloudcart-logo.png"

# Table of Contents Structure
toc:
  - title: "Getting Started"
    items:
      - title: "Introduction"
        file: "README.md"
      - title: "Quick Start"
        file: "quick-start.md"
      - title: "Migration Guide"
        file: "migration-guide.md"

  - title: "Core Tags"
    items:
      - title: "Control Flow Tags"
        file: "tags/control-flow-tags.md"
        description: "Conditional logic with if, unless, case statements"
      - title: "Iteration Tags"
        file: "tags/iteration-tags.md"
        description: "Loops and iteration with for, tablerow, cycle"
      - title: "Variable Tags"
        file: "tags/variable-tags.md"
        description: "Variable management with assign, capture, increment"
      - title: "Template Tags"
        file: "tags/template-tags.md"
        description: "Template structure with include, render, layout, extends"
      - title: "Theme Tags"
        file: "tags/theme-tags.md"
        description: "Theme functionality with section, schema, style"
      - title: "Form Tags"
        file: "tags/form-tags.md"
        description: "Form handling and validation"
      - title: "Utility Tags"
        file: "tags/utility-tags.md"
        description: "Comments, raw content, liquid blocks"
      - title: "Advanced Tags"
        file: "tags/advanced-tags.md"
        description: "Pagination, JavaScript, content management"

  - title: "CloudCart Tags"
    items:
      - title: "Authentication Tags"
        file: "tags/auth-tags.md"
        description: "Customer authentication with auth, guest"
      - title: "Navigation Tags"
        file: "tags/navigation-tags.md"
        description: "URL generation and routing"
      - title: "Function Tags"
        file: "tags/function-tags.md"
        description: "Reusable template functions"
      - title: "Integration Tags"
        file: "tags/integration-tags.md"
        description: "Third-party integrations"
      - title: "Asset Tags"
        file: "tags/asset-tags.md"
        description: "Asset management and optimization"
      - title: "Search Tags"
        file: "tags/search-tags.md"
        description: "Search functionality"

  - title: "Filters"
    items:
      - title: "String Filters"
        file: "filters/string-filters.md"
        description: "Text manipulation and formatting"
      - title: "Array Filters"
        file: "filters/array-filters.md"
        description: "Collection and array operations"
      - title: "Math Filters"
        file: "filters/math-filters.md"
        description: "Mathematical operations and calculations"
      - title: "Date Filters"
        file: "filters/date-filters.md"
        description: "Date and time formatting"
      - title: "URL Filters"
        file: "filters/url-filters.md"
        description: "URL generation and manipulation"
      - title: "Asset Filters"
        file: "filters/asset-filters.md"
        description: "Asset management and optimization"
      - title: "Color Filters"
        file: "filters/color-filters.md"
        description: "Color manipulation and conversion"
      - title: "Escape Filters"
        file: "filters/escape-filters.md"
        description: "Security and content escaping"
      - title: "Helper Filters"
        file: "filters/helper-filters.md"
        description: "Utility and convenience filters"
      - title: "Custom Filters"
        file: "filters/custom-filters.md"
        description: "CloudCart-specific filters"

  - title: "Data Objects (Drops)"
    items:
      - title: "Product Drops"
        file: "drops/product-drops.md"
        description: "Product, Variant, Collection objects"
      - title: "Order Drops"
        file: "drops/order-drops.md"
        description: "Order, OrderProduct, Payment objects"
      - title: "Customer Drops"
        file: "drops/customer-drops.md"
        description: "Customer and Address objects"
      - title: "Cart Drops"
        file: "drops/cart-drops.md"
        description: "Shopping cart and line items"
      - title: "Content Drops"
        file: "drops/content-drops.md"
        description: "Blog, Article, Page objects"
      - title: "Store Drops"
        file: "drops/store-drops.md"
        description: "Shop, Settings, Currency objects"
      - title: "Navigation Drops"
        file: "drops/navigation-drops.md"
        description: "Menu and navigation objects"
      - title: "GDPR Drops"
        file: "drops/gdpr-drops.md"
        description: "Privacy and compliance objects"

  - title: "Advanced Topics"
    items:
      - title: "Performance Optimization"
        file: "advanced/performance.md"
        description: "Optimization techniques and best practices"
      - title: "Security Guidelines"
        file: "advanced/security.md"
        description: "Security best practices and XSS prevention"
      - title: "Debugging"
        file: "advanced/debugging.md"
        description: "Debugging techniques and tools"
      - title: "Testing"
        file: "advanced/testing.md"
        description: "Testing Liquid templates"

  - title: "Examples"
    items:
      - title: "Common Patterns"
        file: "examples/common-patterns.md"
        description: "Frequently used template patterns"
      - title: "E-commerce Templates"
        file: "examples/ecommerce-templates.md"
        description: "Complete e-commerce template examples"
      - title: "Component Library"
        file: "examples/component-library.md"
        description: "Reusable component examples"

# Styling and Appearance
styles:
  primary_color: "#007bff"
  secondary_color: "#6c757d"
  success_color: "#28a745"
  warning_color: "#ffc107"
  danger_color: "#dc3545"
  font_family: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
  code_font: "'Fira Code', 'Monaco', 'Menlo', monospace"

# Code Highlighting
highlight:
  theme: "github"
  languages:
    - liquid
    - html
    - css
    - javascript
    - json
    - yaml

# Search Configuration
search:
  enabled: true
  placeholder: "Search documentation..."
  max_results: 10

# Navigation
navigation:
  show_toc: true
  show_breadcrumbs: true
  show_edit_link: true
  edit_base_url: "https://github.com/cloudcart/liquid-docs/edit/main/"

# Footer
footer:
  copyright: "© 2024 CloudCart. All rights reserved."
  links:
    - title: "CloudCart"
      url: "https://cloudcart.com"
    - title: "Support"
      url: "https://support.cloudcart.com"
    - title: "GitHub"
      url: "https://github.com/cloudcart/liquid"

# SEO and Meta
meta:
  keywords: "CloudCart, Liquid, template engine, e-commerce, Shopify, documentation"
  author: "CloudCart Development Team"
  robots: "index, follow"

# Social Media
social:
  twitter: "@cloudcart"
  facebook: "cloudcart"
  linkedin: "company/cloudcart"

# Analytics
analytics:
  google_analytics: "GA_TRACKING_ID"
  google_tag_manager: "GTM_CONTAINER_ID"

# Features
features:
  copy_code: true
  line_numbers: true
  collapsible_sections: true
  dark_mode: true
  print_friendly: true

# Custom CSS and JavaScript
custom:
  css: |
    .liquid-tag {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 2px 6px;
      font-family: var(--code-font);
      font-size: 0.875em;
    }
    
    .liquid-filter {
      background: #e3f2fd;
      border: 1px solid #bbdefb;
      border-radius: 4px;
      padding: 2px 6px;
      font-family: var(--code-font);
      font-size: 0.875em;
    }
    
    .example-box {
      background: #f8f9fa;
      border-left: 4px solid #007bff;
      padding: 1rem;
      margin: 1rem 0;
    }
    
    .warning-box {
      background: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 1rem;
      margin: 1rem 0;
    }
    
    .tip-box {
      background: #d1ecf1;
      border-left: 4px solid #17a2b8;
      padding: 1rem;
      margin: 1rem 0;
    }

  js: |
    // Custom JavaScript for enhanced functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Add copy buttons to code blocks
      const codeBlocks = document.querySelectorAll('pre code');
      codeBlocks.forEach(function(block) {
        const button = document.createElement('button');
        button.className = 'copy-button';
        button.textContent = 'Copy';
        button.addEventListener('click', function() {
          navigator.clipboard.writeText(block.textContent);
          button.textContent = 'Copied!';
          setTimeout(() => button.textContent = 'Copy', 2000);
        });
        block.parentNode.appendChild(button);
      });
      
      // Enhance liquid code highlighting
      const liquidBlocks = document.querySelectorAll('code.language-liquid');
      liquidBlocks.forEach(function(block) {
        block.innerHTML = block.innerHTML
          .replace(/(\{%[^%]*%\})/g, '<span class="liquid-tag">$1</span>')
          .replace(/(\|[^}]*)/g, '<span class="liquid-filter">$1</span>');
      });
    });

# Build Configuration
build:
  clean: true
  minify: true
  source_maps: false
  
# Development
development:
  live_reload: true
  port: 3000
  host: "localhost"
