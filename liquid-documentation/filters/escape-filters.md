# Escape Filters

Escape filters provide essential security functionality by sanitizing and encoding content to prevent XSS attacks and ensure safe output in CloudCart templates.

## HTML Escaping

### escape
Escapes HTML characters to prevent XSS attacks.

```liquid
{{ '<script>alert("xss")</script>' | escape }}
<!-- Output: &lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt; -->

{{ customer.name | escape }}
<!-- Safe output of user-provided data -->

{{ product.description | escape }}
<!-- Escaped product description -->
```

### escape_once
Escapes HTML characters but doesn't double-escape already escaped content.

```liquid
{{ '&lt;p&gt;Hello &amp; goodbye&lt;/p&gt;' | escape_once }}
<!-- Output: &lt;p&gt;Hello &amp; goodbye&lt;/p&gt; -->

{{ 'Hello & goodbye' | escape_once }}
<!-- Output: Hello &amp; goodbye -->
```

### User-Generated Content Safety
```liquid
{% comment %} Safe display of customer reviews {% endcomment %}
<div class="customer-review">
  <h4>{{ review.title | escape }}</h4>
  <div class="review-author">
    By {{ review.author | escape }}
  </div>
  <div class="review-content">
    {{ review.content | escape | newline_to_br }}
  </div>
</div>

{% comment %} Safe contact form display {% endcomment %}
{% if form.posted_successfully? %}
  <div class="success-message">
    <p>Thank you, {{ form.name | escape }}! Your message has been sent.</p>
  </div>
{% endif %}
```

## URL Encoding

### url_encode
Encodes strings for safe use in URLs.

```liquid
{{ 'hello world' | url_encode }}
<!-- Output: hello%20world -->

{{ search.terms | url_encode }}
<!-- Safe search parameter -->

{{ 'special chars: !@#$%' | url_encode }}
<!-- Output: special%20chars%3A%20%21%40%23%24%25 -->
```

### url_decode
Decodes URL-encoded strings.

```liquid
{{ 'hello%20world' | url_decode }}
<!-- Output: hello world -->

{{ request.params.q | url_decode }}
<!-- Decode search query parameter -->
```

### Safe URL Generation
```liquid
{% comment %} Safe search URL generation {% endcomment %}
<form action="/search" method="get">
  <input type="text" name="q" value="{{ search.terms | escape }}">
  <button type="submit">Search</button>
</form>

{% comment %} Safe collection filtering URLs {% endcomment %}
<div class="filter-links">
  {% for vendor in collection.all_vendors %}
    <a href="{{ collection.url }}?vendor={{ vendor | url_encode }}">
      {{ vendor | escape }}
    </a>
  {% endfor %}
</div>

{% comment %} Safe social sharing URLs {% endcomment %}
{% assign share_url = shop.url | append: product.url | url_encode %}
{% assign share_text = 'Check out this product: ' | append: product.title | url_encode %}

<div class="social-share">
  <a href="https://twitter.com/intent/tweet?url={{ share_url }}&text={{ share_text }}" 
     target="_blank">
    Share on Twitter
  </a>
  <a href="https://www.facebook.com/sharer/sharer.php?u={{ share_url }}" 
     target="_blank">
    Share on Facebook
  </a>
</div>
```

## JavaScript Escaping

### script_tag
Creates safe script tags with proper escaping.

```liquid
{% comment %} Safe JavaScript variable injection {% endcomment %}
<script>
  window.productData = {
    id: {{ product.id | json }},
    title: {{ product.title | json }},
    price: {{ product.price | json }},
    available: {{ product.available | json }}
  };
  
  window.customerData = {
    {% if customer %}
      id: {{ customer.id | json }},
      email: {{ customer.email | json }},
      firstName: {{ customer.first_name | json }}
    {% else %}
      id: null,
      email: null,
      firstName: null
    {% endif %}
  };
</script>
```

### JSON Escaping
```liquid
{% comment %} Safe JSON data for JavaScript {% endcomment %}
<script>
  // Product variants data
  window.productVariants = {{ product.variants | json }};
  
  // Collection products
  window.collectionProducts = {{ collection.products | map: 'id' | json }};
  
  // Cart data
  window.cartData = {
    itemCount: {{ cart.item_count | json }},
    totalPrice: {{ cart.total_price | json }},
    items: {{ cart.items | json }}
  };
  
  // Settings data
  window.themeSettings = {
    currency: {{ shop.currency | json }},
    moneyFormat: {{ shop.money_format | json }},
    locale: {{ shop.locale | json }}
  };
</script>
```

## CSS Escaping

### Safe CSS Values
```liquid
{% comment %} Safe CSS custom properties {% endcomment %}
<style>
  :root {
    --primary-color: {{ settings.primary_color | escape }};
    --secondary-color: {{ settings.secondary_color | escape }};
    --font-family: {{ settings.font_family | escape }};
    --border-radius: {{ settings.border_radius | escape }}px;
  }
  
  .hero-section {
    background-color: var(--primary-color);
    font-family: var(--font-family);
  }
</style>

{% comment %} Safe inline styles {% endcomment %}
<div class="custom-banner" 
     style="background-color: {{ section.settings.bg_color | escape }}; 
            color: {{ section.settings.text_color | escape }};">
  {{ section.settings.banner_text | escape }}
</div>
```

## Attribute Escaping

### Safe HTML Attributes
```liquid
{% comment %} Safe data attributes {% endcomment %}
<div class="product-card" 
     data-product-id="{{ product.id }}"
     data-product-title="{{ product.title | escape }}"
     data-product-handle="{{ product.handle | escape }}"
     data-product-price="{{ product.price }}">
  
  <img src="{{ product.featured_image | img_url: '300x300' }}" 
       alt="{{ product.title | escape }}"
       title="{{ product.title | escape }}">
  
  <h3>{{ product.title | escape }}</h3>
</div>

{% comment %} Safe form attributes {% endcomment %}
<form action="/contact" method="post">
  <input type="text" 
         name="contact[name]" 
         value="{{ form.name | escape }}"
         placeholder="{{ 'contact.form.name' | t | escape }}">
  
  <input type="email" 
         name="contact[email]" 
         value="{{ form.email | escape }}"
         placeholder="{{ 'contact.form.email' | t | escape }}">
  
  <textarea name="contact[message]" 
            placeholder="{{ 'contact.form.message' | t | escape }}">{{ form.message | escape }}</textarea>
</form>
```

## Advanced Security Patterns

### Content Security Policy (CSP)
```liquid
{% comment %} CSP-compliant script injection {% endcomment %}
<script nonce="{{ csp_nonce }}">
  // Safe inline script with nonce
  window.shopData = {
    name: {{ shop.name | json }},
    currency: {{ shop.currency | json }},
    domain: {{ shop.domain | json }}
  };
</script>

{% comment %} External script with integrity check {% endcomment %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p"
        crossorigin="anonymous"></script>
```

### Input Sanitization
```liquid
{% comment %} Multi-layer sanitization for user content {% endcomment %}
{% assign safe_content = user_content | strip_html | escape | truncate: 500 %}

<div class="user-generated-content">
  {{ safe_content | newline_to_br }}
</div>

{% comment %} Safe search term handling {% endcomment %}
{% assign safe_search_terms = search.terms | strip | escape | truncate: 100 %}

<div class="search-results">
  <h2>Search results for "{{ safe_search_terms }}"</h2>
  <!-- search results -->
</div>
```

### XSS Prevention Patterns
```liquid
{% comment %} Safe dynamic content rendering {% endcomment %}
<div class="dynamic-content">
  {% case content_type %}
    {% when 'html' %}
      {% comment %} Only allow HTML from trusted sources {% endcomment %}
      {% if content.trusted %}
        {{ content.html }}
      {% else %}
        {{ content.html | strip_html | escape }}
      {% endif %}
      
    {% when 'text' %}
      {{ content.text | escape | newline_to_br }}
      
    {% when 'markdown' %}
      {{ content.markdown | markdownify | strip_html | escape }}
      
    {% else %}
      {{ content | escape }}
  {% endcase %}
</div>

{% comment %} Safe template variable injection {% endcomment %}
<script>
  // Never inject raw user data into JavaScript
  const userData = {
    name: {{ customer.name | default: '' | json }},
    email: {{ customer.email | default: '' | json }}
  };
  
  // Safe DOM manipulation
  document.getElementById('user-name').textContent = userData.name;
  document.getElementById('user-email').textContent = userData.email;
</script>
```

## Error Handling and Validation

### Safe Error Display
```liquid
{% comment %} Safe error message display {% endcomment %}
{% if form.errors %}
  <div class="error-messages">
    <h4>Please correct the following errors:</h4>
    <ul>
      {% for field in form.errors %}
        <li>
          <strong>{{ field | capitalize | escape }}:</strong>
          {% for error in form.errors[field] %}
            {{ error | escape }}{% unless forloop.last %}, {% endunless %}
          {% endfor %}
        </li>
      {% endfor %}
    </ul>
  </div>
{% endif %}

{% comment %} Safe success message display {% endcomment %}
{% if form.posted_successfully? %}
  <div class="success-message">
    <p>{{ 'contact.form.success' | t: name: form.name | escape }}</p>
  </div>
{% endif %}
```

### Input Validation
```liquid
{% comment %} Client-side validation with safe attributes {% endcomment %}
<form class="contact-form" novalidate>
  <div class="form-group">
    <label for="contact_name">{{ 'contact.form.name' | t | escape }}</label>
    <input type="text" 
           id="contact_name" 
           name="contact[name]" 
           value="{{ form.name | escape }}"
           pattern="[A-Za-z\s]{2,50}"
           title="{{ 'contact.form.name_requirements' | t | escape }}"
           required>
    <div class="invalid-feedback">
      {{ 'contact.form.name_invalid' | t | escape }}
    </div>
  </div>
  
  <div class="form-group">
    <label for="contact_email">{{ 'contact.form.email' | t | escape }}</label>
    <input type="email" 
           id="contact_email" 
           name="contact[email]" 
           value="{{ form.email | escape }}"
           pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
           title="{{ 'contact.form.email_requirements' | t | escape }}"
           required>
    <div class="invalid-feedback">
      {{ 'contact.form.email_invalid' | t | escape }}
    </div>
  </div>
</form>
```

## Performance Considerations

### Efficient Escaping
```liquid
{% comment %} Cache escaped values for repeated use {% endcomment %}
{% assign escaped_product_title = product.title | escape %}
{% assign escaped_product_description = product.description | escape %}

<div class="product-info">
  <h1>{{ escaped_product_title }}</h1>
  <meta property="og:title" content="{{ escaped_product_title }}">
  <meta name="twitter:title" content="{{ escaped_product_title }}">
  
  <div class="description">{{ escaped_product_description }}</div>
  <meta name="description" content="{{ escaped_product_description | truncate: 160 }}">
</div>

{% comment %} Batch escaping for collections {% endcomment %}
{% assign escaped_products = collection.products | map: 'title' | map: 'escape' %}
```

## Best Practices

### Security Guidelines
1. **Always escape user input**: Never trust user-provided data
2. **Use appropriate escaping**: Choose the right filter for the context (HTML, URL, JavaScript)
3. **Validate on both sides**: Client-side validation for UX, server-side for security
4. **Implement CSP**: Use Content Security Policy to prevent XSS attacks

### Performance Tips
1. **Cache escaped values**: Store frequently used escaped content in variables
2. **Escape at output**: Only escape when outputting to prevent double-escaping
3. **Use escape_once**: When content might already be escaped
4. **Minimize escaping operations**: Combine with other filters efficiently

### Accessibility
1. **Provide meaningful alt text**: Escape but don't remove important information
2. **Maintain semantic structure**: Escaping shouldn't break HTML semantics
3. **Preserve language attributes**: Ensure language information survives escaping
4. **Keep error messages clear**: Escaped error messages should remain understandable

### Development Workflow
1. **Test with malicious input**: Always test with potential XSS payloads
2. **Use automated scanning**: Implement security scanning in CI/CD
3. **Regular security audits**: Periodically review escaping implementations
4. **Document security decisions**: Comment why specific escaping methods are used
