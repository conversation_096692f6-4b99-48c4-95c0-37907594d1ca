# Math Filters

Math filters perform mathematical operations on numbers, providing essential functionality for calculations in e-commerce templates.

## Basic Arithmetic

### plus
Adds a number to another number.

```liquid
{{ 10 | plus: 5 }}
<!-- Output: 15 -->

{{ product.price | plus: 500 }}
<!-- Add $5.00 to product price -->

{{ cart.total_price | plus: shipping_cost }}
<!-- Calculate total with shipping -->
```

### minus
Subtracts a number from another number.

```liquid
{{ 10 | minus: 3 }}
<!-- Output: 7 -->

{{ product.compare_at_price | minus: product.price }}
<!-- Calculate discount amount -->

{{ cart.total_price | minus: discount_amount }}
<!-- Apply discount to total -->
```

### times
Multiplies a number by another number.

```liquid
{{ 5 | times: 3 }}
<!-- Output: 15 -->

{{ product.price | times: 0.8 }}
<!-- Calculate 20% discount price -->

{{ line_item.quantity | times: line_item.price }}
<!-- Calculate line total -->
```

### divided_by
Divides a number by another number.

```liquid
{{ 15 | divided_by: 3 }}
<!-- Output: 5 -->

{{ cart.total_price | divided_by: cart.item_count }}
<!-- Calculate average item price -->

{{ product.price | divided_by: 100 }}
<!-- Convert cents to dollars -->
```

### modulo
Returns the remainder of a division operation.

```liquid
{{ 17 | modulo: 5 }}
<!-- Output: 2 -->

{{ forloop.index | modulo: 2 }}
<!-- Check if iteration is even (0) or odd (1) -->

{{ product.id | modulo: 3 }}
<!-- Distribute products into 3 groups -->
```

## Rounding and Precision

### round
Rounds a number to the nearest integer or specified decimal places.

```liquid
{{ 4.6 | round }}
<!-- Output: 5 -->

{{ 4.4 | round }}
<!-- Output: 4 -->

{{ 3.14159 | round: 2 }}
<!-- Output: 3.14 -->

{{ product.price | divided_by: 100 | round: 2 }}
<!-- Convert cents to dollars with 2 decimal places -->
```

### floor
Rounds a number down to the nearest integer.

```liquid
{{ 4.9 | floor }}
<!-- Output: 4 -->

{{ 4.1 | floor }}
<!-- Output: 4 -->

{{ product.price | divided_by: 1000 | floor }}
<!-- Get price in thousands (rounded down) -->
```

### ceil
Rounds a number up to the nearest integer.

```liquid
{{ 4.1 | ceil }}
<!-- Output: 5 -->

{{ 4.9 | ceil }}
<!-- Output: 5 -->

{{ cart.total_weight | divided_by: 1000 | ceil }}
<!-- Calculate shipping weight (rounded up) -->
```

### abs
Returns the absolute value of a number.

```liquid
{{ -5 | abs }}
<!-- Output: 5 -->

{{ 5 | abs }}
<!-- Output: 5 -->

{{ temperature_difference | abs }}
<!-- Get absolute temperature difference -->
```

## Practical E-commerce Examples

### Price Calculations
```liquid
{% comment %} Calculate discount percentage {% endcomment %}
{% assign original_price = product.compare_at_price | default: product.price %}
{% assign discount_amount = original_price | minus: product.price %}
{% assign discount_percentage = discount_amount | times: 100 | divided_by: original_price | round %}

{% if discount_percentage > 0 %}
  <span class="discount-badge">{{ discount_percentage }}% OFF</span>
{% endif %}
```

### Tax Calculations
```liquid
{% comment %} Calculate tax amount {% endcomment %}
{% assign tax_rate = 0.08 %}
{% assign subtotal = cart.total_price %}
{% assign tax_amount = subtotal | times: tax_rate | round: 2 %}
{% assign total_with_tax = subtotal | plus: tax_amount %}

<div class="price-breakdown">
  <div class="subtotal">Subtotal: {{ subtotal | money }}</div>
  <div class="tax">Tax: {{ tax_amount | money }}</div>
  <div class="total">Total: {{ total_with_tax | money }}</div>
</div>
```

### Shipping Calculations
```liquid
{% comment %} Calculate shipping cost based on weight {% endcomment %}
{% assign total_weight = cart.total_weight %}
{% assign weight_in_kg = total_weight | divided_by: 1000 | ceil %}
{% assign base_shipping = 500 %}
{% assign additional_shipping = weight_in_kg | minus: 1 | times: 200 %}
{% assign shipping_cost = base_shipping | plus: additional_shipping %}

<div class="shipping-info">
  <p>Weight: {{ weight_in_kg }}kg</p>
  <p>Shipping: {{ shipping_cost | money }}</p>
</div>
```

### Quantity Discounts
```liquid
{% comment %} Apply quantity-based discounts {% endcomment %}
{% assign quantity = line_item.quantity %}
{% assign unit_price = line_item.price %}

{% if quantity >= 10 %}
  {% assign discount_rate = 0.15 %}
{% elsif quantity >= 5 %}
  {% assign discount_rate = 0.10 %}
{% elsif quantity >= 3 %}
  {% assign discount_rate = 0.05 %}
{% else %}
  {% assign discount_rate = 0 %}
{% endif %}

{% assign discounted_price = unit_price | times: 1 | minus: discount_rate %}
{% assign line_total = discounted_price | times: quantity %}
{% assign savings = unit_price | times: quantity | minus: line_total %}

<div class="quantity-pricing">
  {% if discount_rate > 0 %}
    <p class="original-price">{{ unit_price | times: quantity | money }}</p>
    <p class="discounted-price">{{ line_total | money }}</p>
    <p class="savings">You save: {{ savings | money }}</p>
  {% else %}
    <p class="price">{{ line_total | money }}</p>
  {% endif %}
</div>
```

### Installment Calculations
```liquid
{% comment %} Calculate monthly installments {% endcomment %}
{% assign product_price = product.price %}
{% assign installment_months = 12 %}
{% assign monthly_payment = product_price | divided_by: installment_months | round: 2 %}

<div class="installment-option">
  <p>Or pay {{ monthly_payment | money }} per month for {{ installment_months }} months</p>
  <small>0% interest</small>
</div>
```

### Inventory Calculations
```liquid
{% comment %} Calculate stock levels and alerts {% endcomment %}
{% assign current_stock = variant.inventory_quantity %}
{% assign reserved_stock = variant.inventory_reserved %}
{% assign available_stock = current_stock | minus: reserved_stock %}
{% assign stock_percentage = available_stock | times: 100 | divided_by: variant.inventory_policy %}

<div class="stock-info">
  {% if available_stock <= 0 %}
    <span class="out-of-stock">Out of Stock</span>
  {% elsif available_stock <= 5 %}
    <span class="low-stock">Only {{ available_stock }} left!</span>
  {% elsif stock_percentage <= 25 %}
    <span class="limited-stock">Limited stock available</span>
  {% else %}
    <span class="in-stock">In Stock</span>
  {% endif %}
</div>
```

### Rating Calculations
```liquid
{% comment %} Calculate average rating {% endcomment %}
{% assign total_rating = 0 %}
{% assign review_count = product.reviews.size %}

{% for review in product.reviews %}
  {% assign total_rating = total_rating | plus: review.rating %}
{% endfor %}

{% if review_count > 0 %}
  {% assign average_rating = total_rating | divided_by: review_count | round: 1 %}
  
  <div class="product-rating">
    <div class="stars">
      {% assign full_stars = average_rating | floor %}
      {% assign has_half_star = average_rating | modulo: 1 | times: 10 | round %}
      
      {% for i in (1..full_stars) %}
        <span class="star full">★</span>
      {% endfor %}
      
      {% if has_half_star >= 5 %}
        <span class="star half">☆</span>
      {% endif %}
    </div>
    <span class="rating-text">{{ average_rating }} ({{ review_count }} reviews)</span>
  </div>
{% endif %}
```

### Pagination Calculations
```liquid
{% comment %} Calculate pagination info {% endcomment %}
{% assign items_per_page = 12 %}
{% assign total_items = collection.products_count %}
{% assign total_pages = total_items | divided_by: items_per_page | ceil %}
{% assign current_page = request.page | default: 1 %}
{% assign start_item = current_page | minus: 1 | times: items_per_page | plus: 1 %}
{% assign end_item = start_item | plus: items_per_page | minus: 1 %}

{% if end_item > total_items %}
  {% assign end_item = total_items %}
{% endif %}

<div class="pagination-info">
  <p>Showing {{ start_item }}-{{ end_item }} of {{ total_items }} products</p>
  <p>Page {{ current_page }} of {{ total_pages }}</p>
</div>
```

### Currency Conversion
```liquid
{% comment %} Convert currency (example with exchange rate) {% endcomment %}
{% assign usd_price = product.price %}
{% assign eur_rate = 0.85 %}
{% assign gbp_rate = 0.73 %}
{% assign jpy_rate = 110 %}

<div class="currency-options">
  <div class="currency">
    <span class="code">USD</span>
    <span class="price">{{ usd_price | money }}</span>
  </div>
  <div class="currency">
    <span class="code">EUR</span>
    <span class="price">€{{ usd_price | times: eur_rate | round: 2 }}</span>
  </div>
  <div class="currency">
    <span class="code">GBP</span>
    <span class="price">£{{ usd_price | times: gbp_rate | round: 2 }}</span>
  </div>
  <div class="currency">
    <span class="code">JPY</span>
    <span class="price">¥{{ usd_price | times: jpy_rate | round }}</span>
  </div>
</div>
```

### Progress Indicators
```liquid
{% comment %} Calculate progress towards free shipping {% endcomment %}
{% assign free_shipping_threshold = 5000 %}
{% assign current_total = cart.total_price %}
{% assign remaining_amount = free_shipping_threshold | minus: current_total %}
{% assign progress_percentage = current_total | times: 100 | divided_by: free_shipping_threshold %}

{% if progress_percentage > 100 %}
  {% assign progress_percentage = 100 %}
{% endif %}

<div class="free-shipping-progress">
  {% if remaining_amount > 0 %}
    <p>Add {{ remaining_amount | money }} more for free shipping!</p>
  {% else %}
    <p>🎉 You qualify for free shipping!</p>
  {% endif %}
  
  <div class="progress-bar">
    <div class="progress-fill" style="width: {{ progress_percentage }}%"></div>
  </div>
  <span class="progress-text">{{ progress_percentage }}%</span>
</div>
```

### Bulk Pricing Calculator
```liquid
{% comment %} Calculate bulk pricing tiers {% endcomment %}
{% assign base_price = product.price %}

<div class="bulk-pricing">
  <h4>Bulk Pricing</h4>
  <table>
    <tr>
      <th>Quantity</th>
      <th>Price Each</th>
      <th>Total</th>
      <th>Savings</th>
    </tr>
    {% for tier in (1..4) %}
      {% case tier %}
        {% when 1 %}
          {% assign qty = 1 %}
          {% assign discount = 0 %}
        {% when 2 %}
          {% assign qty = 5 %}
          {% assign discount = 0.05 %}
        {% when 3 %}
          {% assign qty = 10 %}
          {% assign discount = 0.10 %}
        {% when 4 %}
          {% assign qty = 25 %}
          {% assign discount = 0.15 %}
      {% endcase %}
      
      {% assign tier_price = base_price | times: 1 | minus: discount %}
      {% assign tier_total = tier_price | times: qty %}
      {% assign tier_savings = base_price | times: qty | minus: tier_total %}
      
      <tr>
        <td>{{ qty }}+</td>
        <td>{{ tier_price | money }}</td>
        <td>{{ tier_total | money }}</td>
        <td>
          {% if tier_savings > 0 %}
            {{ tier_savings | money }}
          {% else %}
            -
          {% endif %}
        </td>
      </tr>
    {% endfor %}
  </table>
</div>
```

## Performance Tips

1. **Cache calculations**: Store complex calculations in variables
2. **Use appropriate precision**: Don't over-round financial calculations
3. **Validate inputs**: Check for zero division and null values
4. **Combine operations**: Chain math filters efficiently

## Common Patterns

### Safe Division
```liquid
{% if denominator != 0 %}
  {% assign result = numerator | divided_by: denominator %}
{% else %}
  {% assign result = 0 %}
{% endif %}
```

### Percentage Calculation
```liquid
{% assign percentage = part | times: 100 | divided_by: whole | round: 1 %}
```

### Price Range
```liquid
{% assign min_price = collection.products | map: 'price' | sort | first %}
{% assign max_price = collection.products | map: 'price' | sort | last %}
```

### Average Calculation
```liquid
{% assign total = 0 %}
{% for item in items %}
  {% assign total = total | plus: item.value %}
{% endfor %}
{% assign average = total | divided_by: items.size | round: 2 %}
```
