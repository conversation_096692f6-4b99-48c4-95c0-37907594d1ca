# Helper Filters

Helper filters provide utility functions and convenience methods for common template operations in CloudCart Liquid.

## Type Checking and Conversion

### default
Provides a fallback value when a variable is nil, false, or empty.

```liquid
{{ product.title | default: 'Untitled Product' }}
<!-- Output: Product title or 'Untitled Product' if empty -->

{{ customer.first_name | default: 'Guest' }}
<!-- Output: Customer name or 'Guest' if not logged in -->

{{ settings.items_per_page | default: 12 }}
<!-- Output: Setting value or 12 if not set -->
```

### default_errors
Provides default error messages for forms.

```liquid
{% if form.errors %}
  <div class="error-messages">
    {{ form.errors | default_errors }}
  </div>
{% endif %}
```

### default_pagination
Generates default pagination HTML.

```liquid
{% paginate collection.products by 12 %}
  <!-- Product grid -->
  
  {% if paginate.pages > 1 %}
    {{ paginate | default_pagination }}
  {% endif %}
{% endpaginate %}
```

## Formatting and Display

### money
Formats currency values according to shop settings.

```liquid
{{ product.price | money }}
<!-- Output: $29.99 -->

{{ cart.total_price | money }}
<!-- Output: $149.95 -->

{{ order.total_price | money_with_currency }}
<!-- Output: $149.95 USD -->
```

### money_without_currency
Formats money without currency symbol.

```liquid
{{ product.price | money_without_currency }}
<!-- Output: 29.99 -->

{{ 'Price: ' | append: product.price | money_without_currency }}
<!-- Output: Price: 29.99 -->
```

### money_without_trailing_zeros
Removes unnecessary decimal places.

```liquid
{{ 2500 | money_without_trailing_zeros }}
<!-- Output: $25 -->

{{ 2550 | money_without_trailing_zeros }}
<!-- Output: $25.50 -->
```

### weight_with_unit
Formats weight values with appropriate units.

```liquid
{{ product.weight | weight_with_unit }}
<!-- Output: 1.5 kg -->

{{ cart.total_weight | weight_with_unit }}
<!-- Output: 3.2 kg -->
```

## Collection and Array Helpers

### at_least
Ensures a number is at least a minimum value.

```liquid
{{ cart.item_count | at_least: 1 }}
<!-- Ensures at least 1 for calculations -->

{{ product.inventory_quantity | at_least: 0 }}
<!-- Prevents negative inventory display -->
```

### at_most
Ensures a number doesn't exceed a maximum value.

```liquid
{{ product.inventory_quantity | at_most: 99 }}
<!-- Caps display at 99+ -->

{{ review.rating | at_most: 5 }}
<!-- Ensures rating doesn't exceed 5 stars -->
```

### within
Limits a collection to items within a parent collection.

```liquid
{% assign featured_products = collection.products | within: collections.featured %}
<!-- Only products that are also in featured collection -->

{% assign sale_articles = blog.articles | within: collections.sale %}
<!-- Articles related to sale items -->
```

## Text and Content Helpers

### highlight
Highlights search terms in text.

```liquid
{{ product.title | highlight: search.terms }}
<!-- Wraps search terms in <strong> tags -->

{{ article.content | highlight: search.terms, '<mark>', '</mark>' }}
<!-- Custom highlight tags -->
```

### highlight_active_tag
Highlights the currently active tag.

```liquid
{% for tag in collection.all_tags %}
  <a href="{{ collection.url }}/{{ tag | handle }}" 
     class="tag-link {{ tag | highlight_active_tag }}">
    {{ tag }}
  </a>
{% endfor %}
```

### link_to
Creates HTML links with proper attributes.

```liquid
{{ 'Visit our store' | link_to: shop.url }}
<!-- Output: <a href="https://shop.com">Visit our store</a> -->

{{ product.title | link_to: product.url, class: 'product-link' }}
<!-- Output: <a href="/products/awesome-shirt" class="product-link">Awesome Shirt</a> -->
```

### link_to_tag
Creates links for collection tags.

```liquid
{{ tag | link_to_tag }}
<!-- Creates a link to filter collection by tag -->

{{ tag | link_to_tag: collection }}
<!-- Links to specific collection with tag filter -->
```

### link_to_type
Creates links for product types.

```liquid
{{ product.type | link_to_type }}
<!-- Creates a link to filter by product type -->

{{ 'T-Shirts' | link_to_type: collection }}
<!-- Links to collection filtered by type -->
```

### link_to_vendor
Creates links for product vendors.

```liquid
{{ product.vendor | link_to_vendor }}
<!-- Creates a link to filter by vendor -->

{{ 'Nike' | link_to_vendor: collection }}
<!-- Links to collection filtered by vendor -->
```

## Image and Media Helpers

### img_tag
Creates HTML img tags with proper attributes.

```liquid
{{ product.featured_image | img_url: '300x300' | img_tag }}
<!-- Output: <img src="/images/product_300x300.jpg" alt=""> -->

{{ product.featured_image | img_url: '300x300' | img_tag: product.title }}
<!-- Output: <img src="/images/product_300x300.jpg" alt="Product Title"> -->

{{ 'logo.png' | asset_url | img_tag: shop.name, class: 'logo' }}
<!-- Output: <img src="/assets/logo.png" alt="Shop Name" class="logo"> -->
```

### placeholder_svg_tag
Generates SVG placeholder images.

```liquid
{{ 'product-1' | placeholder_svg_tag }}
<!-- Generates a product placeholder SVG -->

{{ 'collection-1' | placeholder_svg_tag: 'Collection placeholder' }}
<!-- Collection placeholder with custom alt text -->

{{ 'image' | placeholder_svg_tag: 'Image placeholder', class: 'placeholder-img' }}
<!-- Custom placeholder with CSS class -->
```

## Form and Input Helpers

### option_selection_for
Creates option selection JavaScript for product variants.

```liquid
{{ product | option_selection_for }}
<!-- Generates JavaScript for variant selection -->
```

### payment_button
Creates payment buttons for express checkout.

```liquid
{{ form | payment_button }}
<!-- Generates payment buttons (Apple Pay, Google Pay, etc.) -->
```

### payment_terms
Displays payment terms and installment options.

```liquid
{{ product | payment_terms }}
<!-- Shows available payment terms for the product -->
```

## Utility and Convenience

### json
Converts objects to JSON format.

```liquid
<script>
  window.productData = {{ product | json }};
  window.cartData = {{ cart | json }};
  window.customerData = {{ customer | json }};
</script>
```

### t (translate)
Translates text using locale files.

```liquid
{{ 'general.search.placeholder' | t }}
<!-- Output: Translated placeholder text -->

{{ 'cart.general.item_count' | t: count: cart.item_count }}
<!-- Output: Translated text with variable substitution -->

{{ 'products.product.price' | t: price: product.price | money }}
<!-- Complex translation with filters -->
```

### time_tag
Creates HTML time elements with proper datetime attributes.

```liquid
{{ article.published_at | time_tag }}
<!-- Output: <time datetime="2024-01-15T10:30:00Z">January 15, 2024</time> -->

{{ article.published_at | time_tag: '%B %d, %Y' }}
<!-- Custom date format -->

{{ order.created_at | time_tag: '%B %d, %Y', class: 'order-date' }}
<!-- With CSS class -->
```

## Advanced Helper Patterns

### Conditional Helpers
```liquid
{% comment %} Safe money formatting {% endcomment %}
{% assign formatted_price = product.price | default: 0 | money %}

{% comment %} Fallback image handling {% endcomment %}
{% if product.featured_image %}
  {{ product.featured_image | img_url: '300x300' | img_tag: product.title }}
{% else %}
  {{ 'product-1' | placeholder_svg_tag: product.title }}
{% endif %}

{% comment %} Safe link generation {% endcomment %}
{% if product.available %}
  {{ 'Add to Cart' | link_to: product.url, class: 'btn btn-primary' }}
{% else %}
  <span class="btn btn-disabled">Sold Out</span>
{% endif %}
```

### Complex Formatting
```liquid
{% comment %} Price range formatting {% endcomment %}
{% if product.price_varies %}
  {% assign min_price = product.price_min | money %}
  {% assign max_price = product.price_max | money %}
  <span class="price-range">{{ min_price }} - {{ max_price }}</span>
{% else %}
  <span class="price">{{ product.price | money }}</span>
{% endif %}

{% comment %} Weight calculation and formatting {% endcomment %}
{% assign total_weight = 0 %}
{% for item in cart.items %}
  {% assign item_weight = item.variant.weight | times: item.quantity %}
  {% assign total_weight = total_weight | plus: item_weight %}
{% endfor %}
<p>Total weight: {{ total_weight | weight_with_unit }}</p>
```

### Internationalization Helpers
```liquid
{% comment %} Multi-language content {% endcomment %}
<h1>{{ 'general.404.title' | t }}</h1>
<p>{{ 'general.404.subtext' | t }}</p>

{% comment %} Dynamic translations {% endcomment %}
{% assign translation_key = 'products.product.' | append: product.type | downcase %}
<span class="product-type">{{ translation_key | t: default: product.type }}</span>

{% comment %} Pluralization {% endcomment %}
{% assign item_count = cart.item_count %}
{% if item_count == 1 %}
  {{ 'cart.general.item' | t }}
{% else %}
  {{ 'cart.general.items' | t: count: item_count }}
{% endif %}
```

### Performance Optimization
```liquid
{% comment %} Cache expensive operations {% endcomment %}
{% assign formatted_prices = collection.products | map: 'price' | map: 'money' %}
{% assign product_links = collection.products | map: 'url' %}

{% for product in collection.products %}
  {% assign index = forloop.index0 %}
  <div class="product-card">
    <a href="{{ product_links[index] }}">
      {{ product.title }}
    </a>
    <span class="price">{{ formatted_prices[index] }}</span>
  </div>
{% endfor %}

{% comment %} Batch image processing {% endcomment %}
{% assign product_images = collection.products | map: 'featured_image' %}
{% assign image_urls = product_images | map: 'url' %}
```

### Error Handling
```liquid
{% comment %} Safe helper usage with fallbacks {% endcomment %}
{% assign safe_title = product.title | default: 'Product' | escape %}
{% assign safe_price = product.price | default: 0 | money %}
{% assign safe_image = product.featured_image | default: 'product-1' %}

<div class="product-card">
  {% if product.featured_image %}
    {{ product.featured_image | img_url: '300x300' | img_tag: safe_title }}
  {% else %}
    {{ 'product-1' | placeholder_svg_tag: safe_title }}
  {% endif %}
  
  <h3>{{ safe_title }}</h3>
  <p>{{ safe_price }}</p>
</div>

{% comment %} Translation with fallbacks {% endcomment %}
{% assign button_text = 'buttons.add_to_cart' | t %}
{% if button_text contains 'translation missing' %}
  {% assign button_text = 'Add to Cart' %}
{% endif %}
```

## Best Practices

### Performance
1. **Cache helper results**: Store expensive helper operations in variables
2. **Batch operations**: Use map and other array filters for bulk processing
3. **Avoid nested helpers**: Minimize complex filter chains
4. **Use appropriate defaults**: Provide sensible fallback values

### Accessibility
1. **Meaningful alt text**: Always provide descriptive alt attributes for images
2. **Semantic markup**: Use appropriate HTML elements (time, img, etc.)
3. **Screen reader friendly**: Ensure helper-generated content is accessible
4. **Keyboard navigation**: Maintain focus management with generated links

### Internationalization
1. **Use translation keys**: Always use the `t` filter for user-facing text
2. **Provide fallbacks**: Include default values for missing translations
3. **Context-aware translations**: Use appropriate translation keys for context
4. **Variable substitution**: Use translation variables for dynamic content

### Security
1. **Escape user content**: Always escape user-generated content
2. **Validate inputs**: Check for nil/empty values before processing
3. **Safe JSON output**: Use the json filter for JavaScript data
4. **Sanitize URLs**: Ensure generated URLs are safe and valid
