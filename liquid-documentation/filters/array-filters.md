# Array Filters

Array filters manipulate collections and arrays, providing powerful tools for sorting, filtering, and transforming data.

## Basic Array Operations

### first
Returns the first element of an array.

```liquid
{{ collection.products.first.title }}
<!-- Output: First product title -->

{% assign featured_product = collections.featured.products.first %}
<h2>Featured: {{ featured_product.title }}</h2>
```

### last
Returns the last element of an array.

```liquid
{{ collection.products.last.title }}
<!-- Output: Last product title -->

{% assign newest_article = blog.articles.last %}
<p>Latest: {{ newest_article.title }}</p>
```

### size
Returns the number of elements in an array.

```liquid
{{ collection.products.size }}
<!-- Output: 25 -->

{% if cart.items.size > 0 %}
  <p>You have {{ cart.items.size }} items in your cart</p>
{% else %}
  <p>Your cart is empty</p>
{% endif %}
```

## Array Transformation

### reverse
Reverses the order of elements in an array.

```liquid
{% assign reversed_products = collection.products | reverse %}
{% for product in reversed_products limit: 3 %}
  <div>{{ product.title }}</div>
{% endfor %}
```

### sort
Sorts an array by a specified property.

```liquid
{% comment %} Sort products by price (ascending) {% endcomment %}
{% assign sorted_products = collection.products | sort: 'price' %}

{% comment %} Sort articles by date {% endcomment %}
{% assign recent_articles = blog.articles | sort: 'published_at' | reverse %}

{% comment %} Sort by title alphabetically {% endcomment %}
{% assign alphabetical_products = collection.products | sort: 'title' %}
```

### sort_natural
Sorts an array using natural sort order (handles numbers correctly).

```liquid
{% comment %} Natural sorting handles "Product 2" before "Product 10" {% endcomment %}
{% assign naturally_sorted = products | sort_natural: 'title' %}

{% comment %} Sort sizes naturally: XS, S, M, L, XL {% endcomment %}
{% assign sorted_sizes = product.options_with_values.first.values | sort_natural %}
```

### uniq
Removes duplicate elements from an array.

```liquid
{% comment %} Get unique vendors {% endcomment %}
{% assign all_vendors = collection.products | map: 'vendor' | uniq %}
{% for vendor in all_vendors %}
  <option value="{{ vendor }}">{{ vendor }}</option>
{% endfor %}

{% comment %} Get unique product types {% endcomment %}
{% assign product_types = collection.products | map: 'type' | uniq | sort %}
```

## Array Filtering

### where
Filters an array based on a property value.

```liquid
{% comment %} Get only available products {% endcomment %}
{% assign available_products = collection.products | where: 'available', true %}

{% comment %} Get products from specific vendor {% endcomment %}
{% assign nike_products = collection.products | where: 'vendor', 'Nike' %}

{% comment %} Get featured articles {% endcomment %}
{% assign featured_articles = blog.articles | where: 'tags', 'featured' %}
```

### where with operators
CloudCart supports advanced where filtering with operators.

```liquid
{% comment %} Products under $50 {% endcomment %}
{% assign affordable_products = collection.products | where: 'price', '<', 5000 %}

{% comment %} Products over $100 {% endcomment %}
{% assign premium_products = collection.products | where: 'price', '>', 10000 %}

{% comment %} Products in price range {% endcomment %}
{% assign mid_range = collection.products | where: 'price', '>=', 5000 | where: 'price', '<=', 15000 %}
```

## Array Extraction

### map
Extracts a property from each element in an array.

```liquid
{% comment %} Get all product titles {% endcomment %}
{% assign product_titles = collection.products | map: 'title' %}

{% comment %} Get all product prices {% endcomment %}
{% assign product_prices = collection.products | map: 'price' %}

{% comment %} Get all customer emails {% endcomment %}
{% assign customer_emails = shop.customers | map: 'email' %}

{% comment %} Extract nested properties {% endcomment %}
{% assign image_urls = collection.products | map: 'featured_image' | map: 'url' %}
```

### pluck
Similar to map, but can handle nested properties more elegantly.

```liquid
{% comment %} Extract variant prices {% endcomment %}
{% assign variant_prices = product.variants | pluck: 'price' %}

{% comment %} Extract option values {% endcomment %}
{% assign color_options = product.options_with_values | pluck: 'values' | first %}
```

## Array Combination

### join
Joins array elements into a string with a separator.

```liquid
{% comment %} Create comma-separated list {% endcomment %}
{{ product.tags | join: ', ' }}
<!-- Output: featured, sale, new -->

{% comment %} Create breadcrumb navigation {% endcomment %}
{% assign breadcrumbs = collections | map: 'title' | join: ' > ' %}

{% comment %} Create CSS classes {% endcomment %}
{% assign css_classes = product.tags | join: ' tag-' | prepend: 'tag-' %}
<div class="{{ css_classes }}">Product</div>
```

### concat
Combines two arrays into one.

```liquid
{% comment %} Combine featured and sale products {% endcomment %}
{% assign featured_products = collections.featured.products %}
{% assign sale_products = collections.sale.products %}
{% assign combined_products = featured_products | concat: sale_products %}

{% comment %} Combine multiple collections {% endcomment %}
{% assign all_products = collections.electronics.products | concat: collections.clothing.products %}
```

### compact
Removes nil/null values from an array.

```liquid
{% comment %} Remove empty values {% endcomment %}
{% assign clean_tags = product.tags | compact %}

{% comment %} Remove products without images {% endcomment %}
{% assign products_with_images = collection.products | map: 'featured_image' | compact %}
```

## Advanced Array Operations

### group_by
Groups array elements by a property value.

```liquid
{% comment %} Group products by vendor {% endcomment %}
{% assign products_by_vendor = collection.products | group_by: 'vendor' %}
{% for vendor_group in products_by_vendor %}
  <h3>{{ vendor_group.name }}</h3>
  {% for product in vendor_group.items %}
    <p>{{ product.title }}</p>
  {% endfor %}
{% endfor %}

{% comment %} Group articles by author {% endcomment %}
{% assign articles_by_author = blog.articles | group_by: 'author' %}
```

### slice
Extracts a portion of an array.

```liquid
{% comment %} Get first 3 products {% endcomment %}
{% assign first_three = collection.products | slice: 0, 3 %}

{% comment %} Get products 4-6 {% endcomment %}
{% assign next_three = collection.products | slice: 3, 3 %}

{% comment %} Get last 5 products {% endcomment %}
{% assign last_five = collection.products | slice: -5, 5 %}
```

### sample
Returns a random element from an array.

```liquid
{% comment %} Random featured product {% endcomment %}
{% assign random_product = collection.products | sample %}
<div class="random-feature">
  <h3>You might like: {{ random_product.title }}</h3>
</div>

{% comment %} Random testimonial {% endcomment %}
{% assign random_testimonial = testimonials | sample %}
```

## Practical Examples

### Product Filtering Interface
```liquid
{% comment %} Create filter options {% endcomment %}
{% assign all_vendors = collection.products | map: 'vendor' | uniq | sort %}
{% assign all_types = collection.products | map: 'type' | uniq | sort %}
{% assign price_ranges = collection.products | map: 'price' | sort %}

<div class="filters">
  <select name="vendor">
    <option value="">All Vendors</option>
    {% for vendor in all_vendors %}
      <option value="{{ vendor }}">{{ vendor }}</option>
    {% endfor %}
  </select>
  
  <select name="type">
    <option value="">All Types</option>
    {% for type in all_types %}
      <option value="{{ type }}">{{ type }}</option>
    {% endfor %}
  </select>
</div>
```

### Product Recommendations
```liquid
{% comment %} Related products by tags {% endcomment %}
{% assign current_tags = product.tags %}
{% assign related_products = collections.all.products | where: 'id', '!=', product.id %}

{% assign recommendations = '' | split: '' %}
{% for related_product in related_products %}
  {% assign common_tags = related_product.tags | concat: current_tags | uniq %}
  {% if common_tags.size < related_product.tags.size | plus: current_tags.size %}
    {% assign recommendations = recommendations | concat: related_product %}
  {% endif %}
{% endfor %}

{% assign final_recommendations = recommendations | uniq | slice: 0, 4 %}
```

### Inventory Analysis
```liquid
{% comment %} Analyze product availability {% endcomment %}
{% assign available_products = collection.products | where: 'available', true %}
{% assign sold_out_products = collection.products | where: 'available', false %}
{% assign low_stock_products = collection.products | where: 'inventory_quantity', '<', 10 %}

<div class="inventory-stats">
  <p>Available: {{ available_products.size }}</p>
  <p>Sold Out: {{ sold_out_products.size }}</p>
  <p>Low Stock: {{ low_stock_products.size }}</p>
</div>
```

### Price Analysis
```liquid
{% comment %} Calculate price statistics {% endcomment %}
{% assign prices = collection.products | map: 'price' | sort %}
{% assign min_price = prices.first %}
{% assign max_price = prices.last %}
{% assign total_price = 0 %}

{% for price in prices %}
  {% assign total_price = total_price | plus: price %}
{% endfor %}

{% assign average_price = total_price | divided_by: prices.size %}

<div class="price-stats">
  <p>Price Range: {{ min_price | money }} - {{ max_price | money }}</p>
  <p>Average Price: {{ average_price | money }}</p>
</div>
```

### Dynamic Collections
```liquid
{% comment %} Create dynamic featured collection {% endcomment %}
{% assign featured_products = '' | split: '' %}

{% comment %} Add new products {% endcomment %}
{% assign new_products = collections.all.products | where: 'created_at', '>', 'now' | date: '%s' | minus: 604800 %}
{% assign featured_products = featured_products | concat: new_products | slice: 0, 2 %}

{% comment %} Add sale products {% endcomment %}
{% assign sale_products = collections.all.products | where: 'compare_at_price', '>', 0 %}
{% assign featured_products = featured_products | concat: sale_products | slice: 0, 4 %}

{% comment %} Add popular products {% endcomment %}
{% assign popular_products = collections.all.products | where: 'tags', 'popular' %}
{% assign featured_products = featured_products | concat: popular_products | slice: 0, 6 %}

{% assign featured_products = featured_products | uniq | slice: 0, 8 %}
```

### Search Results Processing
```liquid
{% comment %} Process and rank search results {% endcomment %}
{% assign search_results = search.results %}

{% comment %} Exact title matches first {% endcomment %}
{% assign exact_matches = search_results | where: 'title', search.terms %}

{% comment %} Title contains search term {% endcomment %}
{% assign title_matches = '' | split: '' %}
{% for result in search_results %}
  {% if result.title contains search.terms and result.title != search.terms %}
    {% assign title_matches = title_matches | concat: result %}
  {% endif %}
{% endfor %}

{% comment %} Description matches {% endcomment %}
{% assign description_matches = '' | split: '' %}
{% for result in search_results %}
  {% if result.description contains search.terms %}
    {% assign description_matches = description_matches | concat: result %}
  {% endif %}
{% endfor %}

{% comment %} Combine and deduplicate {% endcomment %}
{% assign ranked_results = exact_matches | concat: title_matches | concat: description_matches | uniq %}
```

## Performance Tips

1. **Cache filtered arrays**: Use `assign` to store filtered results
2. **Limit early**: Apply `slice` or `limit` as early as possible
3. **Chain filters efficiently**: `products | where: 'available' | sort: 'price' | slice: 0, 10`
4. **Use appropriate filters**: `where` is faster than manual filtering in loops

## Common Patterns

### Pagination Helper
```liquid
{% assign items_per_page = 12 %}
{% assign current_page = request.page | default: 1 %}
{% assign offset = current_page | minus: 1 | times: items_per_page %}
{% assign paginated_products = collection.products | slice: offset, items_per_page %}
```

### Category Navigation
```liquid
{% assign categories = collections | map: 'handle' | uniq | sort %}
{% for category in categories %}
  <a href="/collections/{{ category }}">{{ category | replace: '-', ' ' | capitalize }}</a>
{% endfor %}
```

### Stock Status
```liquid
{% assign in_stock = collection.products | where: 'available', true | size %}
{% assign total_products = collection.products.size %}
{% assign stock_percentage = in_stock | times: 100 | divided_by: total_products %}
```
