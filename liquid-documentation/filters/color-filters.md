# Color Filters

Color filters provide functionality for color manipulation, conversion, and accessibility in CloudCart Liquid templates.

## Color Format Conversion

### color_to_hex
Converts color values to hexadecimal format.

```liquid
{{ '#ff0000' | color_to_hex }}
<!-- Output: #ff0000 -->

{{ 'rgb(255, 0, 0)' | color_to_hex }}
<!-- Output: #ff0000 -->

{{ 'red' | color_to_hex }}
<!-- Output: #ff0000 -->
```

### color_to_rgb
Converts color values to RGB format.

```liquid
{{ '#ff0000' | color_to_rgb }}
<!-- Output: rgb(255, 0, 0) -->

{{ 'red' | color_to_rgb }}
<!-- Output: rgb(255, 0, 0) -->

{{ 'hsl(0, 100%, 50%)' | color_to_rgb }}
<!-- Output: rgb(255, 0, 0) -->
```

### color_to_hsl
Converts color values to HSL format.

```liquid
{{ '#ff0000' | color_to_hsl }}
<!-- Output: hsl(0, 100%, 50%) -->

{{ 'rgb(255, 0, 0)' | color_to_hsl }}
<!-- Output: hsl(0, 100%, 50%) -->
```

### color_to_rgba
Converts color values to RGBA format with alpha channel.

```liquid
{{ '#ff0000' | color_to_rgba: 0.5 }}
<!-- Output: rgba(255, 0, 0, 0.5) -->

{{ 'red' | color_to_rgba: 0.8 }}
<!-- Output: rgba(255, 0, 0, 0.8) -->
```

## Color Manipulation

### color_lighten
Lightens a color by a specified percentage.

```liquid
{{ '#ff0000' | color_lighten: 20 }}
<!-- Output: Lighter red -->

{{ settings.primary_color | color_lighten: 10 }}
<!-- Lighten theme color by 10% -->

{{ '#333333' | color_lighten: 50 }}
<!-- Create a lighter variant -->
```

### color_darken
Darkens a color by a specified percentage.

```liquid
{{ '#ff0000' | color_darken: 20 }}
<!-- Output: Darker red -->

{{ settings.primary_color | color_darken: 15 }}
<!-- Darken theme color by 15% -->

{{ '#cccccc' | color_darken: 30 }}
<!-- Create a darker variant -->
```

### color_saturate
Increases color saturation by a specified percentage.

```liquid
{{ '#ff6666' | color_saturate: 25 }}
<!-- Output: More saturated red -->

{{ settings.accent_color | color_saturate: 20 }}
<!-- Make accent color more vibrant -->
```

### color_desaturate
Decreases color saturation by a specified percentage.

```liquid
{{ '#ff0000' | color_desaturate: 30 }}
<!-- Output: Less saturated red -->

{{ settings.primary_color | color_desaturate: 50 }}
<!-- Create a muted version -->
```

### color_mix
Mixes two colors together.

```liquid
{{ '#ff0000' | color_mix: '#0000ff', 50 }}
<!-- Output: Purple (50% mix of red and blue) -->

{{ settings.primary_color | color_mix: '#ffffff', 20 }}
<!-- Mix primary color with 20% white -->

{{ '#000000' | color_mix: settings.brand_color, 75 }}
<!-- Mix black with 75% brand color -->
```

## Color Analysis

### color_brightness
Returns the brightness value of a color (0-100).

```liquid
{{ '#ffffff' | color_brightness }}
<!-- Output: 100 -->

{{ '#000000' | color_brightness }}
<!-- Output: 0 -->

{{ settings.background_color | color_brightness }}
<!-- Get background brightness for contrast calculations -->
```

### color_contrast
Calculates contrast ratio between two colors.

```liquid
{{ '#ffffff' | color_contrast: '#000000' }}
<!-- Output: 21 (maximum contrast) -->

{{ settings.background_color | color_contrast: settings.text_color }}
<!-- Check theme color contrast -->
```

### color_difference
Calculates the difference between two colors.

```liquid
{{ '#ff0000' | color_difference: '#ff0001' }}
<!-- Output: Very small difference -->

{{ settings.primary_color | color_difference: settings.secondary_color }}
<!-- Compare theme colors -->
```

## Accessibility Helpers

### color_modify
Modifies a color to meet accessibility requirements.

```liquid
{{ settings.text_color | color_modify: 'contrast', settings.background_color }}
<!-- Ensure sufficient contrast -->

{{ '#666666' | color_modify: 'brightness', 70 }}
<!-- Adjust brightness to specific level -->
```

### color_accessible
Returns an accessible color variant.

```liquid
{{ settings.link_color | color_accessible: settings.background_color }}
<!-- Get accessible version of link color -->

{{ '#ff0000' | color_accessible: '#ffffff', 'AA' }}
<!-- Ensure AA compliance -->
```

## Practical Examples

### Dynamic Theme Colors
```liquid
{% comment %} Generate color palette from primary color {% endcomment %}
{% assign primary_color = settings.primary_color %}
{% assign primary_light = primary_color | color_lighten: 20 %}
{% assign primary_dark = primary_color | color_darken: 20 %}
{% assign primary_muted = primary_color | color_desaturate: 30 %}

<style>
  :root {
    --color-primary: {{ primary_color }};
    --color-primary-light: {{ primary_light }};
    --color-primary-dark: {{ primary_dark }};
    --color-primary-muted: {{ primary_muted }};
    
    /* Hover states */
    --color-primary-hover: {{ primary_color | color_darken: 10 }};
    --color-primary-active: {{ primary_color | color_darken: 15 }};
    
    /* Background variants */
    --color-primary-bg: {{ primary_color | color_lighten: 45 }};
    --color-primary-bg-hover: {{ primary_color | color_lighten: 40 }};
  }
  
  .btn-primary {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
  }
  
  .btn-primary:hover {
    background-color: var(--color-primary-hover);
    border-color: var(--color-primary-hover);
  }
  
  .alert-primary {
    background-color: var(--color-primary-bg);
    border-color: var(--color-primary-light);
    color: var(--color-primary-dark);
  }
</style>
```

### Accessible Color Schemes
```liquid
{% comment %} Ensure accessible text colors {% endcomment %}
{% assign bg_color = settings.background_color %}
{% assign text_color = settings.text_color %}
{% assign link_color = settings.link_color %}

{% comment %} Check contrast ratios {% endcomment %}
{% assign text_contrast = text_color | color_contrast: bg_color %}
{% assign link_contrast = link_color | color_contrast: bg_color %}

<style>
  body {
    background-color: {{ bg_color }};
    
    {% if text_contrast >= 4.5 %}
      color: {{ text_color }};
    {% else %}
      {% comment %} Use accessible alternative {% endcomment %}
      color: {{ text_color | color_accessible: bg_color }};
    {% endif %}
  }
  
  a {
    {% if link_contrast >= 4.5 %}
      color: {{ link_color }};
    {% else %}
      color: {{ link_color | color_accessible: bg_color }};
    {% endif %}
  }
  
  {% comment %} Focus states for accessibility {% endcomment %}
  a:focus,
  button:focus {
    outline: 2px solid {{ link_color | color_contrast: bg_color | at_least: 3 }};
    outline-offset: 2px;
  }
</style>
```

### Product Color Variants
```liquid
{% comment %} Display product color options {% endcomment %}
{% if product.options contains 'Color' %}
  <div class="color-swatches">
    <h4>{{ 'products.product.color' | t }}</h4>
    
    {% for variant in product.variants %}
      {% if variant.option1 contains 'Color' or variant.option2 contains 'Color' or variant.option3 contains 'Color' %}
        {% assign color_name = variant.option1 %}
        {% if variant.option2 contains 'Color' %}
          {% assign color_name = variant.option2 %}
        {% elsif variant.option3 contains 'Color' %}
          {% assign color_name = variant.option3 %}
        {% endif %}
        
        {% comment %} Convert color name to hex {% endcomment %}
        {% assign color_hex = color_name | color_to_hex %}
        
        <label class="color-swatch" 
               style="background-color: {{ color_hex }};"
               title="{{ color_name }}">
          <input type="radio" 
                 name="color" 
                 value="{{ variant.id }}"
                 {% if variant == product.selected_or_first_available_variant %}checked{% endif %}>
          <span class="swatch-color" 
                style="background-color: {{ color_hex }};
                       border: 2px solid {{ color_hex | color_darken: 20 }};">
          </span>
          <span class="sr-only">{{ color_name }}</span>
        </label>
      {% endif %}
    {% endfor %}
  </div>
{% endif %}
```

### Dynamic Status Colors
```liquid
{% comment %} Order status colors {% endcomment %}
{% for order in customer.orders %}
  {% case order.financial_status %}
    {% when 'paid' %}
      {% assign status_color = '#28a745' %}
    {% when 'pending' %}
      {% assign status_color = '#ffc107' %}
    {% when 'refunded' %}
      {% assign status_color = '#6c757d' %}
    {% when 'cancelled' %}
      {% assign status_color = '#dc3545' %}
    {% else %}
      {% assign status_color = '#17a2b8' %}
  {% endcase %}
  
  <div class="order-status" 
       style="background-color: {{ status_color | color_lighten: 40 }};
              border-left: 4px solid {{ status_color }};
              color: {{ status_color | color_darken: 20 }};">
    <span class="status-indicator" 
          style="background-color: {{ status_color }};"></span>
    {{ order.financial_status | capitalize }}
  </div>
{% endfor %}
```

### Gradient Generation
```liquid
{% comment %} Create gradients from theme colors {% endcomment %}
{% assign primary = settings.primary_color %}
{% assign secondary = settings.secondary_color %}

<style>
  .hero-gradient {
    background: linear-gradient(
      135deg,
      {{ primary }} 0%,
      {{ primary | color_mix: secondary, 50 }} 50%,
      {{ secondary }} 100%
    );
  }
  
  .card-gradient {
    background: linear-gradient(
      to bottom,
      {{ primary | color_lighten: 30 }} 0%,
      {{ primary | color_lighten: 40 }} 100%
    );
  }
  
  .text-gradient {
    background: linear-gradient(
      45deg,
      {{ primary }},
      {{ primary | color_saturate: 20 | color_lighten: 10 }}
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
</style>
```

### Color Palette Generator
```liquid
{% comment %} Generate complete color palette {% endcomment %}
{% assign base_color = settings.brand_color | default: '#007bff' %}

<div class="color-palette">
  <h3>Brand Color Palette</h3>
  
  <div class="palette-grid">
    {% for i in (1..5) %}
      {% assign lightness = i | times: 15 | plus: 10 %}
      {% assign light_variant = base_color | color_lighten: lightness %}
      {% assign dark_variant = base_color | color_darken: lightness %}
      
      <div class="color-swatch">
        <div class="swatch" style="background-color: {{ light_variant }};"></div>
        <code>{{ light_variant }}</code>
      </div>
    {% endfor %}
    
    <div class="color-swatch primary">
      <div class="swatch" style="background-color: {{ base_color }};"></div>
      <code>{{ base_color }}</code>
    </div>
    
    {% for i in (1..5) %}
      {% assign darkness = i | times: 15 %}
      {% assign dark_variant = base_color | color_darken: darkness %}
      
      <div class="color-swatch">
        <div class="swatch" style="background-color: {{ dark_variant }};"></div>
        <code>{{ dark_variant }}</code>
      </div>
    {% endfor %}
  </div>
</div>
```

### Dark Mode Support
```liquid
{% comment %} Automatic dark mode color adjustments {% endcomment %}
{% assign light_bg = settings.background_color | default: '#ffffff' %}
{% assign light_text = settings.text_color | default: '#333333' %}

{% comment %} Generate dark mode variants {% endcomment %}
{% assign dark_bg = light_bg | color_darken: 80 %}
{% assign dark_text = light_text | color_lighten: 70 %}

<style>
  :root {
    --bg-color: {{ light_bg }};
    --text-color: {{ light_text }};
    --border-color: {{ light_bg | color_darken: 10 }};
  }
  
  @media (prefers-color-scheme: dark) {
    :root {
      --bg-color: {{ dark_bg }};
      --text-color: {{ dark_text }};
      --border-color: {{ dark_bg | color_lighten: 15 }};
    }
  }
  
  [data-theme="dark"] {
    --bg-color: {{ dark_bg }};
    --text-color: {{ dark_text }};
    --border-color: {{ dark_bg | color_lighten: 15 }};
  }
  
  body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
  }
</style>
```

## Best Practices

### Accessibility
1. **Maintain contrast ratios**: Ensure WCAG AA compliance (4.5:1 minimum)
2. **Test with tools**: Use accessibility testing tools to verify color choices
3. **Provide alternatives**: Don't rely solely on color to convey information
4. **Consider color blindness**: Test with color blindness simulators

### Performance
1. **Cache color calculations**: Store computed colors in CSS custom properties
2. **Minimize filter usage**: Avoid excessive color manipulation in loops
3. **Use CSS when possible**: Prefer CSS color functions over Liquid filters
4. **Optimize gradients**: Use efficient gradient syntax

### User Experience
1. **Consistent color schemes**: Maintain visual harmony across the site
2. **Meaningful color usage**: Use colors that align with user expectations
3. **Responsive design**: Ensure colors work well on different devices
4. **Brand alignment**: Keep colors consistent with brand guidelines

### Development
1. **Document color systems**: Maintain a clear color palette documentation
2. **Use semantic naming**: Name colors by function, not appearance
3. **Version control**: Track color changes in theme updates
4. **Test thoroughly**: Verify colors across different browsers and devices
