# Asset Filters

Asset filters manage and optimize theme assets including stylesheets, JavaScript files, images, and other media resources in CloudCart.

## Asset URL Generation

### asset_url
Generates URLs for theme assets stored in the assets directory.

```liquid
{{ 'theme.css' | asset_url }}
<!-- Output: /assets/theme.css -->

{{ 'product-gallery.js' | asset_url }}
<!-- Output: /assets/product-gallery.js -->

{{ 'logo.png' | asset_url }}
<!-- Output: /assets/logo.png -->
```

### asset_img_url
Generates optimized image URLs with size parameters.

```liquid
{{ 'hero-banner.jpg' | asset_img_url }}
<!-- Output: /assets/hero-banner.jpg -->

{{ 'hero-banner.jpg' | asset_img_url: '1920x800' }}
<!-- Output: /assets/hero-banner_1920x800.jpg -->

{{ 'product-placeholder.png' | asset_img_url: '300x300', crop: 'center' }}
<!-- Output: /assets/product-placeholder_300x300_crop_center.png -->
```

### file_url
Generates URLs for uploaded files in the files directory.

```liquid
{{ 'catalog.pdf' | file_url }}
<!-- Output: /files/catalog.pdf -->

{{ 'size-guide.pdf' | file_url }}
<!-- Output: /files/size-guide.pdf -->
```

## Stylesheet Management

### stylesheet_tag
Creates HTML link tags for stylesheets.

```liquid
{{ 'theme.css' | asset_url | stylesheet_tag }}
<!-- Output: <link href="/assets/theme.css" rel="stylesheet" type="text/css" media="all"> -->

{{ 'print.css' | asset_url | stylesheet_tag: media: 'print' }}
<!-- Output: <link href="/assets/print.css" rel="stylesheet" type="text/css" media="print"> -->
```

### Conditional Stylesheet Loading
```liquid
{% comment %} Load different stylesheets based on template {% endcomment %}
{% case template %}
  {% when 'index' %}
    {{ 'homepage.css' | asset_url | stylesheet_tag }}
  {% when 'product' %}
    {{ 'product-page.css' | asset_url | stylesheet_tag }}
  {% when 'collection' %}
    {{ 'collection-page.css' | asset_url | stylesheet_tag }}
{% endcase %}

{% comment %} Load stylesheets based on settings {% endcomment %}
{% if settings.enable_animations %}
  {{ 'animations.css' | asset_url | stylesheet_tag }}
{% endif %}

{% if settings.theme_style == 'dark' %}
  {{ 'dark-theme.css' | asset_url | stylesheet_tag }}
{% endif %}
```

### Critical CSS Inlining
```liquid
{% comment %} Inline critical CSS for performance {% endcomment %}
<style>
  {% comment %} Critical above-the-fold styles {% endcomment %}
  .header { display: flex; justify-content: space-between; }
  .hero { height: 100vh; background: #f0f0f0; }
  .btn { padding: 12px 24px; background: #007bff; color: white; }
</style>

{% comment %} Load non-critical CSS asynchronously {% endcomment %}
<link rel="preload" href="{{ 'theme.css' | asset_url }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript>{{ 'theme.css' | asset_url | stylesheet_tag }}</noscript>
```

## JavaScript Management

### script_tag
Creates HTML script tags for JavaScript files.

```liquid
{{ 'theme.js' | asset_url | script_tag }}
<!-- Output: <script src="/assets/theme.js" type="text/javascript"></script> -->

{{ 'product-gallery.js' | asset_url | script_tag: defer: 'defer' }}
<!-- Output: <script src="/assets/product-gallery.js" type="text/javascript" defer="defer"></script> -->
```

### Async and Defer Loading
```liquid
{% comment %} Load scripts with performance optimizations {% endcomment %}
{{ 'critical.js' | asset_url | script_tag }}
{{ 'non-critical.js' | asset_url | script_tag: defer: 'defer' }}
{{ 'analytics.js' | asset_url | script_tag: async: 'async' }}

{% comment %} Conditional script loading {% endcomment %}
{% if template contains 'product' %}
  {{ 'product-variants.js' | asset_url | script_tag: defer: 'defer' }}
  {{ 'product-gallery.js' | asset_url | script_tag: defer: 'defer' %}
{% endif %}

{% if template contains 'cart' %}
  {{ 'cart-functionality.js' | asset_url | script_tag }}
{% endif %}
```

### Module Scripts
```liquid
{% comment %} ES6 module loading {% endcomment %}
<script type="module" src="{{ 'app.js' | asset_url }}"></script>
<script nomodule src="{{ 'app-legacy.js' | asset_url }}"></script>

{% comment %} Dynamic imports for code splitting {% endcomment %}
<script type="module">
  {% if template contains 'product' %}
    import('{{ "product-module.js" | asset_url }}').then(module => {
      module.initProductPage();
    });
  {% endif %}
</script>
```

## Image Optimization

### Responsive Images
```liquid
{% comment %} Generate responsive image sets {% endcomment %}
{% assign image = product.featured_image %}
{% if image %}
  <img src="{{ image | img_url: '800x600' }}"
       srcset="{{ image | img_url: '400x300' }} 400w,
               {{ image | img_url: '800x600' }} 800w,
               {{ image | img_url: '1200x900' }} 1200w,
               {{ image | img_url: '1600x1200' }} 1600w"
       sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
       alt="{{ image.alt | default: product.title }}">
{% endif %}
```

### Image Lazy Loading
```liquid
{% comment %} Lazy loading with intersection observer {% endcomment %}
<img class="lazy-image"
     data-src="{{ product.featured_image | img_url: '400x400' }}"
     data-srcset="{{ product.featured_image | img_url: '200x200' }} 200w,
                  {{ product.featured_image | img_url: '400x400' }} 400w,
                  {{ product.featured_image | img_url: '800x800' }} 800w"
     src="{{ 'placeholder.svg' | asset_url }}"
     alt="{{ product.title }}">

<script>
  // Lazy loading implementation
  const lazyImages = document.querySelectorAll('.lazy-image');
  
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        if (img.dataset.srcset) {
          img.srcset = img.dataset.srcset;
        }
        img.classList.remove('lazy-image');
        observer.unobserve(img);
      }
    });
  });
  
  lazyImages.forEach(img => imageObserver.observe(img));
</script>
```

### Image Formats and Fallbacks
```liquid
{% comment %} Modern image formats with fallbacks {% endcomment %}
<picture>
  <source srcset="{{ product.featured_image | img_url: '400x400', format: 'webp' }}" type="image/webp">
  <source srcset="{{ product.featured_image | img_url: '400x400', format: 'avif' }}" type="image/avif">
  <img src="{{ product.featured_image | img_url: '400x400' }}" alt="{{ product.title }}">
</picture>
```

## Font Management

### font_face
Generates CSS @font-face declarations for custom fonts.

```liquid
{% comment %} Custom font loading {% endcomment %}
<style>
  @font-face {
    font-family: 'CustomFont';
    src: url('{{ "custom-font.woff2" | asset_url }}') format('woff2'),
         url('{{ "custom-font.woff" | asset_url }}') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }
  
  body {
    font-family: 'CustomFont', Arial, sans-serif;
  }
</style>
```

### Google Fonts Integration
```liquid
{% comment %} Google Fonts with performance optimization {% endcomment %}
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

{% comment %} Font loading with fallbacks {% endcomment %}
<style>
  :root {
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-secondary: Georgia, 'Times New Roman', serif;
  }
  
  body {
    font-family: var(--font-primary);
  }
</style>
```

## Asset Optimization

### CSS Minification and Concatenation
```liquid
{% comment %} Combine and minify CSS files {% endcomment %}
{% if settings.minify_css %}
  {{ 'theme.min.css' | asset_url | stylesheet_tag }}
{% else %}
  {{ 'reset.css' | asset_url | stylesheet_tag }}
  {{ 'base.css' | asset_url | stylesheet_tag }}
  {{ 'components.css' | asset_url | stylesheet_tag }}
  {{ 'utilities.css' | asset_url | stylesheet_tag }}
{% endif %}
```

### JavaScript Bundling
```liquid
{% comment %} Load bundled or individual scripts based on environment {% endcomment %}
{% if settings.environment == 'production' %}
  {{ 'bundle.min.js' | asset_url | script_tag }}
{% else %}
  {{ 'utils.js' | asset_url | script_tag }}
  {{ 'components.js' | asset_url | script_tag }}
  {{ 'app.js' | asset_url | script_tag }}
{% endif %}
```

### Resource Hints
```liquid
{% comment %} Performance optimization with resource hints {% endcomment %}
<head>
  <!-- DNS prefetch for external resources -->
  <link rel="dns-prefetch" href="//fonts.googleapis.com">
  <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
  
  <!-- Preload critical assets -->
  <link rel="preload" href="{{ 'theme.css' | asset_url }}" as="style">
  <link rel="preload" href="{{ 'theme.js' | asset_url }}" as="script">
  
  <!-- Preload important images -->
  {% if template == 'index' and collections.featured.image %}
    <link rel="preload" href="{{ collections.featured.image | img_url: '1920x800' }}" as="image">
  {% endif %}
  
  <!-- Prefetch likely next pages -->
  {% if template == 'index' %}
    <link rel="prefetch" href="{{ collections.all.url }}">
  {% endif %}
</head>
```

## Advanced Asset Patterns

### Asset Versioning
```liquid
{% comment %} Cache busting with version numbers {% endcomment %}
{% assign css_version = 'v1.2.3' %}
{% assign js_version = 'v2.1.0' %}

{{ 'theme.css' | asset_url | append: '?v=' | append: css_version | stylesheet_tag }}
{{ 'theme.js' | asset_url | append: '?v=' | append: js_version | script_tag }}

{% comment %} Dynamic versioning based on file modification {% endcomment %}
{% assign theme_css_url = 'theme.css' | asset_url %}
{% assign cache_buster = 'now' | date: '%s' %}
{{ theme_css_url | append: '?cb=' | append: cache_buster | stylesheet_tag }}
```

### Conditional Asset Loading
```liquid
{% comment %} Load assets based on device capabilities {% endcomment %}
<script>
  // Feature detection and conditional loading
  if ('IntersectionObserver' in window) {
    // Load modern JavaScript
    const script = document.createElement('script');
    script.src = '{{ "modern-features.js" | asset_url }}';
    document.head.appendChild(script);
  } else {
    // Load polyfills
    const script = document.createElement('script');
    script.src = '{{ "polyfills.js" | asset_url }}';
    document.head.appendChild(script);
  }
  
  // Load high-DPI images for retina displays
  if (window.devicePixelRatio > 1) {
    document.documentElement.classList.add('high-dpi');
  }
</script>

<style>
  .hero-image {
    background-image: url('{{ "hero-standard.jpg" | asset_url }}');
  }
  
  .high-dpi .hero-image {
    background-image: url('{{ "hero-retina.jpg" | asset_url }}');
  }
</style>
```

### Service Worker Integration
```liquid
{% comment %} Service worker for asset caching {% endcomment %}
<script>
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
      navigator.serviceWorker.register('{{ "sw.js" | asset_url }}')
        .then(function(registration) {
          console.log('SW registered: ', registration);
        })
        .catch(function(registrationError) {
          console.log('SW registration failed: ', registrationError);
        });
    });
  }
</script>

{% comment %} sw.js - Service Worker file {% endcomment %}
{% raw %}
// Cache strategy for different asset types
const CACHE_NAME = 'cloudcart-v1';
const urlsToCache = [
  '{{ "theme.css" | asset_url }}',
  '{{ "theme.js" | asset_url }}',
  '{{ "logo.png" | asset_url }}'
];

self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        return cache.addAll(urlsToCache);
      })
  );
});

self.addEventListener('fetch', function(event) {
  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        if (response) {
          return response;
        }
        return fetch(event.request);
      }
    )
  );
});
{% endraw %}
```

### Asset Monitoring
```liquid
{% comment %} Monitor asset loading performance {% endcomment %}
<script>
  // Performance monitoring for assets
  window.addEventListener('load', function() {
    if ('performance' in window) {
      const resources = performance.getEntriesByType('resource');
      
      resources.forEach(resource => {
        if (resource.name.includes('/assets/')) {
          console.log(`Asset: ${resource.name}`);
          console.log(`Load time: ${resource.loadEnd - resource.loadStart}ms`);
          console.log(`Size: ${resource.transferSize} bytes`);
        }
      });
    }
  });
  
  // Error tracking for failed assets
  window.addEventListener('error', function(e) {
    if (e.target.tagName === 'SCRIPT' || e.target.tagName === 'LINK') {
      console.error('Asset failed to load:', e.target.src || e.target.href);
      
      // Send error to analytics
      if (typeof gtag !== 'undefined') {
        gtag('event', 'exception', {
          description: `Asset load error: ${e.target.src || e.target.href}`,
          fatal: false
        });
      }
    }
  }, true);
</script>
```

## Best Practices

### Performance Optimization
1. **Minimize HTTP requests**: Combine CSS and JS files when possible
2. **Use appropriate image formats**: WebP for modern browsers, JPEG/PNG fallbacks
3. **Implement lazy loading**: Load images and non-critical assets on demand
4. **Leverage browser caching**: Use proper cache headers and versioning

### Security Considerations
1. **Validate file types**: Only allow safe file extensions
2. **Use HTTPS**: Serve all assets over secure connections
3. **Implement CSP**: Use Content Security Policy for script sources
4. **Sanitize file names**: Ensure uploaded files have safe names

### Accessibility
1. **Provide alt text**: Always include descriptive alt attributes for images
2. **Use semantic markup**: Proper HTML structure for screen readers
3. **Ensure contrast**: Maintain sufficient color contrast ratios
4. **Support keyboard navigation**: Ensure all interactive elements are accessible

### Maintainability
1. **Organize assets logically**: Use clear directory structure and naming
2. **Document dependencies**: Comment complex asset relationships
3. **Version control**: Track asset changes and maintain backups
4. **Monitor performance**: Regularly audit asset loading times and sizes
