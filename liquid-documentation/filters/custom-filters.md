# Custom Filters

CloudCart provides custom filters specifically designed for e-commerce functionality and enhanced template capabilities.

## E-commerce Specific Filters

### product_url_with_variant
Generates product URLs with variant parameters.

```liquid
{{ product | product_url_with_variant: variant }}
<!-- Output: /products/awesome-shirt?variant=123456 -->

{{ product | product_url_with_variant: product.selected_or_first_available_variant }}
<!-- URL with selected variant -->
```

### collection_url_with_filters
Creates collection URLs with applied filters.

```liquid
{{ collection | collection_url_with_filters: vendor: 'Nike', type: 'Shoes' }}
<!-- Output: /collections/footwear?vendor=Nike&type=Shoes -->

{{ collection | collection_url_with_filters: price_min: 50, price_max: 200 }}
<!-- Price range filtering -->
```

### cart_add_url_with_properties
Generates cart add URLs with line item properties.

```liquid
{{ variant | cart_add_url_with_properties: 'Gift Message': 'Happy Birthday!' }}
<!-- Add to cart with custom properties -->

{{ variant | cart_add_url_with_properties: properties }}
<!-- Use properties object -->
```

### order_status_class
Returns CSS class based on order status.

```liquid
{{ order | order_status_class }}
<!-- Output: 'order-paid', 'order-pending', etc. -->

<div class="order-item {{ order | order_status_class }}">
  Order #{{ order.order_number }}
</div>
```

### inventory_status
Returns inventory status information.

```liquid
{{ variant | inventory_status }}
<!-- Output: 'in-stock', 'low-stock', 'out-of-stock' -->

{{ product | inventory_status: threshold: 5 }}
<!-- Custom low stock threshold -->
```

## Content and Media Filters

### responsive_image
Generates responsive image markup with srcset.

```liquid
{{ product.featured_image | responsive_image }}
<!-- Generates img tag with srcset for different sizes -->

{{ product.featured_image | responsive_image: sizes: '(max-width: 768px) 100vw, 50vw' }}
<!-- Custom sizes attribute -->
```

### lazy_image
Creates lazy-loading image markup.

```liquid
{{ product.featured_image | lazy_image: '400x400' }}
<!-- Image with lazy loading attributes -->

{{ product.featured_image | lazy_image: '400x400', placeholder: 'blur' }}
<!-- With blur placeholder -->
```

### video_tag
Generates HTML5 video elements.

```liquid
{{ 'product-demo.mp4' | asset_url | video_tag }}
<!-- Basic video tag -->

{{ 'hero-video.mp4' | asset_url | video_tag: autoplay: true, muted: true, loop: true }}
<!-- Video with attributes -->
```

### social_meta_tags
Generates social media meta tags.

```liquid
{{ product | social_meta_tags }}
<!-- Generates Open Graph and Twitter Card meta tags -->

{{ article | social_meta_tags: image: article.image }}
<!-- Custom image for social sharing -->
```

## Formatting and Display Filters

### currency_symbol
Returns the currency symbol for the shop.

```liquid
{{ shop | currency_symbol }}
<!-- Output: $, €, £, etc. -->

{{ 'USD' | currency_symbol }}
<!-- Get symbol for specific currency -->
```

### format_address
Formats addresses according to country standards.

```liquid
{{ customer.default_address | format_address }}
<!-- Properly formatted address -->

{{ order.shipping_address | format_address: 'short' }}
<!-- Condensed address format -->
```

### format_phone
Formats phone numbers according to locale.

```liquid
{{ customer.phone | format_phone }}
<!-- Formatted phone number -->

{{ '+1234567890' | format_phone: 'international' }}
<!-- International format -->
```

### reading_time
Calculates estimated reading time for content.

```liquid
{{ article.content | reading_time }}
<!-- Output: "5 min read" -->

{{ page.content | reading_time: words_per_minute: 250 }}
<!-- Custom reading speed -->
```

### excerpt
Creates content excerpts with smart truncation.

```liquid
{{ article.content | excerpt }}
<!-- Smart excerpt with sentence boundaries -->

{{ article.content | excerpt: 150, '...' }}
<!-- Custom length and suffix -->
```

## Utility and Helper Filters

### random
Returns a random item from an array.

```liquid
{{ collection.products | random }}
<!-- Random product from collection -->

{{ testimonials | random: 3 }}
<!-- 3 random testimonials -->
```

### shuffle
Randomizes the order of array items.

```liquid
{{ collection.products | shuffle }}
<!-- Products in random order -->

{{ related_products | shuffle | limit: 4 }}
<!-- 4 random related products -->
```

### group_by_date
Groups items by date periods.

```liquid
{{ blog.articles | group_by_date: 'month' }}
<!-- Group articles by month -->

{{ customer.orders | group_by_date: 'year' }}
<!-- Group orders by year -->
```

### distance_of_time
Returns human-readable time differences.

```liquid
{{ article.published_at | distance_of_time }}
<!-- Output: "2 days ago", "1 week ago", etc. -->

{{ order.created_at | distance_of_time: 'now' }}
<!-- Time since order creation -->
```

### humanize_bytes
Converts byte values to human-readable format.

```liquid
{{ file.size | humanize_bytes }}
<!-- Output: "1.5 MB", "256 KB", etc. -->

{{ attachment.file_size | humanize_bytes: precision: 1 }}
<!-- Custom precision -->
```

## Search and Filter Helpers

### highlight_search
Highlights search terms in content.

```liquid
{{ product.description | highlight_search: search.terms }}
<!-- Highlights search terms with <mark> tags -->

{{ product.title | highlight_search: search.terms, '<strong>', '</strong>' }}
<!-- Custom highlight tags -->
```

### search_url_with_filters
Creates search URLs with filters applied.

```liquid
{{ search.terms | search_url_with_filters: type: 'product' }}
<!-- Search only products -->

{{ search.terms | search_url_with_filters: collection: collection.handle }}
<!-- Search within collection -->
```

### filter_by_availability
Filters products by availability status.

```liquid
{{ collection.products | filter_by_availability: true }}
<!-- Only available products -->

{{ collection.products | filter_by_availability: false }}
<!-- Only sold out products -->
```

### filter_by_price_range
Filters products by price range.

```liquid
{{ collection.products | filter_by_price_range: 1000, 5000 }}
<!-- Products between $10-$50 -->

{{ collection.products | filter_by_price_range: min: 2000 }}
<!-- Products over $20 -->
```

## Advanced Custom Filters

### calculate_shipping
Calculates shipping costs based on cart contents.

```liquid
{{ cart | calculate_shipping: 'standard' }}
<!-- Standard shipping cost -->

{{ cart | calculate_shipping: 'express', country: 'US' }}
<!-- Express shipping to specific country -->
```

### tax_calculator
Calculates tax amounts for different regions.

```liquid
{{ cart.total_price | tax_calculator: customer.default_address }}
<!-- Tax based on customer address -->

{{ product.price | tax_calculator: 'CA', 'ON' }}
<!-- Tax for specific province -->
```

### discount_calculator
Calculates discount amounts and percentages.

```liquid
{{ product | discount_calculator: discount_code }}
<!-- Apply discount code to product -->

{{ cart | discount_calculator: customer.tags }}
<!-- Customer-specific discounts -->
```

### loyalty_points
Calculates loyalty points for purchases.

```liquid
{{ order.total_price | loyalty_points }}
<!-- Points earned from order -->

{{ customer | loyalty_points: 'balance' }}
<!-- Current points balance -->
```

## Practical Examples

### Enhanced Product Display
```liquid
<div class="product-card">
  {% assign product_image = product.featured_image | lazy_image: '400x400' %}
  {{ product_image }}
  
  <div class="product-info">
    <h3>{{ product.title }}</h3>
    
    <div class="product-price">
      {{ product.price | money }}
      {% assign savings = product | discount_calculator: customer.tags %}
      {% if savings > 0 %}
        <span class="savings">Save {{ savings | money }}</span>
      {% endif %}
    </div>
    
    <div class="product-status">
      <span class="inventory-status {{ product | inventory_status }}">
        {% case product | inventory_status %}
          {% when 'in-stock' %}
            In Stock
          {% when 'low-stock' %}
            Only {{ product.inventory_quantity }} left!
          {% when 'out-of-stock' %}
            Sold Out
        {% endcase %}
      </span>
    </div>
    
    {% assign shipping_cost = cart | calculate_shipping: 'standard' %}
    {% if shipping_cost == 0 %}
      <div class="free-shipping">Free Shipping</div>
    {% endif %}
  </div>
</div>
```

### Smart Content Display
```liquid
<article class="blog-post">
  <header class="post-header">
    <h1>{{ article.title }}</h1>
    
    <div class="post-meta">
      <time>{{ article.published_at | distance_of_time }}</time>
      <span class="reading-time">{{ article.content | reading_time }}</span>
    </div>
  </header>
  
  <div class="post-content">
    {% if search.terms %}
      {{ article.content | highlight_search: search.terms }}
    {% else %}
      {{ article.content }}
    {% endif %}
  </div>
  
  <div class="post-social">
    {{ article | social_meta_tags }}
  </div>
</article>
```

### Advanced Search Results
```liquid
<div class="search-results">
  {% assign highlighted_results = search.results | highlight_search: search.terms %}
  
  {% for result in highlighted_results %}
    <div class="search-result">
      {% case result.object_type %}
        {% when 'product' %}
          <div class="result-product">
            {{ result.featured_image | lazy_image: '100x100' }}
            <div class="result-content">
              <h3>{{ result.title }}</h3>
              <p class="price">{{ result.price | money }}</p>
              <span class="availability {{ result | inventory_status }}">
                {{ result | inventory_status | capitalize }}
              </span>
            </div>
          </div>
        {% when 'article' %}
          <div class="result-article">
            <h3>{{ result.title }}</h3>
            <p>{{ result.content | excerpt: 100 }}</p>
            <time>{{ result.published_at | distance_of_time }}</time>
          </div>
      {% endcase %}
    </div>
  {% endfor %}
</div>
```

### Customer Dashboard Enhancement
```liquid
<div class="customer-dashboard">
  <div class="customer-stats">
    <div class="stat">
      <span class="value">{{ customer.orders_count }}</span>
      <span class="label">Orders</span>
    </div>
    <div class="stat">
      <span class="value">{{ customer | loyalty_points: 'balance' }}</span>
      <span class="label">Points</span>
    </div>
    <div class="stat">
      <span class="value">{{ customer.total_spent | money }}</span>
      <span class="label">Total Spent</span>
    </div>
  </div>
  
  <div class="recent-orders">
    <h3>Recent Orders</h3>
    {% assign grouped_orders = customer.orders | group_by_date: 'month' %}
    {% for group in grouped_orders limit: 3 %}
      <div class="order-group">
        <h4>{{ group.name }}</h4>
        {% for order in group.items %}
          <div class="order-item {{ order | order_status_class }}">
            <span class="order-number">#{{ order.order_number }}</span>
            <span class="order-date">{{ order.created_at | distance_of_time }}</span>
            <span class="order-total">{{ order.total_price | money }}</span>
          </div>
        {% endfor %}
      </div>
    {% endfor %}
  </div>
</div>
```

## Best Practices

### Performance
1. **Cache filter results**: Store expensive custom filter operations in variables
2. **Use appropriate filters**: Choose the most efficient filter for the task
3. **Minimize complex operations**: Avoid nested custom filters in loops
4. **Optimize for mobile**: Consider performance impact on mobile devices

### Maintainability
1. **Document custom filters**: Provide clear documentation for custom functionality
2. **Use consistent naming**: Follow naming conventions for custom filters
3. **Version compatibility**: Ensure custom filters work across CloudCart updates
4. **Error handling**: Implement proper error handling in custom filters

### User Experience
1. **Progressive enhancement**: Ensure functionality works without JavaScript
2. **Accessibility**: Make custom filter output accessible to screen readers
3. **Loading states**: Provide feedback during filter operations
4. **Fallback content**: Always provide fallback for failed filter operations

### Security
1. **Validate inputs**: Always validate data passed to custom filters
2. **Escape output**: Ensure custom filter output is properly escaped
3. **Sanitize data**: Clean user-provided data before processing
4. **Rate limiting**: Implement rate limiting for expensive operations
