# String Filters

String filters manipulate and format text strings. CloudCart Liquid includes all standard Shopify string filters plus additional enhancements.

## Text Transformation

### capitalize
Capitalizes the first letter of each word in a string.

```liquid
{{ 'hello world' | capitalize }}
<!-- Output: Hello World -->

{{ product.title | capitalize }}
<!-- Output: Awesome T-Shirt -->
```

### downcase
Converts all characters to lowercase.

```liquid
{{ 'HELLO WORLD' | downcase }}
<!-- Output: hello world -->

{{ customer.email | downcase }}
<!-- Output: <EMAIL> -->
```

### upcase
Converts all characters to uppercase.

```liquid
{{ 'hello world' | upcase }}
<!-- Output: HELLO WORLD -->

{{ product.vendor | upcase }}
<!-- Output: NIKE -->
```

## String Manipulation

### append
Adds text to the end of a string.

```liquid
{{ 'Hello' | append: ' World' }}
<!-- Output: Hello World -->

{{ product.handle | append: '.html' }}
<!-- Output: awesome-product.html -->

{{ 'Price: ' | append: product.price | append: ' USD' }}
<!-- Output: Price: 29.99 USD -->
```

### prepend
Adds text to the beginning of a string.

```liquid
{{ 'World' | prepend: 'Hello ' }}
<!-- Output: Hello World -->

{{ product.title | prepend: 'Product: ' }}
<!-- Output: Product: Awesome T-Shirt -->
```

### remove
Removes all occurrences of a substring.

```liquid
{{ 'Hello World Hello' | remove: 'Hello' }}
<!-- Output:  World  -->

{{ product.title | remove: 'Sale - ' }}
<!-- Output: Awesome T-Shirt -->
```

### remove_first
Removes only the first occurrence of a substring.

```liquid
{{ 'Hello World Hello' | remove_first: 'Hello' }}
<!-- Output:  World Hello -->

{{ product.tags | join: ', ' | remove_first: 'featured, ' }}
<!-- Output: sale, new, trending -->
```

### replace
Replaces all occurrences of a substring with another string.

```liquid
{{ 'Hello World' | replace: 'World', 'Universe' }}
<!-- Output: Hello Universe -->

{{ product.description | replace: '\n', '<br>' }}
<!-- Converts line breaks to HTML -->
```

### replace_first
Replaces only the first occurrence of a substring.

```liquid
{{ 'Hello World Hello' | replace_first: 'Hello', 'Hi' }}
<!-- Output: Hi World Hello -->

{{ product.title | replace_first: 'Sale', 'Special Offer' }}
<!-- Output: Special Offer - Awesome T-Shirt -->
```

## String Extraction

### slice
Extracts a substring starting at a specified position.

```liquid
{{ 'Hello World' | slice: 0, 5 }}
<!-- Output: Hello -->

{{ 'Hello World' | slice: 6 }}
<!-- Output: World -->

{{ product.handle | slice: 0, 10 }}
<!-- Output: awesome-pr -->

{% comment %} Negative indices work from the end {% endcomment %}
{{ 'Hello World' | slice: -5 }}
<!-- Output: World -->
```

### truncate
Shortens a string to a specified length and adds an ellipsis.

```liquid
{{ 'This is a very long product description' | truncate: 20 }}
<!-- Output: This is a very lo... -->

{{ product.description | truncate: 100, '...' }}
<!-- Custom ellipsis -->

{{ article.title | truncate: 50, '' }}
<!-- No ellipsis -->
```

### truncatewords
Shortens a string to a specified number of words.

```liquid
{{ 'This is a very long product description with many words' | truncatewords: 5 }}
<!-- Output: This is a very long... -->

{{ product.description | truncatewords: 10, ' [Read More]' }}
<!-- Custom ending -->
```

## String Cleaning

### strip
Removes whitespace from both ends of a string.

```liquid
{{ '  Hello World  ' | strip }}
<!-- Output: Hello World -->

{{ product.title | strip }}
<!-- Removes any leading/trailing spaces -->
```

### lstrip
Removes whitespace from the left (beginning) of a string.

```liquid
{{ '  Hello World  ' | lstrip }}
<!-- Output: Hello World   -->
```

### rstrip
Removes whitespace from the right (end) of a string.

```liquid
{{ '  Hello World  ' | rstrip }}
<!-- Output:   Hello World -->
```

### strip_html
Removes all HTML tags from a string.

```liquid
{{ '<p>Hello <strong>World</strong></p>' | strip_html }}
<!-- Output: Hello World -->

{{ product.description | strip_html | truncate: 100 }}
<!-- Clean description for meta tags -->
```

### strip_newlines
Removes all line breaks from a string.

```liquid
{{ "Hello\nWorld\nHow are you?" | strip_newlines }}
<!-- Output: HelloWorldHow are you? -->

{{ product.description | strip_newlines | strip_html }}
<!-- Clean single-line description -->
```

## HTML and URL Handling

### escape
Escapes HTML characters to prevent XSS attacks.

```liquid
{{ '<script>alert("xss")</script>' | escape }}
<!-- Output: &lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt; -->

{{ customer.name | escape }}
<!-- Safe output of user data -->
```

### escape_once
Escapes HTML characters, but doesn't double-escape already escaped characters.

```liquid
{{ '&lt;p&gt;Hello &amp; goodbye&lt;/p&gt;' | escape_once }}
<!-- Output: &lt;p&gt;Hello &amp; goodbye&lt;/p&gt; -->
```

### newline_to_br
Converts line breaks to HTML `<br>` tags.

```liquid
{{ "Hello\nWorld" | newline_to_br }}
<!-- Output: Hello<br>World -->

{{ product.description | newline_to_br }}
<!-- Convert text line breaks to HTML -->
```

### url_encode
Encodes a string for use in URLs.

```liquid
{{ 'hello world' | url_encode }}
<!-- Output: hello%20world -->

{{ search.terms | url_encode }}
<!-- Safe search parameter -->
```

### url_decode
Decodes a URL-encoded string.

```liquid
{{ 'hello%20world' | url_decode }}
<!-- Output: hello world -->
```

## String Analysis

### size
Returns the length of a string.

```liquid
{{ 'Hello World' | size }}
<!-- Output: 11 -->

{% if product.title.size > 50 %}
  {{ product.title | truncate: 50 }}
{% else %}
  {{ product.title }}
{% endif %}
```

## String Splitting and Joining

### split
Splits a string into an array based on a delimiter.

```liquid
{% assign words = 'apple,banana,cherry' | split: ',' %}
{% for word in words %}
  <span>{{ word }}</span>
{% endfor %}
<!-- Output: <span>apple</span><span>banana</span><span>cherry</span> -->

{% assign product_tags = product.tags | join: ',' | split: ',' %}
```

## Encoding and Hashing

### base64_encode
Encodes a string to Base64.

```liquid
{{ 'Hello World' | base64_encode }}
<!-- Output: SGVsbG8gV29ybGQ= -->

{{ customer.email | base64_encode }}
<!-- Encoded email for API calls -->
```

### base64_decode
Decodes a Base64 string.

```liquid
{{ 'SGVsbG8gV29ybGQ=' | base64_decode }}
<!-- Output: Hello World -->
```

### md5
Generates an MD5 hash of a string.

```liquid
{{ 'Hello World' | md5 }}
<!-- Output: b10a8db164e0754105b7a99be72e3fe5 -->

{{ customer.email | md5 }}
<!-- Generate unique identifier -->
```

### sha1
Generates a SHA1 hash of a string.

```liquid
{{ 'Hello World' | sha1 }}
<!-- Output: 0a4d55a8d778e5022fab701977c5d840bbc486d0 -->
```

### sha256
Generates a SHA256 hash of a string.

```liquid
{{ 'Hello World' | sha256 }}
<!-- Output: a591a6d40bf420404a011733cfb7b190d62c65bf0bcda32b57b277d9ad9f146e -->
```

## Advanced String Operations

### camelcase
Converts a string to camelCase.

```liquid
{{ 'hello world' | camelcase }}
<!-- Output: helloWorld -->

{{ 'product-title' | camelcase }}
<!-- Output: productTitle -->
```

### handleize
Converts a string to a handle (URL-friendly format).

```liquid
{{ 'Hello World!' | handleize }}
<!-- Output: hello-world -->

{{ product.title | handleize }}
<!-- Create URL-friendly version -->
```

### pluralize
Returns singular or plural form based on a number.

```liquid
{{ cart.item_count | pluralize: 'item', 'items' }}
<!-- Output: "1 item" or "5 items" -->

{{ collection.products_count | pluralize: 'product', 'products' }}
```

## Practical Examples

### Product Title Formatting
```liquid
{% comment %} Clean and format product title {% endcomment %}
{% assign clean_title = product.title | strip | escape %}
{% if clean_title.size > 60 %}
  {% assign display_title = clean_title | truncate: 60 %}
{% else %}
  {% assign display_title = clean_title %}
{% endif %}

<h1 class="product-title">{{ display_title }}</h1>
```

### Search Highlighting
```liquid
{% comment %} Highlight search terms in results {% endcomment %}
{% if search.terms %}
  {% assign highlighted_title = product.title | replace: search.terms, '<mark>' | append: search.terms | append: '</mark>' %}
  <h3>{{ highlighted_title }}</h3>
{% else %}
  <h3>{{ product.title }}</h3>
{% endif %}
```

### URL Generation
```liquid
{% comment %} Create SEO-friendly URLs {% endcomment %}
{% assign product_url = '/products/' | append: product.handle %}
{% assign collection_url = '/collections/' | append: collection.handle %}
{% assign blog_url = '/blogs/' | append: blog.handle %}

<a href="{{ product_url }}">{{ product.title }}</a>
```

### Content Sanitization
```liquid
{% comment %} Safe user-generated content {% endcomment %}
{% assign safe_content = user_content | strip_html | escape | truncate: 500 %}
{% assign safe_content = safe_content | newline_to_br %}

<div class="user-content">{{ safe_content }}</div>
```

### Dynamic CSS Classes
```liquid
{% comment %} Generate CSS classes from product properties {% endcomment %}
{% assign css_classes = 'product' %}
{% assign css_classes = css_classes | append: ' ' | append: product.type | handleize %}
{% assign css_classes = css_classes | append: ' ' | append: product.vendor | handleize %}
{% if product.available %}
  {% assign css_classes = css_classes | append: ' available' %}
{% else %}
  {% assign css_classes = css_classes | append: ' sold-out' %}
{% endif %}

<div class="{{ css_classes }}">
  <!-- product content -->
</div>
```

### Text Processing Pipeline
```liquid
{% comment %} Complete text processing example {% endcomment %}
{% assign processed_text = article.content %}
{% assign processed_text = processed_text | strip_html %}
{% assign processed_text = processed_text | strip %}
{% assign processed_text = processed_text | truncatewords: 50 %}
{% assign processed_text = processed_text | escape %}

<meta name="description" content="{{ processed_text }}">
```

## Performance Tips

1. **Chain filters efficiently**: `{{ text | strip | downcase | truncate: 50 }}`
2. **Cache processed strings**: Use `assign` for repeated operations
3. **Avoid unnecessary escaping**: Only escape when outputting to HTML
4. **Use appropriate truncation**: `truncatewords` for content, `truncate` for titles

## Security Best Practices

1. **Always escape user input**: `{{ user_input | escape }}`
2. **Sanitize HTML content**: `{{ content | strip_html | escape }}`
3. **URL encode parameters**: `{{ param | url_encode }}`
4. **Use escape_once for pre-escaped content**: Prevents double-escaping
