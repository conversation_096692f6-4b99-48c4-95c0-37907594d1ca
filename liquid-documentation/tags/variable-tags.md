# Variable Tags

Variable tags allow you to create, modify, and manage variables within your Liquid templates.

## assign

Creates a new variable or assigns a new value to an existing variable.

### Basic Syntax
```liquid
{% assign variable_name = value %}
```

### Simple Examples
```liquid
{% assign product_title = product.title %}
{% assign sale_price = product.price | times: 0.8 %}
{% assign is_on_sale = product.compare_at_price > product.price %}
```

### String Assignment
```liquid
{% assign greeting = 'Hello, World!' %}
{% assign full_name = customer.first_name | append: ' ' | append: customer.last_name %}
{% assign product_url = '/products/' | append: product.handle %}
```

### Number Assignment
```liquid
{% assign discount_percent = 20 %}
{% assign total_items = cart.item_count %}
{% assign average_price = collection.products_count | divided_by: collection.products.size %}
```

### Boolean Assignment
```liquid
{% assign is_customer = false %}
{% if customer %}
  {% assign is_customer = true %}
{% endif %}

{% assign show_sale_badge = product.compare_at_price > product.price %}
```

### Array Assignment
```liquid
{% assign featured_products = collections.featured.products %}
{% assign product_tags = product.tags | split: ',' %}
{% assign sorted_products = collection.products | sort: 'price' %}
```

### Object Assignment
```liquid
{% assign current_variant = product.selected_or_first_available_variant %}
{% assign blog_article = blog.articles.first %}
{% assign customer_address = customer.default_address %}
```

### Complex Assignments
```liquid
{% comment %} Calculate discount amount {% endcomment %}
{% assign original_price = product.compare_at_price | default: product.price %}
{% assign discount_amount = original_price | minus: product.price %}
{% assign discount_percentage = discount_amount | times: 100 | divided_by: original_price %}

{% comment %} Create formatted strings {% endcomment %}
{% assign sale_text = 'Save ' | append: discount_percentage | append: '%' %}
{% assign price_range = product.price_min | money | append: ' - ' | append: product.price_max | money %}
```

## capture

Captures the output of a block of code and assigns it to a variable.

### Basic Syntax
```liquid
{% capture variable_name %}
  <!-- content to capture -->
{% endcapture %}
```

### Simple Examples
```liquid
{% capture product_info %}
  {{ product.title }} - {{ product.price | money }}
{% endcapture %}

{% capture sale_badge %}
  <span class="sale">Save {{ product.compare_at_price | minus: product.price | money }}!</span>
{% endcapture %}
```

### HTML Capture
```liquid
{% capture product_card %}
  <div class="product-card">
    <img src="{{ product.featured_image | img_url: '300x300' }}" alt="{{ product.title }}">
    <h3>{{ product.title }}</h3>
    <p class="price">{{ product.price | money }}</p>
    {% if product.available %}
      <button class="btn-primary">Add to Cart</button>
    {% else %}
      <button class="btn-disabled" disabled>Sold Out</button>
    {% endif %}
  </div>
{% endcapture %}

{{ product_card }}
```

### Dynamic Content Capture
```liquid
{% capture breadcrumbs %}
  <nav class="breadcrumbs">
    <a href="/">Home</a>
    {% if collection %}
      <span class="separator">/</span>
      <a href="{{ collection.url }}">{{ collection.title }}</a>
    {% endif %}
    {% if product %}
      <span class="separator">/</span>
      <span class="current">{{ product.title }}</span>
    {% endif %}
  </nav>
{% endcapture %}
```

### Loop Content Capture
```liquid
{% capture featured_products_html %}
  {% for product in collections.featured.products limit: 4 %}
    <div class="featured-item">
      <h4>{{ product.title }}</h4>
      <p>{{ product.price | money }}</p>
    </div>
  {% endfor %}
{% endcapture %}

<section class="featured-section">
  <h2>Featured Products</h2>
  {{ featured_products_html }}
</section>
```

### Conditional Capture
```liquid
{% capture customer_greeting %}
  {% if customer %}
    Welcome back, {{ customer.first_name }}!
    {% if customer.orders_count > 0 %}
      You have {{ customer.orders_count }} previous orders.
    {% endif %}
  {% else %}
    Welcome! Please <a href="/account/login">sign in</a> or <a href="/account/register">create an account</a>.
  {% endif %}
{% endcapture %}

<div class="customer-message">{{ customer_greeting }}</div>
```

## increment

Creates a new variable with an initial value of 0, then increases its value by 1 each time it's called.

### Basic Syntax
```liquid
{% increment variable_name %}
```

### Simple Example
```liquid
{% for product in collection.products %}
  <div class="product-{{ increment counter }}">
    {{ product.title }}
  </div>
{% endfor %}
```
Output:
```html
<div class="product-1">Product A</div>
<div class="product-2">Product B</div>
<div class="product-3">Product C</div>
```

### Multiple Counters
```liquid
{% for product in collection.products %}
  <div class="row-{{ increment row_counter }} col-{{ increment col_counter }}">
    {{ product.title }}
    {% if col_counter == 3 %}
      {% assign col_counter = 0 %}
    {% endif %}
  </div>
{% endfor %}
```

### Practical Use Cases
```liquid
{% comment %} Generate unique IDs {% endcomment %}
{% for variant in product.variants %}
  <input type="radio" id="variant-{{ increment variant_id }}" name="id" value="{{ variant.id }}">
  <label for="variant-{{ variant_id }}">{{ variant.title }}</label>
{% endfor %}

{% comment %} Create alternating layouts {% endcomment %}
{% for article in blog.articles %}
  {% if increment article_counter is odd %}
    <article class="article-left">{{ article.title }}</article>
  {% else %}
    <article class="article-right">{{ article.title }}</article>
  {% endif %}
{% endfor %}
```

## decrement

Creates a new variable with an initial value of -1, then decreases its value by 1 each time it's called.

### Basic Syntax
```liquid
{% decrement variable_name %}
```

### Simple Example
```liquid
{% for product in collection.products %}
  <div class="product-{{ decrement countdown }}">
    {{ product.title }}
  </div>
{% endfor %}
```
Output:
```html
<div class="product--1">Product A</div>
<div class="product--2">Product B</div>
<div class="product--3">Product C</div>
```

### Countdown Example
```liquid
{% assign total_products = collection.products.size %}
{% for product in collection.products %}
  <div class="product">
    <span class="position">{{ total_products | plus: decrement position }}</span>
    <h3>{{ product.title }}</h3>
  </div>
{% endfor %}
```

## Variable Scope and Best Practices

### Variable Scope
Variables created with `assign` and `capture` are available throughout the template and in included templates. Variables created with `increment` and `decrement` are global and persist across includes.

```liquid
{% comment %} Global variable {% endcomment %}
{% assign global_message = 'This is available everywhere' %}

{% comment %} In a loop {% endcomment %}
{% for product in collection.products %}
  {% assign loop_variable = product.title %}
  {% comment %} loop_variable is available outside the loop {% endcomment %}
{% endfor %}

{% comment %} loop_variable is still available here {% endcomment %}
{{ loop_variable }}
```

### Performance Considerations
```liquid
{% comment %} Good: Calculate once, use multiple times {% endcomment %}
{% assign discounted_price = product.price | times: 0.8 %}
{% assign savings = product.price | minus: discounted_price %}

<p>Sale Price: {{ discounted_price | money }}</p>
<p>You Save: {{ savings | money }}</p>

{% comment %} Avoid: Recalculating the same value {% endcomment %}
<p>Sale Price: {{ product.price | times: 0.8 | money }}</p>
<p>You Save: {{ product.price | minus: product.price | times: 0.8 | money }}</p>
```

### Naming Conventions
```liquid
{% comment %} Use descriptive names {% endcomment %}
{% assign is_product_available = product.available %}
{% assign formatted_price = product.price | money %}
{% assign customer_full_name = customer.first_name | append: ' ' | append: customer.last_name %}

{% comment %} Use consistent prefixes for related variables {% endcomment %}
{% assign product_title = product.title %}
{% assign product_price = product.price %}
{% assign product_vendor = product.vendor %}

{% comment %} Use boolean prefixes for clarity {% endcomment %}
{% assign is_on_sale = product.compare_at_price > product.price %}
{% assign has_variants = product.variants.size > 1 %}
{% assign can_add_to_cart = product.available and product.price > 0 %}
```

### Complex Variable Management
```liquid
{% comment %} Product analysis {% endcomment %}
{% assign product_analysis = '' %}
{% capture product_analysis %}
  {% assign is_new = product.created_at > 'now' | date: '%s' | minus: 2592000 %}
  {% assign is_popular = product.tags contains 'popular' %}
  {% assign is_discounted = product.compare_at_price > product.price %}
  
  {% if is_new %}new {% endif %}
  {% if is_popular %}popular {% endif %}
  {% if is_discounted %}sale {% endif %}
{% endcapture %}

<div class="product {{ product_analysis | strip }}">
  <!-- product content -->
</div>
```

### Error Prevention
```liquid
{% comment %} Safe variable assignment with defaults {% endcomment %}
{% assign product_title = product.title | default: 'Untitled Product' %}
{% assign product_price = product.price | default: 0 %}
{% assign product_image = product.featured_image | default: 'placeholder.jpg' %}

{% comment %} Type checking {% endcomment %}
{% if product.price and product.price > 0 %}
  {% assign formatted_price = product.price | money %}
{% else %}
  {% assign formatted_price = 'Price not available' %}
{% endif %}

{% comment %} Array safety {% endcomment %}
{% if product.images.size > 0 %}
  {% assign first_image = product.images.first %}
{% else %}
  {% assign first_image = 'no-image.jpg' %}
{% endif %}
```

### Advanced Patterns
```liquid
{% comment %} Dynamic class generation {% endcomment %}
{% assign product_classes = 'product' %}
{% if product.available %}
  {% assign product_classes = product_classes | append: ' available' %}
{% else %}
  {% assign product_classes = product_classes | append: ' sold-out' %}
{% endif %}
{% if product.tags contains 'featured' %}
  {% assign product_classes = product_classes | append: ' featured' %}
{% endif %}

<div class="{{ product_classes }}">
  <!-- product content -->
</div>

{% comment %} Configuration object {% endcomment %}
{% assign config = settings %}
{% assign show_vendor = config.show_vendor | default: true %}
{% assign show_reviews = config.show_reviews | default: false %}
{% assign max_products = config.max_products | default: 12 %}

{% comment %} Data transformation {% endcomment %}
{% assign product_data = '' %}
{% capture product_data %}
  {
    "id": {{ product.id }},
    "title": "{{ product.title | escape }}",
    "price": {{ product.price }},
    "available": {{ product.available }},
    "tags": [{% for tag in product.tags %}"{{ tag }}"{% unless forloop.last %},{% endunless %}{% endfor %}]
  }
{% endcapture %}

<script>
  window.productData = {{ product_data }};
</script>
```
