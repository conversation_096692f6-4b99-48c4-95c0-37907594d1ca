# Integration Tags

CloudCart provides specialized integration tags for third-party services, compliance features, and social media integration.

## gdpr

Handles GDPR compliance and cookie consent management.

### Basic Syntax
```liquid
{% gdpr 'policy_type' %}
```

### <PERSON>ie Consent Banner
```liquid
{% gdpr 'cookie_banner' %}
  <div class="gdpr-banner" id="cookie-consent-banner">
    <div class="gdpr-content">
      <h4>{{ 'gdpr.cookie_banner.title' | t }}</h4>
      <p>{{ 'gdpr.cookie_banner.message' | t }}</p>
      
      <div class="gdpr-actions">
        <button class="btn btn-primary" onclick="acceptAllCookies()">
          {{ 'gdpr.cookie_banner.accept_all' | t }}
        </button>
        <button class="btn btn-secondary" onclick="showCookieSettings()">
          {{ 'gdpr.cookie_banner.customize' | t }}
        </button>
        <button class="btn btn-outline" onclick="rejectAllCookies()">
          {{ 'gdpr.cookie_banner.reject_all' | t }}
        </button>
      </div>
    </div>
  </div>
{% endgdpr %}
```

### Cookie Settings Modal
```liquid
{% gdpr 'cookie_settings' %}
  <div class="gdpr-modal" id="cookie-settings-modal">
    <div class="gdpr-modal-content">
      <div class="gdpr-modal-header">
        <h3>{{ 'gdpr.cookie_settings.title' | t }}</h3>
        <button class="close-modal" onclick="closeCookieSettings()">&times;</button>
      </div>
      
      <div class="gdpr-modal-body">
        <p>{{ 'gdpr.cookie_settings.description' | t }}</p>
        
        <div class="cookie-category">
          <div class="category-header">
            <h4>{{ 'gdpr.categories.necessary.title' | t }}</h4>
            <label class="toggle-switch">
              <input type="checkbox" checked disabled>
              <span class="slider"></span>
            </label>
          </div>
          <p>{{ 'gdpr.categories.necessary.description' | t }}</p>
        </div>
        
        <div class="cookie-category">
          <div class="category-header">
            <h4>{{ 'gdpr.categories.analytics.title' | t }}</h4>
            <label class="toggle-switch">
              <input type="checkbox" id="analytics-cookies" name="analytics">
              <span class="slider"></span>
            </label>
          </div>
          <p>{{ 'gdpr.categories.analytics.description' | t }}</p>
        </div>
        
        <div class="cookie-category">
          <div class="category-header">
            <h4>{{ 'gdpr.categories.marketing.title' | t }}</h4>
            <label class="toggle-switch">
              <input type="checkbox" id="marketing-cookies" name="marketing">
              <span class="slider"></span>
            </label>
          </div>
          <p>{{ 'gdpr.categories.marketing.description' | t }}</p>
        </div>
      </div>
      
      <div class="gdpr-modal-footer">
        <button class="btn btn-primary" onclick="saveCookiePreferences()">
          {{ 'gdpr.cookie_settings.save' | t }}
        </button>
        <button class="btn btn-secondary" onclick="closeCookieSettings()">
          {{ 'gdpr.cookie_settings.cancel' | t }}
        </button>
      </div>
    </div>
  </div>
{% endgdpr %}
```

### Privacy Policy Links
```liquid
{% gdpr 'privacy_links' %}
  <div class="privacy-links">
    <a href="/pages/privacy-policy">{{ 'gdpr.links.privacy_policy' | t }}</a>
    <a href="/pages/cookie-policy">{{ 'gdpr.links.cookie_policy' | t }}</a>
    <a href="/pages/terms-of-service">{{ 'gdpr.links.terms' | t }}</a>
    <button onclick="showCookieSettings()">{{ 'gdpr.links.cookie_settings' | t }}</button>
  </div>
{% endgdpr %}
```

## recaptcha

Integrates Google reCAPTCHA for form protection.

### Basic Syntax
```liquid
{% recaptcha 'site_key' %}
```

### Contact Form with reCAPTCHA
```liquid
<form action="/contact" method="post" class="contact-form">
  <div class="form-group">
    <label for="contact_name">Name *</label>
    <input type="text" id="contact_name" name="contact[name]" required>
  </div>
  
  <div class="form-group">
    <label for="contact_email">Email *</label>
    <input type="email" id="contact_email" name="contact[email]" required>
  </div>
  
  <div class="form-group">
    <label for="contact_message">Message *</label>
    <textarea id="contact_message" name="contact[message]" required></textarea>
  </div>
  
  <div class="form-group">
    {% recaptcha settings.recaptcha_site_key %}
      <div class="g-recaptcha" data-sitekey="{{ settings.recaptcha_site_key }}"></div>
    {% endrecaptcha %}
  </div>
  
  <div class="form-group">
    <button type="submit" class="btn btn-primary">Send Message</button>
  </div>
</form>

<script src="https://www.google.com/recaptcha/api.js" async defer></script>
```

### Newsletter Signup with reCAPTCHA
```liquid
<form action="/newsletter" method="post" class="newsletter-form">
  <div class="newsletter-input-group">
    <input type="email" 
           name="email" 
           placeholder="Enter your email address" 
           required>
    
    {% recaptcha settings.recaptcha_site_key, size: 'compact' %}
      <div class="g-recaptcha" 
           data-sitekey="{{ settings.recaptcha_site_key }}" 
           data-size="compact"></div>
    {% endrecaptcha %}
    
    <button type="submit" class="btn btn-primary">Subscribe</button>
  </div>
</form>
```

### Invisible reCAPTCHA
```liquid
<form action="/contact" method="post" id="contact-form">
  <!-- Form fields -->
  
  {% recaptcha settings.recaptcha_site_key, invisible: true %}
    <button class="g-recaptcha btn btn-primary" 
            data-sitekey="{{ settings.recaptcha_site_key }}" 
            data-callback="onSubmit" 
            data-action="submit">
      Send Message
    </button>
  {% endrecaptcha %}
</form>

<script>
  function onSubmit(token) {
    document.getElementById("contact-form").submit();
  }
</script>
```

## instagram

Displays Instagram feed integration.

### Basic Syntax
```liquid
{% instagram 'access_token' %}
```

### Instagram Feed Display
```liquid
{% instagram settings.instagram_access_token %}
  <div class="instagram-feed">
    <div class="instagram-header">
      <h3>{{ 'social.instagram.follow_us' | t }}</h3>
      <a href="https://instagram.com/{{ settings.instagram_handle }}" 
         target="_blank" 
         class="instagram-link">
        @{{ settings.instagram_handle }}
      </a>
    </div>
    
    <div class="instagram-grid" id="instagram-posts">
      <!-- Posts will be loaded here -->
    </div>
    
    <div class="instagram-footer">
      <a href="https://instagram.com/{{ settings.instagram_handle }}" 
         target="_blank" 
         class="btn btn-outline">
        {{ 'social.instagram.view_more' | t }}
      </a>
    </div>
  </div>
{% endinstagram %}

<script>
  // Load Instagram posts
  fetch('/api/instagram/posts?token={{ settings.instagram_access_token | url_encode }}')
    .then(response => response.json())
    .then(data => {
      const container = document.getElementById('instagram-posts');
      
      data.posts.slice(0, 6).forEach(post => {
        const postElement = document.createElement('div');
        postElement.className = 'instagram-post';
        postElement.innerHTML = `
          <a href="${post.permalink}" target="_blank">
            <img src="${post.media_url}" alt="${post.caption || 'Instagram post'}">
            <div class="post-overlay">
              <div class="post-stats">
                <span class="likes">❤️ ${post.like_count}</span>
                <span class="comments">💬 ${post.comments_count}</span>
              </div>
            </div>
          </a>
        `;
        container.appendChild(postElement);
      });
    })
    .catch(error => {
      console.error('Error loading Instagram posts:', error);
    });
</script>
```

### Instagram Stories Integration
```liquid
{% instagram settings.instagram_access_token, type: 'stories' %}
  <div class="instagram-stories">
    <h4>{{ 'social.instagram.stories' | t }}</h4>
    <div class="stories-container" id="instagram-stories">
      <!-- Stories will be loaded here -->
    </div>
  </div>
{% endinstagram %}
```

## socials

Manages social media links and sharing functionality.

### Basic Syntax
```liquid
{% socials 'platform' %}
```

### Social Media Links
```liquid
{% socials 'links' %}
  <div class="social-links">
    {% if settings.facebook_url %}
      <a href="{{ settings.facebook_url }}" 
         target="_blank" 
         aria-label="Follow us on Facebook">
        <i class="fab fa-facebook-f"></i>
      </a>
    {% endif %}
    
    {% if settings.twitter_url %}
      <a href="{{ settings.twitter_url }}" 
         target="_blank" 
         aria-label="Follow us on Twitter">
        <i class="fab fa-twitter"></i>
      </a>
    {% endif %}
    
    {% if settings.instagram_url %}
      <a href="{{ settings.instagram_url }}" 
         target="_blank" 
         aria-label="Follow us on Instagram">
        <i class="fab fa-instagram"></i>
      </a>
    {% endif %}
    
    {% if settings.youtube_url %}
      <a href="{{ settings.youtube_url }}" 
         target="_blank" 
         aria-label="Subscribe to our YouTube channel">
        <i class="fab fa-youtube"></i>
      </a>
    {% endif %}
    
    {% if settings.linkedin_url %}
      <a href="{{ settings.linkedin_url }}" 
         target="_blank" 
         aria-label="Connect with us on LinkedIn">
        <i class="fab fa-linkedin-in"></i>
      </a>
    {% endif %}
  </div>
{% endsocials %}
```

### Social Sharing Buttons
```liquid
{% socials 'share' %}
  <div class="social-share">
    <h4>{{ 'social.share.title' | t }}</h4>
    
    {% assign share_url = shop.url | append: request.path | url_encode %}
    {% assign share_title = page_title | url_encode %}
    {% assign share_description = page_description | default: shop.description | url_encode %}
    
    <div class="share-buttons">
      <a href="https://www.facebook.com/sharer/sharer.php?u={{ share_url }}" 
         target="_blank" 
         class="share-button facebook">
        <i class="fab fa-facebook-f"></i>
        {{ 'social.share.facebook' | t }}
      </a>
      
      <a href="https://twitter.com/intent/tweet?url={{ share_url }}&text={{ share_title }}" 
         target="_blank" 
         class="share-button twitter">
        <i class="fab fa-twitter"></i>
        {{ 'social.share.twitter' | t }}
      </a>
      
      <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ share_url }}" 
         target="_blank" 
         class="share-button linkedin">
        <i class="fab fa-linkedin-in"></i>
        {{ 'social.share.linkedin' | t }}
      </a>
      
      <a href="https://pinterest.com/pin/create/button/?url={{ share_url }}&description={{ share_title }}" 
         target="_blank" 
         class="share-button pinterest">
        <i class="fab fa-pinterest"></i>
        {{ 'social.share.pinterest' | t }}
      </a>
      
      <button onclick="copyToClipboard('{{ shop.url | append: request.path }}')" 
              class="share-button copy-link">
        <i class="fas fa-link"></i>
        {{ 'social.share.copy_link' | t }}
      </button>
    </div>
  </div>
{% endsocials %}

<script>
  function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
      alert('{{ "social.share.link_copied" | t }}');
    });
  }
</script>
```

### Product Social Sharing
```liquid
{% if template contains 'product' %}
  {% socials 'product_share' %}
    <div class="product-social-share">
      <h4>{{ 'product.share.title' | t }}</h4>
      
      {% assign product_url = shop.url | append: product.url | url_encode %}
      {% assign product_title = product.title | url_encode %}
      {% assign product_image = product.featured_image | img_url: '1200x630' | url_encode %}
      
      <div class="share-buttons">
        <a href="https://www.facebook.com/sharer/sharer.php?u={{ product_url }}" 
           target="_blank" 
           class="share-button facebook">
          <i class="fab fa-facebook-f"></i>
        </a>
        
        <a href="https://twitter.com/intent/tweet?url={{ product_url }}&text=Check%20out%20{{ product_title }}" 
           target="_blank" 
           class="share-button twitter">
          <i class="fab fa-twitter"></i>
        </a>
        
        <a href="https://pinterest.com/pin/create/button/?url={{ product_url }}&media={{ product_image }}&description={{ product_title }}" 
           target="_blank" 
           class="share-button pinterest">
          <i class="fab fa-pinterest"></i>
        </a>
        
        <a href="mailto:?subject={{ product_title }}&body=Check%20out%20this%20product:%20{{ product_url }}" 
           class="share-button email">
          <i class="fas fa-envelope"></i>
        </a>
      </div>
    </div>
  {% endsocials %}
{% endif %}
```

## Advanced Integration Patterns

### Multi-Service Integration
```liquid
<div class="integrations-section">
  {% comment %} GDPR Compliance {% endcomment %}
  {% gdpr 'cookie_banner' %}
    <!-- Cookie consent banner -->
  {% endgdpr %}
  
  {% comment %} Social Media Feed {% endcomment %}
  {% if settings.show_instagram_feed %}
    {% instagram settings.instagram_access_token %}
      <!-- Instagram feed -->
    {% endinstagram %}
  {% endif %}
  
  {% comment %} Contact Form with Protection {% endcomment %}
  <form action="/contact" method="post">
    <!-- Form fields -->
    
    {% recaptcha settings.recaptcha_site_key %}
      <!-- reCAPTCHA widget -->
    {% endrecaptcha %}
    
    <div class="form-footer">
      <p>
        {{ 'contact.form.privacy_notice' | t }}
        <a href="/pages/privacy-policy">{{ 'gdpr.links.privacy_policy' | t }}</a>
      </p>
    </div>
  </form>
  
  {% comment %} Social Sharing {% endcomment %}
  {% socials 'share' %}
    <!-- Social sharing buttons -->
  {% endsocials %}
</div>
```

### Conditional Integration Loading
```liquid
{% comment %} Load integrations based on user consent {% endcomment %}
<script>
  // Check GDPR consent before loading tracking scripts
  if (getCookieConsent('analytics')) {
    // Load Google Analytics
    gtag('config', '{{ settings.google_analytics_id }}');
  }
  
  if (getCookieConsent('marketing')) {
    // Load Facebook Pixel
    fbq('init', '{{ settings.facebook_pixel_id }}');
    
    // Load Instagram integration
    loadInstagramFeed();
  }
  
  function getCookieConsent(category) {
    const consent = localStorage.getItem('cookie_consent');
    return consent && JSON.parse(consent)[category];
  }
</script>
```

## Best Practices

### Privacy and Compliance
1. **Always obtain consent**: Implement proper GDPR consent mechanisms
2. **Respect user choices**: Honor cookie preferences and opt-outs
3. **Provide transparency**: Clear information about data collection
4. **Regular updates**: Keep privacy policies and consent forms current

### Performance Considerations
1. **Lazy load social feeds**: Load social media content asynchronously
2. **Optimize API calls**: Cache social media data when possible
3. **Minimize third-party scripts**: Only load necessary integrations
4. **Use CDNs**: Leverage content delivery networks for external resources

### Security Guidelines
1. **Validate reCAPTCHA**: Always verify reCAPTCHA responses server-side
2. **Secure API keys**: Never expose sensitive credentials in client-side code
3. **Use HTTPS**: Ensure all integrations use secure connections
4. **Regular security audits**: Monitor and update integration security

### User Experience
1. **Progressive enhancement**: Ensure functionality works without JavaScript
2. **Clear messaging**: Provide helpful error messages and loading states
3. **Accessibility**: Ensure integrations are keyboard and screen reader accessible
4. **Mobile optimization**: Test integrations on various devices and screen sizes
