# Theme Tags

Theme tags control theme structure, sections, and styling, providing essential functionality for CloudCart theme development.

## section

Renders a theme section with its settings and blocks.

### Basic Syntax
```liquid
{% section 'section-name' %}
```

### Simple Section
```liquid
{% comment %} Render header section {% endcomment %}
{% section 'header' %}

{% comment %} Render footer section {% endcomment %}
{% section 'footer' %}

{% comment %} Render hero banner section {% endcomment %}
{% section 'hero-banner' %}
```

### Section with Settings
```liquid
{% comment %} sections/hero-banner.liquid {% endcomment %}
<section class="hero-banner" style="background-color: {{ section.settings.background_color }};">
  {% if section.settings.hero_image %}
    <div class="hero-image">
      <img src="{{ section.settings.hero_image | img_url: '1920x800' }}" 
           alt="{{ section.settings.hero_title }}">
    </div>
  {% endif %}
  
  <div class="hero-content">
    {% if section.settings.hero_title %}
      <h1 style="color: {{ section.settings.text_color }};">
        {{ section.settings.hero_title }}
      </h1>
    {% endif %}
    
    {% if section.settings.hero_subtitle %}
      <p>{{ section.settings.hero_subtitle }}</p>
    {% endif %}
    
    {% if section.settings.button_text and section.settings.button_url %}
      <a href="{{ section.settings.button_url }}" class="btn btn-primary">
        {{ section.settings.button_text }}
      </a>
    {% endif %}
  </div>
</section>

{% schema %}
{
  "name": "Hero Banner",
  "settings": [
    {
      "type": "image_picker",
      "id": "hero_image",
      "label": "Hero Image"
    },
    {
      "type": "text",
      "id": "hero_title",
      "label": "Hero Title",
      "default": "Welcome to Our Store"
    },
    {
      "type": "textarea",
      "id": "hero_subtitle",
      "label": "Hero Subtitle"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button URL"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#000000"
    }
  ]
}
{% endschema %}
```

### Section with Blocks
```liquid
{% comment %} sections/featured-products.liquid {% endcomment %}
<section class="featured-products">
  <div class="container">
    {% if section.settings.section_title %}
      <h2>{{ section.settings.section_title }}</h2>
    {% endif %}
    
    <div class="products-grid">
      {% for block in section.blocks %}
        {% case block.type %}
          {% when 'product' %}
            {% assign featured_product = all_products[block.settings.product] %}
            {% if featured_product %}
              <div class="product-item" {{ block.shopify_attributes }}>
                <a href="{{ featured_product.url }}">
                  <img src="{{ featured_product.featured_image | img_url: '300x300' }}" 
                       alt="{{ featured_product.title }}">
                  <h3>{{ featured_product.title }}</h3>
                  <p>{{ featured_product.price | money }}</p>
                </a>
              </div>
            {% endif %}
            
          {% when 'collection' %}
            {% assign featured_collection = collections[block.settings.collection] %}
            {% if featured_collection %}
              <div class="collection-item" {{ block.shopify_attributes }}>
                <a href="{{ featured_collection.url }}">
                  <img src="{{ featured_collection.image | img_url: '300x300' }}" 
                       alt="{{ featured_collection.title }}">
                  <h3>{{ featured_collection.title }}</h3>
                  <p>{{ featured_collection.products_count }} products</p>
                </a>
              </div>
            {% endif %}
        {% endcase %}
      {% endfor %}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Featured Products",
  "settings": [
    {
      "type": "text",
      "id": "section_title",
      "label": "Section Title",
      "default": "Featured Products"
    }
  ],
  "blocks": [
    {
      "type": "product",
      "name": "Product",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Select Product"
        }
      ]
    },
    {
      "type": "collection",
      "name": "Collection",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "Select Collection"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Featured Products",
      "blocks": [
        {
          "type": "product"
        },
        {
          "type": "product"
        },
        {
          "type": "product"
        }
      ]
    }
  ]
}
{% endschema %}
```

## sections

Renders multiple sections dynamically.

### Basic Syntax
```liquid
{% sections 'section-group' %}
```

### Dynamic Section Rendering
```liquid
{% comment %} Render all sections for homepage {% endcomment %}
{% for section in template.sections %}
  {% sections section %}
{% endfor %}

{% comment %} Render specific section group {% endcomment %}
{% sections 'homepage-sections' %}
```

## schema

Defines the settings schema for a section or block.

### Basic Schema Structure
```liquid
{% schema %}
{
  "name": "Section Name",
  "settings": [
    // Section settings
  ],
  "blocks": [
    // Block definitions
  ],
  "presets": [
    // Default configurations
  ]
}
{% endschema %}
```

### Complete Schema Example
```liquid
{% schema %}
{
  "name": "Product Grid",
  "class": "product-grid-section",
  "settings": [
    {
      "type": "text",
      "id": "section_title",
      "label": "Section Title",
      "default": "Our Products"
    },
    {
      "type": "range",
      "id": "products_per_row",
      "label": "Products per row",
      "min": 2,
      "max": 5,
      "step": 1,
      "default": 4
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show vendor",
      "default": true
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "Image ratio",
      "options": [
        {
          "value": "square",
          "label": "Square"
        },
        {
          "value": "portrait",
          "label": "Portrait"
        },
        {
          "value": "landscape",
          "label": "Landscape"
        }
      ],
      "default": "square"
    }
  ],
  "blocks": [
    {
      "type": "featured_product",
      "name": "Featured Product",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        },
        {
          "type": "text",
          "id": "custom_title",
          "label": "Custom Title"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Product Grid",
      "settings": {
        "section_title": "Featured Products",
        "products_per_row": 4
      }
    }
  ]
}
{% endschema %}
```

## style

Embeds CSS styles within a section.

### Basic Syntax
```liquid
{% style %}
  /* CSS styles */
{% endstyle %}
```

### Section-Specific Styles
```liquid
{% style %}
  .hero-section-{{ section.id }} {
    background-color: {{ section.settings.background_color }};
    color: {{ section.settings.text_color }};
    padding: {{ section.settings.padding_top }}px 0 {{ section.settings.padding_bottom }}px;
  }
  
  .hero-section-{{ section.id }} .hero-title {
    font-size: {{ section.settings.title_size }}px;
    font-weight: {{ section.settings.title_weight }};
  }
  
  @media (max-width: 768px) {
    .hero-section-{{ section.id }} {
      padding: {{ section.settings.mobile_padding }}px 0;
    }
    
    .hero-section-{{ section.id }} .hero-title {
      font-size: {{ section.settings.title_size | times: 0.8 }}px;
    }
  }
{% endstyle %}

<section class="hero-section hero-section-{{ section.id }}">
  <!-- Section content -->
</section>
```

### Dynamic Styles with Settings
```liquid
{% style %}
  .product-grid-{{ section.id }} {
    display: grid;
    grid-template-columns: repeat({{ section.settings.columns }}, 1fr);
    gap: {{ section.settings.grid_gap }}px;
  }
  
  {% for block in section.blocks %}
    .product-item-{{ block.id }} {
      {% if block.settings.custom_background %}
        background-color: {{ block.settings.background_color }};
      {% endif %}
      
      {% if block.settings.border_radius %}
        border-radius: {{ block.settings.border_radius }}px;
      {% endif %}
    }
  {% endfor %}
{% endstyle %}
```

## stylesheet

Links to external stylesheets or theme assets.

### Basic Syntax
```liquid
{% stylesheet 'filename' %}
```

### Theme Stylesheet
```liquid
{% comment %} Link to main theme stylesheet {% endcomment %}
{% stylesheet 'theme.css' %}

{% comment %} Link to section-specific stylesheet {% endcomment %}
{% stylesheet 'sections/hero-banner.css' %}

{% comment %} Conditional stylesheet loading {% endcomment %}
{% if template contains 'product' %}
  {% stylesheet 'product-page.css' %}
{% endif %}
```

### External Stylesheets
```liquid
{% comment %} Link to external CSS frameworks {% endcomment %}
{% stylesheet 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' %}

{% comment %} Google Fonts {% endcomment %}
{% stylesheet 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap' %}
```

### Conditional and Dynamic Stylesheets
```liquid
{% comment %} Load stylesheets based on settings {% endcomment %}
{% if settings.enable_animations %}
  {% stylesheet 'animations.css' %}
{% endif %}

{% if settings.theme_style == 'modern' %}
  {% stylesheet 'modern-theme.css' %}
{% elsif settings.theme_style == 'classic' %}
  {% stylesheet 'classic-theme.css' %}
{% endif %}

{% comment %} Load page-specific styles {% endcomment %}
{% case template %}
  {% when 'index' %}
    {% stylesheet 'homepage.css' %}
  {% when 'product' %}
    {% stylesheet 'product.css' %}
  {% when 'collection' %}
    {% stylesheet 'collection.css' %}
  {% when 'cart' %}
    {% stylesheet 'cart.css' %}
{% endcase %}
```

## Practical Examples

### Complete Section with All Features
```liquid
{% comment %} sections/testimonials.liquid {% endcomment %}

{% style %}
  .testimonials-{{ section.id }} {
    background: {{ section.settings.background_color }};
    padding: {{ section.settings.padding_top }}px 0 {{ section.settings.padding_bottom }}px;
  }
  
  .testimonials-{{ section.id }} .testimonial-item {
    text-align: {{ section.settings.text_alignment }};
    margin-bottom: 2rem;
  }
  
  .testimonials-{{ section.id }} .testimonial-text {
    font-size: {{ section.settings.text_size }}px;
    color: {{ section.settings.text_color }};
  }
  
  .testimonials-{{ section.id }} .testimonial-author {
    font-weight: bold;
    color: {{ section.settings.author_color }};
  }
{% endstyle %}

<section class="testimonials testimonials-{{ section.id }}">
  <div class="container">
    {% if section.settings.section_title %}
      <h2 class="section-title">{{ section.settings.section_title }}</h2>
    {% endif %}
    
    <div class="testimonials-grid">
      {% for block in section.blocks %}
        {% case block.type %}
          {% when 'testimonial' %}
            <div class="testimonial-item" {{ block.shopify_attributes }}>
              {% if block.settings.customer_photo %}
                <img src="{{ block.settings.customer_photo | img_url: '100x100' }}" 
                     alt="{{ block.settings.customer_name }}" 
                     class="customer-photo">
              {% endif %}
              
              <blockquote class="testimonial-text">
                "{{ block.settings.testimonial_text }}"
              </blockquote>
              
              <cite class="testimonial-author">
                {{ block.settings.customer_name }}
                {% if block.settings.customer_title %}
                  <span class="customer-title">{{ block.settings.customer_title }}</span>
                {% endif %}
              </cite>
              
              {% if block.settings.rating %}
                <div class="rating">
                  {% for i in (1..5) %}
                    {% if i <= block.settings.rating %}
                      <span class="star filled">★</span>
                    {% else %}
                      <span class="star empty">☆</span>
                    {% endif %}
                  {% endfor %}
                </div>
              {% endif %}
            </div>
        {% endcase %}
      {% endfor %}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Testimonials",
  "settings": [
    {
      "type": "text",
      "id": "section_title",
      "label": "Section Title",
      "default": "What Our Customers Say"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#f8f9fa"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "author_color",
      "label": "Author Color",
      "default": "#666666"
    },
    {
      "type": "range",
      "id": "text_size",
      "label": "Text Size",
      "min": 14,
      "max": 24,
      "step": 1,
      "default": 16
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text Alignment",
      "options": [
        {"value": "left", "label": "Left"},
        {"value": "center", "label": "Center"},
        {"value": "right", "label": "Right"}
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Padding Top",
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Padding Bottom",
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 50
    }
  ],
  "blocks": [
    {
      "type": "testimonial",
      "name": "Testimonial",
      "settings": [
        {
          "type": "textarea",
          "id": "testimonial_text",
          "label": "Testimonial Text"
        },
        {
          "type": "text",
          "id": "customer_name",
          "label": "Customer Name"
        },
        {
          "type": "text",
          "id": "customer_title",
          "label": "Customer Title"
        },
        {
          "type": "image_picker",
          "id": "customer_photo",
          "label": "Customer Photo"
        },
        {
          "type": "range",
          "id": "rating",
          "label": "Rating",
          "min": 1,
          "max": 5,
          "step": 1,
          "default": 5
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Testimonials",
      "blocks": [
        {
          "type": "testimonial",
          "settings": {
            "testimonial_text": "Amazing products and excellent customer service!",
            "customer_name": "John Doe",
            "rating": 5
          }
        },
        {
          "type": "testimonial",
          "settings": {
            "testimonial_text": "Fast shipping and high quality items.",
            "customer_name": "Jane Smith",
            "rating": 5
          }
        }
      ]
    }
  ]
}
{% endschema %}
```

## Best Practices

### Performance Optimization
1. **Minimize inline styles**: Use external stylesheets when possible
2. **Conditional loading**: Only load styles when needed
3. **Optimize images**: Use appropriate image sizes in sections

### Maintainability
1. **Use consistent naming**: Follow naming conventions for sections and settings
2. **Document schemas**: Provide clear labels and descriptions
3. **Modular design**: Create reusable section components

### User Experience
1. **Provide defaults**: Always include sensible default values
2. **Logical grouping**: Group related settings together
3. **Clear labels**: Use descriptive labels for all settings
