# Control Flow Tags

Control flow tags determine which blocks of Liquid code get executed based on conditions.

## if

Executes a block of code if a condition is true.

### Syntax
```liquid
{% if condition %}
  <!-- code to execute -->
{% endif %}
```

### Basic Usage
```liquid
{% if product.available %}
  <button>Add to Cart</button>
{% endif %}
```

### With else
```liquid
{% if customer %}
  <p>Welcome back, {{ customer.first_name }}!</p>
{% else %}
  <p>Please log in to continue.</p>
{% endif %}
```

### With elsif
```liquid
{% if product.price > 100 %}
  <span class="expensive">Premium Product</span>
{% elsif product.price > 50 %}
  <span class="moderate">Mid-range Product</span>
{% else %}
  <span class="affordable">Budget-friendly Product</span>
{% endif %}
```

### Operators

#### Comparison Operators
- `==` - Equal to
- `!=` - Not equal to
- `>` - Greater than
- `<` - Less than
- `>=` - Greater than or equal to
- `<=` - Less than or equal to

```liquid
{% if product.price >= 50 %}
  <span class="premium">Premium</span>
{% endif %}
```

#### Logical Operators
- `and` - Logical AND
- `or` - Logical OR

```liquid
{% if product.available and product.price < 100 %}
  <button>Buy Now - Great Deal!</button>
{% endif %}

{% if product.tags contains 'sale' or product.compare_at_price > product.price %}
  <span class="sale-badge">On Sale!</span>
{% endif %}
```

#### String Operators
- `contains` - String contains substring
- `starts_with` - String starts with substring
- `ends_with` - String ends with substring

```liquid
{% if product.title contains 'iPhone' %}
  <span class="apple-product">Apple Product</span>
{% endif %}

{% if product.handle starts_with 'new-' %}
  <span class="new-product">New Arrival</span>
{% endif %}
```

#### CloudCart Enhanced Operators
- `size` - Get size of arrays/strings
- `type` - Get type of variable
- `first` - Get first element
- `last` - Get last element
- `blank` - Check if variable is blank
- `empty` - Check if variable is empty
- `nil` - Check if variable is null
- `present` - Check if variable is present

```liquid
{% if products.size > 0 %}
  <p>Found {{ products.size }} products</p>
{% endif %}

{% if product.title.type == "string" %}
  <h1>{{ product.title }}</h1>
{% endif %}

{% if collection.products.first %}
  <p>Featured: {{ collection.products.first.title }}</p>
{% endif %}
```

### Complex Conditions
```liquid
{% if customer and customer.tags contains 'vip' and cart.total_price > 500 %}
  <div class="vip-discount">
    <p>VIP Discount Applied!</p>
  </div>
{% endif %}
```

### Nested Conditions
```liquid
{% if product.available %}
  {% if product.compare_at_price > product.price %}
    <span class="sale-price">
      Sale: {{ product.price | money }}
      <s>{{ product.compare_at_price | money }}</s>
    </span>
  {% else %}
    <span class="regular-price">{{ product.price | money }}</span>
  {% endif %}
{% else %}
  <span class="sold-out">Sold Out</span>
{% endif %}
```

## unless

Executes a block of code unless a condition is true (opposite of if).

### Syntax
```liquid
{% unless condition %}
  <!-- code to execute -->
{% endunless %}
```

### Basic Usage
```liquid
{% unless product.available %}
  <p class="sold-out">This product is currently sold out.</p>
{% endunless %}
```

### With else
```liquid
{% unless customer %}
  <p>Please <a href="/account/login">log in</a> to continue.</p>
{% else %}
  <p>Welcome, {{ customer.first_name }}!</p>
{% endunless %}
```

### Practical Examples
```liquid
{% unless cart.item_count == 0 %}
  <a href="/cart" class="cart-link">
    Cart ({{ cart.item_count }})
  </a>
{% endunless %}

{% unless product.tags contains 'hidden' %}
  <div class="product-card">
    <!-- product display -->
  </div>
{% endunless %}
```

## case/when

Compares a variable against different values and executes code based on the match.

### Syntax
```liquid
{% case variable %}
  {% when value1 %}
    <!-- code for value1 -->
  {% when value2 %}
    <!-- code for value2 -->
  {% else %}
    <!-- default code -->
{% endcase %}
```

### Basic Usage
```liquid
{% case product.type %}
  {% when 'Electronics' %}
    <span class="category-electronics">📱 Electronics</span>
  {% when 'Clothing' %}
    <span class="category-clothing">👕 Clothing</span>
  {% when 'Books' %}
    <span class="category-books">📚 Books</span>
  {% else %}
    <span class="category-other">🏷️ Other</span>
{% endcase %}
```

### Multiple Values
```liquid
{% case product.vendor %}
  {% when 'Apple', 'Samsung', 'Google' %}
    <span class="premium-brand">Premium Brand</span>
  {% when 'Generic', 'Unknown' %}
    <span class="budget-brand">Budget Brand</span>
  {% else %}
    <span class="standard-brand">Standard Brand</span>
{% endcase %}
```

### With Variables
```liquid
{% assign shipping_method = order.shipping_method %}
{% case shipping_method %}
  {% when 'express' %}
    <p>Express delivery: 1-2 business days</p>
  {% when 'standard' %}
    <p>Standard delivery: 3-5 business days</p>
  {% when 'economy' %}
    <p>Economy delivery: 5-10 business days</p>
  {% else %}
    <p>Delivery time varies</p>
{% endcase %}
```

### Complex Case Statements
```liquid
{% case template %}
  {% when 'product' %}
    {% if product.available %}
      {% assign button_text = 'Add to Cart' %}
    {% else %}
      {% assign button_text = 'Sold Out' %}
    {% endif %}
  {% when 'collection' %}
    {% assign button_text = 'View Products' %}
  {% when 'cart' %}
    {% assign button_text = 'Checkout' %}
  {% else %}
    {% assign button_text = 'Learn More' %}
{% endcase %}

<button class="btn">{{ button_text }}</button>
```

## Best Practices

### Performance Tips
1. **Use case instead of multiple if statements** when checking the same variable
2. **Put most likely conditions first** in if/elsif chains
3. **Avoid deeply nested conditions** - use early returns or separate logic

### Readability Tips
1. **Use meaningful variable names** in conditions
2. **Add comments** for complex logic
3. **Break long conditions** into multiple lines
4. **Use consistent indentation**

### Example: Clean Conditional Logic
```liquid
{% comment %} Check product availability and pricing {% endcomment %}
{% assign is_available = product.available %}
{% assign is_on_sale = product.compare_at_price > product.price %}
{% assign is_premium = product.price > 100 %}

{% if is_available %}
  <div class="product-actions">
    {% if is_on_sale %}
      <span class="sale-badge">Sale!</span>
    {% endif %}
    
    {% if is_premium %}
      <span class="premium-badge">Premium</span>
    {% endif %}
    
    <button class="add-to-cart">Add to Cart</button>
  </div>
{% else %}
  <div class="product-unavailable">
    <span>Currently Unavailable</span>
    <button class="notify-me">Notify When Available</button>
  </div>
{% endif %}
```

## Common Patterns

### Product Availability Check
```liquid
{% if product.available %}
  {% if product.variants.size > 1 %}
    <select name="id">
      {% for variant in product.variants %}
        {% if variant.available %}
          <option value="{{ variant.id }}">
            {{ variant.title }} - {{ variant.price | money }}
          </option>
        {% endif %}
      {% endfor %}
    </select>
  {% else %}
    <input type="hidden" name="id" value="{{ product.variants.first.id }}">
  {% endif %}
  <button type="submit">Add to Cart</button>
{% else %}
  <button disabled>Sold Out</button>
{% endif %}
```

### Customer Authentication
```liquid
{% if customer %}
  <div class="customer-menu">
    <span>Hello, {{ customer.first_name }}!</span>
    <a href="/account">My Account</a>
    <a href="/account/logout">Logout</a>
  </div>
{% else %}
  <div class="guest-menu">
    <a href="/account/login">Login</a>
    <a href="/account/register">Register</a>
  </div>
{% endif %}
```

### Cart Status
```liquid
{% case cart.item_count %}
  {% when 0 %}
    <p>Your cart is empty</p>
    <a href="/collections/all">Continue Shopping</a>
  {% when 1 %}
    <p>You have 1 item in your cart</p>
  {% else %}
    <p>You have {{ cart.item_count }} items in your cart</p>
{% endcase %}
```
