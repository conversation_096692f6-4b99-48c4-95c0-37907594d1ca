# Iteration Tags

Iteration tags allow you to loop through collections and arrays, providing powerful ways to display repeated content.

## for

Loops through a collection or array, executing the contained code for each item.

### Basic Syntax
```liquid
{% for item in collection %}
  <!-- code to execute for each item -->
{% endfor %}
```

### Simple Example
```liquid
{% for product in collection.products %}
  <div class="product">
    <h3>{{ product.title }}</h3>
    <p>{{ product.price | money }}</p>
  </div>
{% endfor %}
```

### Loop Variables

Inside a for loop, you have access to special variables:

#### forloop object
- `forloop.first` - true if it's the first iteration
- `forloop.last` - true if it's the last iteration
- `forloop.index` - current iteration number (1-based)
- `forloop.index0` - current iteration number (0-based)
- `forloop.rindex` - remaining iterations (including current)
- `forloop.rindex0` - remaining iterations (0-based)
- `forloop.length` - total number of iterations

```liquid
{% for product in collection.products %}
  <div class="product{% if forloop.first %} first{% endif %}{% if forloop.last %} last{% endif %}">
    <span class="position">{{ forloop.index }} of {{ forloop.length }}</span>
    <h3>{{ product.title }}</h3>
  </div>
{% endfor %}
```

### Shopify Attributes

#### limit
Limits the number of iterations:
```liquid
{% for product in collection.products limit: 4 %}
  <div class="featured-product">{{ product.title }}</div>
{% endfor %}
```

#### offset
Skips the specified number of items:
```liquid
{% for product in collection.products offset: 2 limit: 4 %}
  <div class="product">{{ product.title }}</div>
{% endfor %}
```

#### reversed
Reverses the order of iteration:
```liquid
{% for article in blog.articles reversed %}
  <article>
    <h2>{{ article.title }}</h2>
    <time>{{ article.published_at | date: '%B %d, %Y' }}</time>
  </article>
{% endfor %}
```

#### range
Creates a range of numbers:
```liquid
{% for i in (1..5) %}
  <span>Number {{ i }}</span>
{% endfor %}

{% assign start = 10 %}
{% assign end = 15 %}
{% for num in (start..end) %}
  <div>{{ num }}</div>
{% endfor %}
```

#### cols (for grid layouts)
Organizes items into columns:
```liquid
{% for product in collection.products cols: 3 %}
  <div class="col-4">
    <div class="product">{{ product.title }}</div>
  </div>
  {% if forloop.index % 3 == 0 %}</div><div class="row">{% endif %}
{% endfor %}
```

### Complex Examples

#### Product Grid with Pagination
```liquid
<div class="product-grid">
  {% for product in collection.products limit: 12 %}
    <div class="product-card">
      <a href="{{ product.url }}">
        <img src="{{ product.featured_image | img_url: '300x300' }}" alt="{{ product.title }}">
        <h3>{{ product.title }}</h3>
        <p class="price">
          {% if product.compare_at_price > product.price %}
            <span class="sale-price">{{ product.price | money }}</span>
            <span class="original-price">{{ product.compare_at_price | money }}</span>
          {% else %}
            {{ product.price | money }}
          {% endif %}
        </p>
      </a>
    </div>
  {% endfor %}
</div>
```

#### Nested Loops
```liquid
{% for collection in collections %}
  <div class="collection-section">
    <h2>{{ collection.title }}</h2>
    <div class="products">
      {% for product in collection.products limit: 4 %}
        <div class="product">
          <h4>{{ product.title }}</h4>
          <p>{{ product.price | money }}</p>
        </div>
      {% endfor %}
    </div>
  </div>
{% endfor %}
```

### else

Executes when the collection is empty:
```liquid
{% for product in collection.products %}
  <div class="product">{{ product.title }}</div>
{% else %}
  <p>No products found in this collection.</p>
{% endfor %}
```

## tablerow

Creates HTML table rows with automatic row and column management.

### Basic Syntax
```liquid
<table>
  {% tablerow item in collection %}
    <td>{{ item }}</td>
  {% endtablerow %}
</table>
```

### Shopify Attributes

#### cols
Specifies number of columns per row:
```liquid
<table>
  {% tablerow product in collection.products cols: 3 %}
    <td>
      <img src="{{ product.featured_image | img_url: '150x150' }}">
      <p>{{ product.title }}</p>
    </td>
  {% endtablerow %}
</table>
```

#### limit and offset
```liquid
<table>
  {% tablerow product in collection.products cols: 4 limit: 8 offset: 4 %}
    <td>{{ product.title }}</td>
  {% endtablerow %}
</table>
```

#### range
```liquid
<table>
  {% tablerow i in (1..20) cols: 5 %}
    <td>{{ i }}</td>
  {% endtablerow %}
</table>
```

### Tablerow Variables

Similar to forloop, but with table-specific variables:
- `tablerowloop.col` - current column number
- `tablerowloop.col0` - current column number (0-based)
- `tablerowloop.col_first` - true if first column
- `tablerowloop.col_last` - true if last column
- `tablerowloop.first` - true if first item
- `tablerowloop.index` - current item number
- `tablerowloop.index0` - current item number (0-based)
- `tablerowloop.last` - true if last item
- `tablerowloop.length` - total number of items
- `tablerowloop.rindex` - remaining items
- `tablerowloop.rindex0` - remaining items (0-based)
- `tablerowloop.row` - current row number

```liquid
<table class="product-table">
  {% tablerow product in collection.products cols: 3 %}
    <td class="product-cell{% if tablerowloop.col_first %} first-col{% endif %}{% if tablerowloop.col_last %} last-col{% endif %}">
      <div class="product-info">
        <img src="{{ product.featured_image | img_url: '200x200' }}" alt="{{ product.title }}">
        <h4>{{ product.title }}</h4>
        <p>{{ product.price | money }}</p>
        <small>Row {{ tablerowloop.row }}, Col {{ tablerowloop.col }}</small>
      </div>
    </td>
  {% endtablerow %}
</table>
```

## cycle

Cycles through a group of values, useful for alternating styles or content.

### Basic Syntax
```liquid
{% cycle 'value1', 'value2', 'value3' %}
```

### Simple Example
```liquid
{% for product in collection.products %}
  <div class="product {% cycle 'odd', 'even' %}">
    {{ product.title }}
  </div>
{% endfor %}
```

### Named Cycles
Use named cycles when you have multiple cycles in the same template:
```liquid
{% for product in collection.products %}
  <div class="product {% cycle 'row_colors': 'red', 'blue', 'green' %}">
    <span class="{% cycle 'text_colors': 'light', 'dark' %}">
      {{ product.title }}
    </span>
  </div>
{% endfor %}
```

### Cycle Groups
Group related cycles together:
```liquid
{% for item in items %}
  <div class="item {% cycle group: 'colors', 'red', 'blue', 'green' %}">
    <span class="{% cycle group: 'sizes', 'small', 'medium', 'large' %}">
      {{ item.name }}
    </span>
  </div>
{% endfor %}
```

### Advanced Cycle Features

#### Reset Cycle
```liquid
{% for collection in collections %}
  <h2>{{ collection.title }}</h2>
  {% cycle reset: true, 'odd', 'even' %}
  {% for product in collection.products limit: 3 %}
    <div class="product {% cycle 'odd', 'even' %}">
      {{ product.title }}
    </div>
  {% endfor %}
{% endfor %}
```

#### Clear Cycle
```liquid
{% cycle clear: 'my_cycle' %}
{% for item in items %}
  <div class="{% cycle 'my_cycle': 'class1', 'class2' %}">
    {{ item.name }}
  </div>
{% endfor %}
```

## break and continue

Control loop execution flow.

### break
Exits the loop immediately:
```liquid
{% for product in collection.products %}
  {% if product.price > 1000 %}
    {% break %}
  {% endif %}
  <div class="affordable-product">{{ product.title }}</div>
{% endfor %}
```

### continue
Skips the current iteration and continues with the next:
```liquid
{% for product in collection.products %}
  {% if product.available == false %}
    {% continue %}
  {% endif %}
  <div class="available-product">{{ product.title }}</div>
{% endfor %}
```

### Practical Example
```liquid
{% assign featured_count = 0 %}
{% for product in collection.products %}
  {% if product.tags contains 'hidden' %}
    {% continue %}
  {% endif %}
  
  {% if featured_count >= 6 %}
    {% break %}
  {% endif %}
  
  {% if product.tags contains 'featured' %}
    <div class="featured-product">
      <h3>{{ product.title }}</h3>
      <p>{{ product.price | money }}</p>
    </div>
    {% assign featured_count = featured_count | plus: 1 %}
  {% endif %}
{% endfor %}
```

## Best Practices

### Performance Optimization
1. **Use limit** to avoid processing unnecessary items
2. **Use offset** for pagination instead of processing all items
3. **Break early** when you've found what you need
4. **Cache collections** in variables when used multiple times

### Code Organization
```liquid
{% comment %} Cache frequently used collections {% endcomment %}
{% assign featured_products = collections.featured.products %}
{% assign sale_products = collections.sale.products %}

{% comment %} Display featured products {% endcomment %}
{% for product in featured_products limit: 8 %}
  {% render 'product-card', product: product %}
{% endfor %}

{% comment %} Display sale products {% endcomment %}
{% for product in sale_products limit: 4 %}
  {% render 'product-card', product: product, show_sale_badge: true %}
{% endfor %}
```

### Responsive Grid Example
```liquid
<div class="product-grid">
  {% for product in collection.products %}
    <div class="product-item {% cycle 'col-1', 'col-2', 'col-3', 'col-4' %}">
      {% if forloop.index <= 4 %}
        {% comment %} First row - larger images {% endcomment %}
        <img src="{{ product.featured_image | img_url: '400x400' }}" alt="{{ product.title }}">
      {% else %}
        {% comment %} Subsequent rows - smaller images {% endcomment %}
        <img src="{{ product.featured_image | img_url: '300x300' }}" alt="{{ product.title }}">
      {% endif %}
      
      <h3>{{ product.title }}</h3>
      <p>{{ product.price | money }}</p>
      
      {% if forloop.index % 4 == 0 and forloop.last == false %}
        </div><div class="product-grid">
      {% endif %}
    </div>
  {% else %}
    <p class="no-products">No products found.</p>
  {% endfor %}
</div>
```
