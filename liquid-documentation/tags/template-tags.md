# Template Tags

Template tags control template structure, inheritance, and inclusion, providing powerful tools for organizing and reusing template code.

## include

Includes another template file within the current template, with access to the current context.

### Basic Syntax
```liquid
{% include 'template-name' %}
```

### Simple Include
```liquid
{% comment %} Include a header template {% endcomment %}
{% include 'header' %}

{% comment %} Include a footer template {% endcomment %}
{% include 'footer' %}

{% comment %} Include product card template {% endcomment %}
{% include 'product-card' %}
```

### Include with Variables
```liquid
{% comment %} Pass variables to included template {% endcomment %}
{% include 'product-card', product: featured_product %}
{% include 'product-card', product: featured_product, show_vendor: true %}
{% include 'button', text: 'Add to Cart', class: 'btn-primary' %}
```

### Include in Loops
```liquid
{% comment %} Include template for each product {% endcomment %}
{% for product in collection.products %}
  {% include 'product-grid-item', product: product %}
{% endfor %}

{% comment %} Include with loop variables {% endcomment %}
{% for article in blog.articles %}
  {% include 'article-summary', article: article, index: forloop.index %}
{% endfor %}
```

### Conditional Includes
```liquid
{% comment %} Include different templates based on conditions {% endcomment %}
{% if template contains 'product' %}
  {% include 'product-breadcrumbs' %}
{% elsif template contains 'collection' %}
  {% include 'collection-breadcrumbs' %}
{% else %}
  {% include 'default-breadcrumbs' %}
{% endif %}

{% comment %} Include based on settings {% endcomment %}
{% if settings.show_newsletter %}
  {% include 'newsletter-signup' %}
{% endif %}
```

## render

Similar to include, but creates an isolated scope for the rendered template.

### Basic Syntax
```liquid
{% render 'template-name' %}
```

### Render with Parameters
```liquid
{% comment %} Render with explicit parameters {% endcomment %}
{% render 'product-card', product: product, show_vendor: true %}
{% render 'price-display', price: product.price, compare_price: product.compare_at_price %}
```

### Render vs Include
```liquid
{% comment %} Include shares the current scope {% endcomment %}
{% assign message = 'Hello from parent' %}
{% include 'child-template' %}
<!-- child-template can access 'message' variable -->

{% comment %} Render creates isolated scope {% endcomment %}
{% assign message = 'Hello from parent' %}
{% render 'child-template' %}
<!-- child-template cannot access 'message' unless passed explicitly -->
{% render 'child-template', message: message %}
```

### Render for Component Architecture
```liquid
{% comment %} Render reusable components {% endcomment %}
{% render 'components/button', 
   text: 'Add to Cart', 
   type: 'primary', 
   action: 'add-to-cart',
   data_attributes: 'data-product-id="' | append: product.id | append: '"' %}

{% render 'components/modal', 
   id: 'product-modal', 
   title: product.title,
   content: product.description %}

{% render 'components/breadcrumbs', 
   items: breadcrumb_items %}
```

## layout

Specifies which layout template to use for the current template.

### Basic Syntax
```liquid
{% layout 'layout-name' %}
```

### Layout Selection
```liquid
{% comment %} Use specific layout {% endcomment %}
{% layout 'theme' %}

{% comment %} Use different layouts based on conditions {% endcomment %}
{% if template contains 'product' %}
  {% layout 'product-layout' %}
{% elsif template contains 'collection' %}
  {% layout 'collection-layout' %}
{% else %}
  {% layout 'theme' %}
{% endif %}
```

### Disable Layout
```liquid
{% comment %} Render template without any layout {% endcomment %}
{% layout none %}
```

### Dynamic Layout Assignment
```liquid
{% comment %} Choose layout based on settings or conditions {% endcomment %}
{% if settings.use_minimal_layout %}
  {% layout 'minimal' %}
{% elsif customer and customer.tags contains 'vip' %}
  {% layout 'vip-layout' %}
{% else %}
  {% layout 'theme' %}
{% endif %}
```

## extends

Extends a parent template, allowing template inheritance.

### Basic Syntax
```liquid
{% extends 'parent-template' %}
```

### Template Inheritance Example
```liquid
{% comment %} base.liquid - Parent template {% endcomment %}
<!DOCTYPE html>
<html>
<head>
  <title>{% block title %}{{ shop.name }}{% endblock %}</title>
  {% block head %}{% endblock %}
</head>
<body>
  <header>
    {% block header %}
      {% include 'header' %}
    {% endblock %}
  </header>
  
  <main>
    {% block content %}{% endblock %}
  </main>
  
  <footer>
    {% block footer %}
      {% include 'footer' %}
    {% endblock %}
  </footer>
</body>
</html>

{% comment %} product.liquid - Child template {% endcomment %}
{% extends 'base' %}

{% block title %}{{ product.title }} - {{ shop.name }}{% endblock %}

{% block head %}
  <meta name="description" content="{{ product.description | strip_html | truncate: 160 }}">
{% endblock %}

{% block content %}
  <div class="product-page">
    <h1>{{ product.title }}</h1>
    <div class="product-details">
      <!-- product content -->
    </div>
  </div>
{% endblock %}
```

## block

Defines content blocks that can be overridden in child templates.

### Basic Syntax
```liquid
{% block block-name %}
  <!-- default content -->
{% endblock %}
```

### Block with Default Content
```liquid
{% comment %} In parent template {% endcomment %}
{% block sidebar %}
  <div class="default-sidebar">
    <h3>Categories</h3>
    {% for collection in collections %}
      <a href="{{ collection.url }}">{{ collection.title }}</a>
    {% endfor %}
  </div>
{% endblock %}

{% comment %} In child template {% endcomment %}
{% block sidebar %}
  <div class="product-sidebar">
    <h3>Related Products</h3>
    {% for product in related_products %}
      {% render 'product-card-mini', product: product %}
    {% endfor %}
  </div>
{% endblock %}
```

### Multiple Blocks
```liquid
{% comment %} Parent template with multiple blocks {% endcomment %}
<div class="page-layout">
  <aside class="sidebar">
    {% block sidebar %}
      <!-- default sidebar content -->
    {% endblock %}
  </aside>
  
  <main class="content">
    {% block main_content %}
      <!-- default main content -->
    {% endblock %}
  </main>
  
  <section class="secondary">
    {% block secondary_content %}
      <!-- default secondary content -->
    {% endblock %}
  </section>
</div>
```

### Nested Blocks
```liquid
{% comment %} Parent template {% endcomment %}
{% block content %}
  <div class="content-wrapper">
    {% block content_header %}
      <h1>Default Title</h1>
    {% endblock %}
    
    {% block content_body %}
      <p>Default content</p>
    {% endblock %}
    
    {% block content_footer %}
      <!-- default footer -->
    {% endblock %}
  </div>
{% endblock %}
```

## Practical Examples

### Component-Based Architecture
```liquid
{% comment %} components/product-card.liquid {% endcomment %}
<div class="product-card {{ card_class }}">
  <div class="product-image">
    <a href="{{ product.url }}">
      <img src="{{ product.featured_image | img_url: image_size | default: '300x300' }}" 
           alt="{{ product.title }}">
    </a>
    {% if show_badges %}
      {% render 'product-badges', product: product %}
    {% endif %}
  </div>
  
  <div class="product-info">
    <h3 class="product-title">
      <a href="{{ product.url }}">{{ product.title }}</a>
    </h3>
    
    {% if show_vendor %}
      <p class="product-vendor">{{ product.vendor }}</p>
    {% endif %}
    
    {% render 'price-display', 
       price: product.price, 
       compare_price: product.compare_at_price %}
    
    {% if show_actions %}
      {% render 'product-actions', product: product %}
    {% endif %}
  </div>
</div>

{% comment %} Usage in collection template {% endcomment %}
<div class="product-grid">
  {% for product in collection.products %}
    {% render 'components/product-card',
       product: product,
       show_vendor: true,
       show_badges: true,
       show_actions: true,
       image_size: '400x400',
       card_class: 'grid-item' %}
  {% endfor %}
</div>
```

### Layout Inheritance System
```liquid
{% comment %} layouts/base.liquid {% endcomment %}
<!DOCTYPE html>
<html lang="{{ shop.locale }}">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  
  {% block meta_tags %}
    <meta name="description" content="{{ page_description | default: shop.description }}">
  {% endblock %}
  
  <title>{% block title %}{{ page_title | default: shop.name }}{% endblock %}</title>
  
  {% block stylesheets %}
    {{ 'theme.css' | asset_url | stylesheet_tag }}
  {% endblock %}
  
  {% block head_scripts %}{% endblock %}
</head>
<body class="{% block body_class %}{{ template | replace: '.', '-' }}{% endblock %}">
  
  {% block header %}
    {% include 'header' %}
  {% endblock %}
  
  <main id="main-content">
    {% block content %}{% endblock %}
  </main>
  
  {% block footer %}
    {% include 'footer' %}
  {% endblock %}
  
  {% block scripts %}
    {{ 'theme.js' | asset_url | script_tag }}
  {% endblock %}
</body>
</html>

{% comment %} templates/product.liquid {% endcomment %}
{% extends 'base' %}

{% block title %}{{ product.title }} - {{ shop.name }}{% endblock %}

{% block meta_tags %}
  <meta name="description" content="{{ product.description | strip_html | truncate: 160 }}">
  <meta property="og:title" content="{{ product.title }}">
  <meta property="og:description" content="{{ product.description | strip_html | truncate: 160 }}">
  <meta property="og:image" content="{{ product.featured_image | img_url: '1200x630' }}">
{% endblock %}

{% block body_class %}product-page{% endblock %}

{% block content %}
  <div class="product-container">
    {% render 'product-gallery', product: product %}
    {% render 'product-form', product: product %}
    {% render 'product-description', product: product %}
    {% render 'product-recommendations', product: product %}
  </div>
{% endblock %}
```

### Modular Template System
```liquid
{% comment %} sections/hero.liquid {% endcomment %}
<section class="hero-section">
  {% if section.settings.hero_image %}
    <div class="hero-image">
      <img src="{{ section.settings.hero_image | img_url: '1920x800' }}" 
           alt="{{ section.settings.hero_title }}">
    </div>
  {% endif %}
  
  <div class="hero-content">
    {% if section.settings.hero_title %}
      <h1>{{ section.settings.hero_title }}</h1>
    {% endif %}
    
    {% if section.settings.hero_subtitle %}
      <p>{{ section.settings.hero_subtitle }}</p>
    {% endif %}
    
    {% if section.settings.hero_button_text %}
      <a href="{{ section.settings.hero_button_url }}" class="btn btn-primary">
        {{ section.settings.hero_button_text }}
      </a>
    {% endif %}
  </div>
</section>

{% comment %} templates/index.liquid {% endcomment %}
{% extends 'base' %}

{% block content %}
  {% for section in template.sections %}
    {% render section.type, section: section %}
  {% endfor %}
{% endblock %}
```

### Dynamic Template Loading
```liquid
{% comment %} Dynamic component loading {% endcomment %}
{% assign component_name = 'product-card-' | append: settings.product_card_style %}
{% render component_name, product: product %}

{% comment %} Fallback template loading {% endcomment %}
{% assign template_name = 'custom-' | append: product.type | handleize %}
{% if templates contains template_name %}
  {% render template_name, product: product %}
{% else %}
  {% render 'default-product-template', product: product %}
{% endif %}
```

## Best Practices

### Template Organization
1. **Use consistent naming**: Follow a clear naming convention for templates
2. **Organize by function**: Group related templates in subdirectories
3. **Keep templates focused**: Each template should have a single responsibility

### Performance Optimization
1. **Minimize includes**: Avoid excessive template inclusion
2. **Use render for isolation**: Use render when you need scope isolation
3. **Cache template results**: Store rendered content in variables when appropriate

### Maintainability
1. **Document template dependencies**: Comment which variables templates expect
2. **Use meaningful block names**: Block names should clearly indicate their purpose
3. **Provide default content**: Always provide sensible defaults in blocks

### Error Handling
```liquid
{% comment %} Safe template inclusion {% endcomment %}
{% if templates contains 'custom-header' %}
  {% include 'custom-header' %}
{% else %}
  {% include 'default-header' %}
{% endif %}

{% comment %} Safe rendering with fallbacks {% endcomment %}
{% assign component = 'components/' | append: component_type %}
{% if templates contains component %}
  {% render component, data: component_data %}
{% else %}
  <div class="component-error">
    <p>Component "{{ component_type }}" not found</p>
  </div>
{% endif %}
```
