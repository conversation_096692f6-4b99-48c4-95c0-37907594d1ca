# Form Tags

Form tags handle form creation and processing in CloudCart, providing essential functionality for customer interactions, contact forms, and e-commerce operations.

## form

Creates HTML forms with proper action URLs and CSRF protection.

### Basic Syntax
```liquid
{% form 'form_type' %}
  <!-- form content -->
{% endform %}
```

### Contact Form
```liquid
{% form 'contact' %}
  {% if form.posted_successfully? %}
    <div class="success-message">
      <p>Thank you for your message! We'll get back to you soon.</p>
    </div>
  {% endif %}
  
  {% if form.errors %}
    <div class="error-messages">
      <h4>Please correct the following errors:</h4>
      <ul>
        {% for field in form.errors %}
          <li>{{ field | capitalize }}: {{ form.errors[field] }}</li>
        {% endfor %}
      </ul>
    </div>
  {% endif %}
  
  <div class="form-group">
    <label for="contact_name">Name *</label>
    <input type="text" 
           id="contact_name" 
           name="contact[name]" 
           value="{{ form.name }}" 
           required>
  </div>
  
  <div class="form-group">
    <label for="contact_email">Email *</label>
    <input type="email" 
           id="contact_email" 
           name="contact[email]" 
           value="{{ form.email }}" 
           required>
  </div>
  
  <div class="form-group">
    <label for="contact_phone">Phone</label>
    <input type="tel" 
           id="contact_phone" 
           name="contact[phone]" 
           value="{{ form.phone }}">
  </div>
  
  <div class="form-group">
    <label for="contact_message">Message *</label>
    <textarea id="contact_message" 
              name="contact[message]" 
              rows="5" 
              required>{{ form.message }}</textarea>
  </div>
  
  <div class="form-group">
    <button type="submit" class="btn btn-primary">Send Message</button>
  </div>
{% endform %}
```

### Customer Registration Form
```liquid
{% form 'create_customer' %}
  {% if form.errors %}
    <div class="error-messages">
      {% for field in form.errors %}
        <p>{{ field | capitalize }}: {{ form.errors[field] }}</p>
      {% endfor %}
    </div>
  {% endif %}
  
  <div class="form-row">
    <div class="form-group">
      <label for="first_name">First Name *</label>
      <input type="text" 
             id="first_name" 
             name="customer[first_name]" 
             value="{{ form.first_name }}" 
             required>
    </div>
    
    <div class="form-group">
      <label for="last_name">Last Name *</label>
      <input type="text" 
             id="last_name" 
             name="customer[last_name]" 
             value="{{ form.last_name }}" 
             required>
    </div>
  </div>
  
  <div class="form-group">
    <label for="email">Email Address *</label>
    <input type="email" 
           id="email" 
           name="customer[email]" 
           value="{{ form.email }}" 
           required>
  </div>
  
  <div class="form-group">
    <label for="password">Password *</label>
    <input type="password" 
           id="password" 
           name="customer[password]" 
           required>
    <small class="form-text">Minimum 6 characters</small>
  </div>
  
  <div class="form-group">
    <label for="password_confirmation">Confirm Password *</label>
    <input type="password" 
           id="password_confirmation" 
           name="customer[password_confirmation]" 
           required>
  </div>
  
  <div class="form-group">
    <label class="checkbox-label">
      <input type="checkbox" 
             name="customer[accepts_marketing]" 
             value="1">
      Subscribe to our newsletter
    </label>
  </div>
  
  <div class="form-group">
    <button type="submit" class="btn btn-primary">Create Account</button>
  </div>
  
  <div class="form-footer">
    <p>Already have an account? <a href="/account/login">Sign in</a></p>
  </div>
{% endform %}
```

### Customer Login Form
```liquid
{% form 'customer_login' %}
  {% if form.errors %}
    <div class="error-messages">
      <p>{{ form.errors | default_errors }}</p>
    </div>
  {% endif %}
  
  <div class="form-group">
    <label for="customer_email">Email Address</label>
    <input type="email" 
           id="customer_email" 
           name="customer[email]" 
           value="{{ form.email }}" 
           required>
  </div>
  
  <div class="form-group">
    <label for="customer_password">Password</label>
    <input type="password" 
           id="customer_password" 
           name="customer[password]" 
           required>
  </div>
  
  <div class="form-group">
    <label class="checkbox-label">
      <input type="checkbox" name="customer[remember_me]" value="1">
      Remember me
    </label>
  </div>
  
  <div class="form-group">
    <button type="submit" class="btn btn-primary">Sign In</button>
  </div>
  
  <div class="form-footer">
    <p><a href="/account/recover">Forgot your password?</a></p>
    <p>Don't have an account? <a href="/account/register">Create one</a></p>
  </div>
{% endform %}
```

### Password Recovery Form
```liquid
{% form 'recover_customer_password' %}
  {% if form.posted_successfully? %}
    <div class="success-message">
      <p>We've sent you an email with a link to reset your password.</p>
    </div>
  {% endif %}
  
  {% if form.errors %}
    <div class="error-messages">
      <p>{{ form.errors | default_errors }}</p>
    </div>
  {% endif %}
  
  <div class="form-group">
    <label for="recover_email">Email Address</label>
    <input type="email" 
           id="recover_email" 
           name="email" 
           value="{{ form.email }}" 
           required>
  </div>
  
  <div class="form-group">
    <button type="submit" class="btn btn-primary">Reset Password</button>
  </div>
  
  <div class="form-footer">
    <p><a href="/account/login">Back to login</a></p>
  </div>
{% endform %}
```

### Product Review Form
```liquid
{% form 'new_comment', article %}
  {% if form.posted_successfully? %}
    <div class="success-message">
      <p>Thank you for your review! It will be published after moderation.</p>
    </div>
  {% endif %}
  
  {% if form.errors %}
    <div class="error-messages">
      {% for field in form.errors %}
        <p>{{ field | capitalize }}: {{ form.errors[field] }}</p>
      {% endfor %}
    </div>
  {% endif %}
  
  <div class="form-group">
    <label for="comment_author">Name *</label>
    <input type="text" 
           id="comment_author" 
           name="comment[author]" 
           value="{{ form.author }}" 
           required>
  </div>
  
  <div class="form-group">
    <label for="comment_email">Email *</label>
    <input type="email" 
           id="comment_email" 
           name="comment[email]" 
           value="{{ form.email }}" 
           required>
  </div>
  
  <div class="form-group">
    <label for="comment_rating">Rating *</label>
    <div class="rating-input">
      {% for i in (1..5) %}
        <input type="radio" 
               id="rating_{{ i }}" 
               name="comment[rating]" 
               value="{{ i }}" 
               {% if form.rating == i %}checked{% endif %}>
        <label for="rating_{{ i }}" class="star">★</label>
      {% endfor %}
    </div>
  </div>
  
  <div class="form-group">
    <label for="comment_body">Review *</label>
    <textarea id="comment_body" 
              name="comment[body]" 
              rows="4" 
              required>{{ form.body }}</textarea>
  </div>
  
  <div class="form-group">
    <button type="submit" class="btn btn-primary">Submit Review</button>
  </div>
{% endform %}
```

### Newsletter Signup Form
```liquid
{% form 'customer', class: 'newsletter-form' %}
  {% if form.posted_successfully? %}
    <div class="success-message">
      <p>Thank you for subscribing to our newsletter!</p>
    </div>
  {% endif %}
  
  {% if form.errors %}
    <div class="error-messages">
      <p>{{ form.errors.email | default: 'Please enter a valid email address.' }}</p>
    </div>
  {% endif %}
  
  <div class="newsletter-input-group">
    <input type="email" 
           name="contact[email]" 
           placeholder="Enter your email address" 
           value="{{ form.email }}" 
           required>
    <input type="hidden" name="contact[tags]" value="newsletter">
    <button type="submit" class="btn btn-primary">Subscribe</button>
  </div>
  
  <small class="form-text">
    By subscribing, you agree to our <a href="/pages/privacy-policy">Privacy Policy</a>.
  </small>
{% endform %}
```

### Address Form
```liquid
{% form 'customer_address', customer.new_address %}
  {% if form.errors %}
    <div class="error-messages">
      {% for field in form.errors %}
        <p>{{ field | capitalize }}: {{ form.errors[field] }}</p>
      {% endfor %}
    </div>
  {% endif %}
  
  <div class="form-row">
    <div class="form-group">
      <label for="address_first_name">First Name *</label>
      <input type="text" 
             id="address_first_name" 
             name="address[first_name]" 
             value="{{ form.first_name }}" 
             required>
    </div>
    
    <div class="form-group">
      <label for="address_last_name">Last Name *</label>
      <input type="text" 
             id="address_last_name" 
             name="address[last_name]" 
             value="{{ form.last_name }}" 
             required>
    </div>
  </div>
  
  <div class="form-group">
    <label for="address_company">Company</label>
    <input type="text" 
           id="address_company" 
           name="address[company]" 
           value="{{ form.company }}">
  </div>
  
  <div class="form-group">
    <label for="address_address1">Address *</label>
    <input type="text" 
           id="address_address1" 
           name="address[address1]" 
           value="{{ form.address1 }}" 
           required>
  </div>
  
  <div class="form-group">
    <label for="address_address2">Apartment, suite, etc.</label>
    <input type="text" 
           id="address_address2" 
           name="address[address2]" 
           value="{{ form.address2 }}">
  </div>
  
  <div class="form-row">
    <div class="form-group">
      <label for="address_city">City *</label>
      <input type="text" 
             id="address_city" 
             name="address[city]" 
             value="{{ form.city }}" 
             required>
    </div>
    
    <div class="form-group">
      <label for="address_province">State/Province</label>
      <select id="address_province" name="address[province]">
        <option value="">Select State/Province</option>
        {% for province in country.provinces %}
          <option value="{{ province.code }}" 
                  {% if form.province == province.code %}selected{% endif %}>
            {{ province.name }}
          </option>
        {% endfor %}
      </select>
    </div>
  </div>
  
  <div class="form-row">
    <div class="form-group">
      <label for="address_zip">Postal/Zip Code *</label>
      <input type="text" 
             id="address_zip" 
             name="address[zip]" 
             value="{{ form.zip }}" 
             required>
    </div>
    
    <div class="form-group">
      <label for="address_country">Country *</label>
      <select id="address_country" name="address[country]" required>
        {% for country in countries %}
          <option value="{{ country.code }}" 
                  {% if form.country == country.code %}selected{% endif %}>
            {{ country.name }}
          </option>
        {% endfor %}
      </select>
    </div>
  </div>
  
  <div class="form-group">
    <label for="address_phone">Phone</label>
    <input type="tel" 
           id="address_phone" 
           name="address[phone]" 
           value="{{ form.phone }}">
  </div>
  
  <div class="form-group">
    <label class="checkbox-label">
      <input type="checkbox" 
             name="address[default]" 
             value="1" 
             {% if form.default %}checked{% endif %}>
      Set as default address
    </label>
  </div>
  
  <div class="form-group">
    <button type="submit" class="btn btn-primary">Save Address</button>
    <a href="/account/addresses" class="btn btn-secondary">Cancel</a>
  </div>
{% endform %}
```

## Advanced Form Features

### Form Validation and Error Handling
```liquid
{% form 'contact' %}
  <div class="form-validation">
    {% if form.posted_successfully? %}
      <div class="alert alert-success">
        <h4>Success!</h4>
        <p>Your message has been sent successfully.</p>
      </div>
    {% endif %}
    
    {% if form.errors %}
      <div class="alert alert-danger">
        <h4>Please correct the following errors:</h4>
        <ul class="error-list">
          {% for field in form.errors %}
            <li>
              <strong>{{ field | capitalize }}:</strong> 
              {{ form.errors[field] | join: ', ' }}
            </li>
          {% endfor %}
        </ul>
      </div>
    {% endif %}
  </div>
  
  <!-- Form fields with individual error display -->
  <div class="form-group {% if form.errors.name %}has-error{% endif %}">
    <label for="contact_name">Name *</label>
    <input type="text" 
           id="contact_name" 
           name="contact[name]" 
           value="{{ form.name }}" 
           class="{% if form.errors.name %}error{% endif %}"
           required>
    {% if form.errors.name %}
      <span class="error-message">{{ form.errors.name }}</span>
    {% endif %}
  </div>
{% endform %}
```

### AJAX Form Submission
```liquid
{% form 'contact', class: 'ajax-form' %}
  <div class="form-messages"></div>
  
  <div class="form-group">
    <label for="contact_name">Name *</label>
    <input type="text" id="contact_name" name="contact[name]" required>
  </div>
  
  <div class="form-group">
    <label for="contact_email">Email *</label>
    <input type="email" id="contact_email" name="contact[email]" required>
  </div>
  
  <div class="form-group">
    <label for="contact_message">Message *</label>
    <textarea id="contact_message" name="contact[message]" required></textarea>
  </div>
  
  <div class="form-group">
    <button type="submit" class="btn btn-primary">
      <span class="btn-text">Send Message</span>
      <span class="btn-loading" style="display: none;">Sending...</span>
    </button>
  </div>
{% endform %}

<script>
document.querySelector('.ajax-form').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const form = this;
  const formData = new FormData(form);
  const submitBtn = form.querySelector('button[type="submit"]');
  const btnText = submitBtn.querySelector('.btn-text');
  const btnLoading = submitBtn.querySelector('.btn-loading');
  const messagesDiv = form.querySelector('.form-messages');
  
  // Show loading state
  btnText.style.display = 'none';
  btnLoading.style.display = 'inline';
  submitBtn.disabled = true;
  
  fetch(form.action, {
    method: 'POST',
    body: formData,
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      messagesDiv.innerHTML = '<div class="alert alert-success">Message sent successfully!</div>';
      form.reset();
    } else {
      messagesDiv.innerHTML = '<div class="alert alert-danger">Please correct the errors and try again.</div>';
    }
  })
  .catch(error => {
    messagesDiv.innerHTML = '<div class="alert alert-danger">An error occurred. Please try again.</div>';
  })
  .finally(() => {
    // Reset button state
    btnText.style.display = 'inline';
    btnLoading.style.display = 'none';
    submitBtn.disabled = false;
  });
});
</script>
```

## Best Practices

### Security
1. **CSRF Protection**: Always use the `{% form %}` tag for CSRF token inclusion
2. **Input Validation**: Validate all inputs on both client and server side
3. **Sanitize Output**: Escape user input when displaying form data

### Accessibility
1. **Proper Labels**: Associate labels with form controls
2. **Error Messages**: Provide clear, descriptive error messages
3. **Keyboard Navigation**: Ensure forms are keyboard accessible

### User Experience
1. **Clear Instructions**: Provide helpful placeholder text and instructions
2. **Progressive Enhancement**: Ensure forms work without JavaScript
3. **Loading States**: Show loading indicators for form submissions

### Performance
1. **Minimize Fields**: Only ask for necessary information
2. **Client-side Validation**: Validate inputs before submission
3. **Optimize Requests**: Use AJAX for better user experience when appropriate
