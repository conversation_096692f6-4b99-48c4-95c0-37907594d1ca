# Authentication Tags

CloudCart provides specialized authentication tags for handling customer login states and user-specific content.

## auth

The `auth` tag displays content only when a customer is authenticated (logged in).

### Basic Syntax
```liquid
{% auth %}
  <!-- Content for authenticated customers -->
{% endauth %}
```

### Simple Example
```liquid
{% auth %}
  <div class="welcome-message">
    <h2>Welcome back, {{ customer.first_name }}!</h2>
    <p>You have {{ customer.orders_count }} previous orders.</p>
  </div>
{% endauth %}
```

### Customer Information Display
```liquid
{% auth %}
  <div class="customer-info">
    <div class="customer-avatar">
      <img src="{{ customer.avatar | default: '/assets/default-avatar.png' }}" alt="{{ customer.name }}">
    </div>
    <div class="customer-details">
      <h3>{{ customer.first_name }} {{ customer.last_name }}</h3>
      <p>{{ customer.email }}</p>
      {% if customer.phone %}
        <p>Phone: {{ customer.phone }}</p>
      {% endif %}
    </div>
  </div>
{% endauth %}
```

### Account Navigation
```liquid
{% auth %}
  <nav class="account-nav">
    <ul>
      <li><a href="/account">Dashboard</a></li>
      <li><a href="/account/orders">Orders ({{ customer.orders_count }})</a></li>
      <li><a href="/account/addresses">Addresses</a></li>
      <li><a href="/account/profile">Profile</a></li>
      <li><a href="/account/logout">Logout</a></li>
    </ul>
  </nav>
{% endauth %}
```

### Personalized Content
```liquid
{% auth %}
  <section class="personalized-recommendations">
    <h2>Recommended for You</h2>
    {% comment %} Show products based on customer's order history {% endcomment %}
    {% assign customer_tags = customer.tags %}
    {% if customer_tags contains 'vip' %}
      <div class="vip-products">
        {% for product in collections.vip.products limit: 4 %}
          {% render 'product-card', product: product %}
        {% endfor %}
      </div>
    {% else %}
      <div class="regular-recommendations">
        {% for product in collections.recommended.products limit: 4 %}
          {% render 'product-card', product: product %}
        {% endfor %}
      </div>
    {% endif %}
  </section>
{% endauth %}
```

### Customer-Specific Pricing
```liquid
{% auth %}
  {% if customer.tags contains 'wholesale' %}
    <div class="wholesale-pricing">
      <p class="wholesale-price">
        Wholesale Price: {{ product.price | times: 0.8 | money }}
      </p>
      <p class="savings">
        You save: {{ product.price | times: 0.2 | money }}
      </p>
    </div>
  {% elsif customer.tags contains 'vip' %}
    <div class="vip-pricing">
      <p class="vip-price">
        VIP Price: {{ product.price | times: 0.9 | money }}
      </p>
      <p class="savings">
        You save: {{ product.price | times: 0.1 | money }}
      </p>
    </div>
  {% endif %}
{% endauth %}
```

### Order History Integration
```liquid
{% auth %}
  {% if customer.orders_count > 0 %}
    <div class="reorder-section">
      <h3>Reorder Previous Items</h3>
      {% assign recent_order = customer.orders.first %}
      <div class="recent-order">
        <p>Last order: {{ recent_order.created_at | date: '%B %d, %Y' }}</p>
        <div class="order-products">
          {% for line_item in recent_order.line_items limit: 3 %}
            <div class="reorder-item">
              <img src="{{ line_item.product.featured_image | img_url: '100x100' }}" alt="{{ line_item.title }}">
              <div class="item-details">
                <h4>{{ line_item.title }}</h4>
                <p>{{ line_item.price | money }}</p>
                <button class="btn-reorder" data-variant-id="{{ line_item.variant_id }}">
                  Reorder
                </button>
              </div>
            </div>
          {% endfor %}
        </div>
      </div>
    </div>
  {% endif %}
{% endauth %}
```

## guest

The `guest` tag displays content only when no customer is authenticated (not logged in).

### Basic Syntax
```liquid
{% guest %}
  <!-- Content for guest users -->
{% endguest %}
```

### Login Prompt
```liquid
{% guest %}
  <div class="login-prompt">
    <h3>Sign in for a better experience</h3>
    <p>Access your order history, save favorites, and get personalized recommendations.</p>
    <div class="auth-buttons">
      <a href="/account/login" class="btn btn-primary">Sign In</a>
      <a href="/account/register" class="btn btn-secondary">Create Account</a>
    </div>
  </div>
{% endguest %}
```

### Guest Checkout Options
```liquid
{% guest %}
  <div class="checkout-options">
    <h3>Checkout Options</h3>
    <div class="option-cards">
      <div class="guest-checkout">
        <h4>Guest Checkout</h4>
        <p>Quick checkout without creating an account</p>
        <a href="/checkout" class="btn btn-primary">Continue as Guest</a>
      </div>
      <div class="account-checkout">
        <h4>Create Account</h4>
        <p>Save your information for faster future checkouts</p>
        <a href="/account/register" class="btn btn-secondary">Create Account</a>
      </div>
    </div>
  </div>
{% endguest %}
```

### Registration Incentives
```liquid
{% guest %}
  <div class="registration-incentive">
    <h3>Join Our Community</h3>
    <div class="benefits">
      <ul>
        <li>✓ Exclusive member discounts</li>
        <li>✓ Early access to sales</li>
        <li>✓ Order tracking and history</li>
        <li>✓ Personalized recommendations</li>
        <li>✓ Faster checkout process</li>
      </ul>
    </div>
    <div class="signup-form">
      <form action="/account/register" method="post">
        <input type="email" name="customer[email]" placeholder="Enter your email" required>
        <button type="submit" class="btn btn-primary">Get Started</button>
      </form>
    </div>
  </div>
{% endguest %}
```

### Social Login Options
```liquid
{% guest %}
  <div class="social-login">
    <h3>Quick Sign In</h3>
    <p>Use your existing social media accounts</p>
    <div class="social-buttons">
      <a href="/auth/google" class="btn btn-google">
        <i class="fab fa-google"></i> Continue with Google
      </a>
      <a href="/auth/facebook" class="btn btn-facebook">
        <i class="fab fa-facebook-f"></i> Continue with Facebook
      </a>
      <a href="/auth/apple" class="btn btn-apple">
        <i class="fab fa-apple"></i> Continue with Apple
      </a>
    </div>
    <div class="divider">
      <span>or</span>
    </div>
    <a href="/account/login" class="btn btn-outline">Sign in with Email</a>
  </div>
{% endguest %}
```

## Combined Authentication Logic

### Complete Authentication Flow
```liquid
<div class="user-section">
  {% auth %}
    <div class="authenticated-user">
      <div class="user-greeting">
        <span>Hello, {{ customer.first_name }}!</span>
        <div class="user-menu">
          <a href="/account">My Account</a>
          <a href="/account/orders">Orders</a>
          <a href="/account/logout">Logout</a>
        </div>
      </div>
      
      {% if customer.tags contains 'vip' %}
        <div class="vip-status">
          <span class="vip-badge">VIP Member</span>
          <p>Enjoy exclusive benefits and early access!</p>
        </div>
      {% endif %}
      
      {% if cart.item_count > 0 %}
        <div class="cart-reminder">
          <p>You have {{ cart.item_count }} items in your cart</p>
          <a href="/cart" class="btn btn-small">View Cart</a>
        </div>
      {% endif %}
    </div>
  {% endauth %}
  
  {% guest %}
    <div class="guest-user">
      <div class="welcome-message">
        <h3>Welcome to {{ shop.name }}!</h3>
        <p>Sign in to unlock exclusive features and personalized shopping.</p>
      </div>
      
      <div class="quick-actions">
        <a href="/account/login" class="btn btn-primary">Sign In</a>
        <a href="/account/register" class="btn btn-secondary">Register</a>
      </div>
      
      {% if cart.item_count > 0 %}
        <div class="guest-cart">
          <p>{{ cart.item_count }} items in cart</p>
          <p><small>Create an account to save your cart</small></p>
        </div>
      {% endif %}
    </div>
  {% endguest %}
</div>
```

### Conditional Product Actions
```liquid
<div class="product-actions">
  {% auth %}
    <div class="authenticated-actions">
      <button class="btn btn-primary add-to-cart" data-variant-id="{{ product.selected_or_first_available_variant.id }}">
        Add to Cart
      </button>
      
      <button class="btn btn-wishlist" data-product-id="{{ product.id }}">
        {% if customer.wishlist contains product.id %}
          ❤️ Remove from Wishlist
        {% else %}
          🤍 Add to Wishlist
        {% endif %}
      </button>
      
      {% if customer.orders contains product.id %}
        <div class="reorder-notice">
          <span>✓ You've purchased this before</span>
          <button class="btn btn-small btn-reorder">Reorder</button>
        </div>
      {% endif %}
    </div>
  {% endauth %}
  
  {% guest %}
    <div class="guest-actions">
      <button class="btn btn-primary add-to-cart" data-variant-id="{{ product.selected_or_first_available_variant.id }}">
        Add to Cart
      </button>
      
      <div class="wishlist-prompt">
        <p><small><a href="/account/login">Sign in</a> to save to wishlist</small></p>
      </div>
    </div>
  {% endguest %}
</div>
```

### Personalized Navigation
```liquid
<nav class="main-navigation">
  <ul class="nav-links">
    <li><a href="/">Home</a></li>
    <li><a href="/collections/all">Products</a></li>
    <li><a href="/pages/about">About</a></li>
    <li><a href="/pages/contact">Contact</a></li>
  </ul>
  
  <div class="user-nav">
    {% auth %}
      <div class="authenticated-nav">
        <a href="/account" class="account-link">
          <span>{{ customer.first_name }}</span>
          {% if customer.orders_count > 0 %}
            <small>({{ customer.orders_count }} orders)</small>
          {% endif %}
        </a>
        
        {% if customer.tags contains 'vip' %}
          <a href="/collections/vip" class="vip-link">VIP Collection</a>
        {% endif %}
        
        <a href="/cart" class="cart-link">
          Cart ({{ cart.item_count }})
        </a>
      </div>
    {% endauth %}
    
    {% guest %}
      <div class="guest-nav">
        <a href="/account/login" class="login-link">Sign In</a>
        <a href="/account/register" class="register-link">Register</a>
        <a href="/cart" class="cart-link">
          Cart ({{ cart.item_count }})
        </a>
      </div>
    {% endguest %}
  </div>
</nav>
```

## Best Practices

### Performance Considerations
1. **Cache customer data**: Use `assign` to store frequently accessed customer properties
2. **Minimize database queries**: Access customer properties efficiently
3. **Use conditional loading**: Only load customer-specific content when needed

### Security Best Practices
1. **Validate customer data**: Always validate customer input and data
2. **Protect sensitive information**: Don't expose sensitive customer data in templates
3. **Use proper escaping**: Escape customer-provided content to prevent XSS

### User Experience Guidelines
1. **Provide clear authentication states**: Make it obvious when users are logged in or out
2. **Offer easy authentication**: Provide multiple ways to sign in
3. **Personalize appropriately**: Use customer data to enhance, not overwhelm
4. **Graceful degradation**: Ensure functionality works for both authenticated and guest users

### Example: Complete Authentication Component
```liquid
{% comment %} Customer authentication component {% endcomment %}
<div class="auth-component">
  {% auth %}
    <div class="customer-section">
      {% assign customer_name = customer.first_name | default: customer.email %}
      <div class="customer-header">
        <h3>Welcome back, {{ customer_name }}!</h3>
        {% if customer.tags contains 'vip' %}
          <span class="status-badge vip">VIP</span>
        {% elsif customer.orders_count > 10 %}
          <span class="status-badge loyal">Loyal Customer</span>
        {% endif %}
      </div>
      
      <div class="customer-stats">
        <div class="stat">
          <span class="number">{{ customer.orders_count }}</span>
          <span class="label">Orders</span>
        </div>
        <div class="stat">
          <span class="number">{{ customer.total_spent | money_without_currency }}</span>
          <span class="label">Total Spent</span>
        </div>
      </div>
      
      <div class="quick-links">
        <a href="/account/orders">Recent Orders</a>
        <a href="/account/addresses">Addresses</a>
        <a href="/account/logout">Logout</a>
      </div>
    </div>
  {% endauth %}
  
  {% guest %}
    <div class="guest-section">
      <h3>Join {{ shop.name }}</h3>
      <p>Create an account for exclusive benefits and faster checkout.</p>
      
      <div class="auth-options">
        <a href="/account/register" class="btn btn-primary">Create Account</a>
        <a href="/account/login" class="btn btn-secondary">Sign In</a>
      </div>
      
      <div class="benefits-list">
        <ul>
          <li>Track your orders</li>
          <li>Save favorite products</li>
          <li>Faster checkout</li>
          <li>Exclusive offers</li>
        </ul>
      </div>
    </div>
  {% endguest %}
</div>
```
