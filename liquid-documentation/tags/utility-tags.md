# Utility Tags

Utility tags provide essential functionality for comments, raw content, liquid processing, and output in CloudCart Liquid templates.

## comment

Creates comments in Liquid templates that are not rendered in the output.

### Basic Syntax
```liquid
{% comment %}
  This is a comment and won't appear in the output
{% endcomment %}
```

### Single Line Comments
```liquid
{% comment %} This is a single line comment {% endcomment %}
```

### Multi-line Comments
```liquid
{% comment %}
  This is a multi-line comment
  that spans several lines
  and provides detailed explanations
{% endcomment %}
```

### Documentation Comments
```liquid
{% comment %}
  Product Card Component
  
  This component displays a product with:
  - Product image
  - Title and vendor
  - Price (with sale price if applicable)
  - Add to cart button
  
  Required variables:
  - product: Product object
  
  Optional variables:
  - show_vendor: Boolean (default: true)
  - image_size: String (default: '300x300')
{% endcomment %}

<div class="product-card">
  <!-- Product card content -->
</div>
```

### Code Organization Comments
```liquid
{% comment %} === HEADER SECTION === {% endcomment %}
<header class="site-header">
  <!-- Header content -->
</header>

{% comment %} === NAVIGATION === {% endcomment %}
<nav class="main-navigation">
  <!-- Navigation content -->
</nav>

{% comment %} === MAIN CONTENT === {% endcomment %}
<main class="main-content">
  {{ content_for_layout }}
</main>

{% comment %} === FOOTER SECTION === {% endcomment %}
<footer class="site-footer">
  <!-- Footer content -->
</footer>
```

### Debugging Comments
```liquid
{% comment %} 
  DEBUG: Current template - {{ template }}
  DEBUG: Customer logged in - {{ customer != null }}
  DEBUG: Cart item count - {{ cart.item_count }}
  DEBUG: Collection products count - {{ collection.products_count }}
{% endcomment %}
```

### TODO Comments
```liquid
{% comment %}
  TODO: Add product recommendations
  TODO: Implement wishlist functionality
  TODO: Add social sharing buttons
  FIXME: Fix mobile responsive issues
  NOTE: This section needs performance optimization
{% endcomment %}
```

## raw

Prevents Liquid from processing the content inside, outputting it exactly as written.

### Basic Syntax
```liquid
{% raw %}
  Content that should not be processed by Liquid
{% endraw %}
```

### Displaying Liquid Code
```liquid
{% raw %}
  <p>Use {{ product.title }} to display the product title</p>
  <p>Use {% if customer %} to check if customer is logged in</p>
{% endraw %}
```

### JavaScript with Liquid-like Syntax
```liquid
<script>
{% raw %}
  // This JavaScript won't be processed by Liquid
  const template = '{{ title }}';
  const data = { title: 'Hello World' };
  
  function renderTemplate(template, data) {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return data[key] || match;
    });
  }
  
  console.log(renderTemplate(template, data));
{% endraw %}
</script>
```

### Mustache Templates
```liquid
{% raw %}
<script id="product-template" type="text/x-handlebars-template">
  <div class="product">
    <h3>{{title}}</h3>
    <p>{{description}}</p>
    <span class="price">${{price}}</span>
  </div>
</script>
{% endraw %}
```

### Code Examples in Documentation
```liquid
<div class="code-example">
  <h4>Liquid Code:</h4>
  <pre><code>{% raw %}
{% for product in collection.products %}
  <div class="product">
    <h3>{{ product.title }}</h3>
    <p>{{ product.price | money }}</p>
  </div>
{% endfor %}
  {% endraw %}</code></pre>
</div>
```

## liquid

Processes multiple Liquid operations in a single block, improving performance and readability.

### Basic Syntax
```liquid
{% liquid
  # Liquid operations
%}
```

### Variable Assignments
```liquid
{% liquid
  assign product_count = collection.products.size
  assign has_products = product_count > 0
  assign show_pagination = product_count > 12
  assign current_page = request.page | default: 1
%}

{% if has_products %}
  <p>Showing {{ product_count }} products</p>
  
  {% if show_pagination %}
    <div class="pagination-info">
      Page {{ current_page }}
    </div>
  {% endif %}
{% endif %}
```

### Complex Logic
```liquid
{% liquid
  # Calculate discount information
  assign original_price = product.compare_at_price | default: product.price
  assign current_price = product.price
  assign discount_amount = original_price | minus: current_price
  assign discount_percentage = discount_amount | times: 100 | divided_by: original_price | round
  assign has_discount = discount_percentage > 0
  
  # Determine product availability
  assign is_available = product.available
  assign low_stock = product.inventory_quantity <= 5 and product.inventory_quantity > 0
  assign out_of_stock = product.inventory_quantity <= 0
  
  # Set display classes
  assign product_classes = 'product-card'
  if has_discount
    assign product_classes = product_classes | append: ' on-sale'
  endif
  if low_stock
    assign product_classes = product_classes | append: ' low-stock'
  endif
  if out_of_stock
    assign product_classes = product_classes | append: ' out-of-stock'
  endif
%}

<div class="{{ product_classes }}">
  <h3>{{ product.title }}</h3>
  
  {% if has_discount %}
    <div class="price-container">
      <span class="sale-price">{{ current_price | money }}</span>
      <span class="original-price">{{ original_price | money }}</span>
      <span class="discount-badge">{{ discount_percentage }}% OFF</span>
    </div>
  {% else %}
    <span class="regular-price">{{ current_price | money }}</span>
  {% endif %}
  
  {% if low_stock %}
    <p class="stock-warning">Only {{ product.inventory_quantity }} left!</p>
  {% elsif out_of_stock %}
    <p class="out-of-stock">Out of stock</p>
  {% endif %}
</div>
```

### Loop Processing
```liquid
{% liquid
  # Process collection data
  assign featured_products = ''
  assign sale_products = ''
  assign new_products = ''
  
  for product in collection.products
    if product.tags contains 'featured'
      assign featured_products = featured_products | append: product.id | append: ','
    endif
    
    if product.compare_at_price > product.price
      assign sale_products = sale_products | append: product.id | append: ','
    endif
    
    assign created_days_ago = 'now' | date: '%s' | minus: product.created_at | date: '%s' | divided_by: 86400
    if created_days_ago <= 30
      assign new_products = new_products | append: product.id | append: ','
    endif
  endfor
  
  # Convert to arrays
  assign featured_ids = featured_products | split: ','
  assign sale_ids = sale_products | split: ','
  assign new_ids = new_products | split: ','
%}

<div class="collection-stats">
  <p>Featured Products: {{ featured_ids.size }}</p>
  <p>Sale Products: {{ sale_ids.size }}</p>
  <p>New Products: {{ new_ids.size }}</p>
</div>
```

## echo

Outputs the result of an expression, similar to `{{ }}` but can be used within liquid blocks.

### Basic Syntax
```liquid
{% liquid
  echo 'Hello World'
%}
```

### Variable Output
```liquid
{% liquid
  assign greeting = 'Hello'
  assign name = 'World'
  echo greeting | append: ' ' | append: name
%}
```

### Conditional Output
```liquid
{% liquid
  if customer
    echo 'Welcome back, ' | append: customer.first_name
  else
    echo 'Welcome, Guest'
  endif
%}
```

### Complex Expressions
```liquid
{% liquid
  assign base_price = product.price
  assign tax_rate = 0.08
  assign tax_amount = base_price | times: tax_rate
  assign total_price = base_price | plus: tax_amount
  
  echo 'Price: ' | append: base_price | money
  echo '<br>Tax: ' | append: tax_amount | money
  echo '<br>Total: ' | append: total_price | money
%}
```

### Loop Output
```liquid
{% liquid
  echo '<ul class="product-list">'
  
  for product in collection.products limit: 5
    echo '<li>'
    echo '<a href="' | append: product.url | append: '">'
    echo product.title
    echo '</a> - '
    echo product.price | money
    echo '</li>'
  endfor
  
  echo '</ul>'
%}
```

## Practical Examples

### Template Documentation
```liquid
{% comment %}
  ===================================================================
  PRODUCT CARD COMPONENT
  ===================================================================
  
  Description:
  Displays a product card with image, title, price, and actions
  
  Required Variables:
  - product (object): Product object from collection
  
  Optional Variables:
  - show_vendor (boolean): Display product vendor (default: true)
  - show_compare_price (boolean): Show compare at price (default: true)
  - image_size (string): Image dimensions (default: '300x300')
  - card_class (string): Additional CSS classes
  
  Usage:
  {% render 'product-card', 
     product: product, 
     show_vendor: true,
     image_size: '400x400' %}
  
  Last Updated: 2024-01-15
  Author: Development Team
  ===================================================================
{% endcomment %}

{% liquid
  # Set default values
  assign show_vendor = show_vendor | default: true
  assign show_compare_price = show_compare_price | default: true
  assign image_size = image_size | default: '300x300'
  assign card_class = card_class | default: ''
  
  # Calculate pricing
  assign has_compare_price = product.compare_at_price > product.price
  assign discount_amount = product.compare_at_price | minus: product.price
  assign discount_percentage = discount_amount | times: 100 | divided_by: product.compare_at_price | round
%}

<div class="product-card {{ card_class }}">
  <!-- Product card implementation -->
</div>
```

### Performance Optimization
```liquid
{% comment %}
  Performance optimization: Use liquid block for multiple operations
  instead of individual Liquid tags to reduce processing overhead
{% endcomment %}

{% liquid
  # Cache frequently used values
  assign cart_count = cart.item_count
  assign is_customer = customer != null
  assign current_url = request.path
  assign shop_name = shop.name
  
  # Build navigation classes
  assign nav_classes = 'main-nav'
  if cart_count > 0
    assign nav_classes = nav_classes | append: ' has-items'
  endif
  if is_customer
    assign nav_classes = nav_classes | append: ' logged-in'
  endif
  
  # Generate meta information
  assign page_title = page_title | default: shop_name
  assign meta_description = page_description | default: shop.description | truncate: 160
%}

<nav class="{{ nav_classes }}">
  <!-- Navigation content using cached values -->
</nav>
```

### Debugging and Development
```liquid
{% comment %} === DEVELOPMENT DEBUG INFO === {% endcomment %}
{% if settings.debug_mode %}
  {% raw %}
  <div class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px;">
    <h4>Debug Information</h4>
    <p><strong>Template:</strong> {{ template }}</p>
    <p><strong>Request URL:</strong> {{ request.path }}</p>
    <p><strong>Customer:</strong> {{ customer.email | default: 'Not logged in' }}</p>
    <p><strong>Cart Items:</strong> {{ cart.item_count }}</p>
    {% if product %}
      <p><strong>Product ID:</strong> {{ product.id }}</p>
      <p><strong>Product Handle:</strong> {{ product.handle }}</p>
    {% endif %}
    {% if collection %}
      <p><strong>Collection:</strong> {{ collection.handle }}</p>
      <p><strong>Products Count:</strong> {{ collection.products_count }}</p>
    {% endif %}
  </div>
  {% endraw %}
{% endif %}
```

### Code Generation
```liquid
{% comment %} Generate structured data for SEO {% endcomment %}
{% liquid
  if template contains 'product'
    assign structured_data = '{'
    assign structured_data = structured_data | append: '"@context": "https://schema.org/",'
    assign structured_data = structured_data | append: '"@type": "Product",'
    assign structured_data = structured_data | append: '"name": "' | append: product.title | escape | append: '",'
    assign structured_data = structured_data | append: '"description": "' | append: product.description | strip_html | escape | append: '",'
    assign structured_data = structured_data | append: '"image": "' | append: product.featured_image | img_url: 'master' | append: '",'
    assign structured_data = structured_data | append: '"offers": {'
    assign structured_data = structured_data | append: '"@type": "Offer",'
    assign structured_data = structured_data | append: '"price": "' | append: product.price | divided_by: 100 | append: '",'
    assign structured_data = structured_data | append: '"priceCurrency": "' | append: shop.currency | append: '",'
    if product.available
      assign structured_data = structured_data | append: '"availability": "https://schema.org/InStock"'
    else
      assign structured_data = structured_data | append: '"availability": "https://schema.org/OutOfStock"'
    endif
    assign structured_data = structured_data | append: '}}'
    
    echo '<script type="application/ld+json">'
    echo structured_data
    echo '</script>'
  endif
%}
```

## Best Practices

### Comments
1. **Document complex logic**: Explain why, not just what
2. **Use consistent formatting**: Maintain readable comment structure
3. **Keep comments updated**: Remove or update outdated comments
4. **Use TODO/FIXME**: Mark areas that need attention

### Raw Content
1. **Minimize usage**: Only use when necessary to avoid processing
2. **Security considerations**: Be careful with user-generated content
3. **Performance impact**: Raw blocks still need to be parsed

### Liquid Blocks
1. **Group related operations**: Combine multiple assignments and calculations
2. **Improve readability**: Use for complex logic that would span multiple tags
3. **Performance benefits**: Reduce the number of Liquid tag evaluations

### Echo Statements
1. **Use within liquid blocks**: Primary use case for echo is within `{% liquid %}` blocks
2. **Complex expressions**: Useful for building complex output strings
3. **Conditional output**: Better than multiple conditional blocks for simple output
