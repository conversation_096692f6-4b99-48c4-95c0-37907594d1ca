# Advanced Tags

Advanced tags provide sophisticated functionality for pagination, JavaScript integration, content management, and dynamic template features.

## paginate

Creates pagination for collections, blog articles, and search results.

### Basic Syntax
```liquid
{% paginate collection.products by 12 %}
  <!-- paginated content -->
  {{ paginate | default_pagination }}
{% endpaginate %}
```

### Collection Pagination
```liquid
{% paginate collection.products by 16 %}
  <div class="collection-header">
    <h1>{{ collection.title }}</h1>
    <p>{{ paginate.items }} products</p>
  </div>
  
  <div class="product-grid">
    {% for product in collection.products %}
      <div class="product-card">
        <a href="{{ product.url }}">
          <img src="{{ product.featured_image | img_url: '300x300' }}" alt="{{ product.title }}">
          <h3>{{ product.title }}</h3>
          <p>{{ product.price | money }}</p>
        </a>
      </div>
    {% endfor %}
  </div>
  
  {% if paginate.pages > 1 %}
    <nav class="pagination" aria-label="Pagination">
      {{ paginate | default_pagination }}
    </nav>
  {% endif %}
{% endpaginate %}
```

### Blog Pagination
```liquid
{% paginate blog.articles by 5 %}
  <div class="blog-header">
    <h1>{{ blog.title }}</h1>
    <p>{{ paginate.items }} articles</p>
  </div>
  
  <div class="articles-list">
    {% for article in blog.articles %}
      <article class="article-summary">
        {% if article.image %}
          <div class="article-image">
            <a href="{{ article.url }}">
              <img src="{{ article.image | img_url: '400x250' }}" alt="{{ article.title }}">
            </a>
          </div>
        {% endif %}
        
        <div class="article-content">
          <h2><a href="{{ article.url }}">{{ article.title }}</a></h2>
          <div class="article-meta">
            <time datetime="{{ article.published_at | date_to_xmlschema }}">
              {{ article.published_at | date: '%B %d, %Y' }}
            </time>
            {% if article.author %}
              <span class="author">by {{ article.author }}</span>
            {% endif %}
          </div>
          <div class="article-excerpt">
            {{ article.excerpt | truncatewords: 50 }}
          </div>
          <a href="{{ article.url }}" class="read-more">Read More</a>
        </div>
      </article>
    {% endfor %}
  </div>
  
  {% if paginate.pages > 1 %}
    <nav class="pagination">
      {{ paginate | default_pagination }}
    </nav>
  {% endif %}
{% endpaginate %}
```

### Search Results Pagination
```liquid
{% paginate search.results by 20 %}
  <div class="search-header">
    <h1>Search Results</h1>
    {% if search.terms %}
      <p>{{ paginate.items }} results for "{{ search.terms }}"</p>
    {% else %}
      <p>{{ paginate.items }} total results</p>
    {% endif %}
  </div>
  
  <div class="search-results">
    {% for item in search.results %}
      <div class="search-result">
        {% case item.object_type %}
          {% when 'product' %}
            <div class="result-product">
              <a href="{{ item.url }}">
                <img src="{{ item.featured_image | img_url: '100x100' }}" alt="{{ item.title }}">
              </a>
              <div class="result-content">
                <h3><a href="{{ item.url }}">{{ item.title }}</a></h3>
                <p class="result-type">Product</p>
                <p class="result-price">{{ item.price | money }}</p>
              </div>
            </div>
            
          {% when 'article' %}
            <div class="result-article">
              <div class="result-content">
                <h3><a href="{{ item.url }}">{{ item.title }}</a></h3>
                <p class="result-type">Article</p>
                <p class="result-excerpt">{{ item.excerpt | truncatewords: 20 }}</p>
              </div>
            </div>
            
          {% when 'page' %}
            <div class="result-page">
              <div class="result-content">
                <h3><a href="{{ item.url }}">{{ item.title }}</a></h3>
                <p class="result-type">Page</p>
                <p class="result-excerpt">{{ item.content | strip_html | truncatewords: 20 }}</p>
              </div>
            </div>
        {% endcase %}
      </div>
    {% endfor %}
  </div>
  
  {% if paginate.pages > 1 %}
    <nav class="pagination">
      {{ paginate | default_pagination }}
    </nav>
  {% endif %}
{% endpaginate %}
```

### Custom Pagination Controls
```liquid
{% paginate collection.products by 12 %}
  <!-- Product grid content -->
  
  {% if paginate.pages > 1 %}
    <nav class="custom-pagination">
      <div class="pagination-info">
        <p>
          Showing {{ paginate.current_offset | plus: 1 }}-{{ paginate.current_offset | plus: paginate.page_size | at_most: paginate.items }} 
          of {{ paginate.items }} products
        </p>
      </div>
      
      <ul class="pagination-list">
        {% if paginate.previous %}
          <li class="pagination-item">
            <a href="{{ paginate.previous.url }}" class="pagination-link" aria-label="Previous page">
              &laquo; Previous
            </a>
          </li>
        {% endif %}
        
        {% for part in paginate.parts %}
          {% if part.is_link %}
            <li class="pagination-item">
              <a href="{{ part.url }}" class="pagination-link">{{ part.title }}</a>
            </li>
          {% else %}
            {% if part.title == paginate.current_page %}
              <li class="pagination-item">
                <span class="pagination-link current" aria-current="page">{{ part.title }}</span>
              </li>
            {% else %}
              <li class="pagination-item">
                <span class="pagination-ellipsis">{{ part.title }}</span>
              </li>
            {% endif %}
          {% endif %}
        {% endfor %}
        
        {% if paginate.next %}
          <li class="pagination-item">
            <a href="{{ paginate.next.url }}" class="pagination-link" aria-label="Next page">
              Next &raquo;
            </a>
          </li>
        {% endif %}
      </ul>
      
      <div class="pagination-jump">
        <form action="{{ request.path }}" method="get" class="page-jump-form">
          {% for param in request.params %}
            {% unless param[0] == 'page' %}
              <input type="hidden" name="{{ param[0] }}" value="{{ param[1] }}">
            {% endunless %}
          {% endfor %}
          <label for="page-jump">Go to page:</label>
          <input type="number" id="page-jump" name="page" min="1" max="{{ paginate.pages }}" value="{{ paginate.current_page }}">
          <button type="submit">Go</button>
        </form>
      </div>
    </nav>
  {% endif %}
{% endpaginate %}
```

## javascript

Embeds JavaScript code with proper script tags and optimization.

### Basic Syntax
```liquid
{% javascript %}
  // JavaScript code
{% endjavascript %}
```

### Simple JavaScript
```liquid
{% javascript %}
  console.log('Hello from CloudCart Liquid!');
  
  document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded');
  });
{% endjavascript %}
```

### Product Interaction JavaScript
```liquid
{% javascript %}
  // Product variant selection
  class ProductVariants {
    constructor(productId) {
      this.productId = productId;
      this.form = document.querySelector(`#product-form-${productId}`);
      this.variantSelects = this.form.querySelectorAll('.variant-select');
      this.priceElement = this.form.querySelector('.product-price');
      this.addToCartButton = this.form.querySelector('.add-to-cart-btn');
      
      this.init();
    }
    
    init() {
      this.variantSelects.forEach(select => {
        select.addEventListener('change', () => this.updateVariant());
      });
    }
    
    updateVariant() {
      const selectedOptions = Array.from(this.variantSelects).map(select => select.value);
      const variant = this.findVariant(selectedOptions);
      
      if (variant) {
        this.updatePrice(variant.price);
        this.updateAvailability(variant.available);
        this.updateVariantId(variant.id);
      }
    }
    
    findVariant(selectedOptions) {
      return window.productVariants[this.productId].find(variant => {
        return variant.options.every((option, index) => option === selectedOptions[index]);
      });
    }
    
    updatePrice(price) {
      this.priceElement.textContent = this.formatMoney(price);
    }
    
    updateAvailability(available) {
      this.addToCartButton.disabled = !available;
      this.addToCartButton.textContent = available ? 'Add to Cart' : 'Sold Out';
    }
    
    updateVariantId(variantId) {
      const variantInput = this.form.querySelector('input[name="id"]');
      variantInput.value = variantId;
    }
    
    formatMoney(cents) {
      return (cents / 100).toLocaleString('en-US', {
        style: 'currency',
        currency: '{{ shop.currency }}'
      });
    }
  }
  
  // Initialize product variants
  document.addEventListener('DOMContentLoaded', function() {
    {% if product %}
      new ProductVariants({{ product.id }});
    {% endif %}
  });
{% endjavascript %}
```

### Cart Functionality
```liquid
{% javascript %}
  // Cart management
  class CartManager {
    constructor() {
      this.cartDrawer = document.querySelector('.cart-drawer');
      this.cartCount = document.querySelector('.cart-count');
      this.cartTotal = document.querySelector('.cart-total');
      
      this.bindEvents();
    }
    
    bindEvents() {
      // Add to cart buttons
      document.addEventListener('click', (e) => {
        if (e.target.matches('.add-to-cart-btn')) {
          e.preventDefault();
          this.addToCart(e.target.closest('form'));
        }
      });
      
      // Cart quantity updates
      document.addEventListener('change', (e) => {
        if (e.target.matches('.cart-quantity-input')) {
          this.updateQuantity(e.target);
        }
      });
      
      // Remove from cart
      document.addEventListener('click', (e) => {
        if (e.target.matches('.remove-from-cart')) {
          e.preventDefault();
          this.removeFromCart(e.target.dataset.lineItem);
        }
      });
    }
    
    async addToCart(form) {
      const formData = new FormData(form);
      
      try {
        const response = await fetch('/cart/add.js', {
          method: 'POST',
          body: formData
        });
        
        if (response.ok) {
          const item = await response.json();
          this.updateCartUI();
          this.showCartDrawer();
          this.showNotification(`Added ${item.title} to cart`);
        } else {
          throw new Error('Failed to add item to cart');
        }
      } catch (error) {
        this.showNotification('Error adding item to cart', 'error');
      }
    }
    
    async updateQuantity(input) {
      const lineItem = input.dataset.lineItem;
      const quantity = parseInt(input.value);
      
      try {
        const response = await fetch('/cart/change.js', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            line: lineItem,
            quantity: quantity
          })
        });
        
        if (response.ok) {
          this.updateCartUI();
        }
      } catch (error) {
        this.showNotification('Error updating cart', 'error');
      }
    }
    
    async removeFromCart(lineItem) {
      try {
        const response = await fetch('/cart/change.js', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            line: lineItem,
            quantity: 0
          })
        });
        
        if (response.ok) {
          this.updateCartUI();
        }
      } catch (error) {
        this.showNotification('Error removing item', 'error');
      }
    }
    
    async updateCartUI() {
      try {
        const response = await fetch('/cart.js');
        const cart = await response.json();
        
        this.cartCount.textContent = cart.item_count;
        this.cartTotal.textContent = this.formatMoney(cart.total_price);
        
        // Update cart drawer content
        this.refreshCartDrawer();
      } catch (error) {
        console.error('Error updating cart UI:', error);
      }
    }
    
    showCartDrawer() {
      this.cartDrawer.classList.add('open');
    }
    
    hideCartDrawer() {
      this.cartDrawer.classList.remove('open');
    }
    
    showNotification(message, type = 'success') {
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.textContent = message;
      
      document.body.appendChild(notification);
      
      setTimeout(() => {
        notification.remove();
      }, 3000);
    }
    
    formatMoney(cents) {
      return (cents / 100).toLocaleString('en-US', {
        style: 'currency',
        currency: '{{ shop.currency }}'
      });
    }
    
    async refreshCartDrawer() {
      try {
        const response = await fetch('/cart?view=drawer');
        const html = await response.text();
        this.cartDrawer.innerHTML = html;
      } catch (error) {
        console.error('Error refreshing cart drawer:', error);
      }
    }
  }
  
  // Initialize cart manager
  document.addEventListener('DOMContentLoaded', function() {
    new CartManager();
  });
{% endjavascript %}
```

## content_for

Captures content to be rendered in a specific area of the layout.

### Basic Syntax
```liquid
{% content_for 'area_name' %}
  <!-- content to capture -->
{% endcontent_for %}
```

### Page-Specific Styles
```liquid
{% comment %} In a product template {% endcomment %}
{% content_for 'head' %}
  <style>
    .product-gallery {
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: 2rem;
    }
    
    .product-thumbnails {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
  </style>
{% endcontent_for %}

{% comment %} In the layout {% endcomment %}
<head>
  <title>{{ page_title }}</title>
  {{ content_for_head }}
</head>
```

### Page-Specific JavaScript
```liquid
{% comment %} In a collection template {% endcomment %}
{% content_for 'scripts' %}
  <script>
    // Collection-specific filtering
    document.addEventListener('DOMContentLoaded', function() {
      const filterForm = document.querySelector('.collection-filters');
      const productGrid = document.querySelector('.product-grid');
      
      filterForm.addEventListener('change', function() {
        // Apply filters via AJAX
        applyFilters();
      });
      
      function applyFilters() {
        const formData = new FormData(filterForm);
        const params = new URLSearchParams(formData);
        
        fetch(`{{ collection.url }}?${params.toString()}`)
          .then(response => response.text())
          .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newGrid = doc.querySelector('.product-grid');
            productGrid.innerHTML = newGrid.innerHTML;
          });
      }
    });
  </script>
{% endcontent_for %}

{% comment %} In the layout {% endcomment %}
<body>
  {{ content_for_layout }}
  {{ content_for_scripts }}
</body>
```

### Meta Tags
```liquid
{% comment %} In a product template {% endcomment %}
{% content_for 'meta_tags' %}
  <meta property="og:type" content="product">
  <meta property="og:title" content="{{ product.title | escape }}">
  <meta property="og:description" content="{{ product.description | strip_html | truncate: 160 | escape }}">
  <meta property="og:image" content="{{ product.featured_image | img_url: '1200x630' }}">
  <meta property="og:url" content="{{ shop.url }}{{ product.url }}">
  
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="{{ product.title | escape }}">
  <meta name="twitter:description" content="{{ product.description | strip_html | truncate: 160 | escape }}">
  <meta name="twitter:image" content="{{ product.featured_image | img_url: '1200x630' }}">
  
  <script type="application/ld+json">
  {
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": "{{ product.title | escape }}",
    "description": "{{ product.description | strip_html | escape }}",
    "image": "{{ product.featured_image | img_url: 'master' }}",
    "offers": {
      "@type": "Offer",
      "price": "{{ product.price | divided_by: 100 }}",
      "priceCurrency": "{{ shop.currency }}",
      "availability": "{% if product.available %}https://schema.org/InStock{% else %}https://schema.org/OutOfStock{% endif %}"
    }
  }
  </script>
{% endcontent_for %}

{% comment %} In the layout {% endcomment %}
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  {{ content_for_meta_tags }}
</head>
```

## yield

Renders content that was captured with content_for.

### Basic Syntax
```liquid
{% yield 'area_name' %}
```

### Layout Implementation
```liquid
{% comment %} In layout/theme.liquid {% endcomment %}
<!DOCTYPE html>
<html>
<head>
  <title>{% if page_title %}{{ page_title }} - {% endif %}{{ shop.name }}</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  
  {% yield 'meta_tags' %}
  {% yield 'head' %}
  
  {{ 'theme.css' | asset_url | stylesheet_tag }}
</head>
<body>
  <header class="site-header">
    {% section 'header' %}
  </header>
  
  <main class="main-content">
    {{ content_for_layout }}
  </main>
  
  <footer class="site-footer">
    {% section 'footer' %}
  </footer>
  
  {{ 'theme.js' | asset_url | script_tag }}
  {% yield 'scripts' %}
</body>
</html>
```

### Conditional Content Areas
```liquid
{% comment %} In layout {% endcomment %}
{% if content_for_sidebar %}
  <div class="layout-with-sidebar">
    <main class="main-content">
      {{ content_for_layout }}
    </main>
    <aside class="sidebar">
      {% yield 'sidebar' %}
    </aside>
  </div>
{% else %}
  <div class="layout-full-width">
    <main class="main-content">
      {{ content_for_layout }}
    </main>
  </div>
{% endif %}
```

## Best Practices

### Pagination
1. **Appropriate page sizes**: Use reasonable items per page (12-24 for products)
2. **SEO considerations**: Ensure paginated pages are crawlable
3. **Performance**: Don't paginate unnecessarily small collections
4. **User experience**: Provide clear navigation and page information

### JavaScript
1. **Progressive enhancement**: Ensure functionality works without JavaScript
2. **Performance**: Minimize JavaScript payload and use async loading
3. **Error handling**: Always include try-catch blocks for API calls
4. **Accessibility**: Ensure JavaScript interactions are keyboard accessible

### Content Areas
1. **Semantic naming**: Use descriptive names for content areas
2. **Default content**: Provide fallbacks when content areas are empty
3. **Performance**: Don't overuse content_for for simple includes
4. **Organization**: Keep related content together in logical areas
