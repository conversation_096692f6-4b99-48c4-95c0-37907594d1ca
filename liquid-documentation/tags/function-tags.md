# Function Tags

CloudCart Liquid provides powerful function tags that allow you to create reusable template functions, improving code organization and maintainability.

## function

The `function` tag defines a reusable block of Liquid code that can be called multiple times with different parameters.

### Basic Syntax
```liquid
{% function function_name %}
  <!-- function content -->
{% endfunction %}
```

### Simple Function Example
```liquid
{% function greeting %}
  <div class="welcome-message">
    <h2>Welcome to our store!</h2>
    <p>Discover amazing products at great prices.</p>
  </div>
{% endfunction %}
```

### Function with Variables
Functions have access to the current template context and can use any available variables.

```liquid
{% function product_badge %}
  {% if product.available %}
    <span class="badge available">In Stock</span>
  {% else %}
    <span class="badge sold-out">Sold Out</span>
  {% endif %}
  
  {% if product.compare_at_price > product.price %}
    <span class="badge sale">Sale</span>
  {% endif %}
{% endfunction %}
```

### Complex Function Example
```liquid
{% function product_card_layout %}
  <div class="product-card">
    <div class="product-image">
      <img src="{{ product.featured_image | img_url: '300x300' }}" alt="{{ product.title }}">
      {% if product.images.size > 1 %}
        <div class="image-count">+{{ product.images.size | minus: 1 }}</div>
      {% endif %}
    </div>
    
    <div class="product-info">
      <h3 class="product-title">{{ product.title }}</h3>
      <p class="product-vendor">{{ product.vendor }}</p>
      
      <div class="product-price">
        {% if product.compare_at_price > product.price %}
          <span class="sale-price">{{ product.price | money }}</span>
          <span class="original-price">{{ product.compare_at_price | money }}</span>
        {% else %}
          <span class="regular-price">{{ product.price | money }}</span>
        {% endif %}
      </div>
      
      <div class="product-actions">
        {% if product.available %}
          <button class="btn btn-primary" data-product-id="{{ product.id }}">
            Add to Cart
          </button>
        {% else %}
          <button class="btn btn-disabled" disabled>
            Sold Out
          </button>
        {% endif %}
      </div>
    </div>
  </div>
{% endfunction %}
```

### Function with Conditional Logic
```liquid
{% function customer_pricing %}
  <div class="pricing-info">
    {% if customer %}
      {% if customer.tags contains 'wholesale' %}
        <div class="wholesale-pricing">
          <span class="label">Wholesale Price:</span>
          <span class="price">{{ product.price | times: 0.7 | money }}</span>
          <small class="savings">Save 30%</small>
        </div>
      {% elsif customer.tags contains 'vip' %}
        <div class="vip-pricing">
          <span class="label">VIP Price:</span>
          <span class="price">{{ product.price | times: 0.85 | money }}</span>
          <small class="savings">Save 15%</small>
        </div>
      {% else %}
        <div class="regular-pricing">
          <span class="price">{{ product.price | money }}</span>
        </div>
      {% endif %}
    {% else %}
      <div class="guest-pricing">
        <span class="price">{{ product.price | money }}</span>
        <small><a href="/account/login">Sign in</a> for member pricing</small>
      </div>
    {% endif %}
  </div>
{% endfunction %}
```

## call

The `call` tag executes a previously defined function, optionally passing parameters.

### Basic Syntax
```liquid
{% call "function_name" %}
```

### Simple Function Call
```liquid
{% comment %} Define the function {% endcomment %}
{% function store_hours %}
  <div class="store-hours">
    <h4>Store Hours</h4>
    <ul>
      <li>Monday - Friday: 9:00 AM - 8:00 PM</li>
      <li>Saturday: 10:00 AM - 6:00 PM</li>
      <li>Sunday: 12:00 PM - 5:00 PM</li>
    </ul>
  </div>
{% endfunction %}

{% comment %} Call the function {% endcomment %}
{% call "store_hours" %}
```

### Function with Parameters
```liquid
{% comment %} Define function that uses parameters {% endcomment %}
{% function social_share_button %}
  <div class="social-share">
    <a href="https://facebook.com/sharer/sharer.php?u={{ share_url | url_encode }}" 
       class="share-facebook" 
       target="_blank">
      Share on Facebook
    </a>
    <a href="https://twitter.com/intent/tweet?url={{ share_url | url_encode }}&text={{ share_text | url_encode }}" 
       class="share-twitter" 
       target="_blank">
      Share on Twitter
    </a>
  </div>
{% endfunction %}

{% comment %} Call function with parameters {% endcomment %}
{% assign share_url = shop.url | append: product.url %}
{% assign share_text = 'Check out this amazing product: ' | append: product.title %}
{% call "social_share_button" %}
```

### Function Call with Variable Assignment
```liquid
{% comment %} Function that generates content {% endcomment %}
{% function breadcrumb_navigation %}
  <nav class="breadcrumbs">
    <a href="/">Home</a>
    {% if collection %}
      <span class="separator">/</span>
      <a href="{{ collection.url }}">{{ collection.title }}</a>
    {% endif %}
    {% if product %}
      <span class="separator">/</span>
      <span class="current">{{ product.title }}</span>
    {% endif %}
  </nav>
{% endfunction %}

{% comment %} Call and use the function {% endcomment %}
<header class="page-header">
  {% call "breadcrumb_navigation" %}
</header>
```

## Advanced Function Patterns

### Parameterized Functions
While Liquid functions don't have formal parameters, you can use variable assignment to pass data:

```liquid
{% comment %} Function that uses "parameters" via variables {% endcomment %}
{% function price_display %}
  <div class="price-container">
    {% if show_compare_price and compare_price > current_price %}
      <span class="sale-price">{{ current_price | money }}</span>
      <span class="original-price">{{ compare_price | money }}</span>
      {% assign savings = compare_price | minus: current_price %}
      <span class="savings">Save {{ savings | money }}</span>
    {% else %}
      <span class="regular-price">{{ current_price | money }}</span>
    {% endif %}
  </div>
{% endfunction %}

{% comment %} Call with "parameters" {% endcomment %}
{% assign current_price = product.price %}
{% assign compare_price = product.compare_at_price %}
{% assign show_compare_price = true %}
{% call "price_display" %}
```

### Conditional Function Calls
```liquid
{% comment %} Define multiple layout functions {% endcomment %}
{% function grid_layout %}
  <div class="product-grid">
    {% for product in collection.products %}
      <div class="grid-item">
        {% call "product_card_layout" %}
      </div>
    {% endfor %}
  </div>
{% endfunction %}

{% function list_layout %}
  <div class="product-list">
    {% for product in collection.products %}
      <div class="list-item">
        <img src="{{ product.featured_image | img_url: '150x150' }}" alt="{{ product.title }}">
        <div class="product-details">
          <h3>{{ product.title }}</h3>
          <p>{{ product.price | money }}</p>
        </div>
      </div>
    {% endfor %}
  </div>
{% endfunction %}

{% comment %} Conditional function call based on user preference {% endcomment %}
{% if settings.product_layout == 'grid' %}
  {% call "grid_layout" %}
{% else %}
  {% call "list_layout" %}
{% endif %}
```

### Nested Function Calls
```liquid
{% comment %} Base card function {% endcomment %}
{% function base_card %}
  <div class="card">
    <div class="card-content">
      {{ card_content }}
    </div>
  </div>
{% endfunction %}

{% comment %} Product-specific card function {% endcomment %}
{% function product_info_card %}
  {% capture card_content %}
    <h3>{{ product.title }}</h3>
    <p>{{ product.description | truncate: 100 }}</p>
    <span class="price">{{ product.price | money }}</span>
  {% endcapture %}
  {% call "base_card" %}
{% endfunction %}

{% comment %} Article-specific card function {% endcomment %}
{% function article_info_card %}
  {% capture card_content %}
    <h3>{{ article.title }}</h3>
    <p>{{ article.excerpt | truncate: 100 }}</p>
    <time>{{ article.published_at | date: '%B %d, %Y' }}</time>
  {% endcapture %}
  {% call "base_card" %}
{% endfunction %}
```

### Dynamic Function Selection
```liquid
{% comment %} Define different button styles {% endcomment %}
{% function primary_button %}
  <button class="btn btn-primary {{ button_class }}" {{ button_attributes }}>
    {{ button_text }}
  </button>
{% endfunction %}

{% function secondary_button %}
  <button class="btn btn-secondary {{ button_class }}" {{ button_attributes }}>
    {{ button_text }}
  </button>
{% endfunction %}

{% function danger_button %}
  <button class="btn btn-danger {{ button_class }}" {{ button_attributes }}>
    {{ button_text }}
  </button>
{% endfunction %}

{% comment %} Dynamic function call {% endcomment %}
{% assign button_text = 'Add to Cart' %}
{% assign button_class = 'add-to-cart' %}
{% assign button_attributes = 'data-product-id="' | append: product.id | append: '"' %}

{% case button_style %}
  {% when 'primary' %}
    {% call "primary_button" %}
  {% when 'secondary' %}
    {% call "secondary_button" %}
  {% when 'danger' %}
    {% call "danger_button" %}
  {% else %}
    {% call "primary_button" %}
{% endcase %}
```

## Practical Examples

### Reusable Product Components
```liquid
{% comment %} Product rating function {% endcomment %}
{% function product_rating %}
  {% if product.reviews_count > 0 %}
    <div class="product-rating">
      <div class="stars">
        {% assign rating = product.rating | round %}
        {% for i in (1..5) %}
          {% if i <= rating %}
            <span class="star filled">★</span>
          {% else %}
            <span class="star empty">☆</span>
          {% endif %}
        {% endfor %}
      </div>
      <span class="rating-count">({{ product.reviews_count }})</span>
    </div>
  {% endif %}
{% endfunction %}

{% comment %} Product availability function {% endcomment %}
{% function product_availability %}
  <div class="availability-info">
    {% if product.available %}
      {% if product.inventory_quantity <= 5 and product.inventory_quantity > 0 %}
        <span class="low-stock">Only {{ product.inventory_quantity }} left!</span>
      {% else %}
        <span class="in-stock">In Stock</span>
      {% endif %}
    {% else %}
      <span class="out-of-stock">Out of Stock</span>
    {% endif %}
  </div>
{% endfunction %}

{% comment %} Complete product card {% endcomment %}
{% function complete_product_card %}
  <div class="product-card">
    <a href="{{ product.url }}">
      <img src="{{ product.featured_image | img_url: '300x300' }}" alt="{{ product.title }}">
    </a>
    
    <div class="product-info">
      <h3><a href="{{ product.url }}">{{ product.title }}</a></h3>
      <p class="vendor">{{ product.vendor }}</p>
      
      {% call "product_rating" %}
      {% call "product_availability" %}
      {% call "customer_pricing" %}
      
      <div class="product-actions">
        {% if product.available %}
          <button class="btn btn-primary add-to-cart" data-product-id="{{ product.id }}">
            Add to Cart
          </button>
        {% endif %}
      </div>
    </div>
  </div>
{% endfunction %}
```

### Form Components
```liquid
{% comment %} Form field function {% endcomment %}
{% function form_field %}
  <div class="form-field {{ field_class }}">
    {% if field_label %}
      <label for="{{ field_id }}">{{ field_label }}</label>
    {% endif %}
    
    {% case field_type %}
      {% when 'text' %}
        <input type="text" id="{{ field_id }}" name="{{ field_name }}" value="{{ field_value }}" {{ field_attributes }}>
      {% when 'email' %}
        <input type="email" id="{{ field_id }}" name="{{ field_name }}" value="{{ field_value }}" {{ field_attributes }}>
      {% when 'textarea' %}
        <textarea id="{{ field_id }}" name="{{ field_name }}" {{ field_attributes }}>{{ field_value }}</textarea>
      {% when 'select' %}
        <select id="{{ field_id }}" name="{{ field_name }}" {{ field_attributes }}>
          {{ field_options }}
        </select>
    {% endcase %}
    
    {% if field_error %}
      <span class="field-error">{{ field_error }}</span>
    {% endif %}
  </div>
{% endfunction %}

{% comment %} Contact form using form fields {% endcomment %}
{% function contact_form %}
  <form action="/contact" method="post" class="contact-form">
    {% assign field_type = 'text' %}
    {% assign field_id = 'contact_name' %}
    {% assign field_name = 'contact[name]' %}
    {% assign field_label = 'Your Name' %}
    {% assign field_attributes = 'required' %}
    {% call "form_field" %}
    
    {% assign field_type = 'email' %}
    {% assign field_id = 'contact_email' %}
    {% assign field_name = 'contact[email]' %}
    {% assign field_label = 'Your Email' %}
    {% call "form_field" %}
    
    {% assign field_type = 'textarea' %}
    {% assign field_id = 'contact_message' %}
    {% assign field_name = 'contact[message]' %}
    {% assign field_label = 'Your Message' %}
    {% assign field_attributes = 'rows="5" required' %}
    {% call "form_field" %}
    
    <button type="submit" class="btn btn-primary">Send Message</button>
  </form>
{% endfunction %}
```

## Best Practices

### Function Organization
1. **Keep functions focused**: Each function should have a single responsibility
2. **Use descriptive names**: Function names should clearly indicate their purpose
3. **Document complex functions**: Add comments explaining the function's purpose and usage

### Performance Considerations
1. **Avoid deep nesting**: Limit function call depth to prevent performance issues
2. **Cache function results**: Store function output in variables when called multiple times
3. **Use functions for repeated code**: Functions reduce template size and improve maintainability

### Error Handling
```liquid
{% function safe_product_display %}
  {% if product %}
    {% call "complete_product_card" %}
  {% else %}
    <div class="product-error">
      <p>Product information unavailable</p>
    </div>
  {% endif %}
{% endfunction %}
```

### Function Libraries
```liquid
{% comment %} Create a library of utility functions {% endcomment %}
{% function format_currency %}
  {{ amount | money }}
{% endfunction %}

{% function format_date %}
  {{ date | date: '%B %d, %Y' }}
{% endfunction %}

{% function truncate_text %}
  {{ text | truncate: length | default: 100 }}
{% endfunction %}

{% function generate_id %}
  {{ prefix | default: 'item' }}-{{ suffix | default: forloop.index }}
{% endfunction %}
```

Functions provide a powerful way to create modular, reusable template code in CloudCart Liquid, improving maintainability and reducing code duplication across your theme.
