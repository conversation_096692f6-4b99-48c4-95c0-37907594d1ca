<?php

declare(strict_types=1);

namespace Api\Http\Resources;

class JsonResource extends \Illuminate\Http\Resources\Json\JsonResource
{
    public static $allowedIncludes = [];

    protected static $includesResources = [];

    /**
     * @return object[]
     */
    public function included(): array
    {
        $included = [];

        foreach (static::$allowedIncludes as $allowedInclude) {
            //            if ($tmp = $this->whenLoaded($allowedInclude)) {
            if ($this->resource->relationLoaded($allowedInclude)) {
                $included[] = new static::$includesResources[$allowedInclude]($this->{$allowedInclude});
            }
        }

        return $included;
    }

}
