<?php

declare(strict_types=1);

namespace Api\Http\Controllers;

use Api\Http\Resources\Product\ProductCollection;
use Api\Http\Resources\Product\ProductResource;
use Api\Models\Product;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

class ProductController extends Controller
{
    /**
     * List products
     * [Description]
     *
     * @queryParam include What to be included in response. Example: vendor
     * @queryParam filter Filter products by name. Example: {"name": "new product"}
     *
     * @param Request $request
     * @return
     * @example {"name": "new product"}
     *
     * @responseFile /../api2/resources/apidoc/products/index.json
     * @responseFile 404 /../api2/resources/apidoc/model.not.found.json
     */
    public function index(Request $request): \Api\Http\Resources\Product\ProductCollection
    {
        $query = Product::query();

        $products = QueryBuilder::for($query)
            ->allowedIncludes(ProductResource::$allowedIncludes)
            ->allowedFilters('name')
            ->allowedAppends([])
            ->jsonPaginate(1)
            ->appends($request->query());

        return new ProductCollection($products);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request): void
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param Product $product
     * @return ProductResource
     */
    public function show(Product $product): \Api\Http\Resources\Product\ProductResource
    {
        return new ProductResource($product);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id): void
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id): void
    {
        //
    }
}
