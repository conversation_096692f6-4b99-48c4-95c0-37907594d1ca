<?php

declare(strict_types=1);

namespace Api\Modules\Blog\Blogs;

use Api\Modules\Api\BaseValidators;
use App\Models\Blog\Blog as Category;
use CloudCreativity\LaravelJsonApi\Contracts\Validation\ValidatorInterface;
use ErrorException;
use Illuminate\Support\Arr;

class Validators extends BaseValidators
{
    /**
     * @inheritDoc
     */
    protected $readOnlyAttributes = [
        'created_at',
        'updated_at',
        'image',
        'max_thumb_size',
    ];

    /**
     * @inheritDoc
     */
    protected $allowedSortParameters = [
        'id',
        'name',
        'created_at',
        'updated_at',
    ];

    /**
     * @inheritDoc
     */
    #[\Override]
    protected function rules($record = null): array
    {
        /** @var Category $record */
        $required = $record ? 'sometimes' : 'required';
        $urlUnique = $record ? 'unique:blogs,url_handle,' . $record->getKey() : 'unique:blogs';

        return [
            'name' => $required . '|string|min:3',
            'url_handle' => sprintf('alpha_dash|%s|max:191', $urlUnique),
            'image_url' => 'url',
            'comments' => 'in:no,moderator,automatic',
        ];
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    protected function queryRules(): array
    {
        return array_merge(parent::queryRules(), [
            'filter.url_handle' => 'filled|alpha_dash',
        ]);
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public function create(array $document): ValidatorInterface
    {
        $validator = parent::create($document);

        $this->validateImageType($validator, $document);

        return $validator;
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public function update($record, array $document): ValidatorInterface
    {
        $validator = parent::update($record, $document);

        $this->validateImageType($validator, $document);

        return $validator;
    }

    /**
     * @param ValidatorInterface $validator
     * @param array $document
     */
    protected function validateImageType(ValidatorInterface $validator, array $document)
    {
        if ($imageSrc = Arr::get($document, 'data.attributes.image_url')) {
            $validator->after(function (\Illuminate\Contracts\Validation\Validator $validator) use ($imageSrc): void {
                if ($validator->errors()->isEmpty()) {
                    try {
                        $type = exif_imagetype($imageSrc);
                    } catch (ErrorException) {
                        $type = false;
                    }

                    if ($type === false) {
                        $validator->errors()->add(
                            'image_url',
                            sprintf('The content at %s is not a valid image file', $imageSrc)
                        );
                    }
                }
            });
        }
    }
}
