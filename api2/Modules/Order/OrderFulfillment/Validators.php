<?php

declare(strict_types=1);

namespace Api\Modules\Order\OrderFulfillment;

use Api\Modules\Api\BaseValidators;
use App\Models\Order\OrderFulfillment;
use App\Models\Product\Variant;
use CloudCreativity\LaravelJsonApi\Contracts\Validation\ValidatorInterface;
use CloudCreativity\LaravelJsonApi\Rules\HasOne;

class Validators extends BaseValidators
{
    /**
     * @inheritDoc
     */
    protected $allowedIncludePaths = [
        'order',
    ];

    /**
     * @inheritDoc
     */
    protected $readOnlyAttributes = [
        'order_id',
        'shipping_provider',
        'date_fulfilled',
    ];

    /**
     * @inheritDoc
     */
    protected $allowedSortParameters = [
        'id',
        'order_id',
        'date_fulfilled',
        'shipping_date_delivery',
        'shipping_date_expedition',
    ];

    /**
     * @inheritDoc
     */
    #[\Override]
    protected function rules($record = null): array
    {
        /** @var OrderFulfillment $record */
        $required = $record ? 'sometimes' : 'required';

        return [
            'shipping_tracking_url' => 'url',
            'shipping_date_delivery' => 'date_format:Y-m-d',
            'shipping_date_expedition' => 'date_format:Y-m-d',
            'order' => [
                $required,
                new HasOne(),
            ],
        ];
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public function create(array $document): ValidatorInterface
    {
        $validator = parent::create($document);

        return $this->validateCreateUpdate($validator, $document);
    }

    /**
     * @param ValidatorInterface $validator
     * @param array $document
     * @param null|Variant $record
     * @return ValidatorInterface
     */
    protected function validateCreateUpdate(ValidatorInterface $validator, array $document, $record = null): ValidatorInterface
    {
        $parameters = json_api_request()->getParameters();
        $newInstance = $this->adapter->newRecord($document, $parameters);

        if ($record) {
            $record->fill($newInstance->attributesToArray());
        } else {
            $record = $newInstance;
        }

        $this->validateOrderStatus($validator, $record);

        return $validator;
    }

    /**
     * @param ValidatorInterface $validator
     * @param OrderFulfillment $record
     */
    protected function validateOrderStatus(ValidatorInterface $validator, OrderFulfillment $record)
    {
        $validator->after(function (\Illuminate\Contracts\Validation\Validator $validator) use ($record): void {
            if ($record->order->status_fulfillment == 'fulfilled') {
                $validator->errors()->add(
                    'order',
                    "This order is already fulfilled."
                );
            }
        });
    }
}
