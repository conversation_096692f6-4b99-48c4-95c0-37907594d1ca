<?php

declare(strict_types=1);

namespace Api\Modules\Order\OrderShipping;

use Api\Modules\Api\BaseSchema;

class Schema extends BaseSchema
{
    /**
     * @var string
     */
    protected $resourceType = 'order-shipping';

    /**
     * The model relationships to serialize.
     *
     * @var array
     */
    public static $relationships = [
        'order',
        'provider',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'geo_zone_id',
        'geo_zone_name',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [];

}
