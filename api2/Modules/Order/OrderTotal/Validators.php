<?php

declare(strict_types=1);

namespace Api\Modules\Order\OrderTotal;

use Api\Modules\Api\BaseValidators;

class Validators extends BaseValidators
{
    /**
     * @inheritDoc
     */
    protected $allowedIncludePaths = [
        'order',
    ];

    /**
     * @inheritDoc
     */
    protected $readOnlyAttributes = [];

    /**
     * @inheritDoc
     */
    protected $allowedSortParameters = [];

    /**
     * @inheritDoc
     */
    #[\Override]
    protected function rules($record = null): array
    {
        return [];
    }
}
