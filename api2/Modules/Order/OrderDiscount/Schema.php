<?php

declare(strict_types=1);

namespace Api\Modules\Order\OrderDiscount;

use Api\Modules\Api\BaseSchema;

class Schema extends BaseSchema
{
    /**
     * @var string
     */
    protected $resourceType = 'order-discount';

    /**
     * The model relationships to serialize.
     *
     * @var array
     */
    public static $relationships = [
        'order',
        'discount',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'discount_type',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'type',
    ];

}
