<?php

declare(strict_types=1);

namespace Api\Modules\Order\OrderShippingAddress;

use Api\Modules\Api\BaseSchema;

class Schema extends BaseSchema
{
    /**
     * @var string
     */
    protected $resourceType = 'order-shipping-address';

    /**
     * @inheritDoc
     */
    public static $relationships = [
        'order',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'formatted'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'address',
        'country',
        'city',
        'state',
        'street',
    ];

}
