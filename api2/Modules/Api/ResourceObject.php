<?php

declare(strict_types=1);

namespace Api\Modules\Api;

use Neomerx\JsonApi\Contracts\Schema\SchemaProviderInterface;

class ResourceObject extends \Neomerx\JsonApi\Schema\ResourceObject
{
    /**
     * @var BaseSchema
     */
    protected $schema;

    /**
     * @param SchemaProviderInterface $schema
     * @param object                  $resource
     * @param bool                    $isInArray
     * @param array<string,int>|null  $attributeKeysFilter
     */
    public function __construct(
        SchemaProviderInterface $schema,
        $resource,
        $isInArray,
        ?array $attributeKeysFilter = null
    ) {
        parent::__construct($schema, $resource, $isInArray, $attributeKeysFilter);
    }

    /**
     * @inheritdoc
     */
    public function getAttributes()
    {
        $attributes = parent::getAttributes();

        $appends = $this->schema->getAppends($this->resource);
        unset($appends['meta']);

        return array_merge($attributes, $appends);
    }

}
