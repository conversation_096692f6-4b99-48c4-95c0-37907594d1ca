<?php

declare(strict_types=1);

namespace Api\Modules\Api;

use App\Helper\YesNo;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Neomerx\JsonApi\Contracts\Document\LinkInterface;
use Neomerx\JsonApi\Schema\SchemaProvider;

/**
 * Class DefaultSchema
 * @package Api\JsonApi
 */
class BaseSchema extends SchemaProvider
{
    public static $showLinks = false;

    /**
     * Whether Eloquent relations are camel cased.
     *
     * @var bool
     */
    protected $camelCaseRelations = true;

    /**
     * The model relationships to serialize.
     *
     * @var array
     */
    public static $relationships = [];

    /**
     * Foreign keys to relationships mapping.
     *
     * @var array
     */
    protected $foreignKeys = [];

    /**
     * Relationships class mapping (Only needed for relationships by $foreignKeys).
     *
     * @var array
     */
    protected $relationshipsClasses = [];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [];

    /**
     * @var array
     */
    protected $casts = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [];

    /**
     * Get resource identity.
     *
     * @param object $resource
     *
     * @return string
     */
    public function getId($resource)
    {
        return (string)$resource->getRouteKey();
    }

    /**
     * Get resource attributes.
     *
     * @param object|Model $resource
     *
     * @return array
     */
    public function getAttributes($resource)
    {
        if ($resource->wasRecentlyCreated) {
            $resource = $resource->fresh() ?? $resource;
        }

        $resource->setHidden(array_merge($this->hidden, [$resource->getKeyName()]));
        $resource->setAppends($this->appends);

        $attributes = $resource->attributesToArray();
        //        $this->yesNoToInt($attributes);

        $dates = $resource->getDates();
        $atomDates = [];
        $casts = [];

        foreach ($dates as $date) {
            if (array_key_exists($date, $attributes)) {
                $atomDates[$date] = $resource->$date ? $resource->$date->toAtomString() : null;
            }
        }

        if ($this->casts) {
            foreach ($this->casts as $attribute => $cast) {
                if (array_key_exists($attribute, $attributes) && method_exists($this, $method = sprintf('cast%s', Str::studly($cast)))) {
                    $casts[$attribute] = $this->$method($resource->$attribute);
                }
            }
        }

        return array_merge($attributes, $atomDates, $casts);
    }

    /**
     * @param object|Model $resource
     * @return array
     */
    public function getAppends($resource)
    {
        $attributes = [];

        /** @var EncodingParameters $request */
        $request = json_api_request()->getParameters();
        $appends = $request->getAppendsForResourceType($this->resourceType);

        foreach ($appends as $append) {
            $attributes[$append] = $resource->$append;
        }

        return $attributes;
    }

    /**
     * Get resource links.
     *
     * @param Model $resource
     * @param bool $isPrimary
     * @param array $includeRelationships A list of relationships that will be included as full resources.
     *
     * @return array
     */
    public function getRelationships($resource, $isPrimary, array $includeRelationships)
    {
        //        return parent::getRelationships($resource, $isPrimary, $includeRelationships);
        $relationships = [];

        foreach (static::$relationships as $key => $relationship) {
            $relationshipName = is_numeric($key) ? $relationship : $key;
            $data = null;

            if (isset($includeRelationships[$relationshipName])) {
                $data = function () use ($resource, $relationship) {
                    $relationship = $this->camelCaseRelations ? Str::camel($relationship) : $relationship;
                    return $resource->$relationship;
                };
            }

            if (empty($data) && in_array($relationshipName, $this->foreignKeys)) {
                if (!empty($fkField = array_flip($this->foreignKeys)[$relationshipName]) && $resource->$fkField) {
                    $class = $this->relationshipsClasses[$relationshipName];
                    /** @var Model $data */
                    $data = new $class();
                    $data->{$data->getKeyName()} = $resource->$fkField;
                }
            }

            if (!empty($data)) {
                $relationships[$relationshipName] = [
                    self::SHOW_SELF => true,
                    self::SHOW_RELATED => true,
                    self::SHOW_DATA => true,
                    self::DATA => $data,
                ];
            }
        }

        return $relationships;
    }

    /**
     * @inheritdoc
     */
    public function getResourceLinks($resource)
    {
        if (static::$showLinks) {
            $links = [
                LinkInterface::SELF => $this->getSelfSubLink($resource),
            ];
        }

        return $links ?? [];
    }

    /**
     * @inheritdoc
     */
    public function getIncludedResourceLinks($resource)
    {
        if (static::$showLinks) {
            $links = [
                LinkInterface::SELF => $this->getSelfSubLink($resource),
            ];
        }

        return $links ?? [];
    }

    /**
     * @inheritdoc
     */
    protected function readLinks($resource, $relationshipName, array $description, $isShowSelf, $isShowRelated)
    {
        if (!static::$showLinks) {
            $isShowSelf = false;
            $isShowRelated = false;
        }

        return parent::readLinks($resource, $relationshipName, $description, $isShowSelf, $isShowRelated);
    }

    /**
     * @param array $attributes
     */
    protected function yesNoToInt(array &$attributes)
    {
        foreach ($attributes as $attribute => $value) {
            if (in_array($value, [YesNo::True, YesNo::False])) {
                $attributes[$attribute] = $value == YesNo::True ? 1 : 0;
            }
        }
    }

}
