<?php

declare(strict_types=1);

namespace Api\Modules\Customer\CustomerTags;

use Api\Modules\Api\BaseValidators;

class Validators extends BaseValidators
{
    /**
     * @inheritDoc
     */
    protected $readOnlyAttributes = [
    ];

    /**
     * @inheritDoc
     */
    protected $allowedSortParameters = [
        'tag',
        'url_handle',
    ];

    /**
     * @inheritDoc
     */
    #[\Override]
    protected function rules($record = null): array
    {
        return [

        ];
    }
}
