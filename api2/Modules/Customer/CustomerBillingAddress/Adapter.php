<?php

declare(strict_types=1);

namespace Api\Modules\Customer\CustomerBillingAddress;

use Api\Modules\Api\AbstractAdapter;
use App\Models\Customer\CustomerBillingAddress;
use CloudCreativity\LaravelJsonApi\Document\ResourceObject;
use CloudCreativity\LaravelJsonApi\Pagination\StandardStrategy;
use League\ISO3166\ISO3166;
use stdClass;

class Adapter extends AbstractAdapter
{
    /**
     * Mapping of JSON API attribute field names to model keys.
     *
     * @var array
     */
    protected $attributes = [];

    /**
     * @inheritDoc
     */
    protected $includePaths = [
//        'customer',
    ];

    /**
     * The resource attributes that are dates.
     *
     * A list of JSON API attribute fields that should be cast to dates. If this is
     * empty, then `Model::getDates()` will be used.
     *
     * @var string[]
     */
    protected $dates = [];

    /**
     * Mapping of JSON API filter names to model scopes.
     *
     * @var array
     */
    protected $filterScopes = [];

    /**
     * Adapter constructor.
     *
     * @param StandardStrategy $paging
     */
    public function __construct(StandardStrategy $paging)
    {
        parent::__construct(new CustomerBillingAddress(), $paging);
    }

    /**
     * @param CustomerBillingAddress $record
     * @param stdClass|ResourceObject $resource
     */
    protected function created(CustomerBillingAddress $record, ResourceObject $resource): void
    {
        if ($record->customer->billing_addresses()->count('id') == 1) {
            $record->customer->update(['default_billing_address_id' => $record->getKey()]);
        }
    }

    /**
     * @param CustomerBillingAddress $record
     */
    protected function saving(CustomerBillingAddress $record): void
    {
        if ($record->isDirty('country_iso2')) {
            $record->country_iso2 = strtoupper($record->country_iso2);
            $record->country_name = locale_get_display_region('-' . $record->country_iso2, site('language'));
            $iso = new ISO3166();
            $record->country_iso3 = $iso->alpha2($record->country_iso2)['alpha3'];
        }

        $record->text = $record->format(false, false);
    }

    protected function customer()
    {
        return $this->belongsTo();
    }
}
