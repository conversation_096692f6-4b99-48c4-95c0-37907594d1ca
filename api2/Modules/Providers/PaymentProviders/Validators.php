<?php

declare(strict_types=1);

namespace Api\Modules\Providers\PaymentProviders;

use Api\Modules\Api\BaseValidators;

class Validators extends BaseValidators
{
    /**
     * @inheritDoc
     */
    protected $allowedIncludePaths = [];

    /**
     * @inheritDoc
     */
    protected $readOnlyAttributes = [];

    /**
     * @inheritDoc
     */
    protected $allowedSortParameters = [];

    /**
     * @inheritDoc
     */
    protected $allowedFilteringParameters = [
        'active',
    ];

    /**
     * @inheritDoc
     */
    #[\Override]
    protected function rules($record = null): array
    {
        return [];
    }
}
