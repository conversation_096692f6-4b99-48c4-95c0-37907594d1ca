<?php

declare(strict_types=1);

namespace Api\Modules\Campaigns\SubscribersTags;

use Api\Modules\Api\BaseSchema;

class Schema extends BaseSchema
{
    /**
     * @var string
     */
    protected $resourceType = 'subscribers-tags';

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [

    ];

    /**
     * The model relationships to serialize.
     *
     * @var array
     */
    public static $relationships = [
        'subscriber', 'tag'
    ];
}
