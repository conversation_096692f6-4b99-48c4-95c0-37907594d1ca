<?php

declare(strict_types=1);

namespace Api\Modules\Product\Categories;

use CloudCreativity\LaravelJsonApi\Eloquent\HasMany;
use Neomerx\JsonApi\Contracts\Encoder\Parameters\EncodingParametersInterface;

/**
 * Class PropertiesRelation
 * @package Api\Modules\Product\Categories
 */
class PropertiesRelation extends HasMany
{
    /**
     * @inheritDoc
     */
    #[\Override]
    public function add($record, array $relationship, EncodingParametersInterface $parameters)
    {
        $related = $this->findRelated($record, $relationship);
        $relation = $this->getRelation($record, $this->key);

        $existing = $relation
            ->getQuery()
            ->whereKey($related->modelKeys())
            ->get();

        $relatedForSave = $related->filter(fn ($item): bool => is_null($existing->where('property_id', $item->id)->first()));

        $relation->saveMany($relatedForSave);
        $record->refresh(); // in case the relationship has been cached.

        return $record;
    }

}
