<?php

declare(strict_types=1);

namespace Api\Modules\Product\Products;

use App\Models\Product\Product;
use CloudCreativity\LaravelJsonApi\Eloquent\HasMany;
use Illuminate\Support\Arr;
use Neomerx\JsonApi\Contracts\Encoder\Parameters\EncodingParametersInterface;

/**
 * Class LinkedProductsRelation
 * @package Api\Modules\Product\Products
 */
class LinkedProductsRelation extends HasMany
{
    /**
     * @inheritDoc
     */
    #[\Override]
    public function add($record, array $relationship, EncodingParametersInterface $parameters)
    {
        return $this->update($record, $relationship, $parameters);
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public function remove($record, array $relationship, EncodingParametersInterface $parameters)
    {
        if (data_get($relationship, 'data.type')) {
            $relationship = ['data' => [$relationship['data']]];
        }

        /** @var Product $record */
        $related = collect($relationship['data'] ?? [])->pluck('two_way', 'id')->map(fn ($two_way): array => ['two_way' => intval(is_null($two_way) ? 1 : $two_way)])->forget($record->id)->all();

        $record->linked_products()->detach(array_keys($related));

        $relatedDiff = ['detached' => array_keys($related)];

        $relatedDiff = array_filter(array_map(fn ($ids) => Product::withoutGlobalScopes()->whereIn('id', $ids)->get()->map(fn (Product $product): array => [
            'id' => $product->id,
            'name' => $product->name,
            'two_way' => $related[$product->id]['two_way'],
            'model' => $product,
        ])->all(), $relatedDiff));

        if (!$relatedDiff) {
            return $record;
        }

        $record->changeLogRegisterExtend([
            'relations' => [
                'linked_products' => array_map(fn ($relatedDiff): array => array_map(fn ($relatedDiff) => Arr::only($relatedDiff, ['id', 'name']), $relatedDiff), $relatedDiff)
            ]
        ]);

        foreach ($relatedDiff as $relatedDiffType => $relatedDiffValue) {
            foreach ($relatedDiffValue as $value) {
                $log = null;
                if ($value['model']->linked_products()->detach($record->id)) {
                    $log = [$relatedDiffType => [['id' => $record->id, 'name' => $record->name]]];
                }

                if ($log) {
                    $value['model']->changeLogRegisterExtend([
                        'relations' => [
                            'linked_products' => $log
                        ]
                    ]);
                }
            }
        }

        collect($relatedDiff)->collapse()->where('two_way', 0)->map(function (array $value) use ($record): void {
            if ($value['model']->linked_products()->detach($record->id)) {
                $value['model']->changeLogRegisterExtend([
                    'relations' => [
                        'linked_products' => ['detached' => [['id' => $record->id, 'name' => $record->name]]]
                    ]
                ]);
            }
        });

        return $record;
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public function update($record, array $relationship, EncodingParametersInterface $parameters)
    {
        if (data_get($relationship, 'data.type')) {
            $relationship = ['data' => [$relationship['data']]];
        }

        /** @var Product $record */
        $sorting = -1;
        $related = collect($relationship['data'] ?? [])->pluck('two_way', 'id')->map(function ($two_way) use (&$sorting): array {
            $sorting++;
            return ['two_way' => intval($two_way), 'sorting' => $sorting];
        })->forget($record->id)->all();

        $relatedDiff = array_filter($record->linked_products()->syncWithoutDetaching($related));

        if (!$relatedDiff) {
            return collect(['data' => $record->linked_products->map->pivot->map(fn ($a): array => [
                "type" => "products",
                "id" => $a->linked_id,
                "two_way" => $a->two_way
            ])]);
        }

        $relatedDiff = array_filter(array_map(fn ($ids) => Product::withoutGlobalScopes()->whereIn('id', $ids)->get()->map(fn (Product $product): array => [
            'id' => $product->id,
            'name' => $product->name,
            'two_way' => $related[$product->id]['two_way'],
            'model' => $product,
        ])->all(), $relatedDiff));

        if (!$relatedDiff) {
            return collect(['data' => $record->linked_products->map->pivot->map(fn ($a): array => [
                "type" => "products",
                "id" => $a->linked_id,
                "two_way" => $a->two_way
            ])]);
        }

        $record->changeLogRegisterExtend([
            'relations' => [
                'linked_products' => array_map(fn ($relatedDiff): array => array_map(fn ($relatedDiff) => Arr::only($relatedDiff, ['id', 'name']), $relatedDiff), $relatedDiff)
            ]
        ]);

        foreach ($relatedDiff as $relatedDiffType => $relatedDiffValue) {
            foreach ($relatedDiffValue as $value) {
                $log = null;
                if ($relatedDiffType == 'attached' && !empty($value['two_way'])) {
                    if (!empty($value['model']->linked_products()->syncWithoutDetaching($record->id)['attached'])) {
                        $log = [$relatedDiffType => [['id' => $record->id, 'name' => $record->name]]];
                    }
                } elseif ($relatedDiffType == 'detached') {
                    if ($value['model']->linked_products()->detach($record->id)) {
                        $log = [$relatedDiffType => [['id' => $record->id, 'name' => $record->name]]];
                    }
                }

                if ($log) {
                    $value['model']->changeLogRegisterExtend([
                        'relations' => [
                            'linked_products' => $log
                        ]
                    ]);
                }
            }
        }

        collect($relatedDiff)->collapse()->where('two_way', 0)->map(function (array $value) use ($record): void {
            if ($value['model']->linked_products()->detach($record->id)) {
                $value['model']->changeLogRegisterExtend([
                    'relations' => [
                        'linked_products' => ['detached' => [['id' => $record->id, 'name' => $record->name]]]
                    ]
                ]);
            }
        });

        collect($relatedDiff)->collapse()->where('two_way', 1)->map(function (array $value) use ($record): void {
            if (!empty($value['model']->linked_products()->syncWithoutDetaching($record->id)['attached'])) {
                $value['model']->changeLogRegisterExtend([
                    'relations' => [
                        'linked_products' => ['attached' => [['id' => $record->id, 'name' => $record->name]]]
                    ]
                ]);
            }
        });

        return collect(['data' => $record->linked_products->map->pivot->map(fn ($a): array => [
            "type" => "products",
            "id" => $a->linked_id,
            "two_way" => $a->two_way
        ])]);
    }
}
