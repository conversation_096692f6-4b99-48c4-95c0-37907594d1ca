<?php

declare(strict_types=1);

namespace Api\Modules\Product\Images;

use Api\Modules\Api\BaseValidators;
use CloudCreativity\LaravelJsonApi\Contracts\Validation\ValidatorInterface;
use CloudCreativity\LaravelJsonApi\Rules\HasOne;
use ErrorException;
use Illuminate\Support\Arr;

/**
 * Class Validators
 * @package Api\Modules\Product\Images
 */
class Validators extends BaseValidators
{
    /**
     * @inheritDoc
     */
    protected $readOnlyAttributes = [
        'image_id',
        'parent_id',
        'last_edited',
        'date_added',
        'max_thumb_size',
        'width',
        'height',
        'thumbs',
    ];

    /**
     * @inheritDoc
     */
    protected $allowedSortParameters = [
        'id',
        'name',
        'date_added',
        'last_edited',
        'sort_order',
        'parent_id',
    ];

    /**
     * @inheritDoc
     */
    #[\Override]
    protected function rules($record = null): array
    {
        $required = $record ? 'sometimes' : 'required';

        return [
            'src' => $required . '|url',
            'product' => [
                $required,
                new HasOne(),
            ],
        ];
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public function create(array $document): ValidatorInterface
    {
        $validator = parent::create($document);

        $this->validateImageType($validator, $document);

        return $validator;
    }

    /**
     * @param ValidatorInterface $validator
     * @param array $document
     */
    protected function validateImageType(ValidatorInterface $validator, array $document)
    {
        if ($imageSrc = Arr::get($document, 'data.attributes.src')) {
            $validator->after(function (\Illuminate\Contracts\Validation\Validator $validator) use ($imageSrc): void {
                if ($validator->errors()->isEmpty()) {
                    try {
                        $type = exif_imagetype($imageSrc);
                    } catch (ErrorException) {
                        $type = false;
                    }

                    if ($type === false) {
                        $validator->errors()->add(
                            'src',
                            sprintf('The content at %s is not a valid image file', $imageSrc)
                        );
                    }
                }
            });
        }
    }
}
