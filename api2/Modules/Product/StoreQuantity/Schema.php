<?php

declare(strict_types=1);

namespace Api\Modules\Product\StoreQuantity;

use Api\Modules\Api\BaseSchema;

class Schema extends BaseSchema
{
    /**
     * @var string
     */
    protected $resourceType = 'store-quantity';

    /**
     * The model relationships to serialize.
     *
     * @var array
     */
    public static $relationships = [
        'store' => 'shop',
        'product',
        'variant',
    ];

    /**
     * Foreign keys to relationships mapping.
     *
     * @var array
     */
    //    protected $foreignKeys = [
    //        'shop_id' => 'store',
    //        'product_id' => 'product',
    //        'variant_id' => 'variant',
    //    ];

    /**
     * Relationships class mapping (Only needed for relationships by $foreignKeys).
     *
     * @var array
     */
    //    protected $relationshipsClasses = [
    //        'store' => Shop::class,
    //        'product' => Product::class,
    //        'variant' => Variant::class,
    //    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'id',
    ];

}
