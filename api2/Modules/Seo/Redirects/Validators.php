<?php

declare(strict_types=1);

namespace Api\Modules\Seo\Redirects;

use Api\Modules\Api\BaseValidators;
use App\Models\Redirect\Redirect;
use CloudCreativity\LaravelJsonApi\Contracts\Validation\ValidatorInterface;
use CloudCreativity\LaravelJsonApi\Rules\HasOne;

class Validators extends BaseValidators
{
    /**
     * @inheritDoc
     */
    protected $readOnlyAttributes = [
        'item_type',
        'item_id',
        'full_new_url',
    ];

    /**
     * @inheritDoc
     */
    protected $allowedSortParameters = [
        'item_type',
        'item_id',
        'redirect_type',
    ];

    /**
     * @inheritDoc
     */
    #[\Override]
    protected function rules($record = null): array
    {
        /** @var Redirect $record */
        $urlUnique = $record ? 'unique:redirects,old_url,' . $record->getKey() : 'unique:redirects,old_url';

        return [
            'redirect_type' => 'required|in:product,category,vendor,blog,article,page,section,manual,external',
            'old_url' => 'required|' . $urlUnique,
            'new_url' => 'required_if:redirect_type,manual,external',
            'item' => [
                'required_unless:redirect_type,manual,external',
                new HasOne('products', 'categories', 'vendors', 'blogs', 'posts'),
            ],
        ];
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    protected function validatorForResource(
        array $data,
        array $rules,
        array $messages = [],
        array $customAttributes = []
    ): ValidatorInterface {
        if (!empty($data['attributes']['old_url'])) {
            $data['attributes']['old_url'] = (new Redirect())->parseOldUrl($data['attributes']['old_url']);
        }

        return parent::validatorForResource($data, $rules, $messages, $customAttributes);
    }
}
