<?php

declare(strict_types=1);

namespace Resellers\Http\Controllers\Api;

use App\Jobs\InstallSite;
use App\Models\Gate\UsersSites;
use App\Models\Router\Site;
use App\Models\Setting\Admin;
use App\Models\Setting\AdminsLoginCode;
use App\Setup\Install;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Validation\ValidationException;
use Resellers\Http\Controllers\Controller;

/**
 * Class SitesController
 * @package Resellers\Http\Controllers\Api
 */
class SitesController extends Controller
{
    /**
     * @var \App\Models\Gate\KeyAccount
     */
    protected $keyAccount;

    /**
     * Instantiate a new controller instance.
     *
     */
    public function __construct()
    {
        /** @var \App\Models\Oauth\User $user */
        if ($user = auth()->user()) {
            $this->keyAccount = $user->keyAccount;
        }

        //        $this->middleware('permission:create sites')->only('store');
        $this->middleware('permission:update sites')->only('update');
        $this->middleware('permission:delete sites')->only('destroy');
    }

    /**
     * @return AnonymousResourceCollection
     */
    public function index()
    {
        $sites = UsersSites::console()->get();

        return JsonResource::collection($sites);
    }

    /**
     * @param $field
     * @param $value
     * @return AnonymousResourceCollection
     * @throws ValidationException
     */
    public function search($field, $value)
    {
        try {
            $items = UsersSites::console()
                ->where($field, 'like', sprintf('%%%s%%', $value))
                ->get();
        } catch (QueryException $queryException) {
            if (strpos($queryException->getMessage(), 'Unknown column')) {
                throw ValidationException::withMessages([$field => 'Invalid search field.']);
            }

            throw $queryException;
        }

        return JsonResource::collection($items);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'host' => 'required|min:' . config('setup.host.min')
                . '|max:' . config('setup.host.max')
                . '|not_in:' . implode(',', config('setup.host.reserved'))
                . '|regex:' . config('setup.host.regex')
                . '|unique:gate.cc_gate.users_sites,host',
            'user_id' => 'reseller_user',
        ]);

        $request->offsetSet('reseller_id', $this->keyAccount->reseller_id);
        $request->offsetSet('key_account_id', $this->keyAccount->getKey());

        $user_site = Install::createSite($request);
        $site_data = Install::getSiteData($user_site);

        dispatch(new InstallSite($site_data, 'Resellers API'));

        return response()->json([
            'message' => 'New site created. Check the status_url for installation status.',
            'status_url' => route('reseller.site.install-status', $user_site->getKey()),
            'site_id' => $user_site->getKey(),
        ], 202);
    }

    /**
     * @param $id
     * @return JsonResponse
     */
    public function installStatus($id)
    {
        /** @var UsersSites $site */
        $site = UsersSites::console()
            ->where('id', $id)
            ->firstOrFail();

        return response()->json([
            'site_id' => $site->getKey(),
            'progress' => $site->routerSite->progress,
            'created_at' => $site->routerSite->created_at,
            'updated_at' => $site->routerSite->updated_at,
        ]);
    }

    /**
     * @param $id
     * @return JsonResource
     */
    public function show($id): \Illuminate\Http\Resources\Json\JsonResource
    {
        $site = UsersSites::console()
            ->where('id', $id)
            ->firstOrFail();

        return new JsonResource($site);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id): void
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id): void
    {
        //
        //        return response('', 204);
    }

    /**
     * @param $siteId
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\Error
     * @throws \App\Exceptions\Fault
     */
    public function login($siteId)
    {
        $site = Site::findOrFail($siteId);
        auth()->user()->restrictAccessTo($site->gate);

        $site->bootDB();

        $code = AdminsLoginCode::generate(
            Admin::whereType('owner')->value('id')
        );

        return response()->json([
            'login_url' => $site->getSiteUrl('primary') . '/admin/login?code=' . $code,
            'expires_in' => AdminsLoginCode::$login_code_expire,
        ]);
    }

}
