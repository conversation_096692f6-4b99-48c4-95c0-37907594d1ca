{capture append="js"}
    <script type="text/javascript">
        $(document).on("click", "[data-btn='status']", function () {
            var $btn = $(this);
            var $input = $("#status_" + $btn.data("target"));

            $btn.trigger('loading.start');
            $btn.prop("disabled", true);

            $.ajax({
                type: "post",
                url: "{route('admin.statuses.payment.edit')}",
                data: {
                    type: $btn.data("type"),
                    status: $btn.data("status"),
                    name: $input.val(),
                    save: null
                },
                success: function (res) {
                    $btn.trigger('loading.end');
                    $btn.prop("disabled", false);

                    if ("error" === res.status && res.msg) {
                        swal({
                            title: TranslationLabels['error'],
                            text: res.msg,
                            type: "error",
                            confirmButtonText: "OK",
                            confirmButtonClass: "btn-danger"
                        });
                    }
                    if ("success" === res.status && res.msg) {
                        toastr.success(res.msg)
                    }
                },
                error: function (jqXHR, text, error) {
                    $btn.trigger('loading.end');
                    $btn.prop("disabled", false);

                    var data = $.parseJSON(jqXHR.responseText);

                    if (!data || !data.status || !data.msg)
                        return;

                    swal({
                        title: TranslationLabels['error'],
                        text: data.msg,
                        type: "error",
                        confirmButtonText: "OK",
                        confirmButtonClass: "btn-danger"
                    });
                }
            });
        });
    </script>
{/capture}