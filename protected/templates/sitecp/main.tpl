{$dependency = null}
{$js = null}
{capture "content"}
    {if Illuminate\Support\Str::endsWith($content_display,'.blade.php')}
        {blade_include file="$content_display"}
    {else}
        {include file="$content_display"}
    {/if}
{/capture}
{if isset($fetch) && $fetch === true}
    {if !empty($dependency)}
        {foreach $dependency as $item}
            {$item nofilter}
        {/foreach}
    {/if}
    {$smarty.capture.content nofilter}
    {if !empty($js)}
        {foreach $js as $item}
            {$item nofilter}
        {/foreach}
    {/if}
{else}
    <!DOCTYPE html>
    <html lang="{site('language_cp', site('language'))}">
    <head>
        <script type="text/javascript">
            var cc_last_build = '{app('last_build')}';
            window.dataLayer = window.dataLayer || [];
        </script>

        {$admin = \App\Models\Setting\Admin::getOwner()}
{*        GA4 conflict with GTM "Uncaught TypeError: Cannot set properties of undefined (setting 'href')" *}
{*        {include file="includes/main/main-google.tpl"}*}

        {include file="includes/main/main-cc-user.tpl"}

        {if !inDevelopment()}
            {GoogleTagManager::view('head') nofilter}
        {/if}
        <meta charset="utf-8"/>
        <title>CloudCart Builder</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        {if segment(1) == 'terminal'}
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        {else}
        <meta name="viewport" content="width=1024, maximum-scale=1.3, user-scalable=yes"/>
        {/if}
        <meta content="CloudCart Builder Admin Panel" name="description"/>
        <meta name="author" content="CloudCart LLC">
        <link rel="shortcut icon" href="{asset('')}favicon.ico"/>
        <meta name="robots" content="noindex, nofollow">

        {if setting('data.js')}
            <script type="text/javascript" src="{config('url.storage')}{setting('data.js')}"></script>
        {/if}
        {if activeRoute('admin.geo_polygon.*')}
            {script core="googleapis.maps" language=site('language_cp', site('language')) libraries="drawing"}{/script}
        {elseif activeRoute('admin.geo_zone.*')}
            {script core="googleapis.maps" language=site('language') libraries="places"}{/script}
        {else}
            {script core="googleapis.maps" language=site('language_cp', site('language')) libraries="places"}{/script}
        {/if}

        {if segment(1) != 'terminal'}
            <link href="{$img_url}builder/global/fontawesome-pro/css/all.min.css?{app('last_build')}" rel="stylesheet"
            type="text/css"/>
            <link href="{$img_url}builder/global/fontawesome-pro/css/duotone.min.css?{app('last_build')}" rel="stylesheet"
                    type="text/css"/>
            <link rel="stylesheet" type="text/css" href="{assetic bundle="admin_main_bdl" output="css"}"/>
            {if $rtl}
                <link rel="stylesheet" type="text/css" href="{assetic bundle="basic_bdl_rtl" output="css"}"/>
                <link rel="stylesheet" type="text/css" href="{$img_url}sitecp/style-rtl.css?{app('last_build')}"/>
            {else}
                <link rel="stylesheet" type="text/css" href="{assetic bundle="basic_bdl" output="css"}"/>
                <link rel="stylesheet" type="text/css" href="{$img_url}sitecp/style.css?{app('last_build')}"/>
            {/if}
            <link href="{$img_url}sitecp/css/build.min.css?{app('last_build')}" rel="stylesheet" type="text/css"/>
            <!--[if IE]>
        <link href="{$img_url}sitecp/ie.css?{app('last_build')}" media="screen, projection" rel="stylesheet" type="text/css"/>
        <![endif]-->
            <!-- END GLOBAL MANDATORY STYLES INJECT -->
        {/if}
        <!-- BEGIN PAGE SPECIFIC STYLES INJECT -->
        {if !empty($dependency)}
            {foreach $dependency as $item}
                {$item nofilter}
            {/foreach}
        {/if}
        <!-- END PAGE SPECIFIC STYLES INJECT -->

        {if segment(1) != 'terminal'}
            <style>
                /*google places autocomplete*/
                .pac-container {
                    z-index: 999999999999;
                }

                .input-group-addon.clear {
                    padding: 0;
                    border: 0 !important;
                    box-shadow: inset 0px 0px 0px 0px #dedede;
                    background: transparent;
                }

                .input-group-addon.clear.input-group-addon-right,
                .input-group-addon.gray.input-group-addon-right {
                    left: -15px !important;
                }

                .input-group-addon.clear.input-group-addon-right .btn {
                    padding: 5px 12px;
                }

                .nav-tabs .control-group {
                    padding: 0;
                }

                .nav-tabs .control-group .form-control {
                    padding: 0 !important;
                    height: auto;
                    font-size: 14px;
                    background: transparent;
                    border: 0;
                    width: auto !important;
                    display: inline;
                    border-radius: 0 !important;
                    line-height: normal;
                }

                .nav-tabs .control-group span.editable-clear-x {
                    display: none !important;
                }

                .nav-tabs i.editable-clear-x {
                    top: 1px;
                    cursor: pointer;
                    right: -4px;
                    display: none;
                }

                .nav-tabs .active i.editable-clear-x {
                    display: block;
                }

                .nav-tabs .tab-editable {
                    cursor: text;
                }

                .operation-group {
                    padding: 10px;
                    margin-bottom: 10px;
                    position: relative;
                }

                .operation-group .operation-group-operation-first {
                    text-align: center;
                    margin-bottom: 7px;
                    margin-top: -5px;
                }

                .operation-group .operation-group-remove {
                    position: absolute;
                    top: 32px;
                    left: -7px;
                }

                .topbar .user .username-link.username-link-topbar {
                    display: table;
                }

                .username-link-topbar > span {
                    display: table-cell;
                }

                .username-link-topbar > span .img-circle {
                    width: 30px;
                }

                .topbar .user span.img-circle {
                    display: inline-block;
                    overflow: hidden;
                }
                .editableform {
                    margin-top: 0 !important;
                }
            </style>
            <!-- Payment method Modal -->
            <style type="text/css">
                #dropin-container label {
                    display: block;
                }

                #Cardinal-Modal {
                    z-index: 9999999999;
                    height: 90% !important;
                    width: 90% !important;
                }

                #Cardinal-ModalContent {
                    height: 100% !important;
                }
            </style>
            <!-- alerts -->
            <style>
                .body-alert .alert-holder {
                    position: fixed;
                    width: 100%;
                    top: 0;
                    left: 0;
                    z-index: 999;
                }
            </style>
            <style>
                @media (max-height: 800px) {
                    body > .HW_frame_cont {
                        bottom: 0;
                        top: auto !important;
                    }
                }
            </style>
            <style>
                *[data-ajax-panel] {
                    pointer-events: none;
                }
            </style>
        {/if}
    </head>
    {$logs = collect()}
    {if Auth::adminId()}
        {$logs = \App\Models\Log\UpTimeRobotLog::getLog()}
    {/if}

    <body class="sitecp body{$rtl}{if $smarty.capture.body_class} {$smarty.capture.body_class}{/if} content-loading{if $logs->isNotEmpty()} body-alert{/if}">
    {if $logs->isNotEmpty()}
        <div class="alert alert-warning alert-holder">
            {foreach $logs as $log}
                <p>
                    <a href="https://s.status.cloudcart.com/{$log->monitorID}" target="_blank">{$log->message nofilter}
                        <i class="fal fa-external-link"></i></a>
                </p>
            {/foreach}
        </div>
    {/if}

    {GoogleTagManager::view('body') nofilter}
    {*{include file="includes/topbar.tpl"}*}
    {if segment(1) != 'terminal' && !activeRoute('admin.plan.purchase', 'admin.promo.purchase', 'admin.promo.status', 'admin.plan.feature', 'admin.checkout', 'admin.checkout.*', 'admin.services.*')}
        {include file="includes/sidebar.tpl"}
        {include file="includes/user_account.tpl"}
{*        {include file="includes/sidebar-vue-shadow.tpl"}*}
{*        {include file="includes/sidebar-vue.tpl"}*}
    {/if}
    {*<div class="add-quick {if !$rtl}right{/if}" data-cc-toggle="class" data-cc-toggle-class="active"*}
    {*data-cc-toggle-target=".add-quick-box"*}
    {*data-html="true">*}
    {*<i class="all-icons-add-quick tooltips"></i>*}
    {*<ul class="add-quick-box">*}
    {*<li><a href="{route('admin.products.add')}"><i*}
    {*class="sidebar-subicons-products"></i>{t}product.action.add{/t}</a></li>*}
    {*<li><a href="{asset('')}vendors/list?add"><i*}
    {*class="sidebar-subicons-discounts"></i>{t}vendor.action.add{/t}</a></li>*}
    {*<li><a href="{route('admin.categories.list', ['add'])}"><i*}
    {*class="sidebar-subicons-vendors"></i>{t}category.action.add{/t}</a></li>*}
    {*<li><a href="{route('admin.discounts.list', ['add'=>1])}"><i*}
    {*class="sidebar-subicons-categories"></i>{t}discount.action.add{/t}</a></li>*}
    {*</ul>*}
    {*</div>*}

    <div class="page-container{if $smarty.capture.page_container_class} {$smarty.capture.page_container_class}{/if}">
        <div class="page-content{if $smarty.capture.page_content_class} {$smarty.capture.page_content_class}{/if}">
            {$smarty.capture.content nofilter}
        </div>
    </div>

    {* uncoment this to enable login-promo *}
    {*{if site('user.country') == 'BG' && site('plan') == 'startup'}*}
    {if segment(1) != 'terminal'}
        {if !activeRoute('admin.plan.purchase', 'admin.promo.purchase', 'admin.promo.status', 'admin.plan.feature', 'admin.checkout', 'admin.checkout.*', 'admin.services.*')}
            {include file="includes/main/main-promo-panel.tpl"}
        {/if}
    {/if}
    {*{/if}*}
    {* {if !activeRoute('admin.plan.purchase', 'admin.promo.purchase', 'admin.promo.status', 'admin.plan.feature', 'admin.checkout', 'admin.checkout.*', 'admin.services.*')}
        {include file="includes/main/main-promo-wheel.tpl"}
    {/if} *}

    {if !activeRoute('admin.plan.purchase', 'admin.promo.purchase', 'admin.promo.status', 'admin.plan.feature', 'admin.checkout', 'admin.checkout.*', 'admin.services.*') && segment(1) != 'terminal'}
        {include file="includes/main/main-sandbox-bar.tpl"}
    {/if}

    {include file="includes/js_translation_labels.tpl"}
    {include file="includes/global_variables.tpl"}
    {if segment(1) != 'terminal'}
        <script type="text/javascript" src="{assetic bundle="basic_bdl" output="js"}"></script>
        <script type="text/javascript" src="{assetic bundle="admin_main_bdl" output="js"}"></script>
        <script type="text/javascript" src="{assetic bundle="custom_bdl" output="js"}"></script>
        <script type="text/javascript" src="{$img_url}sitecp/js/build.min.js?{app('last_build')}"></script>
        <!-- BEGIN PAGE SPECIFIC PLUGINS INJECT -->
        {if !empty($js)}
            {foreach $js as $item}
                {$item nofilter}
            {/foreach}
        {/if}
        <!-- END PAGE SPECIFIC PLUGINS INJECT -->
        <script type="text/javascript">
            //Fixes the "bootstrap modal + CKEditor" focus fields bug
            $.fn.modal.Constructor.prototype.enforceFocus = function () {
                modal_this = this
                $(document).on('focusin.modal', function (e) {
                    if (modal_this.$element[0] !== e.target && !modal_this.$element.has(e.target).length
                        && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_select')
                        && !$(e.target.parentNode).hasClass('cke_dialog_ui_input_text')) {
                        modal_this.$element.focus()
                    }
                })
            };

            App.init();
        </script>
        <script>
            window.addEventListener('load', function() {
                const checkForCCPanel = setInterval(function() {
                    if(window.CCPanel) {
                        const style = document.createElement('style');
                        style.textContent = "*[data-ajax-panel] { pointer-events: auto; }";
                        document.head.appendChild(style);
                        clearInterval(checkForCCPanel);
                    }
                }, 20);
                checkForCCPanel;
            });
        </script>
    {/if}

    {*{include file="includes/main/main-crisp.tpl"}*}

    {if $flash_errors|default}
        {foreach $flash_errors->all() AS $err}
            {script}
                <script>
                    CCHelper.toastrError('{$err}');
                </script>
            {/script}
        {/foreach}
    {/if}

    {if $flash_success|default}
        {foreach $flash_success->all() AS $success}
            {script}
                <script>
                    CCHelper.toastrSuccess('{$success}');
                </script>
            {/script}
        {/foreach}
    {/if}
    <!-- End Google Tag Manager -->
    <style type="text/css">
        pre.sf-dump {
            z-index: 0;
        }
    </style>

    {if is_object($panelOpenHistory|default) && get_class($panelOpenHistory) == 'App\Helper\GridPanelOpen'}
        <script type="text/javascript">
            var url = $('<a href="{$panelOpenHistory->getUrl()}" data-ajax-panel="true">').hide(),
                title = document.title;
            {if $panelOpenHistory->getClass()}
            url.attr('data-panel-class', {json_encode($panelOpenHistory->getClass()) nofilter});
            {/if}
            url.appendTo('body').promise().done(function () {
                url.trigger('click');
                {if $panelOpenHistory->getCloseUrl()}
                $(document).on('cc.panel.success', function () {
                    var panel = url.data('panel-instance');
                    if (panel && panel.panel) {
                        panel.panel.on('dismiss.panel', function () {
                            History.pushState({
                                link: "{$panelOpenHistory->getCloseUrl()}"
                            }, title, "{$panelOpenHistory->getCloseUrl()}");
                        });
                    }
                });
                {/if}
            });
        </script>
    {/if}

    {*{if site('user.country') == 'BG'}*}
    {*{include file="includes/main/main-promo-panel.tpl"}*}
    {*{/if}*}

    {if $logs->isNotEmpty()}
        <script type="text/javascript">
            $(function () {
                let alerts = $('.alert-holder'),
                    sidebar = $('.body-alert .page-sidebar'),
                    pageBreadcrumb = $('.page-content .page-breadcrumb'),
                    wrapper = $('.body-alert > .page-container > .page-content'),
                    height = alerts.outerHeight();

                sidebar.css({
                    top: height
                });
                pageBreadcrumb.css({
                    top: height
                });
                wrapper.css({
                    'padding-top': parseInt(wrapper.css('padding-top')) + height
                });
            });
        </script>
    {/if}
    </body>
    </html>
{/if}
