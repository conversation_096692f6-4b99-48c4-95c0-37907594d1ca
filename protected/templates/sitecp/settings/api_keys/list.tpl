<div class="page-breadcrumb clearfix">
    {$breadcrumb = [
    [
        'title' => "{t}integration.header.title{/t}",
        'href' => "{route('admin.api_keys.list')}"
    ],
    [
        'title' => "{t}integration.header.api_keys{/t}"
    ]
    ]}
    {include file="includes/breadcrumb.tpl"}

    <div class="pull-right">
        {*<a data-modal-size="small" data-modal-ajax="{route('admin.api_keys.add')}" href="#"*}
        <a data-ajax-panel="true" href="{route('admin.api_keys.add')}"
           class="btn btn-primary">{t}integration.action.add_api_key{/t}</a>
    </div>

    <div class="pull-right">
        Site ID: {site('site_id')}
    </div>
</div>
<div id="noResults" class="hidden">
    <div class="content-padding">
        <div class="empty-box discounts">
            <span class="title">{t}integration.notify.no_api_keys_yet{/t}</span>
            <p>{t}integration.notify.no_api_keys_records_info{/t}</p>
            <div class="empty-image-holder container">
                {*<img src="{$img_url}sitecp/img/empty/blank-image.svg?{app('last_build')}">*}
            </div>
        </div>
    </div>
    <div class="help-box">
        <i class="fal fa-life-ring fa-3x cc-grey"></i>
        {*<i class="fal fa-life-ring fa-3x cc-grey"></i>*}
        <div class="help-container">
            <span>{t}integration.notify.no_api_keys_records_help{/t}</span>
            <a href="{app('support_url')}" target="_blank">{t}integration.notify.no_api_keys_records_help_link{/t}</a>
        </div>
    </div>
</div>
<div id="api_keys_wrapper" class="grid-wrapper" data-url="{route('admin.api_keys.list')}">
    <div class="grid-controls margin-top-30">
        {include file="includes/bulk.tpl" bulk="settings/api_keys/filters/api_keys_bulk"}
    </div>
    <div class="content-padding padding-top-0">
        <table class="listing">
            <thead>
            <tr>
                <th data-field="name_link" class="text-left" data-sort="no" data-class="integration-name">{t}integration.th.name{/t}</th>
                <th data-field="key" class="text-left" data-align="left" data-sort="no" data-class="integration-key">{t}integration.th.key{/t}</th>
                <th data-field="created" class="text-left" data-align="left" data-sort="no" data-class="integration-created">{t}integration.th.created{/t}</th>
                <th data-field="updated" class="text-left" data-align="left" data-sort="no" data-class="integration-updated">{t}integration.th.updated{/t}</th>
                <th data-field="checkbox" class="text-center" data-align="center" data-sort="no" data-class="integration-active">{t}global.active{/t}</th>
                <th class="text-center" data-field="actions" data-align="right" style="width: 100px;"
                    data-sort="no" data-class="integration-delete"></th>
            </tr>
            </thead>
        </table>
        <div class="table-footer">
            {include file="includes/bulk.tpl" bulk="settings/api_keys/filters/api_keys_bulk"}
        </div>
    </div>
</div>

{capture append="js"}
    <script>
        var $api_keys_wrapper = $('#api_keys_wrapper');

        $api_keys_wrapper.on('cc.ajax.success', '.delete', function () {
            $api_keys_wrapper.trigger('cc.ajax.reload');
        });

        $api_keys_wrapper.on('cc.ajax.success', function (e, data) {
            if (data.custom_data !== undefined && data.custom_data.records !== undefined) {
                if (data.custom_data.records == false) {
                    $('#noResults').removeClass('hidden');
                    $api_keys_wrapper.addClass('hidden');
                } else {
                    $('#noResults').addClass('hidden');
                    $api_keys_wrapper.removeClass('hidden');
                }
            }
        });
    </script>
{/capture}