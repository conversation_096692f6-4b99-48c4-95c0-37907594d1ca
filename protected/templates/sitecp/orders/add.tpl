<div class="form">
    <form class="messenger-form ajaxForm" id="NewOrder" role="form" action="#">
        <div class="box-title fixed-top">
            <div class="side-panel-header">
                <div class="left-controls">
                    <div class="close" data-dismiss="panel"></div>
                    <h3>Добави поръчка</h3>
                </div>
                <div class="right-controls">
                    <button class="btn btn-default" data-dismiss="panel">{t}global.cancel{/t}</button>
                    <button class="btn btn-primary js-add-category submit">{t}global.save{/t}</button>
                </div>
            </div>
        </div>
        <div class="box">
            <div id="order_customer" class="box" data-box-ajax="">
                <div class="form-group row">
                    <div class="col-md-6">
                        <div class="box-title">
                            <div class="box-title-text fixed-top">
                                <h5>Изберете потребител
                                    <a href="{route('admin.customers.edit')}" data-ajax-panel
                                       data-panel-class="wide" class="add-address" style="float:right;">
                                        <i class="fa fa-plus" aria-hidden="true"></i> {t}order.add_customer{/t}</a>
                                </h5>
                            </div>
                        </div>
                        <div class="box-section">
                            <div class="customer-search">
                                <input name="customer_id" id="customer_id" class="form-control select2_ajax" data-url="{route('admin.autocomplete.customer')}">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="box-title">
                            <div class="box-title-text fixed-top">
                                <h5>Изберете адрес за доставка
                                    <a href="{route('admin.customers.edit')}" data-ajax-panel
                                       data-panel-class="wide" class="add-address" style="float:right;">
                                        <i class="fa fa-plus" aria-hidden="true"></i> Добави адрес</a>
                                </h5>
                            </div>
                        </div>
                        <div class="box-section">
                            <div class="customer-search">
                                <div class="form-group" id="city-form">
                                    <select class="form-control select2me" name="city_id" id="city" disabled></select>
                                    <input type="hidden" name="city_name" value="{$city_json|default}" disabled>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>
</form>
</div>
{capture append="js"}
    <script type="text/javascript">
        $(function () {
            var $city_id = $("#city");
            var $city_name = $('input[name="city_name"]');
            var $city_form = $("#city-form");
            $('#customer_id').on('change', function () {
                var element = $(this);
                var customer_id = element.val();
                var url = '{route('admin.customers.list.addresses', ":customer_id")}';
                url = url.replace(":customer_id", customer_id);
                CC.ajax({
                    url: url,
                    success: function (json) {
                        if (json.results.length) {
                            $city_id.select2('destroy').empty();
                            $city_id.prop('disabled', false).removeClass('hidden');
                            $city_name.prop('disabled', false);
                            $city_form.removeClass('hidden');

                            $.each(json.results, function () {
                                $city_id.append($("<option></option>").attr("value", this.id).text(this.text));
                            });
                            App.initSelect2($city_form);
                            $city_id.trigger('change');
                        } else {
                            $city_id.select2('destroy').empty();
                            $city_id.prop('disabled', true).removeClass('hidden');
                            $city_form.removeClass('hidden');
                        }
                    }
                });
            });
        });
    </script>
{/capture}