{capture append="js"}
    <script type="text/javascript">
        $(function ()
        {
            {if $section == 'edit'}
                var $form = $('#editForm');
                var $submit = $('#editSubmit');
            {elseif $section == 'add'}
                var $form = $('#addForm');
                var $submit = $('#addSubmit');
            {/if}

            loadScripts([
                '{assetic bundle="multipart_form_bdl" output="js"}'
            ]);

            $(document).one('scripts-load-success', 'body', function ()
            {
                $form.on('submit', function (e)
                {
                    e.preventDefault();
                    tinymceSubmit();
                    Custom.submitFormMultipart($(this));
                });

            });

            $submit.on('click', function (e)
            {
                e.preventDefault();
                $form.trigger('submit');
            });

            {if $section == 'edit'}
            $('#productTypeNav li').off('click').on('click', function (e)
            {
                e.preventDefault();
            });
            $('.shifting-rows-js .shifting-row-js .tinymce.product-descr').each(function() {
                tinymce.EditorManager.execCommand('mceRemoveEditor', true, $(this).attr('id'));
            });
            {/if}

            /**
             * Manage hover on row
             */
            $('.shifting-rows-js')
                .on('mouseenter', '.shifting-row-js', function ()
                {
                    var $this = $(this);

                    if ($this.is(':last-of-type')) {
                        $this.next('.add-row-button-js').find('.add-row-js').addClass('hovered');
                    }

                    $this.prev('.add-row-button-js').find('.add-row-js').addClass('hovered');
                })
                .on('mouseleave', '.shifting-row-js', function ()
                {
                    var $this = $(this);

                    if ($this.is(':last-of-type')) {
                        $this.next('.add-row-button-js').find('.add-row-js').removeClass('hovered');
                    }

                    $this.prev('.add-row-button-js').find('.add-row-js').removeClass('hovered');
                });

            /**
             * Manage events to add rows
             */
            $(document).on('click', '.add-row-js', function ()
            {
                var $btn = $(this).closest('.add-row-button-js');
                var $rowContent = $btn.next('.shifting-row-js');
                var newId = 'tinymce-' + CC.uniqid();
                var $btnClone,
                    $newBtn,
                    $rowContentClone,
                    textId;

                if (!$rowContent.length) {
                    $rowContent = $btn.prev('.shifting-row-js');
                }

                textId = $rowContent.find('.tinymce').attr('id');
                tinymce.EditorManager.execCommand('mceRemoveEditor', true, textId);

                $btnClone = $btn.clone();
                $newBtn   = $btnClone.insertBefore($btn);
                $rowContentClone = $rowContent.clone();

                $rowContentClone.find('textarea').val('').attr('id', newId);
                $rowContentClone.find('input[type="text"]').val('');
                $rowContentClone.insertBefore($btn);

                tinymce.EditorManager.execCommand('mceAddEditor', true, textId);
                tinymce.EditorManager.execCommand('mceAddEditor', true, newId);

                addButtonPosition($newBtn);
                addRowNumber();
            });

            /**
             * Manage events to remove rows
             */
            $(document).on('click', '.remove-row-js', function ()
            {
                var $this = $(this);
                var $rowContent = $this.closest('.shifting-row-js');
                var $btn = $rowContent.prev('.add-row-button-js');
                var $row = $('.shifting-row-js');
                var textId = $rowContent.find('.tinymce').attr('id');

                if ($row.length > 1) {
                    tinymce.EditorManager.execCommand('mceRemoveEditor', true, textId);
                    $btn.remove();
                    $rowContent.remove();
                    addRowNumber();
                }
            });

            /**
             * Manage events to move rows up
             */
            $(document).on('click', '.up-row-js', function ()
            {
                var $this = $(this);
                var $rowContent = $this.closest('.shifting-row-js');
                var $btn = $rowContent.prev('.add-row-button-js');
                var $prevRowContent = $btn.prev('.shifting-row-js');
                var textId = $rowContent.find('.tinymce').attr('id');

                if ($prevRowContent.length) {
                    tinymce.EditorManager.execCommand('mceRemoveEditor', true, textId);
                    $rowContent.insertBefore($prevRowContent);
                    $btn.insertBefore($prevRowContent);
                    tinymce.EditorManager.execCommand('mceAddEditor', true, textId);
                    addRowNumber();
                }
            });

            /**
             * Manage events to move rows down
             */
            $(document).on('click', '.down-row-js', function ()
            {
                var $this = $(this);
                var $rowContent = $this.closest('.shifting-row-js');
                var $btn = $rowContent.next('.add-row-button-js');
                var $nextRowContent = $btn.next('.shifting-row-js');
                var textId = $rowContent.find('.tinymce').attr('id');

                if ($nextRowContent.length) {
                    tinymce.EditorManager.execCommand('mceRemoveEditor', true, textId);
                    $rowContent.insertAfter($nextRowContent);
                    $btn.insertAfter($nextRowContent);
                    tinymce.EditorManager.execCommand('mceAddEditor', true, textId);
                    addRowNumber();
                }
            });

            $(window).on('load resize', function ()
            {
                $('.add-row-button-js').each(function ()
                {
                    var $this = $(this);

                    addButtonPosition($this);

                    $this.addClass('visible');
                });
            });

            function tinymceReinitialize(id)
            {
                $(id).init();
            }

            function addRowNumber()
            {
                var i = 1;

                $('.shifting-row-js').each(function ()
                {
                    $(this).find('.shifting-row-numner-js').text(i);
                    $(this).find('textarea').attr('name', 'content['+i+'][answers]');
                    $(this).find('input[type=text]').attr('name', 'content['+i+'][questions]');
                    i++;
                });
            }

            function addButtonPosition($btnContainer)
            {
                var $nextRow = $btnContainer.next('.shifting-row-js');

                if ($nextRow.length) {
                    var top = $nextRow.find('.main-title').outerHeight(true);
                }

                $btnContainer.css({
                    top: top
                });
            }
        });
    </script>
{/capture}