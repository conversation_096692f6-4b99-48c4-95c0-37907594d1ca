{capture append="js"}
    <script type="text/javascript">

        loadScripts([
            '{assetic bundle="multipart_form_bdl" output="js"}',
            '{$img_url}sitecp/plugins/jquery.form.min.js'
        ]);


        $(function ()
        {

            $('#edit_{$group->mapping}').on('cc.ajax.success', function (e, data)
            {

                var $tree = $('#{ucfirst($group->mapping)}NavigationTree');
                var parsed_data;

                /* update category */
                $tree.trigger('cc.tree.node.update', data);
                if (data.target_id) {
                    /* move from one place to another */
                    $tree.trigger('cc.tree.node.move', data);
                    if (data.old_parent_id) {
                        /* updating old parent */
                        parsed_data = {
                            node: data.old_parent_node,
                            node_id: data.old_parent_id
                        };
                        $tree.trigger('cc.tree.node.update', parsed_data);
                    }

                    if (data.new_parent_id) {
                        parsed_data = {
                            node: data.new_parent_node,
                            node_id: data.new_parent_id
                        };
                        /* update new parent */
                        $tree.trigger('cc.tree.node.update', parsed_data);
                        /* open new parent */
                        $tree.trigger('cc.tree.node.open', parsed_data);
                    }

                } else {
                    if (data.parent_id == undefined) {
                        $tree.trigger('cc.tree.reloadTree');
                    } else {
                        $tree.trigger('cc.tree.reloadTree', {
                            node_id: data.parent_id
                        });
                    }
                }

                $tree.trigger('cc.tree.node.blink', data);

            });

            $(document).one('scripts-load-success', 'body', function ()
            {
                $('#edit_{$group->mapping}').on('submit', function (e)
                {
                    e.preventDefault();
                    Custom.submitFormMultipart($(this));
                });

            });


        });
    </script>
{/capture}
<div class="row form">
    <form action="{route('admin.navigation.edit', [$group->mapping, $navigation->id])}" id="edit_{$group->mapping}" role="form"
          class="ajaxForm">
        <div class="box-title fixed-top">
            <div class="side-panel-header">
                <div class="left-controls">
                    <div class="close" data-dismiss="panel"></div>
                </div>
                <div class="right-controls">
                    {*<button class="btn btn-default" data-dismiss="panel">{t}global.cancel{/t}</button>*}
                    <button class="btn btn-primary submit">{t}global.save{/t}</button>
                </div>
            </div>
        </div>
        <input type="hidden" name="link_type" value="{$navigation->type}">
        <div class="main-fields-holder clearfix">
            <div class="col-xs-6">
                <div class="form-group padding-top-0">
                    {if $navigation->link_type === 'group'}
                        <label class="control-label">{t}menu.label.group_name{/t}</label>
                        <span class="help-block">{t}menu.help.group_name{/t}</span>
                    {else}
                        <label class="control-label">{t}menu.label.link_name{/t}</label>
                        <span class="help-block">{t}menu.help.link_name{/t}</span>
                    {/if}
                    <input type="text" name="name" value="{$navigation->name}" class="form-control" data-autofocus=""/>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group padding-top-0">
                    <label class="control-label">{t}menu.label.parent{/t}</label>
                    <span class="help-block">{t}menu.help.parent{/t}</span>
                    <select name="parent_id" class="form-control select2me"
                            data-placeholder="{t}global.select{/t}...">
                        <option value="">{t}global.label_no_parent{/t}</option>
                        {if isset($navigation)}
                            {include file="includes/recursive_items.tpl" items=$navigations allitems=$navigations level=0 item_info=$navigation}
                        {else}
                            {include file="includes/recursive_items.tpl" items=$navigations allitems=$navigations level=0}
                        {/if}
                    </select>
                </div>
            </div>
        </div>
        {if $navigation->type == 'product'}
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.product_select{/t}</label>

                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}menu.help.product_select{/t}</span>
                        <input name="link_id" class="form-control select2_ajax" value="{$navigation->auto_complete_value}"
                               data-url="{route('admin.autocomplete.products')}"
                               data-placeholder="{t}menu.ph.select_product{/t}" data-dropdownclass="select2-modal"/>
                    </div>
                </div>
            </div>
        {elseif $navigation->type == 'category'}
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.category_select{/t}</label>

                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}menu.help.category_select{/t}</span>
                        {include file="categories/tree_select.tpl" select_name="link_id" category_id=$navigation->auto_complete_value|default}
                    </div>
                </div>
            </div>
        {elseif $navigation->type == 'vendor'}
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.vendor_select{/t}</label>

                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}menu.help.vendor_select{/t}</span>
                        <input name="link_id" class="form-control select2_ajax" value="{$navigation->auto_complete_value}"
                               data-url="{route('admin.autocomplete.vendor')}"/>
                    </div>
                </div>
            </div>
        {elseif $navigation->type == 'blog'}
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.blog_select{/t}</label>

                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}menu.help.blog_select{/t}</span>
                        <input name="link_id" class="form-control select2_ajax" value="{$navigation->auto_complete_value}"
                               data-url="{route('admin.autocomplete.blog')}"/>
                    </div>
                </div>
            </div>
        {elseif $navigation->type == 'article'}
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.article_select{/t}</label>

                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}menu.help.article_select{/t}</span>
                        <input name="link_id" class="form-control select2_ajax" value="{$navigation->auto_complete_value}"
                               data-url="{route('admin.autocomplete.blog-article')}"/>
                    </div>
                </div>
            </div>
        {elseif $navigation->type == 'page'}
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.page_select{/t}</label>

                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}menu.help.page_select{/t}</span>
                        <select name="link_id" class="form-control select2me">
                            {foreach $pages as $page}
                                <option value="{$page->id}"{if $page->id == $navigation->auto_complete_value} selected="selected"{/if}>{$page->name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
            </div>
        {elseif $navigation->type == 'url'}
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.external_link_address{/t}</label>

                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}menu.help.external_link_address{/t}</span>
                        <input type="text" name="url" class="form-control" value="{$navigation->auto_complete_value}"/>
                    </div>
                </div>
            </div>
        {elseif $navigation->type == 'section'}
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.theme_section_select{/t}</label>

                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}menu.help.theme_section_select{/t}</span>
                        <select name="route" class="form-control select2me">
                            {foreach $sections as $name => $section}
                                <option value="{$name}"{if $name == $navigation->route} selected="selected"{/if}>{$section}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
            </div>
        {elseif $navigation->type === 'selection'}
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.selection_select{/t}</label>
                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}menu.help.selection_select{/t}</span>
                        <select name="link_id" class="form-control select2me">
                            {foreach $selections as $selection}
                                <option value="{$selection->id}"
                                        {if $navigation->auto_complete_value == $selection->id}selected{/if}>{$selection->name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
            </div>
        {/if}
        {if !in_array($navigation->type, ['group', 'mailchimp', 'snippet'])}
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.open_in_new_window{/t}</label>

                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}menu.help.open_in_new_window{/t}</span>
                        <input type="checkbox" name="blank" class="switch"
                               value="1"{if $navigation->blank} checked="checked"{/if} />
                    </div>
                </div>
            </div>
        {/if}
        {if $navigation->type === 'snippet'}
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.snippet{/t}</label>

                    <textarea name="widget_text" class="form-control"
                              placeholder="{t}menu.ph.snippet{/t}">{$navigation->widget_text nofilter}</textarea>
                </div>
            </div>
        {/if}

        {if 'snippet' != $navigation->type && $theme_config->bool('functions.navigations.icon.status') && $icons}
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="control-label">{t}menu.label.icon{/t}</label>
                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}menu.help.icon{/t}</span>
                        <select name="class" class="form-control select2me"
                                data-placeholder="{t}global.select{/t}..." data-type="class">
                            <option value="">{t}global.select{/t}</option>
                            {foreach from=$icons item=$icon}
                                <option {if $icon['class']==$navigation->class}selected="selected"{/if}
                                        value="{$icon['class']}">{$icon['name']}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
            </div>
        {/if}

        {if in_array(site('site_id'), config('integration.zora.id', []))}
            <div class="clearfix"></div>
            <div class="col-xs-12" style="margin-top: 20px">
                <div class="form-group file-preview-holder">
                    <label class="control-label">{t}menu.label.image{/t}</label>

                    <div class="main-box skip-has-error padding-0 upload-box">
                        <div class="image-upload-holder">
                        <span class="image img-border file-preview">
                            <img src="{$navigation->getImage()}" alt="">
                            {if $navigation->hasImage()}
                                <a href="{route('admin.bulk.delete_image', ['navigation', $navigation->id])}"
                                   data-confirm="{t}menu.confirm.image_delete{/t}" class="remove-image-quick js-remove-image-quick"><i
                                            class="fal fa-times-circle fa-lg cc-grey"></i><span>{t}global.action.drop_existing_image{/t}</span></a>
                            {/if}
                        </span>
                        </div>
                        <div class="upload-field-box">
                            <span class="help-block">{t}menu.help.image{/t}</span>

                            <div id="digital_file_holder" style="width: 100%;">
                                <div class="fileupload fileupload-new input-group" data-provides="fileupload">
                               <span class="form-control" data-trigger="fileupload">
                                   <span class="uneditable-input big">
                                       <i class="fal fa-file fileupload-exists"></i>
                                       <span class="fileupload-preview"></span>
                                   </span>
                               </span>
                                    <a href="#" class="btn-revert fileupload-exists"
                                       data-dismiss="fileupload"><i class="fal fa-times-circle fa-lg cc-grey"></i></a>
                                    <span class="input-group-addon btn btn-white btn-file">
                                   <span class="fileupload-new"><i class="fal fa-cloud-upload fa-lg"></i> {t}global.action.select_file{/t}</span>
                                   <span class="fileupload-exists"><i class="fal fa-cloud-upload fa-lg"></i></span>
                                   <input type="file" name="image" class="default preview"/>
                               </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        {/if}
    </form>
</div>
