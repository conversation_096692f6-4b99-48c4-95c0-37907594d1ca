<div class="modal fade" id="ReportTable" role="basic" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
                <h4 class="modal-title">{t}report.table_visualisation_of_results{/t}</h4>
            </div>
            <div class="modal-body">
                <table class="table table-hover listing">
                    <thead>
                        <tr>
                            <th>{t}report.{$group}{/t}</th>
                            <th class="text-right" width="100px;">{t}report.orders{/t}</th>
                            <th class="text-right" width="200px;">{t}report.order_avg{/t}</th>
                            <th class="text-right" width="200px;">{t}report.total_income{/t}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {foreach $report_data as $group => $item}
                            <tr>
                                <td>{$item.group nofilter}</td>
                                <td class="text-right">{$item.count}</td>
                                <td class="total text-right">{$item.price_average nofilter}</td>
                                <td class="total text-right">{$item.price_total nofilter}</td>
                            </tr>
                        {foreachelse}
                            <tr>
                                <td colspan="3">{t}report.no_results{/t}</td>
                            </tr>
                        {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div class="container">
    <div class="col-md-3">
        <div class="total-report order-income border-left-0">
            <div class="total-values">
                <span class="title-report">{t}report.total_order_income{/t}</span>
                <span class="amount">{$totals.amount}</span>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="total-report order-value">
            <div class="total-values">
                <span class="title-report">{t}report.avg_order_value{/t}</span>
                <span class="amount">{$totals.average}</span>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="total-report order-count">
            <div class="total-values">
                <span class="title-report">{t}report.total_orders{/t}</span>
                <span class="amount">{$totals.count}</span>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="total-report product-count">
            <div class="total-values">
                <span class="title-report">{t}report.total_products{/t}</span>
                <span class="amount">{$totals.products}</span>
            </div>
        </div>
    </div>
</div>