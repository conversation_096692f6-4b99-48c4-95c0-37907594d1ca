{include file="../dependency/settings.tpl"}

<div class="page-breadcrumb clearfix">
    {$breadcrumb = [
    [
    'title' => "{t}sidebar.apps{/t}",
    'href' => "{route('admin.apps')}"
    ],
    [
    'href' => "{asset('')}apps/etsy",
    'title' => "{t}etsy.info.title{/t}"
    ]
    ]}
    {include file="includes/breadcrumb.tpl"}
    {strip}
        <div class="pull-right">
            <a class="btn btn-default" href="{route('apps.etsy.settings')}">
                {t}saleschannel.heading.etsy_config{/t}
            </a>
            <a href='#' class="btn btn-primary" data-modal-size="small" data-modal-ajax="{route('apps.etsy.add_listing')}">
                {t}etsy.action.add.listing{/t}
            </a>
        </div>
    {/strip}
</div>

<div class="wrapper">
    {include file="./tabs.tpl"}
    {if $connected && count($shops)}
        <div class="container-small">
            <form class="save_config" action="{route('apps.etsy.settings')}" id="editForm" role="form">
                <div class="box">
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-6">
                                <label class="control-label">{t}etsy.select_etsy_store{/t}</label>
                                <select name="shop_id" class="form-control select2me input-lg" data-no-input="true">
                                    <option selected="selected" value="" >-- {t}filter.all{/t} --</option>
                                    {foreach $shops as $shop}
                                        <option {if $settings['shop_id'] == $shop['shop_id']} selected="selected"{/if} value="{$shop['shop_id']}">{$shop['shop_name']}</option>
                                    {/foreach}
                                </select>
                            </div>
                            <div class="col-xs-6">
                                <div class="form-group">
                                    <label class="control-label">{t}etsy.shipping_templates{/t}</label>
                                    <select name="shipping_template_id" class="form-control select2me input-lg" data-no-input="true">
                                        <option selected="selected" value="" >-- {t}filter.all{/t} --</option>
                                        {foreach $shipping_templates as $shipping_template}
                                            <option {if $settings['shipping_template_id'] == $shipping_template['shipping_template_id']} selected="selected"{/if} value="{$shipping_template['shipping_template_id']}">{$shipping_template['title']}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-6">
                                <label class="control-label">{t}etsy.listing.who_made{/t}</label>
                                <select name="who_made" class="form-control select2me input-lg" data-no-input="true">
                                    <option selected="selected" value="" >-- {t}filter.all{/t} --</option>
                                    {foreach $who_made_choices as $key => $value}
                                        <option {if $settings['who_made'] == $key} selected="selected"{/if} value="{$key}">{$value}</option>
                                    {/foreach}
                                </select>
                            </div>
                            <div class="col-xs-6">
                                <label class="control-label">{t}etsy.listing.when_made{/t}</label>
                                <select name="when_made" class="form-control select2me input-lg" data-no-input="true">
                                    <option selected="selected" value="" >-- {t}filter.all{/t} --</option>
                                    {foreach $when_made_choices as $key=>$value}
                                        <option {if $settings['when_made'] == $key} selected="selected"{/if} value="{$key}">{$value}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-6">
                                <label class="control-label">{t}etsy.listing.to_be_sync{/t}</label>
                                <select name="what_to_sync" class="form-control select2me input-lg" data-no-input="true">
                                    <option selected="selected" value="" >-- {t}filter.all{/t} --</option>
                                    <option {if $settings['what_to_sync'] == 'nothing'} selected="selected"{/if} value="nothing">{t}etsy.listing.nothing_to_be_sync{/t}</option>
                                    <option {if $settings['what_to_sync'] == 'price'} selected="selected"{/if} value="price">{t}etsy.listing.price_to_be_sync{/t}</option>
                                    <option {if $settings['what_to_sync'] == 'quantity'} selected="selected"{/if} value="quantity">{t}etsy.listing.quantity_to_be_sync{/t}</option>
                                    <option {if $settings['what_to_sync'] == 'price_and_quantity'} selected="selected"{/if} value="price_and_quantity">{t}etsy.listing.price_and_quantity_to_be_sync{/t}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-6">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="form-control-check">
                                            <input name="listing_is_supply" type="checkbox" value="1" {if $settings['listing_is_supply']|default} checked="checked"{/if}>
                                            {t}etsy.listing.is_supply{/t}
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-4">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="form-control-check">
                                            <input name="listing_is_active" type="checkbox" value="1" {if $settings['listing_is_active']|default} checked="checked"{/if}>
                                            {t}etsy.listing.state.is_active{/t}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-6">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="form-control-check">
                                            <input name="update_in_etsy" type="checkbox" value="1" {if $settings['update_in_etsy']|default} checked="checked"{/if}>
                                            {t}etsy.listing.state.update_in_etsy{/t}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-6" align="left">
                                <a href='#' id="map_params" class="btn btn-default col-xs-12" data-modal-size="small" data-modal-ajax="{route('apps.etsy.map_parameters')}">
                                    {t}etsy.action.map_parameters{/t}
                                </a>
                            </div>
                            <div class="col-xs-6" align="right">
                                <button class="btn btn-primary" type="submit" id="editSubmit">{t}global.save{/t}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    {elseif $connected}
        <div class="content-padding">
            <div class="empty-box regions">
                <span class="title">{t}etsy.info.no_shops{/t}</span>
                <div class="empty-image-holder container">
                    {*<img src="{$img_url}sitecp/img/empty/blank-image.svg?{app('last_build')}">*}
                </div>
            </div>
        </div>
    {else}
        <div class="app-wrapper">
            {include file="../includes/app-intro.tpl" title=__('etsy.header.setting') text=__('etsy.help.setting')}
            {include file="../includes/app-icons.tpl"  app_icon='icon-etsy-beta.png'}
        </div>

        <div class="container-small center">
            <div class="box-section">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <a href="{route('admin.etsy.authorize')}" class="btn btn-primary">{t}etsy.action.connect_with_etsy{/t}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {/if}
</div>

{capture append="js"}
    <script type="text/javascript">
        $(function () {
            $('#editForm').on('cc.ajax.success', function (e) {
                $('#map_params').trigger('click');
            });
        });
    </script>
{/capture}
