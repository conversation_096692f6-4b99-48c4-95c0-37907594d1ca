{include file="./includes/dependency.tpl"}
{include file="./includes/overrides_dependency.tpl"}

<div class="form">
    <form action="{$action}" id="TaxForm" role="form" class="ajaxForm">
        <div class="box-title fixed-top">
            <div class="side-panel-header">
                <div class="left-controls">
                    <div class="close" data-dismiss="panel"></div>
                </div>
                <div class="right-controls">
                    <button class="btn btn-default" data-dismiss="panel">{t}global.cancel{/t}</button>
                    <button class="btn btn-primary submit">{t}global.save{/t}</button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="main-fields-holder clearfix">
                <div class="col-xs-6">
                    <div class="form-group padding-top-0">
                        <label class="control-label">{t}tax.label.name{/t}</label>
                        <span class="help-block">{t}tax.help.name{/t}</span>
                        <input type="text" name="tax[name]" value="{$tax->name}" class="form-control" data-autofocus="" placeholder="{t}tax.ph.name{/t}" />
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group padding-top-0">
                            <label class="control-label">{t}tax.label.description{/t}</label>
                            &nbsp;<i class="glyphicon glyphicon-info-sign tooltips" title="{t}tax.help.description{/t}" data-placement="top"></i>
                        <textarea name="tax[description]" class="form-control" placeholder="{t}tax.ph.description{/t}">{$tax->description}</textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="radio-holder text-center padding-top-40 padding-bottom-40">
                <label class="checkbox-inline"><input type="radio" name="tax[type]" value="percent"{if $tax->type == 'percent'} checked="checked"{/if} /> <span>{t}tax.switch.percent{/t}</span></label>
                <label class="checkbox-inline"><input type="radio" name="tax[type]" value="flat"{if $tax->type == 'flat'} checked="checked"{/if} /> <span>{t}tax.switch.flat{/t}</span></label>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-4">
                <div class="form-group padding-top-0">
                    <label class="control-label">{t}tax.label.tax{/t}</label>
                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}tax.help.tax{/t}</span>
                        <div id="tax_amount_type" class="input-group">
                            {if $currency[1] == 'before'}
                                <span id="tax_type_flat" class="input-group-addon{if $tax->type == 'percent'} hidden{/if} gray input-group-addon-left">
                                    {$currency[0]}
                                </span>
                            {/if}
                            <input type="text" name="tax[tax]" value="{$tax->tax_input}" class="form-control{if $tax->type == 'percent'} mask-percent{else} mask-currency{/if}" placeholder="{t}tax.ph.tax{/t}" />
                            {if $currency[1] == 'after'}
                                <span id="tax_type_flat" class="input-group-addon{if $tax->type == 'percent'} hidden{/if} gray input-group-addon-right">
                                    {$currency[0]}
                                </span>
                            {/if}
                            <span id="discount_type_percent" class="input-group-addon{if $tax->type == 'flat'} hidden{/if} gray input-group-addon-right">
                                {t}discount.addon.percent_discount{/t}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-4" id="vat_tax">
                <div class="form-group padding-top-0">
                    <label class="control-label">{t}tax.label.vat_tax{/t}</label>
                    <div class="main-box skip-has-error">
                        <span class="help-block">{t}tax.help.vat_tax{/t}</span>
                        <input name="tax[vat]" id="tax_is_vat" type="checkbox" class="switch" value="yes" data-on="{t}tax.switch.vat_tax{/t}"{if $tax->vat == 'yes'} checked="checked"{/if} />
                    </div>
                </div>
            </div>
            <div class="col-xs-4">
                <div class="form-group padding-top-0">
                    <label class="control-label">{t}tax.act.include_vat{/t}</label>
                    <div class="main-box skip-has-error margin-bottom-30">
                        <span class="help-block">{t}tax.tip.include_vat{/t}</span>
                        <input name="tax[price_with_vat]" type="checkbox" class="switch" value="1" data-on="{t}tax.switch.include_price{/t}"{if $tax->price_with_vat|default:1} checked="checked"{/if} />
                    </div>
                </div>
            </div>
        </div>

        <input type="hidden" name="tax[shipping]" value="{if $tax->vat == 'no'}no{else}yes{/if}" readonly="readonly">
        {*
        <div class="row{if $tax->type == 'flat' || $tax->vat == 'no'} hidden{/if}" id="tax_shipping">
            <div class="radio-holder text-center padding-top-40 padding-bottom-40">
                <label class="checkbox-inline"><input type="radio" name="tax[shipping]" value="no"{if $tax->shipping == 'no'} checked="checked"{/if} /> {t}tax.switch.before_shipping{/t}</label>
                <label class="checkbox-inline"><input type="radio" name="tax[shipping]" value="yes"{if $tax->shipping == 'yes'} checked="checked"{/if} /> {t}tax.switch.after_shipping{/t}</label>
            </div>
        </div>
        *}

        <div class="row {if $tax->vat == 'yes'}hidden{/if} js-geo-zones-holder" id="geo_zones_holder">
            <div class="col-md-12">
                <div class="form-group padding-0">
                    <label class="control-label">{t}tax.label.regions{/t}</label>
                    <div class="main-box padding-top-0 clearfix skip-has-error">
                        <div class="main-box-header">
                            <p>{t}tax.help.target{/t}</p>
                            <input {if $tax->vat == 'yes'}disabled="disabled"{/if} name="tax[target]" value="restofworld" type="checkbox" class="switch" value="yes"{if $tax->vat == 'no' && $tax->target == 'restofworld'} checked="checked"{/if} /><span class="switch-label">{t}tax.label.the_whole_world{/t}</span>
                        </div>
                        <div class="clearfix main-box-section skip-has-error js-regions-holder {if $tax->vat == 'no'}{inputJsError('tax-geo-zone-id')}{/if}">
                            <select name="tax[geo_zone_id]" class="form-control select2me" {if $tax->vat == 'yes'}disabled="disabled"{/if}>
                                <option value="">{t}global.select{/t}</option>
                                {foreach $geo_zones AS $geo_zone}
                                    <option {if $tax->vat == 'no' && $tax->geo_zone_id == $geo_zone->id}selected="selected"{/if} value="{$geo_zone->id}">{$geo_zone->name}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row {if $tax->vat == 'no'}hidden{/if} js-without-vat-reasons">
            <div class="col-xs-12">
                <div class="form-group padding-top-0">
                    <label class="control-label">{t}invoice.without_vat_reasons{/t}</label>
                    <textarea type="text" name="tax[without_vat_reasons]" class="form-control" data-autofocus="" style="min-height: 120px;">{$tax->without_vat_reasons}</textarea>
                </div>
            </div>
        </div>

        <div class="row {if $tax->vat == 'no'}hidden{/if} js-geo-zones-holder margin-top-20" id="geo_zones_countries_holder">
            <div class="col-md-12">
                <div class="form-group padding-0">
                    <label class="control-label">{t}tax.label.regions{/t}</label>
                    <div class="main-box padding-top-0 clearfix skip-has-error">
                        <div class="main-box-header">
                            <p>{t}tax.help.target{/t}</p>
                            <input {if $tax->vat == 'no'}disabled="disabled"{/if} name="tax[target]" value="restofworld" type="checkbox" class="switch" value="yes"{if $tax->vat == 'yes' && $tax->target == 'restofworld'} checked="checked"{/if} /><span class="switch-label">{t}tax.label.the_whole_world{/t}</span>
                        </div>
                        <div class="clearfix main-box-section skip-has-error js-regions-holder {if $tax->vat == 'yes'}{inputJsError('tax-geo-zone-id')}{/if}">
                            <select name="tax[geo_zone_id]" class="form-control select2me" {if $tax->vat == 'no'}disabled="disabled"{/if}>
                                <option value="">{t}global.select{/t}</option>
                                {foreach $countries_geo_zones AS $geo_zone}
                                    <option {if $tax->vat == 'yes' && $tax->geo_zone_id == $geo_zone->id}selected="selected"{/if} value="{$geo_zone->id}" data-countries="{$geo_zone->values->pluck('country_iso2')}">{$geo_zone->name}</option>
                                {/foreach}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {include file="./includes/regions.tpl"}
        {include file="./includes/categories.tpl"}
    </form>
</div>