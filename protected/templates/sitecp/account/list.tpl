{$admin = Auth::admin()}
<div class="page-breadcrumb clearfix">
    <ul class="breadcrumb">
        <li>
            <span>{t}account.header.staff{/t}</span>
        </li>
    </ul>
    <div class="pull-right">
        {if $admin.type|default == 'owner'}
            <a href="javascript:;" data-ajax="{route('admin.account.logout-all')}" data-confirm="{t}account.confirm.force_relogin{/t}" class="btn btn-default" id="btn-admins-remove">
                {t}account.action.force_relogin{/t}
            </a>
        {/if}
        {*<a href="javascript:;" data-modal-size="wide" data-modal-ajax="{route('admin.account.add')}" class="btn btn-primary" id="btn-admins-add">{t}account.action.add_moderator{/t}</a>*}
        <a href="{route('admin.account.add')}" data-panel-class="wide" data-ajax-panel="true" class="btn btn-primary" id="btn-admins-add">{t}account.action.add_moderator{/t}</a>
        {include file="includes/breadcrumbs_actions.tpl" grid=false}
    </div>
</div>

<div id="noResults" class="no-results-admins {if $adminExists}hidden{/if}">
    <i class="ico ico-72 ico-products"></i><br />
    <div>{t}account.header.no_moderators_added_yet{/t}</div><br />
    {*<a href="javascript:;" data-modal-size="wide" data-modal-ajax="{route('admin.account.add')}" class="btn btn-lg btn-success" id="btn-admins-add-main"><span class="glyphicon glyphicon-plus"></span> {t}account.action.add_moderator{/t}</a>*}
    <a href="{route('admin.account.add')}" data-panel-class="wide" data-ajax-panel="true" class="btn btn-lg btn-success" id="btn-admins-add-main"><span class="glyphicon glyphicon-plus"></span> {t}account.action.add_moderator{/t}</a>
</div>

<div id="admins_wrapper" class="grid-wrapper grid-admins {if !$adminExists}hidden{/if}" data-url="{route('admin.account.admins')}" data-view_cookie="admins">
    <div class="content-padding">
        <table class="listing">
            <thead>
                <tr>
                    <th data-field="name_list" class="sorting" data-class="account-username">{t}account.th.username{/t}</th>
                    <th data-field="email" class="text-center sorting" data-align="center" data-class="email-style">{t}account.th.email{/t}</th>
                    <th data-field="address" class="text-center" data-sort="no" data-align="center" data-class="address-style">{t}account.th.address{/t}</th>
                    <th data-field="phone" class="text-center" data-sort="no" data-align="center" data-class="account-phone">{t}account.th.phone{/t}</th>
                    <th data-field="type" class="text-center sorting" data-align="center" data-class="account-type">{t}account.th.type{/t}</th>
                    <th data-field="last_edited" class="text-center sorting" data-align="center" data-class="account-last-edited">{t}account.th.last_edited{/t}</th>
                    <th data-field="remove" class="text-center" data-align="center" data-sort="no" data-class="account-delete"></th>
                </tr>
            </thead>
        </table>
    </div>
    <div class="grid row" data-item-class="col-lg-2 col-xs-3">
        <div class="prototype clearfix text-center">
            <span data-field="image_grid" class="customer-image"></span>
            <div data-field="name_grid" class="costumer-name"></div>
            <div data-field="email" class="costumer-email"></div>
            <div data-field="type" class="costumer-group"></div>
            <div data-field="address" class="costumer-address"></div>
            <div class="row text-center margin-bottom-10 margin-top-10">
                <div class="col-xs-12"><span data-field="phone_grid" class="costumer-address"></span></div>
            </div>
        </div>
    </div>
</div>

{capture append="js"}
    <script type="text/javascript">
        var $grid = $("#admins_wrapper");

        $grid.on('cc.ajax.success', function (e, data) {
            if (data.custom_data !== undefined && data.custom_data.records !== undefined) {
                if (data.custom_data.records == false) {
                    $('#noResults').removeClass('hidden');
                    $grid.addClass('hidden');
                } else {
                    $('#noResults').addClass('hidden');
                    $grid.removeClass('hidden');
                }
            }
        });

        $grid.on('cc.ajax.success', 'a.delete', function() {
            $grid.trigger("cc.ajax.reload");
        });

    </script>
{/capture}