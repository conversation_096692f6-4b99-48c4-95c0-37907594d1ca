{include file="./includes/dependency.tpl"}
<form action="{$route}" id="parameterEditForm" method="post" class="form ajaxForm" >
    <div class="box-title fixed-top">
        <div class="side-panel-header">
            <div class="left-controls">
                <div class="close" data-dismiss="panel"></div>
                <h3>{t}{$title}{/t}</h3>
            </div>
            <div class="right-controls">
                <button class="btn btn-default" data-dismiss="panel">{t}global.cancel{/t}</button>
                <button class="btn btn-primary submit">{t}global.save{/t}</button>
            </div>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-xs-6">
            <label for="parameterName">{t}product.label.parameter_name{/t}</label>
            <input type="text" class="form-control" name="name" id="parameterName" value="{$parameter->name}">
        </div>
        <div class="col-xs-6">
            <label for="parameterName">{t}product.label.parameter_type{/t}</label>
            <select name="type" class="form-control select2me" data-no-input="true">
                <option value="">{t}global.select{/t}</option>
                {foreach $types AS $key => $value}
                <option value="{$key}" {if $parameter->type == $key}selected="selected"{/if}>{$value}</option>
                {/foreach}`
            </select>
        </div>
    </div>
    <div class="form-group row">
        <div class="col-xs-10">
            {t}product.label.product_in_listing{/t}
        </div>
        <div class="col-xs-2">
            <span class="switch-holder" id="switch-new">
                <input name="in_listing" id="in_listing_is_cheked" value="1" type="checkbox" class="switch" {if !is_null($parameter->next_update) && $parameter->next_update->isPast() == false}disabled{/if} {if $parameter->in_listing} checked="checked" {/if} />
            </span>
        </div>
    </div>
    <div class="form-group row hidden" id="show_label">
        <div class="col-xs-10">
            {t}product.label.parameter_add_name{/t}
        </div>
        <div class="col-xs-2">
            <span class="switch-holder" id="switch-new">
                <input name="show_label" value="1" id="show_label_input" type="checkbox" class="switch" {if !is_null($parameter->next_update) && $parameter->next_update->isPast() == false}disabled{/if} {if $parameter->in_listing && $parameter->show_label} checked="checked" {/if} />
            </span>
        </div>
    </div>
    <div class="form-group row">
        <div class="alert alert-warning col-xs-12" style="float: left">
            {t}product.alert.label.product_in_listing{/t}
            {if !is_null($parameter->next_update) && $parameter->next_update->isPast() == false}<hr /> {t}product.alert.label.product_in_listing.next_update{/t}  {$parameter->next_update->tz(site('timezone'))->format('d.m.Y H:i')} {/if}
        </div>
    </div>
</form>
{capture append="js"}
<script type="text/javascript">
    $('#in_listing_is_cheked').on('change', function(){
        CC.statusBox($('#show_label'), $(this).is(':checked'));
    }).trigger('change');
</script>
{/capture}
<style>
    .side-panel-title {
        display: none;
    }

</style>
