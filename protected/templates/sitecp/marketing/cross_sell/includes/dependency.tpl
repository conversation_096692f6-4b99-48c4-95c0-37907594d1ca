<script type="text/template" id="js-template-operation-target">
    {include file="./target.tpl"}
</script>
<script type="text/template" id="js-template-operation-action">
    {include file="./action.tpl"}
</script>
<script type="text/template" id="js-template-group-operation-target">
    {include file="./_group.tpl" type="target"}
</script>
<script type="text/template" id="js-template-group-operation-action">
    {include file="./_group.tpl" type="action"}
</script>
{if $type|default == 'action'}
    {$type = 'product'}
{/if}
<script type="text/template" id="js-template-type-select-action-popup">
    <a class="js-editable js-type-select-action js-error-cross-sell-actions-{$group|default:'@GROUP@'}-{$row|default:'@ROW@'}-type"
       data-name="cross_sell[actions][{$group|default:'@GROUP@'}][{$row|default:'@ROW@'}][type]" data-type="select2"
       data-select2-no-input="true" data-value="{$type|default:'product'}"
       data-source="{json_encode([['id' => 'product','text'=>__('global.filters.action.product')],['id'=>'category','text'=>__('global.filters.action.category')],['id' => 'vendor','text'=>__('global.filters.action.vendor')],['id'=>'selection','text'=>__('global.filters.action.selection')]])}">{t}global.filters.action.{$type|default:'product'}{/t}</a>
</script>
<script type="text/template" id="js-template-type-select-action-add_to_cart">
    <a class="js-editable js-type-select-action js-error-cross-sell-actions-{$group|default:'@GROUP@'}-{$row|default:'@ROW@'}-type"
       data-name="cross_sell[actions][{$group|default:'@GROUP@'}][{$row|default:'@ROW@'}][type]" data-type="select2"
       data-select2-no-input="true" data-value="{$type|default:'product'}"
       data-source="{json_encode([['id' => 'product','text'=>__('global.filters.action.product')]])}">{t}global.filters.action.{$type|default:'product'}{/t}</a>
</script>

<script type="text/template" id="js-template-type-select-event-shipping">
    <a class="js-editable js-type-select-target js-error-cross-sell-targets-{$group|default:'@GROUP@'}-{$row|default:'@ROW@'}-type"
       data-name="cross_sell[targets][{$group|default:'@GROUP@'}][{$row|default:'@ROW@'}][type]" data-type="select2"
       data-select2-no-input="true" data-value="{$type|default:'product'}"
       data-source="{json_encode([['id' => 'product','text'=>__('global.filters.target.product')],['id'=>'category','text'=>__('global.filters.target.category')],['id' => 'vendor','text'=>__('global.filters.target.vendor')],['id'=>'selection','text'=>__('global.filters.target.selection')],['id'=>'cart','text'=>__('global.filters.target.cart')],['id'=>'shipping','text'=>__('global.filters.target.shipping')]])}">{t}global.filters.target.{$type|default:'product'}{/t}</a>
</script>
<script type="text/template" id="js-template-type-select-event-payment">
    <a class="js-editable js-type-select-target js-error-cross-sell-targets-{$group|default:'@GROUP@'}-{$row|default:'@ROW@'}-type"
       data-name="cross_sell[targets][{$group|default:'@GROUP@'}][{$row|default:'@ROW@'}][type]" data-type="select2"
       data-select2-no-input="true" data-value="{$type|default:'product'}"
       data-source="{json_encode([['id' => 'product','text'=>__('global.filters.target.product')],['id'=>'category','text'=>__('global.filters.target.category')],['id' => 'vendor','text'=>__('global.filters.target.vendor')],['id'=>'selection','text'=>__('global.filters.target.selection')],['id'=>'cart','text'=>__('global.filters.target.cart')],['id'=>'payment','text'=>__('global.filters.target.payment')]])}">{t}global.filters.target.{$type|default:'product'}{/t}</a>
</script>
<script type="text/template" id="js-template-type-select-event-short">
    <a class="js-editable js-type-select-target js-error-cross-sell-targets-{$group|default:'@GROUP@'}-{$row|default:'@ROW@'}-type"
       data-name="cross_sell[targets][{$group|default:'@GROUP@'}][{$row|default:'@ROW@'}][type]" data-type="select2"
       data-select2-no-input="true" data-value="{$type|default:'product'}"
       data-source="{json_encode([['id' => 'product','text'=>__('global.filters.target.product')],['id'=>'category','text'=>__('global.filters.target.category')],['id' => 'vendor','text'=>__('global.filters.target.vendor')],['id'=>'selection','text'=>__('global.filters.target.selection')],['id'=>'cart','text'=>__('global.filters.target.cart')]])}">{t}global.filters.target.{$type|default:'product'}{/t}</a>
</script>

{capture append="js"}
    {if in_array(activeRoute(), ['admin.cross_sell.add', 'admin.cross_sell.edit'])}
        <link rel="stylesheet" type="text/css"
              href="{$img_url}sitecp/plugins/bootstrap-colorpicker/css/bootstrap-colorpicker.css?{app('last_build')}">
        <script src="{$img_url}sitecp/plugins/bootstrap-colorpicker/js/bootstrap-colorpicker.min.js?{app('last_build')}"></script>
    {/if}
    <script type="text/javascript">
        var CrossSellActions = new (Class({
            form: null,
            __construct: function () {
                this.form = $('#submit_cross_sell_form');
                this.init();
                this.formEvents();
            },
            init: function () {
                var $display = this.form.find('[name="cross_sell[display_type]"]'),
                    self = this;

                {if in_array($cross_sell->event, ['checkout_select_payment', 'checkout_select_shipping']) || $parent}
                $display.find('option:not([value="popup"])').attr('disabled', true);
                {else}
                $display.find('option').removeAttr('disabled');
                {/if}

                CCHelper.disableEnableBox(this.form.find('.js-product-settings'), {if $cross_sell->display_type == 'add_to_cart'}0{else}1{/if});
                CCHelper.disableEnableBox(this.form.find('.js-product-settings-reverse'), {if $cross_sell->display_type == 'add_to_cart'}1{else}0{/if});

                this.attachTypeTargetEvent(this.form.find('.js-type-input-target'));

                setTimeout(function () {
                    self.form.find('.js-type-input-target:not(.hidden)').map(function () {
                        $(this).trigger('initpage');
                    });
                }, 100);

                this.initOperations();
                this.addActionGroupsByNumber();

                {if $cross_sell->discount_type|default:'percent' == 'free_product'}
                CCHelper.disableEnableBox(this.form.find('.js-discount-percent-holder'), 0);
                {/if}
            },
            attachTypeTargetEvent: function (target) {
                $(target).off('save initpage').on('save initpage', function (e, params) {
                    var $el = $(this),
                        $holder = $el.closest('.js-operation'),
                        type = $holder.find('.js-type-select-action').data('value'),
                        $plus = $holder.find('.js-action-plus-global'),
                        total_next = $holder.nextAll('.js-operation').length,
                        value = e.type == 'initpage' ? $el.data('value') : params.submitValue,
                        suffix = $holder.closest('.js-operations-holder-action').length ? 'action' : 'target',
                        display_type = $('[name="cross_sell[display_type]"]').val();

                    if (suffix == 'action' && display_type == 'add_to_cart') {
                        $plus.addClass('hidden');
                    } else if (suffix == 'action' && type == 'product') {
                        $plus.addClass('hidden');
                    } else if (!total_next && value) {
                        $plus.removeClass('hidden');
                    } else {
                        $plus.addClass('hidden');
                    }
                });
            },
            replaceSelectType: function (suffix, $select_type) {
                var self = this;
                if (suffix == 'action') {
                    var display_type = $('[name="cross_sell[display_type]"]').val();

                    if (!display_type) {
                        return;
                    }

                    $($select_type).each(function () {
                        var $el = $(this),
                            $group = $el.closest('.js-operations-action'),
                            $row = $el.closest('.js-operation'),
                            group = $group.data('group') || 0,
                            row = $row.data('row'),
                            $templ = $($('#js-template-type-select-action-' + display_type).html().replace(/\@ROW\@/g, row).replace(/\@GROUP\@/g, group));

                        $el.replaceWith($templ);
                        self.attachTypeTargetEvent(self.form.find('.js-type-input-target'));
                    });
                } else {
                    var value = $('[name="cross_sell[event]"]').val(),
                        type = 'short';

                    if (value == 'checkout_select_shipping') {
                        type = 'shipping';
                    } else if (value == 'checkout_select_payment') {
                        type = 'payment';
                    }

                    if (!type) {
                        return;
                    }

                    var $tmpl = $('#js-template-type-select-event-' + type).html();

                    $($select_type).each(function () {
                        var $el = $(this),
                            row = $el.closest('.js-operation').data('row'),
                            group = $el.closest('.js-operations-target').data('group') || 0,
                            $templ = $($tmpl.replace(/\@ROW\@/g, row).replace(/\@GROUP\@/g, group));

                        $el.replaceWith($templ);
                        self.attachTypeTargetEvent(self.form.find('.js-type-input-target'));
                    });
                }
            },
            plusMinusActions: function (suffix) {
                if (suffix == 'action' && $('[name="cross_sell[display_type]"]').val() == 'add_to_cart') {
                    return $('.js-action-plus-action, .js-action-minus-action').addClass('hidden');
                }
                $('.js-operations-' + suffix).each(function (h) {
                    var $holder = $(this),
                        $plus = $('.js-action-plus-' + suffix, this).addClass('hidden').filter(':last').removeClass('hidden'),
                        $operation = $plus.closest('.js-operation'),
                        $target = $operation.find('.js-type-input-target:visible');

                    var $operations = $holder.find('.js-operation');
                    if ($operations.length <= 1 && (suffix == 'action' ? true : $('.js-operations-' + suffix).length == 1)) {
                        $operations.find('.js-action-minus-' + suffix).addClass('hidden');
                    }
                    $operations.each(function (i) {
                        $('.js-operations-where, .js-operations-' + suffix + '-operation, .js-operations-operation', this).addClass('hidden');
                        if (h === 0) {
                            $(i === 0 ? '.js-operations-where' : '.js-operations-' + suffix + '-operation', this).removeClass('hidden');
                        } else {
                            $(i === 0 ? '.js-operations-where, .js-operations-operation' : '.js-operations-' + suffix + '-operation', this).removeClass('hidden');
                        }
                    });
                    if ($target.length && $target.editable.lenght && $target.editable('getValue', true)) {
                        $plus.removeClass('hidden');
                    } else {
                        $plus.addClass('hidden');
                    }
                });

                $('.js-' + suffix + '-group-minus').removeClass('hidden').filter(':first').addClass('hidden');
            },
            showHideAddButton: function (event) {
                var $visibleOperations = $('.js-operations-holder-target .js-operations-target'),
                    treshold = event === 'minus' ? 1 : 2;

                if ($visibleOperations.length >= 3 || $visibleOperations.length < treshold) {
                    $('.js-add-group-of-conditions-target').addClass('hidden');
                } else {
                    $('.js-add-group-of-conditions-target').removeClass('hidden');
                }
            },
            initOperations: function () {
                this.operationEvents('target');
                this.operationEvents('action');
            },
            addGroupOfConditions: function (suffix, group) {
                var $holder = $('.js-operations-holder-' + suffix),
                    row = 1,
                    template = $('#js-template-group-operation-' + suffix).html()
                        .replace(/\@GROUP\@/g, group).replace(/\@ROW\@/g, row),
                    $template = $(template);

                $holder.append($template);
                this.replaceSelectType(suffix, $template.find('.js-type-select-' + suffix));
                App.init($template);
                $template.find('.js-type-select-' + suffix).trigger('change');
                this.plusMinusActions(suffix);
                if (suffix == 'target') {
                    this.showHideAddButton('plus');
                }
            },
            addRowOfCondition: function ($element, suffix, group, row) {
                var $holder = $element.is('.js-operations-' + suffix) ? $element : $element.closest('.js-operations-' + suffix),
                    template = $('#js-template-operation-' + suffix).html()
                        .replace(/\@GROUP\@/g, group).replace(/\@ROW\@/g, row),
                    $template = $(template);

                if (suffix == 'action') {
                    $holder.append($template);
                } else {
                    $holder.find('.js-' + suffix + '-group-minus').before($template);
                }

                this.replaceSelectType(suffix, $template.find('.js-type-select-' + suffix));
                App.init($template);
                $template.find('.js-type-select-' + suffix).trigger('change');
                this.plusMinusActions(suffix);
            },
            operationEvents: function (suffix) {
                var self = this;
                //type operation change
                this.form.on('click', '.js-action-minus-' + suffix, function () {
                    var $element = $(this),
                        $holder = $element.closest('.js-operations-' + suffix);

                    $element.closest('.js-operation').remove();
                    if ($holder.find('.js-operation').length < 1) {
                        if (suffix == 'action') {
                            self.addRowOfCondition($holder, suffix, $holder.data('group'), 0);
                        } else {
                            $holder.remove();
                        }
                    }
                    self.plusMinusActions(suffix);
                    if (suffix == 'target') {
                        self.showHideAddButton('minus');
                    }
                }).on('click', '.js-' + suffix + '-group-minus', function () {
                    var $element = $(this),
                        $holder = $element.closest('.js-operations-' + suffix);

                    $holder.remove();
                    self.plusMinusActions(suffix);
                    if (suffix == 'target') {
                        self.showHideAddButton('minus');
                    }
                }).on('click', '.js-action-plus-' + suffix, function () {
                    var $element = $(this),
                        $holder = $element.closest('.js-operations-' + suffix),
                        group = $holder.data('group') || 0,
                        row = (parseInt($holder.find('.js-operation:last').data('row')) || 0) + 1;

                    self.addRowOfCondition($holder, suffix, group, row);
                }).on('change', '.js-type-select-' + suffix, function () {
                    var $element = $(this),
                        $holder = $element.closest('.js-operation'),
                        $plus = $holder.find('.js-action-plus-global'),
                        value = $element.data('value'),
                        $types = $holder.find('.js-type-input').attr('data-disabled', 'true'),
                        $selected = $types.filter('.js-type-input.js-type-' + value).removeAttr('data-disabled'),
                        $comparison = $holder.find('.js-cross_sell-' + suffix + '-comparison'),
                        $comparison_cart = $holder.find('.js-cross_sell-' + suffix + '-comparison-price');

                    if (value) {
                        $element.data('disabled', false);
                    }

                    $selected.each(function () {
                        var $element_selected = $(this);
                        if ($element_selected.data('disabled')) {
                            $selected.editable('enable');
                        }
                    });

                    $plus.addClass('hidden');

                    CCHelper.disableEnableBox($types, 0);
                    CCHelper.disableEnableBox($selected, 1);

                    if ($.inArray(value, ['cart', 'price']) > -1) {
                        $comparison.addClass('hidden');
                        $comparison_cart.removeClass('hidden');
                    } else {
                        $comparison.removeClass('hidden');
                        $comparison_cart.addClass('hidden');
                    }

                }).find('.js-operation .js-type-select-' + suffix).trigger('change');

                this.plusMinusActions(suffix);

                if (suffix == 'action') {
                    this.form.on('change input keyup paste', '[name="cross_sell[products_limit]"]', function () {
                        self.addActionGroupsByNumber();
                    });
                } else {
                    this.form.on('click', '.js-add-group-of-conditions-' + suffix, function () {
                        var $holder = $('.js-operations-holder-' + suffix),
                            group = ($holder.find('.js-operations-' + suffix + ':last').data('group') || 0) + 1;

                        self.addGroupOfConditions(suffix, group);
                    });
                }
            },
            addActionGroupsByNumber: function () {
                var $el = $('[name="cross_sell[products_limit]"]'),
                    value = $el.val(),
                    $exists = $('.js-operations-action');

                for (var limit = 0; limit < Math.max(value, 1); limit++) {
                    if (!$exists.filter('[data-group="' + limit + '"]').length) {
                        this.addGroupOfConditions('action', limit);
                    }
                }
                $exists.map(function (group) {
                    if (group >= limit) {
                        $(this).remove();
                    }
                });
            },
            formEvents: function () {
                var self = this;
                this.form.on('change', '[name="cross_sell[event]"]', function () {
                    var $select = $(this),
                        $holder = $select.closest('.js-cross-sale-conditions'),
                        $select_type = $('.js-type-select-target'),
                        $display = $('[name="cross_sell[display_type]"]'),
                        $target = $holder.find('.js-editable-place'),
                        value = $select.val();

                    {if $enable_targets_edit}
                    $holder.find('.js-operations-action, .js-operations-target').remove();
                    $holder.find('.js-add-group-of-conditions-target')
                        .trigger('click').removeClass('hidden');

                    self.addActionGroupsByNumber();

                    self.replaceSelectType('target', $select_type);
                    {/if}

                    {if !$parent}
                    if ($.inArray($select.val(), ['checkout_select_payment', 'checkout_select_shipping']) > -1) {
                        if ($display.val() != 'popup') {
                            $display.val('popup');
                        }
                        $display.find('option').removeAttr('disabled');
                        $display.find('option[value="add_to_cart"]')
                            .removeAttr('selected').attr('disabled', true);
                    } else {
                        $display.find('option').removeAttr('disabled');
                    }
                    {/if}

                    App.initAjax($display.parent());
                    $display.trigger('change');

                    if (value) {
                        $target.removeClass('hidden');
                        if (!$holder.find('.js-operation').length) {
                            $holder.find('[class*=js-add-group-of-conditions]').click();
                        }
                    } else {
                        $target.addClass('hidden');
                    }

                }).on('change', '[name="cross_sell[display_type]"]', function () {
                    var $select = $(this),
                        $holder = $select.closest('.js-cross-sale-conditions'),
                        $select_type = $('.js-type-select-action'),
                        value = $select.val(),
                        $target = $holder.find('.js-editable-place');

                    $holder.find('.js-operations-action, .js-operations-target').remove();
                    $holder.find('.js-add-group-of-conditions-target')
                        .trigger('click').removeClass('hidden');

                    self.addActionGroupsByNumber();

                    self.replaceSelectType('action', $select_type);

                    App.initEditable($holder);

                    if (value == 'add_to_cart') {
                        CCHelper.disableEnableBox($('.js-product-settings'), 0);
                        CCHelper.disableEnableBox($('.js-product-settings-reverse'), 1);
                    } else {
                        CCHelper.disableEnableBox($('.js-product-settings'), 1);
                        CCHelper.disableEnableBox($('.js-product-settings-reverse'), 0);
                    }

                    if (value) {
                        $target.removeClass('hidden');

                        if (!$holder.find('.js-operation').length) {
                            $holder.find('[class*=js-add-group-of-conditions]').click();
                        }
                    } else {
                        $target.addClass('hidden');
                    }
                }).on('change', 'input[name="cross_sell[no_expire]"]', function (e) {
                    e.preventDefault();
                    var $el = $(this),
                        $active_to = self.form.find('input[name="cross_sell[active_to]"]'),
                        $timer = self.form.find('input[name="cross_sell[meta][timer]"]');
                    if ($el.prop('checked')) {
                        $active_to.prop('disabled', true);
                        $timer.prop('disabled', true).closest('.checker').addClass('disabled');
                    } else {
                        $active_to.prop('disabled', false);
                        $timer.prop('disabled', false).closest('.checker').removeClass('disabled');
                    }
                }).find('input[name="no_expire"]').trigger('change');

                this.form.on('change', 'select[name="cross_sell[discount_type]"]', function () {
                    var type_value_selected = $(this).val(),
                        $type_opts_holder = $('#discount_type_options'),
                        $input = $type_opts_holder.find('input');

                    if (type_value_selected == 'percent') {
                        CCHelper.disableEnableBox($('.js-discount-percent-holder'), 1);
                        CCHelper.disableEnableBox($('.js-discount-percent-holder').find('.col-xs-9'), 1);
                        CCHelper.disableEnableBox($('.js-discount-percent-holder').find('.col-xs-3'), 1);
                        $input.switchClass('mask-currency', 'mask-percent');
                        App.initAjaxInputMasks($type_opts_holder);
                    } else if (type_value_selected == 'fixed') {
                        CCHelper.disableEnableBox($('.js-discount-percent-holder'), 1);
                        CCHelper.disableEnableBox($('.js-discount-percent-holder').find('.col-xs-9'), 1);
                        CCHelper.disableEnableBox($('.js-discount-percent-holder').find('.col-xs-3'), 1);
                        $input.switchClass('mask-percent', 'mask-currency');
                        App.initAjaxInputMasks($type_opts_holder);
                    } else {
                        CCHelper.disableEnableBox($('.js-discount-percent-holder'), 0);
                    }
                    $type_opts_holder.find('span').addClass('hidden');
                    $type_opts_holder.find('#discount_type_' + type_value_selected).removeClass('hidden');
                }).find('select[name="cross_sell[discount_type]"]').trigger('change');

                //type
                this.form.on('change', '[name="cross_sell[type]"]', function () {
                    var $el = $(this),
                        value = $el.val();
                    CCHelper.disableEnableBox($('.js-cross_sell-type-change'), 0);
                    CCHelper.disableEnableBox($('.js-cross_sell-type-change-' + value), 1);                
                }).find('[name="cross_sell[type]"]').trigger('change');
                
                //type operation change
                this.form.on('submit', function (e) {
                    e.preventDefault();

                    self.form.find('.js-hidden-operations').remove();
                    self.form.find('.js-operation .js-editable:visible').each(function () {
                        var $element = $(this),
                            name = $element.data('name'),
                            value = $element.data('value');
                        //if (!($element.data('disabled') || false)) {
                        self.form.append($('<input type="hidden" class="js-hidden-operations">').attr('name', name).val(value));
                        //}
                    });

                    tinymceSubmit();
                    Custom.submitFormAjax(self.form.attr('id'), self.form.attr('target'), self.form.find('.errorBox'), self.form.find('.successBox'));
                    return false;
                });

                this.form.on('change', '.js-type-input-target', function () {
                    var $container = $(this).closest('.js-cross-sale-conditions');
                    var $target = $container.find('[class*=js-add-group-of-conditions]');

                    $target.removeClass('hidden');
                });

            }
        }));

    </script>
{/capture}