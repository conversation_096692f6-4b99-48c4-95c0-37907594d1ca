{$isCardType = $discount->type|default:$type == 'code-pro'}
<div class="box js-box-target">
    <div class="box-title">
        <div class="box-title-text">
            <h5>{t}discount.label.target{/t}</h5>
            <p>{t}discount.help.target{/t}</p>
        </div>
    </div>

    <div class="box-section">
        <div class="form-group-dynamic">

            <div class="row form-group">
                <div class="col-xs-12">
                    <select name="settings" class="form-control select2me js-target-settings" data-placeholder="{t}global.select{/t}" data-no-input="true">
                        <option></option>
                        {if !in_array($discount->type, ['label', 'banner'])}
                            <option value="all"{if $discount->all_targets} selected="selected"{/if}>{t}discount.target.all_orders{/t}</option>
                            <option value="order_over"{if $discount->order_over_targets} selected="selected"{/if}>{t}discount.target.order_over{/t}</option>
                        {/if}
                        <option value="product"{if $discount->product_targets} selected="selected"{/if}>{t}discount.target.product{/t}</option>
                        <option value="product_category"{if $discount->category_targets && !$discount->vendor_targets} selected="selected"{/if}>{t}discount.target.product_category{/t}</option>
                        <option value="product_vendor"{if !$discount->category_targets && $discount->vendor_targets} selected="selected"{/if}>{t}discount.target.product_vendor{/t}</option>
                        <option value="selection"{if $discount->selection_targets} selected="selected"{/if}>{t}discount.target.selection{/t}</option>
                        <option value="category_vendor"{if $discount->category_targets && $discount->vendor_targets} selected="selected"{/if}>{t}discount.target.category_vendor{/t}</option>
                    </select>
                </div>
            </div>

            <div class="row form-group target_item js-target-order_over{if empty($discount->order_over)} hidden{/if}">
                <div class="col-xs-12">
                    <div class="input-group">
                        <span class="input-group-addon input-group-addon-left input-group-addon-left gray">{t}discount.addon.over_over_amount{/t}</span>
                        <input name="order_over" type="text" class="form-control"{if !empty($discount->order_over)} value="{$discount->order_over_formatted_input}"{else} disabled="disabled"{/if} placeholder="{t}discount.ph.over_over_amount{/t}"/>
                    </div>
                </div>
            </div>

            {if !in_array($discount->type, ['label', 'banner'])}
                <div class="box-title js-target-force-save hidden" style="margin-top: -20px;border: 0">
                    <div class="box-title-text padding-left-0">
                        <label class="control-label">{t}discount.label.force_save{/t}</label>
                        <p>{t}discount.help.force_save{/t}</p>
                    </div>
                    <div class="box-title-addon padding-right-0">
                        <input name="force_save" type="checkbox" class="switch" value="1" {if $discount->force_save} checked="checked"{/if} />
                    </div>
                </div>
            {/if}

            <div class="row form-group target_item js-target-product{if !$discount->product_targets} hidden{/if}">
                <div class="col-xs-12">
                    <input name="products"{if $discount->product_targets} value="{$discount->product_targets}"{else} disabled="disabled"{/if}
                           type="hidden" class="form-control select2 select2_ajax"
                           data-url="{route('admin.autocomplete.products')}"
                           data-multiple="true"
                           data-autowidth="false"
                           data-placeholder="{t}discount.ph.select_product{/t}"
                           data-dropdownclass="select2-modal"/>
                </div>
            </div>

            <div class="row form-group target_item js-target-product_category js-target-category_vendor{if !$discount->category_targets} hidden{/if}">
                <div class="col-xs-12">
                    <div class="alert alert-warning margin-bottom-10" style="text-align: left">{t}discount.warning.target.category{/t}</div>
                    {if $discount->category_targets}
                        {$categoryIds = $discount->category_targets->pluck('id')->all()}
                    {/if}
                    {include file="categories/tree_select.tpl" multiple=true select_name="product_categories" category_id=$categoryIds|default}
                </div>
            </div>

            <div class="row form-group target_item js-target-selection{if !$discount->selection_targets} hidden{/if}">
                <div class="col-xs-12">
                    <input name="selections"{if $discount->selection_targets} value="{$discount->selection_targets}"{else} disabled="disabled"{/if}
                           type="hidden"
                           class="form-control select2 select2_ajax"
                           data-url="{route('admin.autocomplete.selection')}"
                           data-multiple="true"
                           data-autowidth="false"
                           data-placeholder="{t}discount.ph.select_selection{/t}"
                           data-dropdownclass="select2-modal"/>
                </div>
            </div>

            <div class="row form-group target_item js-target-product_vendor js-target-category_vendor{if !$discount->vendor_targets} hidden{/if}">
                <div class="col-xs-12">
                    <input name="vendors"{if $discount->vendor_targets} value="{$discount->vendor_targets}"{else} disabled="disabled"{/if}
                           type="hidden"
                           class="form-control select2 select2_ajax"
                           data-url="{route('admin.autocomplete.vendor')}"
                           data-multiple="true"
                           data-autowidth="false"
                           data-placeholder="{t}discount.ph.select_vendor{/t}"
                           data-dropdownclass="select2-modal"/>
                </div>
            </div>
        </div>
    </div>
</div>

{capture append="js"}
    <script type="text/javascript">
        (function() {
            let holder = $('.js-box-target'),
                boxCode = holder.parent().find('.js-box-code-holder');

            function forceSaveDisplay(setting, discountType) {
                var force_save = 0;
                if(['flat', 'percent'].indexOf(discountType) > -1) {
                    force_save = setting === 'order_over';
                } else if(discountType === 'shipping') {
                    force_save = true;
                }

                CC.statusBox(holder.find('.js-target-force-save'), force_save);
            }

            function manipulateApplyRegularPrice(value) {
                let code_apply = boxCode.find('[name="code_apply"]').is(':checked');
                if(value === 'order_over') {
                    CC.statusBox(boxCode.find('.js-apply-regular-price'), 0)
                } else {
                    CC.statusBox(boxCode.find('.js-apply-regular-price'), code_apply)
                }
            }

            $(function() {
                manipulateApplyRegularPrice(holder.find('select.js-target-settings').val())
            });

            holder.on('change', '.js-target-settings', function (e) {
                var that = $(this),
                    el_value = that.val();

                CC.statusBox(holder.find('.target_item:not(.js-target-' + el_value + ')'), 0);
                let inputHolder = holder.find('.js-target-' + el_value);
                CC.statusBox(inputHolder, 1);

                if(['product', 'product_category', 'selection', 'product_vendor', 'category_vendor'].indexOf(el_value) > -1) {
                    inputHolder.find(':input:last').select2('enable', true).select2('open');
                } else {
                    inputHolder.find(':input').focus();
                }

                manipulateApplyRegularPrice(el_value);

                forceSaveDisplay(el_value, $('.js-discount-box-type select.js-discount-type-select').val())
            });

            $('.js-discount-box-type').on('change', 'select.js-discount-type-select', function() {
                forceSaveDisplay(holder.find('select.js-target-settings').val(), $(this).val())
            })

            forceSaveDisplay(holder.find('select.js-target-settings').val(), $('.js-discount-box-type select.js-discount-type-select').val())
        })();
    </script>
{/capture}