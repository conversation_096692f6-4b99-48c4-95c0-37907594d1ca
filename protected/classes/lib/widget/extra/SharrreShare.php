<?php

declare(strict_types=1);

namespace lib\widget\extra;

use Illuminate\Support\Arr;
use lib\widget\WidgetAbstract;

class SharrreShare extends WidgetAbstract
{
    protected static $_restrictions = [
        'id' => 'char:2,50',
        'url' => 'url',
        'className' => 'char:2,50',
        'title' => 'char:2,250',
        'shorterTotal' => 'in:yes,no',
        'enableHover' => 'in:yes,no',
        'enableTracking' => 'in:yes,no',
        'enableCounter' => 'in:yes,no',
        'template' => false,
        'render' => false
    ];

    protected static $_default_settings = [
        'id' => '',
        'url' => '',
        'className' => '',
        'title' => 'Share',
        'shorterTotal' => 'yes',
        'enableHover' => 'no',
        'enableTracking' => 'no',
        'enableCounter' => 'yes',
        'template' => '<div class="box"><div class="left">Share</div><div class="middle"><a href="#" class="facebook">f</a><a href="#" class="twitter">t</a><a href="#" class="googleplus">+1</a></div><div class="right">{total}</div></div>',
        'render' => "function(api, options){
          \$(api.element).on('click', '.twitter', function() {
            api.openPopup('twitter');
          });
          \$(api.element).on('click', '.facebook', function() {
            api.openPopup('facebook');
          });
          \$(api.element).on('click', '.googleplus', function() {
            api.openPopup('googlePlus');
          });
        }",
        'buttons' => '',
        'services' => ["googlePlus", "facebook", "twitter"]
    ];

    protected static $_id = '';

    private $_config = '';

    private $_url_curl = '';

    private array $_services = [
        "googlePlus" => true,
        "facebook" => true,
        "twitter" => true,
        "digg" => true,
        "delicious" => true,
        "stumbleupon" => true,
        "linkedin" => true,
        "pinterest" => true
    ];

    public function init(): void
    {

        if (!empty($this->_settings['id'])) {
            self::setID($this->_settings['id']);
        }

        $config = [];

        if (!empty($this->_settings['className'])) {
            $config['className'] = $this->_settings['className'];
        }

        if (!empty($this->getServices())) {
            $config['share'] = $this->getServices();
        }

        if (!empty($this->_settings['template'])) {
            $config['template'] = $this->_settings['template'];
        }

        if (!empty($this->_settings['title'])) {
            $config['title'] = $this->_settings['title'];
        }

        $this->_url_curl = \Linker::widget($this->getWidgetName(), 'curl');

        $config['url'] = $this->getAbsoluteLink();
        $config['urlCurl'] = $this->_url_curl;
        $config['shorterTotal'] = (($this->_settings['shorterTotal'] == 'yes') ? true : false);
        $config['enableHover'] = (($this->_settings['enableHover'] == 'yes') ? true : false);
        $config['enableTracking'] = (($this->_settings['enableTracking'] == 'yes') ? true : false);
        $config['enableCounter'] = (($this->_settings['enableCounter'] == 'yes') ? true : false);

        if (!empty($this->_settings['render'])) {
            $config['render'] = $this->_settings['render'];
        }

        if (!empty($this->_settings['buttons'])) {
            $config['buttons'] = $this->_settings['buttons'];
        }


        $this->_config = json_encode($config, JSON_NUMERIC_CHECK);

    }

    public function getDependency()
    {

    }

    public function getConfigJS()
    {
        return $this->_config;
    }

    public function getServices()
    {

        return Arr::only($this->_services, $this->_settings['services']);

    }

    public function getID()
    {
        if (empty(self::$_id)) {
            self::$_id = uniqid('shareme_');
        }

        return self::$_id;
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public static function setID($id): void
    {
        self::$_id = $id;
    }

    public function getAbsoluteLink()
    {

        if (empty($this->_settings['url'])) {
            return site()->getSiteUrl('primary') . request()->segment(1);
        }

        return $this->_settings['url'];

    }

    public function actionCurl(): void
    {

        header('content-type: application/json');

        $json = ['url' => '', 'count' => 0];
        $json['url'] = $_GET['url'];
        $url = urlencode((string) $_GET['url']);
        $type = urlencode((string) $_GET['type']);

        if (filter_var($_GET['url'], FILTER_VALIDATE_URL)) {
            if ($type == 'googlePlus') {  //source http://www.helmutgranda.com/2011/11/01/get-a-url-google-count-via-php/

                $contents = parse('https://plusone.google.com/u/0/_/+1/fastbutton?url=' . $url . '&count=true');

                preg_match('/window\.__SSR = {c: ([\d]+)/', $contents, $matches);

                if (isset($matches[0])) {
                    $json['count'] = (int)str_replace('window.__SSR = {c: ', '', $matches[0]);
                }

            } else {
                if ($type == 'stumbleupon') {

                    $content = parse('https://www.stumbleupon.com/services/1.01/badge.getinfo?url=' . $url);

                    $result = json_decode($content);
                    if (isset($result->result->views)) {
                        $json['count'] = $result->result->views;
                    }

                }
            }
        }

        echo str_replace('\\/', '/', json_encode($json));
        die();

        function parse($encUrl): bool|string
        {
            $options = [
                CURLOPT_RETURNTRANSFER => true, // return web page
                CURLOPT_HEADER => false,        // don't return headers
                CURLOPT_FOLLOWLOCATION => true, // follow redirects
                CURLOPT_ENCODING => "",         // handle all encodings
                CURLOPT_USERAGENT => 'sharrre', // who am i
                CURLOPT_AUTOREFERER => true,    // set referer on redirect
                CURLOPT_CONNECTTIMEOUT => 5,    // timeout on connect
                CURLOPT_TIMEOUT => 10,          // timeout on response
                CURLOPT_MAXREDIRS => 3,         // stop after 10 redirects
                CURLOPT_SSL_VERIFYHOST => 0,
                CURLOPT_SSL_VERIFYPEER => false,
            ];
            $ch = curl_init();

            $options[CURLOPT_URL] = $encUrl;
            curl_setopt_array($ch, $options);

            $content = curl_exec($ch);
            $err = curl_errno($ch);
            $errmsg = curl_error($ch);

            curl_close($ch);

            if ($errmsg != '' || $err != 0) {
                /*print_r($errmsg);
                print_r($errmsg);*/
            }

            return $content;
        }

    }

}
