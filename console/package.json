{"private": true, "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build"}, "devDependencies": {"@vitejs/plugin-vue2": "^2.3.3", "ajv": "^6.12.6", "axios": "1.7.8", "babel-loader": "^8.2.2", "cross-env": "^5.2.1", "css-loader": "^2.1.1", "jwt-decode": "^2.2.0", "laravel-vite-plugin": "^1.2.0", "lodash": "^4.17.21", "moment": "^2.29.4", "nprogress": "^0.2.0", "sortablejs": "^1.14.0", "stylus": "^0.60.0", "stylus-loader": "^3.0.2", "unplugin-auto-import": "^19.1.1", "vite": "^6.0.11", "vite-plugin-commonjs": "^0.10.4", "vue": "^2.6.14", "vue-highlightjs": "^1.3.3", "vue-loader": "^15.9.8", "vue-mc": "^0.5.0", "vue-router": "^3.5.2", "vue-template-compiler": "^2.6.14", "vuetify": "^1.5.24", "vuex": "^3.6.2"}, "dependencies": {"@vueuse/core": "^11.2.0", "ansi_up": "5.0.0", "axios-auth-refresh": "^3.2.2", "chart.js": "^2.9.4", "dotenv": "^16.4.7", "tiny-emitter": "^2.1.0", "tiptap-vuetify": "^1.7.0", "vue-chartjs": "^3.5.1", "vue-clip": "^1.0.0", "vue-codemirror": "^4.0.6", "vue-email-editor": "^0.8.0", "vue-i18n": "^8.14.1", "vue-json-viewer": "^2.2.22"}}