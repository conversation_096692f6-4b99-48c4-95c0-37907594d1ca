<template>
    <v-dialog
        v-model="dialog"
        scrollable
        max-width="600px"
        @keydown.esc="closeDialogOnEsc"
    >
        <v-card>
            <v-card-title>
                <span class="headline">{{ title }}</span>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="dialog = false">Close</v-btn>
            </v-card-title>
            <v-card-text>
                <v-container grid-list-md>
                    <v-layout wrap>
                        <v-flex xs12 v-highlightjs="content" :dark="$root.dark">
                            <code class="json"></code>
                        </v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import axios from "axios";

export default {
    name: "show-email-vars",
    data() {
        return {
            title: '',
            content: {},
            dialog: false,
        }
    },
    methods: {
        show(item) {
            this.dialog = true
            this.title = item.vars_type
            this.content = {}
            axios.get('/email-templates/var-type/' + item.vars_type)
                .then((response) => {
                    this.content = JSON.stringify(response.data.data, null, 2)
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    }
}
</script>
