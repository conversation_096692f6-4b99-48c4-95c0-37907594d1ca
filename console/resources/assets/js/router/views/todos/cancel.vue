<template>
    <v-dialog
        v-model="dialog"
        max-width="600px"
        persistent
        scrollable
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h3 class="headline white--text mb-0">{{ title }}</h3>
            </v-card-title>

            <v-card-text>
                <v-flex xs12>
                    <v-textarea
                        outline
                        v-model="item.cancel_reason"
                        label="Cancel Reason"
                        :error-messages="errors.cancel_reason"
                        ref="noteTextArea"
                        height="400"
                    ></v-textarea>
                </v-flex>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                    color="primary"
                    flat
                    @click.native="closeDialog"
                >Cancel
                </v-btn>
                <v-btn
                    color="primary"
                    :loading="loading"
                    :disabled="loading"
                    flat
                    @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>

    </v-dialog>
</template>

<script>
import axios from "axios";

export default {
    name: "todo-cancel",
    data() {
        return {
            title: 'Cancel Task',
            item: {},
            loading: false,
            dialog: false,
            errors: [],
        }
    },
    methods: {
        show(item) {
            this.item = item
            this.errors = []
            this.showDialog()
            setTimeout(() => {
                this.$refs.noteTextArea.focus()
            }, 100);
        },
        save() {
            this.loading = true
            axios.put(`/todos/${this.item.id}/cancel`, this.item)
                .then(response => {
                    this.loading = false
                    this.closeDialog()
                    this.$emit('todo:canceled', response.data.data)
                })
                .catch(error => {
                    this.handleError(error)
                    setTimeout(() => {
                        this.$refs.noteTextArea.focus()
                    }, 100);
                })
        },
    }
}
</script>

<style scoped>

</style>