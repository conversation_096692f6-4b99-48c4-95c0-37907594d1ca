<template>
    <v-card>
        <v-card>
            <v-card-title>
                <h1>Domains</h1>
                <v-btn
                        small
                        @click="$refs.dnsDialog.showCloudCartNet()"
                >
                    cloudcart.net
                </v-btn>
                <v-spacer></v-spacer>
                <v-text-field
                        append-icon="search"
                        label="Search"
                        single-line
                        v-model="search"
                        clearable
                ></v-text-field>
            </v-card-title>

            <domains-filter
                ref="filters"
                v-on:filters:change="() => this.filteredItems = this.$refs.filters.filterItems(this.items)"
            ></domains-filter>
        </v-card>

        <v-data-table
                :headers="headers"
                :items="filteredItems"
                :search="search"
                :pagination.sync="pagination"
                :loading="loading"
                :custom-filter="customFilter"
                class="elevation-1"
                must-sort
        >
            <template slot="items" slot-scope="props">
                <tr>
                    <td>{{ props.item.id }}<br>{{ props.item.created_at | formatDate }}</td>
                    <td nowrap>
                        <v-layout row>
                            <v-flex shrink>
                                <v-btn icon small class="mx-0"
                                       :href="'/sites/' + props.item.site_id + '/login?target=/settings/domains'"
                                       target="_blank">
                                    <v-icon color="primary">exit_to_app</v-icon>
                                </v-btn>
                            </v-flex>
                            <v-flex grow>
                                <router-link :to="{ name: 'sites.show', params: { id: props.item.site_id }}">
                                    <span class="subheading"><b>{{ props.item.host }}</b></span>
                                </router-link>
                                <a v-if="props.item.whois"
                                   @click="$refs.jsonDialog.viewData(props.item.whois, 'Whois')">
                                    <v-icon small>info</v-icon>
                                </a>
                                <br>
                                <span class="caption text-no-wrap">
                                    <a :href="'https://intodns.com/' + props.item.host" target="_blank">intoDNS</a> |
                                    <a :href="'https://dnschecker.org/#A/' + props.item.host"
                                       target="_blank">DnsChecker</a>
                                </span>
                            </v-flex>
                        </v-layout>
                    </td>
                    <td>
                        <v-btn v-if="props.item.cloudflare_zone_id" icon class="mx-0"
                               @click="$refs.dnsDialog.show(props.item)">
                            <v-img
                                    max-width="24px"
                                    src="/images/cloudflare-icon.ico"
                            ></v-img>
                        </v-btn>
                        <v-btn v-else icon class="mx-0">
                            <v-icon color="error">block</v-icon>
                        </v-btn>
                    </td>
                    <td>
                        <v-btn
                                v-if="props.item.cloudflare_hostname_id"
                                icon
                                class="mx-0"
                                @click="getMainHostDNS(props.item)"
                        >
                            <v-icon color="success">check_circle</v-icon>
                        </v-btn>
                        <v-btn
                                v-else
                                icon
                                class="mx-0"
                        >
                            <v-icon color="error">block</v-icon>
                        </v-btn>
                    </td>
                    <td>
                        <v-btn
                                v-if="props.item.ssl === 'yes'"
                                icon
                                class="mx-0"
                                @click="$refs.sslDialog.show(props.item)"
                        >
                            <v-icon color="success">check_circle</v-icon>
                        </v-btn>
                        <v-btn
                                v-else
                                icon
                                class="mx-0"
                        >
                            <v-icon color="error">block</v-icon>
                        </v-btn>
                    </td>
                    <td>
                        <v-btn v-if="props.item.cloudcart === 1" icon class="mx-0">
                            <v-icon color="success">check_circle</v-icon>
                        </v-btn>
                        <v-btn v-else icon class="mx-0">
                            <v-icon color="error">block</v-icon>
                        </v-btn>
                    </td>
                    <td>
                        <v-btn icon class="mx-0" @click="refresh(props.item)"
                               :loading="loading && editedIndex === items.indexOf(props.item)">
                            <v-icon color="orange">refresh</v-icon>
                        </v-btn>
                        {{ props.item.expire_date | formatDate }}
                    </td>
                    <td v-highlightjs="json(props.item.ns)" :class="$root.dark ? 'dark' : ''">
                        <code class="json"></code>
                    </td>
                    <td v-highlightjs="json(props.item.ips)" :class="$root.dark ? 'dark' : ''">
                        <code class="json"></code>
                    </td>
                </tr>
            </template>

            <v-alert slot="no-results" :value="true" color="error" icon="warning">
                Your search for "{{ search }}" found no results.
            </v-alert>

        </v-data-table>

        <json-dialog ref="jsonDialog" max-width="500px"></json-dialog>
        <show-ssl ref="sslDialog"></show-ssl>
        <show-dns ref="dnsDialog"></show-dns>
    </v-card>

</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import {flatten} from '@helpers'
import JsonDialog from "../../../components/JsonDialog";
import ShowSsl from "./ssl";
import ShowDns from "./dns";
import DomainsFilter from "./filter.vue";

export default {
    name: 'domains-list',
    components: {
        DomainsFilter,
        ShowDns,
        ShowSsl,
        JsonDialog,
        LayoutDefault,
    },
    data() {
        return {
            search: '',
            items: [],
            filteredItems: [],
            editedIndex: -1,
            loading: true,
            pagination: {
                rowsPerPage: 25,
                descending: true,
                sortBy: 'id',
            },
            headers: [
                {text: 'ID / Created At', value: 'id'},
                {text: 'Host', value: 'host'},
                {text: 'CF DNS', value: 'cloudflare_zone_id'},
                {text: 'Custom Host', value: 'cloudflare_hostname_id'},
                {text: 'SSL', value: 'ssl'},
                {text: 'NameCom', value: 'cloudcart'},
                {text: 'Expire', value: 'expire_date'},
                {text: 'NS', value: 'ns'},
                {text: 'IPs', value: 'ips'},
            ],
        }
    },
    mounted() {
        this.getItems()
    },
    watch: {
        items: {
            handler(newVal, oldVal) {
                this.filteredItems = this.$refs.filters.filterItems(newVal)
            },
            deep: true
        },
    },
    methods: {
        json(value) {
            return JSON.stringify(value)
        },
        getMainHostDNS(item) {
            axios.get('/domains/main-host/' + item.site_id)
                .then(response => {
                    let result = response.data.data
                    this.$refs.jsonDialog.viewData(result, result ? result.name : 'Data')
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getItems() {
            this.loading = true
            axios.get('/domains')
                .then(response => {
                    this.loading = false
                    this.items = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        refresh(item) {
            this.loading = true
            this.editedIndex = this.items.indexOf(item)
            axios.put('/domains/' + item.id, {})
                .then((response) => {
                    this.loading = false
                    Object.assign(this.items[this.editedIndex], {
                        ...response.data.data,
                        flatten: Object.values(flatten(response.data.data))
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        customFilter(items, search, filter) {
            search = search.toString().toLowerCase()
            if (search.trim() === '') return items

            return items.filter(item => item.flatten.some(val => filter(val, search)))
        },
    },
}
</script>
<style>
.hljs-string {
    white-space: nowrap;
}
</style>