<template>
    <v-layout row wrap v-if="item">
        <v-flex xs12 sm6>
            <v-card height="100%">
                <v-card-title primary-title>
                    <v-avatar size="50">
                        <img :src="item.favicon" alt="Avatar">
                    </v-avatar>
                    <v-flex pl-2>
                        <span v-if="isCloudCartEmployee" class="pull-right mt-2">
                            <a @click="$refs.jsonDialog.viewData(item, 'Site')">
                                <v-icon>info</v-icon>
                            </a>
                        </span>
                        <h1 class="title" v-if="isCloudCartEmployee">
                            <a :href="item.url" target="_blank">{{ item.router_site.primary_host.host }}</a>
                            <v-chip
                                v-if="item.stage_step"
                                small
                                :color="stepColor"
                                text-color="white"
                                @click="$refs.stageDetails.show(item)"
                            >
                                <span>{{ item.stage_step.name }}</span>
                                <span v-if="item.stage_percent">&nbsp;{{ item.stage_percent }}%</span>
                                <span v-if="item.stage_step_days">&nbsp;{{ item.stage_step_days }} days</span>
                            </v-chip>
                            <v-chip
                                v-else
                                small
                                disabled
                                color="success"
                                text-color="white"
                            > {{ item.store_type_name }}
                            </v-chip>
                            <v-chip
                                v-if="item.stage_status === 3"
                                small
                                disabled
                                color="success"
                                text-color="white"
                                @click="$refs.stageDetails.show(item)"
                            > Confirmed
                            </v-chip>
                            <v-chip
                                v-if="item.stage_status === 4"
                                small
                                disabled
                                color="error"
                                text-color="white"
                                @click="$refs.stageDetails.show(item)"
                            > Not Interested
                            </v-chip>
                            <br>
                            # {{ item.id }} <span class="grey--text caption">{{
                                showDB(item.router_site)
                            }} {{ item.router_site.main_host.host }}</span>
                            <v-btn v-if="item.router_site.primary_host_id !== item.router_site.main_host_id"
                                   flat small color="primary" @click.native="mainAsPrimary(item.id)">
                                Set As Primary
                            </v-btn>
                        </h1>
                        <h1 class="title" v-else>
                            <a :href="item.url" target="_blank">{{ item.primary_host }}</a>
                            <br>
                            # {{ item.id }}
                        </h1>
                    </v-flex>
                </v-card-title>
                <v-card-text>
                    <v-layout row>
                        <v-flex xs6>
                            <div v-if="isCloudCartEmployee && item.company">
                                Company:
                                <a @click="$refs.companyDetails.show(item.company)">
                                    <strong>{{ item.company.name }}</strong>
                                </a>
                            </div>
                            <div>Plan: <strong>{{ item.plan }} {{ item.period }}</strong></div>
                            <div>Status:
                                <strong>{{ status(item.status) }}
                                    <span v-if="item.status === 3">: {{ item.router_site.expire_reason }}</span>
                                </strong>
                            </div>
                            <div>
                                Next Billing Date: <strong>{{ item.next_billing_date | formatDate }}</strong>
                            </div>
                            <div>Created: <strong>{{ item.created_at | formatDateTime }}</strong></div>
                            <div v-if="item.reseller">Reseller: <strong>{{ item.reseller.name }}</strong></div>
                        </v-flex>
                        <v-flex xs6 class="caption">
                            <div>Referral: <strong>{{ item.referral }}</strong></div>
                            <div>Source: <strong>{{ item.utm_source }}</strong></div>
                            <div>Medium: <strong>{{ item.utm_medium }}</strong></div>
                            <div>Campaign: <strong>{{ item.utm_campaign }}</strong></div>
                            <div v-if="item.utm_content">Content: <strong>{{ item.utm_content }}</strong></div>
                            <div v-if="item.utm_term">Term: <strong>{{ item.utm_term }}</strong></div>
                            <div v-if="item.cc_user && isCloudCartEmployee">
                                CC User: <strong>{{ item.cc_user.name }}</strong>
                            </div>
                        </v-flex>
                    </v-layout>
                </v-card-text>
                <v-card-actions v-if="isCloudCartEmployee">
                    <v-flex xs12 v-if="item.router_site && item.router_site.progress !== 'completed'">
                        <v-alert :value="true" color="warning"
                                 icon="warning">
                            Site DB Not Found!
                            <v-btn style="float: right"
                                   @click.native="reinstall()">
                                Re-install
                            </v-btn>
                        </v-alert>
                    </v-flex>
                    <v-flex xs12 v-else>
                        <v-btn flat color="primary" :href="'/sites/' + item.id + '/login'" target="_blank">
                            Login
                        </v-btn>
                        <v-btn flat color="primary" @click.native="$refs.siteEditDialog.editItem(item)">
                            Edit
                        </v-btn>
                        <v-btn flat color="primary" ref="callBtn" @click="$refs.callDialog.call(item); tab = 3">
                            Call
                        </v-btn>
                        <v-btn flat color="primary" ref="meetBtn" @click="$refs.meetingDialog.forSite(item); tab = 2">
                            Meeting
                        </v-btn>
                    </v-flex>
                </v-card-actions>
            </v-card>
        </v-flex>
        <v-flex xs12 sm6>
            <v-card height="100%">
                <v-card-title primary-title>
                    <v-avatar size="50" color="pink">
                        <span class="white--text headline">{{ getUserAvatarLetters(item.user) }}</span>
                    </v-avatar>
                    <v-flex pl-2>
                        <span v-if="isCloudCartEmployee" class="pull-right mt-2">
                            <a @click="$refs.jsonDialog.viewData(item.user, 'Merchant')">
                                <v-icon>info</v-icon>
                            </a>
                        </span>
                        <h1 class="title" v-if="isCloudCartEmployee">
                            <a @click="$refs.merchantDetails.show(item.user)">
                                {{ item.user.name }}
                            </a>
                            <v-chip v-if="item.user.user_type" small disabled color="orange" text-color="white">
                                {{ item.user.user_type }}
                            </v-chip>
                            <br>
                            # {{ item.user.id }} <span v-if="item.user.braintree_id" class="grey--text caption">{{
                                item.user.braintree_id
                            }}</span>
                        </h1>
                        <h1 class="title" v-else>
                            <a @click="$refs.merchantDetails.show(item.user)">
                                {{ item.user.name }}
                            </a>
                            <br>
                            # {{ item.user.id }}
                        </h1>
                    </v-flex>
                </v-card-title>
                <v-card-text>
                    <v-layout row wrap>
                        <v-flex xs6>
                            <div>Email: <strong>{{ item.user.email }}</strong></div>
                            <div>Phone: <strong>{{ item.user.phone }}</strong></div>
                            <div>Locale:
                                <strong>
                                    {{ item.user.country_name }} / {{ item.user.lang_name }}
                                </strong>
                            </div>
                            <div>Created: <strong>{{ item.user.created_for_humans }}</strong></div>
                            <div v-if="item.user.reseller">Reseller: <strong>{{
                                    item.user.reseller.name
                                }}</strong></div>
                        </v-flex>
                        <v-flex xs6 class="caption">
                            <div>Referral: <strong>{{ item.user.referral }}</strong></div>
                            <div>Source: <strong>{{ item.user.utm_source }}</strong></div>
                            <div>Medium: <strong>{{ item.user.utm_medium }}</strong></div>
                            <div>Campaign: <strong>{{ item.user.utm_campaign }}</strong></div>
                            <div v-if="item.user.utm_content">
                                Content: <strong>{{ item.user.utm_content }}</strong>
                            </div>
                            <div v-if="item.user.utm_term">Term: <strong>{{ item.user.utm_term }}</strong></div>
                            <div v-if="item.user.cc_user && isCloudCartEmployee">
                                CC User: <strong>{{ item.user.cc_user.name }}</strong>
                            </div>
                        </v-flex>
                    </v-layout>
                </v-card-text>
                <v-card-actions v-if="isCloudCartEmployee">
                    <v-btn flat color="primary" :href="'/merchants/' + item.user.id + '/login'" target="_blank">
                        Login
                    </v-btn>
                    <v-btn flat color="primary" @click="$refs.editDialog.editItem(item.user)">
                        Edit
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-flex>
        <v-flex xs12>
            <v-tabs v-model="tab">
                <v-tab v-if="isCloudCartEmployee">
                    <span>Notes</span>
                    <span class="caption primary--text ml-1">{{ notes.length }}</span>
                </v-tab>
                <v-tab v-if="isCloudCartEmployee">
                    <span>Tasks</span>
                    <span class="caption primary--text ml-1">{{ todos.length }}</span>
                </v-tab>
                <v-tab v-if="isCloudCartEmployee">
                    <span>Meetings</span>
                    <span class="caption primary--text ml-1">{{ meetings.length }}</span>
                </v-tab>
                <v-tab v-if="isCloudCartEmployee">
                    <span>Calls</span>
                    <span class="caption primary--text ml-1">{{ calls.length }}</span>
                </v-tab>
                <v-tab>
                    <span>Subscriptions</span>
                    <span class="caption primary--text ml-1">{{ subscriptions.length }}</span>
                </v-tab>
                <v-tab v-if="isCloudCartEmployee">
                    <span>Transactions</span>
                    <span class="caption primary--text ml-1">{{ transactions.length }}</span>
                </v-tab>
                <v-tab v-if="isCloudCartEmployee">
                    <span>Invoices</span>
                    <span class="caption primary--text ml-1">{{ invoices.length }}</span>
                </v-tab>
                <v-tab v-if="isCloudCartEmployee || isResellerLevel(2)">
                    <span>Offers</span>
                    <span class="caption primary--text ml-1">{{ offers.length }}</span>
                </v-tab>
                <v-tab v-if="isCloudCartEmployee">
                    <span>Segments</span>
                    <span class="caption primary--text ml-1">{{ segments.length }}</span>
                </v-tab>
                <v-tab v-if="isCloudCartEmployee">
                    <span>Apps</span>
                    <span class="caption primary--text ml-1">{{ apps.length }}</span>
                </v-tab>
                <v-tab v-if="isCloudCartEmployee">
                    <span>Providers</span>
                    <span class="caption primary--text ml-1">{{ providers.length }}</span>
                </v-tab>
                <v-tab v-if="isCloudCartEmployee">Boarding</v-tab>
                <v-tab v-if="isCloudCartEmployee">Traffic</v-tab>
                <v-tab v-if="isCloudCartEmployee">Statistics</v-tab>
                <v-tab v-if="isCloudCartEmployee">Log</v-tab>
                <v-tab v-if="isCloudCartEmployee">Campaign Reputation</v-tab>

                <v-tabs-items :touchless="true">
                    <v-tab-item v-if="isCloudCartEmployee">
                        <note-list :loading="false" :items="notes" :site="item"/>
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <todo-list
                            :loading="false"
                            :items="todos"
                            v-on:call:create="(item) => this.$refs.callList.addItem(item)"
                            v-on:call:update="(item) => { this.$refs.callList.refreshItem(item); this.getNotes(); this.getTodos() }"
                            v-on:call:deleted="(item) => this.$refs.callList.removeItem(item)"
                        />
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <meeting-list
                            ref="meetingList"
                            :items="meetings"
                            :meet-btn="$refs.meetBtn"
                        />
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <call-list
                            ref="callList"
                            :items="calls"
                            :call-btn="$refs.callBtn"
                            v-on:call:follow-up="(item) => { this.getTodos(); this.getNotes() }"
                        />
                    </v-tab-item>
                    <v-tab-item>
                        <subscription-list
                            :items="subscriptions"
                            :site="item"
                            v-on:subscription:new="() => this.getTransactions() || this.getInvoices() || this.getEventsLog()"
                            v-on:subscription:renew="() => this.getTransactions() || this.getInvoices() || this.getEventsLog()"
                        ></subscription-list>
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <v-card>
                            <v-card-title>
                                <h1>Transactions</h1>
                            </v-card-title>
                        </v-card>
                        <transaction-list :items="transactions"></transaction-list>
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <invoices-list
                            :items="invoices"
                            :hideSite="true"
                            v-on:invoice:void="() => this.getTransactions() || this.getEventsLog()"
                            v-on:invoice:refund="() => this.getTransactions() || this.getEventsLog()"
                        ></invoices-list>
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee || isResellerLevel(2)">
                        <offers-list
                            :items="offers"
                            :site="item"
                        />
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <segments-list :loading="loading" :items="segments" :hide-controls="true"></segments-list>
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <installed-apps :items="apps" :site="item"/>
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <installed-providers :items="providers" :site="item"/>
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <boarding-answered
                            :loading="loading"
                            :items="boarding"
                            :site="item"
                            v-on:boarding:changed="() => {
                                this.getItem()
                                this.getBoarding()
                            }"
                        />
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <traffic :items="traffic" :site="item"/>
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <statistics :item="statistics"/>
                    </v-tab-item>
                    <v-tab-item v-if="isCloudCartEmployee">
                        <site-events-log :items="eventsLog"></site-events-log>
                    </v-tab-item>
                    <v-tab-item>
                        <campaign-reputation :items="reputation" :site="item"/>
                    </v-tab-item>
                </v-tabs-items>
            </v-tabs>
        </v-flex>

        <site-edit
            :items="[item]"
            :resellers="resellers"
            ref="siteEditDialog"
            v-on:site:updated="getItem"
        />

        <edit
            :items="[item ? item.user : null]"
            ref="editDialog"
            v-on:merchant:updated="getItem"
        />
        <json-dialog ref="jsonDialog"></json-dialog>
        <merchant-show-dialog ref="merchantDetails"></merchant-show-dialog>
        <company-show-dialog ref="companyDetails"></company-show-dialog>
        <call-create
            ref="callDialog"
            v-on:call:create="(item) => this.$refs.callList.addItem(item)"
            v-on:call:update="(item) => { this.$refs.callList.refreshItem(item); this.getNotes() }"
            v-on:call:deleted="(item) => this.$refs.callList.removeItem(item)"
        ></call-create>
        <meeting-create
            ref="meetingDialog"
            v-on:meeting:create="(item) => this.$refs.meetingList.addItem(item)"
        ></meeting-create>
        <site-stage-details
            ref="stageDetails"
            v-on:stage-status:changed="getItem"
            v-on:boarding:changed="() => {
                                this.getItem()
                                this.getBoarding()
                            }"
            v-on:offer:create="() => {
                                this.getItem()
                                this.getOffers()
                            }"
            v-on:offer:refresh-item="() => {
                                this.getOffers()
                            }"
            v-on:call:follow-up="() => {
                                this.getTodos()
                                this.getNotes()
                            }"
        ></site-stage-details>
    </v-layout>
    <div class="text-xs-center" v-else>
        <v-progress-circular
            :size="70"
            color="primary"
            indeterminate
        ></v-progress-circular>
    </div>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import {flatten} from '@helpers'
import LayoutDefault from '@layouts/Default'
import Statistics from './statistics'
import Traffic from '@views/statistics/traffic'
import TransactionList from '@views/billing/transactions/list'
import InvoicesList from '@views/billing/invoices/list'
import SubscriptionList from '@views/billing/subscriptions/list'
import SiteEdit from '@views/sites/edit'
import Edit from '@views/merchants/edit'
import OffersList from '@views/billing/offers/list'
import InstalledApps from "../statistics/installed-apps";
import InstalledProviders from "../statistics/installed-providers";
import SiteEventsLog from "./events-log";
import JsonDialog from "../../../components/JsonDialog";
import BoardingAnswered from "../adorimo/boarding/answered";
import SegmentsList from "../segments/list";
import CampaignReputation from "../statistics/campaign-reputation";
import CallCreate from "../calls/create";
import CallList from "../calls/list";
import CompanyShowDialog from "../companies/show-dialog";
import MeetingCreate from "../meetings/create";
import VueCalendly from "../../../components/Calendly";
import MeetingList from "../meetings/list";
import TodoList from "../todos/list";
import NoteList from "../notes/list";
import SiteStageDetails from "./site-stage-details";

export default {
    name: "site-details",
    // props: ['item'],
    components: {
        SiteStageDetails,
        NoteList,
        TodoList,
        MeetingList,
        VueCalendly,
        MeetingCreate,
        CompanyShowDialog,
        CallList,
        CallCreate,
        MerchantShowDialog: () => import("../merchants/show-dialog"),
        SegmentsList,
        BoardingAnswered,
        JsonDialog,
        SiteEventsLog,
        InstalledProviders,
        InstalledApps,
        LayoutDefault,
        Traffic,
        Statistics,
        TransactionList,
        InvoicesList,
        SubscriptionList,
        SiteEdit,
        Edit,
        OffersList,
        CampaignReputation,
    },
    data() {
        return {
            tab: 0,
            item: null,
            loading: false,
            transactions: [],
            invoices: [],
            notes: [],
            todos: [],
            meetings: [],
            calls: [],
            offers: [],
            subscriptions: [],
            traffic: [],
            apps: [],
            providers: [],
            resellers: [],
            eventsLog: [],
            boarding: [],
            segments: [],
            statuses: {
                0: 'Canceled',
                1: 'Active',
                2: 'Past Due',
                3: 'Expired',
            },
            statistics: {},
            dialog: false,
            reputation: [],
        }
    },
    created() {
    },
    mounted() {
        // this.init()
    },
    watch: {
        // item() {
        //     this.init()
        // }
    },
    computed: {
        user() {
            return this.$store.getters['auth/user']
        },
        stepColor() {
            if (this.item.stage_step && this.item.stage_step.days < this.item.stage_step_days) {
                return 'error'
            }

            return 'orange'
        },
    },
    methods: {
        init() {
            this.getSubscriptions()
            if (this.isResellerLevel(2)) {
                this.getOffers()
            }
            if (this.isCloudCartEmployee) {
                this.getTransactions()
                this.getInvoices()
                this.getResellers()
                this.getNotes();
                this.getTodos();
                this.getMeetings()
                this.getCalls()
                this.getTraffic()
                this.getOffers()
                this.getApps()
                this.getProviders()
                this.getEventsLog()
                this.getBoarding()
                this.getSegments()
                this.getStatistics()
                this.getCampaignReputation()
            }
        },
        getItem() {
            axios.get(`/sites/${this.item.id}`)
                .then(response => {
                    let item = response.data.data
                    if (!item.invoicing) {
                        item.invoicing = {
                            user_id: item.user_id,
                            site_id: item.id,
                            country: item.user.country,
                            lang: item.user.lang,
                            name: item.user.name,
                        }
                    }
                    this.item = item
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        reinstall() {
            this.loading = true
            axios.post(`/sites/${this.item.id}/re-install`)
                .then((response) => {
                    this.handleSuccess(response)
                    this.item.router_site.progress = 'completed'
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        showDB(routerSite) {
            if (!routerSite) {
                return '0 - null'
            }

            let dbIds = this.dbIds()

            return dbIds.find(db => db.id === routerSite.database_id)['name']
        },
        getBoarding() {
            this.loading = true
            axios.get(`/sites/${this.item.id}/boarding`)
                .then(response => {
                    this.loading = false
                    this.boarding = response.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getSegments() {
            this.loading = true
            axios.get(`/sites/${this.item.id}/segments`)
                .then(response => {
                    this.loading = false
                    this.segments = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getTransactions() {
            this.transactions = []
            axios.get(`/sites/${this.item.id}/transactions`)
                .then(response => {
                    this.transactions = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getInvoices() {
            this.invoices = []
            axios.get(`/sites/${this.item.id}/invoices`)
                .then(response => {
                    this.invoices = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getSubscriptions() {
            this.subscriptions = []
            axios.get(`/sites/${this.item.id}/subscriptions`)
                .then(response => {
                    this.subscriptions = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getResellers() {
            this.resellers = []
            axios.get('/resellers/autocomplete')
                .then(response => {
                    this.resellers = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getTraffic() {
            this.traffic = []
            axios.get(`/statistics/traffic/${this.item.id}`)
                .then(response => {
                    this.traffic = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getNotes() {
            this.todos = []
            this.loading = true
            axios.get(`/notes/for-site/${this.item.id}`)
                .then(response => {
                    this.notes = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getTodos() {
            this.todos = []
            this.loading = true
            axios.get(`/todos/for-site/${this.item.id}`)
                .then(response => {
                    this.todos = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getMeetings() {
            this.meetings = []
            this.loading = true
            axios.get(`/meetings/for-site/${this.item.id}`)
                .then(response => {
                    this.meetings = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getCalls() {
            this.calls = []
            this.loading = true
            axios.get(`/calls/for-site/${this.item.id}`)
                .then(response => {
                    this.calls = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getOffers() {
            this.offers = []
            this.loading = true
            axios.get(`/offers/for-site/${this.item.id}`)
                .then(response => {
                    this.offers = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getApps() {
            this.apps = []
            this.loading = true
            axios.get(`/statistics/installed-apps/${this.item.id}`)
                .then(response => {
                    this.apps = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getProviders() {
            this.providers = []
            this.loading = true
            axios.get(`/statistics/installed-providers/${this.item.id}`)
                .then(response => {
                    this.providers = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getStatistics() {
            this.statistics = {}
            this.loading = true
            axios.get(`/sites/${this.item.id}/statistics`)
                .then(response => {
                    this.statistics = response.data.data
                    this.loading = false
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getEventsLog() {
            this.eventsLog = []
            this.loading = true
            axios.get(`/sites/${this.item.id}/events-log`)
                .then(response => {
                    this.eventsLog = response.data.data
                    this.loading = false
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        mainAsPrimary(siteId) {
            axios.post(`/sites/${siteId}/main-as-primary`)
                .then((response) => {
                    this.item = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getCampaignReputation() {
            this.loading = true
            this.reputation = []
            axios.get(`/campaigns/channel-reputation/${this.item.id}`)
                .then(response => {
                    this.reputation = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        status(value) {
            return this.statuses[value]
        },
        close() {
            this.dialog = false
        }
    }
}
</script>

<style scoped>

</style>
