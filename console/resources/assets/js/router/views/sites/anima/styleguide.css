:root {
    --alabaster: #fafafa;
    --apple: #4caf50;
    --black: #000000;
    --black-22: #000000de;
    --black-32: #00000099;
    --black-42: #00000066;
    --black-62: #0000001f;
    --black-72: #0000008a;
    --black-8: #0000000a;
    --black-haze: #f7f7f7;
    --delta: #a3a3a3;
    --denim: #1867c0;
    --granite-gray: #666666;
    --granite-gray-2: #666666de;
    --grey: #d9d9d9;
    --lemon: #ffeb3b;
    --mine-shaft: #333333;
    --pizazz: #fb8c00;
    --rose-2: #ff5252;
    --star-dust: #9e9e9e;
    --star-dust-2: #9b9b9b;
    --v-primary-base: #1976d2;
    --white: #ffffff;

    --font-size-l2: 20px;
    --font-size-l3: 18px;
    --font-size-m2: 16px;
    --font-size-s2: 14px;
    --font-size-xl2: 24px;
    --font-size-xs2: 13px;
    --font-size-xxs: 10px;
    --font-size-xxs2: 12px;
}

.roboto-medium-black-20px {
    font-size: var(--font-size-l2);
    font-style: normal;
    font-weight: 500;
}

.roboto-medium-black-18px {
    font-size: var(--font-size-l3);
    font-style: normal;
    font-weight: 500;
}

.roboto-normal-black-16px-22 {
    color: var(--black-32);
    font-size: var(--font-size-m2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-black-14px-22 {
    color: var(--granite-gray);
    font-size: var(--font-size-s2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-black-12px-22 {
    color: var(--black-72);
    font-size: var(--font-size-xxs2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-granite-gray-16px {
    color: var(--granite-gray);
    font-size: var(--font-size-m2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-black-16px-32 {
    color: var(--black-42);
    font-size: var(--font-size-m2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-black-12px-32 {
    color: var(--star-dust);
    font-size: var(--font-size-s2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-black-12px-32 {
    color: var(--star-dust);
    font-size: var(--font-size-xxs2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-black-14px {
    color: var(--black-32);
    font-size: var(--font-size-s2);
    font-style: normal;
    font-weight: 400;
}

.roboto-medium-black-14px {
    font-size: var(--font-size-s2);
    font-style: normal;
    font-weight: 500;
}

.roboto-medium-black-12px {
    font-size: var(--font-size-xxs2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-black-16px2 {
    font-size: var(--font-size-m2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-white-12px {
    color: var(--white);
    font-size: var(--font-size-xxs2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-granite-gray-16px-2 {
    color: var(--granite-gray-2);
    font-size: var(--font-size-m2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-star-dust-16px {
    color: var(--star-dust);
    font-size: var(--font-size-m2);
    font-style: normal;
    font-weight: 400;
}

.roboto-semi-bold-white-16px {
    color: var(--white);
    font-size: var(--font-size-m2);
    font-style: normal;
    font-weight: 600;
}

.roboto-normal-navy-blue-16px {
    color: var(--v-primary-base);
    font-size: var(--font-size-m2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-navy-blue-12px {
    color: var(--v-primary-base);
    font-size: var(--font-size-xxs2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-star-dust-14px {
    color: var(--star-dust-2);
    font-size: var(--font-size-s2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-white-20px {
    color: var(--white);
    font-size: var(--font-size-l2);
    font-style: normal;
    font-weight: 400;
}

.roboto-medium-navy-blue-14px {
    color: var(--v-primary-base);
    font-size: var(--font-size-s2);
    font-style: normal;
    font-weight: 500;
}

.roboto-normal-mine-shaft-16px {
    color: var(--mine-shaft);
    font-size: var(--font-size-m2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-white-16px {
    color: var(--white);
    font-size: var(--font-size-m2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-navy-blue-20px {
    color: var(--v-primary-base);
    font-size: var(--font-size-l2);
    font-style: normal;
    font-weight: 400;
}

.roboto-medium-black-13px {
    font-size: var(--font-size-xs2);
    font-style: normal;
    font-weight: 500;
}

.roboto-normal-navy-blue-24px {
    color: var(--v-primary-base);
    font-size: var(--font-size-xl2);
    font-style: normal;
    font-weight: 400;
}

.roboto-normal-black-24px-3 {
    font-size: var(--font-size-xl2);
    font-style: normal;
    font-weight: 400;
}

.roboto-semi-bold-black-10px {
    font-size: var(--font-size-xxs);
    font-style: normal;
    font-weight: 600;
}

.roboto-semi-bold-black-16px2 {
    font-size: var(--font-size-m2);
    font-style: normal;
    font-weight: 600;
}

.roboto-semi-bold-black-20px-3 {
    font-size: var(--font-size-l2);
    font-style: normal;
    font-weight: 600;
}

.roboto-semi-bold-black-14px {
    color: var(--black-32);
    font-size: var(--font-size-s2);
    font-style: normal;
    font-weight: 600;
}

.roboto-normal-black-14px-4 {
    color: var(--black-42);
    font-size: var(--font-size-s2);
    font-style: normal;
    font-weight: 400;
}

.roboto-medium-navy-blue-13px {
    color: var(--v-primary-base);
    font-size: var(--font-size-xs2);
    font-style: normal;
    font-weight: 500;
}

.border-1px-black-6 {
    border: 1px solid var(--black-62);
}

.border-radius-4 {
    border-radius: 4px;
}

.border-bottom-1px-black-6 {
    border-bottom: 1px solid var(--black-62);
}

.theme--dark .roboto-normal-black-16px-32,
.theme--dark .roboto-normal-black-14px-4,
.theme--dark .roboto-normal-black-16px-22,
.theme--dark .roboto-normal-black-14px-22,
.theme--dark .roboto-normal-black-12px-32,
.theme--dark .roboto-normal-black-12px-32,
.theme--dark .roboto-normal-black-12px-22 {
    color: var(--alabaster);
}

.theme--dark .roboto-normal-granite-gray-16px,
.theme--dark .roboto-normal-granite-gray-16px-2 {
    color: var(--star-dust);
}
.theme--dark .border-bottom-1px-black-6 {
    border-bottom-color: var(--grey);
}

.v-card.no-shadow {
    box-shadow: none;
}

.store-stats {
    align-items: center;
    display: flex;
    flex-flow: wrap;
    position: relative;
    flex-direction: column;
}

.right-cards-holder {
    position: fixed;
    right: 10px;
    width: 320px;
    height: calc(100vh - 100px);
    overflow-y: auto;
}

.right-card {
    background-color: transparent;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    padding: 16px 15px;
    position: relative;
    margin-left: auto;
}

.right-card + .right-card {
    margin-top: 3px;
}

.card-head {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    width: 100%;
}

.card-head-with-icon {
    align-items: center;
    display: flex;
    justify-content: space-between;
    position: relative;
    height: 26px;
    width: 100%;
}

.head-line {
    border-top: 1px solid #d9d9d9;
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%;
}

.store-head {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-height: 72px;
}

@media (min-width: 1264px) {
    .store-head {
        margin-top: -10px;
    }
}

.frame-109 {
    align-items: center;
    display: flex;
}

.title {
    letter-spacing: 0;
    margin-bottom: 2px;
    min-width: 174px;
}

.frame-103 {
    align-items: center;
    display: flex;
    flex-flow: wrap;
    position: relative;
}

.border-bottom {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.w-100 {
    width: 100% !important;
}

.text-align-right {
    text-align: right !important;
}

.text-align-center {
    text-align: center !important;
}

.v-card__text .d-flex>*, .d-inline-flex>* {
    flex: initial !important;
}

.justify-between {
    justify-content: space-between !important;
}

.justify-center {
    justify-content: center !important;
}

.justify-start {
    justify-content: flex-start !important;
}

@media (min-width: 960px) {
    .justify-center-md {
        justify-content: center !important;
    }
}

@media (max-width: 959px) {
    .flex.px-xs-1,
    .px-xs-1 {
        padding-left: 4px !important;
        padding-right: 4px !important;
    }
}

@media (max-width: 600px) {
    .frame-103 {
        min-width: auto;
    }
}

.database {
    align-items: center;
    display: flex;
    margin-left: 16px;
    min-width: 46px;
    white-space: nowrap;
}

.number-1 {
    letter-spacing: 0;
    margin-left: 4px;
    min-width: 18px;
}

.other-site {
    align-items: center;
    display: flex;
    margin-left: 16px;
    min-width: 167px;
    white-space: nowrap;
}

.store-info {
    align-items: baseline;
}

.store-plan {
    align-items: center;
    display: flex;
    flex-flow: wrap;
    flex-direction: column;
}

.store-details {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    word-break: break-word;
}

.pb {
    height: 16px;
    width: 100%;
}

.frame-113 {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
}

.frame-111 {
    align-items: center;
    display: flex;
}

.frame-113-1 {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-height: 52px;
}

.frame-113-2 {
    align-items: center;
    display: flex;
    min-width: 151px;
}

.frame-113-3 {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-height: 52px;
}

.frame-113-5 {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-height: 52px;
}

.frame-113-9 {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-height: 52px;
}

.frame-111-5 {
    align-items: flex-start;
    display: flex;
    position: relative;
}

.frame-113-10 {
    align-items: flex-end;
    display: flex;
}

.frame-113-11 {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-height: 45px;
    position: relative;
}

.frame-111-6 {
    align-items: flex-start;
    display: flex;
    position: relative;
}

.frame-113-12 {
    align-items: flex-start;
    display: flex;
    position: relative;
}

.frame-143 {
    align-items: flex-end;
    display: flex;
    flex-direction: column;
    min-height: 202px;
}

.notes-field {
    align-items: flex-start;
    background-color: #f0f7fd;
    border-radius: 0px 0px 4px 4px;
    display: flex;
    padding: 15px 16px;
}

.nam-rhoncus-egestas {
    letter-spacing: 0;
    line-height: 18px;
    min-height: 126px;
}

.notes-button {
    align-items: flex-start;
    background-color: var(--white);
    border: 1px solid var(--delta);
    border-radius: 4px;
    display: flex;
    height: 28px;
    margin-top: 16px;
    min-width: 57px;
    padding: 6px 14px;
}

.save {
    color: var(--delta);
    font-size: var(--font-size-xxs2);
    font-weight: 400;
    letter-spacing: 0;
    min-height: 14px;
    line-height: 1.2;
}

.additional-informati {
    font-size: var(--font-size-m2);
    letter-spacing: 0;
    margin-top: 32px;
    min-height: 83px;
}

.span1 {
    color: var(--black);
    font-weight: 400;
}

.frame-91 {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
}

.merchant-id {
    align-items: center;
    display: flex;
}

.id,
.id-1 {
    letter-spacing: 0;
    margin-top: 1px;
}

.phone,
.number-2,
.phone-2 {
    letter-spacing: 0;
    margin-left: 8px;
}

.merchant-phone {
    align-items: flex-start;
    display: flex;
    margin-top: 3px;
}

.phone-1 {
    letter-spacing: 0;
    margin-left: 4px;
}

.merchant-email {
    align-items: center;
    display: flex;
    margin-top: 3px;
}

.frame-94 {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-height: 60px;
}

.merchant-stores {
    align-items: center;
    display: flex;
    justify-content: flex-end;
}

.stores {
    letter-spacing: 0;
    margin-top: 1px;
    min-height: 19px;
}

.merchant-row,
.merchant-companies {
    align-items: center;
    display: flex;
    justify-content: flex-start;
    margin-top: 3px;
}

.companies {
    letter-spacing: 0;
    min-height: 19px;
}

.number-3 {
    color: var(--black-22);
    font-weight: 500;
    letter-spacing: 0;
    margin-left: 8px;
    margin-top: -1px;
    min-height: 21px;
}

.v-card-company {
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    margin-top: 3px;
    padding: 16px 15px;
    position: relative;
    margin-left: auto;
}

.comnpany-details {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    margin-top: 5px;
    width: 100%;
}

.company-id {
    align-items: center;
    display: flex;
}

.company-vat {
    align-items: center;
    display: flex;
    justify-content: flex-end;
    margin-top: 3px;
}

.vat {
    letter-spacing: 0;
}

.bg7372727332,
.sofia-bulgaria {
    letter-spacing: 0;
    margin-left: 8px;
}

.company-address {
    align-items: center;
    display: flex;
    justify-content: flex-end;
    margin-top: 3px;
}

.other-stores-list {
    position: relative;
    overflow: auto;
    max-height: 169px;
}

.v-card-timeline {
    align-items: center;
    background-color: var(--white);
    border-radius: 4px;
    box-shadow: 0px 1px 5px #0000001f, 0px 2px 2px #00000024, 0px 1px 1px #00000033;
    display: flex;
    margin-top: 45px;
    min-width: 100%;
    padding: 0 16px;
    position: relative;
}

.activities-timeline {
    margin-left: 16px;
}

@media (max-width: 992px) {
    .activities-timeline {
        margin-left: 0;
    }
}

.date {
    align-items: flex-start;
    display: flex;
    margin-top: 32px;
}

.today {
    height: 16px;
    letter-spacing: 0;
    line-height: 16px;
    margin-top: -1px;
    white-space: nowrap;
}

.date-1 {
    height: 16px;
    letter-spacing: 0;
    line-height: 16px;
    margin-left: 4px;
    margin-top: -1px;
    white-space: nowrap;
}

.v-card.no-shadow {
    box-shadow: none;
}

.theme--dark .number-3 {
    color: var(--white-2);
}

.theme--dark .additional-informati {
    background-color: #303030;
}

.mail {
    word-break: break-word;
}

.store-link {
    display: flex;
    align-items: center;
    margin-top: 10px;
    padding-bottom: 5px;
}

.shop {
    word-break: break-word;
}

.merchant-info {
    align-items: center;
    display: flex;
    margin-top: 10px;
    margin-bottom: 15px;
}

.merchant-title-wrap {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    margin-left: 16px;
    min-height: 40px;
}

.merchant-country {
    align-items: center;
    display: flex;
    margin-top: 1px;
    min-width: 75px;
}

.merchant-country-icon {
    align-self: center;
    height: 14px;
    width: 19px;
}

.merchant-country-text {
    margin-left: 4px;
}

.merchant-actions {
    border-top: 1px solid #d9d9d9;
    align-items: flex-start;
    display: flex;
    flex-flow: wrap;
    justify-content: space-between;
    margin-top: 24px;
    padding: 0 16px;
    width: 100%;
}

.task-title-wrap {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    min-height: 52px;
}

.task-title {
    align-items: flex-start;
    display: flex;
}

.task-info {
    letter-spacing: 0;
    margin-top: 3px;
    min-height: 16px;
}

button.notes-button {
    align-items: flex-start;
    background-color: var(--white);
    border: 1px solid var(--delta);
    border-radius: 4px;
    display: flex;
    height: 28px;
    margin-top: 16px;
    min-width: 57px;
    padding: 6px 14px
}

/*.v-btn--icon-small {*/
/*    width: 22px;*/
/*    height: 22px;*/
/*}*/

.bottom-card table.v-table tbody td {
    vertical-align: baseline;
}

.bottom-card table.v-table tbody td:last-child {
    vertical-align: middle;
}

@media (min-width: 1200px) {
    .v-card .flex.lg5-custom {
        width: 20%;
        max-width: 20%;
        flex-basis: 20%;
    }
}

@media (min-width: 1400px) {
    .v-card .flex.lg9-custom {
        flex-basis: 80%;
        flex-grow: 0;
        max-width: 80%;
    }

    .v-card .flex.lg3-custom {
        max-width: 325px;
    }
}

@media (min-width: 960px) {
    .justify-md-center {
        justify-content: center;
    }

    .padding-right-custom {
        padding-right: 320px;
    }

    .border-md-left {
        border-left: 1px solid #dedede;
    }
}

@media (max-width: 1399px) and (min-width: 960px) {
    .right-cards-wrap .v-card-merchant,
    .right-cards-wrap .v-card-company,
    .right-cards-wrap .v-card-other-stores,
    .right-cards-holder {
        width: 250px;
    }

    .padding-right-custom {
        padding-right: 250px;
    }
}

@media (max-width: 1399px) and (min-width: 768px) {
    .right-cards-wrap {
        display: flex;
        flex-flow: wrap;
        width: 100%;
    }
}

@media (max-width: 1799px) {
    .merchant-details {
        flex-direction: column;
    }
}

@media (max-width: 959px) {
    .v-content .v-navigation-drawer__border {
        display: none;
    }

    .flex.p-custom-mobile {
        padding: 20px 4px !important;
    }

    .right-cards-holder {
        width: auto;
        height: auto;
        position: relative;
        top: auto;
        right: auto;
    }

    .right-cards-wrap .v-card-merchant,
    .right-cards-wrap .v-card-company,
    .right-cards-wrap .v-card-other-stores {
        width: 50%;
        margin: 20px 0;
    }

    .store-stats {
        align-items: flex-start;
    }

    .store-stats .v-chip {
        margin-left: 0 !important;
    }

    .store-stats .flex {
        padding-left: 0 !important;
    }

    .column-xs {
        flex-direction: column !important;
    }
}

@media (min-width: 960px) {
    .custom-width-left {
        position: absolute;
        height: calc(100% - 20px);
    }

    .custom-width-left .v-navigation-drawer {
        height: 100%;
    }

    .custom-width-right {
        padding-left: 160px !important;
    }
}

@media (max-width: 599px) {
    .right-cards-wrap .v-card-merchant,
    .right-cards-wrap .v-card-company,
    .right-cards-wrap .v-card-other-stores {
        width: 100%;
        margin: 20px 0;
    }

    .top-stats {
        padding-bottom: 30px !important;
    }
}

