<template>
  <div :class="[`v-chipv-size-small`, className || ``]">
    <div class="text-2 valign-text-middle roboto-semi-bold-white-16px">{{ text }}</div>
  </div>
</template>

<script>
export default {
  name: "VChipvSizeSmall",
  props: ["text", "className"],
};
</script>

<style scoped>
.v-chipv-size-small {
  align-items: flex-start;
  background-color: var(--apple);
  border-radius: 12px;
  display: flex;
  height: 24px;
  margin-left: 4px;
  min-width: 70px;
  padding: 1px 12px;
}

.text-2 {
  height: 20px;
  letter-spacing: 0;
  line-height: 20px;
  white-space: nowrap;
}

.v-chipv-size-small.v-chipv-size-small-1 {
  background-color: var(--pizazz);
  min-width: 73px;
}

.v-chipv-size-small.v-chipv-size-small-2 {
  background-color: var(--rose-2);
  min-width: 56px;
}
</style>
