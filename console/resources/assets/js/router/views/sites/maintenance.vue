<template>
    <v-dialog
        v-model="dialog"
        max-width="700px"
        scrollable
        persistent
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h3 class="headline white--text mb-0">{{ title }}</h3>
            </v-card-title>

            <v-card-text>
                <v-container grid-list-md pa-0>
                    <v-layout wrap>
                        <v-flex xs12 v-if="!maintenance">
                            <v-textarea
                                outline
                                v-model="reason"
                                label="Reason"
                                :error-messages="errors['router_site.maintenance_reason']"
                                ref="noteTextArea"
                                height="400"
                            ></v-textarea>
                        </v-flex>
                        <v-flex xs12 v-else>
                            <p v-html="reason"></p>
                        </v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click="closeDialog">Cancel</v-btn>
                <v-btn
                    v-if="!maintenance"
                    color="primary"
                    :loading="loading"
                    :disabled="loading"
                    flat
                    @click.native="save"
                >Put in maintenance mode
                </v-btn>
                <v-btn
                    v-else
                    color="primary"
                    :loading="loading"
                    :disabled="loading"
                    flat
                    @click.native="unmaintenance"
                >Remove maintenance mode
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import {
    TiptapVuetify, Heading, Bold, Italic, Strike, Underline, Code, CodeBlock, Paragraph, BulletList, OrderedList,
    ListItem, Link, Blockquote, HardBreak, HorizontalRule, History
} from 'tiptap-vuetify'
import {Image} from 'tiptap-extensions'

export default {
    name: "site-maintenance",
    components: {
        TiptapVuetify,
    },
    data() {
        return {
            dialog: false,
            loading: false,
            statuses: [],
            errors: [],
            item: {},
            title: '',
            // declare tip-tap extensions
            extensions: [
                new Heading({
                    levels: [1, 2, 3]
                }),
                new Bold(),
                new Italic(),
                new Strike(),
                new Underline(),
                // new Code(),
                // new CodeBlock(),
                new Paragraph(),
                new BulletList(),
                new OrderedList(),
                new ListItem(),
                // new Link(),
                new Blockquote(),
                new HardBreak(),
                new HorizontalRule(),
                new History(),
            ],
            nativeExtensions: [
                new Image(),
            ],
        }
    },
    mounted() {
    },
    methods: {
        show(item) {
            this.item = item
            this.title = item.router_site?.maintenance ? 'Remove maintenance mode' : 'Put site in maintenance mode'
            this.errors = [];
            this.showDialog()
            setTimeout(() => {
                this.$refs.noteTextArea.focus()
            }, 100);
        },
        save() {
            this.loading = true
            axios.post(`/sites/${this.item.id}/maintenance`, this.item)
                .then((response) => {
                    this.loading = false
                    this.$emit('site:updated')
                    this.closeDialog()
                })
                .catch(error => {
                    this.handleError(error)
                    setTimeout(() => {
                        this.$refs.noteTextArea.focus()
                    }, 100);
                })
        },
        unmaintenance() {
            this.loading = true
            axios.post(`/sites/${this.item.id}/unmaintenance`, {})
                .then((response) => {
                    this.loading = false
                    this.$emit('site:updated')
                    this.closeDialog()
                })
                .catch(error => {
                    this.handleError(error)
                    setTimeout(() => {
                        this.$refs.noteTextArea.focus()
                    }, 100);
                })
        },
    },
    computed: {
        reason: {
            get() {
                return this.item.router_site?.maintenance_reason || null;
            },
            set(value) {
                if(this.item.router_site) {
                    this.item.router_site.maintenance_reason = value;
                }
            }
        },
        maintenance: {
            get() {
                return this.item.router_site?.maintenance || 0;
            },
            set(value) {
                if(this.item.router_site) {
                    this.item.router_site.maintenance = value;
                }
            }
        }
    }
}
</script>