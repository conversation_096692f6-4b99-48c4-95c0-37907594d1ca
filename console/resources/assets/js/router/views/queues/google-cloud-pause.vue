<template>
    <layout-default>

        <v-container grid-list-md fluid>
            <v-card>
                <v-card-title>
                    <v-list-tile>
                        <v-list-tile-content>
                            <v-list-tile-title><h2>{{ 'Queues pause' | t }}</h2></v-list-tile-title>
                            <v-list-tile-sub-title class="caption">google-cloud</v-list-tile-sub-title>
                        </v-list-tile-content>
                    </v-list-tile>
                    <v-spacer></v-spacer>
                </v-card-title>
            </v-card>

            <v-data-table
                :headers="headers"
                :items="items"
                :pagination.sync="pagination"
                :total-items="totalItems"
                :loading="loading"
                class="elevation-1"
            >
                <template slot="items" slot-scope="props">
                    <td>{{ props.item.id }}</td>
                    <td>
                        <v-btn
                            icon my-0 class="mx-0 my-0 v-btn--icon-small"
                            @click="queuePause(props.item)"
                        >
                            <v-icon v-if="props.item.paused" color="green">check_circle</v-icon>
                            <v-icon v-if="!props.item.paused" color="red">block</v-icon>
                        </v-btn>
                    </td>
                    <td>
                        <span v-if="props.item.paused">{{ props.item.paused  | formatDateTimeSeconds}}</span>
                    </td>
                </template>
            </v-data-table>
        </v-container>

    </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'

export default {
    components: {
        LayoutDefault,
    },
    data() {
        return {
            totalItems: 0,
            items: [],
            loading: true,
            pagination: {
                rowsPerPage: 25,
                descending: true,
            },
            headers: [
                {
                    text: 'ID',
                    align: 'left',
                    sortable: true,
                    value: 'id'
                },
                {text: 'Paused', value: 'paused'},
                {text: 'Paused date', value: 'paused'},
            ],
        }
    },
    mounted() {
        this.getItems()
    },
    methods: {
        getItems() {
            this.loading = true
            axios.get('/queues/pause/google-cloud')
                .then(response => {
                    this.loading = false
                    this.items = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        queuePause(queue) {
            this.loading = true
            axios.post('/queues/pause/google-cloud/' + queue.id + '/' + (queue.paused === false ? 1 : 0))
                .then(response => {
                    this.loading = false
                    this.items = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    },
}
</script>
