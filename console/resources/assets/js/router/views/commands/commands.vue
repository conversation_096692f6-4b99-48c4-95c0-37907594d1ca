<template>
    <layout-default>

        <v-container grid-list-md fluid>
            <v-card>
                <v-card-title>
                    <h1>Commands</h1>
                    <v-btn
                        small
                        dark
                        fab
                        color="pink"
                        @click="$refs.runDialog.show()"
                    >
                        <v-icon>add</v-icon>
                    </v-btn>
                    <v-spacer></v-spacer>
                    <v-text-field
                        append-icon="search"
                        label="Search"
                        single-line
                        v-model="search"
                        clearable
                    ></v-text-field>
                </v-card-title>
            </v-card>

            <v-data-table
                :headers="headers"
                :items="items"
                :pagination.sync="pagination"
                :total-items="totalItems"
                :loading="loading"
                :custom-filter="customFilter"
                class="elevation-1"
                ref="dataTable"
                must-sort
            >
                <template slot="items" slot-scope="props">
                    <tr>
                        <td>
                            {{ props.item.id }}<br>
                            {{ props.item.created_at | formatDateTime }}
                        </td>
                        <td v-if="props.item.user">
                            <v-list two-line dense class="transparent list-p0">
                                <v-list-tile>
                                    <v-list-tile-avatar>
                                        <v-avatar>
                                            <img :src="props.item.user.google_account.avatar" alt="Avatar">
                                        </v-avatar>
                                    </v-list-tile-avatar>
                                    <v-list-tile-content>
                                        <v-list-tile-title>{{ props.item.user.name }}</v-list-tile-title>
                                        <v-list-tile-sub-title>{{ props.item.user.email }}</v-list-tile-sub-title>
                                    </v-list-tile-content>
                                </v-list-tile>
                            </v-list>
                        </td>
                        <td v-else></td>
                        <td>{{ props.item.command }}</td>
                        <td v-highlightjs="json(props.item.params)" :class="$root.dark ? 'dark' : ''">
                            <code class="json"></code>
                        </td>
                        <td>
                            <v-btn v-if="props.item.status === 0"
                                   color="success"
                                   @click="$refs.outputDialog.show(props.item)"
                            >Success
                            </v-btn>
                            <v-btn v-if="props.item.status === null"
                                   color="warning"
                                   @click="$refs.outputDialog.show(props.item)"
                            >Running
                            </v-btn>
                            <v-btn v-if="props.item.status === 1"
                                   color="error"
                                   @click="$refs.outputDialog.show(props.item)"
                            >Error
                            </v-btn>
                            <v-btn v-if="props.item.status === 2"
                                   color="info"
                                   @click="$refs.outputDialog.show(props.item)"
                            >Canceled
                            </v-btn>
                            <v-tooltip bottom v-if="props.item.status === null">
                                <v-btn slot="activator" icon class="mx-0" @click="cancel(props.item)">
                                    <v-icon color="pink">block</v-icon>
                                </v-btn>
                                <span>Cancel</span>
                            </v-tooltip>
                        </td>
                        <td>{{ props.item.time }}</td>
                    </tr>
                </template>

                <v-alert slot="no-results" :value="true" color="error" icon="warning">
                    Your search for "{{ search }}" found no results.
                </v-alert>

            </v-data-table>

            <command-output ref="outputDialog"/>
            <run-command ref="runDialog" v-on:refresh="getItems"/>
        </v-container>

    </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import {flatten} from '@helpers'
import CommandOutput from './commands/output'
import RunCommand from './commands/run'
import _ from "lodash"

const CancelToken = axios.CancelToken;
let cancel;

export default {
    components: {
        LayoutDefault,
        CommandOutput,
        RunCommand,
    },
    data() {
        return {
            search: '',
            totalItems: 0,
            items: [],
            loading: false,
            editedIndex: null,
            enableAutoRefresh: false,
            refreshInterval: null,
            pagination: {
                page: 1,
                sortBy: "id",
                totalItems: 0,
                rowsPerPage: 25,
                descending: true,
            },
            headers: [
                {text: 'ID / Created At', value: 'id'},
                {text: 'Triggered By', value: 'user_id'},
                {text: 'Command', value: 'command'},
                {text: 'Params', value: 'params', sortable: false},
                {text: 'Status', value: 'status'},
                {text: 'Time (sec)', value: 'time'},
            ],
        }
    },
    mounted() {
        this.getItems()
    },
    beforeDestroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval)
        }
    },
    watch: {
        enableAutoRefresh(val) {
            if (val === true) {
                this.autoRefresh()
            } else {
                clearInterval(this.refreshInterval)
            }
        },
        search(val) {
            if (!val || val.length >= 3) {
                this.getItems()
            }
        },
        pagination: {
            handler(newVal, oldVal) {
                delete newVal.totalItems;
                delete oldVal.totalItems
                if (!_.isEqual(newVal, oldVal)) {
                    this.getItems()
                }
            },
            deep: false
        },
    },
    methods: {
        json(value) {
            return JSON.stringify(value, null, 2)
        },
        autoRefresh() {
            this.refreshInterval = setInterval(() => {
                this.getItems();
            }, 5000)
        },
        getItems() {
            this.loading = true
            if (cancel !== undefined) {
                cancel();
            }
            this.loaded = false
            const params = {
                cancelToken: new CancelToken(function executor(c) {
                    cancel = c;
                }),
                params: {
                    search: this.search,
                    ...this.pagination,
                }
            }
            axios.get('/commands', params)
                .then(response => {
                    this.loading = false
                    this.enableAutoRefresh = false
                    this.totalItems = response.data.meta.total
                    this.items = response.data.data.map(item => {
                        if (item.status === null) {
                            this.enableAutoRefresh = true
                        }
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        cancel(item) {
            this.editedIndex = this.items.indexOf(item)
            this.loading = true
            axios.post(`/commands/${item.id}/cancel`)
                .then((response) => {
                    this.loading = false
                    Object.assign(this.items[this.editedIndex], response.data)
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        customFilter(items, search, filter) {
            if (!search || search.trim() === '') return items
            search = search.toString().toLowerCase().trim()

            return items.filter(item => item.flatten.some(val => filter(val, search)))
        },
    }
}
</script>
