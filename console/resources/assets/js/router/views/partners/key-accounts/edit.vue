<template>
    <v-dialog
            v-model="dialog"
            max-width="400px"
            scrollable
            persistent
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h3 class="headline white--text mb-0">{{ title }}</h3>
            </v-card-title>

            <v-card-text>
                <v-container grid-list-md>
                    <v-layout wrap>
                        <v-flex xs12>
                            <v-text-field
                                    label="Name"
                                    v-model="editedItem.name"
                                    required
                                    :error-messages="errors.name"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12>
                            <v-text-field
                                    label="Email"
                                    v-model="editedItem.email"
                                    required
                                    :error-messages="errors.email"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12>
                            <v-text-field
                                    label="Password"
                                    v-model="password"
                                    required
                                    hint="Optional"
                                    :type="show1 ? 'text' : 'password'"
                                    :append-icon="show1 ? 'visibility' : 'visibility_off'"
                                    :error-messages="errors.password"
                                    @click:append="show1 = !show1"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12>
                            <v-text-field
                                    label="Phone"
                                    v-model="editedItem.phone"
                                    required
                                    hint="Optional"
                                    :error-messages="errors.phone"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12>
                            <v-switch
                                    label="Active"
                                    v-model="editedItem.active"
                            ></v-switch>
                        </v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                <v-btn
                        color="primary"
                        :loading="loading"
                        :disabled="loading"
                        flat
                        @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'

export default {
    name: "edit",
    props: ['items', 'resellers', 'reseller'],
    data() {
        return {
            title: '',
            dialog: false,
            loading: false,
            show1: false,
            password: null,
            errors: [],
            editedItem: {},
            editedIndex: -1,
        }
    },
    mounted() {
    },
    methods: {
        close() {
            this.dialog = false
        },
        code() {
            return (new Date().getTime()).toString(36)
        },
        add() {
            this.title = 'Create New Account'
            this.dialog = true
            this.password = null
            this.errors = []
            this.editedItem = {
                code: this.code(),
            }
            if (this.reseller) {
                this.editedItem.reseller_id = this.reseller.id
            }
            this.editedIndex = -1;
        },
        edit(item) {
            this.editedItem = this.makeNonReactiveCopy(item)
            if (!this.editedItem.code) {
                this.editedItem.code = this.code()
            }
            this.editedIndex = this.items.indexOf(item)
            this.errors = []
            this.title = 'Edit Account ' + item.id
            this.dialog = true
            this.password = null
        },
        save() {
            this.loading = true
            let success = {
                data: {
                    message: `Account ${this.editedItem.id} saved successfully`
                }
            }

            this.editedItem = {
                ...this.editedItem,
                password: this.password
            }

            if (this.editedIndex > -1) {
                axios.put('/key-accounts/' + this.editedItem.id, this.editedItem)
                    .then((response) => {
                        this.loading = false
                        Object.assign(this.items[this.editedIndex], response.data.data)
                        this.close()
                        this.handleSuccess(success)
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            } else {
                axios.post('/key-accounts', this.editedItem)
                    .then((response) => {
                        this.loading = false
                        let item = response.data.data
                        this.items.unshift(item)
                        this.close()
                        this.handleSuccess(success)
                        this.$emit('new:account')
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        }
    }
}
</script>