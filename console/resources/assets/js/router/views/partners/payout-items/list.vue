<template>
    <v-card>
        <v-card>
            <v-card-title>
                <h1>
                    Payout Items
                    <br>
                    <v-btn-toggle v-if="showFilter" v-model="filters.ecosystem">
                        <v-btn
                            :value=0
                            small
                            outline
                            color="primary"
                        >Reseller Program
                        </v-btn>
                        <v-btn
                            :value=1
                            small
                            outline
                            color="primary"
                        >Ecosystem
                        </v-btn>
                    </v-btn-toggle>
                </h1>
                <v-menu v-if="selected.length" offset-y :nudge-bottom="5">
                    <v-btn slot="activator"
                           small
                           fab
                           dark
                           absolute
                           bottom
                           left
                           color="pink"
                    >
                        <v-icon>more_vert</v-icon>
                    </v-btn>
                    <v-list>
                        <v-list-tile @click="generatePayout()">
                            <v-list-tile-content>
                                <v-list-tile-title>Generate Payout</v-list-tile-title>
                            </v-list-tile-content>
                            <v-list-tile-action>
                                <v-icon>attach_money</v-icon>
                            </v-list-tile-action>
                        </v-list-tile>
                        <v-list-tile v-if="isCloudCartEmployee" @click="deletePayoutItems()">
                            <v-list-tile-content>
                                <v-list-tile-title>Delete Payout Items</v-list-tile-title>
                            </v-list-tile-content>
                            <v-list-tile-action>
                                <v-icon>delete</v-icon>
                            </v-list-tile-action>
                        </v-list-tile>
                    </v-list>
                </v-menu>
            </v-card-title>
        </v-card>

        <v-data-table
            :headers="headers"
            :items="filteredItems"
            :loading="loading"
            :pagination.sync="pagination"
            :select-all="false"
            v-model="selected"
            class="elevation-1"
            ref="dataTable"
            must-sort
        >
            <template slot="items" slot-scope="props">
                <tr>
                    <td>
                        <v-checkbox
                            v-if="resellerId && !props.item.payout_id"
                            primary
                            hide-details
                            v-model="props.selected"
                            :label="props.item.created_at | formatDateTime"
                        ></v-checkbox>
                        <span v-else>{{ props.item.created_at | formatDateTime }}</span>
                    </td>
                    <td v-if="props.item.site" nowrap>
                        <a v-if="isCloudCartEmployee || !props.item.expert_service_job_id"
                           @click="$refs.details.show(props.item.site)"
                        >
                            {{ props.item.site.primary_host }}
                        </a>
                        <strong v-else>{{ props.item.site.primary_host }}</strong>
                        <br>Plan: <strong>{{ props.item.site.plan }} {{ props.item.site.period }}</strong><br>
                        <span v-if="isCloudCartEmployee && props.item.reseller">
                            <a @click="$refs.resellerDetails.show(props.item.reseller)" class="subheading">
                                <b>{{ props.item.reseller.name }}</b>
                            </a>
                        </span>
                    </td>
                    <td v-else></td>
                    <td>
                        {{ props.item.invoice_item.description }}
                    </td>
                    <td nowrap>
                        <div v-if="!props.item.discount_amount">
                            <b>{{ props.item.sale_price | toCurrency(props.item.currency) }}</b>
                        </div>
                        <div v-else>
                            Original Price: {{ props.item.original_price | toCurrency(props.item.currency) }}
                            <br>
                            Discount: {{ props.item.discount_amount | toCurrency(props.item.currency) }} /
                            {{ props.item.discount_percent }} %
                            <br>
                            Sale Price: <b>{{ props.item.sale_price | toCurrency(props.item.currency) }}</b>
                        </div>
                    </td>
                    <td nowrap>
                        <b>{{ props.item.payout_amount | toCurrency(props.item.currency) }}</b> /
                        {{ props.item.percent }} %
                    </td>
                    <td v-if="isCloudCartEmployee">
                        <a href="javascript:" @click="$refs.showDialog.show({id: props.item.invoice_id})">
                            {{ props.item.invoice_number }}
                        </a>
                    </td>
                </tr>
            </template>

            <template slot="footer">
                <td colspan="100%">
                    Total Amount: <strong>{{ totalAmount | toCurrency }}</strong>
                    | Total Payout Amount: <strong>{{ totalPayout | toCurrency }}</strong>
                    <span v-if="isCloudCartEmployee">| CloudCart Revenue: <strong>{{ ccRevenue | toCurrency }}</strong></span>
                </td>
            </template>

        </v-data-table>

        <show ref="showDialog"/>
        <site-show-dialog ref="details"></site-show-dialog>
        <reseller-show-dialog ref="resellerDetails"></reseller-show-dialog>
    </v-card>
</template>

<script type="text/ecmascript-6">
import Show from '@views/billing/invoices/show'
import SiteShowDialog from "../../sites/show-dialog";
import axios from "axios";

export default {
    name: "payout-items-list",
    props: ['loading', 'items', 'reseller'],
    components: {
        ResellerShowDialog: () => import("../resellers/show-dialog.vue"),
        SiteShowDialog,
        Show,
    },
    data() {
        return {
            dialog: false,
            item: {},
            totalAmount: 0,
            totalPayout: 0,
            pagination: {
                rowsPerPage: 10,
                descending: true,
            },
            selected: [],
            filters: {
                ecosystem: 0
            },
        }
    },
    mounted() {
    },
    computed: {
        showFilter() {
            if (!this.isCloudCartEmployee) {
                const user = this.$store.getters['auth/user']
                return user.partner_scopes.reseller && user.partner_scopes.expert
            }

            if (this.isCloudCartEmployee && !this.reseller) {
                return true
            }

            if (this.reseller.expert && this.reseller.level > 0) {
                return true;
            }

            return false
        },
        filteredItems() {
            if (!this.showFilter) {
                return this.items
            }

            return this.items.filter((item) => {
                if (this.filters.ecosystem === 1) {
                    return item.expert_service_job_id;
                }

                return !item.expert_service_job_id;
            })
        },
        resellerId() {
            const user = this.$store.getters['auth/user'];
            if (!this.isCloudCartEmployee && !this.reseller) {
                if (user.key_account) {
                    return user.key_account.reseller_id
                }
            }

            if (this.reseller) {
                return this.reseller.id
            }

            return null
        },
        headers() {
            let headers = [
                {text: 'Created At', value: 'created_at'},
                {text: 'Site', value: 'created_at', sortable: false},
                {text: 'Description', value: 'description', sortable: false},
                {text: 'Sale Price (no VAT)', value: 'payout_price'},
                {text: 'Payout Amount (no VAT)', value: 'payout_amount'},
                {text: 'Invoice', value: 'invoice_number'},
            ]

            if (!this.isCloudCartEmployee) {
                delete headers[5]
            }

            return headers
        },
        ccRevenue() {
            return this.totalAmount - this.totalPayout
        },
    },
    watch: {
        filteredItems(val) {
            this.calculateTotal(val)
        },
    },
    methods: {
        calculateTotal(items) {
            this.totalAmount = 0
            this.totalPayout = 0
            items.forEach(item => {
                this.totalAmount = this.totalAmount + item.payout_price
                this.totalPayout = this.totalPayout + item.payout_amount
            })
        },
        generatePayout() {
            const result = this.selected.map(item => item.id);
            axios.post(`/resellers/${this.resellerId}/payout-items/generate-payout`, result)
                .then(response => {
                    // Object.assign(this.items, response.data.data);
                    this.selected = []
                    this.handleSuccess(response)
                    this.$emit('reload:items')
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        deletePayoutItems() {
            const result = this.selected.map(item => item.id);
            axios.post(`/resellers/${this.resellerId}/payout-items/destroy`, result)
                .then(response => {
                    // Object.assign(this.items, response.data.data);
                    this.selected = []
                    this.handleSuccess(response)
                    this.$emit('reload:items')
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    }
}
</script>
<style>
.v-input--selection-controls.v-input .v-label {
    font-size: 14px !important;
}
</style>