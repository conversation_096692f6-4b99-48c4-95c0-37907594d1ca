<template>
    <v-dialog
            v-model="dialog"
            max-width="500px"
            scrollable
            persistent
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h3 class="headline white--text mb-0">{{ title }}</h3>
            </v-card-title>

            <v-card-text>
                <v-container grid-list-md>
                    <v-layout wrap>
                        <v-flex xs12>
                            <div v-if="showInvoiceDetails">
                                <h2>Recipient</h2>
                                <br>
                                {{ invoiceDetails.company }}<br>
                                {{ invoiceDetails.country }}<br>
                                {{ invoiceDetails.address }}<br>
                                {{ invoiceDetails.vat }}<br>
                                {{ invoiceDetails.responsible }}<br><br>
                            </div>
                            <div v-else>
                                <h2>Issuer</h2>
                                <br>
                                {{ editedItem.reseller.company }}<br>
                                {{ editedItem.reseller.country_name }}<br>
                                {{ editedItem.reseller.address }}<br>
                                {{ editedItem.reseller.company_eik }}<br><br>
                            </div>
                            <div>
                                <h2>Invoice Details</h2>
                                <br>
                                <div>Reason: <strong>Payout #{{ editedItem.id }}</strong></div>
                                <div>Amount without VAT:
                                    <strong>
                                        {{ editedItem.payout_amount | toCurrency(editedItem.currency) }}
                                    </strong>
                                </div>
                                <div v-if="totalPayoutAmountWithVAT">
                                    Total Amount with VAT:
                                    <strong class="red--text">
                                        {{ totalPayoutAmountWithVAT | toCurrency(editedItem.reseller.payout_currency) }}
                                    </strong>
                                </div>
                                <br>
                            </div>
                            <v-divider></v-divider>
                            <vue-clip ref="vc"
                                      :options="options"
                                      :on-complete="fileCompleted"
                                      :on-total-progress="totalProgress"
                            >
                                <template slot="clip-uploader-action" slot-scope="params">
                                    <div :class="{'is-dragging': params.dragging}" class="upload-action">
                                        <div class="dz-message">
                                            <br><br>
                                            <h1>Upload PDF</h1>
                                            <div class="grey--text">Click or Drag and Drop file here upload</div>
                                        </div>
                                    </div>
                                </template>
                            </vue-clip>
                        </v-flex>
                        <v-progress-linear
                                v-model="valueProgress"
                                :active="valueProgress != 0"
                        ></v-progress-linear>
                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'

export default {
    name: "upload",
    props: ['items'],
    data() {
        return {
            url: '',
            title: '',
            dialog: false,
            loading: false,
            showInvoiceDetails: false,
            errors: [],
            editedItem: {},
            invoiceDetails: {},
            options: {
                url: () => {
                    return this.url
                },
                paramName: 'upload',
                uploadMultiple: false,
                maxFiles: 1,
                maxFilesize: 10, // MB
                parallelUploads: 1,
                acceptedFiles: {
                    extensions: ['application/pdf'],
                    message: 'You are uploading an invalid file. Only PDF is allowed!'
                },
                headers: {
                    Authorization: this.$store.getters['auth/user'].token
                }
            },
            valueProgress: 0
        }
    },
    mounted() {
        this.getInvoiceDetails()
    },
    computed: {
        totalPayoutAmountWithVAT() {
            if (!this.editedItem.reseller.include_vat) {
                return null
            }

            return this.convertCurrency(
                this.editedItem.payout_amount * 1.2,
                this.editedItem.currency,
                this.editedItem.reseller.payout_currency
            )
        },
    },
    methods: {
        close() {
            this.dialog = false
        },
        getInvoiceDetails() {
            this.loading = true
            axios.get('/resellers/cloudcart-invoice-details')
                .then(response => {
                    this.loading = false
                    this.invoiceDetails = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        fileCompleted(file, status, xhr) {
            this.valueProgress = 0
            this.$refs.vc.removeFile(file);
            if (file.errorMessage.length) {
                let error = file.errorMessage
                this.handleError(error)
            } else {
                let success = {
                    data: {
                        message: `File uploaded successfully`
                    }
                }
                this.handleSuccess(success)
                if (this.editedIndex) {
                    Object.assign(this.items[this.editedIndex], JSON.parse(xhr.response));
                } else {
                    this.$emit('upload:completed')
                }
                this.close()
            }
        },
        totalProgress(progress, totalBytes, bytesSent) {
            // console.log(progress, totalBytes, bytesSent)
            this.valueProgress = progress
        },
        addInvoice(item) {
            this.valueProgress = 0
            this.showInvoiceDetails = true
            this.editedItem = this.makeNonReactiveCopy(item)
            this.editedIndex = this.items.indexOf(item)
            this.errors = []
            this.title = 'Upload Invoice'
            this.dialog = true
            this.url = `/api/resellers/payouts/${item.id}/upload-invoice`
        },
        addReceipt(item) {
            if (!this.isCloudCartEmployee) {
                this.handleError('Only CloudCart employee can upload a payout receipt')
                return
            }
            this.valueProgress = 0
            this.showInvoiceDetails = false
            this.editedItem = this.makeNonReactiveCopy(item)
            if (this.items) {
                this.editedIndex = this.items.indexOf(item);
            }
            this.errors = []
            this.title = 'Upload Payout Receipt'
            this.dialog = true
            this.url = `/api/resellers/payouts/${item.id}/upload-receipt`
        },
    }
}
</script>