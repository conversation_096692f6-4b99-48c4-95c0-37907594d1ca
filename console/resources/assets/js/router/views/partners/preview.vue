<template>
    <v-dialog v-model="dialog" scrollable max-width="800px">
        <v-card>
            <v-card-title>
                <span class="headline">{{ previewItem.title }}</span>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Close</v-btn>
            </v-card-title>
            <v-card-text>
                <v-container grid-list-md>
                    <v-layout wrap>
                        <v-flex xs12 v-highlightjs="previewItem.content" :dark="$root.dark">
                            <code class="html"></code>
                        </v-flex>
                        <v-flex xs12 v-html="previewItem.content"></v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
    export default {
        name: "preview",
        data() {
            return {
                dialog: false,
                previewItem: {},
            }
        },
        mounted() {
        },
        methods: {
            viewItem(item) {
                this.dialog = true
                this.previewItem = {
                    title: item.title,
                    content: this.iframe(item.url),
                }
            },
            iframe(value) {
                return `<iframe src="${value}?iframe" style="border: none; width: 100%; min-height: 555px"></iframe>`
            },
            close() {
                this.dialog = false
            }
        },
    }
</script>
