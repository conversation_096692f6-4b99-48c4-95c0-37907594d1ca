<template>
    <v-dialog
        v-model="dialog"
        max-width="500px"
        scrollable
        persistent
    >
        <v-form ref="form" v-model="valid" lazy-validation>
            <v-card>
                <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                    <h3 class="headline white--text mb-0">New Reseller Invoice</h3>
                </v-card-title>

                <v-card-text>
                    <v-container grid-list-md>
                        <v-layout wrap>

                            <v-flex xs12 sm6>
                                <v-menu
                                    ref="fromDate"
                                    lazy
                                    :close-on-content-click="false"
                                    v-model="fromDate"
                                    transition="scale-transition"
                                    offset-y
                                    full-width
                                    :nudge-right="40"
                                    min-width="290px"
                                    :return-value.sync="from_date"
                                >
                                    <v-text-field
                                        slot="activator"
                                        label="From Date"
                                        v-model="from_date"
                                        prepend-icon="event"
                                        readonly
                                        clearable
                                        required
                                        :error-messages="errors.from_date"
                                    ></v-text-field>
                                    <v-date-picker v-model="from_date"
                                                   :allowed-dates="pastDates"
                                                   @input="$refs.fromDate.save(from_date)">
                                    </v-date-picker>
                                </v-menu>
                            </v-flex>


                            <v-flex xs12 sm6>
                                <v-menu
                                    ref="toDate"
                                    lazy
                                    :close-on-content-click="false"
                                    v-model="toDate"
                                    transition="scale-transition"
                                    offset-y
                                    full-width
                                    :nudge-right="40"
                                    min-width="290px"
                                    :return-value.sync="to_date"
                                >
                                    <v-text-field
                                        slot="activator"
                                        label="To Date"
                                        v-model="to_date"
                                        prepend-icon="event"
                                        readonly
                                        clearable
                                        required
                                        :error-messages="errors.to_date"
                                    ></v-text-field>
                                    <v-date-picker v-model="to_date"
                                                   :allowed-dates="pastDates"
                                                   @input="$refs.toDate.save(to_date)">
                                    </v-date-picker>
                                </v-menu>
                            </v-flex>

                            <v-flex v-if="reseller" xs12>
                                <v-card>
                                    <v-btn
                                        small
                                        absolute
                                        dark
                                        fab
                                        bottom
                                        right
                                        color="teal"
                                        @click="$refs.invoicingDialog.newInvoicing(reseller.invoicing)"
                                    >
                                        <v-icon>edit</v-icon>
                                    </v-btn>
                                </v-card>
                                <v-card>
                                    <v-card-text>
                                        <div><strong>{{ reseller.invoicing.company }}</strong></div>
                                        <div>{{ reseller.invoicing.company_id }}</div>
                                        <div>{{ reseller.invoicing.vat }}</div>
                                        <div>{{ reseller.invoicing.country_name }}</div>
                                        <div>{{ reseller.invoicing.city }}</div>
                                        <div>{{ reseller.invoicing.address }}</div>
                                        <div>{{ reseller.invoicing.name }}</div>
                                    </v-card-text>
                                </v-card>
                            </v-flex>

                        </v-layout>
                    </v-container>
                </v-card-text>

                <v-divider></v-divider>

                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                    <v-btn
                        color="primary"
                        :loading="loading"
                        :disabled="loading"
                        flat
                        @click.native="save"
                    >Next
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-form>

        <invoicing :site="reseller" ref="invoicingDialog"></invoicing>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import moment from 'moment'
import store from '@state/store'
import Invoicing from '@views/sites/invoicing'

export default {
    name: "credits-invoice-period",
    props: ['reseller'],
    components: {
        Invoicing,
    },
    data() {
        return {
            valid: true,
            loading: false,
            dialog: false,
            fromDate: false,
            toDate: false,
            from_date: null,
            to_date: null,
            errors: [],
        }
    },
    mounted() {
    },
    computed: {},
    watch: {},
    methods: {
        pastDates(date) {
            return moment(date).isBefore(moment())
        },
        show() {
            this.to_date = moment().format('YYYY-MM-DD')
            this.from_date = moment().subtract(1, 'month').format('YYYY-MM-DD')
            this.dialog = true
            this.valid = true
            this.loading = false
            this.errors = []
        },
        close() {
            this.dialog = false
        },
        createRequest() {
            return {
                reseller_id: this.reseller.id,
                from_date: this.from_date,
                to_date: this.to_date,
            }
        },
        save() {
            if (this.$refs.form.validate()) {
                this.loading = true
                const request = this.createRequest()
                axios.post('/resellers/credits-for-invoice', request)
                    .then((response) => {
                        if (!response.data.data.length) {
                            this.handleError('No not invoiced credits found!')
                        } else {
                            this.loading = false
                            this.close()
                            setTimeout(() => this.$emit('credits:for-invoice', {
                                items: response.data.data,
                                reseller: this.reseller,
                                from_date: this.from_date,
                                to_date: this.to_date,
                            }), 500)
                        }
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        },
    }
}
</script>