<template>
    <v-dialog
        v-model="dialog"
        fullscreen
        hide-overlay
        transition="dialog-bottom-transition"
        scrollable
    >
        <v-card tile>
            <v-toolbar card dark :color="$root.dark ? 'grey darken-4' : 'primary'">
                <v-toolbar-title>Not invoiced credits from {{ from_date }} to {{ to_date }} for reseller
                    {{ reseller.name }}
                </v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon dark @click.native="dialog = false">
                    <v-icon>close</v-icon>
                </v-btn>
            </v-toolbar>

            <v-card-text class="transparent">
                <credits-list :items="items" :loading="loading" :hide-search="true"></credits-list>
            </v-card-text>

            <div style="flex: 1 1 auto;" class="transparent"></div>

            <v-footer class="pa-4">
                <v-spacer></v-spacer>
                <h2>New invoice for {{ items.length }} credits. Total amount: {{ totalCredit | toCurrency('EUR') }}</h2>
                <v-btn flat color="primary"
                       :loading="loading"
                       :disabled="loading"
                       @click.native="save"
                >Save
                </v-btn>
                <v-spacer></v-spacer>
            </v-footer>

        </v-card>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import CreditsList from "./list";
import axios from "axios";

export default {
    name: "credits-invoice-details",
    props: [],
    components: {
        CreditsList,
    },
    data() {
        return {
            dialog: false,
            loading: false,
            reseller: {},
            items: [],
            from_date: null,
            to_date: null,
            totalCredit: 0,
        }
    },
    methods: {
        show(data) {
            this.items = data.items
            this.reseller = data.reseller
            this.from_date = data.from_date
            this.to_date = data.to_date
            this.loading = false
            this.dialog = true
            this.calculateTotal(this.items)
        },
        calculateTotal(items) {
            this.totalCredit = 0
            items.forEach(item => {
                let credit = this.convertCurrency(item.amount_credit, item.currency, 'EUR')
                this.totalCredit = this.totalCredit + credit
            })
        },
        close() {
            this.dialog = false
        },
        createRequest() {
            return {
                reseller_id: this.reseller.id,
                from_date: this.from_date,
                to_date: this.to_date,
            }
        },
        save() {
            this.loading = true
            const request = this.createRequest()
            axios.post('/resellers/credits-create-invoice', request)
                .then((response) => {
                    this.handleSuccess(response)
                    this.close()
                    this.$emit('credits:invoice-created')
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    }
}
</script>