<template>
  <layout-default>
    <v-card>
      <v-card-title>
        <v-list two-line dense class="transparent">
          <v-list-tile>
            <v-list-tile-content>
              <v-list-tile-title><h2>Slow Queries</h2></v-list-tile-title>
              <v-list-tile-sub-title class="caption">{{
                platform
              }}</v-list-tile-sub-title>
            </v-list-tile-content>
          </v-list-tile>
        </v-list>
        <v-btn small color="error" :loading="loading" @click="clearItems"
          >Delete All
        </v-btn>
        <v-spacer></v-spacer>
        <v-text-field
          append-icon="search"
          label="Search"
          single-line
          v-model="search"
          clearable
        ></v-text-field>
      </v-card-title>
    </v-card>

    <v-data-table
      :headers="headers"
      :items="items"
      :pagination.sync="pagination"
      :total-items="totalItems"
      :loading="loading"
      :rows-per-page-items="[5, 10, 25, 50]"
      class="elevation-1"
    >
      <template slot="items" slot-scope="props">
        <tr>
          <td>
            {{ displayText(props.item.id, 8) }}
          </td>
          <td>{{ props.item.created_at | formatDateTime }}</td>
          <td>{{ props.item.site_id }}</td>
          <td style="width: 200px !important">
            <a
              @click="
                $refs.jsonDialog.viewData(
                  {
                    _id: props.item.id,
                    query: props.item.sql,
                    previousQuery: props.item.prev,
                  },
                  'SQL Query'
                )
              "
            >
              {{ displayMessage(props.item.sql) }}
            </a>
          </td>
          <td>
            <a @click="$refs.jsonDialog.viewData(props.item.debug, 'Backtrace')">
              View
            </a>
          </td>
          <td>{{ props.item.time.toFixed(2) }}</td>
          <td>{{ props.item.connection }}</td>
          <td>{{ props.item.url }}<br />{{ props.item.hostname }}</td>
        </tr>
      </template>

      <v-alert slot="no-results" :value="true" color="error" icon="warning">
        Your search for "{{ search }}" found no results.
      </v-alert>
    </v-data-table>
    <json-dialog ref="jsonDialog"></json-dialog>
  </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import JsonDialog from "../../../components/JsonDialog"
import _ from "lodash"

const CancelToken = axios.CancelToken;
let cancel;

export default {
    name: 'queries',
    props: ['platform'],
    components: {
        JsonDialog,
        LayoutDefault,
    },
    data() {
        return {
            search: '',
            totalItems: 0,
            items: [],
            loading: false,
            pagination: {
                descending: true,
                page: 1,
                rowsPerPage: 25,
                sortBy: "id",
                totalItems: 0,
            },
            headers: [
                {text: 'ID', value: 'id'},
                {text: 'Created At', value: 'created_at'},
                {text: 'Site', value: 'site_id'},
                {text: 'Query', value: 'sql'},
                {text: 'Backtrace', value: 'debug'},
                {text: 'Time (sec)', value: 'time'},
                {text: 'Connection', value: 'connection'},
                {text: 'URL / POD', value: 'url'},
            ],
        }
    },
    watch: {
        search(val) {
            if (!val || val.length >= 3) {
                if (cancel != undefined) {
                    cancel();
                }
                this.getItems()
            }
        },
        pagination: {
            handler(newVal, oldVal) {
                delete newVal.totalItems
                delete oldVal.totalItems
                if (!_.isEqual(newVal, oldVal)) {
                    this.getItems()
                }
            },
            deep: false
        },
    },
    mounted() {
        this.getItems()
    },
    methods: {
        displayMessage: msg => {
            const length = 50
            if (msg.length > length) {
                return msg.substring(0, length) + ' ...'
            }
            return msg
        },
        getItems() {
            this.loading = true
            const params = {
                cancelToken: new CancelToken(function executor(c) {
                    cancel = c;
                }),
                params: {
                    search: this.search,
                    ...this.pagination,
                }
            }

            axios.get('/queries/' + this.platform, params)
                .then(response => {
                    this.loading = false
                    this.items = response.data.data
                    this.totalItems = response.data.meta.total
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        displayText: (text, length) => {
            if (text && text.length > length) {
                return text.substring(0, length) + ' ...'
            }
            return text
        },
        clearItems() {
            this.loading = true
            axios.post(`/queries/${this.platform}/clear`)
                .then(response => {
                    this.handleSuccess(response)
                    this.loading = false
                    this.items = []
                    this.totalItems = 0
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    },
}
</script>
