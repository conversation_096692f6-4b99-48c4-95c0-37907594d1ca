<template>
    <v-dialog
        v-model="dialog"
        fullscreen
        hide-overlay
        transition="dialog-bottom-transition"
        scrollable
    >
        <v-card tile>
            <v-toolbar card dark :color="$root.dark ? 'grey darken-4' : 'primary'">
                <v-toolbar-title>User Statistics for {{ item.name_id }}</v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon dark @click.native="dialog = false">
                    <v-icon>close</v-icon>
                </v-btn>
            </v-toolbar>

            <v-card-text class="transparent">
                <v-data-table
                    :headers="headers"
                    :items="items"
                    :pagination.sync="pagination"
                    :loading="loading"
                    class="elevation-1"
                    must-sort
                >
                    <template slot="items" slot-scope="props">
                        <td>
                            {{ props.item.USER }}
                            <br>
                            <a v-if="props.item.site" @click="$refs.details.show(props.item.site)">
                                <b>{{ props.item.site.primary_host }}</b>
                            </a>
                        </td>
                        <td>{{ props.item.TOTAL_CONNECTIONS }}</td>
                        <td>{{ props.item.CONCURRENT_CONNECTIONS }}</td>
                        <td>{{ props.item.CONNECTED_TIME }}</td>
                        <td>{{ props.item.BUSY_TIME }}</td>
                        <td>{{ props.item.CPU_TIME }}</td>
                        <td>{{ props.item.BYTES_RECEIVED }}</td>
                        <td>{{ props.item.BYTES_SENT }}</td>
                        <td>{{ props.item.BINLOG_BYTES_WRITTEN }}</td>
                        <td>{{ props.item.ROWS_READ }}</td>
                        <td>{{ props.item.ROWS_SENT }}</td>
                        <td>{{ props.item.ROWS_DELETED }}</td>
                        <td>{{ props.item.ROWS_INSERTED }}</td>
                        <td>{{ props.item.ROWS_UPDATED }}</td>
                        <td>{{ props.item.SELECT_COMMANDS }}</td>
                        <td>{{ props.item.UPDATE_COMMANDS }}</td>
                        <td>{{ props.item.OTHER_COMMANDS }}</td>
                        <td>{{ props.item.COMMIT_TRANSACTIONS }}</td>
                        <td>{{ props.item.ROLLBACK_TRANSACTIONS }}</td>
                        <td>{{ props.item.DENIED_CONNECTIONS }}</td>
                        <td>{{ props.item.LOST_CONNECTIONS }}</td>
                        <td>{{ props.item.ACCESS_DENIED }}</td>
                        <td>{{ props.item.EMPTY_QUERIES }}</td>
                        <td>{{ props.item.TOTAL_SSL_CONNECTIONS }}</td>
                        <td>{{ props.item.MAX_STATEMENT_TIME_EXCEEDED }}</td>
                    </template>
                </v-data-table>
            </v-card-text>

            <div style="flex: 1 1 auto;" class="transparent"></div>
        </v-card>

        <site-show-dialog ref="details"></site-show-dialog>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import MerchantsList from '@views/merchants/list'
import SitesList from '@views/sites/list'
import SiteShowDialog from "../sites/show-dialog";

export default {
    name: "sql-db-user-statistics",
    components: {
        SiteShowDialog,
        MerchantsList,
        SitesList,
    },
    data() {
        return {
            dialog: false,
            loading: false,
            item: {},
            items: [],
            headers: [
                {text: 'USER', value: 'USER'},
                {text: 'TOTAL_CONNECTIONS', value: 'TOTAL_CONNECTIONS'},
                {text: 'CONCURRENT_CONNECTIONS', value: 'CONCURRENT_CONNECTIONS'},
                {text: 'CONNECTED_TIME', value: 'CONNECTED_TIME'},
                {text: 'BUSY_TIME', value: 'BUSY_TIME'},
                {text: 'CPU_TIME', value: 'CPU_TIME'},
                {text: 'BYTES_RECEIVED', value: 'BYTES_RECEIVED'},
                {text: 'BYTES_SENT', value: 'BYTES_SENT'},
                {text: 'BINLOG_BYTES_WRITTEN', value: 'BINLOG_BYTES_WRITTEN'},
                {text: 'ROWS_READ', value: 'ROWS_READ'},
                {text: 'ROWS_SENT', value: 'ROWS_SENT'},
                {text: 'ROWS_DELETED', value: 'ROWS_DELETED'},
                {text: 'ROWS_INSERTED', value: 'ROWS_INSERTED'},
                {text: 'ROWS_UPDATED', value: 'ROWS_UPDATED'},
                {text: 'SELECT_COMMANDS', value: 'SELECT_COMMANDS'},
                {text: 'UPDATE_COMMANDS', value: 'UPDATE_COMMANDS'},
                {text: 'OTHER_COMMANDS', value: 'OTHER_COMMANDS'},
                {text: 'COMMIT_TRANSACTIONS', value: 'COMMIT_TRANSACTIONS'},
                {text: 'ROLLBACK_TRANSACTIONS', value: 'ROLLBACK_TRANSACTIONS'},
                {text: 'DENIED_CONNECTIONS', value: 'DENIED_CONNECTIONS'},
                {text: 'LOST_CONNECTIONS', value: 'LOST_CONNECTIONS'},
                {text: 'ACCESS_DENIED', value: 'ACCESS_DENIED'},
                {text: 'EMPTY_QUERIES', value: 'EMPTY_QUERIES'},
                {text: 'TOTAL_SSL_CONNECTIONS', value: 'TOTAL_SSL_CONNECTIONS'},
                {text: 'MAX_STATEMENT_TIME_EXCEEDED', value: 'MAX_STATEMENT_TIME_EXCEEDED'},
            ],
            pagination: {
                rowsPerPage: 25,
                descending: true,
                sortBy: 'CPU_TIME',
            },
        }
    },
    methods: {
        show(item) {
            this.items = []
            this.item = item
            this.dialog = true
            this.loading = true
            axios.get(`/sql-dbs/stats/${item.id}/users`)
                .then(response => {
                    this.loading = false
                    this.items = response.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        }
    }
}
</script>