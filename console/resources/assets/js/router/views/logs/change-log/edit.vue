<template>
    <v-dialog
        v-model="dialog"
        max-width="500px"
        scrollable
        persistent
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h3 class="headline white--text mb-0">{{editedIndex.id ? 'Edit' : 'Add'}} Log: {{ editedItem.log }}</h3>
            </v-card-title>

            <v-tabs
                v-model="tab"
                dark
                :color="$root.dark ? 'grey darken-4' : 'primary'"
                slider-color="yellow"
            >
                <v-tab
                    v-for="lang in languages"
                    :key="lang"
                > {{ lang }}
                </v-tab>

                <v-tab-item
                    v-for="lang in languages"
                    :key="lang"
                    lazy
                >
                    <v-card-text>
                        <v-container grid-list-md style="height: 250px; overflow: auto">
                            <v-layout wrap v-if="editedItem[lang]">
                                <v-flex xs12>
                                    <v-text-field
                                        label="Title"
                                        v-model="editedItem[lang].title"
                                        counter
                                        :error-messages="errors[lang + '.title']"
                                    ></v-text-field>
                                </v-flex>
                                <template v-for="(field, fieldIndex) in editedItem[lang].fields">
                                    <v-flex xs12>
                                        <v-text-field
                                            :label="`Field ${fieldIndex + 1} name`"
                                            v-model="editedItem[lang].fields[fieldIndex].name"
                                            counter
                                            :error-messages="errors[lang + '.fields.' + fieldIndex + '.name']"
                                        ></v-text-field>
                                    </v-flex>
                                    <v-flex xs12>
                                        <v-textarea
                                            :label="`Field ${fieldIndex + 1} value`"
                                            v-model="editedItem[lang].fields[fieldIndex].value"
                                            counter
                                            :error-messages="errors[lang + '.fields.' + fieldIndex + '.value']"
                                        ></v-textarea>
                                    </v-flex>
                                </template>
                                <v-flex xs12>
                                    <v-textarea
                                        label="Footer text"
                                        v-model="editedItem[lang].footer.text"
                                        counter
                                        :error-messages="errors[lang + '.footer.text']"
                                    ></v-textarea>
                                </v-flex>
                            </v-layout>
                        </v-container>
                    </v-card-text>
                </v-tab-item>
            </v-tabs>

            <v-divider></v-divider>

            <v-card-text>
                <v-container grid-list-md>
                    <v-layout wrap>
                        <v-flex xs1>
                            <v-text-field
                                type="color"
                                label=""
                                v-model="editedItem.color"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs11>
                            <v-text-field
                                label="Color"
                                v-model="editedItem.color"
                                :error-messages="errors.color"
                            ></v-text-field>
                        </v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                <v-btn
                    color="primary"
                    :loading="loading"
                    :disabled="loading"
                    flat
                    @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'

export default {
    name: "edit",
    props: ['items', 'addNew'],
    components: {},
    data() {
        return {
            title: '',
            tab: 0,
            dialog: false,
            loading: false,
            errors: [],
            categories: [],
            editedItem: {},
            editedIndex: -1,
        }
    },
    mounted() {

    },
    computed: {
        languages() {
            return this.$store.state.languages
        },
        priceWithVat() {
            if (this.editedItem === undefined) {
                return ''
            }

            let price = this.editedItem.price_input ? this.editedItem.price_input * 100 * 1.2 : 0
            let priceFormatted = this.$options.filters.toCurrency(price)

            return 'Price with VAT: ' + priceFormatted
        },
    },
    watch: {
        errors(val) {
            const errors = Object.keys(val)
            for (const field of errors) {
                if (field.includes('en')) {
                    return this.tab = 0
                }
                if (field.includes('bg')) {
                    return this.tab = 1
                }
            }
        },
    },
    methods: {
        close() {
            this.dialog = false
        },
        dechex(colorInt) {
            // Extracting the RGB components from the integer
            const red = (colorInt >> 16) & 255;
            const green = (colorInt >> 8) & 255;
            const blue = colorInt & 255;

            // Convert each component to a hexadecimal string and concatenate them
            return '#' +
                red.toString(16).padStart(2, '0') +
                green.toString(16).padStart(2, '0') +
                blue.toString(16).padStart(2, '0');
        },
        hexdec(hexColor) {
            // Извличане на стойностите на червено, зелено и синьо от шестнадесетичния цвят
            const red = parseInt(hexColor.slice(1, 3), 16);
            const green = parseInt(hexColor.slice(3, 5), 16);
            const blue = parseInt(hexColor.slice(5, 7), 16);

            // Обединяване на стойностите в едно число
            const colorInt = (red << 16) + (green << 8) + blue;

            return colorInt;
        },
        edit(item) {
            this.editedItem = this.makeNonReactiveCopy(item)
            this.editedIndex = this.items.indexOf(item)
            this.errors = []
            this.dialog = true
            this.loadTranslations()
            this.editedItem.color = this.dechex(this.editedItem.color)
        },
        add() {
            const fields = this.languages.reduce((acc, key) => {
                acc[key] = {
                    title: '',
                    footer: {text: ''},
                    fields: [
                        {name: '', value: ''},
                        {name: '', value: ''}
                    ],
                }
                return acc;
            }, {})

            this.edit({
                id: '',
                color: '',
                ...fields
            })
        },
        loadTranslations() {
            const emptyFields = (() => {
                for(let index in this.languages) {
                    if (this.editedItem[this.languages[index]] && this.editedItem[this.languages[index]].fields?.length) {
                        return this.makeNonReactiveCopy(this.editedItem[this.languages[index]].fields).map(() => {
                            return {name: '', value: ''}
                        });
                    }
                }
            })();
            this.languages.map(lang => {
                if (!this.editedItem[lang]) {
                    this.editedItem[lang] = {
                        title: '',
                        footer: {text: ''},
                        fields: emptyFields,
                    }
                }
            })
        },
        save() {
            this.loading = true

            const body = Object.keys(this.editedItem).reduce((acc, key) => {
                if ((this.languages.includes(key) && this.editedItem[key].title) || key === 'color') {
                    acc[key] = key === 'color' ? this.hexdec(this.editedItem[key]) : this.editedItem[key];
                }
                return acc;
            }, {})

            if(this.editedItem.id) {
                axios.put('/logs/google-cloud/change-logs/' + this.editedItem.id, body)
                    .then((response) => {
                        this.loading = false
                        Object.assign(this.items[this.editedIndex], response.data.data)
                        this.close()
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            } else {
                axios.post('/logs/google-cloud/change-logs', body)
                    .then((response) => {
                        this.loading = false
                        if(this.addNew) {
                            this.addNew();
                        }
                        this.close()
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        }
    }
}
</script>

<style>
input[type="color"] {
    padding: 0 !important;
}
</style>
