<template>
    <v-dialog
        v-model="dialog"
        max-width="500px"
        scrollable
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h2 class="white--text mb-0">{{ title }}</h2>
            </v-card-title>

            <v-card-text>
                <v-flex xs12>
                    <v-text-field
                        v-model="editedItem.name"
                        label="Name"
                        :error-messages="errors.name"
                    ></v-text-field>
                </v-flex>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                <v-btn
                    color="primary"
                    :loading="loading"
                    :disabled="loading"
                    flat
                    @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import store from '@state/store'

export default {
    name: "edit-tag",
    props: ['items'],
    data() {
        return {
            title: '',
            dialog: false,
            loading: false,
            tab: 0,
            errors: [],
            editedIndex: -1,
            editedItem: {
                name: {}
            },
        }
    },
    created() {
        this.clear()
    },
    mounted() {
    },
    computed: {
    },
    watch: {
    },
    methods: {
        clear() {
            this.errors = []
            this.editedIndex = -1
            this.editedItem = {}
        },
        editItem(item) {
            this.clear()
            this.dialog = true
            this.title = 'Edit Service Tag #' + item.id
            this.editedIndex = this.items.indexOf(item)
            this.editedItem = this.makeNonReactiveCopy(item)
        },
        addItem() {
            this.dialog = true
            this.clear()
            this.title = 'New Service Tag'
        },
        close() {
            this.dialog = false
        },
        save() {
            this.loading = true
            if (this.editedIndex > -1) {
                axios.put('/service-tags/' + this.editedItem.id, this.editedItem)
                    .then((response) => {
                        this.loading = false
                        Object.assign(this.items[this.editedIndex], response.data.data)
                        this.close()
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            } else {
                axios.post('/service-tags', this.editedItem)
                    .then((response) => {
                        this.loading = false
                        this.items.unshift(response.data.data)
                        this.close()
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        }
    }
}
</script>