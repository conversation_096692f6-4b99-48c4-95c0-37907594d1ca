<template>
    <v-dialog
            v-model="dialog"
            max-width="800px"
            scrollable
            persistent
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h2 class="white--text mb-0">{{ title }}</h2>
            </v-card-title>

            <v-card-text class="pa-0">
                <v-tabs
                        v-model="tab"
                        dark
                        :color="$root.dark ? 'grey darken-4' : 'primary'"
                        slider-color="yellow"
                >
                    <v-tab
                            v-for="lang in languages"
                            :key="lang"
                    > {{ lang }}
                    </v-tab>
                    <v-tab-item
                            v-for="lang in languages"
                            :key="lang"
                            lazy
                    >
                        <v-card-text class="pa-0">
                            <v-container grid-list-md>
                                <v-layout wrap>
                                    <v-flex xs12>
                                        <v-text-field
                                                label="Name"
                                                v-model="editedItem.name[lang]"
                                                :error-messages="errors['name.' + lang]"
                                        ></v-text-field>
                                    </v-flex>
                                    <v-flex xs12>
                                        <v-textarea
                                                outline
                                                v-model="editedItem.description[lang]"
                                                label="Description"
                                                :error-messages="errors['description.' + lang]"
                                        ></v-textarea>
                                    </v-flex>
                                </v-layout>
                            </v-container>
                        </v-card-text>
                    </v-tab-item>
                </v-tabs>

                <v-container grid-list-md class="pt-0">
                    <v-layout wrap>
                        <v-flex xs12 sm6>
                            <v-autocomplete
                                    :items="parents"
                                    v-model="parentCategory"
                                    label="Category"
                                    item-text="name_translated"
                                    item-value="id"
                                    clearable
                            ></v-autocomplete>
                        </v-flex>
                        <v-flex xs12 sm6>
                            <v-autocomplete
                                    :items="children"
                                    v-model="editedItem.group_id"
                                    label="Sub Category"
                                    item-text="name_translated"
                                    item-value="id"
                                    clearable
                                    :error-messages="errors.group_id"
                            ></v-autocomplete>
                        </v-flex>
                        <v-flex xs12 sm6>
                            <v-text-field
                                    v-model="editedItem.price_input"
                                    label="Price without VAT"
                                    :suffix="currency"
                                    :error-messages="errors.price_input"
                                    :hint="priceWithVat"
                                    persistent-hint
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12 sm4>
                            <v-autocomplete
                                    :items="billingCycle"
                                    v-model="editedItem.billing_cycle"
                                    label="Billing Cycle"
                                    item-text="text"
                                    item-value="value"
                            ></v-autocomplete>
                        </v-flex>
                        <v-flex xs12 sm2>
                            <v-text-field
                                    v-model="editedItem.sort_order"
                                    label="Sort Order"
                                    :error-messages="errors.sort_order"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12 sm6>
                            <v-switch
                                    label="Public"
                                    v-model="editedItem.public"
                            ></v-switch>
                        </v-flex>
                        <v-flex xs12 sm6>
                            <v-switch
                                    label="Recommend"
                                    v-model="editedItem.recommend"
                            ></v-switch>
                        </v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                <v-btn
                        color="primary"
                        :loading="loading"
                        :disabled="loading"
                        flat
                        @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import store from '@state/store'
import _ from "lodash"

export default {
    name: "edit",
    props: ['items'],
    components: {},
    data() {
        return {
            title: '',
            dialog: false,
            loading: false,
            tab: 0,
            parentCategory: null,
            parents: [],
            children: [],
            errors: [],
            editedIndex: -1,
            editedItem: {
                name: {},
                description: {},
            },
        }
    },
    created() {
        this.clear()
    },
    mounted() {
        this.getParents()
    },
    computed: {
        currency() {
            return store.getters.currency
        },
        languages() {
            return this.$store.state.languages
        },
        priceWithVat() {
            if (this.editedItem === undefined) {
                return ''
            }

            let price = this.editedItem.price_input ? this.editedItem.price_input * 100 * 1.2 : 0
            let priceFormatted = this.$options.filters.toCurrency(price)

            return 'Price with VAT: ' + priceFormatted
        },
    },
    watch: {
        parentCategory(val) {
            if (!val) {
                this.editedItem.group_id = null;
            }
            this.getChildren()
        },
        errors(val) {
            const errors = Object.keys(val)
            for (const field of errors) {
                if (field.includes('en')) {
                    return this.tab = 0
                }
                if (field.includes('bg')) {
                    return this.tab = 1
                }
            }
        },
    },
    methods: {
        getParents() {
            axios.get('/service-groups/parents')
                .then(response => {
                    this.parents = response.data.data
                    this.parents = _.sortBy(this.parents, 'name_translated')
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getChildren() {
            axios.get(`/service-groups/${this.parentCategory}/children`)
                .then(response => {
                    this.children = response.data.data
                    this.children = _.sortBy(this.children, 'name_translated')
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        clear() {
            this.tab = 0
            this.errors = []
            this.parentCategory = null
            this.editedIndex = -1
            this.editedItem = {
                name: {},
                description: {},
            }
        },
        editItem(item) {
            this.clear()
            this.title = 'Edit Service #' + item.id
            this.editedIndex = this.items.indexOf(item)
            this.editedItem = this.makeNonReactiveCopy(item)
            this.parentCategory = item.group ? item.group.parent_id : null
            if (!this.parentCategory) {
                this.editedItem.group_id = null;
            }
            this.dialog = true
        },
        addItem() {
            this.dialog = true
            this.title = 'New Service'
            this.clear()
        },
        close() {
            this.dialog = false
        },
        save() {
            this.loading = true
            if (this.editedIndex > -1) {
                axios.put('/services/' + this.editedItem.id, this.editedItem)
                    .then((response) => {
                        this.loading = false
                        Object.assign(this.items[this.editedIndex], response.data.data)
                        this.close()
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            } else {
                axios.post('/services', this.editedItem)
                    .then((response) => {
                        this.loading = false
                        this.items.unshift(response.data.data)
                        this.close()
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        },
    }
}
</script>
