<template>
    <v-dialog
            v-model="dialog"
            max-width="500px"
            scrollable
            persistent
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h3 class="headline white--text mb-0">{{ title }}</h3>
            </v-card-title>

            <v-tabs
                    v-model="tab"
                    dark
                    :color="$root.dark ? 'grey darken-4' : 'primary'"
                    slider-color="yellow"
            >
                <v-tab
                        v-for="lang in languages"
                        :key="lang"
                > {{ lang }}
                </v-tab>
                <v-tab-item
                        v-for="lang in languages"
                        :key="lang"
                        lazy
                >
                    <v-card-text>
                        <v-container grid-list-md>
                            <v-layout wrap>
                                <v-flex xs12>
                                    <v-text-field
                                            label="Name"
                                            v-model="editedItem.name[lang]"
                                            :error-messages="errors['name.' + lang]"
                                    ></v-text-field>
                                </v-flex>
                            </v-layout>
                        </v-container>
                    </v-card-text>
                </v-tab-item>
            </v-tabs>

            <v-card-text>
                <v-container grid-list-md>
                    <v-layout wrap>
                        <v-flex xs12>
                            <v-text-field
                                    label="Mapping"
                                    v-model="editedItem.mapping"
                                    :error-messages="errors.mapping"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12>
                            <v-autocomplete
                                    :items="groups"
                                    v-model="editedItem.group_id"
                                    label="Group"
                                    item-text="text"
                                    item-value="id"
                                    clearable
                                    :error-messages="errors.group_id"
                            ></v-autocomplete>
                        </v-flex>
                        <v-flex xs12>
                            <v-autocomplete
                                    :items="cast"
                                    v-model="editedItem.cast"
                                    label="Cast"
                                    clearable
                                    :error-messages="errors.cast"
                            ></v-autocomplete>
                        </v-flex>
                        <v-flex xs12 v-if="editedItem.cast !== 'bool'">
                            <v-text-field
                                    label="Max Purchase Value"
                                    v-model="editedItem.max_value"
                                    :error-messages="errors.max_value"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12 sm6>
                            <v-switch
                                    label="Active"
                                    v-model="editedItem.active"
                            ></v-switch>
                        </v-flex>
                        <v-flex xs12 sm6>
                            <v-switch
                                    label="Dynamic Pricing"
                                    v-model="editedItem.dynamic_pricing"
                            ></v-switch>
                        </v-flex>
                        <v-flex xs12 sm6 v-if="editedItem.cast === 'int'">
                          <v-switch
                              label="Overwritable"
                              v-model="editedItem.overwritable"
                          ></v-switch>
                        </v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                <v-btn
                        color="primary"
                        :loading="loading"
                        :disabled="loading"
                        flat
                        @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
    import axios from 'axios'
    import _ from "lodash"

    export default {
        name: "edit",
        props: ['items'],
        data() {
            return {
                title: '',
                tab: 0,
                dialog: false,
                loading: false,
                errors: [],
                editedItem: {
                    name: {}
                },
                editedIndex: -1,
                cast: [
                    'bool',
                    'int',
                    'storage',
                    'fee',
                ],
                groups: [],
            }
        },
        mounted() {
            this.getGroups()
        },
        computed: {
            languages() {
                return this.$store.state.languages
            },
        },
        methods: {
            getGroups() {
                axios.get('/plan-features/groups')
                    .then(response => {
                        this.groups = response.data.data.map(item => {
                            item.text = `${item.name_translated} (${item.mapping})`;
                            return item
                        });

                        this.groups = _.sortBy(this.groups, 'text')
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            },
            close() {
                this.dialog = false
            },
            edit(item) {
                this.editedItem = this.makeNonReactiveCopy(item);
                this.editedIndex = this.items.indexOf(item);
                this.errors = [];
                this.title = 'Edit Feature ' + item.mapping;
                this.dialog = true
            },
            add() {
                this.dialog = true;
                this.errors = [];
                this.title = 'New Feature';
                this.editedIndex = -1;
            },
            save() {
                this.loading = true;
                if (this.editedIndex > -1) {
                    axios.put('/plan-features/' + this.editedItem.id, this.editedItem)
                        .then((response) => {
                            this.loading = false;
                            Object.assign(this.items[this.editedIndex], response.data.data);
                            this.close()
                        })
                        .catch(error => {
                            this.handleError(error)
                        })
                } else {
                    axios.post('/plan-features', this.editedItem)
                        .then((response) => {
                            this.loading = false;
                            this.items.unshift(response.data.data);
                            this.close()
                        })
                        .catch(error => {
                            this.handleError(error)
                        })
                }
            }
        }
    }
</script>
