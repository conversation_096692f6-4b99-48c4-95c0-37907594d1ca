<template>
    <v-dialog
            v-model="dialog"
            max-width="500px"
            scrollable
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h3 class="headline white--text mb-0">{{ title }}</h3>
            </v-card-title>

            <v-card-text>
                <v-container grid-list-md>
                    <v-layout wrap>
                        <v-flex xs12>
                            <v-autocomplete
                                    :items="features"
                                    v-model="editedItem.feature_id"
                                    label="Feature"
                                    item-text="text"
                                    item-value="id"
                                    required
                                    :clearable="editedIndex < 0"
                                    :readonly="editedIndex > 0"
                                    :error-messages="errors.feature_id"
                            ></v-autocomplete>
                        </v-flex>
                        <v-flex xs12 v-if="!editedItem.feature || editedItem.feature.cast != 'bool'">
                            <v-text-field
                                    label="Value"
                                    v-model="editedItem.value"
                                    required
                                    :hint="editedItem.feature ? editedItem.feature.cast : ''"
                                    :error-messages="errors.value"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12>
                            <v-switch
                                label="Enable feature pack upsell"
                                v-model="editedItem.enable_feature_pack"
                            ></v-switch>
                        </v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                <v-btn
                        color="primary"
                        :loading="loading"
                        :disabled="loading"
                        flat
                        @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
    import axios from 'axios'
    import _ from "lodash"

    export default {
        name: "edit",
        props: ['items', 'plan'],
        data() {
            return {
                title: '',
                dialog: false,
                loading: false,
                errors: [],
                editedItem: {},
                editedIndex: -1,
                cast: [
                    'bool',
                    'int',
                    'storage',
                    'fee',
                ],
                features: [],
            }
        },
        mounted() {
            this.getFeatures()
        },
        methods: {
            getFeatures() {
                axios.get('/plan-features')
                        .then(response => {
                            this.features = response.data.data.map(item => {
                                item.text = `${item.name_translated} (${item.mapping})`
                                item.feature_id = item.id
                                return item
                            })

                            this.features = _.sortBy(this.features, 'text')
                        })
                        .catch(error => {
                            this.handleError(error)
                        })
            },
            close() {
                this.dialog = false
            },
            edit(item) {
                this.editedItem = this.makeNonReactiveCopy(item)
                this.editedIndex = this.items.indexOf(item)
                this.errors = []
                this.title = 'Edit Restriction #' + item.id
                this.dialog = true
            },
            add() {
                this.dialog = true
                this.errors = []
                this.title = 'New Restriction'
                this.editedIndex = -1
                this.editedItem = {}
                this.features = _.differenceBy(this.features, this.items, 'feature_id')
            },
            save() {
                this.loading = true
                if (this.editedIndex > -1) {
                    axios.put('/plan-restrictions/' + this.editedItem.id, this.editedItem)
                            .then((response) => {
                                this.loading = false
                                Object.assign(this.items[this.editedIndex], response.data.data)
                                this.close()
                            })
                            .catch(error => {
                                this.handleError(error)
                            })
                } else {
                    let data = {...this.editedItem, plan_id: this.plan.id}
                    axios.post('/plan-restrictions', data)
                            .then((response) => {
                                this.loading = false
                                this.items.unshift(response.data.data)
                                this.close()
                            })
                            .catch(error => {
                                this.handleError(error)
                            })
                }
            }
        }
    }
</script>
