<template>
    <v-card>
        <v-card>
            <v-card-title>
                <h1>
                    <span v-if="plan">{{ plan.name }} ({{ plan.mapping }})</span>
                    <span v-else>Plan Pricing Details</span>
                    <br>
                    <v-btn-toggle v-if="filters" v-model="filters.status">
                        <v-btn
                            :value=1
                            small
                            outline
                            color="primary"
                        >Active
                        </v-btn>
                        <v-btn
                            :value=0
                            small
                            outline
                            color="primary"
                        >Inactive
                        </v-btn>
                    </v-btn-toggle>
                </h1>
                <v-spacer></v-spacer>
                <v-text-field
                    v-if="!plan"
                    append-icon="search"
                    label="Search"
                    single-line
                    v-model="search"
                    clearable
                ></v-text-field>
                <v-menu offset-y :nudge-bottom="5" v-if="!plan">
                    <v-btn slot="activator"
                           small
                           fab
                           dark
                           absolute
                           bottom
                           right
                           color="pink"
                    >
                        <v-icon>more_vert</v-icon>
                    </v-btn>
                    <v-list>
                        <v-list-tile @click="$refs.editDialog.add()">
                            <v-list-tile-content>
                                <v-list-tile-title>Add</v-list-tile-title>
                            </v-list-tile-content>
                            <v-list-tile-action>
                                <v-icon>add</v-icon>
                            </v-list-tile-action>
                        </v-list-tile>
                        <v-list-tile @click="exportExcel()">
                            <v-list-tile-content>
                                <v-list-tile-title>Export</v-list-tile-title>
                            </v-list-tile-content>
                            <v-list-tile-action>
                                <v-icon>file_copy</v-icon>
                            </v-list-tile-action>
                        </v-list-tile>
                    </v-list>
                </v-menu>
                <v-btn
                    v-else
                    small
                    absolute
                    dark
                    fab
                    bottom
                    right
                    color="pink"
                    @click="$refs.editDialog.add(plan)"
                >
                    <v-icon>add</v-icon>
                </v-btn>
            </v-card-title>
        </v-card>

        <v-data-table
            :headers="headers"
            :items="filteredItems"
            :search="search"
            :pagination.sync="pagination"
            :total-items="totalItems"
            :loading="loading"
            :custom-filter="customFilter"
            class="elevation-1"
            must-sort
        >
            <template slot="items" slot-scope="props">
                <tr>
                    <td>{{ props.item.id }}</td>
                    <td>{{ props.item.plan.name }} / {{ props.item.plan.mapping }}</td>
                    <td>{{ props.item.period }}</td>
                    <td class="text-xs-right">
                        {{ props.item.price | toCurrency(props.item.currency) }}
                    </td>
                    <td class="text-xs-right" v-if="props.item.original_price">
                        {{ props.item.original_price | toCurrency(props.item.currency) }}
                    </td>
                    <td v-else></td>
                    <td>
                        <v-icon v-if="props.item.active" color="green">check_circle</v-icon>
                        <v-icon v-if="!props.item.active" color="red">block</v-icon>
                    </td>
                    <td>{{ props.item.updated_at | formatDateTime }}</td>
                    <td class="text-xs-left px-0" nowrap>
                        <v-btn icon class="mx-0" @click="$refs.editDialog.edit(props.item)">
                            <v-icon color="teal">edit</v-icon>
                        </v-btn>
                    </td>
                </tr>
            </template>

            <v-alert slot="no-results" :value="true" color="error" icon="warning">
                Your search for "{{ search }}" found no results.
            </v-alert>

        </v-data-table>

        <edit :items="items" ref="editDialog"/>
    </v-card>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import {flatten} from '@helpers'
import Edit from './edit-details'

export default {
    name: 'plan-details-list',
    props: ['plan', 'filters'],
    components: {
        LayoutDefault,
        Edit,
    },
    data() {
        return {
            search: '',
            totalItems: 0,
            items: [],
            loading: true,
            pagination: {
                rowsPerPage: 25,
                descending: true,
            },
            headers: [
                {text: 'ID', value: 'id'},
                {text: 'Plan / Mapping', value: 'plan.name'},
                {text: 'Period', value: 'period'},
                {text: 'Price', align: 'right', value: 'price'},
                {text: 'Original Price', align: 'right', value: 'original_price'},
                {text: 'Active', value: 'active'},
                {text: 'Updated At', value: 'updated_at'},
                {text: '', value: 'id', sortable: false, align: 'center'}
            ],
        }
    },
    created() {
    },
    computed: {
        filteredItems() {
            if (!this.filters) {
                return this.items
            }

            return this.items.filter((item) => {
                return this.filters.status === item.active
            });
        },
    },
    methods: {
        json(value) {
            return JSON.stringify(value, null, 2)
        },
        getItems(plan) {
            let url = plan ? '/plan-details/' + plan.id : '/plan-details'
            this.loading = true
            axios.get(url)
                .then(response => {
                    this.loading = false
                    this.items = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        customFilter(items, search, filter) {
            search = search.toString().toLowerCase()
            if (search.trim() === '') return items

            return items.filter(item => item.flatten.some(val => filter(val, search)))
        },
        exportExcel() {
            axios.post('/plan-details/export')
                .then(response => {
                    window.location = response.data.redirect
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    },
}
</script>