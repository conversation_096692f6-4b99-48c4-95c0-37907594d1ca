<template>
    <v-dialog
        v-model="dialog"
        max-width="500px"
        scrollable
        persistent
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h2 class="white--text mb-0">{{ title }}</h2>
            </v-card-title>

            <v-tabs
                v-model="tab"
                dark
                :color="$root.dark ? 'grey darken-4' : 'primary'"
                slider-color="yellow"
            >
                <v-tab
                    v-for="lang in languages"
                    :key="lang"
                > {{ lang }}
                </v-tab>
                <v-tab-item
                    v-for="lang in languages"
                    :key="lang"
                    lazy
                >
                    <v-card-text>
                        <v-container grid-list-md>
                            <v-layout wrap>
                                <v-flex xs12>
                                    <v-text-field
                                        label="Name"
                                        v-model="editedItem.name[lang]"
                                        :error-messages="errors['name.' + lang]"
                                    ></v-text-field>
                                </v-flex>
                            </v-layout>
                        </v-container>
                    </v-card-text>
                </v-tab-item>
            </v-tabs>

            <v-card-text>
                <v-container grid-list-md>
                    <v-layout wrap>
                        <v-flex xs12>
                            <v-autocomplete
                                :items="features"
                                v-model="editedItem.feature_id"
                                label="Feature"
                                item-text="text"
                                item-value="id"
                                required
                                :clearable="editedIndex < 0"
                                :readonly="editedIndex > 0"
                                :error-messages="errors.feature_id"
                                @change="changeFeature"
                                ref="feature"
                            ></v-autocomplete>
                        </v-flex>
                        <v-flex xs6>
                            <v-text-field
                                v-model="editedItem.price_input"
                                label="Price without VAT"
                                :suffix="currency"
                                :error-messages="errors.price_input"
                                :hint="priceWithVat"
                                persistent-hint
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs6>
                            <v-autocomplete
                                :items="billingCycle"
                                v-model="editedItem.billing_cycle"
                                label="Billing Cycle"
                                item-text="text"
                                item-value="value"
                            ></v-autocomplete>
                        </v-flex>
                        <v-flex xs6>
                            <v-text-field
                                v-model="editedItem.value"
                                label="Value"
                                :error-messages="errors.value"
                            ></v-text-field>
                        </v-flex>
                        <v-flex v-if="editedItem.dynamic_pricing" xs6>
                            <v-text-field
                                v-model="editedItem.value_max"
                                label="Value Max"
                                :error-messages="errors.value_max"
                            ></v-text-field>
                        </v-flex>
                        <!--                        <v-flex xs6>-->
                        <!--                            <v-switch-->
                        <!--                                label="Increment value on renewal"-->
                        <!--                                v-model="editedItem.increment_value"-->
                        <!--                            ></v-switch>-->
                        <!--                        </v-flex>-->
                        <v-flex xs6>
                            <v-switch
                                label="Active"
                                v-model="editedItem.active"
                            ></v-switch>
                        </v-flex>
                        <v-flex xs6>
                            <v-switch
                                label="Enable sell from console"
                                v-model="editedItem.console_enable_sell"
                            ></v-switch>
                        </v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn v-if="editedItem.dynamic_pricing" color="primary" flat @click.native="pricing">Show Pricing
                </v-btn>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                <v-btn
                    color="primary"
                    :loading="loading"
                    :disabled="loading"
                    flat
                    @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>

        <dynamic-pricing ref="pricing"></dynamic-pricing>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import EditService from '../services/edit'
import DynamicPricing from "./dynamic-pricing";

export default {
    name: "edit-feature-pack",
    components: {DynamicPricing},
    extends: EditService,
    data() {
        return {
            features: [],
        }
    },
    mounted() {
        this.getFeatures()
    },
    computed: {
        priceWithVat() {
            if (this.editedItem === undefined) {
                return ''
            }

            let price = this.editedItem.price_input ? this.editedItem.price_input * 100 * 1.2 : 0
            let priceFormatted = this.$options.filters.toCurrency(price)

            return 'Price with VAT: ' + priceFormatted
        },
    },
    methods: {
        getFeatures() {
            axios.get('/plan-features')
                .then(response => {
                    this.features = response.data.data.map(item => {
                        item.text = `${item.name_translated} (${item.mapping})`
                        return item
                    })

                    this.features = _.sortBy(this.features, 'text')
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        changeFeature() {
            let feature = _.find(this.features, ['id', this.editedItem.feature_id])
            if (feature) {
                this.editedItem.dynamic_pricing = feature.dynamic_pricing
            }
        },
        pricing() {
            this.errors = {}
            if (!this.editedItem.price_input) {
                this.errors.price_input = ['Price is required']
            }

            if (!this.editedItem.value) {
                this.errors.value = ['Value is required']
            }

            if (!this.editedItem.value_max) {
                this.errors.value_max = ['Value Max is required']
            }

            if (!_.isEmpty(this.errors)) {
                return
            }

            this.$refs.pricing.showPricing(this.editedItem)
        },
        editItem(item) {
            this.clear()
            this.dialog = true
            this.title = 'Edit Plan Feature Pack #' + item.id
            this.editedIndex = this.items.indexOf(item)
            this.editedItem = this.makeNonReactiveCopy(item)
        },
        addItem() {
            this.dialog = true
            this.clear()
            this.title = 'New Plan Feature Pack'
        },
        save() {
            this.loading = true
            if (this.editedIndex > -1) {
                axios.put('/plan-feature-packs/' + this.editedItem.id, this.editedItem)
                    .then((response) => {
                        this.loading = false
                        Object.assign(this.items[this.editedIndex], response.data.data)
                        this.close()
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            } else {
                axios.post('/plan-feature-packs', this.editedItem)
                    .then((response) => {
                        this.loading = false
                        this.items.unshift(response.data.data)
                        this.close()
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        }
    }
}
</script>
