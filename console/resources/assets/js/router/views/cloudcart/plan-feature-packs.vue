<template>
    <layout-default>

        <v-container grid-list-md fluid>
            <v-card>
                <v-card-title>
                    <h1>
                        Plan Feature Packs
                        <br>
                        <v-btn-toggle v-model="filters.active">
                            <v-btn
                                    :value=1
                                    small
                                    outline
                                    color="primary"
                            >Active
                            </v-btn>
                            <v-btn
                                    :value=0
                                    small
                                    outline
                                    color="primary"
                            >Inactive
                            </v-btn>
                        </v-btn-toggle>
                    </h1>
                    <v-spacer></v-spacer>
                    <v-text-field
                            append-icon="search"
                            label="Search"
                            single-line
                            v-model="search"
                            clearable
                    ></v-text-field>
                    <v-menu offset-y :nudge-bottom="5">
                        <v-btn slot="activator"
                               small
                               fab
                               dark
                               absolute
                               bottom
                               right
                               color="pink"
                        >
                            <v-icon>more_vert</v-icon>
                        </v-btn>
                        <v-list>
                            <v-list-tile @click="$refs.editDialog.addItem()">
                                <v-list-tile-content>
                                    <v-list-tile-title>Add</v-list-tile-title>
                                </v-list-tile-content>
                                <v-list-tile-action>
                                    <v-icon>add</v-icon>
                                </v-list-tile-action>
                            </v-list-tile>
                            <v-list-tile @click="exportExcel()">
                                <v-list-tile-content>
                                    <v-list-tile-title>Export</v-list-tile-title>
                                </v-list-tile-content>
                                <v-list-tile-action>
                                    <v-icon>file_copy</v-icon>
                                </v-list-tile-action>
                            </v-list-tile>
                        </v-list>
                    </v-menu>
                </v-card-title>

                <filter-feature-packs
                        :filters="filters"
                        ref="filters"
                ></filter-feature-packs>

            </v-card>

            <v-data-table
                    :headers="headers"
                    :items="filteredItems"
                    :search="search"
                    :pagination.sync="pagination"
                    :total-items="totalItems"
                    :loading="loading"
                    :custom-filter="customFilter"
                    class="elevation-1"
            >
                <template slot="items" slot-scope="props">
                    <tr>
                        <td>{{ props.item.id }}</td>
                        <td>{{ props.item.updated_at | formatDateTime }}</td>
                        <td>{{ props.item.name['en'] }}</td>
                        <td>{{ props.item.sold_details.count }}</td>
                        <td>{{ props.item.sold_details.revenue | toCurrency }}</td>
                        <td>
                            {{ props.item.feature.name['en'] }}
                            <br>
                            <b>{{ props.item.mapping }}</b>
                        </td>
                        <td>{{ props.item.value }}</td>
                        <td nowrap class="text-xs-right">
                            <v-tooltip v-if="props.item.dynamic_pricing" bottom>
                                <v-btn slot="activator" icon class="mx-0"
                                       @click="showPricing(props.item)">
                                    <v-icon color="teal">attach_money</v-icon>
                                </v-btn>
                                <span>Show Dynamic Pricing</span>
                            </v-tooltip>
                            {{ props.item.price | toCurrency }}
                        </td>
                        <td>{{ props.item.billing_period }}</td>
                        <td class="text-xs-center px-0" nowrap>
                            <v-btn icon class="mx-0" @click="$refs.editDialog.editItem(props.item)">
                                <v-icon color="teal">edit</v-icon>
                            </v-btn>
                            <v-btn icon class="mx-0" @click="$refs.deleteDialog.deleteItem(props.item)">
                                <v-icon color="pink">delete</v-icon>
                            </v-btn>
                        </td>
                    </tr>
                </template>

                <v-alert slot="no-results" :value="true" color="error" icon="warning">
                    Your search for "{{ search }}" found no results.
                </v-alert>

            </v-data-table>

            <edit :items="items" ref="editDialog"/>

            <delete :items="items" url="/plan-feature-packs/" ref="deleteDialog"/>

            <dynamic-pricing ref="pricing"></dynamic-pricing>
        </v-container>

    </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import store from '@state/store'
import {flatten} from '@helpers'
import Edit from './plans/edit-feature-pack'
import Delete from '@components/Delete'
import DynamicPricing from "./plans/dynamic-pricing";
import FilterFeaturePacks from "./plans/filter-feature-packs";

export default {
    components: {
        FilterFeaturePacks,
        DynamicPricing,
        LayoutDefault,
        Edit,
        Delete,
    },
    data() {
        return {
            search: '',
            totalItems: 0,
            items: [],
            loading: true,
            pagination: {
                rowsPerPage: 25,
                descending: true,
            },
            headers: [
                {text: 'ID', value: 'id'},
                {text: 'Updated At', value: 'updated_at'},
                {text: 'Name', value: 'name_translated'},
                {text: 'Sold Count', value: 'sold_details.count'},
                {text: 'Sold Revenue', value: 'sold_details.revenue'},
                {text: 'Feature', value: 'mapping'},
                {text: 'Value', value: 'value'},
                {text: 'Price', value: 'price_formatted'},
                {text: 'Billing Cycle', value: 'billing_period'},
                {text: '', value: 'id', sortable: false, align: 'center'}
            ],
            filters: {
                active: 1,
            },
        }
    },
    created() {
        this.getItems()
    },
    computed: {
        filteredItems() {
            return this.items.filter((item) => {
                if (!this.$refs.filters) {
                    return item
                }
                return this.$refs.filters.filter(item)
            })
        },
    },
    methods: {
        showPricing(item) {
            this.$refs.pricing.showPricing(item)
        },
        json(value) {
            return JSON.stringify(value, null, 2)
        },
        toBGN(value) {
            const rates = store.getters.toCurrencies(value)
            return this.$options.filters.toCurrency(rates[1].value / 100, rates[1].currency)
        },
        getItems() {
            this.loading = true
            axios.get('/plan-feature-packs')
                .then(response => {
                    this.loading = false
                    this.items = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        customFilter(items, search, filter) {
            search = search.toString().toLowerCase()
            if (search.trim() === '') return items

            return items.filter(item => item.flatten.some(val => filter(val, search)))
        },
        exportExcel() {
            axios.post('/plan-feature-packs/export')
                .then(response => {
                    window.location = response.data.redirect
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    },
}
</script>