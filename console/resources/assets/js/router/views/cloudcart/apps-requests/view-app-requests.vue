<template>
  <v-dialog
      v-model="dialog"
      fullscreen
      hide-overlay
      transition="dialog-bottom-transition"
      scrollable
  >
    <v-card tile>
      <v-toolbar card dark :color="$root.dark ? 'grey darken-4' : 'primary'">
        <v-toolbar-title>
          <v-icon>apps</v-icon>
          App key: {{appKey}}
        </v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon dark @click="close()">
          <v-icon>close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text class="transparent">
      <v-card-title>
      <h1>
        {{title}} Requests
      </h1>
      <v-spacer></v-spacer>
      <v-text-field
          append-icon="search"
          label="Search"
          single-line
          v-model="search"
          clearable
      ></v-text-field>
    </v-card-title>
      <v-data-table
          :headers="headers"
          :items="items"
          :search="search"
          :pagination.sync="pagination"
          :total-items="totalItems"
          :loading="loading"
          :custom-filter="customFilter"
          class="elevation-1"
          must-sort
          style="overflow: scroll"
      >
        <template slot="items" slot-scope="props">
          <tr>
            <td>{{ props.item.created_at }}</td>
            <td>{{ props.item.site_id }}</td>
            <td>{{ props.item.admin_name }}</td>
            <td>{{ props.item.admin_mail }}</td>
            <td>{{ props.item.admin_id }}</td>
            <td>{{ props.item.domain }}</td>
          </tr>
        </template>
        <v-alert slot="no-results" :value="true" color="error" icon="warning">
          Your search for "{{ search }}" found no results.
        </v-alert>
      </v-data-table>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import {flatten} from '@helpers'

export default {
  name: "ViewAppRequests",
  data() {
    return {
      title: '',
      tab: 0,
      dialog: false,
      loading: false,
      errors: [],
      categories: [],
      editedItem: {},
      editedIndex: -1,
      search: '',
      totalItems: 0,
      items: [],
      appKey: '',
      pagination: {
        rowsPerPage: 25,
        descending: true,
      },
      headers: [
        {text: 'Requested At', sortable: true, value: 'created_at'},
        {text: 'Site ID', sortable: true, value: 'site_id'},
        {text: 'Client Name', sortable: true, value: 'admin_name'},
        {text: 'Client Email', sortable: true, value: 'admin_mail'},
        {text: 'Client ID', sortable: true, value: 'admin_id'},
        {text: 'Site', sortable: true, value: 'domain'},

      ],
    }
  },
  methods: {
    close() {
      this.dialog = false
      this.items = []
    },
    show(item) {
      this.dialog = true;
      this.title = item.name;
      this.appKey = item.key;
      this.loading = true

      axios.post('/apps-requests/'+this.appKey)
          .then(response => {
            this.loading = false
            console.log(response);
            this.items = response.data.data.map(item => {
              return {...item, flatten: Object.values(flatten(item))}
            })
          })
          .catch(error => {
            this.handleError(error)
          })
    },
    customFilter(items, search, filter) {
      search = search.toString().toLowerCase()
      if (search.trim() === '') return items

      return items.filter(item => item.flatten.some(val => filter(val, search)))
    },
  }
}
</script>
