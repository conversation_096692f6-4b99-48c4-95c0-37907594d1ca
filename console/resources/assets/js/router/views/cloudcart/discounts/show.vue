<template>
    <layout-default>

        <v-container v-if="item" grid-list-md fluid>
            <v-layout row wrap>
                <v-flex xs12>
                    <v-card>
                        <v-card-title primary-title>
                            <v-flex xs12>
                                <h3 class="headline">
                                    {{ item.name }} <span class="grey--text caption">Discount # {{ item.id }}</span>
                                </h3>
                            </v-flex>
                        </v-card-title>
                        <v-card-text>
                            <v-layout row wrap>
                                <v-flex xs6 sm3>
                                    <div>Code:</div>
                                    <div>Items:</div>
                                    <div>Uses:</div>
                                    <div>Max Uses:</div>
                                </v-flex>
                                <v-flex xs6 sm3>
                                    <div><strong>{{ item.code }}</strong></div>
                                    <div><strong>{{ item.items_count }}</strong></div>
                                    <div><strong>{{ item.uses }}</strong></div>
                                    <div><strong>{{ item.max_uses }}</strong></div>
                                </v-flex>
                                <v-flex xs6 sm3>
                                    <div>Expire At:</div>
                                    <div>Created:</div>
                                    <div>Updated:</div>
                                    <div>Status:</div>
                                </v-flex>
                                <v-flex xs6 sm3>
                                    <div><strong>{{ item.expire_at | formatDateTime }}&nbsp;</strong></div>
                                    <div><strong>{{ item.created_at | formatDateTime }}</strong></div>
                                    <div><strong>{{ item.updated_at | formatDateTime }}</strong></div>
                                    <div><strong>{{ item.status_name }}</strong></div>
                                </v-flex>
                            </v-layout>
                        </v-card-text>
                        <v-card-actions v-if="item.discount_type == 2">
                            <v-btn flat color="primary" @click.native="purchaseLink">
                                Purchase Link
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </v-flex>

                <v-flex xs12>
                    <v-tabs>
                        <v-tab key="1" v-if="item.discount_type != 1">Items</v-tab>
                        <v-tab key="2">Transactions</v-tab>
                        <v-tab key="3">Subscriptions</v-tab>
                        <v-tab key="4" v-if="item.discount_type == 2">Offers</v-tab>

                        <v-tabs-items :touchless="true">
                            <v-tab-item key="1" v-if="item.discount_type != 1">
                                <discount-items-list
                                    :items="items"
                                    :discount="item"
                                    v-on:offer:refresh="() => this.getItem()"
                                ></discount-items-list>
                            </v-tab-item>
                            <v-tab-item key="2">
                                <v-card>
                                    <v-card-title>
                                        <h1>Transactions</h1>
                                    </v-card-title>
                                </v-card>
                                <transaction-list :items="transactions"></transaction-list>
                            </v-tab-item>
                            <v-tab-item key="3">
                                <subscription-list
                                    :items="subscriptions"
                                    :site="null"
                                    :hideControls="true"
                                ></subscription-list>
                            </v-tab-item>
                            <v-tab-item key="4" v-if="item.discount_type == 2">
                                <offers-list
                                    :items="offers"
                                />
                            </v-tab-item>
                        </v-tabs-items>
                    </v-tabs>
                </v-flex>

            </v-layout>
        </v-container>

    </layout-default>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import NProgress from 'nprogress/nprogress'
import LayoutDefault from '@layouts/Default'
import DiscountItemsList from "./list-items";
import TransactionList from '@views/billing/transactions/list'
import SubscriptionList from '@views/billing/subscriptions/list'
import OffersList from "../../billing/offers/list";

export default {
    name: "show",
    components: {
        OffersList,
        LayoutDefault,
        DiscountItemsList,
        TransactionList,
        SubscriptionList,
    },
    data() {
        return {
            item: null,
            items: [],
            transactions: [],
            subscriptions: [],
            offers: [],
        }
    },
    created() {
        this.getItem()
        this.getItems()
        this.getTransactions()
        this.getSubscriptions()
        this.getOffers()
    },
    methods: {
        getItem() {
            NProgress.start()
            axios.get(`/discounts/${this.$route.params.id}`)
                .then(response => {
                    this.item = response.data.data
                    NProgress.done()
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getItems() {
            NProgress.start()
            axios.get(`/discounts/${this.$route.params.id}/items`)
                .then(response => {
                    this.items = response.data.data
                    NProgress.done()
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getTransactions() {
            axios.get(`/discounts/${this.$route.params.id}/transactions`)
                .then(response => {
                    this.transactions = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getSubscriptions() {
            axios.get(`/discounts/${this.$route.params.id}/subscriptions`)
                .then(response => {
                    this.subscriptions = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getOffers() {
            axios.get(`/discounts/${this.$route.params.id}/offers`)
                .then(response => {
                    this.offers = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        purchaseLink() {
            this.copyToClipboard(this.item.purchase_link)
            let success = {
                data: {
                    message: "Purchase link copied to clipboard!",
                }
            }
            this.handleSuccess(success)
        },
        copyToClipboard(str) {
            const el = document.createElement('textarea')
            el.addEventListener('focusin', e => e.stopPropagation())
            el.value = str
            document.body.appendChild(el)
            el.select()
            document.execCommand('copy')
            document.body.removeChild(el)
        },

    }
}
</script>