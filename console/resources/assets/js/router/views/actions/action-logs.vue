<template>
    <v-dialog
        v-model="dialog"
        max-width="600px"
        hide-overlay
        transition="dialog-bottom-transition"
        scrollable
    >
        <v-card tile>
            <v-toolbar card dark :color="$root.dark ? 'grey darken-4' : 'primary'">
                <v-toolbar-title>
                    Action log for {{ item.name }}
                </v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon dark @click.native="dialog = false">
                    <v-icon>close</v-icon>
                </v-btn>
            </v-toolbar>

            <v-card-text class="transparent">
                <div v-highlightjs="actionHtml(item)" :class="$root.dark ? 'dark' : ''">
                    <code class="html"></code>
                </div>
                <v-data-table
                    :headers="headers"
                    :items="items"
                    :pagination.sync="pagination"
                    :loading="loading"
                    class="elevation-1"
                    ref="dataTable"
                >
                    <template slot="items" slot-scope="props">
                        <tr>
                            <td>
                                {{ props.item.created_at | formatDateTime }}
                            </td>
                            <td v-if="props.item.data && item.action_type === 'send_email'">
                                <v-btn icon class="mx-0"
                                       @click="$refs.emailDialog.show(props.item.data)">
                                    <v-icon color="teal">email</v-icon>
                                </v-btn>
                            </td>
                            <td v-else-if="props.item.data" v-highlightjs="json(props.item.data)"
                                :class="$root.dark ? 'dark' : ''">
                                <code class="json"></code>
                            </td>
                            <td v-else></td>
                            <td>
                                <a v-if="props.item.site_id"
                                   @click="$refs.siteDetails.show({id: props.item.site_id})"
                                >{{ props.item.site_id }}</a>
                                <span v-else>{{ props.item.user_id }}</span>
                            </td>
                        </tr>
                    </template>

                    <v-alert slot="no-results" :value="true" color="error" icon="warning">
                        Your search for "{{ search }}" found no results.
                    </v-alert>

                </v-data-table>
            </v-card-text>

            <div style="flex: 1 1 auto;" class="transparent"></div>
        </v-card>

        <email-show ref="emailDialog" :is-cloud-cart-mail="true"></email-show>
        <json-dialog ref="jsonDialog" max-width="500px"></json-dialog>
        <site-show-dialog ref="siteDetails"></site-show-dialog>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import SitesList from "../sites/list";
import EmailShow from "../marketing/email-show";
import JsonDialog from "../../../components/JsonDialog";
import SiteShowDialog from "../sites/show-dialog.vue";

export default {
    name: "action-logs",
    components: {
        SiteShowDialog,
        JsonDialog,
        EmailShow,
        SitesList,
    },
    data() {
        return {
            dialog: false,
            loading: false,
            search: '',
            item: {},
            items: [],
            pagination: {
                rowsPerPage: 25,
                descending: true,
            },
            headers: [
                {text: 'Created At', value: 'id'},
                {text: 'Data', value: 'data'},
                {text: 'Site / User', value: 'site_id'},
            ],
        }
    },
    computed: {},
    methods: {
        actionHtml(item) {
            let html = item.action_type + ' ' + item.value + ' | ' + item.event_type
            if (item.segment) {
                html = html + ' ' + item.segment.name
            }

            return html
        },
        json(value) {
            return JSON.stringify(value, null, 2)
        },
        show(item) {
            this.items = []
            this.item = item
            this.dialog = true
            this.loading = true
            axios.get(`/site-actions/${item.id}/logs`)
                .then(response => {
                    this.loading = false
                    this.items = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        }
    }
}
</script>