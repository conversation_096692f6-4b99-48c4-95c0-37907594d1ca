<template>
  <v-card>
    <v-data-table
        :headers="headers"
        :items="items"
        :pagination.sync="pagination"
        :loading="loading"
        class="elevation-1"
    >
      <template slot="items" slot-scope="props">
        <td>
          <a @click="$refs.detailsInvoices.showByPeriod(props.item)">
            {{ props.item.period }}
          </a>
        </td>
        <td>
          <a @click="$refs.detailsSites.show(props.item, 'sites')">
            {{ props.item.sites_count }}
          </a>
        </td>
        <td>
          {{ props.item.mrr | toCurrency('EUR') }}
        </td>
        <td>{{ props.item.arr | toCurrency('EUR') }}</td>
        <td>{{ props.item.arpu | toCurrency('EUR') }}</td>
      </template>
    </v-data-table>

    <details-invoice-items ref="detailsInvoices"></details-invoice-items>
    <details-registrations ref="detailsSites" type="sites" title="Sites"></details-registrations>
  </v-card>
</template>

<script type="text/ecmascript-6">

import DetailsInvoiceItems from "./details-invoice-items";
import DetailsRegistrations from "./details-registrations";

export default {
  name: "report-mrr",
  props: ['loading', 'items'],
  components: {DetailsRegistrations, DetailsInvoiceItems},
  data() {
    return {
      newUsers: [],
      pagination: {
        rowsPerPage: 25,
        descending: true,
      },
      headers: [
        {
          text: 'Period',
          align: 'left',
          sortable: true,
          value: 'period'
        },
        {text: 'Sites', value: 'sites_count'},
        {text: 'MRR', value: 'mrr'},
        {text: 'ARR', value: 'arr'},
        {text: 'ARPU', value: 'arpu'},
      ]
    }
  },
  mounted() {
  },
  computed: {},
  methods: {},
}
</script>
