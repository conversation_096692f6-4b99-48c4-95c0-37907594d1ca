<template>
    <layout-default>
        <v-container grid-list-md fluid>
            <v-card>
                <v-card-title>
                    <v-flex xs12 sm4>
                        <h1>Expired Subscriptions</h1>
                    </v-flex>
                </v-card-title>

                <report-filter-subscriptions
                    :filters="filters"
                    v-on:filters:submit="(filters) => this.getItems(filters)"
                ></report-filter-subscriptions>

                <v-flex xs12>
                    <v-tabs>
                        <v-tab key="2">Month</v-tab>
                        <v-tab key="3">Quarter</v-tab>
                        <v-tab key="4">Year</v-tab>

                        <v-tabs-items :touchless="true">
                            <v-tab-item key="2">
                                <report-subscriptions :items="month" :loading="loading" title="Expired Subscriptions"/>
                            </v-tab-item>
                            <v-tab-item key="3">
                                <report-subscriptions :items="quarter" :loading="loading"
                                                      title="Expired Subscriptions"/>
                            </v-tab-item>
                            <v-tab-item key="4">
                                <report-subscriptions :items="year" :loading="loading" title="Expired Subscriptions"/>
                            </v-tab-item>
                        </v-tabs-items>
                    </v-tabs>
                </v-flex>
            </v-card>
        </v-container>
    </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from "axios";
import ReportSubscriptions from "./includes/subscriptions";
import ReportFilterSubscriptions from "./includes/filter-subscriptions";

export default {
    components: {
        ReportFilterSubscriptions,
        ReportSubscriptions,
        LayoutDefault,
    },
    data() {
        return {
            loading: false,
            filters: {},
            month: [],
            quarter: [],
            year: [],
        }
    },
    mounted() {
        this.getItems()
    },
    methods: {
        getItems(filters) {
            this.loading = true
            axios.post('/reports/expired-subscriptions', {filters: filters})
                .then(response => {
                    this.loading = false
                    this.month = response.data.month
                    this.quarter = response.data.quarter
                    this.year = response.data.year
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    }
}
</script>