<template>
    <v-dialog
        v-model="dialog"
        fullscreen
        hide-overlay
        transition="dialog-bottom-transition"
        scrollable
        @keydown.esc="closeDialogOnEsc"
    >
        <v-card tile>
            <v-toolbar dense dark :color="$root.dark ? 'grey darken-4' : 'primary'">
                <v-toolbar-title>
                    Company Details
                </v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn
                    v-if="item"
                    icon
                    dark
                    :to="{ name: 'companies.show', params: { id: item.id }}"
                    target="_blank"
                >
                    <v-icon>fa-external-link</v-icon>
                </v-btn>
                <v-btn icon dark @click.native="show(item)">
                    <v-icon>refresh</v-icon>
                </v-btn>
                <v-btn icon dark @click.native="dialog = false">
                    <v-icon>close</v-icon>
                </v-btn>
            </v-toolbar>

            <v-card-text>
                <v-container grid-list-md fluid py-0 px-0>
                    <company-details
                        ref="details"
                        v-on:offer:refresh-item="(item) => this.$emit('offer:refresh-item', item)"
                    ></company-details>
                </v-container>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from "axios";
import CompanyDetails from "./details";

export default {
    name: "company-show-dialog",
    components: {
        CompanyDetails,
    },
    data() {
        return {
            dialog: false,
            loading: false,
            item: null,
        }
    },
    methods: {
        show(item) {
            this.item = null
            this.dialog = true
            this.$refs.details.item = null
            axios.get(`/companies/${item.id}`)
                .then(response => {
                    this.item = response.data.data
                    setTimeout(() => {
                        this.$refs.details.item = response.data.data
                        this.$refs.details.init()
                    }, 50)
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    }
}
</script>
<style scoped>
.theme--light.v-card {
    background-color: #fafafa;
}

.theme--dark.v-card {
    background-color: #303030;
}
</style>