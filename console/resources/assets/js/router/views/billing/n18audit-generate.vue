<template>
    <v-dialog
        v-model="dialog"
        max-width="500px"
        scrollable
    >
        <v-card>
            <v-form ref="form" v-model="valid" lazy-validation>
                <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                    <h3 class="headline white--text mb-0">Generate N18 Audit</h3>
                </v-card-title>

                <v-card-text>
                    <v-container>
                        <v-layout wrap>

                            <v-flex xs12>
                                <v-menu
                                    ref="periodPicker"
                                    lazy
                                    :close-on-content-click="false"
                                    v-model="periodPicker"
                                    transition="scale-transition"
                                    offset-y
                                    full-width
                                    :nudge-right="40"
                                    min-width="290px"
                                    :return-value.sync="period"
                                >
                                    <v-text-field
                                        slot="activator"
                                        label="Period Month"
                                        v-model="period"
                                        prepend-icon="event"
                                        readonly
                                        clearable
                                        :rules="[v => !!v || 'Item is required']"
                                        :error-messages="errors.period"
                                    ></v-text-field>
                                    <v-date-picker v-model="period"
                                                   @input="$refs.periodPicker.save(period)">
                                    </v-date-picker>
                                </v-menu>
                            </v-flex>

                        </v-layout>
                    </v-container>
                </v-card-text>

                <v-divider></v-divider>

                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                    <v-btn
                        color="primary"
                        :loading="loading"
                        :disabled="loading"
                        flat
                        @click.native="save"
                    >Generate
                    </v-btn>
                </v-card-actions>
            </v-form>
        </v-card>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import moment from 'moment'

export default {
    name: "n18-audit-generate",
    components: {},
    data() {
        return {
            periodPicker: false,
            valid: true,
            loading: false,
            dialog: false,
            period: moment().format('YYYY-MM-DD'),
            errors: [],
        }
    },
    methods: {
        show() {
            this.dialog = true
        },
        close() {
            this.dialog = false
        },
        save() {
            if (this.$refs.form.validate()) {
                this.loading = true

                axios.post('/n18audit/generate/' + this.period)
                    .then((response) => {
                        this.handleSuccess(response)
                        this.close()
                        this.$emit('new-n18-audit')
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        }
    }
}
</script>