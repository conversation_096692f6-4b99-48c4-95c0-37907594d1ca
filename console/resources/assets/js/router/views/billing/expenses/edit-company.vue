<template>
    <v-dialog
            v-model="dialog"
            max-width="500px"
            scrollable
            persistent
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h2 class="white--text mb-0">{{ title }}</h2>
            </v-card-title>

            <v-card-text>
                <v-container grid-list-md>
                    <v-layout wrap>
                        <v-flex xs12>
                            <v-text-field
                                    browser-autocomplete="chrome-off"
                                    v-model="editedItem.name"
                                    label="Company Name"
                                    :error-messages="errors.name"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12 sm6>
                            <v-text-field
                                    browser-autocomplete="chrome-off"
                                    v-model="editedItem.cid"
                                    label="Company ID"
                                    :error-messages="errors.cid"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12 sm6>
                            <v-text-field
                                    browser-autocomplete="chrome-off"
                                    v-model="editedItem.vat_number"
                                    label="VAT Number"
                                    :error-messages="errors.vat_number"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12>
                            <v-text-field
                                    browser-autocomplete="chrome-off"
                                    v-model="editedItem.iban"
                                    label="IBAN"
                                    :error-messages="errors.iban"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12>
                            <v-autocomplete
                                    :items="partners"
                                    v-model="editedItem.partner_id"
                                    label="Related Partner"
                                    item-text="name"
                                    item-value="id"
                                    clearable
                            ></v-autocomplete>
                        </v-flex>
                        <v-flex xs12 sm6 pull-right v-if="!editedItem.partner_id">
                            <v-switch
                                    label="Calculate VAT"
                                    v-model="editedItem.calculate_vat"
                            ></v-switch>
                        </v-flex>
                        <v-flex xs12 sm6 pull-right v-if="!editedItem.partner_id">
                            <v-switch
                                    label="Require Invoice"
                                    v-model="editedItem.require_invoice"
                            ></v-switch>
                        </v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                <v-btn
                        color="primary"
                        :loading="loading"
                        :disabled="loading"
                        flat
                        @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import store from '@state/store'

export default {
    name: "expenses-edit-company",
    props: ['items'],
    data() {
        return {
            title: '',
            dialog: false,
            loading: false,
            errors: [],
            partners: [],
            editedIndex: -1,
            editedItem: {},
        }
    },
    created() {
        this.clear()
    },
    mounted() {
        this.getPartners()
    },
    computed: {},
    watch: {},
    methods: {
        getPartners() {
            axios.get('/resellers/autocomplete?all=1')
                .then(response => {
                    this.partners = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        clear() {
            this.errors = []
            this.editedIndex = -1
            this.editedItem = {}
        },
        editItem(item) {
            this.clear()
            this.dialog = true
            this.title = 'Edit Expense Company #' + item.id
            this.editedIndex = this.items.indexOf(item)
            this.editedItem = this.makeNonReactiveCopy(item)
        },
        addItem() {
            this.dialog = true
            this.clear()
            this.title = 'New Expense Company'
            this.editedItem = {
                calculate_vat: 1,
                require_invoice: 1,
            }
        },
        close() {
            this.dialog = false
        },
        save() {
            this.loading = true
            if (this.editedIndex > -1) {
                axios.put('/expense-companies/' + this.editedItem.id, this.editedItem)
                    .then((response) => {
                        this.loading = false
                        Object.assign(this.items[this.editedIndex], response.data.data)
                        this.close()
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            } else {
                axios.post('/expense-companies', this.editedItem)
                    .then((response) => {
                        this.loading = false
                        this.items.unshift(response.data.data)
                        this.close()
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        }
    }
}
</script>