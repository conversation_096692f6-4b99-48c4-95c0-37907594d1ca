<template>
    <v-expansion-panel>
        <v-expansion-panel-content>
            <div slot="header"><strong>Filters</strong></div>
            <v-card>
                <v-card-text>
                    <v-container grid-list-md>
                        <v-layout wrap>
                            <v-flex xs12 sm2>
                                <v-menu
                                        ref="startDatePicker"
                                        lazy
                                        :close-on-content-click="false"
                                        v-model="startDatePicker"
                                        transition="scale-transition"
                                        offset-y
                                        full-width
                                        :nudge-right="40"
                                        min-width="290px"
                                        :return-value.sync="filters.start_date"
                                >
                                    <v-text-field
                                            slot="activator"
                                            label="Start Date"
                                            v-model="filters.start_date"
                                            prepend-icon="event"
                                            readonly
                                            clearable
                                    ></v-text-field>
                                    <v-date-picker v-model="filters.start_date"
                                                   @input="$refs.startDatePicker.save(filters.start_date)">
                                    </v-date-picker>
                                </v-menu>
                            </v-flex>
                            <v-flex xs12 sm2>
                                <v-menu
                                        ref="endDatePicker"
                                        lazy
                                        :close-on-content-click="false"
                                        v-model="endDatePicker"
                                        transition="scale-transition"
                                        offset-y
                                        full-width
                                        :nudge-right="40"
                                        min-width="290px"
                                        :return-value.sync="filters.end_date"
                                >
                                    <v-text-field
                                            slot="activator"
                                            label="End Date"
                                            v-model="filters.end_date"
                                            prepend-icon="event"
                                            readonly
                                            clearable
                                    ></v-text-field>
                                    <v-date-picker v-model="filters.end_date"
                                                   @input="$refs.endDatePicker.save(filters.end_date)">
                                    </v-date-picker>
                                </v-menu>
                            </v-flex>

                            <v-flex xs12 sm2>
                                <v-autocomplete
                                        :items="paymentMethods"
                                        v-model="filters.payment_method"
                                        label="Payment Method"
                                        item-text="name"
                                        item-value="value"
                                        prepend-icon="local_atm"
                                        clearable
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm2>
                                <v-autocomplete
                                        :items="paymentStatus"
                                        v-model="filters.payment_status"
                                        label="Payment Status"
                                        item-text="name"
                                        item-value="value"
                                        prepend-icon="local_atm"
                                        clearable
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm2 v-if="isCloudCartEmployee && ssUrl">
                                <v-autocomplete
                                        browser-autocomplete="chrome-off"
                                        :items="country"
                                        v-model="filters.country"
                                        label="Country"
                                        prepend-icon="language"
                                        item-text="name"
                                        item-value="code"
                                        clearable
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm2>
                                <v-autocomplete
                                        :items="invoiceStatus"
                                        v-model="filters.canceled"
                                        label="Invoice Status"
                                        item-text="name"
                                        item-value="value"
                                        prepend-icon="check_circle"
                                        clearable
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm2>
                                <v-autocomplete
                                        :items="apps"
                                        v-model="filters.app"
                                        label="App"
                                        item-text="name"
                                        item-value="id"
                                        prepend-icon="apps"
                                        clearable
                                >
                                    <template slot="item" slot-scope="data">
                                        <v-list-tile-avatar>
                                            <img :src="data.item.icon">
                                        </v-list-tile-avatar>
                                        <v-list-tile-content>
                                            <v-list-tile-title v-html="data.item.name"></v-list-tile-title>
                                            <v-list-tile-sub-title v-html="data.item.key"></v-list-tile-sub-title>
                                        </v-list-tile-content>
                                    </template>
                                </v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm2>
                                <v-autocomplete
                                        :items="services"
                                        v-model="filters.service"
                                        label="Service"
                                        item-text="name_translated"
                                        item-value="id"
                                        prepend-icon="money"
                                        clearable
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm2>
                                <v-autocomplete
                                        :items="plans"
                                        v-model="filters.plan"
                                        label="Plan"
                                        item-text="mapping"
                                        item-value="id"
                                        prepend-icon="attach_money"
                                        clearable
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm2>
                                <v-autocomplete
                                        :items="itemType"
                                        v-model="filters.type"
                                        label="Item Type"
                                        item-text="name"
                                        item-value="value"
                                        prepend-icon="check_circle"
                                        clearable
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm2>
                                <v-autocomplete
                                        :items="billingCycle"
                                        v-model="filters.cycle"
                                        label="Billing Cycle"
                                        item-text="text"
                                        item-value="value"
                                        prepend-icon="update"
                                        clearable
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm2>
                                <v-autocomplete
                                        :items="actions"
                                        v-model="filters.renew"
                                        label="Action"
                                        prepend-icon="update"
                                        item-text="name"
                                        item-value="value"
                                        clearable
                                ></v-autocomplete>
                            </v-flex>

                            <div v-if="ssUrl">
                                <v-autocomplete
                                        :items="levels"
                                        v-model="filters.reseller_level"
                                        label="Reseller Level"
                                        prepend-icon="people"
                                        small-chips
                                        clearable
                                        multiple
                                >
                                    <template v-slot:selection="data">
                                        <v-chip
                                                :selected="data.selected"
                                                small
                                                close
                                                @input="removeItem(data.item.id, 'reseller_level')"
                                        >
                                            <strong>{{ data.item.text }}</strong>
                                        </v-chip>
                                    </template>
                                </v-autocomplete>
                            </div>

                            <div v-if="ssUrl">
                                <v-autocomplete
                                        :items="partners"
                                        v-model="filters.reseller"
                                        label="Reseller Name"
                                        item-text="name"
                                        item-value="id"
                                        prepend-icon="people"
                                        small-chips
                                        clearable
                                        multiple
                                >
                                    <template v-slot:selection="data">
                                        <v-chip
                                                :selected="data.selected"
                                                small
                                                close
                                                @input="removeItem(data.item.id, 'reseller')"
                                        >
                                            <strong>{{ data.item.name }}</strong>
                                        </v-chip>
                                    </template>
                                </v-autocomplete>
                            </div>

                            <div v-if="ssUrl">
                                <v-autocomplete
                                        :items="experts"
                                        v-model="filters.expert"
                                        label="Expert Name"
                                        item-text="name"
                                        item-value="id"
                                        prepend-icon="people"
                                        small-chips
                                        clearable
                                        multiple
                                >
                                    <template v-slot:selection="data">
                                        <v-chip
                                                :selected="data.selected"
                                                small
                                                close
                                                @input="removeItem(data.item.id, 'expert')"
                                        >
                                            <strong>{{ data.item.name }}</strong>
                                        </v-chip>
                                    </template>
                                </v-autocomplete>
                            </div>

                            <v-flex xs12 sm2>
                                <v-autocomplete
                                        :items="users"
                                        v-model="filters.user"
                                        label="Issuer"
                                        item-text="name"
                                        item-value="user_id"
                                        prepend-icon="people"
                                        clearable
                                >
                                    <template slot="item" slot-scope="data">
                                        <v-list-tile-avatar>
                                            <img :src="data.item.google_account.avatar">
                                        </v-list-tile-avatar>
                                        <v-list-tile-content>
                                            <v-list-tile-title v-html="data.item.name"></v-list-tile-title>
                                            <v-list-tile-sub-title v-html="data.item.email"></v-list-tile-sub-title>
                                        </v-list-tile-content>
                                    </template>
                                </v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm2>
                                <v-autocomplete
                                        :items="ccUsers"
                                        v-model="filters.cc_user_id"
                                        label="Account Manager"
                                        item-text="name"
                                        item-value="user_id"
                                        prepend-icon="people"
                                        clearable
                                >
                                    <template slot="item" slot-scope="data">
                                        <v-list-tile-avatar>
                                            <img :src="data.item.google_account.avatar">
                                        </v-list-tile-avatar>
                                        <v-list-tile-content>
                                            <v-list-tile-title v-html="data.item.name"></v-list-tile-title>
                                            <v-list-tile-sub-title v-html="data.item.email"></v-list-tile-sub-title>
                                        </v-list-tile-content>
                                    </template>
                                </v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm2 v-if="ssUrl">
                                <v-autocomplete
                                        :items="yesNo"
                                        v-model="filters.has_reseller_payout_items"
                                        label="Has Payouts"
                                        prepend-icon="people"
                                        clearable
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm1 v-if="ssUrl">
                                <v-autocomplete
                                        :items="yesNo"
                                        v-model="filters.has_lta"
                                        label="LTA"
                                        prepend-icon="gavel"
                                        clearable
                                ></v-autocomplete>
                            </v-flex>

                             <v-flex xs12 sm2 v-if="isCloudCartEmployee && ssUrl">
                                <v-btn
                                        flat
                                        color="primary"
                                        :loading="loading"
                                        :disabled="loading"
                                        @click="exportExcel"
                                >
                                    <v-icon left dark>file_copy</v-icon>
                                    Export
                                </v-btn>
                            </v-flex>

                        </v-layout>
                    </v-container>
                </v-card-text>
            </v-card>
        </v-expansion-panel-content>
    </v-expansion-panel>
</template>

<script type="text/ecmascript-6">
import moment from 'moment'
import axios from 'axios'
import {flatten} from '@helpers'
import _ from "lodash"

export default {
    name: "filters",
    props: ['ssUrl'],
    data() {
        return {
            filters: {},
            loading: false,
            startDatePicker: false,
            endDatePicker: false,
            yesNo: [
                'Yes',
                'No',
            ],
            levels: [
                // {value: 0, text: 'Expert'},
                {value: 1, text: 'Level 1'},
                {value: 2, text: 'Level 2'},
                {value: 3, text: 'Level 3'},
            ],
            paymentMethods: [
                {
                    name: 'Bank Card',
                    value: 'card',
                },
                {
                    name: 'Bank Transfer',
                    value: 'bank',
                },
            ],
            paymentStatus: [
                {
                    name: 'Paid',
                    value: 'paid',
                },
                {
                    name: 'Unpaid',
                    value: 'unpaid',
                },
            ],
            invoiceStatus: [
                {
                    name: 'Active',
                    value: 0,
                },
                {
                    name: 'Canceled',
                    value: 1,
                },
            ],
            itemType: [
                {
                    name: 'Plan',
                    value: 'plan_details',
                },
                {
                    name: 'App',
                    value: 'cloudcart_app',
                },
                {
                    name: 'Service',
                    value: 'cloudcart_service',
                },
                {
                    name: 'CloudCart Service',
                    value: 'cc_service',
                },
                {
                    name: 'Ecosystem Service',
                    value: 'eco_service',
                },
                {
                    name: 'Feature Pack',
                    value: 'cloudcart_feature',
                },
            ],
            currencies: [
                'EUR',
                'BGN',
                'USD',
                'GBP',
            ],
            actions: [
                {
                    name: 'New',
                    value: 0,
                },
                {
                    name: 'Renew',
                    value: 1,
                },
                {
                    name: 'First Site Invoice',
                    value: -1,
                },
                {
                    name: 'Credit Note',
                    value: -2,
                },
            ],
            users: [
                {
                    google_account: {},
                }
            ],
            ccUsers: [],
            services: [],
            plans: [],
            apps: [],
            partners: [],
            experts: [],
            country: [],
        }
    },
    mounted() {
        this.getUsers()
        this.getCcUsers()
        this.getServices()
        this.getPlans()
        this.getApps()
        this.getResellers()
        this.getExperts()
        this.getLocales()
    },
    watch: {
        filters: {
            handler(newVal, oldVal) {
                this.$emit('filters:change')
            },
            deep: true
        },
    },
    methods: {
        removeItem(item, attribute) {
            this.filters[attribute].splice(this.filters[attribute].indexOf(item), 1)
        },
        getLocales() {
            axios.get('/invoices/countries')
                .then(response => {
                    this.country = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getResellers() {
            axios.get('/resellers/autocomplete')
                .then(response => {
                    this.partners = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getExperts() {
            axios.get('/experts/autocomplete')
                .then(response => {
                    this.experts = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getUsers() {
            axios.get('/users/autocomplete')
                .then(response => {
                    this.users = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getCcUsers() {
            axios.get('/employees/autocomplete')
                .then(response => {
                    this.ccUsers = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getServices() {
            axios.get('/services/autocomplete')
                .then(response => {
                    this.services = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getPlans() {
            axios.get('/plans')
                .then(response => {
                    this.plans = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getApps() {
            axios.get('/apps/paid')
                .then(response => {
                    this.apps = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        filterItems(items) {
            return items.filter((item) => {
                return this.filter(item)
            })
        },
        filter(item) {
            // if (item.model_type === 'plan_details' && !item.model) {
            //     console.log(item)
            // }
            let startDate = !this.filters.start_date || moment(item.created_at).isSameOrAfter(this.filters.start_date, 'day')
            let endDate = !this.filters.end_date || moment(item.created_at).isSameOrBefore(this.filters.end_date, 'day')
            let paymentMethod = _.isUndefined(this.filters.payment_method) || (item.invoice.payment_method === this.filters.payment_method)
            let paymentStatus = _.isUndefined(this.filters.payment_status) || (item.invoice.payment_status === this.filters.payment_status)
            let invoiceStatus = _.isUndefined(this.filters.canceled) || (item.invoice.canceled === this.filters.canceled)
            let currency = _.isUndefined(this.filters.currency) || (item.currency === this.filters.currency)
            let issuer = _.isUndefined(this.filters.user) || (item.invoice.issuer_id === this.filters.user)
            let service = _.isUndefined(this.filters.service) || (item.model_type === 'cloudcart_service' && item.model_id === this.filters.service)
            let plan = _.isUndefined(this.filters.plan) || (item.model_type === 'plan_details' && item.model && item.model.plan_id === this.filters.plan)
            let app = _.isUndefined(this.filters.app) || (item.model_type === 'cloudcart_app' && item.model_id === this.filters.app)
            let type = _.isUndefined(this.filters.type) || (item.model_type === this.filters.type)
            let cycle = _.isUndefined(this.filters.cycle) || (item.model && item.model.billing_cycle === this.filters.cycle)
            let renew = this.filters.renew
            if (renew === -1) {
                renew = item.invoice && item.invoice.site_first_invoice === 1;
            } else {
                renew = _.isUndefined(renew) || (item.invoice && item.invoice.renew === renew);
            }

            return startDate && endDate && paymentMethod && paymentStatus && invoiceStatus && currency && issuer &&
                service && plan && app && type && renew && cycle
        },
        exportExcel() {
            this.loading = true
            axios.post('/invoices/items/export', {filters: this.filters}, {
                responseType: 'arraybuffer',
            })
                .then(response => {
                    const url = URL.createObjectURL(new Blob([response.data]));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', 'invoice-items.xls');
                    document.body.appendChild(link);
                    link.click();
                    URL.revokeObjectURL(url)
                    this.loading = false
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    }
}
</script>
