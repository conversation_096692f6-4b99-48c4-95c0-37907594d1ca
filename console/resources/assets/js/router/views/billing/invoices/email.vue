<template>
    <v-dialog
            v-model="dialog"
            max-width="500px"
            scrollable
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h3 class="headline white--text mb-0">Send Email</h3>
            </v-card-title>

            <v-card-text>
                <v-container grid-list-md>
                    <v-layout wrap>
                        <v-flex xs12>
                            <v-text-field
                                    label="Name"
                                    v-model="mail.name"
                                    :error-messages="errors.name"
                            ></v-text-field>
                        </v-flex>

                        <v-flex xs12>
                            <v-text-field
                                    label="Email"
                                    v-model="mail.email"
                                    :error-messages="errors.email"
                            ></v-text-field>
                        </v-flex>

                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                <v-btn
                        color="primary"
                        :loading="loading"
                        :disabled="loading"
                        flat
                        @click.native="send"
                >Send
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
    import axios from 'axios'

    export default {
        name: "email",
        props: ['mail'],
        data() {
            return {
                dialog: false,
                loading: false,
                errors: [],
            }
        },
        mounted() {
        },
        methods: {
            show() {
                this.dialog = true
            },
            close() {
                this.dialog = false
                this.errors = []
            },
            send() {
                this.loading = true
                axios.post('/invoices/' + this.mail.invoice_id + '/email', this.mail)
                        .then((response) => {
                            this.handleSuccess(response)
                            this.close()
                        })
                        .catch(error => {
                            this.handleError(error)
                        })
            }
        }
    }
</script>