<template>
    <layout-default>

        <v-container v-if="item" grid-list-md fluid>
            <v-layout row wrap>
                <v-flex xs12 sm6>
                    <v-card>
                        <v-card-title primary-title>
                            <v-flex xs12>
                                <span class="pull-right mt-2 grey--text caption">
                                  # {{ item.id }}
                                </span>
                                <h3 class="headline">
                                    Offer <span class="grey--text caption">{{ item.unique_id }}</span>
                                </h3>
                            </v-flex>
                        </v-card-title>
                        <v-card-text>
                            <v-layout row wrap>
                                <v-flex xs6>
                                    <div>Items:</div>
                                    <div>Amount:</div>
                                    <div>Created:</div>
                                    <div>Expire / Updated:</div>
                                    <div>Status:</div>
                                </v-flex>
                                <v-flex xs6>
                                    <div><strong>{{ item.items_count }}</strong></div>
                                    <div><strong>{{ item.amount | toCurrency('EUR') }}</strong></div>
                                    <div><strong>{{ item.created_at | formatDateTime }}</strong></div>
                                    <div><strong>{{ item.expire_at | formatDate }} / {{
                                            item.updated_at | formatDateTime
                                        }}</strong></div>
                                    <div><strong>{{ item.status_name }}</strong></div>
                                </v-flex>
                            </v-layout>
                        </v-card-text>
                        <v-card-actions>
                            <v-btn flat color="primary" @click.native="purchaseLink">
                                Purchase Link
                            </v-btn>
                            <v-btn flat color="primary" @click.native="$refs.editDialog.show(item)">
                                Send Email
                            </v-btn>
                        </v-card-actions>
                    </v-card>
                </v-flex>

                <v-flex xs12 sm6>
                    <v-card>

                        <v-list two-line class="transparent">
                            <v-list-tile>
                                <v-list-tile-avatar>
                                    <v-avatar>
                                        <v-icon large>store</v-icon>
                                    </v-avatar>
                                </v-list-tile-avatar>
                                <v-list-tile-content>
                                    <v-list-tile-title>Site:
                                        <router-link :to="{ name: 'sites.show', params: { id: item.site_id }}">
                                            {{ item.site.primary_host }}
                                        </router-link>
                                    </v-list-tile-title>
                                    <v-list-tile-sub-title># {{ item.site_id }}</v-list-tile-sub-title>
                                </v-list-tile-content>
                            </v-list-tile>
                        </v-list>

                        <v-list two-line class="transparent">
                            <v-list-tile>
                                <v-list-tile-avatar>
                                    <v-avatar>
                                        <img :src="item.user.avatar" alt="Avatar">
                                    </v-avatar>
                                </v-list-tile-avatar>
                                <v-list-tile-content>
                                    <v-list-tile-title>Merchant:
                                        <router-link
                                            :to="{ name: 'merchants.show', params: { uid: item.user.unique_id }}">
                                            {{ item.user.name }}
                                        </router-link>
                                    </v-list-tile-title>
                                    <v-list-tile-sub-title>{{ item.user.email }} / {{
                                            item.user.phone
                                        }}
                                    </v-list-tile-sub-title>
                                </v-list-tile-content>
                            </v-list-tile>
                        </v-list>

                        <v-list two-line class="transparent mt-2">
                            <v-list-tile>
                                <v-list-tile-avatar>
                                    <v-avatar>
                                        <img :src="item.owner.avatar" alt="Avatar">
                                    </v-avatar>
                                </v-list-tile-avatar>
                                <v-list-tile-content>
                                    <v-list-tile-title>Owner: {{ item.owner.name }}</v-list-tile-title>
                                    <v-list-tile-sub-title>{{ item.owner.email }}</v-list-tile-sub-title>
                                </v-list-tile-content>
                            </v-list-tile>
                        </v-list>
                    </v-card>
                </v-flex>

                <v-flex xs12>
                    <v-tabs>
                        <v-tab key="1">Items</v-tab>
                        <v-tab key="2">Transactions</v-tab>

                        <v-tabs-items :touchless="true">
                            <v-tab-item key="1">
                                <offer-items-list
                                    :items="items"
                                    :offer="item"
                                    v-on:offer:refresh="() => this.getItem()"
                                ></offer-items-list>
                            </v-tab-item>
                            <v-tab-item key="2">
                                <v-card>
                                    <v-card-title>
                                        <h1>Transactions</h1>
                                    </v-card-title>
                                </v-card>
                                <transaction-list :items="transactions"></transaction-list>
                            </v-tab-item>
                        </v-tabs-items>
                    </v-tabs>
                </v-flex>

            </v-layout>
        </v-container>

        <offer-edit-mail
            ref="editDialog"
            v-on:offer:refresh="() => this.getItem()"
        />
    </layout-default>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import NProgress from 'nprogress/nprogress'
import LayoutDefault from '@layouts/Default'
import OfferItemsList from "./list-items";
import OfferEditMail from "./edit-mail";
import TransactionList from '@views/billing/transactions/list'

export default {
    name: "show",
    components: {
        LayoutDefault,
        OfferItemsList,
        OfferEditMail,
        TransactionList,
    },
    data() {
        return {
            item: null,
            items: [],
            transactions: [],
        }
    },
    created() {
        this.getItem()
        this.getItems()
        this.getTransactions()
    },
    methods: {
        getItem() {
            NProgress.start()
            axios.get(`/offers/${this.$route.params.uid}`)
                .then(response => {
                    this.item = response.data.data
                    NProgress.done()
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getItems() {
            NProgress.start()
            axios.get(`/offers/${this.$route.params.uid}/items`)
                .then(response => {
                    this.items = response.data.data
                    NProgress.done()
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getTransactions() {
            axios.get(`/offers/${this.$route.params.uid}/transactions`)
                .then(response => {
                    this.transactions = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        purchaseLink() {
            this.copyToClipboard(this.item.site.url + '/admin/offers/' + this.item.unique_id)
            let success = {
                data: {
                    message: "Purchase link copied to clipboard!",
                }
            }
            this.handleSuccess(success)
        },
        copyToClipboard(str) {
            const el = document.createElement('textarea')
            el.addEventListener('focusin', e => e.stopPropagation())
            el.value = str
            document.body.appendChild(el)
            el.select()
            document.execCommand('copy')
            document.body.removeChild(el)
        },

    }
}
</script>
