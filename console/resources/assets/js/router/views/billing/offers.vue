<template>
    <layout-default>

        <v-container grid-list-md fluid>
            <list
                :loading="loading"
                :items="items"
                ref="list"
                ss-url="/offers"
            ></list>
        </v-container>

    </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import {flatten} from '@helpers'
import List from '@views/billing/offers/list'

export default {
    components: {
        LayoutDefault,
        List,
    },
    data() {
        return {
            items: [],
            loading: true,
        }
    },
    mounted() {
        this.$refs.list.getItems()
        // this.getItems()
    },
    methods: {
        getItems() {
            this.loading = true
            axios.get('/offers')
                .then(response => {
                    this.loading = false
                    this.items = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    },
}
</script>
