<template>
    <v-card>

        <v-card>
            <v-card-title>
                <h1>3DS Verifications</h1>
                <v-spacer></v-spacer>
                <v-text-field
                    append-icon="search"
                    label="Search"
                    single-line
                    v-model="search"
                    clearable
                ></v-text-field>
            </v-card-title>
        </v-card>

        <v-data-table
            :headers="headers"
            :items="items"
            :search="search"
            :pagination.sync="pagination"
            :total-items="totalItems"
            :loading="loading"
            :custom-filter="customFilter"
            class="elevation-1"
        >
            <template slot="items" slot-scope="props">
                <tr>
                    <td>{{ props.item.created_at | formatDateTime }}</td>
                    <td v-if="showCustomer">
                        <router-link :to="{ name: 'merchants.show', params: { uid: props.item.customer_id }}">
                            {{ props.item.customer_id }}
                        </router-link>
                    </td>
                    <td>{{ props.item.method_info.issuingBank }} {{ props.item.method_info.countryOfIssuance }}</td>
                    <td>
                        <v-list two-line dense class="transparent list-p0">
                            <v-list-tile>
                                <v-list-tile>
                                    <img :src="props.item.method_info.imageUrl" alt="Avatar">
                                </v-list-tile>
                                <v-list-tile-content>
                                    <v-list-tile-title>{{ props.item.method_info.maskedNumber }}</v-list-tile-title>
                                    <v-list-tile-sub-title>{{ props.item.method_info.expirationDate }}
                                    </v-list-tile-sub-title>
                                </v-list-tile-content>
                            </v-list-tile>
                        </v-list>
                    </td>
                    <td>
                        <a @click="$refs.jsonDialog.viewData(props.item.method_info, 'Payment Method Info')">
                            {{ props.item.token }}
                        </a>
                    </td>
                    <td v-if="props.item.nonce_info">
                        <a @click="$refs.jsonDialog.viewData(props.item.nonce_info, 'Payment Nonce Info')">
                            {{ props.item.nonce_info.nonce }}
                        </a>
                    </td>
                    <td v-else></td>
                    <td v-if="props.item.verification_info">
                        Amount: <strong>{{ props.item.verification_info.amount * 100 | toCurrency(props.item.verification_info.currencyIsoCode) }}</strong><br>
                        <a @click="$refs.jsonDialog.viewData(props.item.verification_info, 'Verification Info')">
                            {{ props.item.verification_info.processorResponseCode }}:
                            {{ props.item.verification_info.processorResponseText }}
                        </a>
                    </td>
                    <td v-else></td>
                </tr>
            </template>

            <v-alert slot="no-results" :value="true" color="error" icon="warning">
                Your search for "{{ search }}" found no results.
            </v-alert>

        </v-data-table>
        <json-dialog ref="jsonDialog"></json-dialog>
    </v-card>
</template>

<script type="text/ecmascript-6">
import JsonDialog from "../../../components/JsonDialog";

export default {
    name: "verifications-list",
    components: {JsonDialog},
    props: ['loading', 'items', 'showCustomer'],
    data() {
        return {
            dialog: false,
            search: '',
            totalItems: 0,
            pagination: {
                rowsPerPage: 25,
                descending: true,
            },
        }
    },
    watch: {},
    computed: {
        headers() {
            let headers = [
                {text: 'Created At', value: 'created_at'},
                {text: 'Customer', value: 'customer_id'},
                {text: 'Bank', value: 'method_info.issuingBank'},
                {text: 'Card', value: 'method_info.maskedNumber'},
                {text: 'Token', value: 'token'},
                {text: '3DS Nonce', value: 'nonce_info.nonce'},
                {text: 'Verification', value: 'verification_info.amount'},
            ]

            if (!this.showCustomer) {
                delete headers[1]
            }

            return headers
        },
    },
    mounted() {
    },
    methods: {
        customFilter(items, search, filter) {
            search = search.toString().toLowerCase()
            if (search.trim() === '') return items

            return items.filter(item => item.flatten.some(val => filter(val, search)))
        },
        close() {
            this.dialog = false
        }
    },
}
</script>