<template>
    <v-dialog
            v-model="dialog"
            max-width="600px"
            scrollable
            persistent
    >
        <v-form ref="form" v-model="valid" lazy-validation>
            <v-card>
                <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                    <h3 class="headline white--text mb-0">New Subscription</h3>
                </v-card-title>

                <v-card-text>
                    <v-container grid-list-md>
                        <v-layout wrap>

                            <v-flex xs12 v-if="showType">
                                <v-autocomplete
                                        :items="itemType"
                                        v-model="type"
                                        label="Item Type"
                                        item-text="name"
                                        item-value="value"
                                        prepend-icon="shopping_cart"
                                        @change="expertService = {}"
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 v-if="type === 'cloudcart_app'">
                                <v-autocomplete
                                        :items="apps"
                                        v-model="item"
                                        label="App"
                                        item-text="name"
                                        item-value="key"
                                        prepend-icon="apps"
                                        clearable
                                        required
                                        return-object
                                        persistent-hint
                                        :hint="item.billing_period ? `Billing Cycle: ${item.billing_period}` : ''"
                                        :rules="[v => !!v || 'Item is required']"
                                        :error-messages="errors.id"
                                >
                                    <template slot="item" slot-scope="data">
                                        <v-list-tile-avatar tile>
                                            <img :src="data.item.icon">
                                        </v-list-tile-avatar>
                                        <v-list-tile-content>
                                            <v-list-tile-title v-html="data.item.name"></v-list-tile-title>
                                            <v-list-tile-sub-title v-html="data.item.key"></v-list-tile-sub-title>
                                        </v-list-tile-content>
                                    </template>
                                </v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm4 v-if="type === 'cloudcart_service'">
                                <v-autocomplete
                                        :items="serviceTypes"
                                        v-model="serviceType"
                                        label="Service Type"
                                        item-text="name"
                                        item-value="value"
                                        prepend-icon="hub"
                                        @change="categoryId = null; services = []; item = {}; expertService = {}"
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 sm8 v-if="type === 'cloudcart_service'">
                                <v-autocomplete
                                        :items="categories"
                                        v-model="categoryId"
                                        label="Category"
                                        item-text="display_name"
                                        item-value="id"
                                        prepend-icon="category"
                                        @change="getServices(); item = {}; expertService = {}"
                                >
                                    <template v-slot:item="data">
                                        <v-list-tile-content>
                                            <v-list-tile-title>
                                                {{ data.item.display_name }}
                                            </v-list-tile-title>
                                        </v-list-tile-content>
                                        <v-list-tile-action>
                                            <v-chip
                                                    small
                                                    color="grey lighten-1"
                                            >
                                                {{
                                                    serviceType === 1 ?
                                                        data.item.ecosystem_services_count_active :
                                                        data.item.cc_services_count_active
                                                }}
                                            </v-chip>
                                        </v-list-tile-action>
                                    </template>
                                </v-autocomplete>
                            </v-flex>

                            <v-flex xs12 v-if="type === 'cloudcart_service' && serviceType === 0">
                                <v-autocomplete
                                        :items="services"
                                        v-model="item"
                                        label="CloudCart Service"
                                        item-text="name_translated"
                                        item-value="id"
                                        prepend-icon="money"
                                        return-object
                                        clearable
                                        :error-messages="errors.id"
                                >
                                    <template v-slot:item="data">
                                        <v-list-tile-action>
                                            <v-chip
                                                    small
                                                    color="green lighten-1"
                                            >
                                                {{ data.item.price | toCurrency('EUR') }}
                                            </v-chip>
                                        </v-list-tile-action>
                                        <v-list-tile-content>
                                            <v-list-tile-title>
                                                {{ data.item.name_translated | stripHtml(70) }}
                                            </v-list-tile-title>
                                        </v-list-tile-content>
                                    </template>
                                </v-autocomplete>
                            </v-flex>

                            <v-flex xs12 v-if="type === 'cloudcart_service' && serviceType === 1">
                                <v-autocomplete
                                        :items="services"
                                        v-model="item"
                                        label="Ecosystem Service"
                                        item-text="name_translated"
                                        item-value="id"
                                        prepend-icon="money"
                                        return-object
                                        clearable
                                        :error-messages="errors.id"
                                        @change="expertServices = []; expertService = {}; getExpertServices()"
                                >
                                    <template v-slot:item="data">
                                        <v-list-tile-content>
                                            <v-list-tile-title>
                                                {{ data.item.name_translated }}
                                            </v-list-tile-title>
                                        </v-list-tile-content>
                                        <v-list-tile-action>
                                            <v-chip
                                                    small
                                                    color="grey lighten-1"
                                            >
                                                {{ data.item.expert_services_count_active }}
                                            </v-chip>
                                        </v-list-tile-action>
                                    </template>
                                </v-autocomplete>
                            </v-flex>

                            <v-flex xs12 v-if="type === 'cloudcart_service' && serviceType === 1">
                                <v-autocomplete
                                        :items="expertServices"
                                        v-model="expertService"
                                        label="Expert Service Price"
                                        item-text="name_translated"
                                        item-value="id"
                                        prepend-icon="money"
                                        return-object
                                        clearable
                                        :error-messages="errors.expert_service_id"
                                >
                                    <template v-slot:item="data">
                                        <v-list-tile-action>
                                            <v-chip
                                                    small
                                                    color="green lighten-1"
                                            >
                                                {{ data.item.price | toCurrency('EUR') }}
                                            </v-chip>
                                        </v-list-tile-action>
                                        <v-list-tile-content>
                                            <v-list-tile-title>
                                                {{ data.item.name_translated }}
                                            </v-list-tile-title>
                                        </v-list-tile-content>
                                    </template>
                                </v-autocomplete>
                            </v-flex>

                            <v-flex xs12 v-if="type === 'cloudcart_feature'">
                                <v-autocomplete
                                        :items="features"
                                        v-model="item"
                                        label="Feature Pack"
                                        item-text="text"
                                        item-value="id"
                                        prepend-icon="attach_money"
                                        return-object
                                        clearable
                                        persistent-hint
                                        :hint="item.billing_period ? `Billing Cycle: ${item.billing_period}` : ''"
                                        :error-messages="errors.id"
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 v-if="type === 'plan_details'">
                                <v-autocomplete
                                        :items="plans"
                                        v-model="item"
                                        label="Plan"
                                        item-text="text"
                                        item-value="id"
                                        prepend-icon="attach_money"
                                        return-object
                                        clearable
                                        persistent-hint
                                        :hint="item?.billing_period ? `Billing Cycle: ${item.billing_period}` : ''"
                                        :error-messages="errors.id"
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 v-if="type === 'theme'">
                                <v-autocomplete
                                        :items="themes"
                                        v-model="item"
                                        label="Theme"
                                        item-text="name"
                                        item-value="id"
                                        prepend-icon="desktop_windows"
                                        clearable
                                        required
                                        return-object
                                        persistent-hint
                                        :rules="[v => !!v || 'Item is required']"
                                        :error-messages="errors.id"
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12 v-if="item.dynamic_pricing">
                                <v-autocomplete
                                        :items="featurePricing"
                                        v-model="featureValue"
                                        label="Select Value"
                                        item-text="value"
                                        prepend-icon="attach_money"
                                        return-object
                                        clearable
                                        :error-messages="errors.value"
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs6>
                                <v-text-field
                                        v-model="item.price_input"
                                        label="Price"
                                        prepend-icon="attach_money"
                                        required
                                        :suffix="currency"
                                        :error-messages="errors.price"
                                        persistent-hint
                                        :hint="item.price_input ? hintWithVat(item.price_input) : ''"
                                ></v-text-field>
                            </v-flex>

                            <v-flex xs6>
                                <v-text-field
                                        v-model="item.original_price_input"
                                        label="Original Price"
                                        required
                                        :suffix="currency"
                                        persistent-hint
                                        readonly
                                        disabled
                                        :hint="item.original_price_input ? hintWithVat(item.original_price_input) : ''"
                                ></v-text-field>
                            </v-flex>

                            <v-flex xs12 v-if="item.billing_cycle">
                                <v-text-field
                                        v-model="nextBillingAmount"
                                        label="Next Billing Amount"
                                        prepend-icon="attach_money"
                                        required
                                        :suffix="currency"
                                        :error-messages="errors.next_billing_amount"
                                        persistent-hint
                                        :hint="hintWithVat(nextBillingAmount)"
                                ></v-text-field>
                            </v-flex>

                            <!--                            <v-flex xs6 v-if="item.billing_cycle">-->
                            <!--                                <v-menu-->
                            <!--                                    ref="billingDate"-->
                            <!--                                    lazy-->
                            <!--                                    :close-on-content-click="false"-->
                            <!--                                    v-model="billingDate"-->
                            <!--                                    transition="scale-transition"-->
                            <!--                                    offset-y-->
                            <!--                                    full-width-->
                            <!--                                    :nudge-right="40"-->
                            <!--                                    min-width="290px"-->
                            <!--                                    :return-value.sync="nextBillingDate"-->
                            <!--                                >-->
                            <!--                                    <v-text-field-->
                            <!--                                        slot="activator"-->
                            <!--                                        label="Next Billing Date"-->
                            <!--                                        v-model="nextBillingDate"-->
                            <!--                                        prepend-icon="event"-->
                            <!--                                        readonly-->
                            <!--                                        clearable-->
                            <!--                                        :error-messages="errors.next_billing_date"-->
                            <!--                                    ></v-text-field>-->
                            <!--                                    <v-date-picker v-model="nextBillingDate"-->
                            <!--                                                   :allowed-dates="futureDates"-->
                            <!--                                                   @input="$refs.billingDate.save(nextBillingDate)">-->
                            <!--                                    </v-date-picker>-->
                            <!--                                </v-menu>-->
                            <!--                            </v-flex>-->

                            <v-flex xs12>
                                <v-autocomplete
                                        :items="sites"
                                        v-model="site"
                                        label="Site"
                                        item-text="text"
                                        item-value="id"
                                        prepend-icon="store"
                                        required
                                        return-object
                                        :rules="[v => !!v || 'Item is required']"
                                        :error-messages="errors.site_id"
                                        :readonly="siteReadOnly"
                                        :clearable="!siteReadOnly"
                                ></v-autocomplete>
                            </v-flex>

                            <v-flex xs12>
                                <v-card v-if="site.id">
                                    <v-btn
                                            small
                                            absolute
                                            dark
                                            fab
                                            bottom
                                            right
                                            color="teal"
                                            @click="$refs.invoicingDialog.newInvoicing(site.invoicing)"
                                    >
                                        <v-icon>edit</v-icon>
                                    </v-btn>
                                </v-card>
                                <v-card v-if="site.invoicing">
                                    <v-card-text>
                                        <div><strong>{{ site.invoicing.company }}</strong></div>
                                        <div>{{ site.invoicing.company_id }}</div>
                                        <div>{{ site.invoicing.vat }}</div>
                                        <div>{{ site.invoicing.country_name }} / {{ site.invoicing.lang_name }}</div>
                                        <div>{{ site.invoicing.city }}</div>
                                        <div>{{ site.invoicing.address }}</div>
                                        <div>{{ site.invoicing.name }}</div>
                                    </v-card-text>
                                </v-card>
                            </v-flex>

                            <v-flex xs12>
                                <v-autocomplete
                                        :items="paymentType"
                                        v-model="paymentMethod"
                                        label="Payment Method"
                                        item-text="name"
                                        item-value="value"
                                        prepend-icon="local_atm"
                                        required
                                        :error-messages="errors.payment_method"
                                ></v-autocomplete>
                                <v-list two-line dense class="transparent list-p0"
                                        v-if="bankCard && paymentMethod === 'card'">
                                    <v-list-tile>
                                        <v-list-tile>
                                            <img :src="bankCard.icon" alt="Avatar">
                                        </v-list-tile>
                                        <v-list-tile-content>
                                            <v-list-tile-title>{{ bankCard.number }}</v-list-tile-title>
                                            <v-list-tile-sub-title>{{ bankCard.bank }}</v-list-tile-sub-title>
                                        </v-list-tile-content>
                                    </v-list-tile>
                                </v-list>
                            </v-flex>

                        </v-layout>
                    </v-container>
                </v-card-text>

                <v-divider></v-divider>

                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                    <v-btn
                            color="primary"
                            :loading="loading"
                            :disabled="loading"
                            flat
                            @click.native="save"
                    >Save
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-form>

        <invoicing :site="site" ref="invoicingDialog"></invoicing>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import moment from 'moment'
import store from '@state/store'
import Invoicing from '@views/sites/invoicing'
import _ from "lodash"

export default {
    name: "sell",
    props: ['items'],
    components: {
        Invoicing,
    },
    data() {
        return {
            showType: true,
            siteReadOnly: false,
            valid: true,
            loading: false,
            dialog: false,
            billingDate: false,
            nextBillingDate: null,
            nextBillingAmount: null,
            site: {},
            item: {},
            sites: [],
            plans: [],
            apps: [],
            services: [],
            features: [],
            themes: [],
            categoryId: null,
            expertService: {},
            expertServices: [],
            errors: [],
            type: 'plan_details',
            paymentMethods: [],
            paymentMethod: {},
            bankCard: {},
            featureValue: {},
            featurePricing: [],
            paymentType: [
                {
                    name: 'Bank Card',
                    value: 'card',
                }
            ],
            itemType: [
                {
                    name: 'Plan',
                    value: 'plan_details',
                },
                {
                    name: 'App',
                    value: 'cloudcart_app',
                },
                {
                    name: 'Service',
                    value: 'cloudcart_service',
                },
                {
                    name: 'Feature Pack',
                    value: 'cloudcart_feature',
                },
                {
                    name: 'Theme',
                    value: 'theme',
                },
            ],
            serviceType: 0,
            serviceTypes: [
                {
                    name: 'CloudCart',
                    value: 0,
                },
                {
                    name: 'Ecosystem',
                    value: 1,
                },
            ],
        }
    },
    mounted() {
    },
    computed: {
        categories() {
            if (this.serviceType) {
                return this.groups.filter(item => item.ecosystem_services_count_active > 0)
            } else {
                return this.groups.filter(item => item.cc_services_count_active > 0)
            }
        },
        currency() {
            return this.item ? this.item.currency : null
        },
    },
    watch: {
        type() {
            this.item = {}
            this.errors = []
        },
        paymentMethod(val) {
            if (val) {
                delete this.errors.payment_method
            } else {
                this.errors.payment_method = ['Missing site owner bank card details']
            }
        },
        item(val) {
            this.errors = []
            if (val === undefined) {
                this.item = {}
            }

            if (this.item.billing_cycle) {
                this.nextBillingAmount = this.item.price_input
                this.nextBillingDate = moment().add(this.item.billing_cycle, 'M').format('YYYY-MM-DD')
            } else {
                this.nextBillingAmount = null
                this.nextBillingDate = null
            }

            this.featureValue = {
                value: null
            }

            if (this.item.dynamic_pricing) {
                this.getFeaturePricing(this.item)
            } else {
                this.featureValue = {
                    value: this.item.value
                }
            }
        },
        expertService(val) {
            this.item = {
                ...this.item,
                price_input: val.price_input,
                original_price_input: val.price_input,
                billing_cycle: val.billing_cycle,
            }
        },
        site(val) {
            this.paymentMethod = this.bankCard = null

            if (!val) {
                this.site = {}
                return
            }

            delete this.errors.site_id

            if (val && val.user) {
                let uid = val.user.braintree_id
                if (uid) {
                    if (this.paymentMethods[uid]) {
                        this.bankCard = this.paymentMethods[uid]
                        this.paymentMethod = 'card'
                    } else {
                        this.getPaymentMethod(uid)
                    }
                }
                this.paymentType = [
                    {
                        name: 'Bank Card',
                        value: 'card',
                    }
                ]
                if (val.bank_transfer) {
                    this.paymentType.push(
                        {
                            name: 'Bank Transfer',
                            value: 'bank',
                        }
                    )
                }
            }
        }
    },
    methods: {
        init() {
            if (_.isEmpty(this.apps)) {
                this.getProducts('apps')
            }
            if (_.isEmpty(this.groups)) {
                this.getGroups()
            }
            if (_.isEmpty(this.plans)) {
                this.getProducts('plans')
            }
            if (_.isEmpty(this.features)) {
                this.getProducts('features')
            }
            if (_.isEmpty(this.themes)) {
                this.getProducts('themes')
            }
        },
        hintWithVat(price) {
            if (this.item === undefined || !price) {
                return ''
            }

            price = price * 100 * 1.2
            let priceFormatted = this.$options.filters.toCurrency(price)

            return 'Price with VAT: ' + priceFormatted
        },
        futureDates(date) {
            return moment(date).isAfter(moment())
        },
        getGroups() {
            axios.get('/service-groups')
                .then(response => {
                    this.groups = response.data.data
                    this.groups = _.sortBy(this.groups, 'name_translated')
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getServices() {
            let url = '/services/category/'
            if (this.serviceType === 1) {
                url = '/ecosystem/services/category/'
            }

            axios.get(url + this.categoryId)
                .then(response => {
                    this.services = response.data.data.map(item => {
                        return {
                            ...item,
                            name_translated: this.serviceType === 1 ?
                                item.name_translated : item.name_translated + ' | ' + item.billing_period,
                            price_input: item.price_input,
                            original_price_input: item.price_input
                        }
                    })
                    this.services = _.sortBy(this.services, 'name_translated')
                })
                .catch(error => {
                    this.handleError(error)
                });
        },
        getExpertServices() {
            let url = `/ecosystem/services/${this.item.id}/related-expert-services`
            let price = 0
            this.expertServices = []
            axios.get(url)
                .then(response => {
                    this.expertServices = response.data.data.map(item => {
                        price = item.price_input
                        return {
                            ...item,
                            name_translated: item.expert.name + ' | ' +
                                item.commission + '% | ' +
                                item.execution_time + ' days | ' +
                                item.billing_period,
                            price_input: price,
                            original_price_input: price
                        }
                    })
                    this.expertServices = _.sortBy(this.expertServices, 'price')
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getSites() {
            axios.get('/sites/autocomplete')
                .then(response => {
                    this.loading = false
                    this.sites = response.data.data.map(item => {
                        if (!item.invoicing) {
                            item.invoicing = {
                                user_id: item.user_id,
                                site_id: item.id,
                                country: item.user.country,
                                lang: item.user.lang,
                                name: item.user.name,
                            }
                        }
                        return {...item, text: '#' + item.id + ' ' + item.primary_host}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getPaymentMethod(uid) {
            axios.get(`/merchants/${uid}/payment-method`)
                .then(response => {
                    let paymentMethod = response.data.data
                    if (paymentMethod.bin) {
                        this.bankCard = this.paymentMethods[uid] = {
                            icon: paymentMethod.imageUrl,
                            number: paymentMethod.maskedNumber + ' ' + paymentMethod.expirationDate,
                            bank: paymentMethod.issuingBank + ' ' + paymentMethod.countryOfIssuance
                        }
                        this.paymentMethod = 'card'
                    } else {
                        this.paymentMethod = this.bankCard = null
                    }
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        sell(item, type, site) {
//                this.$refs.form.reset()
            this.init()
            if (item && type) {
                this.type = type
                this.showType = false
            }

            this.dialog = true
            this.valid = true
            this.errors = []
            this.item = this.makeNonReactiveCopy(item)
            if (site) {
                this.siteReadOnly = true
                this.site = {...site, text: '#' + site.id + ' ' + site.primary_host}
                this.sites.push(this.site)
            } else {
                this.site = {}
            }
            // if (!this.sites.length) {
            //     this.getSites()
            // }
        },
        close() {
            this.dialog = false
        },
        createRequest() {
            return {
                type: this.type,
                id: this.item.id,
                price: this.item.price_input,
                site_id: this.site.id,
                payment_method: this.paymentMethod,
                billing_cycle: this.item.billing_cycle,
                next_billing_amount: this.nextBillingAmount,
                service_type: this.serviceType,
                expert_service_id: this.expertService.id,
                value: this.featureValue ? this.featureValue.value : null,
            }
        },
        save() {
            if (this.$refs.form.validate()) {
                this.loading = true
                const request = this.createRequest()
                axios.post('/subscriptions', request)
                    .then((response) => {
                        this.handleSuccess(response)
                        // console.log([response.data.data, this.items])
                        if (this.items) {
                            let item = response.data.data
                            let editedIndex = -1
                            this.items.forEach(function (i, index) {
                                if (item.id === i.id) {
                                    editedIndex = index;
                                }
                            })
                            if (editedIndex >= 0) {
                                Object.assign(this.items[editedIndex], item)
                            } else {
                                this.items.unshift(item)
                            }
                        }
                        this.close()
                        this.$emit('subscription:new')
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        },
        purchaseLink() {
            this.loading = true
            const request = this.createRequest()
            axios.post('/subscriptions/purchase-link', request)
                .then((response) => {
                    this.copyToClipboard(response.data.link)
                    this.handleSuccess(response)
                    this.close()
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        copyToClipboard(str) {
            const el = document.createElement('textarea')
            el.addEventListener('focusin', e => e.stopPropagation())
            el.value = str
            document.body.appendChild(el)
            el.select()
            document.execCommand('copy')
            document.body.removeChild(el)
        }
    }
}
</script>
