<template>
    <v-dialog
        v-model="dialog"
        max-width="500px"
        scrollable
    >
        <v-form ref="form" v-model="valid" lazy-validation style="width: 100%">
            <v-card>
                <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                    <h3 class="headline white--text mb-0">{{ title }}</h3>
                </v-card-title>

                <v-card-text>
                    <v-flex xs12>
                        <v-menu
                            ref="datePicker"
                            lazy
                            :close-on-content-click="false"
                            v-model="datePicker"
                            transition="scale-transition"
                            offset-y
                            full-width
                            :nudge-right="40"
                            min-width="290px"
                            :return-value.sync="expireAt"
                        >
                            <v-text-field
                                slot="activator"
                                label="Expire At"
                                v-model="expireAt"
                                readonly
                                clearable
                                prepend-icon="event"
                                :error-messages="errors.expire_at"
                            ></v-text-field>
                            <v-date-picker v-model="expireAt"
                                           :allowed-dates="allowedDates"
                                           @input="$refs.datePicker.save(expireAt)">
                            </v-date-picker>
                        </v-menu>
                    </v-flex>

                    <v-flex xs12 v-if="!public">
                        <v-autocomplete
                            :items="sites"
                            v-model="site"
                            label="Site"
                            item-text="text"
                            item-value="id"
                            prepend-icon="store"
                            required
                            return-object
                            :rules="[v => !!v || 'Item is required']"
                            :error-messages="errors.site_id"
                            :readonly="siteReadOnly"
                            :clearable="!siteReadOnly"
                        ></v-autocomplete>
                    </v-flex>

                    <v-flex xs12>
                        <v-autocomplete
                            :items="languages"
                            v-model="lang"
                            label="Language"
                            prepend-icon="language"
                            item-text="name"
                            item-value="code"
                        ></v-autocomplete>
                    </v-flex>

                    <v-flex xs12>
                        <v-autocomplete
                            :items="templates"
                            browser-autocomplete="chrome-off"
                            v-model="template"
                            label="Email Template"
                            prepend-icon="email"
                            item-text="name"
                            item-value="id"
                            :error-messages="errors.template"
                        ></v-autocomplete>
                    </v-flex>

                    <v-flex xs12 v-if="site">
                        <v-card v-if="site.user">
                            <v-card-text>
                                <div><strong>{{ site.user.name }}</strong></div>
                                <div>{{ site.user.phone }}</div>
                                <div>{{ site.user.email }}</div>
                            </v-card-text>
                        </v-card>
                    </v-flex>

                </v-card-text>

                <v-divider></v-divider>

                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" flat @click="closeDialog">Cancel</v-btn>
                    <v-btn
                        color="primary"
                        :loading="loading"
                        :disabled="loading"
                        flat
                        @click.native="save"
                    >Save
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-form>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import moment from 'moment'

export default {
    name: "offer-create",
    props: ['items'],
    components: {},
    data() {
        return {
            datePicker: false,
            siteReadOnly: false,
            valid: true,
            loading: false,
            dialog: false,
            public: false,
            template: null,
            title: 'New Offer',
            lang: 'en',
            site: {},
            sites: [],
            errors: [],
            languages: [],
            templates: [],
            expireAt: moment().add(20, 'day').format('YYYY-MM-DD'),
        }
    },
    mounted() {
        // this.getSites()
    },
    methods: {
        allowedDates(date) {
            // let seconds = 1000000;
            // let duration = moment.duration(seconds, 'seconds');
            // // let formatted = duration.format("hh:mm:ss", { trim: false });
            // let formatted = duration.format("hh:mm:ss", { trim: false });
            // console.log(formatted);
            return moment(date).isBetween(moment().subtract(1, 'day'), moment().add(20, 'day'))
        },
        getLocales() {
            axios.get('/merchants/locales')
                .then(response => {
                    this.languages = response.data.data.lang
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getTemplates() {
            axios.get('/offers/email-templates')
                .then(response => {
                    this.templates = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getSites() {
            axios.get('/sites/autocomplete')
                .then(response => {
                    this.loading = false
                    this.sites = response.data.data.map(item => {
                        if (!item.invoicing) {
                            item.invoicing = {
                                user_id: item.user_id,
                                site_id: item.id,
                                country: item.user.country,
                                lang: item.user.lang,
                                name: item.user.name,
                            }
                        }
                        return {...item, text: '#' + item.id + ' ' + item.primary_host}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        new(site) {
//                this.$refs.form.reset()
            this.valid = true
            this.template = null
            this.lang = site.user.lang
            this.errors = []
            this.expireAt = moment().add(20, 'day').format('YYYY-MM-DD')
            if (site) {
                this.siteReadOnly = true
                this.site = {...site, text: '#' + site.id + ' ' + site.primary_host}
                this.sites.push(this.site)
            } else {
                this.site = {}
            }
            if (!this.sites.length) {
                this.getSites()
            }
            if (!this.languages.length) {
                this.getLocales()
            }
            if (!this.templates.length) {
                this.getTemplates()
            }
            this.showDialog()
        },
        newPublic() {
//                this.$refs.form.reset()
            this.valid = true
            this.public = true
            this.template = null
            this.lang = 'en'
            this.title = 'New Public Offer'
            this.errors = []
            this.expireAt = moment().add(1, 'month').format('YYYY-MM-DD')
            this.site = {}
            if (!this.languages.length) {
                this.getLocales()
            }
            if (!this.templates.length) {
                this.getTemplates()
            }
            this.showDialog()
        },
        createRequest() {
            return {
                expire_at: this.expireAt,
                site_id: this.site.id,
                lang: this.lang,
                template: this.template,
            }
        },
        save() {
            if (this.$refs.form.validate()) {
                this.loading = true
                const request = this.createRequest()
                axios.post('/offers', request)
                    .then((response) => {
                        this.handleSuccess(response)
                        let item = response.data.data
                        if (this.items) {
                            let editedIndex = this.items.indexOf(item)
                            if (editedIndex >= 0) {
                                Object.assign(this.items[editedIndex], item)
                            } else {
                                this.items.unshift(item)
                            }
                            // setTimeout(() => this.$router.push({
                            //     name: 'offers.show',
                            //     params: {uid: item.unique_id}
                            // }), 500)
                        }
                        this.$emit('offer:create', item)
                        this.closeDialog()
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        },
    }
}
</script>