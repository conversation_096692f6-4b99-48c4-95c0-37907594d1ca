<template>
    <v-card>
        <v-card>
            <v-card-title>
                <h1>Offers</h1>
                <v-btn
                    v-if="site && (isCloudCartEmployee || isResellerLevel(2))"
                    small
                    dark
                    fab
                    color="pink"
                    @click="$refs.createDialog.new(site)"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <!--                Disable public offers:-->
                <v-btn
                    v-else-if="isCloudCartEmployee"
                    small
                    dark
                    fab
                    color="pink"
                    @click="$refs.createDialog.newPublic()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-spacer></v-spacer>
                <v-text-field
                    append-icon="search"
                    label="Search"
                    single-line
                    v-model="search"
                    clearable
                ></v-text-field>
            </v-card-title>

            <filters
                v-if="isCloudCartEmployee"
                :ss-url="ssUrl"
                ref="filters"
                v-on:filters:change="onFilterChange"
            ></filters>

        </v-card>

        <v-data-table
            :headers="headers"
            :items="filteredItems"
            :search="ssUrl ? null : search"
            :pagination.sync="pagination"
            :total-items="totalItems"
            :loading="progress"
            :custom-filter="customFilter"
            must-sort
            class="elevation-1"
            ref="dataTable"
        >
            <template slot="items" slot-scope="props">
                <tr>
                    <td>
                        <a @click="$refs.details.show(props.item)">
                            <strong>{{ props.item.unique_id }}</strong>
                        </a>
                        <br>
                        {{ props.item.created_at | formatDateTime }}
                    </td>
                    <td nowrap v-if="!site">
                        <div v-if="props.item.site">
                            <a @click="$refs.siteDetails.show(props.item.site)">
                                {{ props.item.site.primary_host }}
                            </a>
                            <span class="pull-right mt-2">
                                <a :href="props.item.site.url" target="_blank"
                                   style="text-decoration: none;">
                                    <v-icon small>fa-external-link</v-icon>
                                </a>
                            </span>
                        </div>
                        <div v-if="props.item.user">
                            <a @click="$refs.merchantDetails.show(props.item.user)">
                                <strong>{{ props.item.user.name }}</strong>
                            </a>
                            <br>
                            {{ props.item.user.phone }}
                            <br>
                            {{ props.item.user.email }}
                        </div>
                    </td>
                    <td>{{ props.item.items_count }}</td>
                    <td class="text-xs-right">
                            {{ props.item.amount | toCurrency('EUR') }}
                    </td>
                    <td class="text-xs-right">
                            {{ props.item.amount_paid | toCurrency('EUR') }}
                    </td>
                    <td>
                        {{ props.item.status_name }}
                    </td>
                    <td v-if="!owner && props.item.owner">
                        <cc-user
                            :item="props.item.owner"
                            :user-details="$refs.userDetails"
                        ></cc-user>
                    </td>
                    <td nowrap>
                        {{ props.item.expire_at | formatDate }}
                        <v-tooltip bottom>
                            <v-btn slot="activator" icon class="mx-0" @click="$refs.extendDialog.show(props.item)">
                                <v-icon color="yellow">more_time</v-icon>
                            </v-btn>
                            <span>Extend</span>
                        </v-tooltip>
                    </td>
                    <td class="text-xs-center px-0" nowrap>
                        <v-btn icon class="mx-0" @click="$refs.deleteDialog.deleteItem(props.item)">
                            <v-icon color="pink">delete</v-icon>
                        </v-btn>
                    </td>
                </tr>
            </template>
            <template v-slot:no-results>
                <v-alert :value="true" color="error" icon="warning">
                    Your search for "{{ search }}" found no results.
                </v-alert>
            </template>
            <template v-slot:no-data v-if="ssUrl && search && search.length > 2">
                <v-alert :value="true" color="error" icon="warning">
                    Your search for "{{ search }}" found no results.
                </v-alert>
            </template>
            <template v-slot:footer>
                <td colspan="100%">
                    Total Amount: <strong>{{ totalAmount | toCurrency('EUR') }}</strong>
                    | Paid Amount: <strong>{{ paidAmount | toCurrency('EUR') }}</strong>
                </td>
            </template>

        </v-data-table>

        <offer-create
            :items="filteredItems"
            ref="createDialog"
            v-on:offer:create="(item) => this.onOfferCreate(item)"
        />

        <delete :items="filteredItems" url="/offers/" ref="deleteDialog"/>

        <offer-show-dialog
            ref="details"
            v-on:offer:refresh-item="(item) => this.refreshItem(item)"
        ></offer-show-dialog>
        <offer-extend ref="extendDialog" :items="filteredItems"></offer-extend>
        <site-show-dialog ref="siteDetails"></site-show-dialog>
        <merchant-show-dialog ref="merchantDetails"></merchant-show-dialog>
        <user-show-dialog ref="userDetails"></user-show-dialog>
    </v-card>
</template>

<script type="text/ecmascript-6">
import Delete from '@components/Delete'
import OfferCreate from "./create";
import Filters from './filters'
import OfferShowDialog from "./show-dialog"
import CcUser from "../../../../components/CcUser"
import OfferExtend from "./extend"
import axios from "axios"
import _ from "lodash"

const CancelToken = axios.CancelToken;
let cancel;

export default {
    name: "offers-list",
    props: ['loading', 'items', 'site', 'owner', 'ssUrl', 'rowsPerPage'],
    components: {
        OfferExtend,
        CcUser,
        MerchantShowDialog: () => import("../../merchants/show-dialog"),
        SiteShowDialog: () => import("../../sites/show-dialog"),
        UserShowDialog: () => import("../../access/users/show-dialog"),
        OfferShowDialog,
        OfferCreate,
        Delete,
        Filters,
    },
    data() {
        return {
            loaded: null,
            search: '',
            totalItems: 0,
            totalAmount: 0,
            paidAmount: 0,
            filteredItems: [],
            pagination: {
                page: 1,
                sortBy: "id",
                totalItems: 0,
                rowsPerPage: 25,
                descending: true,
            },
        }
    },
    mounted() {
        this.filteredItems = this.items
        this.pagination.rowsPerPage = this.rowsPerPage ? this.rowsPerPage : 25
    },
    computed: {
        progress() {
            if (this.loaded === null) {
                return this.loading
            }

            return !this.loaded
        },
        headers() {
            let headers = [
                {text: 'ID / Created At', value: 'created_at'},
                {text: 'Site / Merchant', value: 'site_id'},
                {text: 'Items', value: 'items_count'},
                {text: 'Total Amount', align: 'right', value: 'amount'},
                {text: 'Paid Amount', align: 'right', value: 'amount_paid'},
                {text: 'Status', value: 'status'},
                {text: 'Owner', value: 'owner_id'},
                {text: 'Expire At', value: 'expire_at'},
                {text: 'Actions', value: 'id', sortable: false, align: 'left'},
            ]

            if (this.site) {
                delete headers[1]
            }

            if (this.owner) {
                delete headers[6]
            }

            return headers
        },
    },
    watch: {
        items(val) {
            if (!this.ssUrl) {
                this.filteredItems = val
            }
        },
        filteredItems(val) {
            if (!this.ssUrl) {
                this.calculateTotal(val)
            }
        },
        search(val) {
            if (!this.ssUrl) {
                let items = this.customFilter(this.filteredItems, val, this.$refs.dataTable.filter)
                this.calculateTotal(items)
            } else if (!val || val.length >= 3) {
                this.getItems()
            }
        },
        pagination: {
            handler(newVal, oldVal) {
                if (!this.ssUrl) {
                    return
                }
                delete newVal.totalItems;
                delete oldVal.totalItems
                if (!_.isEqual(newVal, oldVal)) {
                    this.getItems()
                }
            },
            deep: false
        },
    },
    methods: {
        onFilterChange() {
            if (!this.ssUrl) {
                this.filteredItems = this.$refs.filters.filterItems(this.items)
            } else {
                this.getItems()
            }
        },
        getItems() {
            if (cancel !== undefined) {
                cancel();
            }
            this.loaded = false
            const params = {
                cancelToken: new CancelToken(function executor(c) {
                    cancel = c;
                }),
                params: {
                    search: this.search,
                    filters: this.$refs.filters ? this.$refs.filters.filters : {},
                    ...this.pagination,
                }
            }

            axios.get(this.ssUrl, params)
                .then(response => {
                    this.loaded = true
                    this.filteredItems = response.data.data
                    this.totalItems = response.data.meta.total
                    this.totalAmount = response.data.meta.total_amount
                    this.paidAmount = response.data.meta.total_paid
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        onOfferCreate(item) {
            this.$refs.details.show(item)
        },
        refreshItem(item) {
            let editedIndex = this.filteredItems.indexOf(this.filteredItems.find(el => el.id === item.id))
            if (editedIndex >= 0) {
                Object.assign(this.filteredItems[editedIndex], item)
            }
        },
        calculateTotal(items) {
            this.totalAmount = 0
            this.paidAmount = 0
            items.forEach(item => {
                this.totalAmount = this.totalAmount + item.amount
                this.paidAmount = this.paidAmount + item.amount_paid
            })
        },
        customFilter(items, search, filter) {
            if (!search || search.trim() === '') return items
            search = search.toString().toLowerCase().trim()

            return items.filter(item => item.flatten.some(val => filter(val, search)))
        },
    },
}
</script>

<style scoped>
.v-list__tile__avatar {
    min-width: 48px;
}
</style>
