<template>
    <v-dialog
        v-model="dialog"
        max-width="500px"
        scrollable
    >
        <v-form ref="form" v-model="valid" lazy-validation style="width: 100%">
            <v-card>
                <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                    <h3 class="headline white--text mb-0">Select Discount</h3>
                </v-card-title>

                <v-card-text>
                    <v-flex xs12>
                        <v-autocomplete
                            :items="discounts"
                            v-model="discount"
                            label="Discount Bundle"
                            item-text="name"
                            item-value="id"
                            prepend-icon="money"
                            required
                            return-object
                            :rules="[v => !!v || 'Item is required']"
                            :error-messages="errors.discount_id"
                            clearable
                        ></v-autocomplete>
                    </v-flex>

                </v-card-text>

                <v-divider></v-divider>

                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                    <v-btn
                        color="primary"
                        :loading="loading"
                        :disabled="loading"
                        flat
                        @click.native="save"
                    >Import
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-form>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import moment from 'moment'

export default {
    name: "offer-items-from-discount",
    components: {},
    data() {
        return {
            datePicker: false,
            valid: true,
            loading: false,
            dialog: false,
            offer: {},
            discount: {},
            discounts: [],
            errors: [],
        }
    },
    mounted() {
        // this.getDiscounts()
    },
    methods: {
        getDiscounts() {
            axios.get('/discounts/bundles')
                .then(response => {
                    this.discounts = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        show(offer) {
            this.dialog = true
            this.valid = true
            this.offer = offer
            this.errors = []
            if (!this.discounts.length) {
                this.getDiscounts()
            }
        },
        close() {
            this.dialog = false
        },
        createRequest() {
            return {
                offer_id: this.offer.id,
                discount_id: this.discount.id,
            }
        },
        save() {
            if (this.$refs.form.validate()) {
                this.loading = true
                const request = this.createRequest()
                axios.post('/offer-items/from-discount', request)
                    .then((response) => {
                        this.handleSuccess(response)
                        this.close()
                        this.$emit('offer-item:add')
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        },
    }
}
</script>