<template>
    <v-dialog
        v-model="dialog"
        max-width="700px"
        scrollable
        persistent
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h3 class="headline white--text mb-0">Change Offer Status</h3>
            </v-card-title>

            <v-card-text>
                <v-container grid-list-md pa-0>
                    <v-layout wrap>
                        <v-flex xs12>
                            <v-autocomplete
                                :items="statuses"
                                v-model="offerStatus"
                                label="Status"
                                item-text="name"
                                item-value="id"
                                required
                                readonly
                                :error-messages="errors.status"
                            ></v-autocomplete>
                        </v-flex>
                        <v-flex xs12>
                            <v-textarea
                                outline
                                v-model="note"
                                label="Note"
                                :error-messages="errors.note"
                                ref="noteTextArea"
                                height="400"
                            ></v-textarea>
                        </v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click="closeDialog">Cancel</v-btn>
                <v-btn
                    color="primary"
                    :loading="loading"
                    :disabled="loading"
                    flat
                    @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import {
    TiptapVuetify, Heading, Bold, Italic, Strike, Underline, Code, CodeBlock, Paragraph, BulletList, OrderedList,
    ListItem, Link, Blockquote, HardBreak, HorizontalRule, History
} from 'tiptap-vuetify'
import {Image} from 'tiptap-extensions'

export default {
    name: "offer-change-status",
    components: {
        TiptapVuetify,
    },
    data() {
        return {
            note: '',
            dialog: false,
            loading: false,
            offerStatus: null,
            statuses: [],
            errors: [],
            item: {},
            // declare tip-tap extensions
            extensions: [
                new Heading({
                    levels: [1, 2, 3]
                }),
                new Bold(),
                new Italic(),
                new Strike(),
                new Underline(),
                // new Code(),
                // new CodeBlock(),
                new Paragraph(),
                new BulletList(),
                new OrderedList(),
                new ListItem(),
                // new Link(),
                new Blockquote(),
                new HardBreak(),
                new HorizontalRule(),
                new History(),
            ],
            nativeExtensions: [
                new Image(),
            ],
        }
    },
    mounted() {
        this.getStatuses()
    },
    methods: {
        show(item, status) {
            this.item = item
            this.offerStatus = status
            this.note = ''
            this.errors = [];
            this.showDialog()
            setTimeout(() => {
                this.$refs.noteTextArea.focus()
            }, 100);
        },
        getStatuses() {
            axios.get('/offers/statuses')
                .then(response => {
                    this.statuses = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        save() {
            this.loading = true
            axios.post(`/offers/${this.item.id}/change-status`, {
                note: this.note,
                status: this.offerStatus,
            })
                .then((response) => {
                    this.loading = false
                    this.$emit('offer-status:changed')
                    this.closeDialog()
                })
                .catch(error => {
                    this.handleError(error)
                    setTimeout(() => {
                        this.$refs.noteTextArea.focus()
                    }, 100);
                })
        },
    }
}
</script>