<template>
    <v-dialog
        v-model="dialog"
        fullscreen
        hide-overlay
        transition="dialog-bottom-transition"
        scrollable
    >
        <v-card tile>
            <v-toolbar card dark :color="$root.dark ? 'grey darken-4' : 'primary'">
                <v-toolbar-title>Offer {{ item.unique_id }} Email Preview</v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon dark @click.native="dialog = false">
                    <v-icon>close</v-icon>
                </v-btn>
            </v-toolbar>

            <v-card-text class="transparent">
                <v-flex xs12 sm6 offset-sm3>
                    <v-card>
                        <div style="padding: 15px" class="white elevation-8">
                            <iframe @load="resizeIFrameToFitContent"
                                    id="mail-body"
                                    :srcdoc="item.body"
                                    style="width: 100%; border: none"
                                    v-show="item.body"
                            ></iframe>
                        </div>
                    </v-card>
                </v-flex>
            </v-card-text>

            <div style="flex: 1 1 auto;" class="transparent"></div>
        </v-card>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import NProgress from 'nprogress/nprogress'

export default {
    name: "offer-preview-email",
    props: ['content'],
    data() {
        return {
            dialog: false,
            loading: false,
            item: {},
        }
    },
    methods: {
        show(item) {
            this.item = {}
            this.dialog = true
            NProgress.start()
            axios.post(`/offers/${item.id}/preview-mail`, {
                content: this.content,
            })
                .then((response) => {
                    NProgress.done()
                    this.item = {
                        ...item,
                        ...response.data
                    }
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        resizeIFrameToFitContent() {
            let iFrame = document.getElementById('mail-body');
            iFrame.width = iFrame.contentWindow.document.body.scrollWidth;
            iFrame.height = iFrame.contentWindow.document.body.scrollHeight;
        },
    }
}
</script>