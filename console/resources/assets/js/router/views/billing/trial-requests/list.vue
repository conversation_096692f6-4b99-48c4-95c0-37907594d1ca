<template>
    <v-card>
        <v-card>
            <v-card-title>
                <h1>{{ title ? title : 'Subscription Trial Requests' }}</h1>
                <v-spacer></v-spacer>
                <v-text-field v-if="!hideFilters"
                              append-icon="search"
                              label="Search"
                              single-line
                              v-model="search"
                              clearable
                ></v-text-field>
            </v-card-title>

            <filters
                v-if="!hideFilters"
                :filters="filters"
                v-on:filters:change="onFilterChange"
            ></filters>

        </v-card>

        <v-data-table
            :headers="headers"
            :items="filteredItems"
            :search="ssUrl ? null : search"
            :pagination.sync="pagination"
            :total-items="totalItems"
            :loading="loading"
            :custom-filter="customFilter"
            must-sort
            class="elevation-1"
            ref="dataTable"
        >
            <template slot="items" slot-scope="props">
                <tr>
                    <td style="width: 50px">
                        {{ props.item.created_at | formatDateTime }} / {{ props.item.id }}
                    </td>
                    <td v-if="!site">
                        <div>
                            <a @click="$refs.siteDetails.show(props.item.site)">{{ props.item.site.primary_host }}</a>
                            <br>
                            Plan: <b>{{ props.item.site.plan }}</b>
                        </div>
                    </td>
                    <td v-if="props.item.model_type === 'cloudcart_app'">
                        <v-list two-line dense class="transparent list-p0">
                            <v-list-tile>
                                <v-list-tile-avatar>
                                    <v-avatar tile>
                                        <img :src="props.item.model.icon" alt="Avatar">
                                    </v-avatar>
                                </v-list-tile-avatar>
                                <v-list-tile-content>
                                    <v-list-tile-title>{{ props.item.name }}</v-list-tile-title>
                                    <v-list-tile-sub-title>{{ props.item.model.mapping }}</v-list-tile-sub-title>
                                </v-list-tile-content>
                            </v-list-tile>
                        </v-list>
                    </td>
                    <td v-else-if="props.item.model_type === 'cloudcart_service'">
                        <div class="d-inline-flex">
                            <v-icon v-if="props.item.model && props.item.model.ecosystem">hub</v-icon>
                            <v-icon v-else>closed_caption</v-icon>
                            <span class="pa-1">{{ props.item.name }}</span>
                        </div>
                    </td>
                    <td v-else-if="props.item.model">
                        {{ props.item.name }}
                    </td>
                    <td v-else></td>
                    <td class="text-xs-right">
                        <div>
                            {{ props.item.next_billing_amount | toCurrency(props.item.currency) }}
                        </div>
                    </td>
                    <td nowrap>
                        {{ props.item.next_billing_date | formatDate }}
                        <br>
                        Trial days: {{ props.item.trial_days }}
                    </td>
                    <td>
                        <div class="pa-1">
                            <cc-user
                                :item="props.item.requested_by"
                                :small="true"
                            ></cc-user>
                            <div class="mt-1">
                                <cc-user
                                    :item="props.item.manager"
                                    :small="true"
                                ></cc-user>
                            </div>
                        </div>
                    </td>
                    <td>
                        <v-tooltip v-if="props.item.status == 0" bottom>
                            <v-icon slot="activator" color="error">cancel</v-icon>
                            <span>Canceled</span>
                        </v-tooltip>
                        <v-tooltip v-if="props.item.status == 1" bottom>
                            <v-icon slot="activator" color="success">check_circle</v-icon>
                            <span>Approved</span>
                        </v-tooltip>
                        <v-tooltip v-if="props.item.status == 2" bottom>
                            <v-icon slot="activator" color="orange">pending</v-icon>
                            <span>Pending</span>
                        </v-tooltip>
                    </td>
                    <td class="text-xs-left px-0"
                        nowrap
                        v-if="!hideControls"
                    >
                        <v-tooltip
                            v-if="props.item.subscription_id"
                            bottom
                        >
                            <v-btn
                                slot="activator"
                                class="mx-0"
                                icon
                                @click="$refs.details.show(props.item.subscription_id, true)"
                            >
                                <v-icon color="teal">preview</v-icon>
                            </v-btn>
                            <span>View Subscription</span>
                        </v-tooltip>
                        <v-tooltip bottom v-if="props.item.status == 2">
                            <v-btn
                                slot="activator"
                                class="mx-0"
                                icon
                                :loading="approveLoader[props.item.id]"
                                @click="approve(props.item)"
                            >
                                <v-icon color="success">check_circle</v-icon>
                            </v-btn>
                            <span>Approve</span>
                        </v-tooltip>
                        <v-tooltip bottom v-if="props.item.status == 2">
                            <v-btn
                                slot="activator"
                                class="mx-0"
                                icon
                                :loading="cancelLoader[props.item.id]"
                                @click="cancel(props.item)"
                            >
                                <v-icon color="error">cancel</v-icon>
                            </v-btn>
                            <span>Cancel</span>
                        </v-tooltip>

                    </td>
                </tr>
            </template>

        </v-data-table>

        <subscription-show-dialog ref="details"></subscription-show-dialog>

        <site-show-dialog ref="siteDetails"></site-show-dialog>

    </v-card>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import Filters from '@views/billing/trial-requests/filters'
import SubscriptionShowDialog from "@views/billing/subscriptions/show-dialog";
import CcUser from "../../../../components/CcUser.vue";
import _ from 'lodash';

const CancelToken = axios.CancelToken;
let cancel;

export default {
    name: "trial-requests-list",
    props: [
        'site',
        'hideControls',
        'hideFilters',
    ],
    components: {
        CcUser,
        SiteShowDialog: () => import("../../sites/show-dialog"),
        SubscriptionShowDialog,
        Filters,
    },
    data() {
        return {
            ssUrl: '/trial-requests',
            loading: false,
            approveLoader: {},
            cancelLoader: {},
            search: '',
            totalItems: 0,
            filteredItems: [],
            filters: {
                date_field: 'created_at',
            },
            pagination: {
                page: 1,
                sortBy: 'id',
                totalItems: 0,
                rowsPerPage: 25,
                descending: true,
            },
        }
    },
    mounted() {
        if (this.$route.query.filters) {
            this.filters = JSON.parse(this.$route.query.filters)
        }

        if (this.site) {
            this.filters = {
                site_id: this.site.id
            }
        }
    },
    computed: {
        headers() {
            let headers = [
                {text: 'Created At / ID', value: 'id'},
                {text: 'Site', value: 'site_id'},
                {text: 'Item', value: 'model_id'},
                {text: 'Next Billing Amount', align: 'right', value: 'next_billing_amount'},
                {text: 'Next Billing', value: 'next_billing_date'},
                {text: 'Requested By / Manager', align: 'left', sortable: false},
                {text: 'Status', value: 'status'},
                {text: 'Actions', value: 'id', sortable: false, align: 'left'},
            ]

            if (this.site) {
                delete headers[1]
            }

            return headers
        },
    },
    watch: {
        search(val) {
            if (!val || val.length >= 3) {
                this.getItems()
            }
        },
        pagination: {
            handler(newVal, oldVal) {
                delete newVal.totalItems;
                delete oldVal.totalItems
                if (!_.isEqual(newVal, oldVal)) {
                    this.getItems()
                }
            },
            deep: false
        },
    },
    methods: {
        onFilterChange() {
            this.getItems()
        },
        getItems() {
            if (cancel !== undefined) {
                cancel();
            }
            this.loading = true
            const params = {
                cancelToken: new CancelToken(function executor(c) {
                    cancel = c;
                }),
                params: {
                    search: this.search,
                    filters: this.filters,
                    ...this.pagination,
                }
            }

            axios.get(this.ssUrl, params)
                .then(response => {
                    this.loading = false
                    this.filteredItems = response.data.data
                    this.totalItems = response.data.meta.total
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        approve(item) {
            this.$set(this.approveLoader, item.id, true)
            let editedIndex = this.filteredItems.indexOf(item)
            axios.post(`${this.ssUrl}/${item.id}/approve`)
                .then(response => {
                    Object.assign(this.filteredItems[editedIndex], response.data.data)
                    this.$root.success = {
                        snackbar: true,
                        msg: response.data.message,
                    }
                    this.$emit('request:approved')
                })
                .catch(error => {
                    this.handleError(error)
                })
                .finally(() => {
                    this.$set(this.approveLoader, item.id, false)
                })
        },
        cancel(item) {
            this.$set(this.cancelLoader, item.id, true)
            this.editedIndex = this.filteredItems.indexOf(item)
            axios.post(`${this.ssUrl}/${item.id}/cancel`)
                .then((response) => {
                    Object.assign(this.filteredItems[this.editedIndex], response.data.data)
                    this.$emit('request:canceled')
                })
                .catch(error => {
                    this.handleError(error)
                })
                .finally(() => {
                    this.$set(this.cancelLoader, item.id, false)
                })
        },
    },
}
</script>
