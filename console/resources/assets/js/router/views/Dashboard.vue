<template>
    <layout-default>
        <v-container grid-list-md fluid>
            <v-layout wrap>
                <v-flex xs12 sm6>
                    <v-form ref="siteForm" v-model="valid" lazy-validation v-on:submit.prevent="() => false">
                        <v-card>
                            <v-card-text>
                                <v-autocomplete
                                    :items="sites"
                                    v-model="site"
                                    label="Open Site Details"
                                    item-text="text"
                                    item-value="site_id"
                                    prepend-icon="store"
                                    autocomplete="off"
                                    required
                                    clearable
                                    :rules="[v => !!v || 'Item is required']"
                                    v-on:keyup.13="goToSite"
                                >
                                    <template v-if="false" v-slot:append-item>
                                        <div v-intersect.quiet="onIntersect" class="pa-4 teal--text">
                                            Loading more items ...
                                        </div>
                                    </template>
                                    <template v-slot:append-outer>
                                        <v-tooltip bottom>
                                            <v-btn
                                                slot="activator"
                                                icon
                                                color="primary"
                                                bottom
                                                @click.native="goToSite"
                                            >
                                                <v-icon>open_in_browser</v-icon>
                                            </v-btn>
                                            <span>Open Site Details</span>
                                        </v-tooltip>
                                    </template>
                                </v-autocomplete>
                            </v-card-text>
                        </v-card>
                    </v-form>
                </v-flex>
                <v-flex xs12 sm6>
                    <v-form ref="merchantForm" v-model="valid" lazy-validation v-on:submit.prevent="() => false">
                        <v-card>
                            <v-card-text>
                                <v-autocomplete
                                    :items="merchants"
                                    v-model="merchant"
                                    label="Open Merchant Details"
                                    item-text="text"
                                    item-value="id"
                                    prepend-icon="supervisor_account"
                                    required
                                    clearable
                                    :rules="[v => !!v || 'Item is required']"
                                    v-on:keyup.13="goToMerchant"
                                >
                                    <template v-slot:append-outer>
                                        <v-tooltip bottom>
                                            <v-btn
                                                slot="activator"
                                                icon
                                                color="primary"
                                                bottom
                                                @click.native="goToMerchant"
                                            >
                                                <v-icon>open_in_browser</v-icon>
                                            </v-btn>
                                            <span>Open Merchant Details</span>
                                        </v-tooltip>
                                    </template>
                                </v-autocomplete>
                            </v-card-text>
                        </v-card>
                    </v-form>
                </v-flex>
            </v-layout>

            <user-details ref="userDetails"></user-details>

        </v-container>

        <site-show-dialog ref="siteDetails"></site-show-dialog>

        <merchant-show-dialog ref="merchantDetails"></merchant-show-dialog>
    </layout-default>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import store from '@state/store'
import LayoutDefault from '@layouts/Default'
import MerchantsList from '@views/merchants/list'
import SitesList from '@views/sites/list'
import UserDetails from "./access/users/details";
import NProgress from "nprogress";
import SiteShowDialog from "./sites/show-dialog";
import MerchantShowDialog from "./merchants/show-dialog";

const CancelToken = axios.CancelToken;
let cancel;

export default {
    name: 'dashboard',
    components: {
        MerchantShowDialog,
        SiteShowDialog,
        UserDetails,
        LayoutDefault,
        MerchantsList,
        SitesList,
    },
    data() {
        return {
            valid: false,
            site: null,
            merchant: null,
            sites: [],
            merchants: [],
            sitesPage: 1,
        }
    },
    mounted() {
        this.getUser()
        this.getMerchants()
        this.getSites()
    },
    computed: {},
    watch: {},
    methods: {
        getUser() {
            NProgress.start()
            axios.get(`/auth/me`)
                .then(response => {
                    this.$refs.userDetails.item = response.data
                    this.$refs.userDetails.init()
                    NProgress.done()
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        onIntersect () { // v-intersect.quiet directive not found
            console.log('load more...')
            this.sitesPage += 1
            this.getSites()
        },
        getSitesNew() { // not working
            if (cancel !== undefined) {
                cancel();
            }
            this.loading = true
            const params = {
                cancelToken: new CancelToken(function executor(c) {
                    cancel = c;
                }),
                params: {
                    search: this.search,
                    page: this.sitesPage,
                    sortBy: "url",
                    rowsPerPage: 5,
                    descending: false,
                }
            }

            axios.get('/sites/autocomplete', params)
                .then(response => {
                    this.loading = false
                    let items = response.data.data.map(item => {
                        return {...item, text: '#' + item.site_id + ' ' + item.primary_host}
                    })
                    this.sites = [
                        ...this.sites,
                        ...items,
                    ]
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getSites() {
            this.sites = store.getters.sites
            axios.get('/sites/cached')
                .then(response => {
                    this.loading = false
                    this.sites = response.data.data.map(item => {
                        return {...item, text: '#' + item.site_id + ' ' + item.primary_host}
                    })
                    store.commit('sites', this.sites)
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getMerchants() {
            this.merchants = store.getters.merchants
            axios.get('/merchants/autocomplete')
                .then(response => {
                    this.merchants = response.data.data.map(item => {
                        return {...item, text: '#' + item.id + ' ' + item.name + ' ' + item.phone + ' ' + item.email}
                    })
                    store.commit('merchants', this.merchants)
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        goToSite() {
            if (this.$refs.siteForm.validate()) {
                // this.$router.push({name: 'sites.show', params: {id: this.site}})
                this.$refs.siteDetails.show({id: this.site})
            }
        },
        goToMerchant() {
            if (this.$refs.merchantForm.validate()) {
                // this.$router.push({name: 'merchants.show', params: {uid: this.merchant}})
                this.$refs.merchantDetails.show({id: this.merchant})
            }
        },
    }
}
</script>
