<template>
    <v-layout row justify-center>
        <v-dialog v-model="dialog" persistent max-width="500">
            <v-card>
                <v-card-title class="headline">Please, confirm removal of record # {{ editedItem.id }}</v-card-title>
                <v-card-text>
                    <v-alert :value="true" outline color="error" icon="warning">
                        <strong>WARNING!</strong> This action is irreversible!
                    </v-alert>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="green darken-1" flat @click.native="close">Cancel</v-btn>
                    <v-btn
                            color="red darken-1"
                            flat
                            :loading="loading"
                            :disabled="loading"
                            @click.native="destroy">Delete
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </v-layout>
</template>

<script type="text/ecmascript-6">
    import axios from 'axios'

    export default {
        name: "delete",
        props: ['items'],
        data() {
            return {
                editedItem: {},
                dialog: false,
                loading: false,
                editedIndex: -1,
            }
        },
        mounted() {
        },
        methods: {
            deleteItem(item) {
                this.dialog = true
                this.editedIndex = this.items.indexOf(item)
                this.editedItem = Object.assign({}, item)
            },
            close() {
                this.dialog = false
            },
            destroy() {
                this.loading = true
                axios.delete('/merchants/' + this.editedItem.id)
                        .then(() => {
                            this.loading = false
                            this.items.splice(this.editedIndex, 1)
                            let success = {
                                data: {
                                    message: 'Delete command started successfully'
                                }
                            }
                            this.handleSuccess(success)
                            this.close()
                        })
                        .catch(error => {
                            this.handleError(error)
                        })
            }
        }
    }
</script>