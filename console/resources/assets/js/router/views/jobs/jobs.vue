<template>
    <v-card>
        <v-card>
            <v-card-title>
                <h1>{{ platform }}</h1>
                <v-spacer></v-spacer>
                <v-text-field
                        append-icon="search"
                        label="Search"
                        single-line
                        v-model="search"
                        clearable
                ></v-text-field>
            </v-card-title>
        </v-card>

        <v-data-table
                :headers="headers"
                :items="items"
                :pagination.sync="pagination"
                :rows-per-page-items="[5,10,25,50,100,200]"
                :total-items="totalItems"
                :loading="loading"
                class="elevation-1"
                must-sort
        >
            <template slot="items" slot-scope="props">
                <tr>
                    <td>{{ props.item.completed_at | formatDateTime }}</td>
                    <td>{{ props.item.next_execution_at | formatDateTime }}</td>
                    <td>{{ props.item.site_id }}</td>
                    <td>{{ props.item.mapping }}<br>{{ props.item.class }}</td>
                    <td>{{ props.item.executions }}</td>
                    <td>{{ props.item.queue }}</td>
                    <td>{{ props.item.max_memory_formatted }}</td>
                </tr>
            </template>
            <template v-slot:no-data v-if="search && search.length > 2">
                <v-alert :value="true" color="error" icon="warning">
                    Your search for "{{ search }}" found no results.
                </v-alert>
            </template>
        </v-data-table>
    </v-card>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import {flatten} from '@helpers'
import _ from "lodash"

const CancelToken = axios.CancelToken;
let cancel;

export default {
    name: 'jobs',
    props: ['platform'],
    data() {
        return {
            search: '',
            totalItems: 0,
            items: [],
            loading: false,
            filters: {},
            pagination: {
                page: 1,
                sortBy: "completed_at",
                totalItems: 0,
                rowsPerPage: 25,
                descending: true,
            },
            headers: [
                {text: 'Last Run', value: 'completed_at'},
                {text: 'Next Run', value: 'next_execution_at'},
                {text: 'Site ID', value: 'site_id'},
                {text: 'Mapping / Class', value: 'mapping'},
                {text: 'Executions', value: 'executions'},
                {text: 'Queue', value: 'queue'},
                {text: 'Max Memory', value: 'max_memory'},
            ],
        }
    },
    mounted() {
        this.getItems()
    },
    watch: {
        search(val) {
            if (!val || val.length >= 3) {
                this.getItems()
            }
        },
        pagination: {
            handler(newVal, oldVal) {
                delete newVal.totalItems;
                delete oldVal.totalItems
                if (!_.isEqual(newVal, oldVal)) {
                    this.getItems()
                }
            },
            deep: false
        },
    },
    methods: {
        getItems() {
            if (cancel !== undefined) {
                cancel();
            }
            this.loading = true
            const params = {
                cancelToken: new CancelToken(function executor(c) {
                    cancel = c;
                }),
                params: {
                    search: this.search,
                    filters: this.filters,
                    ...this.pagination,
                }
            }

            axios.get('/jobs/' + this.platform, params)
                .then(response => {
                    this.loading = false
                    this.items = response.data.data
                    this.totalItems = response.data.meta.total
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    },
}
</script>
