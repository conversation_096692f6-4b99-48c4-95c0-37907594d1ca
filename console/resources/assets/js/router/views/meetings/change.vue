<template>
    <vue-calendly
            ref="calendly"
            v-on:calendly:event:scheduled="(payload) => save(payload)"
    ></vue-calendly>
</template>

<script>
import axios from "axios";
import VueCalendly from "../../../components/Calendly";

export default {
    name: "meeting-change",
    components: {VueCalendly},
    data() {
        return {
            site: {},
            user: {},
            errors: [],
        }
    },
    computed: {},
    methods: {
        cancel(meeting) {
            this.$refs.calendly.popup(meeting.cancel_url)
        },
        reschedule(meeting) {
            this.$refs.calendly.popup(meeting.reschedule_url)
        },
        save(payload) {
            // TODO: implement calencly.popup update events
            return
            let request = {
                ...payload,
                site_id: this.site ? this.site.id : null,
                user_id: this.user ? this.user.id : null,
            }

            axios.post('/meetings/', request)
                .then(response => {
                    this.$emit('meeting:create', response.data.data)
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    }
}
</script>

<style scoped>

</style>