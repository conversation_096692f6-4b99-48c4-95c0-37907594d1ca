<template>
    <layout-default>

        <v-container grid-list-md fluid>
            <lta-contract-details ref="details"></lta-contract-details>
        </v-container>

    </layout-default>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import NProgress from 'nprogress/nprogress'
import LayoutDefault from '@layouts/Default'
import LtaContractDetails from "./details.vue";

export default {
    components: {
        LtaContractDetails,
        LayoutDefault,
    },
    data() {
        return {
            item: null,
        }
    },
    created() {
        this.getItem()
    },
    methods: {
        getItem() {
            NProgress.start()
            axios.get(`/lta-contracts/${this.$route.params.id}`)
                .then(response => {
                    this.item = response.data.data
                    this.$refs.details.item = this.item
                    this.$refs.details.init()
                    NProgress.done()
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    }
}
</script>