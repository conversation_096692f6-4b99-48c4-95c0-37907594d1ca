<template>
    <layout-default>

        <v-container grid-list-md fluid>
            <v-card>
                <v-card-title>
                    <h1>
                        Functionality
                    </h1>
                    <v-spacer></v-spacer>
                    <v-text-field
                        append-icon="search"
                        label="Search"
                        single-line
                        v-model="search"
                        clearable
                    ></v-text-field>
                </v-card-title>
            </v-card>
            <v-data-table
                :headers="headers"
                :items="items"
                :search="search"
                :pagination.sync="pagination"
                :total-items="totalItems"
                :loading="loading"
                :custom-filter="customFilter"
                class="elevation-1"
                must-sort
            >
                <template slot="items" slot-scope="props">
                    <tr>
                        <td>{{ props.item.id }}</td>
                        <td>
                            <v-icon v-if="props.item.status" color="green">check_circle</v-icon>
                            <v-icon v-if="!props.item.status" color="red">block</v-icon>
                        </td>
                        <td v-if="props.item.id" class="text-xs-left px-0" nowrap>
                            <v-btn icon class="mx-0" @click="$refs.editDialog.edit(props.item)">
                                <v-icon color="teal">edit</v-icon>
                            </v-btn>
                        </td>
                        <td v-else=""></td>
                    </tr>
                </template>

                <v-alert slot="no-results" :value="true" color="error" icon="warning">
                    Your search for "{{ search }}" found no results.
                </v-alert>

            </v-data-table>

            <edit :items="items" ref="editDialog"/>
        </v-container>

    </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import {flatten} from '@helpers'
import Edit from "./edit";

export default {
    components: {
        Edit,
        LayoutDefault,
    },
    data() {
        return {
            search: '',
            totalItems: 0,
            items: [],
            loading: true,
            pagination: {
                rowsPerPage: 25,
                descending: true,
            },
            headers: [
                {text: 'ID', value: 'id'},
                {text: 'Status', value: 'status'},
                {text: '', value: 'id', sortable: false, align: 'center'}
            ],
            filters: {
                devMode: 0
            },
        }
    },
    created() {
        this.getItems()
    },
    methods: {
        getItems() {
            axios.get('/functionality')
                .then(response => {
                    this.loading = false
                    this.items = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        customFilter(items, search, filter) {
            search = search.toString().toLowerCase()
            if (search.trim() === '') return items

            return items.filter(item => item.flatten.some(val => filter(val, search)))
        },
    },
}
</script>