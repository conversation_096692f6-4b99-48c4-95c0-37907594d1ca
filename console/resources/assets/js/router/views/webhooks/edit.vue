<template>
    <v-dialog
        v-model="dialog"
        max-width="400px"
        scrollable
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h3 class="headline white--text mb-0">{{ title }}</h3>
            </v-card-title>

            <v-card-text>
                <v-container grid-list-md>
                    <v-layout wrap>
                        <v-flex xs12>
                            <v-text-field
                                label="Name"
                                v-model="editedItem.name"
                                required
                                :error-messages="errors.name"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12>
                            <v-text-field
                                label="Url"
                                v-model="editedItem.url"
                                required
                                :error-messages="errors.url"
                            ></v-text-field>
                        </v-flex>
                        <v-flex xs12>
                            <v-autocomplete
                                :items="events"
                                v-model="editedItem.event"
                                label="Event"
                            ></v-autocomplete>
                        </v-flex>
                        <v-flex xs12>
                            <v-switch
                                label="Active"
                                v-model="editedItem.active"
                            ></v-switch>
                        </v-flex>
                    </v-layout>
                </v-container>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                <v-btn
                    color="primary"
                    :loading="loading"
                    :disabled="loading"
                    flat
                    @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'

export default {
    name: "edit",
    props: ['items'],
    data() {
        return {
            title: '',
            dialog: false,
            loading: false,
            errors: [],
            editedItem: {},
            editedIndex: -1,
            events: [
                'admin.login',
                'admin.login_lite',
                'transaction.create',
                'subscription.create',
                'subscription.create.plan',
                'subscription.create.service',
                'subscription.create.application',
                'subscription.create.feature',
                'subscription.upcoming.payment',
                'subscription.renew',
                'subscription.update',
                'offer.create',
                'offer.update',
                'invoice.create',
                'invoice.update',
                'site.create',
                'site.boarding',
                'app.request',
            ]
        }
    },
    mounted() {
    },
    methods: {
        close() {
            this.dialog = false
        },
        add() {
            this.title = 'Create New Webhook'
            this.dialog = true
            this.errors = []
            this.editedItem = {}
            this.editedIndex = -1
        },
        edit(item) {
            this.editedItem = this.makeNonReactiveCopy(item)
            this.editedIndex = this.items.indexOf(item)
            this.errors = []
            this.title = 'Edit webhook ' + item.id
            this.dialog = true
        },
        save() {
            this.loading = true
            let success = {
                data: {
                    message: `Webhook ${this.editedItem.id} saved successfully`
                }
            }

            if (this.editedIndex > -1) {
                axios.put('/webhooks/' + this.editedItem.id, this.editedItem)
                    .then((response) => {
                        this.loading = false
                        Object.assign(this.items[this.editedIndex], response.data.data)
                        this.close()
                        this.handleSuccess(success)
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            } else {
                axios.post('/webhooks', this.editedItem)
                    .then((response) => {
                        this.loading = false
                        let item = response.data.data
                        this.items.unshift(item)
                        this.close()
                        this.handleSuccess(success)
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        }
    }
}
</script>