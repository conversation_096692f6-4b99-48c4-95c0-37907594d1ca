<template>
    <v-card>
        <v-card>
            <v-card-title>
                <h1>Ecosystem Services</h1>
                <v-spacer></v-spacer>
                <v-text-field
                        append-icon="search"
                        label="Search"
                        single-line
                        v-model="search"
                        clearable
                ></v-text-field>
                <v-btn
                        v-if="!hideNewRecord"
                        small
                        absolute
                        dark
                        fab
                        bottom
                        right
                        color="pink"
                        @click="$refs.editDialog.addItem()"
                >
                    <v-icon>add</v-icon>
                </v-btn>
            </v-card-title>
        </v-card>

        <v-data-table
                :headers="headers"
                :items="items"
                :search="search"
                :pagination.sync="pagination"
                :loading="loading"
                :custom-filter="customFilter"
                class="elevation-1"
        >
            <template slot="items" slot-scope="props">
                <tr>
                    <td>{{ props.item.id }}</td>
                    <td v-if="props.item.group_id">
                        {{ props.item.group.name['en'] }}<br>
                        <span v-if="props.item.tag">{{ props.item.tag.name }}</span>
                    </td>
                    <td v-else></td>
                    <td>{{ props.item.name['en'] }}</td>
                    <td>
                        <a v-if="props.item.expert_services_count" href="javascript:"
                           @click="$refs.relatedDialog.show(props.item)">
                            {{ props.item.expert_services_count }}
                        </a>
                        <span v-else>{{ props.item.expert_services_count }}</span>
                    </td>

                    <td class="text-xs-center px-0" nowrap>
                        <v-btn icon class="mx-0" @click="$refs.editDialog.editItem(props.item)">
                            <v-icon color="teal">edit</v-icon>
                        </v-btn>
                        <v-btn v-if="!props.item.archived" icon class="mx-0"
                               @click="archive(props.item)">
                            <v-icon color="error">archive</v-icon>
                        </v-btn>
                        <v-btn v-else icon class="mx-0" @click="unarchive(props.item)">
                            <v-icon color="success">unarchive</v-icon>
                        </v-btn>
                    </td>
                </tr>
            </template>

            <v-alert slot="no-results" :value="true" color="error" icon="warning">
                Your search for "{{ search }}" found no results.
            </v-alert>

        </v-data-table>

        <edit :items="items" ref="editDialog"/>
        <related-expert-services
                ref="relatedDialog"
        ></related-expert-services>
    </v-card>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import Edit from './edit'
import RelatedExpertServices from "../expert-services/related-expert-services";

export default {
    name: 'ecosystem-list-services',
    props: ['items', 'loading', 'hideNewRecord'],
    components: {
        RelatedExpertServices,
        Edit,
    },
    data() {
        return {
            search: '',
            pagination: {
                rowsPerPage: 25,
                descending: true,
            },
            headers: [
                {text: 'ID', value: 'id'},
                {text: 'Category', value: 'group_id'},
                {text: 'Name', value: 'name_translated'},
                {text: 'Experts Services', value: 'experts_count'},
                {text: '', value: 'id', sortable: false, align: 'center'}
            ],
        }
    },
    methods: {
        archive(item) {
            let editedIndex = this.items.indexOf(item)
            axios.delete('/services/' + item.id)
                .then(() => {
                    this.items.splice(editedIndex, 1)
                    this.$emit('service:archived')
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        unarchive(item) {
            let editedIndex = this.items.indexOf(item)
            axios.post('/services/unarchive/' + item.id)
                .then(() => {
                    this.items.splice(editedIndex, 1)
                    this.$emit('service:unarchived')
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        customFilter(items, search, filter) {
            search = search.toString().toLowerCase()
            if (search.trim() === '') return items

            return items.filter(item => item.flatten.some(val => filter(val, search)))
        },
        exportExcel() {
            axios.post('/services/export')
                .then(response => {
                    window.location = response.data.redirect
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    },
}
</script>