<template>
  <v-dialog
      v-model="dialog"
      max-width="400px"
      scrollable
      persistent
      @keydown.esc="closeDialogOnEsc"
  >
    <v-card>
      <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
        <h3 class="headline white--text mb-0">Add User</h3>
      </v-card-title>

      <v-card-text>
        <v-container grid-list-md>
          <v-layout wrap>
            <v-flex xs12>
              <v-autocomplete
                  ref="searchBar"
                  v-model="editedItem"
                  :items="searchResults"
                  hide-no-data
                  item-text="email"
                  item-value="email"
                  label="Type an gmail address to search"
                  :search-input.sync="searchText"
                  :messages="msg"
                  :loading="searchLoading"
                  :error-messages="errors.user_id"
                  append-icon=""
                  return-object
                  clearable
              ></v-autocomplete>
            </v-flex>
          </v-layout>
        </v-container>
      </v-card-text>

      <v-divider></v-divider>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
        <v-btn
            color="primary"
            :loading="loading"
            :disabled="loading"
            flat
            @click.native="save"
        >Save
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'

export default {
  name: "add-user",
  props: ['items'],
  data() {
    return {
      dialog: false,
      loading: false,
      searchLoading: false,
      errors: [],
      editedItem: {},
      searchResults: [],
      searchText: '',
      msg: '',
    }
  },
  mounted() {
  },
  watch: {
    searchText(val) {
      if (!val) {
        return
      }
      this.search();
    }
  },
  methods: {
    search() {
      if (this.searchLoading) {
        return
      }

      this.errors = []
      if (this.searchText.length < 6) {
        this.msg = `Minimum 6 symbols`
        return
      }

      this.searchLoading = true
      axios.post(`/user-search`, {email: this.searchText})
          .then((response) => {
            this.searchResults = response.data.data
          })
          .catch(error => {
            this.handleError(error)
          })
          .finally(() => {
            this.msg = `${this.searchResults.length} result(s) found`
            this.searchLoading = false
          })
    },
    close() {
      this.dialog = false
    },
    show() {
      this.editedItem = {}
      this.errors = []
      this.searchResults = []
      this.searchText = ''
      this.msg = ''
      this.dialog = true
    },
    save() {
      this.loading = true

      axios.post('/users', this.editedItem)
          .then((response) => {
            this.loading = false
            this.items.unshift(response.data.data)
            this.close()
          })
          .catch(error => {
            this.handleError(error)
          })
    },
  }
}
</script>