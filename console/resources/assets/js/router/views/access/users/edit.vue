<template>
    <v-dialog
            v-model="dialog"
            max-width="500px"
            scrollable
            persistent
    >
        <v-card>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'">
                <h3 class="headline white--text mb-0">Edit User</h3>
            </v-card-title>

            <v-card-text>
                <v-layout wrap>
                    <v-flex xs12 sm6>
                        <v-list two-line dense class="transparent list-p0">
                            <v-list-tile>
                                <v-list-tile-avatar>
                                    <v-avatar>
                                        <img :src="editedItem.avatar" alt="Avatar">
                                    </v-avatar>
                                </v-list-tile-avatar>
                                <v-list-tile-content>
                                    <v-list-tile-title>{{ editedItem.name }}</v-list-tile-title>
                                    <v-list-tile-sub-title>{{ editedItem.email }}</v-list-tile-sub-title>
                                </v-list-tile-content>
                            </v-list-tile>
                        </v-list>
                    </v-flex>
                    <v-flex xs12 sm6>
                        <v-switch
                                label="CloudCart Employee"
                                v-model="editedItem.is_cc_employee"
                        ></v-switch>
                    </v-flex>
                </v-layout>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-text v-if="editedItem.is_cc_employee">
                <v-layout wrap>
                    <v-flex xs12 sm6>
                        <v-text-field
                                v-model="editedItem.phone"
                                label="Mobile Phone"
                                :error-messages="errors.phone"
                                prepend-icon="phone"
                        ></v-text-field>
                    </v-flex>
                    <v-flex xs12 sm6>
                        <v-text-field
                                v-model="editedItem.voip"
                                label="Voip Number"
                                :error-messages="errors.voip"
                                prepend-icon="phone"
                        ></v-text-field>
                    </v-flex>
                    <v-flex xs12 sm4>
                        <v-text-field
                                v-model="editedItem.target_new"
                                label="Target new revenue"
                                :error-messages="errors.target_new"
                                prepend-icon="euro"
                        ></v-text-field>
                    </v-flex>
                    <v-flex xs12 sm4>
                        <v-text-field
                                v-model="editedItem.target_rec"
                                label="Target rec. revenue"
                                :error-messages="errors.target_rec"
                                prepend-icon="euro"
                        ></v-text-field>
                    </v-flex>
                    <v-flex xs12 sm4>
                        <v-text-field
                                v-model="editedItem.max_discount"
                                label="Max Discount"
                                :error-messages="errors.max_discount"
                                prepend-icon="percent"
                        ></v-text-field>
                    </v-flex>
                    <v-flex xs12>
                        <v-text-field
                                v-model="editedItem.calendly_url"
                                label="Calendly URL"
                                :error-messages="errors.calendly_url"
                                prepend-icon="event"
                        ></v-text-field>
                    </v-flex>
                    <v-flex xs12>
                        <v-autocomplete
                                browser-autocomplete="chrome-off"
                                :items="countries"
                                v-model="editedItem.countries"
                                label="Countries"
                                prepend-icon="language"
                                item-text="name"
                                item-value="code"
                                clearable
                                multiple
                                deletable-chips
                                small-chips
                                color="primary"
                        ></v-autocomplete>
                    </v-flex>
                    <v-flex xs11>
                        <v-autocomplete
                                :items="groups"
                                v-model="editedItem.group_ids"
                                label="AM Groups"
                                prepend-icon="people"
                                item-text="name"
                                item-value="id"
                                multiple
                                deletable-chips
                                small-chips
                                clearable
                        ></v-autocomplete>
                    </v-flex>
                    <v-flex xs1>
                        <v-btn
                                small
                                dark
                                fab
                                color="pink"
                                @click="$refs.groupDialog.show()"
                        >
                            <v-icon>edit</v-icon>
                        </v-btn>
                    </v-flex>
                </v-layout>
            </v-card-text>

            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                <v-btn
                        color="primary"
                        :loading="loading"
                        :disabled="loading"
                        flat
                        @click.native="save"
                >Save
                </v-btn>
            </v-card-actions>
        </v-card>

        <list-user-groups ref="groupDialog" :items="groups"></list-user-groups>
    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import ListUserGroups from "./list-groups.vue";

export default {
    name: "edit",
    props: ['items'],
    components: {ListUserGroups},
    data() {
        return {
            title: '',
            dialog: false,
            loading: false,
            errors: [],
            editedItem: {},
            editedIndex: -1,
            countries: [],
            groups: [],
        }
    },
    created() {
    },
    mounted() {
        this.getGroups()
        this.getLocales()
    },
    methods: {
        close() {
            this.dialog = false
        },
        getGroups() {
            axios.get('/user-groups')
                .then(response => {
                    this.groups = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getLocales() {
            axios.get('/merchants/locales')
                .then(response => {
                    this.countries = response.data.data.country
                    // this.lang = response.data.data.lang
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        edit(item) {
            this.editedIndex = this.items.indexOf(item)
            this.editedItem = this.makeNonReactiveCopy(item)
            this.errors = []
            this.dialog = true
        },
        save() {
            this.loading = true
            axios.put('/users/' + this.editedItem.user_id, this.editedItem)
                .then((response) => {
                    this.loading = false
                    Object.assign(this.items[this.editedIndex], response.data.data)
                    this.close()
                })
                .catch(error => {
                    this.handleError(error)
                })
        }
    }
}
</script>
