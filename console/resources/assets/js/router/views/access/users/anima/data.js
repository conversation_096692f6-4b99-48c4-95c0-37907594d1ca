export const acquisition1Data = {
    span: "Contacts Step 1",
};

export const acquisition2Data = {
    span: "Lead Step 1",
    className: "acquisition",
};

export const acquisition3Data = {
    span: "Lead Step 2",
    className: "acquisition",
};

export const acquisition4Data = {
    span: "Prospect Step 1",
    className: "acquisition",
};

export const acquisition5Data = {
    span: "Prospect Step 2",
    className: "acquisition-2",
};

export const acquisition6Data = {
    span: "Prospect Step 3",
    className: "acquisition",
};

export const frame321Data = {
    span: "Not Interested",
};

export const frame322Data = {
    span: "Paid",
    className: "frame-33",
};

export const frame642Data = {
    className: "frame-64",
};

export const frame652Data = {
    className: "frame-65-1",
};

export const frame401Data = {
    frame64Props: frame642Data,
    frame65Props: frame652Data,
};

export const frame643Data = {
    className: "frame-64",
};

export const frame653Data = {
    className: "frame-65-1",
};

export const frame402Data = {
    className: "frame-41",
    frame64Props: frame643Data,
    frame65Props: frame653Data,
};

export const vTextField1Data = {
    x01051305Vs01041305: "01.05 - 01.05",
};

export const vTextField2Data = {
    x01051305Vs01041305: " 01.04 - 13.05",
    className: "v-text-field-2-1",
};

export const frame411Data = {
    vTextField1Props: vTextField1Data,
    vTextField2Props: vTextField2Data,
};

export const vTab2Data = {
    children: "MEETINGS",
};

export const vTabData = {
    vTab2Props: vTab2Data,
};

export const vTab41Data = {
    children: "CALLS",
    className: "",
};

export const vTab31Data = {
    vTab4Props: vTab41Data,
};

export const vTab42Data = {
    children: "PAID OFFERS",
    className: "v-tab-8",
};

export const vTab32Data = {
    className: "v-tab-4",
    vTab4Props: vTab42Data,
};

export const vTab43Data = {
    children: "SENT OFFERS",
    className: "v-tab-10",
};

export const vTab33Data = {
    className: "v-tab-5",
    vTab4Props: vTab43Data,
};

export const vTablistData = {
    vTabProps: vTabData,
    vTab31Props: vTab31Data,
    vTab32Props: vTab32Data,
    vTab33Props: vTab33Data,
};

export const vCardText21Data = {
    spanText2: "1",
};

export const vCardText22Data = {
    spanText2: "2",
};

export const vCardText23Data = {
    spanText2: "2",
};

export const vTextField3Data = {
    x01051305Vs01041305: "01.05 - 01.05",
};

export const vTextField4Data = {
    x01051305Vs01041305: " 01.04 - 13.05",
    className: "v-text-field-2",
};

export const frame412Data = {
    className: "frame-41-2",
    vTextField1Props: vTextField3Data,
    vTextField2Props: vTextField4Data,
};

export const vCardData = {
    vCard__Title: "Total revenue",
    vCard__Text1: "€ 2 324",
    vCard__Text2: "€ 21 234",
};

export const vCardTitle1Data = {
    children: "Paid offers",
};

export const vCardText61Data = {
    children: "12 / 25 paid",
};

export const vCard2Data = {
    spanText1: "32% ",
    spanText2: "/ 55% CR",
    vCardTitleProps: vCardTitle1Data,
    vCardText6Props: vCardText61Data,
};

export const vCard3Data = {
    vCard__Title: "Revenue existing Merchants",
    vCard__Text1: "€ 2 039",
    vCard__Text2: "€ 2 019",
};

export const vCardText43Data = {
    className: "v-card__text-17",
};

export const vCardText62Data = {
    children: "€ 0 / € 1030",
};

export const vCard4Data = {
    vCard__Title: "New paid Merchants",
    spanText1: "0 ",
    spanText2: "/ 12",
    vCardText4Props: vCardText43Data,
    vCardText6Props: vCardText62Data,
};

export const vCardTitle2Data = {
    children: "Sent offers",
    className: "v-card__title-4",
};

export const vCard5Data = {
    spanText1: "42 ",
    spanText2: "/ € 23 234",
    vCard__Text: "22 / € 13 234",
    vCardTitleProps: vCardTitle2Data,
};

export const vCard6Data = {
    vCard__Title: "Forecast revenue",
    vCard__Text: "€ 65 032",
};

export const vCard7Data = {
    vCard__Title1: "Alexander Hristov",
    vCard__Text1: "Account Manager",
    vCard__Text2: "14 monts",
    vCard__Title2: "Target new revenue",
    vCard__Text3: "€ 5000",
    spanText1: "€ 2324 ",
    spanText2: "/ 45%",
    vCard__Title3: "Target rec. revenue",
    vCard__Text4: "€ 24000",
    spanText3: "€ 2324 ",
    spanText4: "/ 5%",
};

export const frame1Data = {
    profilePic64X64: "https://anima-uploads.s3.amazonaws.com/projects/62a208a7adcd216dc0661c45/releases/62a208b6c2e118b9520e92d6/img/<EMAIL>",
    image1: "https://anima-uploads.s3.amazonaws.com/projects/62a208a7adcd216dc0661c45/releases/62a208b6c2e118b9520e92d6/img/<EMAIL>",
    span1: "Stage:",
    span2: "Acquisition",
    span3: "Store",
    span4: "Plan",
    span5: "Stage",
    span6: "Merchant",
    span7: "Actions",
    phone: "30753",
    text1: "/",
    date: "08.06.2022",
    name: "Peter Iliev",
    x2Stores: "(2 Stores)",
    place: "Bulgaria",
    span8: "Commercial activities",
    span9: "Contacts",
    span10: "Leads",
    span11: "Prospects",
    span12: "Merchants",
    spanText1: "2 ",
    spanText2: "/ 12",
    spanText3: " planned",
    spanText4: "1 ",
    spanText5: "/ 3",
    spanText6: " planned",
    vCard__Text: " -",
    spanText7: "Prev. ",
    spanText8: "12",
    spanText9: "3 / ",
    spanText10: "0",
    spanText11: " planned",
    span13: "Performance",
    acquisition1Props: acquisition1Data,
    acquisition2Props: acquisition2Data,
    acquisition3Props: acquisition3Data,
    acquisition4Props: acquisition4Data,
    acquisition5Props: acquisition5Data,
    acquisition6Props: acquisition6Data,
    frame321Props: frame321Data,
    frame322Props: frame322Data,
    frame401Props: frame401Data,
    frame402Props: frame402Data,
    frame411Props: frame411Data,
    vTablistProps: vTablistData,
    vCardText21Props: vCardText21Data,
    vCardText22Props: vCardText22Data,
    vCardText23Props: vCardText23Data,
    frame412Props: frame412Data,
    vCardProps: vCardData,
    vCard2Props: vCard2Data,
    vCard3Props: vCard3Data,
    vCard4Props: vCard4Data,
    vCard5Props: vCard5Data,
    vCard6Props: vCard6Data,
    vCard7Props: vCard7Data,
};

