<template>
    <v-layout class="mx-0" row wrap>
        <v-flex>
            <v-card height="100%">
                <v-card-text>
                    <div class="counts">
                        <div class="headline">
                            <a @click="click('sites')">Sites</a>
                        </div>
                        <div>Total: <strong>{{ counts.sites.total }}</strong></div>
                        <div>Lead: <strong>{{ counts.sites.lead }}</strong></div>
                        <div>Prospect: <strong>{{ counts.sites.prospect }}</strong></div>
                        <div>Paid: <strong>{{ counts.sites.paid }}</strong></div>
                        <div>Expired: <strong>{{ counts.sites.expired }}</strong></div>
                    </div>
                </v-card-text>
            </v-card>
        </v-flex>

        <v-flex>
            <v-card height="100%">
                <v-card-text>
                    <div class="counts">
                        <div class="headline">
                            <a @click="click('offers')">Offers</a>
                        </div>
                        <div>Total: <strong>{{ counts.offers.total }}</strong></div>
                        <div>Paid: <strong>{{ counts.offers.paid }}</strong></div>
                        <div>Partial Paid: <strong>{{ counts.offers.partial }}</strong></div>
                        <div>Failed: <strong>{{ counts.offers.failed }}</strong></div>
                        <div>Expired: <strong>{{ counts.offers.expired }}</strong></div>
                    </div>
                </v-card-text>
            </v-card>
        </v-flex>

        <v-flex>
            <v-card height="100%">
                <v-card-text>
                    <div class="counts">
                        <div class="headline">
                            <a @click="click('todos')">Tasks</a>
                        </div>
                        <div>Total: <strong>{{ counts.todos.total }}</strong></div>
                        <div>Completed: <strong>{{ counts.todos.completed }}</strong></div>
                        <div>Pending: <strong>{{ counts.todos.pending }}</strong></div>
                        <div>Canceled: <strong>{{ counts.todos.canceled }}</strong></div>
                        <div>Missing Notes: <strong>{{ counts.todos.no_note }}</strong></div>
                    </div>
                </v-card-text>
            </v-card>
        </v-flex>

        <v-flex>
            <v-card height="100%">
                <v-card-text>
                    <div class="counts">
                        <div class="headline">
                            <a @click="click('meetings')">Meetings</a>
                        </div>
                        <div>Total: <strong>{{ counts.meetings.total }}</strong></div>
                        <div>Upcoming: <strong>{{ counts.meetings.upcoming }}</strong></div>
                        <div>Past: <strong>{{ counts.meetings.past }}</strong></div>
                        <div>Canceled: <strong>{{ counts.meetings.canceled }}</strong></div>
                        <div>Missing Notes: <strong>{{ counts.meetings.no_note }}</strong></div>
                    </div>
                </v-card-text>
            </v-card>
        </v-flex>

        <v-flex>
            <v-card height="100%">
                <v-card-text>
                    <div class="counts">
                        <div class="headline">
                            <a @click="click('calls')">Calls</a>
                        </div>
                        <br>
                        <div>Total: <strong>{{ counts.calls.total }}</strong></div>
                        <div>Answered: <strong>{{ counts.calls.answered }}</strong></div>
                        <div>Not Answered: <strong>{{ counts.calls.not_answered }}</strong></div>
                        <div>Missing Notes: <strong>{{ counts.calls.no_note }}</strong></div>
                    </div>
                </v-card-text>
            </v-card>
        </v-flex>

<!--        <v-flex xs6 sm2>-->
<!--            <v-card height="100%">-->
<!--                <v-card-text>-->
<!--                    <div class="counts">-->
<!--                        <div class="headline">-->
<!--                            <a @click="click('subscriptions')">Subscriptions</a>-->
<!--                        </div>-->
<!--                        <div>Total: <strong>{{ counts.subscriptions.total }}</strong></div>-->
<!--                        <div>Active: <strong>{{ counts.subscriptions.active }}</strong></div>-->
<!--                        <div>Past Due: <strong>{{ counts.subscriptions.past }}</strong></div>-->
<!--                        <div>Canceled: <strong>{{ counts.subscriptions.canceled }}</strong></div>-->
<!--                        <div>Expired: <strong>{{ counts.subscriptions.expired }}</strong></div>-->
<!--                    </div>-->
<!--                </v-card-text>-->
<!--            </v-card>-->
<!--        </v-flex>-->
    </v-layout>
</template>

<script>
import moment from "moment";

export default {
    name: "user-counts",
    props: ['todos', 'meetings', 'calls', 'sites', 'offers', 'subscriptions'],
    computed: {
        counts() {
            let counts = {
                todos: {
                    total: 0,
                    pending: 0,
                    completed: 0,
                    canceled: 0,
                    no_note: 0,
                },
                calls: {
                    total: 0,
                    answered: 0,
                    not_answered: 0,
                    no_note: 0,
                },
                meetings: {
                    total: 0,
                    upcoming: 0,
                    past: 0,
                    canceled: 0,
                    no_note: 0,
                },
                sites: {
                    total: 0,
                    lead: 0,
                    prospect: 0,
                    paid: 0,
                    expired: 0,
                },
                offers: {
                    total: 0,
                    paid: 0,
                    partial: 0,
                    failed: 0,
                    expired: 0,
                },
                subscriptions: {
                    total: 0,
                    active: 0,
                    past: 0,
                    canceled: 0,
                    expired: 0,
                },
            }

            counts.todos.total = this.todos.length
            this.todos.map((item) => {
                switch (item.status) {
                    case 1:
                        counts.todos.pending++;
                        break
                    case 2:
                        counts.todos.completed++;
                        break
                    case 3:
                        counts.todos.canceled++;
                        break
                }

                if (!item.note) {
                    counts.todos.no_note++;
                }
            })

            counts.meetings.total = this.meetings.length
            this.meetings.map((item) => {
                if (!item.note) {
                    counts.meetings.no_note++;
                }
                if (item.status === 2) {
                    counts.meetings.canceled++;
                }
                if (moment(item.start_time).isSameOrAfter()) {
                    counts.meetings.upcoming++;
                } else {
                    counts.meetings.past++;
                }
            })

            counts.calls.total = this.calls.length
            this.calls.map((item) => {
                if (item.disposition === 'ANSWER') {
                    counts.calls.answered++;
                } else {
                    counts.calls.not_answered++;
                }

                if (!item.note) {
                    counts.calls.no_note++;
                }
            })

            counts.sites.total = this.sites.length
            this.sites.map((item) => {
                if (item.status === 3) {
                    counts.sites.expired++;
                }

                switch (item.store_type) {
                    case 1:
                        counts.sites.lead++;
                        break
                    case 2:
                        counts.sites.prospect++;
                        break
                    case 3:
                        counts.sites.paid++;
                        break
                }
            })

            counts.offers.total = this.offers.length
            this.offers.map((item) => {
                switch (item.status) {
                    case 4:
                        counts.offers.partial++;
                        break
                    case 5:
                        counts.offers.paid++;
                        break
                    case 6:
                        counts.offers.failed++;
                        break
                    case 7:
                        counts.offers.expired++;
                        break
                }
            })

            counts.subscriptions.total = this.subscriptions.length
            this.subscriptions.map((item) => {
                switch (item.status) {
                    case 0:
                        counts.subscriptions.canceled++;
                        break
                    case 1:
                        counts.subscriptions.active++;
                        break
                    case 2:
                        counts.subscriptions.past++;
                        break
                    case 3:
                        counts.subscriptions.expired++;
                        break
                }
            })

            return counts
        }
    },
    methods: {
        click(type) {
            this.$emit('counts:click', type)
        },
    },
}
</script>

<style scoped>
.counts strong {
    float: right;
}
</style>