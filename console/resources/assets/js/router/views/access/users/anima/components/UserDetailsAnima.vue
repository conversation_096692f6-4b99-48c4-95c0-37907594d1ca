<template>
    <div class="container-center-horizontal">
        <div class="frame-1 screen">
            <div class="overlap-group4">
                <user-targets
                    :item="item"
                    :targets="targets"
                />

                <v-card class="px-3 py-3 mb-3" style="overflow: hidden;">
                    <div class="frame-49">
                        <div class="th-12">
                            <div class="span roboto-normal-black-26px">Performance</div>
                        </div>
                        <range
                            :start_date="performance_start_date"
                            :end_date="performance_end_date"
                        />
                    </div>
                    <v-layout row style="overflow-x: auto;">
                        <v-flex v-if="performance.total_revenue" xs12>
                            <div class="border-1px-black-6 px-3 py-3 nowrap fixed-width-250">
                                <div>
                                    <div class="roboto-medium-black-16px">
                                        Total revenue
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div>
                                        <i v-if="performance.total_revenue.current > performance.total_revenue.previous"
                                           class="fa fa-long-arrow-up long-arrow-alt-down-2 fontawesome5pro-regular-normal-forest-green-24px"
                                        ></i>
                                        <i v-else
                                           class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-24px"
                                        ></i>
                                        <span class="roboto-normal-black-24px">
                                            {{ performance.total_revenue.current | toCurrency('EUR') }}
                                        </span>
                                    </div>
                                    <div>
                                        <span class="roboto-normal-black-14px">
                                            {{ performance.total_revenue.previous | toCurrency('EUR') }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </v-flex>

                        <v-flex v-if="performance.paid_offers" xs12>
                            <div class="border-1px-black-6 px-3 py-3 nowrap fixed-width-250">
                                <div>
                                    <div class="roboto-medium-black-16px">
                                        Paid offers
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div>
                                        <i v-if="performance.paid_offers.current.cr > performance.paid_offers.previous.cr"
                                           class="fa fa-long-arrow-up long-arrow-alt-down-2 fontawesome5pro-regular-normal-forest-green-24px"
                                        ></i>
                                        <i v-else
                                           class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-24px"
                                        ></i>
                                        <span class="roboto-normal-black-24px">
                                            <span>
                                                <span class="roboto-normal-black-24px">
                                                    {{ performance.paid_offers.current.cr }}%
                                                </span>
                                                <span class="roboto-normal-black-24px-2">
                                                    / {{ performance.paid_offers.previous.cr }}% CR
                                                </span>
                                            </span>
                                        </span>
                                    </div>
                                    <div>
                                        <span class="roboto-normal-black-14px">
                                            {{
                                                performance.paid_offers.current.count
                                            }} / {{ performance.paid_offers.previous.count }} paid
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </v-flex>

                        <v-flex v-if="performance.existing_stores_revenue" xs12>
                            <div class="border-1px-black-6 px-3 py-3 nowrap fixed-width-250">
                                <div>
                                    <div class="roboto-medium-black-16px">
                                        Revenue existing Stores
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div>
                                        <i v-if="performance.existing_stores_revenue.current.amount > performance.existing_stores_revenue.previous.amount"
                                           class="fa fa-long-arrow-up long-arrow-alt-down-2 fontawesome5pro-regular-normal-forest-green-24px"
                                        ></i>
                                        <i v-else
                                           class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-24px"
                                        ></i>
                                        <span class="roboto-normal-black-24px">
                                            {{ performance.existing_stores_revenue.current.amount | toCurrency }}
                                        </span>
                                    </div>
                                    <div>
                                        <span class="roboto-normal-black-14px">
                                            {{ performance.existing_stores_revenue.previous.amount | toCurrency }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </v-flex>
                        <v-flex v-if="performance.new_paid_stores" xs12>
                            <div class="border-1px-black-6 px-3 py-3 nowrap fixed-width-250">
                                <div>
                                    <div class="roboto-medium-black-16px">
                                        New paid Stores
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div>
                                        <i v-if="performance.new_paid_stores.current.count > performance.new_paid_stores.previous.count"
                                           class="fa fa-long-arrow-up long-arrow-alt-down-2 fontawesome5pro-regular-normal-forest-green-24px"
                                        ></i>
                                        <i v-else
                                           class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-24px"
                                        ></i>
                                        <span class="roboto-normal-black-24px">
                                            <span class="roboto-normal-black-24px">
                                                {{ performance.new_paid_stores.current.count }}
                                            </span>
                                            <span class="roboto-normal-black-24px-2">
                                                / {{ performance.new_paid_stores.previous.count }}
                                            </span>
                                        </span>
                                    </div>
                                    <div>
                                        <span class="roboto-normal-black-14px">
                                            {{ performance.new_paid_stores.current.amount | toCurrency }}
                                            /
                                            {{ performance.new_paid_stores.previous.amount | toCurrency }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </v-flex>

                        <v-flex v-if="performance.sent_offers" xs12>
                            <div class="border-1px-black-6 px-3 py-3 nowrap fixed-width-250">
                                <div>
                                    <div class="roboto-medium-black-16px">
                                        Sent offers
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div>
                                        <i v-if="performance.sent_offers.current.count > performance.sent_offers.previous.count"
                                           class="fa fa-long-arrow-up long-arrow-alt-down-2 fontawesome5pro-regular-normal-forest-green-24px"
                                        ></i>
                                        <i v-else
                                           class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-24px"
                                        ></i>
                                        <span class="roboto-normal-black-24px">
                                            <span class="roboto-normal-black-24px">
                                                {{ performance.sent_offers.current.count }}
                                            </span>
                                            <span class="roboto-normal-black-24px-2">
                                                / {{ performance.sent_offers.current.amount | toCurrency('EUR') }}
                                            </span>
                                        </span>
                                    </div>
                                    <div>
                                        <span class="roboto-normal-black-14px">
                                            {{
                                                performance.sent_offers.previous.count
                                            }} / {{ performance.sent_offers.previous.amount | toCurrency('EUR') }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </v-flex>
                        <v-flex v-if="performance.forecast_revenue > -1" xs12>
                            <div class="border-1px-black-6 px-3 py-3 nowrap fixed-width-250">
                                <div>
                                    <div>
                                        <span class="roboto-medium-black-16px">Forecast revenue</span>
                                    </div>
                                    <div class="pt-4 mt-1">
                                        <span class="roboto-normal-black-24px">
                                            {{ performance.forecast_revenue | toCurrency('EUR') }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </v-flex>
                    </v-layout>
                </v-card>

                <v-card v-if="false" class="px-3 py-3 mb-3">
                    <div class="frame-49">
                        <div class="th-7">
                            <div class="span roboto-normal-black-26px">{{ frame1Data.span8 }}</div>
                        </div>
                        <range
                            :vTextField1Props="frame1Data.frame411Props.vTextField1Props"
                            :vTextField2Props="frame1Data.frame411Props.vTextField2Props"
                        />
                    </div>

                    <v-tabs>
                        <v-tab key="1">{{ frame1Data.vTablistProps.vTabProps.vTab2Props.children }}</v-tab>
                        <v-tab key="2">{{ frame1Data.vTablistProps.vTab31Props.vTab4Props.children }}</v-tab>
                        <v-tab key="3">{{ frame1Data.vTablistProps.vTab32Props.vTab4Props.children }}</v-tab>
                        <v-tab key="4">{{ frame1Data.vTablistProps.vTab33Props.vTab4Props.children }}</v-tab>

                        <v-tabs-items overflow-sm-hidden>
                            <v-tab-item key="1">
                                <v-data-table
                                    :headers="headers1"
                                    :items="items1"
                                    class="th-commercial-activities"
                                >
                                    <template slot="items" slot-scope="props">
                                        <tr>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                        <span>
                                                            <span class="roboto-semi-bold-black-20px">{{
                                                                    frame1Data.spanText1
                                                                }}</span>
                                                            <span class="roboto-semi-bold-black-20px-2">{{
                                                                    frame1Data.spanText2
                                                                }}</span>
                                                            <span class="roboto-normal-black-20px">{{
                                                                    frame1Data.spanText3
                                                                }}</span>
                                                        </span>
                                                    </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px">
                                                      <span>
                                                          <span class="roboto-normal-black-14px">Prev. </span>
                                                          <span class="roboto-semi-bold-black-14px">1</span>
                                                      </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">{{
                                                                frame1Data.spanText4
                                                            }}</span>
                                                        <span class="roboto-semi-bold-black-20px-2">{{
                                                                frame1Data.spanText5
                                                            }}</span>
                                                        <span class="roboto-normal-black-20px">{{
                                                                frame1Data.spanText6
                                                            }}</span>
                                                    </span>
                                            </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px">
                                                      <span>
                                                          <span class="roboto-normal-black-14px">Prev. </span>
                                                          <span class="roboto-semi-bold-black-14px">2</span>
                                                      </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="v-card__text-1 roboto-semi-bold-black-20px">
                                                        {{ frame1Data.vCard__Text }}
                                                    </span>
                                                    </div>
                                                    <div>
                                                        <div class="roboto-normal-black-14px">
                                                        <span>
                                                            <span class="roboto-normal-black-14px">
                                                                {{ frame1Data.spanText7 }}</span>
                                                            <span class="roboto-semi-bold-black-14px">
                                                                {{ frame1Data.spanText8 }}</span>
                                                        </span>
                                                        </div>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>
                                                        <span class="v-card__text-1 roboto-semi-bold-white-20px">
                                                <span>
                                                    <span class="roboto-semi-bold-black-20px">
                                                        {{ frame1Data.spanText9 }}
                                                    </span>
                                                    <span class="roboto-semi-bold-black-20px-2">
                                                        {{ frame1Data.spanText10 }}
                                                    </span>
                                                    <span class="roboto-normal-black-20px">
                                                        {{ frame1Data.spanText11 }}
                                                    </span>
                                                </span>
                                            </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px">
                                                      <span>
                                                          <span class="roboto-normal-black-14px">Prev. </span>
                                                          <span class="roboto-semi-bold-black-14px">2</span>
                                                      </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                        </tr>
                                    </template>

                                </v-data-table>
                            </v-tab-item>

                            <v-tab-item key="2">
                                <v-data-table
                                    :headers="headers1"
                                    :items="items1"
                                    class="th-commercial-activities"
                                >
                                    <template slot="items" slot-scope="props">
                                        <tr>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">5</span>
                                                        <span class="roboto-semi-bold-black-20px-2">calls /</span>
                                                        <span class="roboto-semi-bold-black-20px">90</span>
                                                        <span class="roboto-semi-bold-black-20px-2">min.</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="v-card__text-5">
                                                        <div class="roboto-normal-black-14px">
                                                    <span>
                                                        <span
                                                            class="roboto-normal-black-14px">15 calls / 120 min.</span>
                                                    </span>
                                                        </div>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                        <span>
                                                            <span class="roboto-semi-bold-black-20px">{{
                                                                    frame1Data.spanText4
                                                                }}</span>
                                                            <span class="roboto-semi-bold-black-20px-2">{{
                                                                    frame1Data.spanText5
                                                                }}</span>
                                                            <span class="roboto-normal-black-20px">{{
                                                                    frame1Data.spanText6
                                                                }}</span>
                                                        </span>
                                                </span>
                                                    </div>
                                                    <div class="v-card__text-5">
                                                        <div class="roboto-normal-black-14px">
                                                    <span>
                                                        <span class="roboto-normal-black-14px">1 call / 20 min.</span>
                                                    </span>
                                                        </div>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <div
                                                            class="roboto-semi-bold-black-20px">
                                                            {{ frame1Data.vCard__Text }}
                                                        </div>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">{{
                                                                frame1Data.spanText9
                                                            }}</span>
                                                        <span class="roboto-semi-bold-black-20px-2">{{
                                                                frame1Data.spanText10
                                                            }}</span>
                                                        <span class="roboto-normal-black-20px">{{
                                                                frame1Data.spanText11
                                                            }}</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="v-card__text-5">
                                                        <div class="roboto-normal-black-14px">
                                                    <span>
                                                        <span class="roboto-normal-black-14px">-</span>
                                                    </span>
                                                        </div>
                                                    </div>
                                                </v-flex>
                                            </td>
                                        </tr>
                                    </template>

                                </v-data-table>
                            </v-tab-item>

                            <v-tab-item key="3">
                                <v-data-table
                                    :items="items1"
                                    class="th-commercial-activities"
                                >
                                    <template slot="items" slot-scope="props">
                                        <tr @click="showMorePaidOffers = !showMorePaidOffers">
                                            <td>
                                                <v-flex md7 nowrap>
                                                    <span class="roboto-semi-bold-black-20px">Total Sent Offers</span>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div class="roboto-semi-bold-black-16px-2 mb-2">Merchants:</div>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                <span>
                                                    <span class="roboto-semi-bold-black-20px">4</span>
                                                    <span class="roboto-semi-bold-black-20px-2">/</span>
                                                        <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>

                                                    <span class="roboto-semi-bold-black-20px">€ 3 042</span>
                                                </span>
                                            </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                            <span>
                                                <span class="roboto-normal-black-14px">4 / € 3 002</span>
                                            </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex xs1 text-center nowrap>
                                                    <i class="fa fa-2x fa-chevron-down" v-if="!showMorePaidOffers"></i>
                                                    <i class="fa fa-2x fa-chevron-up" v-else></i>
                                                </v-flex>
                                            </td>
                                        </tr>
                                        <tr class="border-1px-black-6" v-if="showMorePaidOffers">
                                            <td>
                                                <v-flex nowrap>
                                                    <span>New Merchants revenue</span>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex xs12 md2 offset-md2 py-3 nowrap>
                                                    -
                                                </v-flex>
                                            </td>
                                            <td></td>
                                        </tr>
                                        <tr class="border-1px-black-6" v-if="showMorePaidOffers">
                                            <td>
                                                <v-flex nowrap>
                                                    <span>Existing Merchants</span>
                                                    <strong>New revenue</strong>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                              <span>
                                                  <span class="roboto-semi-bold-black-20px">2</span>
                                                  <span class="roboto-semi-bold-black-20px-2">/</span>
                                                      <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>

                                                  <span class="roboto-semi-bold-black-20px">€ 500</span>
                                              </span>
                                          </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                            <span>
                                                <span class="roboto-normal-black-14px">1 / € 250</span>
                                            </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                        </tr>
                                        <tr class="border-1px-black-6" v-if="showMorePaidOffers">
                                            <td>
                                                <v-flex nowrap>
                                                    <span>Existing Merchants</span>
                                                    <strong>Recurring revenue</strong>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">2</span>
                                                        <span class="roboto-semi-bold-black-20px-2">/</span>
                                                            <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>

                                                        <span class="roboto-semi-bold-black-20px">€ 2 500</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                            <span>
                                                <span class="roboto-normal-black-14px">3 / € 1 000</span>
                                            </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                        </tr>
                                    </template>
                                </v-data-table>
                            </v-tab-item>

                            <v-tab-item key="4">
                                <v-data-table
                                    :items="items1"
                                    class="th-commercial-activities"
                                >
                                    <template slot="items" slot-scope="props">
                                        <tr @click="showMoreSendOffers = !showMoreSendOffers">
                                            <td>
                                                <v-flex nowrap>
                                                    <span class="roboto-semi-bold-black-20px">Total Sent Offers</span>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div class="roboto-semi-bold-black-16px-2 mb-2">Prospects:</div>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                <span>
                                                    <span class="roboto-semi-bold-black-20px">62</span>
                                                    <span class="roboto-semi-bold-black-20px-2">/</span>
                                                    <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>
                                                    <span class="roboto-semi-bold-black-20px">€ 30 042</span>
                                                </span>
                                            </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                        <span>
                                            <span class="roboto-normal-black-14px">76 / € 30 42</span>
                                        </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                <span>
                                                    <span class="roboto-semi-bold-black-20px">4</span>
                                                    <span class="roboto-semi-bold-black-20px-2">/</span>
                                                        <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>
                                                    <span class="roboto-semi-bold-black-20px">€ 3 042</span>
                                                </span>
                                            </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                        <span>
                                            <span class="roboto-normal-black-14px">4 / € 3 002</span>
                                        </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex text-center nowrap>
                                                    <i class="fa fa-2x fa-chevron-down" v-if="!showMoreSendOffers"></i>
                                                    <i class="fa fa-2x fa-chevron-up" v-else></i>
                                                </v-flex>
                                            </td>
                                        </tr>
                                        <tr class="border-1px-black-6" v-if="showMoreSendOffers">
                                            <td>
                                                <v-flex nowrap>
                                                    <span>New Merchants revenue</span>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex xs12 md2 offset-md2 py-3 nowrap>
                                                    -
                                                </v-flex>
                                            </td>
                                            <td></td>
                                        </tr>
                                        <tr class="border-1px-black-6" v-if="showMoreSendOffers">
                                            <td>
                                                <v-flex nowrap>
                                                    <span>Not interested Offers</span>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    -
                                                </v-flex>
                                            </td>
                                        </tr>
                                        <tr class="border-1px-black-6" v-if="showMoreSendOffers">
                                            <td>
                                                <v-flex nowrap>
                                                    <span>Existing Merchants</span>
                                                    <strong>Recurring revenue</strong>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">2</span>
                                                        <span class="roboto-semi-bold-black-20px-2">/</span>
                                                            <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>

                                                        <span class="roboto-semi-bold-black-20px">€ 2 500</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                            <span>
                                                <span class="roboto-normal-black-14px">3 / € 1 000</span>
                                            </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                        </tr>

                                        <tr class="border-1px-black-6" v-if="showMoreSendOffers">
                                            <td>
                                                <v-flex nowrap>
                                                    <span>Soft Decline</span>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">2</span>
                                                        <span class="roboto-semi-bold-black-20px-2">/</span>
                                                            <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>
                                                        <span class="roboto-semi-bold-black-20px">€ 500</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                        <span>
                                            <span class="roboto-normal-black-14px">1 / € 250</span>
                                        </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">2</span>
                                                        <span class="roboto-semi-bold-black-20px-2">/</span>
                                                            <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>

                                                        <span class="roboto-semi-bold-black-20px">€ 500</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                        <span>
                                            <span class="roboto-normal-black-14px">1 / € 250</span>
                                        </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                        </tr>
                                        <tr class="border-1px-black-6" v-if="showMoreSendOffers">
                                            <td>
                                                <v-flex>
                                                    <span>Change Offer</span>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">4</span>
                                                        <span class="roboto-semi-bold-black-20px-2">/</span>
                                                            <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>

                                                        <span class="roboto-semi-bold-black-20px">€ 3 042</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                        <span>
                                            <span class="roboto-normal-black-14px">4 / € 3 002</span>
                                        </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">2</span>
                                                        <span class="roboto-semi-bold-black-20px-2">/</span>
                                                            <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>

                                                        <span class="roboto-semi-bold-black-20px">€ 2 500</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                        <span>
                                            <span class="roboto-normal-black-14px">3 / € 1 000</span>
                                        </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                        </tr>
                                        <tr class="border-1px-black-6" v-if="showMoreSendOffers">
                                            <td>
                                                <v-flex nowrap>
                                                    <span>Goodwill</span>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">4</span>
                                                        <span class="roboto-semi-bold-black-20px-2">/</span>
                                                            <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>

                                                        <span class="roboto-semi-bold-black-20px">€ 3 042</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                        <span>
                                            <span class="roboto-normal-black-14px">4 / € 3 002</span>
                                        </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">2</span>
                                                        <span class="roboto-semi-bold-black-20px-2">/</span>
                                                            <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>

                                                        <span class="roboto-semi-bold-black-20px">€ 2 500</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                        <span>
                                            <span class="roboto-normal-black-14px">3 / € 1 000</span>
                                        </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                        </tr>
                                        <tr class="border-1px-black-6" v-if="showMoreSendOffers">
                                            <td>
                                                <v-flex nowrap>
                                                    <span>Confirmed</span>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">4</span>
                                                        <span class="roboto-semi-bold-black-20px-2">/</span>
                                                            <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>

                                                        <span class="roboto-semi-bold-black-20px">€ 3 042</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                        <span>
                                            <span class="roboto-normal-black-14px">4 / € 3 002</span>
                                        </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                            <td>
                                                <v-flex nowrap>
                                                    <div>
                                                        <i class="fa fa-long-arrow-down fontawesome5pro-regular-normal-rose-20px"></i>
                                                        <span class="roboto-semi-bold-white-20px">
                                                    <span>
                                                        <span class="roboto-semi-bold-black-20px">2</span>
                                                        <span class="roboto-semi-bold-black-20px-2">/</span>
                                                            <i class="fa fa-long-arrow-up fontawesome5pro-regular-normal-forest-green-20px"></i>
                                                        <span class="roboto-semi-bold-black-20px">€ 2 500</span>
                                                    </span>
                                                </span>
                                                    </div>
                                                    <div class="roboto-normal-black-14px mt-1">
                                        <span>
                                            <span class="roboto-normal-black-14px">3 / € 1 000</span>
                                        </span>
                                                    </div>
                                                </v-flex>
                                            </td>
                                        </tr>
                                    </template>
                                </v-data-table>
                            </v-tab-item>
                        </v-tabs-items>
                    </v-tabs>
                </v-card>

                <user-resources
                    :item="item"
                    ref="resources"
                    v-on:tab:click="(hideTabs) => this.hideSites = !hideTabs"
                ></user-resources>

                <v-card v-if="stage && !hideSites" class="py-3 px-3 oveflow-hidden bottom-card">
                    <div class="v-data-table-header">
                        <div class="th">
                            <div class="span roboto-normal-black-26px">Stage:</div>
                        </div>
                        <div class="th-1">
                            <div class="span-2">{{ stage.name }}</div>
                        </div>
                    </div>

                    <div class="frame-71">
                        <div class="frame-28 justify-space-between">
                            <acquisition
                                v-for="(step, i) in stage.steps"
                                :key="i"
                                :step="step"
                                :class-name="hover === step.id ? 'hover' : ''"
                                v-on:stage-step:click="(item) => { onStepCountClick(item) }"
                            />
                            <acquisition-colored
                                :step="{name: 'Not Interested', count: stage.not_interested_count }"
                                @click.native="onStepCountClick({stage_status: 4})"
                            />
                            <acquisition-colored
                                :step="{name: 'Paid', count: stage.paid_count }"
                                :className="frame1Data.frame322Props.className"
                                @click.native="onStepCountClick({store_type: [3]})"
                            />
                        </div>
                    </div>

                </v-card>

                <leads ref="leads" v-if="showContacts && !hideSites"/>

                <sites-list-anima
                    :initial-loading="false"
                    ref="siteList"
                    :ss-url="`/sites/user/${item.user_id}`"
                    :style="showContacts || hideSites ? `display: none` : ''"
                />
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment'
import {flatten} from '@helpers'
import Acquisition from "./Acquisition";
import AcquisitionColored from "./AcquisitionColored";
import Range from "./Range";
import UserTargets from "./UserTargets";
import VChipvSizeXSmall from "./VChipvSizeXSmall"
import SitesListAnima from "../../../../sites/list-anima";
import Leads from "../../../../merchants/leads";
import axios from "axios";
import UserCounts from "../../counts";
import UserResources from "../../resources";
import '@stylesUsersAnima/styleguide.css';

export default {
    name: "Frame1",
    components: {
        UserResources,
        UserCounts,
        Leads,
        SitesListAnima,
        Acquisition,
        AcquisitionColored,
        Range,
        UserTargets,
        VChipvSizeXSmall
    },
    props: [
        "item",
        "stage",
        "frame1Data",
    ],
    data() {
        return {
            showContacts: false,
            hideSites: false,
            notes: [],
            todos: [],
            sites: [],
            meetings: [],
            calls: [],
            offers: [],
            subscriptions: [],
            headers1: [
                {
                    text: this.frame1Data.span9,
                    class: "span roboto-semi-bold-black-16px-2",
                },
                {
                    text: this.frame1Data.span10,
                    class: "span roboto-semi-bold-black-16px-2",

                },
                {
                    text: this.frame1Data.span11,
                    class: "span roboto-semi-bold-black-16px-2",

                },
                {
                    text: this.frame1Data.span12,
                    class: "span roboto-semi-bold-black-16px-2",
                }
            ],
            items1: [
                {
                    contacts: '',
                    leads: '',
                    prospects: '',
                    merchants: '',
                },
            ],
            search: '',
            showMorePaidOffers: false,
            showMoreSendOffers: false,
            hover: 0,
            targets: {},
            performance: {},
            performance_start_date: moment().subtract(7, 'day').format('YYYY-MM-DD'),
            performance_end_date: moment().format('YYYY-MM-DD'),
        }
    },
    mounted() {
        this.$refs.resources.init()
        this.$refs.siteList.getItems()
        this.getMonthlyTargetsExecution()
        this.getPerformance()
        let date = moment().subtract(1, 'month')
        // console.log(date.relativeTimeThreshold('y', 365))
    },
    watch: {
        performanceStartDate(val) {
            console.log(val)
            this.getPerformance()
        },
    },
    methods: {
        getNotes() {
            this.todos = []
            this.loading = true
            axios.get(`/notes/for-cc-user/${this.item.user_id}`)
                .then(response => {
                    this.notes = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getTodos() {
            this.todos = []
            this.loading = true
            axios.get(`/todos/for-cc-user/${this.item.user_id}`)
                .then(response => {
                    this.todos = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getMeetings() {
            this.meetings = []
            this.loading = true
            axios.get(`/meetings/for-cc-user/${this.item.user_id}`)
                .then(response => {
                    this.meetings = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getCalls() {
            this.calls = []
            this.loading = true
            axios.get(`/calls/for-cc-user/${this.item.user_id}`)
                .then(response => {
                    this.calls = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getMonthlyTargetsExecution() {
            if (!this.item.target_new) {
                return;
            }

            axios.get(`/users/${this.item.user_id}/monthly-targets-execution`)
                .then(response => {
                    this.targets = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                });
        },
        getPerformance() {
            axios.post(`/users/${this.item.user_id}/performance`, {
                start_date: this.performance_start_date,
                end_date: this.performance_end_date,
            })
                .then(response => {
                    this.performance = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                });
        },
        onStepCountClick(item) {
            if (item.id === 7) {
                this.$refs.siteList.filters.stage_step = null;
                this.$refs.siteList.filters.store_type = [1, 2, 3];
                this.$refs.siteList.filters.stage_status = null;
                this.showContacts = true
            } else if (item.id) {
                this.showContacts = false
                this.$refs.siteList.filters.stage_step = item.id;
                this.$refs.siteList.filters.store_type = [1, 2, 3];
                this.$refs.siteList.filters.stage_status = null;
            } else if (item.store_type) {
                this.showContacts = false
                this.$refs.siteList.filters.stage_step = null;
                this.$refs.siteList.filters.store_type = item.store_type;
                this.$refs.siteList.filters.stage_status = null;
            } else if (item.stage_status) {
                this.showContacts = false
                this.$refs.siteList.filters.stage_step = null;
                this.$refs.siteList.filters.store_type = [1, 2, 3];
                this.$refs.siteList.filters.stage_status = item.stage_status;
            }

            this.hover = item.id

            if (item.id !== 7) {
                this.$refs.siteList.pagination.sortBy = 'stage_step_changed_at'
                this.$refs.siteList.pagination.descending = false
            }
        },
    },
};
</script>
