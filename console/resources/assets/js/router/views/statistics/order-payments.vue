<template>
    <layout-default>

        <v-container grid-list-md fluid>
            <v-card>
                <v-card-title>
                    <h1>Order Payments</h1>
                    <v-spacer></v-spacer>
                    <v-text-field
                        append-icon="search"
                        label="Search"
                        single-line
                        v-model="search"
                        clearable
                    ></v-text-field>
                    <v-btn absolute dark bottom right color="green" @click.native="executeJob">Regenerate</v-btn>
                </v-card-title>
            </v-card>

            <v-data-table
                :headers="headers"
                :items="items"
                :search="search"
                :pagination.sync="pagination"
                :loading="loading"
                :custom-filter="customFilter"
                must-sort
                class="elevation-1"
                ref="dataTable"
            >
                <template slot="items" slot-scope="props">
                    <td>{{ props.item.date | formatDate }}</td>
                    <td>{{ props.item.provider }}</td>
                    <td>{{ props.item.count }}</td>
                    <td>{{ props.item.amount | toCurrency('EUR') }}</td>
                    <td>{{ props.item.count_completed }}</td>
                    <td>{{ props.item.amount_completed | toCurrency('EUR') }}</td>
                </template>
                <template slot="footer">
                    <td colspan="100%">
                        Total Count: <strong>{{ totalCount }}</strong>
                        | Total Amount: <strong>{{ totalAmount | toCurrency('EUR') }}</strong>
                        | Completed Count: <strong>{{ completedCount }}</strong>
                        | Completed Amount: <strong>{{ completedAmount | toCurrency('EUR') }}</strong>
                    </td>
                </template>
                <v-alert slot="no-results" :value="true" color="error" icon="warning">
                    Your search for "{{ search }}" found no results.
                </v-alert>
            </v-data-table>
        </v-container>

    </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'

export default {
    components: {
        LayoutDefault,
    },
    data() {
        return {
            search: '',
            totalCount: 0,
            totalAmount: 0,
            completedCount: 0,
            completedAmount: 0,
            items: [],
            loading: true,
            pagination: {
                rowsPerPage: 25,
                descending: true,
            },
            headers: [
                {text: 'Date', value: 'date', sortable: true, align: 'left'},
                {text: 'Provider', value: 'provider', sortable: true, align: 'left'},
                {text: 'Total count', value: 'count', sortable: true, align: 'left'},
                {text: 'Total amount', value: 'amount', sortable: true, align: 'left'},
                {text: 'Completed count', value: 'count_completed', sortable: true, align: 'left'},
                {text: 'Completed amount', value: 'amount_completed', sortable: true, align: 'left'},
            ],
        }
    },
    mounted() {
        this.getItems()
    },
    watch: {
        items(val) {
            this.calculateTotal(val)
        },
        search(val) {
            let items = this.customFilter(this.items, val, this.$refs.dataTable.filter)
            this.calculateTotal(items)
        }
    },
    methods: {
        calculateTotal(items) {
            this.totalCount = 0
            this.totalAmount = 0
            this.completedCount = 0
            this.completedAmount = 0
            items.forEach(item => {
                this.totalCount += item.count
                this.totalAmount += item.amount
                this.completedCount += item.count_completed
                this.completedAmount += item.amount_completed
            })
        },
        getItems() {
            this.loading = true;
            axios.get('/statistics/order-payments')
                .then(response => {
                    this.loading = false;
                    this.items = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        executeJob() {
            this.loading = true;
            axios.post('/statistics/run-command/statistics:orders-payments')
                .then(() => {
                    let success = {
                        data: {
                            message: 'Command statistics:orders-payments started successfully'
                        }
                    }
                    this.handleSuccess(success)
                    this.close()
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        customFilter(items, search, filter) {
            if (!search || search.trim() === '') return items
            search = search.toString().toLowerCase().trim()

            return items.filter(item => filter(item.provider, search))
        },
    },
}
</script>
