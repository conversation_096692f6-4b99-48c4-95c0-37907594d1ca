<template>
  <v-expansion-panel>
    <v-expansion-panel-content>
      <div slot="header"><strong>Filters</strong></div>
      <v-card>
        <v-card-text>
          <v-container grid-list-md>
            <v-layout wrap>
              <v-flex xs12 v-if="users.length">
                <v-autocomplete
                    :items="users"
                    v-model="filters.user"
                    label="Owner"
                    item-text="name"
                    item-value="user_id"
                    prepend-icon="people"
                    small-chips
                    clearable
                    multiple
                >
                  <template v-slot:selection="data">
                    <v-chip
                        :selected="data.selected"
                        small
                        close
                        @input="removeItem(data.item.user_id, 'user')"
                    >
                      <strong>{{ data.item.name }}</strong>
                    </v-chip>
                  </template>
                  <template slot="item" slot-scope="data">
                    <v-list-tile-avatar>
                      <img :src="data.item.google_account.avatar">
                    </v-list-tile-avatar>
                    <v-list-tile-content>
                      <v-list-tile-title v-html="data.item.name"></v-list-tile-title>
                      <v-list-tile-sub-title v-html="data.item.email"></v-list-tile-sub-title>
                    </v-list-tile-content>
                  </template>
                </v-autocomplete>
              </v-flex>
            </v-layout>
          </v-container>
        </v-card-text>
      </v-card>
    </v-expansion-panel-content>
  </v-expansion-panel>
</template>

<script type="text/ecmascript-6">
import moment from 'moment'
import axios from 'axios'
import {flatten} from '@helpers'
import _ from "lodash"

export default {
  name: "filters",
  props: ['ssUrl'],
  data() {
    return {
      filters: {},
      users: [],
      loading: false,
    }
  },
  mounted() {
    if (this.isCloudCartEmployee) {
      this.getUsers()
    }
  },
  watch: {
    filters: {
      handler(newVal, oldVal) {
        this.$emit('filters:change')
      },
      deep: true
    },
  },
  methods: {
    removeItem(item, attribute) {
      this.filters[attribute].splice(this.filters[attribute].indexOf(item), 1)
    },
    getUsers() {
      axios.get('/users/autocomplete')
          .then(response => {
            this.users = response.data.data.map(item => {
              return {...item, flatten: Object.values(flatten(item))}
            })
          })
          .catch(error => {
            this.handleError(error)
          })
    },
    filterItems(items) {
      return items.filter((item) => {
        return this.filter(item)
      })
    },
    filter(item) {
      let startDate = !this.filters.start_date || moment(item.created_at).isSameOrAfter(this.filters.start_date, 'day')
      let endDate = !this.filters.end_date || moment(item.created_at).isSameOrBefore(this.filters.end_date, 'day')
      let status = _.isUndefined(this.filters.status) || (item.status == this.filters.status)
      let issuer = _.isUndefined(this.filters.user) || (item.owner_id == this.filters.user)

      return startDate && endDate && status && issuer
    },
  },
}
</script>
