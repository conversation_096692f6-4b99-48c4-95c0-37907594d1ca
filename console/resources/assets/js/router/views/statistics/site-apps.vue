<template>
  <layout-default>

    <v-container grid-list-md fluid>
      <installed-apps
          ref="list"
          :loading="true"
          :items="[]"
          :site="false"
          ss-url="/statistics/installed-apps"
      ></installed-apps>
    </v-container>

  </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import InstalledApps from "./installed-apps";

export default {
  components: {
    InstalledApps,
    LayoutDefault,
  },
  mounted() {
    this.$refs.list.getItems()
  },
}
</script>
