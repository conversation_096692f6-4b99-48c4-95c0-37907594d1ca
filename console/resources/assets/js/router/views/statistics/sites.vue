<template>
  <layout-default>

    <v-container grid-list-md fluid>

      <v-card>
        <v-card-title>
          <h1>Site Statistics</h1>
          <v-spacer></v-spacer>
          <v-text-field
              append-icon="search"
              label="Search"
              single-line
              v-model="search"
              clearable
          ></v-text-field>
        </v-card-title>

        <filters
            v-if="isCloudCartEmployee"
            ref="filters"
            v-on:filters:change="getItems"
        ></filters>

      </v-card>

      <v-data-table
          :headers="headers"
          :items="items"
          :pagination.sync="pagination"
          :total-items="totalItems"
          :loading="loading"
          must-sort
          class="elevation-1"
      >
        <template slot="items" slot-scope="props">
          <td>
            <a @click="$refs.siteDetails.show({id: props.item.site_id})">
              {{ props.item.site_id }}<br>
              {{ props.item.primary_host }}<br>
            </a>
            <span v-if="props.item.cc_user_name">AM: {{ props.item.cc_user_name }}</span>
          </td>
          <td>{{ props.item.products }}</td>
          <td>{{ props.item.customers }}</td>
          <td>{{ props.item.orders }}</td>
          <td>{{ props.item.revenue_st | toCurrency('EUR') }}</td>
          <td>{{ props.item.revenue_lt | toCurrency('EUR') }}</td>
          <td>{{ props.item.revenue_6m | toCurrency('EUR') }}</td>
          <td>{{ props.item.revenue_1y | toCurrency('EUR') }}</td>
          <td>
            CC: {{ props.item.cc_pageviews_st }}<br>
            GA: {{ props.item.ga_pageviews_st }}
          </td>
          <td>
            CC: {{ props.item.cc_pageviews_lt }}<br>
            GA: {{ props.item.ga_pageviews_lt }}
          </td>
          <td>{{ props.item.sf_lang }}</td>
          <td>{{ props.item.cp_lang }}</td>
          <td>{{ props.item.store_country }}</td>
          <td>{{ props.item.store_currency }}</td>
          <td>{{ props.item.admin_last_login | formatDate }}</td>
          <td>{{ props.item.plan }}</td>
          <td>{{ props.item.billing_cycle }}</td>
          <td>{{ props.item.next_billing_date | formatDate }}</td>
          <td>{{ props.item.total_spent | toCurrency('EUR') }}</td>
          <td>{{ props.item.store_lifetime }}</td>
          <td>{{ props.item.last_order_date | formatDate }}</td>
          <td>{{ props.item.industry_score }}</td>
          <td>{{ props.item.sssi }}</td>
          <td>{{ props.item.lssi }}</td>
          <td>{{ props.item.sspi }}</td>
          <td>{{ props.item.lspi }}</td>
          <td>{{ props.item.sri }}</td>
        </template>
        <v-alert slot="no-results" :value="true" color="error" icon="warning">
          Your search for "{{ search }}" found no results.
        </v-alert>
      </v-data-table>

      <site-show-dialog ref="siteDetails"></site-show-dialog>
    </v-container>
  </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import Filters from "./sites/filters.vue"
import SiteShowDialog from "../sites/show-dialog.vue"
import _ from "lodash"


const CancelToken = axios.CancelToken;
let cancel;

export default {
  components: {
    SiteShowDialog,
    Filters,
    LayoutDefault,
  },
  data() {
    return {
      loading: false,
      search: '',
      totalItems: 0,
      items: [],
      pagination: {
        descending: false,
        page: 1,
        rowsPerPage: 25,
        sortBy: "site_id",
        totalItems: 0,
      },
    }
  },
  computed: {
    headers() {
      let headers = [
        {text: 'Site', value: 'site_id'},
        {text: 'Products', value: 'products'},
        {text: 'Customers', value: 'customers'},
        {text: 'Orders', value: 'orders'},
        {text: 'Revenue ST', value: 'revenue_st'},
        {text: 'Revenue LT', value: 'revenue_lt'},
        {text: 'Revenue 6M', value: 'revenue_6m'},
        {text: 'Revenue 1Y', value: 'revenue_1y'},
        {text: 'Pageviews ST', value: 'cc_pageviews_st'},
        {text: 'Pageviews LT', value: 'cc_pageviews_lt'},
        {text: 'SF Lang', value: 'sf_lang'},
        {text: 'CP Lang', value: 'cp_lang'},
        {text: 'Country', value: 'store_country'},
        {text: 'Currency', value: 'store_currency'},
        {text: 'Last Login', value: 'admin_last_login'},
        {text: 'Plan', value: 'plan'},
        {text: 'Billing Cycle', value: 'billing_cycle'},
        {text: 'Billing Date', value: 'next_billing_date'},
        {text: 'Total Spent', value: 'total_spent'},
        {text: 'Lifetime', value: 'store_lifetime'},
        {text: 'Last Order', value: 'last_order_date'},
        {text: 'Industry Score', value: 'industry_score'},
        {text: 'SSSI', value: 'sssi'},
        {text: 'LSSI', value: 'lssi'},
        {text: 'SSPI', value: 'sspi'},
        {text: 'LSPI', value: 'lspi'},
        {text: 'SRI', value: 'sri'},
      ]

      if (this.site) {
        delete headers[1]
      }

      return headers
    },
  },
  mounted() {
    this.getItems()
  },
  watch: {
    pagination: {
      handler(newVal, oldVal) {
        delete newVal.totalItems
        delete oldVal.totalItems
        if (!_.isEqual(newVal, oldVal)) {
          this.getItems()
        }
      },
      deep: false
    },
  },
  methods: {
    getItems() {
      this.loading = true
      const params = {
        cancelToken: new CancelToken(function executor(c) {
          cancel = c;
        }),
        params: {
          search: this.search,
          filters: this.$refs.filters ? this.$refs.filters.filters : {},
          ...this.pagination,
        }
      }
      axios.get('/statistics/sites', params)
          .then(response => {
            this.loading = false
            this.items = response.data.data
            this.totalItems = response.data.meta.total
          })
          .catch(error => {
            this.handleError(error)
          })
    },
  },
}
</script>
