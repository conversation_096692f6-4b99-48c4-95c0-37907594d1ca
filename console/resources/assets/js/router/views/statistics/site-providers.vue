<template>
    <layout-default>

        <v-container grid-list-md fluid>
            <installed-providers :loading="loading" :items="items" :site="false"></installed-providers>
        </v-container>

    </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import InstalledProviders from "./installed-providers";

export default {
    components: {
        InstalledProviders,
        LayoutDefault,
    },
    data() {
        return {
            items: [],
            loading: true,
        }
    },
    mounted() {
        this.getItems()
    },
    methods: {
        getItems() {
            this.loading = true;
            axios.get('/statistics/installed-providers')
                .then(response => {
                    this.loading = false;
                    this.items = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    },
}
</script>
