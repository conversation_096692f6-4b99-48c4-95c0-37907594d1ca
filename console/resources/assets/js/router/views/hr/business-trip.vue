<template>
    <layout-default>

        <v-container grid-list-md fluid>
            <calendar-list-trips
                :items="items"
                :users="users"
                ref="list"
            ></calendar-list-trips>
        </v-container>

    </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import {flatten} from '@helpers'
import CalendarListTrips from "./calendar/list-trips.vue";

export default {
    components: {
        CalendarListTrips,
        LayoutDefault,
    },
    data() {
        return {
            items: [],
            users: [],
        }
    },
    created() {
        this.getItems()
        this.getUsers()
    },
    methods: {
        getItems() {
            // this.$refs.list.loading = true
            axios.get('/business-trip')
                .then(response => {
                    this.$refs.list.loading = false
                    this.items = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getUsers() {
            // this.$refs.list.loading = true
            axios.get('/employees/autocomplete')
                .then(response => {
                    this.$refs.list.loading = false
                    this.users = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    },
}
</script>