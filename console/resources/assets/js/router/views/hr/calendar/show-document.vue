<template>
    <v-dialog
        v-model="dialog"
        fullscreen
        hide-overlay
        transition="dialog-bottom-transition"
        scrollable
        @keydown.esc="closeDialogOnEsc"
    >
        <v-card tile>
            <v-toolbar dense dark :color="$root.dark ? 'grey darken-4' : 'primary'">
                <v-menu bottom right offset-y>
                    <v-btn slot="activator" dark icon>
                        <v-icon>more_vert</v-icon>
                    </v-btn>
                    <v-list>
                        <v-list-tile @click="print">
                            <v-list-tile-content>
                                <v-list-tile-title>Print</v-list-tile-title>
                            </v-list-tile-content>
                            <v-list-tile-action>
                                <v-icon>print</v-icon>
                            </v-list-tile-action>
                        </v-list-tile>
                    </v-list>
                </v-menu>
                <v-toolbar-title>{{ title }}</v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon dark @click.native="dialog = false">
                    <v-icon>close</v-icon>
                </v-btn>
            </v-toolbar>

            <v-card-text class="transparent" style="height: 1000px">
                <v-flex xs12 sm6 offset-sm3>
                    <v-card>
                        <div v-html="item.html" style="padding: 15px" class="white elevation-8"></div>
                    </v-card>
                </v-flex>
            </v-card-text>

            <div style="flex: 1 1 auto;" class="transparent"></div>
            <iframe
                id="print-body"
                :srcdoc="item.html"
                style="display: none"
            ></iframe>
        </v-card>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import NProgress from 'nprogress/nprogress'

export default {
    name: "calendar-show-document",
    components: {},
    data() {
        return {
            dialog: false,
            loading: false,
            item: {},
        }
    },
    computed: {
        title() {
            return this.item.event_type_name + ' Request'
        },
    },
    methods: {
        json(value) {
            return JSON.stringify(value, null, 2)
        },
        print() {
            let iFrame = document.getElementById('print-body');
            iFrame.contentWindow.print()
        },
        download(id) {
            axios.post('/employee-calendar/' + id + '/download')
                .then(response => {
                    window.location = response.data.redirect
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        show(item) {
            this.item = item
            this.dialog = true
            NProgress.start()
            axios.get('/employee-calendar/' + item.id)
                .then((response) => {
                    NProgress.done()
                    this.item = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        refresh() {
            NProgress.start()
            axios.get('/invoices/' + this.item.id)
                .then((response) => {
                    NProgress.done()
                    this.item = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
    }
}
</script>