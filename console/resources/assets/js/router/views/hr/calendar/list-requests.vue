<template>
    <v-card>
        <v-card>
            <v-card-title>
                <v-avatar v-if="icon">
                    <v-icon size="40">{{ icon }}</v-icon>
                </v-avatar>
                <h1>{{ title }}</h1>
                <v-btn
                    small
                    dark
                    fab
                    color="pink"
                    @click="newRequest"
                >
                    <v-icon>add</v-icon>
                </v-btn>
                <v-spacer></v-spacer>
                <v-text-field
                    append-icon="search"
                    label="Search"
                    single-line
                    v-model="search"
                    clearable
                ></v-text-field>
            </v-card-title>

            <calendar-filter-requests
                ref="filters"
                :filters="filters"
                :users="users"
            ></calendar-filter-requests>
        </v-card>

        <v-data-table
            :headers="headers"
            :items="filteredItems"
            :search="search"
            :pagination.sync="pagination"
            :total-items="totalItems"
            :loading="loading"
            :custom-filter="customFilter"
            must-sort
            class="elevation-1"
        >
            <template slot="items" slot-scope="props">
                <tr>
                    <td>
                        {{ props.item.created_at | formatDateTime }} / {{ props.item.id }}<br>
                        <cc-user
                            :item="props.item.creator"
                            :small="true"
                        ></cc-user>
                    </td>
                    <td>{{ props.item.days_before_the_event }}</td>
                    <td>{{ props.item.from_date | formatDate }}</td>
                    <td>{{ props.item.to_date | formatDate }}</td>
                    <td>{{ props.item.days }}</td>
                    <td>
                        <cc-user
                            :item="props.item.user"
                        ></cc-user>
                        <span v-if="props.item.substitute">Substitute: </span>
                        <cc-user
                            v-if="props.item.substitute"
                            :item="props.item.substitute"
                            :small="true"
                        ></cc-user>
                    </td>
                    <td v-if="eventType === 2">
                        {{ props.item.vacation_type === 1 ? 'Paid' : 'Unpaid' }}
                    </td>
                    <td>
                        <v-tooltip v-if="props.item.status == 4" bottom>
                            <v-icon slot="activator" color="primary">edit_document</v-icon>
                            <span>Draft</span>
                        </v-tooltip>
                        <v-tooltip v-if="props.item.status == 3" bottom>
                            <v-icon slot="activator" color="warning">pending</v-icon>
                            <span>Pending</span>
                        </v-tooltip>
                        <v-tooltip v-if="props.item.status == 1" bottom>
                            <v-icon slot="activator" color="success">check_circle</v-icon>
                            <span>Approved</span>
                        </v-tooltip>
                        <v-tooltip v-if="props.item.status == 2" bottom>
                            <v-icon slot="activator" color="error">block</v-icon>
                            <span>Declined</span>
                        </v-tooltip>
                        <v-tooltip v-if="props.item.status == 5" bottom>
                            <v-icon slot="activator" color="error">cancel</v-icon>
                            <span>Canceled</span>
                        </v-tooltip>
                    </td>
                    <td class="px-0" nowrap>
                        <v-tooltip bottom v-if="props.item.event_type == 3 && !props.item.file_id">
                            <v-btn
                                slot="activator"
                                class="mx-0"
                                icon
                                @click="$refs.upload.show(props.item)"
                            >
                                <v-icon color="pink">upload_file</v-icon>
                            </v-btn>
                            <span>Upload Document</span>
                        </v-tooltip>
                        <v-tooltip
                            v-if="(props.item.event_type == 3 && props.item.file_id) || props.item.event_type != 3"
                            bottom
                        >
                            <v-btn
                                v-if="props.item.event_type == 3 && props.item.document"
                                slot="activator"
                                icon
                                class="mx-0"
                                :href="props.item.document.url"
                                target="_blank"
                            >
                                <v-icon color="teal">preview</v-icon>
                            </v-btn>
                            <v-btn
                                v-else
                                slot="activator"
                                class="mx-0"
                                icon
                                @click="$refs.showDocument.show(props.item)"
                            >
                                <v-icon color="teal">preview</v-icon>
                            </v-btn>
                            <span>View</span>
                        </v-tooltip>
                        <v-tooltip
                            v-if="props.item.status == 4 &&
                            (
                                (props.item.event_type == 3 && props.item.file_id)
                                || props.item.event_type != 3
                            )"
                            bottom
                        >
                            <v-btn
                                slot="activator"
                                class="mx-0"
                                icon
                                @click="sendForApproval(props.item)"
                            >
                                <v-img
                                    max-width="24px"
                                    src="/images/slack_logo.png"
                                ></v-img>
                            </v-btn>
                            <span>Send for approval</span>
                        </v-tooltip>
                        <v-tooltip bottom v-if="props.item.status == 3">
                            <v-btn
                                slot="activator"
                                class="mx-0"
                                icon
                                @click="approve(props.item)"
                            >
                                <v-icon color="success">check_circle</v-icon>
                            </v-btn>
                            <span>Approve</span>
                        </v-tooltip>
                        <v-tooltip bottom v-if="props.item.status == 3">
                            <v-btn
                                slot="activator"
                                class="mx-0"
                                icon
                                @click="decline(props.item)"
                            >
                                <v-icon color="error">block</v-icon>
                            </v-btn>
                            <span>Decline</span>
                        </v-tooltip>
                        <v-tooltip
                            v-if="props.item.status == 3 || props.item.status == 4"
                            bottom
                        >
                            <v-btn
                                slot="activator"
                                class="mx-0"
                                icon
                                @click="cancel(props.item)"
                            >
                                <v-icon color="error">cancel</v-icon>
                            </v-btn>
                            <span>Cancel</span>
                        </v-tooltip>
                        <v-tooltip
                            v-if="props.item.status == 5 || isSuperUser"
                            bottom
                        >
                            <v-btn
                                slot="activator"
                                class="mx-0"
                                icon
                                @click="$refs.deleteDialog.deleteItem(props.item)"
                            >
                                <v-icon color="error">delete</v-icon>
                            </v-btn>
                            <span>Delete</span>
                        </v-tooltip>
                    </td>
                </tr>
            </template>

            <v-alert slot="no-results" :value="true" color="error" icon="warning">
                Your search for "{{ search }}" found no results.
            </v-alert>

        </v-data-table>

        <calendar-new-event
            :items="items"
            :users="users"
            ref="newEvent"
        />
        <calendar-show-document
            ref="showDocument"
        ></calendar-show-document>

        <calendar-upload-document
            :items="items"
            ref="upload"
        ></calendar-upload-document>

        <delete
            :items="items"
            url="/employee-calendar/"
            ref="deleteDialog"
        />
    </v-card>
</template>

<script>
import CalendarShowDocument from "./show-document";
import CalendarNewEvent from "./new-event";
import CcUser from "../../../../components/CcUser";
import axios from "axios";
import Delete from '@components/Delete'
import CalendarUploadDocument from "./upload-document";
import CalendarFilterRequests from "./filter-requests";

export default {
    name: "calendar-list-requests",
    props: ['items', 'users', 'eventType', 'ssUrl'],
    components: {
        CalendarFilterRequests,
        CalendarUploadDocument,
        CalendarShowDocument,
        CalendarNewEvent,
        CcUser,
        Delete,
    },
    data() {
        return {
            search: '',
            totalItems: 0,
            filters: {},
            filteredItems: [],
            loading: true,
            superUsers: [],
            pagination: {
                rowsPerPage: 25,
                descending: true,
                // sortBy: "status",
            },
            eventTypes: [
                {
                    name: 'Home Office',
                    value: 1,
                },
                {
                    name: 'Vacation',
                    value: 2,
                },
                {
                    name: 'Sick Leave',
                    value: 3,
                },
            ],
        }
    },
    mounted() {
        if (this.$route.query.filters) {
            this.filters = JSON.parse(this.$route.query.filters)
        }
        this.getSuperUsers()
    },
    watch: {
        items(val) {
            if (!this.ssUrl) {
                this.filteredItems = this.$refs.filters.filterItems(val)
            }
        },
        filters: {
            handler() {
                this.filteredItems = this.$refs.filters.filterItems(this.items)
            },
            deep: true
        },
    },
    computed: {
        headers() {
            let headers = [
                {text: 'Created At/By / ID', value: 'created_at'},
                {text: 'DBE', value: 'days_before_the_event'},
                {text: 'From Date', value: 'from_date'},
                {text: 'To Date', value: 'from_date'},
                {text: 'Days', value: 'days'},
                {text: 'Employee', value: 'user.name'},
                {text: 'Type', value: 'vacation_type'},
                {text: 'Status', value: 'status'},
                {text: 'Actions', sortable: false}
            ];

            if (this.eventType !== 2) {
                headers.splice(6, 1)
            }

            return headers;
        },
        title() {
            if (!this.eventType) {
                return 'Employees Events'
            }

            return this.eventTypes.find(({value}) => value === this.eventType).name
        },
        icon() {
            switch (this.eventType) {
                case 1:
                    return 'home_work'
                case 2:
                    return 'beach_access'
                case 3:
                    return 'sick'
                default:
                    return null
            }
        },
        isSuperUser() {
            const user = this.$store.getters['auth/user']
            return this.superUsers.includes(user.user_id)
        },
    },
    methods: {
        newRequest() {
            this.$refs.newEvent.type = this.eventType
            this.$refs.newEvent.showType = !this.eventType
            this.$refs.newEvent.show()
        },
        sendForApproval(item) {
            this.loading = true;
            let editedIndex = this.items.indexOf(item)
            axios.post(`/employee-calendar/${item.id}/send-for-approval`)
                .then(response => {
                    this.loading = false;
                    Object.assign(this.items[editedIndex], response.data.data)
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        approve(item) {
            this.loading = true;
            let editedIndex = this.items.indexOf(item)
            axios.post(`/employee-calendar/${item.id}/approve`)
                .then(response => {
                    this.loading = false;
                    Object.assign(this.items[editedIndex], response.data.data)
                    this.$root.success = {
                        snackbar: true,
                        msg: 'You have successfully approved the request',
                    }
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        decline(item) {
            this.loading = true;
            let editedIndex = this.items.indexOf(item)
            axios.post(`/employee-calendar/${item.id}/decline`)
                .then(response => {
                    this.loading = false;
                    Object.assign(this.items[editedIndex], response.data.data)
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        cancel(item) {
            this.loading = true;
            let editedIndex = this.items.indexOf(item)
            axios.post(`/employee-calendar/${item.id}/cancel`)
                .then(response => {
                    this.loading = false;
                    Object.assign(this.items[editedIndex], response.data.data)
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        getSuperUsers() {
            axios.get(`/employee-calendar/super-users`)
                .then(response => {
                    this.superUsers = response.data
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        customFilter(items, search, filter) {
            search = search.toString().toLowerCase()
            if (search.trim() === '') return items

            return items.filter(item => item.flatten.some(val => filter(val, search)))
        },
    },
}
</script>
