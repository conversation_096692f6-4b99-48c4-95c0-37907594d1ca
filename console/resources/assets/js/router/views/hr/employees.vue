<template>
    <layout-default>

        <v-container grid-list-md fluid>
            <v-card>
                <v-card-title>
                    <v-avatar>
                        <v-icon size="40">badge</v-icon>
                    </v-avatar>
                    <h1>Employees</h1>
                    <v-btn
                        small
                        dark
                        fab
                        color="pink"
                        @click="$refs.calendar.show()"
                    >
                        <v-icon>event</v-icon>
                    </v-btn>
                    <v-spacer></v-spacer>
                    <v-text-field
                        append-icon="search"
                        label="Search"
                        single-line
                        v-model="search"
                        clearable
                    ></v-text-field>
                </v-card-title>
            </v-card>

            <v-data-table
                :headers="headers"
                :items="items"
                :search="search"
                :pagination.sync="pagination"
                :total-items="totalItems"
                :loading="loading"
                :custom-filter="customFilter"
                must-sort
                class="elevation-1"
            >
                <template slot="items" slot-scope="props">
                    <tr>
                        <td>
                            <v-chip
                                v-if="props.item.today_event"
                                :color="getEventColor(props.item.today_event)"
                                text-color="white"
                            >
                                <v-avatar>
                                    <v-icon>{{ getEventIcon(props.item.today_event) }}</v-icon>
                                </v-avatar>
                                {{ props.item.today_event.event_type_name }}
                            </v-chip>
                        </td>
                        <td>
                            <cc-user
                                :item="props.item"
                                :sub-title2="'Slack: @' + props.item.slack_username"
                            ></cc-user>
                        </td>
                        <td>{{ props.item.position }}</td>
                        <td>{{ props.item.department ? props.item.department.name : '' }}</td>
                        <td nowrap>
                            <span class="caption">
                                Vacation: <b>{{ props.item.vacation_days_left }}</b> / {{ props.item.vacation_days }}
                            </span>
                            <br>
                            <span class="caption">
                                Home Office: <b>{{
                                    props.item.home_office_days_left
                                }}</b> / {{ props.item.home_office_days }}
                            </span>
                            <br>
                        </td>
                        <td v-if="props.item.manager">
                            <cc-user
                                :item="props.item.manager"
                            ></cc-user>
                        </td>
                        <td v-else></td>
                        <td class="text-xs-center px-0" nowrap>
                            <v-tooltip bottom>
                                <v-btn
                                    slot="activator"
                                    class="mx-0"
                                    icon
                                    @click="$refs.editDialog.edit(props.item)"
                                >
                                    <v-icon color="teal">edit</v-icon>
                                </v-btn>
                                <span>Edit</span>
                            </v-tooltip>
                            <v-tooltip bottom>
                                <v-btn
                                    slot="activator"
                                    class="mx-0"
                                    icon
                                    @click="$refs.addresses.show(props.item)"
                                >
                                    <v-icon color="success">home</v-icon>
                                </v-btn>
                                <span>Addresses</span>
                            </v-tooltip>
                        </td>
                    </tr>
                </template>

                <v-alert slot="no-results" :value="true" color="error" icon="warning">
                    Your search for "{{ search }}" found no results.
                </v-alert>

            </v-data-table>

            <employee-edit :items="items" ref="editDialog"/>
            <employee-addresses ref="addresses"></employee-addresses>
            <calendar-dialog ref="calendar"></calendar-dialog>
        </v-container>

    </layout-default>
</template>

<script type="text/ecmascript-6">
import LayoutDefault from '@layouts/Default'
import axios from 'axios'
import {flatten} from '@helpers'
import CcUser from "../../../components/CcUser";
import EmployeeEdit from "./employees/edit";
import EmployeeAddresses from "./employees/addresses";
import CalendarDialog from "./calendar/show-calendar";

export default {
    components: {
        CalendarDialog,
        EmployeeAddresses,
        EmployeeEdit,
        CcUser,
        LayoutDefault,
    },
    data() {
        return {
            search: '',
            totalItems: 0,
            items: [],
            loading: true,
            pagination: {
                rowsPerPage: 25,
                descending: true,
            },
            headers: [
                {text: 'Today Event', value: 'today_event'},
                {text: 'Employee', value: 'name'},
                {text: 'Position', value: 'position'},
                {text: 'Department', value: 'department.name'},
                {text: 'Days', value: 'vacation_days'},
                {text: 'Manager', value: 'manager.name'},
                {text: 'Actions', sortable: false, align: 'center'}
            ],
        }
    },
    created() {
        this.getItems()
    },
    methods: {
        getItems() {
            this.loading = true
            axios.get('/employees')
                .then(response => {
                    this.loading = false
                    this.items = response.data.data.map(item => {
                        return {...item, flatten: Object.values(flatten(item))}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                })
        },
        customFilter(items, search, filter) {
            search = search.toString().toLowerCase()
            if (search.trim() === '') return items

            return items.filter(item => item.flatten.some(val => filter(val, search)))
        },
        getEventIcon(item) {
            switch (item.event_type) {
                case 1:
                    return 'home_work'
                case 2:
                    return 'beach_access'
                case 3:
                    return 'sick'
                case 4:
                    return 'luggage'
                default:
                    return null
            }
        },
        getEventColor(item) {
            switch (item.event_type) {
                case 1:
                    return 'success'
                case 2:
                    return 'warning'
                case 3:
                    return 'error'
                default:
                    return null
            }
        },
    },
}
</script>