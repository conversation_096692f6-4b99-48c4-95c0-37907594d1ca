<template>
    <v-dialog
            v-model="dialog"
            max-width="600px"
            scrollable
            persistent
    >
        <v-form ref="form" v-model="valid" lazy-validation>
            <v-card>
                <v-toolbar dense dark :color="$root.dark ? 'grey darken-4' : 'primary'">
                    <v-toolbar-title>
                        Follow Up Call
                    </v-toolbar-title>
                </v-toolbar>

                <v-card-text>
                    <v-container grid-list-md pa-2>
                        <v-layout wrap>
                            <v-flex xs8>
                                <v-menu
                                        ref="billingDate"
                                        lazy
                                        :close-on-content-click="false"
                                        v-model="selectDate"
                                        transition="scale-transition"
                                        offset-y
                                        full-width
                                        :nudge-right="40"
                                        min-width="290px"
                                        :return-value.sync="date"
                                >
                                    <v-text-field
                                            slot="activator"
                                            label="Schedule Date"
                                            v-model="date"
                                            prepend-icon="event"
                                            readonly
                                            :error-messages="errors.date"
                                    ></v-text-field>
                                    <v-date-picker v-model="date"
                                                   :allowed-dates="futureDates"
                                                   @input="$refs.billingDate.save(date)">
                                    </v-date-picker>
                                </v-menu>
                            </v-flex>
                            <v-flex xs4>
                                <v-menu
                                        ref="menu"
                                        v-model="selectTime"
                                        :close-on-content-click="false"
                                        :nudge-right="40"
                                        :return-value.sync="time"
                                        lazy
                                        transition="scale-transition"
                                        offset-y
                                        full-width
                                        max-width="290px"
                                        min-width="290px"
                                >
                                    <template v-slot:activator="{ on }">
                                        <v-text-field
                                                v-model="time"
                                                label="Schedule Time"
                                                prepend-icon="access_time"
                                                readonly
                                                v-on="on"
                                        ></v-text-field>
                                    </template>
                                    <v-time-picker
                                            v-if="selectTime"
                                            v-model="time"
                                            full-width
                                            format="24hr"
                                            @click:minute="$refs.menu.save(time)"
                                    ></v-time-picker>
                                </v-menu>
                            </v-flex>
                            <v-flex xs12 v-if="modelType === 'cc-user' && !userId">
                                <v-autocomplete
                                        :items="sites"
                                        v-model="siteId"
                                        label="Site"
                                        item-text="primary_host"
                                        item-value="id"
                                        clearable
                                        prepend-icon="store"
                                        :error-messages="errors.site_id"
                                        @change="userId = null"
                                ></v-autocomplete>
                            </v-flex>
                            <v-flex xs12 v-if="modelType === 'cc-user' && !siteId">
                                <v-autocomplete
                                        :items="users"
                                        v-model="userId"
                                        label="Merchant"
                                        item-text="text"
                                        item-value="id"
                                        clearable
                                        prepend-icon="people"
                                        :error-messages="errors.user_id"
                                        @change="siteId = null"
                                ></v-autocomplete>
                            </v-flex>
                            <v-flex xs12>
                                <v-textarea
                                        outline
                                        v-model="note"
                                        label="Note"
                                        :error-messages="errors.note"
                                        ref="noteTextArea"
                                        height="350"
                                ></v-textarea>
                            </v-flex>
                        </v-layout>
                    </v-container>
                </v-card-text>

                <v-divider></v-divider>

                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" flat @click.native="close">Cancel</v-btn>
                    <v-btn
                            color="primary"
                            :loading="loading"
                            :disabled="loading"
                            flat
                            @click.native="save"
                    >Save
                    </v-btn>
                </v-card-actions>
            </v-card>
        </v-form>

    </v-dialog>
</template>

<script type="text/ecmascript-6">
import axios from 'axios'
import moment from 'moment'
import {flatten} from '@helpers'

export default {
    name: "call-followup",
    components: {},
    data() {
        return {
            valid: true,
            loading: false,
            dialog: false,
            selectDate: false,
            selectTime: false,
            modelType: null,
            offerStatus: null,
            note: null,
            date: null,
            time: null,
            item: {},
            siteId: null,
            userId: null,
            users: [],
            sites: [],
            errors: [],
        }
    },
    mounted() {
    },
    computed: {},
    watch: {},
    methods: {
        futureDates(date) {
            return moment(date).isSameOrAfter(moment().format('YYYY-MM-DD'))
        },
        show(item, modelType = 'call', offerStatus = null) {
            this.offerStatus = offerStatus
            this.dialog = true
            this.valid = true
            this.errors = []
            this.modelType = modelType
            this.date = moment().format('YYYY-MM-DD')
            this.time = moment().format('HH:MM')
            this.item = this.makeNonReactiveCopy(item)
            if (modelType === 'cc-user') {
                this.getSites()
                this.getMerchants()
            }
            setTimeout(() => {
                this.$refs.noteTextArea.focus()
            }, 100);
        },
        getSites() {
            if (this.sites.length) {
                return
            }

            axios.get(`/sites/user/${this.item.user_id}`)
                .then(response => {
                    this.sites = response.data.data
                })
                .catch(error => {
                    this.handleError(error)
                });
        },
        getMerchants() {
            if (this.users.length) {
                return
            }

            axios.get(`/merchants/autocomplete`)
                .then(response => {
                    this.users = response.data.data.map(item => {
                        return {...item, text: [item.name, item.email, item.phone].join(' | ')}
                    })
                })
                .catch(error => {
                    this.handleError(error)
                });
        },
        close() {
            this.dialog = false
        },
        createRequest() {
            let request = {
                date: this.date,
                time: this.time,
                note: this.note,
            }

            switch (this.modelType) {
                case 'call':
                    request.call_id = this.item.id
                    break;
                case 'offer':
                    request.offer_id = this.item.id
                    request.offer_status = this.offerStatus
                    break;
                case 'site':
                    request.site_id = this.item.id
                    break;
                case 'user':
                    request.user_id = this.item.id
                    break;
                case 'cc-user':
                    request.cc_user_id = this.item.user_id
                    request.user_id = this.userId
                    request.site_id = this.siteId
                    break;
            }

            return request
        },
        save() {
            if (this.$refs.form.validate()) {
                this.loading = true
                const request = this.createRequest()
                axios.post('/todos/follow-up-call', request)
                    .then((response) => {
                        this.close()
                        let success = {
                            data: {
                                message: 'Call Follow Up Todo created successfully'
                            }
                        }
                        this.handleSuccess(success)
                        let item = {...response.data.data, flatten: Object.values(flatten(response.data.data))}
                        this.$emit('call:follow-up', item)
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        },
    }
}
</script>