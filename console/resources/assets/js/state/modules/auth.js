import axios from 'axios'
import jwt_decode from 'jwt-decode'
import createAuthRefreshInterceptor from 'axios-auth-refresh';

// Function that will be called to refresh authorization
const refreshAuthLogic = failedRequest => axios.post('/auth/refresh-token', {}, {skipAuthRefresh: true})
    .then(response => {
        let authorization = response.headers.authorization
        let currentUser = getSavedState('auth.currentUser')
        saveState('auth.currentUser', {...currentUser, ...{token: authorization}})
        axios.defaults.headers.common.Authorization = authorization
        failedRequest.response.config.headers['Authorization'] = authorization
        return Promise.resolve();
    })
    .catch(error => {
        saveState('auth.currentUser', null)
        console.log(error)
    });

// Instantiate the interceptor
createAuthRefreshInterceptor(axios, refreshAuthLogic);

export const state = {
    currentUser: getSavedState('auth.currentUser'),
}

export const mutations = {
    SET_CURRENT_USER(state, newValue) {
        state.currentUser = newValue
        saveState('auth.currentUser', newValue)
        setDefaultAuthHeaders(state)
    },
}

export const getters = {
    user(state) {
        return state.currentUser
    },
}

export const actions = {
    // This is automatically run in `src/state/store.js` when the app
    // starts, along with any other actions named `init` in other modules.
    init({state, dispatch}) {
        setDefaultAuthHeaders(state)
        // dispatch('validate')
    },

    // Logs in the current user.
    logIn({commit, dispatch, getters}, {hash} = {}) {
        if (getters.user) return //dispatch('validate')

        return axios.post('/auth/obtain-token', {hash})
            .then(response => {
                const user = response.data
                commit('SET_CURRENT_USER', user)
                return user
            })
            .catch(error => {
                if (error.response.status === 401) {
                    commit('SET_CURRENT_USER', null)
                }
                return null
            })
    },

    // Logs out the current user.
    logOut({commit}) {
        axios.post('/auth/logout')
        commit('SET_CURRENT_USER', null)
    },

    // Validates the current user's token and refreshes it
    // with new data from the API.
    validate({state, dispatch}) {
        if (!state.currentUser) return null

        if (state.currentUser.token) {
            const secondsBeforeExpire = 30
            const decoded = jwt_decode(state.currentUser.token)
            const exp = decoded.exp
            if (exp - (Date.now() / 1000) < secondsBeforeExpire) {
                return dispatch('refreshToken')
            }

            return state.currentUser
        }
    },

    refreshToken({state, commit}) {
        return axios
            .post('/auth/refresh-token')
            .then(response => {
                commit('SET_CURRENT_USER', {...state.currentUser, ...{token: response.headers.authorization}})
                return state.currentUser
            })
            .catch(error => {
                // if (error.response.status === 401) {
                commit('SET_CURRENT_USER', null)
                // }
                return null
            })
    },
}

// ===
// Private helpers
// ===

function getSavedState(key) {
    const value = window.localStorage.getItem(key)

    if (!value) {
        return null
    }

    return JSON.parse(window.localStorage.getItem(key))
}

function saveState(key, state) {
    window.localStorage.setItem(key, JSON.stringify(state))
}

function setDefaultAuthHeaders(state) {
    axios.defaults.headers.common.Authorization = state.currentUser
        ? state.currentUser.token
        : ''
}
