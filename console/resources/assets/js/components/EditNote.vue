<template>
    <v-edit-dialog
        :return-value.sync="note"
        large
        lazy
        persistent
        @save="editNote(item)"
        @open="showEditNote(item)"
    >
        <div>
            <v-btn
                v-if="add"
                small
                color="primary"
            >
                Note
                <v-icon right>note_add</v-icon>
            </v-btn>
            <span v-else>{{ item.note }}</span>
        </div>
        <template v-slot:input>
            <v-card-title :class="$root.dark ? 'grey darken-4' : 'primary'"
                          style="margin: 0 -24px 24px; padding: 5px 10px 0;"
            >
                <h3 class="white--text mb-1">{{ title }}</h3>
            </v-card-title>
            <v-textarea
                style="margin: -10px -10px 0;"
                v-model="note"
                label="Note"
                autofocus
                required
                outline
                hide-details
                ref="noteTextArea"
            ></v-textarea>
        </template>
    </v-edit-dialog>
</template>

<script>
import axios from "axios";

export default {
    name: "edit-note",
    props: ['item', 'items', 'url', 'add'],
    data() {
        return {
            note: '',
            title: 'Edit Note',
        }
    },
    methods: {
        showEditNote(item) {
            this.note = item.note
            if (this.add) {
                this.title = 'Add Note'
            }
            setTimeout(() => {
                this.$refs.noteTextArea.focus()
            }, 100);
        },
        editNote(item) {
            if (this.note.trim().length) {
                let editedIndex = this.items.indexOf(item)
                axios.put(`/${this.url}/${item.id}/edit-note`, {note: this.note})
                    .then((response) => {
                        this.loading = false
                        Object.assign(this.items[editedIndex], response.data.data)
                    })
                    .catch(error => {
                        this.handleError(error)
                    })
            }
        },
    },
}
</script>

<style scoped>
.v-small-dialog__content {
    padding: 0 !important;
}
</style>