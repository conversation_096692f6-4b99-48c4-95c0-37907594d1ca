<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/17/2018
 * Time: 11:12 AM
 */

namespace Console\Http\Controllers\Api;

use App\Models\Oauth\ConsoleActivity;
use Console\Http\Resources\Resource;

/**
 * Class ActivityController
 * @package Console\Http\Controllers\Api
 */
class ActivityController extends ResourceController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $collection = ConsoleActivity::with(['user.googleAccount'])->get();

        return $this->response(Resource::collection($collection));
    }

}
