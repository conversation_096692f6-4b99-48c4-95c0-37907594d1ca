<?php

declare(strict_types=1);

namespace Console\Http\Controllers\Api;

use App\Models\Gate\ExpertService;
use App\Models\Gate\Service;
use Console\Http\Resources\ExpertServiceResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\Resource;

/**
 * Class EcosystemServicesController
 * @package Console\Http\Controllers\Api
 */
class EcosystemServicesController extends ServicesController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    #[\Override]
    public function index()
    {
        $collection = Service::ecosystem()
            ->active()
            ->with(['group'])
            ->withCount('expertServices')
            ->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * @return \Illuminate\Http\Response
     */
    #[\Override]
    public function autocomplete()
    {
        $collection = Service::ecosystem()->active()->orderBy('name')->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    #[\Override]
    public function archived()
    {
        $collection = Service::ecosystem()
            ->archived()
            ->with(['group', 'tag'])
            ->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function relatedExpertServices($id)
    {
        $collection = ExpertService::console()->active()
            ->where('service_id', $id)
            ->with([
                'expert',
                'category',
                'service',
                'operationCountries',
                'languages',
            ])
            ->get();

        return $this->response(ExpertServiceResource::collection($collection));
    }

    /**
     * @return \Illuminate\Http\Response
     */
    #[\Override]
    public function byCategory($categoryId)
    {
        $collection = Service::ecosystem()->active()
            ->where('group_id', $categoryId)
            ->with('group')
            ->withCount(['expertServices as expert_services_count_active' => function ($q): void {
                $q->where('active', 1);
            }])
            ->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * @param Request $request
     * @param Service|null $item
     * @throws \Illuminate\Validation\ValidationException
     */
    #[\Override]
    protected function validateRequest(Request $request, $item = null)
    {
        $rules = [
            'name.en' => 'required',
            'name.bg' => 'sometimes|required',
        ];

        if (!$item || !$item->archived) {
            $rules['group_id'] = 'required|int';
        }

        $this->validate($request, $rules, [
            'group_id.required' => 'The Sub Category is required',
        ]);
    }

    /**
     * @param Service $service
     * @param Request $request
     * @return Service
     */
    #[\Override]
    protected function fill(Service $service, Request $request): Service
    {
        $service->fill([
            'group_id' => $request->input('group_id'),
            'name' => $request->input('name'),
            'ecosystem' => 1,
        ]);

        return $service;
    }
}
