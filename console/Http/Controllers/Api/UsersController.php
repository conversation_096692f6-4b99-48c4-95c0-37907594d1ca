<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/17/2018
 * Time: 11:12 AM
 */

namespace Console\Http\Controllers\Api;

use App\Models\Gate\Stage;
use App\Models\Gate\StageStep;
use App\Models\Gate\UsersSites;
use App\Models\Oauth\ConsoleActivity;
use App\Models\Oauth\UserGroup;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\Resource;
use App\Models\Oauth\User;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;

/**
 * Class UsersController
 * @package Console\Http\Controllers\Api
 */
class UsersController extends ResourceController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $collection = User::active()
            ->whereHas('googleAccount', function ($query): void {
                $query->where('console_access', 1);
            })->with('googleAccount', 'roles', 'groups')->get();

        foreach ($collection as $item) {
            $item->group_ids = $item->groups->pluck('id');
            $item->group_names = $item->groups->implode('name', ', ');
        }

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return Response
     * @throws ValidationException
     */
    public function search(Request $request)
    {
        $this->validate($request, ['email' => 'required|min:6']);
        $email = $request->input('email');

        $collection = User::where('email', 'like', sprintf('%%%s%%', $email))
            ->whereHas('googleAccount', function ($query): void {
                $query->where('console_access', 0);
            })->get();

        $collection->map->addVisible(['user_id', 'email']);

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function deleted()
    {
        $collection = User::whereNotNull('console_deleted_at')
            ->whereHas('googleAccount', function ($query): void {
                $query->where('console_access', 1);
            })->with('googleAccount', 'roles', 'groups')->get();

        foreach ($collection as $item) {
            $item->group_ids = $item->groups->pluck('id');
            $item->group_names = $item->groups->implode('name', ', ');
        }

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    public function autocomplete()
    {
        $collection = User::whereHas('googleAccount', function ($query): void {
            $query->where('console_access', 1);
        })->with(['googleAccount' => function ($query): void {
            $query->select('user_id', 'avatar');
        }])->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * Display the specified resource.
     * @param string $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $item = User::with(['googleAccount', 'roles'])->findOrFail($id);

        return $this->response(new \Illuminate\Http\Resources\Json\JsonResource($item));
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function stageStepsCounts($id)
    {
        $stage = Stage::with(['steps' => function ($q): void {
            $q->orderBy('sort_order');
        }])->findOrFail(1);

        foreach ($stage->steps as $step) {
            if ($step->getKey() == StageStep::CONTACT_STEP_ID) {
                $step->count = \App\Models\Gate\User::where('stage_step_id', $step->getKey())->count();
                continue;
            }

            $step->count = UsersSites::where('stage_step_id', $step->getKey())
                ->where('cc_user_id', $id)
                ->count();
        }

        $stage->paid_count = UsersSites::where('store_type', UsersSites::STORE_TYPE_PAID)
            ->where('cc_user_id', $id)
            ->count();

        $stage->not_interested_count = UsersSites::where('stage_status', Stage::STATUS_NOT_INTERESTED)
            ->where('cc_user_id', $id)
            ->count();

        return response()->json(['data' => $stage]);
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'user_id' => 'required|int'
        ]);

        /** @var User $user */
        $user = User::findOrFail($request->input('user_id'));
        $user->load(['googleAccount', 'roles', 'groups']);

        $user->googleAccount->update(['console_access' => 1]);

        return $this->response(new \Illuminate\Http\Resources\Json\JsonResource($user));
    }

    /**
     * @param Request $request
     * @param $id
     * @return Response
     * @throws ValidationException
     */
    public function update(Request $request, $id)
    {
        $this->validate($request, [
            'max_discount' => 'nullable|int|min:0|max:100',
            'calendly_url' => 'nullable|url',
        ]);

        $maxDiscount = $request->input('max_discount');
        if (empty($maxDiscount) && $maxDiscount !== 0) {
            $maxDiscount = null;
        }

        /** @var User $user */
        $user = User::findOrFail($id);

        $user->fill([
            'countries' => $request->input('countries'),
            'phone' => $request->input('phone'),
            'voip' => $request->input('voip'),
            'calendly_url' => $request->input('calendly_url'),
            'target_new' => $request->input('target_new'),
            'target_rec' => $request->input('target_rec'),
            'max_discount' => $maxDiscount,
            'is_cc_employee' => $request->input('is_cc_employee'),
        ])->save();

        $user->groups()->detach();
        if (!empty($ids = $request->input('group_ids'))) {
            $groups = UserGroup::findMany($ids);
            $user->groups()->saveMany($groups);
        }

        $user->load(['googleAccount', 'roles', 'groups']);
        $user->group_ids = $user->groups->pluck('id');
        $user->group_names = $user->groups->implode('name', ', ');

        return $this->response(new \Illuminate\Http\Resources\Json\JsonResource($user));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $item = User::findOrFail($id);

        $item->update(['console_deleted_at' => now()]);

        ConsoleActivity::log(auth()->user(), 'delete', $item);

        return response('', 204);
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function undelete($id)
    {
        $item = User::findOrFail($id);

        $item->update(['console_deleted_at' => null]);

        ConsoleActivity::log(auth()->user(), 'update', $item);

        return response('', 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function updateRoles(Request $request, $id)
    {
        /** @var User $user */
        $user = User::findOrFail($id);

        $user->syncRoles($this->extractRoles($request));
        $user->load(['googleAccount', 'roles', 'groups']);

        $user->group_ids = $user->groups->pluck('id');
        $user->group_names = $user->groups->implode('name', ', ');

        return $this->response(new \Illuminate\Http\Resources\Json\JsonResource($user));
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function monthlyTargetsExecution($id)
    {
        /** @var User $user */
        $user = User::findOrFail($id);

        return response()->json(['data' => $user->getMonthlyTargetsExecution()]);
    }

    /**
     * @param Illuminate\Http\Request $request
     * @param mixed $id
     * @return mixed
     */
    public function performance(Request $request, $id)
    {
        /** @var User $user */
        $user = User::findOrFail($id);

        $currentStart = Carbon::createFromFormat('Y-m-d', $request->input('start_date'))->startOfDay()->utc();
        $currentEnd = Carbon::createFromFormat('Y-m-d', $request->input('end_date'))->endOfDay()->utc();
        $diff = $currentEnd->diffInDays($currentStart) + 1;
        $previousStart = clone $currentStart;
        $previousStart->subDays($diff)->startOfDay()->utc();
        $previousEnd = clone $currentEnd;
        $previousEnd->subDays($diff)->endOfDay()->utc();

        $data['total_revenue']['current'] = $user->getTotalRevenue($currentStart, $currentEnd);
        $data['total_revenue']['previous'] = $user->getTotalRevenue($previousStart, $previousEnd);

        $data['paid_offers']['current'] = $user->getPaidOffers($currentStart, $currentEnd);
        $data['paid_offers']['previous'] = $user->getPaidOffers($previousStart, $previousEnd);

        $data['sent_offers']['current'] = $user->getSentOffers($currentStart, $currentEnd);
        $data['sent_offers']['previous'] = $user->getSentOffers($previousStart, $previousEnd);

        $data['paid_offers']['current']['cr'] = get_percentage(
            $data['sent_offers']['current']['count'],
            $data['paid_offers']['current']['count'],
        );
        $data['paid_offers']['previous']['cr'] = get_percentage(
            $data['sent_offers']['previous']['count'],
            $data['paid_offers']['previous']['count'],
        );

        $data['forecast_revenue'] = $user->getForecastRevenue($currentStart, $currentEnd);

        $data['new_paid_stores']['current'] = $user->getNewPaidStores($currentStart, $currentEnd);
        $data['new_paid_stores']['previous'] = $user->getNewPaidStores($previousStart, $previousEnd);

        $data['existing_stores_revenue']['current'] = $user->getExistingStoresRevenue($currentStart, $currentEnd);
        $data['existing_stores_revenue']['previous'] = $user->getExistingStoresRevenue($previousStart, $previousEnd);

        return response()->json(['data' => $data]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Support\Collection
     */
    protected function extractRoles(Request $request)
    {
        $roles = collect($request->input('roles'));
        $roles = $roles->where('active', true)->pluck('name');

        return $roles;
    }

}
