<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/17/2018
 * Time: 11:12 AM
 */

namespace Console\Http\Controllers\Api;

use AcmePhp\Ssl\Parser\CertificateParser;
use App\Helper\YesNo;
use App\Models\Router\Certificate;
use App\Models\Router\Host;
use Carbon\Carbon;
use Cloudflare;
use Illuminate\Http\Resources\Json\Resource;
use Throwable;

/**
 * Class DomainsController
 * @package Console\Http\Controllers\Api
 */
class DomainsController extends ResourceController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $collection = Host::where('external', YesNo::True)
//            ->where('active', YesNo::True)
            ->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * @param $id
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|\Illuminate\Http\Response
     */
    public function certificate($id)
    {
        $item = Certificate::findOrFail($id);

        $cert_info = Certificate::parseAcme($item->cert);

        return response(['data' => $cert_info]);
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function update($id)
    {
        $item = Host::findOrFail($id);

        try {
            $item->ns = $item->getCurrentNameservers();
            $item->ips = $item->getCurrentIps();
            $item->fillWhois();
        } catch (Throwable) {
        }

        if ($item->isDirty()) {
            $item->save();
        }

        return $this->response(new \Illuminate\Http\Resources\Json\JsonResource($item));
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function zone($id)
    {
        $item = Host::findOrFail($id);

        try {
            $response = Cloudflare::zone()->getZoneById($item->cloudflare_zone_id);
            $item->zone = $response->result;
            //        $item->ssl = Cloudflare::ssl()->getSSLVerificationStatus($item->cloudflare_zone_id);

            $adapter = Cloudflare::getAdapter();
            $ssl = json_decode($adapter->get(
                'zones/' . $item->cloudflare_zone_id . '/ssl/certificate_packs'
            )->getBody()->getContents());
            $item->ssl = $ssl->result ?? null;
        } catch (\Exception) {
            //
        }

        return $this->response(new \Illuminate\Http\Resources\Json\JsonResource($item));
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function zoneRecords($id)
    {
        $item = Host::findOrFail($id);
        $response = Cloudflare::dns($item->cloudflare_zone_id)->getRecords(
            '',
            '',
            '',
            1,
            200
        );

        $data = $response->result ?: [];

        foreach ($data as $item) {
            if (str_contains((string) $item->content, (string) config('url.sites'))) {
                try {
                    $response = Cloudflare::dns()->getRecordByName($item->content);
//                    $response = Cloudflare::dns()->getRecordByName('b2bmitevbg.cloudcart.net');
                    if ($response) {
                        $item->content_details = $response;
                    } else {
                        $item->content_details = Cloudflare::dns()->getRecordByName('gcp.cloudcart.net');
                    }
                } catch (\Exception) {
                    //
                }
            }
        }

        return response()->json(['data' => $data]);
    }

    /**
     * @param mixed $siteId
     * @return mixed
     */
    public function getMainHostDNS($siteId)
    {
        $data = [];

        /** @var Host $domain */
        $domain = Host::owner($siteId)
            ->where('external', YesNo::False)
            ->firstOrFail();

        try {
            $response = Cloudflare::dns()->getRecordByName($domain->host);
//            $response = Cloudflare::dns()->getRecordByName('b2bmitevbg.cloudcart.net');
            if ($response) {
                $data = $response;
            }
        } catch (\Exception) {
            //
        }

        return response()->json(['data' => $data]);
    }

    public function getCloudCartNetRecords()
    {
        $response = Cloudflare::dns()->getRecords(
            '',
            '',
            '',
            1,
            10000
        );

        return response()->json(['data' => $response->result]);
    }
}
