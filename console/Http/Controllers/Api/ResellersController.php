<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/17/2018
 * Time: 11:12 AM
 */

namespace Console\Http\Controllers\Api;

use App\Helper\CloudcartCurrency;
use App\Helper\Mail\ElasticEmail;
use App\Mail\ResellerActivation;
use App\Models\Gate\Invoice;
use App\Models\Gate\KeyAccount;
use App\Models\Gate\Reseller;
use App\Models\Gate\ResellerCommissionSetting;
use App\Models\Gate\ResellerCredit;
use App\Models\Gate\SiteSubscription;
use App\Models\Gate\User;
use App\Models\Gate\UsersSites;
use App\Models\Oauth\ConsoleActivity;
use App\Models\Oauth\SocialAccount;
use Console\Exports\ResellersExport;
use Console\Http\Resources\ExpertResource;
use Console\Http\Resources\MerchantResource;
use Console\Http\Resources\Resource;
use Console\Http\Resources\SiteResource;
use Hashids\Hashids;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Storage;

/**
 * Class ResellersController
 * @package Console\Http\Controllers\Api
 */
class ResellersController extends ResourceController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $collection = Reseller::console()
            ->reseller()
            ->with(['operationCountries'])
            ->withCount('sites')
            ->get();

        $collection->map->append([
            'reseller_revenue',
        ]);

        return $this->response(ExpertResource::collection($collection));
    }

    /**
     * @return \Illuminate\Http\Response
     */
    public function autocomplete(Request $request)
    {
        $query = Reseller::console()->active();

        if (!$request->get('all')) {
            $query->reseller();
        }

        $collection = $query->orderBy('name')->get();

        return $this->response(Resource::collection($collection));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $this->validateResellerRequest($request);

        $attributes = array_merge(
            $request->except([
                'operation_countries',
                'contact_name',
                'email',
                'phone',
            ]),
            [
                'expert' => (int)$request->input('expert'),
                'active' => 2,
            ]
        ); // New partners are always pending (active = 2)

        if ((int)$request->input('level')) {
            $attributes['mapping'] = Str::slug($attributes['name']);
        }

        $item = Reseller::create($attributes);

        $phone = trim((string) $request->input('phone'));
        if (empty($phone)) {
            $phone = null;
        }

        $account = KeyAccount::create([
            'reseller_id' => $item->getKey(),
            'name' => $request->input('contact_name'),
            'email' => $request->input('email'),
            'phone' => $phone,
        ]);

        $item->keyAccount()->associate($account)->save();

        $item->syncOperationCountries($request->input('operation_countries', []));

        return $this->response(new ExpertResource($item));
    }

    /**
     * Display the specified resource.
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $item = Reseller::with(['keyAccount', 'invoicing', 'operationCountries'])
//            ->reseller()
            ->findOrFail($id);
        //        $item->append('short_url');
        //        if ($item->keyAccount) {
        //            $item->keyAccount->append('short_url');
        //        }

        return $this->response(new ExpertResource($item));
    }

    /**
     * @param $id
     * @return Response
     */
    public function activate($id)
    {
        $reseller = Reseller::with('keyAccount')->findOrFail($id);
        if (!$reseller->keyAccount) {
            throw new HttpResponseException(new Response([
                'message' => 'This Reseller does not have a default key account!!'
            ], 422));
        }

        $reseller->update(['active' => 1]);

        $hash = new Hashids(uniqid(), 8);
        $password = $hash->encode(\Carbon\Carbon::now()->timestamp);
        $reseller->keyAccount->createUpdateOAuth(['password' => bcrypt($password)]);
        $reseller->keyAccount->update(['active' => 1]);

        ElasticEmail::changeAccount('elastic_email_sub');
        $res = \Illuminate\Support\Facades\Mail::to($reseller->keyAccount)->send(new ResellerActivation($reseller, $password));

        return $this->response(new JsonResource($reseller));
    }

    /**
     * @param $resellerId
     * @return \Illuminate\Http\Response
     */
    public function keyAccounts($resellerId)
    {
        $collection = KeyAccount::with('reseller')
            ->where('reseller_id', $resellerId)
            ->get();

        return $this->response(Resource::collection($collection));
    }

    /**
     * @param $resellerId
     * @return \Illuminate\Http\Response
     */
    public function merchants($resellerId)
    {
        $merchants = User::where('reseller_id', $resellerId)
            ->orderBy('id', 'desc')
            ->get();

        return $this->response(MerchantResource::collection($merchants));
    }

    /**
     * @param $resellerId
     * @return \Illuminate\Http\Response
     */
    public function sites($resellerId)
    {
        $sites = UsersSites::with(['user', 'keyAccount'])
            ->where('reseller_id', $resellerId)
            ->orderBy('id', 'desc')
            ->get();

        return $this->response(SiteResource::collection($sites));
    }

    /**
     * @param $resellerId
     * @return \Illuminate\Http\Response
     */
    public function subscriptions($resellerId)
    {
        $collection = SiteSubscription::with(['user', 'model', 'site'])
            ->whereHas('site', function ($query) use ($resellerId): void {
                $query->where('reseller_id', $resellerId);
            })
            ->orderByDesc('updated_at')
            ->get();

        return $this->response(JsonResource::collection($collection));
    }

    /**
     * @param $resellerId
     * @return Response
     */
    public function invoices($resellerId = null)
    {
        $collection = Invoice::consoleReseller();

        if ($resellerId) {
            $collection->where('reseller_id', $resellerId);
        }

        $collection = $collection->with(['reseller', 'recipient'])
            ->get();

        return $this->response(Resource::collection($collection));
    }

    /**
     * @param null $resellerId
     * @return \Illuminate\Http\Response
     */
    public function credits($resellerId = null)
    {
        $collection = ResellerCredit::console();

        if ($resellerId) {
            $collection->where('reseller_id', $resellerId);
        }

        $collection = $collection->with([
            'reseller',
            'invoice',
            'model',
            'subscription.site',
            'subscription.user',
//            'subscription.model',
        ])->get();

        return $this->response(JsonResource::collection($collection));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function creditsForInvoice(Request $request)
    {
        $reseller = $this->validateInvoiceRequest($request);

        $collection = ResellerCredit::console()
            ->where('reseller_id', $reseller->getKey())
            ->whereNull('invoice_id')
            ->whereDate('created_at', '>=', $request->input('from_date'))
            ->whereDate('created_at', '<=', $request->input('to_date'))
            ->with([
                'reseller',
                'model',
                'subscription.site',
                'subscription.user',
//                'subscription.model',
            ])->get();

        return $this->response(JsonResource::collection($collection));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ValidationException
     * @throws \Exception
     */
    public function creditsCreateInvoice(Request $request)
    {
        $reseller = $this->validateInvoiceRequest($request);

        $credits = ResellerCredit::with('subscription.site')
            ->where('reseller_id', $reseller->getKey())
            ->whereNull('invoice_id')
            ->whereDate('created_at', '>=', $request->input('from_date'))
            ->whereDate('created_at', '<=', $request->input('to_date'))
            ->get();

        if ($credits->isEmpty()) {
            throw new HttpResponseException(new Response([
                'message' => 'No not invoiced credits found!'
            ], 422));
        }

        $amount = 0;
        $attributes = [
            'recipient_id' => $reseller->invoicing->getKey(),
            'reseller_id' => $reseller->getKey(),
            'site_id' => null,
            'type' => Invoice::INVOICE,
            'payment_method' => Invoice::BANK,
            'payment_status' => Invoice::UNPAID,
//                'amount' => $amount * 100,
            'amount' => $amount,
            'currency' => CloudcartCurrency::default(),
//                'subscription_id' => $params['subscriptionId'] ?? null,
//                'discount_id' => $params['discount_id'] ?? null,
//                'discount_amount' => $params['discount_amount'] ?? null,
//                'renew' => (int)$isRenew,
        ];

        $invoice = retry(3, fn() => Invoice::create($attributes), 1000);

        foreach ($credits as $credit) {
            /** @var ResellerCredit $credit */
            $description = implode(' | ', [
                $credit->name,
                'Credit #' . $credit->getKey(),
                $credit->created_at->format('d.m.y'),
                'Site #' . $credit->subscription->site_id,
                $credit->subscription->site->primary_host,
                'Subscription #' . $credit->subscription->getKey(),
                $credit->percent . ' %'
            ]);

            $item = $invoice->createItem($credit->subscription->model, [
                'description' => $description,
                'amount' => $credit->amount_credit,
                'quantity' => 1,
                'reseller_credit_id' => $credit->getKey(),
            ]);

            $credit->invoice()->associate($invoice)->save();
            $amount += $item->total;
        }

        $invoice->update(['amount' => $amount]);
        $invoice->setIssuer(auth()->user())->save();

        ConsoleActivity::log(auth()->user(), 'create', $invoice);

        $message = sprintf('Invoice #%s was created successfully', $invoice->number);

        $response = [
            'status' => 'success',
            'message' => $message,
        ];

        return response()->json($response);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function update(Request $request, $id)
    {
        /** @var Reseller $item */
        $item = Reseller::findOrFail($id);

        $this->validateResellerRequest($request);

        $item->fill($request->only(array_keys($item->getOriginal())));
        $item->key_account_id = $request->input('key_account_id');

        if (!$item->mapping && $item->isReseller()) {
            $item->mapping = Str::slug($item->name);
        }

        if ($item->isDirty('cc_user_id')) {
            $sites = $item->sites()->get();
            foreach ($sites as $site) {
                $site->update(['cc_user_id' => $item->cc_user_id]);
            }
        }

        $item->save();

        $item->syncOperationCountries($request->input('operation_countries', []));
        $item->refresh()->load(['keyAccount']);

        return $this->response(new ExpertResource($item));
    }

    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function currentCommission($id)
    {
        /** @var ResellerCommissionSetting $settings */
        $settings = ResellerCommissionSetting::with('reseller')->partner($id)->firstOrFail();
        auth()->user()->restrictAccessTo($settings);

        return \response()->json(['data' => $settings->reseller->getCurrentCommissionSettings()]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     * @throws \Exception
     */
    public function destroy($id)
    {
        /** @var Reseller $item */
        $item = Reseller::findOrFail($id);
        if ($item->isInUse()) {
            throw new HttpResponseException(new Response([
                'message' => 'The Reseller is in use and can not be removed!'
            ], 422));
        }

        $item->delete();

        ConsoleActivity::log(auth()->user(), 'delete', $item);

        return response('', 204);
    }

    /**
     * @param Request $request
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateResellerRequest(Request $request)
    {
        $id = (int)$request->input('id');

        $rules = [
            'name' => 'required|unique:gate.cc_gate.resellers,name,' . $id,
            'company' => 'required|unique:gate.cc_gate.resellers,company,' . $id,
            'company_eik' => 'required|unique:gate.cc_gate.resellers,company_eik,' . $id,
            'country' => 'required',
            'city' => 'required',
            'address' => 'required',
            'website' => 'nullable|url',
        ];

        if ($request->input('expert')) {
            $rules += [
                'operation_countries' => 'required|array|min:1',
                'number_employees' => 'required|int',
                'number_clients' => 'required|int',
                'company_lifetime' => 'required|int',
                'company_info' => 'required',
                'company_industry' => 'required',
            ];
        } else {
            $rules += [
                'level' => "required|int|min:1|max:3",
            ];
        }

        if (empty($id)) {
            $rules += [
                'contact_name' => 'required',
                'email' => "required|email|unique:gate.cc_gate.key_accounts,email",
                'phone' => "nullable|numeric|unique:gate.cc_gate.key_accounts,phone",
            ];
        }

        $this->validate($request, $rules);

        if (empty($id)) {
            $hasConsoleAccess = SocialAccount::where('console_access', 1)
                ->where('email', $request->input('email'))
                ->first(['id']);

            if ($hasConsoleAccess) {
                throw ValidationException::withMessages([
                    'email' => 'The email has already been taken.'
                ]);
            }
        }

        //        $id = (int)$request->input('id');
        //        if ($request->input('mapping')) {
        //            $request->offsetSet('mapping', Str::slug($request->input('mapping')));
        //        }
        //
        //        $this->validate($request, [
        //            'name' => "required|unique:gate.cc_gate.resellers,name,$id",
        //            'mapping' => "required|unique:gate.cc_gate.resellers,mapping,$id",
        //            'level' => 'required',
        //            'company' => "required|unique:gate.cc_gate.resellers,company,$id",
        //            'company_eik' => "required|unique:gate.cc_gate.resellers,company_eik,$id",
        //            'country' => 'required',
        //            'city' => 'required',
        //            'address' => 'required',
        //        ]);
    }

    /**
     * @param Request $request
     * @return Reseller
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateInvoiceRequest(Request $request)
    {
        $this->validate($request, [
            'reseller_id' => 'required|numeric',
            'from_date' => 'required|date',
            'to_date' => 'required|date',
        ]);

        $reseller = Reseller::findOrFail($request->input('reseller_id'));

        if (!$reseller->invoicing) {
            throw new HttpResponseException(new Response([
                'message' => 'No invoicing details were found'
            ], 422));
        }

        return $reseller;
    }

    /**
     * @return Response
     */
    public function countries()
    {
        $country = Reseller::distinct()->pluck('country')->filter()->values()->map(fn($item): array => [
            'code' => $item,
            'name' => $item . ' - ' . locale_get_display_region('-' . $item, 'en')
        ]);

        $data = compact('country');
        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::make($data));
    }

    /**
     * @param Request $request
     * @return ResellersExport
     */
    public function export(Request $request): \Console\Exports\ResellersExport
    {
        $query = Reseller::console()
            ->reseller()
            ->withCount('sites');

        $query = $this->filterQuery($request, $query);

        $items = $query->get();

        return new ResellersExport($items);
    }

    /**
     * @param Request $request
     * @param $query
     * @return \App\Models\Gate\User|\Illuminate\Database\Eloquent\Builder
     */
    protected function filterQuery(Request $request, $query)
    {
        $filters = $request->get('filters', '{}');
        if (!is_array($filters)) {
            $filters = json_decode((string) $request->get('filters', '{}'), true);
        }

        if ($filters['country'] ?? null) {
            $query->where('country', $filters['country']);
        }

        if (isset($filters['status'])) {
            $query->where('active', $filters['status']);
        }

        if (isset($filters['level'])) {
            $query->where('level', $filters['level']);
        }

        if (isset($filters['sites'])) {
            if ($filters['sites'] > 0) {
                $query->has('sites', '>', 0);
            } else {
                $query->has('sites', 0);
            }
        }

        if (!empty($search = trim((string) $request->get('search')))) {
            $columns = [
                'name',
                'company',
                'company_eik',
                'website',
            ];

            $query->where(function ($q) use ($columns, $search): void {
                $q->where('id', $search);
                foreach ($columns as $column) {
                    $q->orWhere($column, 'LIKE', '%' . $search . '%');
                }
            });
        }

        return $query;
    }
}
