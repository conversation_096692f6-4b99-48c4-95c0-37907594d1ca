<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/17/2018
 * Time: 11:12 AM
 */

namespace Console\Http\Controllers\Api;

use App\Models\Gate\PolicyAcceptanceLog;
use App\Models\Gate\PolicyAcceptanceLogContent;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\Resource;

/**
 * Class GdprController
 * @package Console\Http\Controllers\Api
 */
class GdprController extends ResourceController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //        $collection = PolicyAcceptanceLog::with(['user', 'content'])->orderBy('id', 'desc')->get();
        //        $collection = PolicyAcceptanceLog::with(['user'])->orderBy('id', 'desc')->get();
        //        $collection->map(function ($item) {
        //            unset($item->content->content);
        //            return $item;
        //        });

        $query = PolicyAcceptanceLog::with(['user']);
        $query = $this->filterQuery($request, $query);

        $collection = $this->paginateQuery($request, $query);

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    public function content()
    {
        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection(PolicyAcceptanceLogContent::all()));
    }

    /**
     * @param Request $request
     * @param $query
     * @return \App\Models\Gate\User|\Illuminate\Database\Eloquent\Builder
     */
    protected function filterQuery(Request $request, $query)
    {
        if (!empty($search = trim((string) $request->get('search')))) {
            $columns = [
                'ip',
                'user_agent',
                'form',
            ];

            $query->where(function ($q) use ($columns, $search): void {
                $q->where('id', $search);
                foreach ($columns as $column) {
                    $q->orWhere($column, 'LIKE', '%' . $search . '%');
                }

                $columns = [
                    'id',
                    'unique_id',
                    'first_name',
                    'last_name',
                    'email',
                    'phone',
                ];
                $q->orWhereHas('user', function ($uq) use ($columns, $search): void {
                    $uq->whereRaw('concat(users.first_name, " ", users.last_name) like "%' . $search . '%"');
                    foreach ($columns as $column) {
                        $uq->orWhere($column, 'LIKE', '%' . $search . '%');
                    }
                });

                $columns = [
                    'mapping',
                    'title',
                ];
                $q->orWhereHas('content', function ($uq) use ($columns, $search): void {
                    $uq->where('id', $search);
                    foreach ($columns as $column) {
                        $uq->orWhere($column, 'LIKE', '%' . $search . '%');
                    }
                });
            });
        }

        return $query;
    }

}
