<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/17/2018
 * Time: 11:12 AM
 */

namespace Console\Http\Controllers\Api;

use App\Models\Gateway\PaymentProviderConfiguration;
use App\Models\Log\ConsoleCommand;
use App\Models\Queue\SiteQueue;
use App\Models\Router\SiteInstalledApps;
use App\Models\Statistics\OrderFulfillment;
use App\Models\Statistics\OrderPayment;
use App\Models\Statistics\Site;
use App\Models\Statistics\SiteIncomesByPaymentTypeAndIndustry;
use App\Models\Statistics\SitePerIndustry;
use App\Models\Statistics\SiteSessions;
use Console\Http\Controllers\Paginatable;
use Console\Http\Resources\Resource;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

/**
 * Class ReportsController
 * @package Console\Http\Controllers\Api
 */
class StatisticsController extends ResourceController
{
    use Paginatable;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function orderPayments()
    {
        $resource = OrderPayment::get();

        return $this->response(Resource::collection($resource));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function orderFulfillment()
    {
        $resource = OrderFulfillment::get();

        return $this->response(Resource::collection($resource));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function sitesPerIndustry()
    {
        $resource = SitePerIndustry::get();

        return $this->response(Resource::collection($resource));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function sitesFulfillments()
    {
        $resource = OrderFulfillment::get();

        return $this->response(Resource::collection($resource));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function sitesIncomesByPaymentTypeAndIndustry()
    {
        $resource = SiteIncomesByPaymentTypeAndIndustry::get();

        return $this->response(Resource::collection($resource));
    }

    /**
     * @param null $id
     * @return \Illuminate\Http\Response
     */
    public function siteTraffic($id = null)
    {
        if ($id) {
            $resource = SiteSessions::where('site_id', $id)->get();
        } else {
            $resource = SiteSessions::get();
        }

        return $this->response(Resource::collection($resource));
    }

    /**
     * @param null $id
     * @return \Illuminate\Http\Response
     */
    public function sites(Request $request)
    {
        $sortBy = $request->get('sortBy', 'site_id');
        $sortOrder = 'false' == $request->get('descending') ? 'asc' : 'desc';
        $rowsPerPage = (int)$request->get('rowsPerPage', 25);
        if ($rowsPerPage < 0) {
            $rowsPerPage = 20000;
        }

        $query = Site::query();
        $query = $this->filterSiteStatisticQuery($request, $query);

        $collection = $this->paginateQuery($request, $query);

        return $this->response(Resource::collection($collection));
    }

    /**
     * @param Request $request
     * @param $query
     * @return \App\Models\Gate\User|\Illuminate\Database\Eloquent\Builder
     */
    protected function filterSiteStatisticQuery(Request $request, $query)
    {
        if (is_array($request->get('filters'))) {
            $filters = $request->get('filters');
        } else {
            $filters = json_decode((string) $request->get('filters', '{}'), true);
        }

        if (!empty($filters['user'])) {
            $query->whereIn('cc_user_id', (array)$filters['user']);
        }

        return $query;
    }

    /**
     * @param Illuminate\Http\Request $request
     * @param mixed $query
     * @return mixed
     */
    protected function filterSiteApps(Request $request, $query)
    {
        if (is_array($request->get('filters'))) {
            $filters = $request->get('filters');
        } else {
            $filters = json_decode((string) $request->get('filters', '{}'), true);
        }

        if ($filters['status'] ?? null) {
            $query->where('installed', (int)in_array(1, $filters['status']))
                ->where('active', (int)in_array(2, $filters['status']));
        }

        if (!empty($search = trim((string) $request->get('search')))) {
            $columns = [
//                'site_id',
                'mapping',
            ];

            $query->where(function ($q) use ($columns, $search): void {
                $q->where('site_id', $search);
                foreach ($columns as $column) {
                    $q->orWhere($column, 'LIKE', '%' . $search . '%');
                }

                $columns = [
//                    'id',
                    'name',
                ];
                $q->orWhereHas('app.translations', function ($tq) use ($columns, $search): void {
                    $tq->where('id', $search);
                    foreach ($columns as $column) {
                        $tq->orWhere($column, 'LIKE', '%' . $search . '%');
                    }
                });
            });
        }

        return $query;
    }

    /**
     * @param Request $request
     * @param null $id
     * @return Response
     */
    public function siteApps(Request $request, $id = null)
    {
        if ($id) {
            $collection = SiteInstalledApps::with(['app.translations' => function ($query): void {
                $query->select('app_id', 'locale', 'name');
            }])
                ->whereSiteId($id)
                ->get();
        } else {
            $query = SiteInstalledApps::with(['app.translations' => function ($q): void {
                $q->select('app_id', 'locale', 'name');
            }]);

            $query = $this->filterSiteApps($request, $query);
            $collection = $this->paginateQuery($request, $query);
        }

        return $this->response(Resource::collection($collection));
    }

    /**
     * @param null $id
     * @return \Illuminate\Http\Response
     */
    public function siteProviders($id = null)
    {
        $columns = [
            'site_id',
            'provider',
            'active',
            'created_at',
            'updated_at',
        ];

        if ($id) {
            $resource = PaymentProviderConfiguration::where('site_id', $id)
                ->get($columns);
        } else {
            $resource = PaymentProviderConfiguration::get($columns);
        }

        return $this->response(Resource::collection($resource));
    }

    /**
     * @param $key
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function executeJob($key)
    {
        SiteQueue::executeQueueTask($key);

        return response()->json([
            'status' => 'success',
            'message' => sprintf('Job (%s) will start soon', $key),
        ]);
    }

    /**
     * @param mixed $command
     * @return mixed
     */
    public function runCommand($command)
    {
        $command = ConsoleCommand::create([
            'command' => $command,
            'params' => [],
            'user_id' => auth()->id()
        ]);

        return $command->run();
    }
}
