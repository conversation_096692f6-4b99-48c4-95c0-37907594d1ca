<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/17/2018
 * Time: 11:12 AM
 */

namespace Console\Http\Controllers\Api;

use App\Contracts\InvoiceItemContract;
use App\Helper\Subscription;
use App\Models\Apps\Applications;
use App\Models\Gate\Discount;
use App\Models\Gate\ExpertService;
use App\Models\Gate\LtaBundle;
use App\Models\Gate\LtaBundleItem;
use App\Models\Gate\Offer;
use App\Models\Gate\OfferItem;
use App\Models\Gate\PlanDetails;
use App\Models\Gate\PlanFeaturePack;
use App\Models\Gate\Service;
use App\Models\Gate\Template;
use App\Models\Gate\Transaction;
use App\Models\Oauth\ConsoleActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\Resource;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;

/**
 * Class OfferItemsController
 * @package Console\Http\Controllers\Api
 */
class OfferItemsController extends ResourceController
{
    /**
     * Instantiate a new controller instance.
     *
     */
    public function __construct()
    {
        /** @var \App\Models\Oauth\User $user */
        $user = auth()->user();
        $role = $user ? $user->getConsoleRole() : null;
        if ($role == \App\Models\Oauth\User::CONSOLE_CLOUD_CART) {
            $this->middleware('permission:list subscriptions')->only('index');
            $this->middleware('permission:create subscriptions')->only('store');
            $this->middleware('permission:delete subscriptions')->only('destroy');
        }

        $this->middleware('permission:update subscriptions')->only('update');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $collection = OfferItem::with(['model'])
            ->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $this->validateCreate($request);

        /** @var \App\Models\Oauth\User $user */
        $user = auth()->user();

        /** @var \App\Models\Gate\Offer $offer */
        $offer = Offer::findOrFail($request->input('offer_id'));
        if (!$user->hasRole('super-admin') && $offer->owner_id != $user->getKey()) {
            throw new HttpResponseException(new Response([
                'message' => 'Only the owner can change the offer!'
            ], 422));
        }

        /** @var InvoiceItemContract|Model $item */
        $item = $this->getItem($request->input('type'), $request->input('id'));

        /** @var \App\Models\Gate\KeyAccount $ka */
        if (!$user->isCloudCartEmployee() && $ka = $user->keyAccount) {
            $maxPrice = round($ka->reseller->getMaxDiscountedPrice($item) / 100, 2);
            if ($request->input('price') < $maxPrice) {
                throw ValidationException::withMessages(['price' => sprintf('Max discounted price is %s.', $maxPrice)]);
            }

            if ($item->billing_cycle) {
                if ($request->input('next_billing_amount') < $item->price_input) {
                    throw ValidationException::withMessages([
                        'next_billing_amount' => "You cannot change next billing amount."
                    ]);
                }
            }
        }

        if ($request->input('expert_service_id')) {
            ExpertService::checkPricesOverride($request);
        }

        if ($user->isCloudCartEmployee()) {
            Subscription::checkPricesOverride($request, $item);
        }

        $offerItem = OfferItem::create([
            'offer_id' => $offer->getKey(),
            'model_id' => $item->getKey(),
            'model_type' => $item->getMorphClass(),
            'price' => round($request->input('price') * 100),
            'next_billing_amount' => round($request->input('next_billing_amount') * 100),
            'status' => 1,
            'value' => $request->input('value'),
            'expert_service_id' => $request->input('expert_service_id'),
        ]);

        ConsoleActivity::log(auth()->user(), 'create', $offerItem);

        $message = sprintf('New Offer Item #%s was created successfully', $offerItem->getKey());

        $offerItem->load(['model', 'expertService.expert']);

        $response = [
            'status' => 'success',
            'message' => $message,
            'data' => $offerItem->toArray()
        ];

        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ValidationException
     */
    public function fromLta(Request $request)
    {
        $this->validate($request, [
            'offer_id' => 'required|int',
            'lta_bundle_id' => 'required|int',
        ]);

        /** @var \App\Models\Gate\Offer $offer */
        $offer = Offer::findOrFail($request->input('offer_id'));
        if (!$request->user()->hasRole('super-admin') && $offer->owner_id != auth()->id()) {
            throw new HttpResponseException(new Response([
                'message' => 'Only the owner can change the offer!'
            ], 422));
        }

        $bundle = LtaBundle::with('items.model')
            ->findOrFail($request->input('lta_bundle_id'));

        $offer->items()->delete();
        foreach ($bundle->items as $item) {
            /** @var LtaBundleItem $item */
            $offerItem = OfferItem::create([
                'offer_id' => $offer->getKey(),
                'model_id' => $item->model_id,
                'model_type' => $item->model_type,
                'original_price' => $item->bundle_original_price,
                'price' => $item->bundle_price,
                'next_billing_amount' => $item->monthly_price,
                'status' => 1,
                'value' => $item->value,
                'expert_service_id' => $item->expert_service_id,
                'lta_bundle_item_id' => $item->getKey(),
            ]);
        }

        $offer->update(['lta_bundle_id' => $bundle->getKey()]);

        $message = "Items imported successfully";

        $response = [
            'status' => 'success',
            'message' => $message,
//            'data' => $offerItem->toArray()
        ];

        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ValidationException
     */
    public function fromDiscount(Request $request)
    {
        $this->validate($request, [
            'offer_id' => 'required|int',
            'discount_id' => 'required|int',
        ]);

        /** @var \App\Models\Gate\Offer $offer */
        $offer = Offer::findOrFail($request->input('offer_id'));
        if (!$request->user()->hasRole('super-admin') && $offer->owner_id != auth()->id()) {
            throw new HttpResponseException(new Response([
                'message' => 'Only the owner can change the offer!'
            ], 422));
        }

        /** @var \App\Models\Gate\Offer $offer */
        $discount = Discount::with('items.model')->findOrFail($request->input('discount_id'));

        $offer->items()->delete();
        foreach ($discount->items as $item) {
            $offerItem = OfferItem::create([
                'offer_id' => $offer->getKey(),
                'model_id' => $item->model_id,
                'model_type' => $item->model_type,
                'price' => $item->item_discounted_price,
                'next_billing_amount' => $item->model->billing_cycle ? $item->item_original_price : null,
                'status' => 1,
                'value' => $item->value,
            ]);
        }

        $offer->update(['discount_id' => $discount->getKey()]);

        $message = "Items imported successfully";

        $response = [
            'status' => 'success',
            'message' => $message,
//            'data' => $offerItem->toArray()
        ];

        return response()->json($response);
    }

    /**
     * @param $type
     * @param $id
     * @return Applications|Applications[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Eloquent\Model|null
     */
    protected function getItem($type, $id)
    {
        $item = match ($type) {
            'cloudcart_app' => Applications::findOrFail($id),
            'cloudcart_service' => Service::findOrFail($id),
            'cloudcart_feature' => PlanFeaturePack::findOrFail($id),
            'plan_details' => PlanDetails::findOrFail($id),
            'theme' => Template::findOrFail($id),
            default => null,
        };

        return $item;
    }

    /**
     * @param Request $request
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateCreate(Request $request)
    {
        $rules = [
            'type' => 'required',
            'id' => 'required',
            'offer_id' => 'required',
            'price' => 'required|numeric',
            'next_billing_amount' => 'required_with:billing_cycle',
            'value' => 'feature_value',
        ];

        if ($request->input('type') == 'cloudcart_service' &&
            $request->input('service_type') == 1 // ecosystem service
        ) {
            $rules += [
                'expert_service_id' => 'required|int',
            ];
        }

        $this->validate($request, $rules);

        if ($request->input('type') == 'cloudcart_feature' && $request->input('value')) {
            $item = OfferItem::where('offer_id', $request->input('offer_id'))
                ->where('model_id', $request->input('id'))
                ->where('model_type', $request->input('type'))
                ->where('value', $request->input('value'))
                ->first();
        } else {
            $item = OfferItem::where('offer_id', $request->input('offer_id'))
                ->where('model_id', $request->input('id'))
                ->where('model_type', $request->input('type'))
                ->first();
        }

        if ($item) {
            throw ValidationException::withMessages(['id' => 'This item already exists.']);
        }

        //        if ($request->input('type') == 'plan_details') {
        //            $item = OfferItem::where('offer_id', $request->input('offer_id'))
        //                ->where('model_type', $request->input('type'))
        //                ->first();
        //
        //            if ($item) {
        //                throw ValidationException::withMessages(['type' => 'This item type can be added only once.']);
        //            }
        //        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     * @throws \Exception
     */
    public function destroy($id)
    {
        /** @var OfferItem $item */
        $item = OfferItem::with('offer')->findOrFail($id);
        if (!auth()->user()->hasRole('super-admin') && $item->offer->owner_id != auth()->id()) {
            throw new HttpResponseException(new Response([
                'message' => 'Only the owner can change the offer!'
            ], 422));
        }

        $transaction = Transaction::where('offer_item_id', $item->getKey())->exists();

        if ($transaction) {
            throw new HttpResponseException(new Response([
                'message' => 'This item has transactions and can not be deleted!'
            ], 422));
        }

        $item->delete();

        ConsoleActivity::log(auth()->user(), 'delete', $item);

        return response('', 204);
    }
}
