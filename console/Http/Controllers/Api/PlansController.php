<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/23/2018
 * Time: 4:24 PM
 */

namespace Console\Http\Controllers\Api;

use App\Helper\Plan as PlanHelper;
use App\Models\Gate\CountryLimitations;
use App\Models\Gate\Plan;
use App\Models\Gate\UsersSites;
use App\Models\Oauth\ConsoleActivity;
use Cache;
use Console\Http\Resources\Resource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * Class PlansController
 * @package Console\Http\Controllers\Api
 */
class PlansController extends ResourceController
{
    /**
     * Instantiate a new controller instance.
     *
     */
    public function __construct()
    {
        $this->middleware('permission:list plans')->only('list');
        $this->middleware('permission:create plans')->only('store');
        $this->middleware('permission:update plans')->only('update');
    }

    /**
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $plans = Plan::with('country_limits')
            ->withCount('country_limits')
            ->orderBy('mapping')->get();
        //        $plans->push([
        //            'id' => 1,
        //            'mapping' => 'cc-demo',
        //            'name' => 'cc-demo',
        //        ]);

        $plans = $plans->sortBy('name');

        return $this->response(Resource::collection($plans));
    }

    /**
     * @return \Illuminate\Http\Response
     */
    public function total()
    {
        $plans = UsersSites::groupBy('plan')
            ->select('plan', DB::connection('gate')->raw('count(*) as total'))
            ->get();

        return $this->response(Resource::collection($plans));
    }

    public function list()
    {
        return $this->index();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $this->validateRequest($request);

        $plan = Plan::create($request->input());
        $plan->refresh();
        PlanHelper::clearCache();
        ConsoleActivity::log(auth()->user(), 'create', $plan);

        Cache::tags('gate-response-cache')->flush();

        $plan->load(['country_limits']);
        $plan->setAttribute('country_limits_count', $plan->country_limits->count());

        return $this->response(new Resource($plan));
    }

    /**
     * Display the specified resource.
     * @param $planId
     * @return \Illuminate\Http\Response
     */
    public function show($planId)
    {
        $plan = Plan::findOrFail($planId);

        return $this->response(new Resource($plan));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function update(Request $request, $id)
    {
        /** @var Plan $plan */
        $plan = Plan::findOrFail($id);

        $this->validateRequest($request);

        $plan->fill($request->only(
            'name',
            'mapping',
            'active'
        ));

        $plan->setAttribute('issuer_company_id', $request->input('issuer_company_id'));

        ConsoleActivity::log(auth()->user(), 'update', $plan);
        $plan->save();
        $plan->refresh();
        PlanHelper::clearCache();

        Cache::tags('gate-response-cache')->flush();

        $plan->load(['country_limits']);
        $plan->setAttribute('country_limits_count', $plan->country_limits->count());

        return $this->response(new Resource($plan));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     * @throws \Exception
     */
    public function limits(Request $request, $id)
    {
        $plan = Plan::findOrFail($id);

        $plan->country_limits()->delete();

        $limits = $request->input('country_limits', []);
        foreach ($limits as $limit) {
            if (empty($limit['country'])) {
                continue;
            }

            CountryLimitations::create($limit)->record()
                ->associate($plan)->save();
        }

        $plan->load(['country_limits']);
        $plan->setAttribute('country_limits_count', $plan->country_limits->count());

        return $this->response(new Resource($plan));
    }

    /**
     * @param Request $request
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateRequest(Request $request)
    {
        $id = (int)$request->input('id');

        $this->validate($request, [
            'mapping' => 'required|regex:/^[a-z0-9\-]+$/u|unique:gate.cc_gate.plans,mapping,' . $id,
            'name' => 'required',
        ], [
            'mapping.regex' => 'The :attribute may only contain lowercase letters, numbers, and hyphens (-).',
        ]);
    }
}
