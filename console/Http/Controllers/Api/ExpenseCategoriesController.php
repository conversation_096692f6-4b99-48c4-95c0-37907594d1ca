<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/23/2018
 * Time: 4:24 PM
 */

namespace Console\Http\Controllers\Api;

use App\Models\Gate\ExpenseCategory;
use App\Models\Oauth\ConsoleActivity;
use Console\Http\Resources\Resource;
use Illuminate\Http\Request;

/**
 * Class ExpenseCategoriesController
 * @package Console\Http\Controllers\Api
 */
class ExpenseCategoriesController extends ResourceController
{
    /**
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $collection = ExpenseCategory::with('parent')->withCount('expenses')->get();

        $collection->map->append([
            'expenses_total',
        ]);

        return $this->response(Resource::collection($collection));
    }

    /**
     * @return \Illuminate\Http\Response
     */
    public function parents()
    {
        $collection = ExpenseCategory::whereNull('parent_id')->get();

        return $this->response(Resource::collection($collection));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $this->validateRequest($request);

        $item = ExpenseCategory::create($request->only(['name', 'parent_id']));

        ConsoleActivity::log(auth()->user(), 'create', $item);

        return $this->response(new Resource($item));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function update(Request $request, $id)
    {
        /** @var ExpenseCategory $item */
        $item = ExpenseCategory::findOrFail($id);

        $this->validateRequest($request);

        $item->fill($request->only(['name', 'parent_id']));

        ConsoleActivity::log(auth()->user(), 'update', $item);
        $item->save();

        return $this->response(new Resource($item));
    }

    /**
     * @param Request $request
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateRequest(Request $request)
    {
        $id = (int)$request->input('id');

        $this->validate($request, [
            'name' => 'required|unique:gate.cc_gate.expense_categories,name,' . $id,
        ]);
    }
}
