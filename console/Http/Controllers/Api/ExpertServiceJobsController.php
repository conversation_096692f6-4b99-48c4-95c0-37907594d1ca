<?php

declare(strict_types=1);

namespace Console\Http\Controllers\Api;

use App\Helper\Mail\ElasticEmail;
use App\Jobs\SendSlackMessage;
use App\Mail\ExpertNewServiceJob;
use App\Models\Gate\ExpertService;
use App\Models\Gate\ExpertServiceJob;
use Carbon\Carbon;
use Console\Http\Resources\Resource;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

/**
 * Class ExpertServicesController
 * @package Console\Http\Controllers\Api
 */
class ExpertServiceJobsController extends ResourceController
{
    /**
     * @param null $expertId
     * @return \Illuminate\Http\Response
     */
    public function index($expertId = null)
    {
        $query = ExpertServiceJob::console();

        if ($expertId) {
            $query->where('expert_id', $expertId)
                ->with([
                    'expert',
                    'site',
                    'service',
                    'expertService',
                ]);
        } else {
            $query->with([
                'expert',
                'site',
                'service',
                'expertService',
            ]);
        }

        $collection = $query->get();

        return $this->response(Resource::collection($collection));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function reassign(Request $request)
    {
        $this->validate($request, [
            'job_id' => 'required',
            'expert_service_id' => 'required',
        ]);

        /** @var ExpertServiceJob $job */
        $job = ExpertServiceJob::console()
            ->findOrFail($request->input('job_id'));

        $service = ExpertService::console()
            ->findOrFail($request->input('expert_service_id'));

        $newJob = new ExpertServiceJob(Arr::except($job->getAttributes(), [
            'id',
            'created_at',
            'updated_at',
        ]));

        $newJob->fill([
            'expert_service_id' => $service->getKey(),
            'expert_id' => $service->expert_id,
            'status' => ExpertServiceJob::STATUS_PENDING,
        ])->save();

        $job->update(['reassigned_job_id' => $newJob->getKey()]);

        ElasticEmail::changeAccount('elastic_email_sub');
        \Illuminate\Support\Facades\Mail::to($service->expert->keyAccount)
            ->send(new ExpertNewServiceJob($service->expert->keyAccount, $newJob));

        return $this->response(new Resource($newJob));
    }

    /**
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function accept($id)
    {
        /** @var ExpertServiceJob $item */
        $item = ExpertServiceJob::console()
            ->with(['service', 'expert', 'site.ccUser'])
            ->findOrFail($id);

        $item->update([
            'status' => ExpertServiceJob::STATUS_ACCEPTED,
            'accepted_at' => now(),
        ]);

        if ($item->site->ccUser) {
            dispatch(new SendSlackMessage(
                $item->site->ccUser,
                'Expert ' . $item->expert->name . " ACCEPTED the job request for service: \n" .
                $item->service->name_translated . "\nExecution time: " . $item->execution_time .
                " days\nDeadline: " . $item->deadline->format('d.m.Y') .
                "\nCreated at: " . $item->created_at->format('d.m.Y H:i')
            ));

            dispatch(new SendSlackMessage(
                $item->site->ccUser,
                "Ecosystem service job execution time has passed: \nExpert: " . $item->expert->name .
                "\nService: " . $item->service->name_translated . "\nExecution time: " . $item->execution_time .
                " days\nDeadline: " . $item->deadline->format('d.m.Y') .
                "\nCreated at: " . $item->created_at->format('d.m.Y H:i')
            ))->delay($item->deadline);
        }

        return $this->response(new Resource($item));
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    public function postpone(Request $request)
    {
        /** @var ExpertServiceJob $item */
        $item = ExpertServiceJob::console()
            ->with(['service', 'expert', 'site.ccUser'])
            ->findOrFail($request->input('id'));

        $this->validate($request, [
            'to_date' => "required|date|after_or_equal:deadline",
        ]);

        $deadline = Carbon::parse($request->input('to_date'), 'UTC')->endOfDay();
        $diff = $deadline->diffInDays($item->created_at);
        $postpone_days = $diff > $item->execution_time ?
            $diff - $item->execution_time : null;

        if (!$postpone_days) {
            return $this->response(new Resource($item));
        }

        $item->update([
            'status' => ExpertServiceJob::STATUS_POSTPONE,
            'postpone_days' => $postpone_days,
        ]);

        if ($item->site->ccUser) {
            dispatch(new SendSlackMessage(
                $item->site->ccUser,
                'Expert ' . $item->expert->name . " POSTPONE the job request for service: \n" .
                $item->service->name_translated . "\nPostpone days: " . $item->postpone_days . ' days' .
                "\nDeadline: " . $item->deadline->format('d.m.Y') .
                "\nCreated at: " . $item->created_at->format('d.m.Y H:i')
            ));

            dispatch(new SendSlackMessage(
                $item->site->ccUser,
                "Ecosystem service job execution time has passed: \nExpert: " . $item->expert->name .
                "\nService: " . $item->service->name_translated . "\nExecution time: " . $item->execution_time .
                " days\nPostpone days: " . $item->postpone_days .
                " days\nDeadline: " . $item->deadline->format('d.m.Y') .
                "\nCreated at: " . $item->created_at->format('d.m.Y H:i')
            ))->delay($item->deadline);
        }

        return $this->response(new Resource($item));
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function done($id)
    {
        /** @var ExpertServiceJob $item */
        $item = ExpertServiceJob::console()
            ->with(['service', 'subscription', 'invoice', 'site.ccUser'])
            ->findOrFail($id);

        $item->subscription->update([
            'expert_service_id' => $item->expert_service_id,
            'expert_service_job_id' => $item->getKey(),
        ]);

        $item->invoice->createPayoutItemsFromExpertJob();

        $item->update([
            'status' => ExpertServiceJob::STATUS_DONE,
            'done_at' => now(),
        ]);

        if ($item->site->ccUser) {
            dispatch(new SendSlackMessage(
                $item->site->ccUser,
                'Expert ' . $item->expert->name . " COMPLETED the job request for service: \n" .
                $item->service->name_translated
            ));
        }

        return $this->response(new Resource($item));
    }

    /**
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function decline($id)
    {
        /** @var ExpertServiceJob $item */
        $item = ExpertServiceJob::console()
            ->with(['service', 'site.ccUser', 'expert'])
            ->findOrFail($id);

        $item->update(['status' => ExpertServiceJob::STATUS_DECLINED]);

        if ($item->site->ccUser) {
            dispatch(new SendSlackMessage(
                $item->site->ccUser,
                'Expert ' . $item->expert->name . ' DECLINED the job request for service: ' .
                $item->service->name_translated
            ));
        }

        return $this->response(new Resource($item));
    }
}
