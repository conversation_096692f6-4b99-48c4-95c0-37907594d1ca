<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/23/2018
 * Time: 4:24 PM
 */

namespace Console\Http\Controllers\Api;

use App\Models\Oauth\ConsoleActivity;
use App\Models\Oauth\UserDepartment;
use Console\Http\Resources\Resource;
use Illuminate\Http\Request;

/**
 * Class UserDepartmentsController
 * @package Console\Http\Controllers\Api
 */
class UserDepartmentsController extends ResourceController
{
    /**
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $collection = UserDepartment::orderBy('name')->get();

        return $this->response(Resource::collection($collection));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $this->validateRequest($request);

        $item = UserDepartment::create($request->only(['name']));

        ConsoleActivity::log(auth()->user(), 'create', $item);

        return $this->response(new Resource($item));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function update(Request $request, $id)
    {
        /** @var UserDepartment $item */
        $item = UserDepartment::findOrFail($id);

        $this->validateRequest($request);

        $item->fill($request->only(['name']));

        ConsoleActivity::log(auth()->user(), 'update', $item);
        $item->save();

        return $this->response(new Resource($item));
    }

    /**
     * @param Request $request
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateRequest(Request $request)
    {
        $id = (int)$request->input('id');

        $this->validate($request, [
            'name' => 'required|unique:oauth.cc_oauth.user_departments,name,' . $id,
        ]);
    }
}
