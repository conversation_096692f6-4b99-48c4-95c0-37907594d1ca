<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: evgen
 * Date: 4/17/2018
 * Time: 11:12 AM
 */

namespace Console\Http\Controllers\Api;

use App\Models\Gate\Discount;
use App\Models\Gate\DiscountItem;
use App\Models\Gate\Offer;
use App\Models\Gate\SiteSubscription;
use App\Models\Gate\Transaction;
use App\Models\Oauth\ConsoleActivity;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

/**
 * Class DiscountsController
 * @package Console\Http\Controllers\Api
 */
class DiscountsController extends ResourceController
{
    /**
     * Instantiate a new controller instance.
     *
     */
    public function __construct()
    {
        /** @var \App\Models\Oauth\User $user */
        $user = auth()->user();
        $role = $user ? $user->getConsoleRole() : null;
        if ($role == \App\Models\Oauth\User::CONSOLE_CLOUD_CART) {
            $this->middleware('permission:list subscriptions')->only('index');
        }

        $this->middleware('permission:create subscriptions')->only('store');
        $this->middleware('permission:update subscriptions')->only('update');
        $this->middleware('permission:delete subscriptions')->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $collection = Discount::get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    public function bundles()
    {
        $collection = Discount::where('discount_type', Discount::TYPE_BUNDLE)->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $item = Discount::findOrFail($id);

        return $this->response(new \Illuminate\Http\Resources\Json\JsonResource($item));
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function items($id)
    {
        $collection = DiscountItem::with(['model'])
            ->where('discount_id', $id)
            ->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function transactions($id)
    {
        $item = Discount::findOrFail($id);

        $collection = Transaction::with(['details', 'invoice'])
            ->where('discount_id', $item->getKey())
            ->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function subscriptions($id)
    {
        $item = Discount::findOrFail($id);

        $collection = SiteSubscription::with(['user', 'model', 'site.routerSite.primaryHost'])
            ->where('discount_id', $item->getKey())
            ->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * @param mixed $id
     * @return mixed
     */
    public function offers($id)
    {
        $item = Discount::findOrFail($id);

        $collection = Offer::with(['owner.googleAccount', 'user', 'site'])
            ->where('discount_id', $item->getKey())
            ->get();

        return $this->response(\Illuminate\Http\Resources\Json\JsonResource::collection($collection));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $this->validateRequest($request);

        $item = Discount::create(array_merge($request->only([
            'discount_type',
            'name',
            'code',
            'max_uses',
            'payment_method',
            'expire_at',
            'active',
            'value_type',
        ]), [
            'value' => $request->input('value_input') ? round($request->input('value_input') * 100) : null,
            'order_over' => $request->input('order_over_input') ? round($request->input('order_over_input') * 100) : null,
        ]));

        ConsoleActivity::log(auth()->user(), 'create', $item);
        $message = sprintf('Discount #%s was created successfully', $item->getKey());

        $item->refresh();
        $response = [
            'status' => 'success',
            'message' => $message,
            'data' => $item->toArray()
        ];

        return response()->json($response);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function update(Request $request, $id)
    {
        /** @var Discount $item */
        $item = Discount::findOrFail($id);

        $this->validateRequest($request);

        $item->fill(array_merge($request->only([
            'name',
            'code',
            'max_uses',
            'payment_method',
            'expire_at',
            'active',
            'value_type',
        ]), [
            'value' => $request->input('value_input') ? round($request->input('value_input') * 100) : null,
            'order_over' => $request->input('order_over_input') ? round($request->input('order_over_input') * 100) : null,
        ]));

        ConsoleActivity::log(auth()->user(), 'update', $item);
        $item->save();

        return $this->response(new \Illuminate\Http\Resources\Json\JsonResource($item));
    }

    /**
     * @param Request $request
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateRequest(Request $request)
    {
        $id = (int)$request->input('id');

        $this->validate($request, [
            'discount_type' => 'required',
            'name' => 'required|unique:gate.cc_gate.discounts,name,' . $id,
            'code' => 'required|unique:gate.cc_gate.discounts,code,' . $id,
            'value_type' => "required_if:discount_type,1",
            'value_input' => "required_if:discount_type,1",
            'order_over_input' => "required_if:discount_type,1",
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     * @throws \Exception
     */
    public function destroy($id)
    {
        /** @var Discount $item */
        $item = Discount::findOrFail($id);

        try {
            $item->delete();
        } catch (\Exception) {
            //            throw ValidationException::withMessages(['message' => 'This item is in use and cannot be deleted.']);
            throw new HttpResponseException(new Response([
                'message' => 'This item is in use and cannot be deleted!'
            ], 422));
        }

        ConsoleActivity::log(auth()->user(), 'delete', $item);

        return response('', 204);
    }
}
