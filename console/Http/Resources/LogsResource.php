<?php

declare(strict_types=1);

namespace Console\Http\Resources;

use App\Models\Oauth\ConsoleActivity;

class LogsResource extends \Illuminate\Http\Resources\Json\JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray(\Illuminate\Http\Request $request): array
    {
        $trace = $this->trace ? explode("\n", $this->trace) : null;

        /** @var $this ConsoleActivity */
        return array_merge(parent::toArray($request), [
            'trace' => $trace,
            'id' => $this->id,
        ]);
    }
}
