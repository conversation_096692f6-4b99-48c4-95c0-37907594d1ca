<?php

declare(strict_types=1);

namespace Console\Http\Resources;

use App\Models\Gate\Reseller;

class ExpertResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    #[\Override]
    public function toArray(\Illuminate\Http\Request $request): array
    {
        if ($this->resource instanceof Reseller && $this->resource->relationLoaded('operationCountries')) {
            $this->resource->setRelation(
                'operationCountries',
                $this->resource->operation_countries_codes
            );
        }

        return parent::toArray($request);
    }
}
