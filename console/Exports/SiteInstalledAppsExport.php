<?php

declare(strict_types=1);

namespace Console\Exports;

use Illuminate\Contracts\Support\Responsable;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Excel;

/**
 * Class SitesInstalledAppsExport
 * @package Console\Exports
 */
class SiteInstalledAppsExport implements FromCollection, WithHeadings, ShouldAutoSize, Responsable, WithTitle
{
    use Exportable;

    /**
     * It's required to define the fileName within
     * the export class when making use of Responsable.
     */
    private string $fileName = 'site-installed-apps.xls';

    /**
     * Optional Writer Type
     */
    private string $writerType = Excel::XLS;

    protected $rows;

    /**
     * InvoicesExport constructor.
     * @param Collection $items
     */
    public function __construct(protected \Illuminate\Support\Collection $items)
    {
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return $this->rows;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        $this->rows = $this->items->map(fn($item): array => [
            'site_id' => $item->site_id,
            'app_id' => $item->app ? $item->app->getKey() : null,
            'app_name' => $item->app ? $item->app->name : null,
            'app_mapping' => $item->mapping,
            'installed' => $item->installed,
            'active' => $item->active,
            'created_at' => $item->created_at,
            'updated_at' => $item->updated_at,
        ]);

        return array_keys($this->rows->first() ?? []);
    }

    public function title(): string
    {
        return 'Site Installed Apps ' . \Carbon\Carbon::now()->format('d.m.Y');
    }
}
