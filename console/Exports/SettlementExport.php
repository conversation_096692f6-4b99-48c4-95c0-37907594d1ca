<?php

declare(strict_types=1);

namespace Console\Exports;

use App\Models\Gate\BraintreeAccount;
use App\Models\Gate\Transaction;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Excel;

/**
 * Class SettlementExport
 * @package Console\Exports
 */
class SettlementExport implements FromCollection, WithHeadings, ShouldAutoSize, Responsable
{
    use Exportable;

    /**
     * It's required to define the fileName within
     * the export class when making use of Responsable.
     */
    private string $fileName = 'settlement-batch.xls';

    /**
     * Optional Writer Type
     */
    private string $writerType = Excel::XLS;

    /**
     * @var
     */
    protected $rows;

    /**
     * SettlementExport constructor.
     * @param Collection $items
     */
    public function __construct(protected \Illuminate\Support\Collection $items)
    {
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        return $this->rows;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        $this->rows = $this->items->map(function ($item): array {
            /** @var Transaction $item */
            $row = [
                'settled_at' => $item->settled_at->format('d.m.Y'),
                'created_at' => $item->created_at->format('d.m.Y H:i:s'),
                'transaction_id' => $item->reference_id,
                'description' => $item->description,
                'amount' => $item->amount / 100,
                'estimated_fee' => BraintreeAccount::calculateFee($item->amount) / 100,
                'currency' => $item->currency,
                'issuer_company_id' => $item->issuer_company_id,
                'invoice' => $item->invoice->number,
                'merchant' => implode(', ', [
                    $item->invoice->recipient->name,
                    $item->invoice->recipient->company,
                    $item->invoice->recipient->vat,
                ]),
                'address' => implode(', ', [
                    $item->invoice->recipient->country_name,
                    $item->invoice->recipient->city,
                    $item->invoice->recipient->address,
                ]),
            ];
            //dd($row);
            return $row;
        });

        return array_keys($this->rows->first() ?? []);
    }
}
