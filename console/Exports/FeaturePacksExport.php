<?php

declare(strict_types=1);

namespace Console\Exports;

use App\Models\Gate\PlanFeaturePack;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Excel;

/**
 * Class FeaturePacksExport
 * @package Console\Exports
 */
class FeaturePacksExport implements FromCollection, WithHeadings, ShouldAutoSize, Responsable
{
    use Exportable;

    /**
     * It's required to define the fileName within
     * the export class when making use of Responsable.
     */
    private string $fileName = 'plan_feature_packs.xls';

    /**
     * Optional Writer Type
     */
    private string $writerType = Excel::XLS;

    /**
     * InvoicesExport constructor.
     * @param Collection $items
     */
    public function __construct(protected \Illuminate\Support\Collection $items)
    {
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        $rows = $this->items->map(fn($item): array =>
            /** @var PlanFeaturePack $item */
            [
            'id' => $item->getKey(),
            'name' => $item->invoiceItemDescription(),
            'price' => $item->price_input,
            'currency' => $item->currency,
            'active' => (int)$item->active,
        ]);

        return $rows;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'id',
            'name',
            'price',
            'currency',
            'active',
        ];
    }
}
