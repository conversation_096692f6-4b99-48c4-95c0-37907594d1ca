<?php

declare(strict_types=1);

namespace Liquid\Exceptions;

use Exception;
use Liquid\LiquidCompiler;

class Error extends Exception
{
    /** @var string|null The template name (basename of path, or Source::getName()) */
    private readonly ?string $name;

    /** @var string|null The full path to the source file, if known */
    private ?string $sourcePath = null;

    /** @var string|null The source code, if retrievable */
    private ?string $sourceCode = null;

    /**
     * Constructor.
     *
     * @param string                               $rawMessage The error message
     * @param int                                  $lineno     The template line where the error occurred
     * @param LiquidCompiler|string|null   $source     Compiler, Source object, or file path string
     * @param Exception|null                       $previous   Previous exception
     * @param bool                                 $autoGuess  Unused (for backward compatibility)
     */
    public function __construct(
        private string $rawMessage,
        private readonly ?int $lineno = -1,
        LiquidCompiler|string|null $source = null,
        ?Exception $previous = null,
        bool $autoGuess = true
    ) {
        // We bypass the normal Exception constructor so we can build our own $this->message
        parent::__construct('', 0, $previous);

        // 1) If caller passed a plain string path
        if (is_string($source)) {
            $this->name       = basename($source);
            $this->sourcePath = $source;
            $this->sourceCode = null;

            // 2) If caller passed the compiler instance
        } elseif ($source instanceof LiquidCompiler) {
            $path             = $source->getPath() ?: '(inline template)';
            $this->name       = basename($path);
            $this->sourcePath = $path;
            // try to load its contents:
            try {
                $this->sourceCode = $source->getFileSource($path);
            } catch (\Throwable) {
                $this->sourceCode = null;
            }

            // 3) If caller passed your Source abstraction
        } elseif ($source instanceof Source) {
            $this->name       = $source->getName();
            $this->sourceCode = $source->getCode();
            $this->sourcePath = $source->getPath();

            // 4) Nothing or unknown
        } else {
            $this->name = null;
        }

        // Build the final Exception::$message,::$file,::$line
        $this->updateRepr();
    }

    /**
     * Append extra text onto the raw message and rebuild representation.
     */
    public function appendMessage(string $more): void
    {
        $this->rawMessage .= $more;
        $this->updateRepr();
    }

    /**
     * Return the original, un-prefixed message.
     */
    public function getRawMessage(): string
    {
        return $this->rawMessage;
    }

    /**
     * Return the template line number where the error occurred.
     */
    public function getTemplateLine(): int
    {
        return $this->lineno;
    }

    /**
     * Build $this->message, and set Exception::$file and::$line when possible.
     */
    private function updateRepr(): void
    {
        // Start with the raw message
        $this->message = $this->rawMessage;

        // If we have both path and explicit line, treat it as “real” exception location
        if ($this->sourcePath !== null && $this->lineno > 0) {
            $this->file = $this->sourcePath;
            $this->line = $this->lineno;
            return;
        }

        // Otherwise, decorate message with name and line if available
        $suffix = '';
        if ($this->name) {
            $suffix .= sprintf(' in "%s"', $this->name);
        }
        if ($this->lineno >= 0) {
            $suffix .= sprintf(' at line %d', $this->lineno);
        }
        if ($suffix !== '') {
            $this->message .= $suffix;
        }
    }
}
