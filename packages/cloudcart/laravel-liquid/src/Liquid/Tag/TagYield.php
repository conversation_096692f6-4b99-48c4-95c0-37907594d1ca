<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid\Tag;

use Liquid\AbstractTag;
use Liquid\Context;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;
use Liquid\Regexp;

/**
 * Shopify yield tag - outputs content from content_for areas
 * 
 * Example:
 * {% yield 'header' %}
 */
class TagYield extends AbstractTag
{
    /**
     * The name of the content area to yield
     *
     * @var string
     */
    protected string $contentName;

    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     * @param LiquidCompiler $compiler
     * @throws LiquidException
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        parent::__construct($markup, $tokens, $compiler);
        
        // Parse the content area name
        $syntax = new Regexp('/^(' . LiquidCompiler::QUOTED_FRAGMENT . ')$/');
        
        if ($syntax->match(trim($markup))) {
            $this->contentName = $syntax->matches[1];
            // Remove quotes if present
            $this->contentName = trim($this->contentName, '"\'');
        } else {
            throw new LiquidException("Syntax Error in 'yield' - Valid syntax: yield 'area_name'");
        }
    }

    /**
     * Renders the current node
     *
     * @param Context $context
     * @return string
     * @throws LiquidException
     */
    #[\Override]
    public function render(Context $context): string
    {
        try {
            // Get the content areas from context
            $contentAreas = $context->get('content_areas') ?: [];
            
            // Return the content for this area, or empty string if not found
            return $contentAreas[$this->contentName] ?? '';
            
        } catch (\Exception $e) {
            if ($context->get('strict_variables', false)) {
                throw new LiquidException("Yield tag error: " . $e->getMessage());
            }
            // Silently fail in non-strict mode
            return '';
        }
    }
}
