<?php

declare(strict_types=1);

/**
 * This file is part of the Liquid package.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @package Liquid
 */

namespace Liquid\Tag;

use Liquid\AbstractBlock;
use Liquid\Context;
use Liquid\LiquidCompiler;
use Liquid\LiquidException;

/**
 * Shopify JavaScript tag - allows embedding JavaScript code in sections/blocks
 * 
 * Example:
 * {% javascript %}
 *   console.log('Hello from Shopify section');
 *   // JavaScript code here
 * {% endjavascript %}
 */
class TagJavascript extends AbstractBlock
{
    /**
     * Constructor
     *
     * @param string $markup
     * @param array $tokens
     * @param LiquidCompiler $compiler
     * @throws LiquidException
     */
    public function __construct($markup, array &$tokens, LiquidCompiler $compiler)
    {
        parent::__construct($markup, $tokens, $compiler);
    }

    /**
     * Renders the current node
     *
     * @param Context $context
     * @return string
     * @throws LiquidException
     */
    #[\Override]
    public function render(Context $context): string
    {
        try {
            // Get the JavaScript content between {% javascript %} and {% endjavascript %}
            $jsContent = $this->getRawContent();
            
            // Clean up the content
            $jsContent = trim($jsContent);
            
            if (empty($jsContent)) {
                return '';
            }
            
            // Wrap in script tags for output
            $output = "<script>\n";
            $output .= $jsContent . "\n";
            $output .= "</script>";
            
            return $output;
            
        } catch (\Exception $e) {
            if ($context->get('strict_variables', false)) {
                throw new LiquidException("JavaScript tag error: " . $e->getMessage());
            }
            // Silently fail in non-strict mode
            return '';
        }
    }

    /**
     * Get raw content between javascript tags
     *
     * @return string
     */
    protected function getRawContent(): string
    {
        $content = '';
        foreach ($this->nodelist as $node) {
            if (is_string($node)) {
                $content .= $node;
            } else {
                // For non-string nodes, try to render them
                try {
                    $content .= $node->render(new Context());
                } catch (\Exception $e) {
                    // Skip problematic nodes
                    continue;
                }
            }
        }
        return $content;
    }
}
