---
description: Guidelines for Custom Request validation and form handling in CloudCart (site namespace with Smarty views)
globs:
alwaysApply: false
---
# CloudCart Custom Request Validation Rule

## Description

Guidelines for Custom Request validation and form handling in CloudCart. This rule covers the custom AbstractRequest class used primarily in the `site` namespace and `sitecp` namespace where Smarty templates are used for rendering views.

## Overview

CloudCart uses a sophisticated custom request validation system with the AbstractRequest base class, custom validators, and comprehensive error handling. This system is primarily used in the `site` namespace for storefront functionality and `sitecp` namespace where Smarty templates are used for rendering views.

## Request Architecture

### 1. Custom Request Base Class

CloudCart provides a custom AbstractRequest base class for request validation in traditional CloudCart applications:

#### AbstractRequest (Traditional CloudCart)

```php
<?php

declare(strict_types=1);

namespace App\Http\Request\Sitecp\YourDomain;

use App\Http\AbstractRequest;

class YourModelRequest extends AbstractRequest
{
    /**
     * Initialize custom validations
     */
    public function init(): void
    {
        // Register custom validators here
        $this->setCustomValidations();
    }
    
    /**
     * Define validation rules
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:191',
            'description' => 'nullable|string|max:65535',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
            'category_id' => 'required|exists:categories,id',
        ];
    }
    
    /**
     * Custom error messages
     */
    public function messages(): array
    {
        return [
            'name.required' => __('validation.name_required'),
            'name.max' => __('validation.name_max_chars', ['max' => 191]),
            'category_id.required' => __('validation.category_required'),
            'category_id.exists' => __('validation.category_not_found'),
        ];
    }
    
    /**
     * Custom attribute names
     */
    public function attributes(): array
    {
        return [
            'name' => __('labels.name'),
            'description' => __('labels.description'),
            'sort_order' => __('labels.sort_order'),
        ];
    }
}
```

#### AbstractFormRequest (Modern Laravel)

```php
<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Request\YourDomain;

use Modules\Core\Requests\AbstractFormRequest;

class YourModelRequest extends AbstractFormRequest
{
    /**
     * Define validation rules
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:191',
            'price' => 'required|numeric|min:0',
            'variants' => 'array',
            'variants.*.price' => 'required|numeric|min:0',
            'variants.*.sku' => 'required|string|unique:product_variants,sku',
            'images' => 'array|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,webp|max:10240',
        ];
    }
    
    /**
     * Custom error messages with placeholder support
     */
    #[\Override]
    public function messages(): array
    {
        return [
            'name.required' => 'Field is required',
            'name.min' => 'Field must be at least {min} characters',
            'name.max' => 'Field may not be greater than {max} characters',
            'price.required' => 'Price is required',
            'price.min' => 'Price must be at least 0',
            'variants.*.price.required' => 'Variant price is required',
            'variants.*.sku.unique' => 'SKU must be unique',
            'images.max' => 'Maximum 10 images allowed',
            'images.*.image' => 'File must be an image',
            'images.*.max' => 'Image size cannot exceed 10MB',
        ];
    }
    
    /**
     * Custom validation logic
     */
    public function withValidator(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        $validator->after(function ($validator) {
            if ($this->hasVariants() && !$this->hasValidVariants()) {
                $validator->errors()->add('variants', 'At least one variant is required');
            }
        });
    }
    
    /**
     * Prepare data for validation
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'price' => $this->convertPrice($this->input('price')),
            'status' => $this->input('status', 'active'),
        ]);
    }
    
    private function hasVariants(): bool
    {
        return $this->input('type') === 'variable';
    }
    
    private function hasValidVariants(): bool
    {
        return count($this->input('variants', [])) > 0;
    }
    
    private function convertPrice($price): float
    {
        return (float) str_replace(',', '.', (string) $price);
    }
}
```

## Validation Patterns

### 1. Basic Validation Rules

```php
public function rules(): array
{
    return [
        // Required fields
        'name' => 'required|string|max:191',
        'email' => 'required|email|max:191',
        
        // Numeric validations
        'price' => 'required|numeric|min:0|max:999999.99',
        'quantity' => 'nullable|integer|min:1|max:10000',
        'percentage' => 'nullable|numeric|min:0|max:100',
        
        // String validations
        'description' => 'nullable|string|max:65535',
        'slug' => 'required|string|max:191|regex:/^[a-z0-9-]+$/',
        
        // Enum validations
        'status' => 'required|in:active,inactive,pending',
        'type' => 'required|in:product,service,digital',
        
        // Date validations
        'start_date' => 'nullable|date',
        'end_date' => 'nullable|date|after_or_equal:start_date',
        
        // File validations
        'image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:10240',
        'document' => 'nullable|file|mimes:pdf,doc,docx|max:5120',
        
        // Array validations
        'categories' => 'required|array|min:1',
        'categories.*' => 'exists:categories,id',
        
        // Conditional validations
        'shipping_cost' => 'required_if:type,physical|numeric|min:0',
        'digital_file' => 'required_if:type,digital|file|mimes:zip,pdf',
        
        // Unique validations
        'sku' => 'required|string|unique:products,sku,' . $this->route('id'),
        'email' => 'required|email|unique:users,email,' . $this->user()?->id,
        
        // Exists validations
        'category_id' => 'required|exists:categories,id',
        'vendor_id' => 'nullable|exists:vendors,id',
    ];
}
```

### 2. Complex Validation Rules

```php
public function rules(): array
{
    return [
        // Nested array validation
        'variants' => 'array',
        'variants.*.name' => 'required|string|max:191',
        'variants.*.price' => 'required|numeric|min:0',
        'variants.*.sku' => 'required|string|unique:product_variants,sku',
        'variants.*.attributes' => 'array',
        'variants.*.attributes.*.name' => 'required|string',
        'variants.*.attributes.*.value' => 'required|string',
        
        // Dynamic field validation
        'options' => 'array',
        'options.*.type' => 'required|in:text,select,checkbox,radio',
        'options.*.name' => 'required|string|max:191',
        'options.*.required' => 'boolean',
        'options.*.values' => 'required_if:options.*.type,select,radio|array',
        'options.*.values.*' => 'string|max:191',
        
        // Conditional complex validation
        'shipping_zones' => 'required_if:shipping_enabled,true|array',
        'shipping_zones.*.zone_id' => 'required|exists:geo_zones,id',
        'shipping_zones.*.rates' => 'required|array|min:1',
        'shipping_zones.*.rates.*.weight_from' => 'required|numeric|min:0',
        'shipping_zones.*.rates.*.weight_to' => 'required|numeric|gt:shipping_zones.*.rates.*.weight_from',
        'shipping_zones.*.rates.*.price' => 'required|numeric|min:0',
    ];
}
```

### 3. Custom Validation Rules

CloudCart provides many custom validation rules:

```php
public function init(): void
{
    // Phone number validation
    Validation::extendImplicit('phone_number', function (string $attribute, $value, $parameters, \Illuminate\Contracts\Validation\Validator $validator) {
        $phoneUtil = PhoneNumberUtil::getInstance();
        $locale = !empty($parameters[0]) ? $this->input($parameters[0]) : null;
        
        try {
            $numberProto = $phoneUtil->parse($value, $locale);
            return $phoneUtil->isValidNumber($numberProto);
        } catch (Exception $e) {
            return false;
        }
    });
    
    // Currency amount validation
    Validation::extend('currency_amount', function ($attribute, $value, $parameters, $validator) {
        try {
            Validate::currencyAmount($value, $parameters[0] ?? 10);
            return true;
        } catch (Exception $e) {
            $validator->setCustomMessages([
                $attribute . '.currency_amount' => $e->getMessage()
            ]);
            return false;
        }
    });
    
    // EIK validation (Bulgarian company identifier)
    Validation::extend('eik_validation', function ($attribute, $value, $parameters, $validator) {
        try {
            return EIKValidator::validate($value);
        } catch (Exception $e) {
            return false;
        }
    });
    
    // Select2 to array conversion
    Validation::extendImplicit('select2_to_array', function ($attribute, $value, $parameters, \Illuminate\Contracts\Validation\Validator $validator) {
        if ($this->has($attribute) && !empty($value)) {
            $value = explode(',', $value);
            $value = array_filter($value);
            $value = array_unique($value);
            $this->inputSet($attribute, $value);
            
            $data = $validator->getData();
            data_set($data, $attribute, $value);
            $validator->setData($data);
        }
        
        return true;
    });
}
```

### 4. Plan-Based Validation

CloudCart includes plan-based validation for feature limits:

```php
public function withValidator(\Illuminate\Contracts\Validation\Validator $validator): void
{
    $validator->addExtension('validate_feature', function ($attribute, $value, $parameters, $validator): bool {
        $planFeature = PlanFeature::findByMap('cart_rules_total');
        $id = $this->route('id');
        
        if ($planFeature && is_numeric($value = Plan::featureValue('cart_rules_total'))) {
            if (CartRule::count() >= $value && !$id) {
                $validator->setCustomMessages([
                    $attribute . '.validate_feature' => 'You have reached the maximum number of cart rules'
                ]);
                return false;
            }
        }
        
        return true;
    });
    
    $validator->addExtension('feature_value', function (string $attribute, $value, $parameters, \Illuminate\Contracts\Validation\Validator $validator): bool {
        if (request()->input('type') != 'cloudcart_feature') {
            return true;
        }
        
        $item = PlanFeaturePack::find(request()->input('id'));
        
        if ($item && $item->dynamic_pricing && !$value) {
            $validator->setCustomMessages([
                $attribute . '.feature_value' => 'Value is required for features with dynamic pricing'
            ]);
            return false;
        }
        
        return true;
    });
}
```

## Error Handling

### 1. AbstractRequest Error Format

```php
protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
{
    $errors = [];
    foreach ($validator->messages()->getMessages() as $field => $error) {
        $errors[formatField($field)] = is_array($error) ? [\Illuminate\Support\Arr::first($error)] : [$error];
    }
    
    throw new Errors($errors);
}
```

### 2. AbstractFormRequest Error Format

```php
protected function failedValidation(Validator $validator)
{
    $messages = $validator->getMessageBag()->getMessages();
    $rules = $validator->getRules();
    
    $newMessages = [];
    foreach ($messages as $field => $fieldMessage) {
        $fieldRules = $rules[$field] ?? [];
        
        $properties = [];
        foreach ($fieldRules as $rule) {
            $parameters = $this->parseRule($rule);
            $properties = array_merge($properties, $parameters);
        }
        
        foreach ($fieldMessage as $messageIndex => $message) {
            if ($formatted = $this->replaceMessageData($message, $properties)) {
                $newMessages[$field][$messageIndex] = $formatted;
            } else {
                $newMessages[$field][$messageIndex] = $message;
            }
        }
    }
    
    throw new ValidationException($validator, response()->json([
        'errors' => $newMessages,
    ], 422));
}
```

### 3. Custom Error Messages with Placeholders

```php
public function messages(): array
{
    return [
        'name.required' => 'Field is required',
        'name.min' => 'Field must be at least {min} characters',
        'name.max' => 'Field may not be greater than {max} characters',
        'price.min' => 'Price must be at least {min}',
        'price.max' => 'Price may not be greater than {max}',
        'quantity.between' => 'Quantity must be between {min} and {max}',
        'email.unique' => 'This email is already taken',
        'sku.unique' => 'This SKU is already in use',
    ];
}
```

## Advanced Patterns

### 1. Inheritance and Abstract Requests

```php
<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Request\Discounts;

use Modules\Core\Requests\AbstractFormRequest;
use Modules\Core\Core\Rules\CustomRegexRule;

abstract class AbstractDiscountRequest extends AbstractFormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'max:191'],
            'date_start' => 'nullable|date',
            'date_end' => 'nullable|date|after_or_equal:date_start',
            'code_format' => 'in:ean13,ean8',
            'max_uses' => 'nullable|integer|max:100000',
            'code_discount' => 'in:1,0',
            'code' => ['unique:discounts,code', 'max:20', new CustomRegexRule('/^[a-z0-9\#\.]+$/i')],
            'maxused_user' => 'nullable|integer|max:100000',
            'products' => 'required_if:settings,product|array',
            'products.*' => 'exists:products,id',
            'selections' => 'required_if:settings,selection|array',
            'selections.*' => 'exists:product_selections,id',
            'product_categories' => 'required_if:settings,product_category|array',
            'product_categories.*' => 'exists:type__products_categories,id',
            'vendors' => 'required_if:settings,product_vendor|array',
            'vendors.*' => 'exists:type__products_vendors,id',
            'settings' => 'in:all,order_over,product,product_category,product_vendor,selection,category_vendor',
            'customer_groups' => 'array|exists:type__customer_groups,id',
            'type' => 'in:flat,percent,shipping,fixed,quantity|validate_type',
            'type_value' => 'required_if:type,flat,percent|numeric|validate_value',
            'order_over' => 'required_if:settings,order_over|numeric',
            'geo_zone_id' => 'exists:geo_zones,id',
        ];
    }
    
    public function messages(): array
    {
        return [
            'name.required' => 'Discount name is required',
            'name.max' => 'Discount name may not be greater than 191 characters',
            'date_start.date' => 'Start date must be a valid date',
            'date_end.date' => 'End date must be a valid date',
            'date_end.after_or_equal' => 'End date must be after or equal to start date',
            'code.unique' => 'This discount code is already in use',
            'code.max' => 'Discount code may not be greater than 20 characters',
            'products.required_if' => 'Products are required when settings is product',
            'products.array' => 'Products must be an array',
            'products.*.exists' => 'Selected product does not exist',
            'type.in' => 'Type must be one of: flat, percent, shipping, fixed, quantity',
            'type_value.required_if' => 'Type value is required for flat and percent discounts',
            'type_value.numeric' => 'Type value must be a number',
        ];
    }
}

// Concrete implementation
class PercentDiscountRequest extends AbstractDiscountRequest
{
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'type' => 'required|in:percent',
            'type_value' => 'required|numeric|min:1|max:100',
        ]);
    }
    
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'type_value.min' => 'Percentage must be at least 1%',
            'type_value.max' => 'Percentage cannot exceed 100%',
        ]);
    }
}
```

### 2. Shipping Request Pattern

```php
<?php

declare(strict_types=1);

namespace App\Http\Request\Sitecp\Shipping;

use App\Http\AbstractRequest;
use Modules\Apps\Shippings\Omniship\Traits\OverlappedRanges;

abstract class AbstractShippingRequest extends AbstractRequest
{
    use OverlappedRanges;
    
    protected $_rates = [];
    protected $_category_rates = [];
    protected $_provider_key = 'provider';
    protected $_rates_key;
    protected $_support = ['address'];
    
    public function rules(): array
    {
        return [
            $this->_provider_key . '.provider_name' => 'required|max:191',
            'payments_providers' => 'required_without:payments_all',
            $this->_provider_key . '.geo_zone_id' => 'required_unless:' . $this->_provider_key . '.target,restofworld|exists:geo_zones,id',
            $this->_provider_key . '.target' => 'required|in:restofworld,regions',
            $this->_provider_key . '.pricing_address' => 'required_if:' . $this->_provider_key . '.to_address,1',
            $this->_provider_key . '.pricing_office' => 'required_if:' . $this->_provider_key . '.to_office,1',
            $this->_provider_key . '.pricing_locker' => 'required_if:' . $this->_provider_key . '.to_locker,1',
        ];
    }
    
    public function messages(): array
    {
        return [
            $this->_provider_key . '.provider_name.required' => 'Provider name is required',
            $this->_provider_key . '.provider_name.max' => 'Provider name may not exceed 191 characters',
            'payments_providers.required_without' => 'Payment providers are required',
            $this->_provider_key . '.geo_zone_id.required_unless' => 'Geographic zone is required for regional targeting',
            $this->_provider_key . '.geo_zone_id.exists' => 'Selected geographic zone does not exist',
            $this->_provider_key . '.target.required' => 'Target is required',
            $this->_provider_key . '.target.in' => 'Target must be either restofworld or regions',
        ];
    }
}

// Concrete shipping provider implementation
class DPDShippingRequest extends AbstractShippingRequest
{
    protected $_provider_key = 'dpd';
    protected $_support = ['address', 'office'];
    
    public function rules(): array
    {
        return array_merge(parent::rules(), [
            'dpd.username' => 'required|string',
            'dpd.password' => 'required|string',
            'dpd.sender_name' => 'required|string|max:191',
            'dpd.sender_phone' => 'required|phone_number_global_vue',
            'dpd.sender_address' => 'required|string|max:255',
            'dpd.default_weight' => 'required|numeric|min:0.1|max:1000',
        ]);
    }
    
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'dpd.username.required' => 'DPD username is required',
            'dpd.password.required' => 'DPD password is required',
            'dpd.sender_name.required' => 'Sender name is required',
            'dpd.sender_phone.required' => 'Sender phone is required',
            'dpd.sender_phone.phone_number_global_vue' => 'Sender phone must be a valid phone number',
            'dpd.sender_address.required' => 'Sender address is required',
            'dpd.default_weight.required' => 'Default weight is required',
            'dpd.default_weight.min' => 'Default weight must be at least 0.1 kg',
            'dpd.default_weight.max' => 'Default weight cannot exceed 1000 kg',
        ]);
    }
}
```

### 3. Dynamic Validation Rules

```php
<?php

declare(strict_types=1);

namespace Modules\Marketing\CartRules\Http\Requests;

use Modules\Core\Requests\AbstractFormRequest;
use Modules\Marketing\CartRules\Http\Requests\Rules\Value;
use Modules\Marketing\CartRules\Http\Requests\Rules\ValueType;

class RuleRequest extends AbstractFormRequest
{
    public function rules(): array
    {
        $maxRows = Plan::featureValue('cart_rules_range') ?: 20;
        
        $rules = [
            'name' => 'required|min:1|max:191|validate_feature',
            'title' => 'nullable|max:191',
            'active_from' => 'nullable|date:' . DateTimeFormat::getSiteCurrentDateFormat()['format'],
            'active_to' => 'nullable|date:' . DateTimeFormat::getSiteCurrentDateFormat()['format'] . '|after_or_equal:active_from',
            'rows' => 'array|required|min:1|max:' . intval($maxRows),
            'rows.*.action.value_type' => 'required',
            'rows.*.action.value' => 'required_if:rows.*.action.value_type,amount,percent',
        ];
        
        // Add dynamic validation rules for each row
        foreach ($this->input('rows', []) as $rowIndex => $row) {
            $conditionType = $row['condition_type'] ?? null;
            $filterType = $row['filter_type'] ?? null;
            $valueType = $row['value_type'] ?? null;
            
            if ($conditionType && $filterType && $valueType) {
                $rules["rows.{$rowIndex}.value"] = [
                    new Value($conditionType, $filterType, $valueType, $rowIndex)
                ];
                
                $rules["rows.{$rowIndex}.value_type"] = [
                    new ValueType($conditionType, $filterType, $rowIndex)
                ];
            }
        }
        
        return $rules;
    }
    
    public function messages(): array
    {
        return [
            'name.required' => 'Field is required',
            'name.min' => 'Field must be at least {min} characters',
            'name.max' => 'Field may not be greater than {max} characters',
            'name.validate_feature' => 'You have reached the maximum number of cart rules',
            'rows.required' => 'You must have at least one row with conditions',
            'rows.min' => 'You must have at least one row with conditions',
            'rows.max' => 'You may have a maximum of {max} rows',
        ];
    }
}
```

### 4. File Upload Validation

```php
public function rules(): array
{
    return [
        // Single file upload
        'avatar' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:2048',
        'document' => 'nullable|file|mimes:pdf,doc,docx,txt|max:5120',
        
        // Multiple file uploads
        'images' => 'array|max:10',
        'images.*' => 'image|mimes:jpeg,png,jpg,webp|max:10240',
        
        // Conditional file uploads
        'digital_file' => 'required_if:type,digital|file|mimes:zip,pdf,epub|max:51200',
        'product_manual' => 'nullable|file|mimes:pdf|max:10240',
        
        // File with custom validation
        'import_file' => [
            'required',
            'file',
            'mimes:csv,xlsx',
            'max:10240',
            function ($attribute, $value, $fail) {
                if ($value && $value->getSize() === 0) {
                    $fail('The file cannot be empty.');
                }
            }
        ],
    ];
}

public function messages(): array
{
    return [
        'avatar.image' => 'Avatar must be an image file',
        'avatar.mimes' => 'Avatar must be a JPEG, PNG, JPG, or WebP file',
        'avatar.max' => 'Avatar size cannot exceed 2MB',
        'images.max' => 'You can upload a maximum of 10 images',
        'images.*.image' => 'Each file must be an image',
        'images.*.mimes' => 'Images must be JPEG, PNG, JPG, or WebP files',
        'images.*.max' => 'Each image cannot exceed 10MB',
        'digital_file.required_if' => 'Digital file is required for digital products',
        'import_file.mimes' => 'Import file must be CSV or Excel format',
    ];
}
```

## Testing Request Validation

### 1. Unit Testing Validation Rules

```php
<?php

declare(strict_types=1);

namespace Tests\Unit\Http\Requests;

use Tests\TestCase;
use App\Http\Request\Sitecp\Products\ProductRequest;
use Illuminate\Support\Facades\Validator;

class ProductRequestTest extends TestCase
{
    public function test_validates_required_fields(): void
    {
        $request = new ProductRequest();
        $validator = Validator::make([], $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
        $this->assertArrayHasKey('price', $validator->errors()->toArray());
    }
    
    public function test_validates_field_lengths(): void
    {
        $request = new ProductRequest();
        $data = [
            'name' => str_repeat('a', 192), // Too long
            'price' => 99.99,
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('name', $validator->errors()->toArray());
    }
    
    public function test_validates_numeric_fields(): void
    {
        $request = new ProductRequest();
        $data = [
            'name' => 'Test Product',
            'price' => 'invalid-price',
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('price', $validator->errors()->toArray());
    }
    
    public function test_passes_with_valid_data(): void
    {
        $request = new ProductRequest();
        $data = [
            'name' => 'Test Product',
            'price' => 99.99,
            'description' => 'Test description',
            'status' => 'active',
        ];
        
        $validator = Validator::make($data, $request->rules());
        
        $this->assertFalse($validator->fails());
    }
}
```

### 2. Feature Testing Request Validation

```php
<?php

declare(strict_types=1);

namespace Tests\Feature\Http\Requests;

use Tests\TestCase;
use App\Models\User;
use App\Models\Product\Category;

class ProductControllerValidationTest extends TestCase
{
    public function test_product_creation_validates_required_fields(): void
    {
        $this->actingAs(User::factory()->create());
        
        $response = $this->postJson(route('admin.products.store'), []);
        
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'price', 'category_id']);
    }
    
    public function test_product_creation_validates_field_types(): void
    {
        $this->actingAs(User::factory()->create());
        
        $response = $this->postJson(route('admin.products.store'), [
            'name' => 123, // Should be string
            'price' => 'invalid', // Should be numeric
            'category_id' => 'invalid', // Should be integer
        ]);
        
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'price', 'category_id']);
    }
    
    public function test_product_creation_validates_relationships(): void
    {
        $this->actingAs(User::factory()->create());
        
        $response = $this->postJson(route('admin.products.store'), [
            'name' => 'Test Product',
            'price' => 99.99,
            'category_id' => 99999, // Non-existent category
        ]);
        
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['category_id']);
    }
    
    public function test_product_creation_succeeds_with_valid_data(): void
    {
        $this->actingAs(User::factory()->create());
        $category = Category::factory()->create();
        
        $response = $this->postJson(route('admin.products.store'), [
            'name' => 'Test Product',
            'price' => 99.99,
            'category_id' => $category->id,
            'description' => 'Test description',
        ]);
        
        $response->assertStatus(201);
        $this->assertDatabaseHas('products', [
            'name' => 'Test Product',
            'price' => 9999, // Stored in cents
        ]);
    }
}
```

## Best Practices Summary

### ✅ **Request Structure**
- Extend `App\Http\AbstractRequest` for traditional CloudCart requests
- Extend `Modules\Core\Requests\AbstractFormRequest` for modern Laravel requests
- Implement `rules()`, `messages()`, and `attributes()` methods
- Use `#[\Override]` attribute for overridden methods

### ✅ **Validation Rules**
- Use Laravel's built-in validation rules when possible
- Create custom validation rules for complex business logic
- Group related validation rules logically
- Use rule arrays for complex validations

### ✅ **Error Messages**
- Use translation strings with `__()` helper
- Follow the pattern: `__('namespace.error.field.rule')`
- Provide specific, actionable error messages
- Use placeholders for dynamic content in messages

### ✅ **Custom Validation**
- Register custom validators in the `init()` method
- Use `Validation::extendImplicit()` for complex validations
- Set custom messages with `$validator->setCustomMessages()`
- Return boolean from custom validators

### ✅ **Security Considerations**
- Validate all user input without exception
- Use strict type checking (`in:` validator for enums)
- Sanitize inputs that will be used in database queries
- Validate file uploads with size and type restrictions

### ✅ **Performance**
- Use conditional validation to avoid unnecessary checks
- Implement efficient custom validators
- Cache validation results when appropriate
- Use database exists checks sparingly

This comprehensive rule provides developers with everything they need to implement robust request validation in CloudCart, following established patterns and best practices.