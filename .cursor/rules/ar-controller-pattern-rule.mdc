---
description: This rule provides comprehensive guidelines for implementing controllers in CloudCart's multi-tenant e-commerce platform. It covers namespace-based architecture (admin panels, APIs, storefront), request validation patterns, response formatting, multi-tenant operations, plan restrictions, error handling, and performance optimization. Use this rule when creating or modifying any controller to ensure consistency with CloudCart's established patterns and best practices.
globs: 
alwaysApply: false
---
# CloudCart Controller Pattern Rule

## Description

This rule provides comprehensive guidelines for implementing controllers in CloudCart's multi-tenant e-commerce platform. It covers controller patterns, request validation, response formatting, multi-tenant operations, plan restrictions, error handling, and performance optimization. For namespace architecture details, see `@ar-namespace-rule.mdc`. Use this rule when creating or modifying any controller to ensure consistency with CloudCart's established patterns and best practices.

## Overview

CloudCart follows a namespace-based controller architecture with different patterns for web admin panels, API endpoints, and storefront functionality. Controllers are organized by namespace (see `@ar-namespace-rule.mdc`) and follow specific patterns for multi-tenant operations, validation, and response formatting.

## Controller Architecture

### 1. Namespace-Based Organization

CloudCart organizes controllers by namespace for different execution contexts. For detailed namespace architecture, see `@ar-namespace-rule.mdc`.

```php
// Admin Panel Controllers
namespace App\Http\Controllers\Sitecp;

// API Controllers  
namespace Api\Http\Controllers;

// Storefront Controllers
namespace App\Http\Controllers\Site;

// Uptime Monitoring Controllers
namespace App\Http\Controllers\Uptime;
```

### 2. Base Controller Classes

#### Main Application Controller

```php
<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Exceptions\MethodNotFound;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Traits\Localizable;

class Controller extends BaseController
{
    use AuthorizesRequests;
    use DispatchesJobs;
    use Localizable;
    use ValidatesRequests;
    
    /**
     * Custom validation with locale support
     */
    public function validate(Request $request, array $rules, array $messages = [], array $customAttributes = []): array
    {
        return $this->withLocale('en', fn() => $this->baseValidate($request, $rules, $messages, $customAttributes));
    }
    
    /**
     * Handle missing methods
     */
    public function __missingMethod(): never
    {
        throw new MethodNotFound();
    }
}
```

#### API Controller

```php
<?php

declare(strict_types=1);

namespace Api\Http\Controllers;

use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class Controller extends BaseController
{
    use AuthorizesRequests;
    use DispatchesJobs;
    use ValidatesRequests;
}
```

## Controller Structure Best Practices

### 1. Complete Controller Template

```php
<?php

declare(strict_types=1);

namespace App\Http\Controllers\Sitecp;

use App\Http\Controllers\Controller;
use App\Http\Request\Sitecp\YourModelRequest;
use App\Models\YourDomain\YourModel;
use App\Helper\Grid;
use App\Helper\SiteCp\Search;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Throwable;

/**
 * Class YourModelController
 * @package App\Http\Controllers\Sitecp
 */
class YourModelController extends Controller
{
    protected $records_exist;
    protected $search_object;
    
    /**
     * Display a listing of the resource
     */
    public function index(Request $request): Response|View
    {
        $this->records_exist = YourModel::count();
        $this->search_object = new Search(YourModelFilter::class);
        
        return $request->ajax() ? $this->_grid() : $this->_init();
    }
    
    /**
     * Show the form for creating a new resource
     */
    public function add(): Response
    {
        $data = [
            'related_data' => $this->getRelatedData(),
            'config' => $this->getConfig(),
        ];
        
        return \Illuminate\Support\Facades\View::mainResponse('your-model.add', $data);
    }
    
    /**
     * Store a newly created resource
     */
    public function save(YourModelRequest $request): Response
    {
        try {
            $model = \Illuminate\Support\Facades\DB::transaction(function () use ($request) {
                return YourModel::create($request->validated());
            });
            
            return response()->json([
                'success' => true,
                'message' => __('messages.created_successfully'),
                'data' => $model->toArray()
            ]);
            
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }
    
    /**
     * Display the specified resource
     */
    public function show(YourModel $model): Response|View
    {
        return \Illuminate\Support\Facades\View::mainResponse('your-model.show', [
            'model' => $model
        ]);
    }
    
    /**
     * Show the form for editing the specified resource
     */
    public function edit(YourModel $model): Response
    {
        $data = [
            'model' => $model,
            'related_data' => $this->getRelatedData(),
        ];
        
        return \Illuminate\Support\Facades\View::mainResponse('your-model.edit', $data);
    }
    
    /**
     * Update the specified resource
     */
    public function update(YourModelRequest $request, YourModel $model): Response
    {
        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($request, $model) {
                $model->update($request->validated());
            });
            
            return response()->json([
                'success' => true,
                'message' => __('messages.updated_successfully')
            ]);
            
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }
    
    /**
     * Remove the specified resource
     */
    public function destroy(YourModel $model): JsonResponse
    {
        try {
            $model->delete();
            
            return response()->json([
                'success' => true,
                'message' => __('messages.deleted_successfully')
            ]);
            
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 422);
        }
    }
    
    /**
     * Grid data for AJAX requests
     */
    protected function _grid(): Response
    {
        $grid = new Grid();
        // Grid configuration
        return $grid->render();
    }
    
    /**
     * Initial page load
     */
    protected function _init(): View
    {
        return \Illuminate\Support\Facades\View::mainResponse('your-model.index');
    }
    
    /**
     * Get related data for forms
     */
    protected function getRelatedData(): array
    {
        return [
            // Related model data
        ];
    }
    
    /**
     * Get configuration data
     */
    protected function getConfig(): array
    {
        return [
            // Configuration data
        ];
    }
}
```

### 2. API Controller Template

```php
<?php

declare(strict_types=1);

namespace Api\Http\Controllers;

use Api\Http\Resources\YourModel\YourModelCollection;
use Api\Http\Resources\YourModel\YourModelResource;
use Api\Models\YourModel;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

class YourModelController extends Controller
{
    /**
     * List resources with filtering and pagination
     * 
     * @queryParam include What to be included in response. Example: category,vendor
     * @queryParam filter Filter resources. Example: {"name": "search term"}
     * @queryParam sort Sort by fields. Example: name,-created_at
     * 
     * @responseFile /../api2/resources/apidoc/your-model/index.json
     */
    public function index(Request $request): YourModelCollection
    {
        $query = YourModel::query();
        
        $models = QueryBuilder::for($query)
            ->allowedIncludes(YourModelResource::$allowedIncludes)
            ->allowedFilters(['name', 'status'])
            ->allowedSorts(['name', 'created_at'])
            ->allowedAppends([])
            ->jsonPaginate()
            ->appends($request->query());
            
        return new YourModelCollection($models);
    }
    
    /**
     * Store a newly created resource
     */
    public function store(Request $request): YourModelResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:191',
            'description' => 'nullable|string',
        ]);
        
        $model = YourModel::create($validated);
        
        return new YourModelResource($model);
    }
    
    /**
     * Display the specified resource
     */
    public function show(YourModel $model): YourModelResource
    {
        return new YourModelResource($model);
    }
    
    /**
     * Update the specified resource
     */
    public function update(Request $request, YourModel $model): YourModelResource
    {
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:191',
            'description' => 'nullable|string',
        ]);
        
        $model->update($validated);
        
        return new YourModelResource($model);
    }
    
    /**
     * Remove the specified resource
     */
    public function destroy(YourModel $model): JsonResponse
    {
        $model->delete();
        
        return response()->json(['message' => 'Resource deleted successfully']);
    }
}
```

## Request Handling and Validation

### 1. Form Request Classes

CloudCart uses custom form request classes for validation:

```php
<?php

declare(strict_types=1);

namespace App\Http\Request\Sitecp\YourDomain;

use App\Http\AbstractRequest;

class YourModelRequest extends AbstractRequest
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:191',
            'description' => 'nullable|string|max:65535',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
            'category_id' => 'required|exists:categories,id',
        ];
    }
    
    public function messages(): array
    {
        return [
            'name.required' => __('validation.name_required'),
            'name.max' => __('validation.name_max_chars', ['max' => 191]),
            'category_id.required' => __('validation.category_required'),
            'category_id.exists' => __('validation.category_not_found'),
        ];
    }
    
    public function attributes(): array
    {
        return [
            'name' => __('labels.name'),
            'description' => __('labels.description'),
            'sort_order' => __('labels.sort_order'),
        ];
    }
}
```

### 2. Advanced Form Request with Custom Validation

```php
<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Request\Products;

use Modules\Core\Requests\AbstractFormRequest;

class ProductRequest extends AbstractFormRequest
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:191',
            'price' => 'required|numeric|min:0',
            'variants' => 'array',
            'variants.*.price' => 'required|numeric|min:0',
            'variants.*.sku' => 'required|string|unique:product_variants,sku',
            'images' => 'array|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,webp|max:10240',
        ];
    }
    
    public function messages(): array
    {
        return [
            'name.required' => 'Product name is required',
            'price.required' => 'Price is required',
            'price.min' => 'Price must be at least 0',
            'variants.*.price.required' => 'Variant price is required',
            'variants.*.sku.unique' => 'SKU must be unique',
            'images.max' => 'Maximum 10 images allowed',
            'images.*.image' => 'File must be an image',
            'images.*.max' => 'Image size cannot exceed 10MB',
        ];
    }
    
    /**
     * Custom validation logic
     */
    public function withValidator(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        $validator->after(function ($validator) {
            if ($this->hasVariants() && !$this->hasValidVariants()) {
                $validator->errors()->add('variants', 'At least one variant is required');
            }
        });
    }
    
    /**
     * Prepare data for validation
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'price' => $this->convertPrice($this->input('price')),
            'status' => $this->input('status', 'active'),
        ]);
    }
    
    private function hasVariants(): bool
    {
        return $this->input('type') === 'variable';
    }
    
    private function hasValidVariants(): bool
    {
        return count($this->input('variants', [])) > 0;
    }
    
    private function convertPrice($price): float
    {
        return (float) str_replace(',', '.', (string) $price);
    }
}
```

### 3. Abstract Form Request Base Class

```php
<?php

declare(strict_types=1);

namespace Modules\Core\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

abstract class AbstractFormRequest extends FormRequest
{
    /**
     * Handle failed validation with custom response format
     */
    protected function failedValidation(Validator $validator)
    {
        $messages = $validator->getMessageBag()->getMessages();
        $rules = $validator->getRules();
        
        $newMessages = [];
        foreach ($messages as $field => $fieldMessage) {
            $fieldRules = $rules[$field] ?? [];
            
            $properties = [];
            foreach ($fieldRules as $rule) {
                $parameters = $this->parseRule($rule);
                $properties = array_merge($properties, $parameters);
            }
            
            foreach ($fieldMessage as $messageIndex => $message) {
                if ($formatted = $this->replaceMessageData($message, $properties)) {
                    $newMessages[$field][$messageIndex] = $formatted;
                } else {
                    $newMessages[$field][$messageIndex] = $message;
                }
            }
        }
        
        throw new ValidationException($validator, response()->json([
            'errors' => $newMessages,
        ], 422));
    }
    
    /**
     * Replace message placeholders with actual data
     */
    protected function replaceMessageData($message, $properties): ?array
    {
        if (is_string($message) && preg_match_all('#\{(\w+)\}#', $message, $match)) {
            return [
                'label' => $message, 
                'properties' => collect($properties)->only($match[1])->all()
            ];
        }
        
        return null;
    }
}
```

## Response Formatting

### 1. Admin Panel Responses

```php
// View Response for initial page load
public function index(Request $request): Response|View
{
    return $request->ajax() ? $this->_grid() : $this->_init();
}

// JSON Response for AJAX operations
public function save(ProductRequest $request): Response
{
    try {
        $product = Product::create($request->validated());
        
        return response()->json([
            'success' => true,
            'message' => __('messages.product_created'),
            'data' => $product->toArray(),
            'redirect' => route('admin.products.edit', $product->id)
        ]);
        
    } catch (Throwable $e) {
        return response()->json([
            'success' => false,
            'message' => $e->getMessage(),
            'errors' => $this->formatErrors($e)
        ], 422);
    }
}

// Grid Response for data tables
protected function _grid(): Response
{
    $grid = new Grid();
    $grid->setModel(Product::class);
    $grid->setColumns([
        'id' => ['title' => 'ID', 'width' => 80],
        'name' => ['title' => 'Name', 'searchable' => true],
        'price' => ['title' => 'Price', 'formatter' => 'currency'],
        'status' => ['title' => 'Status', 'formatter' => 'status'],
    ]);
    
    return $grid->render();
}
```

### 2. API Responses

```php
// Resource Collections for listings
public function index(Request $request): ProductCollection
{
    $products = QueryBuilder::for(Product::query())
        ->allowedIncludes(['category', 'variants', 'images'])
        ->allowedFilters(['name', 'status', 'category_id'])
        ->allowedSorts(['name', 'price', 'created_at'])
        ->jsonPaginate()
        ->appends($request->query());
        
    return new ProductCollection($products);
}

// Resource for single items
public function show(Product $product): ProductResource
{
    return new ProductResource($product);
}

// Standard JSON responses
public function destroy(Product $product): JsonResponse
{
    $product->delete();
    
    return response()->json([
        'message' => 'Product deleted successfully'
    ], 200);
}
```

### 3. Error Response Formatting

```php
/**
 * Format error responses consistently
 */
protected function errorResponse(string $message, array $errors = [], int $code = 422): JsonResponse
{
    return response()->json([
        'success' => false,
        'message' => $message,
        'errors' => $errors,
        'code' => $code
    ], $code);
}

/**
 * Format validation errors
 */
protected function validationErrorResponse(ValidationException $e): JsonResponse
{
    return response()->json([
        'success' => false,
        'message' => 'Validation failed',
        'errors' => $e->errors()
    ], 422);
}

/**
 * Format success responses
 */
protected function successResponse(string $message, array $data = [], int $code = 200): JsonResponse
{
    return response()->json([
        'success' => true,
        'message' => $message,
        'data' => $data
    ], $code);
}
```

## Multi-Tenant Patterns

### 1. Site Context in Controllers

Controllers automatically operate within the current site and namespace context. For detailed namespace architecture, see `@ar-namespace-rule.mdc`.

```php
class ProductsController extends Controller
{
    /**
     * Ensure all operations are scoped to current site
     */
    public function index(Request $request): Response|View
    {
        // Automatically scoped to current site via model scopes
        $products = Product::owner()->get();
        
        return $request->ajax() ? $this->_grid() : $this->_init();
    }
    
    /**
     * Create product for current site
     */
    public function save(ProductRequest $request): Response
    {
        $product = Product::create(array_merge(
            $request->validated(),
            ['site_id' => site('site_id')] // Ensure site context
        ));
        
        return $this->successResponse('Product created', $product->toArray());
    }
}
```

### 2. Namespace-Specific Middleware

Controllers use namespace-specific middleware automatically. See `@ar-namespace-rule.mdc` for complete middleware patterns.

```php
// In route definitions
Route::group(['namespace' => 'Sitecp', 'middleware' => ['auth:admin']], function () {
    Route::resource('products', ProductsController::class);
});

Route::group(['namespace' => 'Api', 'middleware' => ['auth:api']], function () {
    Route::apiResource('products', ProductController::class);
});
```

## Authorization and Security

### 1. Policy-Based Authorization

```php
class ProductController extends Controller
{
    public function show(Product $product): Response
    {
        $this->authorize('view', $product);
        
        return new ProductResource($product);
    }
    
    public function update(ProductRequest $request, Product $product): Response
    {
        $this->authorize('update', $product);
        
        $product->update($request->validated());
        
        return new ProductResource($product);
    }
    
    public function destroy(Product $product): JsonResponse
    {
        $this->authorize('delete', $product);
        
        $product->delete();
        
        return response()->json(['message' => 'Product deleted']);
    }
}
```

### 2. Plan-Based Restrictions

```php
class ProductsController extends Controller
{
    /**
     * Check plan restrictions before operations
     */
    public function save(ProductRequest $request): Response
    {
        // Check plan limits
        if (!$this->canCreateProduct()) {
            return $this->errorResponse('Plan limit reached for products', [], 403);
        }
        
        $product = Product::create($request->validated());
        
        return $this->successResponse('Product created', $product->toArray());
    }
    
    /**
     * Get plan restriction data for views
     */
    protected function getPlanRestrictionData(string $feature): array
    {
        return [
            'allowed' => Plan::check($feature),
            'limit' => Plan::getLimit($feature),
            'current' => Plan::getCurrentUsage($feature),
        ];
    }
    
    private function canCreateProduct(): bool
    {
        return Plan::check('products') && 
               Product::count() < Plan::getLimit('products');
    }
}
```

## Error Handling Best Practices

### 1. Exception Handling

```php
class ProductsController extends Controller
{
    public function save(ProductRequest $request): Response
    {
        try {
            $product = \Illuminate\Support\Facades\DB::transaction(function () use ($request) {
                $product = Product::create($request->validated());
                
                // Handle related data
                $this->handleVariants($product, $request->input('variants', []));
                $this->handleImages($product, $request->input('images', []));
                
                return $product;
            });
            
            return $this->successResponse('Product created successfully', $product->toArray());
            
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e);
            
        } catch (PlanLimitException $e) {
            return $this->errorResponse('Plan limit exceeded', [], 403);
            
        } catch (Throwable $e) {
            \Log::error('Product creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return $this->errorResponse('An error occurred while creating the product', [], 500);
        }
    }
}
```

### 2. Custom Exception Classes

```php
// Custom exceptions for specific scenarios
class ProductNotFoundException extends HttpNotFound
{
    public function __construct(int $productId)
    {
        parent::__construct("Product with ID {$productId} not found");
    }
}

class PlanLimitException extends Exception
{
    public function __construct(string $feature)
    {
        parent::__construct("Plan limit reached for feature: {$feature}");
    }
}

// Usage in controllers
public function show(int $productId): Response
{
    $product = Product::find($productId);
    
    if (!$product) {
        throw new ProductNotFoundException($productId);
    }
    
    return new ProductResource($product);
}
```

## Performance Optimization

### 1. Efficient Data Loading

```php
class ProductsController extends Controller
{
    public function index(Request $request): Response|View
    {
        // Optimize queries with eager loading
        $products = Product::with(['category:id,name', 'variants:id,product_id,price'])
            ->select(['id', 'name', 'price', 'category_id', 'status'])
            ->owner()
            ->paginate(50);
            
        return $request->ajax() ? $this->_grid() : $this->_init();
    }
    
    /**
     * Optimize grid queries
     */
    protected function _grid(): Response
    {
        $grid = new Grid();
        $grid->setModel(Product::class);
        
        // Only load necessary columns
        $grid->setSelect(['id', 'name', 'price', 'status', 'created_at']);
        
        // Use efficient joins instead of N+1 queries
        $grid->setJoins([
            'categories' => ['type' => 'left', 'on' => 'products.category_id = categories.id']
        ]);
        
        return $grid->render();
    }
}
```

### 2. Caching Strategies

```php
class ProductsController extends Controller
{
    /**
     * Cache expensive operations
     */
    public function getRelatedData(): array
    {
        return Cache::remember('product_form_data', 3600, function () {
            return [
                'categories' => Category::active()->pluck('name', 'id'),
                'vendors' => Vendor::active()->pluck('name', 'id'),
                'statuses' => Status::orderBy('name')->get(),
            ];
        });
    }
    
    /**
     * Cache-aware updates
     */
    public function update(ProductRequest $request, Product $product): Response
    {
        $product->update($request->validated());
        
        // Clear related caches
        Cache::tags(['products', "product:{$product->id}"])->flush();
        
        return $this->successResponse('Product updated');
    }
}
```

## Testing Patterns

### 1. Controller Testing

```php
class ProductsControllerTest extends TestCase
{
    public function test_can_create_product(): void
    {
        $this->actingAs($this->createAdmin());
        
        $data = [
            'name' => 'Test Product',
            'price' => 99.99,
            'category_id' => Category::factory()->create()->id,
        ];
        
        $response = $this->postJson(route('admin.products.save'), $data);
        
        $response->assertStatus(200)
                ->assertJson(['success' => true]);
                
        $this->assertDatabaseHas('products', [
            'name' => 'Test Product',
            'price' => 9999, // Stored in cents
        ]);
    }
    
    public function test_validates_required_fields(): void
    {
        $this->actingAs($this->createAdmin());
        
        $response = $this->postJson(route('admin.products.save'), []);
        
        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'price']);
    }
    
    public function test_respects_plan_limits(): void
    {
        $this->actingAs($this->createAdmin());
        
        // Mock plan limit
        Plan::shouldReceive('check')->with('products')->andReturn(false);
        
        $response = $this->postJson(route('admin.products.save'), [
            'name' => 'Test Product',
            'price' => 99.99,
        ]);
        
        $response->assertStatus(403);
    }
}
```

This comprehensive controller pattern rule provides developers with all the patterns and best practices needed to work effectively with CloudCart's controller architecture. For namespace-specific patterns and architecture details, see `@ar-namespace-rule.mdc`. This rule focuses on controller implementation, validation, response formatting, and multi-tenant considerations.