---
description: Concise summary of CloudCart Laravel backend conventions and best practices.
globs: **/*.php
alwaysApply: true
---
# Unified Cursor AI Rules for CloudCart Project

## Description
Unified Cursor AI rules for CloudCart project covering Laravel backend conventions, architecture patterns, and comprehensive development guidelines.

## 1. High-Level Overview

CloudCart is a **SaaS** multi-tenant e-commerce platform:

* **Backend**: PHP 8.4 / Laravel 12+
* **Frontend**: Vue 3 SPA
* **Output language**: English

Primary goal: Enable SMBs to launch stores within minutes, offering deep customization via our public REST API and Webhooks.

### 1.1 Platform-Specific Terminology

* **Administrator** → store owner; also called merchant, admin, or owner. Full access to admin panel.
* **Moderator** → subordinate user; also called staff or employee. Partial access to admin panel.
* **Customer** → end user purchasing from the store; also called user or client. Accesses the storefront.
* **Subscriber** → user subscribed to newsletters or notifications. Accesses the storefront.

---

## 2. Architecture

* **Backend**: <PERSON><PERSON> 12+ (strict types, service-oriented, under `src/Domain/*`)
* **Frontend**: Vue 3 + TypeScript (`apps/storefront`, `apps/admin`), Composition API, SSR via Vite SSR
* **Data**: MariaDB Router + store-specific MariaDB databases; MongoDB for cache, sessions, queues
* **Infrastructure**: Docker-based; CI via GitLab → GCP

### 2.1 Namespaces

Each namespace has its own middlewares, providers, and routes.
Access via `app_namespace()` or `app('namespace')`.

| Namespace   | Description                                                                |
| ----- | ----- |
| `site`      | Storefront, defined in `bootstrap/app.php`                                 |
| `sitecp`    | Admin area, defined in `bootstrap/app.php`                                 |
| `api2`      | API endpoints for managing store data, defined in `api2/Bootstrap/app.php` |
| `builder`   | CloudCart CLI, defined from `artisan` (can switch using `-n=site` option)  |
| `facebook`  | Facebook webhook receiver, defined in `facebook/index.php`                 |
| `payments`  | Payment server for processor callbacks, defined in `bootstrap/app.php`     |
| `console`   | CloudCart internal control panel for staff                                 |
| `resellers` | Reseller management, defined in `resellers/Bootstrap/app.php`              |

**For detailed namespace patterns and organization:** Apply the namespace rule (`@ar-namespace-rule.mdc`)

### 2.2 Modules

* **Modules Structure**: Each contains providers, routes, models, events, and controllers.
* **Core Module**: Located in `modules/Core`, replacing older logic from `app/`.
* **API Responses**: All module controllers return JSON for Vue 3; some return rendered Smarty templates.
* **Vue Integration**: Used only in `sitecp` namespace (as of v2.1), under `vuejs-sitecp/`.

---

## 3. Code Organization & Structure

### 3.1 Directory Layout

* Use Laravel's defaults:
  `app`, `config`, `database`, `public`, `resources`, `routes`, `storage`.
* Inside `app/`:
  Group by responsibility → `Models`, `Http/Controllers`, `Services`, `Exceptions`, `Providers`, `Http/Middleware`.

### 3.2 Working with the Database

* Always rely on existing tables, databases, and columns before adding new ones.
* Check the `migrations` folder to understand structure and rules.

**For database-related work:** Apply the database rule (`@ar-database-rule.mdc`)

### 3.3 File Naming Conventions

| Item              | Style       | Example                      |
| ----- | ----- | ---- |
| Classes           | PascalCase  | `UserController`             |
| Variables/Methods | camelCase   | `$userName`, `getUserName()` |
| DB Tables/Columns | snake\_case | `users`, `user_id`           |
| Routes            | kebab-case  | `user.profile`               |

### 3.4 Method and Class Reuse

* Always search for an existing method or class before creating a new one.
* If a method is missing, add it to an existing class.
* If the class is missing, create the class.

---

## 4. Coding Standards

### 4.1 File-Level

* Always declare strict types: `declare(strict_types=1);`
* Omit the closing `?>` tag.

### 4.2 Formatting (PSR-12)

* 4-space indent
* Opening braces on the same line
* One statement per line
* \~120-character line length
* Blank line between methods and after `namespace`/`use` blocks

### 4.3 Namespaces & Imports

* Single `namespace` line, followed by a blank line
* `use` statements ordered: core → third-party → app

### 4.4 Signature Discipline

* Typed properties and scalar type hints
* Use union/intersection types over `@param` annotations
* Prefer `?Type` over `Type|null`
* Explicit `void` returns when applicable

### 4.5 PHP 8+ Features

* Constructor property promotion
* Named arguments
* `match` expressions
* Null-safe operator (`?->`)
* First-class callables (`foo(...)`)
* Arrow functions (`fn() => …`)
* Attributes (over docblock annotations)
* `readonly` properties and classes

### 4.6 No Dynamic Globals

* No variable variables (`$$foo`)
* No untyped dynamic properties
* No defining functions or classes inside conditionals

### 4.7 Documentation

* Use PHPDoc only when needed (e.g., for array shapes, generics)
* Document public methods' intent and edge cases
* Write all annotations, docblocks, comments, and TODOs in English

### 4.8 Error Handling

* Use `throw` expressions
* Catch `\Throwable` at high levels for logging and recovery
* Gracefully handle exceptions with try-catch
* Rely on Laravel's exception handler for logging/reporting
* Create custom exceptions for specific scenarios
* Provide meaningful error messages to users

### 4.9 Expression Rules

* Avoid unparenthesized nested ternaries; rewrite for clarity:
  Replace `a ? b : c ? d : e` with `(a ? b : c) ? d : e` or `a ? b : (c ? d : e)`

---

## 5. Laravel-Specific Rules

* Use `php artisan make:{option}` for generating models, migrations, controllers, etc.
* In Laravel 11+, `app\Console\Kernel.php` was removed; use `app.php` for console config.
* In Laravel 12+, commands in `app\Console\Commands` are auto-discovered.
* Store environment values in config files; reference via `config('app.name')` (not `env('APP_NAME')`).
* Prevent N+1 queries by eager/batch loading:

  ```php
  $users = User::with('posts')->get();
  $posts = Post::whereIn('user_id', $users->pluck('id'))->get();
  ```

### 5.1 Multi-Tenant Execution

Use `exec()` from `App\Console\Traits\TargetSites` for tenant-wide operations.
This loops over all sites and applies your callback.

Example:

```php
$this->exec(function (Site $site) {
    // Logic for each site
});
```

### 5.2 Current Site Instance

* Web: resolved by hostname (`site()` → `App\Models\Router\Site`)
* CLI: resolved after calling `bootDB()` in `App\Models\Router\Site`
* DB queries must always use the current site's connection.

---

## 6. Design Patterns

* **Repository Pattern** → separates data access logic
* **Service Pattern** → encapsulates business logic (`@ar-service-pattern-rule.mdc`)
* **Observer Pattern** → handles model events in a decoupled way

---

## 7. Recommended Approaches

* **Eloquent ORM** → leverage relationships and aggregates
* **Validation** → use Laravel's built-in system (`@ar-request-validation-rule.mdc`)
* **Middleware** → manage auth, access, request handling (`@ar-middleware-rule.mdc`)
* **Queues** → use for async/background tasks (`@ar-queue-rule.mdc`)
* **Events & Listeners** → decouple logic like notifications, model updates (`@ar-events-rule.mdc`)

---

## 8. Anti-Patterns & Code Smells

* **God Classes** → avoid multi-responsibility classes
* **Spaghetti Code** → keep logic clean and structured
* **Copy-Paste Programming** → favor reusable components/functions
* **Ignoring Exceptions** → always handle errors properly
* **Over-Engineering** → keep solutions simple
* **Mass Assignment** → secure models via `fillable` or `guarded`

---

## 9. Performance

* **Caching** → use `CcCache` over Laravel's base cache when available
* **DB Optimization** → apply indexes, optimize queries
* **Efficient Code** → minimize loops and conditionals in hot paths

---

## 10. Queues

* Queues by priority (0–8): system, import, export, product-images, order-events, subscribers, segments, cc-system, campaigns, campaigns-messages, campaigns-hooks, campaigns-process, translate, tmp, cloudio.
* Higher number = higher priority (`system3` > `system2`).
* All jobs extend `App\Jobs\Job`.
* Learn `createQueueByMapping()` in `App\Models\Queue\SiteQueue` to register recursive jobs.

**For detailed queue implementation:** Apply the queue rule (`@ar-queue-rule.mdc`)

---

## 11. Behavior

* **Operator Requests** → If an operator requests a change, review your work and remove/rewrite parts that should follow the new logic.

---

## 12. Component-Specific Rules

When working with specific components, apply these specialized rules:

### 12.1 Controllers
When creating or modifying controllers, apply the controller pattern rule: `@ar-controller-pattern-rule.mdc`

### 12.2 Models
When creating or modifying models, apply the model definition rule: `@ar-model-definition-rule.mdc`

### 12.3 Custom Request Validation
When creating or modifying custom request classes (site/sitecp with Smarty), apply the custom request validation rule: `@ar-request-validation-rule.mdc`

### 12.4 Laravel FormRequest Validation
When creating or modifying Laravel FormRequest classes (modules/API), apply the Laravel FormRequest validation rule: `@ar-request-validation-original-rule.mdc`

### 12.5 Services
When implementing business logic services, apply the service pattern rule: `@ar-service-pattern-rule.mdc`

### 12.6 Middleware
When creating or modifying middleware, apply the middleware rule: `@ar-middleware-rule.mdc`

### 12.7 API Resources
When working with API transformations and resources, apply the API resource rule: `@ar-api-resource-rule.mdc`

### 12.8 Events & Listeners
When implementing event-driven architecture, apply the events rule: `@ar-events-rule.mdc`

### 12.9 Queue Jobs
When creating background jobs and queue processing, apply the queue rule: `@ar-queue-rule.mdc`

### 12.10 Database Operations
When working with migrations, models, or database queries, apply the database rule: `@ar-database-rule.mdc`

### 12.11 Namespace Organization
When organizing code across namespaces or working with multi-namespace features, apply the namespace rule: `@ar-namespace-rule.mdc`

### 12.12 Module Development
When creating or modifying modules, apply the module pattern rule: `@ar-module-pattern.mdc`

### 12.13 Unit Tests
When writing unit tests, apply the unit test rule: `@ar-unit-test-rule.mdc`

---

## 13. Rule Application Guidelines

### 13.1 When to Apply Specialized Rules

- **Always apply** the relevant specialized rule when working on specific components
- **Combine rules** when working on features that span multiple components
- **Reference base rule** for general coding standards and architecture understanding

### 13.2 Rule Priority

1. **Base Rule** (`a-base-rule.mdc`) - Always applicable for general standards
2. **Component-Specific Rules** - Apply based on the component being worked on
3. **Architecture Rules** - Apply when working on system-wide features

### 13.3 Multi-Component Development

When working on features that involve multiple components:

```
Example: Creating a new product feature
- Apply @ar-controller-pattern-rule.mdc for controllers
- Apply @ar-model-definition-rule.mdc for models
- Apply @ar-request-validation-rule.mdc for custom validation (site/sitecp)
- Apply @ar-request-validation-original-rule.mdc for Laravel FormRequest (modules/API)
- Apply @ar-api-resource-rule.mdc for API responses
- Apply @ar-events-rule.mdc for event handling
- Apply @ar-queue-rule.mdc for background processing
- Apply @ar-module-pattern.mdc for module structure
```

This comprehensive rule system ensures consistent development practices across CloudCart's sophisticated multi-tenant e-commerce platform while providing specialized guidance for each component type.

