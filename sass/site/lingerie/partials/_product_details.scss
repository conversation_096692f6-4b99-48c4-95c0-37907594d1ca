.modal-dialog.product-details {
    width: 1000px;
    .modal-body {
        padding: 30px 15px;
        .product-details-js {
            width: auto;
            ul.product-details-gallery {
                li {
                    margin-right: 9px;
                }
            }
            h1 {
                margin: 0;
                padding: 0;
            }
            .product-details-description-js {
                margin: 0;
                border-top: none;
                padding-bottom: 25px;
            }
            .product-list-discount-flat {
                border: none;
            }
        }
    }
}

.quantity-holder {
    //margin-top: 15px;
    .quantity-selection-row {
        @extend %flexbox;
        @include justify-content(space-between);
        @include align-items(center);

        .quantity-selection {
            max-width: 136px;
        }

        .quantity-left-js {
            max-width: 60%;
        }

        .quantity-left-js {
            @include flex(1 1 auto);
        }

        .buy-btn-holder {
            @include flex(1 1 auto);
            @include align-items(flex-end);
        }
    }

    .submit-js {
        margin: 0 0 0px 20px;
    }

    .btn-primary {
        float: right;
        white-space: inherit;
    }

    .input-group {
        button,
        input {
             height: 40px;
        }
    }
    .quantity-selection {
        //float: none;
    }
}

.product-details-js {
    margin-bottom: 40px;
    .options {
        padding-bottom: 20px;
        margin-bottom: 20px;

        .parameters {
            .parameter-holder {
                margin-bottom: 20px;
            }
        }
        .btn-primary {
            margin-top: 20px;
        }
    }
    @media (max-width: $break-md) {
        .product-details-left-box {
            margin-bottom: 30px;
        }
    }
    .product.zoom-enabled {
        cursor: image-url("images/cursor-zoom.png"), auto;
    }
    img {
        max-height: 100%;
        max-width: 100%;
    }
    .product {
        position: relative;
        z-index: 1;
        text-align: center;
        //@extend %flexbox;
        //@include align-items(center);
        //@include justify-content(center);

        img {
            display: inline-block;
            //@include flex(none);
        }

        > span {
            width: 100%;
            .image {
                width: 100%;
            }
        }
            
        .product-ribbon-banner {
            width: auto;
            position: absolute;
            z-index: 3;
        }

        .sale,
        .new {
            //position: absolute;
            //left: -5px;
            //z-index: 1;
            //min-width: 60px;
            //max-width: 100px;
            //height: 40px;
            //line-height: 40px;
            //padding: 0 10px;
            //text-align: center;
            //text-transform: uppercase;
            //pointer-events: none;
            //@include ellipsis(true);
            //display: none;
            position: absolute;
            top: 5px;
            z-index: 1;
            text-align: center;
            pointer-events: none;
            @include ellipsis(true);
            overflow: visible;
            @include font-size($item-price-font-size);
        }

        .sale {
            min-width: 60px;
            line-height: 1.666666667;
            left: 0px;
            padding: 0 10px;
            width: auto;
        }

        .new {
            right: 5px;
            min-width: 70px;
            max-width: 70px;
            height: 70px;
            @include border-radius(50%);
            text-transform: uppercase;
            //@include flexbox;
            @extend %flexbox;
            @include align-items(center);
            @include justify-content(center);
        }

        //@media (max-width: $break-sm) {
        //    .sale, .new {
        //        min-width: 50px;
        //        height: 30px;
        //        line-height: 30px;
        //        padding: 0 5px;
        //        &.alone {
        //            top: 15px;
        //        }
        //    }
        //    .sale {
        //        top: 15px;
        //    }
        //    .new {
        //        top: 55px;
        //    }
        //}
    }
    .featured {
        position: absolute;
        bottom: 5px;
        left: 0;
        padding: 0 10px;
        line-height: 1.667;
        width: auto;
    }
    .discount-box {
        overflow: hidden;
        position: absolute;
        top: 45px;
        left: 0;
        line-height: 20px;
        margin-top: 30px;
        z-index: 1;
        pointer-events: none;
        padding: 0 10px;
        //min-width: 70px;
        //height: 25px;
        //max-width: 100px;
        //display: none !important;

        p {
            padding-bottom: 0;
            margin-bottom: 0
        }

        &.discount-box-leasing {
            margin-top: 0;
        }

        .product-list-discount-percent, .product-list-discount-flat {
            border-bottom: none !important;
            margin-bottom: 0;

            span {
                display: inline-block;
                line-height: 20px;
                text-align: center;
                text-transform: uppercase;
                pointer-events: none;
            }
        }
    }
    .thumbs-container-js {
        margin-top: 10px;
        .scroll-top,
        .scroll-bottom {
            display: none;
        }
    }

    .scroll-top,
    .scroll-bottom {
        a {
            display: block;
            text-align: center;
            padding: 3px 0;
        }
    }
    ul.product-details-gallery {
        height: 100px;
        overflow: hidden;
        text-align: center;
        margin: 4px 0 0;
        li {
            display: inline-block;
            position: relative;
            vertical-align: top;
            //margin-top: 10px;
            margin: 0 0 4px 5px;
            height: 96px;
            width: 96px;
            padding: 3px;
            text-align: center;

            &:last-of-type {
                margin-right: 0;
            }
            a {
                height: 100%;
                @extend %flexbox;
                @include align-items(center);
                @include justify-content(center);
            }
            img {
                @include flex(0 0 auto);
                display: block;
                max-width: 100%;
                max-height: 100%;
            }
        }
    }
    .title h1 {
        margin-bottom: 0;
    }
    p {
        padding-bottom: 20px;
    }

    .tags-box {
        .tags {
            padding-top: 20px;
            margin-bottom: 10px;
            .tag {
                margin-bottom: 10px;
            }
        }
    }
    .product-details-description {
        //margin-bottom: 20px;
        padding: 20px 0;

        p:last-of-type {
            margin-bottom: 0;
            padding-bottom: 0;
        }
    }
    .product-details-price-js {
        margin-bottom: 20px;
        padding-top: 5px;
        padding-bottom: 7px;
        &.no-discout-price {
            padding-top: 5px;
        }
        .details-price-left {
            @include flex(1 1 auto);
            max-width: 100%;
        }
        .price-new-js {
            display: inline-block;
            @include font-size($price-font-size);
            margin: 0 10px 2px 0;
            padding-top: 1px;
        }
        .prices-holder {
            display: inline-block;
            min-width: 25%;
            font-weight: $light;

            .price-old-js,
            .price-discount-js {
                display: block;
                line-height: 1.1;
            }

            .price-old-js {
                vertical-align: top;
                margin-top: 10px;
                text-decoration: line-through;
            }
            .price-discount-js {
                vertical-align: baseline;
                font-weight: $light;
                text-transform: capitalize;
            }
        }
        .variant-sku-js {
            @extend %flexbox;
            @include flex(none);
            @include align-self(auto);
            float: right;
            vertical-align: top;
            margin-top: 10px;
        }
    }
    @media (max-width: $break-sm) {
        .product-details-price-js {
            padding-right: 0;
            .details-price-left {
                display: block;
                padding-bottom: 1px;
            }
            .price-new-js {
                @include font-size($price-font-size);
                margin: 0 10px 10px 0;
            }
            .price-old-js {
                margin-top: 5px;
            }
            .variant-sku-js {
                margin-top: 10px;
                text-align: left;
                margin-right: 0;
                width: 100%;
                float: none;
                display: block;
            }
        }
    }
    .options {
        .quantity-holder {
            //margin-top: 20px;
            &:not(.no-padding-left) {
                .col-sm-3,
                .col-sm-9 {
                    padding-left: 0;
                }
            }

            &-nospace {
                margin-top: 0;
                .quantity-selection {
                    margin: 0;
                }
            }

            &-digital {
                margin-top: 0;
                position: relative;
                top: -20px;
            }

        }

        .bootstrap-touchspin {
            input {
                text-align: center;
                padding: 10px 5px;
            }
            .input-group-btn .btn {
                @include border-radius(0px);
                @include box-shadow(none);
                border: none;
                padding: 0px 14px;
                line-height: 34px;
            }
        }
        .no-padding:first-of-type {
            padding-right: 15px !important;
        }
    }
    .product-meta {
        padding-bottom: 15px;
    }
    .published,
    .product-vendor,
    .category {
        margin: 0 10px 5px 0;
        a {
            text-decoration: none;
            display: inline-block;
        }

        &:first-of-type {
            margin-left: 0;
        }
    }
    .choose-variant-msg-js {
        width: 100%;
        margin: 0 0 16px;
    }

    .quantity-left-js {
        //@extend %inline-flex;
        //@include align-items(center);
        //min-height: 40px;
        text-transform: uppercase;
        font-weight: $bold;

        .out-of-stock-js {
            display: none;
            color: red;
        }
    }

    .select2-container {
        &.select2-allowclear {
            .select2-choice {
                abbr {
                    display: none;
                }
            }
        }
    }
}

.stock-status-bar {
    display: inline-block;
    padding: 2px 7px;
    font-size: 12px;
}

.category-properties-list {
    margin-bottom: 0;
}

.category-properties-item {
    margin: 0 10px 5px 0;
}

.share-wrapper {
  position: relative;
  .share {
        position: relative;
        height: 0;
        overflow: hidden;
        visibility: hidden;
        -webkit-transition: height .3s ease;
        -moz-transition: height .3s ease;
        -ms-transition: height .3s ease;
        -o-transition: height .3s ease;
        transition: height .3s ease;
        text-align: center;

    @media (max-width: $break-sm) {
      margin-top: 0;
    }

    .addthis_toolbox {
      display: inline-block;
      margin: 0 auto;
      visibility: hidden;
      padding: 10px 0 5px;
      -webkit-transition: visibility 0s ease .3s;
      -moz-transition: visibility 0s ease .3s;
      -ms-transition: visibility 0s ease .3s;
      -o-transition: visibility 0s ease .3s;
      transition: visibility 0s ease .3s;
    }
  }
}

.credit-calculator-button-holder {
    margin-top: 20px;
}

.product-related {
  .new,
  .sale,
  .discount-box {
    display: none;
  }
}

.facebook-comments {
    margin-bottom: 30px;
}

.disqus-comments {
    margin-bottom: 30px;
}

.modal {
    .comment-forms {
        padding: 0 15px;
    }
}

/* parameters */
._parameter-radio-values {
    margin-bottom: -3px;
    margin-right: -3px;
    @extend .clearfix;
}

._parameter-radio-value {
    float: left;
    display: table;
    height: 36px;
    min-width: 44px;
    margin-right: 3px;
    margin-bottom: 3px;

    ._radio {
        display: table-cell;
        vertical-align: middle;
        border: 1px solid;
        padding: 5px;
        text-align: center;
        font-weight: normal;
        font-size: 13px;

        &.active {
            border-width: 2px;
            padding: 4px;
        }

        .radio {
            display: none;
        }
    }
}

._parameter-image-values {
    margin-left: -1%;
    margin-bottom: -1%;
    @extend .clearfix;
}

._parameter-image-value {
    float: left;
    border: 1px solid;
    width: 24%;
    max-width: 100px;
    margin-left: 1%;
    margin-bottom: 1%;
    word-break: break-word;

    &.active {
        border-width: 2px;
    }

    ._radio {
        display: block;
        height: 0;
        padding: 0;
        padding-bottom: 100%;
        position: relative;
        overflow: hidden;
        cursor: pointer;

        .radio {
            top: 5px;
            left: 5px;
        }

        .radio-label {
            display: inline-block;
            padding-top: 4px;
            padding-left: 30px;
            padding-right: 5px;
        }

        img {
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
                    transform: translate(-50%, -50%);

            ~ .radio,
            ~ .radio-label {
                display: none;
            }
        }
    }
}

._parameter-color-values {
    margin-right: -5px;
    margin-bottom: -5px;
    @extend .clearfix;
}

._parameter-color-value {
    float: left;
    margin-right: 5px;
    margin-bottom: 5px;

    ._radio {
        display: block;
        width: 34px;
        height: 34px;
        border: 2px solid transparent;
        padding: 0;
        margin: 0;
        font-size: 0;
        line-height: 0;
        cursor: pointer;

        .radio {
            display: none;
        }

        .radio-label {
            display: none;
        }
    }
}

@media (min-width: $break-md) {
    .product-details-js {
        .quantity-holder {
            .submit-js {
                margin-top: 0 !important;
            }
        }
    }
}
@media (max-width: $break-md) {
    .product-details-js .options {
        .quantity-holder {
            margin-top: 0;
            width: 100%;

            .quantity-selection-row {
                @include flex-flow(column);
                .quantity-selection {
                    max-width: 100%;
                    width: 100%;
                }

                .quantity-left-js {
                    max-width: 100%;
                    width: 100%;
                }

                .buy-btn-holder {
                    width: 100%;
                }
            }
        }
    }

    .product-details-js {
        .product {
            height: 300px;
        }
        .options {
            .btn-primary {
                width: 100%;
            }
        }
    }

    .credit-calculator-button-holder {
        margin-top: 20px;
        text-align: center;
    }
}

@media (max-width: $break-sm - 1px) {
    .share-wrapper {
        top: 0;
    }
    .quantity-holder {
        margin-top: 15px;

        .submit-js {
            margin-bottom: 0;
        }
    }
}
