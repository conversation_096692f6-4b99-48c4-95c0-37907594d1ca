/*==================================================*\
    TITLES
\*==================================================*/

.title-1 {
}

.title-2 {
	font-size: 48px;
	line-height: 52px;
	text-transform: uppercase;
}

.title-3 {
	font-size: 56px;
	line-height: 60px;
	text-transform: uppercase;
}

.title-4 {
	font-size: 24px;
	line-height: 28px;
	font-weight: 400;
	text-transform: uppercase;
}

.title-5 {
	font-size: 42px;
	line-height: 46px;
	font-weight: 400;
	text-transform: uppercase;
}

.title-6 {
	font-size: 20px;
	line-height: 24px;
	font-weight: 600;
}

.title-7 {
	font-size: 36px;
	line-height: 40px;
	font-weight: 400;
	text-transform: uppercase;
}

.title-8 {
	font-size: 90px;
	line-height: 94px;
	font-weight: 400;
	text-transform: uppercase;
}

/* 	 SECTION TITLES
----------------------------------------------------*/
header.title {
	@extend .lay-container-sep;
	border-bottom: none;
	margin-bottom: 0;
	height: 0;
	h2 {
		font-size: 24px;
		line-height: 1;
		text-transform: uppercase;
		font-weight: 500;
		padding: 23px 30px;
		display: inline-block;
		width: auto;
		position: relative;
		bottom: 19px;
		z-index: 1000;
		.icon-wrapper {
			vertical-align: middle;
			line-height: 9px;
		}
	}
}

header.subtitle {
	text-align: right;
	h4 {
		font-size: 20px;
		line-height: 1;
		text-transform: uppercase;
		font-weight: 500;
		padding: 12px 30px;
		display: inline-block;
		width: auto;
		margin: 0;
		text-align: center;
		.icon-wrapper {
			vertical-align: middle;
			line-height: 9px;
		}
	}
}

/*==================================================*\
    TEXTBOX
\*==================================================*/

.textbox {
	@include clearfix;
	font-size: 14px;
	line-height: 22px;
	h1, h2, h3 {
		font-weight: 600;
		line-height: 1.2;
		& + p, ul, ol {
			margin-top: 20px;
		}
	}
	* {
		margin-bottom: 0;
		margin-top: 35px;
		z-index: 1;
		&:first-child {
			margin-top: 0;
		}
	}
	h1 {
		font-size: 24px;
	}
	h2 {
		font-size: 20px;
	}
	h3 {
		font-size: 16px;
	}
	ul,
	ol {
		list-style-position: inside;
	}
	ul {
		list-style-type: disc;
	}
	ol {
		list-style-type: decimal;
	}
	blockquote {
		font-style: italic;
	}
	pre {
		white-space: pre-wrap;
		word-wrap: break-word
	}
	img {
		height: auto;
		max-width: 100%;
	}
	table {
		width: 100%;
	}

    .aligncenter {
        display: block;
        max-width: 100%;
        margin: 0 auto;
    }

	.alignleft {
		float: left;
		margin: 5px 20px 10px 0;
	}

	.alignright {
		float: right;
		margin: 5px 0 10px 20px;
	}

    .text-aligncenter {
        text-align: center;
    }

    .text-alignleft {
        text-align: left;
    }

    .text-alignright {
        text-align: right;
    }

    .text-alignjustify {
        text-align: justify;
    }
}

.textbox-iframe {
    height: 0;
    padding-bottom: 56.25%;
    position: relative;

    iframe {
        border: 0;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
    }
}

/*==================================================*\
    TABLES
\*==================================================*/

.table {
	thead {
		th {
			font-weight: 400;
			font-size: 20px;
			line-height: 20px;
			text-transform: uppercase;
			padding: 10px;
		}
		&.noborder {
			th {
				border-top: none !important;
			}
		}
	}
	tbody {
		td {
			padding: 10px !important;
			border: none !important;
			vertical-align: middle !important;
			font-size: 14px;
			line-height: 18px;
			p {
				margin-bottom: 0;
			}
		}
	}
}

/*==================================================*\
    NOTIFICATIONS
\*==================================================*/

.notif {
	font-size: 18px;
	line-height: 22px;
	font-style: italic;
}