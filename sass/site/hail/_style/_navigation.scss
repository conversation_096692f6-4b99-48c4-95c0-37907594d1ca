.header-top {
    background: $header_top_background;
    border-bottom: 1px solid $header_border_color;

    .search-box {
        input {
            background: $header_top_background;
            border: 0;
            border-left: 1px solid $header_border_color;
            border-right: 1px solid $header_border_color;

            &:focus {
                border-color: $header_border_color_focus;
            }
        }

        .fa-search {
            font-size: 16px;
            color: $socials_icons_header;
        }
    }
}

.header-middle {
    background: $header_middle_background;

    .header-social {
        li {
            a {
                border: 2px solid $border_color_socials_header;
                color: $socials_icons_header;
            }
        }
    }
}

.navbar {
    background: $header_bottom_background;

    &-toggle {
        background-color: $background_color_socials_header;
        border-color: $border_color_socials_header;

        &-line {
            background-color: $socials_icons_header;
        }
    }

    &-nav {
        li {
            a {
                color: $navigation_color;

                &:hover,
                &:focus:hover {
                    background-color: $navigation_background;
                }

                &:focus {
                    background-color: transparent;
                }

                &:hover, &:active, &.active, &:focus {
                    opacity: 1;
                }
            }

            &:hover {
                > a {
                    background-color: $navigation_background;
                    color: $navigation_color;
                }
            }
        }

        ul {
            background-color: $header_top_background;
            border-color: $border_color;

            li {
                a {
                    color: $header_bottom_background;
                }
            }
        }
    }

    .collapse-icon {
        border-color: $border-color;
        color: $border_color;
    }
}

.collapse-icon {
    color: $header_bottom_background;
}

@media (max-width: $break-device) {
    .navbar {
        &-nav {
            border-color: $border_color;

            li {
                border-color: $border_color;

                a {
                    color: $navigation_color;

                    &:hover,
                    &:focus:hover {
                        background-color: transparent;
                    }
                }

                &:hover {
                    > a {
                        background-color: transparent;
                    }
                }
            }

            @for $i from 1 through 10 {
                .level-#{$i} {
                    background-color: $header_bottom_background; 
                    border-color: $border_color; 

                    > li {
                        border-color: $border_color;

                        > a {
                            color: $navigation_color;
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: $break-sm) {
    .header-top {
        border: 1px solid $border_color;
        border-width: 0 1px;

        .user-controls {
            border-bottom: 1px solid $border_color;
        }

        .search-box {
            input {
                border: 0;
                border-bottom: 1px solid $border_color;
            }
        }
    }

    .user-controls a {
        border: 0;

        + a {
            border-left: 1px solid $border_color;
        }
    }
}