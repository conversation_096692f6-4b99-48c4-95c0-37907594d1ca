.navcart {
    position: relative;
    text-align: left;

    .navcart-information {
        display: none;
        text-align: left;
        border: 1px solid;
        position: absolute;
        top: 100%;
        left: 0;
        padding: 20px;
        min-width: 280px;
        z-index: 11;
        margin-top: 10px;

        &:before {
            content: "";
            display: block;
            position: absolute;
            top: -11px;
            left: 38px;
            width: 0;
            height: 0;
            z-index: 10;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
        }
        &:after {
            content: "";
            display: block;
            position: absolute;
            top: -10px;
            left: 38px;
            width: 0;
            height: 0;
            z-index: 11;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
        }
        ul {
            margin: 0;
            max-height: 400px;
            overflow-y: auto;
            overflow-x: hidden;

            li {
                position: relative;
                padding: 0;
                margin-bottom: 20px;
                text-align: right;
                > a {
                    @extend .cl;
                }

                .image-holder {
                    float: right;
                    border: 1px solid;
                    display: inline-block;
                    width: 64px;
                    height: 64px;
                    overflow: hidden;
                    margin-bottom: -5px;
                    text-align: center;
                    cursor: pointer;
                    img {
                        display: inline-block;
                        position: relative;
                        max-width: 100%;
                        max-height: 100%;
                        top: 50%;
                        -webkit-transform: translateY(-50%);
                        -moz-transform: translateY(-50%);
                        -ms-transform: translateY(-50%);
                        -o-transform: translateY(-50%);
                        transform: translateY(-50%);
                    }
                }

                img, h4, span, a {
                    margin: 0;
                }
                a {
                    display: block;
                    cursor: default;
                }
                h4 {
                    font-size: $second-font-size;
                    word-break: break-word;
                    cursor: pointer;
                    margin-bottom: 5px;
                    padding: 0 70px 0 10px;

                    &:hover {
                        text-decoration: underline;
                    }
                }
                .product-cart-product-price {
                    direction: ltr;
                    padding-right: 70px;
                }
                .product-cart-product-remove {
                    display: block;
                    opacity: 1;
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 8px;
                    height: 8px;
                    @include opacity(1);


                    &:hover {
                        cursor: pointer;
                        @include opacity(0.7);
                    }
                }
                .product-cart-product-price {
                    display: block;
                    font-size: $second-font-size;
                }
                &:hover {
                    .product-cart-product-remove {
                        &:hover {
                            @include opacity(0.6);
                        }
                    }
                }
            }
            .product-cart-subtotal {
                line-height: 34px;
                width: 100%;
                text-align: center;
                margin-bottom: 10px;
            }
        }

        .btn {
            width: 100%;
        }
    }
    .navcart-button {
        position: relative;
        @extend .cl;
        .navcart-button-icon {
            float: left;
            width: 38px;
            height: 38px;
            @include border-radius(50%);
            text-align: center;

            > i {
                margin: 9px auto 0;
                display: block;
                @include scaleX(-1);
            }
        }
        .navcart-items {
            display: block;
            width: auto;
            line-height: 16px;
            height: 16px;
            color: #fff;
            padding: 0 4px;
            @include border-radius(20px);
            position: absolute;
            top: 0;
            left: 0;
            margin: auto;
            z-index: 10;
            text-align: center;
        }
        &:hover {
            .navcart-information {
                //display: block;
            }
        }
    }

    .navcart-dropdown {
        display: inline-block;
        text-align: center;

        &:after {
            content: '';
            display: block;
            position: absolute;
            top: 100%;
            right: 0;
            width: 100%;
            height: 20px;
            z-index:1;
        }

        &:hover {
            .navcart-information {
                //display: block;
            }
        }

        a {
            display: inline-block;
        }
    }
}

.checkout-steps {
    text-align: center;
    padding-bottom: 12px;
    padding-top: 12px;
    margin-bottom: 20px;

    .checkout-step {
        display: inline-block;
        margin-right: 20px;
        line-height: 25px;

        &:first-of-type {
            margin-right: 0;
        }

        .name {
            color: #aeaeae !important;
            font-size: 13px;
        }

        .indicator {
            margin-top: 4px;
            width: 20px;
            height: 20px;
            display: inline-block;
            float: right;
            margin-left: 10px;
            position: relative;
            @include border-radius(50%);
            &:before {
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                text-align: center;
                line-height: 18px;
                display: block;
                margin: auto;
                color: #aeaeae;
            }
        }
        &.checkout-step-cart .indicator:before {
            content: "1";
        }
        &.checkout-step-authorize .indicator:before {
            content: "2";
        }
        &.checkout-step-address .indicator:before {
            content: "3";
        }
        &.checkout-step-shipping .indicator:before {
            content: "4";
        }
        &.checkout-step-verify .indicator:before {
            content: "5";
        }
        &.checkout-step-complete .indicator:before {
            content: "6";
        }
        &:after {
            content: "";
            background-image: image-url("images/step-arrow.png");
            background-position: center center;
            background-repeat: no-repeat;
            width: 6px;
            height: 21px;
            display: inline-block;
            margin-right: 20px;
            vertical-align: middle;
            @include scaleX(-1);
        }
        &:last-of-type:after {
            display: none;
        }
    }
}

.cart-items {
    padding: 0 0 20px 0;
    span {
        margin-right: 10px;
    }
}



.cart-holder, .checkout-cart-holder {
    .cart-product-list, .checkout-cart-product-list {
        margin-bottom: 30px;
        .cart-product {
            padding: 20px;
            min-height: 140px;
            margin-bottom: 15px;
            position: relative;
            box-shadow: 0 2px 3px rgba(1,1,1,.25);

            &:before, &:after {
                content: "";
                display: table;
                clear: both;
            }
            &:last-of-type {
            margin-bottom: 0;
            }
            &:hover {
                .cart-image-box .image-holder {
                    img {
                        @include opacity(.7);
                    }
                }
                .cart-product-remove {
                    display: block;
                    @include opacity(1);
                }
            }
            .cart-image-box {
                display: block;
                margin-bottom: 15px;
                @extend .cl;
                .image-holder {
                    position: absolute;
                    right: 20px;
                    top: 20px;
                    width: 100px;
                    height: 100px;
                    img {
                        max-width: 100%;
                        max-height: 100%;
                        border: 1px solid #e8e8e8;
                        @extend .stickCenter;
                    }
                }
                .cart-parameter-box {
                    padding-right: 130px;
                    .weight {
                        text-align: right;
                    }
                    h4 {
                        margin-top: 0;
                    }
                    .parameter-holder {
                        .parameter {
                            display: block;
                            margin-left: 10px;
                            .name {
                                margin-right: 5px;
                            }
                        }
                    }
                }
            }
            .cart-product-remove {
                position: absolute;
                width: 16px;
                height: 16px;
                top: 20px;
                left: 20px;
                text-align: center;
                line-height: 15px;
                display: block;
                @include transition(all, .3s);
                @include border-radius(50%);
                @include opacity(0);
                &:hover {
                    background-image: image-url("images/remove-product-hover.png");
                }
                white-space :nowrap;
                text-indent: -9999px;
                background-image: image-url("images/remove-product.png");
                border-radius : 2px;
            }
            .cart-product-quantity {
                display: block !important;
                padding-right: 130px;
                .bootstrap-touchspin {
                    width: 105px;
                    height: 25px;
                    margin-bottom: 10px;
                }
            }
            .cart-product-single-price {
                display: inline-block;
                position: absolute;
                left: 20px;
                bottom: 15px;
                font-size: 18px;
                line-height: 1.2;
                //text-align: right;
                direction: ltr;
                .main-price {
                    //line-height: 1.2;
                }
            }
            .cart-product-total-price {
                display: none;
            }
        }
    }
}

.cart-totals, .checkout-cart-totals, .cart-providers, .cart-estimate .cart-settings-box {
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 3px rgba(1,1,1,.25);

    .btn {
        width: 100%;
        padding: 3px;
    }
    .section-title {
        margin: 0 0 20px 0;
        padding-bottom: 20px;
        border-bottom: 1px solid #dcdbdb;
    }
    .cart-products-subtotal-box {
        padding: 5px 0;

        img {
            width: 30px;
        }
        .weight {
            display: inline-block;
        }
        .total-title {
            float: right;
        }
        .total-value {
            float: left;
        }
        &:before, &:after {
            content: "";
            display: table;
            clear: both;
        }
        &.shipping {
            position: relative;
            .total-title {
                display: block;
            }
            .btn {
                background: transparent;
                width: auto;
                padding: 0 5px;
            }
        }
        &.final-total {
            line-height: 40px;
            margin-top: 10px;
            border-top: 1px solid #dcdbdb;
            border-bottom: 1px solid #dcdbdb;
        }
    }
}

.cart-estimate-tax-submit , .cart-shipping-provider-submit {
    line-height: 1.6;
}

.cart-totals {
    border-bottom: 0
}

.checkout-cart-totals {
    border-bottom: 0;
}

.cart-settings-js , .cart-submit {
    box-shadow: 0 2px 3px rgba(1,1,1,.25);
}

.cart-submit {
    box-shadow: 0 2px 3px rgba(1,1,1,.25);
}

.cart-products-continue {
    margin-top: 15px;
}

.cart-estimate .cart-settings-box {
    padding: 15px;
    h4 {
        margin: 0;
    }

    .cart-shipping-providers-js {
        margin-top: 20px;

        ul {
            overflow: hidden;

            li {
                width: 100%;
                display: inline-block;

                label {
                    float:left;
                }

                label + label {
                    float: right;
                }
            }
        }
    }
}
.cart-totals, .checkout-cart-totals {
    margin-bottom: 0;
}
.cart-submit {
    padding: 0 20px 20px 20px;
    .btn {
        width: 100%;
    }
}

.checkout-address-choose-type {

    label {
        margin-bottom: 15px;
    }
}

.checkout-address-form-add {
    margin-top: 15px;
}

#orderAddressChooseJs {
    .checkout-address-choose {
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 3px rgba(1,1,1,.25);

        h2 {
            margin: 0 0 20px 0;
            padding-bottom: 20px;
            border-bottom: 1px solid #dcdbdb;
        }
        .checkout-address-form-add {
            padding-top: 15px;
        }
        .checkout-address-forms .checkout-address-form-choose .checkout-order-details-addresses {
            li {
                padding: 15px 0 15px 20px;
                position: relative;
                overflow: visible;
                direction: rtl;

                .address-actions {
                    left: 0;
                    @extend .stickCenterV;
                }
                .edit {

                    i {
                        background: image-url("images/edit-button.png")no-repeat;
                        width: 16px;
                        height: 16px;

                        &:before {
                            display: none;
                        }

                        &:hover {
                            background: image-url("images/edit-button-hover.png")no-repeat;
                        }
                    }
                }
            }
        }
    }
}

.checkout-address-choose-shipping {

    h2 {
        font-size: 14px;
    }

    .use-shipping-as-billing {
        margin: 10px 0 -5px 0;
    }

    .order-details-city-shipping {
        input {
            width: 100%;
        }
    }
}

.shipping-box {
    padding: 20px;
    box-shadow: 0 2px 3px rgba(1,1,1,.25);

    + .btn-cancel {
        margin-right: 15px;
    }

    .shipping-box-header {
        margin: 0;
        margin-bottom: 15px;
    }

    .checkout-shipping-providers {
        margin-bottom: 0;
        li {
            position: relative;
            line-height: 31px;
            padding: 10px 0;
            border-top: 1px solid #dcdbdb;

            img {
                width: 30px;
            }
            label {
                margin-bottom: 0;
            }

            &:last-of-type {
                border-bottom: 0;
            }
            .bracketing {
                //display: inline;
            }
        }
    }
}


.checkout-address-holder, .checkout-order-note {
    margin-bottom: 15px;

    .address-container {
        display: inline-block;
        box-shadow: 0 2px 3px rgba(1,1,1,.25);
        padding: 20px;
        width: 100%;

        h2 {
            margin-bottom: 15px;
        }
    }

    .checkout-order-container {
        box-shadow: 0 2px 3px rgba(1,1,1,.25);
        padding: 20px;

        .checkout-payment-providers {
            margin-bottom: 0;

            li {
                border-top: 1px solid #dcdbdb;
                padding: 10px 0;

                label {
                    margin-bottom: 0;
                }

                &:last-child {
                    padding-bottom: 0;
                }
            }
        }
    }

    .checkout-address {
        border-top: 1px solid #dcdbdb;
        margin: 10px 0;
    }

    .checkout-addresses {
        padding: 0 15px;
        border: 1px solid #dcdbdb;
    }

    .checkout-address-shipping {
        overflow: hidden;
    }

    h2 {
        margin-top: 0;
    }

    h3 {
        margin-right: 5px;
    }
    .checkout-address-holder {
        border: 0;
        margin-bottom: 0;

        .address-details {
            margin: 0;

            .titles-list {
                display: none;
            }

            ul {
                display: inline-block;
                text-align: left;
                margin-bottom: 0;
                > li {
                    float:left;
                    line-height: 30px;
                    margin-right: 15px;

                    &:first-child {
                        margin-left: 0;
                    }

                    &:last-of-type {
                        border-bottom: 0;
                    }
                }
                &.values-list {
                    float:left;
                    margin: 13px 0 0 7px;

                    li {
                        display: inline-block;
                        margin-left: 5px;

                        &:first-child {
                            margin-left: 0;
                        }
                    }
                }
            }

            .address-list {
                overflow: hidden;

                li {
                    span {
                        &:nth-child(2){
                            margin-left: 10px;
                        }
                    }
                }
            }
        }
        .checkout-address-edit {
            display: none;
            text-align: right;
            padding-top: 20px;
        }
    }

    textarea {
        display: block;
        background-color: #fffad6;
        border: 1px solid #f5eac5;
        border-bottom: 3px solid #f5eac5;
        margin-top: 30px;
        max-width: 100%;

        &:focus {
            border-color: #ede0b6;
        }
    }
}


.cart-settings-js {
    padding: 0 20px 20px 20px;
    .cart-discount-code {
        position: relative;
        input {
            padding-left: 60px;
        }
        .cart-discount-code-submit-holder {
            position: absolute;
            bottom: 0;
            left: 0;

            .btn {
                padding: 0 10px 0 10px;
            }
        }
    }
}

#CheckoutFormsJs {
    margin-top: -1px;
    padding-left: 2px;

    div.form-group {
        display: inline-block;
        width: 48.5%;

        input {
            height: 40px;
        }
    }

    div.form-group:nth-child(2n+1){
       margin-left: 2% !important;
    }

}

.checkout-authorize {
    #CheckoutRegister, #CheckoutLogin, #CheckoutGuest {
        padding: 15px;
        box-shadow: 0 2px 3px rgba(1,1,1,.25);

        h3 {
            background-position: center top;
            background-repeat: no-repeat;
            text-align: center;
            margin: 10px 0 15px 0;
            padding-bottom: 15px;
            border-bottom: 1px solid #dcdbdb;
            font-size: 14px;

        }
        .form-group {
            margin-left: 0;
            margin-right: 0;
        }

        .user-login-actions-box, .user-register-submit-holder, .user-register-login-holder {
            .btn {
                width: 100%;
                text-align: center;
            }
        }
    }

    #CheckoutRegister , #CheckoutGuest  {
        h3 {
            display: none;
        }

        .btn-edit {
            display: none;
        }
    }

    #CheckoutRegister {
        padding-bottom: 3px;
    }
}

.padding-none {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.user-controls {
    height: 49px;
    line-height: 47px;

    .user-controls-account {
        position: relative;
    }

    a {
        display: block;
        min-width: 105px;
        height: 49px;
        float:left;
        padding: 0 15px;
        margin-left: -1px;
    }

    .user-controls-logout , .user-controls-login {
        position: relative;
    }
}

.tabs {
    position: relative;
    padding-right: 15px;

    &-nav {
        position: relative;
        z-index: 2;

        & + section {
            z-index: 1;
        }
    }

    ul {
        overflow: hidden;
        margin-bottom: 0;

        li {
            float: left;
            a {
                padding: 20px;
                margin-bottom: 0;
                display: block;
                background: #fff;
            }
        }

        li.current {
            a {
                box-shadow: 0 2px 3px rgba(1,1,1,.25);
                border: 1px solid transparent;
            }
        }
    }

    .form-group {
        margin-bottom: 20px;
    }

    .help-block-error {
        position: absolute;
    }
}

.checkout-address-choose-type {
    label {
        margin-left: 20px;
    }
}

.checkout-return {
    max-width: 700px;
    margin: 0 auto;
    padding: 50px 0 100px;

    .checkout-return {
        padding: 0;
    }

    @media (max-width: $break-sm) {
        padding: 20px 0;
    }
}

.checkout-return-requested {
     h2 {
         font-size: 36px;
         color: #666;
     }

     p {
         font-size: 18px;
         margin: 30px 0;
         color: #666666;
     }
 }

.checkout-return-completed {
    h2 {
        font-size: 36px;
        color: #666;
    }

    p {
        font-size: 18px;
        margin: 30px 0;
        color: #666666;
    }

    a {
        font-size: 14px;
        color: #ffffff;
        background: #000000;
        padding: 15px 70px;
        display: inline-block;

        &:hover {
            color: #ffffff;
        }
    }
}

.amount-to-free-shipping {
    border-bottom: 1px solid #dbdbdb;
    width: 100%;
    display: block;
    padding-bottom: 20px;
}

.cart-totals , .cart-submit {
    margin-right: 0;
}

.checkout-address {
    .btn.btn-cancel {
        margin-top: 20px;
    }
}

.btn.btn-cancel {
    display: block;
    padding: 0 20px;
    height: 40px;
    border: 0 !important;

    &:hover {
    }
}

.checkout-shipping {
    .btn.btn-cancel {
        margin-top: 15px;
        margin-right: -15px;
    }

    .btn-primary {
        margin-top: 15px;
    }
}

.checkout-address-holder {
    display: block;
}

.checkout-verify {
    .checkout-address-holder {
        margin-left: 0;
    }

    .checkout-cart-totals {
        margin-top: 20px;
    }

    .cart-products-subtotal-box {
        &.shipping {
            position: relative;
            padding-bottom: 45px;
            .btn {
                position: absolute;
                left: 0;
                bottom: 0;
            }
        }
    }
    .checkout-cart-holder {
        .checkout-cart-product-list {
            .cart-product {
                .cart-product-quantity {
                    display: inline-block !important;
                    position: relative;
                    &:after {
                        content: 'x';
                        display: block;
                        position: absolute;
                        left: -15px;
                        bottom: 0;
                    }
                }
            }
        }
    }
}

@media (max-width: $break-md) {
   .checkout-verify {
        margin-left: 0;
        margin-right: 0;

        .checkout-address-holder {
            margin-left: 0;
        }

        .verify-products {
            margin-right: -15px;
        }

        .checkout-order-container {
            margin-right: 0;
        }

        .checkout-cart-totals , .cart-submit {
            margin-left: 0;
            margin-right: 0;
        }
    }
    .shipping-left {
        .shipping-box {
            margin-left: 0;
        }

        .btn-primary {
            margin-top: 20px;
        }
    }

    .shipping-right {
        .cart-totals, .cart-submit {
            margin-right: 0;
        }
    }


    #orderAddressChooseJs {
        .checkout-address-choose-shipping {
            margin-left: 0;
        }

        .cart-totals, .cart-submit {
            margin-right: 0;
        }

        .shipping-box {
            margin-left: 0;
        }
    }

    .cart-estimate {
        margin-top: 15px;
    }
    
    .checkout-shipping {
        .btn.btn-cancel {
            margin-left: 0;
        }
    }

    .checkout-address-holder , .verify-products {
        padding-right: 30px;
    }

    .checkout-order-note , .verify-prizes {
        padding-left: 30px;
    }

    .checkout-order-note , .verify-prizes {
        padding-left: 15px;
    }

    .checkout-address-holder, .verify-products {
        padding-right: 15px;
    }

    .checkout-cart-totals {
        margin-top: 20px;
    }

    .tabs {
        margin-top: 20px;
        padding: 0;
    }

    .cart-totals {
        margin-top: 20px;
    }

    .checkout-return-completed {
        h2 {
            font-size: 26px;
        }
    }
}

#addressForm {
    .btn-cancel {
        padding-left: 15px;
    }
}

@media (max-width: $break-md) {
    .checkout-address-choose-billing {
        margin-left: 0;
    }

    .cart-holder, .checkout-cart-holder {
        .cart-product-list, .checkout-cart-product-list {
            .cart-product {
                .cart-image-box {
                    .cart-product-remove {
                        opacity: 1;
                    }
                }
            }
        }
    }
}

@media (min-width: $break-sm + 1px) {
    .navcart {
        &:hover {
            .navcart-information {
                display: block;
            }
        }
    }
}
@media (max-width: $break-sm) {
    .cart-holder, .checkout-cart-holder {
        .cart-product-list, .checkout-cart-product-list {
            .cart-product {
                .cart-image-box {
                    .cart-parameter-box {
                        .parameter-holder {
                            .parameter {
                                display:block;
                            }
                        }
                    }
                }
            }
        }
    }
    
    .user-controls {
        a {
            margin-left: 0;
        }
    }

    .checkout-address-choose-type {
        label + label {
            margin-left: 0;
        }
    }

    #CheckoutFormsJs div.form-group:nth-child(2n+1) {
        margin-left : 0 !important;
    }

    #CheckoutFormsJs div.form-group {
        width: 100%;
    }

    .tabs {
        ul {
            li {
                a {
                    padding: 8px;
                }
            }
        }
    }

    .cart-holder, .checkout-cart-holder {
        .cart-product-list, .checkout-cart-product-list {
            .cart-product {
                .cart-product-quantity {
                    margin-bottom: 0;

                    .cart-product-out-of-stock {
                        display: none;
                    }
                }

                .cart-product-single-price {
                    font-size: 16px;
                    padding-top: 10px;
                    del {
                        position: relative;
                        display: block;
                        margin-top: -10px;
                    }
                }
            }
        }
    }


    .checkout-return-completed {
        h2 {
            font-size: 22px;
        }
    }
}

.user-register-js {
    .user-register-submit {
        height: 40px;
    }
}

.cart-submit-terms {
    margin-bottom: 14px;
}

@media (max-width: $break-lg) {
    .checkout-steps {
        .checkout-step {
            display: none;
        }

        .checkout-step:after {
            display: none;
        }

        .checkout-step.active {
            &, & + .checkout-step {
                display: inline-block;
            }

            &:after {
                display: inline-block;
            }
        }
        .checkout-step-complete.completed {
            display: inline-block;
        }
    }
}

@media (max-width: $break-md) {
    .shipping-box {
        margin-top: 20px;
    }
}

@media (max-width: $break-sm) {
    .checkout-steps {
        .checkout-step {
            display: none;
        }

        .checkout-step:after {
            display: none;
        }

        .checkout-step.active {
            & + .checkout-step {
                display: none;
            }

            &:after {
                display: none;
            }
        }
    }
}

@media (max-width: $break-sm - 1px) {
    .cart-holder, .checkout-cart-holder {
        .cart-product-list, .checkout-cart-product-list {
            .cart-product {
                .cart-image-box {
                    //margin-bottom: 5px;
                    .image-holder {
                        position: relative;
                        right: 0;
                        top: 0;
                        float: right;
                        margin-left: 20px;
                    }
                    .cart-parameter-box {
                        padding-right: 130px;
                        h4 {
                            padding-left: 25px;
                        }
                    }
                }
                .cart-product-quantity {
                    padding-right: 0;
                    float: right;
                }
                .cart-product-single-price {
                    //margin-left: 135px;
                    margin-left: 0;
                    float: left;
                    position: static;
                }
            }
        }
    }
    .checkout-shipping-providers {
        li {
            label {
                display: block;
                float: none !important;
            }
        }
    }
    .checkout-address-holder {
        border: 0;
        margin-bottom: 0;

        .checkout-address-holder {
            padding-right: 0;
        }

        .address-details {
            ul {
                display: block !important;
                li {
                    display: block;
                    float: none !important;
                    margin-right: 0 !important;
                    font-size: 12px;
                    &:after {
                        content: '';
                        display: block;
                        clear: both;
                    }
                    .address-title {
                        float: left;
                    }
                    .address-value {
                        float: right;
                    }
                }
            }
        }
    }
}

@media (max-width: $break-ls) {
    .cart-holder, .checkout-cart-holder {
        .cart-product-list, .checkout-cart-product-list {
            .cart-product {
                padding: 10px;
                .cart-image-box {
                    .image-holder {
                        margin-left:20px;
                    }
                    .cart-parameter-box {
                        padding-right: 120px;
                        display: block;
                    }
                    .cart-product-remove {
                        top: 10px;
                        left: 10px;
                    }
                }
                .cart-product-single-price {

                }
                //.cart-product-quantity {
                //    float: none;
                //    display: block;
                //    margin:0 auto;
                //}
                //.cart-product-single-price {
                //    float: none;
                //    display: block;
                //    margin:0 auto;
                //}
            }
        }
    }
}
@media (max-width: 400px) {
    .tabs {
        ul {
            li {
                a {
                    white-space: nowrap;
                    width: 130px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}

.cart-container {
    display: block;
    height: 99%;
    margin-left: -15px;
    margin-right: -15px;
}

.navcart-additional-info {
    text-align: left;
    padding: 2px 0 0 50px;
    p {
        font-size: 12px;
        margin: 0;
    }

    p.cart-total-price {
        font-size: 14px;
    }
}

.btn-cancel {
    text-align: center;
}

.cart-discount-code-remove {
    display: table;
    margin-bottom: 10px;
}

@media (max-width: $break-ls) {
    .checkout-address-forms {
        input {
            float: none;
            width: 100%;
            margin-left: 0 !important;
        }
    }
}