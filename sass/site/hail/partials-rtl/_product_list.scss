.left-box {
     section {
        padding: 15px 0;
        margin-bottom: 30px;

         &:last-of-type {
             margin-bottom: 0;
         }

        .top-title {
            display: none;
        }

        .info-box {
            .box-title {
                margin-bottom: 8px;
                display: block;
            }

            p {
                margin-bottom: 35px !important;
            }
        }

        li {
            position: relative;
            &:last-of-type {
                border-bottom: 0;
                padding-bottom: 0;
            }
            &.active {
                .product-listing-filter-remove, .blog-filter-remove {
                    background-repeat: no-repeat;
                    background-position: center center;
                    background-image: image-url("images/remove-button.png");
                    text-indent: -9999px;
                    position: absolute;
                    top: 13px;
                    left: 15px;
                    display: block;
                    width: 15px;
                    height: 15px;
                }
            }
            a {
                padding: 10px 0;
                display: inline-block;
                width: 90%;
            }
            .collapse-icon {
                @include hide-text;
                background-repeat: no-repeat;
                background-position: center center;
                width: 10px;
                height: 10px;
                cursor: pointer;
                position: absolute;
                top: 15px;
                right: 0;
            }
            > ul > li {
                > a {
                    &:before {
                        content: "";
                        display: inline-block;
                        margin: 0 7px 2px 0;
                        width: 7px;
                        height: 7px;
                        @include border-radius(50%);
                    }
                }
                > ul > li > a {
                    padding-left: 15px;
                }
            }
        }
        .form-control, .btn {
            margin-top: 15px;
        }
    }
}

.product-list-container {
    position: relative;
    min-height: 400px;

    .loader {
        margin-top: -50px;
    }
}

.filters-wrapper {
    .col-md-2,
    .col-md-1 {
        margin-bottom: 10px;
    }
}

.separator-border {
    margin-top: 10px;
}

.category-properties {
    padding-top: 20px;
}

.category-property {
    float: right;
    margin-left: 15px;
    width: calc(16.6667% - 15px);
    width: -webkit-calc(16.6667% - 15px);
    margin-bottom: 10px;
    position: relative;

    &:nth-child(6n+1) {
        margin-right: 15px;
        width: calc(16.6667% - 30px);
        width: -webkit-calc(16.6667% - 30px);
    }

    &:hover {
        .category-property-form {
            opacity: 1;
            visibility: visible;
        }
    }
}

.category-property-title {
    padding: 0 15px;
    position: relative;

    &:after {
        content: '';
        position: absolute;
        top: 50%;
        left: 6px;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 4px solid #333;
    }

    h3 {
        font-size: 14px;
        height: 40px;
        margin: 0;
        line-height: 40px;
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.category-property-form {
    padding: 15px 15px 10px;
    position: absolute;
    top: calc(100% - 1px);
    top: -webkit-calc(100% - 1px);
    left: 0;
    right: 0;
    z-index: 1;
    opacity: 0;
    visibility: hidden;
    @include transition(.2s);

    ul {
        list-style-type: none;
        margin-bottom: 0;
    }

    li {
        margin-bottom: 5px;
    }

    label {
        margin-bottom: 0;
    }
}

.mobile-slide {
    margin-bottom: 20px;
    position: relative;
    z-index: 5;
}

@media (max-width: $break-md) {
    .mobile-slide {
        position: fixed;
        height: 100%;
        overflow: auto;
        width: 60%;
        top: 0;
        padding: 0;
        left: -100%;
        z-index: 12;
        @include box-shadow(0px 0px 5px rgba(0,0,0, .5));
        @include transition(all, .5s);

        .row {
            margin: 0;
        }

        .filters-wrapper {
            .no-padding-left {
                padding-right: 15px !important;
            }

            .filters {
                margin: 10px 0;
                p {
                    padding-right: 20px;
                }
            }
        }


        > section {
            border: 0 !important;

            &:first-of-type {
                .top-title {
                    padding-right: 20px;
                }
            }
        }
        &.active {
            left: 0;
            .mobile-slide-toggle {
                background-image: url('../../layouts/hail/images/mobile-slide-toggle-back.png');
                background-position: center center;
                background-repeat: no-repeat;
                width: 13px;
                height: 23px;
                display: block;
                position: absolute;
                top: 13px !important;
                left: 15px;
                margin: 0;
                z-index: 10;
            }
        }
    }

    .mobile-slide-toggle {
        display: block;
    }

    .category-properties {
        padding-top: 0;
    }

    .category-property {
        float: none;
        width: auto;
        margin-right: 15px;

        &:nth-child(6n+1) {
            width: auto;
        }
    }

    .category-property-form {
        margin-top: -1px;
        position: static;
        opacity: 1;
        visibility: visible;
    }

    .separator-border {
        display: none;
    }
}

.right-box {
    .title {
        position: relative;
        margin-top: 0;
        margin-bottom: 0;

        h1 {
            padding-left: 0;
        }
    }

    .about-info {
        margin-bottom: 30px;
    }

    .form-group {
        input {
            height: 40px;
        }

        textarea {
            height: 100px;
        }
    }

}

@media (max-width: $break-md) {
    .left-box section {
        margin: 0;
        padding: 0;
    }
}

div.product-list {
    list-style: none;

    .product-box {
        div.product {
            display: inline-block;
            width: 100%;
            position: relative;
            border-width: 1px 1px 1px 1px;
            border-color: transparent;
            border-style: solid;
            //@include transition(all, 0.7s);
            margin-bottom: 30px;
            height: 395px;

            @media (max-width: $break-sm) {
                height: auto;
                padding-bottom: 15px;
            }

            &:hover {
                box-shadow: 0 2px 3px rgba(1,1,1,.25);
                //@include transition(all, 0.7s);

                .product-image {
                    box-shadow: none;
                    @include transition(all, 1s);
                }

                border-width: 1px 1px 1px 1px;
            }

            .description, .category, .vendor, .discount {
                display: none;
            }

            &:hover {
                .overlay, .quick-view {
                    display: block;
                }
            }

            .product-image {
                position: relative;
                display: block;
                width: 100%;
                height: 250px;
                @include transition(all, 1s);
                overflow: hidden;
                text-align: center;
            }

            img {
                max-width: 100%;
                max-height: 100%;
                position: absolute;
                top: 0;
                bottom: 0;
                right: 0;
                left: 0;
                margin: auto;
            }
            
            .product-ribbon-banner {
                position: absolute;
                z-index: 3;

                img {
                    max-width: initial;
                    max-height: initial;
                    position: static;
                    top: auto;
                    left: auto;
                    right: auto;
                    bottom: auto;
                    margin: 0;
                }
            }

            .name {
                width: 100%;
                display: block;
                padding: 0 10px 0 15px;
                margin: 15px 0 10px 0;
                height: 36px;
                font-weight: $medium;
                font-size: $third-font-size;
                overflow: hidden;
                text-align: right;

                @media (max-width: $break-sm) {
                    height: auto;
                }

                &:hover {
                    text-decoration: none;
                }
            }

            .sale, .new {
                position: absolute;
                top: 10px;
                padding: 0 10px;
                font-size: $third-font-size;
                line-height: 25px;
                z-index:2;
                background: #fff;
                pointer-events: none;
                &:after {
                    content: "";
                    width: 0;
                    height: 0;
                    position: absolute;
                    top: 28px;
                    bottom: 0;
                    margin: auto;
                }
            }

            .sale {
                top: 40px;
                left: 10px;
            }

            .sale.alone {
                top: 10px;
            }

            .new {
                left: 10px;
            }
            .price-row {
                @extend %flexbox;
                @include flex-flow(row wrap);
                @include justify-content(space-between);
            }

            .add {
                @extend %flexbox;
                line-height: 40px;
                text-align: center;
                padding: 0 5px;
                margin-left: auto;

                a {
                    font-size: 10px;
                    text-decoration: none;
                    display: block;
                    padding: 0 10px;
                    line-height: 25px;
                    height: 25px;
                    @include transition(all, .3s);
                    &:hover {
                        @include opacity(.6);
                    }
                }
            }

            .discount-box {
                display: block;
                position: absolute;
                line-height: 25px;
                top: 213px;
                background: #fff;
                left: 10px;
                font-size: 12px;
                text-transform: uppercase;
                padding: 0 10px;
                z-index: 2;

                &-leasing {
                    left: auto;
                    right: 10px;
                }

                p {
                    margin-bottom: 0;
                }
            }
            .description {
                display: none;
            }
            .price, .price_compare {
                text-align: left;
                direction: ltr;
                //width: 55%;
                display: block;
                font-size: $nav-font-size;
                line-height: 1.2;
                padding-bottom: 10px;
                margin: 0 10px 0 15px;
            }
            .price_compare {
                del {
                    margin-left: 3px;
                    font-size: $base-font-size;
                    text-decoration: line-through;
                    display: block;
                }
            }
            .overlay {
                position: absolute;
                z-index: 1;
                display: none;
                width: 100%;
                height: 100%;
                text-align: center;
                @include transition(all, .3s);
            }
            .quick-view {
                position: absolute;
                z-index: 10;
                top: 10px;
                right: 10px;
                margin: auto;
                display: none;
                height: 28px;
                width: 28px;
                line-height: 26px;
                text-align: center;

                i {
                    display: none;
                }

                span {
                    display: inline-block;
                    font: normal normal normal 14px/1 FontAwesome;
                    font-size: inherit;
                    text-rendering: auto;
                    -webkit-font-smoothing: antialiased;
                    font-size: 0;    

                    &:before {
                        font-size: 14px;
                        content: "\f06e";
                    }
                }

                &:hover {
                    @include opacity(.6);
                }
            }
        }
    }
}

.extra-contact-form-js {
    #submit {
        height: 40px;
        width: 55%;
    }
}

.product-description {
    margin-bottom: 30px;
    .products-top-info-box {
        @extend %flexbox;
        @include flex-flow(row);

        figure {
            @include flex(1 1 150px);
            min-width: 150px;
            max-width: 150px;
            margin-left: 20px;

            img {
                display: block;
                max-width: 100%;
                max-height: 100%;
            }
        }

        .html-content-container-js {
            @include flex(1 1 auto);
        }

    }
}

.filters,
.vendors-list{
}

.filters {
    min-height: 38px;
    @extend %flexbox;
    @include align-items(center);
}

.separator-border {
    margin-top: 20px;
}

.filters , .vendor-dropdown-link , #price_filter_control {
    p {
        margin-bottom: 0;
    }
}

.price-static {
    position: relative;
}

.price-range-filter {
    height: 40px;
    border-width: 1px;
    border-style: solid;

    #price_filter_control {
        line-height: 37px;
        display: block;
        padding: 0 30px 0 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 14px;
        margin-right: 0;
        position: relative;
        &:after {
            content: '';
            display: block;
            border-top: 4px solid #333;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            position: absolute;
            right: 5px;
            top: 50%;
            -webkit-transform: translateY(-50%);
            -moz-transform: translateY(-50%);
            -ms-transform: translateY(-50%);
            -o-transform: translateY(-50%);
            transform: translateY(-50%);
        }
    }
}

#price_filter_holder {
    padding: 15px;
    position: absolute;
    left: 15px;
    right: 15px;
    top: 44px;
    z-index: 12;
    text-align: center;
    border-top-width: 0;
    @include box-shadow(0px 4px 5px 0px rgba(black, .1));
    #price_filter {
        margin: 0 10px;
        background-image: none;
        height: 8px;
        border: 0 none;
        div {
            top: 1px;
        }
        a {
            width: 15px;
            height: 15px;
            top: -3px;
            @include border-radius(50%);
        }
    }
    .control-label {
        display: block;
        padding: 5px 0;
    }
    #price_range {
        display: block;
        font-size: 15px;
        margin-bottom: 10px;
    }
    #apply_price_filter {
        font-size: 14px;
        padding: 10px;
        margin-top: 20px;
        display: block;
        @include transition(all .3s ease);
    }
}

@media (max-width: $break-sm) {
    #price_filter_holder {
        padding: 15px;

        .control-label {
            display: none !important;
        }
    }
}

.vendor-dropdown {
    display: none;

    ul {
        position: absolute;
        top: 42px;
        left: 0;
        padding: 30px;
        width: 100%;
        box-shadow: 0 2px 3px rgba(1,1,1,.25);
        max-height: 220px;
        overflow: hidden;
        z-index: 11;

        li {
            width: 24%;
            float: left;
        }
    }
}

.vendor-dropdown.active {
    display: block;
}

.left-box.mobile-slide.active {
    .filters , .vendors-list {
        margin-left: 10px;

        &:after {
            content: "";
        }
    }

    .vendor-dropdown {
        ul {
            position: absolute;
            top: -155px;
            left: 0;
        }
    }
}

/* category properties rtl */
.category-property-image,
.category-property-color {
    .category-property-form {
        padding-bottom: 15px;

        ul {
            margin-right: -1%;
            margin-bottom: -1%;
            @extend .clearfix;

            li {
                float: right;
                margin-right: 1%;
                margin-bottom: 1%;

                + li {
                    margin-top: 0;
                }
            }
        }

        ._checkbox {
            display: block;
            height: 0;
            padding: 0;
            font-size: 0;
            line-height: 0;
            position: relative;
            overflow: hidden;
            word-break: break-word;
            cursor: pointer;

            .checker {
                display: none;
            }
        }
    }
}

.category-property-image {
    .category-property-form {
        li {
            width: 32.3%;
            max-width: 100px;
        }

        ._checkbox {
            border: 1px solid;
            padding-bottom: -webkit-calc(100% - 2px);
            padding-bottom:         calc(100% - 2px);

            &.active {
                border-width: 2px;
                padding-bottom: -webkit-calc(100% - 4px);
                padding-bottom:         calc(100% - 4px);
            }

            img {
                position: absolute;
                top: 50%;
                left: 50%;
                -webkit-transform: translate(-50%, -50%);
                        transform: translate(-50%, -50%);
            }
        }
    }
}

.category-property-color {
    .category-property-form {
        li {
            width: 15.66%;
            max-width: 40px;
        }

        ._checkbox {
            border: 2px solid transparent;
            padding-bottom: -webkit-calc(100% - 4px);
            padding-bottom:         calc(100% - 4px);
        }
    }
}

@media (max-width: $break-md) {
    .extra-contact-form-js {
        #submit {
            width: 100%;
        }
    }

    .left-box.mobile-slide {
        .no-padding-right {
            padding-right: 15px !important;
        }
    }

    .left-box.mobile-slide.active {
        .filters , .vendors-list {
            margin-left: 0;
        }
    }
    .price-range-filter {
        margin-bottom: 10px;
    }

    .left-box.mobile-slide.active .vendor-dropdown ul {
        top: 125px;
    }

    #price_filter_holder {
        top: 163px;
    }
}

@media (max-width: $break-sm) {
    .vendor-dropdown {
        display: none;

        ul {
            box-shadow: 2px 1px 2px 2px rgba(1, 1, 1, 0.25);
            padding: 15px;

            li {
                width: 100%;
                float: left;
                margin: 5px 0;
            }
        }
    }
}

@media (max-width: $break-ls) {
    .product-description {
        .products-top-info-box {
            @include flex-flow(column);
            @include align-items(center);

            figure {
                @include flex-basis(auto);
                min-width: inherit;
                max-width: none;
                margin: 0 0 20px;
            }
        }
    }

    div.product-list {
        .product-box {
            div.product {
                .name {
                    text-align: center;
                }
                .price-row {
                    @include flex-flow(column);
                    @include align-items(center);
                }
            }
        }
    }
}

@media (max-width: $break-xs) {
    .extra-contact-form-js {
        #submit {
            width: 100%;
        }
    }
}
