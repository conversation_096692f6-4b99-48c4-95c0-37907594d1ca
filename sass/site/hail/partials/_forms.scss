input[type="text"], input[type="password"], textarea, .form-control {
    height: 40px;
    @include border-radius(0px);
    box-shadow: none;
    &:focus {
        box-shadow: none;
    }
}

.page-products {
    .select2-container.form-control {
        //border: 0;
    }
}

input , textarea , .select2-chosen {
    @include input-placeholder {
        color: #999999;
        font-size: 12px;

        &:first-letter {
            text-transform: capitalize;
        }
    }
}

.order-details-region .select2-container._select {
    margin-bottom: 0;
}

.checkout-address-forms .order-details-city-shipping input {
    margin-left: 0 !important;
}

.select2-dropdown-open.select2-drop-above {

    .select2-choice, .select2-choices {
        background-image: none;
    }
}

.select2-container {
    outline: none;
    padding: 0;

    &._select  {
        width: 100%;

        .select2-choice .select2-arrow {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;

            b {
                height: 15px;
            }
        }

        .select2-chosen {
            height: 33px;
            line-height: 33px;
        }

        .select2-choice {
            padding: 0 10px;
        }
    }

    .select2-choice {
        @include box-shadow(none);
        @include border-radius(0px);
        line-height: 19px;
        height: 100%;
        border: none;
        padding: 0 0 0 10px;
        .select2-arrow {
            @include border-radius(0px);
            border-left: none;
            background: transparent;

            &:after {
                content: '';
                display: block;
                border-top: 4px solid #333;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                position: absolute;
                left: 50%;
                top: 50%;
                -webkit-transform: translate(-50%, -50%);
                -moz-transform: translate(-50%, -50%);
                -ms-transform: translate(-50%, -50%);
                -o-transform: translate(-50%, -50%);
                transform: translate(-50%, -50%);
            }

            b {
                display: none;
            }
        }
        .select2-chosen {
            height: inherit;
            line-height: 38px;
            color: #333333 !important;
            font-size: 14px !important;
            text-transform: uppercase;
        }
    }
}

.price-static {
    height: 40px;
    line-height: 38px;
    display: block;
    padding: 0 5px;
    white-space: nowrap;
    //overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    font-size: 14px;
    border-width: 1px;
    border-style: solid;
    @media (max-width: $break-lg) {
        margin-bottom: 15px;
    }
}

#price_filter_holder {
    padding: 15px;
    position: absolute;
    left: 15px;
    right: 15px;
    top: 44px;
    z-index: 12;
    text-align: center;
    border-top-width: 0;
    @include box-shadow(0px 4px 5px 0px rgba(black, .1));
    #price_filter {
        margin: 0 10px;
        background-image: none;
        height: 8px;
        border: 0 none;
        div {
            top: 1px;
        }
        a {
            width: 15px;
            height: 15px;
            top: -3px;
            text-transform: uppercase;
            @include border-radius(50%);
        }
    }
    .control-label {
        display: block;
        padding: 5px 0;
        text-transform: uppercase;
    }
    #price_range {
        display: block;
        font-size: 15px;
        margin-bottom: 10px;
    }
    #apply_price_filter {
        font-size: 14px;
        padding: 10px;
        margin-top: 20px;
        display: block;
        text-transform: uppercase;
        @include transition(all .3s ease);
    }
}

.input-group-btn {
    .bootstrap-touchspin-down , .bootstrap-touchspin-up {
        height: 40px;
        border: 0;
    }
}

.select2-container-active {
    .select2-choice {
        .select2-arrow {
            b {
                background-color:none;
            }
        }
    }
}

.select2-drop-active {
    @include border-radius(0px);
}
.select2-container .select2-choice abbr {
    top: 12px;
}
.select2-container .select2-choice .select2-arrow b {
    background-position: 0 -4px;
}

.select2-results {

    li {
        white-space: normal;
        word-break: break-word;
    }

    .select2-suboption {

        &:hover, &.select2-highlighted {

            .select2-result-label:before {
                border-left: 4px solid #fff;
            }
        } 

        .select2-result-label  {
            position: relative;
            padding-left: 10px;

            &:before {
                position: absolute;
                content: '';
                left: 0;
                top: 9px;
                margin: auto;
                width: 0;
                height: 0;
                border-top: 4px solid transparent;
                border-bottom: 4px solid transparent;
                border-left: 4px solid #000;
            }
        }
    }

    .select2-suboption1 {
        padding-left: 15px;
    }

    .select2-suboption2 {
        padding-left: 25px;
    }

    .select2-suboption3 {
        padding-left: 35px;
    }

    .select2-suboption4 {
        padding-left: 45px;
    }
}


.checkbox, .radio {
    > label {
        padding: 0;
    }
}

.radio,
.checker {
    &.has-error {
        span {
            span {
                display: none;
            }
        }
    }
}

.select2-container .select2-choice {
    background: none;
}

.region-holder {
    margin-bottom: 15px;
}

.form-group-labels-horizontal {
    label {
        margin-right: 10px;
    }
}