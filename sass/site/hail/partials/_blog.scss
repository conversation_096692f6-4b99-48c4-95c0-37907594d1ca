.blog-blogs,
.blog-articles-recent,
.blog-articles-comments-recent {
    overflow: visible;
    max-height: none !important;
    margin-bottom: 0;

    li {
        padding: 10px !important;
        overflow: hidden;

        a {
            width: 100%;
            padding: 0 !important;
        }
        .image-holder {
            display: block;
            @include flex(none);
            padding-bottom: 5px;

            img {
                display: block;
                max-width: 100%;
            }
        }

        .image-info-holder {
            max-width: 100%;
            @include flex(1 1 auto);

            span,
            time {
                display: block;
            }

            time {
                pointer-events: none;
                margin: 5px 0 0;
            }

        }
    }
}
.blog-blogs {
    li {
        position: relative;
        a {
            padding: 0 10px 0 0 !important;

            &.blog-filter-remove {
                display: block;
                position: absolute;
                right: 7px;
                top: 49% !important;
                padding-right: 0 !important;
                -webkit-transform: translateY(-50%);
                -moz-transform: translateY(-50%);
                -ms-transform: translateY(-50%);
                -o-transform: translateY(-50%);
                transform: translateY(-50%);
            }
        }
    }
}
.blog-blogs,
.blog-articles-recent {
    li {
        > a {
            @extend %flexbox;
            @include flex-flow(column nowrap);
            @include align-items(center);
            .image-holder {
                text-align: center;
            }
        }
    }
}

.blog-articles-comments-recent {
    li {
        @extend %flexbox;
        @include flex-flow(column nowrap);
        padding: 10px !important;
        overflow: hidden;
        .image-holder {

        }
    }
}

.blog-left {

    section {
        padding: 20px;
        box-shadow: 0 2px 3px rgba(1,1,1,.25);

        .top-title {
            display: block;
            font-size: 18px;
            margin: 0 0 10px 0;
            padding-bottom: 10px;
        }
    }
}

.right-box {
    .blog-articles {
        article {
            padding: 20px 0;
            margin-bottom: 30px;
            &:hover {
                .blog-article-info-holder {
                    .blog-articles-article-title:after {
                        width: 100px;
                    }
                }
            }
            .blog-article-image-holder {

            }
            .blog-article-image-holder, .blog-article-info-holder {
                float: none;
                width: 100%;
                max-width: 100%;
                margin-bottom: 15px;

                img {
                    max-width: 100%;
                    max-height: 100%;
                    box-shadow: 0 2px 3px rgba(1,1,1,.25);
                }
            }
            .blog-article-info-holder {
                .blog-articles-article-title {
                    display: block;
                    font-size: 14px;
                    text-transform: uppercase;

                    &:after {
                        content: "";
                        display: block;
                        width: 70px;
                        height: 3px;
                        margin-top: 10px;
                        @include transition(all, .2s);
                        @include border-radius(2px);
                    }
                }
                &:last-of-type {
                    margin-bottom: 0;
                }
                span {
                    font-size: 12px;
                    min-width: 32.5%;
                    display: inline-block;
                    padding: 5px 10px 5px 0;
                }
                .blog-articles-article-content {
                    padding-top: 10px;
                }
                .blog-articles-article-view-button {
                    float: right;
                }
            }
        }
    }
    & > article {
        margin-bottom: 30px;

        h1 {
            display: block;
            font-size: 14px !important;
            text-transform: uppercase;
            margin-bottom: 10px;
            margin-top: 0;
        }

        img {
            box-shadow: 0 2px 3px rgba(1,1,1,.25);
        }

        .tags-title {
            display: block;
            margin-bottom: 20px;
            span {
                margin-right: 10px;
            }
            .tags {
                display: inline-block;
                .tag {
                    display: inline-block;
                    margin: 10px 5px 0 0;
                    padding: 0 10px;
                    line-height: 30px;
                }
            }
        }
    }
    .blog-articles-article-comments {
        padding-top: 10px;
        article {
            margin-bottom: 20px;

            &:last-of-type {
                margin-bottom: 30px;
            }

            .text {
                display: block;
                padding: 10px 0;
                margin-top: 10px;
            }

            .info {
                img {
                    max-width: 50px;
                    height: auto;
                    display: block;
                    vertical-align: middle;
                    width: 50px;
                    overflow: hidden;
                    margin: 0 10px 10px 0;
                    box-shadow: 0 2px 3px rgba(1,1,1,.25);
                }
                time {
                    display: block;
                    text-align: right;
                }
            }
        }
    }
}

.single-article {
    padding: 10px 0 20px;

    > header {
        padding: 10px 0 5px;
        time,
        div {
            display: inline-block;
            min-width: 32.5%;
            padding-right: 10px;
        }
    }

    .html-content-container-js {
        margin-bottom: 20px;
    }

    figure {
        padding: 10px 0;
        img {
            display: block;
        }
    }
}

.comments-wrapper {
    margin-top: 20px;

    .title {
        margin-bottom: 10px;
    }
}

.blog-articles-article-image {
    display: block;
}

@media (max-width: $break-md) {
    .right-box {
        .blog-articles {
            article {
                .blog-article-info-holder {
                    > span {
                        width: 100%;
                    }
                }
            }
        }
    }

    .blog-left {
        section {
            -webkit-box-shadow: none;
            -moz-box-shadow: none;
            box-shadow: none;
        }
    }

}

