.user-controls {
	text-align: right;
}

.navcart-container {
	text-align: right;
}

.navcart {
	text-align: left;

	& > a {
		.button-icon {
			left: 0;
			right: auto;
		}
	}
}

.navcart-button {
	padding-left: 54px;

	.button-icon {
		right: 0;
	}
}

.navcart-item-notification {
	right: 0;
}

.navcart-information {
	right: 0;
}

.product-cart-product-link {
	padding: 0 15px 0 76px;

	.image-holder {
		left: 0;
	}
}

.product-cart-product-remove {
	right: 0;
}

.cart-product-remove {
	left: 0;
}

.search-form {
	button {
		right: 0;
	}

	.form-control {
		padding-right: 42px;
	}
}

.nav {
	text-transform: uppercase;

	li + li {
		&:before {
			left: -1px;
		}
	}

	.collapse-icon {
		border-left: 1px solid;
		right: 0;
	}

	@media (min-width: $break-device + 1) {
		ul {
			ul {
				left: 100%;
			}
		}
	}
}

.slide-text {
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 { text-transform: uppercase; }
}

.owl-prev {
	left: 0;
}

.owl-next {
	right: 0;
}

.owl-prev {
    &:hover {
        background: -moz-linear-gradient(left,  rgba(0,0,0,.5) 0%, rgba(0,0,0,0) 100%);
        background: -webkit-gradient(linear, left top, right top, color-stop(0%,rgba(0,0,0,.5)), color-stop(100%,rgba(0,0,0,0)));
        background: -webkit-linear-gradient(left,  rgba(0,0,0,.5) 0%,rgba(0,0,0,0) 100%);
        background: -o-linear-gradient(left,  rgba(0,0,0,.5) 0%,rgba(0,0,0,0) 100%);
        background: -ms-linear-gradient(left,  rgba(0,0,0,.5) 0%,rgba(0,0,0,0) 100%);
        background: linear-gradient(to right,  rgba(0,0,0,.5) 0%,rgba(0,0,0,0) 100%);
    }
}

.owl-next {
    &:hover {
        background: -moz-linear-gradient(left,  rgba(0,0,0,0) 0%, rgba(0,0,0,0.5) 100%);
        background: -webkit-gradient(linear, left top, right top, color-stop(0%,rgba(0,0,0,0)), color-stop(100%,rgba(0,0,0,0.5)));
        background: -webkit-linear-gradient(left,  rgba(0,0,0,0) 0%,rgba(0,0,0,0.5) 100%);
        background: -o-linear-gradient(left,  rgba(0,0,0,0) 0%,rgba(0,0,0,0.5) 100%);
        background: -ms-linear-gradient(left,  rgba(0,0,0,0) 0%,rgba(0,0,0,0.5) 100%);
        background: linear-gradient(to right,  rgba(0,0,0,0) 0%,rgba(0,0,0,0.5) 100%);
    }
}

.title {
	text-transform: uppercase;
}

.banner-image {
	img {
		left: 0;
	}
}

.navigation-footer {
	li {
		float: left;
		padding-right: 20px;
	}
}

.powered {
	text-align: right;
}

.payment-provider {
	margin-left: 10px;
}

.socials {
	li {
		margin-right: 5px;
	}
}

.newsletter-text {
    padding-right: 15px;
}

.newsletter-input {
    .help-block-error {
    	left: 0;
    }
}

.newsletter-button {
    padding-left: 10px;
}

.select2-container {
	&.select2-allowclear {
		.select2-choice {
			.select2-chosen {
				margin: 0 40px 0 0;
			}
		}
	}

	.select2-choice {
		padding: 0 0 0 15px;

		.select2-arrow {
			right: 0;
			left: auto;

			b {
				left: 0;
			}
		}

		& > .select2-chosen {
			margin: 0 24px 0 0;
		}

		abbr {
			right: 24px;
			left: auto;
		}
	}
}

.select2-results {
	.select2-suboption {
		.select2-result-label {
			padding-left: 10px;
			
			&:before {
				left: 0;
				content: '\f0da';
			}
		}
	}

	.select2-suboption1 {
		padding-left: 15px;
	}

	.select2-suboption2 {
		padding-left: 25px;
	}

	.select2-suboption3 {
		padding-left: 35px;
	}

	.select2-suboption4 {
		padding-left: 45px;
	}
}

.breadcrumb {
	li {
		&:after {
			padding: 0 6px 0 3px;
		}
	}
}

.modal-content {
	.product {
		.new,
		.sale {
			right: 10px;
		}
	}

	.close {
		right: 0;
	}
}

.ui-slider-horizontal {
	.ui-slider-handle {
		&:first-of-type {
			margin: 0 0 0 -1px;
		}

		&:last-of-type {
			margin: 0 0 0 1px;
			@include translate(-100%, 0);
		}
	}
}

#price_filter_control {
	padding-right: 25px;

	&:after {
		right: 12px;
	}
}

.products-filter {
	margin-left: -1%;
}

.products-filter-item {
	float: left;
	margin-left: 1%;
}

.products-filter-per-page {
	float: right;
}

.category-properties-inner {
	margin-left: -1%;
}

.category-property {
	float: left;
	margin-left: 1%;
}

.category-property-title {
	padding-right: 25px;

	&:after {
		right: 12px;
	}
}

.category-property-image,
.category-property-color {
    .category-property-form {
        ul {
            margin-left: -1%;

            li {
                float: left;
                margin-left: 1%;
            }
        }
    }
}

.product {
	.name {
		text-transform: uppercase;
	}

	.new,
	.sale {
		text-transform: uppercase;
		right: 15px;
	}

	.quick-view {
		text-transform: uppercase;
	}

	.featured,
	.discount-box {
		left: -5px;
	}
}

.product-details-js {
	.quantity-holder {
		.col-sm-9 {
			text-align: right;
		}
	}

	.share {
		.btn {
			&:before {
				margin-right: 5px;
			}
		}
	}

	.out-of-stock-js {
		left: 5px;
		text-transform: capitalize;
	}
}

.product-details-image {
	.product {
		.discount-box {
			left: 0;
		}
	}
}

.product-details-gallery {
	margin-left: -2%;

	li {
		float: left;
		margin-left: 2%;
	}
}

.product-details-title {
	text-transform: uppercase;
}

.product-details-price-js {
	.price-discount-js {
		text-transform: capitalize;
	}
}

.price_compare_price {
	margin-right: 5px;
}

.variant-sku-js {
	text-align: right;
}

._parameter-radio-values {
    margin-right: -3px;
}

._parameter-radio-value {
    float: left;
    margin-right: 3px;
}

._parameter-image-values {
    margin-left: -1%;
}

._parameter-image-value {
    float: left;
    margin-left: 1%;

    ._radio {
        .radio {
            left: 5px;
        }

        .radio-label {
            padding-left: 30px;
            padding-right: 5px;
        }
    }
}

._parameter-color-values {
    margin-right: -5px;
}

._parameter-color-value {
    float: left;
    margin-right: 5px;
}

.tag {
	margin-right: 5px;
}

._leasing-creditor-controls {
    margin-left: -15px;
}

._leasing-creditor-control {
    float: left;
    margin-left: 15px;
}

._leasing-creditor-control-box {
    padding-left: 48px;
    padding-right: 15px;
    text-align: left;

    .radio {
        left: 15px;
    }
}

._leasing-creditors {
    ._leasing-creditor {
        ._leasing-creditor-actions {
            text-align: right;
        }
    }
}

.product-vendors-by-letter-index {
	h3 {
		margin-right: 16px;
	}
}

.product-vendors-by-letter-index {
	li {
		margin-right: 18px;
	}
}

.product-vendors-by-letter {
	li {
		float: left;
	}
}

.products-top-info-box-image {
	float: left;
	margin-right: 10px;
}

.user-login-actions-box {
	.user-login-sign-in-holder {
		text-align: right;
	}

	.btn {
		&:first-letter {
			text-transform: capitalize;
		}
	}
}

.user-register-actions-box {
	.btn {
		&:first-letter {
			text-transform: capitalize;
		}
	}
}

.choice-box {
	.btn {
		text-transform: capitalize;
	}
}

.search-title {
	h4 {
		text-transform: uppercase;
	}
}

.search-results-no-results {
	h2 {
		text-transform: capitalize;
	}
}

.widget {
	h3 {
		text-transform: uppercase;
	}
}

.widget-categories {
	a {
		&:first-letter {
			text-transform: uppercase;
		}
	}
}

.widget-recent-comments {
	.image-holder {
		float: left;
		margin-right: 10px;
	}
}

.widget-recent-articles {
	.image-holder {
		float: left;
		margin-right: 10px;
	}
}

.widget-info-box {
	.box-title {
		text-transform: uppercase;
	}
}

.blog-article-content,
.about-container,
.widget-info-box,
.static-page-text,
.product-details-description,
.html-content-container-js {
	ul,
	ol {
		padding-left: 20px;
	}

	ul ul,
	ul ol,
	ol ol,
	ol ul {
		padding-left: 25px;
	}

	blockquote {
		&:before {
			left: 0;
		}
	}
}

.blog-article-date,
.blog-article-blog,
.blog-article-author {
	margin-right: 10px;
}

.blog-articles-article-blog,
.blog-articles-article-published,
.blog-articles-article-author {
	margin-right: 15px
}

.blog-articles-article-comments {
	.info {
		img {
			float: left;
			margin-right: 10px;
		}
	}
}

.mobile-slide {
	left: 0;
}

.mobile-slide-toggle-container {
	text-align: right;
}

.mobile-slide-toggle {
	text-align: right;
}

.extra-contact-form-js {
	::-webkit-input-placeholder { text-transform: capitalize; }
	:-moz-placeholder { text-transform: capitalize; }
	::-moz-placeholder { text-transform: capitalize; }
	:-ms-input-placeholder { text-transform: capitalize; }
}

.about-info {
	h3 {
		text-transform: uppercase;
	}
}

.account-tabs-nav {
	text-transform: uppercase;
}

.account-addresses {
	.name,
	.address,
	.postal-code,
	.phone,
	.actions {
		padding-right: 20px;
	}

	.actions {
		text-align: right;
		padding-right: 0;
	}
}

.confirm {
	.modal-footer {
		.col-xs-6:first-child {
			text-align: left;
		}

		.col-xs-6:last-child {
			text-align: right;
		}
	}
}

.account-orders {
	.actions {
		text-align: right;
	}
}

.order-product-image {
	float: left;
	margin-right: 10px;
}

.order-product-header-shipped,
.order-product-shipped {
	text-align: right;
}

.order-invoice-box {
	a {
		margin-right: 20px;
	}
}

.order-details-total-row {
	label {
		float: left;
	}

	span {
		float: right;
		text-align: right;
	}
}

.step-heading {
	text-transform: uppercase;
}

.cart-products-header {
	.name,
	.quantity,
	.single-price,
	.total-price {
		padding-right: 20px;
	}

	.single-price,
	.total-price {
		text-align: right;
	}

	.total-price {
		padding-right: 0;
	}
}

.cart-image-box {
	.image-holder {
		float: left;
		margin-right: 13px;
	}
}

.cart-image-box,
.cart-product-quantity,
.cart-product-single-price {
	padding-right: 20px;
}

.cart-product-single-price {
	text-align: right;
}

.cart-product-total-price {
	text-align: right;
}

.cart-totals {
	margin-left: auto;
}

.cart-totals-box {
	margin-left: auto;

	.total-title,
	.total-value,
	.weight {
		float: left;
		text-align: right;
	}

	.total-title {
		padding-right: 20px;
	}
}

.cart-total-submit-box {
	text-align: right;

	.btn {
		margin-left: 10px;
	}
}

.cart-discount-code {
	.form-control {
		padding-right: 120px;
	}
}

.cart-discount-code-submit-holder {
	right: 0;
}

.contact-information {
	.alert {
		text-align: left;
	}
}

.checkout-register-login {
	.user-register-submit-holder {
		text-align: left;
	}
}

.checkout-order-details-addresses {
	.address-actions {
		text-align: right;
	}
}

.checkout-order-details-addresses {
	.address,
	.phone {
		padding-right: 20px;
	}
}

.shipping-provider-title {
	padding-right: 20px;

	.radio {
		padding-right: 8px;
	}
}

.shipping-provider-image {
	padding-right: 8px;
}

.shipping-provider-features-inner {
	float: right;
}

.change-shipping-provider {
	right: calc(100% + 10px);
	right: -webkit-calc(100% + 10px);
}

div.checker,
div.radio {
	margin: 0 5px 0 0;
}

.checkbox,
.radio {
	label {
		padding-left: 0;
	}
}

.form-group,
.checkout-address-choose-type {
	label + label {
		margin-left: 10px;
	}
}

.forget-form-action {
	.btn {
		&:first-letter {
			text-transform: uppercase;
		}
	}
}

.forgot-password {
	.text-info {
		text-transform: uppercase;
	}
}

.tooltip {
	&.left {
		.tooltip-arrow {
			border-left-color: $secondary-color;
		}
	}
	&.right {
		.tooltip-arrow {
			border-right-color: $secondary-color;
		}
	}
}

.confirm ._form-row {

    ._form-col {
        text-align: left;

        &:last-of-type {
            text-align: right;
        }
    }    
}


@media (max-width: $break-device) {
	.navigation {
		.navigation-links {
			float: left;
			margin-left: 10px;
		}

		.user-controls {
			float: right;
		}
	}
}

@media (max-width: $break-md) {
	.cart-products-header {
		.name,
		.quantity,
		.single-price {
			padding-right: 10px;
		}
	}

	.cart-image-box,
	.cart-product-quantity,
	.cart-product-single-price {
		padding-right: 10px;
	}

	.cart-totals-box {
		.total-title {
			padding-right: 10px;
		}
	}

	.user-register-login-holder {
		float: left;
	}

	.user-register-submit-holder {
		float: right;
	}
}

@media (max-width: $break-sm) {
	.pull-no-mobile {
		float: none !important;
	}

	.logo {
		text-align: left;
	}

	.navcart-button {
		padding-left: 0;
	}

	.navcart {
		& > a {
			margin-left: auto;
		}
	}

	.products-filter-item {
		float: none;
	}

	.category-property {
		float: none;
	}

	.product-details-js {
  		.quantity-holder {
  			.col-sm-9 {
  				text-align: left;
  			}
  		}

  		.share {
  			text-align: center;

  			.btn {
  				float: none;
  			}
  		}
  	}

  	.products-top-info-box-image {
  		float: none;
  		margin: 0 0 10px;
  	}

	.product-vendors-by-letter {
		li {
			float: none;
		}
	}

  	.pagination {
  		.first {
  			left: 0;
  		}

  		.last {
  			right: 0;
  		}
  	}

	.form-group,
	.checkout-address-choose-type {
		label {
			margin-right: 20px;
		}

		label + label {
			margin-left: 0;
		}
	}

	.account-addresses {
		.actions {
			text-align: inherit;
		}
	}

	.order-details-total-table {
		float: none !important;
	}

	.cart-image-box,
	.cart-product-quantity,
	.cart-product-single-price {
		padding-right: 0;
	}

	.cart-product-quantity {
		left: 116px;
	}

	.cart-product-total-price {
		right: 0;
	}

	.request-confirmation-link {
		float: none !important;
	}

	.user-register-login-holder,
	.user-register-submit-holder {
		float: none;
	}

	.checkout-order-details-addresses {
		li {
			padding-left: 30px;
		}

		.address-radio {
			text-align: left;
			left: 0;
		}

		.address-actions {
			text-align: left;
		}
	}

	.shipping-provider-title {
		padding-right: 0;
	}

	.shipping-provider-features {
		padding-left: 28px;
	}

	.shipping-provider-features-inner {
		float: none;
	}

	.change-shipping-provider {
		right: 0;
		@include translate(0, -0);
	}

	.cart-totals-box {
		.total-title {
			text-align: left;
		}

		.total-value,
		.weight {
			padding-left: 5px;
			float: right;
			text-align: right;
		}
	}

	.cart-total-submit-box {
		text-align: center;

		.btn {
			margin-left: 0;
		}
	}

	.mobile-slide {
		border-right: 1px solid $border-color;
		@include translate(-240px, 0);

		&.active {
			@include translate(0, 0);
			box-shadow: 0 0 5px rgba(0, 0, 0, 1);
		}
	}

	.powered {
		text-align: center;
	}

	.payment-provider {
		margin: 0 3px;
	}

  	.socials {
  		li {
  			margin: 0 3px;
  		}
  	}

  	.providers {
  		float: none !important;
  	}
}

@media (max-width: $break-xs) {
	.cart-product-quantity {
		left: 0;
	}
}