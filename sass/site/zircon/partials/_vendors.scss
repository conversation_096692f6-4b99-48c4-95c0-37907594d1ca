.product-vendors-by-letter {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    margin-top: 30px;

    li {
        -webkit-flex: 1 0 25%;
        -ms-flex: 1 0 25%;
        flex: 1 0 25%;
        max-width: 25%;
        text-align: center;
        margin-bottom: 10px;

        a {
            display: block;
            padding: 10px;
            text-transform: capitalize;

            &.product-image {
                height: 250px;
                text-align: center;

                img {
                    position: relative;
                    top: 50%;
                    max-height: 100%;
                    @include translateY(-50%);
                }
            }

            &:hover {
                text-decoration: underline !important;
            }
        }
    }
}

@media (max-width: $break-md) {
    
    .product-vendors-by-letter li a.product-image {
        height: 200px;
    }
}

@media (max-width: $break-sm) {
    .product-vendors-by-letter li {
        -webkit-flex: 1 0 50%;
        -ms-flex: 1 0 50%;
        flex: 1 0 50%;
        max-width: 50%;
    }
}

@media (max-width: $break-ls) {

    .product-vendors-by-letter {
        @include flex-flow(column);

        li {
            -webkit-flex: 1 0 auto;
            -ms-flex: 1 0 auto;
            flex: 1 0 auto;
            max-width: 100%;
            margin-bottom: 30px;
        }
    }
}
