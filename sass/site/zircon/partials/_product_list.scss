.product-list-container {
    position: relative;
    min-height: 450px;

    .loader {
        margin-top: -50px;
    }
}

div.product-list {
    list-style: none;

    .col-lg-4 {
        &:nth-child(3n+1) {
            clear: both;
        }
    }

    @media (max-width: $break-lg) {
        .col-md-6 {
            &:nth-child(3n+1) {
                clear: none;
            }

            &:nth-child(2n+1) {
                clear: both;
            }
        }
    }
    
    .product-box {
        div.product {
            display: inline-block;
            width: 100%;
            position: relative;
            margin: 0 0 25px 0;
            padding-bottom: 52px;
            &.no-button {
                padding-bottom: 15px;
            }
            &:hover {
                .overlay {
                    display: block;
                }

                a.quick-view {
                    display: -webkit-box;
                    display: -webkit-flex;
                    display: -ms-flexbox;
                    display: flex;
                }
            }

            .product-image {
                display: block;
                position: relative;
                padding-bottom: 100%;
            }

            img {
                max-width: 100%;
                max-height: 100%;
                position: absolute;
                top: 50%;
                left: 50%;
                -webkit-transform: translate(-50%, -50%);
                        transform: translate(-50%, -50%);
            }
            
            .product-ribbon-banner {
                position: absolute;
                z-index: 3;
                max-width: 25%;

                img {
                    max-width: 100%;
                    max-height: 100px;
                    position: static;
                    top: auto;
                    left: auto;
                    right: auto;
                    bottom: auto;
                    margin: 0;
                    -webkit-transform: translate(0, 0);
                            transform: translate(0, 0);
                }
            }

            .name {
                width: 100%;
                font-size: 15px;
                display: block;
                line-height: 1.2;
                height: 36px;
                padding: 0 10px;
                font-weight: 500;
                margin: 15px 0 0 0;
                text-align: center;
                overflow: hidden;

                @media (max-width: $break-sm) {
                    height: auto;
                }
                
                &:hover {
                    text-decoration: none;
                }
            }

            .description {
                display: none;
                padding: 5px 10px 5px 10px;
                font-size: 13px;
                width: 100%;
                text-align: center;
                height: 60px;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .category {
                display: none;
            }

            .vendor {
                display: none;
            }

            .sale {
                position: absolute;
                top: 30px;
                left: 15px;
                font-size: 12px;
                width: 60px;
                height: 60px;
                line-height: 60px;
                border-radius: 100px;
                text-align: center;
                text-transform: capitalize;
                &:after {
                    content: "";
                    width: 0;
                    height: 0;
                    border-top: 6px solid transparent;
                    border-bottom: 6px solid transparent;
                    position: absolute;
                    top: 28px;
                    bottom: 0;
                    right: -3px;
                    margin: auto;
                    @include transform(rotate(90deg));
                }
            }

            .featured {
                position: absolute;
                top: 0;
                left: 0;
                padding: 5px 10px;
                font-size: 12px;
            }

            .discount {
                position: absolute;
                top: 105px;
                left: 15px;
                font-size: 12px;
                width: 60px;
                height: 60px;
                line-height: 60px;
                border-radius: 100px;
                text-align: center;
                text-transform: capitalize;
            }

            .new {
                position: absolute;
                top: 30px;
                right: 15px;
                font-size: 12px;
                width: 60px;
                height: 60px;
                line-height: 60px;
                border-radius: 100px;
                text-align: center;
                text-transform: capitalize;
                @include border-radius(50%);
                &:after {
                    content: "";
                    width: 0;
                    height: 0;
                    border-top: 6px solid transparent;
                    border-bottom: 6px solid transparent;
                    position: absolute;
                    top: 28px;
                    bottom: 0;
                    left: -3px;
                    margin: auto;
                    @include transform(rotate(90deg));
                }
            }

            .add {
                background-repeat: no-repeat;
                background-position: 30px center;
                width: 100%;
                text-align: center;
                display: block;
                position: absolute;
                bottom: 0;

                a {
                    display: block;
                    text-align: center;
                    font-size: 16px;
                    text-decoration: none;
                    position: relative;
                    padding: 14px 10px;

                    &:before {
                        content: "\f07a";
                        display: inline-block;
                        margin-right: 7px;
                        font: normal normal normal 17px/1 FontAwesome;
                        text-rendering: auto;
                        -webkit-font-smoothing: antialiased;
                        -moz-osx-font-smoothing: grayscale;
                    }

                    &.out-of-stock {
                        margin: 0;

                        &:before {
                            display: none;
                        }
                    }
                }
            }
            .discount-box {
                overflow: hidden;
                position: absolute;
                top: -6px;
                right: -6px;
                display: block;
                width: 70px;
                height: 70px;
    
                &-leasing,
                &-fixed {
                    top: 50%;
                    right: 10px;
                    width: auto;
                    height: auto;
                    padding: 5px 10px;

                    p {
                        margin-bottom: 0;
                    }

                    @media (max-width: $break-lg) {
                        top: 56%;
                    }

                    @media (max-width: $break-md) {
                        top: 57%;
                    }
                }

                &-fixed {
                    right: auto;
                    left: 10px;
                }

                .product-list-discount-percent,
                .product-list-discount-flat {
                    border-bottom: none !important;
                    span {
                        position: absolute;
                        display: inline-block;
                        right: -23px;
                        box-shadow: 0px 0px 10px rgba(0,0,0,0.2), inset 0px 5px 30px rgba(255,255,255,0.2);
                        text-align: center;
                        text-transform: uppercase;
                        top: 14px;
                        width: 100px;
                        padding: 3px 10px;
                        z-index: 9;
                        line-height: 18px;
                        @include transform(rotate(45deg));
                        pointer-events: none;
                    }
                    &:before {
                        content: "";
                        width: 0;
                        height: 0;
                        position: absolute;
                        top: 0px;
                        left: 0px;
                        border: 17px solid;
                        z-index: -1;
                    }
                    &:after {
                        content: "";
                        width: 0;
                        height: 0;
                        position: absolute;
                        bottom: 0px;
                        right: 0px;
                        border: 17px solid;
                        z-index: -1;
                    }
                }
            }

            .price_compare {
                text-align: center;
                width: 100%;
                display: block;
                font-size: 18px;
                line-height: 45px;

                del {
                    text-decoration: line-through;
                }
            }

            .price {
                text-align: center;
                width: 100%;
                display: block;
                font-size: 18px;
                line-height: 45px;
            }
            .overlay {
                position: absolute;
                top: 0;
                left: 0;
                z-index: 1;
                display: none;
                width: 100%;
                height: 100%;
                text-align: center;
            }
            a.quick-view {
                display: none;
                z-index: 2;
                width: 60px;
                height: 60px;
                @include border-radius(50%);
                text-align: center;
                position: absolute;
                top: 100px;
                right: 0;
                left: 0;
                margin: auto;
                vertical-align: middle;
                text-transform: uppercase;
                -webkit-box-pack:center;
                -webkit-justify-content:center;
                -ms-flex-pack:center;
                justify-content:center;
                -webkit-box-align:center;
                -webkit-align-items:center;
                -ms-flex-align:center;
                align-items:center;

                &:hover {
                    text-decoration: none;
                }
            }
        }
    }
}
.left-box {
    margin-top: 20px;

    > section {
        margin-bottom: 15px;
    }

    h3 {
        padding: 15px;
        margin: 0;
    }
    ul {
        margin: 0;
        overflow: auto;

        li {
            display: block;
            position: relative;

            a {
                display: block;
                text-decoration: none;
                padding: 9px 15px 10px;
                font-size: 14px;
            }
            .product-listing-filter-remove {
                display: none;
            }
            .blog-filter-remove {
                background-image: image-url("images/filter-remove.png");
                background-repeat: no-repeat;
                background-position: right center;
                position: absolute;
                top: 15px;
                margin: auto;
                right: 30px;
                display: block;
                width: 10px;
                height: 10px;
                text-indent: -9999px;
            }

            @for $i from 1 through 10 {
                .categories-dd-level-#{$i} {
                    > li {

                        a {
                            padding-left: $i*10px + 15px;
                        }
                    }
                }
            }

            ul {
                display: none;
                border: none;
            }
            &.active ul {
                background-color: transparent;
            }
            &.item-collapse {
                &.cc-open > ul {
                    display: block;
                }

                > .collapse-icon {
                    text-align: center;
                    line-height: 15px;
                    width: 14px;
                    height: 14px;
                    font-size: 20px;
                    cursor: pointer;
                    width: 38px;
                    height: 38px;
                    line-height: 38px;
                    text-align: center;
                    position: absolute;
                    top: 0;
                    right: 0;

                    &:hover {
                        opacity: .5;
                    }
                }
            }
        }
    }

    .btn {
        width: 100%;
        display: block;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .category-property {
        margin-bottom: 15px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .category-property-title {
        margin-bottom: 15px;
    }

    .category-property-form {
        ul {
            padding: 15px 15px 10px;
        }
    }
}

.js-pagination {
    text-align: center;
}

.vendor-info {
    margin-bottom: 30px;

    &:empty {
        margin-bottom: 0;
    }
}

.vendor-image {
    float: left;
    margin: 0 20px 10px 0;
}

.right-box {
    margin-top: 20px;
    .title {
        h1 {
            margin: 0;
            display: inline-block;
            padding: 15px;
        }
    }
    h3 {
        margin: 20px 0px;
    }
    .btn-primary {
        margin: 0 !important;
    }
    ul.tags {
        li {
            padding: 10px 15px;
            margin-right: 5px;
            display: inline-block;
            &:last-of-type {
                margin-right: 0;
            }
            .tag-clear {
                margin-left: 10px;
                display: inline-block;
            }
        }
    }
    @media (max-width: $break-md) {
        .select2-container {
            margin-top: 15px;
        }
    }
}
.pagination {
    li {
        @include border-radius(0px);
        a {
            margin: 0px 5px;
        }
        &:last-child a, &:last-child span, &:first-child a, &:first-child span {
            @include border-radius(0px);
        }
    }
    li.disabled > a {
        @include border-radius(0px);
    }
}
.breadcrumb {
    margin: 20px 0 0 0;
    padding: 15px 15px 0px 0px;

    > li + li:before { display: none; }

    > li {
        a {
            text-decoration: none;
        }

        &:last-of-type:after {
            display: none;
        }

        &:after { 
            content: "/";
            padding: 0 5px;
            color: #ccc;
        }
    }
}

/* category properties */
.category-property-image,
.category-property-color {
    .category-property-form {
        ul {
            padding-left: calc(15px - 1%);
            padding-bottom: calc(10px - 1%);
            @extend .clearfix;
        }

        li {
            float: left;
            margin-left: 1%;
            margin-bottom: 1%;
        }

        ._checkbox {
            display: block;
            height: 0;
            padding: 0;
            font-size: 0;
            line-height: 0;
            position: relative;
            overflow: hidden;
            word-break: break-word;
            cursor: pointer;

            .checker {
                display: none;
            }
        }
    }
}

.category-property-image {
    .category-property-form {
        li {
            width: 24%;
            max-width: 100px;
        }

        ._checkbox {
            border: 1px solid;
            padding-bottom: -webkit-calc(100% - 2px);
            padding-bottom:         calc(100% - 2px);

            &.active {
                border-width: 2px;
                padding-bottom: -webkit-calc(100% - 4px);
                padding-bottom:         calc(100% - 4px);
            }

            img {
                position: absolute;
                top: 50%;
                left: 50%;
                -webkit-transform: translate(-50%, -50%);
                        transform: translate(-50%, -50%);
            }
        }
    }
}

.category-property-color {
    .category-property-form {
        li {
            width: 11.5%;
            max-width: 40px;
        }

        ._checkbox {
            border: 2px solid transparent;
            padding-bottom: -webkit-calc(100% - 4px);
            padding-bottom:         calc(100% - 4px);
        }
    }
}

@media (max-width: $break-md) {
    .left-box.mobile-slide {
        position: fixed;
        top: 0;
        left: -100%;
        width: auto;
        min-width: 60%;
        height: 100%;
        overflow: auto;
        z-index: 99999;
        margin: 0;
        padding: 0;
        @include box-shadow(-2px 0px 15px #000);

        @include transition-property(left);
        @include transition-duration(.50s);
        @include transition-timing-function(ease-in-out);

        &.active {
            left: 0;
        }
        ul {
            max-height: none;
            overflow: visible;
        }
        
        .btn {
            margin: 10px auto;
            width: 90%;
        }
    }

    .mobile-slide-toggle {
        display: inline-block;
    }
}

@media (max-width: $break-ls) {
    
    .pagination {
        padding-top: 0;

        .next, .prev {
            display: none;
        }
    }
}