.account-views {
    text-align: center;

    li {
        display: inline-block;
        padding: 10px 15px;

        a {
            display: block;
        }
    }
}

.account-content {

    fieldset {
        margin-bottom: 40px;

        legend {
            padding: 10px 0;
        }
    }

    .alert-warning {
        @extend %flexbox;
        @include flex-flow(row wrap);
        @include justify-content(space-between);

        .request-confirmation-link {
            float: none !important;
        }
    }

    .btn-primary {
        width: auto;
        padding: 14px 20px;
    }

    .btn-cancel {
        height: 50px;
        border-radius: 0;
    }

    .form label {
        margin-right: 15px;
    }

    #addressFormsJs {
        margin-top: 15px;
    }

    .account-addresses li {
        @extend %flexbox;
        @include flex-flow(row);
        @include align-items(center);
        @include justify-content(space-between);
        margin-bottom: 5px;

        span {
            padding-right: 15px;
            @include flex(1 1 25%);

            &.actions {
                padding-right: 0;
                @include flex(none);
            }
        }
    }

    .account-orders, .alert--files {
        margin-top: 20px;
    }
}

.popup-order-details-box {

    .image {
        display: inline-block;
        width: 70px;
        height: 70px;
        background-size: cover;
        background-position: center center;
    }

    .order-invoice-box {
        padding: 15px 0px;
        a {
            margin-right: 10px;
            text-decoration: none;
            i {
                margin-right: 5px;
            }
        }
    }

    .order-details-total {
        padding: 15px 0px 0px 0px;
        span {
            margin-left: 100px;
            float: right;
        }
    }
}

.table-responsive {

    thead tr th {
        line-height: 60px;
        padding: 0;
        border: none;
    }

    tbody tr td {
        vertical-align: top;
        padding: 15px 15px 15px 0;
        width: 200px;
        max-width: 200px;

        &.order-product-item {
            width: 300px;
            max-width: 300px;
        }

        .variant-box {
            display: inline-block;
            width: calc(100% - 75px);
            vertical-align: top;
            padding-left: 5px;

            span {
                white-space: normal;
            }

            .parameter {
                display: block;
            }

            .name {
                margin-right: 5px;
                display: inline-block;
            }
        }
    }
}

.confirm ._form-row {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;

    ._form-col {
        -webkit-box-flex: 1;
        -webkit-flex: 1 1 50%;
        -ms-flex: 1 1 50%;
        flex: 1 1 50%;
        text-align: left;

        &:last-of-type {
            text-align: right;
        }

        ._button {
            width: 100%;
            max-width: 70%;
            padding: 5px 0;
        }
    }    
}

@media (max-width: $break-md) {
    .order-details {
        width: 700px;
    }
}

@media (max-width: $break-sm) {

    .order-details {
        width: auto;
    }

    .page-account {

        .table-responsive {
            padding: 0 10px;

            thead {
                display: none;
            }

            tr {
                margin-top: 30px;
                display: block;

                &:first-of-type {
                    margin: 0;
                }
            }

            td {
                display: block;
                text-align: right;
                overflow: hidden;
                line-height: 25px;
                padding: 10px 0;
                max-width: 100%;
                width: 100%;

                &.order-product-item {
                    width: 100%;
                    max-width: 100%;
                }

                &:first-of-type {
                    border: none;
                }

                &:before {
                    content: attr(data-title);
                    float: left;
                    margin-right: 5px;
                }

                .variant-box {
                    width: auto;
                }

                a {
                    display: inline-block;
                    float: right;
                }
            }
        }
    }

    .popup-order-details-box {

        .order-details-box {
            .status-box {
                margin-top: 30px;
            }
        }

        .order-details-total span {
            margin-left: 0;
        }
    }
}

@media (max-width: $break-ls) {

    .account-views li {
        display: block;
        padding: 5px;
    }

    .contact-information, .change-password-form {

        .row:last-of-type .col-md-6 {
            width: 100%;
            margin-bottom: 10px;

            input {
                width: 100%;
                float: none !important;
            }
        }
    }

    .account-content {

        .btn-primary {
            width: 100%;
        }

        .account-addresses li {
            @include flex-flow(column);
            @include align-items(flex-start);

            span {
                @include flex(1 1 auto);
                margin: 5px 0;
            }

        }
    }

    #removeAccountSubmit {
        width: 100%;
    }

    .confirm ._form-row {
        -webkit-flex-flow: column;
        -ms-flex-flow: column;
        flex-flow: column;

        ._form-col {
            -webkit-box-flex: 1;
            -webkit-flex: 1 1 auto;
            -ms-flex: 1 1 auto;
            flex: 1 1 auto;
            -webkit-box-ordinal-group: 3;
            -webkit-order: 2;
            -ms-flex-order: 2;
            order: 2;

            &:last-of-type {
                -webkit-box-ordinal-group: 2;
                -webkit-order: 1;
                -ms-flex-order: 1;
                order: 1;
                margin-bottom: 15px;
            }

            ._button {
                max-width: 100%;
            }
        }
    }
}