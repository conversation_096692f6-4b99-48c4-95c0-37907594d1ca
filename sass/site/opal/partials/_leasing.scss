.btn-buy-on-leasing {
    margin-bottom: 10px;
}

.leasing {
    &-options {
        &-heading {
            margin-bottom: 17px;

            h3 {
                font-weight: normal;
                margin: 0;
            }
        }

        &-tabs {
            overflow: hidden;
            margin-bottom: 15px;
        }

        &-tab {
            float: left;
            width: 33.33%;
            padding: 15px 30px 15px 0;

            img {
                max-width: 100%;
            }
        }

        &-trigger {
            display: inline-block;
            padding-left: 25px;
            position: relative;

            input {
                position: absolute;
                top: 50%;
                left: 0;
                -webkit-transform: translate(0, -50%);
                        transform: translate(0, -50%);

                &:focus {
                    outline: 0;
                }
            }
        }

        &-table {
            font-size: 22px;

            &-holder {
                border: 1px solid;
                overflow: auto;

                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: -1px 0;
                }

                tbody {
                    tr {
                        &:nth-child(even) {
                            td {
                                background-color: #efefef;
                            }
                        }
                    }
                }

                th,
                td {
                    border: 1px solid;
                    border-width: 1px 0;
                    padding: 10px;
                    white-space: nowrap;
                }

                th {
                    text-transform: uppercase;
                    font-size: 14px;
                }
            }

            &-monthly,
            &-total {
                font-weight: bold;
            }
        }

        &-message {
            text-align: center;
            margin-bottom: 20px;
        }

        &-button {
            margin-top: 10px;
        }

        &-footer {
            font-size: 12px;
            margin-top: 10px;
        }
    }

    &-form {
        padding: 10px;

        &-title {
            margin-top: 0;
        }

        .checkout-payment-providers {
            margin: 0 -15px -15px;
            text-align: center;
        }

        .checkout-payment-provider {
            vertical-align: top;
            display: inline-block;
            width: 33.33%;
            padding: 0 15px 15px;
            font-size: 16px;
            line-height: 1;

            &.disabled {
                label {
                    cursor: not-allowed;
                }
            }
            
            label {
                cursor: pointer;
                margin-bottom: 0;
                position: relative;
                padding-left: 25px;
            }

            .radio {
                position: absolute;
                top: 50%;
                left: -25px;
                -webkit-transform: translate(0, -50%);
                        transform: translate(0, -50%);
            }

            &-image {
                position: relative;
                display: block;
                margin-bottom: 10px;

                img {
                    max-width: 100%;
                    max-height: 50px;
                }
            }

            &-price {
                font-weight: bold;
                color: #000;
            }
        }

        .shipping-to-office-label-text {
            a {
                text-decoration: underline !important;
            }
        }
    }

    &-providers {
        margin: 20px 0;
        font-size: 0;
        line-height: 0;
    }

    &-conditions {
        ol {
            padding-left: 15px;
            list-style-position: outside;
            list-style-type: decimal;
        }
    }
}

@media (max-width: $break-sm) {
    .leasing {
        &-options {
            &-tab {
                float: none;
                width: auto;

                img {
                    max-width: 120px;
                }
            }

            &-table {
                font-size: 18px;

                &-holder {
                    th {
                        text-transform: uppercase;
                        font-size: 12px;
                    }
                }
            }
        }

        &-form {
            .checkout-payment-providers {
                text-align: inherit;
            }

            .checkout-payment-provider {
                display: block;
                float: none;
                width: auto;
            }
        }
    }
}