.modal-dialog.product-details {
    width: 850px;

    .modal-body {
        padding: 30px 0px 30px 0px;

        .product-details-js {
            width: auto;
            @include flex-flow(row wrap);
            @include justify-content(space-around);
            padding-top: 10px;

            .single-info {
                max-width: 480px;
                width: 480px;
                @include order(3);
                margin-top: 10px;

                h1 {
                    margin: 0 0 20px  0;
                }

                .product-details-price-js {
                    max-width: 450px;
                    padding: 15px;
                }

                .share {
                    margin: 20px 0 0 0;
                }

                .options .btn-primary {
                    padding: 0 20px;
                    max-width: 250px;
                }
            }

            .single-photos {
                width: 300px;
                max-width: 300px;

                .product-details-gallery {
                    max-height: 100px;
                    display: block;

                    &:after {
                        content: '';
                        clear: both;
                        display: block;
                    }

                    li {
                        float: left;
                        width: 65px;
                        height: 60px;
                        margin: 0 10px 10px 0;
                    }
                }
            }

            .image.primary {
                max-width: 100%;
                margin:0 auto;
            }

            .product.zoom-enabled {
                margin-bottom: 10px;
            }

            .product-details-description {
                padding: 10px 0;
            }

            .product-list-discount-flat {
                border: none;
            }
        }
    }
}

.product-details-js {
    @media (max-width: $break-md) {
        .product-details-left-box {
            margin-bottom: 30px;
        }
    }
    .product.zoom-enabled {
        text-align: center;
    }

    img {
        max-width: 100%;
        max-height: 100%;
        margin:0 auto;
    }
            
    .product-ribbon-banner {
        position: absolute;
        z-index: 3;

        img {
            max-width: initial;
            max-height: initial;
            margin: 0;
        }
    }

    .product {
        position: relative;
        z-index: 1;

        .image.primary {
            display: block;
            max-width: 100%;
            max-height: 100%;
            margin: 0 auto;
        }

        .sale, .new {
            position: absolute;
            width: 50px;
            min-width: 50px;
            height: 50px;
            left: auto;
            right: 5px;
            top: 10px;
            border-radius: 50%;
            text-transform: capitalize;
            @extend %flexbox;
            @include align-items(center);
            @include justify-content(center);

            &.alone {
                top: 10px;
            }
        }

        .sale {
            top: 70px;
        }
    }

    .featured {
        position: absolute;
        bottom: 10px;
        left: 0;
        padding: 0 10px;
        height: 25px;
        line-height: 25px;
        z-index: 1
    }

    .discount-box {
        position: absolute;
        top: 10px;
        left: 0;
        padding: 0 10px;
        height: 25px;
        line-height: 25px;
        z-index: 1;

        + .discount-box {
            margin-top: 30px;
        }

        p {
            margin-bottom: 0;
        }
    }

    ul.product-details-gallery {
        margin: 10px 0;

        li {
            display: inline-block;
            margin-right: 10px;
            width: 64px;
            height: 64px;
            overflow: hidden;
            position: relative;

            &:last-of-type {
                margin-right: 0;
            }

            img {
                position: absolute;
                top: 0;
                bottom: 0;
                margin: auto;
                max-height: 100%;
                max-width: 100%;
            }
        }
    }

    h1 {
        padding-bottom: 20px;
        margin: 0 0 20px  0;
    }
    p {
        padding:0;
        margin: 0;
    }
    .tags-box {
        margin: 20px 0px;
        padding-top: 20px;
        .tags {
            margin-bottom: 10px;
            .tag {
                margin-bottom: 10px;
            }
        }
    }
    .product-details-description {
        margin-top: 20px;
        padding: 20px 0;
    }
    .product-details-price-js {
        margin-bottom: 20px;
        padding-right: 280px;
        position: relative;
        .details-price-left {
            width: 100%;
            float: left;
            text-align: left;
        }
        .price-new-js {
            display: inline-block;
            @include font-size($price-font-size);
            margin: 0 10px 10px 0;
        }
        .prices-holder {
            display: inline-block;
            min-width: 25%;
            .price-old-js, .price-discount-js {
                display: block;
            }
            .price-old-js {
                vertical-align: top;
                margin-top: 10px;
                > i {
                    text-decoration: line-through;
                }
            }
            .price-discount-js {
                vertical-align: baseline;
            }
        }
        .variant-sku-js {
            position: absolute;
            top: 23px;
            right: 20px;
        }
    }

    @media (max-width: $break-sm) {
        .product.zoom-enabled {
            cursor: default;
        }

        .product-details-price-js {
            padding-right: 0;
            .details-price-left {
                display: block;
            }
            .price-new-js {
                @include font-size($price-font-size);
                margin: 0 10px 10px 0;
            }
            .price-old-js {
                margin-top: 5px;
            }
            .variant-sku-js {
                top: 20px;
            }
        }
    }

    .options {

        .bootstrap-touchspin {
            input {
                text-align: center;
                padding: 10px 5px;
            }
            .input-group-btn .btn {
                @include border-radius(0px);
                @include box-shadow(none);
                border: none;
                padding: 6px 14px;
            }
        }
        .btn-primary {
            float:right;
            padding: 0 20px;
        }
    }
    .published, .product-vendor, .category {
        display: inline-block;
        margin-right: 10px;
        a {
            text-decoration: none;
        }
        &:first-of-type {
            margin-left: 0;
        }
    }

    .select2-container {
        &.select2-allowclear {
            .select2-choice {
                abbr {
                    display: none;
                }
            }
        }
    }
}

//single product product produc page
.product-details-js.singleProduct {
    @extend %flexbox;
    @include flex-flow(row);
    @include align-items(flex-start);
    @include justify-content(space-between);
    padding-top: 40px;

    &:before, &:after {
        display: none;
    }

    > div {
        @include flex(1 1 auto);
    }

    .single-info {
        width: 340px;
        max-width: 340px;

        h1 {
            border-bottom: none;
            margin-bottom: 0;
        }

        .product-details-description {
            border: none;
            margin-top: 0;
            padding: 10px 40px 10px 0;

            img {
                max-width: 100% !important;
                height: auto !important;
                margin: 20px auto;
                display: block;

                &[style*="float:"] {
                    &[style*="left"] {
                       margin: 5px 15px 5px 0;
                   }

                   &[style*="right"] {
                       margin: 5px 0 5px 15px;
                   }
               }
            }
        }

        .product-details-price-js {
            padding: 20px;

            .choose-variant-msg-js {
                margin-bottom: 20px;
            }

            .details-price-left {
                float:none;
                @extend %flexbox;
                @include flex-flow(column);

                .price-new-js {
                    @include order(2);
                }

                .prices-holder {

                    .price-old-js {
                        margin-top: 0;
                    }
                }
            }

            .add-to-cart-form-js {

                .row {
                    margin: 0;
                }
            }
        }

        .options {

            .btn-primary {
                width: 100%;
                float: none;
                padding: 5px 20px;
            }

            .quantity-holder {
                padding: 0;
            }

            .product-details-parameters {
                @extend %flexbox;
                @include flex-flow(row wrap);
                @include justify-content(space-between);
            }

            .parameter-holder {
                @include flex(1 1 45%);
                max-width: 48%;
                min-width: 135px;
                margin-bottom: 20px;
            }

            .select2-container {

                &.form-control {
                    height: 41px;
                    padding: 0;
                }

                .select2-choice {
                    height: 100%;
                    @extend %flexbox;
                    @include align-items(center);
                    padding: 0 5px;

                    abbr {
                        top: 0;
                        bottom: 0;
                        right: 15px;
                        margin: auto;
                    }

                    .select2-arrow {
                        @extend %flexbox;
                        @include align-items(center);
                        right: 0;

                        b {
                            width: 15px;
                            height: 15px;
                        }
                    }
                }
            }
        }

        .share {
            margin: 30px 0;
        }

        .quantity-left-js {
            padding: 0;

            > span {
                display: block;
                padding-top: 10px;
            }

            .out-of-stock-js {
                text-align: center;
            }
        }
    }

    .single-main {
        width: 550px;
        max-width: 550px;

        .product {

            > span {
                display:-webkit-box !important;
                display:-webkit-flex !important;
                display:-ms-flexbox !important;
                display:flex !important;
                @include justify-content(center);
                @include align-items(center);
            }

            .new, .sale {
                width: 50px;
                min-width: 50px;
                height: 50px;
                left: auto;
                right: 5px;
                top: 10px;
                border-radius: 50%;
                text-transform: capitalize;
                z-index: 2;
            }

            .sale {
                top: 70px;

                &.alone {
                    top: 10px;
                }
            }
        }
    }

    .single-photos {
        width: 90px;
        max-width: 90px;
        margin-left: 10px;

        .arrows {
            display: none;
            text-align: center;

            a {
                padding: 5px 10px;
                display: inline-block;
            }
        }

        .product-details-gallery {
            max-height: 450px;
            overflow: hidden;
            @extend %flexbox;
            @include flex-flow(column);
            @include align-items(flex-end);
            margin: 0;

            li {
                min-width: 90px;
                min-height: 90px;
                margin: 0 0 10px 0;

                a {
                    height: 100%;
                    display: block;
                }

                img {
                    left: 0;
                    bottom: 0;
                    right: 0;
                }
            }
        }
    }
}

.facebook-comments {
    margin-bottom: 30px;
}

.disqus-comments {
    margin-bottom: 30px;
}

.modal {
    .comment-forms {
        padding: 0 30px;

        @media (max-width: $break-sm) {
            padding: 0 15px;
        }
    }
}

.topInfo {

    &.fixed {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        width: 100%;
        max-width: 1170px;
        margin: 0 auto;
        z-index: 3;

        .quickControls {
            opacity: 1;
            z-index: 1;
            display:-webkit-box;
            display:-webkit-flex;
            display:-ms-flexbox;
            display:flex;
        }
    }

    .quickControls {
        opacity: 0;
        z-index: -1;
        display: none;
        margin: 5px 0;

        .visiblePrice {
            margin-right: 10px;
        }
    }
}

//related products
div.product-list {
    margin-bottom: 100px;
    position: relative;

    > div {
        position: static;
    }

    .product-box div.product {
        min-height: 505px;

        .add {
            margin-top: 15px;

            a {
                display: block;
                @extend .btn;
                padding: 10px 20px;
            }
        }

        .product-image {
            height: 350px;
        }

        a.quick-view {
            top: 145px;
        }

        img {
            max-width: 100%;
            max-height: 100%;
            top: 50%;
            left: 50%;
            right: auto;
            bottom: auto;
            @include translate(-50%, -50%);
        }

        .name {
            text-align: left;
            text-transform: capitalize;
        }

        .description {
            margin: 8px 0;
            padding: 0;
            text-align: left;
            word-break: normal;
        }

        .price_compare, .price {
            width: 100%;
            height: auto;

            del {
                position: static;
                display: inline-block;
                padding-left: 10px;
                text-decoration: line-through;
            }
        }
    }
}

.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group {
    margin-left: 0;
}

.credit-calculator-button-holder {
    margin-bottom: 20px;
}

/* parameters */
._parameter-radio-values {
    margin-bottom: -3px;
    margin-right: -3px;
    @extend .clearfix;
}

._parameter-radio-value {
    float: left;
    display: table;
    height: 36px;
    min-width: 44px;
    margin-right: 3px;
    margin-bottom: 3px;

    ._radio {
        display: table-cell;
        vertical-align: middle;
        border: 1px solid;
        padding: 5px;
        text-align: center;
        font-weight: normal;
        font-size: 13px;

        &.active {
            border-width: 2px;
            padding: 4px;
        }

        .radio {
            display: none;
        }
    }
}

._parameter-image-values {
    margin-left: -1%;
    margin-bottom: -1%;
    @extend .clearfix;
}

._parameter-image-value {
    float: left;
    border: 1px solid;
    width: 24%;
    max-width: 100px;
    margin-left: 1%;
    margin-bottom: 1%;
    word-break: break-word;

    &.active {
        border-width: 2px;
    }

    ._radio {
        display: block;
        height: 0;
        padding: 0;
        padding-bottom: 100%;
        position: relative;
        overflow: hidden;
        cursor: pointer;

        .radio {
            top: 5px;
            left: 5px;
        }

        .radio-label {
            display: inline-block;
            padding-top: 4px;
            padding-left: 30px;
            padding-right: 5px;
        }

        img {
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
                    transform: translate(-50%, -50%);

            ~ .radio,
            ~ .radio-label {
                display: none;
            }
        }
    }
}

._parameter-color-values {
    margin-right: -5px;
    margin-bottom: -5px;
    @extend .clearfix;
}

._parameter-color-value {
    float: left;
    margin-right: 5px;
    margin-bottom: 5px;

    ._radio {
        display: block;
        width: 34px;
        height: 34px;
        border: 2px solid transparent;
        padding: 0;
        margin: 0;
        font-size: 0;
        line-height: 0;
        cursor: pointer;

        .radio {
            display: none;
        }

        .radio-label {
            display: none;
        }
    }
}

@media (max-width: $break-lg) {

    .product-details-js.singleProduct {

        .single-info {
            width: 350px;
            max-width: 350px;
            padding-right: 10px;
        }
    }
}

@media (max-width: $break-md) {

    .product-details-js.singleProduct {
        padding-top: 20px;
        @include flex-flow(row wrap);

        .single-info {
            width: 100%;
            max-width: 100%;
            padding-right: 0;
            margin-top: 20px;
            @include order(3);

            .product-details-price-js {
                padding: 15px;
            }

            .options {

                .btn-primary {
                    max-width: 250px;
                    padding: 0 20px;
                }

                .parameters .parameter-holder {
                    padding-right: 0;
                }
            }
        }
    }

    div.product-list .col-md-3 {
        max-width: 100%
    }

    .modal-dialog.product-details {
        width: 95%;
        max-width: 770px;
        margin: 0 auto;

        .modal-body .product-details-js {

            .single-info {
                max-width: 400px;
            }
        }
    }

    .topInfo.fixed {
       padding: 0;

       .quickControls .btn {
            padding: 10px 0;
        }
    }
}

@media (max-width: $break-sm) {

    div.product-list .product-box div.product {
        min-height: 450px;
        margin-bottom: 0;

        a.quick-view {
            top: 115px;
        }

        .description {
            padding: 10px 0;
            height: auto;
            max-height: 100%;
        }

        .price-row {
            padding-bottom: 10px;
        }
    }

    .productsAll__holder div.product-list .product-box div.product {
        margin-bottom: 0;
        border-bottom: none;
    }

    .product-details-js.singleProduct {
        @include flex-flow(column);

        .single-info .share {
            margin: 20px 0;
        }

        .single-main {
            width: 100%;
            max-width: 100%;

            .product .new {
                top: 10px;
                right: 10px;
            }

            .product .sale {
                top: 70px;
                right: 10px;

                &.alone {
                    top: 10px;
                }
            }
        }

        .single-photos {
            width: 100%;
            max-width: 100%;
            margin: 20px 0 0 0;

            .product-details-gallery {
                @include flex-flow(row wrap);
                max-height: 90px;

                li {
                    min-width: 60px;
                    width: 60px;
                    margin: 0 10px 0 0;
                }
            }
        }
    }

    .product-details-js .options .bootstrap-touchspin .input-group-btn .btn {
        padding: 8px 14px;
    }

    .modal-dialog.product-details .modal-body .product-details-js {

        .single-info {
            max-width: 100%;
        }
    }
}

@media (max-width: 580px) {
    .modal-dialog.product-details .modal-body .product-details-js {

        .single-photos {
            @include flex-flow(column);

            .product {
                margin: 0 auto;
            }

            .product-details-gallery {
                padding-left: 0;
                margin-top: 10px;

                li {
                    width: 24%;
                    margin: 0 1% 10px 0;
                    min-width: 55px;
                    height: 55px;
                }
            }
        }

        .single-info {

            h1 {
                margin-bottom: 10px;
            }
        }

        .product-details-description {
            padding: 5px 0;
        }
    }
}

@media (max-width: $break-ls) {

    div.product-list .product-box div.product {
        min-height: 0;
        margin-bottom: 20px;

        .price_compare, .price {
            position: relative;
        }

        .product-image {
            height: auto;

            img {
                position: static;
                top: 0;
                left: 0;
                @include transform(none);
            }
        }
    }

    .product-details-js.singleProduct {

        .single-main .product {

            .new, .sale {
                width: 40px;
                height: 40px;
                min-width: 40px;
            }
        }

        .single-photos .product-details-gallery li {
            min-width: 55px;
            width: 19%;
            margin: 0 1% 0 0;
        }

        .single-info {

            .product-details-description {
                padding-right: 0;
            }

            .options {

                .product-details-parameters {
                    width: 100%;
                    max-width: 100%;
                }

                .parameter-holder {
                   min-width: 100%;
                   @include flex(1 1 100%);
                }

                .btn-primary {
                    max-width: 100%;
                }
            }
        }
    }

    .product-details-js .product.zoom-enabled {
        height: 350px;
    }

    .modal-dialog.product-details .modal-body .product-details-js .single-info .options .btn-primary {
        max-width: 100%;
    }

    .topInfo.fixed .quickControls .btn {
        padding: 5px 0;
    }
}

@media (max-width: 400px) {
    .product-details-js.singleProduct {

        .single-photos .product-details-gallery li {
            min-width: 55px;
            width: 24%;
        }
    }
}
