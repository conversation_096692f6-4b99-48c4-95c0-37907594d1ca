body {
    background: $background_color_primary;
    color: $text_color_primary;
    font-family: $fontfamily;
}

.controls-nav {
    background-color: $background_header_top_menu;
}

a {
    color: $text_color_primary;
    
    &:hover, &:active, &.active, &:focus {
        color: $text_color_active;
    }
}
label {
    font-weight: $normal;
}

.product-details-description, .blog-article-content, .widget-info-box, .html-content-container-js, .slide-html, .showcase-info-box .description {

    table td {
        border: 1px solid $border_color;
    }
}

.title {
    //background: $background_color_fourth;
    //color: $text_color_fourth;
    background: $default_title;
    color: $default_title_bg;

    &.product-showcase {
        background: $product_showcase_title_bg;

        h2 {
            color: $product_showcase_title_txt;
        }
    }
}
.black-title {
    background-color: $text_color_fifth;
    h1, h2, h3, h4, h5 {
        color: $text_color_fourth;
        font-weight: $normal;
        font-size: $title-font-size;
    }
}

.modal-dialog {
    border-color: $border-color;
    .modal-content {
        border-color: $border-color;
        .close:after {
            color: $text_color_active;
        }
    }
}

.modal-backdrop {
    background: $background_color_secondary;
}

.navbar-header {
    .header-actions {
        .search-box {
            button {
                background-color: $light_border_color;
                border: 1px solid $border_color;
                font-size: 20px;
                color: $header_search_icon;
            }
        }
    }
}

.breadcrumb {
    background: transparent;

    &__wrapper {
        background-color: $breadcrumbs_background;
    }

    li.active {
        color: $breadcrumb_active;
    }
    li, > li+li, li.active {
        &:before {
            color: $text_color_active;         
        }    
    }
}

.pagination {
    background-color: $text_color_fourth;
    border: 1px solid $border_color;
    border-bottom: 3px solid $border_color;
    li {
        &.active {
            a {
                background-color: $text_color_active;
                &:hover {
                    background-color: $text_color_active;
                    opacity: 0.9;    
                }
            }
        }
        &:hover {
            background-color: $light_border_color;
            a {
                color: $text_color_fifth; 
            }
        }
        a {
            color: $text_color_fifth; 
        }
        &:first-of-type, &:last-of-type {
            a:hover {
                color: $text_color_active;    
            }
        }
    }
}
.error-box {
    .error-title {
        color: $text_color_active;
    }
    .message {
        font-size: $title-font-size;  
    }
    .floor {
        background-color: $text_color_active;
    }   
}

.loader {
    span {
        background-color: $background_color_third;
    }
}

.loader-dots {
    span {
        background-color: $main_buttons_text_color;
    }
}

@media (max-width: $break-md){
    .left-box.mobile-slide {
        background-color: $background_color_secondary;
        border-right: 1px solid $border_color;    

        &.active {
            .mobile-slide-toggle {
                color: $text_color_active;

                &:before {
                   font-size: 18px;
                }
            }
        }
    }
}

.rc-anchor-light {
    background-color: $background_color_secondary !important;
}

.bootstrap-touchspin {
    button {
        background-color: $light_border_color;
        border: 1px solid $border_color;
        i {
            color: $dark_border_color;
        } 
    }
}

.slider {
    .loader-container {
        background-color: $background_color_primary;
    }
}