.navcart {
    position: relative;
    text-align: center;
    min-height: 49px;

    &:hover {
        .navcart-information {
            display: block;
        }
    }

    .navcart-information {
        text-align: left;
        border: 1px solid;
        position: absolute;
        display: none;
        top: 47px;
        right: 0;
        padding: 20px;
        min-width: 280px;
        z-index: 11;

        &:before {
            content: "";
            display: block;
            position: absolute;
            top: -11px;
            right: 11px;
            width: 0;
            height: 0;
            z-index: 10;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
        }
        &:after {
            content: "";
            display: block;
            position: absolute;
            top: -10px;
            right: 12px;
            width: 0;
            height: 0;
            z-index: 11;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
        }

        #HomeCarouselProducts {

        }

        ul {
            margin: 0;
            max-height: 400px;
            overflow-x: hidden;
            overflow-y: auto;

            li {
                position: relative;
                padding: 0;
                margin-bottom: 20px;
                .image-holder {
                    border: 1px solid;
                    display: inline-block;
                    width: 64px;
                    height: 64px;
                    overflow: hidden;
                    margin-bottom: -5px;
                    text-align: center;
                    cursor: pointer;
                    img {
                        position: relative;
                        top: 50%;
                        max-width: 100%;
                        max-height: 100%;
                        @include translateY(-50%);
                    }
                }
                img, h4, span, a {
                    display: inline-block;
                    margin: 0;
                }
                a {
                    display: block;
                    cursor: default;
                }
                h4 {
                    margin-left: 5px;
                    word-wrap: break-word;
                    width: 60%;
                    vertical-align: top;
                    padding-bottom: 20px;
                    font-size: $second-font-size;
                    cursor: pointer;
                    &:hover {
                        text-decoration: underline;
                    }
                }
                .product-cart-product-remove {
                    background: image-url("images/filter-remove.png")no-repeat;
                    position: absolute;
                    top: 5px;
                    right: 10px;
                    display: block;
                    width: 8px;
                    height: 8px;
                    text-indent: -9999px;

                    &:hover {
                        @include opacity(0.7);
                    }
                }
                .product-cart-product-price {
                    position: absolute;
                    display: block;
                    left: 72px;
                    bottom: 0;
                    font-size: $second-font-size;
                }
                .product-cart-product-remove {
                    @include opacity(0);
                }
                &:hover {
                    .product-cart-product-remove {
                        @include opacity(1);
                        &:hover {
                            @include opacity(0.6);
                        }
                    }
                }
            }
            .product-cart-subtotal {
                line-height: 34px;
                width: 100%;
                text-align: center;
                margin-bottom: 10px;
            }
        }

        .btn {
            width: 100%;
            text-transform: capitalize;
        }
    }
    .navcart-button {
        // position: relative;

        .navcart-button-icon {
            position: absolute;
            top: 0;
            right: 0;
            width: 49px;
            height: 49px;

            > i {
                position: absolute;
                left: 50%;
                top: 50%;
                @include translate(-50%, -50%);
            }
        }
        .navcart-items {
            position: absolute;
            width: 20px;
            height: 20px;
            @include border-radius(50%);
            color: #fff;
            top: 4px;
            right: 3px;
            margin: auto;
            z-index: 10;
            text-align: center;  
            font-size: 10px;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-box-pack: center;
            -webkit-justify-content: center;
            -ms-flex-pack: center;
            justify-content: center; 
        }
    }

    .navcart-dropdown {
        text-align: center;
    }
}

.checkout-steps {
    text-align: center;
    padding-bottom: 12px;
    padding-top: 12px;
    margin-bottom: 20px;

    .checkout-step {
        display: inline-block;
        margin-right: 20px;
        line-height: 25px;

        .name {
            text-transform: uppercase;
            color: #aeaeae !important;
            font-size: 13px;
        }

        .indicator {
            margin-top: 4px;
            width: 20px;
            height: 20px;
            display: inline-block;
            float: left;
            margin-right: 10px;
            position: relative;
            @include border-radius(50%);
            &:before {
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                text-align: center;
                line-height: 18px;
                display: block;
                margin: auto;
                color: #aeaeae;
            }
        }
        &.checkout-step-cart .indicator:before {
            content: "1";
        }
        &.checkout-step-authorize .indicator:before {
            content: "2";
        }
        &.checkout-step-address .indicator:before {
            content: "3";
        }
        &.checkout-step-shipping .indicator:before {
            content: "4";
        }
        &.checkout-step-verify .indicator:before {
            content: "5";
        }
        &.checkout-step-complete .indicator:before {
            content: "6";
        }
        &:after {
            content: "";
            background-image: image-url("images/step-arrow.png");
            background-position: center center;
            background-repeat: no-repeat;
            width: 6px;
            height: 21px;
            display: inline-block;
            margin-left: 20px;
            vertical-align: middle;
        }
        &:last-of-type:after {
            display: none;
        }
    }
}

.cart-items {
    padding: 0 0 20px 0;
    span {
        margin-right: 10px;
    }
}

.cart-holder, .checkout-cart-holder {
    .cart-product-list, .checkout-cart-product-list {
        .cart-product {
            padding: 20px 20px 20px 20px;
            min-height: 160px;
            margin-bottom: 15px;
            position: relative;

            &:before, &:after {
                content: "";
                display: table;
                clear: both;
            }
            @media (min-width: $break-md + 1) {
                &:last-of-type {
                    margin-bottom: 0;
                }
            }
            &:hover {
                .cart-image-box .image-holder {
                    img {
                        @include opacity(.7);
                    }
                }
                .cart-product-remove {
                    display: block;
                    @include opacity(1);
                }
            }
            .cart-image-box {
                display: block;
                overflow: hidden;

                .image-holder {
                    float: left;
                    width: 100px;
                    margin-right: 20px;

                    img {
                        max-width: 100px;
                        border: 1px solid #e8e8e8;
                    }
                }
                .cart-parameter-box {
                    display: block;
                    overflow: hidden;
                    vertical-align: top;
                    margin-bottom: 60px;

                    .parameter-holder {
                        .parameter {
                            margin-right: 10px;
                            .name {
                                margin-right: 5px;
                            }
                        }
                    }
                }
            }
            .cart-product-remove {
                position: absolute;
                width: 16px;
                height: 16px;
                top: 30px;
                right: 30px;
                text-align: center;
                line-height: 15px;
                display: block;
                @include transition(all, .3s);
                @include border-radius(50%);
                @include opacity(0);
                &:hover {
                    background-image: image-url("images/remove-product-hover.png");
                }
                white-space :nowrap;
                text-indent: -9999px;
                background-image: image-url("images/remove-product.png");
                border-radius : 2px;
            }
            .cart-product-quantity {
                display: block;
                position: absolute;
                bottom: 20px;
                left: 140px;
                margin-bottom: 0;
                
                .bootstrap-touchspin {
                    width: 105px;
                    height: 25px;
                    margin: 0;
                }
            }
            .cart-product-single-price {
                display: none;
            }
            .cart-product-total-price {
                position: absolute;
                right: 20px;
                bottom: 20px;
                font-size: 18px;
                del {
                    display: inline-block;
                    font-size: 14px;
                    margin-right: 10px;
                }
                .main-price {
                    display: inline-block;
                }
                .total-save {
                    display: none;
                }
            }
        }
    }
}

.cart-totals, .checkout-cart-totals, .cart-providers, .cart-estimate {
    padding: 20px;
    margin-bottom: 15px;

    .btn {
        width: 100%;
        padding: 3px;
    }
    .section-title {
        margin: 0 0 20px 0;
        padding-bottom: 20px;
        text-transform: uppercase;
        border-bottom: 1px solid #dcdbdb;
    }
    .cart-products-subtotal-box {
        padding: 5px 0;

        &:first-letter {
            text-transform: capitalize;
        }
        img {
            width: 30px;
        }
        .total-title {
            float: left;
        }
        .total-value {
            float: right;
        }
        &:before, &:after {
            content: "";
            display: table;
            clear: both;
        }
        &.shipping {
            .total-title {
                display: block;
            }
            .btn {
                background: transparent;
                width: auto;
                padding: 0 5px;
            }
        }
        &.final-total {
            line-height: 40px;
            margin-top: 10px;
            border-top: 1px solid #dcdbdb;
            border-bottom: 1px solid #dcdbdb;
        }
    }
}

.cart-estimate-tax-submit , .cart-shipping-provider-submit {
    line-height: 1.6;
}

.cart-totals {
    border-bottom: 0
}

.checkout-cart-totals {
    border-bottom: 0;
}

.cart-products-continue {
    margin-top: 15px;
}

.cart-estimate {
    padding: 0;

    .cart-settings-box {
        padding: 15px;
    }

    h4 {
        margin: 0;
    }

    .cart-shipping-providers-js {
        margin-top: 20px;

        ul {
            overflow: hidden;

            li {
                width: 100%;
                display: inline-block;

                label {
                    display: block;
                    line-height: 25px;
                }

                label + label {
                    float: none !important;
                    margin-top: 10px;
                }
            }
        }
    }
}
.cart-totals, .checkout-cart-totals {
    margin-bottom: 0;
}
.cart-submit {
    padding: 0 20px 20px 20px;
    .btn {
        width: 100%;
    }
}


.shipping-box {
    + .btn-cancel {
        margin-right: 15px;
    }

    .shipping-box-header {
        margin: 0 0 20px 0;
        padding: 20px;
        text-transform: uppercase;
        font-size: 21px;
        font-weight: 600;
    }

    .checkout-shipping-providers {
        margin-bottom: 0;
        li {
            position: relative;
            line-height: 31px;
            padding: 10px 0;
            border-top: 1px solid #dcdbdb;
            
            img {
                width: 30px;
            }
            label {
                margin-bottom: 0;
            }

            &:last-of-type {
                border-bottom: 0;
            }
        }
    }
}

.checkout-address-holder, .checkout-order-note {
    margin-bottom: 15px;

    .address-container {
        display: inline-block;
        width: 100%;

        h2 {
            margin-bottom: 20px;
            padding: 20px;
            font-size: 21px;
        }
    }

    .checkout-order-container {
        padding: 20px;

        .checkout-payment-providers {
            margin-bottom: 0;

            li {
                padding: 5px 0;

                &:last-child {
                    padding-bottom: 0;
                }
            }
        }
    }

    .checkout-address {
        border-top: 1px solid #dcdbdb;
        margin: 10px 0;
    }

    .checkout-addresses {
        padding: 0 15px;
        border: 1px solid #dcdbdb;
    }

    .checkout-address-shipping {
        overflow: hidden;
    }

    h2 {
        text-transform: uppercase;
        margin-top: 0;
    }

    h3 {
        margin-right: 5px;
        margin-bottom: 0;
    }
    .checkout-address-holder {
        border: 0;
        margin-bottom: 0;

        .address-details {
        margin: 0;

            .titles-list {
                display: none;
            }

            ul {
                display: inline-block;
                text-align: left;
                margin-bottom: 0;
                > li {
                    float:left;
                    line-height: 30px;
                    margin-left: 15px;

                    &:first-child {
                        margin-left: 0;
                    }

                    &:last-of-type {
                        border-bottom: 0;
                    }
                }
                &.values-list {
                    float:left;
                    margin: 13px 0 0 7px;

                    li {
                        display: inline-block;
                        margin-left: 5px;

                        &:first-child {
                            margin-left: 0;
                        }
                    }
                }
            }

            .address-list {
                overflow: hidden;

                li {
                    span {
                        &:nth-child(2){
                            margin-left: 10px;
                        }
                    }
                }
            }
        }
        .checkout-address-edit {
            display: none;
            text-align: right;
            padding-top: 20px;
        }
    }

    textarea {
        display: block;
        background-color: #fffad6;
        border: 1px solid #f5eac5;
        border-bottom: 3px solid #f5eac5;
        margin-top: 30px;
        max-width: 100%;

        &:focus {
            border-color: #ede0b6;
        }
    }
}

.cart-settings-js {
    padding: 0 20px 20px 20px;
    .cart-discount-code {
        position: relative;
        input {
            padding-right: 60px;
        }
        .cart-discount-code-submit-holder {
            position: absolute;
            bottom: 0;
            right: 0;
            .btn {
                padding: 0 10px 0 10px;
            }
        }
    }
}

#CheckoutFormsJs {
    div.form-group {
        display: inline-block;
        width: 100%;

        input {
            height: 40px;
        }
    }
}

.checkout-authorize {
    section {
        div {
            label {
                font-weight: 500;
                margin-bottom: 10px;

                .radio {
                    margin-right: 10px;
                }
            }
        }
    }

    .cart-header {
        text-transform: uppercase;
        padding: 20px;
        margin: 20px 0;
        font-size: 21px;
        font-weight: 600;
    }

    #CheckoutRegister, #CheckoutLogin, #CheckoutGuest {
        padding: 15px;

        h3 {
            background-position: center top;
            background-repeat: no-repeat;
            text-align: left;
            margin: 10px 0 15px 0;
            padding-bottom: 15px;
            font-size: 14px;

        }
        .form-group {
            margin-left: 0;
            margin-right: 0;
        }

        .user-login-actions-box, .user-register-submit-holder, .user-register-login-holder {
            .btn {
                width: 100%;
                text-align: center;
            }
        }
    }

    #CheckoutRegister , #CheckoutGuest  {
        h3 {
            display: block;
        }

        .btn-edit {
            display: none;
        }
    }

    #CheckoutRegister {
        padding-bottom: 3px;
    }
}

.padding-none {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.user-controls {
    height: 49px;
    line-height: 47px;

    .user-controls-account {
        position: relative;
    }

    a {
        display: block;
        width: 105px;
        height: 49px;
        float:left;
        padding: 0 15px;
    }

    .user-controls-logout , .user-controls-login {
        position: relative;
    }
}

.tabs {
    position: relative;
    padding-left: 15px;

    ul {
        overflow: hidden;
        margin-bottom: 0;

        li {
            float:left;
            padding-left: 2px;

            a {
                text-transform: uppercase;
                padding: 20px;
                margin-bottom: 0;
                display: block;
            }
        }

        li.current {
            a {
                box-shadow: 0 2px 3px rgba(1,1,1,.25);
            }
        }
    }

    .form-group {
        margin-bottom: 20px;
    }

    .help-block-error {
        position: absolute;
    }
}

.checkout-return {
    max-width: 700px;
    margin: 0 auto;
    padding: 50px 0 100px;

    .checkout-return {
        padding: 0;
    }

    @media (max-width: $break-sm) {
        padding: 20px 0;
    }
}

.checkout-return-requested, .checkout-return-completed  {
    h2 {
        font-size: 36px;
    }

    p {
        font-size: 18px;
        margin: 30px 0;
        color: #666666;
    }

    a {
        text-transform: uppercase;
        padding: 15px 70px;
        border-radius: 5px;
        display: inline-block;
        margin-top: 15px;
    }
}

.amount-to-free-shipping {
    border-bottom: 1px solid #dbdbdb;
    width: 100%;
    display: block;
    padding-bottom: 20px;
}

.cart-totals , .cart-submit {
    margin-right: 0;
}

.btn.btn-cancel {
    padding: 0 20px;
    height: 40px;

    &:hover {
    }
}

.checkout-shipping {
    .btn.btn-cancel {
        margin-top: 15px;
    }

    .btn-primary {
        margin-top: 15px;
    }
}

.checkout-address-holder {
    display: block;
}

.checkout-verify {
    .checkout-address-holder {
        margin-left: 0;
    }

    .checkout-cart-totals {
        margin-top: 20px;
    }
}

#addressForm {
    .btn-cancel {
        padding: 0;
        margin: 15px 0;
    }
}

.user-register-js {
    .user-register-submit {
        height: 40px;
    }
}

.cart-container {
    display: block;
    height: 99%;
    margin-left: -15px;
    margin-right: -15px;
}

.navcart-additional-info {
    width: 100%;
    display: inline-block;
    text-transform: capitalize;
    padding-top: 4px;
    padding-bottom: 6px;

    p {
        font-size: 16px;
        margin: 0;
    }

    p.cart-total-price {
        font-size: 12px;
    }
}

.cart-product-quantity {
    .bootstrap-touchspin button {
        border: 1px solid #ddd;
    }
}

.cart-header-inactive , .shipping-billing-inactive , .shipping-box-header-inactive , .shipping-billing-address-inactive {
    text-transform: uppercase;
    font-size: 21px;
    padding: 20px;
    margin: 10px 0;
    font-weight: 600;
}

.btn-cancel {
    width: 100%;
    text-align: center;
}

.weight {
    display: inline-block;
}

.cart-discount-code-remove {
    display: table;
    margin-bottom: 10px;
}

@media (max-width: $break-md) {
    #CheckoutFormsJs {
        margin-top: 10px;
    }

   .checkout-verify {
        margin-left: 0;
        margin-right: 0;

        .checkout-address-holder {
            margin-left: 0;
        }

        .verify-products {
            margin-right: -15px;
        }

        .checkout-order-container {
            margin-right: 0;
        }

        .checkout-cart-totals , .cart-submit {
            margin-left: 0;
            margin-right: 0;
        }
    }

    .shipping-left {
        .shipping-box {
            margin-left: 0;
        }

        .btn-primary {
            margin-top: 20px;
        }
    }

    .shipping-right {
        .cart-totals, .cart-submit {
            margin-right: 0;
        }
    }

    #orderAddressChooseJs {
        .checkout-address-choose-shipping {
            margin-left: 0;
        }

        .cart-totals, .cart-submit {
            margin-right: 0;
        }

        .shipping-box {
            margin-left: 0;
        }
    }

    .cart-estimate {
        margin-top: 15px;
    }

    .checkout-steps {

        .checkout-step {
            display: none;

            &.active {
                display: inline-block;
            }
        }
    }

    .checkout-shipping {
        .btn.btn-cancel {
            margin-left: 0;
        }
    }

    .checkout-address {
        .btn.btn-cancel {
            margin: 15px;
            margin-bottom: 20px;
        }
    }

    .checkout-address-holder , .verify-products {
        padding-right: 30px;
    }

    .checkout-order-note , .verify-prizes {
        padding-left: 30px;
    }

    .checkout-order-note , .verify-prizes {
        padding-left: 15px;
    }

    .checkout-address-holder, .verify-products {
        padding-right: 15px;
    }

    .checkout-cart-totals {
        margin-top: 20px;
    }

    .tabs {
        margin-top: 20px;
        padding: 0;
    }

    .cart-totals {
        margin-top: 20px;
    }

    .shipping-box {
        margin-top: 20px;
    }

    .cart-header-inactive , .shipping-billing-inactive , .shipping-box-header-inactive , .shipping-billing-address-inactive {
        font-size: 18px;
    }

    .checkout-address-holder, .checkout-order-note {

        .address-container  h2 {
            font-size: 18px;
        }
    }
}

@media (max-width: $break-sm) {

    .navcart {
        .navcart-dropdown {
            .icons-cart {
                display: block;
            }

            &:hover {
                .navcart-information {
                    display: none;
                }
            }
        }

        .navcart-button .navcart-items {
            left: auto;
        }
    }

    .header-wrapper {
        .sr-only {
            display: none;
        }
    }

    .navcart {
        &:hover {
            .navcart-information {
                display: none;
            }
        }
    }

    .user-login-continue-holder, .user-register-submit-holder {
        text-align: center;
    }

    .user-login-continue, .user-register-submit {
        width: 50% !important;
        min-width: 200px;
        margin: 0 auto;
    }

    #CheckoutFormsJs div.form-group:nth-child(2n+1) {
        margin-left : 0 !important;
    }

    #CheckoutFormsJs div.form-group {
        width: 100%;
    }

    .user-controls {
        a {
            height: 50px;
        }
    }

    .tabs {
        ul {
            li {
                a {
                    padding: 8px;
                }
            }
        }
    }

    .cart-holder, .checkout-cart-holder {
        .cart-product-list, .checkout-cart-product-list {
            .cart-product {
                .cart-image-box {
                    .image-holder {
                        img {
                            width: auto;
                        }
                    }

                    .cart-parameter-box {
                        .parameter-holder {
                            .parameter {
                                display:block;
                            }
                        }
                    }
                }

                .cart-product-quantity {
                    margin-bottom: 0;

                    .cart-product-out-of-stock {
                        display: none;
                    }
                }

                .cart-product-remove {
                    right: 15px;
                    top: 15px;
                }
            }
        }
    }

    .checkout-steps {
        .checkout-step {
            display: none;
        }

        .checkout-step.active {
            display: inline-block;
        }

        .checkout-step:after {
            display: none;
        }
    }

    .cart-holder, .checkout-cart-holder {
        .cart-product-list, .checkout-cart-product-list {
            .cart-product {
                .cart-image-box {
                    .image-holder {
                        img {
                            width: 100px;
                        }
                    }
                }
            }
        }
    }

    .navcart {
        padding: 0;
        .navcart-button {
            position: relative;

            .navcart-button-icon {
                position: absolute;
                width: 49px;
                height: 49px;

                > i {
                    position: absolute;
                    left: 45%;
                }
            }
        }
    }

    .checkout-address-holder , .checkout-order-note {

        .checkout-address-holder .address-details .address-list {

            li {
                float: none;
                display: block;
                width: 100%;
                margin-left: 0;
            }
        }
    }
}

.cart-submit-terms {
    margin-bottom: 14px;
}

@media (max-width: $break-ls) {
    .cart-holder, .checkout-cart-holder {
        .cart-product-list, .checkout-cart-product-list {
            .cart-product {
                .cart-image-box {
                    text-align: center;
                    margin-bottom: 10px;

                    .image-holder {
                        float: none;
                        width: auto;
                        margin-right: 0;
                    }

                    .cart-parameter-box {
                        margin-bottom: 0;
                    }
                }

                .cart-product-quantity {
                    position: static;
                    left: 0;
                    bottom: 0;

                    .bootstrap-touchspin {
                        margin: 0 auto;
                    }
                }

                .cart-product-total-price {
                    position: static;
                    display: block;
                    text-align: center;
                    margin-top: 10px;
                }
            }
        }
    }

    .cart-header-inactive , .shipping-billing-inactive , .shipping-box-header-inactive , .shipping-billing-address-inactive {
        font-size: 16px;
    }

    .checkout-address-holder, .checkout-order-note {

        .address-container  h2 {
            font-size: 16px;
        }
    }

    .navcart-additional-info {
        padding-right: 50px;
        height: 49px;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-flow:column;
        -ms-flex-flow:column;
        flex-flow:column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center; 

        p {
            font-size: 13px;

            &.cart-total-price {
                font-size: 10px;
            }
        }
    }
}

@media (max-width: 400px) {
    .tabs {
        ul {
            li {
                a {
                    white-space: nowrap;
                    width: 130px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}
