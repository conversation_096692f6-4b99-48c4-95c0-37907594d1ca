.modal-dialog.product-details {
    width: 1000px;
    border: none;
    background: none;
    
    .modal-body {
        padding: 30px 0 30px 0;
        .product-details-js {
            width: auto;
            border: none;
            background: none;

            .product-box {
                background: transparent;
                border: none;
            }
            .details {
                .description, .share {
                    padding: 0;
                    border: none;
                }
            }
            .sale, .new {
                position: absolute;
                top: 10px;
                padding: 0 10px;
                font-size: 18px;
                line-height: 67px;
                
                z-index:2;
                pointer-events: none;
                &:after {
                    content: "";
                    width: 0;
                    height: 0;
                    position: absolute;
                    top: 28px;
                    bottom: 0;
                    margin: auto;
                }
            }

            .sale {
                left: 10px;
                top: 90px;
            }

            .sale.alone {
                left: 10px;
                top: 10px;
            }

            .new {

            }
        }
    }
}
.product-details-js {
    padding:0 20px 20px 20px;
    .product-box {
        margin-bottom: 40px !important;
        .product {
            position: relative;
            overflow: hidden;
            text-align: center;
            margin-bottom: 20px;

            .zoomWrapElement {
                width: 100%;
            }

            .zoomImg {
                background: #ffffff;
            }

            .featured {
                position: absolute;
                line-height: 30px;
                font-size: 18px;
                font-weight: bold;
                bottom: 10px;
                left: 0;
                padding: 0 10px;
                z-index: 2;
            }

            .discount-box {
                position: absolute;
                line-height: 30px;
                font-size: 18px;
                font-weight: bold;
                top: 10px;
                left: -5px;
                padding: 0 10px;
                z-index: 2;

                &-leasing {
                    top: auto;
                    left: auto;
                    bottom: 0;
                    right: 0;

                    &:after {
                        display: none;
                    }
                }

                p {
                    margin-bottom: 0;
                }

                &:after {
                    content: "";
                    width: 0;
                    height: 0;
                    border-bottom: 5px solid transparent;
                    border-left: 5px solid transparent;
                    position: absolute;
                    left: 0;
                    bottom: -10px;
                }
            }
        }
    }
    .margin {
        margin: 0;
    }
            
    .product-ribbon-banner {
        position: absolute;
        z-index: 3;
    }

    .image.primary {
        position: relative;
        display: block;
        max-width: 100%;
        max-height: 100%;
        margin: 0 auto;
    }
    
    ul.product-details-gallery {
        margin: 10px -2% 10px 0;

        &:after {
            content: '';
            clear: both;
            display: block;
        }

        li {
            display: inline-block;
            margin: 0 2% 2% 0;
            float: right;
            width: 18%;
            overflow: hidden;
            position: relative;

            a {
                position: relative;
                display: block;
                margin:3px;
                padding-bottom:calc(100% - 8px);
           }

            img {
                max-width: 100%;
                max-height: 100%;
                width: auto;
                height: auto;
                position: absolute;
                top: 50%;
                left: 50%;
                @include translate(-50%, -50%);
            }
        }
    }
    h1 {
        margin: 0;
        padding-bottom: 30px;
        font-size: 30px;
        
        font-weight: 200 !important;

        &:after {
        }
    }
    .product-vendor, .category {
        display: inline-block;
        margin-left: 10px;
    }
    .product-details-description {
        padding-bottom: 20px;
        margin: 0 0 20px  0;
    }
    .product-list-discount-flat {
        border: none;
    }
    .product-details-price-js {
        position: relative;
        padding-left: 100px;
        overflow: hidden;

        .price-new-js {
            display: inline-block;
            direction: ltr;
            font-size: 36px;
            margin-right: 10px;
            line-height: 1.1;
            display: inline-block;
        }
        .price-old-js {
            display: inline-block;
            direction: ltr;
            font-size: 20px;
            text-decoration: line-through;
            display: block;
        }
        .price-discount-js {
            display: inline-block;
            direction: ltr;
        }
        .product-discount-section {
            display:inline-block;
        }
    }

    .published {
        margin-left: 10px;
        margin-top: 15px;
    }

    .product-vendor , .category , .published {
        font-size: 13px !important;
        display: inline-block;
    }

    .tags-box {
        .tags {
            margin: 15px 0 0 0;
            padding-bottom: 15px;

            li {
                line-height: 30px;
                display: inline-block;
                padding: 5px 10px;
                margin-bottom: 5px;
            }
        }
    }
    .details {
        .description{

            &:last-of-type {
                margin-bottom: 0;
            }
            h3 {
                padding-bottom: 10px;
                margin-top: 0;
            }
            .product-details-description {
                margin-bottom: 0;
                padding-bottom: 0;
            }
        }
    }

    .share {
        background: #e4e4e4;
        padding: 15px;
        margin-top: 30px;

        h3 {
            display: none;
        }
    }

    .sale, .new {
        position: absolute;
        top: 10px;
        font-size: 18px;
        text-align: center;
        height: 70px;
        width: 70px;
        line-height: 67px;
        @include border-radius(50%);
        font-weight: bold;
        
        z-index: 2;
        pointer-events: none;
    }

    .sale {
        left: 10px;
        top: 90px;
    }

    .sale.alone {
        top: 10px;
    }

    .new {
        left: 10px;
    }
    .options {
        overflow: hidden;
        margin-top: 15px;
        padding-left: 1px;

        .parameter-holder {
            margin-bottom: 20px;
        }

        .bootstrap-touchspin {
            position: relative;
            display: table;
            border-collapse: separate;

            .input-group-btn {
                .btn {
                    padding: 6px 8px !important;
                }
            }

            .input-group-btn-vertical {
                position: relative;
                white-space: nowrap;
                width: 1%;
                vertical-align: middle;
                display: table-cell;

                button {
                    display: block;
                    float: none;
                    width: 100%;
                    max-width: 100%;
                    padding: 4.5px 10px;
                    margin-left: -1px;
                    position: relative;
                    font-size: 7px;
                    @include border-radius(0);
                }
            }
        }

        .btn-primary {
            padding: 0 20px;
            height: 40px;
            float:right;
        }
    }

    .select2-container {
        &.select2-allowclear {
            .select2-choice {
                abbr {
                    display: none;
                }
            }
        }
    }
}

.product-sku {
    position: absolute;
    bottom: 0;
    left: 0;
}

.options {
    .quantity-holder {
        display: block;
        padding: 0;
    }
}

.product-description-heading {
    padding: 0 8%;
    text-align: center;
    margin-bottom: 30px;
}

.left-box {
    margin: 20px 0;
}

.select2-container.parameter-value-js.form-control {
    border-radius: 4px;
}

.facebook-comments {
    margin-bottom: 30px;
}

.disqus-comments {
    margin-bottom: 30px;
}

.modal {
    .comment-forms {
        padding: 0 20px;
    }
}

.category-properties-list {
    margin-top: 10px;
    list-style-type: none;
    font-size: 13px;
}

.category-properties-item {
    display: inline-block;
    margin-left: 10px;
}

.credit-calculator-button-holder {
    margin-top: 20px;

    @media (max-width: $break-sm) {
        text-align: center;
    }
}

/* parameters rtl */
._parameter-radio-values {
    margin-bottom: -3px;
    margin-left: -3px;
    @extend .clearfix;
}

._parameter-radio-value {
    float: right;
    display: table;
    height: 36px;
    min-width: 44px;
    margin-left: 3px;
    margin-bottom: 3px;

    ._radio {
        display: table-cell;
        vertical-align: middle;
        border: 1px solid;
        padding: 5px;
        text-align: center;
        font-weight: normal;
        font-size: 13px;

        &.active {
            border-width: 2px;
            padding: 4px;
        }

        .radio {
            display: none;
        }
    }
}

._parameter-image-values {
    margin-right: -1%;
    margin-bottom: -1%;
    @extend .clearfix;
}

._parameter-image-value {
    float: right;
    border: 1px solid;
    width: 24%;
    max-width: 100px;
    margin-right: 1%;
    margin-bottom: 1%;
    word-break: break-word;

    &.active {
        border-width: 2px;
    }

    ._radio {
        display: block;
        height: 0;
        padding: 0;
        padding-bottom: 100%;
        position: relative;
        overflow: hidden;
        cursor: pointer;

        .radio {
            top: 5px;
            right: 5px;
        }

        .radio-label {
            display: inline-block;
            padding-top: 4px;
            padding-right: 30px;
            padding-right: 5px;
        }

        img {
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
                    transform: translate(-50%, -50%);

            ~ .radio,
            ~ .radio-label {
                display: none;
            }
        }
    }
}

._parameter-color-values {
    margin-left: -5px;
    margin-bottom: -5px;
    @extend .clearfix;
}

._parameter-color-value {
    float: right;
    margin-left: 5px;
    margin-bottom: 5px;

    ._radio {
        display: block;
        width: 34px;
        height: 34px;
        border: 2px solid transparent;
        padding: 0;
        margin: 0;
        font-size: 0;
        line-height: 0;
        cursor: pointer;

        .radio {
            display: none;
        }

        .radio-label {
            display: none;
        }
    }
}

@media (max-width: $break-md) {
    .vendors-list {
        margin-left: 0 !important;
        margin-bottom: 15px;

        .ico-arrow-down {
            margin: 3px 2px 0 3px !important;
        }
    }

    .product-details-js {

        .product-vendor , .category , .published {
            display: block;
        }
    }

    .price-static {
        margin-bottom: 15px;
    }

    .mibile-slide .select2-choice {
        margin-bottom: 15px;
    }

    .parameter-holder {
        width: 100%;
    }

    .options {
        .quantity-holder {
            width: 100%;
        }
    }
}

@media (max-width: $break-sm) {
    .add-to-cart-form-js .quantity-holder {
        width: 100%;
    }

    .select2-container.parameter-value-js.form-control {
        margin-left: 0;
    }

    .table-responsive {
        border: 0;
    }

    .product-details-js {

        h1 {
            font-size: 25px;
        }

        .options {
            .btn-primary {
                margin-top: 15px;
                width: 100%;
            }
        }
    }
}

@media (max-width: $break-ls) {
    .product-details-js {
        h1 {
            font-size: 20px;
        }

        .product-box .product {
            height: 350px;
        }
    }
}

@media (max-width: 450px) {
    .product-details-js {
        ul.product-details-gallery {
            li {
                width: 31%;
            }
        }
    }
}

@media (max-width: $break-xs) {
    .table-responsive {
        border: 0;
    }
}
