.modal-dialog.user-login , .cart-step.user-login , .form-box {
    width: 350px !important;
    h3 {
        background-position: center top;
        background-repeat: no-repeat;
        text-transform: uppercase;
        text-align: center;
        margin: 10px -15px 20px -15px;
        padding-bottom: 20px;
        border-bottom: 1px solid #dcdbdb;
    }

    .btn-primary {
        float: right;
        width: 100%;
        height: 40px;
    }

    .text-center , .user-login-sign-in-holder button {
        text-transform: uppercase;
    }

    /*.text-center {
        border-top: 1px solid #dcdbdb;
    }*/

    .user-login-forgotten-pass-link, .checkbox label {
        &:first-letter {
            text-transform: uppercase;
        }
    }

    span.checked {
        background: #999999;
    }

    .choice-box {
        border-top: 1px solid #dcdbdb;
        padding-top: 15px;
    }

    .user-login-remember-field {
        float: none;
        display: block;
        margin-bottom: 10px;
        width: 100%;
        label {
            font-size: 13px;
        }
    }

    .user-login-forgotten-pass-link {
        font-size: 13px;
        color: #333333;
        display: inline-block;
        float: none;
        width: 100%;
    }

    .user-login-regiter-link {
        margin-top: 10px;
    }

    .user-login-sign-in {
        margin-top: 20px;
    }
}

.form-horizontal {
    .btn, a {
        &:first-letter {
            text-transform: uppercase;
        }
    }
}

.cart-login {
    border: 1px solid #dcdcdc;
    padding: 0 15px;
}

.user-register-login-holder {
    margin-top: 15px;
}
.forget-form-action-back {
    margin-bottom: 10px;
    .btn {
        text-transform: uppercase;
        padding: 7px 20px;
    }
}
.forget-form-action-back,
.user-register-login-holder {
    float: none;
    display: block;
    width: 100%;
    text-align: center;
}
.forget-form-action-back + .col-md-6,
.user-register-submit-holder {
    float: left !important;
    width: 100%;
}

.forget-form-action-back + .col-md-6 {
    margin-bottom: 10px;
}
.forget-form-action-back,
.forget-form-action-back + .col-md-6,
.user-register-login-holder,
.user-register-submit-holder {
    .btn {
        float: none !important;
    }
}

.cart-step.user-login {
    width: 100% !important;
    
    h3 {
        margin: 20px -15px 15px -15px;
        font-size: 14px !important;
        text-align: left;
    }

    .form-group.text-center.choice-box {
        display: none;
    }
}

.form-box {
    width: 100% !important;
    padding: 0 10%;

    h3 {
        margin: 20px -15px 20px -15px;
        font-size: 14px !important;
        text-align: left;
    }
}

.user-register-login-link {
    text-transform: uppercase;
}

@media (max-width: $break-sm) {
    .modal-dialog {
        .confirm {
            .modal-footer {
                .row {
                    display: -webkit-box;
                    display: -webkit-flex;
                    display: -moz-flex;
                    display: -ms-flexbox;
                    display: flex;
                    @include flex-flow(column);
                    div {
                        float: none;
                        width: 100%;
                        &:first-of-type {
                            margin-top: 15px;
                            @include order(2);
                        }
                        &:nth-of-type(2) {
                            @include order(1);
                        }
                    }
                }
            }
        }
        .continue-shopping {
            .row {
                > div {
                    float: none;
                    width: 100%;
                    .bnt-full {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
    .forget-form-action {
        .btn {
            float: none !important;
            display: block;
            width: 100%;
        }
    }
}

@media (max-width: $break-xs) {
    .modal-dialog.user-login {
        width: 290px !important;
    }
}