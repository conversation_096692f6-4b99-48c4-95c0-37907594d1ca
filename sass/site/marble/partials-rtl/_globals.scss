html {
    height: 100%;
    position: relative;
}

body {
    font-size: $base-font-size;
    min-height: 100%;
    @extend %flexbox;
    @include flex-flow(column);
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;

    &.body-rtl {
        .pull-left { float: right !important; }
        .pull-right { float: left !important; }
        .text-left { text-align: right; }
        .text-right { text-align: left; }
        .bracketing { display: inline-block; direction: ltr; }
        .date { display: inline-block; direction: ltr; }
        .order-product-quantity,
        .order-product-total-price,
        .total-value,
        .weight,
        .address-list li:last-of-type,
        .address-list li:nth-of-type(4),
        .order-payments td { direction: ltr; }
        .cart-total-price,
        .navcart-total,
        .order-details-total span { display: inline-block; direction: ltr; }
        .order-payments td { text-align: right; }
        .product-cart-subtotal span { display: inline-block; direction: ltr; }
        time { display: inline-block; direction: ltr; }
        td { text-align: right; }
        th { text-align: right; }
        td[align="left"] { text-align: right; }
        td[align="right"] { text-align: left; }

        .price_compare {

            span, del {
                display: inline-block;
                direction: ltr;
                padding: 0 2px;
            }
        }
    }
}

.site-wrapper {
    @include flex(1 1 auto);
    position: relative;
}

footer {
    @include flex(none);
}

body.page-home {
    .background-image {
        display: none;
    }
}

a {
    text-decoration: none !important;
}

.cl {
    &:after {
        content: '';
        display: block;
        clear: both;
    }
}

div[id*="recaptcha"] {
    width: 100%;
    transform: scale(0.88);
    transform-origin: right 0;
}

img {
    max-width: 100%;

    &.js-lazy-load {
        opacity: 0;
        -webkit-transition: opacity .3s ease-out;
        -moz-transition: opacity .3s ease-out;
        -ms-transition: opacity .3s ease-out;
        -o-transition: opacity .3s ease-out;
        transition: opacity .3s ease-out;
    }
}

#price_range {
    direction: ltr;
    display: inline-block;
}

.faq {
    margin: 15px 0;

    ul {
        list-style-type: circle;
        padding: 0 20px;
        
        li {
            margin-bottom: 20px;
        }
    }
}

.textbox, .product-details-description, .blog-article-content, .widget-info-box, .html-content-container-js, .slide-html, .showcase-info-box .description { 
    table {
        width: 100%;
        margin: 20px 0;

        th,
        td {
            border-collapse: collapse;
            padding: 3px 5px;
        }
    }

    ul { 
        list-style-type: disc; 
        list-style-position: inside; 
    }
    ol { 
        list-style-type: decimal; 
        list-style-position: inside; 
    }
    ul ul, ol ul { 
        list-style-type: circle; 
        list-style-position: inside; 
        margin-right: 15px; 
    }
    ol ol, ul ol { 
        list-style-type: lower-latin; 
        list-style-position: inside; 
        margin-right: 15px; 
    }

    img {
        max-width: 100% !important;
        height: auto !important;
    }

    .aligncenter {
        display: block;
        max-width: 100%;
        margin: 0 auto;
    }

    .alignleft {
        float: left;
        margin: 5px 20px 10px 0;
    }

    .alignright {
        float: right;
        margin: 5px 0 10px 20px;
    }

    .text-aligncenter {
        text-align: center;
    }

    .text-alignleft {
        text-align: left;
    }

    .text-alignright {
        text-align: right;
    }

    .text-alignjustify {
        text-align: justify;
    }
}

.textbox-iframe {
    height: 0;
    padding-bottom: 56.25%;
    position: relative;

    iframe {
        border: 0;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
    }
}

.pull-left {
    float: right !important;
}

.pull-right {
    float: left !important;
}

.bracketing { display: inline-block; }

.background-image {
    background-size: cover;
    background-position: center center;
    padding-top: 110px;
    margin-bottom: 40px;

    @media (max-width: $break-sm) {
        padding-top: 0;
        margin-bottom: 30px;
    }
}

.title {
    margin: 20px 0;
    h1, h2, h3, h4, h5 {
        font-size: $title-font-size;
        font-weight: $normal;
        padding: 0 0 0 20px;
        line-height: 45px;
        margin: 0;
    }
}

.black-title {
    margin: 30px 0;
    text-align: center;
    position: relative;
    padding: 0 15px;
    background: none !important;
    width: 100%;

    h1, h2, h3, h4, h5 {
        line-height: 29px;
    }

    h1 {
        padding: 8px 15px;
        display: inline-block;
        background-color: #fff;
        margin: 0 auto;
        font-size: 23px !important;
        position: relative;
        color: #000 !important;
    }

    &:before {
        content: "";
        display: block;
        height: 2px;
        text-shadow: none;
        background-color: #ededed;
        width: 100%;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }
}

.alert-danger {
    margin-bottom: 0;
}

.modal-dialog {
    border-top: 1px solid;
    .modal-content {
        @include box-shadow(0px 0px 25px rgba(0,0,0,0.25));
        @include border-radius(0px);
        border-width: 0 1px 3px 1px;
        border-style: solid;
        &.continue-shopping {
            .btn {
                width: 100%;
                margin-top: 20px;
            }
            .message {
                border-bottom: 1px solid #cccccc;
                padding-bottom: 20px;
                display: block;
                text-align: center;
            }
        }
    }
    .modal-body {
        @include box-shadow(none);
        position: relative;
        .close {
            position: absolute;
            top: 0;
            right: 0;
            width: 25px;
            height: 25px;
            display: block;
            z-index: 9999;
            @include opacity(1);
            &:after {
                content: "+";
                display: block;
                text-align: center;
                line-height: 25px;
                font-size: 30px;
                font-weight: 100;
                @include rotate(45deg);
            }
            &:hover {
                @include opacity(.8);
            }
        }
    }
    .modal-footer {
        border-top: none;
        .btn {
            width: 100%;
        }
    }
    &.confirm {
        .modal-body, .modal-header {
            text-align: center;
            border: none;
        }
    }
}

.modal {
    .static-page-text,
    .textbox {
        padding: 10px 20px;

        @media (max-width: $break-md) {
            padding: 0;
        }
    }
}

.modal-backdrop.in {
    @include opacity(0.7);
}

.navbar-header {
    .header-actions {
        display: inline-block;
        margin-top: 40px;
        .search-box {
            input {
                line-height: 40px;
                height: 41px;
                margin-left: 5px;
                padding: 0 10px;
                display: inline-block;
                width: 200px;
            }
            button {
                height: 41px;
                width: 40px;
                display: inline-block;
                margin-left: -1px;
                i {
                    margin-top: 5px;
                }
            }
        }
        @media (max-width: $break-sm) {
            .search-box {
                position: absolute;
                top: -46px;
                left: 0;
                margin-right: 244px;
            }
        }
        @media (max-width: 505px) {
            .search-box {
                margin-right: 98px;
                input {
                    width: 150px;
                }
            }
        }
    }
}

.breadcrumb {
    padding: 25px 0;
    margin: 0;
    background: transparent;
    width: 100%;
    text-align: center;
    font-size: 11px;

    + h1 {
        padding-bottom: 25px;
        margin: 0;
        background: transparent;
        width: 100%;
        text-align: center;
        font-size: 30px;
    }

    li, > li+li, li.active {
        &:before {
            display: none;
        }
        &:after {
            content: "/ ";
            padding: 0 5px;
            color: #ccc;
        }
    }

    li:last-of-type:after {
        display: none;
    }
}

.pagination {
    width: 100%;
    padding: 15px 0;
    text-align: center;

    li {
        a {
            border: none;
            float: none;
            margin: 0 5px;
            padding: 8px 12px;
            display: block;
        }
        
        &:first-of-type {
            float: left;
            a:hover {
                background-color: transparent;
            }
        }
        &:last-of-type {
            float: right;
            a:hover {
                background-color: transparent;
            }
        }
    }

    @media (max-width: $break-sm) {
        display: block;
        position: relative;
        padding-top: 40px;
        text-align: center;

        li {
            &.page {
                a {
                    margin: 0 3px;
                }
            }

            a,
            &:last-child > a,
            &:last-child > span {
                margin: 0;
            }
        }

        .first,
        .last {
            display: none;
        }

        .prev {
            position: absolute;
            top: 0;
            left: 0;
        }

        .next {
            position: absolute;
            top: 0;
            right: 0;
        }
    }
}

.error-box {
    margin-top: 40px;
    text-align: center;
    .error-icon-stroke {
        width: 170px;
        height: 170px;
        display: block;
        position: relative;
        right: 0;
        left: 0;
        margin: auto;
        @include border-radius(50%);
        .error-icon {
            width: 150px;
            height: 150px;
            display: block;
            position: absolute;
            right: 0;
            left: 0;
            top: 0;
            bottom: 0;
            margin: auto;
            @include border-radius(50%);
            span {
                text-align: center;
                //@include font-size($error-icon-font-size);
                font-weight: $bold;
                line-height: 150px;
            }
        }
    }
    @media (max-width: $break-md) {
        .error-icon-stroke {
            width: 180px;
            height: 180px;
            .error-icon {
                width: 150px;
                height: 150px;
                span {
                    //@include font-size(110px);
                    line-height: 150px;
                }
            }
        }
    }
    .code {
        margin-top: 40px;
        //@include font-size(150px);
    }
    @media (max-width: $break-md) {
        .code {
            //@include font-size(200px);
        }
    }
    .message {
        //@include font-size($error-txt-font-size);
        margin-top: 40px;
        display: block;
    }
    @media (max-width: $break-md) {
        .message {
            font-size: 20px;
        }
    }
}

.mobile-slide-toggle {
    display: none;
}

.rc-anchor-light {
    border: none;
    @include box-shadow(0);
    @include border-radius(0);
}

.bootstrap-touchspin {
    position: relative;
    display: table;
    border-collapse: separate;
    .input-group-btn-vertical {
        position: relative;
        white-space: nowrap;
        width: 1%;
        vertical-align: middle;
        display: table-cell;
        
        button {
            display: block;
            float: none;
            width: 100%;
            max-width: 100%;
            padding: 3px 10px;
            margin-left: -1px;
            position: relative;
            font-size: 7px;
            @include border-radius(0);
        }
    }
}

.microdata {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    z-index: -1;
}

.input-group-btn {
    line-height: 33px;
}

.html-content-header {
    font-size: 18px;
    text-align: center;
}

.help-block-error {
    display: block;
    color: #ff0000;
}

// Contact page
.map-wrapper {
    padding-bottom: 10px;
}

#googleMap {
    height: 350px !important;
}

.user-confirm-mail-success-msg {
    margin-bottom: 20px;
}

.search-section {

    .title {
        margin-top: 0;
        margin-bottom: 30px;
        text-align: center;
        h1 {
            padding-left: 0;
        }
        a {
            margin: 0;
        }
    }
}

.well {
    min-height: 20px;
    padding: 0;
    margin-bottom: 20px;
    background-color: transparent;
    border: none;
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
}

@media (min-width: $break-md) {
    .contact-info {
        h1, h2, h3, h4, h5, h6 {
            word-break: break-all;
        }
    }

    .modal-large {
        width: 960px;
    }
}

@media (max-width: $break-md) {
    .tooltip {
        display: none !important;
    }

    #googleMap {
        height: 250px !important;
    }

    .page-account {
        .left-box.mobile-slide {
            .row {
                margin-right: 0;
            }

            position: fixed;
            height: 100%;
            overflow: auto;
            width: 60%;
            top: 0;
            padding: 0;
            left: -100%;
            z-index: 20;
            @include box-shadow(0px 0px 5px rgba(0,0,0, .5));
            @include transition(all, .5s);
            > section {
                border: 0 !important;
            }
            &.active {
                left: 0;
            }
        }
    }
}

@media (max-width: $break-sm) {
    div[id*="recaptcha"] {
        transform: scale(0.74);
    }
    h2, .h2 {
        font-size: 20px;
    }

    .page-account {
        .left-box.mobile-slide {
            &.active {
                .mobile-slide-toggle {
                    top: 13px !important;
                }
            }
        }
    }

    .pagination {
        .first , .last {
            display: none;
        }
    }

    .modal-dialog {
        width: 90% !important;
        margin: 10px auto !important;
    }

    .text-info {
        font-size: 20px;
    }
}

@media (max-width: $break-ls) {
    
    .pagination {
        padding: 0;

        .next, .prev {
            display: none;
        }
    }

    .breadcrumb {
        padding-bottom: 15px;

        & + h1 {
            font-size: 23px;
            padding-bottom: 10px;
        }
    }
}

@media (max-width: $break-xs) {
    .modal-dialog {
        width: 290px !important;
        margin: 5px auto !important;
    }
}