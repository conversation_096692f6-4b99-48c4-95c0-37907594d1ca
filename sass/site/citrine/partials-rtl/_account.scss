.account-info-holder {
    h3 {
        margin-top: 40px;
        margin-bottom: 40px;
        text-align: center;
        position: relative;
        &:after {
            content: "";
            width: 50px;
            height: 3px;
            position: absolute;
            left: 0;
            right: 0;
            top: 80px;
            margin: auto;
            @include border-radius(3px);
        }
    }
    .account-views {
        margin: 0;

        li {
            display:inline-block;
            text-align: center;

            &:first-child {
                margin-left: 0;
            }

            > a {
                padding: 15px 30px;
                display: block;
                position: relative;
                
            }
        }

        li.active {
            > a {
                box-shadow: 0 -1px 3px rgba(1,1,1,.25);
            }
        }
    }
}

.title {
    h1, h2, h3, h4, h5 {
        padding: 0 0 0 15px;
    }
}

.order-details-box {
    padding-bottom: 20px;
}

.popup-order-details-box .order-details-box h3 {
    font-size: 18px;
    font-weight: 500;
    margin-top: 0;
}

.order-payments-box {
    h3 {
        font-size: 18px;
        font-weight: 500;
    }
}

.popup-order-details-box {
    .order-details-box label, .store-details-box label, .order-payments-box label {
        font-weight: 500;
    }

    .order-products tbody tr td {
        font-weight: 500;
    }
}

.popup-order-details-box {
    .order-products .order-product-item .variant-box .parameter .value,
    .order-details-box .order-product-item .variant-box .parameter .value,
    .order-payments-box .order-product-item .variant-box .parameter .value,
    .order-invoice-box .order-product-item .variant-box .parameter .value,
    .store-details-box .order-product-item .variant-box .parameter .value {
        font-weight: 500;

    }
}

.popup-order-details-box {
    .order-product {
        td {
            span {
                font-size: 13px;
            }

            span.name , span.value {
                font-size: 12px;
            }
        }
    }
}

.popup-order-details-box {
    .order-details-box .order-payments tbody tr td,
    .store-details-box .order-payments tbody tr td,
    .order-payments-box .order-payments tbody tr td {
        font-weight: 500;
    }
}

.right-box {
    .main-accordion {
        .collapse {
            display: block !important;
        }
    }
}

.contacts-information , .password-section  {
    margin: 0px 0 70px 0;

    legend {
        border: 0;
        
        margin-bottom: 29px;
    }

    .btn-primary {
        padding: 0 30px;
        height: 40px;
        
    }
}

.address-book , .remove-account-heading {
    display: block;
    
    margin-bottom: 29px;
}

.remove-account-heading {
    margin-bottom: 18px;
}

.contacts-information {
    .form-group {
        margin-bottom: 10px;
    }

    .alert {
        margin-left: 15px;
        display: inline-block;
        margin-bottom: 10px;
    }

    .alert.alert-warning {
        margin-left: 0;
        display: block;

        a {
            float: left;
        }
    }
}

.alert.files {
    margin-top: 20px;
}

.address-section {
    margin-bottom: 70px;

    .existing-add {
        margin-bottom: 20px;
    }

    .account-addresses {

        li {
            padding: 20px 0;

            > span {
                width: 23%;
                display: inline-block;
            }

            .postal-code {
                text-align: center;
            }

            .phone {
                text-align: left;
            }

            .actions {
                text-align: left;
                float: left;

                .address_action {
                    overflow: hidden;

                    i.glyphicon-ok {
                        display: none;
                    }

                    i.glyphicon-edit {
                        background: image-url("images/edit-button.png")no-repeat;
                        width: 16px;
                        height: 16px;

                        &:before {
                            display: none;
                        }

                        &:hover {
                            background: image-url("images/edit-button-hover.png")no-repeat;
                        }
                    }

                    i.glyphicon-remove {
                        background: image-url("images/remove-product.png")no-repeat;
                        width: 16px;
                        height: 16px;

                        &:before {
                            display: none;
                        }

                        &:hover {
                            background: image-url("images/remove-product-hover.png")no-repeat;
                        }
                    }
                }
            }
        }
    }

    #addressAddSubmit {
        height: 40px;
        padding: 0 30px;
    }
}

.remove-section {
    .remove-message {
        display: block;
        margin-bottom: 20px;
    }

    .btn-primary {
        
        height: 40px;
    }
}

.table-holder {
    border: 0 !important;

    table {
        margin-bottom: 0;

        @media (max-width: $break-sm) {
            thead {
                display: none;
            }
        }

        thead {
            th {
                border: 0;
                padding: 14px 0;
                font-size: 12px;
            }
        }

        tbody {
            tr {
                border-bottom: 1px solid #dcdbdb;

                @media (max-width: $break-sm) {
                    display: block;
                    margin-top: 50px;
                    &:first-of-type {
                        margin: 0;
                    }
                }

                td {
                    padding: 8px;
                    max-width: 100px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    @media (max-width: $break-sm) {
                        display: block;
                        text-align: left;
                        overflow: hidden;
                        line-height: 25px;
                        padding: 10px 15px;
                        max-width: 100%;
                        &:before {
                            content: attr(data-title);
                            float: right;
                        }
                        a {
                            display: inline-block;
                            float: left;
                        }
                    }
                }
            }

            .status-order-red , .status-fulfilment-red {
                color: #e74c3c !important;
            }

            i.glyphicon-list {
                background: image-url("images/order-form-image.png")no-repeat;
                width: 25px;
                height: 25px;

                &:before {
                    display: none;
                }
            }
        }
    }
}

.table > thead > tr > th, .table > thead > tr > td, .table > tbody > tr > th, .table > tbody > tr > td, .table > tfoot > tr > th, .table > tfoot > tr > td {
    padding: 18px 0;
}

.table.account-orders {
     td {
         a {
            display: inline-block;
            padding: 2px 20px;
         }
     }
}

.status-payment-orange , .status-order-orange {
    color: #ffa500;
}

.status-payment-red {
    color: #ff0000;
}

.status-fulfilment-green {
    color: #32cd32;
}

.status {
    margin-top: 15px;
    padding-top: 15px;
}

.account-orders {
    tr {
        img {
            max-width: 75px;
        }
    }
}

.popup-order-details-box {
    table {
        thead {
            @media (max-width: $break-sm) {
                display: none;
            }
        }
        tbody tr  {
            @media (max-width: $break-sm) {
                margin-bottom: 20px;
                display: block;
            }
            td {
                max-width: 200px;

                .image {
                    display: inline-block;
                    width: 70px;
                    height: 70px;
                    background-size: cover;
                }

                .variant-box {
                    display: inline-block;
                    vertical-align: top;
                    max-width: 130px;

                    span.name {
                        margin-right: 10px;
                    }
                }

                @media (max-width: $break-sm) {
                    display: block;
                    overflow: hidden;
                    line-height: 25px;
                    padding: 10px 15px;
                    text-align: right;
                    max-width: 100%;
                    
                    &:before {
                        content: attr(data-title);
                        float: left;
                    }
                }
            }
        }
    }

    .order-details-box {
        .clearfix {
            font-size: 14px;
        }
        label {
            font-size: 14px;
        }
    }
    .order-invoice-box > div > div {
        padding-top: 10px;
        padding-bottom: 10px;
        a {
            margin-right: 10px;
        }
    }
    
    .order-details-total {
        margin-top: 20px;

        .pull-right {
            padding: 0 15px;
            width: 50%;

            @media (max-width: $break-sm) {
                width: 100%;
            }
        }

        div {
            font-weight: 500;
            font-size: 13px;
            overflow: hidden;
        }

        label {
            float: right;
            font-weight: 400;
        }

        span {
            float: left;
        }
    }
}

.parameter {
    span {
        display: inline-block;
    }
}

.page-account {
    .mobile-slide-toggle {
        top: -25px !important;
    }

    .right-box {
        padding: 30px 45px;
        box-shadow: 0 2px 3px rgba(1,1,1,.25);

        .form-group input[type="checkbox"] {
            height: 25px;
        }
    }

    .left-box {
        margin-bottom: 0;
    }
}

.confirm ._form-row {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;

    ._form-col {
        -webkit-box-flex: 1;
        -webkit-flex: 1 1 50%;
        -ms-flex: 1 1 50%;
        flex: 1 1 50%;
        text-align: right;

        &:last-of-type {
            text-align: left;
        }

        ._button {
            width: 100%;
            max-width: 70%;
            padding: 5px 0;
        }
    }    
}

@media (max-width: $break-md) {
    .contacts-information , .password-section  {
        margin-top: 50px;
    }

    .table-holder {
        margin-top: 50px;
    }

    .modal.in .modal-dialog {
        width: 80% !important;
    }

    .account-info-holder {
        margin-bottom: 30px;

    }
    
    .table-responsive {
        tbody tr td,
        thead tr th {
            padding-right: 20px;
            padding-left: 20px;
        }
    }

    .mobile-slide {
        .account-info-holder {
            border-bottom: 0;

            .account-views {
                li {
                    margin: 20px 0 0 20px;
                    display: block;

                    a {
                        text-align: left;
                    }
                }
            }
        }
    }
}

@media (max-width: $break-sm) {
    .modal.in .modal-dialog {
        width: 90% !important;
    }

    .popup-order-details-box {
        overflow: hidden;
    }

    .modal-body {
        .table-responsive {
        }
    }

    .request-confirmation-link {
        float: none !important;
        display: inline-block;
    }

    .address-section .account-addresses li {
        position: relative;
        padding-left: 50px;

        > span {
            width: 100%;
            display: block;
            text-align: right !important;
        }

        .actions {
            float: none;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 20px;
            margin: auto;
        }
    }
}

@media (max-width: $break-ls) {
    .password-section, .password-section {
        
        .btn-primary, .btn-cancel {
            float: none !important;
            width: 100%;
        }
    }

    .confirm ._form-row {
        -webkit-flex-flow: column;
        -ms-flex-flow: column;
        flex-flow: column;

        ._form-col {
            -webkit-box-flex: 1;
            -webkit-flex: 1 1 auto;
            -ms-flex: 1 1 auto;
            flex: 1 1 auto;
            -webkit-box-ordinal-group: 3;
            -webkit-order: 2;
            -ms-flex-order: 2;
            order: 2;

            &:last-of-type {
                -webkit-box-ordinal-group: 2;
                -webkit-order: 1;
                -ms-flex-order: 1;
                order: 1;
                margin-bottom: 15px;
            }

            ._button {
                max-width: 100%;
            }
        }
    }
}

@media (max-width: 350px) {
    .modal.in .modal-dialog {
        width: auto;
    }

    .popup-order-details-box {
        .order-details-total  {
            span {
                margin-left: 90px;
            }
        }
    }
}
