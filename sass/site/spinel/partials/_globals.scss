body {
    font-size: $base-font-size;
}

body.page-home {
    .background-image {
        display: none;
    }
}

a {
    text-decoration: none !important;
}

img {
     max-width: 100%;

     &.js-lazy-load {
         opacity: 0;
         -webkit-transition: opacity .3s ease-out;
         -moz-transition: opacity .3s ease-out;
         -ms-transition: opacity .3s ease-out;
         -o-transition: opacity .3s ease-out;
         transition: opacity .3s ease-out;
     }
 }

.textbox, .product-details-description, .blog-article-content, .widget-info-box, .html-content-container-js, .slide-html, .showcase-info-box .description { 
    table {
        width: 100%;
        margin: 20px 0;

        th,
        td {
            border-collapse: collapse;
            padding: 3px 5px;
        }
    }

    ul { 
        list-style-type: disc; 
        list-style-position: inside; 
    }
    ol { 
        list-style-type: decimal; 
        list-style-position: inside; 
    }
    ul ul, ol ul { 
        list-style-type: circle; 
        list-style-position: inside; 
        margin-left: 15px; 
    }
    ol ol, ul ol { 
        list-style-type: lower-latin; 
        list-style-position: inside; 
        margin-left: 15px; 
    }

    img {
        max-width: 100% !important;
        height: auto !important;
   }

    .aligncenter {
        display: block;
        max-width: 100%;
        margin: 0 auto;
    }

    .alignleft {
        float: left;
        margin: 5px 20px 10px 0;
    }

    .alignright {
        float: right;
        margin: 5px 0 10px 20px;
    }

    .text-aligncenter {
        text-align: center;
    }

    .text-alignleft {
        text-align: left;
    }

    .text-alignright {
        text-align: right;
    }

    .text-alignjustify {
        text-align: justify;
    }
}

.textbox-iframe {
    height: 0;
    padding-bottom: 56.25%;
    position: relative;

    iframe {
        border: 0;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
    }
}

.faq {
    margin: 15px 0;

    ul {
        list-style-type: circle;
        padding: 0 20px;
        
        li {
            margin-bottom: 20px;
        }
    }
}

.bracketing { display: inline-block; }

.title {
    margin: 20px 0;
    h1, h2, h3, h4, h5 {
        font-size: $title-font-size;
        font-weight: $normal;
        padding: 0 0 0 20px;
        line-height: 45px;
        margin: 0;
    }
}

.black-title {
    margin: 30px 0;

    h1, h2, h3, h4, h5 {
        line-height: 45px;
    }

    background: none !important;
    width: 100%;
    padding: 0;

    h1 {
        display: inline-block;
        font-size: 30px !important;
        text-transform: capitalize;
        padding: 0;
        margin-bottom: 10px;
    }
}

.btn-primary {
    background: transparent !important;
    text-transform: capitalize;

    &:hover {
        @include opacity(0.6);
    }
}

.forgot-password .well {
    background-color: transparent;
}

.alert-danger {
    margin-bottom: 0;
}

.modal-dialog {
    border-top: 1px solid;
    .modal-content {
        @include box-shadow(0px 0px 25px rgba(0,0,0,0.25));
        @include border-radius(0px);
        border-width: 0 1px 3px 1px;
        border-style: solid;
        &.continue-shopping {
            .btn {
                width: 100%;
                margin-top: 20px;
            }

            .message {
                padding-bottom: 20px;
                display: block;
                text-transform: uppercase;
                text-align: center;
            }
        }
    }
    .modal-body {
        @include box-shadow(none);
        position: relative;
        .close {
            position: absolute;
            top: 0;
            right: 0;
            width: 25px;
            height: 25px;
            display: block;
            z-index: 9999;
            @include opacity(1);
            &:after {
                content: "+";
                display: block;
                text-align: center;
                line-height: 25px;
                font-size: 30px;
                font-weight: 100;
                @include rotate(45deg);
            }
            &:hover {
                @include opacity(.8);
            }
        }
    }
    .modal-footer {
        border-top: none;
        .btn {
            width: 100%;
        }
    }
    &.confirm {
        .modal-body, .modal-header {
            text-align: center;
            border: none;
        }
    }
}

.modal {
    .static-page-text,
    .textbox {
        padding: 10px 20px;

        @media (max-width: $break-md) {
            padding: 0;
        }
    }
}

.input-group-addon, .input-group-btn, .input-group .form-control {
    text-align: center;
}

.modal-backdrop.in {
    @include opacity(0.7);
}

.navbar-header {
    .header-actions {
        display: inline-block;
        margin-top: 40px;
        .search-box {
            input {
                line-height: 40px;
                height: 41px;
                margin-left: 5px;
                padding: 0 10px;
                display: inline-block;
                width: 200px;
            }
            button {
                height: 41px;
                width: 40px;
                display: inline-block;
                margin-left: -1px;
                i {
                    margin-top: 5px;
                }
            }
        }
        @media (max-width: $break-sm) {
            .search-box {
                position: absolute;
                top: -46px;
                left: 0;
                margin-right: 244px;
            }
        }
        @media (max-width: 505px) {
            .search-box {
                margin-right: 98px;
                input {
                    width: 150px;
                }
            }
        }
    }
}

.breadcrumb {
    padding: 25px 10px;
    margin: 0;
    background: transparent;
    width: 100%;
    text-align: left;
    font-size: 12px;
    text-transform: capitalize;

    + h1 {
        padding: 15px 0;
        margin: 0;
        background: transparent;
        width: 100%;
        text-align: center;
        text-transform: uppercase;
        font-size: 21px;
        font-weight: 600;
    }

    li, > li+li, li.active {
        &:after {
            content: ">";
            display: inline-block;
            padding: 0 5px;
            color: #ccc;
        }

        &:before {
            display: none;
        }
    }
    
    li:last-of-type:after {
        content: "";
    }
}

.pagination {
    width: 100%;
    padding: 15px 0;
    text-align: center;
    li {

        a {
            float: none;
            margin: 0 5px;
            padding: 12px 16px;
            display: block;
        }
        &:first-of-type {
            a:hover {
                background-color: transparent;
            }
        }
        &:last-of-type {
            a:hover {
                background-color: transparent;
            }
        }
    }

    @media (max-width: $break-sm) {
        display: block;
        position: relative;
        padding-top: 60px;
        text-align: center;

        li {
            &.page {
                a {
                    margin: 0 3px;
                }
            }

            a,
            &:last-child > a,
            &:last-child > span {
                margin: 0;
            }
        }

        .first,
        .last {
            display: none;
        }

        .prev {
            position: absolute;
            top: 15px;
            left: 10px;
        }

        .next {
            position: absolute;
            top: 15px;
            right: 10px;
        }
    }
}

.error-box {
    text-align: center;
    margin-top: 80px;
    font-size: 50px;

    .info-container {
        width: 641px;
        margin: 0 auto;

        .error-heading {
            width: 460px;
            height: 460px;
            border-radius: 50%;
            margin: 0 auto;
            position: relative;
            background-image: image-url("images/404-background.png");
            background-position: top center;
            background-repeat: no-repeat;
            display: block;

            h1 {
                font-size: 200px;
                position: absolute;
                top: 70%;
                left: 50%;
                @include translate(-50%, -50%);
                margin: 0;
                font-weight: medium;
            }
        }

        .error-title {
            font-size: 60px;
            margin-top: 50px;
            display: block;
            font-weight: bold;
            text-transform: uppercase;
        }

        .message {
            font-size: 30px;
            display: block;
            margin: 0 0 190px 0;
            text-transform: uppercase;
            font-weight: bold;
        }
    }

    @media (max-width: $break-md){
        .info-container {
            width: 441px;

            .error-heading {
                width: 300px;
                height: 300px;
                margin: 0 auto;
                position: relative;

                h1 {
                    font-size: 100px;
                    top: 90%;
                }
            }

            .error-title {
                font-size: 40px;
                margin-top: 50px;
            }

            .message {
                font-size: 25px;
                margin: 0 0 100px 0;
            }
        }
    }
    @media (max-width: $break-sm){
        .info-container {
            width: 241px;

            .error-heading {
                width: 230px;
                height: 293px;

                h1 {
                    top: 90%;
                    font-size: 100px;
                }
            }

            .error-title {
                font-size: 30px;
                margin-top: 50px;
            }

            .message {
                font-size: 22px;
                margin: 0px 0 50px 0;
            }
        }
    }
    .error-title {
        font-size: 50px;
    }
    @media (max-width: $break-md) {
        .error-title {
            font-size: 30px;
        }
    }
    @media (max-width: $break-sm) {
        .error-title {
            font-size: 20px;
        }
    }
    .message {
        display: block;
    }
    .floor {
        width: 100%;
        height: 150px;
        margin-bottom: -30px;
        margin-top: -30px;
    }
    @media (max-width: $break-md){
        .floor {
            height: 100px;
        }
    }
    @media (max-width: $break-sm){
        .floor {
            height: 70px;
        }
    }
    img {
        margin-top: 20px;
        width: 100%;
    }
}
.mobile-slide-toggle {
    display: none;
}

.rc-anchor-light {
    border: none;
    @include box-shadow(0);
    @include border-radius(0);
}

.bootstrap-touchspin {
    position: relative;
    display: table;
    border-collapse: separate;
    .input-group-btn-vertical {
        position: relative;
        white-space: nowrap;
        width: 1%;
        vertical-align: middle;
        display: table-cell;
        button {
            display: block;
            float: none;
            width: 100%;
            max-width: 100%;
            padding: 4.5px 10px;
            margin-left: -1px;
            position: relative;
            font-size: 7px;
            @include border-radius(0);
        }
    }
}

.microdata {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    z-index: -1;
}

.container-search {
    margin-top: 20px;

    .search-section {
        .title {
            margin: 0 0 20px;
            overflow: hidden;

            h1 {
                padding-right: 15px;
                display: inline-block;
                text-transform: capitalize;
                float:left;
            }

            a {
                float:right;
                line-height: 45px;
                margin-right: 15px;
            }
        }

        ul {
            margin-bottom: 0;

            li {
                overflow: hidden;
                padding: 15px 0;

                .image-holder {
                    float:left;
                    width: 15%;

                    img {
                        max-width: 100%;
                    }
                }

                .info-holder {
                    float:left;
                    width: 45%;
                }

                .actions-box {
                    float:right;

                    .search-result-price {
                        display: block;
                        text-align: center;
                        margin-bottom: 10px;
                    }
                }

                &:last-child {
                    border-bottom: 0;
                }
            }
        }
    }
}

.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group {
    margin-right: -2px;
}

.html-content-header {
    font-size: 18px;
    text-align: center;

    &:first-letter {
        text-transform: uppercase;
    }
}

.help-block-error {
    display: block;
    color: #ff0000;
}

.search-results-no-results {
    text-align: center;

    h2 {
        font-size: 48px;
        color: #333;
        font-weight: normal;
        margin: 100px 0 70px 0;

        &:first-letter {
            text-transform: uppercase;
        }
    }

    p {
        font-size: 18px;
        color: #666666;
        display: block;
        margin-bottom: 20px;

        &:first-letter {
            text-transform: uppercase;
        }
    }

    form {
        display: inline-block;
        text-align: center;
        margin-bottom: 200px;
    }

    .search-box {
        input {
            line-height: 40px;
            height: 41px;
            margin-left: 5px;
            padding: 0 10px;
            display: inline-block;
            width: 200px;
        }
        button {
            background: transparent;
            border: none;
            display: block;
            padding-top: 10px;
            margin-left: -35px;

            i {
                background-image: url('../../layouts/spinel/images/search.png?1437059078');
                display: inline-block;
                height: 20px;
                width: 20px;
            }
        }
    }
}

.page-page {
    img {
       max-width: 100% !important;
       height: auto !important;
       margin: 20px auto;
       display: block;

       &[style*="float:"] {
           &[style*="left"] {
               margin: 5px 15px 5px 0;
           }

           &[style*="right"] {
               margin: 5px 0 5px 15px;
           }
       }
    }
}

@media (min-width: $break-md) {
    .modal-large {
        width: 960px;
    }
}

@media (max-width: $break-md){
    .mobile-slide {
        .row {
            margin: 0;
        }

        .col-md-2 {
            padding: 0;
        }

        position: fixed;
        height: 100%;
        overflow: auto;
        width: 73%;
        top: 0;
        padding: 0 20px;
        left: -100%;
        margin-top: 0;
        min-width: 280px;
        z-index: 12;
        @include box-shadow(0px 0px 5px rgba(0,0,0, .5));
        @include transition(all, .5s);
        > section {
            border: 0 !important;
        }
        &.active {
            left: 0;
            .mobile-slide-toggle.back {
                display: inline-block;
                font: normal normal normal 14px/1 FontAwesome;
                font-size: inherit;
                text-rendering: auto;
                -webkit-font-smoothing: antialiased;
                width: 25px;
                height: 25px;
                display: block;
                position: absolute;
                top: 12px !important;
                right: 20px;
                font-size: 20px;
                margin: 0;

                &:before {
                    content: "\f053";
                }
            }
        }
    }
}

@media (max-width: $break-sm) {
    .actions-box {
        display: block;
        width: 100%;

        a.btn-primary {
            width: 100%;
            margin-top: 10px;
        }
    }

    .modal-dialog {
        width: 650px !important;
        margin: 10px auto !important;
    }
}

@media (max-width: 500px) {
    .pagination {
        .first , .last {
            display: none;
        }
    }

    .container-search {
        .search-section {
            ul {
                li {
                    .image-holder {
                        float:left;
                        width: 20%;

                        img {
                            width: 68px;
                            height: 68px;
                        }
                    }

                    .info-holder {
                        float:right;
                        width: 60%;
                    }

                    .actions-box {
                        width: 100%;
                        float:left;
                        margin-top: 14px;

                        .search-result-price {
                            display: block;
                            text-align: center;
                        }

                        .btn-primary {
                            margin-top: 10px;
                            width: 100%;
                        }
                    }

                    &:last-child {
                        border-bottom: 0;
                    }
                }
            }
        }
    }
}

@media (max-width: $break-ls) {
    .pagination {
        padding: 0;

        .prev , .next {
            display: none;
        }
    }

    [id*="recaptcha_div_"] {
        transform: scale(0.80);
        transform-origin: 0 center;
        max-width: 100%;
    }
}

@media (max-width: $break-xs) {
    .modal-dialog {
        width: 290px !important;
        margin: 5px auto !important;
    }
}
