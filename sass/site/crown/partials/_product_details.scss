.product {
    &__details {
        z-index: 2;
        width: 25%;
        min-width: 330px;
        height: 100%;
        position: relative;
        display: inline-block;
        float: left;
        padding: 65px 50px;

        h1 {
            margin-bottom: 32px;
        }
    }

    &__vendor {
        margin-bottom: 24px;
    }

    &__description {
        position: relative;
        overflow: hidden;
        margin-bottom: 42px;
        @include transition(all 0.5s ease);

        &:after {
            content: '';
            display: block;
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 100px;
            opacity: 1;
            background: -moz-linear-gradient(top,  rgba(255,255,255,0) 0%, rgba(249,249,249,1) 100%);
            background: -webkit-linear-gradient(top,  rgba(255,255,255,0) 0%,rgba(249,249,249,1) 100%);
            background: linear-gradient(to bottom,  rgba(255,255,255,0) 0%,rgba(249,249,249,1) 100%);
            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00ffffff', endColorstr='#f9f9f9',GradientType=0 );
            -webkit-transition: opacity 0.4s ease;
            -moz-transition: opacity 0.4s ease;
            -ms-transition: opacity 0.4s ease;
            -o-transition: opacity 0.4s ease;
            transition: opacity 0.4s ease;
        }

        &.expand {
            &:after {
                opacity: 0;
            }
        }
    }

    &__price {
        padding: 15px 0;
        margin: 35px 0;
        display: block;

        &__holder {

        }

        &__new {

        }

        &__old {

        }

        &__msg {
            padding: 8px 0 7px;
        }

        .price-old-js {
            display: inline-block;
            text-decoration: line-through;
            text-transform: uppercase;
        }

        .price-discount-js {
            display: block;
            font-size: 14px;
        }
    }

    &__options {
        margin: 42px 0;
        padding-bottom: 22px;
    }

    &__sku {
        display: block;
        margin-top: 15px;
    }

    &__quantity {
        @extend %flexbox;
        @include flex-flow(row wrap);
        @include justify-content(space-between);
        @include align-items(center);

        .input-group-btn {
            display: none;
        }

        .form-control {
            width: 60px;
            height: 56px;
            border-radius: 6px !important;
            text-align: center;
            box-shadow: none;
            padding: 0 18px;
            margin: 0 20px 20px 0;
            -webkit-appearance: none;
            -moz-appearance: textfield;
        }
    }

    &__buy-btn {
        width: calc(100% - 84px);

        .btn-primary {
            width: 100%;
        }
    }

    &__gallery {
        width: calc(75% - 220px);
        height: 100%;
        position: fixed;
        top: 0;
        right: 0;
        overflow: hidden;

        .owl-item {
            float: left;
        }

        .js-slide {
            text-align: center;
            @include flexbox();
            @include align-items(center);
            @include justify-content(center);

            img {
                display: inline-block;
            }
        }
    }

    .select2-container {
        &.select2-allowclear {
            .select2-choice {
                abbr {
                    display: none;
                }
            }
        }
    }
}

.tags-box,
.tags-title {
    padding: 15px 0 10px;

    .tags {
        position: relative;
        margin: 0 -5px 0 0;
        @extend %flexbox;
        @include flex-flow(row wrap);

        li {
            display: inline-block;
            //padding: 10px;
            margin: 0 5px 5px 0;
            //text-align: -webkit-match-parent;
            //border-radius: 3px;
        }
    }
}

.expand-description-btn {
    //padding-top: 40px;
    margin-bottom: 40px;

    .less {
        display: none;
    }
}

.share-this {
    margin-top: 20px;

    a {
        float: none !important;
    }

    label {
        @include flexbox;
        @include align-items(center);
        cursor: pointer;
        margin-bottom: 15px;

        span {
            display: inline-block;
        }
    }

    .share-title {

    }

    &__wrapper {
        height: 1px;
        overflow: hidden;
        @include transition(height 0.5s ease);

        > div {
            @include flexbox;
            @include transition(visibility 0.5s ease);
            visibility: hidden;
        }
    }

    .checkbox-animation:checked + div {
        height: 38px;
        padding: 0;

        > div {
            visibility: visible;
        }
    }


    &__position2 {
        float: right;
        margin-right: 20px;
        text-align: right;

        label {
            @include justify-content(flex-end);
        }
    }
}

.parameter-holder {
    .help-block-error {
        padding: 0;
        margin-bottom: 10px;
    }
}

/* parameters */
._parameter-radio-value {
    margin-bottom: 5px;

    ._radio {
        padding-top: 2px;
    }

    &:last-child {
        margin-bottom: 0;
    }
}

._parameter-image-values {
    margin-left: -1%;
    margin-bottom: -1%;
    @extend .clearfix;
}

._parameter-image-value {
    float: left;
    border: 1px solid;
    width: 24%;
    max-width: 100px;
    margin-left: 1%;
    margin-bottom: 1%;
    word-break: break-word;

    &.active {
        border-width: 2px;
    }

    ._radio {
        display: block;
        height: 0;
        padding: 0;
        padding-bottom: 100%;
        position: relative;
        overflow: hidden;

        .radio {
            top: 5px;
            left: 5px;
        }

        .radio-label {
            display: inline-block;
            padding-top: 4px;
            padding-left: 30px;
            padding-right: 5px;
        }

        img {
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
                    transform: translate(-50%, -50%);

            ~ .radio,
            ~ .radio-label {
                display: none;
            }
        }
    }
}

._parameter-color-values {
    margin-right: -5px;
    margin-bottom: -5px;
    @extend .clearfix;
}

._parameter-color-value {
    float: left;
    margin-right: 5px;
    margin-bottom: 5px;

    ._radio {
        display: block;
        width: 34px;
        height: 34px;
        border: 2px solid transparent;
        padding: 0;
        margin: 0;
        font-size: 0;
        line-height: 0;

        .radio {
            display: none;
        }

        .radio-label {
            display: none;
        }
    }
}

@media (max-width: $break-xlg) {
    .product {
        &__gallery {
            width: calc(100% - 620px);
        }
    }
}
@media (max-width: $break-mobile) {
    .product-details-js {
        @include flexbox;
        @include flex-flow(column);

        .product {
            &__quantity {
                @include flex-flow(column);
            }

            &__quantity-box {
                width: 100%;
                .input-group.bootstrap-touchspin {
                    margin-left: 0;
                    width: 100%;

                    .form-control {
                        width: 100%;
                        margin: 0 0 15px 0;
                    }
                }
            }

            &__description {
                text-align: center;
            }

            &__details {
                min-width: initial;
                width: 100%;
                height: auto;
                @include order(2);
                padding: 30px 25px;
            }

            &__buy-btn {
                width: 100%;
            }

            &__gallery {
                width: 100%;
                position: relative;
                height: auto;

                .owl-height {
                    //height: auto !important;
                }
            }

            &__options {

            }
        }

        .tags {
            @include justify-content(center);
        }

        .expand-description-btn {
            text-align: center;
        }


        .share-this {
            margin-top: 20px;

            label {
                @include justify-content(center);
            }

            &__wrapper {
                > div {
                    @include justify-content(center);
                }
            }
        }
    }
}
