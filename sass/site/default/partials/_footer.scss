footer {
    @include flex(none);
    padding: 30px 0 15px 0;
    .copyright {
        padding: 10px 0;
        text-align: center;
        .footer-social {
            height: 100%;
            text-align: left;
            .extra-social {
                line-height: 1;
                margin-bottom: 0;
                li {
                    position:relative;
                    display: inline-block;
                    margin-right: 5px;

                    &:last-of-type {
                        margin-right: 0;
                    }
                    
                    &:hover {
                        @include opacity(.8);
                    }

                    a {
                        display: block;
                        width: 26px;
                        height: 26px;
                        line-height: 26px;
                        border-radius: 50%;
                        text-align: center;
                    }
                }
            }
        }
        .footer-text {
            line-height: 26px;
        }
        .footer-providers {
            text-align: right;
        }
    }
    .navigation-footer {
        margin-bottom: 13px;
        //@include column-count(4);
        .collapse-icon {
            display: none;
        }
        > li {
            width: 33.33%;
            position: static;
            float: left;
            padding: 0 15px 10px 0;
            text-align: left;
            > a {
                display: inline-block;
                @include font-size($nav-font-size);
                text-transform: capitalize;
                &:hover {
                    background: none;
                }
            }
            ul.level-1 {
                padding-top: 10px;
                li  {
                    margin-bottom: 7px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                    
                    a {
                        display: inline;
                        line-height: 1.5;
                    }
                }
            }
        }
    }
}
.full-container {
    width: 100%;
    padding: 40px 0px;
    margin-top: 40px;
    .banner-image-caption {
        display: none;
    }
    &.top {
        margin: 0 0 40px 0;
    }
}

.newsletter {
    display: table;
    table-layout: fixed;
    width: 100%;

    &-text {
        display: table-cell;
        vertical-align: middle;
        padding: 15px 0;
        padding-right: 15px;
    }

    &-title {
        margin: 5px 0;
    }

    &-description {
        &-modal {
            p,
            img {
                margin: 0 0 10px;
            }
        }

        p,
        img {
            margin: 5px 0;
        }
    }

    &-form {
        display: table-cell;
        vertical-align: middle;
        padding: 20px 0;
    }

    &-group {
        display: table;
        width: 100%;
        margin-bottom: 0;
    }

    &-input {
        display: table-cell;
        vertical-align: middle;
        width: 100%;
        position: relative;

        .help-block-error {
            position: absolute;
            top: 100%;
            left: 0;
        }

        input {
            width: 100%;
        }
    }

    &-button {
        display: table-cell;
        vertical-align: middle;
        padding-left: 10px;
    }

    @media (max-width: $break-sm) {
        display: block;

        &-text {
            display: block;
        }

        &-form {
            display: block;
            padding-top: 0;
        }
    }
}

@media (max-width: $break-lg) {
    .full-container .banner-image {
        margin-bottom: 30px;
    }
    /*
    .navigation-footer {
        @include column-count(3 !important);
    }
    */
}
@media (min-width: $break-sm) {
    .footer-social {
        padding: 8px 0;
    }
    .footer-social {
        padding-left: 0;
    }
    .footer-providers {
        padding-right: 0;
    }
}
@media (max-width: $break-sm) {
    .full-container .banner-image {
        margin-bottom: 20px;
    }

    .full-container {
        padding: 20px 0px;
        margin-top: 20px;
        &.top {
            margin: 0 0 20px 0;
        }
    }

    footer {
        .copyright {
            padding-top: 20px;
        }
        .footer-text {
            margin: 10px 0;
        }
    }
}
@media (max-width: $break-ls) {
    footer {
        .navigation-footer {
            margin-bottom: 0;
            > li {
                width: 100%;
                float: none;
                text-align: center;
            }
        }
    }
}
@media (max-width: 420px) {
    .navigation-footer {
        //@include column-count(auto !important);
        text-align: center;
        > li {
            display: block !important;
        }
    }
}
/*
@media (min-width: $break-md) {
    .navigation-footer {
        @include column-count(3 !important);
    }
}
@media (max-width: $break-sm) {
    .navigation-footer {
        @include column-count(2 !important);
    }
}
*/

