$panel-width: 600px;
$panel-width-medium: 960px;
$panel-width-wide: 1170px;
$panel-width-wider: 1500px;
$panel-width-custom-helpdesk: 727px;
$panel-horizontal-offset: 30px;
$panel-bg-color: #f6f7fb;
$panel-borders-color: #e6e6e6;
$panel-transition-duration: .3s;
$panel-close-position: calc(100% + 10px);

.ios-device,
.ios-device body {
    position: relative;
    overflow: hidden;
    height: 100%;
}

.side-panel-open {
    padding-right: 0 !important;
    overflow: hidden;

    .select2-drop-mask {
        z-index: $z-over-panel;
    }

    .select2-drop,
    .intl-tel-input.iti-container {
        z-index: $z-over-panel + 1;
    }
    .user-account {
        z-index: 1000;
    }

    .mce-menu {
        position: fixed;
    }
}

.side-panel {
    background-color: $panel-bg-color;
    width: $panel-width;
    height: 100%;
    position: fixed !important;
    top: 0;
    z-index: $z-panel;
    transition: $panel-transition-duration;
    outline: 0;

    &.open {
        transform: translate(0, 0);
        box-shadow: 0 0 30px rgba(0, 0, 0, .5);
        //overflow-y: scroll;
        //overflow-x: auto;
    }

    &.wide {
        width: $panel-width-wide;

        .side-panel-inner {
            width: $panel-width-wide;
        }

        .side-panel-header {
            h3 {
                max-width: 730px;
            }
        }
    }

    &.wider {
        max-width: $panel-width-wider;
        width: 100%;

        .side-panel-inner {
            max-width: $panel-width-wider;
            width: 100%;
        }

        .side-panel-header {
            h3 {
                max-width: 850px;
            }
        }
    }

    &.medium {
        width: $panel-width-medium;

        .side-panel-inner {
            width: $panel-width-medium;
        }

        .side-panel-header {
            h3 {
                max-width: 400px;
            }
        }
    }

    &.helpdesk-iframe {
        width: $panel-width-custom-helpdesk;

        .side-panel-inner {
            width: $panel-width-custom-helpdesk;
        }
    }

    .cc-panel-pull {
        margin-top: -$panel-horizontal-offset;
    }

    .cc-checkout-sidebar-section,
    .cc-totals-row-total {
        border-color: $panel-borders-color;
    }

    &.has-animation {
        transition: 1s cubic-bezier(0.77, 0, 0.175, 1);
    }

    .side-panel-header {
        align-items: center;
        border-bottom: 1px solid #e5e5e5;
        display: flex;
        justify-content: flex-start;
        margin-bottom: 20px;
        padding: 8px 20px;
        background-color: #fafafa;
        height: 53px;

        .side-panel-title {
            color: #26292f;
            font-size: 24px;
            font-weight: 300;
        }

        .close {
            margin-right: 16px;
            width: 23px;
            min-width: 21px;

            &:hover {
                opacity: .8;
            }
            &:active {
                outline: 0 none;
            }
        }
    }

    .grid-controls {
        padding: 10px 0;
    }
    .form-control-check.pull-left {
        text-align: left;
    }
}

.side-panel-container {
    max-width: 725px;
    margin: 0 auto 20px;
}

.side-panel-left {
    left: 0;
    transform: translate(-100%, 0);

    &.has-animation {
        &.animated {
            left: 100% !important;
        }
    }

    .side-panel-close {
        left: $panel-close-position;
    }

    .side-panel-inner {
        .side-panel-close {
            left: $panel-horizontal-offset;
        }
    }
}

.side-panel-right {
    right: 0;
    transform: translate(100%, 0);

    &.has-animation {
        &.animated {
            right: 100% !important;
        }
    }

    .side-panel-close {
        right: $panel-close-position;
    }

    .side-panel-inner {
        scrollbar-width: none;
        .side-panel-close {
            right: $panel-horizontal-offset;
        }
    }

    .side-panel-title {
        display: none;
    }

    .side-panel-body-section {
        margin-top: 0;
        padding-top: 0;
        form, .box-viber {
            margin-top: 74px;
        }
        .box-viber {
            form {
                margin-top: 0;
            }
        }
    }

    .side-panel-header {
        border-bottom: 0;
        margin-bottom: 0;
        h3 {
            color: #333333;
            font-weight: 300;
            font-size: 23px;
            white-space: nowrap;
            width: auto;
            max-width: 350px;
            overflow: hidden;
            text-overflow: ellipsis;
            text-transform: none;
        }
        &.add-card {
            border-bottom: 1px solid $panel-borders-color;
        }

        .left-controls, .right-controls {
            display: flex;
            align-items: center;
        }

        .right-controls {
            .btn {
                margin-left: 10px;
            }
        }
    }

    .box-title.fixed-top {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 2;
        .side-panel-header {
            justify-content: space-between;
        }
    }

    .side-panel-inner {
        padding: 0;
    }

    .side-panel-close {
        display: none;
    }

    ::-webkit-scrollbar {
        //width: 0;
    }

}

.side-panel-stretch {
    right: 0;
    left: 0;
    width: 100%;
    transform: translate(100%, 0);
    overflow: auto;

    &.has-animation {
        &.animated {
            right: 100% !important;
        }
    }

    .side-panel-close {
        right: $panel-close-position;
    }

    .side-panel-inner {
        .side-panel-close {
            right: $panel-horizontal-offset;
        }
    }
}

.side-panel-inner {
    width: $panel-width;
    max-width: 100%;
    height: 100%;
    padding: 0 $panel-horizontal-offset;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;

    &.js-box-loader-container {
        .side-panel-close {
            z-index: 1040;
        }
    }

    .side-panel-close {
        display: none;
        color: $main-smooth-text-color;
        text-shadow: none;
    }
}

.side-panel-body-section {
    padding: $panel-horizontal-offset 20px;
}

.side-panel-title,
.side-panel-body,
.side-panel-body-section {
    border-bottom: 1px solid $panel-borders-color;

    &:last-child {
        border: 0;
    }
}

.side-panel-title {
    display: table;
    width: auto;
    position: relative;

    h1, h2, h3, h4, h5, h6 {
        text-align: left;
        text-transform: initial;
        padding: 0;
        margin: 0;
    }
}

.side-panel-title-main,
.side-panel-title-addon {
    display: table-cell;
    vertical-align: middle;
    padding: $panel-horizontal-offset 0;
}

.side-panel-title-main {
    padding-right: 20px;
}

.side-panel-title-addon {
    width: 1px;
    white-space: nowrap;
}

.side-panel-footer {
    border-top: none;
    margin-top: 0;
    padding-bottom: 20px;
    padding-top: 20px;
    padding-right: 30px;
    text-align: right;
    button {
        margin-left: 10px;
    }
}

.side-panel-close {
    background: transparent;
    border: 0;
    font-family: $font-awesome, sans-serif;
    color: $panel-bg-color;
    font-size: 30px;
    line-height: 1;
    position: absolute;
    outline: 0;
    top: 5px;
    z-index: 1;
    text-shadow: 0 0 2px rgba(0, 0, 0, 1);
    transition: .2s;

    @media hover {
        &:hover {
            color: $panel-borders-color;
        }
    }
}

.side-panel-backdrop {
    background-color: #070d3a;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transition: $panel-transition-duration;
    z-index: $z-panel-backdrop;
    outline: 0;

    &.open {
        opacity: .5;
    }
}

@media (max-width: 1270px) {
    .side-panel {
        .container,
        .container-fluid {
            padding: 0;
        }

        &.wide,
        &.wider {
            width: 100%;

            .side-panel-close {
                display: none;
            }

            .side-panel-inner {
                width: auto;

                .side-panel-close {
                    display: block;
                }
            }
        }
    }
}

@media (max-width: $screen-desktop) {
    .side-panel {
        -webkit-overflow-scrolling: touch;
    }
}

@media (max-width: 992px) {
    .side-panel {
        width: 100%;

        .side-panel-close {
            display: none;
        }
    }

    .side-panel-inner {
        width: auto;

        .side-panel-close {
            display: block;
        }
    }
}

@media (max-width: 768px) {
    $panel-horizontal-offset-mobile: 20px;

    .side-panel-inner {
        padding: 15px $panel-horizontal-offset-mobile 0;
    }

    .side-panel-left {
        .side-panel-inner {
            .side-panel-close {
                left: $panel-horizontal-offset-mobile;
            }
        }
    }

    .side-panel-right {
        .side-panel-inner {
            .side-panel-close {
                right: $panel-horizontal-offset-mobile;
            }
        }
    }

    .side-panel-title-main,
    .side-panel-title-addon,
    .side-panel-body-section {
        padding: 20px 0;
    }
}