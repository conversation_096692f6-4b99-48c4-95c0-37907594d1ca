html, .sitecp {
    width: 100% !important; 
    height: 100% !important;
    min-width: 1000px;
    position: relative;
}

.sitecp {
    background-color: $main-bg-color;
    font-family: roboto, sans-serif;
    padding: 0 0 0 100px;
    //padding: 93px 0 0 100px;

    @media (min-width: 1201px) {
        padding-left: 200px;
    }

    &.body-secondary {
        background-color: $cc-background-main;
    }
    .page-container {
        padding-left: 20px;
    }
}


h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: roboto, sans-serif;
    font-weight: 300;
}

a {
    text-decoration: none !important;
    color: #7c83b7;
    &:hover, &:active, &:focus {
        color: #352164;
    }
}

a,
*:focus {
    outline: none;
}

ul {
    padding: 0;
    margin: 0;

    li {
        list-style: none;
    
        &:focus {
            outline: none;
        }
    }
}

@-webkit-viewport {
  width: device-width;
}

@-moz-viewport {
  width: device-width;
}

@-ms-viewport {
  width: device-width;
}

@-o-viewport {
  width: device-width;
}

@viewport {
  width: device-width;
}

@mixin clip-path($clip) {
  -webkit-clip-path: $clip;
  -moz-clip-path: $clip;
  -ms-clip-path: $clip;
  clip-path: $clip;
}

@mixin clearfix() {
    &:before,
    &:after { content: " "; display: table; }
    &:after { clear: both; }
}

/* Internet Explorer 10 doesn't differentiate device width from viewport width,
and thus doesn't properly apply the media queries in Bootstrap's CSS. To address this, following CSS code applied */
@-ms-viewport {
  width: auto !important;
}

/***
Font Awesome Icons
***/
[class^="icon-"],
[class*=" icon-"] {
  display: inline-block;
  font-size: 15px;
  *margin-right: .3em;
  line-height: 14px;
}

/***
Make font awesome icons fixed width(latest version issue)
***/
li [class^="icon-"],
li [class*=" icon-"] {
  display: inline-block;
  width: 1.25em;
  text-align: center;
}
li [class^="icon-"].icon-large,
li [class*=" icon-"].icon-large {
  /* increased font size for icon-large */
  width: 1.5625em;
}

.container-fluid {
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
}

.container-small {
    max-width: 720px;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
}

.img-responsive {
    vertical-align: middle;
    margin: auto;
    padding: 23px 0;
    width: 80px;
    
    @media (min-width: 1201px) {
        width: 130px;
        margin: auto;
        padding: 17px 0;
    }
}

.pie_chart {
    position: absolute !important;
    width: 80%;
    display: block;
    float: left;
}

.highcharts-tooltip > span {
    background-color: #fff;
    z-index: 9;
    padding: 5px;
}

.highcharts-title {
    font-size: 28px !important;
    font-weight: 300 !important;
    font-family: roboto, sans-serif !important;
    color: #26292f !important;
    &:first-letter {
        text-transform: capitalize;
    }
}
.dash-view-chart {
    font-size: 16px !important;
    color: #5cb3ec !important;
    font-family: roboto, sans-serif !important;
}


/***
Close icon used for modal dialog and other UI element close buttons
***/
.close {
  display: inline-block;
  margin-top: 0px;
  margin-right: 0px;
  width: 21px;
  height: 21px;
  opacity: 1;
  background-repeat: no-repeat !important;
  background-image: image-url("img/all-icons/delete.png") !important;
  &:hover {
    opacity: .8;
  }
}

.tooltips {
    color: #333;
    &.additional {
        position: absolute;
        top: 28px;
        right: 30px;
    }
}

.inactive {
    color: #fc4f4d;
}

.active {
    color: #8d58e0;
}

.tooltip {
    z-index: 9999;
    .tooltip-inner {
        padding: 7px 12px;
        background-color: #191d24;
        font-family: Lato, sans-serif !important;
        font-size: 14px;
        font-weight: 300;
    }
    &.top .tooltip-arrow {
        border-top-color: #191d24;
    }
    &.bottom .tooltip-arrow {
        border-bottom-color: #191d24;
    }
    &.left .tooltip-arrow {
        border-left-color: #191d24;
    }
    &.right .tooltip-arrow {
        border-right-color: #191d24;
    }
}

.relative {
    position: relative !important;
}

.videos-list {
    margin-top: -$layout-offset;

    [class^="col-"] {
        margin-top: $layout-offset;
    }

    .video {
        border: 1px solid #ccc;
        background-color: #000;
    }
}

.video-title {
    text-align: center;
    margin-bottom: 15px;

    h4,
    h5 {
        font-weight: bold;
        overflow: hidden;
    }

    h4 {
        height: 38px;
    }

    h5 {
        height: 30px;
    }
    
    @media (max-width: 1500px) {
        h4 {
            font-size: 16px;
            height: 36px;
        }
    }
}

.video {
    padding-bottom: 56.25%;
    position: relative;

    iframe,
    video {
        border: 0;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
}

#HW_frame_cont {
    z-index: 10002 !important;
}

//Action icons

.actions-add, .actions-approve, .actions-archive, .actions-ban, .actions-change,
.actions-complete, .actions-deactive, .actions-delete, .actions-edit, .actions-new,
.actions-not-spam, .actions-not-ban, .actions-not-new, .actions-not-sale, .actions-sale,
.actions-spam, .actions-tag, .actions-unarchive, .actions-pin {
    position: absolute;
    top: 0;
    bottom: 0;
    left: -30px;
    margin: auto;
    display: block;
}
//Action icons End

table.listing  {
    background-color: $box-bg-color;
    border-collapse: separate;

    thead tr th {
        border-bottom: 1px solid #ddd !important;
    }
    tbody tr {
        position: relative;
    }
    tbody tr td:last-child, thead tr th:last-child {
        padding-right: 20px;
    }
    tbody tr td:first-child, thead tr th:first-child {
        padding-left: 20px;
    }
    tbody tr td > i {
        display: inline-block;
        vertical-align: middle;
        padding-right: 10px;
    }
    .translation-wrapper {
        .plain-translation {
            color: #5cb3ec;
        }
        textarea {
            padding: 10px;
        }
    }
}

.drop {
    display: block;
    right: 10px;
    top: 54px;
    padding: 0 !important;
    z-index: 1;
    text-align: left;
    ul {
        padding: 0 !important;
        margin: 0;
        text-align: left !important;
        li {
            list-style: none;
            padding: 5px 20px 5px 40px !important;
            border-bottom: 1px solid #ebebeb;
            a {
                font-size: 13px;
                color: #898989;
                line-height: 30px;
                position: relative !important;
                text-transform: capitalize;
            }
            &:hover {
                background-color: #f9f9f9;
            }
            &:last-of-type {
                border-bottom: 0;
            }
            &:hover a {
                color: darken(#898989, 30%);
            }
        }
    }
}
.popover {
    border: 1px solid #ebebeb !important;
    z-index: 9999;
    border-radius: 2px;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.15);
    .popover-content {
        padding: 0 !important;
        ul li {
            list-style: none;
            padding: 5px 20px 5px 40px !important;
            border-bottom: 1px solid #ebebeb;
            a {
                font-size: 13px;
                color: #898989;
                line-height: 30px;
                position: relative !important;
                text-transform: capitalize;
            }
            &:hover {
                background-color: #f9f9f9;
            }
            &:last-of-type {
                border-bottom: 0;
            }
            &:hover a {
                color: darken(#898989, 30%);
            }
        }
    }
    .arrow {
        border-bottom-color: #ebebeb !important;
    }
}

.container-small {
    max-width: 720px;
    margin: 0 auto;
}

/***
Popovers fixes
***/
.popovers {
    cursor: pointer !important;
}
.popover.fade {
    visibility: hidden;
    &.in {
        visibility: visible;
    }
}

.chart-switcher-container {
    ul {
        li {
            background-color: #352164;
            color: #fff;
            line-height: 36px;
            padding: 0 10px;
            width: auto;
            margin-bottom: 10px;
            cursor: pointer;
            border-radius: 6px;
            &:first-letter {
                text-transform: capitalize;
            }
            &:hover {
                background-color: lighten(#352164, 5%);
            }
            &:last-of-type {
                margin-bottom: 0;
            }
            &.active {
                background-color: #8d58e0;
                &:hover {
                    background-color: lighten(#8d58e0, 5%);
                }
            }
        }
    }
}

.upload-box {
    display: table;
    width: 100%;
    .image-upload-holder {
        display: table-cell;
        padding: 20px;
        border-right: 1px solid #dedede;
        width: 190px;
        height: 190px;
        overflow: hidden;
        img {
            width: auto;
            height: auto;
            max-width: 100%;
            max-height: 100%;
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
                    transform: translate(-50%, -50%);
        }
        .image {
            position: relative;
            .remove-image-quick {
                background-color: rgba(22, 23, 26, 0.8);
                line-height: 60px;
                width: 190px;
                text-align: center;
                height: 60px;
                margin: -20px 0 0 -20px;
                vertical-align: middle;
                position: absolute;
                bottom: -80px;
                transition: all, .1s;
                i {
                    display: inline-block;
                    vertical-align: middle;
                    margin-right: 3px;
                    margin-top: -3px;
                    transform: scale(.8);
                }
                span {
                    font-weight: 300;
                    color: rgba(213, 213, 213, 1);
                    &:first-letter {
                        text-transform: capitalize;
                    }
                }
            }
        }
        &:hover {
            .image > a {
                bottom: -20px;
            }
        }
    }
    .upload-field-box {
        display: table-cell;
        vertical-align: middle;
        padding: 20px;
        width: 100%;
    }
    &.upload-categories {
        .image-upload-holder {
            width: 140px;
            height: 140px;
            .image {
                width: 100px;
                height: 100px;
                .remove-image-quick {
                    width: 141px;
                }
            }
        }
    }
}

.fileupload {
    .btn-revert {
        background-color: transparent;
        border: 0;
        position: absolute;
        top: 50%;
        right: 65px;
        left: auto !important;
        transform: -webkit-translate(0, -50%);
        transform:         translate(0, -50%);
        box-shadow: none;

        i {
            display: block;
        }

        &:hover {
            opacity: .8;
        }
    }

    .btn-file {
        position: relative;

        i {
            vertical-align: middle;
            display: inline-block;
        }

        .fileupload-exists {
            i {
                margin: 0;
            }
        }
    }
}

.modal-header {
    padding: ($modal-offset - 5px) $modal-offset;
}

.modal-body,
.modal-footer {
    padding: $modal-offset;
}

.modal-title {
    color: #26292f;
    font-size: 24px;
    font-weight: 300;
}

.modal-content {
    .modal-footer {
        border-top: none;
        margin-top: 0;
        padding-top: 0;
    }
}

//.sweet-overlay {
//    z-index: 1999;
//}

.control-label {
    color: #7a7d84;
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 5px !important;
    padding: 0 !important;
    text-align: left !important;
    width: auto;
    display: inline-block;
    max-width: 100%;
    
    &:first-letter {
        text-transform: capitalize;
    }
}
.control-label-bottom {
    margin: 5px 0 0 !important;
}

.form-add {
    .row {
        margin: 0 0 20px;
    }

    .form-group {
        background-color: #fafafa;
        border: 1px solid #dedede;
        border-top: 3px solid #dedede;
        padding: 30px 20px 20px !important;

        &:last-child {
            border: 1px solid #dedede !important;
            border-top: 3px solid #dedede !important;
        }
    }
}

.main-fields-holder {
    border-top: 1px solid #d5d5d6;
    //border-bottom: 1px solid #d5d5d6;
    padding: 15px 5px;
    margin: -21px -5px 0;
    input {
        background-color: #fff;
        line-height: 35px;
    }
    .main-box {
        input {
            background-color: #fafafa;
        }
        input.placeholder {
            color: lighten(#26292f, 30%);
            font-size: 14px !important;
            font-weight: 400 !important;
        }
        input:-moz-placeholder {
            color: lighten(#26292f, 30%);
            font-size: 14px !important;
            font-weight: 400 !important;
        }
        input::-moz-placeholder {
            color: lighten(#26292f, 30%);
            font-size: 14px !important;
            font-weight: 400 !important;
        }
        input:-ms-input-placeholder {
            color: lighten(#26292f, 30%);
            font-size: 14px !important;
            font-weight: 400 !important;
        }
        input::-webkit-input-placeholder {
            color: lighten(#26292f, 30%);
            font-size: 14px !important;
            font-weight: 400 !important;
        }
    }
    .textarea-title-holder {
        background-color: #fafafa;
        border-bottom: 0;
        display: block;
        border-radius: 6px 6px 0px 0px;
        label {
            margin-bottom: 0;
            padding: 0 0 0 10px;
            line-height: 29px;
        }
    }
    textarea {
        height: 69px;
        min-height: 69px !important;
        background-color: #fff;
        border-radius: 0px 0px 6px 6px !important;
    }
    .select2-choice {
        background-color: #fff !important;
    }

    .discount-code-box {
        .control-label {
            display: block;
        }
        
        .btn {
            z-index: 5;
            margin-left: -10px;
            line-height: 45px !important;
            border-radius: 6px !important;
        }
    }
}

.full-page {
    margin: -1px 0;
    padding: ($breadcrumb-height + $layout-offset) $layout-offset/2 $layout-offset $layout-offset/2;

    .form-control  {
        padding: 0 20px;
        font-size: 18px;
        height: 47px;
    }

    .select2-container {
        .select2-choice {
            height: 47px;
            line-height: 47px;
            padding-left: 20px;

            .select2-chosen {
                font-size: 18px;
            }
        }
    }

    .control-label {
        margin-bottom: 10px;
    }

    .input-group-addon-style {
        font-size: 18px;
        font-weight: 300;

        &:before {
            border-top-width: 24px;
            border-bottom-width: 24px;
        }

        &:after {
            border-top-width: 24px;
            border-bottom-width: 24px;
        }
    }
}

.modal-content {
    .main-fields-holder {
        &.main-fields-holder-equal {
            margin: -20px -20px 0;
        }
        input.placeholder {
            color: lighten(#26292f, 30%);
            font-size: 14px !important;
            font-weight: 400 !important;
            line-height: 35px !important;
        }
        input:-moz-placeholder {
            color: lighten(#26292f, 30%);
            font-size: 14px !important;
            font-weight: 400 !important;
            line-height: 35px !important;
        }
        input::-moz-placeholder {
            color: lighten(#26292f, 30%);
            font-size: 14px !important;
            font-weight: 400 !important;
            line-height: 35px !important;
        }
        input:-ms-input-placeholder {
            color: lighten(#26292f, 30%);
            font-size: 14px !important;
            font-weight: 400 !important;
            line-height: 35px !important;
        }
        input::-webkit-input-placeholder {
            color: lighten(#26292f, 30%);
            font-size: 14px !important;
            font-weight: 400 !important;
            line-height: 35px !important;
        }
        .main-box {
            input {
              background-color: #fafafa;
            }
            input.placeholder {
                color: lighten(#26292f, 30%);
                font-size: 14px !important;
                font-weight: 400 !important;
                line-height: 35px !important;
            }
            input:-moz-placeholder {
                color: lighten(#26292f, 30%);
                font-size: 14px !important;
                font-weight: 400 !important;
                line-height: 35px !important;
            }
            input::-moz-placeholder {
                color: lighten(#26292f, 30%);
                font-size: 14px !important;
                font-weight: 400 !important;
                line-height: 35px !important;
            }
            input:-ms-input-placeholder {
                color: lighten(#26292f, 30%);
                font-size: 14px !important;
                font-weight: 400 !important;
                line-height: 35px !important;
            }
            input::-webkit-input-placeholder {
                color: lighten(#26292f, 30%);
                font-size: 14px !important;
                font-weight: 400 !important;
                line-height: 35px !important;
            }
        }
    }
}

.chart-controls {
    .chart-title {
        font-size: 28px;
        font-weight: 300;
        color: #26292f;
        display: inline-block;
        &:first-letter {
            text-transform: capitalize;
        }
    }
    .select2-container {
        max-width: 200px;
        display: inline-block;
        float: left;
        margin-left: 10px;
    }
    @media (max-width: 1115px) {
        .select2-container {
            max-width: 100px;
        }
    }
}

.remove-image-quick {
    position: absolute;
}

.publish-date, .product-embed, .toggle-dropdown {
    top: 65px;
    min-width: 300px;
    padding: 15px;
    position: absolute;
    z-index: 10;
    background-color: #fff;
    border: 1px solid #dedede;
    display: none;
    transition: all, .3s;
    box-shadow: 0px 3px 9px -2px lighten(#000, 60%);
    border-radius: 6px;
    &:before {
        content: "";
        display: block;
        position: absolute;
        top: -15px;
        right: 20px;
        width: 0;
        height: 0;
        border-left: 15px solid transparent;
        border-right: 15px solid transparent;
        border-bottom: 15px solid #dedede;
    }
    &:after {
        content: "";
        display: block;
        position: absolute;
        top: -14px;
        right: 20px;
        width: 0;
        height: 0;
        border-left: 15px solid transparent;
        border-right: 15px solid transparent;
        border-bottom: 15px solid #fff;
    }
    input {
        font-size: 18px;
        font-weight: 500;
    }
    .switch-wrapper {
        margin: 0 0 15px;
    }
    .btn {
        width: 100%;
        margin-left: 0 !important;
    }
    &.active {
        animation: fade-in .3s 1 linear, publish-animation-up .3s 1 ease-in-out;
        opacity: 1;
        z-index: 11;
        display: block;
    }
    &.dismiss {
        display: block;
        opacity: 0;
        animation: fade-out .3s 1 linear, publish-animation-down .3s 1 ease-in-out;
    }
}
.publish-date, .toggle-dropdown {
    right: 30px;
}
.product-embed {
    right: 145px;
}

.radio-holder {
    label {
        margin-right: 10px;
        span {
            vertical-align: middle;
        }
        &:last-of-type {
            margin: 0;
        }
    }
}

.regions-drop-list {
    position: absolute;
    top: 24px;
    right: 105%;
    width: auto;
    min-width: 170px;
    background-color: #fff;
    border: 1px solid #dedede;
    z-index: -1;
    opacity: 0;
    transition: opacity, .3s;
    box-shadow: 0px 3px 10px 1px rgba(0, 0, 0, 0.1);
    .scroll-box {
        overflow: hidden;
        position: relative;
        max-height: 180px;
        li {
            padding: 0 20px 0 10px;
            line-height: 30px;
            font-size: 14px;
            font-weight: 500;
            color: #40444d;
            white-space: nowrap;
            &:nth-child(2n) {
                background-color: #f5f5f5;
            }
            &:before {
                content: "";
                display: inline-block;
                margin-right: 8px;
                width: 12px;
                height: 12px;
                background-color: transparent;
                border: 2px solid #24cf8c;
                vertical-align: middle;
                border-radius: 50%;
            }
        }
    }
    &:before {
        content: "";
        display: block;
        position: absolute;
        top: 6px;
        right: -10px;
        width: 0;
        height: 0;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
        border-left: 10px solid #dedede;
    }
    &:after {
        content: "";
        display: block;
        position: absolute;
        top: 6px;
        right: -9px;
        width: 0;
        height: 0;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
        border-left: 10px solid #fff;
    }
    &.scroll {
        padding: 5px 0;
        .scroll-box li {
            margin-right: 10px;
        }
    }
    &.active {
        display: block;
        z-index: 9;
        opacity: 1;
    }
}

.perfect-scroll {
    position: relative;
    overflow: hidden;
}

.file-input-wrapper {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    z-index: -1;
}

.css-icon {
    position: relative;
    display: inline-block;
    width: 16px;
    height: 16px;
    &::before,
    &::after {
        content: "";
        position: absolute;
    }
    &, &.icon_hamburger::before,
    &.icon_hamburger::after {
        width: 1.625em;
        height: .313em;
        background: black;
    }
    &.icon_hamburger::before {
        top: -.625em;
        left: 0;
    }
    &.icon_hamburger::after {
        bottom: -.625em;
        left: 0;
    }
}

.incomplete-address {
    width: 20px;
    height: 20px;
    background-color: transparent;
    border: 1px solid #f65f5f;
    color: #f65f5f;
    font-size: 12px;
    margin-left: 5px;
    display: inline-block;
    text-align: center;
    padding-top: 2px;
    font-weight: 600;
    border-radius: 50%;
    &:hover {
        color: #f65f5f;
    }
}

.ui-spinner.ui-widget.ui-widget-content.ui-corner-all {
    width: 100%;
    border-color: #dedede;
    border-width: 0;
    input {
        margin: 0;
        padding: 0 20px 0 10px;
        background-color: #fafafa;
        line-height: 30px;
    }
    a span {
        .ui-icon-triangle-1-n {
            top: 15px;
            background-position-x: -3px;
        }
        .ui-icon-triangle-1-s {
            margin-top: -10px;
            background-position-x: -68px;
        }
    }
}

.imports-holder {
    p {
        font-size: 16px;
        font-weight: 300;
    }
    .btn {
        margin-left: 20px;
    }
}

.import-values {
    font-size: 16px;
    display: inline-block;
    margin-right: 10px;
    line-height: 36px;
    font-weight: 300;
    color: #848484;
    &:last-of-type {
        margin-right: 0;
    }
}

.padding-top {
    padding-top: 60px;
}

#importBindForm {
    .imports-field {
        .form-group {
            padding-top: 15px !important;
            padding-bottom: 10px !important;
            .main-box .control-label {
                margin: 0;
            }
        }
    }
}

.seo-settings textarea {
    max-height: 100px;
    min-height: 100px !important;
    padding: 10px;
    max-width: 100%;
}

#boardingPageContainer {
    .main-fields-holder {
        padding: 25px 10px;
    }
}
.chart-no-data {
    background-color: rgb(248, 248, 248);
    width: 230px;
    height: 230px;
    border-radius: 50%;
    vertical-align: middle;
    font-size: 20px;
    font-weight: 300;
    margin: 15px auto 0 auto;
    color: rgb(199, 199, 199);
    padding-top: 75px;
    display: block;
    position: relative;
    border: 25px solid #ccc;
    box-sizing: border-box;
    &:before {
        content: '';
        width: 1px;
        height: 25px;
        background-color: rgb(248, 248, 248);
        position: absolute;
        top: -25px;
        left: 0;
        right: 0;
        margin: auto;
    }
}

input.placeholder {
    font-size: 14px !important;
    font-weight: 400 !important;
}
input:-moz-placeholder {
    font-size: 14px !important;
    font-weight: 400 !important;
}
input::-moz-placeholder {
    font-size: 14px !important;
    font-weight: 400 !important;
}
input:-ms-input-placeholder {
    font-size: 14px !important;
    font-weight: 400 !important;
}
input::-webkit-input-placeholder {
    font-size: 14px !important;
    font-weight: 400 !important;
}

.positionformessage {
    top: 121px;
    right: 0 !important;
}

.form-control {
    &.monospace {
        font-family: 'Consolas', monospace;
        font-size: 12px;
    }
}

.main-wrapper {
    padding-top: 100px;
}

.container-offset {
    padding: 0 25px;
}

#submit_ssl_form {
    textarea {
        height: 213px;
    }

    #csr {
        height: 156px;
        margin-bottom: 20px;
    }

    .form-group {
        label {
            display: inline-block;
            margin-bottom: 4px;
            font-size: 14px;
            font-weight: normal;
            color: #7a7d84;
        }
    }
}

.statuses-wrapper {
    td:last-child {
        width: 300px;
        white-space: nowrap;
    }

    .content-padding {
        padding-top: 30px;
    }

    .form-control {
        display: inline-block;
        margin-right: 10px;
        width: 300px;
    }
}

.import-notify {
    margin: 90px 0 -25px;
}

.import-form {
    max-width: 800px;
    margin: 0 auto;

    .form {
        display: table;
        width: 100%;
    }

    p {
        margin-bottom: 0;
    }
}

.import-status,
.import-status-completed,
.import-status-button {
    display: table-cell;
    width: 33.33%;
    vertical-align: middle;
}

.import-status {
    font-size: 16px;
    color: #24cf8c;
}

.import-status-completed {
    text-align: center;
}

.import-status-button {
    text-align: right;
}

.import-status-close {
    display: none;
}

.import-status-progress {
    font-size: 16px;
}

.errors-head {
    margin-bottom: 20px;

    h3 {
        margin: 12px 0 0;
    }
}

.ebay-configuration {
    label {
        margin-bottom: 5px;
    }
}

#ebayShippingDetails,
#mappedCategories {
    font-size: 15px;
    margin-bottom: 0;

    li {
        position: relative;
        padding-right: 30px;
        margin-bottom: 5px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .close {
        position: absolute;
        top: 0;
        right: 0;
    }
}

.ebay-shipping-full-details {
    padding-left: 20px;

    li {
        margin-bottom: 15px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.ebay-shipping-full-details-name {
    color: #adb1b9;
    font-size: 16px;
    font-weight: bold;
}

.selection-criteria {
    margin-left: -7px;
    margin-right: -7px;
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }

    [class^="col-"] {
        padding-left: 7px;
        padding-right: 7px;
    }

    .close {
        margin-top: 8px;
    }

    @media (max-width: 767px) {
        [class^="col-"] { margin-bottom: 10px; }
    }
}

.selection-criteria-select,
.selection-criteria-input {
    width: 150px;
    max-width: 100%;
}

.selection-criteria-yesno {
    width: 100px;
    max-width: 100%;
}

.selection-slider-input {
    float: left;
    width: 100px;
    max-width: 38%;
}

.selection-slider-word {
    float: left;
    max-width: 20%;
    margin: 0 2%;
    line-height: 37px;
}

.fileupload-hidden {
    overflow: hidden;
}

.tinymce {
    opacity: 0;
}

.mce-fullscreen {
    z-index: 100003 !important;
}

.mce-tinymce {
    iframe {
        width: -webkit-calc(100% - 1px) !important;
        width:         calc(100% - 1px) !important;
    }

    .mce-btn-group {
        .mce-txt {
            white-space: nowrap;
        }
    }
}

.mce-menubtn {
    &.mce-fixed-width {
        &[aria-label="Font Sizes"] {
            .mce-txt {
                width: 40px;
            }
        }
    }
}

.modal-open {
    .mce-tooltip {
        position: fixed !important;
    }

    .mce-floatpanel {
        position: fixed !important;
    }
    .mce-floatpanel.mce-popover {
        position: absolute !important;
    }
}

.has-error {
    .mce-tinymce {
        border-color: #f06969;
    }
}

.leasing-table-holder {
    border: 1px solid #ddd;
}

.leasing-table {
    width: auto;
    overflow: auto;
    margin: 0 -1px -1px;

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        border: 1px solid #ddd;
    }
}

.tutorialize {
    &-slide {
        box-shadow: 0 0 50px rgba(0, 0, 0, .4);
        border: 1px solid #c7c7c7;
        z-index: 10000;

        &-overlay {
            z-index: 9999;
        }

        &-close {
            color: #fff;
            font-size: 0;
            line-height: 1;

            &:hover {
                background-color: #8d58e0 !important;
                opacity: .7;
            }

            &:before {
                content:"\f00d";
                font-family: 'FontAwesome', sans-serif;
                font-size: 12px;
                line-height: 20px;
            }
        }

        &-title {
            background-color: #8d58e0;
            border-radius: 5px 5px 0 0;
            font-size: 16px !important;
            color: #fff;
            padding: 10px 20px;
        }

        &-content {
            padding: 10px 20px;
            line-height: 1.5;
        }

        &-control {
            padding: 10px 20px 20px;

            .prev,
            .next {
                border-radius: 5px;

                &:hover {
                    background-color: #8d58e0 !important;
                    opacity: .7;
                }
            }
        }
    }
}

.comments-table {
    tbody {
        tr {
            td {
                a {
                    &.delete {
                        float: right;
                        position: static;
                    }
                }
            }
        }
    }
}

/* #New
-------------------------------------------------------------------------------*/

.help-block {
    color: #b0b0b0;
    font-weight: 300;
    font-size: 13px;
    margin-top: 0;

    &:last-child {
        margin-bottom: 0;
    }
}

.help-block-error {
    display: block;
    margin-top: 5px;
    color: #f06969;
}

.has-error {
    .help-block {
        color: #b0b0b0;
    }

    .control-label,
    .main-title {
        color: #7a7d84;
    }

    .form-control {
        border-color: #f06969 !important;

        &:focus {
            border-color: #f06969;
        }
    }

    .select2-container {
        .select2-choice {
            border-color: #f06969 !important;
        }
    }

    .select2-container-multi {
        .select2-choices {
            border-color: #f06969 !important;
        }
    }

    .input-group-addon,
    .input-group-addon.btn {
        border-color: #f06969;
    }

    .input-group-addon.btn-white {
        box-shadow: 0 0 0 1px #f06969;
    }
}

.seo-google {
    &-section {
        &.seo-visible {
            .seo-google-box {
                display: block;
            }

            .seo-google-notice {
                display: none;
            }
        }
    }

    &-box {
        display: none;
        max-width: 100%;
        width: 512px;
    }
    
    &-title {
        font-family: Arial, sans-serif;
        font-size: 18px;
        line-height: 1.2;
        color: #1a0dab;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-bottom: 3px;
    }

    &-url {
        font-family: Arial, sans-serif;
        font-size: 14px;
        line-height: 1.15;
        color: #006621;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-bottom: 4px;
    }
    
    &-description {
        font-family: Arial, sans-serif;
        font-size: 13px;
        line-height: 1.4;
        color: #545454;
        margin-bottom: 0;
    }

    &-notice {
        margin-bottom: 0;
    }
}

.as-block {
    display: block !important;
    width: 100% !important;
}

.order-payment-details {
    .right.total {
        font-size: 16px;
        color: #2d2f34;
        font-weight: 600;
        white-space: nowrap;

    }
}

.iti__flag-container .iti__selected-flag {
    height: 37px;
}

.text-block {
    color: #26292f;
    font-size: 16px;
}