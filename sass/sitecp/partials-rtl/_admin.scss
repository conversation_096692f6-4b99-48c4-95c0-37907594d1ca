
/* ==============================================================================
    #Helper Start
=============================================================================== */
          
.form-control.border-none {
    border-right: none;
    font-weight: 500;
}

.form-control:focus {
    border-color: darken(#dedede, 5%);
    box-shadow: none;
    transition: border-color 0.3s linear;
    -moz-transition: border-color 0.3s linear;
    -o-transition: border-color 0.1s linear;
    -webkit-transition: border-color 0.3s linear;
    -ms-transition: border-color 0.3s linear;
}

#customer_groups_holder .checkbox-list .checkbox-inline {
    margin-top: 15px;
}

.checkbox-list .checkbox-inline {
    font-size: 15px;
    font-weight: 400;
    display: inline-block;    
}

.checkbox-list {
    .form-control-check {
        display: table;
        margin: 0 0 10px 10px;
    }
    
    .checkbox-inline {
        display: inline-block;

        &:first-child {
            padding-right: 0;
            margin-right: 0;
        }
    }

    .checkbox-list {
        margin-right: 20px;
    }
}

.checkbox-inline {
    padding-right: 0;
    margin-left: 20px;

    &:last-child {
        margin-left: 0;
    }

    + .checkbox-inline {
        margin-right: 0;
    }
}

.regions-list {
    background-color: #f7f7f7;
    border-color: #e8e8e8;
    border-radius: 6px;

    &.has-error {
        border-color: #b94a48;
    }
}

.target_item .help-block-error {
    color: #F59A93; 
}

.input-addon-right { position: relative; left: 10px !important;
    &.form-control { border-right: 1px solid #dedede !important; border-left: 0; }
}

div.checker {
    margin: 0 0 0 5px;
}

.form .form-group label.control-label.sm {
    font-size: 15px;    
}

.form form h3 {
    font-size: 22px;
}

.form form .form-group textarea {
    min-height: 250px;
}

/**     
* GRID AND LIST VIEW
*/
a.item-store-view, a.view-store-button {
    color: #76BBE0 !important;
    position: relative;
    margin-left: 40px;    
    margin-right: 40px;
    font-size: 13px;
    font-weight: 400;    
}
a.view-store-button {
    margin-right: 0;
    background: image-url("img/product-viewin-sprite.png")no-repeat center bottom;
    width: 21px;
    height: 21px;
    display: inline-block;
    background-repeat: no-repeat;
    margin: 0px 10px -6px 10px;
    &:hover {
        opacity: .7;
    }  
}
tbody tr td.name-width { 
    width: 540px;
}

tbody tr a.editable, tbody tr a.addable, tbody tr span.editable, tbody tr span.addable {
    margin-left: 20px;
    max-width: 460px;
    display: inline-block;
    overflow: hidden;
}

tbody tr .editable > a, tbody tr .addable > a {
    display: block;
}

tbody tr:hover a.editable, tbody tr:hover a.addable, tbody tr:hover span.editable, tbody tr:hover span.addable {
    color: #2d2f34;
}

.page-type-icon {
    margin-left: 9px;
}

.grid-vendors tbody tr .editable,
.grid-controls tbody tr .editable,
.grid-products tbody tr .editable,
.grid-products-order-add tbody tr .addable {
    display: table;
    border-collapse: collapse;
    max-width: 100%;
    & > a, & > .name-holder {
        display: table-cell;
        vertical-align: middle;
    }
    & > .name-holder {
        > * {
            display: block;
            width: 100%;
        }
    }

    .list-info-holder {
        width: 50px;
        min-width: 50px;
    }

    .product-image {
        width: 50px;
        min-width: 50px;
    }

    .name-holder {
        padding-right: 10px;
    }
}
.grid-products tbody tr .editable {
    width: 100%;
    table-layout: fixed;
}

.product-list-actions {
    white-space: nowrap;

    > * {
        white-space: normal;
    }

    a {
        display: inline-block;
        margin: 0 3px;
    }
}

tbody tr td.customer-value {
    color: #6f6f6f;
    font-weight: 500;
}

tbody tr td.email-style {
    color: #5cb3ec;
    font-weight: 400;
    font-size: 14px;  
}

tbody tr td.address-style {
    font-weight: 500;
}

.table tbody tr td a .icon-note {
    background: image-url("img/icon-note.png")no-repeat;
    display: inline-block;
    width: 19px;
    height: 19px;
    vertical-align: middle;
    margin: 0 0 0 10px;
}

.table thead>tr>th, .table tbody>tr>th, .table tfoot>tr>th {
    vertical-align: middle;
    font-weight: normal;
}

.table thead>tr>td, .table tbody>tr>td, .table tfoot>tr>td {
    vertical-align: middle;
}

.table-aligntop thead>tr>th, .table-aligntop tbody>tr>th, .table-aligntop tfoot>tr>th, .table-aligntop thead>tr>td, .table-aligntop tbody>tr>td, .table-aligntop tfoot>tr>td {
    vertical-align: top;

    .list-thumb {
        padding-top: 3px;
        vertical-align: top;
        height: auto;
    }

    .list-info {
        vertical-align: top;
    }
}

.table caption+thead tr:first-child th, .table colgroup+thead tr:first-child th, .table thead:first-child tr:first-child th, .table caption+thead tr:first-child td, .table colgroup+thead tr:first-child td, .table thead:first-child tr:first-child td {
    border-bottom: 0;
} 

.grid-wrapper {
    padding-top: 60px;
}
.table tbody>.ui-sortable-placeholder {
    visibility: visible !important;
}

.table tbody>.ui-sortable-helper>td {
    border-bottom: 1px solid #ddd;

    &:first-child {
        border-right: 1px solid #ddd;
    }

    &:last-child {
        border-left: 1px solid #ddd;
    }
}

.grid-wrapper .listing, .grid-wrapper .grid .prototype {
    display: none;
}

.grid-wrapper .actions {
    margin: 0 0 10px 0;
    height: 35px;    
}

.grid-wrapper .actions .bulk {
    float: left;
    margin-right: 10px;
}

.grid-wrapper .actions .bulk ul > li > a {
    
}

.grid-wrapper .per-page {
    margin-left: 7px;
}

.grid-wrapper .pagination {
    float: right;
    margin: 0 !important;
}

.grid-wrapper thead th.check-all {
    width: 52px !important;
}

.grid-wrapper .grid-controls {
    &:after {
        content: '';
        display: table;
        clear: both;
    }
    &:last-of-type {
        margin-bottom: 40px;
    }
}

tbody tr td .dicount-period p {
    font-size: 11px;
}

tbody tr td .dicount-period p:last-child {
    margin-bottom: 0;
}

.table-striped>tbody>tr>td, .table-striped>tbody>tr>th {
    background-color: #fff;
}

.table-striped>tbody>tr:nth-child(odd)>td, .table-striped>tbody>tr:nth-child(odd)>th {
    background-color: #fff;
}
table.table tbody tr td.option a i {
    font-size: 12px;
    color: #9c9c9c;    
}


table tbody tr td .note-info p {
    font-size: 12px;
    font-weight: 500;
}

table tbody td span.product-vendor {
    font-size: 14px;
    color: #2d2f34;
    display: block;
    font-weight: 400;
    
}

table tbody td span.product-vendor a {
    font-size: 14px;
    color: #5cb3ec !important;
    font-weight: 400;
}

table tbody td span.product-vendor a:hover {
    color: #468FB6; 
}
table tbody td .view-in-box  {
    height: 70px;
    line-height: 67px;
}
table tbody td .view-in-box a {       
    margin-right: 0;
    background: image-url("img/product-viewin-sprite.png")no-repeat center bottom;
    width: 21px;
    height: 21px;
    display: inline-block;
    background-repeat: no-repeat;
    margin: 0px 10px -6px 10px;
    &:hover {
        opacity: .7;
    }  
}
table tbody td .view-in-box a:last-of-type {
    margin-right: 0;
}
table tbody td .view-in-box a:hover {
    color: #63acd3;
}
table tbody td .view-in-box a.image-view {
    background-position: center top;
}

table tbody tr td span.date {
    font-size: 14px;
    color: #2d2f34;
    font-weight: 400;
}

table tbody tr td span.time {
    font-size: 13px;
    color: #7d7e80;
    font-weight: 400;
}

table.listing tbody tr td a.customer {
    color: #5cb3ec;
    font-size: 14px;
    font-weight: 400;
}

table.listing tbody tr td a.customer:hover {
    color: lighten(#5cb3ec, 10%);
}

table.listing tbody tr td a.customer_orders {
    font-size: 12px;
    color: #7d7e80;
    margin-left: 0;
}

table.listing tbody tr td a.customer_orders:hover {
    color: lighten(#7d7e80, 10%);
}

table.listing tbody tr td .money {
    
    color: #2d2f34;
    font-size: 16px;
    font-weight: 400;
}

table.listing tbody tr td.money {
    
    color: #808ca3;
    font-size: 16px !important;
    font-weight: 500;
}

table.listing tbody tr td.name {
    color: #494949;
    font-size: 15px !important;
    font-weight: 500;
}


table.listing tbody tr:hover a.customer:after {
    color: #76BBE0;
}


table.listing tbody tr:hover a.customer_orders:after {
    color: #a7a7a7;
}

table.table-sorting {
    tbody {
        tr {
            cursor: move;
        }
    }
}

table tbody tr td.grid-empty-set {
    border: none;
}

table tbody tr td.grid-empty-set:hover {
    background-color: transparent;
}

.no-results {
    font-size: 20px;
    color: #c9c9c9;
    font-weight: 300;
    text-align: center;
    font-weight: 300;
}

.no-results i {
    font-size: 120px;
    color: #e5e5e5;
}

table tbody tr td .parameter_products {
    color: #cacaca;
    font-size: 12px;
    font-weight: 500;   
}

table tbody tr td .discount-code {
    white-space: nowrap;
}

.order-date {
    font-size: 11px;
    color: #b6b6b6 !important;
    font-weight: 500 !important;
    display: inherit;
}

.register-date {
    font-size: 11px;
    color: #b6b6b6 !important;
    font-weight: 500 !important;
    padding-left: 0 !important;
    display: inherit;
}


#order-table tbody tr td.desc {
    font-size: 10px;
    color: #b9b9b9;
    font-weight: 500;
}

 tr td a.product-name {
    font-size: 17px;
    font-weight: 500;
    margin-right: 15px;
}

.archived #order-table tbody tr td .btn {
    background-color: #c2c2c2;
    border-color: #c2c2c2;
    color: #fff;
    cursor: none;
}

.archived #order-table tbody tr td .btn span {
    display: none;    
}
.archived #order-table tbody tr td .btn:hover {
    background-color: #c2c2c2;
    border-color: #c2c2c2;
    color: #fff;
    pointer-events: none;
}

#order-table tbody td {
    width: 100px;
}
.archived #order-table tbody tr td .btn.btn-success {
    display: none;    
}

.modal-dialog .modal-content .modal-body #productFulfillForm table tr td {
    border: 1px solid #ddd;
}

.modal-dialog .modal-content .modal-body #productFulfillForm table thead tr:first-child {
    background-color: #f9f9f9;
}

#order-table tbody td.shipping {
    width: auto;
}

#order-table tbody tr button.details {
    opacity: 0;
    outline: none;
    margin-left: 15px;
}

#order-table tbody tr td .btn.btn-order-edit {
    position: absolute;
    top: 20px;
    right: 0px;
}

#order-table tbody tr td .pull-right>.dropdown-menu {
    top: 43px;
}

#order-table tbody tr:hover button.details {
    opacity: 1;
} 


#order-table tbody tr td .btn-group .badge.badge-details {
    margin-right: 25px;
    outline: none;
}

#order-table tbody tr td.text-center a {
    margin-top: 10px;
}

#order-table tbody tr:hover td {
    background-color: #fff;
}
#order-table tbody tr.sub-details:hover td {
    background-color: #f8f8f8;
}

#order-table tbody tr {
    border-color: #f2f2f2;
}

#order-table thead tr th {
    color: #adadad;
    text-transform: none;
}

#order-table tbody td.shipping {
    border-right: 1px dotted #f2f2f2;
    border-left: 1px dotted #f2f2f2;
}

table tbody tr td.total {
    font-size: 17px !important;
    color: #7088A8;
    font-weight: 500; 
}


#order-table tbody td.paid {
    font-size: 17px !important;
    color: #a2cc66;
    font-weight: 500;
    background-color: #f7f7f7;
    border-bottom: 1px solid #d9e0e9;
    border-top: 1px solid #d9e0e9;
}

#order-table tbody tr:hover td.paid  {
    background-color: #f7f7f7;    
}

#order-table tbody tr td.gray {
    color: #797c81;
    font-weight: 500;
    font-size: 14px !important;
}
#order-table tbody tr td.font-amount {
    font-size: 14px !important;
        
}

#order-table tbody tr td.shipping {
    text-align: center;
}

#order-table tbody tr.sub-details {
    background-color: #f8f8f8;
}

#order-table tbody tr.sub-details td {
    color: #b6b6b6;
    
    font-size: 10px;
}

#order-table tbody tr.sub-details:first-child .row:first-child {
    margin-bottom: 40px;
}

#order-table tbody tr.sub-details .col-xs-3 {
    border-right: 1px dotted #ccc;
}

#order-table tbody tr.sub-details .option-1, #order-table tbody tr.sub-details .option-2, #order-table tbody tr.sub-details .option-3 {
    color: #838383 !important;
    margin-right: 10px;
}

table tbody tr.parameter-title td {
    color: #838383 !important;
    font-size: 11px !important;   
}

.modal-body table tbody tr.parameter-title td {
    color: #838383 !important;
    
    font-size: 11px !important;   
}

.modal-body table tbody tr.parameter-title td a {
    
}

table tbody tr.parameter-title td span.option-1,
table tbody tr.parameter-title td span.option-2,
table tbody tr.parameter-title td span.option-3 {
     margin-left: 5px;    
}

#order-table tbody tr.sub-details  {
    .option-1-value {
        color: #a2cc66;
        margin-left: 3px;
    }
    .option-2-value {
        color: #e3c071;
        margin-left: 3px;
    }
    .option-3-value {
        color: #84c2e3;
        margin-left: 3px;
    }
    td span.value {
        color: #000;
        font-size: 13px;
        font-weight: 500;
        display: block;
        margin-top: 5px;
        .glyphicon {
            color: #cacaca;
            font-size: 14px;
            margin-left: 5px;
            cursor: pointer;
        }  
    }    
}


#order-table tbody tr.sub-details td span.option-1, #order-table tbody tr.sub-details td span.option-2, #order-table tbody tr.sub-details td span.option-3 {
    margin-top: 5px;
    display: inline-block;  
}


#order-table tbody tr td a.order-discount:hover {
    background-color: #f7674b;   
}

table thead tr.gray-head {
    background-color: #f7f7f7;
} 

table.table-hover tbody td img {
    width: 50px;
    margin-left: 10px;
}

table.table-hover tbody .provider-number img {
    display: block;
    max-width: 100px;
    width: auto;
    margin: 0 auto 5px;
}

#category_properties_wrapper,
#category_properties_options_wrapper {
    a {
        color: #5cb3eb;
    }
}

#category_properties_options_wrapper {
    .filter_warehouse {
        display: none;
    }
}

.provider-number {
    word-break: break-word;
    font-size: 12px !important;
    color: #8F8F8F !important;
}

/** GRID VIEW **/

.check-all {
    padding: 0 15px 20px 15px;
}

.grid-wrapper {
    .grid-item {
        border: 1px solid #ebebeb;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        .item-active:hover, .item-inactive:hover {
            border-color: #bbb;    
        }
        &:hover .item-active, &:hover .item-inactive {
            border-color: #ebebeb;    
        }
        .checker {
            display: block;
            top: 7px;
            left: 5px;
            position: absolute;
            z-index: 4;   
        }
        .customer-image {
            display: inline-block;
            width: 110px;
            height: 110px;
            margin: 25px 0;
            .image {
                width: 100%;
                height: 100%;
                display: block;
                background-size: cover;
                background-position: center;
                border-radius: 50%;
                -moz-border-radius: 50%;
                -webkit-border-radius: 50%;
            }
        }
        .costumer-name a {
            font-size: 15px;
            color: #000;
            font-weight: 700;
        }
        .costumer-email {
            font-size: 11px;
            color: #4d7fc2;
            margin-bottom: 10px;
        }
        .grid-image {
            overflow: hidden;
            width: 100%;
            height: 250px;
            display: block;
            text-align: center;
            position: relative;
            .image {
                width: 100%;
                height: 100%;
                display: block;
                position: absolute;
                background-size: cover;
                background-position: center;
                z-index: 1;
            }
            &:before {
                opacity: 0.5;
                content: "";
                position: absolute;
                width: 100%;
                height: 25%;
                bottom: 0;
                left: 0;
                z-index: 2;
                background: -moz-linear-gradient(top,  rgba(0,0,0,0) 0%, rgba(63,63,63,0.65) 100%); /* FF3.6+ */
                background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(0,0,0,0)), color-stop(100%,rgba(63,63,63,0.65))); /* Chrome,Safari4+ */
                background: -webkit-linear-gradient(top,  rgba(0,0,0,0) 0%,rgba(63,63,63,0.65) 100%); /* Chrome10+,Safari5.1+ */
                background: -o-linear-gradient(top,  rgba(0,0,0,0) 0%,rgba(63,63,63,0.65) 100%); /* Opera 11.10+ */
                background: -ms-linear-gradient(top,  rgba(0,0,0,0) 0%,rgba(63,63,63,0.65) 100%); /* IE10+ */
                background: linear-gradient(to bottom,  rgba(0,0,0,0) 0%,rgba(63,63,63,0.65) 100%); /* W3C */
                filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00000000', endColorstr='#a63f3f3f',GradientType=0 ); /* IE6-9 */    
            }
            &:hover:before {
                opacity: 0.3;    
            }
        }
        .grid-image-name {
            bottom: 20px;
            padding: 15px;
            display: block;
            position: absolute;
            z-index: 3;
            font-size: 15px;
            a {
                color: #fff;
                text-shadow: 0px 1px 1px #7e7e7e;
            }
        }
        .grid-image-date {
            padding: 15px;
            bottom: 0px;
            font-size: 11px;
            display: block;
            position: absolute;
            z-index: 3;
            color: #fff;
        }
        
    }
    &.grid-products {
        .grid-item {
            border: 0;
            border-bottom: 1px solid #ebebeb;   
        }
    }
    tbody .image, #order-table .image {
        width: 50px;
        height: 50px;
        display: inline-block;
        background-size: cover;
        background-position: center;
        vertical-align: middle;
        margin: 0 0 0 10px;
        position: relative;
        overflow: hidden;
        border-radius: 50%;
        .overlay {
          width: 100%;
          height: 100%;
          background-color: rgba(45, 47, 52, 0);
          position: absolute;
          border-radius: 50%;
            transition: background .3s ease;
          z-index: 0;
          i {
            display: block;
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            margin: auto;
            transform: translateY(-20%);
              opacity: 0;
              transition: transform .3s ease, opacity .2s ease;
          }
        }
      &:hover {
        .overlay {
          z-index: 2;
            background-color: rgba(45, 47, 52, 0.9);
            > i {
                transform: translateY(-50%);
                opacity: 1;
            }
        }
      }
    }
}

.grid-text {
    
    font-size: 11px;
    margin: 0 0 2px 0;
}

.dd-content span.badge {
    margin-top: 6px;
}

#RegionsTree, #CategoriesTree {
    padding-top: 10px;
    .jqtree-dragging {
        background-color: #f9f9f9;
        border: 1px solid #dbdddf;
        color: #2d2f34; 
        font-weight: 400;
        border-radius: 6px;
        display: table;
        > a {
            display: table-cell;
        }
    }
    span.jqtree-border {
        border-radius: 6px 0px 0px 6px;
    }
    ul li {
        > div {
            background-color: #f9f9f9;
            border-width: 1px;
            border-style: solid;
            border-color: #dbdddf !important;
            position: relative;
            line-height: 43px;
            height: 45px;
            cursor: move !important;
            border-radius: 0 6px 6px 0;
            &:after {
                content: ""; 
                position: absolute;
                top: 16px;
                left: 50px;
                @include all-icons-sprite(drag-grip);
            }
            .children_count.item-counter {
                display: inline-block;
                background-color: #4d525c;
                color: #fff !important;
                position: relative;
                padding: 0px 3px 1px 10px;
                font-size: 14px;
                font-style: italic;
                margin-left: 10px;
                &:after {
                    content:"";
                    display: inline-block;
                    position: absolute;
                    left: -10px;
                    top: 0;       
                    bottom: 0;
                    width: 0; 
                    height: 0; 
                    border-top: 22px solid transparent;
                    border-bottom: 22px solid transparent;
                    border-right: 10px solid #4d525c; 
                }
                
            }
            > a {
                background-color: #4d525c;
                color: #fff;
                height: 44px;
                font-weight: 300;
                width: 40px;
                font-size: 11px;
                padding-right: 21px;
                display: inline-block;
                position: relative;
                border-radius: 0 6px 6px 0;
                margin: 0;
                &:before {
                    content: "";
                    width: 20px;
                    height: 20px;
                    display: block;
                    position: absolute;
                    background-color: transparent;
                    border: 1px solid #fff;
                    top: 0;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    margin: auto;
                    margin-left: 8px;
                    border-radius: 50%;
                }      
            }
            > a.jqtree-closed {
                padding-right: 19px;
            }
            span.jqtree-title {
                display: inline-block;
                line-height: 43px;
                padding: 0;
                a {
                    color: #2d2f34;
                    font-weight: 400;
                    font-size: 14px;
                    margin-left: 25px;
                    display: inline-block;
                    vertical-align: middle;
                    height: 100%;
                    &.editable, &.non-editable {
                        padding-right: 10px;   
                    }
                    &.non-editable {
                        cursor: move; 
                    }
                }
                .children_count {
                    color: #84c2e3;
                }
            }
        }
        .controls {
            position: relative;
            height: 50px;
            .delete {
                @include all-icons-sprite(delete);
                display: inline-block; 
                position: absolute;
                top: -7px; 
                bottom: 0;
                left: 10px;
                margin: auto;
                &:hover {
                    opacity: .7;
                }
            }  
            .span {
                font-size: 12px;   
            }
            .add-subregions {
                margin-right: 50px;
                
                font-size: 13px;
                cursor: pointer;
                color: #a2cc66;
                position: relative;
                &:hover {
                    color: darken(#a2cc66, 10%);
                }
                &:hover:before {
                    color: darken(#a2cc66, 10%);    
                    border-color: darken(#a2cc66, 10%);    
                }
                &:before {
                    content: "+";
                    border: 1px solid #a2cc66;
                    width: 21px;
                    height: 21px;
                    line-height: 19px;
                    font-size: 13px;
                    text-align: center;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: -30px;
                    margin: auto;
                    border-radius: 50%;
                }
            }
            a.item-active, a.item-inactive {
                padding: 7px 10px;
                margin: 0 10px;    
            }
            .region-tax, .region-shipping {
                
                color: #a5a5a5;
                display: inline-block;
                text-align: center;
                line-height: 18px;
                margin: 0 50px 0 0;
                vertical-align: middle;
                cursor: pointer;    
            }
            .region-tax {
                i {
                    font-style: normal;
                    font-weight: 700;
                    font-size: 12px;
                    display: block;    
                }
                &.tax-specific i {
                    color: #c3dc9b;   
                }
                &.tax-inherit i {
                    color: #E6D380;    
                }
                &.tax-none i {
                    color: #f5877f;    
                }    
            } 
            .region-shipping {
                i {
                    font-size: 14px;
                    line-height: 18px;
                    color: #c3dc9b;
                    display: block;    
                }
                &.shipping-specific i {
                    color: #c3dc9b;
                }
                &.shipping-inherit i {
                    color: #E6D380;    
                }
                &.shipping-none i {
                    color: #f5877f;    
                }
            } 
        }  
    }   
}

.table tbody tr.active, .table tbody tr.active td {
    background-color: #def0fb;
    color: #000;
}

.table tbody tr.active:hover td {
    background-color: #def0fb !important;
    &:hover {
        background-color: #def0fb !important;
    }
}

.table-nowrap {
    white-space: nowrap;
    width: 1px;
}

.discount-name {
    color: #2d2f34;
    font-weight: 400;
    font-size: 14px;
    vertical-align: baseline;
}
.discount-desc {
    color: #5cb3ec;
    font-weight: 300;
    font-size: 14px;
    margin-top: -7px;
}

.discount-name,
.discount-desc {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
}

@media (max-width: 1400px) {
    .discount-inner {
        max-width: 200px;
    }
}
@media (max-width: 1110px) {
  .discount-inner {
    max-width: 130px;
  }
}

.dicount-period p {
    .date-label {
        color: #959697;
        margin-left: 30px;
        font-size: 14px;
        
        font-weight: 400;
    }
    span:last-of-type {
        color: #2d2f34;
        font-weight: 400;
        font-size: 14px;
    }
}                      
 
/**     
* END GRID AND LIST VIEW
*/



/**
* Modals
*/

@media (max-width: 1200px) {
    .modal-dialog {
        min-width: 600px;
    }

    .modal-full,
    .modal-wide,
    .modal-large {
        min-width: 970px;
    }
}

.modal-xsmall {
    width: 600px !important;
    min-width: 600px;
}

.modal-small {
    width: 800px !important;
    min-width: 800px;
}

.modal-medium {
    width: 940px !important;
    min-width: 940px;
}

.modal-wide {
    width: 75% !important;
}

.modal-large {
    width: 85% !important;
}

.modal-full {
    width: 100% !important;
}

@media screen and (min-width: 768px) {
    .modal-dialog {
        padding-top: 70px !important;
    }
}

.modal-content .modal-footer .btn-edit {
    padding: 0 12px 0 22px;
}

/**
* END Modals
*/

.nav.nav-pills {
    .steps li {
        border: none;
        a {
            padding: 10px 15px;
            &.step {
                border-radius: 50%;
                width: 48px;
                height: 48px;
                text-align: center;
                vertical-align: middle;
                background-color: #fff;
                border: 2px solid #a2cc66;
                font-size: 17px;
                color: #606060;
                display: block;
                margin: 0 auto 5px auto;
                &:hover {
                    background-color: #f9f9f9;
                    border-color: #afdc6f;
                }
            }
        }
        &.active {
            a.step {
                background-color: #ebebeb;
                border-color: #ebebeb;
                &:hover {
                    background-color: #e5e5e5;
                    border-color: #e5e5e5;
                    color: #606060;    
                }    
            }
        }
    }
    li {
        &.border-left {
            border-left: 1px solid #ebebeb;
        }
        &.padding {
            padding: 0 30px;    
        }
        &.last-child {
            padding-right: 0;
        }
        .number {
           text-align: center;   
        }
        .desc {
            
            margin-top: 15px !important;
            color: #000;
            font-weight: 700;
            font-size: 11px;
        }
        &.active .desc {
            color: #8c8c8c;    
        }   
    }
}

.empty-image-list-holder {
    font-size: 20px;
    color: #c9c9c9;
    font-weight: 300;
    
    i {
        font-size: 120px;
    }
}

.full-iframe {
    height: 100%;
    padding-top: 60px;
    overflow: hidden;

    .page-content {
        height: 100%;
    }

    iframe {
        width: 100%;
        height: 100%;
    }
}

.bulk {
     .tools-btn {
        line-height: 26px !important;
        background-color: lighten(#5cb3ec, 17%);
        border-color: lighten(#5cb3ec, 17%);
        color: #fff;
        &:first-letter {
              
        }
        i {
            display: inline-block;
            vertical-align: sub;
            margin-left: 5px;
        }
    }
    &.active {
        .tools-btn {
            background-color: #5cb3ec !important;
            border-color: #5cb3ec !important;
            color: #fff;
            box-shadow: inset 0 0 0 0 !important;
        }
    }
}
.grid-controls {
    padding: 0 30px 10px;

    .grid-total {
        position: relative;
        top: 2px;
    }
}
.page-breadcrumb .breadcrumb a.item-active, .page-content .page-breadcrumb .breadcrumb a.item-inactive {
    padding: 7px 10px;
}
.row.form .pull-right {
    height: 63px;
    line-height: 60px;
    margin: 0 10px 0 0;
}
.example-box {
    border: 1px dashed #c9c9c9;
    padding: 40px 50px;
    box-shadow: 0px 0px 7px rgba(0,0,0,0.5);
    outline: 2px solid #fff;
    border-radius: 4px;   
}

.modal-dialog {
    .grid-controls {
        padding: 10px 0;
    }
    .my_filter {
      margin: -20px -20px 25px -20px;
    }
}