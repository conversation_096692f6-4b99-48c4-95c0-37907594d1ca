.bee-sticky-atc {
    padding: 10px;
    background-color: var(--bee-light-color);
    z-index: 410;
    box-shadow: 0 0 9px rgb(0 0 0 / 12%);
    transition: -webkit-transform .25s ease;
    transition: transform .25s ease;
    transition: transform .25s ease,-webkit-transform .25s ease;
    -webkit-transform: translate3d(0,105%,0);
    transform: translate3d(0,105%,0);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
    pointer-events: auto;
}
.bee-sticky-atc.is--shown {
    -webkit-transform: translate3d(0,0,0) !important;
    transform: translate3d(0,0,0) !important;
}
.bee-sticky-atc__product,
.bee-sticky-atc__btns {
    display: flex;
    align-items: center;
}
.bee-sticky-atc__img {
    width: 65px;
    height: 65px;
    min-width: 65px;
    border-radius: 50%;
    overflow: hidden;
}
.bee-sticky-atc__img img {
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 50%;
}
.bee-sticky-atc__title {
    color: var(--secondary-color);
    font-weight: 500;
    font-size: 15px;
}
.bee-sticky-atc__price {
    color: var(--secondary-price-color);
    font-size: 15px;
}
.bee-sticky-atc__price ins {
    color: var(--sale-price-color);
}
.bee-sticky-atc__price del {
    color: #868686;
}
.bee-sticky-atc__infos {
    margin: 0 15px;
}
.bee-sticky-atc__qty {
    min-width: 120px;
    width: 120px;
    height: 40px;
    border: 2px solid var(--border-color);
    text-align: center;
    display: inline-block;
    position: relative;
    border-radius: var(--btn-radius);
    margin-right: 15px;
}
.bee-sticky-atc__qty:hover {
    border-color: var(--secondary-color);
}
.bee-sticky-atc__qty button {
    position: absolute;
    display: block;
    padding: 0;
    top: 0;
    width: 30px;
    height: 38px;
    line-height: 38px;
    border: 0;
    background: 0 0;
    color: var(--text-color);
}
.bee-sticky-atc__qty button:hover {
    color: var(--secondary-color);
}
.bee-sticky-atc__qty .is--minus {
    left: 0;
    text-align: center;
    padding-left: 5px;
}
.bee-sticky-atc__qty .is--plus {
    right: 0;
    text-align: center;
    padding-right: 5px;
}
.bee-sticky-atc__qty input.bee-quantity-input[type=number] {
    width: 35px;
    border: 0px;
    height: 38px;
    background: 0px 0px;
    padding: 0;
    font-weight: 600;
    font-size: 16px;
    color: var(--secondary-color);
    text-align: center;
}
.bee-sticky-atc__qty button svg {
    width: 10px;
    height: 12px;
    stroke-width: 1;
}
.bee-sticky-atc__atc {
    font-size: 12px;
    font-weight: 500;
    min-height: 40px;
    padding: 5px 25px;
    min-width: 160px;
    text-transform: uppercase;
    border-radius: var(--btn-radius);
    background-color: var(--secondary-color);
    color: var(--bee-light-color);
}
.bee-sticky-atc__atc:hover {
    background-color: var(--accent-color-darken);
}
.bee-sticky-atc__v-title {
    margin: 0 30px;
    color: var(--secondary-color);
    text-decoration: underline;
}
[data-sticky-v-title] {
    cursor: pointer;
}
.bee-sticky-atc__v-title .bee-dropdown {
   cursor: default;
}
.bee-sticky-atc__v-title:empty {
    margin: 0 44px;
}
.bee-sticky-atc__v-title select {
    min-width: 100px;
}
.rtl_false .bee-sticky-atc__v-title .bee-icon-select-arrow {
    right: 8px;
}
.rtl_false .bee-sticky-atc__v-title .bee-dropdown__sortby button[data-dropdown-open] {
    padding-right: 20px;
}
.rtl_true .bee-sticky-atc__v-title .bee-icon-select-arrow {
    left: 8px;
}
.rtl_true .bee-sticky-atc__v-title .bee-dropdown__sortby button[data-dropdown-open] {
    padding-left: 20px;
}
button.bee-sticky-atc__atc[disabled="disabled"],
button.bee-sticky-atc__atc[aria-disabled="true"] {
    opacity: .5;
    animation: none !important;
    pointer-events: none;
}
.bee-sticky-atc[hidden],
button.bee-sticky-close {
    display: none;
}
.bee-sticky-atc__v-title .bee-drop-arrow {
    display: none !important;
}
button.bee-sticky-atc__atc[disabled="disabled"]{
    pointer-events: none;
}
.bee-sticky-atc__v-title .bee-dropdown__content button[disabled] {
    pointer-events: none;
    opacity: .6;
}
.bee-sticky-atc__product .bee-dropdown__sortby button[data-dropdown-open] {
    color: var(--secondary-color);
    border-radius: var(--btn-radius);
    border-width: 2px;
}
@media (max-width: 767px) {
    .bee-sticky-atc {
        flex-wrap: wrap;
    }
    .bee-sticky-atc__img,
    .bee-sticky-atc__title {
        display: none
    }
    .bee-sticky-atc__product {
        font-size: 16px;
        margin-bottom: 5px;
    }
    .bee-sticky-atc__price {
        color: var(--secondary-color);
        font-size: 16px;
    }
    .bee-sticky-atc__infos{
        margin: 0;
    }
    html:not(.is--opend-drawer) .bee-sticky-atc {
        z-index: 465;
    }
    .sticky-is--active .bee-close-overlay.is--pindop {
        z-index: 462;
    }
    .bee-sticky-atc .bee-dropdown__wrapper, .bee-lb__wrapper {
        transition: .2s cubic-bezier(.645, .045, .355, 1);
    }
    .bee-sticky-atc .bee-dropdown__sortby button[data-dropdown-open] {
        border-bottom: 1px solid var(--border-color);
        border-radius: 0;
        min-width: 50px;
        padding: 5px 20px 5px 0;
    }
    .bee-sticky-atc .bee-dropdown__sortby button[data-dropdown-open] svg {
        right: 0;
        top: calc(50% - 7px);
        width: 12px;
    }
    .sticky_layout_mb--minimal .bee-sticky-atc__product,
    .sticky_layout_mb--minimal2 .bee-sticky-atc__product {
        display: none
    }
    .sticky_layout_mb--minimal .bee-sticky-atc__title,
    .sticky_layout_mb--minimal .bee-sticky-atc__img {
        display: block;
    }
    .sticky_layout_mb--minimal .bee-sticky-atc__product {
        flex-wrap: wrap;
        flex-direction: row;
    }
    .sticky_layout_mb--minimal .bee-sticky-atc__infos {
        width: calc( 100% - 110px);
        width: -webkit-calc( 100% - 110px);
        padding: 0 15px;
        margin: 0;
    }
    .sticky_layout_mb--minimal .bee-sticky-atc__v-title {
        width: 100%;
        margin: 10px 0;
        text-align: center;
        border-top: 1px solid var(--border-color);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .sticky_layout_mb--minimal .bee-sticky-atc__v-title.is--enable_select_false {
        padding: 9px 0;
        border-bottom: 1px solid var(--border-color);
    }
    .sticky_layout_mb--minimal button[data-dropdown-open] {
        width: 100%;
        min-height: 44px !important;
    }
    .sticky_layout_mb--minimal .bee-sticky-atc__title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    button.bee-sticky-close {
        width: 44px;
        height: 44px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        background-color: transparent;
        color: var(--secondary-color);
        border-radius: 50%;
        padding: 0
    }
}
@media (max-width: 575px) {
    .bee-sticky-atc__product, .bee-sticky-atc__btns {        
        width: 100%;
    }
    .bee-sticky-atc__qty {
        margin-right: 10px;
    }
    .bee-sticky-atc__atc {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%;
    }
}