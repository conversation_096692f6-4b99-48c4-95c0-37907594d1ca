var BEEThemeSP={},isStorageSpSession=!1,isStorageSpSessionAll=!1,isStorageSpdLocal=!1,isStorageSpdLocalAll=!1,BEEconfigs=window.BEEconfigs,IsDesignMode=window.BEErequest.design_mode,isThemeRTL="rtl"==document.documentElement.getAttribute("dir");Beett_var={HoverInterval:35,HoverTimeout:150,dragThreshold:10,prevOnHref:!1},function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){"use strict";var i=[],n=Object.getPrototypeOf,o=i.slice,r=i.flat?function(e){return i.flat.call(e)}:function(e){return i.concat.apply([],e)},s=i.push,a=i.indexOf,l={},u=l.toString,c=l.hasOwnProperty,h=c.toString,d=h.call(Object),f={},p=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},g=function(e){return null!=e&&e===e.window},m=e.document,v={type:!0,src:!0,nonce:!0,noModule:!0};function y(e,t,i){var n,o,r=(i=i||m).createElement("script");if(r.text=e,t)for(n in v)(o=t[n]||t.getAttribute&&t.getAttribute(n))&&r.setAttribute(n,o);i.head.appendChild(r).parentNode.removeChild(r)}function b(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?l[u.call(e)]||"object":typeof e}var x="3.6.0",w=function(e,t){return new w.fn.init(e,t)};function E(e){var t=!!e&&"length"in e&&e.length,i=b(e);return!p(e)&&!g(e)&&("array"===i||0===t||"number"==typeof t&&0<t&&t-1 in e)}w.fn=w.prototype={jquery:x,constructor:w,length:0,toArray:function(){return o.call(this)},get:function(e){return null==e?o.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=w.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return w.each(this,e)},map:function(e){return this.pushStack(w.map(this,function(t,i){return e.call(t,i,t)}))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(w.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(w.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,i=+e+(e<0?t:0);return this.pushStack(0<=i&&i<t?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:s,sort:i.sort,splice:i.splice},w.extend=w.fn.extend=function(){var e,t,i,n,o,r,s=arguments[0]||{},a=1,l=arguments.length,u=!1;for("boolean"==typeof s&&(u=s,s=arguments[a]||{},a++),"object"==typeof s||p(s)||(s={}),a===l&&(s=this,a--);a<l;a++)if(null!=(e=arguments[a]))for(t in e)n=e[t],"__proto__"!==t&&s!==n&&(u&&n&&(w.isPlainObject(n)||(o=Array.isArray(n)))?(i=s[t],r=o&&!Array.isArray(i)?[]:o||w.isPlainObject(i)?i:{},o=!1,s[t]=w.extend(u,r,n)):void 0!==n&&(s[t]=n));return s},w.extend({expando:"jQuery"+(x+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,i;return!(!e||"[object Object]"!==u.call(e)||(t=n(e))&&("function"!=typeof(i=c.call(t,"constructor")&&t.constructor)||h.call(i)!==d))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,i){y(e,{nonce:t&&t.nonce},i)},each:function(e,t){var i,n=0;if(E(e))for(i=e.length;n<i&&!1!==t.call(e[n],n,e[n]);n++);else for(n in e)if(!1===t.call(e[n],n,e[n]))break;return e},makeArray:function(e,t){var i=t||[];return null!=e&&(E(Object(e))?w.merge(i,"string"==typeof e?[e]:e):s.call(i,e)),i},inArray:function(e,t,i){return null==t?-1:a.call(t,e,i)},merge:function(e,t){for(var i=+t.length,n=0,o=e.length;n<i;n++)e[o++]=t[n];return e.length=o,e},grep:function(e,t,i){for(var n=[],o=0,r=e.length,s=!i;o<r;o++)!t(e[o],o)!==s&&n.push(e[o]);return n},map:function(e,t,i){var n,o,s=0,a=[];if(E(e))for(n=e.length;s<n;s++)null!=(o=t(e[s],s,i))&&a.push(o);else for(s in e)null!=(o=t(e[s],s,i))&&a.push(o);return r(a)},guid:1,support:f}),"function"==typeof Symbol&&(w.fn[Symbol.iterator]=i[Symbol.iterator]),w.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){l["[object "+t+"]"]=t.toLowerCase()});var S=function(e){var t,i,n,o,r,s,a,l,u,c,h,d,f,p,g,m,v,y,b,x="sizzle"+1*new Date,w=e.document,E=0,S=0,C=le(),T=le(),_=le(),k=le(),I=function(e,t){return e===t&&(h=!0),0},D={}.hasOwnProperty,P=[],L=P.pop,A=P.push,z=P.push,N=P.slice,B=function(e,t){for(var i=0,n=e.length;i<n;i++)if(e[i]===t)return i;return-1},M="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",j="[\\x20\\t\\r\\n\\f]",W="(?:\\\\[\\da-fA-F]{1,6}"+j+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",O="\\["+j+"*("+W+")(?:"+j+"*([*^$|!~]?=)"+j+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+W+"))|)"+j+"*\\]",q=":("+W+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+O+")*)|.*)\\)|)",$=new RegExp(j+"+","g"),H=new RegExp("^"+j+"+|((?:^|[^\\\\])(?:\\\\.)*)"+j+"+$","g"),R=new RegExp("^"+j+"*,"+j+"*"),F=new RegExp("^"+j+"*([>+~]|"+j+")"+j+"*"),U=new RegExp(j+"|>"),V=new RegExp(q),G=new RegExp("^"+W+"$"),X={ID:new RegExp("^#("+W+")"),CLASS:new RegExp("^\\.("+W+")"),TAG:new RegExp("^("+W+"|[*])"),ATTR:new RegExp("^"+O),PSEUDO:new RegExp("^"+q),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+j+"*(even|odd|(([+-]|)(\\d*)n|)"+j+"*(?:([+-]|)"+j+"*(\\d+)|))"+j+"*\\)|)","i"),bool:new RegExp("^(?:"+M+")$","i"),needsContext:new RegExp("^"+j+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+j+"*((?:-\\d)?\\d*)"+j+"*\\)|)(?=[^-]|$)","i")},Y=/HTML$/i,Q=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,Z=/^[^{]+\{\s*\[native \w/,K=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ee=/[+~]/,te=new RegExp("\\\\[\\da-fA-F]{1,6}"+j+"?|\\\\([^\\r\\n\\f])","g"),ie=function(e,t){var i="0x"+e.slice(1)-65536;return t||(i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320))},ne=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,oe=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},re=function(){d()},se=xe(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{z.apply(P=N.call(w.childNodes),w.childNodes),P[w.childNodes.length].nodeType}catch(t){z={apply:P.length?function(e,t){A.apply(e,N.call(t))}:function(e,t){for(var i=e.length,n=0;e[i++]=t[n++];);e.length=i-1}}}function ae(e,t,n,o){var r,a,u,c,h,p,v,y=t&&t.ownerDocument,w=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==w&&9!==w&&11!==w)return n;if(!o&&(d(t),t=t||f,g)){if(11!==w&&(h=K.exec(e)))if(r=h[1]){if(9===w){if(!(u=t.getElementById(r)))return n;if(u.id===r)return n.push(u),n}else if(y&&(u=y.getElementById(r))&&b(t,u)&&u.id===r)return n.push(u),n}else{if(h[2])return z.apply(n,t.getElementsByTagName(e)),n;if((r=h[3])&&i.getElementsByClassName&&t.getElementsByClassName)return z.apply(n,t.getElementsByClassName(r)),n}if(i.qsa&&!k[e+" "]&&(!m||!m.test(e))&&(1!==w||"object"!==t.nodeName.toLowerCase())){if(v=e,y=t,1===w&&(U.test(e)||F.test(e))){for((y=ee.test(e)&&ve(t.parentNode)||t)===t&&i.scope||((c=t.getAttribute("id"))?c=c.replace(ne,oe):t.setAttribute("id",c=x)),a=(p=s(e)).length;a--;)p[a]=(c?"#"+c:":scope")+" "+be(p[a]);v=p.join(",")}try{return z.apply(n,y.querySelectorAll(v)),n}catch(t){k(e,!0)}finally{c===x&&t.removeAttribute("id")}}}return l(e.replace(H,"$1"),t,n,o)}function le(){var e=[];return function t(i,o){return e.push(i+" ")>n.cacheLength&&delete t[e.shift()],t[i+" "]=o}}function ue(e){return e[x]=!0,e}function ce(e){var t=f.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function he(e,t){for(var i=e.split("|"),o=i.length;o--;)n.attrHandle[i[o]]=t}function de(e,t){var i=t&&e,n=i&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(n)return n;if(i)for(;i=i.nextSibling;)if(i===t)return-1;return e?1:-1}function fe(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}function pe(e){return function(t){var i=t.nodeName.toLowerCase();return("input"===i||"button"===i)&&t.type===e}}function ge(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&se(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function me(e){return ue(function(t){return t=+t,ue(function(i,n){for(var o,r=e([],i.length,t),s=r.length;s--;)i[o=r[s]]&&(i[o]=!(n[o]=i[o]))})})}function ve(e){return e&&void 0!==e.getElementsByTagName&&e}for(t in i=ae.support={},r=ae.isXML=function(e){var t=e&&e.namespaceURI,i=e&&(e.ownerDocument||e).documentElement;return!Y.test(t||i&&i.nodeName||"HTML")},d=ae.setDocument=function(e){var t,o,s=e?e.ownerDocument||e:w;return s!=f&&9===s.nodeType&&s.documentElement&&(p=(f=s).documentElement,g=!r(f),w!=f&&(o=f.defaultView)&&o.top!==o&&(o.addEventListener?o.addEventListener("unload",re,!1):o.attachEvent&&o.attachEvent("onunload",re)),i.scope=ce(function(e){return p.appendChild(e).appendChild(f.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),i.attributes=ce(function(e){return e.className="i",!e.getAttribute("className")}),i.getElementsByTagName=ce(function(e){return e.appendChild(f.createComment("")),!e.getElementsByTagName("*").length}),i.getElementsByClassName=Z.test(f.getElementsByClassName),i.getById=ce(function(e){return p.appendChild(e).id=x,!f.getElementsByName||!f.getElementsByName(x).length}),i.getById?(n.filter.ID=function(e){var t=e.replace(te,ie);return function(e){return e.getAttribute("id")===t}},n.find.ID=function(e,t){if(void 0!==t.getElementById&&g){var i=t.getElementById(e);return i?[i]:[]}}):(n.filter.ID=function(e){var t=e.replace(te,ie);return function(e){var i=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return i&&i.value===t}},n.find.ID=function(e,t){if(void 0!==t.getElementById&&g){var i,n,o,r=t.getElementById(e);if(r){if((i=r.getAttributeNode("id"))&&i.value===e)return[r];for(o=t.getElementsByName(e),n=0;r=o[n++];)if((i=r.getAttributeNode("id"))&&i.value===e)return[r]}return[]}}),n.find.TAG=i.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):i.qsa?t.querySelectorAll(e):void 0}:function(e,t){var i,n=[],o=0,r=t.getElementsByTagName(e);if("*"===e){for(;i=r[o++];)1===i.nodeType&&n.push(i);return n}return r},n.find.CLASS=i.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&g)return t.getElementsByClassName(e)},v=[],m=[],(i.qsa=Z.test(f.querySelectorAll))&&(ce(function(e){var t;p.appendChild(e).innerHTML="<a id='"+x+"'></a><select id='"+x+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+j+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+j+"*(?:value|"+M+")"),e.querySelectorAll("[id~="+x+"-]").length||m.push("~="),(t=f.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||m.push("\\["+j+"*name"+j+"*="+j+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+x+"+*").length||m.push(".#.+[+~]"),e.querySelectorAll("\\\f"),m.push("[\\r\\n\\f]")}),ce(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=f.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+j+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),p.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")})),(i.matchesSelector=Z.test(y=p.matches||p.webkitMatchesSelector||p.mozMatchesSelector||p.oMatchesSelector||p.msMatchesSelector))&&ce(function(e){i.disconnectedMatch=y.call(e,"*"),y.call(e,"[s!='']:x"),v.push("!=",q)}),m=m.length&&new RegExp(m.join("|")),v=v.length&&new RegExp(v.join("|")),t=Z.test(p.compareDocumentPosition),b=t||Z.test(p.contains)?function(e,t){var i=9===e.nodeType?e.documentElement:e,n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(i.contains?i.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},I=t?function(e,t){if(e===t)return h=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!i.sortDetached&&t.compareDocumentPosition(e)===n?e==f||e.ownerDocument==w&&b(w,e)?-1:t==f||t.ownerDocument==w&&b(w,t)?1:c?B(c,e)-B(c,t):0:4&n?-1:1)}:function(e,t){if(e===t)return h=!0,0;var i,n=0,o=e.parentNode,r=t.parentNode,s=[e],a=[t];if(!o||!r)return e==f?-1:t==f?1:o?-1:r?1:c?B(c,e)-B(c,t):0;if(o===r)return de(e,t);for(i=e;i=i.parentNode;)s.unshift(i);for(i=t;i=i.parentNode;)a.unshift(i);for(;s[n]===a[n];)n++;return n?de(s[n],a[n]):s[n]==w?-1:a[n]==w?1:0}),f},ae.matches=function(e,t){return ae(e,null,null,t)},ae.matchesSelector=function(e,t){if(d(e),i.matchesSelector&&g&&!k[t+" "]&&(!v||!v.test(t))&&(!m||!m.test(t)))try{var n=y.call(e,t);if(n||i.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){k(t,!0)}return 0<ae(t,f,null,[e]).length},ae.contains=function(e,t){return(e.ownerDocument||e)!=f&&d(e),b(e,t)},ae.attr=function(e,t){(e.ownerDocument||e)!=f&&d(e);var o=n.attrHandle[t.toLowerCase()],r=o&&D.call(n.attrHandle,t.toLowerCase())?o(e,t,!g):void 0;return void 0!==r?r:i.attributes||!g?e.getAttribute(t):(r=e.getAttributeNode(t))&&r.specified?r.value:null},ae.escape=function(e){return(e+"").replace(ne,oe)},ae.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},ae.uniqueSort=function(e){var t,n=[],o=0,r=0;if(h=!i.detectDuplicates,c=!i.sortStable&&e.slice(0),e.sort(I),h){for(;t=e[r++];)t===e[r]&&(o=n.push(r));for(;o--;)e.splice(n[o],1)}return c=null,e},o=ae.getText=function(e){var t,i="",n=0,r=e.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)i+=o(e)}else if(3===r||4===r)return e.nodeValue}else for(;t=e[n++];)i+=o(t);return i},(n=ae.selectors={cacheLength:50,createPseudo:ue,match:X,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(te,ie),e[3]=(e[3]||e[4]||e[5]||"").replace(te,ie),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ae.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ae.error(e[0]),e},PSEUDO:function(e){var t,i=!e[6]&&e[2];return X.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":i&&V.test(i)&&(t=s(i,!0))&&(t=i.indexOf(")",i.length-t)-i.length)&&(e[0]=e[0].slice(0,t),e[2]=i.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(te,ie).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=C[e+" "];return t||(t=new RegExp("(^|"+j+")"+e+"("+j+"|$)"))&&C(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,t,i){return function(n){var o=ae.attr(n,e);return null==o?"!="===t:!t||(o+="","="===t?o===i:"!="===t?o!==i:"^="===t?i&&0===o.indexOf(i):"*="===t?i&&-1<o.indexOf(i):"$="===t?i&&o.slice(-i.length)===i:"~="===t?-1<(" "+o.replace($," ")+" ").indexOf(i):"|="===t&&(o===i||o.slice(0,i.length+1)===i+"-"))}},CHILD:function(e,t,i,n,o){var r="nth"!==e.slice(0,3),s="last"!==e.slice(-4),a="of-type"===t;return 1===n&&0===o?function(e){return!!e.parentNode}:function(t,i,l){var u,c,h,d,f,p,g=r!==s?"nextSibling":"previousSibling",m=t.parentNode,v=a&&t.nodeName.toLowerCase(),y=!l&&!a,b=!1;if(m){if(r){for(;g;){for(d=t;d=d[g];)if(a?d.nodeName.toLowerCase()===v:1===d.nodeType)return!1;p=g="only"===e&&!p&&"nextSibling"}return!0}if(p=[s?m.firstChild:m.lastChild],s&&y){for(b=(f=(u=(c=(h=(d=m)[x]||(d[x]={}))[d.uniqueID]||(h[d.uniqueID]={}))[e]||[])[0]===E&&u[1])&&u[2],d=f&&m.childNodes[f];d=++f&&d&&d[g]||(b=f=0)||p.pop();)if(1===d.nodeType&&++b&&d===t){c[e]=[E,f,b];break}}else if(y&&(b=f=(u=(c=(h=(d=t)[x]||(d[x]={}))[d.uniqueID]||(h[d.uniqueID]={}))[e]||[])[0]===E&&u[1]),!1===b)for(;(d=++f&&d&&d[g]||(b=f=0)||p.pop())&&((a?d.nodeName.toLowerCase()!==v:1!==d.nodeType)||!++b||(y&&((c=(h=d[x]||(d[x]={}))[d.uniqueID]||(h[d.uniqueID]={}))[e]=[E,b]),d!==t)););return(b-=o)===n||b%n==0&&0<=b/n}}},PSEUDO:function(e,t){var i,o=n.pseudos[e]||n.setFilters[e.toLowerCase()]||ae.error("unsupported pseudo: "+e);return o[x]?o(t):1<o.length?(i=[e,e,"",t],n.setFilters.hasOwnProperty(e.toLowerCase())?ue(function(e,i){for(var n,r=o(e,t),s=r.length;s--;)e[n=B(e,r[s])]=!(i[n]=r[s])}):function(e){return o(e,0,i)}):o}},pseudos:{not:ue(function(e){var t=[],i=[],n=a(e.replace(H,"$1"));return n[x]?ue(function(e,t,i,o){for(var r,s=n(e,null,o,[]),a=e.length;a--;)(r=s[a])&&(e[a]=!(t[a]=r))}):function(e,o,r){return t[0]=e,n(t,null,r,i),t[0]=null,!i.pop()}}),has:ue(function(e){return function(t){return 0<ae(e,t).length}}),contains:ue(function(e){return e=e.replace(te,ie),function(t){return-1<(t.textContent||o(t)).indexOf(e)}}),lang:ue(function(e){return G.test(e||"")||ae.error("unsupported lang: "+e),e=e.replace(te,ie).toLowerCase(),function(t){var i;do{if(i=g?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(i=i.toLowerCase())===e||0===i.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var i=e.location&&e.location.hash;return i&&i.slice(1)===t.id},root:function(e){return e===p},focus:function(e){return e===f.activeElement&&(!f.hasFocus||f.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:ge(!1),disabled:ge(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!n.pseudos.empty(e)},header:function(e){return J.test(e.nodeName)},input:function(e){return Q.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:me(function(){return[0]}),last:me(function(e,t){return[t-1]}),eq:me(function(e,t,i){return[i<0?i+t:i]}),even:me(function(e,t){for(var i=0;i<t;i+=2)e.push(i);return e}),odd:me(function(e,t){for(var i=1;i<t;i+=2)e.push(i);return e}),lt:me(function(e,t,i){for(var n=i<0?i+t:t<i?t:i;0<=--n;)e.push(n);return e}),gt:me(function(e,t,i){for(var n=i<0?i+t:i;++n<t;)e.push(n);return e})}}).pseudos.nth=n.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})n.pseudos[t]=fe(t);for(t in{submit:!0,reset:!0})n.pseudos[t]=pe(t);function ye(){}function be(e){for(var t=0,i=e.length,n="";t<i;t++)n+=e[t].value;return n}function xe(e,t,i){var n=t.dir,o=t.next,r=o||n,s=i&&"parentNode"===r,a=S++;return t.first?function(t,i,o){for(;t=t[n];)if(1===t.nodeType||s)return e(t,i,o);return!1}:function(t,i,l){var u,c,h,d=[E,a];if(l){for(;t=t[n];)if((1===t.nodeType||s)&&e(t,i,l))return!0}else for(;t=t[n];)if(1===t.nodeType||s)if(c=(h=t[x]||(t[x]={}))[t.uniqueID]||(h[t.uniqueID]={}),o&&o===t.nodeName.toLowerCase())t=t[n]||t;else{if((u=c[r])&&u[0]===E&&u[1]===a)return d[2]=u[2];if((c[r]=d)[2]=e(t,i,l))return!0}return!1}}function we(e){return 1<e.length?function(t,i,n){for(var o=e.length;o--;)if(!e[o](t,i,n))return!1;return!0}:e[0]}function Ee(e,t,i,n,o){for(var r,s=[],a=0,l=e.length,u=null!=t;a<l;a++)(r=e[a])&&(i&&!i(r,n,o)||(s.push(r),u&&t.push(a)));return s}function Se(e,t,i,n,o,r){return n&&!n[x]&&(n=Se(n)),o&&!o[x]&&(o=Se(o,r)),ue(function(r,s,a,l){var u,c,h,d=[],f=[],p=s.length,g=r||function(e,t,i){for(var n=0,o=t.length;n<o;n++)ae(e,t[n],i);return i}(t||"*",a.nodeType?[a]:a,[]),m=!e||!r&&t?g:Ee(g,d,e,a,l),v=i?o||(r?e:p||n)?[]:s:m;if(i&&i(m,v,a,l),n)for(u=Ee(v,f),n(u,[],a,l),c=u.length;c--;)(h=u[c])&&(v[f[c]]=!(m[f[c]]=h));if(r){if(o||e){if(o){for(u=[],c=v.length;c--;)(h=v[c])&&u.push(m[c]=h);o(null,v=[],u,l)}for(c=v.length;c--;)(h=v[c])&&-1<(u=o?B(r,h):d[c])&&(r[u]=!(s[u]=h))}}else v=Ee(v===s?v.splice(p,v.length):v),o?o(null,s,v,l):z.apply(s,v)})}function Ce(e){for(var t,i,o,r=e.length,s=n.relative[e[0].type],a=s||n.relative[" "],l=s?1:0,c=xe(function(e){return e===t},a,!0),h=xe(function(e){return-1<B(t,e)},a,!0),d=[function(e,i,n){var o=!s&&(n||i!==u)||((t=i).nodeType?c(e,i,n):h(e,i,n));return t=null,o}];l<r;l++)if(i=n.relative[e[l].type])d=[xe(we(d),i)];else{if((i=n.filter[e[l].type].apply(null,e[l].matches))[x]){for(o=++l;o<r&&!n.relative[e[o].type];o++);return Se(1<l&&we(d),1<l&&be(e.slice(0,l-1).concat({value:" "===e[l-2].type?"*":""})).replace(H,"$1"),i,l<o&&Ce(e.slice(l,o)),o<r&&Ce(e=e.slice(o)),o<r&&be(e))}d.push(i)}return we(d)}return ye.prototype=n.filters=n.pseudos,n.setFilters=new ye,s=ae.tokenize=function(e,t){var i,o,r,s,a,l,u,c=T[e+" "];if(c)return t?0:c.slice(0);for(a=e,l=[],u=n.preFilter;a;){for(s in i&&!(o=R.exec(a))||(o&&(a=a.slice(o[0].length)||a),l.push(r=[])),i=!1,(o=F.exec(a))&&(i=o.shift(),r.push({value:i,type:o[0].replace(H," ")}),a=a.slice(i.length)),n.filter)!(o=X[s].exec(a))||u[s]&&!(o=u[s](o))||(i=o.shift(),r.push({value:i,type:s,matches:o}),a=a.slice(i.length));if(!i)break}return t?a.length:a?ae.error(e):T(e,l).slice(0)},a=ae.compile=function(e,t){var i,o,r,a,l,c,h=[],p=[],m=_[e+" "];if(!m){for(t||(t=s(e)),i=t.length;i--;)(m=Ce(t[i]))[x]?h.push(m):p.push(m);(m=_(e,(o=p,a=0<(r=h).length,l=0<o.length,c=function(e,t,i,s,c){var h,p,m,v=0,y="0",b=e&&[],x=[],w=u,S=e||l&&n.find.TAG("*",c),C=E+=null==w?1:Math.random()||.1,T=S.length;for(c&&(u=t==f||t||c);y!==T&&null!=(h=S[y]);y++){if(l&&h){for(p=0,t||h.ownerDocument==f||(d(h),i=!g);m=o[p++];)if(m(h,t||f,i)){s.push(h);break}c&&(E=C)}a&&((h=!m&&h)&&v--,e&&b.push(h))}if(v+=y,a&&y!==v){for(p=0;m=r[p++];)m(b,x,t,i);if(e){if(0<v)for(;y--;)b[y]||x[y]||(x[y]=L.call(s));x=Ee(x)}z.apply(s,x),c&&!e&&0<x.length&&1<v+r.length&&ae.uniqueSort(s)}return c&&(E=C,u=w),b},a?ue(c):c))).selector=e}return m},l=ae.select=function(e,t,i,o){var r,l,u,c,h,d="function"==typeof e&&e,f=!o&&s(e=d.selector||e);if(i=i||[],1===f.length){if(2<(l=f[0]=f[0].slice(0)).length&&"ID"===(u=l[0]).type&&9===t.nodeType&&g&&n.relative[l[1].type]){if(!(t=(n.find.ID(u.matches[0].replace(te,ie),t)||[])[0]))return i;d&&(t=t.parentNode),e=e.slice(l.shift().value.length)}for(r=X.needsContext.test(e)?0:l.length;r--&&(u=l[r],!n.relative[c=u.type]);)if((h=n.find[c])&&(o=h(u.matches[0].replace(te,ie),ee.test(l[0].type)&&ve(t.parentNode)||t))){if(l.splice(r,1),!(e=o.length&&be(l)))return z.apply(i,o),i;break}}return(d||a(e,f))(o,t,!g,i,!t||ee.test(e)&&ve(t.parentNode)||t),i},i.sortStable=x.split("").sort(I).join("")===x,i.detectDuplicates=!!h,d(),i.sortDetached=ce(function(e){return 1&e.compareDocumentPosition(f.createElement("fieldset"))}),ce(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||he("type|href|height|width",function(e,t,i){if(!i)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),i.attributes&&ce(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||he("value",function(e,t,i){if(!i&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),ce(function(e){return null==e.getAttribute("disabled")})||he(M,function(e,t,i){var n;if(!i)return!0===e[t]?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null}),ae}(e);w.find=S,w.expr=S.selectors,w.expr[":"]=w.expr.pseudos,w.uniqueSort=w.unique=S.uniqueSort,w.text=S.getText,w.isXMLDoc=S.isXML,w.contains=S.contains,w.escapeSelector=S.escape;var C=function(e,t,i){for(var n=[],o=void 0!==i;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(o&&w(e).is(i))break;n.push(e)}return n},T=function(e,t){for(var i=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&i.push(e);return i},_=w.expr.match.needsContext;function k(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var I=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function D(e,t,i){return p(t)?w.grep(e,function(e,n){return!!t.call(e,n,e)!==i}):t.nodeType?w.grep(e,function(e){return e===t!==i}):"string"!=typeof t?w.grep(e,function(e){return-1<a.call(t,e)!==i}):w.filter(t,e,i)}w.filter=function(e,t,i){var n=t[0];return i&&(e=":not("+e+")"),1===t.length&&1===n.nodeType?w.find.matchesSelector(n,e)?[n]:[]:w.find.matches(e,w.grep(t,function(e){return 1===e.nodeType}))},w.fn.extend({find:function(e){var t,i,n=this.length,o=this;if("string"!=typeof e)return this.pushStack(w(e).filter(function(){for(t=0;t<n;t++)if(w.contains(o[t],this))return!0}));for(i=this.pushStack([]),t=0;t<n;t++)w.find(e,o[t],i);return 1<n?w.uniqueSort(i):i},filter:function(e){return this.pushStack(D(this,e||[],!1))},not:function(e){return this.pushStack(D(this,e||[],!0))},is:function(e){return!!D(this,"string"==typeof e&&_.test(e)?w(e):e||[],!1).length}});var P,L=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(w.fn.init=function(e,t,i){var n,o;if(!e)return this;if(i=i||P,"string"==typeof e){if(!(n="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:L.exec(e))||!n[1]&&t)return!t||t.jquery?(t||i).find(e):this.constructor(t).find(e);if(n[1]){if(t=t instanceof w?t[0]:t,w.merge(this,w.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:m,!0)),I.test(n[1])&&w.isPlainObject(t))for(n in t)p(this[n])?this[n](t[n]):this.attr(n,t[n]);return this}return(o=m.getElementById(n[2]))&&(this[0]=o,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):p(e)?void 0!==i.ready?i.ready(e):e(w):w.makeArray(e,this)}).prototype=w.fn,P=w(m);var A=/^(?:parents|prev(?:Until|All))/,z={children:!0,contents:!0,next:!0,prev:!0};function N(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}w.fn.extend({has:function(e){var t=w(e,this),i=t.length;return this.filter(function(){for(var e=0;e<i;e++)if(w.contains(this,t[e]))return!0})},closest:function(e,t){var i,n=0,o=this.length,r=[],s="string"!=typeof e&&w(e);if(!_.test(e))for(;n<o;n++)for(i=this[n];i&&i!==t;i=i.parentNode)if(i.nodeType<11&&(s?-1<s.index(i):1===i.nodeType&&w.find.matchesSelector(i,e))){r.push(i);break}return this.pushStack(1<r.length?w.uniqueSort(r):r)},index:function(e){return e?"string"==typeof e?a.call(w(e),this[0]):a.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(w.uniqueSort(w.merge(this.get(),w(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),w.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return C(e,"parentNode")},parentsUntil:function(e,t,i){return C(e,"parentNode",i)},next:function(e){return N(e,"nextSibling")},prev:function(e){return N(e,"previousSibling")},nextAll:function(e){return C(e,"nextSibling")},prevAll:function(e){return C(e,"previousSibling")},nextUntil:function(e,t,i){return C(e,"nextSibling",i)},prevUntil:function(e,t,i){return C(e,"previousSibling",i)},siblings:function(e){return T((e.parentNode||{}).firstChild,e)},children:function(e){return T(e.firstChild)},contents:function(e){return null!=e.contentDocument&&n(e.contentDocument)?e.contentDocument:(k(e,"template")&&(e=e.content||e),w.merge([],e.childNodes))}},function(e,t){w.fn[e]=function(i,n){var o=w.map(this,t,i);return"Until"!==e.slice(-5)&&(n=i),n&&"string"==typeof n&&(o=w.filter(n,o)),1<this.length&&(z[e]||w.uniqueSort(o),A.test(e)&&o.reverse()),this.pushStack(o)}});var B=/[^\x20\t\r\n\f]+/g;function M(e){return e}function j(e){throw e}function W(e,t,i,n){var o;try{e&&p(o=e.promise)?o.call(e).done(t).fail(i):e&&p(o=e.then)?o.call(e,t,i):t.apply(void 0,[e].slice(n))}catch(e){i.apply(void 0,[e])}}w.Callbacks=function(e){var t,i;e="string"==typeof e?(t=e,i={},w.each(t.match(B)||[],function(e,t){i[t]=!0}),i):w.extend({},e);var n,o,r,s,a=[],l=[],u=-1,c=function(){for(s=s||e.once,r=n=!0;l.length;u=-1)for(o=l.shift();++u<a.length;)!1===a[u].apply(o[0],o[1])&&e.stopOnFalse&&(u=a.length,o=!1);e.memory||(o=!1),n=!1,s&&(a=o?[]:"")},h={add:function(){return a&&(o&&!n&&(u=a.length-1,l.push(o)),function t(i){w.each(i,function(i,n){p(n)?e.unique&&h.has(n)||a.push(n):n&&n.length&&"string"!==b(n)&&t(n)})}(arguments),o&&!n&&c()),this},remove:function(){return w.each(arguments,function(e,t){for(var i;-1<(i=w.inArray(t,a,i));)a.splice(i,1),i<=u&&u--}),this},has:function(e){return e?-1<w.inArray(e,a):0<a.length},empty:function(){return a&&(a=[]),this},disable:function(){return s=l=[],a=o="",this},disabled:function(){return!a},lock:function(){return s=l=[],o||n||(a=o=""),this},locked:function(){return!!s},fireWith:function(e,t){return s||(t=[e,(t=t||[]).slice?t.slice():t],l.push(t),n||c()),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!r}};return h},w.extend({Deferred:function(t){var i=[["notify","progress",w.Callbacks("memory"),w.Callbacks("memory"),2],["resolve","done",w.Callbacks("once memory"),w.Callbacks("once memory"),0,"resolved"],["reject","fail",w.Callbacks("once memory"),w.Callbacks("once memory"),1,"rejected"]],n="pending",o={state:function(){return n},always:function(){return r.done(arguments).fail(arguments),this},catch:function(e){return o.then(null,e)},pipe:function(){var e=arguments;return w.Deferred(function(t){w.each(i,function(i,n){var o=p(e[n[4]])&&e[n[4]];r[n[1]](function(){var e=o&&o.apply(this,arguments);e&&p(e.promise)?e.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[n[0]+"With"](this,o?[e]:arguments)})}),e=null}).promise()},then:function(t,n,o){var r=0;function s(t,i,n,o){return function(){var a=this,l=arguments,u=function(){var e,u;if(!(t<r)){if((e=n.apply(a,l))===i.promise())throw new TypeError("Thenable self-resolution");u=e&&("object"==typeof e||"function"==typeof e)&&e.then,p(u)?o?u.call(e,s(r,i,M,o),s(r,i,j,o)):(r++,u.call(e,s(r,i,M,o),s(r,i,j,o),s(r,i,M,i.notifyWith))):(n!==M&&(a=void 0,l=[e]),(o||i.resolveWith)(a,l))}},c=o?u:function(){try{u()}catch(e){w.Deferred.exceptionHook&&w.Deferred.exceptionHook(e,c.stackTrace),r<=t+1&&(n!==j&&(a=void 0,l=[e]),i.rejectWith(a,l))}};t?c():(w.Deferred.getStackHook&&(c.stackTrace=w.Deferred.getStackHook()),e.setTimeout(c))}}return w.Deferred(function(e){i[0][3].add(s(0,e,p(o)?o:M,e.notifyWith)),i[1][3].add(s(0,e,p(t)?t:M)),i[2][3].add(s(0,e,p(n)?n:j))}).promise()},promise:function(e){return null!=e?w.extend(e,o):o}},r={};return w.each(i,function(e,t){var s=t[2],a=t[5];o[t[1]]=s.add,a&&s.add(function(){n=a},i[3-e][2].disable,i[3-e][3].disable,i[0][2].lock,i[0][3].lock),s.add(t[3].fire),r[t[0]]=function(){return r[t[0]+"With"](this===r?void 0:this,arguments),this},r[t[0]+"With"]=s.fireWith}),o.promise(r),t&&t.call(r,r),r},when:function(e){var t=arguments.length,i=t,n=Array(i),r=o.call(arguments),s=w.Deferred(),a=function(e){return function(i){n[e]=this,r[e]=1<arguments.length?o.call(arguments):i,--t||s.resolveWith(n,r)}};if(t<=1&&(W(e,s.done(a(i)).resolve,s.reject,!t),"pending"===s.state()||p(r[i]&&r[i].then)))return s.then();for(;i--;)W(r[i],a(i),s.reject);return s.promise()}});var O=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;w.Deferred.exceptionHook=function(t,i){e.console&&e.console.warn&&t&&O.test(t.name)&&e.console.warn("jQuery.Deferred exception: "+t.message,t.stack,i)},w.readyException=function(t){e.setTimeout(function(){throw t})};var q=w.Deferred();function $(){m.removeEventListener("DOMContentLoaded",$),e.removeEventListener("load",$),w.ready()}w.fn.ready=function(e){return q.then(e).catch(function(e){w.readyException(e)}),this},w.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--w.readyWait:w.isReady)||(w.isReady=!0)!==e&&0<--w.readyWait||q.resolveWith(m,[w])}}),w.ready.then=q.then,"complete"===m.readyState||"loading"!==m.readyState&&!m.documentElement.doScroll?e.setTimeout(w.ready):(m.addEventListener("DOMContentLoaded",$),e.addEventListener("load",$));var H=function(e,t,i,n,o,r,s){var a=0,l=e.length,u=null==i;if("object"===b(i))for(a in o=!0,i)H(e,t,a,i[a],!0,r,s);else if(void 0!==n&&(o=!0,p(n)||(s=!0),u&&(s?(t.call(e,n),t=null):(u=t,t=function(e,t,i){return u.call(w(e),i)})),t))for(;a<l;a++)t(e[a],i,s?n:n.call(e[a],a,t(e[a],i)));return o?e:u?t.call(e):l?t(e[0],i):r},R=/^-ms-/,F=/-([a-z])/g;function U(e,t){return t.toUpperCase()}function V(e){return e.replace(R,"ms-").replace(F,U)}var G=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function X(){this.expando=w.expando+X.uid++}X.uid=1,X.prototype={cache:function(e){var t=e[this.expando];return t||(t={},G(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,i){var n,o=this.cache(e);if("string"==typeof t)o[V(t)]=i;else for(n in t)o[V(n)]=t[n];return o},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][V(t)]},access:function(e,t,i){return void 0===t||t&&"string"==typeof t&&void 0===i?this.get(e,t):(this.set(e,t,i),void 0!==i?i:t)},remove:function(e,t){var i,n=e[this.expando];if(void 0!==n){if(void 0!==t){i=(t=Array.isArray(t)?t.map(V):(t=V(t))in n?[t]:t.match(B)||[]).length;for(;i--;)delete n[t[i]]}(void 0===t||w.isEmptyObject(n))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!w.isEmptyObject(t)}};var Y=new X,Q=new X,J=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Z=/[A-Z]/g;function K(e,t,i){var n,o;if(void 0===i&&1===e.nodeType)if(n="data-"+t.replace(Z,"-$&").toLowerCase(),"string"==typeof(i=e.getAttribute(n))){try{i="true"===(o=i)||"false"!==o&&("null"===o?null:o===+o+""?+o:J.test(o)?JSON.parse(o):o)}catch(e){}Q.set(e,t,i)}else i=void 0;return i}w.extend({hasData:function(e){return Q.hasData(e)||Y.hasData(e)},data:function(e,t,i){return Q.access(e,t,i)},removeData:function(e,t){Q.remove(e,t)},_data:function(e,t,i){return Y.access(e,t,i)},_removeData:function(e,t){Y.remove(e,t)}}),w.fn.extend({data:function(e,t){var i,n,o,r=this[0],s=r&&r.attributes;if(void 0===e){if(this.length&&(o=Q.get(r),1===r.nodeType&&!Y.get(r,"hasDataAttrs"))){for(i=s.length;i--;)s[i]&&0===(n=s[i].name).indexOf("data-")&&(n=V(n.slice(5)),K(r,n,o[n]));Y.set(r,"hasDataAttrs",!0)}return o}return"object"==typeof e?this.each(function(){Q.set(this,e)}):H(this,function(t){var i;if(r&&void 0===t)return void 0!==(i=Q.get(r,e))?i:void 0!==(i=K(r,e))?i:void 0;this.each(function(){Q.set(this,e,t)})},null,t,1<arguments.length,null,!0)},removeData:function(e){return this.each(function(){Q.remove(this,e)})}}),w.extend({queue:function(e,t,i){var n;if(e)return t=(t||"fx")+"queue",n=Y.get(e,t),i&&(!n||Array.isArray(i)?n=Y.access(e,t,w.makeArray(i)):n.push(i)),n||[]},dequeue:function(e,t){t=t||"fx";var i=w.queue(e,t),n=i.length,o=i.shift(),r=w._queueHooks(e,t);"inprogress"===o&&(o=i.shift(),n--),o&&("fx"===t&&i.unshift("inprogress"),delete r.stop,o.call(e,function(){w.dequeue(e,t)},r)),!n&&r&&r.empty.fire()},_queueHooks:function(e,t){var i=t+"queueHooks";return Y.get(e,i)||Y.access(e,i,{empty:w.Callbacks("once memory").add(function(){Y.remove(e,[t+"queue",i])})})}}),w.fn.extend({queue:function(e,t){var i=2;return"string"!=typeof e&&(t=e,e="fx",i--),arguments.length<i?w.queue(this[0],e):void 0===t?this:this.each(function(){var i=w.queue(this,e,t);w._queueHooks(this,e),"fx"===e&&"inprogress"!==i[0]&&w.dequeue(this,e)})},dequeue:function(e){return this.each(function(){w.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var i,n=1,o=w.Deferred(),r=this,s=this.length,a=function(){--n||o.resolveWith(r,[r])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(i=Y.get(r[s],e+"queueHooks"))&&i.empty&&(n++,i.empty.add(a));return a(),o.promise(t)}});var ee=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,te=new RegExp("^(?:([+-])=|)("+ee+")([a-z%]*)$","i"),ie=["Top","Right","Bottom","Left"],ne=m.documentElement,oe=function(e){return w.contains(e.ownerDocument,e)},re={composed:!0};ne.getRootNode&&(oe=function(e){return w.contains(e.ownerDocument,e)||e.getRootNode(re)===e.ownerDocument});var se=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&oe(e)&&"none"===w.css(e,"display")};function ae(e,t,i,n){var o,r,s=20,a=n?function(){return n.cur()}:function(){return w.css(e,t,"")},l=a(),u=i&&i[3]||(w.cssNumber[t]?"":"px"),c=e.nodeType&&(w.cssNumber[t]||"px"!==u&&+l)&&te.exec(w.css(e,t));if(c&&c[3]!==u){for(l/=2,u=u||c[3],c=+l||1;s--;)w.style(e,t,c+u),(1-r)*(1-(r=a()/l||.5))<=0&&(s=0),c/=r;c*=2,w.style(e,t,c+u),i=i||[]}return i&&(c=+c||+l||0,o=i[1]?c+(i[1]+1)*i[2]:+i[2],n&&(n.unit=u,n.start=c,n.end=o)),o}var le={};function ue(e,t){for(var i,n,o,r,s,a,l,u=[],c=0,h=e.length;c<h;c++)(n=e[c]).style&&(i=n.style.display,t?("none"===i&&(u[c]=Y.get(n,"display")||null,u[c]||(n.style.display="")),""===n.style.display&&se(n)&&(u[c]=(l=s=r=void 0,s=(o=n).ownerDocument,a=o.nodeName,(l=le[a])||(r=s.body.appendChild(s.createElement(a)),l=w.css(r,"display"),r.parentNode.removeChild(r),"none"===l&&(l="block"),le[a]=l)))):"none"!==i&&(u[c]="none",Y.set(n,"display",i)));for(c=0;c<h;c++)null!=u[c]&&(e[c].style.display=u[c]);return e}w.fn.extend({show:function(){return ue(this,!0)},hide:function(){return ue(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){se(this)?w(this).show():w(this).hide()})}});var ce,he,de=/^(?:checkbox|radio)$/i,fe=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,pe=/^$|^module$|\/(?:java|ecma)script/i;ce=m.createDocumentFragment().appendChild(m.createElement("div")),(he=m.createElement("input")).setAttribute("type","radio"),he.setAttribute("checked","checked"),he.setAttribute("name","t"),ce.appendChild(he),f.checkClone=ce.cloneNode(!0).cloneNode(!0).lastChild.checked,ce.innerHTML="<textarea>x</textarea>",f.noCloneChecked=!!ce.cloneNode(!0).lastChild.defaultValue,ce.innerHTML="<option></option>",f.option=!!ce.lastChild;var ge={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function me(e,t){var i;return i=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&k(e,t)?w.merge([e],i):i}function ve(e,t){for(var i=0,n=e.length;i<n;i++)Y.set(e[i],"globalEval",!t||Y.get(t[i],"globalEval"))}ge.tbody=ge.tfoot=ge.colgroup=ge.caption=ge.thead,ge.th=ge.td,f.option||(ge.optgroup=ge.option=[1,"<select multiple='multiple'>","</select>"]);var ye=/<|&#?\w+;/;function be(e,t,i,n,o){for(var r,s,a,l,u,c,h=t.createDocumentFragment(),d=[],f=0,p=e.length;f<p;f++)if((r=e[f])||0===r)if("object"===b(r))w.merge(d,r.nodeType?[r]:r);else if(ye.test(r)){for(s=s||h.appendChild(t.createElement("div")),a=(fe.exec(r)||["",""])[1].toLowerCase(),l=ge[a]||ge._default,s.innerHTML=l[1]+w.htmlPrefilter(r)+l[2],c=l[0];c--;)s=s.lastChild;w.merge(d,s.childNodes),(s=h.firstChild).textContent=""}else d.push(t.createTextNode(r));for(h.textContent="",f=0;r=d[f++];)if(n&&-1<w.inArray(r,n))o&&o.push(r);else if(u=oe(r),s=me(h.appendChild(r),"script"),u&&ve(s),i)for(c=0;r=s[c++];)pe.test(r.type||"")&&i.push(r);return h}var xe=/^([^.]*)(?:\.(.+)|)/;function we(){return!0}function Ee(){return!1}function Se(e,t){return e===function(){try{return m.activeElement}catch(e){}}()==("focus"===t)}function Ce(e,t,i,n,o,r){var s,a;if("object"==typeof t){for(a in"string"!=typeof i&&(n=n||i,i=void 0),t)Ce(e,a,i,n,t[a],r);return e}if(null==n&&null==o?(o=i,n=i=void 0):null==o&&("string"==typeof i?(o=n,n=void 0):(o=n,n=i,i=void 0)),!1===o)o=Ee;else if(!o)return e;return 1===r&&(s=o,(o=function(e){return w().off(e),s.apply(this,arguments)}).guid=s.guid||(s.guid=w.guid++)),e.each(function(){w.event.add(this,t,o,n,i)})}function Te(e,t,i){i?(Y.set(e,t,!1),w.event.add(e,t,{namespace:!1,handler:function(e){var n,r,s=Y.get(this,t);if(1&e.isTrigger&&this[t]){if(s.length)(w.event.special[t]||{}).delegateType&&e.stopPropagation();else if(s=o.call(arguments),Y.set(this,t,s),n=i(this,t),this[t](),s!==(r=Y.get(this,t))||n?Y.set(this,t,!1):r={},s!==r)return e.stopImmediatePropagation(),e.preventDefault(),r&&r.value}else s.length&&(Y.set(this,t,{value:w.event.trigger(w.extend(s[0],w.Event.prototype),s.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===Y.get(e,t)&&w.event.add(e,t,we)}w.event={global:{},add:function(e,t,i,n,o){var r,s,a,l,u,c,h,d,f,p,g,m=Y.get(e);if(G(e))for(i.handler&&(i=(r=i).handler,o=r.selector),o&&w.find.matchesSelector(ne,o),i.guid||(i.guid=w.guid++),(l=m.events)||(l=m.events=Object.create(null)),(s=m.handle)||(s=m.handle=function(t){return void 0!==w&&w.event.triggered!==t.type?w.event.dispatch.apply(e,arguments):void 0}),u=(t=(t||"").match(B)||[""]).length;u--;)f=g=(a=xe.exec(t[u])||[])[1],p=(a[2]||"").split(".").sort(),f&&(h=w.event.special[f]||{},f=(o?h.delegateType:h.bindType)||f,h=w.event.special[f]||{},c=w.extend({type:f,origType:g,data:n,handler:i,guid:i.guid,selector:o,needsContext:o&&w.expr.match.needsContext.test(o),namespace:p.join(".")},r),(d=l[f])||((d=l[f]=[]).delegateCount=0,h.setup&&!1!==h.setup.call(e,n,p,s)||e.addEventListener&&e.addEventListener(f,s)),h.add&&(h.add.call(e,c),c.handler.guid||(c.handler.guid=i.guid)),o?d.splice(d.delegateCount++,0,c):d.push(c),w.event.global[f]=!0)},remove:function(e,t,i,n,o){var r,s,a,l,u,c,h,d,f,p,g,m=Y.hasData(e)&&Y.get(e);if(m&&(l=m.events)){for(u=(t=(t||"").match(B)||[""]).length;u--;)if(f=g=(a=xe.exec(t[u])||[])[1],p=(a[2]||"").split(".").sort(),f){for(h=w.event.special[f]||{},d=l[f=(n?h.delegateType:h.bindType)||f]||[],a=a[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=r=d.length;r--;)c=d[r],!o&&g!==c.origType||i&&i.guid!==c.guid||a&&!a.test(c.namespace)||n&&n!==c.selector&&("**"!==n||!c.selector)||(d.splice(r,1),c.selector&&d.delegateCount--,h.remove&&h.remove.call(e,c));s&&!d.length&&(h.teardown&&!1!==h.teardown.call(e,p,m.handle)||w.removeEvent(e,f,m.handle),delete l[f])}else for(f in l)w.event.remove(e,f+t[u],i,n,!0);w.isEmptyObject(l)&&Y.remove(e,"handle events")}},dispatch:function(e){var t,i,n,o,r,s,a=new Array(arguments.length),l=w.event.fix(e),u=(Y.get(this,"events")||Object.create(null))[l.type]||[],c=w.event.special[l.type]||{};for(a[0]=l,t=1;t<arguments.length;t++)a[t]=arguments[t];if(l.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,l)){for(s=w.event.handlers.call(this,l,u),t=0;(o=s[t++])&&!l.isPropagationStopped();)for(l.currentTarget=o.elem,i=0;(r=o.handlers[i++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==r.namespace&&!l.rnamespace.test(r.namespace)||(l.handleObj=r,l.data=r.data,void 0!==(n=((w.event.special[r.origType]||{}).handle||r.handler).apply(o.elem,a))&&!1===(l.result=n)&&(l.preventDefault(),l.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,l),l.result}},handlers:function(e,t){var i,n,o,r,s,a=[],l=t.delegateCount,u=e.target;if(l&&u.nodeType&&!("click"===e.type&&1<=e.button))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==e.type||!0!==u.disabled)){for(r=[],s={},i=0;i<l;i++)void 0===s[o=(n=t[i]).selector+" "]&&(s[o]=n.needsContext?-1<w(o,this).index(u):w.find(o,this,null,[u]).length),s[o]&&r.push(n);r.length&&a.push({elem:u,handlers:r})}return u=this,l<t.length&&a.push({elem:u,handlers:t.slice(l)}),a},addProp:function(e,t){Object.defineProperty(w.Event.prototype,e,{enumerable:!0,configurable:!0,get:p(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[w.expando]?e:new w.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return de.test(t.type)&&t.click&&k(t,"input")&&Te(t,"click",we),!1},trigger:function(e){var t=this||e;return de.test(t.type)&&t.click&&k(t,"input")&&Te(t,"click"),!0},_default:function(e){var t=e.target;return de.test(t.type)&&t.click&&k(t,"input")&&Y.get(t,"click")||k(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},w.removeEvent=function(e,t,i){e.removeEventListener&&e.removeEventListener(t,i)},w.Event=function(e,t){if(!(this instanceof w.Event))return new w.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?we:Ee,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&w.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[w.expando]=!0},w.Event.prototype={constructor:w.Event,isDefaultPrevented:Ee,isPropagationStopped:Ee,isImmediatePropagationStopped:Ee,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=we,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=we,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=we,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},w.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},w.event.addProp),w.each({focus:"focusin",blur:"focusout"},function(e,t){w.event.special[e]={setup:function(){return Te(this,e,Se),!1},trigger:function(){return Te(this,e),!0},_default:function(){return!0},delegateType:t}}),w.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){w.event.special[e]={delegateType:t,bindType:t,handle:function(e){var i,n=e.relatedTarget,o=e.handleObj;return n&&(n===this||w.contains(this,n))||(e.type=o.origType,i=o.handler.apply(this,arguments),e.type=t),i}}}),w.fn.extend({on:function(e,t,i,n){return Ce(this,e,t,i,n)},one:function(e,t,i,n){return Ce(this,e,t,i,n,1)},off:function(e,t,i){var n,o;if(e&&e.preventDefault&&e.handleObj)return n=e.handleObj,w(e.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler),this;if("object"==typeof e){for(o in e)this.off(o,t,e[o]);return this}return!1!==t&&"function"!=typeof t||(i=t,t=void 0),!1===i&&(i=Ee),this.each(function(){w.event.remove(this,e,i,t)})}});var _e=/<script|<style|<link/i,ke=/checked\s*(?:[^=]|=\s*.checked.)/i,Ie=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function De(e,t){return k(e,"table")&&k(11!==t.nodeType?t:t.firstChild,"tr")&&w(e).children("tbody")[0]||e}function Pe(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Le(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Ae(e,t){var i,n,o,r,s,a;if(1===t.nodeType){if(Y.hasData(e)&&(a=Y.get(e).events))for(o in Y.remove(t,"handle events"),a)for(i=0,n=a[o].length;i<n;i++)w.event.add(t,o,a[o][i]);Q.hasData(e)&&(r=Q.access(e),s=w.extend({},r),Q.set(t,s))}}function ze(e,t,i,n){t=r(t);var o,s,a,l,u,c,h=0,d=e.length,g=d-1,m=t[0],v=p(m);if(v||1<d&&"string"==typeof m&&!f.checkClone&&ke.test(m))return e.each(function(o){var r=e.eq(o);v&&(t[0]=m.call(this,o,r.html())),ze(r,t,i,n)});if(d&&(s=(o=be(t,e[0].ownerDocument,!1,e,n)).firstChild,1===o.childNodes.length&&(o=s),s||n)){for(l=(a=w.map(me(o,"script"),Pe)).length;h<d;h++)u=o,h!==g&&(u=w.clone(u,!0,!0),l&&w.merge(a,me(u,"script"))),i.call(e[h],u,h);if(l)for(c=a[a.length-1].ownerDocument,w.map(a,Le),h=0;h<l;h++)u=a[h],pe.test(u.type||"")&&!Y.access(u,"globalEval")&&w.contains(c,u)&&(u.src&&"module"!==(u.type||"").toLowerCase()?w._evalUrl&&!u.noModule&&w._evalUrl(u.src,{nonce:u.nonce||u.getAttribute("nonce")},c):y(u.textContent.replace(Ie,""),u,c))}return e}function Ne(e,t,i){for(var n,o=t?w.filter(t,e):e,r=0;null!=(n=o[r]);r++)i||1!==n.nodeType||w.cleanData(me(n)),n.parentNode&&(i&&oe(n)&&ve(me(n,"script")),n.parentNode.removeChild(n));return e}w.extend({htmlPrefilter:function(e){return e},clone:function(e,t,i){var n,o,r,s,a,l,u,c=e.cloneNode(!0),h=oe(e);if(!(f.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||w.isXMLDoc(e)))for(s=me(c),n=0,o=(r=me(e)).length;n<o;n++)a=r[n],"input"===(u=(l=s[n]).nodeName.toLowerCase())&&de.test(a.type)?l.checked=a.checked:"input"!==u&&"textarea"!==u||(l.defaultValue=a.defaultValue);if(t)if(i)for(r=r||me(e),s=s||me(c),n=0,o=r.length;n<o;n++)Ae(r[n],s[n]);else Ae(e,c);return 0<(s=me(c,"script")).length&&ve(s,!h&&me(e,"script")),c},cleanData:function(e){for(var t,i,n,o=w.event.special,r=0;void 0!==(i=e[r]);r++)if(G(i)){if(t=i[Y.expando]){if(t.events)for(n in t.events)o[n]?w.event.remove(i,n):w.removeEvent(i,n,t.handle);i[Y.expando]=void 0}i[Q.expando]&&(i[Q.expando]=void 0)}}}),w.fn.extend({detach:function(e){return Ne(this,e,!0)},remove:function(e){return Ne(this,e)},text:function(e){return H(this,function(e){return void 0===e?w.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return ze(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||De(this,e).appendChild(e)})},prepend:function(){return ze(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=De(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return ze(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return ze(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(w.cleanData(me(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return w.clone(this,e,t)})},html:function(e){return H(this,function(e){var t=this[0]||{},i=0,n=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!_e.test(e)&&!ge[(fe.exec(e)||["",""])[1].toLowerCase()]){e=w.htmlPrefilter(e);try{for(;i<n;i++)1===(t=this[i]||{}).nodeType&&(w.cleanData(me(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return ze(this,arguments,function(t){var i=this.parentNode;w.inArray(this,e)<0&&(w.cleanData(me(this)),i&&i.replaceChild(t,this))},e)}}),w.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){w.fn[e]=function(e){for(var i,n=[],o=w(e),r=o.length-1,a=0;a<=r;a++)i=a===r?this:this.clone(!0),w(o[a])[t](i),s.apply(n,i.get());return this.pushStack(n)}});var Be=new RegExp("^("+ee+")(?!px)[a-z%]+$","i"),Me=function(t){var i=t.ownerDocument.defaultView;return i&&i.opener||(i=e),i.getComputedStyle(t)},je=function(e,t,i){var n,o,r={};for(o in t)r[o]=e.style[o],e.style[o]=t[o];for(o in n=i.call(e),t)e.style[o]=r[o];return n},We=new RegExp(ie.join("|"),"i");function Oe(e,t,i){var n,o,r,s,a=e.style;return(i=i||Me(e))&&(""!==(s=i.getPropertyValue(t)||i[t])||oe(e)||(s=w.style(e,t)),!f.pixelBoxStyles()&&Be.test(s)&&We.test(t)&&(n=a.width,o=a.minWidth,r=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=i.width,a.width=n,a.minWidth=o,a.maxWidth=r)),void 0!==s?s+"":s}function qe(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function t(){if(c){u.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ne.appendChild(u).appendChild(c);var t=e.getComputedStyle(c);n="1%"!==t.top,l=12===i(t.marginLeft),c.style.right="60%",s=36===i(t.right),o=36===i(t.width),c.style.position="absolute",r=12===i(c.offsetWidth/3),ne.removeChild(u),c=null}}function i(e){return Math.round(parseFloat(e))}var n,o,r,s,a,l,u=m.createElement("div"),c=m.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",f.clearCloneStyle="content-box"===c.style.backgroundClip,w.extend(f,{boxSizingReliable:function(){return t(),o},pixelBoxStyles:function(){return t(),s},pixelPosition:function(){return t(),n},reliableMarginLeft:function(){return t(),l},scrollboxSize:function(){return t(),r},reliableTrDimensions:function(){var t,i,n,o;return null==a&&(t=m.createElement("table"),i=m.createElement("tr"),n=m.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",i.style.cssText="border:1px solid",i.style.height="1px",n.style.height="9px",n.style.display="block",ne.appendChild(t).appendChild(i).appendChild(n),o=e.getComputedStyle(i),a=parseInt(o.height,10)+parseInt(o.borderTopWidth,10)+parseInt(o.borderBottomWidth,10)===i.offsetHeight,ne.removeChild(t)),a}}))}();var $e=["Webkit","Moz","ms"],He=m.createElement("div").style,Re={};function Fe(e){return w.cssProps[e]||Re[e]||(e in He?e:Re[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),i=$e.length;i--;)if((e=$e[i]+t)in He)return e}(e)||e)}var Ue=/^(none|table(?!-c[ea]).+)/,Ve=/^--/,Ge={position:"absolute",visibility:"hidden",display:"block"},Xe={letterSpacing:"0",fontWeight:"400"};function Ye(e,t,i){var n=te.exec(t);return n?Math.max(0,n[2]-(i||0))+(n[3]||"px"):t}function Qe(e,t,i,n,o,r){var s="width"===t?1:0,a=0,l=0;if(i===(n?"border":"content"))return 0;for(;s<4;s+=2)"margin"===i&&(l+=w.css(e,i+ie[s],!0,o)),n?("content"===i&&(l-=w.css(e,"padding"+ie[s],!0,o)),"margin"!==i&&(l-=w.css(e,"border"+ie[s]+"Width",!0,o))):(l+=w.css(e,"padding"+ie[s],!0,o),"padding"!==i?l+=w.css(e,"border"+ie[s]+"Width",!0,o):a+=w.css(e,"border"+ie[s]+"Width",!0,o));return!n&&0<=r&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-r-l-a-.5))||0),l}function Je(e,t,i){var n=Me(e),o=(!f.boxSizingReliable()||i)&&"border-box"===w.css(e,"boxSizing",!1,n),r=o,s=Oe(e,t,n),a="offset"+t[0].toUpperCase()+t.slice(1);if(Be.test(s)){if(!i)return s;s="auto"}return(!f.boxSizingReliable()&&o||!f.reliableTrDimensions()&&k(e,"tr")||"auto"===s||!parseFloat(s)&&"inline"===w.css(e,"display",!1,n))&&e.getClientRects().length&&(o="border-box"===w.css(e,"boxSizing",!1,n),(r=a in e)&&(s=e[a])),(s=parseFloat(s)||0)+Qe(e,t,i||(o?"border":"content"),r,n,s)+"px"}function Ze(e,t,i,n,o){return new Ze.prototype.init(e,t,i,n,o)}w.extend({cssHooks:{opacity:{get:function(e,t){if(t){var i=Oe(e,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,i,n){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var o,r,s,a=V(t),l=Ve.test(t),u=e.style;if(l||(t=Fe(a)),s=w.cssHooks[t]||w.cssHooks[a],void 0===i)return s&&"get"in s&&void 0!==(o=s.get(e,!1,n))?o:u[t];"string"==(r=typeof i)&&(o=te.exec(i))&&o[1]&&(i=ae(e,t,o),r="number"),null!=i&&i==i&&("number"!==r||l||(i+=o&&o[3]||(w.cssNumber[a]?"":"px")),f.clearCloneStyle||""!==i||0!==t.indexOf("background")||(u[t]="inherit"),s&&"set"in s&&void 0===(i=s.set(e,i,n))||(l?u.setProperty(t,i):u[t]=i))}},css:function(e,t,i,n){var o,r,s,a=V(t);return Ve.test(t)||(t=Fe(a)),(s=w.cssHooks[t]||w.cssHooks[a])&&"get"in s&&(o=s.get(e,!0,i)),void 0===o&&(o=Oe(e,t,n)),"normal"===o&&t in Xe&&(o=Xe[t]),""===i||i?(r=parseFloat(o),!0===i||isFinite(r)?r||0:o):o}}),w.each(["height","width"],function(e,t){w.cssHooks[t]={get:function(e,i,n){if(i)return!Ue.test(w.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?Je(e,t,n):je(e,Ge,function(){return Je(e,t,n)})},set:function(e,i,n){var o,r=Me(e),s=!f.scrollboxSize()&&"absolute"===r.position,a=(s||n)&&"border-box"===w.css(e,"boxSizing",!1,r),l=n?Qe(e,t,n,a,r):0;return a&&s&&(l-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(r[t])-Qe(e,t,"border",!1,r)-.5)),l&&(o=te.exec(i))&&"px"!==(o[3]||"px")&&(e.style[t]=i,i=w.css(e,t)),Ye(0,i,l)}}}),w.cssHooks.marginLeft=qe(f.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Oe(e,"marginLeft"))||e.getBoundingClientRect().left-je(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),w.each({margin:"",padding:"",border:"Width"},function(e,t){w.cssHooks[e+t]={expand:function(i){for(var n=0,o={},r="string"==typeof i?i.split(" "):[i];n<4;n++)o[e+ie[n]+t]=r[n]||r[n-2]||r[0];return o}},"margin"!==e&&(w.cssHooks[e+t].set=Ye)}),w.fn.extend({css:function(e,t){return H(this,function(e,t,i){var n,o,r={},s=0;if(Array.isArray(t)){for(n=Me(e),o=t.length;s<o;s++)r[t[s]]=w.css(e,t[s],!1,n);return r}return void 0!==i?w.style(e,t,i):w.css(e,t)},e,t,1<arguments.length)}}),((w.Tween=Ze).prototype={constructor:Ze,init:function(e,t,i,n,o,r){this.elem=e,this.prop=i,this.easing=o||w.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=n,this.unit=r||(w.cssNumber[i]?"":"px")},cur:function(){var e=Ze.propHooks[this.prop];return e&&e.get?e.get(this):Ze.propHooks._default.get(this)},run:function(e){var t,i=Ze.propHooks[this.prop];return this.options.duration?this.pos=t=w.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):Ze.propHooks._default.set(this),this}}).init.prototype=Ze.prototype,(Ze.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=w.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){w.fx.step[e.prop]?w.fx.step[e.prop](e):1!==e.elem.nodeType||!w.cssHooks[e.prop]&&null==e.elem.style[Fe(e.prop)]?e.elem[e.prop]=e.now:w.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=Ze.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},w.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},w.fx=Ze.prototype.init,w.fx.step={};var Ke,et,tt,it,nt=/^(?:toggle|show|hide)$/,ot=/queueHooks$/;function rt(){et&&(!1===m.hidden&&e.requestAnimationFrame?e.requestAnimationFrame(rt):e.setTimeout(rt,w.fx.interval),w.fx.tick())}function st(){return e.setTimeout(function(){Ke=void 0}),Ke=Date.now()}function at(e,t){var i,n=0,o={height:e};for(t=t?1:0;n<4;n+=2-t)o["margin"+(i=ie[n])]=o["padding"+i]=e;return t&&(o.opacity=o.width=e),o}function lt(e,t,i){for(var n,o=(ut.tweeners[t]||[]).concat(ut.tweeners["*"]),r=0,s=o.length;r<s;r++)if(n=o[r].call(i,t,e))return n}function ut(e,t,i){var n,o,r=0,s=ut.prefilters.length,a=w.Deferred().always(function(){delete l.elem}),l=function(){if(o)return!1;for(var t=Ke||st(),i=Math.max(0,u.startTime+u.duration-t),n=1-(i/u.duration||0),r=0,s=u.tweens.length;r<s;r++)u.tweens[r].run(n);return a.notifyWith(e,[u,n,i]),n<1&&s?i:(s||a.notifyWith(e,[u,1,0]),a.resolveWith(e,[u]),!1)},u=a.promise({elem:e,props:w.extend({},t),opts:w.extend(!0,{specialEasing:{},easing:w.easing._default},i),originalProperties:t,originalOptions:i,startTime:Ke||st(),duration:i.duration,tweens:[],createTween:function(t,i){var n=w.Tween(e,u.opts,t,i,u.opts.specialEasing[t]||u.opts.easing);return u.tweens.push(n),n},stop:function(t){var i=0,n=t?u.tweens.length:0;if(o)return this;for(o=!0;i<n;i++)u.tweens[i].run(1);return t?(a.notifyWith(e,[u,1,0]),a.resolveWith(e,[u,t])):a.rejectWith(e,[u,t]),this}}),c=u.props;for(function(e,t){var i,n,o,r,s;for(i in e)if(o=t[n=V(i)],r=e[i],Array.isArray(r)&&(o=r[1],r=e[i]=r[0]),i!==n&&(e[n]=r,delete e[i]),(s=w.cssHooks[n])&&"expand"in s)for(i in r=s.expand(r),delete e[n],r)i in e||(e[i]=r[i],t[i]=o);else t[n]=o}(c,u.opts.specialEasing);r<s;r++)if(n=ut.prefilters[r].call(u,e,c,u.opts))return p(n.stop)&&(w._queueHooks(u.elem,u.opts.queue).stop=n.stop.bind(n)),n;return w.map(c,lt,u),p(u.opts.start)&&u.opts.start.call(e,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),w.fx.timer(w.extend(l,{elem:e,anim:u,queue:u.opts.queue})),u}w.Animation=w.extend(ut,{tweeners:{"*":[function(e,t){var i=this.createTween(e,t);return ae(i.elem,e,te.exec(t),i),i}]},tweener:function(e,t){p(e)?(t=e,e=["*"]):e=e.match(B);for(var i,n=0,o=e.length;n<o;n++)i=e[n],ut.tweeners[i]=ut.tweeners[i]||[],ut.tweeners[i].unshift(t)},prefilters:[function(e,t,i){var n,o,r,s,a,l,u,c,h="width"in t||"height"in t,d=this,f={},p=e.style,g=e.nodeType&&se(e),m=Y.get(e,"fxshow");for(n in i.queue||(null==(s=w._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,d.always(function(){d.always(function(){s.unqueued--,w.queue(e,"fx").length||s.empty.fire()})})),t)if(o=t[n],nt.test(o)){if(delete t[n],r=r||"toggle"===o,o===(g?"hide":"show")){if("show"!==o||!m||void 0===m[n])continue;g=!0}f[n]=m&&m[n]||w.style(e,n)}if((l=!w.isEmptyObject(t))||!w.isEmptyObject(f))for(n in h&&1===e.nodeType&&(i.overflow=[p.overflow,p.overflowX,p.overflowY],null==(u=m&&m.display)&&(u=Y.get(e,"display")),"none"===(c=w.css(e,"display"))&&(u?c=u:(ue([e],!0),u=e.style.display||u,c=w.css(e,"display"),ue([e]))),("inline"===c||"inline-block"===c&&null!=u)&&"none"===w.css(e,"float")&&(l||(d.done(function(){p.display=u}),null==u&&(c=p.display,u="none"===c?"":c)),p.display="inline-block")),i.overflow&&(p.overflow="hidden",d.always(function(){p.overflow=i.overflow[0],p.overflowX=i.overflow[1],p.overflowY=i.overflow[2]})),l=!1,f)l||(m?"hidden"in m&&(g=m.hidden):m=Y.access(e,"fxshow",{display:u}),r&&(m.hidden=!g),g&&ue([e],!0),d.done(function(){for(n in g||ue([e]),Y.remove(e,"fxshow"),f)w.style(e,n,f[n])})),l=lt(g?m[n]:0,n,d),n in m||(m[n]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?ut.prefilters.unshift(e):ut.prefilters.push(e)}}),w.speed=function(e,t,i){var n=e&&"object"==typeof e?w.extend({},e):{complete:i||!i&&t||p(e)&&e,duration:e,easing:i&&t||t&&!p(t)&&t};return w.fx.off?n.duration=0:"number"!=typeof n.duration&&(n.duration in w.fx.speeds?n.duration=w.fx.speeds[n.duration]:n.duration=w.fx.speeds._default),null!=n.queue&&!0!==n.queue||(n.queue="fx"),n.old=n.complete,n.complete=function(){p(n.old)&&n.old.call(this),n.queue&&w.dequeue(this,n.queue)},n},w.fn.extend({fadeTo:function(e,t,i,n){return this.filter(se).css("opacity",0).show().end().animate({opacity:t},e,i,n)},animate:function(e,t,i,n){var o=w.isEmptyObject(e),r=w.speed(t,i,n),s=function(){var t=ut(this,w.extend({},e),r);(o||Y.get(this,"finish"))&&t.stop(!0)};return s.finish=s,o||!1===r.queue?this.each(s):this.queue(r.queue,s)},stop:function(e,t,i){var n=function(e){var t=e.stop;delete e.stop,t(i)};return"string"!=typeof e&&(i=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each(function(){var t=!0,o=null!=e&&e+"queueHooks",r=w.timers,s=Y.get(this);if(o)s[o]&&s[o].stop&&n(s[o]);else for(o in s)s[o]&&s[o].stop&&ot.test(o)&&n(s[o]);for(o=r.length;o--;)r[o].elem!==this||null!=e&&r[o].queue!==e||(r[o].anim.stop(i),t=!1,r.splice(o,1));!t&&i||w.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,i=Y.get(this),n=i[e+"queue"],o=i[e+"queueHooks"],r=w.timers,s=n?n.length:0;for(i.finish=!0,w.queue(this,e,[]),o&&o.stop&&o.stop.call(this,!0),t=r.length;t--;)r[t].elem===this&&r[t].queue===e&&(r[t].anim.stop(!0),r.splice(t,1));for(t=0;t<s;t++)n[t]&&n[t].finish&&n[t].finish.call(this);delete i.finish})}}),w.each(["toggle","show","hide"],function(e,t){var i=w.fn[t];w.fn[t]=function(e,n,o){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(at(t,!0),e,n,o)}}),w.each({slideDown:at("show"),slideUp:at("hide"),slideToggle:at("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){w.fn[e]=function(e,i,n){return this.animate(t,e,i,n)}}),w.timers=[],w.fx.tick=function(){var e,t=0,i=w.timers;for(Ke=Date.now();t<i.length;t++)(e=i[t])()||i[t]!==e||i.splice(t--,1);i.length||w.fx.stop(),Ke=void 0},w.fx.timer=function(e){w.timers.push(e),w.fx.start()},w.fx.interval=13,w.fx.start=function(){et||(et=!0,rt())},w.fx.stop=function(){et=null},w.fx.speeds={slow:600,fast:200,_default:400},w.fn.delay=function(t,i){return t=w.fx&&w.fx.speeds[t]||t,i=i||"fx",this.queue(i,function(i,n){var o=e.setTimeout(i,t);n.stop=function(){e.clearTimeout(o)}})},tt=m.createElement("input"),it=m.createElement("select").appendChild(m.createElement("option")),tt.type="checkbox",f.checkOn=""!==tt.value,f.optSelected=it.selected,(tt=m.createElement("input")).value="t",tt.type="radio",f.radioValue="t"===tt.value;var ct,ht=w.expr.attrHandle;w.fn.extend({attr:function(e,t){return H(this,w.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){w.removeAttr(this,e)})}}),w.extend({attr:function(e,t,i){var n,o,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return void 0===e.getAttribute?w.prop(e,t,i):(1===r&&w.isXMLDoc(e)||(o=w.attrHooks[t.toLowerCase()]||(w.expr.match.bool.test(t)?ct:void 0)),void 0!==i?null===i?void w.removeAttr(e,t):o&&"set"in o&&void 0!==(n=o.set(e,i,t))?n:(e.setAttribute(t,i+""),i):o&&"get"in o&&null!==(n=o.get(e,t))?n:null==(n=w.find.attr(e,t))?void 0:n)},attrHooks:{type:{set:function(e,t){if(!f.radioValue&&"radio"===t&&k(e,"input")){var i=e.value;return e.setAttribute("type",t),i&&(e.value=i),t}}}},removeAttr:function(e,t){var i,n=0,o=t&&t.match(B);if(o&&1===e.nodeType)for(;i=o[n++];)e.removeAttribute(i)}}),ct={set:function(e,t,i){return!1===t?w.removeAttr(e,i):e.setAttribute(i,i),i}},w.each(w.expr.match.bool.source.match(/\w+/g),function(e,t){var i=ht[t]||w.find.attr;ht[t]=function(e,t,n){var o,r,s=t.toLowerCase();return n||(r=ht[s],ht[s]=o,o=null!=i(e,t,n)?s:null,ht[s]=r),o}});var dt=/^(?:input|select|textarea|button)$/i,ft=/^(?:a|area)$/i;function pt(e){return(e.match(B)||[]).join(" ")}function gt(e){return e.getAttribute&&e.getAttribute("class")||""}function mt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(B)||[]}w.fn.extend({prop:function(e,t){return H(this,w.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[w.propFix[e]||e]})}}),w.extend({prop:function(e,t,i){var n,o,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return 1===r&&w.isXMLDoc(e)||(t=w.propFix[t]||t,o=w.propHooks[t]),void 0!==i?o&&"set"in o&&void 0!==(n=o.set(e,i,t))?n:e[t]=i:o&&"get"in o&&null!==(n=o.get(e,t))?n:e[t]},propHooks:{tabIndex:{get:function(e){var t=w.find.attr(e,"tabindex");return t?parseInt(t,10):dt.test(e.nodeName)||ft.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),f.optSelected||(w.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),w.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){w.propFix[this.toLowerCase()]=this}),w.fn.extend({addClass:function(e){var t,i,n,o,r,s,a,l=0;if(p(e))return this.each(function(t){w(this).addClass(e.call(this,t,gt(this)))});if((t=mt(e)).length)for(;i=this[l++];)if(o=gt(i),n=1===i.nodeType&&" "+pt(o)+" "){for(s=0;r=t[s++];)n.indexOf(" "+r+" ")<0&&(n+=r+" ");o!==(a=pt(n))&&i.setAttribute("class",a)}return this},removeClass:function(e){var t,i,n,o,r,s,a,l=0;if(p(e))return this.each(function(t){w(this).removeClass(e.call(this,t,gt(this)))});if(!arguments.length)return this.attr("class","");if((t=mt(e)).length)for(;i=this[l++];)if(o=gt(i),n=1===i.nodeType&&" "+pt(o)+" "){for(s=0;r=t[s++];)for(;-1<n.indexOf(" "+r+" ");)n=n.replace(" "+r+" "," ");o!==(a=pt(n))&&i.setAttribute("class",a)}return this},toggleClass:function(e,t){var i=typeof e,n="string"===i||Array.isArray(e);return"boolean"==typeof t&&n?t?this.addClass(e):this.removeClass(e):p(e)?this.each(function(i){w(this).toggleClass(e.call(this,i,gt(this),t),t)}):this.each(function(){var t,o,r,s;if(n)for(o=0,r=w(this),s=mt(e);t=s[o++];)r.hasClass(t)?r.removeClass(t):r.addClass(t);else void 0!==e&&"boolean"!==i||((t=gt(this))&&Y.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||!1===e?"":Y.get(this,"__className__")||""))})},hasClass:function(e){var t,i,n=0;for(t=" "+e+" ";i=this[n++];)if(1===i.nodeType&&-1<(" "+pt(gt(i))+" ").indexOf(t))return!0;return!1}});var vt=/\r/g;w.fn.extend({val:function(e){var t,i,n,o=this[0];return arguments.length?(n=p(e),this.each(function(i){var o;1===this.nodeType&&(null==(o=n?e.call(this,i,w(this).val()):e)?o="":"number"==typeof o?o+="":Array.isArray(o)&&(o=w.map(o,function(e){return null==e?"":e+""})),(t=w.valHooks[this.type]||w.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,o,"value")||(this.value=o))})):o?(t=w.valHooks[o.type]||w.valHooks[o.nodeName.toLowerCase()])&&"get"in t&&void 0!==(i=t.get(o,"value"))?i:"string"==typeof(i=o.value)?i.replace(vt,""):null==i?"":i:void 0}}),w.extend({valHooks:{option:{get:function(e){var t=w.find.attr(e,"value");return null!=t?t:pt(w.text(e))}},select:{get:function(e){var t,i,n,o=e.options,r=e.selectedIndex,s="select-one"===e.type,a=s?null:[],l=s?r+1:o.length;for(n=r<0?l:s?r:0;n<l;n++)if(((i=o[n]).selected||n===r)&&!i.disabled&&(!i.parentNode.disabled||!k(i.parentNode,"optgroup"))){if(t=w(i).val(),s)return t;a.push(t)}return a},set:function(e,t){for(var i,n,o=e.options,r=w.makeArray(t),s=o.length;s--;)((n=o[s]).selected=-1<w.inArray(w.valHooks.option.get(n),r))&&(i=!0);return i||(e.selectedIndex=-1),r}}}}),w.each(["radio","checkbox"],function(){w.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<w.inArray(w(e).val(),t)}},f.checkOn||(w.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),f.focusin="onfocusin"in e;var yt=/^(?:focusinfocus|focusoutblur)$/,bt=function(e){e.stopPropagation()};w.extend(w.event,{trigger:function(t,i,n,o){var r,s,a,l,u,h,d,f,v=[n||m],y=c.call(t,"type")?t.type:t,b=c.call(t,"namespace")?t.namespace.split("."):[];if(s=f=a=n=n||m,3!==n.nodeType&&8!==n.nodeType&&!yt.test(y+w.event.triggered)&&(-1<y.indexOf(".")&&(y=(b=y.split(".")).shift(),b.sort()),u=y.indexOf(":")<0&&"on"+y,(t=t[w.expando]?t:new w.Event(y,"object"==typeof t&&t)).isTrigger=o?2:3,t.namespace=b.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=n),i=null==i?[t]:w.makeArray(i,[t]),d=w.event.special[y]||{},o||!d.trigger||!1!==d.trigger.apply(n,i))){if(!o&&!d.noBubble&&!g(n)){for(l=d.delegateType||y,yt.test(l+y)||(s=s.parentNode);s;s=s.parentNode)v.push(s),a=s;a===(n.ownerDocument||m)&&v.push(a.defaultView||a.parentWindow||e)}for(r=0;(s=v[r++])&&!t.isPropagationStopped();)f=s,t.type=1<r?l:d.bindType||y,(h=(Y.get(s,"events")||Object.create(null))[t.type]&&Y.get(s,"handle"))&&h.apply(s,i),(h=u&&s[u])&&h.apply&&G(s)&&(t.result=h.apply(s,i),!1===t.result&&t.preventDefault());return t.type=y,o||t.isDefaultPrevented()||d._default&&!1!==d._default.apply(v.pop(),i)||!G(n)||u&&p(n[y])&&!g(n)&&((a=n[u])&&(n[u]=null),w.event.triggered=y,t.isPropagationStopped()&&f.addEventListener(y,bt),n[y](),t.isPropagationStopped()&&f.removeEventListener(y,bt),w.event.triggered=void 0,a&&(n[u]=a)),t.result}},simulate:function(e,t,i){var n=w.extend(new w.Event,i,{type:e,isSimulated:!0});w.event.trigger(n,null,t)}}),w.fn.extend({trigger:function(e,t){return this.each(function(){w.event.trigger(e,t,this)})},triggerHandler:function(e,t){var i=this[0];if(i)return w.event.trigger(e,t,i,!0)}}),f.focusin||w.each({focus:"focusin",blur:"focusout"},function(e,t){var i=function(e){w.event.simulate(t,e.target,w.event.fix(e))};w.event.special[t]={setup:function(){var n=this.ownerDocument||this.document||this,o=Y.access(n,t);o||n.addEventListener(e,i,!0),Y.access(n,t,(o||0)+1)},teardown:function(){var n=this.ownerDocument||this.document||this,o=Y.access(n,t)-1;o?Y.access(n,t,o):(n.removeEventListener(e,i,!0),Y.remove(n,t))}}});var xt=e.location,wt={guid:Date.now()},Et=/\?/;w.parseXML=function(t){var i,n;if(!t||"string"!=typeof t)return null;try{i=(new e.DOMParser).parseFromString(t,"text/xml")}catch(t){}return n=i&&i.getElementsByTagName("parsererror")[0],i&&!n||w.error("Invalid XML: "+(n?w.map(n.childNodes,function(e){return e.textContent}).join("\n"):t)),i};var St=/\[\]$/,Ct=/\r?\n/g,Tt=/^(?:submit|button|image|reset|file)$/i,_t=/^(?:input|select|textarea|keygen)/i;function kt(e,t,i,n){var o;if(Array.isArray(t))w.each(t,function(t,o){i||St.test(e)?n(e,o):kt(e+"["+("object"==typeof o&&null!=o?t:"")+"]",o,i,n)});else if(i||"object"!==b(t))n(e,t);else for(o in t)kt(e+"["+o+"]",t[o],i,n)}w.param=function(e,t){var i,n=[],o=function(e,t){var i=p(t)?t():t;n[n.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==i?"":i)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!w.isPlainObject(e))w.each(e,function(){o(this.name,this.value)});else for(i in e)kt(i,e[i],t,o);return n.join("&")},w.fn.extend({serialize:function(){return w.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=w.prop(this,"elements");return e?w.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!w(this).is(":disabled")&&_t.test(this.nodeName)&&!Tt.test(e)&&(this.checked||!de.test(e))}).map(function(e,t){var i=w(this).val();return null==i?null:Array.isArray(i)?w.map(i,function(e){return{name:t.name,value:e.replace(Ct,"\r\n")}}):{name:t.name,value:i.replace(Ct,"\r\n")}}).get()}});var It=/%20/g,Dt=/#.*$/,Pt=/([?&])_=[^&]*/,Lt=/^(.*?):[ \t]*([^\r\n]*)$/gm,At=/^(?:GET|HEAD)$/,zt=/^\/\//,Nt={},Bt={},Mt="*/".concat("*"),jt=m.createElement("a");function Wt(e){return function(t,i){"string"!=typeof t&&(i=t,t="*");var n,o=0,r=t.toLowerCase().match(B)||[];if(p(i))for(;n=r[o++];)"+"===n[0]?(n=n.slice(1)||"*",(e[n]=e[n]||[]).unshift(i)):(e[n]=e[n]||[]).push(i)}}function Ot(e,t,i,n){var o={},r=e===Bt;function s(a){var l;return o[a]=!0,w.each(e[a]||[],function(e,a){var u=a(t,i,n);return"string"!=typeof u||r||o[u]?r?!(l=u):void 0:(t.dataTypes.unshift(u),s(u),!1)}),l}return s(t.dataTypes[0])||!o["*"]&&s("*")}function qt(e,t){var i,n,o=w.ajaxSettings.flatOptions||{};for(i in t)void 0!==t[i]&&((o[i]?e:n||(n={}))[i]=t[i]);return n&&w.extend(!0,e,n),e}jt.href=xt.href,w.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:xt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(xt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Mt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":w.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?qt(qt(e,w.ajaxSettings),t):qt(w.ajaxSettings,e)},ajaxPrefilter:Wt(Nt),ajaxTransport:Wt(Bt),ajax:function(t,i){"object"==typeof t&&(i=t,t=void 0),i=i||{};var n,o,r,s,a,l,u,c,h,d,f=w.ajaxSetup({},i),p=f.context||f,g=f.context&&(p.nodeType||p.jquery)?w(p):w.event,v=w.Deferred(),y=w.Callbacks("once memory"),b=f.statusCode||{},x={},E={},S="canceled",C={readyState:0,getResponseHeader:function(e){var t;if(u){if(!s)for(s={};t=Lt.exec(r);)s[t[1].toLowerCase()+" "]=(s[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=s[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return u?r:null},setRequestHeader:function(e,t){return null==u&&(e=E[e.toLowerCase()]=E[e.toLowerCase()]||e,x[e]=t),this},overrideMimeType:function(e){return null==u&&(f.mimeType=e),this},statusCode:function(e){var t;if(e)if(u)C.always(e[C.status]);else for(t in e)b[t]=[b[t],e[t]];return this},abort:function(e){var t=e||S;return n&&n.abort(t),T(0,t),this}};if(v.promise(C),f.url=((t||f.url||xt.href)+"").replace(zt,xt.protocol+"//"),f.type=i.method||i.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match(B)||[""],null==f.crossDomain){l=m.createElement("a");try{l.href=f.url,l.href=l.href,f.crossDomain=jt.protocol+"//"+jt.host!=l.protocol+"//"+l.host}catch(t){f.crossDomain=!0}}if(f.data&&f.processData&&"string"!=typeof f.data&&(f.data=w.param(f.data,f.traditional)),Ot(Nt,f,i,C),u)return C;for(h in(c=w.event&&f.global)&&0==w.active++&&w.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!At.test(f.type),o=f.url.replace(Dt,""),f.hasContent?f.data&&f.processData&&0===(f.contentType||"").indexOf("application/x-www-form-urlencoded")&&(f.data=f.data.replace(It,"+")):(d=f.url.slice(o.length),f.data&&(f.processData||"string"==typeof f.data)&&(o+=(Et.test(o)?"&":"?")+f.data,delete f.data),!1===f.cache&&(o=o.replace(Pt,"$1"),d=(Et.test(o)?"&":"?")+"_="+wt.guid+++d),f.url=o+d),f.ifModified&&(w.lastModified[o]&&C.setRequestHeader("If-Modified-Since",w.lastModified[o]),w.etag[o]&&C.setRequestHeader("If-None-Match",w.etag[o])),(f.data&&f.hasContent&&!1!==f.contentType||i.contentType)&&C.setRequestHeader("Content-Type",f.contentType),C.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Mt+"; q=0.01":""):f.accepts["*"]),f.headers)C.setRequestHeader(h,f.headers[h]);if(f.beforeSend&&(!1===f.beforeSend.call(p,C,f)||u))return C.abort();if(S="abort",y.add(f.complete),C.done(f.success),C.fail(f.error),n=Ot(Bt,f,i,C)){if(C.readyState=1,c&&g.trigger("ajaxSend",[C,f]),u)return C;f.async&&0<f.timeout&&(a=e.setTimeout(function(){C.abort("timeout")},f.timeout));try{u=!1,n.send(x,T)}catch(t){if(u)throw t;T(-1,t)}}else T(-1,"No Transport");function T(t,i,s,l){var h,d,m,x,E,S=i;u||(u=!0,a&&e.clearTimeout(a),n=void 0,r=l||"",C.readyState=0<t?4:0,h=200<=t&&t<300||304===t,s&&(x=function(e,t,i){for(var n,o,r,s,a=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===n&&(n=e.mimeType||t.getResponseHeader("Content-Type"));if(n)for(o in a)if(a[o]&&a[o].test(n)){l.unshift(o);break}if(l[0]in i)r=l[0];else{for(o in i){if(!l[0]||e.converters[o+" "+l[0]]){r=o;break}s||(s=o)}r=r||s}if(r)return r!==l[0]&&l.unshift(r),i[r]}(f,C,s)),!h&&-1<w.inArray("script",f.dataTypes)&&w.inArray("json",f.dataTypes)<0&&(f.converters["text script"]=function(){}),x=function(e,t,i,n){var o,r,s,a,l,u={},c=e.dataTypes.slice();if(c[1])for(s in e.converters)u[s.toLowerCase()]=e.converters[s];for(r=c.shift();r;)if(e.responseFields[r]&&(i[e.responseFields[r]]=t),!l&&n&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=r,r=c.shift())if("*"===r)r=l;else if("*"!==l&&l!==r){if(!(s=u[l+" "+r]||u["* "+r]))for(o in u)if((a=o.split(" "))[1]===r&&(s=u[l+" "+a[0]]||u["* "+a[0]])){!0===s?s=u[o]:!0!==u[o]&&(r=a[0],c.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+l+" to "+r}}}return{state:"success",data:t}}(f,x,C,h),h?(f.ifModified&&((E=C.getResponseHeader("Last-Modified"))&&(w.lastModified[o]=E),(E=C.getResponseHeader("etag"))&&(w.etag[o]=E)),204===t||"HEAD"===f.type?S="nocontent":304===t?S="notmodified":(S=x.state,d=x.data,h=!(m=x.error))):(m=S,!t&&S||(S="error",t<0&&(t=0))),C.status=t,C.statusText=(i||S)+"",h?v.resolveWith(p,[d,S,C]):v.rejectWith(p,[C,S,m]),C.statusCode(b),b=void 0,c&&g.trigger(h?"ajaxSuccess":"ajaxError",[C,f,h?d:m]),y.fireWith(p,[C,S]),c&&(g.trigger("ajaxComplete",[C,f]),--w.active||w.event.trigger("ajaxStop")))}return C},getJSON:function(e,t,i){return w.get(e,t,i,"json")},getScript:function(e,t){return w.get(e,void 0,t,"script")}}),w.each(["get","post"],function(e,t){w[t]=function(e,i,n,o){return p(i)&&(o=o||n,n=i,i=void 0),w.ajax(w.extend({url:e,type:t,dataType:o,data:i,success:n},w.isPlainObject(e)&&e))}}),w.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),w._evalUrl=function(e,t,i){return w.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){w.globalEval(e,t,i)}})},w.fn.extend({wrapAll:function(e){var t;return this[0]&&(p(e)&&(e=e.call(this[0])),t=w(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(e){return p(e)?this.each(function(t){w(this).wrapInner(e.call(this,t))}):this.each(function(){var t=w(this),i=t.contents();i.length?i.wrapAll(e):t.append(e)})},wrap:function(e){var t=p(e);return this.each(function(i){w(this).wrapAll(t?e.call(this,i):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){w(this).replaceWith(this.childNodes)}),this}}),w.expr.pseudos.hidden=function(e){return!w.expr.pseudos.visible(e)},w.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},w.ajaxSettings.xhr=function(){try{return new e.XMLHttpRequest}catch(e){}};var $t={0:200,1223:204},Ht=w.ajaxSettings.xhr();f.cors=!!Ht&&"withCredentials"in Ht,f.ajax=Ht=!!Ht,w.ajaxTransport(function(t){var i,n;if(f.cors||Ht&&!t.crossDomain)return{send:function(o,r){var s,a=t.xhr();if(a.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(s in t.xhrFields)a[s]=t.xhrFields[s];for(s in t.mimeType&&a.overrideMimeType&&a.overrideMimeType(t.mimeType),t.crossDomain||o["X-Requested-With"]||(o["X-Requested-With"]="XMLHttpRequest"),o)a.setRequestHeader(s,o[s]);i=function(e){return function(){i&&(i=n=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===e?a.abort():"error"===e?"number"!=typeof a.status?r(0,"error"):r(a.status,a.statusText):r($t[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=i(),n=a.onerror=a.ontimeout=i("error"),void 0!==a.onabort?a.onabort=n:a.onreadystatechange=function(){4===a.readyState&&e.setTimeout(function(){i&&n()})},i=i("abort");try{a.send(t.hasContent&&t.data||null)}catch(o){if(i)throw o}},abort:function(){i&&i()}}}),w.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),w.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return w.globalEval(e),e}}}),w.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),w.ajaxTransport("script",function(e){var t,i;if(e.crossDomain||e.scriptAttrs)return{send:function(n,o){t=w("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",i=function(e){t.remove(),i=null,e&&o("error"===e.type?404:200,e.type)}),m.head.appendChild(t[0])},abort:function(){i&&i()}}});var Rt,Ft=[],Ut=/(=)\?(?=&|$)|\?\?/;w.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Ft.pop()||w.expando+"_"+wt.guid++;return this[e]=!0,e}}),w.ajaxPrefilter("json jsonp",function(t,i,n){var o,r,s,a=!1!==t.jsonp&&(Ut.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ut.test(t.data)&&"data");if(a||"jsonp"===t.dataTypes[0])return o=t.jsonpCallback=p(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,a?t[a]=t[a].replace(Ut,"$1"+o):!1!==t.jsonp&&(t.url+=(Et.test(t.url)?"&":"?")+t.jsonp+"="+o),t.converters["script json"]=function(){return s||w.error(o+" was not called"),s[0]},t.dataTypes[0]="json",r=e[o],e[o]=function(){s=arguments},n.always(function(){void 0===r?w(e).removeProp(o):e[o]=r,t[o]&&(t.jsonpCallback=i.jsonpCallback,Ft.push(o)),s&&p(r)&&r(s[0]),s=r=void 0}),"script"}),f.createHTMLDocument=((Rt=m.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Rt.childNodes.length),w.parseHTML=function(e,t,i){return"string"!=typeof e?[]:("boolean"==typeof t&&(i=t,t=!1),t||(f.createHTMLDocument?((n=(t=m.implementation.createHTMLDocument("")).createElement("base")).href=m.location.href,t.head.appendChild(n)):t=m),r=!i&&[],(o=I.exec(e))?[t.createElement(o[1])]:(o=be([e],t,r),r&&r.length&&w(r).remove(),w.merge([],o.childNodes)));var n,o,r},w.fn.load=function(e,t,i){var n,o,r,s=this,a=e.indexOf(" ");return-1<a&&(n=pt(e.slice(a)),e=e.slice(0,a)),p(t)?(i=t,t=void 0):t&&"object"==typeof t&&(o="POST"),0<s.length&&w.ajax({url:e,type:o||"GET",dataType:"html",data:t}).done(function(e){r=arguments,s.html(n?w("<div>").append(w.parseHTML(e)).find(n):e)}).always(i&&function(e,t){s.each(function(){i.apply(this,r||[e.responseText,t,e])})}),this},w.expr.pseudos.animated=function(e){return w.grep(w.timers,function(t){return e===t.elem}).length},w.offset={setOffset:function(e,t,i){var n,o,r,s,a,l,u=w.css(e,"position"),c=w(e),h={};"static"===u&&(e.style.position="relative"),a=c.offset(),r=w.css(e,"top"),l=w.css(e,"left"),("absolute"===u||"fixed"===u)&&-1<(r+l).indexOf("auto")?(s=(n=c.position()).top,o=n.left):(s=parseFloat(r)||0,o=parseFloat(l)||0),p(t)&&(t=t.call(e,i,w.extend({},a))),null!=t.top&&(h.top=t.top-a.top+s),null!=t.left&&(h.left=t.left-a.left+o),"using"in t?t.using.call(e,h):c.css(h)}},w.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){w.offset.setOffset(this,e,t)});var t,i,n=this[0];return n?n.getClientRects().length?(t=n.getBoundingClientRect(),i=n.ownerDocument.defaultView,{top:t.top+i.pageYOffset,left:t.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,i,n=this[0],o={top:0,left:0};if("fixed"===w.css(n,"position"))t=n.getBoundingClientRect();else{for(t=this.offset(),i=n.ownerDocument,e=n.offsetParent||i.documentElement;e&&(e===i.body||e===i.documentElement)&&"static"===w.css(e,"position");)e=e.parentNode;e&&e!==n&&1===e.nodeType&&((o=w(e).offset()).top+=w.css(e,"borderTopWidth",!0),o.left+=w.css(e,"borderLeftWidth",!0))}return{top:t.top-o.top-w.css(n,"marginTop",!0),left:t.left-o.left-w.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===w.css(e,"position");)e=e.offsetParent;return e||ne})}}),w.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var i="pageYOffset"===t;w.fn[e]=function(n){return H(this,function(e,n,o){var r;if(g(e)?r=e:9===e.nodeType&&(r=e.defaultView),void 0===o)return r?r[t]:e[n];r?r.scrollTo(i?r.pageXOffset:o,i?o:r.pageYOffset):e[n]=o},e,n,arguments.length)}}),w.each(["top","left"],function(e,t){w.cssHooks[t]=qe(f.pixelPosition,function(e,i){if(i)return i=Oe(e,t),Be.test(i)?w(e).position()[t]+"px":i})}),w.each({Height:"height",Width:"width"},function(e,t){w.each({padding:"inner"+e,content:t,"":"outer"+e},function(i,n){w.fn[n]=function(o,r){var s=arguments.length&&(i||"boolean"!=typeof o),a=i||(!0===o||!0===r?"margin":"border");return H(this,function(t,i,o){var r;return g(t)?0===n.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(r=t.documentElement,Math.max(t.body["scroll"+e],r["scroll"+e],t.body["offset"+e],r["offset"+e],r["client"+e])):void 0===o?w.css(t,i,a):w.style(t,i,o,a)},t,s?o:void 0,s)}})}),w.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){w.fn[t]=function(e){return this.on(t,e)}}),w.fn.extend({bind:function(e,t,i){return this.on(e,null,t,i)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,i,n){return this.on(t,e,i,n)},undelegate:function(e,t,i){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",i)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),w.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){w.fn[t]=function(e,i){return 0<arguments.length?this.on(t,null,e,i):this.trigger(t)}});var Vt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;w.proxy=function(e,t){var i,n,r;if("string"==typeof t&&(i=e[t],t=e,e=i),p(e))return n=o.call(arguments,2),(r=function(){return e.apply(t||this,n.concat(o.call(arguments)))}).guid=e.guid=e.guid||w.guid++,r},w.holdReady=function(e){e?w.readyWait++:w.ready(!0)},w.isArray=Array.isArray,w.parseJSON=JSON.parse,w.nodeName=k,w.isFunction=p,w.isWindow=g,w.camelCase=V,w.type=b,w.now=Date.now,w.isNumeric=function(e){var t=w.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},w.trim=function(e){return null==e?"":(e+"").replace(Vt,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return w});var Gt=e.jQuery,Xt=e.$;return w.noConflict=function(t){return e.$===w&&(e.$=Xt),t&&e.jQuery===w&&(e.jQuery=Gt),w},void 0===t&&(e.jQuery=e.$=w),w});var jQuery_BENT=jQuery;!function(e){var t="";screen.width&&(width=screen.width?screen.width:"",height=screen.height?screen.height:"",t+=width+" x "+height);var i,n,o,r=navigator.appVersion,s=navigator.userAgent,a=navigator.appName,l=""+parseFloat(navigator.appVersion),u=parseInt(navigator.appVersion,10);-1!=(n=s.indexOf("Opera"))&&(a="Opera",l=s.substring(n+6),-1!=(n=s.indexOf("Version"))&&(l=s.substring(n+8))),-1!=(n=s.indexOf("OPR"))?(a="Opera",l=s.substring(n+4)):-1!=(n=s.indexOf("Edge"))?(a="Edge",l=s.substring(n+5)):-1!=(n=s.indexOf("MSIE"))?(a="Internet",l=s.substring(n+5)):-1!=(n=s.indexOf("Chrome"))?(a="Chrome",l=s.substring(n+7)):-1!=(n=s.indexOf("Safari"))?(a="Safari",l=s.substring(n+7),-1!=(n=s.indexOf("Version"))&&(l=s.substring(n+8))):-1!=(n=s.indexOf("Firefox"))?(a="Firefox",l=s.substring(n+8)):-1!=s.indexOf("Trident/")?(a="Internet",l=s.substring(s.indexOf("rv:")+3)):(i=s.lastIndexOf(" ")+1)<(n=s.lastIndexOf("/"))&&(a=s.substring(i,n),l=s.substring(n+1),a.toLowerCase()==a.toUpperCase()&&(a=navigator.appName)),-1!=(o=l.indexOf(";"))&&(l=l.substring(0,o)),-1!=(o=l.indexOf(" "))&&(l=l.substring(0,o)),-1!=(o=l.indexOf(")"))&&(l=l.substring(0,o)),u=parseInt(""+l,10),isNaN(u)&&(l=""+parseFloat(navigator.appVersion),u=parseInt(navigator.appVersion,10));var c=/Mobile|mini|Fennec|Android|iP(ad|od|hone)/.test(r),h="-",d=[{s:"Windows 10",r:/(Windows 10.0|Windows NT 10.0)/},{s:"Windows 8.1",r:/(Windows 8.1|Windows NT 6.3)/},{s:"Windows 8",r:/(Windows 8|Windows NT 6.2)/},{s:"Windows 7",r:/(Windows 7|Windows NT 6.1)/},{s:"Windows Vista",r:/Windows NT 6.0/},{s:"Windows Server 2003",r:/Windows NT 5.2/},{s:"Windows XP",r:/(Windows NT 5.1|Windows XP)/},{s:"Windows 2000",r:/(Windows NT 5.0|Windows 2000)/},{s:"Windows ME",r:/(Win 9x 4.90|Windows ME)/},{s:"Windows 98",r:/(Windows 98|Win98)/},{s:"Windows 95",r:/(Windows 95|Win95|Windows_95)/},{s:"Windows NT 4.0",r:/(Windows NT 4.0|WinNBE.0|WinNT|Windows NT)/},{s:"Windows CE",r:/Windows CE/},{s:"Windows 3.11",r:/Win16/},{s:"Android",r:/Android/},{s:"Open BSD",r:/OpenBSD/},{s:"Sun OS",r:/SunOS/},{s:"Linux",r:/(Linux|X11)/},{s:"iOS",r:/(iPhone|iPad|iPod)/},{s:"Mac OS X",r:/Mac OS X/},{s:"Mac OS",r:/(MacPPC|MacIntel|Mac_PowerPC|Macintosh)/},{s:"QNX",r:/QNX/},{s:"UNIX",r:/UNIX/},{s:"BeOS",r:/BeOS/},{s:"OS/2",r:/OS\/2/},{s:"Search Bot",r:/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask Jeeves\/Teoma|ia_archiver)/}];for(var f in d){var p=d[f];if(p.r.test(s)){h=p.s;break}}/Windows/.test(h)&&(h="Windows");var g="no check";if("undefined"!=typeof swfobject){var m=swfobject.getFlashPlayerVersion();g=m.major>0?m.major+"."+m.minor+" r"+m.release:"-"}e.jscd={screen:t,browser:a,browserVersion:l,browserMajorVersion:u,mobile:c,os:h,osVersion:"-",flashVersion:g}}(this),jQuery_BENT("html").addClass("bee-browser-"+jscd.browser+" bee-platform-"+jscd.os);var jsBdBee=document.getElementsByTagName("HTML")[0],RtlBee=jsBdBee.classList.contains("rtl_true"),LtrBee=!RtlBee;!function(e,t){"function"==typeof define&&define.amd?define("jQuery_BENT-bridget/jQuery_BENT-bridget",["jQuery_BENT"],function(i){return t(e,i)}):"object"==typeof module&&module.exports?module.exports=t(e,require("jQuery_BENT")):e.jQuery_BENTBridget=t(e,e.jQuery_BENT)}(window,function(e,t){"use strict";function i(i,r,a){(a=a||t||e.jQuery_BENT)&&(r.prototype.option||(r.prototype.option=function(e){a.isPlainObject(e)&&(this.options=a.extend(!0,this.options,e))}),a.fn[i]=function(e){return"string"==typeof e?function(e,t,n){var o,r="$()."+i+'("'+t+'")';return e.each(function(e,l){var u=a.data(l,i);if(u){var c=u[t];if(c&&"_"!=t.charAt(0)){var h=c.apply(u,n);o=void 0===o?h:o}else s(r+" is not a valid method")}else s(i+" not initialized. Cannot call methods, i.e. "+r)}),void 0!==o?o:e}(this,e,o.call(arguments,1)):(function(e,t){e.each(function(e,n){var o=a.data(n,i);o?(o.option(t),o._init()):(o=new r(n,t),a.data(n,i,o))})}(this,e),this)},n(a))}function n(e){!e||e&&e.bridget||(e.bridget=i)}var o=Array.prototype.slice,r=e.console,s=void 0===r?function(){}:function(e){r.error(e)};return n(t||e.jQuery_BENT),i}),function(e,t){"function"==typeof define&&define.amd?define("ev-emitter/ev-emitter",t):"object"==typeof module&&module.exports?module.exports=t():e.EvEmitter=t()}("undefined"!=typeof window?window:this,function(){function e(){}var t=e.prototype;return t.on=function(e,t){if(e&&t){var i=this._events=this._events||{},n=i[e]=i[e]||[];return-1==n.indexOf(t)&&n.push(t),this}},t.once=function(e,t){if(e&&t){this.on(e,t);var i=this._onceEvents=this._onceEvents||{};return(i[e]=i[e]||{})[t]=!0,this}},t.off=function(e,t){var i=this._events&&this._events[e];if(i&&i.length){var n=i.indexOf(t);return-1!=n&&i.splice(n,1),this}},t.emitEvent=function(e,t){var i=this._events&&this._events[e];if(i&&i.length){i=i.slice(0),t=t||[];for(var n=this._onceEvents&&this._onceEvents[e],o=0;o<i.length;o++){var r=i[o];n&&n[r]&&(this.off(e,r),delete n[r]),r.apply(this,t)}return this}},t.allOff=function(){delete this._events,delete this._onceEvents},e}),function(e,t){"function"==typeof define&&define.amd?define("get-size/get-size",t):"object"==typeof module&&module.exports?module.exports=t():e.getSize=t()}(window,function(){"use strict";function e(e){var t=parseFloat(e);return-1==e.indexOf("%")&&!isNaN(t)&&t}function t(e){var t=getComputedStyle(e);return t||r("Style returned "+t+". Are you running this code in a hidden iframe on Firefox? See https://bit.ly/getsizebug1"),t}function i(){if(!l){l=!0;var i=document.createElement("div");i.style.width="200px",i.style.padding="1px 2px 3px 4px",i.style.borderStyle="solid",i.style.borderWidth="1px 2px 3px 4px",i.style.boxSizing="border-box";var r=document.body||document.documentElement;r.appendChild(i);var s=t(i);o=200==Math.round(e(s.width)),n.isBoxSizeOuter=o,r.removeChild(i)}}function n(n){if(i(),"string"==typeof n&&(n=document.querySelector(n)),n&&"object"==typeof n&&n.nodeType){var r=t(n);if("none"==r.display)return function(){for(var e={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},t=0;t<a;t++)e[s[t]]=0;return e}();var l={};l.width=n.offsetWidth,l.height=n.offsetHeight;for(var u=l.isBorderBox="border-box"==r.boxSizing,c=0;c<a;c++){var h=s[c],d=r[h],f=parseFloat(d);l[h]=isNaN(f)?0:f}var p=l.paddingLeft+l.paddingRight,g=l.paddingTop+l.paddingBottom,m=l.marginLeft+l.marginRight,v=l.marginTop+l.marginBottom,y=l.borderLeftWidth+l.borderRightWidth,b=l.borderTopWidth+l.borderBottomWidth,x=u&&o,w=e(r.width);!1!==w&&(l.width=w+(x?0:p+y));var E=e(r.height);return!1!==E&&(l.height=E+(x?0:g+b)),l.innerWidth=l.width-(p+y),l.innerHeight=l.height-(g+b),l.outerWidth=l.width+m,l.outerHeight=l.height+v,l}}var o,r="undefined"==typeof console?function(){}:function(e){console.error(e)},s=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],a=s.length,l=!1;return n}),function(e,t){"use strict";"function"==typeof define&&define.amd?define("desandro-matches-selector/matches-selector",t):"object"==typeof module&&module.exports?module.exports=t():e.matchesSelector=t()}(window,function(){"use strict";var e=function(){var e=window.Element.prototype;if(e.matches)return"matches";if(e.matchesSelector)return"matchesSelector";for(var t=["webkit","moz","ms","o"],i=0;i<t.length;i++){var n=t[i]+"MatchesSelector";if(e[n])return n}}();return function(t,i){return t[e](i)}}),function(e,t){"function"==typeof define&&define.amd?define("fizzy-ui-utils/utils",["desandro-matches-selector/matches-selector"],function(i){return t(e,i)}):"object"==typeof module&&module.exports?module.exports=t(e,require("desandro-matches-selector")):e.fizzyUIUtils=t(e,e.matchesSelector)}(window,function(e,t){var i={extend:function(e,t){for(var i in t)e[i]=t[i];return e},modulo:function(e,t){return(e%t+t)%t}},n=Array.prototype.slice;i.makeArray=function(e){return Array.isArray(e)?e:null==e?[]:"object"==typeof e&&"number"==typeof e.length?n.call(e):[e]},i.removeFrom=function(e,t){var i=e.indexOf(t);-1!=i&&e.splice(i,1)},i.getParent=function(e,i){for(;e.parentNode&&e!=document.body;)if(e=e.parentNode,t(e,i))return e},i.getQueryElement=function(e){return"string"==typeof e?document.querySelector(e):e},i.handleEvent=function(e){var t="on"+e.type;this[t]&&this[t](e)},i.filterFindElements=function(e,n){e=i.makeArray(e);var o=[];return e.forEach(function(e){if(e instanceof HTMLElement){if(!n)return void o.push(e);t(e,n)&&o.push(e);for(var i=e.querySelectorAll(n),r=0;r<i.length;r++)o.push(i[r])}}),o},i.debounceMethod=function(e,t,i){i=i||100;var n=e.prototype[t],o=t+"Timeout";e.prototype[t]=function(){var e=this[o];clearTimeout(e);var t=arguments,r=this;this[o]=setTimeout(function(){n.apply(r,t),delete r[o]},i)}},i.docReady=function(e){var t=document.readyState;"complete"==t||"interactive"==t?setTimeout(e):document.addEventListener("DOMContentLoaded",e)},i.toDashed=function(e){return e.replace(/(.)([A-Z])/g,function(e,t,i){return t+"-"+i}).toLowerCase()};var o=e.console;return i.htmlInit=function(t,n){i.docReady(function(){var r=i.toDashed(n),s="data-"+r,a=document.querySelectorAll("["+s+"]"),l=document.querySelectorAll(".js-"+r),u=i.makeArray(a).concat(i.makeArray(l)),c=s+"-options",h=e.jQuery_BENT;u.forEach(function(e){var i,r=e.getAttribute(s)||e.getAttribute(c);try{i=r&&JSON.parse(r)}catch(t){return void(o&&o.error("Error parsing "+s+" on "+e.className+": "+t))}var a=new t(e,i);h&&h.data(e,n,a)})})},i}),function(e,t){"function"==typeof define&&define.amd?define("outlayer/item",["ev-emitter/ev-emitter","get-size/get-size"],t):"object"==typeof module&&module.exports?module.exports=t(require("ev-emitter"),require("get-size")):(e.Outlayer={},e.Outlayer.Item=t(e.EvEmitter,e.getSize))}(window,function(e,t){"use strict";function i(e,t){e&&(this.element=e,this.layout=t,this.position={x:0,y:0},this._create())}var n=document.documentElement.style,o="string"==typeof n.transition?"transition":"WebkitTransition",r="string"==typeof n.transform?"transform":"WebkitTransform",s={WebkitTransition:"webkitTransitionEnd",transition:"transitionend"}[o],a={transform:r,transition:o,transitionDuration:o+"Duration",transitionProperty:o+"Property",transitionDelay:o+"Delay"},l=i.prototype=Object.create(e.prototype);l.constructor=i,l._create=function(){this._transn={ingProperties:{},clean:{},onEnd:{}},this.css({position:"absolute"})},l.handleEvent=function(e){var t="on"+e.type;this[t]&&this[t](e)},l.getSize=function(){this.size=t(this.element)},l.css=function(e){var t=this.element.style;for(var i in e)t[a[i]||i]=e[i]},l.getPosition=function(){var e=getComputedStyle(this.element),t=LtrBee,i=this.layout._getOption("originTop"),n=e[t?"left":"right"],o=e[i?"top":"bottom"],r=parseFloat(n),s=parseFloat(o),a=this.layout.size;-1!=n.indexOf("%")&&(r=r/100*a.width),-1!=o.indexOf("%")&&(s=s/100*a.height),r=isNaN(r)?0:r,s=isNaN(s)?0:s,r-=t?a.paddingLeft:a.paddingRight,s-=i?a.paddingTop:a.paddingBottom,this.position.x=r,this.position.y=s},l.layoutPosition=function(){var e=this.layout.size,t={},i=LtrBee,n=this.layout._getOption("originTop"),o=i?"paddingLeft":"paddingRight",r=i?"left":"right",s=i?"right":"left",a=this.position.x+e[o];t[r]=this.getXValue(a),t[s]="";var l=n?"paddingTop":"paddingBottom",u=n?"top":"bottom",c=n?"bottom":"top",h=this.position.y+e[l];t[u]=this.getYValue(h),t[c]="",this.css(t),this.emitEvent("layout",[this])},l.getXValue=function(e){var t=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&!t?e/this.layout.size.width*100+"%":e+"px"},l.getYValue=function(e){var t=this.layout._getOption("horizontal");return this.layout.options.percentPosition&&t?e/this.layout.size.height*100+"%":e+"px"},l._transitionTo=function(e,t){this.getPosition();var i=this.position.x,n=this.position.y,o=e==this.position.x&&t==this.position.y;if(this.setPosition(e,t),!o||this.isTransitioning){var r=e-i,s=t-n,a={};a.transform=this.getTranslate(r,s),this.transition({to:a,onTransitionEnd:{transform:this.layoutPosition},isCleaning:!0})}else this.layoutPosition()},l.getTranslate=function(e,t){return"translate3d("+(e=LtrBee?e:-e)+"px, "+(t=this.layout._getOption("originTop")?t:-t)+"px, 0)"},l.goTo=function(e,t){this.setPosition(e,t),this.layoutPosition()},l.moveTo=l._transitionTo,l.setPosition=function(e,t){this.position.x=parseFloat(e),this.position.y=parseFloat(t)},l._nonTransition=function(e){for(var t in this.css(e.to),e.isCleaning&&this._removeStyles(e.to),e.onTransitionEnd)e.onTransitionEnd[t].call(this)},l.transition=function(e){if(parseFloat(this.layout.options.transitionDuration)){var t=this._transn;for(var i in e.onTransitionEnd)t.onEnd[i]=e.onTransitionEnd[i];for(i in e.to)t.ingProperties[i]=!0,e.isCleaning&&(t.clean[i]=!0);e.from&&(this.css(e.from),this.element.offsetHeight),this.enableTransition(e.to),this.css(e.to),this.isTransitioning=!0}else this._nonTransition(e)};var u="opacity,"+r.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()});l.enableTransition=function(){if(!this.isTransitioning){var e=this.layout.options.transitionDuration;e="number"==typeof e?e+"ms":e,this.css({transitionProperty:u,transitionDuration:e,transitionDelay:this.staggerDelay||0}),this.element.addEventListener(s,this,!1)}},l.onwebkitTransitionEnd=function(e){this.ontransitionend(e)},l.onotransitionend=function(e){this.ontransitionend(e)};var c={"-webkit-transform":"transform"};l.ontransitionend=function(e){if(e.target===this.element){var t=this._transn,i=c[e.propertyName]||e.propertyName;delete t.ingProperties[i],function(e){for(var t in e)return!1;return!0}(t.ingProperties)&&this.disableTransition(),i in t.clean&&(this.element.style[e.propertyName]="",delete t.clean[i]),i in t.onEnd&&(t.onEnd[i].call(this),delete t.onEnd[i]),this.emitEvent("transitionEnd",[this])}},l.disableTransition=function(){this.removeTransitionStyles(),this.element.removeEventListener(s,this,!1),this.isTransitioning=!1},l._removeStyles=function(e){var t={};for(var i in e)t[i]="";this.css(t)};var h={transitionProperty:"",transitionDuration:"",transitionDelay:""};return l.removeTransitionStyles=function(){this.css(h)},l.stagger=function(e){e=isNaN(e)?0:e,this.staggerDelay=e+"ms"},l.removeElem=function(){this.element.parentNode.removeChild(this.element),this.css({display:""}),this.emitEvent("remove",[this])},l.remove=function(){return o&&parseFloat(this.layout.options.transitionDuration)?(this.once("transitionEnd",function(){this.removeElem()}),void this.hide()):void this.removeElem()},l.reveal=function(){delete this.isHidden,this.css({display:""});var e=this.layout.options,t={};t[this.getHideRevealTransitionEndProperty("visibleStyle")]=this.onRevealTransitionEnd,this.transition({from:e.hiddenStyle,to:e.visibleStyle,isCleaning:!0,onTransitionEnd:t})},l.onRevealTransitionEnd=function(){this.isHidden||this.emitEvent("reveal")},l.getHideRevealTransitionEndProperty=function(e){var t=this.layout.options[e];if(t.opacity)return"opacity";for(var i in t)return i},l.hide=function(){this.isHidden=!0,this.css({display:""});var e=this.layout.options,t={};t[this.getHideRevealTransitionEndProperty("hiddenStyle")]=this.onHideTransitionEnd,this.transition({from:e.visibleStyle,to:e.hiddenStyle,isCleaning:!0,onTransitionEnd:t})},l.onHideTransitionEnd=function(){this.isHidden&&(this.css({display:"none"}),this.emitEvent("hide"))},l.destroy=function(){this.css({position:"",left:"",right:"",top:"",bottom:"",transition:"",transform:""})},i}),function(e,t){"use strict";"function"==typeof define&&define.amd?define("outlayer/outlayer",["ev-emitter/ev-emitter","get-size/get-size","fizzy-ui-utils/utils","./item"],function(i,n,o,r){return t(e,i,n,o,r)}):"object"==typeof module&&module.exports?module.exports=t(e,require("ev-emitter"),require("get-size"),require("fizzy-ui-utils"),require("./item")):e.Outlayer=t(e,e.EvEmitter,e.getSize,e.fizzyUIUtils,e.Outlayer.Item)}(window,function(e,t,i,n,o){"use strict";function r(e,t){var i=n.getQueryElement(e);if(i){this.element=i,l&&(this.$element=l(this.element)),this.options=n.extend({},this.constructor.defaults),this.option(t);var o=++c;this.element.outlayerGUID=o,h[o]=this,this._create(),this._getOption("initLayout")&&this.layout()}else a&&a.error("Bad element for "+this.constructor.namespace+": "+(i||e))}function s(e){function t(){e.apply(this,arguments)}return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t}var a=e.console,l=e.jQuery_BENT,u=function(){},c=0,h={};r.namespace="outlayer",r.Item=o,r.defaults={containerStyle:{position:"relative"},initLayout:!0,originLeft:!0,originTop:!0,resize:!0,resizeContainer:!0,transitionDuration:"0.4s",hiddenStyle:{opacity:0,transform:"scale(0.001)"},visibleStyle:{opacity:1,transform:"scale(1)"}};var d=r.prototype;n.extend(d,t.prototype),d.option=function(e){n.extend(this.options,e)},d._getOption=function(e){var t=this.constructor.compatOptions[e];return t&&void 0!==this.options[t]?this.options[t]:this.options[e]},r.compatOptions={initLayout:"isInitLayout",horizontal:"isHorizontal",layoutInstant:"isLayoutInstant",originLeft:"isOriginLeft",originTop:"isOriginTop",resize:"isResizeBound",resizeContainer:"isResizingContainer"},d._create=function(){this.reloadItems(),this.stamps=[],this.stamp(this.options.stamp),n.extend(this.element.style,this.options.containerStyle),this._getOption("resize")&&this.bindResize()},d.reloadItems=function(){this.items=this._itemize(this.element.children)},d._itemize=function(e){for(var t=this._filterFindItemElements(e),i=this.constructor.Item,n=[],o=0;o<t.length;o++){var r=new i(t[o],this);n.push(r)}return n},d._filterFindItemElements=function(e){return n.filterFindElements(e,this.options.itemSelector)},d.getItemElements=function(){return this.items.map(function(e){return e.element})},d.layout=function(){this._resetLayout(),this._manageStamps();var e=this._getOption("layoutInstant"),t=void 0!==e?e:!this._isLayoutInited;this.layoutItems(this.items,t),this._isLayoutInited=!0},d._init=d.layout,d._resetLayout=function(){this.getSize()},d.getSize=function(){this.size=i(this.element)},d._getMeasurement=function(e,t){var n,o=this.options[e];o?("string"==typeof o?n=this.element.querySelector(o):o instanceof HTMLElement&&(n=o),this[e]=n?i(n)[t]:o):this[e]=0},d.layoutItems=function(e,t){e=this._getItemsForLayout(e),this._layoutItems(e,t),this._postLayout()},d._getItemsForLayout=function(e){return e.filter(function(e){return!e.isIgnored})},d._layoutItems=function(e,t){if(this._emitCompleteOnItems("layout",e),e&&e.length){var i=[];e.forEach(function(e){var n=this._getItemLayoutPosition(e);n.item=e,n.isInstant=t||e.isLayoutInstant,i.push(n)},this),this._processLayoutQueue(i)}},d._getItemLayoutPosition=function(){return{x:0,y:0}},d._processLayoutQueue=function(e){this.updateStagger(),e.forEach(function(e,t){this._positionItem(e.item,e.x,e.y,e.isInstant,t)},this)},d.updateStagger=function(){var e=this.options.stagger;return null==e?void(this.stagger=0):(this.stagger=function(e){if("number"==typeof e)return e;var t=e.match(/(^\d*\.?\d*)(\w*)/),i=t&&t[1],n=t&&t[2];return i.length?(i=parseFloat(i))*(f[n]||1):0}(e),this.stagger)},d._positionItem=function(e,t,i,n,o){n?e.goTo(t,i):(e.stagger(o*this.stagger),e.moveTo(t,i))},d._postLayout=function(){this.resizeContainer()},d.resizeContainer=function(){if(this._getOption("resizeContainer")){var e=this._getContainerSize();e&&(this._setContainerMeasure(e.width,!0),this._setContainerMeasure(e.height,!1))}},d._getContainerSize=u,d._setContainerMeasure=function(e,t){if(void 0!==e){var i=this.size;i.isBorderBox&&(e+=t?i.paddingLeft+i.paddingRight+i.borderLeftWidth+i.borderRightWidth:i.paddingBottom+i.paddingTop+i.borderTopWidth+i.borderBottomWidth),e=Math.max(e,0),this.element.style[t?"width":"height"]=e+"px"}},d._emitCompleteOnItems=function(e,t){function i(){o.dispatchEvent(e+"Complete",null,[t])}function n(){++s==r&&i()}var o=this,r=t.length;if(t&&r){var s=0;t.forEach(function(t){t.once(e,n)})}else i()},d.dispatchEvent=function(e,t,i){var n=t?[t].concat(i):i;if(this.emitEvent(e,n),l)if(this.$element=this.$element||l(this.element),t){var o=l.Event(t);o.type=e,this.$element.trigger(o,i)}else this.$element.trigger(e,i)},d.ignore=function(e){var t=this.getItem(e);t&&(t.isIgnored=!0)},d.unignore=function(e){var t=this.getItem(e);t&&delete t.isIgnored},d.stamp=function(e){(e=this._find(e))&&(this.stamps=this.stamps.concat(e),e.forEach(this.ignore,this))},d.unstamp=function(e){(e=this._find(e))&&e.forEach(function(e){n.removeFrom(this.stamps,e),this.unignore(e)},this)},d._find=function(e){if(e)return"string"==typeof e&&(e=this.element.querySelectorAll(e)),n.makeArray(e)},d._manageStamps=function(){this.stamps&&this.stamps.length&&(this._getBoundingRect(),this.stamps.forEach(this._manageStamp,this))},d._getBoundingRect=function(){var e=this.element.getBoundingClientRect(),t=this.size;this._boundingRect={left:e.left+t.paddingLeft+t.borderLeftWidth,top:e.top+t.paddingTop+t.borderTopWidth,right:e.right-(t.paddingRight+t.borderRightWidth),bottom:e.bottom-(t.paddingBottom+t.borderBottomWidth)}},d._manageStamp=u,d._getElementOffset=function(e){var t=e.getBoundingClientRect(),n=this._boundingRect,o=i(e);return{left:t.left-n.left-o.marginLeft,top:t.top-n.top-o.marginTop,right:n.right-t.right-o.marginRight,bottom:n.bottom-t.bottom-o.marginBottom}},d.handleEvent=n.handleEvent,d.bindResize=function(){e.addEventListener("resize",this),this.isResizeBound=!0},d.unbindResize=function(){e.removeEventListener("resize",this),this.isResizeBound=!1},d.onresize=function(){this.resize()},n.debounceMethod(r,"onresize",100),d.resize=function(){this.isResizeBound&&this.needsResizeLayout()&&this.layout()},d.needsResizeLayout=function(){var e=i(this.element);return this.size&&e&&e.innerWidth!==this.size.innerWidth},d.addItems=function(e){var t=this._itemize(e);return t.length&&(this.items=this.items.concat(t)),t},d.appended=function(e){var t=this.addItems(e);t.length&&(this.layoutItems(t,!0),this.reveal(t))},d.prepended=function(e){var t=this._itemize(e);if(t.length){var i=this.items.slice(0);this.items=t.concat(i),this._resetLayout(),this._manageStamps(),this.layoutItems(t,!0),this.reveal(t),this.layoutItems(i)}},d.reveal=function(e){if(this._emitCompleteOnItems("reveal",e),e&&e.length){var t=this.updateStagger();e.forEach(function(e,i){e.stagger(i*t),e.reveal()})}},d.hide=function(e){if(this._emitCompleteOnItems("hide",e),e&&e.length){var t=this.updateStagger();e.forEach(function(e,i){e.stagger(i*t),e.hide()})}},d.revealItemElements=function(e){var t=this.getItems(e);this.reveal(t)},d.hideItemElements=function(e){var t=this.getItems(e);this.hide(t)},d.getItem=function(e){for(var t=0;t<this.items.length;t++){var i=this.items[t];if(i.element==e)return i}},d.getItems=function(e){e=n.makeArray(e);var t=[];return e.forEach(function(e){var i=this.getItem(e);i&&t.push(i)},this),t},d.remove=function(e){var t=this.getItems(e);this._emitCompleteOnItems("remove",t),t&&t.length&&t.forEach(function(e){e.remove(),n.removeFrom(this.items,e)},this)},d.destroy=function(){var e=this.element.style;e.height="",e.position="",e.width="",this.items.forEach(function(e){e.destroy()}),this.unbindResize();var t=this.element.outlayerGUID;delete h[t],delete this.element.outlayerGUID,l&&l.removeData(this.element,this.constructor.namespace)},r.data=function(e){var t=(e=n.getQueryElement(e))&&e.outlayerGUID;return t&&h[t]},r.create=function(e,t){var i=s(r);return i.defaults=n.extend({},r.defaults),n.extend(i.defaults,t),i.compatOptions=n.extend({},r.compatOptions),i.namespace=e,i.data=r.data,i.Item=s(o),n.htmlInit(i,e),l&&l.bridget&&l.bridget(e,i),i};var f={ms:1,s:1e3};return r.Item=o,r}),function(e,t){"function"==typeof define&&define.amd?define("isotopebee-layout/js/item",["outlayer/outlayer"],t):"object"==typeof module&&module.exports?module.exports=t(require("outlayer")):(e.isotopebee=e.isotopebee||{},e.isotopebee.Item=t(e.Outlayer))}(window,function(e){"use strict";function t(){e.Item.apply(this,arguments)}var i=t.prototype=Object.create(e.Item.prototype),n=i._create;i._create=function(){this.id=this.layout.itemGUID++,n.call(this),this.sortData={}},i.updateSortData=function(){if(!this.isIgnored){this.sortData.id=this.id,this.sortData["original-order"]=this.id,this.sortData.random=Math.random();var e=this.layout.options.getSortData,t=this.layout._sorters;for(var i in e){var n=t[i];this.sortData[i]=n(this.element,this)}}};var o=i.destroy;return i.destroy=function(){o.apply(this,arguments),this.css({display:""})},t}),function(e,t){"function"==typeof define&&define.amd?define("isotopebee-layout/js/layout-mode",["get-size/get-size","outlayer/outlayer"],t):"object"==typeof module&&module.exports?module.exports=t(require("get-size"),require("outlayer")):(e.isotopebee=e.isotopebee||{},e.isotopebee.LayoutMode=t(e.getSize,e.Outlayer))}(window,function(e,t){"use strict";function i(e){this.isotopebee=e,e&&(this.options=e.options[this.namespace],this.element=e.element,this.items=e.filteredItems,this.size=e.size)}var n=i.prototype;return["_resetLayout","_getItemLayoutPosition","_manageStamp","_getContainerSize","_getElementOffset","needsResizeLayout","_getOption"].forEach(function(e){n[e]=function(){return t.prototype[e].apply(this.isotopebee,arguments)}}),n.needsVerticalResizeLayout=function(){var t=e(this.isotopebee.element);return this.isotopebee.size&&t&&t.innerHeight!=this.isotopebee.size.innerHeight},n._getMeasurement=function(){this.isotopebee._getMeasurement.apply(this,arguments)},n.getColumnWidth=function(){this.getSegmentSize("column","Width")},n.getRowHeight=function(){this.getSegmentSize("row","Height")},n.getSegmentSize=function(e,t){var i=e+t,n="outer"+t;if(this._getMeasurement(i,n),!this[i]){var o=this.getFirstItemSize();this[i]=o&&o[n]||this.isotopebee.size["inner"+t]}},n.getFirstItemSize=function(){var t=this.isotopebee.filteredItems[0];return t&&t.element&&e(t.element)},n.layout=function(){this.isotopebee.layout.apply(this.isotopebee,arguments)},n.getSize=function(){this.isotopebee.getSize(),this.size=this.isotopebee.size},i.modes={},i.create=function(e,t){function o(){i.apply(this,arguments)}return o.prototype=Object.create(n),o.prototype.constructor=o,t&&(o.options=t),o.prototype.namespace=e,i.modes[e]=o,o},i}),function(e,t){"function"==typeof define&&define.amd?define("masonry-layout/masonry",["outlayer/outlayer","get-size/get-size"],t):"object"==typeof module&&module.exports?module.exports=t(require("outlayer"),require("get-size")):e.Masonry=t(e.Outlayer,e.getSize)}(window,function(e,t){var i=e.create("masonry");i.compatOptions.fitWidth="isFitWidth";var n=i.prototype;return n._resetLayout=function(){this.getSize(),this._getMeasurement("columnWidth","outerWidth"),this._getMeasurement("gutter","outerWidth"),this.measureColumns(),this.colYs=[];for(var e=0;e<this.cols;e++)this.colYs.push(0);this.maxY=0,this.horizontalColIndex=0},n.measureColumns=function(){if(this.getContainerWidth(),!this.columnWidth){var e=this.items[0],i=e&&e.element;this.columnWidth=i&&t(i).outerWidth||this.containerWidth}var n=this.columnWidth+=this.gutter,o=this.containerWidth+this.gutter,r=o/n,s=n-o%n;r=Math[s&&s<1?"round":"floor"](r),this.cols=Math.max(r,1)},n.getContainerWidth=function(){var e=this._getOption("fitWidth")?this.element.parentNode:this.element,i=t(e);this.containerWidth=i&&i.innerWidth},n._getItemLayoutPosition=function(e){e.getSize();var t=e.size.outerWidth%this.columnWidth,i=Math[t&&t<1?"round":"ceil"](e.size.outerWidth/this.columnWidth);i=Math.min(i,this.cols);for(var n=this[this.options.horizontalOrder?"_getHorizontalColPosition":"_getTopColPosition"](i,e),o={x:this.columnWidth*n.col,y:n.y},r=n.y+e.size.outerHeight,s=i+n.col,a=n.col;a<s;a++)this.colYs[a]=r;return o},n._getTopColPosition=function(e){var t=this._getTopColGroup(e),i=Math.min.apply(Math,t);return{col:t.indexOf(i),y:i}},n._getTopColGroup=function(e){if(e<2)return this.colYs;for(var t=[],i=this.cols+1-e,n=0;n<i;n++)t[n]=this._getColGroupY(n,e);return t},n._getColGroupY=function(e,t){if(t<2)return this.colYs[e];var i=this.colYs.slice(e,e+t);return Math.max.apply(Math,i)},n._getHorizontalColPosition=function(e,t){var i=this.horizontalColIndex%this.cols;i=e>1&&i+e>this.cols?0:i;var n=t.size.outerWidth&&t.size.outerHeight;return this.horizontalColIndex=n?i+e:this.horizontalColIndex,{col:i,y:this._getColGroupY(i,e)}},n._manageStamp=function(e){var i=t(e),n=this._getElementOffset(e),o=this._getOption("originLeft")?n.left:n.right,r=o+i.outerWidth,s=Math.floor(o/this.columnWidth);s=Math.max(0,s);var a=Math.floor(r/this.columnWidth);a-=r%this.columnWidth?0:1,a=Math.min(this.cols-1,a);for(var l=(this._getOption("originTop")?n.top:n.bottom)+i.outerHeight,u=s;u<=a;u++)this.colYs[u]=Math.max(l,this.colYs[u])},n._getContainerSize=function(){this.maxY=Math.max.apply(Math,this.colYs);var e={height:this.maxY};return this._getOption("fitWidth")&&(e.width=this._getContainerFitWidth()),e},n._getContainerFitWidth=function(){for(var e=0,t=this.cols;--t&&0===this.colYs[t];)e++;return(this.cols-e)*this.columnWidth-this.gutter},n.needsResizeLayout=function(){var e=this.containerWidth;return this.getContainerWidth(),e!=this.containerWidth},i}),function(e,t){"function"==typeof define&&define.amd?define("isotopebee-layout/js/layout-modes/masonry",["../layout-mode","masonry-layout/masonry"],t):"object"==typeof module&&module.exports?module.exports=t(require("../layout-mode"),require("masonry-layout")):t(e.isotopebee.LayoutMode,e.Masonry)}(window,function(e,t){"use strict";var i=e.create("masonry"),n=i.prototype,o={_getElementOffset:!0,layout:!0,_getMeasurement:!0};for(var r in t.prototype)o[r]||(n[r]=t.prototype[r]);var s=n.measureColumns;n.measureColumns=function(){this.items=this.isotopebee.filteredItems,s.call(this)};var a=n._getOption;return n._getOption=function(e){return"fitWidth"==e?void 0!==this.options.isFitWidth?this.options.isFitWidth:this.options.fitWidth:a.apply(this.isotopebee,arguments)},i}),function(e,t){"function"==typeof define&&define.amd?define("isotopebee-layout/js/layout-modes/fit-rows",["../layout-mode"],t):"object"==typeof exports?module.exports=t(require("../layout-mode")):t(e.isotopebee.LayoutMode)}(window,function(e){"use strict";var t=e.create("fitRows"),i=t.prototype;return i._resetLayout=function(){this.x=0,this.y=0,this.maxY=0,this._getMeasurement("gutter","outerWidth")},i._getItemLayoutPosition=function(e){e.getSize();var t=e.size.outerWidth+this.gutter,i=this.isotopebee.size.innerWidth+this.gutter;0!==this.x&&t+this.x>i&&(this.x=0,this.y=this.maxY);var n={x:this.x,y:this.y};return this.maxY=Math.max(this.maxY,this.y+e.size.outerHeight),this.x+=t,n},i._getContainerSize=function(){return{height:this.maxY}},t}),function(e,t){"function"==typeof define&&define.amd?define("isotopebee-layout/js/layout-modes/vertical",["../layout-mode"],t):"object"==typeof module&&module.exports?module.exports=t(require("../layout-mode")):t(e.isotopebee.LayoutMode)}(window,function(e){"use strict";var t=e.create("vertical",{horizontalAlignment:0}),i=t.prototype;return i._resetLayout=function(){this.y=0},i._getItemLayoutPosition=function(e){e.getSize();var t=(this.isotopebee.size.innerWidth-e.size.outerWidth)*this.options.horizontalAlignment,i=this.y;return this.y+=e.size.outerHeight,{x:t,y:i}},i._getContainerSize=function(){return{height:this.y}},t}),function(e,t){"function"==typeof define&&define.amd?define(["outlayer/outlayer","get-size/get-size","desandro-matches-selector/matches-selector","fizzy-ui-utils/utils","isotopebee-layout/js/item","isotopebee-layout/js/layout-mode","isotopebee-layout/js/layout-modes/masonry","isotopebee-layout/js/layout-modes/fit-rows","isotopebee-layout/js/layout-modes/vertical"],function(i,n,o,r,s,a){return t(e,i,n,o,r,s,a)}):"object"==typeof module&&module.exports?module.exports=t(e,require("outlayer"),require("get-size"),require("desandro-matches-selector"),require("fizzy-ui-utils"),require("isotopebee-layout/js/item"),require("isotopebee-layout/js/layout-mode"),require("isotopebee-layout/js/layout-modes/masonry"),require("isotopebee-layout/js/layout-modes/fit-rows"),require("isotopebee-layout/js/layout-modes/vertical")):e.isotopebee=t(e,e.Outlayer,e.getSize,e.matchesSelector,e.fizzyUIUtils,e.isotopebee.Item,e.isotopebee.LayoutMode)}(window,function(e,t,i,n,o,r,s){var a=e.jQuery_BENT,l=String.prototype.trim?function(e){return e.trim()}:function(e){return e.replace(/^\s+|\s+$/g,"")},u=t.create("isotopebee",{layoutMode:"masonry",isjQuery_BENTFiltering:!0,sortAscending:!0});u.Item=r,u.LayoutMode=s;var c=u.prototype;c._create=function(){for(var e in this.itemGUID=0,this._sorters={},this._getSorters(),t.prototype._create.call(this),this.modes={},this.filteredItems=this.items,this.sortHistory=["original-order"],s.modes)this._initLayoutMode(e)},c.reloadItems=function(){this.itemGUID=0,t.prototype.reloadItems.call(this)},c._itemize=function(){for(var e=t.prototype._itemize.apply(this,arguments),i=0;i<e.length;i++)e[i].id=this.itemGUID++;return this._updateItemsSortData(e),e},c._initLayoutMode=function(e){var t=s.modes[e],i=this.options[e]||{};this.options[e]=t.options?o.extend(t.options,i):i,this.modes[e]=new t(this)},c.layout=function(){return!this._isLayoutInited&&this._getOption("initLayout")?void this.arrange():void this._layout()},c._layout=function(){var e=this._getIsInstant();this._resetLayout(),this._manageStamps(),this.layoutItems(this.filteredItems,e),this._isLayoutInited=!0},c.arrange=function(e){this.option(e),this._getIsInstant();var t=this._filter(this.items);this.filteredItems=t.matches,this._bindArrangeComplete(),this._isInstant?this._noTransition(this._hideReveal,[t]):this._hideReveal(t),this._sort(),this._layout()},c._init=c.arrange,c._hideReveal=function(e){this.reveal(e.needReveal),this.hide(e.needHide)},c._getIsInstant=function(){var e=this._getOption("layoutInstant"),t=void 0!==e?e:!this._isLayoutInited;return this._isInstant=t,t},c._bindArrangeComplete=function(){function e(){t&&i&&n&&o.dispatchEvent("arrangeComplete",null,[o.filteredItems])}var t,i,n,o=this;this.once("layoutComplete",function(){t=!0,e()}),this.once("hideComplete",function(){i=!0,e()}),this.once("revealComplete",function(){n=!0,e()})},c._filter=function(e){var t=this.options.filter;t=t||"*";for(var i=[],n=[],o=[],r=this._getFilterTest(t),s=0;s<e.length;s++){var a=e[s];if(!a.isIgnored){var l=r(a);l&&i.push(a),l&&a.isHidden?n.push(a):l||a.isHidden||o.push(a)}}return{matches:i,needReveal:n,needHide:o}},c._getFilterTest=function(e){return a&&this.options.isjQuery_BENTFiltering?function(t){return a(t.element).is(e)}:"function"==typeof e?function(t){return e(t.element)}:function(t){return n(t.element,e)}},c.updateSortData=function(e){var t;e?(e=o.makeArray(e),t=this.getItems(e)):t=this.items,this._getSorters(),this._updateItemsSortData(t)},c._getSorters=function(){var e=this.options.getSortData;for(var t in e){var i=e[t];this._sorters[t]=h(i)}},c._updateItemsSortData=function(e){for(var t=e&&e.length,i=0;t&&i<t;i++)e[i].updateSortData()};var h=function(e){if("string"!=typeof e)return e;var t=l(e).split(" "),i=t[0],n=i.match(/^\[(.+)\]$/),o=function(e,t){return e?function(t){return t.getAttribute(e)}:function(e){var i=e.querySelector(t);return i&&i.textContent}}(n&&n[1],i),r=u.sortDataParsers[t[1]];return r?function(e){return e&&r(o(e))}:function(e){return e&&o(e)}};u.sortDataParsers={parseInt:function(e){return parseInt(e,10)},parseFloat:function(e){return parseFloat(e)}},c._sort=function(){if(this.options.sortBy){var e=o.makeArray(this.options.sortBy);this._getIsSameSortBy(e)||(this.sortHistory=e.concat(this.sortHistory));var t=function(e,t){return function(i,n){for(var o=0;o<e.length;o++){var r=e[o],s=i.sortData[r],a=n.sortData[r];if(s>a||s<a)return(s>a?1:-1)*((void 0!==t[r]?t[r]:t)?1:-1)}return 0}}(this.sortHistory,this.options.sortAscending);this.filteredItems.sort(t)}},c._getIsSameSortBy=function(e){for(var t=0;t<e.length;t++)if(e[t]!=this.sortHistory[t])return!1;return!0},c._mode=function(){var e=this.options.layoutMode,t=this.modes[e];if(!t)throw new Error("No layout mode: "+e);return t.options=this.options[e],t},c._resetLayout=function(){t.prototype._resetLayout.call(this),this._mode()._resetLayout()},c._getItemLayoutPosition=function(e){return this._mode()._getItemLayoutPosition(e)},c._manageStamp=function(e){this._mode()._manageStamp(e)},c._getContainerSize=function(){return this._mode()._getContainerSize()},c.needsResizeLayout=function(){return this._mode().needsResizeLayout()},c.appended=function(e){var t=this.addItems(e);if(t.length){var i=this._filterRevealAdded(t);this.filteredItems=this.filteredItems.concat(i)}},c.prepended=function(e){var t=this._itemize(e);if(t.length){this._resetLayout(),this._manageStamps();var i=this._filterRevealAdded(t);this.layoutItems(this.filteredItems),this.filteredItems=i.concat(this.filteredItems),this.items=t.concat(this.items)}},c._filterRevealAdded=function(e){var t=this._filter(e);return this.hide(t.needHide),this.reveal(t.matches),this.layoutItems(t.matches,!0),t.matches},c.insert=function(e){var t=this.addItems(e);if(t.length){var i,n,o=t.length;for(i=0;i<o;i++)n=t[i],this.element.appendChild(n.element);var r=this._filter(t).matches;for(i=0;i<o;i++)t[i].isLayoutInstant=!0;for(this.arrange(),i=0;i<o;i++)delete t[i].isLayoutInstant;this.reveal(r)}};var d=c.remove;return c.remove=function(e){e=o.makeArray(e);var t=this.getItems(e);d.call(this,e);for(var i=t&&t.length,n=0;i&&n<i;n++){var r=t[n];o.removeFrom(this.filteredItems,r)}},c.shuffle=function(){for(var e=0;e<this.items.length;e++)this.items[e].sortData.random=Math.random();this.options.sortBy="random",this._sort(),this._layout()},c._noTransition=function(e,t){var i=this.options.transitionDuration;this.options.transitionDuration=0;var n=e.apply(this,t);return this.options.transitionDuration=i,n},c.getFilteredItemElements=function(){return this.filteredItems.map(function(e){return e.element})},u}),function(e,t){"function"==typeof define&&define.amd?define("packery/js/rect",t):"object"==typeof module&&module.exports?module.exports=t():(e.Packery=e.Packery||{},e.Packery.Rect=t())}(window,function(){function e(t){for(var i in e.defaults)this[i]=e.defaults[i];for(i in t)this[i]=t[i]}e.defaults={x:0,y:0,width:0,height:0};var t=e.prototype;return t.contains=function(e){var t=e.width||0,i=e.height||0;return this.x<=e.x&&this.y<=e.y&&this.x+this.width>=e.x+t&&this.y+this.height>=e.y+i},t.overlaps=function(e){var t=this.x+this.width,i=this.y+this.height,n=e.x+e.width,o=e.y+e.height;return this.x<n&&t>e.x&&this.y<o&&i>e.y},t.getMaximalFreeRects=function(t){if(!this.overlaps(t))return!1;var i,n=[],o=this.x+this.width,r=this.y+this.height,s=t.x+t.width,a=t.y+t.height;return this.y<t.y&&(i=new e({x:this.x,y:this.y,width:this.width,height:t.y-this.y}),n.push(i)),o>s&&(i=new e({x:s,y:this.y,width:o-s,height:this.height}),n.push(i)),r>a&&(i=new e({x:this.x,y:a,width:this.width,height:r-a}),n.push(i)),this.x<t.x&&(i=new e({x:this.x,y:this.y,width:t.x-this.x,height:this.height}),n.push(i)),n},t.canFit=function(e){return this.width>=e.width&&this.height>=e.height},e}),function(e,t){if("function"==typeof define&&define.amd)define("packery/js/packer",["./rect"],t);else if("object"==typeof module&&module.exports)module.exports=t(require("./rect"));else{var i=e.Packery=e.Packery||{};i.Packer=t(i.Rect)}}(window,function(e){function t(e,t,i){this.width=e||0,this.height=t||0,this.sortDirection=i||"downwardLeftToRight",this.reset()}var i=t.prototype;i.reset=function(){this.spaces=[];var t=new e({x:0,y:0,width:this.width,height:this.height});this.spaces.push(t),this.sorter=n[this.sortDirection]||n.downwardLeftToRight},i.pack=function(e){for(var t=0;t<this.spaces.length;t++){var i=this.spaces[t];if(i.canFit(e)){this.placeInSpace(e,i);break}}},i.columnPack=function(e){for(var t=0;t<this.spaces.length;t++){var i=this.spaces[t];if(i.x<=e.x&&i.x+i.width>=e.x+e.width&&i.height>=e.height-.01){e.y=i.y,this.placed(e);break}}},i.rowPack=function(e){for(var t=0;t<this.spaces.length;t++){var i=this.spaces[t];if(i.y<=e.y&&i.y+i.height>=e.y+e.height&&i.width>=e.width-.01){e.x=i.x,this.placed(e);break}}},i.placeInSpace=function(e,t){e.x=t.x,e.y=t.y,this.placed(e)},i.placed=function(e){for(var t=[],i=0;i<this.spaces.length;i++){var n=this.spaces[i],o=n.getMaximalFreeRects(e);o?t.push.apply(t,o):t.push(n)}this.spaces=t,this.mergeSortSpaces()},i.mergeSortSpaces=function(){t.mergeRects(this.spaces),this.spaces.sort(this.sorter)},i.addSpace=function(e){this.spaces.push(e),this.mergeSortSpaces()},t.mergeRects=function(e){var t=0,i=e[t];e:for(;i;){for(var n=0,o=e[t+n];o;){if(o==i)n++;else{if(o.contains(i)){e.splice(t,1),i=e[t];continue e}i.contains(o)?e.splice(t+n,1):n++}o=e[t+n]}i=e[++t]}return e};var n={downwardLeftToRight:function(e,t){return e.y-t.y||e.x-t.x},rightwardTopToBottom:function(e,t){return e.x-t.x||e.y-t.y}};return t}),function(e,t){"function"==typeof define&&define.amd?define("packery/js/item",["outlayer/outlayer","./rect"],t):"object"==typeof module&&module.exports?module.exports=t(require("outlayer"),require("./rect")):e.Packery.Item=t(e.Outlayer,e.Packery.Rect)}(window,function(e,t){var i="string"==typeof document.documentElement.style.transform?"transform":"WebkitTransform",n=function(){e.Item.apply(this,arguments)},o=n.prototype=Object.create(e.Item.prototype),r=o._create;o._create=function(){r.call(this),this.rect=new t};var s=o.moveTo;return o.moveTo=function(e,t){var i=Math.abs(this.position.x-e),n=Math.abs(this.position.y-t);return this.layout.dragItemCount&&!this.isPlacing&&!this.isTransitioning&&1>i&&1>n?void this.goTo(e,t):void s.apply(this,arguments)},o.enablePlacing=function(){this.removeTransitionStyles(),this.isTransitioning&&i&&(this.element.style[i]="none"),this.isTransitioning=!1,this.getSize(),this.layout._setRectSize(this.element,this.rect),this.isPlacing=!0},o.disablePlacing=function(){this.isPlacing=!1},o.removeElem=function(){this.element.parentNode.removeChild(this.element),this.layout.packer.addSpace(this.rect),this.emitEvent("remove",[this])},o.showDropPlaceholder=function(){var e=this.dropPlaceholder;e||((e=this.dropPlaceholder=document.createElement("div")).className="packery-drop-placeholder",e.style.position="absolute"),e.style.width=this.size.width+"px",e.style.height=this.size.height+"px",this.positionDropPlaceholder(),this.layout.element.appendChild(e)},o.positionDropPlaceholder=function(){this.dropPlaceholder.style[i]="translate("+this.rect.x+"px, "+this.rect.y+"px)"},o.hideDropPlaceholder=function(){this.layout.element.removeChild(this.dropPlaceholder)},n}),function(e,t){"function"==typeof define&&define.amd?define("packery/js/packery",["get-size/get-size","outlayer/outlayer","./rect","./packer","./item"],t):"object"==typeof module&&module.exports?module.exports=t(require("get-size"),require("outlayer"),require("./rect"),require("./packer"),require("./item")):e.Packery=t(e.getSize,e.Outlayer,e.Packery.Rect,e.Packery.Packer,e.Packery.Item)}(window,function(e,t,i,n,o){function r(e,t){return e.position.y-t.position.y||e.position.x-t.position.x}function s(e,t){return e.position.x-t.position.x||e.position.y-t.position.y}i.prototype.canFit=function(e){return this.width>=e.width-1&&this.height>=e.height-1};var a=t.create("packery");a.Item=o;var l=a.prototype;l._create=function(){t.prototype._create.call(this),this.packer=new n,this.shiftPacker=new n,this.isEnabled=!0,this.dragItemCount=0;var e=this;this.handleDraggabilly={dragStart:function(){e.itemDragStart(this.element)},dragMove:function(){e.itemDragMove(this.element,this.position.x,this.position.y)},dragEnd:function(){e.itemDragEnd(this.element)}},this.handleUIDraggable={start:function(t,i){i&&e.itemDragStart(t.currentTarget)},drag:function(t,i){i&&e.itemDragMove(t.currentTarget,i.position.left,i.position.top)},stop:function(t,i){i&&e.itemDragEnd(t.currentTarget)}}},l._resetLayout=function(){var e,t,i;this.getSize(),this._getMeasurements(),this._getOption("horizontal")?(e=1/0,t=this.size.innerHeight+this.gutter,i="rightwardTopToBottom"):(e=this.size.innerWidth+this.gutter,t=1/0,i="downwardLeftToRight"),this.packer.width=this.shiftPacker.width=e,this.packer.height=this.shiftPacker.height=t,this.packer.sortDirection=this.shiftPacker.sortDirection=i,this.packer.reset(),this.maxY=0,this.maxX=0},l._getMeasurements=function(){this._getMeasurement("columnWidth","width"),this._getMeasurement("rowHeight","height"),this._getMeasurement("gutter","width")},l._getItemLayoutPosition=function(e){if(this._setRectSize(e.element,e.rect),this.isShifting||this.dragItemCount>0){var t=this._getPackMethod();this.packer[t](e.rect)}else this.packer.pack(e.rect);return this._setMaxXY(e.rect),e.rect},l.shiftLayout=function(){this.isShifting=!0,this.layout(),delete this.isShifting},l._getPackMethod=function(){return this._getOption("horizontal")?"rowPack":"columnPack"},l._setMaxXY=function(e){this.maxX=Math.max(e.x+e.width,this.maxX),this.maxY=Math.max(e.y+e.height,this.maxY)},l._setRectSize=function(t,i){var n=e(t),o=n.outerWidth,r=n.outerHeight;(o||r)&&(o=this._applyGridGutter(o,this.columnWidth),r=this._applyGridGutter(r,this.rowHeight)),i.width=Math.min(o,this.packer.width),i.height=Math.min(r,this.packer.height)},l._applyGridGutter=function(e,t){if(!t)return e+this.gutter;var i=e%(t+=this.gutter);return Math[i&&1>i?"round":"ceil"](e/t)*t},l._getContainerSize=function(){return this._getOption("horizontal")?{width:this.maxX-this.gutter}:{height:this.maxY-this.gutter}},l._manageStamp=function(e){var t,n=this.getItem(e);if(n&&n.isPlacing)t=n.rect;else{var o=this._getElementOffset(e);t=new i({x:this._getOption("originLeft")?o.left:o.right,y:this._getOption("originTop")?o.top:o.bottom})}this._setRectSize(e,t),this.packer.placed(t),this._setMaxXY(t)},l.sortItemsByPosition=function(){var e=this._getOption("horizontal")?s:r;this.items.sort(e)},l.fit=function(e,t,i){var n=this.getItem(e);n&&(this.stamp(n.element),n.enablePlacing(),this.updateShiftTargets(n),t=void 0===t?n.rect.x:t,i=void 0===i?n.rect.y:i,this.shift(n,t,i),this._bindFitEvents(n),n.moveTo(n.rect.x,n.rect.y),this.shiftLayout(),this.unstamp(n.element),this.sortItemsByPosition(),n.disablePlacing())},l._bindFitEvents=function(e){function t(){2==++n&&i.dispatchEvent("fitComplete",null,[e])}var i=this,n=0;e.once("layout",t),this.once("layoutComplete",t)},l.resize=function(){this.isResizeBound&&this.needsResizeLayout()&&(this.options.shiftPercentResize?this.resizeShiftPercentLayout():this.layout())},l.needsResizeLayout=function(){var t=e(this.element),i=this._getOption("horizontal")?"innerHeight":"innerWidth";return t[i]!=this.size[i]},l.resizeShiftPercentLayout=function(){var t=this._getItemsForLayout(this.items),i=this._getOption("horizontal"),n=i?"y":"x",o=i?"height":"width",r=i?"rowHeight":"columnWidth",s=i?"innerHeight":"innerWidth",a=this[r];if(a=a&&a+this.gutter){this._getMeasurements();var l=this[r]+this.gutter;t.forEach(function(e){var t=Math.round(e.rect[n]/a);e.rect[n]=t*l})}else{var u=e(this.element)[s]+this.gutter,c=this.packer[o];t.forEach(function(e){e.rect[n]=e.rect[n]/c*u})}this.shiftLayout()},l.itemDragStart=function(e){if(this.isEnabled){this.stamp(e);var t=this.getItem(e);t&&(t.enablePlacing(),t.showDropPlaceholder(),this.dragItemCount++,this.updateShiftTargets(t))}},l.updateShiftTargets=function(e){this.shiftPacker.reset(),this._getBoundingRect();var t=this._getOption("originLeft"),n=this._getOption("originTop");this.stamps.forEach(function(e){var o=this.getItem(e);if(!o||!o.isPlacing){var r=this._getElementOffset(e),s=new i({x:t?r.left:r.right,y:n?r.top:r.bottom});this._setRectSize(e,s),this.shiftPacker.placed(s)}},this);var o=this._getOption("horizontal"),r=o?"rowHeight":"columnWidth",s=o?"height":"width";this.shiftTargetKeys=[],this.shiftTargets=[];var a,l=this[r];if(l=l&&l+this.gutter){var u=Math.ceil(e.rect[s]/l),c=Math.floor((this.shiftPacker[s]+this.gutter)/l);a=(c-u)*l;for(var h=0;c>h;h++)this._addShiftTarget(h*l,0,a)}else a=this.shiftPacker[s]+this.gutter-e.rect[s],this._addShiftTarget(0,0,a);var d=this._getItemsForLayout(this.items),f=this._getPackMethod();d.forEach(function(e){var t=e.rect;this._setRectSize(e.element,t),this.shiftPacker[f](t),this._addShiftTarget(t.x,t.y,a);var i=o?t.x+t.width:t.x,n=o?t.y:t.y+t.height;if(this._addShiftTarget(i,n,a),l)for(var r=Math.round(t[s]/l),u=1;r>u;u++){var c=o?i:t.x+l*u,h=o?t.y+l*u:n;this._addShiftTarget(c,h,a)}},this)},l._addShiftTarget=function(e,t,i){var n=this._getOption("horizontal")?t:e;if(!(0!==n&&n>i)){var o=e+","+t;-1!=this.shiftTargetKeys.indexOf(o)||(this.shiftTargetKeys.push(o),this.shiftTargets.push({x:e,y:t}))}},l.shift=function(e,t,i){var n,o=1/0,r={x:t,y:i};this.shiftTargets.forEach(function(e){var t=function(e,t){var i=t.x-e.x,n=t.y-e.y;return Math.sqrt(i*i+n*n)}(e,r);o>t&&(n=e,o=t)}),e.rect.x=n.x,e.rect.y=n.y},l.itemDragMove=function(e,t,i){function n(){r.shift(o,t,i),o.positionDropPlaceholder(),r.layout()}var o=this.isEnabled&&this.getItem(e);if(o){t-=this.size.paddingLeft,i-=this.size.paddingTop;var r=this,s=new Date;this._itemDragTime&&s-this._itemDragTime<120?(clearTimeout(this.dragTimeout),this.dragTimeout=setTimeout(n,120)):(n(),this._itemDragTime=s)}},l.itemDragEnd=function(e){function t(){2==++n&&(i.element.classList.remove("is-positioning-post-drag"),i.hideDropPlaceholder(),o.dispatchEvent("dragItemPositioned",null,[i]))}var i=this.isEnabled&&this.getItem(e);if(i){clearTimeout(this.dragTimeout),i.element.classList.add("is-positioning-post-drag");var n=0,o=this;i.once("layout",t),this.once("layoutComplete",t),i.moveTo(i.rect.x,i.rect.y),this.layout(),this.dragItemCount=Math.max(0,this.dragItemCount-1),this.sortItemsByPosition(),i.disablePlacing(),this.unstamp(i.element)}},l.bindDraggabillyEvents=function(e){this._bindDraggabillyEvents(e,"on")},l.unbindDraggabillyEvents=function(e){this._bindDraggabillyEvents(e,"off")},l._bindDraggabillyEvents=function(e,t){var i=this.handleDraggabilly;e[t]("dragStart",i.dragStart),e[t]("dragMove",i.dragMove),e[t]("dragEnd",i.dragEnd)},l.bindUIDraggableEvents=function(e){this._bindUIDraggableEvents(e,"on")},l.unbindUIDraggableEvents=function(e){this._bindUIDraggableEvents(e,"off")},l._bindUIDraggableEvents=function(e,t){var i=this.handleUIDraggable;e[t]("dragstart",i.start)[t]("drag",i.drag)[t]("dragstop",i.stop)};var u=l.destroy;return l.destroy=function(){u.apply(this,arguments),this.isEnabled=!1},a.Rect=i,a.Packer=n,a}),function(e,t){"function"==typeof define&&define.amd?define(["isotopebee-layout/js/layout-mode","packery/js/packery"],t):"object"==typeof module&&module.exports?module.exports=t(require("isotopebee-layout/js/layout-mode"),require("packery")):t(e.isotopebee.LayoutMode,e.Packery)}(window,function(e,t){var i=e.create("packery"),n=i.prototype,o={_getElementOffset:!0,_getMeasurement:!0};for(var r in t.prototype)o[r]||(n[r]=t.prototype[r]);var s=n._resetLayout;n._resetLayout=function(){this.packer=this.packer||new t.Packer,this.shiftPacker=this.shiftPacker||new t.Packer,s.apply(this,arguments)};var a=n._getItemLayoutPosition;n._getItemLayoutPosition=function(e){return e.rect=e.rect||new t.Rect,a.call(this,e)};var l=n.needsResizeLayout;n.needsResizeLayout=function(){return this._getOption("horizontal")?this.needsVerticalResizeLayout():l.call(this)};var u=n._getOption;return n._getOption=function(e){return"horizontal"==e?void 0!==this.options.isHorizontal?this.options.isHorizontal:this.options.horizontal:u.apply(this.isotopebee,arguments)},i}),function(e,t){"function"==typeof define&&define.amd?define("jquery-bridget/jquery-bridget",["jquery"],function(i){return t(e,i)}):"object"==typeof module&&module.exports?module.exports=t(e,require("jquery")):e.jQuery_BENTBridget=t(e,e.jQuery_BENT)}(window,function(e,t){"use strict";var i=Array.prototype.slice,n=e.console,o=void 0===n?function(){}:function(e){n.error(e)};function r(n,r,a){(a=a||t||e.jQuery_BENT)&&(r.prototype.option||(r.prototype.option=function(e){a.isPlainObject(e)&&(this.options=a.extend(!0,this.options,e))}),a.fn[n]=function(e){var t;return"string"==typeof e?function(e,t,i){var r,s="$()."+n+'("'+t+'")';return e.each(function(e,l){var u=a.data(l,n);if(u){var c=u[t];if(c&&"_"!=t.charAt(0)){var h=c.apply(u,i);r=void 0===r?h:r}else o(s+" is not a valid method")}else o(n+" not initialized. Cannot call methods, i.e. "+s)}),void 0!==r?r:e}(this,e,i.call(arguments,1)):(t=e,this.each(function(e,i){var o=a.data(i,n);o?(o.option(t),o._init()):(o=new r(i,t),a.data(i,n,o))}),this)},s(a))}function s(e){!e||e&&e.bridget||(e.bridget=r)}return s(t||e.jQuery_BENT),r}),function(e,t){"function"==typeof define&&define.amd?define("ev-emitter/ev-emitter",t):"object"==typeof module&&module.exports?module.exports=t():e.EvEmitter=t()}("undefined"!=typeof window?window:this,function(){function e(){}var t=e.prototype;return t.on=function(e,t){if(e&&t){var i=this._events=this._events||{},n=i[e]=i[e]||[];return-1==n.indexOf(t)&&n.push(t),this}},t.once=function(e,t){if(e&&t){this.on(e,t);var i=this._onceEvents=this._onceEvents||{};return(i[e]=i[e]||{})[t]=!0,this}},t.off=function(e,t){var i=this._events&&this._events[e];if(i&&i.length){var n=i.indexOf(t);return-1!=n&&i.splice(n,1),this}},t.emitEvent=function(e,t){var i=this._events&&this._events[e];if(i&&i.length){i=i.slice(0),t=t||[];for(var n=this._onceEvents&&this._onceEvents[e],o=0;o<i.length;o++){var r=i[o];n&&n[r]&&(this.off(e,r),delete n[r]),r.apply(this,t)}return this}},t.allOff=function(){delete this._events,delete this._onceEvents},e}),function(e,t){"function"==typeof define&&define.amd?define("get-size/get-size",t):"object"==typeof module&&module.exports?module.exports=t():e.getSize=t()}(window,function(){"use strict";function e(e){var t=parseFloat(e);return-1==e.indexOf("%")&&!isNaN(t)&&t}var t="undefined"==typeof console?function(){}:function(e){console.error(e)},i=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],n=i.length;function o(e){var i=getComputedStyle(e);return i||t("Style returned "+i+". Are you running this code in a hidden iframe on Firefox? See https://bit.ly/getsizebug1"),i}var r,s=!1;return function t(a){if(function(){if(!s){s=!0;var i=document.createElement("div");i.style.width="200px",i.style.padding="1px 2px 3px 4px",i.style.borderStyle="solid",i.style.borderWidth="1px 2px 3px 4px",i.style.boxSizing="border-box";var n=document.body||document.documentElement;n.appendChild(i);var a=o(i);r=200==Math.round(e(a.width)),t.isBoxSizeOuter=r,n.removeChild(i)}}(),"string"==typeof a&&(a=document.querySelector(a)),a&&"object"==typeof a&&a.nodeType){var l=o(a);if("none"==l.display)return function(){for(var e={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},t=0;t<n;t++)e[i[t]]=0;return e}();var u={};u.width=a.offsetWidth,u.height=a.offsetHeight;for(var c=u.isBorderBox="border-box"==l.boxSizing,h=0;h<n;h++){var d=i[h],f=l[d],p=parseFloat(f);u[d]=isNaN(p)?0:p}var g=u.paddingLeft+u.paddingRight,m=u.paddingTop+u.paddingBottom,v=u.marginLeft+u.marginRight,y=u.marginTop+u.marginBottom,b=u.borderLeftWidth+u.borderRightWidth,x=u.borderTopWidth+u.borderBottomWidth,w=c&&r,E=e(l.width);!1!==E&&(u.width=E+(w?0:g+b));var S=e(l.height);return!1!==S&&(u.height=S+(w?0:m+x)),u.innerWidth=u.width-(g+b),u.innerHeight=u.height-(m+x),u.outerWidth=u.width+v,u.outerHeight=u.height+y,u}}}),function(e,t){"use strict";"function"==typeof define&&define.amd?define("desandro-matches-selector/matches-selector",t):"object"==typeof module&&module.exports?module.exports=t():e.matchesSelector=t()}(window,function(){"use strict";var e=function(){var e=window.Element.prototype;if(e.matches)return"matches";if(e.matchesSelector)return"matchesSelector";for(var t=["webkit","moz","ms","o"],i=0;i<t.length;i++){var n=t[i]+"MatchesSelector";if(e[n])return n}}();return function(t,i){return t[e](i)}}),function(e,t){"function"==typeof define&&define.amd?define("fizzy-ui-utils/utils",["desandro-matches-selector/matches-selector"],function(i){return t(e,i)}):"object"==typeof module&&module.exports?module.exports=t(e,require("desandro-matches-selector")):e.fizzyUIUtils=t(e,e.matchesSelector)}(window,function(e,t){var i={extend:function(e,t){for(var i in t)e[i]=t[i];return e},modulo:function(e,t){return(e%t+t)%t}},n=Array.prototype.slice;i.makeArray=function(e){return Array.isArray(e)?e:null==e?[]:"object"==typeof e&&"number"==typeof e.length?n.call(e):[e]},i.removeFrom=function(e,t){var i=e.indexOf(t);-1!=i&&e.splice(i,1)},i.getParent=function(e,i){for(;e.parentNode&&e!=document.body;)if(e=e.parentNode,t(e,i))return e},i.getQueryElement=function(e){return"string"==typeof e?document.querySelector(e):e},i.handleEvent=function(e){var t="on"+e.type;this[t]&&this[t](e)},i.filterFindElements=function(e,n){e=i.makeArray(e);var o=[];return e.forEach(function(e){if(e instanceof HTMLElement)if(n){t(e,n)&&o.push(e);for(var i=e.querySelectorAll(n),r=0;r<i.length;r++)o.push(i[r])}else o.push(e)}),o},i.debounceMethod=function(e,t,i){i=i||100;var n=e.prototype[t],o=t+"Timeout";e.prototype[t]=function(){var e=this[o];clearTimeout(e);var t=arguments,r=this;this[o]=setTimeout(function(){n.apply(r,t),delete r[o]},i)}},i.docReady=function(e){var t=document.readyState;"complete"==t||"interactive"==t?setTimeout(e):document.addEventListener("DOMContentLoaded",e)},i.toDashed=function(e){return e.replace(/(.)([A-Z])/g,function(e,t,i){return t+"-"+i}).toLowerCase()};var o=e.console;return i.htmlInit=function(t,n){i.docReady(function(){var r=i.toDashed(n),s="data-"+r,a=document.querySelectorAll("["+s+"]"),l=document.querySelectorAll(".js-"+r),u=i.makeArray(a).concat(i.makeArray(l)),c=s+"-options",h=e.jQuery_BENT;u.forEach(function(e){var i,r=e.getAttribute(s)||e.getAttribute(c);try{i=r&&JSON.parse(r)}catch(t){return void(o&&o.error("Error parsing "+s+" on "+e.className+": "+t))}var a=new t(e,i);h&&h.data(e,n,a)})})},i}),function(e,t){"function"==typeof define&&define.amd?define("flickitybee/js/cell",["get-size/get-size"],function(i){return t(e,i)}):"object"==typeof module&&module.exports?module.exports=t(e,require("get-size")):(e.Flickitybee=e.Flickitybee||{},e.Flickitybee.Cell=t(e,e.getSize))}(window,function(e,t){function i(e,t){this.element=e,this.parent=t,this.create()}var n=i.prototype;return n.create=function(){this.element.style.position="absolute",this.element.setAttribute("aria-hidden","true"),this.x=0,this.shift=0,this.element.style[this.parent.originSide]=0},n.destroy=function(){this.unselect(),this.element.style.position="";var e=this.parent.originSide;this.element.style[e]="",this.element.style.transform="",this.element.removeAttribute("aria-hidden")},n.getSize=function(){this.size=t(this.element)},n.setPosition=function(e){this.x=e,this.updateTarget(),this.renderPosition(e)},n.updateTarget=n.setDefaultTarget=function(){var e="left"==this.parent.originSide?"marginLeft":"marginRight";this.target=this.x+this.size[e]+this.size.width*this.parent.cellAlign},n.renderPosition=function(e){var t="left"===this.parent.originSide?1:-1,i=this.parent.options.percentPosition?e*t*(this.parent.size.innerWidth/this.size.width):e*t;this.element.style.transform="translateX("+this.parent.getPositionValue(i)+")"},n.select=function(){this.element.classList.add("is-selected"),this.element.removeAttribute("aria-hidden")},n.unselect=function(){this.element.classList.remove("is-selected"),this.element.setAttribute("aria-hidden","true")},n.wrapShift=function(e){this.shift=e,this.renderPosition(this.x+this.parent.slideableWidth*e)},n.remove=function(){this.element.parentNode.removeChild(this.element)},i}),function(e,t){"function"==typeof define&&define.amd?define("flickitybee/js/slide",t):"object"==typeof module&&module.exports?module.exports=t():(e.Flickitybee=e.Flickitybee||{},e.Flickitybee.Slide=t())}(window,function(){"use strict";function e(e){this.parent=e,this.isOriginLeft="left"==e.originSide,this.cells=[],this.outerWidth=0,this.height=0}var t=e.prototype;return t.addCell=function(e){if(this.cells.push(e),this.outerWidth+=e.size.outerWidth,this.height=Math.max(e.size.outerHeight,this.height),1==this.cells.length){this.x=e.x;var t=this.isOriginLeft?"marginLeft":"marginRight";this.firstMargin=e.size[t]}},t.updateTarget=function(){var e=this.isOriginLeft?"marginRight":"marginLeft",t=this.getLastCell(),i=t?t.size[e]:0,n=this.outerWidth-(this.firstMargin+i);this.target=this.x+this.firstMargin+n*this.parent.cellAlign},t.getLastCell=function(){return this.cells[this.cells.length-1]},t.select=function(){this.cells.forEach(function(e){e.select()})},t.unselect=function(){this.cells.forEach(function(e){e.unselect()})},t.getCellElements=function(){return this.cells.map(function(e){return e.element})},e}),function(e,t){"function"==typeof define&&define.amd?define("flickitybee/js/animate",["fizzy-ui-utils/utils"],function(i){return t(e,i)}):"object"==typeof module&&module.exports?module.exports=t(e,require("fizzy-ui-utils")):(e.Flickitybee=e.Flickitybee||{},e.Flickitybee.animatePrototype=t(e,e.fizzyUIUtils))}(window,function(e,t){return{startAnimation:function(){this.isAnimating||(this.isAnimating=!0,this.restingFrames=0,this.animate())},animate:function(){this.applyDragForce(),this.applySelectedAttraction();var e=this.x;if(this.integratePhysics(),this.positionSlider(),this.settle(e),this.isAnimating){var t=this;requestAnimationFrame(function(){t.animate()})}},positionSlider:function(){var e=this.x;this.options.wrapAround&&this.cells.length>1&&(e=t.modulo(e,this.slideableWidth),e-=this.slideableWidth,this.shiftWrapCells(e)),this.setTranslateX(e,this.isAnimating),this.dispatchScrollEvent()},setTranslateX:function(e,t){e+=this.cursorPosition,e=RtlBee?-e:e;var i=this.getPositionValue(e);this.slider.style.transform=t?"translate3d("+i+",0,0)":"translateX("+i+")"},dispatchScrollEvent:function(){var e=this.slides[0];if(e){var t=-this.x-e.target,i=t/this.slidesWidth;this.dispatchEvent("scroll",null,[i,t])}},positionSliderAtSelected:function(){this.cells.length&&(this.x=-this.selectedSlide.target,this.velocity=0,this.positionSlider())},getPositionValue:function(e){return this.options.percentPosition?.01*Math.round(e/this.size.innerWidth*1e4)+"%":Math.round(e)+"px"},settle:function(e){!this.isPointerDown&&Math.round(100*this.x)==Math.round(100*e)&&this.restingFrames++,this.restingFrames>2&&(this.isAnimating=!1,delete this.isFreeScrolling,this.positionSlider(),this.dispatchEvent("settle",null,[this.selectedIndex]))},shiftWrapCells:function(e){var t=this.cursorPosition+e;this._shiftCells(this.beforeShiftCells,t,-1);var i=this.size.innerWidth-(e+this.slideableWidth+this.cursorPosition);this._shiftCells(this.afterShiftCells,i,1)},_shiftCells:function(e,t,i){for(var n=0;n<e.length;n++){var o=e[n],r=t>0?i:0;o.wrapShift(r),t-=o.size.outerWidth}this._checkVisibility()},_unshiftCells:function(e){if(e&&e.length)for(var t=0;t<e.length;t++)e[t].wrapShift(0)},integratePhysics:function(){this.x+=this.velocity,this.velocity*=this.getFrictionFactor()},applyForce:function(e){this.velocity+=e},getFrictionFactor:function(){return 1-this.options[this.isFreeScrolling?"freeScrollFriction":"friction"]},getRestingPosition:function(){return this.x+this.velocity/(1-this.getFrictionFactor())},applyDragForce:function(){if(this.isDraggable&&this.isPointerDown){var e=this.dragX-this.x-this.velocity;this.applyForce(e)}},applySelectedAttraction:function(){if((!this.isDraggable||!this.isPointerDown)&&!this.isFreeScrolling&&this.slides.length){var e=(-1*this.selectedSlide.target-this.x)*this.options.selectedAttraction;this.applyForce(e)}}}}),function(e,t){if("function"==typeof define&&define.amd)define("flickitybee/js/flickitybee",["ev-emitter/ev-emitter","get-size/get-size","fizzy-ui-utils/utils","./cell","./slide","./animate"],function(i,n,o,r,s,a){return t(e,i,n,o,r,s,a)});else if("object"==typeof module&&module.exports)module.exports=t(e,require("ev-emitter"),require("get-size"),require("fizzy-ui-utils"),require("./cell"),require("./slide"),require("./animate"));else{var i=e.Flickitybee;e.Flickitybee=t(e,e.EvEmitter,e.getSize,e.fizzyUIUtils,i.Cell,i.Slide,i.animatePrototype)}}(window,function(e,t,i,n,o,r,s){var a=e.jQuery_BENT,l=e.getComputedStyle,u=e.console;function c(e,t){for(e=n.makeArray(e);e.length;)t.appendChild(e.shift())}var h=0,d={};function f(e,t){var i=n.getQueryElement(e);if(i){if(this.element=i,this.element.flickitybeeGUID){var o=d[this.element.flickitybeeGUID];return o&&o.option(t),o}switch(a&&(this.$element=a(this.element)),this.options=n.extend({},this.constructor.defaults),t.originwrapAround=t.wrapAround,t.rightToLeft="rtl"==document.documentElement.getAttribute("dir"),t.arrowIcon){case"1":t.arrowShape="M 10,50 L 60,100 L 70,90 L 30,50  L 70,10 L 60,0 Z";break;case"2":t.arrowShape="M 10,50 L 60,100 L 65,95 L 20,50  L 65,5 L 60,0 Z";break;case"3":t.arrowShape="M 0,50 L 60,00 L 50,30 L 80,30 L 80,70 L 50,70 L 60,100 Z"}this.option(t),this._create()}else u&&u.error("Bad element for Flickitybee: "+(i||e))}f.defaults={accessibility:!0,cellAlign:"center",freeScrollFriction:.075,friction:.28,namespaceJQueryEvents:!0,percentPosition:!0,resize:!0,selectedAttraction:.025,setGallerySize:!0,setPrevNextButtons:!1,checkVisibility:!1,sync:!1},f.createMethods=[];var p=f.prototype;n.extend(p,t.prototype),p._create=function(){var t=this.guid=++h;for(var i in this.element.flickitybeeGUID=t,d[t]=this,this.selectedIndex=0,this.restingFrames=0,this.x=0,this.velocity=0,this.originSide=RtlBee?"right":"left",this.viewport=document.createElement("div"),this.viewport.className="flickitybee-viewport",this._createSlider(),(this.options.resize||this.options.watchCSS)&&e.addEventListener("resize",this),this.options.on){var n=this.options.on[i];this.on(i,n)}f.createMethods.forEach(function(e){this[e]()},this),this.options.watchCSS?this.watchCSS():this.activate()},p.option=function(e){n.extend(this.options,e)},p.activate=function(){this.isActive||(this.isActive=!0,this.element.classList.add("flickitybee-enabled"),RtlBee&&this.element.classList.add("flickitybee-rtl"),this.getSize(),c(this._filterFindCellElements(this.element.children),this.slider),this.viewport.appendChild(this.slider),this.element.appendChild(this.viewport),this.reloadCells(),this.options.accessibility&&(this.element.tabIndex=0,this.element.addEventListener("keydown",this)),this.emitEvent("activate"),this.selectInitialIndex(),this.isInitActivated=!0,this.dispatchEvent("ready"))},p._createSlider=function(){var e=document.createElement("div");e.className="flickitybee-slider",e.style[this.originSide]=0,this.slider=e},p._filterFindCellElements=function(e){return n.filterFindElements(e,this.options.cellSelector)},p.reloadCells=function(){this.cells=this._makeCells(this.slider.children),this.positionCells(),this._getWrapShiftCells(),this.setGallerySize(),this.setPrevNextButtons()},p._makeCells=function(e){return this._filterFindCellElements(e).map(function(e){return new o(e,this)},this)},p.getLastCell=function(){return this.cells[this.cells.length-1]},p.getLastSlide=function(){return this.slides[this.slides.length-1]},p.positionCells=function(){this._sizeCells(this.cells),this._positionCells(0)},p._positionCells=function(e){e=e||0,this.maxCellHeight=e&&this.maxCellHeight||0;var t=0;if(e>0){var i=this.cells[e-1];t=i.x+i.size.outerWidth}for(var n=this.cells.length,o=e;o<n;o++){var r=this.cells[o];r.setPosition(t),t+=r.size.outerWidth,this.maxCellHeight=Math.max(r.size.outerHeight,this.maxCellHeight)}this.slideableWidth=t,this.updateSlides(),this._containSlides(),this.slidesWidth=n?this.getLastSlide().target-this.slides[0].target:0,this.maxVisibilityHeight=0},p._sizeCells=function(e){e.forEach(function(e){e.getSize()})},p.updateSlides=function(){if(this.slides=[],this.cells.length){var e=new r(this);this.slides.push(e);var t="left"==this.originSide?"marginRight":"marginLeft",i=this._getCanCellFit();this.cells.forEach(function(n,o){if(e.cells.length){var s=e.outerWidth-e.firstMargin+(n.size.outerWidth-n.size[t]);i.call(this,o,s)?e.addCell(n):(e.updateTarget(),e=new r(this),this.slides.push(e),e.addCell(n))}else e.addCell(n)},this),e.updateTarget(),this.updateSelectedSlide()}},p._getCanCellFit=function(){var e=this.options.groupCells;if(!e)return function(){return!1};if("number"==typeof e){var t=parseInt(e,10);return function(e){return e%t!=0}}var i="string"==typeof e&&e.match(/^(\d+)%$/),n=i?parseInt(i[1],10)/100:1;return function(e,t){return t<=(this.size.innerWidth+1)*n}},p._init=p.reposition=function(){this.positionCells(),this.positionSliderAtSelected()},p.getSize=function(){this.size=i(this.element),this.setCellAlign(),this.cursorPosition=this.size.innerWidth*this.cellAlign};var g={center:{left:.5,right:.5},left:{left:0,right:1},right:{right:0,left:1}};return p.setCellAlign=function(){var e=g[this.options.cellAlign];this.cellAlign=e?e[this.originSide]:this.options.cellAlign},p.setGallerySize=function(){if(this.options.setGallerySize){var e=this.options.adaptiveHeight&&this.selectedSlide?this.selectedSlide.height:this.maxCellHeight;e=this.maxVisibilityHeight&&this.maxVisibilityHeight>e?this.maxVisibilityHeight:e,this.viewport.style.height=e+"px"}},p.setPrevNextButtons=function(){if(this.options.setPrevNextButtons){var e=this.viewport.querySelector(".is-selected [data-cacl-slide]");if(null!==e){var t=e.offsetHeight/2;this.element.style.setProperty("--prev-next-top",t+"px")}}},p._checkVisibility=function(){if(this.options.checkVisibility&&this.options.adaptiveHeight)for(var e=this.viewport.getBoundingClientRect().x,t=this.viewport.offsetWidth,i=this.cells.length,n=0;n<i;n++){var o=this.cells[n],r=o.element.getBoundingClientRect().x-e;r+o.size.innerWidth>e&&r+o.size.innerWidth<t||r>e&&r<t?(this.maxVisibilityHeight=Math.max(o.size.outerHeight,this.maxVisibilityHeight),o.element.classList.add("is-bee-visible"),o.element.removeAttribute("aria-hidden")):(o.element.classList.remove("is-bee-visible"),o.element.setAttribute("aria-hidden",!0))}},p._getWrapShiftCells=function(){if(this.options.originwrapAround)if(this.slides.length<2)this.options.wrapAround=!1;else{this.options.wrapAround=!0,this._unshiftCells(this.beforeShiftCells),this._unshiftCells(this.afterShiftCells);var e=this.cursorPosition,t=this.cells.length-1;this.beforeShiftCells=this._getGapCells(e,t,-1),e=this.size.innerWidth-this.cursorPosition,this.afterShiftCells=this._getGapCells(e,0,1)}},p._getGapCells=function(e,t,i){for(var n=[];e>0;){var o=this.cells[t];if(!o)break;n.push(o),t+=i,e-=o.size.outerWidth}return n},p._containSlides=function(){if(this.options.contain&&!this.options.wrapAround&&this.cells.length){var e=RtlBee?"marginRight":"marginLeft",t=RtlBee?"marginLeft":"marginRight",i=this.slideableWidth-this.getLastCell().size[t],n=i<this.size.innerWidth,o=this.cursorPosition+this.cells[0].size[e],r=i-this.size.innerWidth*(1-this.cellAlign);this.slides.forEach(function(e){n?e.target=i*this.cellAlign:(e.target=Math.max(e.target,o),e.target=Math.min(e.target,r))},this)}},p.dispatchEvent=function(e,t,i){var n=t?[t].concat(i):i;if(this.emitEvent(e,n),a&&this.$element){var o=e+=this.options.namespaceJQueryEvents?".flickitybee":"";if(t){var r=new a.Event(t);r.type=e,o=r}this.$element.trigger(o,i)}},p.select=function(e,t,i){if(this.isActive&&(e=parseInt(e,10),this._wrapSelect(e),(this.options.wrapAround||t)&&(e=n.modulo(e,this.slides.length)),this.slides[e])){var o=this.selectedIndex;this.selectedIndex=e,this.updateSelectedSlide(),i?this.positionSliderAtSelected():this.startAnimation(),this.options.adaptiveHeight&&this.setGallerySize(),this.setPrevNextButtons(),this.dispatchEvent("select",null,[e]),e!=o&&this.dispatchEvent("change",null,[e]),this.dispatchEvent("cellSelect")}},p._wrapSelect=function(e){var t=this.slides.length;if(!(this.options.wrapAround&&t>1))return e;var i=n.modulo(e,t),o=Math.abs(i-this.selectedIndex),r=Math.abs(i+t-this.selectedIndex),s=Math.abs(i-t-this.selectedIndex);!this.isDragSelect&&r<o?e+=t:!this.isDragSelect&&s<o&&(e-=t),e<0?this.x-=this.slideableWidth:e>=t&&(this.x+=this.slideableWidth)},p.previous=function(e,t){this.select(this.selectedIndex-1,e,t)},p.next=function(e,t){this.select(this.selectedIndex+1,e,t)},p.updateSelectedSlide=function(){var e=this.slides[this.selectedIndex];e&&(this.unselectSelectedSlide(),this.selectedSlide=e,e.select(),this.selectedCells=e.cells,this.selectedElements=e.getCellElements(),this.selectedCell=e.cells[0],this.selectedElement=this.selectedElements[0])},p.unselectSelectedSlide=function(){this.selectedSlide&&this.selectedSlide.unselect()},p.selectInitialIndex=function(){var e=this.options.initialIndex;if(this.isInitActivated)this.select(this.selectedIndex,!1,!0);else{if(e&&"string"==typeof e&&this.queryCell(e))return void this.selectCell(e,!1,!0);var t=0;e&&this.slides[e]&&(t=e),this.select(t,!1,!0)}},p.selectCell=function(e,t,i){var n=this.queryCell(e);if(n){var o=this.getCellSlideIndex(n);this.select(o,t,i)}},p.getCellSlideIndex=function(e){for(var t=0;t<this.slides.length;t++)if(-1!=this.slides[t].cells.indexOf(e))return t},p.getCell=function(e){for(var t=0;t<this.cells.length;t++){var i=this.cells[t];if(i.element==e)return i}},p.getCells=function(e){e=n.makeArray(e);var t=[];return e.forEach(function(e){var i=this.getCell(e);i&&t.push(i)},this),t},p.getCellElements=function(){return this.cells.map(function(e){return e.element})},p.getParentCell=function(e){return this.getCell(e)||(e=n.getParent(e,".flickitybee-slider > *"),this.getCell(e))},p.getAdjacentCellElements=function(e,t){if(!e)return this.selectedSlide.getCellElements();t=void 0===t?this.selectedIndex:t;var i=this.slides.length;if(1+2*e>=i)return this.getCellElements();for(var o=[],r=t-e;r<=t+e;r++){var s=this.options.wrapAround?n.modulo(r,i):r,a=this.slides[s];a&&(o=o.concat(a.getCellElements()))}return o},p.queryCell=function(e){if("number"==typeof e)return this.cells[e];if("string"==typeof e){if(e.match(/^[#.]?[\d/]/))return;e=this.element.querySelector(e)}return this.getCell(e)},p.uiChange=function(){this.emitEvent("uiChange")},p.childUIPointerDown=function(e){"touchstart"!=e.type&&e.preventDefault(),this.focus()},p.onresize=function(){this.watchCSS(),this.resize()},n.debounceMethod(f,"onresize",150),p.resize=function(){if(this.isActive&&!this.isAnimating&&!this.isDragging){this.getSize(),this.options.wrapAround&&(this.x=n.modulo(this.x,this.slideableWidth)),this.positionCells(),this._getWrapShiftCells(),this.setGallerySize(),this.setPrevNextButtons(),this.emitEvent("resize");var e=this.selectedElements&&this.selectedElements[0];this.selectCell(e,!1,!0)}},p.watchCSS=function(){this.options.watchCSS&&(-1!=l(this.element,":after").content.indexOf("flickitybee")?this.activate():this.deactivate())},p.onkeydown=function(e){var t=document.activeElement&&document.activeElement!=this.element;if(this.options.accessibility&&!t){var i=f.keyboardHandlers[e.keyCode];i&&i.call(this)}},f.keyboardHandlers={37:function(){var e=RtlBee?"next":"previous";this.uiChange(),this[e]()},39:function(){var e=RtlBee?"previous":"next";this.uiChange(),this[e]()}},p.focus=function(){var t=e.pageYOffset;this.element.focus({preventScroll:!0}),e.pageYOffset!=t&&e.scrollTo(e.pageXOffset,t)},p.deactivate=function(){this.isActive&&(this.element.classList.remove("flickitybee-enabled"),this.element.classList.remove("flickitybee-rtl"),this.unselectSelectedSlide(),this.cells.forEach(function(e){e.destroy()}),this.element.removeChild(this.viewport),c(this.slider.children,this.element),this.options.accessibility&&(this.element.removeAttribute("tabIndex"),this.element.removeEventListener("keydown",this)),this.isActive=!1,this.emitEvent("deactivate"))},p.destroy=function(){this.deactivate(),e.removeEventListener("resize",this),this.allOff(),this.emitEvent("destroy"),a&&this.$element&&a.removeData(this.element,"flickitybee"),delete this.element.flickitybeeGUID,delete d[this.guid]},n.extend(p,s),f.data=function(e){var t=(e=n.getQueryElement(e))&&e.flickitybeeGUID;return t&&d[t]},n.htmlInit(f,"flickitybee"),a&&a.bridget&&a.bridget("flickitybee",f),f.setJQuery=function(e){a=e},f.Cell=o,f.Slide=r,f}),function(e,t){"function"==typeof define&&define.amd?define("unipointer/unipointer",["ev-emitter/ev-emitter"],function(i){return t(e,i)}):"object"==typeof module&&module.exports?module.exports=t(e,require("ev-emitter")):e.Unipointer=t(e,e.EvEmitter)}(window,function(e,t){function i(){}var n=i.prototype=Object.create(t.prototype);n.bindStartEvent=function(e){this._bindStartEvent(e,!0)},n.unbindStartEvent=function(e){this._bindStartEvent(e,!1)},n._bindStartEvent=function(t,i){var n=(i=void 0===i||i)?"addEventListener":"removeEventListener",o="mousedown";"ontouchstart"in e?o="touchstart":e.PointerEvent&&(o="pointerdown"),t[n](o,this)},n.handleEvent=function(e){var t="on"+e.type;this[t]&&this[t](e)},n.getTouch=function(e){for(var t=0;t<e.length;t++){var i=e[t];if(i.identifier==this.pointerIdentifier)return i}},n.onmousedown=function(e){var t=e.button;t&&0!==t&&1!==t||this._pointerDown(e,e)},n.ontouchstart=function(e){this._pointerDown(e,e.changedTouches[0])},n.onpointerdown=function(e){this._pointerDown(e,e)},n._pointerDown=function(e,t){e.button||this.isPointerDown||(this.isPointerDown=!0,this.pointerIdentifier=void 0!==t.pointerId?t.pointerId:t.identifier,this.pointerDown(e,t))},n.pointerDown=function(e,t){this._bindPostStartEvents(e),this.emitEvent("pointerDown",[e,t])};var o={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"]};return n._bindPostStartEvents=function(t){if(t){var i=o[t.type];i.forEach(function(t){e.addEventListener(t,this)},this),this._boundPointerEvents=i}},n._unbindPostStartEvents=function(){this._boundPointerEvents&&(this._boundPointerEvents.forEach(function(t){e.removeEventListener(t,this)},this),delete this._boundPointerEvents)},n.onmousemove=function(e){this._pointerMove(e,e)},n.onpointermove=function(e){e.pointerId==this.pointerIdentifier&&this._pointerMove(e,e)},n.ontouchmove=function(e){var t=this.getTouch(e.changedTouches);t&&this._pointerMove(e,t)},n._pointerMove=function(e,t){this.pointerMove(e,t)},n.pointerMove=function(e,t){this.emitEvent("pointerMove",[e,t])},n.onmouseup=function(e){this._pointerUp(e,e)},n.onpointerup=function(e){e.pointerId==this.pointerIdentifier&&this._pointerUp(e,e)},n.ontouchend=function(e){var t=this.getTouch(e.changedTouches);t&&this._pointerUp(e,t)},n._pointerUp=function(e,t){this._pointerDone(),this.pointerUp(e,t)},n.pointerUp=function(e,t){this.emitEvent("pointerUp",[e,t])},n._pointerDone=function(){this._pointerReset(),this._unbindPostStartEvents(),this.pointerDone()},n._pointerReset=function(){this.isPointerDown=!1,delete this.pointerIdentifier},n.pointerDone=function(){},n.onpointercancel=function(e){e.pointerId==this.pointerIdentifier&&this._pointerCancel(e,e)},n.ontouchcancel=function(e){var t=this.getTouch(e.changedTouches);t&&this._pointerCancel(e,t)},n._pointerCancel=function(e,t){this._pointerDone(),this.pointerCancel(e,t)},n.pointerCancel=function(e,t){this.emitEvent("pointerCancel",[e,t])},i.getPointerPoint=function(e){return{x:e.pageX,y:e.pageY}},i}),function(e,t){"function"==typeof define&&define.amd?define("unidragger/unidragger",["unipointer/unipointer"],function(i){return t(e,i)}):"object"==typeof module&&module.exports?module.exports=t(e,require("unipointer")):e.Unidragger=t(e,e.Unipointer)}(window,function(e,t){function i(){}var n=i.prototype=Object.create(t.prototype);n.bindHandles=function(){this._bindHandles(!0)},n.unbindHandles=function(){this._bindHandles(!1)},n._bindHandles=function(t){for(var i=(t=void 0===t||t)?"addEventListener":"removeEventListener",n=t?this._touchActionValue:"",o=0;o<this.handles.length;o++){var r=this.handles[o];this._bindStartEvent(r,t),r[i]("click",this),e.PointerEvent&&(r.style.touchAction=n)}},n._touchActionValue="none",n.pointerDown=function(e,t){this.okayPointerDown(e)&&(this.pointerDownPointer={pageX:t.pageX,pageY:t.pageY},e.preventDefault(),this.pointerDownBlur(),this._bindPostStartEvents(e),this.emitEvent("pointerDown",[e,t]))};var o={TEXTAREA:!0,INPUT:!0,SELECT:!0,OPTION:!0},r={radio:!0,checkbox:!0,button:!0,submit:!0,image:!0,file:!0};return n.okayPointerDown=function(e){var t=o[e.target.nodeName],i=r[e.target.type],n=!t||i;return n||this._pointerReset(),n},n.pointerDownBlur=function(){var e=document.activeElement;e&&e.blur&&e!=document.body&&e.blur()},n.pointerMove=function(e,t){var i=this._dragPointerMove(e,t);this.emitEvent("pointerMove",[e,t,i]),this._dragMove(e,t,i)},n._dragPointerMove=function(e,t){var i={x:t.pageX-this.pointerDownPointer.pageX,y:t.pageY-this.pointerDownPointer.pageY};return!this.isDragging&&this.hasDragStarted(i)&&this._dragStart(e,t),i},n.hasDragStarted=function(e){return Math.abs(e.x)>3||Math.abs(e.y)>3},n.pointerUp=function(e,t){this.emitEvent("pointerUp",[e,t]),this._dragPointerUp(e,t)},n._dragPointerUp=function(e,t){this.isDragging?this._dragEnd(e,t):this._staticClick(e,t)},n._dragStart=function(e,t){this.isDragging=!0,this.isPreventingClicks=!0,this.dragStart(e,t)},n.dragStart=function(e,t){this.emitEvent("dragStart",[e,t])},n._dragMove=function(e,t,i){this.isDragging&&this.dragMove(e,t,i)},n.dragMove=function(e,t,i){e.preventDefault(),this.emitEvent("dragMove",[e,t,i])},n._dragEnd=function(e,t){this.isDragging=!1,setTimeout(function(){delete this.isPreventingClicks}.bind(this)),this.dragEnd(e,t)},n.dragEnd=function(e,t){this.emitEvent("dragEnd",[e,t])},n.onclick=function(e){this.isPreventingClicks&&e.preventDefault()},n._staticClick=function(e,t){this.isIgnoringMouseUp&&"mouseup"==e.type||(this.staticClick(e,t),"mouseup"!=e.type&&(this.isIgnoringMouseUp=!0,setTimeout(function(){delete this.isIgnoringMouseUp}.bind(this),400)))},n.staticClick=function(e,t){this.emitEvent("staticClick",[e,t])},i.getPointerPoint=t.getPointerPoint,i}),function(e,t){"function"==typeof define&&define.amd?define("flickitybee/js/drag",["./flickitybee","unidragger/unidragger","fizzy-ui-utils/utils"],function(i,n,o){return t(e,i,n,o)}):"object"==typeof module&&module.exports?module.exports=t(e,require("./flickitybee"),require("unidragger"),require("fizzy-ui-utils")):e.Flickitybee=t(e,e.Flickitybee,e.Unidragger,e.fizzyUIUtils)}(window,function(e,t,i,n){n.extend(t.defaults,{draggable:">1",dragThreshold:3}),t.createMethods.push("_createDrag");var o=t.prototype;n.extend(o,i.prototype),o._touchActionValue="pan-y",o._createDrag=function(){this.on("activate",this.onActivateDrag),this.on("uiChange",this._uiChangeDrag),this.on("deactivate",this.onDeactivateDrag),this.on("cellChange",this.updateDraggable)},o.onActivateDrag=function(){this.handles=[this.viewport],this.bindHandles(),this.updateDraggable()},o.onDeactivateDrag=function(){this.unbindHandles(),this.element.classList.remove("is-draggable")},o.updateDraggable=function(){">1"==this.options.draggable?this.isDraggable=this.slides.length>1:"smart"==this.options.draggable?(this.viewport,this.isDraggable=this.viewport.scrollWidth>this.viewport.offsetWidth):this.isDraggable=this.options.draggable,this.isDraggable?this.element.classList.add("is-draggable"):this.element.classList.remove("is-draggable")},o.bindDrag=function(){this.options.draggable=!0,this.updateDraggable()},o.unbindDrag=function(){this.options.draggable=!1,this.updateDraggable()},o._uiChangeDrag=function(){delete this.isFreeScrolling},o.pointerDown=function(t,i){this.isDraggable?this.okayPointerDown(t)&&(this._pointerDownPreventDefault(t),this.pointerDownFocus(t),document.activeElement!=this.element&&this.pointerDownBlur(),this.dragX=this.x,this.viewport.classList.add("is-pointer-down"),this.pointerDownScroll=s(),e.addEventListener("scroll",this),this._pointerDownDefault(t,i)):this._pointerDownDefault(t,i)},o._pointerDownDefault=function(e,t){this.pointerDownPointer={pageX:t.pageX,pageY:t.pageY},this._bindPostStartEvents(e),this.dispatchEvent("pointerDown",e,[t])};var r={INPUT:!0,TEXTAREA:!0,SELECT:!0};function s(){return{x:e.pageXOffset,y:e.pageYOffset}}return o.pointerDownFocus=function(e){r[e.target.nodeName]||this.focus()},o._pointerDownPreventDefault=function(e){var t="touchstart"==e.type,i="touch"==e.pointerType,n=r[e.target.nodeName];t||i||n||e.preventDefault()},o.hasDragStarted=function(e){return Math.abs(e.x)>this.options.dragThreshold},o.pointerUp=function(e,t){delete this.isTouchScrolling,this.viewport.classList.remove("is-pointer-down"),this.dispatchEvent("pointerUp",e,[t]),this._dragPointerUp(e,t)},o.pointerDone=function(){e.removeEventListener("scroll",this),delete this.pointerDownScroll},o.dragStart=function(t,i){this.isDraggable&&(this.dragStartPosition=this.x,this.startAnimation(),e.removeEventListener("scroll",this),this.dispatchEvent("dragStart",t,[i]))},o.pointerMove=function(e,t){var i=this._dragPointerMove(e,t);this.dispatchEvent("pointerMove",e,[t,i]),this._dragMove(e,t,i)},o.dragMove=function(e,t,i){if(this.isDraggable){e.preventDefault(),this.previousDragX=this.dragX;var n=RtlBee?-1:1;this.options.wrapAround&&(i.x%=this.slideableWidth);var o=this.dragStartPosition+i.x*n;if(!this.options.wrapAround&&this.slides.length){var r=Math.max(-this.slides[0].target,this.dragStartPosition);o=o>r?.5*(o+r):o;var s=Math.min(-this.getLastSlide().target,this.dragStartPosition);o=o<s?.5*(o+s):o}this.dragX=o,this.dragMoveTime=new Date,this.dispatchEvent("dragMove",e,[t,i])}},o.dragEnd=function(e,t){if(this.isDraggable){this.options.freeScroll&&(this.isFreeScrolling=!0);var i=this.dragEndRestingSelect();if(this.options.freeScroll&&!this.options.wrapAround){var n=this.getRestingPosition();this.isFreeScrolling=-n>this.slides[0].target&&-n<this.getLastSlide().target}else this.options.freeScroll||i!=this.selectedIndex||(i+=this.dragEndBoostSelect());delete this.previousDragX,this.isDragSelect=this.options.wrapAround,this.select(i),delete this.isDragSelect,this.dispatchEvent("dragEnd",e,[t])}},o.dragEndRestingSelect=function(){var e=this.getRestingPosition(),t=Math.abs(this.getSlideDistance(-e,this.selectedIndex)),i=this._getClosestResting(e,t,1),n=this._getClosestResting(e,t,-1);return i.distance<n.distance?i.index:n.index},o._getClosestResting=function(e,t,i){for(var n=this.selectedIndex,o=1/0,r=this.options.contain&&!this.options.wrapAround?function(e,t){return e<=t}:function(e,t){return e<t};r(t,o)&&(n+=i,o=t,null!==(t=this.getSlideDistance(-e,n)));)t=Math.abs(t);return{distance:o,index:n-i}},o.getSlideDistance=function(e,t){var i=this.slides.length,o=this.options.wrapAround&&i>1,r=o?n.modulo(t,i):t,s=this.slides[r];if(!s)return null;var a=o?this.slideableWidth*Math.floor(t/i):0;return e-(s.target+a)},o.dragEndBoostSelect=function(){if(void 0===this.previousDragX||!this.dragMoveTime||new Date-this.dragMoveTime>100)return 0;var e=this.getSlideDistance(-this.dragX,this.selectedIndex),t=this.previousDragX-this.dragX;return e>0&&t>0?1:e<0&&t<0?-1:0},o.staticClick=function(e,t){var i=this.getParentCell(e.target),n=i&&i.element,o=i&&this.cells.indexOf(i);this.dispatchEvent("staticClick",e,[t,n,o])},o.onscroll=function(){var e=s(),t=this.pointerDownScroll.x-e.x,i=this.pointerDownScroll.y-e.y;(Math.abs(t)>3||Math.abs(i)>3)&&this._pointerDone()},t}),function(e,t){"function"==typeof define&&define.amd?define("flickitybee/js/prev-next-button",["./flickitybee","unipointer/unipointer","fizzy-ui-utils/utils"],function(i,n,o){return t(e,i,n,o)}):"object"==typeof module&&module.exports?module.exports=t(e,require("./flickitybee"),require("unipointer"),require("fizzy-ui-utils")):t(e,e.Flickitybee,e.Unipointer,e.fizzyUIUtils)}(window,function(e,t,i,n){"use strict";var o="http://www.w3.org/2000/svg";function r(e,t){this.direction=e,this.parent=t,this._create()}r.prototype=Object.create(i.prototype),r.prototype._create=function(){this.isEnabled=!0,this.isPrevious=-1==this.direction;var e=this.parent.options.rightToLeft?1:-1;this.isLeft=this.direction==e;var t=this.element=document.createElement("button");t.className="flickitybee-button flickitybee-prev-next-button",t.className+=this.isPrevious?" previous":" next",t.setAttribute("type","button"),this.disable(),t.setAttribute("aria-label",this.isPrevious?"Previous":"Next");var i=this.createSVG();t.appendChild(i),this.parent.on("select",this.update.bind(this)),this.on("pointerDown",this.parent.childUIPointerDown.bind(this.parent))},r.prototype.activate=function(){this.bindStartEvent(this.element),this.element.addEventListener("click",this),this.parent.element.appendChild(this.element)},r.prototype.deactivate=function(){this.parent.element.removeChild(this.element),this.unbindStartEvent(this.element),this.element.removeEventListener("click",this)},r.prototype.createSVG=function(){var e=document.createElementNS(o,"svg");e.setAttribute("class","flickitybee-button-icon"),e.setAttribute("viewBox","0 0 100 100");var t=document.createElementNS(o,"path"),i=function(e){return"string"==typeof e?e:"M "+e.x0+",50 L "+e.x1+","+(e.y1+50)+" L "+e.x2+","+(e.y2+50)+" L "+e.x3+",50  L "+e.x2+","+(50-e.y2)+" L "+e.x1+","+(50-e.y1)+" Z"}(this.parent.options.arrowShape);return t.setAttribute("d",i),t.setAttribute("class","arrow"),this.isLeft||t.setAttribute("transform","translate(100, 100) rotate(180) "),e.appendChild(t),e},r.prototype.handleEvent=n.handleEvent,r.prototype.onclick=function(){if(this.isEnabled){this.parent.uiChange();var e=this.isPrevious?"previous":"next";this.parent[e]()}},r.prototype.enable=function(){this.isEnabled||(this.element.disabled=!1,this.isEnabled=!0)},r.prototype.disable=function(){this.isEnabled&&(this.element.disabled=!0,this.isEnabled=!1)},r.prototype.update=function(){var e=this.isPrevious?"prev_":"next_";this.parent.element.classList.remove("flickitybee_"+e+"disable","flickitybee_"+e+"enable");var t=this.parent.slides;if(this.parent.options.wrapAround&&t.length>1)this.enable();else{var i=t.length?t.length-1:0,n=this.isPrevious?0:i,o=this.parent.selectedIndex==n?"disable":"enable";this[o](),this.parent.element.classList.add("flickitybee_"+e+o)}},r.prototype.destroy=function(){this.deactivate(),this.allOff()},n.extend(t.defaults,{prevNextButtons:!0,arrowShape:{x0:10,x1:60,y1:50,x2:70,y2:40,x3:30}}),t.createMethods.push("_createPrevNextButtons");var s=t.prototype;return s._createPrevNextButtons=function(){this.options.prevNextButtons&&(this.prevButton=new r(-1,this),this.nextButton=new r(1,this),this.on("activate",this.activatePrevNextButtons))},s.activatePrevNextButtons=function(){this.prevButton.activate(),this.nextButton.activate(),this.on("deactivate",this.deactivatePrevNextButtons)},s.deactivatePrevNextButtons=function(){this.prevButton.deactivate(),this.nextButton.deactivate(),this.off("deactivate",this.deactivatePrevNextButtons)},t.PrevNextButton=r,t}),function(e,t){"function"==typeof define&&define.amd?define("flickitybee/js/page-dots",["./flickitybee","unipointer/unipointer","fizzy-ui-utils/utils"],function(i,n,o){return t(e,i,n,o)}):"object"==typeof module&&module.exports?module.exports=t(e,require("./flickitybee"),require("unipointer"),require("fizzy-ui-utils")):t(e,e.Flickitybee,e.Unipointer,e.fizzyUIUtils)}(window,function(e,t,i,n){function o(e){this.parent=e,this._create()}o.prototype=Object.create(i.prototype),o.prototype._create=function(){this.holder=document.createElement("ol"),this.holder.className="flickitybee-page-dots",this.dots=[],this.handleClick=this.onClick.bind(this),this.on("pointerDown",this.parent.childUIPointerDown.bind(this.parent))},o.prototype.activate=function(){this.setDots(),this.holder.addEventListener("click",this.handleClick),this.bindStartEvent(this.holder),this.parent.element.appendChild(this.holder)},o.prototype.deactivate=function(){this.holder.removeEventListener("click",this.handleClick),this.unbindStartEvent(this.holder),this.parent.element.removeChild(this.holder)},o.prototype.setDots=function(){var e=this.parent.slides.length-this.dots.length;e>0?this.addDots(e):e<0&&this.removeDots(-e)},o.prototype.addDots=function(e){for(var t=document.createDocumentFragment(),i=[],n=this.dots.length,o=n+e,r=n;r<o;r++){var s=document.createElement("li");s.className="dot",s.setAttribute("aria-label","Page dot "+(r+1)),t.appendChild(s),i.push(s)}this.holder.appendChild(t),this.dots=this.dots.concat(i)},o.prototype.removeDots=function(e){this.dots.splice(this.dots.length-e,e).forEach(function(e){this.holder.removeChild(e)},this)},o.prototype.updateSelected=function(){this.selectedDot&&(this.selectedDot.className="dot",this.selectedDot.removeAttribute("aria-current")),this.dots.length&&(this.selectedDot=this.dots[this.parent.selectedIndex],this.selectedDot.className="dot is-selected",this.selectedDot.setAttribute("aria-current","step"))},o.prototype.onTap=o.prototype.onClick=function(e){var t=e.target;if("LI"==t.nodeName){this.parent.uiChange();var i=this.dots.indexOf(t);this.parent.select(i)}},o.prototype.destroy=function(){this.deactivate(),this.allOff()},t.PageDots=o,n.extend(t.defaults,{pageDots:!0}),t.createMethods.push("_createPageDots");var r=t.prototype;return r._createPageDots=function(){this.options.pageDots&&(this.pageDots=new o(this),this.on("activate",this.activatePageDots),this.on("select",this.updateSelectedPageDots),this.on("cellChange",this.updatePageDots),this.on("resize",this.updatePageDots),this.on("deactivate",this.deactivatePageDots))},r.activatePageDots=function(){this.pageDots.activate()},r.updateSelectedPageDots=function(){this.pageDots.updateSelected()},r.updatePageDots=function(){this.pageDots.setDots()},r.deactivatePageDots=function(){this.pageDots.deactivate()},t.PageDots=o,t}),function(e,t){"function"==typeof define&&define.amd?define("flickitybee/js/player",["ev-emitter/ev-emitter","fizzy-ui-utils/utils","./flickitybee"],function(e,i,n){return t(e,i,n)}):"object"==typeof module&&module.exports?module.exports=t(require("ev-emitter"),require("fizzy-ui-utils"),require("./flickitybee")):t(e.EvEmitter,e.fizzyUIUtils,e.Flickitybee)}(window,function(e,t,i){function n(e){this.parent=e,this.state="stopped",this.onVisibilityChange=this.visibilityChange.bind(this),this.onVisibilityPlay=this.visibilityPlay.bind(this)}n.prototype=Object.create(e.prototype),n.prototype.play=function(){"playing"!=this.state&&(document.hidden?document.addEventListener("visibilitychange",this.onVisibilityPlay):(this.state="playing",document.addEventListener("visibilitychange",this.onVisibilityChange),this.tick()))},n.prototype.tick=function(){if("playing"==this.state){var e=this.parent.options.autoPlay;e="number"==typeof e?e:3e3;var t=this;this.clear(),this.timeout=setTimeout(function(){t.parent.next(!0),t.tick()},e)}},n.prototype.stop=function(){this.state="stopped",this.clear(),document.removeEventListener("visibilitychange",this.onVisibilityChange)},n.prototype.clear=function(){clearTimeout(this.timeout)},n.prototype.pause=function(){"playing"==this.state&&(this.state="paused",this.clear())},n.prototype.unpause=function(){"paused"==this.state&&this.play()},n.prototype.visibilityChange=function(){this[document.hidden?"pause":"unpause"]()},n.prototype.visibilityPlay=function(){this.play(),document.removeEventListener("visibilitychange",this.onVisibilityPlay)},t.extend(i.defaults,{pauseAutoPlayOnHover:!0}),i.createMethods.push("_createPlayer");var o=i.prototype;return o._createPlayer=function(){this.player=new n(this),this.on("activate",this.activatePlayer),this.on("uiChange",this.stopPlayer),this.on("pointerDown",this.stopPlayer),this.on("deactivate",this.deactivatePlayer)},o.activatePlayer=function(){this.options.autoPlay&&(this.player.play(),this.element.addEventListener("mouseenter",this))},o.playPlayer=function(){this.player.play()},o.stopPlayer=function(){this.player.stop()},o.pausePlayer=function(){this.player.pause()},o.unpausePlayer=function(){this.player.unpause()},o.deactivatePlayer=function(){this.player.stop(),this.element.removeEventListener("mouseenter",this)},o.onmouseenter=function(){this.options.pauseAutoPlayOnHover&&(this.player.pause(),this.element.addEventListener("mouseleave",this))},o.onmouseleave=function(){this.player.unpause(),this.element.removeEventListener("mouseleave",this)},i.Player=n,i}),function(e,t){"function"==typeof define&&define.amd?define("flickitybee/js/add-remove-cell",["./flickitybee","fizzy-ui-utils/utils"],function(i,n){return t(e,i,n)}):"object"==typeof module&&module.exports?module.exports=t(e,require("./flickitybee"),require("fizzy-ui-utils")):t(e,e.Flickitybee,e.fizzyUIUtils)}(window,function(e,t,i){var n=t.prototype;return n.insert=function(e,t){var i=this._makeCells(e);if(i&&i.length){var n=this.cells.length;t=void 0===t?n:t;var o=function(e){var t=document.createDocumentFragment();return e.forEach(function(e){t.appendChild(e.element)}),t}(i),r=t==n;if(r)this.slider.appendChild(o);else{var s=this.cells[t].element;this.slider.insertBefore(o,s)}if(0===t)this.cells=i.concat(this.cells);else if(r)this.cells=this.cells.concat(i);else{var a=this.cells.splice(t,n-t);this.cells=this.cells.concat(i).concat(a)}this._sizeCells(i),this.cellChange(t,!0)}},n.append=function(e){this.insert(e,this.cells.length)},n.prepend=function(e){this.insert(e,0)},n.remove=function(e){var t=this.getCells(e);if(t&&t.length){var n=this.cells.length-1;t.forEach(function(e){e.remove();var t=this.cells.indexOf(e);n=Math.min(t,n),i.removeFrom(this.cells,e)},this),this.cellChange(n,!0)}},n.cellSizeChange=function(e){var t=this.getCell(e);if(t){t.getSize();var i=this.cells.indexOf(t);this.cellChange(i)}},n.cellChange=function(e,t){var i=this.selectedElement;this._positionCells(e),this._getWrapShiftCells(),this.setGallerySize(),this.setPrevNextButtons();var n=this.getCell(i);n&&(this.selectedIndex=this.getCellSlideIndex(n)),this.selectedIndex=Math.min(this.slides.length-1,this.selectedIndex),this.emitEvent("cellChange",[e]),this.select(this.selectedIndex),t&&this.positionSliderAtSelected()},t}),function(e,t){"function"==typeof define&&define.amd?define("flickitybee/js/index",["./flickitybee","./drag","./prev-next-button","./page-dots","./player","./add-remove-cell","./lazyload"],t):"object"==typeof module&&module.exports&&(module.exports=t(require("./flickitybee"),require("./drag"),require("./prev-next-button"),require("./page-dots"),require("./player"),require("./add-remove-cell"),require("./lazyload")))}(window,function(e){return e}),function(e,t){"function"==typeof define&&define.amd?define("flickitybee-as-nav-for/as-nav-for",["flickitybee/js/index","fizzy-ui-utils/utils"],t):"object"==typeof module&&module.exports?module.exports=t(require("flickitybee"),require("fizzy-ui-utils")):e.Flickitybee=t(e.Flickitybee,e.fizzyUIUtils)}(window,function(e,t){e.createMethods.push("_createAsNavFor");var i=e.prototype;return i._createAsNavFor=function(){this.on("activate",this.activateAsNavFor),this.on("deactivate",this.deactivateAsNavFor),this.on("destroy",this.destroyAsNavFor);var e=this.options.asNavFor;if(e){var t=this;setTimeout(function(){t.setNavCompanion(e)})}},i.setNavCompanion=function(i){i=t.getQueryElement(i);var n=e.data(i);if(n&&n!=this){this.navCompanion=n;var o=this;this.onNavCompanionSelect=function(){o.navCompanionSelect()},n.on("select",this.onNavCompanionSelect),this.on("staticClick",this.onNavStaticClick),this.navCompanionSelect(!0)}},i.navCompanionSelect=function(e){var t=this.navCompanion&&this.navCompanion.selectedCells;if(t){var i,n=t[0],o=this.navCompanion.cells.indexOf(n),r=o+t.length-1,s=Math.floor((r-(i=o))*this.navCompanion.cellAlign+i);if(this.selectCell(s,!1,e),this.removeNavSelectedElements(),!(s>=this.cells.length)){var a=this.cells.slice(o,r+1);this.navSelectedElements=a.map(function(e){return e.element}),this.changeNavSelectedClass("add")}}},i.changeNavSelectedClass=function(e){this.navSelectedElements.forEach(function(t){t.classList[e]("is-nav-selected")})},i.activateAsNavFor=function(){this.navCompanionSelect(!0)},i.removeNavSelectedElements=function(){this.navSelectedElements&&(this.changeNavSelectedClass("remove"),delete this.navSelectedElements)},i.onNavStaticClick=function(e,t,i,n){"number"==typeof n&&this.navCompanion.selectCell(n)},i.deactivateAsNavFor=function(){this.removeNavSelectedElements()},i.destroyAsNavFor=function(){this.navCompanion&&(this.navCompanion.off("select",this.onNavCompanionSelect),this.off("staticClick",this.onNavStaticClick),delete this.navCompanion)},e}),function(e,t){"function"==typeof define&&define.amd?define(["flickitybee/js/index","fizzy-ui-utils/utils"],t):"object"==typeof module&&module.exports?module.exports=t(require("flickitybee"),require("fizzy-ui-utils")):t(e.Flickitybee,e.fizzyUIUtils)}(this,function(e,t){var i=e.Slide,n=i.prototype.updateTarget;i.prototype.updateTarget=function(){if(n.apply(this,arguments),this.parent.options.fade){var e=this.target-this.x,t=this.cells[0].x;this.cells.forEach(function(i){var n=i.x-t-e;i.renderPosition(n)})}},i.prototype.setOpacity=function(e){this.cells.forEach(function(t){t.element.style.opacity=e})};var o=e.prototype;e.createMethods.push("_createFade"),o._createFade=function(){this.fadeIndex=this.selectedIndex,this.prevSelectedIndex=this.selectedIndex,this.on("select",this.onSelectFade),this.on("dragEnd",this.onDragEndFade),this.on("settle",this.onSettleFade),this.on("activate",this.onActivateFade),this.on("deactivate",this.onDeactivateFade)};var r=o.updateSlides;o.updateSlides=function(){r.apply(this,arguments),this.options.fade},o.onSelectFade=function(){this.fadeIndex=Math.min(this.prevSelectedIndex,this.slides.length-1),this.prevSelectedIndex=this.selectedIndex},o.onSettleFade=function(){delete this.didDragEnd,this.options.fade},o.onDragEndFade=function(){this.didDragEnd=!0},o.onActivateFade=function(){this.options.fade&&this.element.classList.add("is-fade")},o.onDeactivateFade=function(){this.options.fade&&(this.element.classList.remove("is-fade"),this.slides.forEach(function(e){e.setOpacity("")}))};var s=o.positionSlider;o.positionSlider=function(){this.options.fade?(this.fadeSlides(),this.dispatchScrollEvent()):s.apply(this,arguments)};var a=o.positionSliderAtSelected;o.positionSliderAtSelected=function(){this.options.fade&&this.setTranslateX(0),a.apply(this,arguments)},o.fadeSlides=function(){this.slides.length},o.getFadeIndexes=function(){return this.isDragging||this.didDragEnd?this.options.wrapAround?this.getFadeDragWrapIndexes():this.getFadeDragLimitIndexes():{a:this.fadeIndex,b:this.selectedIndex}},o.getFadeDragWrapIndexes=function(){var e=this.slides.map(function(e,t){return this.getSlideDistance(-this.x,t)},this),i=e.map(function(e){return Math.abs(e)}),n=Math.min.apply(Math,i),o=i.indexOf(n),r=e[o],s=this.slides.length,a=r>=0?1:-1;return{a:o,b:t.modulo(o+a,s)}},o.getFadeDragLimitIndexes=function(){for(var e=0,t=0;t<this.slides.length-1;t++){var i=this.slides[t];if(-this.x<i.target)break;e=t}return{a:e,b:e+1}},o.wrapDifference=function(e,t){var i=t-e;if(!this.options.wrapAround)return i;var n=i+this.slideableWidth,o=i-this.slideableWidth;return Math.abs(n)<Math.abs(i)&&(i=n),Math.abs(o)<Math.abs(i)&&(i=o),i};var l=o._getWrapShiftCells;o._getWrapShiftCells=function(){this.options.fade||l.apply(this,arguments)};var u=o.shiftWrapCells;return o.shiftWrapCells=function(){this.options.fade||u.apply(this,arguments)},e}),function(e,t){"function"==typeof define&&define.amd?define(["flickitybee/js/index","fizzy-ui-utils/utils"],t):"object"==typeof module&&module.exports?module.exports=t(require("flickitybee"),require("fizzy-ui-utils")):e.Flickitybee=t(e.Flickitybee,e.fizzyUIUtils)}(window,function(e,t){"use strict";return e.createMethods.push("_createSync"),e.prototype._createSync=function(){this.syncers={};var e=this.options.sync;if(this.on("destroy",this.unsyncAll),e){var t=this;setTimeout(function(){t.sync(e)})}},e.prototype.sync=function(i){i=t.getQueryElement(i);var n=e.data(i);n&&(this._syncCompanion(n),n._syncCompanion(this))},e.prototype._syncCompanion=function(e){var t=this;function i(){var i=t.selectedIndex;e.selectedIndex!=i&&e.select(i)}this.on("select",i),this.syncers[e.guid]={flickitybee:e,listener:i}},e.prototype.unsync=function(i){i=t.getQueryElement(i);var n=e.data(i);this._unsync(n)},e.prototype._unsync=function(e){e&&(this._unsyncCompanion(e),e._unsyncCompanion(this))},e.prototype._unsyncCompanion=function(e){var t=e.guid,i=this.syncers[t];this.off("select",i.listener),delete this.syncers[t]},e.prototype.unsyncAll=function(){for(var e in this.syncers){var t=this.syncers[e];this._unsync(t.flickitybee)}},e}),function(e,t){"undefined"!=typeof module&&module.exports?module.exports=t():"function"==typeof define&&define.amd?define(t):this.$script=t()}(0,function(){function e(e,t){for(var i=0,n=e.length;i<n;++i)if(!t(e[i]))return l;return 1}function t(t,i){e(t,function(e){return i(e),1})}function i(r,s,a){function l(e){return e.call?e():d[e]}function c(){if(!--y)for(var i in d[v]=1,m&&m(),p)e(i.split("|"),l)&&!t(p[i],l)&&(p[i]=[])}r=r[u]?r:[r];var h=s&&s.call,m=h?s:a,v=h?r.join(""):s,y=r.length;return setTimeout(function(){t(r,function e(t,i){return null===t?c():(!i&&!/^https?:\/\//.test(t)&&o&&(t=-1===t.indexOf(".js")?o+t+".js":o+t),g[t]?(v&&(f[v]=1),2==g[t]?c():setTimeout(function(){e(t,!0)},0)):(g[t]=1,v&&(f[v]=1),void n(t,c)))})},0),i}function n(e,t){var i,n=s.createElement("script");n.onload=n.onerror=n[h]=function(){n[c]&&!/^c|loade/.test(n[c])||i||(n.onload=n[h]=null,i=1,g[e]=2,t())},n.async=1,n.src=r?e+(-1===e.indexOf("?")?"?":"&")+r:e,a.insertBefore(n,a.lastChild)}var o,r,s=document,a=s.getElementsByTagName("head")[0],l=!1,u="push",c="readyState",h="onreadystatechange",d={},f={},p={},g={};return i.get=n,i.order=function(e,t,n){!function o(r){r=e.shift(),e.length?i(r,o):i(r,t,n)}()},i.path=function(e){o=e},i.urlArgs=function(e){r=e},i.ready=function(n,o,r){n=n[u]?n:[n];var s=[];return!t(n,function(e){d[e]||s[u](e)})&&e(n,function(e){return d[e]})?o():function(e){p[e]=p[e]||[],p[e][u](o),r&&r(s)}(n.join("|")),i},i.done=function(e){i([null],e)},i}),function(e,t){var i,n;"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self,i=e.Cookies,(n=e.Cookies=t()).noConflict=function(){return e.Cookies=i,n})}(this,function(){"use strict";function e(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)e[n]=i[n]}return e}return function t(i,n){function o(t,o,r){if("undefined"!=typeof document){"number"==typeof(r=e({},n,r)).expires&&(r.expires=new Date(Date.now()+864e5*r.expires)),r.expires&&(r.expires=r.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var s="";for(var a in r)r[a]&&(s+="; "+a,!0!==r[a]&&(s+="="+r[a].split(";")[0]));return document.cookie=t+"="+i.write(o,t)+s}}return Object.create({set:o,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var t=document.cookie?document.cookie.split("; "):[],n={},o=0;o<t.length;o++){var r=t[o].split("="),s=r.slice(1).join("=");try{var a=decodeURIComponent(r[0]);if(n[a]=i.read(s,a),e===a)break}catch(e){}}return e?n[e]:n}},remove:function(t,i){o(t,"",e({},i,{expires:-1}))},withAttributes:function(i){return t(this.converter,e({},this.attributes,i))},withConverter:function(i){return t(e({},this.converter,i),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(i)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})});var CookiesBE=Cookies.noConflict();!function(e){"use strict";var t=e(window),i=e(document),n=e("body"),o=window.BEEroutes.root_url,r=t.width(),s=BEEconfigs.cacheName+BEEconfigs.cartCurrency+Shopify.country+o,a=r<768,l=r<1025;function u(e,t=!0){if(IsDesignMode&&t)return!1;var i="session"===e?window.sessionStorage:window.localStorage;try{return i.setItem("bee","test"),i.removeItem("bee"),!0}catch(e){return!1}}isStorageSpSession=u("session"),isStorageSpSessionAll=u("session",!1),isStorageSpdLocal=u("local"),isStorageSpdLocalAll=u("local",!1),"development"==Shopify.theme.role&&isStorageSpSessionAll&&sessionStorage.clear(),BEEThemeSP.$appendComponent=e("#bee-append-component"),BEEThemeSP.cacheNameFirst=s,BEEThemeSP.root_url="/"!=o?o+"/":"/",void 0===window.Shopify&&(window.Shopify={}),Shopify.bind=function(e,t){return function(){return e.apply(t,arguments)}},Shopify.setSelectorByValue=function(e,t){for(var i=0,n=e.options.length;i<n;i++){var o=e.options[i];if(t==o.value||t==o.innerHTML)return e.selectedIndex=i,i}},Shopify.addListener=function(e,t,i){e.addEventListener?e.addEventListener(t,i,!1):e.attachEvent("on"+t,i)},Shopify.postLink=function(e,t){var i=(t=t||{}).method||"post",n=t.parameters||{},o=document.createElement("form");for(var r in o.setAttribute("method",i),o.setAttribute("action",e),n){var s=document.createElement("input");s.setAttribute("type","hidden"),s.setAttribute("name",r),s.setAttribute("value",n[r]),o.appendChild(s)}document.body.appendChild(o),o.submit(),document.body.removeChild(o)},Shopify.CountryProvinceSelector=function(e,t,i){this.countryEl=document.getElementById(e),this.provinceEl=document.getElementById(t),this.provinceContainer=document.getElementById(i.hideElement||t),Shopify.addListener(this.countryEl,"change",Shopify.bind(this.countryHandler,this)),this.initCountry(),this.initProvince()},Shopify.CountryProvinceSelector.prototype={initCountry:function(){var e=this.countryEl.getAttribute("data-default");Shopify.setSelectorByValue(this.countryEl,e),this.countryHandler()},initProvince:function(){var e=this.provinceEl.getAttribute("data-default");e&&this.provinceEl.options.length>0&&Shopify.setSelectorByValue(this.provinceEl,e)},countryHandler:function(e){var t=(o=this.countryEl.options[this.countryEl.selectedIndex]).getAttribute("data-provinces"),i=JSON.parse(t);if(this.clearOptions(this.provinceEl),i&&0==i.length)this.provinceContainer.style.display="none";else{for(var n=0;n<i.length;n++){var o;(o=document.createElement("option")).value=i[n][0],o.innerHTML=i[n][1],this.provinceEl.appendChild(o)}this.provinceContainer.style.display=""}},clearOptions:function(e){for(;e.firstChild;)e.removeChild(e.firstChild)},setOptions:function(e,t){var i=0;for(t.length;i<t.length;i++){var n=document.createElement("option");n.value=t[i],n.innerHTML=t[i],e.appendChild(n)}}},BEEThemeSP.resizeEventBE=function(e,t,i){try{window.dispatchEvent(new Event("resize"))}catch(e){var n=window.document.createEvent("UIEvents");n.initUIEvent("resize",!0,!1,window,0),window.dispatchEvent(n)}},BEEThemeSP.debounce=function(e,t,i){var n;return function(){var o=this,r=arguments,s=i&&!n;clearTimeout(n),n=setTimeout(function(){n=null,i||t.apply(o,r)},e),s&&t.apply(o,r)}},BEEThemeSP.storageCurrency=function(){return isStorageSpdLocal?localStorage.getItem("BECurrency"):null},BEEThemeSP.fullHeightFirtSe=function(){var i,n,o,r=e("#MainContent >.bee-section:first").find(".bee_ratio_fh");0!=r.length&&(i=t.height(),(n=r.offset().top)<i&&(o=100-n/(i/100),r.css("--bee-aspect-ratio-fh",o+"vh")))},BEEThemeSP.handle=function(e){var t=(e=(e+"").toLowerCase()).replace(/'|"|\(|\)|\[|\]/g,"").replace(/[\s\x21-\x2f\x3a-\x40\x7b-\x7f^`\\[\]]+/g,"-").replace(/\W+/g,"-").replace(/^-+|-+$/g,"");return""==t?e:t},BEEThemeSP._handle=function(e){var t=(e=(e+"").toLowerCase()).replace(/'|"|\(|\)|\[|\]/g,"").replace(/[\s\x21-\x2f\x3a-\x40\x7b-\x7f^`\\[\]]+/g,"_").replace(/\W+/g,"_").replace(/^-+|-+$/g,"");return""==t?e:t},BEEThemeSP.escapeHtml=function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")},BEEThemeSP.descapeHtml=function(e){return e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'").replace(/&amp;/g,"&")},BEEThemeSP.Images=function(){return{preloadImages:function(t){e(t).each(function(){e("<img/>")[0].src=this})},getNewImageUrl:function(e,t=0,i=0){return t||i?(t&&(e=e+"&width="+t),i&&(e=e+"&height="+i),this.removeProtocol(e)):null},removeProtocol:function(e){return e.replace(/http(s)?:/,"")},lazyloadImagePath:function(e){return this.removeProtocol(e+"&width=1")}}}(),BEEThemeSP.getUID=function(e){do{e+=~~(1e6*Math.random())}while(document.getElementById(e));return e},BEEThemeSP.Carousel=function(){var i,n="is--playing",o="is--paused",r="is--active",s="is-nav-selected",u="is-shopify-xr__showing",c="bee-carousel__nav-item",h="is-selected",d={currentSlide:`.${h}`,wrapper:".bee-carousel-wrapper",pauseButton:".bee-carousel__pause",productMediaWrapper:"[data-product-single-media-wrapper]",mediaGroup:"[data-product-single-media-group]",dataMediaPlay:"data-is-mediaPlay",productMediaPlay:"[data-is-mediaPlay]"},f={adaptiveHeight:!1,autoPlay:!1,avoidReflow:!1,thumbNav:!1,thumbVertical:!1,navUI:!1,dotUI:!1,parallax:!1,status:!1,isMedia:!1,beeid:"19041994",beeidTab:"19041994",selectWithSelector:!1,scrollbar:!1,scrollbarDraggable:!1,fullwidthSide:!1,centerSlide:!1,isSimple:!1,minWidthLG:19041994,cellAlign:"center",cellAlignLG:"left",btnSmartTab:!1,activeTab:!1,customIcon:0,viewBox:"0 0 100 100",checkVisibility:!0,autoPlayBE:!1,dragThreshold:7,fade:!1,friction:.8,initialIndex:0,pageDots:!1,pauseAutoPlayOnHover:!1,prevNextButtons:!1,selectedAttraction:.14},p="select.carousel",g="click.navbee",m="click.dotbee",v="click.thumbbee",y={video:'<svg viewBox="0 0 384 512"><use href="#icon-thumb-video"/></svg>',external_videoyoutube:'<svg viewBox="0 0 576 512"><use href="#icon-external-youtube"/></svg>',external_videovimeo:'<svg viewBox="0 0 448 512"><use href="#icon-external-vimeo"/></svg>',model:'<svg viewBox="0 0 512 512"><use href="#icon-thumb-model"/></svg>',360:'<svg viewBox="0 0 640 512"><use href="#icon-thumb-360"/></svg>'},b="destroy.bee",x="",w="{}",E=!1,S=window.CSS.supports("scroll-behavior","smooth");const C={start:"left",end:"right"};function T(n){this.el=n,this.$el=e(n),this.UID=BEEThemeSP.getUID(),i="resize.carousel"+this.UID;const o=JSON.parse(this.$el.attr("data-flickitybee-js")||w);this.args=Object.assign({},f,o);let r=this.args.cellAlign,s=this.args.cellAlignLG;this.args.cellAlign=C[r]||r,this.args.cellAlignLG=C[s]||s,this.IdSlider=this.args.beeid,this.args.fade=this.$el.hasClass("bee-slide-eff-fade")||this.args.fade,this.args.rightToLeft=isThemeRTL,this.$deferredMedia=this.$el.find(d.productMediaWrapper),this.args.isMedia&&(this.isMedia=this.$el.find("[data-deferred-media]").length>0||this.$el.find('[data-media-type="360"]').length>0,this.isMedia&&(this.$groupBtn=e(`[ data-bee-group-btns="${this.args.beeid}"]`),this.$mediaGroup=this.$el.closest(d.mediaGroup))),this.args.wrapAround&&l&&(this.args.dragThreshold=55),this.args.on={ready:this.init.bind(this),change:this.slideChange.bind(this),select:this.slideSelect.bind(this),settle:this.afterChange.bind(this)},this.args.thumbNav&&this._initCarouselNav(),this.args.avoidReflow&&function(e){if(!e.id)return;var t=e.firstChild;for(;null!=t&&3==t.nodeType;)t=t.nextSibling;var i=document.createElement("style");i.innerHTML=`#${e.id} .flickity-viewport{height:${t.offsetHeight}px}`,document.head.appendChild(i)}(n),this.$wrapper=this.$el.closest(d.wrapper),this.wrapper=this.$wrapper[0],this.pauseBtn=this.wrapper?this.wrapper.querySelector(d.pauseButton):null,this.$pauseBtn=e(this.pauseBtn),this.isPlaying=this.args.autoPlay||this.args.autoPlayBE,this.args.cellAlignOriginnal=this.args.cellAlign,this.hasMWLG=19041994!=this.args.minWidthLG,this.hasMWLG&&this.args.minWidthLG<=t.width()&&(this.args.cellAlign=this.args.cellAlignLG),this.args.centerSlide&&this.args.wrapAround&&(this.args.cellAlign="center",this.args.cellAlignOriginnal="center"),this.$carousel=this.$el.flickitybee(this.args);var a=this;this.flkty=this.$carousel.data("flickitybee"),this.selectedIndex=this.flkty.selectedIndex,setTimeout(a.actionsAPI("resize"),0),setTimeout(function(){a.$el.addClass("bee-enabled"),a.args.isSimple&&(a.actionsAPI("resize"),setTimeout(a.actionsAPI("resize"),150))},100),this._selectChange(),this.isPlaying&&this.wrapper&&this.pauseBtn&&this.pauseBtn.addEventListener("click",this._togglePause.bind(this)),this.args.navUI&&this._customNavUI(),this.args.dotUI&&this._customDotUI(),this.args.parallax&&this._parallaxEffect(),this.args.status&&this._status(),this.args.isFilter&&this._updateCarousel(),this.args.btnSmartTab&&this._updateBtnTab(),this.args.prevNextButtons&&this._customIcon(this.$carousel.find(".flickitybee-button.previous"),this.$carousel.find(".flickitybee-button.next")),this.args.selectWithSelector&&this._selectWithSelector(),this.args.scrollbar&&!this.args.scrollbarDraggable&&this._scrollbarCarousel(),this.args.scrollbar&&this.args.scrollbarDraggable&&this._scrollbarDraggableCarousel();var u=this.args.thumbVertical;t.width()<1025&&u&&(this.args.thumbVertical=!1),t.on(i,BEEThemeSP.debounce(300,function(){a.hasMWLG&&(a.args.minWidthLG<=t.width()?a.flkty.options.cellAlign=a.args.cellAlignLG:a.flkty.options.cellAlign=a.args.cellAlignOriginnal),a.actionsAPI("resize"),t.width()<1025&&u?a.args.thumbVertical=!1:t.width()>1024&&u&&(a.args.thumbVertical=!0)}.bind(this))),a.$el.on(b,a.destroy.bind(a))}return T.prototype=Object.assign({},T.prototype,{init:function(e){this.currentSlide=this.el.querySelector(d.currentSlide),this.args.autoPlayBE&&this.autoPlayBE(),this.$pauseBtn.addClass(n),this.args.callbacks&&this.args.callbacks.onInit&&"function"==typeof this.args.callbacks.onInit&&this.args.callbacks.onInit(this.currentSlide)},slideChange:function(t){this.args.thumbNav&&this.thumbnailsGoto(t),this.args.callbacks&&this.args.callbacks.onChange&&"function"==typeof this.args.callbacks.onChange&&this.args.callbacks.onChange(t),this.$carouselNavPrev&&this.$carouselNavPrev.length&&(this.$carouselNavPrev[0].classList.toggle("is--hide",0===t),this.$carouselNavNext[0].classList.toggle("is--hide",t===this.$carouselNavLinks.length-1)),this.$carousel.find("video").length>0&&(this.$carousel.find("video").each(function(e,t){t.pause()}),e(this.currentSlide).each(function(t,i){e(this).find("video")[0].play()})),this.isMedia&&this._switchMedia(t),this.args.autoPlayBE&&this.autoPlayBE()},autoPlayBE:function(){var e=this;e.time||(e.wrapper&&e.wrapper.style.setProperty("--play-carousel-speed",`${e.args.autoPlayBE}ms`),e.time={}),e.time.START=(new Date).getTime(),e.time.END=e.time.START+e.args.autoPlayBE,e.$pauseBtn.removeClass(n),e.isPlaying&&(clearTimeout(e.stayTimeout),e.stayTimeout=setTimeout(function(){e.actionsAPI("next",!0)},e.args.autoPlayBE),clearTimeout(e.pauseBtnTimeout),e.pauseBtnTimeout=setTimeout(function(){e.$pauseBtn.addClass(n)},20)),e.time.REMAINING=e.args.autoPlayBE},slideSelect:function(e){},afterChange:function(e){this.args.thumbNav&&this.thumbnailsGoto(this.flkty.selectedIndex)},destroy:function(){this.$carouselNav&&this.$carouselNav.find("."+s).removeClass(s),this.actionsAPI("destroy"),this.$el.off(b),t.off(i)},_togglePause:function(){var t=this,i=t.$pauseBtn.data("pause-title"),r=t.$pauseBtn.data("play-title");t.pauseBtn.classList.contains(o)?(t.pauseBtn.classList.remove(o),t.wrapper.classList.remove(o),e(".bee-tooltip .tooltip-inner").text(i),t.pauseBtn.setAttribute("data-original-title",i),t.isPlaying=!0,t.args.autoPlayBE&&(t.time.END=(new Date).getTime()+t.time.REMAINING,t.stayTimeout=setTimeout(function(){t.actionsAPI("next",!0)},t.time.REMAINING))):(t.wrapper.classList.add(o),t.pauseBtn.classList.add(o),e(".bee-tooltip .tooltip-inner").text(r),t.pauseBtn.setAttribute("data-original-title",r),t.isPlaying=!1,t.args.autoPlayBE&&(clearTimeout(t.stayTimeout),t.time.REMAINING=t.time.END-(new Date).getTime())),t.isPlaying&&t.$pauseBtn.addClass(n)},actionsAPI:function(e,t=!1){this.$carousel.flickitybee(e,t)},_selectChange:function(e){var t=this;t.$carousel.on("select.flickitybee",function(e,i){t.$carousel.trigger(p)})},_customNavUI:function(t,i){var n=this,o=n.args.wrapAround||!1;t=t||e(".btn__prev--"+n.IdSlider),0!=(i=i||e(".btn__next--"+n.IdSlider)).length&&(n._customIcon(t,i),t.off(g).on(g,function(){n.actionsAPI("previous")}),i.off(g).on(g,function(){n.actionsAPI("next")}),n._setButtonStatus(o,t,i),n.$carousel.on(p,function(){n._setButtonStatus(o,t,i)}))},_setButtonStatus:function(e,t,i){let n=i.closest("[data-tab-active]"),o=this.flkty.selectedCell.target;n.addClass("prev_next_added"),n.removeClass("tab_prev_next_disable"),this.flkty.slides.length<2?(n.addClass("tab_prev_next_disable"),t.attr("disabled","disabled"),i.attr("disabled","disabled")):o!=this.flkty.cells[0].target||e?o!=this.flkty.getLastCell().target||e?(t.removeAttr("disabled"),i.removeAttr("disabled")):(i.attr("disabled","disabled"),t.removeAttr("disabled")):(t.attr("disabled","disabled"),i.removeAttr("disabled"))},_customDotUI:function(){var t=this,i=e(".btn_group--cells"+t.IdSlider),n=i.find(".btn_dotbee");0!=n.length&&(n.eq(t.flkty.selectedIndex).addClass(h),i.on(m,".btn_dotbee",function(){t.$carousel.flickitybee("select",e(this).index())}),t.$carousel.on(p,function(){n.filter(`.${h}`).removeClass(h),n.eq(t.flkty.selectedIndex).addClass(h)}))},_parallaxEffect:function(){var e=this;if(e.$carousel.hasClass("slide-eff-parallax")){if(a)var t=".bee-slide .bee-img-as-bg.bee-d-md-none";else t=".bee-slide .bee-img-as-bg.bee-d-md-block";var i=e.$carousel.find(t);0!=i.length&&e.$carousel.on("scroll.flickitybee",function(t,n){e.flkty.slides.forEach(function(t,n){var o=i[n],r=-1*(t.target+e.flkty.x)/3;o.style.transform="translateX( "+r+"px)"})})}},_status:function(){var t=this,i=e(".carousel--status"+t.IdSlider);if(0!=i.length){var n=i.find("[data-current-slide]"),o=i.find("[data-total-number]"),r=t.args.pad||!1,s=0,a=0;l(),t.$carousel.on(p,l)}function l(){s=t.flkty.selectedIndex+1,a=t.flkty.slides.length,r&&(s=u(s,2),a=u(a,2)),n.text(s),o.text(a)}function u(e,t){return(e=e.toString()).length<t?u("0"+e,t):e}},_initCarouselNav:function(){var t=this;t.$carouselNav=e(".carousel__nav--"+t.IdSlider),0!=t.$carouselNav.length&&(E||t.addThumbIcons(),t.thumbnailsMarkup(),t.$carouselNavLinks.eq([t.args.initialIndex]).addClass(s),t.$carouselNavPrev=e(`[data-thumb-btn__prev="${t.IdSlider}"]`),t.$carouselNavNext=e(`[data-thumb-btn__next="${t.IdSlider}"]`),(this.$carouselNavPrev.length||this.$carouselNavNext.length)&&(t.$carouselNavPrev.on(v,function(){t.actionsAPI("previous")}),t.$carouselNavNext.on(v,function(){t.actionsAPI("next")})),t.args.isFilter?t.$carouselNav.on(v,"."+c,function(i){let n=t.$carouselNav.find(`.${c}:visible`).index(e(this));t.$carousel.flickitybee("select",n)}):t.$carouselNav.on(v,"."+c,function(i){t.$carousel.flickitybee("select",e(this).index())}))},addThumbIcons:function(){var t=e("template[data-icons-thumb]");0!=t.length&&(BEEThemeSP.$appendComponent.after(t.html()),E=!0)},thumbnailsMarkup:function(){x="",this.$el.find("[data-main-slide]").each(function(){var t=e(this),i=t.hasClass("is--media-hide")?"is--media-hide":"",n=t.data("media-type"),o=t.data("vhost")||"",r=t.data("grname")||"",s=t.data("grpvl")||"",a=t.find(".bee_ratio").attr("style"),l=t.find("img"),u=BEEThemeSP.Images.lazyloadImagePath(l.attr("data-master")||l.attr("data-src")),h=y[n+o]||"";x+=`<div class="bee-col-item ${c} ${i}" data-grname="${r}" data-grpvl="${s}" data-mdtype="${n}" data-vhost="${o}"><div class="bee_ratio bee-carousel__nav-inner bee-bg-11" style="${a};background: url(${u})"><img alt="${BEEThemeSP.escapeHtml(l.attr("alt"))}" loading="lazy" class="lazyloadbee" data-src="${u}" data-widths="[80, 120, 160 ,180, 360, 540]" data-optimumx="1.8" data-sizes="auto" src="${u}"><span class="bee-thumbnail__badge bee-not-style bee-op-0" aria-hidden="true">${h}</span></div></div>`}),this.$carouselNav.empty().append(x).addClass("is--nav-ready"),this.$carouselNav.parents(".bee-parent-nav").addClass("thumb-nav-active"),this.$carouselNavLinks=this.$carouselNav.find(`.${c}:not(.is--media-hide):visible`)},thumbnailsGoto:function(t){var i=this.$carouselNavLinks.eq(t),n=i[0];if(this.$carouselNavScroller=e(`[data-thumb__scroller="${this.IdSlider}"]`),this.$carouselNav.find("."+s).removeClass(s),i.addClass(s),this.args.thumbVertical){var o=n.offsetTop;S?this.$carouselNavScroller[0].scrollTop=o-100:this.$carouselNavScroller.stop().animate({scrollTop:o-100},350)}else{var r=n.offsetLeft;S?this.$carouselNavScroller[0].scrollLeft=r-100:this.$carouselNavScroller.stop().animate({scrollLeft:r-100},350)}},_switchMedia:function(t){this.$el.find(`.flickitybee-slider ${d.productMediaWrapper}`).eq(this.selectedIndex);var i=this.$el.find(`.flickitybee-slider ${d.productMediaWrapper}`).eq(t);this.selectedIndex=this.flkty.selectedIndex,this.$groupBtn.removeAttr("hidden"),this.$mediaGroup.removeClass(u),this.flkty.options.draggable=!0,e(d.productMediaPlay).each(function(){this.dispatchEvent(new CustomEvent("mediaHidden",{bubbles:!0,cancelable:!0})),this.removeAttribute(d.dataMediaPlay)}),i.is("[data-deferred-media]")||i.is('[data-media-type="360"]')?(this.flkty.options.draggable=!1,this.flkty.updateDraggable(),i.is('[data-media-type="model"]')?this.$mediaGroup.addClass(u):this.$groupBtn.attr("hidden",!0),i.attr(d.dataMediaPlay,""),i[0].dispatchEvent(new CustomEvent("mediaVisible",{bubbles:!0,cancelable:!0}))):this.flkty.updateDraggable()},_updateCarousel:function(t){var i=this;i.$carousel.on("update.flickitybee",function(t){e(this).flickitybee("deactivate").flickitybee("activate"),i.$carouselNav&&(i.$carouselNavLinks=i.$carouselNav.find(`.${c}:not(.is--media-hide):visible`))})},_updateBtnTab:function(t){var i=this;let n=e("#btn-tab-smart__prev--"+i.args.beeidTab),o=e("#btn-tab-smart__next--"+i.args.beeidTab);0!=o.length&&(i.$carousel.on("updateBtnTab.flickitybee",function(e){n.off(g),o.off(g),i._customNavUI(n,o),i._customIcon(n,o)}),i.args.activeTab&&i.$carousel.trigger("updateBtnTab.flickitybee"))},_customIcon:function(e,t){let i=this.args.customIcon;if(!i)return;let n=`<svg viewBox="${this.args.viewBox}" class="flickitybee-button-icon bee-cus-icon-slider is--cus-ic-${i}"><use href="#svg-slider-btn___prev-${i}"></svg><span class="bee-flicky-btn-text">${BEEstrings.btn_prev}</span>`,o=`<svg viewBox="${this.args.viewBox}" class="flickitybee-button-icon bee-cus-icon-slider is--cus-ic-${i}"><use href="#svg-slider-btn___next-${i}"></svg><span class="bee-flicky-btn-text">${BEEstrings.btn_next}</span>`;e.html(n),t.html(o)},_selectWithSelector:function(){var t=this;let i=e(`[data-carousel-id="${t.IdSlider}"]`);if(0==i.length)return;i.on("click",function(){t.$carousel.flickitybee("select",e(this).index())});let n=t.$carousel.find("[data-flickity-link]");t.$carousel.on(p,function(){i.filter(`.${r}`).removeClass(r),i.eq(t.flkty.selectedIndex).addClass(r);let o=e(t.flkty.selectedElement).data("url");n[0]&&o&&n.attr("href",o)})},_scrollbarCarousel:function(){var i=this;function n(){clearTimeout(i.recalculateScrollSizeTimeout),i.recalculateScrollSizeTimeout=setTimeout(function(){i.$scrollbar.css({"--width":i.flkty.size.width*i.flkty.size.width/i.flkty.slideableWidth+"px"}),i.flkty.size.width>=i.flkty.slideableWidth?i.$scrollbar.addClass("is--hidden"):i.$scrollbar.removeClass("is--hidden"),i.scrollInnerSize=i.$scrollbarDrag.width()/i.$scrollbar.width()},155)}i.$scrollbar=e(".bee-carousel-scrollbar--"+i.IdSlider),i.$scrollbar[0]&&(i.$scrollbarDrag=i.$scrollbar.find(".bee-carousel-scrollbar__drag"),i.scrollInnerSize=0,i.recalculateScrollSizeTimeout,n(),t.on(`resize.scrollbar${i.IdSlider}`,BEEThemeSP.debounce(400,function(){n()})),i.$carousel.on("scroll.flickitybee",function(e,t){i.$scrollbar.css({"--left":t*(1-i.scrollInnerSize)*100+"%"})}))},_scrollbarDraggableCarousel:function(){}}),T}(),window.liquidWindowWidth=function(){return window.innerWidth},BEEThemeSP.initCarousel=function(){var t=e(".flickitybee:not(.flickitybee-later):not(.flickitybee-enabled)");0!=t.length&&t.each(function(){this.flickitybee=new BEEThemeSP.Carousel(this)})},BEEThemeSP.initWhenVisible=function(e){var t=e.threshold?e.threshold:0;new IntersectionObserver((t,i)=>{t.forEach(t=>{t.isIntersecting&&"function"==typeof e.callback&&(e.callback(),i.unobserve(t.target))})},{rootMargin:"0px 0px "+t+"px 0px"}).observe(e.element)},BEEThemeSP.Isotopebee=function(){function t(e){e.removeClass("isotopebee-later");var t=e.attr("data-isotopebee-js")||"{}";e.isotopebee(JSON.parse(t)).addClass("isotopebee-enabled")}function i(){var t=e("[data-isotopebee-filter]");if(0!=t.length){var i=IsDesignMode?"[data-isotopebee-filter]>button":">button";(IsDesignMode?n:t).on("click",i,function(){var t=e(this),i=t.closest("[data-isotopebee-filter]"),n=i.data("grid"),o=n?e(n):i.next();i.find(".is--active").removeClass("is--active"),t.addClass("is--active"),o.isotopebee({filter:t.attr("data-filter")})})}}return{initEach:function(){var n=e(".isotopebee:not(.isotopebee-later):not(.isotopebee-enabled)");0!=n.length&&(n.each(function(){t(e(this))}),i())},init:t,filter:i}}(),BEEThemeSP.BEEWrappTable=function(){e("table:not('.bee-table-res')").wrap("<div class='bee-table-res-df'></div>")},BEEThemeSP.AccordionMobileInt=function(){var t=e(".bee-accordion-mb-true");0!=t.length&&(e(window).width()<=1024?t.removeClass("bee-type-tabs").addClass("bee-type-accordion"):t.removeClass("bee-type-accordion").addClass("bee-type-tabs"))},BEEThemeSP.CartAttrHidden=function(){var t=e("[data-cart-attr-rm]"),i=0;if(0!=t.length&&!BEEconfigs.CartAttrHidden){var o=setInterval(function(){t.val(""),15==i&&clearInterval(o),i++},500);n.on("click",'button[type=submit][name="checkout"]',function(e){t.val("")})}},BEEThemeSP.announcement=function(){var t=".bee-announcement-bar__close",i=e(".bee-announcement-bar"),n=i.attr("data-ver"),o=BEEconfigs.theme,r="bee_announcement_"+o+"_"+n,s=parseInt(i.attr("data-date"));if(0!=i.length&&"closed"!=CookiesBE.get(r)){var a="bee_announcement_"+o+"_"+("1_nt"==n?"2_nt":"1_nt");"closed"==CookiesBE.get(a)&&CookiesBE.remove(a),e(t).on("click",function(e){i.css("min-height","auto").attr("aria-hidden",!0).slideUp(),IsDesignMode||CookiesBE.set(r,"closed",{expires:s,path:"/"})})}},BEEThemeSP.Marquee3k=function(){var i,n={enabled:"marqueebee-enabled",animation:"bee-marquee--animation",duplicate:"bee-marquee--duplicate"};function o(o){this.el=o,this.$el=e(o),this.width=0,this.UID=BEEThemeSP.getUID(),i="resize.marquee"+this.UID,this.marquee3kItem=o.querySelector(".bee-marquee__item"),IsDesignMode?setTimeout(this.resizeHandler.bind(this),100):this.resizeHandler(),this.$el.addClass(n.enabled);var r=this;t.on(i,BEEThemeSP.debounce(300,function(){r.resizeHandler()}.bind(this)))}return o.prototype=Object.assign({},o.prototype,{resizeHandler:function(){if(this.width!=window.innerWidth){this.width=window.innerWidth,this.marquee3kItem.classList.remove(n.animation);var e=this.el.querySelectorAll("."+n.duplicate);e.length&&e.forEach(e=>e.remove());var t=window.innerWidth/this.marquee3kItem.offsetWidth,i=!1;t=t==1/0?5:t;for(var o=0;o<t;o++)(i=this.marquee3kItem.cloneNode(!0)).setAttribute("aria-hidden",!0),i.classList.add(n.duplicate),i.classList.add(n.animation),this.el.append(i);this.marquee3kItem.classList.add(n.animation)}}}),o}(),BEEThemeSP.initMarquee3k=function(){var t=e(".bee-marquee:not(.marqueebee-enabled)");0!=t.length&&t.each(function(){this.marquee3k=new BEEThemeSP.Marquee3k(this)})},BEEThemeSP.initVarHeight=function(){var i="[data-get-height]:not(.var-css-enabled)",n=e(i);function o(){n.each(function(){e(this).closest(".bee-section").css("--var-bee-height",e(this).height()+"px")})}0!=n.length&&(o(),t.on("resize.varHeight",BEEThemeSP.debounce(550,function(){n=e(i),o()})))};(function(){if(window.location.search.indexOf("_posted=true")<0)return;let t=localStorage.getItem("bee-recentform")||"xyz";e(` form:not(${window.location.hash}):not(#${t})`).each(function(){e(this).find("[data-bee-response-form]").hide()})})(),window.location.href.indexOf(BEEconfigs.preViewBar)<0||e("html").addClass("is--hidden-previewbar");BEEThemeSP.Helpers=function(){return{disableBodyScroll:function(){var e,t=!1,i=!1;Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(e){var t=this;if(!document.documentElement.contains(el))return null;do{if(t.matches(e))return t;t=t.parentElement}while(null!==t);return el});var n=function(e){!1!==i&&e.target.closest(t)||e.preventDefault()},o=function(t){1===t.targetTouches.length&&(e=t.targetTouches[0].clientY)},r=function(t){if(1===t.targetTouches.length){var n=t.targetTouches[0].clientY-e;0===i.scrollTop&&n>0&&t.preventDefault(),i.scrollHeight-i.scrollTop<=i.clientHeight&&n<0&&t.preventDefault()}};return function(e,s){void 0!==s&&(t=s,i=document.querySelector(s)),!0===e?(!1!==i&&(i.addEventListener("touchstart",o,!1),i.addEventListener("touchmove",r,!1)),document.body.addEventListener("touchmove",n,!1)):(!1!==i&&(i.removeEventListener("touchstart",o,!1),i.removeEventListener("touchmove",r,!1)),document.body.removeEventListener("touchmove",n,!1))}},debounce:function(e,t,i){var n;return function(){var o=this,r=arguments,s=i&&!n;clearTimeout(n),n=setTimeout(function(){n=null,i||e.apply(o,r)},t),s&&e.apply(o,r)}},getScript:function(e,t){return new Promise(function(i,n){var o=document.createElement("script"),r=t||document.getElementsByTagName("script")[0];function s(e,t){(t||!o.readyState||/loaded|complete/.test(o.readyState))&&(o.onload=null,o.onreadystatechange=null,o=void 0,t?n():i())}o.async=!0,o.defer=!0,o.onload=s,o.onreadystatechange=s,o.src=e,r.parentNode.insertBefore(o,r)})},loadScript:function(t,i){null!=t&&e.ajax({url:t,dataType:"script",success:i,async:!0})},prepareTransition:function(e){e.addEventListener("transitionend",function(e){e.currentTarget.classList.remove("is-transitioning")},{once:!0});var t=0;["transition-duration","-moz-transition-duration","-webkit-transition-duration","-o-transition-duration"].forEach(function(i){var n=getComputedStyle(e)[i];n&&(n.replace(/\D/g,""),t||(t=parseFloat(n)))}),0!==t&&(e.classList.add("is-transitioning"),e.offsetWidth)},cookiesEnabled:function(){var e=navigator.cookieEnabled;return e||(document.cookie="testcookie",e=-1!==document.cookie.indexOf("testcookie")),e},promiseStylesheet:function(e){var t=e,i=t.match(/[\w-]+\.(css)/g)[0];return void 0===BEEThemeSP.stylesheetPromise[i]&&(BEEThemeSP.stylesheetPromise[i]=new Promise(function(e){let i=document.getElementsByTagName("head")[0],n=document.createElement("link");n.rel="stylesheet",n.type="text/css",n.href=t,n.media="all",i.appendChild(n),n.loaded&&e(),n.addEventListener("load",function(){setTimeout(e,100)})})),BEEThemeSP.stylesheetPromise[i]}}}(),BEEThemeSP.stylesheetPromise={},BEEThemeSP.Drawer=function(){var t={overlayVisible:"is--visible"},o=!1,r="click.drawer",s="keyup.drawer",a="data-drawer-options",l="data-drawer-delay",u=" is--opend-drawer",c=(e("html"),e(".bee-close-overlay")),h="bee-lock-scroll",d="[data-bee-scroll-me]";function f(i,r,s=!1){if(!o){i&&i.preventDefault();var l,f,p=(l=r.attr(a),JSON.parse(l||f||"{}")),g=s?r:e(p.id);g.trigger("opendDrawer"),g.attr("aria-hidden","false"),c.addClass(t.overlayVisible),o=!0,n.addClass(h+u),BEEThemeSP.Helpers.disableBodyScroll(!0,d)}}function p(i){if(o){i&&i.preventDefault();var a=e(".bee-drawer[aria-hidden=false]");a.attr("aria-hidden","true"),c.removeClass(t.overlayVisible),o=!1,a.one("transitionend webkitTransitionEnd oTransitionEnd",function(){n.removeClass(h+u),BEEThemeSP.Helpers.disableBodyScroll(!1,d)}),c.off(r),n.off(r),n.off(s)}}function g(){c.on(r,function(e){p(e)}),n.on(r,"[data-drawer-close]",function(e){p(e)}),n.on("keyup.drawer",function(e){27===e.keyCode&&p(e)})}return{init:function(){i.on(r,"["+a+"]:not(["+l+"])",function(t){f(t,e(this)),g()}),function(){let t=e("[data-sidebar-trigger]"),i=e(".bee-btn-sidebar");0!=t.length&&0!=i.length&&t.on(r,function(e){e.preventDefault(),i.trigger(r)})}()},opend:function(e){o=!1,f(null,e,!0),g()},close:p,remove:function(t){e("["+a+'*="#'+t+'"]').removeAttr(l)}}}()}(jQuery_BENT),jQuery_BENT(document).ready(function(e){BEEThemeSP.fullHeightFirtSe(),BEEThemeSP.initVarHeight(),BEEThemeSP.initMarquee3k(),BEEThemeSP.initCarousel(),BEEThemeSP.Isotopebee.initEach(),BEEThemeSP.announcement(),BEEThemeSP.BEEWrappTable(),BEEThemeSP.CartAttrHidden(),BEEThemeSP.Drawer.init(),"fetch"in window&&"assign"in Object&&"ResizeObserver"in window&&"IntersectionObserver"in window&&"DateTimeFormat"in Intl?$script(BEEconfigs.script2,function(){IsDesignMode&&$script(BEEconfigs.script9),$script(BEEconfigs.script10)}):$script(BEEconfigs.script1,function(){$script(BEEconfigs.script2,function(){IsDesignMode&&$script(BEEconfigs.script9),$script(BEEconfigs.script10)})}),e("[data-bee-type]").length>0&&$script(BEEconfigs.script12d)}),jQuery_BENT(window).on("resize",function(){BEEThemeSP.AccordionMobileInt()});