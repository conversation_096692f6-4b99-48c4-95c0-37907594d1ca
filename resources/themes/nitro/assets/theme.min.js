"undefined"!=typeof exports&&(exports=void 0),function(){var t={2:function(t,e,i){i="undefined"!=typeof window?window:void 0!==i.g?i.g:"undefined"!=typeof self?self:{},t.exports=i},1:function(t){t.exports=function(t){"complete"===document.readyState||"interactive"===document.readyState?t.call():document.attachEvent?document.attachEvent("onreadystatechange",function(){"interactive"===document.readyState&&t.call()}):document.addEventListener&&document.addEventListener("DOMContentLoaded",t)}},8:function(t,e,i){"use strict";i.r(e);var n=i(1),a=(n=i.n(n),i(2));function o(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var n,a,o=[],r=!0,s=!1;try{for(i=i.call(t);!(r=(n=i.next()).done)&&(o.push(n.value),!e||o.length!==e);r=!0);}catch(t){s=!0,a=t}finally{try{r||null==i.return||i.return()}finally{if(s)throw a}}return o}}(t,e)||function(t,e){if(t){if("string"==typeof t)return r(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(i="Object"===i&&t.constructor?t.constructor.name:i)||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?r(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var l,c,u=a.window.navigator,f=-1<u.userAgent.indexOf("MSIE ")||-1<u.userAgent.indexOf("Trident/")||-1<u.userAgent.indexOf("Edge/"),p=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(u.userAgent),h=function(){for(var t="transform WebkitTransform MozTransform".split(" "),e=document.createElement("div"),i=0;i<t.length;i+=1)if(e&&void 0!==e.style[t[i]])return t[i];return!1}();function m(){c=p?(!l&&document.body&&((l=document.createElement("div")).style.cssText="position: fixed; top: -9999px; left: 0; height: 100vh; width: 0;",document.body.appendChild(l)),(l?l.clientHeight:0)||a.window.innerHeight||document.documentElement.clientHeight):a.window.innerHeight||document.documentElement.clientHeight}m(),a.window.addEventListener("resize",m),a.window.addEventListener("orientationchange",m),a.window.addEventListener("load",m),n()(function(){m()});var g=[];function v(){g.length&&(g.forEach(function(t,e){var i,n=t.instance,o=t.oldData;t={width:(i=n.$item.getBoundingClientRect()).width,height:i.height,top:i.top,bottom:i.bottom,wndW:a.window.innerWidth,wndH:c},o=(i=!o||o.wndW!==t.wndW||o.wndH!==t.wndH||o.width!==t.width||o.height!==t.height)||!o||o.top!==t.top||o.bottom!==t.bottom;g[e].oldData=t,i&&n.onResize(),o&&n.onScroll()}),a.window.requestAnimationFrame(v))}var y=0,b=function(){function t(e,i){!function(e,i){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this);var n=this;n.instanceID=y,y+=1,n.$item=e,n.defaults={type:"scroll",speed:.5,imgSrc:null,imgElement:".jarallax-img",imgSize:"cover",imgPosition:"50% 50%",imgRepeat:"no-repeat",keepImg:!1,elementInViewport:null,zIndex:-100,disableParallax:!1,disableVideo:!1,videoSrc:null,videoStartTime:0,videoEndTime:0,videoVolume:0,videoLoop:!0,videoPlayOnlyVisible:!0,videoLazyLoading:!0,onScroll:null,onInit:null,onDestroy:null,onCoverImage:null};var a,r,d=n.$item.dataset||{},l={};Object.keys(d).forEach(function(t){var e=t.substr(0,1).toLowerCase()+t.substr(1);e&&void 0!==n.defaults[e]&&(l[e]=d[t])}),n.options=n.extend({},n.defaults,l,i),n.pureOptions=n.extend({},n.options),Object.keys(n.options).forEach(function(t){"true"===n.options[t]?n.options[t]=!0:"false"===n.options[t]&&(n.options[t]=!1)}),n.options.speed=Math.min(2,Math.max(-1,parseFloat(n.options.speed))),"string"==typeof n.options.disableParallax&&(n.options.disableParallax=new RegExp(n.options.disableParallax)),n.options.disableParallax instanceof RegExp&&(a=n.options.disableParallax,n.options.disableParallax=function(){return a.test(u.userAgent)}),"function"!=typeof n.options.disableParallax&&(n.options.disableParallax=function(){return!1}),"string"==typeof n.options.disableVideo&&(n.options.disableVideo=new RegExp(n.options.disableVideo)),n.options.disableVideo instanceof RegExp&&(r=n.options.disableVideo,n.options.disableVideo=function(){return r.test(u.userAgent)}),"function"!=typeof n.options.disableVideo&&(n.options.disableVideo=function(){return!1}),(i=(i=n.options.elementInViewport)&&"object"===s(i)&&void 0!==i.length?o(i,1)[0]:i)instanceof Element||(i=null),n.options.elementInViewport=i,n.image={src:n.options.imgSrc||null,$container:null,useImgTag:!1,position:"fixed"},n.initImg()&&n.canInitParallax()&&n.init()}var e,i;return e=t,(i=[{key:"css",value:function(t,e){return"string"==typeof e?a.window.getComputedStyle(t).getPropertyValue(e):(e.transform&&h&&(e[h]=e.transform),Object.keys(e).forEach(function(i){t.style[i]=e[i]}),t)}},{key:"extend",value:function(t){for(var e=arguments.length,i=new Array(1<e?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];return t=t||{},Object.keys(i).forEach(function(e){i[e]&&Object.keys(i[e]).forEach(function(n){t[n]=i[e][n]})}),t}},{key:"getWindowData",value:function(){return{width:a.window.innerWidth||document.documentElement.clientWidth,height:c,y:document.documentElement.scrollTop}}},{key:"initImg",value:function(){var t=this,e=t.options.imgElement;return(e=e&&"string"==typeof e?t.$item.querySelector(e):e)instanceof Element||(t.options.imgSrc?(e=new Image).src=t.options.imgSrc:e=null),e&&(t.options.keepImg?t.image.$item=e.cloneNode(!0):(t.image.$item=e,t.image.$itemParent=e.parentNode),t.image.useImgTag=!0),!(!t.image.$item&&(null===t.image.src&&(t.image.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",t.image.bgImage=t.css(t.$item,"background-image")),!t.image.bgImage||"none"===t.image.bgImage))}},{key:"canInitParallax",value:function(){return h&&!this.options.disableParallax()}},{key:"init",value:function(){var t,e=this,i={position:"absolute",top:0,left:0,width:"100%",height:"100%",overflow:"hidden"},n={pointerEvents:"none",transformStyle:"preserve-3d",backfaceVisibility:"hidden",willChange:"transform,opacity"};e.options.keepImg||((t=e.$item.getAttribute("style"))&&e.$item.setAttribute("data-jarallax-original-styles",t),!e.image.useImgTag||(t=e.image.$item.getAttribute("style"))&&e.image.$item.setAttribute("data-jarallax-original-styles",t)),"static"===e.css(e.$item,"position")&&e.css(e.$item,{position:"relative"}),"auto"===e.css(e.$item,"z-index")&&e.css(e.$item,{zIndex:0}),e.image.$container=document.createElement("div"),e.css(e.image.$container,i),e.css(e.image.$container,{"z-index":e.options.zIndex}),f&&e.css(e.image.$container,{opacity:.9999}),e.image.$container.setAttribute("id","jarallax-container-".concat(e.instanceID)),e.$item.appendChild(e.image.$container),e.image.useImgTag?n=e.extend({"object-fit":e.options.imgSize,"object-position":e.options.imgPosition,"font-family":"object-fit: ".concat(e.options.imgSize,"; object-position: ").concat(e.options.imgPosition,";"),"max-width":"none"},i,n):(e.image.$item=document.createElement("div"),e.image.src&&(n=e.extend({"background-position":e.options.imgPosition,"background-size":e.options.imgSize,"background-repeat":e.options.imgRepeat,"background-image":e.image.bgImage||'url("'.concat(e.image.src,'")')},i,n))),"opacity"!==e.options.type&&"scale"!==e.options.type&&"scale-opacity"!==e.options.type&&1!==e.options.speed||(e.image.position="absolute"),"fixed"===e.image.position&&(i=function(t){for(var e=[];null!==t.parentElement;)1===(t=t.parentElement).nodeType&&e.push(t);return e}(e.$item).filter(function(t){var e=a.window.getComputedStyle(t);return(t=e["-webkit-transform"]||e["-moz-transform"]||e.transform)&&"none"!==t||/(auto|scroll)/.test(e.overflow+e["overflow-y"]+e["overflow-x"])}),e.image.position=i.length?"absolute":"fixed"),n.position=e.image.position,e.css(e.image.$item,n),e.image.$container.appendChild(e.image.$item),e.onResize(),e.onScroll(!0),e.options.onInit&&e.options.onInit.call(e),"none"!==e.css(e.$item,"background-image")&&e.css(e.$item,{"background-image":"none"}),e.addToParallaxList()}},{key:"addToParallaxList",value:function(){g.push({instance:this}),1===g.length&&a.window.requestAnimationFrame(v)}},{key:"removeFromParallaxList",value:function(){var t=this;g.forEach(function(e,i){e.instance.instanceID===t.instanceID&&g.splice(i,1)})}},{key:"destroy",value:function(){var t=this;t.removeFromParallaxList();var e,i=t.$item.getAttribute("data-jarallax-original-styles");t.$item.removeAttribute("data-jarallax-original-styles"),i?t.$item.setAttribute("style",i):t.$item.removeAttribute("style"),t.image.useImgTag&&(e=t.image.$item.getAttribute("data-jarallax-original-styles"),t.image.$item.removeAttribute("data-jarallax-original-styles"),e?t.image.$item.setAttribute("style",i):t.image.$item.removeAttribute("style"),t.image.$itemParent&&t.image.$itemParent.appendChild(t.image.$item)),t.$clipStyles&&t.$clipStyles.parentNode.removeChild(t.$clipStyles),t.image.$container&&t.image.$container.parentNode.removeChild(t.image.$container),t.options.onDestroy&&t.options.onDestroy.call(t),delete t.$item.jarallax}},{key:"clipContainer",value:function(){var t,e,i;"fixed"===this.image.position&&(e=(i=(t=this).image.$container.getBoundingClientRect()).width,i=i.height,t.$clipStyles||(t.$clipStyles=document.createElement("style"),t.$clipStyles.setAttribute("type","text/css"),t.$clipStyles.setAttribute("id","jarallax-clip-".concat(t.instanceID)),(document.head||document.getElementsByTagName("head")[0]).appendChild(t.$clipStyles)),i="#jarallax-container-".concat(t.instanceID," {\n            clip: rect(0 ").concat(e,"px ").concat(i,"px 0);\n            clip: rect(0, ").concat(e,"px, ").concat(i,"px, 0);\n            -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);\n            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);\n        }"),t.$clipStyles.styleSheet?t.$clipStyles.styleSheet.cssText=i:t.$clipStyles.innerHTML=i)}},{key:"coverImage",value:function(){var t,e=this,i=e.image.$container.getBoundingClientRect(),n=i.height,a=e.options.speed,o="scroll"===e.options.type||"scroll-opacity"===e.options.type,r=0,s=n;return o&&(a<0?(r=a*Math.max(n,c),c<n&&(r-=a*(n-c))):r=a*(n+c),1<a?s=Math.abs(r-c):a<0?s=r/a+Math.abs(r):s+=(c-n)*(1-a),r/=2),e.parallaxScrollDistance=r,t=o?(c-s)/2:(n-s)/2,e.css(e.image.$item,{height:"".concat(s,"px"),marginTop:"".concat(t,"px"),left:"fixed"===e.image.position?"".concat(i.left,"px"):"0",width:"".concat(i.width,"px")}),e.options.onCoverImage&&e.options.onCoverImage.call(e),{image:{height:s,marginTop:t},container:i}}},{key:"isVisible",value:function(){return this.isElementInViewport||!1}},{key:"onScroll",value:function(t){var e,i,n,o,r,s,d,l=this,u=l.$item.getBoundingClientRect(),f=u.top,p=u.height,h={},m=u;l.options.elementInViewport&&(m=l.options.elementInViewport.getBoundingClientRect()),l.isElementInViewport=0<=m.bottom&&0<=m.right&&m.top<=c&&m.left<=a.window.innerWidth,(t||l.isElementInViewport)&&(e=Math.max(0,f),i=Math.max(0,p+f),n=Math.max(0,-f),o=Math.max(0,f+p-c),r=Math.max(0,p-(f+p-c)),s=Math.max(0,-f+c-p),m=1-(c-f)/(c+p)*2,t=1,p<c?t=1-(n||o)/p:i<=c?t=i/c:r<=c&&(t=r/c),"opacity"!==l.options.type&&"scale-opacity"!==l.options.type&&"scroll-opacity"!==l.options.type||(h.transform="translate3d(0,0,0)",h.opacity=t),"scale"!==l.options.type&&"scale-opacity"!==l.options.type||(d=1,l.options.speed<0?d-=l.options.speed*t:d+=l.options.speed*(1-t),h.transform="scale(".concat(d,") translate3d(0,0,0)")),"scroll"!==l.options.type&&"scroll-opacity"!==l.options.type||(d=l.parallaxScrollDistance*m,"absolute"===l.image.position&&(d-=f),h.transform="translate3d(0,".concat(d,"px,0)")),l.css(l.image.$item,h),l.options.onScroll&&l.options.onScroll.call(l,{section:u,beforeTop:e,beforeTopEnd:i,afterTop:n,beforeBottom:o,beforeBottomEnd:r,afterBottom:s,visiblePercent:t,fromViewportCenter:m}))}},{key:"onResize",value:function(){this.coverImage(),this.clipContainer()}}])&&d(e.prototype,i),t}();(n=function(t,e){for(var i,n=(t=("object"===("undefined"==typeof HTMLElement?"undefined":s(HTMLElement))?t instanceof HTMLElement:t&&"object"===s(t)&&null!==t&&1===t.nodeType&&"string"==typeof t.nodeName)?[t]:t).length,a=0,o=arguments.length,r=new Array(2<o?o-2:0),d=2;d<o;d++)r[d-2]=arguments[d];for(;a<n;a+=1)if("object"===s(e)||void 0===e?t[a].jarallax||(t[a].jarallax=new b(t[a],e)):t[a].jarallax&&(i=t[a].jarallax[e].apply(t[a].jarallax,r)),void 0!==i)return i;return t}).constructor=b,e.default=n}},e={};function i(n){var a=e[n];return void 0!==a?a.exports:(a=e[n]={exports:{}},t[n](a,a.exports,i),a.exports)}i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,{a:e}),e},i.d=function(t,e){for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};!function(){"use strict";i.r(n);var t=i(1),e=i.n(t),a=i(2),o=i(8);function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var s,d=a.window.jarallax;a.window.jarallax=o.default,a.window.jarallax.noConflict=function(){return a.window.jarallax=d,this},void 0!==a.jQuery_BENT&&((t=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];Array.prototype.unshift.call(e,this);var n=o.default.apply(a.window,e);return"object"!==r(n)?n:this}).constructor=o.default.constructor,s=a.jQuery_BENT.fn.jarallax,a.jQuery_BENT.fn.jarallax=t,a.jQuery_BENT.fn.jarallax.noConflict=function(){return a.jQuery_BENT.fn.jarallax=s,this}),e()(function(){(0,o.default)(document.querySelectorAll("[data-jarallax]"))})}()}();var beeJarallax=jarallax.noConflict();jQuery_BENT.fn.beeJarallax=jQuery_BENT.fn.jarallax.noConflict(),function(t){"use strict";"function"==typeof define&&define.amd?define(["jQuery_BENT"],t):t(jQuery_BENT)}(function(t){"use strict";function e(t){var e=t.toString().replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1");return new RegExp(e)}function i(t){return function(i){var a=i.match(/%(-|!)?[A-Z]{1}(:[^;]+;)?/gi);if(a)for(var o=0,r=a.length;o<r;++o){var d=a[o].match(/%(-|!)?([a-zA-Z]{1})(:[^;]+;)?/),l=e(d[0]),c=d[1]||"",u=d[3]||"",f=null;d=d[2],s.hasOwnProperty(d)&&(f=s[d],f=Number(t[f])),null!==f&&("!"===c&&(f=n(u,f)),""===c&&f<10&&(f="0"+f.toString()),i=i.replace(l,f.toString()))}return i.replace(/%%/,"%")}}function n(t,e){var i="s",n="";return t&&(1===(t=t.replace(/(:|;|\s)/gi,"").split(/\,/)).length?i=t[0]:(n=t[0],i=t[1])),Math.abs(e)>1?i:n}var a=[],o=[],r={precision:100,elapse:!1,defer:!1};o.push(/^[0-9]*$/.source),o.push(/([0-9]{1,2}\/){2}[0-9]{4}( [0-9]{1,2}(:[0-9]{2}){2})?/.source),o.push(/[0-9]{4}([\/\-][0-9]{1,2}){2}( [0-9]{1,2}(:[0-9]{2}){2})?/.source),o=new RegExp(o.join("|"));var s={Y:"years",m:"months",n:"daysToMonth",d:"daysToWeek",w:"weeks",W:"weeksToMonth",H:"hours",M:"minutes",S:"seconds",D:"totalDays",I:"totalHours",N:"totalMinutes",T:"totalSeconds"},d=function(e,i,n){this.el=e,this.$el=t(e),this.interval=null,this.offset={},this.options=t.extend({},r),this.instanceNumber=a.length,a.push(this),this.$el.data("countdown-instance",this.instanceNumber),n&&("function"==typeof n?(this.$el.on("update.countdown",n),this.$el.on("stoped.countdown",n),this.$el.on("finish.countdown",n)):this.options=t.extend({},r,n)),this.setFinalDate(i),!1===this.options.defer&&this.start()};t.extend(d.prototype,{start:function(){null!==this.interval&&clearInterval(this.interval);var t=this;this.update(),this.interval=setInterval(function(){t.update.call(t)},this.options.precision)},stop:function(){clearInterval(this.interval),this.interval=null,this.dispatchEvent("stoped")},toggle:function(){this.interval?this.stop():this.start()},pause:function(){this.stop()},resume:function(){this.start()},remove:function(){this.stop.call(this),a[this.instanceNumber]=null,delete this.$el.data().countdownInstance},setFinalDate:function(t){this.finalDate=function(t){if(t instanceof Date)return t;if(String(t).match(o))return String(t).match(/^[0-9]*$/)&&(t=Number(t)),String(t).match(/\-/)&&(t=String(t).replace(/\-/g,"/")),new Date(t);throw new Error("Couldn't cast `"+t+"` to a date object.")}(t)},update:function(){if(0!==this.$el.closest("html").length){var e,i=void 0!==t._data(this.el,"events"),n=new Date;e=this.finalDate.getTime()-n.getTime(),e=Math.ceil(e/1e3),e=!this.options.elapse&&e<0?0:Math.abs(e),this.totalSecsLeft!==e&&i&&(this.totalSecsLeft=e,this.elapsed=n>=this.finalDate,this.offset={seconds:this.totalSecsLeft%60,minutes:Math.floor(this.totalSecsLeft/60)%60,hours:Math.floor(this.totalSecsLeft/60/60)%24,days:Math.floor(this.totalSecsLeft/60/60/24)%7,daysToWeek:Math.floor(this.totalSecsLeft/60/60/24)%7,daysToMonth:Math.floor(this.totalSecsLeft/60/60/24%30.4368),weeks:Math.floor(this.totalSecsLeft/60/60/24/7),weeksToMonth:Math.floor(this.totalSecsLeft/60/60/24/7)%4,months:Math.floor(this.totalSecsLeft/60/60/24/30.4368),years:Math.abs(this.finalDate.getFullYear()-n.getFullYear()),totalDays:Math.floor(this.totalSecsLeft/60/60/24),totalHours:Math.floor(this.totalSecsLeft/60/60),totalMinutes:Math.floor(this.totalSecsLeft/60),totalSeconds:this.totalSecsLeft},this.options.elapse||0!==this.totalSecsLeft?this.dispatchEvent("update"):(this.stop(),this.dispatchEvent("finish")))}else this.remove()},dispatchEvent:function(e){var n=t.Event(e+".countdown");n.finalDate=this.finalDate,n.elapsed=this.elapsed,n.offset=t.extend({},this.offset),n.strftime=i(this.offset),this.$el.trigger(n)}}),t.fn.countdown=function(){var e=Array.prototype.slice.call(arguments,0);return this.each(function(){var i=t(this).data("countdown-instance");if(void 0!==i){var n=a[i],o=e[0];d.prototype.hasOwnProperty(o)?n[o].apply(n,e.slice(1)):null===String(o).match(/^[$A-Z_][0-9A-Z_$]*$/i)?(n.setFinalDate.call(n,o),n.start()):t.error("Method %s does not exist on jQuery_BENT.countdown".replace(/\%s/gi,o))}else new d(this,e[0],e[1])})}}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).dayjs=e()}(this,function(){"use strict";var t="millisecond",e="second",i="minute",n="hour",a="day",o="week",r="month",s="quarter",d="year",l="date",c="Invalid Date",u=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,f=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,p={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_")},h=function(t,e,i){var n=String(t);return!n||n.length>=e?t:""+Array(e+1-n.length).join(i)+t},m={s:h,z:function(t){var e=-t.utcOffset(),i=Math.abs(e),n=Math.floor(i/60),a=i%60;return(e<=0?"+":"-")+h(n,2,"0")+":"+h(a,2,"0")},m:function t(e,i){if(e.date()<i.date())return-t(i,e);var n=12*(i.year()-e.year())+(i.month()-e.month()),a=e.clone().add(n,r),o=i-a<0,s=e.clone().add(n+(o?-1:1),r);return+(-(n+(i-a)/(o?a-s:s-a))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(c){return{M:r,y:d,w:o,d:a,D:l,h:n,m:i,s:e,ms:t,Q:s}[c]||String(c||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}},g="en",v={};v[g]=p;var y=function(t){return t instanceof _},b=function(t,e,i){var n;if(!t)return g;if("string"==typeof t)v[t]&&(n=t),e&&(v[t]=e,n=t);else{var a=t.name;v[a]=t,n=a}return!i&&n&&(g=n),n||!i&&g},w=function(t,e){if(y(t))return t.clone();var i="object"==typeof e?e:{};return i.date=t,i.args=arguments,new _(i)},S=m;S.l=b,S.i=y,S.w=function(t,e){return w(t,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var _=function(){function p(t){this.$L=b(t.locale,null,!0),this.parse(t)}var h=p.prototype;return h.parse=function(t){this.$d=function(t){var e=t.date,i=t.utc;if(null===e)return new Date(NaN);if(S.u(e))return new Date;if(e instanceof Date)return new Date(e);if("string"==typeof e&&!/Z$/i.test(e)){var n=e.match(u);if(n){var a=n[2]-1||0,o=(n[7]||"0").substring(0,3);return i?new Date(Date.UTC(n[1],a,n[3]||1,n[4]||0,n[5]||0,n[6]||0,o)):new Date(n[1],a,n[3]||1,n[4]||0,n[5]||0,n[6]||0,o)}}return new Date(e)}(t),this.$x=t.x||{},this.init()},h.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},h.$utils=function(){return S},h.isValid=function(){return!(this.$d.toString()===c)},h.isSame=function(t,e){var i=w(t);return this.startOf(e)<=i&&i<=this.endOf(e)},h.isAfter=function(t,e){return w(t)<this.startOf(e)},h.isBefore=function(t,e){return this.endOf(e)<w(t)},h.$g=function(t,e,i){return S.u(t)?this[e]:this.set(i,t)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(t,s){var c=this,u=!!S.u(s)||s,f=S.p(t),p=function(t,e){var i=S.w(c.$u?Date.UTC(c.$y,e,t):new Date(c.$y,e,t),c);return u?i:i.endOf(a)},h=function(t,e){return S.w(c.toDate()[t].apply(c.toDate("s"),(u?[0,0,0,0]:[23,59,59,999]).slice(e)),c)},m=this.$W,g=this.$M,v=this.$D,y="set"+(this.$u?"UTC":"");switch(f){case d:return u?p(1,0):p(31,11);case r:return u?p(1,g):p(0,g+1);case o:var b=this.$locale().weekStart||0,w=(m<b?m+7:m)-b;return p(u?v-w:v+(6-w),g);case a:case l:return h(y+"Hours",0);case n:return h(y+"Minutes",1);case i:return h(y+"Seconds",2);case e:return h(y+"Milliseconds",3);default:return this.clone()}},h.endOf=function(t){return this.startOf(t,!1)},h.$set=function(o,s){var c,u=S.p(o),f="set"+(this.$u?"UTC":""),p=(c={},c[a]=f+"Date",c[l]=f+"Date",c[r]=f+"Month",c[d]=f+"FullYear",c[n]=f+"Hours",c[i]=f+"Minutes",c[e]=f+"Seconds",c[t]=f+"Milliseconds",c)[u],h=u===a?this.$D+(s-this.$W):s;if(u===r||u===d){var m=this.clone().set(l,1);m.$d[p](h),m.init(),this.$d=m.set(l,Math.min(this.$D,m.daysInMonth())).$d}else p&&this.$d[p](h);return this.init(),this},h.set=function(t,e){return this.clone().$set(t,e)},h.get=function(t){return this[S.p(t)]()},h.add=function(t,s){var l,c=this;t=Number(t);var u=S.p(s),f=function(e){var i=w(c);return S.w(i.date(i.date()+Math.round(e*t)),c)};if(u===r)return this.set(r,this.$M+t);if(u===d)return this.set(d,this.$y+t);if(u===a)return f(1);if(u===o)return f(7);var p=(l={},l[i]=6e4,l[n]=36e5,l[e]=1e3,l)[u]||1,h=this.$d.getTime()+t*p;return S.w(h,this)},h.subtract=function(t,e){return this.add(-1*t,e)},h.format=function(t){var e=this,i=this.$locale();if(!this.isValid())return i.invalidDate||c;var n=t||"YYYY-MM-DDTHH:mm:ssZ",a=S.z(this),o=this.$H,r=this.$m,s=this.$M,d=i.weekdays,l=i.months,u=function(t,i,a,o){return t&&(t[i]||t(e,n))||a[i].substr(0,o)},p=function(t){return S.s(o%12||12,t,"0")},h=i.meridiem||function(t,e,i){var n=t<12?"AM":"PM";return i?n.toLowerCase():n},m={YY:String(this.$y).slice(-2),YYYY:this.$y,M:s+1,MM:S.s(s+1,2,"0"),MMM:u(i.monthsShort,s,l,3),MMMM:u(l,s),D:this.$D,DD:S.s(this.$D,2,"0"),d:String(this.$W),dd:u(i.weekdaysMin,this.$W,d,2),ddd:u(i.weekdaysShort,this.$W,d,3),dddd:d[this.$W],H:String(o),HH:S.s(o,2,"0"),h:p(1),hh:p(2),a:h(o,r,!0),A:h(o,r,!1),m:String(r),mm:S.s(r,2,"0"),s:String(this.$s),ss:S.s(this.$s,2,"0"),SSS:S.s(this.$ms,3,"0"),Z:a};return n.replace(f,function(t,e){return e||m[t]||a.replace(":","")})},h.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},h.diff=function(t,l,c){var u,f=S.p(l),p=w(t),h=6e4*(p.utcOffset()-this.utcOffset()),m=this-p,g=S.m(this,p);return g=(u={},u[d]=g/12,u[r]=g,u[s]=g/3,u[o]=(m-h)/6048e5,u[a]=(m-h)/864e5,u[n]=m/36e5,u[i]=m/6e4,u[e]=m/1e3,u)[f]||m,c?g:S.a(g)},h.daysInMonth=function(){return this.endOf(r).$D},h.$locale=function(){return v[this.$L]},h.locale=function(t,e){if(!t)return this.$L;var i=this.clone(),n=b(t,e,!0);return n&&(i.$L=n),i},h.clone=function(){return S.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},p}(),$=_.prototype;return w.prototype=$,[["$ms",t],["$s",e],["$m",i],["$H",n],["$W",a],["$M",r],["$y",d],["$D",l]].forEach(function(t){$[t[1]]=function(e){return this.$g(e,t[0],t[1])}}),w.extend=function(t,e){return t.$i||(t(e,_,w),t.$i=!0),w},w.locale=b,w.isDayjs=y,w.unix=function(t){return w(1e3*t)},w.en=v[g],w.Ls=v,w.p={},w}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).dayjs_plugin_utc=e()}(this,function(){"use strict";var t="minute",e=/[+-]\d\d(?::?\d\d)?/g,i=/([+-]|\d\d)/g;return function(n,a,o){var r=a.prototype;o.utc=function(t){return new a({date:t,utc:!0,args:arguments})},r.utc=function(e){var i=o(this.toDate(),{locale:this.$L,utc:!0});return e?i.add(this.utcOffset(),t):i},r.local=function(){return o(this.toDate(),{locale:this.$L,utc:!1})};var s=r.parse;r.parse=function(t){t.utc&&(this.$u=!0),this.$utils().u(t.$offset)||(this.$offset=t.$offset),s.call(this,t)};var d=r.init;r.init=function(){if(this.$u){var t=this.$d;this.$y=t.getUTCFullYear(),this.$M=t.getUTCMonth(),this.$D=t.getUTCDate(),this.$W=t.getUTCDay(),this.$H=t.getUTCHours(),this.$m=t.getUTCMinutes(),this.$s=t.getUTCSeconds(),this.$ms=t.getUTCMilliseconds()}else d.call(this)};var l=r.utcOffset;r.utcOffset=function(n,a){var o=this.$utils().u;if(o(n))return this.$u?0:o(this.$offset)?l.call(this):this.$offset;if("string"==typeof n&&null===(n=function(t){void 0===t&&(t="");var n=t.match(e);if(!n)return null;var a=(""+n[0]).match(i)||["-",0,0],o=a[0],r=60*+a[1]+ +a[2];return 0===r?0:"+"===o?r:-r}(n)))return this;var r=Math.abs(n)<=16?60*n:n,s=this;if(a)return s.$offset=r,s.$u=0===n,s;if(0!==n){var d=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(s=this.local().add(r+d,t)).$offset=r,s.$x.$localOffset=d}else s=this.utc();return s};var c=r.format;r.format=function(t){var e=t||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return c.call(this,e)},r.valueOf=function(){var t=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||(new Date).getTimezoneOffset());return this.$d.valueOf()-6e4*t},r.isUTC=function(){return!!this.$u},r.toISOString=function(){return this.toDate().toISOString()},r.toString=function(){return this.toDate().toUTCString()};var u=r.toDate;r.toDate=function(t){return"s"===t&&this.$offset?o(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():u.call(this)};var f=r.diff;r.diff=function(t,e,i){if(t&&this.$u===t.$u)return f.call(this,t,e,i);var n=this.local(),a=o(t).local();return f.call(n,a,e,i)}}}),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).dayjs_plugin_timezone=e()}(this,function(){"use strict";var t={year:0,month:1,day:2,hour:3,minute:4,second:5},e={};return function(i,n,a){var o,r=function(t,i,n){void 0===n&&(n={});var a=new Date(t);return function(t,i){void 0===i&&(i={});var n=i.timeZoneName||"short",a=t+"|"+n,o=e[a];return o||(o=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:t,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:n}),e[a]=o),o}(i,n).formatToParts(a)},s=function(e,i){for(var n=r(e,i),o=[],s=0;s<n.length;s+=1){var d=n[s],l=d.type,c=d.value,u=t[l];u>=0&&(o[u]=parseInt(c,10))}var f=o[3],p=24===f?0:f,h=o[0]+"-"+o[1]+"-"+o[2]+" "+p+":"+o[4]+":"+o[5]+":000",m=+e;return(a.utc(h).valueOf()-(m-=m%1e3))/6e4},d=n.prototype;d.tz=function(t,e){void 0===t&&(t=o);var i=this.utcOffset(),n=this.toDate(),r=n.toLocaleString("en-US",{timeZone:t}),s=Math.round((n-new Date(r))/1e3/60),d=a(r).$set("millisecond",this.$ms).utcOffset(15*-Math.round(n.getTimezoneOffset()/15)-s,!0);if(e){var l=d.utcOffset();d=d.add(i-l,"minute")}return d.$x.$timezone=t,d},d.offsetName=function(t){var e=this.$x.$timezone||a.tz.guess(),i=r(this.valueOf(),e,{timeZoneName:t}).find(function(t){return"timezonename"===t.type.toLowerCase()});return i&&i.value};var l=d.startOf;d.startOf=function(t,e){if(!this.$x||!this.$x.$timezone)return l.call(this,t,e);var i=a(this.format("YYYY-MM-DD HH:mm:ss:SSS"));return l.call(i,t,e).tz(this.$x.$timezone,!0)},a.tz=function(t,e,i){var n=i&&e,r=i||e||o,d=s(+a(),r);if("string"!=typeof t)return a(t).tz(r);var l=function(t,e,i){var n=t-60*e*1e3,a=s(n,i);if(e===a)return[n,e];var o=s(n-=60*(a-e)*1e3,i);return a===o?[n,a]:[t-60*Math.min(a,o)*1e3,Math.max(a,o)]}(a.utc(t,n).valueOf(),d,r),c=l[0],u=l[1],f=a(c).utcOffset(u);return f.$x.$timezone=r,f},a.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},a.tz.setDefault=function(t){o=t}}});var dayjs_utc=window.dayjs_plugin_utc,dayjs_timezone=window.dayjs_plugin_timezone;function _typeof(t){"@babel/helpers - typeof";return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function createCommonjsModule(t,e,i){return t(i={path:e,exports:{},require:function(t,e){return commonjsRequire(t,null==e?i.path:e)}},i.exports),i.exports}function commonjsRequire(){throw new Error("Error commonjs")}dayjs.locale("en"),dayjs.extend(dayjs_utc),dayjs.extend(dayjs_timezone),function(t){"function"==typeof define&&define.amd?define(["jQuery_BENT"],t):"object"==typeof exports?t(require("jQuery_BENT")):t(window.jQuery_BENT||window.Zepto)}(function(t){var e,i,n,a,o,r,s={},d=function(){},l=!!window.jQuery_BENT,c=t(window),u=function(t,i){e.ev.on("mfp"+t+".mfp",i)},f=function(e,i,n,a){var o=document.createElement("div");return o.className="mfp-"+e,n&&(o.innerHTML=n),a?i&&i.appendChild(o):(o=t(o),i&&o.appendTo(i)),o},p=function(i,n){e.ev.triggerHandler("mfp"+i,n),e.st.callbacks&&(i=i.charAt(0).toLowerCase()+i.slice(1),e.st.callbacks[i]&&e.st.callbacks[i].apply(e,t.isArray(n)?n:[n]))},h=function(i){return i===r&&e.currTemplate.closeBtn||(e.currTemplate.closeBtn=t(e.st.closeMarkup.replace("%title%",e.st.tClose)),r=i),e.currTemplate.closeBtn},m=function(){t.magnificPopupBee.instance||((e=new d).init(),t.magnificPopupBee.instance=e)};d.prototype={constructor:d,init:function(){var i=navigator.appVersion;e.isLowIE=e.isIE8=document.all&&!document.addEventListener,e.isAndroid=/android/gi.test(i),e.isIOS=/iphone|ipad|ipod/gi.test(i),e.supportsTransition=function(){var t=document.createElement("p").style,e=["ms","O","Moz","Webkit"];if(void 0!==t.transition)return!0;for(;e.length;)if(e.pop()+"Transition"in t)return!0;return!1}(),e.probablyMobile=e.isAndroid||e.isIOS||/(Opera Mini)|Kindle|webOS|BlackBerry|(Opera Mobi)|(Windows Phone)|IEMobile/i.test(navigator.userAgent),n=t(document),e.popupsCache={}},open:function(i){var a;if(!1===i.isObj){e.items=i.items.toArray(),e.index=0;var r,s=i.items;for(a=0;a<s.length;a++)if((r=s[a]).parsed&&(r=r.el[0]),r===i.el[0]){e.index=a;break}}else e.items=t.isArray(i.items)?i.items:[i.items],e.index=i.index||0;if(!e.isOpen){e.types=[],o="",i.mainEl&&i.mainEl.length?e.ev=i.mainEl.eq(0):e.ev=n,i.key?(e.popupsCache[i.key]||(e.popupsCache[i.key]={}),e.currTemplate=e.popupsCache[i.key]):e.currTemplate={},e.st=t.extend(!0,{},t.magnificPopupBee.defaults,i),e.fixedContentPos="auto"===e.st.fixedContentPos?!e.probablyMobile:e.st.fixedContentPos,e.st.modal&&(e.st.closeOnContentClick=!1,e.st.closeOnBgClick=!1,e.st.showCloseBtn=!1,e.st.enableEscapeKey=!1),e.bgOverlay||(e.bgOverlay=f("bg").on("click.mfp",function(){e.close()}),e.wrap=f("wrap").attr("tabindex",-1).on("click.mfp",function(t){e._checkIfClose(t.target)&&e.close()}),e.container=f("container",e.wrap)),e.contentContainer=f("content"),e.st.preloader&&(e.preloader=f("preloader",e.container,e.st.tLoading));var d=t.magnificPopupBee.modules;for(a=0;a<d.length;a++){var l=d[a];l=l.charAt(0).toUpperCase()+l.slice(1),e["init"+l].call(e)}p("BeforeOpen"),e.st.showCloseBtn&&(e.st.closeBtnInside?(u("MarkupParse",function(t,e,i,n){i.close_replaceWith=h(n.type)}),o+=" mfp-close-btn-in"):e.wrap.append(h())),e.st.alignTop&&(o+=" mfp-align-top"),e.fixedContentPos?e.wrap.css({overflow:e.st.overflowY,overflowX:"hidden",overflowY:e.st.overflowY}):e.wrap.css({top:c.scrollTop(),position:"absolute"}),(!1===e.st.fixedBgPos||"auto"===e.st.fixedBgPos&&!e.fixedContentPos)&&e.bgOverlay.css({height:n.height(),position:"absolute"}),e.st.enableEscapeKey&&n.on("keyup.mfp",function(t){27===t.keyCode&&e.close()}),c.on("resize.mfp",function(){e.updateSize()}),e.st.closeOnContentClick||(o+=" mfp-auto-cursor"),o&&e.wrap.addClass(o);var m=e.wH=c.height(),g={};if(e.fixedContentPos&&e._hasScrollBar(m)){var v=e._getScrollbarSize();v&&(g.marginRight=v)}e.fixedContentPos&&(e.isIE7?t("body, html").css("overflow","hidden"):g.overflow="hidden");var y=e.st.mainClass;return e.isIE7&&(y+=" mfp-ie7"),y&&e._addClassToMFP(y),e.updateItemHTML(),p("BuildControls"),t("html").css(g),e.bgOverlay.add(e.wrap).prependTo(e.st.prependTo||t(document.body)),e._lastFocusedEl=document.activeElement,setTimeout(function(){e.content?(e._addClassToMFP("mfp-ready"),e._setFocus()):e.bgOverlay.addClass("mfp-ready"),n.on("focusin.mfp",e._onFocusIn)},16),e.isOpen=!0,e.updateSize(m),p("Open"),i}e.updateItemHTML()},close:function(){e.isOpen&&(p("BeforeClose"),e.isOpen=!1,e.st.removalDelay&&!e.isLowIE&&e.supportsTransition?(e._addClassToMFP("mfp-removing"),setTimeout(function(){e._close()},e.st.removalDelay)):e._close())},_close:function(){p("Close");var i="mfp-removing mfp-ready ";if(e.bgOverlay.detach(),e.wrap.detach(),e.container.empty(),e.st.mainClass&&(i+=e.st.mainClass+" "),e._removeClassFromMFP(i),e.fixedContentPos){var a={marginRight:""};e.isIE7?t("body, html").css("overflow",""):a.overflow="",t("html").css(a)}n.off("keyup.mfp focusin.mfp"),e.ev.off(".mfp"),e.wrap.attr("class","mfp-wrap").removeAttr("style"),e.bgOverlay.attr("class","mfp-bg"),e.container.attr("class","mfp-container"),!e.st.showCloseBtn||e.st.closeBtnInside&&!0!==e.currTemplate[e.currItem.type]||e.currTemplate.closeBtn&&e.currTemplate.closeBtn.detach(),e.st.autoFocusLast&&e._lastFocusedEl&&t(e._lastFocusedEl).focus(),e.currItem=null,e.content=null,e.currTemplate=null,e.prevHeight=0,p("AfterClose")},updateSize:function(t){if(e.isIOS){var i=document.documentElement.clientWidth/window.innerWidth,n=window.innerHeight*i;e.wrap.css("height",n),e.wH=n}else e.wH=t||c.height();e.fixedContentPos||e.wrap.css("height",e.wH),p("Resize")},updateItemHTML:function(){var i=e.items[e.index];e.contentContainer.detach(),e.content&&e.content.detach(),i.parsed||(i=e.parseEl(e.index));var n=i.type;if(p("BeforeChange",[e.currItem?e.currItem.type:"",n]),e.currItem=i,!e.currTemplate[n]){var o=!!e.st[n]&&e.st[n].markup;p("FirstMarkupParse",o),e.currTemplate[n]=!o||t(o)}a&&a!==i.type&&e.container.removeClass("mfp-"+a+"-holder");var r=e["get"+n.charAt(0).toUpperCase()+n.slice(1)](i,e.currTemplate[n]);e.appendContent(r,n),i.preloaded=!0,p("Change",i),a=i.type,e.container.prepend(e.contentContainer),p("AfterChange")},appendContent:function(t,i,n){if(""==t&&"ajax"==i)return!1;e.content=t,t?e.st.showCloseBtn&&e.st.closeBtnInside&&!0===e.currTemplate[i]?e.content.find(".mfp-close").length||e.content.append(h()):e.content=t:e.content="",p("BeforeAppend"),e.container.addClass("mfp-"+i+"-holder"),e.contentContainer.append(e.content)},parseEl:function(i){var n,a=e.items[i];if(a.tagName?a={el:t(a)}:(n=a.type,a={data:a,src:a.src}),a.el){for(var o=e.types,r=0;r<o.length;r++)if(a.el.hasClass("mfp-"+o[r])){n=o[r];break}a.src=a.el.attr("data-mfp-src"),a.src||(a.src=a.el.attr("href"))}return a.type=n||e.st.type||"inline",a.index=i,a.parsed=!0,e.items[i]=a,p("ElementParse",a),e.items[i]},addGroup:function(t,i){var n=function(n){n.mfpEl=this,e._openClick(n,t,i)};i||(i={});var a="click.magnificPopupBee";i.mainEl=t,i.items?(i.isObj=!0,t.off(a).on(a,n)):(i.isObj=!1,i.delegate?t.off(a).on(a,i.delegate,n):(i.items=t,t.off(a).on(a,n)))},_openClick:function(i,n,a){if((void 0!==a.midClick?a.midClick:t.magnificPopupBee.defaults.midClick)||!(2===i.which||i.ctrlKey||i.metaKey||i.altKey||i.shiftKey)){var o=void 0!==a.disableOn?a.disableOn:t.magnificPopupBee.defaults.disableOn;if(o)if(t.isFunction(o)){if(!o.call(e))return!0}else if(c.width()<o)return!0;i.type&&(i.preventDefault(),e.isOpen&&i.stopPropagation()),a.el=t(i.mfpEl),a.delegate&&(a.items=n.find(a.delegate)),e.open(a)}},updateStatus:function(t,n){if(e.preloader){i!==t&&e.container.removeClass("mfp-s-"+i),n||"loading"!==t||(n=e.st.tLoading);var a={status:t,text:n};p("UpdateStatus",a),t=a.status,n=a.text,e.preloader.html(n),e.preloader.find("a").on("click",function(t){t.stopImmediatePropagation()}),e.container.addClass("mfp-s-"+t),i=t}},_checkIfClose:function(i){if(!t(i).hasClass("mfp-prevent-close")){var n=e.st.closeOnContentClick,a=e.st.closeOnBgClick;if(n&&a)return!0;if(!e.content||t(i).hasClass("mfp-close")||e.preloader&&i===e.preloader[0])return!0;if(i===e.content[0]||t.contains(e.content[0],i)){if(n)return!0}else if(a&&t.contains(document,i))return!0;return!1}},_addClassToMFP:function(t){e.bgOverlay.addClass(t),e.wrap.addClass(t)},_removeClassFromMFP:function(t){this.bgOverlay.removeClass(t),e.wrap.removeClass(t)},_hasScrollBar:function(t){return(e.isIE7?n.height():document.body.scrollHeight)>(t||c.height())},_setFocus:function(){(e.st.focus?e.content.find(e.st.focus).eq(0):e.wrap).focus()},_onFocusIn:function(i){if(i.target!==e.wrap[0]&&!t.contains(e.wrap[0],i.target))return e._setFocus(),!1},_parseMarkup:function(e,i,n){var a;n.data&&(i=t.extend(n.data,i)),p("MarkupParse",[e,i,n]),t.each(i,function(i,n){if(void 0===n||!1===n)return!0;if((a=i.split("_")).length>1){var o=e.find(".mfp-"+a[0]);if(o.length>0){var r=a[1];"replaceWith"===r?o[0]!==n[0]&&o.replaceWith(n):"img"===r?o.is("img")?o.attr("src",n):o.replaceWith(t("<img>").attr("src",n).attr("class",o.attr("class"))):o.attr(a[1],n)}}else e.find(".mfp-"+i).html(n)})},_getScrollbarSize:function(){if(void 0===e.scrollbarSize){var t=document.createElement("div");t.style.cssText="width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;",document.body.appendChild(t),e.scrollbarSize=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return e.scrollbarSize}},t.magnificPopupBee={instance:null,proto:d.prototype,modules:[],open:function(e,i){return m(),(e=e?t.extend(!0,{},e):{}).isObj=!0,e.index=i||0,this.instance.open(e)},close:function(){return t.magnificPopupBee.instance&&t.magnificPopupBee.instance.close()},registerModule:function(e,i){i.options&&(t.magnificPopupBee.defaults[e]=i.options),t.extend(this.proto,i.proto),this.modules.push(e)},defaults:{disableOn:0,key:null,midClick:!1,mainClass:"",preloader:!0,focus:"",closeOnContentClick:!1,closeOnBgClick:!0,closeBtnInside:!0,showCloseBtn:!0,enableEscapeKey:!0,modal:!1,alignTop:!1,removalDelay:0,prependTo:null,fixedContentPos:"auto",fixedBgPos:"auto",overflowY:"auto",closeMarkup:'<button title="%title%" type="button" class="mfp-close"><svg class="beemfp-icon-close" role="presentation" viewBox="0 0 16 14"><path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path></svg></button>',tClose:"Close (Esc)",tLoading:"Loading...",autoFocusLast:!0}},t.fn.magnificPopupBee=function(i){m();var n=t(this);if("string"==typeof i)if("open"===i){var a,o=l?n.data("magnificPopup"):n[0].magnificPopupBee,r=parseInt(arguments[1],10)||0;o.items?a=o.items[r]:(a=n,o.delegate&&(a=a.find(o.delegate)),a=a.eq(r)),e._openClick({mfpEl:a},n,o)}else e.isOpen&&e[i].apply(e,Array.prototype.slice.call(arguments,1));else i=t.extend(!0,{},i),l?n.data("magnificPopup",i):n[0].magnificPopupBee=i,e.addGroup(n,i);return n};var g,v,y,b=function(){y&&(v.after(y.addClass(g)).detach(),y=null)};t.magnificPopupBee.registerModule("inline",{options:{hiddenClass:"hide",markup:"",tNotFound:"Content not found"},proto:{initInline:function(){e.types.push("inline"),u("Close.inline",function(){b()})},getInline:function(i,n){if(b(),i.src){var a=e.st.inline,o=t(i.src);if(o.length){var r=o[0].parentNode;r&&r.tagName&&(v||(g=a.hiddenClass,v=f(g),g="mfp-"+g),y=o.after(v).detach().removeClass(g)),e.updateStatus("ready")}else e.updateStatus("error",a.tNotFound),o=t("<div>");return i.inlineElement=o,o}return e.updateStatus("ready"),e._parseMarkup(n,{},i),n}}});var w,S,_,$=function(){w&&t(document.body).removeClass(w)},C=function(){$(),e.req&&e.req.abort()};t.magnificPopupBee.registerModule("ajax",{options:{settings:null,cursor:"mfp-ajax-cur",tError:'<a href="%url%">The content</a> could not be loaded.'},proto:{initAjax:function(){e.types.push("ajax"),w=e.st.ajax.cursor,u("Close.ajax",C),u("BeforeChange.ajax",C)},getAjax:function(i){w&&t(document.body).addClass(w),e.updateStatus("loading");var n=t(i.el).attr("data-storageid")||"nt94",a=t.extend({url:i.src,success:function(a,o,r){var d={data:a,xhr:r};p("ParseAjax",d),e.appendContent(t(d.data),"ajax"),i.finished=!0,$(),e._setFocus(),setTimeout(function(){e.wrap.addClass("mfp-ready")},16),e.updateStatus("ready"),p("AjaxContentAdded"),s[n]=a},error:function(){$(),i.finished=i.loadError=!0,e.updateStatus("error",e.st.ajax.tError.replace("%url%",i.src))}},e.st.ajax.settings),o=s[n];if(void 0!==o){var r={data:o};p("ParseAjax",r),e.appendContent(t(r.data),"ajax"),$(),e._setFocus(),setTimeout(function(){e.wrap.addClass("mfp-ready")},16),e.updateStatus("ready"),p("AjaxContentAdded")}else e.req=t.ajax(a);return""}}}),t.magnificPopupBee.registerModule("image",{options:{markup:'<div class="mfp-figure"><div class="mfp-close"></div><figure><div class="mfp-img"></div><figcaption><div class="mfp-bottom-bar"><div class="mfp-title"></div><div class="mfp-counter"></div></div></figcaption></figure></div>',cursor:"mfp-zoom-out-cur",titleSrc:"title",verticalFit:!0,tError:'<a href="%url%">The image</a> could not be loaded.'},proto:{initImage:function(){var i=e.st.image,n=".image";e.types.push("image"),u("Open"+n,function(){"image"===e.currItem.type&&i.cursor&&t(document.body).addClass(i.cursor)}),u("Close"+n,function(){i.cursor&&t(document.body).removeClass(i.cursor),c.off("resize.mfp")}),u("Resize"+n,e.resizeImage),e.isLowIE&&u("AfterChange",e.resizeImage)},resizeImage:function(){var t=e.currItem;if(t&&t.img&&e.st.image.verticalFit){var i=0;e.isLowIE&&(i=parseInt(t.img.css("padding-top"),10)+parseInt(t.img.css("padding-bottom"),10)),t.img.css("max-height",e.wH-i)}},_onImageHasSize:function(t){t.img&&(t.hasSize=!0,S&&clearInterval(S),t.isCheckingImgSize=!1,p("ImageHasSize",t),t.imgHidden&&(e.content&&e.content.removeClass("mfp-loading"),t.imgHidden=!1))},findImageSize:function(t){var i=0,n=t.img[0],a=function(o){S&&clearInterval(S),S=setInterval(function(){n.naturalWidth>0?e._onImageHasSize(t):(i>200&&clearInterval(S),3==++i?a(10):40===i?a(50):100===i&&a(500))},o)};a(1)},getImage:function(i,n){var a=0,o=function(){i&&(i.img[0].complete?(i.img.off(".mfploader"),i===e.currItem&&(e._onImageHasSize(i),e.updateStatus("ready")),i.hasSize=!0,i.loaded=!0,p("ImageLoadComplete")):++a<200?setTimeout(o,100):r())},r=function(){i&&(i.img.off(".mfploader"),i===e.currItem&&(e._onImageHasSize(i),e.updateStatus("error",s.tError.replace("%url%",i.src))),i.hasSize=!0,i.loaded=!0,i.loadError=!0)},s=e.st.image,d=n.find(".mfp-img");if(d.length){var l=document.createElement("img");l.className="mfp-img",i.el&&i.el.find("img").length&&(l.alt=i.el.find("img").attr("alt")),i.img=t(l).on("load.mfploader",o).on("error.mfploader",r),l.src=i.src,d.is("img")&&(i.img=i.img.clone()),(l=i.img[0]).naturalWidth>0?i.hasSize=!0:l.width||(i.hasSize=!1)}return e._parseMarkup(n,{title:function(i){if(i.data&&void 0!==i.data.title)return i.data.title;var n=e.st.image.titleSrc;if(n){if(t.isFunction(n))return n.call(e,i);if(i.el)return i.el.attr(n)||""}return""}(i),img_replaceWith:i.img},i),e.resizeImage(),i.hasSize?(S&&clearInterval(S),i.loadError?(n.addClass("mfp-loading"),e.updateStatus("error",s.tError.replace("%url%",i.src))):(n.removeClass("mfp-loading"),e.updateStatus("ready")),n):(e.updateStatus("loading"),i.loading=!0,i.hasSize||(i.imgHidden=!0,n.addClass("mfp-loading"),e.findImageSize(i)),n)}}}),t.magnificPopupBee.registerModule("zoom",{options:{enabled:!1,easing:"ease-in-out",duration:300,opener:function(t){return t.is("img")?t:t.find("img")}},proto:{initZoom:function(){var t,i=e.st.zoom,n=".zoom";if(i.enabled&&e.supportsTransition){var a,o,r=i.duration,s=function(t){var e=t.clone().removeAttr("style").removeAttr("class").addClass("mfp-animated-image"),n="all "+i.duration/1e3+"s "+i.easing,a={position:"fixed",zIndex:9999,left:0,top:0,"-webkit-backface-visibility":"hidden"},o="transition";return a["-webkit-"+o]=a["-moz-"+o]=a["-o-"+o]=a[o]=n,e.css(a),e},d=function(){e.content.css("visibility","visible")};u("BuildControls"+n,function(){if(e._allowZoom()){if(clearTimeout(a),e.content.css("visibility","hidden"),!(t=e._getItemToZoom()))return void d();(o=s(t)).css(e._getOffset()),e.wrap.append(o),a=setTimeout(function(){o.css(e._getOffset(!0)),a=setTimeout(function(){d(),setTimeout(function(){o.remove(),t=o=null,p("ZoomAnimationEnded")},16)},r)},16)}}),u("BeforeClose"+n,function(){if(e._allowZoom()){if(clearTimeout(a),e.st.removalDelay=r,!t){if(!(t=e._getItemToZoom()))return;o=s(t)}o.css(e._getOffset(!0)),e.wrap.append(o),e.content.css("visibility","hidden"),setTimeout(function(){o.css(e._getOffset())},16)}}),u("Close"+n,function(){e._allowZoom()&&(d(),o&&o.remove(),t=null)})}},_allowZoom:function(){return"image"===e.currItem.type},_getItemToZoom:function(){return!!e.currItem.hasSize&&e.currItem.img},_getOffset:function(i){var n,a=(n=i?e.currItem.img:e.st.zoom.opener(e.currItem.el||e.currItem)).offset(),o=parseInt(n.css("padding-top"),10),r=parseInt(n.css("padding-bottom"),10);a.top-=t(window).scrollTop()-o;var s={width:n.width(),height:(l?n.innerHeight():n[0].offsetHeight)-r-o};return void 0===_&&(_=void 0!==document.createElement("p").style.MozTransform),_?s["-moz-transform"]=s.transform="translate("+a.left+"px,"+a.top+"px)":(s.left=a.left,s.top=a.top),s}}});var k=function(t){if(e.currTemplate.iframe){var i=e.currTemplate.iframe.find("iframe");i.length&&(t||(i[0].src="//about:blank"),e.isIE8&&i.css("display",t?"block":"none"))}};t.magnificPopupBee.registerModule("iframe",{options:{markup:'<div class="mfp-iframe-scaler"><div class="mfp-close"></div><iframe class="mfp-iframe" src="//about:blank" frameborder="0" allowfullscreen></iframe></div>',srcAction:"iframe_src",patterns:{youtube:{index:"youtube.com",id:"v=",src:"//www.youtube.com/embed/%id%?autoplay=1"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%?autoplay=1"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}}},proto:{initIframe:function(){e.types.push("iframe"),u("BeforeChange",function(t,e,i){e!==i&&("iframe"===e?k():"iframe"===i&&k(!0))}),u("Close.iframe",function(){k()})},getIframe:function(i,n){var a=i.src,o=e.st.iframe;t.each(o.patterns,function(){if(a.indexOf(this.index)>-1)return this.id&&(a="string"==typeof this.id?a.substr(a.lastIndexOf(this.id)+this.id.length,a.length):this.id.call(this,a)),a=this.src.replace(/%id%/g,a),!1});var r={};return o.srcAction&&(r[o.srcAction]=a),e._parseMarkup(n,r,i),e.updateStatus("ready"),n}}});var T=function(t){var i=e.items.length;return t>i-1?t-i:t<0?i+t:t},I=function(t,e,i){return t.replace(/%curr%/gi,e+1).replace(/%total%/gi,i)};t.magnificPopupBee.registerModule("gallery",{options:{enabled:!1,arrowMarkup:'<button title="%title%" type="button" class="mfp-arrow mfp-arrow-%dir%"></button>',preload:[0,2],navigateByImgClick:!0,arrows:!0,tPrev:"Previous (Left arrow key)",tNext:"Next (Right arrow key)",tCounter:"%curr% of %total%"},proto:{initGallery:function(){var i=e.st.gallery,a=".mfp-gallery";if(e.direction=!0,!i||!i.enabled)return!1;o+=" mfp-gallery",u("Open"+a,function(){i.navigateByImgClick&&e.wrap.on("click"+a,".mfp-img",function(){if(e.items.length>1)return e.next(),!1}),n.on("keydown"+a,function(t){37===t.keyCode?e.prev():39===t.keyCode&&e.next()})}),u("UpdateStatus"+a,function(t,i){i.text&&(i.text=I(i.text,e.currItem.index,e.items.length))}),u("MarkupParse"+a,function(t,n,a,o){var r=e.items.length;a.counter=r>1?I(i.tCounter,o.index,r):""}),u("BuildControls"+a,function(){if(e.items.length>1&&i.arrows&&!e.arrowLeft){var n=i.arrowMarkup,a=e.arrowLeft=t(n.replace(/%title%/gi,i.tPrev).replace(/%dir%/gi,"left")).addClass("mfp-prevent-close"),o=e.arrowRight=t(n.replace(/%title%/gi,i.tNext).replace(/%dir%/gi,"right")).addClass("mfp-prevent-close");a.click(function(){e.prev()}),o.click(function(){e.next()}),e.container.append(a.add(o))}}),u("Change"+a,function(){e._preloadTimeout&&clearTimeout(e._preloadTimeout),e._preloadTimeout=setTimeout(function(){e.preloadNearbyImages(),e._preloadTimeout=null},16)}),u("Close"+a,function(){n.off(a),e.wrap.off("click"+a),e.arrowRight=e.arrowLeft=null})},next:function(){e.direction=!0,e.index=T(e.index+1),e.updateItemHTML()},prev:function(){e.direction=!1,e.index=T(e.index-1),e.updateItemHTML()},goTo:function(t){e.direction=t>=e.index,e.index=t,e.updateItemHTML()},preloadNearbyImages:function(){var t,i=e.st.gallery.preload,n=Math.min(i[0],e.items.length),a=Math.min(i[1],e.items.length);for(t=1;t<=(e.direction?a:n);t++)e._preloadItem(e.index+t);for(t=1;t<=(e.direction?n:a);t++)e._preloadItem(e.index-t)},_preloadItem:function(i){if(i=T(i),!e.items[i].preloaded){var n=e.items[i];n.parsed||(n=e.parseEl(i)),p("LazyLoad",n),"image"===n.type&&(n.img=t('<img class="mfp-img" />').on("load.mfploader",function(){n.hasSize=!0}).on("error.mfploader",function(){n.hasSize=!0,n.loadError=!0,p("LazyLoadError",n)}).attr("src",n.src)),n.preloaded=!0}}}}),t.magnificPopupBee.registerModule("retina",{options:{replaceSrc:function(t){return t.src.replace(/\.\w+$/,function(t){return"@2x"+t})},ratio:1},proto:{initRetina:function(){if(window.devicePixelRatio>1){var t=e.st.retina,i=t.ratio;(i=isNaN(i)?i():i)>1&&(u("ImageHasSize.retina",function(t,e){e.img.css({"max-width":e.img[0].naturalWidth/i,width:"100%"})}),u("ElementParse.retina",function(e,n){n.src=t.replaceSrc(n,i)}))}}}}),m()});var fastdomBee=createCommonjsModule(function(t){!function(e){var i=function(){},n=e.requestAnimationFrame||e.webkitRequestAnimationFrame||e.mozRequestAnimationFrame||e.msRequestAnimationFrame||function(t){return setTimeout(t,16)};function a(){this.reads=[],this.writes=[],this.raf=n.bind(e)}function o(t){t.scheduled||(t.scheduled=!0,t.raf(function(t){var e,n=t.writes,a=t.reads;try{i("flushing reads",a.length),r(a),i("flushing writes",n.length),r(n)}catch(t){e=t}t.scheduled=!1,(a.length||n.length)&&o(t);if(e){if(i("task errored",e.message),!t.catch)throw e;t.catch(e)}}.bind(null,t)))}function r(t){for(var e;e=t.shift();)e()}function s(t,e){var i=t.indexOf(e);return!!~i&&!!t.splice(i,1)}a.prototype={constructor:a,measure:function(t,e){var i=e?t.bind(e):t;return this.reads.push(i),o(this),i},mutate:function(t,e){var i=e?t.bind(e):t;return this.writes.push(i),o(this),i},clear:function(t){return s(this.reads,t)||s(this.writes,t)},extend:function(t){if("object"!=_typeof(t))throw new Error("expected object");var e=Object.create(this);return function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])}(e,t),e.fastdom=this,e.initialize&&e.initialize(),e},catch:null};var d=e.fastdom=e.fastdom||new a;t.exports=d}("undefined"!=typeof window?window:commonjsGlobal)}),smoothscroll=createCommonjsModule(function(t,e){!function(){t.exports={polyfill:function(){var t=window,e=document;if(!("scrollBehavior"in e.documentElement.style&&!0!==t.__forceSmoothScrollPolyfill__)){var i,n=t.HTMLElement||t.Element,a=468,o={scroll:t.scroll||t.scrollTo,scrollBy:t.scrollBy,elementScroll:n.prototype.scroll||d,scrollIntoView:n.prototype.scrollIntoView},r=t.performance&&t.performance.now?t.performance.now.bind(t.performance):Date.now,s=(i=t.navigator.userAgent,new RegExp(["MSIE ","Trident/","Edge/"].join("|")).test(i)?1:0);t.scroll=t.scrollTo=function(){void 0!==arguments[0]&&(!0!==l(arguments[0])?h.call(t,e.body,void 0!==arguments[0].left?~~arguments[0].left:t.scrollX||t.pageXOffset,void 0!==arguments[0].top?~~arguments[0].top:t.scrollY||t.pageYOffset):o.scroll.call(t,void 0!==arguments[0].left?arguments[0].left:"object"!==_typeof(arguments[0])?arguments[0]:t.scrollX||t.pageXOffset,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:t.scrollY||t.pageYOffset))},t.scrollBy=function(){void 0!==arguments[0]&&(l(arguments[0])?o.scrollBy.call(t,void 0!==arguments[0].left?arguments[0].left:"object"!==_typeof(arguments[0])?arguments[0]:0,void 0!==arguments[0].top?arguments[0].top:void 0!==arguments[1]?arguments[1]:0):h.call(t,e.body,~~arguments[0].left+(t.scrollX||t.pageXOffset),~~arguments[0].top+(t.scrollY||t.pageYOffset)))},n.prototype.scroll=n.prototype.scrollTo=function(){if(void 0!==arguments[0])if(!0!==l(arguments[0])){var t=arguments[0].left,e=arguments[0].top;h.call(this,this,void 0===t?this.scrollLeft:~~t,void 0===e?this.scrollTop:~~e)}else{if("number"==typeof arguments[0]&&void 0===arguments[1])throw new SyntaxError("Value could not be converted");o.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left:"object"!==_typeof(arguments[0])?~~arguments[0]:this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top:void 0!==arguments[1]?~~arguments[1]:this.scrollTop)}},n.prototype.scrollBy=function(){void 0!==arguments[0]&&(!0!==l(arguments[0])?this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior}):o.elementScroll.call(this,void 0!==arguments[0].left?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,void 0!==arguments[0].top?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop))},n.prototype.scrollIntoView=function(){if(!0!==l(arguments[0])){var i=function(t){for(;t!==e.body&&!1===f(t);)t=t.parentNode||t.host;return t}(this),n=i.getBoundingClientRect(),a=this.getBoundingClientRect();i!==e.body?(h.call(this,i,i.scrollLeft+a.left-n.left,i.scrollTop+a.top-n.top),"fixed"!==t.getComputedStyle(i).position&&t.scrollBy({left:n.left,top:n.top,behavior:"smooth"})):t.scrollBy({left:a.left,top:a.top,behavior:"smooth"})}else o.scrollIntoView.call(this,void 0===arguments[0]||arguments[0])}}function d(t,e){this.scrollLeft=t,this.scrollTop=e}function l(t){if(null===t||"object"!==_typeof(t)||void 0===t.behavior||"auto"===t.behavior||"instant"===t.behavior)return!0;if("object"===_typeof(t)&&"smooth"===t.behavior)return!1;throw new TypeError("behavior member of ScrollOptions "+t.behavior+" is not a valid value for enumeration ScrollBehavior.")}function c(t,e){return"Y"===e?t.clientHeight+s<t.scrollHeight:"X"===e?t.clientWidth+s<t.scrollWidth:void 0}function u(e,i){var n=t.getComputedStyle(e,null)["overflow"+i];return"auto"===n||"scroll"===n}function f(t){var e=c(t,"Y")&&u(t,"Y"),i=c(t,"X")&&u(t,"X");return e||i}function p(e){var i,n,o,s,d=(r()-e.startTime)/a;s=d=d>1?1:d,i=.5*(1-Math.cos(Math.PI*s)),n=e.startX+(e.x-e.startX)*i,o=e.startY+(e.y-e.startY)*i,e.method.call(e.scrollable,n,o),n===e.x&&o===e.y||t.requestAnimationFrame(p.bind(t,e))}function h(i,n,a){var s,l,c,u,f=r();i===e.body?(s=t,l=t.scrollX||t.pageXOffset,c=t.scrollY||t.pageYOffset,u=o.scroll):(s=i,l=i.scrollLeft,c=i.scrollTop,u=d),p({scrollable:s,method:u,startTime:f,startX:l,startY:c,x:n,y:a})}}}}()});function onYouTubeIframeAPIReady(){document.dispatchEvent(new CustomEvent("youtube:ready"))}!function(t){"use strict";var e=t(window),i=t(document),n=e.width(),a=t("html"),o=t("body"),r=n<768,s=n<=1024,d=window.BEErequest.design_mode,l=window.BEEstrings,c=BEEThemeSP.cacheNameFirst,u=!!("ontouchstart"in window||window.DocumentTouch&&window.document instanceof DocumentTouch||window.navigator.maxTouchPoints||window.navigator.msMaxTouchPoints);BEEThemeSP.isHover=a.hasClass("beep-hover"),BEEThemeSP.isTouch=u&&(!BEEThemeSP.isHover||s),document.addEventListener("theme:hover",function(t){BEEThemeSP.isHover=!0,BEEThemeSP.isTouch=!1}),BEEThemeSP.getToFetchSection=function(t,e="text",i=null){let n=t?BEEThemeSP.root_url+t:i;return fetch(n,{method:"GET",headers:{"Cache-Control":"no-cache"}}).then(t=>t.redirected?"NVT_94":"text"==e?t.text():t.json()).then(t=>t).catch(t=>(console.warn(t),"NVT_94"))},BEEThemeSP.OverflowScroller=function(){function t(t,e){!t&&n>767||(this.element=t,this.options=e,this.lastKnownY=window.scrollY,this.currentTop=0,this.initialTopOffset=e.offsetTop||parseInt(window.getComputedStyle(this.element).top),this._attachListeners(),e.updateOffsetTop&&(this.initialTopOffsetCache=this.initialTopOffset,this._updateInitialTopOffset()))}return t.prototype=Object.assign({},t.prototype,{_updateInitialTopOffset:function(){window.addEventListener("BeeHeaderReveal",function(){this.initialTopOffset=this.initialTopOffsetCache}),window.addEventListener("BeeHeaderHide",function(){this.initialTopOffset=30})},_attachListeners:function(){this._checkPositionListener=this._checkPosition.bind(this),window.addEventListener("scroll",this._checkPositionListener)},_checkPosition:function(){var t=this;fastdomBee.measure(function(){var e=t.element.getBoundingClientRect().top+window.scrollY-t.element.offsetTop+t.initialTopOffset,i=t.element.clientHeight-window.innerHeight+(t.options.offsetBottom||0);window.scrollY<t.lastKnownY?t.currentTop-=window.scrollY-t.lastKnownY:t.currentTop+=t.lastKnownY-window.scrollY,t.currentTop=Math.min(Math.max(t.currentTop,-i),e,t.initialTopOffset),t.lastKnownY=window.scrollY}),fastdomBee.mutate(function(){t.element.style.top="".concat(t.currentTop,"px")})},destroy:function(){window.removeEventListener("scroll",this._checkPositionListener)}}),t}();var f=function(){var e="[data-swatch-item]",i="is--unavailable",n="is--soldout "+i,a=window.BEEProductStrings,r=a.unavailable,s=a.addToCart,d=a.soldOut,l=a.preOrder,c=(s=a.addToCart,a.replace_qs_atc),u=a.replace_qs_pre,f=a.badgeSavePercent2,p=a.badgeSaveFixed2,h="aria-disabled";function m(e){this.$container=e.$container,this.variants=e.variants,this.productOptions=e.productOptions,this.productOptionSize=e.PrOptionsSize,this.formSelectorId=e.formSelectorId,this.$formSelectorId=t(this.formSelectorId),this.$originalSelectorId=e.$originalSelectorId,this.originalSelectorId=this.$originalSelectorId[0],this.enableHistoryState=e.enableHistoryState,this.removeSoldout=e.removeSoldout,this.$options1=e.$options1,this.$options2=e.$options2,this.$options3=e.$options3,this.isNoPick=e.isNoPick,this.isNoPickOriginal=e.isNoPick,this.hasSoldoutUnavailable=e.hasSoldoutUnavailable,this.canMediaGroup=e.canMediaGroup,this.badgesConfigs=e.badgesConfigs,this.$variantImg=e.$variantImg,this.disableVariantImage=e.disableVariantImage,this.swatchWidth=e.swatchWidth,this.$incomingMess=this.$formSelectorId.find("[data-incoming__mess"),this.isSticky=e.isSticky,this.useStickySelect=e.useStickySelect,this.isMainProduct=e.isMainProduct,this.$quantity=this.$formSelectorId.find("[data-quantity-value"),this.$mainMedia=this.$container.find("[data-main-media]"),this.$mainNav=this.$container.find(".bee-carousel__nav"),this.clickedOptions=[],this.showFirstMedia=!this.isNoPickOriginal&&e.showFirstMedia,this.oldVariant={},this.currentVariant={},this.mediaID=0,this.eventClickedSwatch=!1,this.variantState={available:!0,soldOut:!1,onSale:!1,preOrder:!1,showUnitPrice:!1},this.$productPrice=this.$container.find("[data-product-price]"),this.formartPrice="ins-del"==this.$productPrice.data("formartPrice")?"<ins>money_ins</ins> <del>money_del</del>":"<del>money_del</del> <ins>money_ins</ins>",this.saletype=this.$productPrice.data("saletype");let i=this.$container.find("[data-product-unit-price]");this.$unit_price=i.find("[data-unit-price]"),this.$unit_base=i.find("[data-unit-base]");let n=this.$container.find("[data-product-single-badge]"),a=this.badgesConfigs.texts,o=this.badgesConfigs.saleStyle;if(this.badgeSelector={$onSale:n.find("[data-badge-sale]"),$preOrder:n.find("[data-badge-preorder]"),$soldOut:n.find("[data-badge-soldout]")},this.saleLabel="2"==o?a.SavePercent:a.sale,this.useComingMess=!1,this.$incomingMess[0]&&(this.useComingMess=!0,this.$incomingAvailable=this.$incomingMess.find("[data-incoming-available]"),this.$incomingSoldout=this.$incomingMess.find("[data-incoming-soldout]"),this.$incomingAvailableDate=this.$incomingAvailable.find("[data-incoming-date]"),this.$incomingSoldoutDate=this.$incomingSoldout.find("[data-incoming-date]")),this.$addToCartButton=this.formSelectorId.find('[type="submit"][name="add"]'),this.$quantityWrapper=this.formSelectorId.find("[data-quantity-wrapper]"),this.$paymentButton=this.formSelectorId.find(".shopify-payment-button"),this.$addToCartButtonText=this.$addToCartButton.find(".bee-btn-atc_text"),this.isSticky){let e=t("[data-sticky-addtocart]");this.$stickyimg=e.find("[data-sticky-img] img"),this.$stickyVtitle=e.find("[data-sticky-v-title]"),this.$stickyPrice=e.find("[data-sticky-price]"),this.$stickyATC=e.find("[data-action-atc]"),this.$stickyATCText=this.$stickyATC.find(".bee-btn-atc_text"),this.$stickySelect=e.find("[data-sticky-select]"),this.stickyImgOrginal=this.$stickyimg.data("orginal"),this.$stickyQuantityWrapper=e.find("[data-quantity-wrapper]"),this.$stickyQuantity=this.$stickyQuantityWrapper.find("[data-quantity-value]"),this.isStickyChanging=!1,(r=this).$stickySelect.on("change:drop",function(t,e,i){r.eventClickedSwatch=!1,r.isStickyChanging=!0,r.originalSelectorId.value=i,r.originalSelectorId.dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0}))})}var r;(BEEThemeSP.isEditCartReplace&&(this.txt_addToCart=c,this.txt_preOrder=u),this.unQuickShopInline=e.unQuickShopInline,this.isQuickShopForm=e.isQuickShopForm,this.$imgMainItem=this.$container.find("[data-main-img-change]"),e.unQuickShopInline)?(this.originalSelectorId.addEventListener("change",this._onSelectChange.bind(this)),this._updateSwatchFromSizeOne(),this.isNoPick?this.currentVariant=this._getVariantFromVariantid():this.originalSelectorId.dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0}))):((r=this).$container.one("replace:btnAtc",function(){r.$addToCartButton=r.$container.find(".bee-pr-addtocart"),r.$quantityWrapper=r.$container.find("[data-quantity-wrapper]"),r.$addToCartButtonText=r.$addToCartButton.find(".bee-text-pr")}),n=r.$container.find("[data-product-badge]"),r.badgeSelector={$onSale:n.find("[data-badge-sale]"),$preOrder:n.find("[data-badge-preorder]"),$soldOut:n.find("[data-badge-soldout]")},r.$dataHref=r.$container.find("[data-pr-href]"),r.productHref=r.$dataHref.attr("href"),r.currentVariant=r._getVariantFromVariantid(),r.$originalSelectorId.on("change",r._onQuickShopInlineChange.bind(r)),r._updateSwatchFromSizeOne())}return m.prototype=Object.assign({},m.prototype,{_onSelectChange:function(){this.eventClickedSwatch||(this.oldVariant=this.currentVariant);var t=this.eventClickedSwatch?this.currentVariant:this._getVariantFromVariantid();this._setVariantState(t),this._updateSwatchSelector(t,this.oldVariant,this.formSelectorId,this.hasSoldoutUnavailable),this._updatePrice(t,this.oldVariant,this.$container),this._updateAddToCartButton(t,this.oldVariant,this.$addToCartButton,this.$quantityWrapper,this.$paymentButton,this.$addToCartButtonText),this._updateAvailability(t,this.oldVariant,this.$container),this._updateSKU(t,this.oldVariant,this.$container),this._updateBarcode(t,this.oldVariant,this.$container),this._updateMetafield(t,this.oldVariant,this.$container),this._updateDelivery(t,this.oldVariant,this.$container),this._updateInventoryQuantity(t,this.oldVariant,this.$container),this._updatePickupAvailabilityContent(t,this.$container),this._updateNotifyBackinStock(t,this.$container),this._updateBadges(),this._updateIncomingMess(t),t&&(this.currentVariant=t,this.canMediaGroup&&this._updateMediaFilter(t,this.oldVariant,this.$container),this._updateMedia(t,this.oldVariant,this.$container),this._updateQuantity(t),this.disableVariantImage||this._updateVariantImageSwatch(t),this.isSticky&&this._updateStickyATC(t),this.enableHistoryState&&this._updateHistoryState(t),this.$container.trigger({type:"variant:changed",currentVariant:t,oldVariant:this.oldVariant}))},_onQuickShopInlineChange:function(){this.notSelected=!0,this.eventClickedSwatch||(this.oldVariant=this.currentVariant);var t=this.eventClickedSwatch?this.currentVariant:this._getVariantFromVariantid();this._setVariantState(t),this._updateSwatchSelector(t,this.oldVariant,this.formSelectorId,this.hasSoldoutUnavailable),this._updatePrice(t,this.oldVariant,this.$container),this._updateAtcBtnQSInline(t,this.oldVariant,this.$addToCartButton,this.$quantityWrapper,this.$addToCartButtonText),this._updateBadges(),t&&(this.currentVariant=t,this._updateMedia(t,this.oldVariant,this.$container),this._updateQuantity(t),this.$dataHref.attr("href",this._getUrlWithVariant(this.productHref,t.id)),this.disableVariantImage||this._updateVariantImageSwatch(t),this.$container.trigger({type:"variant:changed",currentVariant:t,oldVariant:this.oldVariant}))},_getVariantFromOptions:function(){var t=this.clickedOptions;return this.variants.find(function(e){return t.every(function(t){return e[t.index]===t.value})})||"nathan"},_getVariantFromSize:function(){var e,i=this.variants,n=this.productOptionSize,a=this.removeSoldout,o=this.clickedOptions[0].value,r=this.clickedOptions[1],s=(this.clickedOptions[2],this.clickedCurrentValue),d=this.clickedCurrentIndex;return 1==n?e=t.grep(i,function(t,e){return t.available}):3==n&a?(r=r.value,(e=t.grep(i,function(t,e){return t.option1==o&&t.option2==r&&t.available}))[0]||(e=t.grep(i,function(t,e){return t.available&&t[d]==s}))):a?(e=t.grep(i,function(t,e){return t.option1==o&&t.available}))[0]||(e=t.grep(i,function(t,e){return t.available&&t[d]==s})):3==n?(r=r.value,(e=t.grep(i,function(t,e){return t.option1==o&&t.option2==r}))[0]||(e=t.grep(i,function(t,e){return t[d]==s}))):(e=t.grep(i,function(t,e){return t.option1==o}))[0]||(e=t.grep(i,function(t,e){return t[d]==s})),e[0]},_getVariantFromVariantid:function(){var t=[],e=this.variants,i=e.length,n=this.$originalSelectorId.val();for(let a=0;a<i;a++)if(e[a].id==n){t[0]=e[a];break}return t[0]||null},_getVariantFromOptionIndex:function(e,i){var n,a=this.variants,o=i.option1,r=i.option2,s=i.option3;switch(e){case 1:n=t.grep(a,function(t,e){return t.option1==o});break;case 2:n=t.grep(a,function(t,e){return t.option2==r});break;case 3:n=t.grep(a,function(t,e){return t.option3==s});break;case 1.2:n=t.grep(a,function(t,e){return t.option1==o&&t.option2==r});break;default:n=t.grep(a,function(t,e){return 0==t.available})}return n||"nathan"},_updateMediaFilterNoPick:function(){if(this.clickedCurrentValue&&this.clickedCurrentIndex&&this.canMediaGroup){var t=this.clickedCurrentIndex.replace("option",""),e=this.productOptions[parseInt(t)-1].name||"nobee",i=this.clickedCurrentValue||"nobee",n=this.$mainMedia,a=this.$mainNav,o=`[data-grname="${(e+"").toLowerCase()}"][data-grpvl="${(i+"").toLowerCase()}"]`,r=n.find(o),s=a.find(o);0!=r.length&&(n.find("[data-main-slide]").addClass("is--media-hide"),r.removeClass("is--media-hide"),a.find(".bee-carousel__nav-item").addClass("is--media-hide"),s.removeClass("is--media-hide"),n.hasClass("flickitybee-enabled")?(n.trigger("update.flickitybee"),s.hasClass("is-nav-selected")||s.first().addClass("is-nav-selected")):n.hasClass("isotopebee-enabled")&&(b(n),n.isotopebee()))}},_updateSwatchFromSizeOne:function(){var t,e=this.variants,i=e.length,n=!1,a=0,o=this.productOptionSize,r=this.productOptions;if(3==o)var s=r[0].values.length,d=r[1].values.length,l=r[2].values.length;else if(2==o)s=r[0].values.length,d=r[1].values.length;if(o<2?(n=1,t=this.$options1):2==o&&1==s?(n=1,t=this.$options2,a=1):2==o&&1==d?(n=1,t=this.$options1):3==o&&1==s&&1==d?(n=1,t=this.$options3,a=2):3==o&&1==s&&1==l?(n=1,t=this.$options2,a=1):3==o&&1==d&&1==l?(n=1,t=this.$options1):3==o&&1==l&&(n=2),this.hasSoldoutUnavailable){let t=r[a].values,n=t.length,o=this[`$options${a+1}`].find("[data-swatch-item]");for(let r=0;r<n;r++){let n=!0,s=t[r];for(let t=0;t<i;t++){let i=e[t];if(i.options[a]==s&&i.available){n=!1;break}}n&&o.eq(r).addClass("is--soldout")}}this.getProductSize=n,this.$optionsOne=t,this.$optionsOneIndex=a},_updateMediaFilter:function(t,e,i){if(this.currentVariant&&this.canMediaGroup){var n,a,o,r,s,d=this.productOptions,l=this.productOptionSize,c=this.currentVariant,u=this.$mainMedia,f=this.$mainNav;for(let t=0;t<l;t++){if(r=d[t].name||"nobee",0==u.find(`[data-grname="${(r+"").toLowerCase()}"]`).length)return;s=c.options[t]+"",n=`[data-grname="${(r+"").toLowerCase()}"][data-grpvl="${s.toLowerCase()}"]`;break}if(a=u.find(n),o=f.find(n),0!=a.length&&s!=this.groupValue)if(this.groupValue=s,u.find("[data-main-slide]").addClass("is--media-hide"),a.removeClass("is--media-hide"),f.find(".bee-carousel__nav-item").addClass("is--media-hide"),o.removeClass("is--media-hide"),u.hasClass("flickitybee-enabled")){if(u.trigger("update.flickitybee"),t.featured_media)var p=u.find(`[data-media-id="${t.featured_media.id}"`).index();u.flickitybee("selectCell",p,!1,!1)}else u.hasClass("isotopebee-enabled")&&(b(u),u.isotopebee())}},_updateSwatchSelector:function(t,a,o,r){var s,d,l,c,u,f,p,h=1,m=this.$options1,g=this.$options2,v=this.$options3,y=this.getProductSize||this.productOptionSize,b=[],w=0,S=t.option1,_=t.option2,$=t.option3,C=[],k=[],T=[];if(o.find(".is--selected").removeClass("is--selected"),o.find("[data-current-value]").html(""),C=this.productOptions[0].values,m.find("[data-current-value]").html(S),m.find(e).eq(C.indexOf(S)).addClass("is--selected"),g[0]&&(k=this.productOptions[1].values,g.find("[data-current-value]").html(_),g.find(e).eq(k.indexOf(_)).addClass("is--selected")),v[0]&&(T=this.productOptions[2].values,v.find("[data-current-value]").html($),v.find(e).eq(T.indexOf($)).addClass("is--selected")),r)switch(y){case 3:for(1==T.length?(s=m,d=C,l="option3",c=$,u="option1",h=3):(s=v,d=T,l="option1",c=S,u="option3"),w=(b=this._getVariantFromOptionIndex(h,t)).length,s.find(e).addClass(n),g.find(e).addClass(n),p=0;p<w;p++)(f=b[p])[l]==c&&(f.available?(g.find(e).eq(k.indexOf(f.option2)).removeClass(n),f.option2==_&&s.find(e).eq(d.indexOf(f[u])).removeClass(n)):(g.find(e).eq(k.indexOf(f.option2)).removeClass(i),f.option2==_&&s.find(e).eq(d.indexOf(f[u])).removeClass(i)));break;case 2:for(w=(b=this._getVariantFromOptionIndex(1,t)).length,g.find(e).addClass(n),p=0;p<w;p++)(f=b[p]).option1==S&&g.find(e).eq(k.indexOf(f.option2)).removeClass(f.available?n:i);break;default:this.removeSoldout&&this.$optionsOne.find(".is--selected").is(":hidden")&&this.$optionsOne.find("[data-swatch-item]:visible:first").trigger("click")}},_updateMetafield:function(e,i,n){e&&e.id!=i.id&&(n.find("[data-variant-toggle]").hide(),n.find(`[data-variant-toggle="${e.id}"]`).show(),this.isMainProduct&&(t("[data-variant-tab][data-variant-toggle]").hide(),t(`[data-variant-tab][data-variant-toggle="${e.id}"]`).show()))},_updateMedia:function(t,e,i){if(t.featured_media&&JSON.stringify(t.featured_media)!==JSON.stringify(e.featured_media)&&!this.showFirstMedia){if(!this.unQuickShopInline||this.isQuickShopForm){let e=t.featured_media.preview_image;return this.$imgMainItem.attr("data-srcset",BEEThemeSP.Images.getNewImageUrl(e.src,1)),void(this.notSelected&&(this.$container.addClass("bee-colors-selected"),this.notSelected=!1))}this.mediaID=t.featured_media.id;var n=i.find("[data-main-media]");if(n.hasClass("flickitybee-enabled")){var a=n.find('[data-media-id="'+this.mediaID+'"]').index();n.flickitybee("select",a,!1,!0),this.eventClickedSwatch=!1}else{var o=n.find('[data-media-id="'+this.mediaID+'"]'),r=o[0];if(!r||BEEThemeSP.isVisible(o)||this.isStickyChanging)return;this.header||(this.header=document.querySelector(".bee-section-header")),this.header.dispatchEvent(new Event("preventHeaderReveal")),window.setTimeout(()=>{n[0].scrollLeft=0,r.scrollIntoView({behavior:"smooth"})})}}else this.showFirstMedia=!1},_updateMediaFirst:function(t){if(this.unQuickShopInline)return;var e=t.closest("[data-swatch-option]");if(!e.hasClass("is-bee-style__color"))return;let i=this.variants,n=i.length,a=e.data("id");let o=function(t){for(let e=0;e<n;e++){let n=i[e];if(n.featured_media&&(n.options[a]+"").toLowerCase()==t)return n.featured_media.preview_image}}((t.data("value")+"").toLowerCase());o&&this.$imgMainItem.attr("data-srcset",BEEThemeSP.Images.getNewImageUrl(o.src,1))},_updatePrice:function(t,e,i){if(!t)return void this.$productPrice.hide();let n=t.price,a=t.compare_at_price;if(!this.isNoPickOriginal&&n===e.price&&a===e.compare_at_price&&t.unit_price===e.unit_price)return;this.isNoPickOriginal&&(this.isNoPickOriginal=!1);let r=BEEThemeSP.Currency.formatMoney(n);if(this.$productPrice.show(),this.variantState.onSale){let t=BEEThemeSP.Currency.formatMoney(a),e=this.formartPrice.replace("money_ins",r).replace("money_del",t),i=a-n,o=100*i/a,s=Math.round(o);this.isSticky&&this.$stickyPrice.html(e),this.badgeSelector.$onSale.html(this.saleLabel.replace("[sale]",s)),"1"==this.saletype?e+=` <span class="bee-badge-price">${f.replace("[sale]",s)}</span>`:"2"==this.saletype&&(e+=` <span class="bee-badge-price">${p.replace("[sale]",BEEThemeSP.Currency.formatMoney(i))}</span>`),this.$productPrice.html(e)}else this.$productPrice.html(r),this.isSticky&&this.$stickyPrice.html(r);this.variantState.showUnitPrice&&(this.$unit_price.html(BEEThemeSP.Currency.formatMoney(t.unit_price)),this.$unit_base.html(BEEThemeSP.Currency.getBaseUnit(t))),this.$container.find("shopify-payment-terms").attr("variant-id",t.id),o.trigger("currency:update")},_updateQuantity:function(t){if(this.variantState.preOrder)this.$quantity.attr("max",9999),this.isSticky&&this.$stickyQuantity.attr("max",9999);else if(null!=t.inventory_management&&"continue"!=t.inventory_policy){let e=t.inventory_quantity;this.$quantity.attr("max",e),this.isSticky&&this.$stickyQuantity.attr("max",e),parseInt(this.$quantity.val())>e&&this.$quantity.attr("value",1).val(1),this.isSticky&&parseInt(this.$stickyQuantity.val())>e&&this.$stickyQuantity.attr("value",1).val(1)}else this.$quantity.attr("max",9999),this.isSticky&&this.$stickyQuantity.attr("max",9999)},_updateAvailability:function(t,e,i){var n=i.find("[data-product-available]");if(n[0]){var a=n.find("[data-available-status]"),o=n.find("[data-soldout-status]"),r=n.find("[data-instock-status]"),s=n.find("[data-preorder-status]");t?(n.show(),this.variantState.available?(a.show(),o.hide(),this.variantState.preOrder?(s.show(),r.hide()):(r.show(),s.hide())):(o.show(),a.hide())):n.hide()}},_updateBarcode:function(t,e,i){var n=i.find("[data-product-barcode]");if(n[0]){var a=n.find("[data-product__barcode-number]");if(t&&""!==t.barcode){if(e&&e.barcode===t.barcode)return;a.text(t.barcode),n.show(0)}else n.hide(0)}},_updateSKU:function(t,e,i){var n=i.find("[data-product-sku]");if(n[0]){var a=n.find("[data-product__sku-number]");if(t&&""!==t.sku){if(e&&e.sku===t.sku)return;a.text(t.sku),n.show(0)}else n.hide(0)}},_updateAddToCartButton:function(t,e,i,n,a,o){if(i[0]||a[0])if(BEEThemeSP.isEditCartReplace&&!i.is("[data-replace-item]")&&i.attr("data-replace-item",""),t&&"nathan"!=t)if(t.available){let t=this.variantState.preOrder?this.txt_preOrder||l:this.txt_addToCart||s;n.show(),i.removeAttr("disabled "+h).attr("data-atc-form",""),o.text(t),a.show(),this.isSticky&&(this.$stickyQuantityWrapper.show(),this.$stickyATC.removeAttr("disabled "+h),this.$stickyATCText.text(t))}else n.hide(),i.attr("disabled","disabled").attr(h,!0).removeAttr("data-atc-form",""),o.text(d),a.hide(),this.isSticky&&(this.$stickyQuantityWrapper.hide(),this.$stickyATC.attr("disabled","disabled").attr(h,!0),this.$stickyATCText.text(d));else i.attr("disabled","disabled").attr(h,!0).removeAttr("data-atc-form"),o.text(r),n.hide(),a.hide(),this.isSticky&&(this.$stickyQuantityWrapper.hide(),this.$stickyATC.attr("disabled","disabled").attr(h,!0),this.$stickyATCText.text(r))},_updateAtcBtnQSInline:function(t,e,i,n,a){if(i[0])if(t&&"nathan"!=t)if(t.available){let e=this.variantState.preOrder?this.txt_preOrder||l:this.txt_addToCart||s;n.show(),i.removeAttr("disabled "+h).attr("data-action-atc","").attr("data-variant-id",t.id),a.text(e)}else n.hide(),i.attr("disabled","disabled").attr(h,!0).removeAttr("data-action-atc",""),a.text(d);else i.attr("disabled","disabled").attr(h,!0).removeAttr("data-action-atc"),a.text(r),n.hide()},_updateDelivery:function(t,e,i){var n=i.find("[data-order-delivery]");if(n[0])if(t&&t.available){var a=w(n.attr("data-order-delivery"));this.variantState.preOrder&&a.hideWithPreorder?n.hide():n.show()}else n.hide()},_updateInventoryQuantity:function(t,e,i){var n=i.find("[data-inventory-qty]");n[0]&&(t&&t.available?n.trigger({type:"variant:inventory",currentVariant:t,oldVariant:this.oldVariant}):n.hide())},_updatePickupAvailabilityContent:function(t,e){let i=t.available?"pickupAvailability:update":"pickupAvailability:clear";e.trigger({type:i,currentVariant:t})},_updateNotifyBackinStock:function(t,e){let i=this.variantState.available?"notifyBackinStock:hide":"notifyBackinStock:show";e.trigger({type:i,currentVariant:t})},_updateBadges:function(){let t=this.variantState,e=this.badgeSelector;t.onSale?e.$onSale.removeAttr("hidden"):e.$onSale.attr("hidden",!0),t.preOrder?e.$preOrder.removeAttr("hidden"):e.$preOrder.attr("hidden",!0),t.soldOut?e.$soldOut.removeAttr("hidden"):e.$soldOut.attr("hidden",!0)},_setVariantState:function(t){t?this.variantState={available:t.available,soldOut:!t.available,onSale:t.compare_at_price>t.price,showUnitPrice:!!t.unit_price,preOrder:"shopify"==t.inventory_management&&t.inventory_quantity<=0&&t.available}:this.variantState.available=!1},_updateVariantImageSwatch:function(t){if(!t.featured_image)return;let e=this.$variantImg.find(".is--selected"),i=e.find("[data-img-el]");(e=i[0]?i:e).attr("data-bg",BEEThemeSP.Images.getNewImageUrl(t.featured_image.src,this.swatchWidth))},_updateIncomingMess:function(t){if(!this.useComingMess)return;let e=t.next_incoming_date,i=t.inventory_quantity,n=t.incoming,a=t.inventory_management;t&&e&&!(i>0)&&n&&"shopify"==a?(this.$incomingMess.removeAttr("hidden"),this.variantState.available?(this.$incomingAvailableDate.html(e),this.$incomingSoldout.hide(),this.$incomingAvailable.show()):(this.$incomingSoldoutDate.html(e),this.$incomingAvailable.hide(),this.$incomingSoldout.show())):this.$incomingMess.attr("hidden","")},_updateStickyATC:function(t){this.isStickyChanging=!1,this.$stickyimg.attr("data-src",t.featured_image?BEEThemeSP.Images.lazyloadImagePath(t.featured_image.src):this.stickyImgOrginal),this.useStickySelect?t.available&&(this.$stickyVtitle.find("[data-dropdown-open]>span").text(t.title),this.$stickySelect.find("[data-dropdown-item]").removeClass("is--selected"),this.$stickySelect.find(`[data-dropdown-item][data-value="${t.id}"]`).addClass("is--selected")):this.$stickyVtitle.html(t.title),this.$stickyATC.attr("data-variant-id",t.id)},_updateHistoryState:function(t){if(history.replaceState&&t){var e=window.location.protocol+"//"+window.location.host+window.location.pathname+"?variant="+t.id;window.history.replaceState({path:e},"",e),void 0!==window.addthis&&addthis.layers.refresh()}},_getUrlWithVariant:function(t,e){return/variant=/.test(t)?t.replace(/(variant=)[^&]+/,"$1"+e):/\?/.test(t)?t.concat("&variant=").concat(e):t.concat("?variant=").concat(e)}}),m}(),p=function(){var e="data-animation-atc",i=BEEconfigs.timezone,n="bee_nt_guess";try{n=dayjs.tz.guess()}catch(t){}var a="nobee"!=i&&n!=i;function o(t){this.$container=t,this.BootSalesInt()}function r(t){if(0!=t.length){var i=w(t.attr(e)),n=i.ani;if("none"!=n){var a="is--animated "+n,o=parseInt(i.time),r=parseInt(i.animTime)||1e3;setInterval(function(){t.addClass(a),setTimeout(function(){t.removeClass(a)},r)},o)}}}return o.prototype=Object.assign({},o.prototype,{BootSalesInt:function(){this._liveView(),this._flashSold(),this._animationATC(),this._orderDelivery(),this._inventoryQuantity(),this._countdown()},_getRandomInt:function(t,e){return Math.floor(Math.random()*(e-t+1))+t},_animationATC:function(){var t=this.$container.find("["+e+"]");return r(t),void t.length},_liveView:function(){var t=this.$container.find("[data-live-view]");if(0!=t.length){var e=w(t.attr("data-live-view")),i=this,n=e.min,a=e.max,o=e.interval,r=i._getRandomInt(n,a),s=["1","2","4","3","6","10","-1","-3","-2","-4","-6"],d=["10","20","15"],l="",c="",u="",f=t.find("[data-count]");p(),t.show(),setInterval(p,o)}function p(){if(l=Math.floor(Math.random()*s.length),c=s[l],r=parseInt(r)+parseInt(c),n>=r){u=Math.floor(Math.random()*d.length);var t=d[u];r+=t}(r<n||r>a)&&(r=i._getRandomInt(n,a)),f.html(parseInt(r))}},_flashSold:function(){var t=this.$container.find("[data-flash-sold]");if(0!=t.length){var e=w(t.attr("data-flash-sold")),i=this,n=e.mins,a=e.maxs,o=e.mint,r=e.maxt,s=e.id,d=sessionStorage.getItem("soldS"+s)||i._getRandomInt(n,a),l=sessionStorage.getItem("soldT"+s)||i._getRandomInt(o,r),c=parseInt(d),u=parseInt(l),f=parseInt(e.time),p=t.find("[data-sold]"),h=t.find("[data-hour]");g(),m(c,u),t.show(),setInterval(function(){c+=i._getRandomInt(1,4),u+=1*(Math.random()*(.8-.1)+.1).toFixed(1),g(),m(c,u)},f)}function m(t,e){p.html(t),h.html(Math.floor(u)),sessionStorage.setItem("soldS"+s,t),sessionStorage.setItem("soldT"+s,e)}function g(){c>a&&(c=i._getRandomInt(n,a)),u>r&&(u=i._getRandomInt(o,r))}},_orderDelivery:function(){var e=this.$container.find("[data-order-delivery]");if(0!=e.length){var n=w(e.attr("data-order-delivery")),o=n.format_day,r=n.time.replace("24:00:00","23:59:59")||"19041994",s=["SUN","MON","TUE","WED","THU","FRI","SAT"],d=n.estimateStartDate||0,l=n.estimateEndDate||0,c=n.cut_day.replace(/ /g,"").split(","),u=dayjs(),f=0,p=dayjs(),h=0,m=n.timezone,g=dayjs(),v=g.format("HHmmss"),y=r.replace(/ /g,"").replace(/:/g,""),b=BEEProductStrings.order_dayNames.replace(/ /g,"").split(","),S=BEEProductStrings.order_monthNames.replace(/ /g,"").split(",");if(a&&m)try{v=(g=dayjs.tz(g,i)).format("HHmmss")}catch(t){console.log("Timezone error: "+i)}if(parseInt(v)>=parseInt(y)&&(g=g.add(1,"day"),u=u.add(1,"day"),p=p.add(1,"day")),"2"==n.mode){for(;c.indexOf(s[u.format("d")])>-1;)u=u.add(1,"day");for(;f<d;)f++,u=u.add(1,"day"),c.indexOf(s[u.format("d")])>-1&&f--;for(;c.indexOf(s[p.format("d")])>-1;)p=p.add(1,"day");for(;h<l;)h++,p=p.add(1,"day"),c.indexOf(s[p.format("d")])>-1&&h--}else{for(u=u.add(d,"day");c.indexOf(s[u.format("d")])>-1;)u=u.add(1,"day");for(p=p.add(l,"day");c.indexOf(s[p.format("d")])>-1;)p=p.add(1,"day")}b=B(b),S=B(S);var _=parseInt(u.format("D")),$=_+M(_),C=S[parseInt(u.format("M"))-1],k=b[parseInt(u.format("d"))],T=parseInt(p.format("D")),I=T+M(T),E=S[parseInt(p.format("M"))-1],x=b[parseInt(p.format("d"))];if(e.find("[data-start-delivery]").html(u.format(o).replace("bee4",k).replace("bee5",$).replace("bee6",C)),e.find("[data-end-delivery]").html(p.format(o).replace("bee4",x).replace("bee5",I).replace("bee6",E)),"19041994"!=r){var P=e.find("[data-hour-delivery]");P.countdown(g.format("YYYY-MM-DD "+r),{elapse:!0}).on("update.countdown",function(i){if(i.elapsed)e.hide();else{var n=24*i.offset.totalDays+i.offset.hours;P.html(i.strftime(t.trim(P.html().replace("[totalHours]",n)))).show()}})}e.show()}function B(t){return t.filter(function(t,e,i){return i.indexOf(t)===e})}function M(t){if(t>3&&t<21)return"th";switch(t%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}}},_inventoryQuantity:function(){var t=this.$container.find("[data-inventory-qty]");if(0!=t.length){t.removeAttr("data-ttcalc");var e=w(t.attr("data-inventory-qty")),i=this,n=e.stock,a=e.qty,o=e.total,r=e.min,s=e.max,l=e.reduce,c=e.bgprocess,u=e.bgten,f=e.id,p=e.inventoryQty||0,h=null,m=null,g=1,v=1.7,y=.17,b=i._getRandomInt(r,s),S=t.find("[data-count]"),_=t.find("[data-progressbar]"),$=t.find("[data-message]"),C=_.find(">div");if(t.on("variant:inventory",function(e){if("2"!=n){var o=e.currentVariant,d=o.inventory_quantity||0;if(f=o.id,(d>=a||d<1)&&"1"==n)t.hide();else{(d>=a||0==d)&&isStorageSpSession&&(d=sessionStorage.getItem("probar"+f)||i._getRandomInt(r,s),t.attr("data-variant-qty"+f,d)),t.attr("data-variant-qty"+f,d),sessionStorage.setItem("probar"+f,d),S.text(d);var l=100*d/t.attr("data-ttcalc"),p=d<10?u:c;C.css({"background-color":p,width:l+"%"}),$.show(),_.show(),t.show()}}else t.show()}),!(p>=a||p<1)||"1"!=n){if(p<a&&p>0&&(b=p),isStorageSpSession&&!d&&"1"!=n){var k=sessionStorage.getItem("probar"+f);k>0&&(b=k)}S.text(b).css({"background-color":"#fff",color:c}),T(b,c,u),$.show(),_.show(),function(){if(!l)return;h=setTimeout(function(){--b<g&&(b=I(t.attr("data-variant-qty"+f))||i._getRandomInt(r,s)),S.css({"background-color":c,color:"#fff"}),setTimeout(function(){S.css({"background-color":"#fff",color:c})},1800),S.text(b),T(b,c,u)},6e4*y),m=setInterval(function(){--b<g&&(b=I(t.attr("data-variant-qty"+f))||i._getRandomInt(r,s),t.on("destroy:inventoryQty").hide()),S.css({"background-color":c,color:"#fff"}),setTimeout(function(){S.css({"background-color":"#fff",color:c})},1800),S.text(b),T(b,c,u)},6e4*v)}(),t.on("destroy:inventoryQty",function(){clearTimeout(h),clearInterval(m)}),t.on("update:inventoryQty",function(){var e=parseInt(S.text())-1;if(!(e<1)){S.text(e);var i=100*e/t.attr("data-ttcalc"),n=e<10?u:c;C.css({"background-color":n,width:i+"%"})}})}}function T(e,i,n){e=parseInt(e),isStorageSpSession&&sessionStorage.setItem("probar"+f,e),o=t.attr("data-ttcalc")||o>e?o:e+o,t.attr("data-ttcalc",o);var a=100*e/o,r=e<10?n:i;C.css("background-color",r),setTimeout(function(){C.css("width",a+"%")},300),C.css("background-color",r)}function I(t){return t||0}},_countdown:function(){var t=this.$container.find("[data-countdown-pr]");if(0!=t.length){var e,n=t.find("[data-cd-options]"),o=w(n.attr("data-cd-options"));if(!o.isCountdownMeta){e=o.cd_date.replace("24:00:00","23:59:59").split(",");var r,s,d=dayjs(),l=d.format("HHmmss"),c=e.length;if(a)try{l=(d=dayjs.tz(d,i)).format("HHmmss")}catch(t){console.log("Timezone error: "+i)}for(r=0;r<c;r++){if(parseInt(e[r].replace(/:/g,""))>=l){s=e[r];break}r==c-1&&(s=e[r])}n.attr("data-date",d.format("YYYY-MM-DD")+" "+s)}n.attr("data-countdown-bee",""),BEEThemeSP.Countdown()}}}),{init:o,ani:r}}(),h=function(){var t={},e={shopify:"shopify",external:"external"},i={productMediaWrapper:"[data-product-single-media-wrapper]"},n={enableVideoLooping:"enable-video-looping",enableVideoMuting:"enable-video-muting",enableVideoAutoplaying:"enable-video-autoplaying",videoId:"video-id"};function a(i){i?function(){for(var i in t)if(t.hasOwnProperty(i)){var n=t[i];if(n.nativeVideo)continue;n.host===e.shopify&&(n.element.setAttribute("controls","controls"),n.nativeVideo=!0)}}():o()}function o(){for(var e in t){if(t.hasOwnProperty(e))t[e].ready()}}return{init:function(o,r){if(o){var s=o.querySelector("iframe, video");if(s){var d=o.getAttribute("data-nt-media-id");t[d]={mediaId:d,sectionId:r,host:(l=s,"VIDEO"===l.tagName?e.shopify:e.external),container:o,element:s,ready:function(){!function(t){if(!t.player){var e=t.container.closest(i.productMediaWrapper),a="true"===e.getAttribute("data-"+n.enableVideoLooping),o="true"===e.getAttribute("data-"+n.enableVideoMuting),r="true"===e.getAttribute("data-"+n.enableVideoAutoplaying);t.player=new Shopify.Video(t.element,{loop:{active:a},volume:o?0:1,muted:o}),e.classList.add("is-media__initialized");var s=function(){t.player&&t.player.pause()};e.addEventListener("mediaHidden",s),e.addEventListener("xrLaunch",s),e.addEventListener("mediaVisible",function(){!BEEThemeSP.isTouch&&r&&t.player&&t.player.play()})}}(this)}},window.Shopify.loadFeatures([{name:"video-ui",version:"2.0",onLoad:a}]),BEEThemeSP.LibraryLoader.load("plyrShopifyStyles")}}var l},hosts:e,loadVideos:o,removeSectionVideos:function(e){for(var i in t)if(t.hasOwnProperty(i)){var n=t[i];n.sectionId===e&&(n.player&&n.player.destroy(),delete t[i])}}}}(),m=function(){var t={},e={},i={},n={mediaGroup:"[data-product-single-media-group]",xrButton:"[data-shopify-xr]"};function a(e){if(!e)if(window.ShopifyXR){for(var i in t)if(t.hasOwnProperty(i)){var n=t[i];if(n.loaded)continue;var o=document.querySelector("#ModelJson-"+i);window.ShopifyXR.addModels(JSON.parse(o.innerHTML)),n.loaded=!0}window.ShopifyXR.setupXRElements()}else document.addEventListener("shopify_xr_initialized",function(){a()})}function o(t){if(!t)for(var i in e)if(e.hasOwnProperty(i)){var n=e[i];n.modelViewerUi||(n.modelViewerUi=new Shopify.ModelViewerUI(n.element)),r(n)}}function r(t){var e=i[t.sectionId];t.container.classList.add("is-media__initialized"),t.container.addEventListener("mediaVisible",function(){e.element&&e.element.setAttribute("data-shopify-model3d-id",t.modelId),BEEThemeSP.isTouch||t.modelViewerUi.play()}),t.container.addEventListener("mediaHidden",function(){e.element&&e.element.setAttribute("data-shopify-model3d-id",e.defaultId),t.modelViewerUi.pause()}),t.container.addEventListener("xrLaunch",function(){t.modelViewerUi.pause()})}return{init:function(r,s){t[s]={loaded:!1},r.forEach(function(t,a){var o=t.getAttribute("data-nt-media-id"),r=t.querySelector("model-viewer"),d=r.getAttribute("data-model-id");if(0===a){var l=t.closest(n.mediaGroup).querySelector(n.xrButton);i[s]={element:l,defaultId:d}}e[o]={modelId:d,sectionId:s,container:t,element:r}}),window.Shopify.loadFeatures([{name:"shopify-xr",version:"1.0",onLoad:a},{name:"model-viewer-ui",version:"1.0",onLoad:o}]),BEEThemeSP.LibraryLoader.load("modelViewerUiStyles")},removeSectionModels:function(i){for(var n in e)e.hasOwnProperty(n)&&e[n].sectionId===i&&(e[n].modelViewerUi.destroy(),delete e[n]);delete t[i]}}}(),g=function(){var e=!1;function i(e,i){e=e[0];let n=JSON.parse(document.querySelector("#Json360-"+i).innerHTML),a=n.imgArray,o=parseFloat(e.getAttribute("data-min"))||1.194,r=parseFloat(e.getAttribute("data-max"))||2,s=window.devicePixelRatio<o?o:window.devicePixelRatio,d=s>r?r:s,l=Math.round(e.clientWidth*d);n.imgArray=[];for(let t in a)n.imgArray.push(`${a[t]}&width=${l}`);n.onReady=function(){!function(e,i){i.classList.add("is-media__initialized"),i.addEventListener("mediaVisible",function(){if(!BEEThemeSP.isTouch)try{e.play(),t(i.querySelector(".nav_bar_play")).removeClass("nav_bar_play").addClass("nav_bar_stop")}catch(t){}}),t(i).hasClass("is-selected")&&(e.play(),setTimeout(function(){t(i.querySelector(".nav_bar_play")).removeClass("nav_bar_play").addClass("nav_bar_stop")},50));i.addEventListener("mediaHidden",function(){e.stop(),t(i.querySelector(".nav_bar_stop")).removeClass("nav_bar_stop").addClass("nav_bar_play")})}(c,e)};var c=t(e.querySelector(".bee-threesixty")).ThreeSixty(n)}return{init:function(t,n){e?i(t,n):$script(BEEconfigs.script12b,function(){i(t,n),e=!0})}}}(),v=function(){var e="[data-pickup-availability-popup-open]",i="[data-pickup-availability-popup-close]",n=l.mfp_close,a=l.mfp_loading,r={};function s(t,e){this.container=e,this.idPopup=this.container.dataset.idPopup,this.hasOnlyDefaultVariant="true"===this.container.dataset.hasOnlyDefaultVariant,this.rootUrl=this.container.dataset.rootUrl,this.variantId=this.container.dataset.variantId;var i=this;t.on("pickupAvailability:update",function(t){i.updateContent(t.currentVariant.id)}),t.on("pickupAvailability:clear",function(t){i.clearContent()})}return s.prototype=Object.assign({},s.prototype,{updateContent:function(t=this.variantId){let i=this.rootUrl;i.endsWith("/")||(i+="/");var n=i+"variants/"+t+"/?section_id=pickup-availability",a=this,o=c+"pickup-availability"+t,s=a.container.querySelector(e);a.container.style.opacity=.5,s&&(s.disabled=!0,s.setAttribute("aria-busy",!0)),r[o]?a.updateResponse(r[o]):BEEThemeSP.getToFetchSection(null,"text",n).then(t=>{"NVT_94"!=t&&(r[o]=t,a.updateResponse(t))})},updateResponse:function(t){if(""!==t.trim()){this.container.innerHTML=t,this.container.innerHTML=this.container.firstElementChild.innerHTML,this.container.style.opacity=1;var i=this.container.querySelector(e);i&&(this.container.querySelector("#pickupAvailabilityPopup").id=this.idPopup,i.addEventListener("click",this._onClickModalOpen.bind(this)))}},clearContent:function(){this.container.innerHTML=""},_onClickModalOpen:function(){var e=this;t.magnificPopupBee.open({items:{src:`#${e.idPopup}`},type:"inline",removalDelay:500,tClose:n,tLoading:a,callbacks:{beforeOpen:function(){this.st.mainClass="mfp-move-horizontal bee-pickup-availability_pp_wrapper"},open:function(){o.trigger("NTpopupInline:offClose"),o.trigger("currency:update");var t=document.querySelector(`#${e.idPopup} ${i}`);t&&(t.removeEventListener("click",e._onClickModalClose),t.addEventListener("click",e._onClickModalClose))},beforeClose:function(){},close:function(){},afterClose:function(){o.trigger("NTpopupInline:onClose")}}})},_onClickModalClose:function(){t.magnificPopupBee.close()}}),s}(),y=function(){var n,r,s,d={isShow:"is--shown",isActive:"sticky-is--active",isHiddenMb:"is-hidden--mobile"},l="click.sticky",c=e.height(),u=i.height(),f=0,h=0,m="#bee-backToTop",g="is--show";function v(i,a,c){if(!i[0])return;var u,f=this,h="2"==a;r=i.offset().top+i.outerHeight(),f._updateContent(),s=n.find("[data-action-info-close]"),p.ani(n.find("[data-action-atc]")),f._stickyAddToCartToggle(),h||(n.addClass(d.isShow),o.addClass(d.isActive)),e.scroll(function(){u&&clearTimeout(u),u=setTimeout(function(){f._stickyAddToCartToggle(h)},30)});var v=n.find("[data-quantity-wrapper] [data-quantity-value]"),b=i.find("[data-quantity-value");v.change(function(){b.val(this.value)}),b.change(function(){v.val(this.value)}),c||n.find("[data-sticky-v-title]").on(l,function(e){e.preventDefault(),t("html, body").animate({scrollTop:i.offset().top-100},500),s.trigger(l)});let w=n.find(".bee-sticky-atc__product"),S=n.find("[data-action-atc]"),_=!0;n.find("[data-action-atc][data-action-delay]").on(l,function(i){!t(this)[0].hasAttribute("data-action-delay")||e.width()>767||(i.preventDefault(),i.stopPropagation(),_=!0,w.slideDown({start:function(){t(this).css({display:"flex"}),S.removeAttr("data-action-delay"),t(m).removeClass(g)},complete:function(){y(),t(m).addClass(g)}}))}),s.on(l,function(i){!_||e.width()>767||(i.preventDefault(),_=!1,w.slideUp({start:function(){S.attr("data-action-delay",""),t(m).removeClass(g)},complete:function(){y(),t(m).addClass(g)}}))}),e.on("resize.sticky",y)}function y(){h=n.outerHeight(),f!=h&&(f=h,a.css({"--stickyATC-height":n.outerHeight()+"px"}))}return v.prototype=Object.assign({},v.prototype,{_updateContent:function(){BEEThemeSP.$appendComponent.after(t("#bee-sticky-atc-temp").html()),n=t("[data-sticky-addtocart]")},_stickyAddToCartToggle:function(t){var a=e.scrollTop(),f=parseInt(a+c)+60;u=i.height(),r<a&&f!==u&&f<u?(n.addClass(d.isShow),o.addClass(d.isActive),y()):(f===u||f>u||r>a&&t)&&(n.removeClass(d.isShow),o.removeClass(d.isActive),n.find("[data-dropdown-open].is--clicked").click(),s.trigger(l))}}),v}();function b(e){var i=e.find(".bee-product__media-item:not(.is--media-hide)"),n=i.length;(4==n||n>5)&&(n="normal"),e.attr("data-media-sizes",n),e.find(".bee-product__media-item:not(.is--media-hide):last").addClass("is--media-last"),e.find(".bee-product__media-item").attr("data-index",""),i.each(function(e){t(this).attr("data-index",e)})}function w(t){return JSON.parse(t||"{}")}BEEThemeSP.Product=function(){var s="data-product-featured",l={},c={},u=!a.hasClass("is-remove-unavai-0"),S="is-pswp-disable",_=".bee-color-mode__variant_image .is--first-color",$="[data-main-media]",C="[data-pickup-availability-container]",k=BEEconfigs,T=BEEProductStrings,I=k.nowTimestamp,E=k.new_day_int,x=k.use_sale_badge,P=k.label_sale_style,B=k.use_preorder_badge,M=k.use_new_badge,O=k.use_soldout_badge,A=k.use_custom_badge,V={sale:T.badgeSale,new:T.badgeNew,preOrder:T.badgepreOrder,soldout:T.badgeSoldout,SavePercent:T.badgeSavePercent},L={texts:V,saleStyle:P},j=!1;function D(e){if(this.$container=t(e),this.$container.is("[data-product-options]"))this._itemQuickShopInline();else if(this.productConfigs=w(this.$container.attr(s)),this.productID=this.productConfigs.id,this.container=e,this.$mainMedia=this.$container.find($),this.mainMedia=this.$mainMedia[0],this.sectionId=this.productConfigs.sectionId,this.disableSwatch=this.productConfigs.disableSwatch,this.isSticky=this.productConfigs.isSticky,this.isStickyMB=this.productConfigs.isStickyMB,this.stickyShow=this.productConfigs.stickyShow,this.useStickySelect=this.productConfigs.useStickySelect,this.$addThis=this.$container.find("[data-bee-addthis]"),this.eventHandlers={},this._createBadgesProduct(),this._initBootSales(),this._initSubmit(),this.productConfigs.id){this.$variantImg=this.$container.find(_),this.disableVariantImage=!this.$variantImg[0],this.$formSelectorId=this.$container.find(this.productConfigs.formID),this.$formSelectorIdLength=this.$formSelectorId.length,this.pickupAvailabilityContainer=this.$container.find(C)[0],this.pickupAvailabilityContainer&&this.$formSelectorIdLength>0&&(this._initPickupAvailability(),this.disableSwatch&&this.pickupAvailability.updateContent()),this._initNotifyBackinStock(),r&&!this.isStickyMB&&(this.isSticky=!1),this.isSticky&&this._initStickyAddToCart(),!this.disableSwatch&&this.$formSelectorIdLength>0&&(this.$originalSelectorId=this.$formSelectorId.find('select[name="id"]'),this.$options1=this.$formSelectorId.find('[data-swatch-option][data-id="0"]'),this.$options2=this.$formSelectorId.find('[data-swatch-option][data-id="1"]'),this.$options3=this.$formSelectorId.find('[data-swatch-option][data-id="2"]'),this.PrOptionsSize,this.disableVariantImage||(this.$variantImgItems=this.$variantImg.find("[data-swatch-item]"),this.colorOptionIndex=this.$variantImg.data("id"),this.swatchWidth=2*this.$variantImgItems.outerWidth()),this._initVariants(),this._swatchesEventListeners(),this._changeMediaSlider(),this.disableVariantImage||this._updateVariantImageSwatchFirst());var i=this;if(i.mainMedia&&i._initProductIsotope(),setTimeout(function(){i.mainMedia&&(i._initLoadContent(),setTimeout(function(){i._initProductVideo(),i._initModelViewerLibraries(),i._initShopifyXrLaunch(),i._init360ViewerLibraries();var t=i.container.querySelector(".bee-product__info-container--sticky");t&&i.productConfigs.infoOverflowScroller&&(i.infoOverflowScroller=new BEEThemeSP.OverflowScroller(t,{offsetTop:109,offsetBottom:30,updateOffsetTop:!0}))},100))},1e3),i.$addThis[0])if(i.$addThis.is("[data-qv-addthis]")){if(d&&(j=!1),void 0!==window.addthis_new_tool)return window.addthis_new_tool(),void addthis.layers.refresh();j?z():$script("https://s7.addthis.com/js/300/addthis_widget.js#pubid=ra-56efaa05a768bd19",function(){j=!0,z()})}else j||$script("//s7.addthis.com/js/300/addthis_widget.js#pubid=ra-56efaa05a768bd19",function(){j=!0});var a=i.productConfigs.main_click;if("none"!=a&&i.mainMedia&&(BEEThemeSP.isTouch&&i.productConfigs.enable_zoom_click_mb||BEEThemeSP.isHover&&"pswp"==a)){var o=this.$mainMedia.find("."+S);o.removeClass(S),BEEThemeSP.isTouch&&i.productConfigs.enable_zoom_click_mb&&n>1024&&document.addEventListener("theme:hover",function(t){o.addClass(S)})}}}function z(){$script("https://s7.addthis.com/downloads/ajax_help.min.js",function(){const t=setInterval(function(){void 0!==window.addthis_new_tool&&(clearInterval(t),window.addthis_new_tool(),addthis.layers.refresh())},100)})}return D.prototype=Object.assign({},D.prototype,{_itemQuickShopInline:function(){this.$qsInline=this.$container.find("[data-qs-inl]"),this.$formSelectorId=this.$qsInline.find("form"),this.$originalSelectorId=this.$formSelectorId.find('select[name="id"]'),this.$options1=this.$formSelectorId.find('[data-swatch-option][data-id="0"]'),this.$options2=this.$formSelectorId.find('[data-swatch-option][data-id="1"]'),this.$options3=this.$formSelectorId.find('[data-swatch-option][data-id="2"]'),this.productConfigs=w(this.$originalSelectorId.attr("data-product-featured")),this.productID=this.productConfigs.id,this.$variantImg=this.$qsInline.find(_),this.disableVariantImage=!this.$variantImg[0],this.disableVariantImage||(this.$variantImgItems=this.$variantImg.find("[data-swatch-item]"),this.colorOptionIndex=this.$variantImg.data("id"),this.swatchWidth=2*this.$variantImgItems.outerWidth()),this._initVariants(),this._swatchesEventListeners(),this._initSubmit(),this.disableVariantImage||this._updateVariantImageSwatchFirst()},_initVariants:function(){var e,i,n,a=this.productConfigs;if(a.isGrouped&&(a.isGrouped=this.$container.find("form[data-groups-pr-form]").length>0),!a.isGrouped){if(l[this.productID])n=l[this.productID],i=c[this.productID],this.PrOptionsSize=i.length;else try{n=JSON.parse(this.$container.find(".pr_variants_json").html()),this.$originalSelectorId.find("> option").each(function(i){e=t(this),n[i].incoming=e.data("incoming"),n[i].next_incoming_date=e.data("nextincomingdate")||null,n[i].inventory_policy=e.data("inventorypolicy")||null,n[i].inventory_quantity=e.data("inventoryquantity")}),l[this.productID]=n,i=JSON.parse(this.$container.find(".pr_options_json").html()),c[this.productID]=i,this.PrOptionsSize=i.length}catch(t){return void console.log("not found pr json")}"boolean"!=typeof a.unQuickShopInline&&(a.unQuickShopInline=!0);var o={enableHistoryState:a.enableHistoryState||!1,$container:this.$container,formSelectorId:this.$formSelectorId,$originalSelectorId:this.$originalSelectorId,$options1:this.$options1,$options2:this.$options2,$options3:this.$options3,variants:n,productOptions:i,PrOptionsSize:this.PrOptionsSize,removeSoldout:a.removeSoldout,isNoPick:a.isNoPick,hasSoldoutUnavailable:a.hasSoldoutUnavailable,canMediaGroup:a.canMediaGroup,isMainProduct:a.isMainProduct,oldVariant:{},badgesConfigs:L,$variantImg:this.$variantImg,disableVariantImage:this.disableVariantImage,swatchWidth:this.swatchWidth,isSticky:this.isSticky,useStickySelect:this.useStickySelect,showFirstMedia:a.showFirstMedia,unQuickShopInline:a.unQuickShopInline,isQuickShopForm:a.isQuickShopForm};this.Variants=new f(o)}},_swatchesEventListeners:function(){if(this.PrOptionsSize){var e,i,n,a=!0,r=this.$formSelectorId.hasClass("is-form-beepritem"),s=this;s.$formSelectorId.on("click","[data-swatch-item]:not(.is--selected)",function(d){d.preventDefault();var l=t(this);if(l.addClass("is--selected").siblings().removeClass("is--selected"),l.closest("[data-swatch-option]").find("[data-current-value]").html(l.data("value")),r&&a&&(s.$formSelectorId.addClass("beeproduct-swatched"),s.$formSelectorId.find('[data-swatch-option][data-id="0"] [data-swatch-name],[data-swatch-option][data-id="1"],[data-swatch-option][data-id="2"]').show(150),a=!1),s.Variants.clickedCurrentValue=l.data("value")+"",n=l.closest("[data-swatch-option]").data("id"),s.Variants.clickedCurrentIndex="option"+ ++n,s.$formSelectorId.find(".is--selected").length<s.PrOptionsSize&&s.Variants.isNoPick)return s.Variants._updateMediaFilterNoPick(),void s.Variants._updateMediaFirst(l);s.Variants.eventClickedSwatch=!0,s.Variants.clickedOptions=[],s.$formSelectorId.find("[data-swatch-option] .is--selected").each(function(e,i){s.Variants.clickedOptions.push({value:t(i).data("value")+"",index:"option"+ ++e})}),s.Variants.isNoPick||(s.Variants.oldVariant=s.Variants.currentVariant),s.Variants.isNoPick&&(o.trigger("hide.bee.notices"),s.isSticky&&t("[data-sticky-addtocart]").removeAttr("hidden"),s.Variants.isNoPick=!1,s.$container.trigger("replace:btnAtc")),i=s.Variants._getVariantFromOptions(),s.$originalSelectorId.val(i.id),e=s.$originalSelectorId.val(),!u||null!==e&&""!==e||(i=s.Variants._getVariantFromSize(),s.$originalSelectorId.val(i.id),e=s.$originalSelectorId.val()),s.Variants.currentVariant=i,s.$originalSelectorId[0].dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0}))})}},_changeMediaSlider:function(){if(this.PrOptionsSize&&this.productConfigs.changeVariantByImg&&0!=this.$container.find(".flickitybee[data-main-media] .bee-product__media-item--variant").length){var e,i,n,a,o=this;this.$container.find(".flickitybee[data-main-media]").off("select.flickitybee").on("select.flickitybee",function(r,s){t(this).trigger("select.carousel"),(a=t(this).find(".flickitybee-slider>[data-main-slide]").eq(s)).hasClass("bee-product__media-item--variant")&&!o.Variants.eventClickedSwatch&&(e=a.data("media-id"),n=o.$originalSelectorId.val(),void 0===(i=o.$originalSelectorId.find('option[data-mdid="'+e+'"]:not(:disabled)').val())||n==i||o.Variants.isNoPick||o.Variants.mediaID==e||(o.$originalSelectorId.val(i),o.$originalSelectorId[0].dispatchEvent(new Event("change",{bubbles:!0,cancelable:!0}))))})}},_initBootSales:function(){this.BootSales=new p.init(this.$container)},_initSubmit:function(){i.trigger({type:"submitAtc:bee",$container:this.$container})},_initProductVideo:function(){var t=this.sectionId,e=this.mainMedia.querySelectorAll('[data-media-type="video"], [data-media-type="external_video"]');e.length<1||e.forEach(function(e){h.init(e,t)})},_init360ViewerLibraries:function(){var t=this.mainMedia.querySelectorAll('[data-media-type="360"]');t.length<1||g.init(t,this.sectionId)},_initModelViewerLibraries:function(){var t=this.mainMedia.querySelectorAll('[data-media-type="model"]');t.length<1||m.init(t,this.sectionId)},_initShopifyXrLaunch:function(){this.eventHandlers.initShopifyXrLaunchHandler=this._initShopifyXrLaunchHandler.bind(this),document.addEventListener("shopify_xr_launch",this.eventHandlers.initShopifyXrLaunchHandler)},_initShopifyXrLaunchHandler:function(){this.mainMedia.querySelector("[data-product-single-media-wrapper]").dispatchEvent(new CustomEvent("xrLaunch",{bubbles:!0,cancelable:!0}))},loadContent:function(t){if(t.getAttribute("loaded"))return;const e=document.createElement("div"),i=t.querySelector("template");e.appendChild(i.content.firstElementChild.cloneNode(!0)),t.setAttribute("loaded",!0);t.appendChild(e.querySelector("video, model-viewer, iframe"));i.remove()},_initLoadContent:function(){var t=this;t.mainMedia.querySelectorAll("[data-deferred-media]").forEach(function(e){e.classList.add("is--adding"),t.loadContent(e.querySelector(".bee-pr"))})},_initProductIsotope:function(){var t=this;!r&&t.productConfigs.hasIsotope&&(b(this.$mainMedia),BEEThemeSP.Isotopebee.init(this.$mainMedia),e.on("resize.prIstope",function(){e.width()<768&&t.$mainMedia.hasClass("isotopebee-enabled")?t.$mainMedia.isotopebee("destroy").removeClass("isotopebee-enabled"):e.width()>=768&&!t.$mainMedia.hasClass("isotopebee-enabled")&&setTimeout(function(){BEEThemeSP.Isotopebee.init(t.$mainMedia)},500)}))},_initPickupAvailability:function(){this.pickupAvailability=new v(this.$container,this.pickupAvailabilityContainer)},_initNotifyBackinStock:function(){let e=this,i=this.$container.find(".bee-product-notify-stock"),n=this.$container.find("[data-notify-stock-btn]");if(i[0]||n[0])if(i[0])this.$container.on("notifyBackinStock:show",function(n){let a=t("#ContactFormNotifyStock"+e.productID);i.show();let o=`${n.currentVariant.name.replace("- ","( ")} ) ${e.productConfigs.orgUrl}?variants=${n.currentVariant.id}`;a.find('[name="contact[product]"]').text(o)}),this.$container.on("notifyBackinStock:hide",function(t){i.hide()});else{var a=this.$container.find("[data-notify-stock-btn]"),o=a.data("root-url"),r="";if(o.endsWith("/")||(o+="/"),r=`${o}variants/${a.data("variant-id")}/?section_id=back-in-stock`,a.attr("data-mfp-src",r).hide().removeClass("bee-d-none"),!this.productConfigs.available&&this.productConfigs.disableSwatch)return void a.show();this.$container.on("notifyBackinStock:show",function(t){r=`${o}variants/${t.currentVariant.id}/?section_id=back-in-stock`,a.attr({"data-mfp-src":r,"data-storageid":`notify-stock${t.currentVariant.id}`}).show()}),this.$container.on("notifyBackinStock:hide",function(t){a.hide()})}},_createBadgesProduct:function(){let t=this.$container.find("[data-product-single-badge]"),e=(t.attr("data-sort")||"").replace(/ /g,"").split(","),i=this.productConfigs,n="";if(0==e.length||0==t.length)return;let a=e.length;for(let t=0;t<a;t++)switch(e[t]){case"sale":if(!x)break;let a=i.compare_at_price,c=i.price;if(a<=c){n+='<span data-badge-sale class="bee-badge-item bee-badge-sale" hidden></span>';break}if("2"==P)var o=100*(a-c)/a,r=V.SavePercent.replace("[sale]",Math.round(o));else if("3"==P){var s=a-c;r=BEEThemeSP.Currency.formatMoney(s)}else r=V[e[t]];n+='<span data-badge-sale class="bee-badge-item bee-badge-sale">'+r+"</span>";break;case"preOrder":if(!B)break;n+=`<span data-badge-preorder class="bee-badge-item bee-badge-preorder"${i.isPreoder?"":" hidden"}>${V[e[t]]}</span>`;break;case"new":var d=I-i.dateStart,l=Math.floor(d/3600);if((l=Math.floor(l/24))>=E||!M)break;n+='<span class="bee-badge-item bee-badge-new">'+V[e[t]]+"</span>";break;case"soldout":if(!O)break;n+=`<span data-badge-soldout class="bee-badge-item bee-badge-soldout"${i.available?" hidden":""}>${V[e[t]]}</span>`;break;default:let u=i.customBadge;if(!u||!A)break;let f=u.length;for(let t=0;t<f;t++)n+='<span class="bee-badge-item bee-badge-custom bee-badge-'+i.customBadgeHandle[t]+'">'+u[t]+"</span>"}t.html(n)},_updateVariantImageSwatchFirst:function(){let e=this,i=e.Variants.variants,n=i.length,a=this.colorOptionIndex;e.$variantImgItems.each(function(o){let r=t(this),s=function(t){for(let e=0;e<n;e++){let n=i[e];if(n.featured_image&&(n.options[a]+"").toLowerCase()==t)return n.featured_image}}((r.data("value")+"").toLowerCase());if(!s)return;let d=r.find("[data-img-el]");(r=d[0]?d:r).attr("data-bg",BEEThemeSP.Images.getNewImageUrl(s.src,e.swatchWidth))})},_initStickyAddToCart:function(){this.stickyAddToCart=new y(this.$formSelectorId,this.stickyShow,this.useStickySelect)}}),D}(),BEEThemeSP._initProducts=function(){var e="data-product-featured",i="initProducts__enabled";return function(){t("["+e+"]:not(."+i+")").each(function(){t(this).addClass(i),new BEEThemeSP.Product(this)})}}(),BEEThemeSP._initBundlePrs=function(){var e="data-product-bundles",i="initBundles__enabled",n="has--hover-pin",a="is--hover",o="is--trigger-hover";return function(){t("["+e+"]:not(."+i+")").each(function(){let e=t(this);e.addClass(i),p.ani(e.find("[data-atc-form]")),function(e){if(BEEThemeSP.isTouch||0==e.length)return;let i=e.find("[data-bundles-pr-form]"),r=e.find("[data-bundle-image]");e.hoverIntent({selector:"[data-bundle-pin]",sensitivity:6,interval:40,timeout:40,over:function(e){r.addClass(n),i.addClass(n),t(this).addClass(a),t(t(this).data("trigger")).addClass(o)},out:function(){r.removeClass(n),i.removeClass(n),r.find("."+a).removeClass(a),i.find("."+o).removeClass(o)}})}(e)})}}(),BEEThemeSP.Cookies=function(){return function(){var t;(t=navigator.cookieEnabled)||(document.cookie="testcookie",t=-1!==document.cookie.indexOf("testcookie")),t||a.addClass("not--cookies")}}(),BEEThemeSP.isVisible=function(i,n,a,o,r){if(!(i.length<1)){o=o||"both";var s=e,d=i.length>1?i.eq(0):i,l=null!=r,c=l?t(r):s,u=l?c.position():0,f=d.get(0),p=c.outerWidth(),h=c.outerHeight(),m=!0!==a||f.offsetWidth*f.offsetHeight;if("function"==typeof f.getBoundingClientRect){var g=f.getBoundingClientRect(),v=l?g.top-u.top>=0&&g.top<h+u.top:g.top>=0&&g.top<h,y=l?g.bottom-u.top>0&&g.bottom<=h+u.top:g.bottom>0&&g.bottom<=h,b=l?g.left-u.left>=0&&g.left<p+u.left:g.left>=0&&g.left<p,w=l?g.right-u.left>0&&g.right<p+u.left:g.right>0&&g.right<=p,S=n?v||y:v&&y,_=n?b||w:b&&w;S=g.top<0&&g.bottom>h||S,_=g.left<0&&g.right>p||_;if("both"===o)return m&&S&&_;if("vertical"===o)return m&&S;if("horizontal"===o)return m&&_}else{var $=l?0:u,C=$+h,k=c.scrollLeft(),T=k+p,I=d.position(),E=I.top,x=E+d.height(),P=I.left,B=P+d.width(),M=!0===n?x:E,O=!0===n?E:x,A=!0===n?B:P,V=!0===n?P:B;if("both"===o)return!!m&&O<=C&&M>=$&&V<=T&&A>=k;if("vertical"===o)return!!m&&O<=C&&M>=$;if("horizontal"===o)return!!m&&V<=T&&A>=k}}},BEEThemeSP.Tabs=function(){var e,i,n,a,o="bee-tabs-enabled",r="bee-tabs-simple-enabled",s="bee-tabs-accordion-enabled",d="bee-active",l=300,c=150;return{Default:function(){0!=(i=t(`[data-bee-tabs]:not(.${o})`)).length&&(i.addClass(o),i.on("click","[data-bee-tab-ul] [data-bee-tab-item]",function(i){i.preventDefault();var n=t(this),a=n.closest("[data-bee-tabs]"),o=n.attr("href")||n.data("id-tab"),r=a.find(o),s=r.find(".flickitybee"),l=r.find(".isotopebee");a.find("."+d).removeClass(d),a.find("[data-bee-tab-content]").hide(),n.addClass(d),r.show().addClass(d),r.closest("[data-bee-tab-wrapper]").addClass(d),clearTimeout(e),e=setTimeout(function(){s.hasClass("flickitybee-enabled")?s.flickitybee("resize"):l.hasClass("isotopebee-enabled")&&l.isotopebee("layout")},200)}))},Simple:function(){0!=(n=t(`[data-bee-tabs2]:not(.${r})`)).length&&(n.addClass(r),n.on("click","[data-bee-tab-ul2] [data-bee-tab-item]",function(e){e.preventDefault();var i=t(this),n=i.closest("[data-bee-tabs2]"),a=i.attr("href")||i.data("id-tab"),o=n.find(a);n.find("."+d).removeClass(d),i.addClass(d),o.addClass(d),o.closest("[data-bee-tab-wrapper]").addClass(d),o.closest("[data-bee-tabs2]").attr("data-tab-active",a.replace("#","")),i.is("[data-triger-btns-tab]")&&(o.hasClass("flickitybee flickitybee-enabled")?o.trigger("updateBtnTab.flickitybee"):o.find(".flickitybee.flickitybee-enabled").trigger("updateBtnTab.flickitybee"))}))},Accordion:function(){0!=(a=t(`[data-bee-tabs]:not(.${s})`)).length&&(a.addClass(s),t(".bee-type-accordion, [data-bee-accordion-pr]").find("."+d).find("[data-bee-tab-content]").css("display","block"),a.on("click","[data-bee-tab-wrapper] [data-bee-tab-item]",function(e){e.preventDefault();var i=t(this),n=i.closest("[data-bee-tabs]"),a=n.find("[data-bee-tab-ul]"),o=n.find("[data-bee-tab-wrapper]:not([data-no-auto-close])."+d),r=o.find("[data-bee-tab-content]"),s=i.closest("[data-bee-tab-wrapper]"),u=s.find("[data-bee-tab-content]"),f=i.closest(".bee-section"),p=u.find(".flickitybee"),h=u.find(".isotopebee");0==f.length&&(f=i.closest(".bee-section,.shopify-section")),s.hasClass(d)?(a.find("."+d).removeClass(d),s.removeClass(d),u.slideUp(l).removeClass(d)):(o.removeClass(d),a.find("."+d).removeClass(d),s.addClass(d),a.find(`a[href="${i.attr("href")}"], [data-href="${i.attr("href")}"]`).addClass(d),r.slideUp(c).removeClass(d),u.stop(!0,!0).slideDown(l,function(){if(p.hasClass("flickitybee-enabled")?p.flickitybee("resize"):h.hasClass("isotopebee-enabled")&&h.isotopebee("layout"),!BEEThemeSP.isVisible(i,!0)){var e=t(".bee-section-header").height()||0,n=f.find(".bee-tab-wrapper.bee-active").offset().top-e-10;t("body,html").animate({scrollTop:n})}}).addClass(d))}))}}}(),BEEThemeSP.RenderRefresh=function(){function e(t){o.trigger("currency:update");let e=t.find(".flickitybee"),i=t.find(".isotopebee");t.find(".bee-products").length>0&&"function"==typeof BEEThemeSP.reinitProductGridItem&&BEEThemeSP.reinitProductGridItem(),i.length>0&&BEEThemeSP.Isotopebee.init(i),e.length>0&&(e[0].flickitybee=new BEEThemeSP.Carousel(e[0])),BEEThemeSP.ProductItem.resizeObserver(),BEEThemeSP.initLoadMore&&BEEThemeSP.initLoadMore()}return function(){0!=t("[data-render-lazy-component]").length&&(t("[data-render-lazy-component].lazyloadbeeed").each(function(){e(t(this))}),t("[data-render-lazy-component]:not(.lazyloadbeeed)").one("lazyincluded",function(i){var n=t(i.target)[0];e(t(n))}),t("[data-render-lazy-component]:not(.lazyloadbee)").addClass("lazyloadbee"))}}(),BEEThemeSP.ParallaxInt=function(){var e=t("[data-parallax-beetrue]:not(.parallax_enabled)");0!=e.length&&e.each(function(){var e=t(this),i=e.attr("data-imgsl")||e.find(".bee-parallax-img:visible")[0]||".bee-parallax-img";(e.find(i).length>0||e.is(".bee-parallax-bg.lazyloadbeeed"))&&e.addClass("parallax_enabled").beeJarallax({speed:e.attr("data-speed")||.8,imgElement:i})})},BEEThemeSP.Countdown=function(){var e=BEEconfigs.timezone,i="bee_nt_guess";try{i=dayjs.tz.guess()}catch(t){}return function(){var n=t("[data-countdown-bee]:not(.bee-countdown-enabled)");0!=n.length&&n.each(function(){var n,a=t(this),o=t(a.attr("data-keyid")).html()||t.trim(a.html())||"%D days %H:%M:%S",r=a.is("[data-refresh-owl]"),s=a.data("loop"),d=a.data("date"),l=parseInt(a.data("dayl")),c=dayjs(),u=d.replace(/\//g,"").replace(/-/g,"")+"",f=parseInt(u),p=u.length<9?"YYYYMMDD":"YYYYMMDDHHmmss";if((f>parseInt(c.format(p))||l<1)&&(s=!1),s||"true"==s){var h=dayjs(d).format(" HH:mm:ss"),m=l-c.diff(d.replace(/\//g,"-"),"days")%l;d=(c=c.add(m,"day")).format("YYYY/MM/DD")+h,a.attr("data-dateloop",d)}a.countdown(function(t){if(void 0!==t){var n=t.replace("24:00:00","23:59:59");if("nobee"!=e&&i!=e)try{n=dayjs.tz(t.replace(/\//g,"-"),e).toDate()}catch(t){console.log("Timezone error: "+e)}else n=new Date(n);return n}}(d),{elapse:!0}).on("update.countdown",function(t){if(t.elapsed)a.html("").addClass("expired_cdbee").closest("[data-countdown-wrap]").html("").addClass("expired_cdbee");else{var e=24*t.offset.totalDays+t.offset.hours;a.html(t.strftime(o.replace("[totalHours]",e)))}}).addClass("bee-countdown-enabled").closest("[data-countdown-wrap]").addClass("bee-countdown-enabled"),r&&(clearTimeout(n),n=setTimeout(()=>{a.closest(".flickitybee-enabled").flickitybee("resize")},600))})}}();var S=function(){const e={from:0,to:0,speed:1e3,refreshInterval:100,decimals:0,formatter:function(t,e){return t.toFixed(e.decimals)},onUpdate:null,onComplete:null};function i(i,n){const a="object"==typeof n?n:{};this.$element=t(i),this.options=Object.assign({},e,this._dataOptions(),a),this._init()}return i.prototype=Object.assign({},i.prototype,{_init:function(){this.value=this.options.from,this.loops=Math.ceil(this.options.speed/this.options.refreshInterval),this.loopCount=0,this.increment=(this.options.to-this.options.from)/this.loops,this._start()},_dataOptions:function(){var t={from:this.$element.data("from"),to:this.$element.data("to"),speed:this.$element.data("speed"),refreshInterval:this.$element.data("refresh-interval"),decimals:this.$element.data("decimals")},e=Object.keys(t);for(var i in e){var n=e[i];void 0===t[n]&&delete t[n]}return t},_update:function(){this.value+=this.increment,this.loopCount++,this._render(),"function"==typeof this.options.onUpdate&&this.options.onUpdate.call(this.$element,this.value),this.loopCount>=this.loops&&(clearInterval(this.interval),this.value=this.options.to,"function"==typeof this.options.onComplete&&this.options.onComplete.call(this.$element,this.value))},_render:function(){var t=this.options.formatter.call(this.$element,this.value,this.options);this.$element.text(t)},_start:function(){this._stop(),this._render(),this.interval=setInterval(this._update.bind(this),this.options.refreshInterval)},_stop:function(){this.interval&&clearInterval(this.interval)}}),i}();BEEThemeSP.AnimateOnScroll=function(){var e=BEEconfigs.timeani||200,i=new IntersectionObserver(function(n,a){n.forEach(function(n){var a=n.target;n.isIntersecting&&!a.classList.contains("bee_animated")&&(setTimeout(function(){a.classList.add("bee_animated"),t(a).is("[data-count-to]")&&(this.countTo=new S(a))},e),i.unobserve(a))})});return function(){var e=t("[data-bee-animate]:not(.bee-animate-init)");0!=e.length&&window.IntersectionObserver&&e.each(function(e){i.observe(this),t(this).addClass("bee-animate-init")})}}(),BEEThemeSP.PopupMFP=function(){var e=t.fn.magnificPopupBee,i=l.mfp_close,n=l.mfp_loading,r=t("[data-open-mfp-inline]:not(.bee-mfp-enabled)"),s=t("[data-open-mfp-iframe]:not(.bee-mfp-enabled)"),d=t("[data-open-mfp-video]:not(.bee-mfp-enabled)"),c=t("[data-open-mfp-ajax]:not(.bee-mfp-enabled)"),u=t("[data-open-mfp]"),f="is-opening-mfp";return function(){void 0!==e&&(0!=r.length&&r.magnificPopupBee({type:"inline",removalDelay:500,tClose:i,tLoading:n,callbacks:{beforeOpen:function(){a.addClass(f),this.st.mainClass="mfp-move-horizontal bee-inline-popup-wrapper bee-rte "+t(this.st.el).data("id")||""},open:function(){o.trigger("NTpopupInline:offClose"),o.trigger("currency:update")},afterClose:function(){o.trigger("NTpopupInline:onClose"),a.removeClass(f)}}}).addClass("bee-mfp-enabled"),0!=(s=t("[data-open-mfp-iframe]:not(.bee-mfp-enabled)")).length&&s.magnificPopupBee({type:"iframe",tClose:i,tLoading:n,iframe:{markup:'<div class="mfp-iframe-scaler bee-pr bee-mfp-iframe"><div class="mfp-close"></div><iframe class="mfp-iframe" allow="autoplay; encrypted-media" frameborder="0" allowfullscreen></iframe></div>',patterns:{youtube:{index:"youtube.com/",id:"v=",src:"//www.youtube.com/embed/%id%?enablejsapi=1&autoplay=0&rel=0&playlist=%id%&loop=1"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%?autoplay=0&loop=1"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}},srcAction:"iframe_src"},callbacks:{beforeOpen:function(){a.addClass(f),this.st.mainClass="bee-iframe-popup-wrapper "+t(this.st.el).data("id")||""},change:function(){},open:function(){var e=t(this.st.el),i=t(".bee-mfp-iframe").find(".mfp-iframe"),n=i.attr("src");e.is("[data-autoplay-true]")&&(n=n.replace("autoplay=0","autoplay=1")),e.is("[data-loop-false]")&&(n=(n=n.split("&playlist=")[0]).replace("loop=1","loop=0")),i.attr("src",n)},close:function(){},afterClose:function(){a.removeClass(f)}}}).addClass("bee-mfp-enabled"),0!=(d=t("[data-open-mfp-video]:not(.bee-mfp-enabled)")).length&&d.on("click",function(e){e.preventDefault();var o,r=t(this),s=JSON.parse(r.attr("data-options")||"{}"),d=s.type,l=s.vid,c=s.autoplay,u=s.loop,p='<iframe src="src_bee" class="class_bee" title="" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>',h={html5:"html5",youtube:"youtube",vimeo:"vimeo"};switch(d){case h.html5:let e=s.id,i="";i=e&&t(e)[0]?t(e).html():'<video class="mfp-video" src="'+s.srcDefault+'" preload="auto" controls '+(c?"autoplay":"")+(c?" loop":"")+" playsinline></video>",o=`<div class="mfp-video-scaler bee-pr bee-mfp-video">${i.replace("<video",'<video  class="mfp-video"')}</div>`;break;case h.youtube:p=p.replace("src_bee","//www.youtube.com/embed/"+l+"?enablejsapi=1&showinfo=0&controls=1&modestbranding=1&autoplay="+ +c+"&rel=0"+(u?"&playlist="+l+"&loop=1":"")).replace("class_bee","js-youtube"),o='<div class="mfp-iframe-scaler bee-pr bee-mfp-iframe">'+p+"</div>";break;case h.vimeo:p=p.replace("src_bee","//player.vimeo.com/video/"+l+"?&portrait=0&byline=0&color="+s.accent_color+"&autoplay="+ +c+"&loop="+ +u).replace("class_bee","js-vimeo"),o='<div class="mfp-iframe-scaler bee-pr bee-mfp-iframe">'+p+"</div>"}t.magnificPopupBee.open({items:{src:o,type:"inline"},tClose:i,tLoading:n,callbacks:{beforeOpen:function(){a.addClass(f),this.st.mainClass="bee-video-popup-wrapper mfp-video-holder "+r.data("id")||""},open:function(){r.addClass("bee-mfp-enabled")},afterClose:function(){r.removeClass("bee-mfp-enabled"),a.removeClass(f)}}})}),0!=(c=t("[data-open-mfp-ajax]:not(.bee-mfp-enabled)")).length&&c.magnificPopupBee({type:"ajax",removalDelay:500,tClose:i,tLoading:'<div class="bee-loading-spin bee-spin-centered bee-spin-dark bee-spin-medium"></div>',callbacks:{parseAjax:function(e){var i=t(this.st.el),n=i.data("id")||"",a=i.data("class")||"",o=i.data("style")||"",r=e.data;e.data=`<div class="mfp-with-anim bee-mfp-popup bee-rte ${a}" id="${n}" style="${o}">${r.split("[beeplitlz]")[1]||r}</div>`},ajaxContentAdded:function(){},beforeOpen:function(){a.addClass(f),this.st.mainClass="mfp-move-horizontal bee-ajax-popup-wrapper"},open:function(){var e=t(this.st.el).data("custom"),i=t(this.st.el).data("phone"),n=t(".bee-ajax-popup-wrapper:not(.mfp-bg) .mfp-content");if(o.trigger("NTpopupInline:offClose"),o.trigger("currency:update"),0==i&&(n.find("#bee-ContactFormAsk__phone").remove(),setTimeout(function(){n.find("#bee-ContactFormAsk__phone").remove()},400)),e){var a=e.split("||");t.each(a,function(t,e){var i=e.split("=>");n.find(i[0]).html(i[1])}),setTimeout(function(){t.each(a,function(t,e){var i=e.split("=>");n.find(i[0]).html(i[1])})},400)}},afterClose:function(){o.trigger("NTpopupInline:onClose"),a.removeClass(f)}}}).addClass("bee-mfp-enabled"),0!=u.length&&o.on("click","[data-open-mfp]",function(e){e.preventDefault();var i=t(e.currentTarget),n=(t("html"),i.data()),o=n.opennt,r=n.color,s=n.bg,d=n.pos,l=n.ani||"has_ntcanvas",c=n.remove,u=n.class,p=n.close||!1,h=n.focuslast||!1,m=i.attr("data-focus"),g=window.pageYOffset;window.height,t("#shopify-section-header_banner").outerHeight(),t(".ntheader_wrapper").outerHeight(),i.addClass("current_clicked"),t.magnificPopupBee.open({items:{src:o,type:"inline",tLoading:'<div class="loading-spin dark"></div>'},tClose:nt_settings.close,removalDelay:300,closeBtnInside:p,focus:m,autoFocusLast:h,callbacks:{beforeOpen:function(){this.st.mainClass=l+" "+r+" "+l+"_"+d,a.addClass(f)},open:function(){a.addClass(l),a.addClass(l+"_"+d),u&&t(".mfp-content").addClass(u),s&&t(".mfp-bg").addClass(s),body.on("click",".close_pp",function(e){e.preventDefault(),t.magnificPopup.close()}),g&&t("html, body").scrollTop(g)},beforeClose:function(){a.removeClass(l)},afterClose:function(){a.removeClass(l+"_"+d),t(".current_clicked").removeClass("current_clicked"),c&&t(o).removeClass("mfp-hide"),a.removeClass(f)}}})}))}}(),BEEThemeSP.NTpopupInline=function(){var e,i=t("#bee_temp_modal").html(),n="modalbee:trigger",r="modalbee:opened",s="modalbee:closed",d="modalbee:destroy",l="bee-modal--is-active",c="bee-modal-opened",u="click.bee_qv",f="keyup.bee_qv",p="transitionend webkitTransitionEnd oTransitionEnd";function h(){return t("html").hasClass(c)}function m(t){27===t.keyCode&&o.trigger(s)}function g(e){t(e.target).parents().is(".bee-modal__inner")||t(e.target).parents().is(".mfp-ready")||(e.preventDefault(),o.trigger(s))}return function(v,y,b,w=null){e=w,o.off(n).on(n,function(t){h()?o.trigger(s):o.trigger(r)}),o.off(r).on(r,function(n){h()||(function(n){BEEThemeSP.$appendComponent.after(i),e&&a.addClass(e),t(".bee-modal__content").html(n)}(v),function(e){a.addClass(c),t(".bee-modal").addClass(l),t(".bee-modal").on(p,function(){t(this).focus().off(p)}),setTimeout(function(){t(".bee-modal").focus()},500),e()}(b))}),o.off(s).on(s,function(i){h()&&(t(".bee-modal .flickitybee-enabled").trigger("destroy.bee"),t("html").removeClass(c).addClass("bee-modal-closing"),t(".bee-modal").removeClass(l).addClass("bee-modal--is-closing"),setTimeout(function(){t(".bee-modal").remove(),t("html").removeClass("bee-modal-closing"),e&&a.removeClass(e),BEEThemeSP.isEditCartReplace=!1},500),o.trigger(d),o.trigger("bee:hideTooltip"))}),o.off(d).on(d,function(t){o.off(n).off(r).off(s).off(d).off(u).off(f)}),o.on(u,"[data-bee-modal-close]",function(t){t.preventDefault(),o.trigger(s)}),o.on(u,g),o.on(f,m),o.on("NTpopupInline:offClose",function(t){o.off(f,m),o.off(u,g)}),o.on("NTpopupInline:onClose",function(t){o.on(f,m),o.on(u,g)})}}(),BEEThemeSP.Currency=function(){var t="${{amount}}",e=BEEThemeSP.settings&&BEEThemeSP.settings.superScriptPrice,i=window.BEEconfigs.moneyFormat,n=function(t,e){return null==t||t!=t?e:t};return{formatMoney:function(a,o){o||(o=i),"string"==typeof a&&(a=a.replace(".",""));var r="",s=/\{\{\s*(\w+)\s*\}\}/,d=o||t;function l(t,e,i,a){if(e=n(e,2),i=n(i,","),a=n(a,"."),isNaN(t)||null==t)return 0;var o=(t=(t/100).toFixed(e)).split(".");return o[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g,"$1"+i)+(o[1]?a+o[1]:"")}switch(d.match(s)[1]){case"amount":r=l(a,2),e&&r&&r.includes(".")&&(r=r.replace(".","<sup>")+"</sup>");break;case"amount_no_decimals":r=l(a,0);break;case"amount_with_comma_separator":r=l(a,2,".",","),e&&r&&r.includes(".")&&(r=r.replace(",","<sup>")+"</sup>");break;case"amount_no_decimals_with_comma_separator":r=l(a,0,".",",");break;case"amount_no_decimals_with_space_separator":r=l(a,0," ")}return d.replace(s,r)},getBaseUnit:function(t){if(t&&t.unit_price_measurement&&t.unit_price_measurement.reference_value)return 1===t.unit_price_measurement.reference_value?t.unit_price_measurement.reference_unit:t.unit_price_measurement.reference_value+t.unit_price_measurement.reference_unit}}}(),BEEThemeSP.slate={};var _=BEEThemeSP.slate;_.utils={getParameterByName:function(t,e){e||(e=window.location.href),t=t.replace(/[[\]]/g,"\\$&");var i=new RegExp("[?&]"+t+"(=([^&#]*)|&|#|$)").exec(e);return i?i[2]?decodeURIComponent(i[2].replace(/\+/g," ")):"":null},removeParameterByName:function(t,e){e||(e=window.location.href),t=t.replace(/[[\]]/g,"\\$&");var i=e.split("?")[0],n=[],a=-1!==e.indexOf("?")?e.split("?")[1]:"";if(""!==a){for(var o=(n=a.split("&")).length-1;o>=0;o-=1)n[o].split("=")[0]===t&&n.splice(o,1);n.length&&(i=i+"?"+n.join("&"))}return i},resizeSelects:function(e){e.each(function(){var e=t(this),i=e.find("option:selected").text(),n=t("<span>").html(i);n.appendTo("body");var a=n.width();n.remove(),e.width(a+10)})},keyboardKeys:{TAB:9,ENTER:13,ESCAPE:27,LEFTARROW:37,RIGHTARROW:39}},_.rte={wrapTable:function(t){t.$tables.wrap('<div class="'+t.tableWrapperClass+'"></div>')},wrapIframe:function(e){e.$iframes.each(function(){t(this).wrap('<div class="'+e.iframeWrapperClass+'"></div>'),this.src=this.src})}},_.a11y={pageLinkFocus:function(t){var e="js-focus-hidden";t.first().attr("tabIndex","-1").focus().addClass(e).one("blur",function(){t.first().removeClass(e).removeAttr("tabindex")})},focusHash:function(){var e=window.location.hash;e&&document.getElementById(e.slice(1))&&this.pageLinkFocus(t(e))},bindInPageLinks:function(){t("a[href*=#]").on("click",function(e){this.pageLinkFocus(t(e.currentTarget.hash))}.bind(this))},trapFocus:function(e){var i={focusin:e.namespace?"focusin."+e.namespace:"focusin",focusout:e.namespace?"focusout."+e.namespace:"focusout",keydown:e.namespace?"keydown."+e.namespace:"keydown.handleFocus"},n=e.$container.find(t('button, [href], input, select, textarea, [tabindex]:not([tabindex^="-"])').filter(":visible")),a=n[0],o=n[n.length-1];e.$elementToFocus||(e.$elementToFocus=e.$container),e.$container.attr("tabindex","-1"),e.$elementToFocus.focus(),t(document).off("focusin"),t(document).on(i.focusout,function(){t(document).off(i.keydown)}),t(document).on(i.focusin,function(e){e.target!==o&&e.target!==a||t(document).on(i.keydown,function(t){!function(t){t.keyCode===_.utils.keyboardKeys.TAB&&(t.target!==o||t.shiftKey||(t.preventDefault(),a.focus()),t.target===a&&t.shiftKey&&(t.preventDefault(),o.focus()))}(t)})})},removeTrapFocus:function(e){var i=e.namespace?"focusin."+e.namespace:"focusin";e.$container&&e.$container.length&&e.$container.removeAttr("tabindex"),t(document).off(i)},accessibleLinks:function(e){var i=document.querySelector("body"),n={newWindow:"a11y-new-window-message",external:"a11y-external-message",newWindowExternal:"a11y-new-window-external-message"};void 0!==e.$links&&e.$links.jquery||(e.$links=t("a[href]:not([aria-describedby])")),t.each(e.$links,function(){var e=t(this),i=e.attr("target"),a=e.attr("rel"),o=function(t){var e=window.location.hostname;return t[0].hostname!==e}(e),r="_blank"===i;o&&e.attr("aria-describedby",n.external),r&&(void 0!==a&&-1!==a.indexOf("noopener")||e.attr("rel",function(t,e){return(void 0===e?"":e+" ")+"noopener"}),e.attr("aria-describedby",n.newWindow)),o&&r&&e.attr("aria-describedby",n.newWindowExternal)}),function(e){"object"!=typeof e&&(e={});var a=t.extend({newWindow:"Opens in a new window.",external:"Opens external website.",newWindowExternal:"Opens external website in a new window."},e),o=document.createElement("ul"),r="";for(var s in a)r+="<li id="+n[s]+">"+a[s]+"</li>";o.setAttribute("hidden",!0),o.innerHTML=r,i.appendChild(o)}(e.messages)}},BEEThemeSP.LinkMyltiLang=function(){var e=BEEThemeSP.root_url,i=window.location.hostname,n=BEEroutes.root_url,a="is--checked-link",o=i+n;return function(){"/"!=e&&t(`a[href*="${i}"]:not(.${a})`).each(function(){let r=t(this),s=r.attr("href");r.addClass(a),s.indexOf(i+e)>=0&&"/"!=s||s.indexOf("preview_theme_id=")>-1||("/"!=s?r.attr("href",s.replace(i,o)):r.attr("href",n))})}}();var $,C=function(){function e(e){var i=this;if(i.$slider=t(e),i.slideWrap=i.$slider.closest("[data-slide-wrap]")[0]||e,!i.slideWrap)return;if(i.sliderOptions=JSON.parse(i.$slider.attr("data-options")||"{}"),i.slider=e,i.sliderItems=e.querySelectorAll(".bee-slider__slide"),i.pageCount=i.slideWrap.querySelector(".bee-slider-counter--current"),i.pageTotal=i.slideWrap.querySelector(".bee-slider-counter--total"),i.prevButton=i.slideWrap.querySelector(".bee-slider__slide-prev"),i.nextButton=i.slideWrap.querySelector(".bee-slider__slide-next"),!i.slider||!i.nextButton)return;new ResizeObserver(t=>i._initPages()).observe(i.slider),i.slider.addEventListener("scroll",i._update.bind(i)),i.prevButton.addEventListener("click",i._onButtonClick.bind(i)),i.nextButton.addEventListener("click",i._onButtonClick.bind(i))}return e.prototype=Object.assign({},e.prototype,{_initPages:function(){this.slider.classList.remove("is--active"),0!==!this.sliderItems.length&&(this.slider.classList.add("is--active"),this.slidesPerPage=Math.floor(this.slider.clientWidth/this.sliderItems[0].clientWidth),this.totalPages=this.sliderItems.length-this.slidesPerPage+1,this._update())},_update:function(){this.pageCount&&this.pageTotal&&(this.currentPage=Math.round(this.slider.scrollLeft/this.sliderItems[0].clientWidth)+1,1===this.currentPage?this.prevButton.setAttribute("disabled",!0):this.prevButton.removeAttribute("disabled"),this.currentPage===this.totalPages?this.nextButton.setAttribute("disabled",!0):this.nextButton.removeAttribute("disabled"),this.pageCount.textContent=this.currentPage,this.pageTotal.textContent=this.totalPages)},_onButtonClick:function(t){t.preventDefault();const e="next"===t.currentTarget.name?this.slider.scrollLeft+this.sliderItems[0].clientWidth:this.slider.scrollLeft-this.sliderItems[0].clientWidth;this.slider.scrollTo({left:e})}}),e}();BEEThemeSP.SliderComponentInt=void(0!=($=t(".bee-slider:not(.bee-enabled)")).length&&$.each(function(t){this.classList.add("bee-enabled"),this.sliderComponent=new C(this)})),BEEThemeSP.LibraryLoader=function(){var t={link:"link",script:"script"},e={requested:"requested",loaded:"loaded"},n="https://cdn.shopify.com/shopifycloud/",a={youtubeSdk:{tagId:"youtube-sdk",src:"https://www.youtube.com/iframe_api",type:t.script},vimeoSdk:{tagId:"vimeo-sdk",src:"https://player.vimeo.com/api/player.js",type:t.script},plyrShopifyStyles:{tagId:"plyr-shopify-styles",src:n+"plyr/v2.0/shopify-plyr.css",type:t.link},modelViewerUiStyles:{tagId:"shopify-model-viewer-ui-styles",src:n+"model-viewer-ui/assets/v1.0/model-viewer-ui.css",type:t.link}};return{load:function(n,o){var r=a[n];if(r){if(d&&"youtubeSdk"==n&&window.YT)return o(),void i.trigger("youtube:ready");if(r.status!==e.requested)if(o=o||function(){},r.status!==e.loaded){var s;switch(r.status=e.requested,r.type){case t.script:s=function(t,i){var n=document.createElement("script");return n.src=t.src,n.addEventListener("load",function(){t.status=e.loaded,i()}),n}(r,o);break;case t.link:s=function(t,i){var n=document.createElement("link");return n.href=t.src,n.rel="stylesheet",n.type="text/css",n.addEventListener("load",function(){t.status=e.loaded,i()}),n}(r,o)}s.id=r.tagId,r.element=s;var l=document.getElementsByTagName(r.type)[0];l.parentNode.insertBefore(s,l)}else o()}}}}();var k,T=function(){var n={html5:"html5",youtube:"youtube",vimeo:"vimeo"},a=!1,o=!1;function r(e){switch(this.$video=t(e),this.video_options=JSON.parse(this.$video.attr("data-options")||"{}"),this.video_type=this.video_options.type,this.video_mute=this.video_options.mute,this.$videoInsert=this.$video.find("[data-bgvideo-insert]"),this.$elementToInsert=this.$videoInsert.length?this.$videoInsert:this.$video,this.elementToInsert=this.$elementToInsert[0],this.$video.attr("loaded",!0),this.video_type){case n.html5:this._setupBgHtml5Video();break;case n.youtube:window.YT?this._setupBgYouTubeVideo():(this._triggerBgYouTubeVideo(),a||(BEEThemeSP.LibraryLoader.load("youtubeSdk"),a=!0));break;case n.vimeo:window.Vimeo?this._setupBgVimeoVideo():(this._triggerBgVimeoVideo(),o||(BEEThemeSP.LibraryLoader.load("vimeoSdk",this._loadedVimeoSDK.bind(this)),o=!0))}}return r.prototype=Object.assign({},r.prototype,{_triggerBgYouTubeVideo:function(){var t=this;i.on("youtube:ready",function(e){t._setupBgYouTubeVideo()})},_loadedVimeoSDK:function(){i.trigger("vimeo:ready")},_triggerBgVimeoVideo:function(){var t=this;i.on("vimeo:ready",function(e){t._setupBgVimeoVideo()})},_setupBgHtml5Video:function(){var e=this,i=e.video_options.id;let n=i&&t(i)[0]?t(i).html():'<video class="bee_bg_vid_html5" src="'+e.video_options.srcDefault+'" preload="auto" playsinline autoplay '+(e.video_mute?"muted ":" ")+"loop></video>";e.$elementToInsert.replaceWith(n),e.$video.find(".bee_bg_vid_html5").on("playing",function(t){e.$video.addClass("bee-bgvideo-playing")})},_setupBgYouTubeVideo:function(){if(window.YT){var t=this;t.player=new YT.Player(t.elementToInsert,{videoId:t.video_options.vid,playerVars:{iv_load_policy:3,enablejsapi:1,disablekb:1,autoplay:0,controls:0,rel:0,loop:0,playsinline:1,modestbranding:1,autohide:1,branding:0,cc_load_policy:0,fs:0,quality:"hd1080",wmode:"transparent",height:"100%",width:"100%",origin:t.video_options.requestHost},events:{onReady:t.onPlayerReady.bind(this),onStateChange:t.onPlayerStateChange.bind(this)}}),t.resizeVideoBackground(),e.on("resize",BEEThemeSP.debounce(300,function(){t.resizeVideoBackground()}.bind(t)))}},onPlayerReady:function(t){this.video_mute&&this.player.mute(),this.player.playVideo()},onPlayerStateChange:function(t){t.data===YT.PlayerState.PLAYING?this.$video.addClass("bee-bgvideo-playing"):t.data===YT.PlayerState.ENDED&&this.player.playVideo()},_setupBgVimeoVideo:function(){if(window.Vimeo){var t=this;t.player=new Vimeo.Player(t.elementToInsert.parentNode,{id:t.video_options.vid,autoplay:!0,autopause:!1,muted:!0,background:!0,loop:t.video_mute}),t.$videoInsert.remove(),t.resizeVideoBackground(),e.on("resize",BEEThemeSP.debounce(300,function(){t.resizeVideoBackground()}.bind(t))),t.player.on("play",function(){t.$video.addClass("bee-bgvideo-playing")}),t.player.on("ended",function(){})}},resizeVideoBackground:function(){var t,e,i,n,a=this.$video,o=a.innerWidth(),r=a.innerHeight();o/r<16/9?(t=r*(16/9),e=r,i=-Math.round((t-o)/2)+"px",n=-Math.round((e-r)/2)+"px"):(e=(t=o)*(9/16),n=-Math.round((e-r)/2)+"px",i=-Math.round((t-o)/2)+"px"),t+="px",e+="px",a.find("iframe").css({maxWidth:"1000%",marginLeft:i,marginTop:n,width:t,height:e})}}),r}();BEEThemeSP.BgVideo=function(){var e=t('[data-video-background]:not([loaded="true"])');0!=e.length&&e.each(function(t){this.bgVideo=new T(this)})},BEEThemeSP.Footer=function(){var i={opened:"is--footer_opened",btnClose:".bee-announcement-bar__close"},n=200;function a(){t("[data-footer-open]").off("click").on("click",function(){var e=t(this).parent(),a=e.find("> [data-footer-content]");e.hasClass(i.opened)?(e.removeClass(i.opened),a.stop().slideUp(n)):(e.addClass(i.opened),a.stop().slideDown(n))}),e.off("resize.FooterCollapse")}return function(){e.on("resize.FooterCollapse",a),e.width()<768&&t(".is--footer-collapse-true").length>0&&a()}}(),BEEThemeSP.Notices=function(){var e,i,n,r,s,d=t("#bee-notices__tmp"),l=d.html(),c="is--show",u="is--active-notices",f="click.notices",p="hide.bee.notices",h=!1,m=200,g=BEEconfigs.autoHideNotices,v=BEEconfigs.timeOutNotices;function y(){n.hide(),clearTimeout(s),e.removeClass(c),a.removeClass(u),e.off(f).off(p)}return d.remove(),function(d,b="warning"){h||(BEEThemeSP.$appendComponent.after(l),e=t("#bee-notices__wrapper"),i=e.find(".bee-notices__mess"),n=e.find(".bee-notices__progressbar"),r=n.show().find(">span"),h=!0,l=null,r.css("animation-duration",`${v}ms`)),e.attr("data-notices-status",b),i.html(d),g?n.show():n.hide(),setTimeout(function(){e.addClass(c),a.addClass(u)},m),g&&(s=setTimeout(function(){y()},v+m)),e.on(f,function(){y()}),o.on(p,function(){y()})}}(),BEEThemeSP.FormShopifyMessSuccess=function(){t(document).on("submit",'form[action^="/contact"]',function(e){localStorage.setItem("bee-recentform",t(this).attr("id"))});let e=location.href,i=localStorage.getItem("bee-recentform")||"";e.indexOf("contact_posted=true")<0&&""!==i||(e.indexOf("contact_posted=true#ContactFormNotifyStock")>-1||i.includes("ContactFormNotifyStock")?BEEThemeSP.Notices(l.frm_notify_stock_success,"success"):e.indexOf("contact_posted=true#ContactFormAsk")>-1||i.includes("ContactFormAsk")?BEEThemeSP.Notices(l.frm_contact_ask_success,"success"):(e.indexOf("contact_posted=true#beeNewsletterFormPopup")>-1||i.includes("beeNewsletterFormPopup"))&&BEEThemeSP.Notices(l.frm_newsletter_popup_success,"success"))},BEEThemeSP.PreloadStylePopup=function(){var e=t("#bee-style-popup");0!=e.length&&t("#bee-assets-pre").html(e.html())},BEEThemeSP.BtnMore=function(){var e={enabled:"is--enabled",btn:"[data-btn-toogle]",open:"is--open"};function i(i){this.el=i,this.$el=t(i),this.clickHandler(),this.$el.addClass(e.enabled),this.selector=this.$el.data("slector"),this.tMore=this.$el.data("tmore"),this.tLess=this.$el.data("tless"),this.hasIsotope=this.$el.hasClass("isotopebee")}return i.prototype=Object.assign({},i.prototype,{clickHandler:function(){var i=this;i.$el.on("click.more",e.btn,function(n){n.preventDefault();let a=t(this);a.parent().find(i.selector).slideToggle(200),a.toggleClass(e.open),a.hasClass(e.open)?a.html(i.tLess):a.html(i.tMore),i.hasIsotope&&(i.$el.isotopebee("layout"),setTimeout(function(){i.$el.isotopebee("layout")},219))})}}),i}(),BEEThemeSP.initBtnMore=void(0!=(k=t("[data-wrap-toogle]:not(.is--enabled)")).length&&k.each(function(){this.btnMore=new BEEThemeSP.BtnMore(this)})),BEEThemeSP.fixHand=function(){var t=navigator.userAgent.indexOf("Windows Phone")>=0;!/iP(ad|hone|od)/.test(navigator.userAgent)||t||n>1199||$script(BEEconfigs.script12c,function(){BEEThemeSP.FastClick.attach(document.body),BEEconfigs.disFlashyApp||function(){const t=document.querySelector("body");new MutationObserver(function(){let t=document.querySelector("flashy-popup");t&&!t.classList.contains("needsclick")&&t.classList.add("needsclick")}).observe(t,{attributes:!0,childList:!0,subtree:!0})}()})}}(jQuery_BENT),jQuery_BENT(document).ready(function(t){smoothscroll.polyfill(),BEEThemeSP.FormShopifyMessSuccess(),BEEThemeSP.BgVideo(),BEEThemeSP.ParallaxInt(),t(".bee-parallax-bg:not(.lazyloadbeeed)").one("lazyloaded",function(t){setTimeout(function(){BEEThemeSP.ParallaxInt()},100)}),BEEThemeSP.Countdown(),BEEThemeSP.AnimateOnScroll(),BEEThemeSP._initProducts(),BEEThemeSP.slate.a11y.accessibleLinks({$links:t("a[href]:not([aria-describedby])")}),BEEThemeSP.Tabs.Default(),BEEThemeSP.Tabs.Simple(),BEEThemeSP.Tabs.Accordion(),BEEThemeSP.Footer(),BEEThemeSP.PopupMFP(),BEEThemeSP.Cookies(),BEEThemeSP.fixHand(),$script([BEEconfigs.script3,BEEconfigs.script6]),setTimeout(function(){BEEThemeSP.LinkMyltiLang()},500),setTimeout(function(){BEEThemeSP.PreloadStylePopup()},1500)});