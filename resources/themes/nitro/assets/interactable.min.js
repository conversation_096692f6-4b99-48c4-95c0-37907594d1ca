!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIBeeCore={})}(this,function(e){"use strict";function t(e){return e.split("-")[0]}function n(e){return e.split("-")[1]}function i(e){return["top","bottom"].includes(t(e))?"x":"y"}function o(e){return"y"===e?"height":"width"}function a(e,a,r){let{reference:s,floating:l}=e;const c=s.x+s.width/2-l.width/2,d=s.y+s.height/2-l.height/2,u=i(a),p=o(u),m=s[p]/2-l[p]/2,f="x"===u;let h;switch(t(a)){case"top":h={x:c,y:s.y-l.height};break;case"bottom":h={x:c,y:s.y+s.height};break;case"right":h={x:s.x+s.width,y:d};break;case"left":h={x:s.x-l.width,y:d};break;default:h={x:s.x,y:s.y}}switch(n(a)){case"start":h[u]-=m*(r&&f?-1:1);break;case"end":h[u]+=m*(r&&f?-1:1)}return h}function r(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function s(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}async function l(e,t){var n;void 0===t&&(t={});const{x:i,y:o,platform:a,rects:l,elements:c,strategy:d}=e,{boundary:u="clippingAncestors",rootBoundary:p="viewport",elementContext:m="floating",altBoundary:f=!1,padding:h=0}=t,g=r(h),v=c[f?"floating"===m?"reference":"floating":m],b=s(await a.getClippingRect({element:null==(n=await(null==a.isElement?void 0:a.isElement(v)))||n?v:v.contextElement||await(null==a.getDocumentElement?void 0:a.getDocumentElement(c.floating)),boundary:u,rootBoundary:p})),y=s(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({rect:"floating"===m?{...l.floating,x:i,y:o}:l.reference,offsetParent:await(null==a.getOffsetParent?void 0:a.getOffsetParent(c.floating)),strategy:d}):l[m]);return{top:b.top-y.top+g.top,bottom:y.bottom-b.bottom+g.bottom,left:b.left-y.left+g.left,right:y.right-b.right+g.right}}const c=Math.min,d=Math.max;function u(e,t,n){return d(e,c(t,n))}const p={left:"right",right:"left",bottom:"top",top:"bottom"};function m(e){return e.replace(/left|right|bottom|top/g,e=>p[e])}function f(e,t,a){void 0===a&&(a=!1);const r=n(e),s=i(e),l=o(s);let c="x"===s?r===(a?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(c=m(c)),{main:c,cross:m(c)}}const h={start:"end",end:"start"};function g(e){return e.replace(/start|end/g,e=>h[e])}const v=["top","right","bottom","left"],b=v.reduce((e,t)=>e.concat(t,t+"-start",t+"-end"),[]);function y(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function E(e){return v.some(t=>e[t]>=0)}function w(e){return"x"===e?"y":"x"}e.arrow=(e=>({name:"arrow",options:e,async fn(t){const{element:n,padding:a=0}=null!=e?e:{},{x:s,y:l,placement:c,rects:d,platform:p}=t;if(null==n)return{};const m=r(a),f={x:s,y:l},h=i(c),g=o(h),v=await p.getDimensions(n),b="y"===h?"top":"left",y="y"===h?"bottom":"right",E=d.reference[g]+d.reference[h]-f[h]-d.floating[g],w=f[h]-d.reference[h],T=await(null==p.getOffsetParent?void 0:p.getOffsetParent(n)),S=T?"y"===h?T.clientHeight||0:T.clientWidth||0:0,x=E/2-w/2,C=m[b],_=S-v[g]-m[y],P=S/2-v[g]/2+x,B=u(C,P,_);return{data:{[h]:B,centerOffset:P-B}}}})),e.autoPlacement=function(e){return void 0===e&&(e={}),{name:"autoPlacement",options:e,async fn(i){var o,a,r,s,c;const{x:d,y:u,rects:p,middlewareData:m,placement:h,platform:v,elements:y}=i,{alignment:E=null,allowedPlacements:w=b,autoAlignment:T=!0,...S}=e,x=function(e,i,o){return(e?[...o.filter(t=>n(t)===e),...o.filter(t=>n(t)!==e)]:o.filter(e=>t(e)===e)).filter(t=>!e||n(t)===e||!!i&&g(t)!==t)}(E,T,w),C=await l(i,S),_=null!=(o=null==(a=m.autoPlacement)?void 0:a.index)?o:0,P=x[_],{main:B,cross:k}=f(P,p,await(null==v.isRTL?void 0:v.isRTL(y.floating)));if(h!==P)return{x:d,y:u,reset:{skip:!1,placement:x[0]}};const I=[C[t(P)],C[B],C[k]],D=[...null!=(r=null==(s=m.autoPlacement)?void 0:s.overflows)?r:[],{placement:P,overflows:I}],A=x[_+1];if(A)return{data:{index:_+1,overflows:D},reset:{skip:!1,placement:A}};const M=D.slice().sort((e,t)=>e.overflows[0]-t.overflows[0]),R=null==(c=M.find(e=>{let{overflows:t}=e;return t.every(e=>e<=0)}))?void 0:c.placement;return{reset:{placement:null!=R?R:M[0].placement}}}}},e.computePosition=(async(e,t,n)=>{const{placement:i="bottom",strategy:o="absolute",middleware:r=[],platform:s}=n,l=await(null==s.isRTL?void 0:s.isRTL(t));let c=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:u}=a(c,i,l),p=i,m={};const f=new Set;for(let n=0;n<r.length;n++){const{name:h,fn:g}=r[n];if(f.has(h))continue;const{x:v,y:b,data:y,reset:E}=await g({x:d,y:u,initialPlacement:i,placement:p,strategy:o,middlewareData:m,rects:c,platform:s,elements:{reference:e,floating:t}});d=null!=v?v:d,u=null!=b?b:u,m={...m,[h]:{...m[h],...y}},E&&("object"==typeof E&&(E.placement&&(p=E.placement),E.rects&&(c=!0===E.rects?await s.getElementRects({reference:e,floating:t,strategy:o}):E.rects),({x:d,y:u}=a(c,p,l)),!1!==E.skip&&f.add(h)),n=-1)}return{x:d,y:u,placement:p,strategy:o,middlewareData:m}}),e.detectOverflow=l,e.flip=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(n){var i;const{placement:o,middlewareData:a,rects:r,initialPlacement:s,platform:c,elements:d}=n,{mainAxis:u=!0,crossAxis:p=!0,fallbackPlacements:h,fallbackStrategy:v="bestFit",flipAlignment:b=!0,...y}=e,E=t(o),w=[s,...h||(E!==s&&b?function(e){const t=m(e);return[g(e),t,g(t)]}(s):[m(s)])],T=await l(n,y),S=[];let x=(null==(i=a.flip)?void 0:i.overflows)||[];if(u&&S.push(T[E]),p){const{main:e,cross:t}=f(o,r,await(null==c.isRTL?void 0:c.isRTL(d.floating)));S.push(T[e],T[t])}if(x=[...x,{placement:o,overflows:S}],!S.every(e=>e<=0)){var C,_;const e=(null!=(C=null==(_=a.flip)?void 0:_.index)?C:0)+1,t=w[e];if(t)return{data:{index:e,overflows:x},reset:{skip:!1,placement:t}};let n="bottom";switch(v){case"bestFit":{var P;const e=null==(P=x.slice().sort((e,t)=>e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)-t.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0))[0])?void 0:P.placement;e&&(n=e);break}case"initialPlacement":n=s}return{reset:{placement:n}}}return{}}}},e.hide=function(e){let{strategy:t="referenceHidden",...n}=void 0===e?{}:e;return{name:"hide",async fn(e){const{rects:i}=e;switch(t){case"referenceHidden":{const t=y(await l(e,{...n,elementContext:"reference"}),i.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:E(t)}}}case"escaped":{const t=y(await l(e,{...n,altBoundary:!0}),i.floating);return{data:{escapedOffsets:t,escaped:E(t)}}}default:return{}}}}},e.inline=function(e){return void 0===e&&(e={}),{name:"inline",options:e,async fn(n){var o;const{placement:a,elements:l,rects:u,platform:p,strategy:m}=n,{padding:f=2,x:h,y:g}=e,v=s(p.convertOffsetParentRelativeRectToViewportRelativeRect?await p.convertOffsetParentRelativeRectToViewportRelativeRect({rect:u.reference,offsetParent:await(null==p.getOffsetParent?void 0:p.getOffsetParent(l.floating)),strategy:m}):u.reference),b=null!=(o=await(null==p.getClientRects?void 0:p.getClientRects(l.reference)))?o:[],y=r(f);return{reset:{rects:await p.getElementRects({reference:{getBoundingClientRect:function(){var e;if(2===b.length&&b[0].left>b[1].right&&null!=h&&null!=g)return null!=(e=b.find(e=>h>e.left-y.left&&h<e.right+y.right&&g>e.top-y.top&&g<e.bottom+y.bottom))?e:v;if(b.length>=2){if("x"===i(a)){const e=b[0],n=b[b.length-1],i="top"===t(a),o=e.top,r=n.bottom,s=i?e.left:n.left,l=i?e.right:n.right;return{top:o,bottom:r,left:s,right:l,width:l-s,height:r-o,x:s,y:o}}const e="left"===t(a),n=d(...b.map(e=>e.right)),o=c(...b.map(e=>e.left)),r=b.filter(t=>e?t.left===o:t.right===n),s=r[0].top,l=r[r.length-1].bottom;return{top:s,bottom:l,left:o,right:n,width:n-o,height:l-s,x:o,y:s}}return v}},floating:l.floating,strategy:m})}}}}},e.limitShift=function(e){return void 0===e&&(e={}),{options:e,fn(n){const{x:o,y:a,placement:r,rects:s,middlewareData:l}=n,{offset:c=0,mainAxis:d=!0,crossAxis:u=!0}=e,p={x:o,y:a},m=i(r),f=w(m);let h=p[m],g=p[f];const v="function"==typeof c?c({...s,placement:r}):c,b="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(d){const e="y"===m?"height":"width",t=s.reference[m]-s.floating[e]+b.mainAxis,n=s.reference[m]+s.reference[e]-b.mainAxis;h<t?h=t:h>n&&(h=n)}if(u){var y,E,T,S;const e="y"===m?"width":"height",n=["top","left"].includes(t(r)),i=s.reference[f]-s.floating[e]+(n&&null!=(y=null==(E=l.offset)?void 0:E[f])?y:0)+(n?0:b.crossAxis),o=s.reference[f]+s.reference[e]+(n?0:null!=(T=null==(S=l.offset)?void 0:S[f])?T:0)-(n?b.crossAxis:0);g<i?g=i:g>o&&(g=o)}return{[m]:h,[f]:g}}}},e.offset=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(o){const{x:a,y:r,placement:s,rects:l,platform:c,elements:d}=o,u=function(e,o,a,r){void 0===r&&(r=!1);const s=t(e),l=n(e),c="x"===i(e),d=["left","top"].includes(s)?-1:1;let u=1;"end"===l&&(u=-1),r&&c&&(u*=-1);const p="function"==typeof a?a({...o,placement:e}):a,{mainAxis:m,crossAxis:f}="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};return c?{x:f*u,y:m*d}:{x:m*d,y:f*u}}(s,l,e,await(null==c.isRTL?void 0:c.isRTL(d.floating)));return{x:a+u.x,y:r+u.y,data:u}}}},e.rectToClientRect=s,e.shift=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(n){const{x:o,y:a,placement:r}=n,{mainAxis:s=!0,crossAxis:c=!1,limiter:d={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...p}=e,m={x:o,y:a},f=await l(n,p),h=i(t(r)),g=w(h);let v=m[h],b=m[g];if(s){const e="y"===h?"bottom":"right";v=u(v+f["y"===h?"top":"left"],v,v-f[e])}if(c){const e="y"===g?"bottom":"right";b=u(b+f["y"===g?"top":"left"],b,b-f[e])}const y=d.fn({...n,[h]:v,[g]:b});return{...y,data:{x:y.x-o,y:y.y-a}}}}},e.size=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(i){const{placement:o,rects:a,platform:r,elements:s}=i,{apply:c,...u}=e,p=await l(i,u),m=t(o),f=n(o);let h,g;"top"===m||"bottom"===m?(h=m,g=f===(await(null==r.isRTL?void 0:r.isRTL(s.floating))?"start":"end")?"left":"right"):(g=m,h="end"===f?"top":"bottom");const v=d(p.left,0),b=d(p.right,0),y=d(p.top,0),E=d(p.bottom,0),w={height:a.floating.height-(["left","right"].includes(o)?2*(0!==y||0!==E?y+E:d(p.top,p.bottom)):p[h]),width:a.floating.width-(["top","bottom"].includes(o)?2*(0!==v||0!==b?v+b:d(p.left,p.right)):p[g])};return null==c||c({...w,...a}),{reset:{rects:!0}}}}},Object.defineProperty(e,"__esModule",{value:!0})}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@floating-ui/core")):"function"==typeof define&&define.amd?define(["exports","@floating-ui/core"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIBeeDOM={},e.FloatingUIBeeCore)}(this,function(e,t){"use strict";function n(e){return"[object Window]"===(null==e?void 0:e.toString())}function i(e){if(null==e)return window;if(!n(e)){const t=e.ownerDocument;return t&&t.defaultView||window}return e}function o(e){return i(e).getComputedStyle(e)}function a(e){return n(e)?"":e?(e.nodeName||"").toLowerCase():""}function r(e){return e instanceof i(e).HTMLElement}function s(e){return e instanceof i(e).Element}function l(e){return e instanceof i(e).ShadowRoot||e instanceof ShadowRoot}function c(e){const{overflow:t,overflowX:n,overflowY:i}=o(e);return/auto|scroll|overlay|hidden/.test(t+i+n)}function d(e){return["table","td","th"].includes(a(e))}function u(e){const t=navigator.userAgent.toLowerCase().includes("firefox"),n=o(e);return"none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||["transform","perspective"].includes(n.willChange)||t&&"filter"===n.willChange||t&&!!n.filter&&"none"!==n.filter}const p=Math.min,m=Math.max,f=Math.round;function h(e,t){void 0===t&&(t=!1);const n=e.getBoundingClientRect();let i=1,o=1;return t&&r(e)&&(i=e.offsetWidth>0&&f(n.width)/e.offsetWidth||1,o=e.offsetHeight>0&&f(n.height)/e.offsetHeight||1),{width:n.width/i,height:n.height/o,top:n.top/o,right:n.right/i,bottom:n.bottom/o,left:n.left/i,x:n.left/i,y:n.top/o}}function g(e){return(t=e,(t instanceof i(t).Node?e.ownerDocument:e.document)||window.document).documentElement;var t}function v(e){return n(e)?{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}:{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function b(e){return h(g(e)).left+v(e).scrollLeft}function y(e){return"html"===a(e)?e:e.assignedSlot||e.parentNode||(l(e)?e.host:null)||g(e)}function E(e){return r(e)&&"fixed"!==getComputedStyle(e).position?e.offsetParent:null}function w(e){const t=i(e);let n=E(e);for(;n&&d(n)&&"static"===getComputedStyle(n).position;)n=E(n);return n&&("html"===a(n)||"body"===a(n)&&"static"===getComputedStyle(n).position&&!u(n))?t:n||function(e){let t=y(e);for(l(t)&&(t=t.host);r(t)&&!["html","body"].includes(a(t));){if(u(t))return t;t=t.parentNode}return null}(e)||t}function T(e){if(r(e))return{width:e.offsetWidth,height:e.offsetHeight};const t=h(e);return{width:t.width,height:t.height}}function S(e,t){var n;void 0===t&&(t=[]);const o=function e(t){return["html","body","#document"].includes(a(t))?t.ownerDocument.body:r(t)&&c(t)?t:e(y(t))}(e),s=o===(null==(n=e.ownerDocument)?void 0:n.body),l=i(o),d=s?[l].concat(l.visualViewport||[],c(o)?o:[]):o,u=t.concat(d);return s?u:u.concat(S(y(d)))}function x(e,n){return"viewport"===n?t.rectToClientRect(function(e){const t=i(e),n=g(e),o=t.visualViewport;let a=n.clientWidth,r=n.clientHeight,s=0,l=0;return o&&(a=o.width,r=o.height,Math.abs(t.innerWidth/o.scale-o.width)<.01&&(s=o.offsetLeft,l=o.offsetTop)),{width:a,height:r,x:s,y:l}}(e)):s(n)?function(e){const t=h(e),n=t.top+e.clientTop,i=t.left+e.clientLeft;return{top:n,left:i,x:i,y:n,right:i+e.clientWidth,bottom:n+e.clientHeight,width:e.clientWidth,height:e.clientHeight}}(n):t.rectToClientRect(function(e){var t;const n=g(e),i=v(e),a=null==(t=e.ownerDocument)?void 0:t.body,r=m(n.scrollWidth,n.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),s=m(n.scrollHeight,n.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0);let l=-i.scrollLeft+b(e);const c=-i.scrollTop;return"rtl"===o(a||n).direction&&(l+=m(n.clientWidth,a?a.clientWidth:0)-r),{width:r,height:s,x:l,y:c}}(g(e)))}function C(e){const t=S(y(e)),n=["absolute","fixed"].includes(o(e).position)&&r(e)?w(e):e;return s(n)?t.filter(e=>s(e)&&function(e,t){const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&l(n)){let n=t;do{if(n&&e===n)return!0;n=n.parentNode||n.host}while(n)}return!1}(e,n)&&"body"!==a(e)):[]}const _={getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i}=e;const o=[..."clippingAncestors"===n?C(t):[].concat(n),i],a=o[0],r=o.reduce((e,n)=>{const i=x(t,n);return e.top=m(i.top,e.top),e.right=p(i.right,e.right),e.bottom=p(i.bottom,e.bottom),e.left=m(i.left,e.left),e},x(t,a));return{width:r.right-r.left,height:r.bottom-r.top,x:r.left,y:r.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{rect:t,offsetParent:n,strategy:i}=e;const o=r(n),s=g(n);if(n===s)return t;let l={scrollLeft:0,scrollTop:0};const d={x:0,y:0};if((o||!o&&"fixed"!==i)&&(("body"!==a(n)||c(s))&&(l=v(n)),r(n))){const e=h(n,!0);d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}return{...t,x:t.x-l.scrollLeft+d.x,y:t.y-l.scrollTop+d.y}},isElement:s,getDimensions:T,getOffsetParent:w,getDocumentElement:g,getElementRects:e=>{let{reference:t,floating:n,strategy:i}=e;return{reference:function(e,t,n){const i=r(t),o=g(t),s=h(e,i&&function(e){const t=h(e);return f(t.width)!==e.offsetWidth||f(t.height)!==e.offsetHeight}(t));let l={scrollLeft:0,scrollTop:0};const d={x:0,y:0};if(i||!i&&"fixed"!==n)if(("body"!==a(t)||c(o))&&(l=v(t)),r(t)){const e=h(t,!0);d.x=e.x+t.clientLeft,d.y=e.y+t.clientTop}else o&&(d.x=b(o));return{x:s.left+l.scrollLeft-d.x,y:s.top+l.scrollTop-d.y,width:s.width,height:s.height}}(t,w(n),i),floating:{...T(n),x:0,y:0}}},getClientRects:e=>Array.from(e.getClientRects()),isRTL:e=>"rtl"===o(e).direction};Object.defineProperty(e,"arrow",{enumerable:!0,get:function(){return t.arrow}}),Object.defineProperty(e,"autoPlacement",{enumerable:!0,get:function(){return t.autoPlacement}}),Object.defineProperty(e,"detectOverflow",{enumerable:!0,get:function(){return t.detectOverflow}}),Object.defineProperty(e,"flip",{enumerable:!0,get:function(){return t.flip}}),Object.defineProperty(e,"hide",{enumerable:!0,get:function(){return t.hide}}),Object.defineProperty(e,"inline",{enumerable:!0,get:function(){return t.inline}}),Object.defineProperty(e,"limitShift",{enumerable:!0,get:function(){return t.limitShift}}),Object.defineProperty(e,"offset",{enumerable:!0,get:function(){return t.offset}}),Object.defineProperty(e,"shift",{enumerable:!0,get:function(){return t.shift}}),Object.defineProperty(e,"size",{enumerable:!0,get:function(){return t.size}}),e.autoUpdate=function(e,t,n,i){void 0===i&&(i={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:r=!0,animationFrame:l=!1}=i;let c=!1;const d=o&&!l,u=a&&!l,p=r&&!l,m=d||u?[...s(e)?S(e):[],...S(t)]:[];m.forEach(e=>{d&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let f,g=null;p&&(g=new ResizeObserver(n),s(e)&&g.observe(e),g.observe(t));let v=l?h(e):null;return l&&function t(){if(c)return;const i=h(e);!v||i.x===v.x&&i.y===v.y&&i.width===v.width&&i.height===v.height||n(),v=i,f=requestAnimationFrame(t)}(),()=>{var e;c=!0,m.forEach(e=>{d&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==(e=g)||e.disconnect(),g=null,l&&cancelAnimationFrame(f)}},e.computePosition=((e,n,i)=>t.computePosition(e,n,{platform:_,...i})),e.getOverflowAncestors=S,Object.defineProperty(e,"__esModule",{value:!0})}),function(e){"use strict";"function"==typeof define&&define.amd?define(["jQuery_BENT"],e):"object"==typeof module&&module.exports?module.exports=e(require("jQuery_BENT")):jQuery_BENT&&!jQuery_BENT.fn.hoverIntent&&e(jQuery_BENT)}(function(e){"use strict";function t(e){i=e.pageX,o=e.pageY}function n(e){return"function"==typeof e}var i,o,a={interval:100,sensitivity:6,timeout:0},r=0,s=function(e,n,a,r){if(Math.sqrt((a.pX-i)*(a.pX-i)+(a.pY-o)*(a.pY-o))<r.sensitivity)return n.off(a.event,t),delete a.timeoutId,a.isActive=!0,e.pageX=i,e.pageY=o,delete a.pX,delete a.pY,r.over.apply(n[0],[e]);a.pX=i,a.pY=o,a.timeoutId=setTimeout(function(){s(e,n,a,r)},r.interval)};e.fn.hoverIntent=function(i,o,l){var c=r++,d=e.extend({},a);function u(n){var i=e.extend({},n),o=e(this),a=o.data("hoverIntent");a||o.data("hoverIntent",a={});var r=a[c];r||(a[c]=r={id:c}),r.timeoutId&&(r.timeoutId=clearTimeout(r.timeoutId));var l=r.event="mousemove.hoverIntent.hoverIntent"+c;if("mouseenter"===n.type){if(r.isActive)return;r.pX=i.pageX,r.pY=i.pageY,o.off(l,t).on(l,t),r.timeoutId=setTimeout(function(){s(i,o,r,d)},d.interval)}else{if(!r.isActive)return;o.off(l,t),r.timeoutId=setTimeout(function(){!function(e,t,n,i){var o=t.data("hoverIntent");o&&delete o[n.id],i.apply(t[0],[e])}(i,o,r,d.out)},d.timeout)}}return e.isPlainObject(i)?n((d=e.extend(d,i)).out)||(d.out=d.over):d=n(o)?e.extend(d,{over:i,out:o,selector:l}):e.extend(d,{over:i,out:i,selector:o}),this.on({"mouseenter.hoverIntent":u,"mouseleave.hoverIntent":u},d.selector)}}),function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipe=t()}(this,function(){"use strict";return function(e,t,n,i){var o={features:null,bind:function(e,t,n,i){var o=(i?"remove":"add")+"EventListener";t=t.split(" ");for(var a=0;a<t.length;a++)t[a]&&e[o](t[a],n,!1)},isArray:function(e){return e instanceof Array},createEl:function(e,t){var n=document.createElement(t||"div");return e&&(n.className=e),n},getScrollY:function(){var e=window.pageYOffset;return void 0!==e?e:document.documentElement.scrollTop},unbind:function(e,t,n){o.bind(e,t,n,!0)},removeClass:function(e,t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(e,t){o.hasClass(e,t)||(e.className+=(e.className?" ":"")+t)},hasClass:function(e,t){return e.className&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)},getChildByClass:function(e,t){for(var n=e.firstChild;n;){if(o.hasClass(n,t))return n;n=n.nextSibling}},arraySearch:function(e,t,n){for(var i=e.length;i--;)if(e[i][n]===t)return i;return-1},extend:function(e,t,n){for(var i in t)if(t.hasOwnProperty(i)){if(n&&e.hasOwnProperty(i))continue;e[i]=t[i]}},easing:{sine:{out:function(e){return Math.sin(e*(Math.PI/2))},inOut:function(e){return-(Math.cos(Math.PI*e)-1)/2}},cubic:{out:function(e){return--e*e*e+1}}},detectFeatures:function(){if(o.features)return o.features;var e=o.createEl().style,t="",n={};if(n.oldIE=document.all&&!document.addEventListener,n.touch="ontouchstart"in window,window.requestAnimationFrame&&(n.raf=window.requestAnimationFrame,n.caf=window.cancelAnimationFrame),n.pointerEvent=!!window.PointerEvent||navigator.msPointerEnabled,!n.pointerEvent){var i=navigator.userAgent;if(/iP(hone|od)/.test(navigator.platform)){var a=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);a&&a.length>0&&(a=parseInt(a[1],10))>=1&&a<8&&(n.isOldIOSPhone=!0)}var r=i.match(/Android\s([0-9\.]*)/),s=r?r[1]:0;(s=parseFloat(s))>=1&&(s<4.4&&(n.isOldAndroid=!0),n.androidVersion=s),n.isMobileOpera=/opera mini|opera mobi/i.test(i)}for(var l,c,d=["transform","perspective","animationName"],u=["","webkit","Moz","ms","O"],p=0;p<4;p++){t=u[p];for(var m=0;m<3;m++)l=d[m],c=t+(t?l.charAt(0).toUpperCase()+l.slice(1):l),!n[l]&&c in e&&(n[l]=c);t&&!n.raf&&(t=t.toLowerCase(),n.raf=window[t+"RequestAnimationFrame"],n.raf&&(n.caf=window[t+"CancelAnimationFrame"]||window[t+"CancelRequestAnimationFrame"]))}if(!n.raf){var f=0;n.raf=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-f)),i=window.setTimeout(function(){e(t+n)},n);return f=t+n,i},n.caf=function(e){clearTimeout(e)}}return n.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,o.features=n,n}};o.detectFeatures(),o.features.oldIE&&(o.bind=function(e,t,n,i){t=t.split(" ");for(var o,a=(i?"detach":"attach")+"Event",r=function(){n.handleEvent.call(n)},s=0;s<t.length;s++)if(o=t[s])if("object"==typeof n&&n.handleEvent){if(i){if(!n["oldIE"+o])return!1}else n["oldIE"+o]=r;e[a]("on"+o,n["oldIE"+o])}else e[a]("on"+o,n)});var a=this,r={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(e){return"A"===e.tagName},getDoubleTapZoom:function(e,t){return e?1:t.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"};o.extend(r,i);var s,l,c,d,u,p,m,f,h,g,v,b,y,E,w,T,S,x,C,_,P,B,k,I,D,A,M,R,O,N,$,L,F,U,H,W,j,z,G,Z,q,K,V,Y,X,J,Q,ee,te,ne,ie,oe,ae,re,se,le,ce={x:0,y:0},de={x:0,y:0},ue={x:0,y:0},pe={},me=0,fe={},he={x:0,y:0},ge=0,ve=!0,be=[],ye={},Ee=!1,we=function(e,t){o.extend(a,t.publicMethods),be.push(e)},Te=function(e){var t=Ht();return e>t-1?e-t:e<0?t+e:e},Se={},xe=function(e,t){return Se[e]||(Se[e]=[]),Se[e].push(t)},Ce=function(e){var t=Se[e];if(t){var n=Array.prototype.slice.call(arguments);n.shift();for(var i=0;i<t.length;i++)t[i].apply(a,n)}},_e=function(){return(new Date).getTime()},Pe=function(e){re=e,a.bg.style.opacity=e*r.bgOpacity},Be=function(e,t,n,i,o){(!Ee||o&&o!==a.currItem)&&(i/=o?o.fitRatio:a.currItem.fitRatio),e[B]=b+t+"px, "+n+"px"+y+" scale("+i+")"},ke=function(e){te&&(e&&(g>a.currItem.fitRatio?Ee||(Yt(a.currItem,!1,!0),Ee=!0):Ee&&(Yt(a.currItem),Ee=!1)),Be(te,ue.x,ue.y,g))},Ie=function(e){e.container&&Be(e.container.style,e.initialPosition.x,e.initialPosition.y,e.initialZoomLevel,e)},De=function(e,t){t[B]=b+e+"px, 0px"+y},Ae=function(e,t){if(!r.loop&&t){var n=d+(he.x*me-e)/he.x,i=Math.round(e-dt.x);(n<0&&i>0||n>=Ht()-1&&i<0)&&(e=dt.x+i*r.mainScrollEndFriction)}dt.x=e,De(e,u)},Me=function(e,t){var n=ut[e]-fe[e];return de[e]+ce[e]+n-n*(t/v)},Re=function(e,t){e.x=t.x,e.y=t.y,t.id&&(e.id=t.id)},Oe=function(e){e.x=Math.round(e.x),e.y=Math.round(e.y)},Ne=null,$e=function(){Ne&&(o.unbind(document,"mousemove",$e),o.addClass(e,"pswp--has_mouse"),r.mouseUsed=!0,Ce("mouseUsed")),Ne=setTimeout(function(){Ne=null},100)},Le=function(e,t){var n=Zt(a.currItem,pe,e);return t&&(ee=n),n},Fe=function(e){return e||(e=a.currItem),e.initialZoomLevel},Ue=function(e){return e||(e=a.currItem),e.w>0?r.maxSpreadZoom:1},He=function(e,t,n,i){return i===a.currItem.initialZoomLevel?(n[e]=a.currItem.initialPosition[e],!0):(n[e]=Me(e,i),n[e]>t.min[e]?(n[e]=t.min[e],!0):n[e]<t.max[e]&&(n[e]=t.max[e],!0))},We=function(e){var t="";r.escKey&&27===e.keyCode?t="close":r.arrowKeys&&(37===e.keyCode?t="prev":39===e.keyCode&&(t="next")),t&&(e.ctrlKey||e.altKey||e.shiftKey||e.metaKey||(e.preventDefault?e.preventDefault():e.returnValue=!1,a[t]()))},je=function(e){e&&(K||q||ne||j)&&(e.preventDefault(),e.stopPropagation())},ze=function(){a.setScrollOffset(0,o.getScrollY())},Ge={},Ze=0,qe=function(e){Ge[e]&&(Ge[e].raf&&A(Ge[e].raf),Ze--,delete Ge[e])},Ke=function(e){Ge[e]&&qe(e),Ge[e]||(Ze++,Ge[e]={})},Ve=function(){for(var e in Ge)Ge.hasOwnProperty(e)&&qe(e)},Ye=function(e,t,n,i,o,a,r){var s,l=_e();Ke(e);var c=function(){if(Ge[e]){if((s=_e()-l)>=i)return qe(e),a(n),void(r&&r());a((n-t)*o(s/i)+t),Ge[e].raf=D(c)}};c()},Xe={shout:Ce,listen:xe,viewportSize:pe,options:r,isMainScrollAnimating:function(){return ne},getZoomLevel:function(){return g},getCurrentIndex:function(){return d},isDragging:function(){return G},isZooming:function(){return J},setScrollOffset:function(e,t){fe.x=e,N=fe.y=t,Ce("updateScrollOffset",fe)},applyZoomPan:function(e,t,n,i){ue.x=t,ue.y=n,g=e,ke(i)},init:function(){if(!s&&!l){var n;a.framework=o,a.template=e,a.bg=o.getChildByClass(e,"pswp__bg"),M=e.className,s=!0,$=o.detectFeatures(),D=$.raf,A=$.caf,B=$.transform,O=$.oldIE,a.scrollWrap=o.getChildByClass(e,"pswp__scroll-wrap"),a.container=o.getChildByClass(a.scrollWrap,"pswp__container"),u=a.container.style,a.itemHolders=T=[{el:a.container.children[0],wrap:0,index:-1},{el:a.container.children[1],wrap:0,index:-1},{el:a.container.children[2],wrap:0,index:-1}],T[0].el.style.display=T[2].el.style.display="none",function(){if(B){var t=$.perspective&&!I;return b="translate"+(t?"3d(":"("),void(y=$.perspective?", 0px)":")")}B="left",o.addClass(e,"pswp--ie"),De=function(e,t){t.left=e+"px"},Ie=function(e){var t=e.fitRatio>1?1:e.fitRatio,n=e.container.style,i=t*e.w,o=t*e.h;n.width=i+"px",n.height=o+"px",n.left=e.initialPosition.x+"px",n.top=e.initialPosition.y+"px"},ke=function(){if(te){var e=te,t=a.currItem,n=t.fitRatio>1?1:t.fitRatio,i=n*t.w,o=n*t.h;e.width=i+"px",e.height=o+"px",e.left=ue.x+"px",e.top=ue.y+"px"}}}(),h={resize:a.updateSize,orientationchange:function(){clearTimeout(L),L=setTimeout(function(){pe.x!==a.scrollWrap.clientWidth&&a.updateSize()},500)},scroll:ze,keydown:We,click:je};var i=$.isOldIOSPhone||$.isOldAndroid||$.isMobileOpera;for($.animationName&&$.transform&&!i||(r.showAnimationDuration=r.hideAnimationDuration=0),n=0;n<be.length;n++)a["init"+be[n]]();t&&(a.ui=new t(a,o)).init(),Ce("firstUpdate"),d=d||r.index||0,(isNaN(d)||d<0||d>=Ht())&&(d=0),a.currItem=Ut(d),($.isOldIOSPhone||$.isOldAndroid)&&(ve=!1),e.setAttribute("aria-hidden","false"),r.modal&&(ve?e.style.position="fixed":(e.style.position="absolute",e.style.top=o.getScrollY()+"px")),void 0===N&&(Ce("initialLayout"),N=R=o.getScrollY());var c="pswp--open ";for(r.mainClass&&(c+=r.mainClass+" "),r.showHideOpacity&&(c+="pswp--animate_opacity "),c+=I?"pswp--touch":"pswp--notouch",c+=$.animationName?" pswp--css_animation":"",c+=$.svg?" pswp--svg":"",o.addClass(e,c),a.updateSize(),p=-1,ge=null,n=0;n<3;n++)De((n+p)*he.x,T[n].el.style);O||o.bind(a.scrollWrap,f,a),xe("initialZoomInEnd",function(){a.setContent(T[0],d-1),a.setContent(T[2],d+1),T[0].el.style.display=T[2].el.style.display="block",r.focus&&e.focus(),o.bind(document,"keydown",a),$.transform&&o.bind(a.scrollWrap,"click",a),r.mouseUsed||o.bind(document,"mousemove",$e),o.bind(window,"resize scroll orientationchange",a),Ce("bindEvents")}),a.setContent(T[1],d),a.updateCurrItem(),Ce("afterInit"),ve||(E=setInterval(function(){Ze||G||J||g!==a.currItem.initialZoomLevel||a.updateSize()},1e3)),o.addClass(e,"pswp--visible")}},close:function(){s&&(s=!1,l=!0,Ce("close"),o.unbind(window,"resize scroll orientationchange",a),o.unbind(window,"scroll",h.scroll),o.unbind(document,"keydown",a),o.unbind(document,"mousemove",$e),$.transform&&o.unbind(a.scrollWrap,"click",a),G&&o.unbind(window,m,a),clearTimeout(L),Ce("unbindEvents"),Wt(a.currItem,null,!0,a.destroy))},destroy:function(){Ce("destroy"),Nt&&clearTimeout(Nt),e.setAttribute("aria-hidden","true"),e.className=M,E&&clearInterval(E),o.unbind(a.scrollWrap,f,a),o.unbind(window,"scroll",a),ft(),Ve(),Se=null},panTo:function(e,t,n){n||(e>ee.min.x?e=ee.min.x:e<ee.max.x&&(e=ee.max.x),t>ee.min.y?t=ee.min.y:t<ee.max.y&&(t=ee.max.y)),ue.x=e,ue.y=t,ke()},handleEvent:function(e){e=e||window.event,h[e.type]&&h[e.type](e)},goTo:function(e){var t=(e=Te(e))-d;ge=t,d=e,a.currItem=Ut(d),me-=t,Ae(he.x*me),Ve(),ne=!1,a.updateCurrItem()},next:function(){a.goTo(d+1)},prev:function(){a.goTo(d-1)},updateCurrZoomItem:function(e){if(e&&Ce("beforeChange",0),T[1].el.children.length){var t=T[1].el.children[0];te=o.hasClass(t,"pswp__zoom-wrap")?t.style:null}else te=null;ee=a.currItem.bounds,v=g=a.currItem.initialZoomLevel,ue.x=ee.center.x,ue.y=ee.center.y,e&&Ce("afterChange")},invalidateCurrItems:function(){w=!0;for(var e=0;e<3;e++)T[e].item&&(T[e].item.needsUpdate=!0)},updateCurrItem:function(e){if(0!==ge){var t,n=Math.abs(ge);if(!(e&&n<2)){a.currItem=Ut(d),Ee=!1,Ce("beforeChange",ge),n>=3&&(p+=ge+(ge>0?-3:3),n=3);for(var i=0;i<n;i++)ge>0?(t=T.shift(),T[2]=t,De((++p+2)*he.x,t.el.style),a.setContent(t,d-n+i+1+1)):(t=T.pop(),T.unshift(t),De(--p*he.x,t.el.style),a.setContent(t,d+n-i-1-1));if(te&&1===Math.abs(ge)){var o=Ut(S);o.initialZoomLevel!==g&&(Zt(o,pe),Yt(o),Ie(o))}ge=0,a.updateCurrZoomItem(),S=d,Ce("afterChange")}}},updateSize:function(t){if(!ve&&r.modal){var n=o.getScrollY();if(N!==n&&(e.style.top=n+"px",N=n),!t&&ye.x===window.innerWidth&&ye.y===window.innerHeight)return;ye.x=window.innerWidth,ye.y=window.innerHeight,e.style.height=ye.y+"px"}if(pe.x=a.scrollWrap.clientWidth,pe.y=a.scrollWrap.clientHeight,ze(),he.x=pe.x+Math.round(pe.x*r.spacing),he.y=pe.y,Ae(he.x*me),Ce("beforeResize"),void 0!==p){for(var i,s,l,c=0;c<3;c++)i=T[c],De((c+p)*he.x,i.el.style),l=d+c-1,r.loop&&Ht()>2&&(l=Te(l)),(s=Ut(l))&&(w||s.needsUpdate||!s.bounds)?(a.cleanSlide(s),a.setContent(i,l),1===c&&(a.currItem=s,a.updateCurrZoomItem(!0)),s.needsUpdate=!1):-1===i.index&&l>=0&&a.setContent(i,l),s&&s.container&&(Zt(s,pe),Yt(s),Ie(s));w=!1}v=g=a.currItem.initialZoomLevel,(ee=a.currItem.bounds)&&(ue.x=ee.center.x,ue.y=ee.center.y,ke(!0)),Ce("resize")},zoomTo:function(e,t,n,i,a){t&&(v=g,ut.x=Math.abs(t.x)-ue.x,ut.y=Math.abs(t.y)-ue.y,Re(de,ue));var r=Le(e,!1),s={};He("x",r,s,e),He("y",r,s,e);var l=g,c=ue.x,d=ue.y;Oe(s);var u=function(t){1===t?(g=e,ue.x=s.x,ue.y=s.y):(g=(e-l)*t+l,ue.x=(s.x-c)*t+c,ue.y=(s.y-d)*t+d),a&&a(t),ke(1===t)};n?Ye("customZoomTo",0,1,n,i||o.easing.sine.inOut,u):u(1)}},Je={},Qe={},et={},tt={},nt={},it=[],ot={},at=[],rt={},st=0,lt={x:0,y:0},ct=0,dt={x:0,y:0},ut={x:0,y:0},pt={x:0,y:0},mt=function(e,t){return rt.x=Math.abs(e.x-t.x),rt.y=Math.abs(e.y-t.y),Math.sqrt(rt.x*rt.x+rt.y*rt.y)},ft=function(){V&&(A(V),V=null)},ht=function(){G&&(V=D(ht),kt())},gt=function(e,t){return!(!e||e===document)&&!(e.getAttribute("class")&&e.getAttribute("class").indexOf("pswp__scroll-wrap")>-1)&&(t(e)?e:gt(e.parentNode,t))},vt={},bt=function(e,t){return vt.prevent=!gt(e.target,r.isClickableElement),Ce("preventDragEvent",e,t,vt),vt.prevent},yt=function(e,t){return t.x=e.pageX,t.y=e.pageY,t.id=e.identifier,t},Et=function(e,t,n){n.x=.5*(e.x+t.x),n.y=.5*(e.y+t.y)},wt=function(){var e=ue.y-a.currItem.initialPosition.y;return 1-Math.abs(e/(pe.y/2))},Tt={},St={},xt=[],Ct=function(e){for(;xt.length>0;)xt.pop();return k?(le=0,it.forEach(function(e){0===le?xt[0]=e:1===le&&(xt[1]=e),le++})):e.type.indexOf("touch")>-1?e.touches&&e.touches.length>0&&(xt[0]=yt(e.touches[0],Tt),e.touches.length>1&&(xt[1]=yt(e.touches[1],St))):(Tt.x=e.pageX,Tt.y=e.pageY,Tt.id="",xt[0]=Tt),xt},_t=function(e,t){var n,i,o,s,l=ue[e]+t[e],c=t[e]>0,d=dt.x+t.x,u=dt.x-ot.x;return n=l>ee.min[e]||l<ee.max[e]?r.panEndFriction:1,l=ue[e]+t[e]*n,!r.allowPanToNext&&g!==a.currItem.initialZoomLevel||(te?"h"!==ie||"x"!==e||q||(c?(l>ee.min[e]&&(n=r.panEndFriction,ee.min[e],i=ee.min[e]-de[e]),(i<=0||u<0)&&Ht()>1?(s=d,u<0&&d>ot.x&&(s=ot.x)):ee.min.x!==ee.max.x&&(o=l)):(l<ee.max[e]&&(n=r.panEndFriction,ee.max[e],i=de[e]-ee.max[e]),(i<=0||u>0)&&Ht()>1?(s=d,u>0&&d<ot.x&&(s=ot.x)):ee.min.x!==ee.max.x&&(o=l))):s=d,"x"!==e)?void(ne||Y||g>a.currItem.fitRatio&&(ue[e]+=t[e]*n)):(void 0!==s&&(Ae(s,!0),Y=s!==ot.x),ee.min.x!==ee.max.x&&(void 0!==o?ue.x=o:Y||(ue.x+=t.x*n)),void 0!==s)},Pt=function(e){if(!("mousedown"===e.type&&e.button>0)){if(Ft)return void e.preventDefault();if(!z||"mousedown"!==e.type){if(bt(e,!0)&&e.preventDefault(),Ce("pointerDown"),k){var t=o.arraySearch(it,e.pointerId,"id");t<0&&(t=it.length),it[t]={x:e.pageX,y:e.pageY,id:e.pointerId}}var n=Ct(e),i=n.length;X=null,Ve(),G&&1!==i||(G=oe=!0,o.bind(window,m,a),W=se=ae=j=Y=K=Z=q=!1,ie=null,Ce("firstTouchStart",n),Re(de,ue),ce.x=ce.y=0,Re(tt,n[0]),Re(nt,tt),ot.x=he.x*me,at=[{x:tt.x,y:tt.y}],U=F=_e(),Le(g,!0),ft(),ht()),!J&&i>1&&!ne&&!Y&&(v=g,q=!1,J=Z=!0,ce.y=ce.x=0,Re(de,ue),Re(Je,n[0]),Re(Qe,n[1]),Et(Je,Qe,pt),ut.x=Math.abs(pt.x)-ue.x,ut.y=Math.abs(pt.y)-ue.y,Q=mt(Je,Qe))}}},Bt=function(e){if(e.preventDefault(),k){var t=o.arraySearch(it,e.pointerId,"id");if(t>-1){var n=it[t];n.x=e.pageX,n.y=e.pageY}}if(G){var i=Ct(e);if(ie||K||J)X=i;else if(dt.x!==he.x*me)ie="h";else{var a=Math.abs(i[0].x-tt.x)-Math.abs(i[0].y-tt.y);Math.abs(a)>=10&&(ie=a>0?"h":"v",X=i)}}},kt=function(){if(X){var e=X.length;if(0!==e)if(Re(Je,X[0]),et.x=Je.x-tt.x,et.y=Je.y-tt.y,J&&e>1){if(tt.x=Je.x,tt.y=Je.y,!et.x&&!et.y&&function(e,t){return e.x===t.x&&e.y===t.y}(X[1],Qe))return;Re(Qe,X[1]),q||(q=!0,Ce("zoomGestureStarted"));var t=mt(Je,Qe),n=Rt(t);n>a.currItem.initialZoomLevel+a.currItem.initialZoomLevel/15&&(se=!0);var i=1,o=Fe(),s=Ue();if(n<o)if(r.pinchToClose&&!se&&v<=a.currItem.initialZoomLevel){var l=1-(o-n)/(o/1.2);Pe(l),Ce("onPinchClose",l),ae=!0}else(i=(o-n)/o)>1&&(i=1),n=o-i*(o/3);else n>s&&((i=(n-s)/(6*o))>1&&(i=1),n=s+i*o);i<0&&(i=0),Et(Je,Qe,lt),ce.x+=lt.x-pt.x,ce.y+=lt.y-pt.y,Re(pt,lt),ue.x=Me("x",n),ue.y=Me("y",n),W=n>g,g=n,ke()}else{if(!ie)return;if(oe&&(oe=!1,Math.abs(et.x)>=10&&(et.x-=X[0].x-nt.x),Math.abs(et.y)>=10&&(et.y-=X[0].y-nt.y)),tt.x=Je.x,tt.y=Je.y,0===et.x&&0===et.y)return;if("v"===ie&&r.closeOnVerticalDrag&&"fit"===r.scaleMode&&g===a.currItem.initialZoomLevel){ce.y+=et.y,ue.y+=et.y;var c=wt();return j=!0,Ce("onVerticalDrag",c),Pe(c),void ke()}(function(e,t,n){if(e-U>50){var i=at.length>2?at.shift():{};i.x=t,i.y=n,at.push(i),U=e}})(_e(),Je.x,Je.y),K=!0,ee=a.currItem.bounds,_t("x",et)||(_t("y",et),Oe(ue),ke())}}},It=function(e){if($.isOldAndroid){if(z&&"mouseup"===e.type)return;e.type.indexOf("touch")>-1&&(clearTimeout(z),z=setTimeout(function(){z=0},600))}var t;if(Ce("pointerUp"),bt(e,!1)&&e.preventDefault(),k){var n=o.arraySearch(it,e.pointerId,"id");n>-1&&(t=it.splice(n,1)[0],navigator.msPointerEnabled?(t.type={4:"mouse",2:"touch",3:"pen"}[e.pointerType],t.type||(t.type=e.pointerType||"mouse")):t.type=e.pointerType||"mouse")}var i,s=Ct(e),l=s.length;if("mouseup"===e.type&&(l=0),2===l)return X=null,!0;1===l&&Re(nt,s[0]),0!==l||ie||ne||(t||("mouseup"===e.type?t={x:e.pageX,y:e.pageY,type:"mouse"}:e.changedTouches&&e.changedTouches[0]&&(t={x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY,type:"touch"})),Ce("touchRelease",e,t));var c=-1;if(0===l&&(G=!1,o.unbind(window,m,a),ft(),J?c=0:-1!==ct&&(c=_e()-ct)),ct=1===l?_e():-1,i=-1!==c&&c<150?"zoom":"swipe",J&&l<2&&(J=!1,1===l&&(i="zoomPointerUp"),Ce("zoomGestureEnded")),X=null,K||q||ne||j)if(Ve(),H||(H=Dt()),H.calculateSwipeSpeed("x"),j)if(wt()<r.verticalDragRange)a.close();else{var d=ue.y,u=re;Ye("verticalDrag",0,1,300,o.easing.cubic.out,function(e){ue.y=(a.currItem.initialPosition.y-d)*e+d,Pe((1-u)*e+u),ke()}),Ce("onVerticalDrag",1)}else{if((Y||ne)&&0===l){if(Mt(i,H))return;i="zoomPointerUp"}if(!ne)return"swipe"!==i?void Ot():void(!Y&&g>a.currItem.fitRatio&&At(H))}},Dt=function(){var e,t,n={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(i){at.length>1?(e=_e()-U+50,t=at[at.length-2][i]):(e=_e()-F,t=nt[i]),n.lastFlickOffset[i]=tt[i]-t,n.lastFlickDist[i]=Math.abs(n.lastFlickOffset[i]),n.lastFlickDist[i]>20?n.lastFlickSpeed[i]=n.lastFlickOffset[i]/e:n.lastFlickSpeed[i]=0,Math.abs(n.lastFlickSpeed[i])<.1&&(n.lastFlickSpeed[i]=0),n.slowDownRatio[i]=.95,n.slowDownRatioReverse[i]=1-n.slowDownRatio[i],n.speedDecelerationRatio[i]=1},calculateOverBoundsAnimOffset:function(e,t){n.backAnimStarted[e]||(ue[e]>ee.min[e]?n.backAnimDestination[e]=ee.min[e]:ue[e]<ee.max[e]&&(n.backAnimDestination[e]=ee.max[e]),void 0!==n.backAnimDestination[e]&&(n.slowDownRatio[e]=.7,n.slowDownRatioReverse[e]=1-n.slowDownRatio[e],n.speedDecelerationRatioAbs[e]<.05&&(n.lastFlickSpeed[e]=0,n.backAnimStarted[e]=!0,Ye("bounceZoomPan"+e,ue[e],n.backAnimDestination[e],t||300,o.easing.sine.out,function(t){ue[e]=t,ke()}))))},calculateAnimOffset:function(e){n.backAnimStarted[e]||(n.speedDecelerationRatio[e]=n.speedDecelerationRatio[e]*(n.slowDownRatio[e]+n.slowDownRatioReverse[e]-n.slowDownRatioReverse[e]*n.timeDiff/10),n.speedDecelerationRatioAbs[e]=Math.abs(n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]),n.distanceOffset[e]=n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]*n.timeDiff,ue[e]+=n.distanceOffset[e])},panAnimLoop:function(){if(Ge.zoomPan&&(Ge.zoomPan.raf=D(n.panAnimLoop),n.now=_e(),n.timeDiff=n.now-n.lastNow,n.lastNow=n.now,n.calculateAnimOffset("x"),n.calculateAnimOffset("y"),ke(),n.calculateOverBoundsAnimOffset("x"),n.calculateOverBoundsAnimOffset("y"),n.speedDecelerationRatioAbs.x<.05&&n.speedDecelerationRatioAbs.y<.05))return ue.x=Math.round(ue.x),ue.y=Math.round(ue.y),ke(),void qe("zoomPan")}};return n},At=function(e){return e.calculateSwipeSpeed("y"),ee=a.currItem.bounds,e.backAnimDestination={},e.backAnimStarted={},Math.abs(e.lastFlickSpeed.x)<=.05&&Math.abs(e.lastFlickSpeed.y)<=.05?(e.speedDecelerationRatioAbs.x=e.speedDecelerationRatioAbs.y=0,e.calculateOverBoundsAnimOffset("x"),e.calculateOverBoundsAnimOffset("y"),!0):(Ke("zoomPan"),e.lastNow=_e(),void e.panAnimLoop())},Mt=function(e,t){var n,i,s;if(ne||(st=d),"swipe"===e){var l=tt.x-nt.x,c=t.lastFlickDist.x<10;l>30&&(c||t.lastFlickOffset.x>20)?i=-1:l<-30&&(c||t.lastFlickOffset.x<-20)&&(i=1)}i&&((d+=i)<0?(d=r.loop?Ht()-1:0,s=!0):d>=Ht()&&(d=r.loop?0:Ht()-1,s=!0),s&&!r.loop||(ge+=i,me-=i,n=!0));var u,p=he.x*me,m=Math.abs(p-dt.x);return n||p>dt.x==t.lastFlickSpeed.x>0?(u=Math.abs(t.lastFlickSpeed.x)>0?m/Math.abs(t.lastFlickSpeed.x):333,u=Math.min(u,400),u=Math.max(u,250)):u=333,st===d&&(n=!1),ne=!0,Ce("mainScrollAnimStart"),Ye("mainScroll",dt.x,p,u,o.easing.cubic.out,Ae,function(){Ve(),ne=!1,st=-1,(n||st!==d)&&a.updateCurrItem(),Ce("mainScrollAnimComplete")}),n&&a.updateCurrItem(!0),n},Rt=function(e){return 1/Q*e*v},Ot=function(){var e=g,t=Fe(),n=Ue();g<t?e=t:g>n&&(e=n);var i,r=re;return ae&&!W&&!se&&g<t?(a.close(),!0):(ae&&(i=function(e){Pe((1-r)*e+r)}),a.zoomTo(e,0,200,o.easing.cubic.out,i),!0)};we("Gestures",{publicMethods:{initGestures:function(){var e=function(e,t,n,i,o){x=e+t,C=e+n,_=e+i,P=o?e+o:""};(k=$.pointerEvent)&&$.touch&&($.touch=!1),k?navigator.msPointerEnabled?e("MSPointer","Down","Move","Up","Cancel"):e("pointer","down","move","up","cancel"):$.touch?(e("touch","start","move","end","cancel"),I=!0):e("mouse","down","move","up"),m=C+" "+_+" "+P,f=x,k&&!I&&(I=navigator.maxTouchPoints>1||navigator.msMaxTouchPoints>1),a.likelyTouchDevice=I,h[x]=Pt,h[C]=Bt,h[_]=It,P&&(h[P]=h[_]),$.touch&&(f+=" mousedown",m+=" mousemove mouseup",h.mousedown=h[x],h.mousemove=h[C],h.mouseup=h[_]),I||(r.allowPanToNext=!1)}}});var Nt,$t,Lt,Ft,Ut,Ht,Wt=function(t,n,i,s){var l;Nt&&clearTimeout(Nt),Ft=!0,Lt=!0,t.initialLayout?(l=t.initialLayout,t.initialLayout=null):l=r.getThumbBoundsFn&&r.getThumbBoundsFn(d);var u=i?r.hideAnimationDuration:r.showAnimationDuration,p=function(){qe("initialZoom"),i?(a.template.removeAttribute("style"),a.bg.removeAttribute("style")):(Pe(1),n&&(n.style.display="block"),o.addClass(e,"pswp--animated-in"),Ce("initialZoom"+(i?"OutEnd":"InEnd"))),s&&s(),Ft=!1};if(!u||!l||void 0===l.x)return Ce("initialZoom"+(i?"Out":"In")),g=t.initialZoomLevel,Re(ue,t.initialPosition),ke(),e.style.opacity=i?0:1,Pe(1),void(u?setTimeout(function(){p()},u):p());!function(){var n=c,s=!a.currItem.src||a.currItem.loadError||r.showHideOpacity;t.miniImg&&(t.miniImg.style.webkitBackfaceVisibility="hidden"),i||(g=l.w/t.w,ue.x=l.x,ue.y=l.y-R,a[s?"template":"bg"].style.opacity=.001,ke()),Ke("initialZoom"),i&&!n&&o.removeClass(e,"pswp--animated-in"),s&&(i?o[(n?"remove":"add")+"Class"](e,"pswp--animate_opacity"):setTimeout(function(){o.addClass(e,"pswp--animate_opacity")},30)),Nt=setTimeout(function(){if(Ce("initialZoom"+(i?"Out":"In")),i){var a=l.w/t.w,r={x:ue.x,y:ue.y},c=g,d=re,m=function(t){1===t?(g=a,ue.x=l.x,ue.y=l.y-N):(g=(a-c)*t+c,ue.x=(l.x-r.x)*t+r.x,ue.y=(l.y-N-r.y)*t+r.y),ke(),s?e.style.opacity=1-t:Pe(d-t*d)};n?Ye("initialZoom",0,1,u,o.easing.cubic.out,m,p):(m(1),Nt=setTimeout(p,u+20))}else g=t.initialZoomLevel,Re(ue,t.initialPosition),ke(),Pe(1),s?e.style.opacity=1:Pe(1),Nt=setTimeout(p,u+20)},i?25:90)}()},jt={},zt=[],Gt={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return $t.length}},Zt=function(e,t,n){if(e.src&&!e.loadError){var i=!n;if(i&&(e.vGap||(e.vGap={top:0,bottom:0}),Ce("parseVerticalMargin",e)),jt.x=t.x,jt.y=t.y-e.vGap.top-e.vGap.bottom,i){var o=jt.x/e.w,a=jt.y/e.h;e.fitRatio=o<a?o:a;var s=r.scaleMode;"orig"===s?n=1:"fit"===s&&(n=e.fitRatio),n>1&&(n=1),e.initialZoomLevel=n,e.bounds||(e.bounds={center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}})}if(!n)return;return function(e,t,n){var i=e.bounds;i.center.x=Math.round((jt.x-t)/2),i.center.y=Math.round((jt.y-n)/2)+e.vGap.top,i.max.x=t>jt.x?Math.round(jt.x-t):i.center.x,i.max.y=n>jt.y?Math.round(jt.y-n)+e.vGap.top:i.center.y,i.min.x=t>jt.x?0:i.center.x,i.min.y=n>jt.y?e.vGap.top:i.center.y}(e,e.w*n,e.h*n),i&&n===e.initialZoomLevel&&(e.initialPosition=e.bounds.center),e.bounds}return e.w=e.h=0,e.initialZoomLevel=e.fitRatio=1,e.bounds={center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}},e.initialPosition=e.bounds.center,e.bounds},qt=function(e,t,n,i,o,r){t.loadError||i&&(t.imageAppended=!0,Yt(t,i,t===a.currItem&&Ee),n.appendChild(i),r&&setTimeout(function(){t&&t.loaded&&t.placeholder&&(t.placeholder.style.display="none",t.placeholder=null)},500))},Kt=function(e){e.loading=!0,e.loaded=!1;var t=e.img=o.createEl("pswp__img","img"),n=function(){e.loading=!1,e.loaded=!0,e.loadComplete?e.loadComplete(e):e.img=null,t.onload=t.onerror=null,t=null};return t.onload=n,t.onerror=function(){e.loadError=!0,n()},t.src=e.src,t},Vt=function(e,t){if(e.src&&e.loadError&&e.container)return t&&(e.container.innerHTML=""),e.container.innerHTML=r.errorMsg.replace("%url%",e.src),!0},Yt=function(e,t,n){if(e.src){t||(t=e.container.lastChild);var i=n?e.w:Math.round(e.w*e.fitRatio),o=n?e.h:Math.round(e.h*e.fitRatio);e.placeholder&&!e.loaded&&(e.placeholder.style.width=i+"px",e.placeholder.style.height=o+"px"),t.style.width=i+"px",t.style.height=o+"px"}},Xt=function(){if(zt.length){for(var e,t=0;t<zt.length;t++)(e=zt[t]).holder.index===e.index&&qt(e.index,e.item,e.baseDiv,e.img,0,e.clearPlaceholder);zt=[]}};we("Controller",{publicMethods:{lazyLoadItem:function(e){e=Te(e);var t=Ut(e);t&&(!t.loaded&&!t.loading||w)&&(Ce("gettingData",e,t),t.src&&Kt(t))},initController:function(){o.extend(r,Gt,!0),a.items=$t=n,Ut=a.getItemAt,Ht=r.getNumItemsFn,r.loop,Ht()<3&&(r.loop=!1),xe("beforeChange",function(e){var t,n=r.preload,i=null===e||e>=0,o=Math.min(n[0],Ht()),s=Math.min(n[1],Ht());for(t=1;t<=(i?s:o);t++)a.lazyLoadItem(d+t);for(t=1;t<=(i?o:s);t++)a.lazyLoadItem(d-t)}),xe("initialLayout",function(){a.currItem.initialLayout=r.getThumbBoundsFn&&r.getThumbBoundsFn(d)}),xe("mainScrollAnimComplete",Xt),xe("initialZoomInEnd",Xt),xe("destroy",function(){for(var e,t=0;t<$t.length;t++)(e=$t[t]).container&&(e.container=null),e.placeholder&&(e.placeholder=null),e.img&&(e.img=null),e.preloader&&(e.preloader=null),e.loadError&&(e.loaded=e.loadError=!1);zt=null})},getItemAt:function(e){return e>=0&&void 0!==$t[e]&&$t[e]},allowProgressiveImg:function(){return r.forceProgressiveLoading||!I||r.mouseUsed||screen.width>1200},setContent:function(e,t){r.loop&&(t=Te(t));var n=a.getItemAt(e.index);n&&(n.container=null);var i,l=a.getItemAt(t);if(l){Ce("gettingData",t,l),e.index=t,e.item=l;var c=l.container=o.createEl("pswp__zoom-wrap");if(!l.src&&l.html&&(l.html.tagName?c.appendChild(l.html):c.innerHTML=l.html),Vt(l),Zt(l,pe),!l.src||l.loadError||l.loaded)l.src&&!l.loadError&&((i=o.createEl("pswp__img","img")).style.opacity=1,i.src=l.src,Yt(l,i),qt(0,l,c,i));else{if(l.loadComplete=function(n){if(s){if(e&&e.index===t){if(Vt(n,!0))return n.loadComplete=n.img=null,Zt(n,pe),Ie(n),void(e.index===d&&a.updateCurrZoomItem());n.imageAppended?!Ft&&n.placeholder&&(n.placeholder.style.display="none",n.placeholder=null):$.transform&&(ne||Ft)?zt.push({item:n,baseDiv:c,img:n.img,index:t,holder:e,clearPlaceholder:!0}):qt(0,n,c,n.img,0,!0)}n.loadComplete=null,n.img=null,Ce("imageLoadComplete",t,n)}},o.features.transform){var u="pswp__img pswp__img--placeholder";u+=l.msrc?"":" pswp__img--placeholder--blank";var p=o.createEl(u,l.msrc?"img":"");l.msrc&&(p.src=l.msrc),Yt(l,p),c.appendChild(p),l.placeholder=p}l.loading||Kt(l),a.allowProgressiveImg()&&(!Lt&&$.transform?zt.push({item:l,baseDiv:c,img:l.img,index:t,holder:e}):qt(0,l,c,l.img,0,!0))}Lt||t!==d?Ie(l):(te=c.style,Wt(l,i||l.img)),e.el.innerHTML="",e.el.appendChild(c)}else e.el.innerHTML=""},cleanSlide:function(e){e.img&&(e.img.onload=e.img.onerror=null),e.loaded=e.loading=e.img=e.imageAppended=!1}}});var Jt,Qt,en={},tn=function(e,t,n){var i=document.createEvent("CustomEvent"),o={origEvent:e,target:e.target,releasePoint:t,pointerType:n||"touch"};i.initCustomEvent("pswpTap",!0,!0,o),e.target.dispatchEvent(i)};we("Tap",{publicMethods:{initTap:function(){xe("firstTouchStart",a.onTapStart),xe("touchRelease",a.onTapRelease),xe("destroy",function(){en={},Jt=null})},onTapStart:function(e){e.length>1&&(clearTimeout(Jt),Jt=null)},onTapRelease:function(e,t){if(t&&!K&&!Z&&!Ze){var n=t;if(Jt&&(clearTimeout(Jt),Jt=null,function(e,t){return Math.abs(e.x-t.x)<25&&Math.abs(e.y-t.y)<25}(n,en)))return void Ce("doubleTap",n);if("mouse"===t.type)return void tn(e,t,"mouse");if("BUTTON"===e.target.tagName.toUpperCase()||o.hasClass(e.target,"pswp__single-tap"))return void tn(e,t);Re(en,n),Jt=setTimeout(function(){tn(e,t),Jt=null},300)}}}}),we("DesktopZoom",{publicMethods:{initDesktopZoom:function(){O||(I?xe("mouseUsed",function(){a.setupDesktopZoom()}):a.setupDesktopZoom(!0))},setupDesktopZoom:function(t){Qt={};var n="wheel mousewheel DOMMouseScroll";xe("bindEvents",function(){o.bind(e,n,a.handleMouseWheel)}),xe("unbindEvents",function(){Qt&&o.unbind(e,n,a.handleMouseWheel)}),a.mouseZoomedIn=!1;var i,r=function(){a.mouseZoomedIn&&(o.removeClass(e,"pswp--zoomed-in"),a.mouseZoomedIn=!1),g<1?o.addClass(e,"pswp--zoom-allowed"):o.removeClass(e,"pswp--zoom-allowed"),s()},s=function(){i&&(o.removeClass(e,"pswp--dragging"),i=!1)};xe("resize",r),xe("afterChange",r),xe("pointerDown",function(){a.mouseZoomedIn&&(i=!0,o.addClass(e,"pswp--dragging"))}),xe("pointerUp",s),t||r()},handleMouseWheel:function(e){if(g<=a.currItem.fitRatio)return r.modal&&(!r.closeOnScroll||Ze||G?e.preventDefault():B&&Math.abs(e.deltaY)>2&&(c=!0,a.close())),!0;if(e.stopPropagation(),Qt.x=0,"deltaX"in e)1===e.deltaMode?(Qt.x=18*e.deltaX,Qt.y=18*e.deltaY):(Qt.x=e.deltaX,Qt.y=e.deltaY);else if("wheelDelta"in e)e.wheelDeltaX&&(Qt.x=-.16*e.wheelDeltaX),e.wheelDeltaY?Qt.y=-.16*e.wheelDeltaY:Qt.y=-.16*e.wheelDelta;else{if(!("detail"in e))return;Qt.y=e.detail}Le(g,!0);var t=ue.x-Qt.x,n=ue.y-Qt.y;(r.modal||t<=ee.min.x&&t>=ee.max.x&&n<=ee.min.y&&n>=ee.max.y)&&e.preventDefault(),a.panTo(t,n)},toggleDesktopZoom:function(t){t=t||{x:pe.x/2+fe.x,y:pe.y/2+fe.y};var n=r.getDoubleTapZoom(!0,a.currItem),i=g===n;a.mouseZoomedIn=!i,a.zoomTo(i?a.currItem.initialZoomLevel:n,t,333),o[(i?"remove":"add")+"Class"](e,"pswp--zoomed-in")}}});var nn,on,an,rn,sn,ln,cn,dn,un,pn,mn,fn,hn={history:!0,galleryUID:1},gn=function(){return mn.hash.substring(1)},vn=function(){nn&&clearTimeout(nn),an&&clearTimeout(an)},bn=function(){var e=gn(),t={};if(e.length<5)return t;var n,i=e.split("&");for(n=0;n<i.length;n++)if(i[n]){var o=i[n].split("=");o.length<2||(t[o[0]]=o[1])}if(r.galleryPIDs){var a=t.pid;for(t.pid=0,n=0;n<$t.length;n++)if($t[n].pid===a){t.pid=n;break}}else t.pid=parseInt(t.pid,10)-1;return t.pid<0&&(t.pid=0),t},yn=function(){if(an&&clearTimeout(an),Ze||G)an=setTimeout(yn,500);else{rn?clearTimeout(on):rn=!0;var e=d+1,t=Ut(d);t.hasOwnProperty("pid")&&(e=t.pid);var n=cn+"&gid="+r.galleryUID+"&pid="+e;dn||-1===mn.hash.indexOf(n)&&(pn=!0);var i=mn.href.split("#")[0]+"#"+n;fn?"#"+n!==window.location.hash&&history[dn?"replaceState":"pushState"]("",document.title,i):dn?mn.replace(i):mn.hash=n,dn=!0,on=setTimeout(function(){rn=!1},60)}};we("History",{publicMethods:{initHistory:function(){if(o.extend(r,hn,!0),r.history){mn=window.location,pn=!1,un=!1,dn=!1,cn=gn(),fn="pushState"in history,cn.indexOf("gid=")>-1&&(cn=(cn=cn.split("&gid=")[0]).split("?gid=")[0]),xe("afterChange",a.updateURL),xe("unbindEvents",function(){o.unbind(window,"hashchange",a.onHashChange)});var e=function(){ln=!0,un||(pn?history.back():cn?mn.hash=cn:fn?history.pushState("",document.title,mn.pathname+mn.search):mn.hash=""),vn()};xe("unbindEvents",function(){c&&e()}),xe("destroy",function(){ln||e()}),xe("firstUpdate",function(){d=bn().pid});var t=cn.indexOf("pid=");t>-1&&"&"===(cn=cn.substring(0,t)).slice(-1)&&(cn=cn.slice(0,-1)),setTimeout(function(){s&&o.bind(window,"hashchange",a.onHashChange)},40)}},onHashChange:function(){return gn()===cn?(un=!0,void a.close()):void(rn||(sn=!0,a.goTo(bn().pid),sn=!1))},updateURL:function(){vn(),sn||(dn?nn=setTimeout(yn,800):yn())}}}),o.extend(a,Xe)}}),function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipeUI_Default=t()}(this,function(){"use strict";return function(e,t){var n,i,o,a,r,s,l,c,d,u,p,m,f,h,g,v,b,y,E=this,w=!1,T=!0,S=!0,x={barsSize:{top:44,bottom:"auto"},closeElClasses:["item","caption","zoom-wrap","ui","top-bar"],timeToIdle:4e3,timeToIdleOutside:1e3,loadingIndicatorDelay:1e3,addCaptionHTMLFn:function(e,t){return e.title?(t.children[0].innerHTML=e.title,!0):(t.children[0].innerHTML="",!1)},closeEl:!0,captionEl:!0,fullscreenEl:!0,zoomEl:!0,shareEl:!0,counterEl:!0,arrowEl:!0,preloaderEl:!0,tapToClose:!1,tapToToggleControls:!0,clickToCloseNonZoomable:!0,shareButtons:[{id:"facebook",label:"Share on Facebook",url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"},{id:"twitter",label:"Tweet",url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:"Pin it",url:"http://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"},{id:"download",label:"Download image",url:"{{raw_image_url}}",download:!0}],getImageURLForShare:function(){return e.currItem.src||""},getPageURLForShare:function(){return window.location.href},getTextForShare:function(){return e.currItem.title||""},indexIndicatorSep:" / ",fitControlsWidth:1200},C=function(e){if(v)return!0;e=e||window.event,g.timeToIdle&&g.mouseUsed&&!d&&O();for(var n,i,o=(e.target||e.srcElement).getAttribute("class")||"",a=0;a<F.length;a++)(n=F[a]).onTap&&o.indexOf("pswp__"+n.name)>-1&&(n.onTap(),i=!0);if(i){e.stopPropagation&&e.stopPropagation(),v=!0;var r=t.features.isOldAndroid?600:30;setTimeout(function(){v=!1},r)}},_=function(){return!e.likelyTouchDevice||g.mouseUsed||screen.width>g.fitControlsWidth},P=function(e,n,i){t[(i?"add":"remove")+"Class"](e,"pswp__"+n)},B=function(){var e=1===g.getNumItemsFn();e!==h&&(P(i,"ui--one-slide",e),h=e)},k=function(){P(l,"share-modal--hidden",S)},I=function(){return(S=!S)?(t.removeClass(l,"pswp__share-modal--fade-in"),setTimeout(function(){S&&k()},300)):(k(),setTimeout(function(){S||t.addClass(l,"pswp__share-modal--fade-in")},30)),S||A(),!1},D=function(t){var n=(t=t||window.event).target||t.srcElement;return e.shout("shareLinkClick",t,n),!(!n.href||!n.hasAttribute("download")&&(window.open(n.href,"pswp_share","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,top=100,left="+(window.screen?Math.round(screen.width/2-275):100)),S||I(),1))},A=function(){for(var e,t,n,i,o="",a=0;a<g.shareButtons.length;a++)e=g.shareButtons[a],t=g.getImageURLForShare(e),n=g.getPageURLForShare(e),i=g.getTextForShare(e),o+='<a href="'+e.url.replace("{{url}}",encodeURIComponent(n)).replace("{{image_url}}",encodeURIComponent(t)).replace("{{raw_image_url}}",t).replace("{{text}}",encodeURIComponent(i))+'" target="_blank" class="pswp__share--'+e.id+'"'+(e.download?"download":"")+">"+e.label+"</a>",g.parseShareButtonOut&&(o=g.parseShareButtonOut(e,o));l.children[0].innerHTML=o,l.children[0].onclick=D},M=function(e){for(var n=0;n<g.closeElClasses.length;n++)if(t.hasClass(e,"pswp__"+g.closeElClasses[n]))return!0},R=0,O=function(){clearTimeout(y),R=0,d&&E.setIdle(!1)},N=function(e){var t=(e=e||window.event).relatedTarget||e.toElement;t&&"HTML"!==t.nodeName||(clearTimeout(y),y=setTimeout(function(){E.setIdle(!0)},g.timeToIdleOutside))},$=function(e){m!==e&&(P(p,"preloader--active",!e),m=e)},L=function(e){var n=e.vGap;if(_()){var r=g.barsSize;if(g.captionEl&&"auto"===r.bottom)if(a||((a=t.createEl("pswp__caption pswp__caption--fake")).appendChild(t.createEl("pswp__caption__center")),i.insertBefore(a,o),t.addClass(i,"pswp__ui--fit")),g.addCaptionHTMLFn(e,a,!0)){var s=a.clientHeight;n.bottom=parseInt(s,10)||44}else n.bottom=r.top;else n.bottom="auto"===r.bottom?0:r.bottom;n.top=r.top}else n.top=n.bottom=0},F=[{name:"caption",option:"captionEl",onInit:function(e){o=e}},{name:"share-modal",option:"shareEl",onInit:function(e){l=e},onTap:function(){I()}},{name:"button--share",option:"shareEl",onInit:function(e){s=e},onTap:function(){I()}},{name:"button--zoom",option:"zoomEl",onTap:e.toggleDesktopZoom},{name:"counter",option:"counterEl",onInit:function(e){r=e}},{name:"button--close",option:"closeEl",onTap:e.close},{name:"button--arrow--left",option:"arrowEl",onTap:e.prev},{name:"button--arrow--right",option:"arrowEl",onTap:e.next},{name:"button--fs",option:"fullscreenEl",onTap:function(){n.isFullscreen()?n.exit():n.enter()}},{name:"preloader",option:"preloaderEl",onInit:function(e){p=e}}];E.init=function(){t.extend(e.options,x,!0),g=e.options,i=t.getChildByClass(e.scrollWrap,"pswp__ui"),u=e.listen,function(){var e;u("onVerticalDrag",function(e){T&&e<.95?E.hideControls():!T&&e>=.95&&E.showControls()}),u("onPinchClose",function(t){T&&t<.9?(E.hideControls(),e=!0):e&&!T&&t>.9&&E.showControls()}),u("zoomGestureEnded",function(){(e=!1)&&!T&&E.showControls()})}(),u("beforeChange",E.update),u("doubleTap",function(t){var n=e.currItem.initialZoomLevel;e.getZoomLevel()!==n?e.zoomTo(n,t,333):e.zoomTo(g.getDoubleTapZoom(!1,e.currItem),t,333)}),u("preventDragEvent",function(e,t,n){var i=e.target||e.srcElement;i&&i.getAttribute("class")&&e.type.indexOf("mouse")>-1&&(i.getAttribute("class").indexOf("__caption")>0||/(SMALL|STRONG|EM)/i.test(i.tagName))&&(n.prevent=!1)}),u("bindEvents",function(){t.bind(i,"pswpTap click",C),t.bind(e.scrollWrap,"pswpTap",E.onGlobalTap),e.likelyTouchDevice||t.bind(e.scrollWrap,"mouseover",E.onMouseOver)}),u("unbindEvents",function(){S||I(),b&&clearInterval(b),t.unbind(document,"mouseout",N),t.unbind(document,"mousemove",O),t.unbind(i,"pswpTap click",C),t.unbind(e.scrollWrap,"pswpTap",E.onGlobalTap),t.unbind(e.scrollWrap,"mouseover",E.onMouseOver),n&&(t.unbind(document,n.eventK,E.updateFullscreen),n.isFullscreen()&&(g.hideAnimationDuration=0,n.exit()),n=null)}),u("destroy",function(){g.captionEl&&(a&&i.removeChild(a),t.removeClass(o,"pswp__caption--empty")),l&&(l.children[0].onclick=null),t.removeClass(i,"pswp__ui--over-close"),t.addClass(i,"pswp__ui--hidden"),E.setIdle(!1)}),g.showAnimationDuration||t.removeClass(i,"pswp__ui--hidden"),u("initialZoomIn",function(){g.showAnimationDuration&&t.removeClass(i,"pswp__ui--hidden")}),u("initialZoomOut",function(){t.addClass(i,"pswp__ui--hidden")}),u("parseVerticalMargin",L),function(){var e,n,o,a=function(i){if(i)for(var a=i.length,r=0;r<a;r++){e=i[r],n=e.className;for(var s=0;s<F.length;s++)o=F[s],n.indexOf("pswp__"+o.name)>-1&&(g[o.option]?(t.removeClass(e,"pswp__element--disabled"),o.onInit&&o.onInit(e)):t.addClass(e,"pswp__element--disabled"))}};a(i.children);var r=t.getChildByClass(i,"pswp__top-bar");r&&a(r.children)}(),g.shareEl&&s&&l&&(S=!0),B(),g.timeToIdle&&u("mouseUsed",function(){t.bind(document,"mousemove",O),t.bind(document,"mouseout",N),b=setInterval(function(){2==++R&&E.setIdle(!0)},g.timeToIdle/2)}),g.fullscreenEl&&!t.features.isOldAndroid&&(n||(n=E.getFullscreenAPI()),n?(t.bind(document,n.eventK,E.updateFullscreen),E.updateFullscreen(),t.addClass(e.template,"pswp--supports-fs")):t.removeClass(e.template,"pswp--supports-fs")),g.preloaderEl&&($(!0),u("beforeChange",function(){clearTimeout(f),f=setTimeout(function(){e.currItem&&e.currItem.loading?(!e.allowProgressiveImg()||e.currItem.img&&!e.currItem.img.naturalWidth)&&$(!1):$(!0)},g.loadingIndicatorDelay)}),u("imageLoadComplete",function(t,n){e.currItem===n&&$(!0)}))},E.setIdle=function(e){d=e,P(i,"ui--idle",e)},E.update=function(){T&&e.currItem?(E.updateIndexIndicator(),g.captionEl&&(g.addCaptionHTMLFn(e.currItem,o),P(o,"caption--empty",!e.currItem.title)),w=!0):w=!1,S||I(),B()},E.updateFullscreen=function(i){i&&setTimeout(function(){e.setScrollOffset(0,t.getScrollY())},50),t[(n.isFullscreen()?"add":"remove")+"Class"](e.template,"pswp--fs")},E.updateIndexIndicator=function(){g.counterEl&&(r.innerHTML=e.getCurrentIndex()+1+g.indexIndicatorSep+g.getNumItemsFn())},E.onGlobalTap=function(n){var i=(n=n||window.event).target||n.srcElement;if(!v)if(n.detail&&"mouse"===n.detail.pointerType){if(M(i))return void e.close();t.hasClass(i,"pswp__img")&&(1===e.getZoomLevel()&&e.getZoomLevel()<=e.currItem.fitRatio?g.clickToCloseNonZoomable&&e.close():e.toggleDesktopZoom(n.detail.releasePoint))}else if(g.tapToToggleControls&&(T?E.hideControls():E.showControls()),g.tapToClose&&(t.hasClass(i,"pswp__img")||M(i)))return void e.close()},E.onMouseOver=function(e){var t=(e=e||window.event).target||e.srcElement;P(i,"ui--over-close",M(t))},E.hideControls=function(){t.addClass(i,"pswp__ui--hidden"),T=!1},E.showControls=function(){T=!0,w||E.update(),t.removeClass(i,"pswp__ui--hidden")},E.supportsFullscreen=function(){var e=document;return!!(e.exitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen||e.msExitFullscreen)},E.getFullscreenAPI=function(){var t,n=document.documentElement,i="fullscreenchange";return n.requestFullscreen?t={enterK:"requestFullscreen",exitK:"exitFullscreen",elementK:"fullscreenElement",eventK:i}:n.mozRequestFullScreen?t={enterK:"mozRequestFullScreen",exitK:"mozCancelFullScreen",elementK:"mozFullScreenElement",eventK:"moz"+i}:n.webkitRequestFullscreen?t={enterK:"webkitRequestFullscreen",exitK:"webkitExitFullscreen",elementK:"webkitFullscreenElement",eventK:"webkit"+i}:n.msRequestFullscreen&&(t={enterK:"msRequestFullscreen",exitK:"msExitFullscreen",elementK:"msFullscreenElement",eventK:"MSFullscreenChange"}),t&&(t.enter=function(){return c=g.closeOnScroll,g.closeOnScroll=!1,"webkitRequestFullscreen"!==this.enterK?e.template[this.enterK]():void e.template[this.enterK](Element.ALLOW_KEYBOARD_INPUT)},t.exit=function(){return g.closeOnScroll=c,document[this.exitK]()},t.isFullscreen=function(){return document[this.elementK]}),t}}}),function(e){"use strict";var t=e(window),n=e(document),i=t.width(),o=e("html"),a=e("body"),r=i<768,s=window.BEEstrings,l=e(".bee-close-overlay"),c="bee-lock-scroll",d="[data-bee-scroll-me]",u=BEErequest.page_type,p=BEEroutes.search_url,m=BEEconfigs.platform_email,f=BEEconfigs.enableConfetti,h=BEEThemeSP.cacheNameFirst,g="change:ajaxCart",v="top-start, top, top-end, left-start, left, left-end, right-start, right, right-end, bottom-start, bottom, bottom-end".split(", "),b={left:"right","left-start":"right-start","left-end":"right-end",right:"left","right-start":"left-start","right-end":"left-end"},y=function(e){return isThemeRTL&&b[e]||e};function E(e,t){return JSON.parse(e||t||"{}")}BEEThemeSP.Tooltip=function(){var t='<div class="bee-tooltip" id="id_nt_tt" role="tooltip"><div class="bee-tt-arrow"></div><div class="bee-tooltip-inner">nt_txt_tt</div></div>',n="is--show",i=function(e){var t=e.find(".bee-text-pr").text()||e.text();return e.attr("title")&&"string"!=typeof e.attr("data-original-title")&&(t=e.attr("title"),e.attr("data-original-title",e.attr("title")||"").attr("title","")),e.attr("data-bee-tooltip")?t=e.attr("data-bee-tooltip"):e.attr("data-original-title")&&(t=e.attr("data-original-title")),t};function o(i,o,a){var r=e(i),s=function(e){do{e+=~~(1e6*Math.random())}while(document.getElementById(e));return e}("tooltipbee");r.attr("aria-describedby",s),function(e,n){BEEThemeSP.$appendComponent.after(t.replace("nt_txt_tt",e).replace("id_nt_tt",n))}(a,s);var l=e("#"+s);!function(e,t,n,i){fastdomBee.mutate(function(){FloatingUIBeeDOM.computePosition(e,t,{placement:i,middleware:[FloatingUIBeeDOM.offset(6),FloatingUIBeeDOM.flip({fallbackPlacements:["top","bottom"]}),FloatingUIBeeDOM.shift({padding:5}),FloatingUIBeeDOM.arrow({element:n})]}).then(({x:e,y:i,placement:o,middlewareData:a})=>{Object.assign(t.style,{top:"0",left:"0",transform:`translate3d(${Math.round(e)}px,${Math.round(i)}px,0)`});const{x:r,y:s}=a.arrow,l={top:"bottom",right:"left",bottom:"top",left:"right"}[o.split("-")[0]];Object.assign(n.style,{left:null!=r?`${r}px`:"",top:null!=s?`${s}px`:"",right:"",bottom:"",[l]:"-4px"})})})}(i,l[0],l.find(".bee-tt-arrow")[0],o),l.addClass(n)}function r(t){var i=e("#"+e(t).attr("aria-describedby"));i.removeClass(n),i.remove()}return a.on("bee:hideTooltip",function(){e(".bee-tooltip.is--show").remove()}),function(t){if(!BEEThemeSP.isTouch){var n=e("[data-tooltip]:not(.bee-tooltip-actived)");0!=n.length&&n.hoverIntent({sensitivity:6,interval:80,timeout:100,over:function(t){let n=e(this),a=n.attr("data-tooltip")||"nt94";v.indexOf(a)<0||(o(this,y(a),i(n)),n.on("updateTooltip",function(){r(this),o(this,y(a),i(n))}),n.on("destroyTooltip",function(){r(this)}))},out:function(t){var n=e(this).attr("data-tooltip")||"nt94";v.indexOf(n)<0||(r(this),e(this).off("updateTooltip").off("destroyTooltip"))}}).addClass("bee-tooltip-actived")}}}(),BEEThemeSP.LookBook=function(){var i={loading:"is--loading",loaded:"is--loaded",clicked:"is--clicked",selected:"is--selected",opened:"is--opened",preload:"is--preLoaded",visible:"is--visible is--pindop"},o=[],s=!1;function u(t,n,a=!1){if(t.hasClass(i.loaded))a&&m(t,n);else{var r=t.data("sid"),l=o[n+r];if(!t.is("[data-is-pr]"))return t.addClass(i.loaded),l=e("#tem"+n).html(),BEEThemeSP.$appendComponent.after(l.replace('id=""','id="'+n+'"')),void(a&&m(t,n));l?(BEEThemeSP.$appendComponent.after(l),t.addClass(i.loaded),BEEThemeSP.ProductItem.init(),BEEThemeSP.Tooltip(),a&&m(t,n)):(t.addClass(i.loading),fetch(t.data("href")+"/?section_id="+r).then(function(e){return e.text()}).then(function(e){e=(e=e.split("[beeplitlz]")[1]).replace("id_nt_bee",n),BEEThemeSP.$appendComponent.after(e),t.removeClass(i.loading).addClass(i.loaded),BEEThemeSP.ProductItem.init(),BEEThemeSP.Tooltip(),a&&m(t,n),s&&(o[n+r]=e)}).catch(function(e){t.removeClass(i.loading),console.log(e)}))}}function p(){r&&(a.removeClass(c),BEEThemeSP.Helpers.disableBodyScroll(!1,d)),e("[data-pin-close],[data-pin-popup]."+i.clicked).off("click.closelb"),n.off("click.closelb").off("keyup.closelb"),e("[data-pin-wrapper]."+i.opened).removeClass(i.opened),e("[data-pin-popup]."+i.clicked).removeClass(i.clicked),a.hasClass("is--opend-drawer")?l.removeClass("is--pindop"):l.removeClass(i.visible)}function m(o,s,u="lb"){!function(t){r&&(a.addClass(c),BEEThemeSP.Helpers.disableBodyScroll(!0,d));let n=e("#"+t);n.addClass(i.opened),n.hasClass("is-style-mb--false")||l.addClass(i.visible)}(s);var m=e("#"+s),f=m[0],g=m.find(".bee-"+u+"-arrow"),v=o.data("position")||"top";(t.width()>767||m.hasClass("is-style-mb--false"))&&function(e,t,n,i){fastdomBee.mutate(function(){FloatingUIBeeDOM.computePosition(e,t,{placement:i,middleware:[FloatingUIBeeDOM.offset(12),FloatingUIBeeDOM.flip({fallbackPlacements:["top","bottom"]}),FloatingUIBeeDOM.shift({padding:0}),FloatingUIBeeDOM.arrow({element:n})]}).then(({x:e,y:i,placement:o,middlewareData:a})=>{Object.assign(t.style,{top:"0",left:"0",transform:`translate3d(${Math.round(e)}px,${Math.round(i)}px,0)`});const{x:r,y:s}=a.arrow,l={top:"bottom",right:"left",bottom:"top",left:"right"}[o.split("-")[0]];Object.assign(n.style,{left:null!=r?`${r}px`:"",top:null!=s?`${s}px`:"",right:"",bottom:"",[l]:"-6px"})})})}(o[0],f,g[0],y(v)),"lb"==u?(n.on("click.closelb",function(t){var n=e(t.target);n.closest("[data-pin-wrapper]").length>0||n.is("[data-pin-popup]")||p()}),e(`#${s} [data-pin-close], [data-pin-popup].${i.clicked}`).on("click.closelb",function(e){e.preventDefault(),e.stopPropagation(),p()}),n.on("keyup.closelb",function(e){27===e.keyCode&&p()})):(n.on("click.closeDrop",function(t){var n=e(t.target);n.closest("[data-dropdown-wrapper]").length>0||n.is("[data-dropdown-open]")||n.closest("[data-dropdown-open]").length>0||h()}),e(`#${s} [data-dropdown-close], [data-dropdown-open].${i.clicked}`).on("click.closeDrop",function(e){e.preventDefault(),e.stopPropagation(),h()}),n.on("keyup.closeDrop",function(e){27===e.keyCode&&h()}))}function f(){n.on("click","[data-pin-popup]:not(."+i.clicked+")",function(t){t.preventDefault();var n=e(this),o=n.data("bid");p(),n.addClass(i.clicked),u(n,o,!0)}),e("[data-pin-popup][data-is-pr]:not(."+i.clicked+"):not(."+i.opened+")").on("mouseenter.pin",function(t){var n=e(this),o=n.data("bid");n.addClass(i.preload),u(n,o),n.off("touchstart.pin mouseenter.pin")})}function h(){a.removeClass(c),BEEThemeSP.Helpers.disableBodyScroll(!1,d),e("[data-dropdown-close],[data-dropdown-open]."+i.clicked).off("click.closeDrop"),n.off("click.closeDrop").off("keyup.closeDrop"),e("[data-dropdown-wrapper]."+i.opened).removeClass(i.opened),e("[data-dropdown-open]."+i.clicked).removeClass(i.clicked),a.hasClass("is--opend-drawer")?l.removeClass("is--pindop"):l.removeClass(i.visible)}function g(){n.on("click","[data-dropdown-open]:not(."+i.clicked+")",function(t){t.preventDefault();var n=e(this),o=n.data("id");h(),n.addClass(i.clicked),m(n,o,"drop"),function(t){e("#"+t).on("click.dopText","[data-dropdown-item]",function(n){n.preventDefault();var o=e(this);o.parents().find("."+i.selected).removeClass(i.selected),o.addClass(i.selected),e('[data-dropdown-open][data-id="'+t+'"]>span:not([data-not-change-txt])').text(o.text()),e("#"+t).off("click.dopText"),o.trigger("change:drop",[o,o.data("value")]),h()})}(o)}),n.on("click","[data-dropdown-off]",function(t){var n=e(this);e(this).closest("[data-dropdown-wrapper]").find("[data-dropdown-open]>span").text(n.text()),h()}),n.on("dropdown:bee:close",function(e){h()})}return function(){f(),g()}}(),BEEThemeSP.Hover=function(){var t=e("[data-hover-bee]");if(!(0==t.length||i<1025)){t.each(function(t,n){var i=this,o=e(i);i.ishasGroup=i.hasAttribute("data-has-group"),o.hoverIntent({sensitivity:3,interval:o.data("interval")||35,timeout:o.data("timeout")||150,over:function(e){i.ishasGroup?(o.siblings().removeClass("is--hover"),o.addClass("is--hover")):o.addClass("is--hover")},out:function(){i.ishasGroup||o.removeClass("is--hover")}})})}},BEEThemeSP.ProductItem=function(){var t=e("#btns_pr_temp").html(),i="data-tooltip",r=i+'="',s="id_nt_94",l="handle_nt_94",c=BEEconfigs,d=BEEProductStrings,u=BEEconfigs.img2,p=c.nowTimestamp,m=c.new_day_int,f=c.show_img,h=c.enable_quickshop,g=c.use_sale_badge,v=c.label_sale_style,b=c.use_preorder_badge,y=c.use_new_badge,w=c.use_soldout_badge,T=c.use_custom_badge,S=c.swatch_limit,x=c.swatch_click,C=c.swatch_num,_=2,P=d.preOrder,B=d.readMore,k=(d.soldOut,d.selectOption),I=d.quickShop,D=d.preView,A=".bee-text-pr",M=".bee-svg-pr-icon use",R="data-color-options",O=".bee-product-sizes",N="is--loading",$="is-bee--opended",L="is-bee--calced",F="is-bee--limit",U="[data-bee-resizeobserver]",H=c.sw_item_style,W=d.swatch_limit,j=d.swatch_limit_less,z=c.show_qty,G='.bee-pr-color__item:not(.is-swatch--current):not([data-img="none"]):not(.is--colors-more)',Z=c.pr_curent,q=BEEconfigs.app_review,K={sale:d.badgeSale,new:d.badgeNew,preOrder:d.badgepreOrder,soldout:d.badgeSoldout,SavePercent:d.badgeSavePercent},V="initProducts__enabled";function Y(t){if(S){var n=t?"["+R+"]":"["+R+"]:not(."+L+")";C?fastdomBee.measure(function(){e(n).each(function(t){var n=e(this),i=n.find(".bee-pr-color__item").length-1,o=i-C;fastdomBee.mutate(function(){n.addClass(L).removeClass(F),n.find(".is-color--limit").removeClass("is-color--limit"),o>0&&(n.addClass(F),n.find(".bee-pr-color__item").eq(C-1).addClass("is-color--limit"),n.attr("data-limit",i).attr("style",'--text : "+'+o+'";--text2 : "-'+o+'"'))})})}):fastdomBee.measure(function(){e(n).each(function(t){var n=e(this),i=n.find(".bee-pr-color__item"),o=i.outerWidth(!0),a=i.length-1,r=Math.floor(n.outerWidth()/o),s=a-r;fastdomBee.mutate(function(){n.addClass(L).removeClass(F),n.find(".is-color--limit").removeClass("is-color--limit"),s>0&&s!=a&&(s+=1,n.addClass(F),n.find(".bee-pr-color__item").eq(r-2).addClass("is-color--limit"),n.attr("data-limit",r).attr("style",'--text : "+'+s+'";--text2 : "-'+s+'"'))})})})}}function X(t){var n=t.closest("[data-product-options]"),i=n.data("product-options");if("0"==t.data("imgid")){var o,a,r=t.data("img"),s=(t.data("ratio"),t.data("vid")),l=n.find("[data-pr-img]"),c=n.find("[data-pr-href]"),d=c.attr("href");if(t.closest("[data-color-options]").find(".is-swatch--selected").removeClass("is-swatch--selected"),t.addClass("is-swatch--selected"),n.addClass("bee-colors-selected"),l.attr("data-srcset",r),"1"!=Z&&void 0!==s)if(c.attr("href",(a=s,/variant=/.test(o=d)?o.replace(/(variant=)[^&]+/,"$1"+a):/\?/.test(o)?o.concat("&variant=").concat(a):o.concat("?variant=").concat(a))),void 0!==BeeFunc.psjson_lib[i.id]){let t=n.find(O),o=t.find(">span"),a=BeeFunc.psjson_lib[i.id].variants,r=a.find(e=>e.id===s),l=a.filter(e=>e[i.index_color]===r[i.index_color]);o.removeClass("bee-product-sizes--sold-out");let c=0;e.map(l,function(e,t){e.available?++c:o.eq(t).addClass("bee-product-sizes--sold-out")}),t.attr("data-size",c)}}else{let e=n.find(".flickitybee-enabled[data-product-img-carousel]");if(0==e.length)return;let i=t.data("imgid");t.data("isWrapped"),t.data("isInstant");e.flickitybee("select",e.find(`[data-product-img-slide="${i}"]`).index())}}S&&(o.addClass("bee-pr-item-sw-limit"),C&&C>0&&(_=C));var J,Q,ee="data-collection-url",te="[data-collection-url]",ne="is--href-replaced",ie="[data-pr-href]:not(.is--href-replaced)",oe="/products/";function ae(){BEEconfigs.within_cat&&0!=e(ie).length&&e(te).each(function(){J=e(this).attr(ee),e.trim(J).length<1||e(this).find(ie).each(function(){Q=(Q=e(this).attr("href")).split(oe)[1],Q=oe+Q,e(this).attr("href",J+Q).addClass(ne)})})}var re=[];return{init:function(){ae(),e("[data-product-options]:not(.is-bee-pr-created)").each(function(){var n=e(this),o=E(n.attr("data-product-options")),a=n.find("[data-pr-href]").attr("href");"boolean"!=typeof o.unQuickShopInline&&(o.unQuickShopInline=!0),function(e,t,n){var i="";"2"==f&&t&&(i=u.replace("image_src",t).replace("image_alt",n),e.find(".bee-product-img").addClass("is-show-img2")),e.find("[data-replace-img2]").replaceWith(i)}(n,o.image2,o.alt),function(n,o,a){var c=n.find("[data-replace-quickview]"),d=c.attr(i)||"",u=o.id,p=n.find("[data-replace-compare]"),m=p.attr(i)||"",f=n.find("[data-replace-wishlist]"),g=f.attr(i)||"",v=n.find("[data-replace-atc]"),b=v.is("[data-has-qty]"),y=v.attr(i)||"",E=t.replace(/#bee_pr_url/g,a).split("[split_beent]");c.each(function(t){d=e(this).attr(i)||"",e(this).replaceWith(E[0].replace(r,r+d).replace(s,u))}),p.each(function(t){m=e(this).attr(i)||"",e(this).replaceWith(E[1].replace(r,r+m).replace(s,u).replace(l,o.handle))}),f.each(function(t){g=e(this).attr(i)||"",e(this).replaceWith(E[2].replace(r,r+g).replace(s,u).replace(l,o.handle))}),v.each(function(t){y=v.attr(i)||"",e(this).replaceWith(E[3].replace(r,r+y).replace(s,u))});var w=(v=n.find("[data-atc-selector]")).find(A),T=v.find(M);if(o.isExternal)v.attr("href",o.external_link).attr("target","_blank"),w.text(o.external_title),T.attr("xlink:href","#bee-icon-external");else if(o.available)if(o.isGrouped)w.text(D);else if(o.isDefault){if(o.isPreoder&&w.text(P),v.attr({"data-action-atc":"","data-variant-id":o.VariantFirstID,"data-qty":o.cusQty||1}),z&&b&&v[0]){var S=v[0].outerHTML,x=E[4].replace('max="9999"',`max="${o.maxQuantity}"`).replace('min="1"',`min="${o.cusQty||1}"`);v.replaceWith('<div class="bee-product-atc-qty">'+x+S+"</div>")}}else h&&o.unQuickShopInline?(v.attr("data-action-quickshop",""),w.text(I)):w.text(k);else w.text(B),T.attr("xlink:href","#bee-icon-link");o.unQuickShopInline||n.one("replace:btnAtc",function(){if(v.attr({"data-action-atc":"","data-variant-id":o.VariantFirstID,"data-qty":o.cusQty||1}),z&&b&&v[0]){var e=v[0].outerHTML,t=E[4].replace('max="9999"',`max="${o.maxQuantity}"`).replace('min="1"',`min="${o.cusQty||1}"`);v.replaceWith('<div class="bee-product-atc-qty">'+t+e+"</div>")}})}(n,o,a),function(e,t){var n=e.find("[data-product-badge]"),i=(n.attr("data-sort")||"").replace(/ /g,"").split(","),o="",a=!t.unQuickShopInline;if(0!=i.length&&0!=n.length){for(var r=i.length,s=0;s<r;s++)switch(i[s]){case"sale":var l=t.compare_at_price,c=t.price;if(l<=c||!g){a&&(o+='<span data-badge-sale class="bee-badge-item bee-badge-sale" hidden></span>');break}if("2"==v)var d=100*(l-c)/l,u=K.SavePercent.replace("[sale]",Math.round(d));else if("3"==v){var f=l-c;u=BEEThemeSP.Currency.formatMoney(f)}else u=K[i[s]];o+='<span data-badge-sale class="bee-badge-item bee-badge-sale">'+u+"</span>";break;case"preOrder":if(!t.isPreoder||!b){a&&(o+='<span data-badge-preorder class="bee-badge-item bee-badge-preorder" hidden>'+K[i[s]]+"</span>");break}o+='<span data-badge-preorder class="bee-badge-item bee-badge-preorder">'+K[i[s]]+"</span>";break;case"new":var h=p-t.dateStart,E=Math.floor(h/3600);if((E=Math.floor(E/24))>=m||!y)break;o+='<span class="bee-badge-item bee-badge-new">'+K[i[s]]+"</span>";break;case"soldout":if(t.available||!w){a&&(o+='<span data-badge-soldout class="bee-badge-item bee-badge-soldout" hidden>'+K[i[s]]+"</span>");break}o+='<span data-badge-soldout class="bee-badge-item bee-badge-soldout">'+K[i[s]]+"</span>";break;default:var S=t.customBadge;if(!S||!T)break;for(var x=S.length,C=0;C<x;C++)o+='<span class="bee-badge-item bee-badge-custom bee-badge-'+t.customBadgeHandle[C]+'">'+S[C]+"</span>"}n.html(o)}}(n,o),function(t,n){var i=t.find("["+R+"]");if(0!=i.length){for(var o=E(i.attr(R)),a=o.color_variants,r=o.color_variants_avai,s=o.color_variants_handle,l=o.img_options,c=o.img_variants,d=o.id_variants,u=o.id_images||[],p=o.img_ratios,m="",f=c.length>0,h=a.length,g=h!=r.length,v=0;v<h;v++){var b,y=a[v],w=l.indexOf(y),T="nt94"!=(b=(b=f?c[w]:"nt94")||"nt94")?BEEThemeSP.Images.lazyloadImagePath(b):"none",x="nt94"!=b&&"2"==H?'data-bg="'+BEEThemeSP.Images.getNewImageUrl(b,100)+'"':"",C=g&&r.indexOf(y)<0?" bee-pr-color--sold-out":"";m+='<span data-imgid="'+(u[w]||"0")+'" class="bee-pr-color__item'+C+'" data-vid="'+d[w]+'" data-tooltip="top" data-img="'+T+'" data-ratio="'+(p[w]||"")+'"><span class="bee-pr-color__name">'+y+'</span><span class="bee-pr-color__value bg_color_'+s[v]+' lazyloadbee" '+x+"></span></span>"}if(h>_&&S&&(m+='<span class="bee-pr-color__item is--colors-more" data-tooltip="top-end"><span class="bee-pr-color__name">'+W+'</span><a href="'+n+'" class="bee-pr-color__value bg_color_limibee"></a></span>'),i.html(m),0!=t.find(O).length){var P=t.closest("[data-product-options]").data("product-options");null==BeeFunc.psjson_lib[P.id]&&e.ajax({url:Shopify.routes.root+"products/"+P.handle+".js",type:"GET",dataType:"json"}).done(function(e){BeeFunc.psjson_lib[P.id]=e}).fail(function(){}).always(function(e){})}}}(n,a),Y(),function(e,t,n){if(!(n.unQuickShopInline||t.hasClass(V)||n.isGrouped||n.isExternal)){var i=t.find("[data-qs-inl]");i.hasClass("lazyloadbeeed")?new BEEThemeSP.Product(e):i.one("lazyincluded",function(){new BEEThemeSP.Product(e)})}}(this,n,o),n.addClass("is-bee-pr-created")}),BEEThemeSP.Tooltip(),BEEThemeSP.Compare.updateAll(),BEEThemeSP.Wishlist.updateAll()},initQuickVS:function(){function t(){var t=e(".bee-product-quick-view"),n=t.find("[data-product-featured]:not(."+V+")");n.addClass(V),new BEEThemeSP.Product(n[0]);var i=t.find("[data-main-media]");i.hasClass("flickitybee")&&!i.hasClass("flickitybee-enabled")&&(i[0].flickitybee=new BEEThemeSP.Carousel(i[0])),BEEThemeSP.PopupMFP(),BEEThemeSP.initGroupsProduct(),window.Shopify&&Shopify.PaymentButton&&Shopify.PaymentButton.init(),BEEThemeSP.Wishlist.updateAll(),BEEThemeSP.Compare.updateAll(),BEEThemeSP.ProductItem.reloadReview(),BEEThemeSP.Tooltip(),a.trigger("currency:update")}function i(){var t=e(".bee-product-quick-shop:not(."+V+")");t.addClass(V),new BEEThemeSP.Product(t[0]),BEEThemeSP.PopupMFP(),window.Shopify&&Shopify.PaymentButton&&Shopify.PaymentButton.init(),BEEThemeSP.Wishlist.updateAll(),BEEThemeSP.Compare.updateAll(),BEEThemeSP.ProductItem.reloadReview(),BEEThemeSP.Tooltip(),a.trigger("currency:update")}z&&o.addClass("bee-pr-item-has-qty"),a.on("click","[data-action-quickview], [data-action-quickshop]",function(o){o.preventDefault();var r=e(this);if(!r.hasClass(N)){var s=r.attr("href"),l=r.is("[data-action-quickview]"),c=l?"main-qv":"main-qs",d=l?"bee-opening-qv":"bee-opening-qs",u=r.data("id"),p=re[c+u];if(BEEThemeSP.isEditCartReplace="0"==r.data("edit"),BEEThemeSP.iDVariantEdit=u,BEEThemeSP.keyVariantEdit=r.data("key"),p)BEEThemeSP.NTpopupInline(p,c,l?t:i,d),a.trigger("modalbee:opened");else{if(BEEThemeSP.isEditCartReplace){var m=r.closest("[data-cart-item]"),f=r.closest("[data-cart-wrapper]"),h=m.find(".bee-cart-ld__bar"),g=h.find(".bee-cart-spinner");f.addClass("is--contentUpdate"),m.addClass("is--update"),h.removeAttr("hidden"),g.removeAttr("hidden"),n.on("cart:updated",function(e){f.removeClass("is--contentUpdate"),n.off("cart:updated"),m.removeClass("is--update"),h.attr("hidden",""),g.attr("hidden","")})}else r.addClass(N);fetch(function(e,t){return e+(e.indexOf("?")>-1||e.indexOf("&")>-1?"&":"/?")+t}(s,"section_id="+c)).then(function(e){return e.text()}).then(function(o){r.removeClass(N),n.trigger("cart:updated"),o=IsDesignMode?e(o).find("template").html():e(o).html(),BEEThemeSP.NTpopupInline(o,c,l?t:i,d),a.trigger("modalbee:opened"),re[c+u]=o}).catch(function(e){r.removeClass(N),n.trigger("cart:updated"),console.log(e)})}}})},recalculateSwatches:Y,clickMoreSwatches:function(){S&&"2"!=x&&a.on("click",".bee-pr-color__item.is--colors-more>a",function(t){t.preventDefault();var n=e(this).closest("."+F);n.hasClass($)?(n.removeClass($),e(this).siblings().text(W)):(n.addClass($),e(this).siblings().text(j))})},swatchesClickHover:function(){BEEThemeSP.isTouch?a.on("click",G,function(){X(e(this))}):a.hoverIntent({selector:G,sensitivity:6,interval:100,timeout:100,over:function(t){X(e(this))},out:function(){}})},resizeObserver:function(){var t=e(U+".flickitybee-enabled .bee-product:not(.bee_observered), "+U+".isotopebee-enabled .bee-product:not(.bee_observered)");if(0!=t.length&&window.ResizeObserver){var n=new ResizeObserver(function(t){t.forEach(function(t){var n,i=e(t.target),o=i.is(U)?i:i.closest(U);clearTimeout(n),o.addClass("is-bee--doing"),n=setTimeout(function(){o.hasClass("flickitybee-enabled")?o.flickitybee("resize"):o.hasClass("isotopebee-enabled")&&o.isotopebee("layout"),o.removeClass("is-bee--doing")},28)})});t.each(function(t){var i=e(this);n.observe(this),i.addClass("bee_observered"),i.one("destroy.observered",function(){n.unobserve(this),i.removeClass("bee_observered")})})}},reloadReview:function(){if("1"==q)try{window.SPR&&e(".spr-badge").length>0&&SPR.initDomEls(),SPR.loadBadges()}catch(e){}else"8"==q?"undefined"!=typeof SMARTIFYAPPS&&SMARTIFYAPPS.rv.installed&&SMARTIFYAPPS.rv.scmReviewsRate.actionCreateReviews():"6"==q&&a.trigger("reloadReview.bee")},loadjsRevew:function(){"6"==q&&$script(BEEconfigs.script12)},updateColelction:ae}}(),BEEThemeSP.ProductAjax=function(){var t={loading:"is--loading"},i={disabled:"aria-disabled"},o="disable"==BEEconfigs.cartType,r="cart"!=u?o?"cart_data":"cart_data,mini_cart":`cart_data,${window.cartBEEectionID}`,s=BEEconfigs.enableAjaxATC,l=BEEconfigs.enableAjaxCart,c=BEEroutes.cart_add_url+".js",d=BEEroutes.cart_change_url+".js",p=BEEconfigs.disATCerror,m=r.split(","),f="change.required keyup.required",h="is--field-emty is--animated bee-ani-shake";function v(e="json"){return{method:"POST",headers:{"Content-Type":"application/json",Accept:`application/${e}`}}}function b(e=!1){e&&!p&&BEEThemeSP.Notices(e)}function y(e,t,i){var o=e.closest("[data-cart-item]"),a=e.closest("[data-cart-wrapper]"),s=o.find(".bee-cart-ld__bar"),l=s.find(".bee-cart-spinner");a.addClass("is--contentUpdate"),o.addClass("is--update"),s.removeAttr("hidden"),l.removeAttr("hidden"),n.on("cart:updated",function(e){a.removeClass("is--contentUpdate"),n.off("cart:updated"),o.removeClass("is--update"),s.attr("hidden",""),l.attr("hidden","")});const c=v("javascript");c.headers["X-Requested-With"]="XMLHttpRequest",c.body=JSON.stringify({id:t,quantity:i,sections:r,sections_url:window.location.pathname}),fetch(`${d}`,c).then(e=>e.json()).then(e=>{if(e.status)return b(e.description),void n.trigger("cart:updated");E(e.sections)}).catch(e=>{n.trigger("cart:updated"),console.error(e)})}function E(e){e?BEEThemeSP.Cart.renderContents(e):BEEThemeSP.Cart.getToFetch()}function w(o){var r,l=o.find("[data-field-required]"),u=!!o.hasClass("has--properties")&&l.length>0,p=!1;o.on("click","[data-atc-form]",function(g){if(e(this).attr(i.disabled))g.preventDefault(),BEEThemeSP.Notices(BEEProductStrings.pleaseChooseOptions);else{if(u&&(g.preventDefault(),p=!1,l.each(function(){let t=e(this),n=t.closest("[data-item-property-field]");n.hasClass("is--type-radio")||n.hasClass("is--type-checkbox")?n.find("input[name]").is(":checked")||(n.addClass(h),p=!0):0==t.val().length&&(n.addClass(h),p=!0)})),p)return clearTimeout(r),r=setTimeout(function(){o.find(".is--animated.bee-ani-shake").removeClass("is--animated bee-ani-shake")},999),void l.off(f).on(f,function(t){let n=e(this),i=n.closest("[data-item-property-field]");i.hasClass("is--type-radio")||i.hasClass("is--type-checkbox")?i.find("input[name]").is(":checked")&&i.removeClass(h):n.val().length>0&&i.removeClass(h)});if(!s)return;l.off(f),g.preventDefault(),function(e){e.addClass(t.loading).attr("aria-disabled",!0),e.find(".loading-overlay__spinner").removeAttr("hidden"),e.find(".bee-svg-spinner").removeAttr("hidden"),n.on("cart:updated",function(o){e.removeClass(t.loading).removeAttr(i.disabled),e.find(".loading-overlay__spinner").attr("hidden",""),e.find(".bee-svg-spinner").attr("hidden",""),n.off("cart:updated"),a.trigger("modalbee:closed")});const o=v("javascript");o.headers["X-Requested-With"]="XMLHttpRequest",delete o.headers["Content-Type"];const r=new FormData(e.closest("form")[0]);r.append("sections",m),r.append("sections_url",window.location.pathname),o.body=r,fetch(`${c}`,o).then(e=>e.json()).then(e=>{if(e.status)return b(e.description),void n.trigger("cart:updated");if(BEEThemeSP.isEditCartReplace&&e.variant_id!=BEEThemeSP.iDVariantEdit){const e=v("javascript");e.headers["X-Requested-With"]="XMLHttpRequest",e.body=JSON.stringify({id:`${BEEThemeSP.keyVariantEdit}`,quantity:0}),fetch(`${d}`,e).then(e=>e.json()).then(e=>{e.status?b(e.description):(n.trigger("add:cart:success").trigger("add:cart:upsell"),BEEThemeSP.isATCSuccess=!0,E(e.sections))}).catch(e=>{console.error(e)})}else n.trigger("add:cart:success").trigger("add:cart:upsell"),BEEThemeSP.isATCSuccess=!0,E(e.sections)}).catch(e=>{n.trigger("cart:updated")}).finally(()=>{})}(e(this))}})}function T(){a.on("click","[data-action-atc]",function(o){s&&(o.preventDefault(),function(e){e.addClass(t.loading).attr(i.disabled,!0);const o=v("javascript");o.headers["X-Requested-With"]="XMLHttpRequest";var a=e.attr("data-variant-id"),s=parseInt(e.prev("[data-quantity-wrapper]").find("[data-quantity-value]").val())||e.data("qty")||1,l=[];l.push({id:a,quantity:s}),o.body=JSON.stringify({items:l,sections:r,sections_url:window.location.pathname}),n.on("cart:updated",function(o){e.removeClass(t.loading).removeAttr(i.disabled),n.off("cart:updated")}),fetch(`${c}`,o).then(e=>e.json()).then(e=>{if(e.status)return b(e.description),void n.trigger("cart:updated");n.trigger("add:cart:success").trigger("add:cart:upsell"),BEEThemeSP.isATCSuccess=!0,E(e.sections)}).catch(e=>{n.trigger("cart:updated"),console.error(e)})}(e(this)))})}function S(){e("[data-cart-items]").off(g).on(g,"[data-action-change]",function(t){var n=e(this),i=(n.closest("[data-cart-item]"),n.data("id")),o=n.val()||1;n.attr("max");l&&y(n,i,o)}).off("click.remove").on("click.remove","[data-cart-remove]",function(t){if(l){t.preventDefault();var n=e(this);y(n,n.data("id"),0)}})}return{init:function(){n.on("submitAtc:bee",function(e){w(e.$container.find('[data-type="add-to-cart-form"]'))}),w(e('[data-type="add-to-cart-form"]')),T(),S()},change:S}}(),BEEThemeSP.BeeQuantityAdjust=function(){var t=s.notice_stock_msg,n=BEEconfigs.disOnlyStock,i="data-current-qty";return String.prototype.getDecimals||(String.prototype.getDecimals=function(){var e=(""+this).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return e?Math.max(0,(e[1]?e[1].length:0)-(e[2]?+e[2]:0)):0}),function(){a.on("change","[data-quantity-value]",function(o){var r=e(this),s=(r.data("id"),r.val()||1),l=r.attr("max")||9999,c=r.attr("min")||1,d=r.attr(i)||.1,u=r.closest(".fgr_frm");if(u.length>0&&(subtt_price_group(u),a.trigger("currency:update")),parseInt(s)>parseInt(l)){if(r.val(l),r.attr(i,l),d!=l&&r.attr(i,l).trigger(g),n)return;return BEEThemeSP.Notices(t.replace("[max]",l)),!1}if(parseInt(s)<parseInt(c))return r.val(c),d!=c&&r.attr(i,c).trigger(g),!1;r.trigger(g),r.attr(i,s)}),a.on("click","[data-quantity-selector]",function(i){i.preventDefault();var o=e(this),a=o.closest("[data-quantity-wrapper]").find("[data-quantity-value]"),r=parseFloat(a.val()),s=parseFloat(a.attr("max")),l=parseFloat(a.attr("min")),c=a.attr("step");if(r&&""!==r&&"NaN"!==r||(r=0),""!==s&&"NaN"!==s||(s=""),""!==l&&"NaN"!==l||(l=0),"any"!==c&&""!==c&&void 0!==c&&"NaN"!==parseFloat(c)||(c=1),o.is("[data-increase-qty]")){if(s&&r>=s){if(a.val(s),n)return;return BEEThemeSP.Notices(t.replace("[max]",s)),!1}a.val((r+parseFloat(c)).toFixed(c.getDecimals()))}else l&&r<=l?a.val(l):r>0&&a.val((r-parseFloat(c)).toFixed(c.getDecimals()));a.trigger("change")})}}(),BEEThemeSP.agreeForm=function(){var t=!1,n="[data-agree-checkbox]";return function(){t||0==e(n).length||(t=!0,a.on("click",'[data-agree-btn], [name="checkout"], [name="goto_pp"], [name="goto_gc"]',function(t){var i=e(this).closest("form"),o=i.find(`[type="checkbox"]${n}`);i[0]&&0!=o.length&&(o.is(":checked")?e(this).submit():(t.preventDefault(),t.stopPropagation(),BEEThemeSP.Notices(s.agree_checkout)))}),a.on("click",n,function(t){e(this).closest("form"),e(this).is(":checked")&&a.trigger("hide.bee.notices")}))}}(),BEEThemeSP.PhotoSwipe=function(){var t=e("#photoswipe_template").html(),i="pswp__thumbnails",o="pswp_thumb_active",r=!1,l="[data-pswp-btn-triger], [data-bee-gallery--open]:not(.is-pswp-disable)",c="click.pswp",d="data-pswp-images-trigger",u=".pswp__bee";function p(t,n,i,a){if(!(t<=1)){for(var r=0;r<t;r++)i.append('<div class="pswp_thumb_item" data-index="'+(r+1)+'"><img loading="lazy" src="'+n[r].src+'" alt="pswp-thumb-img"></div>');i.find('.pswp_thumb_item[data-index="'+(a.getCurrentIndex()+1)+'"]').addClass(o),a.listen("beforeChange",function(){var e=a.getCurrentIndex()+1,t=i.find('.pswp_thumb_item[data-index="'+e+'"]');t.siblings().removeClass(o),t.addClass(o)}),a.listen("afterChange",function(){!function(t){let n=e("."+o)[0],i=t,a=i[0],r=n.getBoundingClientRect(),s=a.getBoundingClientRect();r.left+r.width>s.width?i.animate({scrollLeft:n.offsetLeft+r.width-s.width+10},200):n.offsetLeft<a.scrollLeft&&i.animate({scrollLeft:n.offsetLeft-10},200)}(i)}),i.find(".pswp_thumb_item").on("click",function(){var t=e(this).data("index");a.goTo(t-1)})}}return{gallery:function(){e("[data-bee-gallery].flickitybee-enabled").on("dragEnd.flickitybee",function(e,t){r=!0}).on("staticClick.flickitybee",function(e,t,n,i){r=!1}),n.on(c,l,function(n){n.preventDefault();var o=e(this),c=-1;if(o.is("[data-pswp-btn-triger]")){var d=o.closest("[data-bee-group-btns]")||o,m=o[0].hasAttribute("data-pr-trigger-pswp"),f=d.siblings("[data-bee-gallery]");c=function(t,n,i){return n.hasClass("flickitybee-enabled")?n.find(i+".is-selected").index():e(t.currentTarget).is(l)?0:e(t.currentTarget).index()}(n,f,h=m?'[data-media-type="image"]:not(.is--media-hide)':"[data-bee-gallery--item]"),o=f.find("[data-bee-gallery--open]").eq(c),r=!1}else var h=(m=o.hasClass("bee-product__media"))?'[data-media-type="image"]:not(.is--media-hide)':"[data-bee-gallery--item]";var g=o.parents(h),v=o.closest("[data-bee-gallery]"),b=v.find(m?'[data-media-type="image"]:not(.is--media-hide) [data-master]':"[data-pswp-src]");if(0==b.length||r)r=!1;else{var y=v.is("[data-bee-thumb-true]"),E=function(t){var n,i=[];return t.each(function(){n=e(this),i.push({src:n.attr("data-pswp-src")||n.attr("data-master"),w:n.attr("data-pswp-w")||n.attr("width"),h:n.attr("data-pswp-h")||n.attr("height")})}),i}(b),w=parseFloat(v.attr("data-maxSpreadZoom")),T=v.attr("data-fullscreenEl"),S=v.attr("data-shareEl"),x=v.attr("data-counterEl");!function(n){var o=n.items,r=o.length,l=n.index,c=n.galleryItems;isThemeRTL&&(l=r-l-1);var d={closeEl:!0,captionEl:!0,fullscreenEl:n.fullscreenEl||!0,zoomEl:!0,shareEl:n.shareEl||!0,counterEl:n.counterEl||!0,arrowEl:!0,preloaderEl:!0,history:!1,maxSpreadZoom:n.maxSpreadZoom||2,showHideOpacity:!0,bgOpacity:1,index:n.index,tapToToggleControls:!0,shareButtons:[{id:"facebook",label:s.pswp_facebook,url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"},{id:"twitter",label:s.pswp_twitter,url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:s.pswp_pinterest,url:"https://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"}],getThumbBoundsFn:function(e){var t=c.find(n.parents).eq(e);n.global&&(t=c.find("a[data-index="+e+"]").parents(n.parents));var i=window.pageYOffset||document.documentElement.scrollTop,o=t[0].getElementsByTagName("img")[0].getBoundingClientRect();return{x:o.left,y:o.top+i,w:o.width}}};a.find(u).remove(),BEEThemeSP.$appendComponent.after(t);var m,f=document.querySelectorAll(u)[0],h=e("."+i),g=new PhotoSwipe(f,PhotoSwipeUI_Default,o,d);g.init(),h.empty(),clearTimeout(m),a.trigger("NTpopupInline:offClose"),g.listen("close",function(){if(m=setTimeout(function(){a.trigger("NTpopupInline:onClose")},500),c.hasClass("flickitybee-enabled")){var e=g.getCurrentIndex();c.flickitybee("selectCell",e,!1,!0)}}),n.HasThumb&&p(r,o,h,g)}({index:c=c>-1?c:g.index(),items:E,HasThumb:y,galleryItems:v,parents:h,global:!1,maxSpreadZoom:w,fullscreenEl:T,shareEl:S,counterEl:x})}})},image:function(){n.on(c,"[data-bee-image-opend]",function(n){n.preventDefault(),function(n){a.find(u).remove(),BEEThemeSP.$appendComponent.after(t);var i=e(u),o=i[0],r=n.attr("data-pswp-class");r&&i.addClass(r);var s={history:!1,maxSpreadZoom:2,showHideOpacity:!0,fullscreenEl:!1,shareEl:!1,counterEl:!1,bgOpacity:1,getThumbBoundsFn:function(e){var t=window.pageYOffset||document.documentElement.scrollTop,i=n[0].getBoundingClientRect();return{x:i.left,y:i.top+t,w:i.width}}},l=[],c=n.attr("data-pswp-w"),d=n.attr("data-pswp-h"),p=n.attr("data-pswp-src");l.push({src:p,w:c,h:d,title:n.text()});var m,f=new PhotoSwipe(o,PhotoSwipeUI_Default,l,s);f.init(),clearTimeout(m),a.trigger("NTpopupInline:offClose"),f.listen("close",function(){m=setTimeout(function(){a.trigger("NTpopupInline:onClose")},500)})}(e(this))})},images:function(){n.on(c,`[${d}]`,function(n){n.preventDefault();let o=e(this),r=E(o.attr(d),"[]");0!=r.length&&function(n,o){if(!n)return;a.find(u).remove(),BEEThemeSP.$appendComponent.after(t);let r=n.length,s=document.querySelectorAll(u)[0],l=e("."+i),c=new PhotoSwipe(s,PhotoSwipeUI_Default,n,{history:!1,focus:!1,showAnimationDuration:0,hideAnimationDuration:0});c.init(),o.is("data-disable-thumb")||p(r,n,l,c)}(r,o)})}}}(),BEEThemeSP.Video=function(){var t="bee-postervideo-playing";function n(){e(".js-youtube").each(function(){this.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}',"*")}),e(".js-vimeo").each(function(){this.contentWindow.postMessage('{"method":"pause"}',"*")}),e("video:not(.bee_bg_vid_html5)").each(function(){this.pause()}),e(".product-model").each(function(){this.modelViewerUI&&this.modelViewerUI.pause()})}function i(e){e.target.playVideo()}return{initPoster:function(){e("[data-video-poster-btn]").on("click",function(o){o.preventDefault();var a=e(this).closest("[data-video-poster]"),r=a.find("video, iframe");n(),function(t,n){if(!t.is("[loaded]")){var o=t.find("[data-video-insert]").length?t.find("[data-video-insert]"):t,a='<iframe src="src_bee" id="id_bee" class="class_bee" title="" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen frameborder="0"></iframe>',r=JSON.parse(n.attr("data-options")||"{}"),s=r.type,l=r.vid,c=r.id,d=r.srcDefault,u=r.autoplay,p=r.loop,m="youtube",f="vimeo";if(s==m?a='<div id="'+c.replace("#","bee_yt_")+'"></div>':s==f?a=a.replace("src_bee","//player.vimeo.com/video/"+l+"?&portrait=0&byline=0&color="+r.accent_color+"&autoplay="+ +u+"&loop="+ +p).replace("class_bee","js-vimeo"):c&&e(c)[0]?a=e(c).html():(a='<video src="src_bee" preload="auto" controls data-autoplay data-loop playsinline></video>'.replace("src_bee",d),u&&(a=a.replace("data-autoplay","autoplay")),p&&(a=a.replace("data-loop","loop"))),o.append(a),t.attr("data-type-video-inline","").attr("loaded",!0),s==m){if(e("#YTAPI").length<=0){var h=document.createElement("script");h.src="https://www.youtube.com/iframe_api",h.id="YTAPI";var g=document.getElementsByTagName("script")[0];g.parentNode.insertBefore(h,g)}var v=setInterval(function(){"function"==typeof onYouTubeIframeAPIReady&&"function"==typeof YT.Player&&(new YT.Player(c.replace("#","bee_yt_"),{height:"315",width:"560",videoId:l,playerVars:{playsinline:1,rel:0,playlist:l,loop:p?1:0},events:{onReady:i}}),clearInterval(v))},100)}}}(a,e(this)),setTimeout(function(){a.addClass(t),r.focus()},50)}),e("[data-video-poster-close]").on("click",function(i){i.preventDefault(),n();var o=e(this).closest("[data-video-poster]");o.removeAttr("loaded").removeClass(t),o.find("video, iframe").remove()})}}}();var w=function(){var t,n="is--loading";function i(t){this.container=t,this.$container=e(t),this.options=JSON.parse(this.$container.attr("data-ntajax-options")),this.main=this.options.id||"",this.typeAjax=this.options.type||"LmDefault",this.isProduct=this.options.isProduct||!1,this.$section=e(`#shopify-section-${this.main}`),self.isbtnLoadMore=!0,this.setupEventListeners()}return i.prototype=Object.assign({},i.prototype,{setupEventListeners:function(){var i=this;i.$container.on("click.ntlm","[data-load-more]",function(o){o.preventDefault();const a=e(this);a.addClass(n),t=`${a.attr("data-href")||a.attr("href")}&section_id=${i.main}`,i.isbtnLoadMore=!e(this).is("[data-is-prev]"),i.$btnCurrent=e(this),i.renderSectionFromFetch(t)})},renderSectionFromFetch:function(e){var t=this;BEEThemeSP.getToFetchSection(null,"text",e).then(e=>{"NVT_94"!=e?(t.$btnCurrent.removeClass(n),t[t.typeAjax](e),this.isProduct&&BEEThemeSP.reinitProductGridItem(),a.trigger("bee:hideTooltip")):t.$btnCurrent.removeClass(n)})},LmDefault:function(t){var n=e(t),i=n.find("[data-contentlm-replace]").html(),o=this.isbtnLoadMore?n.find("[data-wrap-lm]"):n.find("[data-wrap-lm-prev]"),a=this.$container.find("[data-contentlm-replace]");a[0]||(a=this.$section.find("[data-contentlm-replace]")),this.isbtnLoadMore?a.append(i):a.prepend(i),this.initLoamoreUpdate(o)},LmIsotope:function(t){var n=e(t),i=n.find("[data-contentlm-replace]").html(),o=this.isbtnLoadMore?n.find("[data-wrap-lm]"):n.find("[data-wrap-lm-prev]"),a=this.$container.find("[data-contentlm-replace]");a[0]||(a=this.$section.find("[data-contentlm-replace]")),i=e(i),this.isbtnLoadMore?a.append(i).isotopebee("appended",i):a.prepend(i).isotopebee("prepended",i),this.initLoamoreUpdate(o)},initLoamoreUpdate:function(e){let t=this.isbtnLoadMore?"[data-wrap-lm]":"[data-wrap-lm-prev]",n=this.$container.find(t);n[0]||(n=this.$section.find(t)),e.length>0?n.html(e.html()):n.hide()}}),i}();BEEThemeSP.initLoadMore=function(){e("[data-ntajax-options][data-not-main]:not(.is--enabled)").each(function(){e(this).addClass("is--enabled"),this.LoadMore=new w(this)})},BEEThemeSP.reinitProductGridItem=function(){this.ProductItem.init(),this.ProductItem.reloadReview(),this.Tooltip(),BEEThemeSP.Countdown(),BEEThemeSP.Compare.updateAll(),BEEThemeSP.Wishlist.updateAll(),a.trigger("currency:update")},BEEThemeSP.instagram=function(){var t="https://d3ejra0xbg20rg.cloudfront.net/instagram/media?shop="+Shopify.shop+"&resource=default",n="ins_19041994",i="ig_bee_token",o="true"===CookiesBE.get(i),a=".bee-icons-ins-svg",r=e(a).html()||"",s=r.split("[beeplit]"),l={loaded:"ins-is--loaded"};function c(t,n,i,o,c,d,u){1==s.length&&(r=e(a).html()||"",s=r.split("[beeplit]"));var p="",m=(i=i||!0,{IMAGE:"image",VIDEO:"video",CAROUSEL_ALBUM:"carousel_album"}),f={image:s[0],video:s[1],carousel_album:s[2]};e.each(n,function(e,t){if(e>=c)return 0;var n=t.thumbnail_url||t.media_url,i=m[t.media_type];p+='<div class="bee-col-ins'+e+" bee-col-ins bee-col-item bee-ins-type-"+i+'"><a data-no-instant rel="nofollow" class="bee-d-block bee-pr bee-oh" href="'+t.permalink+'" target="'+u+'"><div class="bee_ratio bee_bg lazyloadbee bee-lz--ins" data-bg="'+n+'" data-sizes="auto"></div><span class="lazyloadbee-loader"></span><div class="bee-ins-info"><span class="bee-ins-icon">'+f[i]+"</span></div></a></div>"}),t.html(p).parent().addClass(l.loaded),t.hasClass("flickitybee-later")&&(t[0].flickitybee=new BEEThemeSP.Carousel(t[0])),isStorageSpSessionAll&&i&&(sessionStorage.setItem("nt_ins"+o+d,p),sessionStorage.setItem("nt_ins"+o,JSON.stringify({timestamp:new Date,content:n})))}return function(){0!=e("[data-insbee-options]").length&&e("[data-insbee-options]").each(function(a){!function(e){var a,r,s=E(e.attr("data-insbee-options")),d=null,u=null,p=s.id,m=s.limit,f=s.acc||"spnt_bee",h=s.target;if("spnt_bee"!=f){if(f=atob(f),isStorageSpSessionAll&&(d=sessionStorage.getItem("nt_ins"+f+p),null!=(u=sessionStorage.getItem("nt_ins"+f))&&""!=u&&(a=new Date,(r=new Date(u.timestamp)).setMinutes(r.getMinutes()+30),a.getTime()>r.getTime()&&(u=null,d=null,sessionStorage.removeItem("nt_ins"+f+p),sessionStorage.removeItem("nt_ins"+f)))),null!=d&&""!=u){if(e.html(d).parent().addClass(l.loaded),!e.hasClass("flickitybee-later"))return;return e[0].flickitybee=new BEEThemeSP.Carousel(e[0]),!1}null!=u&&""!=u?(u=JSON.parse(u).content,c(e,u,!1,f,m,p,h)):(f!=n&&(t="https://graph.instagram.com/me/media?fields=id,media_type,media_url,permalink,thumbnail_url,caption,children&access_token="+f),fetch(t).then(function(e){if(!e.ok)throw new Error("not ok");return e.json()}).then(t=>{var a=f==n?t:t.data;c(e,a,!0,f,m,p,h),o||(CookiesBE.set(i,"true",{expires:7}),fetch("https://graph.instagram.com/refresh_access_token?grant_type=ig_refresh_token&access_token="+f))}).catch(t=>{e.html(""),console.error("Instagram Feed:error fetch")}))}}(e(this))})}}(),BEEThemeSP.sideBarInit=function(){var i=e("[data-sidebar-id]"),o=i.is("[data-sidebar-true]"),r=e("[data-sidebar-content]"),s=i.is("[data-is-disableDrawer]");if(0!=i.length){var l=i.attr("data-sidebar-id"),c=window.location.search.slice(1),d=`${window.location.pathname}?section_id=${l}&${c}`;BEEThemeSP.getToFetchSection(null,"text",d).then(l=>{if("NVT_94"==l)return r.html(""),void console.log(error);if(o||t.width()<1024&&!s){var c=(d=l.split("[beeplitlz]"))[2].split("[beeplitlz2]");BEEThemeSP.$appendComponent.after(c[0]+d[1]+c[1])}else{var d;c=(d=l.split("[beeplitlz]"))[2].split("[beeplitlz2]");r.html(d[1]),BEEThemeSP.$appendComponent.after(c[0]+d[1]+c[1])}BEEThemeSP.instagram(),BEEThemeSP.Countdown(),BEEThemeSP.Tooltip(),BEEThemeSP.reinitProductGridItem(),BEEThemeSP.Tabs.Accordion(),a.trigger("currency:update"),n.trigger("sidebar:updated");let u=i.attr("data-drawer-options");e.each(e(JSON.parse(u).id).find(".flickitybee-later"),function(e){this.flickitybee=new BEEThemeSP.Carousel(this)}),e.each(e("[data-sidebar-content]").find(".flickitybee-later"),function(e){this.flickitybee=new BEEThemeSP.Carousel(this)})})}},BEEThemeSP.BackToTop=function(){var n,i,o=e("#bee-backToTop"),a=parseInt(o.data("scrolltop")),r=o.find(".bee-circle-css")[0];return function(){t.width()<768&&o.data("hidden-mobile")||0==o.length||(window.addEventListener("scroll",()=>{n&&clearTimeout(n),n=setTimeout(function(){window.scrollY>a?o.addClass("is--show"):o.removeClass("is--show")},40),r&&(i&&clearTimeout(i),i=setTimeout(function(){let e=window.scrollY/(document.body.offsetHeight-window.innerHeight),t=(Math.round(100*e),360*e);r.style.setProperty("--cricle-degrees",t+"deg")},6))}),o.on("click",function(t){t.preventDefault(),e("html, body").animate({scrollTop:0},800)}))}}(),BEEThemeSP.Header=function(){var a,r,s=".bee-section-header",l=document.querySelector(s),c={},d=0,u=!1,m=0,f="data-header-options",g=e(l).find("["+f+"]").attr(f)||"{}",v=(g=JSON.parse(g)).isSticky,b=g.hideScroldown,y=document.documentElement,E="is-action__hover",w=e("[data-menu-nav]"),T=w.find(">li.has--children"),S=(T.find(">.bee-sub-menu"),e(l)),x=(e(".bee-website-wrapper"),"calc-pos-submenu"),C=S.hasClass("header-vertical"),_=".bee-is-header-categories-menu",P=".bee-is-header-categories",B=e(_),k=e(P),I="is--child-open",D="no-transiton-nav-a",A="is-header--stuck",M=200,R=!0,O=BEEconfigs.onlyClickDropIcon?".bee-menu-item.has--children>a>.bee--menu-toggle":".bee-menu-item.has--children>a";function N(){IsDesignMode&&(l.removeEventListener("preventHeaderReveal",$),window.removeEventListener("scroll",L),l=document.querySelector(".bee-section-header"),c={},d=0,u=!1,g=e(l).find("["+f+"]").attr(f)||"{}",g=JSON.parse(g),v=g.isSticky,b=g.hideScroldown),v&&b&&!C?(l.addEventListener("preventHeaderReveal",$),window.addEventListener("scroll",L,!1),new IntersectionObserver((e,t)=>{c=e[0].intersectionRect,t.disconnect()}).observe(l)):l.classList.remove("shopify-section-header-hidden","animate")}function $(){u=!0}function L(){const e=window.pageYOffset||document.documentElement.scrollTop;e>d&&e>c.bottom?requestAnimationFrame(F):e<d&&e>c.bottom?u?(window.clearTimeout(m),m=setTimeout(()=>{u=!1},366),requestAnimationFrame(F)):requestAnimationFrame(U):e<=c.top&&requestAnimationFrame(H),d=e}function F(){l.dispatchEvent(new Event("BeeHeaderHide")),S.one("transitionend webkitTransitionEnd oTransitionEnd",function(){S.hasClass("shopify-section-header-hidden")&&y.classList.remove(A)}),l.classList.add("shopify-section-header-hidden","shopify-section-header-sticky")}function U(){l.dispatchEvent(new Event("BeeHeaderReveal")),y.classList.add(A),l.classList.add("shopify-section-header-sticky","animate"),l.classList.remove("shopify-section-header-hidden")}function H(){y.classList.remove(A),R&&0==c.top&&0==c.height&&(c=l.getBoundingClientRect(),R=!1),l.classList.remove("shopify-section-header-hidden","shopify-section-header-sticky","animate"),y.classList.add(D),clearTimeout(r),r=setTimeout(()=>{y.classList.remove(D)},366)}function W(t,n){var i=e(n);t.each(function(t){var n,o=e(this),a="#bee-mega-contents"+o.data("id"),r=i.find(a).html();r?(o.html(r),o.find(".bee-products .bee-product").length>0&&BEEThemeSP.reinitProductGridItem(),setTimeout(function(){o.hasClass("isotopebee")&&BEEThemeSP.Isotopebee.init(o),K(o.closest(".has--children")),BEEThemeSP.PopupMFP(),0!=(n=o.find(".flickitybee-later")).length&&n.each(function(e){this.flickitybee=new BEEThemeSP.Carousel(this)})},600)):o.html("")}),setTimeout(function(){S.find(".isotopebee-enabled").isotopebee("layout")},800)}function j(t){t.find(O).off("click").on("click",function(t){t.preventDefault();var n=e(this);n.hasClass("bee--menu-toggle")&&(n=n.closest("a")),n.hasClass(I)?n.removeClass(I).siblings("ul").slideUp(M):n.addClass(I).siblings("ul").slideDown(M)})}function z(t){if(t&&(T=t),!(i<1024||0==T.length)){T.each(function(t,n){var i=e(this);i.hoverIntent({sensitivity:3,interval:35,timeout:150,over:function(e){i.addClass(E)},out:function(){i.removeClass(E)}})})}}function G(t){a=t?t.find(">a"):T.find(">a"),BEEThemeSP.isHover||i<1024||0==a.length?a.off("click.menu click.menuIntent"):a.on("click.menu",function(t){t.preventDefault();let i=e(this).parent();i.hasClass(E)?(i.removeClass(E),n.off("click.menuIntent")):(i.addClass(E).siblings().removeClass(E),n.on("click.menuIntent",function(t){var i=t.target;e(i).is("."+E)||e(i).parents("li").is("."+E)||(w.find("."+E).removeClass(E),n.off("click.menuIntent"))}))})}function Z(t,n){t&&(T=t),i<1024||0==T.length||(IsDesignMode&&(S=e(s)),T.each(function(t,i){K(e(this),n)}),S.addClass(x))}function q(){if(!(t.width()<1024)){IsDesignMode&&(S=e(s)),S.removeAttr("style").css({"--bee-max-width":`${t.width()-10}px`,"--bee-max-height":`${t.height()-Math.max(0,l.getBoundingClientRect().top)-Math.max(0,l.offsetHeight)-20}px`}),S.find(".isotopebee-enabled").isotopebee("layout"),setTimeout(function(){S.find(".isotopebee-enabled").isotopebee("layout")},500);var n=e("#bee-nav-categories");0!=n.length&&(n.removeAttr("style").css({"--bee-max-width":`${t.width()-Math.max(0,n[0].getBoundingClientRect().left)-n.width()}px`,"--bee-max-height":`${t.height()-Math.max(0,n[0].getBoundingClientRect().top)-10}px`}),n.find(".isotopebee-enabled").isotopebee("layout"),setTimeout(function(){n.find(".isotopebee-enabled").isotopebee("layout")},500))}}function K(t,n=!1){var o=t.find(">a")[0],a=t.find(">.bee-sub-menu")[0],r=t.data("placement")||"bottom";if((!t.hasClass("menu-width__full")||"right-start"==r)&&a&&(e(a).attr("style",""),("bottom"!=r||t.hasClass("bee-type__drop")||!function(t){var n=e(t);n.attr("style","");var o=n.outerWidth(),a=n.offset();if(!o||!a)return!1;var r=a.left,s=(i-o)/2;return!isThemeRTL&&s<=r&&r<=s+o&&i>=s+o?(n.addClass("is--center-screen"),!0):isThemeRTL&&s<=r+o&&r<=s&&i>=s+o?(n.addClass("is--center-screen"),!0):(n.removeClass("is--center-screen"),!1)}(a))&&(FloatingUIBeeDOM.computePosition(o,a,{placement:r,middleware:[FloatingUIBeeDOM.flip({}),FloatingUIBeeDOM.shift({padding:5})]}).then(({x:e,y:t,placement:n,middlewareData:i})=>{Object.assign(a.style,{left:`${e}px`,top:n.indexOf("bottom")>-1?"100%":`${t}px`})}),!n))){let t=e(a).find(".bee-lazy_menu.isotopebee.isotopebee-enabled");t.length>0&&t.isotopebee("layout")}}function V(t,n){var i=void 0===n?B.find("[data-wrapper-categories]"):n;i.html(t),q();var o=i.find("#bee-nav-categories>.has--children");z(o),G(o),document.addEventListener("theme:hover",function(e){G(o)}),Z(o),setTimeout(()=>{Z(o)},1e3),i.find(".bee-type__drop .bee-lazy_menu").each(function(t){j(e(this))}),i.find(".bee-products .bee-product").length>0&&BEEThemeSP.reinitProductGridItem();var a=i.find(".isotopebee-later"),r=i.find(".flickitybee-later");a.length>0&&a.each(function(t){BEEThemeSP.Isotopebee.init(e(this))}),a.length>0&&r.each(function(e){this.flickitybee=new BEEThemeSP.Carousel(this)})}return{stickyInit:function(){!function(){if(!v||v&&b||C)return;let t=new IntersectionObserver(function(e){0===e[0].intersectionRatio?(y.classList.add(A),R&&(y.classList.add("bee-hsticky__ready"),R=!1)):1===e[0].intersectionRatio&&(y.classList.remove(A),y.classList.add(D),clearTimeout(r),r=setTimeout(()=>{y.classList.remove(D)},366))},{threshold:[0,1]});e("#bee-hsticky__sentinel").length>0&&t.observe(document.querySelector("#bee-hsticky__sentinel")),setTimeout(()=>{y.classList.add("bee-hsticky__ready"),R=!1},396)}(),N(),o.css({"--topbar-height":(e("#bee-top-bar-main").height()||0)+"px","--header-height":(e(".bee-section-header").height()||0)+"px"}),t.on("resize",function(){o.css({"--topbar-height":(e("#bee-top-bar-main").height()||0)+"px","--header-height":(e(".bee-section-header").height()||0)+"px"})})},init:function(n){!function(){var t=e(".bee-type__mega .bee-lazy_menu"),n=e(".bee-list-categories--item.is--active"),o=n.index(),a=o>0?o:"",r=h+"timeMegaBee"+a,s=h+"dataMegaBee"+a,l=isStorageSpSession&&sessionStorage.getItem(r)||0,c=(l=parseInt(l),18e5);if(!(0==t.length||i<1024))if(!IsDesignMode&&l>0&&l>=Date.now())W(t,sessionStorage.getItem(s));else{var d=e(".bee-section-mega__menu"),u=d.length>0?d.find("[data-section-id]").data("section-id"):"mega-menu,mega-menu2",p=n.find(">a").attr("href");function m(e){BEEThemeSP.getToFetchSection("?sections="+e,"json").then(e=>{if("NVT_94"==e||e.status)e.status&&console.error(e.description);else{var n="";for(const t in e)n+=e[t].split("[nt_mega_split1]")[1];W(t,"<div>"+n+"</div>"),isStorageSpSession&&(l=Date.now()+c,sessionStorage.setItem(r,l),sessionStorage.setItem(s,"<div>"+n+"</div>"))}})}o>0&&0==d.length&&location.pathname!=p?BEEThemeSP.getToFetchSection(null,"text",p).then(t=>{if("NVT_94"==t)return;const n=e(t).find(".bee-section-mega__menu [data-section-id]").data("section-id");n&&m(n)}):m(u)}}(),function(){var t=e(".bee-type__drop .bee-lazy_menu"),n=e(".bee-list-categories--item.is--active").index(),o=n>0?n:"",a=h+"timeDropBee"+o,r=h+"dataDropBee"+o,s=isStorageSpSession&&sessionStorage.getItem(a)||0;if(s=parseInt(s),IsDesignMode&&t.each(function(t){K(e(this).closest(".has--children")),j(e(this))}),!(0==t.length||i<1024||IsDesignMode))if(s>0&&s>=Date.now()){var l=sessionStorage.getItem(r).split("[nt_drop_split2]");t.each(function(t){e(this).html(l[t]),K(e(this).closest(".has--children")),j(e(this))})}else{var c=[];t.each(function(t){c.push(e(this).data("handle"))}),c=c.join(" "),BEEThemeSP.getToFetchSection(null,"text",`${window.BEEroutes.search_url}?type=article&q=${c}&section_id=dropdown-menu`).then(n=>{if("NVT_94"!=n){var i=n.split("[nt_drop_split1]")[1],o=i.split("[nt_drop_split2]");t.each(function(t){e(this).html(o[t]),K(e(this).closest(".has--children")),j(e(this))}),isStorageSpSession&&(s=Date.now()+18e5,sessionStorage.setItem(a,s),sessionStorage.setItem(r,"<div>"+i+"</div>"))}})}}(),z(n),G(n),document.addEventListener("theme:hover",function(e){G(n)}),q(),Z(n,!0),function(){if(0!=B.find("[data-wrapper-categories]").length)if(IsDesignMode||0!=k.length)IsDesignMode&&(B=e(_),V((k=e(P)).html()));else{var t=h+"timeCatBee",n=h+"dataCatBee",i=isStorageSpSession&&sessionStorage.getItem(t)||0;(i=parseInt(i))>0&&i>=Date.now()?V(sessionStorage.getItem(n)):BEEThemeSP.getToFetchSection(null,"text",p+"/?section_id=header-categories").then(e=>{if("NVT_94"!=e){var o=(new DOMParser).parseFromString(e,"text/html").querySelector("div").innerHTML;V(o),isStorageSpSession&&(i=Date.now()+18e5,sessionStorage.setItem(t,i),sessionStorage.setItem(n,o))}})}}(),setTimeout(function(){t.on("resize.menu",q)},2e3)},updateCat:V}}(),BEEThemeSP.MobileNav=function(){var n,o,a={tabNavActive:"is--active",opend:"is--opend"},r="resize.navmb",s="opendDrawer",l=e(".bee-list-categories--item.is--active").index(),c=l>0?l:"",d=h+"timeMenuBee"+c,u=h+"dataMenuBee"+c,p=!1,m=18e5,f=e(".bee-sp-section-mb-nav [data-section-id]"),g=e(".bee-sp-section-mb-cat [data-section-id]"),v=f.length,b=g.length;l>0&&(n=f.data("section-id"),o=g.data("section-id"));var y=isStorageSpSession&&sessionStorage.getItem(d)||0,E=(y=parseInt(y),{});function w(e){e.hasClass(a.opend)?e.removeClass(a.opend).children("ul").slideUp(200):e.addClass(a.opend).children("ul").slideDown(200)}function T(e=4){IsDesignMode||p||E.$mobileNav.hasClass("bee-d-lg-none")&&i>1024||setTimeout(function(){if(l>0){n=1==v?n:"mb_nav",o=1==b?o:"mb_cat";var e=!1,t=!1;BEEThemeSP.getToFetchSection("?section_id="+n,"text").then(n=>{"NVT_94"!=n&&(E.$mobileNav.find("#shopify-mb_nav").html(n),t?S("indexPage"):e=!0)}),BEEThemeSP.getToFetchSection("?section_id="+o,"text").then(n=>{"NVT_94"!=n&&(E.$mobileNav.find("#shopify-mb_cat").html(n),e?S("indexPage"):t=!0)})}else BEEThemeSP.getToFetchSection(null,"text",BEEroutes.search_url+"/?view=mn").then(e=>{"NVT_94"!=e&&S(e)})},e)}function S(e){BEEThemeSP.Helpers.promiseStylesheet(BEEconfigs.stylesheet2).then(function(){p=!0,l<=0&&"indexPage"!=e&&E.$mobileNav.html(e),l>0&&"indexPage"==e&&(e=E.$mobileNav.html()),t.off(r),E.$mobileNav.off(s),isStorageSpSession&&(y=Date.now()+m,sessionStorage.setItem(d,y),sessionStorage.setItem(u,e))})}return function(){E={$mobileNav:e("#bee-menu-drawer")},isStorageSpSession&&y>0&&y>=Date.now()?BEEThemeSP.Helpers.promiseStylesheet(BEEconfigs.stylesheet2).then(function(){E.$mobileNav.html(sessionStorage.getItem(u))}):(t.on(r,BEEThemeSP.debounce(300,function(){i=t.width(),T(0)})),T(500),IsDesignMode||E.$mobileNav.on(s,function(){T(0)})),E.$mobileNav.on("click","[data-tab-mb-nav]>[data-tab-mb-item]",function(){var t=e(this);t.hasClass(a.tabNavActive)||(t.addClass(a.tabNavActive).siblings().removeClass(a.tabNavActive),e("[data-tab-mb-content]."+a.tabNavActive).removeClass(a.tabNavActive),e(t.data("id")).addClass(a.tabNavActive))}),E.$mobileNav.on("click",".bee-menu-item-has-children.bee-only_icon_false>a",function(t){t.preventDefault(),t.stopPropagation(),w(e(this).parent())}),E.$mobileNav.on("click",".bee-menu-item-has-children > a > .bee-mb-nav__icon",function(t){t.preventDefault(),t.stopPropagation(),w(e(this).parent().parent())})}}(),BEEThemeSP.loadingBar=function(){return function(){console.log("loadingBar")}}(),BEEThemeSP.currencyForm=function(){var t={select:"is--selected",opend:"is--opend"},n={},i=BEEconfigs.cartCurrency;function o(t){i!=t&&(isStorageSpdLocalAll&&localStorage.setItem("BECurrency",t),e(`[data-currency-wrap] [data-iso="${t}"]`).first().trigger("click"))}function r(){var i,o,r,s,l,c,d;a.on("click","[data-locale-wrap] [data-locale-item] ,[data-currency-wrap] [data-currency-item]",function(a){a.preventDefault(),i=e(this),"2"==BEEconfigs.currency_type&&i.is("[data-currency-item]")||i.hasClass(t.select)||(i.is("[data-locale-item]")?(o="[data-locale-wrap]","[data-locale-wrap] [data-locale-item]",r="$localeSelector",d=!1):(o="[data-currency-wrap]","[data-currency-wrap] [data-currency-item]",r="$currencySelector",d=!0),s=i.attr("data-iso"),l=e(o+" ."+t.select).first().attr("data-iso"),c=i.attr("data-country"),e(o+" ."+t.select).first().attr("data-country"),e(o+" [data-current]").text(s).removeClass("flagsbee-"+l).addClass("flagsbee-"+s),e(o+" [data-iso="+s+"]").addClass(t.select).siblings().removeClass(t.select),function(e,t,i){n[e].val(t),"$currencySelector"===e&&n.$countryMirror.val(i),n.$formCurrencyLocale.submit()}(r,s,c),isStorageSpdLocal&&d&&localStorage.setItem("BECurrency",s),BEEThemeSP.loadingBar())})}return function(){0!=(n={$formCurrencyLocale:e("#CurrencyLangSelector"),$countryMirror:e("#countryMirror"),$localeSelector:e("#LocaleSelector"),$currencySelector:e("#CurrencySelector")}).$formCurrencyLocale.length&&(r(),function(){var t=isStorageSpdLocalAll?localStorage.getItem("BECurrency"):null;if(BEEconfigs.auto_currency&&!navigator.userAgent.match(/bot|spider/i)&&!t&&!IsDesignMode){var n,i=isStorageSpdLocalAll?localStorage.getItem("nt_currency"):null,a={AF:"AFN",AX:"EUR",AL:"ALL",DZ:"DZD",AS:"USD",AD:"EUR",AO:"AOA",AI:"XCD",AQ:"",AG:"XCD",AR:"ARS",AM:"AMD",AW:"AWG",AU:"AUD",AT:"EUR",AZ:"AZN",BS:"BSD",BH:"BHD",BD:"BDT",BB:"BBD",BY:"BYN",BE:"EUR",BZ:"BZD",BJ:"XOF",BM:"BMD",BT:"BTN",BO:"BOB",BA:"BAM",BW:"BWP",BV:"NOK",BR:"BRL",IO:"USD",BN:"BND",BG:"BGN",BF:"XOF",BI:"BIF",KH:"KHR",CM:"XAF",CA:"CAD",CV:"CVE",KY:"KYD",CF:"XAF",TD:"XAF",CL:"CLP",CN:"CNY",CX:"AUD",CC:"AUD",CO:"COP",KM:"KMF",CG:"XAF",CD:"CDF",CK:"NZD",CR:"CRC",CI:"XOF",HR:"HRK",CU:"CUP",CY:"EUR",CZ:"CZK",DK:"DKK",DJ:"DJF",DM:"XCD",DO:"DOP",EC:"USD",EG:"EGP",SV:"USD",GQ:"XAF",ER:"ERN",EE:"EUR",ET:"ETB",FK:"FKP",FO:"DKK",FJ:"FJD",FI:"EUR",FR:"EUR",GF:"EUR",PF:"XPF",TF:"EUR",GA:"XAF",GM:"GMD",GE:"GEL",DE:"EUR",GH:"GHS",GI:"GIP",GR:"EUR",GL:"DKK",GD:"XCD",GP:"EUR",GU:"USD",GT:"GTQ",GG:"GBP",GN:"GNF",GW:"XOF",GY:"GYD",HT:"HTG",HM:"AUD",VA:"EUR",HN:"HNL",HK:"HKD",HU:"HUF",IS:"ISK",IN:"INR",ID:"IDR",IR:"IRR",IQ:"IQD",IE:"EUR",IM:"GBP",IL:"ILS",IT:"EUR",JM:"JMD",JP:"JPY",JE:"GBP",JO:"JOD",KZ:"KZT",KE:"KES",KI:"AUD",KR:"KRW",KW:"KWD",KG:"KGS",LA:"LAK",LV:"EUR",LB:"LBP",LS:"LSL",LR:"LRD",LY:"LYD",LI:"CHF",LT:"EUR",LU:"EUR",MO:"MOP",MK:"MKD",MG:"MGA",MW:"MWK",MY:"MYR",MV:"MVR",ML:"XOF",MT:"EUR",MH:"USD",MQ:"EUR",MR:"MRU",MU:"MUR",YT:"EUR",MX:"MXN",FM:"USD",MD:"MDL",MC:"EUR",MN:"MNT",ME:"EUR",MS:"XCD",MA:"MAD",MZ:"MZN",MM:"MMK",NA:"NAD",NR:"AUD",NP:"NPR",NL:"EUR",AN:"",NC:"XPF",NZ:"NZD",NI:"NIO",NE:"XOF",NG:"NGN",NU:"NZD",NF:"AUD",MP:"USD",NO:"NOK",OM:"OMR",PK:"PKR",PW:"USD",PS:"ILS",PA:"PAB",PG:"PGK",PY:"PYG",PE:"PEN",PH:"PHP",PN:"NZD",PL:"PLN",PT:"EUR",PR:"USD",QA:"QAR",RE:"EUR",RO:"RON",RU:"RUB",RW:"RWF",BL:"EUR",SH:"SHP",KN:"XCD",LC:"XCD",MF:"EUR",PM:"EUR",VC:"XCD",WS:"WST",SM:"EUR",ST:"STN",SA:"SAR",SN:"XOF",RS:"RSD",SC:"SCR",SL:"SLL",SG:"SGD",SK:"EUR",SI:"EUR",SB:"SBD",SO:"SOS",ZA:"ZAR",GS:"GBP",ES:"EUR",LK:"LKR",SD:"SDG",SR:"SRD",SJ:"NOK",SZ:"SZL",SE:"SEK",CH:"CHF",SY:"SYP",TW:"TWD",TJ:"TJS",TZ:"TZS",TH:"THB",TL:"USD",TG:"XOF",TK:"NZD",TO:"TOP",TT:"TTD",TN:"TND",TR:"TRY",TM:"TMT",TC:"USD",TV:"AUD",UG:"UGX",UA:"UAH",AE:"AED",GB:"GBP",US:"USD",UM:"USD",UY:"UYU",UZ:"UZS",VU:"VUV",VE:"VEF",VN:"VND",VG:"USD",VI:"USD",WF:"XPF",EH:"MAD",YE:"YER",ZM:"ZMW",ZW:"ZWD"};if(i){let e=JSON.parse(i);try{n=t||e.currency.handle}catch(t){n=a[e.countryCode]||a[e.country]||e.currency}o(n)}else{var r={type:"get",url:"https://extreme-ip-lookup.com/json/?key=demo2",dataType:"json",success:function(t){"success"==t.status?(o(a[t.countryCode]),isStorageSpdLocal&&localStorage.setItem("nt_currency",JSON.stringify(t))):e.ajax(s)},error:function(t,n){e.ajax(s)}},s={type:"get",url:"https://ipinfo.io/json",dataType:"json",success:function(e){o(a[e.country]),isStorageSpdLocal&&localStorage.setItem("nt_currency",JSON.stringify(e))},error:function(t,n){e.ajax(l)}},l={type:"get",url:"https://d1hcrjcdtouu7e.cloudfront.net/users/countryDetection",dataType:"json",success:function(e){o(a[e.country]),isStorageSpdLocal&&localStorage.setItem("nt_currency",JSON.stringify(e))}};e.ajax({type:"get",url:"/browsing_context_suggestions.json?source=geolocation_recommendation&currency[enabled]=true&language[enabled]=true",dataType:"json",success:function(t){try{var n=t.suggestions[0].parts;o(n.currency.handle),isStorageSpdLocal&&localStorage.setItem("nt_currency",JSON.stringify(n))}catch(t){e.ajax(r)}},error:function(t,n){e.ajax(r)}})}}}())}}(),BEEThemeSP.productRecommendations=function(){var t={};function n(n){n[0]?n.hide():t.$recommendationsWrap.hasClass("bee-pr-single_tab-content")?(t.$recommendationsWrap.find(".bee-loading--bg").hide(),t.$recommendationsWrap.find("[data-emty-product]").show()):(BEEThemeSP.isRelatedEmty=!0,BEEThemeSP.isRencentEmty?e(".bee-tp-rencent-related").hide():t.$recommendationsWrap.hide())}return function(i){t={$recommendationsWrap:e("#pr_recommendations:not(.is--not-rub-js)")},0!=(i=i||t.$recommendationsWrap).length&&function(t){var i=t.data("type"),o=t.data("sid"),a=t.data("baseurl"),r=t.closest(".id_product-recommendations"),s=p+a+"&section_id="+o;"3"==i&&(s=a+"?section_id="+o+"&product_id="+t.data("id")+"&limit="+t.data("limit")),BEEThemeSP.getToFetchSection(null,"text",s).then(i=>{if("NVT_94"!=i){var o=IsDesignMode?e(e(i)[2]).html():e(i).html();try{o.trim()}catch(t){o=e(i).html()}if(""!==o.trim()){if(t.html(o),t.find(".bee-product").length>0&&BEEThemeSP.reinitProductGridItem(),t.find(".flickitybee").length>0){var a=t.find(".flickitybee")[0];a.flickitybee=new BEEThemeSP.Carousel(a),BEEThemeSP.ProductItem.resizeObserver()}}else n(r)}else n(r)})}(i)}}(),BEEThemeSP.recentlyViewed=function(){var t={};function n(n,i=!0){n[0]?i?n.hide():n.slideUp():t.$recentlyWrap.hasClass("bee-pr-single_tab-content")?(t.$recentlyWrap.find(".bee-loading--bg").hide(),t.$recentlyWrap.find("[data-emty-product]").show()):(BEEThemeSP.isRencentEmty=!0,BEEThemeSP.isRelatedEmty?e(".bee-tp-rencent-related").hide():t.$recentlyWrap.hide())}return function(i){t={$recentlyWrap:e("#recently_wrap")},i=i||t.$recentlyWrap,isStorageSpdLocalAll&&0!=i.length&&function(t){var i=localStorage.getItem("nt_recent"),o="product"==u?t.data("id"):"19041994",a=t.data("sid"),r=t.data("unpr"),s=t.data("limit"),l=t.closest(".id_recently_viewed");if(null!=i){var c=(f=i.split(",")).indexOf(o);if(c>-1?(f=f.splice(0,s+1)).splice(c,1):f=f.splice(0,s),0==f.length)return n(l,!1),!1;var d=f.toString().replace(/,/g," OR "),m=encodeURI(d);BEEThemeSP.getToFetchSection(null,"text",p+"/?section_id="+a+"&type=product&options[unavailable_products]="+r+"&q="+m).then(i=>{if("NVT_94"!=i){var o=IsDesignMode?e(e(i)[2]).html():e(i).html();try{o.trim()}catch(t){o=e(i).html()}if(""!==o.trim()){if(t.html(o),t.find(".bee-product").length>0&&BEEThemeSP.reinitProductGridItem(),t.find(".flickitybee").length>0){var a=t.find(".flickitybee")[0];a.flickitybee=new BEEThemeSP.Carousel(a),BEEThemeSP.ProductItem.resizeObserver()}}else n(l)}else n(l)})}else{n(l);var f=new Array}f.indexOf(o)<0&&"19041994"!=o&&(f.length>s&&(f=f.splice(0,s)),f.unshift(o),localStorage.setItem("nt_recent",f.toString()))}(i)}}(),BEEThemeSP.Cart=function(){var t="[data-cart-items]",i="[data-cart-prices]",r="[data-cart-calc-shipping]",s="[data-cart-ship-text]",l="[data-cart-ship-bar]",c="[data-cart-discounts]",d="data-cart-upsell-options",m="data-bee-percent",h=!0,g=!0,v="disable"==BEEconfigs.cartType,b="cart"!=u?BEEconfigs.afterActionATC:"4",y=v?"cart_data":"cart_data,mini_cart",w={loading:"is--loading",none:"bee-d-none",active:"is--active"},T=window.cartBEEectionID,S="cart"!=u?y:`cart_data,${T}`,x=19041994,C=!1,_={},P=window.BEEroutes.cart_url;function B(){e("[data-cart-count]").html(e("#bee-mini_cart").data("ccount")),BEEThemeSP.Tooltip(),a.trigger("currency:update"),v||(BEEThemeSP.ProductAjax.change(),BEEThemeSP.agreeForm(),function(){if("cart"==u)return;var t=e("[data-cart-wrapper]"),n=".bee-mini_cart-tool__content";e("[data-cart-tools]").on("click","[data-cart-tool_action]",function(i){i.preventDefault(),i.stopPropagation();var o=e(this),r=o.data("id"),s=e(n+".is--"+r),l=s.find("[data-cart-tool_close]");s.addClass("is--opend"),t.addClass("is--contentUpdate"),s.removeAttr("style"),BEEThemeSP.isTouch||s.one("transitionend webkitTransitionEnd oTransitionEnd",function(){s.find("[data-opend-focus]").focus()}),t.on("click.tool",function(i){i.preventDefault(),e(i.target).is(n)||e(i.target).closest(n).length>0||(e(n+".is--"+r).removeClass("is--opend"),t.removeClass("is--contentUpdate"),t.off("click.tool"))}),l.on("click",function(e){e.preventDefault(),s.removeClass("is--opend"),t.removeClass("is--contentUpdate"),l.off("click")}),a.off("keyup.drawer").on("keyup.toolCart",function(i){27===i.keyCode&&(e(n+".is--"+r).removeClass("is--opend"),t.removeClass("is--contentUpdate"),a.off("keyup.toolCart").on("keyup.drawer",function(e){27===e.keyCode&&BEEThemeSP.Drawer.close(e)}))})})}(),M(),O(),R(),N(),function(){let t=e("[data-tab-cart-wrap]");if(BEEThemeSP.cartTabActive=!0,!t[0])return;let n=e("[data-cart-tab-title]"),i=e("[data-cart-tab-content]");t.on("click","[data-tab-cart-item]",function(t){t.preventDefault();let o=e(this);BEEThemeSP.cartTabActive=o.is("[data-is-tab-cart]"),n.text(o.data("title")),o.addClass(w.active).siblings("."+w.active).removeClass(w.active),i.attr("aria-hidden",!0).eq(o.index()).attr("aria-hidden",!1)})}(),$(),L(),function(){if(0==e("#bee-tab-visited").length||!isStorageSpdLocalAll)return;let t=localStorage.getItem("nt_recent");if(null==t)return;let n=t.split(",").toString().replace(/,/g," OR "),i=encodeURI(n),o=e(".bee-tab-visited-empty"),r=e(".bee-tab-visited-skeleton");o.hide(),r.show(),BEEThemeSP.getToFetchSection(null,"text",p+"/?section_id=mini_cart_visited&type=product&options[unavailable_products]=show&q="+i).then(t=>{r.hide(),"NVT_94"!=t&&(r.after(e(t).html()),a.trigger("currency:update"),BEEThemeSP.Wishlist.updateAll(),BEEThemeSP.Tooltip())})}(),n.on("update:mini_cart:wishlist",F),n.trigger("update:mini_cart:wishlist"))}function k(e=!1){BEEThemeSP.getToFetchSection("?sections="+S,"json").then(t=>{"NVT_94"!=t&&I(t,e)})}function I(d,p=!1){var f=d.cart_data,h=d.mini_cart||d[T],g=e(h),v=e(t),y=e(i),E=e(r),w=E.find(s),S=E.find(l),x=e(c);0!=(f=(f=f.split("[bee_split1]")[1]).split("[bee_split2]"))[0]&&!BEEThemeSP.isATCSuccess||"cart"!=u?(!BEEThemeSP.isATCSuccess&&p&&(BEEThemeSP.isATCSuccess=p),v.html(g.find(t).html()),y.html(g.find(i).html()),E.attr(m,g.find(r).attr(m)),w.replaceWith(g.find(s).wrap()),S.attr("style",g.find(l).attr("style")),x.html(g.find(c).html()),e("[data-cart-count]").html(f[0]),e("[data-cart-tt-price]").html(f[1]),"1"==f[2]?(e(".bee-mini_cart-tool__content.is--gift.is--opend [data-cart-tool_close]").trigger("click"),e("[data-toogle-gift]").hide()):e("[data-toogle-gift]").show(),0==f[0]?o.addClass("bee-cart-count-0"):(o.removeClass("bee-cart-count-0"),C&&(L(),C=!1)),document.dispatchEvent(new CustomEvent("cart:update:count",{detail:{count:f[0]},bubbles:!0,cancelable:!0})),BEEThemeSP.Tooltip(),a.trigger("currency:update"),"0"!=b&&BEEThemeSP.isATCSuccess&&D(),n.trigger("cart:updated"),$()):D()}function D(){BEEThemeSP.isATCSuccess=!1,"1"==b||"2"==b||("3"==b?(BEEThemeSP.cartTabActive||e("[data-is-tab-cart]").trigger("click"),BEEThemeSP.Drawer.opend(e("#bee-mini_cart"))):document.location.href="5"==b?BEEThemeSP.root_url+"checkout":P)}function A(t){var n,i=new Headers({"Content-Type":"application/json"}),o=("string"!=typeof(n=t)&&"undefined"==(n+="")&&(n=""),e.trim(n)),a={method:"POST",headers:i,body:JSON.stringify({note:o})};fetch(P+"/update.js",a).then(function(e){return e.json()}).then(function(t){o.length>0?(e('[data-id="note"].is--editNote, .bee-txt_edit_note').removeClass(w.none),e('[data-id="note"].is--addNote, .bee-txt_add_note').addClass(w.none)):(e('[data-id="note"].is--editNote, .bee-txt_edit_note').addClass(w.none),e('[data-id="note"].is--addNote, .bee-txt_add_note').removeClass(w.none))}).catch(function(e){console.log("cart update error: ",e)})}function M(){e('textarea[name="note"]').on("change",function(){A(e(this).val())})}function R(){if(isStorageSpdLocal){var t=e("#CartDiscountcode"),n=e("#CartDiscountcode, [data-cart-discount]"),i=localStorage.getItem("CartDiscountcode");n.val(i).trigger("keyup"),e('[data-action="save-discountcode"]').click(function(e){localStorage.setItem("CartDiscountcode",t.val()),n.val(t.val()).trigger("keyup")})}}function O(){let t,n=e("#CartDiscountcode");n[0]&&n.keyup(function(){clearTimeout(t),t=setTimeout(function(){n.val().length?n.attr("name","discount"):n.removeAttr("name")},300)})}function N(){var t,n=e("[data-estimate-shipping-wrap]");n[0]&&(n[0].langRates=JSON.parse(n.find("template[data-lang-rates]").html()||"{}"),t=n.data("id"),Shopify&&new Shopify.CountryProvinceSelector(`ShippingCountry_${t}`,`ShippingProvince_${t}`,{hideElement:`ShippingProvinceContainer_${t}`}),n.on("click",'[data-action="estimate-shipping"]',function(t){t.preventDefault(),t.stopPropagation();var n=this,i=e(n),o=i.find("[data-response-rates]"),r={},s=e(t.currentTarget);s.addClass(w.loading),document.dispatchEvent(new CustomEvent("theme:loading:start")),r.country=i.find('[name="country"]').val()||"",r.province=i.find('[name="province"]').val()||"",r.zip=i.find('[name="zip"]').val()||"",fetch(P+"/shipping_rates.json?"+e.param({shipping_address:r}),{credentials:"same-origin",method:"GET"}).then(function(t){document.dispatchEvent(new CustomEvent("theme:loading:end")),s.removeClass(w.loading),t.json().then(function(i){!function(t,n,i,o,r){t?function(t,n,i,o){var r="",s="",l="";n.zip&&(r+=n.zip+", ");n.province&&(r+=n.province+", ");if(r+=n.country,t.length>1){var c=BEEThemeSP.Currency.formatMoney(t[0].price);s=o.multiple_rates.replace("[number_of_rates]",t.length).replace("[address]",r).replace("[rate]",c)}else s=1===t.length?o.one_rate.replace("[address]",r):o.no_rates;var l="";e.each(t,function(e,t){var n=BEEThemeSP.Currency.formatMoney(t.price),i=o.rate_value.replace("[rate_title]",t.name).replace("[rate]",n);l+="<li>"+i+"</li>"}),i.html('<div class="bee-mess__rates is--rates-success">'+s+'</div><div class="bee-results__rates"><ul>'+l.toString().replace(/<\/li>,<li>/g,"</li><li>")+"</ul></div>").fadeIn(),a.trigger("currency:update")}(n.shipping_rates,i,o,r):function(e,t,n){var i="";Object.keys(e).forEach(function(t){i+="<li><span class='bee-key__rate'>".concat(t,"</span> ").concat(e[t],"</li>")}),"country is not supported."===i&&(i=`<li>${n.no_rates}</li>`);t.html(`<p>${n.errors}</p><ul class="bee-mess__rates is--rates-error">${i}</ul>`).fadeIn()}(n,o,r)}(t.ok,i,r,o,n.langRates)})})}.bind(n[0])))}function $(){if(!f)return;let t=e("[data-cart-ship-done]").length;t>0&&h?(BEEThemeSP.CanvasConfetti(),h=!1,g=!1):e(r).length>0&&g?(BEEThemeSP.CanvasConfetti(),g=!1):0==t&&(g=!1,h=!0)}function L(){let t=e(`[${d}]`);if(0==t.length)return;let n=E(t.attr(d));if(n.product_id=e("[data-cart-items] [data-cart-item]:first").data("pid")||19041994,x==n.product_id)return;x=n.product_id;let i=`${n.baseurl}?section_id=${n.section_id}&product_id=${n.product_id}&limit=${n.limit}`;BEEThemeSP.getToFetchSection(null,"text",i).then(n=>{if("NVT_94"==n)return void t.hide();_.flickitybee&&_.flickitybee.destroy(),t.html(e(n).html()),a.trigger("currency:update"),BEEThemeSP.Tooltip();let i=t.find(".flickitybee")[0];i&&(_.flickitybee=new BEEThemeSP.Carousel(i),setTimeout(function(){e(i).flickitybee("resize")},150),setTimeout(function(){e(i).flickitybee("resize")},450))})}function F(){let t=e("#bee-tab-wishlist"),n=BEEThemeSP.linkWishlist||"";if(0==t.length||n.indexOf("id:")<0)return;let i=e(".bee-tab-wishlist-empty"),o=e(".bee-tab-wishlist-skeleton"),r=n.replace("view=wishlist","section_id=mini_cart_wishlist");i.hide(),o.show(),BEEThemeSP.getToFetchSection(null,"text",r).then(t=>{o.hide(),"NVT_94"!=t&&(o.siblings(".bee-widget__pr").remove(),o.after(e(t).html()),a.trigger("currency:update"),BEEThemeSP.Wishlist.updateAll(),BEEThemeSP.Tooltip())})}return{renderContents:I,getToFetch:k,init:function(){!function(){if("cart"==u)return M(),O(),R(),N(),BEEThemeSP.agreeForm(),$(),void L();IsDesignMode?B():v||BEEThemeSP.getToFetchSection("?section_id=mini_cart").then(t=>{"NVT_94"!=t&&BEEThemeSP.Helpers.promiseStylesheet(BEEconfigs.stylesheet1).then(function(){e("#bee-mini_cart").html(e(t).html()),B()})})}(),document.addEventListener("cart:refresh",function(e){k(!1)}),document.addEventListener("cart:refresh:opend",function(e){k(!0)}),n.on("add:cart:upsell",function(e){C=!0})}}}(),BEEThemeSP.Login=function(){var t="data-login-sidebar";function n(){var n=e("#bee-login-sidebar");BEEThemeSP.Drawer.remove("bee-login-sidebar"),n.on("click","["+t+"]",function(i){i.preventDefault();var o=e(this).attr(t);e("#bee-login-sidebar .bee-content-login-sidebar.is--"+o).attr("aria-hidden","false").siblings().attr("aria-hidden","true"),e("#bee-login-sidebar .bee-drawer__header .is--"+o).attr("aria-hidden","false").siblings().attr("aria-hidden","true"),n.attr("data-target",o)})}return function(){IsDesignMode?n():function(){var t=e("#bee-login-sidebar");if(0!=t.length){var i=h+"timeLoginBee",o=h+"dataLoginBee",a=isStorageSpSession&&sessionStorage.getItem(i)||0;(a=parseInt(a))>0&&a>=Date.now()?BEEThemeSP.Helpers.promiseStylesheet(BEEconfigs.stylesheet3).then(function(){t.html(sessionStorage.getItem(o)),n()}):BEEThemeSP.getToFetchSection("?section_id=login-sidebar").then(r=>{"NVT_94"!=r&&BEEThemeSP.Helpers.promiseStylesheet(BEEconfigs.stylesheet3).then(function(){t.html(e(r).html()),n(),isStorageSpSession&&(a=Date.now()+24e6,sessionStorage.setItem(i,a),sessionStorage.setItem(o,e(r).html()))})})}}()}}(),BEEThemeSP.Compare=function(){var t,n,i=!BEEconfigs.enable_compare,o=6,r="[data-link-compare]",s="[data-action-compare]",l="[data-remove-compare]",c="[data-count-compare]",d="[data-clear-compare]",u="[data-close-compare]",m="bee_cp",f="is--pe-none",h="is--added",g="is--activate",v=BEEProductStrings.added_text_cp,b=BEEProductStrings.compare,y=0,E=p+"/?view=compare&type=product&options[unavailable_products]=last&q=",w="",T=BEEconfigs.cp_icon,S=BEEconfigs.cp_icon_added,x=BEEconfigs.enableCompePopup,C=window.isPageCompare;function _(e,t,n){return n.indexOf(e)===t}function P(){var e=localStorage.getItem(m);if(null!=e){t=e.split(",");var n=e.replace(/,/g," OR "),i=encodeURI(n);w=E+i}}function B(){if(isStorageSpdLocalAll&&!i){var n=localStorage.getItem(m);if(null!=n){var o=n.replace(/id:/g,"");t=o.split(","),y=""==n?0:t.length,t.forEach(function(e,t){k(e.replace("id:",""))}),e(c).html(window.countComparePage||y)}}}function k(t,n){var i=e(s+'[data-id="'+t+'"]:not(.'+h+")");i.addClass(h).removeClass(f).find(".bee-text-pr").text(v),i.find(".bee-svg-pr-icon").html(S),n&&n.trigger("updateTooltip")}function I(t,i=!1){x&&(null==BEEconfigs.compePopupDes||"canvas"==BEEconfigs.compePopupDes?BEEThemeSP.getToFetchSection(null,"text",t.replace("view=compare","section_id=compare-popup")).then(t=>{"NVT_94"!=t&&(i&&n&&n.remove(),BEEThemeSP.$appendComponent.after(t),n=e(".bee_section__compare-popup"),i&&setTimeout(function(){n.addClass(g)},20),BEEThemeSP.Tooltip())}):i&&"modal"==BEEconfigs.compePopupDes&&BEEThemeSP.getToFetchSection(null,"text",t.replace("view=compare","section_id=compare-modal")).then(e=>{"NVT_94"!=e&&(i&&n&&n.remove(),BEEThemeSP.NTpopupInline(e,"",D,"bee-opening-cp"),a.trigger("modalbee:opened"),BEEThemeSP.Tooltip())}))}function D(){BEEThemeSP.Wishlist.updateAll(),BEEThemeSP.Compare.updateAll(),BEEThemeSP.ProductItem.reloadReview(),BEEThemeSP.Tooltip(),a.trigger("currency:update")}return{init:function(){isStorageSpdLocalAll&&!i&&(history.replaceState&&C&&window.history.replaceState({},document.title,p+"/?view=compare"),P(),C&&(window.isEmtyCompare&&window.isComparePerformed?localStorage.removeItem(m):(window.countComparePage!=t.length&&window.isComparePerformed&&(e(dt_count_wishlist).html(window.countComparePage),localStorage.setItem(bee_wis,window.listIDPrs)),!window.isEmtyCompare||window.isComparePerformed||""==t.toString()||IsDesignMode||(window.location.href=w))),a.on("click",s+"."+h,function(e){e.preventDefault(),e.stopPropagation(),x?null==BEEconfigs.compePopupDes||"canvas"==BEEconfigs.compePopupDes?n.addClass(g):"modal"==BEEconfigs.compePopupDes&&I(w,!0):window.location.href=w}),a.on("click",r,function(e){e.preventDefault(),window.location.href=w}),a.on("click",d,function(i){i.preventDefault(),null==BEEconfigs.compePopupDes||"canvas"==BEEconfigs.compePopupDes?n.removeClass(g):"modal"==BEEconfigs.compePopupDes&&a.trigger("modalbee:closed");let o=t.length;for(let n=0;n<o;n++){let i=t[n].replace("id:","");e(s+'[data-id="'+i+'"]').removeClass(h).find(".bee-text-pr").text(b),e(s+'[data-id="'+i+'"]').find(".bee-svg-pr-icon").html(T)}t=[],localStorage.setItem(m,t.toString()),y=0,e(c).html(y),P()}),a.on("click",u,function(e){e.preventDefault(),n.removeClass(g)}),null!=localStorage.getItem(m)&&""!=t.toString()&&I(w),a.on("click",s+":not(."+h+")",function(t){t.preventDefault(),t.stopPropagation();var n=e(this),i=n.data("id")||"",a="id:"+i,r=localStorage.getItem(m),l=!1;if(""!=i){if(n.addClass(f),null!=r&&r.length>0)(d=r.split(",")).unshift(a);else{var d=new Array;d.unshift(a)}if((d=d.filter(_)).length>o&&(d=d.splice(0,o),l=!0),localStorage.setItem(m,d.toString()),y=d.length,l){var u=e(s+h);u.removeClass(h).find(".bee-text-pr").text(b),u.find(".bee-svg-pr-icon").html(T),B()}else k(i,n);e(c).html(y),P(),I(w,!0),C&&(window.location.href=w)}}),a.on("click",l,function(i){i.preventDefault(),i.stopPropagation();var r=e(this),l=r.data("id"),d="id:"+l,u=localStorage.getItem(m);r.addClass(f);var p=(t=u.split(",")).indexOf(d);p>-1?(t=t.splice(0,o+1)).splice(p,1):t=t.splice(0,o),localStorage.setItem(m,t.toString()),r.removeClass(f),r.trigger("destroyTooltip"),e(".bee_compare_id_"+l).remove(),e(s+'[data-id="'+l+'"]').removeClass(h).find(".bee-text-pr").text(b),e(s+'[data-id="'+l+'"]').find(".bee-svg-pr-icon").html(T),y=t.length,e(c).html(y),P(),C&&""==t.toString()&&(e(".bee_compare_table").fadeTo(300,0),window.location.href=w),0==y&&(x?null==BEEconfigs.compePopupDes||"canvas"==BEEconfigs.compePopupDes?n.removeClass(g):"modal"==BEEconfigs.compePopupDes&&a.trigger("modalbee:closed"):window.location.href=w)}))},updateAll:B}}(),BEEThemeSP.Wishlist=function(){var t=BEEconfigs.wishlist_mode,i="2"==BEEconfigs.wis_atc_added,o="1"==t,r="2"==t,s=!(o||r),l=50,c="[data-link-wishlist]",d="data-action-wishlist",u="["+d+"]",m="data-remove-wishlist",f="["+m+"]",h="[data-count-wishlist]",g="bee_wis",v="is--pe-none",b="is--added",y=BEEProductStrings.browse_wishlist,E=BEEProductStrings.remove_wishlist,w=BEEProductStrings.add_to_wishlist,T=i?E:y,S=0,x=p+"/?view=wishlist&type=product&options[unavailable_products]=last&q=",C="",_="",P=BEEconfigs.wis_icon,B=BEEconfigs.wis_icon_remove,k=BEEconfigs.wis_icon_added,I="is--loading",D="/tools/the4/wishlist",A="",M=window.isPageWishlist,R=window.hasPaginateWishlist;if(r){var O=e("#wis_bee_list").html()||"";A=O.length>0?O.split(" "):[]}function N(e,t,n){return n.indexOf(e)===t}function $(){a.on("click",u+":not(."+b+")",function(t){t.preventDefault(),t.stopPropagation(),o?function(t){var n=t.data("id")||"",i="id:"+n,o=localStorage.getItem(g),a=!1;if(""==n)return;if(t.addClass(v),null!=o&&o.length>0)(r=o.split(",")).unshift(i);else{var r=new Array;r.unshift(i)}(r=r.filter(N)).length>l&&(r=r.splice(0,l),a=!0);if(localStorage.setItem(g,r.toString()),S=r.length,a){var s=e(u+b);s.removeClass(b).find(".bee-text-pr").text(w),s.find(".bee-svg-pr-icon").html(P),W()}else j(n,t);e(h).html(S),U(),M&&(window.location.href=C)}(e(this)):function(t){var n=t.attr("data-id")||"",i=t.attr("data-handle")||"ntbee"+n;if(""==n)return;t.addClass(I+" "+v),fetch(D,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({product_id:n,product_handle:i,action:"add"})}).then(function(e){return e.json()}).then(function(t){"success"==t.status?(A=JSON.parse(t.response.metafield.value).the4_ids,Array.isArray(A)||(A=A.split(",")),j(n),S=A.length,e(h).html(S),H(),M&&(window.location.href=C)):console.error(t.message||"Unknow error")}).catch(function(e){console.log("Error: "+e)}).finally(()=>{t.removeClass(I+" "+v)})}(e(this))})}function L(t){var n=t.data("id"),i="id:"+n,o=localStorage.getItem(g);t.addClass(v);var a=(_=o.split(",")).indexOf(i);a>-1?(_=_.splice(0,l+1)).splice(a,1):_=_.splice(0,l),localStorage.setItem(g,_.toString()),t.removeClass(v),t.trigger("destroyTooltip");var r=e(".bee-products-wishlist .bee-products");if(r.length>0){let i=t.closest(".bee-product");i=i[0]?i:e(`[data-remove-wishlist][data-id="${n}"]`).closest(".bee-product"),r.hasClass("isotopebee-enabled")?r.isotopebee("remove",i[0]).isotopebee("layout"):i.remove()}e(u+'[data-id="'+n+'"]').removeClass(b).find(".bee-text-pr").text(w),e(u+'[data-id="'+n+'"]').find(".bee-svg-pr-icon").html(P),S=_.length,e(h).html(S),U(),M&&(""==_.toString()||R)&&(e(".bee-products-wishlist").fadeTo(300,0),window.location.href=C)}function F(t){var n=t.attr("data-id")||"",i=t.attr("data-handle")||"ntbee"+n;t.addClass(I+" "+v),fetch(D,{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({product_id:n,product_handle:i,action:"add",_method:"DELETE"})}).then(function(e){return e.json()}).then(function(i){if("success"==i.status){_=JSON.parse(i.response.metafield.value).the4_ids,A=_,Array.isArray(A)||(A=A.split(",")),t.trigger("destroyTooltip");var o=e(".bee-products-wishlist .bee-products");if(o.length>0){let i=t.closest(".bee-product");i=i[0]?i:e(`[data-remove-wishlist][data-id="${n}"]`).closest(".bee-product"),o.hasClass("isotopebee-enabled")?o.isotopebee("remove",i[0]).isotopebee("layout"):i.remove()}e(u+'[data-id="'+n+'"]').removeClass(b).find(".bee-text-pr").text(w),e(u+'[data-id="'+n+'"]').find(".bee-svg-pr-icon").html(P),S=A.length,e(h).html(S),H(),M&&(""==_.toString()||R)&&(e(".bee-products-wishlist").fadeTo(300,0),window.location.href=x)}else console.error(i.message||"Unknow error")}).catch(function(e){console.log("Error: "+e)}).finally(()=>{t.removeClass(I+" "+v)})}function U(){var e=localStorage.getItem(g);if(null!=e){_=e.split(",");var t=e.replace(/,/g," OR "),i=encodeURI(t);C=x+i,BEEThemeSP.linkWishlist=C,n.trigger("update:mini_cart:wishlist")}}function H(){if(0!=(_=A).length){var e="id:"+_.join(" OR id:"),t=encodeURI(e);C=x+t,BEEThemeSP.linkWishlist=C,n.trigger("update:mini_cart:wishlist")}}function W(){if(!(!isStorageSpdLocalAll&&o||s)){if(o){var t=localStorage.getItem(g);if(null==t)return;var n=t.replace(/id:/g,"");_=n.split(","),S=""==t?0:_.length}else{if(""==(_=A).toString())return;S=_.length}if(_.forEach(function(e,t){j(e.replace("id:",""))}),M){var i=e(".bee-products-wishlist "+u);i.removeClass(b).removeAttr(d,"").attr(m,"").find(".bee-text-pr").html(E),i.find(".bee-svg-pr-icon").html(B)}e(h).html(S||window.countWishlistPage)}}function j(t,n){var i=e(u+'[data-id="'+t+'"]:not(.'+b+")");i.addClass(b).removeClass(v).find(".bee-text-pr").text(T),i.find(".bee-svg-pr-icon").html(k),n&&i.trigger("updateTooltip")}return{init:function(){!isStorageSpdLocalAll&&o||s||(history.replaceState&&M&&window.history.replaceState({},document.title,p+"/?view=wishlist"),o?U():H(),M&&(window.isEmtyWishlist&&window.isWishlistPerformed?localStorage.removeItem(g):(window.countWishlistPage!=_.length&&window.isWishlistPerformed&&(e(h).html(window.countWishlistPage),localStorage.setItem(g,window.listIDPrs)),!window.isEmtyWishlist||window.isWishlistPerformed||""==_.toString()||IsDesignMode||(window.location.href=C))),a.on("click",u+"."+b,function(t){t.preventDefault(),t.stopPropagation(),i?o?L(e(this)):F(e(this)):window.location.href=C}),a.on("click",c,function(e){0!=C.length&&(e.preventDefault(),window.location.href=C)}),$(),a.on("click",f,function(t){t.preventDefault(),t.stopPropagation(),o?L(e(this)):F(e(this))}))},updateAll:W}}();let T=location.search.indexOf("customer_posted=true")>-1||location.search.indexOf("newsletter&form_type=customer")>-1;BEEThemeSP.PopupPro=function(){var i=s.mfp_close,o=(s.mfp_loading,BEEconfigs.theme),r="open.popup",l="close.popup",c="is--loaded",d={click:"click.age",popup:"#bee-popup__age",CookiesName:`${o}_age_verify`},u={mouseleave:"mouseleave.exit",click:"click.exit",popup:"#bee-popup__exit",CookiesName:`${o}_exit`},p={scroll:"scroll.newsletter",click:"click.newsletter",popup:"#bee-popup__newsletter",canvas:".bee-newsletter_canvas",CookiesName:`${o}_newsletter`},m={popup:"#bee-popup__cookies-law",click:"click.cookies"},f=!0,h={popup:"#bee-popup__sales-tmp",close:"[data-close-sale]"};function g(){e.magnificPopupBee.open({items:{src:d.popup},type:"inline",closeOnBgClick:!1,closeBtnInside:!1,showCloseBtn:!1,enableEscapeKey:!1,removalDelay:500,tClose:i,callbacks:{beforeOpen:function(){this.st.mainClass="mfp-move-horizontal bee-age_pp_wrapper"},open:function(){d.$popup.find(".bee-age_verify_allowed").on(d.click,function(){if(d.date_of_birth){var t=parseInt(e("#ageyear").val()),n=parseInt(e("#agemonth").val()),i=parseInt(e("#ageday").val()),o=new Date(t+d.age_limit,n,i);(new Date).getTime()-o.getTime()<0?(d.$popup.addClass("bee-animated bee-shake"),window.setTimeout(function(){d.$popup.removeClass("bee-animated bee-shake")},1e3)):(CookiesBE.set(d.CookiesName,"confirmed",{expires:parseInt(d.day_next),path:"/"}),e.magnificPopupBee.close())}else CookiesBE.set(d.CookiesName,"confirmed",{expires:parseInt(d.day_next),path:"/"}),e.magnificPopupBee.close()}),d.$popup.find(".bee-age_verify_forbidden").on(d.click,function(){d.$popup.addClass("active_forbidden")})},beforeClose:function(){},close:function(){d.$popup.find(".bee-age_verify_allowed, .bee-age_verify_forbidden").off(d.click)}}})}function v(){e.magnificPopupBee.open({items:{src:u.popup},type:"inline",removalDelay:500,tClose:i,callbacks:{beforeOpen:function(){this.st.mainClass="mfp-move-horizontal bee-exit_pp_wrapper"},open:function(){if(e(".bee-exit_pp_wrapper .bee-product").length>0&&BEEThemeSP.reinitProductGridItem(),e(".bee-exit_pp_wrapper .flickitybee").length>0){var t=e(".bee-exit_pp_wrapper .flickitybee")[0];t.flickitybee=new BEEThemeSP.Carousel(t)}a.trigger("bee:hideTooltip"),a.trigger("currency:update")},beforeClose:function(){},close:function(){n.off(u.mouseleave),CookiesBE.set(u.CookiesName,"shown",{expires:u.day_next,path:"/"})}}})}function b(t,n){1===t?e.magnificPopupBee.open({items:{src:p.popup},type:"inline",removalDelay:500,tClose:i,callbacks:{beforeOpen:function(){this.st.mainClass="mfp-move-horizontal bee-newsletter_pp_wrapper"},open:function(){a.on("mail.subscribe.success",function(e){CookiesBE.set(p.CookiesName,"shown",{expires:p.day_next,path:"/"})})},beforeClose:function(){(e("[data-checked-newsletter]:checked").length>0||!e("[data-checked-newsletter]")[0])&&CookiesBE.set(p.CookiesName,"shown",{expires:p.day_next,path:"/"})},close:function(){}}}):n?(p.$content.addClass("on--show").removeClass("on--shown"),setTimeout(()=>{p.$content.removeClass("on--show")},500),(e("[data-checked-newsletter]:checked").length>0||!e("[data-checked-newsletter]")[0])&&CookiesBE.set(p.CookiesName,"shown",{expires:p.day_next,path:"/"})):(p.$content.addClass("on--show"),setTimeout(()=>{p.$content.removeClass("on--show").addClass("on--shown")},100))}function y(){m.$popup.removeClass("on--hide").addClass("on--show"),m.$popup.on(m.click,".bee-pp_cookies__accept-btn",function(e){e.preventDefault(),window.Shopify.customerPrivacy.setTrackingConsent(!0,w),document.addEventListener("trackingConsentAccepted",function(){CookiesBE.set(m.CookiesName,"accepted",{expires:m.day_next,path:"/"})}),f&&CookiesBE.set(m.CookiesName,"accepted",{expires:m.day_next,path:"/"})}),m.$popup.on(m.click,".bee-pp_cookies__decline-btn",function(e){e.preventDefault(),window.Shopify.customerPrivacy.setTrackingConsent(!1,w)})}function w(){m.$popup.addClass("on--hide").removeClass("on--show")}function S(){m.$popup=e(m.popup),0!=m.$popup.length&&(m.stts=m.$popup.data("stt"),m.day_next=m.stts.day_next||60,m.pp_version=m.stts.pp_version||1994,m.CookiesName=`${o}_cookies_${m.pp_version}`,f="1"==m.stts.show,"accepted"==CookiesBE.get(m.CookiesName)||m.$popup.hasClass(c)||(m.$popup.addClass(c),window.Shopify.loadFeatures([{name:"consent-tracking-api",version:"0.1"}],function(t){if(t)throw t;!function(){const t=window.Shopify.customerPrivacy.userCanBeTracked(),n=window.Shopify.customerPrivacy.getTrackingConsent();(!t&&"no_interaction"===n||IsDesignMode||f)&&(IsDesignMode?m.$popup.on(r,function(){m.$popup=e(m.popup),y()}).on(l,function(){w()}):y())}()})))}function x(e,t){return Math.floor(Math.random()*(t-e+1))+e}function C(e){var t=h.imageArray[e],n=BEEThemeSP.Images.getNewImageUrl(t,65),i=BEEThemeSP.Images.getNewImageUrl(t,130);h.$temp.find("[data-img-sale]").attr("src",n).attr("srcset",`${n} 1x, ${i} 2x`),h.$temp.find("[data-title-sale]").text(h.titleArray[e]),h.$temp.find("[data-href-sale]").attr("href",h.urlArray[e]),h.$temp.find("[data-action-quickview]").attr("data-id",h.idArray[e]),h.$temp.find("[data-location-sale]").text(h.locationArray[x(h.min,h.max2)]),h.$temp.find("[data-ago-sale]").text(h.timeArray[x(h.min,h.max3)])}function _(){a.trigger("bee:hideTooltip"),h.$temp&&h.$temp.removeClass(h.classUp).addClass(h.classDown).off("mouseenter mouseleave")}function P(){_(),h.starTimeout=setTimeout(function(){h.$temp&&h.$temp.remove(),BEEThemeSP.$appendComponent.after(h.temp),BEEThemeSP.Tooltip(),h.$temp=e(".bee-popup__sales"),"1"==h.ppType?(C(h.index),++h.index,h.index>h.max&&(h.index=0)):C(x(h.min,h.max)),h.time.START=(new Date).getTime(),h.time.END=h.time.START+h.stayTime,h.stayTimeout=setTimeout(function(){clearTimeout(h.stayTimeout),P()},h.stayTime),h.$progressbarSpan=e(".bee-pp-slpr-progressbar>span"),h.pauseOnHover&&h.$temp.on("mouseenter",function(e){h.resetOnHover?h.$progressbarSpan.css("animation-name","none"):h.time.REMAINING=h.time.END-(new Date).getTime(),clearTimeout(h.stayTimeout)}).on("mouseleave",function(e){h.resetOnHover?(h.time.REMAINING=h.stayTime,h.$progressbarSpan.css("animation-name","bee-ani-w")):h.time.END=(new Date).getTime()+h.time.REMAINING,h.stayTimeout=setTimeout(function(){P()},h.time.REMAINING)}),h.$temp.find(h.close).on("click",function(e){e.preventDefault(),_(),a.trigger("bee:hideTooltip"),h.$temp.off("mouseenter mouseleave"),clearTimeout(h.stayTimeout),clearTimeout(h.starTimeout)})},h.starTime)}return function(){S(),function(){p.$content=e(p.popup).length>0?e(p.popup):e(p.canvas);var i=`${o}_shown_pages`,s=CookiesBE.get(i),d=e(p.popup).length>0?1:2;if(s||(s=0),0==p.$content.length)return s++,void CookiesBE.set(i,s,{expires:194,path:"/"});if(p.stts=p.$content.data("stt"),p.pp_version=p.stts.pp_version,p.CookiesName=p.CookiesName+p.pp_version,!(!IsDesignMode&&"shown"==CookiesBE.get(p.CookiesName)||!p.stts.isMobile&&t.width()<768||p.$content.hasClass(c))){var u="1"==p.pp_version?"2":"1",m=`${o}_newsletter${u}`;if("shown"==CookiesBE.get(m)&&CookiesBE.remove(m),p.$content.addClass(c),p.day_next=p.stts.day_next,p.scroll_delay=p.stts.scroll_delay,p.after=p.stts.after,T){let e=p.$content.find(".bee-newsletter__success").length,t=p.$content.find(".bee-newsletter__error").length;(e>0||t>0)&&(e>0&&CookiesBE.set(p.CookiesName,"shown",{expires:p.day_next,path:"/"}),p.after="auto",p.stts.time_delay=500)}if(IsDesignMode)1===d?p.$content.on(r,function(){e.magnificPopupBee.instance.isOpen?(e.magnificPopupBee.close(),setTimeout(function(){p.$content.off(r).off(l),p.$content=e(p.popup),b(d),p.$content.on(r,function(){b(d)}).on(l,function(){e.magnificPopupBee.close()})},e.magnificPopupBee.instance.st.removalDelay+10)):b(d)}).on(l,function(){e.magnificPopupBee.close()}):(p.$content.on("shopify:block:select",function(e){b(d)}),p.$content.on("shopify:block:deselect",function(e){b(d,"close")}));else{var f=p.stts.number_pages;if(s<f)return s++,CookiesBE.set(i,s,{expires:194,path:"/"}),!1;CookiesBE.set(i,f,{expires:194,path:"/"}),"scroll"==p.after?t.on(p.scroll,function(){n.scrollTop()<p.scroll_delay||(b(d),t.off(p.scroll))}):setTimeout(function(){b(d)},p.stts.time_delay),e(document).on("click","[data-triger-newsletter]",function(e){e.preventDefault(),b(d)}).on("click","[data-bee-dismiss]",function(e){e.preventDefault(),b(d,"close")}),a.on("mail.subscribe.success",function(e){b(d),CookiesBE.set(p.CookiesName,"shown",{expires:p.day_next,path:"/"})})}}}(),u.$popup=e(u.popup),0!=u.$popup.length&&(!IsDesignMode&&"shown"==CookiesBE.get(u.CookiesName)||u.$popup.hasClass(c)||(u.stts=u.$popup.data("stt"),u.day_next=u.stts.day_next,u.$popup.addClass(c),IsDesignMode?u.$popup.on(r,function(){e.magnificPopupBee.instance.isOpen?(e.magnificPopupBee.close(),setTimeout(function(){u.$popup.off(r).off(l),u.$popup=e(u.popup),v(),u.$popup.on(r,function(){v()}).on(l,function(){e.magnificPopupBee.close()})},e.magnificPopupBee.instance.st.removalDelay+10)):v()}).on(l,function(){e.magnificPopupBee.close()}):n.on(u.mouseleave,function(t){t.clientY<60&&0==e(".mfp-content").length&&v()}))),d.$popup=e(d.popup),0!=d.$popup.length&&(!IsDesignMode&&"confirmed"==CookiesBE.get(d.CookiesName)||d.$popup.hasClass(c)||(d.stts=d.$popup.data("stt"),d.age_limit=d.stts.age_limit,d.date_of_birth=d.stts.date_of_birth,d.day_next=d.stts.day_next,d.$popup.addClass(c),IsDesignMode?d.$popup.on(r,function(){e.magnificPopupBee.instance.isOpen?(e.magnificPopupBee.close(),setTimeout(function(){d.$popup.off(r).off(l),d.$popup=e(d.popup),g(),d.$popup.on(r,function(){g()}).on(l,function(){e.magnificPopupBee.close()})},e.magnificPopupBee.instance.st.removalDelay+10)):g()}).on(l,function(){e.magnificPopupBee.close()}):g())),function(){if(h.$popup=e(h.popup),0==h.$popup.length)return;let n=e("#bee-popup__sales-JSON");h.stts=E(n.html()),n.remove(),!h.stts.isMobile&&t.width()<768||h.$popup.hasClass(c)||(h.$popup.addClass(c),h.temp=h.$popup.html(),h.starTime=h.stts.starTime*h.stts.starTimeUnit,h.stayTime=h.stts.stayTime*h.stts.stayTimeUnit,h.index=0,h.limit=h.stts.limit,h.max=h.stts.max,h.min=0,h.classUp=h.stts.classUp,h.classDown=h.stts.classDown[h.classUp],h.ppType=h.stts.ppType,h.pauseOnHover=h.stts.pauseOnHover,h.resetOnHover=h.stts.resetOnHover,h.idArray=h.stts.idArray,h.titleArray=h.stts.titleArray,h.urlArray=h.stts.urlArray,h.locationArray=h.stts.locationArray,h.timeArray=h.stts.timeArray,h.imageArray=h.stts.imageArray,h.max=h.urlArray.length-1,h.max2=h.locationArray.length-1,h.max3=h.timeArray.length-1,h.starTimeout,h.stayTimeout,h.time={},IsDesignMode?(BEEThemeSP.$appendComponent.after(h.temp),h.$temp=e(".bee-popup__sales"),h.$temp.hide(),BEEThemeSP.Tooltip(),e(h.close).on("click",function(e){e.preventDefault(),_()}),h.$popup.on(r,function(){h.$temp.show(),h.$temp.addClass(h.classUp).removeClass(h.classDown)}).on(l,function(){_()})):(h.$popup.remove(),P()))}()}}(),BEEThemeSP.PopupFetch=function(){let e=BEErequest.path,t=("/"!=e?e:"")+"/?section_id=popups";T&&(t=t+"&"+location.href.split("/?")[1]),BEEThemeSP.getToFetchSection(null,"text",t).then(e=>{"NVT_94"!=e&&(BEEThemeSP.$appendComponent.after(e),BEEThemeSP.PopupPro(),BEEThemeSP.PlatFormMail(),BEEThemeSP.PopupMFP())})},BEEThemeSP.PlatFormMail=function(){var t={loading:"is--loading",enabled:"is--enabled",errorCheckbox:"is--error-checkbox",errorEmail:"is--error-email"},n={klaviyo:"[data-bee-klaviyo-submit]"},i={click:"click.mail",keyup:"keyup.mail"},o="[data-agreeMail-checkbox]";return function(){"4"==m&&e(`[data-bee-mailChimp-ajax]:not(.${t.enabled})`).addClass(t.enabled).submit(function(n){n.preventDefault();var i=e(this),o=i.find("[data-new-response-form]"),r=i.find("[data-bee-mailChimp-submit]"),s=o.find("[data-new-response-success]"),l=o.find("[data-new-response-error]");r.addClass(t.loading),e.ajax({type:"GET",url:i.attr("action"),data:i.serialize(),cache:!1,dataType:"jsonp",jsonp:"c",contentType:"application/json; charset=utf-8",error:function(e){r.removeClass(t.loading);try{var n=e.replace("0 - ","").replace("1 - ","").replace("2 - ","");l.html(n).slideDown(100)}catch(e){}},success:function(e){r.removeClass(t.loading);try{var n=e.msg.replace("0 - ","").replace("1 - ","").replace("2 - ","");"success"!=e.result?(s.slideUp(100),l.html(n).slideDown(100)):(a.trigger("mail.subscribe.success"),l.slideUp(100),s.slideDown(100))}catch(e){}}})}),"3"==m&&$script("//www.klaviyo.com/media/js/public/klaviyo_subscribe.js",function(){e.each(e(`[data-bee-klaviyo-ajax]:not(.${t.enabled})`),function(){var i=e(this),o=i.attr("data-brand")||"Kalles Klaviyo";KlaviyoSubscribe.attachToForms("#"+i.attr("id"),{custom_success_message:!0,extra_properties:{$source:"Newsletter Popup",Brand:o},success:function(e){a.trigger("mail.subscribe.success"),e.find(n.klaviyo).removeClass(t.loading)}}),i.addClass(t.enabled).submit(function(i){e(this).find(n.klaviyo).addClass(t.loading)})}),a.on("klaviyo.subscribe.success",function(e){a.trigger("mail.subscribe.success")}),a.on("klaviyo.subscribe.success klaviyo.subscribe.error",function(i){e(i.target).find(n.klaviyo).removeClass(t.loading)})}),0!=e(o).length&&(e("[data-agreeMail-btn]").off(i.click).on(i.click,function(n){var i=e(this).closest("form");i.find(`[type="checkbox"]${o}`).is(":checked")||(n.preventDefault(),n.stopPropagation(),i.addClass(t.errorCheckbox),i.find('[type="email"]').val().length<1&&i.addClass(t.errorEmail))}),e(o).off(i.click).on(i.click,function(n){e(this).is(":checked")&&e(this).closest("form").removeClass(t.errorCheckbox)}),e('[data-form-mail-agree] [type="email"]').off(i.keyup).on(i.keyup,function(n){var i=e(this).closest("form");e(this).val().length<1?i.addClass(t.errorEmail):i.removeClass(t.errorEmail)})),"?contact%5Btags%5D=newsletter&form_type=customer"==location.search&&e("[data-new-response-form]").html(`<div class="bee-newsletter__error">${s.error_exist}</div>`).slideDown(100)}}();var S=function(){var t="[data-groups-total-price]",n="[data-groups-pr-item]",i="change.groups",o="is--checked";function r(i){this.$form=e(i),this.$totalPrice=this.$form.find(t),this.ArrayPrice=[],this.ArrayComparePrice=[];let o=this;this.$form.find(n).each(function(e){o._updateItemPrice(this,e)}),o._updateTotalPrice(),o._eventListeners()}return r.prototype=Object.assign({},r.prototype,{_eventListeners:function(){let t=this;this.$form.on(i,"select[data-groups-pr-sl]",function(){t._updateItem(this)}),this.$form.on(i,"[data-groups-pr-ck]",function(){let i=e(this).closest(n),a=i.index(),r=t.$form.find(`[data-groups-img="${a}"]`),s=i.find('[name*="items[]"]');this.checked?(i.addClass(o),t._updateItemPrice(i,a),r.fadeIn(300),s.prop("disabled",!1)):(i.removeClass(o),t.ArrayPrice[a]=0,t.ArrayComparePrice[a]=0,r.fadeOut(300),s.prop("disabled",!0)),t._updateTotalPrice()}),this.$form.on(i,"[data-groups-qty-value]",function(){let i=e(this).closest(n),o=i.index();t._updateItemPrice(i[0],o),t._updateTotalPrice()})},_updateItemPrice:function(t,n){let i=e(t),o=i.find("[data-groups-pr-sl]"),a=parseInt(i.find("[data-groups-qty-value]").val()),r=parseInt(o.attr("data-price")),s=parseInt(o.attr("data-cpprice"));r*=a,s*=a,this.ArrayPrice[n]=r,this.ArrayComparePrice[n]=s>r?s:r},_updateItem:function(t){let i=e(t),o=i.find(":selected"),a=i.closest(n),r=a.data("index"),s=void 0===r?a.index():r,l=o.data("img"),c=o.data("max"),d=a.find("[data-groups-qty-value]"),u=d.val(),p=this.$form.find(`[data-groups-img="${s}"] img`),m=parseInt(o.data("price")),f=parseInt(o.data("cpprice")),h=a.find("[data-groups-item-price]");d.attr("max",c),p.attr("data-orginal")!=l&&p.attr({"data-src":l,"data-orginal":l}).removeClass("lazyloadbeeed").addClass("lazyloadbee"),f=f>m?f:m,m*=u,f*=u,this.ArrayPrice[s]==m&&this.ArrayComparePrice[s]==f||(i.attr({"data-price":m,"data-cpprice":f}),this.ArrayPrice[s]=m,this.ArrayComparePrice[s]=f,f>m?void 0!==BEEProductStrings.price_template?h.html(BEEProductStrings.price_template.replace("INS",BEEThemeSP.Currency.formatMoney(m)).replace("DEL",BEEThemeSP.Currency.formatMoney(f))):h.html("<del>"+BEEThemeSP.Currency.formatMoney(f)+"</del> <ins>"+BEEThemeSP.Currency.formatMoney(m)+"</ins>"):h.html(BEEThemeSP.Currency.formatMoney(m)),this._updateTotalPrice())},_updateTotalPrice:function(){let e=this.ArrayPrice.reduce((e,t)=>e+t,0),t=this.ArrayComparePrice.reduce((e,t)=>e+t,0);t>e?void 0!==BEEProductStrings.price_template?this.$totalPrice.html(BEEProductStrings.price_template.replace("INS",BEEThemeSP.Currency.formatMoney(e)).replace("DEL",BEEThemeSP.Currency.formatMoney(t))):this.$totalPrice.html("<del>"+BEEThemeSP.Currency.formatMoney(t)+"</del> <ins>"+BEEThemeSP.Currency.formatMoney(e)+"</ins>"):this.$totalPrice.html(BEEThemeSP.Currency.formatMoney(e)),a.trigger("currency:update")}}),r}();BEEThemeSP.initGroupsProduct=function(){0!=e("[data-groups-pr-form]:not(.is--enabled)").length&&e("[data-groups-pr-form]:not(.is--enabled)").each(function(t){e(this).addClass("is--enabled"),new S(this)})},BEEThemeSP.goToID=function(){var t,n,i="[data-go-id]",o=(e("html,body"),0);return function(){0!=(t=e(i)).length&&t.click(function(t){t.preventDefault(),t.stopPropagation();let i=e(this),a=i.data("go-id")||i.attr("href"),r=e(a),s=i.data("offset")||100;0!=r.length&&(r.is(":hidden")&&(e(`[data-bee-tab-item][href="${a}"]:visible`).trigger("click"),o=100),clearTimeout(n),n=setTimeout(function(){window.scrollTo({behavior:"smooth",top:r.offset().top-s})},o))})}}(),BEEThemeSP.CanvasConfetti=function(){var e,n,i,o,a,r,s=window.innerWidth<988?75:150,l=[],c=0,d=!0,u=!0,p=!1,m={colorOptions:["DodgerBlue","OliveDrab","Gold","pink","SlateBlue","lightblue","Violet","PaleGreen","SteelBlue","SandyBrown","Chocolate","Crimson"],colorIndex:0,colorIncrementer:0,colorThreshold:10,getColor:function(){return this.colorIncrementer>=10&&(this.colorIncrementer=0,this.colorIndex++,this.colorIndex>=this.colorOptions.length&&(this.colorIndex=0)),this.colorIncrementer++,this.colorOptions[this.colorIndex]}};function f(e){var t,a;this.x=Math.random()*i,this.y=Math.random()*o-o,this.r=(t=10,a=30,Math.floor(Math.random()*(a-t+1)+t)),this.d=Math.random()*s+10,this.color=e,this.tilt=Math.floor(10*Math.random())-10,this.tiltAngleIncremental=.07*Math.random()+.05,this.tiltAngle=0,this.draw=function(){return n.beginPath(),n.lineWidth=this.r/2,n.strokeStyle=this.color,n.moveTo(this.x+this.tilt+this.r/4,this.y),n.lineTo(this.x+this.tilt,this.y+this.tilt+this.r/4),n.stroke()}}function h(e,t){(e.x>i+20||e.x<-20||e.y>o)&&d&&(t%5>0||t%2==0?v(e,Math.random()*i,-10,Math.floor(10*Math.random())-10):Math.sin(c)>0?v(e,-5,Math.random()*o,Math.floor(10*Math.random())-10):v(e,i+5,Math.random()*o,Math.floor(10*Math.random())-10))}function g(e,t){e.tiltAngle+=e.tiltAngleIncremental,e.y+=(Math.cos(c+e.d)+3+e.r/2)/2,e.x+=Math.sin(c),e.tilt=15*Math.sin(e.tiltAngle-t/3)}function v(e,t,n,i){e.x=t,e.y=n,e.tilt=i}function b(){clearTimeout(a),clearTimeout(r)}function y(){u=!0,null!=n&&(n.clearRect(0,0,i,o),e.style.display="none")}return window.requestAnimFrameBE=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){return window.setTimeout(e,1e3/60)},function(){if(p)return b(),y(),a=setTimeout(function(){d=!0,u=!1,function(){e.style.display="block",l=[],u=!1;for(var t=0;t<s;t++){var a=m.getColor();l.push(new f(a))}i=window.innerWidth,o=window.innerHeight,e.width=i,e.height=o,function e(){return u?null:(r=requestAnimFrameBE(e),function(){n.clearRect(0,0,i,o);for(var e=[],t=0;t<s;t++)a=t,e.push(l[a].draw());var a;return function(){var e,t=0;c+=.01,.1;for(var n=0;n<s;n++){if(e=l[n],u)return;!d&&e.y<-15?e.y=o+100:(g(e,n),e.y<=o&&t++,h(e,n))}0===t&&y()}(),e}())}()}()},100),void setTimeout(function(){d=!1,b()},3500);BEEThemeSP.$appendComponent.after('<canvas id="confettiCanvas" style="position:fixed;top:0;left:0;display:none;z-index:9999;pointer-events: none;"></canvas>'),e=document.getElementById("confettiCanvas"),n=e.getContext("2d"),i=window.innerWidth,o=window.innerHeight,e.width=i,e.height=o,t.resize(function(){i=window.innerWidth,o=window.innerHeight,e.width=i,e.height=o}),p=!0}}(),BEEThemeSP.ToggleClass=void e(document).on("click","[data-toggle-class]",function(t){var n=e(this).attr("data-toggle-class"),i=e(this).attr("data-toggle-trigger");e(this).toggleClass(n),e(i).toggleClass(n)})}(jQuery_BENT),jQuery_BENT(document).ready(function(){BEEThemeSP.Hover(),BEEThemeSP.Header.stickyInit(),BEEThemeSP.MobileNav(),BEEThemeSP.Cart.init(),BEEThemeSP.agreeForm(),BEEThemeSP.Login(),BEEThemeSP.Compare.init(),BEEThemeSP.Wishlist.init(),BEEThemeSP.recentlyViewed(),BEEThemeSP.productRecommendations(),BEEThemeSP.ProductItem.init(),BEEThemeSP.ProductItem.loadjsRevew(),BEEThemeSP.Tooltip(),BEEThemeSP.ProductItem.clickMoreSwatches(),BEEThemeSP.ProductItem.swatchesClickHover(),BEEThemeSP.ProductItem.resizeObserver(),BEEThemeSP.ProductItem.initQuickVS(),BEEThemeSP.RenderRefresh(),BEEThemeSP.ProductAjax.init(),BEEThemeSP._initBundlePrs(),BEEThemeSP.BeeQuantityAdjust(),BEEThemeSP.PhotoSwipe.gallery(),BEEThemeSP.PhotoSwipe.images(),BEEThemeSP.PhotoSwipe.image(),BEEThemeSP.Video.initPoster(),BEEThemeSP.initLoadMore(),(jQuery_BENT(".bee-section-main [data-ntajax-container]").length>0||IsDesignMode)&&$script(BEEconfigs.script7,"bee:facets"),BEEThemeSP.instagram(),BEEThemeSP.sideBarInit(),BEEThemeSP.LookBook(),BEEThemeSP.initGroupsProduct(),IsDesignMode?BEEThemeSP.PopupPro():setTimeout(function(){BEEThemeSP.PopupFetch()},686),setTimeout(function(){BEEThemeSP.PlatFormMail(),BEEThemeSP.goToID()},500),BEEThemeSP.isHover&&jQuery_BENT("[data-zoom-options]").length>0&&$script(BEEconfigs.script5,"bee:zoom"),document.addEventListener("theme:hover",function(e){jQuery_BENT("[data-zoom-options]").length>0&&$script(BEEconfigs.script5,"bee:zoom")}),BEEThemeSP.BackToTop(),BEEThemeSP.Header.init(),BEEThemeSP.currencyForm(),"2"==BEEconfigs.currency_type&&$script(BEEconfigs.script12a),"none"!=BEEconfigs.script11&&$script(BEEconfigs.script11,"bee:customjs")}),jQuery_BENT(window).resize(function(){BEEThemeSP.ProductItem.recalculateSwatches(!0)});