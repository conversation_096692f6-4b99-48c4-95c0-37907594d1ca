{%- case icon_name -%}
    {%- when 'cup' -%}
        <svg class="{{ icon_class }} bee-icon-cup" viewBox="0 0 22 22" ><path id="Cup" class="st1" d="M16.57,7.62L16.57,7.62C19.57,6.35,20,0,20,0H2c0,0,0.43,6.35,3.43,7.62l0,0
    c0,2.79,1.74,5.08,4.57,5.08v3.38H7v2.54l-3,0V22h14v-3.38h-3v-2.54h-3v-3.38C14.83,12.69,16.57,10.41,16.57,7.62z"/>
                </svg>
    {%- when 'rugby' -%}
        <svg class="{{ icon_class }} bee-icon-rugby" viewBox="0 0 22 22" ><path id="Rugby" class="st1" d="M0.74,15.94L0.33,15.2c-0.53,3.33-0.34,6.07,0.03,6.44c0.37,0.37,3.12,0.56,6.44,0.03
    c-0.24-0.13-0.49-0.27-0.74-0.41C4.17,20.21,1.79,17.83,0.74,15.94z M12.52,1.88l-1.03-0.69C9.08,1.95,6.7,3.13,4.92,4.92
    C3.14,6.7,1.95,9.08,1.19,11.49c0.22,0.33,0.45,0.67,0.69,1.03c1.68,2.52,5.08,5.92,7.6,7.6l1.03,0.69
    c2.41-0.76,4.79-1.94,6.57-3.73c1.78-1.78,2.97-4.16,3.73-6.57c-0.22-0.33-0.45-0.67-0.69-1.03C18.44,6.96,15.04,3.56,12.52,1.88z
     M15.34,9.26C15.19,9.41,15,9.48,14.8,9.48c-0.19,0-0.39-0.07-0.54-0.22l-0.22-0.22L13.6,9.48l0.22,0.22c0.3,0.3,0.3,0.78,0,1.07
    C13.67,10.93,13.47,11,13.28,11c-0.19,0-0.39-0.07-0.54-0.22l-0.22-0.22L12.07,11l0.22,0.22c0.3,0.3,0.3,0.78,0,1.07
    c-0.15,0.15-0.34,0.22-0.54,0.22c-0.19,0-0.39-0.07-0.54-0.22L11,12.07l-0.45,0.45l0.22,0.22c0.3,0.3,0.3,0.78,0,1.08
    c-0.15,0.15-0.34,0.22-0.54,0.22c-0.19,0-0.39-0.07-0.54-0.22L9.48,13.6l-0.45,0.45l0.22,0.22c0.3,0.3,0.3,0.78,0,1.07
    c-0.15,0.15-0.34,0.22-0.54,0.22c-0.19,0-0.39-0.07-0.54-0.22l-1.52-1.52c-0.3-0.3-0.3-0.78,0-1.08c0.3-0.3,0.78-0.3,1.07,0
    l0.22,0.22l0.45-0.45L8.18,12.3c-0.3-0.3-0.3-0.78,0-1.07c0.3-0.3,0.78-0.3,1.07,0l0.22,0.22L9.93,11L9.7,10.78
    c-0.3-0.3-0.3-0.78,0-1.07c0.3-0.3,0.78-0.3,1.07,0L11,9.93l0.45-0.44l-0.22-0.22c-0.3-0.3-0.3-0.78,0-1.07c0.3-0.3,0.78-0.3,1.07,0
    l0.22,0.22l0.45-0.45l-0.22-0.22c-0.3-0.3-0.3-0.78,0-1.07c0.3-0.3,0.78-0.3,1.07,0l1.52,1.52C15.63,8.48,15.63,8.96,15.34,9.26z
     M21.64,0.36c-0.37-0.37-3.12-0.56-6.44-0.03c0.24,0.13,0.49,0.27,0.74,0.41c1.89,1.05,4.27,3.43,5.32,5.32l0.41,0.74
    C22.2,3.48,22.01,0.73,21.64,0.36z"/></svg></svg>
    {%- when 'bowlling' -%}
        <svg class="{{ icon_class }} bee-icon-bowlling" viewBox="0 0 22 22"><path id="Bowlling" class="st1" d="M10.1,5.74C10.3,4.4,11,3.54,11,2.39C11,1.34,10,0,8,0C6,0,5,1.34,5,2.39
    C5,3.54,5.7,4.4,5.9,5.74H10.1z M8,16.74c0-2.3,1.3-4.4,3.3-5.45c-0.2-0.67-0.5-1.34-0.8-1.72H5.4C4.8,10.71,4,12.82,4,15.3
    C4,19.13,6,22,6,22h4c0,0,0.1-0.19,0.3-0.48C8.9,20.37,8,18.65,8,16.74z M6,6.7h4v1.91H6V6.7z M14.5,11.48c-3,0-5.5,2.39-5.5,5.26
    C9,19.61,11.5,22,14.5,22c3,0,5.5-2.39,5.5-5.26C20,13.87,17.5,11.48,14.5,11.48z M13.5,16.26c-0.3,0-0.5-0.19-0.5-0.48
    c0-0.29,0.2-0.48,0.5-0.48c0.3,0,0.5,0.19,0.5,0.48C14,16.07,13.8,16.26,13.5,16.26z M14.5,14.35c-0.3,0-0.5-0.19-0.5-0.48
    c0-0.29,0.2-0.48,0.5-0.48c0.3,0,0.5,0.19,0.5,0.48C15,14.16,14.8,14.35,14.5,14.35z M15.5,16.26c-0.3,0-0.5-0.19-0.5-0.48
    c0-0.29,0.2-0.48,0.5-0.48c0.3,0,0.5,0.19,0.5,0.48C16,16.07,15.8,16.26,15.5,16.26z"/></svg>
    {%- when 'baseball' -%}
        <svg class="{{ icon_class }} bee-icon-baseball" viewBox="0 0 22 22" ><path id="Baseball" class="st1" d="M16.78,20.65c0,0.15,0,0.23,0,0.38c1.6-0.15,2.82-1.44,2.97-2.96c-0.15,0-0.23,0-0.38,0
    C18,17.99,16.78,19.2,16.78,20.65z M8.64,10.92l2.51,2.51l-6.39,5.55l-1.75-1.67L8.64,10.92z M4.23,19.51c0.08,0.46,0,0.91-0.3,1.22
    c-0.46,0.46-1.14,0.46-1.6,0l-1.07-1.06c-0.46-0.46-0.46-1.14,0-1.6c0.38-0.38,0.84-0.46,1.22-0.3L4.23,19.51z M19.83,17.31
    c-0.15,0-0.23,0-0.38,0c-1.9,0-3.42,1.52-3.42,3.42c0,0.15,0,0.23,0,0.38c-1.6-0.15-2.89-1.44-3.04-3.04c0.15,0,0.23,0,0.38,0
    c1.9,0,3.42-1.52,3.42-3.42c0-0.15,0-0.23,0-0.38C18.38,14.42,19.68,15.71,19.83,17.31z M13.44,17.15c-0.15,0-0.23,0-0.38,0
    c0.15-1.52,1.37-2.81,2.97-2.96c0,0.15,0,0.23,0,0.38C16.02,16.09,14.81,17.23,13.44,17.15z M20.13,5.45l-8.45,7.45L9.1,10.32
    l7.46-8.43c0.91-0.99,2.44-1.06,3.42-0.08l0.23,0.23C21.2,3.02,21.2,4.54,20.13,5.45z"/></svg>
    {%- when 'basket-ball' -%}
        <svg class="{{ icon_class }} bee-icon-basket-ball" viewBox="0 0 22 22"><path id="Basket_ball" class="st1" d="M8.44,6.23c-0.3-0.04-0.59-0.07-0.88-0.1C6.92,7.49,6.48,8.95,6.3,10.5
                c1.98-0.01,5.27,0.81,6.32,6.02c0.52,2.58,1.75,3.89,3.82,4.04c1.71-0.97,3.13-2.39,4.11-4.1C18.86,11.19,14.29,7.07,8.44,6.23z
                 M5.29,11.5c-1.51,0.23-2.56,0.83-3.13,1.77c-0.8,1.34-0.48,3.09-0.24,3.95c1.53,2.23,3.86,3.87,6.58,4.5
                c-2.04-2.55-3.23-5.84-3.23-9.52C5.27,11.97,5.28,11.74,5.29,11.5z M7.02,5.18c0.42-0.78,0.9-1.52,1.44-2.22
                c-1.3-0.45-1.69-1.4-1.73-2.11C4.07,1.98,1.95,4.12,0.84,6.78c1.69-1.05,3.62-1.61,5.75-1.61C6.73,5.18,6.88,5.18,7.02,5.18z
                 M5.36,10.56C5.54,8.99,5.95,7.49,6.57,6.1c-2.47,0-4.53,0.78-6.22,2.11C0.12,9.1,0,10.04,0,11c0,1.4,0.26,2.74,0.74,3.98
                c0.04-0.72,0.22-1.48,0.63-2.18C2.11,11.56,3.46,10.81,5.36,10.56z M9.11,2.2c0.73-0.81,1.54-1.55,2.43-2.18C11.36,0,11.18,0,11,0
                C8.89,0,7.66,0.58,7.66,0.58C7.64,0.71,7.51,1.83,9.11,2.2z M19.92,4.57l-0.02,0.01c-2.06-2.58-5.39-1.24-5.42-1.22
                c-0.9,0.37-2.55,0.32-5.04-0.16C8.92,3.84,8.44,4.53,8.03,5.25c0.18,0.02,0.36,0.04,0.55,0.07c4.19,0.6,7.9,2.8,10.44,6.2
                c0.88,1.18,1.59,2.46,2.11,3.79C21.69,13.99,22,12.53,22,11C22,8.6,21.23,6.38,19.92,4.57z M14.13,2.51
                c0.77-0.32,2.08-0.59,3.44-0.33c-1.34-1-2.91-1.7-4.62-2.01C11.93,0.8,11,1.55,10.17,2.4C12.59,2.83,13.66,2.71,14.13,2.51z
                 M11.72,16.7c-0.91-4.54-3.62-5.33-5.5-5.29c-0.01,0.26-0.02,0.52-0.02,0.78c0,3.78,1.36,7.22,3.72,9.75C10.27,21.98,10.63,22,11,22
                c1.43,0,2.8-0.27,4.06-0.77C13.33,20.65,12.21,19.14,11.72,16.7z"/></svg>
    {%- when 'footwear' -%}
        <svg class="{{ icon_class }} bee-icon-footwear" viewBox="0 0 22 22"><path id="Footwear" class="st1" d="M2.17,15.06c0.18-0.01,0.37-0.01,0.57-0.01c0.3,0,0.56,0.01,0.82,0.02l-0.26,1.44
                c-0.01,0.06-0.06,0.1-0.12,0.1H1.9c-0.06,0-0.11-0.04-0.12-0.1l-0.3-1.45c0.08,0,0.15,0,0.23,0C1.85,15.07,2,15.07,2.17,15.06z
                 M6.68,16.54c-0.01,0.06-0.06,0.1-0.12,0.1H5.29c-0.06,0-0.11-0.04-0.12-0.1l-0.28-1.4c0.56,0.02,1.26,0.05,2.04,0.07L6.68,16.54z
                 M16.53,16.73c-0.01,0.06-0.06,0.1-0.12,0.1h-1.27c-0.06,0-0.11-0.04-0.12-0.1l-0.27-1.31c0.8,0.02,1.49,0.03,2.01,0.04L16.53,16.73
                z M19.72,15.18l-0.28,1.56c-0.01,0.06-0.06,0.1-0.12,0.1h-1.27c-0.06,0-0.11-0.04-0.12-0.1l-0.26-1.27
                c0.68-0.03,1.38-0.17,2.03-0.38C19.71,15.11,19.72,15.14,19.72,15.18z M1.94,6.07c0.49,0,0.44,1.49,1.49,1.87
                C3.93,8.13,4.42,8.2,4.85,8.2c0.76,0,1.34-0.22,1.56-0.44C6.74,7.43,3.66,6.39,4.32,5.5l1.01-1.35C5.4,4.05,5.51,4,5.61,4
                c0.07,0,0.14,0.02,0.2,0.07L9.2,6.56c0,0,1.29,0,1.52,0.76c0,0,0.04-0.01,0.1-0.01c0.28,0,1.04,0.06,1.25,0.7
                c0,0,3.2,0.63,5.11,1.66c1.91,1.02,4.83,0.96,4.83,2.59s-2.58,2.72-4.63,2.72c-2.05,0-12.33-0.31-13.77-0.39
                c-0.31-0.02-0.6-0.02-0.87-0.02c-0.38,0-0.73,0.01-1.03,0.01c-1.09,0-1.65-0.14-1.67-1.39C0,11.33-0.22,8.48,1.18,6.69
                C1.54,6.25,1.78,6.07,1.94,6.07z M8.61,13.27h1.7c0.03,0,0.06-0.01,0.08-0.03l3.68-3.37c0.07-0.07,0.04-0.19-0.05-0.21l-1.28-0.34
                c-0.04-0.01-0.09,0-0.11,0.03l-4.1,3.72C8.44,13.13,8.49,13.27,8.61,13.27z M5.19,13.27l1.79-0.02c0.03,0,0.06-0.01,0.08-0.03
                l4.56-4.15c0.07-0.07,0.05-0.19-0.05-0.22l-1.28-0.34c-0.04-0.01-0.08,0-0.11,0.03L5.1,13.05C5.02,13.13,5.07,13.27,5.19,13.27z"/></svg>
    {%- when 'football' -%}
        <svg class="{{ icon_class }} bee-icon-football" viewBox="0 0 22 22"><path id="Football" class="st1" d="M11,0C4.95,0,0,4.95,0,11c0,6.05,4.95,11,11,11c6.05,0,11-4.95,11-11C22,4.95,17.05,0,11,0z
                 M18.44,16.81l-0.2-1.32l-3.39,0.73l-1.83,3.02l1.25,0.6c-1.02,0.38-2.12,0.59-3.27,0.59c-1.15,0-2.25-0.21-3.27-0.59l1.25-0.6
                l-1.83-3.02l-3.39-0.73l-0.2,1.32c-1.25-1.6-2-3.61-2-5.81c0-0.1,0.01-0.2,0.02-0.31l0.9,0.95l2.38-2.66L4.49,5.5L3.15,5.75
                c1.23-1.84,3.1-3.22,5.29-3.83l-0.65,1.2L11,4.49l3.21-1.38l-0.65-1.2c2.19,0.61,4.06,1.99,5.29,3.83L17.51,5.5l-0.37,3.48
                l2.38,2.66l0.9-0.95c0,0.1,0.02,0.2,0.02,0.31C20.44,13.2,19.69,15.21,18.44,16.81z M11,7.33l3.85,2.84l-1.47,4.49H8.62l-1.47-4.49
                L11,7.33z"/></svg>
    {%- when 'cyling' -%}
       <svg class="{{ icon_class }} bee-icon-cyling"  viewBox="0 0 22 22" ><path id="Cyling" class="st1" d="M9.95,4.32L5.38,9.18c-0.33,0.48-0.21,1.14,0.28,1.47l4.6,3.14v3.05c0,0.58,0.46,1.07,1.05,1.08
                c0.6,0.01,1.08-0.47,1.08-1.06v-3.62c0-0.35-0.17-0.68-0.46-0.87l-2.95-2.01l2.49-2.62l1.27,2.88c0.17,0.38,0.55,0.63,0.97,0.63
                h3.88c0.58,0,1.07-0.46,1.08-1.04c0.01-0.59-0.47-1.08-1.06-1.08h-3.2c-0.42-0.95-0.87-1.9-1.26-2.86c-0.2-0.49-0.31-0.7-0.68-1.04
                c-0.07-0.07-0.59-0.54-1.05-0.96C11,3.89,10.34,3.91,9.95,4.32z M14.31,0c1.35,0,2.44,1.09,2.44,2.43c0,1.34-1.09,2.43-2.44,2.43
                s-2.44-1.09-2.44-2.43C11.87,1.09,12.97,0,14.31,0z M4.49,12.07c2.48,0,4.49,2,4.49,4.47C8.98,19,6.97,21,4.49,21
                C2.01,21,0,19,0,16.53C0,14.07,2.01,12.07,4.49,12.07z M17.51,12.07c2.48,0,4.49,2,4.49,4.47C22,19,19.99,21,17.51,21
                s-4.49-2-4.49-4.47C13.02,14.07,15.03,12.07,17.51,12.07z"/></svg>
    {%- when 'tennis' -%}
        <svg class="{{ icon_class }} bee-icon-tennis" viewBox="0 0 22 22"><path id="Tennis" class="st1" d="M20.44,1.56C19.34,0.55,17.97,0,16.41,0c-1.28,0-2.57,0.37-3.85,1.01c0.37,0.55,0.55,1.1,0.73,1.74
                c0.92-0.64,2.02-0.92,3.12-0.92c0.82,0,1.93,0.18,2.75,1.01c1.83,1.83,1.1,5.68-1.56,8.34c-1.65,1.65-3.67,2.57-5.59,2.57
                c-0.82,0-1.93-0.18-2.75-1.01c-0.92-0.92-1.19-2.47-0.92-4.03c-0.09,0-0.09,0-0.18,0c-0.55,0-1.19-0.09-1.65-0.27
                c-0.2,0.93-0.22,1.86-0.06,2.73c0.03,1.34-0.11,3.51-0.95,4.43l-0.96,0.97l-0.04-0.05c-0.27-0.28-0.73-0.28-1.01,0L0.09,19.9
                c-0.18,0.28-0.09,1.01,0.46,1.56c0.55,0.55,1.28,0.64,1.47,0.46l3.39-3.39c0.27-0.28,0.27-0.73,0-1.01l1.01-1.01
                c0.86-0.86,2.85-1.05,4.2-1.04c0.42,0.08,0.85,0.13,1.31,0.13c2.29,0,4.86-1.1,6.88-3.12C22.37,9.08,23.01,4.13,20.44,1.56z
                 M7.51,14.49c0.06-0.24,0.11-0.47,0.15-0.71c0.07,0.08,0.14,0.17,0.22,0.25c0.11,0.11,0.23,0.21,0.34,0.31
                C7.98,14.38,7.75,14.42,7.51,14.49z M9.53,2.38C8.61,1.47,8.43,0.55,8.43,0c0.92,0,1.74,0.37,2.48,1.1
                c0.64,0.64,1.01,1.56,1.01,2.38C11.36,3.48,10.45,3.21,9.53,2.38z M5.68,6.33c-0.73-0.73-1.1-1.56-1.1-2.48
                c0.64,0,1.47,0.18,2.38,1.1c0.83,0.92,1.1,1.83,1.1,2.38C7.24,7.34,6.41,7.06,5.68,6.33z M7.51,4.4c-1.1-1.01-2.2-1.28-2.84-1.28
                C4.76,2.38,5.13,1.65,5.68,1.1C6.23,0.55,6.87,0.18,7.6,0.09C7.7,0.73,7.97,1.83,9.07,2.84c1.01,1.1,2.11,1.38,2.84,1.38
                c-0.09,0.73-0.46,1.47-1.01,2.02c-0.55,0.55-1.28,0.92-2.02,1.01C8.8,6.6,8.52,5.5,7.51,4.4z"/></svg>
    {%- when 'clothing' -%}
        <svg class="{{ icon_class }} bee-icon-clothing" viewBox="0 0 22 22"><path id="Clothing" class="st1" d="M20.31,16.39c0.01-3.61-3.33-11.3-4.25-13.33c-0.13,0.01-0.26,0.03-0.39,0.04
                c0.89,1.89,3.97,8.79,4.09,13.48C19.96,16.51,20.14,16.45,20.31,16.39z M5.12,3.05C4.8,3.02,4.63,3,4.63,3
                C0.83,11.68,0,16.17,0,16.17c0.16,0,0.49,0.06,0.92,0.15C0.95,12.64,4.1,5.32,5.12,3.05z M1.94,16.56C2.07,11.9,5.12,5.06,6.02,3.13
                C5.88,3.11,5.75,3.1,5.63,3.09c-0.94,2.1-4.26,9.77-4.23,13.33C1.57,16.47,1.75,16.51,1.94,16.56z M9.71,17.8
                c0-0.52,0.62-2.53,0.62-2.53c0.67,1.88,1.5,2.5,1.5,2.5c2.83,0.17,5.54-0.44,7.45-1.04c-0.06-4.67-3.35-11.95-4.12-13.59
                C11.85,3.43,8.54,3.3,6.54,3.16c-0.78,1.67-4.02,8.87-4.11,13.52c1.5,0.37,3.34,0.86,4.61,1.12C9.29,18.26,9.71,17.8,9.71,17.8z
                 M22,15.7c-0.58-0.78-2.04-6.41-2.54-7.62c-0.44-1.06-2.35-4.16-2.81-4.91c1.06,2.37,4.06,9.4,4.15,13.05
                C21.56,15.92,22,15.7,22,15.7z"/></svg>
    {%- when 'swimming' -%}
        <svg class="{{ icon_class }} bee-icon-swimming" viewBox="0 0 22 22"><path id="Swimming" class="st1" d="M14.58,7.72c0-1.54,1.23-2.79,2.75-2.79c1.52,0,2.75,1.25,2.75,2.79c0,1.54-1.23,2.79-2.75,2.79
                C15.81,10.51,14.58,9.26,14.58,7.72z M15.95,13.3c-2.75,0-5.13,1.86-9.63,1.86C1.74,15.15,0,13.3,0,13.3s2.02,3.72,6.97,3.72
                c4.4,0,7.06-1.86,10.08-1.86c3.67,0,4.95,1.86,4.95,1.86S20.08,13.3,15.95,13.3z M3.99,8.55l4.74-2.4l0.18,0.3L9.9,9.21
                c-0.09,0-5.32,3.16-6.42,3.81c0.73,0.19,1.65,0.28,2.75,0.28c3.58,0,5.87-1.21,8.07-1.67L9.9,4.46L9.88,4.48L9.86,4.45
                C9.61,4.03,9.09,3.88,8.66,4.1l-5.5,2.79C2.71,7.12,2.53,7.67,2.75,8.13C2.98,8.59,3.53,8.78,3.99,8.55z"/></svg>


    {%- when 't-shirt' -%}
        <svg class="{{ icon_class }} bee-icon-t-shirt" viewBox="0 0 24 24"><g><path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M8.7836,1.5404
        c0.6272,4.3268,5.7322,4.2217,6.4875,0.0437"/><path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M18.4462,22.4277
        L17.9177,8.5325c1.149,0.2878,2.6185,1.9259,3.3677,0.9806c0.175-0.2206,2.0869-2.338,1.9503-2.492
        c-1.1196-1.2637-2.2389-2.5284-3.3585-3.7923c-0.8878-1.003-2.1739-1.0764-3.4547-1.4335
        c-1.7323-0.4826-2.5877,0.0357-4.4478,0.0357c-1.6426,0-2.4866-0.5358-3.9766-0.1285C6.8845,2.0073,5.1358,2.0887,4.3467,2.9792
        C3.189,4.287,2.0304,5.5948,0.8727,6.9026C0.5118,7.3106,1.875,8.4989,2.1535,8.8327c1.0838,1.3003,2.3417,0.1038,3.9257-0.2925
        L5.5703,21.25"/><path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M5.5703,21.25
        c0,0,2.0685,2.1924,6.835,1.1203c4.7689-1.0726,6.0409,0.0574,6.0409,0.0574"/><path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M14.6062,11.0137
        h-0.0254c-0.7373,0-1.335-0.5977-1.335-1.335V7.8638h2.6954v1.8149C15.9412,10.416,15.3435,11.0137,14.6062,11.0137z"/></g><rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/></svg>
    {%- when 'shirt-tie' -%}
        <svg class="{{ icon_class }} bee-icon-shirt-tie" viewBox="0 0 24 24"><g>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M7.9441,2.2253
        c-1.1296,0.3092-2.8988-0.311-3.6993,0.5924c-1.1744,1.3267-2.3497,2.6533-3.5242,3.98C0.3545,7.2116,1.7374,8.4171,2.02,8.7557
        C3.1194,10.0748,4.3955,8.861,6.0023,8.459L5.486,23.5469"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M18.5477,23.5469
        L18.0116,8.4512c1.1656,0.292,2.6563,1.9537,3.4163,0.9947c0.1776-0.2238,2.117-2.3718,1.9784-2.5279
        c-1.1358-1.282-2.2712-2.5649-3.407-3.847c-0.9006-1.0174-2.5713-0.4833-3.8707-0.8456"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M18.5477,23.5469"/>
        <polyline fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" points="
            12.0364,3.965 10.774,4.8498 7.9441,2.2253 8.296,0.5412 12.0364,3.965    "/>
        <polyline fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" points="
        12.0364,3.965 13.2987,4.8498 16.1286,2.2253 15.7767,0.5412 12.0364,3.965    "/>
    
        <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="18.5477" y1="23.5469" x2="5.486" y2="23.5469"/>
    
        <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" x1="8.296" y1="0.5412" x2="15.7767" y2="0.5412"/><path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M10.9938,4.6961
        c-0.2961,0.2289,0.0801,0.8832,0.3157,1.3069c0.4683-0.0778,0.9535-0.0827,1.4224-0.0136c0.236-0.4268,0.5939-1.0998,0.3005-1.3262
        "/><polyline fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="11.5457,7.5627 10.4483,16.1244 12.0667,17.7436 13.5854,16.1244 12.4862,7.5462   "/></g><rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/></svg>
    {%- when 'short-pants' -%}
        <svg class="{{ icon_class }} bee-icon-short-pants" viewBox="0 0 24 24">
    
        <rect x="0.5142" y="2.5344" fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" width="23" height="3"/>
        <polygon fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" points="
            0.5142,5.5344 0.5142,21.5344 9.8614,21.5344 12.0482,13.1011 14.235,21.5344 23.5142,21.5344 23.5142,5.5344   "/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M2.6802,11.5916
            c0,0,4.475-0.7868,4.6954-5.1419"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M21.3482,11.5916
            c0,0-4.475-0.7868-4.6954-5.1419"/>
    
        <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" x1="0.5142" y1="19.5344" x2="10.38" y2="19.5344"/>
    
        <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" x1="13.7163" y1="19.5344" x2="23.5142" y2="19.5344"/>
        </g><rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/></svg>
    {%- when 'pants' -%}
       <svg class="{{ icon_class }} bee-icon-pants" viewBox="0 0 24 24">
    
        <g>
    
        <rect x="7.5142" y="0.5344" fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" width="11" height="2"/>
        <polygon fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" points="
            7.5142,2.5344 7.5142,23.5344 11.3479,23.5344 12.9812,7.257 14.6145,23.5344 18.5142,23.5344 18.5142,2.5344   "/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M7.5491,5.3452
            c0,0,1.886,0.1387,1.9712-1.88"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M18.4896,5.3452
            c0,0-1.9591,0.1387-2.0476-1.88"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M12.9812,2.5198
            l0,3.9922c0,0,1.4288,0.1324,1.4278-1.2027V2.5198"/>
        
        <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" x1="12.9812" y1="6.512" x2="12.9812" y2="7.2763"/>
        </g>
        <rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/>
        </svg>
    {%- when 'skirt' -%}
        <svg class="{{ icon_class }} bee-icon-skirt" viewBox="0 0 24 24">
    
       <g>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M6.6725,7.9396
            c0,0,2.1462-0.4463,2.252-2.9168"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M17.3543,7.9396
            c0,0-2.1462-0.4463-2.252-2.9168"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M5.9876,4.1309"
            />
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M18.0392,4.1309"
            />
        <polygon fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" points="
            23.2172,21.84 23.217,21.8396 23.2155,21.84  "/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M6.1007,0.8338
            v1.8146c3.5536,0.4584,8.269,0.4588,11.8254,0V0.8338C14.3895,1.2922,9.644,1.2931,6.1007,0.8338z"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M5.9876,4.1309
            L0.8112,21.84h0.0001c2.2526,0.8004,5.8432,1.3498,11.2021,1.3498s8.9496-0.5494,11.2021-1.3498l0.0015-0.0005L18.0392,4.1309"/>
        
            <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" x1="12.0134" y1="20.395" x2="12.0134" y2="23.1898"/>
        
            <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" x1="8.2092" y1="12.6217" x2="6.3586" y2="22.9405"/>
        
            <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" x1="16.6527" y1="16.2921" x2="17.6682" y2="22.7139"/>
        </g>
        <rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/></svg>
    {%- when 'dress' -%}
        <svg class="{{ icon_class }} bee-icon-dress" viewBox="0 0 24 24">
        <g>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M14.3407,9.5448
            h-4.61c-0.6234,0-1.171,0.4142-1.3407,1.0141L4.7444,23.5321h14.5825l-3.6456-12.9732C15.5117,9.959,14.9641,9.5448,14.3407,9.5448
            z"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M14.978,4.1473
        h-0.6611c-1.418,0.5561-3.1426,0.5568-4.5624,0H9.0933c-1.1579,0-1.9635,1.1508-1.5672,2.2387L8.343,8.6284
        c0.2004,0.5503,0.7235,0.9163,1.3091,0.9163h4.767c0.5856,0,1.1087-0.366,1.3092-0.9163l0.8168-2.2423
        C16.9415,5.2981,16.1359,4.1473,14.978,4.1473z"/>
    
        <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" x1="9.5142" y1="4.1473" x2="9.5142" y2="1.0344"/>
    
        <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" x1="14.5142" y1="4.1473" x2="14.5142" y2="1.0344"/>
    
        <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" x1="11.54" y1="18.6026" x2="11.54" y2="15.5586"/>
    
        <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" x1="8.6734" y1="21.0852" x2="9.9175" y2="13.0153"/>
        </g>
        <rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/>  </svg>
    {%- when 'underwear' -%}
        <svg class="{{ icon_class }} bee-icon-underwear" viewBox="0 0 24 24">
    
        <g>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M22.1627,6.2597
            c-2.7296,0.6629-6.2952,1.0652-10.2001,1.0652c-3.8304,0-7.3346-0.387-10.0434-1.0273l-1.202,9.2561
            c0,0,8.2283,1.0917,8.8019,4.1214c0.2948,0.9825,4.7727,0.9392,5.0486,0c0.5735-3.0297,8.8019-4.1214,8.8019-4.1214L22.1627,6.2597
            z"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M22.1627,4.3115
            c-2.7296,0.6629-6.2952,1.0652-10.2001,1.0652c-3.8304,0-7.3346-0.387-10.0434-1.0273"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M8.9043,9.526
            c0,0-1.5403,5.095-1.1099,8.1619"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M15.1868,9.526
            c0,0,1.5403,5.095,1.1099,8.1619"/>
        </g>
        <rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/>
        </svg>
    {%- when 'socks' -%}
        <svg class="{{ icon_class }} bee-icon-socks" viewBox="0 0 24 24">
            <g>
    
                <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" x1="3.8336" y1="16.4908" x2="7.4093" y2="22.8606"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M19.2699,13.309
                c0,0-5.5495-0.9132-5.1479,5.9693"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M21.6474,4.9712l0.6343-2.2243
                l-7.1133-1.9259l-3.2724,11.1318l-8.0623,4.5381c0,0-3.4873,2.2307-1.5772,5.0802c1.9101,2.8495,5.1529,1.2896,5.1529,1.2896
                s6.4841-3.4792,8.9121-4.7391c2.125-1.1026,2.7026-3.9502,2.7026-3.9502L21.6474,4.9712L14.5142,3.046"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M14.5142,3.046"/>
            
                <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" x1="17.0336" y1="1.326" x2="16.3872" y2="3.5506"/>
            
                <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" x1="18.0533" y1="3.9917" x2="18.7116" y2="1.7803"/>
            
                <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" x1="19.7194" y1="4.4326" x2="20.3734" y2="2.2302"/>
        </g>
        <rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/>
        </svg>
        
    {%- when 'baseball-cap' -%}
       <svg class="{{ icon_class }} bee-icon-baseball-cap" viewBox="0 0 24 24"><g>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M6.4474,11.5053
            c0,0,3.0184-8.5235,10.4615-6.5513c8.2125,2.1761,6.2254,10.4443,6.2254,10.4443S14.3707,16.0844,6.4474,11.5053z"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="3.864" d="M6.4474,11.5053
            c0,0-6.3626,1.3891-5.6938,2.3838c0.6509,0.968,4.9561,5.7259,11.1721,0.0108"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M16.9089,4.9541
        c0,0-4.4098,2.2854-4.9832,8.9459"/>
    
        <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="16.8906" y1="3.8178" x2="17.5332" y2="3.9793"/>
        </g>
        <rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/></svg>
    {%- when 'hat-beanie' -%}
        <svg class="{{ icon_class }} bee-icon-hat-beanie" viewBox="0 0 24 24"><g>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M23.5142,22.8554
            c0,0.375-0.304,0.679-0.679,0.679H1.1932c-0.375,0-0.679-0.304-0.679-0.679v-4.6421c0-0.375,0.304-0.679,0.679-0.679h21.6421
            c0.375,0,0.679,0.304,0.679,0.679V22.8554z"/>
        <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M22.3839,17.5344
            c0-6.6258-4.6445-11.5059-10.3737-11.5059S1.6365,10.9086,1.6365,17.5344H22.3839z"/>
        
            <circle fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" cx="12.0102" cy="4.0447" r="1.9837"/>
        
            <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="19.5139" y1="19.4987" x2="19.5139" y2="21.5485"/>
        
            <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="16.5138" y1="19.4987" x2="16.5138" y2="21.5485"/>
        
            <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="13.5136" y1="19.4987" x2="13.5136" y2="21.5485"/>
        
            <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="10.5134" y1="19.4987" x2="10.5134" y2="21.5485"/>
        
            <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="7.5132" y1="19.4987" x2="7.5132" y2="21.5485"/>
        
            <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="4.513" y1="19.4987" x2="4.513" y2="21.5485"/>
        </g>
        <rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/>
        </svg>
    {%- when 'shoe' -%}
       <svg class="{{ icon_class }} bee-icon-shoe" viewBox="0 0 24 24"><g>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M1.9352,21.5344
                h21.579v-2h-23v0.6342C0.5142,20.9186,1.1852,21.5344,1.9352,21.5344z"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M13.0787,12.2953
                l-8.811,3.209c0,0-3.4475,0.4105-3.4475,4.03"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M23.062,19.5344
                v-4.3397c0-0.4385-0.3832-0.7594-0.7706-0.6398c-1.4435,0.4459-4.7377,0.8865-7.0976-1.6404"/>
            <polygon fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" points="
                13.0787,12.2953 15.6331,10.692 15.1938,12.9145  "/>
            
                <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="11.0381" y1="13.0385" x2="13.0787" y2="14.2404"/>
            
                <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="9.2182" y1="13.7014" x2="10.9285" y2="14.7087"/>
            
                <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="7.3982" y1="14.3642" x2="8.7782" y2="15.177"/>
            
                <line fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" x1="4.5142" y1="15.4146" x2="4.5142" y2="19.5344"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M22.2914,14.5549
                c0,0-1.7973,1.1262-1.6544,4.9794"/>
        </g>
        <rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/></svg>
    {%- when 'women-high' -%}
       <svg class="{{ icon_class }} bee-icon-women-high" viewBox="0 0 24 24"><g>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M6.6339,16.5468
                c0,0,6.9106-5.4222,12.067-8.6117s2.1795,0.4253-0.8505,3.2427S8.6539,19.6832,6.6339,16.5468z"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M21.4359,6.7309
                c0,0,0.933-0.0185,1.6772,3.2774s-1.9834,5.7332-1.9834,5.7332l-0.2654,5.7624h-1.4191c0,0,0-0.1726,0-4.585
                c0-4.4121-3.7053,0.2917-5.9329,2.5517c-0.9832,0.9975-1.9741,1.6736-3.123,1.9963c-0.1805,0.0507-0.3704,0.0698-0.5578,0.0698
                l-6.2724,0c-0.2774,0-0.5552-0.0465-0.8055-0.1661c-0.482-0.2303-0.9712-0.5458-1.4887-0.9432
                c-2.9769-2.286,5.369-3.8807,5.369-3.8807"/>
        </g>
        <rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/></svg>
    {%- when 'boots' -%}
       <svg class="{{ icon_class }} bee-icon-boots" viewBox="0 0 24 24"><g>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M16.5142,21.8757
                v1.6587h7v-3h-23v1.4268c0,0.8904,0.7175,1.5732,1.462,1.5732h3.2468C5.2231,23.5344,9.5142,23.7598,16.5142,21.8757z"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M0.8347,20.5344
                c0,0-0.0365-4.4384,3.3246-4.7606c3.3612-0.3222,9.2067,0.179,9.6085-3.7953C14.0995,8.6987,13.5136,0.516,13.5136,0.516h10.0006
                l-0.702,20.0184"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M15.4821,0.516
                l-0.0337,9.068c0,0,0.0337,1.2115-1.61,1.3447"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M13.3312,12.1883
                c0,0,2.0691-0.3348,2.8143,0.9142"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M12.4774,13.5726
                c0,0,2.0314,0.1756,2.487,1.5569"/>
            <path fill="none" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" d="M10.9296,14.6505
                c0,0,1.8685,0.4941,1.9916,1.9433"/>
        </g>
        <rect x="0.0142" y="0.0344" fill="none" width="24" height="24"/></svg> 
{%- endcase -%}
