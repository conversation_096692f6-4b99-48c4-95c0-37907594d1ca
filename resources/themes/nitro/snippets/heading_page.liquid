{%- if se_bks.size > 0 -%}

  {%- liquid 
  capture heading_title
    case request.page_type
      when 'cart'
        echo 'cart.cart_page.title' | t
      when 'list-collections'
        echo 'list_collections.collections' | t
      when 'page'
        echo page.title | default: page_title | escape
      when 'customers/login'
        echo 'customer.login.title' | t
      when 'customers/register'
        echo 'customer.register.title' | t
      when 'customers/activate_account'
        echo 'customer.activate_account.title' | t
      when 'customers/reset_password'
        echo 'customer.reset_password.title' | t
      when 'customers/order'
        echo 'customer.order.title' | t: name: order.name
      when 'customers/addresses'
        echo 'customer.addresses.title' | t
      when 'customers/account'
        echo 'customer.account.title' | t
      else
        echo page.title | escape
    endcase
  endcapture -%}
  {%- capture html_page -%}
      {%- for block in se_bks %}{% assign bk_stts = block.settings -%}
        {%- case block.type -%}
            {%- when '1' -%}
            {%- assign general_block = true -%}
            {%- assign heading_title = bk_stts.heading | default: heading_title -%}
            <h1 class="bee-title-head bee-bl-item bee-animation-none bee-text-bl bee-fnt-fm-{{ bk_stts.fontf }} bee-font-italic-{{ bk_stts.font_italic }} bee-uppercase-{{ bk_stts.font_uppercase }} bee-hidden-mobile-{{ bk_stts.hidden_mobile }} bee-hidden-br-mb-{{ bk_stts.remove_br_tag }} bee-text-shadow-{{ bk_stts.text_shadow }}" id="b_{{ block.id }}" {%- render 'bk_cus_style', type: 'custom_text', bk_stts: bk_stts, ani_delay: ani_delay -%}>{{ heading_title }} </h1>
            
            {%- when '2' -%}
            {%- if bk_stts.content == blank %}{% continue %}{% endif %}{% assign general_block = true %}<div class="bee-desc-head bee-bl-item bee-animation-none bee-text-bl bee-fnt-fm-{{ bk_stts.fontf }} bee-font-italic-{{ bk_stts.font_italic }} bee-uppercase-{{ bk_stts.font_uppercase }} bee-hidden-mobile-{{ bk_stts.hidden_mobile }} bee-hidden-br-mb-{{ bk_stts.remove_br_tag }} bee-text-shadow-{{ bk_stts.text_shadow }}" id="b_{{ block.id }}" {%- render 'bk_cus_style', type: 'custom_text', bk_stts: bk_stts, ani_delay: ani_delay -%}>{{ bk_stts.content }}</div> 
            
            {%- when '3' -%}
            <nav class="bee-breadcrumbs" role="navigation" aria-label="breadcrumbs" style="--brc-cl: {{ bk_stts.breadcrumb_color }};--brc-cl-link: {{ bk_stts.breadcrumb_color_link }};--brc_mgb: {{ bk_stts.brc_mgb }}px;">
            <ul class="bee-breadcrumbs__list">
                   <li class="bee-breadcrumbs__item">
                      <a href="{{ routes.root_url }}" class="bee-dib">{{ 'general.breadcrumb.home' | t }}</a><span class="bee-space-db"><svg version="1.1" x="0px" y="0px" width="10px" height="10px" viewBox="0 0 22 22"><polygon points="6,20 6,2 17,11 "/></svg></span>
                  </li>
                  <li class="bee-breadcrumbs__item">{{ heading_title }}</li> 
              </ul>
          </nav> 
        {%- endcase -%} 
      {%- endfor -%}
  {%- endcapture -%}

  {%- if general_block -%}
      {{ 'bee-general-block.css' | asset_url | stylesheet_tag }}
  {%- endif -%}

  <div class="bee-page-head bee-oh bee-text-center"><div class="bee-container bee-pr" style="z-index: 1">{{ html_page }}</div></div>

{%- endif -%} 