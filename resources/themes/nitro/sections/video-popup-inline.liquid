<!-- sections/video-popup-inline.liquid -->
{{ 'bee-section.css' | asset_url | stylesheet_tag }}
{{ 'bee-content-position.css' | asset_url | stylesheet_tag }}
{{ 'bee-hero.css' | asset_url | stylesheet_tag }}
{%-liquid
    assign image_fix = image_nt | image_tag
	assign sid = section.id
    assign se_blocks = section.blocks
    assign se_stts = section.settings
    assign stt_layout = se_stts.layout
    assign image_mb = se_stts.image_mb
    assign image = se_stts.image | default: image_mb
    assign mb_image = image_mb | default: image 
    assign se_height = se_stts.se_height
    assign video_url = se_stts.video_url 

	assign url = se_stts.link_img1 
    assign open_link = se_stts.open_link
    
    assign bg_content_op = se_stts.bg_content_op | divided_by: 100.0 
    assign bg_content = se_stts.bg_content_cl | color_modify: 'alpha', bg_content_op 

    assign bg_opacity = se_stts.bg_opacity | divided_by: 100.0 
    assign bg_overlay = se_stts.bg_overlay | color_modify: 'alpha', bg_opacity 

    assign content_pd = se_stts.content_pd | remove: ' ' | split: ','  
    assign content_pd_mb = se_stts.content_pd_mb | remove: ' ' | split: ','

    assign br_opacity = se_stts.br_opacity | divided_by: 100.0
    assign br_bg = se_stts.br_bg | color_modify: 'alpha', br_opacity 

    assign ani_delay = 0   
    assign percent_delay = se_stts.animation_delay | divided_by: 100.0
    assign time_ani_delay = se_stts.time_animation | times: percent_delay

    if stt_layout == 'bee-container-wrap' 
        assign html_layout = '<div class="bee-container">__</div>' | split: '__'
    else
        assign html_layout = '__' | split: '__'
    endif

    assign countdown = false
    assign use_button = false
    assign use_animation = false
    assign parallax = false
    assign general_block = false
 -%}
{%- capture append_style -%}
    --aspect-ratio-cus : {{ se_stts.height_dk }}px;--aspect-ratio-custb : {{ se_stts.height_tb }}px;--aspect-ratio-cusmb : {{ se_stts.height_mb }}px;
{%- endcapture -%}
{%- capture append_bg_content_style -%}--bg-content: {{ bg_content }};--content-pd: {{ se_stts.content_pd_tb }}px {{ se_stts.content_pd_lr }}px;--content-pd-mb: {{ se_stts.content_pd_tb_mb }}px {{ se_stts.content_pd_lr_mb }}px;{%- endcapture -%}
{%- if se_stts.border_bl -%}
    {%- capture append_bg_border_style -%}--br-color: {{ se_stts.br_color }};--br-style: {{ se_stts.br_style }};--br-pd: {{ se_stts.br_pd }}px;--br-pd-mb: {{ se_stts.br_pd_mb }}px;--border-bg: {{ br_bg }};{%- endcapture -%}
{%- endif -%}
<div class="bee-section-inner bee-pr bee_nt_se_{{ sid }} {{ stt_layout }}" {% render 'section_style', se_stts: se_stts, append_style: append_style -%} data-bee-animate>
    {{- html_layout[0] -}} 
    <div class="bee-pr bee-oh">
        <div class="bee-video-popup-inline bee-bg-video bee-pr bee-row bee-row-cols-1 bee-gx-0 bee-video bee_ratio_mix {{ se_height }} bee_ratio_cuspx_mb_{{ se_stts.custom_mb }} bee_ratio_cuspx_tb_{{ se_stts.custom_tb }} bee_ratio_cuspx_{{ se_stts.custom_dk }}" data-video-poster>
            <div class="bee-col-item">
                <div data-video-insert class="bee-hero-inner bee-pr bee-oh bee_cover bee_ratio " style="--ratioapt: {{ image.aspect_ratio | default: 1.7777 }};--ratioaptmb: {{ mb_image.aspect_ratio | default: 1.7777 }};"> 
                    {%- if image != blank -%}    
                    {%- liquid
                        assign img_url_w     = image | image_url: width: 1904 | split: '1904'
                        assign img_url_w0    = img_url_w[0]
                        assign img_url_w1    = img_url_w[1]
                        assign img_url_w_mb  = mb_image | image_url: width: 1904 | split: '1904'
                        assign img_url_w_mb0 = img_url_w_mb[0]
                        assign img_url_w_mb1 = img_url_w_mb[1]  
                    -%}
                        <img {% if image_mb.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{image_mb.presentation.focal_point}}"{% endif %} class="bee-img-as-bg bee-d-md-none bee-parallax-img"
                        srcset="{{ img_url_w_mb0 }}375{{ img_url_w_mb1 }} 375w, {{ img_url_w_mb0 }}550{{ img_url_w_mb1 }} 550w, {{ img_url_w_mb0 }}750{{ img_url_w_mb1 }} 750w, {{ img_url_w_mb0 }}1100{{ img_url_w_mb1 }} 1100w, {{ img_url_w_mb0 }}1500{{ img_url_w_mb1 }} 1500w, {{ img_url_w_mb0 }}1780{{ img_url_w_mb1 }} 1780w, {{ img_url_w_mb0 }}2000{{ img_url_w_mb1 }} 2000w, {{ img_url_w_mb0 }}3000{{ img_url_w_mb1 }} 3000w,{{ img_url_w_mb0 }}3840{{ img_url_w_mb1 }} 3840w, {{ mb_image | image_url }} {{ mb_image.width }}w"
                        sizes="100vw" src="{{ img_url_w_mb0 }}1500{{ img_url_w_mb1 }}"
                        loading="lazy" alt="{{ mb_image.alt | escape }}" width="{{ mb_image.width }}" height="{{ mb_image.height }}">
                        <img {% if image.presentation.focal_point != '50.0% 50.0%' %} style="object-position: {{image.presentation.focal_point}}"{% endif %} class="bee-img-as-bg bee-d-none bee-d-md-block bee-parallax-img"
                        srcset="{{ img_url_w0 }}375{{ img_url_w1 }} 375w, {{ img_url_w0 }}550{{ img_url_w1 }} 550w, {{ img_url_w0 }}750{{ img_url_w1 }} 750w, {{ img_url_w0 }}1100{{ img_url_w1 }} 1100w, {{ img_url_w0 }}1500{{ img_url_w1 }} 1500w, {{ img_url_w0 }}1780{{ img_url_w1 }} 1780w, {{ img_url_w0 }}2000{{ img_url_w1 }} 2000w, {{ img_url_w0 }}3000{{ img_url_w1 }} 3000w,{{ img_url_w0 }}3840{{ img_url_w1 }} 3840w, {{ image | image_url }} {{ image.width }}w"
                        sizes="100vw" src="{{ img_url_w0 }}1500{{ img_url_w1 }}"
                        loading="lazy" alt="{{ image.alt | escape }}" width="{{ image.width }}" height="{{ image.height }}">
                    {%- else -%}
                        {{ 'lifestyle-1' | placeholder_svg_tag: 'bee-placeholder-svg bee-svg-bg1' }} 
                    {%- endif -%}
                    <div class="bee-content-wrap bee-bg-cl-wrap bee-pe-none bee-full-width-link bee-z-100">
                        <div class="bee-content-position bee-{{ se_stts.content_width }} bee-pa bee-text-lg-{{ se_stts.text_align }} bee-text-md-{{ se_stts.text_align_tb }} bee-text-{{ se_stts.text_align_mb }} bee-bg-content-true bee-br-content-{{ se_stts.border_bl }}" style="--time-animation: {{ se_stts.time_animation }}s;{%- render 'content_position', ch_pos: se_stts.ch_pos, cv_pos: se_stts.cv_pos, ch_pos_tb: se_stts.ch_pos_tb, cv_pos_tb: se_stts.cv_pos_tb, ch_pos_mb: se_stts.ch_pos_mb, cv_pos_mb: se_stts.cv_pos_mb, append_bg_content_style: append_bg_content_style, append_bg_border_style: append_bg_border_style -%}">
                            {%- for block in se_blocks -%} 
                                {%- assign bk_stts = block.settings -%}
                                {%- case block.type -%}
                                    {%- when 'custom_text' -%}
                                        {%- assign general_block = true -%}
                                        {%- if bk_stts.animation != 'none' -%}{%- assign use_animation = true -%} {%- endif -%}
                                        <{{ bk_stts.tag }} data-lh="{{bk_stts.text_lh_mb}}" data-lh-md="{{bk_stts.text_lh_tb}}" data-lh-lg="{{bk_stts.text_lh}}" class="bee-bl-item bee-animation-{{ bk_stts.animation }} bee-text-bl bee-fnt-fm-{{ bk_stts.fontf }} bee-font-italic-{{ bk_stts.font_italic }} bee-hidden-mobile-{{ bk_stts.hidden_mobile }} bee-hidden-br-mb-{{ bk_stts.remove_br_tag }} bee-text-shadow-{{ bk_stts.text_shadow }}" id="b_{{ block.id }}" {%- render 'bk_cus_style', type: 'custom_text', bk_stts: bk_stts, ani_delay: ani_delay -%}>{{ bk_stts.text }}</{{ bk_stts.tag }}>
                                    {%- when 'space_html' -%}
                                        {%- assign general_block = true -%}
                                        {%- if bk_stts.animation != 'none' -%}{%- assign use_animation = true -%} {%- endif -%}
                                        <div class="bee-bl-item bee-space-html bee-bl-item bee-animation-{{ bk_stts.animation }} bee-hidden-mobile-{{ bk_stts.hidden_mobile }}" {%- render 'bk_cus_style', type: 'space_html', bk_stts: bk_stts, ani_delay: ani_delay -%}></div>
                                    {%- when 'html' -%}
                                        {%- assign general_block = true -%}
                                        {%- if bk_stts.animation != 'none' -%}{%- assign use_animation = true -%} {%- endif -%}
                                        <div class="bee-bl-item bee-animation-{{ bk_stts.animation }} bee-raw-html bee-hidden-mobile-{{ bk_stts.hidden_mobile }} bee-rte--list" {%- render 'bk_cus_style', type: 'html', bk_stts: bk_stts, ani_delay: ani_delay -%}>{{ bk_stts.html_content }}</div>
                                    {%- when 'image' -%}
                                        {%- assign image = bk_stts.image_child -%}
                                        {%- if image -%}
                                            {%- assign general_block = true -%}
                                            {%- if bk_stts.animation != 'none' -%}{%- assign use_animation = true -%} {%- endif -%}
                                            <div class="bee-bl-item bee-img-child bee-animation-{{ bk_stts.animation }} bee-hidden-mobile-{{ bk_stts.hidden_mobile }}" {%- render 'bk_cus_style', type: 'image', bk_stts: bk_stts, ani_delay: ani_delay -%}>
                                                <img class="lazyloadbee bee-lz--fadeIn" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                                            </div>
                                        {%- endif -%}
                                    {%- when "countdown" -%}
                                        {%- if bk_stts.date != blank -%}
                                            {%- if bk_stts.animation != 'none' -%}{%- assign use_animation = true -%} {%- endif -%}
                                            {%- assign countdown = true -%}
                                                <div class="bee-bl-item bee-countdown sepr_coun_dt_wrap bee-countdown-des-{{ bk_stts.cdt_des }} bee-countdown-size-{{ bk_stts.cdt_size }} bee-animation-{{ bk_stts.animation }} bee-hidden-mobile-{{ bk_stts.hidden_mobile }}" id="b_{{ block.id }}" {%- render 'bk_cus_style', type: 'countdown', bk_stts: bk_stts, ani_delay: ani_delay -%}>
                                                    <div class="bee-time" data-countdown-bee data-date='{{ bk_stts.date }}' data-keyid='#countdown-{{ sid }}'></div>
                                                </div>
                                        {% endif %}
                                    {%- when 'custom_button' -%}
                                        {%- if bk_stts.button_link and bk_stts.button_text != blank -%}
                                            {%- if bk_stts.animation != 'none' -%}{%- assign use_animation = true -%} {%- endif -%}
                                            {%- assign use_button = true -%}
                                            {%- assign button_style = bk_stts.button_style -%}
                                            <a href="{{ bk_stts.button_link }}" target="{{ bk_stts.target_link }}" class="bee-bl-item bee-animation-{{ bk_stts.animation }} bee-btn bee-btn-custom bee-pe-auto bee-fnt-fm-{{ bk_stts.fontf }} bee-hidden-mobile-{{ bk_stts.hidden_mobile }} bee-btn-style-{{ button_style }} {% if button_style == 'default' or button_style == 'outline' %}bee-btn-effect-{{ bk_stts.button_effect }}{% endif %}" {%- render 'bk_cus_style', type: 'custom_button', bk_stts: bk_stts, ani_delay: ani_delay -%}>{{ bk_stts.button_text }}
                                                {%- if bk_stts.button_icon_w > 0 -%}
                                                    {%- if bk_stts.icon_style == "1" -%}
                                                        <svg  class="bee-btn-icon" viewBox="0 0 22 22"><use xlink:href="#bee-icon-btn"></use></svg>
                                                    {%- else -%}
                                                        <svg class="bee-btn-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M96 480c-8.188 0-16.38-3.125-22.62-9.375c-12.5-12.5-12.5-32.75 0-45.25L242.8 256L73.38 86.63c-12.5-12.5-12.5-32.75 0-45.25s32.75-12.5 45.25 0l192 192c12.5 12.5 12.5 32.75 0 45.25l-192 192C112.4 476.9 104.2 480 96 480z"/></svg>   
                                                    {%- endif -%}
                                                {%- endif -%}
                                            </a>
                                        {%- endif -%}
                                    {%- when 'button_video' -%}
                                        {%- assign enable_btn_close = bk_stts.btn_close -%}
                                        {%- capture data_options -%}
                                            {"type": "{%- if bk_stts.source == "1" -%}{{ bk_stts.video_url.type }}{%-else -%}html5{%- endif -%}", "vid": "{{ bk_stts.video_url.id }}", "autoplay": {{ bk_stts.au_video }}, "loop": {{ bk_stts.loop_video }}, "accent_color": "{{ settings.accent_color | remove_first: '#' }}","srcDefault": "https://cdn.shopify.com/s/files/1/0610/5209/2628/files/Share_your_brand_story_by_adding_a_video_to_your_store.mp4?v=1641630446", "id":"#tmp-video-{{sid}}" }
                                        {%- endcapture -%}
                                        <div class="bee-btn-video__wrapper bee-d-inline-flex bee-align-items-center bee-bl-item bee-animation-{{ bk_stts.animation }}" style="--btn-video-w: {{ bk_stts.button_width }}px;--icon-video-w: {{ bk_stts.icon_width }}px;--btn-video-fs: {{ bk_stts.button_fs }}px;--btn-video-fw: {{ bk_stts.button_fw }};--btn-video-w-tb: {{ bk_stts.button_width_tb }}px;--icon-video-w-tb: {{ bk_stts.icon_width_tb }}px;--btn-video-fs-tb: {{ bk_stts.button_fs_tb }}px;--btn-video-w-mb: {{ bk_stts.button_width_mb }}px;--icon-video-w-mb: {{ bk_stts.icon_width_mb }}px;--btn-video-fs-mb: {{ bk_stts.button_fs_mb }}px;--mgb: {{ bk_stts.mgb }}px;--mgb-tb: {{ bk_stts.mgb_tb }}px;--mgb-mb: {{ bk_stts.mgb_mb }}px;--btn-bg: {{ bk_stts.button_bg }};--btn-br-color: {{ bk_stts.button_br_color }};--icon-color: {{ bk_stts.icon_color }};--label-color: {{ bk_stts.label_color }};--animation: {{ bk_stts.animation }};--delay-animation: {{ ani_delay }}s;">
                                            <div class="bee-btn-video__box bee-btn-video__border-{{ bk_stts.use_border }} bee-pe-auto" {{ bk_stts.click_action }} data-options='{{ data_options }}'>
                                                <svg xmlns="http://www.w3.org/2000/svg" version="1.0" viewBox="0 0 44.000000 48.000000">
                                                    <g transform="translate(0.000000,48.000000) scale(0.100000,-0.100000)" stroke="none">
                                                        <path d="M20 246 c0 -130 2 -236 6 -236 3 0 49 25 102 56 53 31 111 64 127 74 117 68 155 92 155 100 0 8 -38 32 -155 100 -162 94 -212 123 -222 132 -10 8 -13 -38 -13 -226z"></path>
                                                    </g>
                                                </svg>
                                            </div> 
                                            {%- if bk_stts.btn_label != blank -%}<button class="bee-btn-video__link bee-pe-auto" {{ bk_stts.click_action }} data-options='{{ data_options }}'>{{ bk_stts.btn_label }}</button>{%- endif -%}
                                        </div>
                                        {%- if bk_stts.source == "2" and bk_stts.video != blank -%}
                                        <template id="tmp-video-{{sid}}" class="bee-d-none">{{ bk_stts.video | video_tag: image_size: '1x1', autoplay: bk_stts.au_video, loop: bk_stts.loop_video, muted: false, controls: true, controlsList: 'nodownload', preload: 'auto', playsinline: '' }}</template>
                                        {%- endif -%}
                                        
                                    {%- when 'custom_text_circle' -%}
                                        {%- if bk_stts.text != blank -%}
                                        {{ 'bee-text-circle.css' | asset_url | stylesheet_tag }} 
                                        {%- assign image = bk_stts.image_icon -%}
                                        <div class="bee-bl-item bee-animation-{{ bk_stts.animation }} bee-hidden-mobile-{{ bk_stts.hidden_mobile }} bee-box-text-circle-wrap bee-size-icon-{{ bk_stts.icon_size }}{% if bk_stts.use_rds %} bee_ratio1_1 bee_position_8 bee_cover {%- endif -%}" {%- render 'bk_cus_style', type: 'custom_text_circle', bk_stts: bk_stts, ani_delay: ani_delay -%}>
                                            <div class="bee-box-text-circle bee-pr bee-oh bee-d-inline-block">
                                            <svg viewbox="0 0 100 100" class="bee-text-circle">
                                                <defs><path id="bee-text-circle" d="M 50, 50 m -37, 0 a 37,37 0 1,1 74,0 a 37,37 0 1,1 -74,0"></path></defs>
                                                <text font-size="6px"><textpath xlink:href="#bee-text-circle">{{ bk_stts.text | strip_html }}</textpath></text>
                                            </svg>
                                            {%- if bk_stts.source == 'themes_icon' -%}
                                                {%- if bk_stts.icon_themes != blank and bk_stts.icon_themes != "none" -%}
                                                <div class="bee-img-icon bee-theme-icon">
                                                    {%- render 'icon_shipping', icon_name: bk_stts.icon_themes %}
                                                </div>
                                                {%- endif -%}
                                            {%- elsif bk_stts.source == 'get_image' -%}
                                                {%- if image != blank -%}
                                                <div class="bee-img-icon bee-img-icon {% if bk_stts.use_rds %}bee_ratio{%- endif -%}">
                                                    <img class="lazyloadbee" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                                                </div>
                                                {%- endif  -%}
                                            {%- else -%}
                                                {%- if bk_stts.icon != blank -%}
                                                <div class="bee-img-icon bee-las-icon">
                                                    <i class="{{ bk_stts.icon }}"></i>
                                                </div>
                                                {%- endif  -%}
                                            {%- endif  -%}
                                            </div>
                                        </div>
                                    {%- endif -%}
                                {%- endcase -%}
                                {%- if bk_stts.animation != 'none' %}{% assign ani_delay = ani_delay | plus: time_ani_delay %}{% endif -%}
                            {%- endfor -%} 
                        </div>
                    </div>
					<a href="{{ url }}" target="{{ open_link }}" class="bee-full-width-link{% if url == blank %} bee-pe-none {% else %} bee-pe-auto{% endif %}" style="--bg-overlay: {{ bg_overlay }};"></a>
                </div>
                {%- if enable_btn_close -%}
                    <button class="bee-btn-cl-vi bee-pa bee-t-0" data-video-poster-close title="{{ 'general.popup.close' | t }}"><svg class="bee-iconsvg-close" role="presentation" viewBox="0 0 16 14"><path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path></svg></button>
                {%- endif -%}
            </div>
        </div>
    </div>
    {{- html_layout[1] -}}
    {%- if se_stts.bottom_arrow %}{{ 'bottom_arrow.css' | asset_url | stylesheet_tag }}<button data-go-id="#section-{{ sid }}-anchor" data-offset="{% if se_stts.bottom_arrow_pos == '2' %}-28{% else %}-.1{% endif %}" class="bee-scroll-bottom_arrow is--style_{{ se_stts.bottom_arrow_style }} is--cl_{{ se_stts.bottom_arrow_color }} is--pos_{{ se_stts.bottom_arrow_pos }}" aria-label="{{ 'general.aria.scroll_for_more' | t | escape }}"><svg viewBox="0 0 21 11" role="presentation" class="bee-icon--arrow-bottom"><polyline fill="none" stroke="currentColor" points="0.5 0.5 10.5 10.5 20.5 0.5" stroke-width="1.25"></polyline></svg></button><span id="section-{{ sid }}-anchor" class="bee-anchor"></span>{% endif -%}
</div>
{%- if general_block -%}
    {{ 'bee-general-block.css' | asset_url | stylesheet_tag }}
{%- endif -%}
{%- if use_button -%}
    {{ 'button-style.css' | asset_url | stylesheet_tag }}
    <link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- endif -%}
{%- if use_animation -%}
    <link href="{{ 'bee-animation.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- endif -%}
{%- if countdown -%} 
    {{ 'bee-countdown.css' | asset_url | stylesheet_tag }}
    <template id="countdown-{{ sid }}">
        <span class="countdown-days">
            <span class="cd_timebee cd-number">%-D</span>
            <span class="cd_txtbee cd-text">%!D:{{ "sections.countdown_text.day" | t }},{{ "sections.countdown_text.day_plural" | t }};</span>
        </span>
        <span class="countdown-hours">
            <span class="cd_timebee cd-number">%H</span> 
            <span class="cd_txtbee cd-text">%!H:{{ "sections.countdown_text.hr" | t }},{{ "sections.countdown_text.hr_plural" | t }};</span>
        </span>
        <span class="countdown-min">
            <span class="cd_timebee cd-number">%M</span> 
            <span class="cd_txtbee cd-text">%!M:{{ "sections.countdown_text.min" | t }},{{ "sections.countdown_text.min_plural" | t }};</span>
        </span>
        <span class="countdown-sec">
            <span class="cd_timebee cd-number">%S</span> 
            <span class="cd_txtbee cd-text">%!S:{{ "sections.countdown_text.sec" | t }},{{ "sections.countdown_text.sec_plural" | t }};</span>
        </span>
    </template>
{%- endif -%}
{%- if parallax -%}
    {{ 'parallax.min.css' | asset_url | stylesheet_tag }}
{%- endif -%}
{% schema %}
{
    "name": "Video popup & inline",
    "tag": "section",
    "class": "bee-section bee-section-all bee_tp_parallax bee_tp_cd bee_tp_mfps bee_tp_video",
    "settings": [
        {
            "type": "header",
            "content": "1. General options"
        },
        {
            "type": "select",
            "id": "se_height",
            "label": "Section height",
            "default": "bee_ratioadapt_mix",
            "options": [
                {
                    "value": "bee_ratio_fh",
                    "label": "Full screen height"
                },
                {
                    "value": "bee_ratioadapt_mix",
                    "label": "Adapt to image"
                },
                {
                    "value": "bee_ratioadapt_mix16_9",
                    "label": "16:9"
                },
                {
                    "value": "bee_ratioadapt_mix4_3",
                    "label": "4:3"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "custom_dk",
            "label": "Use custom height (Desktop)",
            "default": true
        },
        {
            "type": "number",
            "id": "height_dk",
            "label": "Section height (Desktop)",
            "default": 600
        },
        {
            "type": "checkbox",
            "id": "custom_tb",
            "label": "Use custom height (Tablet)",
            "default": true
        },
        {
            "type": "number",
            "id": "height_tb",
            "label": "Section height (Tablet)",
            "default": 400
        },
        {
            "type": "checkbox",
            "id": "custom_mb",
            "label": "Use custom height (Mobile)",
            "default": true
        },
        {
            "type": "number",
            "id": "height_mb",
            "label": "Section height (Mobile)",
            "default": 250
        },  
        {                   
            "type": "image_picker",
            "id": "image",
            "label": "Choose Image",
            "info": "1800 x 600px .jpg recommended"                       
        }, 
        {
            "type": "image_picker","id": "image_mb",
            "label": "Mobile image (optional)",
            "info": "750 x 1100px .jpg recommended. If none is set, desktop image will be used."
        },
        {
            "type": "checkbox",
            "id": "parallax",
            "label": "Enable parallax scroll",
            "default": false
        },
        {
          "type": "url",
          "id": "link_img1",
          "label": "Link image",
        "info": "The whole image becomes clickable."
        },
		{
          "type": "select",
          "id": "open_link",
          "label": "Open link in",
          "default": "_blank",
          "options": [
            {
              "value": "_self",
              "label": "Current window"
            },
            {
              "value": "_blank",
              "label": "New window"
            }
          ]
		},
        {
            "type": "header",
            "content": "+ Bottom arrow"
        },
        {
            "type": "checkbox",
            "id": "bottom_arrow",
            "label": "Enable bottom arrow",
            "info": "When click will scroll the page to the section below.",
            "default": false
        },
        {
            "type": "select","id": "bottom_arrow_style", "default": "button",
            "label": "Style",
            "options": [
                {
                    "value": "simple","label": "Simple"
                },
                {
                    "value": "button","label": "Button"
                }
            ]
        },
        {
            "type": "select","id": "bottom_arrow_color", "default": "light",
            "label": "Color",
            "options": [
                {
                    "value": "light","label": "Light"
                },
                {
                    "value": "dark","label": "Dark"
                }
            ]
        },
        {
            "type": "select","id": "bottom_arrow_pos", "default": "2",
            "label": "Position",
            "options": [
                {
                    "value": "1","label": "Bottom"
                },
                {
                    "value": "2","label": "Bottom alternative"
                }
            ]
        },
        {
            "type": "header",
            "content": "+ Content position"
        },
        {
            "type": "select",
            "id": "text_align",
            "label": "Content align",
            "default": "center",
            "options": [
                {
                    "label": "Left",
                    "value": "start"
                },
                {
                    "label": "Center",
                    "value": "center"
                },
                {
                    "label": "Right",
                    "value": "end"
                }
            ]
        },
        {
            "type": "select",
            "id": "text_align_tb",
            "label": "Content align (Tablet)",
            "default": "center",
            "options": [
                {
                    "label": "Left",
                    "value": "start"
                },
                {
                    "label": "Center",
                    "value": "center"
                },
                {
                    "label": "Right",
                    "value": "end"
                }
            ]
        },
        {
            "type": "select",
            "id": "text_align_mb",
            "label": "Content align (Mobile)",
            "default": "center",
            "options": [
                {
                    "label": "Left",
                    "value": "start"
                },
                {
                    "label": "Center",
                    "value": "center"
                },
                {
                    "label": "Right",
                    "value": "end"
                }
            ]
        },
        {
            "type": "select",
            "id": "content_width",
            "label": "Content width",
            "default": "auto",
            "options": [
                {
                    "label": "Auto",
                    "value": "auto"
                },
                {
                    "label": "Container",
                    "value": "container"
                }
            ]
        },
        {
            "type": "header",
            "content": "--Content position options--"
        },
        {
            "type": "paragraph",
            "content": "Warning: \"Content horizontal position\" options doesn't work when using \"Content width\" is 'Container'"
          },
        {
            "type": "range",
            "id": "cv_pos",
            "label": "Content vertical position",
            "info": " <= 50: Top position , > 50 bottom position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "range",
            "id": "ch_pos",
            "label": "Content horizontal position",
            "info": " <= 50: Left position , > 50 right position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "header",
            "content": "--Content position options (Tablet)--"
        },
        {
            "type": "range",
            "id": "cv_pos_tb",
            "label": "Content vertical position",
            "info": " <= 50: Top position , > 50 bottom position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "range",
            "id": "ch_pos_tb",
            "label": "Content horizontal position",
            "info": " <= 50: Left position , > 50 right position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "header",
            "content": "--Content position options (Mobile)--"
        },
        {
            "type": "range",
            "id": "cv_pos_mb",
            "label": "Content vertical position",
            "info": " <= 50: Top position , > 50 bottom position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "range",
            "id": "ch_pos_mb",
            "label": "Content horizontal position",
            "info": " <= 50: Left position , > 50 right position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "header",
            "content": "+ Content background, color options"
        },
        {
            "type": "color",
            "id": "bg_overlay",
            "label": "Overlay",
            "default": "#000"
        },
        {
            "type": "range",
            "id": "bg_opacity",
            "label": "Overlay opacity",
            "default": 0,
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%"
        },
        {
            "type": "color",
            "id": "bg_content_cl",
            "label": "Background color",
            "default": "#fff"
        },
        {
            "type": "range",
            "id": "bg_content_op",
            "label": "Background color opacity",
            "default": 0,
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%"
        },
        {
            "type": "number",
            "id": "content_pd_tb",
            "label": "Content padding top/bottom (px)",
            "default": 15,
            "info": "Working on the Desktop"   
        },
        {
            "type": "number",
            "id": "content_pd_lr",
            "label": "Content padding left/right (px)",
            "default": 15 ,
            "info": "Working on the Desktop"   
        },
        {
            "type": "number",
            "id": "content_pd_tb_mb",
            "label": "Content padding top/bottom (px)",
            "default": 10,
            "info": "Working on the Mobile"    
        },
        {
            "type": "number",
            "id": "content_pd_lr_mb",
            "label": "Content padding left/right (px)",
            "default": 10,
            "info": "Working on the mobile"
        },
        {"type": "paragraph","content": "————————————————"},
        {
            "type": "checkbox",
            "id": "border_bl",
            "label": "Use border content",
            "default": false
        },
        {
            "type": "color",
            "id": "br_color",
            "label": "Border color",
            "default": "#222"
        },
        {
            "type": "color",
            "id": "br_bg",
            "label": "Background border",
            "default": "#fff"
        },
        {
            "type": "range",
            "id": "br_opacity",
            "label": "Border opacity",
            "default": 0,
            "min": 0,
            "default": 50,
            "max": 100,
            "step": 1,
            "unit": "%"
        },
        {
            "type": "select",
            "id": "br_style",
            "label": "Border style",
            "default": "solid",
            "options": [
                {
                    "value": "none",
                    "label": "None"
                },
                {
                    "value": "solid",
                    "label": "Solid"
                },
                {
                    "value": "dashed",
                    "label": "Dashed"
                },
                {
                    "value": "double",
                    "label": "Double"
                }
            ]
        },
        {
            "type": "range",
            "id": "br_pd",
            "label": "Border padding (Desktop)",
            "default": 20,
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "px"
        },
        {
            "type": "range",
            "id": "br_pd_mb",
            "label": "Border padding (Mobile)",
            "default": 10,
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "px"
        },
        {
            "type": "header",
            "content": "--Animation Options--"
        },
        {
            "type": "range",
            "id": "time_animation",
            "label": "Duration animation each block",
            "max": 5,
            "min": 1,
            "default": 1,
            "unit": "s",
            "step": 0.5
        },
        {
            "type": "range",
            "id": "animation_delay",
            "label": "Time animation delay",
            "max": 110,
            "min": 10,
            "step": 10,
            "unit": "%",
            "default": 40,
            "info": "Defines the number of time to wait when the animation previous end, before the animation next will start."
        },
        {
            "type": "header",
            "content": "2. Design options"
        },
        {
            "type": "select","id": "layout","default": "bee-container-fluid","label": "Layout",
            "options": [
                {"value": "bee-container-wrap", "label": "Wrapped container"},
                { "value": "bee-container-fluid", "label": "Full width"}
            ]
        },
        {
            "type": "color",
            "id": "cl_bg",
            "label": "Background"
        },
        {
            "type": "color_background",
            "id": "cl_bg_gradient",
            "label": "Background gradient"
        },
        {
            "type": "text",
            "id": "mg",
            "label": "Margin",
            "info": "Margin top, margin right, margin bottom, margin left. If you do not use it please blank.",
            "default": ",,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd",
            "label": "Padding",
            "info": "Padding top, padding right, padding bottom, padding left. If you do not use it please blank.",
            "placeholder": "50px,,50px,"
        },
        {
            "type": "header",
            "content": "+ Design tablet options"
        },
        {
            "type": "text",
            "id": "mg_tb",
            "label": "Margin",
            "default": ",,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_tb",
            "label": "Padding",
            "placeholder": "50px,,50px,"
        },
        {
            "type": "header",
            "content": "+ Design Mobile Options"
        },
        {
            "type": "text",
            "id": "mg_mb",
            "label": "Margin",
            "default": ",,30px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_mb",
            "label": "Padding",
            "placeholder": ",,50px,"
        }
    ],
    "blocks": [
        {
            "type": "custom_text",
            "name": "Text",
            "settings": [
                {
                    "type": "textarea",
                    "id": "text",
                    "label": "Input text",
                    "default": "Text",
                    "info": "If you want to line break, please add a <br> tag in the text"
                },
                {
                    "type": "checkbox",
                    "id": "remove_br_tag",
                    "label": "Remove <br> tag on tablet & mobile",
                    "default": false
                },
                {
                    "type": "select",
                    "id": "tag",
                    "label": "Html tag",
                    "default": "p",
                    "options": [
                        {
                            "value": "h2",
                            "label": "H2"
                        },
                        {
                            "value": "h3",
                            "label": "H3"
                        },
                        {
                            "value": "h4",
                            "label": "H4"
                        },
                        {
                            "value": "h5",
                            "label": "H5"
                        },
                        {
                            "value": "h6",
                            "label": "H6"
                        },
                        {
                            "value": "p",
                            "label": "P"
                        },
                        {
                            "value": "div",
                            "label": "DIV"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "fontf",
                    "default": "inherit",
                    "label": "Font family",
                    "options": [
                        {
                            "label": "Inherit",
                            "value": "inherit"
                        },
                        {
                            "label": "Font Family #1",
                            "value": "1"
                        },
                        {
                            "label": "Font Family #2",
                            "value": "2"
                        },
                        {
                            "label": "Font Family #3",
                            "value": "3"
                        }
                    ]
                },
                {
                    "type": "color",
                    "id": "text_cl",
                    "label": "Color text",
                    "default": "#fff"
                },
                {
                    "type": "range",
                    "id": "text_fs",
                    "label": "Font size",
                    "max": 100,
                    "min": 10,
                    "step": 1,
                    "unit": "px",
                    "default": 16
                },
                {
                    "type": "range",
                    "id": "text_lh",
                    "label": "Line height",
                    "max": 100,
                    "min": 0,
                    "step": 1,
                    "default": 0,
                    "unit": "px",
                    "info": "Set '0' to use default"            
                },
                {
                    "type": "range",
                    "id": "text_fw",
                    "label": "Font weight",
                    "min": 100,
                    "max": 900,
                    "step": 100,
                    "default": 400
                },
                {
                    "type": "range",
                    "id": "text_ls",
                    "label": "Letter spacing",
                    "max": 10,
                    "min": 0,
                    "default": 0,
                    "step": 0.1,
                    "unit": "px"
                },
                {
                    "type": "number",
                    "id": "mgb",
                    "label": "Margin bottom",
                    "default": 15
                },
                {
                    "type": "checkbox",
                    "id": "font_italic",
                    "label": "Enable font italic style",
                    "default": false
                },
                {
                    "type": "checkbox",
                    "id": "text_shadow",
                    "label": "Enable text shadow",
                    "default": false
                },
                {
                    "type": "header",
                    "content": "+ Options on tablet"
                },
                {
                    "type": "range",
                    "id": "text_fs_tb",
                    "label": "Font size",
                    "max": 60,
                    "min": 10,
                    "step": 1,
                    "unit": "px",
                    "default": 16
                },
                {
                    "type": "range",
                    "id": "text_lh_tb",
                    "label": "Line height",
                    "max": 70,
                    "min": 0,
                    "step": 1,
                    "default": 0,
                    "unit": "px",
                    "info": "Set '0' to use default"            
                },
                {
                    "type": "range",
                    "id": "text_ls_tb",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "mgb_tb",
                    "label": "Margin bottom",
                    "default": 10
                },
                {
                    "type": "header",
                    "content": "+  Options mobile"
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile",
                    "default": false
                },
                {
                    "type": "range",
                    "id": "text_fs_mb",
                    "label": "Font size",
                    "max": 60,
                    "min": 10,
                    "step": 1,
                    "unit": "px",
                    "default": 16
                },
                {
                    "type": "range",
                    "id": "text_lh_mb",
                    "label": "Line height",
                    "max": 70,
                    "min": 0,
                    "step": 1,
                    "default": 0,
                    "unit": "px",
                    "info": "Set '0' to use default"            
                },
                {
                    "type": "range",
                    "id": "text_ls_mb",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "mgb_mb",
                    "label": "Margin bottom",
                    "default": 10
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                "type": "select",
                "id": "animation",
                "label": "Animation",
                "default": "none",
                "options": [
                    {
                        "label": "None",
                        "value": "none"
                    },
                    {
                        "label": "fadeIn",
                        "value": "fadeIn"
                    },
                    {
                        "label": "fadeInDown",
                        "value": "fadeInDown"
                    },
                    {
                        "label": "fadeInDownBig",
                        "value": "fadeInDownBig"
                    },
                    {
                        "label": "fadeInLeft",
                        "value": "fadeInLeft"
                    },
                    {
                        "label": "fadeInLeftBig",
                        "value": "fadeInLeftBig"
                    },
                    {
                        "label": "fadeInRight",
                        "value": "fadeInRight"
                    },
                    {
                        "label": "fadeInRightBig",
                        "value": "fadeInRightBig"
                    },
                    {
                        "label": "fadeInUp",
                        "value": "fadeInUp"
                    },
                    {
                        "label": "fadeInUpBig",
                        "value": "fadeInUpBig"
                    },
                    {
                        "label": "fadeInTopLeft",
                        "value": "fadeInTopLeft"
                    },
                    {
                        "label": "fadeInTopRight",
                        "value": "fadeInTopRight"
                    },
                    {
                        "label": "fadeInBottomLeft",
                        "value": "fadeInBottomLeft"
                    },
                    {
                        "label": "fadeInBottomRight",
                        "value": "fadeInBottomRight"
                    },
                    {
                        "label": "bounceIn",
                        "value": "bounceIn"
                    },
                    {
                        "label": "bounceInDown",
                        "value": "bounceInDown"
                    },
                    {
                        "label": "bounceInLeft",
                        "value": "bounceInLeft"
                    },
                    {
                        "label": "bounceInRight",
                        "value": "bounceInRight"
                    },
                    {
                        "label": "bounceInUp",
                        "value": "bounceInUp"
                    },
                    {
                        "label": "zoomIn",
                        "value": "zoomIn"
                    },
                    {
                        "label": "zoomInDown",
                        "value": "zoomInDown"
                    },
                    {
                        "label": "zoomInLeft",
                        "value": "zoomInLeft"
                    },
                    {
                        "label": "zoomInRight",
                        "value": "zoomInRight"
                    },
                    {
                        "label": "zoomInUp",
                        "value": "zoomInUp"
                    },
                    {
                        "label": "slideInDown",
                        "value": "slideInDown"
                    },
                    {
                        "label": "slideInLeft",
                        "value": "slideInLeft"
                    },
                    {
                        "label": "slideInRight",
                        "value": "slideInRight"
                    },
                    {
                        "label": "slideInUp",
                        "value": "slideInUp"
                    },
                    {
                        "label": "lightSpeedInRight",
                        "value": "lightSpeedInRight"
                    },
                    {
                        "label": "lightSpeedInLeft",
                        "value": "lightSpeedInLeft"
                    },
                    {
                        "label": "lightSpeedOutRight",
                        "value": "lightSpeedOutRight"
                    },
                    {
                        "label": "lightSpeedOutLeft",
                        "value": "lightSpeedOutLeft"
                    },
                    {
                        "label": "jello",
                        "value": "jello"
                    },
                    {
                        "label": "tada",
                        "value": "tada"
                    },
                    {
                        "label": "pulse",
                        "value": "pulse"
                    }
                ]
                }
            ]
        },
        {
            "type": "html",
            "name": "HTML",
            "settings": [
                {
                    "type": "html",
                    "id": "html_content",
                    "label": "Type html"
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile",
                    "default": false
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                    "type": "select",
                    "id": "animation",
                    "label": "Animation",
                    "default": "none",
                    "options": [
                    {
                        "label": "None",
                        "value": "none"
                    },
                    {
                        "label": "fadeIn",
                        "value": "fadeIn"
                    },
                    {
                        "label": "fadeInDown",
                        "value": "fadeInDown"
                    },
                    {
                        "label": "fadeInDownBig",
                        "value": "fadeInDownBig"
                    },
                    {
                        "label": "fadeInLeft",
                        "value": "fadeInLeft"
                    },
                    {
                        "label": "fadeInLeftBig",
                        "value": "fadeInLeftBig"
                    },
                    {
                        "label": "fadeInRight",
                        "value": "fadeInRight"
                    },
                    {
                        "label": "fadeInRightBig",
                        "value": "fadeInRightBig"
                    },
                    {
                        "label": "fadeInUp",
                        "value": "fadeInUp"
                    },
                    {
                        "label": "fadeInUpBig",
                        "value": "fadeInUpBig"
                    },
                    {
                        "label": "fadeInTopLeft",
                        "value": "fadeInTopLeft"
                    },
                    {
                        "label": "fadeInTopRight",
                        "value": "fadeInTopRight"
                    },
                    {
                        "label": "fadeInBottomLeft",
                        "value": "fadeInBottomLeft"
                    },
                    {
                        "label": "fadeInBottomRight",
                        "value": "fadeInBottomRight"
                    },
                    {
                        "label": "bounceIn",
                        "value": "bounceIn"
                    },
                    {
                        "label": "bounceInDown",
                        "value": "bounceInDown"
                    },
                    {
                        "label": "bounceInLeft",
                        "value": "bounceInLeft"
                    },
                    {
                        "label": "bounceInRight",
                        "value": "bounceInRight"
                    },
                    {
                        "label": "bounceInUp",
                        "value": "bounceInUp"
                    },
                    {
                        "label": "zoomIn",
                        "value": "zoomIn"
                    },
                    {
                        "label": "zoomInDown",
                        "value": "zoomInDown"
                    },
                    {
                        "label": "zoomInLeft",
                        "value": "zoomInLeft"
                    },
                    {
                        "label": "zoomInRight",
                        "value": "zoomInRight"
                    },
                    {
                        "label": "zoomInUp",
                        "value": "zoomInUp"
                    },
                    {
                        "label": "slideInDown",
                        "value": "slideInDown"
                    },
                    {
                        "label": "slideInLeft",
                        "value": "slideInLeft"
                    },
                    {
                        "label": "slideInRight",
                        "value": "slideInRight"
                    },
                    {
                        "label": "slideInUp",
                        "value": "slideInUp"
                    },
                    {
                        "label": "lightSpeedInRight",
                        "value": "lightSpeedInRight"
                    },
                    {
                        "label": "lightSpeedInLeft",
                        "value": "lightSpeedInLeft"
                    },
                    {
                        "label": "lightSpeedOutRight",
                        "value": "lightSpeedOutRight"
                    },
                    {
                        "label": "lightSpeedOutLeft",
                        "value": "lightSpeedOutLeft"
                    },
                    {
                        "label": "jello",
                        "value": "jello"
                    },
                    {
                        "label": "tada",
                        "value": "tada"
                    },
                    {
                        "label": "pulse",
                        "value": "pulse"
                    }
                    ]
                }
            ]
        },
        {
            "type": "image",
            "name": "Image (Child)",
            "settings": [
                {
                    "type": "image_picker",
                    "id": "image_child",
                    "label": "Image (Child)"
                },
                {
                    "type": "number",
                    "id": "img_width",
                    "label": "Image width (Unit: px)",
                    "info": "Set 0 to use width default of image",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "img_width_tb",
                    "label": "Image width on tablet (Unit: px)",
                    "info": "Set 0 to use width default of image",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "img_width_mb",
                    "label": "Image width on mobile (Unit: px)",
                    "info": "Set 0 to use width default of image",
                    "default": 0
                },
                {
                    "type": "url",
                    "id": "img_link",
                    "label": "Image link"
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile ",
                    "default": false
                },
                {
                    "type": "number",
                    "id": "mgb",
                    "label": "Margin bottom (Unit: px)",
                    "default": 20
                },
                {
                    "type": "number",
                    "id": "mgb_tb",
                    "label": "Margin bottom on tablet(Unit: px)",
                    "default": 20
                },
                {
                    "type": "number",
                    "id": "mgb_mb",
                    "label": "Margin bottom on mobile(Unit: px)",
                    "default": 10
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                    "type": "select",
                    "id": "animation",
                    "label": "Animation",
                    "default": "none",
                    "options": [
                        {
                            "label": "None",
                            "value": "none"
                        },
                        {
                            "label": "fadeIn",
                            "value": "fadeIn"
                        },
                        {
                            "label": "fadeInDown",
                            "value": "fadeInDown"
                        },
                        {
                            "label": "fadeInDownBig",
                            "value": "fadeInDownBig"
                        },
                        {
                            "label": "fadeInLeft",
                            "value": "fadeInLeft"
                        },
                        {
                            "label": "fadeInLeftBig",
                            "value": "fadeInLeftBig"
                        },
                        {
                            "label": "fadeInRight",
                            "value": "fadeInRight"
                        },
                        {
                            "label": "fadeInRightBig",
                            "value": "fadeInRightBig"
                        },
                        {
                            "label": "fadeInUp",
                            "value": "fadeInUp"
                        },
                        {
                            "label": "fadeInUpBig",
                            "value": "fadeInUpBig"
                        },
                        {
                            "label": "fadeInTopLeft",
                            "value": "fadeInTopLeft"
                        },
                        {
                            "label": "fadeInTopRight",
                            "value": "fadeInTopRight"
                        },
                        {
                            "label": "fadeInBottomLeft",
                            "value": "fadeInBottomLeft"
                        },
                        {
                            "label": "fadeInBottomRight",
                            "value": "fadeInBottomRight"
                        },
                        {
                            "label": "bounceIn",
                            "value": "bounceIn"
                        },
                        {
                            "label": "bounceInDown",
                            "value": "bounceInDown"
                        },
                        {
                            "label": "bounceInLeft",
                            "value": "bounceInLeft"
                        },
                        {
                            "label": "bounceInRight",
                            "value": "bounceInRight"
                        },
                        {
                            "label": "bounceInUp",
                            "value": "bounceInUp"
                        },
                        {
                            "label": "zoomIn",
                            "value": "zoomIn"
                        },
                        {
                            "label": "zoomInDown",
                            "value": "zoomInDown"
                        },
                        {
                            "label": "zoomInLeft",
                            "value": "zoomInLeft"
                        },
                        {
                            "label": "zoomInRight",
                            "value": "zoomInRight"
                        },
                        {
                            "label": "zoomInUp",
                            "value": "zoomInUp"
                        },
                        {
                            "label": "slideInDown",
                            "value": "slideInDown"
                        },
                        {
                            "label": "slideInLeft",
                            "value": "slideInLeft"
                        },
                        {
                            "label": "slideInRight",
                            "value": "slideInRight"
                        },
                        {
                            "label": "slideInUp",
                            "value": "slideInUp"
                        },
                        {
                            "label": "lightSpeedInRight",
                            "value": "lightSpeedInRight"
                        },
                        {
                            "label": "lightSpeedInLeft",
                            "value": "lightSpeedInLeft"
                        },
                        {
                            "label": "lightSpeedOutRight",
                            "value": "lightSpeedOutRight"
                        },
                        {
                            "label": "lightSpeedOutLeft",
                            "value": "lightSpeedOutLeft"
                        },
                        {
                            "label": "jello",
                            "value": "jello"
                        },
                        {
                            "label": "tada",
                            "value": "tada"
                        },
                        {
                            "label": "pulse",
                            "value": "pulse"
                        }
                    ]
                }
            ]
        },
        {
            "type": "custom_button",
            "name": "Button",
            "settings": [
                {
                    "type": "text",
                    "id": "button_text",
                    "label": "Button label",
                    "default": "Button label",
                    "info": "If set blank will not show"
                },
                {
                    "type": "url",
                    "id": "button_link",
                    "label": "Button link",
                    "info": "If set blank will not show"
                },
                {
                    "type": "select",
                    "id": "target_link",
                    "label": "Open link in",
                    "default": "_self",
                    "options": [
                        {
                            "value": "_self",
                            "label": "Current window"
                        },
                        {
                            "value": "_blank",
                            "label": "New window"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "fontf",
                    "default": "inherit",
                    "label": "Font family",
                    "options": [
                        {
                            "label": "Inherit",
                            "value": "inherit"
                        },
                        {
                            "label": "Font Family #1",
                            "value": "1"
                        },
                        {
                            "label": "Font Family #2",
                            "value": "2"
                        },
                        {
                            "label": "Font Family #3",
                            "value": "3"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "button_style",
                    "label": "Button style",
                    "options": [
                        {
                            "label": "Default",
                            "value": "default"
                        },
                        {
                            "label": "Outline",
                            "value": "outline"
                        },
                        {
                            "label": "Bordered top",
                            "value": "bordered_top"
                        },
                        {
                            "label": "Border bottom", 
                            "value": "bordered"
                        },
                        {
                            "label": "Link",
                            "value": "link"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "icon_style",
                    "default": "1",
                    "label": "Icon style",
                    "options": [
                        {
                            "label": "Arrow",
                            "value": "2"
                        },
                        {
                            "label": "Long arrow",
                            "value": "1"
                        }
                    ]
                },
                {
                    "type": "range",
                    "id": "button_icon_w",
                    "label": "Button icon width",
                    "min": 0,
                    "max": 50,
                    "step": 1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "range",
                    "id": "border_w",
                    "label": "Border width",
                    "min": 1,
                    "max": 3,
                    "step": 1,
                    "default": 1,
                    "unit": "px"
                },
                {
                    "type": "select",
                    "id": "button_effect",
                    "label": "Hover button effect",
                    "default": "default",
                    "info": "Only working button style default, outline, highlights icon",
                    "options": [
                        {
                            "label": "Default",
                            "value": "default"
                        },
                        {
                            "label": "Fade",
                            "value": "fade"
                        },
                        {
                            "label": "Rectangle out",
                            "value": "rectangle-out"
                        },
                        {
                            "label": "Sweep to right",
                            "value": "sweep-to-right"
                        },
                        {
                            "label": "Sweep to left",
                            "value": "sweep-to-left"
                        },
                        {
                            "label": "Sweep to bottom",
                            "value": "sweep-to-bottom"
                        },
                        {
                            "label": "Sweep to top",
                            "value": "sweep-to-top"
                        },
                        {
                            "label": "Shutter out horizontal",
                            "value": "shutter-out-horizontal"
                        },
                        {
                            "label": "Outline",
                            "value": "outline"
                        },
                        {
                            "label": "Shadow",
                            "value": "shadow"
                        }
                    ]
                },
                {
                    "type": "color",
                    "id": "pri_cl",
                    "label": "Primary color",
                    "default": "#222"
                },
                {
                    "type": "color",
                    "id": "second_cl",
                    "label": "Secondary color",
                    "default": "#fff",
                    "info": "Only working button style default, highlights icon"
                },
                {
                    "type": "color",
                    "id": "pri_cl_hover",
                    "label": "Primary hover color",
                    "default": "#0ec1ae"
                },
                {
                    "type": "color",
                    "id": "second_cl_hover",
                    "label": "Secondary hover color",
                    "info": "Only working button style default, outline, highlights icon",
                    "default": "#fff"
                },
                {
                    "type": "range",
                    "id": "fsbutton",
                    "label": "Font size",
                    "max": 50,
                    "min": 10,
                    "step": 1,
                    "unit": "px",
                    "default": 14
                },
                {
                    "type": "range",
                    "id": "fwbutton",
                    "label": "Font weight",
                    "min": 100,
                    "max": 900,
                    "step": 100,
                    "default": 400
                },
                {
                    "type": "range",
                    "id": "button_ls",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "range",
                    "id": "button_mh",
                    "label": "Min height",
                    "min": 30,
                    "max": 80,
                    "step": 1,
                    "unit": "px",
                    "default": 42,
                    "info": "Not working button style link"
                },
                {
                    "type": "range",
                    "id": "button_bdr",
                    "label": "Border radius",
                    "min": 0,
                    "max": 40,
                    "step": 1,
                    "unit": "px",
                    "default": 0,
                    "info": "Only working button style default, outline, highlights icon"
                },
                {
                    "type": "range",
                    "id": "button_pd_lr",
                    "label": "Padding left/right",
                    "min": 0,
                    "max": 100,
                    "step": 1,
                    "unit": "px",
                    "default": 20,
                    "info": "Only working button style default, outline, highlights icon"
                },
                {
                    "type": "number",
                    "id": "mgb",
                    "label": "Margin bottom",
                    "default": 0
                },
                {
                    "type": "header",
                    "content": "+ Options tablet"
                },
                {
                    "type": "range",
                    "id": "button_icon_w_tb",
                    "label": "Button icon width",
                    "min": 0,
                    "max": 50,
                    "step": 1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "range",
                    "id": "fsbutton_tb",
                    "label": "Font size",
                    "max": 50,
                    "min": 0,
                    "step": 1,
                    "unit": "px",
                    "default": 12
                },
                {
                    "type": "range",
                    "id": "button_mh_tb",
                    "label": "Min height",
                    "min": 10,
                    "max": 50,
                    "step": 1,
                    "unit": "px",
                    "default": 36,
                    "info": "Not working button style link"
                },
                {
                    "type": "range",
                    "id": "button_pd_lr_tb",
                    "label": "Padding left/right",
                    "min": 0,
                    "max": 60,
                    "step": 1,
                    "unit": "px",
                    "default": 15,
                    "info": "Only working button style default, outline, highlights icon"
                },
                {
                    "type": "range",
                    "id": "button_ls_tb",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "mgb_tb",
                    "label": "Margin bottom",
                    "default": 0
                },
                {
                    "type": "header",
                    "content": "+ Options mobile"
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile",
                    "default": false
                },
                {
                    "type": "range",
                    "id": "button_icon_w_mb",
                    "label": "Button icon width",
                    "min": 0,
                    "max": 50,
                    "step": 1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "range",
                    "id": "fsbutton_mb",
                    "label": "Font size",
                    "max": 50,
                    "min": 0,
                    "step": 1,
                    "unit": "px",
                    "default": 10
                },
                {
                    "type": "range",
                    "id": "button_mh_mb",
                    "label": "Min height",
                    "min": 10,
                    "max": 50,
                    "step": 1,
                    "unit": "px",
                    "default": 36,
                    "info": "Not working button style link"
                },
                {
                    "type": "range",
                    "id": "button_pd_lr_mb",
                    "label": "Padding left/right",
                    "min": 0,
                    "max": 100,
                    "step": 1,
                    "unit": "px",
                    "default": 15,
                    "info": "Only working button style default, outline, highlights icon"
                },
                {
                    "type": "range",
                    "id": "button_ls_mb",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "mgb_mb",
                    "label": "Margin bottom)",
                    "default": 0
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                    "type": "select",
                    "id": "animation",
                    "label": "Animation",
                    "default": "none",
                    "options": [
                        {
                            "label": "None",
                            "value": "none"
                        },
                        {
                            "label": "fadeIn",
                            "value": "fadeIn"
                        },
                        {
                            "label": "fadeInDown",
                            "value": "fadeInDown"
                        },
                        {
                            "label": "fadeInDownBig",
                            "value": "fadeInDownBig"
                        },
                        {
                            "label": "fadeInLeft",
                            "value": "fadeInLeft"
                        },
                        {
                            "label": "fadeInLeftBig",
                            "value": "fadeInLeftBig"
                        },
                        {
                            "label": "fadeInRight",
                            "value": "fadeInRight"
                        },
                        {
                            "label": "fadeInRightBig",
                            "value": "fadeInRightBig"
                        },
                        {
                            "label": "fadeInUp",
                            "value": "fadeInUp"
                        },
                        {
                            "label": "fadeInUpBig",
                            "value": "fadeInUpBig"
                        },
                        {
                            "label": "fadeInTopLeft",
                            "value": "fadeInTopLeft"
                        },
                        {
                            "label": "fadeInTopRight",
                            "value": "fadeInTopRight"
                        },
                        {
                            "label": "fadeInBottomLeft",
                            "value": "fadeInBottomLeft"
                        },
                        {
                            "label": "fadeInBottomRight",
                            "value": "fadeInBottomRight"
                        },
                        {
                            "label": "bounceIn",
                            "value": "bounceIn"
                        },
                        {
                            "label": "bounceInDown",
                            "value": "bounceInDown"
                        },
                        {
                            "label": "bounceInLeft",
                            "value": "bounceInLeft"
                        },
                        {
                            "label": "bounceInRight",
                            "value": "bounceInRight"
                        },
                        {
                            "label": "bounceInUp",
                            "value": "bounceInUp"
                        },
                        {
                            "label": "zoomIn",
                            "value": "zoomIn"
                        },
                        {
                            "label": "zoomInDown",
                            "value": "zoomInDown"
                        },
                        {
                            "label": "zoomInLeft",
                            "value": "zoomInLeft"
                        },
                        {
                            "label": "zoomInRight",
                            "value": "zoomInRight"
                        },
                        {
                            "label": "zoomInUp",
                            "value": "zoomInUp"
                        },
                        {
                            "label": "slideInDown",
                            "value": "slideInDown"
                        },
                        {
                            "label": "slideInLeft",
                            "value": "slideInLeft"
                        },
                        {
                            "label": "slideInRight",
                            "value": "slideInRight"
                        },
                        {
                            "label": "slideInUp",
                            "value": "slideInUp"
                        },
                        {
                            "label": "lightSpeedInRight",
                            "value": "lightSpeedInRight"
                        },
                        {
                            "label": "lightSpeedInLeft",
                            "value": "lightSpeedInLeft"
                        },
                        {
                            "label": "lightSpeedOutRight",
                            "value": "lightSpeedOutRight"
                        },
                        {
                            "label": "lightSpeedOutLeft",
                            "value": "lightSpeedOutLeft"
                        },
                        {
                            "label": "jello",
                            "value": "jello"
                        },
                        {
                            "label": "tada",
                            "value": "tada"
                        },
                        {
                            "label": "pulse",
                            "value": "pulse"
                        }
                    ]
                }
            ]
        },
        {
            "type": "button_video",
            "name": "Button video",
            "limit": 1,
            "settings": [
                {
                    "type": "select",
                    "id": "source",
                    "label": "Source video",
                    "default": "1",
                    "options": [
                        {
                            "value": "1",
                            "label": "Youtube or Vimeo"
                        },
                        {
                            "value": "2",
                            "label": "Shopify-hosted"
                        }
                    ]
                },
                {
                    "id": "video_url",
                    "type": "video_url",
                    "label": "A video from Youtube or Vimeo",
                    "accept": ["youtube","vimeo"],
                    "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc"
                },
                {
                  "type": "video",
                  "id": "video",
                  "label": "A Shopify-hosted video",
                  "info": "File video link from uploaded files. File size is smaller 4 mb recommended"
                },
                {
                    "type": "checkbox",
                    "id": "au_video",
                    "label": "Enable video autoplay",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "loop_video",
                    "label": "Enable video looping",
                    "default": true
                },
                {
                    "type": "checkbox",
                    "id": "btn_close",
                    "label": "Show button close when video inline playing",
                    "default": true
                },
                {
                    "type": "select",
                    "id": "click_action",
                    "label": "Click action",
                    "default": "data-open-mfp-video",
                    "options": [
                        {
                            "label": "Video popup",
                            "value": "data-open-mfp-video"
                        },
                        {
                            "label": "Video inline",
                            "value": "data-video-poster-btn"
                        }
                    ]                   
                },
                {
                    "type": "text",
                    "id": "btn_label",
                    "label": "Button label",
                    "info": "If set blank will not show"
                },
                {
                    "type": "header",
                    "content": "+ Color options"
                },
                {
                    "type": "color",
                    "id": "button_bg",
                    "label": "Background",
                    "default": "#fff"
                },
                {
                    "type": "color",
                    "id": "button_br_color",
                    "label": "Boder color",
                    "default": "#222"
                },
                {
                    "type": "color",
                    "id": "icon_color",
                    "label": "Icon color",
                    "default": "#222"
                },
                {
                    "type": "color",
                    "id": "label_color",
                    "label": "Label color",
                    "default": "#222"
                },  
                {
                    "type": "header",
                    "content": "+ Design button video"
                },
                {
                    "type": "range",
                    "id": "button_width",
                    "label": "Button width",
                    "min": 40,
                    "max": 100,
                    "unit": "px",
                    "default": 74
                },
                {
                    "type": "range",
                    "id": "icon_width",
                    "label": "Icon width",
                    "min": 10,
                    "max": 20,
                    "unit": "px",
                    "default": 12
                },
                {
                    "type": "range",
                    "id": "button_fs",
                    "label": "Button font size",
                    "min": 10,
                    "max": 30,
                    "step": 1,
                    "unit": "px",
                    "default": 12
                },
                {
                    "type": "range",
                    "id": "button_fw",
                    "label": "Button font weight",
                    "min": 300,
                    "max": 900,
                    "step": 100,
                    "default": 500
                },
                {
                    "type": "checkbox",
                    "id": "use_border",
                    "label": "Use border"
                },
                {
                    "type": "number",
                    "id": "mgb",
                    "label": "Margin bottom",
                    "default": 20
                },
                {
                    "type": "header",
                    "content": "+ Design tablet"
                },
                {
                    "type": "range",
                    "id": "button_width_tb",
                    "label": "Button width",
                    "min": 40,
                    "max": 80,
                    "unit": "px",
                    "default": 60
                },
                {
                    "type": "range",
                    "id": "icon_width_tb",
                    "label": "Icon width",
                    "min": 10,
                    "max": 20,
                    "unit": "px",
                    "default": 10
                },
                {
                    "type": "range",
                    "id": "button_fs_tb",
                    "label": "Button font size",
                    "min": 10,
                    "max": 30,
                    "step": 1,
                    "unit": "px",
                    "default": 12
                },
                {
                    "type": "number",
                    "id": "mgb_tb",
                    "label": "Margin bottom",
                    "default": 10
                },
                {
                    "type": "header",
                    "content": "+ Design mobile"
                },
                {
                    "type": "range",
                    "id": "button_width_mb",
                    "label": "Button width",
                    "min": 40,
                    "max": 80,
                    "unit": "px",
                    "default": 50
                },
                {
                    "type": "range",
                    "id": "icon_width_mb",
                    "label": "Icon width",
                    "min": 10,
                    "max": 20,
                    "unit": "px",
                    "default": 10
                },
                {
                    "type": "range",
                    "id": "button_fs_mb",
                    "label": "Button font size",
                    "min": 10,
                    "max": 20,
                    "step": 1,
                    "unit": "px",
                    "default": 12
                },
                {
                    "type": "number",
                    "id": "mgb_mb",
                    "label": "Margin bottom",
                    "default": 10
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                    "type": "select",
                    "id": "animation",
                    "label": "Animation",
                    "default": "none",
                    "options": [
                        {
                            "label": "None",
                            "value": "none"
                        },
                        {
                            "label": "fadeIn",
                            "value": "fadeIn"
                        },
                        {
                            "label": "fadeInDown",
                            "value": "fadeInDown"
                        },
                        {
                            "label": "fadeInDownBig",
                            "value": "fadeInDownBig"
                        },
                        {
                            "label": "fadeInLeft",
                            "value": "fadeInLeft"
                        },
                        {
                            "label": "fadeInLeftBig",
                            "value": "fadeInLeftBig"
                        },
                        {
                            "label": "fadeInRight",
                            "value": "fadeInRight"
                        },
                        {
                            "label": "fadeInRightBig",
                            "value": "fadeInRightBig"
                        },
                        {
                            "label": "fadeInUp",
                            "value": "fadeInUp"
                        },
                        {
                            "label": "fadeInUpBig",
                            "value": "fadeInUpBig"
                        },
                        {
                            "label": "fadeInTopLeft",
                            "value": "fadeInTopLeft"
                        },
                        {
                            "label": "fadeInTopRight",
                            "value": "fadeInTopRight"
                        },
                        {
                            "label": "fadeInBottomLeft",
                            "value": "fadeInBottomLeft"
                        },
                        {
                            "label": "fadeInBottomRight",
                            "value": "fadeInBottomRight"
                        },
                        {
                            "label": "bounceIn",
                            "value": "bounceIn"
                        },
                        {
                            "label": "bounceInDown",
                            "value": "bounceInDown"
                        },
                        {
                            "label": "bounceInLeft",
                            "value": "bounceInLeft"
                        },
                        {
                            "label": "bounceInRight",
                            "value": "bounceInRight"
                        },
                        {
                            "label": "bounceInUp",
                            "value": "bounceInUp"
                        },
                        {
                            "label": "zoomIn",
                            "value": "zoomIn"
                        },
                        {
                            "label": "zoomInDown",
                            "value": "zoomInDown"
                        },
                        {
                            "label": "zoomInLeft",
                            "value": "zoomInLeft"
                        },
                        {
                            "label": "zoomInRight",
                            "value": "zoomInRight"
                        },
                        {
                            "label": "zoomInUp",
                            "value": "zoomInUp"
                        },
                        {
                            "label": "slideInDown",
                            "value": "slideInDown"
                        },
                        {
                            "label": "slideInLeft",
                            "value": "slideInLeft"
                        },
                        {
                            "label": "slideInRight",
                            "value": "slideInRight"
                        },
                        {
                            "label": "slideInUp",
                            "value": "slideInUp"
                        },
                        {
                            "label": "lightSpeedInRight",
                            "value": "lightSpeedInRight"
                        },
                        {
                            "label": "lightSpeedInLeft",
                            "value": "lightSpeedInLeft"
                        },
                        {
                            "label": "lightSpeedOutRight",
                            "value": "lightSpeedOutRight"
                        },
                        {
                            "label": "lightSpeedOutLeft",
                            "value": "lightSpeedOutLeft"
                        },
                        {
                            "label": "jello",
                            "value": "jello"
                        },
                        {
                            "label": "tada",
                            "value": "tada"
                        },
                        {
                            "label": "pulse",
                            "value": "pulse"
                        }
                    ]
                }
            ]
        },
        {
            "type": "countdown",
            "name": "Countdown timer",
            "limit": 2,
            "settings": [
                {
                    "type": "text",
                    "id": "date",
                    "label": "Date countdown",
                    "default": "2022\/12\/26",
                    "info": "Countdown to the end sale date will be shown"
                },
                {
                    "type": "select",
                    "id": "cdt_des",
                    "label": "Countdown design",
                    "default": "1",
                    "options": [
                    {
                        "value": "1",
                        "label": "Design 1"
                    },
                    {
                        "value": "2",
                        "label": "Design 2"
                    }
                    ]
                },
                {
                    "type": "select",
                    "id": "cdt_size",
                    "label": "Countdown size",
                    "default": "medium",
                    "options": [
                        {
                        "value": "small",
                        "label": "Small"
                        },
                        {
                            "value": "medium",
                            "label": "Medium"
                        },
                        {
                            "value": "large",
                            "label": "Large"
                        },
                        {
                            "value": "extra_large",
                            "label": "Extra large"
                        }
                    ]
                },
                {
                    "type": "range",
                    "id": "box_bdr",
                    "label": "Border radius",
                    "default": 0,
                    "min": 0,
                    "max": 50,
                    "step": 1,
                    "unit": "%"
                },
                {
                    "type": "range",
                    "id": "bd_width",
                    "label": "Border width",
                    "default": 0,
                    "min": 0,
                    "max": 5,
                    "step": 1,
                    "unit": "px"
                },
                {
                    "type": "range",
                    "id": "space_item",
                    "label": "Space between items",
                    "default": 10,
                    "min": 0,
                    "max": 30,
                    "step": 1,
                    "unit": "px"
                },
                {
                    "type": "range",
                    "id": "space_item_tb",
                    "label": "Space between items (Tablet)",
                    "default": 10,
                    "min": 0,
                    "max": 30,
                    "step": 1,
                    "unit": "px"
                  },
                  {
                    "type": "range",
                    "id": "space_item_mb",
                    "label": "Space between items (Mobile)",
                    "default": 10,
                    "min": 0,
                    "max": 30,
                    "step": 1,
                    "unit": "px"
                  },
                {
                    "type": "color",
                    "id": "number_cl",
                    "label": "Number color",
                    "default": "#fff"
                },
                {
                    "type": "color",
                    "id": "text_cl",
                    "label": "Text color",
                    "default": "#fff"
                },
                {
                    "type": "color",
                    "id": "border_cl",
                    "label": "Border color item time",
                    "default": "#000"
                },
                {
                    "type": "color",
                    "id": "bg_cl",
                    "label": "Background item time",
                    "default": "#000"
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile ",
                    "default": false
                },
                {
                    "type": "number",
                    "id": "mgb",
                    "label": "Margin bottom",
                    "default": 15
                }, 
                {
                    "type": "number",
                    "id": "mgb_tb",
                    "label": "Margin bottom (Tablet)",
                    "default": 15
                }, 
                {
                    "type": "number",
                    "id": "mgb_mb",
                    "label": "Margin bottom (Mobile)",
                    "default": 10
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                    "type": "select",
                    "id": "animation",
                    "label": "Animation",
                    "default": "none",
                    "options": [
                        {
                            "label": "None",
                            "value": "none"
                        },
                        {
                            "label": "fadeIn",
                            "value": "fadeIn"
                        },
                        {
                            "label": "fadeInDown",
                            "value": "fadeInDown"
                        },
                        {
                            "label": "fadeInDownBig",
                            "value": "fadeInDownBig"
                        },
                        {
                            "label": "fadeInLeft",
                            "value": "fadeInLeft"
                        },
                        {
                            "label": "fadeInLeftBig",
                            "value": "fadeInLeftBig"
                        },
                        {
                            "label": "fadeInRight",
                            "value": "fadeInRight"
                        },
                        {
                            "label": "fadeInRightBig",
                            "value": "fadeInRightBig"
                        },
                        {
                            "label": "fadeInUp",
                            "value": "fadeInUp"
                        },
                        {
                            "label": "fadeInUpBig",
                            "value": "fadeInUpBig"
                        },
                        {
                            "label": "fadeInTopLeft",
                            "value": "fadeInTopLeft"
                        },
                        {
                            "label": "fadeInTopRight",
                            "value": "fadeInTopRight"
                        },
                        {
                            "label": "fadeInBottomLeft",
                            "value": "fadeInBottomLeft"
                        },
                        {
                            "label": "fadeInBottomRight",
                            "value": "fadeInBottomRight"
                        },
                        {
                            "label": "bounceIn",
                            "value": "bounceIn"
                        },
                        {
                            "label": "bounceInDown",
                            "value": "bounceInDown"
                        },
                        {
                            "label": "bounceInLeft",
                            "value": "bounceInLeft"
                        },
                        {
                            "label": "bounceInRight",
                            "value": "bounceInRight"
                        },
                        {
                            "label": "bounceInUp",
                            "value": "bounceInUp"
                        },
                        {
                            "label": "zoomIn",
                            "value": "zoomIn"
                        },
                        {
                            "label": "zoomInDown",
                            "value": "zoomInDown"
                        },
                        {
                            "label": "zoomInLeft",
                            "value": "zoomInLeft"
                        },
                        {
                            "label": "zoomInRight",
                            "value": "zoomInRight"
                        },
                        {
                            "label": "zoomInUp",
                            "value": "zoomInUp"
                        },
                        {
                            "label": "slideInDown",
                            "value": "slideInDown"
                        },
                        {
                            "label": "slideInLeft",
                            "value": "slideInLeft"
                        },
                        {
                            "label": "slideInRight",
                            "value": "slideInRight"
                        },
                        {
                            "label": "slideInUp",
                            "value": "slideInUp"
                        },
                        {
                            "label": "lightSpeedInRight",
                            "value": "lightSpeedInRight"
                        },
                        {
                            "label": "lightSpeedInLeft",
                            "value": "lightSpeedInLeft"
                        },
                        {
                            "label": "lightSpeedOutRight",
                            "value": "lightSpeedOutRight"
                        },
                        {
                            "label": "lightSpeedOutLeft",
                            "value": "lightSpeedOutLeft"
                        },
                        {
                            "label": "jello",
                            "value": "jello"
                        },
                        {
                            "label": "tada",
                            "value": "tada"
                        },
                        {
                            "label": "pulse",
                            "value": "pulse"
                        }
                    ]
                }
            ]
        },  
        {
            "type": "space_html",
            "name": "Space HTML",
            "settings": [
                {
                    "type": "color",
                    "id": "color",
                    "label": "Color",
                    "default": "#fff"
                },
                {
                    "type": "range",
                    "id": "width",
                    "min": 1,
                    "max": 100,
                    "step": 1,
                    "label": "Width",
                    "unit": "px",
                    "default": 40
                },
                {
                    "type": "range",
                    "id": "height",
                    "min": 1,
                    "max": 100,
                    "step": 1,
                    "label": "Height",
                    "unit": "px",
                    "default": 2
                },
                {
                    "type": "number",
                    "id": "mgb",
                    "label": "Margin bottom (Unit: px)",
                    "default": 20
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                    "type": "range",
                    "id": "width_tb",
                    "min": 1,
                    "max": 100,
                    "step": 1,
                    "label": "Width (Tablet)",
                    "unit": "px",
                    "default": 40
                },
                {
                    "type": "range",
                    "id": "height_tb",
                    "min": 1,
                    "max": 100,
                    "step": 1,
                    "label": "Height (Tablet)",
                    "default": 2
                },
                {
                    "type": "number",
                    "id": "mgb_tb",
                    "label": "Margin bottom on tablet(Unit: px)",
                    "default": 20
                }, 
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                    "type": "range",
                    "id": "width_mb",
                    "min": 1,
                    "max": 100,
                    "step": 1,
                    "label": "Width (Mobile)",
                    "unit": "px",
                    "default": 40
                },
                {
                    "type": "range",
                    "id": "height_mb",
                    "min": 1,
                    "max": 100,
                    "step": 1,
                    "label": "Height (Mobile)",
                    "default": 2
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile",
                    "default": false
                },
                {
                    "type": "number",
                    "id": "mgb_mb",
                    "label": "Margin bottom on mobile(Unit: px)",
                    "default": 20
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                    "type": "select",
                    "id": "animation",
                    "label": "Animation",
                    "default": "none",
                    "options": [
                        {
                            "label": "None",
                            "value": "none"
                        },
                        {
                            "label": "fadeIn",
                            "value": "fadeIn"
                        },
                        {
                            "label": "fadeInDown",
                            "value": "fadeInDown"
                        },
                        {
                            "label": "fadeInDownBig",
                            "value": "fadeInDownBig"
                        },
                        {
                            "label": "fadeInLeft",
                            "value": "fadeInLeft"
                        },
                        {
                            "label": "fadeInLeftBig",
                            "value": "fadeInLeftBig"
                        },
                        {
                            "label": "fadeInRight",
                            "value": "fadeInRight"
                        },
                        {
                            "label": "fadeInRightBig",
                            "value": "fadeInRightBig"
                        },
                        {
                            "label": "fadeInUp",
                            "value": "fadeInUp"
                        },
                        {
                            "label": "fadeInUpBig",
                            "value": "fadeInUpBig"
                        },
                        {
                            "label": "fadeInTopLeft",
                            "value": "fadeInTopLeft"
                        },
                        {
                            "label": "fadeInTopRight",
                            "value": "fadeInTopRight"
                        },
                        {
                            "label": "fadeInBottomLeft",
                            "value": "fadeInBottomLeft"
                        },
                        {
                            "label": "fadeInBottomRight",
                            "value": "fadeInBottomRight"
                        },
                        {
                            "label": "bounceIn",
                            "value": "bounceIn"
                        },
                        {
                            "label": "bounceInDown",
                            "value": "bounceInDown"
                        },
                        {
                            "label": "bounceInLeft",
                            "value": "bounceInLeft"
                        },
                        {
                            "label": "bounceInRight",
                            "value": "bounceInRight"
                        },
                        {
                            "label": "bounceInUp",
                            "value": "bounceInUp"
                        },
                        {
                            "label": "zoomIn",
                            "value": "zoomIn"
                        },
                        {
                            "label": "zoomInDown",
                            "value": "zoomInDown"
                        },
                        {
                            "label": "zoomInLeft",
                            "value": "zoomInLeft"
                        },
                        {
                            "label": "zoomInRight",
                            "value": "zoomInRight"
                        },
                        {
                            "label": "zoomInUp",
                            "value": "zoomInUp"
                        },
                        {
                            "label": "slideInDown",
                            "value": "slideInDown"
                        },
                        {
                            "label": "slideInLeft",
                            "value": "slideInLeft"
                        },
                        {
                            "label": "slideInRight",
                            "value": "slideInRight"
                        },
                        {
                            "label": "slideInUp",
                            "value": "slideInUp"
                        },
                        {
                            "label": "lightSpeedInRight",
                            "value": "lightSpeedInRight"
                        },
                        {
                            "label": "lightSpeedInLeft",
                            "value": "lightSpeedInLeft"
                        },
                        {
                            "label": "lightSpeedOutRight",
                            "value": "lightSpeedOutRight"
                        },
                        {
                            "label": "lightSpeedOutLeft",
                            "value": "lightSpeedOutLeft"
                        },
                        {
                            "label": "jello",
                            "value": "jello"
                        },
                        {
                            "label": "tada",
                            "value": "tada"
                        },
                        {
                            "label": "pulse",
                            "value": "pulse"
                        }
                    ]
                }
            ]
        },
        {
            "type": "custom_text_circle",
            "name": "Text circle",
            "settings": [
                {
                    "type": "select",
                    "id": "source",
                    "label": "Source icon",
                    "default": "themes_icon",
                    "options": [
                        {
                            "value": "themes_icon",
                            "label": "Themes icon"
                        },
                        {
                            "value": "get_image",
                            "label": "Use image"
                        },
                        {
                            "value": "line_awe",
                            "label": "Line awesome"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "icon_size",
                    "label": "Icon size",
                    "default": "medium",
                    "options": [
                    {
                        "label": "Small",
                        "value": "small"
                        },
                        {
                        "label": "Medium",
                        "value": "medium"
                        },
                        {
                        "label": "Large",
                        "value": "large"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "icon_themes",
                    "label": "Select icon", 
                    "info": "Only used for source  theme icon",
                    "default": "bee-flash",
                    "options": [
                        {
                        "value": "none",
                        "label": "None"
                        },
                        {
                        "value": "car",
                        "label": "Car"
                        },
                        {
                        "value": "diamond",
                        "label": "Diamond"
                        },
                        {
                        "value": "door-lock",
                        "label": "Door lock"
                        },
                        {
                        "value": "bee-flash",
                        "label": "Flash"
                        },
                        {
                        "value": "gym",
                        "label": "Gym"
                        },
                        {
                        "value": "hammer",
                        "label": "Hammer"
                        },
                        {
                        "value": "headphones",
                        "label": "Headphones"
                        },
                        {
                        "value": "helm",
                        "label": "Helm"
                        },
                        {
                        "value": "hourglass",
                        "label": "Hourglass"
                        },
                        {
                        "value": "map",
                        "label": "Map"
                        },
                        {
                        "value": "piggy",
                        "label": "Piggy"
                        },
                        {
                        "value": "refesh",
                        "label": "Refesh"
                        },
                        {
                        "value": "rocket",
                        "label": "Rocket"
                        },
                        {
                        "value": "bee-rocket",
                        "label": "Rocket 2"
                        },
                        {
                        "value": "shield",
                        "label": "Shield"
                        },
                        {
                        "value": "smile",
                        "label": "Smile"
                        },
                        {
                        "value": "cloud_upload",
                        "label": "Cloud upload"
                        },
                        {
                        "value": "cash",
                        "label": "Cash"
                        },
                        {
                        "value": "way",
                        "label": "Way"
                        },
                        {
                        "value": "wristwatch",
                        "label": "Wristwatch"
                        },
                        {
                        "value": "world",
                        "label": "World"
                        },
                        {
                        "value": "scissors",
                        "label": "Scissors"
                        },
                        {
                        "value": "wallet",
                        "label": "Wallet"
                        },
                        {
                        "value": "unlock",
                        "label": "Unlock"
                        },
                        {
                        "value": "umbrella",
                        "label": "Umbrella"
                        },
                        {
                        "value": "shuffle",
                        "label": "Shuffle"
                        },
                        {
                        "value": "repeat",
                        "label": "Repeat"
                        },
                        {
                        "value": "refesh-2",
                        "label": "Refesh 2"
                        },
                        {
                        "value": "medal",
                        "label": "Medal"
                        },
                        {
                        "value": "portfolio",
                        "label": "Portfolio"
                        },
                        {
                        "value": "like",
                        "label": "Like"
                        },
                        {
                        "value": "plance",
                        "label": "Plance"
                        },
                        {
                        "value": "map-maker",
                        "label": "Map maker"
                        },
                        {
                        "value": "help",
                        "label": "Help"
                        },
                        {
                        "value": "gift",
                        "label": "Gift"
                        },
                        {
                        "value": "cart",
                        "label": "Cart"
                        },
                        {
                        "value": "box",
                        "label": "Box"
                        },
                        {
                        "value": "back",
                        "label": "Back"
                        }
                    ]
                },
                {
                    "type": "image_picker",
                    "id": "image_icon",
                    "label": "Chooes image"
                },            
                {
                    "type": "text",
                    "id": "icon",
                    "label": "Enter icon",
                    "info": "Only used for source line awesome icon",
                    "default": "las la-headset"
                },
                {
                    "type": "paragraph",
                    "content": "[LineAwesome](https://kalles.the4.co/font-lineawesome/)"
                },
                {
                    "type": "checkbox",
                    "id": "use_rds",
                    "label": "Use image circle",
                    "default": false
                },
                {
                    "type": "textarea",
                    "id": "text",
                    "label": "Input text",
                    "default": "Text"
                },
                {
                    "type": "range",
                    "id": "time_tx_pin",
                    "label": "Time text pin",
                    "max": 100,
                    "min": 3,
                    "step": 1,
                    "unit": "s",
                    "default": 18
                },
                {
                    "type": "header",
                    "content": "+ Box text design"
                },
                {
                    "type": "select",
                    "id": "fontf",
                    "default": "inherit",
                    "label": "Font family",
                    "options": [
                        {
                            "label": "Inherit",
                            "value": "inherit"
                        },
                        {
                            "label": "Font family #1",
                            "value": "1"
                        },
                        {
                            "label": "Font family #2",
                            "value": "2"
                        },
                        {
                            "label": "Font family #3",
                            "value": "3"
                        }
                    ]
                },  
                {
                    "type": "color",
                    "id": "text_cl",
                    "label": "Color text",
                    "default": "#000"
                },
                {
                    "type": "range",
                    "id": "box_text_width",
                    "label": "Box text width",
                    "max": 500,
                    "min": 100,
                    "step": 5,
                    "unit": "px",
                    "default": 200
                },     
                {
                    "type": "range",
                    "id": "text_fs",
                    "label": "Font size",
                    "max": 30,
                    "min": 1,
                    "step": 1,
                    "unit": "%",
                    "default": 6
                },
                {
                    "type": "range",
                    "id": "text_fw",
                    "label": "Font weight",
                    "min": 100,
                    "max": 900,
                    "step": 100,
                    "default": 500
                },
                {
                    "type": "range",
                    "id": "text_ls",
                    "label": "Letter spacing",
                    "max": 10,
                    "min": 0,
                    "default": 2.1,
                    "step": 0.1,
                    "unit": "px"
                },
                {
                    "type": "number",
                    "id": "mgb",
                    "label": "Margin bottom (Unit: px)",
                    "default": 15
                },
                {
                    "type": "header",
                    "content": "+ Options on tablet"
                },
                {
                    "type": "range",
                    "id": "box_text_width_tb",
                    "label": "Box text width",
                    "max": 500,
                    "min": 100,
                    "step": 5,
                    "unit": "px",
                    "default": 200
                },
                {
                    "type": "range",
                    "id": "text_fs_tb",
                    "label": "Font size",
                    "max": 20,
                    "min": 1,
                    "step": 1,
                    "unit": "%",
                    "default": 6
                },
                {
                    "type": "range",
                    "id": "text_ls_tb",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 2.1
                },
                {
                    "type": "number",
                    "id": "mgb_tb",
                    "label": "Margin bottom(Unit: px)",
                    "default": 10
                },
                {
                    "type": "header",
                    "content": "+ Options on mobile"
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile ",
                    "default": false
                },
                {
                    "type": "range",
                    "id": "box_text_width_mb",
                    "label": "Box text width",
                    "max": 500,
                    "min": 100,
                    "step": 5,
                    "unit": "px",
                    "default": 150
                },
                {
                    "type": "range",
                    "id": "text_fs_mb",
                    "label": "Font size",
                    "max": 20,
                    "min": 1,
                    "step": 1,
                    "unit": "%",
                    "default": 6
                },
                {
                    "type": "range",
                    "id": "text_ls_mb",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 2.1
                },
                {
                    "type": "number",
                    "id": "mgb_mb",
                    "label": "Margin bottom(Unit: px)",
                    "default": 10
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                    "type": "select",
                    "id": "animation",
                    "label": "Animation",
                    "default": "none",
                    "options": [
                        {
                            "label": "None",
                            "value": "none"
                        },
                        {
                            "label": "fadeIn",
                            "value": "fadeIn"
                        },
                        {
                            "label": "fadeInDown",
                            "value": "fadeInDown"
                        },
                        {
                            "label": "fadeInDownBig",
                            "value": "fadeInDownBig"
                        },
                        {
                            "label": "fadeInLeft",
                            "value": "fadeInLeft"
                        },
                        {
                            "label": "fadeInLeftBig",
                            "value": "fadeInLeftBig"
                        },
                        {
                            "label": "fadeInRight",
                            "value": "fadeInRight"
                        },
                        {
                            "label": "fadeInRightBig",
                            "value": "fadeInRightBig"
                        },
                        {
                            "label": "fadeInUp",
                            "value": "fadeInUp"
                        },
                        {
                            "label": "fadeInUpBig",
                            "value": "fadeInUpBig"
                        },
                        {
                            "label": "fadeInTopLeft",
                            "value": "fadeInTopLeft"
                        },
                        {
                            "label": "fadeInTopRight",
                            "value": "fadeInTopRight"
                        },
                        {
                            "label": "fadeInBottomLeft",
                            "value": "fadeInBottomLeft"
                        },
                        {
                            "label": "fadeInBottomRight",
                            "value": "fadeInBottomRight"
                        },
                        {
                            "label": "bounceIn",
                            "value": "bounceIn"
                        },
                        {
                            "label": "bounceInDown",
                            "value": "bounceInDown"
                        },
                        {
                            "label": "bounceInLeft",
                            "value": "bounceInLeft"
                        },
                        {
                            "label": "bounceInRight",
                            "value": "bounceInRight"
                        },
                        {
                            "label": "bounceInUp",
                            "value": "bounceInUp"
                        },
                        {
                            "label": "zoomIn",
                            "value": "zoomIn"
                        },
                        {
                            "label": "zoomInDown",
                            "value": "zoomInDown"
                        },
                        {
                            "label": "zoomInLeft",
                            "value": "zoomInLeft"
                        },
                        {
                            "label": "zoomInRight",
                            "value": "zoomInRight"
                        },
                        {
                            "label": "zoomInUp",
                            "value": "zoomInUp"
                        },
                        {
                            "label": "slideInDown",
                            "value": "slideInDown"
                        },
                        {
                            "label": "slideInLeft",
                            "value": "slideInLeft"
                        },
                        {
                            "label": "slideInRight",
                            "value": "slideInRight"
                        },
                        {
                            "label": "slideInUp",
                            "value": "slideInUp"
                        },
                        {
                            "label": "lightSpeedInRight",
                            "value": "lightSpeedInRight"
                        },
                        {
                            "label": "lightSpeedInLeft",
                            "value": "lightSpeedInLeft"
                        },
                        {
                            "label": "lightSpeedOutRight",
                            "value": "lightSpeedOutRight"
                        },
                        {
                            "label": "lightSpeedOutLeft",
                            "value": "lightSpeedOutLeft"
                        },
                        {
                            "label": "jello",
                            "value": "jello"
                        },
                        {
                            "label": "tada",
                            "value": "tada"
                        },
                        {
                            "label": "pulse",
                            "value": "pulse"
                        }
                    ]
                }
            ]
        }
    ],
    "presets": [
        {
            "name": "Video popup & inline",
            "category": "homepage",
            "blocks": [
                {
                    "type": "button_video"
                },
                {
                    "type": "custom_text",
					"settings": {
						"text": "Theme Nitro",
						"text_fs": 16,
						"text_fw": 400,
						"text_ls": 1
					}
                },
                {
                    "type": "custom_text",
					"settings": {
						"text": "Best shopify theme",
						"text_fs": 70,
						"text_fw": 300,
						"text_lh": 70,
						"mgb": 25
					}
                },
                {
                    "type": "custom_button",
					"settings": {
						"fsbutton": 14,
						"button_mh": 42,
						"button_pd_lr": 20
					}
                }
            ]
        }
    ]
}
{% endschema %}