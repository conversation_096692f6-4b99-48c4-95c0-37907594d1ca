<!-- sections/product-recommendation.liquid -->
{%- for i in (1..1) %}{% unless section.settings.show %}{% break %}{% endunless -%}

{%- capture file_style -%}
{{ 'bee-section.css' | asset_url | stylesheet_tag }}
{{ 'bee-collection-products.css' | asset_url | stylesheet_tag }}
{{ 'bee-slider-settings.css' | asset_url | stylesheet_tag }}
{{ 'pre_flickitybee.min.css' | asset_url | stylesheet_tag }}
<link href="{{ 'bee-loading.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%- endcapture -%}

{%- liquid
  assign sid = section.id
  assign se_stts = section.settings
  assign se_blocks = section.blocks
  assign stt_layout = se_stts.layout
  assign stt_image_bg = se_stts.image_bg
  if stt_layout == 'bee-se-container' 
    assign html_layout = '<div class="bee-container">__</div></div>' | split:'__'
  elsif stt_layout == 'bee-container-wrap'
    assign html_layout = '<div class="bee-container">__</div>' | split:'__'
  else
    assign html_layout = '__' | split:'__'
  endif
  assign layout_des = se_stts.layout_des
  assign image_ratio = se_stts.image_ratio
  if image_ratio == "ratioadapt"
    assign imgatt = ''
   else 
    assign imgatt = 'data-'
  endif
  assign show_vendor = se_stts.show_vendor
  assign use_link_vendor = settings.use_link_vendor
  assign enable_rating = settings.enable_rating
  assign limit = se_stts.limit
  assign product_des = se_stts.product_des
  assign placeholder_img = settings.placeholder_img
  if se_stts.btn_owl == "simple"
    assign arrow_icon = 1
  else
    assign arrow_icon = 2
  endif

  assign show_img = settings.show_img
  assign isGrowaveWishlist = false
  if settings.wishlist_mode == "3" and shop.customer_accounts_enabled
    assign isGrowaveWishlist = true
  endif
  assign enable_pr_size = settings.enable_pr_size
  assign pr_size_pos = settings.pr_size_pos
  assign show_size_type = settings.show_size_type
  assign size_ck = settings.size_ck | append:',size,sizes,Größe' 
  assign get_size = size_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

  assign enable_pr_color = settings.enable_pr_color
  assign show_cl_type = settings.show_color_type
  assign color_ck = settings.color_ck | append:',color,colors,couleur,colour'
  assign get_color = color_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

  assign price_varies_style = settings.price_varies_style
  assign app_review = settings.app_review
  assign use_countdown = se_stts.use_cdt

  assign display_type   = se_stts.display_type
  assign search_url     = routes.search_url
  assign search_types   = search.types
  assign search_terms   = search.terms

  if request.page_type == 'search' and display_type == '1' or display_type == '2'
    assign arr_terms  = search_terms | split:' '
    assign pid = arr_terms.first | plus: 0
    if display_type == '1'
       assign collection = collections[arr_terms.last]
       assign results_count = collection.products.size | minus: 1
    else 
       assign pr_link   = linklists[arr_terms.last].links | where: "type", "product_link"
       assign results_count = pr_link.size
    endif
  endif

  assign show_btn = se_stts.show_btn
  if show_btn != "hidden"
    assign slider_btns = true
  else
    assign slider_btns = false
  endif
  assign show_dots = se_stts.show_dots
  if show_dots != "hidden"
    assign slider_dots = true
  else
    assign slider_dots = false
  endif
  assign nav_btn_icon = se_stts.nav_btn_icon
  -%}

   {%- if display_type == "3" and recommendations.products_count > 0 -%}
      <div class="bee-section-inner bee_nt_se_{{sid}} bee_se_{{sid}} {{stt_layout}} {% if stt_image_bg != blank and stt_layout != 'bee-se-container' %} bee-has-imgbg lazyloadbee {% endif %}"  {% if stt_image_bg != blank and stt_layout != 'bee-se-container' %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} {% render 'section_style',se_stts:se_stts %} >
            {{-html_layout[0]-}}
              {%- if stt_layout == 'bee-se-container' -%}<div class="bee-container-inner {% if stt_image_bg != blank %} bee-has-imgbg lazyloadbee {% endif %} "  {% if stt_image_bg != blank %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} > {%- endif -%}
              {%- render 'section_tophead',se_stts:se_stts -%}
              
            {%- if layout_des == "1" -%} 
              {{ 'bee-button-style.css' | asset_url | stylesheet_tag }}
              <link href="{{ 'bee-custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
              <div data-contentlm-replace class="bee_box_pr_grid bee-products bee-justify-content-center bee-text-{{se_stts.content_align}} bee_{{image_ratio}} bee_position_{{se_stts.image_position}} bee_{{se_stts.image_size}} bee-row bee-row-cols-lg-{{se_stts.col_dk}} bee-row-cols-md-{{se_stts.col_tb}} bee-row-cols-{{se_stts.col_mb}} bee-gx-md-{{se_stts.space_h_item}} bee-gy-md-{{se_stts.space_v_item}} bee-gx-{{se_stts.space_h_item_mb}} bee-gy-{{se_stts.space_v_item_mb}}">
            {%- else -%} 
              {%- liquid
            assign icon_slider = se_stts.icon_slider 
            if icon_slider == "1"
              assign view_box = "0 0 22 22"
            endif -%}
              <div data-bee-resizeobserver class="bee-flickity-slider bee_box_pr_slider bee-products bee-text-{{se_stts.content_align}} bee_{{image_ratio}} bee_position_{{se_stts.image_position}} bee_{{se_stts.image_size}} {% if slider_btns %}bee-slider-btn-{{ show_btn }} bee-slider-btn__{{ se_stts.btn_pos }}-content bee-slider-btn-style-{{ se_stts.btn_owl }} bee-slider-btn-{{ se_stts.btn_shape }} bee-slider-btn-{{ se_stts.btn_size }} bee-slider-btn-cl-{{ se_stts.btn_cl }} bee-slider-btn-vi-{{ se_stts.btn_vi }} {% endif %}{% if slider_dots %} bee-slider-dots-{{ show_dots }} bee-dots-style-{{ se_stts.dot_owl }} bee-dots-cl-{{ se_stts.dots_cl }} bee-dots-round-{{ se_stts.dots_round }} bee-dots-align-{{ se_stts.dots_align }}{% endif %} bee-row bee-row-cols-lg-{{se_stts.col_dk}} bee-row-cols-md-{{se_stts.col_tb}} bee-row-cols-{{se_stts.col_mb}} bee-gx-lg-{{ se_stts.space_h_item }} bee-gy-lg-{{ se_stts.space_v_item }} bee-gx-md-{{ se_stts.space_h_item_tb }} bee-gy-md-{{ se_stts.space_v_item_tb }} bee-gx-{{ se_stts.space_h_item_mb }} bee-gy-{{ se_stts.space_v_item_mb }} flickitybee flickitybee-later" data-flickitybee-js='{"customIcon":{{icon_slider}}, "viewBox":"{{view_box}}", "setPrevNextButtons": true,"arrowIcon": "{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{ se_stts.loop }},"prevNextButtons": {{ slider_btns }},"percentPosition": 1,"pageDots": {{ slider_dots }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }}, "pauseAutoPlayOnHover" : {{ se_stts.au_hover }} }' style="--btn-distance: {{ se_stts.btn_distance }}px;--space-dots: {{ se_stts.dots_space }}px;--dots-bottom-pos: 20px;">
            {%- endif -%}
          {%- liquid 
          case product_des
            when '1'
              for product in recommendations.products
                render 'pr-grid-item1' product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
              endfor
            when '2'
              for product in recommendations.products
                render 'pr-grid-item2' product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
              endfor
            when '3'
              for product in recommendations.products
                render 'pr-grid-item3' product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
              endfor
            when '4'
              for product in recommendations.products
                render 'pr-grid-item4' product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
              endfor
            when '5'
              for product in recommendations.products
                render 'pr-grid-item5' product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
              endfor
            when '6'
              for product in recommendations.products
                render 'pr-grid-item6' product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
              endfor
            when '7'
              for product in recommendations.products
                render 'pr-grid-item7' product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
              endfor
            endcase -%}
         </div>
        {{-html_layout[1]-}}
      </div>

   {%- elsif display_type == "2" and search_types contains 'page' -%}
      
      {%- liquid
      if results_count == 0
        break
      endif
      assign index = 0
      -%}

      <div class="bee-section-inner bee_nt_se_{{sid}} bee_se_{{sid}} {{stt_layout}} {% if stt_image_bg != blank and stt_layout != 'bee-se-container' %} bee-has-imgbg lazyloadbee {% endif %}"  {% if stt_image_bg != blank and stt_layout != 'bee-se-container' %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} {% render 'section_style',se_stts:se_stts %} >
            {{-html_layout[0]-}}
              {%- if stt_layout == 'bee-se-container' -%}<div class="bee-container-inner {% if stt_image_bg != blank %} bee-has-imgbg lazyloadbee {% endif %} "  {% if stt_image_bg != blank %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} > {%- endif -%}
              {%- render 'section_tophead',se_stts:se_stts -%}
              
            {%- if layout_des == "1" -%} 
              {{ 'bee-button-style.css' | asset_url | stylesheet_tag }}
              <link href="{{ 'bee-custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
              <div data-contentlm-replace class="bee_box_pr_grid bee-products bee-justify-content-center bee-text-{{se_stts.content_align}} bee_{{image_ratio}} bee_position_{{se_stts.image_position}} bee_{{se_stts.image_size}} bee-row bee-row-cols-lg-{{se_stts.col_dk}} bee-row-cols-md-{{se_stts.col_tb}} bee-row-cols-{{se_stts.col_mb}} bee-gx-md-{{se_stts.space_h_item}} bee-gy-md-{{se_stts.space_v_item}} bee-gx-{{se_stts.space_h_item_mb}} bee-gy-{{se_stts.space_v_item_mb}}">
            {%- else -%} 
            {%- liquid
              assign icon_slider = se_stts.icon_slider 
              if icon_slider == "1"
                assign view_box = "0 0 22 22"
              endif -%}
              <div data-bee-resizeobserver class="bee-flickity-slider bee_box_pr_slider bee-products bee-text-{{se_stts.content_align}} bee_{{image_ratio}} bee_position_{{se_stts.image_position}} bee_{{se_stts.image_size}} {% if slider_btns %}bee-slider-btn-{{ show_btn }} bee-slider-btn__{{ se_stts.btn_pos }}-content bee-slider-btn-style-{{ se_stts.btn_owl }} bee-slider-btn-{{ se_stts.btn_shape }} bee-slider-btn-{{ se_stts.btn_size }} bee-slider-btn-cl-{{ se_stts.btn_cl }} bee-slider-btn-vi-{{ se_stts.btn_vi }} {% endif %}{% if slider_dots %} bee-slider-dots-{{ show_dots }} bee-dots-style-{{ se_stts.dot_owl }} bee-dots-cl-{{ se_stts.dots_cl }} bee-dots-round-{{ se_stts.dots_round }} bee-dots-align-{{ se_stts.dots_align }}{% endif %} bee-row bee-row-cols-lg-{{se_stts.col_dk}} bee-row-cols-md-{{se_stts.col_tb}} bee-row-cols-{{se_stts.col_mb}} bee-gx-md-{{se_stts.space_h_item}} bee-gy-md-{{se_stts.space_v_item}} bee-gx-{{se_stts.space_h_item_mb}} bee-gy-{{se_stts.space_v_item_mb}} flickitybee flickitybee-later" data-flickitybee-js='{customIcon":{{ icon_slider }}, "viewBox":"{{ view_box }}", "setPrevNextButtons":true,"arrowIcon":"{{arrow_icon}}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{se_stts.loop}},"prevNextButtons": {{slider_btns}},"percentPosition": 1,"pageDots": {{ slider_dots }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }}, "pauseAutoPlayOnHover" : {{se_stts.au_hover}} }' style="--btn-distance: {{ se_stts.btn_distance }}px;--space-dots: {{ se_stts.dots_space }}px;">
            {%- endif -%}
          
          {%- for link in pr_link -%}
             {%- if pid == link.object.id -%}{%- continue -%}{%- endif -%}
             {%- if index == limit -%}{%- break -%}{%- endif -%}
             {%- assign index = index | plus:1 -%}
             {%- case product_des -%}
                {%- when '1' -%}
                {%- render 'pr-grid-item1',product: link.object, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '2' -%}
                {%- render 'pr-grid-item2',product: link.object, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '3' -%}
                {%- render 'pr-grid-item3',product: link.object, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '4' -%}
                {%- render 'pr-grid-item4',product: link.object, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '5' -%}
                {%- render 'pr-grid-item5',product: link.object, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '6' -%}
                {%- render 'pr-grid-item6',product: link.object, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '7' -%}
                {%- render 'pr-grid-item7',product: link.object, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
              {%- endcase -%}
          {%- endfor -%}

         </div>
        {{-html_layout[1]-}}
      </div>

   {%- elsif display_type == "1" and collection and search_types contains 'page' -%}
      
      {%- liquid
      if results_count == 0
        break
      endif
      -%}

      <div class="bee-section-inner bee_nt_se_{{ sid }} bee_se_{{ sid }} {{ stt_layout }} {% if stt_image_bg != blank and stt_layout != 'bee-se-container' %} bee-has-imgbg lazyloadbee {% endif %}"  {% if stt_image_bg != blank and stt_layout != 'bee-se-container' %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} {% render 'section_style',se_stts:se_stts %} >
            {{-html_layout[0]-}}
              {%- if stt_layout == 'bee-se-container' -%}<div class="bee-container-inner {% if stt_image_bg != blank %} bee-has-imgbg lazyloadbee {% endif %} "  {% if stt_image_bg != blank %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} > {%- endif -%}
              {%- render 'section_tophead',se_stts:se_stts -%}
              
            {%- if layout_des == "1" -%} 
              {{ 'bee-button-style.css' | asset_url | stylesheet_tag }}
              <link href="{{ 'bee-custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
              <div data-collection-url="{{ collection.url }}" data-contentlm-replace class="bee_box_pr_grid bee-products bee-justify-content-center bee-text-{{ se_stts.content_align }} bee_{{image_ratio}} bee_position_{{se_stts.image_position}} bee_{{se_stts.image_size}} bee-row bee-row-cols-lg-{{se_stts.col_dk}} bee-row-cols-md-{{se_stts.col_tb}} bee-row-cols-{{se_stts.col_mb}} bee-gx-md-{{se_stts.space_h_item}} bee-gy-md-{{se_stts.space_v_item}} bee-gx-{{se_stts.space_h_item_mb}} bee-gy-{{se_stts.space_v_item_mb}}">
            {%- else -%} 
              <div data-collection-url="{{ collection.url }}" data-bee-resizeobserver class="bee-flickity-slider bee_box_pr_slider bee-products bee-text-{{ se_stts.content_align }} bee_{{image_ratio}} bee_position_{{se_stts.image_position}} bee_{{se_stts.image_size}} {% if slider_btns %}bee-slider-btn-{{ show_btn }} bee-slider-btn__{{ se_stts.btn_pos }}-content bee-slider-btn-style-{{ se_stts.btn_owl }} bee-slider-btn-{{ se_stts.btn_shape }} bee-slider-btn-{{ se_stts.btn_size }} bee-slider-btn-cl-{{ se_stts.btn_cl }} bee-slider-btn-vi-{{ se_stts.btn_vi }} {% endif %}{% if slider_dots %} bee-slider-dots-{{ show_dots }} bee-dots-style-{{ se_stts.dot_owl }} bee-dots-cl-{{ se_stts.dots_cl }} bee-dots-round-{{ se_stts.dots_round }} bee-dots-align-{{ se_stts.dots_align }}{% endif %} bee-row bee-row-cols-lg-{{se_stts.col_dk}} bee-row-cols-md-{{se_stts.col_tb}} bee-row-cols-{{se_stts.col_mb}} bee-gx-md-{{se_stts.space_h_item}} bee-gy-md-{{se_stts.space_v_item}} bee-gx-{{se_stts.space_h_item_mb}} bee-gy-{{se_stts.space_v_item_mb}} flickitybee flickitybee-later" data-flickitybee-js='{customIcon":{{ icon_slider }}, "viewBox":"{{ view_box }}", "setPrevNextButtons":true,"arrowIcon":"{{arrow_icon}}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{se_stts.loop}},"prevNextButtons": {{slider_btns}},"percentPosition": 1,"pageDots": {{ slider_dots }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }}, "pauseAutoPlayOnHover" : {{se_stts.au_hover}} }' style="--btn-distance: {{ se_stts.btn_distance }}px;--space-dots: {{ se_stts.dots_space }}px;">
            {%- endif -%}
          
          {%- for product in collection.products -%}
            {%- if pid == product.id -%}{%- continue -%}{%- endif -%}
            {%- if index == limit -%}{%- break -%}{%- endif -%}
            {%- assign index = index | plus:1 -%}
             {%- case product_des -%}
                {%- when '1' -%}
                {%- render 'pr-grid-item1',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '2' -%}
                {%- render 'pr-grid-item2',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '3' -%}
                {%- render 'pr-grid-item3',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '4' -%}
                {%- render 'pr-grid-item4',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '5' -%}
                {%- render 'pr-grid-item5',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '6' -%}
                {%- render 'pr-grid-item6',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '7' -%}
                {%- render 'pr-grid-item7',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
              {%- endcase -%}
          {%- endfor -%}

         </div>
        {{-html_layout[1]-}}
      </div>

   {%- elsif search.results.size > 0 and request.page_type == 'search' -%}
      {%- comment -%} display_type 4, 5 {%- endcomment -%}

      <div class="bee-section-inner bee_nt_se_{{sid}} bee_se_{{sid}} {{stt_layout}} {% if stt_image_bg != blank and stt_layout != 'bee-se-container' %} bee-has-imgbg lazyloadbee {% endif %}"  {% if stt_image_bg != blank and stt_layout != 'bee-se-container' %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} {% render 'section_style',se_stts:se_stts %} >
            {{-html_layout[0]-}}
              {%- if stt_layout == 'bee-se-container' -%}<div class="bee-container-inner {% if stt_image_bg != blank %} bee-has-imgbg lazyloadbee {% endif %} "  {% if stt_image_bg != blank %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="2" {% endif %} > {%- endif -%}
              {%- render 'section_tophead',se_stts:se_stts -%}
              
            {%- if layout_des == "1" -%} 
              {{ 'bee-button-style.css' | asset_url | stylesheet_tag }}
              <link href="{{ 'bee-custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
              <div data-contentlm-replace class="bee_box_pr_grid bee-products bee-justify-content-center bee-text-{{se_stts.content_align}} bee_{{image_ratio}} bee_position_{{se_stts.image_position}} bee_{{se_stts.image_size}} bee-row bee-row-cols-lg-{{se_stts.col_dk}} bee-row-cols-md-{{se_stts.col_tb}} bee-row-cols-{{se_stts.col_mb}} bee-gx-md-{{se_stts.space_h_item}} bee-gy-md-{{se_stts.space_v_item}} bee-gx-{{se_stts.space_h_item_mb}} bee-gy-{{se_stts.space_v_item_mb}}">
            {%- else -%} 
              <div data-bee-resizeobserver class="bee-flickity-slider bee_box_pr_slider bee-products bee-text-{{se_stts.content_align}} bee_{{image_ratio}} bee_position_{{se_stts.image_position}} bee_{{se_stts.image_size}} {% if slider_btns %}bee-slider-btn-{{ show_btn }} bee-slider-btn__{{ se_stts.btn_pos }}-content bee-slider-btn-style-{{ se_stts.btn_owl }} bee-slider-btn-{{ se_stts.btn_shape }} bee-slider-btn-{{ se_stts.btn_size }} bee-slider-btn-cl-{{ se_stts.btn_cl }} bee-slider-btn-vi-{{ se_stts.btn_vi }} {% endif %}{% if slider_dots %} bee-slider-dots-{{ show_dots }} bee-dots-style-{{ se_stts.dot_owl }} bee-dots-cl-{{ se_stts.dots_cl }} bee-dots-round-{{ se_stts.dots_round }} bee-dots-align-{{ se_stts.dots_align }}{% endif %} bee-row bee-row-cols-lg-{{se_stts.col_dk}} bee-row-cols-md-{{se_stts.col_tb}} bee-row-cols-{{se_stts.col_mb}} bee-gx-md-{{se_stts.space_h_item}} bee-gy-md-{{se_stts.space_v_item}} bee-gx-{{se_stts.space_h_item_mb}} bee-gy-{{se_stts.space_v_item_mb}} flickitybee flickitybee-later" data-flickitybee-js='{"customIcon":{{icon_slider}}, "viewBox":"{{view_box}}", "setPrevNextButtons":true,"arrowIcon":"{{arrow_icon}}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": {{se_stts.loop}},"prevNextButtons": {{slider_btns}},"percentPosition": 1,"pageDots": {{ slider_dots }}, "autoPlay" : {{ se_stts.au_time | times: 1000 }}, "pauseAutoPlayOnHover" : {{se_stts.au_hover}} }' style="--btn-distance: {{ se_stts.btn_distance }}px;--space-dots: {{ se_stts.dots_space }}px;">
            {%- endif -%}
          {%- for product in search.results limit:limit -%}
            {%- case product_des -%}
                {%- when '1' -%}
                {%- render 'pr-grid-item1',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '2' -%}
                {%- render 'pr-grid-item2',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '3' -%}
                {%- render 'pr-grid-item3',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '4' -%}
                {%- render 'pr-grid-item4',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '5' -%}
                {%- render 'pr-grid-item5',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '6' -%}
                {%- render 'pr-grid-item6',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
                {%- when '7' -%}
                {%- render 'pr-grid-item7',product:product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false -%}
            {%- endcase -%}
          {%- endfor -%}
         </div>
        {{-html_layout[1]-}}
      </div>

   {%- elsif search.performed and search.results_count == 0 -%}
        <!-- no product -->
   {%- elsif display_type == "3" and recommendations.performed == false -%}
   {{- file_style -}}<div class="bee-product-extra" data-sid="{{ section.id }}" data-baseurl="{{ routes.product_recommendations_url }}" id="pr_recommendations" data-id="{{ product.id }}" data-limit="{{ limit}}" data-type='{{ display_type }}' data-expands="-1"><div class="bee-loading--bg"></div></div>
   {%- elsif display_type == "2" -%}

     {%- liquid
        assign rid            = 'relatedbee_' | append: product.id
        assign relatedbee__tag = product.tags | where: 'relatedbee_' | first | default: rid | remove: 'relatedbee_'
        assign pr_link        = linklists[relatedbee__tag].links | where: "type", "product_link" 
      -%}
      {%- if pr_link.size > 0 %}{{- file_style -}}<div class="bee-product-extra" data-sid="{{section.id}}" data-baseurl="/?type=page&q={{ product.id }}+{{ relatedbee__tag }}" id="pr_recommendations" data-id="{{product.id}}" data-limit="{{limit}}" data-type='{{display_type}}' data-expands="-1"><div class="bee-loading--bg"></div></div>{% endif -%}
   
   {%- elsif display_type == "1" and collection -%}
   {{- file_style -}}<div class="bee-product-extra" data-sid="{{ section.id }}" data-baseurl="/?type=page&q={{ product.id }}+{{ collection.handle }}" id="pr_recommendations" data-id="{{ product.id }}" data-limit="{{ limit }}" data-type='{{ display_type }}' data-expands="-1"><div class="bee-loading--bg"></div></div>
   {%- elsif display_type == "4" and product.vendor != blank -%}
   {{- file_style -}}<div class="bee-product-extra" data-sid="{{ section.id }}" data-baseurl="/?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=none&q=vendor%3A{{ product.vendor | url_encode }}%20-id%3A{{ product.id }}" id="pr_recommendations" data-type='{{ display_type }}' data-expands="-1"><div class="bee-loading--bg"></div></div>
   {%- elsif display_type == "5" and product.type != blank -%}
  {{- file_style -}} <div class="bee-product-extra" data-sid="{{ section.id }}" data-baseurl="/?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=none&q=product_type%3A{{ product.type | url_encode }}%20-id%3A{{ product.id }}" id="pr_recommendations" data-type='{{ display_type }}' data-expands="-1"><div class="bee-loading--bg"></div></div>
   {%- endif -%}
{%- endfor -%}

{%- schema -%}
{
  "name": "Product recommendations",
  "class": "bee-section id_product-recommendations",
  "settings": [
    {
      "type": "checkbox",
      "id": "show",
      "label": "Show product recommendations",
      "default": true
    },
    {
        "type": "header",
        "content": "1. Heading options"
    },
    {
        "type": "checkbox",
        "id": "heading_center",
        "label": "Center heading align",
        "default": true
    },
    {
        "type": "text",
        "id": "top_heading",
        "label": "Heading",
        "default": "You may also like"
    },
    {
        "type": "richtext",
        "id": "top_subheading",
        "label": "Subheading"
    },
    {
        "type": "number",
        "id": "tophead_mb",
        "label": "Bottom space (Desktop)(px)",
        "info": "The spacing is between the heading and the content",
        "default": 30
    },
    {
        "type": "number",
        "id": "tophead_mb_tb",
        "label": "Bottom space (Tablet)(px)",
        "info": "The spacing is between the heading and the content",
        "default": 30
    },
    {
        "type": "number",
        "id": "tophead_mb_mb",
        "label": "Bottom space (Mobile)(px)",
        "info": "The spacing is between the heading and the content",
        "default": 25
    },
    {
      "type": "header",
      "content": "2. General options"
    }, 
    {
      "type": "select",
      "id": "product_des",
      "options": [
        {
          "value": "1",
          "label": "Design 1"
        },
        {
          "value": "2",
          "label": "Design 2"
        },
        {
          "value": "3",
          "label": "Design 3"
        },
        {
          "value": "4",
          "label": "Design 4"
        },
        {
          "value": "5",
          "label": "Design 5"
        },
        {
          "value": "6",
          "label": "Design 6"
        },
        {
          "value": "7",
          "label": "Design 7"
        }
      ],
      "label": "Product item design",
      "default": "1"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "label": "Show product vendors",
      "default": false
    },
      {
        "type": "checkbox",
        "id": "use_cdt",
        "label": "Show product countdown",
        "default": false
      },
    {
      "type": "range",
      "id": "limit",
      "min": 1,
      "max": 50,
      "step": 1,
      "label": "Maximum products to show",
      "default": 8
    },
    {
     "type": "select",
     "id": "display_type",
     "default": "1",
     "options": [
       {
         "value": "1",
         "label": "By Collection"
       },
       {
         "value": "2",
         "label": "By Linklist"
       },
       {
         "value": "3",
         "label": "Dynamic recommendations"
       },
       {
         "value": "4",
         "label": "By Product Vendor"
       },
       {
         "value": "5",
         "label": "By Product Type"
       }
     ],
     "info": "Dynamic recommendations change and improve with time. [Learn more](https://shopify.dev/tutorials/develop-theme-recommended-products)",
     "label": "Select type to show"
    },
    {
        "type": "header",
        "content": "+ Options image products"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Image ratio",
        "default": "rationt",
        "info": "Aspect ratio custom will settings in general panel",
        "options": [
          {
            "group": "Natural",
            "value": "ratioadapt",
            "label": "Adapt to image"
          },
          {
            "group": "Landscape",
            "value": "ratio2_1",
            "label": "2:1"
          },
          {
            "group": "Landscape",
            "value": "ratio16_9",
            "label": "16:9"
          },
          {
            "group": "Landscape",
            "value": "ratio8_5",
            "label": "8:5"
          },
          {
            "group": "Landscape",
            "value": "ratio3_2",
            "label": "3:2"
          },
          {
            "group": "Landscape",
            "value": "ratio4_3",
            "label": "4:3"
          },
          {
            "group": "Landscape",
            "value": "rationt",
            "label": "Ratio ASOS"
          },
          {
            "group": "Squared",
            "value": "ratio1_1",
            "label": "1:1"
          },
          {
            "group": "Portrait",
            "value": "ratio2_3",
            "label": "2:3"
          },
          {
            "group": "Portrait",
            "value": "ratio1_2",
            "label": "1:2"
          },
          {
            "group": "Custom",
            "value": "ratiocus1",
            "label": "Ratio custom 1"
          },
          {
            "group": "Custom",
            "value": "ratiocus2",
            "label": "Ratio custom 2"
          },
          {
            "group": "Custom",
            "value": "ratiocus3",
            "label": "Ratio custom 3"
          },
          {
            "group": "Custom",
            "value": "ratiocus4",
            "label": "Ratio custom 4"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Image size",
        "default": "cover",
        "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "label": "Image position",
        "default": "8"
      },
      {
        "type": "select",
        "id": "content_align",
        "label": "Product content align",
        "default": "default",
        "options": [
          {
            "label": "Default",
            "value": "default"
          },
          {
            "label": "Center",
            "value": "center"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_dk",
        "label": "Items per row",
        "default": "4",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          },
          {
            "value": "5",
            "label": "5"
          },
          {
            "value": "6",
            "label": "6"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_tb",
        "label": "Items per row (Tablet)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          },
          {
            "value": "3",
            "label": "3"
          },
          {
            "value": "4",
            "label": "4"
          }
        ]
      },
      {
        "type": "select",
        "id": "col_mb",
        "label": "Items per row (Mobile)",
        "default": "2",
        "options": [
          {
            "value": "1",
            "label": "1"
          },
          {
            "value": "2",
            "label": "2"
          }
        ]
      },
      {
        "type": "select",
        "id": "space_h_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
            "value": "15",
            "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_v_item",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
            "value": "15",
            "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items",
        "default": "30"
      },
      {
        "type": "select",
        "id": "space_h_item_tb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
            "value": "15",
            "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items (Tablet)",
        "default": "10"
      },
      {
        "type": "select",
        "id": "space_v_item_tb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
            "value": "15",
            "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items (Tablet)",
        "default": "10"
      },
      {
        "type": "select",
        "id": "space_h_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
            "value": "15",
            "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space horizontal items (Mobile)",
        "default": "10"
      },
      {
        "type": "select",
        "id": "space_v_item_mb",
        "options": [
          {
              "value": "0", 
              "label": "0"
          },
          {
              "value": "2", 
              "label": "2px"
          },
          {
              "value": "4", 
              "label": "4px"
          },
          {
              "value": "6", 
              "label": "6px"
          },
          {
              "value": "8", 
              "label": "8px"
          },
          {
              "value": "10", 
              "label": "10px"
          },
          {
            "value": "15",
            "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "30",
              "label": "30px"
          }
        ],
        "label": "Space vertical items (Mobile)",
        "default": "10"
      },
      {
        "type": "header",
        "content": "--Box options--"
      },
      {
        "type": "select",
        "id": "layout_des",
        "options": [
          {
            "value": "1",
            "label": "Grid"
          },
          {
            "value": "2",
            "label": "Carousel"
          }
        ],
        "label": "Layout design",
        "default": "2"
      },
      {
        "type": "header",
        "content": "+Options for carousel layout"
      },
      {
        "type": "checkbox",
        "id": "loop",
        "label": "Enable loop",
        "info": "At the end of cells, wrap-around to the other end for infinite scrolling",
        "default": true
      },
      {
        "type": "range",
        "id": "au_time",
        "min": 0,
        "max": 30,
        "step": 0.5,
        "label": "Autoplay speed in second.",
        "info": "Set is '0' to disable autoplay",
        "unit": "s",
        "default": 0
      },
      {
        "type": "checkbox",
        "id": "au_hover",
        "label": "Pause autoplay on hover",
        "info": "Auto-playing will pause when the user hovers over the carousel",
        "default": true
      },
      {
        "type": "paragraph",
        "content": "—————————————————"
      },
      {
        "type": "paragraph",
        "content": "Prev next button"
      },
      {
          "type": "select",
          "id": "show_btn",
           "options": [
            {
              "value": "show_all",
              "label": "Show all screen"
            },
            {
              "value": "show_desktop",
              "label": "Only show on desktop"
            },
            {
              "value": "show_mobile",
              "label": "Only show on tablet & mobile"
            },
            {
              "value": "hidden",
              "label": "Hidden"
            }
          ],
          "label": "Use prev next button",
          "default": "show_all"
        },
        {
            "type": "select",
            "id": "icon_slider",
            "label": "Prev next icon",
            "default": "1",
            "options": [
                {
                    "label": "Default",
                    "value": "0"
                },
                {
                    "label": "Solid",
                    "value": "1"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_pos",
            "label": "Prev next position",
            "info": "Working on screen Desktop",
            "default": "in",
            "options": [
                {
                    "label": "In content",
                    "value": "in"
                },
                {
                    "label": "Out content",
                    "value": "out"
                },
                {
                    "label": "Content side center",
                    "value": "between"
                }
            ]
        },
        {
            "type": "range",
            "id": "btn_distance",
            "min": 0,
            "max": 100,
            "step": 1,
            "label": "Distance from buttons to boundary",
            "info": "Only works when \"Prev next position is In content\". Only works on desktop.",
            "unit": "px",
            "default": 15
        },
        {
            "type": "select",
            "id": "btn_vi",
            "label": "Visible",
            "default": "hover",
            "options": [
                {
                    "value": "always",
                    "label": "Always"
                },
                {
                    "value": "hover",
                    "label": "Only hover"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_owl",
            "label": "Button style",
            "default": "default",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "outline",
                    "label": "Outline"
                },
                {
                    "value": "simple",
                    "label": "Simple"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_shape",
            "label": "Button shape",
            "info": "Not work for 'Simple' button style",
            "default": "none",
            "options": [
                {
                    "value": "none",
                    "label": "Default"
                },
                {
                    "value": "round",
                    "label": "Round"
                },
                {
                    "value": "rotate",
                    "label": "Rotate"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_cl",
            "label": "Button color",
            "default": "dark",
            "options": [
                {
                    "value": "light",
                    "label": "Light"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_size",
            "label": "Buttons size",
            "default": "small",
            "options": [
                {
                    "value": "small",
                    "label": "Small"
                },
                {
                    "value": "medium",
                    "label": "Medium"
                },
                {
                    "value": "large",
                    "label": "Large"
                }
            ]
        },
        {
            "type": "paragraph",
            "content": "—————————————————"
        },
        {
            "type": "paragraph",
            "content": "Page dots"
        },
        {
          "type": "select",
          "id": "show_dots",
          "info": "Creates and show page dots",
           "options": [
            {
              "value": "show_all",
              "label": "Show all screen"
            },
            {
              "value": "show_desktop",
              "label": "Only show on desktop"
            },
            {
              "value": "show_mobile",
              "label": "Only show on tablet & mobile"
            },
            {
              "value": "hidden",
              "label": "Hidden"
            }
          ],
          "label": "Use carousel's dots",
          "default": "hidden"
        },
        {
            "type": "select",
            "id": "dots_align",
            "label": "Dots align",
            "default": "center",
            "info": "Working on screen Desktop",
            "options": [
                {
                    "label": "Left",
                    "value": "start"
                },
                {
                    "label": "Center",
                    "value": "center"
                },
                {
                    "label": "Right",
                    "value": "end"
                }
            ]
        },
        {
            "type": "select",
            "id": "dot_owl",
            "label": "Dots style",
            "default": "dots_simple",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "background-active",
                    "label": "Background Active"
                },
                {
                    "value": "dots_simple",
                    "label": "Dots simple"
                },
                {
                    "value": "elessi",
                    "label": "Elessi"
                },
                {
                    "value": "br-outline",
                    "label": "Outline"
                },
                {
                    "value": "outline-active",
                    "label": "Outline active"
                }
            ]
        },
        {
            "type": "select",
            "id": "dots_cl",
            "label": "Dots color",
            "default": "dark",
            "options": [
                {
                    "value": "light",
                    "label": "Light (Best on dark background)"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "dots_round",
            "label": "Enable round dots",
            "default": true
        },
        {
            "type": "range",
            "id": "dots_space",
            "min": 2,
            "max": 20,
            "step": 1,
            "label": "Space among dots",
            "unit": "px",
            "default": 10
        },
      {
        "type": "header",
        "content": "3. Design options"
      },
      {
        "type": "select","id": "layout","default": "bee-container-wrap","label": "Layout",
        "options": [
            { "value": "bee-se-container", "label": "Container"},
            { "value": "bee-container-wrap", "label": "Wrapped container"},
            { "value": "bee-container-fluid", "label": "Full width"}
        ]
      },
      {
        "type": "color",
        "id": "cl_bg",
        "label": "Background"
      },
      {
        "type": "color_background",
        "id": "cl_bg_gradient",
        "label": "Background gradient"
      },
      {
        "type": "image_picker",
        "id": "image_bg",
        "label": "Background image"
      },
      {
          "type": "text",
          "id": "mg",
          "label": "Margin",
          "info":"Margin top, margin right, margin bottom, margin left. If you not use to blank",
          "default": ",,50px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd",
          "label": "Padding",
          "info":"Padding top, padding right, padding bottom, padding left. If you not use to blank",
          "placeholder": "50px,,50px,"
      },
      {
        "type": "header",
        "content": "+ Design mobile options"
      },
      {
          "type": "text",
          "id": "mg_mb",
          "label": "Margin",
          "default": ",,30px,",
          "placeholder": ",,50px,"
      },
      {
          "type": "text",
          "id": "pd_mb",
          "label": "Padding",
          "placeholder": ",,50px,"
      }
  ]
}
{% endschema %}