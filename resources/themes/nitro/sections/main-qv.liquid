{%- liquid
assign admin_sp = request.design_mode
if admin_sp
  assign arrTemp = '<template> | </template>' | split: ' | '
  assign product = section.settings.demo_pr
  if product.id == blank 
    assign cat_all = collections['all']
    assign avai_cat_all = cat_all.products | where: "available",true 
    assign product = avai_cat_all.first | default: cat_all.first
  endif
endif 

assign isProductAvailable = product.available
if isProductAvailable and settings.variant_remove == '2'
   assign remove_soldout = true 
else 
   assign remove_soldout = false  
endif 
assign se_stts          = section.settings
assign sid            = section.id
assign pr_sid         = product.id | append: sid
assign media_layout     = se_stts.media_layout | default: 'without_thumbnails'
assign pr_variants      = product.variants 
assign variants_size    = pr_variants.size 
assign selected_variant = product.selected_variant
assign pr_curent        = settings.pr_curent
if pr_curent == '1' and variants_size > 1
  assign current_variant = selected_variant
elsif pr_curent == '2' 
  assign current_variant = selected_variant | default: pr_variants.first 
  if remove_soldout and current_variant.available == false and isProductAvailable 
    assign current_variant = product.first_available_variant 
  endif
else 
  assign current_variant = product.selected_or_first_available_variant 
endif
assign PR_no_pick = false
if pr_curent == '1' and variants_size > 1 and selected_variant == blank
  assign PR_no_pick = true
endif
assign isProductDefault = product.has_only_default_variant

assign ntsoldout  = false
assign unvariants = false
assign ck_so_un   = false
if isProductDefault == false and variants_size > 1
  assign unavailable_prs = pr_variants | where: "available", false 
  assign vls0 = product.options_with_values[0].values.size | default: 1
  assign vls1 = product.options_with_values[1].values.size | default: 1
  assign vls2 = product.options_with_values[2].values.size | default: 1
  assign ntvariants_size = vls0 | times: vls1 | times: vls2 | minus: pr_variants.size
  if unavailable_prs.size > 0 or product.available == false
    assign ntsoldout = true
  endif
  if ntvariants_size > 0
    assign unvariants = true
  endif
  if ntsoldout or unvariants
    assign ck_so_un = true
  endif
endif
assign pr_tags = product.tags

assign pr_metafields      = product.metafields
assign meta_theme         = product.metafields.theme 
assign cus_qty            = meta_theme.cus_qty | default:1 
assign isExternal         = false
assign external_title     = meta_theme.external_title 
assign external_link      = meta_theme.external_link
if external_title != blank and external_link != blank 
   assign isExternal = true 
endif
assign isGrouped = false 
if  meta_theme.grouped != blank
   assign isGrouped = true 
endif

assign custom_badge = meta_theme.custom_badge
if custom_badge != blank
   assign arr_badge = custom_badge | replace: '  ', '' | replace: ' ;', ';' | replace: '; ', ';' | split: ';' | join: 'nt-bee' | escape | split: 'nt-bee'
   assign arr_badge_handle = arr_badge | join: 'nt-bee' | handle | split: 'nt-bee'
endif

assign product_media = product.media
assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src'
assign media_size = product_media.size
if media_size < 1
  assign media_layout = 'no_media_size'
elsif media_size == 1
  assign media_layout = 'one_media_size'
endif
assign canMedia_group = false 
assign mediaAlt = product_media | map: 'alt' | join: 'nt-bee'
if settings.use_group_media and variants_size > 1 and media_size > 1 and mediaAlt contains '_'
  assign canMedia_group = true
  
  if mediaAlt contains 'beeoption'
    assign ops_name   = product.options_with_values | map: 'name'
    assign ops_name_1 = product.options_by_name[ops_name[0]].values
    assign ops_name_2 = product.options_by_name[ops_name[1]].values
    assign ops_name_3 = product.options_by_name[ops_name[2]].values
  endif

  unless PR_no_pick
    assign isMediaHidden   = 'is--media-hide'
    assign current_option1 = current_variant.option1 | downcase 
    assign current_option2 = current_variant.option2 | downcase 
    assign current_option3 = current_variant.option3 | downcase
  endunless
endif

assign stts_media_size = se_stts.media_size | default: 'medium'
case stts_media_size
  when 'small'
   assign class_media = 'col-md-4'
   assign class_info = 'col-md-8'
  when 'medium'
   assign class_media = 'col-md-6'
   assign class_info = 'col-md-6'
  when 'large'
   assign class_media = 'col-md-7'
   assign class_info = 'col-md-5'
endcase
assign height = 720 

assign image_ratio = se_stts.image_ratio
if image_ratio == "ratioadapt"
  assign imgatt = ''
 else 
  assign imgatt = 'data-'
endif
if settings.enable_his and PR_no_pick == false
  assign enableHistoryState = settings.enable_his
else
  assign enableHistoryState = false
endif

assign links_vendor = linklists['image-vendor-bee'].links
assign pr_vendor = product.vendor

assign inventory_quantity = current_variant.inventory_quantity
assign inventory_management = current_variant.inventory_management
if inventory_quantity <= 0  and inventory_management == 'shopify' and current_variant.available
  assign classdn1 = 'bee-dn' 
  assign classdn2 = ''
  assign isPreoder = true
else 
  assign classdn1 = '' 
  assign classdn2 = 'bee-dn'
  assign isPreoder = false
endif

assign main_click = se_stts.main_click
if main_click == 'pswp'
  assign class_pswp_disable = ''
else
  assign class_pswp_disable = ' is-pswp-disable'
endif

assign seBlocks = section.blocks
assign Block_break = seBlocks | where: 'type','break' | first
if Block_break 
    assign hasBreak =  true
endif
assign Block_sizeg = seBlocks | where: 'type', 'size' | first
assign bk_stts__sizeg = Block_sizeg.settings
assign bk_size_chart = bk_stts__sizeg.size_chart | default: '1'
assign pos_sizeg = bk_stts__sizeg.pos_sizeg

assign Block_price = seBlocks | where: 'type', 'price' | first
assign block_price_stts = Block_price.settings

assign tabCheck      = false
assign idTabDes      = 'bee-tab-des' | append: sid
assign idTabReview   = 'bee-tab-review' | append: sid
assign tabs_position = se_stts.tabs_position
assign enable_video_looping     = se_stts.enable_video_looping
assign enable_video_muting      = se_stts.enable_video_muting
assign enable_video_autoplaying = se_stts.enable_video_autoplaying
case media_layout
when 'thumbnails_left' or 'thumbnails_bottom' or 'thumbnails_right' or 'without_thumbnails' or 'slider_center'
  assign hasIsotope = false
else
  assign hasIsotope = true
endcase
assign use_link_vendor = settings.use_link_vendor

if se_stts.btn_owl == "simple"
    assign arrow_icon = 1
  else
    assign arrow_icon = 2
  endif
  assign show_btn = se_stts.show_btn
  if show_btn != "hidden"
    assign slider_btns = true
  else
    assign slider_btns = false
  endif
  assign show_dots = se_stts.show_dots
  if show_dots != "hidden" and se_stts.dot_owl != 'dots-number'
    assign slider_dots = true
  else
    assign slider_dots = false
  endif
 -%}
{%- capture html_sizeg -%}
    {%- if bk_size_chart != '1' and Block_sizeg != blank -%}
        {%- liquid
            assign ck_s = true
            assign sc_type = bk_stts__sizeg.sc_type
            assign page_size = bk_stts__sizeg.page
            assign image = bk_stts__sizeg.image
    
            if bk_size_chart == '2' or pos_sizeg == '2'
                assign ck_s = false
                assign size_ck = bk_stts__sizeg.size_ck | append: ',size,sizes,Größe'
                assign get_size = size_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq
                for option in product.options_with_values
                    assign name = option.name | downcase
                    if get_size contains name
                        assign name_sizeg = name
                        assign ck_s = true
                        break
                    endif
                endfor
            endif
        -%}
        {%- capture icon_chart -%}
            {%- if bk_stts__sizeg.icon != '1' -%}
                {%- if bk_stts__sizeg.icon == 'default' -%}
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 136 512 240" class="bee-ani-{{ bk_stts__sizeg.ani }}"><path d="M0,136v240h512V136H0z M472,336H40V176h38v48h40v-48h40v88h40v-88h38v48h40v-48h38v88h40v-88h40v48h40v-48h38V336z"/></svg>
                {%- elsif bk_stts__sizeg.icon == '3' -%}{%- assign image_icon = bk_stts__sizeg.img -%}
                    {%- if image_icon != blank -%} 
                        <img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts__sizeg.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image_icon | image_url: width: 50 }}" width="{{ image_icon.width }}" height="{{ image_icon.height }} alt="{{ image_icon.alt }}">
                    {%- endif -%}
                {%- else -%} 
                    <i class="bee-ani-{{ bk_stts__sizeg.ani }} {{ bk_stts__sizeg.icon_name }}"></i>
                {%- endif -%}
                {%- if bk_stts__sizeg.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}
            {%- endif -%}
        {%- endcapture -%}
        {%- if sc_type == '1' and page_size != blank and ck_s -%}
            <a class="bee-ch bee-btn__size-chart" data-no-instant rel="nofollow" href="{{ page_size.url }}" data-class="bee-mfp-btn-close-inline" data-id="bee-pr-popup__size-guide" data-storageid="{{ page_size.url }}" data-open-mfp-ajax data-style="max-width:950px" data-mfp-src="{{ page_size.url }}/?section_id=ajax_popup">
                {{ icon_chart }}<span>{{ 'products.product.product_size_guide' | t }}</span>
            </a>
        {%- elsif sc_type == '2' and image != blank and ck_s -%}
            <a class="bee-ch bee-btn__size-chart" data-no-instant rel="nofollow" href="{{ product.url }}" data-bee-image-opend data-pswp-src="{{ image | image_url }}" data-pswp-w="{{ image.width }}" data-pswp-h="{{ image.height }}" data-pswp-class="pswp__size-guide">
                {{ icon_chart }}<span>{{ 'products.product.product_size_guide' | t }}</span>
            </a>
        {%- endif -%} 
    {%- endif -%}
{%- endcapture -%}
{%- if Block_price != blank and product.price_varies and block_price_stts.price != '0' -%}
  {%- capture style_price -%}
  style="--price-size:{{ block_price_stts.size_price_pr }}px;--price-del-size:{{ block_price_stts.size_price_pr | times: 0.8 }}px;--price-weight:{{ block_price_stts.fw_price_pr }};--primary-price-color:{{ block_price_stts.primary_price_color }};--secondary-price-color:{{ block_price_stts.secondary_price_color }};--price-sale-color:{{ block_price_stts.price_sale }};margin-bottom: 15px;"
  {%- endcapture -%}
  {%- capture html_price -%}
    {%- render 'product-price-single', variant: current_variant, product: product, PR_no_pick: false, type_sale: block_price_stts.type_sale, price_varies_style: '0', style_inline: style_price -%}
  {%- endcapture -%}
{%- endif -%}

{{- arrTemp[0] -}}

{{ 'pre_flickitybee.min.css' | asset_url | stylesheet_tag }}
{{ 'bee-main-product.css' | asset_url | stylesheet_tag }}
{{ 'bee-qv-product.css' | asset_url | stylesheet_tag }}
<style>.bee-drawer[aria-hidden=false] { z-index: 2000; }</style>

<div data-bee-scroll-me class="bee-container- bee-product-quick-view bee-product-media__{{ media_layout }} bee-product-thumb-size__{{ se_stts.thumb_lr_size }}">
  <div data-product-featured='{"id": "{{ product.id }}", "disableSwatch": {{ isProductDefault }}, "media": {% if media_size > 1 %}true{% else %}false{% endif %},"enableHistoryState": {{ enableHistoryState }}, "formID": "#product-form-{{ pr_sid }}", "removeSoldout": {{ remove_soldout }}, "changeVariantByImg": {{ settings.use_change_variant_by_img }}, "isNoPick": {{ PR_no_pick }},"hasSoldoutUnavailable": {{ ck_so_un }},"enable_zoom_click_mb": {{ se_stts.enable_zoom_click_mb | json }},"main_click": "{{ main_click | json }}","canMediaGroup": {{ canMedia_group }},"isGrouped": {{ isGrouped }},"hasIsotope": {{ hasIsotope }},"available": {{ product.available }}, "customBadge": {{ arr_badge | json }}, "customBadgeHandle": {{ arr_badge_handle | json }},"dateStart": {{ product.created_at | date: "%s" }}, "compare_at_price": {{ current_variant.compare_at_price | json }},"price": {{ current_variant.price | json }}, "isPreoder": {{ isPreoder }}, "showFirstMedia": {{ settings.show_first_media }} }' class="bee-row bee-row__product is-zoom-type__{{ se_stts.zoom_tp }}" data-bee-zoom-main{% if main_click == 'zoom' %} data-zoom-options-='{"type": "{{ se_stts.zoom_tp }}", "magnify": 2, "touch": false, "pr_type": "1"}'{% endif %}>
    <div class="bee-{{ class_media }} bee-col-12 bee-col-item bee-product__media-wrapper bee-pr">
      {%- case media_layout -%}

        {%- when 'no_media_size' -%}
          {%- assign image = settings.placeholder_img -%}
          {%- if image != blank -%}
              <div class="bee-product__no_media bee-pr">
                <div data-bee-gallery data-bee-thumb-false data-main-media class="bee-product__media-item bee_{{ image_ratio }} bee_position_{{ se_stts.image_position }} bee_{{ se_stts.image_size }}">
                  <div data-bee-gallery--open data-media-type="image" class="bee_ratio bee-product__media{{ class_pswp_disable }}" {{ imgatt }}style="--aspect-ratioapt: {{ image.aspect_ratio }};--mw-media: {{ image.width }}px">
                    <noscript>{{ image | image_url: width: 1500 | image_tag: widths: '288, 576, 750, 1100, 1500', class: 'bee-img-noscript', loading: 'lazy', sizes: '(min-width: 1500px) 1500px, (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)' }}</noscript>
                    <img data-master="{{ image | image_url }}" class="lazyloadbee bee-lz--fadeIn" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="1.5" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}"><span class="lazyloadbee-loader"></span>
                  </div>
                </div>
                <div data-product-single-badge data-sort="sale,new,soldout,preOrder,custom" class="bee-single-product-badge lazyloadbee bee-pa bee-pe-none bee-op-0" data-renderbee="css:{{ 'single-pr-badge.css' | asset_url }}"></div>
                {% if se_stts.enable_wishlist or se_stts.enable_compare %}
                  <div class="bee-pr__wis_cp">
                    {%- render 'bee_wis_cp', product: product, bk_stts: bk_stts, disableTooltip: true, enable_wishlist:se_stts.enable_wishlist, enable_compare: se_stts.enable_compare  -%}
                  </div>
                {% endif %}
                {%- render 'product-btns', se_stts: se_stts, product: product -%}
              </div>
              {%- endif -%}

        {%- when 'one_media_size' %}{% assign media = product_media.first -%}
            <div data-product-single-media-group class="bee-product__one_media bee-pr">
              <div data-bee-gallery data-bee-thumb-false data-main-media class="bee-row bee-g-0 bee-product__media bee_{{ image_ratio }} bee_position_{{ se_stts.image_position }} bee_{{ se_stts.image_size }}">
                {%- render 'product-thumbnail', media: media, imgatt: imgatt, variant_images: variant_images, loop: enable_video_looping, mute: enable_video_muting, autoplay: enable_video_autoplaying, height: height, sid: sid, class_pswp_disable: class_pswp_disable, variants_size: 1 -%}
              </div>
              <div data-product-single-badge data-sort="sale,new,soldout,preOrder,custom" class="bee-single-product-badge lazyloadbee bee-pa bee-pe-none bee-op-0" data-renderbee="css:{{ 'single-pr-badge.css' | asset_url }}"></div>
              {% if se_stts.enable_wishlist or se_stts.enable_compare %}
                <div class="bee-pr__wis_cp">
                  {%- render 'bee_wis_cp', product: product, bk_stts: bk_stts, disableTooltip: true, enable_wishlist:se_stts.enable_wishlist, enable_compare: se_stts.enable_compare  -%}
                </div>
              {% endif %}
              {%- render 'product-btns', se_stts: se_stts, product: product -%}
            </div>

        {%- when 'thumbnails_left' or 'thumbnails_bottom' or 'thumbnails_right' or 'without_thumbnails' -%}
          
          {%- liquid
          if media_layout == 'thumbnails_left'
            assign class_main = 'bee-col-lg bee-order-lg-last '
            assign class_thumb = 'bee-col-lg-auto bee-order-lg-first '
          elsif media_layout == 'thumbnails_right'
            assign class_main = 'bee-col-lg '
            assign class_thumb = 'bee-col-lg-auto '
          else 
            assign class_gx_lg = ' bee-g-lg-10 ' 
          endif -%}

          <div class="bee-row bee-g-0 bee-gx-0 bee-gx-lg-0">
            <div data-product-single-media-group class="{{ class_main }}bee-col-12 bee-col-item">
              <div data-bee-gallery data-main-media data-bee-thumb-true class="{{slider_dots}}--x bee-row bee-g-0 bee-slide-eff-{{ se_stts.eff }} flickitybee bee-flickity-slider bee_{{ image_ratio }} bee_position_{{ se_stts.image_position }} bee_{{ se_stts.image_size }} {% if slider_btns %} bee-slider-btn__{{ se_stts.btn_pos }}-content bee-slider-btn-{{show_btn}} bee-slider-btn-style-{{ se_stts.btn_owl }} bee-slider-btn-{{ se_stts.btn_shape }} bee-slider-btn-{{ se_stts.btn_size }} bee-slider-btn-cl-{{ se_stts.btn_cl }} bee-slider-btn-vi-{{ se_stts.btn_vi }} {% endif %}{% if slider_dots %} bee-dots-style-{{ se_stts.dot_owl }} bee-slider-dots-{{show_dots}} bee-dots-cl-{{ se_stts.dots_cl }} bee-dots-round-{{ se_stts.dots_round }} {% endif %}" data-flickitybee-js='{"beeid": "{{ sid }}", "status": true, "cellSelector": "[data-main-slide]:not(.is--media-hide)","isFilter": {{ canMedia_group }},"imagesLoaded": 0,"adaptiveHeight": 1, "contain": 1, "groupCells": "100%", "dragThreshold" : {% if se_stts.eff == 'fade' %}6{% else %}5{% endif %}, "cellAlign": "left","wrapAround": true,"prevNextButtons": {{ slider_btns }},"percentPosition": 1,"pageDots": {{ slider_dots }}, "autoPlay" : 0, "pauseAutoPlayOnHover" : true }' style="--space-dots: {{ se_stts.dots_space }}px;">
                {%- for media in product_media -%}
                   {%- render 'product-thumbnail', media: media, imgatt: imgatt, variant_images: variant_images, loop: enable_video_looping, mute: enable_video_muting, autoplay: enable_video_autoplaying, height: height, sid: sid, isMediaHidden: isMediaHidden, current_option1: current_option1, current_option2: current_option2, current_option3: current_option3, ops_name: ops_name, ops_name_1: ops_name_1, ops_name_2: ops_name_2, ops_name_3: ops_name_3, class_pswp_disable: class_pswp_disable, variants_size: variants_size -%}
                {%- endfor -%}
                {%- if show_dots != "hidden" and se_stts.dot_owl == 'dots-number' -%}
                  <div class="carousel--status{{ sid }} bee-dots-num-cl-{{ se_stts.dots_cl }} bee-slider-dots-number-{{ show_dots }} bee-dots-number-round-{{ se_stts.dots_round }} bee-dots-num-layout"><span data-current-slide></span> / <span data-total-number></span></div>
                  {%- endif -%}
                </div>
              <div data-product-single-badge data-sort="sale,new,soldout,preOrder,custom" class="bee-single-product-badge lazyloadbee bee-pa bee-pe-none bee-op-0" data-renderbee="css:{{ 'single-pr-badge.css' | asset_url }}"></div>
              {% if se_stts.enable_wishlist or se_stts.enable_compare %}
                <div class="bee-pr__wis_cp">
                  {%- render 'bee_wis_cp', product: product, bk_stts: bk_stts, disableTooltip: true, enable_wishlist:se_stts.enable_wishlist, enable_compare: se_stts.enable_compare  -%}
                </div>
              {% endif %}
            </div>

            {%- if media_layout != 'without_thumbnails' -%}
                <div class="{{ class_thumb }}bee-col-12 bee-col-item bee-col-thumb bee-pr bee-oh">
                  <div data-thumb__scroller="{{ sid }}" class="bee-carousel__nav-scroller is__position-{{ media_layout | remove: 'thumbnails_' }}">
                    <div class="bee-row bee-row-mt bee-row-cols-4 bee_{{ image_ratio }} bee_position_{{ se_stts.image_position }} bee_{{ se_stts.image_size }} {{ class_gx_lg }}bee-g-5 bee-carousel__nav carousel__nav--{{ sid }} carousel__nav-hover1">
                      {%- for media in product_media limit: 4 %}{% assign image = media.preview_image -%} 
                        <div class="bee-col-item bee-product__thumb-item"><div class="bee_ratio bee-product__thumb" {{ imgatt }}style="--aspect-ratioapt: {{ image.aspect_ratio }}"></div></div>
                      {%- endfor -%}
                    </div>
                  </div>
                  <button type="button" data-thumb-btn__prev="{{ sid }}" aria-label="Previous" class="btn_pnav_prev bee-pa bee-pe-none bee-op-0"></button>
                  <button type="button" data-thumb-btn__next="{{ sid }}" aria-label="Next" class="btn_pnav_next bee-pa bee-pe-none bee-op-0"></button>
                </div>
            {%- endif -%}

          </div>     
      {%- endcase -%}
      {% if se_stts.enable_view_details %}
        <div class="bee-view-products bee-text-center">
          <a href="{{ product.url }}">
            {{ 'products.product.view_details' | t }} 
            <svg version="1.1" x="0px" y="0px" width="18px" height="18px" viewBox="0 0 18 18" style="enable-background:new 0 0 18 18;" xml:space="preserve">
            <path d="M13.65,5.24c-0.42-0.36-1.05-0.31-1.41,0.11c-0.36,0.42-0.31,1.05,0.11,1.41L13.8,8H1C0.45,8,0,8.45,0,9s0.45,1,1,1h12.8 l-1.45,1.24c-0.42,0.36-0.47,0.99-0.11,1.41C12.44,12.88,12.72,13,13,13c0.23,0,0.46-0.08,0.65-0.24L18.04,9L13.65,5.24z"></path>
            </svg>
          </a>
        </div>
      {% endif %}
    </div>
    <div data-bee-zoom-info class="bee-{{ class_info }} bee-col-12 bee-col-item bee-product__info-wrapper bee-pr">
      <div id="product-zoom-{{ section.id }}" class="bee-product__zoom-wrapper"></div>
      <div id="ProductInfo-template--{{ sid }}__main" class="bee-product__info-container bee-op-0 bee-current-scrollbar{% if se_stts.enable_sticky_info %} bee-product__info-container--sticky{% endif %}">

        {%- for block in seBlocks %}{% assign bk_stts = block.settings -%}{% assign bk_id = block.id -%}
          {%- case block.type -%}
          
            {%- when 'properties' %}{% continue -%}
            {%- when 'title' -%}
              {% liquid 
                  if bk_stts.lh_pr == 0
                      assign lh_pr = 1
                  else
                      assign lh_pr = bk_stts.lh_pr | append: 'px'
                  endif  
                  if bk_stts.lh_pr_tb == 0
                      assign lh_pr_tb =1
                  else
                      assign lh_pr_tb = bk_stts.lh_pr_tb | append: 'px'
                  endif
                  if bk_stts.lh_pr_mb == 0
                      assign lh_pr_mb =1
                  else
                      assign lh_pr_mb = bk_stts.lh_pr_mb | append: 'px'
                  endif
              -%}
              <a class="bee-product__title bee-d-block" href="{{ product.url }}" style="--title-family: var(--font-family-{{ bk_stts.fnt_df_pr }});--title-style: {{ bk_stts.txt_tr_pr }};--title-size: {{ bk_stts.size_pr }}px;--title-weight: {{ bk_stts.fw_pr }};--title-line-height: {{ lh_pr }};--title-spacing: {{ bk_stts.ls_pr }}px;--title-size-tb: {{ bk_stts.size_pr_tb }}px;--title-line-height-tb: {{ lh_pr_tb }};--title-spacing-tb: {{ bk_stts.ls_pr_tb }}px;--title-size-mb: {{ bk_stts.size_pr_mb }}px;--title-line-height-mb: {{ lh_pr_mb }};--title-spacing-mb: {{ bk_stts.ls_pr_mb }}px;--title-color: {{ bk_stts.pr_title_color }};--title-color-hover: {{ bk_stts.pr_title_hover_color }};" {{ block.shopify_attributes }}>
                {{ product.title }}
              </a>
            {%- when 'price' -%}
              <div class="bee-product__price" style="--price-size:{{ bk_stts.size_price_pr }}px;--price-del-size:{{ bk_stts.size_price_pr | times : 0.8 }}px;--price-weight:{{ bk_stts.fw_price_pr }};--primary-price-color:{{ bk_stts.primary_price_color }};--secondary-price-color:{{ bk_stts.secondary_price_color }};--price-sale-color:{{ bk_stts.price_sale }}">
                  {%- render 'product-price-single', variant: current_variant, product: product, PR_no_pick: PR_no_pick, type_sale: bk_stts.type_sale, price_varies_style: bk_stts.price -%}
              </div>
              {%- if bk_stts.tax_ship and cart.taxes_included or shop.shipping_policy.body != blank -%}
                  <div class="bee-product__policies bee-rte" data-product-policies>
                      {%- if cart.taxes_included -%}
                          {{ 'products.product.include_taxes' | t }}
                      {%- endif -%}
                      {%- if shop.shipping_policy.body != blank -%}
                          {{ 'products.product.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                      {%- endif -%}
                  </div>
              {%- endif -%}

            {%- when 'review' -%}
              
                <div class="bee-product__review"> 
                    {%- if bk_stts.rating -%}
                        {%- assign pid = product.id -%}
                        <a href="#{{ idTabReview }}" data-go-id="#{{ idTabReview }}" data-no-instant class="bee-d-inline-block">
                            {%- case settings.app_review -%}              
                                {%- when '1' -%}
                                    <span class="shopify-product-reviews-badge" data-id="{{ pid }}"></span>
                                {%- when '2' -%}
                                    <div class="review-widget"><ryviu-widget-total reviews_data="{{ pr_metafields.ryviu.product_reviews_info | escape }}" product_id="{{ pid }}" handle="{{ product.handle }}"></ryviu-widget-total></div>
                                {%- when '3' -%}
                                    <div product-id="{{ pid }}" class="alr-display-review-badge"></div>
                                {%- when '4' -%}
                                    <div class="loox-rating" data-id="{{ pid }}" data-rating="{{ pr_metafields.loox.avg_rating }}" data-raters="{{ pr_metafields.loox.num_reviews }}"></div>
                                {%- when '5' -%}
                                    {%- capture the_snippet_review_avg %}{% render 'ssw-widget-avg-rate-profile' %}{% endcapture -%}
                                    {%- unless the_snippet_review_avg contains 'Liquid error' %}{{ the_snippet_review_avg }}{% endunless -%}
                                {%- when '7' -%}{%- render 'judgeme_widgets', widget_type: 'judgeme_preview_badge', jm_style: '', concierge_install: false, product: product -%}
                                {%- when '8' -%}<div id="scm-product-detail-rate" class="scm-reviews-rate" data-rate-version2= {{ product.metafields.scm_review_importer.reviewsData.reviewCountInfo | json }} data-product-id= {{ product.id }}></div>
                                {%- else -%}
                                    <div class="bee-pr_rating bee-review_pr_other">{{ bk_stts.review_liquid }}</div>
                            {%- endcase -%}
                        </a>
                  {% endif %}
                </div>
            {%- when 'line' -%}
                <div class="bee-product__line" style="--height-line: {{ bk_stts.height_line }}px;--space-tb: {{ bk_stts.space_tb }}px;--space-tb-mb: {{ bk_stts.space_tb_mb }}px;--color-line: {{ bk_stts.color_line }}"></div>
            {%- when 'description' -%}
              {%- assign pr_des = product.description -%}
              {%- assign description_excerpt = meta_theme.description_excerpt | default: bk_stts.text -%}
              {% if description_excerpt == blank and pr_des == blank %}{% continue %}{% endif -%}
              {%- capture readm -%}
                  {%- if bk_stts.readm %}<button class="bee-product__description_readm" data-go-id='#{{ idTabDes }}'>{{ bk_stts.readm_txt }}</button>{% endif -%}
              {%- endcapture -%}
              <div class="bee-product__description bee-rte" {{ block.shopify_attributes }}>
                  {%- if bk_stts.des == '1' -%}{{- pr_des -}}
                  {%- elsif description_excerpt != blank -%}{{- description_excerpt -}}{{ readm }}
                  {%- else -%}<p>{{- pr_des | strip_html | truncatewords: bk_stts.length -}}{{ readm }}</p>
                  {%- endif -%}
              </div>
            {%- when 'size' -%}
                <div class="bee-product__size">{%- if bk_size_chart != '1' and pos_sizeg == '1' %}{{ html_sizeg }}{% endif -%}</div>
            {%- when 'form' -%}
                {%- liquid
                    assign product_list = bk_stts.product_list
                    assign advance_pr_type = bk_stts.advance_pr_list
                    assign advance_label = bk_stts.advance_label
                    assign is_fit_ratio_img = false
                    if variant_images.size > 0 and bk_stts.enable_fit_ratio_img and bk_stts.color_mode contains 'variant_image'
                        assign is_fit_ratio_img = true
                        assign first_ratio_img = variant_images[0].aspect_ratio
                    endif
                    assign arr_properties = seBlocks | where: 'type', 'properties' 
                    if product_list == blank
                        render 'product-form', type: 'main', product: product, bk_stts: bk_stts, pr_sid: pr_sid, isExternal: isExternal, external_title: external_title, external_link: external_link, isProductAvailable: isProductAvailable, isProductDefault: isProductDefault, current_variant: current_variant, remove_soldout: remove_soldout, PR_no_pick: PR_no_pick, isPreoder: isPreoder, is_fit_ratio_img: is_fit_ratio_img, first_ratio_img: first_ratio_img, name_sizeg: name_sizeg, html_sizeg: html_sizeg,pos_sizeg: pos_sizeg, advance_pr_type: advance_pr_type, advance_label: advance_label, html_price: html_price ,shopify_attributes: block.shopify_attributes, arr_properties: arr_properties, cus_qty: cus_qty
                    else
                        render 'grouped-form', product: product, bk_stts: bk_stts, pr_sid: pr_sid, product_list: product_list, shopify_attributes: block.shopify_attributes, cus_qty: cus_qty
                    endif 
                -%}
            {%- when 'delivery_ask' -%}
                <div class="bee-extra-link" {{ block.shopify_attributes }}>
                    {%- assign page_delivery = bk_stts.page_dr %}{% if bk_stts.delivery and page_delivery != blank -%}
                        <a class="bee-ch" data-no-instant rel="nofollow" href="{{ page_delivery.url }}" data-class="bee-mfp-btn-close-inline" data-id="bee-pr-popup__delivery" data-storageid="{{ page_delivery.url }}" data-open-mfp-ajax data-style="max-width:950px" data-mfp-src="{{ page_delivery.url }}/?section_id=ajax_popup">{{ 'products.product.delivery_return' | t }}</a>
                    {%- endif -%}
        
                    {%- if bk_stts.ask -%}
                    <a class="bee-ch" data-no-instant rel="nofollow" href="{{ product.url }}" data-class="bee-mfp-btn-close-inline" data-id="bee-pr-popup__contact" data-storageid="contact_product{{ product.id }}" data-open-mfp-ajax data-style="max-width:570px" data-mfp-src="{{ product.url }}/?section_id=ajax_popup" data-phone='{{ bk_stts.phone }}'>{{ 'products.product.ask_question' | t }}</a>
                    {%- endif -%}
                </div> 
            {%- when 'meta' -%}
              {%- assign ck_meta = false -%}
              {%- capture meta -%}
                  {%- if bk_stts.show_options and product.has_only_default_variant == false -%}{%- assign ck_meta = true -%}
                      {%- for product_option in product.options_with_values -%}
                          <div class="bee-option-wrapper">{{ product_option.name | escape }}: <span class="bee-productMeta__value bee-option-value bee-csecondary bee-dib">{{ product_option.values | join: ', ' }}</span></div>
                      {%- endfor -%}
                  {%- endif -%}

                  {%- if bk_stts.show_vendor and pr_vendor != blank %}{% assign ck_meta = true -%}
                    {%- liquid
                    assign pr_vendor_url = pr_vendor | url_for_vendor
                    if use_link_vendor
                      assign pr_vendor_handle = pr_vendor | handle
                      assign collection_vendor = collections[pr_vendor_handle]
                      assign pr_vendor_url = collection_vendor.url | default: pr_vendor_url
                    endif -%}
                    <div class="bee-vendor-wrapper">{{ 'products.product.product_vendor' | t }} <span class="bee-productMeta__value bee-vendor-value bee-csecondary bee-dib"><a href="{{ pr_vendor_url }}">{{ pr_vendor }}</a></span></div>
                  {%- endif -%}

                  {%- if bk_stts.show_type and product.type != blank -%} {%- assign ck_meta = true -%}
                    {%- liquid
                    if settings.use_link_type
                    assign pr_type_handle = product.type | handle
                    assign collection_type = collections[pr_type_handle]
                    endif -%}
                  <div class="bee-type-wrapper">{{ 'products.product.product_type' | t }} <span class="bee-productMeta__value bee-type-value bee-csecondary bee-dib"><a href="{% if settings.use_link_type and collection_type.url != blank %}{{ collection_type.url }}{% else %}{{ product.type | url_for_type }}{% endif %}">{{ product.type }}</a></span></div>
                  {%- endif -%}
      
                  {%- if bk_stts.show_sku -%} {%- assign ck_meta = true -%}
                      <div class="bee-sku-wrapper{% if current_variant.sku == blank or current_variant.sku == '' %} bee-dn{% endif %}" data-product-sku>{{ 'products.product.product_sku' | t }} <span class="bee-productMeta__value bee-sku-value bee-csecondary bee-dib" data-product__sku-number>{{ current_variant.sku }}</span></div>
                  {%- endif -%}
      
                  {%- if bk_stts.show_barcode -%} {%- assign ck_meta = true -%}
                      <div class="bee-barcode-wrapper{% if current_variant.barcode == blank or current_variant.barcode == '' %} bee-dn{% endif %}" data-product-barcode>{{ 'products.product.product_barcode' | t }} <span class="bee-productMeta__value bee-barcode-value bee-csecondary bee-dib" data-product__barcode-number>{{ current_variant.barcode }}</span></div>
                  {%- endif -%}
      
                  {%- if bk_stts.show_available -%} {%- assign ck_meta = true -%}
                      <div data-product-available class="bee-available-wrapper">{{ 'products.product.available' | t }}
                          <span class="bee-productMeta__value bee-available-value">
                              <span data-available-status class="bee-available-status bee-csecondary bee-dib{% unless isProductAvailable %} bee-dn{% endunless %}">
                                  <span data-instock-status class="{{ classdn1 }}">{{ 'products.product.in_stock_status' | t }}</span>
                                  <span data-preorder-status class="{{ classdn2 }}">{{ 'products.product.pre_oder_status' | t }}</span>
                              </span>
                              <span data-soldout-status class="bee-soldout-status bee-csecondary bee-dib{% if isProductAvailable %} bee-dn{% endif %}">{{ 'products.product.sold_out_status' | t }}</span>
                          </span>
                      </div>
                  {%- endif -%}
      
                  {%- if product.collections.size > 0 and bk_stts.show_category %}{% assign ck_meta = true -%}
                      <div class="bee-collections-wrapper">{{ 'products.product.product_category' | t }}
                          {% for collection in product.collections -%}{{ collection.title | link_to: collection.url, class: 'bee-dib' }}{%- if forloop.last == false -%}, {% endif -%}{%- endfor -%}
                      </div>
                  {%- endif -%}
      
                  {%- if pr_tags.size > 0 and bk_stts.show_tags and product.collections.size > 0  -%}

                      {%- liquid
                      assign ck_meta = true
                      assign bee_pr_tags = pr_tags | where:'bee_'
                      if pr_tags.size == bee_pr_tags.size
                       assign show_tag_final = false
                      else
                       assign show_tag_final = true
                      endif -%}
                      
                      {%- if show_tag_final -%}
                        {%- assign tag_cat_url = collection.url | default: product.collections.first.url -%}
                        <div class="bee-tags-wrapper">{{ 'products.product.product_tag' | t }}
                            {% for tag in pr_tags -%}
                                {%- if tag contains 'bee_' or tag contains 'badge_' %}{% continue %}{% endif -%}
                                <a class="bee-dib" href="{{ tag_cat_url }}/{{ tag | handle }}">{{ tag | remove: 'type ' | remove: 'Type ' }}</a>{%- unless forloop.last -%}, {% endunless -%}
                            {%- endfor -%}
                        </div>
                      {%- endif -%}

                  {%- endif -%}
              {%- endcapture -%}
              {%- if ck_meta -%}<div class="bee-product__meta{% if bk_stts.pr_meta_horizontal %} bee-product__meta-horizontal{% endif %}" {{ block.shopify_attributes }}>{{- meta -}}</div>{%- endif -%}

            {%- when 'socials' -%}
              <div class="bee-product__social-share bee-text-{{ bk_stts.socials_align }} bee-d-inline-flex bee-align-items-center" {{ block.shopify_attributes }}>
                  <span class="bee-product__social-title">{{ bk_stts.title }}:</span>
                  {%- liquid 
                      assign pri_cl_lightness       = bk_stts.pri_cl | color_extract: 'lightness'
                      assign pri_hover_cl_lightness       = bk_stts.pri_hover_cl | color_extract: 'lightness'
                      if pri_cl_lightness < 85
                          assign pri_cl2 = "#fff"
                      else 
                          assign pri_cl2 = "#000"
                      endif
                      if pri_hover_cl_lightness < 85
                          assign pri_hover_cl2 = "#fff"
                      else 
                          assign pri_hover_cl2 = "#000"
                      endif
                  -%}
                  {%- if settings.share_source == '1' -%}
                      {{ 'bee-icon-social.css' | asset_url | stylesheet_tag }}
                      <div class="bee-socials-block social-{{ block.id }}" style="--pri-cl: {{ bk_stts.pri_cl }};--pri-cl2: {{ pri_cl2 }};--pri-hover-cl: {{ bk_stts.pri_hover_cl }};--pri-hover-cl2: {{ pri_hover_cl2 }};--mgb: {{ bk_stts.mgb }}px;--mgb-tb: {{ bk_stts.mgb_tb }}px;--mgb-mb: {{ bk_stts.mgb_mb }}px; --bd-radius: {{ bk_stts.bd_radius }}px;">
                          {%- if bk_stts.social_mode == '1' -%}{%- assign follow_social = true -%} {%- endif -%} 
                          {%- render 'social_sharing', style: bk_stts.social_style, use_color_set: bk_stts.use_color_set, size: bk_stts.social_size, space_h_item: bk_stts.space_h_item, space_h_item_mb: bk_stts.space_h_item_mb, space_v_item: bk_stts.space_v_item, space_v_item_mb: bk_stts.space_v_item_mb, follow_social: follow_social -%} 
                      </div> 
                  {%- elsif settings.share_source == '3' -%}
                      {{ 'bee-icon-social.css' | asset_url | stylesheet_tag }}
                      {%- capture the_snippet_share -%}{% render 'ssw-widget-share-links' with 1 %}{%- endcapture -%}
                      {%- unless the_snippet_share contains 'Liquid error' %}{{ the_snippet_share }}{%- endunless -%}
                  {%- else -%}
                      {{ 'bee-icon-social-addthis.css' | asset_url | stylesheet_tag }}
                      <div data-bee-addthis class="bee-product__social bee__addthis addthis_inline_share_toolbox_icxz bee-socials bee-socials-size-{{ bk_stts.social_size }} bee-socials-style-{{ bk_stts.social_style }} bee-color-set-{{ bk_stts.use_color_set }}" style="--pri-cl: {{ bk_stts.pri_cl }};--pri-cl2: {{ pri_cl2 }};--pri-hover-cl: {{ bk_stts.pri_hover_cl }};--pri-hover-cl2: {{ pri_hover_cl2 }};--mgb: {{ bk_stts.mgb }}px;--mgb-tb: {{ bk_stts.mgb_tb }}px;--mgb-mb: {{ bk_stts.mgb_mb }}px; --bd-radius: {{ bk_stts.bd_radius }}px;--social-mgr: {{ bk_stts.space_h_item }}px;--social-mgb: {{ bk_stts.space_v_item }}px;--social-mgr-mb: {{ bk_stts.space_h_item_mb }}px;--social-mgb-mb: {{ bk_stts.space_v_item_mb }}px;"></div>
                  {%- endif -%}
              </div>
            {%- when 'vendor_img' -%}
                  {%- liquid 
                  if pr_vendor == blank 
                   continue 
                  endif
                  assign img_vendor = links_vendor | where: 'title', pr_vendor | first
                  assign name_img_vendor = img_vendor.url | split: '?' | first | split : '/' | last
                  assign image = images[name_img_vendor]
                  assign pr_vendor_url = pr_vendor | url_for_vendor
                  if use_link_vendor
                    assign pr_vendor_handle = pr_vendor | handle
                    assign collection_vendor = collections[pr_vendor_handle]
                    assign pr_vendor_url = collection_vendor.url | default: pr_vendor_url
                  endif -%}
                  
                  <div class="bee-pr-vendor bee-d-inline-flex bee-align-items-center bee-justify-content-center bee-text-center{% if image != blank %} has-img__vendor{% endif %}"><a href="{{ pr_vendor_url }}">
                      {%- if image != blank -%}
                       <img width="{{ image.width }}" height="{{ image.height }}" src="data: image/svg+xml,%3Csvg%20viewBox%3D%220%200%20{{ image.width }}%20{{ image.height }}%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3C%2Fsvg%3E" data-src="{{ image | image_url: width: 1 }}" data-widths="[60, 90, 180, 320, 640]" data-sizes="auto" class="bee-w-100 lazyloadbee bee-lz--fadeIn" alt="{{ pr_vendor | escape }}"><span class="lazyloadbee-loader"></span><span class="visually-hidden">{{ pr_vendor | escape }}</span>
                      {%- else -%}
                       {{ pr_vendor | escape }}
                      {%- endif -%}
                   </a></div>

            {%- when 'img' -%}
              {%- capture html_trust -%}
                      {%- assign image = bk_stts.image -%}
                      {%- if bk_stts.source == '1' and image != blank -%}
                          <img style="--max-w-img: {{ bk_stts.wimg }}%" width="{{ image.width }}" height="{{ image.height }}" class="bee-img-tr__img lazyloadbee" src="data: image/svg+xml,%3Csvg%20viewBox%3D%220%200%20{{ image.width }}%20{{ image.height }}%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3C%2Fsvg%3E" data-src="{{ image | image_url: width: 1 }}" data-widths="[90, 120, 150, 180, 360, 480, 600, 750, 940]" data-sizes="auto" alt="{{ image.alt }}">
                      {%- else bk_stts.svg -%}
                          {%- assign isStyleSvg = true -%}
                          {%- assign svg_height = bk_stts.height -%}
                          {%- assign svg_width = svg_height | times: 1.57446809 | ceil -%}
                          {%- assign arr = bk_stts.svg | remove: ' ' | split:"," -%}
                          {%- for img in arr -%}<img width="{{ svg_width }}" height="{{ svg_height }}" class="bee-img-tr__svg lazyloadbee" src="https://cdn.shopify.com/s/assets/payment_icons/generic-dfdcaf09b6731ca14dd7441354c0ad8bc934184eb15ae1fda6a6b9e307675485.svg" data-src="{{ img | payment_type_img_url }}" alt="{{ img | replace: '_',' ' }}" />{%- endfor -%}
                      {%- endif -%}
              {%- endcapture -%}
              <div id="bee-trust_seal{{ bk_id }}" data-rr="{{ svg_width }}" {% if isStyleSvg %} style="--height-img: {{ svg_height }}px;"{% endif %} class="bee-pr_trust_seal bee-text-md-{{ bk_stts.al }} bee-text-center" {{ block.shopify_attributes }}>
              {%- if bk_stts.mess -%}<p class="bee-pr-mess_trust bee-ch" style="--fs-mess-trust: {{ bk_stts.size }}px;">{{ bk_stts.mess }}</p>{%- endif -%}
              {{- html_trust -}}
              </div>
            {%- when 'live_view' %}{% if bk_stts.text == blank %}{% continue %}{% endif -%}
              <div id="bee-counter{{ bk_id }}" class="bee-pr_live_view bee-dn bee-ch bee-fwm" data-live-view='{ "min": {{ bk_stts.min }}, "max": {{ bk_stts.max }}, "interval": {{ bk_stts.time }}000 }' {{ block.shopify_attributes }}>
                  {%- if bk_stts.icon != '1' -%}{%- assign image = bk_stts.img -%}
                      {%- if bk_stts.icon == 'default' -%}
                          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svg="http://svg.com/svg" version="1.1" x="0" y="0" xml:space="preserve" viewBox="3.5 22 91 58" class="bee-ani-{{ bk_stts.ani }}" ><g> <style xmlns="http://www.w3.org/2000/svg" type="text/css"> .st0{fill: url(#SVGID_1_);} .st1{fill: url(#SVGID_2_);} </style> <g xmlns="http://www.w3.org/2000/svg"><linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="49" y1="22" x2="49" y2="80"> <stop offset="0" style="stop-color: #00EFD1"></stop> <stop offset="1" style="stop-color: #00ACEA"></stop></linearGradient><path class="st0" d="M93.9,49.2C93.7,49,88.9,42.4,81,35.7C70.4,26.7,59.3,22,49,22C24.3,22,4.9,48.1,4.1,49.2 c-0.8,1.1-0.8,2.5,0,3.5C4.3,53,9.1,59.6,17,66.3C27.6,75.3,38.7,80,49,80c24.7,0,44.1-26.1,44.9-27.2 C94.7,51.7,94.7,50.3,93.9,49.2z M49,74c-18.2,0-34-17.3-38.7-23C15,45.3,30.7,28,49,28c18.2,0,34,17.3,38.7,23 C83,56.7,67.3,74,49,74z" fill="#000000" data-original="#000000"></path><linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="49.0001" y1="33" x2="49.0001" y2="69"> <stop offset="0" style="stop-color: #00EFD1"></stop> <stop offset="1" style="stop-color: #00ACEA"></stop></linearGradient><path class="st1" d="M49,33c-9.9,0-18,8.1-18,18c0,9.9,8.1,18,18,18c9.9,0,18-8.1,18-18C67,41.1,58.9,33,49,33z M49,63 c-6.6,0-12-5.4-12-12c0-6.6,5.4-12,12-12c6.6,0,12,5.4,12,12C61,57.6,55.6,63,49,63z" fill="#000000" data-original="#000000"></path> </g> </g></svg>
                      {%- elsif bk_stts.icon == '3' and image != blank -%}
                          <img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image | image_url: width: 50 }}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt }}">
                      {%- else -%}
                          <i class="bee-ani-{{ bk_stts.ani }} {{ bk_stts.icon_name }}"></i>
                      {%- endif -%}
                      {%- if bk_stts.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}
                  {%- endif -%}
                  {{ bk_stts.text | replace: '[count]','<span data-count class="bee-pr__live-view bee-fwb"></span>' }}
              </div>
            {%- when 'sold' %}{% if bk_stts.text == blank or isProductAvailable == false %}{% continue %}{% endif -%}
              <div id="bee-sold{{ bk_id }}" class="bee-pr_flash_sold bee-ch bee-fwm bee-dn" data-time="120000" data-flash-sold='{ "mins": {{ bk_stts.mins }}, "maxs": {{ bk_stts.maxs }}, "mint": {{ bk_stts.mint }}, "maxt": {{ bk_stts.maxt }}, "id": "{{ product.id }}", "time": 120000 }' {{ block.shopify_attributes }}>
                  {%- if bk_stts.icon != '1' -%}{%- assign image = bk_stts.img -%}
                      {%- if bk_stts.icon == 'default' -%}
                          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svg="http://svg.com/svg" version="1.1" x="0" y="0" viewBox="0 0 48 48" xml:space="preserve" class="bee-ani-{{ bk_stts.ani }}">
                              <path d="m39.8047 20.5938-19.9287 27a1.0006 1.0006 0 0 1 -.8047.4062.98.98 0 0 1 -.3838-.0771.9983.9983 0 0 1 -.6045-1.0752l2.751-17.8477h-11.834a1 1 0 0 1 -.8047-1.5937l19.9287-27a1 1 0 0 1 1.793.746l-2.751 17.8477h11.834a1 1 0 0 1 .8047 1.5938z" fill="#ffce33" data-original="#ffce33"/>
                          </svg>
                      {%- elsif bk_stts.icon == '3' and image != blank -%}
                          <img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image | image_url: width: 50 }}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt }}">
                      {%- else -%} 
                          <i class="bee-ani-{{ bk_stts.ani }} {{ bk_stts.icon_name }}"></i>
                      {%- endif -%}
                      {%- if bk_stts.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}
                  {%- endif -%}
                  {{ bk_stts.text | replace: '[sold]','<span data-sold class="bee-pr__sold bee-fwb"></span>' | replace: '[hour]','<span data-hour class="bee-pr__hrs bee-fwb"></span>' }}
              </div>

            {%- when 'order' -%}{% unless isProductAvailable %}{% continue %}{% endunless -%}{% if isPreoder and variants_size == 1 and bk_stts.hide_pre %}{% continue %}{% endif -%}
              <div id="bee-delivery{{ bk_id }}" class="bee-pr_delivery bee-dn" data-order-delivery='{ "timezone": false, "format_day": "{{ bk_stts.frm_day }}", "mode": "{{ bk_stts.mode }}", "cut_day": {{ bk_stts.cut_day | json }}, "estimateStartDate": {{ meta_theme.estimateStartDate | default: bk_stts.ds | json }}, "estimateEndDate": {{ meta_theme.estimateEndDate | default: bk_stts.de | json }}, "time": {{ bk_stts.time | default: 19041994 | json }}, "hideWithPreorder": {{ bk_stts.hide_pre }} }' {{ block.shopify_attributes }}>
                  <div class="bee-pr_delivery_wrap bee-d-flex bee-align-items-center">
                      <div class="bee-pr_delivery_icon">
                          {%- if bk_stts.icon != '1' -%}{%- assign image = bk_stts.img -%}
                              {%- if bk_stts.icon == 'default' -%}
                              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:svg="http://svg.com/svg" version="1.1" x="0" y="0" viewBox="0 0 512 512" xml:space="preserve" class="bee-ani-{{ bk_stts.ani }}">
                                  <path d="M509.821,467.046c-3.44-4.319-9.731-5.032-14.049-1.592c-10.778,8.585-25.844,8.599-36.646,0.028l-0.149-0.119 c-18.125-14.383-43.426-14.36-61.525,0.056c-10.784,8.589-25.839,8.601-36.634,0.038c-0.017-0.014-0.033-0.027-0.05-0.04 c-18.099-14.414-43.398-14.438-61.528-0.049l-0.193,0.153c-10.772,8.551-25.823,8.551-36.597,0.002l-0.202-0.161 c-18.127-14.384-43.425-14.359-61.566,0.088c-10.779,8.586-25.846,8.598-36.645,0.028l-0.189-0.15 c-18.098-14.364-43.375-14.362-61.472,0.007l-0.187,0.148c-10.795,8.571-25.866,8.559-36.608,0.003 c-0.014-0.011-0.066-0.053-0.08-0.064c-18.103-14.419-43.402-14.441-61.536-0.051l-0.19,0.151 c-4.322,3.436-5.04,9.724-1.604,14.047c3.436,4.321,9.723,5.042,14.047,1.605l0.185-0.146c10.793-8.566,25.862-8.554,36.602,0.002    c0.014,0.011,0.066,0.054,0.08,0.064c18.106,14.422,43.412,14.443,61.541,0.047l0.187-0.148 c10.776-8.558,25.831-8.557,36.611-0.001l0.189,0.15c18.127,14.389,43.428,14.366,61.57-0.084 c10.778-8.584,25.844-8.597,36.638-0.034l0.202,0.161c9.045,7.179,19.883,10.767,30.725,10.767 c10.842-0.001,21.688-3.592,30.735-10.773l0.193-0.153c10.777-8.554,25.817-8.555,36.594-0.004 c0.016,0.013,0.032,0.026,0.048,0.039c18.133,14.441,43.46,14.44,61.592-0.002c10.779-8.585,25.844-8.598,36.647-0.027 l0.149,0.118c18.124,14.382,43.422,14.361,61.524-0.056C512.548,477.654,513.26,471.365,509.821,467.046z" fill="#000000" data-original="#000000"/>
                                  <path d="M119.616,352.882c-1.859-1.869-4.429-2.929-7.068-2.929c-2.629,0-5.208,1.06-7.068,2.929 c-1.859,1.859-2.929,4.439-2.929,7.068c0,2.629,1.071,5.208,2.929,7.068c1.859,1.859,4.439,2.929,7.068,2.929 c2.639,0,5.208-1.07,7.068-2.929c1.859-1.859,2.929-4.439,2.929-7.068C122.545,357.321,121.474,354.742,119.616,352.882z" fill="#000000" data-original="#000000"/>
                                  <path d="M70.518,430.218l-0.14-0.116c-19.685-16.312-35.255-36.913-45.568-60.158h41.711c5.52,0,9.997-4.476,9.997-9.997 s-4.477-9.997-9.997-9.997H10.149c-3.231,0-6.262,1.562-8.139,4.191c-1.876,2.63-2.365,6.004-1.315,9.059 c11.061,32.147,30.745,60.605,56.918,82.294l0.135,0.112c1.866,1.55,4.129,2.304,6.378,2.304c2.872,0,5.722-1.232,7.701-3.615 C75.353,440.046,74.766,433.743,70.518,430.218z" fill="#000000" data-original="#000000"/>
                                  <path d="M510.141,354.141c-1.876-2.629-4.908-4.191-8.139-4.191h-235.93V30.045c0-5.521-4.477-9.997-9.997-9.997 c-5.52,0-9.997,4.476-9.997,9.997V349.95h-92.473c-5.52,0-9.997,4.476-9.997,9.997s4.477,9.997,9.997,9.997h333.736 c-10.313,23.245-25.883,43.847-45.572,60.162l-0.135,0.112c-4.249,3.525-4.836,9.828-1.311,14.077 c1.978,2.383,4.828,3.614,7.7,3.614c2.249,0,4.512-0.756,6.378-2.304l0.13-0.107c26.179-21.693,45.862-50.151,56.923-82.299 C512.507,360.145,512.018,356.771,510.141,354.141z" fill="#000000" data-original="#000000"/>
                                  <path d="M303.132,158.31c-1.858-1.859-4.438-2.929-7.067-2.929c-2.629,0-5.208,1.07-7.068,2.929 c-1.86,1.859-2.929,4.439-2.929,7.068c0,2.629,1.07,5.208,2.929,7.068s4.439,2.929,7.068,2.929c2.629,0,5.208-1.07,7.067-2.929 c1.86-1.859,2.93-4.439,2.93-7.068C306.062,162.749,304.991,160.169,303.132,158.31z" fill="#000000" data-original="#000000"/>
                                  <path d="M419.938,156.19c-24.91-49.293-61.579-92.664-106.044-125.424l-11.9-8.768c-3.037-2.238-7.075-2.574-10.438-0.875 c-3.366,1.701-5.488,5.151-5.488,8.922v92.43c0,5.521,4.477,9.997,9.997,9.997s9.997-4.476,9.997-9.997V49.876 c80.071,60.897,128.77,155.68,131.81,256.088H306.062V200.433c0-5.521-4.477-9.997-9.997-9.997c-5.52,0-9.997,4.476-9.997,9.997 V315.96c0,2.651,1.054,5.194,2.928,7.069c1.875,1.874,4.418,2.928,7.069,2.928h151.953c5.52,0,9.997-4.476,9.997-9.997 C458.015,260.73,444.847,205.483,419.938,156.19z" fill="#000000" data-original="#000000"/>
                                  <path d="M218.504,70.857c-4.459-1.112-9.092,0.959-11.246,5.013L82.295,311.272c-1.643,3.098-1.545,6.833,0.263,9.839 c1.807,3.007,5.06,4.846,8.567,4.846h124.963c5.52,0,9.997-4.476,9.997-9.997V80.558 C226.085,75.966,222.958,71.965,218.504,70.857z M206.091,305.963H107.75l98.341-185.251V305.963z" fill="#000000" data-original="#000000"/>
                              </svg>
                              {%- elsif bk_stts.icon == '3' and image != blank -%}<img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image | image_url: width: 50 }}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt }}">
                              {%- else -%}<i class="bee-ani-{{ bk_stts.ani }} {{ bk_stts.icon_name }}"></i>
                              {%- endif -%}
                              {%- if bk_stts.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}
                          {%- endif -%}
                      </div>
                      <div class="bee-pr_delivery_content">
                          {%- capture hourHTML -%}<span data-hour-delivery class="bee-h_delivery bee-dn">[totalHours] {{ bk_stts.hr }} %M {{ bk_stts.min }}</span>{%- endcapture -%}
                          {{ bk_stts.txt | replace: '[hour]',hourHTML | replace: '[date_start]','<span data-start-delivery class="bee-start_delivery bee-ch bee-fwm"></span>' | replace: '[date_end]','<span data-end-delivery class="bee-end_delivery  bee-ch bee-fwm"></span>' }}
                      </div>
                  </div>
              </div>

            {%- when 'countdown' %}{% unless bk_stts.source == '1' or pr_tags contains 'has_stock_countdown' %}{% continue %}{% endunless -%}
            {{ 'bee-countdown.css' | asset_url | stylesheet_tag }}
            {%- liquid 
                assign meta = meta_theme.countdown
                assign isCountdownMeta = false
                assign isShowCountdownt = false 
                if meta != blank 
                    assign cd_date = meta | date: '%Y/%m/%d %H:%M:%S'
                    assign isCountdownMeta = true
                else
                    assign cd_date = meta_theme.countdown_day | default: bk_stts.stock_time 
                endif
                if bk_stts.source == '1' and cd_date != blank
                    assign isShowCountdownt = true 
                elsif bk_stts.source == '2' and cd_date != blank and pr_tags contains 'has_stock_countdown'
                    assign isShowCountdownt = true 
                endif
            -%}
            {%- unless isShowCountdownt %}{% continue %}{% endunless -%}
            <div data-countdown-pr data-countdown-wrap id="bee-countdown-wrap{{ bk_id }}" class="bee-countdown-pr bee-text-{{ bk_stts.al }} bee-dn" {{ block.shopify_attributes }}>
                {%- if bk_stts.mess != blank -%}
                    <p class="bee-countdown__mess bee-lh-1 bee-fwm" style="font-size: {{ bk_stts.size }}px;margin-bottom: 20px;color: {{ bk_stts.color }};">
                    {%- if bk_stts.icon != '1' -%}{%- assign image = bk_stts.img -%}
                    {%- if bk_stts.icon == '3' and image != blank -%}<img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image | image_url: width: 50 }}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt }}">
                    {%- else -%}<i class="bee-ani-{{ bk_stts.ani }} {{ bk_stts.icon_name }}"></i>
                    {%- endif -%}
                    {%- if bk_stts.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}{%- endif -%}{{ bk_stts.mess }}</p>
                {%- endif -%}
                <div id="bee-countdow{{ bk_id }}" class="bee-countdown-wrap bee-d-inline-flex bee-justify-content-center bee-justify-content-between bee-lh-1 bee-countdown-des-{{ bk_stts.cdt_des }} bee-countdown bee-countdown-shadow-{{ bk_stts.countdown_shadow }} bee-countdown-size-{{ bk_stts.cdt_size }} "{% if isCountdownMeta %} data-date="{{ cd_date }}" data-loop="{% if bk_stts.dayx > 0 %}true{% else %}false{% endif %}" data-dayl="{{ bk_stts.dayx }}"{% endif %} data-cd-options='{ "isCountdownMeta": {{ isCountdownMeta }},"cd_date": {{ cd_date | json }} }' {%- render 'bk_cus_style',type: 'countdown', bk_stts: bk_stts -%}>
                    <div class="bee-time">
                        <span class="bee-cd__time bee-d-flex bee-text-center is--days"><span class="bee-cd__count cd-number">%-D</span><span class="bee-cd__label cd-text">%!D:{{ 'products.product_single.countdown_text.day' | t }},{{ 'products.product_single.countdown_text.day_plural' | t }};</span></span>
                        <span class="bee-cd__time bee-d-flex bee-text-center is--hours"><span class="bee-cd__count cd-number">%-H</span><span class="bee-cd__label cd-text">%!H:{{ 'products.product_single.countdown_text.hr' | t }},{{ 'products.product_single.countdown_text.hr_plural' | t }};</span></span>
                        <span class="bee-cd__time bee-d-flex bee-text-center is--minutes"><span class="bee-cd__count cd-number">%-M</span><span class="bee-cd__label cd-text">%!M:{{ 'products.product_single.countdown_text.min' | t }},{{ 'products.product_single.countdown_text.min_plural' | t }};</span></span>
                        <span class="bee-cd__time bee-d-flex bee-text-center is--seconds"><span class="bee-cd__count cd-number">%-S</span><span class="bee-cd__label cd-text">%!S:{{ 'products.product_single.countdown_text.sec' | t }},{{ 'products.product_single.countdown_text.sec_plural' | t }};</span></span>
                    </div>
                </div>
            </div>

            {%- when 'inventory_qty' %}{% if bk_stts.mess == blank or isProductAvailable == false %}{% continue %}{% endif -%}
            {%- assign arr_mess = bk_stts.mess | split: '[stock_number]' -%}
            <div id="bee-stock{{ bk_id }}" class="bee-inventory_qty bee-text-{{ bk_stts.al }}" data-inventory-qty='{ "reduce": {{ bk_stts.reduce | default: false }}, "inventoryQty": {{ inventory_quantity | default: 0 }}, "id": {{ current_variant.id | default: product.id }}, "stock": "{{ bk_stts.stock }}", "qty": {{ bk_stts.qty }}, "total": {{ bk_stts.total_items }}, "min": {{ bk_stts.stock_from }}, "max": {{ bk_stts.stock_to }}, "bgprocess": "{{ bk_stts.stock_bg_process }}", "bgten": "{{ bk_stts.bgten }}" }' data-prid="{{ product.id }}" {{ block.shopify_attributes }}>
                <p data-message class="bee-inventory_message bee-dn bee-ch bee-lh-1 bee-fwm" style="font-size: {{ bk_stts.size }}px">
                {%- if bk_stts.icon != '1' -%}{%- assign image = bk_stts.img -%}
                    {%- if bk_stts.icon == '3' and image != blank -%}<img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image | image_url: width: 50 }}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt }}">
                    {%- else -%}
                        <i class="bee-ani-{{ bk_stts.ani }} {{ bk_stts.icon_name }}"></i>
                    {%- endif -%}
                {%- if bk_stts.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}
                {%- endif -%}
                {{ arr_mess[0] }} <span data-count class='bee-count'></span> {{ arr_mess[1] }}</p>
                {%- if bk_stts.progress -%}
                <div data-progressbar class="bee-inventory_progressbar bee-progress__bar bee-pr bee-oh bee-dn" style="background-color: {{ bk_stts.stock_bg }};width: {{ bk_stts.wbar }}%"><div style="background-color: {{ bk_stts.stock_bg_process }};width: 100%;"></div></div>
                {%- endif -%}
            </div>

            {%- when 'store_pickup' %}
              {{ 'pickup-availability.css' | asset_url | stylesheet_tag }}
              <div class="bee-pr__pickup-availability-container bee-dn-" data-variant-id="{{ current_variant.id }}" data-pickup-availability-container data-has-only-default-variant="{{ isProductDefault }}" data-root-url="{{ routes.root_url }}" data-id-popup="popup{{ pr_sid }}"></div>

            {%- when 'tab_des' or 'tab_add' or 'tab_buy' or 'tab_rivui' or 'tab_liquid' or 'tab_html' -%}
              {%- if tabCheck or tabs_position == 'external' %}{% continue %}{% endif -%}
              {%- assign tabCheck = true -%}
              {%- render 'product_tabs', product: product, se_stts: se_stts, seBlocks: seBlocks, idTabDes: idTabDes, idTabReview: idTabReview, isProductDefault: isProductDefault -%}
        
            {%- when 'html' %}{% if bk_stts.page.content == blank %}{% continue %}{% endif -%}
              <div class="bee-custom_{{ bk_id }} bee-pr__html bee-rte" {{ block.shopify_attributes }}>{{ bk_stts.page.content }}</div>

              {%- when 'text' -%}{% if bk_stts.text == blank %}{% continue %}{% endif -%}
              <div class="bee-richtext_{{ bk_id }} bee-pr__richtext" {{ block.shopify_attributes }}>{{ bk_stts.text }}</div>

            {%- when 'custom_liquid' -%}
              <div class="bee-liquid_{{ bk_id }} bee-pr__custom-liquid" {{ block.shopify_attributes }}>{{ bk_stts.custom_liquid }}</div>

            {%- when '@app' -%}{%- render block -%}
          {%- endcase -%}
        {%- endfor -%}
      </div>
    </div>
  </div> 
</div>

{{- arrTemp[1] -}}


{% schema %}
  {
    "name": "Product Quickview",
    "tag": "section",
    "class": "bee-section bee-section-main bee-section-main-product bee_tp_flickity bee-section-admn2-fixed",
    "settings": [
      {
        "type": "product",
        "id": "demo_pr",
        "label": "Choose a product demo",
        "info": "Product only shown on admin editor"
      },
      {
        "type": "header",
        "content": "+ Media"
      },

      {
          "type": "checkbox",
          "id": "enable_wishlist",
          "label": "Enable wishlist",
          "default": true
      },
      {
          "type": "checkbox",
          "id": "enable_compare",
          "label": "Enable compare",
          "default": true
      },
      {
          "type": "checkbox",
          "id": "enable_view_details",
          "label": "Enable 'View details'",
          "default": true
      },
      {
        "type": "paragraph",
        "content": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
      },
      {
        "type": "select",
        "id": "image_ratio",
        "label": "Media ratio",
        "default": "ratioadapt",
        "info": "Aspect ratio custom will settings in general panel",
        "options": [
          {
            "group": "Natural",
            "value": "ratioadapt",
            "label": "Adapt to media"
          },
          {
            "group": "Landscape",
            "value": "ratio2_1",
            "label": "2:1"
          },
          {
            "group": "Landscape",
            "value": "ratio16_9",
            "label": "16:9"
          },
          {
            "group": "Landscape",
            "value": "ratio8_5",
            "label": "8:5"
          },
          {
            "group": "Landscape",
            "value": "ratio3_2",
            "label": "3:2"
          },
          {
            "group": "Landscape",
            "value": "ratio4_3",
            "label": "4:3"
          },
          {
            "group": "Landscape",
            "value": "rationt",
            "label": "Ratio ASOS"
          },
          {
            "group": "Squared",
            "value": "ratio1_1",
            "label": "1:1"
          },
          {
            "group": "Portrait",
            "value": "ratio2_3",
            "label": "2:3"
          },
          {
            "group": "Portrait",
            "value": "ratio1_2",
            "label": "1:2"
          },
          {
            "group": "Custom",
            "value": "ratiocus1",
            "label": "Ratio custom 1"
          },
          {
            "group": "Custom",
            "value": "ratiocus2",
            "label": "Ratio custom 2"
          },
          {
            "group": "Custom",
            "value": "ratiocus3",
            "label": "Ratio custom 3"
          },
          {
            "group": "Custom",
            "value": "ratiocus4",
            "label": "Ratio custom 4"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_size",
        "label": "Media ratio size",
        "default": "cover",
        "info": "This settings apply only if the media ratio is not set to 'Adapt to media'",
        "options": [
          {
            "value": "cover",
            "label": "Full"
          },
          {
            "value": "contain",
            "label": "Auto"
          }
        ]
      },
      {
        "type": "select",
        "id": "image_position",
        "label": "Media position",
        "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the media ratio is not set to 'Adapt to media'",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "1",
            "label": "Left top"
          },
          {
            "value": "2",
            "label": "Left center"
          },
          {
            "value": "3",
            "label": "Left bottom"
          },
          {
            "value": "4",
            "label": "Right top"
          },
          {
            "value": "5",
            "label": "Right center"
          },
          {
            "value": "6",
            "label": "Right bottom"
          },
          {
            "value": "7",
            "label": "Center top"
          },
          {
            "value": "8",
            "label": "Center center"
          },
          {
            "value": "9",
            "label": "Center bottom"
          }
        ],
        "default": "8"
      },
      {
        "type": "checkbox",
        "id": "enable_video_looping",
        "default": false,
        "label": "Enable video looping"
      },
      {
        "type": "checkbox",
        "id": "enable_video_muting",
        "default": true,
        "label": "Enable video muting"
      },
      {
        "type": "checkbox",
        "id": "enable_video_autoplaying",
        "default": true,
        "label": "Enable video autoplaying",
        "info": "Only working when has slider and on desktop"
      },
      {
        "type": "header",
        "content": "+ Product slider"
      },
      {
        "type": "select","id": "eff", "default": "fade",
        "label": "Slider effect","info": "Effect between transitioning slides",
        "options": [
          {
            "value": "slide","label": "Slide"
          },
          {
            "value": "fade","label": "Fade"
          }
        ]
      },
      {
        "type": "select",
        "id": "show_btn",
         "options": [
          {
            "value": "show_all",
            "label": "Show all screen"
          },
          {
            "value": "show_desktop",
            "label": "Only show on desktop"
          },
          {
            "value": "show_mobile",
            "label": "Only show on tablet & mobile"
          },
          {
            "value": "hidden",
            "label": "Hidden"
          }
        ],
        "label": "Use prev next button",
        "default": "hidden"
      },
      {
        "type": "select",
        "id": "btn_vi",
        "label": "Visible",
        "default": "always",
        "options": [
          {
            "value": "always",
            "label": "Always"
          },
          {
            "value": "hover",
            "label": "Only hover"
          }
        ]
      },

      {
        "type": "select",
        "id": "btn_owl",
        "label": "Button style",
        "default": "simple",
        "options": [
          {
            "value": "default",
            "label": "Default"
          },
          {
            "value": "outline",
            "label": "Outline"
          },
          {
            "value": "simple",
            "label": "Simple"
          }
        ]
      },
      {
        "type": "select",
        "id": "btn_shape",
        "label": "Button shape",
        "info": "Not work for 'Simple' button style",
        "default": "round",
        "options": [
          {
            "value": "none",
            "label": "Default"
          },
          {
            "value": "round",
            "label": "Round"
          },
          {
            "value": "rotate",
            "label": "Rotate"
          }
        ]
      },
      {
          "type": "select",
          "id": "btn_cl",
          "label": "Button color",
          "default": "dark",
          "options": [
              {
                  "value": "light",
                  "label": "Light"
              },
              {
                  "value": "dark",
                  "label": "Dark"
              },
              {
                  "value": "primary",
                  "label": "Primary"
              },
              {
                  "value": "custom1",
                  "label": "Custom color 1"
              },
              {
                  "value": "custom2",
                  "label": "Custom color 2"
              },
              {
                  "value": "custom3",
                  "label": "Custom color 3"
              }
          ]
      },
      {
        "type": "select",
        "id": "btn_size",
        "label": "Button size",
        "default": "small",
        "options": [
          {
            "value": "small",
            "label": "Small"
          },
          {
            "value": "medium",
            "label": "Medium"
          },
          {
            "value": "large",
            "label": "Large"
          }
        ]
      },
      {
            "type": "paragraph",
            "content": "—————————————————"
        },
        {
          "type": "select",
          "id": "show_dots",
          "info": "Creates and show page dots",
           "options": [
            {
              "value": "show_all",
              "label": "Show all screen"
            },
            {
              "value": "show_desktop",
              "label": "Only show on desktop"
            },
            {
              "value": "show_mobile",
              "label": "Only show on tablet & mobile"
            },
            {
              "value": "hidden",
              "label": "Hidden"
            }
          ],
          "label": "Use carousel's dots",
          "default": "show_all"
        },
        {
            "type": "select",
            "id": "dot_owl", 
            "label": "Dots style",
            "default": "default",
            "options": [
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "background-active",
                    "label": "Background Active"
                },
                {
                    "value": "dots_simple",
                    "label": "Dots simple"
                },
                {
                    "value": "elessi",
                    "label": "Elessi"
                },
                {
                    "value": "br-outline",
                    "label": "Outline"
                },
                {
                    "value": "outline-active",
                    "label": "Outline active"
                },
                {
                    "value": "dots-number",
                    "label": "Dots number" 
                }
            ]
        },
        {
            "type": "select",
            "id": "dots_cl",
            "label": "Dots color",
            "default": "dark",
            "options": [
                {
                    "value": "light",
                    "label": "Light (Best on dark background)"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                },
                {
                    "value": "custom3",
                    "label": "Custom color 3"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "dots_round",
            "label": "Enable round dots",
            "default": true
        },
        {
            "type": "range",
            "id": "dots_space",
            "min": 2,
            "max": 20,
            "step": 1,
            "label": "Space among dots",
            "unit": "px",
            "default": 20
        }
    ],
    "blocks": [
      {
        "type": "@app"
      },
      {
          "type": "title",
          "name": "Product title",
          "limit": 1,
          "settings": [
              {
                  "type": "select",
                  "id": "txt_tr_pr",
                  "label": "Transform text",
                  "default": "none",
                  "options": [
                      {
                          "value": "none",
                          "label": "None"
                      },
                      {
                          "value": "lowercase",
                          "label": "Lowercase"
                      },
                      {
                          "value": "capitalize",
                          "label": "Capitalize"
                      },
                      {
                          "value": "uppercase",
                          "label": "Uppercase"
                      }
                  ]
              },
              {
                  "type": "select",
                  "id": "fnt_df_pr",
                  "label": "Font family",
                  "default": "1",
                  "options": [
                      {
                          "value": "1",
                          "label": "Font family #1"
                      },
                      {
                          "value": "2",
                          "label": "Font family #2"
                      },
                      {
                          "value": "3",
                          "label": "Font family #3"
                      }
                  ]
              },
              {
                  "type": "range",
                  "id": "size_pr",
                  "min": 10,
                  "max": 60,
                  "step": 0.5,
                  "label": "Font size",
                  "unit": "px",
                  "default": 16
              },
              {
                  "type": "range",
                  "id": "fw_pr",
                  "min": 300,
                  "max": 900,
                  "step": 100,
                  "label": "Font weight",
                  "default": 600
              },
              {
                  "type": "range",
                  "id": "lh_pr",
                  "label": "Line height",
                  "max": 100,
                  "min": 0,
                  "step": 1,
                  "default": 0,
                  "unit": "px",
                  "info": "Set '0' to use default"            
              },
              {
                  "type": "range",
                  "id": "ls_pr",
                  "max": 5,
                  "min": -5,
                  "step": 0.1,
                  "label": "Letter spacing",
                  "info": "set is '0' use to default",
                  "unit": "px",
                  "default": 0
              },
              
              {
                "type": "header",
                "content": "+ OPTION TABLET"
              },
              {
                  "type": "range",
                  "id": "size_pr_tb",
                  "min": 10,
                  "max": 60,
                  "step": 0.5,
                  "label": "Font size",
                  "unit": "px",
                  "default": 24
              },
              {
                  "type": "range",
                  "id": "lh_pr_tb",
                  "label": "Line height",
                  "max": 100,
                  "min": 0,
                  "step": 1,
                  "default": 0,
                  "unit": "px",
                  "info": "Set '0' to use default"            
              },
              {
                  "type": "range",
                  "id": "ls_pr_tb",
                  "max": 5,
                  "min": -5,
                  "step": 0.1,
                  "label": "Letter spacing",
                  "info": "set is '0' use to default",
                  "unit": "px",
                  "default": 0
              },
               {
                "type": "header",
                "content": "+ OPTION MOBILE"
              },
              {
                  "type": "range",
                  "id": "size_pr_mb",
                  "min": 10,
                  "max": 60,
                  "step": 0.5,
                  "label": "Font size",
                  "unit": "px",
                  "default": 24
              },
              {
                  "type": "range",
                  "id": "lh_pr_mb",
                  "label": "Line height",
                  "max": 100,
                  "min": 0,
                  "step": 1,
                  "default": 0,
                  "unit": "px",
                  "info": "Set '0' to use default"            
              },
              {
                  "type": "range",
                  "id": "ls_pr_mb",
                  "max": 5,
                  "min": -5,
                  "step": 0.1,
                  "label": "Letter spacing",
                  "info": "set is '0' use to default",
                  "unit": "px",
                  "default": 0
              },
              {
                  "type": "color",
                  "id": "pr_title_color",
                  "label": "Product title color",
                  "default": "#222"
              },
              {
                  "type": "color",
                  "id": "pr_title_hover_color",
                  "label": "Product title hover color",
                  "default": "#0ec1ae"
              }
          ]
      },
        {
            "type": "price",
            "name": "Product price",
            "limit": 1,
            "settings": [
                {
                    "type": "select",
                    "id": "price",
                    "label": "Price varies settings",
                    "default": "0",
                    "options": [
                        {
                            "value": "0",
                            "label": "None"
                        },
                        {
                            "value": "1",
                            "label": "$39.00 – $59.00"
                        },
                        {
                            "value": "2",
                            "label": "From $39.00"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "type_sale",
                    "label": "Save badge type",
                    "default": "0",
                    "options": [
                        {
                            "value": "0",
                            "label": "None"
                        },
                        {
                            "value": "1",
                            "label": "Percentage"
                        },
                        {
                            "value": "2",
                            "label": "Fixed amount"
                        }
                    ]
                },
                {
                    "type": "checkbox",
                    "id": "tax_ship",
                    "label": "Show tax and shipping information",
                    "default": true
                },
                {
                    "type": "range",
                    "id": "size_price_pr",
                    "min": 10,
                    "max": 50,
                    "step": 0.5,
                    "label": "Price size",
                    "unit": "px",
                    "default": 22
                },
                {
                    "type": "range",
                    "id": "fw_price_pr",
                    "min": 300,
                    "max": 800,
                    "step": 100,
                    "label": "Font weight",
                    "default": 400
                },
                {
                    "type": "color",
                    "id": "primary_price_color",
                    "label": "Primary price color",
                    "default": "#000"
                },
                {
                    "type": "color",
                    "id": "secondary_price_color",
                    "label": "Secondary price color",
                    "default": "#868686"
                },
                {
                    "type": "color",
                    "id": "price_sale",
                    "label": "Price sale color",
                    "default": "#fa0000"
                }
            ]
        },
        {
          "type": "review",
          "name": "Product review",
          "limit": 1,
          "settings": [
              {
                  "type": "checkbox",
                  "id": "rating",
                  "label": "Use rating",
                  "info": "Only works when 'Enable rating' ",
                  "default": true
              },
              {
                  "type": "liquid",
                  "id": "review_liquid",
                  "label": "Add Snippets Liquid",
                  "info": "Add app snippets reviews to show star rating on product page. Will working when you use 'Other app review'"
              }
          ]
      },
      {
          "type": "line",
          "name": "Product line",
          "settings": [
              {
                  "type": "color",
                  "id": "color_line",
                  "label": "Color line",
                  "default": "#e9e9e9"
              },
              {
                  "type": "range",
                  "id": "height_line",
                  "label": "Height",
                  "min": 1,
                  "max": 5,
                  "default": 1,
                  "step": 0.5,
                  "unit": "px"
              },
              {
                  "type": "range",
                  "id": "space_tb",
                  "label": "Bottom space",
                  "min": 1,
                  "max": 20,
                  "default": 10,
                  "step": 0.5,
                  "unit": "px"
              },
              {
                  "type": "range",
                  "id": "space_tb_mb",
                  "label": "Bottom space (Mobile)",
                  "min": 1,
                  "max": 20,
                  "default": 10,
                  "step": 0.5,
                  "unit": "px"
              }
          ]
      },
      {
          "type": "description",
          "name": "Product description",
          "limit": 1,
          "settings": [
              {
                  "type": "select",
                  "id": "des",
                  "options": [
                      {
                          "value": "1",
                          "label": "Full description"
                      },
                      {
                          "value": "2",
                          "label": "Short description"
                      }
                  ],
                  "label": "Description mode",
                  "info": "If you want to show full HTML of the Product Description, please select \"Full Description\".",
                  "default": "2"
              },
              {
                  "type": "header",
                  "content": "Short description configs"
              },
              {
                  "type": "richtext",
                  "id": "text",
                  "label": "Short description",
                  "info": "Short description that will be displayed for each product content if you don't set metafield excerpt for each product content."
              },
              {
                  "type": "number",
                  "id": "length",
                  "label": "Excerpt length (integer)",
                  "info": "Number of words that will be displayed for each product content if you don't set short description page or set metafield excerpt for each product content.",
                  "default": 31
              },
              {
                  "type": "checkbox",
                  "id": "readm",
                  "label": "Use read more",
                  "default": false
              },
              {
                  "type": "text",
                  "id": "readm_txt",
                  "label": "Read more label",
                  "default": "Read more"
              }
          ]
      },
      {
        "type": "form",
        "name": "Product form",
        "limit": 1,
        "settings": [
        {
            "type": "header",
            "content": "+ Product Swatch"
        },
        {
            "type": "select",
            "id": "selector_mode",
            "label": "Selector type",
            "options": [
                {
                    "value": "circle",
                    "label": "Circle"
                },
                {
                    "value": "radio",
                    "label": "Radio"
                },
                {
                    "value": "radio is-sw__full",
                    "label": "Radio full"
                },
                {
                    "value": "block",
                    "label": "Block"
                },
                {
                    "value": "block-round1",
                    "label": "Block round"
                },
                {
                    "value": "block-round2",
                    "label": "Block round 2"
                },
                {
                    "value": "dropdown",
                    "label": "Dropdown"
                }
            ],
            "default": "block"
        },
        {
            "type": "select",
            "id": "color_mode",
            "label": "Color selector type",
            "options": [
                {
                    "value": "circle",
                    "label": "Circle"
                },
                {
                    "value": "radio",
                    "label": "Radio"
                },
                {
                    "value": "radio is-sw-cl__full",
                    "label": "Radio full"
                },
                {
                    "value": "block",
                    "label": "Block"
                },
                {
                    "value": "block2",
                    "label": "Block round"
                },
                {
                    "value": "dropdown",
                    "label": "Dropdown"
                },
                {
                    "value": "color",
                    "label": "Color swatch"
                },
                {
                    "value": "color is-sw-cl__round",
                    "label": "Color swatch round"
                },
                {
                    "value": "color is-sw-cl__round is-sw-cl__label",
                    "label": "Color swatch round with label"
                },
                {
                    "value": "variant_image",
                    "label": "Variant image"
                },
                {
                    "value": "variant_image is-sw-cl__round",
                    "label": "Variant image round"
                }
            ],
            "default": "color"
        },
        {
            "type": "checkbox",
            "id": "enable_fit_ratio_img",
            "label": "Enable adapt to first swatch image variant",
            "default": false
        },
        {
            "type": "select",
            "id": "color_size",
            "options": [
                {
                    "value": "small",
                    "label": "Small"
                },
                {
                    "value": "medium",
                    "label": "Medium"
                },
                {
                    "value": "large",
                    "label": "Large"
                },
                {
                    "value": "exlarge",
                    "label": "Extra Large"
                }
            ],
            "label": "Color selector size",
            "info": "Only working with color swatch, variant image",
            "default": "medium"
        },
        {
            "type": "checkbox",
            "id": "show_qty",
            "label": "Show quantity selector",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "btn_atc_full",
            "label": "Enable button full width",
            "default": false
        },
        {
            "type": "range",
            "id": "pr_btn_round",
            "min": 0,
            "max": 40,
            "step": 1,
            "label": "Button round corners",
            "unit": "px",
            "default": 40
        }, 
        {
            "type": "range",
            "id": "wis_cp_btn_round",
            "min": 0,
            "max": 40,
            "step": 1,
            "label": "Wishlist and compare button round corners",
            "unit": "px",
            "default": 40
        },
        {
            "type": "header",
            "content": "+ Add to cart button"
        },
        {
            "type": "select",
            "id": "ani",
            "options": [
            {
                "value": "none",
                "label": "None"
            },
            {
                "value": "bee-ani-bounce",
                "label": "Bounce"
            },
            {
                "value": "bee-ani-tada",
                "label": "Tada"
            },
            {
                "value": "bee-ani-swing",
                "label": "Swing"
            },
            {
                "value": "bee-ani-flash",
                "label": "Flash"
            },
            {
                "value": "bee-ani-fadeIn",
                "label": "FadeIn"
            },
            {
                "value": "bee-ani-heartBeat",
                "label": "HeartBeat"
            },
            {
                "value": "bee-ani-shake",
                "label": "Shake"
            }
            ],
            "label": "Add to cart animation"
        },
        {
            "type": "range",
            "id": "time",
            "min": 2,
            "max": 40,
            "step": 1,
            "label": "Loop time (seconds)",
            "info": "Loop time add to cart animation",
            "unit": "s",
            "default": 6
        },
        {
            "type": "select",
            "id": "btn_txt",
            "default": "3",
            "options": [
                {
                    "value": "0",
                    "label": "None"
                },
                {
                    "value": "1",
                    "label": "Lowercase"
                },
                {
                    "value": "2",
                    "label": "Capitalize"
                },
                {
                    "value": "3",
                    "label": "Uppercase"
                }
            ],
            "label": "Button transform text"
        },
        {
            "type": "checkbox",
            "id": "btn_icon",
            "label": "Enable button icon",
            "default": false
        },
        {
            "type": "select",
            "id": "button_style",
            "label": "Button style",
            "options": [
                {
                    "label": "Default",
                    "value": "default"
                },
                {
                    "label": "Outline",
                    "value": "outline"
                },
                {
                    "label": "Border bottom",
                    "value": "bordered"
                },
                {
                    "label": "Link",
                    "value": "link"
                }
            ]
        },
        {
            "type": "select",
            "id": "button_color",
            "label": "Button color",
            "default": "dark",
            "options": [
                {
                    "value": "light",
                    "label": "Light"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                },
                {
                    "value": "custom3",
                    "label": "Custom color 3"
                }
            ]
        },
        {
            "type": "select",
            "id": "button_effect",
            "label": "Hover button effect",
            "default": "fade",
            "info": "Only working button style default, outline",
            "options": [
                {
                    "label": "Default",
                    "value": "default"
                },
                {
                    "label": "Fade",
                    "value": "fade"
                },
                {
                    "label": "Rectangle out",
                    "value": "rectangle-out"
                },
                {
                    "label": "Sweep to right",
                    "value": "sweep-to-right"
                },
                {
                    "label": "Sweep to left",
                    "value": "sweep-to-left"
                },
                {
                    "label": "Sweep to bottom",
                    "value": "sweep-to-bottom"
                },
                {
                    "label": "Sweep to top",
                    "value": "sweep-to-top"
                },
                {
                    "label": "Shutter out horizontal",
                    "value": "shutter-out-horizontal"
                },
                {
                    "label": "Outline",
                    "value": "outline"
                },
                {
                    "label": "Shadow",
                    "value": "shadow"
                }
            ]
        },
        {
            "type": "header",
            "content": "+ Dynamic checkout buttons"
        },
        {
            "type": "checkbox",
            "id": "show_dynamic_checkout",
            "label": "Show dynamic checkout buttons",
            "info": "Using the payment methods available on your store, customers see their preferred option, like PayPal or Apple Pay. [Learn more](https:\/\/help.shopify.com\/manual\/using-themes\/change-the-layout\/dynamic-checkout)",
            "default": false
        },
        {
            "type": "select",
            "id": "btn_txt2",
            "default": "3",
            "options": [
            {
                "value": "0",
                "label": "None"
            },
            {
                "value": "1",
                "label": "Lowercase"
            },
            {
                "value": "2",
                "label": "Capitalize"
            },
            {
                "value": "3",
                "label": "Uppercase"
            }
            ],
            "label": "Button transform text"
        },
        {
            "type": "select",
            "id": "button_color_payment",
            "label": "Button color",
            "default": "dark",
            "options": [
                {
                    "value": "light",
                    "label": "Light"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                },
                {
                    "value": "custom3",
                    "label": "Custom color 3"
                }
            ]
        },
        {
          "type": "header",
          "content": "+ Grouped Product"
        },
        {
          "type": "product_list",
          "id": "product_list",
          "label": "Products",
          "limit": 10
        },
        {
          "type": "checkbox",
          "id": "show_product_current",
          "label": "Show product current",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_text_price",
          "label": "Show subtotal price",
          "default": true
        },
        {
          "type": "select",
          "id": "qty_val",
          "label": "Quantity value default",
          "default": "0",
          "options": [
              {
                  "value": "0",
                  "label": "0"
              },
              {
                  "value": "1",
                  "label": "1"
              }
          ]
        },
        {
          "type": "header",
          "content": "+ Advance Product Type"
        },
        {
         "type": "paragraph",
         "content": "Not working if enabled grouped product."
        },
        {
          "type": "text",
          "id": "advance_label",
          "label": "Label",
          "default": "Choose style"
        },
        {
          "type": "product_list",
          "id": "advance_pr_list",
          "label": "Products",
          "limit": 10
        }
        ]
      },
      {
          "type": "size",
          "name": "Product size chart",
          "limit": 1,
          "settings": [
              {
                  "type": "header",
                  "content": "+ Size chart"
              },
              {
                  "type": "select",
                  "id": "size_chart",
                  "label": "Use size chart",
                  "default": "3",
                  "options": [
                      {
                          "value": "1",
                          "label": "None"
                      },
                      {
                          "value": "2",
                          "label": "Only product has option name 'size'"
                      },
                      {
                          "value": "3",
                          "label": "All product"
                      }
                  ]
              },
              {
                  "type": "select",
                  "id": "pos_sizeg",
                  "label": "Position show size chart",
                  "default": "1",
                  "options": [
                      {
                          "value": "1",
                          "label": "Default"
                      },
                      {
                          "value": "2",
                          "label": "Show on swatch option name 'size'"
                      }
                  ]
              },
              {
                  "type": "select",
                  "id": "sc_type",
                  "default": "1",
                  "options": [
                      {
                          "value": "1",
                          "label": "HTML"
                      },
                      {
                          "value": "2",
                          "label": "IMAGE"
                      }
                  ],
                  "label": "Size chart type"
              },
              {
                  "type": "page",
                  "id": "page",
                  "label": "HTML size chart",
                  "info": "This page content will appear."
              },
              {
                  "type": "image_picker",
                  "id": "image",
                  "label": "Image size chart"
              },
              {
                  "type": "textarea",
                  "id": "size_ck",
                  "label": "Enter option name you want has size guide",
                  "info": "Eg: size,sizes,Größe"
              },
              {
                  "type": "header",
                  "content": "+ Icon size chart"
              },
              {
                  "type": "select",
                  "id": "icon",
                  "label": "ICON / IMG",
                  "default": "default",
                  "options": [
                      {
                          "value": "1",
                          "label": "None"
                      },
                      {
                          "value": "default",
                          "label": "Default"
                      },
                      {
                          "value": "2",
                          "label": "Icon"
                      },
                      {
                          "value": "3",
                          "label": "Image"
                      }
                  ]
              },
              {
                  "type": "text",
                  "id": "icon_name",
                  "label": "Icon class name",
                  "default": "las la-ruler-horizontal",
                  "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
              },
              {
                  "type": "image_picker",
                  "id": "img",
                  "label": "Image icon",
                  "info": "50x50 recommend"
              },
              {
              "type": "select",
              "id": "ani",
              "options": [
                  {
                      "value": "none",
                      "label": "None"
                  },
                  {
                      "value": "bounce is--infinite",
                      "label": "Bounce"
                  },
                  {
                      "value": "tada is--infinite",
                      "label": "Tada"
                  },
                  {
                      "value": "swing is--infinite",
                      "label": "Swing"
                  },
                  {
                      "value": "flash is--infinite",
                      "label": "Flash"
                  },
                  {
                      "value": "fadeIn is--infinite",
                      "label": "FadeIn"
                  },
                  {
                      "value": "heartBeat is--infinite",
                      "label": "HeartBeat"
                      },
                  {
                      "value": "shake is--infinite",
                      "label": "Shake"
                  }
              ],
              "label": "ICON / IMG animation"
              }
          ]
      },
      {
          "type": "delivery_ask",
          "name": "Delivery, Ask",
          "limit": 1,
          "settings": [
              {
                  "type": "header",
                  "content": "+ Delivery & Return"
              },
              {
                  "type": "checkbox",
                  "id": "delivery",
                  "label": "Use delivery & return",
                  "default": false
              },
              {
                  "type": "page",
                  "id": "page_dr",
                  "label": "Add page delivery & return",
                      "info": "This page content will appear."
              },
              {
                  "type": "header",
                  "content": "+ Ask a question"
              },
              {
                  "type": "checkbox",
                  "id": "ask",
                  "label": "Show ask a question",
                  "default": false
              },
              {
              "type": "checkbox",
              "id": "phone",
              "label": "Show input phone",
              "default": true
              }
          ]
      },
      {
          "type": "meta",
          "name": "Product meta",
          "limit": 1,
          "settings": [
              {
                  "type": "checkbox",
                  "id": "pr_meta_horizontal",
                  "label": "Design product meta horizontal",
                  "default": false
              },
              {
                  "type": "checkbox",
                  "id": "show_options",
                  "label": "Show product options",
                  "default": true
              },
              {
                  "type": "checkbox",
                  "id": "show_vendor",
                  "label": "Show product vendor",
                  "default": false
              },
              {
                  "type": "checkbox",
                  "id": "show_type",
                  "label": "Show product type",
                  "default": false
              },
              {
                  "type": "checkbox",
                  "id": "show_sku",
                  "label": "Show sku",
                  "default": true
              },
              {
                  "type": "checkbox",
                  "id": "show_barcode",
                  "label": "Show barcode",
                  "default": false
              },
              {
                  "type": "checkbox",
                  "id": "show_available",
                  "label": "Show available",
                  "default": true
              },
              {
                  "type": "checkbox",
                  "id": "show_category",
                  "label": "Show collection product",
                  "default": true
              },
              {
                  "type": "checkbox",
                  "id": "show_tags",
                  "label": "Show product's tags",
                  "default": true
              }
          ]
      },
      {
          "type": "socials",
          "name": "Product socials",
          "limit": 1,
          "settings": [
              {
                  "type": "text",
                  "id": "title", 
                  "label": "Title",
                  "default": "Share"
              },
              {
                  "type": "select",
                  "id": "social_mode",
                  "label": "Socials mode",
                  "options": [
                      {
                          "value": "1",
                          "label": "Follow"
                      },
                      {
                          "value": "2",
                          "label": "Share"
                      }
                  ],
              "default": "2"
              },
              {
                  "type": "select",
                  "id": "social_style",
                  "label": "Socials style",
                  "options": [
                      { "value": "default", "label": "Default"},
                      { "value": "outline", "label": "Outline"},
                      { "value": "simple", "label": "Simple"}
                  ],
                  "default": "simple"
              },
              {
                  "type": "select",
                  "id": "social_size",
                  "label": "Socials size",
                  "options": [
                      { "value": "small", "label": "Small"},
                      { "value": "medium", "label": "Medium"},
                      { "value": "large", "label": "Large"}
                  ],
                  "default": "medium"
              },
              {
                  "type": "range",
                  "id": "bd_radius", 
                  "label": "Border radius",
                  "info": "Not work when socials style is \"Simple\"",
                  "unit": "px",
                  "min": 0,
                  "max": 30,
                  "default": 0,
                  "step": 1
              },
              {
                  "type": "checkbox",
                  "id": "use_color_set",
                  "label": "Use color settings",
                  "info": "Default is themes color, tick here if you want custom color for icon socials.",
                  "default": false
              },
              {
                  "type": "header",
                  "content": "only true when check to box Color Settings"
              },
              {
                  "type": "color",
                  "id": "pri_cl",
                  "label": "Primary color",
                  "default": "#878787"
              },
              {
                  "type": "color",
                  "id": "pri_hover_cl",
                  "label": "Primary hover color",
                  "default": "#222222"
              },
              {
              "type": "select",
              "id": "space_h_item",
              "options": [
                  {
                      "value": "0", 
                      "label": "0"
                  },
                  {
                      "value": "2", 
                      "label": "2px"
                  },
                  {
                      "value": "4", 
                      "label": "4px"
                  },
                  {
                      "value": "5", 
                      "label": "5px"
                  },
                  {
                      "value": "8", 
                      "label": "8px"
                  },
                  {
                      "value": "10", 
                      "label": "10px"
                  },
                  {
                      "value": "15",
                      "label": "15px"
                  },
                  {
                      "value": "20",
                      "label": "20px"
                  },
                  {
                      "value": "25",
                      "label": "25px"
                  },
                  {
                      "value": "30",
                      "label": "30px"
                  }
              ],
              "label": "Space horizontal items",
              "default": "5"
              },
              {
              "type": "select",
              "id": "space_v_item",
              "options": [
                      {
                          "value": "0", 
                          "label": "0"
                      },
                      {
                          "value": "2", 
                          "label": "2px"
                      },
                      {
                          "value": "4", 
                          "label": "4px"
                      },
                      {
                          "value": "5", 
                          "label": "5px"
                      },
                      {
                          "value": "8", 
                          "label": "8px"
                      },
                      {
                          "value": "10", 
                          "label": "10px"
                      },
                      {
                          "value": "15",
                          "label": "15px"
                      },
                      {
                          "value": "20",
                          "label": "20px"
                      },
                      {
                          "value": "25",
                          "label": "25px"
                      },
                      {
                          "value": "30",
                          "label": "30px"
                      }
                  ],
              "label": "Space vertical items",
              "default": "5"
              },
              {
              "type": "select",
              "id": "space_h_item_mb",
              "options": [
                  {
                      "value": "0", 
                      "label": "0"
                  },
                  {
                      "value": "2", 
                      "label": "2px"
                  },
                  {
                      "value": "4", 
                      "label": "4px"
                  },
                  {
                      "value": "6", 
                      "label": "6px"
                  },
                  {
                      "value": "8", 
                      "label": "8px"
                  },
                  {
                      "value": "10", 
                      "label": "10px"
                  },
                  {
                      "value": "15",
                      "label": "15px"
                  },
                  {
                      "value": "20",
                      "label": "20px"
                  },
                  {
                      "value": "25",
                      "label": "25px"
                  },
                  {
                      "value": "30",
                      "label": "30px"
                  }
              ],
              "label": "Space horizontal items (Mobile)",
              "default": "2"
              },
              {
              "type": "select",
              "id": "space_v_item_mb",
              "options": [
                  {
                      "value": "0", 
                      "label": "0"
                  },
                  {
                      "value": "2", 
                      "label": "2px"
                  },
                  {
                      "value": "4", 
                      "label": "4px"
                  },
                  {
                      "value": "6", 
                      "label": "6px"
                  },
                  {
                      "value": "8", 
                      "label": "8px"
                  },
                  {
                      "value": "10", 
                      "label": "10px"
                  },
                  {
                      "value": "15",
                      "label": "15px"
                  },
                  {
                      "value": "20",
                      "label": "20px"
                  },
                  {
                      "value": "25",
                      "label": "25px"
                  },
                  {
                      "value": "30",
                      "label": "30px"
                  }
              ],
              "label": "Space vertical items (Mobile)",
              "default": "2"
              },
              {
                  "type": "number",
                  "id": "mgb",
                  "label": "Margin bottom (Unit: px)"
              },
              {
                  "type": "number",
                  "id": "mgb_tb",
                  "label": "Margin bottom on tablet (Unit: px)"
              },
              {
                  "type": "number",
                  "id": "mgb_mb",
                  "label": "Margin bottom on mobile (Unit: px)"
              }
          ]
      },
      {
        "type": "countdown",
        "name": "Countdown timer",
        "limit": 1,
        "settings": [
         {
           "type": "paragraph",
           "content": "Display a countdown timer in your product page."
         },
         {
            "type": "select",
            "id": "source",
            "label": "Show countdown timer",
            "default": "1",
            "options": [
              {
                "value": "1",
                "label": "All products"
              },
              {
                "value": "2",
                "label": "Only product had tag 'has_stock_countdown'"
              }
            ]
          },
         /*{
           "type": "checkbox",
           "id": "timezone",
           "label": "Use General timezone?",
           "info": "Use to display a countdown accordingly to the General timezone no matter the localtime your computer is.",
           "default": false
         },*/
         {
            "type": "select",
            "id": "icon",
            "label": "ICON / IMG",
            "default": "2",
            "options": [
              {
                "value": "1",
                "label": "None"
              },
              {
                "value": "2",
                "label": "Icon"
              },
              {
                "value": "3",
                "label": "Image"
              }
            ]
          },
         {
           "type": "text",
           "id": "icon_name",
           "label": "Icon class name",
           "default": "las la-stopwatch",
           "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
         },
         {
           "type": "image_picker",
           "id": "img",
           "label": "Image",
           "info": "25x25 recommend"
         },
         {
           "type": "select",
           "id": "ani",
           "options": [
             {
               "value": "none",
               "label": "None"
             },
             {
               "value": "bounce is--infinite",
               "label": "Bounce"
             },
             {
               "value": "tada is--infinite",
               "label": "Tada"
             },
             {
               "value": "swing is--infinite",
               "label": "Swing"
             },
             {
               "value": "flash is--infinite",
               "label": "Flash"
             },
             {
               "value": "fadeIn is--infinite",
               "label": "FadeIn"
             },
             {
               "value": "heartBeat is--infinite",
               "label": "HeartBeat"
             },
             {
               "value": "shake is--infinite",
               "label": "Shake"
             }
           ],
           "label": "ICON / IMG animation"
         },
         {
            "type": "select",
            "id": "al",
            "label": "Text align",
            "default": "center",
            "options": [
              {
                "value": "start",
                "label": "Left"
              },
              {
                "value": "center",
                "label": "Center"
              },
              {
                "value": "end",
                "label": "Right"
              }
            ]
          },
          {
           "type": "textarea",
           "id": "mess",
           "label": "Message",
           "default": "Hurry up! Sale Ends in",
           "placeholder": "Hurry up! Sale Ends in"
          },
          {
            "type": "range",
            "id": "size",
            "min": 14,
            "max": 50,
            "step": 1,
            "label": "Font size",
            "unit": "px",
            "default": 16
          },
          {
              "type": "color",
              "id": "color",
              "label": "Color",
              "default": "#fb0000"
          },
         {
           "type": "header",
           "content": "+ Countdown Metafields"
         },
          {
            "type": "range",
            "id": "dayx",
            "min": 0,
            "max": 100,
            "step": 1,
            "label": "Reset countdown every x days from an initial date.",
            "info": "Set is '0' to disable reset countdown.",
            "unit": "day",
            "default": 0
          },
         {
           "type": "paragraph",
           "content": "Metafields product countdown, Countdown to the end sale date will be shown. Be sure you have set final date of the product sale price. theme.countdown ( 1994/04/19 ) or ( 1994/04/19 17:34:56 )"
         },
         {
           "type": "header",
           "content": "+ Countdown Loop Day"
         },
         {
           "type": "textarea",
           "id": "stock_time",
           "label": "Countdown timer loop in a day",
           "default": "8:00:00,16:00:00,23:59:59",
           "placeholder": "8:00:00,16:00:00,23:59:59"
         },
         {
           "type": "checkbox",
           "id": "loop",
           "label": "Use loop countdown",
           "default": false
         },
         {
           "type": "paragraph",
           "content": "Hour of the day, 24-hour clock (00..23), Minute of the hour (00..59), Second of the minute (00..59)"
         },
         {
            "type": "select",
            "id": "cdt_des",
            "label": "Countdown design",
            "default": "2",
            "options": [
              {
                  "value": "1",
                  "label": "Design 1"
              },
              {
                  "value": "2",
                  "label": "Design 2"
              }
            ]
          },
          {
            "type": "select",
            "id": "cdt_size",
            "label": "Countdown size",
            "default": "large",
            "options": [
              {
                "value": "small",
                "label": "Small"
              },
              {
                  "value": "medium",
                  "label": "Medium"
              },
              {
                  "value": "large",
                  "label": "Large"
              },
              {
                  "value": "extra_large",
                  "label": "Extra large"
              }
            ]
          },
          {
              "type": "checkbox",
              "id": "countdown_shadow",
              "label": "Enable box shadow",
              "default": false
          },
          {
            "type": "range",
            "id": "box_bdr",
            "label": "Border radius",
            "default": 0,
            "min": 0,
            "max": 50,
            "step": 1,
            "unit": "%"
          },
          {
            "type": "range",
            "id": "bd_width",
            "label": "Border width",
            "default": 0,
            "min": 0,
            "max": 5,
            "step": 1,
            "unit": "px"
          },
          {
            "type": "range",
            "id": "space_item",
            "label": "Space between items",
            "default": 50,
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "px"
          },
          {
            "type": "range",
            "id": "space_item_tb",
            "label": "Space between items (Tablet)",
            "default": 10,
            "min": 0,
            "max": 30,
            "step": 1,
            "unit": "px"
          },
          {
            "type": "range",
            "id": "space_item_mb",
            "label": "Space between items (Mobile)",
            "default": 10,
            "min": 0,
            "max": 30,
            "step": 1,
            "unit": "px"
          },
          {
            "type": "color",
            "id": "number_cl",
            "label": "Number color",
            "default": "#fff"
          },
          {
            "type": "color",
            "id": "text_cl",
            "label": "Text color",
            "default": "#fff"
          },
          {
            "type": "color",
            "id": "border_cl",
            "label": "Border color item time",
            "default": "#000"
          },
          {
            "type": "color",
            "id": "bg_cl",
            "label": "Background item time",
            "default": "#000"
          } 
        ]
      },
      {
        "type": "vendor_img",
        "name": "Brand Image",
        "limit": 1,
        "settings": []
      },
      {
        "type": "inventory_qty",
        "name": " Inventory quantity",
        "limit": 1,
        "settings": [
            {
              "type": "paragraph",
              "content": "Display the stock level of your product variant."
            },
            {
                "type": "select",
                "id": "icon",
                "label": "ICON / IMG",
                "default": "2",
                "options": [
                    {
                        "value": "1",
                        "label": "None"
                    },
                    {
                        "value": "2",
                        "label": "Icon"
                    },
                    {
                        "value": "3",
                        "label": "Image"
                    }
                ]
            },
            {
                "type": "text",
                "id": "icon_name",
                "label": "Icon class name",
                "default": "las la-hourglass-start",
                "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
            },
            {
                "type": "image_picker",
                "id": "img",
                "label": "Image",
                "info": "25x25 recommend"
            },
            {
            "type": "select",
            "id": "ani",
            "options": [
                {
                    "value": "none",
                    "label": "None"
                },
                {
                    "value": "bounce is--infinite",
                    "label": "Bounce"
                },
                {
                    "value": "tada is--infinite",
                    "label": "Tada"
                },
                {
                    "value": "swing is--infinite",
                    "label": "Swing"
                },
                {
                    "value": "flash is--infinite",
                    "label": "Flash"
                },
                {
                    "value": "fadeIn is--infinite",
                    "label": "FadeIn"
                },
                {
                    "value": "heartBeat is--infinite",
                    "label": "HeartBeat"
                    },
                {
                    "value": "shake is--infinite",
                    "label": "Shake"
                }
            ],
            "label": "ICON / IMG animation"
            },
            {
                "type": "select",
                "id": "stock",
                "label": "Stock",
                "default": "3",
                "options": [
                    {
                        "value": "1",
                        "label": "Only default"
                    },
                    {
                        "value": "2",
                        "label": "Only random"
                    },
                    {
                        "value": "3",
                        "label": "Default + Random"
                    }
                ]
                },
            /*{
               "type": "checkbox",
               "id": "reduce",
               "label": "Enable gradually reduce the amount of inventory after short time",
               "default": false
            },*/
            {
              "type": "header",
              "content": "Default"
            },
            {
              "type": "range",
              "id": "qty",
              "min": 1,
              "max": 100,
              "step": 1,
              "unit": "Qty",
              "label": "(X) items",
              "info": "Show when less than (X) items are in stock",
              "default": 10
            },
            {
              "type": "header",
              "content": "Random"
            },
            {
              "type": "range",
              "id": "total_items",
              "label": "Total items",
              "min": 10,
              "max": 100,
              "step": 10,
              "default": 100
            },
            {
              "type": "range",
              "id": "stock_from",
              "label": "from",
              "min": 1,
              "max": 19,
              "step": 1,
              "default": 12
            },
            {
              "type": "range",
              "id": "stock_to",
              "label": "to",
              "min": 20,
              "max": 70,
              "step": 1,
              "default": 20
            },
            {
              "type": "header",
              "content": "Translate labels"
            },
           {
              "type": "select",
              "id": "al",
              "label": "Text align",
              "default": "center",
              "options": [
                {
                  "value": "start",
                  "label": "Left"
                },
                {
                  "value": "center",
                  "label": "Center"
                },
                {
                  "value": "end",
                  "label": "Right"
                }
              ]
            },
            {
              "type": "textarea",
              "id": "mess",
              "label": "Message (You can leave it blank)",
              "info": "Hurry! Only [stock_number] left in stock.",
              "placeholder": "Hurry! Only [stock_number] left in stock.",
              "default": "HURRY! ONLY [stock_number] LEFT IN STOCK."
            },
            {
              "type": "range",
              "id": "size",
              "min": 10,
              "max": 35,
              "step": 1,
              "label": "Font size",
              "unit": "px",
              "default": 16
            },
            {
              "type": "header",
              "content": "Progress"
            },
            {
             "type": "checkbox",
             "id": "progress",
             "label": "Use progress bar",
             "default": true
            },
            {
              "type": "range",
              "id": "wbar",
              "min": 40,
              "max": 100,
              "step": 1,
              "unit": "%",
              "label": "Width progress bar",
              "default": 100
            },
            {
              "type": "color",
              "id": "stock_bg_process",
              "label": "Process color",
              "default": "#f76b6a"
            },
            {
              "type": "color",
              "id": "bgten",
              "label": "Less than 10 color",
              "default": "#ec0101"
            },
            {
              "type": "color",
              "id": "stock_bg",
              "label": "Background color",
              "default": "#ffe8e8"
            }
        ]
      },
      {
        "type": "img",
        "name": "Trust Badge",
        "limit": 1,
        "settings": [
        {
          "type": "select",
          "id": "al",
          "label": "Content align",
          "info": "This option only works on desktops. On mobiles, the content will be centered",
          "default": "start",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Right"
            }
          ]
        },
         {
           "type": "textarea",
           "id": "mess",
           "label": "Message",
           "default": "Guaranteed Safe Checkout",
           "placeholder": "Guaranteed Safe Checkout"
         },
          {
            "type": "range",
            "id": "size",
            "min": 10,
            "max": 60,
            "step": 1,
            "label": "Font size",
            "unit": "px",
            "default": 16
          },
         {
            "type": "select",
            "id": "source",
            "label": "Source image",
            "default": "1",
            "options": [
              {
                "value": "1",
                "label": "Image"
              },
              {
                "value": "2",
                "label": "SVG"
              }
            ]
          },
          {
            "type": "header",
            "content": "+ Image"
          },
         {
           "type": "image_picker",
           "id": "image",
           "label": "Trust seal image"
         },
          {
            "type": "range",
            "id": "wimg",
            "min": 40,
            "max": 100,
            "step": 1,
            "unit": "%",
            "label": "Width image",
            "default": 60
          },
          {
            "type": "header",
            "content": "+ SVG"
          },
         {
           "type": "textarea",
           "id": "svg",
           "label": "SVG list",
           "default": "amazon_payments,american_express,apple_pay,bitcoin,dankort,diners_club,discover,dogecoin,dwolla,forbrugsforeningen,interac,google_pay,jcb,klarna,klarna-pay-later,litecoin,maestro,master,paypal,shopify_pay,sofort,visa",
           "info": "amazon_payments,american_express,apple_pay,bitcoin,dankort,diners_club,discover,dogecoin,dwolla,forbrugsforeningen,interac,google_pay,jcb,klarna,klarna-pay-later,litecoin,maestro,master,paypal,shopify_pay,sofort,visa"
         },
        {
          "type": "range",
          "id": "height",
          "min": 1,
          "max": 100,
          "step": 1,
          "label": "Height",
          "unit": "px",
          "default": 50
        }
        ]
      },
      {
        "type": "store_pickup",
        "name": "Pickup availability",
        "limit": 1,
        "settings": [
         {
            "type": "paragraph",
            "content": "Engage local shoppers by showing where items are available for pickup — right from the product page. [Learn more](https://help.shopify.com/en/manual/shipping/setting-up-and-managing-your-shipping/local-methods/local-pickup)"
         }
         ]
      },
      {
        "type": "live_view",
        "name": "Live view",
        "limit": 1,
        "settings": [
         {
            "type": "paragraph",
            "content": "Display fake the number of people viewing your product page."
         },
         {
            "type": "select",
            "id": "icon",
            "label": "ICON / IMG",
            "default": "2",
            "options": [
                {
                    "value": "1",
                    "label": "None"
                },
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "2",
                    "label": "Icon"
                },
                {
                    "value": "3",
                    "label": "Image"
                }
            ]
          },
         {
           "type": "text",
           "id": "icon_name",
           "label": "Icon class name",
           "default": "las la-eye",
           "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
         },
         {
           "type": "image_picker",
           "id": "img",
           "label": "Image",
           "info": "25x25 recommend"
         },
         {
          "type": "select",
          "id": "ani",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "bounce is--infinite",
              "label": "Bounce"
            },
            {
              "value": "tada is--infinite",
              "label": "Tada"
            },
            {
              "value": "swing is--infinite",
              "label": "Swing"
            },
            {
              "value": "flash is--infinite",
              "label": "Flash"
            },
            {
              "value": "fadeIn is--infinite",
              "label": "FadeIn"
            },
            {
              "value": "heartBeat is--infinite",
              "label": "HeartBeat"
            },
            {
              "value": "shake is--infinite",
              "label": "Shake"
            }
          ],
          "label": "ICON / IMG animation"
        },
         {
           "type": "range",
           "id": "min",
           "min": 1,
           "max": 100,
           "step": 1,
           "label": "Min fake real time visitor",
           "default": 1
         },
         {
           "type": "range",
           "id": "max",
           "min": 10,
           "max": 1000,
           "step": 10,
           "label": "Max fake real time visitor",
           "default": 100
         },
         {
           "type": "range",
           "id": "time",
           "min": 1,
           "max": 20,
           "step": 1,
           "unit": "sec",
           "label": "Interval time",
           "default": 2
         },
         {
           "type": "textarea",
           "id": "text",
           "label": "Text",
           "default": "[count] People are viewing this right now"
         }
        ]
      },
      {
          "type": "sold",
          "name": "Total sold flash",
          "limit": 1,
          "settings": [
              {
                  "type": "select",
                  "id": "icon",
                  "label": "ICON / IMG",
                  "default": "2",
                  "options": [
                      {
                          "value": "1",
                          "label": "None"
                      },
                      {
                          "value": "default",
                          "label": "Default"
                      },
                      {
                          "value": "2",
                          "label": "Icon"
                      },
                      {
                          "value": "3",
                          "label": "Image"
                      }
                  ]
              },
              {
                  "type": "text",
                  "id": "icon_name",
                  "label": "Icon class name",
                  "default": "lab la-gripfire",
                  "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
              },
              {
                  "type": "image_picker",
                  "id": "img",
                  "label": "Image",
                  "info": "25x25 recommend"
              },
              {
                  "type": "select",
                  "id": "ani",
                  "options": [
                      {
                      "value": "none",
                      "label": "None"
                      },
                      {
                      "value": "bounce is--infinite",
                      "label": "Bounce"
                      },
                      {
                      "value": "tada is--infinite",
                      "label": "Tada"
                      },
                      {
                      "value": "swing is--infinite",
                      "label": "Swing"
                      },
                      {
                      "value": "flash is--infinite",
                      "label": "Flash"
                      },
                      {
                      "value": "fadeIn is--infinite",
                      "label": "FadeIn"
                      },
                      {
                      "value": "heartBeat is--infinite",
                      "label": "HeartBeat"
                      },
                      {
                      "value": "shake is--infinite",
                      "label": "Shake"
                      }
                  ],
                  "label": "ICON / IMG animation"
              },
              {
                  "type": "range",
                  "id": "mins",
                  "min": 1,
                  "max": 100,
                  "step": 1,
                  "unit": "qty",
                  "label": "Min quantity",
                  "default": 5
              },
              {
                  "type": "range",
                  "id": "maxs",
                  "min": 10,
                  "max": 110,
                  "step": 1,
                  "unit": "qty",
                  "label": "Max quantity",
                  "default": 25
              },
              {
                  "type": "range",
                  "id": "mint",
                  "min": 1,
                  "max": 24,
                  "step": 1,
                  "unit": "h",
                  "label": "Min time",
                  "default": 3
              },
              {
                  "type": "range",
                  "id": "maxt",
                  "min": 1,
                  "max": 24,
                  "step": 1,
                  "unit": "h",
                  "label": "Max time",
                  "default": 24
              },
              {
                  "type": "textarea",
                  "id": "text",
                  "label": "Text",
                  "info": "[sold] sold in last [hour] hours",
                  "default": "[sold] sold in last [hour] hours"
              }
          ]
      },
      {
        "type": "order",
        "name": "Delivery time",
        "limit": 1,
        "settings": [
         {
            "type": "paragraph",
            "content": "Display an approximate date of delivery."
         },
         {
           "type": "checkbox",
           "id": "hide_pre",
           "label": "Hide with 'pre-order'",
           "default": true
         },
         {
            "type": "select",
            "id": "icon",
            "label": "ICON / IMG",
            "default": "2",
            "options": [
                {
                    "value": "1",
                    "label": "None"
                },
                {
                    "value": "default",
                    "label": "Default"
                },
                {
                    "value": "2",
                    "label": "Icon"
                },
                {
                    "value": "3",
                    "label": "Image"
                }
            ]
         },
         {
           "type": "text",
           "id": "icon_name",
           "label": "Icon class name",
           "default": "las la-truck",
           "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
         },
         {
           "type": "image_picker",
           "id": "img",
           "label": "Image",
           "info": "35x35 recommend"
         },
         {
           "type": "select",
           "id": "ani",
           "options": [
             {
               "value": "none",
               "label": "None"
             },
             {
               "value": "bounce is--infinite",
               "label": "Bounce"
             },
             {
               "value": "tada is--infinite",
               "label": "Tada"
             },
             {
               "value": "swing is--infinite",
               "label": "Swing"
             },
             {
               "value": "flash is--infinite",
               "label": "Flash"
             },
             {
               "value": "fadeIn is--infinite",
               "label": "FadeIn"
             },
             {
               "value": "heartBeat is--infinite",
               "label": "HeartBeat"
             },
             {
               "value": "shake is--infinite",
               "label": "Shake"
             }
           ],
           "label": "ICON / IMG animation"
         },
         {
           "type": "textarea",
           "id": "txt",
           "label": "Delivery text",
           "default": "Order in the next [hour] to get it between [date_start] and [date_end]",
           "info": "Order in the next [hour] to get this to you between [date_start] and [date_end], Order in the next [hour] to get it by [date_end], Order in the next [hour] to get it soon"
         },
         {
           "type": "range",
           "id": "ds",
           "min": 0,
           "max": 99,
           "step": 1,
           "label": "Delivery start date",
           "info": "From Current date",
           "default": 10
         },
         {
           "type": "range",
           "id": "de",
           "min": 0,
           "max": 99,
           "step": 1,
           "label": "Delivery end date",
           "info": "From Current date",
           "default": 15
         },
         {
           "type": "select",
           "id": "mode",
           "default": "1",
           "options": [
             {
               "value": "1",
               "label": "Only Delivery"
             },
             {
               "value": "2",
               "label": "Shipping + Delivery"
             }
           ],
           "label": "Exclude days from"
         },
         {
           "type": "text",
           "id": "cut_day",
           "label": "Exclude days",
           "default": "SAT,SUN",
           "info": "Use the 'MON','TUE','WED','THU','FRI','SAT' and 'SUN'. Separate exclude days with a comma (,)."
         },
         {
           "type": "select",
           "id": "frm_day",
           "default": "bee4, bee5 bee6",
           "options": [
             {
               "value": "bee4, bee5 bee6",
               "label": "Wednesday, 19th April"
             },
             {
               "value": "bee4, DD bee6",
               "label": "Wednesday, 19 April"
             },
             {
               "value": "bee4, bee5 bee6 YYYY",
               "label": "Wednesday, 19th April 2019"
             },
             {
               "value": "bee4, bee5 bee6, YYYY",
               "label": "Wednesday, 19th April, 2019"
             },
             {
               "value": "bee4, bee6 bee5, YYYY",
               "label": "Wednesday, April 19th, 2019"
             },
             {
               "value": "bee4, bee6 bee5",
               "label": "Wednesday, April 19th"
             },
             {
               "value": "bee4, bee6 bee5 YYYY",
               "label": "Wednesday, April 19th 2019"
             },
             {
               "value": "bee4, bee6 DD",
               "label": "Wednesday, April 19"
             },
             {
               "value": "bee4, bee6 DD YYYY",
               "label": "Wednesday, April 19 2019"
             },
             {
               "value": "bee4, MM/DD/YYYY",
               "label": "Wednesday, 04/19/2019"
             },
             {
               "value": "bee4, DD/MM/YYYY",
               "label": "Wednesday, 19/04/2019"
             },
             {
               "value": "bee4, YYYY/MM/DD",
               "label": "Wednesday, 2019/04/19"
             }
           ],
           "label": "Date delivery format"
         },
         /*{
           "type": "checkbox",
           "id": "timezone",
           "label": "Use General timezone",
           "info": "Use to display a countdown accordingly to the General timezone no matter the localtime your computer is.",
           "default": false
         },*/
         {
           "type": "text",
           "id": "time",
           "label": "Delivery cut off",
           "info": "Number Only(24 Hours Format - 16:00:00 Means 4PM)",
           "default": "16:00:00"
         },
         {
           "type": "text",
           "id": "hr",
           "label": "Text hours",
           "default": "hours"
         },
         {
           "type": "text",
           "id": "min",
           "label": "Text minutes",
           "default": "minutes"
         }
        ]
      },
      {
        "type": "text",
        "name": "Text",
        "settings": [
            {
            "type": "richtext",
            "id": "text",
            "label": "Text",
            "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
            }
         ]
      },
      {
        "type": "html",
        "name": "Custom HTML",
        "settings": [
          {
            "type": "page",
            "id": "page",
            "label": "Content page",
            "info": "This page content will appear."
          }
        ]
      },
      {
        "type": "custom_liquid",
        "name": "Custom Liquid",
        "settings": [
          {
            "type": "liquid",
            "id": "custom_liquid",
            "label": "Custom Liquid",
            "info": "Add app snippets or other Liquid code to create advanced customizations."
          }
        ]
      },
      {
        "type": "tab_des",
        "name": "Tab Description",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Description"
          }
         ]
      },
      {
        "type": "tab_add", 
        "name": "Tab Additional Infor",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Additional Information"
          }
        ]
      },
      {
        "type": "tab_rivui",
        "name": "Tab Review",
        "limit": 1,
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Reviews"
          },
          { 
            "type": "liquid",
            "id": "review_liquid",
            "label": "Add Snippets Liquid",
            "info": "Add app snippets reviews to show a 'write reviews' on product page. Will working when you use 'Other app review'"
          }
         ]
      },
      {
        "type": "tab_html",
        "name": "Tab Custom HTML",
        "settings": [
            {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Collapsible tab",
            "info": "Include a heading that explains the content."
            },
            {
              "type": "richtext",
              "id": "content",
              "label": "Tab content"
            },
            {
              "type": "page",
              "id": "page",
              "label": "Tab content from page"
            }
         ]
      },
      {
        "type": "tab_liquid",
        "name": "Tab Custom Liquid",
        "settings": [
          {
            "type": "text",
            "id": "title",
            "label": "Heading",
            "default": "Tab Custom Liquid"
          },
          {
            "type": "liquid",
            "id": "custom_liquid",
            "label": "Custom Liquid",
            "info": "Add app snippets or other Liquid code to create advanced customizations."
          }
        ]
      },
      {
        "type": "properties",
        "name": "⚡ Customizable Products",
        "settings": [
          {
            "type": "paragraph",
            "content": "Block properties are used to collect customization information for an item added to the cart. This information can be collected from the buyer on the product page. Only show on block product form. Not show on product grouped, soldout, external/affiliate"
          },
          {
            "type": "header",
            "content": "+ Set your form field"
          },
          {
           "type": "radio",
           "id": "type",
           "default": "short",
           "options": [
             {
               "value": "short",
               "label": "Text - Short"
             },
             {
               "value": "long",
               "label": "Text - Long"
             },
             {
               "value": "checkbox",
               "label": "Checkbox"
             },
             {
               "value": "radio",
               "label": "Radio Buttons"
             },
             {
               "value": "select",
               "label": "Drop-down select"
             },
             {
               "value": "checkbox_group",
               "label": "Checkbox group"
             },
             {
               "value": "file",
               "label": "File upload (beta)"
             }
           ],
           "label": "Type of form field"
          },
          {
            "type": "paragraph",
            "content": "IMPORTANT: Upload file not support dynamic checkout buttons on product page, quick view, quick shop."
          },
          {
           "type": "text",
           "id": "heading",
           "default": "Your name",
           "label": "Your form field label"
          },
          {
           "type": "textarea",
           "id": "options",
           "label": "Options if using radio buttons, a drop-down select, or checkbox group",
           "default": "option1, option2",
           "info": "Separate your options with a comma."
          },
          {
           "type": "checkbox",
           "id": "required",
           "default": false,
           "label": "Required",
           "info": "If you use “Required” with a checkbox, then the checkbox will need to be checked for the customer to add the item to the cart."
          },
          {
           "type": "checkbox",
           "id": "show_at_checkout",
           "default": true,
           "label": "Show at checkout, cart",
           "info": "Uncheck this if you don't want the captured information to be shown in the order summary on the cart, checkout page."
          },
          {
            "type": "header",
            "content": "+ Set your visibility"
          },
          {
           "type": "radio",
           "id": "visibility",
           "default": "all",
           "options": [
             {
               "value": "all",
               "label": "All"
             },
             {
               "value": "collection",
               "label": "Collection based"
             },
             {
               "value": "type",
               "label": "Type based"
             },
             {
               "value": "tag",
               "label": "Tag based"
             },
             {
               "value": "product",
               "label": "Product based"
             },
             {
               "value": "metafield",
               "label": "Metafield based"
             }
           ],
           "label": "Visibility",
           "info": "Metafield based: theme.visibility_customizable"
          },
          {
           "type": "collection_list",
           "id": "collection_based",
           "label": "Collection list",
           "info": "Maximum choose: 50 collections"
          },
          {
           "type": "textarea",
           "id": "type_based",
           "label": "Product types",
           "placeholder": "type1, type2",
           "info": "Separate your types with a comma."
          },
          {
           "type": "textarea",
           "id": "tag_based",
           "label": "Product tags",
           "placeholder": "tag1, tag2",
           "info": "Separate your types with a comma."
          },
          {
           "type": "product_list",
           "id": "product_based",
           "label": "Product list",
           "info": "Maximum choose: 50 products"
          }
        ]
      }
    ],
    "default": {
      "blocks": [
        { "type": "review" },{ "type": "title" },{ "type": "price" },{ "type": "size" },{ "type": "line" },{ "type": "description" },{ "type": "form" }
      ]
    }
  }
{% endschema %} 