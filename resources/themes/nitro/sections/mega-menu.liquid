{%- liquid
  assign se_blocks         = section.blocks 
  assign se_blocks_size    = se_blocks.size 
  assign checkFirst        = true
  assign use_link_vendor = settings.use_link_vendor
  assign sid = section.id
  if sid == 'mega-menu'
   assign show_code = true
  elsif sid != 'mega-menu' and request.design_mode or request.page_type == 'index' 
   assign show_code = true
  else
   assign show_code = false
  endif -%}

<div data-section-id="{{ sid }}"></div>
{%- if show_code -%}
  [nt_mega_split1]
    {%- for block in se_blocks -%}{%- assign bk_type = block.type -%}{%- assign bk_stts = block.settings -%}
    {%- if checkFirst and bk_type == 'mega' -%}
    {%- assign checkFirst = false -%}
    <div id="bee-mega-contents{{ bk_stts.id }}" data-id="{{ bk_stts.id }}" data-blockid="{{ block.id }}" {{ block.shopify_attributes }}>
    <link rel="stylesheet" href="{{ 'bee-mega-menu.css' | asset_url }}" media="all">
    {%- elsif bk_type == 'mega' -%}
    </div>
    <div id="bee-mega-contents{{ bk_stts.id }}" data-id="{{ bk_stts.id }}" data-blockid="{{ block.id }}" {{ block.shopify_attributes }}>
    <link rel="stylesheet" href="{{ 'bee-mega-menu.css' | asset_url }}" media="all">
    {%- endif -%}

      {%- case bk_type -%}
        {%- when 'banner' %}{% assign c_bid = block.id %}{% assign image = bk_stts.image -%}
          {{ 'content-position.css' | asset_url | stylesheet_tag }}
          {{ 'banner.css' | asset_url | stylesheet_tag }}
          {%-liquid 
            assign image = bk_stts.image
            assign image_ratio = bk_stts.image_ratio
            if image_ratio == "ratioadapt"
              assign imgatt = ''
             else 
              assign imgatt = 'data-'
            endif
            assign cl_opacity = bk_stts.cl_opacity | divided_by: 100.0
            assign cl_overlay = bk_stts.cl_overlay | color_modify: 'alpha', cl_opacity
            if bk_stts.b_link != blank
              assign ARRhtml = 'a,,' | split: ','
            else
              assign ARRhtml = 'div,data-,data-' | split: ','
            endif
          -%}
          <div id="bk_{{ c_bid }}" class="type_mn_banner bee-menu-item bee-sub-column-item bee-col-{{ bk_stts.col }} bee-col-item" {{ block.shopify_attributes }}>
            <div class="bee-banner-item bee_{{ image_ratio }} bee_position_{{ bk_stts.image_position }} bee_{{ bk_stts.image_size }} beecuspx3_true bee-eff bee-eff-{{ bk_stts.b_effect }} bee-eff-img-{{ bk_stts.img_effect }}" {% if image_ratio != "ratio_fh" %} style="--aspect-ratio-cusdt : {{ bk_stts.height_dk }}px;" {%- endif %}>
              <div class="bee-banner-inner" style="--cl-text: {{ bk_stts.cl_txt }};--bg-overlay: {{ cl_overlay }};">
                

                <{{ ARRhtml[0] }} {{ ARRhtml[1] }}href="{{ bk_stts.b_link }}"  {{ ARRhtml[2] }}target="{{ bk_stts.open_link }}" class="bee-d-block bee_ratio bee_ratio_hasmb" {{ imgatt }}style="--aspect-ratioapt: {{ image.aspect_ratio | default: 2 }};">
                  {%- if image != blank -%}
                      <img class="lazyloadbee bee-lz--fadeIn bee-obj-eff" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                      <span class="lazyloadbee-loader is-bg-img" style="background: url({{ image | image_url: width: 1 }})"></span>
                  {%- else -%}
                    {{ 'image' | placeholder_svg_tag: 'bee-placeholder-svg bee-svg-bg1 bee-obj-eff' }}
                  {%- endif -%}
                </{{ ARRhtml[0] }}>
                <div class="bee-banner-content bee-content-position bee-pa bee-text-{{ bk_stts.content_align }}" style="{%- render 'content_position', ch_pos: bk_stts.ch_pos, cv_pos: bk_stts.cv_pos -%}">
                  <div class="bee-content-html">{{ bk_stts.html }}</div>
                </div>
              </div>
            </div>
            <style type="text/css">
              .bee-banner-item .bee-content-html {
                color: var(--cl-text) !important;
              }
              .bee-banner-item .bee-content-html > * {
                  color: inherit;
              }
            </style>
          </div>
        {%- when 'banner_text' -%}
          {{ 'bee-banner-custom.css' | asset_url | stylesheet_tag }} 
          {%-liquid 
            assign pd_content = bk_stts.padding_inner_bn | remove: ' ' | split: ','
            assign pd_content_tb = bk_stts.padding_inner_bn_tb | remove: ' ' | split: ','
            assign pd_content_mb = bk_stts.padding_inner_bn_mb | remove: ' ' | split: ','
            assign image = bk_stts.image_bn
            if bk_stts.bn_link != blank
              assign ARRhtml = 'a,,' | split: ','
            else
              assign ARRhtml = 'div,data-,data-' | split: ','
            endif
            if bk_stts.image_ratio == "ratioadapt"
              assign imgatt = '' 
            else 
              assign imgatt = 'data-'
            endif
            assign bg_sale_cl_lightness = bk_stts.bg_sale_cl | color_extract: 'lightness' 
            if bg_sale_cl_lightness < 85  
              assign sale_txt = "#ffffff"
            else 
              assign sale_txt = "#010101"
            endif   
          -%}
          <div id="bk_{{ c_bid }}" class="type_mn_banner bee-menu-item bee-sub-column-item bee-col-{{ bk_stts.col }} bee-col-item" {{ block.shopify_attributes }}>
            <div class="bee-bn-wrap bee-custom-space bee-pr bee-d-block bee-eff-img-{{ bk_stts.img_effect }} bee-bn-{{ bk_stts.bn_des }} bee_position_8 bee_ratioadapt bee_cover" style="--aspect-ratioapt: {{ image.aspect_ratio | default: 1 }};--pdbn: {{ pd_content[0] | default: 0 }} {{ pd_content[1] | default: 0 }} {{ pd_content[2] | default: 0 }} {{ pd_content[3] | default: 0 }};--pdbn-tb: {{ pd_content_tb[0] | default: 0 }} {{ pd_content_tb[1] | default: 0 }} {{ pd_content_tb[2] | default: 0 }} {{ pd_content_tb[3] | default: 0 }};--pdbn-mb: {{ pd_content_mb[0] | default: 0 }} {{ pd_content_mb[1] | default: 0 }} {{ pd_content_mb[2] | default: 0 }} {{ pd_content_mb[3] | default: 0 }};--mgb-box: {{ bk_stts.mgb_box }}px;--mgb-box-tb: {{ bk_stts.mgb_box_tb }}px;--mgb-box-mb: {{ bk_stts.mgb_box_mb }}px;">
              <{{ ARRhtml[0] }} {{ ARRhtml[1] }}href="{{ bk_stts.bn_link }}" {{ ARRhtml[2] }}target="{{ bk_stts.bn_target_link }}" class="bee-d-block bee-oh bee_ratio bee-bg-11"{% if image != blank %} style="background: url({{ image | image_url: width: 1 }});"{% endif %}> 
                {%- if image != blank -%}
                    <img class="lazyloadbee bee-lz--fadeIn bee-obj-eff" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                {%- else -%}
                  {{ 'image' | placeholder_svg_tag: 'bee-placeholder-svg bee-svg-bg1 bee-obj-eff' }} 
                {%- endif -%}
              </{{ ARRhtml[0] }}>
              {%- if bk_stts.label_sale > 0 -%}
              <span class="bee-label-sale-off" style="--bg-sale-cl: {{ bk_stts.bg_sale_cl }};--sale-cl-text: {{ sale_txt }}">{{ bk_stts.label_sale }} <span class="bee-sale-off-txt"><span>%</span>{{ 'customer.menu.off' | t }}</span></span>
              {%- endif -%}
              <div class="bee-bn-content bee-mega-bn bee-pa bee-bn-content-{{ bk_stts.bn_layout }} bee-text-v-{{ bk_stts.content_v_position }} bee-text-h-{{ bk_stts.content_h_position }} bee-text-{{ bk_stts.content_bn_align }}">
                {% if bk_stts.bn_link %}<a href="{{ bk_stts.bn_link }}" target="{{ bk_stts.bn_target_link }}" class="bee-over-link bee-pa"></a>{% endif %}
                <div class="bee-bn-inner">
                  {% if bk_stts.title != blank %}
                    <p class="bee-bn-title bee-text-bl bee-fnt-fm-{{ bk_stts.fontf_1 }} bee-hidden-br-mb-{{ bk_stts.remove_br_tag }}" style="--text-fs: {{ bk_stts.title_size }}px;--text-fw: 300;">{{ bk_stts.title }}</p>
                  {% endif %}
                  {% if bk_stts.subtitle != blank %}
                    <h3 class="bee-bn-subtitle bee-text-bl bee-fnt-fm-{{ bk_stts.fontf_2 }} bee-hidden-br-mb-{{ bk_stts.remove_br_tag }}" style="--text-fs: 13px;">{{ bk_stts.subtitle }}</h3>
                  {% endif %}
                  {%- if bk_stts.button_link != blank and bk_stts.button_text != blank -%}
                    <a href="{{ bk_stts.button_link }}" target="{{ bk_stts.target_link }}" class="bee-btn bee-btn-custom bee-pe-auto bee-btn-style-bordered bee-pr bee-oh bee-fnt-fm-{{ bk_stts.fontf_1 }}" style="--icon-width: {{ bk_stts.button_icon_w }}px;--icon-height: {{ bk_stts.button_icon_w }}px">{{ bk_stts.button_text }} {%- if bk_stts.button_icon_w > 0 -%}<svg  class="bee-btn-icon" viewBox="0 0 22 22"><use xlink:href="#bee-icon-btn"></use></svg>{%- endif -%}</a>
                  {%- endif -%}
                </div>
              </div>
            </div>
          </div> 
        {%- when 'cat' -%}
          {{ 'bee-collection-item.css' | asset_url | stylesheet_tag }}
          {{ 'bee-custom-effect.css' | asset_url | stylesheet_tag }}
          {%-liquid
            assign collection_des = bk_stts.collection_des
            assign image_ratio = bk_stts.image_ratio
            if image_ratio == "ratioadapt"
              assign imgatt = ''
             else 
              assign imgatt = 'data-' 
            endif
            assign content_align = bk_stts.content_align
            assign b_effect = bk_stts.b_effect
            assign img_effect = bk_stts.img_effect
            assign open_link = bk_stts.open_link

            assign pri_cl_lightness         = bk_stts.pri_cl | color_extract: 'lightness'
            assign pri_hover_cl_lightness   = bk_stts.pri_hover_cl | color_extract: 'lightness'
            assign second_cl_lightness         = bk_stts.second_cl | color_extract: 'lightness'
            assign second_hover_cl_lightness   = bk_stts.second_hover_cl | color_extract: 'lightness'

            if pri_cl_lightness < 85  
              assign pri_cl_txt = "#ffffff"
            else 
              assign pri_cl_txt = "#010101"
            endif
            if pri_hover_cl_lightness < 85 
              assign pri_hover_cl_txt = "#ffffff"
            else 
              assign pri_hover_cl_txt = "#010101"
            endif
            if second_cl_lightness < 85 
              assign second_cl_txt = "#ffffff"
            else 
              assign second_cl_txt = "#010101"
            endif
            if second_hover_cl_lightness < 85 
              assign second_hover_cl_txt = "#ffffff"
            else 
              assign second_hover_cl_txt = "#010101"
            endif
          -%}
          <div class="type_mn_pr bee-menu-item bee-sub-column-item bee-col-item bee-col-{{ bk_stts.col }}" >
            <div id="bk_{{ block.id }}" class="bee-list-collections bee-collection-des-{{ collection_des }} bee-collection-pos-{{ bk_stts.content_pos }} bee_{{ image_ratio }} bee_position_{{ bk_stts.image_position }} bee_{{ bk_stts.image_size }} bee-gx-0" {{ block.shopify_attributes }} style="--pri-cl: {{ bk_stts.pri_cl }};--pri-cl-txt: {{ pri_cl_txt }};--pri-hover-cl: {{ bk_stts.pri_hover_cl }};--pri-hover-cl-txt: {{ pri_hover_cl_txt }};--second-cl: {{ bk_stts.second_cl }};--second-cl-txt: {{ second_cl_txt }};--second-hover-cl: {{ bk_stts.second_hover_cl }};--second-hover-cl-txt: {{ second_hover_cl_txt }};--item-rd:{{ bk_stts.item_rd }}%;--item-width: {{ bk_stts.content_width }}px;--item-height: {{ bk_stts.content_height }}px;--item-width-mb: {{ bk_stts.content_width_mb }}px;--item-height-mb: {{ bk_stts.content_height_mb }}px;--space-bottom: {{ bk_stts.space_bottom }}px;--space-bottom-mb: {{ bk_stts.space_bottom_mb }}px;--title-fs: {{ bk_stts.title_fs }}px;--title-fw: {{ bk_stts.title_fw }};">
              {%- capture current -%}{%- cycle 1, 2, 3, 4, 5, 6 -%}{%- endcapture -%}
              <div class="bee-collection-item hh bee-text-{{ bk_stts.content_align }}" id="b_{{ block.id }}" data-select-flickity {{ block.shopify_attributes }}>
                  {%- render 'collection_item', collection_des: collection_des, b_effect: b_effect, img_effect: img_effect, bk_stts: bk_stts, imgatt: imgatt, open_link: open_link, content_align: content_align, current: current -%}
                </div>
            </div>
          </div>
        {%- when 'cat_list' -%}
          {{ 'bee-collection-item.css' | asset_url | stylesheet_tag }}
          {{ 'bee-custom-effect.css' | asset_url | stylesheet_tag }}
          {%-liquid
            assign collection_list = bk_stts.collection_list
            assign collection_des = bk_stts.collection_des
            assign image_ratio = bk_stts.image_ratio
            if image_ratio == "ratioadapt"
              assign imgatt = ''
             else 
              assign imgatt = 'data-' 
            endif
            assign b_effect = bk_stts.b_effect
            assign img_effect = bk_stts.img_effect
            assign open_link = bk_stts.open_link

            assign pri_cl_lightness         = bk_stts.pri_cl | color_extract: 'lightness'
            assign pri_hover_cl_lightness   = bk_stts.pri_hover_cl | color_extract: 'lightness'
            assign second_cl_lightness         = bk_stts.second_cl | color_extract: 'lightness'
            assign second_hover_cl_lightness   = bk_stts.second_hover_cl | color_extract: 'lightness'

            if pri_cl_lightness < 85  
              assign pri_cl_txt = "#ffffff"
            else 
              assign pri_cl_txt = "#010101"
            endif
            if pri_hover_cl_lightness < 85 
              assign pri_hover_cl_txt = "#ffffff"
            else 
              assign pri_hover_cl_txt = "#010101"
            endif
            if second_cl_lightness < 85 
              assign second_cl_txt = "#ffffff"
            else 
              assign second_cl_txt = "#010101"
            endif
            if second_hover_cl_lightness < 85 
              assign second_hover_cl_txt = "#ffffff"
            else 
              assign second_hover_cl_txt = "#010101"
            endif
          -%}
          <div  id="bk_{{ block.id }}" class="type_mn_pr bee-menu-item bee-sub-column-item bee-col-item bee-col-{{ bk_stts.col }}">
            {% if bk_stts.mg_heading != blank and bk_stts.use_heading %}<h5 class="bee-heading-menu bee-d-block">{{ bk_stts.mg_heading }}</h5>{% endif %}
            <div class="bee-list-collections bee-collection-des-{{ collection_des }} bee-collection-pos-{{ bk_stts.content_pos }} bee_{{ image_ratio }} bee_position_{{ bk_stts.image_position }} bee_{{ bk_stts.image_size }} bee-row bee-row-cols-{{ bk_stts.col_dk }} bee-gx-{{ bk_stts.space_h_item }} bee-gy-{{ bk_stts.space_v_item }}" {{ block.shopify_attributes }} style="--pri-cl: {{ bk_stts.pri_cl }};--pri-cl-txt: {{ pri_cl_txt }};--pri-hover-cl: {{ bk_stts.pri_hover_cl }};--pri-hover-cl-txt: {{ pri_hover_cl_txt }};--second-cl: {{ bk_stts.second_cl }};--second-cl-txt: {{ second_cl_txt }};--second-hover-cl: {{ bk_stts.second_hover_cl }};--second-hover-cl-txt: {{ second_hover_cl_txt }};--item-rd:{{ bk_stts.item_rd }}%;--item-width: {{ bk_stts.content_width }}px;--item-height: {{ bk_stts.content_height }}px;--item-width-mb: {{ bk_stts.content_width_mb }}px;--item-height-mb: {{ bk_stts.content_height_mb }}px;--space-bottom: {{ bk_stts.space_bottom }}px;--space-bottom-mb: {{ bk_stts.space_bottom_mb }}px;--title-fs: {{ bk_stts.title_fs }}px;--title-fw: {{ bk_stts.title_fw }};">
              {%- capture current -%}{%- cycle 1, 2, 3, 4, 5, 6 -%}{%- endcapture -%}
                {% for collection in collection_list %}
                  {%- liquid
                    assign collection = collection
                    assign image = collection.metafields.theme.menu_image | default: collection.image | default: collection.products.first.featured_image
                    assign title = collection.metafields.theme.menu_title | default: collection.title
                    assign collection_link = collection.url
                  -%}
                  <div class="bee-col-item bee-collection-item bee-text-{{ bk_stts.content_align }}" id="b_{{ block.id }}" data-select-flickity {{ block.shopify_attributes }}>
                    <div class="bee-collection-content bee-pr bee-eff-img-{{ img_effect }}">
                      <div class="bee-coll-img bee-pr bee-oh bee-eff bee-eff-{{ b_effect }}" data-cacl-slide>
                        <a class="bee_collection_item_link bee-img-wrap bee-d-block" href="{{ collection_link }}" target="{{ open_link }}">
                          <div class="bee_ratio bee-bg-11" style="--aspect-ratioapt: {{ image.aspect_ratio | default: 1.2 }}; {%- if image != blank -%}background: url({{ image | image_url: width: 1 }}); {%- endif -%}" >
                            {%- if image != blank -%}
                              <img class="lazyloadbee bee-lz--fadeIn bee-obj-eff" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                            {%- else -%}
                              {{ 'collection-' | append: current | placeholder_svg_tag: 'bee-placeholder-svg bee-obj-eff' }}  
                            {%- endif -%}
                          </div>
                        </a>
                      </div>
                      {%- if title != blank -%}
                        {% if collection_des != "9" %} 
                          <div class="bee-collection-wrapper">
                            <span>
                              <a class="bee-collection-title" href="{{ collection_link }}" target="{{ open_link }}">
                                <span class="bee-text">{{ title }}</span>
                              </a>
                            </span>
                          </div>
                        {% endif %}
                      {%- endif -%}
                    </div> 
                  </div>
                {% endfor %}
            </div>
          </div>
        {%- when 'pr_loop' -%}
          {{ 'bee-collection-products.css' | asset_url | stylesheet_tag }}
          <link href="{{ 'loading.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
          {%-liquid
            assign show_vendor = bk_stts.show_vendor
            assign enable_rating = settings.enable_rating
            assign limit = bk_stts.limit
            assign tcat = bk_stts.collection | default: "all"
            assign collection = collections[tcat]
            assign collection = collections[bk_stts.collection]
            if settings.within_cat and collection
              assign isHasCollection = true
            else 
              assign isHasCollection = false
            endif
            assign placeholder_img = settings.placeholder_img
            assign price_varies_style = settings.price_varies_style
            if bk_stts.btn_owl == "outline"
              assign arrow_icon = 1
            else
              assign arrow_icon = 2
            endif
            assign image_ratio = bk_stts.image_ratio
            if image_ratio == "ratioadapt"
              assign imgatt = ''
             else 
              assign imgatt = 'data-'
            endif

            assign show_img = settings.show_img
            assign isGrowaveWishlist = false
            if settings.wishlist_mode == "3" and shop.customer_accounts_enabled
              assign isGrowaveWishlist = true
            endif
            assign enable_pr_size = settings.enable_pr_size
            assign pr_size_pos = settings.pr_size_pos
            assign show_size_type = settings.show_size_type
            assign size_ck = settings.size_ck | append: ',size,sizes,Größe' 
            assign get_size = size_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

            assign enable_pr_color = settings.enable_pr_color
            assign show_cl_type = settings.show_color_type
            assign color_ck = settings.color_ck | append: ',color,colors,couleur,colour'
            assign get_color = color_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

            assign app_review = settings.app_review
            assign use_countdown = bk_stts.use_cdt
          -%}
          {%- paginate collection.products by limit -%}
          <div id="bk_{{ block.id }}" class="type_mn_pr bee-menu-item bee-sub-column-item bee-col-{{ bk_stts.col }} bee-col-item bee-products-loop" {{ block.shopify_attributes }}>
            {% if bk_stts.heading != blank %}<h4 class="bee-heading bee-h-has-border-{{ bk_stts.use_bd }}">{{ bk_stts.heading }}</h4>{% endif %}
            <div data-collection-url="{{ collection.url }}" class="bee_box_pr_grid bee-products bee-justify-content-center bee_{{ image_ratio }} bee_position_{{ bk_stts.image_position }} bee_{{ bk_stts.image_size }}">
              {%- if collection != blank -%}{%- if isHasCollection -%}{% assign pr_url = product.url | within: collection %}{% else %}{%-assign pr_url = product.url %}{% endif -%}
                {%- for product in collection.products limit: limit -%}
                  {%- render 'pr-loop-item', imgatt: imgatt, product: product, pr_url: pr_url, placeholder_img: placeholder_img, price_varies_style: price_varies_style -%} 
                {% endfor %}
              {%- else -%}
                {%- for i in (1..6) limit: limit -%}
                  <div class="bee-col-item">
                    <div class="bee-row bee-space-item-inner bee-widget__pr">
                      <div class="bee-col-item bee-col bee-widget_img_pr">
                        {%- if image != blank -%}
                          <a class="bee-d-block" data-cacl-slide href="/admin/products">{%- capture current -%}{%- cycle 1, 2, 3, 4, 5, 6 -%}{%- endcapture -%} 
                          {{ 'product-' | append: current | placeholder_svg_tag: 'bee-placeholder-svg' }}</a>
                        {%- endif -%} 
                      </div>
                      <div class="bee-col-item bee-col bee-widget_if_pr">
                        {%- render 'product-rating', product: product, app_review: app_review -%}
                        <a href="/admin/products" class="bee-d-block bee-widget__pr-title">{{ 'onboarding.product_title' | t }}</a>
                        <span class="bee-product-price">$330.00</span>
                      </div>
                    </div>
                  </div>
                {%- endfor -%}
              {%- endif -%} 
            </div>
          </div>
          {%- endpaginate -%}
        {%- when 'pr' -%}
          {{ 'bee-collection-products.css' | asset_url | stylesheet_tag }}
          <link href="{{ 'loading.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
          {%-liquid
            assign show_vendor = bk_stts.show_vendor
            assign enable_rating = settings.enable_rating
            assign limit = bk_stts.limit
            assign product_des = bk_stts.product_des
            assign tcat = bk_stts.collection | default: "all"
            assign collection = collections[tcat]
            assign collection = collections[bk_stts.collection]
            if bk_stts.btn_owl == "outline"
              assign arrow_icon = 1
            else
              assign arrow_icon = 2
            endif
            assign image_ratio = bk_stts.image_ratio
            if image_ratio == "ratioadapt"
              assign imgatt = ''
             else 
              assign imgatt = 'data-'
            endif

            assign layout_des = bk_stts.layout_des

            assign show_img = settings.show_img
            assign isGrowaveWishlist = false
            if settings.wishlist_mode == "3" and shop.customer_accounts_enabled
              assign isGrowaveWishlist = true
            endif
            assign enable_pr_size = settings.enable_pr_size
            assign pr_size_pos = settings.pr_size_pos
            assign show_size_type = settings.show_size_type
            assign size_ck = settings.size_ck | append: ',size,sizes,Größe' 
            assign get_size = size_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

            assign enable_pr_color = settings.enable_pr_color
            assign show_cl_type = settings.show_color_type
            assign color_ck = settings.color_ck | append: ',color,colors,couleur,colour'
            assign get_color = color_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

            assign price_varies_style = settings.price_varies_style
            assign app_review = settings.app_review
            assign use_countdown = bk_stts.use_cdt
            assign enable_rating = settings.enable_rating
            if enable_rating
              assign rating_pos = bk_stts.rating_pos
            endif
            assign placeholder_img = settings.placeholder_img
          -%}
          {%- paginate collection.products by limit -%}
          <div id="bk_{{ block.id }}" class="type_mn_pr bee-menu-item bee-sub-column-item bee-col-{{ bk_stts.col }} bee-col-item" {{ block.shopify_attributes }}>
            {% if bk_stts.mg_heading != blank and bk_stts.use_heading %}<h5 class="bee-heading-menu bee-d-block">{{ bk_stts.mg_heading }}</h5>{% endif %}
            {%- if layout_des == "1" -%} 
            <div data-collection-url="{{ collection.url }}" class="bee_box_pr_grid bee-products bee-justify-content-center bee-text-{{ bk_stts.content_align }} bee_{{ image_ratio }} bee_position_{{ bk_stts.image_position }} bee_{{ bk_stts.image_size }} bee-row bee-row-cols-{{ bk_stts.col_dk }} bee-gx-{{ bk_stts.space_h_item }} bee-gy-{{ bk_stts.space_v_item }}">
           {%- else layout_des == "2" -%} 
            {{ 'slider-settings.css' | asset_url | stylesheet_tag }}
            {{ 'pre_flickitybee.min.css' | asset_url | stylesheet_tag }}
            <div data-collection-url="{{ collection.url }}" class="bee-flickity-slider bee_box_pr_slider bee-products bee-text-{{ bk_stts.content_align }} bee_{{ image_ratio }} bee_position_{{ bk_stts.image_position }} bee_{{ bk_stts.image_size }} bee-row bee-row-cols-{{ bk_stts.col_dk }} bee-gx-{{ bk_stts.space_h_item }} bee-gy-{{ bk_stts.space_v_item }} {% if bk_stts.nav_btn %} bee-slider-btn__{{ bk_stts.btn_pos }}-content bee-slider-btn-style-{{ bk_stts.btn_owl }} bee-slider-btn-{{ bk_stts.btn_shape }} bee-slider-btn-{{ bk_stts.btn_size }} bee-slider-btn-cl-{{ bk_stts.btn_cl }} bee-slider-btn-vi-{{ bk_stts.btn_vi }} bee-slider-btn-hidden-mobile-{{ bk_stts.btn_hidden_mobile }} {% endif %} flickitybee flickitybee-later" data-flickitybee-js='{"setPrevNextButtons": true,"arrowIcon": "{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": true,"prevNextButtons": {{ bk_stts.nav_btn }},"percentPosition": 1,"pageDots": false, "autoPlay" : false, "pauseAutoPlayOnHover" : true }'>
            {%- endif -%} 
              {%- if collection != blank -%}
              {%- liquid 
                case product_des
                  when '1'
                    render 'pr-grid-item1' for collection.products as product, product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, rating_pos: rating_pos, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
                  when '2'
                    render 'pr-grid-item2' for collection.products as product, product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, rating_pos: rating_pos, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
                  when '3'
                    render 'pr-grid-item3' for collection.products as product, product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, rating_pos: rating_pos, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
                  when '4'
                    render 'pr-grid-item4' for collection.products as product, product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, rating_pos: rating_pos, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
                  when '5'
                    render 'pr-grid-item5' for collection.products as product, product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, rating_pos: rating_pos, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
                  when '6'
                    render 'pr-grid-item6' for collection.products as product, product: product, isGrowaveWishlist: isGrowaveWishlist, show_img: show_img, show_vendor: show_vendor, use_link_vendor: use_link_vendor, rating_pos: rating_pos, enable_pr_color: enable_pr_color, show_cl_type: show_cl_type, get_color: get_color, enable_pr_size: enable_pr_size, pr_size_pos: pr_size_pos, get_size: get_size, show_size_type: show_size_type, price_varies_style: price_varies_style, app_review: app_review, enable_rating: enable_rating, use_countdown: use_countdown, imgatt: imgatt, show_list_btns: false, placeholder_img: placeholder_img
                endcase -%}
              {%- else -%}
                {%- for i in (1..18) limit: limit -%}
                  <div class="bee-col-item bee-product bee-pr-grid bee-pr-style{{ product_des }} bee-pr-item">
                    <div class="bee-product-wrapper" data-cacl-slide >
                      <div class="bee-product-inner">
                        <a class="bee-d-block" data-cacl-slide href="/admin/products">{%- capture current -%}{%- cycle 1, 2, 3, 4, 5, 6 -%}{%- endcapture -%} 
                        {{ 'product-' | append: current | placeholder_svg_tag: 'bee-placeholder-svg' }}</a>
                      </div>
                      <div class="bee-product-info">
                        <div class="bee-product-info__inner">
                          <h3 class="bee-product-title"><a href="/admin/products">{{ 'onboarding.product_title' | t }}</a></h3>
                          <span class="bee-product-price"><del>$59.00</del><ins>$39.00</ins></span>
                        </div>
                      </div>
                    </div>
                  </div>
                {%- endfor -%}
              {%- endif -%} 
            </div>
          </div>
          {%- endpaginate -%}
        {%- when 'link' -%}
            {%- liquid 
              assign llists = linklists[bk_stts.menu].links
              if bk_stts.url != blank
                assign ARRhtml = 'a,,' | split: ','
              else
                assign ARRhtml = 'div,data-,data-' | split: ','
              endif
            -%}
           {%- if llists.size == 0 -%}{% continue %}{%- endif -%}
           <div id="bk_{{ block.id }}" class="type_mn_link bee-menu-item bee-sub-column-item bee-col-{{ bk_stts.col }} bee-col-item" {{ block.shopify_attributes }}>
            {%- if bk_stts.tt_hd != blank %}<{{ ARRhtml[0] }} {{ ARRhtml[1] }}href="{{ bk_stts.url }}"  {{ ARRhtml[2] }}target="{{ bk_stts.open_link }}" class="bee-heading bee-h-has-border-{{ bk_stts.use_bd }}">{%- render 'title_menu2', title: bk_stts.tt_hd -%}</{{ ARRhtml[0] }}>{% endif -%}
            <ul class="bee-sub-column{% if bk_stts.tt_hd != blank %} not_tt_mn{% endif %}">{%- for link in llists -%}
            <li class="bee-menu-item{% if link.active %} is--current{% endif %}"><a href="{{ link.url }}">{%- render 'title_menu2', title: link.title -%}</a></li>
            {%- endfor -%}</ul>
           </div>
        {%- when 'link2' -%}{%- assign llists = linklists[bk_stts.menu].links -%}
           {%- if llists.size == 0 -%}{% continue %}{%- endif -%}
           <div id="bk_{{ block.id }}" class="type_mn_link2 bee-menu-item bee-sub-column-item bee-col-{{ bk_stts.col }} bee-col-item" {{ block.shopify_attributes }}>
            {%- for link in llists -%}<a href="{{ link.url }}">{%- render 'title_menu2', title: link.title -%}</a>{%- endfor -%}
           </div>  
        {%- when 'html' -%}
          <div id="bk_{{ block.id }}" class="type_mn_html bee-menu-item bee-sub-column-item bee-col-{{ bk_stts.col }} bee-col-item bee-rte" {{ block.shopify_attributes }}>{{- bk_stts.html -}}{% if bk_stts.page != blank %}{{ pages[bk_stts.page].content }}{% endif -%}
          </div>
        {%- when 'blogs' -%}
          {{ 'bee-blogs.css' | asset_url | stylesheet_tag }}
          <link href="{{ 'bee-loading.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
          {%-liquid
            assign layout_des = bk_stts.layout_des
            if bk_stts.btn_owl == "outline"
              assign arrow_icon = 1
            else
              assign arrow_icon = 2
            endif
            assign image_ratio = bk_stts.image_ratio
            if image_ratio == "ratioadapt"
              assign imgatt = ''
             else 
              assign imgatt = 'data-'
            endif

            assign blog = blogs[bk_stts.blog]
            assign blog_url = blog.url
            assign blog_title = blog.title
            assign limit = bk_stts.limit
            assign post_des = bk_stts.post_des
            assign by_txt = 'blogs.article.by' | t
            assign on_txt = 'blogs.article.on' | t
            assign readmore_txt = 'blogs.article.read_more' | t
            assign date_format = bk_stts.date
            assign show_tags = bk_stts.show_tags
            assign b_effect = bk_stts.b_effect
            assign img_effect = bk_stts.img_effect
            assign show_content = bk_stts.show_content
            assign show_author = bk_stts.show_author
            assign show_comment = bk_stts.show_comment
            assign show_date = bk_stts.show_date
            assign show_readmore = bk_stts.show_readmore
            assign show_blog_title = bk_stts.show_blog_title

            assign col_dk = bk_stts.col_dk
            if post_des == '4'
              if col_dk != '1'
                 assign col_dk = '2'
              endif
              if col_tb != '1'
                 assign col_tb = '2'
              endif
            endif
          -%}
          {%- if post_des == '1' or post_des == '2' or post_des == '5' and col_dk == '1' -%}{{ 'bee-blogs-1col.css' | asset_url | stylesheet_tag }}{%- endif -%}
          {%- paginate blog.articles by limit -%}
          <div id="bk_{{ block.id }}" class="type_mn_pr bee-menu-item bee-sub-column-item bee-col-{{ bk_stts.col }} bee-col-item bee-blogs-bg-{{ bk_stts.use_bg }}" {{ block.shopify_attributes }}>
            {%- if bk_stts.tt_hd != blank %}<div class="bee-heading bee-h-has-border-{{ bk_stts.use_bd }}">{%- render 'title_menu2', title: bk_stts.tt_hd -%}</div>{% endif -%}
            {%- if layout_des == "1" -%} 
            <div class="blog_grid has_post_des_{{ post_des }} bee-justify-content-center bee-text-{{ bk_stts.content_align }} bee_{{ image_ratio }} bee_position_{{ bk_stts.image_position }} bee_{{ bk_stts.image_size }} bee-row bee-row-cols-{{ bk_stts.col_dk }} bee-gx-{{ bk_stts.space_h_item }} bee-gy-{{ bk_stts.space_v_item }}">
           {%- else layout_des == "2" -%} 
            {{ 'bee-slider-settings.css' | asset_url | stylesheet_tag }}
            {{ 'pre_flickitybee.min.css' | asset_url | stylesheet_tag }}
            <div class="bee-flickity-slider blog_slider has_post_des_{{ post_des }} bee-text-{{ bk_stts.content_align }} bee_{{ image_ratio }} bee_position_{{ bk_stts.image_position }} bee_{{ bk_stts.image_size }} bee-row bee-row-cols-{{ bk_stts.col_dk }} bee-gx-{{ bk_stts.space_h_item }} bee-gy-{{ bk_stts.space_v_item }} {% if bk_stts.nav_btn %} bee-slider-btn__{{ bk_stts.btn_pos }}-content bee-slider-btn-style-{{ bk_stts.btn_owl }} bee-slider-btn-{{ bk_stts.btn_shape }} bee-slider-btn-{{ bk_stts.btn_size }} bee-slider-btn-cl-{{ bk_stts.btn_cl }} bee-slider-btn-vi-{{ bk_stts.btn_vi }} bee-slider-btn-hidden-mobile-{{ bk_stts.btn_hidden_mobile }} {% endif %} flickitybee flickitybee-later" data-flickitybee-js='{"setPrevNextButtons": true,"arrowIcon": "{{ arrow_icon }}","imagesLoaded": 0,"adaptiveHeight": 0, "contain": 1, "groupCells": "100%", "dragThreshold" : 5, "cellAlign": "left","wrapAround": true,"prevNextButtons": {{ bk_stts.nav_btn }},"percentPosition": 1,"pageDots": false, "autoPlay" : false, "pauseAutoPlayOnHover" : true }'>
            {%- endif -%} 
              {%- if blog != blank -%} 
                {%- case post_des -%} 
                  {%- when '1' -%}
                    {%- render 'article_loop_1' for blog.articles as article,
                    article: article, show_blog_title: show_blog_title, blog_url: blog_url, blog_title: blog_title, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_date: show_date, show_author: show_author, show_comment: show_comment, show_tags: show_tags, show_content: show_content, show_readmore: show_readmore, date_format: date_format, readmore_txt: readmore_txt -%}
                  {%- when '2' -%}
                    {%- render 'article_loop_2' for blog.articles as article,
                    article: article, show_blog_title: show_blog_title, blog_url: blog_url, blog_title: blog_title, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_date: show_date, show_author: show_author, show_comment: show_comment, show_tags: show_tags, show_content: show_content, show_readmore: show_readmore, date_format: date_format, readmore_txt: readmore_txt -%}
                  {%- when '3' -%}
                    {%- render 'article_loop_3' for blog.articles as article,
                    article: article, show_blog_title: show_blog_title, blog_url: blog_url, blog_title: blog_title, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_date: show_date, show_author: show_author, show_comment: show_comment, show_tags: show_tags, show_content: show_content, show_readmore: show_readmore, date_format: date_format, readmore_txt: readmore_txt -%}
                  {%- when '4' -%}
                    {%- render 'article_loop_4' for blog.articles as article,
                    article: article, show_blog_title: show_blog_title, blog_url: blog_url, blog_title: blog_title, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_date: show_date, show_author: show_author, show_comment: show_comment, show_tags: show_tags, show_content: show_content, show_readmore: show_readmore, date_format: date_format, readmore_txt: readmore_txt -%}
                  {%- when '5' -%}
                    {%- render 'article_loop_5' for blog.articles as article,
                    article: article, show_blog_title: show_blog_title, blog_url: blog_url, blog_title: blog_title, by_txt: by_txt, on_txt: on_txt, b_effect: b_effect, img_effect: img_effect, show_date: show_date, show_author: show_author, show_comment: show_comment, show_tags: show_tags, show_content: show_content, show_readmore: show_readmore, date_format: date_format, readmore_txt: readmore_txt -%}
                {%- endcase -%}
              {%- else -%}
                {%- for i in (1..6) limit: limit -%}
                  <article class="bee-col-item bee-article-item bee-article-design1">
                      <div class="bee-article-inner bee-pr bee-oh">
                          <div class="bee-article-thumb bee-pr">
                              <a class="bee-eff bee-eff-{{ b_effect }} bee-eff-img-{{ img_effect }} bee-d-block" href="/admin/blogs" data-cacl-slide >
                                  {% capture current %}{% cycle 1, 2 %}{% endcapture %}
                                  <div class="bee_ratio" style="--aspect-ratioapt: {{ image.aspect_ratio | default: 2 }}">
                                      {{ 'lifestyle-' | append: current | placeholder_svg_tag: 'bee-placeholder-svg bee-obj-eff' }}  
                                  </div> 
                              </a>
                              {%- if show_date -%}
                                  <div class="bee-article-time2">
                                      <div class="bee-article-time__top">28</div>
                                      <div class="bee-article-time__bottom">May</div>
                                  </div>    
                              {%- endif -%}
                          </div>
                          <div class="bee-article-content__wrapper">
                              <div class="bee-article-info">
                                  {%- if show_tags -%}
                                      <div class="bee-article-tags">
                                          <a href="/admin/blogs">Fashion</a>, <a href="/admin/blogs">Lifestyle</a>
                                      </div>
                                  {%- endif -%}
                                  <h3 class="bee-article-title"><a href="/admin/blogs">{{ 'onboarding.blog_title' | t }}</a></h3>
                              </div>
                              {%- if show_content -%}
                                  <div class="bee-article-content bee-rte">{{ 'onboarding.blog_excerpt' | t }}</div>
                              {%- endif -%}
                              <div class="bee-article-metas">
                                  {%- if show_author -%}
                                      <div class="bee-article-author">
                                          <span class="bee-article-author__icon">
                                              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Capa_1" x="0px" y="0px" viewBox="0 0 512 512" fill="currentColor" xml:space="preserve">
                                                  <path d="M437.02,330.98c-27.883-27.882-61.071-48.523-97.281-61.018C378.521,243.251,404,198.548,404,148 C404,66.393,337.607,0,256,0S108,66.393,108,148c0,50.548,25.479,95.251,64.262,121.962 c-36.21,12.495-69.398,33.136-97.281,61.018C26.629,379.333,0,443.62,0,512h40c0-119.103,96.897-216,216-216s216,96.897,216,216 h40C512,443.62,485.371,379.333,437.02,330.98z M256,256c-59.551,0-108-48.448-108-108S196.449,40,256,40 c59.551,0,108,48.448,108,108S315.551,256,256,256z"/>
                                              </svg>
                                          </span>
                                          <span class="bee-author-name"><span class="bee-article-text__by">{{ by_txt }} : </span>{{ 'onboarding.blog_author' | t }}</span>
                                      </div>
                                  {%- endif -%}
                                  {%- if show_comment -%}
                                      <div class="bee-article-comment">
                                          <span class="bee-article-comment__icon">
                                              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="feather feather-message-square"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>
                                          </span>
                                          <span class="bee-article-comment__count">{{ 'blogs.comments.with_count' | t: count: 19 }}</span>
                                      </div>
                                  {%- endif -%}
                              </div>
                              {%- if show_readmore %}
                                  <div class="bee-article-btn">
                                      <a href="/admin/blogs" class="bee-article-btn__readmore">{{ 'blogs.article.read_more' | t }}</a>
                                  </div>
                              {% endif -%}
                          </div>
                      </div>
                  </article>
              {%- endfor -%}
              {%- endif -%}
            </div> 
          </div>
          {%- endpaginate -%}
        {%- else -%}
          <style>#item_{{ bk_stts.id }} .sub-menu {display: none !important}</style>
      {%- endcase -%}

  {%- endfor -%}
  {%- if se_blocks_size > 0 -%}
  </div>
  [nt_mega_split1]
  {%- endif -%}
{%- endif -%}

{% schema %}
  {
   "name": "Mega menu",
   "tag": "div",
   "class": "bee-section bee-section-mega__menu bee_bk_flickity bee_tp_cdt bee-d-none",
   "settings": [
   ],
   "blocks": [
      {
        "type": "mega",
        "name": "Mega Menu (Parent)",
        "settings": [
          {
            "type": "range",
            "id": "id",
            "min": 1,
            "max": 30,
            "step": 1,
            "label": "ID",
            "unit": "#",
            "info": "ID connect mega menu.",
            "default": 1
          }
        ]
      },
      {
        "type": "link",
        "name": "Linklist (child)",
        "settings": [
          {
            "type": "text",
            "id": "tt_hd",
            "label": "Heading",
            "default": "Heading"
          },
          {
            "type": "checkbox",
            "id": "use_bd",
            "label": "Use border heading",
            "default": false
          },
         {
            "type": "url",
            "id": "url",
            "label": "Link"
         },
          {
            "type": "select",
            "id": "open_link",
            "options": [
              {
                "value": "_self",
                "label": "Current window"
              },
             {
                "value": "_blank",
                "label": "New window"
              }
            ],
            "label": "Open link in"
          },
          {
            "type": "link_list",
            "id": "menu",
            "label": "Add menu"
          },
           {
            "type": "select",
            "id": "col",
            "default": "4",
            "options": [
              {
                "value": "1",
                "label": "1/12 (8.333333%)"
              },
              {
                "value": "2",
                "label": "2/12 (16.666667%)"
              },
              {
                "value": "3",
                "label": "3/12 (25%)"
              },
              {
                "value": "4",
                "label": "4/12 (33.333333%)"
              },
              {
                "value": "5",
                "label": "5/12 (41.666667%)"
              },
              {
                "value": "6",
                "label": "6/12 (50%)"
              },
              {
                "value": "7",
                "label": "7/12 (58.333333%)"
              },
              {
                "value": "8",
                "label": "8/12 (66.666667%)"
              },
              {
                "value": "9",
                "label": "9/12 (75%)"
              },
              {
                "value": "10",
                "label": "10/12 (83.333333%)"
              },
              {
                "value": "11",
                "label": "11/12 (91.666667%)"
              },
              {
                "value": "12",
                "label": "12/12 (100%)"
              }
            ],
            "label": "Width col:"
           }
        ]
      },
      {
        "type": "link2",
        "name": "Linklist 2 ( child )",
        "settings": [
          {
            "type": "select",
            "id": "col",
            "default": "4",
            "options": [
              {
                "value": "1",
                "label": "1/12 (8.333333%)"
              },
              {
                "value": "2",
                "label": "2/12 (16.666667%)"
              },
              {
                "value": "3",
                "label": "3/12 (25%)"
              },
              {
                "value": "4",
                "label": "4/12 (33.333333%)"
              },
              {
                "value": "5",
                "label": "2.4/12 (20%)"
              },
              {
                "value": "6",
                "label": "6/12 (50%)"
              },
              {
                "value": "7",
                "label": "7/12 (58.333333%)"
              },
              {
                "value": "8",
                "label": "8/12 (66.666667%)"
              },
              {
                "value": "9",
                "label": "9/12 (75%)"
              },
              {
                "value": "10",
                "label": "10/12 (83.333333%)"
              },
              {
                "value": "11",
                "label": "11/12 (91.666667%)"
              },
              {
                "value": "12",
                "label": "12/12 (100%)"
              }
            ],
            "label": "Width col:"
          },
          {
            "type": "link_list",
            "id": "menu", 
            "label": "Add menu"
          }
           
        ]
      },
      {
        "type": "pr",
        "name": "Product (child)",
        "settings": [
          {
            "type": "text",
            "id": "mg_heading",
            "label": "Heading",
            "default": "HEADING"
          },
          {
            "type": "checkbox",
            "id": "use_heading",
            "label": "Use heading",
            "default": false
          },
          {
              "id": "collection",
              "type": "collection",
              "label": "Collection" 
          },
          {
            "type": "select",
            "id": "product_des",
            "options": [
              {
                "value": "1",
                "label": "Design 1"
              },
              {
                "value": "2",
                "label": "Design 2"
              },
              {
                "value": "3",
                "label": "Design 3"
              },
              {
                "value": "4",
                "label": "Design 4"
              },
              {
                "value": "5",
                "label": "Design 5"
              },
              {
                "value": "6",
                "label": "Design 6"
              }
            ],
            "label": "Product item design",
            "default": "1"
          },
          {
            "type": "select",
            "id": "rating_pos",
            "info": "Only work when enable \"Rating\" in themes settings",
            "options": [
              {
                "value": "1",
                "label": "Above product name"
              },
              {
                "value": "2",
                "label": "Under product price"
              },
              {
                "value": "3",
                "label": "Hidden"
              }
            ],
            "label": "Rating position",
            "default": "1"
          },
          {
            "type": "checkbox",
            "id": "show_vendor",
            "label": "Show product vendors",
            "default": false
          },
          {
            "type": "checkbox",
            "id": "use_cdt",
            "label": "Show product countdown",
            "default": false
          },
          {
            "type": "header",
            "content": "+ Options image products"
          },
          {
            "type": "select",
            "id": "image_ratio",
            "label": "Image ratio",
            "default": "rationt",
            "info": "Aspect ratio custom will settings in general panel",
            "options": [
              {
                "group": "Natural",
                "value": "ratioadapt",
                "label": "Adapt to image"
              },
              {
                "group": "Landscape",
                "value": "ratio2_1",
                "label": "2:1"
              },
              {
                "group": "Landscape",
                "value": "ratio16_9",
                "label": "16:9"
              },
              {
                "group": "Landscape",
                "value": "ratio8_5",
                "label": "8:5"
              },
              {
                "group": "Landscape",
                "value": "ratio3_2",
                "label": "3:2"
              },
              {
                "group": "Landscape",
                "value": "ratio4_3",
                "label": "4:3"
              },
              {
                "group": "Landscape",
                "value": "rationt",
                "label": "Ratio ASOS"
              },
              {
                "group": "Squared",
                "value": "ratio1_1",
                "label": "1:1"
              },
              {
                "group": "Portrait",
                "value": "ratio2_3",
                "label": "2:3"
              },
              {
                "group": "Portrait",
                "value": "ratio1_2",
                "label": "1:2"
              },
              {
                "group": "Custom",
                "value": "ratiocus1",
                "label": "Ratio custom 1"
              },
              {
                "group": "Custom",
                "value": "ratiocus2",
                "label": "Ratio custom 2"
              },
              {
                "group": "Custom",
                "value": "ratiocus3",
                "label": "Ratio custom 3"
              },
              {
                "group": "Custom",
                "value": "ratiocus4",
                "label": "Ratio custom 4"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "cover",
                "label": "Full"
              },
              {
                "value": "contain",
                "label": "Auto"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "1",
                "label": "Left top"
              },
              {
                "value": "2",
                "label": "Left center"
              },
              {
                "value": "3",
                "label": "Left bottom"
              },
              {
                "value": "4",
                "label": "Right top"
              },
              {
                "value": "5",
                "label": "Right center"
              },
              {
                "value": "6",
                "label": "Right bottom"
              },
              {
                "value": "7",
                "label": "Center top"
              },
              {
                "value": "8",
                "label": "Center center"
              },
              {
                "value": "9",
                "label": "Center bottom"
              }
            ],
            "label": "Image position",
            "default": "8"
          },
          {
            "type": "select",
            "id": "content_align",
            "label": "Product content align",
            "default": "default",
            "options": [
              {
                "label": "Default",
                "value": "default"
              },
              {
                "label": "Center",
                "value": "center"
              }
            ]
          },
          {
            "type": "range",
            "id": "limit",
            "min": 1,
            "max": 20,
            "step": 1,
            "label": "Maximum products to show",
            "default": 4
          },
          {
            "type": "select",
            "id": "col_dk",
            "label": "Items per row",
            "default": "3",
            "options": [
              {
                "value": "1",
                "label": "1"
              },
              {
                "value": "2",
                "label": "2"
              },
              {
                "value": "3",
                "label": "3"
              },
              {
                "value": "4",
                "label": "4"
              },
              {
                "value": "5",
                "label": "5"
              },
              {
                "value": "6",
                "label": "6"
              }
            ]
          },
          {
            "type": "select",
            "id": "space_h_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space horizontal items",
            "default": "30"
          },
          {
            "type": "select",
            "id": "space_v_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space vertical items",
            "default": "30"
          },
          {
            "type": "header",
            "content": "--Box options--"
          },
          {
            "type": "select",
            "id": "layout_des",
            "options": [
              {
                "value": "1",
                "label": "Grid"
              },
              {
                "value": "2",
                "label": "Carousel"
              }
            ],
            "label": "Layout design",
            "default": "2"
          },
          {
            "type": "header",
            "content": "+Options for carousel layout"
          },
          {
            "type": "paragraph",
            "content": "Prev next button"
          },
          {
            "type": "checkbox",
            "id": "nav_btn",
            "label": "Use prev next button",
            "info": "Enable previous & next buttons",
            "default": false
          },
          {
            "type": "select",
            "id": "btn_vi",
            "label": "Visible",
            "default": "hover",
            "options": [
              {
                "value": "always",
                "label": "Always"
              },
              {
                "value": "hover",
                "label": "Only hover"
              }
            ]
          },
          {
            "type": "select",
            "id": "btn_pos",
            "label": "Prev next position",
            "info": "Working on screen Desktop",
            "default": "in",
            "options": [
                {
                    "label": "In content",
                    "value": "in"
                },
                {
                    "label": "Out content",
                    "value": "out"
                },
                {
                    "label": "Content side center",
                    "value": "between"
                }
            ]
          },
          {
            "type": "select",
            "id": "btn_owl",
            "label": "Button style",
            "default": "default",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "outline",
                "label": "Outline"
              },
              {
                "value": "simple",
                "label": "Simple"
              }
            ]
          },
          {
            "type": "select",
            "id": "btn_shape",
            "label": "Button shape",
            "info": "Not work for 'Simple' button style",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "Default"
              },
              {
                "value": "round",
                "label": "Round"
              },
              {
                "value": "rotate",
                "label": "Rotate"
              }
            ]
          },
          {
              "type": "select",
              "id": "btn_cl",
              "label": "Button color",
              "default": "dark",
              "options": [
                  {
                      "value": "light",
                      "label": "Light"
                  },
                  {
                      "value": "dark",
                      "label": "Dark"
                  },
                  {
                      "value": "primary",
                      "label": "Primary"
                  },
                  {
                      "value": "custom1",
                      "label": "Custom color 1"
                  },
                  {
                      "value": "custom2",
                      "label": "Custom color 2"
                  }
              ]
          },
          {
            "type": "select",
            "id": "btn_size",
            "label": "Button size",
            "default": "small",
            "options": [
              {
                "value": "small",
                "label": "Small"
              },
              {
                "value": "medium",
                "label": "Medium"
              },
              {
                "value": "large",
                "label": "Large"
              }
            ]
          },
          {
            "type": "checkbox",
            "id": "btn_hidden_mobile",
            "label": "Hidden buttons on mobile",
            "default": true
          },
          {
            "type": "select",
            "id": "col",
            "default": "4",
            "options": [
              {
                "value": "1",
                "label": "1/12 (8.333333%)"
              },
              {
                "value": "2",
                "label": "2/12 (16.666667%)"
              },
              {
                "value": "3",
                "label": "3/12 (25%)"
              },
              {
                "value": "4",
                "label": "4/12 (33.333333%)"
              },
              {
                "value": "5",
                "label": "5/12 (41.666667%)"
              },
              {
                "value": "6",
                "label": "6/12 (50%)"
              },
              {
                "value": "7",
                "label": "7/12 (58.333333%)"
              },
              {
                "value": "8",
                "label": "8/12 (66.666667%)"
              },
              {
                "value": "9",
                "label": "9/12 (75%)"
              },
              {
                "value": "10",
                "label": "10/12 (83.333333%)"
              },
              {
                "value": "11",
                "label": "11/12 (91.666667%)"
              },
              {
                "value": "12",
                "label": "12/12 (100%)"
              }
            ],
            "label": "Width col:"
           }
        ]
      },
      {
        "type": "pr_loop",
        "name": "Product loop (child)",
        "settings": [
          {
              "id": "heading",
              "type": "text",
              "label": "Heading",
              "default": "TOP RATE"
          },
          {
            "type": "checkbox",
            "id": "use_bd",
            "label": "Use border heading",
            "default": true
          },
          {
              "id": "collection",
              "type": "collection",
              "label": "Collection" 
          },
          {
            "type": "header",
            "content": "+ Options image products"
          },
          {
            "type": "select",
            "id": "image_ratio",
            "label": "Image ratio",
            "default": "rationt",
            "info": "Aspect ratio custom will settings in general panel",
            "options": [
              {
                "group": "Natural",
                "value": "ratioadapt",
                "label": "Adapt to image"
              },
              {
                "group": "Landscape",
                "value": "ratio2_1",
                "label": "2:1"
              },
              {
                "group": "Landscape",
                "value": "ratio16_9",
                "label": "16:9"
              },
              {
                "group": "Landscape",
                "value": "ratio8_5",
                "label": "8:5"
              },
              {
                "group": "Landscape",
                "value": "ratio3_2",
                "label": "3:2"
              },
              {
                "group": "Landscape",
                "value": "ratio4_3",
                "label": "4:3"
              },
              {
                "group": "Landscape",
                "value": "rationt",
                "label": "Ratio ASOS"
              },
              {
                "group": "Squared",
                "value": "ratio1_1",
                "label": "1:1"
              },
              {
                "group": "Portrait",
                "value": "ratio2_3",
                "label": "2:3"
              },
              {
                "group": "Portrait",
                "value": "ratio1_2",
                "label": "1:2"
              },
              {
                "group": "Custom",
                "value": "ratiocus1",
                "label": "Ratio custom 1"
              },
              {
                "group": "Custom",
                "value": "ratiocus2",
                "label": "Ratio custom 2"
              },
              {
                "group": "Custom",
                "value": "ratiocus3",
                "label": "Ratio custom 3"
              },
              {
                "group": "Custom",
                "value": "ratiocus4",
                "label": "Ratio custom 4"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "cover",
                "label": "Full"
              },
              {
                "value": "contain",
                "label": "Auto"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "1",
                "label": "Left top"
              },
              {
                "value": "2",
                "label": "Left center"
              },
              {
                "value": "3",
                "label": "Left bottom"
              },
              {
                "value": "4",
                "label": "Right top"
              },
              {
                "value": "5",
                "label": "Right center"
              },
              {
                "value": "6",
                "label": "Right bottom"
              },
              {
                "value": "7",
                "label": "Center top"
              },
              {
                "value": "8",
                "label": "Center center"
              },
              {
                "value": "9",
                "label": "Center bottom"
              }
            ],
            "label": "Image position",
            "default": "8"
          },
          {
            "type": "range",
            "id": "limit",
            "min": 1,
            "max": 50,
            "step": 1,
            "label": "Maximum products to show",
            "default": 8
          },
          {
            "type": "select",
            "id": "space_v_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space vertical items",
            "default": "30"
          },
          {
            "type": "select",
            "id": "col",
            "default": "4",
            "options": [
              {
                "value": "1",
                "label": "1/12 (8.333333%)"
              },
              {
                "value": "2",
                "label": "2/12 (16.666667%)"
              },
              {
                "value": "3",
                "label": "3/12 (25%)"
              },
              {
                "value": "4",
                "label": "4/12 (33.333333%)"
              },
              {
                "value": "5",
                "label": "5/12 (41.666667%)"
              },
              {
                "value": "6",
                "label": "6/12 (50%)"
              },
              {
                "value": "7",
                "label": "7/12 (58.333333%)"
              },
              {
                "value": "8",
                "label": "8/12 (66.666667%)"
              },
              {
                "value": "9",
                "label": "9/12 (75%)"
              },
              {
                "value": "10",
                "label": "10/12 (83.333333%)"
              },
              {
                "value": "11",
                "label": "11/12 (91.666667%)"
              },
              {
                "value": "12",
                "label": "12/12 (100%)"
              }
            ],
            "label": "Width col:"
           }
        ]
      },
      {
        "type": "cat",
        "name": "Collection (child)",
        "settings": [
          {
            "type": "select",
            "id": "collection_des",
            "label": "Collection item design",
            "default": "1",
            "options": [
              {
                "value": "1",
                "label": "Design 1"
              },
              {
                "value": "2",
                "label": "Design 2"
              },
              {
                "value": "3",
                "label": "Design 3"
              },
              {
                "value": "4",
                "label": "Design 4"
              },
              {
                "value": "5",
                "label": "Design 5"
              },
              {
                "value": "6",
                "label": "Design 6"
              },
              {
                "value": "7",
                "label": "Design 7"
              },
              {
                "value": "8",
                "label": "Design 8"
              },
              {
                "value": "9",
                "label": "Design 9  (Only image)"
              }
            ]
          },
          {
              "id": "collection",
              "type": "collection",
              "label": "Collection"
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "Collection image"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Collection label",
            "info" :"Leave empty to use 'Collection label'.",
            "default": "Collection "
          },
          {
              "type": "url",
              "id": "collection_link",
              "label": "Link (optional)",
              "info" :"Leave empty to use 'collection url'"
          },
          {
              "type": "text",
              "id": "button_label",
              "label": "Button label",
              "default": "Shop Now",
              "info": "Only use for collection design 3,5,6"
          },
          {
              "type": "range",
              "id": "title_fs",
              "label": "Title font size",
              "max": 100,
              "min": 10,
              "step": 1,
              "unit": "px",
              "default": 22
          },
          {
              "type": "range",
              "id": "title_fw",
              "label": "Title font weight",
              "min": 100,
              "max": 900,
              "step": 100,
              "default": 400
          },
          {
            "type": "color",
            "id": "pri_cl",
            "label": "Primary content color",
            "default": "#ffffff"
          },
          {
            "type": "color",
            "id": "second_cl",
            "label": "Secondary content color",
            "default": "#000000"
          },
          {
            "type": "color",
            "id": "pri_hover_cl",
            "label": "Primary content hover color",
            "default": "#000000"
          },
          {
            "type": "color",
            "id": "second_hover_cl",
            "label": "Secondary content hover color",
            "default": "#ffffff"
          }, 
          {
            "type": "select",
            "id": "content_align",
            "label": "Content align",
            "default": "center",
            "options": [
                {
                  "label": "Default",
                  "value": "default"
                },
                {
                  "label": "Center",
                  "value": "center"
                }
            ]
          },
          {
            "type": "select",
            "id": "content_pos",
            "label": "Content position",
            "info": "Only works with designs have content on images",
            "default": "1",
            "options": [
                {
                  "label": "Center center",
                  "value": "1"
                },
                {
                  "label": "Center bottom",
                  "value": "2"
                },
                {
                  "label": "Right bottom",
                  "value": "3"
                },
                {
                  "label": "Left bottom",
                  "value": "4"
                }
            ]
          },
          {
            "type": "range",
            "id": "space_bottom",
            "min": 0,
            "max": 60,
            "step": 1,
            "label": "Bottom space",
            "unit": "px",
            "default": 20,
            "info": "Space between image and content of collection. Not working with position 'center center'."
          },
          {
            "type": "range",
            "id": "space_bottom_mb",
            "min": 0,
            "max": 60,
            "step": 1,
            "label": "Bottom space (Mobile)",
            "unit": "px",
            "default": 10,
            "info": "Not working with position 'center center'."
          },
          {
            "type": "number",
            "id": "content_width",
            "label": "Content min width (px)",
            "default": 140,
            "info": "Only works with designs have content on images"
          },
          {
            "type": "number",
            "id": "content_height",
            "label": "Content min height (px)",
            "default": 40,
            "info": "Only works with designs have content on images"
          },
          {
            "type": "number",
            "id": "content_width_mb",
            "label": "Content min width (Mobile) (px)",
            "default": 100,
            "info": "Only works with designs have content on images"
          },
          {
            "type": "number",
            "id": "content_height_mb",
            "label": "Content min height (Mobile) (px)",
            "default": 40,
            "info": "Only works with designs have content on images"
          },
          {
            "type": "select",
            "id": "open_link",
            "info": "Works when the item has a link",
            "options": [
              {
                "value": "_self",
                "label": "Current window"
              },
             {
                "value": "_blank",
                "label": "New window"
              }
            ],
            "label": "Open link in",
            "default": "_self"
          },
          {
            "type": "header",
            "content": "+ Options image collection"
          },
          {
            "type": "range",
            "id": "item_rd",
            "min": 0,
            "max": 50,
            "step": 1,
            "label": "Image rounded",
            "unit": "%",
            "default": 0
          },
          {
            "type": "select",
            "id": "img_effect",
            "label": "Image hover effect",
            "info": "Waring: Hovering effect will resize your images",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "None"
              },
              {
                "value": "zoom",
                "label": "Zoom image"
              },
              {
                "value": "rotate",
                "label": "Rotate image "
              },
              {
                "value": "translateToTop",
                "label": "Move to top "
              },
              {
                "value": "translateToRight",
                "label": "Move to right"
              },
              {
                "value": "translateToBottom",
                "label": "Move to bottom"
              },
              {
                "value": "translateToLeft",
                "label": "Move to left"
              }
            ]
          },
          {
            "type": "select",
            "id": "b_effect",
            "label": "Collection hover effect",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "None"
              },
              {
                "value": "border-run",
                "label": "Border run"
              },
              {
                "value": "pervasive-circle",
                "label": "Pervasive circle"
              },
              {
                "value": "plus-zoom-overlay",
                "label": "Plus zoom overlay"
              },
              {
                "value": "dark-overlay",
                "label": "Dark overlay"
              },
              {
                "value": "light-overlay",
                "label": "Light overlay"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_ratio",
            "label": "Image ratio",
            "default": "ratio1_1",
            "info": "Aspect ratio custom will settings in general panel",
            "options": [
              {
                "group": "Natural",
                "value": "ratioadapt",
                "label": "Adapt to image"
              },
              {
                "group": "Landscape",
                "value": "ratio2_1",
                "label": "2:1"
              },
              {
                "group": "Landscape",
                "value": "ratio16_9",
                "label": "16:9"
              },
              {
                "group": "Landscape",
                "value": "ratio8_5",
                "label": "8:5"
              },
              {
                "group": "Landscape",
                "value": "ratio3_2",
                "label": "3:2"
              },
              {
                "group": "Landscape",
                "value": "ratio4_3",
                "label": "4:3"
              },
              {
                "group": "Landscape",
                "value": "rationt",
                "label": "Ratio ASOS"
              },
              {
                "group": "Squared",
                "value": "ratio1_1",
                "label": "1:1"
              },
              {
                "group": "Portrait",
                "value": "ratio2_3",
                "label": "2:3"
              },
              {
                "group": "Portrait",
                "value": "ratio1_2",
                "label": "1:2"
              },
              {
                "group": "Custom",
                "value": "ratiocus1",
                "label": "Ratio custom 1"
              },
              {
                "group": "Custom",
                "value": "ratiocus2",
                "label": "Ratio custom 2"
              },
              {
                "group": "Custom",
                "value": "ratiocus3",
                "label": "Ratio custom 3"
              },
              {
                "group": "Custom",
                "value": "ratiocus4",
                "label": "Ratio custom 4"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "cover",
                "label": "Full"
              },
              {
                "value": "contain",
                "label": "Auto"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "1",
                "label": "Left top"
              },
              {
                "value": "2",
                "label": "Left center"
              },
              {
                "value": "3",
                "label": "Left bottom"
              },
              {
                "value": "4",
                "label": "Right top"
              },
              {
                "value": "5",
                "label": "Right center"
              },
              {
                "value": "6",
                "label": "Right bottom"
              },
              {
                "value": "7",
                "label": "Center top"
              },
              {
                "value": "8",
                "label": "Center center"
              },
              {
                "value": "9",
                "label": "Center bottom"
              }
            ],
            "label": "Image position",
            "default": "8"
          },
          {
            "type": "select",
            "id": "col",
            "default": "4",
            "options": [
              {
                "value": "1",
                "label": "1/12 (8.333333%)"
              },
              {
                "value": "2",
                "label": "2/12 (16.666667%)"
              },
              {
                "value": "3",
                "label": "3/12 (25%)"
              },
              {
                "value": "4",
                "label": "4/12 (33.333333%)"
              },
              {
                "value": "5",
                "label": "5/12 (41.666667%)"
              },
              {
                "value": "6",
                "label": "6/12 (50%)"
              },
              {
                "value": "7",
                "label": "7/12 (58.333333%)"
              },
              {
                "value": "8",
                "label": "8/12 (66.666667%)"
              },
              {
                "value": "9",
                "label": "9/12 (75%)"
              },
              {
                "value": "10",
                "label": "10/12 (83.333333%)"
              },
              {
                "value": "11",
                "label": "11/12 (91.666667%)"
              },
              {
                "value": "12",
                "label": "12/12 (100%)"
              }
            ],
            "label": "Width col:"
          }
        ]
      },
      {
        "type": "cat_list",
        "name": "Collection list (child)",
        "settings": [
          {
            "type": "text",
            "id": "mg_heading",
            "label": "Heading",
            "default": "HEADING"
          },
          {
            "type": "checkbox",
            "id": "use_heading",
            "label": "Use heading",
            "default": false
          },
          {
            "type": "select",
            "id": "collection_des",
            "label": "Collection item design",
            "default": "1",
            "options": [
              {
                "value": "1",
                "label": "Design 1"
              },
              {
                "value": "2",
                "label": "Design 2"
              },
              {
                "value": "4",
                "label": "Design 3"
              },
              {
                "value": "7",
                "label": "Design 4"
              },
              {
                "value": "8",
                "label": "Design 5"
              },
              {
                "value": "9",
                "label": "Design 6  (Only image)"
              },
              {
                "value": "11",
                "label": "Design 7"
              }
            ]
          },
          {
              "id": "collection_list",
              "type": "collection_list",
              "label": "Collection list"
          },
          {
              "type": "range",
              "id": "title_fs",
              "label": "Title font size",
              "max": 100,
              "min": 10,
              "step": 1,
              "unit": "px",
              "default": 22
          },
          {
              "type": "range",
              "id": "title_fw",
              "label": "Title font weight",
              "min": 100,
              "max": 900,
              "step": 100,
              "default": 400
          },
          {
            "type": "color",
            "id": "pri_cl",
            "label": "Primary content color",
            "default": "#ffffff"
          },
          {
            "type": "color",
            "id": "second_cl",
            "label": "Secondary content color",
            "default": "#000000"
          },
          {
            "type": "color",
            "id": "pri_hover_cl",
            "label": "Primary content hover color",
            "default": "#000000"
          },
          {
            "type": "color",
            "id": "second_hover_cl",
            "label": "Secondary content hover color",
            "default": "#ffffff"
          }, 
          {
            "type": "select",
            "id": "content_align",
            "label": "Content align",
            "default": "center",
            "options": [
                {
                  "label": "Default",
                  "value": "default"
                },
                {
                  "label": "Center",
                  "value": "center"
                }
            ]
          },
          {
            "type": "select",
            "id": "content_pos",
            "label": "Content position",
            "info": "Only works with designs have content on images",
            "default": "1",
            "options": [
                {
                  "label": "Center center",
                  "value": "1"
                },
                {
                  "label": "Center bottom",
                  "value": "2"
                },
                {
                  "label": "Right bottom",
                  "value": "3"
                },
                {
                  "label": "Left bottom",
                  "value": "4"
                }
            ]
          },
          {
            "type": "range",
            "id": "space_bottom",
            "min": 0,
            "max": 60,
            "step": 1,
            "label": "Bottom space",
            "unit": "px",
            "default": 20,
            "info": "Space between image and content of collection. Not working with position 'center center'."
          },
          {
            "type": "range",
            "id": "space_bottom_mb",
            "min": 0,
            "max": 60,
            "step": 1,
            "label": "Bottom space (Mobile)",
            "unit": "px",
            "default": 10,
            "info": "Not working with position 'center center'."
          },
          {
            "type": "number",
            "id": "content_width",
            "label": "Content min width (px)",
            "default": 140,
            "info": "Only works with designs have content on images"
          },
          {
            "type": "number",
            "id": "content_height",
            "label": "Content min height (px)",
            "default": 40,
            "info": "Only works with designs have content on images"
          },
          {
            "type": "number",
            "id": "content_width_mb",
            "label": "Content min width (Mobile) (px)",
            "default": 100,
            "info": "Only works with designs have content on images"
          },
          {
            "type": "number",
            "id": "content_height_mb",
            "label": "Content min height (Mobile) (px)",
            "default": 40,
            "info": "Only works with designs have content on images"
          },
          {
            "type": "select",
            "id": "open_link",
            "info": "Works when the item has a link",
            "options": [
              {
                "value": "_self",
                "label": "Current window"
              },
             {
                "value": "_blank",
                "label": "New window"
              }
            ],
            "label": "Open link in",
            "default": "_self"
          },
          {
            "type": "header",
            "content": "+ Options image collection"
          },
          {
            "type": "range",
            "id": "item_rd",
            "min": 0,
            "max": 50,
            "step": 1,
            "label": "Image rounded",
            "unit": "%",
            "default": 0
          },
          {
            "type": "select",
            "id": "img_effect",
            "label": "Image hover effect",
            "info": "Waring: Hovering effect will resize your images",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "None"
              },
              {
                "value": "zoom",
                "label": "Zoom image"
              },
              {
                "value": "rotate",
                "label": "Rotate image "
              },
              {
                "value": "translateToTop",
                "label": "Move to top "
              },
              {
                "value": "translateToRight",
                "label": "Move to right"
              },
              {
                "value": "translateToBottom",
                "label": "Move to bottom"
              },
              {
                "value": "translateToLeft",
                "label": "Move to left"
              }
            ]
          },
          {
            "type": "select",
            "id": "b_effect",
            "label": "Collection hover effect",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "None"
              },
              {
                "value": "border-run",
                "label": "Border run"
              },
              {
                "value": "pervasive-circle",
                "label": "Pervasive circle"
              },
              {
                "value": "plus-zoom-overlay",
                "label": "Plus zoom overlay"
              },
              {
                "value": "dark-overlay",
                "label": "Dark overlay"
              },
              {
                "value": "light-overlay",
                "label": "Light overlay"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_ratio",
            "label": "Image ratio",
            "default": "ratio1_1",
            "info": "Aspect ratio custom will settings in general panel",
            "options": [
              {
                "group": "Natural",
                "value": "ratioadapt",
                "label": "Adapt to image"
              },
              {
                "group": "Landscape",
                "value": "ratio2_1",
                "label": "2:1"
              },
              {
                "group": "Landscape",
                "value": "ratio16_9",
                "label": "16:9"
              },
              {
                "group": "Landscape",
                "value": "ratio8_5",
                "label": "8:5"
              },
              {
                "group": "Landscape",
                "value": "ratio3_2",
                "label": "3:2"
              },
              {
                "group": "Landscape",
                "value": "ratio4_3",
                "label": "4:3"
              },
              {
                "group": "Landscape",
                "value": "rationt",
                "label": "Ratio ASOS"
              },
              {
                "group": "Squared",
                "value": "ratio1_1",
                "label": "1:1"
              },
              {
                "group": "Portrait",
                "value": "ratio2_3",
                "label": "2:3"
              },
              {
                "group": "Portrait",
                "value": "ratio1_2",
                "label": "1:2"
              },
              {
                "group": "Custom",
                "value": "ratiocus1",
                "label": "Ratio custom 1"
              },
              {
                "group": "Custom",
                "value": "ratiocus2",
                "label": "Ratio custom 2"
              },
              {
                "group": "Custom",
                "value": "ratiocus3",
                "label": "Ratio custom 3"
              },
              {
                "group": "Custom",
                "value": "ratiocus4",
                "label": "Ratio custom 4"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "cover",
                "label": "Full"
              },
              {
                "value": "contain",
                "label": "Auto"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "1",
                "label": "Left top"
              },
              {
                "value": "2",
                "label": "Left center"
              },
              {
                "value": "3",
                "label": "Left bottom"
              },
              {
                "value": "4",
                "label": "Right top"
              },
              {
                "value": "5",
                "label": "Right center"
              },
              {
                "value": "6",
                "label": "Right bottom"
              },
              {
                "value": "7",
                "label": "Center top"
              },
              {
                "value": "8",
                "label": "Center center"
              },
              {
                "value": "9",
                "label": "Center bottom"
              }
            ],
            "label": "Image position",
            "default": "8"
          },
          {
            "type": "select",
            "id": "col_dk",
            "label": "Items per row",
            "default": "1",
            "options": [
              {
                "value": "1",
                "label": "1"
              },
              {
                "value": "2",
                "label": "2"
              },
              {
                "value": "3",
                "label": "3"
              },
              {
                "value": "4",
                "label": "4"
              },
              {
                "value": "5",
                "label": "5"
              },
              {
                "value": "6",
                "label": "6"
              }
            ]
          },
          {
            "type": "select",
            "id": "space_h_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space horizontal items",
            "default": "0"
          },
          {
            "type": "select",
            "id": "space_v_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space vertical items",
            "default": "15"
          },
          {
            "type": "select",
            "id": "col",
            "default": "4",
            "options": [
              {
                "value": "1",
                "label": "1/12 (8.333333%)"
              },
              {
                "value": "2",
                "label": "2/12 (16.666667%)"
              },
              {
                "value": "3",
                "label": "3/12 (25%)"
              },
              {
                "value": "4",
                "label": "4/12 (33.333333%)"
              },
              {
                "value": "5",
                "label": "5/12 (41.666667%)"
              },
              {
                "value": "6",
                "label": "6/12 (50%)"
              },
              {
                "value": "7",
                "label": "7/12 (58.333333%)"
              },
              {
                "value": "8",
                "label": "8/12 (66.666667%)"
              },
              {
                "value": "9",
                "label": "9/12 (75%)"
              },
              {
                "value": "10",
                "label": "10/12 (83.333333%)"
              },
              {
                "value": "11",
                "label": "11/12 (91.666667%)"
              },
              {
                "value": "12",
                "label": "12/12 (100%)"
              }
            ],
            "label": "Width col:"
          }
        ]
      },
      {
        "type": "banner",
        "name": "Banner (child)",
        "settings": [
          {
            "type": "select",
            "id": "image_ratio",
            "label": "Banner ratio",
            "default": "ratioadapt",
            "options": [
              {
                "value": "ratio_fh",
                "label": "Full screen"
              },
              {
                "value": "ratioadapt",
                "label": "Adapt to image"
              },
              {
                "value": "ratio_cuspx",
                "label": "Custom height"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "cover",
                "label": "Full"
              },
              {
                "value": "contain",
                "label": "Auto"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "1",
                "label": "Left top"
              },
              {
                "value": "2",
                "label": "Left center"
              },
              {
                "value": "3",
                "label": "Left bottom"
              },
              {
                "value": "4",
                "label": "Right top"
              },
              {
                "value": "5",
                "label": "Right center"
              },
              {
                "value": "6",
                "label": "Right bottom"
              },
              {
                "value": "7",
                "label": "Center top"
              },
              {
                "value": "8",
                "label": "Center center"
              },
              {
                "value": "9",
                "label": "Center bottom"
              }
            ],
            "label": "Image position",
            "default": "8"
          },
          {
            "type": "number",
            "id": "height_dk",
            "label": "Banner height",
            "info": "Only working when \"Banner ratio\" is \"custom height\"",
            "default": 400
          },
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image item"
          },
          {
            "type": "select",
            "id": "img_effect",
            "label": "Image hover effect",
            "info": "Waring: Hovering effect will resize your images",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "None"
              },
              {
                "value": "zoom",
                "label": "Zoom in"
              },
              {
                "value": "rotate",
                "label": "Rotate"
              },
              {
                "value": "translateToTop",
                "label": "Move to top"
              },
              {
                "value": "translateToRight",
                "label": "Move to right"
              },
              {
                "value": "translateToBottom",
                "label": "Move to bottom"
              },
              {
                "value": "translateToLeft",
                "label": "Move to left"
              },
              {
                "value": "filter",
                "label": "Filter"
              },
              {
                "value": "bounceIn",
                "label": "BounceIn"
              }
            ]
          },
          {
            "type": "select",
            "id": "b_effect",
            "label": "Banner effect when hover",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "None"
              },
              {
                "value": "pervasive-circle",
                "label": "Pervasive circle"
              },
              {
                "value": "plus-zoom-overlay",
                "label": "Plus zoom overlay"
              },
              {
                "value": "dark-overlay",
                "label": "Dark overlay"
              },
              {
                "value": "light-overlay",
                "label": "Light overlay"
              } 
            ]
          },

          {
            "type": "url",
            "id": "b_link",
            "label": "Banner link"
          },
          {
            "type": "select",
            "id": "open_link", 
            "options": [
              {
                "value": "_self",
                "label": "Current window"
              },
             {
                "value": "_blank",
                "label": "New window"
              }
            ],
            "label": "Open link in",
            "default": "_self"
          },
          {
           "type": "header",
           "content": "+ Content options"
           },
          {
            "type": "html",
            "id": "html",
            "label": "Content HTML:",
            "default": "<h3 class=\"fs__30 mg__0 lh__1 cw mb__10\">lighting collections<\/h3><h4 class=\"fs__16 mg__0 cw\">Explorer<\/h4>"
          },
          {
            "type": "select",
            "id": "content_align",
            "label": "Content align",
            "default": "center",
            "options": [
                {
                  "label": "Left",
                  "value": "start"
                },
                {
                  "label": "Center",
                  "value": "center"
                },
                {
                  "label": "Right",
                  "value": "end"
                }
            ]
          },
          {
            "type": "header",
            "content": "--Content position options--"
          },
          {
             "type": "range",
             "id": "cv_pos",
             "label": "Content vertical position",
             "info": " <= 50: Top position , > 50 bottom position",
             "default": 50,
             "min": 0,
             "max": 100,
             "step": 1,
             "unit": "%"
          },
          {
             "type": "range",
             "id": "ch_pos",
             "label": "Content horizontal position",
             "info": " <= 50: Left position , > 50 right position",
             "default": 50,
             "min": 0,
             "max": 100,
             "step": 1,
             "unit": "%"
          },
          {
            "type": "color",
            "id": "cl_txt",
            "label": "Text",
            "default": "#fff"
          },
          {
            "type": "color",
            "id": "cl_overlay",
            "label": "Overlay",
            "default": "#000"
          },
          {
            "type": "range",
            "id": "cl_opacity",
            "label": "Overlay opacity",
            "default": 5,
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%"
          },
          {
            "type": "select",
            "id": "col",
            "default": "4",
            "options": [
              {
                "value": "1",
                "label": "1/12 (8.333333%)"
              },
              {
                "value": "2",
                "label": "2/12 (16.666667%)"
              },
              {
                "value": "3",
                "label": "3/12 (25%)"
              },
              {
                "value": "4",
                "label": "4/12 (33.333333%)"
              },
              {
                "value": "5",
                "label": "5/12 (41.666667%)"
              },
              {
                "value": "6",
                "label": "6/12 (50%)"
              },
              {
                "value": "7",
                "label": "7/12 (58.333333%)"
              },
              {
                "value": "8",
                "label": "8/12 (66.666667%)"
              },
              {
                "value": "9",
                "label": "9/12 (75%)"
              },
              {
                "value": "10",
                "label": "10/12 (83.333333%)"
              },
              {
                "value": "11",
                "label": "11/12 (91.666667%)"
              },
              {
                "value": "12",
                "label": "12/12 (100%)"
              }
            ],
            "label": "Width col:"
           }
        ]
      },
      {
        "type": "banner_text",
        "name": "Banner text (Child)",
        "settings": [
          {
            "type": "image_picker",
            "id": "image_bn",
            "label": "+ Image"
          },
          {
            "type": "url",
            "id": "bn_link",
            "label": "Banner link",
            "info": "If set blank will not link"
          },
          {
            "type": "select",
            "id": "bn_target_link",
            "label": "Open link in",
            "default": "_self",
            "options": [
                {
                    "value": "_self",
                    "label": "Current window"
                },
                {
                    "value": "_blank",
                    "label": "New window"
                }
            ]
          },
          {
            "type": "range",
            "id": "label_sale",
            "label": "Label sale off",
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%",
            "default": 0
          },
          {
            "type": "color",
            "id": "bg_sale_cl",
            "label": "Background sale off color",
            "default": "#e9061a"
          },
          {
            "type": "select",
            "id": "img_effect",
            "label": "Image effect when hover",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "None"
              },
              {
                "value": "zoom",
                "label": "Zoom in"
              },
              {
                "value": "rotate",
                "label": "Rotate"
              },
              {
                "value": "translateToTop",
                "label": "Translate to top"
              },
              {
                "value": "translateToRight",
                "label": "Translate to right"
              },
              {
                "value": "translateToBottom",
                "label": "Translate to bottom"
              },
              {
                "value": "translateToLeft",
                "label": "Translate to left"
              },
              {
                "value": "filter",
                "label": "Filter"
              },
              {
                "value": "bounceIn",
                "label": "BounceIn"
              }
            ]
          },
          {
            "type": "textarea",
            "id": "title",
            "label": "+ Title",
            "default": "Title",
            "info": "If you want to line break, please add a <br> tag in the text"
          },
          {
            "type": "checkbox",
            "id": "remove_br_tag",
            "label": "Remove <br> tag on tablet & mobile",
            "default": false
          },
          {
            "type": "range",
            "id": "title_size",
            "label": "Title size",
            "min": 18,
            "max": 50,
            "step": 1,
            "unit": "px",
            "default": 26
          },
          {
            "type": "select",
            "id": "fontf_1",
            "default": "inherit",
            "label": "Font family",
            "options": [
                {
                    "label": "Inherit",
                    "value": "inherit"
                },
                {
                    "label": "Font family #1",
                    "value": "1"
                },
                {
                    "label": "Font family #2",
                    "value": "2"
                },
                {
                    "label": "Font family #3",
                    "value": "3"
                }
            ]
          },
          {
            "type": "textarea",
            "id": "subtitle",
            "label": "+ Subtitle",
            "default": "subtitle",
            "info": "If you want to line break, please add a <br> tag in the text"
          },
          {
            "type": "checkbox",
            "id": "remove_br_tag_2",
            "label": "Remove <br> tag on tablet & mobile",
            "default": false
          },
          {
            "type": "select",
            "id": "fontf_2",
            "default": "inherit",
            "label": "Font family",
            "options": [
                {
                    "label": "Inherit",
                    "value": "inherit"
                },
                {
                    "label": "Font family #1",
                    "value": "1"
                },
                {
                    "label": "Font family #2",
                    "value": "2"
                },
                {
                    "label": "Font family #3",
                    "value": "3"
                }
            ]
          },
          {
            "type": "text",
            "id": "button_text",
            "label": "+ Button label",
            "default": "Button label",
            "info": "If set blank will not show"
          },
          {
            "type": "url",
            "id": "button_link",
            "label": "Button link",
            "info": "If set blank will not show"
          },
          {
            "type": "range",
            "id": "button_icon_w",
            "label": "Button icon width",
            "min": 0,
            "max": 50,
            "step": 1,
            "unit": "px",
            "default": 0
          },
          {
            "type": "select",
            "id": "target_link",
            "label": "Open link in",
            "default": "_self",
            "options": [
                {
                    "value": "_self",
                    "label": "Current window"
                },
                {
                    "value": "_blank",
                    "label": "New window"
                }
            ]
          },
          {
              "type": "header",
              "content": "+ Banner Design"
          },
          {
            "type": "select",
            "id": "bn_layout",
            "default": "bn_dark",
            "label": "Banner content layout", 
            "options": [
              {
                "value": "bn_dark",
                "label": "Layout dark" 
              },
              {
                "value": "bn_light",
                "label": "Layout light"
              }
            ]
          },
          {
            "type": "select",
            "id": "content_bn_align",
            "label": "Content align",
            "default": "center",
            "options": [
                {
                  "label": "Left",
                  "value": "start"
                },
                {
                  "label": "Center",
                  "value": "center"
                },
                {
                  "label": "Right",
                  "value": "end"
                }
            ]
          },
          {
            "type": "select",
            "id": "content_h_position",
            "label": "Content horizontal position",
            "default": "center",
            "options": [
                {
                  "label": "Left",
                  "value": "start"
                },
                {
                  "label": "Center",
                  "value": "center"
                },
                {
                  "label": "Right",
                  "value": "end"
                }
            ]
          },
          {
            "type": "select",
            "id": "content_v_position",
            "label": "Content vertical position",
            "default": "bottom",
            "options": [
                {
                  "label": "Top",
                  "value": "top"
                },
                {
                  "label": "Middle",
                  "value": "middle"
                },
                {
                  "label": "Bottom",
                  "value": "bottom"
                }
            ]
          },
          {
            "type": "text",
            "id": "padding_inner_bn",
            "label": "Padding content inner",
            "info": "Padding top, padding right, padding bottom, padding left. If you do not use it please blank.",
            "placeholder": "30px,,30px," 
          },
          {
            "type": "text",
            "id": "padding_inner_bn_tb",
            "label": "Padding content inner tablet",
            "info": "Padding top, padding right, padding bottom, padding left. If you do not use it please blank.",
            "placeholder": "30px,,30px,"
          },
          {
            "type": "text",
            "id": "padding_inner_bn_mb",
            "label": "Padding content inner mobile",
            "info": "Padding top, padding right, padding bottom, padding left. If you do not use it please blank.",
            "placeholder": "30px,,30px,"
          },
          {
            "type": "number",
            "id": "mgb_box",
            "label": "Margin bottom (Unit: px)",
            "default": 20
          },
          {
            "type": "number",
            "id": "mgb_box_tb",
            "label": "Margin bottom on tablet (Unit: px)",
            "default": 20
          },
          {
            "type": "number",
            "id": "mgb_box_mb",
            "label": "Margin bottom on mobile(Unit: px)",
            "default": 20
          },
          {
            "type": "select",
            "id": "col",
            "default": "4",
            "options": [
              {
                "value": "1",
                "label": "1/12 (8.333333%)"
              },
              {
                "value": "2",
                "label": "2/12 (16.666667%)"
              },
              {
                "value": "3",
                "label": "3/12 (25%)"
              },
              {
                "value": "4",
                "label": "4/12 (33.333333%)"
              },
              {
                "value": "5",
                "label": "5/12 (41.666667%)"
              },
              {
                "value": "6",
                "label": "6/12 (50%)"
              },
              {
                "value": "7",
                "label": "7/12 (58.333333%)"
              },
              {
                "value": "8",
                "label": "8/12 (66.666667%)"
              },
              {
                "value": "9",
                "label": "9/12 (75%)"
              },
              {
                "value": "10",
                "label": "10/12 (83.333333%)"
              },
              {
                "value": "11",
                "label": "11/12 (91.666667%)"
              },
              {
                "value": "12",
                "label": "12/12 (100%)"
              }
            ],
            "label": "Width col:"
          }
        ]
      },
      {
        "type": "html",
        "name": "HTML ( child )",
        "settings": [
          {
            "type": "html",
            "id": "html",
            "label": "HTML custom",
            "default": "HTML custom code"
          },
          {
            "type": "page",
            "id": "page",
            "label": "Content page",
            "info": "This page content will appear."
          },
         {
          "type": "select",
          "id": "col",
          "default": "4",
          "options": [
            {
              "value": "1",
              "label": "1/12 (8.333333%)"
            },
            {
              "value": "2",
              "label": "2/12 (16.666667%)"
            },
            {
              "value": "3",
              "label": "3/12 (25%)"
            },
            {
              "value": "4",
              "label": "4/12 (33.333333%)"
            },
            {
              "value": "5",
              "label": "5/12 (41.666667%)"
            },
            {
              "value": "6",
              "label": "6/12 (50%)"
            },
            {
              "value": "7",
              "label": "7/12 (58.333333%)"
            },
            {
              "value": "8",
              "label": "8/12 (66.666667%)"
            },
            {
              "value": "9",
              "label": "9/12 (75%)"
            },
            {
              "value": "10",
              "label": "10/12 (83.333333%)"
            },
            {
              "value": "11",
              "label": "11/12 (91.666667%)"
            },
            {
              "value": "12",
              "label": "12/12 (100%)"
            }
          ],
          "label": "Width col:"
         }
        ]
      },
      {
        "type": "blogs",
        "name": "Blogs (child)",
        "settings": [
          {
            "type": "text",
            "id": "tt_hd",
            "label": "Heading",
            "default": "Heading"
          },
          {
            "type": "checkbox",
            "id": "use_bd",
            "label": "Use border heading",
            "default": true
          },
          {
            "type": "checkbox",
            "id": "use_bg",
            "label": "Use background color",
            "default": true
          },
          {
              "id": "blog",
              "type": "blog",
              "label": "Blog post"
          },
          {
            "type": "select",
            "id": "post_des",
            "options": [
                {
                    "value": "1",
                    "label": "Design 1"
                },
                {
                    "value": "2",
                    "label": "Design 2"
                },
                {
                    "value": "3",
                    "label": "Design 3"
                },
                {
                    "value": "4",
                    "label": "Design 4"
                },
                {
                    "value": "5",
                    "label": "Design 5"
                }
            ],
            "label": "Post item design",
            "default": "1"
          },
          {
            "type": "select",
            "id": "show_blog_title",
            "label": "Show blog title",
            "info": "From tags contains 'blog_': will show tags on article current contains 'blog_'",
            "default": "blog_current",
            "options": [
                {
                    "value": "disable",
                    "label": "Disable"
                },
                {
                    "value": "blog_current",
                    "label": "From blog current"
                },
                {
                    "value": "blog_tags",
                    "label": "From tags contains 'blog_'"
                }
            ]
          },
          {
            "type": "checkbox",
            "id": "show_content",
            "label": "Show short content",
            "default": true
          },
          {
              "type": "checkbox",
              "id": "show_tags",
              "label": "Show tags",
              "default": false
          },
          {
              "type": "checkbox",
              "id": "show_author",
              "label": "Show author",
              "default": true
          },
          {
              "type": "checkbox",
              "id": "show_comment",
              "label": "Show comment",
              "default": true
          },
          {
              "type": "checkbox",
              "id": "show_readmore",
              "label": "Show readmore",
              "default": false
          },
          {
              "type": "checkbox",
              "id": "show_date",
              "label": "Show date",
              "default": true
          },
          {
              "type": "select",
              "id": "date",
              "options": [
                  {
                      "value": "abbreviated_date",
                      "label": "Apr 19, 1994"
                  },
                  {
                      "value": "basic",
                      "label": "4/19/1994"
                  },
                  {
                      "value": "date",
                      "label": "April 19, 1994"
                  },
                  {
                      "value": "%b %d",
                      "label": "Apr 19"
                  }
              ],
              "label": "Date format",
              "info": "Different format options display for various languages.",
              "default": "%b %d"
          },
          {
            "type": "select",
            "id": "content_align",
            "label": "Content align",
            "default": "start",
            "options": [
              {
                "value": "start",
                "label": "Default"
              },
              {
                "value": "center",
                "label": "Center"
              }
            ]
          },
          {
            "type": "range",
            "id": "limit",
            "min": 1,
            "max": 50,
            "step": 1,
            "label": "Maximum posts to show",
            "default": 8 
          },
          {
            "type": "select",
            "id": "img_effect",
            "label": "Image hover effect",
            "info": "Waring: Hovering effect will resize your images",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "None"
              },
              {
                "value": "zoom",
                "label": "Zoom in"
              },
              {
                "value": "rotate",
                "label": "Rotate"
              },
              {
                "value": "translateToTop",
                "label": "Move to top "
              },
              {
                "value": "translateToRight",
                "label": "Move to right"
              },
              {
                "value": "translateToBottom",
                "label": "Move to bottom"
              },
              {
                "value": "translateToLeft",
                "label": "Move to left"
              }
            ]
          },
          {
            "type": "select",
            "id": "b_effect",
            "label": "Blog effect when hover",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "None"
              },
              {
                "value": "border-run",
                "label": "Border run"
              },
              {
                "value": "pervasive-circle",
                "label": "Pervasive circle"
              },
              {
                "value": "plus-zoom-overlay",
                "label": "Plus zoom overlay"
              },
              {
                "value": "dark-overlay",
                "label": "Dark overlay"
              },
              {
                "value": "light-overlay",
                "label": "Light overlay"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_ratio",
            "label": "Image ratio",
            "default": "ratioadapt",
            "info": "Aspect ratio custom will settings in general panel",
            "options": [
              {
                "group": "Natural",
                "value": "ratioadapt",
                "label": "Adapt to image"
              },
              {
                "group": "Landscape",
                "value": "ratio2_1",
                "label": "2:1"
              },
              {
                "group": "Landscape",
                "value": "ratio16_9",
                "label": "16:9"
              },
              {
                "group": "Landscape",
                "value": "ratio8_5",
                "label": "8:5"
              },
              {
                "group": "Landscape",
                "value": "ratio3_2",
                "label": "3:2"
              },
              {
                "group": "Landscape",
                "value": "ratio4_3",
                "label": "4:3"
              },
              {
                "group": "Landscape",
                "value": "rationt",
                "label": "Ratio ASOS"
              },
              {
                "group": "Squared",
                "value": "ratio1_1",
                "label": "1:1"
              },
              {
                "group": "Portrait",
                "value": "ratio2_3",
                "label": "2:3"
              },
              {
                "group": "Portrait",
                "value": "ratio1_2",
                "label": "1:2"
              },
              {
                "group": "Custom",
                "value": "ratiocus1",
                "label": "Ratio custom 1"
              },
              {
                "group": "Custom",
                "value": "ratiocus2",
                "label": "Ratio custom 2"
              },
              {
                "group": "Custom",
                "value": "ratiocus3",
                "label": "Ratio custom 3"
              },
              {
                "group": "Custom",
                "value": "ratiocus4",
                "label": "Ratio custom 4"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_size",
            "label": "Image size",
            "default": "cover",
            "info": "This settings apply only if the image ratio is not set to 'Adapt to image'.",
            "options": [
              {
                "value": "cover",
                "label": "Full"
              },
              {
                "value": "contain",
                "label": "Auto"
              }
            ]
          },
          {
            "type": "select",
            "id": "image_position",
            "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the image ratio is not set to 'Adapt to image'.",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "1",
                "label": "Left top"
              },
              {
                "value": "2",
                "label": "Left center"
              },
              {
                "value": "3",
                "label": "Left bottom"
              },
              {
                "value": "4",
                "label": "Right top"
              },
              {
                "value": "5",
                "label": "Right center"
              },
              {
                "value": "6",
                "label": "Right bottom"
              },
              {
                "value": "7",
                "label": "Center top"
              },
              {
                "value": "8",
                "label": "Center center"
              },
              {
                "value": "9",
                "label": "Center bottom"
              }
            ],
            "label": "Image position",
            "default": "8"
          },
          {
            "type": "select",
            "id": "col_dk",
            "label": "Items per row",
            "default": "2",
            "options": [
              {
                "value": "1",
                "label": "1"
              },
              {
                "value": "2",
                "label": "2"
              },
              {
                "value": "3",
                "label": "3"
              },
              {
                "value": "4",
                "label": "4"
              }
            ]
          },
          {
            "type": "select",
            "id": "space_h_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space horizontal items",
            "default": "30"
          },
          {
            "type": "select",
            "id": "space_v_item",
            "options": [
              {
                  "value": "0", 
                  "label": "0"
              },
              {
                  "value": "2", 
                  "label": "2px"
              },
              {
                  "value": "4", 
                  "label": "4px"
              },
              {
                  "value": "6", 
                  "label": "6px"
              },
              {
                  "value": "8", 
                  "label": "8px"
              },
              {
                  "value": "10", 
                  "label": "10px"
              },
              {
                  "value": "15",
                  "label": "15px"
              },
              {
                  "value": "20",
                  "label": "20px"
              },
              {
                  "value": "25",
                  "label": "25px"
              },
              {
                  "value": "30",
                  "label": "30px"
              }
            ],
            "label": "Space vertical items",
            "default": "30"
          },
          {
            "type": "header",
            "content": "--Box options--"
          },
          {
            "type": "select",
            "id": "layout_des",
            "options": [
              {
                "value": "1",
                "label": "Grid"
              },
              {
                "value": "2",
                "label": "Carousel"
              }
            ],
            "label": "Layout design",
            "default": "2"
          },
          {
            "type": "header",
            "content": "+Options for carousel layout"
          },
          {
            "type": "paragraph",
            "content": "Prev next button"
          },
          {
            "type": "checkbox",
            "id": "nav_btn",
            "label": "Use prev next button",
            "info": "Enable previous & next buttons",
            "default": false
          },
          {
            "type": "select",
            "id": "btn_vi",
            "label": "Visible",
            "default": "hover",
            "options": [
              {
                "value": "always",
                "label": "Always"
              },
              {
                "value": "hover",
                "label": "Only hover"
              }
            ]
          },
          {
            "type": "select",
            "id": "btn_pos",
            "label": "Prev next position",
            "info": "Working on screen Desktop",
            "default": "in",
            "options": [
                {
                    "label": "In content",
                    "value": "in"
                },
                {
                    "label": "Out content",
                    "value": "out"
                },
                {
                    "label": "Content side center",
                    "value": "between"
                }
            ]
          },
          {
            "type": "select",
            "id": "btn_owl",
            "label": "Button style",
            "default": "default",
            "options": [
              {
                "value": "default",
                "label": "Default"
              },
              {
                "value": "outline",
                "label": "Outline"
              },
              {
                "value": "simple",
                "label": "Simple"
              }
            ]
          },
          {
            "type": "select",
            "id": "btn_shape",
            "label": "Button shape",
            "info": "Not work for 'Simple' button style",
            "default": "none",
            "options": [
              {
                "value": "none",
                "label": "Default"
              },
              {
                "value": "round",
                "label": "Round"
              },
              {
                "value": "rotate",
                "label": "Rotate"
              }
            ]
          },
          {
              "type": "select",
              "id": "btn_cl",
              "label": "Button color",
              "default": "dark",
              "options": [
                  {
                      "value": "light",
                      "label": "Light"
                  },
                  {
                      "value": "dark",
                      "label": "Dark"
                  },
                  {
                      "value": "primary",
                      "label": "Primary"
                  },
                  {
                      "value": "custom1",
                      "label": "Custom color 1"
                  },
                  {
                      "value": "custom2",
                      "label": "Custom color 2"
                  }
              ]
          },
          {
            "type": "select",
            "id": "btn_size",
            "label": "Button size",
            "default": "small",
            "options": [
              {
                "value": "small",
                "label": "Small"
              },
              {
                "value": "medium",
                "label": "Medium"
              },
              {
                "value": "large",
                "label": "Large"
              }
            ]
          },
          {
            "type": "checkbox",
            "id": "btn_hidden_mobile",
            "label": "Hidden buttons on mobile ",
            "default": true
          },
          {
            "type": "select",
            "id": "col",
            "default": "4",
            "options": [
              {
                "value": "1",
                "label": "1/12 (8.333333%)"
              },
              {
                "value": "2",
                "label": "2/12 (16.666667%)"
              },
              {
                "value": "3",
                "label": "3/12 (25%)"
              },
              {
                "value": "4",
                "label": "4/12 (33.333333%)"
              },
              {
                "value": "5",
                "label": "5/12 (41.666667%)"
              },
              {
                "value": "6",
                "label": "6/12 (50%)"
              },
              {
                "value": "7",
                "label": "7/12 (58.333333%)"
              },
              {
                "value": "8",
                "label": "8/12 (66.666667%)"
              },
              {
                "value": "9",
                "label": "9/12 (75%)"
              },
              {
                "value": "10",
                "label": "10/12 (83.333333%)"
              },
              {
                "value": "11",
                "label": "11/12 (91.666667%)"
              },
              {
                "value": "12",
                "label": "12/12 (100%)"
              }
            ],
            "label": "Width col:"
           }
        ]
      }
   ]
}
{% endschema %}