<!-- sections/contact-form.liquid -->
{{ 'bee-section.css' | asset_url | stylesheet_tag }}
{{ 'bee-content-position.css' | asset_url | stylesheet_tag }}
{{ 'bee-button-style.css' | asset_url | stylesheet_tag }}
{{ 'bee-custom-input.css' | asset_url | stylesheet_tag }}
{{ 'bee-contact-form.css' | asset_url | stylesheet_tag }}

<link href="{{ 'custom-effect.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
{%-liquid
    assign sid = section.id
    assign se_stts = section.settings  
    assign se_blocks = section.blocks  
    assign stt_layout = se_stts.layout
    assign image = se_stts.image
    if stt_layout == 'bee-se-container' 
        assign html_layout = '<div class="bee-container">__</div></div>' | split: '__'
    elsif stt_layout == 'bee-container-wrap'
        assign html_layout = '<div class="bee-container">__</div>' | split: '__'
    else
        assign html_layout = '__' | split: '__'
    endif 

    assign bg_content_op = se_stts.bg_content_op | divided_by: 100.0 
    assign bg_content = se_stts.bg_content_cl | color_modify: 'alpha', bg_content_op 

    assign bg_opacity = se_stts.bg_opacity | divided_by: 100.0 
    assign bg_overlay = se_stts.bg_overlay | color_modify: 'alpha', bg_opacity 

    assign br_opacity = se_stts.br_opacity | divided_by: 100.0
    assign br_bg = se_stts.br_bg | color_modify: 'alpha', br_opacity 

    assign stt_image_bg = se_stts.image_bg
    assign image_fix = image_nt | image_tag

    assign formId = 'contact_form_page' | append: sid
    assign button_style = se_stts.button_style

    if se_stts.source == 'none'
        assign has_map = false
    else 
        assign has_map = true
    endif

-%}
{%- capture append_style -%}--aspect-ratioapt: {{ image.aspect_ratio | default: 2 }};--aspect-ratio-cusdt : {{ se_stts.height_dk }}px;--aspect-ratio-custb : {{ se_stts.height_tb }}px;--aspect-ratio-cusmb : {{ se_stts.height_mb }}px;

    {% if stt_image_bg.presentation.focal_point != '50.0% 50.0%' %} --background-position: {{stt_image_bg.presentation.focal_point}}{% endif %} 
{%- endcapture -%}
{%- capture append_bg_content_style -%}--bg-content: {{ bg_content }};--content-pd: {{ se_stts.content_pd_tb }}px {{ se_stts.content_pd_lr }}px;--content-pd-mb: {{ se_stts.content_pd_tb_mb }}px {{ se_stts.content_pd_lr_mb }}px;{%- endcapture -%}
{%- if se_stts.border_bl -%}
    {%- capture append_bg_border_style -%}--br-color: {{ se_stts.br_color }};--br-style: {{ se_stts.br_style }};--br-pd: {{ se_stts.br_pd }}px;--br-pd-mb: {{ se_stts.br_pd_mb }}px;--border-bg: {{ br_bg }};{%- endcapture -%}
{%- endif -%}
<div class="bee-section-inner bee_nt_se_{{ sid }} {{ stt_layout }}{% if stt_image_bg != blank and stt_layout != 'bee-se-container' %} bee-has-imgbg lazyloadbee{% endif %}" {% if stt_image_bg != blank and stt_layout != 'bee-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="1.5"{% endif %} {% render 'section_style', se_stts: se_stts, append_style: append_style -%} >
    {{- html_layout[0] -}}
    {%- if stt_layout == 'bee-se-container' -%}
        <div class="bee-container-inner{% if stt_image_bg != blank %} bee-has-imgbg lazyloadbee{% endif %}" {% if stt_image_bg != blank %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="1.5"{% endif %}>{% endif -%}
        <div class="bee-row">
        <div class="bee-map-info-wrap bee-pr bee-oh {% if has_map %} bee-col-item bee-col-lg-6 bee-col-12 {% endif %}">
            <div class="bee-map bee-pr bee-oh">
                {%- if se_stts.source == "map" -%}
                    <div class="bee-frame-map bee-pr">{{ se_stts.map }}</div>
                {%- elsif se_stts.source == 'use_image' -%}
                    <div class="bee-contact-image {{ se_stts.se_height }} beecuspx1_{{ se_stts.custom_mb }} beecuspx2_{{ se_stts.custom_tb }} beecuspx3_{{ se_stts.custom_dk }}">
                        <div class="bee-contact-image__inner bee-pr bee-oh bee_cover bee_ratio bee-bg-11 bee_position_{{ se_stts.image_position }}"{%- if image != blank -%} style="background: url({{ image | image_url: width: 1 }});"{%- endif -%}>
                            {%- if image != blank -%}   
                                <div class="bee_bg lazyloadbee bee-lz--fadeIn" data-bgset="{{ image | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="1.5" ></div>
                            {%- else -%} 
                                {{ 'lifestyle-1' | placeholder_svg_tag: 'bee-placeholder-svg bee-svg-bg1' }}  
                            {%- endif -%}
                        </div>
                    </div>
                {% endif -%}
                
            </div>
            {%- if se_blocks.size > 0 -%}
            <div class="bee-content-wrap bee-bg-cl-wrap">
                <div class="bee-content-position bee-{{ se_stts.content_width }} bee-pa bee-bg-content-true bee-br-content-{{ se_stts.border_bl }} bee-br-style-{{ se_stts.br_style }} bee-text-md-{{ se_stts.text_align }} bee-text-{{ se_stts.text_align_mb }}" style="--time-animation: {{ se_stts.time_animation }}s;{%- render 'content_position', ch_pos: se_stts.ch_pos, cv_pos: se_stts.cv_pos, ch_pos_tb: se_stts.ch_pos_tb, cv_pos_tb: se_stts.cv_pos_tb, ch_pos_mb: se_stts.ch_pos_mb, cv_pos_mb: se_stts.cv_pos_mb, append_bg_content_style: append_bg_content_style, append_bg_border_style: append_bg_border_style -%}">
                    <div class="bee-btn-close bee-text-end bee-d-none">
                            <span class="bee-svg"><svg version="1.1" x="0px" y="0px" width="18px" height="18px" viewBox="0 0 18 18" style="enable-background:new 0 0 18 18;" xml:space="preserve"><path d="M10.41,9l3.29-3.29c0.39-0.39,0.39-1.02,0-1.41s-1.02-0.39-1.41,0L9,7.59L5.71,4.29c-0.39-0.39-1.02-0.39-1.41,0 s-0.39,1.02,0,1.41L7.59,9l-3.29,3.29c-0.39,0.39-0.39,1.02,0,1.41C4.49,13.9,4.74,14,5,14s0.51-0.1,0.71-0.29L9,10.41l3.29,3.29 C12.49,13.9,12.74,14,13,14s0.51-0.1,0.71-0.29c0.39-0.39,0.39-1.02,0-1.41L10.41,9z"></path></svg></span>
                        </div>
                    <div class="bee-map-info">
                        
                        {%- for block in se_blocks -%} 
                            {%- assign bk_stts = block.settings -%}
                            {%- case block.type -%}
                                {%- when 'custom_text' -%}
                                    {%- assign general_block = true -%} 
                                        <{{ bk_stts.tag }} data-lh="{{bk_stts.text_lh_mb}}" data-lh-md="{{bk_stts.text_lh_tb}}" data-lh-lg="{{bk_stts.text_lh}}" class="bee-bl-item bee-custom-text_parent bee-animation-none bee-text-bl bee-fnt-fm-{{ bk_stts.fontf }} bee-font-italic-{{ bk_stts.font_italic }} bee-hidden-mobile-{{ bk_stts.hidden_mobile }} bee-hidden-br-mb-{{ bk_stts.remove_br_tag }} bee-text-shadow-{{ bk_stts.text_shadow }}" {%- render 'bk_cus_style', type: 'custom_text', bk_stts: bk_stts -%}>{{ bk_stts.text }}</{{ bk_stts.tag }}>
                                {%- when 'space_html' -%}
                                    {%- assign general_block = true -%}
                                    <div class="bee-bl-item bee-space-html bee-animation-none bee-hidden-mobile-{{ bk_stts.hidden_mobile }}" {%- render 'bk_cus_style', type: 'space_html', bk_stts: bk_stts -%}></div>
                                {%- when 'html' -%}
                                    {%- assign general_block = true -%}
                                    <div class="bee-bl-item bee-animation-none bee-raw-html bee-hidden-mobile-{{ bk_stts.hidden_mobile }} bee-rte--list" {%- render 'bk_cus_style', type: 'html', bk_stts: bk_stts -%}>{{ bk_stts.html_content }}</div>
                                {%- when 'image' -%}
                                    {%- assign image = bk_stts.image_child -%}
                                    {%- if image != blank -%}
                                        {%- assign general_block = true -%}
                                        <div class="bee-bl-item bee-img-child bee-animation-none bee-hidden-mobile-{{ bk_stts.hidden_mobile }}" {%- render 'bk_cus_style', type: 'image', bk_stts: bk_stts -%} >
                                            <img class="lazyloadbee bee-lz--fadeIn" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="2" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                                        </div>
                                    {%- endif -%}
                                {%- when 'custom_button' -%}
                                    {%- if bk_stts.button_link != blank and bk_stts.button_text != blank -%}
                                        {%- assign use_button = true -%}
                                        {%- assign button_style = bk_stts.button_style -%}
                                        <a href="{{ bk_stts.button_link }}" target="{{ bk_stts.target_link }}" class="bee-bl-item bee-animation-none bee-btn bee-btn-custom bee-pe-auto bee-fnt-fm-{{ bk_stts.fontf }} bee-hidden-mobile-{{ bk_stts.hidden_mobile }} bee-btn-style-{{ button_style }} {% if button_style == 'default' or button_style == 'outline' %}bee-btn-effect-{{ bk_stts.button_effect }}{% endif %}" {%- render 'bk_cus_style', type: 'custom_button', bk_stts: bk_stts -%}>{{ bk_stts.button_text }} {%- if bk_stts.button_icon_w > 0 -%}<svg  class="bee-btn-icon" viewBox="0 0 22 22"><use xlink:href="#bee-icon-btn"></use></svg>{%- endif -%}</a>
                                    {%- endif -%}
                            {%- endcase -%}
                        {%- endfor -%}  
                    </div>
                </div>
            </div>
            <div class="bee-full-width-link bee-pe-none" style="--bg-overlay: {{ bg_overlay }};"></div>
            {%- endif -%}
        </div>
        <div class="bee-contact-form {% if has_map %}bee-col-item bee-col-lg-6 bee-col-12 {% else %} bee-col-item bee-col-12 {% endif %}">
            <div class="bee-group-form {% if has_map %}bee-form-pd{% endif %} bee-text-md-{{ se_stts.text_align }} bee-text-{{ se_stts.text_align_mb }}">
            {%- if se_stts.form_style == '1' -%}
                <div class="bee-form-style-{{ se_stts.form_style }}">{% render 'form_style_1', se_stts: se_stts %}</div>
            {%- else -%}
                <div class="bee-form-style-{{ se_stts.form_style }}">{% render 'form_style_2', se_stts: se_stts %}</div>
            {%- endif -%}
            </div>
        </div>
        
    {{- html_layout[1] -}}
</div>
{%- if general_block or use_button -%}
    {{ 'bee-general-block.css' | asset_url | stylesheet_tag }}
{%- endif -%} 
<style>
    .bee_nt_se_{{ sid }} .bee-contact-form .bee-contact-form__submit{ 
        font-weight: 500;
        font-size: 15px;
        letter-spacing: 0;
        padding: 0 40px;
        margin-top: 30px;
        border-radius:var(--btn-radius)
    }
</style>
{% schema %}
{
    "name": "Contact Form",
    "tag": "section",
    "class": "bee-section bee-section-main bee-map-info",
    "settings": [
        {
            "type": "header",
            "content": "1.General options"
        },
        {
            "type": "select",
            "id": "source",
            "label": "Source image",
            "default": "map",
            "options": [
                {
                    "value": "none",
                    "label": "None"
                },
                {
                    "value": "use_image",
                    "label": "Use image"
                },
                {
                    "value": "map",
                    "label": "Source map"
                }
            ]
        },
        {
            "type": "header",
            "content": "+ Options for source image"
        },
        {
            "type": "select",
            "id": "se_height",
            "label": "Section height",
            "default": "bee_ratioadapt",
            "options": [
                {
                    "value": "bee_ratioadapt",
                    "label": "Adapt to image"
                },
                {
                    "value": "bee_ratio_cuspx",
                    "label": "Custom height"
                }
            ]
        },
        {
            "type": "checkbox",
            "id": "custom_dk",
            "label": "Use custom height (Desktop)",
            "default": true
        },
        {
            "type": "number",
            "id": "height_dk",
            "label": "Section height (Desktop)",
            "default": 600
        },
        {
            "type": "checkbox",
            "id": "custom_tb",
            "label": "Use custom height (Tablet)",
            "default": true
        },
        {
            "type": "number",
            "id": "height_tb",
            "label": "Section height (Tablet)",
            "default": 400
        },
        {
            "type": "checkbox",
            "id": "custom_mb",
            "label": "Use custom height (Mobile)",
            "default": true
        },
        {
            "type": "number",
            "id": "height_mb",
            "label": "Section height (Mobile)",
            "default": 250
        }, 
        {                   
            "type": "image_picker",
            "id": "image",
            "label": "Choose Image"                  
        },
        {
            "type": "header",
            "content": "+ Options for source map"
        },
        {
            "type": "textarea",
            "id": "map",
            "label": "Map",
            "default": "<iframe src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3154.8939060848147!2d144.81158271584684!3d-37.74563313792195!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad65fa6debeb781%3A0xe1d23f5d1759961e!2s184%20Main%20Rd%20E%2C%20St%20Albans%20VIC%203021%2C%20%C3%9Ac!5e0!3m2!1svi!2s!4v1618277125252!5m2!1svi!2s\" width=\"100%\" height=\"600\" style=\"border: 0;\" allowfullscreen=\"\" loading=\"lazy\"></iframe>" 
        },
        {
            "type": "header",
            "content": "2. Content Design"
        },
        {
            "type": "select",
            "id": "text_align",
            "label": "Content align",
            "default": "start",
            "options": [
                {
                    "label": "Left",
                    "value": "start"
                },
                {
                    "label": "Center",
                    "value": "center"
                },
                {
                    "label": "Right",
                    "value": "end"
                }
            ]
        },
        {
            "type": "select",
            "id": "text_align_mb",
            "label": "Content align (Mobile)",
            "default": "start",
            "options": [
                {
                    "label": "Left",
                    "value": "start"
                },
                {
                    "label": "Center",
                    "value": "center"
                },
                {
                    "label": "Right",
                    "value": "end"
                }
            ]
        },
        {
            "type": "select",
            "id": "content_width",
            "label": "Content width",
            "default": "auto",
            "options": [
                {
                    "label": "Auto",
                    "value": "auto"
                },
                {
                    "label": "Container",
                    "value": "container"
                }
            ]
        },
        {
            "type": "range",
            "id": "cv_pos",
            "label": "Content vertical position",
            "info": " <= 50: Top position , > 50 bottom position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "range",
            "id": "ch_pos",
            "label": "Content horizontal position",
            "info": " <= 50: Left position , > 50 right position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "header",
            "content": "--Content position options (Tablet)--"
        },
        {
            "type": "range",
            "id": "cv_pos_tb",
            "label": "Content vertical position",
            "info": " <= 50: Top position , > 50 bottom position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "range",
            "id": "ch_pos_tb",
            "label": "Content horizontal position",
            "info": " <= 50: Left position , > 50 right position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "header",
            "content": "--Content position options (Mobile)--"
        },
        {
            "type": "range",
            "id": "cv_pos_mb",
            "label": "Content vertical position",
            "info": " <= 50: Top position , > 50 bottom position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "range",
            "id": "ch_pos_mb",
            "label": "Content horizontal position",
            "info": " <= 50: Left position , > 50 right position",
            "max": 100,
            "min": 0,
            "step": 1,
            "unit": "%",
            "default": 50
        },
        {
            "type": "header",
            "content": "+ Content background, color options"
        },
        {
            "type": "color",
            "id": "bg_overlay",
            "label": "Overlay",
            "default": "#000"
        },
        {
            "type": "range",
            "id": "bg_opacity",
            "label": "Overlay opacity",
            "default": 15,
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%"
        },
        {"type": "paragraph","content": "————————————————"},
        {
            "type": "color",
            "id": "bg_content_cl",
            "label": "Background color",
            "default": "#fff"
        },
        {
            "type": "range",
            "id": "bg_content_op",
            "label": "Background color opacity",
            "default": 100,
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "%"
        },
        {
            "type": "number",
            "id": "content_pd_tb",
            "label": "Content padding top/bottom (px)",
            "default": 30,
            "info": "Working on the Desktop"   
        },
        {
            "type": "number",
            "id": "content_pd_lr",
            "label": "Content padding left/right (px)",
            "default": 30,
            "info": "Working on the Desktop"   
        },
        {
            "type": "number",
            "id": "content_pd_tb_mb",
            "label": "Content padding top/bottom (px)",
            "default": 20,
            "info": "Working on the Mobile"    
        },
        {
            "type": "number",
            "id": "content_pd_lr_mb",
            "label": "Content padding left/right (px)",
            "default": 20,
            "info": "Working on the mobile"
        },
        {"type": "paragraph","content": "————————————————"},
        {
            "type": "checkbox",
            "id": "border_bl",
            "label": "Use border content",
            "default": false
        },
        {
            "type": "color",
            "id": "br_color",
            "label": "Border color",
            "default": "#222"
        },
        {
            "type": "color",
            "id": "br_bg",
            "label": "Background border",
            "default": "#fff"
        },
        {
            "type": "range",
            "id": "br_opacity",
            "label": "Border opacity",
            "default": 0,
            "min": 0,
            "default": 50,
            "max": 100,
            "step": 1,
            "unit": "%"
        },
        {
            "type": "select",
            "id": "br_style",
            "label": "Border style",
            "default": "solid",
            "options": [
                {
                    "value": "none",
                    "label": "None"
                },
                {
                    "value": "solid",
                    "label": "Solid"
                },
                {
                    "value": "dashed",
                    "label": "Dashed"
                },
                {
                    "value": "double",
                    "label": "Double"
                }
            ]
        },
        {
            "type": "range",
            "id": "br_pd",
            "label": "Border padding (Desktop)",
            "default": 20,
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "px"
        },
        {
            "type": "range",
            "id": "br_pd_mb",
            "label": "Border padding (Mobile)",
            "default": 10,
            "min": 0,
            "max": 100,
            "step": 1,
            "unit": "px"
        },
        {
            "type": "header",
            "content": "3. Design Form"
        },
        {
            "type": "select",
            "id": "form_style",
            "label": "Design",
            "options": [
                {
                "value": "1",
                "label": "Design 1"
                },
                {
                "value": "2",
                "label": "Design 2"
                }
            ],
            "default": "1"
        },
        {
            "type": "text",
            "id": "title_form",
            "label": "Title",
            "default": "GET IN TOUCH"
        },
        {
            "type": "text",
            "id": "heading_form",
            "label": "Heading",
            "default": "Send Us a Message"
        },
        {
            "type": "text",
            "id": "sub_heading_form",
            "label": "Sub heading",
            "default": "Enter your details and message below and we'll get in touch asap"
        },
        {
            "type": "header",
            "content": "+ Options button submit"
        },
        {
            "type": "checkbox",
            "id": "enable_full_btn_form",
            "label": "Enable button full width",
            "default": true
        },
        {
            "type": "select",
            "id": "button_style_form",
            "label": "Button style",
            "options": [
                {
                    "label": "Default",
                    "value": "default"
                },
                {
                    "label": "Outline",
                    "value": "outline"
                },
                {
                    "label": "Border bottom",
                    "value": "bordered"
                },
                {
                    "label": "Link",
                    "value": "link"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_size_form",
            "label": "Button size",
            "default": "default",
            "options": [
                {
                    "label": "Default",
                    "value": "default"
                },
                {
                    "label": "Custom size #1",
                    "value": "1"
                },
                {
                    "label": "Custom size #2",
                    "value": "2"
                },
                {
                    "label": "Custom size #3",
                    "value": "3"
                }
            ]
        },
        {
            "type": "select",
            "id": "btn_cl_form",
            "label": "Button color",
            "default": "dark",
            "options": [
                {
                    "value": "light",
                    "label": "Light"
                },
                {
                    "value": "dark",
                    "label": "Dark"
                },
                {
                    "value": "primary",
                    "label": "Primary"
                },
                {
                    "value": "custom1",
                    "label": "Custom color 1"
                },
                {
                    "value": "custom2",
                    "label": "Custom color 2"
                }
            ]
        },
          
        {
            "type": "header",
            "content": "4. Design options"
        },
        {
            "type": "select","id": "layout","default": "bee-container-wrap","label": "Layout",
            "options": [
                { "value": "bee-se-container", "label": "Container"},
                { "value": "bee-container-wrap", "label": "Wrapped container"},
                { "value": "bee-container-fluid", "label": "Full width"}
            ]
        },
        {
            "type": "color",
            "id": "cl_bg",
            "label": "Background"
        },
        {
            "type": "color_background",
            "id": "cl_bg_gradient",
            "label": "Background gradient"
        },
        {
            "type": "image_picker",
            "id": "image_bg",
            "label": "Background image"
        },
        {
            "type": "text",
            "id": "mg",
            "label": "Margin",
            "info": "Margin top, margin right, margin bottom, margin left. If you do not use it please blank.",
            "default": "50px,,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd",
            "label": "Padding",
            "info": "Padding top, padding right, padding bottom, padding left. If you do not use it please blank.",
            "placeholder": "50px,,50px,"
        },
        {
            "type": "header",
            "content": "+ Design tablet options"
        },
        {
            "type": "text",
            "id": "mg_tb",
            "label": "Margin",
            "default": ",,50px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_tb",
            "label": "Padding",
            "placeholder": "50px,,50px,"
        },
        {
            "type": "header",
            "content": "+ Design mobile options"
        },
        {
            "type": "text",
            "id": "mg_mb",
            "label": "Margin",
            "default": ",,30px,",
            "placeholder": ",,50px,"
        },
        {
            "type": "text",
            "id": "pd_mb",
            "label": "Padding",
            "placeholder": ",,50px,"
        }
    ],
    "blocks": [
        {
            "type": "custom_text",
            "name": "Text",
            "settings": [
                {
                    "type": "textarea",
                    "id": "text",
                    "label": "Input text",
                    "default": "Text",
                    "info": "If you want to line break, please add a <br> tag in the text"
                },
                {
                    "type": "checkbox",
                    "id": "remove_br_tag",
                    "label": "Remove <br> tag on tablet & mobile",
                    "default": false
                },
                {
                  "type": "select",
                  "id": "tag",
                  "default": "p",
                  "options": [
                     {
                        "value": "h2",
                        "label": "H2"
                     },
                     {
                        "value": "h3",
                        "label": "H3"
                     },
                     {
                        "value": "h4",
                        "label": "H4"
                     },
                     {
                        "value": "h5",
                        "label": "H5"
                     },
                     {
                        "value": "h6",
                        "label": "H6"
                     },
                     {
                        "value": "p",
                        "label": "P"
                      },
                     {
                        "value": "div",
                        "label": "DIV"
                      }
                  ],
                  "label": "Html tag"
                },
                {
                    "type": "select",
                    "id": "fontf",
                    "default": "inherit",
                    "label": "Font family",
                    "options": [
                        {
                            "label": "Inherit",
                            "value": "inherit"
                        },
                        {
                            "label": "Font Family #1",
                            "value": "1"
                        },
                        {
                            "label": "Font Family #2",
                            "value": "2"
                        },
                        {
                            "label": "Font Family #3",
                            "value": "3"
                        }
                    ]
                },
                {
                    "type": "color",
                    "id": "text_cl",
                    "label": "Color text",
                    "default": "#000"
                },
                {
                    "type": "range",
                    "id": "text_fs",
                    "label": "Font size",
                    "max": 100,
                    "min": 10,
                    "step": 1,
                    "unit": "px",
                    "default": 16
                },
                {
                    "type": "range",
                    "id": "text_lh",
                    "label": "Line height",
                    "max": 100,
                    "min": 0,
                    "step": 1,
                    "default": 0,
                    "unit": "px",
                    "info": "Set '0' to use default"            
                },
                {
                    "type": "range",
                    "id": "text_fw",
                    "label": "Font weight",
                    "min": 100,
                    "max": 900,
                    "step": 100,
                    "default": 400
                },
                {
                    "type": "range",
                    "id": "text_ls",
                    "label": "Letter spacing",
                    "max": 10,
                    "min": 0,
                    "default": 0,
                    "step": 0.1,
                    "unit": "px"
                },
                {
                    "type": "number",
                    "id": "mgb",
                    "label": "Margin bottom",
                    "default": 15
                },
                {
                    "type": "checkbox",
                    "id": "font_italic",
                    "label": "Enable font italic style",
                    "default": false
                },
                {
                    "type": "checkbox",
                    "id": "text_shadow",
                    "label": "Enable text shadow",
                    "default": false
                },
                {
                    "type": "header",
                    "content": "+ Options on tablet"
                },
                {
                    "type": "range",
                    "id": "text_fs_tb",
                    "label": "Font size",
                    "max": 60,
                    "min": 10,
                    "step": 1,
                    "unit": "px",
                    "default": 16
                },
                {
                    "type": "range",
                    "id": "text_lh_tb",
                    "label": "Line height",
                    "max": 70,
                    "min": 0,
                    "step": 1,
                    "default": 0,
                    "unit": "px",
                    "info": "Set '0' to use default"            
                },
                {
                    "type": "range",
                    "id": "text_ls_tb",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "mgb_tb",
                    "label": "Margin bottom",
                    "default": 10
                },
                {
                    "type": "header",
                    "content": "+  Options mobile"
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile",
                    "default": false
                },
                {
                    "type": "range",
                    "id": "text_fs_mb",
                    "label": "Font size",
                    "max": 60,
                    "min": 10,
                    "step": 1,
                    "unit": "px",
                    "default": 16
                },
                {
                    "type": "range",
                    "id": "text_lh_mb",
                    "label": "Line height",
                    "max": 70,
                    "min": 0,
                    "step": 1,
                    "default": 0,
                    "unit": "px",
                    "info": "Set '0' to use default"            
                },
                {
                    "type": "range",
                    "id": "text_ls_mb",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "mgb_mb",
                    "label": "Margin bottom",
                    "default": 10
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                }
            ]
        },
        {
            "type": "html",
            "name": "HTML",
            "settings": [
                {
                    "type": "html",
                    "id": "html_content",
                    "label": "Type html"
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile",
                    "default": false
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                }
            ]
        },
        {
            "type": "image",
            "name": "Image (Child)",
            "settings": [
                {
                    "type": "image_picker",
                    "id": "image_child",
                    "label": "Image (Child)"
                },
                {
                    "type": "number",
                    "id": "img_width",
                    "label": "Image width (Unit: px)",
                    "info": "Set 0 to use width default of image",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "img_width_tb",
                    "label": "Image width on tablet (Unit: px)",
                    "info": "Set 0 to use width default of image",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "img_width_mb",
                    "label": "Image width on mobile (Unit: px)",
                    "info": "Set 0 to use width default of image",
                    "default": 0
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile ",
                    "default": false
                },
                {
                    "type": "number",
                    "id": "mgb",
                    "label": "Margin bottom (Unit: px)",
                    "default": 20
                },
                {
                    "type": "number",
                    "id": "mgb_tb",
                    "label": "Margin bottom on tablet(Unit: px)",
                    "default": 20
                },
                {
                    "type": "number",
                    "id": "mgb_mb",
                    "label": "Margin bottom on mobile(Unit: px)",
                    "default": 10
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                }
            ]
        },
        {
            "type": "custom_button",
            "name": "Button",
            "settings": [
                {
                    "type": "text",
                    "id": "button_text",
                    "label": "Button label",
                    "default": "Button label",
                    "info": "If set blank will not show"
                },
                {
                    "type": "url",
                    "id": "button_link",
                    "label": "Button link",
                    "info": "If set blank will not show"
                },
                {
                    "type": "select",
                    "id": "target_link",
                    "label": "Open link in",
                    "default": "_self",
                    "options": [
                        {
                            "value": "_self",
                            "label": "Current window"
                        },
                        {
                            "value": "_blank",
                            "label": "New window"
                        }
                    ]
                },
                {
                    "type": "select",
                    "id": "fontf",
                    "default": "inherit",
                    "label": "Font family",
                    "options": [
                        {
                            "label": "Inherit",
                            "value": "inherit"
                        },
                        {
                            "label": "Font Family #1",
                            "value": "1"
                        },
                        {
                            "label": "Font Family #2",
                            "value": "2"
                        },
                        {
                            "label": "Font Family #3",
                            "value": "3"
                        }
                    ]
                },
                {
                    "type": "range",
                    "id": "button_icon_w",
                    "label": "Button icon width",
                    "min": 0,
                    "max": 50,
                    "step": 1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "select",
                    "id": "button_style",
                    "label": "Button style",
                    "options": [
                        {
                            "label": "Default",
                            "value": "default"
                        },
                        {
                            "label": "Outline",
                            "value": "outline"
                        },
                        {
                            "label": "Bordered top",
                            "value": "bordered_top"
                        },
                        {
                            "label": "Border bottom", 
                            "value": "bordered"
                        },
                        {
                            "label": "Link",
                            "value": "link"
                        }
                    ]
                },
                {
                    "type": "range",
                    "id": "border_w",
                    "label": "Border width",
                    "min": 1,
                    "max": 3,
                    "step": 1,
                    "default": 1,
                    "unit": "px"
                },
                {
                    "type": "select",
                    "id": "button_effect",
                    "label": "Hover button effect",
                    "default": "default",
                    "info": "Only working button style default, outline",
                    "options": [
                        {
                            "label": "Default",
                            "value": "default"
                        },
                        {
                            "label": "Fade",
                            "value": "fade"
                        },
                        {
                            "label": "Rectangle out",
                            "value": "rectangle-out"
                        },
                        {
                            "label": "Sweep to right",
                            "value": "sweep-to-right"
                        },
                        {
                            "label": "Sweep to left",
                            "value": "sweep-to-left"
                        },
                        {
                            "label": "Sweep to bottom",
                            "value": "sweep-to-bottom"
                        },
                        {
                            "label": "Sweep to top",
                            "value": "sweep-to-top"
                        },
                        {
                            "label": "Shutter out horizontal",
                            "value": "shutter-out-horizontal"
                        },
                        {
                            "label": "Outline",
                            "value": "outline"
                        },
                        {
                            "label": "Shadow",
                            "value": "shadow"
                        }
                    ]
                },
                {
                    "type": "color",
                    "id": "pri_cl",
                    "label": "Primary color",
                    "default": "#222"
                },
                {
                    "type": "color",
                    "id": "second_cl",
                    "label": "Secondary color",
                    "default": "#fff",
                    "info": "Only working button style default"
                },
                {
                    "type": "color",
                    "id": "pri_cl_hover",
                    "label": "Primary hover color",
                    "default": "#0ec1ae"
                },
                {
                    "type": "color",
                    "id": "second_cl_hover",
                    "label": "Secondary hover color",
                    "info": "Only working button style default, outline",
                    "default": "#fff"
                },
                {
                    "type": "range",
                    "id": "fsbutton",
                    "label": "Font size",
                    "max": 50,
                    "min": 10,
                    "step": 1,
                    "unit": "px",
                    "default": 14
                },
                {
                    "type": "range",
                    "id": "fwbutton",
                    "label": "Font weight",
                    "min": 100,
                    "max": 900,
                    "step": 100,
                    "default": 400
                },
                {
                    "type": "range",
                    "id": "button_ls",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "range",
                    "id": "button_mh",
                    "label": "Min height",
                    "min": 30,
                    "max": 80,
                    "step": 1,
                    "unit": "px",
                    "default": 42,
                    "info": "Only working button style default, outline"
                },
                {
                    "type": "range",
                    "id": "button_bdr",
                    "label": "Border radius",
                    "min": 0,
                    "max": 40,
                    "step": 1,
                    "unit": "px",
                    "default": 0,
                    "info": "Only working button style default, outline"
                },
                {
                    "type": "range",
                    "id": "button_pd_lr",
                    "label": "Padding left/right",
                    "min": 0,
                    "max": 100,
                    "step": 1,
                    "unit": "px",
                    "default": 20,
                    "info": "Only working button style default, outline"
                },
                {
                    "type": "number",
                    "id": "mgb",
                    "label": "Margin bottom",
                    "default": 0
                },
                {
                    "type": "header",
                    "content": "+ Options tablet"
                },
                {
                    "type": "range",
                    "id": "button_icon_w_tb",
                    "label": "Button icon width",
                    "min": 0,
                    "max": 50,
                    "step": 1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "range",
                    "id": "fsbutton_tb",
                    "label": "Font size",
                    "max": 50,
                    "min": 0,
                    "step": 1,
                    "unit": "px",
                    "default": 12
                },
                {
                    "type": "range",
                    "id": "button_mh_tb",
                    "label": "Min height",
                    "min": 10,
                    "max": 50,
                    "step": 1,
                    "unit": "px",
                    "default": 36,
                    "info": "Only working button style default, outline"
                },
                {
                    "type": "range",
                    "id": "button_pd_lr_tb",
                    "label": "Padding left/right",
                    "min": 0,
                    "max": 60,
                    "step": 1,
                    "unit": "px",
                    "default": 15,
                    "info": "Only working button style default, outline"
                },
                {
                    "type": "range",
                    "id": "button_ls_tb",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "mgb_tb",
                    "label": "Margin bottom",
                    "default": 0
                },
                {
                    "type": "header",
                    "content": "+ Options mobile"
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile",
                    "default": false
                },
                {
                    "type": "range",
                    "id": "button_icon_w_mb",
                    "label": "Button icon width",
                    "min": 0,
                    "max": 50,
                    "step": 1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "range",
                    "id": "fsbutton_mb",
                    "label": "Font size",
                    "max": 50,
                    "min": 0,
                    "step": 1,
                    "unit": "px",
                    "default": 10
                },
                {
                    "type": "range",
                    "id": "button_mh_mb",
                    "label": "Min height",
                    "min": 10,
                    "max": 50,
                    "step": 1,
                    "unit": "px",
                    "default": 36,
                    "info": "Only working button style default, outline"
                },
                {
                    "type": "range",
                    "id": "button_pd_lr_mb",
                    "label": "Padding left/right",
                    "min": 0,
                    "max": 100,
                    "step": 1,
                    "unit": "px",
                    "default": 15,
                    "info": "Only working button style default, outline"
                },
                {
                    "type": "range",
                    "id": "button_ls_mb",
                    "label": "Letter spacing",
                    "min": 0,
                    "max": 10,
                    "step": 0.1,
                    "unit": "px",
                    "default": 0
                },
                {
                    "type": "number",
                    "id": "mgb_mb",
                    "label": "Margin bottom)",
                    "default": 0
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                }
            ]
        },
        {
            "type": "space_html",
            "name": "Space HTML",
            "settings": [
                {
                    "type": "color",
                    "id": "color",
                    "label": "Color",
                    "default": "#fff"
                },
                {
                    "type": "range",
                    "id": "width",
                    "min": 10,
                    "max": 1000,
                    "step": 10,
                    "label": "Width (Unit: px)",
                    "unit": "px",
                    "default": 40
                },
                {
                    "type": "range",
                    "id": "height",
                    "min": 1,
                    "max": 100,
                    "step": 1,
                    "label": "Height (Unit: px)",
                    "unit": "px",
                    "default": 2
                },
                {
                    "type": "number",
                    "id": "mgb",
                    "label": "Margin bottom (Unit: px)",
                    "default": 20
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                    "type": "range",
                    "id": "width_tb",
                    "min": 10,
                    "max": 1000,
                    "step": 10,
                    "label": "Width (Tablet) (Unit: px)",
                    "unit": "px",
                    "default": 40
                },
                {
                    "type": "range",
                    "id": "height_tb",
                    "min": 1,
                    "max": 100,
                    "step": 1,
                    "label": "Height (Tablet) (Unit: px)",
                    "default": 2
                },
                {
                    "type": "number",
                    "id": "mgb_tb",
                    "label": "Margin bottom on tablet(Unit: px)",
                    "default": 20
                }, 
                {
                    "type": "paragraph",
                    "content": "————————————————"
                },
                {
                    "type": "range",
                    "id": "width_mb",
                    "min": 10,
                    "max": 1000,
                    "step": 10,
                    "label": "Width (Mobile) (Unit: px)",
                    "unit": "px",
                    "default": 40
                },
                {
                    "type": "range",
                    "id": "height_mb",
                    "min": 1,
                    "max": 100,
                    "step": 1,
                    "label": "Height (Mobile) (Unit: px)",
                    "default": 2
                },
                {
                    "type": "checkbox",
                    "id": "hidden_mobile",
                    "label": "Hidden on mobile",
                    "default": false
                },
                {
                    "type": "number",
                    "id": "mgb_mb",
                    "label": "Margin bottom on mobile(Unit: px)",
                    "default": 20
                },
                {
                    "type": "paragraph",
                    "content": "————————————————"
                }
            ]
        }
    ],
    "presets": [
        {
            "name": "Contact Form",
            "blocks": [
                { 
                    "type": "custom_text",
                    "settings": {
                        "text": "<span style=\"display: inline-block;\"><span style=\"display: flex;align-items: center;\"><svg style=\"margin-right: 4px;\" xmlns=\"http: \/\/www.w3.org\/2000\/svg\" xmlns:xlink=\"http: \/\/www.w3.org\/1999\/xlink\" xmlns: svgjs=\"http: \/\/svgjs.com\/svgjs\" width=\"30\" height=\"30\" x=\"0\" y=\"0\" viewbox=\"0 0 512 512\" xml:space=\"preserve\"><g>\n<g xmlns=\"http: \/\/www.w3.org\/2000\/svg\">\n\t<g>\n\t\t<path d=\"M256,110.062c-52.412,0-95.053,42.641-95.053,95.053s42.641,95.053,95.053,95.053s95.053-42.641,95.053-95.053    S308.413,110.062,256,110.062z M256,260.146c-30.344,0-55.031-24.687-55.031-55.031s24.687-55.031,55.031-55.031    s55.031,24.687,55.031,55.031S286.344,260.146,256,260.146z\" fill=\"#000000\" data-original=\"#000000\"><\/path>\n\t<\/g>\n<\/g>\n<g xmlns=\"http: \/\/www.w3.org\/2000\/svg\">\n\t<g>\n\t\t<path d=\"M256,0C142.899,0,50.885,92.015,50.885,205.115v5.67c0,57.2,32.794,123.856,97.474,198.113    c46.888,53.832,93.121,91.368,95.065,92.94L256,512l12.576-10.161c1.945-1.572,48.178-39.108,95.065-92.94    c64.679-74.258,97.474-140.913,97.474-198.113v-5.67C461.115,92.015,369.101,0,256,0z M421.093,210.786    c0,96.665-124.551,213.68-165.093,249.202C215.447,424.455,90.907,307.444,90.907,210.786v-5.67    c0-91.032,74.061-165.093,165.093-165.093s165.093,74.061,165.093,165.093V210.786z\" fill=\"#000000\" data-original=\"#000000\"><\/path>\n\t<\/g>\n<\/g>\n<\/g><\/svg> 30 Darrick Meadows<\/span><\/span>",
                        "remove_br_tag": false,
                        "fontf": "inherit",
                        "text_cl": "#000000",
                        "text_fs": 16,
                        "text_lh": 0,
                        "text_fw": 400,
                        "text_ls": 0,
                        "mgb": 15,
                        "font_italic": false,
                        "text_shadow": false,
                        "text_fs_tb": 16,
                        "text_lh_tb": 0,
                        "text_ls_tb": 0,
                        "mgb_tb": 15,
                        "hidden_mobile": false,
                        "text_fs_mb": 16,
                        "text_lh_mb": 0,
                        "text_ls_mb": 0,
                        "mgb_mb": 15
                    }
                },
                {
                    "type": "custom_text",
                    "settings": {
                        "text": "Suite 921 Brayanview,<br>\nHI 60451",
                        "remove_br_tag": false,
                        "fontf": "inherit",
                        "text_cl": "#868686",
                        "text_fs": 13,
                        "text_lh": 24,
                        "text_fw": 400,
                        "text_ls": 0,
                        "mgb": 16,
                        "font_italic": false,
                        "text_shadow": false,
                        "text_fs_tb": 13,
                        "text_lh_tb": 18,
                        "text_ls_tb": 0,
                        "mgb_tb": 10,
                        "hidden_mobile": false,
                        "text_fs_mb": 13,
                        "text_lh_mb": 18,
                        "text_ls_mb": 0,
                        "mgb_mb": 10
                    }
                },
                {
                    "type": "space_html",
                    "settings": {
                        "color": "#e7e7e7",
                        "width": 420,
                        "height": 1,
                        "mgb": 20,
                        "width_tb": 420,
                        "height_tb": 1,
                        "mgb_tb": 20,
                        "width_mb": 420,
                        "height_mb": 1,
                        "hidden_mobile": false,
                        "mgb_mb": 20
                    }
                },
                {
                    "type": "custom_text",
                    "settings": {
                        "text": "Open hours",
                        "remove_br_tag": false,
                        "fontf": "inherit",
                        "text_cl": "#000000",
                        "text_fs": 16,
                        "text_lh": 0,
                        "text_fw": 400,
                        "text_ls": 0,
                        "mgb": 15,
                        "font_italic": false,
                        "text_shadow": false,
                        "text_fs_tb": 16,
                        "text_lh_tb": 0,
                        "text_ls_tb": 0,
                        "mgb_tb": 10,
                        "hidden_mobile": false,
                        "text_fs_mb": 16,
                        "text_lh_mb": 0,
                        "text_ls_mb": 0,
                        "mgb_mb": 10
                    }
                },
                {
                    "type": "custom_text",
                    "settings": {
                        "text": "<div class=\"bee-d-flex bee-justify-content-between\"><span>Sunday</span> <span>10 a.m - 10 p.m</span></div><div class=\"bee-d-flex bee-justify-content-between\"><span>Sunday</span> <span>10 a.m - 10 p.m</span></div><div class=\"bee-d-flex bee-justify-content-between\"><span>Sunday</span> <span>10 a.m - 10 p.m</span></div>",
                        "tag":"div",
                        "remove_br_tag": false,
                        "fontf": "inherit",
                        "text_cl": "#868686",
                        "text_fs": 13,
                        "text_lh": 24,
                        "text_fw": 400,
                        "text_ls": 0,
                        "mgb": 20,
                        "font_italic": false,
                        "text_shadow": false,
                        "text_fs_tb": 13,
                        "text_lh_tb": 24,
                        "text_ls_tb": 0,
                        "mgb_tb": 20,
                        "hidden_mobile": false,
                        "text_fs_mb": 13,
                        "text_lh_mb": 24,
                        "text_ls_mb": 0,
                        "mgb_mb": 20
                    }
                }
            ]
        }
    ]
}
{% endschema %}