{%- liquid
  assign se_stts = section.settings
  assign se_id = section.id
  assign layout = se_stts.layout
  assign use_pagination = se_stts.use_pagination 
  assign sett_equal = se_stts.use_eq_height
  assign show_vendor = se_stts.show_vendor
  assign use_link_vendor = settings.use_link_vendor
  assign enable_rating = settings.enable_rating
  assign inc_pr = se_stts.pr_des
  assign limit = se_stts.limit
  assign collection = collections[se_stts.collection]

  assign show_img = settings.show_img
  assign placeholder_img = settings.placeholder_img
  assign isGrowaveWishlist = false
  if settings.wishlist_mode == "3" and shop.customer_accounts_enabled
    assign isGrowaveWishlist = true
  endif
  assign enable_pr_size = settings.enable_pr_size
  assign pr_size_pos = settings.pr_size_pos
  assign show_size_type = settings.show_size_type
  assign size_ck = settings.size_ck | append:',size,sizes,Größe' 
  assign get_size = size_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

  assign enable_pr_color = settings.enable_pr_color
  assign show_cl_type = settings.show_color_type
  assign color_ck = settings.color_ck | append:',color,colors,couleur,colour'
  assign get_color = color_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq

  assign price_varies_style = settings.price_varies_style
  assign app_review = settings.app_review
  assign use_countdown = se_stts.use_cdt

  assign isLoadmore = true
  assign typeAjax = 'LmDefault'
  assign show_bar_lm = fasle
  assign t4s_se_class = 't4s_nt_se_' |append:sid
  if se_stts.use_cus_css and se_stts.code_cus_css != blank
    render 'se_cus_css',code_cus_css:se_stts.code_cus_css,t4s_se_class:t4s_se_class
  endif -%}
  {%- comment %} LmDefault,LmIsotope,AjaxDefault,AjaxIsotope {% endcomment -%}

 {%- paginate collection.products by limit -%}


      <div class="{{t4s_se_class}} t4s-container t4s-collection-test-demo" data-not-main data-ntajax-options='{"id":"{{se_id}}","type":"{{typeAjax}}","isProduct":true,"updateURL":false,"updateURLPrev":false,"view":""}'>

          

         <div data-contentlm-replace class="t4s-products is--listview t4s-row t4s-gx-30 t4s-gy-30 t4s_rationt t4s-row-cols-2 t4s-row-cols-md-4 t4s-row-cols-lg-4">
               {%- if collection != blank -%}


                  {%- render 'pr-grid-item1' for collection.products as product,
                     product:product,prefix_url:prefix_url,isGrowaveWishlist:isGrowaveWishlist,show_img:show_img,show_vendor:show_vendor,use_link_vendor:use_link_vendor,
                     enable_pr_color:enable_pr_color,show_cl_type:show_cl_type,get_color:get_color,
                     enable_pr_size:enable_pr_size,pr_size_pos:pr_size_pos,get_size:get_size,show_size_type:show_size_type,
                     price_varies_style:price_varies_style,app_review:app_review,use_countdown:use_countdown, show_list_btns:false, enable_rating:enable_rating,placeholder_img:placeholder_img -%}

               {%- else -%}
               
               {%- endif -%}

         </div>

     
         {%- if paginate.pages > 1 and use_pagination != 'none' and false -%}
            
            {%- if isLoadmore -%}

               <div data-wrap-lm class="t4s-pagination-wrapper t4s-text-center t4s-w-100">
                 {%- if show_bar_lm -%}
                 <div data-wrap-lm-bar class="t4s-lm-bar">
                   {%- assign results_count = collection.products_count -%}
                   {%- assign current_pr_size = collection.products.size | plus:paginate.current_offset -%}
                    <span class="t4s-lm-bar--txt">{{ 'collections.pagination.bar_with_count_html' | t:current_count:current_pr_size,total_count:results_count}}</span>
                    <div class="t4s-lm-bar--progress t4s-pr t4s-oh"><span class="t4s-lm-bar--current t4s-pa t4s-l-0 t4s-r-0 t4s-t-0 t4s-b-0" style="width: {{current_pr_size | times:100.0 | divided_by: results_count}}%"></span></div>
                 </div>
                 {%- endif -%}
                  <a data-load-more href="{{ paginate.next.url }}" class="t4s-pr">{{ 'collections.pagination.load_more' | t }}</a>
               </div>
            {%- else -%}
                  <a href="{{ paginate.next.url }}" class="t4s-pr">{{ 'collections.pagination.view_all' | t }}</a>
            {%- endif -%}
         {%- endif -%}

      </div>
      <style>
           .t4s-collection-test-demo .t4s-product-title {
               font-size: 14px;
               margin-top: 15px;
           }
           .t4s-collection-test-demo .t4s-product-btns {
               position: absolute;
               top: 15px;
               right: 15px;
               z-index: 50;
           }
           .t4s-collection-test-demo .t4s-product-btns2 {
               position: absolute;
               top: 15px;
               left: 15px;
               z-index: 50;
           }
           .t4s-collection-test-demo .t4s-product-btns>a,
           .t4s-collection-test-demo .t4s-product-btns2>a {
               color: #fff;
               line-height: 1;
               background-color: #222;
               margin-bottom: 7px;
               padding: 2px;
               border-radius: 50%;
               width: 40px;
               height: 40px;
               display: flex;
               justify-content: center;
               align-items: center;
               text-align: center;
           }
           .t4s-collection-test-demo .t4s-svg-pr-icon svg {
               width: 16px;
               height: 16px;
           }
           .t4s-collection-test-demo .t4s-text-pr {
               display: none; 
               font-size: 0;
           }
           .t4s-collection-test-demo .t4s-product-badge {
               position: absolute;
               bottom: 0;
           }
           .t4s-collection-test-demo .t4s-product-colors {
               margin: -5px -2.5px 0;
               display: flex;
               flex-wrap: wrap;
           }
           .t4s-collection-test-demo .t4s-pr-color__item {
               margin: 5px 2.5px 0;
               display: inline-block;    
               cursor: pointer;
           }
           .t4s-collection-test-demo .t4s-pr-color__name {
               font-size: 0;    
               display: none;
           }
           .t4s-collection-test-demo .t4s-pr-color__value {
               width: 20px;
               height: 20px;
               display: block;
               border-radius: 50%;
               background-size: cover;
               background-repeat: repeat;
               background-position: center;
           }
      </style>

   {%- comment %}

       {%- if show_list_btns -%}
       {%- assign description_excerpt = product.metafields.theme.description_excerpt -%}
       <div class="t4s-rte"><p class="mb__5">{% if description_excerpt != blank %}{{ description_excerpt }}{% else %}{{ product.content | strip_html | truncatewords: 30 }}{% endif %}</p></div>
      {%- endif -%}

      {%- if show_list_btns -%}
      <div class="product-info__btns flex column mt__20">
         {%- if settings.enable_quickview %}<a href="{{pr_url}}" data-tooltip rel="nofollow" class="t4s-pr-item-btn t4s-pr-quickview" data-action-quickview><span class="t4s-svg-pr-icon"><svg viewBox="0 0 24 24"><use xlink:href="#t4s-icon-qv"></use></svg></span><span class="t4s-text-pr">{{'products.product_card.quick_view' | t}}</span></a>{% endif %}
         {%- if settings.enable_atc %}{%- render 'product-atc', pr_available:product.available, pr_url:pr_url, isDefault:isDefault, isPreoder:isPreoder,isExternal:isExternal, external_title:external_title, external_link:external_link, isGrouped:isGrouped, current_variant:current_variant -%}{% endif %}
      </div>
      {%- endif -%}
   {% endcomment -%}

{%- endpaginate -%}
</div>

{%- schema -%}
  {
  "name": "Collection Demo",
  "tag": "section",
  "class": "t4s-section t4s-section-all t4s_bk_flickity t4s_tp_countdown",
  "settings": [
      {
        "id": "collection",
        "type": "collection",
        "label": "Collection"
      },
      {
        "type": "range",
        "id": "limit",
        "min": 1,
        "max": 50,
        "step": 1,
        "label": "Maximum products to show",
        "default": 8
      },
      {
        "type": "header",
        "content": "4. SECTION SPACE"
      },
        {
          "type": "range",
          "id": "padding_top",
          "min": 0,
          "max": 100,
          "step": 4,
          "unit": "px",
          "label": "Top padding",
          "default": 36
        },
        {
          "type": "range",
          "id": "padding_bottom",
          "min": 0,
          "max": 100,
          "step": 4,
          "unit": "px",
          "label": "Bottom padding",
          "default": 36
        },
      {
        "type": "header",
        "content": "4. Custom css"
      },
      {
        "id": "use_cus_css",
        "type": "checkbox",
        "label": "Use custom css",
        "default":false,
        "info": "If you want custom style for this section."
      },
      { 
        "id": "code_cus_css",
        "type": "textarea",
        "label": "Code custom css"
      }
  ],
  "blocks": [
  ],
  "presets": [
    {
      "name": "Collection Demo",
      "category": "A"
    }
  ]
}
{% endschema %}