{%- unless section.settings.hidden_footer -%}
    <!-- sections/footer.liquid -->
    {{ 'bee-section.css' | asset_url | stylesheet_tag }}
    {{ 'bee-footer.css' | asset_url | stylesheet_tag }}
    {%- liquid 
      assign image_fix = image_nt | image_tag
      assign sid = section.id 
      assign se_stts = section.settings 
      assign se_blocks = section.blocks
      assign stt_layout = se_stts.layout
      assign stt_image_bg = se_stts.image_bg 
      if stt_layout == 'bee-se-container' 
        assign html_layout = '<div class="bee-container">__</div></div>' | split: '__'
      elsif stt_layout == 'bee-container-wrap'
        assign html_layout = '<div class="bee-container">__</div>' | split: '__'
      else
        assign html_layout = '__' | split: '__'
      endif
      assign index = 0
      assign index2 = 1
      assign arr_item = se_blocks | where:"type", 'bl_col'  
      assign sticky   = se_stts.sticky
      assign collapse = se_stts.collapse
      assign no_block_parent = true
    -%} 
    {%- capture append_style -%}
    {% if stt_image_bg.presentation.focal_point != '50.0% 50.0%' %} --background-position: {{ stt_image_bg.presentation.focal_point }}{% endif %}
   {%- endcapture -%}
    {%- style -%}
  .bee-section-footer .bee__currencies button,.bee-section-footer .bee__languages button{text-transform: uppercase;background: transparent;color: var(--link-cl);display: flex;padding: 0;align-items: center;line-height: 20px;font-size: 12px;}
  .bee-section-footer .bee__currencies button:hover,.bee-section-footer .bee__languages button:hover{background: transparent;color: var(--link-hover-cl);}
  .bee-section-footer .bee__currencies button svg,.bee-section-footer .bee__languages button svg{width: 15px;margin-left: 5px;fill: currentColor;opacity: 0.5;}
  .bee-section-footer .bee__currencies button:hover svg,.bee-section-footer .bee__languages button:hover svg{opacity: 1;}
  .bee-section-footer .bee__currencies .bee-dropdown__wrapper,.bee-section-footer .bee__languages .bee-dropdown__wrapper {background: #f6f6f8;}
  .bee-section-footer .bee__currencies .bee-drop-arrow, .bee-section-footer .bee__languages .bee-drop-arrow {background-color: #f6f6f8;display:none;}
  .bee-section-footer .bee__currencies button.is--selected, .bee-section-footer .bee__languages button.is--selected{color: var(--link-hover-cl);}
  .bee__currencies .bee-dropdown__wrapper button, .bee__languages .bee-dropdown__wrapper button {color: var(--text-color);text-transform: capitalize;width: 100%;min-height: 40px;justify-content: right;line-height: 40px;padding: 0 10px;}
  .bee__currencies .bee-dropdown__wrapper button.is--selected, .bee__currencies .bee-dropdown__wrapper button:hover, .bee__languages .bee-dropdown__wrapper button.is--selected, .bee__languages .bee-dropdown__wrapper button:hover {background: #e3e7e8;color: #000;}
  .bee-section-footer .bee__currencies:not(:last-child),.bee-section-footer .bee__languages:not(:last-child) {position: relative;padding-right: 18px;}
  .bee-section-footer .bee__currencies:not(:last-child):after,.bee-section-footer .bee__languages:not(:last-child):after{content: '';position: absolute;z-index: 1;top: 0;bottom: 0;height: 15px;margin: auto;right: 0;opacity: 0.3;}
  .bee-currencies-lang-wrap:last-child .bee__currencies, .bee-currencies-lang-wrap:last-child .bee__languages {margin-right: 0;}
  @media (min-width: 768px) {
    .bee-section-footer .bee__currencies .bee-dropdown__wrapper,.bee-section-footer .bee__languages .bee-dropdown__wrapper {min-width: 100px;max-width:300px;width: auto;}
  }
  .bee-currencies-lang-wrap {
    margin-right: 15px;
    margin-right: 18px;
    display: inline-block;
  }
  .bee-currencies-lang-wrap:last-child {
    margin:0;
  }
  .bee-currencies-lang-wrap > span {
    margin-right: 15px;
  }
  {%- endstyle -%}
    <div class="bee-section-inner bee_nt_se_{{ sid }} bee_se_{{ sid }} {{ stt_layout }} bee-footer-hidden-{{ se_stts.hidden_footer }} {%- if  se_stts.border_top == true -%} bee-footer-has-border bee-footer-border-{{ se_stts.border_pos }} {%- endif -%} {%- if stt_image_bg != blank and stt_layout != 'bee-se-container' -%}  bee-has-imgbg lazyloadbee {%- endif -%} "  {% if stt_image_bg != blank and stt_layout != 'bee-se-container' %} data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="1.5" {% endif %} {% render 'section_style', se_stts:se_stts, append_style: append_style %}>
      {{- html_layout[0] -}}
      {%- if stt_layout == 'bee-se-container' -%}<div class="bee-container-inner {% if stt_image_bg != blank %} bee-has-imgbg lazyloadbee {% endif %} "  {% if stt_image_bg != blank %}  data-bgset="{{ stt_image_bg | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="1.5" {% endif %} > {%- endif -%}
      <div class="bee-footer-wrap bee-row{% if se_stts.space_between %} bee-justify-content-lg-between{% endif %} is--footer-sticky-{{ sticky }} is--footer-collapse-{{ collapse }} bee-ver-center-{{ se_stts.align_vertical }} bee-heading-design{{ se_stts.heading_des }} bee-gx-lg-{{ se_stts.space_h_item }} bee-gx-md-{{ se_stts.space_h_item_tb }} bee-gx-{{ se_stts.space_h_item_mb }} bee-gy-lg-{{ se_stts.space_v_item }} bee-gy-md-{{ se_stts.space_v_item_tb }} bee-gy-{{ se_stts.space_v_item_mb }}" style="--heading-fs:{{ se_stts.heading_fs }}px;--heading-fw:{{ se_stts.heading_fw }};--heading-lh:{{ se_stts.heading_lh }}px;--heading-ls:{{ se_stts.heading_ls }}px;--heading-cl:{{ se_stts.heading_cl }};--text-fs:{{ se_stts.text_fs }}px;--text-fw:{{ se_stts.text_fw }};--text-cl:{{ se_stts.text_cl }};--link-cl:{{ se_stts.link_cl }};--link-hover-cl:{{ se_stts.link_hover_cl }};--heading-mgb:{{ se_stts.mgb }}px;--heading-mgb-tb:{{ se_stts.mgb_tb }}px;--heading-mgb-mb:{{ se_stts.mgb_mb }}px; ">   
        {%- if se_blocks.size > 0 -%}
          {%- for block in se_blocks %}
            {% assign block_type = block.type -%}
            {%- if block_type == 'bl_col' -%} 
              {%- liquid
                unless no_block_parent
                  echo '</div></div></div>'
                endunless
                assign no_block_parent = false
                assign bk_stts = block.settings  
                assign pd_item = bk_stts.padding_inner | remove: ' ' | split: ','
                assign pd_item_tb = bk_stts.padding_inner_tb | remove: ' ' | split: ','
                assign pd_item_mb = bk_stts.padding_inner_mb | remove: ' ' | split: ','
              -%}
                <div class="bee-custom-col bee-col-item bee-col-lg-{{ bk_stts.col_dk }} bee-col-md-{{ bk_stts.col_tb }} bee-col-{{ bk_stts.col_mb }} bl-{{ block.id }}">
                <div class="bee-col-inner {{ bk_stts.content_align }} {{ bk_stts.content_align_tb }} {{ bk_stts.content_align_mb }} {%- if bk_stts.bg_image != blank -%}  lazyloadbee bee-has-imgbg  {%- endif -%} "  {%- if bk_stts.bg_image != blank -%}  data-bgset="{{ bk_stts.bg_image | image_url: width: 1 }}" data-sizes="auto"  data-optimumx="1.5" {%- endif -%}  style="--pd-top: {{ pd_item[0] | default: 0 }};--pd-right: {{ pd_item[1] | default: 0 }};--pd-bottom: {{ pd_item[2] | default: 0 }};--pd-left: {{ pd_item[3] | default: 0 }};--pd-tb-top: {{ pd_item_tb[0] | default: 0 }};--pd-tb-right: {{ pd_item_tb[1] | default: 0 }};--pd-tb-bottom: {{ pd_item_tb[2] | default: 0 }};--pd-tb-left: {{ pd_item_tb[3] | default: 0 }};--pd-mb-top: {{ pd_item_mb[0] | default: 0 }};--pd-mb-right: {{ pd_item_mb[1] | default: 0 }};--pd-mb-bottom: {{ pd_item_mb[2] | default: 0 }};--pd-mb-left: {{ pd_item_mb[3] | default: 0 }};">
                  {% if bk_stts.col_heading != blank %}  
                    <div class="bee-footer-heading bee-d-flex bee-align-items-center bee-d-md-block bee-d-none">
                        <h3 class="bee-col-heading bee-fnt-fm-{{ se_stts.heading_fontf }}">{{ bk_stts.col_heading }}</h3>
                    </div>
                  {%- endif -%} 
                  {%- if bk_stts.col_heading_mobile != blank or bk_stts.col_heading != blank -%}   
                    <div data-footer-open class="bee-footer-heading bee-footer-heading-mobile bee-d-flex bee-align-items-center bee-d-md-none">
                      {% if bk_stts.col_heading_mobile != blank %}  
                          <h3 class="bee-col-heading bee-fnt-fm-{{ se_stts.heading_fontf }}">{{ bk_stts.col_heading_mobile }}</h3> 
                      {%- else -%}     
                          <h3 class="bee-col-heading bee-fnt-fm-{{ se_stts.heading_fontf }}">{{ bk_stts.col_heading }}</h3> 
                      {%- endif -%}     
                          <span class="bee-footer-collapse-icon"></span>
                    </div>
                  {%- endif -%} 
                  <div {% if bk_stts.col_heading != blank or bk_stts.col_heading_mobile != is--footer-collapse %}data-footer-content{% endif %} class="bee-footer-content">
                      {%- if bk_stts.menu !=blank -%} 
                        <div class="bee-footer-menu bee-menu-style-{{ bk_stts.menu_style | default:"default" }}" style="--space-item: {{ bk_stts.space_item }}px;--space-item-tb: {{ bk_stts.space_item_tb }}px;--space-item-mb: {{ bk_stts.space_item_mb }}px;--mgb: {{ bk_stts.mgb }}px;--mgb-tb: {{ bk_stts.mgb_tb }}px;--mgb-mb: {{ bk_stts.mgb_mb }}px;"> 
                          <ul class="bee-footer-linklist">
                            {%- for link in bk_stts.menu.links -%}
                            <li>
                                <a href="{{ link.url }}" class="bee-footer-link {% if link.current %}bee-footer-link-active{% endif %}" {% if link.current %}  aria-current="page" {% endif %}>
                                {%- render 'title_menu2',title: link.title -%}
                                </a>
                            </li>
                            {%- endfor -%} 
                          </ul>
                        </div> 
                      {%- endif -%}   
            {%- else -%}
              {%- liquid
                if no_block_parent
                    continue
                endif
                assign bk_stts = block.settings -%}
                {%- render 'footer_content', block: block, bk_stts: bk_stts -%} 
            {%- endif -%}
          {%- endfor -%}
        {%- endif -%}
      </div>
      {{- html_layout[1] -}}
    </div>
    {%- if sticky %}<style>@media (min-width: 1025px) { #MainContent,#shopify-section-announcement-bar,#shopify-section-top-bar,#bee-header,.bee-prefooter,.bee-section-footer{position:relative;z-index:4}#shopify-section-top-bar {z-index: 466;} #MainContent { z-index: 2;background-color: {%- if settings.body_bg != blank and settings.body_bg != 'rgba(0,0,0,0)' %}{{ settings.body_bg }}{% else %}#fff{% endif -%};}  .bee-prefooter{z-index:1}#bee-footer{position:sticky;position:-webkit-sticky;bottom:0;left:0;right:0;width:100%} }</style>{% endif -%}
    
{%- endunless -%}

{% schema %}
{
  "name": "Footer main",
  "tag": "section",
  "class": "bee-section bee-section-footer bee_tp_cdt bee-footer",
  "settings": [
    {
      "type": "header",
      "content": "1. Advance options"
    },
    {
      "type": "checkbox",
      "id": "hidden_footer",
      "label": "Hidden footer",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "sticky",
      "label": "Enable sticky footer",
      "default": false,
      "info": "Enable/disable sticky footer option on desktop"
    },
    {
      "type": "checkbox",
      "id": "collapse",
      "info": "Enable\/disable accordions mobile footer option",
      "label": "Accordions mobile",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "align_vertical",
      "label": "Content align vertical center",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "border_top",
      "label": "Border top",
      "default": true,
      "info": "Enable/disable border top option"
    },
    {
      "type": "select",
      "id": "border_pos",
      "label": "Border position",
      "options": [
        {
          "value": "full",
          "label": "Full width"
        },
        {
          "value": "in",
          "label": "In container"
        }
      ],
      "default": "full"
    },
    {
      "type": "select",
      "id": "space_h_item",
      "options": [
        {
            "value": "0", 
            "label": "0"
        },
        {
            "value": "2", 
            "label": "2px"
        },
        {
            "value": "4", 
            "label": "4px"
        },
        {
            "value": "6", 
            "label": "6px"
        },
        {
            "value": "8", 
            "label": "8px"
        },
        {
            "value": "10", 
            "label": "10px"
        },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
        {
            "value": "30",
            "label": "30px"
        }
      ],
      "label": "Space horizontal items",
      "default": "30"
    },
    {
      "type": "select",
      "id": "space_v_item",
      "options": [
        {
            "value": "0", 
            "label": "0"
        },
        {
            "value": "2", 
            "label": "2px"
        },
        {
            "value": "4", 
            "label": "4px"
        },
        {
            "value": "5", 
            "label": "5px"
        },
        {
            "value": "8", 
            "label": "8px"
        },
        {
            "value": "10", 
            "label": "10px"
        },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
        {
            "value": "30",
            "label": "30px"
        }
      ],
      "label": "Space vertical items",
      "default": "30"
    },
    {
      "type": "select",
      "id": "space_h_item_tb",
      "options": [
        {
            "value": "0", 
            "label": "0"
        },
        {
            "value": "2", 
            "label": "2px"
        },
        {
            "value": "4", 
            "label": "4px"
        },
        {
            "value": "6", 
            "label": "6px"
        },
        {
            "value": "8", 
            "label": "8px"
        },
        {
            "value": "10", 
            "label": "10px"
        },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
        {
            "value": "30",
            "label": "30px"
        }
      ],
      "label": "Space horizontal items (Tablet)",
      "default": "10"
    },
    {
      "type": "select",
      "id": "space_v_item_tb",
      "options": [
        {
            "value": "0", 
            "label": "0"
        },
        {
            "value": "2", 
            "label": "2px"
        },
        {
            "value": "4", 
            "label": "4px"
        },
        {
            "value": "5", 
            "label": "5px"
        },
        {
            "value": "8", 
            "label": "8px"
        },
        {
            "value": "10", 
            "label": "10px"
        },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
        {
            "value": "30",
            "label": "30px"
        }
      ],
      "label": "Space vertical items (Tablet)",
      "default": "30"
    },
    {
      "type": "select",
      "id": "space_h_item_mb",
      "options": [
        {
            "value": "0", 
            "label": "0"
        },
        {
            "value": "2", 
            "label": "2px"
        },
        {
            "value": "4", 
            "label": "4px"
        },
        {
            "value": "6", 
            "label": "6px"
        },
        {
            "value": "8", 
            "label": "8px"
        },
        {
            "value": "10", 
            "label": "10px"
        },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
        {
            "value": "30",
            "label": "30px"
        }
      ],
      "label": "Space horizontal items (Mobile)",
      "default": "10"
    },
    {
      "type": "select",
      "id": "space_v_item_mb",
      "options": [
        {
            "value": "0", 
            "label": "0"
        },
        {
            "value": "2", 
            "label": "2px"
        },
        {
            "value": "4", 
            "label": "4px"
        },
        {
            "value": "5", 
            "label": "5px"
        },
        {
            "value": "8", 
            "label": "8px"
        },
        {
            "value": "10", 
            "label": "10px"
        },
          {
              "value": "15",
              "label": "15px"
          },
          {
              "value": "20",
              "label": "20px"
          },
          {
              "value": "25",
              "label": "25px"
          },
        {
            "value": "30",
            "label": "30px"
        }
      ],
      "label": "Space vertical items (Mobile)",
      "default": "30"
    },
    {
      "type": "checkbox",
      "id": "space_between",
      "label": "Enable space between items (Desktop)",
      "default": false,
      "info": "Only working when items total width less than 100%. The items are evenly distributed within the alignment container along the main axis. The spacing between each pair of adjacent items is the same. The first item is flush with the main-start edge, and the last item is flush with the main-end edge."
    },
    {
      "type": "header",
      "content": "Heading options"
    },
    {
      "type": "select",
      "id": "heading_des",
      "default": "1",
      "label": "Heading design",
      "options": [
          {
              "label": "Design 1",
              "value": "1"
          },
          {
              "label": "Design 2",
              "value": "2"
          }
      ]
    },
    {
      "type": "select",
      "id": "heading_fontf",
      "default": "inherit",
      "label": "Font family",
      "options": [
          {
              "label": "Inherit",
              "value": "inherit"
          },
          {
              "label": "Font family #1",
              "value": "1"
          },
          {
              "label": "Font family #2",
              "value": "2"
          },
          {
              "label": "Font family #3",
              "value": "3"
          }
      ]
    },
    {
        "type": "range",
        "id": "heading_fs",
        "label": "Font size",
        "max": 60,
        "min": 10,
        "step": 1,
        "unit": "px",
        "default": 16
    },
    {
        "type": "range",
        "id": "heading_lh",
        "label": "Line height",
        "max": 60,
        "min": 0,
        "step": 1,
        "default": 22,
        "unit": "px",
        "info": "Set '0' to use default"            
    },
    {
        "type": "range",
        "id": "heading_fw",
        "label": "Font weight",
        "min": 100,
        "max": 900,
        "step": 100,
        "default": 500
    },
    {
        "type": "range",
        "id": "heading_ls",
        "label": "Letter spacing",
        "max": 10,
        "min": 0,
        "default": 0,
        "step": 0.1,
        "unit": "px"
    },
    {
        "type": "number",
        "id": "mgb",
        "label": "Heading margin bottom (px)",
        "default": 30
    },
    {
        "type": "number",
        "id": "mgb_tb",
        "label": "Heading margin bottom tablet (px)",
        "default": 30
    },
    {
        "type": "number",
        "id": "mgb_mb",
        "label": "Heading margin bottom mobile (px)",
        "default": 20
    },
    {
      "type": "header",
      "content": "Text content options"
    },
    {
        "type": "range",
        "id": "text_fs",
        "label": "Font size",
        "max": 50,
        "min": 10,
        "step": 1,
        "unit": "px",
        "default": 14
    },
    {
        "type": "range",
        "id": "text_fw",
        "label": "Font weight",
        "min": 100,
        "max": 900,
        "step": 100,
        "default": 400
    },
    {
      "type": "header",
      "content": "Color options"
    },
    {
      "type": "color",
      "id": "heading_cl",
      "label": "Heading color",
      "default": "#000000"
    },
    {
      "type": "color",
      "id": "text_cl",
      "label": "Text color",
      "default": "#868686"
    },
    {
      "type": "color",
      "id": "link_cl",
      "label": "Link color",
      "default": "#868686"
    },
    {
      "type": "color",
      "id": "link_hover_cl",
      "label": "Link hover color",
      "default": "#000000"
    },
     {
      "type": "color",
      "id": "border_cl_top",
      "label": "Border color",
      "default": "#e6e6e6"
    },
    {
      "type": "header",
      "content": "2. Design options"
    },
    {
      "type": "select","id": "layout","default": "bee-container-wrap","label": "Layout",
      "options": [
          { "value": "bee-se-container", "label": "Container"},
          { "value": "bee-container-wrap", "label": "Wrapped container"},
          { "value": "bee-container-fluid", "label": "Full width"}
      ]
    },
    {
      "type": "color",
      "id": "cl_bg",
      "label": "Background" 
    },
    {
      "type": "color_background",
      "id": "cl_bg_gradient",
      "label": "Background gradient"  
    },
    {
      "type": "image_picker",
      "id": "image_bg",
      "label": "Background image"
    },
    {
        "type": "text",
        "id": "mg",
        "label": "Margin",
        "info": "Margin top, margin right, margin bottom, margin left. If you do not use it please blank.",
        "placeholder": ",,50px,"
    },
    {
        "type": "text",
        "id": "pd",
        "label": "Padding",
        "info": "Padding top, padding right, padding bottom, padding left. If you do not use it please blank.",
        "default": "90px,,70px,"
    },
    {
        "type": "header",
        "content": "+ Design Tablet Options"
      },
      {
          "type": "text",
          "id": "mg_tb",
          "label": "Margin",
          "placeholder": ",,50px," 
      },
      {
          "type": "text",
          "id": "pd_tb",
          "label": "Padding",
          "default":"50px,,40px,"
      },
    {
      "type": "header",
      "content": "+ Design mobile options"
    },
    {
        "type": "text",
        "id": "mg_mb",
        "label": "Margin",
        "placeholder": ",,50px,"
    },
    {
        "type": "text",
        "id": "pd_mb",
        "label": "Padding",
        "default": "40px,,30px,"
    }
  ],
  "blocks": [
    {
      "type": "bl_col",
      "name": "Columns (Parent)",
      "settings": [
        {
          "type": "text",
          "id": "col_heading",
          "label": "Heading"
        },

        {
          "type": "header",
          "content": "+ Options for \"Menu on columns\""
        },
        {
            "type": "link_list",
            "id": "menu",
            "label": "Menu"
        },
        {
          "type": "select",
          "id": "menu_style",
          "label": "Menu style",
          "options": [
              { "value": "default", "label": "Default"},
              { "value": "inline", "label": "Inline"}
          ],
          "default": "default"
        },
        {
          "type": "range",
          "id": "space_item", 
          "label": "Space between menu items (Desktop)",
          "unit": "px",
          "min": 0,
          "max": 100,
          "default": 10,
          "step": 1
        },
        {
          "type": "range",
          "id": "space_item_tb", 
          "label": "Space between menu items (Tablet)",
          "unit": "px",
          "min": 0,
          "max": 60,
          "default": 10,
          "step": 1
        },
        {
          "type": "range",
          "id": "space_item_mb", 
          "label": "Space between menu items (Mobile)",
          "unit": "px",
          "min": 0,
          "max": 40,
          "default": 10,
          "step": 1
        },
        {
           "type": "number",
            "id": "mgb",
            "label": "Menu margin bottom (Unit: px)"
        },
        {
            "type": "number",
            "id": "mgb_tb",
            "label": "Menu margin bottom (Tablet)(Unit: px)"
        },
        {
            "type": "number",
            "id": "mgb_mb",
            "label": "Menu margin bottom (Mobile)(Unit: px)"
        },
        {
          "type": "header",
          "content": "+ Options for these columns"
        },
        {
          "type": "select",
          "id": "col_dk",
          "label": "Item width",
          "default": "3",
          "options": [
            {
              "value": "12",
              "label": "100%"
            },
            {
              "value": "11",
              "label": "91.66%"
            },
            {
              "value": "10",
              "label": "83.33%"
            },
            {
              "value": "9",
              "label": "75%"
            },
            {
              "value": "8",
              "label": "66.66%"
            },
            {
              "value": "7",
              "label": "58.33%"
            },
            {
              "value": "6",
              "label": "50%"
            },
            {
              "value": "5",
              "label": "41.66%"
            },
            {
              "value": "4",
              "label": "33.33%"
            },
            {
              "value": "3",
              "label": "25%"
            },
            {
              "value": "15",
              "label": "20%"
            },
            {
              "value": "2",
              "label": "16.66%"
            },
            {
              "value": "1",
              "label": "8.33%"
            },
            {
              "value": "auto",
              "label": "Auto"
            }
          ]
        },
        {
          "type": "select",
          "id": "col_tb",
          "label": "Item width (Tablet)",
          "default": "6",
          "options": [
            {
              "value": "12",
              "label": "100%"
            },
            {
              "value": "11",
              "label": "91.66%"
            },
            {
              "value": "10",
              "label": "83.33%"
            },
            {
              "value": "9",
              "label": "75%"
            },
            {
              "value": "8",
              "label": "66.66%"
            },
            {
              "value": "7",
              "label": "58.33%"
            },
            {
              "value": "6",
              "label": "50%"
            },
            {
              "value": "5",
              "label": "41.66%"
            },
            {
              "value": "4",
              "label": "33.33%"
            },
            {
              "value": "3",
              "label": "25%"
            },
            {
              "value": "15",
              "label": "20%"
            },
            {
              "value": "2",
              "label": "16.66%"
            },
            {
              "value": "1",
              "label": "8.33%"
            }
          ]
        },
        {
          "type": "select",
          "id": "col_mb",
          "label": "Item width (Mobile)",
          "default": "12",
          "options": [
            {
              "value": "12",
              "label": "100%"
            },
            {
              "value": "11",
              "label": "91.66%"
            },
            {
              "value": "10",
              "label": "83.33%"
            },
            {
              "value": "9",
              "label": "75%"
            },
            {
              "value": "8",
              "label": "66.66%"
            },
            {
              "value": "7",
              "label": "58.33%"
            },
            {
              "value": "6",
              "label": "50%"
            },
            {
              "value": "5",
              "label": "41.66%"
            },
            {
              "value": "4",
              "label": "33.33%"
            },
            {
              "value": "3",
              "label": "25%"
            },
            {
              "value": "15",
              "label": "20%"
            },
            {
              "value": "2",
              "label": "16.66%"
            },
            {
              "value": "1",
              "label": "8.33%"
            }
          ]
        },
        {
          "type": "select",
          "id": "content_align",
          "options": [
            {
              "label": "Left",
              "value": "bee-text-lg-start"
            },
            {
              "label": "Center",
              "value": "bee-text-lg-center"
            },
            {
              "label": "Right",
              "value": "bee-text-lg-end"
            }
          ],
          "label": "Content align",
           "default": "bee-text-lg-start"
        },
        {
          "type": "text",
          "id": "padding_inner",
          "label": "Padding inner",
          "info": "Padding top, padding right, padding bottom, padding left. If you do not use it please blank.",
          "placeholder": "30px,,30px," 
        },
        {
          "type": "header",
          "content": "+ Options on tablet"
        },
        {
          "type": "select",
          "id": "content_align_tb",
          "options": [
            {
              "label": "Left",
              "value": "bee-text-md-start"
            },
            {
              "label": "Center",
              "value": "bee-text-md-center"
            },
            {
              "label": "Right",
              "value": "bee-text-md-end"
            }
          ],
          "label": "Content align",
           "default": "bee-text-md-start"
        },
        {
          "type": "text",
          "id": "padding_inner_tb",
          "label": "Padding inner",
          "placeholder": "30px,,30px,"
        },
        {
          "type": "header",
          "content": "+ Options on mobile"
        },
        {
          "type": "text",
          "id": "col_heading_mobile",
          "label": "Heading mobile",
          "info": "Please enter the text here to show the heading for accordions on the Mobile"
        },
        {
          "type": "select",
          "id": "content_align_mb",
          "options": [
            {
              "label": "Left",
              "value": "bee-text-start"
            },
            {
              "label": "Center",
              "value": "bee-text-center"
            },
            {
              "label": "Right",
              "value": "bee-text-end"
            }
          ],
          "label": "Content align",
           "default": "bee-text-start"
        },
        {
          "type": "text",
          "id": "padding_inner_mb",
          "label": "Padding inner",
          "placeholder": "30px,,30px,"
        } 
      ]
    },
    {
      "type": "Currency_Language",
      "name": "Currency & Language",
      "settings":[
        {
          "type": "checkbox",
          "id": "show_currency",
          "label": "Show currency selector",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_language",
          "label": "Show language selector",
          "default": true 
        },
        {
          "type": "checkbox",
          "id": "use_uppercase",
          "label": "Use uppercase",
          "default": false,
          "info": "Working when not 'Use short language label list'"
        }
      ]
    },
    {
      "type": "custom_text",
      "name": "Text",
      "settings": [
          {
              "type": "textarea",
              "id": "text",
              "label": "Input text",
              "default": "Text",
              "info": "If you want to line break, please add a <br> tag in the text"
          },
          {
              "type": "checkbox",
              "id": "remove_br_tag",
              "label": "Remove <br> tag on tablet & mobile",
              "default": false
          },
          {
              "type": "select",
              "id": "tag",
              "default": "p",
              "options": [
                 {
                    "value": "h2",
                    "label": "H2"
                 },
                 {
                    "value": "h3",
                    "label": "H3"
                 },
                 {
                    "value": "h4",
                    "label": "H4"
                 },
                 {
                    "value": "h5",
                    "label": "H5"
                 },
                 {
                    "value": "h6",
                    "label": "H6"
                 },
                 {
                    "value": "p",
                    "label": "P"
                  },
                 {
                    "value": "div",
                    "label": "DIV"
                  }
              ],
              "label": "Html tag"
            },
          {
              "type": "select",
              "id": "fontf",
              "default": "inherit",
              "label": "Font family",
              "options": [
                  {
                      "label": "Inherit",
                      "value": "inherit"
                  },
                  {
                      "label": "Font family #1",
                      "value": "1"
                  },
                  {
                      "label": "Font family #2",
                      "value": "2"
                  },
                  {
                      "label": "Font family #3",
                      "value": "3"
                  }
              ]
          },
          
          {
              "type": "color",
              "id": "text_cl",
              "label": "Color text",
              "default": "#454545"
          },
          {
              "type": "range",
              "id": "text_fs",
              "label": "Font size",
              "max": 100,
              "min": 10,
              "step": 1,
              "unit": "px",
              "default": 16
          },
          {
              "type": "range",
              "id": "text_lh",
              "label": "Line height",
              "max": 100,
              "min": 0,
              "step": 1,
              "default": 0,
              "unit": "px",
              "info": "Set '0' to use default"            
          },
          {
              "type": "range",
              "id": "text_fw",
              "label": "Font weight",
              "min": 100,
              "max": 900,
              "step": 100,
              "default": 400
          },
          {
              "type": "range",
              "id": "text_ls",
              "label": "Letter spacing",
              "max": 10,
              "min": 0,
              "default": 0,
              "step": 0.1,
              "unit": "px"
          },
          {
              "type": "checkbox",
              "id": "font_italic",
              "label": "Enable font italic style",
              "default": false
          },
          {
              "type": "checkbox",
              "id": "text_shadow",
              "label": "Enable text shadow",
              "default": false
          },
          {
              "type": "number",
              "id": "mgb",
              "label": "Margin bottom",
              "default": 15
          },
          {
              "type": "header",
              "content": "+ Option on tablet"
          },
          {
              "type": "range",
              "id": "text_fs_tb",
              "label": "Font size (Tablet)",
              "max": 60,
              "min": 10,
              "step": 1,
              "unit": "px",
              "default": 16
          },
          {
              "type": "range",
              "id": "text_lh_tb",
              "label": "Line height (Tablet)",
              "max": 70,
              "min": 0,
              "step": 1,
              "default": 0,
              "unit": "px",
              "info": "Set '0' to use default"            
          },
          {
              "type": "range",
              "id": "text_ls_tb",
              "label": "Letter spacing (Tablet)",
              "min": 0,
              "max": 10,
              "step": 0.1,
              "unit": "px",
              "default": 0
          },
          {
              "type": "number",
              "id": "mgb_tb",
              "label": "Margin bottom (Tablet)",
              "default": 10
          },

          {
              "type": "header",
              "content": "+ Option on mobile"
          },
          {
              "type": "checkbox",
              "id": "hidden_mobile",
              "label": "Hidden on mobile ",
              "default": false
          },
          {
              "type": "range",
              "id": "text_fs_mb",
              "label": "Font size (Mobile)",
              "max": 60,
              "min": 10,
              "step": 1,
              "unit": "px",
              "default": 16
          },
          {
              "type": "range",
              "id": "text_lh_mb",
              "label": "Line height (Mobile)",
              "max": 70,
              "min": 0,
              "step": 1,
              "default": 0,
              "unit": "px",
              "info": "Set '0' to use default"            
          },
          {
              "type": "range",
              "id": "text_ls_mb",
              "label": "Letter spacing (Mobile)",
              "min": 0,
              "max": 10,
              "step": 0.1,
              "unit": "px",
              "default": 0
          },
          {
              "type": "number",
              "id": "mgb_mb",
              "label": "Margin bottom (Mobile)",
              "default": 10
          }
      ]
    },
    {
      "type": "html",
      "name": "HTML",
      "settings": [
        {
          "type": "html",
          "id": "html_content",
          "label": "Type html"
        },
        {
          "type": "checkbox",
          "id": "hidden_mobile",
          "label": "Hidden on mobile ",
          "default": false
        }
      ]
    },
    {
        "type": "image",
        "name": "Image (Child)",
        "settings": [
          {
            "type": "image_picker",
            "id": "image_child",
            "label": "Image (Child)"
          },
          {
            "type": "number",
            "id": "img_width",
            "label": "Image width (Unit: px)",
            "info": "Set 0 to use width default of image",
            "default": 0
          },
          {
            "type": "number",
            "id": "img_width_tb",
            "label": "Image width on tablet (Unit: px)",
            "info": "Set 0 to use width default of image",
            "default": 0
          },
          {
            "type": "number",
            "id": "img_width_mb",
            "label": "Image width on mobile (Unit: px)",
            "info": "Set 0 to use width default of image",
            "default": 0
          },
          {
              "type": "url",
              "id": "img_link",
              "label": "Image link"
          },
          {
            "type": "checkbox",
            "id": "hidden_mobile",
            "label": "Hidden on mobile ",
            "default": false
          },
          {
            "type": "number",
            "id": "mgb",
            "label": "Margin bottom (Unit: px)",
            "default": 20
          },
          {
            "type": "number",
            "id": "mgb_tb",
            "label": "Margin bottom on tablet(Unit: px)",
            "default": 20
          },
          {
            "type": "number",
            "id": "mgb_mb",
            "label": "Margin bottom on mobile(Unit: px)",
            "default": 10
          }
        ]
      },
      {
        "type": "newsletter",
        "name": "Newsletter",
        "limit": 1,
        "settings": [
            {
              "type": "select",
              "id": "newl_des",
              "label": "Design",
              "default": "1",
              "options": [
                    {
                      "value": "1",
                      "label": "Design 1"
                    },
                    {
                      "value": "2",
                      "label": "Design 2"
                    },
                    {
                      "value": "3",
                      "label": "Design 3"
                    },
                    {
                      "value": "4",
                      "label": "Design 4"
                    },
                    {
                      "value": "5",
                      "label": "Design 5"
                    }
                  ]
              },
              {
                  "type": "checkbox",
                  "id": "custom_width",
                  "label": "Enable newsletter custom width",
                  "info": "If you don't tick here, 100% is the default",
                  "default": false
              },
              {
                  "type": "range",
                  "id": "form_width",
                  "label": "Maximum form width",
                  "max": 800,
                  "min": 300,
                  "step": 5,
                  "unit": "px",
                  "default": 350
              },
              {
                  "type": "range",
                  "id": "form_width_tb",
                  "label": "Maximum form width (Tablet)",
                  "max": 800,
                  "min": 300,
                  "step": 5,
                  "unit": "px",
                  "default": 300
              },
              {
                  "type": "range",
                  "id": "form_width_mb",
                  "label": "Maximum form width (Mobile)",
                  "max": 500,
                  "min": 250,
                  "step": 2.5,
                  "unit": "px",
                  "default": 250
              },
              {
                  "type": "textarea",
                  "id": "sub_newsl",
                  "label": "Newsletter custom title",
                  "default": "*Don't worry we don't spam"
              },
              {
                "type": "range",
                "id": "fs_subnewsl",
                "min": 10,
                "max": 30,
                "step": 1,
                "unit": "px",
                "label": "Font size custom title",
                "default": 13
              },
              {
                  "type": "select",
                  "id": "newl_size",
                  "label": "Size",
                  "default": "small",
                  "options": [
                      {
                        "value": "small",
                        "label": "Small"
                      },
                      {
                        "value": "medium",
                        "label": "Medium"
                      },
                      {
                        "value": "large",
                        "label": "Large"
                      }
                    ]
                },
                {
                  "type": "select",
                  "id": "source_btn",
                  "label": "Source newsletter button",
                  "default": "text",
                  "options": [
                    {
                      "value": "icon",
                      "label": "Icon"
                    },
                    {
                      "value": "text",
                      "label": "Text"
                    }
                  ]
                },
                {
                  "type": "color",
                  "id": "input_cl",
                  "label": "Input color",
                  "default": "#868686"
                },
                {
                  "type": "color",
                  "id": "btn_cl",
                  "label": "Button color",
                  "default": "#868686"
                },
                {
                  "type": "color",
                  "id": "btn_hover_cl",
                  "label": "Button hover color",
                  "default": "#000000"
                },
                {
                  "type": "color",
                  "id": "border_cl",
                  "label": "Border color",
                  "default": "#000"
                },
                {
                  "type": "color",
                  "id": "background_cl",
                  "label": "background color",
                  "default": "#fff"
                },
                {
                  "type": "color",
                  "id": "cus_title_cl",
                  "label": "Custom title color",
                  "default": "#000"
                },
                {
                  "type": "number",
                  "id": "mgb",
                  "label": "Margin bottom"
                },
                {
                  "type": "number",
                  "id": "mgb_tb",
                  "label": "Margin bottom (Tablet)"
                },
                {
                  "type": "number",
                  "id": "mgb_mb",
                  "label": "Margin bottom (Mobile)"
                }
          ]
      },
    {
      "type": "payments",
      "name": "Payments",
      "settings": [
        {
          "type": "textarea",
          "id": "svg",
          "label": "SVG list",
          "default": "amazon_payments,american_express,apple_pay,bitcoin,dankort,diners_club,discover,dogecoin,dwolla,forbrugsforeningen,interac,google_pay,jcb,klarna,klarna-pay-later,litecoin,maestro,master,paypal,shopify_pay,sofort,visa",
          "info": "amazon_payments,american_express,apple_pay,bitcoin,dankort,diners_club,discover,dogecoin,dwolla,forbrugsforeningen,interac,google_pay,jcb,klarna,klarna-pay-later,litecoin,maestro,master,paypal,shopify_pay,sofort,visa"
        },
        {
          "type": "number",
          "id": "height",
          "label": "SVG height",
          "default": 30
        },
        {
          "type": "checkbox",
          "id": "hidden_mobile",
          "label": "Hidden on mobile ",
          "default": false
        },
        {
            "type": "number",
            "id": "mgb",
            "label": "Margin bottom (Unit: px)"
        },
        {
            "type": "number",
            "id": "mgb_tb",
            "label": "Margin bottom on tablet (Unit: px)"
        },
        {
            "type": "number",
            "id": "mgb_mb",
            "label": "Margin bottom on mobile (Unit: px)"
        }
      ]
    },
    {
      "type": "cus_menu",
      "name": "Menu",
      "settings": [
        {
            "type": "link_list",
            "id": "menu", 
            "label": "Menu"
        },
        {
          "type": "select",
          "id": "menu_style",
          "label": "Menu style",
          "options": [
              { "value": "default", "label": "Default"},
              { "value": "inline", "label": "Inline"},
              { "value": "hover_underline", "label": "Hover underline"},
              { "value": "hover_inline_underline", "label": "Hover inline underline"}
          ],
          "default": "default"
        },
        {
          "type": "range",
          "id": "space_item", 
          "label": "Space between menu items (Desktop)",
          "unit": "px",
          "min": 0,
          "max": 100,
          "default": 10,
          "step": 1
        },
        {
          "type": "range",
          "id": "space_item_tb", 
          "label": "Space between menu items (Tablet)",
          "unit": "px",
          "min": 0,
          "max": 60,
          "default": 10,
          "step": 1
        },
        {
          "type": "range",
          "id": "space_item_mb", 
          "label": "Space between menu items (Mobile)",
          "unit": "px",
          "min": 0,
          "max": 40,
          "default": 10,
          "step": 1
        },
        {
            "type": "number",
            "id": "mgb",
            "label": "Margin bottom (Unit: px)"
        },
        {
            "type": "number",
            "id": "mgb_tb",
            "label": "Margin bottom on tablet (Unit: px)"
        },
        {
            "type": "number",
            "id": "mgb_mb",
            "label": "Margin bottom on mobile (Unit: px)"
        }
      ]
    },
    {
        "type": "cus_socials",
        "name": "Socials",
        "settings": [
            {
                "type": "header",
                "content": "+ OPTIONS TITLE"
            },
            {
                "type": "text",
                "id": "title", 
                "label": "Title"
            },
            {
                "type": "select",
                "id": "fontf",
                "default": "inherit",
                "label": "Font family",
                "options": [
                    {
                        "label": "Inherit",
                        "value": "inherit"
                    },
                    {
                        "label": "Font family #1",
                        "value": "1"
                    },
                    {
                        "label": "Font family #2",
                        "value": "2"
                    },
                    {
                        "label": "Font family #3",
                        "value": "3"
                    }
                ]
            },
            {
                "type": "color",
                "id": "text_cl",
                "label": "Color text",
                "default": "#454545"
            },
            {
                "type": "range",
                "id": "text_fs",
                "label": "Font size",
                "max": 100,
                "min": 10,
                "step": 1,
                "unit": "px",
                "default": 16
            },
            {
                "type": "range",
                "id": "text_lh",
                "label": "Line height",
                "max": 100,
                "min": 0,
                "step": 1,
                "default": 0,
                "unit": "px",
                "info": "Set '0' to use default"            
            },
            {
                "type": "range",
                "id": "text_fw",
                "label": "Font weight",
                "min": 100,
                "max": 900,
                "step": 100,
                "default": 400
            },
            {
                "type": "range",
                "id": "text_ls",
                "label": "Letter spacing",
                "max": 10,
                "min": 0,
                "default": 0,
                "step": 0.1,
                "unit": "px"
            },
            {
                "type": "header",
                "content": "+ Option on tablet"
            },
            {
                "type": "range",
                "id": "text_fs_tb",
                "label": "Font size (Tablet)",
                "max": 60,
                "min": 10,
                "step": 1,
                "unit": "px",
                "default": 16
            },
            {
                "type": "range",
                "id": "text_lh_tb",
                "label": "Line height (Tablet)",
                "max": 70,
                "min": 0,
                "step": 1,
                "default": 0,
                "unit": "px",
                "info": "Set '0' to use default"            
            },
            {
                "type": "range",
                "id": "text_ls_tb",
                "label": "Letter spacing (Tablet)",
                "min": 0,
                "max": 10,
                "step": 0.1,
                "unit": "px",
                "default": 0
            },
            {
                "type": "header",
                "content": "+ Option on mobile"
            },
            {
                "type": "range",
                "id": "text_fs_mb",
                "label": "Font size (Mobile)",
                "max": 60,
                "min": 10,
                "step": 1,
                "unit": "px",
                "default": 16
            },
            {
                "type": "range",
                "id": "text_lh_mb",
                "label": "Line height (Mobile)",
                "max": 70,
                "min": 0,
                "step": 1,
                "default": 0,
                "unit": "px",
                "info": "Set '0' to use default"            
            },
            {
                "type": "range",
                "id": "text_ls_mb",
                "label": "Letter spacing (Mobile)",
                "min": 0,
                "max": 10,
                "step": 0.1,
                "unit": "px",
                "default": 0
            },
            {
                "type": "header",
                "content": "+ OPTIONS SOCIALS"
            },
            {
                "type": "select",
                "id": "social_mode",
                "label": "Socials mode",
                "options": [
                    {
                        "value": "1",
                        "label": "Follow"
                    },
                    {
                        "value": "2",
                        "label": "Share"
                    }
                ],
                "default": "1"
            },
            {
                "type": "select",
                "id": "social_style",
                "label": "Socials style",
                "options": [
                    { "value": "default", "label": "Default"},
                    { "value": "outline", "label": "Outline"},
                    { "value": "simple", "label": "Simple"}
                ],
                "default": "simple"
            },
            {
                "type": "select",
                "id": "social_size",
                "label": "Socials size",
                "options": [
                    { "value": "small", "label": "Small"},
                    { "value": "medium", "label": "Medium"},
                    { "value": "large", "label": "Large"}
                ],
                "default": "medium"
            },
            {
                "type": "range",
                "id": "bd_radius", 
                "label": "Border radius",
                "info": "Not work when socials style is \"Simple\"",
                "unit": "px",
                "min": 0,
                "max": 30,
                "default": 0,
                "step": 1
            },
            {
                "type": "checkbox",
                "id": "use_color_set",
                "label": "Use color settings",
                "info": "Default is themes color, tick here if you want custom color for icon socials.",
                "default": false
            },
            {
                "type": "header",
                "content": "only true when check to box Color Settings"
            },
            {
                "type": "color",
                "id": "pri_cl",
                "label": "Primary color",
                "default": "#868686"
            },
            {
                "type": "color",
                "id": "pri_hover_cl",
                "label": "Primary hover color",
                "default": "#000000"
            },
            {
                "type": "select",
                "id": "space_h_item",
                "options": [
                    {
                        "value": "0", 
                        "label": "0"
                    },
                    {
                        "value": "2", 
                        "label": "2px"
                    },
                    {
                        "value": "4", 
                        "label": "4px"
                    },
                    {
                        "value": "5", 
                        "label": "5px"
                    },
                    {
                        "value": "8", 
                        "label": "8px"
                    },
                    {
                        "value": "10", 
                        "label": "10px"
                    },
                    {
                        "value": "15",
                        "label": "15px"
                    },
                    {
                        "value": "20",
                        "label": "20px"
                    },
                    {
                        "value": "25",
                        "label": "25px"
                    },
                    {
                        "value": "30",
                        "label": "30px"
                    }, 
                    {
                        "value": "40",
                        "label": "40px"
                    }
                ],
                "label": "Space horizontal items",
                "default": "5"
            },
            {
                "type": "select",
                "id": "space_v_item",
                "options": [
                    {
                        "value": "0", 
                        "label": "0"
                    },
                    {
                        "value": "2", 
                        "label": "2px"
                    },
                    {
                        "value": "4", 
                        "label": "4px"
                    },
                    {
                        "value": "5", 
                        "label": "5px"
                    },
                    {
                        "value": "8", 
                        "label": "8px"
                    },
                    {
                        "value": "10", 
                        "label": "10px"
                    },
                    {
                        "value": "15",
                        "label": "15px"
                    },
                    {
                        "value": "20",
                        "label": "20px"
                    },
                    {
                        "value": "25",
                        "label": "25px"
                    },
                    {
                        "value": "30",
                        "label": "30px"
                    }
                
                ],
                "label": "Space vertical items",
                "default": "5"
            },
            {
                "type": "select",
                "id": "space_h_item_mb",
                "options": [
                    {
                        "value": "0", 
                        "label": "0"
                    },
                    {
                        "value": "2", 
                        "label": "2px"
                    },
                    {
                        "value": "4", 
                        "label": "4px"
                    },
                    {
                        "value": "5", 
                        "label": "5px"
                    },
                    {
                        "value": "6", 
                        "label": "6px"
                    },
                    {
                        "value": "8", 
                        "label": "8px"
                    },
                    {
                        "value": "10", 
                        "label": "10px"
                    },
                {
                    "value": "15",
                    "label": "15px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "25",
                    "label": "25px"
                },
                    {
                        "value": "30",
                        "label": "30px"
                    }
                ],
                "label": "Space horizontal items (Mobile)",
                "default": "2"
            },
            {
                "type": "select",
                "id": "space_v_item_mb",
                "options": [
                    {
                        "value": "0", 
                        "label": "0"
                    },
                    {
                        "value": "2", 
                        "label": "2px"
                    },
                    {
                        "value": "4", 
                        "label": "4px"
                    },
                    {
                        "value": "5", 
                        "label": "5px"
                    },
                    {
                        "value": "6", 
                        "label": "6px"
                    },
                    {
                        "value": "8", 
                        "label": "8px"
                    },
                    {
                        "value": "10", 
                        "label": "10px"
                    },
                {
                    "value": "15",
                    "label": "15px"
                },
                {
                    "value": "20",
                    "label": "20px"
                },
                {
                    "value": "25",
                    "label": "25px"
                },
                    {
                        "value": "30",
                        "label": "30px"
                    }
                ],
                "label": "Space vertical items (Mobile)",
                "default": "2"
            },
            {
                "type": "number",
                "id": "mgb",
                "label": "Margin bottom (Unit: px)"
            },
            {
                "type": "number",
                "id": "mgb_tb",
                "label": "Margin bottom on tablet (Unit: px)"
            },
            {
                "type": "number",
                "id": "mgb_mb",
                "label": "Margin bottom on mobile (Unit: px)"
            }
        ]
      },
    {
      "type": "copyR",
      "name": "Copyrights",
      "settings": [
          {
            "type": "html",
            "id": "text",
            "label": "Copyrights",
            "info": "Place here text you want to see in the copyrights area.",
            "default": "Copyright © [year] <span class=\"bee-csecondary\">Nitro<\/span> all rights reserved. Powered by <a href=\"https:\/\/beetheme.co\">Bee<\/a>"
          },
          {
            "type": "number",
            "id": "mgb",
            "label": "Margin bottom (Unit: px)"
          },
          {
              "type": "number",
              "id": "mgb_tb",
              "label": "Margin bottom on tablet (Unit: px)"
          },
          {
              "type": "number",
              "id": "mgb_mb",
              "label": "Margin bottom on mobile (Unit: px)"
          }
      ]
    }
  ],
  "default": {
    
      "blocks": [
        { "type": "bl_col",
          "settings": {
            "col_dk": "4",
            "col_tb": "6",
            "col_mb": "12",
            "col_heading_mobile":"Get in touch"
          }
        },
        { "type": "custom_text",
          "settings": {
            "text": "Customer service",
            "text_fs": 16,
            "text_ls": 0,
            "text_cl": "#151515",
            "text_fw": 700,
            "mgb": 35
          }
        },
        { "type": "custom_text",
          "settings": {
            "text": "Phone: +************",
            "text_fs": 15,
            "text_ls": 0,
            "text_cl": "#454545",
            "text_fw": 400,
            "mgb": 20
          }
        },
         { "type": "cus_menu"},

        { "type": "bl_col",
          "settings": {
            "col_dk": "4",
            "col_tb": "6",
            "col_mb": "12",
            "col_heading_mobile":"Our Stories"
          }
        },
        { "type": "custom_text",
          "settings": {
            "text": "Company",
            "text_fs": 16,
            "text_ls": 0,
            "text_cl": "#151515",
            "text_fw": 700,
            "mgb": 35
          }
        },
        { "type": "cus_menu"},

        { "type": "bl_col",
          "settings": { 
            "col_heading_mobile": "Newsletter Signup",
            "col_dk": "4",
            "col_tb": "6",
            "col_mb": "12"
          }
        },
        { "type": "custom_text",
          "settings": {
            "text": "Our newsletter",
            "text_fs": 16,
            "text_ls": 0,
            "text_cl": "#151515",
            "text_fw": 700,
            "mgb": 35
          }
        },
        { "type": "custom_text",
          "settings": {
            "text": "Join our list and get 15% off your first purchase!",
            "text_fs": 15,
            "text_ls": 0,
            "text_lh": 24,
            "text_cl": "#454545",
            "text_fw": 400,
            "mgb": 18
          }
        },
        { "type": "newsletter",
          "settings": {
            "newl_des": "2",
            "border_cl": "#aaaaaa",
            "btn_cl": "#151515",
            "sub_newsl": "*Don’t worry we don’t spam"
          }
        }
      ]
  }
}
{% endschema %}