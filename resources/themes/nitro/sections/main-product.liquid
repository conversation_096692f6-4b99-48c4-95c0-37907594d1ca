{%- comment -%}
Color_pink | alt text
beeoption0_0 | alt text 
{%- endcomment -%}
<!-- sections/main-product.liquid -->
{%- liquid
    assign isProductAvailable = product.available
    if isProductAvailable and settings.variant_remove == '2'
       assign remove_soldout = true 
    else 
       assign remove_soldout = false  
    endif 
    assign se_stts          = section.settings
    assign sid            = section.id
    assign pr_sid         = product.id | append: sid
    assign media_layout     = se_stts.media_layout
    assign pr_variants      = product.variants 
    assign variants_size    = pr_variants.size 
    assign selected_variant = product.selected_variant
    assign pr_curent        = settings.pr_curent
    if pr_curent == '1' and variants_size > 1
      assign current_variant = selected_variant
    elsif pr_curent == '2' 
      assign current_variant = selected_variant | default: pr_variants.first 
      if remove_soldout and current_variant.available == false and isProductAvailable  
        assign current_variant = product.first_available_variant 
      endif
    else 
      assign current_variant = product.selected_or_first_available_variant 
    endif
    assign PR_no_pick = false
    if pr_curent == '1' and variants_size > 1 and selected_variant == blank
      assign PR_no_pick = true
    endif
    assign isProductDefault   = product.has_only_default_variant
    
    assign ntsoldout  = false
    assign unvariants = false
    assign ck_so_un   = false
    if isProductDefault == false and variants_size > 1
      assign unavailable_prs = pr_variants | where: "available", false
      assign vls0 = product.options_with_values[0].values.size | default: 1
      assign vls1 = product.options_with_values[1].values.size | default: 1
      assign vls2 = product.options_with_values[2].values.size | default: 1
      assign ntvariants_size = vls0 | times: vls1 | times: vls2 | minus: pr_variants.size
      if unavailable_prs.size > 0 or product.available == false
        assign ntsoldout = true
      endif
      if ntvariants_size > 0
        assign unvariants = true
      endif
      if ntsoldout or unvariants
        assign ck_so_un = true
      endif
    endif
    assign pr_tags = product.tags
    
    assign pr_metafields      = product.metafields
    assign pr_metafields_meta = pr_metafields.meta
    assign meta_theme         = product.metafields.theme
    assign cus_qty            = meta_theme.cus_qty | default:1  
    assign isExternal         = false
    assign external_title     = meta_theme.external_title 
    assign external_link      = meta_theme.external_link
    if external_title != blank and external_link != blank 
       assign isExternal = true 
    endif
    assign isGrouped = false 
    if  meta_theme.grouped != blank
       assign isGrouped = true 
    endif
    assign custom_badge = meta_theme.custom_badge
    if custom_badge != blank
       assign arr_badge = custom_badge | replace: '  ', '' | replace: ' ;', ';' | replace: '; ', ';' | split: ';' | join: 'nt-bee' | escape | split: 'nt-bee'
       assign arr_badge_handle = arr_badge | join: 'nt-bee' | handle | split: 'nt-bee'
    endif
    
    assign product_media = product.media
    assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src'
    assign media_size = product_media.size
    if media_size < 1
      assign media_layout = 'no_media_size'
    elsif media_size == 1
      assign media_layout = 'one_media_size'
      elsif media_size == 2 and media_layout == 'slider_center'
      assign media_layout = 'without_thumbnails'
    endif
    assign canMedia_group = false 
    assign mediaAlt = product_media | map: 'alt' | join: 'nt-bee'
    if settings.use_group_media and variants_size > 1 and media_size > 1 and mediaAlt contains '_'
      assign canMedia_group = true
      
      if mediaAlt contains 'beeoption'
        assign ops_name   = product.options_with_values | map: 'name'
        assign ops_name_1 = product.options_by_name[ops_name[0]].values
        assign ops_name_2 = product.options_by_name[ops_name[1]].values
        assign ops_name_3 = product.options_by_name[ops_name[2]].values
      endif
    
      unless PR_no_pick
        assign isMediaHidden   = 'is--media-hide'
        assign current_option1 = current_variant.option1 | downcase 
        assign current_option2 = current_variant.option2 | downcase 
        assign current_option3 = current_variant.option3 | downcase
      endunless
    endif

    if mediaAlt contains 'break--360'
      assign has_media_360 = true 
    else
      assign has_media_360 = false 
    endif

    case se_stts.media_size
        when 'small'
            assign class_media = 'col-md-4'
            assign class_info = 'col-md-8'
            assign height = 530 
        when 'medium'
            assign class_media = 'col-md-6'
            assign class_info = 'col-md-6'
            assign height = 720 
        when 'large'
            assign class_media = 'col-md-7'
            assign class_info = 'col-md-5'
            assign height = 1090 
        when 'full'
            assign class_media = 'col-md-12 bee-product__media-full'
            assign class_info = 'col-md-12 bee-product__info-full'
            assign height = 1390 
    endcase
    
    assign image_ratio = se_stts.image_ratio
    if image_ratio == "ratioadapt"
      assign imgatt = ''
     else 
      assign imgatt = 'data-'
    endif
    if settings.enable_his and PR_no_pick == false
      assign enableHistoryState = settings.enable_his
    else
      assign enableHistoryState = false
    endif
    
    assign links_vendor = linklists['image-vendor-bee'].links
    assign pr_vendor = product.vendor
    
    assign inventory_quantity = current_variant.inventory_quantity
    assign inventory_management = current_variant.inventory_management
    if inventory_quantity <= 0  and inventory_management == 'shopify' and current_variant.available
      assign classdn1 = 'bee-dn' 
      assign classdn2 = ''
      assign isPreoder = true
    else 
      assign classdn1 = '' 
      assign classdn2 = 'bee-dn'
      assign isPreoder = false
    endif
    
    assign main_click = se_stts.main_click
    if main_click == 'pswp'
      assign class_pswp_disable = ''
    else
      assign class_pswp_disable = ' is-pswp-disable'
    endif
    
    assign seBlocks = section.blocks

    assign Block_sizeg = seBlocks | where: 'type', 'delivery_ask_size' | first
    assign bk_stts__sizeg = Block_sizeg.settings
    assign bk_size_chart = bk_stts__sizeg.size_chart | default: '1'
    assign pos_sizeg = bk_stts__sizeg.pos_sizeg
    
    assign Block_price = seBlocks | where: 'type', 'price_review' | first
    assign block_price_stts = Block_price.settings
    
    assign tabCheck      = false
    assign idTabDes      = 'bee-tab-des' | append: sid
    assign idTabReview   = 'bee-tab-review' | append: sid
    assign tabs_position = se_stts.tabs_position
    assign enable_video_looping     = se_stts.enable_video_looping
    assign enable_video_muting      = se_stts.enable_video_muting
    assign enable_video_autoplaying = se_stts.enable_video_autoplaying
    case media_layout
    when 'thumbnails_left' or 'thumbnails_bottom' or 'thumbnails_right' or 'without_thumbnails' or 'slider_center'
      assign hasIsotope = false
    else
      assign hasIsotope = true
    endcase
    assign use_link_vendor = settings.use_link_vendor

    assign isSliderCenter = false
    if media_layout == 'slider_center'
        assign isSliderCenter = true
    endif
    assign isSticky = false
    assign isStickyMB = false
    assign sticky_mode = se_stts.sticky_mode
    if sticky_mode != '0' and isGrouped == false and isProductAvailable and settings.atc_ajax_enable 
       assign isSticky = true
       if sticky_mode == '2'
        assign isStickyMB = true
       endif
    endif 
    assign ck_next_pr = false
    if se_stts.back_next_pr and collection != blank 
      assign ck_next_pr = true 
    endif 
    assign show_btn = se_stts.show_btn
    if show_btn != "hidden"
      assign slider_btns = true
    else
      assign slider_btns = false
    endif
    assign show_dots = se_stts.show_dots
    if show_dots != "hidden" and se_stts.dot_owl != 'dots-number'
      assign slider_dots = true
    else
      assign slider_dots = false
    endif
    assign padding_section = se_stts.pd | remove: ' ' | split: ','
    -%}
    {%- capture html_sizeg -%}
        {%- if bk_size_chart != '1' and Block_sizeg != blank -%}
            {%- liquid
                assign ck_s = true
                assign sc_type = bk_stts__sizeg.sc_type
                assign page_size = bk_stts__sizeg.page
                assign image = bk_stts__sizeg.image
        
                if bk_size_chart == '2' or pos_sizeg == '2'
                    assign ck_s = false
                    assign size_ck = bk_stts__sizeg.size_ck | append: ',size,sizes,Größe'
                    assign get_size = size_ck | downcase | replace: ' ,', ',' | replace: ', ', ',' | split: ',' | uniq
                    for option in product.options_with_values
                        assign name = option.name | downcase
                        if get_size contains name
                            assign name_sizeg = name
                            assign ck_s = true
                            break
                        endif
                    endfor
                endif
            -%}
            {%- capture icon_chart -%}
                {%- if bk_stts__sizeg.icon != '1' -%}
                    {%- if bk_stts__sizeg.icon == 'default' -%}
                    <svg width="20px" height="20px" viewBox="0 0 18 18"><path d="M16,4v8h-2V7h-2v5h-2V4H8v8H6V7H4v5H2V4H0v9c0,0.55,0.45,1,1,1h16c0.55,0,1-0.45,1-1V4H16z"/></svg>{%- elsif bk_stts__sizeg.icon == '3' -%}{%- assign image_icon = bk_stts__sizeg.img -%}
                        {%- if image_icon != blank -%} 
                            <img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts__sizeg.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image_icon | image_url: width: 50 }}" width="{{ image_icon.width }}" height="{{ image_icon.height }}" alt="{{ image_icon.alt }}">
                        {%- endif -%}
                    {%- else -%} 
                        <i class="bee-ani-{{ bk_stts__sizeg.ani }} {{ bk_stts__sizeg.icon_name }}"></i>
                    {%- endif -%}
                    {%- if bk_stts__sizeg.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}
                {%- endif -%}
            {%- endcapture -%}
            {%- if sc_type == '1' and page_size != blank and ck_s -%}
                <a class="bee-ch bee-btn__size-chart" data-no-instant rel="nofollow" href="{{ page_size.url }}" data-class="bee-mfp-btn-close-inline" data-id="bee-pr-popup__size-guide" data-storageid="{{ page_size.url }}" data-open-mfp-ajax data-style="max-width:931px" data-mfp-src="{{ page_size.url }}/?section_id=ajax_popup">
                    {{ icon_chart }}<span>{{ 'products.product.product_size_guide' | t }}</span>
                </a>
            {%- elsif sc_type == '2' and image != blank and ck_s -%}
                <a class="bee-ch bee-btn__size-chart" data-no-instant rel="nofollow" href="{{ product.url }}" data-bee-image-opend data-pswp-src="{{ image | image_url }}" data-pswp-w="{{ image.width }}" data-pswp-h="{{ image.height }}" data-pswp-class="pswp__size-guide">
                    {{ icon_chart }}<span>{{ 'products.product.product_size_guide' | t }}</span>
                </a>
            {%- endif -%} 
        {%- endif -%}
    {%- endcapture -%}
    {%- if Block_price != blank and product.price_varies and block_price_stts.price != '0' -%}
      {%- capture style_price -%}
      style="--price-size:{{ block_price_stts.size_price_pr }}px;--price-del-size:{{ block_price_stts.size_price_pr | times: 0.8 }}px;--price-weight:{{ block_price_stts.fw_price_pr }};--primary-price-color:{{ block_price_stts.primary_price_color }};--secondary-price-color:{{ block_price_stts.secondary_price_color }};--price-sale-color:{{ block_price_stts.price_sale }};margin-bottom: 15px;"
      {%- endcapture -%}
      {%- capture html_price -%}
        {%- render 'product-price-single', variant: current_variant, product: product, PR_no_pick: false, type_sale: block_price_stts.type_sale, price_varies_style: '0', style_inline: style_price -%}
      {%- endcapture -%}
    {%- endif -%}
    {%- capture html_pr_brc -%}
        <div class="bee-row bee-align-items-center"> 
            <div class="bee-col bee-col-item">
                {% if se_stts.pr_breadcrumb %} 
                    <nav class="bee-pr-breadcrumb">
                        <a href="{{ routes.root_url }}" class="bee-dib">{{ 'products.breadcrumb.home' | t }}</a><span class="bee-space-db"><svg version="1.1" x="0px" y="0px" width="10px" height="10px" viewBox="0 0 22 22"><polygon points="6,20 6,2 17,11 "/></svg></span>{% if collection.url != blank %}<a href="{{ collection.url }}" class="bee-dib">{{ collection.title }}</a><span class="bee-space-db"><svg version="1.1" x="0px" y="0px" width="10px" height="10px" viewBox="0 0 22 22"><polygon points="6,20 6,2 17,11 "/></svg></span>{% endif %}<span>{{ product.title }}</span>
                    </nav>
                {% endif %}
            </div> 
            {%- if ck_next_pr -%}
                <div class="bee-col-auto flex bee-align-items-center bee-col-item bee-next-prev-wrap"> 
                    <div class="bee-pr-next-prev">
                        {%- assign next_pr = collection.next_product -%}
                        {%- assign prev_pr = collection.previous_product -%}
                        {%- if prev_pr -%}<a href="{{ prev_pr.url }}" class="bee-nav-pr" data-tooltip="top" data-bee-tooltip='{{ prev_pr.title }}'><span class="bee-text-pr">{{ 'products.breadcrumb.prev' | t }}</span></a>{%- endif -%}
                        <a href="{{ collection.url }}" class="bee-nav-back" data-tooltip="top"><svg width="18" height="18" viewBox="0 0 18 18" class="bee-squares"><g><path d="M8,8H0V0h8V8z M2,6h4V2H2V6z"/></g><g><path d="M18,8h-8V0h8V8z M12,6h4V2h-4V6z"/></g><g><path d="M8,18H0v-8h8V18z M2,16h4v-4H2V16z"/></g><g><path d="M18,18h-8v-8h8V18z M12,16h4v-4h-4V16z"/></g></svg>
                                </span><span class="bee-text-pr">{{ 'products.breadcrumb.back_to' | t: title: collection.title }}</span></a>
                        {%- if next_pr -%}<a href="{{ next_pr.url }}" class="bee-nav-pr" data-tooltip="top" data-bee-tooltip='{{ next_pr.title }}'><span class="bee-text-pr">{{ 'products.breadcrumb.next' | t }}</span></a>{%- endif -%} 
                    </div>
                </div>  
            {%- endif -%} 
        </div>
    {%- endcapture -%}
    {%- if se_stts.breadcrumb_design == 'default' -%}
      {%- if ck_next_pr or se_stts.pr_breadcrumb -%}
      {{ 'bee-brc-product.css' | asset_url | stylesheet_tag }} 
      <div class="breadcrumb_pr_wrap bee-border-{{ se_stts.box_border }}">
          <div class="bee-container">{{ html_pr_brc }}</div>
      </div>
      {%- endif -%} 
    {%- endif -%}
    {{ 'pre_flickitybee.min.css' | asset_url | stylesheet_tag }}
    {{ 'bee-main-product.css' | asset_url | stylesheet_tag }}
    <div class="bee-container bee-main-product__content is--layout_{{ se_stts.pr_layout }} bee-product-media__{{ media_layout }} bee-product-thumb-size__{{ se_stts.thumb_lr_size }} bee-main-pr-has-br-{{ se_stts.breadcrumb_design }}" {%- if se_stts.pr_layout == 'full' -%} style="--pd-top: {{ padding_section[0] | default: "0px" }};--pd-right: {{ padding_section[1] | default: "1.5rem" }};--pd-bottom: {{ padding_section[2] | default: "0px" }};--pd-left: {{ padding_section[3] | default: "1.5rem" }};"{% endif %}>
      <div class="bee-row">
        <div class="bee-col-item bee-col-12 bee-main-area">
          <div data-product-featured='{"id": "{{ product.id }}","isMainProduct": true, "sectionId": "{{ sid }}", "disableSwatch": {{ isProductDefault }}, "media": {% if media_size > 1 %}true{% else %}false{% endif %},"enableHistoryState": {{ enableHistoryState }}, "formID": "#product-form-{{ pr_sid }}", "removeSoldout": {{ remove_soldout }}, "changeVariantByImg": {{ settings.use_change_variant_by_img }}, "isNoPick": {{ PR_no_pick }},"hasSoldoutUnavailable": {{ ck_so_un }},"enable_zoom_click_mb": {{ se_stts.enable_zoom_click_mb }},"main_click": "{{ main_click }}","canMediaGroup": {{ canMedia_group }},"isGrouped": {{ isGrouped }},"hasIsotope": {{ hasIsotope }},"available": {{ product.available }}, "customBadge": {{ arr_badge | json }}, "customBadgeHandle": {{ arr_badge_handle | json }},"dateStart": {{ product.created_at | date: "%s" }}, "compare_at_price": {{ current_variant.compare_at_price | json }},"price": {{ current_variant.price | json }}, "isPreoder": {{ isPreoder }}, "showFirstMedia": {{ settings.show_first_media }}, "isSticky": {{ isSticky }}, "isStickyMB": {{ isStickyMB }}, "stickyShow": "{{ se_stts.sticky_show }}", "useStickySelect": {{se_stts.enable_select}} }' class="bee-row bee-row__product is-zoom-type__{{ se_stts.zoom_tp }}" data-bee-zoom-main{% if main_click == 'zoom' %} data-zoom-options='{"type": "{{ se_stts.zoom_tp }}", "magnify": 2, "touch": false, "pr_type": "1","isZoomPR": true}'{% endif %}>
            <div class="bee-{{ class_media }} bee-col-12 bee-col-item bee-product__media-wrapper">
              {%- case media_layout -%}
    
              {%- when 'no_media_size' -%}
                    {%- assign image = settings.placeholder_img -%}
                    {%- if image != blank -%}
                    <div class="bee-product__no_media bee-pr">
                      <div data-bee-gallery data-bee-thumb-false data-main-media class="bee-product__media-item bee_{{ image_ratio }} bee_position_{{ se_stts.image_position }} bee_{{ se_stts.image_size }}">
                        <div data-bee-gallery--open data-media-type="image" class="bee_ratio bee-product__media{{ class_pswp_disable }}" {{ imgatt }}style="--aspect-ratioapt: {{ image.aspect_ratio }};--mw-media: {{ image.width }}px">
                          <noscript>{{ image | image_url: width: 1500 | image_tag: widths: '288, 576, 750, 1100, 1500', class: 'bee-img-noscript', loading: 'lazy', sizes: '(min-width: 1500px) 1500px, (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)' }}</noscript>
                          <img data-master="{{ image | image_url }}" class="lazyloadbee bee-lz--fadeIn" data-src="{{ image | image_url: width: 1 }}" data-widths="[100,200,400,600,700,800,900,1000,1200,1400,1600]" data-optimumx="1.5" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}"><span class="lazyloadbee-loader"></span>
                        </div>
                      </div>
                      <div data-product-single-badge data-sort="sale,new,soldout,preOrder,custom" class="bee-single-product-badge lazyloadbee bee-pa bee-pe-none bee-op-0" data-renderbee="css:{{ 'bee-single-pr-badge.css' | asset_url }}"></div>
                      {%- render 'product-btns', se_stts: se_stts, product: product -%}
                    </div>
                    {%- endif -%}
    
                {%- when 'one_media_size' %}{% assign media = product_media.first -%}
                    <div data-product-single-media-group class="bee-product__one_media bee-pr">
                      <div data-bee-gallery data-bee-thumb-false data-main-media class="bee-row bee-g-0 bee-product__media bee_{{ image_ratio }} bee_position_{{ se_stts.image_position }} bee_{{ se_stts.image_size }}">
                        {%- render 'product-thumbnail', media: media, imgatt: imgatt, variant_images: variant_images, loop: enable_video_looping, mute: enable_video_muting, autoplay: enable_video_autoplaying, height: height, sid: sid, class_pswp_disable: class_pswp_disable, variants_size: 1 -%}
                      </div>
                      <div data-product-single-badge data-sort="sale,new,soldout,preOrder,custom" class="bee-single-product-badge lazyloadbee bee-pa bee-pe-none bee-op-0" data-renderbee="css:{{ 'bee-single-pr-badge.css' | asset_url }}"></div>
                      {%- render 'product-btns', se_stts: se_stts, product: product -%}
                    </div>
    
              {%- when 'thumbnails_left' or 'thumbnails_bottom' or 'thumbnails_right' or 'without_thumbnails' or 'slider_center' -%}
    
              {%- liquid
                    if media_layout == 'thumbnails_left'
                        assign class_main = 'bee-col-lg bee-order-lg-last '
                        assign class_thumb = 'bee-col-lg-auto bee-order-lg-first '
                    elsif media_layout == 'thumbnails_right'
                        assign class_main = 'bee-col-lg '
                        assign class_thumb = 'bee-col-lg-auto '
                    elsif isSliderCenter and se_stts.media_size == 'full'
                        assign class_gx_lg_main = ' bee-g-md-30 '
                    elsif isSliderCenter 
                        assign class_gx_lg_main = ' bee-g-md-10 '
                    else 
                        assign class_gx_lg = ' bee-g-lg-10 '
                    endif -%}
              {{ 'bee-slider-settings.css' | asset_url | stylesheet_tag }}
              <div class="bee-row bee-g-0">
                <div data-product-single-media-group class="{{ class_main }}bee-col-12 bee-col-item bee-pr" style="--btn-distance: 15px;--space-dots: {{ se_stts.dots_space }}px;">
                  <div data-bee-gallery data-main-media data-bee-thumb-true class="bee-row bee-g-0{{ class_gx_lg_main }}{% unless isSliderCenter %} bee-slide-eff-{{ se_stts.eff }}{% endunless %} flickitybee bee_{{ image_ratio }} bee_position_{{ se_stts.image_position }} bee_{{ se_stts.image_size }}{% if slider_btns %} bee-slider-btn__in-content bee-flickity-slider bee-slider-btn-style-{{ se_stts.btn_owl }} bee-slider-btn-{{ se_stts.btn_shape }} bee-slider-btn-{{ se_stts.btn_size }} bee-slider-btn-cl-{{ se_stts.btn_cl }} bee-slider-btn-vi-{{ se_stts.btn_vi }}{% endif %}{% if slider_dots %} bee-slider-dots-{{ show_dots }} bee-dots-style-{{ se_stts.dot_owl }} bee-dots-cl-{{ se_stts.dots_cl }} bee-dots-round-{{ se_stts.dots_round }} bee-dots-align-center{% endif %}" data-flickitybee-js='{"beeid": "{{ sid }}", "status": true,"cellSelector": "[data-main-slide]:not(.is--media-hide)","isFilter": {{ canMedia_group }},"imagesLoaded": 0,"adaptiveHeight": 1, "contain": 1, "groupCells": "100%", "dragThreshold" : {% if se_stts.eff == 'fade' %}6{% else %}5{% endif %}, "cellAlign": "{% if isSliderCenter %}center{% else %}left{% endif %}","wrapAround": true,"prevNextButtons": {{ slider_btns }},"percentPosition": 1,"pageDots": {{ slider_dots }}, "autoPlay" : 0, "pauseAutoPlayOnHover" : true , "beeid": "{{ sid }}", "thumbNav": {% if media_layout != 'without_thumbnails' and isSliderCenter == false %}true{% else %}false{% endif %}, "thumbVertical": {% if media_layout != 'thumbnails_bottom' and isSliderCenter == false %}true{% else %}false{% endif %}, "isMedia": true }'>
                    {%- liquid 
                    for media in product_media 
                      if media.preview_image and media.preview_image.alt contains 'break--360'
                        break
                      endif
                      render 'product-thumbnail', media: media, imgatt: imgatt, variant_images: variant_images, loop: enable_video_looping, mute: enable_video_muting, autoplay: enable_video_autoplaying, height: height, sid: sid, isMediaHidden: isMediaHidden, current_option1: current_option1, current_option2: current_option2, current_option3: current_option3, ops_name: ops_name, ops_name_1: ops_name_1, ops_name_2: ops_name_2, ops_name_3: ops_name_3, class_pswp_disable: class_pswp_disable, variants_size: variants_size
                    endfor
                    if has_media_360
                    render 'product-360', product: product, sid: sid
                    endif -%}
                  </div>
                  {%- if show_dots != "hidden" and se_stts.dot_owl == 'dots-number' -%}
                  <div class="carousel--status{{ sid }} bee-dots-num-cl-{{ se_stts.dots_cl }} bee-slider-dots-number-{{ show_dots }} bee-dots-number-round-{{ se_stts.dots_round }} bee-dots-num-layout"><span data-current-slide></span> / <span data-total-number></span></div>
                  {%- endif -%}
                  <div data-product-single-badge data-sort="sale,new,soldout,preOrder,custom" class="bee-single-product-badge lazyloadbee bee-pa bee-pe-none bee-op-0" data-renderbee="css:{{ 'bee-single-pr-badge.css' | asset_url }}"></div>
                  {%- render 'product-btns', se_stts: se_stts, product: product -%}
                </div>
    
                {%- if media_layout != 'without_thumbnails' and isSliderCenter == false -%}
                <div class="{{ class_thumb }}bee-col-12 bee-col-item bee-col-thumb bee-pr bee-oh">
                  <div data-thumb__scroller="{{ sid }}" class="bee-carousel__nav-scroller is__position-{{ media_layout | remove: 'thumbnails_' }}">
                    <div class="bee-row bee-row-mt bee-row-cols-6 bee_{{ image_ratio }} bee_position_{{ se_stts.image_position }} bee_{{ se_stts.image_size }} {{ class_gx_lg }}bee-g-5 bee-carousel__nav carousel__nav--{{ sid }} carousel__nav-hover{{ se_stts.thumb_lr_hover }}">
                      {%- for media in product_media limit: 5 %}{% assign image = media.preview_image -%} 
                        <div class="bee-col-item bee-product__thumb-item"><div class="bee_ratio bee-product__thumb" {{ imgatt }}style="--aspect-ratioapt: {{ image.aspect_ratio }}"></div></div>
                      {%- endfor -%}
                    </div>
                  </div>
                  <button type="button" data-thumb-btn__prev="{{ sid }}" aria-label="Previous" class="btn_pnav_prev bee-pa bee-pe-none bee-op-0"></button>
                  <button type="button" data-thumb-btn__next="{{ sid }}" aria-label="Next" class="btn_pnav_next bee-pa bee-pe-none bee-op-0"></button>
                </div>
                {%- endif -%}
    
              </div> 
    
                {%- when 'one_column' or 'two_columns' or 'combined_grid' or 'combined_grid_2' -%}
                    {%- liquid
                        if media_layout == 'one_column'
                            assign class_col = 'bee-col-md-12 '
                            assign row_g = '0'
                        elsif media_layout == 'two_columns'
                            assign class_col = 'bee-col-md-6 '
                            assign row_g = '10'
                        else 
                            assign class_col = 'bee-col-md-12 '
                            assign row_g = '10'
                        endif 
                    -%}
                    <div class="bee-row bee-g-0">
                        <div data-product-single-media-group class="{{ class_main }}bee-col-12 bee-col-item bee-pr">
                            <div data-media-sizes="{{ product_media.size }}" data-bee-gallery data-bee-thumb-true data-main-media class="bee-main-media bee-row bee-g-{{ row_g }} bee-slide-eff-{{ se_stts.eff }} flickitybee isotopebee isotopebee-later carousel-disable-md bee_{{ image_ratio }} bee_position_{{ se_stts.image_position }} bee_{{ se_stts.image_size }}{% if slider_btns %} bee-flickity-slider bee-slider-btn-style-{{ se_stts.btn_owl }} bee-slider-btn-{{ se_stts.btn_shape }} bee-slider-btn-{{ se_stts.btn_size }} bee-slider-btn-cl-{{ se_stts.btn_cl }} bee-slider-btn-vi-{{ se_stts.btn_vi }}{% endif %}" 
                                data-isotopebee-js='{"transitionDuration": 0,"itemSelector-": "[data-main-slide]:not(.is--media-hide)","filter": "[data-main-slide]:not(.is--media-hide)", "layoutMode": "packery" }'
                                data-flickitybee-js='{"cellSelector": "[data-main-slide]:not(.is--media-hide)","isFilter": {{ canMedia_group }},"watchCSS": true,"imagesLoaded": 0,"adaptiveHeight": 1, "contain": 1, "groupCells": "100%", "dragThreshold" : {% if se_stts.eff == 'fade' %}6{% else %}5{% endif %}, "cellAlign": "left","wrapAround": true,"prevNextButtons": {{ slider_btns }},"percentPosition": 1,"pageDots": false, "autoPlay" : 0, "pauseAutoPlayOnHover" : true , "beeid": "{{ sid }}", "thumbNav": false, "thumbVertical": false, "isMedia": true }'>
                                {%- liquid 
                                for media in product_media
                                  if media.preview_image and media.preview_image.alt contains 'break--360'
                                     break
                                  endif
                                  render 'product-thumbnail', media: media, imgatt: imgatt, variant_images: variant_images, loop: enable_video_looping, mute: enable_video_muting, autoplay: false, height: height, sid: sid, isMediaHidden: isMediaHidden, current_option1: current_option1, current_option2: current_option2, current_option3: current_option3, class_col: class_col, ops_name: ops_name, ops_name_1: ops_name_1, ops_name_2: ops_name_2, ops_name_3: ops_name_3, class_pswp_disable: class_pswp_disable, variants_size: variants_size
                                endfor
                                if has_media_360
                                render 'product-360', product: product, sid: sid
                                endif -%}
                            </div>
                            <link href="{{ 'bee-single-pr-badge.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
                            <div data-product-single-badge data-sort="sale,new,soldout,preOrder,custom" class="bee-single-product-badge bee-pa bee-pe-none bee-op-0"></div>
                            {%- render 'product-btns', se_stts: se_stts, product: product -%}
                        </div>
                    </div>
                {%- endcase -%}
            </div>
            <div data-bee-zoom-info class="bee-{{ class_info }} bee-col-12 bee-col-item bee-product__info-wrapper bee-pr">
              <div id="product-zoom-{{ section.id }}" class="bee-product__zoom-wrapper"></div>
              <div id="ProductInfo-template--{{ sid }}__main" class="bee-product__info-main {% if se_stts.enable_space_content %}bee-product__info-space{% endif %}{% if se_stts.enable_sticky_info %} bee-product__info-container--sticky{% endif %}">
                <div class="bee-product__info-container">
                    {%- if se_stts.breadcrumb_design == 'inner_content' -%}
                      {%- if ck_next_pr or se_stts.pr_breadcrumb -%}
                        {{ 'bee-brc-product.css' | asset_url | stylesheet_tag }} 
                        <div class="breadcrumb_pr_wrap">{{ html_pr_brc }}</div>
                      {%- endif -%} 
                    {%- endif -%}
                    {%- for block in seBlocks %}{% assign bk_stts = block.settings %}{% assign bk_id = block.id -%}
                    {%- case block.type -%}
                    
                      {%- when 'properties' %}{% continue -%}
                      {%- when 'title' -%}
                          {% liquid 
                              if bk_stts.lh_pr == 0
                                  assign lh_pr = 1
                              else
                                  assign lh_pr = bk_stts.lh_pr | append: 'px'
                              endif  
                          -%}
                          <h1 class="bee-product__title" data-lh="{{bk_stts.lh_pr_mb}}" data-lh-md="{{bk_stts.lh_pr_tb}}" data-lh-lg="{{ lh_pr }}" style="--title-family: var(--font-family-{{ bk_stts.fnt_df_pr }});--title-style: {{ bk_stts.txt_tr_pr }};--title-size: {{ bk_stts.size_pr }}px;--title-weight: {{ bk_stts.fw_pr }};--title-line-height: {{ lh_pr }};--title-spacing: {{ bk_stts.ls_pr }}px;--title-size-tb: {{ bk_stts.size_pr_tb }}px;--title-line-height-tb: {{ bk_stts.lh_pr_tb }};--title-spacing-tb: {{ bk_stts.ls_pr_tb }}px;--title-size-mb: {{ bk_stts.size_pr_mb }}px;--title-line-height-mb: {{ bk_stts.lh_pr_mb }};--title-spacing-mb: {{ bk_stts.ls_pr_mb }}px;--title-color: {{ bk_stts.pr_title_color }};" {{ block.shopify_attributes }}>{{ product.title }}</h1>
                      {%- when 'price_review' -%}
                          <div class="bee-product__price-review" style="--price-size:{{ bk_stts.size_price_pr }}px;--price-del-size:{{ bk_stts.size_price_pr | times : 0.8 }}px;--price-weight:{{ bk_stts.fw_price_pr }};--primary-price-color:{{ bk_stts.primary_price_color }};--secondary-price-color:{{ bk_stts.secondary_price_color }};--price-sale-color:{{ bk_stts.price_sale }}">
                            {%- render 'product-price-single', variant: current_variant, product: product, PR_no_pick: PR_no_pick, type_sale: bk_stts.type_sale, price_varies_style: bk_stts.price -%}
                          {%- if bk_stts.rating -%}
                            <div class="bee-product__review axc">
                              {%- assign pid = product.id -%}
                              <a href="#{{ idTabReview }}" data-go-id="#{{ idTabReview }}" data-no-instant class="bee-d-inline-block">
                                {%- case settings.app_review -%}
                                  {%- when '1' -%}
                                    <span class="shopify-product-reviews-badge" data-id="{{ pid }}"></span>
                                  {%- when '2' -%}
                                    <div class="review-widget"><ryviu-widget-total reviews_data="{{ pr_metafields.ryviu.product_reviews_info | escape }}" product_id="{{ pid }}" handle="{{ product.handle }}"></ryviu-widget-total></div>
                                  {%- when '3' -%}
                                    <div product-id="{{ pid }}" class="alr-display-review-badge"></div>
                                  {%- when '4' -%}
                                    <div class="loox-rating" data-id="{{ pid }}" data-rating="{{ pr_metafields.loox.avg_rating }}" data-raters="{{ pr_metafields.loox.num_reviews }}"></div>
                                  {%- when '5' -%}
                                    {%- capture the_snippet_review_avg %}{% render 'ssw-widget-avg-rate-profile' %}{% endcapture -%}
                                    {%- unless the_snippet_review_avg contains 'Liquid error' %}{{ the_snippet_review_avg }}{% endunless -%}
                                  {%- when '7' -%}{%- render 'judgeme_widgets', widget_type: 'judgeme_preview_badge', jm_style: '', concierge_install: false, product: product -%}
                                  {%- when '8' -%}<div id="scm-product-detail-rate" class="scm-reviews-rate" data-rate-version2= {{ product.metafields.scm_review_importer.reviewsData.reviewCountInfo | json }} data-product-id= {{ product.id }}></div>
                                  {%- else -%}
                                    <div class="bee-pr_rating bee-review_pr_other">{{ bk_stts.review_liquid }}</div>
                                {%- endcase -%}
                              </a>
                            </div>
                          {%- endif -%}
                          </div>
                          {%- if bk_stts.tax_ship and cart.taxes_included or shop.shipping_policy.body != blank -%}
                            <div class="bee-product__policies bee-rte" data-product-policies>
                              {%- if cart.taxes_included -%}
                                {{ 'products.product.include_taxes' | t }}
                              {%- endif -%}
                              {%- if shop.shipping_policy.body != blank -%}
                                {{ 'products.product.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                              {%- endif -%}
                            </div>
                          {%- endif -%}
                      {%- when 'line' -%}
                        <div class="bee-product__line" style="--height-line: {{ bk_stts.height_line }}px;--space-tb: {{ bk_stts.space_tb }}px;--space-tb-mb: {{ bk_stts.space_tb_mb }}px;--color-line: {{ bk_stts.color_line }}"></div>
                      {%- when 'description' -%}
                        {%- assign pr_des = product.description -%}
                        {%- assign description_excerpt = meta_theme.description_excerpt | default: bk_stts.text -%}
                        {% if description_excerpt == blank and pr_des == blank %}{% continue %}{% endif -%}
                        {%- capture readm -%}
                          {%- if bk_stts.readm %}<button class="bee-product__description_readm" data-go-id='#{{ idTabDes }}'>{{ bk_stts.readm_txt }}</button>{% endif -%}
                        {%- endcapture -%}
                        <div class="bee-product__description bee-rte" {{ block.shopify_attributes }}>
                          {%- if bk_stts.des == '1' -%}{{- pr_des -}}
                          {%- elsif description_excerpt != blank -%}{{- description_excerpt -}}{{ readm }}
                          {%- else -%}<p>{{- pr_des | strip_html | truncatewords: bk_stts.length -}}{{ readm }}</p>
                          {%- endif -%}
                        </div>
                      {%- when 'form' -%}
                        {%- comment -%}
                        {%- liquid
                          assign product_list = meta_theme.grouped.value
                          assign advance_pr_type = meta_theme.advance_pr_type.value
                          assign advance_label = meta_theme.advance_label.value -%}
                        {%- endcomment -%}

                        {%- liquid
                          assign product_list = bk_stts.product_list
                          assign advance_pr_type = bk_stts.advance_pr_list
                          assign advance_label = bk_stts.advance_label
                          assign is_fit_ratio_img = false
                          if variant_images.size > 0 and bk_stts.enable_fit_ratio_img and bk_stts.color_mode contains 'variant_image'
                            assign is_fit_ratio_img = true
                            assign first_ratio_img = variant_images[0].aspect_ratio
                          endif
                          assign arr_properties = seBlocks | where: 'type', 'properties'
                          if product_list == blank
                            render 'product-form', type: 'main', product: product, bk_stts: bk_stts, pr_sid: pr_sid, isExternal: isExternal, external_title: external_title, external_link: external_link, isProductAvailable: isProductAvailable, isProductDefault: isProductDefault, current_variant: current_variant, remove_soldout: remove_soldout, PR_no_pick: PR_no_pick, isPreoder: isPreoder, is_fit_ratio_img: is_fit_ratio_img, first_ratio_img: first_ratio_img, name_sizeg: name_sizeg, html_sizeg: html_sizeg, pos_sizeg: pos_sizeg, advance_pr_type: advance_pr_type, advance_label: advance_label, html_price: html_price, shopify_attributes: block.shopify_attributes, arr_properties: arr_properties, cus_qty: cus_qty
                          else
                            render 'grouped-form', product: product, bk_stts: bk_stts, pr_sid: pr_sid, product_list: product_list, shopify_attributes: block.shopify_attributes, cus_qty: cus_qty
                          endif 
                        -%}
                      {%- when 'delivery_ask_size' -%}
                        <div class="bee-extra-link" {{ block.shopify_attributes }}>
                          {%- assign page_delivery = bk_stts.page_dr %}{% if bk_stts.delivery and page_delivery != blank -%}
                            <a class="bee-ch" data-no-instant rel="nofollow" href="{{ page_delivery.url }}" data-class="bee-mfp-btn-close-inline" data-id="bee-pr-popup__delivery" data-storageid="{{ page_delivery.url }}" data-open-mfp-ajax data-style="max-width:931px" data-mfp-src="{{ page_delivery.url }}/?section_id=ajax_popup"><svg width="20px" height="20px" viewBox="0 0 18 18"><path d="M17.99,4.95c-0.01-0.11-0.02-0.21-0.06-0.31c0-0.01-0.01-0.03-0.01-0.04c-0.05-0.1-0.11-0.2-0.19-0.28  c-0.02-0.02-0.05-0.04-0.07-0.07c-0.03-0.03-0.06-0.06-0.1-0.09l-6-4C11.31,0,11-0.04,10.71,0.04l-10,3 C0.69,3.05,0.67,3.06,0.65,3.07C0.62,3.08,0.59,3.1,0.56,3.11C0.48,3.15,0.41,3.2,0.34,3.26C0.32,3.28,0.3,3.3,0.28,3.32 C0.2,3.4,0.13,3.49,0.09,3.6C0.08,3.62,0.08,3.64,0.07,3.66C0.05,3.72,0.02,3.78,0.01,3.85c0,0.03,0,0.06,0,0.1 C0.01,3.97,0,3.98,0,4v10c0,0.38,0.21,0.72,0.55,0.89l6,3c0.01,0,0.02,0,0.03,0.01C6.71,17.96,6.85,18,7,18 c0.1,0,0.19-0.01,0.29-0.04l10-3C17.71,14.83,18,14.44,18,14V5C18,4.98,17.99,4.97,17.99,4.95z M10.84,2.09l3.84,2.56l-2.02,0.61 c-0.04-0.03-0.07-0.07-0.11-0.09L8.81,2.7L10.84,2.09z M6.31,3.45L10.18,6L7.16,6.91L3.32,4.35L6.31,3.45z M2,5.87l4,2.67v6.85l-4-2 V5.87z M8,15.66V8.74l3-0.9V9c0,0.55,0.45,1,1,1s1-0.45,1-1V7.24l3-0.9v6.91L8,15.66z"/></svg>{{ 'products.product.delivery_return' | t }}</a>
                          {%- endif -%}
              
                          {%- if bk_stts.ask -%}
                            <a class="bee-ch" data-no-instant rel="nofollow" href="{{ product.url }}" data-class="bee-mfp-btn-close-inline" data-id="bee-pr-popup__contact" data-storageid="contact_product{{ product.id }}" data-open-mfp-ajax data-style="max-width:563px" data-mfp-src="{{ product.url }}/?section_id=ajax_popup" data-phone='{{ bk_stts.phone }}'><svg width="20px" height="20px" viewBox="0 0 18 18"><path d="M17,3H1C0.45,3,0,3.45,0,4v10c0,0.55,0.45,1,1,1h16c0.55,0,1-0.45,1-1V4C18,3.45,17.55,3,17,3z M13.46,5L9,6.91L4.54,5H13.46z M2,13V6.09l7,3l7-3V13H2z"/></svg>{{ 'products.product.ask_question' | t }}</a>
                          {%- endif -%}
                          {%- if bk_size_chart != '1' and pos_sizeg == '1' %}{{ html_sizeg }}{% endif -%}
                        </div>
                    {%- when 'meta' -%}
                      {%- assign ck_meta = false -%}
                      {%- capture meta -%}
                          {%- if bk_stts.show_options and product.has_only_default_variant == false -%}{%- assign ck_meta = true -%}
                              {%- for product_option in product.options_with_values -%}
                                  <div class="bee-option-wrapper">{{ product_option.name | escape }}: <span class="bee-productMeta__value bee-option-value bee-csecondary bee-dib">{{ product_option.values | join: ', ' }}</span></div>
                              {%- endfor -%}
                          {%- endif -%}

                          {%- if bk_stts.show_vendor and pr_vendor != blank %}{% assign ck_meta = true -%}
                            {%- liquid
                            assign pr_vendor_url = pr_vendor | url_for_vendor
                            if use_link_vendor
                              assign pr_vendor_handle = pr_vendor | handle
                              assign collection_vendor = collections[pr_vendor_handle]
                              assign pr_vendor_url = collection_vendor.url | default: pr_vendor_url
                            endif -%}
                            <div class="bee-vendor-wrapper">{{ 'products.product.product_vendor' | t }} <span class="bee-productMeta__value bee-vendor-value bee-csecondary bee-dib"><a href="{{ pr_vendor_url }}">{{ pr_vendor }}</a></span></div>
                          {%- endif -%}

                          {%- if bk_stts.show_type and product.type != blank -%} {%- assign ck_meta = true -%}
                            {%- liquid
                            if settings.use_link_type
                            assign pr_type_handle = product.type | handle
                            assign collection_type = collections[pr_type_handle]
                            endif -%}
                          <div class="bee-type-wrapper">{{ 'products.product.product_type' | t }} <span class="bee-productMeta__value bee-type-value bee-csecondary bee-dib"><a href="{% if settings.use_link_type and collection_type.url != blank %}{{ collection_type.url }}{% else %}{{ product.type | url_for_type }}{% endif %}">{{ product.type }}</a></span></div>
                          {%- endif -%}
              
                          {%- if bk_stts.show_sku -%} {%- assign ck_meta = true -%}
                            <div class="bee-sku-wrapper{% if current_variant.sku == blank or current_variant.sku == '' %} bee-dn{% endif %}" data-product-sku>{{ 'products.product.product_sku' | t }} <span class="bee-productMeta__value bee-sku-value bee-csecondary bee-dib" data-product__sku-number>{{ current_variant.sku }}</span></div>
                          {%- endif -%}
              
                          {%- if bk_stts.show_barcode -%} {%- assign ck_meta = true -%}
                            <div class="bee-barcode-wrapper{% if current_variant.barcode == blank or current_variant.barcode == '' %} bee-dn{% endif %}" data-product-barcode>{{ 'products.product.product_barcode' | t }} <span class="bee-productMeta__value bee-barcode-value bee-csecondary bee-dib" data-product__barcode-number>{{ current_variant.barcode }}</span></div>
                          {%- endif -%}
              
                          {%- if bk_stts.show_available -%} {%- assign ck_meta = true -%}
                            <div data-product-available class="bee-available-wrapper">{{ 'products.product.available' | t }}
                              <span class="bee-productMeta__value bee-available-value">
                                <span data-available-status class="bee-available-status bee-csecondary bee-dib{% unless isProductAvailable %} bee-dn{% endunless %}">
                                  <span data-instock-status class="{{ classdn1 }}">{{ 'products.product.in_stock_status' | t }}</span>
                                  <span data-preorder-status class="{{ classdn2 }}">{{ 'products.product.pre_oder_status' | t }}</span>
                                </span>
                                <span data-soldout-status class="bee-soldout-status bee-csecondary bee-dib{% if isProductAvailable %} bee-dn{% endif %}">{{ 'products.product.sold_out_status' | t }}</span>
                              </span>
                            </div>
                          {%- endif -%}
              
                          {%- if product.collections.size > 0 and bk_stts.show_category %}{% assign ck_meta = true -%}
                            <div class="bee-collections-wrapper">{{ 'products.product.product_category' | t }}
                              {% for collection in product.collections -%}{{ collection.title | link_to: collection.url, class: 'bee-dib' }}{%- if forloop.last == false -%}, {% endif -%}{%- endfor -%}
                            </div>
                          {%- endif -%}
              
                          {%- if pr_tags.size > 0 and bk_stts.show_tags and product.collections.size > 0  -%}

                              {%- liquid
                              assign ck_meta = true
                              assign bee_pr_tags = pr_tags | where:'bee_'
                              if pr_tags.size == bee_pr_tags.size
                               assign show_tag_final = false
                              else
                               assign show_tag_final = true
                              endif -%}
                              
                              {%- if show_tag_final -%}
                                {%- assign tag_cat_url = collection.url | default: product.collections.first.url -%}
                                <div class="bee-tags-wrapper">{{ 'products.product.product_tag' | t }}
                                    {% for tag in pr_tags -%}
                                        {%- if tag contains 'bee_' or tag contains 'badge_' %}{% continue %}{% endif -%}
                                        <a class="bee-dib" href="{{ tag_cat_url }}/{{ tag | handle }}">{{ tag | remove: 'type ' | remove: 'Type ' }}</a>{%- unless forloop.last -%}, {% endunless -%}
                                    {%- endfor -%}
                                </div> 
                              {%- endif -%}

                          {%- endif -%}
                      {%- endcapture -%}
                      {%- if ck_meta -%}<div class="bee-product__meta{% if bk_stts.pr_meta_horizontal %} bee-product__meta-horizontal{% endif %}" {{ block.shopify_attributes }}>{{- meta -}}</div>{%- endif -%}

                      {%- when 'socials' -%}
                          <div class="bee-product__social-share bee-text-{{ bk_stts.socials_align }} bee-d-inline-flex bee-align-items-center" {{ block.shopify_attributes }}>
                              <span class="bee-product__social-title">{{ bk_stts.title }}:</span>
                              {%- liquid 
                                  assign pri_cl_lightness       = bk_stts.pri_cl | color_extract: 'lightness'
                                  assign pri_hover_cl_lightness       = bk_stts.pri_hover_cl | color_extract: 'lightness'
                                  if pri_cl_lightness < 85
                                      assign pri_cl2 = "#fff"
                                  else 
                                      assign pri_cl2 = "#000"
                                  endif
                                  if pri_hover_cl_lightness < 85
                                      assign pri_hover_cl2 = "#fff"
                                  else 
                                      assign pri_hover_cl2 = "#000"
                                  endif
                              -%}
                              {%- if settings.share_source == '1' -%}
                                  {{ 'bee-icon-social.css' | asset_url | stylesheet_tag }}
                                  <div class="bee-socials-block social-{{ block.id }}" style="--pri-cl: {{ bk_stts.pri_cl }};--pri-cl2: {{ pri_cl2 }};--pri-hover-cl: {{ bk_stts.pri_hover_cl }};--pri-hover-cl2: {{ pri_hover_cl2 }};--bd-radius: {{ bk_stts.bd_radius }}px;">
                                      {%- if bk_stts.social_mode == '1' -%}{%- assign follow_social = true -%}{%- endif -%} 
                                      {%- render 'social_sharing', style: bk_stts.social_style, use_color_set: bk_stts.use_color_set, size: bk_stts.social_size, space_h_item: bk_stts.space_h_item, space_h_item_mb: bk_stts.space_h_item_mb, space_v_item: bk_stts.space_v_item, space_v_item_mb: bk_stts.space_v_item_mb, follow_social: follow_social -%} 
                                  </div> 
                              {%- elsif settings.share_source == '3' -%}
                                  {{ 'bee-icon-social.css' | asset_url | stylesheet_tag }}
                                  {%- capture the_snippet_share -%}{% render 'ssw-widget-share-links' with 1 %}{%- endcapture -%}
                                  {%- unless the_snippet_share contains 'Liquid error' %}{{ the_snippet_share }}{%- endunless -%}
                              {%- else -%}
                                  {{ 'bee-icon-social-addthis.css' | asset_url | stylesheet_tag }}
                                  <div data-bee-addthis class="bee-product__social bee__addthis addthis_inline_share_toolbox_icxz bee-socials bee-socials-size-{{ bk_stts.social_size }} bee-socials-style-{{ bk_stts.social_style }} bee-color-set-{{ bk_stts.use_color_set }}" style="--pri-cl: {{ bk_stts.pri_cl }};--pri-cl2: {{ pri_cl2 }};--pri-hover-cl: {{ bk_stts.pri_hover_cl }};--pri-hover-cl2: {{ pri_hover_cl2 }};--bd-radius: {{ bk_stts.bd_radius }}px;--social-mgr: {{ bk_stts.space_h_item }}px;--social-mgb: {{ bk_stts.space_v_item }}px;--social-mgr-mb: {{ bk_stts.space_h_item_mb }}px;--social-mgb-mb: {{ bk_stts.space_v_item_mb }}px;"></div>
                              {%- endif -%}
                          </div>
                      {%- when 'vendor_img' -%}
                          {%- liquid 
                          if pr_vendor == blank 
                           continue 
                          endif
                          assign img_vendor = links_vendor | where: 'title', pr_vendor | first
                          assign name_img_vendor = img_vendor.url | split: '?' | first | split : '/' | last
                          assign image = images[name_img_vendor]
                          assign pr_vendor_url = pr_vendor | url_for_vendor
                          if use_link_vendor
                            assign pr_vendor_handle = pr_vendor | handle
                            assign collection_vendor = collections[pr_vendor_handle]
                            assign pr_vendor_url = collection_vendor.url | default: pr_vendor_url
                          endif -%}
                          
                          <div class="bee-pr-vendor bee-d-inline-flex bee-align-items-center bee-justify-content-center bee-text-center{% if image != blank %} has-img__vendor{% endif %}"><a href="{{ pr_vendor_url }}">
                              {%- if image != blank -%}
                               <img width="{{ image.width }}" height="{{ image.height }}" src="data: image/svg+xml,%3Csvg%20viewBox%3D%220%200%20{{ image.width }}%20{{ image.height }}%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3C%2Fsvg%3E" data-src="{{ image | image_url: width: 1 }}" data-widths="[60, 90, 180, 320, 640]" data-sizes="auto" class="bee-w-100 lazyloadbee bee-lz--fadeIn" alt="{{ pr_vendor | escape }}"><span class="lazyloadbee-loader"></span><span class="visually-hidden">{{ pr_vendor | escape }}</span>
                              {%- else -%}
                               {{ pr_vendor | escape }}
                              {%- endif -%}
                           </a></div>
          
                      {%- when 'img' -%}
                          {%- capture html_trust -%}
                                  {%- assign image = bk_stts.image -%}
                                  {%- if bk_stts.source == '1' and image != blank -%}
                                      <img style="--max-w-img: {{ bk_stts.wimg }}%" width="{{ image.width }}" height="{{ image.height }}" class="bee-img-tr__img lazyloadbee" src="data: image/svg+xml,%3Csvg%20viewBox%3D%220%200%20{{ image.width }}%20{{ image.height }}%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3C%2Fsvg%3E" data-src="{{ image | image_url: width: 1 }}" data-widths="[90, 120, 150, 180, 360, 480, 600, 750, 940]" data-sizes="auto" alt="{{ image.alt }}">
                                  {%- else bk_stts.svg -%}
                                      {%- assign isStyleSvg = true -%}
                                      {%- assign svg_height = bk_stts.height -%}
                                      {%- assign svg_width = svg_height | times: 1.57446809 | ceil -%}
                                      {%- assign arr = bk_stts.svg | remove: ' ' | split: "," -%}
                                      {%- for img in arr -%}<img width="{{ svg_width }}" height="{{ svg_height }}" class="bee-img-tr__svg lazyloadbee" src="https://cdn.shopify.com/s/assets/payment_icons/generic-dfdcaf09b6731ca14dd7441354c0ad8bc934184eb15ae1fda6a6b9e307675485.svg" data-src="{{ img | payment_type_img_url }}" alt="{{ img | replace: '_', ' ' }}" />{%- endfor -%}
                                  {%- endif -%}
                          {%- endcapture -%}
                          <div id="bee-trust_seal{{ bk_id }}" data-rr="{{ svg_width }}" {% if isStyleSvg %} style="--height-img:{{ svg_height }}px;"{% endif %} class="bee-pr_trust_seal bee-pr_trust-border__{{ bk_stts.enable_border }} bee-text-md-{{ bk_stts.al }} bee-text-center bee-pr" {{ block.shopify_attributes }}>
                          {%- if bk_stts.mess != blank -%}<p class="bee-pr-mess_trust bee-ch" style="--fs-mess-trust:{{ bk_stts.size }}px;">{{ bk_stts.mess }}</p>
                          {%- else -%}<style>.bee-pr_trust_seal.bee-pr_trust-border__true{padding: 20px;}</style>
                          {%- endif -%}
                          {{- html_trust -}}
                          </div>
                      {%- when 'live_view' %}{% if bk_stts.text == blank %}{% continue %}{% endif -%}
                          <div id="bee-counter{{ bk_id }}" class="bee-pr_live_view bee-dn bee-ch bee-fwm" data-live-view='{ "min": {{ bk_stts.min }}, "max": {{ bk_stts.max }}, "interval": {{ bk_stts.time }}000 }' {{ block.shopify_attributes }}>
                              {%- if bk_stts.icon != '1' -%}{%- assign image = bk_stts.img -%}
                                  {%- if bk_stts.icon == 'default' -%}
                                      <svg width="18px" height="18px" viewBox="0 0 18 18"><g><path d="M9,16c-5.56,0-8.67-6.3-8.8-6.56L-0.01,9L0.2,8.56C0.33,8.3,3.44,2,9,2s8.67,6.3,8.8,6.56L18.01,9L17.8,9.44 C17.67,9.7,14.56,16,9,16z M2.24,9c0.74,1.29,3.19,5,6.76,5c3.59,0,6.02-3.71,6.76-5C15.02,7.71,12.57,4,9,4C5.41,4,2.98,7.71,2.24,9z"/></g><g><path d="M9,12c-1.65,0-3-1.35-3-3s1.35-3,3-3s3,1.35,3,3S10.65,12,9,12z M9,8C8.45,8,8,8.45,8,9s0.45,1,1,1s1-0.45,1-1S9.55,8,9,8z"/></g> </svg>
                                  {%- elsif bk_stts.icon == '3' and image != blank -%}
                                      <img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image | image_url: width: 50 }}" width="{{ image.width }}" height="{{ image.height }} alt="{{ image.alt }}">
                                  {%- else -%}
                                      <i class="bee-ani-{{ bk_stts.ani }} {{ bk_stts.icon_name }}"></i>
                                  {%- endif -%}
                                  {%- if bk_stts.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}
                              {%- endif -%}
                              {{ bk_stts.text | replace: '[count]', '<span data-count class="bee-pr__live-view bee-fwb"></span>' }}
                          </div>
                      {%- when 'sold' %}{% if bk_stts.text == blank or isProductAvailable == false %}{% continue %}{% endif -%}
                          <div id="bee-sold{{ bk_id }}" class="bee-pr_flash_sold bee-ch bee-fwm bee-dn" data-time="120000" data-flash-sold='{ "mins": {{ bk_stts.mins }}, "maxs": {{ bk_stts.maxs }}, "mint": {{ bk_stts.mint }}, "maxt": {{ bk_stts.maxt }}, "id": "{{ product.id }}", "time": 120000 }' {{ block.shopify_attributes }}>
                              {%- if bk_stts.icon != '1' -%}{%- assign image = bk_stts.img -%}
                                  {%- if bk_stts.icon == 'default' -%}
                                      <svg width="22px" height="22px" viewBox="0 0 22 22"><path d="M11,22c-4.36,0-9-3.5-9-10l0-0.09c0.1-1.11,0.57-4.8,2.31-5.77c0.6-0.33,1.28-0.33,1.93-0.01c0.59,0.3,1.4,0.58,1.88,0.25 c0.85-0.59,1.02-2.93,0.88-4.33L8.79,0l1.75,1.11C10.88,1.33,19,6.54,19,12.03C19,19.38,14.87,22,11,22z M4,12.05 C4.02,17.84,8.3,20,11,20c3.76,0,6-2.98,6-7.97c0-2.93-3.51-6.25-5.98-8.13c-0.1,1.44-0.49,3.26-1.75,4.13 C8.24,8.74,6.92,8.7,5.35,7.92C4.87,8.05,4.2,9.94,4,12.05z M10.06,17.09c-0.3,0-0.63-0.07-0.92-0.32 c-0.48-0.42-0.55-1.04-0.55-1.66c0-1.16,0.65-5.27,2.71-5.27S14,13.94,14,15.1c0,0.69-0.2,1.22-0.59,1.57c-0.59,0.53-1.35,0.44-1.9,0.37c-0.34-0.04-0.7-0.03-0.95,0C10.41,17.07,10.24,17.09,10.06,17.09z"/></svg>
                                  {%- elsif bk_stts.icon == '3' and image != blank -%}
                                      <img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image | image_url: width: 50 }}" width="{{ image.width }}" height="{{ image.height }} alt="{{ image.alt }}">
                                  {%- else -%} 
                                      <i class="bee-ani-{{ bk_stts.ani }} {{ bk_stts.icon_name }}"></i>
                                  {%- endif -%}
                                  {%- if bk_stts.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}
                              {%- endif -%}
                              {{ bk_stts.text | replace: '[sold]', '<span data-sold class="bee-pr__sold bee-fwb"></span>' | replace: '[hour]', '<span data-hour class="bee-pr__hrs bee-fwb"></span>' }}
                          </div>
          
                      {%- when 'order' -%}{% unless isProductAvailable %}{% continue %}{% endunless -%}{% if isPreoder and variants_size == 1 and bk_stts.hide_pre %}{% continue %}{% endif -%}
                          <div id="bee-delivery{{ bk_id }}" class="bee-pr_delivery bee-dn" data-order-delivery='{ "timezone": false, "format_day": "{{ bk_stts.frm_day }}", "mode": "{{ bk_stts.mode }}", "cut_day": {{ bk_stts.cut_day | json }}, "estimateStartDate": {{ meta_theme.estimateStartDate | default: bk_stts.ds | json }}, "estimateEndDate": {{ meta_theme.estimateEndDate | default: bk_stts.de | json }}, "time": {{ bk_stts.time | default: 19041994 | json }}, "hideWithPreorder": {{ bk_stts.hide_pre }} }' {{ block.shopify_attributes }}>
                              <div class="bee-pr_delivery_wrap bee-d-flex bee-align-items-center">
                                  <div class="bee-pr_delivery_icon">
                                      {%- if bk_stts.icon != '1' -%}{%- assign image = bk_stts.img -%}
                                          {%- if bk_stts.icon == 'default' -%}
                                          <svg width="18px" height="18px" viewBox="0 0 18 18"><g><path d="M18,5c0-0.15-0.03-0.31-0.1-0.45l-2-4C15.73,0.21,15.38,0,15,0H3C2.62,0,2.27,0.21,2.11,0.55l-2,4C0.04,4.69,0,4.85,0,5h0
                                            v12c0,0.55,0.45,1,1,1h16c0.55,0,1-0.45,1-1L18,5L18,5z M3.62,2h10.76l1,2H2.62L3.62,2z M2,16V6h14v10H2z"/><path d="M9,9C7.9,9,7,8.1,7,7H5c0,2.21,1.79,4,4,4s4-1.79,4-4h-2C11,8.1,10.1,9,9,9z"/></g></svg>
                                          {%- elsif bk_stts.icon == '3' and image != blank -%}<img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image | image_url: width: 50 }}" width="{{ image.width }}" height="{{ image.height }} alt="{{ image.alt }}">
                                          {%- else -%}<i class="bee-ani-{{ bk_stts.ani }} {{ bk_stts.icon_name }}"></i>
                                          {%- endif -%}
                                          {%- if bk_stts.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}
                                      {%- endif -%}
                                  </div>
                                  <div class="bee-pr_delivery_content">
                                      {%- capture hourHTML -%}<span data-hour-delivery class="bee-h_delivery bee-dn">[totalHours] {{ bk_stts.hr }} %M {{ bk_stts.min }}</span>{%- endcapture -%}
                                      {{ bk_stts.txt | replace: '[hour]', hourHTML | replace: '[date_start]', '<span data-start-delivery class="bee-start_delivery bee-ch bee-fwm"></span>' | replace: '[date_end]', '<span data-end-delivery class="bee-end_delivery bee-ch bee-fwm"></span>' }}
                                  </div>
                              </div>
                          </div>
          
                      {%- when 'countdown' %}{% unless bk_stts.source == '1' or pr_tags contains 'has_stock_countdown' %}{% continue %}{% endunless -%}
                          {{ 'bee-countdown.css' | asset_url | stylesheet_tag }}
                          {%- liquid 
                              assign meta = meta_theme.countdown
                              assign isCountdownMeta = false
                              assign isShowCountdownt = false 
                              if meta != blank 
                                  assign cd_date = meta | date: '%Y/%m/%d %H:%M:%S'
                                  assign isCountdownMeta = true
                              else
                                  assign cd_date = meta_theme.countdown_day | default: bk_stts.stock_time 
                              endif
                              if bk_stts.source == '1' and cd_date != blank
                                  assign isShowCountdownt = true 
                              elsif bk_stts.source == '2' and cd_date != blank and pr_tags contains 'has_stock_countdown'
                                  assign isShowCountdownt = true 
                              endif
                          -%}
                          {%- unless isShowCountdownt %}{% continue %}{% endunless -%}
                          <div data-countdown-pr data-countdown-wrap id="bee-countdown-wrap{{ bk_id }}" class="bee-countdown-pr bee-text-{{ bk_stts.al }} bee-dn" {{ block.shopify_attributes }}>
                              {%- if bk_stts.mess != blank -%}
                                  <p class="bee-countdown__mess bee-lh-1 bee-fwm" style="font-size: {{ bk_stts.size }}px;margin-bottom: 20px;color: {{ bk_stts.color }};">
                                  {%- if bk_stts.icon != '1' -%}{%- assign image = bk_stts.img -%}
                                  {%- if bk_stts.icon == '3' and image != blank -%}<img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image | image_url: width: 50 }}" width="{{ image.width }}" height="{{ image.height }} alt="{{ image.alt }}">
                                  {%- else -%}<i class="bee-ani-{{ bk_stts.ani }} {{ bk_stts.icon_name }}"></i>
                                  {%- endif -%}
                                  {%- if bk_stts.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}{%- endif -%}{{ bk_stts.mess }}</p>
                              {%- endif -%}
                              <div id="bee-countdow{{ bk_id }}" class="bee-countdown-wrap bee-d-inline-flex bee-justify-content-center bee-justify-content-between bee-lh-1 bee-countdown sepr_coun_dt_wrap bee-countdown-des bee-countdown-size-{{ bk_stts.cdt_size }} "{% if isCountdownMeta %} data-date="{{ cd_date }}" data-loop="{% if bk_stts.dayx > 0 %}true{% else %}false{% endif %}" data-dayl="{{ bk_stts.dayx }}"{% endif %} data-cd-options='{ "isCountdownMeta": {{ isCountdownMeta }},"cd_date": {{ cd_date | json }} }' style="--number-cl:{{ bk_stts.number_cl }};--text-cl:{{ bk_stts.text_cl }};--bg-cl:{{ bk_stts.bg_cl }};--space-item:{{ bk_stts.space_item }}px;--space-item-tb:{{ bk_stts.space_item_tb }}px;--space-item-mb:{{ bk_stts.space_item_mb }}px;">
                                  <div class="bee-time">
                                      <span class="bee-cd__time bee-d-flex bee-text-center countdown-days"><span class="bee-cd__count cd-number">%-D</span><span class="bee-cd__label cd-text">%!D:{{ 'products.product_single.countdown_text.day' | t }},{{ 'products.product_single.countdown_text.day_plural' | t }};</span></span>
                                      <span class="bee-cd__time bee-d-flex bee-text-center countdown-hours"><span class="bee-cd__count cd-number">%-H</span><span class="bee-cd__label cd-text bee_cl_f8b0a4">%!H:{{ 'products.product_single.countdown_text.hr' | t }},{{ 'products.product_single.countdown_text.hr_plural' | t }};</span></span>
                                      <span class="bee-cd__time bee-d-flex bee-text-center countdown-min"><span class="bee-cd__count cd-number">%-M</span><span class="bee-cd__label cd-text bee_cl_f8b0a4">%!M:{{ 'products.product_single.countdown_text.min' | t }},{{ 'products.product_single.countdown_text.min_plural' | t }};</span></span>
                                      <span class="bee-cd__time bee-d-flex bee-text-center countdown-sec"><span class="bee-cd__count cd-number">%-S</span><span class="bee-cd__label cd-text bee_cl_f8b0a4">%!S:{{ 'products.product_single.countdown_text.sec' | t }},{{ 'products.product_single.countdown_text.sec_plural' | t }};</span></span>
                                  </div>
                              </div>
                          </div> 
                      {%- when 'inventory_qty' %}{% if bk_stts.mess == blank or isProductAvailable == false %}{% continue %}{% endif -%}
                          {%- assign arr_mess = bk_stts.mess | split: '[stock_number]' -%}
                          <div id="bee-stock{{ bk_id }}" class="bee-inventory_qty bee-text-{{ bk_stts.al }}" data-inventory-qty='{ "reduce": {{ bk_stts.reduce | default: false }}, "inventoryQty": {{ inventory_quantity | default: 0 }}, "id": {{ current_variant.id | default: product.id }}, "stock": "{{ bk_stts.stock }}", "qty": {{ bk_stts.qty }}, "total": {{ bk_stts.total_items }}, "min": {{ bk_stts.stock_from }}, "max": {{ bk_stts.stock_to }}, "bgprocess": "{{ bk_stts.stock_bg_process }}", "bgten": "{{ bk_stts.bgten }}" }' data-prid="{{ product.id }}" {{ block.shopify_attributes }}>
                              <p data-message class="bee-inventory_message bee-dn bee-ch bee-lh-1 bee-fwm" style="font-size: {{ bk_stts.size }}px">
                              {%- if bk_stts.icon != '1' -%}{%- assign image = bk_stts.img -%}
                                  {%- if bk_stts.icon == 'default' -%}
                                      <svg width="18px" height="18px" viewBox="0 0 18 18"><g><path d="M9.98,3.08C9.99,3.05,10,3.03,10,3V2h1c0.55,0,1-0.45,1-1s-0.45-1-1-1H7C6.45,0,6,0.45,6,1s0.45,1,1,1h1v1 c0,0.03,0.01,0.05,0.02,0.08C4.62,3.56,2,6.48,2,10c0,3.86,3.14,7,7,7s7-3.14,7-7C16,6.48,13.38,3.56,9.98,3.08z M9,15 c-2.76,0-5-2.24-5-5s2.24-5,5-5s5,2.24,5,5S11.76,15,9,15z"/><path d="M11.45,10.11L10,9.38V7c0-0.55-0.45-1-1-1S8,6.45,8,7v3c0,0.38,0.21,0.72,0.55,0.89l2,1C10.7,11.97,10.85,12,11,12 c0.37,0,0.72-0.2,0.9-0.55C12.14,10.95,11.94,10.35,11.45,10.11z"/></g></svg>
                                  {%- elsif bk_stts.icon == '3' and image != blank -%}<img class="lazyloadbee bee-w-100 bee-ani-{{ bk_stts.ani }}" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ image | image_url: width: 50 }}" width="{{ image.width }}" height="{{ image.height }} alt="{{ image.alt }}">
                                  {%- else -%}
                                      <i class="bee-ani-{{ bk_stts.ani }} {{ bk_stts.icon_name }}"></i>
                                  {%- endif -%}
                              {%- if bk_stts.ani != 'none' %}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{% endif -%}
                              {%- endif -%}
                              {{ arr_mess[0] }} <span data-count class='bee-count'></span> {{ arr_mess[1] }}</p>
                              {%- if bk_stts.progress -%}
                              <div data-progressbar class="bee-inventory_progressbar bee-progress__bar bee-pr bee-oh bee-dn" style="background-color: {{ bk_stts.stock_bg }};width: {{ bk_stts.wbar }}%"><div style="background-color: {{ bk_stts.stock_bg_process }};width: 100%;"></div></div>
                              {%- endif -%}
                          </div>
          
                      {%- when 'store_pickup' %}{% comment %}{% if isProductAvailable == false %}{% continue %}{% endif -%}{% endcomment -%}
          
                      {{ 'bee-pickup-availability.css' | asset_url | stylesheet_tag }}
                      <div class="bee-pr__pickup-availability-container bee-dn-" data-variant-id="{{ current_variant.id }}" data-pickup-availability-container data-has-only-default-variant="{{ isProductDefault }}" data-root-url="{{ routes.root_url }}" data-id-popup="popup{{ pr_sid }}"></div>
          
                      {%- when 'tab_des' or 'tab_add' or 'tab_rivui' or 'tab_liquid' or 'tab_html' -%}
                          {%- if tabCheck or tabs_position == 'external' %}{% continue %}{% endif -%}
                          {%- assign tabCheck = true -%}
                          {%- render 'product_tabs', product: product, se_stts: se_stts, tabs_design: 'accordion', seBlocks: seBlocks, idTabDes: idTabDes, idTabReview: idTabReview, isProductDefault: isProductDefault -%}
                      {%- when 'break' -%}
                      {%- if se_stts.media_size == "full" -%}
                          </div>
                          <div class="bee-product__info-container">
                              {%- style -%}
                                  @media (min-width: 1025px){
                                      .bee-product-media__slider_center .bee-product__info-full{max-width:1215px;margin: 0 auto;}
                                      .bee-product__info-wrapper.bee-product__info-full .bee-product__info-main {
                                          display: flex;
                                          margin: 0 -{{ bk_stts.space_block_tb | divided_by: 2 }}px;
                                          max-width: 100%;
                                      }
                                      .bee-product__info-wrapper.bee-product__info-full .bee-product__info-main .bee-product__info-container{
                                          padding: 0 {{ bk_stts.space_block_tb | divided_by: 2 }}px;
                                          flex: 1 0 0;
                                          width: 50%;
                                      }
                                      .bee-product__info-wrapper.bee-product__info-full .bee-product__info-main {
                                          margin: 0 -{{ bk_stts.space_block | divided_by: 2 }}px;
                                      }
                                      .bee-product__info-wrapper.bee-product__info-full .bee-product__info-main .bee-product__info-container{
                                          padding: 0 {{ bk_stts.space_block | divided_by: 2 }}px;
                                      }
                                  }
                                  @media(max-width:767px){
                                      .bee-product__info-wrapper.bee-product__info-full .bee-product__info-main .bee-product__info-container:first-child{margin-bottom: 20px;}
                                  }
                              {%- endstyle -%}
                          {%- endif -%}
                      {%- when 'html' %}{% if bk_stts.page.content == blank %}{% continue %}{% endif -%}
                          <div class="bee-custom_{{ bk_id }} bee-pr__html bee-rte" {{ block.shopify_attributes }}>{{ bk_stts.page.content }}</div>
          
                          {%- when 'text' -%}{% if bk_stts.text == blank %}{% continue %}{% endif -%}
                          <div class="bee-richtext_{{ bk_id }} bee-pr__richtext" {{ block.shopify_attributes }}>{{ bk_stts.text }}</div>
          
                      {%- when 'custom_liquid' -%}
                      <div class="bee-liquid_{{ bk_id }} bee-pr__custom-liquid" {{ block.shopify_attributes }}>{{ bk_stts.custom_liquid }}</div>
          
                      {%- when '@app' -%}{%- render block -%}
        
        
                    {%- endcase -%}
                {%- endfor -%}
              </div>
            </div>
          </div>
        </div> 
      </div>
      <aside data-sidebar-content class="bee-col-item bee-col-12 bee-col-lg-3 bee-sidebar bee-dn"><div class="bee-loading--bg"></div></aside>
    </div>
    </div>
    {%- render 'FBT' -%}
    
    {%- if tabs_position == 'external' %}{% render 'product_tabs', product: product, se_stts: se_stts, tabs_design: se_stts.tabs_design, seBlocks: seBlocks, idTabDes: idTabDes, idTabReview: idTabReview, isProductDefault: isProductDefault %}{% endif -%}
    
    <template data-icons-thumb>
      <svg class="bee-d-none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
        <symbol id="icon-thumb-video" aria-hidden="true" focusable="false" role="presentation" fill="currentColor" viewBox="34.88 0 442.23 512">
          <path d="M443.86,196.919L141.46,10.514C119.582-2.955,93.131-3.515,70.702,9.016c-22.429,12.529-35.819,35.35-35.819,61.041 v371.112c0,38.846,31.3,70.619,69.77,70.829c0.105,0,0.21,0.001,0.313,0.001c12.022-0.001,24.55-3.769,36.251-10.909 c9.413-5.743,12.388-18.029,6.645-27.441c-5.743-9.414-18.031-12.388-27.441-6.645c-5.473,3.338-10.818,5.065-15.553,5.064 c-14.515-0.079-30.056-12.513-30.056-30.898V70.058c0-11.021,5.744-20.808,15.364-26.183c9.621-5.375,20.966-5.135,30.339,0.636 l302.401,186.405c9.089,5.596,14.29,14.927,14.268,25.601c-0.022,10.673-5.261,19.983-14.4,25.56L204.147,415.945 c-9.404,5.758-12.36,18.049-6.602,27.452c5.757,9.404,18.048,12.36,27.452,6.602l218.611-133.852 c20.931-12.769,33.457-35.029,33.507-59.55C477.165,232.079,464.729,209.767,443.86,196.919z" fill=" currentColor"/>
        </symbol>
        <symbol id="icon-external-youtube" fill="currentColor" viewBox="0 0 576 512">
          <path d="M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z"/>
        </symbol>
        <symbol id="icon-external-vimeo" fill="currentColor" viewBox="0 0 448 512">
          <path d="M403.2 32H44.8C20.1 32 0 52.1 0 76.8v358.4C0 459.9 20.1 480 44.8 480h358.4c24.7 0 44.8-20.1 44.8-44.8V76.8c0-24.7-20.1-44.8-44.8-44.8zM377 180.8c-1.4 31.5-23.4 74.7-66 129.4-44 57.2-81.3 85.8-111.7 85.8-18.9 0-34.8-17.4-47.9-52.3-25.5-93.3-36.4-148-57.4-148-2.4 0-10.9 5.1-25.4 15.2l-15.2-19.6c37.3-32.8 72.9-69.2 95.2-71.2 25.2-2.4 40.7 14.8 46.5 51.7 20.7 131.2 29.9 151 67.6 91.6 13.5-21.4 20.8-37.7 21.8-48.9 3.5-33.2-25.9-30.9-45.8-22.4 15.9-52.1 46.3-77.4 91.2-76 33.3.9 49 22.5 47.1 64.7z"/>
        </symbol>
        <symbol id="icon-thumb-model" fill="currentColor" aria-hidden="true" focusable="false" role="presentation" viewBox="0 0 18 21">
          <path d="M7.67998 20.629L1.28002 16.723C0.886205 16.4784 0.561675 16.1368 0.337572 15.731C0.113468 15.3251 -0.00274623 14.8686 -1.39464e-05 14.405V6.59497C-0.00238367 6.13167 0.113819 5.6755 0.33751 5.26978C0.561202 4.86405 0.884959 4.52227 1.278 4.27698L7.67796 0.377014C8.07524 0.131403 8.53292 0.000877102 8.99999 9.73346e-08C9.46678 -0.000129605 9.92446 0.129369 10.322 0.374024V0.374024L16.722 4.27399C17.1163 4.51985 17.4409 4.86287 17.6647 5.27014C17.8885 5.67742 18.0039 6.13529 18 6.59998V14.409C18.0026 14.8725 17.8864 15.3289 17.6625 15.7347C17.4386 16.1405 17.1145 16.4821 16.721 16.727L10.321 20.633C9.92264 20.8742 9.46565 21.0012 8.99999 21C8.53428 20.9998 8.07761 20.8714 7.67998 20.629V20.629ZM8.72398 2.078L2.32396 5.97803C2.22303 6.04453 2.14066 6.13551 2.08452 6.24255C2.02838 6.34959 2.00031 6.46919 2.00298 6.59003V14.4C2.00026 14.5205 2.02818 14.6396 2.08415 14.7463C2.14013 14.853 2.22233 14.9438 2.32298 15.01L7.99999 18.48V10.919C8.00113 10.5997 8.08851 10.2867 8.25292 10.0129C8.41732 9.73922 8.65267 9.51501 8.93401 9.36401L15.446 5.841L9.28001 2.08002C9.19614 2.02738 9.09901 1.99962 8.99999 2C8.90251 1.99972 8.8069 2.02674 8.72398 2.078V2.078Z"></path>
        </symbol>
       <symbol id="icon-thumb-360" fill="currentColor" aria-hidden="true" focusable="false" role="presentation" viewBox="0 101.9 442.17 238.37">
        <path d="M355.082,152.335c13.926,0,25.216-11.29,25.216-25.216c0-13.926-11.29-25.216-25.216-25.216s-25.216,11.29-25.216,25.216 C329.882,141.038,341.162,152.318,355.082,152.335z M355.082,119.616c4.138,0,7.493,3.355,7.493,7.493 c0,4.138-3.355,7.493-7.493,7.493s-7.493-3.355-7.493-7.493C347.594,122.973,350.946,119.621,355.082,119.616z" fill=" currentColor" />      <path d="M380.18,162.929c-4.373-2.197-9.699-0.433-11.897,3.94c-2.197,4.373-0.433,9.699,3.94,11.897 c0.085,0.043,0.17,0.084,0.257,0.124c33.477,16.167,51.988,36.431,51.988,57.108c0,21.474-20.756,43.126-56.93,59.402 c-38.961,17.526-90.969,27.175-146.432,27.175s-107.461-9.649-146.432-27.175c-36.185-16.266-56.93-37.918-56.93-59.402 c0-20.677,18.462-40.921,51.988-57.108c4.442-2.055,6.376-7.322,4.321-11.764c-2.055-4.442-7.322-6.376-11.764-4.321 c-0.086,0.04-0.172,0.081-0.257,0.124C22.036,182.208,0,208.153,0,235.978c0,58.486,97.113,104.29,221.086,104.29 s221.086-45.814,221.086-104.29C442.201,208.153,420.175,182.208,380.18,162.929z" fill=" currentColor" />       <path d="M96.414,259.175c7.371,6.768,17.372,10.151,30.001,10.151c12.032,0,21.786-3.282,29.263-9.846 c7.477-6.564,11.211-15.097,11.205-25.6c0-7.221-1.874-13.177-5.622-17.871c-3.748-4.693-8.999-7.647-15.754-8.862 c5.573-1.726,9.813-4.5,12.721-8.32c2.975-4.028,4.511-8.937,4.362-13.942c0-8.579-3.17-15.373-9.511-20.382 s-15.032-7.483-26.073-7.424c-12.38,0-22.009,2.8-28.889,8.399c-6.879,5.599-10.417,13.44-10.614,23.522h26.191 c0.098-3.702,1.185-6.564,3.259-8.586c2.074-2.022,4.985-3.033,8.734-3.033c3.167-0.12,6.256,0.995,8.615,3.111 c2.243,2.069,3.473,5.014,3.367,8.064c0,4.142-1.333,7.299-3.997,9.472c-2.665,2.173-6.489,3.259-11.471,3.259h-1.516v17.388 c0.394,0,0.827,0,1.29,0s1.142,0,2.038,0c5.52,0,9.577,1.172,12.17,3.515s3.886,6.006,3.879,10.988 c0,4.437-1.208,7.828-3.623,10.171c-2.416,2.343-5.891,3.515-10.427,3.515c-4.339,0-7.621-1.221-9.846-3.663 c-2.225-2.442-3.344-6.055-3.358-10.841v-1.772H85.356v1.034C85.356,243.212,89.042,252.395,96.414,259.175z" fill=" currentColor" />       <path d="M221.558,269.327c12.623,0,22.695-3.587,30.218-10.762c7.522-7.175,11.284-16.715,11.284-28.623 c0-9.912-3.01-18.087-9.029-24.527c-6.019-6.439-13.614-9.659-22.784-9.659c-1.976-0.006-3.951,0.129-5.908,0.404 c-1.832,0.261-3.639,0.67-5.406,1.221l25.305-38.174H215.05l-15.173,23.473c-7.943,12.432-13.158,21.901-15.646,28.406 c-2.401,6.055-3.666,12.5-3.732,19.013c0,11.986,3.699,21.53,11.097,28.633C198.994,265.835,208.981,269.366,221.558,269.327z M209.3,219.111c2.934-2.987,6.919-4.48,11.953-4.48s9.022,1.493,11.963,4.48c2.934,2.987,4.401,7.043,4.401,12.17 c0,5.127-1.467,9.147-4.401,12.062c-2.934,2.96-6.919,4.441-11.953,4.441s-9.022-1.467-11.963-4.401 c-2.934-2.96-4.401-6.981-4.401-12.062C204.899,226.24,206.366,222.17,209.3,219.111z" fill=" currentColor" />       <path d="M315.963,269.327c12.728,0,22.728-4.923,30.001-14.769c7.273-9.846,10.91-23.303,10.91-40.369 c0-18.097-3.587-32.141-10.762-42.132c-7.175-9.991-17.224-14.983-30.149-14.976c-12.728,0-22.751,4.982-30.07,14.946 s-10.982,23.677-10.988,41.137c0,17.513,3.663,31.262,10.988,41.246C293.219,264.394,303.242,269.366,315.963,269.327z M305.457,187.092c2.173-5.402,5.675-8.103,10.506-8.103c4.687,0,8.126,2.763,10.319,8.29c2.192,5.527,3.289,14.133,3.289,25.817 c0,11.815-1.096,20.447-3.289,25.895c-2.192,5.448-5.632,8.185-10.319,8.212c-4.785,0-8.274-2.724-10.466-8.172 c-2.192-5.448-3.289-14.093-3.289-25.935c-0.007-11.914,1.077-20.572,3.249-25.974L305.457,187.092z" fill=" currentColor"/>
       </symbol>
    </svg>
</template>
{%- if isSticky and product != blank -%}

    {%- liquid 
        assign current_inventory_quantity = current_variant.inventory_quantity
        if current_variant.inventory_management != null and current_inventory_quantity > 0 and current_variant.inventory_policy != 'continue'
            assign max_qty = current_inventory_quantity
        else
            assign max_qty = 9999
        endif -%}
        
    <link href="{{ 'bee-sticky-atc.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">
    {%- if se_stts.ani != 'none' -%}<link href="{{ 'ani-atc.min.css' | asset_url }}" rel="stylesheet" media="print" onload="this.media='all'">{%- endif -%}
    <template class="bee-d-none" id="bee-sticky-atc-temp">
        <div data-sticky-addtocart class="bee-sticky-atc sticky_layout_mb--{{ se_stts.sticky_layout_mb }} bee-pf bee-b-0 bee-l-0 bee-r-0 bee-op-0 bee-pe-none"{% if PR_no_pick %} hidden{% endif %}>
            {%- if request.design_mode %}<link href="{{ 'bee-sticky-atc.css' | asset_url }}" rel="stylesheet" media="all">{% endif -%}
            <div class="bee-sticky-atc__product">

            {%- liquid
            assign image = current_variant.featured_image | default: product.featured_image
            assign img_url = image | image_url: width: 1 -%}
            {% if img_url != blank %}
                <div data-sticky-img class="bee-sticky-atc__img bee-pr bee-oh bee_ratio bee-bg-11" style="background: url({{ img_url }})">
                <img class="lazyloadbee bee-lz--fadeIn" data-orginal="{{ img_url }}" data-src="{{ img_url }}" data-widths="[65,120]" data-optimumx="1.5" data-sizes="auto" src="{% render 'img_svg', w: image.width, h: image.height %}" width="{{ image.width }}" height="{{ image.height }}" alt="{{ image.alt | escape }}">
                </div>
            {% endif %}
            <div class="bee-sticky-atc__infos">
                <div class="bee-sticky-atc__title">{{ product.title }}</div>
                <div data-sticky-price class="bee-sticky-atc__price">{% if current_variant.compare_at_price > current_variant.price %}<del>{{ current_variant.compare_at_price | money }}</del> <ins>{{ current_variant.price | money }}</ins>{% else %}{{ current_variant.price | money }}{% endif %}</div>
            </div>

            {%- if variants_size > 1 and se_stts.sticky_layout_mb == 'minimal' %}<button type="button" class="bee-sticky-close" data-action-info-close aria-label="Close"><svg role="presentation" class="bee-iconsvg-close" viewBox="0 0 16 14"><path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path></svg></button>{% endif -%}
       
            <div {% if variants_size > 1 %}data-sticky-v-title{% endif %} class="bee-sticky-atc__v-title">
            {%- unless isProductDefault -%}
             {%- if se_stts.enable_select -%}
                <link rel="stylesheet" href="{{ 'bee-base_drop.min.css' | asset_url }}" media="all">
                <div class="bee-dropdown bee-dropdown__sortby">
                  <button type="button" data-dropdown-open data-position="bottom-end" data-id="bee__dropdown-{{ pr_sid }}" class="bee-truncate"><span>{{ current_variant.title | escape }}</span><svg class="bee-icon-select-arrow" role="presentation" viewBox="0 0 19 12"><use xlink:href="#bee-select-caret"></use></svg></button>
                  <div data-dropdown-wrapper class="bee-dropdown__wrapper bee-current-scrollbar" id="bee__dropdown-{{ pr_sid }}">
                     <div class="bee-drop-arrow"></div>
                     <div class="bee-dropdown__header">
                        <span class="bee-dropdown__title"></span><button data-dropdown-close="" aria-label="Close"><svg role="presentation" class="bee-iconsvg-close" viewBox="0 0 16 14"><path d="M15 0L1 14m14 0L1 0" stroke="currentColor" fill="none" fill-rule="evenodd"></path></svg></button>
                     </div>
                     <div data-sticky-select class="bee-dropdown__content bee-current-scrollbar">
                      {%- for variant in pr_variants -%}
                        {%- if variant.available -%}
                        <button data-dropdown-item data-value="{{ variant.id }}"{% if variant.id == current_variant.id %} class="is--selected"{% endif %}>{{ variant.title | escape }}</button>
                        {%- else -%}
                        <button data-dropdown-item data-value="{{ variant.id }}" disabled="disabled"{% if variant.id == current_variant.id %} class="is--selected"{% endif %}>{{ variant.title | escape }} - {{ 'products.product.sold_out' | t | escape }}</button>
                        {%- endif -%}
                      {%- endfor -%}
                    </div>
                  </div>
              </div>
             {%- else -%}
                {{ current_variant.title }}
             {%- endif -%}
            {%- endunless -%}
           </div>
            </div>
            <div class="bee-sticky-atc__btns">
                <div data-quantity-wrapper class="bee-quantity-wrapper bee-sticky-atc__qty"> 
                <button data-quantity-selector data-decrease-qty type="button" class="bee-quantity-selector is--minus"><svg focusable="false" class="icon icon--minus" viewBox="0 0 10 2" role="presentation"><path d="M10 0v2H0V0z" fill="currentColor"></path></svg></button>
                <input data-quantity-value type="number" class="bee-quantity-input" step="1" min="{{cus_qty}}" max="{{max_qty}}" name="quantity" value="{{cus_qty}}" size="4" pattern="[0-9]*" inputmode="numeric">
                <button data-quantity-selector data-increase-qty type="button" class="bee-quantity-selector is--plus"><svg focusable="false" class="icon icon--plus" viewBox="0 0 10 10" role="presentation"><path d="M6 4h4v2H6v4H4V6H0V4h4V0h2v4z" fill="currentColor" fill-rule="evenodd"></path></svg></button>
                </div>
                <button {% if variants_size > 1 and se_stts.sticky_layout_mb == 'minimal' %} data-action-delay{% endif %} data-animation-atc='{ "ani": "{{ se_stts.ani }}","time": {{ se_stts.time }}000 }' type="button" data-action-atc data-variant-id="{{ current_variant.id }}" class="bee-sticky-atc__atc bee-btn-loading__svg"{% unless current_variant.available %} aria-disabled="true"{% endunless %}{% unless isProductAvailable %} disabled="disabled"{% endunless %}>
                <span class="bee-btn-atc_text">{% if current_variant.available == false or isProductAvailable == false %}{{ 'products.product.sold_out' | t }}{% elsif isPreoder %}{{ 'products.product.pre_order' | t }}{%- else -%}{{ 'products.product.add_to_cart' | t }}{%- endif -%}</span>
                <span class="bee-loading__spinner" hidden>
                    <svg width="16" height="16" hidden class="bee-svg-spinner" focusable="false" role="presentation" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg"><circle class="bee-path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle></svg>
                </span>
                </button>
            </div>
        </div>
    </template>
{%- endif -%}   
{%- liquid
  assign current_variant = product.selected_or_first_available_variant | default: product.variants.first
  if product.selected_or_first_available_variant.featured_media
    assign seo_media = product.selected_or_first_available_variant.featured_media
  else
    assign seo_media = product.featured_media
  endif
-%}
{%- unless product == empty -%}
  <script type="application/json" id="ModelJson-{{ sid }}">
    {{ product.media | where: 'media_type', 'model' | json }}
  </script>
{%- endunless -%}
<script type="application/ld+json">
{
    "@context": "http://schema.org/",
    "@type": "Product",
    "name": {{ product.title | json }},
    "url": "{{ request.origin }}{{ product.url }}",
    {%- if seo_media != blank -%}
    "image": "https:{{ seo_media | image_url: width: seo_media.preview_image.width }}",
    {%- endif -%}
    "description": {{ product.description | strip_html | escape | strip_newlines | json }},
    "sku": "{{ current_variant.sku | default: current_variant.id }}",
    "mpn": "{{ current_variant.barcode }}",
    "productID": "{{ product.id }}",
    "brand": {
    "@type": "Brand",
    "name": "{{ product.vendor | escape }}"
    },
    {%- if product.price_varies -%}
    "offers": [
    {%- capture not_change -%}
        "@type" :"Offer",
        "availability" :"http://schema.org/{% if product.available %}InStock{% else %}OutOfStock{% endif %}",
        "priceCurrency" :"{{ cart.currency.iso_code }}",
        "itemCondition": "https://schema.org/NewCondition",
        "priceValidUntil": "{{ 'now' | date: '%s' | plus: 31536000 | date: '%Y-%m-%d' | url_encode | replace: '+', '%20' }}",
    {%- endcapture -%}
    {%- assign shop_secure_url = shop.secure_url -%}
    {%- for variant in product.variants -%}{%- assign barcode_size = variant.barcode.size -%}
        {
        {{ not_change }}
        "sku": "{{ variant.sku | default: variant.id }}",
        "price" :"{{ variant.price | divided_by: 100.00 }}",
        "mpn": "{{ variant.barcode }}",
        {%- if barcode_size == 12 or barcode_size == 13 or barcode_size == 14 -%}"gtin{{ barcode_size }}": "{{ variant.barcode }}",{%- endif -%}
        "url" :"{{ request.origin }}{{ variant.url }}"
        }{% unless forloop.last %},{% endunless -%}
    {%- endfor -%}
    ]
    {%- else -%}
    "offers": {
        "@type" :"Offer",
        "sku": "{{ current_variant.sku | default: current_variant.id }}",
        "availability" :"http://schema.org/{% if product.available %}InStock{% else %}OutOfStock{% endif %}",
        "price" :"{{ product.price | divided_by: 100.00 }}",
        "priceCurrency" :"{{ cart.currency.iso_code }}",
        "itemCondition": "https://schema.org/NewCondition",
        "url" :"{{ request.origin }}{{ product.url }}",
        "mpn": "{{ current_variant.barcode }}",
        {%- assign barcode_size = current_variant.barcode.size -%}{%- if barcode_size == 12 or barcode_size == 13 or barcode_size == 14 -%}"gtin{{ barcode_size }}": "{{ variant.barcode }}",{%- endif -%}
        "priceValidUntil": "{{ 'now' | date: '%s' | plus: 31536000 | date: '%Y-%m-%d' | url_encode | replace: '+', '%20' }}"
    }
    {%- endif -%}
    {%- assign review_app = settings.app_review | default: '1' -%}
    {%- case review_app -%}
        {%- when '1' -%}
        ,"aggregateRating": {
          "@type": "AggregateRating",
          "description": "Shopify Product Reviews",
          "ratingCount": {{ product.metafields.reviews.rating.value.rating | default: 0 }},
          "ratingValue": {{ product.metafields.reviews.rating_count | default: 0}}
        }
        {%- when '2' -%}{%- assign aggregateRating = product.metafields.ryviu.product_reviews_info | split: ";" -%}{%- assign ratingCount = aggregateRating[0] -%}
        {%- if ratingCount != "0" and ratingCount != blank -%}
        ,"aggregateRating": {
        "@type": "AggregateRating",
        "ratingCount": {{ ratingCount | plus: 0 }},
        "ratingValue": {{ aggregateRating[1] | plus: 0 }}
        }
        {%- endif -%}
        {%- when '3' -%}{%- assign ratingCount = product.metafields.ali.reviews_count -%}
        {%- if ratingCount != "0" and ratingCount != blank -%}
        ,"aggregateRating": {
        "@type": "AggregateRating",
        "ratingCount": {{ ratingCount }},
        "ratingValue": {{ product.metafields.ali.reviews_average }}
        }
        {%- endif -%}
        {%- when '4' -%}{%- assign ratingCount = product.metafields.loox.num_reviews -%}
        {%- if ratingCount != "0" and ratingCount != blank -%}
        ,"aggregateRating": {
        "@type": "AggregateRating",
        "ratingCount": {{ ratingCount }},
        "ratingValue": {{ product.metafields.loox.avg_rating }}
        }
        {%- endif -%}
        {%- when 'stamped' -%}{%- assign ratingCount = product.metafields.stamped.reviews_count -%}
        {%- if ratingCount != "0" and ratingCount != blank -%}
        ,"aggregateRating": {
        "@type": "AggregateRating",
        "ratingCount": {{ ratingCount }},
        "ratingValue": {{ product.metafields.stamped.reviews_average }}
        }
        {%- endif -%}
    {%- endcase -%}
}
</script>
{% schema %}
{
  "name": "Product main",
  "tag": "section",
  "class": "bee-section bee-section-main bee-section-main-product bee_tp_flickity",
  "settings": [
    {
      "type": "header",
      "content": "+ Product breadcrumb"
    },
    {
      "type": "select",
      "id": "breadcrumb_design",
      "label": "Breadcrumb design",
      "default": "default",
      "options": [
        {
          "value": "default",
          "label": "Default"
        },
        {
          "value": "inner_content",
          "label": "Inner content"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "pr_breadcrumb",
      "label": "Use product breadcrumb",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "back_next_pr",
      "label": "Use back/next product links",
      "default": true
    },
    {
      "type": "select",
      "id": "box_border",
      "label": "Border, background color",
      "default": "top",
      "info": "Option for breadcrumb design 'Default'",
      "options": [
        {
          "label": "None",
          "value": "none"
        },
        {
          "label": "Top",
          "value": "top"
        },
        {
          "label": "Top + Bottom",
          "value": "top_bottom"
        },
        {
          "label": "Background color",
          "value": "bg_cl"
        }
      ]
    },
    {
      "type": "select",
      "id": "pr_layout",
      "default": "default",
      "options": [
        {
          "value": "default",
          "label": "Default"
        },
        {
          "value": "wide",
          "label": "Wide (1600px)"
        },
        {
          "value": "full",
          "label": "Full width"
        }
      ],
      "label": "+ Product Layout"
    },
    {
      "type": "checkbox",
      "id": "enable_sticky_info",
      "label": "Enable sticky product information on large screens",
      "default": false
    },
    {
      "type": "header",
      "content": "+ Content"
    },
    {
      "type": "checkbox",
      "id": "enable_space_content",
      "label": "Enable expanded space between content and media",
      "info": "Not support for media 'Full width' and working at screen 1199px and above",
      "default": false
    },
    {
      "type": "header",
      "content": "+ Media"
    },
    {
      "type": "paragraph",
      "content": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
    },
    {
      "type": "select",
      "id": "media_layout",
      "label": "Media layout",
      "info": "Choose between different predefined designs",
      "default": "thumbnails_bottom",
      "options": [
        {
          "value": "thumbnails_left",
          "label": "Thumbnails left"
        },
        {
          "value": "thumbnails_bottom",
          "label": "Thumbnails bottom"
        },
        {
          "value": "thumbnails_right",
          "label": "Thumbnails right"
        },
        {
          "value": "without_thumbnails",
          "label": "Without thumbnails"
        },
        {
          "value": "one_column",
          "label": "One column"
        },
        {
          "value": "two_columns",
          "label": "Two columns"
        },
        {
          "value": "combined_grid",
          "label": "Combined grid"
        },
        {
          "value": "combined_grid_2",
          "label": "Combined grid 2"
        },
        {
          "value": "slider_center",
          "label": "Slider center"
        }
      ]
    },
    {
      "type": "select",
      "id": "media_size",
      "label": "Media size",
      "info": "Only applicable on desktop screens",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        },
        {
          "value": "full",
          "label": "Full width"
        }
      ]
    },
    {
      "type": "select",
      "id": "thumb_lr_size",
      "label": "Thumbnail left/right size",
      "info": "Only applicable on desktop screens",
      "default": "medium",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "select",
      "id": "thumb_lr_hover",
      "label": "Thumbnail left/right hover",
      "info": "Only applicable on desktop screens",
      "default": "1",
      "options": [
        {
          "value": "1",
          "label": "Style 1"
        },
        {
          "value": "2",
          "label": "Style 2"
        }
      ]
    },
    {
      "type": "select",
      "id": "image_ratio",
      "label": "Media ratio",
      "default": "ratioadapt",
      "info": "Aspect ratio custom will settings in general panel",
      "options": [
        {
          "group": "Natural",
          "value": "ratioadapt",
          "label": "Adapt to media"
        },
        {
          "group": "Landscape",
          "value": "ratio2_1",
          "label": "2:1"
        },
        {
          "group": "Landscape",
          "value": "ratio16_9",
          "label": "16:9"
        },
        {
          "group": "Landscape",
          "value": "ratio8_5",
          "label": "8:5"
        },
        {
          "group": "Landscape",
          "value": "ratio3_2",
          "label": "3:2"
        },
        {
          "group": "Landscape",
          "value": "ratio4_3",
          "label": "4:3"
        },
        {
          "group": "Landscape",
          "value": "rationt",
          "label": "Ratio ASOS"
        },
        {
          "group": "Squared",
          "value": "ratio1_1",
          "label": "1:1"
        },
        {
          "group": "Portrait",
          "value": "ratio2_3",
          "label": "2:3"
        },
        {
          "group": "Portrait",
          "value": "ratio1_2",
          "label": "1:2"
        },
        {
          "group": "Custom",
          "value": "ratiocus1",
          "label": "Ratio custom 1"
        },
        {
          "group": "Custom",
          "value": "ratiocus2",
          "label": "Ratio custom 2"
        },
        {
          "group": "Custom",
          "value": "ratiocus3",
          "label": "Ratio custom 3"
        },
        {
          "group": "Custom",
          "value": "ratiocus4",
          "label": "Ratio custom 4"
        }
      ]
    },
    {
      "type": "select",
      "id": "image_size",
      "label": "Media ratio size",
      "default": "cover",
      "info": "This settings apply only if the media ratio is not set to 'Adapt to media'",
      "options": [
        {
          "value": "cover",
          "label": "Full"
        },
        {
          "value": "contain",
          "label": "Auto"
        }
      ]
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "Media position",
      "info": "The first value is the horizontal position and the second value is the vertical. This settings apply only if the media ratio is not set to 'Adapt to media'",
      "options": [
        {
          "value": "default",
          "label": "Default"
        },
        {
          "value": "1",
          "label": "Left top"
        },
        {
          "value": "2",
          "label": "Left center"
        },
        {
          "value": "3",
          "label": "Left bottom"
        },
        {
          "value": "4",
          "label": "Right top"
        },
        {
          "value": "5",
          "label": "Right center"
        },
        {
          "value": "6",
          "label": "Right bottom"
        },
        {
          "value": "7",
          "label": "Center top"
        },
        {
          "value": "8",
          "label": "Center center"
        },
        {
          "value": "9",
          "label": "Center bottom"
        }
      ],
      "default": "8"
    },
    {
      "type": "radio",
      "id": "main_click",
      "options": [
        {
          "value": "zoom",
          "label": "Zoom"
        },
        {
          "value": "pswp",
          "label": "PhotoSwipe popup"
        },
        {
          "value": "none",
          "label": "None"
        }
      ],
      "label": "Image click action",
      "info": "Only zoom applicable on desktop screens"
    },
    {
      "type": "select",
      "id": "zoom_tp",
      "label": "Zoom type",
      "default": "external",
      "options": [
        {
          "value": "inner",
          "label": "#1 (inner zoom)"
        },
        {
          "value": "external",
          "label": "#2 (external zoom)"
        },
        {
          "value": "inner2",
          "label": "#3 (inner zoom 2)"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "enable_zoom_icon",
      "label": "Enable 'zoom image' icon",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_zoom_click_mb",
      "label": "Enable click image show zoom popup",
      "info": "Only zoom popup applicable on mobile",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "default": false,
      "label": "Enable video looping"
    },
    {
      "type": "checkbox",
      "id": "enable_video_muting",
      "default": true,
      "label": "Enable video muting"
    },
    {
      "type": "checkbox",
      "id": "enable_video_autoplaying",
      "default": true,
      "label": "Enable video autoplaying",
      "info": "Only working when has slider and on desktop"
    },
    {
      "type": "header",
      "content": "+ Product images slider"
    },
    {
      "type": "select",
      "id": "eff",
      "default": "fade",
      "label": "Slider effect",
      "info": "Effect between transitioning slides",
      "options": [
        {
          "value": "slide",
          "label": "Slide"
        },
        {
          "value": "fade",
          "label": "Fade"
        }
      ]
    },
    {
      "type": "paragraph",
      "content": "—————————————————"
    },
    {
      "type": "paragraph",
      "content": "Prev next button"
    },
    {
      "type": "select",
      "id": "show_btn",
      "options": [
        {
          "value": "show_all",
          "label": "Show all screen"
        },
        {
          "value": "show_desktop",
          "label": "Only show on desktop"
        },
        {
          "value": "show_mobile",
          "label": "Only show on tablet & mobile"
        },
        {
          "value": "hidden",
          "label": "Hidden"
        }
      ],
      "label": "Use prev next button",
      "default": "show_all"
    },
    {
      "type": "select",
      "id": "btn_vi",
      "label": "Visible",
      "default": "always",
      "options": [
        {
          "value": "always",
          "label": "Always"
        },
        {
          "value": "hover",
          "label": "Only hover"
        }
      ]
    },
    {
      "type": "select",
      "id": "btn_owl",
      "label": "Button style",
      "default": "default",
      "options": [
        {
          "value": "default",
          "label": "Default"
        },
        {
          "value": "outline",
          "label": "Outline"
        },
        {
          "value": "simple",
          "label": "Simple"
        }
      ]
    },
    {
      "type": "select",
      "id": "btn_shape",
      "label": "Button shape",
      "info": "Not work for 'Simple' button style",
      "default": "round",
      "options": [
        {
          "value": "none",
          "label": "Default"
        },
        {
          "value": "round",
          "label": "Round"
        },
        {
          "value": "rotate",
          "label": "Rotate"
        }
      ]
    },
    {
      "type": "select",
      "id": "btn_cl",
      "label": "Button color",
      "default": "light",
      "options": [
        {
          "value": "light",
          "label": "Light"
        },
        {
          "value": "dark",
          "label": "Dark"
        },
        {
          "value": "primary",
          "label": "Primary"
        },
        {
          "value": "custom1",
          "label": "Custom color 1"
        },
        {
          "value": "custom2",
          "label": "Custom color 2"
        }
      ]
    },
    {
      "type": "select",
      "id": "btn_size",
      "label": "Button size",
      "default": "small",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ]
    },
    {
      "type": "paragraph",
      "content": "—————————————————"
    },
    {
      "type": "paragraph",
      "content": "Page dots"
    },
    {
      "type": "select",
      "id": "show_dots",
      "info": "Creates and show page dots",
      "options": [
        {
          "value": "show_all",
          "label": "Show all screen"
        },
        {
          "value": "show_desktop",
          "label": "Only show on desktop"
        },
        {
          "value": "show_mobile",
          "label": "Only show on tablet & mobile"
        },
        {
          "value": "hidden",
          "label": "Hidden"
        }
      ],
      "label": "Use carousel's dots",
      "default": "hidden"
    },
    {
      "type": "select",
      "id": "dot_owl",
      "label": "Dots style",
      "default": "br-outline",
      "options": [
        {
          "value": "default",
          "label": "Default"
        },
        {
          "value": "background-active",
          "label": "Background Active"
        },
        {
          "value": "dots_simple",
          "label": "Dots simple"
        },
        {
          "value": "elessi",
          "label": "Elessi"
        },
        {
          "value": "br-outline",
          "label": "Outline"
        },
        {
          "value": "outline-active",
          "label": "Outline active"
        },
        {
          "value": "dots-number",
          "label": "Dots number"
        }
      ]
    },
    {
      "type": "select",
      "id": "dots_cl",
      "label": "Dots color",
      "default": "dark",
      "options": [
        {
          "value": "light",
          "label": "Light (Best on dark background)"
        },
        {
          "value": "dark",
          "label": "Dark"
        },
        {
          "value": "primary",
          "label": "Primary"
        },
        {
          "value": "custom1",
          "label": "Custom color 1"
        },
        {
          "value": "custom2",
          "label": "Custom color 2"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "dots_round",
      "label": "Enable round dots",
      "default": true
    },
    {
      "type": "range",
      "id": "dots_space",
      "min": 2,
      "max": 20,
      "step": 1,
      "label": "Space among dots",
      "unit": "px",
      "default": 10
    },
    {
      "type": "header",
      "content": "+ Product Tabs"
    },
    {
      "type": "select",
      "id": "tabs_design",
      "label": "Product tabs design",
      "default": "tab",
      "options": [
        {
          "value": "list",
          "label": "List"
        },
        {
          "value": "tab",
          "label": "Tab"
        },
        {
          "value": "tab-vertical",
          "label": "Tab vertical"
        },
        {
          "value": "accordion",
          "label": "Accordion"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "tabs_accordion_mb",
      "label": "Enable tabs design accordion mobile",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_line_tabs",
      "label": "Enable line tabs content",
      "default": true
    },
    {
      "type": "select",
      "id": "tabs_position",
      "label": "Product tabs position",
      "default": "external",
      "info": "The option product content inner will be set to design tab design accordion by default.",
      "options": [
        {
          "value": "external",
          "label": "Product content external"
        },
        {
          "value": "inner",
          "label": "Product content inner"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "enable_first_tab",
      "label": "Enable auto open first tab",
      "info": "Only working with layout 'Accordion'",
      "default": false
    },
    {
      "type": "select",
      "id": "tabs_layout",
      "default": "default",
      "info": "Not working when enable tabs position inner",
      "options": [
        {
          "value": "default",
          "label": "Default"
        },
        {
          "value": "box",
          "label": "Box (875px)"
        },
        {
          "value": "full",
          "label": "Full width"
        },
        {
          "value": "content_full",
          "label": "Content full width"
        }
      ],
      "label": "Product tabs layout"
    },
    {
      "type": "color",
      "id": "bg",
      "label": "Background Color",
      "info": "Not working when enable tabs position inner",
      "default": "#fff"
    },
    {
      "type": "color",
      "id": "bg_mb",
      "label": "Background Color (mobile)",
      "info": "Not working when enable tabs position inner",
      "default": "#fff"
    },
    {
      "type": "header",
      "content": "+ Frequently Bought Together"
    },
    {
      "type": "product_list",
      "id": "product_list",
      "label": "Products",
      "limit": 10
    },
    {
      "type": "header",
      "content": "+ Sticky add to cart"
    },
    {
      "type": "paragraph",
      "content": "Only working when enable ajax add to cart"
    },
    {
      "type": "select",
      "id": "sticky_mode",
      "label": "Sticky mode",
      "default": "2",
      "options": [
        {
          "value": "0",
          "label": "Disable"
        },
        {
          "value": "1",
          "label": "Only visible desktop"
        },
        {
          "value": "2",
          "label": "Visbile on desktop, mobile"
        }
      ]
    },
    {
      "type": "select",
      "id": "sticky_show",
      "label": "Show sticky add to cart after",
      "default": "2",
      "options": [
        {
          "value": "1",
          "label": "Always show"
        },
        {
          "value": "2",
          "label": "Show when scrolls outside the scope of the form"
        }
      ]
    },
    {
      "type": "select",
      "id": "sticky_layout_mb",
      "label": "Layout Mobile",
      "default": "minimal",
      "options": [
        {
          "value": "default",
          "label": "Default"
        },
        {
          "value": "minimal",
          "label": "Minimal"
        },
        {
          "value": "minimal2",
          "label": "Very minimal"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "enable_select",
      "label": "Enable choose variants",
      "info": "Only working with product has variant",
      "default": true
    },
    {
      "type": "select",
      "id": "ani",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "bee-ani-bounce",
          "label": "Bounce"
        },
        {
          "value": "bee-ani-tada",
          "label": "Tada"
        },
        {
          "value": "bee-ani-swing",
          "label": "Swing"
        },
        {
          "value": "bee-ani-flash",
          "label": "Flash"
        },
        {
          "value": "bee-ani-fadeIn",
          "label": "FadeIn"
        },
        {
          "value": "bee-ani-heartBeat",
          "label": "HeartBeat"
        },
        {
          "value": "bee-ani-shake",
          "label": "Shake"
        }
      ],
      "label": "Button animation"
    },
    {
      "type": "range",
      "id": "time",
      "min": 2,
      "max": 40,
      "step": 1,
      "label": "Loop time (seconds)",
      "info": "Button animation",
      "unit": "sec",
      "default": 6
    },
    {
      "type": "header",
      "content": "+ Design options desktop",
      "info": "Only work on desktop when 'Product Layout Full'"
    },
    {
      "type": "text",
      "id": "pd",
      "label": "Padding",
      "info": "Padding top, padding right, padding bottom, padding left. If you do not use it please blank.",
      "placeholder": ",15px,,15px",
      "default": ",15px,,15px"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "title",
      "name": "Product title",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "txt_tr_pr",
          "label": "Transform text",
          "default": "none",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "lowercase",
              "label": "Lowercase"
            },
            {
              "value": "capitalize",
              "label": "Capitalize"
            },
            {
              "value": "uppercase",
              "label": "Uppercase"
            }
          ]
        },
        {
          "type": "select",
          "id": "fnt_df_pr",
          "label": "Font family",
          "default": "1",
          "options": [
            {
              "value": "1",
              "label": "Font family #1"
            },
            {
              "value": "2",
              "label": "Font family #2"
            },
            {
              "value": "3",
              "label": "Font family #3"
            }
          ]
        },
        {
          "type": "range",
          "id": "size_pr",
          "min": 10,
          "max": 60,
          "step": 0.5,
          "label": "Font size",
          "unit": "px",
          "default": 16
        },
        {
          "type": "range",
          "id": "fw_pr",
          "min": 300,
          "max": 900,
          "step": 100,
          "label": "Font weight",
          "default": 600
        },
        {
          "type": "range",
          "id": "lh_pr",
          "label": "Line height",
          "max": 100,
          "min": 0,
          "step": 1,
          "default": 0,
          "unit": "px",
          "info": "Set '0' to use default"
        },
        {
          "type": "range",
          "id": "ls_pr",
          "max": 5,
          "min": -5,
          "step": 0.1,
          "label": "Letter spacing",
          "info": "set is '0' use to default",
          "unit": "px",
          "default": 0
        },
        {
          "type": "header",
          "content": "+ OPTION TABLET"
        },
        {
            "type": "range",
            "id": "size_pr_tb",
            "min": 10,
            "max": 60,
            "step": 0.5,
            "label": "Font size",
            "unit": "px",
            "default": 24
        },
        {
            "type": "range",
            "id": "lh_pr_tb",
            "label": "Line height",
            "max": 100,
            "min": 0,
            "step": 1,
            "default": 0,
            "unit": "px",
            "info": "Set '0' to use default"            
        },
        {
            "type": "range",
            "id": "ls_pr_tb",
            "max": 5,
            "min": -5,
            "step": 0.1,
            "label": "Letter spacing",
            "info": "set is '0' use to default",
            "unit": "px",
            "default": 0
        },
         {
          "type": "header",
          "content": "+ OPTION MOBILE"
        },
        {
            "type": "range",
            "id": "size_pr_mb",
            "min": 10,
            "max": 60,
            "step": 0.5,
            "label": "Font size",
            "unit": "px",
            "default": 24
        },
        {
            "type": "range",
            "id": "lh_pr_mb",
            "label": "Line height",
            "max": 100,
            "min": 0,
            "step": 1,
            "default": 0,
            "unit": "px",
            "info": "Set '0' to use default"            
        },
        {
            "type": "range",
            "id": "ls_pr_mb",
            "max": 5,
            "min": -5,
            "step": 0.1,
            "label": "Letter spacing",
            "info": "set is '0' use to default",
            "unit": "px",
            "default": 0
        },
        {
          "type": "color",
          "id": "pr_title_color",
          "label": "Product title",
          "default": "#222"
        }
      ]
    },
    {
      "type": "price_review",
      "name": "Product price",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "price",
          "label": "Price varies settings",
          "default": "0",
          "options": [
            {
              "value": "0",
              "label": "None"
            },
            {
              "value": "1",
              "label": "$39.00 – $59.00"
            },
            {
              "value": "2",
              "label": "From $39.00"
            }
          ]
        },
        {
          "type": "select",
          "id": "type_sale",
          "label": "Save badge type",
          "default": "0",
          "options": [
            {
              "value": "0",
              "label": "None"
            },
            {
              "value": "1",
              "label": "Percentage"
            },
            {
              "value": "2",
              "label": "Fixed amount"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "tax_ship",
          "label": "Show tax and shipping information",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "rating",
          "label": "Use rating",
          "info": "Only works when 'Enable rating' ",
          "default": true
        },
        {
          "type": "liquid",
          "id": "review_liquid",
          "label": "Add Snippets Liquid",
          "info": "Add app snippets reviews to show star rating on product page. Will working when you use 'Other app review'"
        },
        {
          "type": "range",
          "id": "size_price_pr",
          "min": 10,
          "max": 50,
          "step": 0.5,
          "label": "Price size",
          "unit": "px",
          "default": 20
        },
        {
          "type": "range",
          "id": "fw_price_pr",
          "min": 300,
          "max": 800,
          "step": 100,
          "label": "Font weight",
          "default": 400
        },
        {
          "type": "color",
          "id": "primary_price_color",
          "label": "Primary price color",
          "default": "#000"
        },
        {
          "type": "color",
          "id": "secondary_price_color",
          "label": "Secondary price color",
          "default": "#868686"
        },
        {
          "type": "color",
          "id": "price_sale",
          "label": "Price sale color",
          "default": "#fa0000"
        }
      ]
    },
    {
      "type": "line",
      "name": "Product line",
      "settings": [
        {
          "type": "color",
          "id": "color_line",
          "label": "Color line",
          "default": "#e9e9e9"
        },
        {
          "type": "range",
          "id": "height_line",
          "label": "Height",
          "min": 1,
          "max": 5,
          "default": 1,
          "step": 0.5,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "space_tb",
          "label": "Bottom space",
          "min": 1,
          "max": 20,
          "default": 10,
          "step": 0.5,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "space_tb_mb",
          "label": "Bottom space (Mobile)",
          "min": 1,
          "max": 20,
          "default": 10,
          "step": 0.5,
          "unit": "px"
        }
      ]
    },
    {
      "type": "description",
      "name": "Product description",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "des",
          "options": [
            {
              "value": "1",
              "label": "Full description"
            },
            {
              "value": "2",
              "label": "Short description"
            }
          ],
          "label": "Description mode",
          "info": "If you want to show full HTML of the Product Description, please select \"Full Description\".",
          "default": "2"
        },
        {
          "type": "header",
          "content": "Short description configs"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Short description",
          "info": "Short description that will be displayed for each product content if you don't set metafield excerpt for each product content."
        },
        {
          "type": "number",
          "id": "length",
          "label": "Excerpt length (integer)",
          "info": "Number of words that will be displayed for each product content if you don't set short description page or set metafield excerpt for each product content.",
          "default": 31
        },
        {
          "type": "checkbox",
          "id": "readm",
          "label": "Use read more",
          "default": false
        },
        {
          "type": "text",
          "id": "readm_txt",
          "label": "Read more label",
          "default": "Read more"
        }
      ]
    },
    {
      "type": "form",
      "name": "Product form",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "+ Product Swatch"
        },
        {
          "type": "select",
          "id": "selector_mode",
          "label": "Selector type",
          "options": [
            {
              "value": "circle",
              "label": "Circle"
            },
            {
              "value": "radio",
              "label": "Radio"
            },
            {
              "value": "radio is-sw__full",
              "label": "Radio full"
            },
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "block-radius",
              "label": "Block radius"
            },
            {
              "value": "block-fill",
              "label": "Block fill"
            },
            {
              "value": "block-round1",
              "label": "Block round"
            },
            {
              "value": "block-round2",
              "label": "Block round 2"
            },
            {
              "value": "dropdown",
              "label": "Dropdown"
            }
          ],
          "default": "block"
        },
        {
          "type": "select",
          "id": "color_mode",
          "label": "Color selector type",
          "options": [
            {
              "value": "circle",
              "label": "Circle"
            },
            {
              "value": "radio",
              "label": "Radio"
            },
            {
              "value": "radio is-sw-cl__full",
              "label": "Radio full"
            },
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "block2",
              "label": "Block round"
            },
            {
              "value": "dropdown",
              "label": "Dropdown"
            },
            {
              "value": "color",
              "label": "Color swatch"
            },
            {
              "value": "color is-sw-cl__round",
              "label": "Color swatch round"
            },
            {
              "value": "color is-sw-cl__round is-sw-cl__label",
              "label": "Color swatch round with label"
            },
            {
              "value": "variant_image",
              "label": "Variant image"
            },
            {
              "value": "variant_image is-sw-cl__round",
              "label": "Variant image round"
            }
          ],
          "default": "color"
        },
        {
          "type": "checkbox",
          "id": "enable_fit_ratio_img",
          "label": "Enable adapt to first swatch image variant",
          "default": false
        },
        {
          "type": "select",
          "id": "color_size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            },
            {
              "value": "exlarge",
              "label": "Extra Large"
            }
          ],
          "label": "Color selector size",
          "info": "Only working with color swatch, variant image",
          "default": "medium"
        },
        {
          "type": "checkbox",
          "id": "show_qty",
          "label": "Show quantity selector",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "qty_full",
          "label": "Enable quantity full width",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "enable_wishlist",
          "label": "Enable wishlist",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "enable_compare",
          "label": "Enable compare",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "btn_atc_full",
          "label": "Enable button full width",
          "info": "Option for desktop. And for some options",
          "default": false
        },
        {
          "type": "select",
          "id": "w_cp_position",
          "options": [
            {
              "value": "in_bt",
              "label": "In bottom"
            },
            {
              "value": "in_inline",
              "label": "In inline"
            }
          ],
          "label": "Wishlist, compare position",
          "info": "Wishlist, compare inline or bottom Add to cart button when quantity not full width",
          "default": "in_inline"
        },
        {
          "type": "range",
          "id": "pr_btn_round",
          "min": 0,
          "max": 40,
          "step": 1,
          "label": "Button round corners",
          "unit": "px",
          "default": 0
        },
        {
          "type": "header",
          "content": "+ Add to cart button"
        },
        {
          "type": "select",
          "id": "ani",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "bee-ani-bounce",
              "label": "Bounce"
            },
            {
              "value": "bee-ani-tada",
              "label": "Tada"
            },
            {
              "value": "bee-ani-swing",
              "label": "Swing"
            },
            {
              "value": "bee-ani-flash",
              "label": "Flash"
            },
            {
              "value": "bee-ani-fadeIn",
              "label": "FadeIn"
            },
            {
              "value": "bee-ani-heartBeat",
              "label": "HeartBeat"
            },
            {
              "value": "bee-ani-shake",
              "label": "Shake"
            }
          ],
          "label": "Add to cart animation"
        },
        {
          "type": "range",
          "id": "time",
          "min": 2,
          "max": 40,
          "step": 1,
          "label": "Loop time (seconds)",
          "info": "Loop time add to cart animation",
          "unit": "s",
          "default": 6
        },
        {
          "type": "select",
          "id": "btn_txt",
          "default": "3",
          "options": [
            {
              "value": "0",
              "label": "None"
            },
            {
              "value": "1",
              "label": "Lowercase"
            },
            {
              "value": "2",
              "label": "Capitalize"
            },
            {
              "value": "3",
              "label": "Uppercase"
            }
          ],
          "label": "Button transform text"
        },
        {
          "type": "checkbox",
          "id": "btn_icon",
          "label": "Enable button icon",
          "default": false
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "Button style",
          "options": [
            {
              "label": "Default",
              "value": "default"
            },
            {
              "label": "Outline",
              "value": "outline"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_color",
          "label": "Button color",
          "default": "dark",
          "options": [
            {
              "value": "light",
              "label": "Light"
            },
            {
              "value": "dark",
              "label": "Dark"
            },
            {
              "value": "primary",
              "label": "Primary"
            },
            {
              "value": "custom1",
              "label": "Custom color 1"
            },
            {
              "value": "custom2",
              "label": "Custom color 2"
            },
            {
              "value": "custom3",
              "label": "Custom color 3"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_effect",
          "label": "Hover button effect",
          "default": "fade",
          "info": "Only working button style default, outline",
          "options": [
            {
              "label": "Default",
              "value": "default"
            },
            {
              "label": "Fade",
              "value": "fade"
            },
            {
              "label": "Rectangle out",
              "value": "rectangle-out"
            },
            {
              "label": "Sweep to right",
              "value": "sweep-to-right"
            },
            {
              "label": "Sweep to left",
              "value": "sweep-to-left"
            },
            {
              "label": "Sweep to bottom",
              "value": "sweep-to-bottom"
            },
            {
              "label": "Sweep to top",
              "value": "sweep-to-top"
            },
            {
              "label": "Shutter out horizontal",
              "value": "shutter-out-horizontal"
            },
            {
              "label": "Outline",
              "value": "outline"
            },
            {
              "label": "Shadow",
              "value": "shadow"
            }
          ]
        },
        {
          "type": "header",
          "content": "+ Dynamic checkout buttons"
        },
        {
          "type": "checkbox",
          "id": "show_dynamic_checkout",
          "label": "Show dynamic checkout buttons",
          "info": "Using the payment methods available on your store, customers see their preferred option, like PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)",
          "default": false
        },
        {
          "type": "select",
          "id": "btn_txt2",
          "default": "3",
          "options": [
            {
              "value": "0",
              "label": "None"
            },
            {
              "value": "1",
              "label": "Lowercase"
            },
            {
              "value": "2",
              "label": "Capitalize"
            },
            {
              "value": "3",
              "label": "Uppercase"
            }
          ],
          "label": "Button transform text"
        },
        {
          "type": "select",
          "id": "button_dynamic_style",
          "label": "Button style",
          "options": [
            {
              "label": "Default",
              "value": "default"
            },
            {
              "label": "Outline",
              "value": "outline"
            }
          ]
        },
        {
          "type": "select",
          "id": "button_color_payment",
          "label": "Button color",
          "default": "dark",
          "options": [
            {
              "value": "light",
              "label": "Light"
            },
            {
              "value": "dark",
              "label": "Dark"
            },
            {
              "value": "primary",
              "label": "Primary"
            },
            {
              "value": "custom1",
              "label": "Custom color 1"
            },
            {
              "value": "custom2",
              "label": "Custom color 2"
            },
            {
              "value": "custom3",
              "label": "Custom color 3"
            }
          ]
        },
        {
          "type": "header",
          "content": "+ Grouped Product"
        },
        {
          "type": "paragraph",
          "content": "Use metafield: theme.grouped"
        },
        {
          "type": "product_list",
          "id": "product_list",
          "label": "Products",
          "limit": 10
        },
        {
          "type": "checkbox",
          "id": "show_product_current",
          "label": "Show product current",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_text_price",
          "label": "Show subtotal price",
          "default": true
        },
        {
          "type": "select",
          "id": "qty_val",
          "label": "Quantity value default",
          "default": "0",
          "options": [
            {
              "value": "0",
              "label": "0"
            },
            {
              "value": "1",
              "label": "1"
            }
          ]
        },
        {
          "type": "header",
          "content": "+ Advance Product Type"
        },
        {
          "type": "paragraph",
          "content": "Not working if enabled grouped product."
        },
        {
          "type": "paragraph",
          "content": "Use metafield: theme.advance_pr_type"
        },
        {
          "type": "text",
          "id": "advance_label",
          "label": "Label",
          "default": "Choose style"
        },
        {
          "type": "product_list",
          "id": "advance_pr_list",
          "label": "Products",
          "limit": 10
        }
      ]
    },
    {
      "type": "delivery_ask_size",
      "name": "Delivery, Ask, Size chart",
      "limit": 1,
      "settings": [
        {
          "type": "header",
          "content": "+ Delivery & Return"
        },
        {
          "type": "checkbox",
          "id": "delivery",
          "label": "Use delivery & return",
          "default": false
        },
        {
          "type": "page",
          "id": "page_dr",
          "label": "Add page delivery & return",
          "info": "This page content will appear."
        },
        {
          "type": "header",
          "content": "+ Ask a question"
        },
        {
          "type": "checkbox",
          "id": "ask",
          "label": "Show ask a question",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "phone",
          "label": "Show input phone",
          "default": true
        },
        {
          "type": "header",
          "content": "+ Size chart"
        },
        {
          "type": "paragraph",
          "content": "Recommend app: [AVADA Size Chart](https://apps.shopify.com/avada-size-chart?utm_source=Bee&utm_medium=inapp&utm_campaign=partnership)"
        },
        {
          "type": "select",
          "id": "size_chart",
          "label": "Use size chart",
          "default": "3",
          "options": [
            {
              "value": "1",
              "label": "None"
            },
            {
              "value": "2",
              "label": "Only product has option name 'size'"
            },
            {
              "value": "3",
              "label": "All product"
            }
          ]
        },
        {
          "type": "select",
          "id": "pos_sizeg",
          "label": "Position show size chart",
          "default": "1",
          "options": [
            {
              "value": "1",
              "label": "Default"
            },
            {
              "value": "2",
              "label": "Show on swatch option name 'size'"
            }
          ]
        },
        {
          "type": "select",
          "id": "sc_type",
          "default": "1",
          "options": [
            {
              "value": "1",
              "label": "HTML"
            },
            {
              "value": "2",
              "label": "IMAGE"
            }
          ],
          "label": "Size chart type"
        },
        {
          "type": "page",
          "id": "page",
          "label": "HTML size chart",
          "info": "This page content will appear."
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image size chart"
        },
        {
          "type": "textarea",
          "id": "size_ck",
          "label": "Enter option name you want has size guide",
          "info": "Eg: size,sizes,Größe"
        },
        {
          "type": "header",
          "content": "+ Icon size chart"
        },
        {
          "type": "select",
          "id": "icon",
          "label": "ICON / IMG",
          "default": "default",
          "options": [
            {
              "value": "1",
              "label": "None"
            },
            {
              "value": "default",
              "label": "Default"
            },
            {
              "value": "2",
              "label": "Icon"
            },
            {
              "value": "3",
              "label": "Image"
            }
          ]
        },
        {
          "type": "text",
          "id": "icon_name",
          "label": "Icon class name",
          "default": "las la-ruler-horizontal",
          "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
        },
        {
          "type": "image_picker",
          "id": "img",
          "label": "Image icon",
          "info": "50x50 recommend"
        },
        {
          "type": "select",
          "id": "ani",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "bounce is--infinite",
              "label": "Bounce"
            },
            {
              "value": "tada is--infinite",
              "label": "Tada"
            },
            {
              "value": "swing is--infinite",
              "label": "Swing"
            },
            {
              "value": "flash is--infinite",
              "label": "Flash"
            },
            {
              "value": "fadeIn is--infinite",
              "label": "FadeIn"
            },
            {
              "value": "heartBeat is--infinite",
              "label": "HeartBeat"
            },
            {
              "value": "shake is--infinite",
              "label": "Shake"
            }
          ],
          "label": "ICON / IMG animation"
        }
      ]
    },
    {
      "type": "meta",
      "name": "Product meta",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "pr_meta_horizontal",
          "label": "Design product meta horizontal",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_options",
          "label": "Show product options",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_vendor",
          "label": "Show product vendor",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_type",
          "label": "Show product type",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_sku",
          "label": "Show sku",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_barcode",
          "label": "Show barcode",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_available",
          "label": "Show available",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_category",
          "label": "Show collection product",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_tags",
          "label": "Show product's tags",
          "default": true
        }
      ]
    },
    {
      "type": "socials",
      "name": "Product socials",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Share"
        },
        {
          "type": "select",
          "id": "social_mode",
          "label": "Socials mode",
          "options": [
            {
              "value": "1",
              "label": "Follow"
            },
            {
              "value": "2",
              "label": "Share"
            }
          ],
          "default": "2"
        },
        {
          "type": "select",
          "id": "social_style",
          "label": "Socials style",
          "options": [
            {
              "value": "default",
              "label": "Default"
            },
            {
              "value": "outline",
              "label": "Outline"
            },
            {
              "value": "simple",
              "label": "Simple"
            }
          ],
          "default": "simple"
        },
        {
          "type": "select",
          "id": "social_size",
          "label": "Socials size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium"
        },
        {
          "type": "range",
          "id": "bd_radius",
          "label": "Border radius",
          "info": "Not work when socials style is \"Simple\"",
          "unit": "px",
          "min": 0,
          "max": 30,
          "default": 0,
          "step": 1
        },
        {
          "type": "header",
          "content": "+ Custom color for social icon"
        },
        {
          "type": "checkbox",
          "id": "use_color_set",
          "label": "Use color settings",
          "info": "The default is the themes color.Tick here if you want to customize the color for the icon socials.",
          "default": false
        },
        {
          "type": "color",
          "id": "pri_cl",
          "label": "Primary color",
          "default": "#878787"
        },
        {
          "type": "color",
          "id": "pri_hover_cl",
          "label": "Primary hover color",
          "default": "#222222"
        },
        {
          "type": "header",
          "content": "+ Space between items"
        },
        {
          "type": "select",
          "id": "space_h_item",
          "options": [
            {
              "value": "0",
              "label": "0"
            },
            {
              "value": "2",
              "label": "2px"
            },
            {
              "value": "4",
              "label": "4px"
            },
            {
              "value": "5",
              "label": "5px"
            },
            {
              "value": "8",
              "label": "8px"
            },
            {
              "value": "10",
              "label": "10px"
            },
            {
              "value": "15",
              "label": "15px"
            },
            {
              "value": "20",
              "label": "20px"
            },
            {
              "value": "25",
              "label": "25px"
            },
            {
              "value": "30",
              "label": "30px"
            }
          ],
          "label": "Space horizontal items",
          "default": "5"
        },
        {
          "type": "select",
          "id": "space_v_item",
          "options": [
            {
              "value": "0",
              "label": "0"
            },
            {
              "value": "2",
              "label": "2px"
            },
            {
              "value": "4",
              "label": "4px"
            },
            {
              "value": "5",
              "label": "5px"
            },
            {
              "value": "8",
              "label": "8px"
            },
            {
              "value": "10",
              "label": "10px"
            },
            {
              "value": "15",
              "label": "15px"
            },
            {
              "value": "20",
              "label": "20px"
            },
            {
              "value": "25",
              "label": "25px"
            },
            {
              "value": "30",
              "label": "30px"
            }
          ],
          "label": "Space vertical items",
          "default": "5"
        },
        {
          "type": "select",
          "id": "space_h_item_mb",
          "options": [
            {
              "value": "0",
              "label": "0"
            },
            {
              "value": "2",
              "label": "2px"
            },
            {
              "value": "4",
              "label": "4px"
            },
            {
              "value": "6",
              "label": "6px"
            },
            {
              "value": "8",
              "label": "8px"
            },
            {
              "value": "10",
              "label": "10px"
            },
            {
              "value": "15",
              "label": "15px"
            },
            {
              "value": "20",
              "label": "20px"
            },
            {
              "value": "25",
              "label": "25px"
            },
            {
              "value": "30",
              "label": "30px"
            }
          ],
          "label": "Space horizontal items (Mobile)",
          "default": "2"
        },
        {
          "type": "select",
          "id": "space_v_item_mb",
          "options": [
            {
              "value": "0",
              "label": "0"
            },
            {
              "value": "2",
              "label": "2px"
            },
            {
              "value": "4",
              "label": "4px"
            },
            {
              "value": "6",
              "label": "6px"
            },
            {
              "value": "8",
              "label": "8px"
            },
            {
              "value": "10",
              "label": "10px"
            },
            {
              "value": "15",
              "label": "15px"
            },
            {
              "value": "20",
              "label": "20px"
            },
            {
              "value": "25",
              "label": "25px"
            },
            {
              "value": "30",
              "label": "30px"
            }
          ],
          "label": "Space vertical items (Mobile)",
          "default": "2"
        }
      ]
    },
    {
      "type": "countdown",
      "name": "Countdown timer",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Display a countdown timer in your product page."
        },
        {
          "type": "select",
          "id": "source",
          "label": "Show countdown timer",
          "default": "1",
          "options": [
            {
              "value": "1",
              "label": "All products"
            },
            {
              "value": "2",
              "label": "Only product had tag 'has_stock_countdown'"
            }
          ]
        },
        {
          "type": "select",
          "id": "icon",
          "label": "ICON / IMG",
          "default": "2",
          "options": [
            {
              "value": "1",
              "label": "None"
            },
            {
              "value": "2",
              "label": "Icon"
            },
            {
              "value": "3",
              "label": "Image"
            }
          ]
        },
        {
          "type": "text",
          "id": "icon_name",
          "label": "Icon class name",
          "default": "las la-stopwatch",
          "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
        },
        {
          "type": "image_picker",
          "id": "img",
          "label": "Image",
          "info": "50x50 recommend"
        },
        {
          "type": "select",
          "id": "ani",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "bounce is--infinite",
              "label": "Bounce"
            },
            {
              "value": "tada is--infinite",
              "label": "Tada"
            },
            {
              "value": "swing is--infinite",
              "label": "Swing"
            },
            {
              "value": "flash is--infinite",
              "label": "Flash"
            },
            {
              "value": "fadeIn is--infinite",
              "label": "FadeIn"
            },
            {
              "value": "heartBeat is--infinite",
              "label": "HeartBeat"
            },
            {
              "value": "shake is--infinite",
              "label": "Shake"
            }
          ],
          "label": "ICON / IMG animation"
        },
        {
          "type": "select",
          "id": "al",
          "label": "Text align",
          "default": "center",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Right"
            }
          ]
        },
        {
          "type": "textarea",
          "id": "mess",
          "label": "Message",
          "default": "Hurry up! Sale Ends in",
          "placeholder": "Hurry up! Sale Ends in"
        },
        {
          "type": "range",
          "id": "size",
          "min": 14,
          "max": 50,
          "step": 1,
          "label": "Font size",
          "unit": "px",
          "default": 16
        },
        {
          "type": "color",
          "id": "color",
          "label": "Color",
          "default": "#fb0000"
        },
        {
          "type": "header",
          "content": "+ Countdown Metafields"
        },
        {
          "type": "range",
          "id": "dayx",
          "min": 0,
          "max": 100,
          "step": 1,
          "label": "Reset countdown every x days from an initial date.",
          "info": "Set is '0' to disable reset countdown.",
          "unit": "day",
          "default": 0
        },
        {
          "type": "paragraph",
          "content": "Metafields product countdown, Countdown to the end sale date will be shown. Be sure you have set final date of the product sale price. theme.countdown ( 1994/04/19 ) or ( 1994/04/19 17:34:56 )"
        },
        {
          "type": "header",
          "content": "+ Countdown Loop Day"
        },
        {
          "type": "textarea",
          "id": "stock_time",
          "label": "Countdown timer loop in a day",
          "default": "8:00:00,16:00:00,23:59:59",
          "placeholder": "8:00:00,16:00:00,23:59:59"
        },
        {
          "type": "checkbox",
          "id": "loop",
          "label": "Use loop countdown",
          "default": false
        },
        {
          "type": "paragraph",
          "content": "Hour of the day, 24-hour clock (00..23), Minute of the hour (00..59), Second of the minute (00..59)"
        },
        {
          "type": "select",
          "id": "cdt_size",
          "label": "Countdown size",
          "options": [
            {
              "value": "small",
              "label": "Small"
            },
            {
              "value": "medium",
              "label": "Medium"
            },
            {
              "value": "large",
              "label": "Large"
            }
          ],
          "default": "medium"
        },
        {
          "type": "range",
          "id": "space_item",
          "label": "Space between items",
          "default": 30,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "space_item_tb",
          "label": "Space between items (Tablet)",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "space_item_mb",
          "label": "Space between items (Mobile)",
          "default": 10,
          "min": 0,
          "max": 30,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "color",
          "id": "number_cl",
          "label": "Number color",
          "default": "#000"
        },
        {
          "type": "color",
          "id": "text_cl",
          "label": "Text color",
          "default": "#000"
        },
        {
          "type": "color",
          "id": "bg_cl",
          "label": "Background color",
          "default": "#f8f8f8"
        }
      ]
    },
    {
      "type": "vendor_img",
      "name": "Brand Image",
      "limit": 1,
      "settings": []
    },
    {
      "type": "inventory_qty",
      "name": " Inventory quantity",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Display the stock level of your product variant."
        },
        {
          "type": "select",
          "id": "icon",
          "label": "ICON / IMG",
          "default": "2",
          "options": [
            {
              "value": "1",
              "label": "None"
            },
            {
              "value": "default",
              "label": "Default"
            },
            {
              "value": "2",
              "label": "Icon"
            },
            {
              "value": "3",
              "label": "Image"
            }
          ]
        },
        {
          "type": "text",
          "id": "icon_name",
          "label": "Icon class name",
          "default": "las la-hourglass-start",
          "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
        },
        {
          "type": "image_picker",
          "id": "img",
          "label": "Image",
          "info": "50x50 recommend"
        },
        {
          "type": "select",
          "id": "ani",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "bounce is--infinite",
              "label": "Bounce"
            },
            {
              "value": "tada is--infinite",
              "label": "Tada"
            },
            {
              "value": "swing is--infinite",
              "label": "Swing"
            },
            {
              "value": "flash is--infinite",
              "label": "Flash"
            },
            {
              "value": "fadeIn is--infinite",
              "label": "FadeIn"
            },
            {
              "value": "heartBeat is--infinite",
              "label": "HeartBeat"
            },
            {
              "value": "shake is--infinite",
              "label": "Shake"
            }
          ],
          "label": "ICON / IMG animation"
        },
        {
          "type": "select",
          "id": "stock",
          "label": "Stock",
          "default": "3",
          "options": [
            {
              "value": "1",
              "label": "Only default"
            },
            {
              "value": "2",
              "label": "Only random"
            },
            {
              "value": "3",
              "label": "Default + Random"
            }
          ]
        },
        {
          "type": "header",
          "content": "Default"
        },
        {
          "type": "range",
          "id": "qty",
          "min": 1,
          "max": 100,
          "step": 1,
          "unit": "Qty",
          "label": "(X) items",
          "info": "Show when less than (X) items are in stock",
          "default": 10
        },
        {
          "type": "header",
          "content": "Random"
        },
        {
          "type": "range",
          "id": "total_items",
          "label": "Total items",
          "min": 10,
          "max": 100,
          "step": 10,
          "default": 100
        },
        {
          "type": "range",
          "id": "stock_from",
          "label": "from",
          "min": 1,
          "max": 19,
          "step": 1,
          "default": 12
        },
        {
          "type": "range",
          "id": "stock_to",
          "label": "to",
          "min": 20,
          "max": 70,
          "step": 1,
          "default": 20
        },
        {
          "type": "header",
          "content": "Translate labels"
        },
        {
          "type": "select",
          "id": "al",
          "label": "Text align",
          "default": "center",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Right"
            }
          ]
        },
        {
          "type": "textarea",
          "id": "mess",
          "label": "Message (You can leave it blank)",
          "info": "Hurry! Only [stock_number] left in stock.",
          "placeholder": "Hurry! Only [stock_number] left in stock.",
          "default": "HURRY! ONLY [stock_number] LEFT IN STOCK."
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 35,
          "step": 1,
          "label": "Font size",
          "unit": "px",
          "default": 16
        },
        {
          "type": "header",
          "content": "Progress"
        },
        {
          "type": "checkbox",
          "id": "progress",
          "label": "Use progress bar",
          "default": true
        },
        {
          "type": "range",
          "id": "wbar",
          "min": 40,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "Width progress bar",
          "default": 100
        },
        {
          "type": "color",
          "id": "stock_bg_process",
          "label": "Process color",
          "default": "#f76b6a"
        },
        {
          "type": "color",
          "id": "bgten",
          "label": "Less than 10 color",
          "default": "#ec0101"
        },
        {
          "type": "color",
          "id": "stock_bg",
          "label": "Background color",
          "default": "#ffe8e8"
        }
      ]
    },
    {
      "type": "img",
      "name": "Trust badge",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Recommend app: [AVADA Boost Sales](https://apps.shopify.com/avada-boost-sales?utm_source=Bee&utm_medium=inapp&utm_campaign=partnership)"
        },
        {
          "type": "select",
          "id": "al",
          "label": "Content align",
          "info": "This option only works on desktops. On mobiles, the content will be centered",
          "default": "start",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Right"
            }
          ]
        },
        {
          "type": "checkbox",
          "id": "enable_border",
          "label": "Enable border"
        },
        {
          "type": "textarea",
          "id": "mess",
          "label": "Message",
          "default": "Guarantee safe & secure checkout",
          "placeholder": "Guarantee safe & secure checkout"
        },
        {
          "type": "range",
          "id": "size",
          "min": 10,
          "max": 20,
          "step": 1,
          "label": "Font size",
          "unit": "px",
          "default": 13
        },
        {
          "type": "select",
          "id": "source",
          "label": "Source image",
          "default": "1",
          "options": [
            {
              "value": "1",
              "label": "Image"
            },
            {
              "value": "2",
              "label": "SVG"
            }
          ]
        },
        {
          "type": "header",
          "content": "+ Image"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Trust seal image"
        },
        {
          "type": "range",
          "id": "wimg",
          "min": 40,
          "max": 100,
          "step": 1,
          "unit": "%",
          "label": "Width image",
          "default": 60
        },
        {
          "type": "header",
          "content": "+ SVG"
        },
        {
          "type": "textarea",
          "id": "svg",
          "label": "SVG list",
          "default": "amazon_payments,american_express,apple_pay,bitcoin,dankort,diners_club,discover,dogecoin,dwolla,forbrugsforeningen,interac,google_pay,jcb,klarna,klarna-pay-later,litecoin,maestro,master,paypal,shopify_pay,sofort,visa",
          "info": "amazon_payments,american_express,apple_pay,bitcoin,dankort,diners_club,discover,dogecoin,dwolla,forbrugsforeningen,interac,google_pay,jcb,klarna,klarna-pay-later,litecoin,maestro,master,paypal,shopify_pay,sofort,visa"
        },
        {
          "type": "range",
          "id": "height",
          "min": 1,
          "max": 100,
          "step": 1,
          "label": "Height",
          "unit": "px",
          "default": 50
        }
      ]
    },
    {
      "type": "store_pickup",
      "name": "Pickup availability",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Engage local shoppers by showing where items are available for pickup — right from the product page. [Learn more](https://help.shopify.com/en/manual/shipping/setting-up-and-managing-your-shipping/local-methods/local-pickup)"
        }
      ]
    },
    {
      "type": "live_view",
      "name": "Live view",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Display fake the number of people viewing your product page."
        },
        {
          "type": "select",
          "id": "icon",
          "label": "ICON / IMG",
          "default": "default",
          "options": [
            {
              "value": "1",
              "label": "None"
            },
            {
              "value": "default",
              "label": "Default"
            },
            {
              "value": "2",
              "label": "Icon"
            },
            {
              "value": "3",
              "label": "Image"
            }
          ]
        },
        {
          "type": "text",
          "id": "icon_name",
          "label": "Icon class name",
          "default": "las la-eye",
          "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
        },
        {
          "type": "image_picker",
          "id": "img",
          "label": "Image",
          "info": "50x50 recommend"
        },
        {
          "type": "select",
          "id": "ani",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "bounce is--infinite",
              "label": "Bounce"
            },
            {
              "value": "tada is--infinite",
              "label": "Tada"
            },
            {
              "value": "swing is--infinite",
              "label": "Swing"
            },
            {
              "value": "flash is--infinite",
              "label": "Flash"
            },
            {
              "value": "fadeIn is--infinite",
              "label": "FadeIn"
            },
            {
              "value": "heartBeat is--infinite",
              "label": "HeartBeat"
            },
            {
              "value": "shake is--infinite",
              "label": "Shake"
            }
          ],
          "label": "ICON / IMG animation"
        },
        {
          "type": "range",
          "id": "min",
          "min": 1,
          "max": 100,
          "step": 1,
          "label": "Min fake real time visitor",
          "default": 1
        },
        {
          "type": "range",
          "id": "max",
          "min": 10,
          "max": 1000,
          "step": 10,
          "label": "Max fake real time visitor",
          "default": 100
        },
        {
          "type": "range",
          "id": "time",
          "min": 1,
          "max": 20,
          "step": 1,
          "unit": "sec",
          "label": "Interval time",
          "default": 2
        },
        {
          "type": "textarea",
          "id": "text",
          "label": "Text",
          "default": "[count] People are viewing this right now"
        }
      ]
    },
    {
      "type": "sold",
      "name": "Total sold flash",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "icon",
          "label": "ICON / IMG",
          "default": "default",
          "options": [
            {
              "value": "1",
              "label": "None"
            },
            {
              "value": "default",
              "label": "Default"
            },
            {
              "value": "2",
              "label": "Icon"
            },
            {
              "value": "3",
              "label": "Image"
            }
          ]
        },
        {
          "type": "text",
          "id": "icon_name",
          "label": "Icon class name",
          "default": "lab la-gripfire",
          "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
        },
        {
          "type": "image_picker",
          "id": "img",
          "label": "Image",
          "info": "50x50 recommend"
        },
        {
          "type": "select",
          "id": "ani",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "bounce is--infinite",
              "label": "Bounce"
            },
            {
              "value": "tada is--infinite",
              "label": "Tada"
            },
            {
              "value": "swing is--infinite",
              "label": "Swing"
            },
            {
              "value": "flash is--infinite",
              "label": "Flash"
            },
            {
              "value": "fadeIn is--infinite",
              "label": "FadeIn"
            },
            {
              "value": "heartBeat is--infinite",
              "label": "HeartBeat"
            },
            {
              "value": "shake is--infinite",
              "label": "Shake"
            }
          ],
          "label": "ICON / IMG animation"
        },
        {
          "type": "range",
          "id": "mins",
          "min": 1,
          "max": 100,
          "step": 1,
          "unit": "qty",
          "label": "Min quantity",
          "default": 5
        },
        {
          "type": "range",
          "id": "maxs",
          "min": 10,
          "max": 110,
          "step": 1,
          "unit": "qty",
          "label": "Max quantity",
          "default": 25
        },
        {
          "type": "range",
          "id": "mint",
          "min": 1,
          "max": 24,
          "step": 1,
          "unit": "h",
          "label": "Min time",
          "default": 3
        },
        {
          "type": "range",
          "id": "maxt",
          "min": 1,
          "max": 24,
          "step": 1,
          "unit": "h",
          "label": "Max time",
          "default": 24
        },
        {
          "type": "textarea",
          "id": "text",
          "label": "Text",
          "info": "[sold] sold in last [hour] hours",
          "default": "[sold] sold in last [hour] hours"
        }
      ]
    },
    {
      "type": "order",
      "name": "Delivery time",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Display an approximate date of delivery."
        },
        {
          "type": "checkbox",
          "id": "hide_pre",
          "label": "Hide with 'pre-order'",
          "default": true
        },
        {
          "type": "select",
          "id": "icon",
          "label": "ICON / IMG",
          "default": "default",
          "options": [
            {
              "value": "1",
              "label": "None"
            },
            {
              "value": "default",
              "label": "Default"
            },
            {
              "value": "2",
              "label": "Icon"
            },
            {
              "value": "3",
              "label": "Image"
            }
          ]
        },
        {
          "type": "text",
          "id": "icon_name",
          "label": "Icon class name",
          "default": "las la-truck",
          "info": "[Get name icon](https://kalles.the4.co/font-lineawesome/)"
        },
        {
          "type": "image_picker",
          "id": "img",
          "label": "Image",
          "info": "35x35 recommend"
        },
        {
          "type": "select",
          "id": "ani",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "bounce is--infinite",
              "label": "Bounce"
            },
            {
              "value": "tada is--infinite",
              "label": "Tada"
            },
            {
              "value": "swing is--infinite",
              "label": "Swing"
            },
            {
              "value": "flash is--infinite",
              "label": "Flash"
            },
            {
              "value": "fadeIn is--infinite",
              "label": "FadeIn"
            },
            {
              "value": "heartBeat is--infinite",
              "label": "HeartBeat"
            },
            {
              "value": "shake is--infinite",
              "label": "Shake"
            }
          ],
          "label": "ICON / IMG animation"
        },
        {
          "type": "textarea",
          "id": "txt",
          "label": "Delivery text",
          "default": "Order in the next [hour] to get it between [date_start] and [date_end]",
          "info": "Order in the next [hour] to get this to you between [date_start] and [date_end], Order in the next [hour] to get it by [date_end], Order in the next [hour] to get it soon"
        },
        {
          "type": "range",
          "id": "ds",
          "min": 0,
          "max": 99,
          "step": 1,
          "label": "Delivery start date",
          "info": "From Current date",
          "default": 10
        },
        {
          "type": "range",
          "id": "de",
          "min": 0,
          "max": 99,
          "step": 1,
          "label": "Delivery end date",
          "info": "From Current date",
          "default": 15
        },
        {
          "type": "select",
          "id": "mode",
          "default": "1",
          "options": [
            {
              "value": "1",
              "label": "Only Delivery"
            },
            {
              "value": "2",
              "label": "Shipping + Delivery"
            }
          ],
          "label": "Exclude days from"
        },
        {
          "type": "text",
          "id": "cut_day",
          "label": "Exclude days",
          "default": "SAT,SUN",
          "info": "Use the 'MON','TUE','WED','THU','FRI','SAT' and 'SUN'. Separate exclude days with a comma (,)."
        },
        {
          "type": "select",
          "id": "frm_day",
          "default": "bee4, bee5 bee6",
          "options": [
            {
              "value": "bee4, bee5 bee6",
              "label": "Wednesday, 19th April"
            },
            {
              "value": "bee4, DD bee6",
              "label": "Wednesday, 19 April"
            },
            {
              "value": "bee4, bee5 bee6 YYYY",
              "label": "Wednesday, 19th April 2019"
            },
            {
              "value": "bee4, bee5 bee6, YYYY",
              "label": "Wednesday, 19th April, 2019"
            },
            {
              "value": "bee4, bee6 bee5, YYYY",
              "label": "Wednesday, April 19th, 2019"
            },
            {
              "value": "bee4, bee6 bee5",
              "label": "Wednesday, April 19th"
            },
            {
              "value": "bee4, bee6 bee5 YYYY",
              "label": "Wednesday, April 19th 2019"
            },
            {
              "value": "bee4, bee6 DD",
              "label": "Wednesday, April 19"
            },
            {
              "value": "bee4, bee6 DD YYYY",
              "label": "Wednesday, April 19 2019"
            },
            {
              "value": "bee4, MM/DD/YYYY",
              "label": "Wednesday, 04/19/2019"
            },
            {
              "value": "bee4, DD/MM/YYYY",
              "label": "Wednesday, 19/04/2019"
            },
            {
              "value": "bee4, YYYY/MM/DD",
              "label": "Wednesday, 2019/04/19"
            }
          ],
          "label": "Date delivery format"
        },
        {
          "type": "text",
          "id": "time",
          "label": "Delivery cut off",
          "info": "Number Only(24 Hours Format - 16:00:00 Means 4PM)",
          "default": "16:00:00"
        },
        {
          "type": "text",
          "id": "hr",
          "label": "Text hours",
          "default": "hours"
        },
        {
          "type": "text",
          "id": "min",
          "label": "Text minutes",
          "default": "minutes"
        }
      ]
    },
    {
      "type": "break",
      "name": "Break block",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Working with option media size 'Full width'"
        },
        {
          "type": "range",
          "id": "space_block",
          "label": "Space betwwen block (Desktop)",
          "min": 10,
          "max": 240,
          "step": 2.5,
          "unit": "px",
          "default": 225
        },
        {
          "type": "range",
          "id": "space_block_tb",
          "label": "Space betwwen block (Tablet)",
          "min": 10,
          "max": 240,
          "step": 2.5,
          "unit": "px",
          "default": 150
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
        }
      ]
    },
    {
      "type": "html",
      "name": "Custom HTML",
      "settings": [
        {
          "type": "page",
          "id": "page",
          "label": "Content page",
          "info": "This page content will appear."
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "Custom Liquid",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "Custom Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "tab_des",
      "name": "Tab Description",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Description"
        }
      ]
    },
    {
      "type": "tab_add",
      "name": "Tab Additional Infor",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Additional Information"
        }
      ]
    },
    {
      "type": "tab_rivui",
      "name": "Tab Review",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Reviews",
          "info": "Use product title, you can use shortocdes: [pr_title]"
        },
        {
          "type": "liquid",
          "id": "review_liquid",
          "label": "Add Snippets Liquid",
          "info": "Add app snippets reviews to show a 'write reviews' on product page. Will working when you use 'Other app review'"
        }
      ]
    },
    {
      "type": "tab_html",
      "name": "Tab Custom HTML",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Collapsible tab",
          "info": "Include a heading that explains the content."
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Tab content"
        },
        {
          "type": "page",
          "id": "page",
          "label": "Tab content from page"
        }
      ]
    },
    {
      "type": "tab_liquid",
      "name": "Tab Custom Liquid",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Tab Custom Liquid"
        },
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "Custom Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "properties",
      "name": "⚡ Customizable Products",
      "settings": [
        {
          "type": "paragraph",
          "content": "Block properties are used to collect customization information for an item added to the cart. This information can be collected from the buyer on the product page. Only show on block product form. Not show on product grouped, soldout, external/affiliate"
        },
        {
          "type": "header",
          "content": "+ Set your form field"
        },
        {
          "type": "radio",
          "id": "type",
          "default": "short",
          "options": [
            {
              "value": "short",
              "label": "Text - Short"
            },
            {
              "value": "long",
              "label": "Text - Long"
            },
            {
              "value": "checkbox",
              "label": "Checkbox"
            },
            {
              "value": "radio",
              "label": "Radio Buttons"
            },
            {
              "value": "select",
              "label": "Drop-down select"
            },
            {
              "value": "checkbox_group",
              "label": "Checkbox group"
            },
            {
              "value": "file",
              "label": "File upload (beta)"
            }
          ],
          "label": "Type of form field"
        },
        {
          "type": "paragraph",
          "content": "IMPORTANT: Upload file not support dynamic checkout buttons on product page, quick view, quick shop."
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Your name",
          "label": "Your form field label"
        },
        {
          "type": "textarea",
          "id": "options",
          "label": "Options if using radio buttons, a drop-down select, or checkbox group",
          "default": "option1, option2",
          "info": "Separate your options with a comma."
        },
        {
          "type": "checkbox",
          "id": "required",
          "default": false,
          "label": "Required",
          "info": "If you use \"Required\" with a checkbox, then the checkbox will need to be checked for the customer to add the item to the cart."
        },
        {
          "type": "checkbox",
          "id": "show_at_checkout",
          "default": true,
          "label": "Show at checkout, cart",
          "info": "Uncheck this if you don't want the captured information to be shown in the order summary on the cart, checkout page."
        },
        {
          "type": "header",
          "content": "+ Set your visibility"
        },
        {
          "type": "radio",
          "id": "visibility",
          "default": "all",
          "options": [
            {
              "value": "all",
              "label": "All"
            },
            {
              "value": "collection",
              "label": "Collection based"
            },
            {
              "value": "type",
              "label": "Type based"
            },
            {
              "value": "tag",
              "label": "Tag based"
            },
            {
              "value": "product",
              "label": "Product based"
            },
            {
              "value": "metafield",
              "label": "Metafield based"
            }
          ],
          "label": "Visibility",
          "info": "Metafield based: theme.visibility_customizable"
        },
        {
          "type": "collection_list",
          "id": "collection_based",
          "label": "Collection list",
          "info": "Maximum choose: 50 collections"
        },
        {
          "type": "textarea",
          "id": "type_based",
          "label": "Product types",
          "placeholder": "type1, type2",
          "info": "Separate your types with a comma."
        },
        {
          "type": "textarea",
          "id": "tag_based",
          "label": "Product tags",
          "placeholder": "tag1, tag2",
          "info": "Separate your types with a comma."
        },
        {
          "type": "product_list",
          "id": "product_based",
          "label": "Product list",
          "info": "Maximum choose: 50 products"
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "title"
      },
      {
        "type": "price_review"
      },
      {
        "type": "description"
      },
      {
        "type": "form"
      },
      {
        "type": "delivery_ask_size"
      },
      {
        "type": "meta"
      },
      {
        "type": "socials"
      }
    ]
  }
}
{% endschema %}