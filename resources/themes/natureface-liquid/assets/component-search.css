.search__input.field__input {
  padding-right: 9.8rem;
}

.search__button {
  right: var(--inputs-border-width);
  top: var(--inputs-border-width);
}

.reset__button {
  right: calc(var(--inputs-border-width) + 4.4rem);
  top: var(--inputs-border-width);
}

.reset__button:not(:focus-visible)::after {
  border-right: 0.1rem solid rgba(var(--color-foreground), 0.08);
  display: block;
  height: calc(100% - 1.6rem);
  content: '';
  position: absolute;
  right: 0;
}

.reset__button:not(:focus)::after {
  border-right: 0.1rem solid rgba(var(--color-foreground), 0.08);
  display: block;
  height: calc(100% - 1.8rem);
  content: '';
  position: absolute;
  right: 0;
}

.search__button:focus-visible,
.reset__button:focus-visible {
  background-color: rgb(var(--color-background));
  z-index: 4;
}

.search__button:focus,
.reset__button:focus {
  background-color: rgb(var(--color-background));
  z-index: 4;
}

.search__button:not(:focus-visible):not(.focused),
.reset__button:not(:focus-visible):not(.focused) {
  box-shadow: inherit;
  background-color: inherit;
}

.search__button:hover .icon,
.reset__button:hover .icon {
  transform: scale(1.07);
}

.search__button .icon {
  height: 1.8rem;
  width: 1.8rem;
}

.reset__button .icon.icon-close {
  height: 1.8rem;
  width: 1.8rem;
  stroke-width: 0.1rem;
}

/* Remove extra spacing for search inputs in Safari */
input::-webkit-search-decoration {
  -webkit-appearance: none;
}

.template-search__results {
  position: relative;
}



/* ---------------extra css---------------- */

.template-search .card.card--standard.card--media {
    border: 0px;
    padding: 10px;
}

.template-search .active-facets-vertical-filter {
    background-color: #21332b70;
    color: #000;
    padding: 10px 10px 0;
    border-radius: 4px 4px 0 0;
}

.template-search .facets__disclosure-vertical {
    padding: 15px;
}

.template-search .facets-container {
    background-color: #f6f6f6;
    border-radius: 0 0 17px 17px;
    padding-top: 0rem;
}

.template-search summary.facets__summary span {
    font-size: 19px;
    letter-spacing: 0px;
    color: #21332b;
    text-transform: capitalize;
    opacity: 1;
    font-family: Poppins;
}

.template-search  h2#verticalTitle {
    font-size: 25px;
    font-family: Poppins;
    letter-spacing: 0px;
    color: #21332b;
    opacity: 1;
    font-weight: 500;
}

.template-search  .facet-filters.sorting.caption {
    border: .8999999761581421px solid #21332B;
    border-radius: 3px;
    opacity: 1;
    padding: 5px;
}

.template-search .list-menu__item span {
    font-size: 14px;
    font-family: poppins;
    letter-spacing: 0px;
    color: #21332b;
    text-transform: capitalize;
    opacity: .7;
}

.template-search  .card__heading {
    letter-spacing: 0px;
    color: #21332b;
    text-transform: capitalize;
    opacity: 1;
    font-size: 16px;
    font-family: poppins;
}

.template-search  .price {
    font-size: 21px;
    letter-spacing: 0px;
    color: #21332b;
    text-transform: uppercase;
    opacity: 1;
    font-family: poppins;
}

.template-search span.new-badge {
    padding: 0 6px;
    font-size: 16px;
    font-weight: 500;
    font-family: Poppins;
}