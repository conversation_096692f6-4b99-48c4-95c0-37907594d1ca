{"settings_schema": {"colors": {"name": "Couleurs", "settings": {"background": {"label": "Arrière‑plan"}, "background_gradient": {"label": "Dégradé de l’arrière-plan", "info": "Le dégradé de l’arrière-plan remplace l’arrière-plan là où c’est possible."}, "text": {"label": "Texte"}, "button_background": {"label": "Arrière-plan plein du bouton"}, "button_label": {"label": "Texte de bouton plein"}, "secondary_button_label": {"label": "Bouton en relief"}, "shadow": {"label": "Ombre"}}}, "typography": {"name": "Typographie", "settings": {"type_header_font": {"label": "Police", "info": "La sélection d'une police différente peut influencer la vitesse de votre boutique. [En savoir plus sur les polices système.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "Titres"}, "header__2": {"content": "Corps"}, "type_body_font": {"label": "Police", "info": "La sélection d'une police différente peut influencer la vitesse de votre boutique. [En savoir plus sur les polices système.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "heading_scale": {"label": "Échelle de taille de police"}, "body_scale": {"label": "Échelle de taille de police"}}}, "social-media": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"social_twitter_link": {"label": "X", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://vimeo.com/shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Co<PERSON><PERSON> sociaux"}}}, "currency_format": {"name": "Format de devise", "settings": {"content": "Codes de devise", "currency_code_enabled": {"label": "Afficher les codes de devise"}, "paragraph": "Le panier et les prix au moment du paiement indiquent toujours les codes de devise. Exemple : 1 EUR."}}, "layout": {"name": "Mise en page", "settings": {"page_width": {"label": "<PERSON><PERSON> de la page"}, "spacing_sections": {"label": "Espace entre les sections du modèle"}, "header__grid": {"content": "Grille"}, "paragraph__grid": {"content": "Affecte les zones présentant plusieurs colonnes ou lignes."}, "spacing_grid_horizontal": {"label": "Espace horizontal"}, "spacing_grid_vertical": {"label": "Espace vertical"}}}, "search_input": {"name": "Comportement de recherche", "settings": {"header": {"content": "Suggestions de recherche"}, "predictive_search_enabled": {"label": "Activer les suggestions de recherche"}, "predictive_search_show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le fournisseur du produit", "info": "Visible lorsque les suggestions de recherche sont activées."}, "predictive_search_show_price": {"label": "Aff<PERSON><PERSON> le prix du produit", "info": "Visible lorsque les suggestions de recherche sont activées."}}}, "global": {"settings": {"header__border": {"content": "Bordure"}, "header__shadow": {"content": "Ombre"}, "blur": {"label": "<PERSON><PERSON>"}, "corner_radius": {"label": "Rayon de coin"}, "horizontal_offset": {"label": "Décalage horizontal"}, "vertical_offset": {"label": "Décalage vertical"}, "thickness": {"label": "Épaisseur"}, "opacity": {"label": "Opacité"}, "image_padding": {"label": "Marge intérieure de l'image"}, "text_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du texte"}}}, "badges": {"name": "Badges", "settings": {"position": {"options__1": {"label": "En bas à gauche"}, "options__2": {"label": "En bas à droite"}, "options__3": {"label": "En haut à gauche"}, "options__4": {"label": "En haut à droite"}, "label": "Position sur les cartes"}, "sale_badge_color_scheme": {"label": "Nuancier de couleurs du badge de vente"}, "sold_out_badge_color_scheme": {"label": "Nuancier de couleurs du badge de rupture de stock"}}}, "buttons": {"name": "Boutons"}, "variant_pills": {"name": "Boutons pilule de variante", "paragraph": "Les boutons pilule de variante servent à afficher les variantes de produit. [En savoir plus](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"}, "inputs": {"name": "Entrées"}, "content_containers": {"name": "Conteneurs de contenu"}, "popups": {"name": "Menus dé<PERSON>lants et pop-ups", "paragraph": "Affecte les zones telles que les menus déroulants de navigation, les fenêtres modales pop-up et les fenêtres pop-up de panier."}, "media": {"name": "Support multimédia"}, "drawers": {"name": "<PERSON><PERSON><PERSON>"}, "cart": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "Type de panier", "drawer": {"label": "Tiroir"}, "page": {"label": "Page"}, "notification": {"label": "Notification contextuelle"}}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le fournisseur"}, "show_cart_note": {"label": "Activer la note de panier"}, "cart_drawer": {"header": "<PERSON><PERSON>", "collection": {"label": "Collection", "info": "Visible lorsque le panier coulissant est vide."}}}}, "cards": {"name": "Cartes de produit", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Style"}}}, "collection_cards": {"name": "Cartes de collection", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Style"}}}, "blog_cards": {"name": "Cartes de blog", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Style"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "Largeur du logo sur ordinateur", "info": "La largeur du logo est automatiquement optimisée pour les mobiles."}, "favicon": {"label": "Image favicon", "info": "Sera réduite à 32 x 32 px"}}}, "brand_information": {"name": "Informations sur la marque", "settings": {"brand_headline": {"label": "Titre"}, "brand_description": {"label": "Description"}, "brand_image": {"label": "Image"}, "brand_image_width": {"label": "Largeur d’image"}, "paragraph": {"content": "Ajoutez une description de votre marque au pied de page de votre boutique."}}}, "animations": {"name": "Animations", "settings": {"animations_reveal_on_scroll": {"label": "Afficher les sections lors du défilement"}, "animations_hover_elements": {"options__1": {"label": "Aucun"}, "options__2": {"label": "Levée verticale"}, "label": "<PERSON><PERSON><PERSON> de <PERSON>vol", "info": "Concerne les cartes et les boutons.", "options__3": {"label": "3D lift"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Marge de la section", "padding_top": "Marge supérieure", "padding_bottom": "Marge inférieure"}, "spacing": "Espacement", "colors": {"label": "Nuancier de couleurs", "has_cards_info": "Pour modifier le nuancier de couleur de la carte, mettez à jour les paramètres de votre thème."}, "heading_size": {"label": "<PERSON><PERSON> du titre", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}, "options__4": {"label": "Très grand"}}, "image_shape": {"options__1": {"label": "Forme par défaut"}, "options__2": {"label": "<PERSON>e"}, "options__3": {"label": "Indistincte"}, "options__4": {"label": "Chevron gauche"}, "options__5": {"label": "Chevron droite"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "Parallélogramme"}, "options__8": {"label": "Cercle"}, "label": "Forme d’image", "info": "Les cartes de style standard n’ont pas de bordures lorsqu’une forme d’image est active."}, "animation": {"content": "Animations", "image_behavior": {"options__1": {"label": "Aucun"}, "options__2": {"label": "Mouvement ambiant"}, "label": "Comportement de l’image", "options__3": {"label": "Position de l’arrière-plan fixe"}, "options__4": {"label": "Zoom sur le défilement"}}}}, "announcement-bar": {"name": "Barre d'annonces", "blocks": {"announcement": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "Texte"}, "text_alignment": {"label": "Alignement du texte", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "link": {"label": "<PERSON><PERSON>"}}}}, "settings": {"auto_rotate": {"label": "Rotation automatique des annonces"}, "change_slides_speed": {"label": "Changer toutes les"}, "header__1": {"content": "Icônes de réseaux sociaux", "info": "Pour afficher vos comptes de réseaux sociaux, ajoutez les liens correspondants dans les [paramètres de votre thème](/editor?context=theme&category=social%20media)."}, "header__2": {"content": "Annonces"}, "show_social": {"label": "Afficher les icônes sur le bureau"}, "header__3": {"content": "Sélecteur de pays/région", "info": "Pour ajouter un pays/une région, accédez à vos [paramètres de marché.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Activer le sélecteur de pays/région"}, "header__4": {"content": "<PERSON><PERSON><PERSON><PERSON> de langue", "info": "Pour ajouter une langue, accédez à vos [paramètres de langue.](/admin/settings/languages)"}, "enable_language_selector": {"label": "<PERSON><PERSON> le sélecteur de langue"}}, "presets": {"name": "Barre d’annonces"}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "Titre"}, "desktop_layout": {"label": "Mise en page du bureau", "options__1": {"label": "Grand bloc gauche"}, "options__2": {"label": "Grand bloc droite"}}, "mobile_layout": {"label": "Mise en page pour téléphone portable", "options__1": {"label": "Collage"}, "options__2": {"label": "Colonne"}}, "card_styles": {"label": "Style de cartes", "info": "Les styles de cartes produits, de collections et de blogs peuvent être mis à jour dans les paramètres de thème.", "options__1": {"label": "Utiliser des styles de cartes individuels"}, "options__2": {"label": "Style pour toutes en tant que cartes de produits"}}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}}}, "product": {"name": "Produit", "settings": {"product": {"label": "Produit"}, "secondary_background": {"label": "Afficher l'arrière-plan secondaire"}, "second_image": {"label": "Afficher la deuxième image en survol"}}}, "collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}, "video": {"name": "Vidéo", "settings": {"cover_image": {"label": "Image de couverture"}, "video_url": {"label": "URL", "info": "Si la section contient d'autres blocs, la vidéo est lue dans une fenêtre pop-up.", "placeholder": "Utiliser une URL YouTube ou Vimeo"}, "description": {"label": "Texte alternatif de la vidéo", "info": "Décrivez la vidéo pour les clients utilisant des lecteurs d'écran. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Liste des collections", "settings": {"title": {"label": "Titre"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "info": "Pour ajouter des images, modifiez vos collections. [En savoir plus](https://help.shopify.com/manual/products/collections)"}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "show_view_all": {"label": "Activer le bouton « Tout afficher » si la liste comprend plus de collections que celles affichées"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "header_mobile": {"content": "Mise en page sur mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}}, "blocks": {"featured_collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}}, "presets": {"name": "Liste des collections"}}, "contact-form": {"name": "Formulaire de contact", "presets": {"name": "Formulaire de contact"}}, "custom-liquid": {"name": "Liquid personnalisé", "settings": {"custom_liquid": {"label": "Code Liquid", "info": "Ajoutez des extraits d’application ou autre code pour créer des personnalisations avancées. [En savoir plus](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Liquid personnalisé"}}, "featured-blog": {"name": "Articles de blog", "settings": {"heading": {"label": "Titre"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Nombre d'articles de blog à afficher"}, "show_view_all": {"label": "<PERSON>r le bouton « Tout afficher » si le blog comprend plus d'articles que ceux affichés"}, "show_image": {"label": "Afficher les images vedettes", "info": "Pour optimiser vos résultats, utilisez une image ayant un rapport d'aspect de 3:2. [En savoir plus](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "show_date": {"label": "Affiche<PERSON> la date"}, "show_author": {"label": "Afficher l'auteur"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}}, "presets": {"name": "Articles de blog"}}, "featured-collection": {"name": "Collection en vedette", "settings": {"title": {"label": "Titre"}, "collection": {"label": "Collection"}, "products_to_show": {"label": "Quantité maximale de produits à afficher"}, "show_view_all": {"label": "Activer le style « Tout afficher » si la collection comprend plus de produits que ce qui est affiché"}, "header": {"content": "Carte de produit"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le fournisseur"}, "show_rating": {"label": "Afficher la note du produit", "info": "Pour afficher une note, ajoutez une application d'évaluation de produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "<PERSON><PERSON> le bouton d'ajout rapide", "info": "Optimal avec un type de panier pop-up ou à tiroir."}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "description": {"label": "Description"}, "show_description": {"label": "Afficher la description de la collection à partir de l'interface administrateur"}, "description_style": {"label": "Style de la description", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "view_all_style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Bouton en relief"}, "options__3": {"label": "Bouton plein"}, "label": "Style « Tout afficher »"}, "enable_desktop_slider": {"label": "<PERSON><PERSON> le carrousel sur ordinateur"}, "full_width": {"label": "Rendre les produits pleine largeur"}, "header_mobile": {"content": "Mise en page sur mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}}, "presets": {"name": "Collection en vedette"}}, "footer": {"name": "Pied de page", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON>-tête"}, "menu": {"label": "<PERSON><PERSON>", "info": "Affiche uniquement les éléments de menu de niveau supérieur."}}}, "text": {"name": "Texte", "settings": {"heading": {"label": "<PERSON>-tête"}, "subtext": {"label": "Sous-texte"}}}, "brand_information": {"name": "Informations sur la marque", "settings": {"paragraph": {"content": "Ce bloc affichera les informations sur votre marque. [Modifier les informations sur la marque.](/editor?context=theme&category=brand%20information)"}, "header__1": {"content": "Icônes de réseaux sociaux"}, "show_social": {"label": "Afficher les icônes des réseaux sociaux", "info": "Pour afficher vos comptes de médias sociaux, ajoutez les liens correspondants dans les [paramètres de votre thème](/editor?context=theme&category=social%20media)."}}}}, "settings": {"newsletter_enable": {"label": "Afficher l'inscription à la liste de diffusion"}, "newsletter_heading": {"label": "<PERSON>-tête"}, "header__1": {"content": "Inscription à la liste de diffusion", "info": "Abonnés automatiquement ajoutés à votre liste de clients « marketing accepté ». [En savoir plus](https://help.shopify.com/manual/customers/manage-customers)"}, "header__2": {"content": "Icônes de médias sociaux", "info": "Pour afficher vos comptes de médias sociaux, ajoutez les liens correspondants dans les [paramètres de votre thème](/editor?context=theme&category=social%20media)."}, "show_social": {"label": "Afficher les icônes des médias sociaux"}, "header__3": {"content": "Sélecteur de pays/région"}, "header__4": {"info": "Pour ajouter un pays/une région, accédez à vos [paramètres de marché.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Activer le sélecteur de pays/région"}, "header__5": {"content": "<PERSON><PERSON><PERSON><PERSON> de langue"}, "header__6": {"info": "Pour ajouter une langue, allez à vos [paramètres de langue.](/admin/settings/languages)"}, "enable_language_selector": {"label": "<PERSON><PERSON> le sélecteur de langue"}, "header__7": {"content": "Moyens de paiement"}, "payment_enable": {"label": "Afficher les icônes de paiement"}, "margin_top": {"label": "Marge supérieure"}, "header__8": {"content": "Liens des politiques", "info": "Pour ajouter des politiques concernant votre boutique, accédez à vos [paramètres de politique ](/admin/settings/legal)."}, "show_policy": {"label": "Afficher les liens des politiques"}, "header__9": {"content": "Suivre sur Shop", "info": "Pour autoriser les clients à suivre votre boutique sur l’application Shop depuis votre boutique en ligne, Shop Pay doit être activé. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}}}, "header": {"name": "<PERSON>-tête", "settings": {"logo_position": {"label": "Emplacement du logo du bureau", "options__1": {"label": "Centré à gauche"}, "options__2": {"label": "En haut à gauche"}, "options__3": {"label": "En haut au centre"}, "options__4": {"label": "Centré au milieu"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Afficher la ligne de séparation"}, "margin_bottom": {"label": "Marge inférieure"}, "menu_type_desktop": {"label": "Type de menu sur ordinateur", "info": "Le type de menu est automatiquement optimisé pour les mobiles.", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Méga menu"}, "options__3": {"label": "Tiroir"}}, "mobile_layout": {"content": "Mise en page sur mobile"}, "mobile_logo_position": {"label": "Position du logo sur mobile", "options__1": {"label": "Centre"}, "options__2": {"label": "G<PERSON><PERSON>"}}, "logo_help": {"content": "Modifiez votre logo dans les [paramètres du thème](/editor?context=theme&category=logo)."}, "sticky_header_type": {"label": "En‑tête fixe", "options__1": {"label": "Aucun"}, "options__2": {"label": "Lors du défilement vers le haut"}, "options__3": {"label": "Toujours"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>z toujours la taille du logo"}}, "header__3": {"content": "Sélecteur de pays/région"}, "header__4": {"info": "Pour ajouter un pays/une région, accédez à [market settings.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Activer le sélecteur de pays/région"}, "header__5": {"content": "<PERSON><PERSON><PERSON><PERSON> de langue"}, "header__6": {"info": "Pour ajouter une langue, accédez à [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "<PERSON><PERSON> le sélecteur de langue"}, "header__1": {"content": "<PERSON><PERSON><PERSON>"}, "menu_color_scheme": {"label": "Nuancier de couleurs de menu"}}}, "image-banner": {"name": "Bannière avec image", "settings": {"image": {"label": "Première image"}, "image_2": {"label": "Deuxième image"}, "stack_images_on_mobile": {"label": "Empiler des images sur un mobile"}, "show_text_box": {"label": "A<PERSON><PERSON><PERSON> le conteneur sur le bureau"}, "image_overlay_opacity": {"label": "Opacité de la superposition d'images"}, "show_text_below": {"label": "<PERSON><PERSON><PERSON><PERSON> le conteneur sur le mobile"}, "image_height": {"label": "<PERSON><PERSON> de la bannière", "options__1": {"label": "Adapter à la première image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Pour optimiser vos résultats, utilisez une image ayant un rapport d'aspect de 3:2. [En savoir plus](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "Grand"}}, "desktop_content_position": {"options__1": {"label": "En haut à gauche"}, "options__2": {"label": "En haut au centre"}, "options__3": {"label": "En haut à droite"}, "options__4": {"label": "Au milieu à gauche"}, "options__5": {"label": "Centré au milieu"}, "options__6": {"label": "Au milieu à droite"}, "options__7": {"label": "En bas à gauche"}, "options__8": {"label": "En bas au centre"}, "options__9": {"label": "En bas à droite"}, "label": "Position du contenu sur le bureau"}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu du bureau"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur mobile"}, "mobile": {"content": "Mise en page sur mobile"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Description"}, "text_style": {"options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Style de texte"}}}, "buttons": {"name": "Boutons", "settings": {"button_label_1": {"label": "Texte du premier bouton", "info": "Laisser le texte vide pour masquer le bouton."}, "button_link_1": {"label": "Lien du premier bouton"}, "button_style_secondary_1": {"label": "Utiliser le style du bouton en relief"}, "button_label_2": {"label": "Texte du deuxième bouton", "info": "Laisser le texte vide pour masquer le bouton."}, "button_link_2": {"label": "Lien du deuxième bouton"}, "button_style_secondary_2": {"label": "Utiliser le style du bouton en relief"}}}}, "presets": {"name": "Bannière avec image"}}, "image-with-text": {"name": "Image avec texte", "settings": {"image": {"label": "Image"}, "height": {"options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "<PERSON>ur de l'image", "options__4": {"label": "Grand"}}, "layout": {"options__1": {"label": "L'image en premier"}, "options__2": {"label": "Deuxième image"}, "label": "Placement de l'image sur ordinateur", "info": "La mise en page par défaut pour les appareils mobiles est « Image en premier »."}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}, "label": "Largeur d'image sur ordinateur", "info": "L'image est automatiquement optimisée pour les mobiles."}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur ordinateur"}, "desktop_content_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Milieu"}, "options__3": {"label": "Bas"}, "label": "Position du contenu sur ordinateur"}, "content_layout": {"options__1": {"label": "Aucun chevauchement"}, "options__2": {"label": "Chevauchement"}, "label": "Mise en page du contenu"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur mobile"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Contenu"}, "text_style": {"label": "Style de texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}}}}, "button": {"name": "Bouton", "settings": {"button_label": {"label": "Texte du bouton", "info": "Laisser le texte vide pour masquer le bouton."}, "button_link": {"label": "<PERSON>n du bouton"}, "outline_button": {"label": "Utiliser le style de bouton en relief"}}}, "caption": {"name": "Légende", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style de texte", "options__1": {"label": "Sous-titre"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "<PERSON>lle du texte", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}}}}, "presets": {"name": "Image avec texte"}}, "main-article": {"name": "Article de blog", "blocks": {"featured_image": {"name": "Image vedette", "settings": {"image_height": {"label": "<PERSON>ur de l'image vedette", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Pour optimiser vos résultats, utilisez une image ayant un rapport d'aspect de 16:9. [En savoir plus](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "Grand"}}}}, "title": {"name": "Titre", "settings": {"blog_show_date": {"label": "Affiche<PERSON> la date"}, "blog_show_author": {"label": "Afficher l'auteur"}}}, "content": {"name": "Contenu"}, "share": {"name": "Partager", "settings": {"featured_image_info": {"content": "Si vous incluez un lien dans les publications sur les réseaux sociaux, l'image vedette de la page sera affichée comme image d'aperçu. [En savoir plus](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Un titre et une description de la boutique sont inclus avec l'image d'aperçu. [En savoir plus](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Texte"}}}}}, "main-blog": {"name": "Articles de blog", "settings": {"header": {"content": "Carte d'article de blog"}, "show_image": {"label": "Afficher les images vedettes"}, "paragraph": {"content": "Pour changer les extraits, modifiez vos articles de blog. [En savoir plus](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "Affiche<PERSON> la date"}, "show_author": {"label": "Afficher l'auteur"}, "layout": {"label": "Mise en page du bureau", "options__1": {"label": "Grille"}, "options__2": {"label": "Collage"}, "info": "Les publications sont empilées sur mobile."}, "image_height": {"label": "<PERSON>ur de l'image vedette", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}, "info": "Pour optimiser vos résultats, utilisez une image ayant un rapport d'aspect de 3:2. [En savoir plus](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Sous-total", "blocks": {"subtotal": {"name": "Sous-total du prix"}, "buttons": {"name": "Bouton de paiement"}}}, "main-cart-items": {"name": "Articles"}, "main-collection-banner": {"name": "Bannière de collection", "settings": {"paragraph": {"content": "Pour ajouter une description ou une image, modifiez votre collection. [En savoir plus](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Afficher la description de la collection"}, "show_collection_image": {"label": "Afficher l'image de la collection", "info": "Pour optimiser vos résultats, utilisez une image ayant un rapport d'aspect de 16:9. [En savoir plus](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "Grille de produit", "settings": {"products_per_page": {"label": "Produits par page"}, "enable_filtering": {"label": "<PERSON>r le filtrage", "info": "Personnalisez les filtres avec l’application Search & Discovery. [En savoir plus](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "<PERSON><PERSON> le tri"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le fournisseur"}, "header__1": {"content": "Filtrage et tri"}, "header__3": {"content": "Carte de produit"}, "enable_tags": {"label": "<PERSON>r le filtrage", "info": "Personnalisez les filtres avec l’application Search & Discovery. [En savoir plus](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "show_rating": {"label": "Afficher la note du produit", "info": "Pour afficher une note, ajoutez une application d'évaluation de produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "enable_quick_buy": {"label": "<PERSON><PERSON> le bouton d'ajout rapide", "info": "Optimal avec un type de panier pop-up ou à tiroir."}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "header_mobile": {"content": "Mise en page sur mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}, "filter_type": {"label": "Mise en page des filtres sur ordinateur", "options__1": {"label": "Horizontale"}, "options__2": {"label": "Verticale"}, "options__3": {"label": "Tiroir"}, "info": "Le tiroir correspond à la mise en page par défaut sur les appareils mobiles."}}}, "main-list-collections": {"name": "Page de liste des collections", "settings": {"title": {"label": "Titre"}, "sort": {"label": "Trier les collections par :", "options__1": {"label": "Alphabétique, de A à Z"}, "options__2": {"label": "Alphabétique, de Z à A"}, "options__3": {"label": "Date, de la plus récente à la plus ancienne"}, "options__4": {"label": "Date, de la plus ancienne à la plus récente"}, "options__5": {"label": "Nombre de produits, par ordre décroissant"}, "options__6": {"label": "Nombre de produits, par ordre croissant"}}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "info": "Pour ajouter des images, modifiez vos collections. [En savoir plus](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "header_mobile": {"content": "Mise en page sur mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}}}, "main-page": {"name": "Page"}, "main-password-footer": {"name": "Pied de page du mot de passe"}, "main-password-header": {"name": "En-tête du mot de passe", "settings": {"logo_header": {"content": "Logo"}, "logo_help": {"content": "Modifiez votre logo dans les paramètres du thème."}}}, "main-product": {"blocks": {"text": {"name": "Texte", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style de texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "Titre"}, "price": {"name": "Prix"}, "quantity_selector": {"name": "Sélecteur de quantité"}, "variant_picker": {"name": "Sélecteur de variante", "settings": {"picker_type": {"label": "Style", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Comprimés"}}, "swatch_shape": {"label": "Échantillon", "info": "Activez [échantillons](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) sur les options produits.", "options__1": {"label": "Cercle"}, "options__2": {"label": "Carré"}, "options__3": {"label": "Aucun"}}}}, "buy_buttons": {"name": "Boutons d'achat", "settings": {"show_dynamic_checkout": {"label": "Afficher les boutons de paiement dynamique", "info": "En utilisant les méthodes de paiement disponibles sur votre boutique, les clients voient leur option préférée, comme PayPal ou Apple Pay. [En savoir plus](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Afficher le formulaire d’information sur le destinataire pour les cartes‑cadeaux", "info": "Permet aux acheteurs d’envoyer des cartes-cadeaux à une date prévue ainsi qu’un message personnel. [En savoir plus](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "Disponibilité du service de retrait"}, "description": {"name": "Description"}, "share": {"name": "Partager", "settings": {"featured_image_info": {"content": "Si vous incluez un lien dans les publications sur les réseaux sociaux, l'image vedette de la page sera affichée comme image d'aperçu. [En savoir plus](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Un titre et une description de la boutique sont inclus avec l'image d'aperçu. [En savoir plus](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Texte"}}}, "collapsible_tab": {"name": "Rangée réductible", "settings": {"heading": {"info": "Incluez un titre qui explique le contenu.", "label": "Titre"}, "content": {"label": "Contenu de la rangée"}, "page": {"label": "Contenu de la rangée de la page"}, "icon": {"options__1": {"label": "Aucune"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Bouteille"}, "options__5": {"label": "<PERSON><PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON>"}, "options__7": {"label": "Bulle de chat"}, "options__8": {"label": "Coche"}, "options__9": {"label": "Presse-papiers"}, "options__10": {"label": "Produits laitiers"}, "options__11": {"label": "Sans produits laitiers"}, "options__12": {"label": "Sèche-linge"}, "options__13": {"label": "Œil"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Sans gluten"}, "options__16": {"label": "<PERSON><PERSON><PERSON>"}, "options__17": {"label": "Fer"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON><PERSON>"}, "options__20": {"label": "Foudre"}, "options__21": {"label": "Rouge à lèvres"}, "options__22": {"label": "Cadenas"}, "options__23": {"label": "Épingle sur la carte"}, "options__24": {"label": "Sans noix"}, "label": "Icône", "options__25": {"label": "Pantalons"}, "options__26": {"label": "Empreinte"}, "options__27": {"label": "Poivre"}, "options__28": {"label": "Parfum"}, "options__29": {"label": "Avion"}, "options__30": {"label": "Plantes"}, "options__31": {"label": "Étiquette de prix"}, "options__32": {"label": "Point d'interrogation"}, "options__33": {"label": "Recyclage"}, "options__34": {"label": "Retour"}, "options__35": {"label": "<PERSON><PERSON><PERSON>"}, "options__36": {"label": "Plat de service"}, "options__37": {"label": "Chemise"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Flocon de neige"}, "options__41": {"label": "<PERSON><PERSON><PERSON>"}, "options__42": {"label": "Chronomètre"}, "options__43": {"label": "Camion"}, "options__44": {"label": "Lavage"}}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Étiquette de lien"}, "page": {"label": "Page"}}}, "rating": {"name": "Note de produit", "settings": {"paragraph": {"content": "Pour afficher une note, ajoutez une application d'évaluation de produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}, "complementary_products": {"name": "Produits complémentaires", "settings": {"paragraph": {"content": "Pour sélectionner des produits complémentaires, ajoutez l'application Search & Discovery. [En savoir plus](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "<PERSON>-tête"}, "make_collapsible_row": {"label": "Afficher sous forme de ligne réductible"}, "icon": {"info": "Visible lorsque la ligne réductible est affichée."}, "product_list_limit": {"label": "Quantité maximale de produits à afficher"}, "products_per_page": {"label": "Nombre de produits par page"}, "pagination_style": {"label": "Style de pagination", "options": {"option_1": "Points", "option_2": "Compteur", "option_3": "<PERSON><PERSON><PERSON><PERSON>"}}, "product_card": {"heading": "Carte de produit"}, "image_ratio": {"label": "Rapport d’aspect de l’image", "options": {"option_1": "Portrait", "option_2": "Carrés"}}, "enable_quick_add": {"label": "<PERSON><PERSON> le bouton d'ajout rapide"}}}, "icon_with_text": {"name": "Icône avec texte", "settings": {"layout": {"label": "Mise en page", "options__1": {"label": "Horizontale"}, "options__2": {"label": "Verticale"}}, "content": {"label": "Contenu", "info": "Choisissez une icône ou ajoutez une image pour chaque colonne ou rangée."}, "heading": {"info": "Laissez l'intitulé du titre vide pour masquer la colonne de l'icône."}, "icon_1": {"label": "Première icône"}, "image_1": {"label": "Première image"}, "heading_1": {"label": "Premier titre"}, "icon_2": {"label": "Deuxième icône"}, "image_2": {"label": "Deuxième image"}, "heading_2": {"label": "Deuxième titre"}, "icon_3": {"label": "Troisième icône"}, "image_3": {"label": "Troisième image"}, "heading_3": {"label": "Troisième titre"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Style de texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous‑titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "inventory": {"name": "État des stocks", "settings": {"text_style": {"label": "Style de texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous‑titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "inventory_threshold": {"label": "Seuil de stock faible", "info": "Sélectionnez 0 pour toujours indiquer la quantité en stock, si disponible."}, "show_inventory_quantity": {"label": "Afficher l'inventaire"}}}}, "settings": {"header": {"content": "Support multimédia", "info": "En savoir plus sur les [types de supports multimédia.](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "<PERSON><PERSON> le bouclage de la vidéo"}, "enable_sticky_info": {"label": "Activez le contenu de la note flottante sur le bureau"}, "hide_variants": {"label": "Masquer les supports multimédias des autres variantes après la sélection d'une variante"}, "gallery_layout": {"label": "Mise en page du bureau", "options__1": {"label": "Empilé"}, "options__2": {"label": "2 colonnes"}, "options__3": {"label": "Vignettes"}, "options__4": {"label": "Carrousel de vignettes"}}, "media_size": {"label": "Largeur des supports multimédias sur ordinateur", "info": "Les médias sont automatiquement optimisés pour les mobiles.", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}, "mobile_thumbnails": {"label": "Mise en page sur mobile", "options__1": {"label": "2 colonnes"}, "options__2": {"label": "Aff<PERSON><PERSON> les vignettes"}, "options__3": {"label": "Masquer les vignettes"}}, "media_position": {"label": "Position des supports multimédias sur ordinateur", "info": "La position est automatiquement optimisée pour les mobiles.", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_zoom": {"label": "Le zoom sur image", "info": "Cliquez et passez la souris sur les éléments par défaut pour ouvrir lightbox sur mobile.", "options__1": {"label": "Ouvrir lightbox"}, "options__2": {"label": "Cliquer et passer la souris"}, "options__3": {"label": "Pas de zoom"}}, "constrain_to_viewport": {"label": "Limiter les supports multimédias à la hauteur de l’écran"}, "media_fit": {"label": "Taille des supports multimédias", "options__1": {"label": "Taille d<PERSON>origine"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "name": "Informations produits"}, "main-search": {"name": "Résultats de la recherche", "settings": {"image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le fournisseur"}, "header__1": {"content": "Carte de produit"}, "header__2": {"content": "<PERSON>te de <PERSON>", "info": "Les styles de cartes de blog s’appliquent également aux cartes de page dans les résultats de recherche. Pour modifier les styles de cartes, mettez à jour les paramètres de votre thème."}, "article_show_date": {"label": "Affiche<PERSON> la date"}, "article_show_author": {"label": "Afficher l'auteur"}, "show_rating": {"label": "Afficher la note du produit", "info": "Pour afficher une note, ajoutez une application d'évaluation de produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "header_mobile": {"content": "Mise en page sur mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}}}, "multicolumn": {"name": "Multicolonne", "settings": {"title": {"label": "Titre"}, "image_width": {"label": "Largeur d'image", "options__1": {"label": "Un tiers de largeur de la colonne"}, "options__2": {"label": "Demi-<PERSON>ur de colonne"}, "options__3": {"label": "Largeur complè<PERSON> de colonne"}}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Cercle"}}, "column_alignment": {"label": "Alignement de colonne", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}}, "background_style": {"label": "Arrière-plan secondaire", "options__1": {"label": "Aucune"}, "options__2": {"label": "Afficher comme arrière-plan de la colonne"}}, "button_label": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}, "swipe_on_mobile": {"label": "<PERSON><PERSON> le balayage sur mobile"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "header_mobile": {"content": "Mise en page sur mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}}, "blocks": {"column": {"name": "Colonne", "settings": {"image": {"label": "Image"}, "title": {"label": "Titre"}, "text": {"label": "Description"}, "link_label": {"label": "Étiquette de lien"}, "link": {"label": "<PERSON><PERSON>"}}}}, "presets": {"name": "Multicolonne"}}, "newsletter": {"name": "Inscription à la liste de diffusion", "settings": {"full_width": {"label": "Rendre la section pleine largeur"}, "paragraph": {"content": "Chaque abonnement aux e-mails entraîne la création d'un compte client. [En savoir plus](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre"}}}, "paragraph": {"name": "Sous-titre", "settings": {"paragraph": {"label": "Description"}}}, "email_form": {"name": "Formulaire électronique"}}, "presets": {"name": "Inscription à la liste de diffusion"}}, "page": {"name": "Page", "settings": {"page": {"label": "Page"}}, "presets": {"name": "Page"}}, "rich-text": {"name": "Texte enrichi", "settings": {"full_width": {"label": "Rendre la section pleine largeur"}, "desktop_content_position": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Position du contenu sur ordinateur", "info": "La position est automatiquement optimisée pour les mobiles."}, "content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu"}}, "blocks": {"heading": {"name": "Titre", "settings": {"heading": {"label": "Titre"}}}, "text": {"name": "Texte", "settings": {"text": {"label": "Description"}}}, "buttons": {"name": "Boutons", "settings": {"button_label_1": {"label": "Texte du premier bouton", "info": "Laissez le texte vide pour masquer le bouton."}, "button_link_1": {"label": "Lien du premier bouton"}, "button_style_secondary_1": {"label": "Utiliser le style de bouton en relief"}, "button_label_2": {"label": "Texte du deuxième bouton", "info": "Laissez le texte vide pour masquer le bouton."}, "button_link_2": {"label": "Lien du deuxième bouton"}, "button_style_secondary_2": {"label": "Utiliser le style de bouton en relief"}}}, "caption": {"name": "Légende", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style de texte", "options__1": {"label": "Sous‑titre"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "<PERSON>lle du texte", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}}}}}, "presets": {"name": "Texte enrichi"}}, "apps": {"name": "Applications", "settings": {"include_margins": {"label": "Rendre les marges des sections identiques à celles du thème"}}, "presets": {"name": "Applications"}}, "video": {"name": "Vidéo", "settings": {"heading": {"label": "<PERSON>-tête"}, "cover_image": {"label": "Image de couverture"}, "video_url": {"label": "URL", "info": "Utilisez une URL YouTube ou Vimeo"}, "description": {"label": "Texte alternatif de la vidéo", "info": "Décrivez la vidéo pour les clients utilisant des lecteurs d'écran. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Ajouter une marge intérieure à l'image", "info": "Sélectionnez une marge intérieure pour éviter que votre image de couverture soit rognée."}, "full_width": {"label": "Rendre la section pleine largeur"}, "video": {"label": "Vidéo"}, "enable_video_looping": {"label": "Lire la vidéo en boucle"}, "header__1": {"content": "Vidéo hébergée par Shopify"}, "header__2": {"content": "Ou vidéo intégrée à partir d’une URL"}, "header__3": {"content": "Style"}, "paragraph": {"content": "<PERSON><PERSON> quand aucune vidéo hébergée par Shopify n’est sélectionnée."}}, "presets": {"name": "Vidéo"}}, "featured-product": {"name": "Produit en vedette", "blocks": {"text": {"name": "Texte", "settings": {"text": {"label": "Texte"}, "text_style": {"label": "Style de texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "Titre"}, "price": {"name": "Prix"}, "quantity_selector": {"name": "Sélecteur de quantité"}, "variant_picker": {"name": "Sélecteur de variante", "settings": {"picker_type": {"label": "Style", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "swatch_shape": {"label": "Échantillon", "info": "Activez [échantillons](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) sur les options produits.", "options__1": {"label": "Cercle"}, "options__2": {"label": "Carré"}, "options__3": {"label": "Aucun"}}}}, "buy_buttons": {"name": "Boutons d'achat", "settings": {"show_dynamic_checkout": {"label": "Afficher les boutons de paiement dynamique", "info": "En utilisant les moyens de paiement disponibles sur votre boutique, les clients voient leur option préférée, comme PayPal ou Apple Pay. [En savoir plus](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Description"}, "share": {"name": "Partager", "settings": {"featured_image_info": {"content": "Si vous incluez un lien dans des publications sur les médias sociaux, l'image vedette de la page sera affichée comme image d'aperçu. [En savoir plus](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Un titre et une description de la boutique sont inclus avec l'image d'aperçu. [En savoir plus](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Texte"}}}, "rating": {"name": "Évaluation de produit", "settings": {"paragraph": {"content": "Pour afficher une évaluation, ajoutez une application d'évaluation de produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Style de texte", "options__1": {"label": "Corps"}, "options__2": {"label": "Sous‑titre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "settings": {"product": {"label": "Produit"}, "secondary_background": {"label": "Afficher l'arrière-plan secondaire"}, "header": {"content": "Support multimédia", "info": "En savoir plus sur les [types de supports multimédias](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "<PERSON>r la vidéo en boucle"}, "hide_variants": {"label": "Masquer les médias des variantes non sélectionnées sur le bureau"}, "media_position": {"label": "Position des supports multimédias sur ordinateur", "info": "La position est automatiquement optimisée pour les mobiles.", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "presets": {"name": "Produit en vedette"}}, "email-signup-banner": {"name": "Bannière d'inscription à la liste de diffusion", "settings": {"paragraph": {"content": "Chaque abonnement par e-mail entraîne la création d'un compte client. [En savoir plus](https://help.shopify.com/manual/customers)"}, "image": {"label": "Image de fond"}, "show_background_image": {"label": "Afficher l'image de fond"}, "show_text_box": {"label": "A<PERSON><PERSON><PERSON> le conteneur sur le bureau"}, "image_overlay_opacity": {"label": "Opacité de la superposition d'images"}, "show_text_below": {"label": "Afficher le contenu sous l'image sur le mobile", "info": "Pour optimiser vos résultats, utilisez une image ayant un rapport d'aspect de 16:9. [En savoir plus](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "image_height": {"label": "<PERSON><PERSON> de la bannière", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}, "info": "Pour optimiser vos résultats, utilisez une image ayant un rapport d'aspect de 16:9. [En savoir plus](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__4": {"label": "Au milieu à gauche"}, "options__5": {"label": "Centré au milieu"}, "options__6": {"label": "Au milieu à droite"}, "options__7": {"label": "En bas à gauche"}, "options__8": {"label": "En bas au centre"}, "options__9": {"label": "En bas à droite"}, "options__1": {"label": "En haut à gauche"}, "options__2": {"label": "En haut au centre"}, "options__3": {"label": "En haut à droite"}, "label": "Position du contenu sur le bureau"}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu du bureau"}, "header": {"content": "Mise en page mobile"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur mobile"}, "color_scheme": {"info": "Visible lorsque le conteneur est affiché."}}, "blocks": {"heading": {"name": "<PERSON>-tête", "settings": {"heading": {"label": "<PERSON>-tête"}}}, "paragraph": {"name": "Paragraphe", "settings": {"paragraph": {"label": "Description"}, "text_style": {"options__1": {"label": "Corps"}, "options__2": {"label": "Sous-titre"}, "label": "Style de texte"}}}, "email_form": {"name": "Formulaire par e-mail"}}, "presets": {"name": "Bannière d'inscription à la liste de diffusion"}}, "slideshow": {"name": "Diaporama", "settings": {"layout": {"label": "Mise en page", "options__1": {"label": "<PERSON><PERSON>e largeur"}, "options__2": {"label": "Grille"}}, "slide_height": {"label": "<PERSON>ur de diapositive", "options__1": {"label": "Adapter à la première image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}}, "slider_visual": {"label": "Style de pagination", "options__1": {"label": "Compteur"}, "options__2": {"label": "Points"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "auto_rotate": {"label": "Rotation automatique des diapositives"}, "change_slides_speed": {"label": "Changer de diapositive toutes les"}, "show_text_below": {"label": "Afficher le contenu sous les images sur le mobile"}, "mobile": {"content": "Mise en page sur mobile"}, "accessibility": {"content": "Accessibilité", "label": "Description du diaporama", "info": "Décrivez le diaporama pour les clients utilisant des lecteurs d'écran."}}, "blocks": {"slide": {"name": "Diapositive", "settings": {"image": {"label": "Image"}, "heading": {"label": "<PERSON>-tête"}, "subheading": {"label": "Sous-titre"}, "button_label": {"label": "Texte du bouton", "info": "Laissez le texte vide pour masquer le bouton."}, "link": {"label": "<PERSON>n du bouton"}, "secondary_style": {"label": "Utiliser le style de bouton en relief"}, "box_align": {"label": "Position du contenu sur ordinateur", "options__1": {"label": "En haut à gauche"}, "options__2": {"label": "En haut au centre"}, "options__3": {"label": "En haut à droite"}, "options__4": {"label": "Centré à gauche"}, "options__5": {"label": "Centré au milieu"}, "options__6": {"label": "Au milieu à droite"}, "options__7": {"label": "En bas à gauche"}, "options__8": {"label": "En bas au centre"}, "options__9": {"label": "En bas à droite"}, "info": "L'emplacement est optimisé pour le mobile automatiquement."}, "show_text_box": {"label": "<PERSON><PERSON><PERSON><PERSON> le conteneur sur ordinateur"}, "text_alignment": {"label": "Alignement du contenu sur ordinateur", "option_1": {"label": "G<PERSON><PERSON>"}, "option_2": {"label": "Centre"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Opacité de la superposition d'images"}, "text_alignment_mobile": {"label": "Alignement du contenu sur mobile", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Diaporama"}}, "collapsible_content": {"name": "Contenu réductible", "settings": {"caption": {"label": "Légende"}, "heading": {"label": "Titre"}, "heading_alignment": {"label": "Alignement des titres", "options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "layout": {"label": "Mise en page", "options__1": {"label": "Aucun contenant"}, "options__2": {"label": "Conteneur de <PERSON>"}, "options__3": {"label": "Conteneur de section"}}, "container_color_scheme": {"label": "Nuancier de couleurs du conteneur", "info": "Visible lorsque la Mise en page est définie comme conteneur de Ligne ou de Section."}, "open_first_collapsible_row": {"label": "Ou<PERSON><PERSON>r d'abord la rangée réductible"}, "header": {"content": "Mise en page des images"}, "image": {"label": "Image"}, "image_ratio": {"label": "Rapport d'image", "options__1": {"label": "Adapter à l'image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "Grand"}}, "desktop_layout": {"label": "Mise en page du bureau", "options__1": {"label": "L'image en premier"}, "options__2": {"label": "L'image en deuxième"}, "info": "L'image apparaît toujours en premier sur un appareil mobile."}}, "blocks": {"collapsible_row": {"name": "Rangée réductible", "settings": {"heading": {"info": "Incluez un titre qui explique le contenu.", "label": "Titre"}, "row_content": {"label": "Contenu de la rangée"}, "page": {"label": "Contenu de la rangée de la page"}, "icon": {"label": "Icône", "options__1": {"label": "Aucun"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Bouteille"}, "options__5": {"label": "<PERSON><PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON>"}, "options__7": {"label": "Bulle de chat"}, "options__8": {"label": "Coche"}, "options__9": {"label": "Presse-papiers"}, "options__10": {"label": "Produits laitiers"}, "options__11": {"label": "Sans produits laitiers"}, "options__12": {"label": "Sèche-linge"}, "options__13": {"label": "Œil"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Sans gluten"}, "options__16": {"label": "<PERSON><PERSON><PERSON>"}, "options__17": {"label": "Fer"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON><PERSON>"}, "options__20": {"label": "Foudre"}, "options__21": {"label": "Rouge à lèvres"}, "options__22": {"label": "Cadenas"}, "options__23": {"label": "Épingle sur la carte"}, "options__24": {"label": "Sans noix"}, "options__25": {"label": "Pantalons"}, "options__26": {"label": "Empreinte"}, "options__27": {"label": "Poivre"}, "options__28": {"label": "Parfum"}, "options__29": {"label": "Avion"}, "options__30": {"label": "Plantes"}, "options__31": {"label": "Étiquette de prix"}, "options__32": {"label": "Point d'interrogation"}, "options__33": {"label": "Recyclage"}, "options__34": {"label": "Retour"}, "options__35": {"label": "<PERSON><PERSON><PERSON>"}, "options__36": {"label": "Plat de service"}, "options__37": {"label": "Chemise"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Flocon de neige"}, "options__41": {"label": "<PERSON><PERSON><PERSON>"}, "options__42": {"label": "Chronomètre"}, "options__43": {"label": "Camion"}, "options__44": {"label": "Lavage"}}}}}, "presets": {"name": "Contenu réductible"}}, "main-account": {"name": "<PERSON><PERSON><PERSON>"}, "main-activate-account": {"name": "Activation du compte"}, "main-addresses": {"name": "Adresses"}, "main-login": {"name": "Se connecter"}, "main-order": {"name": "Commande"}, "main-register": {"name": "Inscription"}, "main-reset-password": {"name": "Réinitialisation du mot de passe"}, "related-products": {"name": "Produits associés", "settings": {"heading": {"label": "<PERSON>-tête"}, "products_to_show": {"label": "Quantité maximale de produits à afficher"}, "columns_desktop": {"label": "Nombre de colonnes sur ordinateur"}, "paragraph__1": {"content": "Les recommandations dynamiques utilisent les informations relatives aux commandes et aux produits pour changer et s’améliorer au fil du temps. [En savoir plus](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "Carte de produit"}, "image_ratio": {"label": "Rapport d’aspect de l’image", "options__1": {"label": "Adapter à l’image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Carré"}}, "show_secondary_image": {"label": "Afficher la deuxième image en survol"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON> le fournisseur"}, "show_rating": {"label": "Afficher la note du produit", "info": "Pour afficher une note, ajoutez une application d’évaluation des produits. [En savoir plus](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "header_mobile": {"content": "Mise en page sur mobile"}, "columns_mobile": {"label": "Nombre de colonnes sur mobile", "options__1": {"label": "1 colonne"}, "options__2": {"label": "2 colonnes"}}}}, "multirow": {"name": "Multiligne", "settings": {"image": {"label": "Image"}, "image_height": {"options__1": {"label": "Adapter à l’image"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Grand"}, "label": "Hauteur de l’image"}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}, "label": "Largeur de l’image sur ordinateur", "info": "L’image est automatiquement optimisée pour les mobiles."}, "heading_size": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grand"}, "label": "<PERSON><PERSON> du titre"}, "text_style": {"options__1": {"label": "Corps"}, "options__2": {"label": "Sous‑titre"}, "label": "Style de texte"}, "button_style": {"options__1": {"label": "Bouton plein"}, "options__2": {"label": "Bouton en relief"}, "label": "Style de bouton"}, "desktop_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur ordinateur"}, "desktop_content_position": {"options__1": {"label": "En haut"}, "options__2": {"label": "Au milieu"}, "options__3": {"label": "En bas"}, "label": "Position du contenu sur ordinateur", "info": "La position est automatiquement optimisée pour les mobiles."}, "image_layout": {"options__1": {"label": "Alterner depuis la gauche"}, "options__2": {"label": "Alterner depuis la droite"}, "options__3": {"label": "<PERSON><PERSON><PERSON> à gauche"}, "options__4": {"label": "<PERSON><PERSON><PERSON> d<PERSON>"}, "label": "Placement de l’image sur ordinateur", "info": "Le placement est automatiquement optimisé pour les mobiles."}, "container_color_scheme": {"label": "Nuancier de couleurs du conteneur"}, "mobile_content_alignment": {"options__1": {"label": "G<PERSON><PERSON>"}, "options__2": {"label": "Centre"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Alignement du contenu sur mobile"}, "header_mobile": {"content": "Mise en page sur mobile"}}, "blocks": {"row": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "Image"}, "caption": {"label": "Légende"}, "heading": {"label": "<PERSON>-tête"}, "text": {"label": "Texte"}, "button_label": {"label": "Texte du bouton"}, "button_link": {"label": "<PERSON>n du bouton"}}}}, "presets": {"name": "Multiligne"}}, "quick-order-list": {"name": "Liste rapide des commandes", "settings": {"show_image": {"label": "Affiche<PERSON> les images"}, "show_sku": {"label": "Afficher les SKU"}}, "presets": {"name": "Liste rapide des commandes"}}}}