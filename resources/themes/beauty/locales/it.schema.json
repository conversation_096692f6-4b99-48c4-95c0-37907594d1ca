{"settings_schema": {"colors": {"name": "Colori", "settings": {"background": {"label": "Sfondo"}, "background_gradient": {"label": "Gradiente sfondo", "info": "Il gradiente sfondo sostituisce lo sfondo ove possibile."}, "text": {"label": "<PERSON><PERSON>"}, "button_background": {"label": "Sfondo pulsante in tinta unita"}, "button_label": {"label": "Etichetta pulsante in tinta unita"}, "secondary_button_label": {"label": "Contorno pulsante"}, "shadow": {"label": "Ombra"}}}, "typography": {"name": "Caratteri tipografici", "settings": {"type_header_font": {"label": "Font", "info": "La selezione di un font diverso può influenzare la velocità del tuo negozio. [Maggiori informazioni sui font di sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "<PERSON><PERSON>"}, "header__2": {"content": "<PERSON><PERSON>"}, "type_body_font": {"label": "Font", "info": "La selezione di un font diverso può influenzare la velocità del tuo negozio. [Maggiori informazioni sui font di sistema.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "heading_scale": {"label": "Scalabilità dimensione font"}, "body_scale": {"label": "Scalabilità dimensione font"}}}, "social-media": {"name": "Social media", "settings": {"social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Account social"}}}, "currency_format": {"name": "Formato valuta", "settings": {"content": "Codici valuta", "currency_code_enabled": {"label": "Mostra i codici valuta"}, "paragraph": "I prezzi del carrello e del check-out mostrano sempre i codici valuta. Esempio: 1,00 EUR."}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "Larghezza pagina"}, "spacing_sections": {"label": "Spazio tra le sezioni del modello"}, "header__grid": {"content": "Griglia"}, "paragraph__grid": {"content": "Incide sulle aree con colonne o righe multiple."}, "spacing_grid_horizontal": {"label": "Spazio orizzontale"}, "spacing_grid_vertical": {"label": "Spazio verticale"}}}, "search_input": {"name": "Comportamento di ricerca", "settings": {"header": {"content": "Suggerimenti di ricerca"}, "predictive_search_enabled": {"label": "Abilita suggerimenti di ricerca"}, "predictive_search_show_vendor": {"label": "<PERSON>ra fornitore prodotto", "info": "Visibile quando i suggerimenti di ricerca sono abilitati."}, "predictive_search_show_price": {"label": "Mostra prezzo prodotto", "info": "Visibile quando i suggerimenti di ricerca sono abilitati."}}}, "global": {"settings": {"header__border": {"content": "<PERSON><PERSON>"}, "header__shadow": {"content": "Ombra"}, "blur": {"label": "Sfocatura"}, "corner_radius": {"label": "Raggio angolo"}, "horizontal_offset": {"label": "Scostamento orizzontale"}, "vertical_offset": {"label": "Scostamento verticale"}, "thickness": {"label": "Spessore"}, "opacity": {"label": "Opacità"}, "image_padding": {"label": "Spaziatura immagine"}, "text_alignment": {"options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}, "label": "Allineamento testo"}}}, "badges": {"name": "Badge", "settings": {"position": {"options__1": {"label": "In basso a sinistra"}, "options__2": {"label": "In basso a destra"}, "options__3": {"label": "In alto a sinistra"}, "options__4": {"label": "In alto a destra"}, "label": "Posizione sulle schede"}, "sale_badge_color_scheme": {"label": "Schema colori per badge vendita"}, "sold_out_badge_color_scheme": {"label": "Schema colori per badge esaurito"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "variant_pills": {"name": "Pulsanti di selezione variante", "paragraph": "I pulsanti di selezione variante costituiscono uno dei modi in cui puoi visualizzare le varianti di prodotto. [Maggiori informazioni](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types#variant-picker-block)"}, "inputs": {"name": "Input"}, "content_containers": {"name": "Contenitori contenuto"}, "popups": {"name": "Menu a discesa e pop up", "paragraph": "Incide su aree come menu di navigazione a discesa, modali pop up e pop up di carrello."}, "media": {"name": "Contenuti multimediali"}, "drawers": {"name": "Finestre"}, "cart": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "Tipo di carrello", "drawer": {"label": "Finestra"}, "page": {"label": "<PERSON><PERSON><PERSON>"}, "notification": {"label": "Notifica pop up"}}, "show_vendor": {"label": "<PERSON>ra fornitore"}, "show_cart_note": {"label": "Abilita nota carrello"}, "cart_drawer": {"header": "Finestra del carrello", "collection": {"label": "Collezione", "info": "Visibile quando la finestra del carrello è vuota."}}}}, "cards": {"name": "<PERSON><PERSON><PERSON> prodotto", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "Scheda"}, "label": "Stile"}}}, "collection_cards": {"name": "Schede collezioni", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "Scheda"}, "label": "Stile"}}}, "blog_cards": {"name": "Schede del blog", "settings": {"style": {"options__1": {"label": "Standard"}, "options__2": {"label": "Scheda"}, "label": "Stile"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "Larghezza logo su desktop", "info": "Larghezza del logo automaticamente ottimizzata per i dispositivi mobili."}, "favicon": {"label": "Imma<PERSON>e favicon", "info": "Verrà ridimensionata a 32 x 32 pixel"}}}, "brand_information": {"name": "Informazioni sul brand", "settings": {"brand_headline": {"label": "<PERSON><PERSON>"}, "brand_description": {"label": "Descrizione"}, "brand_image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "brand_image_width": {"label": "<PERSON><PERSON><PERSON><PERSON> immagine"}, "paragraph": {"content": "Aggiungi una descrizione del tuo brand al footer del negozio."}}}, "animations": {"name": "Animazioni", "settings": {"animations_reveal_on_scroll": {"label": "Mostra sezioni durante lo scorrimento"}, "animations_hover_elements": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Vertical lift"}, "label": "E<PERSON>tto hover", "info": "<PERSON><PERSON><PERSON>a schede e pulsanti.", "options__3": {"label": "3D lift"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "Spaziatura sezione", "padding_top": "Spaziatura superiore", "padding_bottom": "Spaziatura inferiore"}, "spacing": "Spaziatura", "colors": {"label": "Schema colori", "has_cards_info": "Per modificare lo schema dei colori delle schede, aggiorna le impostazioni del tema."}, "heading_size": {"label": "Dimensione titolo", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Media"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Extra large"}}, "image_shape": {"options__1": {"label": "Predefinito"}, "options__2": {"label": "Arco"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "Parentesi ad angolo a sinistra"}, "options__5": {"label": "Parentesi ad angolo a destra"}, "options__6": {"label": "Diamante"}, "options__7": {"label": "Parallelogram<PERSON>"}, "options__8": {"label": "Cerchio"}, "label": "Forma immagine", "info": "Le schede con stile Standard e una Forma immagine selezionata non hanno bordi."}, "animation": {"content": "Animazioni", "image_behavior": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Scorrimento lento"}, "label": "Comportamento delle immagini", "options__3": {"label": "Posizione dello sfondo fissa"}, "options__4": {"label": "Zoom avanti durante lo scorrimento"}}}}, "announcement-bar": {"name": "Barra degli annunci", "blocks": {"announcement": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_alignment": {"label": "Allineamento testo", "options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}}, "link": {"label": "Link"}}}}, "settings": {"auto_rotate": {"label": "Ruota annunci automaticamente"}, "change_slides_speed": {"label": "Cambio"}, "header__1": {"content": "Icone dei social media", "info": "Per mostrare i tuoi account sui social media, inseriscine i link nelle [impostazioni tema](/editor?context=theme&category=social%20media)."}, "header__2": {"content": "<PERSON><PERSON><PERSON>"}, "show_social": {"label": "Mostra icone sul desktop"}, "header__3": {"content": "Selettore paese/area geografica", "info": "Per aggiungere un paese/un'area geografica, vai alle [impostazioni di mercato.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Abilita selettore paese/area geografica"}, "header__4": {"content": "Selettore lingua", "info": "Per aggiungere una lingua, vai alle [impostazioni lingua.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Abilita selettore lingua"}}, "presets": {"name": "Barra degli annunci"}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "<PERSON><PERSON>"}, "desktop_layout": {"label": "Layout desktop", "options__1": {"label": "Blocco grande sinistro"}, "options__2": {"label": "Blocco grande destro"}}, "mobile_layout": {"label": "Layout dispositivo mobile", "options__1": {"label": "Collage"}, "options__2": {"label": "<PERSON>onna"}}, "card_styles": {"label": "<PERSON>ile scheda", "info": "È possibile aggiornare gli stili delle schede dei blog, delle collezioni e dei prodotti.", "options__1": {"label": "Utilizza stili delle schede personali"}, "options__2": {"label": "Assimila tutti gli stili a quelli delle schede di prodotto"}}}, "blocks": {"image": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "product": {"name": "<PERSON><PERSON><PERSON>", "settings": {"product": {"label": "<PERSON><PERSON><PERSON>"}, "secondary_background": {"label": "Mostra sfondo secondario"}, "second_image": {"label": "Mostra la seconda immagine al passaggio del mouse"}}}, "collection": {"name": "Collezione", "settings": {"collection": {"label": "Collezione"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Immagine di copertina"}, "video_url": {"label": "URL", "info": "Se la sezione contiene altri blocchi, il video viene riprodotto in un pop-up.", "placeholder": "Utilizza un URL YouTube o Vimeo"}, "description": {"label": "Testo alternativo del video", "info": "Descrivi il video per i clienti che utilizzano i lettori di schermo. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Elenco delle collezioni", "settings": {"title": {"label": "<PERSON><PERSON>"}, "image_ratio": {"label": "Proporzioni delle immagini", "options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Quadrate"}, "info": "Aggiungi immagini modificando le collezioni. [Maggiori informazioni](https://help.shopify.com/manual/products/collections)"}, "swipe_on_mobile": {"label": "Abilita scorrimento su dispositivo mobile"}, "show_view_all": {"label": "Abilita il pulsante \"Visualizza tutto\" se l'elenco include più collezioni di quelle mostrate"}, "columns_desktop": {"label": "Numero di colonne su desktop"}, "header_mobile": {"content": "Layout dispositivo mobile"}, "columns_mobile": {"label": "Numero di colonne su mobile", "options__1": {"label": "1 colonna"}, "options__2": {"label": "2 colonne"}}}, "blocks": {"featured_collection": {"name": "Collezione", "settings": {"collection": {"label": "Collezione"}}}}, "presets": {"name": "Elenco delle collezioni"}}, "contact-form": {"name": "Modulo di contatto", "presets": {"name": "Modulo di contatto"}}, "custom-liquid": {"name": "Liquid personalizzato", "settings": {"custom_liquid": {"label": "Codice Liquid", "info": "Aggiungi snippet di app o altro codice Liquid per creare personalizzazioni avanzate. [Maggiori informazioni](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Liquid personalizzato"}}, "featured-blog": {"name": "Articoli del blog", "settings": {"heading": {"label": "<PERSON><PERSON>"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Numero di articoli del blog da mostrare"}, "show_view_all": {"label": "Abilita il pulsante \"Visualizza tutto\" se il blog include più articoli di quelli mostrati"}, "show_image": {"label": "Mostra immagine in evidenza", "info": "Per un risultato ottimale, utilizza un'immagine con proporzioni 3:2. [Maggiori informazioni](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "show_date": {"label": "Mostra data"}, "show_author": {"label": "Mostra autore"}, "columns_desktop": {"label": "Numero di colonne su desktop"}}, "presets": {"name": "Articoli del blog"}}, "featured-collection": {"name": "Collezione in evidenza", "settings": {"title": {"label": "<PERSON><PERSON>"}, "collection": {"label": "Collezione"}, "products_to_show": {"label": "<PERSON><PERSON> di prodotti da mostrare"}, "show_view_all": {"label": "Abilita \"Visualizza tutto\" se la collezione include più prodotti di quelli mostrati"}, "header": {"content": "<PERSON><PERSON><PERSON> prodotto"}, "image_ratio": {"label": "Proporzioni delle immagini", "options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Quadrate"}}, "show_secondary_image": {"label": "Mostra la seconda immagine al passaggio del mouse"}, "show_vendor": {"label": "<PERSON>ra fornitore"}, "show_rating": {"label": "Mostra valutazione del prodotto", "info": "Per mostrare una valutazione, aggiungi un'app di valutazione del prodotto. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "Abilita il pulsante di aggiunta rapida", "info": "L'ideale in caso di pop up o di tipo di carrello a finestra."}, "columns_desktop": {"label": "Numero di colonne su desktop"}, "description": {"label": "Descrizione"}, "show_description": {"label": "Mostra descrizione collezione dal pannello di controllo Shopify"}, "description_style": {"label": "Stile descrizione", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "view_all_style": {"options__1": {"label": "Link"}, "options__2": {"label": "Contorno pulsante"}, "options__3": {"label": "Pulsante in tinta unita"}, "label": "<PERSON>ile \"<PERSON><PERSON>za tutto\""}, "enable_desktop_slider": {"label": "<PERSON><PERSON>i carosello su <PERSON>"}, "full_width": {"label": "Rendi prodotti a larghezza intera"}, "header_mobile": {"content": "Layout dispositivo mobile"}, "columns_mobile": {"label": "Numero di colonne su mobile", "options__1": {"label": "1 colonna"}, "options__2": {"label": "2 colonne"}}, "swipe_on_mobile": {"label": "Abilita scorrimento su dispositivo mobile"}}, "presets": {"name": "Collezione in evidenza"}}, "footer": {"name": "Footer", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON>"}, "menu": {"label": "<PERSON><PERSON>", "info": "Mostra solo le voci di menu di livello superiore."}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON>"}, "subtext": {"label": "Sottotesto"}}}, "brand_information": {"name": "Informazioni sul brand", "settings": {"paragraph": {"content": "Questo blocco mostrerà le informazioni sul tuo brand. [Modifica le informazioni sul brand.](/editor?context=theme&category=brand%20information)"}, "header__1": {"content": "Icone dei social media"}, "show_social": {"label": "Mostra icone dei social media", "info": "Per mostrare i tuoi account sui social media, inseriscine i link nelle [impostazioni tema](/editor?context=theme&category=social%20media)."}}}}, "settings": {"newsletter_enable": {"label": "Mostra iscrizione alla newsletter"}, "newsletter_heading": {"label": "<PERSON><PERSON>"}, "header__1": {"content": "Iscrizione alla newsletter", "info": "Iscritti aggiunti automaticamente all'elenco dei clienti che \"hanno accettato le comunicazioni di marketing\". [Maggiori informazioni](https://help.shopify.com/manual/customers/manage-customers)"}, "header__2": {"content": "Icone dei social media", "info": "Per mostrare i tuoi account sui social media, inseriscine i link nelle [impostazioni tema](/editor?context=theme&category=social%20media)."}, "show_social": {"label": "Mostra icone dei social media"}, "header__3": {"content": "Selettore paese/area geografica"}, "header__4": {"info": "Per aggiungere un paese/un'area geografica, vai alle [impostazioni di mercato.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Abilita selettore paese/area geografica"}, "header__5": {"content": "Selettore lingua"}, "header__6": {"info": "Per aggiungere una lingua, vai alle [impostazioni lingua.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Abilita selettore lingua"}, "header__7": {"content": "Metodi di pagamento"}, "payment_enable": {"label": "Mostra le icone di pagamento"}, "margin_top": {"label": "Margine superiore"}, "header__8": {"content": "Link alle informative", "info": "Per aggiungere le informative del negozio, vai alle [impostazioni delle informative](/admin/settings/legal)."}, "show_policy": {"label": "Mostra link alle informative"}, "header__9": {"content": "<PERSON><PERSON><PERSON>", "info": "Shop Pay deve essere abilitato per consentire ai clienti di seguire il tuo negozio sull'app Shop dalla tua vetrina virtuale. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "<PERSON><PERSON><PERSON>"}}}, "header": {"name": "Header", "settings": {"logo_position": {"label": "Posizione logo su desktop", "options__1": {"label": "In mezzo a sinistra"}, "options__2": {"label": "In alto a sinistra"}, "options__3": {"label": "In alto al centro"}, "options__4": {"label": "In mezzo al centro"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Mostra linea di separazione"}, "margin_bottom": {"label": "Margine inferiore"}, "menu_type_desktop": {"label": "Tipo menu desktop", "info": "Tipo menu automaticamente ottimizzato per dispositivi mobili.", "options__1": {"label": "Menu a discesa"}, "options__2": {"label": "Mega menu"}, "options__3": {"label": "Finestra"}}, "mobile_layout": {"content": "Layout dispositivo mobile"}, "mobile_logo_position": {"label": "Posizione del logo mobile", "options__1": {"label": "Al centro"}, "options__2": {"label": "A sinistra"}}, "logo_help": {"content": "Modifica il tuo logo nelle [impostazioni tema](/editor?context=theme&category=logo)."}, "sticky_header_type": {"label": "Header fisso", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Durante lo scorrimento verso l'alto"}, "options__3": {"label": "Sempre"}, "options__4": {"label": "Sempre, riduci dimensione logo"}}, "header__3": {"content": "Selettore paese/area geografica"}, "header__4": {"info": "Per aggiungere un paese/un'area geografica, vai alle [impostazioni di mercato.](/admin/settings/markets)"}, "enable_country_selector": {"label": "Abilita selettore paese/area geografica"}, "header__5": {"content": "Selettore lingua"}, "header__6": {"info": "Per aggiungere una lingua, vai alle [impostazioni lingua.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Abilita selettore lingua"}, "header__1": {"content": "Colore"}, "menu_color_scheme": {"label": "Schema di colori del menu"}}}, "image-banner": {"name": "Immagine banner", "settings": {"image": {"label": "Prima immagine"}, "image_2": {"label": "Seconda immagine"}, "stack_images_on_mobile": {"label": "Elenca immagini su dispositivo mobile"}, "show_text_box": {"label": "Mostra contenitore sul desktop"}, "image_overlay_opacity": {"label": "Opacità della sovrapposizione immagine"}, "show_text_below": {"label": "Mostra contenitore su dispositivo mobile"}, "image_height": {"label": "Altezza banner", "options__1": {"label": "Adatta alla prima immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Media"}, "info": "Per un risultato ottimale, utilizza un'immagine con proporzioni 3:2. [Maggiori informazioni](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "In alto a sinistra"}, "options__2": {"label": "In alto al centro"}, "options__3": {"label": "In alto a destra"}, "options__4": {"label": "In mezzo a sinistra"}, "options__5": {"label": "In mezzo al centro"}, "options__6": {"label": "In mezzo a destra"}, "options__7": {"label": "In basso a sinistra"}, "options__8": {"label": "In basso al centro"}, "options__9": {"label": "In basso a destra"}, "label": "Posizione contenuto su desktop"}, "desktop_content_alignment": {"options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}, "label": "Allineamento contenuto su desktop"}, "mobile_content_alignment": {"options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}, "label": "Allineamento contenuto su dispositivi mobili"}, "mobile": {"content": "Layout dispositivo mobile"}}, "blocks": {"heading": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON>"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "Descrizione"}, "text_style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "label": "Stile del testo"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "Prima etichetta pulsante", "info": "<PERSON>cia vuota l'etichetta per nascondere il pulsante."}, "button_link_1": {"label": "Primo link pulsante"}, "button_style_secondary_1": {"label": "Utilizza stile contorno pulsante"}, "button_label_2": {"label": "Seconda etichetta pulsante", "info": "<PERSON>cia vuota l'etichetta per nascondere il pulsante."}, "button_link_2": {"label": "Secondo link pulsante"}, "button_style_secondary_2": {"label": "Utilizza stile contorno pulsante"}}}}, "presets": {"name": "Immagine banner"}}, "image-with-text": {"name": "Immagine con testo", "settings": {"image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "height": {"options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Media"}, "label": "Altezza immagine", "options__4": {"label": "Grande"}}, "layout": {"options__1": {"label": "Prima l'immagine"}, "options__2": {"label": "Seconda immagine"}, "label": "Posizionamento immagine su desktop", "info": "Il layout predefinito per dispositivi mobili prevede prima l'immagine."}, "desktop_image_width": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Media"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON><PERSON><PERSON> immagine su <PERSON>", "info": "L'immagine viene automaticamente ottimizzata per il mobile."}, "desktop_content_alignment": {"options__1": {"label": "A sinistra"}, "options__3": {"label": "A destra"}, "label": "Allineamento contenuto su desktop", "options__2": {"label": "Al centro"}}, "desktop_content_position": {"options__1": {"label": "In alto"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "In basso"}, "label": "Posizione contenuto su desktop"}, "content_layout": {"options__1": {"label": "Non sovrapporre"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "label": "Layout del contenuto"}, "mobile_content_alignment": {"options__1": {"label": "A sinistra"}, "options__3": {"label": "A destra"}, "label": "Allineamento contenuto su dispositivi mobili", "options__2": {"label": "Al centro"}}}, "blocks": {"heading": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON>"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON><PERSON>"}, "text_style": {"label": "Stile del testo", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}}, "button": {"name": "Pulsante", "settings": {"button_label": {"label": "<PERSON><PERSON><PERSON><PERSON> pulsante", "info": "<PERSON>cia vuota l'etichetta per nascondere il pulsante."}, "button_link": {"label": "<PERSON> pulsante"}, "outline_button": {"label": "Utilizza stile contorno pulsante"}}}, "caption": {"name": "Didascalia", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "Stile testo", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "Dimensione del testo", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Media"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Immagine con testo"}}, "main-article": {"name": "Articolo del blog", "blocks": {"featured_image": {"name": "Immagine in evidenza", "settings": {"image_height": {"label": "Altezza immagine in evidenza", "options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Media"}, "info": "Per un risultato ottimale utilizza un'immagine con proporzioni 16:9. [Maggiori informazioni](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "Grande"}}}}, "title": {"name": "<PERSON><PERSON>", "settings": {"blog_show_date": {"label": "Mostra data"}, "blog_show_author": {"label": "Mostra autore"}}}, "content": {"name": "<PERSON><PERSON><PERSON>"}, "share": {"name": "Condi<PERSON><PERSON>", "settings": {"featured_image_info": {"content": "Se includi un link in post sui social media, l'immagine in evidenza della pagina verrà mostrata come immagine di anteprima. [Maggiori informazioni](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Insieme all'immagine di anteprima sono inclusi un titolo e una descrizione del negozio. [Maggiori informazioni](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "<PERSON><PERSON>"}}}}}, "main-blog": {"name": "Articoli del blog", "settings": {"header": {"content": "Scheda articoli del blog"}, "show_image": {"label": "Mostra immagine in evidenza"}, "paragraph": {"content": "Modifica i riassunti modificando gli articoli del blog. [Maggiori informazioni](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "Mostra data"}, "show_author": {"label": "Mostra autore"}, "layout": {"label": "Layout desktop", "options__1": {"label": "Griglia"}, "options__2": {"label": "Collage"}, "info": "I post sono visualizzati in elenco sui dispositivi mobili."}, "image_height": {"label": "Altezza immagine in evidenza", "options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Media"}, "options__4": {"label": "Grande"}, "info": "Per un risultato ottimale, utilizza un'immagine con proporzioni 3:2. [Maggiori informazioni](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Subtotale", "blocks": {"subtotal": {"name": "Prezzo subtotale"}, "buttons": {"name": "Pulsante check-out"}}}, "main-cart-items": {"name": "Articoli"}, "main-collection-banner": {"name": "Banner collezione", "settings": {"paragraph": {"content": "Aggiungi una descrizione o un'immagine modificando la collezione. [Maggiori informazioni](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Mostra descrizione collezione"}, "show_collection_image": {"label": "Mostra immagine collezione", "info": "Per un risultato ottimale utilizza un'immagine con proporzioni 16:9. [Maggiori informazioni](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "<PERSON><PERSON><PERSON>", "settings": {"products_per_page": {"label": "Prodotti per pagina"}, "image_ratio": {"label": "Proporzioni delle immagini", "options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Quadrate"}}, "show_secondary_image": {"label": "Mostra la seconda immagine al passaggio del mouse"}, "show_vendor": {"label": "<PERSON>ra fornitore"}, "enable_tags": {"label": "Abilita i filtri", "info": "Personalizza i filtri con l'app Search & Discovery. [Maggiori informazioni](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_filtering": {"label": "Abilita i filtri", "info": "Personalizza i filtri con l'app Search & Discovery. [Maggiori informazioni](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "Abilita ordinamento"}, "header__1": {"content": "Filtri e ordinamento"}, "header__3": {"content": "<PERSON><PERSON><PERSON> prodotto"}, "show_rating": {"label": "Mostra valutazione del prodotto", "info": "Per mostrare una valutazione, aggiungi un'app di valutazione del prodotto. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "enable_quick_buy": {"label": "Abilita il pulsante di aggiunta rapida", "info": "L'ideale in caso di pop up o di tipo di carrello a finestra."}, "columns_desktop": {"label": "Numero di colonne su desktop"}, "header_mobile": {"content": "Layout dispositivo mobile"}, "columns_mobile": {"label": "Numero di colonne su mobile", "options__1": {"label": "1 colonna"}, "options__2": {"label": "2 colonne"}}, "filter_type": {"label": "Layout dei filtri desktop", "options__1": {"label": "Orizzontale"}, "options__2": {"label": "Verticale"}, "options__3": {"label": "Finestra"}, "info": "Il layout predefinito per dispositivi mobili prevede la finestra."}}}, "main-list-collections": {"name": "Pagina con l'elenco delle collezioni", "settings": {"title": {"label": "<PERSON><PERSON>"}, "sort": {"label": "Ordina le collezioni per:", "options__1": {"label": "In ordine alfabetico, A-Z"}, "options__2": {"label": "In ordine alfabetico, Z-A"}, "options__3": {"label": "<PERSON>, da più a meno recente"}, "options__4": {"label": "Data, da meno a più recente"}, "options__5": {"label": "Conteggio prodotti decrescente"}, "options__6": {"label": "Conteggio prodotti crescente"}}, "image_ratio": {"label": "Proporzioni delle immagini", "options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Quadrate"}, "info": "Aggiungi immagini modificando le collezioni. [Maggiori informazioni](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Numero di colonne su desktop"}, "header_mobile": {"content": "Layout dispositivo mobile"}, "columns_mobile": {"label": "Numero di colonne su dispositivo mobile", "options__1": {"label": "1 colonna"}, "options__2": {"label": "2 colonne"}}}}, "main-page": {"name": "<PERSON><PERSON><PERSON>"}, "main-password-footer": {"name": "Footer della password"}, "main-password-header": {"name": "Header della <PERSON>", "settings": {"logo_header": {"content": "Logo"}, "logo_help": {"content": "Modifica il tuo logo nelle impostazioni tema."}}}, "main-product": {"name": "Informazioni di prodotto", "blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "Stile del testo", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "title": {"name": "<PERSON><PERSON>"}, "price": {"name": "Prezzo"}, "quantity_selector": {"name": "Selettore di quantità"}, "variant_picker": {"name": "Selettore di variante", "settings": {"picker_type": {"label": "Stile", "options__1": {"label": "Menu a discesa"}, "options__2": {"label": "\"A pillole\""}}, "swatch_shape": {"label": "Campione di colore", "info": "Abilita i [campioni di colore](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) nelle opzioni del prodotto.", "options__1": {"label": "To<PERSON>"}, "options__2": {"label": "Quadrato"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Buy button", "settings": {"show_dynamic_checkout": {"label": "Mostra pulsanti di check-out dinamico", "info": "U<PERSON>iz<PERSON><PERSON> i metodi di pagamento disponibili sul tuo negozio, i clienti vedranno la propria opzione preferita, ad esempio PayPal o Apple Pay. [Maggiori informazioni](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Mostra il modulo informazioni del destinatario per i buoni regalo", "info": "Consente agli acquirenti di inviare i buoni regalo in una data programmata insieme a un messaggio personale. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "Disponibilità di ritiro"}, "description": {"name": "Descrizione"}, "share": {"name": "Condi<PERSON><PERSON>", "settings": {"featured_image_info": {"content": "Se includi un link in post sui social media, l'immagine in evidenza della pagina verrà mostrata come immagine di anteprima. [Maggiori informazioni](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Insieme all'immagine di anteprima sono inclusi un titolo e una descrizione del negozio. [Maggiori informazioni](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "<PERSON><PERSON>"}}}, "collapsible_tab": {"name": "Riga comprimibile", "settings": {"heading": {"info": "Includi un titolo che spieghi il contenuto.", "label": "<PERSON><PERSON>"}, "content": {"label": "Contenuto riga"}, "page": {"label": "Contenuto riga dalla pagina"}, "icon": {"label": "Icona", "options__1": {"label": "Nessuna"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Banana"}, "options__4": {"label": "Bottiglia"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "Carota"}, "options__7": {"label": "<PERSON><PERSON>o chat"}, "options__8": {"label": "Segno di <PERSON>ta"}, "options__9": {"label": "Blocco appunti"}, "options__10": {"label": "<PERSON><PERSON><PERSON>"}, "options__11": {"label": "<PERSON><PERSON>"}, "options__12": {"label": "Asciugatrice"}, "options__13": {"label": "<PERSON><PERSON><PERSON>"}, "options__14": {"label": "Fuoco"}, "options__15": {"label": "Senza glutine"}, "options__16": {"label": "<PERSON><PERSON><PERSON>"}, "options__17": {"label": "<PERSON>rro"}, "options__18": {"label": "Foglia"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Fulmine"}, "options__21": {"label": "<PERSON><PERSON>"}, "options__22": {"label": "<PERSON><PERSON><PERSON>"}, "options__23": {"label": "<PERSON>n mappa"}, "options__24": {"label": "Senza frutta a guscio"}, "options__25": {"label": "<PERSON><PERSON><PERSON>"}, "options__26": {"label": "Impronta di zampa"}, "options__27": {"label": "<PERSON><PERSON><PERSON>"}, "options__28": {"label": "Profumi"}, "options__29": {"label": "Aereo"}, "options__30": {"label": "Piante"}, "options__31": {"label": "<PERSON><PERSON><PERSON> prezzo"}, "options__32": {"label": "Punto interrogativo"}, "options__33": {"label": "R<PERSON><PERSON>lo"}, "options__34": {"label": "<PERSON><PERSON>"}, "options__35": {"label": "<PERSON><PERSON><PERSON>"}, "options__36": {"label": "Piatto da portata"}, "options__37": {"label": "<PERSON><PERSON><PERSON>"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Fiocco di neve"}, "options__41": {"label": "<PERSON>"}, "options__42": {"label": "Cronometro"}, "options__43": {"label": "Camion"}, "options__44": {"label": "Lavaggio"}}}}, "popup": {"name": "Pop up", "settings": {"link_label": {"label": "Etichetta link"}, "page": {"label": "<PERSON><PERSON><PERSON>"}}}, "rating": {"name": "Valutazione del prodotto", "settings": {"paragraph": {"content": "Per mostrare una valutazione, aggiungi un'app di valutazione del prodotto. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}, "complementary_products": {"name": "Prodotti complementari", "settings": {"paragraph": {"content": "Aggiungi l'app Search & Discovery per selezionare i prodotti complementari. [Maggiori informazioni](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "<PERSON><PERSON>"}, "make_collapsible_row": {"label": "Mostra come riga comprimibile"}, "icon": {"info": "Visibile quando è visualizzata la riga comprimibile."}, "product_list_limit": {"label": "<PERSON><PERSON> di prodotti da mostrare"}, "products_per_page": {"label": "Numero di prodotti per pagina"}, "pagination_style": {"label": "Stile impaginazione", "options": {"option_1": "<PERSON><PERSON><PERSON>", "option_2": "Contatore", "option_3": "Numeri"}}, "product_card": {"heading": "<PERSON><PERSON><PERSON> prodotto"}, "image_ratio": {"label": "Proporzioni immagine", "options": {"option_1": "Verticale", "option_2": "Square"}}, "enable_quick_add": {"label": "Abilita il pulsante di aggiunta rapida"}}}, "icon_with_text": {"name": "Icona con testo", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Orizzontale"}, "options__2": {"label": "Verticale"}}, "content": {"label": "<PERSON><PERSON><PERSON>", "info": "Sceg<PERSON> un'icona o aggiungi un'immagine per ogni colonna o riga."}, "heading": {"info": "Lascia vuota l'etichetta del titolo per nascondere la colonna delle icone."}, "icon_1": {"label": "Prima icona"}, "image_1": {"label": "Prima immagine"}, "heading_1": {"label": "Primo titolo"}, "icon_2": {"label": "Seconda icona"}, "image_2": {"label": "Seconda immagine"}, "heading_2": {"label": "Secondo titolo"}, "icon_3": {"label": "<PERSON><PERSON><PERSON> icona"}, "image_3": {"label": "<PERSON><PERSON><PERSON> immagine"}, "heading_3": {"label": "<PERSON><PERSON><PERSON> t<PERSON>"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Stile testo", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "inventory": {"name": "Stato delle scorte", "settings": {"text_style": {"label": "Stile testo", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "inventory_threshold": {"label": "Soglia per le scorte scarse", "info": "Scegli 0 per mostrare sempre disponibili, se stoccati."}, "show_inventory_quantity": {"label": "Mostra conteggio delle scorte"}}}}, "settings": {"header": {"content": "Contenuti multimediali", "info": "Maggiori informazioni sui [tipi di contenuti multimediali](https://help.shopify.com/manual/products/product-media)."}, "enable_video_looping": {"label": "Abilita la riproduzione in loop dei video"}, "enable_sticky_info": {"label": "Abilita contenuto fisso su desktop"}, "hide_variants": {"label": "Nascondi i contenuti multimediali delle altre varianti dopo averne selezionata una"}, "gallery_layout": {"label": "Layout desktop", "options__1": {"label": "Elencato"}, "options__2": {"label": "2 colonne"}, "options__3": {"label": "Miniature"}, "options__4": {"label": "Carosello miniature"}}, "media_size": {"label": "Larghezza dei contenuti multimediali su desktop", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Media"}, "options__3": {"label": "Grande"}, "info": "Contenuti multimediali automaticamente ottimizzati per dispositivi mobili."}, "mobile_thumbnails": {"label": "Layout dispositivo mobile", "options__1": {"label": "2 colonne"}, "options__2": {"label": "Mostra miniature"}, "options__3": {"label": "Nascondi miniature"}}, "media_position": {"label": "Posizione contenuti multimediali su desktop", "info": "Posizione automaticamente ottimizzata per dispositivi mobili.", "options__1": {"label": "A sinistra"}, "options__2": {"label": "A destra"}}, "image_zoom": {"label": "Zoom immagine", "info": "Clicca e fai scorrere il mouse sopra le impostazioni predefinite per aprire una lightbox sul dispositivo mobile.", "options__1": {"label": "Apri la lightbox"}, "options__2": {"label": "Clicca e fai scorrere il mouse"}, "options__3": {"label": "Nessuno zoom"}}, "constrain_to_viewport": {"label": "Adatta i contenuti multimediali all'altezza dello schermo"}, "media_fit": {"label": "Adattamento dei contenuti multimediali", "options__1": {"label": "Originale"}, "options__2": {"label": "Riempi"}}}}, "main-search": {"name": "Risultati della ricerca", "settings": {"image_ratio": {"label": "Proporzioni delle immagini", "options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Quadrate"}}, "show_secondary_image": {"label": "Mostra la seconda immagine al passaggio del mouse"}, "show_vendor": {"label": "<PERSON>ra fornitore"}, "header__1": {"content": "<PERSON><PERSON><PERSON> prodotto"}, "header__2": {"content": "Scheda del blog", "info": "Lo stile della scheda del blog si applica anche alle schede della pagina nei risultati di ricerca. Per modificare lo stile delle schede, aggiorna le impostazioni del tema."}, "article_show_date": {"label": "Mostra data"}, "article_show_author": {"label": "Mostra autore"}, "show_rating": {"label": "Mostra valutazione del prodotto", "info": "Per mostrare una valutazione, aggiungi un'app di valutazione del prodotto. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "columns_desktop": {"label": "Numero di colonne su desktop"}, "header_mobile": {"content": "Layout dispositivo mobile"}, "columns_mobile": {"label": "Numero di colonne su mobile", "options__1": {"label": "1 colonna"}, "options__2": {"label": "2 colonne"}}}}, "multicolumn": {"name": "Multicolonna", "settings": {"title": {"label": "<PERSON><PERSON>"}, "image_width": {"label": "<PERSON><PERSON><PERSON><PERSON> immagine", "options__1": {"label": "Un terzo della larghezza della colonna"}, "options__2": {"label": "Met<PERSON> larghezza della colonna"}, "options__3": {"label": "Intera larghezza della colonna"}}, "image_ratio": {"label": "Proporzioni delle immagini", "options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "Quadrate"}, "options__4": {"label": "Tonde"}}, "column_alignment": {"label": "Allineamento colonna", "options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}}, "background_style": {"label": "S<PERSON>ndo secondario", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Mostra come sfondo colonna"}}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON> pulsante"}, "button_link": {"label": "<PERSON> pulsante"}, "swipe_on_mobile": {"label": "Abilita scorrimento su dispositivo mobile"}, "columns_desktop": {"label": "Numero di colonne su desktop"}, "header_mobile": {"content": "Layout dispositivo mobile"}, "columns_mobile": {"label": "Numero di colonne su mobile", "options__1": {"label": "1 colonna"}, "options__2": {"label": "2 colonne"}}}, "blocks": {"column": {"name": "<PERSON>onna", "settings": {"image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "title": {"label": "<PERSON><PERSON>"}, "text": {"label": "Descrizione"}, "link_label": {"label": "Etichetta link"}, "link": {"label": "Link"}}}}, "presets": {"name": "Multicolonna"}}, "newsletter": {"name": "Iscrizione alla newsletter", "settings": {"full_width": {"label": "Crea sezione a larghezza intera"}, "paragraph": {"content": "Ogni abbonamento email crea un account cliente. [Maggiori informazioni](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON>"}}}, "paragraph": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"paragraph": {"label": "Descrizione"}}}, "email_form": {"name": "<PERSON><PERSON><PERSON> email"}}, "presets": {"name": "Iscrizione alla newsletter"}}, "page": {"name": "<PERSON><PERSON><PERSON>", "settings": {"page": {"label": "<PERSON><PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "rich-text": {"name": "Rich text", "settings": {"full_width": {"label": "Crea sezione a larghezza intera"}, "desktop_content_position": {"options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}, "label": "Posizione contenuto su desktop", "info": "Posizione automaticamente ottimizzata per dispositivi mobili."}, "content_alignment": {"options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}, "label": "Allineamento contenuto"}}, "blocks": {"heading": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON>"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "Descrizione"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "Prima etichetta pulsante", "info": "<PERSON>cia vuota l'etichetta per nascondere il pulsante."}, "button_link_1": {"label": "Primo link pulsante"}, "button_style_secondary_1": {"label": "Utilizza stile contorno pulsante"}, "button_label_2": {"label": "Seconda etichetta pulsante", "info": "<PERSON>cia vuota l'etichetta per nascondere il pulsante."}, "button_link_2": {"label": "Secondo link pulsante"}, "button_style_secondary_2": {"label": "Utilizza stile contorno pulsante"}}}, "caption": {"name": "Didascalia", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "Stile testo", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "Dimensione del testo", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Media"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Rich text"}}, "apps": {"name": "App", "settings": {"include_margins": {"label": "Rendi i margini della sezione uguali al tema"}}, "presets": {"name": "App"}}, "video": {"name": "Video", "settings": {"heading": {"label": "<PERSON><PERSON>"}, "cover_image": {"label": "Immagine di copertina"}, "video_url": {"label": "URL", "info": "Utilizza un URL YouTube o Vimeo"}, "description": {"label": "Testo alternativo del video", "info": "Descrivi il video per i clienti che utilizzano i lettori di schermo. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Aggiungi spaziatura immagine", "info": "Se non desideri che l'immagine di copertina venga ritagliata, seleziona una spaziatura per l'immagine."}, "full_width": {"label": "Rendi sezione a larghezza intera"}, "video": {"label": "Video"}, "enable_video_looping": {"label": "Riproduci il video in loop"}, "header__1": {"content": "Video ospitato da Shopify"}, "header__2": {"content": "Oppure incorpora il video da URL"}, "header__3": {"content": "Stile"}, "paragraph": {"content": "Viene mostrato quando non viene selezionato nessun video ospitato da Shopify."}}, "presets": {"name": "Video"}}, "featured-product": {"name": "Se<PERSON> \"prodotto in primo piano\"", "blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>"}, "text_style": {"label": "Stile del testo", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "title": {"name": "<PERSON><PERSON>"}, "price": {"name": "Prezzo"}, "quantity_selector": {"name": "<PERSON><PERSON><PERSON> quantità"}, "variant_picker": {"name": "Selettore di variante", "settings": {"picker_type": {"label": "Stile", "options__1": {"label": "Menu a discesa"}, "options__2": {"label": "\"A pillole\""}}, "swatch_shape": {"label": "Campione di colore", "info": "Abilita i [campioni di colore](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) nelle opzioni del prodotto.", "options__1": {"label": "To<PERSON>"}, "options__2": {"label": "Quadrato"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "buy_buttons": {"name": "Buy button", "settings": {"show_dynamic_checkout": {"label": "Mostra pulsanti di check-out dinamico", "info": "U<PERSON>iz<PERSON><PERSON> i metodi di pagamento disponibili sul tuo negozio, i clienti vedranno la propria opzione preferita, ad esempio PayPal o Apple Pay. [Maggiori informazioni](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Descrizione"}, "share": {"name": "Condi<PERSON><PERSON>", "settings": {"featured_image_info": {"content": "Se includi un link nei post sui social media, l'immagine in evidenza della pagina verrà mostrata come immagine di anteprima. [Maggiori informazioni](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Insieme all'immagine di anteprima sono inclusi un titolo e una descrizione del negozio. [Maggiori informazioni](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "<PERSON><PERSON>"}}}, "rating": {"name": "Valutazione del prodotto", "settings": {"paragraph": {"content": "Per mostrare una valutazione, aggiungi un'app di valutazione del prodotto. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Stile testo", "options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}}, "settings": {"product": {"label": "<PERSON><PERSON><PERSON>"}, "secondary_background": {"label": "Mostra sfondo secondario"}, "header": {"content": "Contenuti multimediali", "info": "Maggiori informazioni sui [tipi di media](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Abilita la riproduzione in loop dei video"}, "hide_variants": {"label": "Nascondi contenuti multimediali delle varianti non selezionate sul desktop"}, "media_position": {"label": "Posizione contenuti multimediali su desktop", "info": "Posizione automaticamente ottimizzata per i dispositivi mobili.", "options__1": {"label": "A sinistra"}, "options__2": {"label": "A destra"}}}, "presets": {"name": "Se<PERSON> \"prodotto in primo piano\""}}, "email-signup-banner": {"name": "Banner di iscrizione alla newsletter", "settings": {"paragraph": {"content": "Ogni abbonamento email crea un account cliente. [Maggiori informazioni](https://help.shopify.com/manual/customers)"}, "image": {"label": "Immagine di sfondo"}, "show_background_image": {"label": "Mostra immagine di sfondo"}, "show_text_box": {"label": "Mostra contenitore sul desktop"}, "image_overlay_opacity": {"label": "Opacità della sovrapposizione immagine"}, "show_text_below": {"label": "Mostra contenuto sotto le immagini sui dispositivi mobili", "info": "Per un risultato ottimale utilizza un'immagine con proporzioni 16:9. [Maggiori informazioni](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "image_height": {"label": "Altezza banner", "options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Media"}, "options__4": {"label": "Grande"}, "info": "Per un risultato ottimale utilizza un'immagine con proporzioni 16:9. [Maggiori informazioni](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__4": {"label": "In mezzo a sinistra"}, "options__5": {"label": "In mezzo al centro"}, "options__6": {"label": "In mezzo a destra"}, "options__7": {"label": "In basso a sinistra"}, "options__8": {"label": "In basso al centro"}, "options__9": {"label": "In basso a destra"}, "options__1": {"label": "In alto a sinistra"}, "options__2": {"label": "In alto al centro"}, "options__3": {"label": "In alto a destra"}, "label": "Posizione contenuto su desktop"}, "desktop_content_alignment": {"options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}, "label": "Allineamento contenuto su desktop"}, "header": {"content": "Layout dispositivo mobile"}, "mobile_content_alignment": {"options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}, "label": "Allineamento contenuto su dispositivi mobili"}, "color_scheme": {"info": "Visibile quando è visualizzato il contenitore."}}, "blocks": {"heading": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON>"}}}, "paragraph": {"name": "Paragrafo", "settings": {"paragraph": {"label": "Descrizione"}, "text_style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "label": "Stile del testo"}}}, "email_form": {"name": "<PERSON><PERSON><PERSON> email"}}, "presets": {"name": "Banner di iscrizione alla newsletter"}}, "slideshow": {"name": "Presentazione", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Larghezza intera"}, "options__2": {"label": "Griglia"}}, "slide_height": {"label": "Altezza slide", "options__1": {"label": "Adatta alla prima immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Media"}, "options__4": {"label": "Grande"}}, "slider_visual": {"label": "Stile impaginazione", "options__1": {"label": "Contatore"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Numeri"}}, "auto_rotate": {"label": "Ruota slide automaticamente"}, "change_slides_speed": {"label": "Cambia slide ogni"}, "mobile": {"content": "Layout dispositivo mobile"}, "show_text_below": {"label": "Mostra contenuto sotto le immagini sui dispositivi mobili"}, "accessibility": {"content": "Accessibilità", "label": "Descrizione presentazione", "info": "Descrivi la presentazione per i clienti che utilizzano i lettori di schermo."}}, "blocks": {"slide": {"name": "Scorrimento", "settings": {"image": {"label": "Image"}, "heading": {"label": "<PERSON><PERSON>"}, "subheading": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON> pulsante", "info": "<PERSON>cia vuota l'etichetta per nascondere il pulsante."}, "link": {"label": "<PERSON> pulsante"}, "secondary_style": {"label": "Utilizza stile contorno pulsante"}, "box_align": {"label": "Posizione contenuto su desktop", "options__1": {"label": "In alto a sinistra"}, "options__2": {"label": "In alto al centro"}, "options__3": {"label": "In alto a destra"}, "options__4": {"label": "In mezzo a sinistra"}, "options__5": {"label": "In mezzo al centro"}, "options__6": {"label": "In mezzo a destra"}, "options__7": {"label": "In basso a sinistra"}, "options__8": {"label": "In basso al centro"}, "options__9": {"label": "In basso a destra"}, "info": "Posizione automaticamente ottimizzata per dispositivi mobili."}, "show_text_box": {"label": "Mostra contenitore sul desktop"}, "text_alignment": {"label": "Allineamento contenuto su desktop", "option_1": {"label": "A sinistra"}, "option_2": {"label": "Al centro"}, "option_3": {"label": "A destra"}}, "image_overlay_opacity": {"label": "Opacità della sovrapposizione immagine"}, "text_alignment_mobile": {"label": "Allineamento contenuto su dispositivi mobili", "options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}}}}}, "presets": {"name": "Presentazione"}}, "collapsible_content": {"name": "Contenuto comprimibile", "settings": {"caption": {"label": "Didascalia"}, "heading": {"label": "<PERSON><PERSON>"}, "heading_alignment": {"label": "Allineamento titolo", "options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}}, "layout": {"label": "Layout", "options__1": {"label": "<PERSON><PERSON><PERSON> contenitore"}, "options__2": {"label": "Contenitore riga"}, "options__3": {"label": "Contenitore sezione"}}, "container_color_scheme": {"label": "Schema di colori dei contenitori", "info": "Visibile quando il Layout viene impostato a contenitore Riga o Sezione."}, "open_first_collapsible_row": {"label": "Apri prima riga comprimibile"}, "header": {"content": "Layout immagine"}, "image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_ratio": {"label": "Proporzioni immagine", "options__1": {"label": "<PERSON>tta a immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Grande"}}, "desktop_layout": {"label": "Layout desktop", "options__1": {"label": "Prima l'immagine"}, "options__2": {"label": "L'immagine dopo"}, "info": "Sempre prima l'immagine su dispositivo mobile."}}, "blocks": {"collapsible_row": {"name": "Riga comprimibile", "settings": {"heading": {"info": "Includi un titolo che spieghi il contenuto.", "label": "<PERSON><PERSON>"}, "row_content": {"label": "Contenuto riga"}, "page": {"label": "Contenuto riga dalla pagina"}, "icon": {"label": "Icona", "options__1": {"label": "Nessuna"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Banana"}, "options__4": {"label": "Bottiglia"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "Carota"}, "options__7": {"label": "<PERSON><PERSON>o chat"}, "options__8": {"label": "Segno di <PERSON>ta"}, "options__9": {"label": "Blocco appunti"}, "options__10": {"label": "<PERSON><PERSON><PERSON>"}, "options__11": {"label": "<PERSON><PERSON>"}, "options__12": {"label": "Asciugatrice"}, "options__13": {"label": "<PERSON><PERSON><PERSON>"}, "options__14": {"label": "Fuoco"}, "options__15": {"label": "Senza glutine"}, "options__16": {"label": "<PERSON><PERSON><PERSON>"}, "options__17": {"label": "<PERSON>rro"}, "options__18": {"label": "Foglia"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Fulmine"}, "options__21": {"label": "<PERSON><PERSON>"}, "options__22": {"label": "<PERSON><PERSON><PERSON>"}, "options__23": {"label": "<PERSON>n mappa"}, "options__24": {"label": "Senza frutta a guscio"}, "options__25": {"label": "<PERSON><PERSON><PERSON>"}, "options__26": {"label": "Impronta di zampa"}, "options__27": {"label": "<PERSON><PERSON><PERSON>"}, "options__28": {"label": "Profumi"}, "options__29": {"label": "Aereo"}, "options__30": {"label": "Piante"}, "options__31": {"label": "<PERSON><PERSON><PERSON> prezzo"}, "options__32": {"label": "Punto interrogativo"}, "options__33": {"label": "R<PERSON><PERSON>lo"}, "options__34": {"label": "<PERSON><PERSON>"}, "options__35": {"label": "<PERSON><PERSON><PERSON>"}, "options__36": {"label": "Piatto da portata"}, "options__37": {"label": "<PERSON><PERSON><PERSON>"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Fiocco di neve"}, "options__41": {"label": "<PERSON>"}, "options__42": {"label": "Cronometro"}, "options__43": {"label": "Camion"}, "options__44": {"label": "Lavaggio"}}}}}, "presets": {"name": "Contenuto comprimibile"}}, "main-account": {"name": "Account"}, "main-activate-account": {"name": "Attivazione account"}, "main-addresses": {"name": "<PERSON>diriz<PERSON>"}, "main-login": {"name": "Accedi"}, "main-order": {"name": "Ordine"}, "main-register": {"name": "Registrazione"}, "main-reset-password": {"name": "Reimpostazione della password"}, "related-products": {"name": "<PERSON>dotti simili", "settings": {"heading": {"label": "<PERSON><PERSON>"}, "products_to_show": {"label": "<PERSON><PERSON> di prodotti da mostrare"}, "columns_desktop": {"label": "Numero di colonne su desktop"}, "paragraph__1": {"content": "Le raccomandazioni dinamiche utilizzano i dati di ordini e prodotti per cambiare e migliorare nel tempo. [Maggiori informazioni](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "<PERSON><PERSON><PERSON> prodotto"}, "image_ratio": {"label": "Proporzioni immagine", "options__1": {"label": "<PERSON>tta a immagine"}, "options__2": {"label": "Verticale"}, "options__3": {"label": "Quadrata"}}, "show_secondary_image": {"label": "Mostra la seconda immagine al passaggio del mouse"}, "show_vendor": {"label": "<PERSON>ra fornitore"}, "show_rating": {"label": "Mostra valutazione del prodotto", "info": "Per mostrare una valutazione, aggiungi un'app di valutazione del prodotto. [Maggiori informazioni](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "header_mobile": {"content": "Layout dispositivo mobile"}, "columns_mobile": {"label": "Numero di colonne su dispositivo mobile", "options__1": {"label": "1 colonna"}, "options__2": {"label": "2 colonne"}}}}, "multirow": {"name": "Riga multipla", "settings": {"image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_height": {"options__1": {"label": "<PERSON>tta all'immagine"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "Medio"}, "options__4": {"label": "Grande"}, "label": "Altezza immagine"}, "desktop_image_width": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Media"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON><PERSON><PERSON> immagine su <PERSON>", "info": "L'immagine viene automaticamente ottimizzata per il mobile."}, "heading_size": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Media"}, "options__3": {"label": "Grande"}, "label": "Dimensione titolo"}, "text_style": {"options__1": {"label": "Corpo"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "label": "Stile del testo"}, "button_style": {"options__1": {"label": "Pulsante in tinta unita"}, "options__2": {"label": "Contorno pulsante"}, "label": "Stile pulsante"}, "desktop_content_alignment": {"options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}, "label": "Allineamento contenuto su desktop"}, "desktop_content_position": {"options__1": {"label": "In alto"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "In basso"}, "label": "Posizione contenuto su desktop", "info": "Posizione automaticamente ottimizzata per i dispositivi mobili."}, "image_layout": {"options__1": {"label": "Alterna da sinistra"}, "options__2": {"label": "Alterna da destra"}, "options__3": {"label": "Allineata a sinistra"}, "options__4": {"label": "Allineata a destra"}, "label": "Posizionamento immagine su desktop", "info": "Il posizionamento viene automaticamente ottimizzato per i dispositivi mobili."}, "container_color_scheme": {"label": "Schema di colori dei contenitori"}, "mobile_content_alignment": {"options__1": {"label": "A sinistra"}, "options__2": {"label": "Al centro"}, "options__3": {"label": "A destra"}, "label": "Allineamento contenuto su dispositivi mobili"}, "header_mobile": {"content": "Layout dispositivo mobile"}}, "blocks": {"row": {"name": "Riga", "settings": {"image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "caption": {"label": "Didascalia"}, "heading": {"label": "<PERSON><PERSON>"}, "text": {"label": "<PERSON><PERSON>"}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON> pulsante"}, "button_link": {"label": "<PERSON> pulsante"}}}}, "presets": {"name": "Riga multipla"}}, "quick-order-list": {"name": "Elenco ordini rapido", "settings": {"show_image": {"label": "<PERSON><PERSON> immagini"}, "show_sku": {"label": "Mostra SKU"}}, "presets": {"name": "Elenco ordini rapido"}}}}