{% function "navRecursive" %}
    {% if link.divider %}
        <hr class="dropdown-divider">
    {% endif %}
    <li class="nav-item main position-static megamenu{% if link.dropdown.size > 0 %} dropdown{% endif %}{% if link.active > 0 %} active{% endif %}{% if level == 1 %} js-main-nav-item{% endif %}">
        <a {% if link.dropdown.size %}href="#submenu-level-{{ level }}-{{ forloop.index }}" data-toggle="collapse" data-target="#submenu-level-{{ level }}-{{ forloop.index }}" aria-controls="#submenu-level-{{ level }}-{{ forloop.index }}" aria-expanded="false" aria-label="Toggle submenu navigation"{% else %}href="{{ link.href | default:'#' }}"{% endif %}
           class="{% if level == 3 %}list-title list-title-level-{{ level }}{% else %}nav-link nav-link-level-{{ level }}{% endif %}" {% if link.tooltip %}title="{{ link.tooltip }}" data-toggle="tooltip" data-placement="{% if link.dropdown.size > 0 %}right{% else %}bottom{% endif %}"{% endif %}>
            {% if link.addon %}
                {{ link.addon | raw }}
            {% endif %}
            {% if link.has_image %}
                <img src="{{ link.image['25x25'].url }}" alt="{{ link.image['25x25'].alt }}">
            {% endif %}
            {{ link.name }}
            {% if link.suffix %}
                {{ link.suffix | raw }}
            {% endif %}
            {% if sub.dropdown.size or link.dropdown.size %}
                <i class="fal fa-plus d-md-none d-inline-block"></i>
            {% endif %}
        </a>
        {% if link.dropdown.size > 0 %}
            <div class="row no-gutters submenu-level-{{ level }}-{{ forloop.index }} submenu-level-{{ level }}{% if level < 2 %} collapse submenu{% elsif level == 2 %} ml-md-0 mr-md-0 collapse submenu{% elsif level == 3 %} d-md-block collapse{% endif %}" id="submenu-level-{{ level }}-{{ forloop.index }}">
                {% if level == 1 %}
                    <a href="javascript:;" class="close-btn js-close-dropdown">
                        <i class="far fa-times fa-lg"></i>
                        {{ "item.actions.close" | t }}
                    </a>
                {% elsif level == 2 %}
                    <div class="row no-gutters submenu-head border-md-bottom">
                        <a href="javascript:;" class="col-xl-3 col-md-4 col-12 back-btn js-back-btn">
                            <i class="far fa-chevron-left"></i>
                            <span>{{ "item.actions.back" | t }}</span>
                        </a>

                        <a href="{{ link.href | default:'#' }}" class="col-xl-6 col-md-5 col-12 pl-md-4 nav-item">
                             {% if link.has_image %}
                                <img src="{{ link.image['25x25'].url }}" alt="{{ link.image['25x25'].alt }}">
                             {% endif %}
                            {{ link.name }}
                        </a>

                        <a href="javascript:;" class="col-3 text-right close-btn js-close-dropdown">
                            <i class="far fa-times fa-lg"></i>
                            {{ "item.actions.close" | t }}
                        </a>
                    </div>
                {% elsif level == 3 %}
                    <div class="row no-gutters submenu-head d-md-none">
                        <a href="{{ link.href | default:'#' }}" class="col-xl-6 col-md-5 col-12 pl-md-4 nav-item">
                                {% if link.has_image %}
                                <img src="{{ link.image['25x25'].url }}" alt="{{ link.image['25x25'].alt }}">
                                {% endif %}
                            {{ link.name }}
                        </a>
                        <a href="javascript:;" class="back-btn d-md-none js-back-btn">
                            <i class="far fa-chevron-left"></i>
                            <span>{{ "item.actions.back" | t }}</span>
                        </a>
                    </div>
                {% endif %}
                {% if level == 1 %}
                    {% if forloop.index == 1 %}
                        <div class="nav-list on-focus-wrap col-xl-3 col-md-4 col-12 pr-md-4 d-md-flex flex-column">
                            {% assign linksOnFocus = linklists.onFocus.links %}
                            {% if links.size > 0 %}
                                {% render "navigations.on-focus" links:linksOnFocus %}
                            {% endif %}
                        </div>
                    {% elsif forloop.index == 2 %}
                        <div class="nav-list on-focus-wrap col-xl-3 col-md-4 col-12 pr-md-4 d-md-flex flex-column d-none no-gutters">
                            {% assign linksTopVendors = linklists.topVendors.links %}
                            {% if links.size > 0 %}
                                {% render "navigations.top-vendors" links:linksTopVendors %}
                            {% endif %}
                        </div>
                    {% endif %}
                {% elsif level == 2 %}
                    <ul class="col-xl-3 col-md-4 col-12 nav-list pr-md-4 d-md-flex d-none flex-column">
                        {% assign hasLeftSubmenu = false %}
                        {% assign linksLeftSubmenu = linklists.leftSubmenu.links %}
                        {% if links.size > 0 %}
                            {% render "navigations.left-submenu" links:linksLeftSubmenu subName:sub.name hasLeftSubmenu: hasLeftSubmenu %}
                        {% endif %}
                    </ul>
                {% endif %}
                <ul class="nav-list {% if forloop.index == 1 %}col-xl-9 col-md-8 col-12{% elsif forloop.index == 2 %}col-xl-9 col-md-8 col-12{% endif %} {% if (level < 2 or (level == 2  and hasLeftSubmenu)) %}pl-md-4 border-md-left{% else %}{% endif %} position-static">
                    {% if level == 1 %}
                        {% if forloop.index == 1 %}
                            <li class="nav-item nav-item-head">
                                <div class="list-title">{{ "item.text.all_categories" | t }}</div>
                            </li>
                        {% elsif forloop.index == 2 %}
                            <li class="nav-item nav-item-head">
                                <a {% if link.dropdown.size > 0 and level == 1 %}href="#submenu-level-{{ level }}-{{ forloop.index }}" data-toggle="collapse" data-target="#submenu-level-{{ level }}-{{ forloop.index }}" class="list-title"{% else %}href="{{ link.href | default:'#' }}"{% endif %}>{{ "item.text.all_vendors" | t }}</a>
                                <a href="javascript:;" class="back-btn d-md-none d-block js-back-btn">
                                    <i class="far fa-chevron-left"></i>
                                    <span>{{ "item.actions.back" | t }}</span>
                                </a>
                            </li>
                        {% endif %}
                    {% endif %}
                    {% for sub in link.dropdown %}
                        {% assign newLevel = level|plus:1 %}
                        {% call 'navRecursive' link:sub level:newLevel %}
                    {% endfor %}
                </ul>
                {% if level == 1 %}
                <div class="product-extra-info-wrap d-xl-block d-none">
                    {% assign linksProductExtra = linklists.productExtra.links %}
                    {% if links.size > 0 %}
                        {% render "navigations.product-extra" links:linksProductExtra %}
                    {% endif %}
                </div>
                <div class="product-extra-info-wrap d-xl-block d-none">
                    {% assign deliveryLinks = linklists.deliveryLinks.links %}
                    {% if links.size > 0 %}
                        {% render "navigations.delivery-links" links:deliveryLinks %}
                    {% endif %}
                </div>
                {% endif %}
            </div>
        {% endif %}
    </li>
{% endfunction %}

<ul class="navbar-nav">
    {% for link in links %}
        {% call 'navRecursive' link:link level:1 %}
    {% endfor %}
</ul>