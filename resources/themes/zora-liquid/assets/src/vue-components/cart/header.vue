<template>
    <div class="dropdown-menu dropdown-menu-right rounded-0 mr-2 header-cart-popup" v-if="cart.variants_count > 0">
        <!--<div class="d-lg-none border-bottom pb-1 header-cart-title">-->
            <!--<h4 class="h5 ml-2">{{ 'pages.cart' | t }}</h4>-->
            <!--<a href="#" onclick="$(this).closest('dropdown-menu').removeClass('show');return false;" class="close-cart mr-2"><i class="fal fa-times"></i></a>-->
        <!--</div>-->
        <div class="d-scroll-y">
            <ul class="list-unstyled mb-0">
                <li v-for="product in cart.products" class="mb-3 pr-3 position-relative" :key="product.id">
                    <a :href="product.url">
                        <div class="row">
                            <div class="col-4">
                                <img class="img-fluid" :src="product.image['150x150'].url" :alt="product.image['150x150'].alt" :width="product.image['150x150'].width" :height="product.image['150x150'].height">
                            </div>
                            <div class="col-8">
                                <div class="title" :class="product.parameters.length > 0 || product.options.length > 0 ? 'border-bottom pb-2' : ''">
                                    {{ product.name }}
                                </div>

                                <div v-for="parameter in product.parameters" :key="parameter.id">
                                    <small>
                                        <span>{{parameter.name}}: </span>
                                        <span>{{parameter.value}}</span>
                                    </small>
                                </div>
                                <div v-for="option in product.options" :key="option.id">
                                    <small>
                                        <span>{{option.name}}: </span>
                                        <a :href="option.url" target="_blank" v-if="option.type === 'file'">{{option.value}}</a>
                                        <span v-else>{{option.value}}</span>
                                    </small>
                                </div>
                                <div class="price-wrap" v-if="product.discount">
                                    <!--<div class="labels labels-wrap">-->
                                        <!--<span :class="'label label-discount label-discount-' + product.discount.type" :style="'background-color: ' + product.discount.color">-->
                                        <!-- -&nbsp;{{ product.discount.type_value_formatted }}-->
                                        <!--</span>-->
                                    <!--</div>-->
                                    <!--<del class="price-old">{{ product.price_formatted }}</del>-->
                                    <span class="price-new" v-if="product.discount">
                                        {{product.quantity}} x {{product.discount_price_formatted}}
                                    </span>
                                </div>
                                <span class="price-new" v-else>
                                    {{product.quantity}} x {{product.price_formatted}}
                                </span>
                            </div>
                        </div>
                    </a>
                    <a :href="product.remove" @click.prevent="remove($event)" data-cart-item-remove data-noanimation class="remove-item far fa-times"></a>
                </li>
            </ul>
        </div>
        <div class="text-center">
            <div class="subtotal-wrap">
                <span class="subtotal-text">{{ 'cart.text.subtotal' | t }}</span> <span class="subtotal-value">{{ cart.total.amount_formatted }}</span>
            </div>
        </div>
        <div class="text-center purchase-wrap">
            <a :href="checkout" class="btn btn-primary btn-block">{{ 'cart.text.end_purchase' | t }}</a>
        </div>
    </div>
</template>

<script>
    import Ajax from '../../js/helpers/Ajax';

    export default {
        props: [
            'cart'
        ],
        mounted() {
            this.$nextTick(function () {
                let element = $(this.$el),
                    holder = element.parent(),
                    offset = holder.offset(),
                    offsetY = parseInt(offset.top || 0),
                    wHeight = $(window).height();

                if(offsetY && wHeight) {
                    element.find('.d-scroll-y').css({
                        'margin-right' : '-20px',
                        'padding-right' : '20px',
                        'max-height' : wHeight - offsetY - 200,
                    });
                }
            });
        },
        computed: {
            checkout: function() {
                return window.ccRoutes.checkout;
            }
        },
        methods: {
            remove: function (e) {
                Ajax(e.target||e.toElement);
                return false;
            }
        }
    }
</script>