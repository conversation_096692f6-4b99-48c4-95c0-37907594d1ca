<template>
    <div>
        <label :for="labelId" class="d-block mb-2">
            {{ group.group }}:
        </label>

        <div :class="'js-error-field-v' + row" data-fill-on-error></div>

        <table>
            <thead>
            <tr>
                <th></th>
                <th class="text-center pb-1" v-for="Xvalue in group.values.X.values">{{ Xvalue.name }}</th>
            </tr>
            </thead>
            <tbody>
                <tr v-for="(Yvalue, Yindex) in group.values.Y.values">
                    <td class="p-0 align-middle text-right pr-1">{{ Yvalue.name }}</td>
                    <td class="custom-radio-table-table-cell" v-for="(Xvalue, Xindex) in group.values.X.values">
                        <div class="custom-control custom-radio-table">
                            <input :disabled="disabled[Xvalue.name + Yvalue.name]" type="radio" :name="inputName" class="custom-control-input" :id="labelId + '-' + Xindex + Yindex" :value='getValue[Xvalue.name + Yvalue.name]' :data-key='getKey[Xvalue.name + Yvalue.name]'>
                            <label v-b-tooltip.hover :title="tooltip[Xvalue.name + Yvalue.name]" class="custom-control-label-table" :class="variant ? '' : 'missing-status'" :for="labelId + '-' + Xindex + Yindex"></label>
                        </div> <!-- form-check.// -->
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
    export default {
        computed: {
            inputName: function() {
                if(!this.prefix) {
                    return 'v' + this.row;
                }

                return this.prefix + '[v' + this.row + ']';
            },
            labelId: function() {
                return 'product-' + this.product_id + '-variant-' + this.row;
            },
            disabled: function() {
                let disabled = {},
                    that = this;

                this.group.values.Y.values.forEach(function(valueY) {
                    that.group.values.X.values.forEach(function(valueX) {
                        if(!that.variant) {
                            disabled[valueX.name + valueY.name] = false;
                        } else {
                            let check = that.variant[valueX.name + valueY.name].stock_status_key;
                            disabled[valueX.name + valueY.name] = !check || check === 'out_stock';
                        }
                    });
                });

                return disabled;
            },
            tooltip: function() {
                let tooltip = {},
                    that = this;

                this.group.values.Y.values.forEach(function(valueY) {
                    that.group.values.X.values.forEach(function(valueX) {
                        if(!that.variant) {
                            tooltip[valueX.name + valueY.name] = '';
                        } else if(!that.variant[valueX.name + valueY.name]) {
                            tooltip[valueX.name + valueY.name] = '';
                        } else {
                            tooltip[valueX.name + valueY.name] = that.variant[valueX.name + valueY.name].stock_status_key_label;
                        }
                    });
                });

                return tooltip;
            },
            getValue: function() {
                let values = {},
                    that = this;

                this.group.values.Y.values.forEach(function(valueY) {
                    that.group.values.X.values.forEach(function(valueX) {
                        values[valueX.name + valueY.name] = JSON.stringify({X:valueX.name,Y:valueY.name});
                    });
                });

                return values;
            },
            getKey: function() {
                let keys = {},
                    that = this;

                this.group.values.Y.values.forEach(function(valueY) {
                    that.group.values.X.values.forEach(function(valueX) {
                        keys[valueX.name + valueY.name] = JSON.stringify({X:valueX.key,Y:valueY.key});
                    });
                });

                return keys;
            }
        }
    }
</script>