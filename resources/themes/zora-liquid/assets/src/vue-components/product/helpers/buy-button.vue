<template>
    <span v-if="product.overlay && setting('show_buy_button')">
        <span v-if="setting('show_out_of_stock_label')" class="btn btn-sm btn-danger float-right" :style="'background-color: ' + product.overlay.color">{{ product.overlay.name }}</span>
    </span>
    <a v-else-if="setting('show_buy_button') && product.total_variants < 1 && product.variant.price !== null" :href="product.add_to_cart" :data-url="product.add_to_cart" class="btn btn-sm btn-buy float-right w-100 js-add-to-cart"><i class="fal fa-shopping-bag mr-2"></i>{{ "item.actions.add_to_cart" | t }}</a>
    <a v-else-if="setting('show_buy_button') && setting('show_quick_view')" :href="product.url" class="btn btn-sm btn-buy float-right w-100" data-modal="product-details-quick-view" data-modal-type="ajax" data-modal-popup-size="md" data-modal-popup-position="right" data-modal-pre-body="product-detail-quick-view-pre" :data-modal-pre--product="'CloudCart.app.getProduct:' + product.id">{{ "item.actions.quick_view" | t }}</a>
    <a v-else-if="setting('show_buy_button')" :href="product.url" class="btn btn-sm btn-buy float-right w-100">{{ "item.actions.view" | t }}</a>
</template>

<script>
    export default {
        props: [
            'product', 'settings', 'index'
        ],
        computed: {
            setting: function() {
                return (key, def) => this.settings.get(key, def);
            }
        }
    }
</script>