<template>
    <div class="card">
        <article class="card-group-item">
            <header class="card-header c-pointer"  data-filters-toggle=".filter-price-range">
                <h6 class="title float-left">{{ 'item.catalog.filters.price_range' | t }}</h6>
                <span class="float-right"><i class="far" :class="isOpened ? 'fa-chevron-up' : 'fa-chevron-down'"></i></span>
                <i class="clearfix"></i>
            </header>
            <div class="filter-content card-body filter-price-range" :class="isOpened ? 'open' : ''">

            <div data-widget-range-filter
                 :data-min="price_ranges.min"
                 :data-max="price_ranges.max"
                 :data-step="price_ranges.step"
                 :data-value-from="price_ranges.value_from"
                 :data-value-to="price_ranges.value_to"
            >
                <div class="row mb-3">
                    <div class="col-sm-12 d-flex">
                        <div data-widget-range-filter-text-from></div>
                        <div class="pl-1 pr-1">-</div>
                        <div data-widget-range-filter-text-to class="text-right"></div>
                    </div>
                </div>
                <div class="row mt-sm-1">
                    <div class="col-sm-12">
                        <div data-widget-range-filter-slider></div>
                        <input type="hidden" readonly="readonly" :value="price_ranges.active_values.price_from" name="price_from" data-widget-filter="price_from" data-filter-type="range" data-widget-range-filter-value-from>
                        <input type="hidden" readonly="readonly" :value="price_ranges.active_values.price_to" name="price_to" data-widget-filter="price_to" data-filter-type="range" data-widget-range-filter-value-to>
                    </div>
                </div>
                <div class="row mt-sm-1">
                    <div class="col-sm-12 mt-3">
                        <button type="button" class="btn btn-success btn-block" data-widget-range-filter-clear>{{ "global.action.clear" | t }}</button>
                    </div>
                </div>
            </div>

        </div> <!-- card-body.// -->
    </article> <!-- card-group-item.// -->
    </div> <!-- card.// -->
</template>

<script>
    import cookie from '../../js/helpers/cookie-filters';

    export default {
        mounted() {
            $(this.$el).find('[data-widget-range-filter-clear]')[this.price_ranges.min === this.price_ranges.value_from && this.price_ranges.max === this.price_ranges.value_to ? 'addClass' : 'removeClass']('d-none');

            this.$nextTick(function () {
                CloudCart.app.listingRangeFilters();
            });
        },
        props: [
            'price_ranges'
        ],
        computed: {
            isOpened: function() {
                return cookie('filters.filter-price-range').get();
            }
        }
    }
</script>