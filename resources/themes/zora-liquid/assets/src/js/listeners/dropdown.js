import $ from "jquery";

export default function ()
{
    // Add slideDown animation to <PERSON><PERSON><PERSON> dropdown when expanding.
    $('.dropdown').on('show.bs.dropdown', function() {
        // $(this).find('.dropdown-menu').first().stop(true, true).slideDown();
    });

    // Add slideUp animation to <PERSON>trap dropdown when collapsing.
    $('.dropdown').off('hide.bs.dropdown').on('hide.bs.dropdown', function() {
        // $(this).find('.dropdown-menu').first().hide();
    });

    // $(document).off('mouseenter', '.dropdown-menu-hover').on('mouseenter', '.dropdown-menu-hover', function () {
    //     if($(window).width() > 1200)
    //         $(this).find('.dropdown-menu').first().hide().slideDown();
    // });

    $('.js-navbar-toggler').off('click').on('click', function() {
        $(this).toggleClass('open');
    });
}