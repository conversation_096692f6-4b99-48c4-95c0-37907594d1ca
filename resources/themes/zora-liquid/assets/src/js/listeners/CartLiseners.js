import $ from "jquery";
import './../helpers/jquery-plugins/on-last';
import buttonLoader from '../helpers/button-loading';
import modal from "../helpers/modal";
import i18n from "../helpers/i18n";

export default function ()
{
    let app = this;

    let vueCartDetailsRender = function(json) {

        let fullHolder = $('.js-cart-full'),
            vueCartDetails = null;

        if(fullHolder.length < 1) {
            return false;
        }

        let data = {'section_id':'cart-template'};
        if(fullHolder.closest('.modal').length) {
            data.panel = 1;
        }

        $.get(json.cart.url, data, function(response) {
            $('#cc-section-cart-template').replaceWith(response).promise().done(function(){
                app.init($('#cc-section-cart-template'));
            });
        });

        return true;
    };

    let addToCartActionExecute = function(json) {
        let add_to_cart_action = 'ccSettings' in window && 'cart' in ccSettings && 'add_to_cart_action' in ccSettings.cart ? ccSettings.cart['add_to_cart_action'] : 'show_popup',
            compact_cart_panel = 'ccSettings' in window && 'cart' in ccSettings && 'compact_cart_panel' in ccSettings.cart ? ccSettings.cart['compact_cart_panel'] : false;

        switch(add_to_cart_action) {
            case 'checkout_redirect':
                window.location = '/checkout';
            break;
            case 'go_to_cart':
                if(compact_cart_panel) {
                    $('.js-widget-header-loader-holder [data-form-section_id="cart-template"]').trigger('click');
                } else {
                    window.location = '/cart';
                }
            break;
            case 'stay_on_page':
            break;
            case 'show_popup':
            break;
            default:
                modal().init(undefined, {'type': 'success', 'success' : i18n().__('cart.text.successfully_added_product', {product:json.product.name})});
            break;
            case 'none':

            break;
        }
    };

    $(document).off('cc.cart.vue.update').on('cc.cart.vue.update', function(event, element, json) {
        if('cart' in json) {
            let update = vueCartDetailsRender(json);

            $('.js-cart-caret').trigger('cc.ajax.success', [json.cart || {}]);

            if(!update) {
                addToCartActionExecute(json);
            }
        } else if('up_sell' in json) {
            $(document).trigger('cc.cart.up-sell', [json]);
        }
    });

    $(document).off('change', '[data-cart-item-quantity-change]').onLast('change', '[data-cart-item-quantity-change]', function() {
        let element = $(this),
            form = element.closest('form');

        $('.js-cart-update-loader').addClass('loading');

        form.off('cc.ajax.success.quantity cc.ajax.error.quantity').on('cc.ajax.success.quantity', function(e, json) {
            $('.js-cart-update-loader').removeClass('loading');

            $(document).trigger('cc.cart.vue.update', [form, json]);
        }).on('cc.ajax.error.quantity', function() {
            $('.js-cart-update-loader').removeClass('loading');
        });

        form.submit();
    }).off('cc.ajax.success', '[data-cart-item-remove], .js-discount-code-add').on('cc.ajax.success', '[data-cart-item-remove], .js-discount-code-add', function(event, json) {
        if(this.button) {
            this.button.stop();
        }
        $(document).trigger('cc.cart.vue.update', [this, json]);
    }).off('cc.cart.product.addToCart').on('cc.cart.product.addToCart', function(event, json) {
        $(document).trigger('cc.cart.vue.update', [this, json]);
    }).off('cc.ajax.start', '[data-cart-item-remove]').on('cc.ajax.start', '[data-cart-item-remove]:not([data-noanimation])', function() {
        this.button = buttonLoader(this).disable().start();
    }).off('cc.ajax.error', '[data-cart-item-remove]').on('cc.ajax.error', '[data-cart-item-remove]', function() {
        if(this.button) {
            this.button.stop();
        }
    }).off('cc.ajax.success', '.js-cart-panel-remove-discount-code').on('cc.ajax.success', '.js-cart-panel-remove-discount-code', function(event, json) {
        $(document).trigger('cc.cart.vue.update', [this, json]);
        $('.js-cart-update-loader').removeClass('loading');
    }).off('cc.ajax.start', '.js-cart-panel-remove-discount-code').on('cc.ajax.start', '.js-cart-panel-remove-discount-code', function() {
        $('.js-cart-update-loader').addClass('loading');
    }).off('cc.ajax.error', '.js-cart-panel-remove-discount-code').on('cc.ajax.error', '.js-cart-panel-remove-discount-code', function() {
        $('.js-cart-update-loader').removeClass('loading');
    });

    $(document).off('cc.ajax.success', '.js-add-to-cart').on('cc.ajax.success', '.js-add-to-cart', function(event, json) {
        // $(document).trigger('cc.cart.vue.update', [this, json]);
        if(this.button) {
            this.button.stop();
        }
    }).off('cc.ajax.start', '.js-add-to-cart').on('cc.ajax.start', '.js-add-to-cart', function(event) {
        this.button = buttonLoader(this).disable().start();
    }).off('cc.ajax.error', '.js-add-to-cart').on('cc.ajax.error', '.js-add-to-cart', function(event) {
        if(this.button) {
            this.button.stop();
        }
    });
    $(document).on('cc.cart.product.addToCart', function () {
        // $('.js-cart-holder-header a.widget-header').trigger('click');
    });
};