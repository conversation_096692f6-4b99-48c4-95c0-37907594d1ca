import $ from "jquery";
import cookie from '../helpers/cookie-filters'

export default function (options)
{
    options = $.extend({}, {
        infix: 'nan',
        plus: 'fal fa-plus',
        minus: 'fal fa-minus',
        arrowClass: '',
        openedClass: 'open'
    }, options);

    let cookieName = function(text) {
            return text.replace(/[^a-zA-Z0-9]/g,'_');
        },
        toClassSelector = function(text) {
            let result = [];

            for(let i = 0; i < arguments.length; i++) {
                result.push('.' + (new Object(arguments[i])).toString().split(' ').join('.'));
            }

            return result.join(', ');
        },
        filterOpen = function(toggleElement, arrow) {
            $(toggleElement).addClass(options.openedClass);
            $(arrow).html('<i class="' + options.minus + ' ' + options.arrowClass + '"></i>');
        },
        filterClose = function(toggleElement, arrow) {
            $(toggleElement).removeClass(options.openedClass);
            $(arrow).html('<i class="' + options.plus + ' ' + options.arrowClass + '"></i>');
            $(toggleElement).find('[data-' + options.infix + '-toggle]').map(function() {
                let element = $(this),
                    toggle = element.data(options.infix + '-toggle'),
                    arrow = element.find(toClassSelector(options.plus, options.minus)).parent(),
                    toggleElement = $(toggle ? toggle : $());

                filterClose(toggleElement, arrow);
                cookie(options.infix + toggle).remove();
            });
        };

    return $(document).off('click', '[data-' + options.infix + '-toggle]').on('click', '[data-' + options.infix + '-toggle]', function() {
        let element = $(this),
            toggle = element.data(options.infix + '-toggle'),
            arrow = element.find(toClassSelector(options.plus, options.minus)).parent(),
            toggleElement = $(toggle ? toggle : $());

        if(toggleElement.length) {
            if(toggleElement.hasClass(options.openedClass)) {
                filterClose(toggleElement, arrow);
                cookie(options.infix + toggle).remove();
            } else {
                filterOpen(toggleElement, arrow);
                cookie(options.infix + toggle).set(1);
            }
        }
        return false;
    }).find('[data-' + options.infix + '-toggle]').map(function() {
        let element = $(this),
            toggle = element.data(options.infix + '-toggle'),
            opened = element.data(options.infix + '-opened') || false,
            arrow = element.find(toClassSelector(options.plus, options.minus)).parent(),
            toggleElement = $(toggle ? toggle : $());

            if(cookie(options.infix + toggle).get() || opened) {
                filterOpen(toggleElement, arrow);
                element.data(options.infix + '-opened', true);
            } else {
                filterClose(toggleElement, arrow);
                element.data(options.infix + '-opened', false);
            }
    });
}