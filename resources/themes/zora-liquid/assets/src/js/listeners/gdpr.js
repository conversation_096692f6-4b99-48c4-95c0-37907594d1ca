import $ from "jquery";

export default function ()
{
    (function($){
        let d = $(document);

        d.off('click', '#gdpr-trigger').on('click', '#gdpr-trigger', function(e){
            e.preventDefault();
            $('#gdpr_popup').modal();
        }).off('click', '.bottom-freezed-bar-close').on('click', '.bottom-freezed-bar-close', function(e){
            e.preventDefault();
            $('.bottom-freezed-bar').hide();
        }).off('click', '.js-cookies-accept-modal, .js-cookies-accept').on('click', '.js-cookies-accept-modal, .js-cookies-accept', function(e){
            $('#gdpr_popup').modal('hide');
            $('.bottom-freezed-bar').hide();
            Cookie.set('cookies-accepted', '1', {
                path: '/',
                expires: 365
            });
        });

        $(window).off('load.gdpr').on('load.gdpr', function() {
            let gdpr_policies_popup = $('#gdpr_policies_popup').modal({
                backdrop: 'static',
                keyboard: false
            }).modal('show');

            d.on('cc.ajax.success', '.cc-form-policies-popup', function (e) {
                if($(e.toElement||e.target).hasClass('cc-form-policies-popup')) {
                    Cookie.remove('policies_popup');
                    gdpr_policies_popup.modal('hide');
                }
            });

            gdpr_policies_popup.on('click', '.js-policies-cancel', function () {
                Cookie.remove('policies_popup');
                window.location.href = '/auth/logout';
                gdpr_policies_popup.modal('hide');
            });
        });
    })($);
}