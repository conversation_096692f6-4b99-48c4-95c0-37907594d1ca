{% if section.settings.enabled %}
    {% assign = collection = collections[section.settings.latests_collection] %}
    {% if collection.products_count > 0 %}
        <section class="section-content bg padding-y-sm">
            <div class="container-fluid">

                <header class="section-heading heading-line">
                    <h4 class="title-section bg">
                        {{ section.settings.title }}                  
                    </h4>
                    {% if section.settings.is_slider %}
                        <div class="title-section bg title-right">
                            <button type="button" class="slick-prev js-slick-prev"><i class="fal fa-chevron-left"></i></button>
                            <button type="button" class="slick-next js-slick-next"><i class="fal fa-chevron-right"></i></button>
                        </div>
                    {% endif %}
                </header>

                {% if section.settings.is_slider %}
                    <div class="slick-slider-body js-slick-slider" data-slide-items="{{ section.settings.items_per_row | default:4 }}">
                        {% for product in collection.products limit:section.settings.limit %}
                            {% render "product.list.grid" section:sections.list-products %}
                        {% endfor %}
                    </div> <!-- row.// -->
                {% else %}
                    <!-- ========================= from items_per_row convert to class name and capture to itemClass ========================= -->
                    {% render "functions.item-class" %}
                    {% capture "itemClass" %}
                    {% call 'itemClass' per_row:section.settings.items_per_row | default:4 %}
                    {% endcapture %}

                    <div class="row">
                        {% for product in collection.products limit:section.settings.limit %}
                            {% render "product.list.grid" section:sections.list-products %}
                        {% endfor %}
                    </div> <!-- row.// -->
                {% endif %}
            </div><!-- container // -->
        </section>
    {% endif %}
{% endif %}

{% schema %}
{
    "name": {
        "en": "Home page - newest products",
        "bg": "Начална страница - най-нови продукти"
    },
    "settings": [
        {
            "type": "header",
            "content": {
                "en": "Newest products",
                "bg": "Най-нови продукти"
            }
        },
        {
            "type": "collection",
            "id": "latests_collection",
            "label": {
                "en": "Collection",
                "bg": "Колекция"
            },
            "default": "latest-products"
        },
        {
            "type": "checkbox",
            "id": "enabled",
            "label": {
                "en": "Enable latest products",
                "bg": "Разрешаване за показване на последни продукти"
            },
            "default": false
        },
        {
            "type": "checkbox",
            "id": "is_slider",
            "label": {
                "en": "Display latest products as slider",
                "bg": "Показване на последни продукти като въртележка"
            },
            "default": false
        },
        {
            "type": "text",
            "id": "title",
            "label": {
                "en": "Title",
                "bg": "Заглавие"
            }
        },
        {
            "type": "number",
            "id": "limit",
            "label": {
                "en": "Limit",
                "bg": "Брой записи"
            },
            "default": 4,
            "min": 1,
            "step": 1,
            "max": 16
        },
        {
            "type": "select",
            "id": "items_per_row",
            "label": {
                "en": "Items per row",
                "bg": "Брой записи на ред"
            },
            "default": 4,
            "options": [
                {
                    "value": "1",
                    "label": "1"
                },
                {
                    "value": "2",
                    "label": "2"
                },
                {
                    "value": "3",
                    "label": "3"
                },
                {
                    "value": "4",
                    "label": "4"
                },
                {
                    "value": "6",
                    "label": "6"
                }
            ]
        }
    ]
}
{% endschema %}