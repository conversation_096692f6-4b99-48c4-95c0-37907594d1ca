<!-- ========================= SECTION CONTENT ========================= -->
{% render "breadcrumb.line" %}

<section class="section-content bg padding-y-sm">
    <div class="container">
        <form action="{% route 'cart.add' %}" method="post" data-ajax data-reset-success class="product-form js-product-form">
            <input type="hidden" name="_token" readonly="readonly" value="{% csrf_token %}">

            <div class="card product-details">

                <h3 class="title">{{ product.name }}</h3>

                <div class="row no-gutters">
                    <aside class="{% if section.settings.show_side_info %}col-xl-5{% else %}col-xl-6{% endif %}">
                        {% if product.type == 'bundle' %}
                            <div class="m-2">
                                <!-- ========================= from grid_items_per_row convert to class name and capture to itemClass ========================= -->
                                {% render "functions.item-class" %}
                                {% capture "bundleProductsClass" %}
                                {% call 'itemClass' per_row:product.per_row %}
                                {% endcapture %}

                                {% assign productsRows = product.products | chunk:product.per_row %}

                                {% for products in productsRows %}
                                    <div class="row">
                                        {% for product in products %}
                                            <div class="col-sm-6">
                                                {% render "product.list.bundle-product" %}
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <article class="gallery-wrap">
                                {% if product.images.size > 1 %}
                                    <div class="img-small-wrap d-flex flex-column align-items-center pr-3">
                                        <button type="button" class="slick-prev js-slick-prev"><i class="fal fa-angle-up"></i></button>
                                        <div class="slick-slider-body js-slick-gallery" data-slide-items="{{ section.settings.gallery_limit | default:6 }}" data-slide-as-for-change=".js-slick-slider-on-change-{{ product.url_handle }}">
                                            {% for image in product.images %}
                                                {% capture 'sliderImage' %}
                                                    <a class="js-slick-slider-on-change-{{ product.url_handle }}" href="{{ image['1920x1920'] }}" data-toggle="lightbox" data-gallery="product-{{ product.url_handle }}" data-title="{{ image[section.settings.image_size].alt }}" data-type="image">
                                                        <img class="js-lazy lazy-image" src="{{ image['150x150'] }}" data-src="{{ image[section.settings.image_size] }}" alt="{{ image[section.settings.image_size].alt }}" {{ image[section.settings.image_size].htmlDimensions | raw }}>
                                                    </a>
                                                {% endcapture %}
                                                <a href="{{ image['1920x1920'] }}" data-toggle="lightbox" data-gallery="product-{{ product.url_handle }}" data-title="{{ image['150x150'].alt }}" data-type="image" data-change="{{ sliderImage | escape }}">
                                                    <img class="js-lazy lazy-image" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mO8UQ8AAjUBWXO9i8oAAAAASUVORK5CYII=" data-src="{{ image['150x150'] }}" alt="{{ image['150x150'].alt }}">
                                                </a>
                                            {% endfor %}
                                        </div>
                                        <button type="button" class="slick-next js-slick-next"><i class="fal fa-angle-down"></i></button>
                                    </div>
                                {% endif %}
                                <div class="img-big-wrap lazy-bg js-lazy-bg">
                                    <div>
                                        <div class="labels">
                                            {% if product.new %}
                                                <span class="label label-new">{{ 'item.labels.new' | t }}</span>
                                            {% endif %}

                                            {% if product.sale and section.settings.show_sale_label %}
                                                <span class="label label-sale">{{ 'item.labels.sale' | t }}</span>
                                            {% endif %}

                                            {% if product.free_shipping %}
                                                <span class="label label-free-shipping">{{ 'item.labels.free_delivery' | t }}</span>
                                            {% endif %}

                                            {% if product.featured and section.settings.show_featured_label %}
                                                <span class="label label-featured">{{ 'item.labels.featured' | t }}</span>
                                            {% endif %}
                                            {%- comment -%}
                                            {% if product.labels %}
                                                {% for productLabel in product.labels %}
                                                    <div class="label label-label" {% if productLabel.color %}style="background-color: {{ productLabel.color }}" {% endif %}>
                                                        {{ productLabel.name }}
                                                    </div>
                                                {% endfor %}
                                            {% endif %}
                                            {%- endcomment -%}
                                        </div>
                                        {% if product.banners %}
                                            {% for banner in product.banners %}
                                                <div class="labels-banner {{ banner.banner_position }}">
                                                    <img class="labels-banner-image" src="{{ banner.image[section.settings.image_size] }}" alt="{{ banner.image[section.settings.image_size].alt }}">
                                                </div>
                                            {% endfor %}
                                        {% endif %}
                                        {% if product.selected_variant and product.selected_or_first_available_variant.has_image %}
                                            <a class="js-slick-slider-on-change-{{ product.url_handle }}" href="{{ product.selected_or_first_available_variant.image['1920x1920'].url }}" data-toggle="lightbox" data-gallery="product-{{ product.url_handle }}" data-title="{{ product.selected_or_first_available_variant.image[section.settings.image_size].alt }}" data-type="image">
                                                <img class="js-lazy lazy-image js-variant-image" data-image-size="{{ section.settings.image_size }}" src="{{ product.selected_or_first_available_variant.image['150x150'].url }}" data-src="{{ product.selected_or_first_available_variant.image[section.settings.image_size] }}" alt="{{ product.selected_or_first_available_variant.image[section.settings.image_size].alt }}" {{ product.selected_or_first_available_variant.image[section.settings.image_size].htmlDimensions | raw }}>
                                            </a>
                                            {% elsif product.has_image %}
                                            <a class="js-slick-slider-on-change-{{ product.url_handle }}" href="{{ product.image['1920x1920'].url }}" data-toggle="lightbox" data-gallery="product-{{ product.url_handle }}" data-title="{{ product.image[section.settings.image_size].alt }}" data-type="image">
                                                <img class="js-lazy lazy-image js-variant-image" data-image-size="{{ section.settings.image_size }}" src="{{ product.image['150x150'].url }}" data-src="{{ product.image[section.settings.image_size] }}" alt="{{ product.image[section.settings.image_size].alt }}" {{ product.image[section.settings.image_size].htmlDimensions | raw }}>
                                            </a>
                                        {% else %}
                                            <div class="p-2">
                                                <img class="js-lazy lazy-image" src="{{ product.image['150x150'].url }}" data-src="{{ product.image[section.settings.image_size] }}" alt="{{ product.image[section.settings.image_size].alt }}" {{ product.image[section.settings.image_size].htmlDimensions | raw }}>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div> <!-- slider-product.// -->
                            </article> <!-- gallery-wrap .end// -->
                        {% endif %}

                    </aside> <!-- col.// -->
                    <aside class="{% if section.settings.show_side_info %}col-xl-4{% else %}col-xl-6{% endif %}">
                        <article class="pl-lg-4 pr-lg-4 pb-lg-4">
                            <div class="js-msg-ch-box"></div>

                            <div class="js-price-holder">
                                <div class="mb-3">
                                    <var class="h3 d-flex align-items-start justify-content-between">
                                        {% if section.settings.show_price %}
                                        <div class="price">
                                            {% if product.price_from_discounted_formatted %}
                                                <span class="price-new">{{ product.price_from_discounted_formatted }}</span>
                                                <span class="price-old-text">{{ 'item.labels.old_price' | t }} </span><del class="price-old h6">{{ product.price_from_formatted }}</del>
                                                <div class="price-old-text">{{ 'item.labels.saved' | t }} {{ product.price_saved_formatted }}</div>
                                            {% else %}
                                                <span class="price-new">{{ product.price_from_formatted }}</span>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                        <div class="labels labels-wrap">
                                            {% if product.discount && section.settings.show_price %}
                                                <span class="label label-discount label-discount-{{ product.discount.type }}" title="{{ 'item.discount_target' | t }}: {{ product.discount.name }}" {% if product.discount.color %}style="background-color: {{ product.discount.color }}" {% endif %}>
                                                    -&nbsp;{{ product.discount.type_value_formatted }}
                                                </span>
                                            {% endif %}
                                            {% if section.settings.show_sku %}
                                                {% unless product.total_variants > 0 %}
                                                    <dl class="sku-wrap">
                                                        <dt class="sku-text">{{ "item.text.sku" | t }}</dt>
                                                        <dd class="sku-value js-variant-sku">{{ product.variant.sku }}</dd>
                                                    </dl>
                                                {% endunless %}
                                            {% endif %}
                                        </div>
                                    </var>
                                    {% comment %}
                                        <span>/per kg</span>
                                    {% endcomment %}
                                </div> <!-- price-detail-wrap .// -->
                                {% if product.category_properties %}
                                    <hr>
                                    {% render "product.helpers.category-properties" %}
                                {% endif %}
                                {% if section.settings.show_short_description and product.short_description %}
                                    <hr>
                                    <p class="description">{{ product.short_description | raw | strip_html }}</p>
                                {% endif %}
                            </div>

                            {% render "product.properties-images-detailed-page" %}

                            {% if section.settings.page and section.settings.show_page and all_pages[section.settings.page] %}
                                {% if section.settings.show_page_popup %}
                                    <div class="mb-4">

                                        <a href="{{ all_pages[section.settings.page].url }}" class="btn btn-sm btn-primary" data-modal="information-page" data-modal-type="ajax" data-modal-popup-size="md" data-modal-popup-position="right">{{ all_pages[section.settings.page].name }}</a>

                                        {% comment %}
                                            <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#page-{{ settings.show_page.page.settings.show_page.page }}">{{ settings.show_page.page.name }}</button>

                                            <!-- Modal -->
                                            <div class="modal right fade" id="page-{{ settings.show_page.page.settings.show_page.page }}" tabindex="-1" role="dialog">
                                                <div class="modal-dialog modal-md" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">{{ settings.show_page.page.name }}</h5>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">&times;</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            {{ settings.show_page.page.content | raw }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endcomment %}
                                    </div>
                                {% else %}
                                    <dl class="row">
                                        <dt class="col-sm-4">{{ all_pages[section.settings.page].name }}</dt>
                                        <dd class="col-sm-8">{{ all_pages[section.settings.page].content | raw }}</dd>
                                    </dl>
                                {% endif %}
                            {% endif %}

                            {% if product.type == 'bundle' %}
                                <input type="hidden" readonly="readonly" name="bundle_id" value="{{ product.id }}" data-no-reset>
                            {% else %}
                                <input type="hidden" readonly="readonly" name="product_id" value="{{ product.id }}" data-no-reset>
                            {% endif %}

                            {% if product.total_variants > 0 %}
                                <hr>
                                <div class="row">
                                    <div class="col-sm-12">
                                        {% render "product.variants_details" groups:product.variants_groups variants:product.variants %}
                                    </div> <!-- col.// -->
                                </div> <!-- row.// -->
                            {% endif %}
                            {% if product.options.size > 0 %}
                                <hr>
                                <div class="row">
                                    <div class="col-sm-12">
                                        {% render "product.options_details" options:product.options %}
                                    </div> <!-- col.// -->
                                </div> <!-- row.// -->
                            {% endif %}
                            {% if section.settings.show_compare or section.settings.show_wishlist %}
                                <hr>
                                {% comment %}
                                    {% render "product.helpers.compare-favorite" %}
                                {% endcomment %}
                                <div class="row">
                                    {% if section.settings.show_wishlist %}
                                        <div class="ml-3 mr-3">
                                            <a class="add-to-favorite{% if product.favorite %} active{% endif %}" href="{{ product.wishlist_url }}" data-id="{{ product.id }}" data-url="{{ product.wishlist_url }}" data-widget="product-wishlist">
                                                <i class="{% if product.favorite %}fas{% else %}fal{% endif %} fa-heart"></i>
                                                {% unless product.favorite %}
                                                    <span class="form-check-label" data-action="text">{{ "item.favorite.add" | t }}</span>
                                                    {% else %}
                                                    <span class="form-check-label" data-action="text">{{ "item.favorite.remove" | t }}</span>
                                                {% endunless %}
                                            </a>
                                        </div>
                                    {% endif %}
                                    {% if section.settings.show_compare %}
                                        <div class="col text-left">
                                            <label class="form-check checkbox-fad product-compare">
                                                <input class="form-check-input" data-widget="product-compare" type="checkbox" value="{{ product.id }}">
                                                <i class="fal fa-sync"></i>
                                                <span class="form-check-label">{{ "item.compare" | t }}</span>
                                            </label>
                                        </div>
                                    {% endif %}
                                </div> <!-- rating-wrap.// -->
                            {% endif %}
                            <div class="js-buy-button-holder">
                                {% if product.total_variants > 0 %}
                                    <product-details-add-to-cart-details v-bind:variant="variant" v-if="variant" v-bind:settings="{{ section.settings | json }}"></product-details-add-to-cart-details>
                                {% else %}
                                    <div class="row mt-5">
                                        <div class="col-sm-5 d-none">
                                            <dl class="dlist-inline">
                                                <dt><label for="product-quantity-chose">{{ "item.text.quantity" | t }}:</label> </dt>
                                                <dd>
                                                    <input type="number" name="quantity" id="product-quantity-chose" class="form-control" value="1" data-reset-value-default="1" min="1" max="{{ product.variant.quantity_change }}" data-widget-number-spinner>
                                                    <input type="hidden" readonly="readonly" name="variant_id" value="{{ product.variant.id }}" data-no-reset>
                                                </dd>
                                            </dl>  <!-- item-property .// -->
                                        </div> <!-- col.// -->
                                        <div class="col-sm-12">
                                            {% if section.settings.show_buy_button %}
                                                <button class="btn btn-info btn-buy-details js-submit" type="submit" data-submit><i class="far fa-shopping-cart"></i>{{ "item.actions.add_to_cart" | t }}</button>
                                            {% endif %}
                                        </div> <!-- col.// -->
                                    </div> <!-- row.// -->
                                    {% comment %}
                                        <hr>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <button class="btn btn-info" type="button"><i class="fad fa-bags-shopping"></i> Buy now </button>
                                            </div> <!-- col.// -->
                                        </div> <!-- row.// -->
                                    {% endcomment %}
                                {% endif %}
                            </div>
                        </article> <!-- card-body.// -->
                    </aside> <!-- col.// -->
                    {% if  section.settings.show_side_info and (section.settings.side_info_content or section.settings.side_info_links) %}
                    <aside class="col-xl-3 pl-xl-4 my-xl-0 my-5 product-extra-wrap">
                        {% if product.special %}
                        <div class="labels position-static mb-2">
                            <span class="label label-special m-0 py-2">
                                {% if product.special.to %}
                                    {{ 'item.labels.special.to' | t:'to',product.special.to.date }}
                                {% endif %}
                            </span>
                        </div>
                        {% endif %}
                        {% render 'product.countdown' %}
                        {% if product.labels %}
                            <div class="labels position-static mb-2">
                            {% for productLabel in product.labels %}
                                <div class="label label-label ml-0" {% if productLabel.color %}style="background-color: {{ productLabel.color }}" {% endif %}>
                                    {{ productLabel.name }}
                                </div>
                            {% endfor %}
                            </div>
                        {% endif %}

                        {% if product.banners %}
                            {% for banner in product.banners %}
                                <div class="labels-banner {{ banner.banner_position }}">
                                    <img class="labels-banner-image" src="{{ banner.image[section.settings.image_size] }}" alt="{{ banner.image[section.settings.image_size].alt }}">
                                </div>
                            {% endfor %}
                        {% endif %}

                        {% if section.settings.side_info_links %}
                            {% assign links = linklists[section.settings.side_info_links].links %}
                            {% if links.size > 0 %}
                                {% render "navigations.product-extra" links:links %}
                            {% endif %}
                        {% endif %}
                        {% for tab in product.tabs %}
                            {% if tab.name contains "Гаранция" %}
                            {{ tab.content | raw }}
                            {% endif %}
                        {% endfor %}
                        {{ section.settings.side_info_bottom_content | raw }}
                    </aside> <!-- col.// -->
                    {% endif %}
                </div> <!-- row.// -->

                {% render "product.make-tabs-detailed-page" %}

            </div> <!-- card.// -->
        </form>

        {% unless QUERY_STRING contains 'section_id=product-template' %}
            {% if section.settings.related_products_enabled %}
                {% assign findCollection = false %}
                {% for pCollection in product.collections %}
                    {% for collection in collections %}
                        {% if pCollection.url_handle == collection.url_handle and collection.products_count > 2 %}
                            {% assign findCollection = collection %}
                            {% break %}
                        {% endif %}
                    {% endfor %}
                {% endfor %}

                {% if findCollection and findCollection.products_count > 2 %}
                    <section class="section-content bg padding-y-sm">
                        <div class="container-fluid">

                            <header class="section-heading heading-line">
                                <h4 class="title-section bg text-uppercase">{{ section.settings.related_products_title }}</h4>
                                {% if section.settings.related_products_is_slider %}
                                    <div class="title-section bg title-right">
                                        <button type="button" class="slick-prev js-slick-prev"><i class="fal fa-chevron-left"></i></button>
                                        <button type="button" class="slick-next js-slick-next"><i class="fal fa-chevron-right"></i></button>
                                    </div>
                                {% endif %}
                            </header>

                            {% if section.settings.related_products_is_slider %}
                                <div class="slick-slider-body js-slick-slider" data-slide-items="{{ section.settings.related_items_per_row | default:4 }}">
                                    {% for p in findCollection.products limit:section.settings.related_products_limit %}
                                        {% if p.url_handle == product.url_handle %}
                                            {% continue %}
                                        {% endif %}
                                        {% render "product.list.grid" product:p section:sections.list-products %}
                                    {% endfor %}
                                </div> <!-- row.// -->
                            {% else %}
                                <!-- ========================= from items_per_row convert to class name and capture to itemClass ========================= -->
                                {% render "functions.item-class" %}
                                {% capture "itemClass" %}
                                {% call 'itemClass' per_row:section.settings.related_items_per_row | default:4 %}
                                {% endcapture %}

                                <div class="row">
                                    {% for p in findCollection.products limit:section.settings.related_products_limit %}
                                        {% if p.url_handle == product.url_handle %}
                                            {% continue %}
                                        {% endif %}
                                        {% render "product.list.grid" product:p section:sections.list-products %}
                                    {% endfor %}
                                </div> <!-- row.// -->
                            {% endif %}
                        </div><!-- container // -->
                    </section>
                {% endif %}
            {% endif %}
        {% endunless %}
    </div>
</section>

{% schema %}
{
    "name": {
        "en": "Product Page",
        "bg": "Продуктова страница"
    },
    "settings": [
        {
            "type": "header",
            "content": {
                "en": "Image settings",
                "bg": "Настройки на снимките"
            }
        },
        {
            "type": "image_size_select",
            "id": "image_size",
            "required": true,
            "label": {
                "en": "Select image size",
                "bg": "Изберете размер на снимките"
            },
            "default": "600x600"
        },
        {
        "type": "header",
        "content": {
        "en": "Related products",
        "bg": "Подобни продукти"
        }
        },
        {
        "type": "checkbox",
        "id": "related_products_enabled",
        "label": {
        "en": "Enable related products",
        "bg": "Разрешаване за показване на подобни продукти"
        },
        "default": false
        },
        {
            "type": "checkbox",
            "id": "related_products_is_slider",
            "label": {
            "en": "Display related products as slider",
            "bg": "Показване на подобни продукти като въртележка"
        },
            "default": false
        },
        {
        "type": "text",
        "id": "related_products_title",
        "label": {
        "en": "Title",
        "bg": "Заглавие"
        }
        },
        {
        "type": "number",
        "id": "related_products_limit",
        "label": {
        "en": "Limit",
        "bg": "Брой записи"
        },
        "default": 4,
        "min": 1,
        "step": 1,
        "max": 16
        },
        {
        "type": "select",
        "id": "related_items_per_row",
        "label": {
        "en": "Items per row",
        "bg": "Брой записи на ред"
        },
        "default": 4,
        "options": [
        {
        "value": "1",
        "label": "1"
        },
        {
        "value": "2",
        "label": "2"
        },
        {
        "value": "3",
        "label": "3"
        },
        {
        "value": "4",
        "label": "4"
        },
        {
        "value": "6",
        "label": "6"
        }
        ]
        },
        {
        "type": "select",
        "id": "related_items_compare",
        "label": {
        "en": "Related products by",
        "bg": "Подобни продукти по"
        },
        "default": 4,
        "options": [
        {
        "value": "category",
        "label": {
        "en": "Filter by category",
        "bg": "Филтрирай по категория"
        }
        },
        {
        "value": "vendor",
        "label": {
        "en": "Filter by vendor",
        "bg": "Филтрирай по производител"
        }
        },
        {
        "value": "tag",
        "label": {
        "en": "Filter by tag",
        "bg": "Филтрирай по таг"
        }
        }
        ]
        },
        {
        "type": "select",
        "id": "related_items_sort",
        "label": {
        "en": "Order by",
        "bg": "Подреди по"
        },
        "default": "id-desc",
        "options": [
        {
        "value": "id-asc",
        "label": {
        "en": "Date added ascending",
        "bg": "Дата на добавяне - най-стари"
        }
        },
        {
        "value": "id-desc",
        "label": {
        "en": "Date added descending",
        "bg": "Дата на добавяне - най-нови"
        }
        },
        {
        "value": "name-asc",
        "label": {
        "en": "Name ascending",
        "bg": "Име (A-Z)"
        }
        },
        {
        "value": "name-desc",
        "label": {
        "en": "Name descending",
        "bg": "Име (Z-A)"
        }
        },
        {
        "value": "match",
        "label": {
        "en": "Matches (Filter tag)",
        "bg": "Брой съвпадения (Филтър таг)"
        }
        },
        {
        "value": "rand",
        "label": {
        "en": "Rand",
        "bg": "Случаен принцип"
        }
        },
        {
        "value": "price-asc",
        "label": {
        "en": "Price ascending",
        "bg": "Ниска към висока цена"
        }
        },
        {
        "value": "price-desc",
        "label": {
        "en": "Price descending",
        "bg": "Висока към ниска цена"
        }
        }
        ]
        },
        {
            "type": "header",
            "content": "Side Info"
        },
        {
            "type": "checkbox",
            "id": "show_side_info",
            "label": "Show side info",
            "default": true
        },
        {
            "type": "textarea",
            "id": "side_info_top_content",
            "label": "Side info top content"
        },
        {
            "type": "link_list",
            "id": "side_info_links",
                "label": {
                    "en": "Links",
                    "bg": "Линкове"
                }
        },
        {
            "type": "textarea",
            "id": "side_info_bottom_content",
            "label": "Side info bottom content"
        },
        {
            "type": "header",
            "content": {
                "en": "Visibility settings",
                "bg": "Настройки за видимост"
            }
        },
        {
            "type": "select",
            "id": "gallery_limit",
            "label": {
            "en": "Limit for gallery thumbnails display",
            "bg": "Колко снимки да се виждат за галерията"
            },
            "default": 6,
            "options": "1-10"
        },
        {
            "type": "checkbox",
            "id": "show_price",
            "label": {
                "en": "Show price",
                "en": "Показване на цената"
            },
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_price_logged",
            "label": "Show price only for logged users",
            "default": false
        },
        {
            "type": "checkbox",
            "id": "show_buy_button",
            "label": "Show buy button",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_status",
            "label": "Show status",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_quantity_in_status",
            "label": "Show quantity in status",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_sku",
            "label": "Show sku",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_vendor",
            "label": "Show vendor",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_category",
            "label": "Show category",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_category_properties",
            "label": "Show category properties",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_category_properties_images",
            "label": "Show category properties images",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_gallery",
            "label": "Show gallery",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_compare",
            "label": "Show compare",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_wishlist",
            "label": "Show wishlist",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_share_enabled",
            "label": "Show share enabled",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_share_show_counter",
            "label": "Show share show counter",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_share_show_compact",
            "label": "Show share show compact",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_share_show_top_services",
            "label": "Show share show top services",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_share_ui_click",
            "label": "Show share ui click",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_share_layout",
            "label": "Show share layout",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_share_ui_hover_direction",
            "label": "Show share ui hover direction",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_share_default_content",
            "label": "Show share default content",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_sale_label",
            "label": "Show sale label",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_featured_label",
            "label": "Show featured label",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_choose_quantity",
            "label": "Show choose quantity",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_description",
            "label": "Show description",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_short_description",
            "label": "Show short description",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_page",
            "label": "Show page"
        },
        {
            "type": "pages_select",
            "id": "page",
            "label": "Page"
        },
        {
            "type": "checkbox",
            "id": "show_page_popup",
            "label": "Show page in popup",
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_common_products_carousel",
            "label": "Show common products carousel",
            "default": true
        }
    ]
}
{% endschema %}