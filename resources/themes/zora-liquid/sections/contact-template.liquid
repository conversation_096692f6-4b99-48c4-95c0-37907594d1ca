<!-- ========================= SECTION CONTENT ========================= -->
{% render "breadcrumb.line" %}

<section class="section-content bg padding-y-sm">
    <div class="container">

        <header class="section-heading heading-line heading-center">
            <h4 class="title-section bg text-uppercase">{{ title }}</h4>
        </header>

        {% if section.settings.contact_form_enable or section.settings.page_text_enable%}
            <div class="row{{ settings.map.enabled | ifelse,' mb-2' }}">
                {% if section.settings.page_text_enable%}
                    <div class="col-sm-{{ section.settings.contact_form_enable | ifelse,3,12 }}">{{ section.settings.page_text | raw }}</div>
                {% endif %}

                {% if section.settings.contact_form_enable %}
                    <div class="col-sm-{{ section.settings.page_text_enable| ifelse,9,12 }}">
                        <form action="{% route "contacts" %}" method="post" data-ajax data-reset-success data-grecaptcha>
                            <input type="hidden" name="_token" readonly="readonly" value="{% csrf_token %}">

                            {% guest %}
                                <div class="row mb-2">
                                    <div class="col">
                                        <input type="text" class="form-control" placeholder="{{ "customer.label.first_name" | t }}" name="first_name">
                                    </div>
                                    <div class="col">
                                        <input type="text" class="form-control" placeholder="{{ "customer.label.last_name" | t }}" name="last_name">
                                    </div>
                                    <div class="col">
                                        <input type="text" class="form-control" placeholder="{{ "customer.label.email" | t }}" name="email">
                                    </div>
                                </div>
                            {% endguest %}

                            <div class="row mb-2">
                                <div class="col">
                                    <input type="text" class="form-control" placeholder="{{ "contacts.label.subject" | t }}" value="{{ subject }}" name="subject" data-reset-value-success>
                                </div>
                            </div>

                            <div class="row mb-2">
                                <div class="col">
                                    <textarea class="form-control" placeholder="{{ "contacts.label.message" | t }}" rows="5" name="message" data-reset-value-success>{{ message }}</textarea>
                                </div>
                            </div>

                            {% gdpr 'contacts' as 'contacts_gdpr' %}
                                <div class="row mb-2">
                                    {% for gdpr in contacts_gdpr %}
                                        <div class="col-sm-12 col-md-4">
                                            <div class="custom-control custom-checkbox js-error-field-gdpr-{{ gdpr.id }}" data-fill-on-error="append">
                                                <input type="checkbox" class="custom-control-input" id="contacts-gdpr-{{ gdpr.url_handle }}" name="gdpr[{{ gdpr.id }}]">
                                                {% capture 'gdpr_name' %}
                                                    <a href="{{ gdpr.url }}" data-target="#gdpr-policy-{{ gdpr.url_handle }}" data-modal="information-page" data-modal-type="ajax" data-modal-popup-size="sm" data-modal-popup-position="right">{{ gdpr.name }}</a>
                                                {% endcapture %}
                                                <label class="custom-control-label" for="contacts-gdpr-{{ gdpr.url_handle }}">{{ "gdpr.text.accept" |t:"policy",gdpr_name | raw }}</label>
                                            </div>
                                        </div>

                                        <!-- Modal -->
                                        <div class="modal right fade" id="gdpr-policy-{{ gdpr.url_handle }}" tabindex="-1" role="dialog">
                                            <div class="modal-dialog modal-md" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">{{ gdpr.name }}</h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        {{ gdpr.content | raw }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endgdpr %}

                            <div class="row mb-2">
                                <div class="col-9">
                                    {% googleReCaptcha "field" "hidden" %}
                                </div>
                                <div class="col-3 text-right">
                                    <button class="btn btn-primary" type="submit" data-submit>{{ "button.send" | t }}</button>
                                </div>
                            </div>

                        </form>
                    </div>
                {% endif %}
            </div>
        {% endif %}

        {% if section.settings.map_enable and section.settings.map %}
            <div class="row">
                <div class="col-sm-12">

                    <div class="_google-map"
                         {% if section.settings.map.bounds %}
                         data-bounds="{{ section.settings.map.bounds | json }}"
                         {% endif %}
                         {% if section.settings.map.center %}
                         data-center="{{ section.settings.map.center | json }}"
                         {% endif %}
                         data-map-options="{{ section.settings.map | json }}"
                         data-markers="{{ section.settings.map.pins | json }}"
                         data-script="{{ google_map_script }}"
                         data-widget="extra.googleMap"
                         id="google-map"
                         style="height: {{ section.settings.map.height | default:500 }}px;"
                    ></div>

                </div>
            </div>
        {% endif %}

    </div>
</section>

{% script %}
{% googleReCaptcha "init" %}
{% endscript %}

<!-- meta data -->
{% assign description = section.settings.page_text_enable|ifelse:settings.page_text|strip_html|raw %}
{% capture 'url' %}{% route 'contacts' %}{% endcapture %}
{% render "metadata.global" name:title description:description url:url %}

{% schema %}
{
    "name": {
        "en": "Contact Page",
        "bg": "Страница контакти"
    },
    "settings": [
        {
            "type": "header",
            "content": {
                "en": "Visibility settings",
                "bg": "Настройки за видимост"
            }
        },
        {
            "type": "checkbox",
            "id": "contact_form_enable",
            "label": {
                "en": "Show Contact form",
                "bg": "Показване на формата за контакти"
            },
            "default": true
        },
        {
            "type": "checkbox",
            "id": "page_text_enable",
            "label": {
                "en": "Show Contact information",
                "bg": "Показване на контактната информация"
            },
            "default": true
        },
        {
            "type": "checkbox",
            "id": "map_enable",
            "label": {
                "en": "Show Contact map",
                "bg": "Показване на карта"
            },
            "default": true
        },
        {
            "type": "header",
            "content": {
                "en": "Contact Information",
                "bg": "Контактна информация"
            }
        },
        {
            "type": "textarea",
            "id": "page_text",
            "label": {
                "en": "Contacts",
                "bg": "Контакти"
            }
        },
        {
            "type": "header",
            "content": {
                "en": "Map settings",
                "bg": "Настройки на картата"
            }
        },
        {
            "type": "map",
            "id": "map"
        }
    ]
}
{% endschema %}