<div class="modal fade" id="gdpr_policies_popup" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'gdpr.privacy.notice' | t }}</h5>
            </div>
            <form class="cc-form cc-form-policies-popup" method="post" data-ajax role="form" action="{{ form_action }}">
                <div class="modal-body">
                    <div>
                        {{ policies_popup_text | raw }}
                    </div>

                    {% if policies.size > 0 %}
                        <ul class="list-unstyled">
                            {% for policy in policies %}
                                <li>
                                    <div class="custom-control custom-checkbox mr-sm-2 mt-1 float-left">
                                        <input name="gdpr[{{ policy.id }}]" type="checkbox" class="custom-control-input" id="gdpr-input-{{ policy.id }}" {% unless policy.optional %}required="required"{% endunless %}>
                                        <label class="custom-control-label" for="gdpr-input-{{ policy.id }}">
                                            <a href="{{ policy.url }}" data-modal="information-page" data-modal-type="ajax" data-modal-popup-size="md" data-modal--backdrop="static">{{ policy.name }}</a>
                                        </label>
                                    </div>
                                </li>
                            {% endfor %}
                        </ul>
                    {% endif %}

                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">{{ 'sf.privacy.accept_settings' | t }}</button>
                    <button type="button" class="btn btn-secondary js-policies-cancel" data-dismiss="modal">{{ 'sf.global.act.logout' | t }}</button>
                </div>
            </form>
        </div>
    </div>
</div>