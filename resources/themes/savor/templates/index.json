{"sections": {"hero_RkimKJ": {"type": "hero", "blocks": {"group_3cB4GW": {"type": "group", "settings": {"open_in_new_tab": false, "content_direction": "column", "vertical_on_mobile": true, "horizontal_alignment": "flex-start", "vertical_alignment": "center", "align_baseline": false, "horizontal_alignment_flex_direction_column": "flex-start", "vertical_alignment_flex_direction_column": "center", "gap": 12, "width": "custom", "custom_width": 34, "width_mobile": "custom", "custom_width_mobile": 53, "height": "fit", "custom_height": 100, "inherit_color_scheme": true, "color_scheme": "", "background_media": "none", "video_position": "cover", "background_image_position": "cover", "border": "none", "border_width": 1, "border_opacity": 100, "border_radius": 0, "toggle_overlay": false, "overlay_color": "#00000026", "overlay_style": "solid", "gradient_direction": "to top", "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {"text_KxeJ6r": {"type": "text", "settings": {"text": "<p>Savor every. last. bite. </p>", "width": "100%", "max_width": "normal", "alignment": "left", "type_preset": "h2", "font": "var(--font-primary--family)", "font_size": "", "line_height": "normal", "letter_spacing": "normal", "case": "none", "wrap": "pretty", "color": "", "background": false, "background_color": "#00000026", "corner_radius": 0, "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {}}, "button_nazDaa": {"type": "button", "settings": {"label": "Shop all →", "link": "shopify://collections/all", "open_in_new_tab": false, "style_class": "link", "width": "fit-content", "custom_width": 100, "width_mobile": "fit-content", "custom_width_mobile": 100}, "blocks": {}}}, "block_order": ["text_KxeJ6r", "button_nazDaa"]}}, "block_order": ["group_3cB4GW"], "settings": {"open_in_new_tab": false, "media_type_1": "image", "media_type_2": "image", "content_direction": "column", "vertical_on_mobile": true, "horizontal_alignment": "flex-start", "vertical_alignment": "center", "align_baseline": false, "horizontal_alignment_flex_direction_column": "flex-start", "vertical_alignment_flex_direction_column": "center", "gap": 16, "section_width": "page-width", "section_height": "large", "section_height_custom": 50, "color_scheme": "scheme-4", "toggle_overlay": false, "overlay_color": "#000000", "overlay_style": "solid", "gradient_direction": "to top", "padding-block-start": 0, "padding-block-end": 0}}, "product_list_X8Q7bi": {"type": "product-list", "blocks": {"static-header": {"type": "_product-list-content", "static": true, "settings": {"content_direction": "column", "vertical_on_mobile": true, "horizontal_alignment": "flex-start", "vertical_alignment": "center", "align_baseline": false, "horizontal_alignment_flex_direction_column": "flex-start", "vertical_alignment_flex_direction_column": "center", "gap": 12, "width": "fill", "custom_width": 100, "width_mobile": "fill", "custom_width_mobile": 100, "height": "fit", "custom_height": 100, "inherit_color_scheme": true, "color_scheme": "", "background_media": "none", "video_position": "cover", "background_image_position": "cover", "border": "none", "border_width": 1, "border_opacity": 100, "border_radius": 0, "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {}}, "static-product-card": {"type": "product-card", "name": "t:names.product_card", "static": true, "settings": {"product": "{{ closest.product }}", "product_card_gap": 0, "inherit_color_scheme": false, "color_scheme": "scheme-2", "border": "none", "border_width": 1, "border_opacity": 100, "border_radius": 0, "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {"product_card_gallery_Gn6P7T": {"type": "_product-card-gallery", "name": "t:names.product_card_media", "settings": {"product": "{{ closest.product }}", "image_ratio": "square", "border": "solid", "border_width": 0, "border_opacity": 0, "border_radius": 0, "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {}}, "group_AUJmQt": {"type": "group", "name": "t:names.group", "settings": {"open_in_new_tab": false, "content_direction": "column", "vertical_on_mobile": true, "horizontal_alignment": "space-between", "vertical_alignment": "flex-start", "align_baseline": false, "horizontal_alignment_flex_direction_column": "flex-start", "vertical_alignment_flex_direction_column": "space-between", "gap": 4, "width": "fill", "custom_width": 100, "width_mobile": "fill", "custom_width_mobile": 100, "height": "fit", "custom_height": 100, "inherit_color_scheme": true, "color_scheme": "scheme-2", "background_media": "none", "video_position": "cover", "background_image_position": "cover", "border": "none", "border_width": 1, "border_opacity": 100, "border_radius": 0, "toggle_overlay": false, "overlay_color": "#00000026", "overlay_style": "solid", "gradient_direction": "to top", "padding-block-start": 12, "padding-block-end": 12, "padding-inline-start": 12, "padding-inline-end": 12}, "blocks": {"group_pLH7Cd": {"type": "group", "name": "t:names.group", "settings": {"open_in_new_tab": false, "content_direction": "row", "vertical_on_mobile": true, "horizontal_alignment": "space-between", "vertical_alignment": "center", "align_baseline": false, "horizontal_alignment_flex_direction_column": "flex-start", "vertical_alignment_flex_direction_column": "flex-start", "gap": 8, "width": "fill", "custom_width": 75, "width_mobile": "custom", "custom_width_mobile": 75, "height": "fit", "custom_height": 100, "inherit_color_scheme": true, "color_scheme": "", "background_media": "none", "video_position": "cover", "background_image_position": "cover", "border": "none", "border_width": 1, "border_opacity": 100, "border_radius": 0, "toggle_overlay": false, "overlay_color": "#00000026", "overlay_style": "solid", "gradient_direction": "to top", "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {"product_title_gtg38q": {"type": "product-title", "name": "t:names.product_title", "settings": {"product": "{{ closest.product }}", "width": "fit-content", "max_width": "normal", "alignment": "left", "type_preset": "h5", "font": "var(--font-body--family)", "font_size": "1rem", "line_height": "normal", "letter_spacing": "normal", "case": "none", "wrap": "pretty", "color": "var(--color-foreground)", "background": false, "background_color": "#00000026", "corner_radius": 0, "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {}}, "price_Vz3EzL": {"type": "price", "name": "t:names.product_price", "settings": {"product": "{{ closest.product }}", "show_sale_price_first": true, "show_installments": false, "show_tax_info": false, "type_preset": "paragraph", "width": "fit-content", "alignment": "right", "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {}}}, "block_order": ["product_title_gtg38q", "price_Vz3EzL"]}, "group_3Fwdtp": {"type": "group", "name": "t:names.group", "settings": {"open_in_new_tab": false, "content_direction": "column", "vertical_on_mobile": true, "horizontal_alignment": "flex-start", "vertical_alignment": "center", "align_baseline": false, "horizontal_alignment_flex_direction_column": "flex-start", "vertical_alignment_flex_direction_column": "flex-start", "gap": 12, "width": "fit-content", "custom_width": 100, "width_mobile": "fill", "custom_width_mobile": 25, "height": "fit", "custom_height": 100, "inherit_color_scheme": true, "color_scheme": "", "background_media": "none", "video_position": "cover", "background_image_position": "cover", "border": "none", "border_width": 1, "border_opacity": 100, "border_radius": 0, "toggle_overlay": false, "overlay_color": "#00000026", "overlay_style": "solid", "gradient_direction": "to top", "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {"text_JHMjjF": {"type": "text", "name": "t:names.text", "settings": {"text": "<p>{{ closest.product.metafields.descriptors.subtitle.value }}</p>", "width": "100%", "max_width": "none", "alignment": "left", "type_preset": "paragraph", "font": "var(--font-primary--family)", "font_size": "var(--font-size--body-md)", "line_height": "normal", "letter_spacing": "normal", "case": "none", "wrap": "pretty", "color": "", "background": false, "background_color": "#00000026", "corner_radius": 0, "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {}}}, "block_order": ["text_JHMjjF"]}}, "block_order": ["group_pLH7Cd", "group_3Fwdtp"]}}, "block_order": ["product_card_gallery_Gn6P7T", "group_AUJmQt"]}}, "name": "t:names.products_grid", "settings": {"collection": "all", "layout_type": "grid", "carousel_on_mobile": false, "max_products": 6, "columns": 3, "mobile_columns": "1", "columns_gap": 1, "rows_gap": 1, "icons_style": "arrow", "icons_shape": "none", "section_width": "full-width", "horizontal_alignment": "flex-start", "gap": 0, "color_scheme": "scheme-1", "padding-block-start": 0, "padding-block-end": 0}}, "section_RhpYRt": {"type": "section", "blocks": {"video_NqWNaD": {"type": "video", "name": "t:names.video", "settings": {"source": "url", "video_url": "", "video_autoplay": false, "video_loop": true, "alt": "", "custom_width": 67, "custom_width_mobile": 100, "aspect_ratio": "16/9", "border": "none", "border_width": 1, "border_opacity": 100, "border_radius": 0, "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {}}, "group_TEanyT": {"type": "group", "name": "Caption", "settings": {"open_in_new_tab": false, "content_direction": "column", "vertical_on_mobile": true, "horizontal_alignment": "space-between", "vertical_alignment": "center", "align_baseline": false, "horizontal_alignment_flex_direction_column": "flex-start", "vertical_alignment_flex_direction_column": "center", "gap": 12, "width": "custom", "custom_width": 33, "width_mobile": "fill", "custom_width_mobile": 100, "height": "fit", "custom_height": 100, "inherit_color_scheme": true, "color_scheme": "", "background_media": "none", "video_position": "cover", "background_image_position": "cover", "border": "none", "border_width": 1, "border_opacity": 100, "border_radius": 0, "toggle_overlay": false, "overlay_color": "#00000026", "overlay_style": "solid", "gradient_direction": "to top", "padding-block-start": 40, "padding-block-end": 40, "padding-inline-start": 40, "padding-inline-end": 40}, "blocks": {"text_BRFzjN": {"type": "text", "name": "t:names.heading", "settings": {"text": "<h3>A Family Tradition of Bold, Fresh Flavor</h3>", "width": "fit-content", "max_width": "normal", "alignment": "left", "type_preset": "h4", "font": "var(--font-primary--family)", "font_size": "", "line_height": "normal", "letter_spacing": "normal", "case": "none", "wrap": "pretty", "color": "", "background": false, "background_color": "#00000026", "corner_radius": 0, "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {}}, "text_tKxhbY": {"type": "text", "name": "t:names.text", "settings": {"text": "<p>Food has the power to connect generations. Whether it’s learning family recipes or the intense memories triggered by the flavors and aromas of our childhood.</p>", "width": "fit-content", "max_width": "normal", "alignment": "left", "type_preset": "rte", "font": "var(--font-primary--family)", "font_size": "", "line_height": "normal", "letter_spacing": "normal", "case": "none", "wrap": "pretty", "color": "", "background": false, "background_color": "#00000026", "corner_radius": 0, "padding-block-start": 0, "padding-block-end": 0, "padding-inline-start": 0, "padding-inline-end": 0}, "blocks": {}}}, "block_order": ["text_BRFzjN", "text_tKxhbY"]}}, "block_order": ["video_NqWNaD", "group_TEanyT"], "name": "t:names.video_section", "settings": {"content_direction": "row", "vertical_on_mobile": true, "horizontal_alignment": "flex-start", "vertical_alignment": "center", "align_baseline": false, "horizontal_alignment_flex_direction_column": "flex-start", "vertical_alignment_flex_direction_column": "center", "gap": 6, "section_width": "full-width", "section_height": "", "section_height_custom": 50, "color_scheme": "scheme-3", "background_media": "none", "video_position": "cover", "background_image_position": "cover", "border": "none", "border_width": 1, "border_opacity": 100, "border_radius": 0, "toggle_overlay": false, "overlay_color": "#00000026", "overlay_style": "solid", "gradient_direction": "to top", "padding-block-start": 0, "padding-block-end": 0}}}, "order": ["hero_<PERSON><PERSON><PERSON><PERSON>", "product_list_X8Q7bi", "section_RhpYRt"]}