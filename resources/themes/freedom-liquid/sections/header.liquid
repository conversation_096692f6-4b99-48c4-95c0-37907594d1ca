{% if section.settings.show_text_line and section.settings.text_line_text %}
<div class="alert alert-danger rounded-0">{{ section.settings.text_line_text }}</div>
{% endif %}
<header class="section-header">
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="{{ shop.url }}">
                {% if shop.logo.main %}
                    <img class="logo" src="{{ shop.logo.main }}" alt="{{ shop.name }}">
                {% else %}
                    <h1 class="_logo-text">{{ shop.name }}</h1>
                {% endif %}
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarTop" aria-controls="navbarTop" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarTop">
                {% assign links = linklists[section.settings.main_menu].links %}
                {% if links.size > 0 %}
                    {% render "navigations.main" links:links %}
                {% endif %}
            </div> <!-- collapse.// -->
        </div>
    </nav>

    <section class="header-main shadow-sm">
        <div class="container">
            <div class="row align-items-center">
                {% assign links = linklists[section.settings.second_menu].links %}
                {% if links.size > 0 %}
                <div class="col-lg-4 col-4">
                    <div class="category-wrap dropdown py-1">
                        <button type="button" class="btn btn-light  dropdown-toggle" data-toggle="dropdown">
                            <i class="fa fa-bars"></i>
                            <span class="d-none d-md-inline">{{ section.settings.second_menu_text }}</span>
                        </button>
                        <div class="dropdown-menu">
                            {% render "navigations.account-header" links:links %}
                        </div>
                    </div>
                </div>
                {% endif %}
                <div class="{% unless links.size > 0 %}offset-lg-4 col-12 {% else %}col-8 {% endunless %}col-lg-5">
                    <form action="{% route "product.search" %}" class="py-1">
                        <div class="input-group w-100">
                            <input type="text" name="search" class="form-control" placeholder="{{ "global.action.search" | t }}">
                            <div class="input-group-append">
                                <button class="btn btn-warning" type="submit">
                                    <i class="fa fa-search"></i> <span class="d-none d-md-inline">{{ "button.search" | t }}</span>
                                </button>
                            </div>
                        </div>
                    </form> <!-- search-wrap .end// -->
                </div> <!-- col.// -->

                <div class="col-lg-3 col-12">
                    <div class="widgets-wrap float-right row no-gutters py-1">
                        <div class="col-auto">
                            {% auth %}
                                {% render "header.customer-menu" customer_menu:section.settings.customer_menu %}
                            {% else %}
                                {% render "header.login" %}
                            {% endauth %}
                        </div> <!-- col.// -->
                        <div class="col-auto position-static js-widget-header-loader-holder">
                            <div class="cart-holder dropdown-menu-hover">
                                <a href="{% route "cart" %}" class="widget-header" {% if ccSettings.compact_cart_panel %}data-form-section_id="cart-template" data-form-panel="1" data-modal-history="{% route "cart" %}" data-modal="ajax" data-modal-popup-size="sm" data-modal-popup-position="right"{% endif %}>
                                    <div class="icontext">
                                        <div class="icon-wrap"><i class="text-warning icon-sm fa fa-shopping-cart"></i></div>
                                        <div class="text-wrap text-dark">
                                            <span class="small round badge badge-danger position-absolute pst-0 psr-0 d-none js-cart-caret js-widget-header-loader" data-page-load data-url="/cart.json" data-page-load-html="variants_count" data-page-load-template=".js-cart-holder-header">0</span>
                                            <div class="position-absolute psb-1 psr-1">
                                                <i class="fa fa-caret-down"></i>
                                            </div>
                                        </div>
                                    </div>
                                </a>

                                <div class="js-cart-holder-header" data-helper="headerCart"></div>
                            </div>
                        </div> <!-- col.// -->
                        <div class="col-auto position-static js-widget-header-loader-holder">
                            <div class="wishlist-holder dropdown-menu-hover">
                                <a href="{% route "wishlist" %}" class="widget-header" data-toggle="dropdown">
                                    <div class="icontext">
                                        <div class="icon-wrap"><i class="text-warning icon-sm  fa fa-heart"></i></div>
                                        <div class="text-wrap text-dark">
                                            <span class="small round badge badge-danger position-absolute absolute-top pst-0 psr-0 d-none js-wishlist-caret js-widget-header-loader" {% auth %}data-page-load data-url="/wishlist.json"{% endauth %} data-page-load-html="products_count" data-page-load-template=".js-wishlist-holder-header">0</span>
                                            <div class="position-absolute psb-1 psr-1">
                                                <i class="fa fa-caret-down"></i>
                                            </div>
                                        </div>
                                    </div>
                                </a>

                                <div class="js-wishlist-holder-header" data-helper="headerWishlist"></div>
                            </div>
                        </div> <!-- col.// -->
                    </div> <!-- widgets-wrap.// row.// -->
                </div> <!-- col.// -->
            </div> <!-- row.// -->
        </div> <!-- container.// -->
    </section> <!-- header-main .// -->
</header> <!-- section-header.// -->

{% schema %}
{
    "name": {
        "en": "Header",
        "bg": "Горна част"
    },
    "settings": [
        {
            "type": "header",
            "content": {
                "en": "Menu",
                "bg": "Меню"
            }
        },
        {
            "type": "link_list",
            "id": "main_menu",
            "label": {
                "en": "Main Menu",
                "bg": "Основно меню"
            }
        },
        {
            "type": "text",
            "id": "second_menu_text",
            "label": {
                "en": "Second Menu button text",
                "bg": "Текст на бутона за допълнителното меню"
            }
        },
        {
            "type": "link_list",
            "id": "second_menu",
            "label": {
                "en": "Second Menu",
                "bg": "Допълнително меню"
            }
        },
        {
            "type": "link_list",
            "id": "customer_menu",
            "label": {
                "en": "Customer Menu",
                "bg": "Клиентско меню"
            }
        },
        {
            "type": "header",
            "content": {
                "en": "Text line",
                "bg": "Лента с текст"
            }
        },
        {
            "type": "checkbox",
            "id": "show_text_line",
            "label": {
                "en": "Show text line",
                "en": "Показване на текстовата лента"
            },
            "default": true
        },
        {
            "type": "text",
            "id": "text_line_text",
            "label": "Title Text 2",
            "default": "This is some line text"
        },
        {
            "type": "header",
            "content": "Search"
        },
        {
            "type": "search",
            "id": "autocomplete",
            "label": "Enable autocomplete",
            "default": true
        }
    ]
}
{% endschema %}