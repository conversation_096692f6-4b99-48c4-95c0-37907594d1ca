<section class="section-content bg padding-y-sm">
    <div class="container">

        <header class="section-heading heading-line heading-center">
            <h4 class="title-section bg text-uppercase">{{ page.name }}</h4>
        </header>

        <article>
            {% if page.type == 'faq' %}
                <div class="accordion" id="faqPageAccordion">
                    <ul>
                        {% for faq in page.faq %}
                            <li>
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="accordion-title" id="faqPageHeading{{ forloop.key }}">
                                            <h2 class="mb-0" data-toggle="collapse" data-target="#faqPageBody{{ forloop.key }}" aria-expanded="{% if forloop.first %}true{% else %}false{% endif %}" aria-controls="faqPageBody{{ forloop.key }}">
                                                {{ faq.question }}
                                            </h2>
                                        </div>
                                        <div id="faqPageBody{{ forloop.key }}" class="collapse{% if forloop.first %} show{% endif %}" aria-labelledby="faqPageHeading{{ forloop.key }}" data-parent="#faqPageAccordion">
                                            <p>{{ faq.answer | raw }}</p>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            {% else %}
                {{ page.content | raw }}
            {% endif %}
        </article>
    </div>
</section>