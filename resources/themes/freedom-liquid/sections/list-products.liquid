<!-- ========================= SECTION CONTENT ========================= -->
{% unless hide_products %}
    {% render "breadcrumb.products-list" %}
{% else %}
    {% render "breadcrumb.line" %}
{% endunless %}

<!-- ========================= from grid_items_per_row convert to class name and capture to itemClass ========================= -->
{% render "functions.item-class" %}
{% capture "itemClass" %}
{% call 'itemClass' per_row:section.settings.items_per_row %}
{% endcapture %}


<section class="section-content bg padding-y-sm">
    <div class="container">

        <header class="section-heading heading-line heading-center">
            <h4 class="title-section bg text-uppercase">{{ title }}</h4>
            {% comment %}
            <div class="bg position-relative float-right pl-2">
                {{ "global.text.total_products" | t:"total",pagination.items }}
            </div>
            {% endcomment %}
        </header>

        {% if section.settings.with.display_subcategories and categories.size > 0 %}
        <div class="row">
            {% for category in categories %}
                <div class="col-sm-3 mb-2">
                    <div class="card">
                        <h5 class="h6 p-1 text-center mb-1">
                            <a href="{{ category.url }}">{{ category.name }}</a>
                        </h5>
                        <a href="{{ category.url }}"{% unless category.has_image %} class="p-1"{% endunless %}>
                            <img class="card-img-top" src="{{ category.image['300x300'].url }}" alt="{{ category.image['300x300'].alt }}">
                        </a>
                    </div><!-- card.// -->
                </div>
            {% endfor %}
        </div>
        <div class="heading-line mb-4"></div>
        {% endif %}

        {% unless hide_products %}
        <div class="row js-products-list">
            {% if section.settings.filters.enabled %}
                <div class="col-lg-3 col-sm-12">
                    {% render "filters" %}
                </div>
            {% endif %}
            <div class="{% if section.settings.filters.enabled %}col-lg-9 {% endif %}col-sm-12">
                {% render "loader" class:"js-loading-products" %}

                {% if section.settings.filters.active_filters and active_filters.size > 0 %}
                    {% render "product-list-active-filters" %}
                {% endif %}

                {% render "functions.item-list-grid" %}

                <div class="product-cards {% if layoutStyle == 'list' and products.size > 0 %}card-list bg-white{% else %}row{% endif %}">
                    {% for product in products %}
                        {% render "product.list.grid" %}
                    {% else %}
                        <div class="alert alert-danger col" role="alert">
                            {{ "global.error.no_results" | t }}
                        </div>
                    {% endfor %}
                </div>

                {% render "pagination" class:"js-products-pagination" %}

            </div>
        </div>
        {% endunless %}

    </div>
</section>

{% schema %}
    {
    "name": {
        "en": "All Products Lists",
        "bg": "Продуктов листинг"
    },
    "settings": [
        {
            "type": "header",
            "content": {
                "en": "Image settings",
                "bg": "Настройки на снимките"
            }
        },
        {
            "type": "image_size_select",
            "id": "image_size",
            "required": true,
            "label": {
                "en": "Select image size",
                "bg": "Изберете размер на снимките"
            },
            "default": "300x300"
        },
        {
            "type": "header",
            "content": {
                "en": "Order settings",
                "bg": "Други настройки"
            }
        },
        {
            "type": "order_by_select",
            "id": "order_by",
            "label": {
                "en": "Allow order by",
                "bg": "Разреши сортиране по"
            },
            "default": "price_from-asc"
        },
        {
            "type": "header",
            "content": {
                "en": "Grid settings",
                "bg": "Настройки на показването"
            }
        },
        {
            "type": "checkbox",
            "id": "is_grid",
            "label": {
                "en": "Default display as grid",
                "bg": "Показване по подразбиране 'Мрежа'"
            },
            "default": true
        },
        {
            "type": "select",
            "id": "items_per_row",
            "label": {
                "en": "Items per row",
                "bg": "Брой записи на ред"
            },
            "info": {
                "en": "Used where 'Default display as grid' is on",
                "bg": "Използва се когато 'Показване по подразбиране 'Мрежа'' е пуснат"
            },
            "default": 3,
            "options": [
                {
                    "value": "2",
                    "label": "2"
                },
                {
                    "value": "3",
                    "label": "3"
                },
                {
                    "value": "4",
                    "label": "4"
                },
                {
                    "value": "6",
                    "label": "6"
                }
            ]
        },
        {
            "type": "header",
            "content": {
                "en": "Per page settings",
                "bg": "Настройки на страница"
            }
        },
        {
            "type": "items_per_page",
            "id": "per_page",
            "label": {
                "en": "Enable per page",
                "bg": "Разрешаване на брой на страница"
            },
            "default": 9
        },
        {
            "type": "header",
            "content": {
                "en": "Filters",
                "bg": "Филтри"
            }
        },
        {
            "type": "filters",
            "id": "filters",
            "label": {
                "en": "Enable filters",
                "bg": "Разрешаване на филтрите"
            },
            "default": {
                enabled: true,
                active_filters: true,
                filters: {
                    "categories": true,
                    "price_ranges": true,
                    "variants": true,
                    "vendors": true,
                    "category_properties": true,
                    "other": {
                        "featured": true,
                        "new": true,
                        "sale": true
                    }
                },
                "sorting": [
                    "other",
                    "categories",
                    "price_ranges",
                    "variants",
                    "vendors",
                    "category_properties"
                ]
            }
        },
        {
            "type": "header",
            "content": {
                "en": "Additional product information",
                "bg": "Допълнителна информация към продукта"
            },
            "info": {
                "en": "In this section you has enable category properties, images or/and variants",
                "bg": "Взимане на допълнителна информация за характеристика по категория, снимки и/или разновидност"
            }
        },
        {
            "type": "with",
            "id": "with"
        },
        {
            "type": "header",
            "content": {
                "en": "Visibility settings",
                "bg": "Настройки за видимост"
            }
        },
        {
            "type": "number",
            "id": "name_length",
            "label": {
                "en": "Split product name",
                "bg": "Лимит на името на продукта"
            },
            "default": 20,
            "min": 0,
            "info": {
                "en": "What part of the product name to show. Leave it blank for no limit.",
                "bg": "Каква част от името на продукта да бъде показана. Оставете празно за да е без лимит."
            }
        },
        {
            "type": "checkbox",
            "id": "show_wishlist",
            "label": {
                "en": "Show add to wishlist",
                "bg": "Показване на \"добавяне към списъка с желания\""
            },
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_compare",
            "label": {
                "en": "Show compare",
                "bg": "Показване на \"Сравнение на продукти\""
            },
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_price",
            "label": {
                "en": "Show price",
                "en": "Показване на цената"
            },
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_price_logged",
            "label": {
                "en": "Show price only for logged users",
                "bg": "Показване на цена само за регистрирани потребители"
            },
            "default": false
        },
        {
            "type": "checkbox",
            "id": "show_buy_button",
            "label": {
                "en": "Show buy button",
                "bg": "Показване на бутона \"Купи\""
            },
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_quick_view",
            "label": {
                "en": "Show quick view",
                "bg": "Показване на бърз преглед"
            },
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_short_description",
            "label": {
                "en": "Show short description",
                "bg": "Показване на кратко описание"
            },
            "default": true
        },
        {
            "type": "number",
            "id": "short_description_length",
            "label": {
                "en": "Split short description",
                "bg": "Лимит на краткото описание"
            },
            "default": 50,
            "min": 0,
            "info": {
                "en": "What part of the short description to show. Leave it blank for no limit.",
                "bg": "Каква част от краткото описание да бъде показана. Оставете празно за да е без лимит."
            }
        },
        {
            "type": "checkbox",
            "id": "show_sale_label",
            "label": {
                "en": "Show sale label",
                "bg": "Показване на етекета \"На разпродажба\""
            },
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_featured_label",
            "label": {
                "en": "Show featured label",
                "en": "Показване на етекета \"Препоръчан\""
            },
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_list_grid",
            "label": {
                "en": "Show list grid switch",
                "en": "Превключване между \"Мрежа\" и \"Лист\""
            },
            "default": true
        },
        {
            "type": "checkbox",
            "id": "show_out_of_stock_label",
            "label": {
                "en": "Show out of stock label",
                "en": "Показване на етекета \"Няма наличност\""
            },
            "default": true
        }
    ]
}
{% endschema %}