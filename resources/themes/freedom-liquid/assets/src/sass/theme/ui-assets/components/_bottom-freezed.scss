
/*  Bottom bar
-------------------------------------------------------------------------------*/

.bottom-freezed-bar {
  background-color: $gray-600;
  width: 100%;
  padding: 20px;
  position: fixed;
  bottom: 0;
  left: 0;
  color: $white;
  font-size: 14px;
  line-height: 1.5;
  z-index: 1000;

  a {
    color: #fff;
    text-decoration: underline;

    &:hover {
      text-decoration: none;
    }
  }
}

.bottom-freezed-bar-inner {
  display: table;
  width: 100%;
}

.bottom-freezed-bar-content,
.bottom-freezed-bar-buttons {
  display: table-cell;
  vertical-align: middle;
}

.bottom-freezed-bar-buttons {
  border-left: 1px solid $border;
  padding-left: 30px;
  white-space: nowrap;
  line-height: 1.2;

  a,
  .cc-button {
    vertical-align: middle;
  }

  .cc-button {
    margin-left: 30px;
  }
}

.bottom-freezed-bar-content {
  padding-right: 30px;
}

.bottom-freezed-bar-close {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translate(0, -50%);
  font-size: 18px;
  line-height: 1;
}

@media (max-width: 1024px) {
  .bottom-freezed-bar-buttons {
    white-space: normal;
    text-align: center;
    padding-right: 30px;

    .cc-button {
      width: 100%;
      margin: 10px 0 0;
      white-space: nowrap;

      &:first-child {
        margin-top: 0;
      }
    }
  }
}

@media (max-width: 767px) {
  .bottom-freezed-bar {
    padding: 10px 0;
  }

  .bottom-freezed-bar-inner,
  .bottom-freezed-bar-content,
  .bottom-freezed-bar-buttons {
    display: block;
    padding: 0;
    border: 0;
  }

  .bottom-freezed-bar-content {
    padding-right: 10px;
    margin-bottom: 10px;
  }

  .bottom-freezed-bar-close {
    transform: none;
    top: 10px;
    right: 10px;
  }
}