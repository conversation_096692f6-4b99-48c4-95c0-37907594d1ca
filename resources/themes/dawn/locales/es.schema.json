{"settings_schema": {"colors": {"name": "Colores", "settings": {"background": {"label": "Fondo"}, "background_gradient": {"label": "Degradado de fondo", "info": "El degradado de fondo reemplaza el fondo donde es posible."}, "text": {"label": "Texto"}, "button_background": {"label": "Fondo de botón sólido"}, "button_label": {"label": "Etiqueta de botón sólido"}, "secondary_button_label": {"label": "Botón de contorno"}, "shadow": {"label": "Sombra"}}}, "typography": {"name": "Tipografía", "settings": {"type_header_font": {"label": "Fuente"}, "header__1": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header__2": {"content": "<PERSON><PERSON><PERSON>"}, "type_body_font": {"label": "Fuente"}, "heading_scale": {"label": "Escala"}, "body_scale": {"label": "Escala"}}}, "social-media": {"name": "Redes sociales", "settings": {"social_twitter_link": {"label": "X / Twitter", "info": "https://x.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://www.facebook.com/ShopifyES/"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Cuentas de redes sociales"}}}, "currency_format": {"name": "Formato de moneda", "settings": {"currency_code_enabled": {"label": "Códigos de moneda"}, "paragraph": "Los precios en el carrito y la página de pago siempre muestran códigos de moneda"}}, "layout": {"name": "Diseño", "settings": {"page_width": {"label": "<PERSON><PERSON>"}, "spacing_sections": {"label": "Espacio entre las secciones de la plantilla"}, "header__grid": {"content": "Cuadrícula"}, "paragraph__grid": {"content": "Afecta a áreas con varias columnas o filas"}, "spacing_grid_horizontal": {"label": "Espacio horizontal"}, "spacing_grid_vertical": {"label": "Espacio vertical"}}}, "search_input": {"name": "Comportamiento de búsqueda", "settings": {"predictive_search_enabled": {"label": "Sugerencias de búsqueda"}, "predictive_search_show_vendor": {"label": "Proveedor de producto", "info": "Se muestra cuando las sugerencias de búsqueda están activadas"}, "predictive_search_show_price": {"label": "Precio del producto", "info": "Se muestra cuando las sugerencias de búsqueda están activadas"}}}, "global": {"settings": {"header__border": {"content": "<PERSON>rde"}, "header__shadow": {"content": "Sombra"}, "blur": {"label": "Difuminar"}, "corner_radius": {"label": "Radio de esquina"}, "horizontal_offset": {"label": "Desalineación horizontal"}, "vertical_offset": {"label": "Desalineación vertical"}, "thickness": {"label": "Grosor"}, "opacity": {"label": "Opacidad"}, "image_padding": {"label": "<PERSON><PERSON><PERSON>"}, "text_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}, "label": "Alineación de texto"}}}, "badges": {"name": "<PERSON><PERSON><PERSON>", "settings": {"position": {"options__1": {"label": "Abajo a la izquierda"}, "options__2": {"label": "Abajo a la derecha"}, "options__3": {"label": "Arriba a la izquierda"}, "options__4": {"label": "Arriba a la derecha"}, "label": "Posición de las tarjetas"}, "sale_badge_color_scheme": {"label": "Esquema de color de distintivo de oferta"}, "sold_out_badge_color_scheme": {"label": "Esquema de color de emblema de agotado"}}}, "buttons": {"name": "Botones"}, "variant_pills": {"name": "Botones de variantes", "paragraph": "Los botones de variantes son una forma de mostrar las [variantes de producto](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#variant-picker-block)"}, "inputs": {"name": "Entradas"}, "content_containers": {"name": "Contenedores de contenido"}, "popups": {"name": "Menús desplegables y ventanas emergentes", "paragraph": "Afecta áreas como los menús desplegables de navegación, los modales emergentes y los carritos emergentes"}, "media": {"name": "Multimedia"}, "drawers": {"name": "<PERSON><PERSON><PERSON>"}, "cart": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "Tipo", "drawer": {"label": "Lateral"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "notification": {"label": "Notificación emergente"}}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "show_cart_note": {"label": "Nota del carrito"}, "cart_drawer": {"header": "Carrito lateral", "collection": {"label": "Colección", "info": "Se muestra cuando el carrito lateral está vacío"}}}}, "cards": {"name": "Tarjetas de producto", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Tarjeta"}, "label": "<PERSON><PERSON><PERSON>"}}}, "collection_cards": {"name": "Tarjetas de colección", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Tarjeta"}, "label": "<PERSON><PERSON><PERSON>"}}}, "blog_cards": {"name": "Tarjetas de blogs", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Tarjeta"}, "label": "<PERSON><PERSON><PERSON>"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "<PERSON><PERSON>"}, "favicon": {"label": "Favicon", "info": "Se muestra en 32 x 32 px"}}}, "brand_information": {"name": "Información de marca", "settings": {"brand_headline": {"label": "Titular"}, "brand_description": {"label": "Descripción"}, "brand_image": {"label": "Imagen"}, "brand_image_width": {"label": "<PERSON><PERSON>"}, "paragraph": {"content": "Se muestra en el bloque de información de marca del pie de página"}}}, "animations": {"name": "Animaciones", "settings": {"animations_reveal_on_scroll": {"label": "Revelar secciones al desplazarse"}, "animations_hover_elements": {"options__1": {"label": "Ninguna"}, "options__2": {"label": "Elevación vertical"}, "label": "Efecto hover", "info": "Afecta a tarjetas y botones", "options__3": {"label": "Elevación 3D"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "<PERSON><PERSON><PERSON>", "padding_top": "Arriba", "padding_bottom": "Abajo"}, "spacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colors": {"label": "Esquema de colores", "has_cards_info": "Para cambiar el esquema de color de la tarjeta, actualiza la configuración del tema."}, "heading_size": {"label": "Tamaño del título", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}, "options__4": {"label": "Extra grande"}, "options__5": {"label": "Superextragrande"}}, "image_shape": {"options__1": {"label": "Predeterminado"}, "options__2": {"label": "Arco"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Comillas angulares izquierda"}, "options__5": {"label": "Comillas angulares derecha"}, "options__6": {"label": "Diamante"}, "options__7": {"label": "Paralelogramo"}, "options__8": {"label": "Redonda"}, "label": "Forma de la imagen"}, "animation": {"content": "Animaciones", "image_behavior": {"options__1": {"label": "Ninguna"}, "options__2": {"label": "Movimiento de ambiente"}, "label": "Animación", "options__3": {"label": "Posición del fondo fija"}, "options__4": {"label": "Amp<PERSON><PERSON> al desplazarse"}}}}, "announcement-bar": {"name": "Barra de anuncios", "blocks": {"announcement": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "Texto", "default": "Te damos la bienvenida a nuestra tienda"}, "text_alignment": {"label": "Alineación de texto", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}}, "link": {"label": "Enlace"}}}}, "settings": {"auto_rotate": {"label": "Rotar los anuncios automáticamente"}, "change_slides_speed": {"label": "Cambiar cada"}, "show_social": {"label": "Íconos de redes sociales", "info": "[Gestionar cuentas de redes sociales](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "Selector de país o región", "info": "[Gestionar países o regiones](/admin/settings/markets)"}, "enable_language_selector": {"label": "Selector de idioma", "info": "[Gestionar idiomas](/admin/settings/languages)"}, "heading_utilities": {"content": "Utilidades"}, "paragraph": {"content": "Aparece solo en pantallas grandes"}}, "presets": {"name": "Barra de anuncios"}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Collage multimedia"}, "desktop_layout": {"label": "Diseño", "options__1": {"label": "Bloque grande primero"}, "options__2": {"label": "Bloque grande último"}}, "mobile_layout": {"label": "Diseño para móviles", "options__1": {"label": "Collage"}, "options__2": {"label": "Columna"}}, "card_styles": {"label": "Estilo de <PERSON>rjet<PERSON>", "info": "Gestionar estilos de tarjetas individuales en [configuración de temas](/editor?context=theme&category=product%20cards)", "options__1": {"label": "Usar estilos de tarjeta individuales"}, "options__2": {"label": "Definir todos los estilos como tarjetas de producto"}}, "header_layout": {"content": "Diseño"}}, "blocks": {"image": {"name": "Imagen", "settings": {"image": {"label": "Imagen"}}}, "product": {"name": "Producto", "settings": {"product": {"label": "Producto"}, "secondary_background": {"label": "Mostrar fondo secundario"}, "second_image": {"label": "Mostrar segunda imagen al pasar el cursor"}}}, "collection": {"name": "Colección", "settings": {"collection": {"label": "Colección"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Imagen de portada"}, "video_url": {"label": "URL", "info": "El video se reproduce en una ventana emergente si la sección contiene otros bloques.", "placeholder": "Utiliza una URL de YouTube o Vimeo"}, "description": {"label": "Texto alternativo del video", "info": "Describe el video para los clientes que usan lectores de pantalla. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)", "default": "Describir el video"}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Lista de colecciones", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Colecciones"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "show_view_all": {"label": "Botón \"Ver todo\"", "info": "Visible si la lista tiene más colecciones que las que se muestran"}, "columns_desktop": {"label": "Columnas"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "columns_mobile": {"label": "Columnas", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_layout": {"content": "Diseño"}}, "blocks": {"featured_collection": {"name": "Colección", "settings": {"collection": {"label": "Colección"}}}}, "presets": {"name": "Lista de colecciones"}}, "contact-form": {"name": "Formulario de contacto", "presets": {"name": "Formulario de contacto"}, "settings": {"title": {"default": "Formulario de contacto", "label": "Encabezado"}}}, "custom-liquid": {"name": "Liquid personalizado", "settings": {"custom_liquid": {"label": "Código de Liquid", "info": "Agrega fragmentos de la aplicación u otros códigos para crear personalizaciones avanzadas. [Más información](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Liquid personalizado"}}, "featured-blog": {"name": "Artículos de blog", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Artículos del blog"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Publicar recuento"}, "show_view_all": {"label": "Botón \"Ver todo\"", "info": "Visible si el blog tiene más publicaciones de las que se muestran"}, "show_image": {"label": "Imagen destacada"}, "show_date": {"label": "<PERSON><PERSON>"}, "show_author": {"label": "Autor"}, "columns_desktop": {"label": "Columnas"}, "layout_header": {"content": "Diseño"}, "text_header": {"content": "Texto"}}, "presets": {"name": "Artículos de blog"}}, "featured-collection": {"name": "Colección destacada", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Colección destacada"}, "collection": {"label": "Colección"}, "products_to_show": {"label": "Recuento de productos"}, "show_view_all": {"label": "Botón \"Ver todo\"", "info": "Visible si la colección tiene más productos que los que se muestran"}, "header": {"content": "Tarjeta de producto"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "show_rating": {"label": "Calificación del producto", "info": "Se requiere una aplicación para las calificaciones. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "columns_desktop": {"label": "Columnas"}, "description": {"label": "Descripción"}, "show_description": {"label": "Mostrar descripción de la colección desde el panel de control"}, "description_style": {"label": "Estilo de descripción", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "view_all_style": {"label": "<PERSON><PERSON><PERSON> \"Ver todos\"", "options__1": {"label": "Enlace"}, "options__2": {"label": "Botón con contorno"}, "options__3": {"label": "Botón sólido"}}, "enable_desktop_slider": {"label": "<PERSON><PERSON><PERSON>"}, "full_width": {"label": "Productos de ancho completo"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "columns_mobile": {"label": "Columnas", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "enable_quick_buy": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "header_text": {"content": "Texto"}, "header_collection": {"content": "Diseño de colección"}}, "presets": {"name": "Colección destacada"}}, "footer": {"name": "Pie de página", "blocks": {"link_list": {"name": "Menú", "settings": {"heading": {"label": "Encabezado", "default": "En<PERSON><PERSON>"}, "menu": {"label": "Menú"}}}, "text": {"name": "Texto", "settings": {"heading": {"label": "Encabezado", "default": "Encabezado"}, "subtext": {"label": "Subtexto", "default": "<p>Comparte la información de contacto, los detalles de la tienda y el contenido de la marca con tus clientes.</p>"}}}, "brand_information": {"name": "Información de marca", "settings": {"paragraph": {"content": "Gestionar información de marcas en [configuración de tema](/editor?context=theme&category=brand%20information)"}, "show_social": {"label": "Íconos de redes sociales", "info": "[Gestionar cuentas de redes sociales](/editor?context=theme&category=social%20media)"}}}}, "settings": {"newsletter_enable": {"label": "Suscriptor de correo electrónico"}, "newsletter_heading": {"label": "Encabezado", "default": "Suscribirse a nuestros correos electrónicos"}, "header__1": {"content": "Suscriptor de correo electrónico", "info": "Suscriptores agregados [perfiles de clientes](https://help.shopify.com/manual/customers/manage-customers)"}, "show_social": {"label": "Íconos de redes sociales", "info": "[Gestionar cuentas de redes sociales](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "Selector de país o región", "info": "[Gestionar países o regiones](/admin/settings/markets)"}, "enable_language_selector": {"label": "Selector de idioma", "info": "[Gestionar idiomas](/admin/settings/languages)"}, "payment_enable": {"label": "Íconos de forma de pago"}, "margin_top": {"label": "Margen superior"}, "show_policy": {"label": "Enlaces a las políticas", "info": "[Gestionar políticas](/admin/settings/legal)"}, "header__9": {"content": "Utilidades"}, "enable_follow_on_shop": {"label": "<PERSON><PERSON><PERSON> en <PERSON>", "info": "Shop Pay debe estar activado. [Más información](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}}}, "header": {"name": "Encabezado", "settings": {"logo_position": {"label": "Posición del logo", "options__1": {"label": "Centrado a la izquierda"}, "options__2": {"label": "Arriba a la izquierda"}, "options__3": {"label": "Superior centrada"}, "options__4": {"label": "Centrado en el medio"}}, "menu": {"label": "Menú"}, "show_line_separator": {"label": "Línea separadora"}, "margin_bottom": {"label": "Margen inferior"}, "menu_type_desktop": {"label": "Tipo de menú", "options__1": {"label": "Desplegable"}, "options__2": {"label": "Mega menú"}, "options__3": {"label": "Menú lateral"}}, "mobile_logo_position": {"label": "Posición del logo en dispositivo móvil", "options__1": {"label": "Centro"}, "options__2": {"label": "Iz<PERSON>erda"}}, "logo_help": {"content": "Editar tu logo en [configuración del tema](/editor?context=theme&category=logo)"}, "sticky_header_type": {"label": "Encabezado fijo", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Al desplazarse hacia arriba"}, "options__3": {"label": "Siempre"}, "options__4": {"label": "Reducir el tamaño del logo siempre"}}, "enable_country_selector": {"label": "Selector de país o región", "info": "[Gestionar países o regiones](/admin/settings/markets)"}, "enable_language_selector": {"label": "Selector de idioma", "info": "[Gestionar idiomas](/admin/settings/languages)"}, "header__1": {"content": "Color"}, "menu_color_scheme": {"label": "Esquema de colores del menú"}, "enable_customer_avatar": {"label": "Avatar de cuenta de cliente", "info": "Solo visible cuando los clientes iniciaron sesión con Shop. [Gestionar cuentas de clientes](/admin/settings/customer_accounts)"}, "header__utilities": {"content": "Utilidades"}}}, "image-banner": {"name": "<PERSON> de imagen", "settings": {"image": {"label": "Imagen 1"}, "image_2": {"label": "Imagen 2"}, "stack_images_on_mobile": {"label": "Apilar imágenes"}, "show_text_box": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_overlay_opacity": {"label": "Opacidad de sobreposición"}, "show_text_below": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_height": {"label": "Altura", "options__1": {"label": "Adaptar a la primera imagen"}, "options__2": {"label": "Pequeño"}, "options__3": {"label": "Mediano"}, "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "Arriba a la izquierda"}, "options__2": {"label": "Arriba en el centro"}, "options__3": {"label": "Arriba a la derecha"}, "options__4": {"label": "Centrado a la izquierda"}, "options__5": {"label": "Centrado en el medio"}, "options__6": {"label": "Centrado a la derecha"}, "options__7": {"label": "Abajo a la izquierda"}, "options__8": {"label": "Abajo en el centro"}, "options__9": {"label": "Abajo a la derecha"}, "label": "Posición"}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación"}, "mobile": {"content": "Diseño mó<PERSON>"}, "content": {"content": "Contenido"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON> de imagen"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Texto", "default": "Ofrece a los clientes información sobre las imágenes del banner o el contenido de la plantilla."}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON><PERSON>"}}}, "buttons": {"name": "Botones", "settings": {"button_label_1": {"label": "Etiqueta", "info": "Dejar en blanco para ocultar", "default": "Etiqueta de botón"}, "button_link_1": {"label": "Enlace"}, "button_style_secondary_1": {"label": "Estilo <PERSON>"}, "button_label_2": {"label": "Etiqueta", "info": "Dejar en blanco para ocultar", "default": "Etiqueta de botón"}, "button_link_2": {"label": "Enlace"}, "button_style_secondary_2": {"label": "Estilo <PERSON>"}, "header_1": {"content": "Botón 1"}, "header_2": {"content": "Botón 2"}}}}, "presets": {"name": "<PERSON> de imagen"}}, "image-with-text": {"name": "Imagen con texto", "settings": {"image": {"label": "Imagen"}, "height": {"options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeño"}, "options__3": {"label": "Mediano"}, "label": "Altura", "options__4": {"label": "Grande"}}, "layout": {"options__1": {"label": "Imagen primero"}, "options__2": {"label": "<PERSON>n segunda"}, "label": "Colocación"}, "desktop_image_width": {"options__1": {"label": "Pequeña"}, "options__2": {"label": "Mediana"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON>"}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}, "label": "Alineación"}, "desktop_content_position": {"options__1": {"label": "Arriba"}, "options__2": {"label": "Centrada"}, "options__3": {"label": "Abajo"}, "label": "Posición"}, "content_layout": {"options__1": {"label": "<PERSON> solapamiento"}, "options__2": {"label": "Solapamiento"}, "label": "Diseño"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}, "label": "Alineación móvil"}, "header": {"content": "Contenido"}, "header_colors": {"content": "Colores"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Imagen con texto"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Texto", "default": "<p>Vincula un texto con una imagen para atraer la atención hacia tu producto, colección o artículo de blog seleccionados. Agrega detalles sobre disponibilidad y estilo, o incluso ofrece una reseña.</p>"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}}}}, "button": {"name": "Botón", "settings": {"button_label": {"label": "Etiqueta", "info": "Dejar en blanco para ocultar", "default": "Etiqueta de botón"}, "button_link": {"label": "Enlace"}, "outline_button": {"label": "Estilo <PERSON>"}}}, "caption": {"name": "Leyenda", "settings": {"text": {"label": "Texto", "default": "Agregar un eslogan"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Subtítulo"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "<PERSON><PERSON>", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Imagen con texto"}}, "main-article": {"name": "Artí<PERSON>lo de <PERSON>", "blocks": {"featured_image": {"name": "Imagen destacada", "settings": {"image_height": {"label": "Altura de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"blog_show_date": {"label": "<PERSON><PERSON>"}, "blog_show_author": {"label": "Autor"}}}, "content": {"name": "Contenido"}, "share": {"name": "Compartir", "settings": {"text": {"label": "texto", "default": "Compartir"}}}}}, "main-blog": {"name": "Artículos de blog", "settings": {"show_image": {"label": "Imagen destacada"}, "show_date": {"label": "<PERSON><PERSON>"}, "show_author": {"label": "Autor"}, "layout": {"label": "Diseño", "options__1": {"label": "Cuadrícula"}, "options__2": {"label": "Collage"}}, "image_height": {"label": "Altura de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}}}}, "main-cart-footer": {"name": "Subtotal", "blocks": {"subtotal": {"name": "Precio subtotal"}, "buttons": {"name": "Botón de pago"}}}, "main-cart-items": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-collection-banner": {"name": "Banner de colección", "settings": {"paragraph": {"content": "La información de la colección se [gestiona en tu panel de control](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Descripción"}, "show_collection_image": {"label": "Imagen"}}}, "main-collection-product-grid": {"name": "Cuadrícula de productos", "settings": {"products_per_page": {"label": "Productos por página"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "enable_tags": {"label": "<PERSON><PERSON><PERSON>", "info": "Personalizar los filtros con la [aplicación Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_filtering": {"label": "<PERSON><PERSON><PERSON>", "info": "Personalizar los filtros con la [aplicación Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/filters)"}, "enable_sorting": {"label": "Ordenación"}, "header__1": {"content": "Filtrado y ordenado"}, "header__3": {"content": "Tarjeta de producto"}, "show_rating": {"label": "Calificación del producto", "info": "Se requiere una aplicación para las calificaciones de productos. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/collection-pages#product-grid-show-product-rating)"}, "columns_desktop": {"label": "Columnas"}, "columns_mobile": {"label": "Columnas móviles", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "filter_type": {"label": "Diseño de filtro", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cajón"}}, "quick_add": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options": {"option_1": "<PERSON><PERSON><PERSON>", "option_2": "<PERSON><PERSON><PERSON><PERSON>", "option_3": "Masivo"}}}}, "main-list-collections": {"name": "Página de lista de colecciones", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Colecciones"}, "sort": {"label": "Ordenar colecciones", "options__1": {"label": "Alfabéticamente, A-Z"}, "options__2": {"label": "Alfabéticamente, Z-A"}, "options__3": {"label": "Fecha: reciente a antigua"}, "options__4": {"label": "Fecha: antigua a reciente"}, "options__5": {"label": "<PERSON><PERSON><PERSON> de productos, de mayor a menor"}, "options__6": {"label": "<PERSON><PERSON><PERSON> de productos, de menor a mayor"}}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "columns_desktop": {"label": "Columnas"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "columns_mobile": {"label": "Columnas móviles", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "main-page": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "main-password-footer": {"name": "Pie de página de contraseña"}, "main-password-header": {"name": "Encabezado de contraseña", "settings": {"logo_help": {"content": "Editar tu logo en [configuración del tema](/editor?context=theme&category=logo)"}}}, "main-product": {"name": "Información de producto", "blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto", "default": "Bloque de texto"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Precio"}, "quantity_selector": {"name": "Selector de cantidad"}, "variant_picker": {"name": "Selector de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Desplegable"}, "options__2": {"label": "Miniaturas"}}, "swatch_shape": {"label": "Muestra", "info": "Obtén más información acerca de las [muestras](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) en las opciones de producto", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Cuadrado"}, "options__3": {"label": "Ninguna"}}}}, "buy_buttons": {"name": "Botones de compras", "settings": {"show_dynamic_checkout": {"label": "Botones de pago dinámico", "info": "Los clientes verán su opción de pago preferida. [Más información](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": " Opciones de envío de tarjeta de regalo", "info": "Los clientes pueden agregar un mensaje personal y programar la fecha de envío. [Más información](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "Disponibilidad de retiro"}, "description": {"name": "Descripción"}, "share": {"name": "Compartir", "settings": {"text": {"label": "texto", "default": "Compartir"}}}, "collapsible_tab": {"name": "Fila desplegable", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Fila desplegable"}, "content": {"label": "Contenido de fila"}, "page": {"label": "Contenido de fila de la página"}, "icon": {"label": "Ícono", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Man<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__4": {"label": "Biberón"}, "options__5": {"label": "Apartado postal"}, "options__6": {"label": "Zanahoria"}, "options__7": {"label": "Globo de chat"}, "options__8": {"label": "Marca de verificación"}, "options__9": {"label": "Portapapeles"}, "options__10": {"label": "Lácteos"}, "options__11": {"label": "Libre de lácteos"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON>"}, "options__14": {"label": "Fuego"}, "options__15": {"label": "Libre de gluten"}, "options__16": {"label": "Corazón"}, "options__17": {"label": "Plancha"}, "options__18": {"label": "Hoja"}, "options__19": {"label": "<PERSON><PERSON><PERSON>"}, "options__20": {"label": "Relámpago"}, "options__21": {"label": "<PERSON><PERSON><PERSON><PERSON> labial"}, "options__22": {"label": "Candado"}, "options__23": {"label": "Alfiler en mapa"}, "options__24": {"label": "Libre de nueces"}, "options__25": {"label": "<PERSON><PERSON><PERSON>"}, "options__26": {"label": "<PERSON><PERSON> de <PERSON>a"}, "options__27": {"label": "Pimienta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avión"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de precio"}, "options__32": {"label": "Signo de interrogación"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Devolución"}, "options__35": {"label": "Regla"}, "options__36": {"label": "<PERSON> de <PERSON>rvir"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Zapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON> de <PERSON>eve"}, "options__41": {"label": "Estrella"}, "options__42": {"label": "Cronómetro"}, "options__43": {"label": "Camión"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}, "popup": {"name": "Ventana emergente", "settings": {"link_label": {"label": "Vincular etiqueta", "default": "Texto del enlace emergente"}, "page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "rating": {"name": "Calificación de los productos", "settings": {"paragraph": {"content": "Se requiere una aplicación para las calificaciones de productos. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#product-rating-block)"}}}, "complementary_products": {"name": "Productos complementarios", "settings": {"paragraph": {"content": "Gestionar productos complementarios en la [aplicación Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "Encabezado", "default": "Combina bien con"}, "make_collapsible_row": {"label": "Fila desplegable"}, "icon": {"info": "Se muestra cuando se selecciona una fila desplegable"}, "product_list_limit": {"label": "Recuento de productos"}, "products_per_page": {"label": "Productos por página"}, "pagination_style": {"label": "Paginación", "options": {"option_1": "Punt<PERSON>", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "Números"}}, "product_card": {"heading": "Tarjeta de producto"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options": {"option_1": "Retrato", "option_2": "Cuadrado"}}, "enable_quick_add": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}, "icon_with_text": {"name": "Ícono con texto", "settings": {"layout": {"label": "Diseño", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}}, "heading": {"info": "Dejar en blanco para ocultar este emparejamiento"}, "icon_1": {"label": "Ícono"}, "image_1": {"label": "Imagen"}, "heading_1": {"label": "Encabezado", "default": "Encabezado"}, "icon_2": {"label": "Ícono"}, "image_2": {"label": "Imagen"}, "heading_2": {"label": "Encabezado", "default": "Encabezado"}, "icon_3": {"label": "Ícono"}, "image_3": {"label": "Imagen"}, "heading_3": {"label": "Encabezado", "default": "Encabezado"}, "pairing_1": {"label": "Emparejamiento 1", "info": "Elegir un ícono o agregar una imagen para cada emparejamiento"}, "pairing_2": {"label": "Emparejamiento 2"}, "pairing_3": {"label": "Emparejamiento 3"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "inventory": {"name": "Estado del inventario", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "inventory_threshold": {"label": "Umbral de inventario bajo"}, "show_inventory_quantity": {"label": "Recuento de inventario"}}}}, "settings": {"header": {"content": "Multimedia"}, "enable_video_looping": {"label": "Video en bucle"}, "enable_sticky_info": {"label": "Contenido fijo"}, "hide_variants": {"label": "Ocultar otros elementos multimedia de variante después de que se selecciona uno"}, "gallery_layout": {"label": "Diseño", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON> columnas"}, "options__3": {"label": "Miniaturas"}, "options__4": {"label": "Carrusel de miniaturas"}}, "media_size": {"label": "<PERSON><PERSON>", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}, "mobile_thumbnails": {"label": "Diseño para móviles", "options__1": {"label": "<PERSON><PERSON> columnas"}, "options__2": {"label": "Mostrar miniaturas"}, "options__3": {"label": "Ocultar miniaturas"}}, "media_position": {"label": "Posición", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Derecha"}}, "image_zoom": {"label": "Ampliar", "options__1": {"label": "Abrir Lightbox"}, "options__2": {"label": "<PERSON><PERSON> clic y pasar sobre el elemento"}, "options__3": {"label": "Sin zoom"}}, "constrain_to_viewport": {"label": "Ajustar a la altura de la pantalla"}, "media_fit": {"label": "Ajustar", "options__1": {"label": "Original"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}}, "main-search": {"name": "Resultados de búsqueda", "settings": {"image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "header__1": {"content": "Tarjeta de producto"}, "header__2": {"content": "Tarjeta de blog"}, "article_show_date": {"label": "<PERSON><PERSON>"}, "article_show_author": {"label": "Autor"}, "show_rating": {"label": "Calificación del producto", "info": "Se requiere una aplicación para las calificaciones de productos. [Más información](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types/search-page)"}, "columns_desktop": {"label": "Columnas"}, "columns_mobile": {"label": "Columnas móviles", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multicolumn": {"name": "Multicolumna", "settings": {"title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Varias columnas"}, "image_width": {"label": "<PERSON><PERSON>", "options__1": {"label": "Ancho de un tercio de columna"}, "options__2": {"label": "<PERSON><PERSON> de mitad de columna"}, "options__3": {"label": "<PERSON><PERSON> completo de columna"}}, "image_ratio": {"label": "Proporción", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Retrato"}, "options__3": {"label": "Cuadrado"}, "options__4": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "column_alignment": {"label": "Alineación de columna", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}}, "background_style": {"label": "Fondo secundario", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Mostrar como fondo de columna"}}, "button_label": {"label": "Etiqueta", "default": "Etiqueta de botón", "info": "Dejar en blanco para ocultar"}, "button_link": {"label": "Enlace"}, "swipe_on_mobile": {"label": "<PERSON><PERSON><PERSON>"}, "columns_desktop": {"label": "Columnas"}, "header_mobile": {"content": "Diseño mó<PERSON>"}, "columns_mobile": {"label": "Columnas", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_text": {"content": "Encabezado"}, "header_image": {"content": "Imagen"}, "header_layout": {"content": "Diseño"}, "header_button": {"content": "Botón"}}, "blocks": {"column": {"name": "Columna", "settings": {"image": {"label": "Imagen"}, "title": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Columna"}, "text": {"label": "Descripción", "default": "<p>Vincula un texto con una imagen para atraer la atención hacia tu producto, colección o artículo de blog seleccionados. Agrega detalles sobre disponibilidad y estilo, o incluso ofrece una reseña.</p>"}, "link_label": {"label": "Vincular etiqueta", "info": "Dejar en blanco para ocultar"}, "link": {"label": "Enlace"}}}}, "presets": {"name": "Multicolumna"}}, "newsletter": {"name": "Suscriptor de correo electrónico", "settings": {"full_width": {"label": "<PERSON><PERSON> completo"}, "paragraph": {"content": "Suscriptores agregados [perfiles de clientes](https://help.shopify.com/manual/customers/manage-customers)"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Suscribirse a nuestros correos electrónicos"}}}, "paragraph": {"name": "Texto", "settings": {"paragraph": {"label": "Texto", "default": "<p>Conoce las nuevas colecciones y las ofertas exclusivas antes que nadie.</p>"}}}, "email_form": {"name": "Formulario de correo electrónico"}}, "presets": {"name": "Suscriptor de correo electrónico"}}, "page": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"page": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON>"}}, "rich-text": {"name": "Texto enriquecido", "settings": {"full_width": {"label": "<PERSON><PERSON> completo"}, "desktop_content_position": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}, "label": "Posición de contenido"}, "content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}, "label": "Alineación de contenido"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Coméntanos sobre tu marca"}}}, "text": {"name": "Texto", "settings": {"text": {"label": "Texto", "default": "<p>Comparte información sobre tu marca con los clientes. Describe un producto, comparte anuncios o da la bienvenida a los clientes a tu tienda.</p>"}}}, "buttons": {"name": "Botones", "settings": {"button_label_1": {"label": "Etiqueta", "info": "Dejar en blanco para ocultar", "default": "Etiqueta de botón"}, "button_link_1": {"label": "Enlace"}, "button_style_secondary_1": {"label": "Estilo <PERSON>"}, "button_label_2": {"label": "Etiqueta", "info": "Deja la etiqueta en blanco para ocultar"}, "button_link_2": {"label": "Enlace"}, "button_style_secondary_2": {"label": "Estilo <PERSON>"}, "header_button1": {"content": "Botón 1"}, "header_button2": {"content": "Botón 2"}}}, "caption": {"name": "Leyenda", "settings": {"text": {"label": "Texto", "default": "Agregar un eslogan"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Subtítulo"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "caption_size": {"label": "<PERSON><PERSON>", "options__1": {"label": "Pequeño"}, "options__2": {"label": "Mediano"}, "options__3": {"label": "Grande"}}}}}, "presets": {"name": "Texto enriquecido"}}, "apps": {"name": "Aplicaciones", "settings": {"include_margins": {"label": "Hacer que los márgenes de sección sean iguales al tema"}}, "presets": {"name": "Aplicaciones"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Encabezado", "default": "Video"}, "cover_image": {"label": "Imagen de portada"}, "video_url": {"label": "URL", "info": "Usar URL de YouTube o Vimeo"}, "description": {"label": "Texto alternativo del video", "info": "Describir el video para quienes usan lectores de pantalla"}, "image_padding": {"label": "Agregar re<PERSON>o de <PERSON>n", "info": "Selecciona relleno de imagen si no deseas que se corte tu imagen de portada."}, "full_width": {"label": "<PERSON><PERSON> completo"}, "video": {"label": "Video"}, "enable_video_looping": {"label": "Video en bucle"}, "header__1": {"content": "Video alojado en Shopify"}, "header__2": {"content": "O incrustar video a partir de una URL"}, "header__3": {"content": "Diseño"}, "paragraph": {"content": "Se muestra cuando no se seleccionó ningún video alojado en Shopify"}}, "presets": {"name": "Video"}}, "featured-product": {"name": "Producto destacado", "blocks": {"text": {"name": "Texto", "settings": {"text": {"label": "Texto", "default": "Bloque de texto"}, "text_style": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "price": {"name": "Precio"}, "quantity_selector": {"name": "Selector de cantidad"}, "variant_picker": {"name": "Selector de variante", "settings": {"picker_type": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Desplegable"}, "options__2": {"label": "Botones"}}, "swatch_shape": {"label": "Muestra", "info": "Obtén más información acerca de las [muestras](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) en las opciones de producto", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Cuadrado"}, "options__3": {"label": "Ninguna"}}}}, "buy_buttons": {"name": "Botones de compras", "settings": {"show_dynamic_checkout": {"label": "Botones de pago dinámico", "info": "Los clientes verán su opción de pago preferida. [Más información](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Descripción"}, "share": {"name": "Compartir", "settings": {"featured_image_info": {"content": "Si incluyes un enlace en publicaciones de redes sociales, la imagen destacada de la página se mostrará como la imagen de vista previa. [Más información](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Con la imagen de vista previa se incluye un nombre y descripción de la tienda. [Más información](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Texto", "default": "Compartir"}}}, "rating": {"name": "Calificación de productos", "settings": {"paragraph": {"content": "Se requiere una aplicación para las calificaciones de productos. [Más información](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "Estilo de texto", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "settings": {"product": {"label": "Producto"}, "secondary_background": {"label": "Fondo secundario"}, "header": {"content": "Multimedia"}, "enable_video_looping": {"label": "Video en bucle"}, "hide_variants": {"label": "Ocultar elementos multimedia de variantes no seleccionadas en el escritorio"}, "media_position": {"label": "Posición", "info": "La posición se optimiza automáticamente para dispositivos móviles.", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Derecha"}}}, "presets": {"name": "Producto destacado"}}, "email-signup-banner": {"name": "Banner de suscripción de correo electrónico", "settings": {"paragraph": {"content": "Suscriptores agregados [perfiles de clientes](https://help.shopify.com/manual/customers/manage-customers)"}, "image": {"label": "Imagen de fondo"}, "show_background_image": {"label": "Mostrar imagen de fondo"}, "show_text_box": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "image_overlay_opacity": {"label": "Opacidad de sobreposición"}, "show_text_below": {"label": "Apilar el texto debajo de la imagen"}, "image_height": {"label": "Altura", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}}, "desktop_content_position": {"options__1": {"label": "Arriba a la izquierda"}, "options__2": {"label": "Arriba en el centro"}, "options__3": {"label": "Arriba a la derecha"}, "options__4": {"label": "Centrado a la izquierda"}, "options__5": {"label": "Centrado en el medio"}, "options__6": {"label": "Centrado a la derecha"}, "options__7": {"label": "Abajo a la izquierda"}, "options__8": {"label": "Abajo en el centro"}, "options__9": {"label": "Abajo a la derecha"}, "label": "Posición"}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación"}, "header": {"content": "Diseño mó<PERSON>"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación"}, "color_scheme": {"info": "Visible cuando se muestre el contenedor."}, "content_header": {"content": "Contenido"}}, "blocks": {"heading": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Apertura próximamente"}}}, "paragraph": {"name": "Texto", "settings": {"paragraph": {"label": "Texto", "default": "<p>Entérate de nuestros lanzamientos antes que los demás.</p>"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "label": "<PERSON><PERSON><PERSON>"}}}, "email_form": {"name": "Formulario de correo electrónico"}}, "presets": {"name": "Banner de suscripción de correo electrónico"}}, "slideshow": {"name": "Presentación de diapositivas", "settings": {"layout": {"label": "Diseño", "options__1": {"label": "<PERSON><PERSON> completo"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "slide_height": {"label": "Altura", "options__1": {"label": "Adaptar a la primera imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}}, "slider_visual": {"label": "Paginación", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Punt<PERSON>"}, "options__3": {"label": "Números"}}, "auto_rotate": {"label": "Rotar las diapositivas automáticamente"}, "change_slides_speed": {"label": "Cambiar diapositivas cada"}, "mobile": {"content": "Diseño para móviles"}, "show_text_below": {"label": "Apilar el texto debajo de la imagen"}, "accessibility": {"content": "Accesibilidad", "label": "Descripción de la presentación de diapositivas", "info": "Describir la presentación de diapositivas para quienes usan lectores de pantalla", "default": "Presentación de diapositivas sobre tu marca"}}, "blocks": {"slide": {"name": "Diapositiva", "settings": {"image": {"label": "Imagen"}, "heading": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "Diapositiva de imagen"}, "subheading": {"label": "Subtítulo", "default": "Cuenta la historia de tu marca a través de imágenes"}, "button_label": {"label": "Etiqueta", "info": "Dejar en blanco para ocultar", "default": "Etiqueta de botón"}, "link": {"label": "Enlace"}, "secondary_style": {"label": "Estilo <PERSON>"}, "box_align": {"label": "Posición de contenido", "options__1": {"label": "Arriba a la izquierda"}, "options__2": {"label": "Arriba en el centro"}, "options__3": {"label": "Arriba a la derecha"}, "options__4": {"label": "Centrado a la izquierda"}, "options__5": {"label": "Centrado en el medio"}, "options__6": {"label": "Centrado a la derecha"}, "options__7": {"label": "Abajo a la izquierda"}, "options__8": {"label": "Abajo en el centro"}, "options__9": {"label": "Abajo a la derecha"}}, "show_text_box": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "text_alignment": {"label": "Alineación de contenido", "option_1": {"label": "Iz<PERSON>erda"}, "option_2": {"label": "Centrado"}, "option_3": {"label": "Derecha"}}, "image_overlay_opacity": {"label": "Opacidad de sobreposición"}, "text_alignment_mobile": {"label": "Alineación del contenido en dispositivos móviles", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}}, "header_button": {"content": "Botón"}, "header_layout": {"content": "Diseño"}, "header_text": {"content": "Texto"}, "header_colors": {"content": "Colores"}}}}, "presets": {"name": "Presentación de diapositivas"}}, "collapsible_content": {"name": "Contenido desplegable", "settings": {"caption": {"label": "Leyenda"}, "heading": {"label": "Encabezado", "default": "Contenido desplegable"}, "heading_alignment": {"label": "Alineación del encabezado", "options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Derecha"}}, "layout": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON> contenedor"}, "options__2": {"label": "<PERSON>ten<PERSON><PERSON> de <PERSON>la"}, "options__3": {"label": "Contenedor de sección"}}, "container_color_scheme": {"label": "Esquema de color del contenedor"}, "open_first_collapsible_row": {"label": "Abrir la primera fila"}, "header": {"content": "Imagen"}, "image": {"label": "Imagen"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Grande"}}, "desktop_layout": {"label": "Colocación", "options__1": {"label": "Imagen primero"}, "options__2": {"label": "Imagen de segundo"}}, "layout_header": {"content": "Diseño"}, "section_color_scheme": {"label": "Esquema de colores para la sección"}}, "blocks": {"collapsible_row": {"name": "Fila desplegable", "settings": {"heading": {"label": "Encabezado", "default": "Fila desplegable"}, "row_content": {"label": "Contenido de fila"}, "page": {"label": "Contenido de fila de la página"}, "icon": {"label": "Ícono", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Man<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__4": {"label": "Biberón"}, "options__5": {"label": "Apartado postal"}, "options__6": {"label": "Zanahoria"}, "options__7": {"label": "Globo de chat"}, "options__8": {"label": "Marca de verificación"}, "options__9": {"label": "Portapapeles"}, "options__10": {"label": "Lácteos"}, "options__11": {"label": "Libre de lácteos"}, "options__12": {"label": "Secador"}, "options__13": {"label": "<PERSON><PERSON>"}, "options__14": {"label": "Fuego"}, "options__15": {"label": "Libre de gluten"}, "options__16": {"label": "Corazón"}, "options__17": {"label": "Plancha"}, "options__18": {"label": "Hoja"}, "options__19": {"label": "<PERSON><PERSON><PERSON>"}, "options__20": {"label": "Relámpago"}, "options__21": {"label": "<PERSON><PERSON><PERSON><PERSON> labial"}, "options__22": {"label": "Candado"}, "options__23": {"label": "Alfiler en mapa"}, "options__24": {"label": "Libre de nueces"}, "options__25": {"label": "<PERSON><PERSON><PERSON>"}, "options__26": {"label": "<PERSON><PERSON> de <PERSON>a"}, "options__27": {"label": "Pimienta"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Avión"}, "options__30": {"label": "Planta"}, "options__31": {"label": "Etiqueta de precio"}, "options__32": {"label": "Signo de interrogación"}, "options__33": {"label": "Reciclar"}, "options__34": {"label": "Devolución"}, "options__35": {"label": "Regla"}, "options__36": {"label": "<PERSON> de <PERSON>rvir"}, "options__37": {"label": "<PERSON><PERSON>"}, "options__38": {"label": "Zapato"}, "options__39": {"label": "<PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON> de <PERSON>eve"}, "options__41": {"label": "Estrella"}, "options__42": {"label": "Cronómetro"}, "options__43": {"label": "Camión"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Contenido desplegable"}}, "main-account": {"name": "C<PERSON><PERSON>"}, "main-activate-account": {"name": "Activación de cuenta"}, "main-addresses": {"name": "Direcciones"}, "main-login": {"name": "Inicio de sesión", "shop_login_button": {"enable": "Habilitar Iniciar se<PERSON> con <PERSON>"}}, "main-order": {"name": "Pedido"}, "main-register": {"name": "Registro"}, "main-reset-password": {"name": "Restablecimiento de contraseña"}, "related-products": {"name": "Productos relacionados", "settings": {"heading": {"label": "Encabezado"}, "products_to_show": {"label": "Recuento de productos"}, "columns_desktop": {"label": "Columnas"}, "paragraph__1": {"content": "Los productos relacionados se pueden gestionar en la [aplicación Search & Discovery](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)", "default": "También te puede interesar"}, "header__2": {"content": "Tarjeta de producto"}, "image_ratio": {"label": "Relación de aspecto de imagen", "options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Cuadrada"}}, "show_secondary_image": {"label": "Mostrar segunda imagen al pasar el cursor"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "show_rating": {"label": "Calificación del producto", "info": "Se requiere una aplicación para las calificaciones de productos. [Más información](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-product-recommendations)"}, "columns_mobile": {"label": "Columnas móviles", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multirow": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "Imagen"}, "image_height": {"options__1": {"label": "Adaptar a la imagen"}, "options__2": {"label": "Pequeña"}, "options__3": {"label": "Mediana"}, "options__4": {"label": "Grande"}, "label": "Altura"}, "desktop_image_width": {"options__1": {"label": "Pequeña"}, "options__2": {"label": "Mediana"}, "options__3": {"label": "Grande"}, "label": "<PERSON><PERSON>"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Subtítulo"}, "label": "Estilo de texto"}, "button_style": {"options__1": {"label": "Botón sólido"}, "options__2": {"label": "Botón con contorno"}, "label": "Estilo del botón"}, "desktop_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación"}, "desktop_content_position": {"options__1": {"label": "Arriba"}, "options__2": {"label": "Centrado"}, "options__3": {"label": "Abajo"}, "label": "Posición"}, "image_layout": {"options__1": {"label": "Alternar desde la izquierda"}, "options__2": {"label": "Alternar desde la derecha"}, "options__3": {"label": "Alineada a la izquierda"}, "options__4": {"label": "Alineada a la derecha"}, "label": "Colocación"}, "container_color_scheme": {"label": "Esquema de color del contenedor"}, "mobile_content_alignment": {"options__1": {"label": "Iz<PERSON>erda"}, "options__2": {"label": "Centro"}, "options__3": {"label": "Derecha"}, "label": "Alineación móvil"}, "header": {"content": "Imagen"}, "header_2": {"content": "Contenido"}, "header_3": {"content": "Colores"}}, "blocks": {"row": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "Imagen"}, "caption": {"label": "Leyenda", "default": "Leyenda"}, "heading": {"label": "Encabezado", "default": "<PERSON><PERSON>"}, "text": {"label": "Texto", "default": "<p>Vincula un texto con una imagen para atraer la atención hacia tu producto, colección o artículo de blog seleccionados. Agrega detalles sobre disponibilidad y estilo, o incluso ofrece una reseña.</p>"}, "button_label": {"label": "Etiqueta de botón", "default": "Etiqueta de botón", "info": "Dejar en blanco para ocultar"}, "button_link": {"label": "<PERSON>lace de botón"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "quick-order-list": {"name": "Lista de pedidos rápidos", "settings": {"show_image": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "show_sku": {"label": "SKU"}, "variants_per_page": {"label": "<PERSON><PERSON><PERSON> por página"}}, "presets": {"name": "Lista de pedidos rápidos"}}}}