{if !empty($widget->getArticles())}
    {$dateFormats = \App\Common\DateTimeFormat::getSiteCurrentDateFormat()}
    <div class="_blog-list-articles">

    {foreach $widget->getArticles() as $article}
        <div class="_blog-list-article">
            <div class="_blog-list-article-image">
                <a class="blog-list-article-image-thumb" href="{$article->url()}">
                    <img src="{$article->getImage('800x800')}" alt="{$article->name}"
                            {if $article->aspect_ratio}
                                style="aspect-ratio: {$article->aspect_ratio};"
                            {/if}
                         width="100%"
                         height="auto"
                    >
                </a>
            </div>
            <div class="_blog-list-article-info">
                <div class="_blog-list-article-title">
                    <h3 class="_blog-list-article-title-tag">
                        <a href="{$article->url()}">{$article->name}</a>
                    </h3>
                </div>
            </div>
            <div class="_blog-list-article-meta">
					<span class="_blog-list-article-category">
						<a href="{$article->blog->url()}">
							{$article->blog->name}
						</a>
					</span>
                {if $article->author}
                    <span class="_blog-list-article-author">{$article->author->first_name} {$article->author->last_name}</span>
                {/if}
                <span class="_blog-list-article-time">{siteDateTime($article->created_at)}</span>
            </div>
            {if empty($truncate)}{$truncate=100}{/if}
            {if {$article->content|strip_tags|trim|truncate:$truncate}}
                <div class="_blog-list-article-text">
                    {$article->content|strip_tags|trim|truncate:$truncate nofilter}
                </div>
            {/if}
            <div class="_blog-list-article-viewmore">
                <a class="_blog-list-article-viewmore-link" href="{$article->url()}">{t}sf.widget.blog.article.act.read_more{/t}</a>
            </div>
        </div>
    {/foreach}
    </div>
{else}
    <div class="_notification">
        <p>{t}sf.widget.blog.article.nfy.no_articles{/t}</p>
    </div>
{/if}