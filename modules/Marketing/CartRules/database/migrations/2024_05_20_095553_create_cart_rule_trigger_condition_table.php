<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCartRuleTriggerConditionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        if (!Schema::hasTable('@cart_rule_trigger_condition')) {
            Schema::create('@cart_rule_trigger_condition', function (Blueprint $table): void {
                $table->increments('id');
                $table->unsignedInteger('cart_rule_id')->index('idx_cart_rule_id');
                $table->unsignedInteger('cart_rule_row_id')->index('idx_cart_rule_row_id');
                $table->unsignedInteger('cart_rule_trigger_id');
                $table->unsignedInteger('sorting')->index('idx_sorting')
                    ->comment('Sequence number in the conditions');
                $table->string('condition_type')->index('idx_condition_type')
                    ->nullable()->comment('morph -> record, sum, count, new, sale, feature, title and etc');
                $table->string('filter_type')->index('idx_filter_type')
                    ->nullable()->comment('cart_amount, cart_products_count and etc');
                $table->nullableMorphs('record');
                $table->string('value_type')->index('idx_value_type')
                    ->nullable()->comment('amount, percent for discount');
                $table->string('value')->index('idx_value')
                    ->nullable()->comment('amount, count, true, false by condition type');
                $table->string('sub_value')->index('idx_sub_value')
                    ->nullable()->comment('amount, count, true, false by condition type');
                $table->string('operator')->index('idx_operator')
                    ->comment('in, not_in, gt, lt and etc')->default('in');

                $table->foreign('cart_rule_trigger_id', 'fk_trigger_cart_rule_trigger')
                    ->on('@cart_rule_trigger')->references('id')
                    ->onDelete('CASCADE')->onUpdate('NO ACTION');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        if (Schema::hasTable('@cart_rule_trigger_condition')) {
            Schema::table('@cart_rule_trigger_condition', function (Blueprint $table): void {
                $table->dropForeign('fk_trigger_cart_rule_trigger');
            });
        }

        Schema::dropIfExists('@cart_rule_trigger_condition');
    }
}
