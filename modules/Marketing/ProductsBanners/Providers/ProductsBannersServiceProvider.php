<?php

declare(strict_types=1);

namespace Modules\Marketing\ProductsBanners\Providers;


use App\Providers\RouteServiceProvider;
use Exception;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Routing\Router;
use Illuminate\Support\ServiceProvider;
use Modules\Marketing\ProductsBanners\Commands\DestroyDiscountBannersCommand;
use Modules\Marketing\ProductsBanners\Commands\MigrateDiscountsToBannersCommand;
use Modules\Marketing\ProductsBanners\Commands\RegisterJobForExpiredBannersCommand;
use Modules\Marketing\ProductsBanners\Models\Banners;

class ProductsBannersServiceProvider extends ServiceProvider
{


    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'Modules\Marketing\ProductsBanners\Http\Controllers';

    /**
     * @return void
     */
    #[\Override]
    public function register(): void
    {
        $this->registerConfig();
        $this->registerRelationMorphMap();
        $this->registerCommands();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     * @throws Exception
     */
    public function boot(): void
    {
        $this->registerRoutes();
        $this->registerViews();
        $this->registerTranslations();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     * @throws Exception
     */
    protected function registerRoutes()
    {
        $this->app->make('router')->group(['namespace' => $this->namespace], function (Router $route): void {
            if (app_namespace() == 'sitecp') {
                $route->group(['middleware' => RouteServiceProvider::siteCpMiddleware(), 'namespace' => 'Admin'], function (Router $route): void {
                    $this->loadRoutesFrom(__DIR__ . '/../routes/sitecp.php');
                });

                $route->group(['middleware' => RouteServiceProvider::siteCpMiddleware(), 'namespace' => 'Admin\Api'], function (Router $route): void {
                    $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
                });

            }
        });
    }

    /**
     *
     */
    protected function registerConfig()
    {
        $this->mergeConfigFrom(__DIR__ . '/../config/plan.mapping.php', 'plan.mapping');
        $this->mergeConfigFrom(__DIR__ . '/../config/plan.restrict.access.php', 'plan.restrict.access');
        $this->mergeConfigFrom(__DIR__ . '/../config/queue.php', 'queue.mapping');
    }

    /**
     *
     */
    protected function registerRelationMorphMap()
    {
        Relation::morphMap([
            'product_banner' => Banners::class,
        ]);
    }

    /**
     *
     */
    protected function registerCommands()
    {
        $this->commands([
            MigrateDiscountsToBannersCommand::class,
            DestroyDiscountBannersCommand::class,
            RegisterJobForExpiredBannersCommand::class,
        ]);
    }

    /**
     * Define the views for the application.
     *
     * @return void
     * @throws Exception
     */
    public function registerViews(): void
    {
        $this->app->make('view')->addNamespace('product_banner', __DIR__ . '/../resources/views/');
    }

    public function registerTranslations(): void
    {
        $this->loadTranslationsFrom(__DIR__ . '/../resources/lang/', 'product_banner');
    }

}
