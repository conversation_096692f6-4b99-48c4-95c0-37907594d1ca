<?php

declare(strict_types=1);

namespace Modules\Marketing\Campaign\Channels\WebPush;

use App\Exceptions\Error;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Optional;
use Illuminate\Support\Str;
use Modules\Marketing\Campaign\Channels\WebPush\Connections\WebPushClient;
use Modules\Marketing\Campaign\Channels\WebPush\Helpers\WebPushMessage;
use Modules\Marketing\Campaign\Channels\WebPush\Jobs\CampaignWebPushSend;
use Modules\Marketing\Campaign\Channels\WebPush\Models\WebPushSystemMessages;
use Modules\Marketing\Campaign\Core\Channels\AbstractChannelManager;
use Modules\Marketing\Campaign\Core\Channels\ChannelSettingsLink;
use Modules\Marketing\Campaign\Core\Models\Campaign;
use Modules\Marketing\Campaign\Core\Models\CampaignAction;
use Modules\Marketing\Campaign\Core\Models\CampaignActionTemplate;
use Modules\Marketing\Campaign\Core\Models\Logs\CampaignChannelsLog;
use Modules\Marketing\Campaign\Core\Models\PredefinedCampaign;
use Modules\Marketing\Segments\Core\Models\Subscriber;
use stdClass;
use Throwable;

/**
 * https://developers.nth.ch/#operation/message
 */
class WebPushChannelManager extends AbstractChannelManager
{

    /**
     * @inheritDoc
     */
    public function getChannelId(): string
    {
        return 'web_push';
    }

    /**
     * @inheritDoc
     */
    public function getGroup(): string
    {
        return 'web_push';
    }

    /**
     * @inheritDoc
     */
    public function getSubscriberChannel(): ?string
    {
        return Subscriber::WEB_PUSH_CHANNEL;
    }

    /**
     * @var WebPushClient $client
     */
    protected $client;

    /**
     * @inheritDoc
     * @throws Error
     */
    #[\Override]
    public function getIsConfigured(): bool
    {
        return $this->getIsActive();
    }

    /**
     * @inheritDoc
     * @throws Error
     */
    #[\Override]
    public function getIsInstalled(): bool
    {
        return (bool)$this->getSetting('installed');
    }

    #[\Override]
    public function getLinks(): array
    {
        return array_filter([
//            'purchase_credits' => $this->purchase_credits_link,
            'system_message' => new ChannelSettingsLink(
                route('campaigns.channels.channel.web_push.system_message'),
                __('campaigns-channel-web-push::web_push.system_message'),
                [
                    'data-ajax-panel' => 1,
                    'data-panel-class' => 'wide',
                ]
            ),
            'settings' => new ChannelSettingsLink(
                route('campaigns.channels.channel.web_push.settings'),
                __('global.settings'),
                [
                    'data-ajax-panel' => 1,
                    //'data-panel-class' => 'wide',
                ]
            ),
            'usage' => new ChannelSettingsLink(
                route('campaigns.channels.usage', ['channel' => $this->mapping]),
                __('campaigns::campaign.channel.usage'),
                [
                    'data-ajax-panel' => 1,
                    'data-panel-class' => 'wide',
                ]
            ),
            'log' => new ChannelSettingsLink(
                route('campaigns.channels.log', ['channel' => $this->mapping]),
                __('global.text.log'),
                [
                    'data-ajax-panel' => 1,
                    'data-panel-class' => 'wide',
                ]
            ),
        ]);
    }

    /**
     * @inheritDoc
     * @throws Error
     */
    public function getInstallLink(): ?string
    {
        if ($this->getIsInstalled()) {
            return null;
        }

        return route('campaigns.channels.channel.web_push.install');
    }

    /**
     * @inheritDoc
     * @throws Error
     */
    public function getUninstallLink(): ?string
    {
        if (!$this->getIsInstalled()) {
            return null;
        }

        return route('campaigns.channels.channel.web_push.uninstall');
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public function getFaIcon(): string
    {
        return 'fal fa-spider-web';
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public function getConditionOptions(): array
    {
        return [
            (object)[
                'id' => 'link_clicked',
                'name' => __('campaigns::campaign.condition.option.link_clicked'),
                'name_not' => __('campaigns::campaign.condition.option.link_not_clicked'),
            ],
//            (object)[
//                'id' => 'message_read',
//                'name' => __('campaigns::campaign.condition.option.message_read'),
//                'name_not' => __('campaigns::campaign.condition.option.message_not_read'),
//            ],
        ];
    }

    /**
     * @return WebPushClient
     */
    public function getClient(): WebPushClient
    {
        if (is_null($this->client)) {
            $this->client = new WebPushClient();
        }

        return $this->client;
    }

    /**
     * @inheritDoc
     * @throws Error
     * @throws Throwable
     */
    public function executeCampaignAction(Campaign $campaign, CampaignAction $action, Subscriber $subscriber)
    {
        if (empty($subscriber->single_channel->channel_identifier)) {
            return __('campaigns-channel-web-push::log.error.channel_identifier.empty');
        }

        foreach (['endpoint', 'keys.p256dh', 'keys.auth'] as $dataKey) {
            if (empty(Arr::get($subscriber->single_channel->data, $dataKey))) {
                return __('campaigns-channel-web-push::log.error.channel_identifier.empty');
            }
        }

        if (empty($action->template->data['web_push'])) {
            return __('campaigns::log.error.message.empty');
        }

        if ($subscriber->single_channel->unsubscribed) {
            return __('campaigns::log.error.unsubscribed');
        }

        if (!$subscriber->single_channel->marketing) {
            return __('campaigns::log.error.marketing');
        }

        if ($subscriber->single_channel->bounced) {
            return __('campaigns::log.error.bounced');
        }

        $message_hash = sprintf('%d-%d-%s-%s', site('site_id'), $subscriber->id, Str::random(6), Str::random(4));

        $messageFormatter = $this->getMessagePlaceholdersReplacer()
            ->init($campaign, $action, $subscriber);

        $webPushData = $action->template->data['web_push'];
        foreach (['title', 'body', 'data.link'] as $replaceKey) {
            $webPushValue = Arr::get($webPushData, $replaceKey);

            if ($replaceKey == 'data.link' && !$webPushValue) {
                $webPushValue = site()->getSiteUrl('primary');
            }

            if (empty($webPushValue)) {
                Arr::set($webPushData, $replaceKey, '');
                continue;
            }

            $webPushValue = $messageFormatter->replacePlaceholders($webPushValue);

            $webPushValue = $this->shortenUrls($webPushValue, [
                'cc_campaign' => [
                    'subscriber_id' => $subscriber->id,
                    'campaign_id' => $campaign->id,
                    'action_id' => $action->id,
                    'action_type' => $action->data['action_type'],
                    'channel' => $subscriber->single_channel->channel,
                    'channelIdentifier' => urlencode((string) $subscriber->single_channel->channel_identifier),
                    'sent_by' => 'campaign',
                    'uuid' => $subscriber->uuid->uuid ?? null,
                    'message_hash' => $message_hash,
                    'purpose' => $campaign->purpose,
                ],
                'cc_subscriber' => [
                    'id' => $subscriber->id,
                    'cid' => $subscriber->customer->id ?? null,
                    'channel' => $subscriber->single_channel->channel,
                ],
            ]);

            Arr::set($webPushData, $replaceKey, $webPushValue);
        }

        $data = [
            'channel_identifier' => $subscriber->single_channel->channel_identifier,
            'endpoint' => $subscriber->single_channel->data,
            'web_push' => WebPushMessage::initFromArray(array_merge($webPushData, [
                'data' => array_merge($webPushData['data'] ?? [], [
                    'message_hash' => $message_hash,
                    'campaign_id' => $action->campaign_id,
                    'campaign_action_id' => $action->getKey(),
                    'subscriber_id' => $subscriber->id,
                ]),
                //'tag' => 'cc-' . $action->getKey()
            ])),
            'campaign_id' => $action->campaign_id,
            'campaign_action_id' => $action->getKey(),
            'subscriber_id' => $subscriber->id,
            'title' => sprintf('%s - %s', $campaign->title, ($action->template->data['internal_title'] ?? __('global.N/A'))),
            'message_hash' => $message_hash,
        ];

        $this->sendUsageAlert();

        retry(5, function () use ($subscriber, $action, $webPushData, $data, $campaign, $message_hash): void {
            dispatch(
                new CampaignWebPushSend(
                    $subscriber->id,
                    [
                        'campaign_id' => $action->campaign_id,
                        'campaign_action_id' => $action->id,
                    ],
                    $data,
                    [
                        'sent_by' => 'campaign',
                        'log_type' => 'statistic',
                        'channel_action_type' => $this->action_type,
                        'site_id' => site('site_id'),
                        'from' => $this->getClient()->getConfiguration('sender'),
                        'customer_id' => optional($subscriber->customer)->id,
                        'subscriber_id' => $subscriber->id,
                        'first_name' => $subscriber->first_name,
                        'last_name' => $subscriber->last_name,

                        'campaign_id' => $campaign->id,
                        'campaign_name' => $campaign->title,
                        'channel_identifier' => $subscriber->single_channel->channel_identifier,
                        'channel' => $this->mapping,

                        'campaign_action_id' => $action->id,
                        'campaign_action_type' => $action->data['action_type'],
                        'campaign_action_order' => $action->order,

                        'segment_id' => $campaign->segment->id ?? null,
                        'segment_name' => $campaign->segment->name ?? null,
                        'segment_conditions' => $campaign->segment->conditions_formatted ?? null,
                        'segment_channel' => $campaign->segment->channel ?? null,

                        'message' => $webPushData['body'] ?? '',
                        'message_hash' => $message_hash,
                        'action' => 'send_message',
                        'data' => [
                            'messageData' => $webPushData,
                            'channelData' => $subscriber->single_channel->data,
                        ],
                    ],
                    $this->getMessagePlaceholdersReplacer()->getMetas()
                )
            );
        }, 2000);

        return -1;
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    public function sendMessage($data)
    {
        if (empty($data['endpoint'])) {
            return __('campaigns-channel-web-push::log.error.channel_identifier.empty');
        }

        if ($check = $this->checkConsoleAccessDeniedByPlan()) {
            return $check;
        }

        return $this->sendMessageFromData($data, fn($data) => $this->getClient()->send(
            $data['endpoint'],
            $data['web_push'],
            $data['message_hash']
        ));
    }

    /**
     * @inheritDoc
     */
    public function createTemplate(Campaign $campaign, int $actionOrder, ?CampaignActionTemplate $template = null, ?PredefinedCampaign $predefined = null): Response
    {
        return \Illuminate\Support\Facades\View::panel('campaigns-channel-web-push::form', [
            'template' => !empty($template->data) ? (object)$template->data : null,
            'actionOrder' => $actionOrder,
            'campaignId' => $campaign->id,
            'tags' => static::getMessageTags($campaign),
            'predefined' => $predefined,
        ]);
    }

    /**
     * @inheritDoc
     */
    public function createTemplateJson(Campaign $campaign, int $actionOrder, ?CampaignActionTemplate $template = null, ?PredefinedCampaign $predefined = null): JsonResponse
    {
        $mergeTags = $this->withLocale('en', fn(): array => static::getMessageTags($campaign));

        return response()->json([
            'action' => 'form',
            'id' => $template->id ?? null,
            'mapping' => $this->getMapping(),
            'tags' => $mergeTags,
            'template' => (object)($template->data ?? []),
            'predefined' => $predefined instanceof \Modules\Marketing\Campaign\Core\Models\PredefinedCampaign,
            'campaign_id' => $campaign->id,
            'action_order' => $actionOrder,
        ]);
    }

    /**
     * @inheritDoc
     */
    public function createTemplateButton(Campaign $campaign, $row, $action, ?PredefinedCampaign $predefined = null): ?View
    {
        return view('campaigns-channel-web-push::includes.create-button', [
            'campaign' => $campaign,
            'predefined' => $predefined,
            'row' => $row,
            'action' => $action,
        ]);
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public static function statusMapping($status): ?string
    {
        $status = trim((string) $status);
        if ($status === 'PENDING') {
            return null;
        }

        return $status ?: static::STATUS_NOT_SENT;
    }

    /**
     * @param CampaignChannelsLog $log
     * @return Response
     */
    #[\Override]
    public function logMessageView(CampaignChannelsLog $log): Response
    {
        return \Illuminate\Support\Facades\View::panel('campaigns-channel-web-push::message-logs.view', [
            'message' => new Optional((object)[
                'web_push' => WebPushMessage::initFromArray($log->data['messageData'])->toArray(),
            ])
        ]);
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public function getSystemMessages($pluck = true): Collection
    {
        $language = site('language');
        if (!WebPushSystemMessages::whereNotNull('event')->where('language', $language)->exists()) {
            $language = config('app.fallback_locale');
        }

        $messages = WebPushSystemMessages::whereNotNull('event')->where('language', $language)
            ->orderBy('system_title');

        return $pluck ? $messages->pluck('system_title', 'id') : $messages->get();
    }

    /**
     * @inheritDoc
     * @throws Throwable
     */
    public function triggerProductsReplacer(?Collection $products = null): ?string
    {
        if (is_null($products) || $products->isEmpty()) {
            return null;
        }

        return view('campaigns-channel-web-push::message.trigger-products', [
            'products' => $products,
        ])->render();
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public static function getMessageTags(?Campaign $campaign = null, bool $predefined = false): array
    {
        $messageTags = parent::getMessageTags($campaign, $predefined);

        $messageTags = collect($messageTags)->whereIn('value', [
            '{$subscriber_first_name}', '{$subscriber_last_name}', '{$shop_name}',
        ]);

        return $messageTags->unique('value')
            ->sortBy('value', SORT_ASC)
            ->values()->all();
    }

    /**
     * @inheritDoc
     */
    #[\Override]
    public function validateChannelMessageRequest(Request $request): array
    {
        return $this->validate($request, [
            'internal_title' => activeRoute('campaigns.channels.channel.web_push.system_message.edit') ? '' : 'string|required|max:191',
            'web_push.title' => 'string|required|max:63',
            'web_push.body' => 'string|required|max:128',
            'web_push.icon' => 'string|url',
            'web_push.image' => 'string|url',
            'web_push.data.link' => 'string|url',
            'web_push.icon_type' => '',
            'web_push.image_type' => '',
        ], [], [
            'internal_title' => __('campaigns::campaign.label.internal_title'),
            'web_push.title' => __('campaigns-channel-web-push::web_push.box.title'),
            'web_push.body' => __('campaigns-channel-web-push::web_push.box.body'),
            'web_push.icon' => __('campaigns-channel-web-push::web_push.box.icon'),
            'web_push.image' => __('campaigns-channel-web-push::web_push.box.image'),
            'web_push.data.link' => __('campaigns-channel-web-push::web_push.box.link'),
        ]);
    }

    /**
     * @inheritdoc
     */
    #[\Override]
    public function getValidateChannelMessageRules(Request $request): array
    {
        return [
            'internal_title' => 'required|string|max:191',
            'web_push.title' => 'required|string|max:63',
            'web_push.body' => 'required|string|max:128',
            'web_push.icon' => 'string|url',
            'web_push.image' => 'string|url',
            'web_push.data.link' => 'string|url',
            'web_push.icon_type' => 'in:internal,external',
            'web_push.image_type' => 'in:internal,external',
        ];
    }

    /**
     * @inheritdoc
     */
    #[\Override]
    public function getValidateChannelMessageMessages(Request $request): array
    {
        return array_merge(parent::getValidateChannelMessageMessages($request), [
            'internal_title.required' => 'Field is required',
            'internal_title.string' => 'Field must be string',
            'internal_title.max' => 'Field may not be greater than {max} characters',
            'web_push.title.required' => 'Field is required',
            'web_push.title.string' => 'Field must be string',
            'web_push.title.max' => 'Field may not be greater than {max} characters',
            'web_push.body.required' => 'Field is required',
            'web_push.body.string' => 'Field must be string',
            'web_push.body.max' => 'Field may not be greater than {max} characters',
            'web_push.icon.string' => 'Field must be string',
            'web_push.icon.url' => 'Url must be a valid link',
            'web_push.image.string' => 'Field must be string',
            'web_push.image.url' => 'Url must be a valid link',
            'web_push.data.link.string' => 'Field must be string',
            'web_push.data.link.url' => 'Url must be a valid link',
        ]);
    }

    /**
     * @inerhitDoc
     */
    public function renderSf(): ?View
    {
        $configuration = [
            'VapKey' => config('campaigns.web_push.vapid.public_key'),
            'PushAllow' => (static function (bool $checkout, bool $status): bool {
                if ($checkout) {
                    return activeRoute('checkout.return')
                        && routeParameter('status') != 'cancel';
                }

                return $status;
            })(boolval($this->getSetting('checkout')), boolval($this->getSetting('popup_status', true))),
            'CookieLifeTime' => (static function (int $interval): int {
                if (!is_numeric($interval) || $interval <= 0 || $interval >= 365) {
                    return 7;
                }

                return intval($interval);
            })($this->getSetting('cookie_life_time', 7)),
            'overlay' => $this->getOverlayData(),
            'popup' => $this->getPopupData(),
        ];

        return view('campaigns-channel-web-push::site.init', [
            'manager' => $this,
            'configuration' => $configuration,
        ]);
    }

    public function getOverlayData(): stdClass
    {
        return (object)[
            'PushMessage' => $this->getSetting('message') ?: __('campaigns-channel-web-push::settings.overlay.text'),
        ];
    }

    public function getPopupData(): stdClass
    {
        return (object)[
            'image' => $this->getSetting('popup_image') ?: logo(),
            'text' => $this->getSetting('popup_text') ?: __('campaigns-channel-web-push::settings.popup.text'),
            'discardButton' => $this->getSetting('popup_discard_button') ?: __('campaigns-channel-web-push::settings.popup.discard_button'),
            'okButton' => $this->getSetting('popup_ok_button') ?: __('campaigns-channel-web-push::settings.popup.ok_button'),
        ];
    }
}
