<?php

declare(strict_types=1);

namespace Modules\Marketing\Campaign\Core\Http\Controllers\Admin;

use App\Exceptions\Error;
use App\Exceptions\HttpNotFound;
use App\Helper\Grid;
use App\Helper\SiteCp\Search;
use Illuminate\Http\Response;
use Modules\Marketing\Campaign\Core\Models\ChannelLogNames;
use Modules\Marketing\Campaign\Core\Providers\CampaignServiceProvider;
use Modules\Marketing\Campaign\Core\Http\Filters\ChannelLogFilters;
use Modules\Marketing\Campaign\Core\Models\Apps\CampaignChannels;
use Modules\Marketing\Campaign\Core\Models\Logs\CampaignChannelsLog;

class ChannelsLogController extends AbstractCampaignController
{
    /**
     * @param mixed $channel
     * @return mixed
     */
    public function index($channel): Response
    {
        if (!CampaignChannels::hasChannel($channel)) {
            throw new HttpNotFound();
        }

        $channel = CampaignChannels::getChannel($channel);

        $search = new Search(ChannelLogFilters::class);

        $campaigns = ChannelLogNames::whereChannel($channel->mapping)
            ->whereType('campaign')->pluck('name');

        $segments = ChannelLogNames::whereChannel($channel->mapping)
            ->whereType('segment')->pluck('name');

        return \Illuminate\Support\Facades\View::panel(CampaignServiceProvider::MODULE_KEY . '::logs.channel-list', [
            'channel' => $channel,
            'search_object' => $search,
            'campaigns' => $campaigns,
            'segments' => $segments,
            'systemMessages' => $channel->getSystemMessages(),
        ]);
    }

    /**
     * @param $channel
     * @return Response
     * @throws Error
     */
    protected function get($channel): Response
    {
        if (!CampaignChannels::hasChannel($channel)) {
            throw new HttpNotFound();
        }

        $filters = request()->input('filters');

        $search = new Search(ChannelLogFilters::class);
        /** @var ChannelLogFilters $filter */
        $filter = $search->search($filters, true);

        $grid = new Grid('updated_at', 'desc');

        $campaigns = CampaignChannelsLog::owner()->where('channel', $channel)
            ->select([
                '_id', 'channel', 'campaign_action_type', 'channel_identifier',
                'subscriber_id', 'first_name', 'last_name', 'campaign_id',
                'campaign_action_order', 'updated_at', 'status',
                'segment_conditions_formatted', 'segment_name', 'campaign_name',
                'subject', 'data'
            ])
            ->getRelationList($filter->getWheres(), $filter->getJoins(), $grid);

        return $grid->generateIlluminate($campaigns->map(fn(CampaignChannelsLog $log) => $log->formatCpList() + ['messages_send' => $log->data['smsCount'] ?? 1]), $filter->getFilters(), [
            'records' => CampaignChannelsLog::owner()->where('channel', $channel)->count(),
            'search_object' => $search,
        ]);
    }
}
