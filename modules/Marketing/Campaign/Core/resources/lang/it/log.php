<?php

declare(strict_types=1);

return [
    'added_to_campaign' => 'L\'abbonato è stato aggiunto alla campagna ":campaign_name"
',
    'removed_from_campaign' => 'L\'abbonato è stato rimosso dalla campagna ":campaign_name"
',
    'send_message' => 'Invia un messaggio all\'abbonato della campagna ":campaign_name"
',
    'set_tags' => 'Aggiunte etichette all\'abbonato della campagna ":campaign_name"
',
    'added_tags' => 'Etichette aggiunte all\'abbonato della campagna ":campaign_name" con successo
',
    'message_read' => 'L\'abbonato ha visualizzato il messaggio della campagna ":campaign_name"
',
    'link_clicked' => 'L\'abbonato ha aperto l\'url dal messaggio della campagna ":campaign_name"
',
    'unsubscribe' => 'L\'abbonato si è disiscritto
',
    'non_execute_action' => 'Il passo #:action_step dalla campagna ":campaign_name" è fallito
',
    'error.message.empty' => 'Testo del messaggio vuoto
',
    'error.message.max' => 'Il messaggio è più lungo di :max simboli
',
    'error.unsubscribed' => 'Disiscritto
',
    'error.marketing' => 'Non accetta marketing
',
    'error.bounced' => 'SPAM
',

    'msg.remove_from_campaign.if.link_clicked.immediately' => 'Il link è stato aperto immediatamente
',
    'msg.remove_from_campaign.else.link_clicked.immediately' => 'Il link non è stato aperto immediatamente
',
    'msg.remove_from_campaign.if.link_clicked.interval' => 'Il link è stato aperto fino a :interval :interval_type
',
    'msg.remove_from_campaign.else.link_clicked.interval' => 'Il link non è stato aperto fino a :interval :interval_type
',
    'msg.remove_from_campaign.if.message_read.immediately' => 'Il messaggio è stato visualizzato immediatamente
',
    'msg.remove_from_campaign.else.message_read.immediately' => 'Il messaggio non è stato visualizzato immediatamente
',
    'msg.remove_from_campaign.if.message_read.interval' => 'Il messaggio è stato visualizzato fino a :interval :interval_type
',
    'msg.remove_from_campaign.else.message_read.interval' => 'Il messaggio non è stato visualizzato fino a :interval :interval_type
',
    'msg.remove_from_campaign.added_tags' => 'Etichette aggiunte ":tags"
',

    'msg.remove_from_campaign.if.condition_is_completed.immediately' => 'La campagna è completata per l\'abbonato :subscriber_full_name
',
    'msg.remove_from_campaign.if.condition_is_completed.interval' => 'Campagna completata per l\'abbonato :subscriber_full_name fino a :interval :interval_type
',

    'msg.remove_from_campaign.if.makes_an_order.immediately' => 'Fai ordine
',
    'msg.remove_from_campaign.if.makes_an_order.interval' => 'Fai ordine fino a :interval :interval_type
',
];
