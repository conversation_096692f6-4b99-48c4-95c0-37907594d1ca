<?php

declare(strict_types=1);

namespace Modules\Marketing\Campaign\Core\Traits\Logs;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Marketing\Campaign\Core\Models\CampaignAction;
use Modules\Marketing\Campaign\Core\Models\Logs\CampaignChannelsLog;
use Modules\Marketing\Segments\Core\Models\Logs\SegmentsLog;
use Modules\Marketing\Segments\Core\Models\Subscriber;
use MongoDB\BSON\UTCDateTime;
use stdClass;

trait CampaignLogMethods
{
    /**
     * @param mixed $type
     * @param array $ids
     * @param stdClass $reasonForRemoval
     * @return mixed
     */
    public function logSubscriberToCampaign($type, array $ids, ?stdClass $reasonForRemoval = null)
    {
        if (!in_array($type, ['add', 'remove']) || empty($ids)) {
            return null;
        }

        $subscribers = Subscriber::with('customer')
            ->whereIn('id', $ids)->get();

        if ($subscribers->count() == 1) {
            /** @var Subscriber $subscriber */
            $subscriber = $subscribers->first();
            CampaignChannelsLog::create(array_merge([
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'row',
                'subscriber_id' => $subscriber->id,
                'customer_id' => optional($subscriber->customer)->id,
                'first_name' => $subscriber->first_name,
                'last_name' => $subscriber->last_name,
                'country' => $subscriber->country,
                'action' => $type == 'add' ? 'added_to_campaign' : 'removed_from_campaign',
                'log_group' => 'campaigns',
            ], array_filter([
                'data' => $type == 'remove' ? $reasonForRemoval : null
            ])));
        } else {
            $log = CampaignChannelsLog::create(array_merge([
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'head',
                'action' => $type == 'add' ? 'begin_add_to_campaign' : 'begin_remove_from_campaign',
                'log_group' => 'campaigns',
            ], array_filter([
                'data' => $type == 'remove' ? $reasonForRemoval : null
            ])));

            $insert = $subscribers->map(fn(Subscriber $subscriber): array => array_merge([
                'parent_id' => $log->id,
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'single-row',
                'subscriber_id' => $subscriber->id,
                'customer_id' => optional($subscriber->customer)->id,
                'first_name' => $subscriber->first_name,
                'last_name' => $subscriber->last_name,
                'country' => $subscriber->country,
                'action' => $type == 'add' ? 'added_to_campaign' : 'removed_from_campaign',
                'log_group' => 'campaigns',
            ], array_filter([
                'data' => $type == 'remove' ? $reasonForRemoval : null
            ])));

            if ($insert->isNotEmpty()) {
                CampaignChannelsLog::insert($insert->values()->all());
            }
        }
    }

    /**
     * @param mixed $tags
     * @param array $ids
     * @param \Modules\Marketing\Campaign\Core\Models\CampaignAction $action
     * @return mixed
     */
    public function logAddTags($tags, array $ids, ?CampaignAction $action = null)
    {
        if (empty($tags) || empty($ids)) {
            return null;
        }

        $subscribers = Subscriber::with('customer')
            ->whereIn('id', $ids)->get();

        if ($subscribers->count() == 1) {
            /** @var Subscriber $subscriber */
            $subscriber = $subscribers->first();
            SegmentsLog::create([
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'row',
                'subscriber_id' => $subscriber->id,
                'customer_id' => optional($subscriber->customer)->id,
                'first_name' => $subscriber->first_name,
                'last_name' => $subscriber->last_name,
                'country' => $subscriber->country,
                'action' => 'added_tags',
                'log_group' => 'segments',
                'tags' => $tags,
                'campaign_action_id' => $action->id ?? null,
                'campaign_action_type' => $action->data['action_type'] ?? null,
                'campaign_action_order' => $action->order ?? null,
                'channel' => $action->data['action_type'] ?? null,
            ]);
        } else {
            $log = CampaignChannelsLog::create([
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'head',
                'action' => 'added_tags',
                'log_group' => 'campaigns',
                'tags' => $tags,
                'campaign_action_id' => $action->id ?? null,
                'campaign_action_type' => $action->data['action_type'] ?? null,
                'campaign_action_order' => $action->order ?? null,
                'channel' => $action->data['action_type'] ?? null,
            ]);

            $insert = $subscribers->map(fn(Subscriber $subscriber): array => [
                'parent_id' => $log->id,
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'single-row',
                'subscriber_id' => $subscriber->id,
                'customer_id' => optional($subscriber->customer)->id,
                'first_name' => $subscriber->first_name,
                'last_name' => $subscriber->last_name,
                'country' => $subscriber->country,
                'action' => 'added_tags',
                'log_group' => 'campaigns',
                'tags' => $tags,
                'campaign_action_id' => $action->id ?? null,
                'campaign_action_type' => $action->data['action_type'] ?? null,
                'campaign_action_order' => $action->order ?? null,
                'channel' => $action->data['action_type'] ?? null,
            ]);

            if ($insert->isNotEmpty()) {
                CampaignChannelsLog::insert($insert->values()->all());
            }
        }
    }

    /**
     * @param mixed $id
     * @param \Modules\Marketing\Campaign\Core\Models\CampaignAction $action
     * @param mixed $channelIdentifier
     * @return mixed
     */
    public function logActionMessage($id, ?CampaignAction $action = null, $channelIdentifier = null)
    {
        if (!$action instanceof \Modules\Marketing\Campaign\Core\Models\CampaignAction || !in_array($action->data['condition']['continue_condition'] ?? null, ['message_read', 'link_clicked']) || empty($id)) {
            return null;
        }

        $subscriber = Subscriber::with('customer')->find($id);
        if (!$subscriber) {
            return null;
        }

        $message = CampaignChannelsLog::where([
            'site_id' => site('site_id'),
            'subscriber_id' => $subscriber->id,
            'campaign_action_id' => $action->id,
            'action' => 'send_message',
        ])->latest()->first();

        $log = CampaignChannelsLog::create([
            'site_id' => site('site_id'),
            'segment_id' => $this->segment->id,
            'segment_name' => $this->segment->name,
            'segment_conditions' => $this->segment->conditions_formatted,
            'segment_channel' => $this->segment->channel,
            'campaign_id' => $this->id,
            'campaign_name' => $this->title,
            'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
            'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
            'type' => 'row',
            'subscriber_id' => $subscriber->id,
            'customer_id' => optional($subscriber->customer)->id,
            'first_name' => $subscriber->first_name,
            'last_name' => $subscriber->last_name,
            'country' => $subscriber->country,
            'action' => $action->data['condition']['continue_condition'],
            'log_group' => 'campaigns',
            'campaign_action_id' => $action->id,
            'campaign_action_type' => $action->data['action_type'],
            'campaign_action_order' => $action->order,
            'channel' => $action->data['action_type'],
            'channel_identifier' => $channelIdentifier,
        ]);

        $log->content()->create([
            'message' => $message->content->message ?? 'Missing data',
        ]);

    }

    /**
     * @param Illuminate\Support\Collection $subscribers
     * @param mixed $channel
     * @param mixed $channel_identifier
     * @return mixed
     */
    public function logUnsubscribeSubscriber(Collection $subscribers, $channel = null, $channel_identifier = null): void
    {
        if (empty($subscribers) || $subscribers->isEmpty()) {
            return;
        }

        if ($subscribers->count() == 1) {
            /** @var Subscriber $subscriber */
            $subscriber = $subscribers->first();
            CampaignChannelsLog::create([
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'row',
                'subscriber_id' => $subscriber->id,
                'customer_id' => optional($subscriber->customer)->id,
                'first_name' => $subscriber->first_name,
                'last_name' => $subscriber->last_name,
                'country' => $subscriber->country,
                'action' => 'unsubscribe',
                'log_group' => 'campaigns',
                'channel' => $channel,
                'channel_identifier' => $channel_identifier,
            ]);
        } else {
            $log = CampaignChannelsLog::create([
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'head',
                'action' => 'unsubscribe',
                'log_group' => 'campaigns',
                'channel' => $channel,
                'channel_identifier' => $channel_identifier,
            ]);

            $insert = $subscribers->map(fn(Subscriber $subscriber): array => [
                'parent_id' => $log->id,
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'single-row',
                'subscriber_id' => $subscriber->id,
                'customer_id' => optional($subscriber->customer)->id,
                'first_name' => $subscriber->first_name,
                'last_name' => $subscriber->last_name,
                'country' => $subscriber->country,
                'action' => 'unsubscribe',
                'log_group' => 'campaigns',
                'channel' => $channel,
                'channel_identifier' => $channel_identifier,
            ]);

            if ($insert->isNotEmpty()) {
                CampaignChannelsLog::insert($insert->values()->all());
            }
        }
    }

    /**
     * @param Illuminate\Support\Collection $subscribers
     * @param mixed $message
     * @param \Modules\Marketing\Campaign\Core\Models\CampaignAction $action
     * @return mixed
     */
    public function logNonExecuteCampaignAction(Collection $subscribers, $message, ?CampaignAction $action = null)
    {
        if (empty($subscribers) || $subscribers->isEmpty() || !$action) {
            return null;
        }

        if ($subscribers->count() == 1) {
            /** @var Subscriber $subscriber */
            $subscriber = $subscribers->first();
            CampaignChannelsLog::create([
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'row',
                'subscriber_id' => $subscriber->id,
                'customer_id' => optional($subscriber->customer)->id,
                'first_name' => $subscriber->first_name,
                'last_name' => $subscriber->last_name,
                'country' => $subscriber->country,
                'action' => 'non_execute_action',
                'log_group' => 'campaigns',
                'campaign_action_id' => $action->id,
                'campaign_action_type' => $action->data['action_type'],
                'campaign_action_order' => $action->order,
                'execute_message' => $message,
                'channel_identifier' => $subscriber->single_channel->channel_identifier,
            ]);
        } else {
            $log = CampaignChannelsLog::create([
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'head',
                'action' => 'non_execute_action',
                'log_group' => 'campaigns',
                'campaign_action_id' => $action->id,
                'campaign_action_type' => $action->data['action_type'],
                'campaign_action_order' => $action->order,
                'execute_message' => $message,
            ]);

            $insert = $subscribers->map(fn(Subscriber $subscriber): array => [
                'parent_id' => $log->id,
                'site_id' => site('site_id'),
                'segment_id' => $this->segment->id,
                'segment_name' => $this->segment->name,
                'segment_conditions' => $this->segment->conditions_formatted,
                'segment_channel' => $this->segment->channel,
                'campaign_id' => $this->id,
                'campaign_name' => $this->title,
                'created_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'updated_at' => new UTCDateTime(Carbon::now('UTC')->timestamp * 1000),
                'type' => 'single-row',
                'subscriber_id' => $subscriber->id,
                'customer_id' => optional($subscriber->customer)->id,
                'first_name' => $subscriber->first_name,
                'last_name' => $subscriber->last_name,
                'country' => $subscriber->country,
                'action' => 'non_execute_action',
                'log_group' => 'campaigns',
                'campaign_action_id' => $action->id,
                'campaign_action_type' => $action->data['action_type'],
                'campaign_action_order' => $action->order,
                'execute_message' => $message,
                'channel_identifier' => $subscriber->single_channel->channel_identifier,
            ]);

            if ($insert->isNotEmpty()) {
                CampaignChannelsLog::insert($insert->values()->all());
            }
        }
    }
}
