<?php

declare(strict_types=1);

namespace Modules\Marketing\Segments\Core\Traits;

use App\Exceptions\Error;
use App\Helper\YesNo;
use Modules\Apps\Others\Gdpr\GDPRManager;
use App\Models\Customer\Customer;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\Store\Cart;
use Auth;
use GDPR;
use MaxMind;
use Modules\Marketing\Campaign\Core\Models\Apps\CampaignChannels;
use Modules\Marketing\Segments\Core\Models\SubscribeForMissingProduct;
use Modules\Marketing\Segments\Core\Models\Subscriber;
use Modules\Marketing\Segments\Core\Models\SubscriberUuid;
use Modules\Marketing\Segments\Core\SegmentsManager;
use Throwable;

trait MissingProductSubscriberFromRequest
{
    use SaveSubscriberByChannels;

    /** @var Variant $variant */
    private $variant;

    /** @var Product $product */
    private $product;

    /** @var string|null $first_name */
    private $first_name;

    /** @var string|null $last_name */
    private $last_name;

    /** @var string|null $email */
    private $email;

    /** @var string|null $phone */
    private $phone;

    /** @var string|null $gdpr_form_name */
    private $gdpr_form_name;

    /**
     * @param string|null $gdpr_form_name
     * @return $this
     */
    public function setGdprFormName(?string $gdpr_form_name)
    {
        $this->gdpr_form_name = $gdpr_form_name;
        return $this;
    }

    /**
     * @param Product $product
     * @return $this
     */
    protected function setProduct(Product $product)
    {
        $this->product = $product;
        return $this;
    }

    /**
     * @param Variant $variant
     * @return $this
     */
    protected function setVariant(Variant $variant)
    {
        $this->variant = $variant;
        return $this;
    }

    /**
     * @param null|string $first_name
     * @return $this
     */
    protected function setFirstName(?string $first_name)
    {
        $this->first_name = $first_name;
        return $this;
    }

    /**
     * @param null|string $last_name
     * @return $this
     */
    protected function setLastName(?string $last_name)
    {
        $this->last_name = $last_name;
        return $this;
    }

    /**
     * @param string|null $email
     * @return $this
     */
    public function setEmail(?string $email)
    {
        $this->email = $email;
        return $this;
    }

    /**
     * @param string|null $phone
     * @return $this
     */
    public function setPhone(?string $phone)
    {
        $this->phone = $phone;
        return $this;
    }

    /**
     * @return Subscriber|null
     * @throws Error
     * @throws Throwable
     */
    protected function subscribe(): ?Subscriber
    {
        $subscriber_id = static::guessSubscriberId();

        $name = null;
        if ($names = array_filter([$this->first_name, $this->last_name])) {
            $name = implode(' ', array_filter($names)) ?: null;
        }

        $uuids = array_filter([request()->cookie('uuid'), request()->input('uuid')]);
        $customerId = null;
        if (request()->input('customer_id') && Customer::whereKey(request()->input('customer_id'))->value('id')) {
            $customerId = request()->input('customer_id');
        }

        $channels = [];
        $names = [];
        $campaignChannels = CampaignChannels::getConfiguredChannels()->pluck('group', 'group');
        if ($this->email && $campaignChannels->has('email')) {
            if (!$customerId && ($customer = Customer::whereEmail($this->email)->first())) {
                $customerId = $customer->id;
                if (empty($name)) {
                    $name = $customer->full_name;
                }
            }

            $channels[Subscriber::EMAIL_CHANNEL] = $this->email;
            $names[Subscriber::EMAIL_CHANNEL] = $name;
        }

        if ($this->phone && $campaignChannels->has('phone')) {
            $channels[Subscriber::PHONE_CHANNEL] = $this->phone;
            $names[Subscriber::PHONE_CHANNEL] = $name;
        }

        if ($channels) {
            $marketing = 0;
            if (GDPR::isActive() && $this->gdpr_form_name) {
                $policies = GDPR::getFormPolicies($this->gdpr_form_name);
                $gdprManager = new GDPRManager();
                $marketing_policy = $gdprManager->getSetting('marketing_policy');
                foreach ($policies as $policy) {
                    if ($policy->id == $marketing_policy && request()->has('gdpr.' . $policy->id)) {
                        $marketing = Subscriber::FORCE_MARKETING_ON;
                        break;
                    }
                }
            } elseif (!setting('hide_marketing') && (new SegmentsManager())->getTermsPages()->isNotEmpty()) {
                $marketing = Subscriber::FORCE_MARKETING_ON;
            }

            $subscribers = $this->saveSubscriberByChannels(
                $channels,
                $names,
                $customerId,
                $uuids,
                $marketing,
                MaxMind::getCountryIso2(),
                request()->userAgent(),
                'verify',
                $subscriber_id,
                null,
                Subscriber::FROM_SUBSCRIBE_FOR_MISSING_PRODUCT
            );

            /** @var Subscriber|null $subscriber */
            $subscriber = $subscribers->filter()->first();
            if (!$subscriber) {
                return $subscriber;
            }

            /** @var SubscribeForMissingProduct $subscribed */
            $subscribed = $subscriber->subscribe_for_missing_product()->updateOrCreate([
                'subscriber_id' => $subscriber->id,
                'product_id' => $this->product->id,
                'variant_id' => $this->variant->id ?? $this->product->default_variant_id,
            ], [
                'variant_id' => $this->variant->id ?? $this->product->default_variant_id,
            ]);

            if (!$subscribed->wasRecentlyCreated) {
                $subscribed->notifications()->delete();
            }

            if ($customerId && !empty($channels[Subscriber::EMAIL_CHANNEL]) && $marketing == Subscriber::FORCE_MARKETING_ON) {
                Customer::whereKey($customerId)->update([
                    'marketing' => YesNo::True,
                ]);
            }

            return $subscriber;
        }

        return null;
    }

    /**
     * @return null|int
     */
    protected static function guessSubscriberId(): ?int
    {
        if (app_namespace() != 'site' || !(Auth::adminId() ? null : Auth::subscriberId())) {
            return null;
        }

        try {
            if (!($subscriberId = Auth::subscriberId())) {
                if (($check = (Cart::instance()->subscriber_id ?? null)) && Subscriber::whereKey($check)->exists()) {
                    $subscriberId = $check;
                } elseif (request()->cookie('uuid')) {
                    $subscriberId = SubscriberUuid::where('uuid', request()->cookie('uuid'))->value('subscriber_id');
                }
            }

            return $subscriberId ?? null;
        } catch (Throwable) {
            return null;
        }
    }

}
