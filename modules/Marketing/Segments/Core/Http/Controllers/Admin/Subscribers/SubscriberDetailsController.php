<?php

declare(strict_types=1);

namespace Modules\Marketing\Segments\Core\Http\Controllers\Admin\Subscribers;

use App\Common\DateTimeFormat;
use App\Helper\Format;
use App\Helper\Grid;
use App\Models\Customer\Customer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Modules\Marketing\Campaign\Core\Models\Logs\CampaignChannelsStatistic;
use Modules\Marketing\Segments\Core\Http\Controllers\Admin\AbstractSegmentsController;
use Modules\Marketing\Segments\Core\Models\Subscriber;
use Modules\Marketing\Segments\Core\Models\SubscribersSegment;
use Throwable;

/**
 * Class SubscriberDetailsController
 * @package App\Http\Controllers\Sitecp
 */
class SubscriberDetailsController extends AbstractSegmentsController
{
    /**
     * @param $id
     * @return Response
     * @throws Throwable
     */
    public function uuids($id): Response
    {
        $subscriber = Subscriber::findOrFail($id);

        $grid = new Grid('updated_at', 'desc');

        $uuids = $subscriber->uuids()
            ->getRelationList(null, null, $grid);

        return $grid->generateIlluminate($uuids, null, [
            'records' => $grid->pagging->results
        ]);
    }

    /**
     * @param $id
     * @return Response
     * @throws Throwable
     */
    public function customers($id): Response
    {
        $subscriber = Subscriber::findOrFail($id);

        $grid = new Grid('subscriber_to_customer.id', 'desc');

        $customers = $subscriber->customers()->with('group')
            ->getRelationList(null, null, $grid);

        $customers->transform(fn(Customer $customer): array => [
            'id' => $customer->id,
            'name' => view('segments::admin.subscribers.includes.customer-name', ['customer' => $customer])->render(),
            'email' => $customer->email,
            'group_name' => $customer->group->name ?? null,
            'orders_total' => $customer->orders_total,
            'income' => money(intval($customer->income)),
        ]);

        return $grid->generateIlluminate($customers, null, [
            'records' => $grid->pagging->results
        ]);
    }

    /**
     * @param $id
     * @return JsonResponse
     * @throws Throwable
     */
    public function segments($id): JsonResponse
    {
        $subscriber = Subscriber::findOrFail($id);

        $segments = $subscriber->segments()
            ->select([
                'subscribers_segments.id',
                'subscribers_segments.name',
                'subscriber_to_segments.updated_at',
            ])
            ->orderBy('subscriber_to_segments.updated_at', 'desc')
            ->orderBy('subscribers_segments.id', 'desc')
            ->get();

        $segments->transform(fn(SubscribersSegment $segment): array => [
            'id' => $segment->id,
            'name' => $segment->name,
            'updated_at' => $segment->updated_at->format(DateTimeFormat::getFormatByTemplate()),
        ]);

        return response()->json($segments);
    }

    /**
     * @param $id
     * @return JsonResponse
     * @throws Throwable
     */
    public function channelStatistic($id): JsonResponse
    {
        $subscriber = Subscriber::findOrFail($id);

        return response()->json(
            CampaignChannelsStatistic::getStatisticByChannel(['subscriber_id' => $subscriber->id], ['successfully_sent', 'total_sent'], false)
        );
    }

    /**
     * @param $id
     * @return JsonResponse
     * @throws Throwable
     */
    public function statistic($id): JsonResponse
    {
        $subscriber = Subscriber::findOrFail($id);

        $stat = CampaignChannelsStatistic::getStatisticBySubscribers(collect([$subscriber]))->get($subscriber->id);

        CampaignChannelsStatistic::updateSubscriberStatistic($subscriber, $stat);

        return response()->json([
            'reached' => $stat['reached'] ?? 0,
            'open_rate' => Format::percent($stat['open_rate'] ?? 0),
            'click_rate' => Format::percent($stat['click_rate'] ?? 0),
            'conversion_rate' => Format::percent($stat['conversionRate'] ?? 0),
            'revenue' => Format::money($stat['revenue'] ?? 0)
        ]);
    }
}
