<?php

declare(strict_types=1);

return [
    'action.add' => 'Aggiungi segmento',
    'action.add_form' => 'Aggiungi modulo',
    'action.add_conditions' => 'Aggiungi condizioni',
    'action.change_conditions' => 'Cambia condizioni',
    'action.conditions' => 'Condizioni di segmentazione',
    'action.save_conditions' => 'Salva condizioni',
    'conditions' => 'Condizioni',
    'customer.type' => 'Questo tipo include utenti che hanno effettuato un ordine nel tuo negozio online.',
    'err.choose.type' => 'Devi scegliere un tipo di segmento',
    'err.name' => 'Il segmento deve avere un nome',
    'err.filters' => 'Il segmento deve avere condizioni di nome',
    'err.used_in_campaign' => 'Non puoi eliminare il segmento perché è utilizzato nelle campagne: %1$s',
    'err.delete' => 'Non puoi eliminare un segmento che ha una campagna ad essa associata. Per eliminare un segmento, devi prima eliminare una campagna',
    'err.delete_bulk' => 'Non puoi eliminare segmenti che hanno campagne associate. Per eliminare segmenti, devi prima eliminare una campagna',
    'feed.customers_button' => 'Utenti',
    'feed.messenger_subscribers_button' => 'Iscritti nel segmento',
    'filter.abandoned_cart' => 'Carrello abbandonato',
    'filter.added_to_cart_products_from_category_%1$s' => 'Prodotti aggiunti al carrello dalla categoria %1$s',
    'filter.added_to_cart_products_from_vendor_%1$s' => 'Prodotti aggiunti al carrello dal venditore %1$s',
    'filter.added_to_wishlist_%1$s_%2$s' => 'Prodotto aggiunto alla lista dei desideri %1$s %2$s',
    'filter.date_interval' => ' %1$s %2$s %3$s',
    'filter.in_last' => 'Negli ultimi',
    'filter.after' => 'Nella prossima',
    'filter.never' => 'Mai',
    'filter.sometime' => 'Qualche volta',
    'filter.income' => 'Fatturato',
    'filter.is_%1$s' => 'il segmento cliente è %1$s',
    'filter.last_active_in_site_%1$s_%2$s' => 'Ultima attività sul sito %1$s %2$s',
    'filter.last_order' => 'Ultimo ordine',
    'filter.order_amount' => 'Importo dell’ordine',
    'filter.ordered_product' => 'Prodotto ordinato %1$s',
    'filter.not_ordered_products_from_category' => 'Prodotti non ordinati dalla categoria',
    'filter.not_ordered_products_from_vendor' => 'Prodotti non ordinati dal venditore',
    'filter.orders_made' => 'Conteggio ordini',
    'filter.started_checkout_but_didnt_finish' => 'Checkout avviato ma non completato %1$s %2$s %3$s',
    'filter.subscribed_for' => 'Iscritto per',
    'filter.viewed_category_%1$s' => 'Categoria vista %1$s',
    'filter.viewed_page_%1$s' => 'Pagina visitata %1$s',
    'filter.viewed_products_from_category' => 'Prodotti visti dalla categoria',
    'filter.viewed_products_from_vendor' => 'Prodotti visti dal venditore',
    'filter.viewed_products_with_amount_%1$s_%2$s' => 'Prodotti visti con costo %1$s %2$s',
    'filter.viewed_vendor_%1$s' => 'Venditore visualizzato %1$s',
    'filter.viewed_product_%1$s' => 'Prodotto visualizzato %1$s',
    'filter.last_order_status' => 'Ultimo stato dell’ordine',
    'filter.openRate' => 'Tasso di apertura',
    'filter.clickRate' => 'Tasso di clic',
    'filter.has_no_orders' => 'O non ha ordini',
    'filter.rfm_analytics' => 'Analisi RFM',
    'filter.rfm_nan' => 'Senza analisi RFM',
    'header.add' => 'Aggiungi segmento',
    'header.customer_segments' => 'Segmenti',
    'header.edit' => 'Modifica segmento',
    'label.choose_type' => 'Scegli tipo di segmento',
    'label.description' => 'Scegli i segmenti a cui vuoi inviare il messaggio',
    'label.title' => 'Segmenti',
    'messenger.type' => 'Questo tipo sarà composto da utenti che hanno inviato un messaggio alla tua pagina Facebook ma non si sono registrati nel tuo negozio. Puoi utilizzare questo tipo di segmento per inviare campagne periodicche tramite Facebook Messenger.',
    'notify.segment_deleted' => 'A causa di un cambiamento nella funzionalità della piattaforma, abbiamo dovuto eliminare alcuni dei tuoi segmenti Messenger.',
    'succ.edited' => 'Segmento cliente modificato con successo',
    'th.name' => 'Nome del segmento',
    'th.segment_feed' => 'Genera file CSV',
    'th.subscribers.channels' => 'Canali',
    'th.subscribers.channel' => 'Canale',
    'th.subscribers.channel_identifier' => 'Identificatore canale',
    'tooltip.count_info' => 'Puoi aggiungere fino a 4 condizioni',
    'th.last_generated' => 'Ultima generazione il',
    'label.require_email' => 'Richiesta email',
    'filter.cart_amount' => 'Importo prodotti del carrello',
    'filter.filters_required' => 'Devi scegliere almeno una condizione',
    'filter.verified' => 'Ha verificato la sua email',
    'text.verified' => 'Verificato',
    'filter.type' => 'Tipo di iscritto',
    'filter.deviceOs' => 'OS',
    'filter.deviceType' => 'Tipo di dispositivo',
    'filter.browserType' => 'Browser',
    'notify.no_matching_subscribers' => 'Non hai iscritti che soddisfano queste condizioni.',
    'notify.no_matching_subscribers_forms' => 'Non hai moduli per iscritti che soddisfano queste condizioni.',
    'notify.subscribers_generating' => 'I tuoi iscritti sono attualmente in fase di filtraggio, per favore controlla di nuovo più tardi.',
    'th.subscriber.type' => 'Tipo',
    'subscriber.type.customer' => 'Cliente',
    'subscriber.type.subscriber' => 'Iscritto',
    'btn.subscriber_form' => 'Modulo di iscrizione',
    'info.form_purpose' => 'Utilizzando questo modulo, potrai raccogliere iscritti e successivamente contattarli via email o telefono, anche prima che diventino tuoi clienti',
    'info.segment_generating' => 'I tuoi iscritti sono attualmente in fase di filtraggio',
    'info.segment_generated' => 'Filtraggio completato',
    'info.segment_not_generated' => 'Non generato ancora',
    'successfully.verified' => 'Hai verificato con successo il tuo indirizzo email',
    //errors
    'error.from_to_interval.gt' => 'Il valore deve essere maggiore di :value.',
    'error.from_to_interval.lte' => 'Il valore deve essere minore o uguale a :value.',
    'error.required' => 'Il campo è obbligatorio.',
    'error.integer' => 'Il valore deve essere un intero.',
    'error.min' => 'Il valore deve essere almeno :min.',
    'error.max' => 'Il valore non può essere maggiore di :max.',
    'error.numeric' => 'Il valore deve essere un numero.',
    'error.exists' => 'Il valore non è valido.',
    'text.log' => 'Registro',
    'label.marketing' => 'Accetta marketing',
    'label.terms_policy' => 'Politiche e marketing',
    'import_subscribers' => 'Importa iscritti',
    'import_subscriber_email' => 'Email dell’iscritto',
    'import_subscriber_phone' => 'Telefono dell’iscritto',
    'import_subscriber_first_name' => 'Nome dell’iscritto',
    'import_subscriber_last_name' => 'Cognome dell’iscritto',
    'import_subscriber_marketing' => 'Iscritto accetta marketing (Esempio: 1 - sì, 0 - no. Per impostazione predefinita /vuoto/: sì)',
    'import.label.marketing' => 'Contrassegna tutte le email che consentono marketing',
    'import.label.verified' => 'Verifica email',
    'import_subscriber_user_agent' => 'User agent del browser',
    'filter.order_shipping' => 'Metodo di spedizione',
    'filter.last_order_shipping' => 'Ultimo metodo di spedizione dell’ordine',
    'filter.order_payment' => 'Metodo di pagamento',
    'filter.last_order_payment' => 'Ultimo metodo di pagamento dell’ordine',
    'filter.orders_discount' => 'Sconto',
    'filter.manual_added' => 'Aggiunto manualmente',
    'th.unsubscribed' => 'Non iscritti',
    'th.bounced' => 'Rimbalzato',
    'th.partial_bounced' => 'Rimbalzato parzialmente',
    'label.uuids' => 'Dispositivi identificati dell’iscritto',
    'label.segments' => 'Segmenti a cui partecipa l’iscritto',
    'default_channel' => 'Canale di comunicazione attuale',
    'th.subscribed_on' => 'Iscritto il',
    'th.last_active_on' => 'Ultima attività il',
    'th.identified_on' => 'Identificato il',
    'th.verified' => 'Verificato',
    'statistics.click_rate' => 'Tasso di clic',
    'statistics.conversion_rate' => 'TASSO DI CONVERSIONE',
    'statistics.open_rate' => 'Tasso di apertura',
    'statistics.revenue' => 'Fatturato',
    'label.customers' => 'Clienti connessi con l’iscritto',
    'channel.Email' => 'Email',
    'channel.Messenger' => 'Messenger',
    'channel.Phone' => 'Telefono',
    'channel.Viber' => 'Viber',
    'channel.uuid' => 'UUID',
    'channel.WebPush' => 'Web Push',
    'verified.email.unverified' => 'Nessun messaggio sarà inviato a questa email perché non è stata verificata',
    'err.unique_conditions' => 'Esiste un segmento esistente con questa condizione|Esistono segmenti esistenti con queste condizioni',
    'err.channel_identifier.unique' => 'Questo identificatore è già in uso da un altro iscritto. <a href=":url" data-ajax-panel data-panel-class="wide">Vuoi unire gli iscritti?</a>',
    'feature_limit_warning_alert' => 'Hai raggiunto il limite della funzionalità <b>Iscritti - :limit</b><br>Per continuare devi acquistare un pacchetto di funzionalità o passare a un piano con limiti più alti!',
    'feature_limit_warning' => 'Hai :has iscritti. Il tuo limite è :limit',
    //products review
    'filter.productReviewLastDate' => 'Ultimo commento',
    'filter.productReviewWithoutComment' => 'Nessun commento sui prodotti acquistati dall’ultimo ordine',
    'filter.productReviewLastRating' => 'Ultima valutazione',
    'filter.productReviewTotal' => 'Numero di commenti',
    'filter.productReviewAvgRating' => 'Valutazione media di tutti i commenti',
    'filter.productReviewCategory' => 'Commentato sui prodotti della categoria',
    'filter.productReviewVendor' => 'Commentato sui prodotti di un venditore',
    'filter.productReviewNotCategory' => 'Non commentato sui prodotti della categoria',
    'filter.productReviewNotVendor' => 'Non commentato sui prodotti di un venditore',
    'filter.planLimit' => 'Attivo per segmenti',
    'text.add_condition' => 'Aggiungi condizione',
    'help.conditions' => 'Tutte le condizioni hanno un logico "E"',
    'label.subscriber_from_form' => 'Iscritto dal modulo',
    'text.subscribed_from_customer_login' => 'Accesso cliente',
    'text.subscribed_from_subscribe_form' => 'Popup e costruttore di moduli',
    'text.subscribed_from_import' => 'Importa',
    'text.subscribed_from_system' => 'Dal sistema',
    'text.subscribed_from_customer_address_creating' => 'Creazione indirizzo cliente',
    'text.subscribed_from_customer_address_deleting' => 'Eliminazione indirizzo cliente',
    'text.subscribed_from_customer_creating' => 'Creazione cliente',
    'text.subscribed_from_order_creating' => 'Creazione ordine',
    'text.subscribed_from_messenger' => 'Messaggero Facebook',
    'text.subscribed_from_contacts_form' => 'Modulo contatti',
    'text.subscribed_from_web_push' => 'Web push',
    'text.newest' => 'Nuovo',
    'text.sale' => 'Saldi',
    'action.detach_subscribers' => 'Rimuovi dal segmento',
    'action.add_subscriber' => 'Aggiungi iscritto al segmento',
    'title.subscriber.merge_this' => 'Canale duplicato',
    'title.subscriber.merge_other_match' => 'Altri canali duplicati',
    'title.subscriber.merge_other_nomatch' => 'Altri canali non duplicati',
    'text.group_by' => 'Raggruppato per registrazione',
    'help.planLimit' => 'Questo segmento utilizzerà i primi :limit dei :total iscritti.',
    'help.statuses.text' => 'Stati degli ordini che saranno considerati per un fatturato di ordini effettuati da un utente',
    'label.rfm_interval' => 'Intervallo di giorni per il quale fare analisi RFM',
    'text.subscribers' => 'Iscritto|Iscritti',
    'import.text.verify' => 'Contrassegna tutti come verificati',
    'import.text.email' => 'Invia email con link per verificare',
    'marketing.yes' => 'Accetta marketing',
    'marketing.no' => 'Non accetta marketing',
    'filter.bestseller_period' => 'Bestseller',
    'label.bestseller_period' => 'Intervallo bestseller',
    'label.bestseller_period.help' => 'Seleziona un intervallo di giorni per i prodotti più venduti nelle email',
    'text.subscribed_from_subscribe_from_missing_product' => 'Iscriviti a un prodotto disponibile',
    'text.subscribed_from_API' => 'API',
    'text.automated' => 'Automatizzato',
    'text.regular' => 'Una tantum',
    'help.automated' => 'Automatizzato',
    'help.regular' => 'Una tantum',
    'filter.ordered_products_from_category' => 'Prodotti ordinati dalla categoria',
    'filter.ordered_products_from_vendor' => 'Prodotti ordinati dalla marca',
];
