<?php

declare(strict_types=1);

return [
    'error.conditions_allowed.missing_product' => 'Die Bedingung \\":condition\\" kann nur mit den folgenden Bedingungen kombiniert werden: \\":conditions\\"',
    'error.empty_conditions' => 'Du musst Bedingungen hinzufügen.',
    'error.empty_record' => 'Du musst spezifische Datensätze auswählen.',
    'error.empty_record_and_conditions' => 'Du musst spezifische Datensätze auswählen oder Bedingungen hinzufügen.',
    'error.empty_value' => 'Wert ist erforderlich.',
    'error.inactive' => 'Das Segment ist aufgrund des folgenden Fehlers deaktiviert: :errors|Das Segment wurde aufgrund der folgenden Fehler deaktiviert: :errors',
    'error.numeric' => 'Wert muss eine Zahl sein.',
    'error.numeric_value.max' => 'Wert darf nicht größer als :max sein.',
    'error.numeric_value.min' => 'Wert muss mindestens :min sein.',
    'error.record_not_exists' => 'Einer oder mehrere der gewählten Datensätze existieren nicht.',
    'label.begin' => 'Beginnt mit',
    'label.begin_order' => 'Checkout initiiert, aber nicht abgeschlossen',
    'label.begin_order_amount' => 'Bestellbetrag',
    'label.begin_order_customer' => 'Bestellkunde',
    'label.begin_order_customer_country' => 'Bestellkunde Land',
    'label.begin_order_customer_country_region' => 'Ort aus dem Land',
    'label.begin_order_customer_customer_group' => 'Bestellkunde Gruppe',
    'label.begin_order_customer_date_interval' => 'Bestellkunde registriert vor/nach',
    'label.begin_order_customer_tag' => 'Bestellkunde Tag',
    'label.begin_order_date_interval' => 'Bestelldatum vor/nach',
    'label.begin_order_payment' => 'Bestell Zahlungsmethode',
    'label.begin_order_product' => 'Bestellprodukt',
    'label.begin_order_product_amount' => 'Bestellprodukte Menge',
    'label.begin_order_product_average' => 'Durchschnittspreis des Bestellprodukts',
    'label.begin_order_product_category' => 'Bestellproduktkategorie',
    'label.begin_order_product_price' => 'Bestellproduktpreis',
    'label.begin_order_product_quantity' => 'Bestellproduktmenge',
    'label.begin_order_product_tag' => 'Bestellprodukt Tag',
    'label.begin_order_product_times' => 'Produktanzahl in der Bestellung',
    'label.begin_order_product_vendor' => 'Bestellprodukt Marke',
    'label.begin_order_shipping' => 'Bestellversanddienstleister',
    'label.browser' => 'Browser',
    'label.cart' => 'Warenkorb',
    'label.cart_abandoned' => 'Warenkorb verlassen',
    'label.cart_amount' => 'Warenkorb Betrag',
    'label.cart_customer' => 'Warenkorb Kunde',
    'label.cart_customer_country' => 'Warenkorb Kunde Land',
    'label.cart_customer_country_region' => 'Ort aus dem Land',
    'label.cart_customer_customer_group' => 'Warenkorb Kunde Gruppe',
    'label.cart_customer_date_interval' => 'Warenkorb Kunde registriert vor/nach',
    'label.cart_customer_tag' => 'Warenkorb Kunde Tag',
    'label.cart_payment' => 'Warenkorb Zahlungsmethode',
    'label.cart_product' => 'Warenkorb Produkt',
    'label.cart_product_amount' => 'Warenkorb Produkte Menge',
    'label.cart_product_average' => 'Durchschnittspreis des Warenkorbprodukts',
    'label.cart_product_category' => 'Warenkorb Produktkategorie',
    'label.cart_product_price' => 'Warenkorb Produktpreis',
    'label.cart_product_quantity' => 'Warenkorb Produktmenge',
    'label.cart_product_tag' => 'Warenkorb Produkt Tag',
    'label.cart_product_times' => 'Produktanzahl im Warenkorb',
    'label.cart_product_vendor' => 'Warenkorb Produkt Marke',
    'label.cart_shipping' => 'Warenkorb Versandmethoden',
    'label.channel' => 'Für Kanal abonniert',
    'label.channel_contains' => 'Enthält',
    'label.channel_verified' => 'Kanal verifiziert',
    'label.click_rate' => 'Klickrate',
    'label.contains' => 'Enthält',
    'label.country' => 'Land',
    'label.country_region' => 'Ort aus dem Land',
    'label.custom_field' => 'Benutzerdefinierte Felder',
    'label.customer' => 'Kunde',
    'label.customer_country' => 'Kunde Land',
    'label.customer_country_region' => 'Ort aus dem Land',
    'label.customer_custom_field' => 'Benutzerdefinierte Felder',
    'label.customer_customer_group' => 'Kundengruppe',
    'label.customer_date_interval' => 'Kunde registriert vor/nach',
    'label.customer_tag' => 'Kunde Tag',
    'label.date' => 'Abonnierungsdatum',
    'label.date_interval' => 'Abonniert vor/nach Tagen',
    'label.device_type' => 'Gerätetyp',
    'label.end' => 'Endet mit',
    'label.first_name' => 'Vorname',
    'label.group.cart' => 'Warenkorb',
    'label.group.category' => 'Kategorie',
    'label.group.customer' => 'Kunde',
    'label.group.customer_group' => 'Kundengruppe',
    'label.group.date' => 'Datum',
    'label.group.favorite' => 'Wunschliste',
    'label.group.global' => 'Sonstiges',
    'label.group.missing_product' => 'Für die Verfügbarkeit des Produkts abonniert, und die bereits verfügbar sind',
    'label.group.order' => 'Bestellung',
    'label.group.page' => 'Landing-Seite',
    'label.group.product' => 'Produkt',
    'label.group.region' => 'Region',
    'label.group.subscriber' => 'Abonnent',
    'label.group.vendor' => 'Marke',
    'label.group.view' => 'Ansicht',
    'label.last_active' => 'Zuletzt aktiv vor/nach Tagen',
    'label.last_name' => 'Nachname',
    'label.missing_product' => 'Für die Verfügbarkeit des Produkts abonniert, und die bereits verfügbar sind',
    'label.missing_product_date_interval' => 'Vor/nach Tagen abonniert',
    'label.missing_product_product' => 'Produkt',
    'label.missing_product_product_category' => 'Kategorie',
    'label.missing_product_product_quantity' => 'Produktmenge',
    'label.missing_product_product_vendor' => 'Marke',
    'label.not_begin' => 'Beginnt nicht mit',
    'label.not_contains' => 'Enthält nicht',
    'label.not_end' => 'Endet nicht mit',
    'label.open_rate' => 'Öffnungsrate',
    'label.order' => 'Bestellung',
    'label.order_amount' => 'Bestellbetrag',
    'label.order_average' => 'Bestelldurchschnitt',
    'label.order_country' => 'Bestellland',
    'label.order_country_region' => 'Ort aus dem Land',
    'label.order_customer' => 'Bestellkunde',
    'label.order_customer_country' => 'Bestellkunde Land',
    'label.order_customer_country_region' => 'Ort aus dem Land',
    'label.order_customer_customer_group' => 'Bestellkunde Gruppe',
    'label.order_customer_date_interval' => 'Bestellkunde registriert vor/nach',
    'label.order_customer_group' => 'Bestellkunde Gruppe',
    'label.order_customer_tag' => 'Bestellkunde Tag',
    'label.order_date' => 'Bestelldatum',
    'label.order_date_interval' => 'Bestellung vor/nach Tagen',
    'label.order_discount' => 'Bestellrabatt',
    'label.order_last' => 'Wo die Bestellung zuletzt ist',
    'label.order_payment' => 'Bestell Zahlungsmethode',
    'label.order_price' => 'Bestellpreis insgesamt',
    'label.order_product' => 'Bestellprodukt',
    'label.order_product_amount' => 'Bestellprodukte Menge',
    'label.order_product_average' => 'Durchschnittspreis des Bestellprodukts',
    'label.order_product_category' => 'Bestellproduktkategorie',
    'label.order_product_newest' => 'Neu/Nicht neu',
    'label.order_product_price' => 'Bestellproduktpreis',
    'label.order_product_quantity' => 'Bestellproduktmenge',
    'label.order_product_sale' => 'Sale/Nicht Sale',
    'label.order_product_tag' => 'Bestellprodukt Tag',
    'label.order_product_times' => 'Produktanzahl in der Bestellung',
    'label.order_product_vendor' => 'Bestellprodukt Marke',
    'label.order_shipping' => 'Bestellversanddienstleister',
    'label.order_status' => 'Bestellstatus',
    'label.order_status_fulfillment' => 'Bestellstatus Abwicklung',
    'label.order_status_fulfillment_date_interval' => 'Bestellabwicklung vor/nach Tagen',
    'label.order_times' => 'Bestellzeiten',
    'label.order_utm_campaign' => 'Bestell utm Kampagne',
    'label.order_utm_medium' => 'Bestell utm Medium',
    'label.order_utm_source' => 'Bestell utm Quelle',
    'label.os' => 'Betriebssystem',
    'label.registered_interval' => 'Registriert vor/nach Tagen',
    'label.rfm' => 'RFM',
    'label.subscriber' => 'Abonnent',
    'label.subscriber_from' => 'Abonniert von',
    'label.subscriber_from_form' => 'Abonniert über Formular',
    'label.tag' => 'Getaggt',
    'label.type' => 'Abonnent Typ',
    'label.view' => 'Ansicht',
    'label.view_category' => 'Kategorie anzeigen',
    'label.view_category_date' => 'Kategorie am Datum anzeigen',
    'label.view_category_date_interval' => 'Kategorie vor/nach Tagen anzeigen',
    'label.view_category_times' => 'Kategorie anzeigen Zeiten',
    'label.view_page' => 'Seite anzeigen',
    'label.view_page_date' => 'Seite am Datum anzeigen',
    'label.view_page_date_interval' => 'Seite vor/nach Tagen anzeigen',
    'label.view_page_times' => 'Seite anzeigen Zeiten',
    'label.view_product' => 'Produkt anzeigen',
    'label.view_product_amount' => 'Produktmenge anzeigen',
    'label.view_product_average' => 'Durchschnittspreis des Produkts anzeigen',
    'label.view_product_category' => 'Produktkategorie anzeigen',
    'label.view_product_date' => 'Produkt am Datum anzeigen',
    'label.view_product_date_interval' => 'Produkt vor/nach Tagen anzeigen',
    'label.view_product_price' => 'Produktpreis anzeigen',
    'label.view_product_quantity' => 'Produktmenge anzeigen',
    'label.view_product_tag' => 'Produkt Tag anzeigen',
    'label.view_product_times' => 'Produkt anzeigen Zeiten n',
    'label.view_product_vendor' => 'Produkt Marke anzeigen',
    'label.view_products_times' => 'Produkte anzeigen n Zeiten',
    'label.view_vendor' => 'Marke anzeigen',
    'label.view_vendor_date' => 'Marke am Datum anzeigen',
    'label.view_vendor_date_interval' => 'Marke vor/nach Tagen anzeigen',
    'label.view_vendor_times' => 'Marke anzeigen Zeiten',
    'label.wish_list' => 'Produkt zur Wunschliste hinzugefügt',
    'label.wish_list_category' => 'Produkt zur Wunschliste aus Kategorie hinzugefügt',
    'label.wish_list_date_interval' => 'Produkt zur Wunschliste vor/nach Tagen hinzugefügt',
    'label.wish_list_vendor' => 'Produkt zur Wunschliste von Marke hinzugefügt',
    'label.without_order' => 'Ohne Bestellungen',
    'label.without_order_date_interval' => 'Bestellung vor/nach Tagen',
    'label.without_order_discount' => 'Bestellrabatt',
    'label.without_order_product' => 'Bestellprodukt',
    'label.without_order_product_amount' => 'Bestellprodukte Menge',
    'label.without_order_product_average' => 'Durchschnittspreis des Bestellprodukts',
    'label.without_order_product_category' => 'Bestellproduktkategorie',
    'label.without_order_product_price' => 'Bestellproduktpreis',
    'label.without_order_product_quantity' => 'Bestellproduktmenge',
    'label.without_order_product_tag' => 'Bestellprodukt Tag',
    'label.without_order_product_times' => 'Produktanzahl in der Bestellung',
    'label.without_order_product_vendor' => 'Bestellprodukt Marke',
    'label.without_order_status' => 'Bestellstatus',
    'label.without_order_status_fulfillment' => 'Bestellstatus Abwicklung',
    'text.abandoned' => 'abgebrochen',
    'text.amount<' => 'hat einen Betrag von weniger als :price',
    'text.amount<>' => 'hat einen Betrag, der nicht gleich :price ist',
    'text.amount=' => 'hat einen Betrag, der gleich :price ist',
    'text.amount>' => 'hat einen Betrag von mehr als :price',
    'text.average.order<' => 'der durchschnittliche Bestellbetrag ist weniger als :price',
    'text.average.order<>' => 'der durchschnittliche Bestellbetrag ist nicht gleich :price',
    'text.average.order=' => 'der durchschnittliche Bestellbetrag ist gleich :price',
    'text.average.order>' => 'der durchschnittliche Bestellbetrag ist mehr als :price',
    'text.average<' => 'hat einen Durchschnittspreis von weniger als :price',
    'text.average<>' => 'hat einen Durchschnittspreis, der nicht gleich :price ist',
    'text.average=' => 'hat einen Durchschnittspreis, der gleich :price ist',
    'text.average>' => 'hat einen Durchschnittspreis von mehr als :price',
    'text.begin_order' => 'wo der Checkout initiiert wurde, aber nicht abgeschlossen wurde',
    'text.browser<>' => 'wo der Browser nicht “:browser” ist|wo die Browser nicht “:browser” sind',
    'text.browser=' => 'wo der Browser “:browser” ist|wo die Browser “:browser” sind',
    'text.cart' => 'hat Warenkorb',
    'text.category' => 'Kategorien',
    'text.category.view<>' => 'Kategorie, die nicht “:category” ist|Kategorien, die nicht “:category” sind',
    'text.category.view=' => 'Kategorie “:category”|Kategorien “:category”',
    'text.category<>' => 'wo die Kategorie nicht “:category” ist|wo die Kategorien nicht “:category” sind',
    'text.category=' => 'wo die Kategorie “:category” ist|wo die Kategorien “:category” sind',
    'text.channel<>' => 'ist nicht für den Kanal “:channel” abonniert|ist nicht für die Kanäle “:channel” abonniert',
    'text.channel=' => 'ist für den Kanal “:channel” abonniert|ist für die Kanäle “:channel” abonniert',
    'text.click_rate<' => 'hat eine Klickrate von weniger als :click_rate',
    'text.click_rate<>' => 'hat eine Klickrate, die nicht gleich :click_rate ist',
    'text.click_rate=' => 'hat eine Klickrate, die gleich :click_rate ist',
    'text.click_rate>' => 'hat eine Klickrate von mehr als :click_rate',
    'text.contains.<>' => 'und Kanal-Identifikator ist nicht \\":value\\"',
    'text.contains.=' => 'und Kanal-Identifikator ist \\":value\\"',
    'text.contains.begin' => 'und Kanal-Identifikator beginnt mit \\":value\\"',
    'text.contains.contains' => 'und Kanal-Identifikator enthält \\":value\\"',
    'text.contains.end' => 'und Kanal-Identifikator endet mit \\":value\\"',
    'text.contains.not_begin' => 'und Kanal-Identifikator beginnt nicht mit \\":value\\"',
    'text.contains.not_contains' => 'und Kanal-Identifikator enthält nicht \\":value\\"',
    'text.contains.not_end' => 'und Kanal-Identifikator endet nicht mit \\":value\\"',
    'text.country.single<>' => 'nicht aus Land \\":country\\"|nicht aus Ländern \\":country\\"',
    'text.country.single=' => 'aus Land \\":country\\"|aus Ländern \\":country\\"',
    'text.country<>' => 'die nicht aus einem Land “:country” kommen|die nicht aus Ländern “:country” kommen',
    'text.country=' => 'die aus einem Land “:country” kommen|die aus Ländern “:country” kommen',
    'text.custom_field<>' => 'wo benutzerdefiniertes Feld \\":field\\" ist nicht \\":options\\"',
    'text.custom_field=' => 'wo benutzerdefiniertes Feld \\":field\\" ist \\":options\\"',
    'text.customer.has' => 'von Kunden',
    'text.customer<>' => 'wo der Kunde nicht “:customer” ist|wo die Kunden nicht “:customer” sind',
    'text.customer=' => 'wo der Kunde “:customer” ist|wo die Kunden “:customer” sind',
    'text.customer_group<>' => 'wo die Kundengruppe nicht “:group” ist',
    'text.customer_group=' => 'wo die Kundengruppe “:group” ist',
    'text.date.order<' => 'das Bestelldatum ist vor :date',
    'text.date.order<>' => 'das Bestelldatum ist nicht :date',
    'text.date.order=' => 'das Bestelldatum ist :date',
    'text.date.order>' => 'das Bestelldatum ist nach :date',
    'text.date.subscribed<' => 'vor :date',
    'text.date.subscribed<>' => 'ist nicht abonniert :date',
    'text.date.subscribed=' => 'ist am :date abonniert',
    'text.date.subscribed>' => 'nach :date',
    'text.date.view<' => 'vor :date',
    'text.date.view<>' => 'ist nicht :date',
    'text.date.view=' => 'am :date',
    'text.date.view>' => 'nach :date',
    'text.date<' => 'Datum ist vor :date',
    'text.date<>' => 'Datum ist nicht :date',
    'text.date=' => 'Datum ist :date',
    'text.date>' => 'Datum ist nach :date',
    'text.date_interval' => '',
    'text.date_interval.after' => 'in nächster :interval :type',
    'text.date_interval.after_more_than' => 'nach mehr als :interval :type',
    'text.date_interval.before_more_than' => 'vor mehr als :interval :type',
    'text.date_interval.expired.after' => 'und abgelaufen in nächster :interval :type',
    'text.date_interval.expired.after_more_than' => 'und abgelaufen nach mehr als :interval :type',
    'text.date_interval.expired.before_more_than' => 'und abgelaufen vor mehr als :interval :type',
    'text.date_interval.expired.in_last' => 'und abgelaufen in letzter :interval :type',
    'text.date_interval.expired.never' => 'und abgelaufen ist unbestimmt',
    'text.date_interval.expired.sometime' => 'und abgelaufen manchmal',
    'text.date_interval.in_last' => 'in letzter :interval :type',
    'text.date_interval.never' => 'nie',
    'text.date_interval.sometime' => 'manchmal',
    'text.device_type.manufacturer' => 'und der Hersteller “:manufacturer” ist|und die Hersteller “:manufacturer” sind',
    'text.device_type.model' => 'und das Modell “:model” ist|und die Modelle “:model” sind',
    'text.device_type<>' => 'wo der Gerätetyp nicht “:type” ist|wo die Gerätetypen nicht “:type”:hersteller:modell sind',
    'text.device_type=' => 'wo der Gerätetyp “:type” ist|wo die Gerätetypen “:type”:hersteller:modell sind',
    'text.discount<>' => 'Rabatt ist nicht \\":discount\\"|Rabatte sind nicht \\":discount\\"',
    'text.discount=' => 'Rabatt ist \\":discount\\"|Rabatte sind \\":discount\\"',
    'text.first_name.<>' => 'ist nicht mit Vorname \\":value\\"',
    'text.first_name.=' => 'mit Vorname \\":value\\"',
    'text.from<>' => 'ist nicht abonniert von \\":from\\"',
    'text.from=' => 'ist abonniert von \\":from\\"',
    'text.from_form<>' => 'ist nicht abonniert von Formular \\":from\\"|ist nicht abonniert von Formularen \\":from\\"',
    'text.from_form=' => 'ist abonniert von Formular \\":from\\"|ist abonniert von Formularen \\":from\\"',
    'text.last<>' => 'ist nicht letzte Bestellung',
    'text.last=' => 'ist letzte Bestellung',
    'text.last_active.before_more_than' => 'zuletzt aktiv vor mehr als :interval :type',
    'text.last_active.in_last' => 'zuletzt aktiv in den letzten :interval :type',
    'text.last_name.<>' => 'ist nicht mit Nachname \\":value\\"',
    'text.last_name.=' => 'ist mit Nachname \\":value\\"',
    'text.missing_product.any' => 'abonniert die Verfügbarkeitsbenachrichtigung des Produkts|abonniert die Lagerbenachrichtigung von Produkten',
    'text.missing_product_empty.any' => 'abonniert die Verfügbarkeitsbenachrichtigung,|abonniert die Lagerbenachrichtigung',
    'text.missing_product_product.any' => 'abonniert die Verfügbarkeitsbenachrichtigung von|abonniert die Lagerbenachrichtigung von',
    'text.newest<>' => 'das nicht als neu markiert ist|die nicht als neu markiert sind',
    'text.newest=' => 'das als neu markiert ist|die als neu markiert sind',
    'text.open_rate<' => 'hat eine Öffnungsrate von weniger als :open_rate',
    'text.open_rate<>' => 'hat eine Öffnungsrate, die nicht gleich :open_rate ist',
    'text.open_rate=' => 'hat eine Öffnungsrate, die gleich :open_rate ist',
    'text.open_rate>' => 'hat eine Öffnungsrate von mehr als :open_rate',
    'text.order' => 'hat Bestellungen',
    'text.order.product.times<' => 'die weniger als :times Produkt haben|die weniger als :times Produkte haben',
    'text.order.product.times<>' => 'die nicht gleich :times Produkt haben|die nicht gleich :times Produkte haben',
    'text.order.product.times=' => 'die genau :times Produkt haben|die genau :times Produkte haben',
    'text.order.product.times>' => 'die mehr als :times Produkt haben|die mehr als :times Produkte haben',
    'text.order.times<' => 'weniger als :times Bestellung|weniger als :times Bestellungen',
    'text.order.times<>' => 'nicht gleich :times Bestellung|nicht gleich :times Bestellungen',
    'text.order.times=' => 'genau :times Bestellung|genau :times Bestellungen',
    'text.order.times>' => 'mehr als :times Bestellung|mehr als :times Bestellungen',
    'text.order_date_interval.before_more_than' => 'Bestelldatum vor mehr als :interval :type',
    'text.order_date_interval.in_last' => 'Bestelldatum in letzter :interval :type',
    'text.os<>' => 'wo das Betriebssystem nicht “:os” ist|wo die Betriebssysteme nicht “:os” sind',
    'text.os=' => 'wo das Betriebssystem “:os” ist|wo die Betriebssysteme “:os” sind',
    'text.page' => 'Landing-Page',
    'text.page<>' => 'Landing-Page ist nicht \\":page\\"|Landing-Pages sind nicht \\":page\\"',
    'text.page=' => 'Landing-Page \\":page\\"|Landing-Pages \\":page\\"',
    'text.payment<>' => 'Zahlungsmethode ist nicht “:payment”|Zahlungsmethoden sind nicht “:payment”',
    'text.payment=' => 'Zahlungsmethode ist “:payment”|Zahlungsmethoden sind “:payment”',
    'text.price.order<' => 'wo der Bestellbetrag weniger als :price ist',
    'text.price.order<>' => 'wo der Bestellbetrag nicht gleich :price ist',
    'text.price.order=' => 'wo der Bestellbetrag gleich :price ist',
    'text.price.order>' => 'wo der Bestellbetrag mehr als :price ist',
    'text.price<' => 'hat einen Preis von weniger als :price',
    'text.price<>' => 'hat einen Preis, der nicht gleich :price ist',
    'text.price=' => 'hat einen Preis, der gleich :price ist',
    'text.price>' => 'hat einen Preis von mehr als :price',
    'text.product' => 'Produkte',
    'text.product.value<>' => 'das ist nicht “:product”|die sind nicht “:product”',
    'text.product.value=' => 'das ist “:product”|die sind “:product”',
    'text.product.view<>' => 'wo das Produkt nicht “:product” ist|wo die Produkte nicht “:product” sind',
    'text.product.view=' => 'Produkt “:product”|Produkte “:product”',
    'text.product<>' => 'wo das Produkt nicht “:product” ist|wo die Produkte nicht “:product” sind',
    'text.product=' => 'wo das Produkt “:product” ist|wo die Produkte “:product” sind',
    'text.quantity<' => 'hat eine Menge von weniger als :quantity',
    'text.quantity<>' => 'hat eine Menge, die nicht gleich :quantity ist',
    'text.quantity=' => 'hat eine Menge, die gleich :quantity ist',
    'text.quantity>' => 'hat eine Menge von mehr als :quantity',
    'text.region<>' => 'und nicht aus einem Ort “:region” kommen|und nicht aus Orten “:region” kommen',
    'text.region=' => 'und Ort “:region”|und Orte “:region”',
    'text.registered.before_more_than' => 'registriert vor mehr als :interval :type',
    'text.registered.in_last' => 'registriert in letzter :interval :type',
    'text.rfm<>' => 'hat nicht RFM Rang “:rfm”|hat nicht RFM Rang’s “:rfm”',
    'text.rfm=' => 'hat RFM Rang “:rfm”|hat RFM Rang’s “:rfm”',
    'text.sale<>' => 'das nicht als Sale markiert ist|die nicht als Sale markiert sind',
    'text.sale=' => 'das als Sale markiert ist|die als Sale markiert sind',
    'text.shipping<>' => 'Versandanbieter ist nicht “:shipping”|Versandanbieter sind nicht “:shipping”',
    'text.shipping=' => 'Versandanbieter ist “:shipping”|Versandanbieter sind “:shipping”',
    'text.status<>' => 'Status ist nicht “:status”',
    'text.status=' => 'Status ist “:status”',
    'text.status_fulfillment<>' => 'ist nicht erfüllt',
    'text.status_fulfillment=' => 'ist erfüllt',
    'text.subscriber' => 'Abonnent',
    'text.tag<>' => 'ist nicht mit “:tag” getaggt',
    'text.tag=' => 'ist mit “:tag” getaggt',
    'text.times<' => 'weniger als :times Mal|weniger als :times Mal',
    'text.times<>' => 'nicht gleich :times Mal|nicht gleich :times Mal',
    'text.times=' => 'genau :times Mal|genau :times Mal',
    'text.times>' => 'mehr als :times Mal|mehr als :times Mal',
    'text.type<>' => 'ist nicht “:type”',
    'text.type=' => 'ist “:type”',
    'text.utm_campaign<>' => 'utm-Kampagne ist nicht \\":utm\\"',
    'text.utm_campaign=' => 'utm-Kampagne ist \\":utm\\"',
    'text.utm_medium<>' => 'utm-Medium ist nicht \\":utm\\"',
    'text.utm_medium=' => 'utm-Medium ist \\":utm\\"',
    'text.utm_source<>' => 'utm-Quelle ist nicht \\":utm\\"',
    'text.utm_source=' => 'utm-Quelle ist \\":utm\\"',
    'text.vendor' => 'Marke',
    'text.vendor.view<>' => 'Marke, die nicht “:vendor” ist|Marken, die nicht “:vendor” sind',
    'text.vendor.view=' => 'Marke “:vendor”|Marken “:vendor”',
    'text.vendor<>' => 'wo die Marke nicht “:vendor” ist|wo die Marken nicht “:vendor” sind',
    'text.vendor=' => 'wo die Marke “:vendor” ist|wo die Marken “:vendor” sind',
    'text.verified<>' => 'der nicht verifiziert ist|die nicht verifiziert wurden',
    'text.verified=' => 'der verifiziert ist|die verifiziert wurden',
    'text.view' => 'Ansicht',
    'text.wish_list' => 'Produkt zur Wunschliste hinzugefügt',
    'text.with_product' => 'mit Produkten',
    'text.without_order' => 'ohne Bestellungen',
    'text.without_order_date_interval.before_more_than' => 'vor mehr als :interval :type',
    'text.without_order_date_interval.in_last' => 'in letzter :interval :type',
];
