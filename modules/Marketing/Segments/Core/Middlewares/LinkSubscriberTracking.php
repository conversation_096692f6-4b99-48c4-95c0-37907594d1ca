<?php

declare(strict_types=1);

namespace Modules\Marketing\Segments\Core\Middlewares;

use App\Models\Queue\SiteQueue;
use App\Models\Store\Cart as CartModel;
use Auth;
use Carbon\Carbon;
use Closure;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use MaxMind;
use Modules\DatalayerEvents\Formatters\CustomerFormatter;
use Modules\Marketing\Segments\Core\SegmentsManager;
use Symfony\Component\HttpFoundation\Cookie;
use Throwable;

/**
 * @deprecated
 */
class LinkSubscriberTracking
{
    protected $encrypt;

    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $ccaResponse = $this->getCcUuid();
        if ($ccaResponse && $request->session() && $request->input('cc_subscriber.id')) {
            SegmentsManager::setSubscriberId($request->input('cc_subscriber.id'));

            $psId = null;
            if (!Auth::adminId()) {
                $psId = $request->cookie('fb_psid', session('fb_psid')) ?: session('fb_psid');
            }

            SiteQueue::executeQueueTaskNew([
                'job' => 'set_subscriber_uuid',
                'parameters' => [
                    'subscriber_id' => $request->input('cc_subscriber.id'),
                    'uuid' => $ccaResponse->uuid,
                    'psId' => $psId,
                    'user_agent' => $request->userAgent(),
                    'country' => MaxMind::getCountryIso2(),
                    'customer_id' => $request->input('cc_subscriber.cid', Auth::adminId() ? null : Auth::customerId()),
                    'marketing' => 1,
                    'referral' => $request->input('cc_subscriber.channel'),
                ],
            ]);

            $response = redirect()
                ->to(rtrim($request->fullUrlWithQuery(['cc_subscriber' => null]), '?'));

            $response->cookie(new Cookie('uuid', $ccaResponse->uuid, Carbon::now()->addDays(3650), '/', null, false, false));
            $response->cookie(new Cookie('_ccases', $ccaResponse->ccases, Carbon::now()->addDays(3650), '/', null, false, false));

            return $response;
        }

        $response = $next($request);

        if (!$this->shouldResponse($request, $response)) {
            return $response;
        }

        if ($ccaResponse !== null) {
            $response->cookie(new Cookie('uuid', $ccaResponse->uuid, Carbon::now()->addDays(3650), '/', null, false, false));
            $response->cookie(new Cookie('_ccases', $ccaResponse->ccases, Carbon::now()->addDays(3650), '/', null, false, false));
        }

        return $response;
    }

    /**
     * @param Request $request
     * @param Response $response
     * @return bool
     */
    protected function shouldResponse($request, $response): bool
    {
        return !$request->cookie('uuid') &&
            app_namespace() == 'site' &&
            $response instanceof Response &&
            $response->isSuccessful() &&
            !$response->isRedirection() &&
            !$request->ajax() &&
            $request->isMethod('get') &&
            config('uuid.enabled');
    }

    /**
     * @return array
     * @throws Throwable
     */
    protected function getCustomerData(): array
    {
        if (Auth::customerId()) {
            $customer = Auth::customer();
        } elseif (app_namespace() == 'site' && ($cartInstance = CartModel::instance()) && $cartInstance->getCustomerId()) {
            $customer = $cartInstance->customer;
        } else {
            $customer = Auth::guestUser();
        }

        return CustomerFormatter::format($customer);
    }

    /**
     * @return \stdClass
     */
    protected function getCcUuid(): ?object
    {
        if (!$this->shouldMakeRequest()) {
            return null;
        }

        try {
            $query = [
                'ccuid' => null,//CartModel::instance()->cc_user_id??null,
                'site' => [
                    'language' => site('language'),
                    'currency' => site('currency'),
                ],
                'customer' => $this->getCustomerData(),
                '___disableCallback___' => true,
            ];

            $options = [
                RequestOptions::QUERY => $query,
                RequestOptions::TIMEOUT => 3,
                RequestOptions::CONNECT_TIMEOUT => 3,
                RequestOptions::READ_TIMEOUT => 3,
                RequestOptions::HEADERS => [
                    'User-Agent' => request()->userAgent(),
                ],
            ];

            $http = new Client();
            $url = sprintf('https://%s/%s/init', config('url.domains.cc_analytics'), site('site_id'));
            $ccaResponse = $http->get($url, $options);
            $ccaResponse = json_decode($ccaResponse->getBody()->getContents());

            return is_object($ccaResponse) && !empty($ccaResponse->uuid) ? $ccaResponse : null;
        } catch (Throwable) {
            return null;
        }
    }

    /**
     * @return bool
     */
    protected function shouldMakeRequest(): bool
    {
        return !request()->cookie('uuid') &&
            app_namespace() == 'site' &&
            !request()->ajax() &&
            request()->isMethod('get') &&
            config('uuid.enabled');
    }
}
