<?php

declare(strict_types=1);

namespace Modules\Marketing\Segments\Conditions\Subscriber\Channel\Verified;

use Closure;
use Modules\Marketing\Segments\Core\Conditions\AbstractConditionManager;

class VerifiedConditionManager extends AbstractConditionManager
{
    /**
     * @inerhitDoc
     */
    #[\Override]
    public function makeSubscriberSqlCondition(): Closure
    {
        return $this->makeSubscriberSqlInCondition();
    }

    /**
     * @inerhitDoc
     */
    #[\Override]
    protected function parentConditionsList(): array
    {
        return [
            'marketing.segments.conditions.subscriber.channel',
        ];
    }

    /**
     * @inerhitDoc
     */
    #[\Override]
    public function validate(?string $attribute = null): array
    {
        $errors = parent::validate($attribute);
        if (empty($this->getValue())) {
            $errors[$this->errorFieldName('value', $attribute)][] = __('segments::condition.error.empty_record');
        }

        return $errors;
    }

    #[\Override]
    public function getTextValue()
    {
        return __('segments::segment.text.verified');
    }

    /**
     * @inerhitDoc
     */
    #[\Override]
    public function toText(): string
    {
        return trans_choice('segments::condition.text.verified' . $this->getOperator(), count($this->getParent()->getValue()))
            . $this->conditionsToText();
    }
}
