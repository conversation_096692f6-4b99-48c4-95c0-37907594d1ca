<?php

declare(strict_types=1);

return [
    'action.confirm.delete' => 'Ești sigur că vrei să ștergi acest terminal?',
    'add_pack_send_order' => 'Adaugă un nou interval pentru trimiterea comenzii',
    'button.create_slot' => 'Adaugă un nou interval (minute)',
    'create.button' => 'Creează un terminal nou',
    'create.title' => 'Terminal nou',
    'custom_status' => 'Comandă ambalată',
    'custom_status_unprepared' => 'Comandă despachetată',
    'custom_status_with_missing' => 'Comandă ambalată cu produse lipsă',
    'error.cannot_colleted_more' => 'Nu poți încadra mai multe articole pentru produs.',
    'error.cannot_missing_more' => 'Nu ai voie să marchezi mai multe bucăți ale produsului ca lipsă.',
    'error.confirm_product' => 'A apărut o eroare în timpul confirmării produsului',
    'error.order_not_found' => 'Comanda nu există',
    'error.pack_order' => 'A apărut o eroare în timpul ambalării comenzii',
    'error.product_not_found' => 'Produsul nu există',
    'error.unpack_order' => 'A apărut o eroare în timpul despachetării comenzii',
    'form.locations' => 'Locații',
    'form.name' => 'Nume terminal',
    'form.user_access' => 'Utilizatori',
    'generate_pdf' => 'Generează formular pentru tipărire',
    'header.install' => 'Instalează Alege și Ambalează',
    'help.install' => ' ',
    'history.add_comment_%1$s' => 'A fost adăugat un comentariu: %1$s',
    'history.collect_%1$s_to_%2$s' => 'Pentru produsul %1$s au fost colectate %2$s buc.',
    'history.missing_%1$s_to_%2$s' => 'Pentru produsul %1$s au fost marcate %2$s buc. ca lipsă',
    'history.order_cancel' => 'Comanda a fost anulată',
    'history.packed' => 'Comanda a fost ambalată',
    'history.unpacked' => 'Comanda a fost despachetată',
    'info.description' => ' ',
    'info.title' => 'Alege și Ambalează',
    'label.sound' => 'Semnal sonor constant la comandă nouă',
    'label.sound_accept' => 'Butonul înțeleg pentru oprirea sunetului la comandă nouă',
    'label.sound_accept.help' => 'Dacă este activat, vei confirma comenzile noi ca vizualizate prin buton, această acțiune va opri sunetul. Cu opțiunea dezactivată, vei avea un bip până când lista completă de comenzi este ambalată sau expediată.',
    'list.is_active' => 'Activ',
    'list.name' => 'Nume terminal',
    'list.url' => 'Adresa terminalului',
    'list.users' => 'Alege utilizatori',
    'list.users.help' => 'Alege utilizatorii cu acces la terminal',
    'list.zone' => 'Zone geografice',
    'list.zone.help' => 'Selectează zonele deservite de terminal',
    'list.zone.warning' => 'Nu ai creat nicio zonă!',
    'list.zone.warning.add' => 'Adaugă zonă nouă',
    'msg.update_status' => 'Starea terminalului a fost actualizată cu succes',
    'name.required' => 'Nu ai introdus un nume pentru terminal',
    'order_details' => 'Detalii ale comenzii',
    'order_status.cancelled' => 'Anulat',
    'order_status.completed' => 'Completat',
    'order_status.fulfilled' => 'Finalizat',
    'order_status.not_fulfilled' => 'Nefinalizat',
    'order_status.paid' => 'Plătit',
    'order_status.pending' => 'În așteptare',
    'order_status.refunded' => 'Rambursat',
    'order_status.required' => 'Nu ai selectat stările pentru comanda care va fi vizualizată în terminal',
    'order_status.select' => 'Alege stările comenzilor care vor fi vizualizate în terminal',
    'order_status.select.help' => 'Doar comenzile cu stările selectate vor fi vizualizate în terminal',
    'order_time' => 'Alege câte zile în urmă să afișezi comenzile / produsele',
    'order_time.help' => 'Dacă câmpul este gol, toate comenzile / produsele vor fi afișate conform celorlalte setări',
    'select_sort_by' => 'Sortează după',
    'select_sort_by.help' => 'Alege după ce să sortezi comenzile',
    'select_sort_by.id' => 'Data creării',
    'select_sort_by.shipping_from' => 'Data estimată a livrării',
    'select_sort_type' => 'Tipul de sortare',
    'select_sort_type.asc' => 'Ascendent',
    'select_sort_type.desc' => 'Descendent',
    'select_sort_type.help' => 'Alege un tip de sortare',
    'select_status_pack' => 'Stare pentru comanda ambalată',
    'select_status_pack.help' => 'Selectează o stare care va fi setată pentru comanda după ce a fost ambalată',
    'select_status_pack_with_missing' => 'Stare pentru ambalarea unei comenzi cu produse lipsă',
    'select_status_pack_with_missing.help' => 'Alege o stare care va fi setată când se ambalează o comandă care conține produse lipsă',
    'select_status_unpack' => 'Stare pentru despachetarea unei comenzi',
    'select_status_unpack.help' => 'Selectează o stare care va fi setată când comanda este despachetată',
    'send_minutes.required_if' => 'Nu ai introdus minutele pentru trimiterea unei comenzi',
    'show_cancel' => 'Afișează butonul pentru anularea comenzii',
    'show_count_package' => 'Permite introducerea numărului de pachete',
    'show_pick' => 'Permite ambalarea',
    'show_price' => 'Afișează prețul',
    'show_send_order' => 'Permite trimiterea comenzii',
    'success.cancel_order' => 'Ai anulat comanda cu succes',
    'success.collected' => 'Produsul a fost încărcat cu succes',
    'success.create' => 'Terminalul a fost creat cu succes',
    'success.missing' => 'Produsul a fost marcat cu succes ca lipsă',
    'success.pack_order' => 'Ai ambalat comanda cu succes',
    'success.send_comment' => 'Ai adăugat un comentariu la comandă cu succes',
    'success.unpack_order' => 'Ai despachetat comanda cu succes',
    'success.update' => 'Terminalul a fost actualizat cu succes',
    'type' => 'Tipul terminalului',
    'type.pack' => 'Grupat după comenzi - Ambalează',
    'type.pick_pack' => 'Grupat după comenzi - Alege & Ambalează',
    'type.products' => 'Grupat după produse',
    'type.required' => 'Nu ai selectat un tip pentru terminal',
    'user_access.required' => 'Nu ai selectat utilizatorii care au acces la terminal',
];
