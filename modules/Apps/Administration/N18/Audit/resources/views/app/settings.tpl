{include file="apps/dependency/settings.tpl"}
{include file="apps/includes/app-breadcrumb.tpl" active=__('n18_audit::app.title')}

<div class="wrapper">
    <div class="app-wrapper">
        {include file="apps/includes/app-intro.tpl" title=__('n18_audit::app.title') text=__('n18_audit::app.help.install')}
        {include file="apps/includes/app-icons.tpl"  app_icon='icon-n18-audit.png'}
    </div>

    <form action="{route('apps.n18_audit')}" id="editForm" role="form" data-redirect="{route('apps.n18_audit.list')}">
        <div class="container">
            <div class="box">
                <div class="box-section">
                    <div class="row">
                        <div class="col-md-12">

                            <div class="form-group">
                                <label class="control-label">{t}n18_audit::app.form.label.uin{/t}</label>
                                <i class="glyphicon glyphicon-info-sign tooltips"
                                   title="{t}n18_audit::app.form.help.uin{/t}"
                                   data-placement="top"></i>

                                <input name="uin" id="uin" type="text"
                                       value="{$settings.uin|default}" class="form-control" data-autofocus
                                       placeholder="{t}n18_audit::app.form.ph.uin{/t}"/>
                            </div>

                            <div class="form-group">
                                <label class="control-label">{t}n18_audit::app.form.label.domain{/t}</label>
                                <input name="domain" id="domain" type="text"
                                       value="{$domain}" class="form-control" data-autofocus
                                       placeholder="https://example.com"/>
                            </div>

                            <div class="form-group">
                                <label class="control-label">{t}n18_audit::app.form.label.return_pay_type{/t}</label>
                                <i class="glyphicon glyphicon-info-sign tooltips"
                                   title="{t}n18_audit::app.form.help.return_pay_type{/t}"
                                   data-placement="top"></i>

                                <select name="return_pay_type" class="form-control select2me" data-no-input="true">
                                    {for $return_pay_type=1 to 4}
                                        <option value="{$return_pay_type}"
                                                {if $settings.return_pay_type|default == $return_pay_type}selected="selected"{/if}>{t}n18_audit::app.form.text.return_pay_type.{$return_pay_type}{/t}</option>
                                    {/for}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {if $payments_two && $payments_two->isNotEmpty()}
                <div class="box">
                    <div class="box-section">
                        <div class="row">
                            <div class="col-md-12">

                                <div class="row form-group">
                                    <div class="col-xs-12">
                                        <div class="form-control-box">
                                            <div class="form-control-box-inner">
                                                <label class="control-label">Terminal ID</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {foreach $payments_two as $paymant_key => $payments_name}
                                    <div class="form-group">
                                        <label class="control-label">{$payments_name}</label>
                                        {if $terminalId->get($paymant_key)}
                                            <input name="payment[{$paymant_key}]" id="payment-{$paymant_key}"
                                                   type="text"
                                                   value="{$terminalId->get($paymant_key)}" class="form-control"
                                                   readonly data-autofocus/>
                                        {else}
                                            <input name="payment[{$paymant_key}]" id="payment-{$paymant_key}"
                                                   type="text"
                                                   value="{$settings.payment.{$paymant_key}|default}"
                                                   class="form-control" data-autofocus/>
                                        {/if}
                                    </div>
                                {/foreach}

                            </div>
                        </div>
                    </div>
                </div>
            {/if}

            {if $payments_four && $payments_four->isNotEmpty()}
                <div class="box">
                    <div class="box-section">
                        <div class="row">
                            <div class="col-md-12">

                                <div class="row form-group">
                                    <div class="col-xs-12">
                                        <div class="form-control-box">
                                            <div class="form-control-box-inner">
                                                <label class="control-label">{t}n18_audit::app.form.head.payments{/t}</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {foreach $payments_four as $paymant_key => $payments_name}
                                    <div class="form-group">
                                        <label class="control-label">{$payments_name}</label>

                                        <input name="payment_four[{$paymant_key}]" id="payment-{$paymant_key}"
                                               type="text"
                                               value="{$settings.payment_four.{$paymant_key}|default}"
                                               class="form-control" data-autofocus/>
                                    </div>
                                {/foreach}

                            </div>
                        </div>
                    </div>
                </div>
            {/if}


            <div class="row">
                <div class="col-md-12">

                    <div class="form-group">
                        <div class="row">
                            <div class="col-xs-12 text-right">
                                <button class="btn btn-primary" type="submit"
                                        id="editSubmit">{t}global.save{/t}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>