<?php

declare(strict_types=1);

namespace Modules\Apps\Administration\Multylang\Helpers;

use App\Exceptions\Error;
use App\Models\Apps\Applications;
use App\Models\Apps\ApplicationTokens;
use App\Models\Gate\Discount;
use App\Models\Gate\PlanFeaturePack;
use App\Models\Gate\SiteSubscription;
use Google\Cloud\Translate\V3\Client\TranslationServiceClient;
use Google\Cloud\Translate\V3\TranslateTextRequest;
use Modules\Apps\Administration\Multylang\MultylangManager;
use Modules\Apps\Administration\Multylang\Models\Translate;
use Illuminate\Support\Fluent;

class Support
{
    public const TRANSLATE_PRICE = 'machine_translation';

    public const COPY_PRICE = 'copy_info';

    public const DISCOUNT_CODE_PLAN = 'L2FPYK6L';

    public const DISCOUNT_CODE_APPS = 'L2FPZJ5I';

    public const FREE_FOR = [18305, 1846];

    // load manager
    public static function manager(): \Modules\Apps\Administration\Multylang\MultylangManager
    {
        return new MultylangManager();
    }

    /**
     * @param mixed $step
     * @return array
     */
    public static function forCopy($step)
    {
        if ($step == 2) {
            $counts = self::countSymbols(self::manager()->getSetting('main_site'), 2);
            return [
                'design' => [
                    'copy' => [
                        'price' => self::format_price('copy'),
                        'required' => false
                    ],
                ],
                'menu' => [
                    'copy' => [
                        'price' => self::format_price('copy'),
                        'required' => false
                    ],
                    'translate' => self::calculate_translate($counts['menu']),
                    'symbols' => $counts['menu'],
                ],
                'blog' => [
                    'copy' => [
                        'price' => self::format_price('copy'),
                        'required' => false
                    ],
                    'translate' => self::calculate_translate($counts['blog']),
                    'symbols' => $counts['blog'],
                ],
//                'client' => [
//                    'copy' => 0,
//                ],
                'pages' => [
                    'copy' => [
                        'price' => self::format_price('copy'),
                        'required' => false
                    ],
                    'translate' => self::calculate_translate($counts['pages']),
                    'symbols' => $counts['pages'],
                ],
                'product' => [
                    'copy' => [
                        'price' => self::format_price('copy'),
                        'required' => false
                    ],
                ],
            ];
        } elseif ($step == 3) {
            $counts = self::countSymbols(self::manager()->getSetting('main_site'), 3);
            return [
                'name' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'translate' => self::calculate_translate($counts['product']['name']),
                    'symbols' => $counts['product']['name'],

                ],
                'meta_title' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'translate' => self::calculate_translate($counts['product']['meta_title']),
                    'symbols' => $counts['product']['meta_title'],
                ],
                'description' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'translate' => self::calculate_translate($counts['product']['description']),
                    'symbols' => $counts['product']['description'],
                ],
                'short_description' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'translate' => self::calculate_translate($counts['product']['short_description']),
                    'symbols' => $counts['product']['short_description'],
                ],
                'meta_description' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'translate' => self::calculate_translate($counts['product']['meta_description']),
                    'symbols' => $counts['product']['meta_description'],
                ],
                'category' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'translate' => self::calculate_translate($counts['product']['categories']),
                    'symbols' => $counts['product']['categories'],
                ],
                'properties' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'translate' => self::calculate_translate($counts['product']['properties']),
                    'symbols' => $counts['product']['properties'],
                ],
                'vendor' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                ],
                'variety' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'translate' => self::calculate_translate($counts['product']['variety']),
                    'symbols' => $counts['product']['variety'],
                ],
                'tabs' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'translate' => self::calculate_translate($counts['product']['tabs']),
                    'symbols' => $counts['product']['tabs'],
                ],
                'tags' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'translate' => self::calculate_translate($counts['product']['tags']),
                    'symbols' => $counts['product']['tags'],
                ],
                'images' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                ],
                'images_alt' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'translate' => self::calculate_translate($counts['product']['images']),
                    'symbols' => $counts['product']['images'],
                ],
                'price' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                    'conversion' => 0,
                    'rounded' => 0,
                ],
                'url_manipulation' => [
                    'copy' => [
                        'price' => 0,
                        'required' => true
                    ],
                ],
            ];
        } else {
            return;
        }
    }

    /**
     * @param $site_id
     * @param $step
     * @return mixed
     */
    public static function countSymbols($site_id, $step)
    {
        return switchSiteCallback(function () use ($step): array {
            if ($step == 2) {
                $data['menu'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(CHAR_LENGTH(name)) as lenght FROM navigations")[0]->lenght;
                $data['blog'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(description_length) as lenght FROM (SELECT SUM(CHAR_LENGTH( name )) + SUM( CHAR_LENGTH( seo_title )) + SUM( CHAR_LENGTH( seo_description )) AS description_length FROM blogs UNION
	        SELECT SUM( CHAR_LENGTH( name )) + SUM( CHAR_LENGTH( seo_title )) + SUM(CHAR_LENGTH (seo_description)) + SUM(CHAR_LENGTH(content)) AS lenght FROM blogs_articles) AS tmp")[0]->lenght;
                $data['pages'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(CHAR_LENGTH(name)) + SUM(CHAR_LENGTH(content)) + SUM(CHAR_LENGTH(seo_title)) + SUM(CHAR_LENGTH(seo_description)) as lenght FROM pages WHERE type='regular'")[0]->lenght;
            }

            if ($step == 3) {
                $data['product']['name'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(CHAR_LENGTH(name)) as lenght FROM products")[0]->lenght;
                $data['product']['meta_title'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(CHAR_LENGTH(seo_title)) as lenght FROM products")[0]->lenght;
                $data['product']['description'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(CHAR_LENGTH(description)) as lenght FROM products")[0]->lenght;
                $data['product']['short_description'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(CHAR_LENGTH(short_description)) as lenght FROM products")[0]->lenght;
                $data['product']['meta_description'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(CHAR_LENGTH(seo_description)) as lenght FROM products")[0]->lenght;
                $data['product']['categories'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(categories_lenght) as lenght FROM (SELECT SUM(CHAR_LENGTH( name )) + SUM( CHAR_LENGTH( seo_title )) + SUM( CHAR_LENGTH( seo_description )) + SUM( CHAR_LENGTH( description )) AS categories_lenght FROM type__products_categories) AS tmp")[0]->lenght;
                $data['product']['variety'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(text_lenght) as lenght FROM (SELECT SUM(CHAR_LENGTH( name )) + SUM( CHAR_LENGTH( description )) AS text_lenght FROM products_parameters UNION
	        SELECT SUM( CHAR_LENGTH( name )) AS lenght FROM products_parameters_options) AS tmp")[0]->lenght;
                $data['product']['images'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(CHAR_LENGTH(name)) as lenght FROM images__products")[0]->lenght;
                $data['product']['tabs'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(CHAR_LENGTH(name)) + SUM(CHAR_LENGTH(description)) as lenght FROM products_tabs")[0]->lenght;
                $data['product']['tags'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(CHAR_LENGTH(tag)) as lenght FROM tags__products_tags")[0]->lenght;
                $data['product']['properties'] = \Illuminate\Support\Facades\DB::select("SELECT SUM(text_lenght) as lenght FROM (SELECT SUM(CHAR_LENGTH( value )) + SUM( CHAR_LENGTH( COALESCE(description, '') )) + SUM( CHAR_LENGTH( COALESCE(seo_title, '') )) + SUM( CHAR_LENGTH( COALESCE(seo_description, '') )) AS text_lenght FROM properties_options UNION
SELECT SUM( CHAR_LENGTH( name )) AS lenght FROM properties) AS tmp")[0]->lenght;
            }

            if ($step == 4) {
                $data['products'] = \Illuminate\Support\Facades\DB::select("SELECT COUNT(id) as count FROM products")[0]->count;
                $data['pages'] = \Illuminate\Support\Facades\DB::select("SELECT COUNT(id) as count FROM pages")[0]->count;
                $data['blog'] = \Illuminate\Support\Facades\DB::select("SELECT COUNT(id) as count FROM blogs_articles")[0]->count;
            }

            return $data;
        }, $site_id);
    }

    /**
     * @param $symbols
     * @return int|string
     */
    public static function calculate_translate($symbols): string|int
    {
        $getFeature = PlanFeaturePack::findByMap([static::TRANSLATE_PRICE]);
        $single_price = ($getFeature->price / 100) / $getFeature->value;
        if ($symbols > 0) {
            $price = $symbols * $single_price;
            return number_format(max($price, 1), 2, '.', '');
        }

        return 0;
    }

    /**
     * @param $type
     * @return string|void
     */
    public static function format_price($type)
    {
        switch ($type) {
            case 'translate':
                $getPrice = PlanFeaturePack::findByMap(static::TRANSLATE_PRICE)->price / 100;
                return number_format($getPrice, 2, '.', '');
            case 'copy':
                $getPrice = PlanFeaturePack::findByMap(static::COPY_PRICE)->price / 100;
                return number_format($getPrice, 2, '.', '');
        }
    }

    /**
     * @param $type - app or plan
     * @return int
     */
    public static function getDiscount($type): int|float
    {
        if ($type == 'app') {
            $discount = Discount::code(self::DISCOUNT_CODE_APPS)->active()->first();
            if (!empty($discount)) {
                return $discount->value / 100;
            }
        }

        if ($type == 'plan') {
            $discount = Discount::code(self::DISCOUNT_CODE_PLAN)->active()->first();
            if (!empty($discount)) {
                return $discount->value / 100;
            }
        }

        return 0;
    }


    /**
     * @param $settings
     * @param mixed $for_save
     * @return array
     * @throws Error
     */
    public static function countCopyAndTranslate($settings = null, $for_save = false): array
    {
        if (empty($settings)) {
            $settings = self::manager()->getSettings();
        }

        $count_copy = 0;
        $count_translate = 0;
        // count elements in step 2
        if (!empty($settings['copy'])) {
            foreach ($settings['copy'] as $key => $value) {
                if (!empty($value)) {
                    if (!empty($value['copy'])) {
                        $count_copy++;
                    }

                    if (!empty($value['translate']) && is_numeric($value['symbols'])) {
                        $count_translate += $value['symbols'];
                    }
                }
            }
        }

        // count elements in step 3
        if (!empty($settings['copy_product'])) {
            foreach ($settings['copy_product'] as $key => $value) {
                if (!empty($value)) {
                    if (!empty($value['translate']) && !empty($value['symbols'])) {
                        if (is_numeric($value['symbols'])) {
                            $count_translate += $value['symbols'];
                        }
                    }
                }
            }
        }

        return [
            'copy' => $count_copy,
            'translate' => $count_translate,
            'products' => (int)self::countProductsInMainSite(self::manager()->getSetting('main_site'))
        ];
    }

    /**
     * @param mixed $site_id
     * @return mixed
     */
    public static function countProductsInMainSite($site_id)
    {
        return switchSiteCallback(fn() => \Illuminate\Support\Facades\DB::select("SELECT COUNT(id) as count FROM products")[0]->count, $site_id);
    }

    /**
     * @return int
     * @throws Error
     */
    public static function calculateCopy(): int|float
    {
        $count_copy = 0;
        $settings = self::manager()->getSettings();
        if (!empty($settings->get('copy'))) {
            foreach ($settings->get('copy') as $key => $value) {
                if (!empty($value)) {
                    if (!empty($value['copy'])) {
                        $count_copy++;
                    }
                }
            }
        }

        if ($count_copy > 0) {
            return $count_copy * self::format_price('copy');
        }

        return 0;
    }

    public static function calculateTranslate(): string
    {
        $settings = self::manager()->getSettings();
        $getFeature = PlanFeaturePack::findByMap([self::TRANSLATE_PRICE]);
        $sum = 0;
        $product_sum = 0;
        if (!empty($settings->get('copy'))) {
            foreach ($settings->get('copy') as $key => $value) {
                if (!empty($value)) {
                    if (!empty($value['translate'])) {
                        $single_price = ($getFeature->price / 100) / $getFeature->value;
                        if (!empty($value['symbols'])) {
                            $price = $value['symbols'] * $single_price;
                            $sum += number_format(max($price, 1), 2, '.', '');
                        }
                    }
                }
            }

            if (!empty($settings->get('copy_product'))) {
                foreach ($settings->get('copy_product') as $key => $value) {
                    if (!empty($value)) {
                        if (!empty($value['translate'])) {
                            $single_price = ($getFeature->price / 100) / $getFeature->value;
                            if (!empty($value['symbols'])) {
                                $price = $value['symbols'] * $single_price;
                                $product_sum += number_format(max($price, 1), 2, '.', '');
                            }
                        }
                    }
                }
            }
        }

        return number_format($sum + $product_sum, 2, '.', '');
    }

    /**
     * @return array
     * @throws Error
     */
    public static function SubTotal(): array
    {
        $translate = self::calculateTranslate();
        $copy = self::calculateCopy();
        return [
            'price' => (int)(($translate + $copy) * 100),
            'price_formatted' => number_format($translate + $copy, 2, '.', ''),
        ];
    }

    /**
     * @param mixed $step
     * @return mixed
     */
    public static function defaultSetting($step)
    {
        if ($step == 2) {
            return [
                "design" => [
                    "copy" => 0
                ],
                "menu" => [
                    "copy" => 0,
                    "translate" => 0,
                ],
                "blog" => [
                    "copy" => 0,
                    "translate" => 0,
                ],
                "pages" => [
                    "copy" => 0,
                    "translate" => 0,
                ],
                "product" => [
                    "copy" => 0
                ]
            ];
        } elseif ($step == 3) {
            return [
                "name" => [
                    "copy" => 1,
                    "translate" => 0
                ],
                "meta_title" => [
                    "copy" => 1,
                    "translate" => 0,
                ],
                "description" => [
                    "copy" => 1,
                    "translate" => 0,
                ],
                "short_description" => [
                    "copy" => 1,
                    "translate" => 0,
                ],
                "meta_description" => [
                    "copy" => 1,
                    "translate" => 0,
                ],
                "category" => [
                    "copy" => 1,
                    "translate" => 0,
                ],
                "properties" => [
                    "copy" => 1,
                    "translate" => 0,
                ],
                "vendor" => [
                    "copy" => 1
                ],
                "variety" => [
                    "copy" => 1,
                    "translate" => 0,
                ],
                "tabs" => [
                    "copy" => 1,
                    "translate" => 0,
                ],
                "tags" => [
                    "copy" => 1,
                    "translate" => 0,
                ],
                "images" => [
                    "copy" => 1
                ],
                "images_alt" => [
                    "copy" => 1,
                    "translate" => 0,
                ],
                "price" => [
                    "copy" => 1,
                    "change" => null,
                    "round" => null
                ]
            ];

        }
    }

    /**
     * @param MultylangManager $manager
     * @return array
     * @throws Error
     */
    public static function paymentCheck(MultylangManager $manager): array
    {
        $data = [];
        $settings = $manager->getSettings();
        $site_subscriptions = SiteSubscription::where(['site_id' => site()->site_id, 'status' => SiteSubscription::ACTIVE])->get();
        if (!empty($app = $settings->get('app'))) {
            $getPaidApps = Applications::whereIn('id', $settings->get('app'))->where('price', '>', 0)->pluck('id')->all();
            $getApps = $site_subscriptions->map(function ($item) use ($app) {
                if ($item->model_type == 'cloudcart_app') {
                    return $item->model_id;
                }
            })->filter()->toArray();
            $app_diff = array_diff($getPaidApps, $getApps);
            if (!empty($app_diff)) {
                $data['app'] = $app_diff;
            }
        }

        $getPlan = $site_subscriptions->map(function ($item) {
            if ($item->model_type == 'plan_details') {
                return $item->model_id;
            }
        })->filter();

        // ?? ?????? ???????? ?? ?????
        //        if (!empty($settings['plan_id']) && ($getPlan->isEmpty() || $getPlan->first() != $settings['plan_id'])) {
        //            $data['plan'] = $settings['plan_id'];
        //        }

        // check features
        $counter = Support::countCopyAndTranslate($settings);
        $featured = [];
        if (!empty($counter['copy'])) {
            array_push($featured, Support::COPY_PRICE);
        }

        if (!empty($counter['translate'])) {
            array_push($featured, Support::TRANSLATE_PRICE);
        }

        if (!empty($featured)) {
            $getFeatures = $site_subscriptions->map(function ($item) {
                if ($item->model_type == 'cloudcart_feature') {
                    return trim((string)$item->mapping);
                }
            })->filter()->toArray();

            $featured_diff = array_diff($featured, $getFeatures);
            if (!empty($featured_diff)) {
                $data['feature'] = $featured_diff;
            }
        }

        return $data;
    }

    /**
     * @param MultylangManager $manager
     * @return array
     * @throws Error
     */
    public static function progressElements(MultylangManager $manager): array
    {
        $settings = $manager->getSettings();
        $elements = [];
        if (!empty($settings->get('plan_id'))) {
            $elements[] = 'selected_plan';
        }

        if (!empty($settings->get('copy_product'))) {
            $elements[] = 'copy_product';
            if (!empty($settings->get('copy_product')['price']) &&
                (is_numeric($settings->get('copy_product')['price']['change']) || is_numeric($settings->get('copy_product')['price']['round']))
            ) {
                $elements[] = 'update_products_price';
            }
        }

        if (!empty($settings->get('copy'))) {
            foreach ($settings->get('copy') as $key => $value) {
                if (!empty($value)) {
                    if (!empty($value['copy'])) {
                        $elements[] = 'copy_' . $key;
                    }
                }
            }
        }

        if (!empty($settings->get('app'))) {
            $elements[] = 'install_app';
        }

        $elements[] = 'copy_storage';
        if (Support::countCopyAndTranslate($settings)['translate'] > 0) {
            $elements[] = Support::TRANSLATE_PRICE;
        }

        return array_unique($elements);
    }

    /**
     * @param $this_lang
     * @param $new_lang
     * @param array $data
     * @return array
     * @throws \Google\Cloud\Core\Exception\GoogleException
     * @throws \Google\Cloud\Core\Exception\ServiceException
     */
    public static function translate($this_lang, $new_lang, array $data): array
    {
        try {
            $manager = self::manager();
            $jsonKeyFilePath = __DIR__ . '/../cloudcart-144219-e8bbe17b455a.json';
            $translationClient = new TranslationServiceClient([
                'credentials' => $jsonKeyFilePath,
            ]);

            $formattedParent = $translationClient->locationName($manager::GOOGLE_PROJECT_ID, 'global');

            $translateText = new TranslateTextRequest();
            $translateText->setTargetLanguageCode($this_lang);
            $translateText->setTargetLanguageCode($new_lang);
            $translateText->setContents($data);
            $translateText->setParent($formattedParent);
            $response = $translationClient->translateText($translateText);

            $return = [];
            foreach ($response->getTranslations() as $key => $translated) {
                $return[] = [
                    'input' => $data[$key],
                    'text' => $translated->getTranslatedText(),
                ];
            }

            $translationClient->close();
            return $return;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * @param $table
     * @param array $keys
     * @param $siteId
     * @return mixed
     */
    public static function isTranslated($table, array $keys, $siteId)
    {
        return switchSiteCallback(fn() => Translate::where('table', $table)->whereIn('old_string', array_values($keys))->pluck('old_string', 'new_string')->all(), $siteId);
    }

    /**
     * @param array $data
     * @param $siteId
     * @return mixed
     */
    public static function populateTranslate(array $data, $siteId)
    {
        return switchSiteCallback(function () use ($data): void {
            Translate::insert($data);
        }, $siteId);
    }

    /**
     * @param MultylangManager $manager
     * @param $symbols
     * @return void
     * @throws Error
     */
    public static function createAppLog(MultylangManager $manager, $symbols): void
    {
        $getFeature = PlanFeaturePack::findByMap([static::TRANSLATE_PRICE]);
        $singlePriceCC = ($getFeature->price / 100) / $getFeature->value;
        $singlePriceGoogle = 20 / 1000000;
        if ($symbols > 0) {
            $ccPrice = ($symbols * $singlePriceCC) * 100;
            $googlePrice = ($symbols * $singlePriceGoogle) * 100;
            ApplicationTokens::create([
                'record_id' => $manager->getApp()->id,
                'record_type' => $manager->getApp()::class,
                'cc_tokens' => $symbols,
                'cc_price' => $ccPrice,
                'original_tokens' => $symbols,
                'original_price' => $googlePrice,
                'mapping' => $manager::APP_KEY,
            ]);
        }
    }
}
