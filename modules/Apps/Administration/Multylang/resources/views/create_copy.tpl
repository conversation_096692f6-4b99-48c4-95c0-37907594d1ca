{include file="./dependency.tpl"}
<div class="page-breadcrumb clearfix">
    {$breadcrumb = [
    [
    'title' => "{t}sidebar.apps{/t}",
    'href' => "{route('admin.apps')}"
    ],
    [
    'title' => "{t}multilang::app.info.title{/t}",
    'href' => "{route('apps.multilang')}"
    ],
    [
    'title' => "{t}multilang::app.info.step_{{$step}}{/t}"
    ]
    ]}

    {include file="includes/breadcrumb.tpl"}
</div>
<div class="wrapper">
    <div class="wrapper1">
        <div class="app-wrapper">
            {include file="{Apps::templatesPath()}includes/app-intro.tpl" title="{t}multilang::app.info.step_{{$step}}.title{/t} {$main_domain}" text=""}
            {include file="{Apps::templatesPath()}includes/app-addition.tpl" addition=$smarty.capture.app_addition}
        </div>
    </div>


    <div class="container-small">
        <div class="wrapper1321321">
            <div class="app-wrapper">
                {*                <p>{t}multilang::app.step.{$step}.title.progress{/t}</p>*}
                <div class="progress">
                    {$percent =$step*16.66}
                    <div class="progress-bar" style="width: {$percent}%;">
                    </div>
                </div>
            </div>
        </div>
        <form id="settingsForm" class="ajaxForm" role="form" action="{$submit_url}" method="POST">
            <div class="box">
                {*                <div class="box-title">*}
                {*                    <div class="box-title-text">*}
                {*                        <h5>{t}multilang::app.step.{$step}.title{/t}</h5>*}
                {*                        <p>{t}multilang::app.step.{$step}.title.info{/t}</p>*}
                {*                    </div>*}
                {*                </div>*}
                {foreach $for_copy as $elements => $element}
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-12">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="form-control-check">
                                            <b>
                                                {t}multilang::app.step.copy.{$elements}{/t}
                                            </b>
                                            {if $step==2}
                                                <br>
                                                <br>
                                                <span>{sprintf(__(sprintf('multilang::app.step.description.%s', $elements)), $main_domain, $this_site) nofilter}</span>
                                            {/if}
                                        </label>
                                    </div>
                                    {if $step!=3}
                                        <div class="stack-addon">
                                            <input name="copy[{$elements}]" type="checkbox" data-target="{$elements}"
                                                   class="switch calculate  {if !empty($element['copy']) && !empty($element['copy']['required'])} disable_class{/if}"
                                                   value="1"
                                                    {if !empty($settings[$elements]) && !empty($settings[$elements]['copy'])} checked{/if}
                                                    {if !empty($element['copy']) && !empty($element['copy']['required'])} checked readonly{/if}

                                            />
                                        </div>
                                    {/if}
                                </div>
                            </div>
                        </div>
                        <div id="{$elements}"
                                {if !empty($settings[$elements]) && !empty($settings[$elements]['copy'])}
                                {elseif empty($element['copy']) || empty($element['copy']['required'])}class="hidden"{/if}
                        >
                            {foreach $element as $name => $options}
                                {if $name == 'symbols'}
                                    <input name="copy[{$elements}][{$name}]" type="hidden" value="{{$options}}">
                                    {continue}
                                {/if}
                                {if $name == 'conversion'}
                                    <div class="">
                                        <div class="row form-group margin-bottom-10">
                                            <div class="col-xs-9">
                                                <label class="control-label"> {t}multilang::app.step.copy.change.price{/t}</label>
                                            </div>
                                            <div class="col-xs-3">
                                                <input type="number" min="0.00" step="0.01"
                                                        {if !empty($settings[{$elements}]['change'])} value="{$settings[{$elements}]['change']}" {/if}
                                                       name="copy[{$elements}][change]" class="form-control">
                                            </div>
                                        </div>
                                    </div>
                                {elseif $name == 'rounded'}
                                    <div class="">
                                        <div class="row form-group margin-bottom-10">
                                            <div class="col-xs-9">
                                                <label class="control-label"> {t}multilang::app.step.copy.round.price{/t}
                                                    {*                                                    <i class="glyphicon glyphicon-info-sign tooltips" data-placement="top" title="{t}multilang::app.step.copy.round.price.tooltip{/t}"></i>*}
                                                    <br>
                                                    <small class="cc-grey">
                                                        {t}multilang::app.step.copy.round.price.tooltip{/t}
                                                    </small>
                                                </label>
                                            </div>
                                            <div class="col-xs-3">
                                                <div class="input-group">
                                                    <span class="input-group-addon" id="basic-addon1">xx.</span>
                                                    <input type="number" min="00" step="01" max="99" maxlength="2"
                                                           placeholder="00"
                                                           name="copy[{$elements}][round]" {if !empty($settings[{$elements}]['round'])} value="{$settings[{$elements}]['round']}" {else} value="00" {/if}
                                                           class="form-control" aria-describedby="basic-addon1"
                                                           oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);"
                                                           onchange="leadingZeros(this)"
                                                           onclick="leadingZeros(this)"
                                                           onblur="leadingZeros(this)"
                                                    >
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {else}
                                    <div {if $name == 'copy' && $step == 3} class="hidden"{/if}>
                                        <div class="row form-group margin-bottom-10">
                                            <div class="col-xs-12">
                                                <div class="stack">
                                                    <div class="stack-main">
                                                        <label class="form-control-check">
                                                            <input name="copy[{$elements}][{$name}]" type="checkbox"
                                                                   value="1"
                                                                    {if !empty($options['required'])} checked="checked" class="readonly disable_class" {/if}
                                                                    {if !empty($settings[$elements][{$name}]) && !empty($settings[$elements][{$name}])} checked{/if}
                                                            />
                                                            {t}multilang::app.step.copy.{$elements}.{$name}{/t}
                                                            {if $name == 'translate'}
                                                                <img src="{$img_url}sitecp/img/flag/{{$languages['main']}}?{app('last_build')}"
                                                                     class="margin-left-10" width="20">
                                                                <i class="fa fa-arrow-right margin-left-10 margin-right-10"></i>
                                                                <img src="{$img_url}sitecp/img/flag/{{$languages['this']}}?{app('last_build')}"
                                                                     width="20">
                                                            {/if}
                                                        </label>
                                                    </div>

                                                    {if $name == 'copy' && !empty($options['price'])}
                                                        <div class="stack-addon item_price"
                                                             data-copy-price-{$elements}="{$options['price']}">
                                                            € {$options['price']}
                                                        </div>
                                                    {/if}
                                                    {if !empty($options) && is_string($options)}
                                                        <div class="stack-addon item_price"
                                                             data-price-{$elements}="{$options}">
                                                            € {$options}
                                                        </div>
                                                    {/if}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                            {/if}
                                {if $elements == 'url_manipulation'}

                                        <div class="stack">
                                            <div class="stack-main">
                                                <label class="control-label">
                                                    {t}multilang::app.setting.manipulate_url{/t}
                                                    <br>
                                                </label>
                                            </div>
                                            <div class="stack-addon">
                                                <select name="url_manipulation" id="url_manipulation_select" class="form-control select2me"
                                                        data-placeholder="{t}global.select{/t}...">
                                                    <option value="1">{t}multilang::app.setting.manipulate_url.1{/t}</option>
                                                    <option value="2">{t}multilang::app.setting.manipulate_url.2{/t}</option>
                                                    <option value="3">{t}multilang::app.setting.manipulate_url.3{/t}</option>
                                                </select>
                                            </div>
                                        </div>
                                    <div class="stack" style="margin-top: 10px;" id="url_remove_check">
                                        <div class="stack-main">
                                            <label class="control-label">{t}multilang::app.settings.remove_url{/t}</label>
                                        </div>
                                        <div class="stack-addon">
                                            <input type="checkbox" class="switch" value="1"
                                                   name="url_remove">
                                        </div>
                                    </div>
                                {/if}
                            {/foreach}
                        </div>
                    </div>
                {/foreach}
            </div>

            <div class="row form-group">
                <div class="col-md-12 text-right">
                    <h2>{t}multilang::app.step.subtotal{/t} € <span id="total_price" data-step="{$step}"
                                                                    data-subtotal="{{$subtotal['price']}}">{{$subtotal['price_formatted']}}</span>
                    </h2>
                </div>
            </div>
            <div class="row form-group">
                <div class="col-md-6 text-left">
                    {if $step == 3}
                        <a href="{route('apps.multilang.step.2')}" class="btn btn-default">{t}global.action.back{/t}</a>
                    {/if}
                </div>
                <div class="col-md-6 text-right">
                    <button class="btn btn-primary" type="submit"
                            id="editSubmit">{t}multilang::app.button.next_step{/t}</button>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
    .disable_class {
        opacity: 0.5;
    }
</style>