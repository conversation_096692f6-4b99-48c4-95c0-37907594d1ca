<?php

declare(strict_types=1);

namespace Modules\Apps\Administration\Szamlazz;

use App\Exceptions\Error;
use App\Models\Order\Order;
use Carbon\Carbon;
use Modules\Apps\Abstractions\Managers\AbstractAppManager;
use Modules\Apps\Administration\Szamlazz\Enums\DocumentType;
use Modules\Apps\Administration\Szamlazz\Formatters\InvoiceFormat;
use Modules\Apps\Administration\Szamlazz\Formatters\ReceiptFormat;
use Modules\Apps\Administration\Szamlazz\Sdk\SzamlaAgentAPI;
use Modules\Apps\Administration\Szamlazz\Sdk\TaxPayer;
use Throwable;

class SzamlazzManager extends AbstractAppManager
{
    public const APP_KEY = 'szamlazz';

    const TEST_TAXPAYER = '13421739-2-13';

    const DocumentReceipt = 'receipt';
    const DocumentInvoice = 'invoice';
    const DocumentCreditNote = 'credit_note';

    const DocumentAggregator = 'CloudCart';

    public function updateActive($status)
    {
        if(!$status){
            setting()->set('invoicing_provider', 'platform')->save();
        } else {
            setting()->set('invoicing_provider', self::APP_KEY)->save();
        }
        parent::updateActive($status);
    }

    /**
     * Get the Szamlazz API agent.
     * @param string $apiKey
     * @return Sdk\SzamlaAgent
     * @throws Sdk\SzamlaAgentException
     */
    public function getAgentAPI(?string $apiKey = null): Sdk\SzamlaAgent
    {
        if (!$apiKey) {
            $apiKey = $this->getSetting('apiKey');
        }
        $agent = SzamlaAgentAPI::create($apiKey);
        $agent->setXmlFileSave(false);
        $agent->setPdfFileSave(false);
        $agent->setLogLevel(0);
        $agent->setAggregator(self::DocumentAggregator);

        return $agent;
    }

    /**
     * Validate the tax number and determine the taxpayer status.
     * @param string $taxNumber
     * @return array
     */
    public function determineTaxpayerStatus(string $taxNumber): array
    {
        //    $taxNumber = 'BG206004146';
        $taxNumber = trim($taxNumber);

        if (empty($taxNumber)) {
            return [
                'taxPayer' => TaxPayer::TAXPAYER_NO_TAXNUMBER,
                'euVat' => false,
            ];
        }

        if (preg_match('/^\d{8}-\d{1}-\d{2}$/', $taxNumber)) {
            try {
                $agent = $this->getAgentAPI();
                $result = $agent->getTaxPayer($taxNumber);

                if ($result->isSuccess() && $result->getTaxpayerData()) {
                    return [
                        'taxPayer' => TaxPayer::TAXPAYER_HAS_TAXNUMBER,
                        'euVat' => false,
                    ];
                }
            } catch (\Exception) {
                return [
                    'taxPayer' => TaxPayer::TAXPAYER_WE_DONT_KNOW,
                    'euVat' => false,
                ];
            }
        }

        if (preg_match('/^[A-Z]{2}\d+$/', $taxNumber)) {
            return [
                'taxPayer' => TaxPayer::TAXPAYER_EU_ENTERPRISE,
                'euVat' => true,
            ];
        }

        return [
            'taxPayer' => TaxPayer::TAXPAYER_NON_EU_ENTERPRISE,
            'euVat' => true,
        ];
    }

    /**
     * Generate a receipt in Szamlazz for the given order.
     * @param int|Order $order
     * @return void
     */
    public function createReceipt(int|Order $order): bool
    {
        $type = DocumentType::from(self::DocumentReceipt)->value;
        try {
            if (!($order instanceof Order)) {
                $order = Order::find($order);
            }

            if (!$order) {
                throw new Error('Order not found');
            }
            $formatter = new ReceiptFormat($order, $this);
            $response = $formatter->create($formatter->format());
            $order->removeMeta(self::APP_KEY . '_' . $type . '_error');
            $order->updateMeta([
                self::APP_KEY . '_' . $type . '_id' => $response->getId(),
                self::APP_KEY . '_' . $type . '_number' => $response->getReceiptNumber(),
                self::APP_KEY . '_' . $type . '_pdf' => $response->getPdfData(),
                self::APP_KEY . '_' . $type . '_date' => $response->getCreated(),
            ]);

            return true;
        } catch (Throwable $e) {
            $order->updateMeta([
                self::APP_KEY . '_' . $type . '_error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get the PDF of the document for the given order.
     * @param int $orderId
     * @param DocumentType $documentType
     * @param bool $isCancelled
     * @return string
     * @throws Error
     */
    public function getPdf(int $orderId, string $documentType, bool $isCancelled = false): string|Error
    {
        try {
            $documentType = DocumentType::from($documentType);
            if (!$documentType) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid document type'
                ], 422);
            }
            $order = Order::find($orderId);
            if (!$order) {
                throw new Error('Receipt not found', 404);
            }

            if ($isCancelled) {
                $base64 = $order->meta_pluck->get(self::APP_KEY . '_' . $documentType->value . '_pdf_cancel');
            } else {
                $base64 = $order->meta_pluck->get(self::APP_KEY . '_' . $documentType->value . '_pdf');
            }
            if (!$base64) {
                throw new Error('Document not found', 404);
            }
            return $base64;
        } catch (Throwable $e) {
            return new Error($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Cancel the receipt for the given order.
     * @param int|Order $order
     * @return bool
     */
    public function cancelReceipt(int|Order $order): bool
    {
        try {
            $type = DocumentType::from(self::DocumentReceipt)->value;
            if (!($order instanceof Order)) {
                $order = Order::find($order);
            }

            if (!$order) {
                throw new Error('Order not found');
            }

            $getNumber = $order->meta_pluck->get(self::APP_KEY . '_' . $type . '_number');
            if (!$getNumber) {
                throw new Error('Receipt number not found');
            }

            $formatter = new ReceiptFormat($order, $this);
            $response = $formatter->cancel((string)$getNumber);
            $order->removeMeta([self::APP_KEY . '_' . $type . '_error', self::APP_KEY . '_' . $type . '_id', self::APP_KEY . '_' . $type . '_pdf', self::APP_KEY . '_' . $type . '_number', self::APP_KEY . '_' . $type . '_date']);
//            $order->updateMeta([
//                self::APP_KEY . '_' . $type . '_id_cancel' => $response->getId(),
//                self::APP_KEY . '_' . $type . '_number_cancel' => $response->getReceiptNumber(),
//                self::APP_KEY . '_' . $type . '_pdf_cancel' => $response->getPdfData(),
//            ]);
            return true;
        } catch (Throwable $e) {
            $order->updateMeta([
                self::APP_KEY . '_' . $type . '_error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Create an invoice in Szamlazz for the given order.
     * @param int|Order $order
     * @return bool
     */
    public function createInvoice(int|Order $order): bool
    {
        $type = DocumentType::from(self::DocumentInvoice)->value;
        try {
            if (!($order instanceof Order)) {
                $order = Order::find($order);
            }

            if (!$order) {
                throw new Error('Order not found');
            }

            $formatter = new InvoiceFormat($order, $this);
            $response = $formatter->create($formatter->format());
            $order->removeMeta([self::APP_KEY . '_' . $type . '_error', self::APP_KEY . '_' . $type . '_id', self::APP_KEY . '_' . $type . '_pdf', self::APP_KEY . '_' . $type . '_number',
                self::APP_KEY . '_' . self::DocumentCreditNote . '_id', self::APP_KEY . '_' . self::DocumentCreditNote . '_number', self::APP_KEY . '_' . $type . '_pdf',
                self::APP_KEY . '_' . $type . '_cancelled', self::APP_KEY . '_' . $type . '_paid', self::APP_KEY . '_' . $type . '_date',  self::APP_KEY . '_' . self::DocumentCreditNote . '_date']);
            $order->updateMeta([
                self::APP_KEY . '_' . $type . '_id' => $response->getInvoiceIdentifier(),
                self::APP_KEY . '_' . $type . '_number' => $response->getInvoiceNumber(),
                self::APP_KEY . '_' . $type . '_pdf' => $response->getPdfData(),
                self::APP_KEY . '_' . $type . '_paid' => $response->getAssetAmount() == 0 ? 1 : 0,
                self::APP_KEY . '_' . $type . '_date' => Carbon::now()->format('Y-m-d'),
            ]);
            return true;
        } catch (Throwable $e) {
            $order->updateMeta([
                self::APP_KEY . '_' . $type . '_error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Cancel the invoice for the given order.
     * @param int|Order $order
     * @return bool
     */
    public function cancelInvoice(int|Order $order): bool
    {
        try {
            $type = DocumentType::from(self::DocumentInvoice)->value;
            if (!($order instanceof Order)) {
                $order = Order::find($order);
            }

            if (!$order) {
                throw new Error('Order not found');
            }

            $getNumber = $order->meta_pluck->get(self::APP_KEY . '_' . $type . '_number');
            if (!$getNumber) {
                throw new Error('Invoice number not found');
            }

            $formatter = new InvoiceFormat($order, $this);
            $response = $formatter->cancel((string)$getNumber);
            if ($this->getSetting('credit_note.active') == 1) {
                $order->updateMeta([
                    self::APP_KEY . '_' . self::DocumentCreditNote . '_id' => $response->getInvoiceIdentifier(),
                    self::APP_KEY . '_' . self::DocumentCreditNote . '_number' => $response->getInvoiceNumber(),
                    self::APP_KEY . '_' . self::DocumentCreditNote . '_pdf' => $response->getPdfData(),
                    self::APP_KEY . '_' . $type . '_cancelled' => true,
                    self::APP_KEY . '_' . self::DocumentCreditNote . '_date' => Carbon::now()->format('Y-m-d'),
                ]);
            } else {
                $order->removeMeta([self::APP_KEY . '_' . $type . '_error', self::APP_KEY . '_' . $type . '_id', self::APP_KEY . '_' . $type . '_pdf',
                    self::APP_KEY . '_' . $type . '_number', self::APP_KEY . '_' . $type . '_paid', self::APP_KEY . '_' . $type . '_date']);
            }
            return true;
        } catch (Throwable $e) {
            $order->updateMeta([
                self::APP_KEY . '_' . self::DocumentCreditNote . '_error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Pay the invoice for the given order.
     * @param int|Order $order
     * @return void
     */
    public function payInvoice(int|Order $order)
    {
        try {
            if (!($order instanceof Order)) {
                $order = Order::find($order);
            }

            $getNumber = $order->meta_pluck->get(self::APP_KEY . '_' . self::DocumentInvoice . '_number');
            if (!$getNumber) {
                throw new Error('Receipt number not found');
            }

            $formatter = new InvoiceFormat($order, $this);
            $response = $formatter->pay((string)$getNumber);
            $order->updateMeta([
                self::APP_KEY . '_' . self::DocumentInvoice . '_id' => $response->getInvoiceIdentifier(),
                self::APP_KEY . '_' . self::DocumentInvoice . '_paid' => 1,
            ]);
        } catch (Throwable $e) {
            $order->updateMeta([
                self::APP_KEY . '_' . self::DocumentInvoice . '_error' => $e->getMessage(),
            ]);
        }
    }
}
