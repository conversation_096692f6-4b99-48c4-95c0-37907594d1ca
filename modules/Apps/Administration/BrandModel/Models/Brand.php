<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 18.7.2019 г.
 * Time: 10:12 ч.
 */

namespace Modules\Apps\Administration\BrandModel\Models;

use App\Contracts\SeoFieldContract;
use App\Models\Product\Product;
use App\Models\Setting\UrlHandleHistory;
use App\Traits\Crudling;
use App\Traits\ExternalMetaData;
use App\Traits\Image;
use App\Traits\SeoFields;
use App\Traits\UrlHandle;
use App\Traits\Model as ModelTrait;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\Apps\Administration\BrandModel\Models\ProductToBrandModel[] $to_products
 * @property \Illuminate\Database\Eloquent\Collection|\App\Models\Product\Product[] $products
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\Apps\Administration\BrandModel\Models\Model[] $models
 * @property null|\Modules\Apps\Administration\BrandModel\Models\Model $model
 * @property \Illuminate\Database\Eloquent\Collection|\App\Models\Setting\UrlHandleHistory[] $url_history
 * @property string $title
 * @property string $seo_title
 * @property string $seo_description
 * @property int $id
 * @property int $active
 * @property null|integer $models_count
 * @property null|int $to_products_count
 * @property null|int $products_count
 * @property string $url_handle
 * @method \Illuminate\Database\Eloquent\Collection|Brand[] getRelationList($where = null, $join = null, \App\Helper\Grid $grid = null)
 * @method static Brand urlHandle($url_handle)
 * @method static Brand active()
 */
class Brand extends \Eloquent implements SeoFieldContract
{
    use UrlHandle;
    use Image;
    use SeoFields;
    use ModelTrait;
    use Crudling;

    use ExternalMetaData;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = '@brand_model_brands';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title', 'description', 'active',
        'seo_title', 'seo_description'
    ];

    /**
     * The status column used for changeActivity.
     *
     * @var string
     */
    protected $_status_column = 'active';

    /**
     * The name field for url handle.
     *
     * @var string
     */
    protected $name_field = 'title';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|\Modules\Apps\Administration\BrandModel\Models\Model
     */
    public function models()
    {
        return $this->hasMany(Model::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne|\Modules\Apps\Administration\BrandModel\Models\Model
     */
    public function model()
    {
        return $this->hasOne(Model::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany|\App\Models\Product\Product
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, '@brand_model_product_to_model', 'brand_id', 'product_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany|\Modules\Apps\Administration\BrandModel\Models\ProductToBrandModel
     */
    public function to_products()
    {
        return $this->hasMany(ProductToBrandModel::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany|\App\Models\Setting\UrlHandleHistory
     */
    public function url_history()
    {
        return $this->morphMany(UrlHandleHistory::class, 'url_history', 'type', 'model_id', $this->getKeyName());
    }

    /**
     * @param Builder $query
     */
    public function scopeActive($query): void
    {
        $query->where('active', 1);
    }

}
