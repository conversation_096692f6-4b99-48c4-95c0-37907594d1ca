<?php

declare(strict_types=1);

namespace Modules\Apps\Administration\Bundles\Helpers;

use App\Exceptions\Error;
use App\Helper\Format;
use App\Helper\YesNo;
use App\Models\Gate\PlanFeature;
use App\Models\Product\Variant;
use Carbon\Carbon;
use Modules\Apps\Administration\Bundles\Model\BundleProduct;
use Modules\Core\Helpers\Eloquent\Model\AbstractMagicFormatter;

/**
 * Class AbstractFormatter
 *
 * @property int $id The ID of the bundle.
 * @property string $name The name of the bundle.
 * @property string $url_handle The URL handle of the bundle.
 * @property string $date_added The date the bundle was added.
 * @property string $date_modified The date the bundle was modified.
 * @property int|null $sort_order The sort order of the bundle.
 * @property float|null $price_from The starting price of the bundle.
 * @property float|null $price_to The ending price of the bundle.
 * @property string|null $publish_date The publish date of the bundle.
 * @property string|null $active_to The active to date of the bundle.
 * @property bool $active The active status of the bundle.
 * @property bool $draft The draft status of the bundle.
 * @property bool $featured The featured status of the bundle.
 * @property bool $new The new status of the bundle.
 * @property bool $hidden The hidden status of the bundle.
 * @property-read string $type The type of the bundle.
 * @property string $price_type The price type of the bundle.
 * @property int|null $price_percent The price percentage of the bundle.
 * @property int $individual_price The individual price of the bundle.
 * @property string $description The description of the bundle.
 * @property string $seo_title The SEO title of the bundle.
 * @property string $seo_description The SEO description of the bundle.
 * @property int $per_row The per row value of the bundle.
 * @property string $image Get the bundle's image.
 * @property bool $has_image Has bundle's image.
 * @property BundleProductsFormatter $bundle_products The bundle's products.
 * @property MetaFormatter $meta The bundle's meta.
 * @property VariantFormatter $variant The bundle's variant.
 */
abstract class AbstractFormatter extends AbstractMagicFormatter
{
    /** @var BundleProductsFormatter */
    protected $__products;

    /** @var MetaFormatter */
    protected $__meta;

    /** @var VariantFormatter */
    protected $__variant;

    /** @var array */
    public $dirtyLog = [];

    /**
     * AbstractFormatter constructor.
     *
     * @param  BundleProduct  $bundle
     */
    public function __construct(protected \Modules\Apps\Administration\Bundles\Model\BundleProduct $bundle)
    {
        if ($this->bundle->relationLoaded('bundle_products_cp')) {
            $this->__products = BundleProductsFormatter::make($this->bundle->bundle_products_cp, $this);
        }

        if ($this->bundle->relationLoaded('meta_data')) {
            $this->__meta = MetaFormatter::make($this->bundle->meta_data, $this);
        }

        if ($this->bundle->relationLoaded('variant')) {
            $this->__variant = VariantFormatter::make($this->bundle->variant ?: new Variant(), $this);
        }
    }

    /**
     * Create an instance of the formatter.
     *
     * @param  BundleProduct  $bundle
     * @return static
     */
    public static function make(BundleProduct $bundle): AbstractFormatter
    {
        return new static($bundle);
    }

    /**
     * Get the bundle's ID.
     *
     * @return null|int
     */
    public function getId(): ?int
    {
        return $this->bundle->id;
    }

    /**
     * Get the bundle's name.
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->bundle->name;
    }

    /**
     * Set the bundle's name.
     *
     * @param  string  $value
     * @return $this
     */
    public function setName(string $value): AbstractFormatter
    {
        $this->bundle->name = $value;

        return $this;
    }

    /**
     * Get the bundle's name.
     *
     * @return null|int
     */
    public function getCategoryId(): ?int
    {
        return when(is_numeric($this->bundle->category_id), intval($this->bundle->category_id));
    }

    /**
     * Set the bundle's category.
     *
     * @param  int|null  $value
     * @return $this
     */
    public function setCategoryId($value): AbstractFormatter
    {
        $this->bundle->category_id = when(is_numeric($value), intval($value));

        return $this;
    }

    /**
     * Get the bundle's category.
     *
     * @return null|array
     */
    public function getCategory(): ?array
    {
        if (!$this->getCategoryId() || !$this->bundle->category) {
            return null;
        }

        return [
            'id' => $this->getCategoryId(),
            'name' => $this->bundle->category->path->implode('name', ' > '),
        ];
    }

    /**
     * Get the bundle's URL handle.
     *
     * @return null|string
     */
    public function getUrlHandle(): ?string
    {
        return $this->bundle->url_handle;
    }

    /**
     * Set the bundle's URL handle.
     *
     * @param  string|null  $value
     * @return $this
     */
    public function setUrlHandle(?string $value): AbstractFormatter
    {
        $this->bundle->url_handle = $value;

        return $this;
    }

    /**
     * Get the bundle's URL.
     *
     * @return null|string
     */
    public function getWeb(): ?string
    {
        return $this->bundle->url;
    }

    /**
     * Get the date the bundle was added.
     *
     * @return string|null
     */
    public function getDateAdded(): ?string
    {
        return optional($this->bundle->date_added)->toIso8601String();
    }

    /**
     * Get the date the bundle was modified.
     *
     * @return string|null
     */
    public function getDateModified(): ?string
    {
        return optional($this->bundle->date_modified)->toIso8601String();
    }

    /**
     * Get the bundle's sort order.
     *
     * @return null|int
     */
    public function getSortOrder(): ?int
    {
        return $this->bundle->sort_order;
    }

    /**
     * Set the bundle's sort order.
     *
     * @param  null|int  $value
     * @return $this
     */
    public function setSortOrder($value): AbstractFormatter
    {
        $this->bundle->sort_order = is_numeric($value) ? $value : null;

        return $this;
    }

    /**
     * Get the bundle's price from.
     *
     * @return float|null
     */
    public function getPriceFrom(): ?float
    {
        return is_numeric($this->bundle->price_from) ? moneyFloat($this->bundle->price_from) : null;
    }

    /**
     * Set the bundle's price from.
     *
     * @param  float|null  $value
     * @return $this
     */
    public function setPriceFrom($value): AbstractFormatter
    {
        $this->bundle->price_from = is_numeric($value) ? Format::toIntegerPrice($value) : null;

        return $this;
    }

    /**
     * Get the bundle's price to.
     *
     * @return float|null
     */
    public function getPriceTo(): ?float
    {
        return is_numeric($this->bundle->price_to) ? moneyFloat($this->bundle->price_to) : null;
    }

    /**
     * Set the bundle's price to.
     *
     * @param  float|null  $value
     * @return $this
     */
    public function setPriceTo($value): AbstractFormatter
    {
        $this->bundle->price_to = is_numeric($value) ? Format::toIntegerPrice($value) : null;

        return $this;
    }

    /**
     * Get the bundle's publish date.
     *
     * @return string|null
     */
    public function getPublishDate(): ?string
    {
        return optional($this->bundle->publish_date)->toIso8601String();
    }

    /**
     * Set the bundle's publish date.
     *
     * @param  string|null  $value
     * @return $this
     */
    public function setPublishDate(?string $value): AbstractFormatter
    {
        $this->bundle->publish_date = $value ? Carbon::parse($value) : null;

        return $this;
    }

    /**
     * Get the bundle's active to date.
     *
     * @return string|null
     */
    public function getActiveTo(): ?string
    {
        return optional($this->bundle->active_to)->toIso8601String();
    }

    /**
     * Set the bundle's active to date.
     *
     * @param  string|null  $value
     * @return $this
     */
    public function setActiveTo(?string $value): AbstractFormatter
    {
        $this->bundle->active_to = $value ? Carbon::parse($value) : null;

        return $this;
    }

    /**
     * Get the bundle's active status.
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->parseYesNo($this->bundle->active);
    }

    /**
     * Set the bundle's active status.
     *
     * @param  int|string|bool$value
     * @return $this
     */
    public function setActive(int|string|bool $value): AbstractFormatter
    {
        $this->bundle->active = $this->reverseYesNo($value);

        return $this;
    }

    /**
     * Get the bundle's draft status.
     *
     * @return int
     */
    public function isDraft(): int
    {
        return intval($this->parseYesNo($this->bundle->draft));
    }

    /**
     * Set the bundle's draft status.
     *
     * @param  int|string|bool $value
     * @return $this
     */
    public function setDraft(int|string|bool $value): AbstractFormatter
    {
        $this->bundle->draft = $this->reverseYesNo(boolval($value));

        return $this;
    }

    /**
     * Get the bundle's featured status.
     *
     * @return int
     */
    public function isFeatured(): int
    {
        return intval($this->bundle->featured);
    }

    /**
     * Set the bundle's featured status.
     *
     * @param int|string|bool$value
     * @return $this
     */
    public function setFeatured(int|string|bool $value): AbstractFormatter
    {
        $this->bundle->featured = $value;

        return $this;
    }

    /**
     * Get the bundle's new status.
     *
     * @return int
     */
    public function isNew(): int
    {
        return intval($this->parseYesNo($this->bundle->new));
    }

    /**
     * Set the bundle's new status.
     *
     * @param int|string|bool $value
     * @return $this
     */
    public function setNew(int|string|bool $value): AbstractFormatter
    {
        $this->bundle->new = $this->reverseYesNo(boolval($value));

        return $this;
    }

    /**
     * Get the bundle's hidden status.
     *
     * @return int
     */
    public function isHidden(): int
    {
        return intval($this->bundle->is_hidden);
    }

    /**
     * Set the bundle's hidden status.
     *
     * @param  int|string|bool $value
     * @return $this
     */
    public function setHidden(int|string|bool $value): AbstractFormatter
    {
        if ($value && PlanFeature::findByMap('hidden_products')) {
            $this->bundle->is_hidden = $value;
        } else {
            $this->bundle->is_hidden = 0;
        }

        return $this;
    }

    /**
     * Get the bundle's type.
     *
     * @return string
     */
    public function getType(): string
    {
        return BundleProduct::TYPE_BUNDLE;
    }

    /**
     * Get the bundle's price type.
     *
     * @return string
     */
    public function getPriceType(): string
    {
        return in_array($this->bundle->price_type, ['price', 'percent']) ? $this->bundle->price_type : 'price';
    }

    /**
     * Set the bundle's price type.
     *
     * @param  string  $value
     * @return $this
     */
    public function setPriceType(string $value): AbstractFormatter
    {
        $this->bundle->price_type = in_array($value, ['price', 'percent']) ? $value : 'price';

        return $this;
    }

    /**
     * Get the bundle's price percent.
     *
     * @return float|null
     */
    public function getPricePercent(): ?float
    {
        return is_numeric($this->bundle->price_percent) ? percentFloat($this->bundle->price_percent) : null;
    }

    /**
     * Set the bundle's price percent.
     *
     * @param  float|null  $value
     * @return $this
     */
    public function setPricePercent($value): AbstractFormatter
    {
        $this->bundle->price_percent = is_numeric($value) ? round($value * 100) : null;

        return $this;
    }

    /**
     * Get the bundle's individual price.
     *
     * @return int
     */
    public function getIndividualPrice(): int
    {
        return $this->bundle->individual_price;
    }

    /**
     * Set the bundle's individual price.
     *
     * @param  int  $value
     * @return $this
     */
    public function setIndividualPrice($value): AbstractFormatter
    {
        $this->bundle->individual_price = is_numeric($value) ? $value : null;

        return $this;
    }

    /**
     * Get the bundle's description.
     *
     * @return null|string
     */
    public function getDescription(): ?string
    {
        return $this->bundle->description;
    }

    /**
     * Get the bundle's short description.
     *
     * @return null|string
     */
    public function getShortDescription(): ?string
    {
        return $this->bundle->short_description;
    }

    /**
     * @param  string|null  $value
     * @return mixed
     */
    public function setShortDescription(?string $value)
    {
        $this->bundle->short_description = $value;

        return $this;
    }

    /**
     * Set the bundle's description.
     *
     * @param  null|string  $value
     * @return $this
     */
    public function setDescription(?string $value): AbstractFormatter
    {
        $this->bundle->description = $value;

        return $this;
    }

    /**
     * Get the bundle's SEO title.
     *
     * @return null|string
     */
    public function getSeoTitle(): ?string
    {
        return $this->bundle->seo_title;
    }

    /**
     * Set the bundle's SEO title.
     *
     * @param  null|string  $value
     * @return $this
     */
    public function setSeoTitle(?string $value): AbstractFormatter
    {
        $this->bundle->seo_title = $value;

        return $this;
    }

    /**
     * Get the bundle's SEO description.
     *
     * @return null|string
     */
    public function getSeoDescription(): ?string
    {
        return $this->bundle->seo_description;
    }

    /**
     * Set the bundle's SEO description.
     *
     * @param  null|string  $value
     * @return $this
     */
    public function setSeoDescription(?string $value): AbstractFormatter
    {
        $this->bundle->seo_description = $value;

        return $this;
    }

    /**
     * Get the bundle's per row value.
     *
     * @return int
     */
    public function getPerRow(): int
    {
        return (int)$this->bundle->per_row;
    }

    /**
     * Set the bundle's per row value.
     *
     * @param  int  $value
     * @return $this
     */
    public function setPerRow($value): AbstractFormatter
    {
        $this->bundle->per_row = is_numeric($value) ? $value : 1;

        return $this;
    }

    /**
     * Get the bundle's products.
     *
     * @return BundleProductsFormatter
     */
    public function getBundleProducts(): BundleProductsFormatter
    {
        return $this->__products = $this->__products ?: BundleProductsFormatter::make(collect(), $this);
    }

    /**
     * Set the bundle's products.
     *
     * @param  array  $data
     * @return AbstractFormatter
     */
    public function setBundleProducts(array $data): AbstractFormatter
    {
        $this->__products = $this->getBundleProducts()->fill($data);

        return $this;
    }

    /**
     * Get the bundle's meta.
     *
     * @return MetaFormatter
     */
    public function getMeta(): MetaFormatter
    {
        return $this->__meta = $this->__meta ?: MetaFormatter::make(collect(), $this);
    }

    /**
     * Set the bundle's meta.
     *
     * @param  array  $data
     * @return self
     */
    public function setMeta(array $data): self
    {
        $this->__meta = $this->getMeta()->fill($data);

        return $this;
    }

    /**
     * Get the bundle's variant.
     *
     * @return VariantFormatter
     */
    public function getVariant(): VariantFormatter
    {
        return $this->__variant = $this->__variant ?: VariantFormatter::make(new Variant(), $this);
    }

    /**
     * Set the bundle's variant.
     *
     * @param  array  $data
     * @return self
     */
    public function setVariant(array $data): self
    {
        $this->__variant = $this->getVariant()->fill($data);

        return $this;
    }

    /**
     * Get the bundle's image.
     *
     * @return string
     *
     * @throws Error
     */
    public function getImage(): string
    {
        return $this->bundle->getImage('150x150');
    }

    /**
     * Has the bundle's image.
     *
     * @return bool
     */
    public function isHasImage(): bool
    {
        return boolval($this->bundle->hasImage());
    }

    /**
     * Convert the bundle to an array.
     *
     * @return array
     *
     * @throws Error
     */
    public function toArray(): array
    {
        $array = [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'category_id' => $this->getCategoryId(),
            'url_handle' => $this->getUrlHandle(),
            'web' => $this->getWeb(),
            'date_added' => $this->getDateAdded(),
            'date_modified' => $this->getDateModified(),
            'has_image' => $this->isHasImage(),
            'image' => $this->getImage(),
            'category' => $this->getCategory(),
            'vendor' => null,
            'active' => $this->isActive(),
            'draft' => $this->isDraft(),
            'hidden' => $this->isHidden(),
            'featured' => $this->isFeatured(),
            'new' => $this->isNew(),
            'sort_order' => $this->getSortOrder(),
            'quantity' => null,
            'review' => $this->review,
            'price_from' => $this->getPriceFrom(),
            'price_to' => $this->getPriceTo(),
            'publish_date' => $this->getPublishDate(),
            'active_to' => $this->getActiveTo(),
            'seo_title' => $this->getSeoTitle(),
            'seo_description' => $this->getSeoDescription(),
            'per_row' => $this->getPerRow(),
        ];

        if ($this->bundle->relationLoaded('change_log')) {
            $array['change_log_exists'] = $this->bundle->change_log->isNotEmpty();
        }

        if ($this->bundle->relationLoaded('bundle_products_cp')) {
            $array['bundle_products'] = $this->getBundleProducts()->toArray();
        }

        if ($this->bundle->relationLoaded('meta_data')) {
            $array['meta'] = $this->getMeta()->toArray();
        }

        if ($this->bundle->relationLoaded('variant')) {
            $array['variant'] = $this->getVariant()->toArray();
        }

        return $array;
    }

    /**
     * Parse YesNo values.
     *
     * @param  mixed  $value
     * @return bool
     */
    protected function parseYesNo($value): bool
    {
        return boolval(YesNo::parse($value));
    }

    /**
     * Reverse YesNo values.
     *
     * @param  int|string|bool|null $value
     * @return string
     */
    protected function reverseYesNo(int|string|bool|null $value): string
    {
        return YesNo::reverse(boolval($value));
    }
}
