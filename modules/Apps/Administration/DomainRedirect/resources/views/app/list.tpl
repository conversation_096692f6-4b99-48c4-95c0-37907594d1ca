{include file="apps/includes/app-breadcrumb.tpl" active=__('domain_redirect::app.title')}
{include file="./includes/dependency.tpl"}
{include file="apps/dependency/settings.tpl"}

<div class="page-breadcrumb clearfix">
    {$breadcrumb = [
        [
            'href' => asset('apps'),
            'title' => "{t}sidebar.apps{/t}"
        ],
        [
            'title' => __('domain_redirect::app.title')
        ]
    ]}
    {include file="includes/breadcrumb.tpl"}
    <div class="pull-right">
        <a href="#" id="editSubmit" class="btn btn-primary btn-dr">{t}global.save{/t}</a>
    </div>
</div>

<div class="wrapper">
    {$site = site()}
    {if $site->user->id|default}
    <div class="box">
        <div class="box-title">
            <div class="box-title-text alert alert-info">
                <h5>{__('domain_redirect::app.hint', ['user' => $site->user->id])}</h5>
            </div>
        </div>
    </div>
    {/if}

    <form action="{route('apps.domain_redirect')}" id="editForm" role="form" class="ajaxForm">
        <input type="hidden" name="_t" value="1">

        <div class="box">
            <div class="box-title">
                <div class="box-title-text">
                    <div class="row">
                        <div class="col-xs-3">
                            <h5>{t}global.country{/t}</h5>
                        </div>

                        <div class="col-xs-8">
                            <h5>{t}domain.domain{/t}</h5>
                        </div>

                        <div class="col-xs-1">
                        </div>
                    </div>
                </div>
            </div>

            <div class="js-event-mapping">
                <div class="box-section">
                    <div class="row">
                        <div class="col-xs-3">
                            <div class="select2-container form-control">
                                <span class="select2-choice" tabindex="-1">
                                    <span class="select2-chosen" id="select2-chosen-4">All</span>
                                </span>
                            </div>
                        </div>

                        <div class="col-xs-8">
                            <select name="global" class="form-control select2me">
                                <option value="">{t}global.select{/t}</option>
                                {foreach $domains as $site_id => $domain}
                                    <option value="{$site_id}" {if $global->site_id|default == $site_id}selected="selected"{/if}>{$domain}</option>
                                {/foreach}
                            </select>
                        </div>

                        <div class="col-xs-1"></div>
                    </div>
                </div>
                {foreach $records as $row => $record}
                    <div class="box-section">
                        <div class="row">
                            <div class="col-xs-3">
                                <select name="mapping[{$row}][country]" class="form-control select2me" data-type="country">
                                    <option value="">{t}global.select{/t}</option>
                                    {foreach $countries as $country_iso => $country_name}
                                        <option data-flag="{$country_iso}" value="{$country_iso}" {if $record->country == $country_iso}selected="selected"{/if}>{$country_name}</option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="col-xs-8">
                                <select name="mapping[{$row}][site_id]" class="form-control select2me">
                                    <option value="">{t}global.select{/t}</option>
                                    {foreach $domains as $site_id => $domain}
                                        <option value="{$site_id}" {if $record->site_id == $site_id}selected="selected"{/if}>{$domain}</option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="col-xs-1" style="padding-top: 5px">
                                <a href="#" class="text text-alert js-event-mapping-remove"><i class="fal fa-trash-alt"></i></a>
                            </div>
                        </div>
                    </div>
                {/foreach}
            </div>

            <div class="box-section">
                <a class="add-link js-event-mapping-add" href="#"><i class="fal fa-plus-circle fa-lg" style="display: inline-block; vertical-align: middle; margin-right: 5px;"></i>{t}global.add_row{/t}</a>
            </div>
        </div>
    </form>
</div>