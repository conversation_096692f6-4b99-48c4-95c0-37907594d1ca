{capture append="js"}
    <script type="text/javascript">
        loadScripts([
            '{assetic bundle="multipart_form_bdl" output="js"}'
        ]);

        (function () {

            var dpdromaniaApp = {
                _data: null,
                _form: null,
                _checkedServices:function() {
                    return {json_encode($settings->get('allowed_methods',[])) nofilter};
                },
                _checkedServicesInternational:function() {
                    return {json_encode($settings->get('free_method_international',[])) nofilter};
                },
                init: function(object) {
                    this._data = object;
                    if(this._data) {
                        for(var key in this._data) {
                            if(this[key]) {
                                this[key](this._data[key]);
                            }
                        }
                    }
                    return this;
                },
                form: function(form) {
                    if(!this._form) {
                        this._form = form;
                    }
                    return this;
                },

                clients: function(value) {
                    var self = this;
                    function fillSelect(name, value) {
                        var select = self._form.find('[name="dpdromania['+name+']"]').val('').trigger('change').empty();
                        for(i in value) {
                            select.append('<option data-city-id="'+value[i]['city_id']+'" value="'+value[i]['id']+'">'+value[i]['name']+'</option>');
                        }
                        if(select.data('select2')) {
                            select.select2('destroy');
                        }
                        App.initSelect2(select.closest('.row'));
                    }
                    fillSelect('client_id', value);
                    fillSelect('payer_id', value);
                },
            };

            /* begin regions */
            var $form = $('#DpdForm');

            $form.on('change', '[name="dpdromania[option_before_payment]"]', function() {
                console
                CC.statusBox($('.js-instruction-returns'), this.value != 'no_option');
            }).find('[name="dpdromania[option_before_payment]"]').trigger('change');

            $form.on('submit', function (e) {
                e.preventDefault();
                Custom.submitFormMultipart($(this));
            });


            $form.on('change', 'input[name="dpdromania[target]"]', function() {
                var $region_inputs = $form.find('#regions_holder select');
                if ($(this).prop('checked')) {
                    $form.find('#regions_holder').css({
                        'opacity': '0.6'
                    });
                    $region_inputs.prop('disabled', true);
                } else {
                    $form.find('#regions_holder').css({
                        'opacity': '1'
                    });
                    $region_inputs.prop('disabled', false);
                }
                $region_inputs.prop('checked', false);
                App.updateUniform($region_inputs);
            }).find('input[name="dpdromania[target]"]').trigger('change');

            $form.on('click', 'input[type="checkbox"][name="dpdromania[regions][]"]', function (e) {
                handleCheckboxRescursive(e, $(this), '#Regions');
            }).on('change', 'input[name="dpdromania[is_region]"]', function() {
                var $region_inputs = $form.find('#Regions input');
                if ($(this).prop('checked')) {
                    $form.find('#Regions').closest('.box-section').removeClass('hidden');
                    $region_inputs.prop('disabled', false);
                } else {
                    $form.find('#Regions').closest('.box-section').addClass('hidden');
                    $region_inputs.prop('disabled', true);
                }
                $region_inputs.prop('checked', false);
                App.updateUniform($region_inputs);
            });
            $('input[name="dpdromania[is_region]"]').trigger('change');

            {if !empty($provider->regions)}
            {foreach $provider->regions as $region}
            $("input[type='checkbox'][name='regions[]'][value='{$region->id}']").trigger('click');
            {/foreach}
            {elseif isset($provider) && $provider->target == 'restofworld'}
            var $region_inputs = $form.find('#Regions input');
            $form.find('#Regions').css({
                'opacity': '0.6'
            });
            $region_inputs.prop('disabled', true);
            {/if}
            /* end regions */

            $('[name="dpdromania[cd]"]').on('change', function() {
                CC.statusBox($('[name="dpdromania[cd_max]"]').closest('.form-group'), $(this).is(':checked'));
            }).trigger('change');

            $('#dpdromania_city').on('change', function(e) {
                $('#dpdromaniaForm [name="dpdromania[post_code]"]').val('');
                if(e.added && e.added.postcode) {
                    $('#dpdromaniaForm [name="dpdromania[post_code]"]').val(e.added.postcode);
                }
            });

            $("[data-field]").on('input', function(){
                $('[data-hidden]').addClass('hidden');
                $('[data-visible]').removeClass('hidden');
            });

            $('[name="dpdromania[side]"]').on('change', function() {
                CC.statusBox($('[name="dpdromania[payer_id]"]').closest('.js-dpdromania-payer'), $(this).val() == '2');
            }).trigger('change');

            $("[data-btn='dpdromania-test']").on("click", function () {
                var $btn = $(this);
                var $form = $btn.closest("form");

                $btn.trigger('loading.start');
                $btn.prop("disabled", true);

                $form.on('cc.ajax.success', function(e, json) {
                    $btn.trigger('loading.end');
                    $btn.prop("disabled", false);

                    if('fields' in json) {
                        dpdromaniaApp.form($form).init(json.fields);
                        $('[data-hidden]').removeClass('hidden');
                        $('[data-visible]').addClass('hidden');
                    }
                    App.initAjax($form);
                });

                $form.on('cc.ajax.error', function(e) {
                    $btn.trigger('loading.end');
                    $btn.prop("disabled", false);
                });

                Custom.submitFormAjax($form, $btn.data("url"));
            });

            $('[data-to-shipping]').on('change', function() {
                var $el = $(this);
                CC.statusBox($('.js-services-box-' + $el.data('shipping-key')), $el.is(':checked'));
                if($el.data('toShipping')) {
                    if(!$el.is(':checked')) {
                        App.updateSwitch($('[name="dpdromania[' + $el.data('toShipping') + ']"]').prop('checked', true));
                        CC.statusBox($('.js-services-box-' + $el.data('toShipping')), 1);
                    }
                }
            }).trigger('change');

            $(document).on('change', '[name="dpdromania[send_type]"]', function (e) {
                e.preventDefault();
                var $el = $(this);
                if($el.val() =='office'){
                    $('#type_office').removeClass('hidden');
                    $('#type_address').addClass('hidden');
                }
                if($el.val() =='address'){
                    $('#type_office').addClass('hidden');
                    $('#type_address').removeClass('hidden');
                }
            });

            $(document).on('change', '[name="dpdromania[cd]"]', function (e) {
                e.preventDefault();
                var $el = $(this);
                if($el.is(':checked')){
                    $('#cod_pay').removeClass('hidden');
                } else {
                    $('#cod_pay').addClass('hidden');
                }
            });

            $(document).on('change', '[name="dpdromania[cod_pay]"]', function (e) {
                e.preventDefault();
                var $el = $(this);
                if($el.val() =='client_number'){
                    $('#client_number_input').removeClass('hidden');
                }
                if($el.val() =='cash'){
                    $('#client_number_input').addClass('hidden');
                }
            });

        }());
        var $pickup = $("input[data-radio='pickup']");
        $pickup.change(function () {
            $("[data-toggle='pickup']").attr("disabled", "disabled");
            $("[data-toggle-item='pickup']").addClass('hidden');

            if ($pickup.filter(":checked").val()) {
                $("[data-toggle='pickup'][data-enable='" + $pickup.filter(":checked").val() + "']").removeAttr("disabled");
                $("[data-toggle-item='pickup'][data-enable='" + $pickup.filter(":checked").val() + "']").removeClass('hidden');
            }
        }).trigger("change");
        {if $error|default}
        toastr.error('{$error}');
        {/if}
    </script>
{/capture}