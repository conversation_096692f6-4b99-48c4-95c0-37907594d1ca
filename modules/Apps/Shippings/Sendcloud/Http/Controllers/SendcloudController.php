<?php

declare(strict_types=1);

namespace Modules\Apps\Shippings\Sendcloud\Http\Controllers;

use App\Common\DateTimeFormat;
use Exception;
use App\Exceptions\Error;
use App\Helper\Format;
use App\Helper\OmniShip\Address;
use App\Helper\SiteCp\Modal;
use App\Helper\YesNo;
use App\Locale\Country;
use App\Models\Apps\SiteRequiredSetting;
use App\Models\Order\Order;
use App\Models\Order\OrderFulfillment;
use App\Models\Router\Logs;
use App\Models\Shipping\ExternalShippingProviders;
use App\Models\Shipping\ShippingProvider;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\Apps\Shippings\Sendcloud\Http\Requests\PickupCpRequest;
use Modules\Apps\Shippings\Sendcloud\Http\Requests\SaveConfiguration;
use Modules\Apps\Shippings\Sendcloud\Manager;
use Modules\Apps\Shippings\Omniship\Http\Controllers\AbstractShippingController;
use Omniship\Common\Bill\Create;
use Omniship\Consts;
use Throwable;
use Illuminate\Support\Facades\View;

class SendcloudController extends AbstractShippingController
{
    /**
     * @var string
     */
    protected $locale;

    /**
     * @param Manager $manager
     * @throws Error
     */
    public function __construct(Manager $manager)
    {
        $this->locale = app()->getLocale();
        parent::__construct($manager);
    }


    /**
     * @param Request $request
     * @param $order_id
     * @return Response
     * @throws Error
     * @throws Throwable
     */
    public function calculate(Request $request, $order_id): \Illuminate\Http\Response
    {
        $order = Order::find($order_id);
        if (!$order) {
            return new Response([
                'status' => 'error',
                'msg' => __('order.status.error.order_not_found')
            ]);
        }

        $calculate = $this->recalculate($request, $order, true);
        $return = ['status' => 'error'];
        $error = $this->manager->getClient()->getError();
        if (!$error) {
            if ($calculate->count()) {
                $return['pickup'] = $request->input('waybill.pickup');
                $return['status'] = 'success';
                $return['calculate'] = $calculate;
            } else {
                $return['msg'] = __('sf.evropat.err.no_services_support');
            }
        } else {
            $return['msg'] = $error;
        }

        if (!empty($return['msg'])) {
            //$return['field'] = 'js-serices-place-holder';
        }

        return new Response($return);
    }

    /**
     * @return JsonResponse
     * @throws Error
     * @throws Throwable
     */
    public function disconnect()
    {
        $this->manager->removeSettings(['integration_id', 'secret_key', 'public_key', 'login_url', 'service_point_carriers']);
        return response()->json(['message' => 'Successfully disconnected']);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCourier(): JsonResponse
    {
        $getProviders = ShippingProvider::where('integration', $this->manager::APP_KEY)->whereNotNull('external_id')->pluck('external_id')->all();
        $getInstalled = ExternalShippingProviders::whereIn('id', $getProviders)->pluck('data', 'id')->all();
        $couriers = $this->manager->getContrtacts();
        $response = array_map(fn($courier): array => $this->format($courier, $getInstalled), $couriers ?? []);
        return response()->json($response);
    }


    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function installShipping(Request $request)
    {
        $id = (int)$request->input('id');
        $getCourier = $this->manager->getContract($id);
        if (!$getCourier) {
            return response()->json('Shipping not found', 404)
                ->setStatusCode(404);
        }

        $createProvider = ShippingProvider::create([
            'active' => 0,
            'name' => $getCourier['name'],
            'target' => 'restofworld',
            'integration' => $this->manager::APP_KEY,
            'type' => 'courier',
        ]);

        $createExternalShipping = ExternalShippingProviders::create([
            'record_id' => $createProvider->id,
            'record_type' => \App\Models\Shipping\ShippingProvider::class,
            'installed' => 1,
            'data' => $id
        ]);

        $createProvider->update([
            'external_id' => $createExternalShipping->id,
        ]);

        $getProviders = ShippingProvider::where('integration', $this->manager::APP_KEY)->whereNotNull('external_id')->pluck('external_id')->all();
        $getInstalled = ExternalShippingProviders::whereIn('id', $getProviders)->pluck('data', 'id')->all();

        return response()->json($this->format($getCourier, $getInstalled));
    }

    /**
     * @param $courier
     * @param $getInstalled
     * @return mixed
     */
    protected function format(array $courier, $getInstalled): array
    {
        if (in_array($courier['id'], $getInstalled)) {
            $external_id = array_search($courier['id'], $getInstalled, true);
        }

        $courier['is_installed'] = in_array($courier['id'], $getInstalled);
        $courier['external_id'] = $external_id ?? null;
        return $courier;
    }

    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     */
    public function subConfiguration($id)
    {
        $provider = ShippingProvider::where('external_id', $id)->first();
        if (!$provider) {
            return response()->json([], 404);
        }

        $this->manager->setProvider($provider);
        return parent::settings();
    }

    /**
     * @param ConfigurationRequest $request
     * @param $id
     * @return Response
     */
    public function SaveConfiguration(SaveConfiguration $request, $id): \Illuminate\Http\Response
    {
        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($request, $id): void {
                $post = $request->input('sendcloud', []);
                $this->manager->setProvider(ShippingProvider::where('external_id', $id)->first());
                $provider = $this->manager->getProvider();
                $provider->meta()->where('parameter', 'like', 'settings.%')->delete();
                if (!$this->manager->supportSyncPayments()) {
                    $post['sync_payments'] = 0;
                }


                foreach (array_filter($post) as $key => $value) {
                    $provider->meta()->create(['parameter' => 'settings.' . $key, 'value' => $value]);
                }

                $provider->edit([
                    'name' => $request->input('sendcloud.provider_name'),
                    'type' => null,
                    'target' => $request->input('sendcloud.target') ?: 'regions',
                    'geo_zone_id' => $request->input('sendcloud.geo_zone_id'),
                    'active' => (int)$request->input('sendcloud.status'),
                ]);

                $provider->attachRates($request->input('sendcloud', []));

                $provider->attachPayments(!$request->has('payments_all'), $request->input('payments_providers', []));

                try {
                    $provider->uploadImage();
                } catch (Error $error) {
                    throw new Error($error->getMessage(), 'image');
                }
            });

            try {
                SiteRequiredSetting::sync();
            } catch (Throwable) {
            }


            return new Response([
                'msg' => __('shipping.successfully_saved_settings'),
                'status' => 'success'
            ]);

        } catch (Throwable $throwable) {
            return new Response([
                'msg' => $throwable->getMessage(),
                'status' => 'error'
            ]);
        }
    }

    /**
     * @param $id
     * @param $is_json
     * @return array|JsonResponse
     */
    public function getSenderAddresses($id = null, $is_json = true)
    {
        $addresses = $this->manager->getSenderAddresses();
        $address_select = [];
        foreach ($addresses as $address) {
            if (!empty($id) && $id == $address->id) {
                return $is_json ? response()->json([
                    'id' => $address->id,
                    'name' => $address->company_name . ' (' . $address->street . ' ' . $address->house_number . ', ' . $address->city . ', ' . $address->postal_code . ')'
                ]) : [
                    'id' => $address->id,
                    'name' => $address->company_name . ' (' . $address->street . ' ' . $address->house_number . ', ' . $address->city . ', ' . $address->postal_code . ')'
                ];
            }

            $address_select[] = [
                'id' => $address->id,
                'name' => $address->company_name . ' (' . $address->street . ' ' . $address->house_number . ', ' . $address->city . ', ' . $address->postal_code . ')'
            ];
        }

        return $is_json ? response()->json($address_select) : $address_select;

    }

    /**
     * @return array
     * @throws Error
     */
    #[\Override]
    public function getMeta(): array
    {
        $provider = $this->manager->getProvider();
        if (is_null($provider) || !$provider->external_id) {
            return [];
        }

        $sender_id = $provider->meta->where('parameter', 'settings.sender_id')->first();
        if ($sender_id) {
            $settings['sender_id'] = $this->getSenderAddresses($sender_id->value, false);
        }

        if ($this->manager->isActiveSession()) {
            $services = $this->manager->getServices();
            $settings['address_services'] = $services ? $services->all() : [];

            $returnAddress = $this->manager->collectSetting('return_address');
            if ($returnAddress && !empty($returnAddress['from_country'])) {
                $country = Country::get($returnAddress['from_country']);
                $settings['from_country'] = ['id' => $returnAddress['from_country'], 'name' => $country['name']];
            }
        }

        return $settings ?? [];
    }

    /**
     * @param Illuminate\Http\Request $request
     * @param mixed $order_id
     * @return mixed
     */
    public function waybill(Request $request, $order_id)
    {
        $order = Order::with(['shipping', 'shippingAddress', 'products'])->find($order_id);

        if (!$order || $order->omniship_provider != $this->manager->getKey()) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        $this->manager->setProvider($order->shipping->provider);
        $this->manager->setOrder($order);

        $order->setAttribute('price_total_formatted', Format::moneyInput($order->price_total));
        $order->setAttribute('price_products_subtotal_formatted', Format::moneyInput($order->price_products_subtotal));
        $order->setAttribute('price_subtotal_formatted', Format::moneyInput($order->price_subtotal));

        $settings = $this->manager->collectSetting();
        $currency = site('currency');

        $calculate = $this->recalculate($request, $order, false, $params);

        $cash_on_delivery_amount = 0;
        if ($order->meta_pluck->get('cod')) {
            $cash_on_delivery_amount = $order->meta_pluck->get('cod_manual');
        } elseif ($order->isCashOnDelivery()) {
            if ($order->getPayerSide() == 'RECEIVER') {
                $cash_on_delivery_amount = moneyInput($order->getTotal(), $order->currency);
            } else {
                $cash_on_delivery_amount = $order->price_total_formatted;
            }
        }

        $orderContent = '';
        $package = 1;
        return \Illuminate\Support\Facades\View::modal(
            $this->manager::APP_KEY . '::waybill',
            [
            'support_types' => $this->manager->supportDeliveryType(),
            'calculate' => $calculate,
            'date_format' => DateTimeFormat::$date_formats[setting('date_format')]['format_js'],
            'send_date' => Carbon::now()->format(DateTimeFormat::$date_formats[setting('date_format')]['format']),
            'order' => $order,
            'order_id' => $order_id,
            'payed' => in_array($order->status, ['paid', 'completed']),
            'currency' => $currency,
            'settings' => $settings,
            'address' => $order->shippingAddress ? $order->shippingAddress->address : new Address(),
            'contents' => $order->content,
            'order_contents' => $orderContent,
            'side_sender' => in_array($this->manager->getPricingType(), ['fixed_price', 'fixed_weight']),
            'waybill_sides' => $order->getWaybillSides(),
            'side' => 'SENDER',
            'weight' => $this->manager->getWeightFromItems($this->manager->convertOrderProductsToBag($order->products), $this->manager->getDefaultWeight()),
            'cod_total' => $cash_on_delivery_amount,
            'allowed_services' => $this->manager->getServices(),
            'selected_services' => $order->shipping->service_id ?? null,
            'package_count' => $package,
            'totalInsurance' => $cash_on_delivery_amount,
            'addresses' => $this->getSenderAddresses(null, false),
            'customsShipmentType' => [
                '0' => __('sendcloud::sendcloud.gift'),
                '1' => __('sendcloud::sendcloud.document'),
                '2' => __('sendcloud::sendcloud.commercial_goods'),
                '3' => __('sendcloud::sendcloud.commercial_sample'),
                '4' => __('sendcloud::sendcloud.sample'),
            ],
            'return_address' => $this->manager->isValidReturnAddress()

        ],
            null,
            Modal::cancelButton()
            . Modal::button(__('shipping.generate_bill_of_lading'), 'btn-primary js-generate-bill-of-lading')
        );
    }

    /**
     * @param Order $order
     * @param Request $request
     * @param $save_address
     * @param $waybill
     * @return array
     * @throws Error
     */
    #[\Override]
    protected function initCalculateAndGenerate(Order $order, Request $request, $save_address = false, $waybill = false): array
    {
        $settings = $this->manager->collectSetting();
        if ($request->isMethod('post')) {
            $order->updateMeta([
                'contents' => $request->input('waybill.contents'),
                'weight' => $request->input('waybill.weight'),
                'items_count' => $request->input('waybill.items_count', 1),
                'customer_note' => $request->input('waybill.customer_note'),
                'side' => $request->input('waybill.side'),
                'package' => $request->input('waybill.package'),
                'open_package' => $request->input('waybill.allowShipmentCheck'),
                'cod' => $request->input('waybill.cod'),
                'cod_amount' => $request->input('waybill.total'),
                'insurance' => $request->input('waybill.insurance'),
                'insurance_amount' => $request->input('waybill.totalInsurance'),
                'sender_id' => $request->input('waybill.sender_id'),
                'service_id' => $request->input('waybill.service_id'),
                'package_type' => $request->input('waybill.package_type'),
                'return_waybill' => $request->input('waybill.return_waybill'),
            ]);
            $this->manager->setProvider($order->shipping->provider);

            $sender_address = $this->manager->setSenderAddress($request->input('waybill.sender_id'));
            $other_parameters = [
                'sender_id' => $request->input('waybill.sender_id'),
                'service_id' => $request->input('waybill.service_id'),
                'package_type' => $request->input('waybill.package_type'),
                'return_waybill' => $request->input('waybill.return_waybill'),
                'package_count' => $request->input('waybill.package'),
                'return_address' => $this->manager->collectSetting('return_address'),
                'email' => $order->customer_email
            ];
        } else {
            $sender_address = $this->manager->setSenderAddress($settings->get('settings.sender_id'));
        }

        if ($this->manager->collectSetting('insurance') != 1) {
            $params['insurance_amount'] = 0;
        }

        if ($order->meta_pluck->get('cod')) {
            $order->updateMeta(['cod_manual' => $request->input('waybill.total')]);
        } else {
            $order->removeMeta('cod_manual');
        }

        $receiver_address = $order->shippingAddress->address;
        $this->manager->setOrder($order);

        $cash_on_delivery_amount = 0;
        $insurance_amount = 0;
        if ($order->meta_pluck->get('cod_amount')) {
            $cash_on_delivery_amount = $order->meta_pluck->get('cod_amount');
        } elseif ($order->isCashOnDelivery()) {
            $cash_on_delivery_amount = Format::moneyInput($this->manager->getCashOnDeliveryTotal($order));
        }

        if ($order->meta_pluck->get('insurance')) {
            $insurance_amount = $order->meta_pluck->get('insurance_amount');
        }

        $params = [
            'content' => Str::substr($request->input('waybill.contents'), 0, 36),
            'client_note' => Str::substr($request->input('waybill.dsa'), 0, 200),
            'transaction_id' => $order->id,
            'items' => $this->manager->convertOrderToItemsBag($order),
            'receiver_address' => $receiver_address,
            'cash_on_delivery_amount' => $cash_on_delivery_amount,
            'insurance_amount' => $insurance_amount ?? 0,
            'company_id' => $request->input('eushipment.company_id') ?? null,
            'package' => $request->input('waybill.package') ?? null,
            'weight' => $request->input('waybill.weight') ?? $order->getWeight(),
            'fragile' => $request->input('waybill.breakable') ?? null,
            'sender_address' => $sender_address,
            'services' => $settings->get('settings.allowed_methods') ?? [],
            'contract_id' => $this->manager->getCourierByProvider($this->manager->getProvider()->external_id),
            'courier_id' => $order->shipping->provider->external_provider->data,
            'other_parameters' => $other_parameters ?? [],
            'service_id' => $request->isMethod('post') ? $request->input('waybill.service_id') : $order->shipping->service_id,
        ];
        return $params;
    }

    /**
     * @param Illuminate\Http\Request $request
     * @param mixed $order_id
     * @return mixed
     */
    public function waybillSave(Request $request, $order_id)
    {

        /** @var Order $order */
        $order = Order::with(['shipping', 'shippingAddress', 'products'])->find($order_id);
        if (!$order || $order->omniship_provider != $this->manager->getKey()) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        $this->manager->setOrder($order);

        $waybillStd = new \stdClass();
        $request_response = new \stdClass();
        $waybillStd->waybill_id = null;
        try {
            /** @var Create $waybill */
            $waybill = \Illuminate\Support\Facades\DB::transaction(function () use ($order, $request, $waybillStd, &$request_response) {
                $params = $this->initCalculateAndGenerate($order, $request);
                $result = $this->manager->createBillOfLading($params, $request_response);
                if (!$result) {
                    throw new Error($this->manager->getClient()->getError());
                } elseif ($result->getError()) {
                    throw new Error($result->getError());
                }

                $categories = $order->products->where('digital', YesNo::False)->pluck('category_id')->toArray();
                $result = $this->manager->formatCreate(Format::moneyInput($order->price_total - $order->shipping->order_amount), $result, null, $categories);

                $waybillStd->waybill_id = $result->getBolId();
                $order->updateMeta([
                    'integration' => $this->manager->getKey(),
                    'service_name' => $result->getServiceId(),
                    'pdf_url' => $result->getBillOfLadingUrl(),
                    'bol_id' => $waybillStd->waybill_id,
                    'total' => Format::toIntegerPrice($result->getTotal()),
                    'original_total' => $result->getParameter('original_price') != $result->getTotal() ? Format::toIntegerPrice($result->getParameter('original_price')) : null,
                    'currency' => $result->getCurrency(),
                    'omniship.credentials' => json_encode($this->manager->getCredentials()),
                    'money_transfer' => $this->manager->isMoneyTransfer(),
                    'sendcloud_system_id' => $result->getParameter('id'),
                    'tracking_link' => $result->getParameter('tracking'),
                ]);

                $GlobalSettings = $this->manager->collectSetting();


                if (!empty($GlobalSettings['settings.tracking'] && $GlobalSettings['settings.tracking'] == 'cloudcart')) {
                    $trackingUrl = \LinkerCp::siteFullLink('/tracking/' . $waybillStd->waybill_id);
                } else {
                    $trackingUrl = $result->getParameter('tracking');
                }

                $fulfilled = $order->changeStatus('fulfilled', true, [
                    'shipping_tracking_number' => $waybillStd->waybill_id,
                    'shipping_date_delivery' => $result->getEstimatedDeliveryDate(),
                    'shipping_date_expedition' => $result->getPickupDate(),
                    'shipping_tracking_url' => $trackingUrl
                ]);
                if ($fulfilled && $fulfilled instanceof OrderFulfillment) {
                    $fulfilled->update([
                        'shipping_tracking_number' => $waybillStd->waybill_id,
                        'shipping_date_delivery' => $result->getEstimatedDeliveryDate(),
                        'shipping_date_expedition' => $result->getPickupDate(),
                        'shipping_tracking_url' => $trackingUrl
                    ]);
                }

                if (!!request()->input('sync_order_and_provider_amount') && (!in_array($order->status, ['completed', 'paid']) || $order->meta_pluck->get('recalculate_locked', 0) == 0)) {
                    $shipping_provider = $order->initShippingQuote(null, [
                        'service_id' => $params['service_id'] ?? null,
                    ]);

                    $order->updateShippingFromInitQuote($shipping_provider);

                    $order->updateOrderTotals();
                }

                return $result;
            });

            $this->logWaybill($request_response, $order);
            return new Response(array_merge(['status' => 'success'], $waybill->toArray()));
        } catch (Exception $exception) {
            if (!empty($waybillStd->waybill_id)) {
                $this->manager->cancelBillOfLading($waybillStd->waybill_id);
            }

            if (!($exception instanceof Error)) {
                Logs::createFromThrowable($exception, 'SendCloud waybillSave');
            }

            $this->logWaybill($request_response, $order);

            return response([
                'status' => 'error',
                'field' => null,
                'msg' => $this->manager->translateError($exception->getMessage())
            ]);
        }
    }


    /**
     * @param Request $request
     * @param $order_id
     * @return mixed
     * @throws Error
     */
    public function pdfSelect(Request $request, $order_id)
    {
        /** @var Order $order */
        $order = Order::find($order_id);

        if (!$order || !$order->getBolId()) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        $method = $request->ajax() ? 'modal' : 'make';

        return View::$method(Manager::APP_KEY . '::pdf-select', [
            'order' => $order
        ], __('global.print'), false);
    }

    /**
     * @param $order_id
     * @param $type
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|Response
     * @throws Error
     */
    public function pdf($order_id, $type)
    {
        /** @var Order $order */
        $order = Order::find($order_id);
        $this->manager->isActiveSession();
        if (!$order || !$order->meta_pluck->get('sendcloud_system_id')) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        $this->manager->getManager()->setOtherParameters(['format' => $type]);
        $pdf = $this->manager->getPdf($order->meta_pluck->get('sendcloud_system_id'));

        if (!$pdf) {
            throw new Error($this->manager->getClient()->getError());
        }

        return response($pdf)
            ->header('content-type', 'application/pdf');
    }


    /**
     * @param Request $request
     * @param $type
     * @return Response
     * @throws Error
     */
    public function getOffices(Request $request, $type = null): \Illuminate\Http\Response
    {
        try {
            $offices = $this->manager->findOffices($request->query('query'), $request->query('destination_id'));
            if (in_array($type, [Consts::OFFICE_TYPE_APT, Consts::OFFICE_TYPE_OFFICE])) {
                /** @var Collection $offices */
                $offices = $offices->where('type', $type);
            }

            $return = ['results' => $this->_format($offices), 'more' => false];
        } catch (Exception $exception) {
            $return['error'] = $exception->getMessage();
        }

        return new Response($return);
    }

    /**
     * @param \Modules\Apps\Shippings\Sendcloud\Http\Requests\PickupCpRequest $request
     * @param mixed $order_id
     * @param mixed $type
     * @return mixed
     */
    public function changePickupSave(PickupCpRequest $request, $order_id, string $type)
    {
        return $this->changePickupSaveAbstract($request, $order_id, $type);
    }

}
