<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 9.1.2017 г.
 * Time: 10:18 ч.
 */

namespace Modules\Apps\Shippings\Speedy\Http\Controllers;

use App\Common\DateTimeFormat;
use App\Exceptions\Error;
use App\Helper\Format;
use App\Helper\OmniShip\Address;
use App\Helper\SiteCp\Modal;
use App\Helper\YesNo;
use App\Locale\Weight;
use App\Models\Apps\SiteRequiredSetting;
use App\Models\Gateway\Currency;
use App\Models\Order\Order;
use App\Models\Order\OrderFulfillment;
use App\Models\Router\Logs;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Str;
use Modules\Apps\Shippings\Omniship\Http\Controllers\AbstractShippingController;
use Modules\Apps\Shippings\Speedy\Http\Requests\CalculateCpRequest;
use Modules\Apps\Shippings\Speedy\Http\Requests\PickupCpRequest;
use Modules\Apps\Shippings\Speedy\Http\Requests\SaveConfiguration;
use Modules\Apps\Shippings\Speedy\Http\Requests\WaybillRequest;
use Modules\Apps\Shippings\Speedy\Manager;
use Omniship\Common\Bill\Create;
use Omniship\Consts;
use ReflectionException;
use Throwable;

class SpeedyController extends AbstractShippingController
{
    /**
     * @var string
     */
    protected $locale;

    /**
     * @param Manager $manager
     * @throws Error
     */
    public function __construct(\Modules\Apps\Shippings\Speedy\Manager $manager)
    {
        $this->locale = locale();
        parent::__construct($manager);
    }

    /**
     * @return array
     *
     * @throws Error
     */
    #[\Override]
    public function getSelectsOptions(): array
    {
        $values = parent::getSelectsOptions();

        $offices = [];
        $clients = [];
        $services = [];
        $active_session = $this->manager->isActiveSession();
        if ($active_session) {
            $clients = $this->manager->getClient()->listContractClients();
            $clients = array_map(function (\ResultClientData $clientData): array {
                $address = $clientData->getAddress();
                $address_string = $address->getSiteType()
                    .$address->getSiteName().', '
                    .$address->getRegionName().', '
                    .$address->getStreetType()
                    .$address->getStreetName().' '
                    .$address->getPostCode();

                $name = [];
                if (!empty($clientData->getPartnerName())) {
                    $name[] = $clientData->getPartnerName();
                }

                if (!empty($clientData->getObjectName())) {
                    $name[] = $clientData->getObjectName();
                }

                return [
                    'id' => (string) $clientData->getClientId(),
                    'name' => implode(', ', $name).' ('.$address_string.')',
                ];
            }, $clients);

            if ($this->manager->getSetting('pickup') == 'office' && $this->manager->getSetting('office_id')) {
                $offices = $this->manager->getOfficeById($this->manager->getSetting('office_id'));
                $offices = $offices ? [[
                    'id' => $offices->getId(),
                    'name' => implode(', ', array_filter([$offices->getName(), $offices->getAddressString()])),
                ]] : [];
            }
        }

        return array_merge($values, [
            'clients' => $clients,
            'offices' => $offices,
        ]);
    }

    /**
     * @return array
     *
     * @throws Error
     */
    #[\Override]
    public function getMeta(): array
    {
        $options_before_payment = [
            'no_option' => __('No options'),
            'test' => __('Test before payement'),
            'open' => __('Open before payment'),
        ];

        $provider = $this->manager->getProvider();

        $special_delivery_requirements = collect(
            $this->manager->getClient()->getListSpecialDeliveryRequirements()
        )->map(fn(\ResultSpecialDeliveryRequirement $option): array => [
            'id' => $option->getSpecialDeliveryId(),
            'name' => $option->getSpecialDeliveryText(),
            'price' => Format::money(
                Format::toIntegerPrice($option->getSpecialDeliveryPrice()),
                true,
                'BGN'
            ),
        ]);

        $active_session = $this->manager->isActiveSession();

        if ($active_session) {
            $clients = $this->manager->getClient()->listContractClients();
        } else {
            $clients = [];
        }

        $clients = array_map(function (\ResultClientData $clientData): array {
            $address = $clientData->getAddress();
            $address_string = $address->getSiteType()
                .$address->getSiteName().', '
                .$address->getRegionName().', '
                .$address->getStreetType()
                .$address->getStreetName().' '
                .$address->getPostCode();

            $name = [];
            if (!empty($clientData->getPartnerName())) {
                $name[] = $clientData->getPartnerName();
            }

            if (!empty($clientData->getObjectName())) {
                $name[] = $clientData->getObjectName();
            }

            return [
                'id' => (string) $clientData->getClientId(),
                'name' => implode(', ', $name).' ('.$address_string.')',
            ];
        }, $clients);

        $offices = [];
        if ($this->manager->getSetting('pickup') == 'office' && $this->manager->getSetting('office_id')) {
            try {
                $offices = $this->manager->getOfficeById($this->manager->getSetting('office_id'));
                $offices = $offices ? [$offices] : [];
            } catch (Throwable) {
                //
            }
        }

        $settings = [
            'boxes' => json_encode($provider->boxes()->get(['boxes.id', 'boxes.name'])),
            'option_before_payment' => $options_before_payment,
            'available_money_transfer' => $this->manager->isAvailableMoneyTransfer(),
            'special_delivery_requirements' => $special_delivery_requirements,
            'post_boxes' => $this->manager->getSetting('post_boxes', []),
            'clients' => $clients,
            'offices' => $offices,
        ];

        return $settings;
    }

    #[\Override]
    protected function __settings(): array
    {
        $settings = parent::__settings();
        if (empty($settings['settings']['client_id'])) {
            $settings['settings']['client_id'] = $settings['inputs']['clients'][0]['id'] ?? null;
        }

        return $settings;
    }

    /**
     * @param  SaveConfiguration  $request
     * @return \Illuminate\Http\JsonResponse
     *
     * @throws Throwable
     */
    public function saveConfig(SaveConfiguration $request)
    {
        try {
            \Illuminate\Support\Facades\DB::transaction(function () use ($request): void {
                $this->manager->emptySettings();
                $post = $request->input('speedy', []);
                if (!empty($post['post_boxes'])) {
                    $tmp = [];
                    foreach ($post['post_boxes']['to'] as $row => $weight) {
                        $box = $post['post_boxes']['type'][$row];
                        if ($box && in_array($box, ['XS', 'S', 'M', 'L']) && $weight > 0) {
                            $tmp[] = [
                                'weight' => number_format($weight, 2, '.', ''),
                                'type' => $box,
                            ];
                        }
                    }

                    $post['post_boxes'] = collect($tmp)->keyBy('type')->values()->all();
                } else {
                    $post['post_boxes'] = [];
                }

                $post = $this->fixAddress($post);
                if (!$this->manager->supportSyncPayments()) {
                    $post['sync_payments'] = 0;
                }

                $this->manager->updateSettings($post);
                $this->manager->updateActive((int) $request->input('speedy.status'));

                $provider = $this->manager->getProvider();

                $provider->edit([
                    'name' => $request->input('speedy.provider_name'),
                    'type' => null,
                    'target' => ($target = ($request->input('speedy.target') ?: 'regions')),
                    'geo_zone_id' => $target == 'regions' ? $request->input('speedy.geo_zone_id') : null,
                    'active' => (int) $request->input('speedy.status'),
                ]);

                $provider->attachRates($request->input('speedy', []));
                $provider->attachBoxes($request->input('speedy.boxes', []));
                $provider->attachPayments(!$request->has('payments_all'), $request->input('payments_providers', []));
            });

            try {
                SiteRequiredSetting::sync();
            } catch (Throwable) {
            }

            if ($this->manager->getSetting('sync_payments')) {
                \OmniShip::startSyncPayments();
            }

            $this->manager->getProvider()->uploadImageSplFileInfo($request->file('image'));

            return $this->settings();
        } catch (Exception $exception) {
            return response()->json([
                'message' => $exception->getMessage(),
            ], 503);
        }
    }

    /**
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     *
     * @throws Error
     */
    public function services(Request $request)
    {
        $filter = (array) $request->query('service');
        $services = collect($this->manager->getServices()->toArray())->pluck('name', 'id')->all();
        if ($filter) {
            $return['services'] = array_intersect_key($services, array_flip($filter));
        } else {
            $return['services'] = $services;
        }

        return response()->json($return['services']);
    }

    /**
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCountries(Request $request)
    {
        try {
            $countries = $this->manager->findCountries($request->query('query'));
            $return = $this->_format($countries);
        } catch (Exception $exception) {
            $return['error'] = $exception->getMessage();
        }

        return response()->json($return);
    }

    /**
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStreets(Request $request)
    {
        try {
            $streets = $this->manager->findStreets($request->query('query'), $request->query('city_id'));
            $return = $this->_format($streets);
        } catch (Exception $exception) {
            $return['error'] = $exception->getMessage();
        }

        return response()->json($return);
    }

    /**
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCities(Request $request)
    {
        try {
            $cities = $this->manager->findCities($request->query('query'), $request->query('country_id', 100));
            $return = $this->_format($cities);
        } catch (Exception $exception) {
            $return['error'] = $exception->getMessage();
        }

        return response()->json($return);
    }

    /**
     * @param  Request  $request
     * @param  $type
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOffices(Request $request, $type = null)
    {
        try {
            if ($request->input('id')) {
                $offices = $this->manager->getOfficeById($request->input('id'));
                $offices = collect($offices ? [$offices] : []);
            } else {
                $offices = $this->manager->findOffices($request->query('query'), $request->query('city_id'));
            }

            if (in_array($type, [Consts::OFFICE_TYPE_APT, Consts::OFFICE_TYPE_OFFICE])) {
                /** @var Collection $offices */
                $offices = $offices->where('type', $type);
            }

            $return = $this->_format($offices);
        } catch (Exception $exception) {
            $return['error'] = $exception->getMessage();
        }

        return response()->json($return);
    }

    /**
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getQuarters(Request $request)
    {
        try {
            $quarters = $this->manager->findQuarters($request->query('query'), $request->query('city_id'));
            $return = $this->_format($quarters);
        } catch (Exception $exception) {
            $return['error'] = $exception->getMessage();
        }

        return response()->json($return);
    }

    /**
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStates(Request $request)
    {
        try {
            $states = $this->manager->findStates($request->query('query'), $request->query('country_id'));
            $return = $this->_format($states);
        } catch (Exception $exception) {
            $return['error'] = $exception->getMessage();
        }

        return response()->json($return);
    }

    /**
     * @param  CalculateCpRequest  $request
     * @param  $order_id
     * @return Response
     *
     * @throws Error
     * @throws Throwable
     */
    public function calculate(CalculateCpRequest $request, $order_id): \Illuminate\Http\Response
    {
        $order = Order::find($order_id);
        if (!$order) {
            return new Response([
                'status' => 'error',
                'msg' => __('order.status.error.order_not_found'),
            ]);
        }

        $calculate = $this->recalculate($request, $order, true);

        $return = ['status' => 'error'];
        $error = $this->manager->getClient()->getError();
        if (!$error) {
            if ($calculate->count()) {
                $return['pickup'] = $request->input('waybill.pickup');
                $return['status'] = 'success';
                $return['calculate'] = $calculate;
            } else {
                $return['msg'] = __('sf.speedy.err.no_services_support');
            }
        } else {
            $return['msg'] = $error;
        }

        if (!empty($return['msg'])) {
            // $return['field'] = 'js-serices-place-holder';
        }

        return new Response($return);
    }

    /**
     * Когато използвате наложен платеж за Румъния, подавате в стойността на наложения платеж сумата, конвертирана от вас в съответната валута.
     * Ако искате да изпратите в Румъния нещо което струва 100 лева, то за сума на наложения платеж подавате равностойността им в румънски леи (сумата която вие искате куриера да събере от получателя).
     * В случая ангажимент на подателя е да каже колко ще са тези румънски леи.
     *
     * Същото важи и за използването на Наложен платеж за Гърция, само че там валутата е евро.
     *
     * Когато изпращате за чужбина платеца винаги е подателя.
     * Във върнатия резултат само стойността на <codBase> е във съответната валута а другите цени са в лева, понеже се събират от подателя, който е в България.
     *
     * @param  Request  $request
     * @param  $order_id
     * @return Response
     *
     * @throws Error
     * @throws ReflectionException
     * @throws Throwable
     */
    public function waybill(Request $request, $order_id)
    {

        /** @var Order $order */
        $order = Order::allOrderData()->find($order_id);

        if (!$order || ($order->omniship_provider != $this->manager->getKey())) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        $options_before_payment = [
            'no_option' => __('No options'),
            'test' => __('Test before payement'),
            'open' => __('Open before payment'),
        ];

        $weight_unit = Weight::getSignMetric();
        $weight_unit_short = Weight::getSignShort();
        $order->setAttribute('price_total_formatted', Format::moneyInput($order->price_total));
        $order->setAttribute('price_products_subtotal_formatted', Format::moneyInput($order->price_products_subtotal));
        $order->setAttribute('price_subtotal_formatted', Format::moneyInput($order->price_total - ($order->shipping ? $order->shipping->order_amount : 0)));

        $settings = $this->manager->getSettings();
        $currency = site('currency');

        $address = $order->shippingAddress ? $order->shippingAddress->address : new Address();
        if ($address->getCountry() && $address->getCountry()->getId() != 100) {
            $currency = 'BGN';
            $order->setAttribute(
                'price_total_formatted',
                Format::moneyInput(ceil(Currency::convert($order->price_total, site('currency'), $currency)))
            );
            $order->setAttribute(
                'price_products_subtotal_formatted',
                Format::moneyInput(ceil(Currency::convert($order->price_products_subtotal, site('currency'), $currency)))
            );
            $order->setAttribute(
                'price_subtotal_formatted',
                Format::moneyInput(ceil(Currency::convert($order->price_subtotal, site('currency'), $currency)))
            );
        }

        $price = Format::moneyInput($order->price_subtotal);
        $price_formatted = Format::money($order->price_subtotal);

        $calculate = $this->recalculate($request, $order, false, $params);

        $special_delivery_requirements = collect($this->manager->getListSpecialDeliveryRequirements() ?: [])->filter()->map(fn(\ResultSpecialDeliveryRequirement $r): array => $r->toArray())->pluck('special_delivery_text', 'special_delivery_id');

        $isApt = false;
        if (
            ($order->shippingAddress->office_id ?? false) &&
            ($office = $this->manager->getOfficeById($order->shippingAddress->office_id))
        ) {
            $isApt = $office->getType() == Consts::OFFICE_TYPE_APT;
        }

        if ($this->manager->isActiveSession()) {
            $clients = $this->manager->getClient()->listContractClients();
        } else {
            $clients = [];
        }

        $clients = array_map(function (\ResultClientData $clientData): array {
            $address = $clientData->getAddress();
            $address_string = $address->getSiteType()
                .$address->getSiteName().', '
                .$address->getRegionName().', '
                .$address->getStreetType()
                .$address->getStreetName().' '
                .$address->getPostCode();

            $name = [];
            if (!empty($clientData->getPartnerName())) {
                $name[] = $clientData->getPartnerName();
            }

            if (!empty($clientData->getObjectName())) {
                $name[] = $clientData->getObjectName();
            }

            return [
                'id' => (string) $clientData->getClientId(),
                'name' => implode(', ', $name).' ('.$address_string.')',
            ];
        }, $clients);

        $send_date = optional($params['shipment_date'])->format(DateTimeFormat::$date_formats[setting('date_format')]['format']);

        if (Carbon::now()->format('H:i') < 17 && !Carbon::now()->isWeekend() && $send_date != Carbon::now()->format('d.m.Y')) {
            $send_date = Carbon::now()->format('d.m.Y');
        }

        $itemContent = [];
        if ($order->shipping->service_id == 704) {
            $order->meta_pluck->put('items_count', $order->products->sum('quantity'));

            foreach ($order->products as $product) {
                $quantity = $product->quantity;
                do {
                    $itemContent[] = [
                        'width' => $this->manager->getSetting('default_width') / 10,
                        'height' => $this->manager->getSetting('default_height') / 10,
                        'depth' => $this->manager->getSetting('default_depth') / 10,
                        'weight' => !is_null($product->weight) ? number_format($product->weight / 1000, 3, '.', '') : $this->manager->getSetting('default_weight'),
                    ];
                } while (--$quantity > 0);
            }

            $order->meta_pluck->put('custom_pieces', true);
            $order->meta_pluck->put('is_tire_service', true);
        }

        return \Illuminate\Support\Facades\View::modal($this->manager::APP_KEY.'::waybill', [
            'address' => $address,
            'shipping_address' => $order->shippingAddress,
            'date_format' => DateTimeFormat::$date_formats[setting('date_format')]['format_js'],
            'send_date' => $send_date,
            'order' => $order,
            'order_id' => $order_id,
            'payed' => in_array($order->status, ['paid', 'completed']),
            'boxes' => json_decode((string) $order->meta_pluck->get('boxes')) ?? [],
            'shipping' => $order->shipping,
            'weight_unit' => $weight_unit,
            'weight_unit_short' => $weight_unit_short,
            'price' => $price,
            'price_formatted' => $price_formatted,
            'weight' => $this->manager->getLatestRequest() ? $this->manager->getLatestRequest()->getWeight() : $this->manager->getWeightFromItems($this->manager->convertOrderProductsToBag($order->products), $this->manager->getDefaultWeight()),
            'items_count' => $params['items']->sum('quantity'),
            'special_delivery_requirements' => $special_delivery_requirements,
            'settings' => $settings,
            'contents' => __('omniship.waybill.enter.contents', ['order_id' => $order->order_number], $this->manager->getManager()->getLanguageCode()),
            'options_before_payment' => $options_before_payment,
            'currency' => $currency,
            'calculate' => $calculate,
            'customer_note' => $order->content,
            'cash_on_delivery_amount' => $params['cash_on_delivery_amount'],
            'insurance_amount' => $params['insurance_amount'],
            'waybill_sides' => $order->getWaybillSides(),
            'side' => $order->getWaybillSide(),
            'isApt' => $isApt,
            'clients' => $clients,
            'item_content' => $itemContent,
        ], null, Modal::cancelButton().Modal::button(__('shipping.recalculate'), 'btn-default js-btn-calculate')
            .Modal::button(__('shipping.generate_bill_of_lading'), 'btn-primary js-generate-bill-of-lading'));
    }

    /**
     * @param  WaybillRequest  $request
     * @param  $order_id
     * @return Response
     *
     * @throws Error
     * @throws Throwable
     */
    public function waybillSave(WaybillRequest $request, $order_id)
    {

        /** @var Order $order */
        $order = Order::allOrderData()->find($order_id);

        if (!$order || ($order->omniship_provider != $this->manager->getKey())) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        try {
            $this->manager->setOrder($order);
        } catch (Throwable) {
            //
        }

        $waybillStd = new \stdClass();
        $waybillStd->waybill_id = null;

        $request_response = new \stdClass();
        try {
            /** @var Create $waybill */
            $waybill = \Illuminate\Support\Facades\DB::transaction(function () use ($order, $request, $waybillStd, &$request_response) {
                $params = $this->initCalculateAndGenerate($order, $request, true);

                $startTime = microtime(true);
                $result = $this->manager->createBillOfLading($params, $request_response);
                $total_time = microtime(true) - $startTime;
                if (!$result) {
                    throw new Error($this->manager->getClient()->getError());
                } elseif ($result->getError()) {
                    throw new Error($result->getError());
                }

                $categories = $order->products->where('digital', YesNo::False)->pluck('category_id')->toArray();
                $result = $this->manager->formatCreate(Format::moneyInput($order->price_total - $order->shipping->order_amount), $result, null, $categories);

                $waybillStd->waybill_id = $result->getBolId();

                $services = collect($this->manager->getServices()->toArray())->pluck('name', 'id');
                $order->updateMeta([
                    'process_time' => $total_time,
                    'integration' => $this->manager->getKey(),
                    'service_id' => $request->input('waybill.service_id'),
                    'service_name' => $services->get($request->input('waybill.service_id')),
                    'bol_id' => $waybillStd->waybill_id,
                    'total' => Format::toIntegerPrice($result->getTotal()),
                    'original_total' => $result->getParameter('original_price') != $result->getTotal() ? Format::toIntegerPrice($result->getParameter('original_price')) : null,
                    'currency' => $result->getCurrency(),
                    'special_delivery_requirements' => $request->input('waybill.special_delivery_requirements'),
                    'cod' => $request->input('waybill.cod'),
                    'option_before_payment' => $request->input('waybill.option_before_payment'),
                    'insurance' => $request->input('waybill.insurance'),
                    'fragile' => $request->input('waybill.fragile'),
                    'side' => $params['payer'],
                    'omniship.credentials' => json_encode($this->manager->getCredentials()),
                    'money_transfer' => $this->manager->isMoneyTransfer(),
                ]);

                $fulfilled = $order->changeStatus('fulfilled', true, [
                    'shipping_tracking_number' => $waybillStd->waybill_id,
                    'shipping_date_delivery' => $result->getEstimatedDeliveryDate(),
                    'shipping_date_expedition' => $result->getPickupDate(),
                    'shipping_tracking_url' => $this->manager->getSetting('tracking', 'provider') == 'cloudcart' ? \LinkerCp::siteFullLink('/tracking/'.$waybillStd->waybill_id) : $this->manager->trackingUrl($waybillStd->waybill_id),
                ]);
                if ($fulfilled && $fulfilled instanceof OrderFulfillment) {
                    $fulfilled->update([
                        'shipping_tracking_number' => $waybillStd->waybill_id,
                        'shipping_date_delivery' => $result->getEstimatedDeliveryDate(),
                        'shipping_date_expedition' => $result->getPickupDate(),
                        'shipping_tracking_url' => $this->manager->getSetting('tracking', 'provider') == 'cloudcart' ? \LinkerCp::siteFullLink('/tracking/'.$waybillStd->waybill_id) : $this->manager->trackingUrl($waybillStd->waybill_id),
                    ]);
                }

                if ((bool) request()->input('sync_order_and_provider_amount') && (!in_array($order->status, ['completed', 'paid']) || $order->meta_pluck->get('recalculate_locked', 0) == 0)) {
                    $shipping_provider = $order->initShippingQuote(null, [
                        'service_id' => $params['service_id'],
                        'overwrite_price' => true,
                        'price' => Format::toIntegerPrice($result->getTotal()),
                    ]);

                    $order->updateShippingFromInitQuote($shipping_provider);

                    $order->updateOrderTotals();
                }

                return $result;
            });

            $this->logWaybill($request_response, $order);

            return response(array_merge(['status' => 'success'], $waybill->toArray()));
        } catch (Exception $exception) {
            if (!empty($waybillStd->waybill_id)) {
                $this->manager->cancelBillOfLading($waybillStd->waybill_id);
            }

            if (!($exception instanceof Error)) {
                Logs::createFromThrowable($exception, 'Speedy waybillSave');
            }

            $this->logWaybill($request_response, $order);

            return response([
                'status' => 'error',
                'field' => null,
                'msg' => $this->manager->translateError($exception->getMessage()),
            ]);
        }
    }

    /**
     * @param  Request  $request
     * @param  $order_id
     * @return mixed
     *
     * @throws Error
     */
    public function pdfSelect(Request $request, $order_id)
    {

        /** @var Order $order */
        $order = Order::find($order_id);

        if (!$order || !$order->getBolId()) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        $method = $request->ajax() ? 'modal' : 'make';

        return View::$method(Manager::APP_KEY.'::pdf-select', [
            'order' => $order,
        ], __('global.print'), false);

    }

    /**
     * @param  $order_id
     * @param  $type
     * @return Response
     *
     * @throws Error
     */
    public function pdf($order_id, $type)
    {

        /** @var Order $order */
        $order = Order::find($order_id);

        if (!$order || !$order->getBolId()) {
            throw new Error(__('order.err.order_no_longer_exists'));
        }

        $this->manager->getManager()->setOtherParameters('printer_type', $type);
        $pdf = $this->manager->getPdf($order->getBolId());
        if (!$pdf) {
            throw new Error($this->manager->getClient()->getError());
        }

        return response($pdf)
            ->header('content-type', 'application/pdf');
    }

    /**
     * {@inheritDoc}
     */
    #[\Override]
    protected function initCalculateAndGenerate(Order $order, Request $request, $save_address = false, $waybill = false): array
    {
        $address = $order->shippingAddress;
        //        if ($request->input('waybill.pickup') != 'office') {
        //            $address->office = null;
        //        } else {
        //            $address->office = $request->input('waybill.address.office');
        //        }
        //        if ($save_address) {
        //            $address->save();
        //        }

        if (!$this->manager->getOrder()) {
            $this->manager->setOrder($order);
        }

        if ($request->isMethod('post')) {
            $order->updateMeta([
                'contents' => $request->input('waybill.contents'),
                'weight' => $request->input('waybill.weight'),
                'packing' => $request->input('waybill.packing'),
                'items_count' => $request->input('waybill.items_count', 1),
                'service_id' => $request->input('waybill.service_id'),
                'customer_note' => $request->input('waybill.customer_note'),
                'package' => json_encode($request->input('waybill.package')),
                'special_delivery_requirements' => $request->input('waybill.special_delivery_requirements'),
                'option_before_payment' => $request->input('waybill.option_before_payment'),
                'instruction_returns' => $request->input('waybill.instruction_returns'),
                'insurance' => $request->input('waybill.insurance'),
                'fragile' => $request->input('waybill.fragile'),
                'insurance_amount' => $request->input('waybill.totalInsurance'),
                'back_documents' => $request->input('waybill.back_documents'),
                'package_id' => $request->input('waybill.package_id'),
                'documents' => $request->input('waybill.documents'),
                'cod' => $request->input('waybill.cod'),
                'side' => $request->input('waybill.side'),
                'custom_pieces' => json_encode($request->input('custom_pieces')),
            ]);
            if ($order->meta_pluck->get('cod')) {
                $order->updateMeta(['cod_manual' => $request->input('waybill.total')]);
            } else {
                $order->removeMeta('cod_manual');
            }
        }

        if (!$request->isMethod('post')) {
            $allowed_services = (array) $this->manager->getSetting('allowed_methods');
            $service_id = $order->meta_pluck->get('service_id');
            if (!in_array($service_id, $allowed_services)) {
                $service_id = Arr::first($allowed_services);
            }

            $firs_allowed_tacking_day = $this->manager->getClient()->getFirstAllowedDaysForTaking($service_id);

            $priority_time = $order->meta_pluck->get('delivery_hour') ? Carbon::createFromFormat('Hi', sprintf('%02d', $order->meta_pluck->get('delivery_hour')).sprintf('%02d', $order->meta_pluck->get('delivery_time', '00'))) : null;
            $insurance_amount = $order->meta_pluck->get('insurance') ? $order->getInsuranceAmountInput() : 0;
            //            $cash_on_delivery_amount = $order->isCashOnDelivery() ? Format::moneyInput($this->manager->getCashOnDeliveryTotal($order)) : 0;
        } else {
            $firs_allowed_tacking_day = Carbon::createFromFormat(DateTimeFormat::$date_formats[setting('date_format')]['format'], $request->input('send_date'));
            $priority_time = $request->input('waybill.fixed_time') ? Carbon::createFromFormat('Hi', $request->input('waybill.fixed_time')) : null;
            $insurance_amount = $request->input('waybill.insurance') ? $request->input('waybill.totalInsurance') : 0;
            //            $cash_on_delivery_amount = ($order->isCashOnDelivery() ? ($order->meta_pluck->get('cod') && $order->meta_pluck->get('cod_manual') > 0 ? $order->meta_pluck->get('cod_manual') : Format::moneyInput($this->manager->getCashOnDeliveryTotal($order))) : 0);
        }

        $cash_on_delivery_amount = 0;
        if ($order->meta_pluck->get('cod')) {
            $cash_on_delivery_amount = $order->meta_pluck->get('cod_manual');
        } elseif ($order->isCashOnDelivery()) {
            $cash_on_delivery_amount = Format::moneyInput($this->manager->getCashOnDeliveryTotal($order));
        }

        $params = [
            'shipment_date' => $firs_allowed_tacking_day,
            'transaction_id' => $order->id, // create
            'service_id' => $request->isMethod('post') ? $request->input('waybill.service_id') : $order->meta_pluck->get('service_id'), // create
            //  $parameters['other_parameters']['sender_id']
            'client_note' => Str::substr(str_replace(['%', '&'], '', strval($request->input('waybill.customer_note'))), 0, 200), // create
            'content' => Str::substr($request->input('waybill.contents'), 0, 100), // create
            'receiver_email' => $order->customer_email,
            'receiver_address' => $address->address,
//            'sender_address' => $this->manager->getSenderAddress(),
            'items' => ($items = $this->manager->convertOrderProductsToBag($order->products)),
            'number_of_pieces' => $request->input('waybill.items_count') ?: 1,
            'payer' => $order->meta_pluck->get('side', $this->manager->getPayerSide()),
            'priority_time' => $priority_time,
            'other_parameters' => [
                'fragile' => $request->method() == 'POST' ? $request->input('waybill.fragile') : $order->meta_pluck->get('fragile'),
                'special_delivery_id' => $request->method() == 'POST' ? $request->input('waybill.special_delivery_requirements') : $order->meta_pluck->get('special_delivery_requirements'),
                'sender_id' => $request->isMethod('post') ? $request->input('speedy.client_id') : $this->manager->getSetting('client_id'),
            ],
            'package_id' => $request->input('waybill.package_id'), // nomer plik create
            'insurance_amount' => $insurance_amount,
            'cash_on_delivery_amount' => $cash_on_delivery_amount,
            'weight' => $request->method() == 'POST' ? $request->input('waybill.weight') : ($this->manager->getWeightFromBoxes($order) ?: $this->manager->getWeightFromItems($items, Weight::input($order->meta_pluck->get('default_weight')))),
            'package_type' => $request->method() == 'POST' ? $request->input('waybill.packing') : $this->manager->getSetting('packing'),
            'back_documents' => $request->method() == 'POST' ? $request->input('waybill.back_documents', 0) : $this->manager->getSetting('back_documents'),
            'is_documents' => $request->method() == 'POST' ? $request->input('waybill.documents') : (bool) $this->manager->getSetting('documents'),
            'pieces' => array_map(function ($data) {
                if (!empty($data->name)) {
                    unset($data->name);
                }

                return $data;
            }, (array) json_decode((string) $order->meta_pluck->get('boxes'))),
        ];

        if ($params['number_of_pieces'] > 1) {
            $params['pieces'] = $request->input('custom_pieces');
        }

        if ($request->isMethod('post')) {
            if (!empty($instruction_returns = $request->input('waybill.instruction_returns'))) {
                $params['instruction_returns'] = $instruction_returns;
            } else {
                $params['instruction_returns'] = '';
            }
        }

        if ($request->isMethod('post')) {
            if (in_array($option = $request->input('waybill.option_before_payment'), [Consts::OPTION_BEFORE_PAYMENT_OPEN, Consts::OPTION_BEFORE_PAYMENT_TEST])) {
                $params['option_before_payment'] = $option;
            } else {
                $params['option_before_payment'] = false;
            }
        } elseif (!is_null($obp = $this->manager->getOptionBeforePayment())) {
            $params['option_before_payment'] = $obp;
        }

        if (!$this->manager->allowOptionBeforePayment($address->address)) {
            $params['option_before_payment'] = false;
        }

        //        if(!activeRoute('apps.speedy.waybill.save') && $order->meta_pluck->get('rate') && $order->meta_pluck->get('service_id')) {
        //            $params['allowed_services'] = [$order->meta_pluck->get('service_id')];
        //        }

        if (isset($params['pieces'])) {
            $params['pieces'] = array_map(function ($piece) {
                if (isset($piece->items)) {
                    $piece->items = array_map(function ($item) {
                        $item->weight = Weight::input($item->weight);

                        return $item;
                    }, $piece->items);
                }

                return $piece;
            }, $params['pieces']);
        }

        return $params;
    }

    /**
     * @param  $post
     * @return mixed
     *
     * @throws Error
     */
    private function fixAddress($post)
    {
        if ($post['pickup'] == 'office') {
            if ($office = $this->manager->getOfficeById($post['office_id'])) {
                $post['office_id'] = $office->getId();
                $post['office_name'] = $office->getName().' ('.$office->getAddressString().')';
                $post['post_code'] = $office->getCity()->getPostCode();
                $post['country_id'] = $office->getCountryId();
                $post['country_name'] = $office->getCountry()->getName();
                $post['country_iso2'] = $office->getCountry()->getIso2();
                $post['country_iso3'] = $office->getCountry()->getIso3();
                $post['city_id'] = $office->getCityId();
                $post['city_name'] = $office->getCity()->getName();
            }
        } else {
            if (isset($post['office_id'])) {
                unset($post['office_id']);
            }

            $info = $this->manager->getClient()->getClientInfo($post['client_id']);
            if ($info) {
                $address = $info->getAddress();
                $post['post_code'] = $address->getPostCode();
                $post['country_id'] = $address->getCountryId();
                if ($country = $this->manager->getCountryById($address->getCountryId())) {
                    $post['country_name'] = $country->getName();
                    $post['country_iso2'] = $country->getIso2();
                    $post['country_iso3'] = $country->getIso3();
                }

                $post['city_id'] = $address->getSiteId();
                $post['city_name'] = $address->getSiteName();
            }
        }

        return $post;
    }

    /**
     * @param  PickupCpRequest  $request
     * @param  $order_id
     * @param  $type
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|Response
     *
     * @throws Error
     */
    public function changePickupSave(PickupCpRequest $request, $order_id, string $type)
    {
        return $this->changePickupSaveAbstract($request, $order_id, $type);
    }

    /**
     * @param  Request  $request
     * @param  $type
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|Response
     */
    public function getLockers(Request $request, $type = null): \Illuminate\Http\Response
    {
        try {
            $offices = $this->manager->findLockers($request->query('query'), $request->query('city_id'));
            if (in_array($type, [Consts::OFFICE_TYPE_APT, Consts::OFFICE_TYPE_OFFICE])) {
                /** @var Collection $offices */
                $offices = $offices->where('type', $type);
            }

            $return = ['results' => $this->_format($offices), 'more' => false];
        } catch (Exception $exception) {
            $return['error'] = $exception->getMessage();
        }

        return new Response($return);
    }

    /**
     * @param  Request  $request
     * @param  $type
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|Response
     */
    public function getOfficesOld(Request $request): \Illuminate\Http\Response
    {
        try {
            $offices = $this->manager->findOffices($request->query('query'), $request->query('city_id'));
            /** @var Collection $offices */
            $offices = $offices->values();
            $return = ['results' => $offices, 'more' => false];
        } catch (\Exception $exception) {
            $return['error'] = $exception->getMessage();
        }

        return new Response($return);
    }
}
