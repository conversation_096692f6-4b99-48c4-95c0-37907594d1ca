<div class="order-shipping-details-box" data-box-ajax="{route('apps.speedy.waybill', $order->id)}">
    <form action="{route('apps.speedy.waybill', $order_id)}" id="issueWaybill" role="form" class="ajaxForm">
        {if $order->meta_pluck->get('delivery_hour')}
            <input id="speedy_taking_date" type="hidden" name="waybill[fixed_time]"
                   value="{$order->meta_pluck->get('delivery_hour')}{$order->meta_pluck->get('delivery_time','00')}"/>
        {/if}

        <input type="hidden" name="sync_order_and_provider_amount" id="sync_order_and_provider_amount" value="0"/>

        <div class="row">
            <div class="col-xs-12">
                <div class="box">

                    {$isOffice = $address->getOffice() && $address->getOffice()->getId() && $address->getOffice()->getType() == 0}
                    {$isLocker = $address->getOffice() && $address->getOffice()->getId() && $address->getOffice()->getType() == 1}
                    {if $isOffice || $isLocker || ($isOffice && $settings->get('to_address', $settings->get('to_door'))) || (!$isOffice && $settings->get('to_office')) || (!$isLocker && $settings->get('to_locker'))}
                        <div class="box-section">
                            {if $isOffice}
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <a href="{route('apps.speedy.changePickup', ['order_id'=>$order->id,'type' => 'address'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.address{/t}</a>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="{route('apps.speedy.changePickup', ['order_id'=>$order->id, 'type' => 'office'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.office_change{/t}</a>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <a href="{route('apps.speedy.changePickup', ['order_id'=>$order->id, 'type' => 'locker'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.locker{/t}</a>
                                    </div>
                                </div>
                            {elseif $isLocker}
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <a href="{route('apps.speedy.changePickup', ['order_id'=>$order->id,'type' => 'address'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.address{/t}</a>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="{route('apps.speedy.changePickup', ['order_id'=>$order->id, 'type' => 'office'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.office{/t}</a>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <a href="{route('apps.speedy.changePickup', ['order_id'=>$order->id, 'type' => 'locker'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.locker_change{/t}</a>
                                    </div>
                                </div>
                            {else}
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <a href="{route('apps.speedy.changePickup', ['order_id'=>$order->id, 'type' => 'office'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.office{/t}</a>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="{route('apps.speedy.changePickup', ['order_id'=>$order->id, 'type' => 'locker'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.locker{/t}</a>
                                    </div>
                                </div>
                            {/if}
                        </div>
                    {/if}

                    {*                    <div class="box-section">*}
                    {*                        <div class="row form-group js-error-shipping-provider-speedy">*}
                    {*                            <div class="col-xs-6">*}
                    {*                                <label>*}
                    {*                                    <input type="radio" name="waybill[pickup]" value="address" {if !$address->getOffice() || !$address->getOffice()->getId()}checked="checked"{/if} /> {t}shipping.waybill.text.pickup.address{/t}*}
                    {*                                </label>*}
                    {*                            </div>*}

                    {*                            <div class="col-xs-6">*}
                    {*                                <label>*}
                    {*                                    <input type="radio" name="waybill[pickup]" value="office" {if $address->getOffice() && $address->getOffice()->getId()}checked="checked"{/if} /> {t}shipping.waybill.text.pickup.office{/t}*}
                    {*                                </label>*}
                    {*                            </div>*}
                    {*                        </div>*}

                    {*                        <div class="row form-group">*}
                    {*                            <div class="col-xs-12 js-speedy-pickup-office {if !$address->getOffice() || !$address->getOffice()->getId()}hidden{/if}">*}
                    {*                                {include file="./waybill/pickup-office.tpl"}*}
                    {*                            </div>*}

                    {*                            <div class="col-xs-12 js-speedy-pickup-address {if $address->getOffice() && $address->getOffice()->getId()}hidden{/if}">*}
                    {*                                *}{*{include file="./waybill/pickup-address.tpl"}*}
                    {*                            </div>*}
                    {*                        </div>*}
                    {*                    </div>*}
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-12">
                                {t}shipping.waybill.label.service{/t}: {$order->meta_pluck->get('service_name')}
                                {if $order->shippingAddress}
                                    <p>
                                        {$order->shippingAddress->format(false, false)}
                                    </p>
                                {/if}
                            </div>
                        </div>
                    </div>
                    {if $settings->get('pickup') == 'address'}
                        <div class="box-section">
                            <div class="form-group-dynamic">
                                <div class="row form-group">
                                    <div class="col-xs-12">
                                        <label class="control-label"> {t}shipping.select.sender.adress{/t}</label>
                                        <select name="speedy[client_id]" class="form-control select2me">
                                            {foreach $clients as $client}
                                                <option value="{$client['id']}" {if $settings->get('client_id') == $client['id']} selected{/if}>{$client['name']}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-xs-6">
                                        <input type="text" name="speedy[name]" class="form-control" data-autofocus=""
                                               placeholder="{t}shipping.waybill.ph.name{/t}"
                                               value="{$settings->get('name')}"/>
                                    </div>

                                    <div class="col-xs-6">
                                        <input type="text" name="speedy[phone]" class="form-control" data-autofocus=""
                                               placeholder="{t}shipping.waybill.ph.phone{/t}"
                                               value="{$settings->get('phone')}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {/if}
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-6">
                                <label class="control-label">{t}shipping.waybill.label.contents{/t} <i
                                            class="glyphicon glyphicon-info-sign tooltips" title="" data-placement="top"
                                            data-original-title="{t}shipping.waybill.info.contents{/t}"></i></label>

                                <input type="text" name="waybill[contents]" id="speedy_contents" class="form-control"
                                       placeholder="{t}shipping.waybill.ph.contents{/t}"
                                       value="{$contents}"/>
                            </div>

                            <div class="col-xs-3">
                                <label class="control-label">{t}shipping.waybill.label.weight{/t}</label>

                                <div class="input-group">
                                    <input type="text" name="waybill[weight]" id="speedy_weight" class="form-control"
                                           placeholder="{t}speedy::speedy.waybill.ph.weight{/t}"
                                           value="{$weight}"/>

                                    <span class="input-group-addon gray input-group-addon-right">{$weight_unit}</span>
                                </div>
                            </div>

                            <div class="col-xs-3">
                                <label class="control-label">{t}speedy::speedy.waybill.label.packing{/t} <i
                                            class="glyphicon glyphicon-info-sign tooltips" title="" data-placement="top"
                                            data-original-title="{t}speedy::speedy.waybill.info.packing{/t}"></i></label>
                                <input type="text" name="waybill[packing]" id="speedy_count" class="form-control"
                                       placeholder="{t}speedy::speedy.waybill.ph.packing{/t}"
                                       value="{$settings->get('packing')}"/>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-3">
                                <label class="control-label">{t}shipping.waybill.label.payer_type{/t}</label>

                                <select class="form-control select2me" id="speedy_payer_type" name="waybill[side]"
                                        data-no-input="true">
                                    {foreach $waybill_sides AS $waybill_side}
                                        <option {if $waybill_side['id'] == $side}selected="selected"{/if}
                                                value="{$waybill_side['id']}">{$waybill_side['name']}</option>
                                    {/foreach}
                                </select>
                            </div>

                            <div class="col-xs-3">
                                <label class="control-label">{t}speedy::speedy.waybill.label.items_count{/t}</label>

                                <input
                                        name="waybill[items_count]"
                                        {if count($boxes) || $order->meta_pluck->get('is_tire_service', false)}readonly="readonly"{/if}
                                        id="speedy_items_count"
                                        class="form-control"
                                        placeholder="{t}speedy::speedy.waybill.ph.items_count{/t}"
                                        value="{$order->meta_pluck->get('items_count', 1)}"
                                        type="number"
                                        min="1"
                                        step="1"
                                />
                            </div>

                            <div class="col-xs-6">
                                <label class="control-label">{t}shipping.waybill.label.service{/t}</label>
                                <select name="waybill[service_id]" data-no-input="true"
                                        class="form-control select2me js-serices-place" id="js-serices-place-holder">
                                    <option value="">&nbsp;</option>
                                    {foreach $calculate AS $c}
                                        <option value="{$c['id']}" data-type="{$c['type']}" data-total="{$c['price']}"
                                                {if $order->meta_pluck->get('service_id') == $c['id']}selected="selected"{/if}>{$c['name']} {if $c['price'] > 0 || $c['original_price'] > 0}({$c['price_formatted']}) {if $c['price_formatted'] != $c['original_price_formatted']}({$c['original_price_formatted']}){/if}{/if}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>

                        <div class="js-custom-pieces">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th>{t}shipping.waybill.label.width{/t} ({$weight_unit_short})</th>
                                    <th>{t}shipping.waybill.label.height{/t} ({$weight_unit_short})</th>
                                    <th>{t}shipping.waybill.label.depth{/t} ({$weight_unit_short})</th>
                                    <th>{t}shipping.waybill.label.weight{/t} ({$weight_unit_short})</th>
                                </tr>
                                </thead>
                                <tbody>
                                {if count($item_content) > 0}
                                    {foreach $item_content as $key => $item}
                                        <tr>
                                            <td>
                                                <input type="number" name="custom_pieces[{$key}][width]"
                                                       value="{$item['width']}" class="form-control js-width" min="0">
                                            </td>
                                            <td>
                                                <input type="number" name="custom_pieces[{$key}][height]"
                                                       value="{$item['height']}" class="form-control js-height" min="0">
                                            </td>
                                            <td>
                                                <input type="number" name="custom_pieces[{$key}][depth]"
                                                       value="{$item['depth']}" class="form-control js-depth" min="0">
                                            </td>
                                            <td>
                                                <div class="input-group">
                                                    <input type="number" name="custom_pieces[{$key}][weight]"
                                                           value="{$item['weight']}" class="form-control js-weight"
                                                           min="0">
                                                    <span class="input-group-addon gray input-group-addon-right">{$weight_unit}</span>
                                                </div>
                                            </td>
                                        </tr>
                                    {/foreach}
                                {/if}
                                </tbody>
                            </table>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-12">
                                <label class="control-label">{t}speedy::speedy.waybill.label.customer_note{/t}</label>

                                <textarea name="waybill[customer_note]" id="speedy_customer_note" class="form-control"
                                          placeholder="{t}speedy::speedy.waybill.ph.customer_note{/t}">{$customer_note}</textarea>
                            </div>
                        </div>

                        {if count($boxes)}
                            <tr class="grid-wrapper">
                                <table class="table table-lower">
                                    <thead>
                                    <tr>
                                        <th>
                                            {t}shipping.th.waybill.boxes{/t} <i
                                                    class="glyphicon glyphicon-info-sign tooltips"
                                                    title="{t}shipping.waybill.tooltip.boxes{/t}"
                                                    data-placement="top"></i>
                                        </th>

                                        <th class="text-right">
                                            {t}shipping.th.waybill.boxes.count{/t}
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {foreach $boxes as $box}
                                        <tr>
                                            <td>
                                                {$box->name}
                                            </td>
                                            <td style="font-size: 14px;" align="right">
                                                {count($box->items|default:[0])}
                                            </td>
                                        </tr>
                                    {/foreach}
                                    </tbody>
                                </table>

                                <div class="row margin-bottom-10">
                                    <div class="col-xs-10">

                                    </div>
                                    <div class="col-xs-2">
                                    </div>
                                </div>
                            </tr>
                        {/if}
                    </div>

                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-7">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="control-label">{t}speedy::speedy.options_special_delivery_requirements{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-5">
                                <select name="waybill[special_delivery_requirements]"
                                        id="speedy_special_delivery_requirements" class="form-control select2me"
                                        data-no-input="true">
                                    <option value="">{t}global.select{/t}</option>
                                    {foreach $special_delivery_requirements as $option_id => $option_name}
                                        <option {if $option_id == $order->meta_pluck->get('special_delivery_requirements', $settings->get('special_delivery_requirements'))}selected="selected"{/if}
                                                value="{$option_id}">{$option_name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>

                        {*
                        {if !$payed && $order->isCashOnDelivery()}
                            *}
                        {if !$payed}
                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <div class="stack">
                                        <div class="stack-main">
                                            <label class="form-control-check">{t}shipping.waybill.label.cod{/t}</label>
                                        </div>

                                        <div class="stack-addon">
                                            <input type="checkbox" name="waybill[cod]" id="speedy_cod" class="switch"
                                                   placeholder="{t}speedy::speedy.waybill.ph.cod{/t}"
                                                   value="1" {if $order->meta_pluck->get('cod')}checked="checked"{/if}/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row form-group {if !$order->meta_pluck->get('cod')}hidden{/if} js-speedy-code-change-active">
                                <div class="col-xs-9">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="form-control-check">{t}shipping.waybill.label.total{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-3">
                                    <div class="input-group">
                                        <input type="text" name="waybill[total]" id="speedy_total" class="form-control"
                                               placeholder="{t}speedy::speedy.waybill.ph.total{/t}"
                                               value="{$cash_on_delivery_amount}"/>
                                        <span class="input-group-addon">{$currency}</span>
                                    </div>
                                </div>
                            </div>
                            {if !$isApt}
                                <div class="row form-group">
                                    <div class="col-xs-7">
                                        <div class="form-control-box">
                                            <div class="form-control-box-inner">
                                                <label class="control-label">{t}speedy::speedy.waybill.label.option_before_payment{/t}</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-xs-5">
                                        <select class="form-control select2me" data-no-input="true"
                                                id="speedy_option_before_payment" name="waybill[option_before_payment]">
                                            {foreach $options_before_payment as $option_id => $option}
                                                <option {if $option_id == $order->meta_pluck->get('option_before_payment', $settings->get('option_before_payment'))}selected="selected"{/if}
                                                        value="{$option_id}">{$option}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                </div>
                            {/if}
                            <div class="form-group js-instruction-returns">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="form-control-check">{t}shipping.instruction_returns{/t}</label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" class="switch" value="return"
                                               name="waybill[instruction_returns]"{if $order->meta_pluck->get('instruction_returns', $settings->get('instruction_returns')) == 'return'} checked="checked"{/if}>
                                    </div>
                                </div>
                            </div>
                        {/if}

                        <div class="row form-group">
                            <div class="col-xs-12">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="form-control-check">{t}shipping.waybill.label.insurance{/t}</label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" name="waybill[insurance]" id="speedy_insurance"
                                               class="switch"
                                               placeholder="{t}speedy::speedy.waybill.ph.insurance{/t}"
                                               value="1"
                                               {if $settings->get('insurance')}checked="checked"{/if}/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group js-speedy-insurance-change-active">
                            <div class="col-xs-12">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="form-control-check">{t}shipping.waybill.label.fragile{/t}</label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" name="waybill[fragile]" id="speedy_fragile"
                                               class="switch"
                                               placeholder="{t}speedy::speedy.waybill.ph.fragile{/t}"
                                               value="1" {if $settings->get('fragile')}checked="checked"{/if}/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group js-speedy-insurance-change-active">
                            <div class="col-xs-9">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="form-control-check">{t}shipping.waybill.label.total_insurance{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-3">
                                <div class="input-group">
                                    <input type="text" name="waybill[totalInsurance]" id="speedy_totalInsurance"
                                           class="form-control"
                                           placeholder="{t}speedy::speedy.waybill.ph.total_insurance{/t}"
                                           value="{$insurance_amount}"/>
                                    <span class="input-group-addon">{$currency}</span>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-9">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="form-control-check">{t}shipping.waybill.send_date{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-3">
                                <input type="text" size="16" class="form-control form_datetime no_minutes"
                                       data-format="{$date_format}" name="send_date" value="{$send_date}">
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="stack">
                                <div class="stack-main">
                                    <label class="form-control-check">{t}shipping.back_documents{/t}</label>
                                </div>

                                <div class="stack-addon">
                                    <input type="checkbox" class="switch" value="1"
                                           name="waybill[back_documents]"{if $settings->get('back_documents')} checked="checked"{/if}>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-9">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="form-control-check">{t}shipping.waybill.package_number{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-3">
                                <input type="text" size="16" class="form-control" name="waybill[package_id]" value="">
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="stack">
                                <div class="stack-main">
                                    <label class="form-control-check">{t}shipping.documents_shipment{/t}</label>
                                </div>

                                <div class="stack-addon">
                                    <input type="checkbox" class="switch" value="1"
                                           name="waybill[documents]"{if $settings->get('documents')} checked="checked"{/if}>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <script type="text/html" id="js-custom-pieces">
        <tr>
            <td>
                <input type="number" name="custom_pieces[%ROW%][width]" class="form-control js-width" min="0">
            </td>
            <td>
                <input type="number" name="custom_pieces[%ROW%][height]" class="form-control js-height" min="0">
            </td>
            <td>
                <input type="number" name="custom_pieces[%ROW%][depth]" class="form-control js-depth" min="0">
            </td>
            <td>
                <div class="input-group">
                    <input type="number" name="custom_pieces[%ROW%][weight]" class="form-control js-weight" min="0">
                    <span class="input-group-addon gray input-group-addon-right">{$weight_unit}</span>
                </div>
            </td>
        </tr>
    </script>
</div>

{script}
    <script type="text/javascript">
        var custom_pieces = {$order->meta_pluck->get('custom_pieces', 'false') nofilter};
        var isTireService = {$order->meta_pluck->get('is_tire_service', 'false') nofilter};
        var itemsCountInput = $('[name="waybill[items_count]"]'),
            weightInput = $('[name="waybill[weight]"]'),
            serviceInput = $('[name="waybill[service_id]"]');

        if (!isTireService) {
            itemsCountInput.on('change input keyup', function () {
                var element = $(this),
                    value = element.val(),
                    template = $('#js-custom-pieces').html(),
                    tableHolder = $('.js-custom-pieces'),
                    tableBody = tableHolder.find('tbody');

                tableBody.empty();
                for (var i = 0; i < value; i++) {
                    var newTemplate = $(template.replace(/\%ROW\%/g, i)),
                        values = custom_pieces && custom_pieces[i] ? custom_pieces[i] : false;
                    tableBody.append(newTemplate);

                    App.initAjax(newTemplate);

                    if (values) {
                        for (var t in values) {
                            newTemplate.find('input.js-' + t).val(values[t]);
                        }
                    }
                }

                if (value > 1) {
                    weightInput.attr('readonly', true);
                    CC.statusBox(tableHolder, 1);
                } else {
                    weightInput.attr('readonly', false);
                    CC.statusBox(tableHolder, 0);
                }

            }).trigger('change');
        }


        $('#speedy_cod').on('change', function () {
            CC.statusBox($('.js-speedy-code-change-active'), $(this).is(':checked'));
        }).trigger('change');

        var old_service_id = parseInt(serviceInput.val());
        serviceInput.off('change').on('change', function () {
            old_service_id = parseInt(serviceInput.val());
        }).trigger('change');

        $('#speedy_insurance').on('change', function () {
            CC.statusBox($('.js-speedy-insurance-change-active'), $(this).is(':checked'));
        }).trigger('change');

        $('[name="waybill[pickup]"]').on('change', function () {
            CC.statusBox($('.js-speedy-pickup-office'), $(this).val() === 'office');
            CC.statusBox($('.js-speedy-pickup-address'), $(this).val() === 'address');
        }).filter(':checked').trigger('change');


        $('.order-shipping-details [data-dismiss="modal"]').on('click', function () {
            $('.order-shipping-details').remove();
            $('.btn-ship').removeClass('invisible').show();
        });

        $('.js-generate-bill-of-lading').off('click').on('click', function () {
            var $form = $('#issueWaybill'),
                $btn_waybilling = $(this);

            {if !in_array($order->status, ['completed', 'paid'])}
            var syncAmountField = $form.find($('#sync_order_and_provider_amount'));
            Custom.confirm({
                'title': '{t js=true}shipping.waybill.sync_provider_and_order_amount.title{/t}',
                'message': '{t js=true}shipping.waybill.sync_provider_and_order_amount.message{/t}',
                'success': function () {
                    syncAmountField.val(1);
                    $form.trigger('submit');
                },
                'cancel': function () {
                    syncAmountField.val(0);
                    $form.trigger('submit');
                }
            });
            {else}
            $form.trigger('submit');
            {/if}

            $btn_waybilling.trigger('loading.start');
            $btn_waybilling.prop("disabled", true);

            $form.off('cc.ajax.success').on('cc.ajax.success', function (e, json) {
                $btn_waybilling.trigger('loading.end');
                $btn_waybilling.prop("disabled", false);
                if (json.status === 'error') {
                    Custom.handleAjaxFormError(false, false, data, $form);
                    return;
                }
                if (json.status === 'success') {
                    $('#order_summary').trigger('cc.ajax.reload');
                    $('#order_status').trigger('cc.ajax.reload');
                    $('#order_customer').trigger('cc.ajax.reload');
                    $('#order_shipping_address').trigger('cc.ajax.reload');
                    $('#order_history').trigger('cc.ajax.reload');
                    $('.order-shipping-details').remove();
                }
            });
            $form.off("cc.ajax.error").on("cc.ajax.error", function (e, res) {
                $btn_waybilling.trigger('loading.end');
                $btn_waybilling.prop("disabled", false);
                if ("string" == typeof (res)) {
                    res = $.parseJSON(res);
                }
                swal({
                    title: TranslationLabels['error'],
                    text: res.msg ? res.msg.replace(/<br>/g, "\n") : '',
                    type: "error",
                    confirmButtonText: "OK",
                    confirmButtonClass: "btn-danger"
                });
            });
        });

        // serviceInput.off('change.type').on('change.type', function() {
        //     var type = serviceInput.find('option[value="' + serviceInput.val() + '"]').data('type');
        //     console.log(type)
        // });

        $('.js-btn-calculate').off('click').on('click', function () {
            var $form = $('#issueWaybill'),
                $btn = $(this);

            $btn.trigger('loading.start');
            $btn.prop("disabled", true);

            $form.off('cc.ajax.success').on('cc.ajax.success', function (e, json) {
                $btn.trigger('loading.end');
                $btn.prop("disabled", false);
                if (json.status === 'error') {
                    Custom.handleAjaxFormError(false, false, data, $form);
                    return;
                }
                if (json.status === 'success') {
                    var $holder = serviceInput.empty();
                    $holder.append('<option value="">&nbsp;</option>');
                    for (var i in json.calculate) {
                        var text = json.calculate[i].name;
                        if (json.calculate[i].price > 0 || json.calculate[i].original_price > 0) {
                            text += ' (' + json.calculate[i].price_formatted + ')';
                            if (json.calculate[i].price !== json.calculate[i].original_price) {
                                text += ' (' + json.calculate[i].original_price_formatted + ')';
                            }
                        }
                        var $option = $('<option>').text(text)
                            .attr('data-total', json.calculate[i].price)
                            .attr('data-type', json.calculate[i].type)
                            .attr('value', json.calculate[i].id);
                        if (old_service_id === json.calculate[i].id) {
                            $option = $option.attr('selected', 'selected');
                        }
                        $holder.append($option);
                    }
                    $holder.trigger('change');

                    $('.js-generate-bill-of-lading').removeAttr('disabled');

                    //$('#order_summary').trigger('cc.ajax.reload');
                    $('#order_history').trigger('cc.ajax.reload');

                    //$('.order-shipping-details-box').trigger('cc.ajax.reload');
                    json.status = "updated"; //avoid success in order not to close the modal
                }
            });

            $form.off("cc.ajax.error").on("cc.ajax.error", function (e, res) {
                $btn.trigger('loading.end');
                $btn.prop("disabled", false);
                if ("string" == typeof (res)) {
                    res = $.parseJSON(res);
                }
                swal({
                    title: TranslationLabels['error'],
                    text: res.msg ? res.msg.replace(/<br>/g, "\n") : '',
                    type: "error",
                    confirmButtonText: "OK",
                    confirmButtonClass: "btn-danger"
                });
            });

            Custom.submitFormAjax($form, "{route('apps.speedy.calculate', $order->id)}");

            return false;
        });


        $('#issueWaybill').on('keyup change input', ':input', function (e) {
            if (!$(e.currentTarget || e.target).is('[name="waybill[service_id]"]')) {
                $('.js-generate-bill-of-lading').attr('disabled', true);
            }
        });

        $('#issueWaybill').on('change', '[name="waybill[option_before_payment]"]', function () {
            CC.statusBox($('.js-instruction-returns'), this.value !== 'no_option');
        }).find('[name="waybill[option_before_payment]"]').trigger('change');

        //on change payer side
        var $side_change = $('[name="waybill[side]"]'),
            last_side = $side_change.val();
        $side_change.on('change', function () {
            var $select = $(this);

            Custom.confirm({
                'message': '{t js=true}shipping.waybill.change_side{/t}',
                'success': function () {
                    var $summary = $('#order_summary'),
                        $history = $('#order_history'),
                        $form = $('.order-shipping-details-box form');
                    Ajax.post({
                        url: '{route('admin.internal.change-side', $order->id)}',
                        data: {
                            side: $select.val()
                        },
                        formHolder: $form,
                        success: function (json) {
                            if (json.status === 'success') {
                                $summary.trigger('cc.ajax.reload');
                                $history.trigger('cc.ajax.reload');
                                $summary.one('cc.ajax.success', function () {
                                    $('.btn-ship').trigger('click');
                                });
                            }
                        }
                    });
                },
                'cancel': function () {
                    var $holder = $select.val(last_side).parent();
                    App.destroy($holder);
                    App.initSelect2($holder);
                }
            });
            return false;
        });
    </script>
{/script}
