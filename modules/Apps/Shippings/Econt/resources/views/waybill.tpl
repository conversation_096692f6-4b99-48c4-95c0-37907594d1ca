<div class="order-shipping-details-box" data-box-ajax="{route('apps.econt.waybill', $order->id)}">
    <form action="{route('apps.econt.waybill', $order_id)}" id="issueWaybill" role="form" class="ajaxForm">

        <input type="hidden" name="sync_order_and_provider_amount" id="sync_order_and_provider_amount" value="0" />

        <div class="row">
            <div class="col-xs-12">
                <!-- COMMON -->
                <div class="box">
                    {if $sender_addresses->count() > 1}
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-12">
                                <label class="control-label">{t}shipping.pickup.label.from{/t} <i
                                            class="glyphicon glyphicon-info-sign tooltips" title="" data-placement="top"
                                            data-original-title="{t}shipping.price.diff{/t}"></i></label>
                                <select name="sender_address" class="form-control select2me" data-no-input="true">
                                    {foreach $sender_addresses AS $addr}
                                    <option value="{$addr->id}" {if $addr->active}selected="selected"{/if}>{$addr->name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                    </div>
                    {else}
                    <input type="hidden" name="sender_address" value="default">
                    {/if}

                    {$isOffice = $address->getOffice() && $address->getOffice()->getId() && $address->getOffice()->getType() == 0}
                    {$isLocker = $address->getOffice() && $address->getOffice()->getId() && $address->getOffice()->getType() == 1}
                    {if $isOffice || $isLocker || ($isOffice && $settings->get('to_address', $settings->get('to_door'))) || (!$isOffice && $settings->get('to_office')) || (!$isLocker && $settings->get('to_locker'))}
                        <div class="box-section">
                            {if $isOffice}
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <a href="{route('apps.econt.changePickup', ['order_id'=>$order->id,'type' => 'address'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.address{/t}</a>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="{route('apps.econt.changePickup', ['order_id'=>$order->id, 'type' => 'office'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.office_change{/t}</a>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <a href="{route('apps.econt.changePickup', ['order_id'=>$order->id, 'type' => 'locker'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.locker{/t}</a>
                                    </div>
                                </div>
                            {elseif $isLocker}
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <a href="{route('apps.econt.changePickup', ['order_id'=>$order->id,'type' => 'address'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.address{/t}</a>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="{route('apps.econt.changePickup', ['order_id'=>$order->id, 'type' => 'office'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.office{/t}</a>
                                    </div>
                                </div>
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <a href="{route('apps.econt.changePickup', ['order_id'=>$order->id, 'type' => 'locker'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.locker_change{/t}</a>
                                    </div>
                                </div>
                            {else}
                                <div class="row form-group">
                                    <div class="col-md-6">
                                        <a href="{route('apps.econt.changePickup', ['order_id'=>$order->id, 'type' => 'office'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.office{/t}</a>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="{route('apps.econt.changePickup', ['order_id'=>$order->id, 'type' => 'locker'])}"
                                           class="btn btn-primary" data-ajax-panel="true" data-panel-class="medium"
                                           data-placement="left">{t}shipping.waybill.text.pickup.locker{/t}</a>
                                    </div>
                                </div>
                            {/if}
                        </div>
                    {/if}
{*                    <div class="box-section">*}
{*                        <div class="row form-group">*}
{*                            <div class="col-xs-6">*}
{*                                <label>*}
{*                                    <input type="radio" name="waybill[pickup]" value="address" {if !$address->getOffice() || !$address->getOffice()->getId()}checked="checked"{/if} /> {t}shipping.waybill.text.pickup.address{/t}*}
{*                                </label>*}
{*                            </div>*}

{*                            <div class="col-xs-6">*}
{*                                <label>*}
{*                                    <input type="radio" name="waybill[pickup]" value="office" {if $address->getOffice() && $address->getOffice()->getId()}checked="checked"{/if} /> {t}shipping.waybill.text.pickup.office{/t}*}
{*                                </label>*}
{*                            </div>*}
{*                        </div>*}

{*                        <div class="row">*}
{*                            <div class="col-xs-12 js-econt-pickup-office {if !$address->getOffice() || !$address->getOffice()->getId()}hidden{/if}">*}
{*                                {include file="./waybill/pickup-office.tpl"}*}
{*                            </div>*}

{*                            <div class="col-xs-12 js-econt-pickup-address {if $address->getOffice() && $address->getOffice()->getId()}hidden{/if}">*}
{*                                *}{*{include file="./waybill/pickup-address.tpl"}*}
{*                            </div>*}
{*                        </div>*}
{*                    </div>*}
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-12">
                                {t}shipping.waybill.label.service{/t}: {$order->meta_pluck->get('service_name')}
                                {if $order->shippingAddress}
                                <p>
                                    {$order->shippingAddress->format(false, false)}
                                </p>
                                {/if}
                            </div>
                        </div>
                    </div>

                    <div class="box-section">
                        <div class="form-group-dynamic">

                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <label class="control-label">{t}shipping.waybill.label.contents{/t} <i
                                                class="glyphicon glyphicon-info-sign tooltips" title="" data-placement="top"
                                                data-original-title="{t}shipping.waybill.info.contents{/t}"></i></label>

                                    <textarea name="waybill[contents]" id="econt_contents" class="form-control" placeholder="{t}shipping.waybill.ph.contents{/t}">{$order_contents}, {$contents}</textarea>
                                </div>
                            </div>

                            {if $pallet|default}
                            <div class="row form-group">
                                <div class="col-xs-7">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="control-label">{t}shipping.waybill.label.pallet{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-5">
                                    <select name="waybill[package_type]" id="package_type" class="form-control select2me" data-no-input="true">
                                        <option value="">{t}global.select{/t}</option>
                                        <option value="PACK">{t}shipping.text.colet{/t}</option>
                                        <option value="PALLET">{t}shipping.text.pallet{/t}</option>
                                    </select>
                                </div>
                            </div>
                            {/if}

                            <div class="row form-group">
                                <div class="col-xs-7">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="control-label">{t}shipping.side{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-5">
                                    <select name="waybill[side]" id="econt_side" class="form-control select2me" data-no-input="true">
                                        {foreach $waybill_sides AS $waybill_side}
                                            <option {if $waybill_side['id'] == $side}selected="selected"{/if} value="{$waybill_side['id']}">{$waybill_side['name']}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>

                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <div class="stack">
                                        <div class="stack-main">
                                            <label class="form-control-check">{t}shipping.sms{/t}</label>
                                        </div>

                                        <div class="stack-addon">
                                            <input type="checkbox" class="switch" value="1" name="waybill[sms]"{if $settings->get('sms')} checked="checked"{/if}>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <div class="stack">
                                        <div class="stack-main">
                                            <label class="form-control-check">{t}shipping.invoice_before_cd{/t}</label>
                                        </div>

                                        <div class="stack-addon">
                                            <input type="checkbox" class="switch" value="1" name="waybill[invoice_before_cd]"{if $settings->get('invoice_before_cd')} checked="checked"{/if}>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <div class="stack">
                                        <div class="stack-main">
                                            <label class="form-control-check">{t}shipping.dc{/t}</label>
                                        </div>

                                        <div class="stack-addon">
                                            <input type="checkbox" class="switch" value="1" name="waybill[dc]"{if $settings->get('dc')} checked="checked"{/if} data-related="waybill[dc_cp]">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <div class="stack">
                                        <div class="stack-main">
                                            <label class="form-control-check">{t}shipping.back_documents{/t}</label>
                                        </div>

                                        <div class="stack-addon">
                                            <input type="checkbox" class="switch" value="1" name="waybill[back_documents]"{if $settings->get('dc_cp')} checked="checked"{/if} data-related="waybill[dc]">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row form-group js-econt-instruction-dc">
                                <div class="col-xs-12">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="form-control-check">{t}shipping.waybill.instruction{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-12">
                                    <textarea class="form-control" name="waybill[instruction]"></textarea>
                                </div>
                            </div>


                            <div class="row form-group">
                                <div class="col-xs-10">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label for="econt_oc_total" class="form-control-check">{t}shipping.pack_count{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-2">
                                    <input type="number" name="waybill[pack_count]" class="form-control"
                                           step="1" min="1" max="100"
                                           data-autofocus="" placeholder="{t}shipping.waybill.ph.pack_count{/t}"
                                           value="{$settings->get('pack_count', 1)}"/>
                                </div>
                            </div>

                                <div class="form-group">
                                    <div class="stack">
                                        <div class="stack-main">
                                            <label class="form-control-check">{t}shipping.priority{/t}</label>
                                        </div>

                                        <div class="stack-addon">
                                            <input type="checkbox" class="switch" value="1" name="waybill[priority_time]"{if $order->meta_pluck->get('delivery_hour')} checked="checked"{/if}>
                                        </div>
                                    </div>
                                </div>

                                <div class="row form-group">
                                    <div class="col-xs-6">
                                        {$pr_active = 0}
                                        <select id="econt_priority_time_address_hour" name="waybill[priority_type]" class="form-control select2me" data-no-input="true">
                                            {foreach $priority_types AS $key=>$type}
                                                {if $order->meta_pluck->get('priority_type') == $type['id']}{$pr_active = $key}{/if}
                                                <option {if $order->meta_pluck->get('priority_type') == $type['id']}selected{/if} value="{$type['id']}">{$type['name']}</option>
                                            {/foreach}
                                        </select>
                                    </div>

                                    <div class="col-xs-6">
                                        <select id="econt_priority_hour_address" name="waybill[priority_hour]" class="form-control select2me" data-no-input="true">
                                            {foreach $priority_types[$pr_active]['hours'] AS $hour}
                                                {$hour = str_pad(strval($hour), 2, '0', STR_PAD_LEFT)}
                                                <option {if $order->meta_pluck->get('delivery_hour') == $hour}selected{/if} value="{$hour}">{$hour}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                </div>
                        </div>
                    </div>

                    <div class="box-title">
                        <div class="box-title-text">
                            <h5>{t}shipping.waybill.injunction.title{/t}</h5>
                        </div>
                    </div>

                    <div class="box-section">

                        {if !$isApt}
                        <div class="row form-group">
                            <div class="col-xs-12">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="form-control-check">{t}shipping.waybill.pay_after_accept{/t}</label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" class="switch" value="1" name="waybill[pay_after_accept]"{if $settings->get('pay_after_accept')} checked="checked"{/if} data-related="waybill[pay_after_test]">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-12">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="form-control-check">{t}shipping.waybill.pay_after_test{/t}</label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" class="switch" value="1" name="waybill[pay_after_test]"{if $settings->get('pay_after_test')} checked="checked"{/if} data-related="waybill[pay_after_accept]">
                                    </div>
                                </div>
                            </div>
                        </div>

                        {*
                        <div class="row form-group js-reject-return">
                            <div class="col-xs-12">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="form-control-check">{t}shipping.instruction_shipping_returns{/t}</label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" class="switch" value="1" name="waybill[instruction_returns]"{if $settings->get('instruction_returns')} checked="checked"{/if} data-related="waybill[returns]">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group js-reject-return">
                            <div class="col-xs-12">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="form-control-check">{t}shipping.instruction_returns{/t}</label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" class="switch" value="1" name="waybill[returns]"{if $settings->get('returns')} checked="checked"{/if} data-related="waybill[instruction_returns]">
                                    </div>
                                </div>
                            </div>
                        </div>
                        *}
                        {/if}

                        <div class="row form-group">
                            <div class="col-xs-9">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="form-control-check">{t}shipping.waybill.send_date{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-3">
                                <input type="text" size="16" class="form-control form_datetime no_minutes" data-format="{$date_format}" name="waybill[send_date]" value="{$send_date}">
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-9">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="form-control-check">{t}shipping.waybill.package_number{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-3">
                                <input type="text" size="16" class="form-control" name="waybill[package_id]" value="{$order->meta_pluck->get('package_id')}">
                            </div>
                        </div>

                        {*
                        {if !$payed && $order->isCashOnDelivery()}
                            *}
                        {if !$payed}
                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <div class="stack">
                                        <div class="stack-main">
                                            <label class="form-control-check">{t}shipping.waybill.label.cod{/t}</label>
                                        </div>

                                        <div class="stack-addon">
                                            <input type="checkbox" name="waybill[cod]" id="econt_cod" class="switch"
                                                   placeholder="{t}econt::econt.waybill.ph.cod{/t}"
                                                   value="1" {if $order->meta_pluck->get('cod')}checked="checked"{/if}/>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row form-group {if !$order->meta_pluck->get('cod')}hidden{/if} js-econt-code-change-active">
                                <div class="col-xs-9">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="form-control-check">{t}shipping.waybill.label.total{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-3">
                                    <div class="input-group">
                                        <input type="text" name="waybill[total]" id="econt_total" class="form-control"
                                               placeholder="{t}econt::econt.waybill.ph.total{/t}"
                                               value="{$cod_total}"/>
                                        <span class="input-group-addon">{$currency}</span>
                                    </div>
                                </div>
                            </div>
                        {/if}

{*                        {if !$isApt}*}
                        <div class="row form-group">
                            <div class="col-xs-12">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="form-control-check">{t}shipping.waybill.label.insurance{/t}</label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" name="waybill[insurance]" id="econt_insurance" class="switch"
                                               placeholder="{t}econt::econt.waybill.ph.insurance{/t}"
                                               value="1" {if $order->meta_pluck->get('insurance')}checked="checked"{/if}/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group js-econt-insurance-change-active">
                            <div class="col-xs-9">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="form-control-check">{t}shipping.waybill.label.total_insurance{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-3">
                                <div class="input-group">
                                    <input type="text" name="waybill[totalInsurance]" id="econt_totalInsurance" class="form-control"
                                           placeholder="{t}econt::econt.waybill.ph.total_insurance{/t}"
                                           value="{$insurance_amount}"/>
                                    <span class="input-group-addon">{$currency}</span>
                                </div>
                            </div>
                        </div>
{*                        {/if}*}

                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

{script}
<script type="text/javascript">
$('[name="waybill[back_documents]"]').on('change', function() {
    CC.statusBox($('.js-econt-instruction-dc'), $(this).is(':checked'));
}).trigger('change');

$('#econt_insurance').on('change', function() {
    CC.statusBox($('.js-econt-insurance-change-active'), $(this).is(':checked'));
}).trigger('change');
$('#econt_cod').on('change', function() {
    CC.statusBox($('.js-econt-code-change-active'), $(this).is(':checked'));
}).trigger('change');
$('[name="waybill[priority_time]"]').on('change', function() {
    CC.statusBox($('[name="waybill[priority_type]"]').closest('.form-group'), $(this).is(':checked'));
}).trigger('change');
$('[name="waybill[sms]"]').on('change', function() {
    CC.statusBox($('[name="waybill[sms_no]"]').closest('.form-group'), $(this).is(':checked'));
}).trigger('change');
$('[name="waybill[pickup]"]').on('change', function() {
    CC.statusBox($('.js-econt-pickup-office'), $(this).val() == 'office');
    CC.statusBox($('.js-econt-pickup-address'), $(this).val() == 'address');
}).filter(':checked').trigger('change');
$('[data-related]').on('change', function() {
    var $el = $(this);
    if($el.is(':checked') && $el.data('related')) {
        App.updateSwitch($('[name="'+$el.data('related')+'"]').prop('checked', false));
    }
});
var priority_hour_sel = "{$order->meta_pluck->get('delivery_hour', 0)}",
    priority_types = {json_encode($priority_types) nofilter};

$(document).off('change', '*[name="waybill[priority_type]"]').on('change', '*[name="waybill[priority_type]"]', function (e) {
    var $el = $(this),
            $min_holder = $('#econt_priority_hour_address').empty(),
            val = $el.val();

    for(r in priority_types) {
        if(priority_types[r]['id'] == val) {
            for (i in priority_types[r]['hours']) {
                h = priority_types[r]['hours'][i];
                iStr = h.toString();

                if (iStr.length < 2) {
                    priority_hour = '0' + h;
                } else {
                    priority_hour = h;
                }
                $option = $('<option value="' + priority_hour + '">' + priority_hour + '</option>');
                if (priority_hour == priority_hour_sel) {
                    $option.attr('selected', 'selected');
                }
                $min_holder.append($option);
            }
        }
    }
});

$('.js-generate-bill-of-lading').off('click').on('click', function() {
    var $form = $('#issueWaybill'),
        $btn_waybilling = $(this);

    {if !in_array($order->status, ['completed', 'paid'])}
        var syncAmountField = $form.find($('#sync_order_and_provider_amount'));
        Custom.confirm({
            'title' : '{t js=true}shipping.waybill.sync_provider_and_order_amount.title{/t}',
            'message': '{t js=true}shipping.waybill.sync_provider_and_order_amount.message{/t}',
            'success': function() {
                syncAmountField.val(1);
                $form.trigger('submit');
            },
            'cancel': function() {
                syncAmountField.val(0);
                $form.trigger('submit');
            }
        });
    {else}
        $form.trigger('submit');
    {/if}

    $btn_waybilling.trigger('loading.start');
    $btn_waybilling.prop("disabled", true);

    $form.on('cc.ajax.success', function(e, json) {
        $btn_waybilling.trigger('loading.end');
        $btn_waybilling.prop("disabled", false);
        if(json.status == 'error') {
            Custom.handleAjaxFormError(false, false, data, $form);
            return;
        }
        if(json.status == 'success') {
            $('#order_summary').trigger('cc.ajax.reload');
            $('#order_status').trigger('cc.ajax.reload');
            $('#order_customer').trigger('cc.ajax.reload');
            $('#order_shipping_address').trigger('cc.ajax.reload');
            $('#order_history').trigger('cc.ajax.reload');
            $('.order-shipping-details').remove();
        }
    });

    $form.on("cc.ajax.error", function (e, res) {
        $btn_waybilling.trigger('loading.end');
        $btn_waybilling.prop("disabled", false);
        if ("string" == typeof(res)) {
            res = $.parseJSON(res);
        }
        swal({
            title: TranslationLabels['error'],
            text: res.msg ? res.msg.replace(/<br>/g, "\n") : '',
            type: "error",
            confirmButtonText: "OK",
            confirmButtonClass: "btn-danger"
        });
    });
});

$('.order-shipping-details [data-dismiss="modal"]').on('click', function() {
    $('.order-shipping-details').remove();
    $('.btn-ship').removeClass('invisible').show();
});

$('[name="waybill[pay_after_accept]"], [name="waybill[pay_after_test]"]').on('change', function() {
    if($('[name="waybill[pay_after_accept]"]').is(':checked') || $('[name="waybill[pay_after_test]"]').is(':checked')) {
        CC.statusBox($('.js-reject-return'), 1);
    } else {
        CC.statusBox($('.js-reject-return'), 0);
    }
}).trigger('change');

//on change payer side
var $side_change = $('[name="waybill[side]"]'),
        last_side = $side_change.val();
$side_change.on('change', function() {
    var $select = $(this);

    Custom.confirm({
        'message': '{t js=true}shipping.waybill.change_side{/t}',
        'success': function() {
            var $summary = $('#order_summary'),
                $history = $('#order_history'),
                $form = $('.order-shipping-details-box form');
            Ajax.post({
                url: '{route('admin.internal.change-side', $order->id)}',
                data: {
                    side: $select.val()
                },
                formHolder: $form,
                success: function(json) {
                    if(json.status === 'success') {
                        $summary.trigger('cc.ajax.reload');
                        $history.trigger('cc.ajax.reload');
                        $summary.one('cc.ajax.success', function() {
                            $('.btn-ship').trigger('click');
                        });
                    }
                }
            });
        },
        'cancel': function() {
            var $holder = $select.val(last_side).parent();
            App.destroy($holder);
            App.initSelect2($holder);
        }
    });
    return false;
});
</script>
{/script}
