<div class="order-shipping-details-box" data-box-ajax="{route('apps.albanian_courier.waybill', $order->id)}">
    <form action="{route('apps.albanian_courier.waybill.save', $order_id)}" id="issueWaybill" role="form"
          class="ajaxForm">

        <input type="hidden" name="sync_order_and_provider_amount" id="sync_order_and_provider_amount" value="0"/>

        <div class="row">
            <div class="col-xs-12">
                <!-- COMMON -->
                <div class="box">
                    <div class="box-section">
                        <div class="form-group-dynamic">
                            <div class="row form-group">
                                <div class="col-xs-7">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="control-label">{t}shipping.side{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-5">
                                    <select name="waybill[side]" id="albanian_courier_side"
                                            class="form-control select2me" data-no-input="true">
                                        {foreach $waybill_sides AS $waybill_side}
                                            <option {if $waybill_side['id'] == $side}selected="selected"{/if}
                                                    value="{$waybill_side['id']}">{$waybill_side['name']}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>

                            <div class="row form-group">
                                <div class="col-xs-10">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label for="albanian_courier_weight"
                                                   class="form-control-check">{t}shipping.waybill.label.weight{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-2">
                                    <div class="input-group">
                                        <input type="text" name="waybill[weight]" id="albanian_courier_weight"
                                               class="form-control" placeholder="{t}shipping.waybill.label.weight{/t}"
                                               value="{$weight}"/>
                                        <span class="input-group-addon">{t}fancourier::fancourier.kg{/t}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row form-group">
                                <div class="col-xs-10">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label for="albanian_courier_package"
                                                   class="form-control-check">{t}shipping.waybill.ph.pack_count{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-2">
                                    <input type="number" name="waybill[package]"
                                           id="albanian_courier_weightalbanian_courier_package" class="form-control"
                                           value="{$package_count}"/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="box-title">
                        <div class="box-title-text">
                            <h5>{t}shipping.waybill.injunction.title{/t}</h5>
                        </div>
                    </div>

                    <div class="box-section">
                        {if !$payed}
                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <div class="stack">
                                        <div class="stack-main">
                                            <label class="form-control-check">{t}shipping.waybill.label.cod{/t}</label>
                                        </div>

                                        <div class="stack-addon">
                                            <input type="checkbox" name="waybill[cod]" id="albanian_courier_cod"
                                                   class="switch"
                                                   placeholder="{t}albanian_courier::albanian_courier.waybill.ph.cod{/t}"
                                                   value="1" {if $order->isCashOnDelivery()}checked="checked"{/if}/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row form-group {if !$order->meta_pluck->get('cod')}hidden{/if} js-albanian_courier-code-change-active">
                                <div class="col-xs-9">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="form-control-check">{t}shipping.waybill.label.total{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-3">
                                    <div class="input-group">
                                        <input type="text" name="waybill[total]" id="albanian_courier_total"
                                               class="form-control"
                                               value="{$cod_total}"/>
                                        <span class="input-group-addon">{$currency}</span>
                                    </div>
                                </div>
                            </div>
                        {/if}
                        <div class="row form-group" style="margin-top: 10px;">
                            <div class="col-xs-7">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="control-label">{t}albanian_courier::albanian_courier.method_send{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-5">
                                <select name="waybill[method_send]" id="albanian_courier_method_send"
                                        class="form-control select2me"
                                        data-no-input="true">
                                    <option {if 'D2D' == $service_id}selected="selected"{/if}
                                            value="D2D">{t}albanian_courier::albanian_courier.method_send.d2d{/t}</option>
                                    <option {if 'P2D' ==  $service_id}selected="selected"{/if}
                                            value="P2D">{t}albanian_courier::albanian_courier.method_send.p2d{/t}</option>
                                </select>
                            </div>
                        </div>
                        <div class="row form-group">
                            <div class="col-xs-4">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="control-label">{t}albanian_courier::albanian_courier.comment{/t}</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-8">
                                <textarea name="waybill[instructions]" class="form-control" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<style>
    .js-btn-calculate {
        display: none;
    }
</style>
{script}
    <script type="text/javascript">
        $('#albanian_courier_insurance').on('change', function () {
            CC.statusBox($('.js-albanian_courier-insurance-change-active'), $(this).is(':checked'));
        }).trigger('change');
        $('#albanian_courier_cod').on('change', function () {
            CC.statusBox($('.js-albanian_courier-code-change-active'), $(this).is(':checked'));
        }).trigger('change');
        $('[name="waybill[pickup]"]').on('change', function () {
            CC.statusBox($('.js-albanian_courier-pickup-office'), $(this).val() == 'office');
            CC.statusBox($('.js-albanian_courier-pickup-address'), $(this).val() == 'address');
        }).filter(':checked').trigger('change');
        $('[data-related]').on('change', function () {
            var $el = $(this);
            if ($el.is(':checked') && $el.data('related')) {
                App.updateSwitch($('[name="' + $el.data('related') + '"]').prop('checked', false));
            }
        });
        var serviceInput = $('[name="waybill[service_id]"]');
        var old_service_id = parseInt(serviceInput.val());
        serviceInput.off('change').on('change', function () {
            old_service_id = parseInt(serviceInput.val());
        }).trigger('change');

        $('.js-generate-bill-of-lading').off('click').on('click', function () {
            var $form = $('#issueWaybill'),
                $btn_waybilling = $(this);

            {if !in_array($order->status, ['completed', 'paid'])}
            var syncAmountField = $form.find($('#sync_order_and_provider_amount'));
            Custom.confirm({
                'title': '{t js=true}shipping.waybill.sync_provider_and_order_amount.title{/t}',
                'message': '{t js=true}shipping.waybill.sync_provider_and_order_amount.message{/t}',
                'success': function () {
                    syncAmountField.val(1);
                    $form.trigger('submit');
                },
                'cancel': function () {
                    syncAmountField.val(0);
                    $form.trigger('submit');
                }
            });
            {else}
            $form.trigger('submit');
            {/if}

            $btn_waybilling.trigger('loading.start');
            $btn_waybilling.prop("disabled", true);

            $form.on('cc.ajax.success', function (e, json) {
                console.log(json.status);
                $btn_waybilling.trigger('loading.end');
                $btn_waybilling.prop("disabled", false);
                if (json.status == 'error') {
                    Custom.handleAjaxFormError(false, false, data, $form);
                    return;
                }
                if (json.status == 'success') {
                    $('#order_summary').trigger('cc.ajax.reload');
                    $('#order_status').trigger('cc.ajax.reload');
                    $('#order_customer').trigger('cc.ajax.reload');
                    $('#order_shipping_address').trigger('cc.ajax.reload');
                    $('#order_history').trigger('cc.ajax.reload');
                    $('.order-shipping-details').remove();
                }
            });

            $form.on("cc.ajax.error", function (e, res) {
                $btn_waybilling.trigger('loading.end');
                $btn_waybilling.prop("disabled", false);
                if ("string" == typeof (res)) {
                    res = $.parseJSON(res);
                }
                swal({
                    title: TranslationLabels['error'],
                    text: res.msg ? res.msg.replace(/<br>/g, "\n") : '',
                    type: "error",
                    confirmButtonText: "OK",
                    confirmButtonClass: "btn-danger"
                });
            });
        });

        $('.order-shipping-details [data-dismiss="modal"]').on('click', function () {
            $('.order-shipping-details').remove();
            $('.btn-ship').removeClass('invisible').show();
        });

        $('[name="waybill[pay_after_accept]"], [name="waybill[pay_after_test]"]').on('change', function () {
            if ($('[name="waybill[pay_after_accept]"]').is(':checked') || $('[name="waybill[pay_after_test]"]').is(':checked')) {
                CC.statusBox($('.js-reject-return'), 1);
            } else {
                CC.statusBox($('.js-reject-return'), 0);
            }
        }).trigger('change');
        $('.js-btn-calculate').off('click').on('click', function () {
            var $form = $('#issueWaybill'),
                $btn = $(this);


            $btn.trigger('loading.start');
            $btn.prop("disabled", true);

            $form.off('cc.ajax.success').on('cc.ajax.success', function (e, json) {
                console.log(3213);
                $btn.trigger('loading.end');
                $btn.prop("disabled", false);
                if (json.status === 'error') {
                    Custom.handleAjaxFormError(false, false, data, $form);
                    return;
                }
                if (json.status === 'success') {
                    var $holder = serviceInput.empty();
                    $holder.append('<option value="">&nbsp;</option>');
                    for (var i in json.calculate) {
                        var text = json.calculate[i].name;
                        if (json.calculate[i].price > 0 || json.calculate[i].original_price > 0) {
                            text += ' (' + json.calculate[i].price_formatted + ')';
                            if (json.calculate[i].price !== json.calculate[i].original_price) {
                                text += ' (' + json.calculate[i].original_price_formatted + ')';
                            }
                        }
                        var $option = $('<option>').text(text)
                            .attr('data-total', json.calculate[i].price)
                            .attr('data-type', json.calculate[i].type)
                            .attr('value', json.calculate[i].id);
                        if (old_service_id === json.calculate[i].id) {
                            $option = $option.attr('selected', 'selected');
                        }
                        $holder.append($option);
                    }
                    $holder.trigger('change');

                    $('.js-generate-bill-of-lading').removeAttr('disabled');
                    $('#order_summary').trigger('cc.ajax.reload');
                    $('#order_history').trigger('cc.ajax.reload');
                }
            });

            $form.off("cc.ajax.error").on("cc.ajax.error", function (e, res) {
                $btn.trigger('loading.end');
                $btn.prop("disabled", false);
                if ("string" == typeof (res)) {
                    res = $.parseJSON(res);
                }
                swal({
                    title: TranslationLabels['error'],
                    text: res.msg ? res.msg.replace(/<br>/g, "\n") : '',
                    type: "error",
                    confirmButtonText: "OK",
                    confirmButtonClass: "btn-danger"
                });
            });

            Custom.submitFormAjax($form, "{route('apps.albanian_courier.calculate', $order->id)}");

            return false;
        });
        //on change payer side
        var $side_change = $('[name="waybill[side]"]'),
            last_side = $side_change.val();
        $side_change.on('change', function () {
            var $select = $(this);
            Custom.confirm({
                'message': '{t js=true}shipping.waybill.change_side{/t}',
                'success': function () {
                    var $summary = $('#order_summary'),
                        $history = $('#order_history'),
                        $form = $('.order-shipping-details-box form');
                    Ajax.post({
                        url: '{route('admin.internal.change-side', $order->id)}',
                        data: {
                            side: $select.val()
                        },
                        formHolder: $form,
                        success: function (json) {
                            if (json.status === 'success') {
                                $summary.trigger('cc.ajax.reload');
                                $history.trigger('cc.ajax.reload');
                                $summary.one('cc.ajax.success', function () {
                                    $('.btn-ship').trigger('click');
                                });
                            }
                        }
                    });
                },
                'cancel': function () {
                    var $holder = $select.val(last_side).parent();
                    App.destroy($holder);
                    App.initSelect2($holder);
                }
            });
            return false;
        });
    </script>
{/script}