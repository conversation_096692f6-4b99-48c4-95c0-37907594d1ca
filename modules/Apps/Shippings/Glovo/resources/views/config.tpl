{include file="./dependency.tpl"}
{*{include file="shippings/price_and_weight_rate_row.tpl"}*}

<div class="page-breadcrumb clearfix">
    <ul class="breadcrumb">
        <li>
            <a href="{asset('')}apps">{t}sidebar.apps{/t}</a>
            <i class="fal fa-chevron-double-right fa-xs"></i>
        </li>

        <li>
            <span>{t}glovo::glovo.title{/t}</span>
        </li>
    </ul>
</div>

<div class="wrapper">
    <div class="container-wide">
        {if !Apps::installed('stores')}
            <div class="alert-warning text-center" style="padding:20px; margin-bottom:20px">
                {t}glovo::glovo.install.error_store{/t}
            </div>
        {/if}
        {if empty(setting('google_map_api_key'))}
            <div class="alert-warning text-center" style="padding:20px">
                {t}glovo::glovo.install.error_api_key{/t}
            </div>
        {/if}
        {if Apps::installed('stores') && !empty(setting('google_map_api_key'))}
            <form action="{route('apps.glovo')}" id="glovoForm" role="form" method="post" class="ajaxForm"
                  data-rates-form>
                <div class="box">

                    <div class="box-title">
                        <div class="box-title-text">
                            <h5>{t}glovo::glovo.title{/t}</h5>
                        </div>
                        <div class="box-title-addon">
                            <input type="checkbox" name="glovo[status]" value="1"
                                   {if $session == false }disabled {else } {if $status} checked="checked"{/if} {/if}
                                   class="switch"/>
                        </div>
                    </div>
                </div>

                <div class="box{if !$session} hidden{/if}" data-hidden>

                    <div class="box-section">
                        <div class="form-group-dynamic">
                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <input type="text" name="glovo[provider_name]" id="glovo_title" class="form-control"
                                           data-autofocus="" placeholder="{t}shipping.ph.provider_name{/t}"
                                           value="{$provider->name}"/>

                                    <span class="help-block">{t}shipping.help.provider_name{/t}</span>
                                </div>
                            </div>
                            <div class="row form-group">
                                <div class="col-xs-12">
                                    {include file="shipping/include/logo.tpl" image=$provider->getImage('150x150') has_image=$provider->hasImage()}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                {foreach ['address'] as $rate_type}
                    <div class="box{if !$session} hidden{/if}" data-hidden>
                        <div class="box-title">
                            <div class="box-title-text">
                                <h5>{t}shipping.waybill.type.title{/t} {t}omniship.text.to_{$rate_type}{/t}</h5>
                            </div>
                            <div class="box-title-addon">
                                {if $rate_type == 'address'}
                                    {$checked = $settings->get("to_address", $settings->get('to_door'))}
                                    {$reverce='to_office'}
                                {else}
                                    {$checked = $settings->get("to_{$rate_type}")}
                                    {$reverce='to_address'}
                                {/if}
                                <input type="checkbox" name="glovo[to_{$rate_type}]" value="1"
                                       class="switch" checked="checked" data-to-shipping="{$reverce}"
                                       data-shipping-key="to_{$rate_type}"/>
                            </div>
                        </div>

                        {$pricing = $settings->get("pricing_{$rate_type}", $settings->get('pricing'))}
                        <div class="box-section js-services-box-to_{$rate_type} js-price-rate-{$rate_type}">
                            <div class="form-group-dynamic">
                                <div class="row form-group">
                                    <div class="col-xs-12">
                                        <label class="control-label">{t}shipping.pricing{/t} {t}omniship.text.to_{$rate_type}{/t}</label>

                                        <select name="glovo[pricing_{$rate_type}]" id="glovo_pricing_{$rate_type}"
                                                class="form-control select2me" data-no-input="true">
                                            {foreach $pricings as $pricing_id => $pricingText}
                                                <option {if $pricing_id == $pricing}selected="selected"{/if}
                                                        value="{$pricing_id}">{$pricingText}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                </div>


                                <div class="row form-group js-rate-element js-calculator-fixed-rate">
                                    <div class="col-xs-12">
                                        <label for="glovo_calculator_fixed_price"
                                               class="control-label">{t}shipping.processing_fee{/t}</label>

                                        <div class="row">
                                            <div class="col-xs-3">
                                                <div class="input-group">
                                                    {if $currency[1] == 'before'}
                                                        <span class="input-group-addon gray input-group-addon-left">
                                                    {$currency[0]}
                                                </span>
                                                    {/if}

                                                    <input type="text" name="glovo[fixed_price_{$rate_type}]"
                                                           class="form-control"
                                                           id="glovo_calculator_fixed_price_{$rate_type}"
                                                           {if !in_array($pricing, ["calculator_fixed"])}disabled="disabled"{/if}
                                                           placeholder="0"
                                                           value="{$settings->get("fixed_price_{$rate_type}", $settings->get("fixed_price"))}">

                                                    {if $currency[1] == 'after'}
                                                        <span class="input-group-addon gray input-group-addon-right">
                                                    {$currency[0]}
                                                </span>
                                                    {/if}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row form-group js-rate-element js-free-rate_{$rate_type}">
                                    <div class="col-xs-12">
                                        <div class="row">
                                            <div class="col-xs-4">
                                                <label for="glovo_free_shipping_total_address"
                                                       class="control-label">{t}shipping.free_shipping_min_amount{/t}</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-xs-4">
                                                <div class="row">
                                                    <div class="col-xs-12">
                                                        <div class="input-group">
                                                            {if $currency[1] == 'before'}
                                                                <span class="input-group-addon gray input-group-addon-left">
                                                    {$currency[0]}
                                                </span>
                                                            {/if}

                                                            <input type="text"
                                                                   name="glovo[free_shipping_total_{$rate_type}]"
                                                                   class="form-control"
                                                                   id="glovo_free_shipping_total_{$rate_type}"
                                                                   {if !in_array($pricing, ["free"])}disabled="disabled"{/if}
                                                                   placeholder="0"
                                                                   value="{$settings->get("free_shipping_total_{$rate_type}", $settings->get('free_shipping_total'))}">

                                                            {if $currency[1] == 'after'}
                                                                <span class="input-group-addon gray input-group-addon-right">
                                                    {$currency[0]}
                                                </span>
                                                            {/if}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="row form-group
{if $settings->get('pricing_address', 'calculator') != 'calculator' && $settings->get('pricing_address', 'calculator') != 'calculator_fixed' && $settings->get('pricing_address', 'calculator') != 'free'}hidden{/if}
" id="glovo_calculator">
                                </div>


                                <div class="">
                                    {include file="shippings/rate_group.tpl" inputPrefix="glovo" rate_type=$rate_type}
                                </div>

                            </div>
                        </div>
                    </div>
                {/foreach}

                <div class="box hidden" data-hidden>
                    <div class="box-title">
                        <div class="box-title-text">
                            <h5>{t}shipping.label.regions{/t}</h5>
                        </div>
                    </div>
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-7">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="control-label">{t}shipping.help.target{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-5">
                                <input name="glovo[target]" type="checkbox" class="switch"
                                       value="restofworld" checked="checked"/><span
                                        class="switch-label">{t}shipping.label.the_whole_world{/t}</span>
                            </div>
                        </div>
                        <div class="row form-group">
                            <div class="col-xs-12">
                                <div class="clearfix main-box-section skip-has-error" id="regions_holder">
                                    <select name="glovo[geo_zone_id]" class="form-control select2me">
                                        <option value="">{t}global.select{/t}</option>
                                        {foreach $geo_zones AS $geo_zone}
                                            <option {if $provider->geo_zone_id == $geo_zone->id}selected="selected"{/if}
                                                    value="{$geo_zone->id}">{$geo_zone->name}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                {include file="shipping/include/payment-providers.tpl"}
                {include file="./shops_table.tpl"}

                <div class="box{if !$session} hidden{/if}" data-hidden>
                    <div class="box-title">
                        <div class="box-title-text">
                            <h5>{t}glovo::glovo.waybill.acceptance.title{/t}</h5>
                        </div>
                    </div>

                    <div class="box-section">
                        <!-- COD -->
                        <div class="form-group">
                            <div class="stack">
                                <div class="stack-main">
                                    <label class="form-control-check">{t}glovo::glovo.cod{/t}</label>
                                </div>

                                <div class="stack-addon">
                                    <input type="checkbox" class="switch" value="1"
                                           name="glovo[cd]"{if $settings->get('cd', 0) == 1} checked="checked"{/if}>
                                </div>
                            </div>
                        </div>

                        <!-- default weight -->
                        <div class="row form-group">
                            <div class="col-xs-9">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label for="glovo_default_weight"
                                               class="form-control-check">{t}shipping.entry_default_weight{/t}
                                            <i class="glyphicon glyphicon-info-sign tooltips"
                                               title="{t}shipping.tip.entry_default_weight{/t}"
                                               data-placement="top"></i>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-3">
                                <div class="input-group">
                                    <input type="text" name="glovo[default_weight]" class="form-control"
                                           id="glovo_default_weight" placeholder="{t}global.weight{/t}"
                                           value="{$settings->get('default_weight')}">
                                    <span class="input-group-addon gray input-group-addon-right">{t}global.kg{/t}</span>
                                </div>
                            </div>
                        </div>

                        <!-- excution order -->
                        <div class="row form-group">
                            <div class="col-xs-7">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="control-label">{t}glovo::glovo.execution.order{/t}
                                            <i class="glyphicon glyphicon-info-sign tooltips"
                                               title="{t}glovo::glovo.execution.order.help{/t}" data-placement="top"></i>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-5">
                                <select name="glovo[execution_order]" id="glovo_side" class="form-control select2me"
                                        data-no-input="true">
                                    <option {if $settings->get('execution_order', 1) == 1}selected="selected"{/if}
                                            value="1">{t}glovo::glovo.execution.order.auto{/t}</option>
                                    <option {if $settings->get('execution_order', 1) == 2} selected="selected"{/if}
                                            value="2">{t}glovo::glovo.execution.order.manual{/t}</option>
                                </select>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-7">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label class="control-label">{t}glovo::glovo.type.package{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-5">
                                <select name="glovo[contentType]" id="glovo_content_type" class="form-control select2me"
                                        data-no-input="true">
                                    <option {if $settings->get('contentType', 'GENERIC_PARCEL') == 'FOOD'}selected="selected"{/if}
                                            value="FOOD">FOOD
                                    </option>
                                    <option {if $settings->get('contentType', 'GENERIC_PARCEL') == 'FOOD_WL'} selected="selected"{/if}
                                            value="FOOD_WL">FOOD WL
                                    </option>
                                    <option {if $settings->get('contentType', 'GENERIC_PARCEL') == 'GENERIC_PARCEL'} selected="selected"{/if}
                                            value="GENERIC_PARCEL">GENERIC PARCEL
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="box-footer{if !$session} hidden{/if}" data-hidden>
                    <div class="box-buttons-inline">
                        <button class="btn btn-default">{t}global.cancel{/t}</button>
                        <button class="btn btn-primary submit">{t}global.save{/t}</button>
                    </div>
                </div>
            </form>
        {/if}
    </div>
</div>
<style>
    .js-fallback-title-rate {
        display: none;
    }
</style>