<?php

declare(strict_types=1);

namespace Modules\Apps\Shippings\Dpdbulgaria;

use App;
use App\Exceptions\Error;
use App\Helper\ArrayCache;
use App\Helper\Format;
use App\Helper\Google\Map;
use App\Helper\Store\Contracts\ItemContract;
use App\Helper\YesNo;
use App\Locale\Weight;
use App\Models\Order\Order;
use App\Models\Order\OrderFulfillment;
use Crisu83\Conversion\Quantity\Length\Unit as LengthUnit;
use Crisu83\Conversion\Quantity\Mass\Unit as MassUnit;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\Apps\Shippings\Dpdbulgaria\Models\Cities;
use Modules\Apps\Shippings\Dpdbulgaria\Models\Offices;
use Modules\Apps\Shippings\Dpdbulgaria\Models\PostCodes;
use Modules\Apps\Shippings\Dpdbulgaria\Traits\AdministrativeMethodsForConfiguration;
use Modules\Apps\Shippings\Omniship\AbstractManager;
use Modules\Apps\Shippings\Dpdbulgaria\Models\Countries;
use Omniship\Address\City;
use Omniship\Address\Country;
use Omniship\Address\Office;
use Omniship\Common\Address;
use Omniship\Common\Item;
use Omniship\Common\ItemBag;
use Omniship\Common\PieceBag;
use Omniship\Consts;
use Omniship\DpdRomania\Client;
use Omniship\DpdRomania\Http\CreateBillOfLadingRequest;
use Omniship\DpdRomania\Gateway;
use Omniship\Dpdromania\Http\ShippingQuoteRequest;
use Omniship\Omniship;

class Manager extends AbstractManager
{
    use AdministrativeMethodsForConfiguration;

    public const PHONES = [
        '************'
    ];

    /**
     * @var Gateway
     */
    protected $manager;

    public const KEY = 'omniship.dpdbulgaria';

    protected $fallback_allowed_countries = ['BG', 'RO'];

    public const PRICING_TYPES = [
        'calculator',
        'calculator_fixed',
        'free',
        'fixed_price',
        'fixed_weight',
        'price_and_weight'
    ];

    public $support = ['address', 'office', 'locker'];

    public const APP_KEY = 'dpdbulgaria';

    protected $credentials = ['username', 'password'];

    protected $default_settings = [
        'side' => Consts::PAYER_RECEIVER,
        'to_address' => 1,
        'pricing_address' => 'calculator',
        'default_weight' => 0.1,
        'default_width' => 100,
        'default_height' => 100,
        'default_depth' => 100,
        'order_content' => 'sku',
        'option_before_payment' => 'no_option',
        'tracking' => 'cloudcart',
        'office_countries' => [100],
        'locker_countries' => [100],
    ];

    /**
     * Manager constructor.
     * @param null $key
     * @param null|mixed $username
     * @param null|mixed $password
     * @throws Error
     */
    public function __construct($username = null, $password = null)
    {
        App::register(\Modules\Apps\Shippings\Dpdbulgaria\DpdbulgariaServiceProvider::class);
        if ($this->isProviderRoute() && request()->input('username')) {
            $username = request()->input('username');
            $password = request()->input('password');
        } elseif ($this->isProviderRoute() && request()->input('dpdbulgaria.username')) {
            $username = request()->input('dpdbulgaria.username');
            $password = request()->input('dpdbulgaria.password');
        } elseif ($this->isProviderRoute() && request()->input('dpdromania.username')) {
            $username = request()->input('dpdromania.username');
            $password = request()->input('dpdromania.password');
        } elseif (site('site_id')) {
            $username = $username ?: $this->getSetting('username');
            $password = $password ?: $this->getSetting('password');
            $client_id = $this->getSetting('client_id');
        }

        $this->manager = Omniship::create('Dpdromania')->initialize([
            'username' => $username,
            'password' => $password,
            'country' => 'BG',
            'client_id' => $client_id ?? null,
            'connection_options' => [
                'connection_timeout' => app()->runningInConsole() ? 30 : 15,
                'read_timeout' => app()->runningInConsole() ? 30 : 15,
            ]
        ]);
        parent::__construct();
    }

    /**
     * @return Client
     */
    public function getClient()
    {
        $this->getManager()->setCountry('BG');
        return $this->getManager()->getClient();
    }

    /**
     * @param bool $refreshSession
     * @return bool
     */
    public function isActiveSession($refreshSession = true)
    {
        if (!is_null($this->getSetting('username'))) {
            try {
                if (ArrayCache::get($this::APP_KEY . '.session.check')) {
                    return true;
                }

                $validate = $this->getManager()->validateCredentials([
                    'username' => $this->getSetting('username'),
                    'password' => $this->getSetting('password'),
                ])->send()->getData();
                if ($validate != false) {
                    return ArrayCache::remember($this::APP_KEY . '.session.check', fn(): true => true);
                } else {
                    return false;
                }
            } catch (\Exception) {
                return false;
            }
        }

        return false;
    }

    /**
     * @param array $parameters
     * @return \Omniship\Message\AbstractRequest
     */
    public function getQuotesRequest(array $parameters): ?\Omniship\Message\AbstractRequest
    {
        return $this->manager->createRequest(ShippingQuoteRequest::class, $parameters);
    }

    /**
     * @param array $parameters
     * @param \stdClass|null $request_response
     * @param array $categories
     * @return array|bool|mixed|\Omniship\Common\ShippingQuoteBag|null
     * @throws Error
     */
    #[\Override]
    public function getQuotes(array $parameters, ?\stdClass $request_response = null, array $categories = [])
    {
        if (!empty($parameters['items'])) {
            $weight = 0;
            foreach ($parameters['items'] as $item) {
                if ($item->weight == 0) {
                    $item->weight = floatval($this->getSetting('default_weight'));
                }

                $weight += $item->weight * $item->quantity;
                $item->depth = $item->depth ?: floatval($this->getSetting('default_depth'));
                $item->width = $item->width ?: floatval($this->getSetting('default_width'));
                $item->height = $item->height ?: floatval($this->getSetting('default_height'));
            }

            $parameters['weight'] = $weight;
        }

        if (!empty($parameters['receiver_address']) && ($address = $parameters['receiver_address']) instanceof \Omniship\Common\Address && !$address->getOffice()) {
            $parameters['receiver_address'] = $this->translateAddress($address);
        }

        $parameters['sender_address'] = $this->getSenderAddress();
        if (!isset($parameters['payer'])) {
            $parameters['payer'] = $this->getSetting('side');
        }

        $parameters['receiver_address'] = $this->getReceiverAddress($parameters['receiver_address']);
        if (!isset($parameters['back_documents'])) {
            $parameters['back_documents'] = (int)$this->getSetting('back_documents');
        }

        if (!isset($parameters['back_receipt'])) {
            $parameters['back_receipt'] = (int)$this->getSetting('back_receipt');
        }

        $parameters['other_parameters']['payer_id'] = $this->getSetting('payer_id');

        $subTotal = $parameters['sub_total'] ?? 0;

        if ($this->getSetting('declared_amount', 0) == 1) {
            if (empty($parameters['declared_amount'])) {
                $parameters['declared_amount'] = $subTotal;
            }
        }

        if ($this->getSetting('documents') == 1) {
            $parameters['other_parameters']['documents'] = true;
        }


        // $parameters['cash_on_delivery_amount'] = $parameters['sub_total'];

        if (empty($parameters['other_parameters']['fragile'])) {
            $parameters['other_parameters']['fragile'] = (int)$this->getSetting('fragile', 0);
        }

        $parameters['money_transfer'] = $this->getSetting('money_transfer', 0) == 1 ? true : false;

        foreach ($parameters['items'] as $item) {
            if ($item->weight == 0) {
                $item->weight = floatval($this->getSetting('default_weight'));
            }
        }

        $isMachine = false;
        $currency = $this::APP_KEY == 'dpdbulgaria' ? 'BGN' : 'RON';
        if ($parameters['receiver_address'] instanceof Address) {
            if ($parameters['receiver_address']->getCountry() !== null && $parameters['receiver_address']->getCountry()->getCurrency()) {
                $currency = $parameters['receiver_address']->getCountry()->getCurrency();
            }

            $isMachine = $parameters['receiver_address']->getOffice() && in_array($parameters['receiver_address']->getOffice()->getType(), [1, 'apt', 'APT']);
        }

        if (!empty($parameters['cash_on_delivery_amount'])) {
            $parameters['cash_on_delivery_amount'] = App\Models\Gateway\Currency::convert($parameters['cash_on_delivery_amount'], site('currency'), $currency, false);
        }

        $parameters['currency'] = $currency;

        if (!$isMachine && empty($parameters['option_before_payment']) && $this->getSetting('option_before_payment') != 'no_option') {
            $parameters['option_before_payment'] = $this->getSetting('option_before_payment');
            $parameters['other_parameters']['return_pay'] = (int)$this->getSetting('instruction_returns');
        } else {
            $parameters['option_before_payment'] = null;
        }

        if (!empty($parameters['option_before_payment']) && isset($parameters['instruction_returns'])) {
            $parameters['other_parameters']['return_pay'] = $parameters['instruction_returns'];
        }

        $parameters['other_parameters']['return_service'] = $this->getSetting('instruction_returns_service');
        $parameters['other_parameters']['services'] = $this->getSetting('allowed_methods');
        $parameters['other_parameters']['pos_enabled'] = (int)$this->getSetting('pos_enabled');
        $parameters['other_parameters']['item_sizes'] = (int)$this->getSetting('item_sizes');
        if (empty($parameters['service_id'])) {
            if (empty($parameters['other_parameters']['services'])) {
                $parameters['other_parameters']['services'] = $this->getSetting('allowed_methods');
            }
        }


        if (!empty($parameters['items_count'])) {
            $parameters['other_parameters']['items_count'] = $parameters['items_count'];
        }

        if (isset($parameters['pieces']) && (empty($parameters['pieces']) || !($parameters['pieces'] instanceof PieceBag) && is_array($parameters['pieces']))) {
            $parameters['pieces'] = new PieceBag(empty($parameters['pieces']) || !is_array($parameters['pieces']) ? [] : json_decode(json_encode($parameters['pieces']), true));
        }

        if ($this->getSetting('declared_value', 0) == 1) {
            $parameters['declared_amount'] = !empty($parameters['cash_on_delivery_amount']) ? $parameters['cash_on_delivery_amount'] : $subTotal;

            $parameters['declared_amount'] =  App\Models\Gateway\Currency::convert($parameters['declared_amount'], site('currency'), $currency);

            if ($this->getSetting('fragile', 0) == 1) {
                $parameters['other_parameters']['fragile'] = 1;
            }
        }

        return parent::getQuotes($parameters, $request_response, $categories);
    }


    /**
     * @param ItemBag $items
     * @return Collection|null
     * @throws Error
     * @throws \Throwable
     */
    #[\Override]
    public function getPieces(ItemBag $items)
    {
        $pieces = parent::getPieces($items);
        if ($pieces) {
            $pieces = $pieces->map(function (array $piece) {
                $piece['items'] = array_map(function (array $item) {
                    $item['weight'] = floatval(Weight::input($item['weight']));
                    return $item;
                }, $piece['items']);
                return $piece;
            });
        }

        if (!$this->getSetting('item_sizes')) {
            return $pieces;
        }

        if (!$pieces || $pieces->isEmpty()) {
            $allItems = array_filter($items->all(), fn(Item $item): bool => !$item->getParameter('item')->is_digital);
            $allItemsWithDimensions = array_filter($allItems, fn(Item $item) => $item->getParameter('item')->has_dimensions);
            $allItemsWithoutDimensions = array_filter($allItems, fn(Item $item): bool => !$item->getParameter('item')->has_dimensions);

            if (count($allItems) < 1) {
                return null;
            }

            $allItems = collect(array_map(function (Item $item) {
                /** @var ItemContract $cartItem */
                $cartItem = $item->getParameter('item');
                $result = [];
                for ($i = 1; $i <= $cartItem->quantity; $i++) {
                    $data = [
                        //'id' => $item->getId(),
                        'name' => $item->getName(),
                        'description' => $item->getDescription(),
                    ];

                    if (site('unit_system') == 'metric') {
                        $data = array_merge($data, $this->sortPiecesSizes([
                            'width' => ($cartItem->getWidth()) / 10,
                            'height' => ($cartItem->getLength()) / 10,
                            'depth' => ($cartItem->getDepth()) / 10,
                        ]));
                        if ($cartItem->getWeight() > 0) {
                            $data = array_merge($data, [
                                'weight' => floatval(Weight::input($cartItem->getWeight())),
                            ]);
                        }
                    } else {
                        $data = array_merge($data, $this->sortPiecesSizes([
                            'width' => $cartItem->getWidth(),
                            'height' => $cartItem->getLength(),
                            'depth' => $cartItem->getDepth(),
                        ]));
                        if ($cartItem->getWeight() > 0) {
                            $data = array_merge($data, [
                                'weight' => $cartItem->getWeight(),
                            ]);
                        }
                    }

                    $result[] = $data;
                }

                return $result;
            }, $allItemsWithDimensions));

            if (!empty($allItemsWithoutDimensions)) {
                $description = [];
                $weight = 0;
                foreach ($allItemsWithoutDimensions as $other) {
                    $description[] = $other->getName();
                    /** @var ItemContract $cartItem */
                    $cartItem = $other->getParameter('item');

                    if (site('unit_system') == 'metric') {
                        $weight += Weight::input(($cartItem->getWeight() ?: Format::toIntegerWeight($this->getDefaultWeight())) * $cartItem->quantity);
                    } else {
                        $weight += $cartItem->getWeight() * $cartItem->quantity;
                    }
                }

                $allItems->push([
                    [
                        'name' => 'Other',
                        'description' => implode(', ', $description),
                        'weight' => floatval($weight),
                    ]
                ]);
            }

            return $allItems->collapse();
        }

        return $pieces;
    }

    /**
     * @param array $data
     * @return mixed
     */
    protected function sortPiecesSizes(array $data)
    {
        rsort($data);
        $tmp = [];
        foreach (['height', 'depth', 'width'] as $k) {
            if (!empty($value = array_shift($data)) && is_numeric($value) && $value > 0) {
                $tmp[$k] = $value;
            }
        }

        return count($tmp) === 3 ? $tmp : null;
    }

    #[\Override]
    public function supportsBoxingParcel(): bool
    {
        return true;

    }

    /**
     * @param array $parameters
     * @return mixed
     */
    public function createBillOfLadingRequest(array $parameters)
    {
        return $this->manager->createRequest(CreateBillOfLadingRequest::class, $parameters);
    }

    #[\Override]
    public function supportsValidateAddress(): bool
    {
        return false;
    }


    /**
     * @inheritdoc
     */
    #[\Override]
    public function listCountries(): Collection
    {
        return Countries::orderBy($this->manager->getLanguageCode() == 'bg' ? 'name' : 'nameEn')
            ->get()->map(fn(Countries $country): \Omniship\Address\Country => $this->formatCountry($country));
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    public function findCountries($name)
    {

    }

    /**
     * @param $name
     * @return Collection|mixed|Country|null
     * @throws Error
     */
    public function getCountryByIso2($name)
    {
        $result = $this->cache(__FUNCTION__ . '.' . $this::APP_KEY . '.' . $name, function () use ($name): ?\Omniship\Address\Country {
            $country = Countries::where('isoAlpha2', $name)->first();
            if (!$country) {
                return null;
            }

            return $this->formatCountry($country);
        });
        return $this->cacheForget(__FUNCTION__ . '.' . $this::APP_KEY . '.' . $name, $result);
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    public function getCountryByIso3($name)
    {
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    public function findCountryByName($name)
    {
    }

    /**
     * @param $country_id
     * @return Collection|mixed|Country|null
     * @throws Error
     */
    public function getCountryById($country_id)
    {
        $result = $this->cache(__FUNCTION__ . '.' . $this::APP_KEY . '.' . $country_id, function () use ($country_id): ?\Omniship\Address\Country {
            $country = Countries::where('id', $country_id)->first();
            if ($country === false) {
                return null;
            }

            return $this->formatCountry($country);
        });
        return $this->cacheForget(__FUNCTION__ . '.' . $this::APP_KEY . '.' . $country_id, $result);
    }

    /**
     * @param mixed $name
     * @param mixed $country_id
     * @return mixed
     */
    public function findStates($name, $country_id): void
    {
        // TODO: Implement findStates() method.
    }

    /**
     * @param mixed $name
     * @param mixed $country_id
     * @param mixed $state
     * @return mixed
     */
    public function findCities($name, $country_id, $state = null): void
    {
        // TODO: Implement findCities() method.
    }

    /**
     * @param $name
     * @param $country_id
     * @return Collection|mixed|City|null
     * @throws Error
     */
    public function findCityByName($name, $country_id)
    {
        $result = $this->cache(__FUNCTION__ . '.' . $this::APP_KEY . '.' . $name . '.' . $country_id, function () use ($name, $country_id): ?\Omniship\Address\City {
            $name = $this->translateCityName($name);
            $city = Cities::where('countryId', $country_id)->where(function ($c) use ($name): void {
                $c->where('name', 'like', '%' . $name . '%')->orWhere('nameEn', '%' . $name . '%');
            })->first();

            if (!$city) {
                return null;
            }

            return new City([
                'id' => $city->id,
                'name' => $city->name,
                'post_code' => $city->postCode,
                'country_id' => $city->countryId,
            ]);
        });
        return $this->cacheForget(__FUNCTION__ . '.' . $this::APP_KEY . '.' . $name . '.' . $country_id, $result);
    }

    /**
     * @param $post_code
     * @param $name
     * @param $country_id
     * @return Collection|mixed|City|null
     * @throws Error
     */
    public function findCityByPostCode($post_code, $name, $country_id)
    {
        $result = $this->cache(__FUNCTION__ . '.' . $this::APP_KEY . '.2' . $post_code . '.' . $name . '.' . $country_id, function () use ($post_code, $country_id, $name) {
            $city = PostCodes::with('cities')->where('postCode', $post_code)->whereHas('cities', function ($city) use ($name, $country_id): void {
                $city->where(function ($c) use ($name): void {
                    $c->where('name', 'like', '%' . $name . '%')->orWhere('nameEn', 'like', '%' . $name . '%');
                })->where('countryId', $country_id);
            })->first();

            if (!$city) {
                return null;
            }

            $getCity = $city->cities->first();
            if ($city && $getCity) {
                return new City([
                    'id' => $city->cityId,
                    'name' => $getCity->name,
                    'post_code' => $post_code,
                    'country_id' => $country_id,
                ]);
            }
        });
        return $this->cacheForget(__FUNCTION__ . '.' . $this::APP_KEY . '.' . $post_code . '.' . $name . '.' . $country_id, $result);
    }

    /**
     * @param mixed $city_id
     * @return mixed
     */
    public function getCityById($city_id)
    {
        /** @var Cities $city */
        $city = $this->cache(__FUNCTION__ . '.new.' . $city_id, fn() => Cities::with('country')->find($city_id));
        $result = null;
        if ($city) {
            $result = $this->formatCity($city);
        }

        return $this->cacheForget(__FUNCTION__ . '.new.' . $city_id, $result);
    }

    /**
     * @param mixed $name
     * @param mixed $city_id
     * @return mixed
     */
    public function findStreets($name, $city_id): void
    {
//        $street = $this->getClient()->GetStreet($city_id, $name);
//        $result = $this->cache(__FUNCTION__ . '.' . '.' . $name . '.' . $city_id, function () use ($name, $city_id, $street) {
//
//            if ($street === false) {
//                return null;
//            }
//            $city = array_first($cities);
//            return new City([
//                'id' => $city->id,
//                'name' => $city->name,
//                'post_code' => $post_code,
//                'country_id' => $country_id,
//            ]);
//        });
//        return $this->cacheForget(__FUNCTION__ . '.' . $name, $result);
    }

    /**
     * @param mixed $name
     * @param mixed $city_id
     * @return mixed
     */
    public function findQuarters($name, $city_id): void
    {
        // TODO: Implement findQuarters() method.
    }

    /**
     * @param $name
     * @param $country_id
     * @param $city_id
     * @return Collection|mixed|Office[]|null
     * @throws Error
     */
    public function findOffices($name = null, $city_id = null)
    {
        /** @var \ResultOfficeEx[] $offices */
        $offices = $this->cache(__FUNCTION__ . '.' . $this::APP_KEY . '.' . $name . '.' . $city_id, function () use ($name, $city_id): \Illuminate\Support\Collection {
            $offices = Offices::with(['country', 'city'])->office()->where(function ($office) use ($name): void {
                $office->where('name', 'like', '%' . $name . '%')
                    ->orWhere('nameEn', 'like', '%' . $name . '%')
                    ->orWhere('id', 'like', '%' . $name . '%');
            });
            if ($city_id) {
                $offices = $offices->where('cityId', $city_id);
            }

            $offices = $offices->get();
            if (!$offices) {
                throw new Error('Offices not found');
            }

            $result = new Collection();
            foreach ($offices as $office) {
                $result->push($this->formatOfficeLocker($office));
            }

            return $result;
        });

        return $this->cacheForget(__FUNCTION__ . '.' . $this::APP_KEY . '.' . $name . '.' . $city_id, $offices);
    }

    /**
     * @param $office_id
     * @return Office
     * @throws Error
     */
    public function getOfficeById($office_id): \Omniship\Address\Office
    {
        return $this->formatOfficeLocker(Offices::with(['country', 'city'])->where('id', $office_id)->first());
    }

    /**
     * @return array
     * @throws Error
     */
    public function getSupportType(): array
    {
        $support_types = [];
        if ($this->getSetting('to_address', $this->getSetting('to_door'))) {
            $support_types[] = static::SUPPORT_ADDRESS;
        }

        if ($this->getSetting('to_office')) {
            $support_types[] = static::SUPPORT_OFFICE;
        }

        if ($this->getSetting('to_locker')) {
            $support_types[] = static::SUPPORT_LOCKERS;
        }

        return $support_types;
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function getServices()
    {
        if ($this->supportsGetServices()) {
            if ($clientServices = $this->getSetting('client_services')) {
                return $clientServices;
            }

            $services = $this->manager->getServices()->send();

            $this->_latest_request = $services->getRequest();
            if ($services->getCode()) {
                throw new Error($services->getMessage(), $services->getCode());
            }

            $clientServices = $services->getData();
            $this->updateSetting('client_services', $clientServices);
            return $this->getSetting('client_services');

        }

        return collect();
    }

    /**
     * @param $id
     * @return Address
     * @throws Error
     */
    #[\Override]
    public function getSenderAddress($id = null)
    {
        if (!$this->isInstalled()) {
            return;
        }
        $address = new Address();
        $clientAddresses = $this->getSetting('client_addresses_' . $this->getSetting('client_id'));
        if (!$clientAddresses) {
            $clientAddresses = $this->getClient()->getClienAdrresses($this->getSetting('client_id'));
            $this->updateSetting('client_addresses_' . $this->getSetting('client_id'), $clientAddresses);
            $clientAddresses = $this->getSetting('client_addresses_' . $this->getSetting('client_id'));
        }

        $getAddress = collect($clientAddresses)->first();
        $address->setId($this->getSetting('address_id'));
        if ($this->getSetting('pickup') == 'address' && !empty($getAddress['address'])) {
            if (!$getAddress) {
                return $address;
            }

            $address->setCountry(['id' => $getAddress['address']['countryId']]);
            $address->setCity([
                'id' => $getAddress['address']['siteId'],
                'name' => $getAddress['address']['siteName'],
            ]);
            if (!empty($getAddress['address']) && !empty($getAddress['address']['streetId'])) {
                $address->setStreet(['id' => $getAddress['address']['streetId'], 'name' => $getAddress['address']['streetName']]);
            }

            if (!empty($getAddress['address']) && !empty($getAddress['address']['streetNo'])) {
                $address->setStreetNumber($getAddress['address']['streetNo']);
            }

            $address->setPostCode($getAddress['address']['postCode']);
            $address->setLatitude($getAddress['address']['x']);
            $address->setLongitude($getAddress['address']['y']);
            $explode_name = explode(' ', (string)$this->getSetting('client_name'));
            $address->setFirstName($explode_name[0]);
            if (!empty($explode_name[1])) {
                $address->setLastName($explode_name[1]);
            }

            $address->setPhone($this->getSetting('client_phone'));
            $address->setAddress1($getAddress['address']['fullAddressString']);
        } elseif ($this->getSetting('pickup') == 'office') {
            $office = $this->getOfficeById($this->getSetting('office_id'));
            if ($office) {
                $address->setOffice($office);
                if ($office->getCountry() !== null) {
                    $address->setCountry($office->getCountry());
                }

                if ($office->getCity() !== null) {
                    $address->setCity($office->getCity());
                }

                $address->setPostCode($office->getPostCode());

                $address->setPhone($this->getSetting('client_phone'));
                $explode_name = explode(' ', (string)$this->getSetting('client_name'));
                $address->setFirstName($explode_name[0]);
                if (!empty($explode_name[1])) {
                    $address->setLastName($explode_name[1]);
                }
            }
        }

        return $address;
    }

    /**
     * @param $parameter
     * @return mixed
     * @throws Error
     */
    public function getReceiverAddress($parameter)
    {
        $cityName = $parameter->getCity() ? $parameter->getCity()->getName() : null;
        $postCode = $parameter->getPostCode();
        if ($postCode) {
            $parameter->setPostCode(str_replace(' ', '', $postCode));
        }
        if ($parameter->getCountry()) {
            $parameter->setCountry($this->getCountryByIso2($parameter->getCountry()->getIso2()));
        }
        if ($postCode) {
            $city = $this->findCityByPostCode(str_replace(' ', '', $parameter->getPostCode()), $cityName, $parameter->getCountry()->getId());
            if ($city) {
                $parameter->setCity($city);
            }
        }
        return $parameter;
    }

    /**
     * @param $country
     * @return Country|null
     */
    protected function formatCountry($country): \Omniship\Address\Country
    {
        if (empty($country)) {
            return new Country();
        }

        return new Country([
            'id' => $country->id,
            'name' => $this->manager->getLanguageCode() == 'bg' ? $country->name : $country->nameEn,
            'iso2' => $country->isoAlpha2,
            'iso3' => $country->isoAlpha3,
            'currency' => $country->currencyCode,
            'has_cities' => false
        ]);
    }

    /**
     * Get payer side
     * @param bool $with_meta
     * @return string
     */
    #[\Override]
    public function getPayerSide(bool $with_meta = true)
    {
        $side = parent::getPayerSide($with_meta);
        if ($side == Consts::PAYER_RECEIVER && ($address = $this->getShippingAddress()) && strtoupper((string)$address->getCountryIso2()) != 'BG') {
            $side = Consts::PAYER_SENDER;
        }

        return $side;
    }

    #[\Override]
    public function getPayerSideFromConfig()
    {
        $side = parent::getPayerSideFromConfig();
        if ($side == Consts::PAYER_RECEIVER && ($address = $this->getShippingAddress()) && strtoupper((string)$address->getCountryIso2()) != 'BG') {
            $side = Consts::PAYER_SENDER;
        }

        return $side;
    }


    /**
     * @return bool
     * @throws Error
     */
    #[\Override]
    public function supportsCashOnDelivery(): bool
    {
        return parent::supportsCashOnDelivery() && $this->getSetting('cd') && $this->compareMaxCodPrice();
    }

    /**
     * @return bool
     * @throws Error
     * @throws \Exception
     */
    protected function compareMaxCodPrice()
    {
        $sub_total = $this->getOrder() ? $this->getOrder()->getSubTotal('input') : 0;
        if (!$sub_total) {
            return true;
        }

        if (site('currency') == 'BGN') {
            $max_cod = static::BG_MAX_COD;
        } else {
            $max_cod = static::UNLIMITED_MAX_COD;
        }

        $cod_allow_price = $this->getSetting('cd_max') > 0 ? min($this->getSetting('cd_max'), $max_cod) : $max_cod;
        return $sub_total <= $cod_allow_price;
    }


    /**
     * Supports recalculate shipping
     *
     * @return boolean
     * @throws Error
     */
    #[\Override]
    public function supportsRecalculateShippingOnPaymentChange(): bool
    {
        return $this->supportsCashOnDelivery();
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    public function officesAutocomplete($name)
    {
        try {
            $offices = $this->findOffices($name);
        } catch (\Exception) {
            $offices = collect();
        }

        return $offices->take(10);
    }

    /**
     * @return Collection
     */
    #[\Override]
    public function getWaybillSides(): \Illuminate\Support\Collection
    {
        $sides = new Collection();
        //receiver
        $sides->put(Consts::PAYER_RECEIVER, [
            'id' => Consts::PAYER_RECEIVER,
            'name' => 'Receiver'
        ]);
        //sender
        $sides->put(Consts::PAYER_SENDER, [
            'id' => Consts::PAYER_SENDER,
            'name' => 'Sender'
        ]);
        //other
        $sides->put(Consts::PAYER_OTHER, [
            'id' => Consts::PAYER_OTHER,
            'name' => 'Third party'
        ]);

        return $sides;
    }

    #[\Override]
    public function getWaybillSidesLabels(): Collection
    {
        $sides = new Collection();
        //receiver
        $sides->put(Consts::PAYER_RECEIVER, [
            'id' => Consts::PAYER_RECEIVER,
            'name' => 'Receiver'
        ]);
        //sender
        $sides->put(Consts::PAYER_SENDER, [
            'id' => Consts::PAYER_SENDER,
            'name' => 'Sender'
        ]);
        //other
        $sides->put(Consts::PAYER_OTHER, [
            'id' => Consts::PAYER_OTHER,
            'name' => 'Third party'
        ]);

        return $sides;
    }

    #[\Override]
    public function supportsGetPdf(): bool
    {
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function orderWaybillUrl(Order $order): ?string
    {
        return route('apps.' . $this::APP_KEY . '.print_waybill_select', [$order->id]);
    }

    /**
     * {@inheritdoc}
     */
    public function orderWaybillUrlAttr(Order $order): ?string
    {
        return 'data-modal-ajax="' . route('apps.' . $this::APP_KEY . '.print_waybill_select', [$order->id]) . '" data-modal-size="xsmall"';
    }

    /**
     * @param Order $order
     * @return void
     * @throws Error
     * @throws \Throwable
     */
    public function generateOrder(Order $order): void
    {
        $this->setOrder($order);
        $waybillStd = new \stdClass();
        $waybillStd->waybill_id = null;

        $request_response = new \stdClass();

        $allowed_services = (array)$this->getSetting('allowed_methods');
        $service_id = $order->meta_pluck->get('service_id');
        if (!in_array($service_id, $allowed_services)) {
            $service_id = Arr::first($allowed_services);
        }

        $insurance_amount = $order->meta_pluck->get('insurance') ? $order->getInsuranceAmountInput() : 0;
        $cash_on_delivery_amount = 0;
        if ($order->payment->provider == 'cod') {
            if ($order->meta_pluck->get('cod')) {
                $cash_on_delivery_amount = $order->meta_pluck->get('cod_manual');
            } elseif ($order->isCashOnDelivery()) {
                $cash_on_delivery_amount = Format::moneyInput($this->getCashOnDeliveryTotal($order));
            }
            $cash_on_delivery_amount = number_format((float) App\Models\Gateway\Currency::convert($cash_on_delivery_amount, site('currency'), 'BGN'), 2, '.', '');
        }

        if (!empty($order->shippingAddress->office_id)) {
            $reciver_address = new \App\Helper\OmniShip\Address([
                'office' => $this->getOfficeById($order->shippingAddress->office_id),
                'country' => $this->getCountryById($order->shippingAddress->country_id),
            ]);
        } else {
            $reciver_address = $this->getReceiverAddress($order->shippingAddress->address);
        }

        $option_before_payment = $this->getSetting('option_before_payment');
        $instruction_returns = $this->getSetting('instruction_returns');

        $params = [
            //    'shipment_date' => $firs_allowed_tacking_day,
            'transaction_id' => $order->id, //create
            'service_id' => $order->meta_pluck->get('service_id'),
            'client_note' => Str::substr(str_replace(['%', '&'], '', $order->note_customer), 0, 200),
            'content' => '',
            'receiver_email' => $order->customer_email,
            'receiver_address' => $reciver_address,
//            'sender_address' => $this->manager->getSenderAddress(),
            'items' => ($items = $this->convertOrderProductsToBag($order->products)),
            'number_of_pieces' => 1,
            'payer' => $order->meta_pluck->get('side', $this->getPayerSide()),
            'option_before_payment' => $this->getSetting('option_before_payment'),
            'instruction_returns' => $instruction_returns,
            'back_documents' => $order->meta_pluck->get('back_documents'),
            'back_receipt' => $order->meta_pluck->get('back_receipt'),
            'declared_amount' => $order->meta_pluck->get('totalInsurance'),
            'other_parameters' => [
                'fragile' => $order->meta_pluck->get('fragile'),
                'documents' => $order->meta_pluck->get('documents'),
                'return_pay' => (int)$this->getSetting('instruction_returns'),
                'return_service' => $this->getSetting('instruction_returns_service'),
                'pos_enabled' => $this->getSetting('pos_enabled'),
                'services' => [$order->meta_pluck->get('service_id')],
            ],
            'insurance_amount' => $insurance_amount,
            'cash_on_delivery_amount' => $cash_on_delivery_amount,
            'weight' => Weight::input($order->products->sum('weight')),
            'package_type' => $this->getSetting('packing'),
            'is_documents' => (bool)$this->getSetting('documents'),
            'items_count' => null,
            'pieces' => array_map(function ($data) {
                if (!empty($data->name)) {
                    unset($data->name);
                }

                return $data;
            }, (array)json_decode((string)$order->meta_pluck->get('boxes'))),
        ];

        $params['instruction_returns'] = '';

        $params['receiver_address'] = $this->getReceiverAddress($order->shippingAddress->address);
        $params['sender_address'] = $this->getSenderAddress();
        $result = $this->createBillOfLading($params, $request_response);

        $categories = $order->products->where('digital', YesNo::False)->pluck('category_id')->toArray();
        $result = $this->formatCreate(Format::moneyInput($order->price_total - $order->shipping->order_amount), $result, null, $categories);

        $explode_bolId = explode('-', $result->getBolId());
        $waybillStd->waybill_id = \Illuminate\Support\Arr::first($explode_bolId);


        $services = collect($this->manager->getServices()->toArray())->pluck('name', 'id');
        $order->updateMeta([
            'integration' => $this->getKey(),
            'service_id' => $service_id,
            'service_name' => $services->get($service_id),
            'bol_id' => $waybillStd->waybill_id,
            'total' => Format::toIntegerPrice($result->getTotal()),
            'original_total' => $result->getParameter('original_price') != $result->getTotal() ? Format::toIntegerPrice($result->getParameter('original_price')) : null,
            'currency' => $result->getCurrency(),
            'special_delivery_requirements' => null,
            'cod' => $cash_on_delivery_amount,
            'option_before_payment' => $this->getSetting('option_before_payment'),
            'instruction_returns' => (int)$this->getSetting('instruction_returns'),
            'back_documents' => $order->meta_pluck->get('back_documents'),
            'insurance' => $order->meta_pluck->get('totalInsurance'),
            'fragile' => $order->meta_pluck->get('fragile'),
            'side' => $params['payer'],
            'omniship.credentials' => json_encode($this->getCredentials()),
            'money_transfer' => $this->isMoneyTransfer(),
            'parcels_number' => $result->getBolId()
        ]);

        $fulfilled = $order->changeStatus('fulfilled', true, [
            'shipping_tracking_number' => $waybillStd->waybill_id,
            'shipping_date_delivery' => $result->getEstimatedDeliveryDate(),
            'shipping_date_expedition' => $result->getPickupDate(),
            'shipping_tracking_url' => $this->getSetting('tracking', 'provider') == 'cloudcart' ? \LinkerCp::siteFullLink('/tracking/' . $waybillStd->waybill_id) : $this->trackingUrl($waybillStd->waybill_id)
        ]);
        if ($fulfilled && $fulfilled instanceof OrderFulfillment) {
            $fulfilled->update([
                'shipping_tracking_number' => $waybillStd->waybill_id,
                'shipping_date_delivery' => $result->getEstimatedDeliveryDate(),
                'shipping_date_expedition' => $result->getPickupDate(),
                'shipping_tracking_url' => $this->getSetting('tracking', 'provider') == 'cloudcart' ? \LinkerCp::siteFullLink('/tracking/' . $waybillStd->waybill_id) : $this->trackingUrl($waybillStd->waybill_id)
            ]);
        }
    }

    /**
     * @param $name
     * @param $city_id
     * @return Collection|mixed|Office[]|null
     * @throws Error
     */
    public function findLockers($name, $city_id = null)
    {
        /** @var \ResultOfficeEx[] $offices */
        $lockers = $this->cache(__FUNCTION__ . '.' . $this::APP_KEY . '.' . '.' . $name . '.' . $city_id, function () use ($name, $city_id) {
            $offices = Offices::locker()->with(['country', 'city'])->where(function ($locker) use ($name): void {
                $locker->where('name', 'like', '%' . $name . '%')->orWhere('nameEn', 'like', '%' . $name . '%');
            })->get();
            return $offices;
        });

        $result = collect($lockers)->map(function ($office): ?\Omniship\Address\Office {
            if ($office->type == 'APT') {
                return $this->formatOfficeLocker($office);
            }

            return null;
        })->filter();

        return $this->cacheForget(__FUNCTION__ . '.' . $this::APP_KEY . '.' . '.' . $name . '.' . $city_id, $result);
    }

    /**
     * @param $office
     * @return Office
     * @throws Error
     */
    protected function formatOfficeLocker($office): \Omniship\Address\Office
    {
        if (!$office) {
            return new Office();
        }

        $country = $this->formatCountry($office->country);
        return new Office([
            'id' => $office->id,
            'name' => '[' . $office->id . '] ' . $office->name,
            'address_string' => $office->address,
            'max_weight' => $office->max_weight,
            'weight_unit' => MassUnit::KILOGRAM,
            'max_parcel_dimensions' => [
                'width' => $office->max_width,
                'height' => $office->max_height,
                'length' => $office->max_depth
            ],
            'dimension_unit' => LengthUnit::CENTIMETRE,
            'latitude' => $office->y ?? null,
            'longitude' => $office->x ?? null,
            'type' => $office->type == 'APT' ? CONSTS::OFFICE_TYPE_APT : CONSTS::OFFICE_TYPE_OFFICE,
            'phones' => static::PHONES,
            'country_id' => $office->countryId,
            'country' => $country,
            'city_id' => $office->cityId,
            'city' => $this->formatCity($office->city, $country),
            'provider' => $this::APP_KEY,
            'post_code' => $office->postCode,
        ]);
    }

    /**
     * @param $city
     * @param Country|null $country
     * @return City|null
     * @throws Error
     */
    protected function formatCity($city, ?Country $country = null): ?\Omniship\Address\City
    {
        if (!$city) {
            return null;
        }

        return new City([
            'id' => $city->id,
            'post_code' => $city->postCode,
            'address_nomenclature' => $city->addressNomenclature,
            'type' => $this->mapCityType($city->type),
            'name' => $city->name,
            'country_id' => $city->countryId,
            'country' => $country ?: $this->getCountryById($city->country),
        ]);
    }

    /**
     * @param $type
     * @return string
     */
    protected function _translateOfficeType($type)
    {
        if ($type === 3) {
            $type = Consts::OFFICE_TYPE_APT;
        } elseif ($type === 0) {
            $type = Consts::OFFICE_TYPE_OFFICE;
        }

        return $type;
    }

    /**
     * @param $lockerId
     * @return Office
     * @throws Error
     */
    public function getLockerById($lockerId): \Omniship\Address\Office
    {
        return $this->formatOfficeLocker(Offices::with(['country', 'city'])->where('id', $lockerId)->first());
    }

    /**
     * @param $name
     * @return void
     */
    public function lockersAutocomplete($name)
    {
        try {
            $offices = $this->findOffices($name);
        } catch (\Exception) {
            $offices = collect();
        }

        return $offices->take(10);
    }

    /**
     * @param $isMachine
     * @return Collection
     * @throws Error
     */
    public function allOffices($isMachine): Collection
    {
        $isMachine = is_null($isMachine) ? $isMachine : intval($isMachine);
        $defaultCountry = $this::APP_KEY == 'dpdbulgaria' ? [100] : [642];


        if ($isMachine) {
            $countries = $this->getSetting('locker_countries', $defaultCountry);
        } else {
            $countries = $this->getSetting('office_countries', $defaultCountry);
        }

        $offices = $this->cache(__FUNCTION__ . '.' . $this::APP_KEY . '.' . $isMachine . '.' . $this->getManager()->getLanguageCode() . '.' . implode('', $countries), function () use ($isMachine, $countries) {
            if ($isMachine) {
                $offices = Offices::locker()->with(['country', 'city'])->whereIn('countryId', $countries)->get();
            } else {
                $offices = Offices::office()->with(['country', 'city'])->whereIn('countryId', $countries)->get();
            }

            return $offices->map(fn($office): \Omniship\Address\Office => $this->formatOfficeLocker($office));
        });

        return $this->cacheForget(__FUNCTION__ . '.' . $this::APP_KEY . '.' . $isMachine . '.' . $this->getManager()->getLanguageCode() . '.' . implode('', $countries), $offices);
    }

    /**
     * @param $type
     * @return string
     */
    protected function mapCityType($type)
    {
        return match ($type) {
            'гр.', 'or.' => 'city',
            'с', 's.' => 'village',
            default => $type ?: 'city',
        };
    }


    /**
     * @inheritdoc
     */
    #[\Override]
    protected function getProviderCustomSettings(): array
    {
        $defaultCountry = $this::APP_KEY == 'dpdbulgaria' ? [100] : [642];
        $settings = [
            'username' => $this->getSetting('username'),
            'password' => $this->getSetting('password'),
            'pickup' => $this->getSetting('pickup', 'address'),
            'address_id' => intval($this->getSetting('address_id')),
            'client_name' => $this->getSetting('client_name'),
            'client_phone' => $this->getSetting('client_phone'),
            'office_id' => $this->getSetting('office_id'),
            'office_name' => $this->getSetting('office_name'),
            'allowed_methods' => $this->getSetting('allowed_methods', []),
            'option_before_payment' => $this->getSetting('option_before_payment'),
            'instruction_returns' => intval($this->getSetting('instruction_returns')),
            'instruction_returns_service' => $this->getSetting('instruction_returns_service'),
            'default_weight' => $this->getSetting('default_weight'),
            'default_width' => $this->getSetting('default_width'),
            'default_height' => $this->getSetting('default_height'),
            'default_depth' => $this->getSetting('default_depth'),
            'packing' => $this->getSetting('packing'),
            'boxes' => $this->getSetting('boxes'),
            'side' => $this->getSetting('side'),
            'cd' => intval($this->getSetting('cd')),
            'declared_value' => intval($this->getSetting('declared_value')),
            'back_documents' => intval($this->getSetting('back_documents')),
            'back_receipt' => intval($this->getSetting('back_receipt')),
            'label_printer' => intval($this->getSetting('label_printer')),
            'insurance' => intval($this->getSetting('insurance')),
            'pos_enabled' => intval($this->getSetting('pos_enabled')),
            'fragile' => intval($this->getSetting('fragile')),
            'documents' => intval($this->getSetting('documents')),
            'order_content' => $this->getSetting('order_content', 'name'),
            'item_sizes' => $this->getSetting('item_sizes'),
            'speedy_print_size' => $this->getSetting('speedy_print_size', 'ALL'),
            'money_transfer' => $this->getSetting('money_transfer'),
            'return_voucher' => $this->getSetting('return_voucher'),
            'return_voucher_service' => $this->getSetting('return_voucher_service'),
            'return_voucher_validity' => $this->getSetting('return_voucher_validity'),
            'return_voucher_payer' => $this->getSetting('return_voucher_payer'),
            'office_countries' => $this->getSetting('office_countries', $defaultCountry),
            'locker_countries' => $this->getSetting('locker_countries', $defaultCountry),
            'fiscal_receipt' => $this->getSetting('fiscal_receipt', 0),
            'payer_id' => $this->getSetting('payer_id'),
            'cod_ref2' => $this->getSetting('cod_ref2', 0),
        ];
        if ($this->getSetting('pickup', 'address') == 'address') {
            $settings['address_id'] = intval($this->getSetting('address_id'));
        }

        if ($this->getSetting('pickup', 'address') == 'office') {
            $settings['office_id'] = intval($this->getSetting('office_id'));

            $settings['office_input'][] = [
                'id' => $this->getSetting('office_id'),
                'name' => $this->getOfficeById($this->getSetting('office_id')) ? $this->getOfficeById($this->getSetting('office_id'))->getName() : '',
            ];
        }

        return $settings;
    }

    #[\Override]
    public function supportsCodPayment(): bool
    {
        return true;
    }

    /**
     * @return true
     */
    #[\Override]
    public function supportBulkPrintWaybill(): bool
    {
        return true;
    }

    /**
     * @param \App\Helper\OmniShip\Address $address
     * @return Address|void
     * @throws Error
     */
    public function translateAddress(Address $address): Address
    {
        if ($this::APP_KEY != 'dpdbulgaria') {
            return $address;
        }

        if (in_array(site('language'), ['bg', 'en']) || !hasGoogleMapKey()) {
            return $address;
        }

        $neighborhoodRemove = ['g.k.'];
        $streetRemove = ['ul.', 'street', 'ulitsa', 'str.'];

        try {
            $map = new Map();
            $map = $map->setLanguage('en');
            $getAddress = $map->getFirstLocation($address->getText());
            if ($getAddress) {
                $components = $getAddress->getAddressComponents()->toArray();
                $address->setNeighborhood(trim(str_ireplace($neighborhoodRemove, '', $components['neighborhood'])));
                $address->setStreet(['name' => trim(str_ireplace($streetRemove, '', $components['route']))]);
                if ($components['locality']) {
                    $getCity = $address->getCity()->toArray();
                    $getCity['name'] = $components['locality'];
                    if ($address->getCountry()->getIso2() == 'GR') {
                        $findCity = $this->getClient()->searchCities($components['locality'] . ' ' . str_replace(' ', '', $address->getPostCode()), null, 300, 'en');
                        $findCity = \Illuminate\Support\Arr::first($findCity);
                        if ($findCity) {
                            $getCity['name'] = $findCity->getName();
                            $getCity['id'] = $findCity->getId();
                            $stateName = $findCity->getRegion();
                        }
                    }

                    $address->setCity($getCity);
                }

                if ($components['country']) {
                    $getCountry = $address->getCountry()->toArray();
                    $getCountry['name'] = $components['country'];
                    $address->setCountry($getCountry);
                }

                if ($components['administrative_area_level_1']) {
                    $getState = $address->getState()->toArray();
                    $getState['name'] = $components['administrative_area_level_1'];
                    $address->setState($getState);
                }

                if (!empty($stateName)) {
                    $getState = $address->getState()->toArray();
                    $getState['name'] = $stateName;
                    $address->setState($getState);
                }

                if ($components['locality']) {
                    $address->setLocality($components['locality']);
                }

                $address->setText($getAddress->getFormattedAddress());
            }
        } catch (\Throwable) {
            return $address;
        }

        return $address;
    }

    /**
     * @param $name
     * @return array|\ArrayAccess|mixed
     * @throws Error
     */
    public function translateCityName($name)
    {
        if ($this::APP_KEY != 'dpdbulgaria') {
            return $name;
        }

        if (in_array(site('language'), ['bg', 'en']) || !hasGoogleMapKey()) {
            return $name;
        }


        try {
            $map = new Map();
            $map = $map->setLanguage('en');
            $getAddress = $map->getFirstLocation($name);
            if ($getAddress) {
                $address = $getAddress->getAddressComponents()->toArray();
                $name = Arr::get($address, 'locality');
            }
        } catch (\Throwable) {
            return $name;
        }

        return $name;
    }

    /**
     * @return true
     */
    #[\Override]
    public function supportDebugBillOfLading(): bool
    {
        return true;
    }

    /**
     * @return array|false|mixed|string
     * @throws Error
     */
    public function getContractInfo()
    {
        if ($contractInfo = $this->getSetting('contract_info')) {
            return $contractInfo;
        }

        $contractInfo = $this->getClient()->getContractInfo();
        $this->updateSetting('contract_info', $contractInfo);
        $encode = json_encode($contractInfo);
        return json_decode($encode, true);
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function validateCredentials(array $parameters = [], $test = null)
    {
        if ($this->supportsValidateCredentials()) {
            if (($this->getSetting('username') != $parameters['username']) || $this->getSetting('password') != $parameters['password']) {
                $this->removeSettings(['contract_info', 'client_address', 'client_services', 'client_addresses_']);
            }

            $validate = $this->setTestMode($test)->manager->validateCredentials($parameters)->send();
            $this->_latest_request = $validate->getRequest();
            if ($validate->getMessage()) {
                throw new Error(__('shipping.err.incorrect_user_and_pass'));
            }

            return $validate->getData();
        }

        return true;
    }

    /**
     * @param $taxValue
     * @return string|null
     */
    public function getVatGroup($taxValue): ?string
    {
        return match ($taxValue) {
            0 => 'А',
            9 => 'Г',
            20 => 'Б',
            default => null,
        };
    }
}

