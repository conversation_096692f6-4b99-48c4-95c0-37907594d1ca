<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 22.6.2017 г.
 * Time: 15:23 ч.
 */

namespace Modules\Apps\Shippings\Omniship;

use App;
use App\Contracts\GroupedAppsContract;
use App\Contracts\OmniShipContract;
use App\Contracts\Subscriptions\MoreRecordsSubscription;
use App\Helper\ArrayCache;
use App\Helper\GeoZone\GeoZoneFilterInformation;
use App\Helper\TimeZone;
use App\Integration\OmniShip\Internal\Manager as InternalManager;
use App\Locale\Country;
use App\Models\GeoZone\GeoZoneValues;
use App\Models\Queue\SiteQueue;
use App\Models\Shipping\ShippingProvider;
use App\Models\Store\Cart as CartModel;
use Apps;
use ArrayAccess;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\ServiceProvider;
use InvalidArgumentException;
use Modules\Apps\Shippings\ShippingHours\ShippingHoursManager;
use Throwable;

class OmniShipService implements ArrayAccess, GroupedAppsContract, MoreRecordsSubscription
{
    /**
     * @var string
     */
    public const PROVIDERS_NAMESPACE = '\\Modules\\Apps\\Shippings\\%s\\%sServiceProvider';

    /**
     * @var array
     */


    protected $internals = [
        'acscourier' => 'Acscourier',
        'speedy' => 'Speedy',
        'econt' => 'Econt',
        'dhl' => 'Dhl',
        'rapido' => 'Rapido',
        'fancourier' => 'Fancourier',
        'berry' => 'Berry',
        'cargus' => 'Cargus',
        'elslogistic' => 'Elslogistic',
        'evropat' => 'Evropat',
        'dpdromania' => 'Dpdromania',
        'glovo' => 'Glovo',
        'eushipment' => 'Eushipment',
        'albanian_courier' => 'AlbanianCourier',
        'speedex' => 'Speedex',
        'mikmik' => 'MikMik',
        'sameday' => 'Sameday',
        'boxnow' => 'Boxnow',
        'ultracep' => 'Ultracep',
        'ntclogistics' => 'Ntclogistics',
        'tcscourier' => 'Tcscourier',
        'dhlexpress' => 'Dhlexpress',
        'sendcloud' => 'Sendcloud',
        'dpdbulgaria' => 'Dpdbulgaria',
        'gls' => 'Gls',
        'dexpress' => 'Dexpress',
    ];

    /**
     * @var array
     */
    protected $excluded = [
        'eushipment',
        'sendcloud'
    ];

    /**
     * @var array
     */
    protected $externals = [];

    /**
     * @var Collection
     */
    protected $providers;

    /**
     * @var Collection
     */
    protected $external_providers;

    /**
     * @var array
     */
    protected $countries_limit;

    /**
     * @param Illuminate\Foundation\Application $app
     * @return mixed
     */
    public function __construct(protected \Illuminate\Foundation\Application $app)
    {
        $this->reset();
    }

    /**
     * @return $this
     */
    public function reset(): static
    {
        $loaded = array_keys(array_merge((array)$this->internals, (array)$this->externals));
        $this->externals = [];
        $this->providers = null;
        $this->external_providers = null;
        foreach ($loaded as $key) {
            if ($this->app->bound('omniship.' . $key)) {
                $this->app->offsetUnset('omniship.' . $key);
            }
        }

        $this->registerProviders();
        $this->registerExternalProviders();

        return $this;
    }

    /**
     * @return array
     */
    public function getInternals()
    {
        if (App::runningInConsole() || preg_match('!apps\.(' . implode('|', array_keys($this->internals)) . ')\.install!', strval(activeRoute()))) {
            return $this->internals;
        }

        return array_intersect_key($this->internals, $this->getExternalProviders()->all());
    }

    /**
     * @return array
     */
    public function getExternals()
    {
        return $this->externals;
    }

    /**
     * @param $key
     * @return bool
     */
    public function has($key): bool
    {
        $key = $this->normaliseKey($key);
        return array_key_exists($key, $this->getInternals()) || array_key_exists($key, $this->getExternals());
    }

    /**
     * @param $key
     * @return \Modules\Apps\Shippings\Omniship\AbstractManager|null
     * @throws Exception
     */
    public function get($key): ?\App\Contracts\OmniShipContract
    {
        $key = $this->normaliseKey($key);
        if ($this->has($key) && $this->app->bound('omniship.' . $key)) {
            if (($provider = $this->app->make('omniship.' . $key)) instanceof OmniShipContract) {
                return $provider;
            }
        }

        return null;
    }

    /**
     * @param null|boolean $active
     * @param \App\Helper\GeoZone\GeoZoneFilterInformation $geo_zone_information
     * @param mixed $geoZoneToCity
     * @return \Modules\Apps\Shippings\Omniship\AbstractManager[]|\Illuminate\Support\Collection
     */
    public function all($active = null, ?GeoZoneFilterInformation $geo_zone_information = null, $geoZoneToCity = false)
    {
        return Collection::make(array_merge($this->getInternals(), $this->getExternals()))
            ->forget($this->excluded)
            ->map(function ($manager, $key) use ($geo_zone_information) {
                if (is_string($manager)) {
                    if (is_null($provider = $this->getExternalProviders()->get($key))) {
                        return;
                    }

                    return $this->get($key)->setProvider($provider);
                }

                return $manager;
            })->filter()->filter(function (AbstractManager $manager) use ($active, $geo_zone_information, $geoZoneToCity) {
                if (is_null($active)) {
                    return true;
                } elseif ($active) {
                    return $geoZoneToCity ? $manager->isActiveWithGeoZoneToCity($geo_zone_information) : $manager->isActiveWithGeoZone($geo_zone_information);
                } else {
                    return !$manager->isActive();
                }
            });
    }

    /**
     * @return AbstractManager[]|Collection
     */
    public function allEnabledSyncPayments()
    {
        return $this->all()->filter(fn(AbstractManager $manager) => $manager->enableSyncPayments());
    }

    /**
     * @param null $active
     * @param bool $countries
     * @return array
     */
    public function getCountriesLimits($active = null, $countries = false)
    {
        if (!is_null($this->countries_limit) && !$countries) {
            return $this->countries_limit;
        }

        /** @var ShippingProvider[]|Collection $providers */
        $providers = $this->all($active)->map(fn(AbstractManager $manager) => $manager->getProvider([]));

        if ($countries) {
            $providers = $providers->where('active', 1);
        }

        if ($providers->where('geo_zone_id', null)->count()) {
            return $this->countries_limit = [];
        }

        /** @var Collection|GeoZoneValues[] $geo_zones_values */
        $geo_zones_values = $providers->map(fn(ShippingProvider $provider) => $provider->geo_zone_values->where('operation', '!=', GeoZoneValues::OPERATION_POLYGON))->collapse();

        if ($providers->count() > 1) {
            if ($geo_zones_values->where('operation', 3)->count() || ($geo_zones_values->where('operation', 1)->count() && $geo_zones_values->where('operation', 4)->count())) {
                return $this->countries_limit = [];
            }
        }

        $geo_zones_values = $geo_zones_values->keyBy('country_iso2');
        if ($geo_zones_values->count() > 5 && hasGoogleMapKey()) {
            return $this->countries_limit = [];
        }

        $isNotCountries = $geo_zones_values->where('operation', 4)->pluck('country_iso2');
        if ($isNotCountries->isNotEmpty()) {
            $geo_zones_values = collect(App\Locale\Country::all())
                ->forget($isNotCountries->all())->map(fn($country): array => [
                    'iso' => $country['code'],
                    'name' => $country['localized_name'],
                ]);
        } else {
            $geo_zones_values = $geo_zones_values->map(fn(GeoZoneValues $value): array => [
                'iso' => $value->country_iso2,
                'name' => Country::get($value->country_iso2, locale())['localized_name'],
            ])->values();
        }

        if ($countries) {
            return $geo_zones_values->values()->all();
        }

        return $this->countries_limit = $geo_zones_values->values()->all();
    }

    /**
     * @param null $active
     * @param bool $all
     * @return array
     */
    public function getCountries($active = null, bool $all = false): array
    {
        if (count($limits = $this->getCountriesLimits($active, !$all)) > 0) {
            return collect($limits)->pluck('name', 'iso')->all();
        }

        return collect(Country::all(locale()))->pluck('localized_name', 'code')->all();
    }

    /**
     * @param OmniShipContract $object
     * @return $this
     * @throws InvalidArgumentException
     */
    public function set(OmniShipContract $object): static
    {
        $key = $object->getKey();
        //        if($this->has($key)) {
        //@todo check if existing integration
        //throw new InvalidArgumentException(__('omniship.err.provider_is_registered', ['key' => $key]));
        //        }
        $this->externals[$key] = $object;
        $this->app->offsetSet('omniship.' . $key, $object);
        return $this;
    }

    /**
     * @param $key
     * @return OmniShipContract|null
     * @throws Exception
     */
    public function __get($key)
    {
        return $this->get($key);
    }

    /**
     * @param mixed $offset
     * @param mixed $value
     */
    public function offsetSet($offset, $value): void
    {
        $this->set($value);
    }

    /**
     * @param mixed $offset
     * @return bool
     */
    public function offsetExists($offset)
    {
        return $this->has($offset);
    }

    /**
     * @param mixed $offset
     */
    public function offsetUnset($offset): void
    {
        if ($this->has($offset)) {
            $this->app->offsetUnset('omniship.' . $offset);
        }
    }

    /**
     * @param mixed $offset
     * @return AbstractManager|null
     * @throws Exception
     */
    public function offsetGet($offset)
    {
        return $this->get($offset);
    }

    /**
     * @param GeoZoneFilterInformation $geo_zone_information
     * @return Collection
     * @throws Throwable
     */
    public function getSupportTypes(?GeoZoneFilterInformation $geo_zone_information = null)
    {
        $types = [];
        $providers = $this->all(true, $geo_zone_information, true);

        if (app_namespace() == 'site' && ($cartInstance = CartModel::instance()) && $cartInstance->getShippingType()) {
            $active = $cartInstance->getShippingType();
        } elseif (array_key_exists($type = setting('default_shipping_type'), $this->getShippingProvidersTypes())) {
            $active = $type;
        } else {
            $active = null;
        }

        foreach ($this->getShippingProvidersTypes() as $type => $title) {
            foreach ($providers as $provider) {
                if ($provider->isSupportType($type)) {
                    $types[$type] = [
                        'row' => count($types) - 1,
                        'key' => $type,
                        'name' => $title,
                        'active' => $active == $type
                    ];
                }
            }
        }

        return Collection::make($types);
    }

    /**
     * @param GeoZoneFilterInformation $geo_zone_information
     * @return Collection[]|Collection|AbstractManager[]
     * @throws App\Exceptions\Error
     */
    public function getManagersGroupByType(?GeoZoneFilterInformation $geo_zone_information = null): \Illuminate\Support\Collection
    {
        $managers = new Collection();
        $providers = $this->all(true, $geo_zone_information);
        foreach ($this->getShippingProvidersTypes() as $type => $title) {
            foreach ($providers as $provider) {
                if ($provider->isSupportType($type)) {
                    if (!$managers->has($type)) {
                        /** @var Collection $managers */
                        $managers = $managers->put($type, new Collection());
                    }

                    $managers->get($type)->put($provider->getKey(), $provider);
                }
            }
        }

        return $managers;
    }

    /**
     * @return array
     */
    public function getShippingProvidersTypes(): array
    {
        return [
            AbstractManager::SUPPORT_ADDRESS => __('sf.checkout.shipping_items_to_customer_address'),
            AbstractManager::SUPPORT_OFFICE => __('sf.checkout.get_items_from_shipping_provider_office'),
            AbstractManager::SUPPORT_MARKETPLACE => __('sf.checkout.get_items_from_our_store'),
            AbstractManager::SUPPORT_LOCKERS => __('sf.checkout.get_items_from_shipping_provider_locker'),
        ];
    }

    /**
     * @param $country
     * @param $city
     * @return mixed|null
     */
    public function findBestTimezone($country, $city)
    {
        return TimeZone::findTimezone($country, $city);
    }

    /**
     * @return ShippingProvider[]|Collection
     */
    public function getProviders()
    {
        if (!array_key_exists('default', \Illuminate\Support\Facades\DB::getConnections())) {
            return new Collection();
        }

        if (is_null($this->providers)) {
            if (app_namespace() != 'site') {
                $with = [
                    'geo_zone.values.polygon', 'geo_zone_values.polygon',
                    'geo_zone.values.distance', 'geo_zone_values.distance',
                    'addresses.shop', 'rates', 'boxes',
                    'payments', 'meta', 'external_provider'
                ];
                if ($installed = Apps::installed(ShippingHoursManager::APP_KEY)) {
                    $with[] = 'shipping_hours.hours';
                    $with[] = 'shipping_hours_exceptions';
                }

                $this->providers = ShippingProvider::with($with)
                    ->where(function ($query): void {
                        $query->withoutIntegration()
                            ->orWhere(function ($query): void {
                                $query->whereNotNull('integration')
                                    ->whereNotNull('external_id');
                            });
                    })->get()->map(function (ShippingProvider $provider) use ($installed): \App\Models\Shipping\ShippingProvider {
                        $provider->setAttribute('has_delivery_dates', false);
                        $provider->setAttribute('delivery_dates', collect());

                        if ($installed && $provider->shipping_hours->isNotEmpty()) {
                            $provider->setAttribute('has_delivery_dates', $provider->shipping_hours->isNotEmpty());
                            $provider = ShippingHoursManager::formatSiteCp($provider);
                        }

                        $provider->setRelation('shipping_hours', collect());
                        $provider->syncOriginal();
                        return $provider;
                    })->keyBy('omniship_key');
            } else {
                $this->providers = $this->getAllProviders()->filter(function (ShippingProvider $provider): bool {
                    if ($provider->integration && $provider->external_id) {
                        return true;
                    }

                    return empty($provider->integration);
                })->keyBy('omniship_key');
            }
        }

        return $this->providers;
    }

    /**
     * @return ShippingProvider[]|Collection
     */
    public function getExternalProviders()
    {
        if (!array_key_exists('default', \Illuminate\Support\Facades\DB::getConnections())) {
            return new Collection();
        }

        if (is_null($this->external_providers)) {
            if (app_namespace() != 'site') {
                $this->external_providers = ShippingProvider::with([
                    'geo_zone.values.polygon', 'geo_zone_values.polygon',
                    'geo_zone.values.distance', 'geo_zone_values.distance',
                    'addresses.shop', 'rates', 'boxes', 'payments',
                    'meta', 'external_provider'
                ])
                    ->withIntegration()->get()->keyBy('integration');
            } else {
                $this->external_providers = $this->getAllProviders()->filter(fn(ShippingProvider $provider): bool =>
                    //                    if(!empty($provider->external_id)){
                    //                        return $provider->integration.'-'.$provider->external_id;
                    //                    }
                    !empty($provider->integration))->keyBy('integration');
            }
        }

        return $this->external_providers;
    }

    /**
     * @return ShippingProvider[]|Collection
     */
    public function getAllProviders()
    {
        if (!array_key_exists('default', \Illuminate\Support\Facades\DB::getConnections())) {
            return new Collection();
        }

        return ArrayCache::remember('shipping.all', function () {
            $with = [
                'geo_zone.values.polygon', 'geo_zone_values.polygon',
                'geo_zone.values.distance', 'geo_zone_values.distance',
                'addresses.shop', 'rates', 'boxes', 'payments',
                'meta', 'external_provider'
            ];
            if ($installed = Apps::installed($deliveryDateKey = ShippingHoursManager::APP_KEY)) {
                $with[] = 'shipping_hours.hours';
                $with[] = 'shipping_hours_exceptions';
            }

            return ShippingProvider::with($with)->where('active', 1)->get()->map(function (ShippingProvider $provider) use ($installed): \App\Models\Shipping\ShippingProvider {
                $provider->setAttribute('has_delivery_dates', false);
                $provider->setAttribute('delivery_dates', collect());

                if ($installed && $provider->shipping_hours->isNotEmpty()) {
                    $provider->setAttribute('has_delivery_dates', $provider->shipping_hours->isNotEmpty());
                    $provider = ShippingHoursManager::formatCheckout($provider);
                }

                $provider->setRelation('shipping_hours', collect());
                $provider->syncOriginal();
                return $provider;
            });
        });
    }

    /**
     * Normalise manager key
     * @param string $key
     * @return string
     */
    public function normaliseKey($key)
    {
        if (str_starts_with(strval($key), 'omniship.')) {
            return substr($key, 9);
        }

        return $key;
    }

    /**
     * register internals providers
     */
    protected function registerProviders()
    {
        foreach ($this->getInternals() as $key => $name) {
            if ($this->app->isBooted()) {
                /** @var ServiceProvider $service */
                $service = app(sprintf(static::PROVIDERS_NAMESPACE, $name, $name), ['app' => $this->app]);
                $service->register();
                if (method_exists($service, 'boot')) {
                    $this->app->call([$service, 'boot']);
                }
            } else {
                $this->app->register(sprintf(static::PROVIDERS_NAMESPACE, $name, $name));
            }
        }
    }

    /**
     * register externals providers
     */
    protected function registerExternalProviders()
    {
        foreach ($this->getProviders()/*->where('active', 1)*/ as $provider) {
            if (!$this->has($provider->omniship_key)) {
                if ($provider->integration) {
                    $this->app->singleton($provider->omniship_key, fn() => with(clone app('omniship.' . $provider->integration), function (AbstractManager $manager) use ($provider): \Modules\Apps\Shippings\Omniship\AbstractManager {
                        $manager->setProvider($provider);
                        return $manager;
                    }));
                    $this->set(App::make($provider->omniship_key));
                } else {
                    $this->app->singleton($provider->omniship_key, fn(): \App\Integration\OmniShip\Internal\Manager => new InternalManager($provider));
                    $this->set(App::make($provider->omniship_key));
                }
            }
        }
    }

    /**
     *
     * @return SiteQueue|bool
     * @throws Exception
     */
    public static function startSpeedySync()
    {
        return SiteQueue::createQueueByMapping('omniship_speedy_sync_places');
    }

    /**
     *
     * @return SiteQueue|bool
     * @throws Exception
     */
    public static function startEcontSync()
    {
        return SiteQueue::createQueueByMapping('omniship_econt_sync_places');
    }

    /**
     * @return SiteQueue|bool
     * @throws Exception
     * @deprecated
     */
    public static function startRapidoSync()
    {
        return SiteQueue::createQueueByMapping('omniship_rapido_sync_places');
    }

    /**
     * @return SiteQueue|bool
     * @throws Exception
     */
    public static function startSyncPayments()
    {
        return SiteQueue::createQueueByMapping('omniship_sync_payments');
    }

    /**
     * @inheritDoc
     * @return bool
     * @throws Throwable
     */
    public function postSubscription(): ?bool
    {
        return static::startSyncPayments();
    }

}
