{capture append="js"}
    <script type="text/javascript">
        loadScripts([
            '{assetic bundle="multipart_form_bdl" output="js"}'
        ]);

        (function () {

            var mikmikApp = {
                _data: null,
                _form: null,
                _checkedServices:function() {
                    return {json_encode($settings->get('allowed_methods',[])) nofilter};
                },
                _checkedServicesInternational:function() {
                    return {json_encode($settings->get('free_method_international',[])) nofilter};
                },
                init: function(object) {
                    this._data = object;
                    if(this._data) {
                        for(var key in this._data) {
                            if(this[key]) {
                                this[key](this._data[key]);
                            }
                        }
                    }
                    return this;
                },
                form: function(form) {
                    if(!this._form) {
                        this._form = form;
                    }
                    return this;
                },
                clients: function(value) {
                    var self = this;
                    function fillSelect(name, value) {
                        var select = self._form.find('[name="mikmik['+name+']"]').val('').trigger('change').empty();
                        for(i in value) {
                            select.append('<option data-city-id="'+value[i]['city_id']+'" value="'+value[i]['id']+'">'+value[i]['name']+'</option>');
                        }
                        if(select.data('select2')) {
                            select.select2('destroy');
                        }
                        App.initSelect2(select.closest('.row'));
                    }
                    fillSelect('client_id', value);
                    fillSelect('payer_id', value);
                },
                services: function(value) {
                    if(value) {
                        var instance = this,
                            $services = instance._form.find('.js-mikmik-services').empty(),
                            addRow = function($holder, value, name, multiple, ids) {
                                var $select = $('<select name="mikmik[' + name + (multiple ? '][]' : ']') + ']" class="form-control select2me" id="allowed_methods" multiple ' + multiple + name + '></select>').appendTo($holder);

                                for (i in value) {
                                    if(name.indexOf('free_method_city_') === 0 && i.indexOf(1) !== 0) {
                                        continue;
                                    } else if(name.indexOf('free_method_intercity_') === 0 && i.indexOf(2) !== 0) {
                                        continue;
                                    }

                                    var $option = $('<option value="'+i+'">' + value[i] + '</option>');

                                    $option.prop('selected', $.inArray(i, ids) > -1);
                                    $select.append($option);
                                }

                                $holder.closest('.js-mikmik-services-holder').removeClass('hidden');
                                App.initAjax($holder);
                            };

                        $services.each(function(){
                            var $this    = $(this),
                                name     = $this.data('name'),
                                multiple = $this.is('[data-multiple]') ? 'multiple' : '',
                                service  = instance._checkedServices();
                            addRow($this, value, name, multiple, service);
                        });
                    }
                }
            };

            /* begin regions */
            var $form = $('#mikmikForm');

            $form.on('submit', function (e) {
                e.preventDefault();
                Custom.submitFormMultipart($(this));
            });

            $form.on('change', '[name="mikmik[client_id]"]', function() {
                var $option = $('option:selected', this),
                    city_id = $option.length ? $option.data('city-id') : '';
                $('[name="mikmik[client_city_id]"]').val(city_id);
            }).find('[name="mikmik[client_id]"]').trigger('change');

            $form.on('change', 'input[name="mikmik[target]"]', function() {
                var $region_inputs = $form.find('#regions_holder select');
                if ($(this).prop('checked')) {
                    $form.find('#regions_holder').css({
                        'opacity': '0.6'
                    });
                    $region_inputs.prop('disabled', true);
                } else {
                    $form.find('#regions_holder').css({
                        'opacity': '1'
                    });
                    $region_inputs.prop('disabled', false);
                }
                $region_inputs.prop('checked', false);
                App.updateUniform($region_inputs);
            }).find('input[name="mikmik[target]"]').trigger('change');

            $form.on('click', 'input[type="checkbox"][name="mikmik[regions][]"]', function (e) {
                handleCheckboxRescursive(e, $(this), '#Regions');
            }).on('change', 'input[name="mikmik[is_region]"]', function() {
                var $region_inputs = $form.find('#Regions input');
                if ($(this).prop('checked')) {
                    $form.find('#Regions').closest('.box-section').removeClass('hidden');
                    $region_inputs.prop('disabled', false);
                } else {
                    $form.find('#Regions').closest('.box-section').addClass('hidden');
                    $region_inputs.prop('disabled', true);
                }
                $region_inputs.prop('checked', false);
                App.updateUniform($region_inputs);
            });
            $('input[name="mikmik[is_region]"]').trigger('change');

            {if !empty($provider->regions)}
            {foreach $provider->regions as $region}
            $("input[type='checkbox'][name='regions[]'][value='{$region->id}']").trigger('click');
            {/foreach}
            {elseif isset($provider) && $provider->target == 'restofworld'}
            var $region_inputs = $form.find('#Regions input');
            $form.find('#Regions').css({
                'opacity': '0.6'
            });
            $region_inputs.prop('disabled', true);
            {/if}
            /* end regions */

            $('[name="mikmik[cd]"]').on('change', function() {
                CC.statusBox($('[name="mikmik[cd_max]"]').closest('.form-group'), $(this).is(':checked'));
            }).trigger('change');

            $('#mikmik_city').on('change', function(e) {
                $('#mikmikForm [name="mikmik[post_code]"]').val('');
                if(e.added && e.added.postcode) {
                    $('#mikmikForm [name="mikmik[post_code]"]').val(e.added.postcode);
                }
            });

            $("[data-field]").on('input', function(){
                $('[data-hidden]').addClass('hidden');
                $('[data-visible]').removeClass('hidden');
            });

            $('[name="mikmik[side]"]').on('change', function() {
                CC.statusBox($('[name="mikmik[payer_id]"]').closest('.js-mikmik-payer'), $(this).val() == '2');
            }).trigger('change');

            $("[data-btn='mikmik-test']").on("click", function () {
                var $btn = $(this);
                var $form = $btn.closest("form");

                $btn.trigger('loading.start');
                $btn.prop("disabled", true);

                $form.on('cc.ajax.success', function(e, json) {
                    $btn.trigger('loading.end');
                    $btn.prop("disabled", false);

                    if('fields' in json) {
                        mikmikApp.form($form).init(json.fields);
                        $('[data-hidden]').removeClass('hidden');
                        $('[data-visible]').addClass('hidden');
                    }
                    App.initAjax($form);
                });

                $form.on('cc.ajax.error', function(e) {
                    $btn.trigger('loading.end');
                    $btn.prop("disabled", false);
                });

                Custom.submitFormAjax($form, $btn.data("url"));
            });

            $('[data-to-shipping]').on('change', function() {
                var $el = $(this);
                CC.statusBox($('.js-services-box-' + $el.data('shipping-key')), $el.is(':checked'));
                if($el.data('toShipping')) {
                    if(!$el.is(':checked')) {
                        App.updateSwitch($('[name="mikmik[' + $el.data('toShipping') + ']"]').prop('checked', true));
                        CC.statusBox($('.js-services-box-' + $el.data('toShipping')), 1);
                    }
                }
            }).trigger('change');

            $(document).on('change', '[name="mikmik[send_type]"]', function (e) {
                e.preventDefault();
                var $el = $(this);
                if($el.val() =='office'){
                    $('#type_office').removeClass('hidden');
                    $('#type_address').addClass('hidden');
                }
                if($el.val() =='address'){
                    $('#type_office').addClass('hidden');
                    $('#type_address').removeClass('hidden');
                }
            });

            $(document).on('change', '[name="mikmik[cd]"]', function (e) {
                e.preventDefault();
                var $el = $(this);
                if($el.is(':checked')){
                    $('#cod_pay').removeClass('hidden');
                } else {
                    $('#cod_pay').addClass('hidden');
                }
            });

            $(document).on('change', '[name="mikmik[cod_pay]"]', function (e) {
                e.preventDefault();
                var $el = $(this);
                if($el.val() =='client_number'){
                    $('#client_number_input').removeClass('hidden');
                }
                if($el.val() =='cash'){
                    $('#client_number_input').addClass('hidden');
                }
            });

        }());

        {if $error|default}
        toastr.error('{$error}');
        {/if}
    </script>
{/capture}