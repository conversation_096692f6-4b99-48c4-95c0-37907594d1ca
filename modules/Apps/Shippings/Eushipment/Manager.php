<?php

declare(strict_types=1);

namespace Modules\Apps\Shippings\Eushipment;

use App\Exceptions\Error;
use App\Helper\ArrayCache;
use App\Helper\Format;
use App\Helper\Store\Contracts\ItemContract;
use App\Helper\Store\Contracts\OrderContract;
use App\Helper\YesNo;
use App\Locale\Weight;
use App\Mail\SiteMail;
use App\Models\Apps\Applications;
use App\Models\Order\Order;
use App\Models\Order\OrderFulfillment;
use App\Models\Router\Logs;
use App\Models\Shipping\ExternalShippingProviders;
use App\Models\Shipping\ShippingProvider;
use App\Models\System\AppsManager;
use Crisu83\Conversion\Quantity\Mass\Unit as MassUnit;
use Illuminate\Config\Repository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\Apps\Shippings\Eushipment\Traits\AdministrativeMethodsForConfiguration;
use Modules\Apps\Shippings\Eushipment\Models\Cities;
use Modules\Apps\Shippings\Eushipment\Models\Countries;
use Modules\Apps\Shippings\Eushipment\Models\CouriersList;
use Modules\Apps\Shippings\Eushipment\Models\Offices;
use Modules\Apps\Shippings\Omniship\AbstractManager;
use Omniship\Address\City;
use Omniship\Address\Country;
use Omniship\Address\Office;
use Omniship\Common\ItemBag;
use Omniship\Consts;
use Omniship\Eushipment\Gateway;
use Omniship\Eushipment\Http\CreateBillOfLadingRequest;
use Omniship\Eushipment\Http\ShippingQuoteRequest;
use Omniship\Omniship;

class Manager extends AbstractManager
{
    use AdministrativeMethodsForConfiguration;

    /**
     * @var Gateway
     */
    protected $manager;

    public const KEY = 'omniship.eushipment';

    protected $credentials = ['public_key'];

    public const PRICING_TYPES = [
        'calculator',
        'calculator_fixed',
        'free',
        'fixed_price',
        'fixed_weight',
        'price_and_weight'
    ];

    public const APP_KEY = 'eushipment';

    public $support = ['address', 'office'];

    protected $fallback_allowed_countries = [];

    protected $default_settings_for_provider = [
        'settings.side' => Consts::PAYER_RECEIVER,
        'settings.to_address' => 1,
        'settings.pricing_address' => 'calculator',
        'settings.default_weight' => 0.1,
        'settings.order_content' => 'sku',
    ];


    protected $isMultipleCourier = true;

    /**
     * Manager constructor.
     * @param null $apiPublic
     * @param null $testMode
     * @param null $companyId
     * @throws Error
     */
    public function __construct($apiPublic = null, $testMode = false, $companyId = false)
    {
        if ($this->isProviderRoute() && request()->input('eushipment_public_key')) {
            $apiPublic = request()->input('eushipment_public_key');
            $testMode = request()->input('eushipment_test_mode');
        } elseif ($this->isProviderRoute() && request()->input('public_key')) {
            $apiPublic = request()->input('public_key');
            $testMode = request()->input('test_mode');
        } elseif ($this->isProviderRoute() && request()->input('eushipment.public_key')) {
            $apiPublic = request()->input('eushipment.public_key');
            $testMode = request()->input('eushipment.test_mode');
        } elseif (site('site_id')) {
            $apiPublic = $apiPublic ?: $this->getSetting('public_key');
            $testMode = $testMode ?: $this->getSetting('test_mode', false);
        }

        $this->manager = Omniship::create('Eushipment')->initialize([
            'public_key' => $apiPublic,
            'test_mode' => boolval($testMode)
        ]);
        parent::__construct();
        $this->manager->setTestMode(boolval($testMode));
    }

    /**
     * @param $external_id
     * @return void
     */
    public function setProviderByExternalId($external_id): void
    {
        if ($external_id == 'install' || $external_id == 'uninstall') {
            return;
        }

        $this->setProvider(ShippingProvider::where('external_id', $external_id)->first());
    }

    /**
     * @return mixed
     */
    public function getClient()
    {
        return $this->getManager()->getClient();
    }

    /**
     * @param null $mapping
     * @return bool
     * @throws \App\Exceptions\Error
     */
    public function isActive($mapping = null): bool
    {
        $isActive = $this->getProvider()->active ?? false;
        return (bool)$isActive;
    }

    /**
     * @return Repository
     */
    public function getGlobalSetting()
    {
        return AppsManager::getSettings('eushipment');
    }

    /**
     * @param bool $refreshSession
     * @return bool
     */
    public function isActiveSession($refreshSession = true)
    {
        if (!is_null($this->getSetting('public_key'))) {
            try {
                $validate = $this->getManager()->validateCredentials([
                    'public_key' => $this->getSetting('public_key'),
                    'test_mode' => (bool)$this->getSetting('test_mode', 0),
                ])->send()->getData();
                if ($validate != false) {
                    return ArrayCache::remember('eushipment.session.check.' . $this->getSetting('public_key'), fn(): true => true);
                } else {
                    return false;
                }
            } catch (\Exception) {
                return false;
            }
        }

        return false;
    }

    /**
     * @param $id
     * @return false|array
     * @throws Error
     */
    public function getCourier($id)
    {
        $getCourier = array_filter(array_map(function (array $courier) use ($id) {
            if ($courier['id'] == $id) {
                return $courier;
            }
        }, $this->getGlobalSetting()->get('couriers') ?? []));

        if (count($getCourier) == 0) {
            return false;
        }

        return \Illuminate\Support\Arr::first($getCourier);
    }

    /**
     * @param $id
     * @return mixed
     */
    public function getCourierByProvider($id)
    {
        return ExternalShippingProviders::where('id', $id)->value('data');
    }

    /**
     * @param null $key
     * @return Repository|mixed
     * @throws Error
     */
    public function collectSetting($key = null)
    {
        $settings = new Repository(
            collect($this->default_settings_for_provider)->merge($this->getProvider()->meta->pluck('value', 'parameter')->all())->all()
        );
        if ($key) {
            return $settings->get('settings.' . $key);
        }

        return $settings;
    }


    /**
     * @param array $parameters
     * @return \Omniship\Message\AbstractRequest
     * @throws Error
     */
    public function getQuotesRequest(array $parameters): ?\Omniship\Message\AbstractRequest
    {
        return $this->manager->createRequest(ShippingQuoteRequest::class, $parameters);
    }

    /**
     * @param array $parameters
     * @param \stdClass|null $request_response
     * @param array $categories
     * @return bool|mixed|\Omniship\Common\ShippingQuoteBag|null
     * @throws Error
     * @throws \Throwable
     */
    #[\Override]
    public function getQuotes(array $parameters, ?\stdClass $request_response = null, array $categories = [])
    {
        $provider = $this->getProvider();

        $courierID = $this->getCourierByProvider($provider['external_id']);
        $settings = $this->collectSetting();
        //   $parameters['insurance_amount'] = 0;

        $getCourier = $this->getCourierInfo($courierID);
        if ($settings->get('settings.cod') == 1) {
            if (!empty($parameters['cash_on_delivery_amount'])) {
                $cash_on_delivery = $parameters['cash_on_delivery_amount'] ?? false;
            } else {
                $cash_on_delivery = 0;
            }

        }


        $country_id = Countries::where('iso_code', $parameters['receiver_address']->getCountry()->getIso2())->value('id');
        if (!empty($country_id)) {
            $findCity = $this->findCityByPostCode($parameters['receiver_address']->getPostCode(), $parameters['receiver_address']->getCity()->getName(), $country_id);
            if ($findCity) {
                $parameters['receiver_address']->setCity($findCity);
            }
        }

        if (!empty($parameters['insurance_amount'])) {
            $insurance_amount = $parameters['insurance_amount'] ?? false;
        } elseif (!empty($parameters['sub_total'])) {
            $insurance_amount = $parameters['sub_total'] ?? false;
        } else {
            $insurance_amount = 0;
        }

        if (!empty($cash_on_delivery)) {
            $parameters['cash_on_delivery_amount'] = number_format(floatval($cash_on_delivery), 2, '.', '');
        }

        if ($settings->get('settings.insurance', 0) == 1 || !empty($parameters['insurance_amount'])) {
            $parameters['insurance_amount'] = number_format(floatval($insurance_amount), 2, '.', '');
        }

        $parameters['other_parameters']['openPackage'] = $settings->get('settings.open_package', 0) == 1 ? true : false;
        $defaultWeight = $settings->get('settings.default_weight');
        $parameters['weight'] = $parameters['items']->sum(function ($item) use ($defaultWeight): int|float {
            if (is_numeric($item->getId())) {
                if ($item->getWeight() > 0) {
                    return $item->getWeight() * $item->getQuantity();
                } else {
                    return $defaultWeight * $item->getQuantity();
                }
            }

            return 0;
        });
        $parameters['items'] = $parameters['items']->map(function ($item) {
            if (is_numeric($item->getId())) {
                return $item;
            }

            return;
        })->filter();

        if (!empty($parameters['saturday_delivery'])) {
            $saturday_delivery = $parameters['saturday_delivery'];
        } else {
            $saturday_delivery = $settings->get('settings.saturday_delivery', 0);
        }

        $parameters['other_parameters']['currency'] = $getCourier['currency'];
        $parameters['other_parameters']['main_currency'] = site()->currency;
        $parameters['company_id'] = !empty($parameters['company_id']) ? $parameters['company_id'] : $settings->get('settings.default_company');
        $parameters['courier_id'] = $courierID;
        $parameters['side'] = !empty($parameters['side']) ? $parameters['side'] : $settings->get('settings.side');
        $parameters['other_parameters']['saturday'] = $saturday_delivery == 1 ? true : false;
        $parameters['other_parameters']['return_documents'] = $settings->get('settings.return_documents', 0);
        return parent::getQuotes($parameters, $request_response, $categories);
    }

    /**
     * @return string
     */
    public function getPriceList(): string
    {
        return match ($this->collectSetting()->get('settings.price_list') == '0' ? $this->collectSetting()->get('settings.side') : $this->collectSetting()->get('settings.price_list')) {
            '0' => Consts::PAYER_SENDER,
            '1', Consts::PAYER_SENDER => Consts::PAYER_SENDER,
            '2', Consts::PAYER_RECEIVER => Consts::PAYER_RECEIVER,
            default => Consts::PAYER_SENDER,
        };
    }

    /**
     * @return bool
     * @throws Error
     */
    #[\Override]
    public function supportsCashOnDelivery(): bool
    {
        $settings = $this->collectSetting();
        return parent::supportsCashOnDelivery() && $settings->get('settings.cod') && $this->compareMaxCodPrice();
    }

    /**
     * @return bool
     * @throws Error
     * @throws \Exception
     */
    protected function compareMaxCodPrice()
    {
        $settings = $this->collectSetting();

        $sub_total = $this->getOrder() ? $this->getOrder()->getSubTotal('input') : 0;
        if (!$sub_total) {
            return true;
        }

        if (site('currency') == 'BGN') {
            $max_cod = static::BG_MAX_COD;
        } else {
            $max_cod = static::UNLIMITED_MAX_COD;
        }

        $cod_allow_price = $settings->get('cd_max') > 0 ? min($settings->get('cd_max'), $max_cod) : $max_cod;
        return $sub_total <= $cod_allow_price;
    }

    /**
     * Supports recalculate shipping
     *
     * @return boolean
     * @throws Error
     */
    #[\Override]
    public function supportsRecalculateShippingOnPaymentChange(): bool
    {
        return $this->supportsCashOnDelivery();
    }


    /**
     * @param array $parameters
     * @return mixed
     */
    public function createBillOfLadingRequest(array $parameters): \Omniship\Message\AbstractRequest
    {
        return $this->manager->createRequest(CreateBillOfLadingRequest::class, $parameters);
    }

    /**
     * @param array $parameters
     * @param stdClass $request_response
     * @return mixed
     */
    #[\Override]
    public function createBillOfLading(array $parameters, ?\stdClass &$request_response = null)
    {
        return parent::createBillOfLading($parameters, $request_response);
    }

    /**
     * @param mixed $amount
     * @param mixed $receiver_address
     * @return mixed
     */
    public function convertAmount($amount, $receiver_address)
    {
        return $amount;
    }

    /**
     * @param mixed $address
     * @return mixed
     */
    protected function fixReceiverAddress($address)
    {
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    public function findCountries($name): void
    {
        // TODO: Implement findCountries() method.
    }

    /**
     * @param mixed $iso2
     * @return mixed
     */
    public function getCountryByIso2($iso2)
    {
        $result = Countries::where('iso_code', $iso2)->first();
        if ($result) {
            $name = 'name_' . (site('language') == 'bg' ? 'bg' : 'en');
            return new Country([
                'id' => $result->id,
                'name' => $result->$name,
                'iso2' => $iso2,
            ]);
        }
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    public function getCountryByIso3($name): void
    {
        // TODO: Implement getCountryByIso3() method.
    }

    /**
     * @param $name
     * @return Country|void
     * @throws Error
     */
    public function findCountryByName($name)
    {
        $result = Countries::where('name_bg', $name)->orWhere('name_en', $name)->first();
        if ($result) {
            $name = 'name_' . (site('language') == 'bg' ? 'bg' : 'en');
            return new Country([
                'id' => $result->id,
                'name' => $result->$name,
                'iso2' => $result->iso_code,
                'currency' => $this->getCourierInfo($this->getProvider()->external_provider->data)['currency']
            ]);
        }
    }

    /**
     * @param mixed $country_id
     * @return mixed
     */
    public function getCountryById($country_id): void
    {
        // return Countries::find($country_id);
    }

    /**
     * @param mixed $name
     * @param mixed $country_id
     * @return mixed
     */
    public function findStates($name, $country_id): void
    {
        // TODO: Implement findStates() method.
    }

    /**
     * @param mixed $name
     * @param mixed $country_id
     * @param mixed $state
     * @return mixed
     */
    public function findCities($name, $country_id, $state = null)
    {

    }

    /**
     * @param $name
     * @param $country_id
     * @return City|void
     */
    public function findCityByName($name, $country_id)
    {
        $result = Cities::where(['country_id' => $country_id])
            ->where(function ($query) use ($name): void {
                $query->where('name_bg', 'like', '%' . $name . '%')
                    ->orWhere('name_en', 'like', '%' . $name . '%');
            })
            ->first();
        if (!empty($result)) {
            $name = 'name_' . (site('language') == 'bg' ? 'bg' : 'en');
            return new City([
                'id' => $result->system_id,
                'name' => $result->$name,
                'post_code' => $result->post_code
            ]);
        }
    }

    /**
     * @param $post_code
     * @param $name
     * @param $country_id
     * @return City|void
     */
    public function findCityByPostCode($post_code, $name, $country_id)
    {
        $result = Cities::where(['post_code' => $post_code, 'country_id' => $country_id])
            ->where(function ($query) use ($name): void {
                $query->where('name_bg', 'like', '%' . $name . '%')
                    ->orWhere('name_en', 'like', '%' . $name . '%');
            })
            ->first();
        if (!empty($result)) {
            $name = 'name_' . (site('language') == 'bg' ? 'bg' : 'en');
            return new City([
                'id' => $result->system_id,
                'name' => $result->$name,
                'post_code' => $result->post_code
            ]);
        }
    }

    /**
     * @param $city_id
     * @return City|void
     */
    public function getCityById($city_id)
    {
        $result = Cities::where(['system_id' => $city_id])->first();
        if (!empty($result)) {
            $name = 'name_' . (site('language') == 'bg' ? 'bg' : 'en');
            return new City([
                'id' => $result->system_id,
                'name' => $result->$name,
                'post_code' => $result->post_code
            ]);
        }
    }

    /**
     * @param mixed $name
     * @param mixed $city_id
     * @return mixed
     */
    public function findStreets($name, $city_id): void
    {
        // TODO: Implement findStreets() method.
    }

    /**
     * @param mixed $name
     * @param mixed $city_id
     * @return mixed
     */
    public function findQuarters($name, $city_id): void
    {
        // TODO: Implement findQuarters() method.
    }

    /**
     * @param mixed $name
     * @param mixed $city_id
     * @return mixed
     */
    public function findOffices($name, $city_id = null)
    {
        $courier_id = null;
        if (request('external_id')) {
            $this->setProviderByExternalId(request('external_id'));
            $courier_id = $this->getProvider()->external_provider->data;
        } elseif (request('provider_id')) {
            $courier_id = $this->getCourierByProvider(request('provider_id'));
            $this->setProvider(ShippingProvider::find(request('provider_id')));
        }

        $results = $this->cache(__FUNCTION__ . $name . $this->getProvider()->external_id . $city_id, function () use ($name, $city_id, $courier_id) {
            $model = Offices::has('city')->with('city.country');
            if ($city_id) {
                $model->where('city_id', $city_id);
            }

            if (!empty($courier_id)) {
                $model->where('courier_id', $courier_id);
            }

            $model->where(function (Builder $query) use ($name, $courier_id): void {
                $query->where('city_name', 'LIKE', sprintf('%%%s%%', $name))
                    ->orWhere('post_code', 'LIKE', sprintf('%%%s%%', $name))
                    ->orWhere('office_name', 'LIKE', sprintf('%%%s%%', $name));

            })
//                ->orWhereHas('city', function (Builder $query) use ($name) {
//                    $query->where('name_bg', 'LIKE', "%$name%")
//                        ->orWhere('post_code', 'LIKE', "%$name%")
//                        ->orWhere('name_en', 'LIKE', "%$name%");
//                })
                ->orderBy('city_name', 'asc');

            return $model->get();
        });

        return $results->map(function (Offices $office): \Omniship\Address\Office {
            if (!empty($office->city)) {
                $country = $office->city->country()->first();
            }

            $name = 'name_' . (site('language') == 'bg' ? 'bg' : 'en');
            return new Office([
                'id' => $office->id,
                'name' => $office->office_name,
                'address_string' => $office->address,
                'latitude' => $office->lat,
                'longitude' => $office->lng,
                'max_weight' => 1000,
                'weight_unit' => MassUnit::KILOGRAM,
                'city_id' => $office->city_id,
                'type' => 'office',
                'provider' => 'eushipment_' . $this->getProvider()->external_id,
                'city' => [
                    'id' => $office->city_id,
                    'post_code' => $office->city ? $office->city->post_code : null,
                    'type' => 'city',
                    'name' => $office->city ? $office->city->$name : null,
                    'country_id' => $office->city ? $office->city->country_id : null,
                    'address_nomenclature' => 'NO'
                ],
                'country' => [
                    'id' => $office->city ? $office->city->country_id : null,
                    'name' => !empty($country) ? $country->$name : '',
                    'iso2' => !empty($country) ? $country->iso_code : '',
                    'iso3' => '',
                    'has_cities' => false
                ]
            ]);
        });
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    public function officesAutocomplete($name)
    {
        $results = $this->cache(__FUNCTION__ . '.' . $name . $this->getProvider()->external_id . $name, function () use ($name) {
            $model = Offices::with(['city', 'city.country'])->where(function (Builder $query) use ($name): void {
                $query->where('city_name', 'LIKE', sprintf('%%%s%%', $name))
                    ->orWhere('post_code', 'LIKE', sprintf('%%%s%%', $name))
                    ->orWhere('office_name', 'LIKE', sprintf('%%%s%%', $name));

            })
                ->orWhereHas('city', function (Builder $query) use ($name): void {
                    $query->where('name_bg', 'LIKE', sprintf('%%%s%%', $name))
                        ->orWhere('post_code', 'LIKE', sprintf('%%%s%%', $name))
                        ->orWhere('name_en', 'LIKE', sprintf('%%%s%%', $name));
                })
                ->orderBy('city_name', 'asc');

            return $model->get();
        });

        return $results->map(function (Offices $office): \Omniship\Address\Office {
            $country = $office->city->country()->first();
            $name = 'name_' . (site('language') == 'bg' ? 'bg' : 'en');
            return new Office([
                'id' => $office->id,
                'name' => $office->office_name,
                'address_string' => $office->address,
                'latitude' => $office->lat,
                'longitude' => $office->lng,
                'max_weight' => 1000,
                'weight_unit' => MassUnit::KILOGRAM,
                'city_id' => $office->city_id,
                'type' => 'office',
                'provider' => 'eushipment_' . $this->getProvider()->external_id,
                'city' => [
                    'id' => $office->city_id,
                    'post_code' => $office->city->post_code,
                    'type' => 'city',
                    'name' => $office->city->$name,
                    'country_id' => $office->city->country_id,
                    'address_nomenclature' => 'NO'
                ],
                'country' => [
                    'id' => $office->city->country_id,
                    'name' => $country->$name,
                    'iso2' => $country->iso_code,
                    'iso3' => '',
                    'currency' => $this->getCourierInfo([$this->getProvider()->external_provider->data])['currency'],
                    'has_cities' => false
                ]
            ]);
        });

    }

    /**
     * @param $office_id
     * @return false|Office
     * @throws Error
     */
    public function getOfficeById($office_id): \Omniship\Address\Office|false
    {
        $office = Offices::with(['city', 'city.country'])->where('id', $office_id)->first();
        if (!empty($office)) {
            $country = $office->city->country()->first();
            $name = 'name_' . (site('language') == 'bg' ? 'bg' : 'en');
            return new Office([
                'id' => $office->id,
                'name' => $office->office_name,
                'address_string' => $office->address,
                'latitude' => $office->lat,
                'longitude' => $office->lng,
                'max_weight' => 1000,
                'weight_unit' => MassUnit::KILOGRAM,
                'city_id' => $office->city_id,
                'type' => 'office',
                'provider' => 'eushipment_' . $this->getProvider()->external_id,
                'city' => [
                    'id' => $office->city_id,
                    'post_code' => $office->city->post_code,
                    'type' => 'city',
                    'name' => $office->city->$name,
                    'country_id' => $office->city->country_id,
                    'address_nomenclature' => 'NO'
                ],
                'country' => [
                    'id' => $office->city->country_id,
                    'name' => $office->city->country->$name,
                    'iso2' => $office->city->country->iso_code,
                    'iso3' => '',
                    'has_cities' => true
                ]
            ]);
        }

        return false;
    }

    /**
     * @return array
     * @throws Error
     */
    public function getSupportType(): array
    {
        $support_types = [];
        if ($provider = $this->getProvider()->external_provider) {
            $getcourier = $provider->data;
            if ($getcourier) {
                $getcourier = $this->getCourier($getcourier);
                if (!empty($getcourier['to_office']) && $getcourier['to_office'] == true) {
                    $support_types[] = static::SUPPORT_OFFICE;
                }

                if (!empty($getcourier['to_address']) && $getcourier['to_address'] == true) {
                    $support_types[] = static::SUPPORT_ADDRESS;
                }
            }
        }

        return $support_types;
    }

    #[\Override]
    public function getWaybillSides(): \Illuminate\Support\Collection
    {
        $sides = new Collection();
        //receiver
        $sides->put(Consts::PAYER_RECEIVER, [
            'id' => Consts::PAYER_RECEIVER,
            'name' => __('shipping.side_receiver')
        ]);
        //sender
        $sides->put(Consts::PAYER_SENDER, [
            'id' => Consts::PAYER_SENDER,
            'name' => __('shipping.side_sender')
        ]);

        return $sides;
    }


    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function orderWaybillUrl(Order $order): ?string
    {
        if ($order->meta_pluck->get('fullfilment', 0) == 1) {
            return null;
        }

        return route('apps.eushipment.print_waybill_select', [$order->id]);
    }

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function orderWaybillUrlAttr(Order $order): ?string
    {
        return 'data-modal-ajax="' . route('apps.eushipment.print_waybill_select', [$order->id]) . '" data-modal-size="xsmall"';
    }

    /**
     * @param mixed $city_id
     * @return mixed
     */
    public function getStateByCityID($city_id)
    {
    }

    /**
     * @param mixed $parameters
     * @return mixed
     */
    public function geoNamesAddressFormatting(&$parameters)
    {

    }

    /**
     * @param $couriers
     * @return array|array[]
     */
    public function returnMapperCouriers($couriers): array
    {
        return array_filter(array_map(function (array $courier): ?array {
            $mapped = $this->getCourierInfo($courier['ID']);
            if (empty($mapped)) {
                return null;
            }

            return [
                'id' => $courier['ID'],
                'name' => $mapped['name'],
                'logo' => $mapped['logo'],
                'to_office' => $courier['TO_OFFICE'],
                'to_address' => $courier['TO_ADDRESS'],
            ];
        }, $couriers));
    }

    /**
     * @param $bol_id
     * @param $cancelComment
     * @return null
     */
    #[\Override]
    public function cancelBillOfLading($bol_id, $cancelComment = null): null
    {
        try {
            if ($this->supportsCancelBillOfLading()) {
                \Illuminate\Support\Facades\Mail::send(new SiteMail([
                    'email' => '<EMAIL>',
                    'subject' => 'Анулиране на товарителници',
                    'plainMessage' => 'Здравейте, желаем да анулираме товарителница с номер ' . $bol_id,
                    'siteName' => setting('site_name'),
                    'siteEmail' => setting('site_email'),
                    'attachments' => []
                ], 'elastic_email'));

                Logs::debug([
                    'message' => 'Shipping Cancel Bill: ' . $this->getKey(),
                    'data' => null,
                    'request' => ['id' => $bol_id, 'comment' => $cancelComment],
                    'file' => __FILE__,
                    'line' => __LINE__,
                ]);
            }

            return null;
        } catch (\Exception) {
            return null;
        }
    }


    /**
     * @param Order $order
     * @return void
     * @throws Error
     * @throws \Throwable
     */
    public function generateOrder(Order $order): void
    {
        $this->setOrder($order);
        $waybillStd = new \stdClass();
        $waybillStd->waybill_id = null;

        $request_response = new \stdClass();

        $receiver_address = $order->shippingAddress->address;
        $receiver_address = $receiver_address->setPhone(str_replace(' ', '', $order->shippingAddress->phone_national));
        $this->setOrder($order);

        $cash_on_delivery_amount = 0;
        $insurance_amount = 0;
        if ($order->meta_pluck->get('cod_amount')) {
            $cash_on_delivery_amount = $order->meta_pluck->get('cod_amount');
        } elseif ($order->isCashOnDelivery()) {
            $cash_on_delivery_amount = Format::moneyInput($this->getCashOnDeliveryTotal($order));
        }

        if ($order->meta_pluck->get('insurance')) {
            $insurance_amount = $order->meta_pluck->get('insurance_amount');
        } elseif ($order->isCashOnDelivery()) {
            $insurance_amount = $cash_on_delivery_amount;
        }

        $country_id = Countries::where('iso_code', $receiver_address->getCountry()->getIso2())->value('id');

        if (!empty($country_id)) {
            $receiver_address->setCity($this->findCityByName($receiver_address->getCity()->getName(), $country_id));
        }

        $office_code = null;
        $office_address = null;

        if (!empty($receiver_address->getOffice())) {
            $office = Offices::where('id', $receiver_address->getOffice()->getId())->first();
            $office_code = $office->office_code;
            $office_address = $office->address;
        }

        $courierInfo = $this->getCourierInfo($order->shipping->provider->external_provider->data);

        $params = [
            'content' => Str::substr(implode(',', $order->getProducts()->implode('name')), 0, 36),
            'client_note' => Str::substr($order->note_customer, 0, 200),
            'transaction_id' => $order->id,
            'items' => $this->convertOrderToItemsBag($order),
            'other_parameters' => [
                'instructions' => $order->note_customer,
                'parcels' => 1,
                'saturday_delivery' => $this->getSetting('saturday_delivery') ?? null,
                'fragile' => $this->getSetting('breakable') ?? null,
                'office_code' => $office_code,
                'office_address' => $office_address,
                'currency' => $courierInfo['currency'],
                'main_currency' => site()->currency,
            ],
            'receiver_address' => $receiver_address,
            'cash_on_delivery_amount' => $cash_on_delivery_amount,
            'insurance_amount' => $insurance_amount,
            'company_id' => $this->getSetting('settings.default_company') ?? null,
            'package' => null,
            'weight' => null,
            'fragile' => $this->getSetting('breakable') ?? null,
            'option_before_payment' => $this->getSetting('allowShipmentCheck') ?? null,
            'saturday_delivery' => $this->getSetting('saturday_delivery') ?? null,
            'side' => $this->getSetting('side') ?? null,
            'courier_id' => $order->shipping->provider->external_provider->data,
        ];

        $result = $this->createBillOfLading($params, $request_response);

        $categories = $order->products->where('digital', YesNo::False)->pluck('category_id')->toArray();
        $result = $this->formatCreate(Format::moneyInput($order->price_total - $order->shipping->order_amount), $result, null, $categories);

        $waybillStd->waybill_id = $result->getBolId();
        $order->updateMeta([
            'integration' => $this->getKey(),
            'service_name' => $result->getServiceId(),
            'pdf_url' => $result->getBillOfLadingUrl(),
            'bol_id' => $waybillStd->waybill_id,
            'total' => Format::toIntegerPrice($result->getTotal()),
            'original_total' => $result->getParameter('original_price') != $result->getTotal() ? Format::toIntegerPrice($result->getParameter('original_price')) : null,
            'currency' => $result->getCurrency(),
            'omniship.credentials' => json_encode($this->getCredentials()),
            'money_transfer' => $this->isMoneyTransfer(),
        ]);


        $fulfilled = $order->changeStatus('fulfilled', true, [
            'shipping_tracking_number' => $waybillStd->waybill_id,
            'shipping_date_delivery' => $result->getEstimatedDeliveryDate(),
            'shipping_date_expedition' => $result->getPickupDate(),
            'shipping_tracking_url' => $this->getSetting('tracking', 'provider') == 'cloudcart' ? \LinkerCp::siteFullLink('/tracking/' . $waybillStd->waybill_id) : $this->manager->trackingUrl($waybillStd->waybill_id)
        ]);
        if ($fulfilled && $fulfilled instanceof OrderFulfillment) {
            $fulfilled->update([
                'shipping_tracking_number' => $waybillStd->waybill_id,
                'shipping_date_delivery' => $result->getEstimatedDeliveryDate(),
                'shipping_date_expedition' => $result->getPickupDate(),
                'shipping_tracking_url' => $this->getSetting('tracking', 'provider') == 'cloudcart' ? \LinkerCp::siteFullLink('/tracking/' . $waybillStd->waybill_id) : $this->manager->trackingUrl($waybillStd->waybill_id)
            ]);
        }

        if (!in_array($order->status, ['completed', 'paid'])) {
            $shipping_provider = $order->initShippingQuote(null, [
                'service_id' => $params['service_id'] ?? null,
                'overwrite_price' => true,
                'price' => Format::toIntegerPrice($result->getTotal())
            ]);

            $order->updateShippingFromInitQuote($shipping_provider);
            $order->updateOrderTotals();
        }

        $order->updateShippingFromInitQuote($shipping_provider);

        $order->updateOrderTotals();
    }

    /**
     * @param $id
     * @return CouriersList|array|\Illuminate\Database\Eloquent\Model|object
     */
    public function getCourierInfo($id): ?array
    {
        $courier = CouriersList::where('courier_id', $id)->first();
        if (!$courier) {
            return null;
        }

        return [
            'name' => $courier->name, '( ' . $courier->country . ' )',
            //   'logo' => 'sitecp/img/shipping_provider/eushipment-625.png',
            'logo' => 'sitecp/img/shipping_provider/' . $courier->logo,
            'currency' => $courier->currency,
            'country' => $courier->country,
            'options' => [
                'insurance' => (int)$courier->insurance,
                'multy-packages' => (int)$courier->multy_packages,
                'open' => (int)$courier->open,
                'cod' => (int)$courier->cod,
                'saturday' => (int)$courier->saturday
            ]
        ];
    }


    /**
     * @param mixed $name
     * @param mixed $city_id
     * @return mixed
     */
    public function findLockers($name, $city_id = null): void
    {
        // TODO: Implement findLockers() method.
    }

    /**
     * @param mixed $office_id
     * @return mixed
     */
    public function getLockerById($office_id): void
    {
        // TODO: Implement getLockerById() method.
    }

    /**
     * @param mixed $name
     * @return mixed
     */
    public function lockersAutocomplete($name): void
    {
        // TODO: Implement lockersAutocomplete() method.
    }

    /**
     * @param mixed $isMachine
     * @return mixed
     */
    public function allOffices($isMachine): Collection
    {
        if ($isMachine) {
            return collect();
        }

        return $this->cache(__FUNCTION__ . '.2' . $isMachine . $this->getProvider()->external_id . site()->language, fn() => Offices::has('city')->with('city.country')
            ->where('courier_id', $this->getProvider()->external_provider->data ?? null)
            ->get()->map(function (Offices $office): \Omniship\Address\Office {
                $name = 'name_' . (site('language') == 'bg' ? 'bg' : 'en');
                $officeCollect = new Office([
                    'id' => $office->id,
                    'name' => $office->office_name,
                    'address_string' => $office->address,
                    'latitude' => $office->lat ?? 0,
                    'longitude' => $office->lng ?? 0,
                    'max_weight' => 1000,
                    'weight_unit' => MassUnit::KILOGRAM,
                    'city_id' => $office->city_id,
                    'type' => 'office',
                    'provider' => 'eushipment_' . $this->getProvider()->external_id,
                    'city' => [
                        'id' => $office->city_id,
                        'post_code' => $office->post_code,
                        'type' => 'city',
                        'name' => $office->city->$name,
                    ],
                    'country' => [
                        'id' => $office->city->country_id,
                        'name' => $office->city->country->$name,
                        'iso2' => $office->city->country->iso_code,
                        'iso3' => '',
                    ],
                ]);
                $officeCollect->setParameter('maps_icon', 'eushipment_' . $office->courier_id);
                return $officeCollect;
            }));
    }

    /**
     * @param $provider
     * @return array
     */
    public function fullfilementSettings(?\App\Models\Shipping\ShippingProvider $provider): array
    {
        $this->setProvider($provider);
        $settings = $this->collectSetting();
        return [
            'compare' => !empty($settings['settings.fullfilment_compare']) ? $settings['settings.fullfilment_compare'] : null,
            'document' => !empty($settings['settings.fullfilment_documents']) ? $settings['settings.fullfilment_documents'] : null,
        ];
    }

    /**
     * @return true
     */
    #[\Override]
    public function supportsTrackingParcel(): bool
    {
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function trackingParcel($bol_id)
    {
        $orderId = OrderFulfillment::where('shipping_tracking_number', $bol_id)->value('order_id');
        $bol_id = 'Order' . site()->site_id . '-' . $orderId;
        if ($this->supportsTrackingParcel()) {
            $tracking = $this->manager->trackingParcel($bol_id)->send();
            $this->_latest_request = $tracking->getRequest();
            if ($tracking->getCode()) {
                throw new Error($tracking->getMessage(), $tracking->getCode());
            }
            return $tracking->getData();
        }
        return null;
    }


    /**
     * @inheritdoc
     */
    #[\Override]
    protected function getProviderCustomSettings(): array
    {
        $last_url_segment = request()->segments()[count(request()->segments()) - 1];
        $this->setProviderByExternalId($last_url_segment);
        $provider = $this->getProvider();
        $setting = array_merge($provider->meta->pluck('value', 'parameter')->all(), $this->collectSetting()->all());

        $settings = [];
        foreach ($setting as $key => $value) {
            if ($key == 'settings' && is_array($value)) {
                foreach ($value as $k => $v) {
                    $settings[$k] = $v;
                }

                unset($settings[$key]);
            } else {
                $key = str_replace('settings.', '', $key);
                $settings[$key] = $value;
            }
        }

        $settings = array_merge($settings, [
            'public_key' => $this->getSetting('public_key'),
            'default_weight' => $settings['default_weight'] ?? null,
            'side' => $settings['side'] ?? null,
            'default_company' => $settings['default_company'] ?? null,
            'return_documents' => $settings['return_documents'] ?? null,
            'cod' => intval($settings['cod'] ?? 0),
            'insurance' => intval($settings['insurance'] ?? 0),
            'declared_value' => intval($settings['declared_value'] ?? 0),
            'open_package' => intval($settings['open_package'] ?? 0),
            'saturday_delivery' => intval($settings['saturday_delivery'] ?? 0),
            'fragile' => intval($settings['fragile'] ?? 0),
            'fullfilment' => intval($settings['fullfilment'] ?? 0),
            'fullfilment_compare' => !empty($settings['fullfilment_compare']) ? $settings['fullfilment_compare'] : 'sku',
            'fullfilment_documents' => !empty($settings['fullfilment_documents']) ? $settings['fullfilment_documents'] : 'nothing'
        ]);
        return $settings;
    }

    /**
     * @return array
     * @throws Error
     */
    #[\Override]
    public function getClientSettings(): array
    {
        $settings = array_merge([
            'active' => intval(($this->getProvider()->active ?? 0) && $this->isActive()),
            'side' => $this->getPayerSideFromConfig(),
        ], $this->getProviderCustomSettings());

        if ($this->supportsCashOnDelivery()) {
            $settings['cod'] = intval($this->collectSetting('cod'));
        }

        if ($this->supportsInsurance()) {
            $settings['insurance'] = intval($this->collectSetting('insurance'));
        }

        if ($this->supportsTrackingParcel()) {
            $settings['tracking'] = $this->collectSetting('tracking');
        }

        if ($this->supportSyncPayments()) {
            $settings['sync_payments'] = intval($this->collectSetting('sync_payments'));
        }

        if ($this->supportsBoxingParcel()) {
            $settings['boxes'] = (static function ($boxes) {
                if (!is_array($boxes)) {
                    return array_values(array_filter(explode(',', $boxes)));
                }

                return $boxes;
            })($this->getSetting('boxes'));
        }

        if ($this->supportsGetServices()) {
            $settings['allowed_methods'] = (static function ($allowed_methods) {
                if (!is_array($allowed_methods)) {
                    return array_values(array_filter(explode(',', $allowed_methods)));
                }

                return $allowed_methods;
            })($this->getSetting('allowed_methods'));
        }

        foreach ($this->support ?? [] as $support) {
            $settings['to_' . $support] = intval($this->collectSetting('to_' . $support));
            $settings['pricing_' . $support] = $this->collectSetting('pricing_' . $support);
            if (in_array($settings['pricing_' . $support], ['calculator', 'calculator_fixed', 'free', 'fixed_price', 'fixed_weight'])) {
                $settings['fallback_price_' . $support] = intval($this->collectSetting('fallback_price_' . $support));
                $settings['use_price_category_' . $support] = intval($this->collectSetting('use_price_category_' . $support));
                $settings['countries_' . $support] = (function ($val) {
                    if (empty($val)) {
                        return [];
                    }

                    return array_values(array_filter(is_array($val) ? $val : explode(',', $val)));
                })($this->collectSetting('countries_' . $support));
            }
        }


        foreach ($this->support ?? [] as $support) {
            $settings['fixed_price_' . $support] = $this->collectSetting('fixed_price_' . $support);
            $settings['free_method_city_' . $support] = $this->collectSetting('free_method_city_' . $support);
            $settings['free_method_intercity_' . $support] = $this->collectSetting('free_method_intercity_' . $support);
            $settings['free_method_international_' . $support] = $this->collectSetting('free_method_international_' . $support);
            $settings['free_shipping_total_' . $support] = $this->collectSetting('free_shipping_total_' . $support);
        }

        foreach (['default_weight', 'default_width', 'default_height', 'default_depth'] as $key) {
            $settings[$key] = is_numeric($this->collectSetting($key)) ? $this->collectSetting($key) : null;
            if ($key == 'default_weight' && is_numeric($settings[$key])) {
                $settings[$key] = weightInput(Format::toIntegerWeight($settings[$key]));
            }
        }

        return $settings;
    }

    /**
     * @param $thumb_size
     * @param $externalId
     * @return string
     * @throws Error
     */
    public function getExternalImage($thumb_size = null, $externalId = null): string
    {
        $this->setProviderByExternalId($externalId);
        $provider = $this->getProvider();
        if ($provider && $provider->hasImage()) {
            return $this->getProvider([])->getImage($thumb_size);
        }

        if (!empty($this->getProvider()->external_id) && !empty($this->getProvider()->external_provider->data)) {
            $explode = explode('_', (string)$this->getKey());
            return config('url.img') . 'sitecp/img/app-icons/' . $explode[0] . '-' . $this->getProvider()->external_provider->data . '.png';
        }

        if (!$this->sf) {
            return config('url.img') . 'sitecp/img/app-icons/icon-' . $this->getKey() . '.png';
        }

        return config('url.img') . 'sitecp/img/shipping_provider/' . $this->getKey() . '-logo.png';
    }

    #[\Override]
    public function getKey()
    {
        if (!empty($this->provider->omniship_key)) {
            return $this->_app_key = Arr::last(explode('.', (string)$this->provider->omniship_key, 2));
        }

        return parent::getKey();
    }

    public function getGlobalApp(): ?Applications
    {
        return Applications::getApp(self::APP_KEY);
    }

    /**
     * @return array
     * @throws Error
     */
    public function supportDeliveryType(): array
    {
        $support = [];
        if ($this->getProvider()->external_provider) {
            $courierId = $this->getProvider()->external_provider->data;
            $courier = $this->getCourier($courierId);
            if ($courier) {
                if ($courier['to_office']) {
                    $support[] = static::SUPPORT_OFFICE;
                }

                if ($courier['to_address']) {
                    $support[] = static::SUPPORT_ADDRESS;
                }
            }
        }

        return $support;
    }

    /**
     * @param null $type
     * @return mixed
     * @throws Error
     */
    #[\Override]
    public function getFreeShippingTotal($type = null)
    {
        $settings = $this->getProvider()->meta;
        $total = $settings->where('parameter', 'settings.free_shipping_total_' . $this->getAddressType())->first();
        if ($total) {
            $total = $total->value;
        } else {
            $total = $settings->where('parameter', 'settings.free_shipping_total')->first();
            if ($total) {
                $total = $total->value;
            }
        }

        if ($type == 'input') {
            return $total;
        }

        return Format::toIntegerPrice($total);
    }

    /**
     * @param OrderContract $order
     * @return ItemBag
     * @throws Error
     */
    #[\Override]
    public function convertOrderToItemsBag(OrderContract $order): ItemBag
    {
        $list = [];
        /** @var ItemContract $product */
        foreach ($order->getProducts() as $product) {

            $list[$product->getItemKey()] = [
                'id' => $product->getId(),
                'name' => $product->getName(),
                'weight' => floatval($product->getWeight() ? Weight::input($product->getWeight()) : $this->getDefaultWeight()),
//                    'price' => round(($product->getTotalPriceWithOptionsAfterDiscountsNew() / $product->getQuantity()) / 100, 2),
                'price' => round(($product->getTotalPriceWithOptionsAfterDiscountsWithModification() / $product->getQuantity()) / 100, 2),
                'quantity' => (int)$product->getQuantity(),
                'width' => $product->getWidth() ?: $this->getDefaultWidth(),
                'height' => $product->getLength() ?: $this->getDefaultHeight(),
                'depth' => $product->getDepth() ?: $this->getDefaultLength(),
                'sku' => $product->getSku(),
                'barcode' => $product->getBarcode(),
            ];
        }

        $totals = $order->getTotals();
        $discounts = moneyInput($totals->only(['discount.before', 'discount.after'])
            ->collapse()->sum('price_calculation'), $order->getCurrency(), $order->getLanguage());

        if ($discounts < 0) {
            $total = array_sum(array_column($list, 'price'));
            if ($total > 0) {
                $add = $discounts / $total;
                $totalAdded = 0;
                foreach ($list as $listKey => $listItem) {
                    $added = round($listItem['price'] * $add);
                    $totalAdded += $added;
                    $list[$listKey]['price'] += $added;
                }

                if ((float)$discounts != (float)$totalAdded) {
                    $diff = (float)$discounts - (float)$totalAdded;
                    foreach ($list as $listKey => $listItem) {
                        if ($listItem['price'] + $diff > 0) {
                            $list[$listKey]['price'] += $diff;
                            break;
                        }
                    }
                }
            }
        }

        return new ItemBag($list);
    }

    /**
     * @return true
     */
    #[\Override]
    public function supportDebugBillOfLading(): bool
    {
        return true;
    }

    public function isSubProvider(): void
    {
        dump($this->getExternalId());

    }

    /**
     * @return string
     * @throws Error
     */
    #[\Override]
    public function getPayerSideFromConfig()
    {
        return $this->collectSetting()->get('settings.side');
    }

    /**
     * @return int
     * @throws Error
     */
    #[\Override]
    public function getFixedPrice()
    {
        $settings = $this->getProvider()->meta;
        $total = $settings->where('parameter', 'settings.fixed_price_' . $this->getAddressType())->first();
        if ($total) {
            $total = $total->value;
        } else {
            $total = $settings->where('parameter', 'settings.fixed_price_address')->first();
            if ($total) {
                $total = $total->value;
            }
        }

        return $total;
    }

}
