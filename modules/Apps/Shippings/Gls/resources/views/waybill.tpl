<div class="order-shipping-details-box" data-box-ajax="{route('apps.mikmik.waybill', $order->id)}">
    <form action="{route('apps.gls.waybill.save', $order_id)}" id="issueWaybill" role="form" class="ajaxForm">

        <input type="hidden" name="sync_order_and_provider_amount" id="sync_order_and_provider_amount" value="0"/>

        <div class="row">
            <div class="col-xs-12">
                <!-- COMMON -->
                <div class="box">
                    <div class="box-title">
                        <div class="box-title-text">
                            <h5>{t}shipping.waybill.injunction.title{/t}</h5>
                        </div>
                    </div>

                    <div class="box-section">
                        <div class="form-group-dynamic">
                            <div class="row form-group">
                                <div class="col-xs-7">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="control-label">{t}shipping.waybill.label.contents{/t} <i
                                                    class="glyphicon glyphicon-info-sign tooltips" title=""
                                                    data-placement="top"
                                                    data-original-title="{t}shipping.waybill.info.contents{/t}"></i></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-5">
                                    <input type="text" name="waybill[contents]" id="gls_contents"
                                           class="form-control"
                                           placeholder="{t}shipping.waybill.ph.contents{/t}"
                                           value="{$contents}"/>
                                </div>
                            </div>

                            <div class="row form-group">
                                <div class="col-xs-7">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label for="evropat_package"
                                                   class="form-control-check">{t}shipping.waybill.ph.pack_count{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-5">
                                    <input type="number" name="waybill[package]" id="gls_package"
                                           class="form-control" value="{$package_count}"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="box js-fancourier-services-holder">
                        <div class="box-title">
                            <div class="box-title-text">
                                <h5>{t}gls::gls.additional_services{/t}</h5>
                                <p>{t}gls::gls.additional_services_description{/t}</p>
                            </div>
                        </div>

                        <div class="box-section">
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="js-fancourier-services" data-name="allowed_methods" data-multiple>
                                        <select name="waybill[additional_services][]" multiple class="form-control select2me" id="additional_services"  >
                                            {foreach $additional_services as $key => $service}
                                                <option value="{$key}"{if $settings->get(mb_strtolower($key)) == 1} selected{/if}>{$service}</option>
                                            {/foreach}
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="box-section">
                        {if $order->payment->provider == 'cod'}
                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <div class="stack">
                                        <div class="stack-main">
                                            <label
                                                class="form-control-check">{t}shipping.waybill.label.cod{/t}</label>
                                        </div>

                                        <div class="stack-addon">
                                            <input type="checkbox" name="waybill[cod]" id="gls_cod"
                                                   class="switch"
                                                   value="1"
                                                   {if $order->meta_pluck->get('cod')}checked="checked"{/if}/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="row form-group {if !$order->meta_pluck->get('cod')}hidden{/if} js-gls-code-change-active">
                                <div class="col-xs-9">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label
                                                class="form-control-check">{t}shipping.waybill.label.total{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-3">
                                    <div class="input-group">
                                        <input type="text" name="waybill[total]" id="gls_total"
                                               class="form-control"
                                               value="{$cod_total}"/>
                                        <span class="input-group-addon">{$currency}</span>
                                    </div>
                                </div>
                            </div>
                        {/if}
                        <div class="row form-group">
                            <div class="col-xs-12">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label
                                            class="form-control-check">{t}shipping.waybill.label.insurance{/t}</label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" name="waybill[insurance]" id="gls_insurance"
                                               class="switch"
                                               placeholder="{t}gls::gls.waybill.ph.insurance{/t}"
                                               value="1"
                                               {if $declared_value == 1}checked="checked"{/if}/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            class="row form-group js-gls-insurance-change-active {if !$order->meta_pluck->get('insurance')}hidden{/if}">
                            <div class="col-xs-9">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label
                                            class="form-control-check">{t}shipping.waybill.label.total_insurance{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-3">
                                <div class="input-group">
                                    <input type="text" name="waybill[totalInsurance]" id="gls_Insurance"
                                           class="form-control"
                                           value="{$declared_amount}"/>
                                    <span class="input-group-addon">{$currency}</span>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-xs-12">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="form-control-check">{t}gls::gls.declared_amount{/t}</label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" name="waybill[declared]" id="gls_declared"
                                               class="switch"
                                               placeholder="{t}gls::gls.waybill.ph.insurance{/t}"
                                               value="1"
                                               {if $declared_value == 1}checked="checked"{/if}/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-group js-gls-declared-change-active">
                            <div class="col-xs-9">
                                <div class="form-control-box">
                                    <div class="form-control-box-inner">
                                        <label
                                            class="form-control-check">{t}gls::gls.declared_amount_amount{/t}</label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xs-3">
                                <div class="input-group">
                                    <input type="text" name="waybill[declared_value]" id="gls_Insurance"
                                           class="form-control"
                                           value="{$declared_amount}"/>
                                    <span class="input-group-addon">{$currency}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </form>
</div>

{script}
    <script type="text/javascript">

        $('#gls_cod').on('change', function () {
            CC.statusBox($('.js-gls-code-change-active'), $(this).is(':checked'));
        }).trigger('change');

        if ($('#gls_insurance').is(':checked')) {
            $('.js-gls-insurance-change-active').removeClass('hidden');
            $('.js-gls-fragile-change-active').removeClass('hidden');
        }

        $('#gls_insurance').on('change', function () {
            CC.statusBox($('.js-gls-insurance-change-active'), $(this).is(':checked'));
            if ($(this).is(':checked')) {
                $('.js-gls-insurance-change-active').removeClass('hidden');
                $('.js-gls-fragile-change-active').removeClass('hidden');
            } else {
                $('.js-gls-insurance-change-active').addClass('hidden');
                $('.js-gls-fragile-change-active').addClass('hidden');
            }
        }).trigger('change');

        $('#gls_declared').on('change', function () {
            CC.statusBox($('.js-gls-declared-change-active'), $(this).is(':checked'));
            if ($(this).is(':checked')) {
                $('.js-gls-declared-change-active').removeClass('hidden');
            } else {
                $('.js-gls-declared-change-active').addClass('hidden');
            }
        }).trigger('change');


        $('.order-shipping-details [data-dismiss="modal"]').on('click', function () {
            $('.order-shipping-details').remove();
            $('.btn-ship').removeClass('invisible').show();
        });

        $('.js-btn-calculate').off('click').on('click', function () {
            var $form = $('#issueWaybill'),
                $btn = $(this);

            $btn.trigger('loading.start');
            $btn.prop("disabled", true);

            $form.off('cc.ajax.success').on('cc.ajax.success', function (e, json) {
                $btn.trigger('loading.end');
                $btn.prop("disabled", false);
                if (json.status === 'error') {
                    Custom.handleAjaxFormError(false, false, data, $form);
                    return;
                }
                if (json.status === 'success') {
                    var $holder = serviceInput.empty();
                    $holder.append('<option value="">&nbsp;</option>');
                    for (var i in json.calculate) {
                        var text = json.calculate[i].name;
                        if (json.calculate[i].price > 0 || json.calculate[i].original_price > 0) {
                            text += ' (' + json.calculate[i].price_formatted + ')';
                            if (json.calculate[i].price !== json.calculate[i].original_price) {
                                text += ' (' + json.calculate[i].original_price_formatted + ')';
                            }
                        }
                        var $option = $('<option>').text(text)
                            .attr('data-total', json.calculate[i].price)
                            .attr('data-type', json.calculate[i].type)
                            .attr('value', json.calculate[i].id);
                        if (old_service_id === json.calculate[i].id) {
                            $option = $option.attr('selected', 'selected');
                        }
                        $holder.append($option);
                    }
                    $holder.trigger('change');

                    $('.js-generate-bill-of-lading').removeAttr('disabled');

                    //$('#order_summary').trigger('cc.ajax.reload');
                    $('#order_history').trigger('cc.ajax.reload');

                    //$('.order-shipping-details-box').trigger('cc.ajax.reload');
                    json.status = "updated"; //avoid success in order not to close the modal
                }
            });

            $form.off("cc.ajax.error").on("cc.ajax.error", function (e, res) {
                $btn.trigger('loading.end');
                $btn.prop("disabled", false);
                if ("string" == typeof (res)) {
                    res = $.parseJSON(res);
                }
                swal({
                    title: TranslationLabels['error'],
                    text: res.msg ? res.msg.replace(/<br>/g, "\n") : '',
                    type: "error",
                    confirmButtonText: "OK",
                    confirmButtonClass: "btn-danger"
                });
            });
            var $summary = $('#order_summary'),
                $history = $('#order_history'),
                $waybill = $('.order-shipping-details-box');
            $form.off("cc.ajax.success").on("cc.ajax.success", function (e, res) {
                $summary.trigger('cc.ajax.reload');
                $history.trigger('cc.ajax.reload');
                $waybill.trigger('cc.ajax.reload');
                $summary.one('cc.ajax.success', function () {
                    $('.btn-ship').trigger('click');
                });
            });

            Custom.submitFormAjax($form, "{route('apps.gls.calculate', $order->id)}");
            return false;
        });
        $('.js-generate-bill-of-lading').off('click').on('click', function () {
            var $form = $('#issueWaybill'),
                $btn_waybilling = $(this);

            {if !in_array($order->status, ['completed', 'paid'])}
            var syncAmountField = $form.find($('#sync_order_and_provider_amount'));
            Custom.confirm({
                'title': '{t js=true}shipping.waybill.sync_provider_and_order_amount.title{/t}',
                'message': '{t js=true}shipping.waybill.sync_provider_and_order_amount.message{/t}',
                'success': function () {
                    syncAmountField.val(1);
                    $form.trigger('submit');
                },
                'cancel': function () {
                    syncAmountField.val(0);
                    $btn_waybilling.prop("disabled", false);
                    $btn_waybilling.removeClass('loading');
                }
            });
            {else}
            $form.trigger('submit');
            {/if}

            $btn_waybilling.trigger('loading.start');
            $btn_waybilling.prop("disabled", true);

            $form.on('cc.ajax.success', function (e, json) {
                $btn_waybilling.trigger('loading.end');
                $btn_waybilling.prop("disabled", false);
                if (json.status == 'error') {
                    Custom.handleAjaxFormError(false, false, data, $form);
                    return;
                }
                if (json.status == 'success') {
                    $('#order_summary').trigger('cc.ajax.reload');
                    $('#order_status').trigger('cc.ajax.reload');
                    $('#order_customer').trigger('cc.ajax.reload');
                    $('#order_shipping_address').trigger('cc.ajax.reload');
                    $('#order_history').trigger('cc.ajax.reload');
                    $('.order-shipping-details').remove();
                }
            });

            $form.on("cc.ajax.error", function (e, res) {
                $btn_waybilling.trigger('loading.end');
                $btn_waybilling.prop("disabled", false);
                if ("string" == typeof (res)) {
                    res = $.parseJSON(res);
                }
                swal({
                    title: TranslationLabels['error'],
                    text: res.msg ? res.msg.replace(/<br>/g, "\n") : '',
                    type: "error",
                    confirmButtonText: "OK",
                    confirmButtonClass: "btn-danger"
                });
            });
        });
    </script>
{/script}
