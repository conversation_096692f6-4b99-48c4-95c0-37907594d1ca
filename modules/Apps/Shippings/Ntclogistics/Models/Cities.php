<?php

declare(strict_types=1);

namespace Modules\Apps\Shippings\Ntclogistics\Models;

use Illuminate\Database\Query\Builder;
use App\Traits\Model as ModelTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

/**
 * @method static Model|Builder whereNotNull($key)
 * @method static int insert($data)
 * @method static Builder|Cities where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder|Cities orWhere($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Builder|Cities whereIn($column, $value = null, $boolean = 'and')
 * @method static Builder|Cities whereRaw($where, $bindings = [])
 * @method static Builder|Cities find($id, $data = '*')
 * @method static Builder|Cities search($name)
 * @method static Builder|Cities whereCountryId($value)
 * @property integer $id
 * @property mixed $post_code
 * @property string $name
 * @property int $country_id
 */
class Cities extends Model
{
    use ModelTrait;

    protected $connection = 'apps';

    protected $table = 'cc_apps.shipping_ntclogistics_cities';

    protected $words_black_list = [

    ];

    public $fillable = [
        'name', 'post_code'
    ];

    public $timestamps = false;

    /**
     * @param $query
     * @param $name
     * @return mixed
     */
    public function scopeSearch($query, $name)
    {
        $words = $this->getKeywords($name);
        if (!$words) {
            /** @var Cities $query */
            return $query->whereRaw('0=1');
        }

        /** @var Cities $query */
        return $query->where(function ($query) use ($name, $words): void {
            /** @var Cities $query */
            $query->orWhere('name', 'like', sprintf('%%%s%%', $name));
            foreach ($words as $name) {
                $query->orWhere('name', 'like', sprintf('%%%s%%', $name));
            }
        });
    }

    /**
     * Method extracts words from a string.
     *
     * @param string $string The input string
     *
     * @return array
     */
    public function getKeywords($string)
    {
        $words = Collection::make(preg_split("/[\s+|,|\-|\(|\)]/", Str::lower($string), 10, PREG_SPLIT_NO_EMPTY))
            ->unique()->filter(fn($word): bool => Str::length($word) > 2 && !in_array(Str::lower($word), $this->words_black_list));

        return $words->all();
    }
}
