<div class="order-shipping-details-box" data-box-ajax="{route('apps.berry.waybill', $order->id)}">
    <form action="{route('apps.berry.waybill.save', $order_id)}" id="issueWaybill" role="form" class="ajaxForm">

        <input type="hidden" name="sync_order_and_provider_amount" id="sync_order_and_provider_amount" value="0" />

        <div class="row">
            <div class="col-xs-12">
                <!-- COMMON -->
                <div class="box">
                    {if $sender_addresses->count() > 1}
                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-12">
                                <label class="control-label">{t}shipping.pickup.label.from{/t} <i
                                            class="glyphicon glyphicon-info-sign tooltips" title="" data-placement="top"
                                            data-original-title="{t}shipping.price.diff{/t}"></i></label>
                                <select name="sender_address" class="form-control select2me" data-no-input="true">
                                    {foreach $sender_addresses AS $addr}
                                        <option value="{$addr->id}" {if $addr->active}selected="selected"{/if}>{$addr->name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                    </div>
                    {else}
                    <input type="hidden" name="sender_address" value="default">
                    {/if}

                    <div class="box-section">
                        <div class="row form-group">
                            <div class="col-xs-12">
                                {t}shipping.waybill.label.service{/t}: {$order->meta_pluck->get('service_name')}
                                {if $order->shippingAddress}
                                <p>
                                    {$order->shippingAddress->format(false, false)}
                                </p>
                                {/if}
                            </div>
                        </div>
                    </div>

                    <div class="box-section">
                        <div class="form-group-dynamic">
                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <label class="control-label">{t}shipping.waybill.label.contents{/t} <i
                                                class="glyphicon glyphicon-info-sign tooltips" title="" data-placement="top"
                                                data-original-title="{t}shipping.waybill.info.contents{/t}"></i></label>

                                    <textarea name="waybill[contents]" id="berry_contents" class="form-control" placeholder="{t}shipping.waybill.ph.contents{/t}">{$order_contents}, {$contents}</textarea>
                                </div>
                            </div>

                            <div class="row form-group">
                                <div class="col-xs-7">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="control-label">{t}shipping.side{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-5">
                                    <select name="waybill[side]" id="berry_side" class="form-control select2me" data-no-input="true">
                                        {foreach $waybill_sides AS $waybill_side}
                                            <option {if $waybill_side['id'] == $side}selected="selected"{/if} value="{$waybill_side['id']}">{$waybill_side['name']}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                            <div class="row form-group">
                                <div class="col-xs-7">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="control-label">{t}shipping.waybill.label.service{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-5">
                                    <select name="waybill[service_id]" data-no-input="true" class="form-control select2me js-serices-place" id="js-serices-place-holder">
                                        <option value="">&nbsp;</option>
                                        {foreach $calculate AS $c}
                                            <option value="{$c['id']}" data-type="{$c['type']}" data-total="{$c['price']}" selected="selected">
                                                {if $service_name != false && $order->meta_pluck->get('service_id') != 1} {$service_name} {else} {$c['name']}  {/if}
                                                {if $c['price'] > 0 || $c['original_price'] > 0}({$c['price_formatted']}) {if $c['price_formatted'] != $c['original_price_formatted']}({$c['original_price_formatted']}){/if}{/if}
                                            </option>
                                            {break}
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                            <div class="row form-group">
                                <div class="col-xs-7">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="control-label">{t}berry::berry.waybill.start_address{/t}
                                                <i class="glyphicon glyphicon-info-sign tooltips" title="" data-placement="top" data-original-title="{t}berry::berry.waybill.start_address_help{/t}"></i></label>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-5">
                                    <select name="waybill[warehouse_id]" data-no-input="true" class="form-control select2me">
                                        {foreach $warehouses AS $warehouse}
                                            <option
                                                    {if $selected_warehouses == $warehouse['warehouse_id'] && is_null($order->meta_pluck->get('warehouse_id'))} selected="selected"
                                                    {elseif $warehouse['warehouse_id'] == $order->meta_pluck->get('warehouse_id') } selected="selected"
                                                    {/if}
                                                    value="{$warehouse['warehouse_id']}">
                                                {$warehouse['address']}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>

                            <div class="row form-group">
                                <div class="col-xs-7">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="control-label">{t}berry::berry.waybill.select_date_time{/t}
                                                <i class="glyphicon glyphicon-info-sign tooltips" title="" data-placement="top" data-original-title="{t}berry::berry.waybill.select_date_time_help{/t}"></i></label>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-5">
                                    <select name="waybill[dropoff_id]" data-no-input="true" class="form-control select2me">
                                        {foreach $dropoff_slots AS $dropoff}
                                            <option value="{$dropoff['id']}" {if $order->meta_pluck->get('dropoff_id') == $dropoff['id']} selected="selected" {/if}>{$dropoff['name']}</option>
                                        {/foreach}
                                    </select>
                                </div>
                            </div>
                            <div class="row form-group js-berry-instruction-dc">
                                <div class="col-xs-12">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="form-control-check">{t}shipping.waybill.instruction{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-12">
                                    <textarea class="form-control" name="waybill[instruction]"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="box-title">
                        <div class="box-title-text">
                            <h5>{t}shipping.waybill.injunction.title{/t}</h5>
                        </div>
                    </div>

                    <div class="box-section">
                        {*
                        {if !$payed && $order->isCashOnDelivery()}
                            *}
                        {if !$payed}
                            <div class="row form-group">
                                <div class="col-xs-12">
                                    <div class="stack">
                                        <div class="stack-main">
                                            <label class="form-control-check">{t}shipping.waybill.label.cod{/t}</label>
                                        </div>

                                        <div class="stack-addon">
                                            <input type="checkbox" name="waybill[cod]" id="berry_cod" class="switch"
                                                   placeholder="{t}berry::berry.waybill.ph.cod{/t}"
                                                   value="1" {if $order->meta_pluck->get('cod')}checked="checked"{/if}/>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row form-group {if !$order->meta_pluck->get('cod')}hidden{/if} js-berry-code-change-active">
                                <div class="col-xs-9">
                                    <div class="form-control-box">
                                        <div class="form-control-box-inner">
                                            <label class="form-control-check">{t}shipping.waybill.label.total{/t}</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xs-3">
                                    <div class="input-group">
                                        <input type="text" name="waybill[total]" id="berry_total" class="form-control"
                                               value="{$cod_total}"/>
                                        <span class="input-group-addon">{$currency}</span>
                                    </div>
                                </div>
                            </div>
                        {/if}
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

{script}
<script type="text/javascript">
$('#berry_insurance').on('change', function() {
    CC.statusBox($('.js-berry-insurance-change-active'), $(this).is(':checked'));
}).trigger('change');
$('#berry_cod').on('change', function() {
    CC.statusBox($('.js-berry-code-change-active'), $(this).is(':checked'));
}).trigger('change');
$('[name="waybill[pickup]"]').on('change', function() {
    CC.statusBox($('.js-berry-pickup-office'), $(this).val() == 'office');
    CC.statusBox($('.js-berry-pickup-address'), $(this).val() == 'address');
}).filter(':checked').trigger('change');
$('[data-related]').on('change', function() {
    var $el = $(this);
    if($el.is(':checked') && $el.data('related')) {
        App.updateSwitch($('[name="'+$el.data('related')+'"]').prop('checked', false));
    }
});
var serviceInput = $('[name="waybill[service_id]"]');
var old_service_id = parseInt(serviceInput.val());
serviceInput.off('change').on('change', function() {
    old_service_id = parseInt(serviceInput.val());
}).trigger('change');

$('.js-generate-bill-of-lading').off('click').on('click', function() {
    var $form = $('#issueWaybill'),
        $btn_waybilling = $(this);

    {if !in_array($order->status, ['completed', 'paid'])}
        var syncAmountField = $form.find($('#sync_order_and_provider_amount'));
        Custom.confirm({
            'title' : '{t js=true}shipping.waybill.sync_provider_and_order_amount.title{/t}',
            'message': '{t js=true}shipping.waybill.sync_provider_and_order_amount.message{/t}',
            'success': function() {
                syncAmountField.val(1);
                $form.trigger('submit');
            },
            'cancel': function() {
                syncAmountField.val(0);
                $form.trigger('submit');
            }
        });
    {else}
        $form.trigger('submit');
    {/if}

    $btn_waybilling.trigger('loading.start');
    $btn_waybilling.prop("disabled", true);

    $form.on('cc.ajax.success', function(e, json) {
        $btn_waybilling.trigger('loading.end');
        $btn_waybilling.prop("disabled", false);
        if(json.status == 'error') {
            Custom.handleAjaxFormError(false, false, data, $form);
            return;
        }
        if(json.status == 'success') {
            $('#order_summary').trigger('cc.ajax.reload');
            $('#order_status').trigger('cc.ajax.reload');
            $('#order_customer').trigger('cc.ajax.reload');
            $('#order_shipping_address').trigger('cc.ajax.reload');
            $('#order_history').trigger('cc.ajax.reload');
            $('.order-shipping-details').remove();
        }
    });

    $form.on("cc.ajax.error", function (e, res) {
        $btn_waybilling.trigger('loading.end');
        $btn_waybilling.prop("disabled", false);
        if ("string" == typeof(res)) {
            res = $.parseJSON(res);
        }
        swal({
            title: TranslationLabels['error'],
            text: res.msg ? res.msg.replace(/<br>/g, "\n") : '',
            type: "error",
            confirmButtonText: "OK",
            confirmButtonClass: "btn-danger"
        });
    });
});

$('.order-shipping-details [data-dismiss="modal"]').on('click', function() {
    $('.order-shipping-details').remove();
    $('.btn-ship').removeClass('invisible').show();
});

$('[name="waybill[pay_after_accept]"], [name="waybill[pay_after_test]"]').on('change', function() {
    if($('[name="waybill[pay_after_accept]"]').is(':checked') || $('[name="waybill[pay_after_test]"]').is(':checked')) {
        CC.statusBox($('.js-reject-return'), 1);
    } else {
        CC.statusBox($('.js-reject-return'), 0);
    }
}).trigger('change');
$('.js-btn-calculate').off('click').on('click', function() {
    var $form = $('#issueWaybill'),
        $btn = $(this);

    $btn.trigger('loading.start');
    $btn.prop("disabled", true);

    $form.off('cc.ajax.success').on('cc.ajax.success', function(e, json) {
        $btn.trigger('loading.end');
        $btn.prop("disabled", false);
        if(json.status === 'error') {
            Custom.handleAjaxFormError(false, false, data, $form);
            return;
        }
        if(json.status === 'success') {
            var $holder = serviceInput.empty();
            $holder.append('<option value="">&nbsp;</option>');
            for(var i in json.calculate) {
                var text = json.calculate[i].name;
                if(json.calculate[i].price > 0 || json.calculate[i].original_price > 0) {
                    text += ' (' + json.calculate[i].price_formatted + ')';
                    if(json.calculate[i].price !== json.calculate[i].original_price) {
                        text += ' (' + json.calculate[i].original_price_formatted + ')';
                    }
                }
                var $option = $('<option>').text(text)
                    .attr('data-total', json.calculate[i].price)
                    .attr('data-type', json.calculate[i].type)
                    .attr('value', json.calculate[i].id);
                if(old_service_id === json.calculate[i].id) {
                    $option = $option.attr('selected', 'selected');
                }
                $holder.append($option);
            }
            $holder.trigger('change');

            $('.js-generate-bill-of-lading').removeAttr('disabled');

            $('#order_history').trigger('cc.ajax.reload');
        }
    });

    $form.off("cc.ajax.error").on("cc.ajax.error", function (e, res) {
        $btn.trigger('loading.end');
        $btn.prop("disabled", false);
        if ("string" == typeof(res)) {
            res = $.parseJSON(res);
        }
        swal({
            title: TranslationLabels['error'],
            text: res.msg ? res.msg.replace(/<br>/g, "\n") : '',
            type: "error",
            confirmButtonText: "OK",
            confirmButtonClass: "btn-danger"
        });
    });

    Custom.submitFormAjax($form, "{route('apps.berry.calculate', $order->id)}");

    return false;
});
//on change payer side
var $side_change = $('[name="waybill[side]"]'),
        last_side = $side_change.val();
$side_change.on('change', function() {
    var $select = $(this);
    Custom.confirm({
        'message': '{t js=true}shipping.waybill.change_side{/t}',
        'success': function() {
            var $summary = $('#order_summary'),
                $history = $('#order_history'),
                $form = $('.order-shipping-details-box form');
            Ajax.post({
                url: '{route('admin.internal.change-side', $order->id)}',
                data: {
                    side: $select.val()
                },
                formHolder: $form,
                success: function(json) {
                    if(json.status === 'success') {
                        $summary.trigger('cc.ajax.reload');
                        $history.trigger('cc.ajax.reload');
                        $summary.one('cc.ajax.success', function() {
                            $('.btn-ship').trigger('click');
                        });
                    }
                }
            });
        },
        'cancel': function() {
            var $holder = $select.val(last_side).parent();
            App.destroy($holder);
            App.initSelect2($holder);
        }
    });
    return false;
});
</script>
{/script}