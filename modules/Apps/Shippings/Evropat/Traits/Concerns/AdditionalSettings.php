<?php

declare(strict_types=1);

namespace Modules\Apps\Shippings\Evropat\Traits\Concerns;

use Modules\Core\Helpers\VueJs\BoxSettings;
use Modules\Core\Helpers\VueJs\Fields\LineField;
use Modules\Core\Helpers\VueJs\Fields\NumberField;
use Modules\Core\Helpers\VueJs\Fields\RadioField;
use Modules\Core\Helpers\VueJs\Fields\SelectField;
use Modules\Core\Helpers\VueJs\Fields\StringField;
use Modules\Core\Helpers\VueJs\Fields\SwitchField;
use Modules\Core\Helpers\VueJs\ListBoxesSettings;

trait AdditionalSettings
{
    /**
     * @inheritdoc
     */
    public function getAdditionalSettings(): ListBoxesSettings
    {
        return ListBoxesSettings::make(static::APP_KEY)
            ->setBox($this->boxOne())
            ->setBox($this->boxTwo());
    }

    /**
     * @return BoxSettings
     */
    public function boxOne(): BoxSettings
    {
        return BoxSettings::make('general_settings')->setFields([
            with(RadioField::make('return_shipment', 'Payer of the courier service upon return'), function (RadioField $field): \Modules\Core\Helpers\VueJs\Fields\RadioField {
                $this->getWaybillSidesLabels()->map(function (array $side) use ($field): void {
                    $field->setOption($side['id'], $side['name']);
                });
                return $field;
            }),
            new LineField(),
            NumberField::make('default_weight', 'Default weight for one item')->setUnit('weight'),
            NumberField::make('default_width', 'Default width for one product (mm)')->setUnit('mm'),
            NumberField::make('default_length', 'Default length for one product (mm)')->setUnit('mm'),
            NumberField::make('default_height', 'Default height for one product (mm)')->setUnit('mm'),
            with(SelectField::make('order_content', 'Choose a content description'), fn(SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField => $field->setOption('name', 'Product name')
                ->setOption('sku', 'Product SKU')
                ->setOption('barcode', 'Product barcode')),
        ]);
    }

    /**
     * @return BoxSettings
     */
    protected function boxTwo(): ?BoxSettings
    {
        return BoxSettings::make('parcel_and_waybill_settings')->setFields([
            with(RadioField::make('side', 'Who pay the shipping cost'), function (RadioField $field): \Modules\Core\Helpers\VueJs\Fields\RadioField {
                $this->getWaybillSidesLabels()->map(function (array $side) use ($field): void {
                    $field->setOption($side['id'], $side['name']);
                });
                return $field;
            }),
            SwitchField::make('cd', 'Enable cash on delivery'),
            with(SelectField::make('cod_pay', 'Payment of cash on delivery')->setDepend('cd', [1]), fn(SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField => $field->setOption('cash', 'Cash')
                ->setOption('client_number', 'By client number')),
            StringField::make('client_number', 'Client number')->setDepend('cod_pay', ['client_number'])->setDepend('cd', [1]),
            SwitchField::make('insurance', 'Insurance'),
            with(SelectField::make('insurance_document', 'Select a document that declares the value shipment value.')->setDepend('insurance', [1]), function (SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField {
                if (setting('invoicing')) {
                    $field->setOption('invoice', 'Invoice');
                }

                $field->setOption('receipt', 'Receipt');
                return $field;
            }),
            SwitchField::make('money_transfer', 'Money transfer'),
            SwitchField::make('verification', 'Verification with ID card'),
            SwitchField::make('allowShipmentCheck', 'View'),
            SwitchField::make('notification', 'Notify the recipient via SMS / VIBER'),
            SwitchField::make('breakable', 'Fragile shipment'),
            SwitchField::make('returnReceipt', 'Back documents'),
            SwitchField::make('accompanyingDocuments', 'Accompanying documents'),
        ]);
    }
}
