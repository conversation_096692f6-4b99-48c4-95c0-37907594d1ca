<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 13.11.2018 г.
 * Time: 09:56 ч.
 */

namespace Modules\Apps\Exports\XmlFeed\Templates\Csv;

use App\Helper\YesNo;
use App\Models\Product\Product;
use App\Models\Product\Variant;

class Retargeting extends AbstractCsvTemplate
{
    #[\Override]
    protected function header(): array
    {
        return [
            'product id', 'product name', 'product url', 'image url', 'stock', 'price', 'sale price', 'brand', 'category', 'extra data'
        ];
    }

    /**
     * @param \App\Models\Product\Product $product
     * @return mixed
     */
    public function product(Product $product): array
    {
        $variants = [];
        $hide_discount_price = (($product->discount->hide_discount_price ?? 0) || ($product->discount->msrp_price ?? 0));
        foreach ($product->variants()->get() as $variant) {
            $variant = $variant->formatDiscountsVariants($product);
            $variants[] = [
                'code' => !empty($variant->sku) ? $variant->sku : $variant->id,
                'price' => (static function (Variant $variant) use ($hide_discount_price): string {
                    if ($hide_discount_price && !empty($variant->getAttribute('price_discounted'))) {
                        return number_format($variant->getAttribute('price_discounted') / 100, 2, '.', '');
                    }

                    return number_format($variant->price / 100, 2, '.', '');
                })($variant),
                'sale_price' => (static fn(Variant $variant): string => !empty($variant->getAttribute('price_discounted')) ? number_format($variant->getAttribute('price_discounted') / 100, 2, '.', '') : number_format($variant->price / 100, 2, '.', ''))($variant),
                'stock' => !is_null($variant->quantity) ? (int)$variant->quantity : 10,
                'margin' => null,
                'in_supplier_stock' => true
            ];
        }

        if (!empty($product->category->path)) {
            $categories = [];
            foreach ($product->category->path->pluck('id', 'name') as $name => $id) {
                $categories[$id] = $name;
            }
        }

        $get_stock = array_map(fn(array $item): int => $item['stock'], $variants);

        $price = (static function (Product $product) use ($hide_discount_price): string {
            if ($hide_discount_price && !empty($product->getAttribute('price_from_discounted'))) {
                return number_format($product->getAttribute('price_from_discounted_input'), 2, '.', '');
            }

            return number_format($product->price_from_input, 2, '.', '');
        })($product);
        $sale_price =  (static fn(Product $product): string => !empty($product->getAttribute('price_from_discounted')) ? number_format($product->getAttribute('price_from_discounted_input'), 2, '.', '') : number_format($product->price_from_input, 2, '.', ''))($product);
        if ($sale_price > $price) {
            $sale_price = $price;
        }

        return [
            'product id' => $this->addVersionToModelId($product),
            'product name' => $product->name,
            'product url' => $this->feed->manager->urlAppend($product->url),
            'image url' => $product->getImage(),
            'stock' => $product->continue_selling == YesNo::True ? 999 : (int)max($get_stock),
            'price' =>  $price,
            'sale price' => $sale_price,
            'brand' => $product->vendor ? $product->vendor->name : '',
            'category' => !empty($product->category->path) ? $product->category->path->implode('name', ' > ') : null,
            'extra data' => json_encode([
                'acq_price' => null,
                'categories' => !empty($categories) ? $categories : null,
                'variations' => $variants ?: null
            ])
        ];
    }

    /**
     * @param \App\Models\Product\Product $product
     * @return mixed
     */
    public function productToArray(Product $product): array
    {
        $variants = [];
        $hide_discount_price = (($product->discount->hide_discount_price ?? 0) || ($product->discount->msrp_price ?? 0));
        foreach ($product->variants()->get() as $variant) {
            $variant = $variant->formatDiscountsVariants($product);
            $variants[] = [
                'code' => !empty($variant->sku) ? $variant->sku : $variant->id,
                'price' => (static function (Variant $variant) use ($hide_discount_price): string {
                    if ($hide_discount_price && !empty($variant->getAttribute('price_discounted'))) {
                        return number_format($variant->getAttribute('price_discounted') / 100, 2, '.', '');
                    }

                    return number_format($variant->price / 100, 2, '.', '');
                })($variant),
                'sale_price' => (static fn(Variant $variant): string => !empty($variant->getAttribute('price_discounted')) ? number_format($variant->getAttribute('price_discounted') / 100, 2, '.', '') : number_format($variant->price / 100, 2, '.', ''))($variant),
                'stock' => !is_null($variant->quantity) ? (int)$variant->quantity : 10,
                'margin' => null,
                'in_supplier_stock' => true
            ];
        }

        if (!empty($product->category->path)) {
            $categories = [];
            foreach ($product->category->path->pluck('id', 'name') as $name => $id) {
                $categories[$id] = $name;
            }
        }

        $get_stock = array_map(fn(array $item): int => $item['stock'], $variants);

        $price = (static function (Product $product) use ($hide_discount_price): string {
            if ($hide_discount_price && !empty($product->getAttribute('price_from_discounted'))) {
                return number_format($product->getAttribute('price_from_discounted_input'), 2, '.', '');
            }

            return number_format(floatval($product->price_from_input), 2, '.', '');
        })($product);
        $sale_price =  (static fn(Product $product): string => !empty($product->getAttribute('price_from_discounted')) ? number_format(floatval($product->getAttribute('price_from_discounted_input')), 2, '.', '') : number_format(floatval($product->price_from_input), 2, '.', ''))($product);
        if ($sale_price > $price) {
            $sale_price = $price;
        }

        return [
            'product id' => $this->addVersionToModelId($product),
            'product name' => $product->name,
            'product url' => $this->feed->manager->urlAppend($product->url),
            'image url' => $product->getImage(),
            'stock' => $product->continue_selling == YesNo::True ? 999 : (int)max($get_stock),
            'price' =>  $price,
            'sale price' => $sale_price,
            'brand' => $product->vendor ? $product->vendor->name : '',
            'category' => !empty($product->category->path) ? $product->category->path->implode('name', ' > ') : null,
            'extra data' => json_encode([
                'acq_price' => null,
                'categories' => !empty($categories) ? $categories : null,
                'variations' => $variants ?: null
            ])
        ];
    }

    /**
     * @param array $product
     * @return mixed
     */
    protected function extractProductNameFromProduct(array $product): ?string
    {
        return $product['product name'] ?? null;
    }
}
