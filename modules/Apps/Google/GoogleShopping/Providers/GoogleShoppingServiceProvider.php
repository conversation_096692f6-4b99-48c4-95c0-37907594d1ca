<?php

declare(strict_types=1);

namespace Modules\Apps\Google\GoogleShopping\Providers;

use App\Providers\RouteServiceProvider;
use Exception;
use Illuminate\Routing\Router;
use Modules\Apps\Google\GoogleShopping\GoogleShoppingManager;

use Illuminate\Support\ServiceProvider;

class GoogleShoppingServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'Modules\Apps\Google\GoogleShopping\Http\Controllers';

    /**
     * @return void
     */
    #[\Override]
    public function register(): void
    {
        $this->app->singleton(GoogleShoppingManager::APP_KEY, fn(): \Modules\Apps\Google\GoogleShopping\GoogleShoppingManager => new GoogleShoppingManager());
        $this->mergeConfigFrom(__DIR__ . '/../config/config.php', 'google_shopping');
        $this->mergeConfigFrom(__DIR__ . '/../config/queue.php', 'queue.mapping');
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     * @throws Exception
     */
    public function boot(): void
    {
        $this->registerRoutes();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    protected function registerRoutes()
    {
        $this->app->make('router')->group(['namespace' => $this->namespace], function (Router $route): void {
            if (app_namespace() == 'sitecp') {
                $route->group(['middleware' => RouteServiceProvider::siteCpMiddleware()], function (): void {
                    $this->loadRoutesFrom(__DIR__ . '/../routes/sitecp.php');
                });
            }
        });
    }
}
