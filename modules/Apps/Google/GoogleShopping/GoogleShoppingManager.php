<?php

declare(strict_types=1);

namespace Modules\Apps\Google\GoogleShopping;

use App\Exceptions\Error;
use App\Helper\Plan;
use App\Helper\YesNo;
use App\Models\Gate\PlanFeature;
use App\Models\Product\Product;
use Google\Service\ShoppingContent\ProductsCustomBatchRequestEntry;
use Google\Client as GoogleClient;
use Illuminate\Support\Facades\Bus;
use Modules\Apps\Abstractions\Managers\AbstractImportsExportsManager;
use Modules\Apps\Google\GoogleShopping\Models\UploadedProducts;
use Google\Service\ShoppingContent\ProductsCustomBatchRequest;
use Google\Service\ShoppingContent\ProductsCustomBatchResponse;

/**
 * Class GoogleSheetsManager
 * @package Modules\Apps\Google\GoogleSheets
 */
class GoogleShoppingManager extends AbstractImportsExportsManager
{
    public const string APP_KEY = 'google_shopping';

    public const int LIMIT = 500;

    /**
     * {@inheritdoc}
     */
    #[\Override]
    public function uninstall(): void
    {
        $this->disconnectGoogleAccount();
    }

    #[\Override]
    public function install(): void
    {
        $getRobot = setting('robots.txt');
        $getRobot .= "\nUser-agent: Googlebot\nDisallow:\n";
        $getRobot .= "User-agent: Googlebot-image\nDisallow:\n";
        $setting = setting();
        $setting->set('robots.txt', $getRobot);
        $setting->set('update_robots', \Carbon\Carbon::now()->timestamp);
        $setting->save();
    }

    /**
     * @param mixed $is_install
     * @return mixed
     */
    public function getMigrationsPath($is_install): ?string
    {
        return 'modules/Apps/Google/GoogleShopping/migrations';
    }

    /**
     * Load Google Client and set access token
     * @return GoogleClient
     * @throws Error
     */
    public function loadGoogleClient()
    {
        $client = new GoogleClient();
        $oauth = $this->getLiveSetting('oauth', []);
        if (is_null(\Illuminate\Support\Arr::get($oauth, 'access_token'))) {
            return $client;
        }

        $client->getCache()->clear();
        $client->setAccessToken([
            'access_token' => \Illuminate\Support\Arr::get($oauth, 'access_token'),
            'expires_in' => \Illuminate\Support\Arr::get($oauth, 'expires_in'),
            'created' => \Illuminate\Support\Arr::get($oauth, 'created'),
        ]);

        if ($client->isAccessTokenExpired()) {
            $client->setClientId(config('google_shopping.client_id'));
            $client->setClientSecret(config('google_shopping.client_secret'));
            $client->setRedirectUri(config('google.redirect_uri'));
            $client->addScope([
                \Google_Service_Oauth2::OPENID,
                \Google_Service_Oauth2::USERINFO_EMAIL,
                \Google_Service_Oauth2::USERINFO_PROFILE,
                \Google_Service_ShoppingContent::CONTENT,
            ]);
            $newToken = $client->fetchAccessTokenWithRefreshToken(\Illuminate\Support\Arr::get($oauth, 'refresh_token'));
            if (!empty($newToken['access_token'])) {
                $this->updateSetting('oauth', $newToken);
                $client->setAccessToken([
                    'access_token' => $newToken['access_token'],
                    'expires_in' => $newToken['expires_in'],
                    'created' => $newToken['created'],
                ]);
            }
        }

        return $client;
    }

    /**
     * @param $e
     * @return string|null
     */
    public function getAuthErrorMessage($e): ?string
    {
        $errorResponse = json_decode((string) $e->getMessage());

        if ($errorResponse && $errorResponse->error == 'invalid_grant') {
            if (!empty($errorResponse->error_descripton)) {
                return $errorResponse->error_descripton . ' Please, reconnect your google account.';
            } else {
                return 'Please, reconnect your google account.';
            }
        }

        if ($errorResponse && $errorResponse->error->code == 401) {
            return 'Invalid Credentials. Please, reconnect your google account.';
        }

        if ($e->getMessage() == 'Connect your Google account first!') {
            return $e->getMessage();
        }

        return null;
    }

    /**
     * @throws Error
     */
    public function disconnectGoogleAccount(): void
    {
        try {
            $client = $this->loadGoogleClient();
            $client->revokeToken();
            $this->removeSettings(['oauth', 'oauth_user', 'merchant_id', 'html_tag', 'is_verified']);
        } catch (\Exception) {
            //
        }

        $this->getApp()->settings()->delete();
    }

    /**
     * @return string
     */
    public function verifySite()
    {
        if ($this->getSetting('is_verified', 0) == 0 && $this->getSetting('html_tag')) {
            return $this->getSetting('html_tag');
        }
    }

    /**
     * @return array[]
     */
    #[\Override]
    public function getApplicationPlanFeatures(): array
    {
        $featuresArray = ['google_shopping_update_products'];
        $features = parent::getApplicationPlanFeatures();
        foreach ($featuresArray as $feature) {
            if ($planFeature = PlanFeature::findByMap($feature)) {
                $features[] = [
                    'group' => 'application',
                    'id' => $planFeature->id,
                    'mapping' => $planFeature->mapping,
                    'name' => $planFeature->name_translated,
                    'current' => Plan::enabled($feature),
                    'used' => null,
                    'suffix' => null,
                    'type' => [],
                    'cast' => $planFeature->cast,
                ];
            }
        }

        return $features;
    }


    /**
     * Send batch request to Google Shopping
     * @param array $items
     * @param mixed $type
     * @return void
     * @throws Error
     */
    public function uploadBatch($items, $type = 'insert'): void
    {
        try {
            $entries = [];
            $variantProdocutsId = [];
            $merchnatId = $this->getSetting('merchant_id');
            foreach ($items as $key => $item) {
                if ($type == 'delete') {
                    $entries[] = new ProductsCustomBatchRequestEntry([
                        'batchId' => $item,
                        'merchantId' => $merchnatId,
                        'method' => 'delete',
                        'productId' => $key,
                    ]);
                } else {
                    $variantProdocutsId[$item->getOfferId()] = $item->getItemGroupId();
                    $bulkEntry = [
                        'batchId' => $item->getOfferId(),
                        'merchantId' => $merchnatId,
                        'method' => $type,
                        'product' => $item,
                    ];
                    if ($type == 'update') {
                        $bulkEntry['productId'] = $item->getId();
                        $item->setId(null);
                        $item->setOfferId(null);
                    }

                    $entries[] = new ProductsCustomBatchRequestEntry($bulkEntry);
                }
            }

            $service = new \Google\Service\ShoppingContent($this->loadGoogleClient());
            $batchRequest = new ProductsCustomBatchRequest(['entries' => $entries]);
            $response = $service->products->custombatch($batchRequest);
            if ($variantProdocutsId) {
                if ($response instanceof ProductsCustomBatchResponse) {
                    $entries = $response->getEntries();
                    if (count($entries)) {
                        array_map(function ($batchItem) use ($variantProdocutsId, $type): void {
                            $errors = [];
                            if ($batchItem->getErrors() && count($batchItem->getErrors())) {
                                foreach ($batchItem->getErrors() as $err) {
                                    $errors[] = $err->getMessage();
                                }
                            }

                            $arrUpdate = [
                                'product_id' => $variantProdocutsId[$batchItem->getBatchId()],
                                'variant_id' => $batchItem->getBatchId(),
                                'error_message' => $errors ? json_encode($errors) : null,
                                'updated_at' => now(),
                                'google_status' => $errors !== [] ? 'Error' : "Under review",
                            ];
                            if ($type == 'insert') {
                                $arrUpdate['uploaded_at'] = now();
                                $arrUpdate['destinations'] = $this->getSetting('include_destination', []);
                            }

                            if ($batchItem->getProduct()) {
                                $arrUpdate['item_id'] = $batchItem->getProduct()->getId();
                            }

                            UploadedProducts::updateOrCreate(
                                [
                                    'variant_id' => $batchItem->getBatchId(),
                                ],
                                $arrUpdate
                            );
                        }, $entries);
                    }
                }
            }
        } catch (\Google\Service\Exception $exception) {
            $decode = json_decode($exception->getMessage(), true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $getError = \Illuminate\Support\Arr::get($decode, 'error.details.0.fieldViolations', []);
                foreach ($getError as $err) {
                    if (preg_match('/entries\[(\d+)\](\.product\.(.*))?$/', (string) $err['field'], $matches)) {
                        $index = $matches[1];
                        $batchItem = \Illuminate\Support\Arr::get($items, $index);
                        UploadedProducts::updateOrCreate(
                            [
                                'variant_id' => $batchItem->getOfferId(),
                            ],
                            [
                                'item_id' => $batchItem->getId(),
                                'variant_id' => $batchItem->getOfferId(),
                                'error_message' => $err['description'] ? json_encode([$err['description']]) : null,
                                'uploaded_at' => now(),
                                'updated_at' => now(),
                                'google_status' => 'Product error',
                            ]
                        );
                    }
                }
            }
        }
    }

    /**
     * @return array
     * @throws Error
     */
    protected function getProductsFilter(): array
    {
        $filterGroup = $this->getLiveSetting('filter_group');
        if (!in_array($filterGroup, ['category', 'vendor', 'product', 'collection'])) {
            return [];
        }

        return array_filter([
            $filterGroup => (function (): array {
                $values = $this->getLiveSetting('filter_group_value', []);
                if (!is_array($values)) {
                    $values = explode(',', $values);
                }

                return array_unique(array_filter($values));
            })()
        ]);
    }

    /**
     * @param $productId
     * @param array $with
     * @param array $filter
     * @return Product|\Illuminate\Database\Eloquent\Builder
     */
    protected function getProductsQuery($productId = null, array $with = [], array $filter = [])
    {
        if ($with) {
            $query = Product::with($with);
        } else {
            $query = Product::query();
        }

        $query->orderBy('products.id');
        if (!is_null($productId)) {
            $query->limit(self::LIMIT);
        }

        if (!is_null($productId)) {
            $query->where('id', '>', $productId);
        }

        $values = \Illuminate\Support\Arr::first($filter) ?: [];
        match (key($filter)) {
            'category' => $query->where('draft', YesNo::False)
                ->whereIn('category_id', $values),
            'vendor' => $query->where('draft', YesNo::False)
                ->whereIn('vendor_id', $values),
            'product' => $query->where('draft', YesNo::False)
                ->whereIn('products.id', $values),
            'collection' => $query->where('draft', YesNo::False)
                ->whereHas('smart_collections', function ($query) use ($values): void {
                    $query->whereIn('selection_id', $values);
                }),
            default => $query->where('draft', YesNo::False),
        };

        return $query;
    }

    /**
     * @return Product|\Illuminate\Database\Eloquent\Builder
     * @throws Error
     */
    public function getProductsFromFilter()
    {
        $query = $this->getProductsQuery(null, [], $filter = $this->getProductsFilter());
        return $query->select('products.*')
            ->leftJoin('@google_shopping_products', 'products.id', '=', '@google_shopping_products.product_id')
            ->whereNull('@google_shopping_products.product_id');
    }

    /*
   * Get status
   * @return array
   */
    public function getStatus(): array
    {
        $batch = null;
        if ($this->getSetting('batch_id')) {
            $batch = Bus::findBatch($this->getSetting('batch_id'));
        }

        return [
            'is_active' => $this->getSetting('start_export') && !$this->getSetting('export_complete'),
            'progress' => $batch ? $batch->progress() : 0,
            'total_products' => $this->getSetting('total_products'),
            'started_at' => $this->getSetting('start_export'),
            'completed_at' => $this->getSetting('export_complete'),
        ];
    }


}
