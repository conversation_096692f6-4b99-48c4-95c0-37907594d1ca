<?php

declare(strict_types=1);

namespace Modules\Apps\Google\GoogleShopping\Event;

use App\Events\Models\ProductCreated;
use App\Events\Models\ProductDeleted;
use App\Events\Models\ProductUpdated;
use App\Helper\Plan;
use App\Models\Product\Variant;
use Google\Service\ShoppingContent;
use Illuminate\Events\Dispatcher;
use Illuminate\Support\Arr;
use Modules\Apps\Google\GoogleShopping\Formatters\Product;
use Modules\Apps\Google\GoogleShopping\GoogleShoppingManager;
use Modules\Apps\Google\GoogleShopping\Models\UploadedProducts;

/**
 * Class OrderEventSubscriber
 *
 * @package App\Listeners
 */
class EventSubscriber
{
    /**
     * @param $event
     * @return null
     * @throws \App\Exceptions\Error
     */
    public function onProductUpdated($event): void
    {
        $manager = new GoogleShoppingManager();
        if (!$manager->isInstalled()) {
            return;
        }

        if ($event instanceof ProductDeleted) {
            try {
                $productID = $event->product->id ?? (int)$event->product;
                $getProduct = UploadedProducts::where('product_id', $productID)->pluck('variant_id', 'item_id');
                if ($getProduct) {
                    $manager->uploadBatch($getProduct, 'delete');
                    UploadedProducts::where('product_id', $productID)->delete();
                }
            } catch (\Exception) {
            }
        }

        if ($event instanceof ProductUpdated) {
            try {
                $productId = $event->product->id ?? (int)$event->product;
                if ($manager->getSetting('update_products') == 1 && Plan::enabled('google_shopping_update_products')) {
                    $Product = UploadedProducts::where('product_id', $productId)->first();
                    if ($Product) {
                        $productFormatter = new Product();
                        $getProduct = \App\Models\Product\Product::find($productId);
                        $items = $productFormatter->fromArray([$getProduct], true);
                        if (count($items) > 1) {
                            $manager->uploadBatch($items, 'update');
                        } else {
                            $service = new \Google\Service\ShoppingContent($manager->loadGoogleClient());
                            $item = \Illuminate\Support\Arr::first($items);
                            $product = $service->products->update($manager->getSetting('merchant_id'), $Product->item_id, $item);
                            UploadedProducts::updateOrCreate(
                                [
                                    'variant_id' => $product->getOfferId(),
                                ],
                                [
                                    'product_id' => $productId,
                                    'variant_id' => $product->getOfferId(),
                                    'error_message' => null,
                                    'updated_at' => now(),
                                    'google_status' => 'Processing',
                                    'item_id' => $product->getId(),
                                ]
                            );
                        }
                    }

                }
            } catch (\Exception) {
            }
        }

        if ($event instanceof ProductCreated) {
            try {
                $getResults = $manager->getProductsFromFilter();
                if ($getResults->where('id', $event->product->id)->count() > 0) {
                    $productFormatter = new Product();
                    $items = $productFormatter->fromArray([$event->product]);
                    if (count($items) > 1) {
                        $manager->uploadBatch($items);
                    } else {
                        $service = new \Google\Service\ShoppingContent($manager->loadGoogleClient());
                        $item = \Illuminate\Support\Arr::first($items);
                        $product = $service->products->insert($manager->getSetting('merchant_id'), $item);
                        UploadedProducts::updateOrCreate(
                            [
                                'variant_id' => $product->getOfferId(),
                            ],
                            [
                                'product_id' => $event->product->id,
                                'variant_id' => $product->getOfferId(),
                                'error_message' => null,
                                'uploaded_at' => now(),
                                'updated_at' => now(),
                                'google_status' => 'Processing',
                                'item_id' => $product->getId(),
                            ]
                        );
                    }
                }
            } catch (\Exception) {
            }
        }

        if ($event instanceof Variant) {
            if ($manager->getSetting('update_products') == 1 && Plan::enabled('google_shopping_update_products')) {
                $getProduct = UploadedProducts::where('variant_id', $event->id)->first();

                if (!is_null($getProduct)) {
                    $service = new ShoppingContent($manager->loadGoogleClient());

                    $items = (new Product())->get($event->product, $event, true);
                    $item = \Illuminate\Support\Arr::first($items);
                    foreach (['id', 'offerId', 'targetCountry', 'contentLanguage', 'channel'] as $key) {
                        $item->$key = null;
                    }

                    try {
                        $service->products->update($manager->getSetting('merchant_id'), $getProduct->item_id, $item);
                        $getProduct->updated_at = now();
                        $getProduct->error_message = null;
                        $getProduct->save();
                    } catch (\Exception $e) {
                        $decode = json_decode($e->getMessage(), true);
                        if (json_last_error() === JSON_ERROR_NONE) {
                            $getMessages = Arr::get($decode, 'error.message', []);
                            $getProduct->error_message = $getMessages ? json_encode([$getMessages]) : null;
                            $getProduct->updated_at = now();
                            $getProduct->save();
                        }
                    }
                }
            }
        }

        return;
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param Dispatcher $events
     */
    public function subscribe($events): void
    {
        $events->listen(
            [
                \App\Events\Models\ProductDeleted::class,
                \App\Events\Models\ProductUpdated::class,
                \App\Events\Models\ProductCreated::class,
                "eloquent.updating: " . Variant::class,
            ],
            $this->onProductUpdated(...)
        );
    }
}
