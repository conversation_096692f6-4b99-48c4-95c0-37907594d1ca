<?php

declare(strict_types=1);

namespace Modules\Apps\Google\GoogleShopping\Helpers;

class GoogleOtherAttributes
{
    public static function getOtherAttributes(): array
    {
        return [
            'energy_efficiency_class' => [
                'name' => 'Energy efficiency class',
                'values' => [
                    'A+++', 'A++', 'A+', 'A', 'B', 'C', 'D', 'E', 'F', 'G',
                ],
            ],
            'min_energy_efficiency_class' => [
                'name' => 'Minimum energy efficiency class',
                'values' => [
                    'A+++', 'A++', 'A+', 'A', 'B', 'C', 'D', 'E', 'F', 'G',
                ],
            ],
            'max_energy_efficiency' => [
                'name' => 'Maximum energy efficiency class',
                'values' => ['A+++', 'A++', 'A+', 'A', 'B', 'C', 'D', 'E', 'F', 'G'],
            ],
            'age_group' => [
                'name' => 'Age group',
                'values' => [
                    'newborn', 'infant', 'toddler', 'kids', 'adult',
                ],
            ],
            'color' => [
                'name' => 'Color',
                'values' => [],
            ],
            'gender' => [
                'name' => 'Gender',
                'values' => ['male', 'female', 'unisex']
            ],
            'material' => [
                'name' => 'Material',
                'values' => [],
            ],
            'pattern' => [
                'name' => 'Pattern',
                'values' => [],
            ],
            'size' => [
                'name' => 'Size',
                'values' => [],
            ],
            'size_type' => [
                'name' => 'Size type',
                'values' => ['regular', 'petite', 'plus', 'big and tall', 'maternity'],
            ],
            'size_system' => [
                'name' => 'Size system',
                'values' => ['US', 'UK', 'EU', 'FR', 'IT', 'JP', 'AU', 'BR', 'CN', 'MX', 'KR', 'RU'],
            ],
        ];
    }
}
