<?php

declare(strict_types=1);

namespace Modules\Apps\Google\GoogleShopping\Formatters;

use App\Helper\YesNo;
use App\Models\Product\ImageVariant;
use App\Models\Product\Product as ProductModel;
use App\Models\Product\Variant;
use App\Models\System\AppsManager;
use Google\Service\ShoppingContent\Price;
use Google\Service\ShoppingContent\Product as GoogleProduct;
use Google\Service\ShoppingContent\ProductDimension;
use Google\Service\ShoppingContent\ProductUnitPricingBaseMeasure;
use Google\Service\ShoppingContent\ProductUnitPricingMeasure;
use Google\Service\ShoppingContent\ProductWeight;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\Apps\Google\GoogleShopping\GoogleShoppingManager;
use Modules\Apps\Google\GoogleShopping\Models\ShoppingMapping;

class Product
{
    public const googleUnits = [
        'oz', 'lb', 'mg', 'g', 'kg', 'floz', 'pt', 'qt', 'gal', 'ml', 'cl', 'l', 'cbm', 'in', 'ft', 'yd', 'cm', 'm', 'sqft', 'sqm', 'ct'
    ];

    public const destinations = [
        "Free listings",
        "Shopping ads",
        "Surfaces across Google",
        "Dynamic remarketing"
    ];

    public const statuses = [
        'Processing',               // Продуктът се обработва.
        'Under Review',             // Продуктът е в процес на преглед.
        'Approved',                 // Продуктът е одобрен и активен.
        'Approved (Limited)',       // Продуктът е одобрен с ограничения.
        'Disapproved',              // Продуктът е отхвърлен.
        'Pending',                  // Продуктът очаква обработка.
        'Expired',                  // Продуктът е изтекъл.
        'Pending Review After Appeal', // Очаква повторна проверка след обжалване.
        'Not Showing'               // Продуктът не се показва.
    ];

    protected $attributeMapping = [
        'age_group' => 'setAgeGroup',
        'gender' => 'setGender',
        'size_type' => 'setSizeType',
        'size_system' => 'setSizeSystem',
    ];

    private $mapping;

    private $isUpdate;

    protected \Modules\Apps\Google\GoogleShopping\GoogleShoppingManager $manager;

    public function __construct()
    {
        $this->manager = new GoogleShoppingManager();
    }

    /**
     * Format products from array
     * @param $products
     * @param mixed $isUpdate
     * @return array
     * @throws \App\Exceptions\Error
     */
    public function fromArray($products, $isUpdate = false)
    {
        try {
            $items = [];
            foreach ($products as $product) {
                $items = array_merge($items, $this->get($product, null, $isUpdate));
            }

            return $items;
        } catch (\Exception $exception) {
            throw new \App\Exceptions\Error($exception->getMessage());
        }
    }

    /**
     * Get formatted product
     * @param ProductModel $product
     * @param Variant|null $variant
     * @param mixed $isUpdate
     * @return array
     * @throws \App\Exceptions\Error
     */
    public function get(ProductModel $product, ?Variant $variant = null, $isUpdate = false): array
    {
        $items = [];
        $this->mapping = ShoppingMapping::all();
        $this->isUpdate = $isUpdate;
        if ($variant !== null) {
            $items[] = $this->format($product, $variant);
        } else {
            if ($product->type == 'multiple') {
                foreach ($product->variants as $variant) {
                    $items[] = $this->format($product, $variant, $product->id);
                }
            } else {
                $items[] = $this->format($product, $product->variant, $product->id);
            }
        }

        return $items;
    }

    /**
     * Format product
     * @param ProductModel $product
     * @param Variant $variant
     * @param $groupId
     * @return GoogleProduct
     * @throws \App\Exceptions\Error
     */
    private function format(ProductModel $product, Variant $variant, $groupId = null): GoogleProduct
    {
        $columnsUpdate = $this->manager->getSetting('update_columns', []);
        $item = new GoogleProduct();
        if (!$this->isUpdate) {
            $item->setContentLanguage(site('language', 'en'));
            $item->setTargetCountry(site('operation_country'));
            $item->setId($product->id);
            $item->setOfferId($variant->id);
            $item->setChannel('online');
        }

        if (!$this->isUpdate || ($this->isUpdate && in_array('name', $columnsUpdate))) {
            $item->setTitle($product->name);
        }

        if (!$this->isUpdate || ($this->isUpdate && in_array('description', $columnsUpdate))) {
            $item->setDescription(strip_tags(Str::limit((string) $product->description, 5000)));
        }

        if (!$this->isUpdate) {
            $item->setLink($product->url);
        }

        $this->setExpirationDate($item, $product);
        if (!$this->isUpdate || ($this->isUpdate && in_array('price', $columnsUpdate))) {
            $this->setPrice($item, $variant);
            $this->setUnit($item, $variant);
        }

        if (!$this->isUpdate || ($this->isUpdate && in_array('promo_price', $columnsUpdate))) {
            $this->setSalePrice($item, $variant);
        }

        if (!$this->isUpdate || ($this->isUpdate && in_array('availability', $columnsUpdate))) {
            $this->setStatus($item, $product, $variant);
        }

        if (!$this->isUpdate) {
            $item->setItemGroupId($groupId);
            $this->setGoogleCategory($item, $product);
        }

        if (!$this->isUpdate || ($this->isUpdate && in_array('category', $columnsUpdate))) {
            $this->setCategory($item, $product);
        }

        if (!$this->isUpdate || ($this->isUpdate && in_array('vendor', $columnsUpdate))) {
            $this->setBrand($item, $product);
        }

        if (!$this->isUpdate || ($this->isUpdate && in_array('images', $columnsUpdate))) {
            $this->setMainImage($item, $product, $variant, $groupId);
            $this->setAdditionImages($item, $product, $variant, $groupId);
        }

        if (!$this->isUpdate) {
            $item->setCondition($this->manager->getSetting('google_default.condition', 'new'));
            $item->setAdult($this->manager->getSetting('google_default.adult', '0') == 1 ? 'yes' : 'no');
            $item->setIsBundle($product->type == 'bundle' ? 'yes' : 'no');
            $this->setDimension($item, $product);
            if ($variant->barcode) {
                $item->setGtin($variant->barcode);
            }

            $this->setAttributes($item, $product, $variant);
            $item->setCustomLabel0('CloudCart');
            if ($this->manager->getSetting('include_destination')) {
                $item->setIncludedDestinations($this->manager->getSetting('include_destination'));
            }
        }

        $item->setCustomAttributes([
            'name' => 'checkout_link_template',
            'value' => site()->getPrimaryHost()->url . '/checkout-link/' . $variant->id
        ]);


        if ($this->manager->getSetting('size_system')) {
            $item->setSizeSystem($this->manager->getSetting('size_system'));
        }

        return $item;
    }

    /*
     * Set item regular price
     * @param GoogleProduct $item
     * @param Variant $variant
     */
    /**
     * @param Google\Service\ShoppingContent\Product $item
     * @param \App\Models\Product\Variant $variant
     * @return mixed
     */
    private function setPrice(\Google\Service\ShoppingContent\Product &$item, Variant $variant): void
    {
        $item->setPrice(
            new Price(
                [
                    'currency' => site('currency'),
                    'value' => $variant->price_input
                ]
            )
        );
    }

    /*
     * Set item sale price
     * @param GoogleProduct $item
     * @param Variant $variant
     */
    /**
     * @param Google\Service\ShoppingContent\Product $item
     * @param \App\Models\Product\Variant $variant
     * @return mixed
     */
    private function setSalePrice(GoogleProduct &$item, Variant $variant): void
    {
        if ($variant->detailed_discount) {
            $item->setSalePrice(
                new Price(
                    [
                        'currency' => site('currency'),
                        'value' => $variant->detailed_discount->price_input
                    ]
                )
            );
            if ($variant->detailed_discount->start_date && $variant->detailed_discount->end_date) {
                $item->setSalePriceEffectiveDate($variant->detailed_discount->start_date . 'T00:00:00Z/' . $variant->detailed_discount->end_date . 'T23:59:59Z');
            }
        }
    }

    /*
     * Set item status
     * @param GoogleProduct $item
     * @param ProductModel $product
     * @param Variant $variant
     */
    /**
     * @param Google\Service\ShoppingContent\Product $item
     * @param \App\Models\Product\Product $product
     * @param \App\Models\Product\Variant $variant
     * @return mixed
     */
    private function setStatus(GoogleProduct &$item, ProductModel $product, Variant $variant): void
    {
        $variant->setRelation('item', clone $product);
        $getStatuses = $this->manager->getSetting('google_status', []);
        if (!empty($product->variant->status_id)) {
            $variantStatus = $product->variant->status_id;
            $isMappedStatus = Arr::where($getStatuses ?? [], fn($status, $key): bool => in_array($variantStatus, $status));
            if ($isMappedStatus) {
                $item->setAvailability(key($isMappedStatus));
            } else {
                if ($this->enableSell($product, $variant)) {
                    $item->setAvailability('in_stock');
                } else {
                    $item->setAvailability('out_of_stock');
                }
            }
        } else {
            if ($this->enableSell($product, $variant)) {
                $item->setAvailability('in_stock');
            } else {
                $item->setAvailability('out_of_stock');
            }
        }
    }

    /**
     * Check if product is available for sale
     * @param ProductModel $product
     * @param Variant $variant
     * @return bool
     */
    private function enableSell(ProductModel $product, Variant $variant)
    {
        if ($product->tracking == YesNo::True) {
            if ($product->continue_selling == YesNo::True) {
                return true;
            }

            return $variant->quantity >= $variant->minimum;
        }

        return true;
    }

    /**
     * Set item unit and weight
     * @param GoogleProduct $item
     * @param Variant $variant
     * @return void
     */
    private function setUnit(GoogleProduct &$item, Variant $variant): void
    {
        if (AppsManager::isInstalled('grocery_store')) {
            if ($variant->unit && in_array($variant->unit->short_name, self::googleUnits)) {
                $item->setUnitPricingBaseMeasure(new ProductUnitPricingBaseMeasure([
                    'unit' => $variant->unit->short_name,
                    'value' => floatval($variant->base_unit_value)
                ]));
                $item->setUnitPricingMeasure(new ProductUnitPricingMeasure([
                    'unit' => $variant->unit->short_name,
                    'value' => floatval($variant->base_unit_value)
                ]));
            }
        } else {
            $item->setUnitPricingBaseMeasure(new ProductUnitPricingBaseMeasure([
                'unit' => 'ct',
                'value' => 1
            ]));
            $item->setUnitPricingMeasure(new ProductUnitPricingMeasure([
                'unit' => 'ct',
                'value' => 1
            ]));

            $item->setProductWeight(new ProductWeight([
                'unit' => 'g',
                'value' => $variant->weight ?? $this->manager->getSetting('default_weight')
            ]));
        }
    }

    /**
     * Set category
     * @param GoogleProduct $item
     * @param ProductModel $product
     * @return void
     */
    private function setCategory(GoogleProduct &$item, ProductModel $product): void
    {
        if ($product->category) {
            $item->setProductTypes($product->category->path->implode('name', ' > '));
        }
    }

    /**
     * Set brand
     * @param GoogleProduct $item
     * @param ProductModel $product
     * @return void
     */
    private function setBrand(GoogleProduct &$item, ProductModel $product): void
    {
        if ($product->vendor) {
            $item->setBrand($product->vendor->name);
        }
    }

    /**
     * Set google category
     * @param GoogleProduct $item
     * @param ProductModel $product
     * @return void
     */
    private function setGoogleCategory(GoogleProduct &$item, ProductModel $product): void
    {
        if ($product->category && $product->category->taxonomy_id) {
            $item->setGoogleProductCategory($product->category->taxonomy_id);
            //  $item->setGoogleProductCategory($product->category->google_taxonomy->name);
        }
    }

    /**
     * Set main image
     * @param $item
     * @param ProductModel $product
     * @param Variant $variant
     * @param $groupId
     */
    private function setMainImage(GoogleProduct &$item, ProductModel $product, Variant $variant, $groupId): void
    {
        if ($variant->images->isNotEmpty() && $groupId) {
            $item->setImageLink($variant->images->sortBy(fn(ImageVariant $imageVariant) => $imageVariant->image->sort_order ?? 0)->first()->image->getImage());
        } else {
            $item->setImageLink($product->getImage());
        }
    }

    /**
     * Set additional images
     * @param GoogleProduct $item
     * @param ProductModel $product
     * @param Variant $variant
     * @param $groupId
     */
    private function setAdditionImages(GoogleProduct &$item, ProductModel $product, Variant $variant, $groupId): void
    {
        if ($variant->images->isNotEmpty() && $variant->images->count() > 1 && $groupId) {
            $item->setAdditionalImageLinks(array_slice($variant->images->map(fn(ImageVariant $imageVariant) => $imageVariant->image->getImage())->toArray(), 1));
        } else {
            $item->setAdditionalImageLinks($product->images->map(fn($image) => $image->getImage())->toArray());
        }
    }

    /**
     * Set expiration date
     * @param GoogleProduct $item
     * @param ProductModel $product
     * @return void
     */
    private function setExpirationDate(GoogleProduct &$item, ProductModel $product): void
    {
        if ($product->active_to) {
            $item->setExpirationDate($product->active_to->format('Y-m-d\TH:iO'));
        }
    }

    /**
     * Set dimension
     * @param GoogleProduct $item
     * @param ProductModel $product
     * @return void
     */
    private function setDimension(GoogleProduct &$item, ProductModel $product): void
    {
        $width = $product->meta->get('width', $this->manager->getSetting('default_width'));
        if ($width) {
            $item->setProductWidth(new ProductDimension([
                'unit' => 'cm',
                'value' => floatval($width / 10)

            ]));
        }

        $depth = $product->meta->get('depth', $this->manager->getSetting('default_depth'));
        if ($depth) {
            $item->setProductLength(new ProductDimension([
                'unit' => 'cm',
                'value' => floatval($depth / 10)
            ]));
        }

        $height = $product->meta->get('height', $this->manager->getSetting('default_height'));
        if ($height) {
            $item->setProductHeight(new ProductDimension([
                'unit' => 'cm',
                'value' => floatval($height / 10)
            ]));
        }
    }

    /**
     * Set age group
     * @param GoogleProduct $item
     * @param ProductModel $product
     * @param Variant $variant
     * @return void
     */
    private function setAttributes(GoogleProduct &$item, ProductModel $product, Variant $variant): void
    {
        $test = $this->mapping->groupBy('attribute')->map(fn($attr) => $attr->first(fn($item): bool => (
            $item->resource_type == 'category_property_option' &&
                in_array($item->resource_id, $product->propertyOptions->pluck('id')->all())
        ) || (
            $item->resource_type == 'product_parameter_option' && in_array($item->resource_id, [$variant->v1_id, $variant->v2_id, $variant->v3_id])
        ) || (
            $item->resource_type == 'product_parameter' && in_array($item->resource_id, [$product->p1_id, $product->p2_id, $product->p3_id])
        ) || (
            $item->resource_type == 'category_property' && in_array($item->resource_id, $product->propertyOptions->pluck('property_id')->all())
        )))->filter();
        $test->map(function ($item2) use ($item, $product, $variant): void {
            if (array_key_exists($item2->attribute, $this->attributeMapping)) {
                $item->{$this->attributeMapping[$item2->attribute]}($item2->attribute_value);
            } elseif (method_exists($this, $method = "set" . Str::studly($item2->attribute))) {
                $this->{$method}($item, $item2, $product, $variant);
            }
        });
    }

    /**
     * set Color
     * @param GoogleProduct $item
     * @param ShoppingMapping $filterItem
     * @param ProductModel $product
     * @param Variant $variant
     * @return void
     */
    private function setColor(GoogleProduct &$item, ShoppingMapping $filterItem, ProductModel $product, Variant $variant): void
    {
        if ($filterItem->resource_type == 'product_parameter') {
            for ($i = 1; $i <= $product->total_variants; $i++) {
                if ($product->{'p' . $i . '_id'} == $filterItem->resource_id) {
                    $item->setColor($variant->{'v' . $i});
                    break;
                }
            }
        } elseif ($filterItem->resource_type == 'category_property') {
            if ($property = $product->propertyOptions->firstWhere('property_id', $filterItem->resource_id)) {
                $item->setColor($property->value);
            }
        }
    }

    /**
     * set Material
     * @param GoogleProduct $item
     * @param ShoppingMapping $filterItem
     * @param ProductModel $product
     * @param Variant $variant
     * @return void
     */
    private function setMaterial(GoogleProduct &$item, ShoppingMapping $filterItem, ProductModel $product, Variant $variant): void
    {
        if ($filterItem->resource_type == 'product_parameter') {
            for ($i = 1; $i <= $product->total_variants; $i++) {
                if ($product->{'p' . $i . '_id'} == $filterItem->resource_id) {
                    $item->setMaterial($variant->{'v' . $i});
                    break;
                }
            }
        } elseif ($filterItem->resource_type == 'category_property') {
            if ($property = $product->propertyOptions->firstWhere('property_id', $filterItem->resource_id)) {
                $item->setMaterial($property->value);
            }
        }
    }

    /**
     * set Pattern
     * @param GoogleProduct $item
     * @param ShoppingMapping $filterItem
     * @param ProductModel $product
     * @param Variant $variant
     * @return void
     */
    private function setPattern(GoogleProduct &$item, ShoppingMapping $filterItem, ProductModel $product, Variant $variant): void
    {
        if ($filterItem->resource_type == 'product_parameter') {
            for ($i = 1; $i <= $product->total_variants; $i++) {
                if ($product->{'p' . $i . '_id'} == $filterItem->resource_id) {
                    $item->setPattern($variant->{'v' . $i});
                    break;
                }
            }
        } elseif ($filterItem->resource_type == 'category_property') {
            if ($property = $product->propertyOptions->firstWhere('property_id', $filterItem->resource_id)) {
                $item->setPattern($property->value);
            }
        }
    }

    /**
     * set Size
     * @param GoogleProduct $item
     * @param ShoppingMapping $filterItem
     * @param ProductModel $product
     * @param Variant $variant
     * @return void
     */
    private function setSize(GoogleProduct &$item, ShoppingMapping $filterItem, ProductModel $product, Variant $variant): void
    {
        if ($filterItem->resource_type == 'product_parameter') {
            for ($i = 1; $i <= $product->total_variants; $i++) {
                if ($product->{'p' . $i . '_id'} == $filterItem->resource_id) {
                    $item->setSizes($variant->{'v' . $i});
                    break;
                }
            }
        } elseif ($filterItem->resource_type == 'category_property') {
            if ($property = $product->propertyOptions->firstWhere('property_id', $filterItem->resource_id)) {
                $item->setSizes($property->value);
            }
        }
    }

}
