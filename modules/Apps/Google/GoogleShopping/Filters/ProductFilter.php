<?php

declare(strict_types=1);

namespace Modules\Apps\Google\GoogleShopping\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Modules\Apps\Google\GoogleShopping\Formatters\Product;
use Modules\Core\Core\Helpers\AbstractGridFilter;
use Modules\Core\Core\Helpers\GridFilterRules;
use Modules\Core\Core\Traits\GridFilter\QueryFilter;

/**
 * VendorFilter
 *
 * @package \Modules\Core\Core\Http\Filters
 */
class ProductFilter extends AbstractGridFilter
{
    use QueryFilter;

    /**
     * @return array
     */
    public function getSavedSearchQuery(): array
    {
        return [
            'module' => 'google_shopping',
            'filter' => null
        ];
    }

    protected $queryFilterColumns = [
        'variant.product:name',
        'variant:sku',
        'variant:barcode'
    ];

    /**
     * @inheritdoc
     */
    #[\Override]
    protected function validateRequestRules(Request $request): \Modules\Core\Core\Helpers\GridFilterRules
    {
        return GridFilterRules::make([
            'google_status' => 'string|in:'.implode(',', Product::statuses),
            ''
        ])->setOrder('name,google_status,uploaded_at,updated_at');
    }

    /**
     * @param $operator
     * @param string $value
     * @return void
     */
    public function filterGoogleStatus($operator, string $value): void
    {
        $this->setWhere('google_status', function (Builder $query) use ($value): void {
            $query->where('google_status', $value);
        });
        $this->setFilters('google_status', $value);
    }
}
