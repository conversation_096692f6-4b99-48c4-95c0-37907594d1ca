<?php

declare(strict_types=1);

namespace Modules\Apps\Google\GoogleShopping\Models;

class GoogleShoppingParameters extends \Eloquent
{
    protected $table = 'google_shopping';

    protected $connection = 'apps';

    protected $fillable = ['id', 'attribute', 'attribute_name', 'values'];

    protected $data = [];

    public $timestamps = false;

    protected function casts(): array
    {
        return [
            'values' => 'array',
        ];
    }

}
