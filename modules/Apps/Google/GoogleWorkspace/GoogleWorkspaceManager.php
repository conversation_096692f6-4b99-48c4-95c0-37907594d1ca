<?php

declare(strict_types=1);

namespace Modules\Apps\Google\GoogleWorkspace;

use Modules\Apps\Abstractions\Managers\AbstractAppManager;

class GoogleWorkspaceManager extends AbstractAppManager
{
    public const APP_KEY = 'google_workspace';

    /**
     * @return array
     */
    public function appInfo(): array
    {
        return [static::APP_KEY => [
            'direct' => 'apps/google_workspace',
            'uninstall' => 'apps/google_workspace/uninstall',
            'icon' => 'google_workspace.png',
            'name' => __('google_workspace::app.info.title'),
            'description' => __('google_workspace::app.help.install'),
        ]];
    }

}
