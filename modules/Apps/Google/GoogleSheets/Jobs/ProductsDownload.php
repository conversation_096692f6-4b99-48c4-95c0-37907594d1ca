<?php

declare(strict_types=1);

namespace Modules\Apps\Google\GoogleSheets\Jobs;

use App\Jobs\Job;
use App\Models\Queue\SiteQueue;
use App\Models\Router\Logs;
use App\Models\System\AppsManager;
use Modules\Apps\Google\GoogleSheets\GoogleSheetsManager;
use Modules\Apps\Google\GoogleSheets\Helpers\ProductsFormatter;
use Modules\Apps\Google\GoogleSheets\Models\SheetsJob;
use Modules\Apps\Google\GoogleSheets\Models\SheetsProduct;
use Revolution\Google\Sheets\Facades\Sheets;

class ProductsDownload extends Job
{
    public const LIMIT = 250;

    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'import';

    /**
     * ProductsDownload constructor.
     * @param $jobId
     * @param null $siteId
     */
    public function __construct(protected $jobId, $siteId = null)
    {
        $this->site_id = $siteId ?: site('site_id');
    }

    /**
     * {@inheritdoc}
     */
    public function execute()
    {
        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return [static::SITE_MAINTENANCE];
        }

        //migrate job from one to other platform
        if (($platform = platform()) != ($sitePlatform = sitePlatform())) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', $platform, $sitePlatform));
            return [static::WRONG_PLATFORM];
        }

        $this->info("Starting Google Sheets Products Download");

        $job = SheetsJob::find($this->jobId);
        if (!$job) {
            SheetsProduct::where('site_id', $this->site_id)->delete();
            return static::EXECUTE_DESTROY;
        }

        /** @var GoogleSheetsManager $app */
        $app = AppsManager::getManager(GoogleSheetsManager::APP_KEY);

        try {
            $job->update([
                'started_at' => now(),
                'status' => SheetsJob::STATUS_RUNNING,
                'status_message' => 'Parsing Worksheet: ' . $app->getLiveSetting('worksheet_name'),
            ]);

            $sheets = $app->loadSheets();
            //        echo var_export($sheets->first(), true);

            $rows = $sheets->get();
            $header = $rows->pull(0);
            $values = Sheets::collection($header, $rows);
            unset($rows); // free some memory
            $values = $values->map(function ($row) {
                if (empty($row['Product ID'])) {
                    $row['Product ID'] = 'new-' . uniqid();
                }

                return $row;
            });
            $products = $values->groupBy('Product ID');
            unset($values); // free some memory

            $total = $products->count();
            $chunked = $products->chunk(self::LIMIT);
            unset($products); // free some memory

            SheetsProduct::where('site_id', $this->site_id)->delete();

            $chunked->each(function ($collection): void {
                $ids = ProductsFormatter::toSheetsProducts($collection);
                SiteQueue::executeQueueTask('sheets_import', [
                    'jobId' => $this->jobId,
                    'productIds' => $ids,
                ]);
                return;
                $downloaded += $collection->count();
                $updated += ProductsFormatter::sync($collection, $discount_id);
                $message = 'Downloaded products: ' . $downloaded . " of " . $total;
                $this->info($message);

                $job->update([
                    'status_message' => $message,
                ]);
            });

            $job->update([
                'status_message' => 'Total products to download: ' . $total,
                'total_count' => $total,
                'completed_count' => 0,
            ]);

            $this->info("Done");
        } catch (\Exception $exception) {
            $message = $app->getAuthErrorMessage($exception);
            if ($message) {
                $app->removeSetting('oauth');
            } else {
                $message = $exception->getMessage();
            }

            $job->update([
                'finished_at' => now(),
                'status' => SheetsJob::STATUS_FAILED,
                'status_message' => $job->status_message . ': ERROR: ' . $message,
            ]);

            Logs::createFromThrowable($exception, 'GoogleSheets Exception');
        }

        return static::DESTROY;
    }
}
