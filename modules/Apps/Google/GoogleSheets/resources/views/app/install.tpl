{include file="apps/dependency/install_reload.tpl"}
{include file="apps/includes/app-breadcrumb.tpl" active=$app->name}

{capture name="app_addition"}
    <form action="{route('apps.google_sheets.install')}" method="post" id="appInstallReload" role="form"
          data-redirect="{route('apps.google_sheets')}">
        <p>{$app->description}</p>
        <button class="btn btn-primary" type="submit">{t}global.action.install{/t}</button>
    </form>
{/capture}

<div class="wrapper">
    <div class="app-wrapper">
        {include file="apps/includes/app-intro.tpl" title=$app->name}
        {include file="apps/includes/app-icons.tpl"  app_icon=$app->icon_filename}
        {include file="apps/includes/app-addition.tpl" addition=$smarty.capture.app_addition}
    </div>
</div>