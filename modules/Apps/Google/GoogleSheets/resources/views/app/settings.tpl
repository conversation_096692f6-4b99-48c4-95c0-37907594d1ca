{include file="apps/dependency/settings.tpl"}
<div class="page-breadcrumb clearfix">
    <ul class="breadcrumb">
        <li>
            <a href="{route('admin.apps')}">{t}sidebar.apps{/t}</a>
            <i class="fal fa-chevron-double-right fa-xs"></i>
        </li>
        <li>
            <span>{$app->name}</span>
        </li>
    </ul>
    {if !empty($oauth)}
        <div class="pull-right">
            {if $manager->getSetting('spreadsheet_url')}
                <a class="btn btn-default" href="{$manager->getSetting('spreadsheet_url')}"
                   target="_blank">
                    {t}google_sheets::app.settings.help_open{/t}
                </a>
            {/if}
            <a href="javascript:;" data-ajax="{route('apps.google_sheets.new-task', 1)}"
               class="btn btn-default js-new-job">
                {t}google_sheets::app.task.upload_to{/t}
            </a>
            <a href="javascript:;" data-ajax="{route('apps.google_sheets.new-task', 2)}"
               class="btn btn-default js-new-job">
                {t}google_sheets::app.task.download_to{/t}
            </a>
        </div>
    {/if}
</div>

<div class="wrapper">
    {include file="./tabs.tpl"}

    <div class="connect-profile">
        <div class="">
            <div class="col-md-12">
                {if $error}
                    <div class="note error">
                        <span class="note-icon"><i class="fa fa-info-circle"></i></span>
                        <span class="note-text">{$error}</span>
                    </div>
                    <br>
                {/if}
                {if $oauth|default}
                    <a href="{route('apps.google_sheets.disconnect')}"
                    <div class="connected-profile connected-profile-google">
                        <div class="connected-profile-avatar">
                            <div class="connected-profile-avatar-image"
                                 style="background-image: url({$oauth.user.avatar|default});">
                            </div>
                        </div>

                        <div class="connected-profile-name">
                            <span>{$oauth.user.name}</span>
                        </div>

                        <div class="connected-profile-email">
                            <span>{$oauth.user.email}</span>
                        </div>
                    </div>
                    </a>
                {else}
                    <div style="text-align: center">
                        <a href="{route('apps.google_sheets.connect')}"
                           class="btn btn-google-signin">
                        </a>
                    </div>
                {/if}
            </div>
        </div>
    </div>

    {if $oauth|default && $settings->get('spreadsheet_url')}
        <div class="container-small">
            <form action="{route('apps.google_sheets.settings.save')}" id="editForm" role="form">
                <div class="box">
                    <div class="box-section">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <a href="{$settings->get('spreadsheet_url')}"
                                       target="_blank">
                                        {t}google_sheets::app.settings.help_open{/t}
                                    </a>
                                </div>

                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-xs-12">
                                            <label for="spreadsheet_id"
                                                   class="control-label">{t}google_sheets::app.settings.spreadsheet_id{/t}</label>

                                            <div class="input-group">
                                                <input name="spreadsheet_id" id="spreadsheet_id" type="text"
                                                       value="{$settings->get('spreadsheet_id')}" class="form-control"
                                                       readonly
                                                       placeholder="Spreadsheet ID"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-xs-12">
                                            <label for="worksheet_name"
                                                   class="control-label">{t}google_sheets::app.settings.spreadsheet_name{/t}</label>
                                            <div class="input-group">
                                                <input name="worksheet_name" id="worksheet_name" type="text"
                                                       value="{$settings->get('worksheet_name')}" class="form-control"
                                                       readonly
                                                       placeholder="Worksheet Name"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="form-group-dynamic">
                                    <div class="row form-group">
                                        <div class="col-xs-6" id="widget-showcase-filters">
                                            <label class="control-label">{t}google_sheets::app.settings.select_discount{/t}
                                                <i class="glyphicon glyphicon-info-sign tooltips"
                                                   title="{t}google_sheets::app.discount.tooltip{/t}"
                                                   data-placement="top"></i>
                                            </label>

                                            <select name="discount_id" class="form-control select2me"
                                                    data-no-input="true">
                                                {foreach $discounts as $name => $id}
                                                    <option value="{$id}" {if $settings['discount_id'] == $id} selected {/if}>{$name}</option>
                                                {/foreach}
                                            </select>
                                        </div>
                                        <div class="col-xs-6" id="widget-showcase-filters">
                                            <label class="control-label">{t}google_sheets::app.settings.filter_group.label{/t}</label>

                                            <select name="filter_group" class="form-control select2me"
                                                    data-no-input="true"
                                                    data-placeholder="{t}widget.product.productShowcase.ph.filter{/t}">
                                                <option value="all"{if $settings['filter_group'] == 'all'} selected="selected"{/if}>{t}google_sheets::app.settings.filter_group.all{/t}</option>
                                                <option value="category"{if $settings['filter_group'] == 'category'} selected="selected"{/if}>{t}google_sheets::app.settings.filter_group.category{/t}</option>
                                                <option value="vendor"{if $settings['filter_group']== 'vendor'} selected="selected"{/if}>{t}google_sheets::app.settings.filter_group.vendor{/t}</option>
                                                <option value="product"{if $settings['filter_group'] == 'product'} selected="selected"{/if}>{t}google_sheets::app.settings.filter_group.product{/t}</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row form-group" id="filterGroupBox">
                                        <div class="col-xs-12" id="filter_group_value">
                                            <div id="filter_group_category">
                                                {include file="categories/tree_select.tpl" multiple=true select_name="filter_group_value" select_placeholder="{t}widget.product.productShowcase.label.select_categories{/t}" category_id=$settings['filter_group_value']}
                                            </div>
                                            <div id="filter_group_vendor">
                                                <input name="filter_group_value"{if $settings['filter_group'] === 'vendor'} value="{$filter_values}"{/if}
                                                       class="form-control select2_ajax" data-autowidth="false"
                                                       data-url="{route('admin.autocomplete.vendor')}"
                                                       data-multiple="true"
                                                       data-placeholder="{t}widget.product.productShowcase.label.select_vendors{/t}"/>
                                            </div>
                                            <div id="filter_group_product">
                                                <input name="filter_group_value"{if $settings['filter_group'] === 'product'} value="{$filter_values}"{/if}
                                                       class="form-control select2_ajax" data-autowidth="false"
                                                       data-url="{route('admin.autocomplete.products')}"
                                                       data-multiple="true"
                                                       data-placeholder="{t}widget.product.productShowcase.label.select_products{/t}"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group" style="padding-top:15px;">
                                    <div class="row">
                                        <div class="col-xs-12">
                                            <label for="spreadsheet_id"
                                                   class="control-label">{t}google_sheets::app.settings.select_columns{/t}</label>
                                            <select name="allowed_columns[]" class="form-control select2me"
                                                    data-no-input="true" multiple="">
                                                {foreach $allowed_columns as $column}
                                                    <option value="{$column}"
                                                            {if !empty($settings['allowed_columns']) && in_array($column, $settings['allowed_columns'])}selected{/if}
                                                            {if empty($settings['allowed_columns']) && in_array($column, $default_columns)}selected{/if}
                                                    >{t}google_sheets::app.{$column}{/t}</option>
                                                {/foreach}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group text-right">
                    <button class="btn btn-primary" type="submit" id="editSubmit">{t}global.save{/t}</button>
                </div>
            </form>
        </div>
    {/if}
</div>
<style>
    .btn-google-signin {
        border: none;
        width: 191px;
        height: 46px;
        background-image: url("/assets/google_signin_buttons/btn_google_signin_light_normal_web.png");
    }
</style>

{capture append="js"}
    <script type="text/javascript">
        $(function () {
            $filter_group_value = $('#filter_group_value');
            $(':input[name="filter_group"]').on('change', function () {
                var $el = $(this);
                $filter_group_value.removeClass('hidden');

                var $active = $('#filter_group_value #filter_group_' + $el.val()),
                    $disabled = $filter_group_value.find('*[id^="filter_group_"]').not($active),
                    $mainSection = $('#filterGroupBox');

                $active.removeClass('hidden');
                $mainSection.removeClass('hidden');
                $disabled.addClass('hidden');

                $active.find(':input').prop('disabled', false);
                $disabled.find(':input').prop('disabled', true);
            }).trigger('change');
        });
    </script>
{/capture}