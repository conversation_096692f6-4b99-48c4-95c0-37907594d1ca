<?php

declare(strict_types=1);

namespace Modules\Apps\Others\Olx\Http\Controllers\API;

class CategoryAttributes
{    /**
     * Cities constructor.
     * @param Client $client
     */
    public function __construct(private readonly Client $client)
    {
    }

    /**
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function get(string $category_id)
    {
        try {
            $response = $this->client->request('GET', 'partner/categories/'.$category_id.'/attributes');

            if ($response->data != [] || count($response->data) > 0) {
                return $response->data;
            }

        } catch (\Throwable $throwable) {
            logger()->warning($throwable->getMessage(), [
                'line' => $throwable->getLine(),
                'class' => __CLASS__,
                'method' => __METHOD__,
            ]);
        }

        return [];
    }
}
