<?php

declare(strict_types=1);

namespace Modules\Apps\Others\Olx\Http\Controllers\API;

/**
 * Class Adverts
 * @package Gentor\Olx\Api
 */
class Adverts
{
    /**
     * Adverts constructor.
     * @param Client $client
     */
    public function __construct(private readonly Client $client)
    {
    }

    /**
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function get($page = 1, $limit = 500)
    {
        $response = $this->client->user_request('GET', 'partner/adverts', [
            'page' => $page,
            'limit' => $limit
        ]);

        return !empty($response->results) ? $response->results : [];
    }

    /**
     * @param $id
     * @return \stdClass
     */
    public function find(int $id)
    {
        return $this->client->user_request('GET', 'partner/adverts/' . $id);
    }

    /**
     * @param $id
     * @return \stdClass
     */
    public function delete(int $id)
    {
        return $this->client->user_request('DELETE', 'partner/adverts/' . $id);
    }
}
