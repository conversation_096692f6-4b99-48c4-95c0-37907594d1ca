<?php

declare(strict_types=1);

namespace Modules\Apps\Others\Olx\Http\Controllers;

use App\Common\DateTimeFormat;
use App\Exceptions\Error;
use App\Helper\ArrayTree;
use App\Helper\FilterSearch\Olx\Filter;
use App\Helper\Grid;
use App\Helper\SiteCp\Search;
use App\Helper\YesNo;
use App\Models\Product\Product;
use App\Traits\IController;
use Exception;
use Gentor\Olx\Api\OlxException;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Modules\Apps\Abstractions\Controllers\AbstractAppsController;
use Modules\Apps\Others\Olx\Http\Controllers\API\Client;
use Modules\Apps\Others\Olx\Models\Advert;
use Modules\Apps\Others\Olx\Models\Category;
use Modules\Apps\Others\Olx\Models\CategoryMap;
use Modules\Apps\Others\Olx\Models\City;
use Modules\Apps\Others\Olx\Models\Districts;
use Modules\Apps\Others\Olx\Models\Regions;
use Modules\Apps\Others\Olx\OlxManager;
use Modules\Apps\Others\Olx\Request\ProductRequest;
use Pagging;
use SmartyException;
use Throwable;
use Illuminate\Support\Facades\View;

/**
 * Class OlxController
 * @package App\Http\Controllers\Sitecp\Apps
 */
class OlxController extends AbstractAppsController
{
    use IController;

    /**
     * OlxController constructor.
     * @param OlxManager $olx
     * @throws Error
     */
    public function __construct(OlxManager $olx)
    {
        parent::__construct($olx);
    }


    /**
     * @param Request $request
     * @return RedirectResponse|Response|View
     * @throws Error
     */
    public function products(Request $request)
    {
        if ($this->hasConfigSettings()) {
            $search_object = new Search(Filter::class);
            if ($request->ajax()) {
                $filters = $request->input('filters');
                $grid = new Grid();
                [$where, $joins, $filterStrings] = $search_object->search($filters);
                $records = Advert::select('@app_olx_products_map.*')
                    ->orderBy('id', 'desc')
                    ->with(['product.vendor', 'category.parent', 'product'])->getRelationList($where, $joins, $grid)->map(function (Advert $product) {
                        if (is_null($product) || Product::where('id', $product->product_id)->count() == 0) {
                            Advert::where('id', $product->id)->delete();
                            unset($product);
                        } else {
                            $product = $product->prepareProductListingView();
                            return $product;
                        }
                    });
                $records = $records->filter(fn($item): bool => !empty($item));
                return $grid->generateIlluminate($records, $filterStrings, [
                    'records_exist' => Advert::count(),
                    'search_object' => $search_object,
                ]);
            } else {
                return \Illuminate\Support\Facades\View::mainResponse(OlxManager::APP_KEY . '::products', [
                    'search_object' => $search_object,
                    'records_exist' => Advert::count(),
                    'is_active' => $this->manager->isValidtoken()
                ]);
            }
        } else {
            return new RedirectResponse(route('apps.olx.config'));
        }
    }


    /**
     * @param Request $request
     * @return Response
     * @throws Error
     */
    public function deleteAdverts(Request $request): \Illuminate\Http\Response
    {
        if (!$this->manager->isValidtoken()) {
            return new Response([
                'msg' => __('olx::olx.invalid.token.delete'),
                'status' => 'error'
            ]);
        }

        try {
            /**@var Advert[]|Collection $products */
            $products = Advert::whereIn('id', (array)$request->input('ids'))->get();
            foreach ($products as $product) {
                $this->manager->deleteAdvertFromOlx($product->advert_id);
                $product->delete();
            }

            return new Response([
                'status' => 'success',
                'msg' => __('global.success.delete')
            ]);
        } catch (Exception $exception) {
            return new Response([
                'status' => 'error',
                'msg' => $this->manager::getErrorMessage($exception)
            ]);
            // return ;
        }
    }

    /**
     * @return Response
     * @throws Error
     */
    public function getProductsFromWebsite(Request $request): \Illuminate\Http\Response
    {
        $value = request()->query('query');
        $request_data = $request->all();
        $page = 1;
        if (isset($request_data['page'])) {
            $page = (int)$request_data['page'];
        }

        $pagingObject = new Pagging($page, 10);
        $results = Product::whereDoesntHave('olxProduct')->rightJoin('@app_olx_category_map', '@app_olx_category_map.site_category', '=', 'products.category_id')
            ->where('price_to', '>', 0)
            ->where('image_id', '>', 0)
            ->where('name', 'like', sprintf('%%%s%%', $value))
            ->paging($pagingObject);
        $results = $results->map(fn(Product $product) =>
            /** @var Product $record */
            (object)[
            'id' => $product->id,
            'name' => $product->name,
            'img' => $product->getImage()
        ]);
        // dump($pagingObject);
        return new Response([
            'results' => $results,
            'more' => $pagingObject->pages > $pagingObject->page
        ]);
    }

    /**
     * @param $id
     * @return Response
     */
    public function changeAdvertStatus($id): \Illuminate\Http\Response
    {
        if (!$this->manager->isValidtoken()) {
            return new Response([

                'msg' => __('olx::olx.invalid.token.update'),
                'status' => 'error'
            ]);
        }

        try {
            $advert = Advert::findOrFail($id);
            $olx_advert = $this->manager->getAdvert($advert->advert_id);
            $activity = ($olx_advert->data->status ?? 'inactive') == 'active' ? 0 : 1;
            $active = $this->setAdvertActivity($advert, $olx_advert, $activity);
            return new Response([
                'status' => 'success',
                'active' => $active,
                'msg' => __('olx.status_changed'),
            ]);
        } catch (Exception $exception) {
            return new Response([
                'msg' => OlxManager::getErrorMessage($exception),
                'status' => 'error'
            ]);
        }
    }

    /**
     * @param Advert $advert
     * @param $olx_advert
     * @param $active
     * @return bool
     * @throws Error
     * @throws OlxException
     */
    protected function setAdvertActivity(Advert $advert, $olx_advert, $active)
    {
        if (empty($olx_advert->data)) {
            throw new Error(__('olx::olx.invalid.advert'), );
        }

        if ($active && 'active' != $olx_advert->data->status) {
            $this->manager->activateAdvertInOlx($advert->advert_id);
            $advert->update(['advert_status' => 1]);
            return true;
        }

        if (!$active && 'active' == $olx_advert->data->status) {
            $this->manager->deactivateAdvertInOlx($advert->advert_id);
            $advert->update(['advert_status' => 0]);
            return false;
        }

        return (bool)$active;
    }

    /**
     * @param Request $request
     * @param $status
     * @return Response|static
     */
    public function bulkAdvertStatus(Request $request, $status): \Illuminate\Http\Response
    {
        if (!$this->manager->isValidtoken()) {
            return new Response([
                'msg' => __('olx::olx.invalid.token.status'),
                'status' => 'error'
            ]);
        }

        try {
            $adverts = Advert::whereIn('id', (array)$request->input('ids'))->get();

            foreach ($adverts as $advert) {
                $olx_advert = $this->manager->getAdvert($advert->advert_id);
                if (YesNo::True == $status && 'active' != ($olx_advert->data->status ?? null)) {
                    $this->setAdvertActivity($advert, $olx_advert, 1);
                }

                if (YesNo::False == $status && 'active' == ($olx_advert->data->status ?? null)) {
                    $this->setAdvertActivity($advert, $olx_advert, 0);
                }
            }

            return new Response([
                'status' => 'success',
            ]);
        } catch (Exception $exception) {
            return new Response([
                'msg' => OlxManager::getErrorMessage($exception),
                'status' => 'error'
            ]);
        }
    }


    /**
     * @param Request $request
     * @return ResponseFactory|\Illuminate\Contracts\View\View|RedirectResponse|Response
     * @throws Error
     * @throws SmartyException
     */
    public function advertsFromOLX(Request $request)
    {
        if (!$this->manager->isValidtoken()) {
            return new RedirectResponse(route('apps.olx.merchant_info'));
        }

        if ($this->CheckUserToken() == false) {
            $settings = $this->manager->getSettings();
            $state = [
                'site' => site('site_id'),
                'type' => 'olx',
                'country_id' => $settings->get('endpoint_id')
            ];
            $json_state = urlencode(json_encode($state));
            $url_access_code = config('olx.credentials.' . $settings->get('endpoint_id'))['domain'] . 'oauth/authorize/?client_id=' . config('olx.credentials.' . $settings->get('endpoint_id'))['client_id'] . '&response_type=code&scope=read+write+v2&state=' . $json_state;
            return new RedirectResponse($url_access_code);
        }

        if ($this->hasConfigSettings()) {
            if ($request->ajax()) {
                $grid = new Grid();
                try {
                    $adverts = $this->manager->getAdverts($grid);
                } catch (Exception $e) {
                    return new Response([
                        'msg' => OlxManager::getErrorMessage($e),
                        'status' => 'error'
                    ]);
                }

                $adverts_collection = Collection::make($adverts->data)->keyBy('id');
                $local_products = Advert::pluck('advert_id', 'advert_id');

                $adverts_collection = $adverts_collection->diffKeys($local_products);
                $total = count($adverts_collection);
                $grid->pagging->results = $total;
                $grid->pagging->pages = ceil($total / $grid->perpage);
                $records = $this->prepareOLXAdvertsListingView($adverts_collection);

                return $grid->generateIlluminate($records, null, ['records' => $total]);
            } else {
                return \Illuminate\Support\Facades\View::mainResponse(OlxManager::APP_KEY . '::adverts_from_olx');
            }
        } else {
            return new RedirectResponse(route('apps.olx.config'));
        }
    }

    /**
     * @param Request $request
     * @param null $advert_id
     * @param null $category_id
     * @return Response
     * @throws Throwable
     */
    public function addAdvertToStore(Request $request, $advert_id = null, $category_id = null): \Illuminate\Http\Response
    {
        try {

            $category_id = $request->input('category_id');
            if (empty($category_id)) {
                throw new Error(__('product.ph.category'));
            }

            $status = $request->input('store_status');
            if (empty($status)) {
                throw new Error(__('olx::olx.error.no_select_status'));
            }

            if (empty($advert_id)) {
                $advert_id = $request->input('advert_id', 0);
            }

            $advert = $this->manager->getAdvert($advert_id);
            $images = $this->getImagesFromOlxAdvert($advert);
            $product_info = [
                'name' => $advert->data->title,
                'url_handle' => $advert->data->title,
                'description' => $advert->data->description,
                'category_id' => $category_id,
                'type' => Product::TYPE_SIMPLE,
                'variant' => ['price' => $advert->data->price->value ?? 0],
                'active' => $status,
                'app_import' => OlxManager::APP_KEY
            ];
            $product = Product::add($product_info);

            foreach ($images as $image_url) {
                $product->uploadImageFromUrl($image_url);
            }

            Advert::create([
                'product_id' => $product['id'],
                'category_id' => $advert->data->category_id,
                'advert_id' => $advert->data->id,
                'advert_status' => ($advert->data->status == 1) ? 1 : 0,
            ]);

            return new Response([
                'msg' => __('olx::olx.advert_success_save_to_store'),
                'status' => 'success'
            ]);
        } catch (Exception $exception) {
            return new Response([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }
    }




    /**
     * @return Response
     * @throws Error
     */
    public function addProducts()
    {
        return \Illuminate\Support\Facades\View::modal(OlxManager::APP_KEY . '::add_products', [
            'settings' => $this->manager->getSettings(),
            'categories' => ArrayTree::createTree(Category::orderBy('name', 'asc')->get()),
            'date_format' => DateTimeFormat::$date_formats[setting('date_format')]['format'],
            'title_trim' => $this->manager->getSetting('title_trim'),
        ], __('olx::olx.action.add.advert'));
    }

    /**
     * @param ProductRequest $request
     * @return Response
     */
    public function saveProducts(ProductRequest $request): \Illuminate\Http\Response
    {
        try {
            $post = $request->input();
            $productsIds = explode(',', (string) $post['products']);
            $savedProducts = Product::whereIn('id', $productsIds)->get()->keyBy('id');
            $settings = $this->manager->getSettings();
            foreach ($productsIds as $product_id) {
                $product = $savedProducts->get($product_id);

                $product = isset($product) ? $product->format() : null;
                if ($product) {
                    $category_id = CategoryMap::GeMappedCategory($product->category_id);
                    $advert = $this->manager->addAdvertToOlx($this->GenerateAdvert($product, $category_id));
                    if (!empty($advert->data)) {
                        Advert::create([
                            'product_id' => $product_id,
                            'category_id' => $category_id,
                            'advert_id' => $advert->data->id,
                            'advert_status' => 0,
                            'title' => $advert->data->title,
                            'external_status' => $advert->data->status,
                            'external_url' => $advert->data->url,
                            'external_created' => $advert->data->created_at,
                            'external_valid' => $advert->data->valid_to,
                        ]);
                    }
                }
            }

            return new Response([
                'msg' => __('olx.add_advert_success'),
                'status' => 'success'
            ]);
        } catch (Exception $exception) {
            return new Response([
                'msg' => OlxManager::getErrorMessage($exception),
                'status' => 'error'
            ]);
        }
    }

    /**
     * @param mixed $category_id
     * @return mixed
     */
    private function get_mapped_category_by_product($category_id)
    {

    }


    /**
     * @param $adverts
     * @return mixed
     * @throws Error
     * @throws SmartyException
     */
    protected function prepareOLXAdvertsListingView($adverts)
    {
        foreach ($adverts as $advert) {
            $image_url = $advert->images[0]->url ?? (new Product())->getImage('150x150');
            $image = '<a href="' . $advert->url . '" target="_blank" class="list-info-holder">' .
                '<span class="list-thumb tooltips" title="' . __('olx::olx.action.view_in_olx') . '" data-html="true" data-placement="top">' .
                '<span class="overlay"><i class="fal fa-store fa-lg"></i></span>' .
                '<img src="' . $image_url . '" alt="">' .
                '</span>' .
                '</a>';
            $category_id = '';
            $GetCategoryID = CategoryMap::where('olx_category', $advert->category_id)->first();
            if (!is_null($GetCategoryID)) {
                $category_id = $GetCategoryID->site_category;
            }

            $advert->name_list =
                '<div class="pull-left">
                         <span class="editable">' . $image . '<div class="list-info name-holder">' . $advert->title . '</div></span>
                        <input type="hidden" name="advert_id" value="' . $advert->id . '">
                    </div>';

            $advert->category = '' . View::fetch('sitecp::categories.tree_select', ['category_id' => $category_id]) . '';
            $advert->status = '<select class="form-control select2me" name="store_status" data-placeholder="Изберете статус">
                      <option></option>
                        <option value="yes">' . __('olx::olx.advert.pubslish') . '</option>
                        <option value="no">' . __('olx::olx.advert.no_publish') . '</option>
                        </select>';
            $advert->button = '<button class="btn btn-primary js-add_advert pull-right" type="submit">' . __('olx::olx.action.add_to_store') . '</button>';
        }

        return $adverts;
    }


    /**
     * @param $id
     * @return Response
     */
    public function syncAdvertInOLX($id): \Illuminate\Http\Response
    {
        if (!$this->manager->isValidtoken()) {
            return new Response([
                'msg' => __('olx::olx.invalid.token.update.ad'),
                'status' => 'error'
            ]);
        }

        try {
            $advert = Advert::findOrFail($id);
            $product = Product::findOrFail($advert->product_id)->format();

            $olx_advert = $this->manager->getAdvert($advert->advert_id);
            if (empty($olx_advert->data)) {
                return new Response([
                    'msg' => __('olx::olx.invalid.advert'),
                    'status' => 'error'
                ]);
            }

            if (!empty($advert->category_id) && $advert->category_id !== $olx_advert->data->category_id) {
                if (!empty($olx_advert->category_id)) {
                    $advert->category_id = $olx_advert->category_id;
                }

                $advert->save();
            }

            $advertInfo = $this->GenerateAdvert($product, $olx_advert->data->category_id);
            $advertInfo['category_id'] = $olx_advert->data->category_id;
            $result = $this->manager->sync($advert->advert_id, $advertInfo);
            $advert->update([
                'title' => $result->data->title,
                'external_status' => $result->data->status,
                'external_url' => $result->data->url,
                'external_created' => $result->data->created_at,
                'external_valid' => $result->data->valid_to
            ]);
            return new Response([
                'msg' => __('olx::olx.sync_success'),
                'status' => 'success'
            ]);
        } catch (Exception $exception) {
            return new Response([
                'msg' => OlxManager::getErrorMessage($exception),
                'status' => 'error'
            ]);
        }
    }

    /**
     * @param $city_id
     * @return Response
     * @throws Error
     */

    public function getDistrictsForCity(Request $request, $city_id): \Illuminate\Http\Response
    {
        $query = $request->query('query');
        $districts = Districts::where('name', 'like', '%' . $query . '%')
            ->where('city_id', $city_id)
            ->orderBy('name', 'asc')
            ->get();

        return new Response([
            'status' => 'success',
            'results' => $districts,
            'more' => false
        ]);
    }

    /**
     * @param Request $request
     * @param mixed $region_id
     * @return Response
     */
    public function cities(Request $request, $region_id): \Illuminate\Http\Response
    {
        $query = $request->query('query');
        $cities = City::where('name', 'like', '%' . $query . '%')
            ->where('region_id', $region_id)
            ->orderBy('name', 'asc')
            ->get();
        return new Response([
            'status' => 'success',
            'results' => $cities,
            'more' => false
        ]);
    }

    /**
     * @param Request $request
     * @return Response
     */
    public function GetRegions(Request $request): \Illuminate\Http\Response
    {
        $query = $request->query('query');
        $region = Regions::where('name', 'like', '%' . $query . '%')
            ->orderBy('name', 'asc')
            ->get();

        return new Response([
            'status' => 'success',
            'results' => $region,
            'more' => false
        ]);
    }

    /**
     * @return mixed
     * @throws Error
     */
    public function GetMerchantInfo()
    {
        $settings = $this->manager->getSettings();
        $client = new Client(config('olx.credentials.' . $settings->get('endpoint_id')), $settings->get('endpoint_id'));
        return $client->merchant->get()->data;
    }

    /**
     * @return bool
     * @throws Error
     */
    protected function hasConfigSettings(): bool
    {
        $endpoint_id = $this->manager->getSetting('endpoint_id');
        $shipping_payer_id = $this->manager->getSetting('shipping_payer_id');
        return !is_null($endpoint_id) && !is_null($shipping_payer_id);
    }

    //    /**
    //     * @param Request $request
    //     * @return RedirectResponse
    //     * @throws Error
    //     */
    //    public function connect(Request $request)
    //    {
    //        $state = [
    //            'site' => site('site_id'),
    //            'type' => 'olx',
    //            'country_id' => $this->manager->getSetting('endpoint_id')
    //        ];
    //        $json_state = urlencode(json_encode($state));
    //        $url_access_code = config('olx.credentials.' . $this->manager->getSetting('endpoint_id'))['domain'] . 'oauth/authorize/?client_id=' . config('olx.credentials.' . $this->manager->getSetting('endpoint_id'))['client_id'] . '&response_type=code&scope=read+write+v2&state=' . $json_state;
    //        return new RedirectResponse($url_access_code);
    //    }

    /**
     * @param Request $request
     * @return Response
     * @throws Error
     * @throws \Modules\Apps\Others\Olx\Exceptions\OlxException
     */
    public function updatePrices(Request $request): \Illuminate\Http\Response
    {
        foreach ($request['ids'] as $id) {
            $advert = Advert::findOrFail($id);
            $product = Product::findOrFail($advert->product_id)->format();
            if (!empty($product)) {
                $advert_olx = $this->manager->getAdvert($advert->advert_id);
                $advertInfo = [
                    'title' => $advert_olx->data[0]->title,
                    'description' => $advert_olx->data[0]->description,
                    'category_id' => $advert_olx->data[0]->category_id,
                    'advertiser_type' => $advert_olx->data[0]->advertiser_type,
                    'external_id' => $product['id'],
                    'contact' => [
                        'name' => $advert_olx->data[0]->contact->name,
                        'phone' => $advert_olx->data[0]->contact->phone,
                    ],
                    'location' => [
                        'city_id' => $advert_olx->data[0]->location->city_id,
                        'district_id' => $advert_olx->data[0]->location->district_id,
                    ],
                    'images' => $advert_olx->data[0]->images,
                    'price' => $this->getPrice($product),
                    'attributes' => $advert_olx->data[0]->attributes,
                ];
                dd($advert->advert_id, $advertInfo);

                $result = $this->manager->sync($advert->advert_id, $advertInfo);
                if (!empty($result->data)) {
                    $advert->update([
                        'title' => $result->data->title,
                        'external_status' => $result->data->status,
                        'external_url' => $result->data->url,
                        'external_created' => $result->data->created_at,
                        'external_valid' => $result->data->valid_to
                    ]);
                } else {
                    return new Response([
                        'msg' => __('olx::invalid.advert'),
                        'status' => 'error'
                    ]);
                }
            }
        }

        return new Response([
            'msg' => __('olx::olx.bulk_price.success'),
            'status' => 'success'
        ]);
    }
}
