<?php

declare(strict_types=1);

namespace Modules\Apps\Others\Olx\Http\Controllers;

use App\Exceptions\Error;
use App\Traits\IController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Apps\Abstractions\Controllers\AbstractAppsController;
use Modules\Apps\Others\Olx\Models\Attributes;
use Modules\Apps\Others\Olx\Models\Category;
use Modules\Apps\Others\Olx\Models\CategoryMap;
use Modules\Apps\Others\Olx\Models\ParameterMap;
use Modules\Apps\Others\Olx\OlxManager;
use Modules\Apps\Others\Olx\Request\CategoryMapRequest;

class CategoryApi extends AbstractAppsController
{
    use IController;

    /**
     * OlxController constructor.
     * @param OlxManager $olx
     * @throws Error
     */
    public function __construct(OlxManager $olx)
    {
        parent::__construct($olx);
    }

    /**
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $mapping_categories = CategoryMap::with(['OlxCategory', 'OlxCategory.attributes'])->whereHas('SiteCategory')->paginate();
        $mapping_categories->transform(fn($item) => $this->formatRow($item));
        return response()->json($mapping_categories);
    }

    /**
     * @param $category_id
     * @param $name
     * @return \App\Helper\Translation\Translator|array|mixed|string|null
     */
    private function get_parent($category_id, $name = null)
    {
        $data = Category::where('id', $category_id)->first();
        if (isset($data->parent_id)) {
            if ($data->parent_id > 0) {
                if ($name != null) {
                    $name = $data->name . ' > ' . $name;
                } else {
                    $name = $data->name;
                }

                return $this->get_parent($data->parent_id, $name);
            } else {
                $new_name = $name ? $data->name . ' > ' . $name : $data->name;
                return $new_name;
            }
        } else {
            return __('olx::olx.category.not_exist');
        }
    }

    /**
     * @return JsonResponse
     */
    public function search(): JsonResponse
    {
        $array = Category::where('name', 'like', '%' . \request('query') . '%')->orderBy('name', 'asc')->paginate();
        $array->transform(function ($item) {
            $item->name = $this->get_parent($item->id);
            return $item->only(['id', 'name']);
        });
        return response()->json($array);
    }

    /**
     * @param CategoryMapRequest $request
     * @return JsonResponse
     */
    public function save(CategoryMapRequest $request): JsonResponse
    {
        $data = $request->all();
        $data = CategoryMap::updateOrCreate([
            'olx_category' => $data['olx_id'],
            'site_category' => $data['site_id'],
        ], [
            'site_category' => $data['site_id'],
            'olx_category' => $data['olx_id']
        ]);
        $item = CategoryMap::with(['SiteCategory', 'OlxCategory', 'OlxCategory.attributes'])->where('id', $data->id)->first();
        return response()->json($this->formatRow($item));
    }

    /**
     * @return JsonResponse
     */
    public function delete(Request $request): JsonResponse
    {
        CategoryMap::where(['site_category' => $request->get('site_id'), 'olx_category' => $request->get('olx_id')])->delete();
        return response()->json([]);
    }

    /**
     * @param $item
     * @return mixed
     */
    protected function formatRow($item)
    {
        $getAttribute = Attributes::where(['category_id' => $item->olx_category, 'required' => true])->where('code', '!=', 'state')->count();
        $item->site = [
            'id' => $item->site_category,
            'name' => $item->SiteCategory->path->implode('name', ' > '),
        ];
        $item->olx = [
            'id' => $item->olx_category,
            'name' => $this->get_parent($item->olx_category),
            'parameters' => $getAttribute
        ];
        $item->mapped_parameters = $getAttribute > 0 ? ParameterMap::where(['category_id' => $item->olx_category])->count() : 0;
        return $item->only(['site', 'olx', 'mapped_parameters']);
    }
}
