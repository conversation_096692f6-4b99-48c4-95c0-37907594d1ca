<?php

declare(strict_types=1);

namespace Modules\Apps\Others\Olx\Models;

use Modules\Apps\Others\Olx\Scopes\Endpoint;

class Category extends \Eloquent
{
    protected $table = 'cc_apps.olx_categories';

    protected $connection = 'apps';

    protected $fillable = [
        'id', 'name', 'parent_id', 'endpoint_id'
    ];

    protected $data = [];

    public $timestamps = false;

    #[\Override]
    protected static function boot()
    {
        parent::boot();

        if (site('site_id')) {
            static::addGlobalScope(new Endpoint());
        }
    }

    public function parent()
    {
        return $this->hasOne(static::class, 'id', 'parent_id')->with('parent');
    }

    /**
     * @param mixed $category_id
     * @return mixed
     */
    public static function hasChildren($category_id): int
    {
        return Category::all()->where('parent_id', $category_id)->count();
    }

    /**
     * @param mixed $endpoint_id
     * @return mixed
     */
    public static function GetCategoryByEndpoint($endpoint_id)
    {
        if (Category::where('endpoint_id', $endpoint_id)->count() > 0) {
            return Category::where('endpoint_id', $endpoint_id)->get();
        } else {
            return false;
        }
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function attributes()
    {
        return $this->hasMany(Attributes::class, 'category_id', 'id');
    }
    /**
     * @param mixed $value
     * @return mixed
     */
    public function getPathAttribute($value)
    {
        if (!$this->exists) {
            return;
        }
        if (empty($this->data['path'])) {
            $path = [$this->name];
            $parent = $this->parent;
            while ($parent) {
                array_unshift($path, $parent->name);
                $parent = $parent->parent;
            }

            $this->data['path'] = implode(' > ', $path);
        }
        return $this->data['path'];
    }

}
