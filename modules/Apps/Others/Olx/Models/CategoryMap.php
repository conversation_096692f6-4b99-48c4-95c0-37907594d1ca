<?php

declare(strict_types=1);

namespace Modules\Apps\Others\Olx\Models;

use App\Models\Product\Category;

class CategoryMap extends \Eloquent
{
    protected $table = '@app_olx_category_map';

    public $timestamps = false;

    protected $fillable = [
        'id', 'olx_category', 'site_category'
    ];

    public function SiteCategory()
    {
        return $this->HasOne(Category::class, 'id', 'site_category');
    }

    public function OlxCategory()
    {
        return $this->HasOne(\Modules\Apps\Others\Olx\Models\Category::class, 'id', 'olx_category');
    }

    public function OlxParameter()
    {
        return $this->HasMany(\Modules\Apps\Others\Olx\Models\Attributes::class, 'category_id', 'olx_category');
    }

    public static function GetCategories()
    {
        return CategoryMap::with('SiteCategory')->with('OlxCategory');
    }

    /**
     * @param mixed $site_category
     * @return mixed
     */
    public static function GeMappedCategory($site_category)
    {
        return CategoryMap::where('site_category', $site_category)->value('olx_category');
    }

}
