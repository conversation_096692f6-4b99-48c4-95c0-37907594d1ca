<?php

declare(strict_types=1);

namespace Modules\Apps\Others\Olx\Commands;

use Illuminate\Console\Command;

class PopulateRegions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'olx:populate-regions
                            {--endpoint= : Endpoint ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Populate OLX regions for a given endpoint (Country)';

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Throwable
     */
    public function handle(): void
    {
        if (empty($endpoint = $this->input->getOption('endpoint'))) {
            throw new \RuntimeException('Missing Endpoint ID');
        }

        if (empty($credentials = config('olx.credentials.' . $endpoint))) {
            throw new \RuntimeException(sprintf('There is no configuration for endpoint: %s', $endpoint));
        }

        $job = new \Modules\Apps\Others\Olx\Jobs\PopulateRegions($endpoint, $credentials);
        $job->handle();
    }
}
