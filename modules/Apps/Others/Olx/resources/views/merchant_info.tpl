{include file="apps/dependency/settings.tpl"}

<div class="page-breadcrumb clearfix">
    {$breadcrumb = [
    [
    'title' => "{t}sidebar.apps{/t}",
    'href' => "{route('admin.apps')}"
    ],
    [
    'href' => "{asset('')}apps/olx",
    'title' => "{t}olx::olx.info.title{/t}"
    ]
    ]}
    {include file="includes/breadcrumb.tpl"}
    <div class="pull-right">{include file="./connect_button.tpl"}</div>
</div>

<div class="wrapper">
    {include file="./tabs.tpl"}
    <div class="container-small">
        <div class="box">
            <div class="box-section">
                <div class="row">
                    <div class="col-xs-12">
                        <form action="{route('apps.olx.merchant_info')}" id="editForm" role="form"
                              data-redirect="{route('apps.olx')}">
                            <div class="form-group">
                                <label class="control-label">{t}customer.label.first_name{/t}</label>
                                <input type="text" name="first_name" class="form-control" data-autofocus=""
                                       value="{$settings->get('first_name')}">
                            </div>
                            <div class="form-group">
                                <label class="control-label">{t}customer.label.last_name{/t}</label>
                                <input type="text" name="last_name" class="form-control" data-autofocus=""
                                       value="{$settings->get('last_name')}">
                            </div>
                            <div class="form-group">
                                <label class="control-label">{t}olx::olx.phone{/t}</label>
                                <input type="text" name="phone" placeholder="xxx - xxx - xxxx" class="form-control" data-autofocus=""
                                       value="{$settings->get('phone')}">
                            </div>
                            <div class="form-group padding-top-0">
                                <label class="control-label">{t}olx::olx.region{/t}</label>
                                <input name="region_id" id="region_id" class="form-control select2_ajax" data-min-length="3"
                                       data-url="{route('apps.olx.regions')}" value="{$region_json|default}"/>
                            </div>
                            <div class="form-group{if !$settings->get('city_id')} hidden{/if}" id="city-form">
                                <label class="control-label">{t}olx::olx.city{/t}</label>
                                <select class="form-control select2me" name="city_id" id="city"{if !$settings->get('city_id')} disabled{/if}>
                                    {if !is_null($cities) }
                                        {foreach $cities as $city}
                                            <option value="{$city->id}" {if isset($settings->get('city_id')) && $city->id == $settings->get('city_id')} selected{/if}>{$city->name}</option>
                                        {/foreach}
                                    {/if}
                                </select>
                                <input type="hidden" name="city_name" value="{$city_json|default}" {if !$settings->get('city_id')} disabled{/if}>
                            </div>
                            <div class="form-group{if !$settings->get('district_id')} hidden{/if}" id="district-form">
                                <label class="control-label">{t}olx::olx.district{/t}</label>
                                <select class="form-control select2me" name="district_id" id="district"{if !$settings->get('district_id')} disabled{/if}>
                                    {if !is_null($districts) }
                                        {foreach $districts as $district}
                                            <option value="{$district->id}" {if isset($settings->get('district_id')) && $district->id == $settings->get('district_id')} selected{/if}>{$district->name}</option>
                                        {/foreach}
                                    {/if}
                                </select>
                                <input type="hidden" name="district_name" value="{$settings->get('district_name')}"{if !$settings->get('district_id')} disabled{/if}>
                            </div>
                            <div class="form-group">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="control-label">{t}olx::olx.config.sync.status{/t} <i
                                                    class="glyphicon glyphicon-info-sign tooltips"
                                                    title="{t js=true}olx::olx.config.sync.status.help{/t}"
                                                    data-placement="top"></i></label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" class="switch" value="1"
                                               name="sync_status"{if $settings.sync_status|default} checked="checked"{/if}>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="control-label">{t}olx::olx.config.sync.delete{/t} <i
                                                    class="glyphicon glyphicon-info-sign tooltips"
                                                    title="{t js=true}olx::olx.config.sync.delete.help{/t}"
                                                    data-placement="top"></i></label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" class="switch" value="1"
                                               name="sync_delete"{if $settings.sync_delete|default} checked="checked"{/if}>
                                    </div>
                                </div>

                            </div>
                            <div class="form-group">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="control-label">{t}olx::olx.label.sync.qty{/t} <i
                                                    class="glyphicon glyphicon-info-sign tooltips"
                                                    title="{t js=true}olx::olx.help.sync.qty{/t}"
                                                    data-placement="top"></i></label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" class="switch" value="1"
                                               name="sync_quantity"{if $settings.sync_quantity|default} checked="checked"{/if}>
                                    </div>
                                </div>

                            </div>
                            <div class="form-group">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="control-label">{t}olx::olx.setting.is_discount{/t} <i
                                                    class="glyphicon glyphicon-info-sign tooltips"
                                                    title="{t js=true}olx::olx.setting.is_discount_help{/t}"
                                                    data-placement="top"></i></label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" class="switch" value="1"
                                               name="is_discount"{if $settings.is_discount|default} checked="checked"{/if}>
                                    </div>
                                </div>

                            </div>
                            <div class="form-group">
                                <div class="stack">
                                    <div class="stack-main">
                                        <label class="control-label">{t}olx::olx.label.trim.title{/t} <i
                                                    class="glyphicon glyphicon-info-sign tooltips"
                                                    title="{t js=true}olx::olx.help.trim.title{/t}"
                                                    data-placement="top"></i></label>
                                    </div>

                                    <div class="stack-addon">
                                        <input type="checkbox" class="switch" value="1"
                                               name="title_trim" {if $settings.title_trim == 1} checked="checked"{/if}>
                                    </div>
                                </div>

                            </div>
                            <div class="form-group">

                                {if !$is_valid}
                                    <div class="col-xs-12">
                                        <div class="alert alert-warning">
                                            <p>{t}olx::olx.invalid.token.merchant{/t}</p>
                                        </div>
                                    </div>
                                {else}
                                    <button class="btn btn-primary" type="submit" id="editSubmit">{t}global.save{/t}</button>
                                {/if}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{capture append="js"}
    <script type="text/javascript">
        $(function () {
            var $district_id = $("#district");
            var $district_name = $('input[name="district_name"]');
            var $district_form = $("#district-form");

            var $city_id = $("#city");
            var $city_name = $('input[name="city_name"]');
            var $city_form = $("#city-form");
            $('#region_id').on('change', function () {
                var element = $(this);
                var region_id = element.val();
                var url = '{route('apps.olx.cities', ":region_id")}';
                url = url.replace(":region_id", region_id);
                CC.ajax({
                    url: url,
                    success: function (json) {
                        if (json.results.length) {
                            $city_id.select2('destroy').empty();
                            $city_id.prop('disabled', false).removeClass('hidden');
                            $city_name.prop('disabled', false);
                            $city_form.removeClass('hidden');

                            $.each(json.results, function () {
                                $city_id.append($("<option></option>").attr("value", this.id).text(this.name));
                            });

                            App.initSelect2($city_form);
                            $city_id.trigger('change');
                        } else {
                            $city_id.prop('disabled', true).addClass('hidden');
                            $city_name.prop('disabled', true);
                            $city_form.addClass('hidden');
                        }
                    }
                });
            });

            $('#city').on('change', function () {
                var element = $(this);
                var city_id = element.val();
                var url = '{route('apps.olx.districts', ":city_id")}';
                url = url.replace(":city_id", city_id);
                CC.ajax({
                    url: url,
                    success: function (json) {
                        if (json.results.length) {
                            $district_id.select2('destroy').empty();
                            $district_id.prop('disabled', false).removeClass('hidden');
                            $district_name.prop('disabled', false);
                            $district_form.removeClass('hidden');

                            $.each(json.results, function () {
                                $district_id.append($("<option></option>").attr("value", this.id).text(this.name));
                            });

                            App.initSelect2($district_form);
                            $district_id.trigger('change');
                        } else {
                            $district_id.prop('disabled', true).addClass('hidden');
                            $district_name.prop('disabled', true);
                            $district_form.addClass('hidden');
                        }
                    }
                });
            });

            $district_id.on('change', function () {
                $district_name.val($(this).select2('data').text);
            });
            $city_id.on('change', function () {
                $city_name.val($(this).select2('data').text);
            });
        });
    </script>
{/capture}