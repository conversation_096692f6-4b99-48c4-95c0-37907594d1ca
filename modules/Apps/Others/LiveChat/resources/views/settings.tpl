{include file="../dependency/settings.tpl"}
{include file="../includes/app-breadcrumb.tpl" active=__('live_chat.info.title')}

<div class="wrapper">
    <div class="app-wrapper">
        {include file="../includes/app-intro.tpl" title=__('live_chat.header.setting') text=__('live_chat.help.setting')}
        {include file="../includes/app-icons.tpl"  app_icon='icon-live-chat.png'}
    </div>

    <div class="container-small">
        <div class="box">
            <div class="box-section">
                <div class="row">
                    <div class="col-md-12">
                        <form action="{route('apps.live-chat.settings')}" id="editForm" role="form">
                            <div class="form-group">
                                <p class="help-block">{t}live_chat.info{/t}</p>
                            </div>

                            <div class="form-group">
                                <label class="control-label">{t}apps.label.enabled{/t}</label>
                                <input name="active" type="checkbox" class="switch"
                                       value="1"{if $active} checked="checked"{/if} />
                            </div>
                            
                            <div class="form-group">
                                <label class="control-label">{t}live_chat.label.code{/t}</label>
                                &nbsp;<i class="glyphicon glyphicon-info-sign tooltips" title="{t}live_chat.help.code{/t}"
                                         data-placement="top"></i>
                                <input type="text" name="live_chat" value="{$code}" class="form-control" data-autofocus=""/>
                            </div>

                            <div class="form-group">
                                <button class="btn btn-primary" type="submit" id="editSubmit">{t}global.save{/t}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>