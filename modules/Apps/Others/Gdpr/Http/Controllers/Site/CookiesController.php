<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 22.5.2018 г.
 * Time: 14:41 ч.
 */

namespace Modules\Apps\Others\Gdpr\Http\Controllers\Site;

use Modules\Apps\Others\Gdpr\Http\Request\FormRequest;
use Auth;
use Cookie;
use GDPR;
use Illuminate\Http\Request;
use Modules\Apps\Others\Gdpr\GDPRManager;
use Illuminate\Routing\Controller;

class CookiesController extends Controller
{
    /**
     * FormController constructor.
     * @param GDPRManager $manager
     */
    public function __construct(protected \Modules\Apps\Others\Gdpr\GDPRManager $manager)
    {
    }

    /**
     * @param Request $request
     * @param null $type
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\Response
     */
    public function index(Request $request, $type = null)
    {
        if (empty($type) || !array_key_exists($type, $this->manager->getRequestTypes())) {
            $type = 'info';
        }

        $data = [
            'form_types' => $this->manager->getRequestTypes(),
            'type' => $type,
            'customer' => Auth::customer()
        ];

        if ($request->ajax()) {
            return \Illuminate\Support\Facades\View::fetchHtmlJsonResponse('gdpr::site.form', $data);
        }

        return \Illuminate\Support\Facades\View::mainResponse('gdpr::site.form', $data);
    }

    /**
     * @param FormRequest $request
     * @param $type
     * @return \Illuminate\Http\Response
     */
    public function store(FormRequest $request, $type)
    {
        if (empty($type) || !array_key_exists($type, $this->manager->getRequestTypes())) {
            $type = 'info';
        }

        return response([
            'status' => 'success',
            'msg' => __('gdpr.success.form'),
        ]);
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    public function consent(Request $request)
    {
        if (GDPR::isActive()) {
            $notAccepted = GDPR::getNotAcceptedCookies();
            $cookies = array_keys($request->cookie());
            foreach ($cookies as $cookie) {
                if (in_array($cookie, $notAccepted)) {
                    $url = site()->getSiteUrl();
                    Cookie::queue(Cookie::forget($cookie, null, '.' . $url));
                    $url = explode('.', $url);
                    unset($url[0]);
                    $url = '.' . implode('.', $url);
                    Cookie::queue(Cookie::forget($cookie, null, $url));
                }
            }
        }

        return response('');
    }

}
