<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 23.5.2018 г.
 * Time: 12:45 ч.
 */

namespace Modules\Apps\Others\Gdpr\Http\Request;

/**
 * Class PolicyRequest
 */
class PolicyRequest extends FormRequest
{
    /**
     * @return array
     */
    #[\Override]
    public function rules(): array
    {
        $page_id = routeParameter('policy_id');
        return [
            'policy.name' => 'required|min:2|max:191|unique:pages,name' . ($page_id ? ',' . $page_id : ''),
            'policy.content' => 'required|min:2',
        ];
    }

    #[\Override]
    public function messages()
    {
        return [
            'policy.name.required' => 'The name field is required',
            'policy.name.min' => 'The name must be at least 2 characters',
            'policy.name.max' => 'The name may not be greater than 191 characters',
            'policy.name.unique' => 'The name has already been taken',
            'policy.content.required' => 'The content field is required',
            'policy.content.min' => 'The content must be at least 2 characters',
        ];

    }

    /**
     * @return array
     */
    #[\Override]
    public function attributes()
    {
        return [
            'policy.name' => __('gdpr::gdpr.policy.label.name'),
            'policy.content' => __('gdpr::gdpr.policy.label.content'),
        ];
    }

}
