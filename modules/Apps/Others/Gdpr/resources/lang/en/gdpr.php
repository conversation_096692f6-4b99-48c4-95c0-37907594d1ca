<?php

declare(strict_types=1);

return [
    'acceptance.form' => 'Section',
    'acceptance.help.search' => '* You can search only by email',
    'acceptance.notify.no_records_help' => 'If you need help, visit our site',
    'acceptance.notify.no_records_help_link' => 'Help Center',
    'acceptance.notify.no_records_info' => 'There are still no accepted policies by your users',
    'acceptance.notify.no_records_yet' => 'No entries found in the register',
    'acceptance.title' => 'Policy',
    'acceptance.user_agent' => 'Device fingerpirnt',
    'action.add.cookie' => 'Add a cookie',
    'action.send' => 'Send',
    'box.title.add.cookie' => 'Add a cookie',
    'box.title.cookies_bar' => 'Information bar for your cookie policy',
    'breadcrumb.title' => 'General Data Protection Regulation - GDPR',
    'checklist' => 'GDPR list',
    'checklist.description' => 'Use the GDPR list below to be able to prepare your online store according to the GDPR regulation',
    'client_ip' => 'IP address',
    'cookies' => 'Cookies',
    'cookies.cookies_bar' => 'Info bar for using cookies',
    'created_at' => 'Saved on',
    'customer' => 'Client',
    'customer_ip' => 'IP address',
    'data.security.comming.soon' => 'This section will be available soon',
    'email' => 'Email',
    'err.cookie.exists' => 'Same record exists',
    'form.type.contacts' => 'Contact Form',
    'form.type.mailchimp_newsletter' => 'Subscribed to newsletter',
    'form.type.segment_subscription_popup' => 'Subscribers - subscription forms',
    'form.type.policies_popup' => 'Request for consent for registered users after login',
    'form.type.register' => 'User Registration',
    'form.type.submit_payment' => 'Completing an order',
    'forms' => 'Sections in which the consent is given',
    'forms.optional' => '<strong> OPTIONAL: </strong> The pages listed below will be optional and users will not be required to agree with them before they can move on.',
    'forms.required' => '<strong> REQUIRED: </strong> The pages listed below will be mandatory and users will have to agree with them before they can move on.',
    'head.policy.create' => 'Create a Policy',
    'head.policy.edit' => 'Policy Revision',
    'header.install' => 'CloudCart GDPR for online stores',
    'help.install' => 'This is an application for Implementation of the" General Data Protection Regulation',
    'id' => 'ID',
    'info.install' => 'The GDPR Legal Documents You Receive: <br> <br>
- Additional text for your "Terms and Conditions"
- Privacy policy
- Info bar for collection a consents through cookies
- GDPR email policy for "Abondoned orders"
- Protocols for work performed by data administrator
<br> <br>
GDPR functionality in the platform you will get: <br> <br>
- A bar showing all used cookies
- Fields of agreement with additional store policies
- Functionality, recording user actions
- Functionality for the exercise of data transmission rights
- User profile <br> <br>
- Send an email for a failed order conforming to the GDPR ',
    'info.title' => 'GDPR',
    'label.cookie.cookies' => 'Cookies',
    'label.cookie.description' => 'Cookie description',
    'label.cookie.name' => 'Provider name',
    'label.consent.cookies' => 'Key',
    'label.consent.description' => 'Description of consent',
    'label.consent.name' => 'Name',
    'ph.email' => 'Email',
    'ph.forms' => 'Here you select a page with which users agree',
    'ph.message' => 'Message',
    'ph.name' => 'Service name that installs the cookie',
    'policy.action.add' => 'Add new policy',
    'policy.help.marketing' => 'With this option, you mark that the policy is related to your marketing goals. This policy will appear to your customers to agree to receive marketing messages from your store',
    'policy.help.name' => 'This is the title of the new policy you are creating',
    'policy.label.content' => 'Policy text',
    'policy.label.marketing' => 'Marketing',
    'policy.label.name' => 'Policy',
    'policy.notify.no_records_help' => 'If you need help, visit our',
    'policy.notify.no_records_help_link' => 'Help Center',
    'policy.notify.no_records_info' => '',
    'policy.notify.no_records_yet' => 'No Policies Found',
    'policy.ph.content' => 'Policy content',
    'policy.ph.name' => 'Enter Policy Name Here',
    'privacy.notice' => 'Please for your consent',
    'request.type.delete' => 'Request to delete data',
    'request.type.download' => 'Request to retrieve information',
    'request.type.info' => 'Request type',
    'request.type.marketing_unsubscribe' => 'Unsubscribe request from marketing',
    'requests.type' => 'Request type',
    'setting.cookies.consent.description' => 'Short description of your Cookie Policy - (visible in the "Cookie Settings" window)',
    'setting.cookies_bar' => 'Notification to the Supervisory Authority',
    'setting.cookies_bar.description' => 'Third parties processing personal data',
    'success.form' => 'Successfully Recorded Operation',
    'success.form_mapping' => 'Successfully Recorded Operation',
    'success.policy.created' => 'Policy created',
    'success.policy.updated' => 'Policy updated',
    'tab.cookies' => 'Cookie log',
    'tab.data.security' => 'Notification to the supervisory authority',
    'tab.policies' => 'Policies',
    'tab.policy.acceptance' => 'Policy acceptance register',
    'tab.requests' => 'Data processing register',
    'tab.settings' => 'Settings',
    'tab.third.parties' => 'Third parties handling personal data',
    'tip.cookies_bar' => 'This bar will be displayed to all users who did not agree to the terms of your Cookie Policy',
    'title.able_to' => '',
    'updated_at' => 'Updated to',
];
