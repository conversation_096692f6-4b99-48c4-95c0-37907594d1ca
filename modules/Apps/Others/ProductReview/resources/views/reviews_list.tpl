{include file="./list_dependency.tpl"}
<div class="page-breadcrumb clearfix">
    {$breadcrumb = [
    [
    'title' => "{t}sidebar.apps{/t}",
    'href' => "{route('admin.apps')}"
    ],
    [
    'href' => "{asset('')}apps/product_review",
    'title' => "{t}product_review::product_review.info.title{/t}"
    ],
    [
    'title' => "{t}product_review::product_review.added.review{/t}"
    ]
    ]}
    {include file="includes/breadcrumb.tpl"}
    {strip}
        <div class="pull-right">
            <a href='{route('apps.product_review.create')}' class="btn btn-primary" {if $subscription_is_active}data-panel-class="helpdesk-iframe" data-ajax-panel="true"{/if}>
                {t}product_review::product_review.added.new.review{/t}
            </a>
        </div>
    {/strip}
</div>

<div class="wrapper">
    {include file="./tabs.tpl"}
    <div id="products_wrapper" class="grid-wrapper grid-products padding-top-0 hidden" data-url="{route('apps.product_review.reviews')}" data-view_cookie="products">
        {include file="includes/filters.tpl" filter="apps/product_review/filters/review_filter" form_action="{route('apps.product_review.reviews')}" bulk="apps/product_review/filters/review_bulk"}
        <table class="listing">
            <thead>
            <tr>
                <th data-field="image" data-sort="no" width="50"></th>
                <th data-field="product_name" data-sort="no" width="250"> {t}product_review::product_review.product{/t}</th>
                <th data-field="user_data" data-sort="no" > {t}product_review::product_review.client{/t}</th>
                <th data-field="rating"  data-sort="yes" data-field-display="rating" width="150"> {t}product_review::product_review.rating{/t}</th>
                <th data-field="comment" data-sort="no" data-align="left"> {t}product_review::product_review.commenttd{/t}</th>
                <th data-field="created_ats"  data-sort="no"  data-field-display="created_ats"> {t}product_review::product_review.date{/t}</th>
                <th data-field="publish_review"  data-sort="no" data-align="center" width="100"></th>
            </tr>
            </thead>
        </table>

        <div class="table-footer">
            {include file="includes/bulk.tpl" bulk="apps/product_review/filters/review_bulk"}
        </div>
    </div>

    <div id="noResults" class="hidden">
        <div class="content-padding">
        </div>
    </div>
</div>

{capture append="js"}
    <script src="{{$img_url}}site/js/plugins/simple-rating.js"></script>
    <script type="text/javascript">
        $(function () {
            $(document).on('cc.ajax.success', '#ReviewForm,  #category_wrapper .delete', function (e) {
                e.preventDefault();
                $grid = $('.grid-parameter');
                $grid.trigger('cc.ajax.reload');
            });

            $(document).on('click', '.change_review_status, #products_wrapper .delete', function (e) {
                e.preventDefault();
                $form = $(this);
                $form.on('cc.ajax.success', function ()
                {
                    $grid = $('.grid-products');
                    $grid.trigger('cc.ajax.reload');
                });
            });
        });
    </script>
{/capture}
<style>
    .my_filter_group { padding: 15px 0px; }
    .grid-controls { padding: 0 0px 10px; }
    .simple-rating {
    }
    .simple-rating i {
        color: #f5ba00;
        display: inline-block;
        padding: 1px 2px;
        cursor: pointer;
    }
    .text-primary {
        color: #b38af4;
    }
</style>