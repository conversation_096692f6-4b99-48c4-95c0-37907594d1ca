<?php

declare(strict_types=1);

namespace Modules\Apps\Marketing\Mailchimp\Sync;

use App\Helper\Format;
use App\Models\Product\Product as ProductModel;
use DrewM\MailChimp\Batch;
use Illuminate\Support\Collection;
use Modules\Apps\Marketing\Mailchimp\MailchimpManager;

class Product extends Sync
{
    /**
     * @param \Modules\Apps\Marketing\Mailchimp\MailchimpManager $manager
     * @return mixed
     */
    public function __construct(MailchimpManager $manager)
    {
        parent::__construct($manager, 'products');
    }

    public function sync(): void
    {
        /** @var ProductModel[]|Collection $local */
        $local = $this->local();
        $remote = $this->remote();
        $remote_ids = array_map(fn(array $remote) => $remote['id'], $remote);

        foreach ($local as $product) {

            $variants = [$product->id => ['id' => $product->id, 'title' => $product->name]];
            foreach ($product->variants as $v) {
                $variant = [
                    'id' => $v->id,
                    'title' => implode(' / ', array_filter([$v->v1, $v->v2, $v->v3]) ?: [$product->name]),
                    'sku' => (string)$v->sku,
                    'quantity' => (int)$v->quantity,
                    'price' => (float)Format::moneyInput($v->price),
                    'image_url' => (string)$product->getImage('150x150'),
                ];

                if (is_int($v->quantity)) {
                    $variant['inventory_quantity'] = $v->quantity;
                }

                $variants[$v->id] = $variant;
            }

            $data = [
                'id' => $product->id,
                'title' => $product->name,
                'handle' => $product->url_handle,
                'url' => \Linker::fullLink(\Linker::product($product->url_handle)),
                'description' => $product->description ?: '',
                'image_url' => $product->getImage('150x150'),
                'variants' => array_values($variants),
                'published_at_foreign' => (string)(($product->publish_date ?: $product->date_modified) ?: $product->date_added),
            ];

            if ($product->category) {
                $data['category'] = $product->category->name;
            }

            if ($product->vendor) {
                $data['vendor'] = $product->vendor->name;
            }

            $this->put($product->id, $data);
        }

        // remove deleted products
        $check = ProductModel::whereIn('id', array_map(fn(array $remote) => $remote['id'], $remote))
            ->pluck('id');

        $remove = array_diff($remote_ids, $check->all());
        foreach ($remove as $id) {
            $this->delete($id);
        }
    }

    /**
     * @param Batch $batch The MailChimp batch object to enqueue operations to.
     *
     * @return array
     */
    public function enqueue(Batch &$batch): void
    {
        /** @var ProductModel[]|Collection $local */
        $local = $this->local();
        $remote = $this->remote();
        $remote_ids = array_map(fn(array $remote) => $remote['id'], $remote);

        foreach ($local as $product) {

            $variants = [$product->id => ['id' => $product->id, 'title' => $product->name]];
            foreach ($product->variants as $v) {
                $variant = [
                    'id' => $v->id,
                    'title' => implode(' / ', array_filter([$v->v1, $v->v2, $v->v3]) ?: [$product->name]),
                    'sku' => (string)$v->sku,
                    'quantity' => (int)$v->quantity,
                    'price' => (float)Format::moneyInput($v->price),
                    'image_url' => $product->getImage('150x150'),
                ];

                if (is_int($v->quantity)) {
                    $variant['inventory_quantity'] = $v->quantity;
                }

                $variants[$v->id] = $variant;
            }

            $data = [
                'id' => $product->id,
                'title' => $product->name,
                'handle' => $product->url_handle,
                'url' => \Linker::fullLink(\Linker::product($product->url_handle)),
                'description' => strip_tags((string) $product->description) ?: '',
                'image_url' => $product->getImage('150x150'),
                'variants' => array_values($variants),
                'published_at_foreign' => (string)(($product->publish_date ?: $product->date_modified) ?: $product->date_added),
            ];

            if ($product->category) {
                $data['category'] = $product->category->name;
            }

            if ($product->vendor) {
                $data['vendor'] = $product->vendor->name;
            }

            $batch->delete(
                'PRODUCT-DELETE#' . $product->id,
                sprintf('ecommerce/stores/%s/%s/%s', $this->store_id, $this->key, $product->id)
            );
            $batch->post('PRODUCT-POST#' . $product->id, sprintf('ecommerce/stores/%s/%s', $this->store_id, $this->key), $data);
        }

        // remove deleted products
        $check = ProductModel::whereIn('id', array_map(fn(array $remote) => $remote['id'], $remote))
            ->pluck('id');

        $remove = array_diff($remote_ids, $check->all());
        foreach ($remove as $id) {
            $batch->delete(
                'PRODUCT-DELETE#' . $id,
                sprintf('ecommerce/stores/%s/%s/%s', $this->store_id, $this->key, $id)
            );
        }
    }

    /**
     * @param mixed $local
     * @param mixed $remote
     * @return mixed
     */
    public function comparison($local, $remote): bool
    {
        return $local->id == (int)$remote['id'];
    }

    /**
     * @param mixed $id
     * @param mixed $data
     * @return mixed
     */
    #[\Override]
    public function put($id, $data)
    {
        $product = $this->mailchimp->get(sprintf('ecommerce/stores/%s/%s/%s', $this->store_id, $this->key, $id));

        if (is_array($product) && isset($product['id'])) {
            $this->delete($product['id']);
        }

        $result = $this->mailchimp->post(sprintf('ecommerce/stores/%s/%s', $this->store_id, $this->key), $data);

        return $result;
    }

    /**
     * @return ProductModel|\Illuminate\Database\Eloquent\Builder
     */
    public function getModel()
    {
        $model = ProductModel::with(['category', 'vendor', 'variants']);
        if ($this->manager->getSetting('last_mailchimp_synchronization')) {
            $model = $model->where('date_modified', '>=', $this->manager->getSetting('last_mailchimp_synchronization'))
                ->orWhere('date_added', '>=', $this->manager->getSetting('last_mailchimp_synchronization'));
        }

        return $model;
    }

}
