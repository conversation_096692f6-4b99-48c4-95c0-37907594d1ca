<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Selmatic\Services;

use App\Providers\RouteServiceProvider;
use Illuminate\Routing\Router;
use Modules\Apps\Erp\Selmatic\SelMaticManager;
use Illuminate\Support\ServiceProvider;


class SelMaticServiceProvider extends ServiceProvider
{

    protected $namespace = 'Modules\Apps\Erp\Selmatic\Http\Controllers';


    /**
     * @return void
     */
    public function boot(): void
    {
        $this->registerRoutes();
        $this->registerViews();
        $this->registerTranslations();
        $this->mergeConfigFrom(__DIR__ . '/../config/google_drive.php', 'google_drive');
        $this->mergeConfigFrom(__DIR__ . '/../config/queue.php', 'queue.mapping');
    }

    /**
     * @return void
     */
    #[\Override]
    public function register(): void
    {
        $this->app->singleton(SelMaticManager::APP_KEY, fn(): \Modules\Apps\Erp\Selmatic\SelMaticManager => new SelMaticManager());
    }

    /**
     * @return void
     * @throws \Exception
     */
    protected function registerViews()
    {
        $this->app->make('view')->addNamespace(SelMaticManager::APP_KEY, __DIR__ . '/../resources/views');
    }

    /**
     * @return void
     * @throws \Exception
     */
    protected function registerRoutes()
    {
        $this->app->make('router')->group(['middleware' => RouteServiceProvider::siteCpMiddleware(), 'namespace' => $this->namespace], function (Router $route): void {
            $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        });
    }

    /**
     * @return void
     * @throws \Exception
     */
    public function registerTranslations(): void
    {
        $this->loadTranslationsFrom(__DIR__ . '/../resources/lang', SelMaticManager::APP_KEY);
    }

}
