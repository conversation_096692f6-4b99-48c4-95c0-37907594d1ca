<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Selmatic\Http\Controllers;

use App\Exceptions\Error;
use Illuminate\Http\Request;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpSettingsController;
use Modules\Apps\Erp\Selmatic\SelMaticManager;
use Modules\Core\Core\Exceptions\CouldNotConnectToServer;

/**
 * Class SettingsController
 * @package Modules\Apps\Erp\Selmatic\Http\Controllers
 */
class SettingsController extends AbstractErpSettingsController
{
    protected $arrays = [
        'setting_view'
    ];

    protected $booleans = [
        'show_selmatic_id', 'merge_additional_info', 'publish_as_active',
        'publish_as_featured', 'publish_as_new', 'require_shipping',
        'quantity_tracking', 'continue_sell',
    ];

    /**
     * @param SelMaticManager $manager
     * @throws Error
     */
    public function __construct(SelMaticManager $manager)
    {
        parent::__construct($manager);
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function manipulateSettingsRequest(Request $request)
    {
        $request->offsetSet('diff_price_level', $request->has('diff_price_level'));
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function validateSettingsRequest(Request $request): void
    {
        $this->validate($request, [
            'host' => 'required_if:active,1|url',
            'port' => 'required_if:active,1|int|min:1',
            'username' => 'required_if:active,1',
            'password' => 'required_if:active,1',
        ], [
            'host.required_if' => 'Url is required',
            'host.url' => 'Value is not url',
            'port.required_if' => 'Port is required',
            'port.int' => 'Port must be a integer',
            'port.min' => 'Port must be a integer',
            'username.required_if' => 'Username is required',
            'password.required_if' => 'Password is required',
        ]);

        if ($request->input('active')) {
            if (!$this->manager->validateCredentials($request->only($this->manager->getCredentialsFields()))) {
                throw new CouldNotConnectToServer();
            }
        }
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function validateCredentialsRequest(Request $request): void
    {
        $this->validate($request, [
            'host' => 'required|url',
            'port' => 'required|int|min:1',
            'username' => 'required',
            'password' => 'required',
        ], [
            'host.required' => 'Url is required',
            'host.url' => 'Url must be a valid link',
            'port.required' => 'Port is required',
            'port.int' => 'Port must be a integer',
            'port.min' => 'Port must be a integer',
            'username.required' => 'Username is required',
            'password.required' => 'Password is required',
        ]);
    }
}
