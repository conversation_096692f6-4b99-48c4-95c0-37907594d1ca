<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 13.6.2018 г.
 * Time: 09:00 ч.
 */

namespace Modules\Apps\Erp\Selmatic\Jobs;

use App\Helper\YesNo;
use Modules\Apps\Erp\Selmatic\Models\Price;
use Modules\Apps\Erp\Selmatic\Models\Quantity;
use Modules\Apps\Erp\Selmatic\SelMaticManager;
use App\Jobs\Job;
use App\Models\Product\Product;
use App\Models\Queue\SiteQueue;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Modules\Importer\DataFormat\Variant;

class SetQuantitiesAndPrices extends Job
{
    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'import';

    /**@var Collection|int[] $productsIds */
    protected $productsIds;

    /**@var Collection|Product $products */
    protected $products;

    /**@var Collection|Price[] $prices */
    protected $prices;

    /**@var Collection|Quantity[] $quantities */
    protected $quantities;

    /**@var SelMaticManager $manager */
    protected \Modules\Apps\Erp\Selmatic\SelMaticManager $manager;

    /**
     * ImportCategories constructor.
     * @param $productsIds
     */
    public function __construct($productsIds)
    {
        $this->site_id = site('site_id');
        $this->productsIds = $productsIds ?: collect();
        $this->manager = new SelMaticManager();
        $this->prices = collect();
        $this->quantities = collect();
    }

    /**
     * @return array|bool|int
     * @throws \App\Exceptions\Error
     * @throws \App\Exceptions\Fault
     * @throws \Throwable
     */
    public function execute()
    {
        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return static::SITE_MAINTENANCE;
        }

        // move job to another platform
        if (!allowSiteByPlatform()) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', platform(), sitePlatform()));
            return static::WRONG_PLATFORM;
        }

        $this->info('Begin importing SelMatic prices and quantities');

        if ($this->manager->isActive()) {
            $this->products = !empty($this->productsIds) ? Product::whereIn('id', $this->productsIds)->get() : collect();
            if ($this->products->isEmpty()) {
                $this->warning('No products');
                return static::DESTROY;
            }

            $ids = $this->products->map(fn(Product $product): string => \Illuminate\Support\Facades\DB::connection()->getPdo()->quote(Arr::last(explode('-', $product->app_import))));

            $this->prices = Price::whereRaw('`external_id` IN (' . $ids->implode(',') . ')')
                ->get()->keyBy('external_id');
            $this->quantities = Quantity::whereRaw('`external_item_ref` IN (' . $ids->implode(',') . ')')
                ->get()->keyBy('external_item_ref');

            $this->setData();
        } else {
            $this->error('App is not active');
        }

        return static::DESTROY;
    }

    /**
     * @throws \App\Exceptions\Error
     * @throws \Throwable
     */
    protected function setData()
    {
        $discountId = $this->manager->getSetting('discount_id');

        $variants = [];

        /**@var Product $product */
        foreach ($this->products as $product) {
            $importData = explode('-', (string) $product->app_import);
            $quantityData = $this->quantities->get($importData[1]);
            $quantity = null;

            if (!$quantityData) {
                $quantity = $product->tracking == YesNo::True ? 0 : null;
            } else {
                $quantity = $quantityData->quantity ?? null;
            }

            $variant = [
                'compare' => 'id',
                'id' => $product->default_variant_id,
                'quantity' => $quantity,
                'discount_id' => $discountId,
                'updates' => [
                    'quantity' => true,
                ]
            ];

            if ($pricesData = $this->prices->get($importData[1])) {
                $variant['price'] = $pricesData->price ?? null;
                $variant['updates']['price'] = true;

                if ($variant['discount_id'] && $pricesData->discounted_price > 0) {
                    $variant['discount'] = [
                        'price' => $pricesData->discounted_price,
                    ];
                }
            }

            $variants[] = new Variant($variant);
        }

        $variants = array_filter(array_map(fn($variant) => $this->manager->getApp()->records()->create([
                'type' => 'variant',
                'data' => $variant->toArray()
            ])->id ?? null, $variants));

        if ($variants) {
            SiteQueue::executeQueueTaskNew([
                'job' => 'importer',
                'parameters' => ['records' => $variants],
//                'priority' => $this->manager->getPriority()
            ]);
        }

    }

}
