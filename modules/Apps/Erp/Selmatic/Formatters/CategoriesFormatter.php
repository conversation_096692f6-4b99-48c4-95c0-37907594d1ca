<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: user
 * Date: 5/16/2018
 * Time: 1:58 PM
 */

namespace Modules\Apps\Erp\Selmatic\Formatters;

use App\Helper\Import\AbstractErpFormatter;
use Modules\Apps\Erp\Selmatic\Client;
use Modules\Apps\Erp\Selmatic\Models\TemporaryCategories;
use Modules\Apps\Erp\Selmatic\SelMaticManager;
use App\Traits\UrlHandleConsole;

class CategoriesFormatter extends AbstractErpFormatter
{
    use UrlHandleConsole;
    /**
     * @var SelMaticManager
     */
    protected $manager;

    protected \Modules\Apps\Erp\Selmatic\Client $client;

    /**
     * ProductsFormatter constructor.
     * @param SelMaticManager $manager
     * @throws \App\Exceptions\Error
     */
    public function __construct(SelMaticManager $manager)
    {
        $this->manager = $manager;
        $settings = $this->manager->getSettings();

        $this->client = new Client(
            $settings->get('setting_view'),
            $settings->get('host'),
            $settings->get('port'),
            $settings->get('username'),
            $settings->get('password')
        );
    }

    /**
     * @return int
     * @throws \App\Exceptions\Error
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function addCategories(): int
    {
        $categories = $this->client->getCategories();
        //   $chunkedCategorys = array_chunk($categories, 50);
        $i = 0;
        foreach ($categories as $cat) {
            if (TemporaryCategories::where('external_id', $cat['ID'])->count() == 0) {
                $import_category = [
                    'external_id' => $cat['ID'],
                    'category_name' => $cat['CATEGORY_NAME$1'],
                    'external_parent_id' => $cat['CATEGORY_PARENT_REF'],
                ];
                TemporaryCategories::create($import_category);
                $i++;
            }
        }

        return $i;
    }

    /**
     * @param null|TemporaryCategories $category_data
     * @param mixed $categorys
     * @return mixed
     */
    public static function addCategoryToSite(?TemporaryCategories $category_data, $categorys = ''): mixed
    {
        if ($category_data->external_parent_id != null) {
            $parent = TemporaryCategories::where('external_id', $category_data->external_parent_id)->first();
            if ($parent) {
                $categorys = self::addCategoryToSite($parent, $parent->category_name . '/' . $categorys);
            }
        }

        return $categorys;
    }
}
