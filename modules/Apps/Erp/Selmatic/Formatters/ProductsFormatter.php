<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: user
 * Date: 5/16/2018
 * Time: 1:58 PM
 */

namespace Modules\Apps\Erp\Selmatic\Formatters;

use App\Helper\Import\AbstractErpFormatter;
use Masbug\Flysystem\GoogleDriveAdapter;
use Modules\Apps\Erp\Selmatic\Client;
use Modules\Apps\Erp\Selmatic\Models\CustomAttribute;
use Modules\Apps\Erp\Selmatic\Models\DynamicProperty;
use Modules\Apps\Erp\Selmatic\Models\DynamicPropertyValue;
use Modules\Apps\Erp\Selmatic\Models\Image;
use Modules\Apps\Erp\Selmatic\Models\ItemToCategory;
use Modules\Apps\Erp\Selmatic\Models\Price;
use Modules\Apps\Erp\Selmatic\Models\Quantity;
use Modules\Apps\Erp\Selmatic\Models\TemporaryCategories;
use Modules\Apps\Erp\Selmatic\Models\Vendor;
use Modules\Apps\Erp\Selmatic\SelMaticManager;
use Modules\Importer\Models\HistoryImportLogTask;
use App\Models\Product\Variant as ProductVariant;
use App\Models\Queue\SiteQueue;
use App\Models\Router\Exceptions;
use App\Models\System\ExternalMetaData;
use App\Models\Product\Product as ProductModel;
use Illuminate\Support\Collection;
use Modules\Importer\DataFormat\Product;
use Modules\Importer\DataFormat\Variant;
use League\Flysystem\Filesystem;

class ProductsFormatter extends AbstractErpFormatter
{
    /**
     * @var array
     */
    public $forDelete = [];

    /**
     * @var SelMaticManager
     */
    protected $manager;

    /**
     * @var Client
     */
    public $client;

    /**
     * @var \Google_Client
     */
    public $googleClient;

    /**
     * @var \Google_Service_Drive
     */
    public $googleService;

    /**
     * @var GoogleDriveAdapter
     */
    public $googleAdapter;

    public $settings;

    /**
     * @var Filesystem
     */
    public $googleFilesystem;

    /**
     * @var Vendor[]|Collection
     */
    protected $vendors;

    /**
     * @var ItemToCategory[]|Collection
     */
    protected $itemsToCategories;

    /**
     * @var Quantity[]|Collection
     */
    protected $quantities;

    /**
     * @var DynamicProperty[]|Collection
     */
    protected $dynamicAttributes;

    /**
     * @var DynamicPropertyValue[]|Collection
     */
    protected $dynamicAttributesValues;

    /**
     * @var CustomAttribute[]|Collection
     */
    protected $customAttributes;

    /**
     * @var ExternalMetaData[]|Collection
     */
    protected $savedCategoriesMeta;

    /**
     * @var Image[]|Collection
     */
    protected $images;

    /**
     * @var HistoryImportLogTask|null $history
     */
    public $history;

    public const DEFAULT_COMPARE_VALUE = 'sku';

    /**
     * ProductsFormatter constructor.
     * @param SelMaticManager $manager
     * @throws \App\Exceptions\Error|\Exception
     */
    public function __construct(SelMaticManager $manager, ?HistoryImportLogTask $history = null)
    {
        $this->manager = $manager;
        if (Quantity::count() > 0 && Price::count() > 0) {
            $settings = $this->manager->getSettings();
            $this->settings = $settings;
            $this->client = new Client(
                $settings->get('setting_view'),
                $settings->get('host'),
                $settings->get('port'),
                $settings->get('username'),
                $settings->get('password')
            );

            if ($settings->get('images_path_id')) {
                $this->googleClient = new \Google_Client([
                    'client_id' => config('google_drive.client_id'),
                    'client_secret' => config('google_drive.client_secret')
                ]);

                $this->googleClient->refreshToken(config('google_drive.refresh_token'));
                $this->googleService = new \Google_Service_Drive($this->googleClient);
                $this->googleAdapter = new GoogleDriveAdapter($this->googleService, null, [
                    'sharedFolderId' => $this->manager->getSetting('images_path_id'),
                    'useDisplayPaths' => false,
                ]);
                $this->googleFilesystem = new Filesystem($this->googleAdapter);
            }
        }

        $this->history = $history;
    }

    /**
     * @param $products
     * @return int
     * @throws \App\Exceptions\Error
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Throwable
     */
    public function addProducts($products): int
    {
        $allIdss = collect(!empty($products) ? $this->formatSelMaticProducts($products) : [])
            ->filter();
        $allIds = $allIdss->chunk(50);
        foreach ($allIds as $ids) {
            $this->manager->getApp()->records()->whereIn('id', $ids)->update([
                'job_id' => SiteQueue::executeQueueTask('importer', ['records' => $ids])
            ]);
        }

        return $allIdss->count();
    }

    /**
     * @param $products
     * @return array
     * @throws \App\Exceptions\Error
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Throwable
     */
    protected function formatSelMaticProducts($products): array
    {
        $this->loadExternalData();

        $allIds = [];
        foreach ($products as $product) {
            if ($product['OP'] == 'D') {
                $this->forDelete[] = SelMaticManager::APP_KEY . '-' . $product['ID'];
            } else {
                try {
                    $record = $this->manager->getApp()->records()->create([
                        'type' => 'product',
                        'data' => $this->format($product)->toArray()
                    ]);

                    if ($record->id ?? null) {
                        optional($this->history)->createFromGlobalRecord($record);

                        $allIds[] = $record->id;
                    }

                } catch (\Exception $e) {
                    Exceptions::createFromThrowable($e, 'Selmatic import error for product: ' . $product['ID'] . ' ' . $product['ITEM_NAME$1']);

                    continue;
                }
            }
        }

        return $allIds;
    }

    /**
     * @throws \App\Exceptions\Error
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    protected function loadExternalData()
    {
        $this->dynamicAttributes = DynamicProperty::get()->keyBy('external_id');
        $this->dynamicAttributesValues = DynamicPropertyValue::get()->groupBy('external_item_ref');
        $this->customAttributes = CustomAttribute::get()->keyBy('external_id');
        $this->itemsToCategories = ItemToCategory::get()->groupBy('external_item_ref');
        $this->vendors = Vendor::get()->keyBy('external_id');

//        $this->savedCategoriesMeta = ExternalMetaData::whereIntegration(SelMaticManager::APP_KEY)
//            ->where('record_type', 'category')
//            ->with('record')
//            ->get()
//            ->keyBy('external_record_key');

        if ($this->manager->getSetting('images_path_id')) {
            $this->images = Image::get()->keyBy('external_item_id');
        }
    }

    /**
     * @param $selmaticProduct
     * @return Product
     * @throws \App\Exceptions\Error
     */
    protected function format($selmaticProduct): \Modules\Importer\DataFormat\Product
    {
        $settings = $this->manager->getSettings();

        $shortDescription = '';
        if ($settings->get('show_selmatic_id')) {
            $shortDescription .= sprintf(__('selmatic.product_selmatic_id_%1$s'), $selmaticProduct['ID']) . PHP_EOL;
        }

        $shortDescription = $selmaticProduct['SHORT_DESCRIPTION$1'];
        if ($settings->get('merge_additional_info')) {
            $shortDescription .= $selmaticProduct['COMPONENT$1'];
        }

        $name = !empty($selmaticProduct['ITEM_NAME$1']) ? $selmaticProduct['ITEM_NAME$1'] : $selmaticProduct['ID'];
        $currentParams = [
            'name' => $name,
            'url_handle' => $name,
            'short_description' => iconv("UTF-8", "UTF-8//IGNORE", (string)$shortDescription),
            'description' => $this->isBase64($selmaticProduct['LONG_DESCRIPTION$1']) ? base64_decode((string)$selmaticProduct['LONG_DESCRIPTION$1']) : $selmaticProduct['LONG_DESCRIPTION$1'],
            'app_import' => SelMaticManager::APP_KEY . '-' . $selmaticProduct['ID'],
            'status' => empty($selmaticProduct['ITEM_STATUS_REF']) ? 0 : 1,
        ];
        if (!empty($this->settings['compare_by'])) {
            if ($this->settings['compare_by'] == 'sku') {
                $product_id = ProductVariant::whereSku($selmaticProduct['SKU'])->value('item_id');
            }

            if ($this->settings['compare_by'] == 'barcode') {
                $product_id = ProductVariant::whereBarcode($selmaticProduct['SKU'])->value('item_id');
            }

            if ($product_id ?? null) {
                $currentParams = array_merge($currentParams, ['id' => $product_id]);
            }
        }

        $variant = $this->formatVariant($selmaticProduct);
        $commonParams = $this->getProductCommonAttributes($settings, [$variant]);
        $formatted = new Product(array_merge($commonParams, $currentParams));
        $variant->setDiscount($this->setDiscount($selmaticProduct['ID']));
        $formatted->setVariant($variant);
        $this->setVendor($selmaticProduct, $formatted);
        $this->setStockStatus($selmaticProduct, $formatted);
        $this->setVizabilityStatus($selmaticProduct, $formatted);
        $this->setCategories($selmaticProduct, $formatted);
        $this->setCategoryProperties($selmaticProduct, $formatted);

        $getProduct = ProductModel::where('app_import', SelMaticManager::APP_KEY . '-' . $selmaticProduct['ID'])->first();

        if (!$getProduct || $getProduct->image_id == null) {
            $this->setImages($selmaticProduct, $formatted);
        }

        if (empty($formatted->getName()) || empty($formatted->getCategoryId())) {
            $formatted->setDraft(true);
        }

        $updates = [
            'variant' => true,
        ];

        foreach ($this->manager->getSetting('updates', []) as $key) {
            $updates[$key] = true;
        }

        $formatted->setUpdates($updates);
        $formatted->setOriginalDataValue($selmaticProduct);
        return $formatted;
    }

    /**
     * @param $s
     * @return bool
     */
    protected function isBase64($s): bool
    {
        // Check if there are valid base64 characters
        if (!preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', (string)$s)) {
            return false;
        }

        // Decode the string in strict mode and check the results
        $decoded = base64_decode((string)$s, true);
        if (false === $decoded) {
            return false;
        }

        // Encode the string again
        if (base64_encode($decoded) != $s) {
            return false;
        }

        return true;
    }

    /**
     * @param $selmaticProduct
     * @return Variant
     */
    protected function formatVariant(array $selmaticProduct): \Modules\Importer\DataFormat\Variant
    {
        if (Quantity::where('external_item_ref', $selmaticProduct['ID'])->first() == null) {
            $qty = null;
        } else {
            $qty = Quantity::where('external_item_ref', $selmaticProduct['ID'])->value('quantity');
        }

        $variantParams = [
            'barcode' => null,
            'sku' => $selmaticProduct['SKU'],
            'price' => Price::where('external_id', $selmaticProduct['ID'])->value('price'),
            'quantity' => $qty,
            'weight' => round(floatval($selmaticProduct['MEASURE1_GROSS_WEIGHT']), 3),
            'external_meta_data_key' => $selmaticProduct['ID'],
            'external_meta_data_integration' => SelMaticManager::APP_KEY,
            'v1' => null,
            'v2' => null,
            'v3' => null,
            'v1_id' => null,
            'v2_id' => null,
            'v3_id' => null,
        ];
        $variant = new Variant($variantParams);
        $variant->setOriginalDataValue($selmaticProduct);
        return $variant;
    }

    /**
     * @param $selmaticProduct
     * @param Product $formatted
     */
    protected function setVendor(array $selmaticProduct, $formatted)
    {
        $vendorData = $this->vendors->get($selmaticProduct['BRAND_REF']);

        if ($vendorData) {
            $formatted->setVendor([
                'import_key' => 'import',
                'id' => trim((string)$vendorData->external_id),
                'name' => trim((string)$vendorData->name)
            ]);
        }
    }

    /**
     * @param $selmaticProduct
     * @param Product $formatted
     */
    protected function setStockStatus($selmaticProduct, $formatted)
    {
        if (!empty($selmaticProduct['ITEM_AVAIL_REF'])) {
            $keyStatus = [
                '1' => 'in_stock',
                '2' => 'out_of_stock'
            ];

            $formatted->setStockStatusType($keyStatus[$selmaticProduct['ITEM_AVAIL_REF']]);
        }
    }

    /**
     * @param $selmaticProduct
     * @param Product $formatted
     */
    protected function setVizabilityStatus($selmaticProduct, $formatted)
    {
        if (!empty($selmaticProduct['ITEM_STATUS_REF'])) {
            switch ($selmaticProduct['ITEM_STATUS_REF']) {
                case '1':
                    $formatted->setStatus(1);
                    $formatted->setIsHidden("1");
                    break;
                case '2':
                    $formatted->setStatus(1);
                    $formatted->setIsHidden("0");
                    break;
                default:
                    $formatted->setStatus(0);
                    $formatted->setIsHidden("0");
            }
        }
    }

    /**
     * @param $selmaticProduct
     * @param Product $formatted
     */

    protected function setCategories($selmaticProduct, Product $formatted)
    {
        if (isset($selmaticProduct['ID'])) {
            $check = $this->itemsToCategories->get($selmaticProduct['ID']);
            if (isset($check) && ($ProductToCategory = $check->first()) != null) {
                $GetTemporaryCategory = TemporaryCategories::where('external_id', $ProductToCategory['external_category_ref'])->first();
                $categorys = CategoriesFormatter::addCategoryToSite($GetTemporaryCategory);
                $category = explode('/', (string)$categorys);
                $formatted->setCategory([
                    'import_key' => 'import',
                    'id' => (string)$ProductToCategory['external_category_ref'],
                    'name' => (string)$GetTemporaryCategory['category_name'],
                    'path' => array_filter($category)
                ]);
            }
        }
    }

    /**
     * @param $selmaticProduct
     * @param Product $formatted
     */
    protected function setCategoryProperties($selmaticProduct, $formatted)
    {
        if ($formatted->issetCategoryId()) {
            $groupedByName = [];
            $properties = $this->dynamicAttributesValues->get($selmaticProduct['ID']);

            if ($properties) {
                foreach ($properties as $property) {
                    if (!empty($property->value) && $attribute = $this->dynamicAttributes->get($property->external_property_ref)) {
                        $groupedByName[$attribute->name][] = $property->value;
                    }
                }
            }

            // Custom properties
            for ($i = 1; $i < 8; $i++) {
                $key = 'UFN' . $i;
                if (!empty($selmaticProduct[$key])) {
                    $customAttribute = $this->customAttributes->get($selmaticProduct[$key]);
                    if ($customAttribute && $customAttribute->external_reference_number == Client::COLOR_ATTRIBUTE_ID) {
                        $groupedByName[__('selmatic.product_color_attribute')][] = $customAttribute->name;
                    }
                }
            }

            $formattedProperties = [];
            foreach ($groupedByName as $name => $values) {
                $formattedProperties[] = [
                    'name' => $name,
                    'values' => $values,
                ];
            }

            $formatted->setCategoryProperties($formattedProperties);
        }
    }

    /**
     * @param $selmaticProduct
     * @param $formatted
     * @throws \App\Exceptions\Error
     */
    protected function setImages(array $selmaticProduct, $formatted)
    {
        if ($this->manager->getSetting('images_path_id')) {
            $this->setGoogleDriveImages($selmaticProduct, $formatted);
        } elseif ($this->manager->getSetting('images_base_url')) {
            $this->setImagesFromFolder($selmaticProduct, $formatted);
        }
    }

    /**
     * @param $selmaticProduct
     * @param $formatted
     * @throws \App\Exceptions\Error
     */
    protected function setImagesFromFolder(array $selmaticProduct, $formatted)
    {
        $baseUrl = $this->manager->getSetting('images_base_url');
        $jsonImages = json_decode(file_get_contents($baseUrl . '/?base_url=' . $baseUrl . '&product_identifier=' . $selmaticProduct['ID']), true);

        if ($jsonImages) {
            $images = [];
            foreach ($jsonImages as $name => $url) {
                if ($name === $selmaticProduct['ID']) {
                    array_unshift($images, $url);
                } else {
                    $images[] = $url;
                }
            }

            $formatted->setImages($images);
        }
    }

    /**
     * @param $selmaticProduct
     * @param Product $formatted
     * @throws \App\Exceptions\Error
     */
    protected function setGoogleDriveImages(array $selmaticProduct, $formatted)
    {
        $folder = $this->images->get($selmaticProduct['ID']);

        if ($folder) {
            $googleImages = $this->googleFilesystem->listContents($folder->folder_path);

            $images = [];
            foreach ($googleImages as $image) {
                usleep(500000);
                if ($image->isFile()) {
                    $meta = $image->extraMetadata();
                    if ($meta['name'] == $selmaticProduct['ID'] . '.' . $meta['extension']) {
                        array_unshift($images, $this->googleAdapter->getUrl($image->path()));
                    } else {
                        $images[] = $this->googleAdapter->getUrl($image->path());
                    }
                }
            }

            $formatted->setImages($images);
        }
    }

    /**
     * @param mixed $SelmaticId
     * @return mixed
     */
    protected function setDiscount($SelmaticId): ?array
    {
        $DiscountPrice = Price::where('external_id', $SelmaticId)->value('discounted_price');
        if ($DiscountPrice != null) {
            $this->manager->getSetting('discount_id');
            $discoint = [
                'price' => $DiscountPrice,
                'id' => $this->manager->getSetting('discount_id'),
                'type' => 'fixed'
            ];
            return $discoint;
        }

        return null;
    }
}
