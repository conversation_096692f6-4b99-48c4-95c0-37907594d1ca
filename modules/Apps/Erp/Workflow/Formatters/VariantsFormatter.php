<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: user
 * Date: 5/16/2018
 * Time: 1:58 PM
 */

namespace Modules\Apps\Erp\Workflow\Formatters;

use App\Helper\Import\AbstractErpFormatter;
use App\Models\Import\ErpImport;
use Modules\Apps\Erp\Workflow\WorkflowManager;

class VariantsFormatter extends AbstractErpFormatter
{
    /**
     * @var WorkflowManager
     */
    protected $manager;

    /**
     * AppController constructor.
     * @param WorkflowManager $manager
     */
    public function __construct(WorkflowManager $manager)
    {
        $this->manager = $manager;
    }

    /**
     * @return bool
     */
    public function formatERPVariants(): int
    {
        $content = $this->manager->getFileContent($this->manager->getSettings(), 'quantities_file_path');
        $params = !empty($content) ? $this->formatWorkflowVariants($content) : [];

        $params_count = 0;
        if (!empty($params)) {
            $params = array_chunk($params, 50);
            foreach ($params as $chunked_params) {
                $params_count += count($chunked_params);
                ErpImport::insert($chunked_params);
            }
        }

        return $params_count;
    }

    /**
     * @param array $items
     * @return array
     */
    protected function formatWorkflowVariants($items): array
    {
        $params = [];
        foreach ($items as $item) {
            $formatted_variant = [
                'quantity' => $item[4],
                'price' => $item[5] * 100,
                'shops' => [
                    [
                        'name' => $item[0]
                    ]
                ]
            ];

            $params[] = [
                'source' => $this->manager->getKey(),
                'import_type' => static::VARIANT_UPDATE_TYPE,
                'identifier' => $item[2],
                'data' => json_encode($formatted_variant),
            ];
        }

        return $params;
    }
}
