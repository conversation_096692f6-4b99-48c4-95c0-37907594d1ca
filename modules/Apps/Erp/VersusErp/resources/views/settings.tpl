{include file="./dependency.tpl"}
{include file="{Apps::templatesPath()}includes/app-breadcrumb.tpl" active=__('versus_erp.info.title')}

<div class="wrapper">
    <div class="app-wrapper">
        {include file="{Apps::templatesPath()}includes/app-intro.tpl" title=__('versus_erp.header.settings') text=__('versus_erp.help.settings')}
        {include file="{Apps::templatesPath()}includes/app-icons.tpl" app_icon='versus-erp.png'}
    </div>

    <div class="container-small">
        {if $manager->getPriorityUrl() || $manager->getIntervalUrl()}
            <div class="box">
                <div class="box-section">
                    {$manager->getPriorityUrl() nofilter}
                    {$manager->getIntervalUrl() nofilter}
                </div>
            </div>
        {/if}
        <div class="box">
            <div class="box-section">
                <form action="{route('apps.versus_erp.settings')}" method="post" id="appSettings" role="form">
                    <div class="form-group">
                        <div class="stack">
                            <div class="stack-main">
                                <label class="form-control-check">{t}apps.label.enabled{/t}</label>

                            </div>

                            <div class="stack-addon">
                                <input name="active" type="checkbox" class="switch"
                                       value="1"{if $active} checked="checked"{/if} />
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-xs-6">
                            <div class="form-control-box">
                                <div class="form-control-box-inner">
                                    <label class="form-control-check">{t}versus_erp.label.url{/t}</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-xs-6">
                            <input id=url" type="text" class="form-control" placeholder="" name="url"
                                   value="{if isset($settings['url'])}{$settings['url']}{/if}"/>
                            <p class="help-block">{__('versus_erp.label.store_url.help') nofilter}</p>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-xs-6">
                            <div class="form-control-box">
                                <div class="form-control-box-inner">
                                    <label class="form-control-check">{t}versus_erp.label.url_images{/t}</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-xs-6">
                            <input id=pictures" type="text" class="form-control" placeholder="" name="pictures"
                                   value="{if isset($settings['pictures'])}{$settings['pictures']}{/if}"/>
                            <p class="help-block">{__('versus_erp.label.pictures_url.help') nofilter}</p>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-xs-6">
                            <div class="form-control-box">
                                <div class="form-control-box-inner">
                                    <label class="form-control-check">{t}facebook.info.settings.choose_discount{/t}</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-xs-6">
                            <select name="discount_id" id="discount-id" class="form-control select2me"
                                    data-no-input="true">
                                <option value="" selected="selected">-- {t}filter.select{/t} --</option>
                                {foreach $discounts as $discount}
                                    <option value="{$discount->id}"
                                            {if $settings.discount_id|default == $discount->id}selected="selected"{/if}>
                                        {$discount->name}
                                    </option>
                                {/foreach}
                            </select>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-xs-12 pull-right text-right">
                            <a href="{route('admin.discounts.add', 'fixed')}" target="_blank">
                                {t}facebook.btn.info.generate_discount_with_code{/t} <i class="fa fa-external-link"
                                                                                        aria-hidden="true"></i>
                            </a>
                        </div>
                    </div>


                    <div class="row form-group">
                        <div class="col-xs-6">
                            <div class="form-control-box">
                                <div class="form-control-box-inner">
                                    <label class="form-control-check">{t}versus_erp.label.compare{/t}</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-xs-6">
                            <select name="compare_key" class="form-control select2me" data-no-input="true">
                                <option value="sku" {if $settings.compare_key|default == 'sku'}selected="selected"{/if}>{t}versus_erp.compare.sku{/t}</option>
                                <option value="barcode" {if $settings.compare_key|default == 'barcode'}selected="selected"{/if}>{t}versus_erp.compare.barcode{/t}</option>
                            </select>
                            <p class="help-block">{__('versus_erp.label.compare.help') nofilter}</p>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-xs-6">
                            <div class="form-control-box">
                                <div class="form-control-box-inner">
                                    <label class="form-control-check">{t}versus_erp.label.method{/t}</label>
                                </div>
                            </div>
                        </div>

                        <div class="col-xs-6">
                            <select name="method" class="form-control select2me" data-no-input="true">
                                <option value="import" {if $settings.method|default == 'import'}selected="selected"{/if}>{t}versus_erp.method.import{/t}</option>
                                <option value="sync" {if $settings.method|default == 'sync'}selected="selected"{/if}>{t}versus_erp.method.sync{/t}</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group text-right">
                        {include file="imports/includes/status_button.tpl" route={route('apps.versus_erp.status')}}
                        <a href="{route('apps.versus_erp.log')}"
                           class="btn btn-primary">{t}import.history_import{/t}</a>
                        <button type="submit" class="btn btn-primary">{t}versus_erp.button.settings{/t}</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="box">
            <div class="box-section">
                <div class="row">
                    <div class="col-md-12 form-group">
                        <form action="{route('apps.versus_erp.sync')}" method="post" id="appSync" role="form">
                            <div class="col-md-8" style="margin-top: 10px;">
                                <span>{t}versus_erp.info.sync{/t}</span>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary btn-block">
                                    {t}versus_erp.button.sync{/t}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>