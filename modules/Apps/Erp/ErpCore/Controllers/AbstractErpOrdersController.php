<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\ErpCore\Controllers;

use App\Exceptions\Error;
use App\Models\Order\Order;
use App\Models\Router\Exceptions;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Modules\Apps\Erp\ErpCore\Filters\ErpOrdersFilter;
use Throwable;

abstract class AbstractErpOrdersController extends AbstractErpController
{
    /**
     * @return JsonResponse
     * @throws Error
     */
    public function orders()
    {
        $model = $this->getModel();

        $records = $this->getRecords($model, new ErpOrdersFilter($this->manager));

        $records->transform(fn(Order $order) => array_merge($order->getAttributes(), [
            'customer_full_name' => $order->customer_full_name,
            'date_added' => $order->date_added->toIso8601String(),
            'updated_at' => $order->updated_at->toIso8601String(),
            'price_products_subtotal' => moneyFloat($order->price_products_subtotal, $order->getCurrency(), $order->getLanguage()),
            'price_subtotal' => moneyFloat($order->price_subtotal, $order->getCurrency(), $order->getLanguage()),
            'price_total' => moneyFloat($order->price_total, $order->getCurrency(), $order->getLanguage()),
            'products' => [],
        ], $this->getErpInformation($order)));

        return response()->json($records);
    }

    /**
     * @param int $id
     * @return JsonResponse
     */
    public function sendOrder($id): JsonResponse
    {
        $order = $this->getModel()->allOrderData()->find($id);
        if (!$order) {
            return response()->json([], 404);
        }

        return $this->sendOrderToErp($order);
    }

    /**
     * @param int $id
     * @return JsonResponse
     * @throws Error
     */
    public function ignoreOrder($id): JsonResponse
    {
        $order = $this->getModel()->allOrderData()->find($id);
        if (!$order) {
            return response()->json([], 404);
        }

        return $this->ignoreOrderForErp($order);
    }

    /**
     * @param Order $order
     * @return array
     */
    protected function getErpInformation(Order $order): array
    {
        return [];
    }

    /**
     * @param Order $order
     * @return JsonResponse
     */
    protected function sendOrderToErp(Order $order): JsonResponse
    {
        return response()->json([]);
    }

    /**
     * @param Order $order
     * @return JsonResponse
     * @throws Error
     */
    protected function ignoreOrderForErp(Order $order): JsonResponse
    {
        try {
            $order->updateMeta([
                'send_erp_ignore_' . $this->manager->getKey() => $this->manager->getKey(),
                'send_erp_ignore_' . $this->manager->getKey() . '_date' => Carbon::now()->toIso8601String(),
            ]);

            return response()->json([]);
        } catch (Throwable $throwable) {
            // If an error occurs, log it and return a 400 response with an error message
            Exceptions::createFromThrowable($throwable, $this->manager->getKey() . ' ignore order');
            return response()->json([
                'message' => 'Unexpected error. Please try again later',
            ], 400);
        }
    }

    /**
     * @return Builder
     */
    abstract protected function getModel(): Builder;
}
