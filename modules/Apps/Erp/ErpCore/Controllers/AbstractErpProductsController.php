<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\ErpCore\Controllers;

use Modules\Importer\Models\HistoryImportLogRecord;
use App\Models\Product\Product;
use App\Models\Product\Variant;
use App\Models\System\ExternalMetaData;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\Apps\Erp\ErpCore\Filters\ErpProductsFilterByAppImport;
use Modules\Apps\Erp\ErpCore\Filters\ErpProductsFilterByMeta;
use Modules\Core\Core\Helpers\CustomAwarePaginator;
use Modules\Core\Core\Traits\GridFilters;
use Throwable;
use Modules\Apps\Erp\ErpCore\Requests\UpdateExternalIdRequest;

abstract class AbstractErpProductsController extends AbstractErpController
{
    use GridFilters;

    protected $MAPPING_TYPE = 'meta'; //meta, app_import

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    public function products(Request $request): JsonResponse
    {
        $records = new CustomAwarePaginator(new LengthAwarePaginator(collect(), 0, 25), []);
        if ($this->MAPPING_TYPE == 'meta') {
            $records = $this->getProductsFromMeta($request);
        } elseif ($this->MAPPING_TYPE == 'app_import') {
            $records = $this->getProductsFromAppImport($request);
        }

        $records->setMeta('mapping_type', $this->MAPPING_TYPE);


        return response()->json($records);
    }

    /**
     * @param Illuminate\Http\Request $request
     * @param mixed $id
     * @return mixed
     */
    public function productDelete(Request $request, $id): JsonResponse
    {
        if ($this->MAPPING_TYPE == 'meta') {
            return $this->productDeleteFromMeta($request, $id);
        } elseif ($this->MAPPING_TYPE == 'app_import') {
            return $this->productDeleteFromAppImport($request, $id);
        }

        return response()->json([]);
    }

    /* ============================================================================ FROM META ============================================================================ */
    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    protected function getProductsFromMeta(Request $request): CustomAwarePaginator
    {
        $model = $this->getProductsListModelByExternalMeta();

        $records = $this->getRecords($model, new ErpProductsFilterByMeta($this->manager));

        $historyFilter = $records->map(fn(Variant $variant): array => [
            $variant,
            (new Product())->newFromBuilder(['id' => $variant->item_id]),
        ])->collapse();
        $app = $this->manager->getApp();
        $historyData = HistoryImportLogRecord::getLastImportInfo($app, $historyFilter)->keyBy(fn(array $data): string => sprintf('%s-%d', $data['relation_type'], $data['relation_id']));

        $app_id = $app->id;
        $records->transform(function (Variant $variant) use ($historyData, $app_id): array {
            $infos = [];
            foreach (['product', Variant::class] as $key) {
                $infos[] = $historyData->get(sprintf('%s-%d', $key, $key == 'product' ? $variant->item_id : $variant->id));
            }

            $infos = collect($infos)->filter()->sortby('updated_at', SORT_DESC);
            if ($info = $infos->first()) {
                $variant->method = $info['method'];
                $variant->updated_at = $info['updated_at'];
            }

            $variant->timestamps = true;

            $info = $infos->first();
            return [
                'image' => $variant->item->getImage('150x150'),
                'name' => implode(', ', array_merge([$variant->item->name], array_filter($variant->only(['v1', 'v2', 'v3'])))),
                'item_id' => $variant->item_id,
                'id' => $variant->id,
                'sku' => $variant->sku,
                'barcode' => $variant->barcode,
                'url' => $variant->item->url(),
                'external_record_key' => $variant->external_record_key,
                'deleteId' => $variant->external_record_key,
                'updated_at' => optional($info['updated_at'] ?? null)->toIso8601String(),
                'history' => $info ? [
                    'app_id' => $app_id,
                    'type' => ($info['relation_type'] == Variant::class ? 'variant' : $info['relation_type']),
                    'id' => $info['relation_id']
                ] : null,
                'action' => $info['method'] ?? null,
            ];
        });

        return $records;
    }

    /**
     * @param Illuminate\Http\Request $request
     * @param mixed $id
     * @return mixed
     */
    protected function productDeleteFromMeta(Request $request, $id): JsonResponse
    {
        try {
            $model = $this->getProductsListModelByExternalMeta()
                ->where('meta_data.external_record_key', $id)
                ->select('meta_data.id as external_record_id')
                ->firstOrFail();

        } catch (Throwable) {
            return response()->json([], 404);
        }

        ExternalMetaData::whereKey($model->external_record_id)->delete();

        return response()->json([]);
    }

    /* ============================================================================ FROM META ============================================================================ */
    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    protected function getProductsFromAppImport(Request $request): CustomAwarePaginator
    {
        $model = $this->getProductsListModelByAppImport();

        $records = $this->getRecords($model, new ErpProductsFilterByAppImport($this->manager));

        $mappings = [
            sprintf('%s-(.*)', $this->manager->getAppImportKey()),
        ];

        $historyFilter = $records->getCollection();

        $app = $this->manager->getApp();
        $historyData = HistoryImportLogRecord::getLastImportInfo($app, $historyFilter)->keyBy(fn(array $data): string => sprintf('%s-%d', $data['relation_type'], $data['relation_id']));

        $app_id = $app->id;
        $records->transform(function (Product $product) use ($historyData, $app_id, $mappings): array {
            $infos[] = $historyData->get(sprintf('product-%d', $product->id));

            $infos = collect($infos)->filter()->sortby('updated_at', SORT_DESC);
            if ($info = $infos->first()) {
                $product->method = $info['method'];
                $product->updated_at = $info['updated_at'];
            }

            $product->timestamps = true;

            return [
                'image' => $product->getImage('150x150'),
                'name' => $product->name,
                'item_id' => $product->id,
                'id' => $product->default_variant_id,
                'sku' => $product->variant->sku ?? null,
                'barcode' => $product->variant->barcode ?? null,
                'url' => $product->url(),
                'external_record_key' => preg_replace_callback(sprintf('#^%s$#', implode('|', $mappings)), fn($match): string => $match[2] ?? $match[1] ?? $match[0], $product->app_import),
                'deleteId' => $product->app_import,
                'updated_at' => optional($product->updated_at)->toIso8601String(),
                'history' => $product->updated_at ? [
                    'app_id' => $app_id,
                    'type' => ($info['relation_type'] == Variant::class ? 'variant' : $info['relation_type']),
                    'id' => $info['relation_id']
                ] : null,
                'action' => $product->method ?: 'missing',
            ];
        });

        return $records;
    }

    /**
     * @param Illuminate\Http\Request $request
     * @param mixed $id
     * @return mixed
     */
    protected function productDeleteFromAppImport(Request $request, $id): JsonResponse
    {
        try {
            $model = $this->getProductsListModelByAppImport()
                ->where('app_import', $id)->firstOrFail();
        } catch (Throwable) {
            return response()->json([], 404);
        }

        Product::whereKey($model->id)->update([
            'app_import' => null,
        ]);

        return response()->json([]);
    }

    /**
     * Update the external ID of a product or variant.
     * @param UpdateExternalIdRequest $request
     * @return void
     * @throws \App\Exceptions\Error
     */
    public function updateExternalId(UpdateExternalIdRequest $request)
    {
        $id = $request->input('id');
        $external_id = $request->input('external_id');
        if ($this->MAPPING_TYPE == 'meta') {
            $this->variantUpdateFromAppImport($id, $external_id);
        } elseif ($this->MAPPING_TYPE == 'app_import') {
            $this->productUpdateFromMeta($id, $external_id);
        }
    }

    /**
     * @param int $id
     * @param string $external_id
     * @return void
     * @throws \App\Exceptions\Error
     */
    protected function variantUpdateFromAppImport(int $id, string $external_id)
    {
        $variant = Variant::findOrFail($id);
        $variant->external_meta_data()->updateOrCreate(
            [
                'integration' => $this->manager->getKey(),
            ], [
                'external_record_key' => $external_id,
            ]
        );
    }

    /**
     * @param int $id
     * @param string $external_id
     * @return void
     * @throws \App\Exceptions\Error
     */
    protected function productUpdateFromMeta(int $id, string $external_id)
    {
        $product = Product::findOrFail($id);
        $product->update([
            'app_import' => sprintf('%s-%s', $this->manager->getAppImportKey(), $external_id),
        ]);
    }


    /* ============================================================================ HELP METHODS ============================================================================ */

    /**
     * @return Variant
     */
    protected function getProductsListModelByExternalMeta()
    {
        return Variant::withoutGlobalScopes()->join('meta_data', function (JoinClause $clause): void {
            $clause->on('meta_data.record_id', 'products_variants.id')
                ->where('meta_data.record_type', Variant::class)
                ->where('meta_data.integration', $this->manager->getKey());
        })->select([
            'products_variants.*',
            'meta_data.external_record_key',
        ])->with(['item' => function ($query): void {
            $query->withoutGlobalScopes()
                ->with('image');
        }]);
    }

    /**
     * @return Product
     */
    protected function getProductsListModelByAppImport()
    {
        $appKey = $this->manager->getAppImportKey();
        return Product::withoutGlobalScopes()->join('products_variants', function (JoinClause $clause): void {
            $clause->on('products.id', 'products_variants.item_id');
        })->select([
            'products.*',
        ])->with(['image', 'variant'])->where(function ($query) use ($appKey): void {
            $query->where('products.app_import', 'like', sprintf('%s-%%', $appKey));
        })->groupBy(['products.id']);
    }
}
