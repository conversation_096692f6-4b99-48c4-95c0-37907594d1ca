<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\ErpCore\Controllers;

use App\Exceptions\Error;
use App\Models\Apps\ApplicationHistory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Modules\Core\Helpers\VueJs\AbstractBoxField;
use Modules\Core\Helpers\VueJs\BoxSettings;
use Throwable;

abstract class AbstractErpSettingsController extends AbstractErpController
{
    public function resetImport(): JsonResponse
    {
        if ($this->manager->supportResetImport()) {
            $this->manager->resetImport();
        }

        return response()->json([]);
    }

    #[\Override]
    protected function __settings(): array
    {
        //        return [
        //            'settings' => array_merge(parent::__settings(), [
        //                'action' => $this->manager->getAction(),
        //            ]),
        //            'working' => $this->manager->isWorking(),
        //        ];
        return array_merge(parent::__settings(), [
            'action' => $this->manager->getAction(),
            'working' => $this->manager->isWorking(),
        ]);
    }

    #[\Override]
    protected function getBooleans(): array
    {
        return array_values(array_unique(array_merge(parent::getBooleans(), collect($this->manager->getAdditionalSettings()->getBoxes())->map(fn(BoxSettings $settings) => collect($settings->getFields())->filter(fn(AbstractBoxField $field): bool => $field->getType() === 'switch')->map(fn(AbstractBoxField $field): string => $field->getKey())->values())->collapse()->all())));
    }

    #[\Override]
    protected function getArrays(): array
    {
        return array_values(array_unique(array_merge(parent::getArrays(), collect($this->manager->getAdditionalSettings()->getBoxes())->map(fn(BoxSettings $settings) => collect($settings->getFields())->filter(fn(AbstractBoxField $field): bool => $field->getType() === 'select' && $field->isMultiple() && !$field->isZeroValue())->map(fn(AbstractBoxField $field): string => $field->getKey())->values())->collapse()->all())));
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Error
     * @throws Throwable
     */
    public function validateCredentials(Request $request): JsonResponse
    {
        if ($this->manager->supportsValidateCredentials()) {
            if ($request->isMethod('POST')) {
                $this->validateCredentialsRequest($request);

                try {
                    $validate = $this->manager->validateCredentials($request->all());
                } catch (Throwable $e) {
                    return $this->validateCredentialsFieldResponse();
                }
            }

            if ($request->isMethod('GET')) {
                $validate = $this->manager->validateCredentials();
            }

            if ($request->isMethod('POST') && ($validate ?? false)) {
                $this->manager->setWorking(false);
                $this->updateCredentials($request);

                $this->postValidCredentials($request);

                return response()->json([
                    'isValidCredentials' => true,
                    'is_configured' => $this->manager->isConfigured(),
                    'settings' => $this->__settings(),
                    'required' => [
                        'categoryMapping' => $this->manager->requiredCategoryMapping(),
                        'fetchDataAfterValidateCredentials' => $this->manager->requiredFetchDataAfterValidateCredentials(),
                    ],
                    'meta' => [
                        'hasMappedCategories' => $this->manager->requiredCategoryMapping() && $this->manager->hasMappedCategories(),
                        'fetchDataAfterValidateCredentials' => $this->manager->requiredFetchDataAfterValidateCredentials() ? $this->manager->getFetchDataAfterValidateCredentials() : null,
                    ]
                ]);
            } else {
                return $this->validateCredentialsFieldResponse();
            }
        }

        return response()->json(true);
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    protected function updateCredentials(Request $request): void
    {
        $this->writeCredentials($request->only($this->manager->getCredentialsFields()));
    }

    /**
     * @param array $credentials
     * @return mixed
     */
    protected function writeCredentials(array $credentials): void
    {
        $this->manager->updateSettings($credentials);

        try {
            ApplicationHistory::updateOrCreate([
                'group' => 'erp',
                'key' => $this->manager->getKey(),
                'type' => 'credentials',
                'site_id' => site('site_id'),
            ], [
                'group' => 'erp',
                'key' => $this->manager->getKey(),
                'type' => 'credentials',
                'value' => $credentials
            ]);
        } catch (Throwable) {

        }
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    protected function postValidCredentials(Request $request): void
    {
        //
    }

    /**
     * @return JsonResponse
     * @throws Throwable
     */
    protected function validateCredentialsFieldResponse(): JsonResponse
    {
        $fields = $this->manager->getCredentialsFields();
        if (empty($fields) && Arr::first($fields)) {
            return response()->json('Incorrect credentials info', 401);
        }

        $field = Arr::first($fields);
        return response()->json([
            'message' => null,
            'errors' => [
                $field => ['Incorrect credentials info']
            ]
        ], 422);
    }
}
