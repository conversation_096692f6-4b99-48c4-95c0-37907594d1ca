<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\ErpCore\Filters;

use App\Models\Product\Product;
use Closure;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Modules\Core\Core\Helpers\AbstractGridFilter;
use Modules\Core\Core\Helpers\Grid;
use Modules\Core\Core\Helpers\GridFilterRules;
use Modules\Core\Core\Rules\ArrayKeysIn;
use Modules\Core\Core\Traits\GridFilter\QueryFilter;
use Modules\Importer\Models\HistoryImportLogRecord;

abstract class AbstractErpProductsFilter extends AbstractGridFilter
{
    use QueryFilter;

    protected $queryFilterColumns = [
        'item:name',
        'sku',
        'barcode'
    ];

    /**
     * @param \Modules\Apps\Abstractions\Managers\AbstractImportsExportsManager $manager
     * @return mixed
     */
    public function __construct(protected \Modules\Apps\Abstractions\Managers\AbstractImportsExportsManager $manager)
    {
        parent::__construct();
    }

    /**
     * @param mixed $operator
     * @param mixed $value
     * @return mixed
     */
    public function filterSku($operator, $value): void
    {
        $this->compileFilter($operator, $value, 'sku', 'products_variants.sku');
    }

    /**
     * @param mixed $operator
     * @param mixed $value
     * @return mixed
     */
    public function filterBarcode($operator, $value): void
    {
        $this->compileFilter($operator, $value, 'barcode', 'products_variants.barcode');
    }

    /**
     * @param mixed $operator
     * @param mixed $value
     * @return mixed
     */
    public function filterAction($operator, $value): void
    {
        $filter = HistoryImportLogRecord::getLastImportInfoForFilter($this->manager->getApp(), $value)->where('relation_id', '!==', null);
        if (!$filter || $filter->isEmpty()) {
            $this->setWhere('action', function ($query): void {
                $query->whereRaw('0 = 1');
            });

            $this->setFilters('action', $value);
            return;
        }

        $this->setWhere('action', function ($query) use ($filter, $value): void {
            $filter->groupBy('relation_type')->map->pluck('relation_id')->map(function (Collection $collection, $group) use ($query, $value): void {
                if ($group == 'product') {
                    if($value === 'nothing') {
                        $query->orWhereRaw(sprintf('products_variants.item_id NOT IN (%s)', $collection->implode(',')));
                    } else {
                        $query->orWhereRaw(sprintf('products_variants.item_id IN (%s)', $collection->implode(',')));
                    }
                } else {
                    if($value === 'nothing') {
                        $query->orWhereRaw(sprintf('products_variants.id NOT IN (%s)', $collection->implode(',')));
                    } else {
                        $query->orWhereRaw(sprintf('products_variants.id IN (%s)', $collection->implode(',')));
                    }
                }
            });
        });

        $this->setFilters('action', $value);
    }

    /**
     * Filter by product ID.
     *
     * This method filters bundle products by the ID of the associated product.
     *
     * @param string $operator The operator for the filter (e.g., '=', '<>', etc.).
     * @param mixed $value The ID of the product to filter by.
     */
    public function filterProduct($operator, $value): void
    {
        $this->setWhere('product', function ($query) use ($operator, $value): void {
            /** @var Builder $query */
            if ($operator == self::FILTER_NOT_IN) {
                $query->whereNotIn('products_variants.item_id', Arr::wrap($value));
            } else {
                $query->whereIn('products_variants.item_id', Arr::wrap($value));
            }
        });

        $result = Product::withoutGlobalScopes()->whereKey(Arr::wrap($value))->get(['id', 'name']);
        $this->setFilters('product', $this->formatCollectionModels($result));
    }

    /**
     * @inheritdoc
     */
    #[\Override]
    protected function validateRequestRules(Request $request)
    {
        $operators = [
            self::FILTER_IN,
            self::FILTER_NOT_IN,
        ];

        $operatorsString = [
            self::FILTER_IS,
            self::FILTER_IS_NOT,
            self::FILTER_CONTAINS,
            self::FILTER_NOT_CONTAINS,
        ];

        return GridFilterRules::make([
            'query' => 'string',
            'action' => 'string',
            'sku' => ['array', new ArrayKeysIn(['operator', 'value'])],
            'sku.operator' => ['sometimes', 'required', 'in:' . implode(',', $operatorsString)],
            'sku.value' => ['bail', 'sometimes', 'required', 'string'],
            'barcode' => ['array', new ArrayKeysIn(['operator', 'value'])],
            'barcode.operator' => ['sometimes', 'required', 'in:' . implode(',', $operatorsString)],
            'barcode.value' => ['bail', 'sometimes', 'required', 'string'],
            'externalId' => ['array', new ArrayKeysIn(['operator', 'value'])],
            'externalId.operator' => ['sometimes', 'required', 'in:' . implode(',', $operatorsString)],
            'externalId.value' => ['bail', 'sometimes', 'required', 'string'],
            'product' => ['array', new ArrayKeysIn(['operator', 'value'])],
            'product.operator' => ['sometimes', 'required', 'in:' . implode(',', $operators)],
            'product.value' => ['bail', 'sometimes', 'required', 'array_int', 'exists:products,id'],
        ])->setOrder('item_id,external_id');
    }

    // Sorting methods

    /**
     * Sort by external ID as an integer.
     *
     * This method sorts the query results by the external ID, treating it as an integer.
     *
     * @param Grid $grid The grid instance containing sorting direction.
     * @return Closure The closure to apply the sorting.
     */
    public function sortByExternalId(Grid $grid): Closure
    {
        return function (Builder $query) use ($grid): void {
            $query->orderBy(\Illuminate\Support\Facades\DB::raw(sprintf('CAST(REPLACE(app_import, "%s-", "") AS UNSIGNED)', $this->manager->getAppImportKey())), $grid->getDirection());
        };
    }

    /**
     * @param $operator
     * @param $value
     * @return mixed
     */
    abstract public function filterExternalId($operator, $value);

    /**
     * @param $operator
     * @param $value
     * @param string $name
     * @param string $field
     * @return void
     */
    protected function compileFilter($operator, $value, string $name, string $field)
    {
        if (!$value || !is_scalar($value)) {
            return;
        }

        $this->setWhere($name, function ($query) use ($operator, $value, $field): void {
            /** @var Builder $query */
            switch ($operator) {
                case static::FILTER_IS:
                    $query->where($field, $value);
                    break;
                case static::FILTER_IS_NOT:
                    $query->where($field, '<>', $value);
                    break;
                case static::FILTER_CONTAINS:
                    $query->where($field, 'like', sprintf('%%%s%%', $value));
                    break;
                case static::FILTER_NOT_CONTAINS:
                    $query->where($field, 'not like', sprintf('%%%s%%', $value));
                    break;
            }
        });

        if (in_array($operator, [static::FILTER_IS, static::FILTER_IS_NOT, static::FILTER_CONTAINS, static::FILTER_NOT_CONTAINS])) {
            $this->setFilters($name, $value);
        }
    }
}
