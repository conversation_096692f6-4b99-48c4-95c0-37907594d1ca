<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\ValiComputers\Jobs;

use App\Exceptions\Error;
use App\Helper\Plan;
use App\Jobs\Job;
use Modules\Apps\Erp\ValiComputers\Models\Products;
use Modules\Importer\Models\HistoryImportLogTask;
use App\Models\Queue\SiteQueue;
use App\Traits\Crudling;
use App\Traits\Model as ModelTrait;
use App\Traits\PlanLimit;
use Carbon\Carbon;
use Modules\Apps\Erp\ValiComputers\Helpers\ApiClient;
use Modules\Apps\Erp\ValiComputers\Manager;

class GetProducts extends Job
{
    use Crudling;

    use ModelTrait;
    use PlanLimit;
    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'import';

    /**
     * @var Manager
     */
    protected $manager;

    /**
     * ImportProducts constructor.
     * @param mixed $page
     * @param null|mixed $history_id
     */
    public function __construct(protected $page = 1, protected $history_id = null)
    {
        $this->site_id = site('site_id');
    }

    /**
     * @return array|bool|int
     * @throws Error
     * @throws \App\Exceptions\Fault
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Throwable
     */
    public function execute()
    {
        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return static::SITE_MAINTENANCE;
        }

        // move job to another platform
        if (!allowSiteByPlatform()) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', platform(), sitePlatform()));
            return static::WRONG_PLATFORM;
        }

        $this->manager = new Manager();
        $this->info('Get products from Vali Computers');

        if ($this->manager->isActive()) {
            $this->load();
        } else {
            return static::DESTROY;
        }

        return true;
    }

    /**
     * @throws Error
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Throwable
     */
    protected function load()
    {

        $client = new ApiClient($this->manager);
        $getProducts = $client->getProducts($this->page);
        if (empty($getProducts)) {
            return;
        }

        $this->info('Vali Computers: ' . count($getProducts['items']) . ' products');
        $localApp = $this->manager->getApp();
        if ($this->page == 1) {
            $localApp->records()->delete();
            Products::truncate();
        }


        $isVisible = $this->manager->getSetting('only_visible');
        $isStatus = $this->manager->getSetting('import_status', [0, 1, 2, 3, 4]) ?: [0, 1, 2, 3, 4];

        $forInsert = [];
        $products = array_filter(array_map(function ($product) use ($isStatus, $isVisible, &$forInsert) {
            if (isset($product['status']) && in_array($product['status'], $isStatus)) {
                if ($isVisible == 1) {
                    if ($product['show'] == true) {
                        $id = $product['id'];
                    }
                } else {
                    $id = $product['id'];
                }

                if (!empty($id)) {
                    $settingCategory = $this->manager->getSetting('external_categories', []);
                    if (count($product['categories']) > 0) {
                        if (count($settingCategory) == 0 || in_array(0, $settingCategory) || in_array($product['categories'][0]['id'], $settingCategory)) {
                            $forInsert[] = [
                                'product_id' => $id,
                                'product_data' => json_encode($product),
                                'created_at' => Carbon::now(),
                                'updated_at' => Carbon::now(),
                            ];
                            return $id;
                        }
                    }
                }
            }

            return;
        }, $getProducts['items']));

        $this->info('Vali Computers: ' . count($products) . ' filtered products');

        if(($remaining = Plan::remaining('vali_computers_import')) && is_numeric($remaining)) {
            $forInsert = array_slice($forInsert, 0, intval($remaining));
            $products = array_slice($products, 0, intval($remaining));
        }

        if ($forInsert) {
            Products::insert($forInsert);
        }

        if (empty($this->history_id)) {
            $history = HistoryImportLogTask::createByModel($this->manager->getApp(), [
                'total' => count($products),
            ]);
            $this->history_id = $history->id;
        }

        if ($this->page == 1) {
            $this->manager->setProgress([
                'complete' => 0,
                'total' => count($products),
                'msg' => 'Products for creating and updating',
                'date' => Carbon::now('UTC')->format('Y-m-d H:i:s'),
                'info' => [
                    'Getting information for the products'
                ]
            ]);
        }

        $this->manager->incrementTotal(count($products));
        $products = array_chunk($products, 50);
        if ($getProducts['last_page'] > $this->page) {
            SiteQueue::executeQueueTask('vali_products', ['page' => $this->page + 1, 'history_id' => $this->history_id]);
        } else {
            SiteQueue::executeQueueTask('vali_clear_qty');
        }

        foreach ($products as $product) {
            SiteQueue::executeQueueTask('vali_import', ['products' => $product, 'history_id' => $this->history_id]);
        }
    }
}
