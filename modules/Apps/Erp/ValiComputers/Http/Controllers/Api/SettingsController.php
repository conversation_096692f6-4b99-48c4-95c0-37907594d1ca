<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\ValiComputers\Http\Controllers\Api;

use App\Exceptions\Error;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpSettingsController;
use Modules\Apps\Erp\ValiComputers\Manager;
use Modules\Apps\Erp\ValiComputers\Models\Categories;
use Modules\Core\Core\Exceptions\CouldNotConnectToServer;

/**
 * Class SettingsController
 * @package Modules\Apps\Erp\ValiComputers\Http\Controllers\Api
 */
class SettingsController extends AbstractErpSettingsController
{
    protected $arrays = [
        'external_categories', /*'import_status'*/
    ];

    protected $booleans = [
        'only_visible', 'import_category',
    ];

    /**
     * @param Manager $manager
     * @throws Error
     */
    public function __construct(Manager $manager)
    {
        parent::__construct($manager);
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function validateSettingsRequest(Request $request): void
    {
        $this->validate($request, [
            'api_key' => 'required_if:active,1',
        ], [
            'api_key.required_if' => 'API KEY is required',
        ]);

        if ($request->input('active')) {
            if (!$this->manager->validateCredentials($request->only($this->manager->getCredentialsFields()))) {
                throw new CouldNotConnectToServer();
            }
        }
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function validateCredentialsRequest(Request $request): void
    {
        $this->validate($request, [
            'api_key' => 'required',
        ], [
            'api_key.required' => 'API KEY is required',
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getExternalCategories()
    {
        $categories = Categories::withFullName()->get();
        if ($categories->isEmpty()) {
            $this->manager->formatCategories();
            $categories = Categories::withFullName()->get();
        }

        $categories = $categories->map(fn(Categories $c): array => [
            'id' => $c->category_id,
            'name' => $c->full_name ?? $c->name,
        ])->prepend([
            'id' => 0,
            'name' => 'All categories',
        ]);

        return response()->json($categories);
    }
}
