<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\ValiComputers\Helpers;

use App\Helper\ArrayTree;
use App\Helper\Cache\CcCache;
use App\Models\Router\Exceptions;
use App\Traits\IController;
use Guz<PERSON>Http\Client as GuzzleClient;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Collection;
use Modules\Apps\Erp\ValiComputers\Manager;

class ApiClient
{
    use IController;
    protected \Modules\Apps\Erp\ValiComputers\Manager $manager;

    protected \GuzzleHttp\Client $client;

    // https://www.vali.bg/api-documentation
    public const API_URL = 'https://www.vali.bg/api/v1/';

    /**
     * @throws \App\Exceptions\Error
     */
    public function __construct(Manager $manager, ?string $apiKey = null)
    {
        $this->manager = new Manager();
        $this->client = new GuzzleClient(
            [
                'base_uri' => self::API_URL,
                'headers' =>
                    [
                        'Authorization' => 'Bearer ' . ($apiKey ?: $this->manager->getSetting('api_key')),
                        'Content-Type' => 'application/json',
                        'Accept' => 'application/json'
                    ]
            ]
        );
    }

    /**
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function validateCredentials(array $parameters = [])
    {
        try {
            $response = $this->client->request('GET', 'manufacturers');
            return $response->getStatusCode() === 200;
        } catch (ClientException) {
            return false;
        }
    }

    /**
     * @return mixed|void
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getCategories()
    {
        try {
            $response = $this->client->request('GET', 'categories');
            return json_decode((string) $response->getBody()->getContents(), true);
        } catch (\Exception $exception) {
            Exceptions::createFromThrowable($exception, 'Vali Computers API ERROR', $exception->getMessage(), [
                'class' => self::class,
                'file' => __FILE__,
                'line' => __LINE__
            ]);
        }
    }

    /**
     * @return Collection
     */
    public function getCategoriesTree(): Collection
    {
        $categories = CcCache::remember(Manager::APP_KEY . '.categories', config('cache.ttl_1d'), fn() => $this->getCategories());
        $languages = array_unique([site('language_cp'), config('app.fallback_locale')]);
        $categories = array_map(function ($category) use ($languages) {
            foreach (['name', 'description'] as $column) {
                foreach ($category[$column] ?? [] as $name) {
                    foreach ($languages as $lang) {
                        if (is_array($category[$column]) && $name['language_code'] == $lang && !empty($name['text'])) {
                            $category[$column] = $name['text'];
                        }
                    }
                }

                if (is_array($category[$column])) {
                    $category[$column] = \Illuminate\Support\Arr::first($category[$column])['text'] ?? null;
                }
            }

            return $category;
        }, $categories ?: []);

        return collect(ArrayTree::createAutocomplete($categories, [
            'parentKey' => 'parent',
        ]))->map(fn($c): array => [
            'id' => $c['id'],
            'name' => $c['name'],
        ])->sortBy('name', SORT_LOCALE_STRING)->values();
    }

    /**
     * @param $categoryId
     * @return mixed|void
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getCategoryParameters(string $categoryId)
    {
        try {
            $response = $this->client->request('GET', 'parameters/'.$categoryId);
            return json_decode((string) $response->getBody()->getContents(), true);
        } catch (\Exception $exception) {
            Exceptions::createFromThrowable($exception, 'Vali Computers API ERROR', $exception->getMessage(), [
                'class' => self::class,
                'file' => __FILE__,
                'line' => __LINE__
            ]);
        }
    }

    /**
     * @return mixed|void
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @param mixed $page
     */
    public function getProducts($page = 1)
    {
        try {
            $response = $this->client->request('GET', 'products/full?page='.$page.'&per_page=1000');
            // get headers
            $headers = $response->getHeaders();
            return json_decode((string) $response->getBody()->getContents(), true);
        } catch (\Exception $exception) {
            if ($exception->getCode() == 429) {
                sleep(10);
                return $this->getProducts($page);
            }

            Exceptions::createFromThrowable($exception, 'Vali Computers API ERROR', $exception->getMessage(), [
                'class' => self::class,
                'file' => __FILE__,
                'line' => __LINE__
            ]);
        }
    }

    /**
     * @param $product_id
     * @return mixed|void
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getProductData(string $product_id)
    {
        try {
            $response = $this->client->request('GET', 'product/'.$product_id.'/full');
            return json_decode((string) $response->getBody()->getContents(), true);
        } catch (\Exception $exception) {
            if ($exception->getCode() == 429) {
                sleep(10);
                return $this->getProductData($product_id);
            }

            Exceptions::createFromThrowable($exception, 'Vali Computers API ERROR', $exception->getMessage(), [
                'class' => self::class,
                'file' => __FILE__,
                'line' => __LINE__
            ]);
        }
    }
}
