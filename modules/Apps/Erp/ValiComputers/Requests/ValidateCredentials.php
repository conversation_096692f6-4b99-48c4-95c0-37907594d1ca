<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\ValiComputers\Requests;

use App\Facades\Request;

class ValidateCredentials extends Request
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        if ($this->method() == 'POST') {
            return [
                'api_key' => 'required',
            ];
        }

        return [];
    }

    public function messages()
    {
        return response()->json([
            'api_key.required' =>  __('vali_computers::app.api_key.required'),
        ]);
    }
}
