<?php

declare(strict_types=1);

return [
    'action.required' => 'Il campo \'Azione\' è obbligatorio.',
    'action_import' => 'Importa e sincronizza',
    'action_sync' => 'Sincronizza solo',
    'all_categories' => 'Tutte le categorie',
    'api_key.required' => 'Il campo \'Chiave API\' è obbligatorio.',
    'category_connection_help' => 'Se colleghi la categoria con una categoria da CloudCart, quando importi i prodotti, verranno aggiunti alla tua categoria selezionata.',
    'category_import_status' => 'Scegli da quali categorie importare i prodotti',
    'category_name' => 'Categoria',
    'change_percent' => 'Cambia il ricarico per la categoria:',
    'cloudcart_category' => 'Categoria in CloudCart',
    'credentials_invalid' => 'Impossibile connettersi a Vali Computers. Controlla di aver inserito la chiave API corretta.',
    'credentials_valid' => 'Collegamento a Vali Computers avvenuto con successo.',
    'default_category.required_if' => 'Non hai selezionato una categoria.',
    'default_category_' => 'Categoria in cui verranno importati i prodotti',
    'external_categories' => 'Scegli da quali categorie importare i prodotti',
    'feature' => 'Pacchetto prodotto aggiuntivo',
    'get_price' => 'Il prezzo è tratto da',
    'get_price_distribution' => 'Prezzo all’Ingrosso',
    'get_price_distribution_client' => 'Prezzo per il Cliente Finale',
    'header.install' => 'Installa Vali Computers',
    'help.install' => ' ',
    'identifier.required' => 'Il campo \'Identificatore CloudCart\' è obbligatorio.',
    'identifier_barcode' => 'Codice a barre',
    'identifier_erp.required' => 'Il campo \'Identificatore in Vali Computers\' è obbligatorio.',
    'identifier_model' => 'Modello',
    'identifier_sku' => 'SKU',
    'import_category' => 'Importa categorie da Vali Computers',
    'import_status.required' => 'Il campo \'Importa prodotti con stato\' è obbligatorio.',
    'import_status_' => 'Importa prodotti con stato',
    'info.title' => 'Vali Computers',
    'markup' => 'Ricarico (%)',
    'product_status' => 'Importa prodotti con stato',
    'search_category' => 'Cerca per nome categoria...',
    'settings_saved' => 'Impostazioni salvate.',
    'status_0' => 'Esaurito',
    'status_1' => 'Disponibile',
    'status_2' => 'Quantità Limitata',
    'status_3' => 'In Transito',
    'status_4' => 'Solo su Ordine',
    'status_all' => 'Tutti gli stati',
    'status_info' => 'Aggiungi quantità predefinite per ogni stato da Vali Computers',
    'warning_msg' => 'Il tuo limite d\'importazione prodotti è {count} prodotti.',
    'warning_msg_href' => 'Acquista pacchetto aggiuntivo di prodotti.',
];
