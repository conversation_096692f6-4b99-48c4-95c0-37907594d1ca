<?php

declare(strict_types=1);

use Illuminate\Routing\Router;

Route::group(['prefix' => 'api/vali_computers', 'namespace' => 'Api'], function (Router $router): void {
    $router->post('install', ['as' => 'apps.api.vali_computers.install', 'uses' => 'IndexController@install']);
    $router->post('uninstall', ['as' => 'apps.api.vali_computers.uninstall', 'uses' => 'IndexController@uninstall']);
    $router->get('settings', ['as' => 'apps.vali_computers.api.settings2', 'uses' => 'IndexController@settings']);
    $router->post('settings', ['uses' => 'IndexController@saveSettings']);
    $router->post('validate', ['as' => 'apps.vali_computers.api.validate', 'uses' => 'IndexController@validateCredentials']);
    $router->get('categories', ['as' => 'apps.vali_computers.api.categories', 'uses' => 'IndexController@getCategories']);
    $router->get('external-categories/{search?}', ['as' => 'apps.vali_computers.api.categories', 'uses' => 'IndexController@ExternalCategories']);
    $router->post('percent', ['as' => 'apps.vali_computers.api.percent', 'uses' => 'IndexController@savePercent']);
    $router->get('status', ['as' => 'apps.api.vali_computers.status', 'uses' => 'IndexController@getStatus']);
    $router->post('status/change/{status}', ['as' => 'apps.api.vali_computers.status.change', 'uses' => 'IndexController@setActive']);
    $router->get('get-external-categories', ['as' => 'apps.vali_computers.api.select.categories', 'uses' => 'IndexController@getExternalCategories']);
});
