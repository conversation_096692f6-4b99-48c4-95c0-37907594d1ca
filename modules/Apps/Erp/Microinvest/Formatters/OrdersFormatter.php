<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: user
 * Date: 5/16/2018
 * Time: 1:58 PM
 */

namespace Modules\Apps\Erp\Microinvest\Formatters;

use App\Models\Queue\SiteQueue;
use Carbon\Carbon;
use App\Models\Order\Order;
use Illuminate\Support\Collection;
use Modules\Apps\Erp\Microinvest\MicroinvestManager;

class OrdersFormatter
{
    /**
     * @param Carbon $last_sync
     * @return array
     */
    public static function ordersToArray($last_sync): array
    {
        if (Order::getOrderStatusDecrementProducts() == 'pending') {
            $statuses = ['pending', 'paid', 'completed', 'cancelled', 'refunded', 'voided'];
        } else {
            $statuses = ['paid', 'completed', 'cancelled', 'refunded', 'voided'];
        }

        $orders = Order::allOrderData()->where('updated_at', '>=', $last_sync)
            ->where(function ($query) use ($statuses): void {
                $query->whereIn('status', $statuses)
                    ->orWhere('status_fulfillment', 'fulfilled');
            })
            ->get()
            ->sortByDesc('id');

        $orders_as_array[] = ['Partners' => []];
        $orders_as_array[] = ['Goods' => []];

        if ($orders->isEmpty()) {
            $orders_as_array[] = ['Operations' => []];
        } else {
            /**@var Order[]|Collection $orders */
            foreach ($orders as $order) {
                $orders_as_array['Operations'][] = static::prepareOperation($order);
            }
        }

        return $orders_as_array;
    }

    /**
     * @param $xml
     */
    public static function syncOrdersUsn($xml): void
    {
        $result = \Parser::xml($xml);
        $parsed = [];
        if (array_key_first($result['OperationUSN']) !== 0) {
            $parsed['OperationUSN'][] = $result['OperationUSN'];
        } else {
            $parsed = $result;
        }

        $chunked = array_chunk($parsed['OperationUSN'], 100);

        foreach ($chunked as $part) {
            SiteQueue::executeQueueTask('microinvest_sync_orders_usn', [
                'usnData' => $part
            ]);
        }
    }

    /**
     * @param Order $order
     * @return array
     */
    protected static function prepareOperation($order): array
    {
        $payment_method = '';
        if (isset($order->payment)) {
            if ($order->payment->isType('cc')) {
                $payment_method = 3;
            } else {
                $payment_method = match ($order->payment->provider) {
                    'cod' => 1,
                    'bwt' => 2,
                    default => 4,
                };
            }
        }

        $shipping_address = null;
        if (!is_null($order->shippingAddress)) {
            $shipping_address = [
                'country' => $order->shippingAddress->country_name,
                'state' => $order->shippingAddress->state_name,
                'city' => $order->shippingAddress->city_name,
                'street' => $order->shippingAddress->street_name.' '.$order->shippingAddress->street_number,
                'post_code' => $order->shippingAddress->post_code,
                'phone' => $order->shippingAddress->phone,
            ];
        }

        $billing_address = null;
        if (!is_null($order->billingAddress)) {
            $billing_address = [
                'country' => $order->billingAddress->country_name,
                'state' => $order->billingAddress->state_name,
                'city' => $order->billingAddress->city_name,
                'street' => $order->billingAddress->street_name.' '.$order->billingAddress->street_number,
                'post_code' => $order->billingAddress->post_code,
                'phone' => $order->billingAddress->phone,
            ];
        }


        if ($order->status_history->isNotEmpty()) {
            if (in_array($order->status, ['pending', 'pending', 'completed', 'cancelled', 'refunded', 'voided'])) {
                $actual_status = $order->status;
            } else {
                $actual_status = $order->status_history->last()->status ?? 'pending';
            }
        } else {
            $actual_status = $order->status;
        }

        $params = [
            'Operation' => [
                'DocumentNumber' => $order->id,
                'IsValid' => in_array($order->status, ['cancelled', 'refunded', 'voided']) ? 'false' : 'true',
                'DocumentUniqueCode' => $order->id,
                'PartnerID' => is_null($order->customer_id) ? null : 'cc-id-' . $order->customer_id,
                'Date' => $order->updated_at->timestamp,
                'IsInvoice' => false,
                'PaymentMethod' => $payment_method,
                'OrderStatus' => $actual_status,
                'DeliveryPrice' => isset($order->shipping) ? $order->shipping->order_amount_input : 0,
                'Customer' => [
                    'Name' => $order->customer_full_name,
                    'Email' => $order->customer_email,
                    'Group' => $order->customer_group,
                ],
                'Shipping_address' => $shipping_address,
                'Billing_address' => $billing_address
            ],
        ];

        foreach ($order->products as $order_product) {
            $good_id = null;
            if ($order_product->variant) {
                $external_record_key = $order_product->variant->external_meta_data()
                    ->where('integration', MicroinvestManager::APP_KEY)->value('external_record_key');
                $good_id = empty($external_record_key) ? 'cc-id-' . $order_product->variant_id : $external_record_key;
            }

            $arr = $order_product->toArray();
            $mod = round(collect($arr['modifications'] ?? [])->sum('amount') / $order_product->getQuantity());

            $params['Operation']['OperationItems'][] = [
                'OperationItem' => [
                    'GoodID' => $good_id,
                    'GoodSKU' => empty($order_product->sku) ? '' : $order_product->sku,
                    'GoodEAN' => empty($order_product->barcode) ? '' : $order_product->barcode,
                    'Qtty' => $order_product->getQuantity(),
                    'PriceOut' => moneyInput(round($order_product->getTotalPriceWithOptionsAfterDiscountsWithModification() / $order_product->getQuantity()), $order->getCurrency()),
                    'DiscountApplied' => moneyInput(($order_product->hasDiscounts() ? $arr['discounted_amount_per_item']['amount'] : 0) + $mod, $order->getCurrency()),
                ]
            ];
        }

        return $params;
    }
}
