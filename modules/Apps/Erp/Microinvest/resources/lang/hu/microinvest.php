<?php

declare(strict_types=1);

return [
    'err.duplicate_usn' => 'Microinvest - Nem sikerült szinkronizálni a(z) %1$s rendelés UIC-jét. A(z) %2$s UIC már létezik egy másik rendeléshez.',
    'err.duplicate_usn_for_order_%1$s' => 'Microinvest - Nem sikerült szinkronizálni a(z) %1$s rendelés UNP-jét. Ilyen UNP (%2$s) már létezik egy másik rendeléshez',
    'header.install' => 'Microinvest telepítése',
    'header.setting' => 'Microinvest beállítások',
    'help.grocery_store' => 'A(z) ":app" használatakor a termékek nem lesznek csoportosítva és változatokkal együtt létrehozva.',
    'help.install' => ' ',
    'help.setting' => 'Állítsd be az alábbi be<PERSON>t, hogy megfelelően konfiguráld az online boltodat a Microinvesttel.',
    'help.sync' => 'Ha nem találsz rekordot, el kell küldened az XML-t a CloudCart termékek azonosítójának és a Microinvest termékek azonosítójának szinkronizálásához az alábbi linkre: <strong>:link</strong>',
    'info.install' => '- termékek importálása az online boltodból a Microinvestbe<br>
- termékek importálása a Microinvestből az online boltodba<br>
    - a termékek árai és mennyiségei szinkronizálása a Microinvestből az online boltodba<br>
    - a termékek mennyiségeinek szinkronizálása a Microinvestben, miután a rendeléseket leadtad az online boltodban',
    'info.title' => 'Microinvest ERP',
    'keyid' => 'Microinvest azonosító',
    'label.identifier' => 'Azonosító',
    'label.password' => 'Jelszó',
    'label.products_last_import_xml' => 'Termékinformációk a következő időpontban érkeztek: %1$s',
    'label.qty_and_prices_last_import_xml' => 'Ár- és mennyiséginformációk a következő időpontban érkeztek: %1$s',
    'microinvest_id' => 'Microinvest azonosító',
    'msg.completed' => 'Az importálás befejeződött',
    'required.grocery_store' => 'Ez az opció megköveteli, hogy telepítve legyen a(z) ":app" alkalmazás.',
    'settings.mapping.delete.success' => 'Sikeresen törölted a Microinvest azonosítót',
    'table.delete' => 'Microinvest azonosító törlése',
    'table.delete.confirm' => 'Microinvest azonosító törlésekor a termék a kiválasztott azonosító alapján lesz összehasonlítva.',
    'task.received.product' => 'Termékekkel kapcsolatos információk érkeztek: :date',
    'task.received.variant' => 'Mennyiségi információk érkeztek: :date',
    'task.received.xml.product' => 'XML fájl a következő termékekkel kapcsolatos információkkal #:number',
    'task.received.xml.variant' => 'XML fájl a következő mennyiségekkel kapcsolatos információkkal #:number',
    'title.able_to' => 'A Microinvest-tel képes leszel:',
];
