{include file="{Apps::templatesPath()}dependency/install_reload.tpl"}
{include file="{Apps::templatesPath()}includes/app-breadcrumb.tpl" active=__('microinvest::microinvest.info.title')}

{capture name="app_addition"}
    <form action="{route('apps.microinvest.install')}" method="post" id="appInstallReload" role="form">
        <h3>{t}microinvest::microinvest.title.able_to{/t}</h3>
        <p>{t}microinvest::microinvest.info.install{/t}</p>
        <button class="btn btn-primary" type="submit">{t}global.action.install{/t}</button>
    </form>
{/capture}

<div class="wrapper">
    <div class="app-wrapper">
        {include file="{Apps::templatesPath()}includes/app-intro.tpl" title=__('microinvest::microinvest.header.install') text=__('microinvest::microinvest.help.install')}
        {include file="{Apps::templatesPath()}includes/app-icons.tpl"  app_icon='icon-microinvest.png'}
        {include file="{Apps::templatesPath()}includes/app-addition.tpl" addition=$smarty.capture.app_addition}
    </div>
</div>
