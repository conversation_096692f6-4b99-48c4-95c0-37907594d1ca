<?php

declare(strict_types=1);

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'apps/microinvest', 'namespace' => 'Admin', 'middleware' => 'hasApiPermission:apps'], function (Router $router): void {
    //$router->get('/', ['as' => 'apps.microinvest', 'uses' => 'AppController@index']);
    //    $router->get('progress', ['as' => 'apps.api.microinvest.progress', 'uses' => 'AppController@progress']);

    //    $router->group(['prefix' => 'tasks'], function(Router $router) {
    //        $router->get('view/{id}', ['as' => 'apps.microinvest.tasks.view', 'uses' => 'TasksController@view']);
    //    });
    //    $router->group(['prefix' => 'log'], function(Router $router) {
    //        $router->match(['get', 'post'],'/', ['as' => 'apps.microinvest.log', 'uses' => 'LogController@log']);
    //        $router->get('failed', ['as' => 'apps.microinvest.log.failed', 'uses' => 'LogController@failed_product']);
    //    });
    //
    //    $router->get('{slug?}', ['as' => 'apps.microinvest', 'uses' => 'AppController@index']);
});

Route::group(['prefix' => 'api/microinvest', 'namespace' => 'Admin'], function (Router $router): void {
    //    $router->post('install', ['as' => 'apps.microinvest.install', 'uses' => 'AppController@install']);
    //    $router->post('uninstall', ['as' => 'apps.microinvest.uninstall', 'uses' => 'AppController@uninstall']);
    //    $router->get('settings', ['as' => 'microinvest.api.settings', 'uses' => 'AppController@settings']);
    //    $router->post('settings', ['as' => 'microinvest.api.settings', 'uses' => 'AppController@saveSettings']);
    //    $router->group(['prefix' => 'mapping'], function(Router $router) {
    //        $router->get('/', ['as' => 'microinvest.settings.mapping', 'uses' => 'MappingController@index']);
    //        $router->post('reset', ['as' => 'apps.api.microinvest.reset.mapping', 'uses' => 'MappingController@store']);
    //        $router->get('delete/{variantId}/{microiventId}', ['as' => 'microinvest.api.mapping.delete', 'uses' => 'MappingController@delete']);
    //    });
    //    $router->group(['prefix' => 'tasks'], function(Router $router) {
    //        $router->get('/', ['as' => 'apps.api.microinvest.tasks', 'uses' => 'TasksController@index']);
    //    });

    $router->post('install', ['as' => 'apps.microinvest.install', 'uses' => 'SettingsController@install']);
    $router->post('uninstall', ['as' => 'apps.microinvest.uninstall', 'uses' => 'SettingsController@uninstall']);
    $router->get('reset-import', ['as' => 'microinvest.api.reset_import', 'uses' => 'SettingsController@resetImport']);

    $router->group(['prefix' => 'settings'], function (Router $router): void {
        $router->get('/', ['as' => 'microinvest.api.settings', 'uses' => 'SettingsController@settings']);
        $router->post('/', ['uses' => 'SettingsController@settingsSave']);
    });

    $router->group(['prefix' => 'products'], function (Router $router): void {
        $router->get('/', ['as' => 'apps.api.microinvest.products', 'uses' => 'ProductsController@products']);
        $router->delete('{id}', ['as' => 'apps.api.microinvest.product.delete', 'uses' => 'ProductsController@productDelete'])
            ->where('id', '.*');
        $router->post('/', ['as' => 'apps.api.microinvest.product.update', 'uses' => 'ProductsController@updateExternalId']);

    });

    $router->group(['prefix' => 'tasks'], function (Router $router): void {
        $router->get('/{parent_id?}', ['as' => 'apps.api.microinvest.tasks', 'uses' => 'TasksController@tasks'])
            ->where('parent_id', '.*');
        $router->delete('{id}', ['as' => 'apps.api.microinvest.tasks.delete', 'uses' => 'TasksController@taskDelete'])
            ->where('id', '.*');
    });

    $router->group(['prefix' => 'status'], function (Router $router): void {
        $router->get('/', ['as' => 'apps.api.microinvest.status', 'uses' => 'StatusController@getStatus']);
    });
});

Route::group(['prefix' => 'tasks/microinvest', 'namespace' => 'Admin'], function (Router $router): void {
    $router->get('{id}', ['as' => 'apps.microinvest.tasks.view', 'uses' => 'TasksController@view']);
});
