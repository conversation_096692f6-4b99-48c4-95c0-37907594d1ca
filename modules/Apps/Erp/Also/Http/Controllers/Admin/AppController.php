<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Also\Http\Controllers\Admin;

use App\Exceptions\Error;
use App\Exceptions\Errors;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Modules\Apps\Erp\Also\AlsoManager;
use Modules\Apps\Erp\Also\Http\Requests\MapRequest;
use Modules\Apps\Erp\Also\Http\Requests\SettingsRequest;
use Modules\Apps\Erp\Also\Models\CategoryMapping;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpController;
use Modules\Importer\Traits\ProgressInfoController;
use Throwable;

/**
 * @deprecated
 */
class AppController extends AbstractErpController
{
    use ProgressInfoController {
        ProgressInfoController::progress as protectedParent;
    }

    /**
     * AppController constructor.
     * @param AlsoManager $manager
     * @throws Error
     */
    public function __construct(AlsoManager $manager)
    {
        parent::__construct($manager);
    }

    /**
     * @return View
     * @throws Error
     */
    public function index()
    {
        return response()->view('resources::vue-sitecp');
    }

    /**
     * @return View|RedirectResponse
     * @throws Error
     */
    public function config()
    {
        if (!$this->manager->isInstalled()) {
            return redirect()->route('apps.also');
        }

        $statuses = [
            'onhand' => 5,
            'minimum' => 1,
            'onorder' => 0
        ];

        return response()->json([
            'statuses' => $statuses,
            'settings' => $this->setSettingsByKeys(),
            'active' => $this->manager->getApp()->active,
            'has_mapping' => !!CategoryMapping::value('id'),
            'info' => $this->getAppInfo()
        ]);
    }

    /**
     * @return View|RedirectResponse
     * @throws Error
     */
    #[\Override]
    public function settings(): \Illuminate\Http\JsonResponse
    {
        if (!$this->manager->isInstalled()) {
            return redirect('404');
        }

        return response()->view('resources::vue-sitecp');
    }

    /**
     * @param SettingsRequest $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function saveSettings(SettingsRequest $request): JsonResponse
    {
        try {

            if ($request->has('active')) {
                $this->manager->getClient()->validateCredentials($request->input('username'), $request->input('password'));
            }

            $data = array_merge($request->input(), [
                'update_properties' => !!$request->input('update_properties'),
            ]);

            \Illuminate\Support\Facades\DB::transaction(function () use ($request, $data): void {
                $this->manager->updateSettings(Arr::only($data, [
                    'username', 'password', 'price',
                    'onhand', 'minimum', 'onorder', 'update_properties'
                ]));
                $this->manager->updateActive(!!$request->input('active'));
            });

            return response()->json([
                'msg' => __('apps.successfully.configured'),
                'status' => 'success'
            ]);
        } catch (Error|Exception $e) {
            return  response()->json([
                'msg' => $e->getMessage(),
                'status' => 'error'
            ])->setStatusCode('422');
        }
    }

    /**
     * @return View|RedirectResponse
     * @throws Error
     * @throws Errors
     */
    public function mapping()
    {

        $also_categories = CategoryMapping::orderBy('id', 'asc')->get();
        return response()->json([
            'also_categories' => $also_categories,
            'mapping' => $this->manager->getClient()->getCategories()
        ]);
    }

    /**
     * @return Response
     */
    public function mappingVue()
    {
        return response()->view('resources::vue-sitecp');
    }

    /**
     * @param MapRequest $request
     * @return Response
     * @throws Throwable
     */
    public function saveMap(MapRequest $request)
    {
        try {
            if (!$this->manager->isInstalled()) {
                throw new Error(__('apps.error.not_installed'));
            }

            $also = $request->input('also', []);
            $mapping = $this->manager->getClient()->getCategories();
            $insert = [];
            foreach ($also as $data) {
                if ($mapping->firstWhere('property_id', $data['property_id'])) {
                    $insert[] = [
                        'category_id' => $data['category_id'],
                        'property_id' => $data['property_id'],
                        'percentage' => $data['percentage'],
                        'created_at' => Carbon::now('UTC'),
                        'updated_at' => Carbon::now('UTC')
                    ];
                }
            }

            \Illuminate\Support\Facades\DB::transaction(function () use ($insert): void {
                CategoryMapping::whereNotNull('id')->delete();
                CategoryMapping::insert($insert);
                //$this->manager->setWorking(1);
            });

            return response()->json([
                'msg' => __('apps.successfully.configured'),
                'status' => 'success'
            ]);
        } catch (Exception $exception) {
            return response()->json([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }
    }

    /**
     * @return Response
     * @throws Throwable
     */
    public function cancel()
    {
        try {
            $this->manager->setWorking(0);

            return response()->json([
                'msg' => __('global.successfully.cancel_working'),
                'status' => 'success'
            ]);
        } catch (Exception $exception) {
            return response()->json([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }
    }

    /**
     * @return Response
     * @throws Throwable
     */
    public function start()
    {
        try {
            $this->manager->setWorking(1);

            return response()->json([
                'msg' => __('also::app.successfully.start_working'),
                'status' => 'success'
            ]);
        } catch (Exception $exception) {
            return response()->json([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }
    }
}
