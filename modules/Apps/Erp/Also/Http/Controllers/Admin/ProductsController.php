<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Also\Http\Controllers\Admin;

use App\Exceptions\Error;
use Modules\Apps\Erp\Also\AlsoManager;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpProductsController;

/**
 * Class ProductsController
 * @package Modules\Apps\Erp\Also\Http\Controllers\Admin
 */
class ProductsController extends AbstractErpProductsController
{
    protected $MAPPING_TYPE = 'app_import';

    /**
     * @param AlsoManager $manager
     * @throws Error
     */
    public function __construct(AlsoManager $manager)
    {
        parent::__construct($manager);
    }
}
