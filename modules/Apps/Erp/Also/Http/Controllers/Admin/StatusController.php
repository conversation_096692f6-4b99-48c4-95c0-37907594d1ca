<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Also\Http\Controllers\Admin;

use App\Exceptions\Error;
use Modules\Apps\Erp\Also\AlsoManager;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpStatusController;

/**
 * Class StatusController
 * @package Modules\Apps\Erp\Also\Http\Controllers\Admin
 */
class StatusController extends AbstractErpStatusController
{
    /**
     * @param AlsoManager $manager
     * @throws Error
     */
    public function __construct(AlsoManager $manager)
    {
        parent::__construct($manager);
    }
}
