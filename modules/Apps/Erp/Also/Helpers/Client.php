<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 5.10.2016 г.
 * Time: 00:41 ч.
 */

namespace Modules\Apps\Erp\Also\Helpers;

use App\Exceptions\Error;
use Exception;
use Guz<PERSON>Http\Client as GuzzleClient;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\RequestOptions;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Modules\Apps\Erp\Also\AlsoManager;
use Modules\Importer\DataFormat\Product;
use Parser;
use Symfony\Component\DomCrawler\Crawler;

class Client
{
    public const BASE_URL = 'https://b2b.actebis.com/invoke/ActDelivery_HTTP.Inbound/receiveXML_API';

    private array $_documentErrors = [];
    private $_bodyError;

    /**
     * Helper constructor.
     * @param AlsoManager $manager
     * @param array $parameters
     */
    public function __construct(private readonly AlsoManager $manager, private readonly array $parameters = [])
    {
    }

    /**
     * @return Collection
     * @throws Error
     */
    public function getCategories(): Collection
    {
        $this->_documentErrors = [];
        $xml = $this->get(['CatalogCategory' => 'true']);
        if ($this->_documentErrors) {
            $errorString = implode('; ', array_map(fn($errors): string => implode('; ', $errors), Arr::wrap($this->_documentErrors)));
            throw new Error(
                $this->_bodyError ?
                    sprintf('%s: %s', $errorString, $this->_bodyError) :
                    $errorString
            );
        }

        $result = Parser::xml($xml);
        $result = $this->checkAndTransform($result, 'productCategory');

        $categories = [];
        foreach (($result['productCategory'] ?? []) as $productCategory) {
            $name1 = $productCategory['@name'];
            $productGroup = $this->checkAndTransform($productCategory, 'productGroup');
            foreach (($productGroup['productGroup'] ?? []) as $propertyGroupId) {
                $name2 = $propertyGroupId['@name'];
                if (!is_array($propertyGroupId['propertyGroupId'])) {
                    $propertyGroupId['propertyGroupId'] = [$propertyGroupId['propertyGroupId']];
                    $propertyGroupId['atom:link'] = [$propertyGroupId['atom:link']];
                }

                foreach ($propertyGroupId['propertyGroupId'] as $row => $group) {
                    $name3 = $group;
                    parse_str(parse_url($propertyGroupId['atom:link'][$row]['@href'] ?? '')['query'] ?? '', $result);
                    if (!empty($result['propertyId'])) {
                        $categories[] = [
                            'category' => $name1,
                            'sub_category' => $name2,
                            'sub_sub_category' => $name3,
                            'name' => implode(' > ', array_filter([$name1, $name2, $name3])),
                            'property_id' => $result['propertyId'],
                        ];
                    }
                }
            }
        }

        return collect($categories)
            ->sortBy('name', SORT_ASC)
            ->values();
    }

    /**
     * @param $username
     * @param $password
     * @return bool
     * @throws Error
     */
    public function validateCredentials($username, $password): bool
    {
        $this->_documentErrors = [];
        $body = $this->request(static::BASE_URL, [
            'CatalogCategory' => 'true',
            'j_u' => $username,
            'j_p' => $password
        ], [
            RequestOptions::TIMEOUT => 5,
            RequestOptions::CONNECT_TIMEOUT => 5,
            RequestOptions::READ_TIMEOUT => 5,
        ]);

        if ($this->_documentErrors) {
            throw new Error(implode('<br>', $this->_documentErrors), 'username');
        }

        if (!$body) {
            throw new Error("Unable to connect to also.com", 'username');
        }

        if (str_contains($body, '<error>')) {
            throw new Error(\Illuminate\Support\Arr::first(explode("\n", strip_tags((string) $body))), 'username');
        }

        $result = Parser::xml($body);
        if (empty($result['productCategory'])) {
            throw new Error("Unable to get data form also.com", 'username');
        }

        return true;
    }

    /**
     * @param array $product
     * @return Product
     * @throws Exception
     */
    public function toImport(array $product): ?\Modules\Importer\DataFormat\Product
    {
        if (empty($product['id'])) {
            return null;
        }

        $detailed = $this->getProduct($product['id']);
        $product = array_merge($product, [
            'description' => $detailed['description'] ?? null,
            'images' => $detailed['images'] ?? [],
            'properties' => array_merge(
                (is_array($product['properties'] ?? null) ? $product['properties'] : []),
                (is_array($detailed['properties'] ?? null) ? $detailed['properties'] : [])
            ),
        ]);

        return (new FormatProduct($product))->toImport();
    }

    /**
     * @param Collection $mapping
     * @return Collection
     * @throws Error
     */
    public function getProducts(Collection $mapping)
    {
        $products = [];
        foreach ($mapping as $map) {
            $xml = $this->get(['propertyId' => $map->property_id]);
            if ($this->_documentErrors) {
                if (!empty($this->_documentErrors)) {
                    $errorString = implode('; ', array_map(fn($errors): string => implode('; ', $errors), Arr::wrap($this->_documentErrors)));
                    throw new Error(
                        $this->_bodyError ?
                            sprintf('%s: %s', $errorString, $this->_bodyError) :
                            $errorString
                    );
                } else {
                    return collect();
                }
            }

            $result = Parser::xml($xml);
            $result = $this->checkAndTransform($result, 'product');

            $priceField = $this->manager->getSetting('price', 'priceEndUser');
            foreach ($result['product'] ?? [] as $product) {
                $price = (float)($product[$priceField]['#text'] ?? 0);
                //dont import zero price products
                if ($price <= 0) {
                    continue;
                }

                $products[] = [
                    'id' => $product['@productId'],
                    'group_id' => $map->property_id,
                    'category_id' => $map->category_id,
                    'group' => $product['@groupId'],
                    'model' => $product['@groupId'] . '|' . $product['@codeId'],
                    'code' => $product['@codeId'],
                    'ean' => $product['@ean'],
                    'name' => $product['name'],
                    'vendor' => $product['vendor'],
                    'price' => [$price, $currency = ($product[$priceField]['@currency'] ?? 'BGN')],
                    'qty' => $this->_mkQty($product),
                    'percentage' => $map->percentage,
                    'warrantyQty' => $product['warrantyQty'],
                    'warrantyUnit' => $product['warrantyUnit'],
                    'properties' => $this->_makeProperties($product),
                ];
            }
        }

        return collect($products);
    }

    /**
     * @param $mapping
     * @return void
     * @throws Error
     */
    public function getFirstProduct($mapping): void
    {
        $xml = $this->get(['propertyId' => $mapping->property_id]);
        if ($this->_documentErrors) {
            $errorString = implode('; ', array_map(fn($errors): string => implode('; ', $errors), Arr::wrap($this->_documentErrors)));
            throw new Error(
                $this->_bodyError ?
                    sprintf('%s: %s', $errorString, $this->_bodyError) :
                    $errorString
            );
        }

        $result = Parser::xml($xml);
        if (empty($result['product'])) {
            throw new Error(__('Няма намерени продукти за импортиране'));
        }

        $firstProduct = \Illuminate\Support\Arr::first($result['product']);
        $priceField = $this->manager->getSetting('price', 'priceEndUser');
        if (!isset($firstProduct[$priceField])) {
            throw new Error(__('also::app.no_exist.'.$priceField));
        }
    }

    /**
     * @param $id
     * @return mixed
     * @throws Exception
     */
    public function getProduct($id): ?array
    {
        $this->_documentErrors = [];
        if (empty($xml = $this->get(['ProductData' => 'true', 'codeId' => $id]))) {
            if ($this->_documentErrors) {
                $errorString = implode('; ', array_map(fn($errors): string => implode('; ', $errors), Arr::wrap($this->_documentErrors)));
                throw new Exception(
                    $this->_bodyError ? sprintf(
                        '%s: %s',
                        $errorString,
                        $this->_bodyError
                    ) : $errorString
                );
            }

            return null;
        }

        $result = Parser::xml($xml);
        $result = $this->checkAndTransform($result, 'image');
        $result = $this->checkAndTransform($result, 'file');

        $images = $result['image'] ?? [];

        return [
            'description' => $this->_mkDescription(collect($result['file'] ?? [])->firstWhere('@type', 'Datasheet')['#text'] ?? null, $properties),
            'images' => array_column($images, '#text'),
            'properties' => $properties
        ];
    }

    /**
     * @param array $parameters
     * @return null|string
     */
    protected function get(array $parameters = []): ?string
    {
        $this->_bodyError = null;
        $body = $this->request(static::BASE_URL, array_merge($parameters, $this->parameters));
        if (!$body) {
            $this->_bodyError = $body;
            $this->_documentErrors[] = ['Empty request body'];
            return null;
        }

        if (!$this->isXMLContentValid($body)) {
            $this->_documentErrors[] = ['Request body is not valid XML'];
            $this->_bodyError = $body;
            return null;
        }

        if (preg_match('~<error>(.*)<\/error>~imUs', $body, $match)) {
            $this->_bodyError = $body;
            $this->_documentErrors[] = [trim((string) Arr::first(explode("\n", $match[1])))];
            return null;
        }

        return $body;
    }

    /**
     * @param $url
     * @param array $parameters
     * @param array $options
     * @return null|string
     * @throws GuzzleException
     */
    protected function request($url, array $parameters = [], array $options = []): ?string
    {
        $client = new GuzzleClient([
            'base_uri' => $url
        ]);
        try {
            $get = $client->get('', [
                'query' => $parameters,
            ] + $options);
        } catch (Exception $exception) {
            if ($exception instanceof ClientException || $exception instanceof ServerException) {
                $this->_documentErrors[] = [__('also::app.client.error.connection', ['code' => $exception->getResponse()->getStatusCode()])];
            } else {
                $this->_documentErrors[] = [trim((string) Arr::first(explode("\n", $exception->getMessage())))];
            }

            return null;
        }

        return $get->getBody()->getContents();
    }

    /**
     * @param $xmlContent
     * @return bool
     */
    public function isXMLContentValid($xmlContent): bool
    {
        return !!@simplexml_load_string((string) $xmlContent);
    }

    /**
     * Check array and fix it
     * @param $data
     * @param $key
     * @return array
     */
    protected function checkAndTransform($data, $key)
    {
        if (array_key_exists($key, $data)) {
            if (!is_array($data[$key])) {
                return [];
            }

            if (!is_numeric(array_key_first($data[$key]))) {
                $data[$key] = [$data[$key]];
            }
        }

        return $data;
    }

    /**
     * @param $product
     * @return int
     * @throws Error
     */
    protected function _mkQty(array $product)
    {
        if (in_array($stockInfoValue = strtolower($product['stockInfo']['stockInfoValue'] ?? ''), ['onhand', 'minimum', 'onorder'])) {
            //$onMin = (int)($product['stockInfo']['stockInfoData'] ?? 0);
            switch ($stockInfoValue) {
                case 'onhand':
                    return $this->manager->getSetting('onhand', 5);//$onMin >= 5 ? mt_rand(4, 5) : $onMin;
                case 'minimum':
                    return $this->manager->getSetting('minimum', 1);
                case 'onorder':
                    return $this->manager->getSetting('onorder', 0);
            }
        }

        return 0;
    }

    /**
     * @param $product
     * @return array
     */
    protected function _makeProperties(array $product): array
    {
        $properties = [];
        if ($product['warrantyUnit'] == 3) {
            $properties[] = [
                'name' => __('sf.product.warranty'),
                'values' => [__('sf.product.warranty_lifetime')],
            ];
        } elseif ($product['warrantyUnit'] == 2) {
            $properties[] = [
                'name' => __('sf.product.warranty'),
                'values' => [trans_choice('sf.product.warranty_months', $product['warrantyQty'], ['months' => $product['warrantyQty']])],
            ];
        } elseif ($product['warrantyUnit'] == 1) {
            $properties[] = [
                'name' => __('sf.product.warranty'),
                'values' => [trans_choice('sf.product.warranty_years', $product['warrantyQty'], ['months' => $product['warrantyQty']])],
            ];
        }

        return $properties;
    }

    /**
     * @param mixed $url
     * @param mixed $rows
     * @return mixed
     */
    protected function _mkDescription($url, &$rows = []): ?string
    {
        if (empty($url) || !$this->_isLink($url) || empty($html = $this->request($url))) {
            $rows = [];
            return null;
        }

        $crawler = new Crawler($html);

        $rows = $crawler->filter('table.properties')->filter('.alt-0, .alt-1');
        $rows = $rows->each(function (Crawler $row): ?array {
            $cols = $row->filter('td');
            if ($cols->count() < 2) {
                return null;
            }

            return [
                'name' => trim((string) $cols->first()->html()),
                'values' => array_map('trim', explode(',', trim((string) $cols->last()->html()))),
            ];
        });

        if (empty($rows)) {
            return null;
        }

        $t = '<table class="table table-striped">';
        foreach ($rows as $pr) {
            if ($pr['values'][0] ?? null) {
                $t .= '<tr>';
                $t .= '<td>' . $pr['name'] . ':</td>';
                $t .= '<td>' . implode(', ', $pr['values']) . ' </td>';
                $t .= '</tr>';
            }
        }

        $t .= '</table>';
        return $t;
    }

    /**
     * @param $string
     * @return bool
     */
    private function _isLink($string): bool
    {
        return strtolower(substr((string) $string, 0, 5)) == 'http:' || strtolower(substr((string) $string, 0, 6)) == 'https:';
    }
}
