<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAppAlsoCategoryMappingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        if (!Schema::hasTable('@app_also_categories_mapping')) {
            Schema::create('@app_also_categories_mapping', function (Blueprint $table): void {
                $table->increments('id')->unsigned();
                $table->integer('category_id')->unsigned()->index();
                $table->string('property_id')->index();
                $table->integer('percentage')->unsigned()->default(0);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('@app_also_categories_mapping');
    }
}
