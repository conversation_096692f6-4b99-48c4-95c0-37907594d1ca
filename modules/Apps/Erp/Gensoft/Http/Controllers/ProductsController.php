<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Gensoft\Http\Controllers;

use App\Exceptions\Error;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpProductsController;
use Modules\Apps\Erp\Gensoft\GensoftManager;

/**
 * Class ProductsController
 * @package Modules\Apps\Erp\Gensoft\Http\Controllers
 */
class ProductsController extends AbstractErpProductsController
{
    /**
     * @param GensoftManager $manager
     * @throws Error
     */
    public function __construct(GensoftManager $manager)
    {
        parent::__construct($manager);
    }
}
