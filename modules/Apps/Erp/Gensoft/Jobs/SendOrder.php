<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 13.6.2018 г.
 * Time: 09:00 ч.
 */

namespace Modules\Apps\Erp\Gensoft\Jobs;

use App\Jobs\Job;
use App\Models\Order\Order;
use Modules\Apps\Erp\Gensoft\GensoftManager;
use Modules\Apps\Erp\Gensoft\Services\Gensoft;

class SendOrder extends Job
{
    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'order-events6';

    /**
     * SendOrder constructor.
     * @param $orderId
     * @param $operation
     * @param int $orderId
     */
    public function __construct(public $orderId, public $operation)
    {
        $this->site_id = site('site_id');
    }

    /**
     * @return array|bool|int
     * @throws \App\Exceptions\Error
     * @throws \App\Exceptions\Fault
     */
    public function execute()
    {
        Gensoft::log('Start job');

        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return static::SITE_MAINTENANCE;
        }

        // move job to another platform
        if (!allowSiteByPlatform()) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', platform(), sitePlatform()));
            return static::WRONG_PLATFORM;
        }

        $manager = new GensoftManager();
        $order = Order::allOrderData()->find($this->orderId);
        Gensoft::log('Start sending ' . $order->id);
        Gensoft::sendOrderStatusChangeRequest($manager, $this->operation, $order);
        Gensoft::log('Complete sending ' . $order->id);

        return static::DESTROY;
    }
}
