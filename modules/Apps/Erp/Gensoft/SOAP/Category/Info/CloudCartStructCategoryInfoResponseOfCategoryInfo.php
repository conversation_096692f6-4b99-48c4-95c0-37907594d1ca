<?php

declare(strict_types=1);

/**
 * File for class CloudCartStructCategoryInfoResponseOfCategoryInfo
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
/**
 * This class stands for CloudCartStructCategoryInfoResponseOfCategoryInfo originally named CategoryInfoResponseOfCategoryInfo
 * Meta informations extracted from the WSDL
 * - from schema : {@link http://195.34.100.202:7789/MWCloudCart/Services/CloudCartService.asmx?wsdl}
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
class CloudCartStructCategoryInfoResponseOfCategoryInfo extends CloudCartStructMethodResponseOfCategoryInfo
{
    /**
     * The Categories
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var CloudCartStructArrayOfCategoryInfo
     */
    public $Categories;

    /**
     * Constructor method for CategoryInfoResponseOfCategoryInfo
     * @see parent::__construct()
     * @param CloudCartStructArrayOfCategoryInfo $_categories
     * @return CloudCartStructCategoryInfoResponseOfCategoryInfo
     */
    public function __construct($_categories = null)
    {
        CloudCartWsdlClass::__construct(['Categories' => ($_categories instanceof CloudCartStructArrayOfCategoryInfo) ? $_categories : new CloudCartStructArrayOfCategoryInfo($_categories)], false);
    }

    /**
     * Get Categories value
     * @return CloudCartStructArrayOfCategoryInfo|null
     */
    public function getCategories()
    {
        return $this->Categories;
    }

    /**
     * Set Categories value
     * @param CloudCartStructArrayOfCategoryInfo $_categories the Categories
     * @return CloudCartStructArrayOfCategoryInfo
     */
    public function setCategories($_categories)
    {
        return ($this->Categories = $_categories);
    }

    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    #[\Override]
    public function __toString(): string
    {
        return self::class;
    }
}
