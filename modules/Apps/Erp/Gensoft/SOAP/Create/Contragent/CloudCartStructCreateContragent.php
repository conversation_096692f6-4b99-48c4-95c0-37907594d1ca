<?php

declare(strict_types=1);

/**
 * File for class CloudCartStructCreateContragent
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
/**
 * This class stands for CloudCartStructCreateContragent originally named CreateContragent
 * Meta informations extracted from the WSDL
 * - from schema : {@link http://195.34.100.202:7789/MWCloudCart/Services/CloudCartService.asmx?wsdl}
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
class CloudCartStructCreateContragent extends CloudCartWsdlClass
{
    /**
     * The contragent
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var CloudCartStructContragentInfo
     */
    public $contragent;

    /**
     * Constructor method for CreateContragent
     * @see parent::__construct()
     * @param CloudCartStructContragentInfo $_contragent
     * @return CloudCartStructCreateContragent
     */
    public function __construct($_contragent = null)
    {
        parent::__construct(['contragent' => $_contragent], false);
    }

    /**
     * Get contragent value
     * @return CloudCartStructContragentInfo|null
     */
    public function getContragent()
    {
        return $this->contragent;
    }

    /**
     * Set contragent value
     * @param CloudCartStructContragentInfo $_contragent the contragent
     * @return CloudCartStructContragentInfo
     */
    public function setContragent($_contragent)
    {
        return ($this->contragent = $_contragent);
    }

    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    #[\Override]
    public function __toString(): string
    {
        return self::class;
    }
}
