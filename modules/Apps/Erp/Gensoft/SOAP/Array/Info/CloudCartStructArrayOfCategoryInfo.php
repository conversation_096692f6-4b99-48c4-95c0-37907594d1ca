<?php

declare(strict_types=1);

/**
 * File for class CloudCartStructArrayOfCategoryInfo
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
/**
 * This class stands for CloudCartStructArrayOfCategoryInfo originally named ArrayOfCategoryInfo
 * Meta informations extracted from the WSDL
 * - from schema : {@link http://195.34.100.202:7789/MWCloudCart/Services/CloudCartService.asmx?wsdl}
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
class CloudCartStructArrayOfCategoryInfo extends CloudCartWsdlClass
{
    /**
     * The CategoryInfo
     * Meta informations extracted from the WSDL
     * - maxOccurs : unbounded
     * - minOccurs : 0
     * - nillable : true
     * @var CloudCartStructCategoryInfo
     */
    public $CategoryInfo;

    /**
     * Constructor method for ArrayOfCategoryInfo
     * @see parent::__construct()
     * @param CloudCartStructCategoryInfo $_categoryInfo
     * @return CloudCartStructArrayOfCategoryInfo
     */
    public function __construct($_categoryInfo = null)
    {
        parent::__construct(['CategoryInfo' => $_categoryInfo], false);
    }

    /**
     * Get CategoryInfo value
     * @return CloudCartStructCategoryInfo|null
     */
    public function getCategoryInfo()
    {
        return $this->CategoryInfo;
    }

    /**
     * Set CategoryInfo value
     * @param CloudCartStructCategoryInfo $_categoryInfo the CategoryInfo
     * @return CloudCartStructCategoryInfo
     */
    public function setCategoryInfo($_categoryInfo)
    {
        return ($this->CategoryInfo = $_categoryInfo);
    }

    /**
     * Returns the current element
     * @see CloudCartWsdlClass::current()
     * @return CloudCartStructCategoryInfo
     */
    #[\Override]
    public function current()
    {
        return parent::current();
    }

    /**
     * Returns the indexed element
     * @see CloudCartWsdlClass::item()
     * @param int $_index
     * @return CloudCartStructCategoryInfo
     */
    #[\Override]
    public function item($_index)
    {
        return parent::item($_index);
    }

    /**
     * Returns the first element
     * @see CloudCartWsdlClass::first()
     * @return CloudCartStructCategoryInfo
     */
    #[\Override]
    public function first()
    {
        return parent::first();
    }

    /**
     * Returns the last element
     * @see CloudCartWsdlClass::last()
     * @return CloudCartStructCategoryInfo
     */
    #[\Override]
    public function last()
    {
        return parent::last();
    }

    /**
     * Returns the element at the offset
     * @see CloudCartWsdlClass::last()
     * @param int $_offset
     * @return CloudCartStructCategoryInfo
     */
    #[\Override]
    public function offsetGet($_offset)
    {
        return parent::offsetGet($_offset);
    }

    /**
     * Returns the attribute name
     * @see CloudCartWsdlClass::getAttributeName()
     * @return string CategoryInfo
     */
    #[\Override]
    public function getAttributeName(): string
    {
        return 'CategoryInfo';
    }

    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    #[\Override]
    public function __toString(): string
    {
        return self::class;
    }
}
