<?php

declare(strict_types=1);

/**
 * File for class CloudCartStructArrayOfReturnItem
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
/**
 * This class stands for CloudCartStructArrayOfReturnItem originally named ArrayOfReturnItem
 * Meta informations extracted from the WSDL
 * - from schema : {@link http://195.34.100.202:7789/MWCloudCart/Services/CloudCartService.asmx?wsdl}
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
class CloudCartStructArrayOfReturnItem extends CloudCartWsdlClass
{
    /**
     * The ReturnItem
     * Meta informations extracted from the WSDL
     * - maxOccurs : unbounded
     * - minOccurs : 0
     * - nillable : true
     * @var CloudCartStructReturnItem
     */
    public $ReturnItem;

    /**
     * Constructor method for ArrayOfReturnItem
     * @see parent::__construct()
     * @param CloudCartStructReturnItem $_returnItem
     * @return CloudCartStructArrayOfReturnItem
     */
    public function __construct($_returnItem = null)
    {
        parent::__construct(['ReturnItem' => $_returnItem], false);
    }

    /**
     * Get ReturnItem value
     * @return CloudCartStructReturnItem|null
     */
    public function getReturnItem()
    {
        return $this->ReturnItem;
    }

    /**
     * Set ReturnItem value
     * @param CloudCartStructReturnItem $_returnItem the ReturnItem
     * @return CloudCartStructReturnItem
     */
    public function setReturnItem($_returnItem)
    {
        return ($this->ReturnItem = $_returnItem);
    }

    /**
     * Returns the current element
     * @see CloudCartWsdlClass::current()
     * @return CloudCartStructReturnItem
     */
    #[\Override]
    public function current()
    {
        return parent::current();
    }

    /**
     * Returns the indexed element
     * @see CloudCartWsdlClass::item()
     * @param int $_index
     * @return CloudCartStructReturnItem
     */
    #[\Override]
    public function item($_index)
    {
        return parent::item($_index);
    }

    /**
     * Returns the first element
     * @see CloudCartWsdlClass::first()
     * @return CloudCartStructReturnItem
     */
    #[\Override]
    public function first()
    {
        return parent::first();
    }

    /**
     * Returns the last element
     * @see CloudCartWsdlClass::last()
     * @return CloudCartStructReturnItem
     */
    #[\Override]
    public function last()
    {
        return parent::last();
    }

    /**
     * Returns the element at the offset
     * @see CloudCartWsdlClass::last()
     * @param int $_offset
     * @return CloudCartStructReturnItem
     */
    #[\Override]
    public function offsetGet($_offset)
    {
        return parent::offsetGet($_offset);
    }

    /**
     * Returns the attribute name
     * @see CloudCartWsdlClass::getAttributeName()
     * @return string ReturnItem
     */
    #[\Override]
    public function getAttributeName(): string
    {
        return 'ReturnItem';
    }

    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    #[\Override]
    public function __toString(): string
    {
        return self::class;
    }
}
