<?php

declare(strict_types=1);

/**
 * File for class CloudCartStructPropInfo
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
/**
 * This class stands for CloudCartStructPropInfo originally named PropInfo
 * Meta informations extracted from the WSDL
 * - from schema : {@link http://195.34.100.202:7789/MWCloudCart/Services/CloudCartService.asmx?wsdl}
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
class CloudCartStructPropInfo extends CloudCartWsdlClass
{
    /**
     * The ID
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 1
     * @var int
     */
    public $ID;

    /**
     * The Name
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $Name;

    /**
     * The Value
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $Value;

    /**
     * The NameEN
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $NameEN;

    /**
     * The ValueEN
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $ValueEN;

    /**
     * Constructor method for PropInfo
     * @see parent::__construct()
     * @param int $_iD
     * @param string $_name
     * @param string $_value
     * @param string $_nameEN
     * @param string $_valueEN
     * @return CloudCartStructPropInfo
     */
    public function __construct($_iD, $_name = null, $_value = null, $_nameEN = null, $_valueEN = null)
    {
        parent::__construct(['ID' => $_iD,'Name' => $_name,'Value' => $_value,'NameEN' => $_nameEN,'ValueEN' => $_valueEN], false);
    }

    /**
     * Get ID value
     * @return int
     */
    public function getID()
    {
        return $this->ID;
    }

    /**
     * Set ID value
     * @param int $_iD the ID
     * @return int
     */
    public function setID($_iD)
    {
        return ($this->ID = $_iD);
    }

    /**
     * Get Name value
     * @return string|null
     */
    public function getName()
    {
        return $this->Name;
    }

    /**
     * Set Name value
     * @param string $_name the Name
     * @return string
     */
    public function setName($_name)
    {
        return ($this->Name = $_name);
    }

    /**
     * Get Value value
     * @return string|null
     */
    public function getValue()
    {
        return $this->Value;
    }

    /**
     * Set Value value
     * @param string $_value the Value
     * @return string
     */
    public function setValue($_value)
    {
        return ($this->Value = $_value);
    }

    /**
     * Get NameEN value
     * @return string|null
     */
    public function getNameEN()
    {
        return $this->NameEN;
    }

    /**
     * Set NameEN value
     * @param string $_nameEN the NameEN
     * @return string
     */
    public function setNameEN($_nameEN)
    {
        return ($this->NameEN = $_nameEN);
    }

    /**
     * Get ValueEN value
     * @return string|null
     */
    public function getValueEN()
    {
        return $this->ValueEN;
    }

    /**
     * Set ValueEN value
     * @param string $_valueEN the ValueEN
     * @return string
     */
    public function setValueEN($_valueEN)
    {
        return ($this->ValueEN = $_valueEN);
    }

    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    #[\Override]
    public function __toString(): string
    {
        return self::class;
    }
}
