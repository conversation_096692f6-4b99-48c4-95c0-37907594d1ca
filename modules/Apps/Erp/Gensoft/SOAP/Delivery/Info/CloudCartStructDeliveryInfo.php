<?php

declare(strict_types=1);

/**
 * File for class CloudCartStructDeliveryInfo
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
/**
 * This class stands for CloudCartStructDeliveryInfo originally named DeliveryInfo
 * Meta informations extracted from the WSDL
 * - from schema : {@link http://195.34.100.202:7789/MWCloudCart/Services/CloudCartService.asmx?wsdl}
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
class CloudCartStructDeliveryInfo extends CloudCartWsdlClass
{
    /**
     * The Name
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $Name;

    /**
     * The City
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $City;

    /**
     * The Post
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $Post;

    /**
     * The Address
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $Address;

    /**
     * The Email
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $Email;

    /**
     * The Phone
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $Phone;

    /**
     * The StoreID
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $StoreID;

    /**
     * Constructor method for DeliveryInfo
     * @see parent::__construct()
     * @param string $_name
     * @param string $_city
     * @param string $_post
     * @param string $_address
     * @param string $_email
     * @param string $_phone
     * @param string $_storeId
     * @return CloudCartStructDeliveryInfo
     */
    public function __construct($_name = null, $_city = null, $_post = null, $_address = null, $_email = null, $_phone = null, $_storeId = null)
    {
        parent::__construct(['Name' => $_name,'City' => $_city,'Post' => $_post,'Address' => $_address,'Email' => $_email,'Phone' => $_phone, 'StoreID' => $_storeId], false);
    }

    /**
     * Get Name value
     * @return string|null
     */
    public function getName()
    {
        return $this->Name;
    }

    /**
     * Set Name value
     * @param string $_name the Name
     * @return string
     */
    public function setName($_name)
    {
        return ($this->Name = $_name);
    }

    /**
     * Get City value
     * @return string|null
     */
    public function getCity()
    {
        return $this->City;
    }

    /**
     * Set City value
     * @param string $_city the City
     * @return string
     */
    public function setCity($_city)
    {
        return ($this->City = $_city);
    }

    /**
     * Get Post value
     * @return string|null
     */
    public function getPost()
    {
        return $this->Post;
    }

    /**
     * Set Post value
     * @param string $_post the Post
     * @return string
     */
    public function setPost($_post)
    {
        return ($this->Post = $_post);
    }

    /**
     * Get Address value
     * @return string|null
     */
    public function getAddress()
    {
        return $this->Address;
    }

    /**
     * Set Address value
     * @param string $_address the Address
     * @return string
     */
    public function setAddress($_address)
    {
        return ($this->Address = $_address);
    }

    /**
     * Get Email value
     * @return string|null
     */
    public function getEmail()
    {
        return $this->Email;
    }

    /**
     * Set Email value
     * @param string $_email the Email
     * @return string
     */
    public function setEmail($_email)
    {
        return ($this->Email = $_email);
    }

    /**
     * Get Phone value
     * @return string|null
     */
    public function getPhone()
    {
        return $this->Phone;
    }

    /**
     * Set Phone value
     * @param string $_phone the Phone
     * @return string
     */
    public function setPhone($_phone)
    {
        return ($this->Phone = $_phone);
    }

    /**
     * Get StoreID value
     * @return string|null
     */

    public function getStoreID()
    {
        return $this->StoreID;
    }

    /**
     * Set StoreID value
     * @param string $_StoreID the StoreID
     * @return string
     */
    public function setStoreID($_StoreID)
    {
        return ($this->StoreID = $_StoreID);
    }

    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    #[\Override]
    public function __toString(): string
    {
        return self::class;
    }
}
