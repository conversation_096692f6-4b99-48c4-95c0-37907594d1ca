<?php

declare(strict_types=1);

/**
 * File for class CloudCartStructInfo
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
/**
 * This class stands for CloudCartStructInfo originally named Info
 * Meta informations extracted from the WSDL
 * - from schema : {@link http://195.34.100.202:7789/MWCloudCart/Services/CloudCartService.asmx?wsdl}
 * @package CloudCart
 * @subpackage Structs
 * <AUTHOR> Team <<EMAIL>>
 * @version 20150429-01
 * @date 2018-07-13
 */
class CloudCartStructInfo extends CloudCartWsdlClass
{
    /**
     * The Weight
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 1
     * @var double
     */
    public $Weight;

    /**
     * The ID
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $ID;

    /**
     * The Sku
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $Sku;

    /**
     * The Barcode
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var string
     */
    public $Barcode;

    /**
     * The Presence
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var CloudCartStructArrayOfQuantityInfo
     */
    public $Presence;

    /**
     * The Properties
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var CloudCartStructArrayOfPropInfo
     */
    public $Properties;

    /**
     * The BasePrice
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var CloudCartStructPriceInfo
     */
    public $BasePrice;

    /**
     * The PromoPrice
     * Meta informations extracted from the WSDL
     * - maxOccurs : 1
     * - minOccurs : 0
     * @var CloudCartStructPriceInfo
     */
    public $PromoPrice;

    /**
     * Constructor method for Info
     * @see parent::__construct()
     * @param double $_weight
     * @param string $_iD
     * @param string $_sku
     * @param string $_barcode
     * @param CloudCartStructArrayOfQuantityInfo $_presence
     * @param CloudCartStructArrayOfPropInfo $_properties
     * @param CloudCartStructPriceInfo $_basePrice
     * @param CloudCartStructPriceInfo $_promoPrice
     * @return CloudCartStructInfo
     */
    public function __construct($_weight, $_iD = null, $_sku = null, $_barcode = null, $_presence = null, $_properties = null, $_basePrice = null, $_promoPrice = null)
    {
        parent::__construct(['Weight' => $_weight,'ID' => $_iD,'Sku' => $_sku,'Barcode' => $_barcode,'Presence' => ($_presence instanceof CloudCartStructArrayOfQuantityInfo) ? $_presence : new CloudCartStructArrayOfQuantityInfo($_presence),'Properties' => ($_properties instanceof CloudCartStructArrayOfPropInfo) ? $_properties : new CloudCartStructArrayOfPropInfo($_properties),'BasePrice' => $_basePrice,'PromoPrice' => $_promoPrice], false);
    }

    /**
     * Get Weight value
     * @return double
     */
    public function getWeight()
    {
        return $this->Weight;
    }

    /**
     * Set Weight value
     * @param double $_weight the Weight
     * @return double
     */
    public function setWeight($_weight)
    {
        return ($this->Weight = $_weight);
    }

    /**
     * Get ID value
     * @return string|null
     */
    public function getID()
    {
        return $this->ID;
    }

    /**
     * Set ID value
     * @param string $_iD the ID
     * @return string
     */
    public function setID($_iD)
    {
        return ($this->ID = $_iD);
    }

    /**
     * Get Sku value
     * @return string|null
     */
    public function getSku()
    {
        return $this->Sku;
    }

    /**
     * Set Sku value
     * @param string $_sku the Sku
     * @return string
     */
    public function setSku($_sku)
    {
        return ($this->Sku = $_sku);
    }

    /**
     * Get Barcode value
     * @return string|null
     */
    public function getBarcode()
    {
        return $this->Barcode;
    }

    /**
     * Set Barcode value
     * @param string $_barcode the Barcode
     * @return string
     */
    public function setBarcode($_barcode)
    {
        return ($this->Barcode = $_barcode);
    }

    /**
     * Get Presence value
     * @return CloudCartStructArrayOfQuantityInfo|null
     */
    public function getPresence()
    {
        return $this->Presence;
    }

    /**
     * Set Presence value
     * @param CloudCartStructArrayOfQuantityInfo $_presence the Presence
     * @return CloudCartStructArrayOfQuantityInfo
     */
    public function setPresence($_presence)
    {
        return ($this->Presence = $_presence);
    }

    /**
     * Get Properties value
     * @return CloudCartStructArrayOfPropInfo|null
     */
    public function getProperties()
    {
        return $this->Properties;
    }

    /**
     * Set Properties value
     * @param CloudCartStructArrayOfPropInfo $_properties the Properties
     * @return CloudCartStructArrayOfPropInfo
     */
    public function setProperties($_properties)
    {
        return ($this->Properties = $_properties);
    }

    /**
     * Get BasePrice value
     * @return CloudCartStructPriceInfo|null
     */
    public function getBasePrice()
    {
        return $this->BasePrice;
    }

    /**
     * Set BasePrice value
     * @param CloudCartStructPriceInfo $_basePrice the BasePrice
     * @return CloudCartStructPriceInfo
     */
    public function setBasePrice($_basePrice)
    {
        return ($this->BasePrice = $_basePrice);
    }

    /**
     * Get PromoPrice value
     * @return CloudCartStructPriceInfo|null
     */
    public function getPromoPrice()
    {
        return $this->PromoPrice;
    }

    /**
     * Set PromoPrice value
     * @param CloudCartStructPriceInfo $_promoPrice the PromoPrice
     * @return CloudCartStructPriceInfo
     */
    public function setPromoPrice($_promoPrice)
    {
        return ($this->PromoPrice = $_promoPrice);
    }

    /**
     * Method returning the class name
     * @return string __CLASS__
     */
    #[\Override]
    public function __toString(): string
    {
        return self::class;
    }
}
