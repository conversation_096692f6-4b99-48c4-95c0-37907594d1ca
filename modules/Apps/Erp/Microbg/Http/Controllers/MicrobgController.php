<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Microbg\Http\Controllers;

use App\Exceptions\Error;
use App\Helper\Plan;
use App\Models\Gate\Cart;
use App\Models\Gate\PlanFeaturePack;
use App\Models\Setting\ApiKey;
use App\Traits\IController;
use Braintree\Exception\NotFound;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Modules\Apps\Abstractions\Controllers\AbstractAppsController;
use Modules\Apps\Erp\Microbg\Manager;
use Modules\Apps\Erp\Microbg\Request\SettingsRequest;
use Throwable;

/**
 * Class ProductReview
 * @package App\Http\Controllers\Sitecp\Apps
 */
class MicrobgController extends AbstractAppsController
{
    use IController;

    /**
     * MicrobgController constructor.
     * @param Manager $manager
     * @throws Error
     */
    public function __construct(Manager $manager)
    {
        parent::__construct($manager);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     */
    #[\Override]
    public function settings(): JsonResponse
    {
        if (empty($this->manager->getSetting('is_registered'))) {
            return response()->json([
                'is_subscribed' => Plan::enabled('microbg_subscription'),
                'show_button' => 1,
                'info' => $this->getAppInfo()
            ]);
        }

        $data_format = null;
        if (!empty($this->manager->getSetting('PaymentToDate'))) {
            $data_format = Carbon::createFromFormat('Y-m-d H:i:s', $this->manager->getSetting('PaymentToDate'))->format('d.m.Y H:i:s');
        }

        return response()->json([
            'expired' => $data_format,
            'order_id' => $this->manager->getSetting('OrderId'),
            'api_key' => ApiKey::first()->key,
            'host' => site()->getSiteUrl('primary'),
            'info' => $this->getAppInfo()
        ]);
    }

    /**
     * @param SettingsRequest $request
     * @return JsonResponse
     * @throws GuzzleException
     * @throws Throwable
     */
    public function saveSettingsCustom(SettingsRequest $request): JsonResponse
    {
        try {
            $data = $request->all();
            $this->manager->updateSettings($data);

            return $this->checkRegistration($data);
        } catch (Exception $exception) {
            return response()->json([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }
    }

    /**
     * @param $data
     * @return JsonResponse
     * @throws Error
     * @throws GuzzleException
     * @throws Throwable
     * @throws NotFound
     */
    protected function checkRegistration($data): JsonResponse
    {
        $client = new Client('Check');
        $response = $client->sendRequest($data);

        if ($response['Success'] == 1) {
            if (!Plan::enabled('microbg_subscription')) {
                Cart::initFromPromo(
                    [
                        'feature' => [
                            [
                                'id' => PlanFeaturePack::findByMap(['microbg_subscription'])->id,
                                'value' => '1',

                            ]
                        ],
                    ]
                );
                session()->put('redirect_after_install', route('apps.microbg', ['status' => 'success']));

                return response()->json([
                    'redirect' => route('admin.checkout')
                ], 32);
            }

            return $this->Registration($data);
        } else {
            return response()->json([
                'message' => array_values($response['Errors'])[0],
            ], 503);
        }
    }

    /**
     * @param null $data
     * @return JsonResponse
     * @throws Error
     * @throws GuzzleException
     * @throws Throwable
     */
    protected function Registration($data = null): JsonResponse
    {
        if (empty($data)) {
            $data['user'] = $this->manager->getSetting('user');
            $data['company'] = $this->manager->getSetting('company');
        }

        $client = new Client('Create');
        $response = $client->sendRequest($data);
        if ($response['Success'] == 1) {
            if (!empty($response['OrderId'])) {
                $this->manager->updateSettings(['is_registered' => 1, 'PaymentToDate' => $response['PaymentToDate'], 'OrderId' => $response['OrderId']]);
            }

            return response()->json([]);
        } else {
            return response()->json([
                'message' => array_values($response['Errors'])[0],
            ], 503);
        }
    }

    /**
     * @return JsonResponse
     */
    public function isInfo(): JsonResponse
    {
        return response()->json([
            'api_key' => ApiKey::first()->key,
            'host' => site()->getSiteUrl('primary'),
            'registration' => [
                'user' => [
                    'name' => setting('company_mol'),
                    'email' => setting('site_email'),
                    'phone' => setting('site_phone'),
                ],
                'company' => [
                    'name' => setting('company_name'),
                    'eik' => setting('company_bulstat'),
                    'vat' => setting('company_vat'),
                    'place' => setting('site_city'),
                    'address' => setting('site_street'),
                    'mol' => setting('company_mol'),
                    'phone' => setting('site_phone'),
                    'email' => setting('site_email'),
                ]
            ]
        ]);
    }

    /**
     * @return \Illuminate\Contracts\View\View|RedirectResponse
     * @throws Error
     */
    public function info()
    {
        if (!$this->manager->isInstalled()) {
            return redirect('404');
        }

        return response()->view('resources::vue-sitecp');
    }
}
