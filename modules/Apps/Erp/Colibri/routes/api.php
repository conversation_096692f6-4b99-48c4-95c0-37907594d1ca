<?php

declare(strict_types=1);

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'api/colibri', 'middleware' => 'hasApiPermission:apps'], function (Router $router): void {
    $router->post('install', ['as' => 'apps.colibri.install', 'uses' => 'SettingsController@install']);
    $router->post('uninstall', ['as' => 'apps.colibri.uninstall', 'uses' => 'SettingsController@uninstall']);
    $router->match(['get', 'post'], 'credentials', ['as' => 'apps.api.colibri.valid.credentials', 'uses' => 'SettingsController@validateCredentials']);

    $router->group(['prefix' => 'settings'], function (Router $router): void {
        $router->get('/', ['as' => 'apps.api.colibri.settings', 'uses' => 'SettingsController@settings']);
        $router->post('/', ['as' => 'apps.pi.colibri.settings', 'uses' => 'SettingsController@settingsSave']);
    });

    $router->group(['prefix' => 'status'], function (Router $router): void {
        $router->get('/', ['as' => 'apps.api.colibri.status', 'uses' => 'StatusController@getStatus']);
        $router->post('change/{status}', ['as' => 'apps.api.colibri.status.change', 'uses' => 'StatusController@setActive']);
        $router->get('fetch', ['as' => 'apps.api.colibri.status.fetch', 'uses' => 'StatusController@getStatusFetch']);
    });

    $router->group(['prefix' => 'products'], function (Router $router): void {
        $router->get('/', ['as' => 'apps.api.colibri.products', 'uses' => 'ProductsController@products']);
        $router->delete('{id}', ['as' => 'apps.api.colibri.product.delete', 'uses' => 'ProductsController@productDelete'])
            ->where('id', '.*');
        $router->post('/', ['as' => 'apps.api.colibri.product.update', 'uses' => 'ProductsController@updateExternalId']);

    });

    $router->group(['prefix' => 'category-map'], function (Router $router): void {
        $router->get('/', ['as' => 'apps.api.colibri.category-map', 'uses' => 'CategoryMapController@index']);
        $router->get('meta/{id?}', ['as' => 'apps.api.colibri.category-map.meta', 'uses' => 'CategoryMapController@meta']);
        $router->post('/', ['as' => 'apps.api.colibri.category-map.store', 'uses' => 'CategoryMapController@store']);
        $router->put('{id}', ['as' => 'apps.api.colibri.category-map.update', 'uses' => 'CategoryMapController@update']);
        $router->delete('{id}', ['as' => 'apps.api.colibri.category-map.delete', 'uses' => 'CategoryMapController@delete']);
    });
});
