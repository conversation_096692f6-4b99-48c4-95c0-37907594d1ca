<?php

declare(strict_types=1);

/**
 * File for class ColibriERPStructErpOrderSDoc
 *
 * PHP Version 5
 *
 * @category   ColibriERP
 * @package    ColibriERP
 * @subpackage Structs
 * <AUTHOR> <<EMAIL>>
 * @license    https://opensource.org/licenses/MIT MIT
 * @version    GIT: 1.0.0 In development.
 * @link       http://simexis.com
 * @date       2017-06-01
 */
/**
 * This class stands for ColibriERPStructErpOrderSDoc
 *  originally named erpOrderSDoc
 *
 * PHP Version 5
 *
 * @category   ColibriERP
 * @package    ColibriERP
 * @subpackage Structs
 * <AUTHOR> <<EMAIL>>
 * @license    https://opensource.org/licenses/MIT MIT
 * @version    GIT: 1.0.0 In development.
 * @link       http://simexis.com
 * @date       2017-06-01
 */
class ColibriERPStructErpOrderSDoc extends ColibriERPWsdlClass
{
    /**
     * The idDoc
     * - doc : ERP internal document number
     *
     * @var integer
     */
    public $idDoc;

    /**
     * The DocCode
     * - doc : Document code
     *
     * @var string
     */
    public $DocCode;

    /**
     * The DocNum
     * - doc : Document number
     *
     * @var string
     */
    public $DocNum;

    /**
     * The DocDate
     * - doc : Document date
     *
     * @var date
     */
    public $DocDate;

    /**
     * The QtyS
     * - doc : Total quantity of ordered items included in this document
     *
     * @var double
     */
    public $QtyS;

    /**
     * Constructor method for erpOrderSDoc
     *
     * @see parent::__construct()
     *
     * @param integer $_idDoc   expect integer
     * @param string  $_docCode expect string
     * @param string  $_docNum  expect string
     * @param date    $_docDate expect date
     * @param double  $_qtyS    expect double
     *
     * @return ColibriERPStructErpOrderSDoc
     */
    public function __construct($_idDoc = null,$_docCode = null,$_docNum = null,$_docDate = null,$_qtyS = null)
    {
        parent::__construct(['idDoc'=>$_idDoc,'DocCode'=>$_docCode,'DocNum'=>$_docNum,'DocDate'=>$_docDate,'QtyS'=>$_qtyS], false);
    }

    /**
     * Get idDoc value
     *
     * @return integer|null
     */
    public function getIdDoc()
    {
        return $this->idDoc;
    }

    /**
     * Set idDoc value
     *
     * @param integer $_idDoc the idDoc
     *
     * @return integer
     */
    public function setIdDoc($_idDoc)
    {
        return ($this->idDoc = $_idDoc);
    }

    /**
     * Get DocCode value
     *
     * @return string|null
     */
    public function getDocCode()
    {
        return $this->DocCode;
    }

    /**
     * Set DocCode value
     *
     * @param string $_docCode the DocCode
     *
     * @return string
     */
    public function setDocCode($_docCode)
    {
        return ($this->DocCode = $_docCode);
    }

    /**
     * Get DocNum value
     *
     * @return string|null
     */
    public function getDocNum()
    {
        return $this->DocNum;
    }

    /**
     * Set DocNum value
     *
     * @param string $_docNum the DocNum
     *
     * @return string
     */
    public function setDocNum($_docNum)
    {
        return ($this->DocNum = $_docNum);
    }

    /**
     * Get DocDate value
     *
     * @return date|null
     */
    public function getDocDate()
    {
        return $this->DocDate;
    }

    /**
     * Set DocDate value
     *
     * @param date $_docDate the DocDate
     *
     * @return date
     */
    public function setDocDate($_docDate)
    {
        return ($this->DocDate = $_docDate);
    }

    /**
     * Get QtyS value
     *
     * @return double|null
     */
    public function getQtyS()
    {
        return $this->QtyS;
    }

    /**
     * Set QtyS value
     *
     * @param double $_qtyS the QtyS
     *
     * @return double
     */
    public function setQtyS($_qtyS)
    {
        return ($this->QtyS = $_qtyS);
    }

    /**
     * Method returning the class name
     *
     * @return string __CLASS__
     */
    #[\Override]
    public function __toString(): string
    {
        return self::class;
    }
}
