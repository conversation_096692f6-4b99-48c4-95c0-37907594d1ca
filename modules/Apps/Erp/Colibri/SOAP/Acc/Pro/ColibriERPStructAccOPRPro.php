<?php

declare(strict_types=1);

/**
 * File for class ColibriERPStructAccOPRPro
 *
 * PHP Version 5
 *
 * @category   ColibriERP
 * @package    ColibriERP
 * @subpackage Structs
 * <AUTHOR> <<EMAIL>>
 * @license    https://opensource.org/licenses/MIT MIT
 * @version    GIT: 1.0.0 In development.
 * @link       http://simexis.com
 * @date       2017-06-01
 */
/**
 * This class stands for ColibriERPStructAccOPRPro
 *  originally named accOPRPro
 *
 * PHP Version 5
 *
 * @category   ColibriERP
 * @package    ColibriERP
 * @subpackage Structs
 * <AUTHOR> <<EMAIL>>
 * @license    https://opensource.org/licenses/MIT MIT
 * @version    GIT: 1.0.0 In development.
 * @link       http://simexis.com
 * @date       2017-06-01
 */
class ColibriERPStructAccOPRPro extends ColibriERPWsdlClass
{
    /**
     * The ID
     *
     * @var string
     */
    public $ID;

    /**
     * The Name
     *
     * @var string
     */
    public $Name;

    /**
     * Constructor method for accOPRPro
     *
     * @see parent::__construct()
     *
     * @param string $_iD   expect string
     * @param string $_name expect string
     *
     * @return ColibriERPStructAccOPRPro
     */
    public function __construct($_iD = null,$_name = null)
    {
        parent::__construct(['ID'=>$_iD,'Name'=>$_name], false);
    }

    /**
     * Get ID value
     *
     * @return string|null
     */
    public function getID()
    {
        return $this->ID;
    }

    /**
     * Set ID value
     *
     * @param string $_iD the ID
     *
     * @return string
     */
    public function setID($_iD)
    {
        return ($this->ID = $_iD);
    }

    /**
     * Get Name value
     *
     * @return string|null
     */
    public function getName()
    {
        return $this->Name;
    }

    /**
     * Set Name value
     *
     * @param string $_name the Name
     *
     * @return string
     */
    public function setName($_name)
    {
        return ($this->Name = $_name);
    }

    /**
     * Method returning the class name
     *
     * @return string __CLASS__
     */
    #[\Override]
    public function __toString(): string
    {
        return self::class;
    }
}
