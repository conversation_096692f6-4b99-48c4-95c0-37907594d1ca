<?php

declare(strict_types=1);

/**
 * File for class ColibriERPStructAcc
 *
 * PHP Version 5
 *
 * @category   ColibriERP
 * @package    ColibriERP
 * @subpackage Structs
 * <AUTHOR> <<EMAIL>>
 * @license    https://opensource.org/licenses/MIT MIT
 * @version    GIT: 1.0.0 In development.
 * @link       http://simexis.com
 * @date       2017-06-01
 */
/**
 * This class stands for ColibriERPStructAcc
 *  originally named acc
 *
 * PHP Version 5
 *
 * @category   ColibriERP
 * @package    ColibriERP
 * @subpackage Structs
 * <AUTHOR> <<EMAIL>>
 * @license    https://opensource.org/licenses/MIT MIT
 * @version    GIT: 1.0.0 In development.
 * @link       http://simexis.com
 * @date       2017-06-01
 */
class ColibriERPStructAcc extends ColibriERPWsdlClass
{
    /**
     * The aNum
     *
     * @var string
     */
    public $aNum;

    /**
     * The aName
     *
     * @var string
     */
    public $aName;

    /**
     * Constructor method for acc
     *
     * @see parent::__construct()
     *
     * @param string $_aNum  expect string
     * @param string $_aName expect string
     *
     * @return ColibriERPStructAcc
     */
    public function __construct($_aNum = null,$_aName = null)
    {
        parent::__construct(['aNum'=>$_aNum,'aName'=>$_aName], false);
    }

    /**
     * Get aNum value
     *
     * @return string|null
     */
    public function getANum()
    {
        return $this->aNum;
    }

    /**
     * Set aNum value
     *
     * @param string $_aNum the aNum
     *
     * @return string
     */
    public function setANum($_aNum)
    {
        return ($this->aNum = $_aNum);
    }

    /**
     * Get aName value
     *
     * @return string|null
     */
    public function getAName()
    {
        return $this->aName;
    }

    /**
     * Set aName value
     *
     * @param string $_aName the aName
     *
     * @return string
     */
    public function setAName($_aName)
    {
        return ($this->aName = $_aName);
    }

    /**
     * Method returning the class name
     *
     * @return string __CLASS__
     */
    #[\Override]
    public function __toString(): string
    {
        return self::class;
    }
}
