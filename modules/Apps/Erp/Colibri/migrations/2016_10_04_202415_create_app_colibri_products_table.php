<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAppColibriProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        if (!Schema::hasTable('@app_colibri_products')) {
            Schema::create('@app_colibri_products', function (Blueprint $table): void {
                $table->increments('id')->unsigned();
                $table->integer('category_id')->unsigned()->index();
                $table->longText('data');
                $table->string('GNum')->index();
                $table->string('job_id')->index()->nullable()->default(null);
                $table->unsignedTinyInteger('try')->index()->default(0);
                $table->text('exception')->nullable()->default(null);
                $table->integer('percentage')->unsigned()->default(0);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('@app_colibri_products');
    }
}
