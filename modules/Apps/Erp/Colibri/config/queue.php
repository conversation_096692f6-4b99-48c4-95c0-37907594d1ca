<?php

declare(strict_types=1);

use Modules\Apps\Erp\Colibri\Jobs\FetchCategories;
use Modules\Apps\Erp\Colibri\Jobs\GetProducts;
use Modules\Apps\Erp\Colibri\Jobs\UpdateCategories;
use Modules\Apps\Erp\It4profit\It4profitManager;

return [

    'colibri_parse' => [
        'mapping' => GetProducts::class,
        'interval' => 86400,
        'single' => false,
        'visible' => true,
        'queue' => 'import',
        'force_queue' => 'import1',
        'app_key' => It4profitManager::APP_KEY,
    ],

    'colibri_categories' => [
        'mapping' => UpdateCategories::class,
        'interval' => 10800,
        'single' => false,
        'visible' => false,
        'queue' => 'import',
        'force_queue' => 'import1',
        'app_key' => It4profitManager::APP_KEY,
    ],

    'colibri_categories_fetch' => [
        'mapping' => FetchCategories::class,
        'interval' => null,
        'single' => false,
        'visible' => false,
        'queue' => 'import8',
    ],

];
