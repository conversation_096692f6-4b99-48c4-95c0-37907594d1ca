<div class="page-breadcrumb clearfix">
    {$breadcrumb = [
    [
    'title' => "{t}sidebar.apps{/t}",
    'href' => "{route('admin.apps')}"
    ],
    [
    'title' => "{t}colibri.info.title{/t}"
    ]
    ]}

    {include file="includes/breadcrumb.tpl"}
</div>

<div class="app-wrapper">
    <div class="wrapper1">
        <div class="app-wrapper">
            {include file="{Apps::templatesPath()}includes/app-intro.tpl" title="{t}colibri.info.title{/t}" text=""}
            {include file="{Apps::templatesPath()}includes/app-icons.tpl"  app_icon='icon-colibri.png'}
            {include file="{Apps::templatesPath()}includes/app-addition.tpl" addition=$smarty.capture.app_addition}
        </div>
    </div>
</div>

<div class="container-small">
    <div class="box">
        <div class="box-section">
            <div class="row form-group">
                <div class="col-md-12">

                    <div class="alert alert-warning">
                        <div>
                            {t}universum::app.last.update{/t} {if !empty($last_update)} {{$last_update}} {else} {t}universum::app.start.waiting{/t} {/if}
                            <br />
                            {t}universum::app.next.update{/t}  {if !empty($next_update)} {{$next_update}} {else} {t}universum::app.start.waiting{/t} {/if}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-6 text-center">
                            <a class="btn btn-primary" href="{route('apps.colibri')}">{t}xml_feed.button.edit_settings{/t}</a>
                        </div>
                        <div class="col-xs-6 text-center">
                            {if $status == 1}
                                <h3><span class="badge badge-green">{t}global.text.active_status{/t}</span></h3>
                            {else}
                                <a class="btn btn-warning" href="{route('apps.colibri.start')}">{t}global.text.start_queue{/t}</a>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>