<?php

declare(strict_types=1);

use Modules\Apps\Erp\Zeron\Providers\ZeronProvider;
use Modules\Apps\Erp\Zeron\Providers\ZeronListenerProvider;

return [
    'name' => 'zeron',
    'providers' => [
        ZeronProvider::class,
        ZeronListenerProvider::class,
    ],
    'aliases' => [
        \Modules\Zeron\ZeronManager::class => \Modules\Apps\Erp\Zeron\ZeronManager::class,
        \Modules\Zeron\Jobs\GetProducts::class => \Modules\Apps\Erp\Zeron\Jobs\GetProducts::class,
        \Modules\Zeron\Jobs\ImportProducts::class => \Modules\Apps\Erp\Zeron\Jobs\ImportProducts::class,
        \Modules\Zeron\Jobs\SendOrder::class => \Modules\Apps\Erp\Zeron\Jobs\SendOrder::class,
    ]
];
