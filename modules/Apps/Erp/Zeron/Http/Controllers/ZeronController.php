<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Zeron\Http\Controllers;

use App\Exceptions\Error;
use App\Models\Store\Shop;
use App\Traits\IController;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Modules\Apps\Abstractions\Controllers\AbstractAppsController;
use Modules\Apps\Erp\Zeron\Request\SettingsRequest;
use Modules\Apps\Erp\Zeron\Request\ValidateLogin;
use Modules\Apps\Erp\Zeron\ZeronManager;
use Modules\Importer\Traits\ProgressInfoController;

/**
 * Class ZeronController
 * @package App\Http\Controllers\Sitecp\Apps
 * @deprecated
 */
class ZeronController extends AbstractAppsController
{
    use IController;
    use ProgressInfoController {
        ProgressInfoController::progress as protectedParent;
    }

    /**
     * ZeronController constructor.
     * @param ZeronManager $manager
     * @throws Error
     */
    public function __construct(ZeronManager $manager)
    {
        parent::__construct($manager);
    }

    /**
     * @param SettingsRequest $request
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|RedirectResponse|Response|\Illuminate\Routing\Redirector
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Throwable
     */
    public function saveSettings(SettingsRequest $request)
    {

        try {
            $data = Arr::only($request->input(), [
                'active',
                'sync_url',
                'compare_by',
                'send_order',
                'username',
                'password',
                'database',
                'warehouses',
                'category_id',
                'product_status',
                'warehouse_order',
                'action',
                'create_warehouses',
            ]);
            if (!empty($data['create_warehouses'])) {
                $warehouse_map = [];
                $warehouses = (new Client())->getQuantity();

                foreach ($warehouses['Destination']['Operation']['DataSet'] as $warehouse_client) {
                    if (in_array($warehouse_client['Storehouse'], $data['warehouses'])) {
                        $warehouse_map[$warehouse_client['Storehouse']] = Shop::firstOrCreate(['title' => $warehouse_client['StorehouseName']])->id;
                    }
                }

                $data['warehouses_map'] = $warehouse_map;
            }

            $this->manager->updateSettings($data);
            $this->manager->updateActive((int)$request->input('active'));
            if ((int)$request->input('active') == 1) {
                $this->manager->setWorking(1);
                return $this->to(route('apps.zeron.status'));
            }

            return new Response([
                'msg' => __('apps.successfully.configured'),
                'status' => 'success'
            ]);
        } catch (Exception $exception) {
            return response([
                'msg' => $exception->getMessage(),
                'status' => 'error'
            ]);
        }

    }

    /**
     * @return RedirectResponse|Response|\Illuminate\Routing\Redirector
     * @throws Error
     * @throws \Throwable
     */
    public function start()
    {
        $this->manager->setWorking(1);
        return $this->to(route('apps.zeron'));
    }

    /**
     * @param ValidateLogin $request
     * @return Response
     * @throws Error
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Throwable
     */
    public function isValidLogin(ValidateLogin $request): \Illuminate\Http\Response
    {
        $data = Arr::only($request->input(), [
            'sync_url',
            'username',
            'password',
            'database',
            'site_id'
        ]);
        $this->manager->updateSettings($data);
        $client = new Client();
        $warehouses = [];
        if (!empty($warehouses = $client->getQuantity())) {
            $warehouses = $warehouses['Destination']['Operation']['DataSet'];
            return new Response(['status' => 'success', 'warehouses' => $warehouses]);
        }

        return new Response(['status' => 'error', 'msg' => __('acscourier.invalid_login')]);
    }
}
