<?php

declare(strict_types=1);

use Modules\Apps\Erp\Zeron\Jobs\GetProducts;
use Modules\Apps\Erp\Zeron\Jobs\ImportProducts;
use Modules\Apps\Erp\Zeron\Jobs\SendOrder;

return [
    'zeron_products' => [
        'mapping' => GetProducts::class,
        'interval' => 28800,
        'single' => false,
        'visible' => true,
        'queue' => 'import',
    ],
    'zeron_import' => [
        'mapping' => ImportProducts::class,
        'inverval' => null,
        'single' => false,
        'visible' => true,
        'queue' => 'import',
    ],
    'zeron_send_order' => [
        'mapping' => SendOrder::class,
        'inverval' => null,
        'single' => false,
        'visible' => true,
        'queue' => 'order-events5',
    ]
];
