<?php

declare(strict_types=1);

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'api/zeron', 'middleware' => 'hasApiPermission:apps'], function (Router $router): void {
    $router->post('install', ['as' => 'apps.zeron.install', 'uses' => 'SettingsController@install']);
    $router->post('uninstall', ['as' => 'apps.zeron.uninstall', 'uses' => 'SettingsController@uninstall']);
    $router->match(['get', 'post'], 'credentials', ['as' => 'apps.api.zeron.credentials', 'uses' => 'SettingsController@validateCredentials']);

    $router->group(['prefix' => 'settings'], function (Router $router): void {
        $router->get('/', ['as' => 'zeron.api.settings', 'uses' => 'SettingsController@settings']);
        $router->post('/', ['uses' => 'SettingsController@settingsSave']);

        $router->get('warehouses', ['as' => 'apps.api.zeron.warehouses', 'uses' => 'SettingsController@getWarehouses']);
    });

    $router->group(['prefix' => 'status'], function (Router $router): void {
        $router->get('/', ['as' => 'apps.api.zeron.status', 'uses' => 'StatusController@getStatus']);
        $router->post('change/{status}', ['as' => 'apps.api.zeron.status.change', 'uses' => 'StatusController@setActive']);
    });

    $router->group(['prefix' => 'products'], function (Router $router): void {
        $router->get('/', ['as' => 'apps.api.zeron.products', 'uses' => 'ProductsController@products']);
        $router->delete('{id}', ['as' => 'apps.api.zeron.product.delete', 'uses' => 'ProductsController@productDelete'])
            ->where('id', '.*');
        $router->post('/', ['as' => 'apps.api.zeron.product.update', 'uses' => 'ProductsController@updateExternalId']);

    });
});
