<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\BrandsDistribution\Http\Controllers\Admin;

use App\Exceptions\Error;
use Modules\Apps\Erp\BrandsDistribution\BrandsDistribution;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpStatusController;

/**
 * Class StatusController
 * @package Modules\Apps\Erp\BrandsDistribution\Http\Controllers\Admin
 */
class StatusController extends AbstractErpStatusController
{
    /**
     * @param BrandsDistribution $manager
     * @throws Error
     */
    public function __construct(BrandsDistribution $manager)
    {
        parent::__construct($manager);
    }
}
