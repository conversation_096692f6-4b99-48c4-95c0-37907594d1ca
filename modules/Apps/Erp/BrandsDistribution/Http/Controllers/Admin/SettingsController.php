<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\BrandsDistribution\Http\Controllers\Admin;

use App\Exceptions\Error;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use <PERSON><PERSON>les\Apps\Erp\BrandsDistribution\BrandsDistribution;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpSettingsController;
use Modules\Core\Core\Exceptions\CouldNotConnectToServer;

/**
 * Class SettingsController
 * @package Modules\Apps\Erp\BrandsDistribution\Http\Controllers\Admin
 */
class SettingsController extends AbstractErpSettingsController
{
    protected $booleans = [
        'update_name', 'product_active',
        'gender_to_category', 'update_category'
    ];

    protected $arrays = [
        'name_constructor'
    ];

    /**
     * @param BrandsDistribution $manager
     * @throws Error
     */
    public function __construct(BrandsDistribution $manager)
    {
        parent::__construct($manager);
    }

    public function catalog(): JsonResponse
    {
        $results = $this->manager->getConnection()->getCatalogs();
        if (!empty($results['status'])) {
            return response()->json([]);
        }

        return response()->json(collect($results)->map(fn($catalog): array => [
            'id' => $catalog['_id'],
            'name' => $catalog['name'],
        ])->sortBy('name')->values());
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function manipulateSettingsRequest(Request $request)
    {
        if (!$request->input('gender_to_category')) {
            $request->offsetSet('update_category', 0);
        }

        if ($request->input('user_catalog') != $this->manager->getSetting('user_catalog') || empty($this->manager->getSetting('user_catalog_name'))) {
            $results = collect($this->manager->getConnection()->getCatalogs())->pluck('name', '_id');
            $request->offsetSet('user_catalog_name', $results->get($request->input('user_catalog')));
        }
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function validateSettingsRequest(Request $request): void
    {
        $this->validate($request, [
            'email' => 'required_if:active,1|email',
            'password' => 'required_if:active,1',
        ], [
            'email.required_if' => 'Email is required',
            'email.email' => 'Value is not valid email address',
            'password.required_if' => 'Password is required',
        ]);

        if ($request->input('active')) {
            if (!$this->manager->validateCredentials($request->only($this->manager->getCredentialsFields()))) {
                throw new CouldNotConnectToServer();
            }
        }
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function validateCredentialsRequest(Request $request): void
    {
        $this->validate($request, [
            'email' => 'required|email',
            'password' => 'required',
        ], [
            'email.required' => 'Email is required',
            'email.email' => 'Value is not valid email address',
        ]);
    }
}
