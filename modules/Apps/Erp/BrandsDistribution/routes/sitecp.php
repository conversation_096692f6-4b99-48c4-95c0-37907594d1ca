<?php

declare(strict_types=1);

Route::group(['prefix' => 'apps/brands-distribution'], function (): void {
    Route::match(['get', 'post'], '/', ['as' => 'apps.brands-distribution', 'uses' => 'AppController@index']);
    Route::post('install', ['as' => 'apps.brands-distribution.install', 'uses' => 'AppController@install']);
    Route::any('uninstall', ['as' => 'apps.brands-distribution.uninstall', 'uses' => 'AppController@uninstall']);
    Route::get('config', ['as' => 'apps.brands-distribution.config', 'uses' => 'AppController@config']);
    Route::post('config', ['as' => 'apps.brands-distribution.settings', 'uses' => 'AppController@settings']);
    Route::post('cancel', ['as' => 'apps.brands-distribution.cancel', 'uses' => 'AppController@cancel']);
    Route::get('name-constructor-auto-complete', ['as' => 'apps.brands-distribution.nca', 'uses' => 'AppController@nameConstructorAutoComplete']);
    Route::get('catalogs', ['as' => 'apps.brands-distribution.catalogs', 'uses' => 'AppController@catalogs']);
});
