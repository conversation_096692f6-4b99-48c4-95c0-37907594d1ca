<?php

declare(strict_types=1);

/**
 * Created by PhpStorm.
 * User: joro
 * Date: 12.6.2018 г.
 * Time: 12:50 ч.
 */

namespace Modules\Apps\Erp\It4profit\Jobs;

use App\Jobs\Job;
use App\Models\Router\Exceptions;
use App\Traits\PlanLimit;
use Modules\Apps\Erp\It4profit\It4profitManager;

class UpdateCategories extends Job
{
    use PlanLimit;

    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'import1';

    public function __construct()
    {
        $this->site_id = site('site_id');
    }

    /**
     * {@inheritdoc}
     */
    public function execute()
    {
        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return static::SITE_MAINTENANCE;
        }

        // move job to another platform
        if (!allowSiteByPlatform()) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', platform(), sitePlatform()));
            return static::WRONG_PLATFORM;
        }

        $this->info('Start job for get categories!');
        $manager = new It4profitManager();
        if (!$manager->isInstalled() || !$manager->isConfigured()) {
            $this->warning('Application is not installed or is not configured!');
            return static::DESTROY;
        }

        try {
            $manager->updateRequiredData();
        } catch (\Throwable $throwable) {
            Exceptions::createFromThrowable($throwable, 'It4profit update categories');
        }

        return true;
    }
}
