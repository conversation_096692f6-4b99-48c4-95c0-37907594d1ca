{include file="../dependency/settings.tpl"}
{include file="../includes/app-breadcrumb.tpl" active=__('it4profit.info.title')}

<div class="wrapper">
    <div class="app-wrapper">
        {include file="../includes/app-intro.tpl" title=__('it4profit.header.setting') text=__('it4profit.help.setting')}
        {include file="../includes/app-icons.tpl"  app_icon='icon-asbis.png'}
    </div>

    <div class="container-small">
        <form action="{route('apps.it4profit.settings')}" id="editForm" role="form" data-redirect="{route('apps.it4profit')}">
            <div class="box">
                <div class="box-section">
                    <div class="row">
                        <div class="col-md-12">
                                <div class="form-group">
                                    <label class="control-label">{t}apps.label.enabled{/t}</label>
                                    <input name="active" type="checkbox" class="switch"
                                           value="1"{if $active} checked="checked"{/if} />
                                </div>

                                <div class="form-group">
                                    <label for="username" class="control-label">{t}it4profit.label.username{/t}</label>
                                    <i class="glyphicon glyphicon-info-sign tooltips" title="{t}it4profit.help.username{/t}" data-placement="top"></i>

                                    <input name="username" id="username" type="text"
                                           value="{$username}" class="form-control" data-autofocus
                                           placeholder="{t}it4profit.ph.username{/t}"/>
                                </div>

                                <div class="form-group">
                                    <label for="password" class="control-label">{t}it4profit.label.password{/t}</label>
                                    <i class="glyphicon glyphicon-info-sign tooltips" title="{t}it4profit.help.password{/t}" data-placement="top"></i>

                                    <input name="password" id="password" type="text"
                                           value="{$password}" class="form-control"
                                           placeholder="{t}it4profit.ph.password{/t}"/>
                                </div>

                                <div class="form-group">
                                    <label for="percentage" class="control-label">{t}it4profit.label.percentage{/t}</label>
                                    <i class="glyphicon glyphicon-info-sign tooltips" title="{t}it4profit.help.percentage{/t}" data-placement="top"></i>

                                    <input name="percentage" id="percentage" type="number" step="1" min="0" max="100"
                                           value="{$percentage}" class="form-control" data-autofocus
                                           placeholder="{t}it4profit.ph.percentage{/t}"/>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group text-right">
                <button class="btn btn-primary" type="submit" id="editSubmit">{t}global.save{/t}</button>
            </div>
        </form>
    </div>
</div>