<?php

declare(strict_types=1);

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateProductsTemp extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        if (!Schema::hasTable('@app_posmaster_products_temp')) {
            Schema::create('@app_posmaster_products_temp', function (Blueprint $table): void {
                $table->increments('id');
                $table->string('name')->nullable()->default(null);
                $table->string('code')->index('idx_code')->nullable()->default(null);
                $table->string('barcode')->nullable()->default(null);
                $table->char('hash', 32)->index('idx_hash')->nullable()->default(null);
                $table->text('json');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('@app_posmaster_products_temp');
    }
}
