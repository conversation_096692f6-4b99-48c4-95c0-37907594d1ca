<?php

declare(strict_types=1);

use Illuminate\Routing\Router;

Route::group(['prefix' => 'api/posmaster'], function (Router $router): void {
    $router->post('install', ['as' => 'apps.api.posmaster.install', 'uses' => 'PosMasterController@install']);
    $router->post('uninstall', ['as' => 'apps.api.posmaster.uninstall', 'uses' => 'PosMasterController@uninstall']);
    $router->get('settings', ['as' => 'apps.api.posmaster.settings', 'uses' => 'PosMasterController@settings']);
    $router->post('settings', ['as' => 'apps.api.posmaster.settings', 'uses' => 'PosMasterController@settingsSave']);
    $router->get('start', ['as' => 'apps.api.posmaster.start', 'uses' => 'PosMasterController@start']);
    $router->get('status', ['as' => 'apps.api.posmaster.status', 'uses' => 'PosMasterController@getStatus']);
    $router->post('status/change/{status}', ['as' => 'apps.api.posmaster.status.change', 'uses' => 'PosMasterController@setActive']);
});
