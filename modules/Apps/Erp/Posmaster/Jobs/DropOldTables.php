<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Posmaster\Jobs;

use App\Jobs\Job;
use <PERSON><PERSON><PERSON>\Posmaster\PosMasterManager;

class DropOldTables extends Job
{
    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'system3';

    /**
     * @param string $tableSuffix
     * @return mixed
     */
    public function __construct(protected string $tableSuffix)
    {
        $this->site_id = site('site_id');
    }

    public function execute()
    {
        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return [static::SITE_MAINTENANCE, ['tableSuffix' => $this->tableSuffix]];
        }

        // move job to another platform
        if (!allowSiteByPlatform()) {
            $this->warn(sprintf('Migrate JOB from platform %s to platform %s', platform(), sitePlatform()));
            return [static::WRONG_PLATFORM, ['tableSuffix' => $this->tableSuffix]];
        }

        PosMasterManager::dropTables($this->tableSuffix);

        return static::EXECUTE_DESTROY;
    }

}
