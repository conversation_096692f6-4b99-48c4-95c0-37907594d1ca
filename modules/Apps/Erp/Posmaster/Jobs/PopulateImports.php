<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Posmaster\Jobs;

use App\Jobs\Job;
use Illuminate\Support\Collection;
use Modules\Apps\Erp\Posmaster\PosMasterManager;

class PopulateImports extends Job
{
    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'import';

    /** @var PosMasterManager $manager */
    protected $manager;

    /**
     * @param string $tableSuffix
     * @param string $type
     * @param mixed $history
     * @return mixed
     */
    public function __construct(protected string $tableSuffix, protected string $type, protected $history)
    {
        $this->site_id = site('site_id');
    }

    public function execute()
    {
        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return [static::SITE_MAINTENANCE, []];
        }

        // move job to another platform
        if (!allowSiteByPlatform()) {
            $this->warn(sprintf('Migrate JOB from platform %s to platform %s', platform(), sitePlatform()));
            return [static::WRONG_PLATFORM, []];
        }

        $this->manager = new PosMasterManager();
        if (!$this->manager->isActive()) {
            $this->warn('PosMaster is not active!');
            return static::EXECUTE_DESTROY;
        }

        $this->initImport();

        $this->info('Done');

        return static::EXECUTE_DESTROY;
    }

    private function initImport(): void
    {
        $productsModel = $this->manager::getProductsModel($this->tableSuffix);

        if ($this->type == 'with_hash') {
            $this->info('Begin make queues for products with variants');
            $productsModel::whereNotNull('hash')->select(['hash'])//->where('imported', 0)
                ->groupBy(['hash'])->chunk(50, function (Collection $products): void {
                    ImportProducts::dispatch($this->tableSuffix, 'hash', $products->pluck('hash')->all(), $this->history);
                });
        }

        if ($this->type == 'without_hash') {
            $this->info('Begin make queues for products without variants');
            $productsModel::whereNull('hash')->select(['id'])//->where('imported', 0)
                ->chunkById(50, function (Collection $products): void {
                    ImportProducts::dispatch($this->tableSuffix, 'id', $products->pluck('id')->all(), $this->history);
                });
        }
    }
}
