<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Polycomp\Http\Controllers;

use App\Exceptions\Error;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpCategoryMapController;
use Modules\Apps\Erp\Polycomp\Http\Filters\CategoryMapFilter;
use Modules\Apps\Erp\Polycomp\Models\Categories;
use Modules\Apps\Erp\Polycomp\Models\PolycompMapping;
use Modules\Apps\Erp\Polycomp\Polycomp;
use Modules\Core\Core\Helpers\CustomAwarePaginator;

/**
 * Class CategoryMapController
 * @package Modules\Apps\Erp\Polycomp\Http\Controllers
 */
class CategoryMapController extends AbstractErpCategoryMapController
{
    /**
     * @param Polycomp $manager
     * @throws Error
     */
    public function __construct(Polycomp $manager)
    {
        parent::__construct($manager);
    }

    /**
     * @inerhitDoc
     * @param mixed $id
     */
    protected function deleteMapping($id): void
    {
        if ($id == 0) {
            if ($ids = request()->input('ids')) {
                PolycompMapping::destroy($ids);
            }
        } else {
            PolycompMapping::findOrFail($id)->delete();
        }
    }

    /**
     * @inerhitDoc
     * @throws Error
     */
    protected function getMapping(): CustomAwarePaginator
    {
        $mapping = PolycompMapping::with(['category.path', 'mapping'])
            ->has('category.path')->has('mapping');

        $records = $this->getRecords($mapping, CategoryMapFilter::class);

        $records->transform(fn(PolycompMapping $mapping): array => $this->formatMapping($mapping));

        return $records;
    }

    /**
     * @inerhitDoc
     * @param mixed $id
     */
    protected function getCategories($id): Collection
    {
        $model = Categories::orderBy('name', 'asc')
            ->whereDoesntHave('mapping');

        if (is_numeric($id)) {
            $model->orWhereHas('mapping', function ($query) use ($id): void {
                $query->whereKey($id);
            });
        }

        return $model->get(['hash', 'name'])->map(fn(Categories $category): array => [
            'id' => $category->hash,
            'name' => $category->name,
        ]);
    }

    /**
     * @inerhitDoc
     * @param mixed $id
     */
    protected function updateMapping(Request $request, $id): array
    {
        $mapping = PolycompMapping::findOrFail($id);

        $mapping->fill([
            'hash' => $request->input('externalCategory.id'),
        ]);

        $mapping->update([
            'category_id' => $request->input('internalCategory.id'),
            'percentage' => $request->input('percent'),
            'hash' => $mapping->mapping->hash,
            'vendor_id' => $mapping->mapping->vendor_id,
            'group_id' => $mapping->mapping->group_id,
            'subgroup_id' => $mapping->mapping->subgroup_id,
            'name' => $mapping->mapping->name,
        ]);

        return $this->formatMapping($mapping);
    }

    /**
     * @inerhitDoc
     */
    protected function createMapping(Request $request): array
    {
        $mapping = new PolycompMapping([
            'hash' => $request->input('externalCategory.id'),
        ]);

        $mapping = PolycompMapping::create([
            'category_id' => $request->input('internalCategory.id'),
            'percentage' => $request->input('percent'),
            'hash' => $mapping->mapping->hash,
            'vendor_id' => $mapping->mapping->vendor_id,
            'group_id' => $mapping->mapping->group_id,
            'subgroup_id' => $mapping->mapping->subgroup_id,
            'name' => $mapping->mapping->name,
        ]);

        return $this->formatMapping($mapping);
    }

    /**
     * @param PolycompMapping $mapping
     * @return array
     */
    private function formatMapping(PolycompMapping $mapping): array
    {
        return [
            'id' => $mapping->id,
            'internalCategory' => [
                'id' => $mapping->category_id,
                'name' => $mapping->category->path->implode('name', ' > '),
            ],
            'externalCategory' => [
                'id' => $mapping->mapping->hash,
                'name' => $mapping->mapping->name,
            ],
            'internalCategoryName' => $mapping->category->path->implode('name', ' > '),
            'externalCategoryName' => $mapping->mapping->name,
            'percent' => $mapping->percentage,
        ];
    }
}
