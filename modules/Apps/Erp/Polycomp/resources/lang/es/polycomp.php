<?php

declare(strict_types=1);

return [
    'button.cancel_import' => 'Cancelar el proceso de importación',
    'button.collect' => 'Sincronizar marcas, grupos y subgrupos de',
    'button.install' => 'Instalar',
    'button.mapping' => 'Vincular categoría de Polycomp',
    'button.settings' => 'Configuraciones',
    'button.sync' => 'Sincronizar',
    'categories' => 'Sincronizar categorías',
    'choose_category' => 'Elegir categoría',
    'choose_polycomp_category' => 'Elegir categoría de polycomp',
    'collect_information' => 'Recoger información para ":category". Productos encontrados: :total',
    'deleted_category' => 'Esta categoría ha sido eliminada de polycomp',
    'error.api_connect_error' => 'No se puede conectar a la api',
    'error.apicode_required' => 'Se requiere código de API',
    'error.availability_quantity_required' => 'Se requiere la cantidad correspondiente al estado de disponibilidad',
    'error.category_is_required' => 'Se requiere categoría',
    'error.hash_is_required' => 'Se requiere categoría de Polycomp',
    'error.hash_unique' => 'La categoría de Polycomp ya está sincronizada',
    'error.invalid_credentials' => 'Credenciales inválidas',
    'error.map.missing' => 'Para utilizar esta opción tienes que sincronizar marcas, grupos y subgrupos de Polycomp.',
    'error.password_required' => 'Se requiere contraseña',
    'error.please_map_categories' => 'Por favor crea primero categorías de Polycomp',
    'error.status_required' => 'El estado del producto es requerido',
    'error.url.invalid' => 'La URL de Polycomp Rest API no es válida',
    'error.url.required' => 'Se requiere la URL de Polycomp Rest API',
    'error.username_required' => 'Se requiere nombre de usuario',
    'header.install' => 'Instalar Polycomp',
    'header.progress' => 'Estado de la importación',
    'header.progress.text' => 'Puede tardar más en mostrar la información del proceso',
    'header.settings' => 'Comienza con Polycomp',
    'help.apicode' => 'Ingresa un código de API de cuenta en Polycomp',
    'help.install' => 'Estás a punto de instalar Polycomp. Esta aplicación podrá importar datos de tu tienda Versus a tu tienda CloudCart.',
    'help.password' => 'Ingresa una contraseña de cuenta en Polycomp',
    'help.settings' => 'Sincroniza productos, inventario y pedidos de tu sistema Polycomp.',
    'help.status' => 'Los productos serán importados con el estado seleccionado',
    'help.username' => 'Ingresa un nombre de usuario de cuenta en Polycomp',
    'info.description' => 'Estás a punto de instalar Polycomp. Esta aplicación podrá importar datos de tu tienda Versus a tu tienda CloudCart.',
    'info.general' => 'Puedes usar esta herramienta para importar productos y categorías desde Polycomp Bulgaria. Solo deberías registrarte en <a href="http://polycomp.bg/" target="_blank">polycomp.bg</a>. y usar el nombre de usuario y la contraseña aquí para mapear los productos que elijas a tu tienda.',
    'info.install' => 'Polycomp es un distribuidor líder de una amplia gama de productos de hardware y representa más de 5 marcas internacionales líderes para: sistemas y componentes de comercio electrónico. <br /> Si deseas convertirte en socio de Polycomp, por favor sigue <a href="versuserp.com/bg/contacts" target="_blank">este enlace</a>',
    'info.inventory' => 'En la configuración a continuación, puedes establecer reglas en qué estados de disponibilidad agregar cantidades específicas a los productos importados',
    'info.sync' => 'Sincroniza precios y cantidades de productos con Polycomp',
    'info.title' => 'Polycomp',
    'inventory.availability.in_stock' => 'En stock',
    'inventory.availability.limited_quantity' => 'Cantidad limitada',
    'inventory.availability.on_the_road' => 'En camino',
    'inventory.availability.with_an_order' => 'Con un pedido',
    'label.apicode' => 'Código API',
    'label.availability_quantity' => 'La cantidad del producto con estado',
    'label.increasing' => 'Aumentando',
    'label.password' => 'Contraseña',
    'label.percentage' => 'Porcentaje',
    'label.price_type' => 'Tipo de precio para sincronización',
    'label.product_status' => 'Estado del producto',
    'label.store_url.help' => 'Esta es la URL a tu Polycomp Rest API',
    'label.url' => 'URL de Versus Rest API',
    'label.username' => 'Nombre de usuario',
    'msg.collecting' => 'Sincronizando marcas, grupos y subgrupos desde Polycomp.',
    'msg.completed' => 'La importación se ha completado. Se reiniciará en unos minutos.',
    'msg.disabling' => 'Sincronizando productos faltantes desde Polycomp.',
    'msg.importing' => 'Importando producto desde Polycomp.',
    'msg.syncing' => 'Sincronización de precios y cantidades de productos con Polycomp iniciada correctamente.',
    'option.price.kkprice' => 'Precio por Cliente sin IVA',
    'option.price.kkprice_BGL' => 'Precio por Cliente en BGN sin IVA a la tasa del día',
    'option.price.kkprice_BGL_dds' => 'Precio por Cliente en BGN con IVA a la tasa del día',
    'option.price.price' => 'Precio de distribuidor sin IVA',
    'option.price.price_dds' => 'Precio de distribuidor con IVA',
    'option.price.realPrice' => 'Precio de distribuidor con descuentos sin IVA',
    'option.price.realPrice_BGL' => 'Precio con todos los descuentos en BGN sin IVA a la tasa del día',
    'option.price.realPrice_BGL_dds' => 'Precio con todos los descuentos en BGN con IVA a la tasa del día',
    'ph.apicode' => 'Ingresa código API',
    'ph.password' => 'Ingresa contraseña',
    'ph.username' => 'Ingresa nombre de usuario',
    'polycomp.category' => 'Selecciona categoría de Polycomp',
    'polycomp_category' => 'Categoría de Polycomp',
    'product_category' => 'Categoría del producto',
    'self.category' => 'Selecciona una categoría coincidente de la tienda',
    'successfully.cancel_import' => 'El proceso de importación ha sido cancelado con éxito',
    'sync.started' => 'Sincronización de precios y cantidades de productos con Polycomp iniciada correctamente.',
    'text.complete' => ':progress% Completado',
    'text.complete_from' => ':complete de :total',
    'text.result_date' => 'Última actualización :date',
    'title.able_to' => 'Vender en línea con Polycomp',
    'categories_sync' => ':total categorías sincronizadas. Ahora mapealas',
    'keyid' => 'ID de Polycomp',
    'conf.import_available_status' => 'Importar productos con estado',
    'conf.conf.import_available_status.help' => 'Se importarán y actualizarán los productos con solo el estado que seleccionaste'
];
