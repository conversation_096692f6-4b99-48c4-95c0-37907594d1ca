<?php

declare(strict_types=1);

return [
    'button.cancel_import' => 'Cancel Import Process',
    'button.collect' => 'Sync Brands, Groups, and Subgroups from',
    'button.install' => 'Install',
    'button.mapping' => 'Link category from Polycomp',
    'button.settings' => 'Settings',
    'button.sync' => 'Sync',
    'categories' => 'Sync categories',
    'choose_category' => 'Choose category',
    'choose_polycomp_category' => 'Choose polycomp category',
    'collect_information' => 'Collect information for ":category". Founded products: :total',
    'deleted_category' => 'This category is deleted from polycomp',
    'error.api_connect_error' => 'Unable to connect to api',
    'error.apicode_required' => 'API Code is required',
    'error.availability_quantity_required' => 'The quantity corresponding to the availability status is required',
    'error.category_is_required' => 'Category is required',
    'error.hash_is_required' => 'Polycomp category is required',
    'error.hash_unique' => 'Polycomp category is already sync',
    'error.invalid_credentials' => 'Invalid credentials',
    'error.map.missing' => 'To use this option you have to Sync Brands, Groups, and Subgroups from Polycomp.',
    'error.password_required' => 'Password is required',
    'error.please_map_categories' => 'Please first create categories from Polycomp',
    'error.status_required' => 'Product Status is required',
    'error.url.invalid' => 'Polycomp Rest API URL is not valid',
    'error.url.required' => 'Polycomp Rest API URL is required',
    'error.username_required' => 'Username is required',
    'header.install' => 'Install Polycomp',
    'header.progress' => 'Importing status',
    'header.progress.text' => 'It may take longer to display the process information',
    'header.settings' => 'Get started with the Polycomp',
    'help.apicode' => 'Enter an Account API code in Polycomp',
    'help.install' => 'You are about to install Polycomp. This app will be able to import data from your Versus store to your CloudCart store.',
    'help.password' => 'Enter an account password in Polycomp',
    'help.settings' => 'Sync products, inventory, orders, from your Polycomp system.',
    'help.status' => 'Продуктите ще бъдат импортирани с избрания статус',
    'help.username' => 'Enter an account username in Polycomp',
    'info.description' => 'You are about to install Polycomp. This app will be able to import data from your Versus store to your CloudCart store.',
    'info.general' => 'You can use this tool to import products and categories from Polycomp Bulgaria. You should only register at <a href="http://polycomp.bg/" target="_blank">polycomp.bg</a>. and use the username and password here in order to map the products you choose to your store.',
    'info.install' => 'Polycomp is a leading distributor of a wide range of hardware products and represents more than 5 leading international brands for: ecommerce systems and components. <br /> If you would like to become a partner with Polycomp, please follow <a href="versuserp.com/bg/contacts" target="_blank">this link</a>',
    'info.inventory' => 'In the settings below, you can set rules in which statuses for availability to add specific quantities to imported products',
    'info.sync' => 'Sync product prices and quantities with Polycomp',
    'info.title' => 'Polycomp',
    'inventory.availability.in_stock' => 'In stock',
    'inventory.availability.limited_quantity' => 'Limited quantity',
    'inventory.availability.on_the_road' => 'On the road',
    'inventory.availability.with_an_order' => 'With an order',
    'label.apicode' => 'API Code',
    'label.availability_quantity' => 'The quantity of product with status',
    'label.increasing' => 'Increasing',
    'label.password' => 'Password',
    'label.percentage' => 'Percentage',
    'label.price_type' => 'Price type for synchronization',
    'label.product_status' => 'Product status',
    'label.store_url.help' => 'This is URL to your Polycomp Rest API',
    'label.url' => 'Versus Rest API URL',
    'label.username' => 'Username',
    'msg.collecting' => 'Syncing Brands, Groups, and Subgroups from Polycomp.',
    'msg.completed' => 'The import is completed. It will be restarted in a few minutes.',
    'msg.disabling' => 'Syncing missing products from Polycomp.',
    'msg.importing' => 'Importing product from Polycomp.',
    'msg.syncing' => 'Syncing product prices and quantities with Polycomp started successfully.',
    'option.price.kkprice' => 'Price per Client without VAT',
    'option.price.kkprice_BGL' => 'Price per Client in BGN without VAT at the rate of the day',
    'option.price.kkprice_BGL_dds' => 'Price per Client in BGN with VAT at the rate of the day',
    'option.price.price' => 'Dealer price without VAT',
    'option.price.price_dds' => 'Dealer price with VAT',
    'option.price.realPrice' => 'Dealer price with discounts without VAT',
    'option.price.realPrice_BGL' => 'Price with all discounts in BGN without VAT at the rate of the day',
    'option.price.realPrice_BGL_dds' => 'Price with all discounts in BGN with VAT at the rate of the day',
    'ph.apicode' => 'Enter API code',
    'ph.password' => 'Enter password',
    'ph.username' => 'Enter username',
    'polycomp.category' => 'Select category from Polycomp',
    'polycomp_category' => 'Polycomp category',
    'product_category' => 'Product category',
    'self.category' => 'Select a matching category from the store',
    'successfully.cancel_import' => 'The importing process has been canceled successfully',
    'sync.started' => 'Syncing product prices and quantities with Polycomp started successfully.',
    'text.complete' => ':progress% Completed',
    'text.complete_from' => ':complete from :total',
    'text.result_date' => 'Last updated :date',
    'title.able_to' => 'Sell online with Polycomp',
    'categories_sync' => ':total categories sync. Now map them',
    'keyid' => 'Polycomp ID',
    'conf.import_available_status' => 'Import products with Status',
    'conf.conf.import_available_status.help' => 'Products with only the status you selected will be imported and updated'
];
