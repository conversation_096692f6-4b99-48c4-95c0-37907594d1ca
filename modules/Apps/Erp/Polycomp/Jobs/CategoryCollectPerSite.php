<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Polycomp\Jobs;

use App\Helper\DataBase\Importer;
use Modules\Apps\Erp\Polycomp\Client;
use Modules\Apps\Erp\Polycomp\Polycomp;
use App\Jobs\Job;
use Carbon\Carbon;
use Exception;
use Modules\Core\Core\Models\Alerts;

class CategoryCollectPerSite extends Job
{
    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'import1';

    /**
     * @var Client
     */
    protected $client;

    public function __construct()
    {
        $this->site_id = site('site_id');
    }

    public function execute()
    {
        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return static::SITE_MAINTENANCE;
        }

        // move job to another platform
        if (!allowSiteByPlatform()) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', platform(), sitePlatform()));
            return static::WRONG_PLATFORM;
        }

        $manager = new Polycomp();
        if (!$manager->isInstalled()) {
            $this->warning(sprintf('Job "Polycomp:collect" is not installed to running for site "%s"', site('site_id')));
            return static::DESTROY;
        }

        if (!$manager->isActive()) {
            $this->warning(sprintf('Job "Polycomp:collectCategories" is not configured to running for site "%s". Restart with delay', site('site_id')));
            return [static::RESTART_DELAY, 43200];
        }

        $this->client = $manager->getClient();

        $this->info('Begin get vendors');

        try {
            $vendors = $this->getVendors();
        } catch (Exception $exception) {
            if (stripos($exception->getMessage(), 'Unauthorized') !== false) {
                $manager->setWorking(0);

                $this->withLocale(site('language_cp'), function () use ($manager): void {
                    $msg = __('apps.msg.access_denied', [
                        'app' => $manager->getGlobalApp()->name,
                    ]);

                    $manager->notifiy(
                        'polycomp_access_denied',
                        $msg,
                        false,
                        Alerts::TYPE_WARNING
                    );
                });

                return static::DESTROY;
            }

            throw $exception;
        }

        if (!is_array($vendors) && $vendors === -1) {
            $this->warning('Lost connection. Restart job');
            return [static::RESTART_DELAY, 60];
        }

        $this->info('End get vendors. Get other data for mapping');
        $data = collect($vendors)->map(fn($vendor) => collect($this->client->getGroups($vendor['id']))->map(
            function ($group) use ($vendor) {
                if (!empty($group['id'])) {
                    return collect($this->client->getSubgroups($vendor['id'], $group['id']))->map(fn($subGroup): array => [
                        'hash' => md5(implode('_', [$vendor['id'], $group['id'], $subGroup['id']])),
                        'vendor_id' => $vendor['id'],
                        'group_id' => $group['id'],
                        'subgroup_id' => $subGroup['id'],
                        'name' => implode(' > ', [$vendor['name'], $group['name'], $subGroup['name']]),
                        'created_at' => ($date = Carbon::now()),
                        'updated_at' => $date
                    ]);
                }

                return;
            }
        )->collapse())->filter()->collapse();

        $this->info(sprintf('Get %d rows from polycomp', $data->count()));

        $import = new Importer();
        $import->setDelete('@app_polycomp_categories');
        $import->setData('@app_polycomp_categories', $data->all());
        $this->info('Begin execute sql for polycomp categories');
        $import->execute(false);
        $this->info('End execute sql for polycomp categories');

        return true;
    }

    /**
     * @return array|int
     * @throws Exception
     */
    protected function getVendors()
    {
        try {
            return $this->client->getVendors();
        } catch (Exception $exception) {
            if ($exception->getCode() === 500) {
                return -1;
            }

            throw $exception;
        }
    }

}
