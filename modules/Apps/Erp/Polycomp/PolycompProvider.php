<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Polycomp;


use App\Providers\RouteServiceProvider;
use Illuminate\Routing\Router;
use Illuminate\Support\ServiceProvider;

class PolycompProvider extends ServiceProvider
{


    protected $namespace = 'Modules\Apps\Erp\Polycomp\Http\Controllers';

    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = false;

    /**
     * @return void
     */
    #[\Override]
    public function register(): void
    {
        $this->app->singleton(Polycomp::APP_KEY, fn(): \Modules\Apps\Erp\Polycomp\Polycomp => new Polycomp());
        $this->mergeConfigFrom(__DIR__ . '/config/queue.php', 'queue.mapping');
    }

    public function boot(): void
    {
        $this->registerRoutes();
        $this->registerViews();
        $this->registerTranslations();
    }

    protected function registerViews()
    {
        $this->app->make('view')->addNamespace(Polycomp::APP_KEY, __DIR__ . '/resources/views');
    }

    protected function registerRoutes()
    {
        $this->app->make('router')->group(['middleware' => RouteServiceProvider::siteCpMiddleware(), 'namespace' => $this->namespace], function (Router $route): void {
            $this->loadRoutesFrom(__DIR__ . '/routes/api.php');
        });
    }

    public function registerTranslations(): void
    {
        $this->loadTranslationsFrom(__DIR__ . '/resources/lang', Polycomp::APP_KEY);
    }
}
