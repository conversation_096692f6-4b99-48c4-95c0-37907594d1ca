<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Barsy\Event;

use App\Events\FulfillmentAdd;
use App\Events\OrderCreated;
use App\Events\OrderStatusChange;
use App\Events\PostOrderStatusChange;
use App\Models\Queue\SiteQueue;
use Illuminate\Events\Dispatcher;
use Modules\Apps\Administration\StoreLocations\Models\ZoneStores;
use Modules\Apps\Erp\Barsy\Manager;

/**
 * Class OrderEventSubscriber
 *
 * @package App\Listeners
 */
class EventSubscriber
{
    /**
     * @param $event
     * @return null
     * @throws \App\Exceptions\Error
     */
    public function onStatusChange($event): null
    {
        $manager = new Manager();
        $setting = $manager->getSetting('send_order');
        $send = 0;
        if ($manager->isActive() && $manager->getSetting('allow_send_order', 1) == 1) {
            $order = null;
            if ($event instanceof OrderCreated) {
                if($setting == 'new_order'){
                    $order = $event->order;
                    $send = 1;
                }
            }

            if ($event instanceof OrderStatusChange  || $event instanceof PostOrderStatusChange) {
                if ($event->order->status == 'cancelled' &&
                    !empty($event->order->meta_pluck->get('barsy_order_id')) &&
                    empty($event->order->meta_pluck->get('barsy_order_close'))) {
                    SiteQueue::executeQueueTask('barsy_order_cancel', [
                        'id' => $event->order->id,
                    ], null, 10);
                }
            }

            if ($event instanceof OrderStatusChange || $event instanceof PostOrderStatusChange) {
                if($setting == 'paid' && ($event->order->payment->status ?? null) == 'completed'){
                    $order = $event->order;
                    $send = 1;
                }

                if(
                    !empty($manager->getSetting('close_order')) &&
                    ($event->order->status == 'paid' || $event->order->status == 'completed') &&
                    !empty($event->order->meta_pluck->get('barsy_order_id')) &&
                    empty($event->order->meta_pluck->get('barsy_order_close'))){
                    SiteQueue::executeQueueTask('barsy_order_close', [
                        'id' => $event->order->id,
                    ], null, 10);
                }
            }

            if ($event instanceof FulfillmentAdd) {
                if($setting == 'sent') {
                    $order = $event->fulfillment->order;
                    $send = 1;
                }
            }

            if ($order && $send == 1) {
                SiteQueue::executeQueueTask('barsy_order', [
                    'id' => $order->id,
                ], null, 10);
            }
        }

        return null;
    }

    /**
     * @param $event
     * @return void
     * @throws \App\Exceptions\Error
     */
    public function onZoneStoreDelete($event): void
    {
//        $manager = new Manager();
//        if ($manager->isActive()) {
//            $zoneStore = $event->model;
////            SiteQueue::executeQueueTask('barsy_zone_store_delete', [
////                'zoneStoreId' => $zoneStore->id,
////            ], '', '10');
//        }
    }

    /**
     * Register the listeners for the subscriber.
     *
     * @param Dispatcher $events
     */
    public function subscribe($events): void
    {
        $events->listen(
            [
                \App\Events\OrderCreated::class,
                \App\Events\OrderStatusChange::class,
                \App\Events\FulfillmentAdd::class,
                \App\Events\PostOrderStatusChange::class,
            ],
            $this->onStatusChange(...)
        );
        $events->listen(
            [
                "eloquent.deleting:" . ZoneStores::class,
            ],
            $this->onZoneStoreDelete(...)
        );
    }
}
