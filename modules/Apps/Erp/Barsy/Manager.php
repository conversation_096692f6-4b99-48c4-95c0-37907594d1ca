<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Barsy;

use App\Exceptions\Error;
use App\Models\Gateway\PaymentProviders;
use App\Models\Product\Category;
use App\Models\Queue\SiteQueue;
use App\Models\Shipping\ShippingProvider;
use App\Models\System\AppsManager;
use Exception;
use Illuminate\Support\Arr;
use Lukanet\BarsyApiClient\Barsys\Barsys;
use Lukanet\BarsyApiClient\BarsyApiClient;
use Lukanet\BarsyApiClient\Exceptions\BarsyApiClientFault;
use Lukanet\BarsyApiClient\Exceptions\BarsyApiClientMessage;
use Modules\Apps\Administration\StoreLocations\StoreLocationsManager;
use Modules\Apps\Erp\Barsy\Http\Controllers\ApiClient;
use Modules\Apps\Erp\ErpCore\Managers\AbstractErpManager;
use Modules\Core\Helpers\VueJs\BoxSettings;
use Modules\Core\Helpers\VueJs\Fields\AlertLineField;
use Modules\Core\Helpers\VueJs\Fields\NumberField;
use Modules\Core\Helpers\VueJs\Fields\SelectField;
use Modules\Core\Helpers\VueJs\Fields\SwitchField;
use Modules\Core\Helpers\VueJs\Fields\TitleField;
use Modules\Core\Helpers\VueJs\ListBoxesSettings;
use Throwable;

class Manager extends AbstractErpManager
{
    public const APP_KEY = 'barsy';

    public const keySettings = [
        'basic_url', 'username', 'password', 'default_category',
        'action', 'compare_barsy', 'compare_by', 'tracking',
        'qty_default', 'send_order', 'cancel_reason', 'close_order',
        'type', 'default_object', 'location_multiple', 'location_one',
        'payments', 'shippings', 'key_price', 'key_price_promo', 'discount_id', 'allow_send_order', 'create_stores'
    ];

    protected $settings_mapping = [
        'compare_by' => 'compare_cc',
    ];

    protected $default_settings = [
        'type' => 'one',
        'compare_by' => self::COMPARE_SKU,
        'compare_barsy' => 'article_id',
        'send_order' => 'complete',
        'action' => self::ACTION_IMPORT,
        'key_price' => 'actual_price',
        'key_price_promo' => 'none',
        'allow_send_order' => 1,
        'create_stores' => 0
    ];

    protected $depots;

    protected $payments;

    protected $shipping;

    protected $reasons;

    /**
     * @param $is_install
     * @return string
     */
    public function getMigrationsPath($is_install): ?string
    {
        return 'modules/Apps/Erp/Barsy/migrations';
    }

    /**
     * @return array
     */
    public function appInfo(): array
    {
        return [static::APP_KEY => [
            'direct' => 'apps/barsy',
            'uninstall' => 'apps/barsy/uninstall',
            'icon' => 'barsy.png',
            'name' => __('barsy::app.info.title'),
            'description' => __('barsy::app.help.install'),
        ]];
    }

    /**
     * @return SiteQueue|bool
     * @throws Exception
     */
    public function createQueueJobs(): bool|SiteQueue|null
    {
        return SiteQueue::createQueueByMapping('barsy_products');
    }

    /**
     * @inheritdoc
     */
    #[\Override]
    public function jobs(): array
    {
        return [
            'barsy_products'
        ];
    }

    /**
     * @inerhitDoc
     */
    #[\Override]
    public function executeJobs(): array
    {
        return [
            'barsy_import',
            'barsy_order',
            'barsy_order_close',
            'barsy_order_cancel',
            'barsy_get_modifications',
        ];
    }

    /**
     * @return ApiClient
     * @throws Error
     */
    public function getClient(): \Modules\Apps\Erp\Barsy\Http\Controllers\ApiClient
    {
        return new ApiClient($this);
    }

    /**
     * @param array $parameters
     * @return bool
     */
    public function validateCredentials(array $parameters = []): bool
    {
        $credentials = Arr::only($this->getSettings()->all(), $this->getCredentialsFields());
        if ($parameters) {
            $credentials = array_merge($credentials, $parameters);
        }

        $client = new BarsyApiClient($parameters['basic_url'], $parameters['username'], $parameters['password']);

        $barsys = new Barsys($client);
        $result = $barsys->getlist(null);

        return !empty($result[0]);
    }

    /**
     * @return ListBoxesSettings
     */
    #[\Override]
    public function getAdditionalSettings(): ListBoxesSettings
    {
        return ListBoxesSettings::make(static::APP_KEY, [
            $this->boxOne(),
            $this->boxPayments(),
            $this->boxShipping(),
            $this->boxTwo(),
        ]);
    }

    /**
     * @return BoxSettings
     */
    #[\Override]
    protected function boxOne(): ?BoxSettings
    {
        $depots = $this->getDepots();
        $fields = collect();
        $slManager = new StoreLocationsManager();
        if ($slManager->isInstalled()) {
            $locations = $slManager->getAllShops();
            try {
                $fields = collect($depots)->map(fn($item) => with(SelectField::make('location_multiple.' . $item->id, $item->name), function (SelectField $field) use ($locations) {
                    $field->setDepend('type|allow_send_order', ['multiple', 1]);

                    foreach ($locations as $locationId => $locationName) {
                        $field->setOption($locationId, $locationName);
                    }

                    return $field->setDisableTranslatable(true)
                        ->setDisableTranslatableOptions(true);
                }));
            } catch (Throwable) {
                //
            }
        }

        return parent::boxOne()->setKey('operations')->setFields(array_merge([
            SwitchField::make('allow_send_order', 'Sending orders to Barsy')->setDefault(1),
            $this->getStoresSwitch(),
            with((SelectField::make('type', 'Operation mode'))->setDepend('allow_send_order', [1]), function (SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField {
                $field->setOption('multiple', 'I will work with multiple objects');
                $field->setOption('one', 'I will work with one object');

                return $field;
            }),
            with(SelectField::make('default_object', 'Select main object'), function (SelectField $field) use ($depots): \Modules\Core\Helpers\VueJs\Fields\SelectField {
                $field->setDepend('type|allow_send_order', ['one', 1]);

                foreach ($depots as $depot) {
                    $field->setOption($depot->id, $depot->name);
                }

                return $field->setDisableTranslatableOptions(true);
            }),
            TitleField::make('Connect locations between Barsy and CloudCart')
                ->setDepend('type|allow_send_order', ['multiple', 1]),
            $this->getStoreLocationAlert($slManager)
        ], $fields->all()));
    }

    /**
     * @param StoreLocationsManager $manager
     * @return AlertLineField|null
     * @throws Error
     */
    protected function getStoreLocationAlert(StoreLocationsManager $manager): ?AlertLineField
    {
        if ($manager->isInstalled()) {
            return null;
        }

        $mainApp = $manager->getGlobalApp();

        return AlertLineField::make('To use this functionality, you need to have the "{app}" application installed.', [
            'app' => $mainApp->name,
            'mapping' => $mainApp->mapping,
        ], 'warning')
            ->setDepend('type|allow_send_order', ['multiple', 1]);
    }

    /**
     * @return mixed|null
     */
    protected function getStoresSwitch()
    {
        if (!AppsManager::isInstalled('stores')) {
            return null;
        }

        return with(SwitchField::make('create_stores', 'Create Barsy objects as stores'), function (SwitchField $field): \Modules\Core\Helpers\VueJs\Fields\SwitchField {
            $field->setDepend('allow_send_order', [0]);
            return $field;
        });
    }

    /**
     * @return BoxSettings
     */
    protected function boxPayments(): ?BoxSettings
    {
        try {
            $payments = PaymentProviders::getConfigurations();
            $fields = collect($this->getPayments())->map(fn($item) => with(SelectField::make('payments.' . $item['id'], $item['name']), function (SelectField $field) use ($payments): \Modules\Core\Helpers\VueJs\Fields\SelectField {
                foreach ($payments as $payment) {
                    $field->setOption($payment->provider, $payment->title);
                }

                $field->setMultiple(true)
                    ->setCanClear(true)
                    ->setDisableTranslatable(true)
                    ->setDisableTranslatableOptions(true);
                return $field;
            }));
        } catch (Throwable) {
            $fields = collect();
        }

        return parent::boxOne()->setKey('payments')->setFields($fields->all());
    }

    /**
     * @return BoxSettings
     */
    protected function boxShipping(): ?BoxSettings
    {
        try {
            $shipping = ShippingProvider::get();
            $fields = collect($this->getShipping())->map(fn($item) => with(SelectField::make('shippings.' . $item['id'], $item['name']), function (SelectField $field) use ($shipping): \Modules\Core\Helpers\VueJs\Fields\SelectField {
                foreach ($shipping as $ship) {
                    $field->setOption($ship->id, $ship->name);
                }

                $field->setMultiple(true)
                    ->setCanClear(true)
                    ->setDisableTranslatable(true)
                    ->setDisableTranslatableOptions(true);
                return $field;
            }));
        } catch (Throwable) {
            $fields = collect();
        }

        return parent::boxOne()->setKey('shipping')->setFields($fields->all());
    }

    /**
     * @return BoxSettings
     * @throws Error
     */
    protected function boxTwo(): ?BoxSettings
    {
        return parent::boxOne()->setKey('other_settings')->setFields([
            with(SelectField::make('default_category', 'Select Category'), function (SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField {
                $field->setDepend('action', [self::ACTION_IMPORT])
                    ->setUrl(route('admin.api.product_categories.search'));

                if (
                    ($categoryId = $this->getSetting('default_category')) &&
                    ($category = Category::whereKey($categoryId)->first(['id']))
                ) {
                    $field->setOption($category->id, $category->path->implode('name', ' > '));
                }

                return $field->setDisableTranslatableOptions(true);
            })->setRequired(true),
            $this->compareBy(),
            with(SelectField::make('key_price', 'Choose which price from Barsy should be the main price of the product')->setDefault('actual_price'), fn(SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField => $field->setOption('current_price', 'Sale price')
                ->setOption('actual_price', 'Current sale price')),
            with(SelectField::make('key_price_promo', "Choose which Barsy price to use as the product's promotional price"), fn(SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField => $field->setOption('none', "I don't use discounted price")
                ->setOption('current_price', 'Sale price')
                ->setOption('actual_price', 'Current sale price')),
            $this->discounts()->setDepend('key_price_promo', ['current_price', 'actual_price']),
            with(SelectField::make('compare_barsy', 'Identifier in Barsy'), fn(SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField => $field->setOption('article_id', 'ID')
                ->setOption('code', 'Code')
                ->setOption('barcode', 'Barcode')),
            with(SelectField::make('send_order', 'Order information to be sent at status'), fn(SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField => $field->setOption('new_order', 'New Order')
                ->setOption('sent', 'Sent')
                ->setOption('paid', 'Paid'))->setRequired(true),
            SwitchField::make('tracking', 'Product quantity tracking'),
            NumberField::make('qty_default', 'The default quantity')
                ->setDepend('tracking', [1]),
            with(SelectField::make('cancel_reason', 'Select a reason when canceling an order'), function (SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField {
                $field->setUrl(route('apps.api.barsy.cancel_reason'));

                foreach ($this->getReasonList() as $reason) {
                    $field->setOption($reason['reason_id'], $reason['name'])->setDisableTranslatable(false);
                }

                return $field->setDisableTranslatableOptions(false);
            }),
            SwitchField::make('close_order', 'Close the account in Barsy')
        ]);
    }

    public function getCredentials(): ?array
    {
        return ['basic_url', 'username', 'password'];
    }

    /**
     * @return array|object[]
     * @throws Error
     */
    public function getDepots()
    {
        if ($this->isConfigured() && is_null($this->depots)) {
            $this->depots = $this->getClient()->getObjects();
        }

        return $this->depots = ($this->depots ?: []);
    }

    /**
     * @return array|object[]
     * @throws Error
     * @throws BarsyApiClientFault
     * @throws BarsyApiClientMessage
     */
    public function getPayments()
    {
        if ($this->isConfigured() && is_null($this->payments)) {
            $this->payments = array_filter($this->getClient()->getPaymentList() ?: []);
        }

        return $this->payments = ($this->payments ?: []);
    }

    /**
     * @return array|object[]
     * @throws Error
     */
    public function getShipping()
    {
        if ($this->isConfigured() && is_null($this->shipping)) {
            $this->shipping = array_filter($this->getClient()->getProducts(true, null, 'доставка') ?: []);
        }

        return $this->shipping = ($this->shipping ?: []);
    }

    /**
     * @return array|object[]
     * @throws Error
     */
    public function getReasonList()
    {
        if ($this->isConfigured() && is_null($this->reasons)) {
            $this->reasons = array_filter($this->getClient()->getReasonList(false) ?: []);
        }

        return $this->reasons = ($this->reasons ?: []);
    }

    public function getCredentialsFields(): array
    {
        return ['basic_url', 'username', 'password'];
    }
}
