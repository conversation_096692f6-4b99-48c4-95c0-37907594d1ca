<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Barsy\Jobs;

use App\Exceptions\Error;

use App\Models\Mongo\TemporaryData;
use App\Traits\Crudling;
use Modules\Apps\Erp\Barsy\Manager;
use App\Jobs\Job;
use App\Models\Queue\SiteQueue;
use App\Traits\PlanLimit;
use Modules\Importer\Models\HistoryImportLogTask;
use Carbon\Carbon;

class PreparingForFormatiing extends Job
{
    use Crudling;

//    use Duplicate;

    use PlanLimit;

    public $queue = 'import';

    protected ?Manager $manager = null;

    /**
     * ImportProducts constructor.
     */
    public function __construct(protected string $history_id)
    {
        $this->site_id = site('site_id');
    }

    public function execute()
    {
        $site = $this->getSite();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return static::SITE_MAINTENANCE;
        }

        // move job to another platform
        if (!allowSiteByPlatform()) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', platform(), sitePlatform()));
            return static::WRONG_PLATFORM;
        }

        $this->manager = new Manager();
        if ($this->manager->isActive()) {
            $this->getProducts();
        } else {
            return static::DESTROY;
        }

        return true;
    }

    /**
     * @return void
     * @throws Error
     * @throws \Throwable
     */
    public function getProducts(): void
    {
        $history = HistoryImportLogTask::find($this->history_id);
        $totalProducts = TemporaryData::owner()
            ->where('app_key', $this->manager::APP_KEY)
            ->where('status', 0)->count();
        $this->createLog("Start preparing products for formatting. Total products: {$totalProducts}");
        $this->manager->setProgress([
            'complete' => 0,
            'total' => $totalProducts,
            'msg' => "Products for creating and updating",
            'date' => Carbon::now('UTC')->format('Y-m-d H:i:s'),
            'info' => [
                "Total products to import: $totalProducts",
            ]
        ]);
        $batchSize = 100;

        do {
            $items = TemporaryData::owner()
                ->where('app_key', $this->manager::APP_KEY)
                ->where('status', 0)
                ->limit($batchSize)
                ->get();

            if ($items->isEmpty()) {
                break;
            }

            $ids = $items->pluck('_id');
            $getProducts = $items->pluck('data');

            SiteQueue::executeQueueTask('barsy_import', [
                'products' => $getProducts,
                'history_id' => $this->history_id,
            ]);

            TemporaryData::whereIn('_id', $ids)->update(['status' => 1]);
            $history->increment('total', $ids->count());

        } while (true);
        $this->createLog("End preparing products for formatting. Total products: {$totalProducts}");
        $this->manager->removeSetting('in_progress');
        $this->clearQty();
    }

    /**
     * Clear qty for Barsy products
     * @return void
     * @throws Error
     * @throws \Throwable
     */
    protected function clearQty()
    {
        SiteQueue::executeQueueTask('barsy_clear_qty', ['column' => $this->manager->getSetting('compare_by')]);
    }

    /**
     * @param $msg
     * @return void
     */
    protected function createLog($msg)
    {
        if (in_array(site('site_id'), [47481, 5112])) {
            $this->log($msg);
        }
    }
}
