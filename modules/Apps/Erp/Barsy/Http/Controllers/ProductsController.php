<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Barsy\Http\Controllers;

use App\Exceptions\Error;
use Modules\Apps\Erp\Barsy\Manager;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpProductsController;

/**
 * Class ProductsController
 * @package App\Integration\Barsy\Http\Controllers
 */
class ProductsController extends AbstractErpProductsController
{
    /**
     * @param Manager $manager
     * @throws Error
     */
    public function __construct(Manager $manager)
    {
        parent::__construct($manager);
    }
}
