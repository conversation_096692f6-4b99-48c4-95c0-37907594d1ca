<?php

declare(strict_types=1);

return [
    'add_location_title' => 'Lier l’emplacement à l’objet ',
    'add_value' => 'Ajouter une valeur',
    'button.add_location' => 'Ajouter un emplacement',
    'button.add_location_1' => 'Connecter à l’emplacement CloudCart',
    'button.add_location_2' => 'Lier à l’emplacement dans l’application Glovo',
    'button.add_modificator' => 'Connecter la valeur',
    'button.add_payment' => 'Connecter la méthode de paiement',
    'button.add_shipping' => 'Connecter la méthode d’expédition',
    'button.connect' => 'Connecter',
    'header.install' => 'Installer Barsy',
    'help.install' => ' ',
    'info.title' => 'Barsy',
    'invalid_locations' => 'Emplacement invalide',
    'invalid_login' => 'Détails de connexion Barsy invalides',
    'invalid_payment' => 'Méthode de paiement invalide',
    'invalid_shipping' => 'Méthode d’expédition invalide',
    'list.confirm.remove' => 'Supprimer l’emplacement',
    'list.confirm.remove.help' => 'Es-tu sûr de vouloir supprimer l’emplacement ?',
    'list.confirm.remove_payment' => 'Supprimer la méthode de paiement',
    'list.confirm.remove_payment.help' => 'Es-tu sûr de vouloir supprimer la méthode de paiement ?',
    'locations.not_found' => 'Tu n’as aucun emplacement ajouté dans Barsy',
    'modificators' => 'Modificateurs',
    'module_required' => "Pour utiliser le mode Multi-Location, vous devez installer l'application <a href='/admin/apps/store_locations' target='_blank'>Store Locations</a>",
    'module_type_one_title' => 'Sélectionner l\'objet principal',
    'module_type_select' => 'Mode opératoire',
    'module_type_select_multiple' => 'Je travaillerai avec plusieurs objets',
    'module_type_select_one' => 'Je travaillerai avec un objet',
    'option_value' => 'Sélectionner une valeur',
    'product_option' => 'Sélectionner une option de produit',
    'required_store_locations.text_1' => 'Tu dois avoir l’application installée ',
    'required_store_locations.text_2' => 'pour pouvoir utiliser l’application Barsy',
    'setting.action' => 'Action',
    'setting.basic_url' => 'URL du serveur',
    'setting.cancel_reason' => 'Sélectionner une raison lors de l’annulation d’une commande',
    'setting.close_order' => 'Fermer le compte dans Barsy<br /> Avec l’option de fermeture des commandes dans Barsy activée, seulement et seulement les commandes payées avec les détails du paiement seront fermées dans Barsy.',
    'setting.default_category' => 'Sélectionner une catégorie',
    'setting.default_category.help' => 'Sélectionne une catégorie pour importer les nouveaux produits',
    'setting.delivery' => 'Méthode de livraison',
    'setting.delivery.1' => 'Tous les fournisseurs sans Glovo',
    'setting.delivery.2' => 'Livraison avec Glovo',
    'setting.import.import' => 'Importer et synchroniser',
    'setting.import.sync' => 'Synchroniser seulement',
    'setting.not_glovo_locations' => 'Tu n’as aucun emplacement ajouté dans l’application Glovo',
    'setting.not_store_locations' => 'Tu n’as aucun emplacement ajouté dans l’application Store Locations',
    'setting.password' => 'Mot de passe',
    'setting.qty_default' => 'La quantité par défaut',
    'setting.qty_default.help' => 'Peu importe si l’option Suivre la quantité de produit est activée ou désactivée, pour les produits sans quantité dans Barsy, le stock de ta quantité sélectionnée sera ajouté à chaque produit.',
    'setting.send_order' => 'Informations sur la commande à envoyer à Barsy à',
    'setting.send_order.complete' => 'Commande complète',
    'setting.send_order.new_order' => 'Nouvelle commande',
    'setting.send_order.paid' => 'Payé',
    'setting.send_order.paid_complete' => 'Payé ou Complété',
    'setting.send_order.paid_send' => 'Payée ou envoyée',
    'setting.send_order.sent' => 'Envoyé',
    'setting.shipping_product' => 'Sélectionner un produit à expédier',
    'setting.tracking' => 'Suivi de la quantité de produits',
    'setting.unique.barcode' => 'Code-barre',
    'setting.unique.cc' => 'ID CloudCart',
    'setting.unique.code' => 'Code',
    'setting.unique.id' => 'ID',
    'setting.unique.provider' => 'Identifiant dans Barsy',
    'setting.unique.sku' => 'SKU',
    'setting.username' => 'Nom d’utilisateur',
    'settings.unique_barsy_help_barcode' => 'Vous avez sélectionné un identifiant unique « Code » dans Barsy, si la valeur de Code est vide, le produit ne sera pas importé.',
    'settings.unique_barsy_help_code' => 'Tu as sélectionné un identifiant unique',
    'success.remove.location' => 'Tu as supprimé avec succès l’emplacement',
    'success.remove.payment' => 'Tu as supprimé avec succès la méthode de paiement',
    'success.start.sync' => 'La synchronisation des modificateurs a commencé. Veuillez patienter',
    'success_locations' => 'Tu as connecté avec succès l’emplacement',
    'success_modificator' => 'Tu as lié avec succès le modificateur',
    'success_payment' => 'Tu as lié avec succès ta méthode de paiement',
    'success_shipping' => 'Tu as connecté avec succès la méthode d’expédition',
    'sync_mofificators' => 'Synchroniser les modificateurs',
    'tab_modificators' => 'Lier les modificateurs',
    'tab_settings' => 'Paramètres de base',
    'table.location' => 'Objet dans CloudCart',
    'table.object' => 'Objet dans Barsy',
    'table.payment.barsy' => 'Méthode de paiement Barsy',
    'table.payment.cloudcart' => 'Méthode de paiement du magasin',
    'table.shipping' => 'Produit d’expédition Barsy',
    'table.shipping_connect' => 'Méthode d’expédition CloudCart',
    'th.modificator' => 'Modificateurs dans Barsy',
    'th.option' => 'Valeur CloudCart',
    'title.import_sync' => 'Importer et synchroniser',
    'title.locations' => 'Connecter les emplacements entre Barsy et CloudCart',
    'title.other_settings' => 'Paramètres supplémentaires',
    'title.payments' => 'Lier les méthodes de paiement entre Barsy et CloudCart',
    'title.shipping' => 'Connecter les méthodes d’expédition entre Barsy et CloudCart',
    'validate.basic_url.required' => 'Tu n’as pas entré de URL de serveur',
    'validate.basic_url.url' => 'L’URL entrée est invalide',
    'validate.compare_cc.required' => 'Tu n’as pas sélectionné d’ID dans CloudCart',
    'validate.compare_provider.required' => 'Tu n’as pas sélectionné d’ID dans Barsy',
    'validate.default_category.required' => 'Tu n’as pas sélectionné de catégorie principale',
    'validate.delivery_type.required' => 'Tu n’as pas sélectionné de méthode de livraison',
    'validate.import_action.required' => 'Tu n’as pas sélectionné d’action',
    'validate.location.required' => 'Tu n’as pas lié les emplacements',
    'validate.password.required' => 'Tu n’as pas entré de mot de passe',
    'validate.send_order.required' => 'Tu n’as pas sélectionné quand les commandes seront envoyées à Barsy',
    'validate.username.required' => 'Tu n’as pas entré de nom d’utilisateur',
];
