<?php

declare(strict_types=1);

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group(['prefix' => 'api/finaleinventory', 'middleware' => 'hasApiPermission:apps'], function (Router $router): void {
    $router->post('install', ['as' => 'apps.finaleinventory.install', 'uses' => 'SettingsController@install']);
    $router->post('uninstall', ['as' => 'apps.finaleinventory.uninstall', 'uses' => 'SettingsController@uninstall']);
    $router->match(['get', 'post'], 'credentials', ['as' => 'apps.api.finaleinventory.credentials', 'uses' => 'SettingsController@validateCredentials']);
    $router->group(['prefix' => 'settings'], function (Router $router): void {
        $router->get('/', ['as' => 'apps.api.finaleinventory.settings', 'uses' => 'SettingsController@settings']);
        $router->post('/', ['as' => 'apps.pi.finaleinventory.settings', 'uses' => 'SettingsController@settingsSave']);
    });

    $router->get('facility', ['as' => 'apps.api.finaleinventory.facility', 'uses' => 'AppController@facility']);
    $router->get('customers', ['as' => 'apps.api.finaleinventory.customers', 'uses' => 'AppController@customers']);
});

//Route::group(['prefix' => 'api/finaleinventory'], function (Router $router) {
//    $router->post('install', ['as' => 'apps.finaleinventory.install', 'uses' => 'AppController@install']);
//    $router->post('uninstall', ['as' => 'apps.finaleinventory.uninstall', 'uses' => 'AppController@uninstall']);
//    $router->get('settings', ['as' => 'apps.api.finaleinventory.settings', 'uses' => 'AppController@settings']);
//    $router->post('settings', ['as' => 'apps.api.finaleinventory.settings', 'uses' => 'AppController@saveSettings']);
//    $router->get('facility', ['as' => 'apps.api.finaleinventory.facility', 'uses' => 'AppController@facility']);
//    $router->get('customers', ['as' => 'apps.api.finaleinventory.customers', 'uses' => 'AppController@customers']);
//    $router->get('cancel', ['as' => 'apps.api.finaleinventory.cancel', 'uses' => 'AppController@cancel']);
//    $router->get('status', ['as' => 'apps.api.finaleinventory.status', 'uses' => 'AppController@getStatus']);
//    $router->post( 'status/change/{status}', ['as' => 'apps.api.finaleinventory.status.change', 'uses' => 'AppController@setActive']);
//    $router->post( 'validate-login', ['as' => 'apps.api.finaleinventory.status.change', 'uses' => 'AppController@validateCredentials']);
//});
