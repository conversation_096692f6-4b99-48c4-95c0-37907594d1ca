<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Finaleinventory\Http\Controllers;

use App\Exceptions\Error;
use Illuminate\Http\Request;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpSettingsController;
use Modules\Apps\Erp\Finaleinventory\FinaleinventoryManager;
use Modules\Core\Core\Exceptions\CouldNotConnectToServer;

/**
 * Class SettingsController
 * @package App\Integration\Finaleinventory\Http\Controllers
 */
class SettingsController extends AbstractErpSettingsController
{
    /**
     * @param FinaleinventoryManager $manager
     * @throws Error
     */
    public function __construct(FinaleinventoryManager $manager)
    {
        parent::__construct($manager);
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function manipulateSettingsRequest(Request $request)
    {
        //        if($request->input('action') != AbstractErpManager::ACTION_UPDATE) {
        //            $request->request->set('ten_minute_update', 0);
        //        }
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function validateSettingsRequest(Request $request): void
    {
        $this->validate($request, [
            'accountname' => 'required_if:active,1',
            'username' => 'required_if:active,1',
            'password' => 'required_if:active,1',
        ], [
            'accountname.required_if' => 'Account name is required',
            'username.required_if' => 'Username is required',
            'password.required_if' => 'Access password is required',
        ]);

        if ($request->input('active')) {
            if (!$this->manager->validateCredentials($request->only($this->manager->getCredentialsFields()))) {
                throw new CouldNotConnectToServer();
            }
        }
    }

    /**
     * @param Illuminate\Http\Request $request
     * @return mixed
     */
    #[\Override]
    protected function validateCredentialsRequest(Request $request): void
    {
        $this->validate($request, [
            'accountname' => 'required',
            'username' => 'required',
            'password' => 'required',
        ], [
            'accountname.required' => 'Account name is required',
            'username.required' => 'Username is required',
            'password.required' => 'Access password is required',
        ]);
    }
}
