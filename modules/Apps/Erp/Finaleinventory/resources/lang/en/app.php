<?php

declare(strict_types=1);

return [
    'title' => 'Finale Inventory',
    'install.text' => 'Warehouse software Finale inventory',
    'install_description' => 'Finale Inventory',
    'settings.app' => 'Finale Inventory application settings',
    'settings_app_description' => 'Please follow the instructions below to properly configure your online store with Finale Inventory.',
    'setting.account_name' => 'Enter an account name',
    'setting.select_facility' => 'Choose a warehouse',
    'setting.select_category' => 'Select a product category',
    'setting.action' => 'Action',
    'setting.action_import' => 'Import products + update quantities',
    'setting.action_update' => 'Update quantities only',
    'setting.uniqeid' => 'Choose a unique identifier for comparison from Finale Inventory',
    'setting.uniqeid_compare_sku' => 'Compare by Product ID',
    'setting.uniqeid_compare_upc' => 'Compare by UPC',
    'setting.uniqeid_ean' => 'Compare by EAN',
    'setting.uniqeid_cc' => 'Choose a unique comparison ID in CloudCart',
    'setting.error' => 'Please enter valid Final inventory data!',
    'setting.product_status' => 'Status of imported products',
    'setting.product_status_1' => 'Published',
    'setting.product_status_0' => 'Unpublished',
    'setting.commit_order' => 'Commit orders (lock order at finale inventory when created at the store)',
    'setting.commit_order_yes' => 'Yes',
    'setting.commit_order_no' => 'No',
    'setting.order_prefix' => 'Order prefix',
    'request.error.accountname.required' => 'Enter an account name in Finale Inventory',
    'request.error.username.required' => 'Enter a username for Finale Inventory',
    'request.error.password.required' => 'Enter the password for Finale Inventory',
    'request.error.product_status.required' => 'Select a status for the imported products',
    'request.error.facility_id.required' => 'Choose a warehouse',
    'request.error.category_id.required' => 'Select a product category',
    'request.error.action.required' => 'Choose an action',
    'request.error.unique_fi.required' => 'Select a unique identifier from the Finale inventory',
    'request.error.unique_cc.required' => 'Choose a unique identifier in CloudCart',
    'request.error.order_commit.required' => 'You have not chosen to commit the order',
    'request.error.order_prefix.required' => 'You have not entered an order ID prefix',
    'setting.select_client' => 'Select client'

];
