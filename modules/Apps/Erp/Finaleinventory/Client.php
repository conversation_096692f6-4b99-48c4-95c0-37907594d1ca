<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Finaleinventory;

use Guz<PERSON>Http\Client as GuzzleClient;
use GuzzleHttp\Cookie\CookieJar;
use GuzzleHttp\RequestOptions;
use App\Models\Router\Exceptions;
use GuzzleHttp\Exception\ClientException;

/**
 * Class Client
 * @package Modules\Apps\Erp\Finaleinventory
 */
class Client
{
    /**
     * @var GuzzleClient
     */
    protected $client;

    protected \Modules\Apps\Erp\Finaleinventory\FinaleinventoryManager $manager;

    protected $cookie;

    /**
     * Client constructor.
     * @param FinaleinventoryManager $manager
     * @param $url
     * @param $port
     * @param string $username
     * @param string $password
     * @throws \Exception
     */

    public function __construct()
    {
        $manager = new FinaleinventoryManager();
        $this->manager = $manager;
        $this->setClient();
    }

    /**
     * @param false $login
     * @throws \App\Exceptions\Error
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function setClient($login = false): void
    {
        if ($login == true) {
            $jar = new CookieJar();
            $this->client = new GuzzleClient([
                'base_uri' => 'https://app.finaleinventory.com/' . $this->manager->getSetting('accountname') . '/api/',
                RequestOptions::VERIFY => false,
                RequestOptions::DECODE_CONTENT => false,
                'cookies' => $jar,
                RequestOptions::HEADERS => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
            ]);
            $this->post('auth', ['username' => $this->manager->getSetting('username'), 'password' => $this->manager->getSetting('password')]);
            if ($jar->getCookieByName('JSESSIONID') instanceof \GuzzleHttp\Cookie\SetCookie) {
                $this->manager->updateSetting('cookie_session', $jar->getCookieByName('JSESSIONID')->getValue());
                $this->setClient();
            } else {
                $this->manager->setInactive();
            }
        } else {
            $this->client = new GuzzleClient([
                'base_uri' => 'https://app.finaleinventory.com/' . $this->manager->getSetting('accountname') . '/api/',
                RequestOptions::VERIFY => false,
                RequestOptions::DECODE_CONTENT => false,
                'cookies' => $this->setCookies(),
                RequestOptions::HEADERS => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                ],
            ]);
        }
    }

    /**
     * @return CookieJar|void
     * @throws \App\Exceptions\Error
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function setCookies()
    {
        $jar = new CookieJar();

        if (!empty($this->manager->getSetting('cookie_session'))) {
            return $jar::fromArray([
                'JSESSIONID' => $this->manager->getSetting('cookie_session'),
                'ACCOUNT' => $this->manager->getSetting('accountname')
            ], 'app.finaleinventory.com');
        } else {
            return $this->setClient(true);
        }
    }

    /**
     * @return mixed
     * @throws \App\Exceptions\Error
     */
    public function get_cookie()
    {
        if (empty($this->get('securitygroup/STAFF'))) {
            $this->setClient(true);
        }

        return $this->manager->getSetting('cookie_session');
    }

    /**
     * @return array|mixed
     * @throws \App\Exceptions\Error
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function login(): array
    {
        $this->setClient(true);
        $login = $this->post('auth', ['username' => $this->manager->getSetting('username'), 'password' => $this->manager->getSetting('password')]);
        return $login;
    }


    /**
     * @param null $endpoint
     * @param array $parameters
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function get($endpoint = null, array $parameters = [])
    {
        $response = $this->send('GET', $endpoint, [
            RequestOptions::QUERY => $parameters
        ]);
        return $response;
    }

    /**
     * @param null $endpoint
     * @param $parameters
     * @return mixed[][]
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function post($endpoint, $parameters): array
    {
        $response = $this->send('POST', $endpoint, [
            RequestOptions::JSON => $parameters
        ]);
        if (empty($response) || empty($response['Exec'])) {
            return [];
        }

        $data = [];
        foreach ($response['Exec'] as $viewResponse) {
            foreach ($viewResponse as $id => $response) {
                $items = $response['Result'];
                $keys = array_shift($items);

                foreach ($items as $item) {
                    $data[$id] = array_combine($keys, $item);
                }
            }
        }

        return $data;
    }

    /**
     * @param $method
     * @param $endpoint
     * @param array $options
     * @param mixed $try
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    protected function send($method, $endpoint, array $options = [], $try = 0)
    {
        try {
            $response = $this->client->request($method, $endpoint, $options);
            return json_decode($response->getBody()->getContents(), true);
        } catch (\Exception $exception) {
            $message = $exception->getMessage();
            if ($exception instanceof ClientException) {
                if ($exception->getResponse()->getStatusCode() == 401) {
                    if ($try < 3) {
                        $this->manager->removeSetting('cookie_session');
                        $this->setClient(true);
                        return $this->send($method, $endpoint, $options, $try + 1);
                    } else {
                        $this->manager->updateActive(0);
                    }
                }

                $responseContent = $exception->getResponse()->getBody()->getContents();
                $message = json_encode(json_decode((string) $responseContent), JSON_UNESCAPED_UNICODE);
            }

            // Not added 404
            if (!is_null($message)) {
                Exceptions::createFromThrowable($exception, 'Finale Inventory ERROR');
            }

            return [];
        }
    }
}
