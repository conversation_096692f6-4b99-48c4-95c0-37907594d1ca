<form action="{route('apps.rkeeper.add.payment', [$provider])}" id="form_config" role="form" class="ajaxForm">
    <div class="wrapper">
        <div id="Product" class="clearfix container-medium">
            <div class="box-title fixed-top">
                <div class="side-panel-header">
                    <div class="left-controls">
                        <div class="close" data-dismiss="panel"></div>
                        <h3>{$title}</h3>
                    </div>
                    <div class="right-controls">
                        <button class="btn btn-default" data-dismiss="panel">{t}global.cancel{/t}</button>
                        <button class="btn btn-primary js-add-parameter submit">{t}global.save{/t}</button>
                    </div>
                </div>
            </div>

            <div class="box">
                <div class="box-section">
                    <div class="form-group-dynamic">
                        <div class="row form-group">
                            <div class="col-xs-12">
                                <select name="payment[{$provider}][]" class="form-control select2me input-lg" multiple="multiple"
                                        data-no-input="true">
                                    {foreach $available_payments as $payment}
                                        <option value="{$payment.id}" {if !empty($saved) && in_array($payment.id, $saved)} selected="selected" {/if}>{$payment.name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{capture append="js"}
    <script type="text/javascript">
        $('#form_config').on('submit', function (e) {
            $('#form_config').on('cc.ajax.success', function (e, json) {
                $('.grid-payments-list').trigger('cc.ajax.reload');
            });
        });
    </script>
{/capture}