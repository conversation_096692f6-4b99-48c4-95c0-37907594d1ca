<?php

declare(strict_types=1);

return [
    'add_location_title' => 'Standort zum Objekt hinzufügen',
    'button.add_location' => 'Standort hinzufügen',
    'button.add_payment' => 'Zahlungsmethode verknüpfen',
    'button.connect' => 'Verbinden',
    'header.install' => 'Installiere R-keeper',
    'help.install' => ' ',
    'info.title' => 'R-keeper',
    'invalid_locations' => 'Ungültiger Standort',
    'invalid_login' => 'Ungültige R-Keeper Anmeldedaten',
    'invalid_payment' => 'Ungültige Zahlungsmethode',
    'list.confirm.remove' => 'Standort entfernen',
    'list.confirm.remove.help' => 'Bist du sicher, dass du den Standort entfernen möchtest?',
    'locations.not_found' => 'Du hast keine Standorte zu R-Keeper hinzugefügt',
    'required_store_locations.text_1' => 'Du musst die App installiert haben',
    'required_store_locations.text_2' => 'damit du die R-Keeper App nutzen kannst',
    'setting.action' => 'Aktion',
    'setting.basic_url' => 'Basis-URL Server',
    'setting.corp_id' => 'Unternehmens-ID',
    'setting.default_category' => 'Kategorie auswählen',
    'setting.default_category.help' => 'Wähle eine Kategorie aus, in die die neuen Produkte importiert werden sollen',
    'setting.delivery' => 'Zahlungsmethode',
    'setting.delivery.1' => 'Persönliche Lieferung',
    'setting.delivery.2' => 'Lieferung mit Glovo',
    'setting.import.import' => 'Importieren und Synchronisieren',
    'setting.import.sync' => 'Nur synchronisieren',
    'setting.not_glovo_locations' => 'Du hast keine Standorte in der Glovo App hinzugefügt',
    'setting.not_store_locations' => 'Du hast keine Standorte in der Store Locations App hinzugefügt',
    'setting.object' => 'Wähle ein Objekt aus, von dem du die Produkte importieren / synchronisieren möchtest',
    'setting.qty_default' => 'Standardmenge',
    'setting.qty_default.help' => 'Die ausgewählte Standardmenge wird jedem Produkt hinzugefügt',
    'setting.send_order' => 'Bestellinformationen, die gesendet werden sollen an',
    'setting.send_order.complete' => 'Bestellung abgeschlossen',
    'setting.send_order.new_order' => 'Neue Bestellung',
    'setting.send_order.paid' => 'Bezahlt',
    'setting.send_order.paid_send' => 'Bezahlt oder gesendet',
    'setting.send_order.sent' => 'Gesendet',
    'setting.sid' => 'SID',
    'setting.unique.barcode' => 'Barcode',
    'setting.unique.cc' => 'CloudCart-ID',
    'setting.unique.code' => 'Code',
    'setting.unique.id' => 'ID',
    'setting.unique.provider' => 'Identifikator in R-Keeper',
    'setting.unique.sku' => 'SKU-Code',
    'setting.unique_cc' => 'Eindeutige ID in CloudCart',
    'setting.unique_provider' => '',
    'success.remove.location' => 'Du hast den Standort erfolgreich entfernt',
    'success_locations' => 'Du hast den Standort erfolgreich verknüpft',
    'success_payment' => 'Du hast die Zahlungsmethode erfolgreich verknüpft',
    'table.location' => 'Objekt in CloudCart',
    'table.object' => 'Objekt in R-Keeper',
    'table.payment.cloudcart' => 'ZAHLUNGSMETHODE IM SHOP',
    'table.payment.rkeeper' => 'ZAHLUNGSMETHODE IN R Keeper',
    'title.import_sync' => 'Importieren und Synchronisieren',
    'title.locations' => 'Standorte verknüpfen',
    'title.payments' => 'Verknüpfung der Zahlungsmethoden zwischen R Keeper und CloudCart',
    'validate.basic_url.required' => 'Basis-URL-Server erforderlich',
    'validate.compare_cc.required' => 'Du hast keine ID in CloudCart ausgewählt',
    'validate.compare_provider.required' => 'Du hast keinen Identifikator in R-Keeper ausgewählt',
    'validate.corp_id.required' => 'Unternehmens-ID ist erforderlich',
    'validate.default_category.required' => 'Du hast keine Hauptkategorie ausgewählt',
    'validate.delivery_type.required' => 'Du hast keine Zahlungsmethode ausgewählt',
    'validate.import_action.required' => 'Du hast keine Aktion ausgewählt',
    'validate.location.required' => 'Du hast keine Standorte verknüpft',
    'validate.send_order.required' => 'Du hast nicht ausgewählt, wann du Bestellungen an R-Keeper senden möchtest',
    'validate.sid.required' => 'SID ist erforderlich',
];
