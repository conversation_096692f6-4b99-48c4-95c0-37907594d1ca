<?php

declare(strict_types=1);

return [
    'add_location_title' => 'Aggiungi location all oggetto',
    'button.add_location' => 'Aggiungi Location',
    'button.add_payment' => 'Collega metodo di pagamento',
    'button.connect' => 'Collegati',
    'header.install' => 'Installa R-keeper',
    'help.install' => ' ',
    'info.title' => 'R-keeper',
    'invalid_locations' => 'Location non valida',
    'invalid_login' => 'Dettagli di accesso R-Keeper non validi',
    'invalid_payment' => 'Metodo di pagamento non valido',
    'list.confirm.remove' => 'Rimuovi la location',
    'list.confirm.remove.help' => 'Sei sicuro di voler rimuovere la location?',
    'locations.not_found' => 'Non hai aggiunto location a R-Keeper',
    'required_store_locations.text_1' => "Devi avere l'app installata ",
    'required_store_locations.text_2' => "per poter utilizzare l'app R-Keeper",
    'setting.action' => 'Azione',
    'setting.basic_url' => 'URL di base del server',
    'setting.corp_id' => 'ID azienda',
    'setting.default_category' => 'Seleziona Categoria',
    'setting.default_category.help' => 'Seleziona una categoria per importare i nuovi prodotti',
    'setting.delivery' => 'Metodo di consegna',
    'setting.delivery.1' => 'Consegna personale',
    'setting.delivery.2' => 'Consegna con Glovo',
    'setting.import.import' => 'Importa e Sincronizza',
    'setting.import.sync' => 'Sincronizza solo',
    'setting.not_glovo_locations' => "Non hai aggiunto location nell'app Glovo",
    'setting.not_store_locations' => "Non hai aggiunto location nell'app Store Locations",
    'setting.object' => 'Seleziona un oggetto da cui importare/sincronizzare i prodotti',
    'setting.qty_default' => 'Quantità predefinita',
    'setting.qty_default.help' => 'La quantità predefinita selezionata verrà aggiunta a ciascun prodotto',
    'setting.send_order' => 'Informazioni sull ordine da inviare a',
    'setting.send_order.complete' => 'Ordine completato',
    'setting.send_order.new_order' => 'Nuovo ordine',
    'setting.send_order.paid' => 'Pagato',
    'setting.send_order.paid_send' => 'Pagato o Inviato',
    'setting.send_order.sent' => 'Inviato',
    'setting.sid' => 'SID',
    'setting.unique.barcode' => 'Codice a barre',
    'setting.unique.cc' => 'ID CloudCart',
    'setting.unique.code' => 'Codice',
    'setting.unique.id' => 'ID',
    'setting.unique.provider' => 'Identificatore in R-Keeper',
    'setting.unique.sku' => 'Codice SKU',
    'setting.unique_cc' => 'ID unico in CloudCart',
    'setting.unique_provider' => '',
    'success.remove.location' => 'Hai rimosso con successo la location',
    'success_locations' => 'Hai collegato con successo la location',
    'success_payment' => 'Hai collegato con successo il metodo di pagamento',
    'table.location' => 'Oggetto in CloudCart',
    'table.object' => 'Oggetto in R-Keeper',
    'table.payment.cloudcart' => 'METODO DI PAGAMENTO NEL NEGOZIO',
    'table.payment.rkeeper' => 'METODO DI PAGAMENTO IN R Keeper',
    'title.import_sync' => 'Importa e Sincronizza',
    'title.locations' => 'Collegamenti Location',
    'title.payments' => 'Collegamento dei metodi di pagamento tra R Keeper e CloudCart',
    'validate.basic_url.required' => 'URL di base del server richiesto',
    'validate.compare_cc.required' => 'Non hai selezionato un ID in CloudCart',
    'validate.compare_provider.required' => 'Non hai selezionato un identificatore in R-Keeper',
    'validate.corp_id.required' => 'ID azienda richiesto',
    'validate.default_category.required' => 'Non hai selezionato una categoria principale',
    'validate.delivery_type.required' => 'Non hai selezionato un metodo di consegna',
    'validate.import_action.required' => 'Non hai selezionato un azione',
    'validate.location.required' => 'Non hai collegato location',
    'validate.send_order.required' => 'Non hai selezionato quando inviare gli ordini a R-Keeper',
    'validate.sid.required' => 'SID richiesto',
];
