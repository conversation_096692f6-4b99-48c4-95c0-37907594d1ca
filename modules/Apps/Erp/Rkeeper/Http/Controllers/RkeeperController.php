<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Rkeeper\Http\Controllers;

use App\Exceptions\Error;
use App\Models\Gateway\PaymentProviders;
use App\Models\System\AppsManager;
use App\Traits\IController;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Apps\Administration\StoreLocations\StoreLocationsManager;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpController;
use Modules\Apps\Erp\Rkeeper\Http\Requests\SettingsRequest;
use Modules\Apps\Erp\Rkeeper\Manager;
use Modules\Core\Core\Traits\GridFilters;
use Modules\Importer\Traits\ProgressInfoController;

/**
 * Class ProductReview
 * @package App\Http\Controllers\Sitecp\Apps
 * @deprecated
 */
class RkeeperController extends AbstractErpController
{
    use IController;
    use GridFilters;
    use ProgressInfoController {
        ProgressInfoController::progress as protectedParent;
    }

    /**
     * Manager constructor.
     * @param Manager $universum
     * @throws Error
     */
    public function __construct(Manager $manager)
    {
        parent::__construct($manager);
    }

    /**
     * @param SettingsRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     * @throws \Throwable
     */
    public function saveSettings(SettingsRequest $request)
    {
        $data = $request->input();
        $data['delivery_type'] = 1;
        $this->manager->updateSettings($data);
        $this->manager->updateActive((int)$request->input('active'));
        if ((int)$request->input('active') == 1) {
            $this->manager->setWorking(1);
        }

        return response()->json([
            'status' => 'success',
            'msg' => __('global.success.save')
        ]);

    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     * @throws \Throwable
     */
    public function start()
    {
        $this->manager->setWorking(1);
        return response()->json([
            'status' => 'success',
            'msg' => ''
        ]);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     */
    public function getObjects()
    {
        $getObjects = $this->manager->getClient()->getRestaurants();
        return response()->json([
            'data' => $getObjects
        ]);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLocations()
    {
        return response()->json([
            'data' => StoreLocationsManager::getAllShops()->all()
        ]);
    }

    /**
     * @param Request $request
     * @param $type
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|Response|void
     * @throws Error
     */
    public function locations(string $type)
    {
        $locations = StoreLocationsManager::getAllShops();
        $getSettings = $this->manager->getSetting('location_' . $type, []);
        $records = collect($this->manager->getClient()->getRestaurants())->map(function (array $item) use ($locations, $getSettings, $type) {
            $item['location'] = null;
            if (!empty($getSettings[$item['id']]) && !empty($locations[$getSettings[$item['id']]])) {
                $item['location'] = $locations[$getSettings[$item['id']]];
            }

            $item['provider_name'] = $item['name'];
            return collect($item);
        });
        return response()->json([
            'data' => $records->all()
        ]);
    }

    /**
     * @param $type
     * @param $id
     * @return \Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Foundation\Application|Response
     * @throws Error
     * @throws \Throwable
     */
    public function removeLocation(string $type, $id)
    {
        $getLocations = $this->manager->getSetting('location_' . $type, []);
        if (!empty($getLocations[$id])) {
            unset($getLocations[$id]);
            $this->manager->updateSettings(['location_' . $type => $getLocations]);
        }

        return response()->json([
            'status' => 'success',
            'msg' => __('rkeeper::app.success.remove.location'),
            'type' => $type,
        ]);
    }

    /**
     * @param $type
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     */
    public function availableLocation(string $type, $id)
    {
        $getSavedLocations = $this->manager->getSetting('location_' . $type, []);
        $locations = StoreLocationsManager::getAllShops();
        $available_locations = [];
        if ($type == 1) {
            foreach ($locations as $key => $location) {
                if (!in_array($key, array_values($getSavedLocations))) {
                    $available_locations[$key] = $location;
                }
            }
        }

        if ($type == 2) {
            $mapped = [];
            $glovo = AppsManager::getManager('glovo')->getSetting('shops', []);
            $glovoLocations = $locations->map(function ($name, $key) use ($glovo, $mapped) {
                if (!in_array($key, $glovo)) {
                    return;
                } else {
                    return $mapped[$key] = $name;
                }
            })->filter()->toArray();
            foreach ($glovoLocations as $key => $location) {
                if (!in_array($key, array_values($getSavedLocations))) {
                    $available_locations[$key] = $location;
                }
            }
        }

        return response()->json([
            'data' => $available_locations
        ]);
    }

    /**
     * @param Request $request
     * @param $id
     * @param $type
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     * @throws \Throwable
     */
    public function addLocation(Request $request, $id, string $type)
    {
        $getSavedLocations = $this->manager->getSetting('location_' . $type, []);
        if ($request->has('location') && !empty($request->post('location'))) {
            $input = $request->post('location');
            $getSavedLocations[array_key_first($input)] = array_values($input)[0];
            $this->manager->updateSettings(['location_' . $type => $getSavedLocations]);
            return response()->json([
                'status' => 'success',
                'msg' => __('rkeeper::app.success_locations')
            ]);
        }

        return response()->json([
            'status' => 'error',
            'msg' => __('rkeeper::app.invalid_locations')
        ]);
    }


    /**
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     */
    public function paymentsList()
    {
        $payments = PaymentProviders::getConfigurations();
        $getObjects = $this->manager->getClient()->getRestaurants();
        $Payments = collect($this->manager->getClient()->getCurrencies($getObjects[0]['id']));
        $getSettings = $this->manager->getSetting('payments', []);
        $records = $Payments->map(function ($item) use ($payments, $getSettings) {
            $item = collect($item);
            $item['rkeeper_payment'] = $item['name'];
            $item['remove'] = null;
            $item['payment_name'] = null;
            if (!empty($getSettings[$item['curid']])) {
                $name = implode(', ', $payments->whereIn('provider', $getSettings[$item['curid']])->pluck('title')->toArray());
                $item['payment_name'] = $name;
            }

            return $item;
        });
        return response()->json([
            'data' => $records->all()
        ]);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     */
    public function availablePayments()
    {
        $getSavedPayments = $this->manager->getSetting('payments', []);
        $savedPayment = [];
        if (!empty($savedPayment)) {
            foreach ($getSavedPayments as $saved) {
                $savedPayment = array_merge($savedPayment, $saved);
            }
        }

        $payments = PaymentProviders::getConfigurations()->map(function ($payment) use ($savedPayment): array {
            $clean_data = [];
            if (!in_array($payment->provider, $savedPayment)) {
                $clean_data['name'] = $payment->title;
                $clean_data['id'] = $payment->provider;
            }

            return $clean_data;
        });

        return response()->json([
            'data' => $payments->filter()->toArray(),
        ]);
    }

    /**
     * @param Request $request
     * @param $payment_id
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     * @throws \Throwable
     */
    public function addPayment(Request $request, $payment_id)
    {
        $getSavedPayments = $this->manager->getSetting('payments', []);

        if ($request->has('payment') && !empty($request->post('payment'))) {
            $input = $request->post('payment');
            $getSavedPayments[array_key_first($input)] = array_values($input)[0];
            $this->manager->updateSettings(['payments' => $getSavedPayments]);

            return response()->json([
                'status' => 'success',
                'msg' => __('rkeeper::app.success_payment')
            ]);
        }

        return response()->json([
            'status' => 'error',
            'msg' => __('rkeeper::app.invalid_payment')
        ]);
    }

    /**
     * @param $provider
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     * @throws \Throwable
     */
    public function removePayment($provider)
    {
        $getPayments = $this->manager->getSetting('payments', []);
        if (!empty($getPayments[$provider])) {
            unset($getPayments[$provider]);
            $this->manager->updateSettings(['payments' => $getPayments]);
        }

        return response()->json([
            'status' => 'success',
            'msg' => __('rkeeper::app.success.remove.payment'),
            'type' => $provider,
        ]);
    }

    /**
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     */
    public function savedPayments($id)
    {
        $getSavedPayments = $this->manager->getSetting('payments', []);

        $payments_saved = [];
        if (!empty($getSavedPayments[$id])) {
            $payments_saved = PaymentProviders::getConfigurations()->whereIn('provider', $getSavedPayments[$id])->pluck('provider')->toArray();
        }

        return response()->json([
            'data' => $payments_saved
        ]);
    }

    /**
     * @param Request $request
     * @param $payment_id
     * @return \Illuminate\Http\JsonResponse
     * @throws Error
     * @throws \Throwable
     */
    public function editPayment(Request $request, $payment_id)
    {
        $getSavedPayments = $this->manager->getSetting('payments', []);
        if ($request->has('payment') && !empty($request->post('payment'))) {
            $input = $request->post('payment');
            $getSavedPayments[array_key_first($input)] = array_values($input)[0];
            $this->manager->updateSettings(['payments' => $getSavedPayments]);

            return response()->json([
                'status' => 'success',
                'msg' => __('rkeeper::app.success_payment')
            ]);
        }

        return response()->json([
            'status' => 'error',
            'msg' => __('rkeeper::app.invalid_payment')
        ]);

    }
}
