<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Rkeeper\Http\Controllers;

use App\Exceptions\Error;
use Modules\Apps\Erp\ErpCore\Controllers\AbstractErpStatusController;
use Modules\Apps\Erp\Rkeeper\Manager;

/**
 * Class StatusController
 * @package App\Integration\Rkeeper\Http\Controllers
 */
class StatusController extends AbstractErpStatusController
{
    /**
     * @param Manager $manager
     * @throws Error
     */
    public function __construct(Manager $manager)
    {
        parent::__construct($manager);
    }
}
