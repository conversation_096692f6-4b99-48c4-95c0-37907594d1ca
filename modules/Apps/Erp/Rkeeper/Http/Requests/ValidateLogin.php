<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Rkeeper\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ValidateLogin extends FormRequest
{
    public function rules(): array
    {
        if ($this->method() == 'POST') {
            return [
                'basic_url' => 'required|string',
                'sid' => 'required|string',
                'corp_id' => 'required',
            ];
        }

        return [];
    }

    #[\Override]
    public function messages()
    {
        return [
            'basic_url.required' => __('rkeeper::app.validate.basic_url.required'),
            'sid.required' => __('rkeeper::app.validate.sid.required'),
            'corp_id.required' => __('rkeeper::app.validate.corp_id.required'),
        ];
    }
}
