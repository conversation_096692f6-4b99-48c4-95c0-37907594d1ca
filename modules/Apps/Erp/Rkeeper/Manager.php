<?php

declare(strict_types=1);

namespace Modules\Apps\Erp\Rkeeper;

use App\Exceptions\Error;
use App\Models\Gateway\PaymentProviders;
use App\Models\Product\Category;
use App\Models\Queue\SiteQueue;
use App\Models\System\AppsManager;
use Exception;
use GuzzleHttp\Client as GuzzleClient;
use Illuminate\Support\Arr;
use Modules\Apps\Administration\StoreLocations\StoreLocationsManager;
use Modules\Apps\Erp\ErpCore\Managers\AbstractErpManager;
use Modules\Apps\Erp\Rkeeper\Http\Controllers\Client;
use Modules\Core\Helpers\VueJs\BoxSettings;
use Modules\Core\Helpers\VueJs\Fields\NumberField;
use Modules\Core\Helpers\VueJs\Fields\SelectField;
use Modules\Core\Helpers\VueJs\Help;
use Modules\Core\Helpers\VueJs\ListBoxesSettings;
use Throwable;

class Manager extends AbstractErpManager
{
    public const APP_KEY = 'rkeeper';

    protected $products_import_feature = self::APP_KEY;

    public const keySettings = [
        'basic_url', 'sid', 'corp_id',
        'action', 'compare_rkeeper',
        'compare_by', 'qty_default',
        'send_order', 'default_category'
    ];

    protected $settings_mapping = [
        'action' => 'import_action',
        'compare_by' => 'compare_cc',
    ];

    protected $default_settings = [
        'compare_by' => self::COMPARE_SKU,
        'compare_rkeeper' => 'code',
        'send_order' => 'complete',
        'action' => self::ACTION_IMPORT,
        'qty_default' => 1,
    ];

    protected $payments;

    protected $rests;

    /**
     * @param $is_install
     * @return string
     */
    public function getMigrationsPath($is_install): ?string
    {
        return 'modules/Apps/Erp/Rkeeper/migrations';
    }

    /**
     * @return array
     */
    public function appInfo(): array
    {
        return [static::APP_KEY => [
            'direct' => 'apps/rkeeper',
            'uninstall' => 'apps/rkeeper/uninstall',
            'icon' => 'rkeeper.png',
            'name' => __('rkeeper::app.info.title'),
            'description' => __('rkeeper::app.help.install'),
        ]];
    }

    /**
     * @return Client
     * @throws Error
     */
    public function getClient(): \Modules\Apps\Erp\Rkeeper\Http\Controllers\Client
    {
        return new Client($this);
    }

    /**
     * @return SiteQueue|bool
     * @throws \Exception
     */
    public function createQueueJobs(): bool|SiteQueue|null
    {
        return SiteQueue::createQueueByMapping('rkpeer_products');
    }

    /**
     * @inheritdoc
     */
    #[\Override]
    public function jobs(): array
    {
        return [
            'rkpeer_products',
        ];
    }

    /**
     * @inerhitDoc
     */
    #[\Override]
    public function executeJobs(): array
    {
        return [
            'rkpeer_import',
            'rkpeer_order',
        ];
    }

    /**
     * @return ListBoxesSettings
     */
    #[\Override]
    public function getAdditionalSettings(): ListBoxesSettings
    {
        return ListBoxesSettings::make(static::APP_KEY, [
            $this->boxLocations(),
            $this->BoxOne(),
            $this->boxPayments(),
        ]);
    }

    /**
     * @return BoxSettings
     * @throws Error
     */
    protected function boxLocations(): ?BoxSettings
    {
        $fields = collect();
        if (AppsManager::isInstalled('store_locations')) {
            $depots = $this->getRestaurants();
            $locations = StoreLocationsManager::getAllShops();
            try {
                $fields = collect($depots)->map(fn($item) => with(SelectField::make('location_1.' . $item['id'], $item['name']), function (SelectField $field) use ($locations) {
                    foreach ($locations as $locationId => $locationName) {
                        $field->setOption($locationId, $locationName);
                    }

                    return $field->setDisableTranslatable(true)
                        ->setDisableTranslatableOptions(true);
                }));
            } catch (Throwable) {
                //
            }
        }

        return parent::boxOne()->setKey('operations')->setFields($fields->all());
    }

    /**
     * @return BoxSettings
     * @throws Error
     */
    #[\Override]
    protected function boxOne(): ?BoxSettings
    {
        return parent::boxOne()->setFields([
            $this->compareBy(),
            with(SelectField::make('compare_rkeeper', 'Identifier in R-Keeper'), fn(SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField => $field->setOption('id', 'ID')
                ->setOption('code', 'Code')),
            with(SelectField::make('default_category', 'Select Category'), function (SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField {
                $field->setDepend('action', [self::ACTION_IMPORT])
                    ->setUrl(route('admin.api.product_categories.search'))
                    ->setHelp(new Help('Select a category to import the new products into'));

                if (
                    ($categoryId = $this->getSetting('default_category')) &&
                    ($category = Category::whereKey($categoryId)->first(['id']))
                ) {
                    $field->setOption($category->id, $category->path->implode('name', ' > '));
                }

                return $field->setDisableTranslatableOptions(true);
            })->setRequired(true),
            with(SelectField::make('send_order', 'Order information to be sent at status'), fn(SelectField $field): \Modules\Core\Helpers\VueJs\Fields\SelectField => $field->setOption('new_order', 'New Order')
                ->setOption('sent', 'Sent')
                ->setOption('paid', 'Paid')
                ->setOption('complete', 'Order complete'))->setRequired(true),
            NumberField::make('qty_default', 'The default quantity')
                ->setHelp(new Help('Regardless of the Track Product Quantity option on or off, for products without a quantity in {app}, stock of your selected quantity will be added to each product.', ['app' => $this->getGlobalApp()->name])),
        ]);
    }

    /**
     * @return BoxSettings
     */
    protected function boxPayments(): ?BoxSettings
    {
        try {
            $payments = PaymentProviders::getConfigurations();
            $fields = collect($this->getPayments())->map(fn($item) => with(SelectField::make('payments.' . $item['id'], $item['name']), function (SelectField $field) use ($payments): \Modules\Core\Helpers\VueJs\Fields\SelectField {
                foreach ($payments as $payment) {
                    $field->setOption($payment->provider, $payment->title);
                }

                $field->setMultiple(true)
                    ->setCanClear(true)
                    ->setDisableTranslatable(true)
                    ->setDisableTranslatableOptions(true);
                return $field;
            }));
        } catch (Throwable) {
            $fields = collect();
        }

        return parent::boxOne()->setKey('payments')->setFields($fields->all());
    }

    public function getCredentialsFields(): array
    {
        return ['basic_url', 'sid', 'corp_id'];
    }

    /**
     * @return bool
     * @throws Error
     */
    public function validateCredentials(array $parameters = [])
    {
        $credentials = Arr::only($this->getSettings()->all(), $this->getCredentialsFields());
        if ($parameters) {
            $credentials = array_merge($credentials, $parameters);
        }

        try {
            $client = new GuzzleClient([
                'base_uri' => $credentials['basic_url'],
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'SID' => $credentials['sid'],
                ],
            ]);

            $response = $client->get('rests?corpid=' . $credentials['corp_id']);
            $decode = json_decode($response->getBody()->getContents(), true);
            if ($decode['status'] == 'Err') {
                return false;
            }

            return !empty($decode['data']['rests'][0]);
        } catch (Exception) {
            //
        }

        return false;
    }

    /**
     * @return array|object[]
     * @throws Error
     */
    public function getPayments()
    {
        if ($this->isConfigured() && is_null($this->payments)) {
            $this->payments = array_filter($this->getClient()->getPaymentList() ?: []);
        }

        return $this->payments = ($this->payments ?: []);
    }

    /**
     * @return array|object[]
     * @throws Error
     */
    public function getRestaurants()
    {
        if ($this->isConfigured() && is_null($this->rests)) {
            $this->rests = array_filter($this->getClient()->getRestaurants() ?: []);
        }

        return $this->rests = ($this->rests ?: []);
    }
}
