<?php

declare(strict_types=1);

namespace Modules\Apps\Imports\XmlImport\Importer\Xml;

use Exception;
use Illuminate\Support\Str;
use SimpleXMLElement;
use Throwable;
use XMLReader;

class SimpleXmlParser
{
    protected $search;

    protected $lines_limit = 500;

    protected $limit = 10;

    protected $url;

    protected $parameters = null;

    protected $error;

    protected $errors = [];

    protected $xml;

    protected $charset;

    /**
     * @param mixed $value
     * @return mixed
     */
    public function setSearch($value): static
    {
        $this->search = $value;
        return $this;
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function setUrl($value): static
    {
        $this->url = $value;
        return $this;
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function setXml($value): static
    {
        $this->xml = $value;
        return $this;
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function setParameters($value): static
    {
        $this->parameters = $value;
        return $this;
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function setLimitLines($value): static
    {
        $this->lines_limit = $value;
        return $this;
    }

    /**
     * @param mixed $value
     * @return mixed
     */
    public function setLimit($value): static
    {
        $this->limit = $value;
        return $this;
    }

    public function getPaths(): bool|array
    {
        try {
            if (!is_array($content = $this->toArray(false))) {
                return false;
            }
        } catch (Throwable $throwable) {
            $er = $throwable->getMessage() ?: $this->getError();
            if (str_contains((string) $er, 'Document is empty')) {
                $er = 'Document is empty or is not valid XML';
            }

            $this->errors['url'] = [$er];
            return !($this->error = $er);
        }

        $content = sprintf('<?xml version="1.0" encoding="utf-8"?><simpleparser_products>%s</simpleparser_products>', implode('', $content));

        try {
            $xml = simplexml_load_string($content, 'SimpleXMLElement', LIBXML_NOCDATA);
            $ns = ['' => null] + $xml->getDocNamespaces(true);
            $return = [];
            $this->xml2path($xml, $ns, '', $return);
            return $return;
        } catch (Exception) {
            $this->errors['url'] = ['Unable to parse XML file'];
            return !($this->error = sprintf('Unable to parse XML file: %s', $this->url));
        }
    }

    /**
     * @param mixed $xmlTag
     * @return mixed
     */
    public function toArray($xmlTag = true)
    {
        $xml = new XMLReader();
        $this->streamOptions();
        if (empty($url = $this->getUrl()) && empty($xmlString = $this->getXml())) {
            $this->errors['url'] = ['Document is empty or is not valid XML'];
            return !($this->error = 'Document is empty or is not valid XML');
        }

        $encoding = 'utf-8';
        if ($check = $this->getCharset()) {
            $encoding = $check;
        }

        if (!empty($xmlString)) {
            if (!@$xml->XML($xmlString, $encoding)) {
                $this->errors['url'] = ['Unable to open XML file'];
                return !($this->error = 'Unable to open XML file');
            }
        } elseif (!empty($url)) {
            if (!@$xml->open($url, $encoding)) {
                $this->errors['url'] = ['Unable to open url'];
                return !($this->error = sprintf('Unable to open url: %s', $this->url));
            }
        } else {
            $this->errors['url'] = ['Unable to open XML file'];
            return !($this->error = 'Unable to open XML file');
        }

        $depth = 0;
        $elemntDepth = null;
        $count = 0;
        $elements = [];
        while ($xml->read()) {
            if (is_numeric($this->lines_limit) && $depth >= $this->lines_limit) {
                break;
            }

            if ($xml->nodeType === XmlReader::ELEMENT && $xml->localName === $this->search && (is_null($elemntDepth) ? true : ($elemntDepth == $xml->depth))) {
                if (is_numeric($this->limit) && $count >= $this->limit) {
                    break;
                }

                if (is_null($elemntDepth)) {
                    $elemntDepth = $xml->depth;
                }

                if ($xmlTag) {
                    $elements[] = sprintf('<?xml version="1.0" encoding="%s"?>%s', $encoding, $xml->readOuterXml());
                } else {
                    $elements[] = $xml->readOuterXml();
                }

                $count++;
            }

            if ($xml->nodeType === XmlReader::ELEMENT) {
                $depth++;
            }
        }

        $xml->close();

        if (empty($elements)) {
            $this->errors['product_tag'] = ['Unable to find search tag: {product_tag}'];
            return !($this->error = sprintf('Unable to find search tag: %s', $this->search));
        }

        return $elements;
    }

    public function getUrl()
    {
        $url = $this->url;
        if ($this->parameters) {
            $url .= str_contains((string) $url, '?') ? '&' : '?';
            $url .= http_build_query($this->parameters);
        }

        return $url;
    }

    public function getXml()
    {
        return $this->xml;
    }

    public function getError()
    {
        return $this->error;
    }

    public function getErrors()
    {
        return $this->errors;
    }

    /**
     * @param mixed $xml
     * @param mixed $ns
     * @param string $path
     * @param array $return
     * @param mixed $tagName
     * @param mixed $tagValue
     * @return mixed
     */
    protected function xml2path($xml, $ns, string $path = '', array &$return = [], ?string $tagName = null, ?string $tagValue = null)
    {
        if (($tagName ?: $xml->getName()) != 'simpleparser_products') {
            $path .= $path ? ' > ' : '';
            $path .= $tagName ?: $xml->getName();
            $pathKey = $path;
            $tagValue = trim(strip_tags(html_entity_decode((string) $tagValue)));

            if (isset($return[$pathKey])) {
                if ($tagValue) {
                    $return[$pathKey]['short_content'][] = Str::limit($tagValue, 20);
                }
            } else {
                $return[$pathKey] = [
                    'path' => $pathKey,
                    'hash' => md5($pathKey),
                    'short_content' => $tagValue ? [Str::limit($tagValue, 20)] : [],
                ];
            }
        }

        foreach ($ns as $nsName => $nsUri) {
            foreach ($xml->attributes($nsUri) as $k => $v) {
                $pathKey = $path . '['.(!empty($nsName) ? $nsName . ':' : '') . $k.']';
                $tagValue = trim((string)$v);
                $tagValue = trim(strip_tags(html_entity_decode((string) $tagValue)));
                if (isset($return[$pathKey])) {
                    if ($tagValue) {
                        $return[$pathKey]['short_content'][] = Str::limit($tagValue, 20);
                    }
                } else {
                    $return[$pathKey] = [
                        'path' => $pathKey,
                        'hash' => md5($pathKey),
                        'short_content' => $tagValue ? [Str::limit($tagValue, 20)] : [],
                    ];
                }

                if (in_array(strtolower((string) $k), ['name', 'id', 'title', 'type', 'currency'])) {
                    $pathKey = $path . '['.(!empty($nsName) ? $nsName . ':' : '') . $k.'='.$v.']';
                    $tagValue = trim((string)$v);
                    $tagValue = trim(strip_tags(html_entity_decode((string) $tagValue)));
                    if (isset($return[$pathKey])) {
                        if ($tagValue) {
                            $return[$pathKey]['short_content'][] = Str::limit((string) $tagValue, 20);
                        }
                    } else {
                        $return[$pathKey] = [
                            'path' => $pathKey,
                            'hash' => md5($pathKey),
                            'short_content' => $tagValue ? [Str::substr($tagValue, 20)] : [],
                        ];
                    }
                }
            }

            $check = [];
            $firstChild = null;
            foreach ($xml->children($nsUri) as $k => $v) {
                if (is_null($firstChild)) {
                    $firstChild = $v;
                }

                $itemName = $nsName ? sprintf('%s:%s', $nsName, $v->getName()) : $v->getName();
                $check[] = $itemName;
                $this->xml2path($v, $ns, $path, $return, $itemName, trim((string)$v));
            }

            if ($check) {
                if (preg_match('~^(.*)([\d]{1,})$~', (string) $check[0], $m)) {
                    $start = $m[2];
                    $prefix = $m[1];
                    for ($i = $start; $i <= count($check); $i++) {
                        if (!in_array($prefix . $i, $check)) {
                            $check = [];
                            $firstChild = null;
                            break;
                        }
                    }
                } else {
                    $check = [];
                    $firstChild = null;
                }
            }

            //for tags with name type item_1,item_2,...,item_n
            if ($check && $firstChild && isset($prefix)) {
                /** @var SimpleXMLElement $child */
                $this->xml2path($firstChild, $ns, $path, $return, $prefix . '(1-n)');
            }
        }
    }

    protected function streamOptions()
    {
        $context = stream_context_create([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ],
            'http' => [
                'method' => "GET",
                'header' => "Accept-language: " . site('language') . "\r\n" .
                    "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\n".
                    "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\r\n",
                'timeout' => 10
            ]
        ]);

        libxml_set_streams_context($context);
        return $context;
    }

    protected function getCharset()
    {
        if (!$this->charset) {
            if (empty($url = $this->getUrl()) && empty($xmlString = $this->getXml())) {
                $this->errors['url'] = ['Document is empty or is not valid XML'];
                return !($this->error = 'Document is empty or is not valid XML');
            }

            if (!empty($xmlString)) {
                for ($i = 0; $i < 10; $i++) {
                    $line = Str::substr($xmlString, $i * 120, 120);
                    if (preg_match('~<\?xml([^>]*)encoding=[\'\"]([^\'\"]*)[\'\"]~i', $line, $match)) {
                        return $this->charset = Str::lower($match[2]);
                    }
                }
            } elseif (!empty($url)) {
                $context = stream_context_create([
                    'ssl' => [
                        'verify_peer' => false,
                        'verify_peer_name' => false,
                        'allow_self_signed' => true
                    ],
                    'http' => [
                        'method' => "GET",
                        'header' => "Accept-language: " . site('language') . "\r\n" .
                            "User-Agent:    Mozilla/5.0 (Windows; U; Windows NT 6.0; en-US; rv:1.9.1.6) Gecko/20091201 Firefox/3.5.6\r\n"
                    ]
                ]);
                $handle = @fopen($url, "r", false, $context);
                if (!$handle) {
                    return;
                }

                //                stream_set_timeout($handle, 600);
                //                dd(stream_get_meta_data($handle));

                $counter = 0;
                while (($line = fgets($handle, 120)) !== false) {
                    if (preg_match('~<\?xml([^>]*)encoding=[\'\"]([^\'\"]*)[\'\"]~i', $line, $match)) {
                        return $this->charset = Str::lower($match[2]);
                    }

                    if ($counter > 10) {
                        break;
                    }

                    $counter++;
                }
            }
        }

        return $this->charset;
    }
}
