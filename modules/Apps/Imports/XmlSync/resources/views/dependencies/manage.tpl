{capture append="js"}
<script type="text/javascript">
    {if $sub_page == 'add' || $sub_page == 'edit'}
        $("[data-btn='xml_sync-add-parameters']").on("click", function () {
            var $btn = $(this);

            $btn.prop("disabled", true);
            $btn.trigger('loading.start');

            var $row = $($("#parameter").html());

            $("#parameters").prepend($row);

            App.initAjax($row);

            $btn.trigger('loading.end');
            $btn.prop("disabled", false);
        });

        $(document).off("click", ".xml_sync-parameter-remove").on("click", ".xml_sync-parameter-remove", function () {
            $(this).closest(".xml_sync-parameter").remove();
            return false;
        });
    {/if}

    {if $sub_page == 'step2'}
        function addRow(name, classname) {
            var html = $('.addable-template').html()
                            .replace(/\@CLASS\@/g, classname)
                            .replace(/\@NAME\@/g, name);
            var $container = $('.event-addable-'+name).append(html);
            App.initAjax($container);
            return false;
        }

        $(document).off('change', '.event-select-type').on('change', '.event-select-type', function() {
            $(this).closest('.row').find('.event-type-base, .event-type-join')
                    .addClass('hide')
                    .filter('.event-type-'+this.value)
                    .removeClass('hide');
        }).find('.event-select-type').change();

        $(document).off('change', '.event-manual-switch').on('change', '.event-manual-switch', function() {
            $(this).closest('.row').find('.event-manual-select, .event-manual-input').addClass('hidden');

            if($(this).is(':checked')) {
                $(this).closest('.row').find('.event-manual-select').addClass('hidden')
                        .find('select,input').attr('disabled', true);
                $(this).closest('.row').find('.event-manual-input').removeClass('hidden')
                        .find('select,input').removeAttr('disabled');
            } else {
                $(this).closest('.row').find('.event-manual-select').removeClass('hidden')
                        .find('select,input').removeAttr('disabled');
                $(this).closest('.row').find('.event-manual-input').addClass('hidden')
                        .find('select,input').attr('disabled', true);
            }
        }).find('.event-manual-switch').change();

        {foreach from=$skeletons item=s}
            {if $s->filter}
                $('.event-{$s->name}').change(function() {
                    var value = $(this).val().split('[')[0];
                    $('.event-hide-option-{$s->name} select:not(.event-{$s->name},.control-type) option').removeClass('hide').each(function() {
                        val = $(this).attr('value');
                        if((val !== '' && val.indexOf(value) !== 0) /*|| val === value*/) {
                            $(this).addClass('hide');
                        }
                    });
                }).change();
            {/if}
        {/foreach}

        $(document).ready(function(){
            $('#xml_sync .box').each(function() {
                var $this = $(this);

                if(!$this.find('.box-section').children().length) {
                    $this.addClass('hide');
                }
            });
        });

    $('.js-tabs a').click(function (e) {
        var tabs = $('.js-multilevel, .js-singlelevel').removeClass('hide'),
            tab = tabs.filter($(this).attr('href'));
        $(this).tab('show');
        CC.statusBox(tabs, 0);
        CC.statusBox(tab, 1);
        return false;
    });
    var click, active = $('.js-tabs li.active');
    if(active.length) {
        active.first().find('a').trigger('click');
    } else {
        $('.js-tabs a:first').trigger('click');
    }


    {/if}

    {if $sub_page == 'step3'}
        function addOperationRow() {
            var $container = $('.js-event-operations').append($('.addable-template').html());
            App.initAjax($container);

            return false;
        }

        $(document).ready(function() {
            if($('.js-event-operations .box-section').length < 1) {
                addOperationRow();
            }
        });

        $(document).on('click', '.js-event-remove-row', function(e){
            $(this).closest('.box-section').remove();
            if ($('.js-event-operations .box-section').length < 1) {
                addOperationRow();
            }

            e.preventDefault();
        });

        $(document).on('change', '.row-col-select', function() {
            var operations = (new String($('option:selected',this).data('operations'))).split(','),
                options = $(this).closest('.box-section').find('.row-operation-select option').addClass('hide');

            options.each(function() {
                if($.inArray($(this).attr('value'), operations) > -1) {
                    $(this).removeClass('hide');
                }
            });

            $(this).closest('.box-section').find('.row-operation-select').val('').change();
        }).find('.row-col-select');

        $(document).on('change', '.row-operation-select', function() {
            var value = $(this).val();
            $(this).closest('.box-section').find('.row-operation').addClass('hide')
                    .filter('.operation-' + value).removeClass('hide');
        }).find('.row-operation-select').change();
    {/if}

    $('.js-xml-import').on('click', function (e) {
        e.preventDefault();
        $el = $(this);
        $form = $('#xml_sync');

        if($el.data('confirm')) {
            // Adds confirmation for certain scenarios.
            Custom.confirm({
                message: $(this).data('confirm'),
                success: function () {
                    $form.trigger('submit');
                }
            });
        } else {
            $form.trigger('submit');
        }
        return false;
    });
</script>

{if $sub_page == 'add' || $sub_page == 'edit'}
    <script id="parameter" type="text/template">
        <div class="row xml_sync-parameter margin-top-20">
            <div class="col-xs-6">
                <input type="text" name="xml_sync[parameters][key][]" class="form-control"
                        data-placeholder="{t}xml_sync.parameters.ph.key{/t}"
                        placeholder="{t}xml_sync.parameters.ph.key{/t}">
            </div>
            <div class="col-xs-5">
                <input type="text" name="xml_sync[parameters][value][]"
                        class="form-control"
                        data-placeholder="{t}xml_sync.parameters.ph.value{/t}"
                        placeholder="{t}xml_sync.parameters.ph.value{/t}">
            </div>

            <div class="col-xs-1">
                <button aria-hidden="true" class="close xml_sync-parameter-remove" type="button" style="margin-top: 8px;"></button>
            </div>
        </div>
    </script>
{/if}

{if $sub_page == 'step2'}
    <script type="text/template" class="addable-template">
        <div class="col-xs-4">
            <select name="@NAME@[]" class="form-control select2me @CLASS@" id="Input@NAME@">
                <option value="">{t}xml_sync.form_skeleton.select{/t}</option>

                {foreach from=$xml_data item=v key=k}
                    <option value="{$k}">{$k}</option>
                {/foreach}
            </select>

            <a href="#" class="fal fa-times-circle fa-lg cc-grey" onclick="$(this).parent().remove();" style="margin-top: 8px; float: {if $rtl}left{else}right{/if}"></a>
        </div>
    </script>
{/if}

{if $sub_page == 'step3'}
    <script type="text/template" class="addable-template">
        <div class="box-section">
            <div class="row">
                <div class="col-xs-2">
                    <select name="option[]" class="form-control select2me row-col-select" data-autowidth="true">
                        <option value="">{t}xml_sync.form_skeleton.select{/t}</option>
                        {$options nofilter}
                    </select>
                </div>
                
                <div class="col-xs-2">
                    <select name="action[]" class="form-control select2me row-operation-select">
                        <option value="">{t}xml_sync.form_skeleton.select{/t}</option>
                        
                        {foreach from=$operations item=operation}
                            <option value="{$operation->name}" class="hide">{$operation->label}</option>
                        {/foreach}
                    </select>
                </div>
                
                <div class="col-xs-7">
                    {foreach from=$operations item=operation}
                        {include "./../operations/{$operation->name}.tpl"}
                    {/foreach}
                </div>

                <div class="col-xs-1">
                    <a href="#" class="fal fa-times-circle fa-lg cc-grey js-event-remove-row" style="margin-top: 8px;"></a>
                </div>
            </div>
        </div>
    </script>
{/if}

{/capture}