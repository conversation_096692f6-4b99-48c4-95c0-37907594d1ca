{include file=View::path('layout.header')}

<!-- BEGIN: content -->
<main class="_content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="_section-separator">

                    <form class="cc-form js-number-search-form" data-submit-loader="true" role="form" method="GET" action="{route('cc9848.number')}">
                        <div class="cc-form-section">
                            <div class="cc-form-row">
                                <div class="cc-form-col-9">
                                    {include file=base_path('themes/_global/templates/form-components/input.tpl') type="text" name="query" id="number-search" label="{t}sf.global.ph.search{/t}" required=true}
                                </div>
                                <div class="cc-form-col-3 cc-form-col-align-right">
                                    <button class="cc-button _button-full js-loading" type="submit" disabled>{t}sf.global.act.search{/t}</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <div class="js-number-search-results hidden" style="margin-top: 20px">

                    </div>

                </div>
            </div>
        </div>
    </div>
</main><!--// END: content -->

{capture append="js"}
<script type="text/javascript">
    var body = $('body'),
        holder = $('.js-number-search-results'),
        xhrSearch = undefined,
        xhrBuy = undefined,
        renderTemplate = function(selector, data) {
            var template = '',
                escapeRegExp = function (string) {
                    return string.replace(/[.*+?^${literal}{}{/literal}@()|[\]\\]/g, '\\$&'); // $& means the whole matched string
                };
            if(Object.prototype.toString.call(selector) === '[object Array]') {
                template = $(selector[1], $(selector[0]).html()).html();
            } else {
                template = $(selector).html();
            }

            if(Object.prototype.toString.call(data) !== '[object Object]') {
                data = [];
            }

            for(var key in data) {
                var value = data[key];
                template = template.replace(new RegExp(escapeRegExp(key), 'g'), value);
            }

            return template;
        },
        findTooltip = function(data) {
            if(data.price > 0) {
                return '{t js=true}cc9848::global.service_price{/t} ' + data.price;
            }

            return '';
        };

    $('.js-number-search-form').on('submit', function() {
        var form = $(this);

        holder.addClass('hidden').html('');

        CCHelper.addLoader(body, "{t js=true}sf.global.please_wait{/t}");

        if(xhrSearch) {
            xhrSearch.abort();
        }

        xhrSearch = $.ajax({
            url: form.attr('action'),
            data: form.serialize()
        }).done(function(json) {

            if('message' in json) {
                var alert = $('<div>').addClass('alert alert-danger').html(json.message);
                holder.html(alert).removeClass('hidden');

                CCHelper.removeLoader(body);
                return;
            }

            holder.html('').removeClass('hidden');

            holder.append($('<h3>').html('{t js=true}cc9848::global.results{/t}'));

            var rows = code = '';
            for(var index in json) {
                if(!code) {
                    code = json[index].code;
                }
                rows += renderTemplate('#js-table-draw-row', {
                    '@CODE@': json[index].code,
                    '@COMPANY_NAME@': json[index].company_name,
                    '@ID@': json[index].id,
                    '@NAME@': json[index].name,
                    '@PRICE@': json[index].price,
                    '@WAREHOUSE@': json[index].warehouse,
                    '@QUANTITY_CHANGE@': json[index].quantity > 0 ? renderTemplate(['#js-table-draw-row-quantity', '.has-qty'], {
                        '@ID@': json[index].id,
                        '@CODE@': json[index].code,
                        '@QUANTITY@': json[index].quantity
                    }) : '{t js=true}sf.global.label.out_of_stock{/t}',
                });
            }

            holder.append(renderTemplate('#js-table-draw', {
                '@NUM@': code,
                '@ROWS@': rows
            }));

            CCHelper.removeLoader(body);
            CC.init(holder);
            if($.fn.tooltip) {
                $('[data-toggle="tooltip"]', holder).tooltip();
            }
        }).fail(function(jqXHR) {
            if(jqXHR.responseJSON && 'message' in jqXHR.responseJSON) {
                var alert = $('<div>').addClass('alert alert-danger').html(jqXHR.responseJSON.message);
                holder.html(alert).removeClass('hidden');
            }

            CCHelper.removeLoader(body);
        });

        return false;
    });

    holder.on('submit', '.js-add-to-cart-number', function() {
        var form = $(this);

        holder.find('.alert').remove();

        CCHelper.addLoader(body, "{t js=true}sf.global.please_wait{/t}");

        if(xhrBuy) {
            xhrBuy.abort();
        }

        setTimeout(function() {
            CCHelper.removeLoader(body);
            if(xhrBuy) {
                xhrBuy.abort();
            }
        }, 30000);

        xhrBuy = $.ajax({
            url: form.attr('action'),
            data: form.serialize(),
            method: 'post'
        }).done(function(data) {
            if(data && 'message' in data) {
                var alert = $('<div>').addClass('alert alert-success').html(data.message);
                form.closest('table').after(alert);
            }

            if (data.events) {
                jQuery.each(data.events, function (index, event) {
                    jQuery(document).trigger(event, data);
                });
            }

            CCHelper.removeLoader(body);

            $('[data-widget="cart-compact"]').trigger('cc.ajax.reload');
        }).fail(function(jqXHR) {
            if(jqXHR.responseJSON && 'message' in jqXHR.responseJSON) {
                var alert = $('<div>').addClass('alert alert-danger').html(jqXHR.responseJSON.message);
                holder.append(alert);
            }

            CCHelper.removeLoader(body);
        });

        $(document).on('cc.cart.product.removed', function() {
            holder.find('.alert').remove();
        });

        return false;
    });
</script>

{include file="./includes/js-template.tpl"}

{/capture}

{include file=View::path('layout.footer')}
