<?php

declare(strict_types=1);

namespace Modules\Cloudio\Jobs;

use App\Helper\Plan;
use App\Jobs\Job;
use App\Models\Mongo\ApplicationLogs;
use App\Models\Mongo\ApplicationLogsSystem;
use App\Models\Product\Product;
use Modules\Cloudio\Helpers\Support;
use Modules\Cloudio\Manager;

class GenerateProductShortDescription extends Job
{
    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = 'cloudio';

    /** @var Manager $manager */
    public $manager;

    /**
     * @var array
     */
    public $skill;

    /**
     * @param int $product_id
     * @param mixed[] $settings
     * @param string $log_id
     * @param mixed $publish
     */
    public function __construct(public $product_id, public $settings, public $log_id, public $publish = false)
    {
        $this->site_id = site('site_id');
    }

    /**
     * @return array|bool|int
     * @throws \App\Exceptions\Error
     * @throws \App\Exceptions\Fault
     * @throws \Throwable
     */
    public function execute()
    {
        $site = $this->getSite();
        $this->manager = new Manager();
        if (!$site || $site->plan_expired) {
            return $site ? static::SITE_PLAN_EXPIRED : static::MISSING_SITE;
        }

        if ($site->maintenance) {
            return static::SITE_MAINTENANCE;
        }

        if (!allowSiteByPlatform()) {
            $this->info(sprintf('Migrate JOB from platform %s to platform %s', platform(), sitePlatform()));
            return static::WRONG_PLATFORM;
        }

        $this->skill = $this->manager::skills['shopper_pen'];

        $this->info('CloudIO: generate short description for product  #' . $this->product_id);

        $getLog = ApplicationLogs::find($this->log_id);
        if (!$getLog) {
            $this->manager->removeSetting('job_product_short_description_' . $this->product_id);
            return static::DESTROY;
        }

        if ($this->settings['tokens'] > Plan::remaining($this->manager::FEATURE)) {
            $getLog->update([
                'progress' => 'complete',
                'request' => $this->settings,
                'response' => sprintf(__('cloudio::app.generate_job_error_%1$s_%2$s'), $this->settings['tokens'], Plan::remaining($this->manager::FEATURE)),
                'is_error' => true,
                'wait_event' => $this->manager::LOG_EVENTS['tokens'],
                'event_data' => [
                    'mapping' => 'cloudio_generate_short_description',
                    'product_id' => $this->product_id,
                    'settings' => $this->settings,
                    'log_id' => $this->log_id,
                    'publish' => $this->publish,
                ]
            ]);
            //$this->manager->removeSetting('job_product_short_description_' . $this->product_id);
            $this->error('CloudIO: generate short description for product failed. Minimum tokens: ' . $this->settings['tokens'] . ', tokens ' . Plan::remaining($this->manager::FEATURE));
            return static::DESTROY;
        }

        if (empty($this->manager->getLiveSetting('job_product_short_description_' . $this->product_id))) {
            $getLog->update([
                'progress' => 'complete',
                'request' => $this->settings,
                'response' => 'CloudIO: generate short description for product failed. No added settings',
                'is_error' => true
            ]);
            $this->error('CloudIO: generate short description for product failed. No added settings');
            return static::DESTROY;
        }

        try {
            $getLog->update([
                'progress' => 'in_progress',
                'request' => $this->settings,
            ]);
            $this->prepare($getLog);
        } catch (\Exception $exception) {
            $getLog->update([
                'progress' => 'complete',
                'request' => $this->settings,
                'response' => __('cloudio::app.unknown_error'),
                'is_error' => true
            ]);
            $this->manager->removeSetting('job_product_short_description_' . $this->product_id);
            $this->error('CloudIO ERROR: generate short description for product ' . $exception->getFile() . ':' . $exception->getLine() . ' - ' . $exception->getMessage());
        }

        return static::DESTROY;
    }

    /**
     * @return void
     * @throws \App\Exceptions\Error
     * @param mixed $mainLog
     */
    protected function prepare($mainLog)
    {
        $language = !empty(config('languages.languages')[$this->settings['language']]) ? ucfirst((string) config('languages.languages')[$this->settings['language']]) : 'English';
        $condition = $this->getTextByKeys($this->settings['selected'], $this->product_id);
        $condition['{language}'] = $language;
        $condition['{store_url}'] = site()->getSiteUrl('primary');
        $condition['{store_name}'] = setting('site_name');
        $condition['{delivery_price}'] = !empty($this->settings['delivery_price']) ? $this->settings['delivery_price'] : null;
        $condition['{other_facts}'] = !empty($this->settings['other_facts']) ? $this->settings['other_facts'] : null;
        if (!empty($this->settings['selectedImage'])) {
            $imageDescription = $this->analyzeImage($this->settings['selectedImage'], $condition['{product_title}'] ?? null);
            if (!empty($imageDescription['status'])) {
                $condition['{image_description}'] = $imageDescription['text'];
                $createLog = ApplicationLogs::create([
                    'site_id' => site()->site_id,
                    'record_id' => $this->manager->getApp()->id,
                    'record_type' => $this->manager->getApp()::class,
                    'resource_id' => $this->product_id,
                    'type' => $this->manager::skills['vision_sense']['log'],
                    'progress' => 'complete',
                    'request' => ['image' => $this->settings['selectedImage'], 'promt' => ''],
                    'response' => $imageDescription['text'],
                    'tokens' => $imageDescription['tokens'],
                    'is_error' => false,
                    'system_id' => $mainLog->_id,
                ]);
                $images = Support::prepareImages($this->settings['selectedImage']);
                $this->manager->createTokenLog(count($images) * 500, $imageDescription['tokens'], $createLog->_id);
            }
        }

        $text = Support::conditionShortDescriptionClear($condition, $this->settings);
        $response = $this->generate($text, $language);
        $ccTokens = $this->settings['tokens'];
        if ($response['status']) {
            $setting = $this->manager->getLiveSetting('job_product_short_description_' . $this->product_id);
            $totalTokens = (int)$response['tokens'];
            if (!empty($this->settings['seo']) && !empty($this->settings['seo']['enable'])) {
                $ccTokens -= 800;
                $rankMasterLog = ApplicationLogs::create([
                    'record_id' => $this->manager->getApp()->id,
                    'record_type' => $this->manager->getApp()::class,
                    'resource_id' => $this->product_id,
                    'type' => $this->manager::skills['rank_master']['log'],
                    'progress' => 'complete',
                    'request' => $text,
                    'response' => ['type' => 'product_short_description', 'text' => $response['text']],
                    'system_id' => $mainLog->_id,
                ]);
                $this->manager->createTokenLog(800, 0, $rankMasterLog->_id);
            }

            if (!empty($this->settings['selectedImage'])) {
                $images = Support::prepareImages($this->settings['selectedImage']);
                $ccTokens -= count($images) * 500;
            }

            $mainLog->update([
                'progress' => 'complete',
                'request' => $text,
                'response' => ['type' => 'product_short_description', 'text' => $response['text']],
                'language' => $language,
                'tokens' => $totalTokens
            ]);
            if (!empty($mainLog->system_id)) {
                $systemLog = ApplicationLogsSystem::find($mainLog->system_id);
                if ($systemLog) {
                    $systemLog->update([
                        'tokens' => (int)$systemLog->tokens + (int)$this->settings['tokens']
                    ]);
                }
            }

            $this->manager->createTokenLog($totalTokens > 0 ? $ccTokens : 0, $totalTokens, $this->log_id);
            $this->manager->removeSetting('job_product_short_description_' . $this->product_id);
            if ($this->publish) {
                Support::populateProductDescription('short_description', $response['text'], $this->product_id);
            }

            $this->info('CloudIO: generate short description for product  #' . $this->product_id . ' success');
        } else {
            $mainLog->update([
                'progress' => 'complete',
                'request' => $text,
                'response' => $response['message'],
                'is_error' => true
            ]);
            $this->manager->removeSetting('job_product_short_description_' . $this->product_id);
            $this->error('CloudIO: generate short description for product  #' . $this->product_id . ' failed. Message: ' . $response['message'] . ', code: ' . $response['code']);
        }
    }


    /**
     * @param $keys
     * @param $product_id
     * @return array|void
     */
    protected function getTextByKeys($keys, $product_id)
    {
        $product = Product::with(['categories', 'vendor', 'tags', 'variants'])
            ->where('id', $product_id)->first();
        if ($product) {
            $variables = [];
            $product_keys = ['name', 'description', 'short_description'];
            foreach ($product_keys as $pk) {
                if (in_array($pk, $keys) && !empty($product->$pk)) {
                    if ($pk == 'name') {
                        $variables['{product_title}'] = strip_tags((string) $product->$pk);
                    }

                    if ($pk == 'description') {
                        $variables['{product_description}'] = strip_tags((string) $product->$pk);
                    }

                    if ($pk == 'short_description') {
                        $variables['{product_short_description}'] = strip_tags((string) $product->$pk);
                    }

                }
            }

            if (!empty($product->properties) && !empty($product->propertyOptions) && in_array('properties', $keys)) {
                $properties = [];
                foreach ($product->properties as $property) {
                    $properties[] = $property->name . ': ' . $product->propertyOptions->where('property_id', $property->id)->implode('value', ', ');
                }

                $variables['{product_characteristics}'] = implode(', ', array_unique($properties));
            }

            if (!empty($product->variants) && in_array('variants', $keys)) {
                $variants = [];
                if (!empty($product->p1)) {
                    $variants[] = $product->p1 . ': ' . $product->variants->unique('v1')->implode('v1', ', ');
                }

                if (!empty($product->p2)) {
                    $variants[] = $product->p2 . ': ' . $product->variants->unique('v2')->implode('v2', ', ');
                }

                if (!empty($product->p3)) {
                    $variants[] = $product->p3 . ': ' . $product->variants->unique('v3')->implode('v3', ', ');
                }

                $variables['{product_variants}'] = implode(', ', array_unique($variants));
            }

            if (in_array('vendor', $keys)) {
                $variables['{brand}'] = $product->vendor->name;
            }

            if (!empty($product->category) && !empty($product->category_id) && in_array('category', $keys)) {
                $variables['{category}'] = $product->category->path->implode('name', ' > ');
            }

            if (!empty($product->tags) && in_array('tags', $keys)) {
                $variables['{tags}'] = $product->tags->implode('tag', ', ');
            }

            return $variables;
        }
    }

    /**
     * @param $text
     * @param $language
     * @return array
     */
    protected function generate($text, $language): array
    {
        $messages = [
            ['role' => 'user', 'content' => $text],
            ['role' => 'system', 'content' => 'Write a short description in ' . ucfirst((string) $language) . ' based on product attributes—limit: 100 words or less. No hashtags.']
        ];
        return $this->manager->gptCompletionsRequest($messages);
    }

    /**
     * @param $image_url
     * @param $productName
     * @return array
     */
    protected function analyzeImage($image_url, $productName = null): array
    {
        $systemText = 'Your role is to create succinct and precise product descriptions. Use the images provided to identify the product, named " {PRODUCT_NAME} ", as a reference point. Your task is to distill the essential attributes and unique qualities of the product from these images, providing clear and compelling descriptions for your team members to build upon.';
        if (!empty($productName) && strpos($systemText, '{PRODUCT_NAME}')) {
            $systemText = str_replace('{PRODUCT_NAME}', $productName, $systemText);
        }

        $userText = 'Extract and summarize the key features, highlights, and advantages of " {PRODUCT_NAME} " from the images provided. Focus on grouping these elements by their distinct characteristics, ensuring clarity and brevity in your description, and avoid any superfluous details or descriptions.';
        if (!empty($productName) && strpos($userText, '{PRODUCT_NAME}')) {
            $userText = str_replace('{PRODUCT_NAME}', $productName, $userText);
        }

        $userPromt = [];
        $userPromt[] = [
            'type' => 'text',
            'text' => $userText
        ];
        $image_url = Support::prepareImages($image_url);
        foreach ($image_url as $img) {
            $userPromt[] = [
                'type' => 'image_url',
                'image_url' => $img
            ];
        }

        $messages = [
            [
                "role" => 'system',
                'content' => $systemText
            ],
            [
                "role" => "user",
                "content" => $userPromt
            ]
        ];
        return $this->manager->gptVisionRequest($messages);
    }
}
