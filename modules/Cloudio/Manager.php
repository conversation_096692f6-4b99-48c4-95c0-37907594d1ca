<?php

declare(strict_types=1);

namespace Modules\Cloudio;

use App\Contracts\Subscriptions\MoreRecordsSubscription;
use App\Exceptions\Error;
use App\Models\Apps\ApplicationTokens;
use App\Models\Mongo\ApplicationLogs;
use App\Models\Queue\SiteQueue;
use Modules\Apps\Abstractions\Managers\AbstractImportsExportsManager;
use App\Models\Gate\PlanFeaturePack;
use Modules\Cloudio\Helpers\ExternalServices;

class Manager extends AbstractImportsExportsManager implements MoreRecordsSubscription
{
    public const APP_KEY = 'cloudio';

    //    const GPT_KEY = '***************************************************';
    public const GPT_KEY = '********************************************************';

    public const IMAGE_API_KEY = 'A13A91DD-BAF2-4793-8B6C-5914077149A46A65DC72-94BD-4605-A9D8-3CB76572BFBE';

    public const MIN_TOKENS = 3000;

    public const FEATURE = 'cloudio_ai';

    public const SKILL_DEFAULT = [
        'shopper_pen',
        'update_master',
    ];

    public const LOG_EVENTS = [
        'tokens' => 'tokens',
    ];

    public const skills = [
        'shopper_pen' => [
            'icon' => 'far fa-magic',
            'isFree' => false,
            'log' => 'shopper_pen',
            'multiply' => 1,
            'min_tokens' => 1,
            'settings' => false,
            'is_visible' => true,
        ],
        'shopper_pen_advanced' => [
            'icon' => 'far fa-magic',
            'isFree' => false,
            'log' => 'shopper_pen_system',
            'multiply' => 1,
            'min_tokens' => 3000,
            'settings' => true,
            'is_visible' => true,
        ],
        'rank_master' => [
            'icon' => 'far fa-chart-line',
            'isFree' => false,
            'log' => 'rank_master',
            'multiply' => [
                'keywords' => 50,
                'addition' => 100,
                'description' => 400
            ],
            'is_visible' => true,
        ],
        'vision_sense' => [
            'icon' => 'far fa-image',
            'isFree' => false,
            'log' => 'vision_sense',
            'multiply' => 1500,
            'settings' => false,
            'is_visible' => true,
        ],
        'shopper_sense' => [
            'icon' => 'far fa-radar',
            'isFree' => true,
            'log' => 'system_shopper_sense',
            'settings' => false,
            'is_visible' => true,
        ],
        'update_master' => [
            'icon' => 'far fa-comment-alt-smile',
            'isFree' => true,
            'log' => 'update_master',
            'settings' => false,
            'is_visible' => true,
        ],
        'translator' => [
            'icon' => 'far fa-language',
            'isFree' => false,
            'log' => 'translator',
            'settings' => true,
            'is_visible' => true
        ],

        'shopper_pen_category' => [
            'icon' => 'far fa-magic',
            'isFree' => false,
            'log' => 'shooper_pen_category',
            'multiply' => 1,
            'min_tokens' => 1,
            'settings' => false,
            'is_visible' => true
        ],
//        'facebook_content' => [
//            'icon' => 'far fa-facebook-f',
//            'isFree' => false,
//            'log' => 'system',
//            'comming_soon' => true,
//        ],
//        'landing_page_copy' => [
//            'icon' => 'far fa-edit',
//            'isFree' => false,
//            'log' => 'landing_page_copy',
//            'comming_soon' => true,
//        ],

    ];

    public const attempts = 5;

    /**
     * @return void
     * @throws Error
     */
    public function afterInstall(): void
    {
        foreach (self::SKILL_DEFAULT as $skill) {
            $this->updateSetting($skill, true);
        }
    }

    /**
     * @return array
     */
    public function appInfo(): array
    {
        return [static::APP_KEY => [
            'direct' => 'apps/cloudio',
            'uninstall' => 'apps/cloudio/uninstall',
            'icon' => 'icon-cloudio.png',
            'name' => __('cloudio::app.info.title'),
            'description' => __('cloudio::app.help.install'),
        ]];
    }


    /**
     * @param $type
     * @param $token
     * @param $log_id
     * @return void
     * @throws Error
     */
    public function createTokenLog($cc_tokens, $original_tokens, $log_id): void
    {
        $getFeature = PlanFeaturePack::findByMap([static::FEATURE]);
        $singlePriceCC = ($getFeature->price / 100) / $getFeature->value;
        $singlePriceGpt = 0.00002;
        if ($cc_tokens > 0) {
            $ccPrice = ($cc_tokens * $singlePriceCC) * 100;
        }

        if ($original_tokens > 0) {
            $gptPrice = ($original_tokens * $singlePriceGpt) * 100;
        }

        ApplicationTokens::create([
            'record_id' => $this->getApp()->id,
            'record_type' => $this->getApp()::class,
            'cc_tokens' => $cc_tokens,
            'cc_price' => $ccPrice ?? 0,
            'original_tokens' => $original_tokens,
            'original_price' => $gptPrice ?? 0,
            'log_id' => $log_id,
            'mapping' => self::APP_KEY
        ]);
    }


    /**
     * @param null|mixed $skill
     * @return array
     * @throws Error
     */
    public function skills($skill = null)
    {
        foreach (static::skills as $key => $info) {
            if ($info['is_visible']) {
                $services[] = [
                    'key' => $key,
                    'icon' => $info['icon'],
                    'title' => __('cloudio::app.services.' . $key),
                    'description' => __('cloudio::app.services.' . $key . '_description'),
                    'description_short' => __('cloudio::app.services.' . $key . '_description_short'),
                    'isActive' => $this->getSetting($key, false),
                    'isFree' => $info['isFree'],
                    'history' => $info['log'] ?? null,
                    'comming_soon' => $info['comming_soon'] ?? false,
                    'settings' => $info['settings'] ?? false,
                ];
            }
        }

        if (!empty($skill)) {
            $getskill = array_filter(array_map(function (array $sk) use ($skill) {
                if ($sk['key'] == $skill) {
                    return $sk;
                }
            }, $services));
            if (!empty($getskill)) {
                return \Illuminate\Support\Arr::first($getskill);
            }

            return null;
        }

        return $services;
    }

    /**
     * @param $key
     * @return mixed
     * @throws Error
     */
    public function skillIsActive($key)
    {
        return $this->getSetting($key, false);
    }

    /**
     * @param $messages
     * @param $model
     * @param $temperature
     * @return array
     */
    public function gptCompletionsRequest($messages, $model = 'gpt-4o-mini', $temperature = 0.3, $max_tokens = 250, $type = 'text')
    {
        return ExternalServices::gptCompletionsRequest(self::GPT_KEY, $messages, $model, $temperature, $max_tokens, $type);
    }

    /**
     * @param $promt
     * @param $imageUrl
     * @param $logId
     * @return array
     * @throws Error
     */
    public function ascticaRequest($promt, $imageUrl, $logId = null): array
    {
        return ExternalServices::ascticaRequest(self::IMAGE_API_KEY, $promt, $imageUrl, $logId);
    }

    /**
     * @param string $keyword
     * @param string $language
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function rankMatchRequest(string $keyword, string $language = 'bg'): array
    {
        return ExternalServices::rankMatchRequest($keyword, $language);
    }

    /**
     * @param $message
     * @return array
     */
    public function gptVisionRequest($message): array
    {
        return ExternalServices::gptVisionRequest(self::GPT_KEY, $message);
    }

    /**
     * @inheritDoc
     * @return bool
     * @throws \Exception
     */
    public function postSubscription(): ?bool
    {
        $waitingTasks = ApplicationLogs::where('wait_event', static::LOG_EVENTS['tokens'])->count() > 0;
        if ($waitingTasks) {
            SiteQueue::executeQueueTask('cloudio_rebuild_jobs');
        }

        if (count($this->getSetting('auto_translate', [])) > 0) {
            SiteQueue::createQueueByMapping('cloudio_translator_auto');
        }

        return true;
    }
}
