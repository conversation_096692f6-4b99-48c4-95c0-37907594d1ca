<?php

declare(strict_types=1);

namespace Modules\Core\Navigations\CoreNavigations\AdminLinks\Marketing\UpCrossSell;

use Apps;
use Modules\Core\Navigations\CoreNavigations\AbstractLink;

class SettingsLink extends AbstractLink
{
    #[\Override]
    public function getName(): string
    {
        return 'marketing.up_cross_sell.settings';
    }

    #[\Override]
    public function isVisible(): bool
    {
        return true;
    }

    /**
     * @return bool
     */
    #[\Override]
    public function isInstalledApp(): bool
    {
        return Apps::installed('up_cross_sell');
    }

    /**
     * @return null|string
     */
    public function getAppMapping(): ?string
    {
        return 'up_cross_sell';
    }

    #[\Override]
    public function getLabel(): string
    {
        return 'Settings';
    }

    #[\Override]
    public function getUrl(): ?string
    {
        return route('apps.up_cross_sell.settings');
    }
}
