<?php

declare(strict_types=1);

namespace Modules\Core\SmartCollections\Helpers;

use App\Models\Category\PropertyOption;
use App\Models\Product\Category;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Collection;
use Modules\Core\Helpers\Eloquent\Model\AbstractMagicFormatter;
use Modules\Core\SmartCollections\Models\SelectionConditions;

/**
 * Class SmartCollectionConditionsFormatter
 *
 * Formatter for selection conditions with dynamic properties.
 *
 * @property string|null $record_type
 * @property-read array $records
 * @property-read array $records_init
 * @property string|null $operation
 * @property string|null $value_type
 * @property mixed $value
 * @property mixed $sub_value
 */
class SmartCollectionConditionsFormatter extends AbstractMagicFormatter
{
    protected \Illuminate\Support\Collection $conditions;

    /** @var array $__records */
    protected $__records;

    /**
     * SmartCollectionConditionsFormatter constructor.
     *
     * @param Collection $conditions The collection of conditions.
     * @param SmartCollectionFormatter $collection The collection formatter instance.
     * @param SmartCollectionRowFormatter $row The row formatter instance.
     */
    protected function __construct(Collection $conditions, protected \Modules\Core\SmartCollections\Helpers\SmartCollectionFormatter $collection, protected \Modules\Core\SmartCollections\Helpers\SmartCollectionRowFormatter $row)
    {
        $this->conditions = $this->transformConditions($conditions);
    }

    /**
     * Create an instance of the formatter.
     *
     * @param Collection $conditions The collection of conditions.
     * @param SmartCollectionFormatter $collection The collection formatter instance.
     * @param SmartCollectionRowFormatter $row The row formatter instance.
     * @return static The instance of the formatter.
     */
    public static function make(Collection $conditions, SmartCollectionFormatter $collection, SmartCollectionRowFormatter $row): SmartCollectionConditionsFormatter
    {
        return new static($conditions, $collection, $row);
    }

    /**
     * Get the record type.
     *
     * @return null|string The record type.
     */
    public function getRecordType(): ?string
    {
        return optional($this->conditions->first())->getRecordType();
    }

    /**
     * Get the records.
     *
     * @return array The records.
     */
    public function getRecords(): array
    {
        if ($this->getRecordType()) {
            return array_column($this->getRecordsInit(), 'id');
        }

        return [];
    }

    /**
     * Get the initial records.
     *
     * @return array The initial records.
     */
    public function getRecordsInit(): array
    {
        if ($this->getRecordType()) {
            return $this->conditions->map(function (SmartCollectionConditionFormatter $formatter): ?array {
                $model = $formatter->getRecord();
                if ($model === null) {
                    return null;
                }

                return [
                    'id' => $model->getKey(),
                    'name' => $this->guessNameByModel($model),
                    'meta' => $formatter->getMeta(),
                ];
            })->filter()->values()->all();
        }

        return [];
    }

    /**
     * Get the operation.
     *
     * @return null|string The operation.
     */
    public function getOperation(): ?string
    {
        return optional($this->conditions->first())->getOperation();
    }

    /**
     * Get the value type.
     *
     * @return null|string The value type.
     */
    public function getValueType(): ?string
    {
        return optional($this->conditions->first())->getValueType();
    }

    /**
     * Get the value.
     *
     * @return mixed The value.
     */
    public function getValue()
    {
        if ($this->getRecordType()) {
            return;
        }

        return optional($this->conditions->first())->getValue();
    }

    public function getMeta()
    {
        return optional($this->conditions->first())->getMeta();
    }

    /**
     * Get the sub value.
     *
     * @return mixed The sub value.
     */
    public function getSubValue()
    {
        if ($this->getRecordType()) {
            return;
        }

        return optional($this->conditions->first())->getSubValue();
    }

    /**
     * @param array $data
     * @return mixed
     */
    #[\Override]
    public function fill(array $data): AbstractMagicFormatter
    {
        if ($this->collection->getId()) {
            return $this->fillForUpdate($data);
        } else {
            return $this->fillForCreate($data);
        }
    }

    public function push(): SmartCollectionConditionsFormatter
    {
        $this->conditions->map(function (SmartCollectionConditionFormatter $formatter): void {
            $formatter->push();
        });

        return $this;
    }

    /**
     * Convert the SmartCollectionConditionsFormatter instance to an array.
     *
     * @return array The array representation of the instance.
     */
    public function toArray(): array
    {
        return [
            'record_type' => $this->getRecordType(),
            'records' => $this->getRecords(),
            'records_init' => $this->getRecordsInit(),
            'operation' => $this->getOperation(),
            'value_type' => $this->getValueType(),
            'value' => $this->getValue(),
            'sub_value' => $this->getValue(),
            'meta' => $this->getMeta(),
        ];
    }

    /**
     * Transform the conditions to an array.
     *
     * @param Collection $conditions The collection of conditions.
     * @return Collection The transformed conditions.
     */
    protected function transformConditions(Collection $conditions): Collection
    {
        return $conditions->map(fn(SelectionConditions $condition): \Modules\Core\SmartCollections\Helpers\SmartCollectionConditionFormatter => SmartCollectionConditionFormatter::make($condition, $this->collection, $this->row));
    }

    /**
     * @param array $data
     * @return AbstractMagicFormatter
     */
    protected function fillForCreate(array $data): AbstractMagicFormatter
    {
        return $this->fillForUpdate($data);
        //        if (empty($data['record_type']) || !array_key_exists($data['record_type'], Relation::morphMap())) {
        //            $this->conditions->push(
        //                SmartCollectionConditionFormatter::make(new SelectionConditions(), $this->collection, $this->row)
        //                    ->fill($data)
        //            );
        //        } else {
        //            foreach ($data['records'] ?? [] as $recordId) {
        //                $this->conditions->push(SmartCollectionConditionFormatter::make(new SelectionConditions([
        //                    'record_id' => $recordId,
        //                    'record_type' => $data['record_type'],
        //                ]), $this->collection, $this->row)->fill($data));
        //            }
        //        }
        //
        //        return $this;
    }

    /**
     * @param array $data
     * @return AbstractMagicFormatter
     */
    protected function fillForUpdate(array $data): AbstractMagicFormatter
    {
        if (empty($data['record_type']) || !array_key_exists($data['record_type'], Relation::morphMap())) {
            if ($this->conditions->isEmpty()) {
                $this->conditions->push(
                    SmartCollectionConditionFormatter::make(new SelectionConditions(), $this->collection, $this->row)
                );
            }

            $set = false;
            foreach ($this->conditions as $key => $condition) {
                if ($set) {
                    $this->conditions->forget($key);
                } else {
                    $this->conditions[$key]->fill($data);
                    $set = true;
                }
            }
        } else {
            $data['records'] ??= [];//collect($data['records'] ?? [])->sort()->values()->all();
            $max = max($this->conditions->count(), count($data['records']));
            for ($i = 0; $i < $max; $i++) {
                if (!array_key_exists($i, $data['records'])) {
                    $this->conditions->forget($i);
                } elseif ($this->conditions->has($i)) {
                    $this->conditions[$i]->fill(array_merge($data, ['record_id' => $data['records'][$i]]));
                } else {
                    $this->conditions->push(SmartCollectionConditionFormatter::make(new SelectionConditions([
                        'record_id' => $data['records'][$i],
                        'record_type' => $data['record_type'],
                    ]), $this->collection, $this->row)->fill(array_merge($data, ['record_id' => $data['records'][$i]])));
                }
            }
        }

        return $this;
    }

    /**
     * @param Model $model
     * @return mixed|string
     */
    protected function guessNameByModel(Model $model)
    {
        if ($model instanceof Category) {
            return $model->path->implode('name', ' > ');
        }

        if ($model instanceof PropertyOption) {
            return sprintf('%s: %s', $model->property->name, $model->value);
        }

        return $model->name ?? $model->title ?? $model->tag ?? $model->getKey();
    }
}
