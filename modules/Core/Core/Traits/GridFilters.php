<?php

declare (strict_types=1);
namespace Modules\Core\Core\Traits;

use App\Exceptions\Error;
use App\Scopes\Sorting;
use Closure;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Traits\Localizable;
use Modules\Core\Core\Helpers\AbstractGridFilter;
use Modules\Core\Core\Helpers\CustomAwarePaginator;
use Modules\Core\Core\Helpers\Grid;
trait GridFilters
{
    use Localizable;
    /** @var AbstractGridFilter $_filterObject */
    protected $_filterObject;

    /**
     * @param string $className
     * @param Request $request
     * @return AbstractGridFilter
     * @throws Error
     * @throws Exception
     */
    public function compileSearchObject(string $className, Request $request): AbstractGridFilter
    {
        return $this->initSearchObject($className, $request)->compile();
    }

    /**
     * Parent Name: GridFilters
     * Name: initSearchObject
     * Type: public
     *
     * DESCRIPTION: This method initializes an instance of an AbstractGridFilter based on the provided class name or the existing instance. It validates the request filters, ensuring they are in the correct format, and sets them for the filter object. It throws exceptions if the provided class name does not exist or if the instantiated object is not an instance of AbstractGridFilter.
     *
     * BUSINESS_LOGIC: The purpose of this method is to seamlessly create and configure a filter object, preparing it to be used with request data, while applying necessary validations. This is integral for enabling grid filtering capabilities in the application.
     *
     * SUMMARY: Initializes and validates a filter object for grid operations based on input parameters.
     *
     * @param AbstractGridFilter|string|null $className The filter class name to instantiate or an existing filter object.
     * @param Request $request The HTTP request containing filter parameters.
     * @return AbstractGridFilter Returns the initialized filter instance ready for use.
     * @throws Error Throws an error if the class name provided does not correspond to a valid filter class.
     * @throws Exception Throws an exception if the instance created is not a valid AbstractGridFilter.
     */
    public function initSearchObject($className, Request $request): AbstractGridFilter
    {
        if (is_string($className) && !class_exists($className)) {
            throw new Exception(sprintf('Filter "%s" is not exists!', $className));
        }

        /** @var AbstractGridFilter $instance */
        $instance = is_string($className) ? new $className($this) : $className;
        if (!$instance instanceof AbstractGridFilter) {
            throw new Exception(sprintf('Filter "%s" is instance of "%s"!', $className, AbstractGridFilter::class));
        }

        $this->withLocale('en', function () use ($request, $instance): void {
            $instance->validateRequest($request);
        });
        $filters = $request->query('filters');
        if (!is_array($filters)) {
            $filters = [];
        }

        $instance->setRequestFilters($filters);
        $this->_filterObject = $instance;
        return $this->_filterObject;
    }

    /**
     * Parent Name: GridFilters
     * Name: getFilterObject
     * Type: public
     *
     * DESCRIPTION: Retrieves the currently assigned filter object used in the grid filters implementation. The method checks for the availability of the filter object and returns it. If no filter object is set, it returns null. This allows for dynamic filtering capabilities within the grid system.
     *
     * BUSINESS_LOGIC: The purpose of this method is to provide access to the filter object that is configured for the grid. It enables other parts of the application to retrieve the filter without needing to know the internal representation of the filter object itself. This is essential for the functionality of the grid filtering mechanism within the application.
     *
     * SUMMARY: Returns the current filter object or null if not set.
     *
     * @return AbstractGridFilter|null An instance of AbstractGridFilter if set, otherwise null.
     */
    public function getFilterObject(): ?AbstractGridFilter
    {
        return $this->_filterObject;
    }

    /**
     * Parent Name: GridFilters
     * Name: getRecords
     * Type: public
     *
     * DESCRIPTION: This method retrieves a collection of records based on the provided model and optional filtering class. It acts as a wrapper around the internal _getRecords method, allowing for additional custom filtering through a closure and request parameters.
     * The method ensures that the records are paginated using a CustomAwarePaginator allowing for efficient data handling in grid views.
     *
     * BUSINESS_LOGIC: The getRecords method is designed to facilitate the retrieval and pagination of data records from the given model. It can incorporate additional filtering logic through the provided class name and closure, making it flexible for various grid implementations in the application.
     *
     * SUMMARY: Retrieves paginated records from a specified model with optional filters.
     *
     * @param mixed $model The model from which records are to be retrieved.
     * @param AbstractGridFilter|string|null $className Optional filtering class name or instance to apply to the records.
     * @param Closure|null $grid Optional closure for additional grid configurations or filtering logic.
     * @param Request|null $request The incoming request instance, which may contain additional filters.
     * @return CustomAwarePaginator Returns a paginated collection of the retrieved records.
     * @throws Error Throws an Error in case of issues during record retrieval.
     */
    public function getRecords($model, $className = null, ?Closure $grid = null, ?Request $request = null): CustomAwarePaginator
    {
        return $this->_getRecords($model, $className, $grid, $request);
    }

    /**
     * @param $model
     * @param AbstractGridFilter|string|null $className
     * @param Closure|null $grid
     * @param Request|null $request
     * @return CustomAwarePaginator
     * @throws Error
     */
    public function getAllRecords($model, $className = null, ?Closure $grid = null, ?Request $request = null): CustomAwarePaginator
    {
        return $this->_getRecords($model, $className, $grid, $request, true);
    }

    /**
     * Parent Name: GridFilters
     * Name: _getRecords
     * Type: protected
     *
     * DESCRIPTION: This method is responsible for retrieving a list of records from a given model, optionally applying filters and pagination based on a specified grid configuration. It evaluates if a filter class is provided, initializes it, and wraps the model accordingly. The method then determines the appropriate list of records based on the grid's configuration and whether all records are requested or not.
     * BUSINESS_LOGIC: The method facilitates dynamic retrieval of record lists with filtering and ordering capabilities. It allows for flexible data handling based on user-defined grid parameters and manages pagination to enhance data presentation in UI components.
     * SUMMARY: Retrieves and paginates records from a specified model based on grid filters and configuration.
     *
     * @param mixed $model The model from which records are to be retrieved.
     * @param AbstractGridFilter|string|null $className Optional filter class name to be initialized.
     * @param Closure|null $grid Optional closure for grid configuration.
     * @param Request|null $request Optional HTTP request object to access request data.
     * @param bool $all Indicates if all records should be retrieved without pagination.
     * @return CustomAwarePaginator A paginator class that contains the retrieved records and pagination metadata.
     * @throws Error If an error occurs during filter initialization or record retrieval.
     */
    protected function _getRecords($model, $className = null, ?Closure $grid = null, ?Request $request = null, bool $all = false): CustomAwarePaginator
    {
        $filters = null;
        if (!is_null($className)) {
            $filters = $this->initSearchObject($className, $request ?: request())->compile();
            $model = $filters->wrapModel($model);
        }

        /** @var Grid $grid */
        $grid = with($grid, function (?Closure $grid) use ($model): \Modules\Core\Core\Helpers\Grid {
            $model = $model->getModel();
            $grid = value($grid);
            return $grid instanceof Grid ? $grid : new Grid($model->getKeyName(), 'desc');
        });
        $grid->setFilters($filters);
        if ($all) {
            $list = $this->getAllListRecords($grid, $model);
        } else {
            $list = $this->getListRecords($grid, $model);
        }

        $pagination = new CustomAwarePaginator($list, optional($this->getFilterObject())->getFilters() ?? [], $all ? [] : optional($this->getFilterObject())->getSavedFilters() ?? []);
        $ordering = $grid->getOrdering();
        if ($ordering) {
            $pagination->setMeta('ordering', array_map(fn($o): string => sprintf('%s-%s', $o['column'], $o['direction']), $ordering));
        } else {
            $order = $grid->getOrder();
            if ($order) {
                if (is_string($order)) {
                    $pagination->setMeta('order', $order)->setMeta('direction', $grid->getDirection());
                } elseif ($order instanceof Closure && $grid->getOrderKey()) {
                    $pagination->setMeta('order', $grid->getOrderKey())->setMeta('direction', $grid->getDirection());
                }
            }
        }

        if ($filters) {
            $pagination->setMeta('allowed_filters', $filters->getAllowedFilters());
            $pagination->setMeta('allowed_orders', $filters->getAllowedOrders());
        }

        return $pagination;
    }

    /**
     * Parent Name: GridFilters
     * Name: getListRecords
     * Type: protected
     *
     * DESCRIPTION: This method retrieves a paginated list of records based on the provided grid and query parameters. It applies sorting based on the ordering rules defined in the grid object, including callback support for complex sorting. The method also handles cases where the model instance might be using a guard, ensuring proper sorting while avoiding restricted fields.
     * Additionally, it checks for sorting scopes and applies default ordering if no specific order has been set. The records are subsequently paginated according to the specified page size and the requested page number.
     *
     * BUSINESS_LOGIC: The purpose of this method is to provide a flexible way to obtain a sorted and paginated list of model records. By utilizing grid-based settings, it ensures that the results are tailored to the needs of the view layer while respecting any defined sorting logic or restrictions on model attributes.
     *
     * SUMMARY: Retrieves and paginates a list of records based on grid settings and query.
     *
     * @param \Modules\Core\Core\Helpers\Grid $grid The grid object containing pagination and sorting information.
     * @param mixed $query The query builder instance for fetching records from the database.
     * @return \Illuminate\Pagination\LengthAwarePaginator A paginator instance containing the paginated results.
     */
    protected function getListRecords(Grid $grid, $query): \Illuminate\Pagination\LengthAwarePaginator
    {
        if (!$this->checkSortingScope($query)) {
            /** @var Model $model */
            $model = $query->getModel();
            if ($ordering = $grid->getOrdering()) {
                foreach ($ordering as $order) {
                    if ($order['callback'] instanceof Closure) {
                        $methodName = $order['callback'];
                        $methodName($query);
                    } else {
                        $query->orderBy($order['column'], $order['direction']);
                    }
                }
            } elseif ($order = $grid->getOrder()) {
                if ($order instanceof Closure) {
                    $order($query);
                } else if ($this->isMongoInstance($query) && $order == 'id') {
                    $query->orderBy('_id', $grid->getDirection());
                } else {
                    $query->orderBy($order, $grid->getDirection());
                }
            } elseif (($guarded = $model->getGuarded()) && in_array($model->getKeyName(), $guarded)) {
                $unions = count($query->getQuery()->unions ?: []);
                if ($unions) {
                    $query->orderBy($model->getKeyName(), 'desc');
                } else {
                    $query->orderBy($model->getTable() . '.' . $model->getKeyName(), 'desc');
                }
            }
        }

        return $query->paginate($grid->getPerpage(), ['*'], 'page', $grid->getPage());
    }

    /**
     * Parent Name: GridFilters
     * Name: isMongoInstance
     * Type: protected
     *
     * DESCRIPTION: This method checks if the given query parameter is an instance of a MongoDB related class. It evaluates whether the query is a Relation that retrieves a query that is a MongoDB Builder or if it is directly an instance of MongoDB Builder. The logic succinctly identifies the type of database interaction at play, determining if it utilizes MongoDB for its operations.
     * BUSINESS_LOGIC: The purpose of this method is to validate the type of query being passed to determine if it is specifically handling MongoDB instances. This helps in ensuring that any operations that differ between relational and MongoDB queries are handled appropriately within the framework.
     * SUMMARY: Validates if the query is a MongoDB instance.
     *
     * @param mixed $query The query object that is being evaluated for its type.
     * @return bool Returns true if the query is a MongoDB instance, otherwise false.
     *
     */
    protected function isMongoInstance($query): bool
    {
        return $query instanceof Relation && $query->getQuery() instanceof \MongoDB\Laravel\Eloquent\Builder || $query instanceof \MongoDB\Laravel\Eloquent\Builder;
    }

    /**
     * Parent Name: GridFilters
     * Name: checkSortingScope
     * Type: protected
     *
     * DESCRIPTION: This method checks if the global sorting scope is available on the given query. It first verifies if the sorting scope has been removed from the query. If removed, it immediately returns false.
     * If not removed, it then retrieves the model associated with the query and checks if the model has a global scope applied for sorting. The result is a boolean indicating the presence of the sorting scope.
     *
     * BUSINESS_LOGIC: The purpose of the method is to determine if sorting is applicable to the current query by checking for the presence of a sorting global scope. This is crucial for managing and maintaining data consistency in sorted records within a grid interface.
     *
     * SUMMARY: Checks if sorting scope is available on the query.
     *
     * @param Builder $query The database query builder instance for which to check the sorting scope.
     * @return bool Returns true if the sorting global scope is present, otherwise false.
     *
     */
    protected function checkSortingScope($query): bool
    {
        if (in_array(Sorting::class, $query->removedScopes())) {
            return false;
        }

        $model = $query->getModel();
        return $model::hasGlobalScope(Sorting::class);
    }

    /**
     * Parent Name: GridFilters
     * Name: getAllListRecords
     * Type: protected
     *
     * DESCRIPTION: This method retrieves a paginated list of records based on the provided query and grid settings. It first checks if sorting is applicable and applies the defined ordering logic. If no specific ordering is defined, it defaults to using the primary key for descending order if the model's key is guarded. The results are fetched and returned in a paginated format, allowing for efficient data handling in a grid layout.
     * BUSINESS_LOGIC: The purpose of this method is to facilitate listing records in a filtered and sorted manner, adhering to the specified grid configuration. It ensures that if there are any sorting instructions provided via callbacks or array definitions, they are properly applied to the query before the records are fetched, optimizing how users interact with large data sets.
     * SUMMARY: Retrieves and paginates records for a grid, applying sorting as necessary.
     *
     * @param \Modules\Core\Core\Helpers\Grid $grid The grid configuration containing sorting and ordering options.
     * @param mixed $query The base query instance from which records will be retrieved, potentially altered based on sorting logic.
     * @return \Illuminate\Pagination\LengthAwarePaginator A paginator containing the results of the query.
     */
    protected function getAllListRecords(Grid $grid, $query): \Illuminate\Pagination\LengthAwarePaginator
    {
        if (!$this->checkSortingScope($query)) {
            $model = $query->getModel();
            if ($ordering = $grid->getOrdering()) {
                foreach ($ordering as $order) {
                    if ($order['callback'] instanceof Closure) {
                        $methodName = $order['callback'];
                        $methodName($query);
                    } else {
                        $query->orderBy($order['column'], $order['direction']);
                    }
                }
            } elseif ($order = $grid->getOrder()) {
                if ($order instanceof Closure) {
                    $order($query);
                } else {
                    $query->orderBy($order, $grid->getDirection());
                }
            } elseif (($guarded = $model->getGuarded()) && in_array($model->getKeyName(), $guarded)) {
                $unions = count($query->getQuery()->unions ?: []);
                if ($unions) {
                    $query->orderBy($model->getKeyName(), 'desc');
                } else {
                    $query->orderBy($model->getTable() . '.' . $model->getKeyName(), 'desc');
                }
            }
        }

        $items = $query->get();
        $total = $items->count();
        return new LengthAwarePaginator($items, $total, $total == 0 ? 25 : $total, 1);
    }

    /**
     * @param $model
     * @param $className
     * @param Request|null $request
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|mixed
     * @throws Error
     */
    public function getRecordsQuery($model, $className = null, ?Request $request = null)
    {
        if (!is_null($className)) {
            $filters = $this->initSearchObject($className, $request ?: request())->compile();
            $model = $filters->wrapModel($model);
        }

        return $model;
    }
}