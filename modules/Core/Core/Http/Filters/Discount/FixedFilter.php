<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Filters\Discount;

use App\Helper\YesNo;
use Illuminate\Http\Request;
use Modules\Core\Core\Helpers\AbstractGridFilter;
use Modules\Core\Core\Rules\ArrayKeysIn;
use Modules\Core\Core\Traits\GridFilter\QueryFilter;

/**
 * DiscountFilter
 *
 * @package \Modules\Core\Core\Http\Filters
 */
class FixedFilter extends AbstractGridFilter
{
    use QueryFilter;

    protected $queryFilterColumns = [
        'product:name',
    ];

    /**
     * Filter by query
     * @param $operator
     * @param $value
     * @return void
     */
    public function filterQuery($operator, $value, string $name = 'query'): void
    {
        $this->setWhere('query', function ($query) use ($value): void {
            $query->whereHas('product', function ($query) use ($value): void {
                $query->where('name', 'like', sprintf('%%%s%%', $value));
            });
        });

        $this->setFilters('query', $value);
    }


    /**
     * Filter by active
     * @param $operator
     * @param $value
     * @return null
     */
    public function filterActive($operator, $value)
    {
        $this->setWhere('active', function ($query) use ($value): void {
            $query->where('active', YesNo::toInt($value));
        });

        return $this->setFilters('active', $value);
    }

    /**
     * @inheritdoc
     */
    public function getSavedSearchQueryFilters(): ?array
    {
        return [
            'module' => 'discounts',
            'filter' => 'fixed',
        ];
    }

    /**
     * @param Request $request
     * @return array
     */
    #[\Override]
    protected function validateRequestRules(Request $request): array
    {
        return [
            'page' => 'int',
            'perpage' => 'int|max:100',
            'order' => 'string|in:name',
            'direction' => 'string|in:asc,desc',
            'filters' => ['array', new ArrayKeysIn(['query', 'active'])],
            'filters.query' => 'string',
            'filters.active' => 'string|in:yes,no',
        ];
    }
}
