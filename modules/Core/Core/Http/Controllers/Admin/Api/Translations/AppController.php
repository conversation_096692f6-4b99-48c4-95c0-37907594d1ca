<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Controllers\Admin\Api\Translations;

use App\Http\Controllers\Controller;
use App\Models\Apps\Applications;
use App\Models\Apps\ApplicationsTranslation;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Modules\Core\Core\Traits\ChatGPTTranslator;

/**
 * Class AppController
 *
 * @package Modules\Core\Core\Http\Controllers\Admin\Api\Translations
 */
class AppController extends Controller
{
    use ChatGPTTranslator;

    /**
     * Display the translations for the specified application.
     *
     * @param int $id The ID of the application.
     * @param $locale
     * @return JsonResponse
     */
    public function index($id, $locale): JsonResponse
    {
        // Check if locale is provided and supported
        if (!$locale || !array_key_exists($locale, config('languages.languages'))) {
            return response()->json(['message' => 'Locale is missing in post data or is not supported!'], 400);
        }

        // Retrieve the application with its translations
        $app = Applications::with('translations')->find($id);
        if (!$app) {
            return response()->json([], 404);
        }

        $translations = $app->translations()->where('locale', $locale)->first();

        return response()->json($this->responseBox(optional($translations)->getAttributes() ?? []));
    }

    /**
     * Store a newly created translation for the specified application.
     *
     * @param Request $request The HTTP request object containing the translation data.
     * @param int $id The ID of the application.
     * @return JsonResponse
     */
    public function store(Request $request, $id): JsonResponse
    {
        // Extract the request data
        $data = $request->all();

        // Check if locale is provided and supported
        if (empty($data['locale']) || !array_key_exists($data['locale'], config('languages.languages'))) {
            return response()->json(['message' => 'Locale is missing in post data or is not supported!'], 400);
        }

        // Retrieve the application
        $app = Applications::find($id);
        if (!$app) {
            return response()->json([], 404);
        }

        // Create or update the translation
        $translation = ApplicationsTranslation::firstOrNew([
            'locale' => $data['locale'],
            'app_id' => $app->id,
        ]);

        $translation->fill(array_merge($data, ['app_id' => $app->id]));
        $translation->save();

        // Return updated translations in JSON format, keyed by locale
        return response()->json($this->responseBox($translation->getAttributes()));
    }

    /**
     * Translate the given text data into the specified locale.
     *
     * @param Request $request The HTTP request object containing the text data and target locale.
     * @param $id
     * @return JsonResponse
     */
    public function translate(Request $request, $id): JsonResponse
    {
        // Retrieve the target locale from the request
        $locale = $request->input('locale');

        // Check if locale is provided and supported
        if (!$locale || !array_key_exists($locale, config('languages.languages'))) {
            return response()->json(['message' => 'Locale is missing in post data or is not supported!'], 400);
        }

        // Check if the translation target locale is different from the control panel language
        if ('en' == $locale) {
            return response()->json(['message' => 'Cannot translate into the default language!'], 400);
        }

        // Extract the text data from the request
        $data = $request->only(['name', 'short_description', 'description', 'settings_description']);

        $data = array_filter($data, fn($value): bool => empty(trim(strip_tags((string) $value))));

        if (empty($data)) {
            return response()->json([]);
        }

        // Retrieve the application with its translations
        $app = Applications::with('translations')->find($id);
        if (!$app) {
            return response()->json([], 404);
        }

        $translations = $app->translations()->where('locale', 'en')->first();
        $data = array_merge($data, Arr::only((optional($translations))->getAttributes() ?? [], array_keys($data)));

        // Translate the text data
        $translation = $this->gptTranslateText($data, 'en', $locale);

        // Return the translated texts in JSON format
        if ($translation['status'] === false) {
            return response()->json([
                'message' => $translation['message']
            ], 400);
        }

        return response()->json($this->responseBox($translation['texts'] ?: []));
    }

    /**
     * @param array $data
     * @return mixed
     */
    protected function responseBox(array $data): array
    {
        $result = Arr::only($data, ['name', 'short_description', 'description', 'settings_description', 'help_url']);

        return array_merge([
            'name' => null,
            'short_description' => null,
            'description' => null,
            'settings_description' => null,
            'help_url' => null,
        ], $result);
    }
}
