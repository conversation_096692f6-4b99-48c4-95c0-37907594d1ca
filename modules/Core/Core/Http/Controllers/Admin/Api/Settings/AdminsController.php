<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Controllers\Admin\Api\Settings;

use App\Exceptions\Error;
use App\Helper\Plan;
use App\Helper\SiteCp\Settings\Config as SettingsConfig;
use App\Locale\Country;
use App\Models\Gate\PlanFeature;
use App\Models\Setting\Admin;
use Exception;
use Illuminate\Http\JsonResponse;
use Modules\Core\Core\Helpers\FunctionalityStatus;
use Modules\Core\Core\Http\Filters\AdminFilter;
use Modules\Core\Core\Http\Request\Settings\AdminAvatarRequest;
use Modules\Core\Core\Http\Request\Settings\AdminRequest;
use Modules\Core\Core\Models\MongoDb\CC2FaTasks;
use Modules\Core\Core\Traits\CC2FaTasks\CC2FaTaskHelper;
use Modules\Core\Core\Traits\GridFilters;
use Auth;
use CC2FA;
use Illuminate\Http\Request;
use Throwable;
use App\Models\Oauth\SocialAccount;

class AdminsController extends AbstractSettingController
{
    use GridFilters;
    use CC2FaTaskHelper;
    /**
     * @var Admin
     */
    protected $self;

    /**
     * @param \Illuminate\Http\Request $request
     * @return mixed
     */
    public function __construct(Request $request)
    {
        $this->self = $request->user('admin');
    }

    /**
     * @return JsonResponse
     * @throws Error
     */
    public function index(): JsonResponse
    {
        $records = $this->getRecords($this->getModel(), AdminFilter::class);
        //  $records->push(Auth::admin());
        $records->transform(fn(Admin $admin): \App\Models\Setting\Admin => $this->transform($admin));

        $createStaffAllow = $this->self?->permissions?->contains('section', 'settings.admins.create') ?? false;
        $records->setMeta('features', $this->getFeatures());
        $records->setMeta('createStaffAllow', Auth::admin()->isOwner() ? true : $createStaffAllow);
        return response()->json($records);

    }

    /**
     * @return JsonResponse
     */
    public function logoutAll(): JsonResponse
    {
        \Illuminate\Support\Facades\DB::connection('mongodb')->table('cc_site_sessions_' . site('site_id'))
            ->whereNotNull('admin_id')->delete();
        CC2FA::logout();
        Auth::guard('admin')->logout();
        return response()->json([]);
    }

    /**
     * @return JsonResponse
     */
    public function options(): JsonResponse
    {
        $sections = SettingsConfig::$sections;
        $allowedPermissions = $this->self?->permissions?->pluck('section')->toArray() ?? [];
        $permissions = $this->stripPermissionsKeys($sections);
        $permissions = $this->setAllowedDisablePermission($permissions, $allowedPermissions, $this->self?->isOwner() ?? false);

        $admin_types = SettingsConfig::$admin_types;
        $types = [];

        foreach ($admin_types as $admin_type) {
            if ($admin_type == 'owner') {
                continue;
            }

            $types[] = ['id' => $admin_type, 'name' => sprintf('admin_type_%1$s_label', $admin_type)];
        }

        return response()->json([
            'types' => $types,
            'permissions' => $permissions,
            'admin_id' => Auth::adminId(),
            'action' => 'add',
            'self' => Auth::admin(),
            'avatar' => [
                'extensions' => config('image.allowed_extensions'),
                'dimensions' => [
                    'width' => 250,
                    'height' => 250,
                ],
                'size' => 25
            ],
        ]);
    }

    /**
     * @param AdminRequest $request
     * @param string $hash
     * @return JsonResponse
     * @throws Error
     */
    public function store(AdminRequest $request, string $hash): JsonResponse
    {
        if (($check = $this->checkHashVue(CC2FaTasks::ACTION_CREATE_MODERATOR, $hash)) !== null) {
            return $check;
        }

        $data = $request->all();
        $data['type'] = 'moderator';

        $admin = (new Admin())->add($data);

        $admin = $this->getModel()->where('id', \Illuminate\Support\Arr::first($admin)->id)->first();

        $this->setHashToUsed(CC2FaTasks::ACTION_CREATE_MODERATOR, $hash);

        return response()->json($this->transform($admin));

    }

    /**
     * @param $admin_id
     * @return JsonResponse
     * @throws Exception
     */
    public function delete($admin_id): JsonResponse
    {
        $admin = Admin::findOrFail($admin_id);
        if ('owner' == $admin->type) {
            return response()->json('You cannot delete the owner.', 422);
        }

        $admin->delete();
        return response()->json('', 204);

    }

    /**
     * @param AdminAvatarRequest $request
     * @param $admin_id
     * @return JsonResponse
     * @throws Error
     * @throws Throwable
     */
    public function avatar(AdminAvatarRequest $request, $admin_id): JsonResponse
    {
        $admin = Admin::find($admin_id);
        if ($admin) {
            $admin->uploadImageSplFileInfo($request->file('image'));

            return response()->json([
                'image' => $admin->getImage('150x150')
            ]);
        }

        return response()->json('', 404);
    }

    /**
     * @param Request $request
     * @param $admin_id
     * @return JsonResponse
     * @throws Error
     * @throws \SmartyException
     */
    public function passwordUpdate(Request $request, $admin_id): JsonResponse
    {
        $admin = Admin::find($admin_id);
        if ($admin->type == 'owner' && Auth::admin()->type != 'owner') {
            return response()->json('You cannot change the owner password.', 422);
        }

        if ($admin) {
            $admin->changePassword($request->all());

            return response()->json($this->transform($admin));
        }

        return response()->json('', 404);
    }

    /**
     * @param $id
     * @return JsonResponse
     * @throws Error
     */
    public function get($id): JsonResponse
    {
        $admin = Admin::find($id);
        if ($admin) {
            return response()->json($this->transform($admin));
        }

        return response()->json('', 404);
    }

    /**
     * @param Admin $admin
     * @return bool
     */
    protected function isRequiredCC2FA(Admin $admin): bool
    {
        if (session('cc2fa_verify')) {
            session()->forget('cc2fa_verify');
            return false;
        }

        /** @var Admin $self */
        $self = Auth::admin();
        if ($self->id == $admin->id) {
            return !empty($admin->cc2fa_secret);
        }

        if ($self->isOwner()) {
            return false;
        }

        return !empty($self->cc2fa_secret);
    }

    /**
     * @param AdminRequest $request
     * @param $admin_id
     * @return JsonResponse
     * @throws Error
     * @throws \SmartyException
     */
    public function updateAdmin(AdminRequest $request, $admin_id = null): JsonResponse
    {
        $admin = Admin::findOrFail($admin_id);
        if ($admin->type == 'owner' && Auth::admin()->type != 'owner') {
            return response()->json('You cannot change the owner.', 422);
        }

        $admin->edit($request->all());

        return response()->json($this->transform($admin->refresh()));

    }

    /**
     * @param $permissions
     * @param $allowedPermissions
     * @param mixed $isOwner
     * @return array
     */
    private function setAllowedDisablePermission(array $permissions, $allowedPermissions, $isOwner = false): array
    {
        foreach ($permissions as &$permission) {
            if ($isOwner) {
                $permission['disabled'] = false;
            } else {
                if (in_array($permission['id'], $allowedPermissions)) {
                    $permission['disabled'] = false;
                } else {
                    $permission['disabled'] = true;
                }
            }

            if (is_array($permission['children'])) {
                $permission['children'] = $this->setAllowedDisablePermission($permission['children'], $allowedPermissions, $isOwner);
            }
        }

        return $permissions;
    }

    /**
     * @param mixed $data
     * @return mixed
     */
    private function stripPermissionsKeys($data): array
    {
        $data = array_values($data);
        foreach ($data as $key => $value) {
            if (is_array($value['children'])) {
                $data[$key]['children'] = $this->stripPermissionsKeys($value['children']);
            }
        }

        return $data;
    }

    /**
     * @return array
     */
    #[\Override]
    protected function getFeatures(): array
    {
        $featuresArray = ['administrators'];
        foreach ($featuresArray as $feature) {
            if ($planFeature = PlanFeature::findByMap($feature)) {
                $features[] = [
                    'group' => 'administrators',
                    'id' => $planFeature->id,
                    'mapping' => $planFeature->mapping,
                    'name' => $planFeature->name_translated,
                    'current' => Plan::featureValue($feature),
                    'used' => Admin::count(),
                    'suffix' => null,
                    'type' => [],
                    'cast' => $planFeature->cast,
                ];
            }
        }

        return $features;
    }

    /**
     * @return Admin
     */
    protected function getModel()
    {
        $model = Admin::with(['info']);
        //        if ($this->self->type == 'owner') {
        //            $model->where('type', '!=', 'owner');
        //        } elseif ($this->self->type == 'admin') {
        //            $model->whereNotIn('type', ['owner', 'admin']);
        //        } else {
        //            $model->where('type', 'moderator');
        //        }

        return $model;
    }

    /**
     * @param Admin $admin
     * @return Admin
     * @throws Error
     */
    protected function transform(Admin $admin): Admin
    {
        if ($admin->info) {
            $admin_info = $admin->info;
            $admin->address = '';

            if (isset($admin_info->country)) {
                $country_name = Country::get(strtoupper($admin_info->country) . '.name');
                $admin->address = $country_name . ', ';
            }

            $admin->address .= ($admin_info->city ?? '') . ((isset($admin_info->street)) ? ', ' . $admin_info->street : '') . ((isset($admin_info->postal_code)) ? ', ' . $admin_info->postal_code : '');
            if (empty($admin->address)) {
                $admin->address = null;
            }

            $admin->address = trim((string) $admin->address, ", ");
            $admin->phone = (!empty($admin_info->phone)) ? $admin_info->phone : null;
            $admin->last_edited = $admin->updated_at;
        }

        $admin->is_required_cc2fa = $this->isRequiredCC2FA($admin);
        $admin->image = $admin->getImage('300x300');
        if ($admin->type == 'owner') {
            $admin->socialAccounts = SocialAccount::getBySiteAdmin();
            $admin->fa_email = FunctionalityStatus::get('2fa_email') && !$admin->is_required_cc2fa;
        }

        $admin->unsetRelation('permissions');
        $admin->setRelation('permissions', $admin->permissions->pluck('section'));
        $admin->phone_number = $admin->phone;
        return $admin;
    }

}
