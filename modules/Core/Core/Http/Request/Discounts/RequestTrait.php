<?php

declare(strict_types=1);

namespace Modules\Core\Core\Http\Request\Discounts;

trait RequestTrait
{
    /**
     * @param mixed $type
     * @return mixed
     */
    public function getRules($type)
    {
        if (!$type) {
            $type = 'default';
        }

        $method = 'rules' . ucfirst((string) $type);
        if (method_exists($this, $method)) {
            return $this->$method();
        }

        return [];
    }

    /**
     * @param mixed $type
     * @return mixed
     */
    public function getMessages($type)
    {
        if (!$type) {
            $type = 'default';
        }

        $method = 'messages' . ucfirst((string) $type);
        if (method_exists($this, $method)) {
            return $this->$method();
        }

        return [];
    }

    private function rulesCode(): array
    {
        return [
            'code_discount' => 'required',
            'code' => 'required',
            'active' => 'required',
        ];
    }

    private function messagesCode(): array
    {
        return [
            'code.required' => 'Code is required',
            'code_discount.required' => 'Code discount is required',
            'active.required' => 'Active is required',
        ];
    }

    private function rulesDefault(): array
    {
        return [
            'discount_amount_type_in_label' => 'required',
            'timer_list' => 'required',
            'timer_details' => 'required',
            'only_customer' => 'required',
            'active' => 'required',
        ];
    }

    private function messagesDefault(): array
    {
        return [
            'discount_amount_type_in_label.required' => 'Discount amount type in label is required',
            'timer_list.required' => 'Timer list is required',
            'timer_details.required' => 'Timer details is required',
            'only_customer.required' => 'Only customer is required',
            'active.required' => 'Active is required',
        ];
    }

    private function rulesContainer(): array
    {
        return [
            "active" => "required",
            "name" => "required",
            "type" => "required",
            "settings" => "required",
            "is_container" => "required|in:1",
        ];
    }

    private function messagesContainer(): array
    {
        return [
            "active.required" => "Is active is required",
            "name.required" => "Name is required",
            "type.required" => "Type is required",
            "settings.required" => "Settings is required",
            "is_container.required" => "Is container is required",
            "is_container.in" => "Is container must be 1",
        ];
    }

    private function rulesFixed(): array
    {
        return [
            "active" => "required",
            "name" => "required",
            "type" => "required",
            "discount_amount_type_in_label" => "required",
            "timer_in_listing" => "required",
            "timer_in_details" => "required",
        ];
    }

    private function messagesFixed(): array
    {
        return [
            "active.required" => "Is active is required",
            "name.required" => "Name is required",
            "type.required" => "Type is required",
            "discount_amount_type_in_label.required" => "Discount amount type in label is required",
            "timer_in_listing.required" => "Timer in listing is required",
            "timer_in_details.required" => "Timer in details is required",
        ];
    }

    private function rulesQuantity(): array
    {
        return [
            'active' => 'required',
            'name' => 'required',
            'product_id' => 'required|exists:products,id|int',
            'conditions' => 'required|array',
            'conditions.*.quantity' => 'required|int',
            'conditions.*.discount_value' => 'required|int',
        ];
    }

    private function messagesQuantity(): array
    {
        return [
            'active.required' => 'Active is required',
            'name.required' => 'Name is required',
            'type.required' => 'Type is required',
            'product_id.required' => 'Product id is required',
            'product_id.exists' => 'Product id is not exists',
            'product_id.int' => 'Product id must be integer',
            'conditions.required' => 'Conditions is required',
            'conditions.array' => 'Conditions must be array',
            'conditions.*.quantity.required' => 'Quantity is required',
            'conditions.*.quantity.int' => 'Quantity must be integer',
            'conditions.*.discount_value.required' => 'Discount value is required',
            'conditions.*.discount_value.int' => 'Discount value must be integer',
        ];
    }

    private function rulesCountdown(): array
    {
        return [
            'active' => 'required',
            'name' => 'required',
            'countdown_discount' => 'required|int|in:1',
            'countdown_popup_effect' => 'nullable|in:confetti,fireworks,school_pride',
            'countdown_minutes' => 'required|int',
            'countdown_description' => 'string',
            'only_customer' => 'required',
        ];
    }

    private function messagesCountdown(): array
    {
        return [
            'active.required' => 'Active is required',
            'name.required' => 'Name is required',
            'countdown_discount.required' => 'Countdown discount is required',
            'countdown_discount.int' => 'Countdown discount must be integer',
            'countdown_discount.in' => 'Countdown discount must be 1',
            'countdown_popup_effect.required' => 'Countdown popup effect is required',
            'countdown_popup_effect.in' => 'Countdown popup effect must be confetti, fireworks, school_pride',
            'countdown_minutes.required' => 'Countdown minutes is required',
            'countdown_minutes.int' => 'Countdown minutes must be integer',
            'countdown_description.string' => 'Countdown description must be string',
            'only_customer.required' => 'Only customer is required',
        ];

    }


}
