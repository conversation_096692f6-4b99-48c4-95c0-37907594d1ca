<?php

declare(strict_types=1);

namespace Modules\Core\Core\Helpers\Settings;

use App\Helper\Mail\QueueNotifyAdmin;
use App\Helper\YesNo;
use App\Models\Apps\SiteRequiredSetting;
use App\Models\Router\Site;
use App\Models\Setting\Configuration;
use App\Models\Setting\Setting;
use Artisan;
use Exception;
use Illuminate\Config\Repository;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class GeneralSettingsFormatter extends AbstractSettingsFormatter
{
    /** @var Site $site */
    protected $site;

    /** @var Repository $maintenance */
    protected $maintenance;

    /** @var bool $mustGenerate */
    protected $mustGenerate = false;

    public function __construct()
    {
        parent::__construct();
        $this->site = site();
        $this->maintenance = configuration('maintenance');
    }

    public function getDateFormat()
    {
        return $this->settings->get('date_format');
    }

    /**
     * @param mixed $format
     * @return mixed
     */
    public function setDateFormat(mixed $format): static
    {
        $this->settings->set('date_format', $format);
        return $this;
    }

    public function getTimeFormat()
    {
        return $this->settings->get('time_format');
    }

    /**
     * @param string $format
     * @return mixed
     */
    public function setTimeFormat(string $format): static
    {
        $this->settings->set('time_format', $format);
        return $this;
    }

    public function getCustomerNameDisplay()
    {
        return $this->settings->get('customer_name_display');
    }

    /**
     * @param string $format
     * @return mixed
     */
    public function setCustomerNameDisplay(string $format): static
    {
        $this->settings->set('customer_name_display', $format);
        return $this;
    }

    public function getSiteEmail()
    {
        return $this->settings->get('site_email');
    }

    /**
     * @param string $format
     * @return mixed
     */
    public function setSiteEmail(string $format): static
    {
        $set = [];
        if (session('cc_console_login.auth_id')) {
            $set = [
                'new_site_email' => null,
                'site_email' => $format,
                '_code1' => null,
                '_code2' => null,
            ];
        } else {
            $newEmail = $this->getNewSiteEmail();
            if (empty($newEmail)) {
                if ($this->getSiteEmail() != $format) {
                    $set['new_site_email'] = $format;
                    $this->mustGenerate = true;
                }
            } else {
                if (request()->input('cancel_new_site_email')) {
                    $set = [
                        'new_site_email' => null,
                        '_code1' => null,
                        '_code2' => null,
                    ];
                } elseif (request()->input('confirm_new_site_email')) {
                    $set = [
                        'new_site_email' => null,
                        'site_email' => $newEmail,
                        '_code1' => null,
                        '_code2' => null,
                    ];
                } elseif ($format != $newEmail && $this->getSiteEmail() != $format) {
                    $set['new_site_email'] = $format;
                    $this->mustGenerate = true;
                }
            }

            if ($this->mustGenerate) {
                $set = array_merge($set, $this->generateCodes());
            }
        }

        if ($set) {
            $this->settings->set($set);
        }

        /*if($this->getSiteEmail() != $format) {
            $this->settings->set('new_site_email', $format);
        } else {
            $this->settings->set('site_email', $format);
            $this->settings->set('new_site_email', null);
        }*/
        return $this;
    }

    protected function generateCodes(): array
    {
        $codes = [];
        while (count($codes) < 3) {
            $codes[] = Str::random(8);
            $codes = array_unique($codes);
        }

        return [
            '_code1' => array_shift($codes),
            '_code2' => array_shift($codes),
        ];
    }

    /**
     * Readonly
     * @return array|Collection|mixed
     */
    public function getNewSiteEmail()
    {
        return $this->settings->get('new_site_email');
    }

    public function getSiteName()
    {
        return $this->settings->get('site_name');
    }

    /**
     * @param string $format
     * @return mixed
     */
    public function setSiteName(string $format): static
    {
        $this->settings->set('site_name', $format);
        return $this;
    }

    public function getCopyright()
    {
        return $this->settings->get('copyright');
    }

    /**
     * @param string $format
     * @return mixed
     */
    public function setCopyright(string $format): static
    {
        $this->settings->set('copyright', $format);
        return $this;
    }

    public function getShowPoweredByInfo(): int
    {
        return intval($this->settings->get('show_powered_by_info', YesNo::True) == YesNo::True);
    }

    /**
     * @param bool|int $format
     * @return mixed
     */
    public function setShowPoweredByInfo(bool|int $format): static
    {
        $this->settings->set('show_powered_by_info', (bool)$format ? YesNo::True : YesNo::False);
        return $this;
    }

    public function getLockOrders(): int
    {
        return intval($this->settings->get('lock_orders', YesNo::True) == YesNo::True);
    }

    /**
     * @param bool|int $lock_orders
     * @return mixed
     */
    public function setLockOrders(bool|int $lock_orders): static
    {
        $this->settings->set('lock_orders', (bool)$lock_orders ? YesNo::True : YesNo::False);
        return $this;
    }

    public function getLockOrdersTime(): int
    {
        return intval($this->settings->get('lock_orders_time', 7));
    }

    /**
     * @param int $lock_orders
     * @return mixed
     */
    public function setLockOrdersTime(int $lock_orders): static
    {
        $this->settings->set('lock_orders_time', $lock_orders);
        return $this;
    }

    public function getProductImageType()
    {
        return $this->settings->get('product_image_type', 'original');
    }

    /**
     * @param string $lock_orders
     * @return mixed
     */
    public function setProductImageType(string $lock_orders): static
    {
        $this->settings->set('product_image_type', $lock_orders);
        return $this;
    }

    public function getSessionKeyGuard()
    {
        return $this->settings->get('sessionKeyGuard');
    }

    /**
     * @param string|null $key
     * @return mixed
     */
    public function setSessionKeyGuard(?string $key): static
    {
        $this->settings->set('sessionKeyGuard', strval($key));
        return $this;
    }

    public function getNewProductsMark(): int
    {
        return intval($this->settings->get('new_products_mark', YesNo::False) == YesNo::True);
    }

    /**
     * @param bool|int $lock_orders
     * @return mixed
     */
    public function setNewProductsMark(bool|int $lock_orders): static
    {
        $this->settings->set('new_products_mark', (bool)$lock_orders ? YesNo::True : YesNo::False);
        return $this;
    }

    public function getNewProductsMarkTime(): int
    {
        return intval($this->settings->get('new_products_mark_time', 7));
    }

    /**
     * @param int $new_products_mark_time
     * @return mixed
     */
    public function setNewProductsMarkTime(int $new_products_mark_time): static
    {
        $this->settings->set('new_products_mark_time', $new_products_mark_time);
        return $this;
    }

    public function getRemoveFeatureProducts(): int
    {
        return intval($this->settings->get('remove_feature_products', YesNo::False) == YesNo::True);
    }

    /**
     * @param bool|int $lock_orders
     * @return mixed
     */
    public function setRemoveFeatureProducts(bool|int $lock_orders): static
    {
        $this->settings->set('remove_feature_products', (bool)$lock_orders ? YesNo::True : YesNo::False);
        return $this;
    }

    public function getRemoveFeatureProductsTime(): int
    {
        return intval($this->settings->get('remove_feature_products_time', 7));
    }

    /**
     * @param int $remove_feature_products_time
     * @return mixed
     */
    public function setRemoveFeatureProductsTime(int $remove_feature_products_time): static
    {
        $this->settings->set('remove_feature_products_time', $remove_feature_products_time);
        return $this;
    }

    public function getAdminBar(): int
    {
        return intval($this->settings->get('admin_bar', YesNo::True) == YesNo::True);
    }

    /**
     * @param bool|int $admin_bar
     * @return mixed
     */
    public function setAdminBar(bool|int $admin_bar): static
    {
        $this->settings->set('admin_bar', (bool)$admin_bar ? YesNo::True : YesNo::False);
        return $this;
    }

    public function getCurrency()
    {
        return $this->site->currency;
    }

    /**
     * @param string $currency
     * @return mixed
     */
    public function setCurrency(string $currency): static
    {
        $this->site->currency = $currency;
        return $this;
    }

    public function getUnitSystem()
    {
        return $this->site->unit_system;
    }

    /**
     * @param string $unit_system
     * @return mixed
     */
    public function setUnitSystem(string $unit_system): static
    {
        $this->site->unit_system = $unit_system;
        return $this;
    }

    public function getLanguage()
    {
        return $this->site->language;
    }

    /**
     * @param string $language
     * @return mixed
     */
    public function setLanguage(string $language): static
    {
        $this->site->language = $language;
        return $this;
    }

    public function getLanguageCp()
    {
        return $this->site->language_cp;
    }

    /**
     * @param string $language_cp
     * @return mixed
     */
    public function setLanguageCp(string $language_cp): static
    {
        $this->site->language_cp = $language_cp;
        return $this;
    }

    public function getTimezone()
    {
        return $this->site->timezone;
    }

    /**
     * @param string $timezone
     * @return mixed
     */
    public function setTimezone(string $timezone): static
    {
        $this->site->timezone = $timezone;
        return $this;
    }

    public function getOperationCountry()
    {
        return $this->site->getOperationCountry()->code ?? null;
    }

    /**
     * @param string $operation_country
     * @return mixed
     */
    public function setOperationCountry(string $operation_country): static
    {
        $this->site->operation_country = $operation_country;
        return $this;
    }

    public function getSiteIndustry()
    {
        return $this->site->industry ?: [];
    }

    /**
     * @param array $industry
     * @return mixed
     */
    public function setSiteIndustry(array $industry): static
    {
        $this->site->industry = $industry;
        return $this;
    }

    public function getMaintenance(): int
    {
        return intval($this->site->manual_maintenance);
    }

    /**
     * @param bool|int $maintenance
     * @return mixed
     */
    public function setMaintenance(bool|int $maintenance): static
    {
        $this->site->manual_maintenance = (bool)$maintenance;
        return $this;
    }

    public function getMaintenancePage(): ?int
    {
        $page = $this->maintenance->get('page_id');
        return is_numeric($page) ? intval($page) : null;
    }

    /**
     * @param int|null $page_id
     * @return mixed
     */
    public function setMaintenancePage(?int $page_id): static
    {
        $this->maintenance->set('page_id', $page_id);
        return $this;
    }

    public function getMaintenanceIpList(): array
    {
        $ips = $this->maintenance->get('allowed_ips');
        return is_array($ips) ? $ips : array_filter(explode(',', (string) $ips));
    }

    /**
     * @param array $data
     * @return mixed
     */
    public function setMaintenanceIpList(array $data): static
    {
        $this->maintenance->set('allowed_ips', $data);
        return $this;
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'date_format' => $this->getDateFormat(),
            'time_format' => $this->getTimeFormat(),
            'currency' => $this->getCurrency(),
            'unit_system' => $this->getUnitSystem(),
            'language' => $this->getLanguage(),
            'language_cp' => $this->getLanguageCp(),
            'customer_name_display' => $this->getCustomerNameDisplay(),
            'timezone' => $this->getTimezone(),
            'operation_country' => $this->getOperationCountry(),
            'site_email' => $this->getSiteEmail(),
            'new_site_email' => $this->getNewSiteEmail(),
            'site_name' => $this->getSiteName(),
            'copyright' => $this->getCopyright(),
            'site_industry' => $this->getSiteIndustry(),
            'show_powered_by_info' => $this->getShowPoweredByInfo(),
            'maintenance' => $this->getMaintenance(),
            'maintenance_page' => $this->getMaintenancePage(),
            'maintenance_ip_list' => $this->getMaintenanceIpList(),
            'sessionKeyGuard' => $this->getSessionKeyGuard(),
            'lock_orders' => $this->getLockOrders(),
            'lock_orders_time' => $this->getLockOrdersTime(),
            'product_image_type' => $this->getProductImageType(),
            'new_products_mark' => $this->getNewProductsMark(),
            'new_products_mark_time' => $this->getNewProductsMarkTime(),
            'remove_feature_products' => $this->getRemoveFeatureProducts(),
            'remove_feature_products_time' => $this->getRemoveFeatureProductsTime(),
            'admin_bar' => $this->getAdminBar(),
        ];
    }

    #[\Override]
    public function save(): static
    {
        $isDirtyLanguage = $this->site->isDirty('language');
        $isDirtyCurrency = $this->site->isDirty('currency');

        parent::save();
        $this->site->save();
        Configuration::createByGroup([
            'maintenance' => $this->maintenance->all(),
        ]);

        if ($isDirtyLanguage) {
            app()->setLocale($this->site->language);
            Artisan::run('db:translation', [
                '--site' => site('site_id'),
                '--force' => null,
                '--command' => 'append',
            ]);

            session()->forget('language_cp_force');
            SiteRequiredSetting::sync();
        }

        if ($isDirtyLanguage || $isDirtyCurrency) {
            Artisan::call('js:data-generate', [
                '--site' => site('site_id'),
            ]);
        }

        if ($this->mustGenerate) {
            QueueNotifyAdmin::emailConfirmation(
                $this->getSiteEmail(),
                $this->settings->get('_code1')
            );
            QueueNotifyAdmin::emailConfirmation(
                $this->getNewSiteEmail(),
                $this->settings->get('_code2')
            );
        }

        Setting::clearCache();

        return $this;
    }

    public function resendEmails(): void
    {
        if (empty($this->settings->get('_code1')) || empty($this->settings->get('_code2'))) {
            throw new Exception('Missing confirm code/s');
        }

        QueueNotifyAdmin::emailConfirmation(
            $this->getSiteEmail(),
            $this->settings->get('_code1')
        );
        QueueNotifyAdmin::emailConfirmation(
            $this->getNewSiteEmail(),
            $this->settings->get('_code2')
        );
    }
}
