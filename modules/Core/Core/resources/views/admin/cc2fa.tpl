<div class="row">
    <div class="main-fields-holder clearfix">
        <div class="form">
            <form method="post" class="ajaxForm js-export-form" action="{route('admin.core.export', ['action' => $log->action, 'chunk' => $log->chunk, 'limit' => $log->limit, 'extra' => $extra, 'random' => $random, 'engine' => 'sm'])}">
                <div class="col-xs-12">
                    <div class="row">
                        <div class="logged-as" style="margin-left: 12px; margin-bottom: 10px;">
                            <span class="initials1">
                                <img src="{$profile->getImage('150x150')}" alt="" style="width: 40px;height: 40px;border-radius: 50%;">
                            </span>
                            <span>
                                {t}cc2fa::cc2fa.signed_as{/t} <strong>{$profile->email}</strong>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-xs-12">
                    <div class="form-group padding-top-0">
                        <label class="control-label" for="code">{t}cc2fa::cc2fa.text.otp_code_label{/t}</label>

                        <div class="skip-has-error">
                            <input type="text" class="form-control without-autocomplete" autocomplete="off" name="code" placeholder="{t}cc2fa::cc2fa.text.otp_code{/t}" />
                        </div>
                        <div class="control-label">{t}cc2fa::cc2fa.info.otp_code{/t}</div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<style>
    .without-autocomplete {
        text-security: disc;
        -webkit-text-security: disc;
    }
</style>
{capture append="js"}
    <script>
        $('.js-export-form').on('cc.ajax.success', function(e, json) {
            if(json.type === 'csv') {
                const csvHandler = new CSVHandler;
                const headers = json.data.shift() || ['Empty'];
                csvHandler.download(json.filename, json.data, headers)
            } else if(json.type === 'zip') {
                const zipHandler = new ZipHandler;
                zipHandler.download(json.filename, json.data)
            }  else if(['import', 'create'].includes(json.type)) {
                $(document).trigger('cc.ajax.' + json.type + '.{$random}', json);
            } else {
                toastr.success('The export is being processed. You will receive an email with the download link.');
            }
        });
    </script>
{/capture}